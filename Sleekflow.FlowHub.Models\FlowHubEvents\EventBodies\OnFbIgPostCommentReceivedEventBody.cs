using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnFbIgPostCommentReceivedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnFbIgPostCommentReceived; }
    }

    [Required]
    [JsonProperty("post_comment")]
    public OnFbIgPostCommentReceivedEventBodyMessage PostComment { get; set; }

    [Required]
    [JsonProperty("page_id")]
    public string PageId { get; set; }

    [Required]
    [JsonProperty("channel")]
    public string Channel { get; set; }

    [Required]
    [JsonProperty("comment_id")]
    public string CommentId { get; set; }

    [Required]
    [JsonProperty("is_new_contact")]
    public bool IsNewContact { get; set; }

    [JsonProperty("conversation_id")]
    public string? ConversationId { get; set; }

    [JsonProperty("contact_id")]
    public string? ContactId { get; set; }

    [JsonProperty("contact")]
    public Dictionary<string, object?>? Contact { get; set; }

    [JsonConstructor]
    public OnFbIgPostCommentReceivedEventBody(
        DateTimeOffset createdAt,
        OnFbIgPostCommentReceivedEventBodyMessage postComment,
        string pageId,
        string commentId,
        bool isNewContact,
        string? conversationId,
        string? contactId,
        Dictionary<string, object?>? contact,
        string channel)
        : base(createdAt)
    {
        PostComment = postComment;
        PageId = pageId;
        CommentId = commentId;
        IsNewContact = isNewContact;
        ConversationId = conversationId;
        ContactId = contactId;
        Contact = contact;
        Channel = channel;
    }
}

public class OnFbIgPostCommentReceivedEventBodyMessage
{
    [JsonProperty("comment_id")]
    public string CommentId { get; set; }

    [JsonProperty("post_id")]
    public string PostId { get; set; }

    [JsonProperty("from")]
    public FromFacebookSender From { get; set; }

    /// <summary>
    /// reaction, comment.
    /// </summary>
    // [JsonProperty("item")]
    // public string Item { get; set; }

    /// <summary>
    /// The comment content, created by the facebook user, under the post.
    /// </summary>
    [JsonProperty("text")]
    public string? Text { get; set; }

    /// <summary>
    /// The Url of the post.
    /// </summary>
    // [JsonProperty("facebook_post_permalink_url")]
    // public string? FacebookPostPermalinkUrl { get; set; }
    //
    // [JsonProperty("video_url_in_comment")]
    // public string? VideoUrlInComment { get; set; }
    //
    // [JsonProperty("photo_url_in_comment")]
    // public string? PhotoUrlInComment { get; set; }

    [JsonConstructor]
    public OnFbIgPostCommentReceivedEventBodyMessage(
        string commentId,
        string postId,
        FromFacebookSender from,
        string? text)
    {
        CommentId = commentId;
        PostId = postId;
        From = from;
        Text = text;
    }
}

public class FromFacebookSender
{
    [JsonProperty("facebook_id")]
    public string FacebookId { get; set; }

    [JsonProperty("facebook_name")]
    public string? FacebookName { get; set; }

    [JsonConstructor]
    public FromFacebookSender(string facebookId, string? facebookName)
    {
        FacebookId = facebookId;
        FacebookName = facebookName;
    }
}