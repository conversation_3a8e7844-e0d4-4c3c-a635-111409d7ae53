﻿using System.Text;
using Microsoft.AspNetCore.Http.Extensions;
using Microsoft.AspNetCore.Http;
using Newtonsoft.Json;

namespace Sleekflow.Mvc.Tests;

public static class BaseTestHost
{
    public static bool IsGithubAction { get; } = Environment.GetEnvironmentVariable("GITHUB_ACTIONS") == "true";

    public static async Task InterceptAfterEachAsync(HttpContext context)
    {
        if (context == null)
        {
            return;
        }


        var isGitHubAction = Environment.GetEnvironmentVariable("GITHUB_ACTIONS");
        var shouldLogHttpReqAndRes = isGitHubAction != "true";

        if (context.Request.Method != "POST" || !context.Request.Headers.ContainsKey("X-Sleekflow-Record"))
        {
            return;
        }

        var uriStr = context.Request.GetEncodedUrl();

        if (shouldLogHttpReqAndRes)
        {
            Console.WriteLine(uriStr.Replace("http://localhost/", "### "));
            Console.WriteLine();
            Console.WriteLine("POST " + uriStr.Replace("http://localhost/", "{{endpoint}}/CommerceHub/"));
            Console.WriteLine("Content-Type: application/json-patch+json");
            Console.WriteLine("Authorization: Bearer {{access_token}}");
            Console.WriteLine();
        }

        context.Request.Body.Position = 0;
        using var requestStreamReader = new StreamReader(context.Request.Body);
        var requestBodyStr = await requestStreamReader.ReadToEndAsync();

        var deserializeObject = JsonConvert.DeserializeObject<Dictionary<string, object?>>(requestBodyStr);
        deserializeObject!.Remove("sleekflow_company_id");
        deserializeObject!.Remove("sleekflow_staff_id");
        deserializeObject!.Remove("sleekflow_staff_team_ids");

        if (shouldLogHttpReqAndRes)
        {
            Console.WriteLine(JsonConvert.SerializeObject(deserializeObject, Formatting.Indented));
            Console.WriteLine();
        }

        using var responseStreamReader = new StreamReader(context.Response.Body);
        var responseBodyStr = await responseStreamReader.ReadToEndAsync();

        if (shouldLogHttpReqAndRes)
        {
            Console.WriteLine(responseBodyStr);
            Console.WriteLine();
        }

        // Reset the response body so it can be read again
        context.Response.Body = new MemoryStream(Encoding.UTF8.GetBytes(responseBodyStr ?? string.Empty));
    }
}