using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;

namespace Sleekflow.FlowHub.Models.FlowHubConfigs;

public class UsageLimit
{
    [JsonProperty(UsageLimitFieldNames.PropertyNameMaximumNumOfWorkflows)]
    public int? MaximumNumOfWorkflows { get; set; }

    [JsonProperty(UsageLimitFieldNames.PropertyNameMaximumNumOfActiveWorkflows)]
    public int? MaximumNumOfActiveWorkflows { get; set; }

    [JsonProperty(UsageLimitFieldNames.PropertyNameMaximumNumOfNodesPerWorkflow)]
    public int? MaximumNumOfNodesPerWorkflow { get; set; }

    [JsonProperty(UsageLimitFieldNames.PropertyNameMaximumNumOfMonthlyWorkflowExecutions)]
    public int? MaximumNumOfMonthlyWorkflowExecutions { get; set; }


    [JsonConstructor]
    public UsageLimit(
        int maximumNumOfWorkflows,
        int maximumNumOfActiveWorkflows,
        int maximumNumOfNodesPerWorkflow,
        int maximumNumOfMonthlyWorkflowExecutions)
    {
        MaximumNumOfWorkflows = maximumNumOfWorkflows;
        MaximumNumOfActiveWorkflows = maximumNumOfActiveWorkflows;
        MaximumNumOfNodesPerWorkflow = maximumNumOfNodesPerWorkflow;
        MaximumNumOfMonthlyWorkflowExecutions = maximumNumOfMonthlyWorkflowExecutions;
    }
}