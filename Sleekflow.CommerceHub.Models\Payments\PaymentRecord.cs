using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Payments;

public class PaymentRecord
{
    [JsonProperty("gateway_request")]
    public Dictionary<string, object>? GatewayRequest { get; set; }

    [JsonProperty("gateway_response")]
    public Dictionary<string, object>? GatewayResponse { get; set; }

    [JsonProperty("action_type")]
    public string ActionType { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonConstructor]
    public PaymentRecord(
        object? gatewayRequest,
        object? gatewayResponse,
        string actionType,
        string type)
    {
        GatewayRequest = GetDictionary(gatewayRequest);
        GatewayResponse = GetDictionary(gatewayResponse);
        ActionType = actionType;
        Type = type;
    }

    private Dictionary<string, object>? GetDictionary(object? obj)
    {
        return JsonConvert.DeserializeObject<Dictionary<string, object>>(JsonConvert.SerializeObject(obj));
    }
}