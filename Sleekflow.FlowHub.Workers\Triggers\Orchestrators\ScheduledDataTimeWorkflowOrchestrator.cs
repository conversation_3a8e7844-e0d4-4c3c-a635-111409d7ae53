using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workers.Triggers.Activities;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class ScheduledDataTimeWorkflowOrchestrator
{
    private readonly ILogger<ScheduledDataTimeWorkflowOrchestrator> _logger;
    private readonly WorkflowRecurringSettingsV2Parser _settingsV2Parser;

    private const int InitialBatchSize = 500;
    private const int MinBatchSize = 10; // Minimum batch size for retries

    public ScheduledDataTimeWorkflowOrchestrator(
        ILogger<ScheduledDataTimeWorkflowOrchestrator> logger,
        WorkflowRecurringSettingsV2Parser settingsV2Parser)
    {
        _logger = logger;
        _settingsV2Parser = settingsV2Parser;
    }

    [Function("ScheduledDataTimeWorkflow_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var scheduleWorkflowInput = context.GetInput<TriggerScheduleWorkflowInput>();

        _logger.LogInformation(
            "Starting process an ScheduledDataTimeWorkflow for company {SleekflowCompanyId} with workflow {WorkflowVersionedId}",
            scheduleWorkflowInput!.SleekflowCompanyId,
            scheduleWorkflowInput!.WorkflowVersionedId);

        try
        {
            var triggerConditionOutput =
                await context.CallActivityAsync<GetTriggerConditionOutput>(
                    "GetTriggerCondition",
                    new GetTriggerConditionInput(
                        scheduleWorkflowInput!.SleekflowCompanyId,
                        scheduleWorkflowInput.WorkflowVersionedId,
                        ScheduledTriggerV2Names.ScheduledDateTimeTriggerName));
            if (triggerConditionOutput == null || string.IsNullOrEmpty(triggerConditionOutput.WorkflowName))
            {
                _logger.LogError(
                    "trigger condition output is null. WorkflowVersionedId: {WorkflowVersionedId}",
                    scheduleWorkflowInput.WorkflowVersionedId);
                return;
            }

            _logger.LogInformation(
                "Enrollment Condition: {EnrollmentCondition}",
                JsonConvert.SerializeObject(triggerConditionOutput));
            DateTimeOffset? currentLastContactCreatedAt = null;
            string? currentLastContactId = null;
            var moreDataToProcess = true;
            var currentConfiguredBatchSize = InitialBatchSize;

            while (moreDataToProcess)
            {
                var attemptBatchSize = currentConfiguredBatchSize;
                var segmentSuccessfullyProcessed = false;
                ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchOutput? activityOutput;

                while (!segmentSuccessfullyProcessed && attemptBatchSize >= MinBatchSize)
                {
                    var activityInput =
                        new ProcessContactEligibilityCheckByBatchV3.ProcessContactEligibilityCheckByBatchV3Input(
                            scheduleWorkflowInput.Origin,
                            scheduleWorkflowInput.SleekflowCompanyId,
                            currentLastContactCreatedAt,
                            currentLastContactId,
                            attemptBatchSize,
                            scheduleWorkflowInput.WorkflowId,
                            scheduleWorkflowInput.WorkflowVersionedId,
                            triggerConditionOutput.WorkflowName,
                            triggerConditionOutput.Condition);
                    try
                    {
                        _logger.LogInformation(
                            "[V3]Begin to process contact batch for workflow {WorkflowVersionedId}. BatchSize: {AttemptBatchSize}, LastContactCreatedAt: {LastContactCreatedAt}, LastContactId: {LastContactId}",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        activityOutput = await context
                            .CallActivityAsync<ProcessContactEligibilityCheckByBatchV3.
                                ProcessContactEligibilityCheckByBatchV3Output>(
                                "ProcessContactEligibilityCheckByBatchV3",
                                activityInput);

                        currentLastContactCreatedAt = activityOutput.NextBatchLastContactCreatedAt;
                        currentLastContactId = activityOutput.NextBatchLastContactId;
                        segmentSuccessfullyProcessed = true;

                        moreDataToProcess = activityOutput.NextBatchLastContactId != null ||
                                            activityOutput.NextBatchLastContactCreatedAt != null;

                        _logger.LogInformation(
                            "[V2]Successfully processed batch for workflow {WorkflowVersionedId}. Contacts in fetched batch: {ContactsFetched}. Contacts enrolled: {ContactsEnrolled}. Next cursor: CreatedAt={NextCreatedAt}, Id={NextId}",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            activityOutput.ContactsInFetchedBatch,
                            activityOutput.ContactsEnrolled,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        currentConfiguredBatchSize = InitialBatchSize;
                    }
                    catch (OutOfMemoryException oomEx)
                    {
                        _logger.LogError(
                            oomEx,
                            "[V2]OutOfMemoryException processing contact batch for workflow {WorkflowVersionedId} with BatchSize {AttemptBatchSize}. Reducing batch size. Current segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        attemptBatchSize /= 2;

                        if (attemptBatchSize < MinBatchSize)
                        {
                            attemptBatchSize = MinBatchSize;
                        }

                        currentConfiguredBatchSize = attemptBatchSize;

                        if (attemptBatchSize == MinBatchSize && !segmentSuccessfullyProcessed)
                        {
                            _logger.LogCritical(
                                oomEx,
                                "[V2]Critical error: OutOfMemoryException persisted for workflow {WorkflowVersionedId} even with MinBatchSize {MinBatchSize}. Stopping processing. Segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                                scheduleWorkflowInput.WorkflowVersionedId,
                                MinBatchSize,
                                currentLastContactCreatedAt,
                                currentLastContactId);
                            moreDataToProcess = false;

                            throw new Exception(
                                $"[V2]Failed to process segment due to OutOfMemoryException for workflow {scheduleWorkflowInput.WorkflowVersionedId} even with MinBatchSize {MinBatchSize}. Cursors: CreatedAt={currentLastContactCreatedAt}, Id={currentLastContactId}",
                                oomEx);
                        }
                    }
                    catch (Exception e)
                    {
                        _logger.LogError(
                            e,
                            "[V2]Exception occurred in ProcessContactEligibilityCheckByBatch for workflow {WorkflowVersionedId} with BatchSize {AttemptBatchSize}. Current segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}. This segment will be marked as failed, and the orchestration will stop processing further segments for this run.",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);
                        moreDataToProcess = false;
                    }
                }

                if (!segmentSuccessfullyProcessed && moreDataToProcess)
                {
                    _logger.LogError(
                        "[V2]Failed to process segment for workflow {WorkflowVersionedId} after all retries. Stopping. Last attempted cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                        scheduleWorkflowInput.WorkflowVersionedId,
                        currentLastContactCreatedAt,
                        currentLastContactId);
                    moreDataToProcess = false;
                }
            }

            _logger.LogInformation(
                "[[V2]] Completed all data processing for workflow {WorkflowVersionedId} for company {SleekflowCompanyId}.",
                scheduleWorkflowInput.WorkflowVersionedId,
                scheduleWorkflowInput.SleekflowCompanyId);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "[V2] Orchestration failed for ScheduledDataTimeWorkflow {WorkflowVersionedId} for {SleekflowCompanyId}",
                scheduleWorkflowInput.WorkflowVersionedId,
                scheduleWorkflowInput.SleekflowCompanyId);
        }
        finally
        {
            if (scheduleWorkflowInput.RecurringSettings == null)
            {
                _logger.LogInformation(
                    "[V2] No recurring settings found for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Orchestration will complete.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId);
            }
            else
            {
                var nextRunTime = _settingsV2Parser.GetNextOccurrenceAfter(
                    scheduleWorkflowInput.ScheduledDatetime,
                    scheduleWorkflowInput.RecurringSettings,
                    context.CurrentUtcDateTime);

                _logger.LogInformation(
                    "[V2] Scheduling next run for Workflow {WorkflowVersionedId} for Company {SleekflowCompanyId} at {NextRunTime} UTC based on recurring settings.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId,
                    nextRunTime);

                await context.CreateTimer(nextRunTime.UtcDateTime, CancellationToken.None);

                _logger.LogInformation(
                    "[DataTime] Timer fired for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Continuing as new.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId);
                context.ContinueAsNew(scheduleWorkflowInput);
            }
        }
    }
}