﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowStepEntryProvider
{
    List<WorkflowStepEntry> GetStepEntries(ProxyWorkflow workflow);

    List<WorkflowStepEntry> GetStepEntries(List<Step> steps);
}

public record WorkflowStepEntry(Step Step, int Depth, string? BranchId);

public sealed class WorkflowStepEntryProvider : IWorkflowStepEntryProvider, ISingletonService
{
    public List<WorkflowStepEntry> GetStepEntries(ProxyWorkflow workflow)
    {
        return GetStepEntries(workflow.Steps);
    }

    public List<WorkflowStepEntry> GetStepEntries(List<Step> steps)
    {
        var stepEntries = new List<WorkflowStepEntry>();

        foreach (var step in steps)
        {
            stepEntries.AddRange(GetStepEntries(new WorkflowStepEntry(step, 0, "root")));
        }

        return stepEntries;
    }

    private static IEnumerable<WorkflowStepEntry> GetStepEntries(WorkflowStepEntry workflowStepEntry)
    {
        var step = workflowStepEntry.Step;
        var depth = workflowStepEntry.Depth;
        var branchId = workflowStepEntry.BranchId;

        return step switch
        {
            LogStep logStep => new List<WorkflowStepEntry>
            {
                new (logStep, depth, branchId)
            },
            ParallelStep parallelStep =>
                new List<WorkflowStepEntry>
                    {
                        new (parallelStep, depth, branchId)
                    }
                    .Concat(
                        parallelStep.ParallelBranches
                            .SelectMany(
                                (b, i) =>
                                    GetStepEntries(
                                        new WorkflowStepEntry(b.Step, depth + 1, parallelStep.Id + "-parallel-" + i)))
                            .ToList()),
            SimpleStep simpleStep => new List<WorkflowStepEntry>
            {
                new (simpleStep, depth, branchId)
            },
            SubFlowStep subFlowStep =>
                new List<WorkflowStepEntry>
                    {
                        new (subFlowStep, depth, branchId)
                    }
                    .Concat(
                        subFlowStep.Substeps
                            .SelectMany(s => GetStepEntries(new WorkflowStepEntry(s, depth + 1, subFlowStep.Id)))
                            .ToList()),
            SwitchStep switchStep => new List<WorkflowStepEntry>
            {
                new (switchStep, depth, branchId)
            },
            ThrowStep throwStep => new List<WorkflowStepEntry>
            {
                new (throwStep, depth, branchId)
            },
            TryCatchStep tryCatchStep =>
                new List<WorkflowStepEntry>
                    {
                        new (tryCatchStep, depth, branchId)
                    }
                    .Concat(
                        GetStepEntries(
                            new WorkflowStepEntry(tryCatchStep.Try.Step, depth + 1, tryCatchStep.Id + "-try")))
                    .Concat(
                        GetStepEntries(
                            new WorkflowStepEntry(tryCatchStep.Catch.Step, depth + 1, tryCatchStep.Id + "-catch"))),

            // CallStep
            object callStep when step.GetType().IsGenericType
                                       && step.GetType().GetGenericTypeDefinition() == typeof(CallStep<>) =>
                new List<WorkflowStepEntry>
                {
                    new ((Step) callStep, depth, branchId)
                },
            _ => throw new ArgumentOutOfRangeException(nameof(workflowStepEntry))
        };
    }
}