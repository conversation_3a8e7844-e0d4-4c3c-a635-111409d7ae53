﻿using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.CommerceHubDb;

public interface ICommerceHubDbResolver : IContainerResolver
{
}

public class CommerceHubDbResolver : ICommerceHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public CommerceHubDbResolver(ICommerceHubDbConfig commerceHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            commerceHubDbConfig.Endpoint,
            commerceHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}