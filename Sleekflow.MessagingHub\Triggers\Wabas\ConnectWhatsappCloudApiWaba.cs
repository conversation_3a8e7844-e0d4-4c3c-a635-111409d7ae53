using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Wabas;

[TriggerGroup(ControllerNames.Wabas)]
public class ConnectWhatsappCloudApiWaba
    : ITrigger<
        ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaInput,
        ConnectWhatsappCloudApiWaba.ConnectWhatsappCloudApiWabaOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<ConnectWhatsappCloudApiWaba> _logger;

    public ConnectWhatsappCloudApiWaba(
        IWabaService wabaService,
        ILogger<ConnectWhatsappCloudApiWaba> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
    }

    public class ConnectWhatsappCloudApiWabaInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("user_access_token")]
        public string UserAccessToken { get; set; }

        [Validations.ValidateArray]
        [JsonProperty("forbidden_waba_ids")]
        public List<string>? ForbiddenWabaIds { get; set; }

        [JsonProperty("webhook_url")]
        public string? WebhookUrl { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ConnectWhatsappCloudApiWabaInput(
            string sleekflowCompanyId,
            string userAccessToken,
            List<string> forbiddenWabaIds,
            string? webhookUrl,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UserAccessToken = userAccessToken;
            ForbiddenWabaIds = forbiddenWabaIds;
            WebhookUrl = webhookUrl;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ConnectWhatsappCloudApiWabaOutput
    {
        [JsonProperty("connected_wabas")]
        public List<WabaDto> WabaDtoDtos { get; set; }

        [JsonConstructor]
        public ConnectWhatsappCloudApiWabaOutput(List<WabaDto> wabaDtos)
        {
            WabaDtoDtos = wabaDtos;
        }
    }

    public async Task<ConnectWhatsappCloudApiWabaOutput> F(
        ConnectWhatsappCloudApiWabaInput connectWhatsappCloudApiWabaInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            connectWhatsappCloudApiWabaInput.SleekflowStaffId,
            connectWhatsappCloudApiWabaInput.SleekflowStaffTeamIds);
        var wabas = await _wabaService.ConnectWabaAsync(
            connectWhatsappCloudApiWabaInput.SleekflowCompanyId,
            connectWhatsappCloudApiWabaInput.UserAccessToken,
            connectWhatsappCloudApiWabaInput.ForbiddenWabaIds,
            connectWhatsappCloudApiWabaInput.WebhookUrl,
            sleekflowStaff);
        return new ConnectWhatsappCloudApiWabaOutput(wabas.Select(w => new WabaDto(w)).ToList());
    }
}