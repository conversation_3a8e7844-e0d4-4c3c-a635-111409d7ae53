﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnHubspotObjectEnrollmentToFlowHubRequestedEventConsumerDefinition : ConsumerDefinition<OnHubspotObjectEnrollmentToFlowHubRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnHubspotObjectEnrollmentToFlowHubRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnHubspotObjectEnrollmentToFlowHubRequestedEventConsumer : IConsumer<OnHubspotObjectEnrollmentToFlowHubRequestedEvent>
{
    private readonly IBus _bus;

    public OnHubspotObjectEnrollmentToFlowHubRequestedEventConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<OnHubspotObjectEnrollmentToFlowHubRequestedEvent> context)
    {
        var onHubspotObjectEnrollmentToFlowHubRequestedEvent = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnHubspotObjectEnrolledEventBody(
                onHubspotObjectEnrollmentToFlowHubRequestedEvent.CreatedAt,
                onHubspotObjectEnrollmentToFlowHubRequestedEvent.HubspotConnectionId,
                onHubspotObjectEnrollmentToFlowHubRequestedEvent.ObjectType,
                onHubspotObjectEnrollmentToFlowHubRequestedEvent.ObjectDict,
                onHubspotObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowId,
                onHubspotObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowVersionedId),
            onHubspotObjectEnrollmentToFlowHubRequestedEvent.ObjectId,
            onHubspotObjectEnrollmentToFlowHubRequestedEvent.ObjectType,
            onHubspotObjectEnrollmentToFlowHubRequestedEvent.SleekflowCompanyId));
    }
}