using Newtonsoft.Json;
using Sleekflow.Constants;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface IWhatsAppMessageBodyCreator : IMessageBodyCreator
{
}

public class WhatsAppMessageBodyCreator : BaseMessageBodyCreator, IWhatsAppMessageBodyCreator
{
    public WhatsAppMessageBodyCreator()
        : base(ChannelTypes.WhatsAppCloudApi)
    {
    }

    private static string GetWhatsAppCloudApiMessageType(SendMessageV2StepArgs callStepArgs)
    {
        return callStepArgs.WhatsAppCloudApiMessageParameters?.MessageType switch
        {
            "template_message" => "template",
            "manual_message" => callStepArgs.WhatsAppCloudApiMessageParameters?.MessageInteractiveType == "none"
                ? "text"
                : "interactive",
            _ => string.Empty
        };
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        var whatsAppCloudApiMessageType = GetWhatsAppCloudApiMessageType(args);

        var messageBody = whatsAppCloudApiMessageType switch
        {
            "template" => JsonConvert.DeserializeObject<MessageBody>(messageStr, JsonConfig.DefaultJsonSerializerSettings)!,
            "text" => CreateBaseMessageBody(
                textMessage: new TextMessageObject(messageStr)),
            "interactive" => CreateBaseMessageBody(
                interactiveMessage: new InteractiveMessageObject(
                    args.WhatsAppCloudApiMessageParameters!.MessageInteractiveType!,
                    header: null,
                    body: new InteractiveMessageObjectBody(Convert.ToString(messageStr)!),
                    footer: null,
                    action: new InteractiveMessageObjectAction(
                        args.WhatsAppCloudApiMessageParameters.InteractiveTitle,
                        buttons: args.WhatsAppCloudApiMessageParameters.MessageInteractiveType == "button"
                            ? args.WhatsAppCloudApiMessageParameters.InteractiveOptions!
                                .Select(x => new InteractiveMessageObjectActionButton(
                                    "reply",
                                    new InteractiveMessageObjectActionButtonReply(x.Id, x.Option)))
                                .ToList()
                            : null,
                        sections: args.WhatsAppCloudApiMessageParameters.MessageInteractiveType == "list"
                            ? [new InteractiveMessageObjectActionSection(
                                string.Empty,
                                null,
                                rows: args.WhatsAppCloudApiMessageParameters.InteractiveOptions!
                                    .Select(x => new InteractiveMessageObjectActionSectionRow(
                                        x.Id,
                                        x.Option,
                                        x.Description))
                                    .ToList())]
                            : null,
                        null,
                        null))),
            _ => throw new InvalidOperationException($"Unsupported message type {whatsAppCloudApiMessageType} for whatsappcloudapi.")
        };

        return Task.FromResult((messageBody, whatsAppCloudApiMessageType));
    }
}