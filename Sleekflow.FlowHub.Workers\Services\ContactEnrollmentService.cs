﻿using Microsoft.Extensions.Logging;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Workers.Services;

public interface IContactEnrollmentService
{
    Task PublishContactEnrollmentEventAsync(
        string contactId,
        ContactDetail contactDetail,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId);

    Task PublishContactEnrollmentEventAsync(
        EventBody eventBody,
        string contactId,
        string sleekflowCompanyId,
        string workflowVersionedId);

    Task ScheduleContactEnrollmentEventAsync(
        EventBody eventBody,
        string contactId,
        string sleekflowCompanyId,
        string workflowVersionedId,
        DateTimeOffset dateTimeOffset);
}

public class ContactEnrollmentService : IContactEnrollmentService
{
    private readonly IServiceBusManager _serviceBusManager;
    private readonly ILogger<ContactEnrollmentService> _logger;

    public ContactEnrollmentService(
        IServiceBusManager serviceBusManager,
        ILogger<ContactEnrollmentService> logger)
    {
        _serviceBusManager = serviceBusManager;
        _logger = logger;
    }

    public async Task PublishContactEnrollmentEventAsync(
        string contactId,
        ContactDetail contactDetail,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId)
    {
        try
        {
            await _serviceBusManager.PublishAsync(
                new OnTriggerEventRequestedEvent(
                    new OnScheduledWorkflowEnrollmentEventBody(
                        DateTimeOffset.UtcNow,
                        contactId,
                        workflowId,
                        workflowVersionedId,
                        contactDetail.Contact,
                        contactDetail.ContactOwner,
                        contactDetail.Lists,
                        contactDetail.Conversation),
                    contactId,
                    "Contact",
                    sleekflowCompanyId));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error publishing enrollment event for contact {ContactId}, workflow {WorkflowVersionedId}",
                contactId,
                workflowVersionedId);

            throw;
        }
    }

    public async Task PublishContactEnrollmentEventAsync(
        EventBody eventBody,
        string contactId,
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        try
        {
            await _serviceBusManager.PublishAsync(
                new OnTriggerEventRequestedEvent(
                    eventBody,
                    contactId,
                    "Contact",
                    sleekflowCompanyId));
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error published a enrollment event for contact {ContactId}, workflow {WorkflowVersionedId}",
                contactId,
                workflowVersionedId);

            throw;
        }
    }

    public async Task ScheduleContactEnrollmentEventAsync(
        EventBody eventBody,
        string contactId,
        string sleekflowCompanyId,
        string workflowVersionedId,
        DateTimeOffset dateTimeOffset)
    {
        try
        {
            await _serviceBusManager.PublishAsync(
                new OnTriggerEventRequestedEvent(
                    eventBody,
                    contactId,
                    "Contact",
                    sleekflowCompanyId),
                dateTimeOffset);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error scheduled a enrollment event for contact {ContactId}, workflow {WorkflowVersionedId} at {DateTime}",
                contactId,
                workflowVersionedId,
                dateTimeOffset);

            throw;
        }
    }
}