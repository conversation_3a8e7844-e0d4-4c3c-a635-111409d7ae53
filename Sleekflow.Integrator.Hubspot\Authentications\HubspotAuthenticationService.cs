﻿using System.Net;
using System.Net.Http.Headers;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Integrator.Hubspot.Configs;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Hubspot.Authentications;

public interface IHubspotAuthenticationService
{
    Task<HubspotAuthentication?> GetAsync(string sleekflowCompanyId);

    Task<HubspotAuthentication?> GetAsync(string id, string sleekflowCompanyId);

    Task<string> AuthenticateAsync(string sleekflowCompanyId, string returnToUrl);

    Task<string> AuthenticateV2Async(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl);

    Task<HubspotAuthentication?> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId);

    Task<HubspotAuthentication?> ReAuthenticateAndStoreAsync(
        string id,
        string sleekflowCompanyId);

    Task<(HubspotAuthentication Authentication, bool IsV2Authentication, string? ReturnToUrl, string? SuccessUrl, string? FailureUrl)>
        HandleAuthenticateCallbackAndStoreAsync(
        string code,
        string encryptedState);

    Task<HubspotAuthentication?> GetHubspotAuthenticationWithPortalIdAsync(long portalId);

    Task DeleteAsync(string id);
}

public class HubspotAuthenticationService : ISingletonService, IHubspotAuthenticationService
{
    private readonly IHubspotConfig _hubspotConfig;
    private readonly ILogger<HubspotAuthenticationService> _logger;
    private readonly IHubspotAuthenticationRepository _hubspotAuthenticationRepository;
    private readonly HttpClient _httpClient;
    private readonly IIdService _idService;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public HubspotAuthenticationService(
        IHubspotConfig hubspotConfig,
        IHttpClientFactory httpClientFactory,
        ILogger<HubspotAuthenticationService> logger,
        IHubspotAuthenticationRepository hubspotAuthenticationRepository,
        IIdService idService,
        IHubspotConnectionService hubspotConnectionService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _hubspotConfig = hubspotConfig;
        _logger = logger;
        _hubspotAuthenticationRepository = hubspotAuthenticationRepository;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _idService = idService;
        _hubspotConnectionService = hubspotConnectionService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    public class State
    {
        public string? SleekflowCompanyId { get; set; }

        public string? ReturnToUrl { get; set; }

        public string? SuccessUrl { get; set; }

        public string? FailureUrl { get; set; }

        [JsonConstructor]
        public State(
            string? sleekflowCompanyId,
            string? returnToUrl,
            string? successUrl,
            string? failureUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ReturnToUrl = returnToUrl;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
        }
    }

    public async Task<HubspotAuthentication?> GetAsync(string sleekflowCompanyId)
    {
        var authentication = await _hubspotAuthenticationRepository.GetOrDefaultAsync(
            sleekflowCompanyId,
            sleekflowCompanyId);
        if (authentication == null)
        {
            return null;
        }

        if (DateTimeOffset.UtcNow - authentication.IssuedAt < TimeSpan.FromSeconds(authentication.ExpiresIn))
        {
            return authentication;
        }

        return await ReAuthenticateAndStoreAsync(sleekflowCompanyId);
    }

    public async Task<HubspotAuthentication?> GetAsync(string id, string sleekflowCompanyId)
    {
        var authentication = await _hubspotAuthenticationRepository.GetOrDefaultAsync(
            id,
            id);
        if (authentication == null)
        {
            return null;
        }

        if (DateTimeOffset.UtcNow - authentication.IssuedAt < TimeSpan.FromSeconds(authentication.ExpiresIn))
        {
            return authentication;
        }

        try
        {
            return await ReAuthenticateAndStoreAsync(id, sleekflowCompanyId);
        }
        catch (Exception)
        {
            var connection = await _hubspotConnectionService.GetByAuthenticationIdAsync(
                sleekflowCompanyId,
                id);

            if (connection is not null)
            {
                await _hubspotConnectionService.PatchAsync(
                    connection.Id,
                    connection.SleekflowCompanyId,
                    connection.Name,
                    false);
            }

            throw;
        }
    }

    public Task<string> AuthenticateAsync(string sleekflowCompanyId, string returnToUrl)
    {
        var clientId = _hubspotConfig.HubspotClientId;
        var hubSpotOauthCallbackUrl = _hubspotConfig.HubspotOauthCallbackUrl;

        var state = new State(sleekflowCompanyId, returnToUrl, null, null);
        var encryptedState = AesUtils.AesEncryptBase64(
            JsonConvert.SerializeObject(state),
            _hubspotConfig.HubspotOauthStateEncryptionKey);

        _logger.LogInformation(
            "Started AuthenticateAsync. encryptedState {EncryptedState}, state {State}, sleekflowCompanyId {SleekflowCompanyId}",
            encryptedState,
            state,
            sleekflowCompanyId);

        // https://app.hubspot.com/oauth/authorize?
        // client_id=2295be6f-255c-41b6-9f4b-8e0a586665e6
        // &redirect_uri=https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/hubspot-integrator/AuthenticateCallback
        // &scope=crm.objects.contacts.read%20crm.objects.contacts.write%20settings.users.read%20crm.schemas.contacts.read%20crm.schemas.contacts.write%20crm.objects.owners.read%20settings.users.teams.read
#pragma warning disable S1075
        var redirectUrl = "https://app.hubspot.com/oauth/authorize?" +
#pragma warning restore S1075
                          $"client_id={clientId}" +
                          $"&redirect_uri={hubSpotOauthCallbackUrl}" +
                          $"&scope=crm.objects.contacts.read%20crm.objects.contacts.write%20settings.users.read%20crm.schemas.contacts.read%20crm.schemas.contacts.write%20crm.objects.owners.read%20settings.users.teams.read" +
                          $"&state={WebUtility.UrlEncode(encryptedState)}";

        return Task.FromResult(redirectUrl);
    }

    public Task<string> AuthenticateV2Async(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl)
    {
        var clientId = _hubspotConfig.HubspotClientId;
        var hubSpotOauthCallbackUrl = _hubspotConfig.HubspotOauthCallbackUrl;

        var state = new State(sleekflowCompanyId, null, successUrl, failureUrl);
        var encryptedState = AesUtils.AesEncryptBase64(
            JsonConvert.SerializeObject(state),
            _hubspotConfig.HubspotOauthStateEncryptionKey);

        _logger.LogInformation(
            "Started AuthenticateAsync. encryptedState {EncryptedState}, state {State}, sleekflowCompanyId {SleekflowCompanyId}",
            encryptedState,
            state,
            sleekflowCompanyId);

#pragma warning disable S1075
        var redirectUrl = "https://app.hubspot.com/oauth/authorize?" +
#pragma warning restore S1075
                          $"client_id={clientId}" +
                          $"&redirect_uri={hubSpotOauthCallbackUrl}" +
                          $"&scope=crm.objects.contacts.read%20crm.objects.contacts.write%20settings.users.read%20crm.schemas.contacts.read%20crm.schemas.contacts.write%20crm.objects.owners.read%20settings.users.teams.read" +
                          $"&optional_scope=crm.objects.custom.read%20crm.objects.custom.write%20crm.schemas.custom.read" +
                          $"&state={WebUtility.UrlEncode(encryptedState)}";

        return Task.FromResult(redirectUrl);
    }

    public async Task<HubspotAuthentication?> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId)
    {
        return await ReAuthenticateAndStore(sleekflowCompanyId, sleekflowCompanyId);
    }

    public async Task<HubspotAuthentication?> ReAuthenticateAndStoreAsync(
        string id,
        string sleekflowCompanyId)
    {
        return await ReAuthenticateAndStore(id, sleekflowCompanyId);
    }

    public async Task<(
            HubspotAuthentication Authentication,
            bool IsV2Authentication,
            string? ReturnToUrl,
            string? SuccessUrl,
            string? FailureUrl)>
        HandleAuthenticateCallbackAndStoreAsync(string code, string encryptedState)
    {
        var decryptStateStr =
            AesUtils.AesDecryptBase64(
                encryptedState,
                _hubspotConfig.HubspotOauthStateEncryptionKey);
        var state = decryptStateStr.ToObject<State>();
        if (state == null || string.IsNullOrEmpty(state.SleekflowCompanyId))
        {
            _logger.LogInformation("The SleekflowCompanyId is null. The state is invalid");

            throw new Exception("Unable to get a correct state.");
        }

        if (state.ReturnToUrl is not null)
        {
            var result = await HandleAuthenticateCallbackAndStore(
                state,
                code,
                encryptedState);

            return (result.Authentication, false, result.ReturnToUrl, null, null);
        }

        var resultV2 =
            await HandleAuthenticateCallbackAndStoreV2(state, code, encryptedState);

        return (resultV2.Authentication, true, null, resultV2.SuccessUrl, resultV2.FailureUrl);
    }

    public async Task<HubspotAuthentication?> GetHubspotAuthenticationWithPortalIdAsync(long portalId)
    {
        return (await _hubspotAuthenticationRepository.GetObjectsAsync(
            hubspotAuthentication =>
                hubspotAuthentication.AccountInformation != null
                && hubspotAuthentication.AccountInformation.PortalId == portalId)).FirstOrDefault();
    }

    public async Task DeleteAsync(string id)
    {
        var authentication = await _hubspotAuthenticationRepository.GetAsync(
            id,
            id);
        if (authentication is null)
        {
            throw new SfNotFoundObjectException(id, id);
        }

        var deleteAsync = await _hubspotAuthenticationRepository.DeleteAsync(
            id,
            id);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the HubspotAuthentication with id {id}, sleekflowCompanyId {id}");
        }
    }

    private async Task<(
            HubspotAuthentication Authentication,
            string? ReturnToUrl)>
        HandleAuthenticateCallbackAndStore(State state, string code, string encryptedState)
    {
        _logger.LogInformation(
            "Started HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "authorization_code"),
            new ("code", code),
            new ("client_id", _hubspotConfig.HubspotClientId),
            new ("client_secret", _hubspotConfig.HubspotClientSecret),
            new ("redirect_uri", _hubspotConfig.HubspotOauthCallbackUrl),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
#pragma warning disable S1075
            "https://api.hubapi.com/oauth/v1/token")
#pragma warning restore S1075
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (httpResponseMessage.IsSuccessStatusCode == false)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotOauthCallbackErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", state.SleekflowCompanyId! },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from Hubspot. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from Hubspot");
        }

        // {
        //     "token_type": "bearer",
        //     "refresh_token": "91ac601d-d61c-42a2-88bc-e9777f89dc3e",
        //     "access_token": "CIj5st-WMBINAAEAQAAAASAAAAAAARiL5MgKIKvE2xUomswcMhTU4YPVEReUJmuUjor_Q-vtl7-PazowAAAAQQAAAAAAAAAAAAAAAACAAAAAAAAAAAAAIAAAAAAA4BEAAAAAAEAAgAEAABACQhSLqLnPZWWxcn6vaXwab2dTC7FDYEoDbmExUgBaAA",
        //     "expires_in": 1800
        // }
        var oauth2TokenOutput = readAsStringAsync.ToObject<Dictionary<string, object?>>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput["token_type"] == null
            || oauth2TokenOutput["access_token"] == null
            || oauth2TokenOutput["refresh_token"] == null
            || oauth2TokenOutput["expires_in"] == null)
        {
            _logger.LogError(
                "Unable to deserialize a success /oauth2/token response from Hubspot. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to deserialize a success /oauth2/token response from Hubspot");
        }

        var accessToken = (string) oauth2TokenOutput["access_token"]!;
        var accountInformation = await GetHubspotAccountInformationAsync(accessToken);

        var authentication = new HubspotAuthentication(
            state.SleekflowCompanyId!,
            state.SleekflowCompanyId!,
            (string) oauth2TokenOutput["token_type"]!,
            (string) oauth2TokenOutput["access_token"]!,
            (string) oauth2TokenOutput["refresh_token"]!,
            (long) oauth2TokenOutput["expires_in"]!,
            DateTimeOffset.UtcNow,
            oauth2TokenOutput,
            accountInformation);

        var upsertAsync = await _hubspotAuthenticationRepository.UpsertAsync(
            authentication,
            state.SleekflowCompanyId!);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert SalesforceAuthentication.");
        }

        _logger.LogInformation(
            "Completed HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        return (authentication, state.ReturnToUrl);
    }

    private async Task<(
            HubspotAuthentication Authentication,
            string? SuccessUrl,
            string? FailureUrl)>
        HandleAuthenticateCallbackAndStoreV2(State state, string code, string encryptedState)
    {
        _logger.LogInformation(
            "Started HandleAuthenticateCallbackAndStoreAsyncV2. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "authorization_code"),
            new ("code", code),
            new ("client_id", _hubspotConfig.HubspotClientId),
            new ("client_secret", _hubspotConfig.HubspotClientSecret),
            new ("redirect_uri", _hubspotConfig.HubspotOauthCallbackUrl),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
#pragma warning disable S1075
            "https://api.hubapi.com/oauth/v1/token")
#pragma warning restore S1075
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (httpResponseMessage.IsSuccessStatusCode == false)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotOauthCallbackErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", state.SleekflowCompanyId! },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from Hubspot. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from Hubspot");
        }

        // {
        //     "token_type": "bearer",
        //     "refresh_token": "91ac601d-d61c-42a2-88bc-e9777f89dc3e",
        //     "access_token": "CIj5st-WMBINAAEAQAAAASAAAAAAARiL5MgKIKvE2xUomswcMhTU4YPVEReUJmuUjor_Q-vtl7-PazowAAAAQQAAAAAAAAAAAAAAAACAAAAAAAAAAAAAIAAAAAAA4BEAAAAAAEAAgAEAABACQhSLqLnPZWWxcn6vaXwab2dTC7FDYEoDbmExUgBaAA",
        //     "expires_in": 1800
        // }
        var oauth2TokenOutput = readAsStringAsync.ToObject<Dictionary<string, object?>>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput["token_type"] == null
            || oauth2TokenOutput["access_token"] == null
            || oauth2TokenOutput["refresh_token"] == null
            || oauth2TokenOutput["expires_in"] == null)
        {
            _logger.LogError(
                "Unable to deserialize a success /oauth2/token response from Hubspot. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to deserialize a success /oauth2/token response from Hubspot");
        }

        var accessToken = (string) oauth2TokenOutput["access_token"]!;
        var accountInformation = await GetHubspotAccountInformationAsync(accessToken);

        var authentication = new HubspotAuthentication(
            _idService.GetId("HubspotAuthentication"),
            state.SleekflowCompanyId!,
            (string) oauth2TokenOutput["token_type"]!,
            (string) oauth2TokenOutput["access_token"]!,
            (string) oauth2TokenOutput["refresh_token"]!,
            (long) oauth2TokenOutput["expires_in"]!,
            DateTimeOffset.UtcNow,
            oauth2TokenOutput,
            accountInformation);

        var upsertAsync = await _hubspotAuthenticationRepository.UpsertAsync(
            authentication,
            authentication.Id);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert HubspotAuthentication.");
        }

        _logger.LogInformation(
            "Completed HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        return (authentication, state.SuccessUrl, state.FailureUrl);
    }

    private async Task<HubspotAuthentication?> ReAuthenticateAndStore(
        string id,
        string sleekflowCompanyId)
    {
        var authentication = await _hubspotAuthenticationRepository.GetOrDefaultAsync(id, id);
        if (authentication == null || authentication.RefreshToken == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started ReAuthenticateAndStoreAsync. sleekflowCompanyId {SleekflowCompanyId}",
            sleekflowCompanyId);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "refresh_token"),
            new ("client_id", _hubspotConfig.HubspotClientId),
            new ("client_secret", _hubspotConfig.HubspotClientSecret),
            new ("refresh_token", authentication.RefreshToken),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            "https://api.hubapi.com/oauth/v1/token")
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (httpResponseMessage.IsSuccessStatusCode == false)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotOauthReAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth2/token response from Hubspot. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from Hubspot");
        }

        var oauth2TokenOutput = readAsStringAsync.ToObject<Dictionary<string, object?>>();
        if (oauth2TokenOutput == null || oauth2TokenOutput["access_token"] == null ||
            oauth2TokenOutput["refresh_token"] == null)
        {
            _logger.LogError(
                "Unable to get a success /oauth2/token response from Hubspot. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from Hubspot");
        }

        authentication.AccessToken = (string) oauth2TokenOutput["access_token"]!;
        authentication.RefreshToken = (string) oauth2TokenOutput["refresh_token"]!;
        authentication.IssuedAt = DateTimeOffset.UtcNow;
        authentication.RefreshRes = oauth2TokenOutput;

        var upsertAsync = await _hubspotAuthenticationRepository.UpsertAsync(authentication, authentication.Id);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert HubspotAuthentication.");
        }

        _logger.LogInformation(
            "Completed ReAuthenticateAndStoreAsync. sleekflowCompanyId {SleekflowCompanyId}",
            sleekflowCompanyId);

        return authentication;
    }

    private async Task<AccountInformation> GetHubspotAccountInformationAsync(string accessToken)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri("https://api.hubapi.com/account-info/v3/details"),
        };
        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var httpResponseMessage = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMessage.Content.ReadAsStringAsync();
        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            throw new Exception(
                $"The httpResponseMessage=[{httpResponseMessage}], str=[{str}], objectCodeName=[AccountInformation] does not exist");
        }

        var accountInformation = str.ToObject<AccountInformation>();
        if (accountInformation == null)
        {
            throw new Exception(
                $"The httpResponseMessage=[{httpResponseMessage}], str=[{str}], objectCodeName=[AccountInformation] does not exist");
        }

        return accountInformation;
    }
}