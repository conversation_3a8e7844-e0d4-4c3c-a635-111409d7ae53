using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderSubscriptions : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderSubscriptions(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderSubscriptionsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [Required]
        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [JsonProperty("typed_ids")]
        public List<TypedId>? TypedIds { get; set; }

        [JsonConstructor]
        public GetProviderSubscriptionsInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId,
            string entityTypeName,
            List<TypedId>? typedIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
            EntityTypeName = entityTypeName;
            TypedIds = typedIds;
        }
    }

    public class GetProviderSubscriptionsOutput
    {
        [JsonProperty("subscriptions")]
        public List<ProviderSubscriptionDto> Subscriptions { get; set; }

        [JsonConstructor]
        public GetProviderSubscriptionsOutput(
            List<ProviderSubscriptionDto> subscriptions)
        {
            Subscriptions = subscriptions;
        }
    }

    public async Task<GetProviderSubscriptionsOutput> F(
        GetProviderSubscriptionsInput getProviderSubscriptionsInput)
    {
        var providerService = _providerSelector.GetProviderService(
            getProviderSubscriptionsInput.ProviderName);

        var output = await providerService.GetProviderSubscriptionsAsync(
            getProviderSubscriptionsInput.SleekflowCompanyId,
            getProviderSubscriptionsInput.ProviderConnectionId,
            getProviderSubscriptionsInput.EntityTypeName,
            getProviderSubscriptionsInput.TypedIds);

        return new GetProviderSubscriptionsOutput(output.Subscriptions);
    }
}