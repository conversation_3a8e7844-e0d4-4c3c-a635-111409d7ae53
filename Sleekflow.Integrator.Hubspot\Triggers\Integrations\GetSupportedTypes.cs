using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetSupportedTypes : ITrigger
{
    private readonly IProviderService _providerService;

    public GetSupportedTypes(IProviderService providerService)
    {
        _providerService = providerService;
    }

    public class GetSupportedTypesInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetSupportedTypesInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetSupportedTypesOutput
    {
        [JsonProperty("supported_types")]
        public List<ProviderService.SupportedEntityType> SupportedTypes { get; set; }

        [JsonConstructor]
        public GetSupportedTypesOutput(List<ProviderService.SupportedEntityType> supportedTypes)
        {
            SupportedTypes = supportedTypes;
        }
    }

    public Task<GetSupportedTypesOutput> F(
        GetSupportedTypesInput getSupportedTypesInput)
    {
        return Task.FromResult(new GetSupportedTypesOutput(_providerService.GetSupportedEntityTypes()));
    }
}