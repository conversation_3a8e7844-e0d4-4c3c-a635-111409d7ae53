using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class GetManagementWhatsappCloudApiConversationUsageAnalytic
    : ITrigger<
        GetManagementWhatsappCloudApiConversationUsageAnalytic.
        GetManagementWhatsappCloudApiConversationUsageAnalyticInput,
        GetManagementWhatsappCloudApiConversationUsageAnalytic.
        GetManagementWhatsappCloudApiConversationUsageAnalyticOutput>
{
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWabaService _wabaService;
    private readonly ILogger<GetManagementWhatsappCloudApiConversationUsageAnalytic> _logger;

    public GetManagementWhatsappCloudApiConversationUsageAnalytic(
        ILogger<GetManagementWhatsappCloudApiConversationUsageAnalytic> logger,
        IWabaService wabaService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    {
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _wabaService = wabaService;
        _logger = logger;
    }

    public class GetManagementWhatsappCloudApiConversationUsageAnalyticInput
    {
        [JsonConstructor]
        public GetManagementWhatsappCloudApiConversationUsageAnalyticInput(
            string facebookBusinessId,
            DateTimeOffset start,
            DateTimeOffset end,
            string granularity)
        {
            FacebookBusinessId = facebookBusinessId;
            Start = start;
            End = end;
            Granularity = granularity;
        }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [Required]
        [JsonProperty("start")]
        public DateTimeOffset Start { get; set; }

        [Required]
        [JsonProperty("end")]
        public DateTimeOffset End { get; set; }

        [Required]
        [RegularExpression("^(HALF_HOUR|DAILY|MONTHLY)$")]
        [JsonProperty("granularity")]
        public string Granularity { get; set; }
    }

    public class GetManagementWhatsappCloudApiConversationUsageAnalyticOutput
    {
        [JsonProperty("summarized_conversation_usage_analytic")]
        public WhatsappCloudApiDetailedConversationUsageAnalyticDto SummarizedConversationUsageAnalytic { get; set; }

        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("facebook_business_name")]
        public string? FacebookBusinessName { get; set; }

        [JsonProperty("waba_conversation_usage_analytics")]
        public List<WhatsappCloudApiWabaConversationUsageAnalytic> WabaConversationUsageAnalytics { get; set; }

        [JsonConstructor]
        public GetManagementWhatsappCloudApiConversationUsageAnalyticOutput(
            WhatsappCloudApiDetailedConversationUsageAnalyticDto summarizedConversationUsageAnalytic,
            string facebookBusinessId,
            string? facebookBusinessName,
            List<WhatsappCloudApiWabaConversationUsageAnalytic> wabaConversationUsageAnalytics)
        {
            SummarizedConversationUsageAnalytic = summarizedConversationUsageAnalytic;
            FacebookBusinessId = facebookBusinessId;
            FacebookBusinessName = facebookBusinessName;
            WabaConversationUsageAnalytics = wabaConversationUsageAnalytics;
        }
    }

    public async Task<GetManagementWhatsappCloudApiConversationUsageAnalyticOutput> F(
        GetManagementWhatsappCloudApiConversationUsageAnalyticInput input)
    {
        // Adjust End date for including the specified date
        switch (input.Granularity)
        {
            case "HALF_HOUR":
                input.End = input.End.AddMinutes(30);
                break;
            case "DAILY":
            case "MONTHLY":
                input.End = input.End.AddDays(1);
                break;
        }

        if (input.Start.Date > DateTimeOffset.UtcNow.Date.AddHours(12))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Start date cannot later then today",
                        new[]
                        {
                            nameof(input.Start)
                        })
                });
        }

        if (input.Start > input.End)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Start cannot later then End",
                        new[]
                        {
                            nameof(input.End),
                            nameof(input.Start)
                        })
                });
        }

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(input.FacebookBusinessId);

        if (wabas.Count == 0)
        {
            throw new SfUserFriendlyException("No Waba found");
        }

        var allWabaConversationUsageAnalytics =
            new List<(WhatsappCloudApiDetailedConversationUsageAnalyticDto ConversationUsageAnalyic, Models.WhatsappCloudApis.Wabas.Waba Waba)>();

        foreach (var waba in wabas)
        {
            allWabaConversationUsageAnalytics.Add(
                await _businessBalanceTransactionLogService
                    .GetWhatsappCloudApiConversationUsageAnalyticByWaba(
                        input.FacebookBusinessId,
                        waba,
                        input.Start,
                        input.End,
                        input.Granularity,
                        true));
        }

        if (allWabaConversationUsageAnalytics.Count == 0)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ("No Waba found")
                });
        }

        var granularConversationUsageAnalytics = allWabaConversationUsageAnalytics
            .SelectMany(x => x.ConversationUsageAnalyic.GranularConversationUsageAnalytics)
            .GroupBy(analytic => analytic.Start)
            .Select(
                analytic => new WhatsappCloudApiGranularConversationUsageAnalyticDto(
                    analytic.Sum(c => c.BusinessInitiatedPaidQuantity),
                    analytic.Sum(c => c.BusinessInitiatedFreeTierQuantity),
                    analytic.Sum(c => c.UserInitiatedPaidQuantity),
                    analytic.Sum(c => c.UserInitiatedFreeTierQuantity),
                    analytic.Sum(c => c.UserInitiatedFreeEntryPointQuantity),
                    CloudApiUtils.SumConversationCategoryQuantities(
                        analytic.Where(x => x.ConversationCategoryQuantities != null)
                            .Select(c => c.ConversationCategoryQuantities!)
                            .ToList()),
                    analytic.Select(y => y.Used).Aggregate(new Money(CurrencyIsoCodes.USD, 0), MoneyExtensions.Add),
                    analytic.Where(x => x.Markup != null).Select(y => y.Markup)
                        .Aggregate(
                            new Money(CurrencyIsoCodes.USD, 0),
                            MoneyExtensions.Add!),
                    analytic.Where(x => x.TransactionHandlingFee != null).Select(y => y.TransactionHandlingFee)
                        .Aggregate(
                            new Money(CurrencyIsoCodes.USD, 0),
                            MoneyExtensions.Add!),
                    analytic.First().Start,
                    analytic.First().End))
            .ToList();

        var conversationAnalytics = allWabaConversationUsageAnalytics.Select(x => x.ConversationUsageAnalyic).ToArray();

        var summarizedConversationUsageAnalytic = new WhatsappCloudApiDetailedConversationUsageAnalyticDto(
            granularConversationUsageAnalytics,
            conversationAnalytics.Sum(c => c.TotalBusinessInitiatedPaidQuantity),
            conversationAnalytics.Sum(c => c.TotalBusinessInitiatedFreeTierQuantity),
            conversationAnalytics.Sum(c => c.TotalUserInitiatedPaidQuantity),
            conversationAnalytics.Sum(c => c.TotalUserInitiatedFreeTierQuantity),
            conversationAnalytics.Sum(c => c.TotalUserInitiatedFreeEntryPointQuantity),
            CloudApiUtils.SumConversationCategoryQuantities(
                conversationAnalytics.Where(x => x.ConversationCategoryQuantities != null)
                    .Select(c => c.ConversationCategoryQuantities!)
                    .ToList()),
            conversationAnalytics
                .Select(y => y.TotalUsed)
                .Aggregate(
                    new Money(CurrencyIsoCodes.USD, 0),
                    MoneyExtensions.Add),
            conversationAnalytics
                .Where(x => x.TotalMarkup != null)
                .Select(y => y.TotalMarkup!)
                .Aggregate(
                    new Money(CurrencyIsoCodes.USD, 0),
                    MoneyExtensions.Add),
            conversationAnalytics
                .Where(x => x.TotalTransactionHandlingFee != null)
                .Select(y => y.TotalTransactionHandlingFee!)
                .Aggregate(
                    new Money(CurrencyIsoCodes.USD, 0),
                    MoneyExtensions.Add),
            conversationAnalytics.First().Granularity,
            conversationAnalytics.First().Start,
            conversationAnalytics.First().End);

        return new GetManagementWhatsappCloudApiConversationUsageAnalyticOutput(
            summarizedConversationUsageAnalytic,
            allWabaConversationUsageAnalytics.First().Waba.FacebookBusinessId,
            allWabaConversationUsageAnalytics.First().Waba.FacebookWabaBusinessName,
            allWabaConversationUsageAnalytics.Select(
                x =>
                    new WhatsappCloudApiWabaConversationUsageAnalytic(
                            x.ConversationUsageAnalyic,
                            x.Waba.FacebookBusinessId,
                            x.Waba.FacebookWabaBusinessName,
                            new FacebookBusinessWabaDto(
                                x.Waba,
                                CloudApiUtils.GetFacebookTimezone(
                                    x.Waba.FacebookWabaSnapshot["timezone_id"].ToString())))).ToList());
    }
}