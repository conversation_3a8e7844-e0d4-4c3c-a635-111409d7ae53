using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.DependencyInjection;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Triggers.Languages;

[TriggerGroup(ControllerNames.Languages)]
public class GetLanguages : ITrigger<GetLanguages.GetLanguagesInput, GetLanguages.GetLanguagesOutput>
{
    public class GetLanguagesInput
    {
    }

    public class GetLanguagesOutput
    {
        [JsonProperty("languages")]
        public List<LanguageDto> Languages { get; }

        [JsonConstructor]
        public GetLanguagesOutput(
            List<LanguageDto> languages)
        {
            Languages = languages;
        }
    }

    public Task<GetLanguagesOutput> F(GetLanguagesInput getLanguagesInput)
    {
        var languageIsoCodeToCultureInfo = CultureUtils.GetLanguageIsoCodeToCultureInfo();

        return Task.FromResult(
            new GetLanguagesOutput(
                languageIsoCodeToCultureInfo
                    .Select(e => new LanguageDto(e.Key, e.Value.EnglishName, e.Value.NativeName))
                    .ToList()));
    }
}