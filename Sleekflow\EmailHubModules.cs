using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.EmailHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class EmailHubModules
{
    public static void BuildEmailHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON>d<PERSON><PERSON><PERSON><IEmailHubDbConfig>(new Mock<IEmailHubDbConfig>().Object);
        b.<PERSON><PERSON><PERSON><IEmailHubDbResolver>(new Mock<IEmailHubDbResolver>().Object);

#else
        var emailHubDbConfig = new EmailHubDbConfig();

        b.<PERSON>d<PERSON><PERSON><PERSON><IEmailHubDbConfig>(emailHubDbConfig);
        b.<PERSON>d<PERSON><PERSON><PERSON><IEmailHubDbResolver, EmailHubDbResolver>();
#endif
    }
}