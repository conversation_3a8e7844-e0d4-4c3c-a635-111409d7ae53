﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Hubspot;

[TriggerGroup(TriggerGroups.InflowActions)]
public class TerminateLoopThroughHubspotObjects
    : ITrigger<
        TerminateLoopThroughHubspotObjects.TerminateLoopThroughHubspotObjectsInput,
        TerminateLoopThroughHubspotObjects.TerminateLoopThroughHubspotObjectsOutput>
{
    private readonly IProviderSelector _providerSelector;

    public TerminateLoopThroughHubspotObjects(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class TerminateLoopThroughHubspotObjectsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughHubspotObjectsInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class TerminateLoopThroughHubspotObjectsOutput
    {
        [JsonProperty("is_terminated")]
        public bool IsTerminated { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughHubspotObjectsOutput(bool isTerminated)
        {
            IsTerminated = isTerminated;
        }
    }

    public async Task<TerminateLoopThroughHubspotObjectsOutput> F(
        TerminateLoopThroughHubspotObjectsInput input)
    {
        var providerService = _providerSelector.GetProviderService("hubspot-integrator");

        var isTerminated = await providerService.TerminateInProgressLoopThroughExecutionAsync(
            input.FlowHubWorkflowId,
            input.FlowHubWorkflowVersionedId,
            input.SleekflowCompanyId);

        return new TerminateLoopThroughHubspotObjectsOutput(isTerminated);
    }
}