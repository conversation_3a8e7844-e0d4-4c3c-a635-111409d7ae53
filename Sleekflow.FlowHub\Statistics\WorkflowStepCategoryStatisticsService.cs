﻿using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Statistics;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Ids;
using Sleekflow.Locks;

namespace Sleekflow.FlowHub.Statistics;

public interface IWorkflowStepCategoryStatisticsService
{
    Task RecordWorkflowStepCategoriesAsync(ProxyWorkflow workflow);

    Task MarkWorkflowStepCategoriesInvalidAsync(
        string sleekflowCompanyId,
        string workflowId);
}

public sealed class WorkflowStepCategoryStatisticsService : IWorkflowStepCategoryStatisticsService, IScopedService
{
    private const string LOCK_CONSTANT = "WorkflowStepCategoryStatistics";

    private readonly IIdService _idService;
    private readonly IWorkflowStepCategoryStatisticsRepository _workflowStepCategoryStatisticsRepository;
    private readonly IWorkflowStepCategoryProvider _workflowStepCategoryProvider;
    private readonly ILogger<WorkflowStepCategoryStatisticsService> _logger;
    private readonly ILockService _lockService;

    public WorkflowStepCategoryStatisticsService(
        IIdService idService,
        IWorkflowStepCategoryStatisticsRepository workflowStepCategoryStatisticsRepository,
        IWorkflowStepCategoryProvider workflowStepCategoryProvider,
        ILogger<WorkflowStepCategoryStatisticsService> logger,
        ILockService lockService)
    {
        _idService = idService;
        _workflowStepCategoryStatisticsRepository = workflowStepCategoryStatisticsRepository;
        _workflowStepCategoryProvider = workflowStepCategoryProvider;
        _logger = logger;
        _lockService = lockService;
    }

    public async Task RecordWorkflowStepCategoriesAsync(ProxyWorkflow workflow)
    {
        Lock? @lock = null;

        try
        {
            var workflowStepCategoryCountDict = _workflowStepCategoryProvider.GetStepCategoryCountDict(workflow.Steps);
            var workflowTriggerStepCategory = _workflowStepCategoryProvider.GetWorkflowTriggerStepCategory(workflow.Triggers);

            @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    LOCK_CONSTANT,
                    workflow.SleekflowCompanyId,
                    workflow.WorkflowId
                },
                TimeSpan.FromSeconds(15),
                TimeSpan.FromSeconds(30));

            var workflowStepCategoryStatistics =
                (await _workflowStepCategoryStatisticsRepository
                    .GetObjectsAsync(
                        x =>
                            x.SleekflowCompanyId == workflow.SleekflowCompanyId
                            && x.WorkflowId == workflow.WorkflowId
                            && x.ValidTo == DateTimeOffset.MaxValue))
                .SingleOrDefault();

            if (workflowStepCategoryStatistics is not null)
            {
                await _workflowStepCategoryStatisticsRepository.PatchAsync(
                    id: workflowStepCategoryStatistics.Id,
                    partitionKey: workflow.SleekflowCompanyId,
                    new List<PatchOperation>()
                    {
                        PatchOperation.Set("/valid_to", workflow.UpdatedAt),
                        PatchOperation.Set("/updated_at", workflow.UpdatedAt)
                    },
                    eTag: workflowStepCategoryStatistics.ETag);
            }

            await _workflowStepCategoryStatisticsRepository.CreateAsync(
                new WorkflowStepCategoryStatistics(
                    _idService.GetId(SysTypeNames.WorkflowStepCategoryStatistics),
                    workflow.SleekflowCompanyId,
                    workflow.WorkflowId,
                    workflow.WorkflowVersionedId,
                    workflow.WorkflowType,
                    workflowTriggerStepCategory,
                    workflowStepCategoryCountDict,
                    workflow.CreatedAt,
                    DateTimeOffset.MaxValue,
                    workflow.CreatedAt,
                    workflow.UpdatedAt,
                    eTag: null),
                workflow.SleekflowCompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to upsert workflow category statistics for workflow {WorkflowVersionedId} in company {SleekflowCompanyId}",
                workflow.WorkflowVersionedId,
                workflow.SleekflowCompanyId);
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }
    }

    public async Task MarkWorkflowStepCategoriesInvalidAsync(
        string sleekflowCompanyId,
        string workflowId)
    {
        Lock? @lock = null;

        try
        {
            @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    LOCK_CONSTANT,
                    sleekflowCompanyId,
                    workflowId
                },
                TimeSpan.FromSeconds(15),
                TimeSpan.FromSeconds(30));

            var workflowStepCategoryStatistics =
                (await _workflowStepCategoryStatisticsRepository
                    .GetObjectsAsync(
                        x =>
                            x.SleekflowCompanyId == sleekflowCompanyId
                            && x.WorkflowId == workflowId
                            && x.ValidTo == DateTimeOffset.MaxValue))
                .SingleOrDefault();

            if (workflowStepCategoryStatistics is not null)
            {
                await _workflowStepCategoryStatisticsRepository.PatchAsync(
                    id: workflowStepCategoryStatistics.Id,
                    partitionKey: sleekflowCompanyId,
                    new List<PatchOperation>()
                    {
                        PatchOperation.Set("/valid_to", DateTimeOffset.UtcNow),
                        PatchOperation.Set("/updated_at", DateTimeOffset.UtcNow)
                    },
                    eTag: workflowStepCategoryStatistics.ETag);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to mark end period of workflow category statistics for workflow {WorkflowId} in company {SleekflowCompanyId}",
                workflowId,
                sleekflowCompanyId);
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }
    }
}