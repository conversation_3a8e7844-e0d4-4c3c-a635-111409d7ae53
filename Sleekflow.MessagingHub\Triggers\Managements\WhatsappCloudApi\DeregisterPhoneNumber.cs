using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class DeregisterPhoneNumber
    : ITrigger<
        DeregisterPhoneNumber.DeregisterPhoneNumberInput,
        DeregisterPhoneNumber.DeregisterPhoneNumberOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<DeregisterPhoneNumber> _logger;
    private readonly ICommonRetryPolicyService _commonRetryPolicyService;

    public DeregisterPhoneNumber(
        IWabaService wabaService,
        ILogger<DeregisterPhoneNumber> logger,
        ICommonRetryPolicyService commonRetryPolicyService)
    {
        _wabaService = wabaService;
        _logger = logger;
        _commonRetryPolicyService = commonRetryPolicyService;
    }

    public class DeregisterPhoneNumberInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_phone_number_id")]
        public string MessagingHubPhoneNumberId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }
    }

    public class DeregisterPhoneNumberOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public DeregisterPhoneNumberOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<DeregisterPhoneNumberOutput> F(
        DeregisterPhoneNumberInput deRegisterPhoneNumberInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            deRegisterPhoneNumberInput.SleekflowStaffId,
            deRegisterPhoneNumberInput.SleekflowStaffTeamIds);
        _logger.LogInformation("Sleekflow staff details {SleekflowStaff}", JsonConvert.SerializeObject(sleekflowStaff));

        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            deRegisterPhoneNumberInput.MessagingHubWabaId,
            deRegisterPhoneNumberInput.SleekflowCompanyId,
            deRegisterPhoneNumberInput.MessagingHubPhoneNumberId);

        if (waba == null)
        {
            return new DeregisterPhoneNumberOutput(false);
        }

        var facebookPhoneNumberId = waba.WabaPhoneNumbers
            .Where(x => x.Id == deRegisterPhoneNumberInput.MessagingHubPhoneNumberId)
            .Select(x => x.FacebookPhoneNumberId).FirstOrDefault();

        if (facebookPhoneNumberId == null)
        {
            return new DeregisterPhoneNumberOutput(false);
        }

        var response = await _commonRetryPolicyService.GetAsyncRetryPolicy().ExecuteAsync(
            async () =>
            {
                try
                {
                    string? businessIntegrationSystemUserAccessToken = null;

                    var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) = _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);
                    if (hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null)
                    {
                        businessIntegrationSystemUserAccessToken =
                            decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken;
                    }

                    return await _wabaService.DeactivateWabaAsync(facebookPhoneNumberId, businessIntegrationSystemUserAccessToken);
                }
                catch (Exception exception)
                {
                    _logger.LogError(
                        "Deregister phone number error {MessagingHubPhoneNumberId} with {Exception}",
                        deRegisterPhoneNumberInput.MessagingHubPhoneNumberId,
                        JsonConvert.SerializeObject(exception));
                }

                return false;
            });

        return new DeregisterPhoneNumberOutput(response);
    }
}