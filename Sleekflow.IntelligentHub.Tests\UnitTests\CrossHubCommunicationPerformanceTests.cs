using System.Diagnostics;
using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.Models.FlowHub;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class CrossHubCommunicationPerformanceTests
{
  private Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>> _mockRequestClient;
  private Mock<ILogger<WorkflowCountService>> _mockLogger;
  private WorkflowCountService _workflowCountService;

  [SetUp]
  public void SetUp()
  {
    _mockRequestClient = new Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>>();
    _mockLogger = new Mock<ILogger<WorkflowCountService>>();
    _workflowCountService = new WorkflowCountService(_mockRequestClient.Object, _mockLogger.Object);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithManyAgentConfigs_CompletesWithinReasonableTime()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = Enumerable.Range(1, 50).Select(i => $"agent-config-{i}").ToList();

    // Mock MassTransit to return workflow counts for all agent configs
    var expectedWorkflowCounts = agentConfigIds.ToDictionary(id => id, _ => 2);

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    // Simulate realistic response times (50-100ms per request)
    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .Returns(async () =>
      {
        await Task.Delay(75); // Simulate network latency
        return mockResponse.Object;
      });

    var stopwatch = Stopwatch.StartNew();

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    stopwatch.Stop();

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Count, Is.EqualTo(50));

    // Since this is now a single MassTransit request, total time should be around 75ms + overhead
    // Much better than the old HTTP approach that would make 50 separate calls
    Assert.That(
      stopwatch.ElapsedMilliseconds,
      Is.LessThan(200),
      $"Performance issue: took {stopwatch.ElapsedMilliseconds}ms for single MassTransit request");

    // Verify request was made once (not 50 times like the HTTP version)
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Once);

    TestContext.WriteLine($"Processed 50 agent configs in {stopwatch.ElapsedMilliseconds}ms via MassTransit");
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_ConcurrentRequestsToSameService_HandledEfficiently()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2", "agent3"
    };

    var expectedWorkflowCounts = agentConfigIds.ToDictionary(id => id, _ => 1);

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .Returns(async () =>
      {
        await Task.Delay(50);
        return mockResponse.Object;
      });

    var stopwatch = Stopwatch.StartNew();

    // Act - Make multiple concurrent requests to the same service
    var tasks = Enumerable.Range(1, 10)
      .Select(_ => _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
        sleekflowCompanyId,
        agentConfigIds))
      .ToArray();

    var results = await Task.WhenAll(tasks);

    stopwatch.Stop();

    // Assert
    Assert.That(results.Length, Is.EqualTo(10));

    foreach (var result in results)
    {
      Assert.That(result, Is.Not.Null);
      Assert.That(result.Count, Is.EqualTo(3));
      Assert.That(result["agent1"], Is.EqualTo(1));
    }

    // Should complete efficiently with concurrent processing
    Assert.That(
      stopwatch.ElapsedMilliseconds,
      Is.LessThan(1000),
      $"Concurrent requests took too long: {stopwatch.ElapsedMilliseconds}ms");

    // Total of 10 MassTransit requests (one per call)
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Exactly(10));

    TestContext.WriteLine($"Processed 10 concurrent requests (10 MassTransit calls) in {stopwatch.ElapsedMilliseconds}ms");
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithFailuresAndRetries_DoesNotBlockOtherRequests()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2", "agent3", "agent4"
    };

    var successfulWorkflowCounts = agentConfigIds.ToDictionary(id => id, _ => 1);

    var mockSuccessResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockSuccessResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(successfulWorkflowCounts));

    var callCount = 0;
    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .Returns(async () =>
      {
        callCount++;
        if (callCount == 2) // Second call fails
        {
          await Task.Delay(50);
          throw new RequestTimeoutException("Simulated MassTransit timeout");
        }
        if (callCount == 3) // Third call is slow
        {
          await Task.Delay(200);
          return mockSuccessResponse.Object;
        }
        // Other calls are fast
        await Task.Delay(50);
        return mockSuccessResponse.Object;
      });

    var stopwatch = Stopwatch.StartNew();

    // Act - Make multiple requests, some will fail/be slow
    var tasks = new[]
    {
      _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(sleekflowCompanyId, agentConfigIds), // Fast success
      _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(sleekflowCompanyId, agentConfigIds), // Will fail
      _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(sleekflowCompanyId, agentConfigIds), // Slow success  
      _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(sleekflowCompanyId, agentConfigIds)  // Fast success
    };

    var results = await Task.WhenAll(tasks);

    stopwatch.Stop();

    // Assert
    Assert.That(results.Length, Is.EqualTo(4));
    
    // Check results - one should have failed and returned zeros
    var successCount = results.Count(r => r["agent1"] == 1);
    var failureCount = results.Count(r => r["agent1"] == 0);
    
    Assert.That(successCount, Is.EqualTo(3)); // 3 successful calls
    Assert.That(failureCount, Is.EqualTo(1)); // 1 failed call

    // Should complete as fast as the slowest request (~200ms) plus some overhead
    Assert.That(
      stopwatch.ElapsedMilliseconds,
      Is.LessThan(400),
      $"Mixed scenario took too long: {stopwatch.ElapsedMilliseconds}ms");

    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Exactly(4));

    TestContext.WriteLine($"Mixed scenario (success/failure/slow) completed in {stopwatch.ElapsedMilliseconds}ms");
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_LargeScaleSimulation_MaintainsPerformance()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = Enumerable.Range(1, 100).Select(i => $"agent-config-{i}").ToList();

    // Simulate variable workflow counts (1-15 per agent config)
    var expectedWorkflowCounts = agentConfigIds.ToDictionary(
      id => id, 
      _ => Random.Shared.Next(1, 16));

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .Returns(async () =>
      {
        // Simulate processing time that scales with number of agent configs
        await Task.Delay(50 + (agentConfigIds.Count / 10)); // 50ms base + 10ms per 10 agent configs
        return mockResponse.Object;
      });

    var stopwatch = Stopwatch.StartNew();

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    stopwatch.Stop();

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Count, Is.EqualTo(100));

    // With MassTransit, this should complete much faster than HTTP approach
    // Single request vs 100 HTTP requests
    Assert.That(
      stopwatch.ElapsedMilliseconds,
      Is.LessThan(200),
      $"Large scale test took too long: {stopwatch.ElapsedMilliseconds}ms for 100 agent configs");

    // Verify all counts are within expected range (1-15 based on our mock responses)
    foreach (var kvp in result)
    {
      Assert.That(kvp.Value, Is.InRange(1, 15), $"Agent config {kvp.Key} has unexpected count: {kvp.Value}");
    }

    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Once); // Single MassTransit request vs 100 HTTP requests

    TestContext.WriteLine($"Large scale test: 100 agent configs processed in {stopwatch.ElapsedMilliseconds}ms");
    TestContext.WriteLine($"Single MassTransit request vs 100 HTTP requests - massive performance improvement!");
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_ErrorRecovery_DoesNotImpactPerformance()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = Enumerable.Range(1, 20).Select(i => $"agent-config-{i}").ToList();

    var callCount = 0;
    var expectedWorkflowCounts = agentConfigIds.ToDictionary(id => id, _ => 3);

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .Returns(async () =>
      {
        callCount++;
        if (callCount <= 3)
        {
          await Task.Delay(30);
          throw new RequestTimeoutException("Simulated transient failure");
        }
        
        await Task.Delay(60); // Normal processing time
        return mockResponse.Object;
      });

    var stopwatch = Stopwatch.StartNew();

    // Act - Make several requests where first few fail
    var tasks = Enumerable.Range(1, 5)
      .Select(_ => _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
        sleekflowCompanyId,
        agentConfigIds))
      .ToArray();

    var results = await Task.WhenAll(tasks);

    stopwatch.Stop();

    // Assert
    Assert.That(results.Length, Is.EqualTo(5));
    
    // First 3 should fail (return zeros), last 2 should succeed
    var failedResults = results.Take(3);
    var successfulResults = results.Skip(3);
    
    foreach (var failedResult in failedResults)
    {
      Assert.That(failedResult.Values.All(v => v == 0), Is.True, "Failed requests should return zeros");
    }
    
    foreach (var successfulResult in successfulResults)
    {
      Assert.That(successfulResult.Values.All(v => v == 3), Is.True, "Successful requests should return expected counts");
    }

    // Performance should recover quickly after initial failures
    Assert.That(
      stopwatch.ElapsedMilliseconds,
      Is.LessThan(400),
      $"Error recovery test took too long: {stopwatch.ElapsedMilliseconds}ms");

    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Exactly(5));

    TestContext.WriteLine($"Error recovery test: 5 requests (3 failures + 2 successes) completed in {stopwatch.ElapsedMilliseconds}ms");
  }
} 