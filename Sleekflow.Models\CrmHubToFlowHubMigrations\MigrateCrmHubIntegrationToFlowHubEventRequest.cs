﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Models.CrmHubToFlowHubMigrations;

public class MigrateCrmHubIntegrationToFlowHubEventRequest
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("provider_name")]
    [Required]
    public string ProviderName { get; set; }

    [JsonProperty("provider_connection_id")]
    [Required]
    public string ProviderConnectionId { get; set; }

    [JsonProperty("entity_type_name_to_sync_config_dict")]
    [Required]
    public Dictionary<string, MigrateCrmHubSyncConfigDto> EntityTypeNameToSyncConfigDict { get; set; }

    [JsonProperty("entity_type_name_to_field_mappings_dict")]
    [Required]
    public Dictionary<string, List<SleekflowFieldToCrmProviderFieldMapping>> EntityTypeNameToFieldMappingsDict { get; set; }

    [Required]
    [JsonProperty("sleekflow_staff_id")]
    public string SleekflowStaffId { get; set; }

    [JsonProperty("sleekflow_staff_team_ids")]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [JsonConstructor]
    public MigrateCrmHubIntegrationToFlowHubEventRequest(
        string sleekflowCompanyId,
        string providerName,
        string providerConnectionId,
        Dictionary<string, MigrateCrmHubSyncConfigDto> entityTypeNameToSyncConfigDict,
        Dictionary<string, List<SleekflowFieldToCrmProviderFieldMapping>> entityTypeNameToFieldMappingsDict,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ProviderName = providerName;
        ProviderConnectionId = providerConnectionId;
        EntityTypeNameToSyncConfigDict = entityTypeNameToSyncConfigDict;
        EntityTypeNameToFieldMappingsDict = entityTypeNameToFieldMappingsDict;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
    }
}