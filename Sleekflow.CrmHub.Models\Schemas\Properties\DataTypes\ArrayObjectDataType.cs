﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;

namespace Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;

public sealed class ArrayObjectDataType : DataTypeBase
{
    private InnerSchema _innerSchema;

    [JsonProperty(IDataType.PropertyNameName)]
    public override string Name { get; set; }

    [JsonProperty(IDataType.PropertyNameInnerSchema)]
    public override InnerSchema? InnerSchema
    {
        get => _innerSchema;
        set
        {
            _innerSchema = value ?? throw new InvalidOperationException("Must Provide an InnerSchema for this DataType.");
        }
    }

    public override bool HasInnerSchema()
        => true;

    public override InnerSchema GetInnerSchema()
    {
        return _innerSchema;
    }

    [JsonConstructor]
    public ArrayObjectDataType(InnerSchema innerSchema)
    {
        Name = SchemaPropertyDataTypes.ArrayObject;
        _innerSchema = innerSchema;
    }
}