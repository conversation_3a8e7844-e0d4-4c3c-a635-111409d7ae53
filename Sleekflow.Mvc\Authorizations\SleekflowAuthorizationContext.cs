﻿namespace Sleekflow.Mvc.Authorizations;

/**
 * The ISleekflowAuthorizationContext is used to capture user information for the given request scope.
 *
 * Note:
 * - SleekflowTenantHubUserId refers to the ID of the tenant hub user, which is in the form of a snowflake ID.
 * - Other Sleekflow... properties refer to tenant hub workspace user details.
 **/
public interface ISleekflowAuthorizationContext
{
    // SleekflowTenantHubUserId refers to the ID of the tenant hub user, which is in the form of a snowflake ID.
    string? SleekflowTenantHubUserId { get; set; }

    // SleekflowUserId refers to the tenant hub workspace Sleekflow user ID or SleekflowCoreUserId (AspNetUser - Id).
    string? SleekflowUserId { get; set; }

    // SleekflowStaffId refers to the tenant hub workspace Sleekflow staff ID or SleekflowCoreStaffId (UserRoleStaff - Id).
    string? SleekflowStaffId { get; set; }

    // SleekflowCompanyId refers to the tenant hub workspace Sleekflow company ID or SleekflowCoreCompanyId (UserRoleStaff - CompanyId).
    string? SleekflowCompanyId { get; set; }

    // SleekflowRoles refers to the tenant hub workspace Sleekflow roles or SleekflowCoreRoles (UserRoleStaff - RoleName).
    List<string>? SleekflowRoles { get; set; }

    // SleekflowRoleIds refers to the tenant hub workspace Sleekflow role IDs or SleekflowCoreRoleIds (UserRoleStaff - RoleId).
    List<string>? SleekflowRoleIds { get; set; }

    // SleekflowTeamIds refers to the tenant hub workspace Sleekflow team IDs or SleekflowCoreTeam (CompanyTeamMembers - TeamId).
    List<string>? SleekflowTeamIds { get; set; }

    // IsRbacEnabled refers to the tenant hub company RBAC status.
    bool? IsRbacEnabled { get; set; }

    // SleekflowImpersonator refers to impersonator details in the scoped request.
    Impersonator? SleekflowImpersonator { get; set; }
}

public class SleekflowAuthorizationContext : ISleekflowAuthorizationContext
{
    public string? SleekflowTenantHubUserId { get; set; } = string.Empty;

    public string? SleekflowUserId { get; set; } = string.Empty;

    public string? SleekflowStaffId { get; set; } = string.Empty;

    public string? SleekflowCompanyId { get; set; } = string.Empty;

    public List<string>? SleekflowRoles { get; set; } = new List<string>();

    public List<string>? SleekflowRoleIds { get; set; } = new List<string>();

    public List<string>? SleekflowTeamIds { get; set; } = new List<string>();

    public bool? IsRbacEnabled { get; set; } = null;

    public Impersonator? SleekflowImpersonator { get; set; } = null;
}