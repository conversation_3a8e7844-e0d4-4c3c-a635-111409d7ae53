﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class CreateActions : ITrigger<
    CreateActions.CreateActionsInput,
    CreateActions.CreateActionsOutput>
{
    private readonly INeedConfigService _needConfigService;

    public CreateActions(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class CreateActionsInput
    {
        [Required]
        [JsonProperty("actions")]
        public List<ActionConfigDto> Actions { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonConstructor]
        public CreateActionsInput(
            List<ActionConfigDto> actions,
            string? version)
        {
            Actions = actions;
            Version = version;
        }
    }

    public class CreateActionsOutput
    {
        [JsonProperty("actions")]
        public List<ActionConfigDto> Actions { get; set; }

        [JsonConstructor]
        public CreateActionsOutput(List<ActionConfigDto> actions)
        {
            Actions = actions;
        }
    }

    public async Task<CreateActionsOutput> F(CreateActionsInput input)
    {
        return new CreateActionsOutput(
            await _needConfigService.CreateActionsAsync(
                input.Version,
                input.Actions));
    }
}