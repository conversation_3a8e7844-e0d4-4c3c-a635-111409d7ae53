using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Messages;

public abstract class BaseMessage : IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty("message_body")]
    public MessageBody MessageBody { get; set; }

    [JsonProperty("message_type")]
    public string MessageType { get; set; }

    [JsonProperty("message_content")]
    public string MessageContent { get; set; }

    [JsonProperty("message_status")]
    public string MessageStatus { get; set; }

    [JsonProperty("message_timestamp")]
    public long MessageTimeStamp => CreatedAt.ToUnixTimeSeconds();

    [JsonProperty("message_delivery_type")]
    public string MessageDeliveryType { get; set; }

    [JsonConstructor]
    protected BaseMessage(
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        MessageBody messageBody,
        string messageType,
        string messageContent,
        string messageStatus,
        string messageDeliveryType)
    {
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        MessageBody = messageBody;
        MessageType = messageType;
        MessageContent = messageContent;
        MessageStatus = messageStatus;
        MessageDeliveryType = messageDeliveryType;
    }
}