using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

public class WhatsappCommerceSetting
{
    [JsonProperty("facebook_whatsapp_commerce_setting_id")]
    public string FacebookWhatsappCommerceSettingId { get; set; }

    [JsonProperty("is_catalog_visible")]
    public bool IsCatalogVisible { get; set; }

    [JsonProperty("is_cart_enabled")]
    public bool IsCartEnabled { get; set; }

    [JsonConstructor]
    public WhatsappCommerceSetting(string facebookWhatsappCommerceSettingId, bool isCatalogVisible, bool isCartEnabled)
    {
        FacebookWhatsappCommerceSettingId = facebookWhatsappCommerceSettingId;
        IsCatalogVisible = isCatalogVisible;
        IsCartEnabled = isCartEnabled;
    }
}