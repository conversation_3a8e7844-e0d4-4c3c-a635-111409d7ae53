﻿using Newtonsoft.Json;

namespace Sleekflow.DurablePayloads;

public class StatusQueryGetOutput<TInput, TCustomStatus, TOutput>
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("instanceId")]
    public string InstanceId { get; set; }

    [JsonProperty("runtimeStatus")]
    public string RuntimeStatus { get; set; }

    [JsonProperty("input")]
    public TInput Input { get; set; }

    [JsonProperty("customStatus")]
    public TCustomStatus CustomStatus { get; set; }

    [JsonProperty("output")]
    public TOutput Output { get; set; }

    [JsonProperty("createdTime")]
    public DateTimeOffset CreatedTime { get; set; }

    [JsonProperty("lastUpdatedTime")]
    public DateTimeOffset LastUpdatedTime { get; set; }

    [JsonConstructor]
    public StatusQueryGetOutput(
        string name,
        string instanceId,
        string runtimeStatus,
        TInput input,
        TCustomStatus customStatus,
        TOutput output,
        DateTimeOffset createdTime,
        DateTimeOffset lastUpdatedTime)
    {
        Name = name;
        InstanceId = instanceId;
        RuntimeStatus = runtimeStatus;
        Input = input;
        CustomStatus = customStatus;
        Output = output;
        CreatedTime = createdTime;
        LastUpdatedTime = lastUpdatedTime;
    }
}