﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.GoogleSheets.Connections;

public interface IGoogleSheetsConnectionService
{
    Task<List<GoogleSheetsConnection>> GetConnectionsAsync(
        string sleekflowCompanyId);

    Task<GoogleSheetsConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string authenticationId,
        string organizationId,
        string name,
        bool isActive);

    Task<GoogleSheetsConnection> GetByIdAsync(
        string id,
        string sleekflowCompanyId);

    Task<GoogleSheetsConnection?> GetByAuthenticationIdAsync(
        string sleekflowCompanyId,
        string authenticationId);

    Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        string name,
        bool isActive);

    Task<GoogleSheetsConnection> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string name);

    Task DeleteAsync(
        string id,
        string sleekflowCompanyId);
}

public class GoogleSheetsConnectionService : ISingletonService, IGoogleSheetsConnectionService
{
    private readonly IGoogleSheetsConnectionRepository _googleSheetsConnectionRepository;
    private readonly IIdService _idService;

    public GoogleSheetsConnectionService(
        IGoogleSheetsConnectionRepository googleSheetsConnectionRepository,
        IIdService idService)
    {
        _googleSheetsConnectionRepository = googleSheetsConnectionRepository;
        _idService = idService;
    }

    public async Task<List<GoogleSheetsConnection>> GetConnectionsAsync(
        string sleekflowCompanyId)
    {
        return await _googleSheetsConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<GoogleSheetsConnection> CreateAndGetAsync(
        string sleekflowCompanyId,
        string authenticationId,
        string organizationId,
        string name,
        bool isActive)
    {
        var connection = new GoogleSheetsConnection(
            _idService.GetId("GoogleSheetsConnection"),
            sleekflowCompanyId,
            authenticationId,
            organizationId,
            name,
            isActive);

        return await _googleSheetsConnectionRepository.CreateAndGetAsync(connection, sleekflowCompanyId);
    }

    public async Task<GoogleSheetsConnection> GetByIdAsync(
        string id,
        string sleekflowCompanyId)
    {
        return await _googleSheetsConnectionRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<GoogleSheetsConnection?> GetByAuthenticationIdAsync(
        string sleekflowCompanyId,
        string authenticationId)
    {
        var connections = await _googleSheetsConnectionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId && x.AuthenticationId == authenticationId);

        return connections.FirstOrDefault();
    }

    public async Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        string name,
        bool isActive)
    {
        await _googleSheetsConnectionRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
                PatchOperation.Replace("/is_active", isActive),
            });
    }

    public async Task<GoogleSheetsConnection> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string name)
    {
        return await _googleSheetsConnectionRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/name", name),
            });
    }

    public async Task DeleteAsync(
        string id,
        string sleekflowCompanyId)
    {
        var connection = await _googleSheetsConnectionRepository.GetAsync(
            id,
            sleekflowCompanyId);
        if (connection is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var deleteAsync = await _googleSheetsConnectionRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the GoogleSheetsConnection with id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}