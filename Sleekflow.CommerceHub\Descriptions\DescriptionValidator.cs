using System.ComponentModel.DataAnnotations;
using Sleekflow.CommerceHub.Blobs;
using Sleekflow.CommerceHub.Models.Blobs;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Descriptions;

public interface IDescriptionValidator
{
    Task AssertValidDescriptionAsync(Description description, HashSet<string> languageIsoCodes);

    Task AssertValidDescriptionsAsync(IEnumerable<Description> descriptions, HashSet<string> languageIsoCodes);
}

public class DescriptionValidator : IDescriptionValidator, IScopedService
{
    private readonly IBlobService _blobService;

    public DescriptionValidator(
        IBlobService blobService)
    {
        _blobService = blobService;
    }

    public async Task AssertValidDescriptionAsync(
        Description description,
        HashSet<string> languageIsoCodes)
    {
        if (description.Type == DescriptionTypes.Text)
        {
            if (description.Text == null)
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            "The Text is null",
                            new[]
                            {
                                "Text"
                            })
                    });
            }

            if (!languageIsoCodes.Contains(description.Text.LanguageIsoCode))
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            "LanguageIsoCode is not valid",
                            new[]
                            {
                                "LanguageIsoCode"
                            })
                    });
            }
        }
        else if (description.Type == DescriptionTypes.Image)
        {
            if (description.Image == null
                || description.Image.BlobName == null)
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            "The Image is null",
                            new[]
                            {
                                "Image"
                            })
                    });
            }

            if (!await _blobService.IsBlobExistsAsync(description.Image.BlobName!, BlobTypes.Image))
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            "The Image is invalid",
                            new[]
                            {
                                "Image"
                            })
                    });
            }
        }
    }

    public async Task AssertValidDescriptionsAsync(
        IEnumerable<Description> descriptions,
        HashSet<string> languageIsoCodes)
    {
        foreach (var description in descriptions)
        {
            await AssertValidDescriptionAsync(description, languageIsoCodes);
        }
    }
}