using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.OnPremise.Authentications;
using Sleekflow.EmailHub.OnPremise.Communications;
using Sleekflow.EmailHub.OnPremise.Subscriptions;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Repositories;

namespace Sleekflow.EmailHub.OnPremise.Integrator;

public interface IOnPremiseIntegratorService : IEmailService
{
}

public class OnPremiseIntegratorService : EmailService, IOnPremiseIntegratorService, IScopedService
{
    public const string EmailProviderName = ProviderNames.OnPremise;

    public OnPremiseIntegratorService(
        IOnPremiseAuthenticationService emailAuthenticationService,
        IOnPremiseSubscriptionService emailSubscriptionService,
        IOnPremiseCommunicationService emailCommunicationService,
        IEmailRepository emailRepository,
        IBus bus,
        IProviderConfigService providerConfigService)
        : base(
            EmailProviderName,
            emailAuthenticationService,
            emailSubscriptionService,
            emailCommunicationService,
            emailRepository,
            bus,
            providerConfigService)
    {
    }
}