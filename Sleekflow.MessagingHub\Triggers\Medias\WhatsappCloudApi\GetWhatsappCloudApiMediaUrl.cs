using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Medias;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Medias.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Medias)]
public class GetWhatsappCloudApiMediaUrl
    : ITrigger<
        GetWhatsappCloudApiMediaUrl.GetWhatsappCloudApiMediaUrlInput,
        GetWhatsappCloudApiMediaUrl.GetWhatsappCloudApiMediaUrlOutput>
{
    private readonly ICloudApiMediaManagementFacade _cloudApiMediaManagementFacade;
    private readonly IWabaService _wabaService;
    private readonly ILogger<GetWhatsappCloudApiMediaUrl> _logger;

    public GetWhatsappCloudApiMediaUrl(ICloudApiMediaManagementFacade cloudApiMediaManagementFacade, IWabaService wabaService, ILogger<GetWhatsappCloudApiMediaUrl> logger)
    {
        _cloudApiMediaManagementFacade = cloudApiMediaManagementFacade;
        _wabaService = wabaService;
        _logger = logger;
    }

    public class GetWhatsappCloudApiMediaUrlInput
    {
        [JsonProperty("media_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string MediaId { get; set; }

        [JsonProperty("waba_id")]
        public string? WabaId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiMediaUrlInput(string mediaId, string? wabaId, string? sleekflowCompanyId)
        {
            MediaId = mediaId;
            WabaId = wabaId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetWhatsappCloudApiMediaUrlOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("mime_type")]
        public string MimeType { get; set; }

        [JsonProperty("sha256")]
        public string Sha256 { get; set; }

        [JsonProperty("file_size")]
        public string FileSize { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiMediaUrlOutput(string id, string url, string mimeType, string sha256, string fileSize)
        {
            Id = id;
            Url = url;
            MimeType = mimeType;
            Sha256 = sha256;
            FileSize = fileSize;
        }
    }

    public async Task<GetWhatsappCloudApiMediaUrlOutput> F(
        GetWhatsappCloudApiMediaUrlInput getWhatsappCloudApiMediaUrlInput)
    {
        if (string.IsNullOrEmpty(getWhatsappCloudApiMediaUrlInput.WabaId) ||
            string.IsNullOrEmpty(getWhatsappCloudApiMediaUrlInput.SleekflowCompanyId))
        {
            var (id, url, mineType, sha256, fileSize) =
                await _cloudApiMediaManagementFacade.GetCloudApiMediaUrlAsync(
                    getWhatsappCloudApiMediaUrlInput.MediaId,
                    null);
            return new GetWhatsappCloudApiMediaUrlOutput(id, url, mineType, sha256, fileSize);
        }

        var waba = await _wabaService.GetWabaOrDefaultAsync(getWhatsappCloudApiMediaUrlInput.WabaId, getWhatsappCloudApiMediaUrlInput.SleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        if (hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null)
        {
            var (id, url, mineType, sha256, fileSize) =
                await _cloudApiMediaManagementFacade.GetCloudApiMediaUrlAsync(
                    getWhatsappCloudApiMediaUrlInput.MediaId,
                    decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken);

            return new GetWhatsappCloudApiMediaUrlOutput(id, url, mineType, sha256, fileSize);
        }
        else
        {
            var (id, url, mineType, sha256, fileSize) =
                await _cloudApiMediaManagementFacade.GetCloudApiMediaUrlAsync(
                    getWhatsappCloudApiMediaUrlInput.MediaId,
                    null);
            return new GetWhatsappCloudApiMediaUrlOutput(id, url, mineType, sha256, fileSize);
        }
    }
}