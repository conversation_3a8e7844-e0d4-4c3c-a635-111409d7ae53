﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Statistics;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Statistics;

public interface IWorkflowStepCategoryStatisticsRepository : IRepository<WorkflowStepCategoryStatistics>
{
}

public sealed class WorkflowStepCategoryStatisticsRepository :
    BaseRepository<WorkflowStepCategoryStatistics>,
    IWorkflowStepCategoryStatisticsRepository,
    IScopedService
{
    public WorkflowStepCategoryStatisticsRepository(
        ILogger<WorkflowStepCategoryStatisticsRepository> logger,
        IServiceProvider serviceProvider)
        : base(
            logger,
            serviceProvider)
    {
    }
}