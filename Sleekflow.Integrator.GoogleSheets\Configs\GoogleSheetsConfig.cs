﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Integrator.GoogleSheets.Configs;

public interface IGoogleSheetsConfig
{
    string GoogleSheetsClientId { get; }

    string GoogleSheetsClientSecret { get; }

    string GoogleSheetsOauthCallbackUrl { get; }

    string GoogleSheetsOauthStateEncryptionKey { get; }

    string GoogleSheetsReadQuotaPerMinute { get; }

    string GoogleSheetsWriteQuotaPerMinute { get; }

    string GoogleSheetsReadQuotaPerMinutePerUser { get; }

    string GoogleSheetsWriteQuotaPerMinutePerUser { get; }
}

public class GoogleSheetsConfig : IConfig, IGoogleSheetsConfig
{
    public string GoogleSheetsClientId { get; private set; }

    public string GoogleSheetsClientSecret { get; private set; }

    public string GoogleSheetsOauthCallbackUrl { get; private set; }

    public string GoogleSheetsOauthStateEncryptionKey { get; private set; }

    public string GoogleSheetsReadQuotaPerMinute { get; private set; }

    public string GoogleSheetsWriteQuotaPerMinute { get; private set; }

    public string GoogleSheetsReadQuotaPerMinutePerUser { get; private set; }

    public string GoogleSheetsWriteQuotaPerMinutePerUser { get; private set; }

    public GoogleSheetsConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        GoogleSheetsClientId =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_CLIENT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_CLIENT_ID");
        GoogleSheetsClientSecret =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_CLIENT_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_CLIENT_SECRET");
        GoogleSheetsOauthCallbackUrl =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_OAUTH_CALLBACK_URL", target)
            ?? configuration["GOOGLE_SHEETS_OAUTH_CALLBACK_URL"]!;
        GoogleSheetsOauthStateEncryptionKey =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_OAUTH_STATE_ENCRYPTION_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_OAUTH_STATE_ENCRYPTION_KEY");
        GoogleSheetsReadQuotaPerMinute =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_READ_QUOTA_PER_MINUTE", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_READ_QUOTA_PER_MINUTE");
        GoogleSheetsWriteQuotaPerMinute =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_WRITE_QUOTA_PER_MINUTE", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_WRITE_QUOTA_PER_MINUTE");
        GoogleSheetsReadQuotaPerMinutePerUser =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_READ_QUOTA_PER_MINUTE_PER_USER", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_READ_QUOTA_PER_MINUTE_PER_USER");
        GoogleSheetsWriteQuotaPerMinutePerUser =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_WRITE_QUOTA_PER_MINUTE_PER_USER", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_WRITE_QUOTA_PER_MINUTE_PER_USER");
    }
}