﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetVersionedWorkflow : ITrigger<
    GetVersionedWorkflow.GetVersionedWorkflowInput,
    GetVersionedWorkflow.GetVersionedWorkflowOutput>
{
    private readonly IWorkflowService _workflowService;

    public GetVersionedWorkflow(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class GetVersionedWorkflowInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [JsonConstructor]
        public GetVersionedWorkflowInput(string sleekflowCompanyId, string workflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowVersionedId = workflowVersionedId;
        }
    }

    public class GetVersionedWorkflowOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto? Workflow { get; set; }

        [JsonConstructor]
        public GetVersionedWorkflowOutput(WorkflowDto? workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<GetVersionedWorkflowOutput> F(GetVersionedWorkflowInput input)
    {
        var versionedWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId);

        return new GetVersionedWorkflowOutput(
            versionedWorkflow is null
                ? null
                : new WorkflowDto(versionedWorkflow));
    }
}