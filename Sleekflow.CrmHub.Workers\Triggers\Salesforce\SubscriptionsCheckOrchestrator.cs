﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Workers.Triggers.Salesforce;

public class SubscriptionsCheckOrchestrator
{
    public class SubscriptionsCheckOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTimeOffset LastUpdateTime { get; set; }

        [JsonProperty("last_object_modification_time")]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorCustomStatusOutput(
            long count,
            DateTimeOffset lastUpdateTime,
            DateTimeOffset? lastObjectModificationTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public class SubscriptionsCheckOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("Salesforce_SubscriptionsCheck_Orchestrator")]
    public async Task<SubscriptionsCheckOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var startTime = context.CurrentUtcDateTime;
        var subscriptionsCheckInput = context.GetInput<SubscriptionsCheck.SubscriptionsCheckInput>();
        var salesforceSubscription = subscriptionsCheckInput.Subscription;

        context.SetCustomStatus(new SubscriptionsCheckOrchestratorCustomStatusOutput(0, startTime, null));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        var totalCount = 0L;

        // Salesforce uses `>=`
        // The first lastObjectModificationTime should use `+1` to get newer objects
        var lastObjectModificationTime =
            salesforceSubscription.LastObjectModificationTime?.AddSeconds(1)
            ?? salesforceSubscription.LastExecutionStartTime;
        var nextLastObjectModificationTime =
            salesforceSubscription.LastObjectModificationTime
            ?? salesforceSubscription.LastExecutionStartTime;
        var nextRecordsUrl = null as string;
        while (true)
        {
            var subscriptionsCheckBatchOutput =
                await context.CallActivityAsync<SubscriptionsCheckBatch.SubscriptionsCheckBatchOutput>(
                    "Salesforce_SubscriptionsCheck_Batch",
                    new SubscriptionsCheckBatch.SubscriptionsCheckBatchInput(
                        subscriptionsCheckInput.SleekflowCompanyId,
                        salesforceSubscription,
                        subscriptionsCheckInput.EntityTypeName,
                        subscriptionsCheckInput.FilterGroups,
                        subscriptionsCheckInput.FieldFilters,
                        lastObjectModificationTime,
                        nextRecordsUrl),
                    taskOptions);

            totalCount += subscriptionsCheckBatchOutput.Count;
            nextLastObjectModificationTime =
                subscriptionsCheckBatchOutput.NextLastObjectModificationTime > nextLastObjectModificationTime
                    ? subscriptionsCheckBatchOutput.NextLastObjectModificationTime
                    : nextLastObjectModificationTime;
            nextRecordsUrl = subscriptionsCheckBatchOutput.NextRecordsUrl;

            context.SetCustomStatus(
                new SubscriptionsCheckOrchestratorCustomStatusOutput(
                    totalCount,
                    context.CurrentUtcDateTime,
                    nextLastObjectModificationTime));

            if (nextRecordsUrl == null)
            {
                break;
            }

            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
                CancellationToken.None);
        }

        await context.CallActivityAsync(
            "Salesforce_SubscriptionsCheck_End",
            new SubscriptionsCheckEnd.SubscriptionsCheckEndInput(
                salesforceSubscription,
                nextLastObjectModificationTime,
                startTime));

        return new SubscriptionsCheckOrchestratorOutput(totalCount);
    }
}