using System.Diagnostics.Metrics;
using Microsoft.Extensions.Logging;
using Sleekflow.OpenTelemetry.FlowHub.MeterNames;
using Sleekflow.OpenTelemetry.MessagingHub.MeterNames;
using Sleekflow.OpenTelemetry.Meters;

namespace Sleekflow.OpenTelemetry.FlowHub;

public interface IFlowHubMeters : IBasicMeters
{
}

public static class FlowHubMeterOptions
{
    public const string WorkflowStart = "workflow_start";
    public const string WorkflowComplete = "workflow_complete";
    public const string WorkflowFailed = "workflow_failed";
}

public class FlowHubMeters : BaseMeters, IFlowHubMeters
{
    private const string FlowHubMeterName = "flow_hub_meters.trigger_type";
    private readonly Dictionary<string, Counter<int>>? _workflowTypeCounters;

    private readonly List<string> _flowTypeOptionsParams = new ()
    {
        FlowHubMeterOptions.WorkflowStart,
        FlowHubMeterOptions.WorkflowComplete,
        FlowHubMeterOptions.WorkflowFailed,
    };

    public FlowHubMeters(
        IMeterFactory meterFactory,
        ILogger<FlowHubMeters> logger)
        : base(meterFactory, logger)
    {
        if (!IsMeterEnabled)
        {
            return;
        }

        _workflowTypeCounters = _flowTypeOptionsParams.SelectMany(
                o => _webhookTypes.Select(
                    c => (Key: GetComposeKey(c.Key, o), Value: GetComposeKey(c.Value, o))))
            .ToDictionary(
                k => k.Key,
                k => CreateCounter<int>($"{FlowHubMeterName}.{k.Value}"));
    }

    protected override Counter<int> GetCounter<T>(T name, string? option = null)
    {
        if (name is string triggerType &&
            _workflowTypeCounters!.TryGetValue(GetComposeKey(triggerType, option), out var counter))
        {
            return counter;
        }

        throw new NotImplementedException();
    }

    private readonly Dictionary<string, string> _webhookTypes = new ()
    {
        {
            FlowHubFlowTypeMeterNames.Normal, FlowHubFlowTypeMeterNames.Normal
        },
        {
            FlowHubFlowTypeMeterNames.InternalForZapierIntegration, FlowHubFlowTypeMeterNames.InternalForZapierIntegration
        },
        {
            FlowHubFlowTypeMeterNames.AIAgent, FlowHubFlowTypeMeterNames.AIAgent
        },
        {
            FlowHubFlowTypeMeterNames.AIWorkflow, FlowHubFlowTypeMeterNames.AIWorkflow
        }
    };
}