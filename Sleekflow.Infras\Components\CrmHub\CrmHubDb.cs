using System.Collections.Immutable;
using Pulumi;
using Pulumi.AzureNative.DocumentDB.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.CrmHub;

public class CrmHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public CrmHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class CrmHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public CrmHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public CrmHubDbOutput InitCrmHubDb()
    {
        const string cosmosDbId = "crmhubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 4000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.CrmDb.ICrmDbService
        var containerParams = new ContainerParam[]
        {
            new ContainerParam(
                "entity",
                "entity",
                new List<string>
                {
                    "/id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 40000 : 4000,
                CompositeIndexingPathArgsList: new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                {
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new DocumentDB.Inputs.CompositePathArgs()
                            {
                                Path = "/\"unified:FirstName\"/i", Order = DocumentDB.CompositePathSortOrder.Ascending
                            },
                            new DocumentDB.Inputs.CompositePathArgs()
                            {
                                Path = "/\"unified:LastName\"/i", Order = DocumentDB.CompositePathSortOrder.Ascending
                            }
                        }
                        .ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new DocumentDB.Inputs.CompositePathArgs()
                            {
                                Path = "/\"unified:FirstName\"/i", Order = DocumentDB.CompositePathSortOrder.Descending
                            },
                            new DocumentDB.Inputs.CompositePathArgs()
                            {
                                Path = "/\"unified:LastName\"/i", Order = DocumentDB.CompositePathSortOrder.Descending
                            }
                        }
                        .ToImmutableArray(),
                    new DocumentDB.Inputs.CompositePathArgs[]
                        {
                            new DocumentDB.Inputs.CompositePathArgs()
                            {
                                Path = "/\"sys_sleekflow_company_id\"",
                                Order = DocumentDB.CompositePathSortOrder.Ascending
                            },
                            new DocumentDB.Inputs.CompositePathArgs()
                            {
                                Path = "/\"sys_entity_type_name\"", Order = DocumentDB.CompositePathSortOrder.Ascending
                            },
                            new DocumentDB.Inputs.CompositePathArgs()
                            {
                                Path = "/\"sys_type_name\"", Order = DocumentDB.CompositePathSortOrder.Ascending
                            }
                        }
                        .ToImmutableArray(),
                }),
            new ContainerParam(
                "entity_event",
                "entity_event",
                new List<string>
                {
                    "/sys_sleekflow_company_id", "/sys_partition_id"
                },
                Ttl: -1,
                ExcludedIndexingPathsList: new List<DocumentDB.Inputs.ExcludedPathArgs>()
                {
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/_etag/?"
                    },
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/change_entries/*"
                    }
                },
                MaxThroughput: _myConfig.Name == "production" ? 3000 : 1000),
            new ContainerParam(
                "metadata",
                "metadata",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000),

            new ContainerParam(
                "sys_unify",
                "sys_unify",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "sys_state_sync_objects_progress",
                "sys_state_sync_objects_progress",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                Ttl: 3600 * 24 * 365,
                MaxThroughput: 1000),
            new ContainerParam(
                "sys_config",
                "sys_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: 1000),

            new ContainerParam(
                "sys_changefeed_lease",
                "sys_changefeed_lease",
                new List<string>
                {
                    "/id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 2000 : 1000),
            new ContainerParam(
                "schema",
                "schema",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 10_000 : 1000),
            _myConfig.Name == "dev"
                ? new ContainerParam(
                    "schemaful_object",
                    "schemaful_object",
                    new List<string>
                    {
                        "/sleekflow_company_id", "/schema_id", "/id"
                    },
                    ExcludedIndexingPathsList: new List<DocumentDB.Inputs.ExcludedPathArgs>()
                    {
                        new DocumentDB.Inputs.ExcludedPathArgs()
                        {
                            Path = "/property_values/*"
                        }
                    })
                : new ContainerParam(
                    "schemaful_object",
                    "schemaful_object",
                    new List<string>
                    {
                        "/sleekflow_company_id", "/schema_id", "/id"
                    },
                    ExcludedIndexingPathsList: new List<DocumentDB.Inputs.ExcludedPathArgs>()
                    {
                        new DocumentDB.Inputs.ExcludedPathArgs()
                        {
                            Path = "/property_values/*"
                        }
                    },
                    MaxThroughput: _myConfig.Name == "production" ? 10_000 : 1000),
            new ContainerParam(
                "sequence",
                "sequence",
                new List<string>
                {
                    "/id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "crm_hub_config",
                "crm_hub_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/sleekflow_company_id"
                    },
                },
                MaxThroughput: _myConfig.Name == "production" ? 2000 : 1000),
            new ContainerParam(
                "sys_state_loop_through_objects_progress",
                "sys_state_loop_through_objects_progress",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                Ttl: 3600 * 24 * 365,
                MaxThroughput: 1000),
            new ContainerParam(
                "integration_object",
                "integration_object",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                MaxThroughput: _myConfig.Name == "production" ? 40000 : 4000),
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

#pragma warning disable S1848
        var _ = new DocumentDB.SqlResourceSqlStoredProcedure(
            $"{cosmosDbId}-metadata-upsert-metadata-stored-procedure",
            new DocumentDB.SqlResourceSqlStoredProcedureArgs()
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                DatabaseName = cosmosDb.Name,
                ContainerName = "metadata",
                Resource = new DocumentDB.Inputs.SqlStoredProcedureResourceArgs
                {
                    Body = string.Join("\n", File.ReadAllLines("./Resources/upsert-metadata-stored-procedure.js")),
                    Id = "upsert-metadata",
                },
                StoredProcedureName = "upsert-metadata",
            },
            new CustomResourceOptions
            {
                Parent = containerIdToContainer["metadata"]
            });
#pragma warning restore S1848

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new CrmHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}