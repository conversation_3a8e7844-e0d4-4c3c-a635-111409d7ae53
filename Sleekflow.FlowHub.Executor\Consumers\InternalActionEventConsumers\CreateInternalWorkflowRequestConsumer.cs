﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events.InternalActionEvents;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;

namespace Sleekflow.FlowHub.Executor.Consumers.InternalActionEventConsumers;

public class CreateInternalWorkflowRequestConsumerDefinition
    : ConsumerDefinition<CreateInternalWorkflowRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<CreateInternalWorkflowRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class CreateInternalWorkflowRequestConsumer : IConsumer<CreateInternalWorkflowRequest>
{
    private readonly IWorkflowService _workflowService;
    private readonly IIdService _idService;
    private readonly IWorkflowStepValidator _workflowStepValidator;

    public CreateInternalWorkflowRequestConsumer(
        IWorkflowService workflowService,
        IIdService idService,
        IWorkflowStepValidator workflowStepValidator)
    {
        _workflowService = workflowService;
        _idService = idService;
        _workflowStepValidator = workflowStepValidator;
    }

    public async Task Consume(ConsumeContext<CreateInternalWorkflowRequest> context)
    {
        var createInternalWorkflowRequest = context.Message;

        var workflowEnrollmentSettings = WorkflowEnrollmentSettings.Default();
        var workflowScheduleSettings = WorkflowScheduleSettings.Default();

        await _workflowStepValidator.AssertAllStepsAreValidAsync(
            createInternalWorkflowRequest.Steps,
            workflowScheduleSettings.ScheduleType,
            workflowScheduleSettings.IsNewScheduledWorkflowSchema);

        var workflowId = _idService.GetId("Workflow");
        var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflowId);

        var workflow = await _workflowService.CreateWorkflowAsync(
            new Workflow(
                workflowId,
                workflowVersionedId,
                createInternalWorkflowRequest.Name,
                createInternalWorkflowRequest.WorkflowType,
                null,
                createInternalWorkflowRequest.Triggers,
                workflowEnrollmentSettings,
                workflowScheduleSettings,
                createInternalWorkflowRequest.Steps,
                WorkflowActivationStatuses.Draft,
                workflowVersionedId,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                createInternalWorkflowRequest.SleekflowCompanyId,
                null,
                null,
                createInternalWorkflowRequest.Metadata,
                "v1",
                null),
            createInternalWorkflowRequest.SleekflowCompanyId);

        await context.RespondAsync(new CreateInternalWorkflowReply(new WorkflowDto(workflow)));
    }
}