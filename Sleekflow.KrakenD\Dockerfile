FROM krakend/builder:2.5.0 AS builder

WORKDIR /app

COPY ./Sleekflow.KrakenD/Go/krakend-authentication-plugin/ .

RUN go build -buildmode=plugin -o krakend-authentication-plugin.so .

FROM devopsfaith/krakend:2.5.0

ENV FC_ENABLE=1
# Set the default file name to krakend.json
ARG FILE_NAME=krakend.json

EXPOSE 8080

COPY ./Sleekflow.KrakenD/Lua/ /etc/krakend/lua/
COPY --from=builder ["/app/krakend-authentication-plugin.so", "/etc/krakend/plugins/krakend-authentication-plugin.so"]

# Copy the krakend.json file to the container and config in docker-compose.yml to toggle between regional and global krakend config
COPY ${FILE_NAME} /etc/krakend/krakend.json