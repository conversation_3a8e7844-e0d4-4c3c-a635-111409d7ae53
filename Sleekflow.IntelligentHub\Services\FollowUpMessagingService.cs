using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Services;

public interface IFollowUpMessagingService
{
    Task SendFollowUpIfAppropriateAsync(
        Kernel kernel,
        string groupChatId,
        string followUpMessage,
        string companyId,
        string objectId,
        string? stateId = null,
        string? contactId = null,
        string? agentId = null,
        string? toolsConfigJson = null,
        string? leadNurturingToolsConfigJson = null,
        string? originalUserMessage = null);
}

public class FollowUpMessagingService : IFollowUpMessagingService, IScopedService
{
    private readonly ILogger<FollowUpMessagingService> _logger;
    private readonly IUserInteractionTrackingService _userInteractionTracker;
    private readonly ISleekflowToolsPlugin _sleekflowToolsPlugin;

    public FollowUpMessagingService(
        ILogger<FollowUpMessagingService> logger,
        IUserInteractionTrackingService userInteractionTracker,
        ISleekflowToolsPlugin sleekflowToolsPlugin)
    {
        _logger = logger;
        _userInteractionTracker = userInteractionTracker;
        _sleekflowToolsPlugin = sleekflowToolsPlugin;
    }

        public async Task SendFollowUpIfAppropriateAsync(
        Kernel kernel,
        string groupChatId,
        string followUpMessage,
        string companyId,
        string objectId,
        string? stateId = null,
        string? contactId = null,
        string? agentId = null,
        string? toolsConfigJson = null,
        string? leadNurturingToolsConfigJson = null,
        string? originalUserMessage = null)
    {
        _logger.LogInformation("Evaluating follow-up message for GroupChatId: {GroupChatId}, ObjectId: {ObjectId}",
            groupChatId, objectId);

        try
        {
            // Check if user has sent a new message since the original message
            var hasUserSentNewMessage = await _userInteractionTracker.HasUserSentNewMessageAsync(
                companyId, objectId, originalUserMessage ?? string.Empty);

            if (hasUserSentNewMessage)
            {
                _logger.LogInformation(
                    "User has sent a new message for ObjectId: {ObjectId}. Skipping follow-up message.", objectId);
                return;
            }

            // Configure kernel with proper context data for SleekflowToolsPlugin
            var kernelClone = kernel.Clone();

            // Set required kernel data
            kernelClone.Data[KernelDataKeys.GROUP_CHAT_ID] = groupChatId;
            kernelClone.Data[KernelDataKeys.SLEEKFLOW_COMPANY_ID] = companyId;

            if (!string.IsNullOrEmpty(stateId))
                kernelClone.Data[KernelDataKeys.STATE_ID] = stateId;

            if (!string.IsNullOrEmpty(contactId))
                kernelClone.Data[KernelDataKeys.CONTACT_ID] = contactId;

            if (!string.IsNullOrEmpty(agentId))
                kernelClone.Data[KernelDataKeys.AGENT_ID] = agentId;

            // Deserialize and set tools configuration if provided
            if (!string.IsNullOrEmpty(toolsConfigJson))
            {
                try
                {
                    var toolsConfig = JsonConvert.DeserializeObject<ToolsConfig>(toolsConfigJson);
                    if (toolsConfig != null)
                        kernelClone.Data[KernelDataKeys.TOOLS_CONFIG] = toolsConfig;
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to deserialize ToolsConfig, will attempt without it");
                }
            }

            if (!string.IsNullOrEmpty(leadNurturingToolsConfigJson))
            {
                try
                {
                    var leadNurturingTools = JsonConvert.DeserializeObject<LeadNurturingTools>(leadNurturingToolsConfigJson);
                    if (leadNurturingTools != null)
                        kernelClone.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] = leadNurturingTools;
                }
                catch (JsonException ex)
                {
                    _logger.LogWarning(ex, "Failed to deserialize LeadNurturingTools, will attempt without it");
                }
            }

            _logger.LogInformation(
                "Sending follow-up message for GroupChatId: {GroupChatId}, ObjectId: {ObjectId}, Message: {Message}",
                groupChatId, objectId, followUpMessage);

            // Send the follow-up message using the properly configured kernel
            var result = await _sleekflowToolsPlugin.SendMessage(kernelClone, followUpMessage);

            if (result.Contains("MessageSent") || result.Contains("EventPublished"))
            {
                _logger.LogInformation("Follow-up message sent successfully for GroupChatId: {GroupChatId}, Result: {Result}",
                    groupChatId, result);
            }
            else
            {
                _logger.LogWarning(
                    "Follow-up message may not have been sent successfully for GroupChatId: {GroupChatId}. Result: {Result}",
                    groupChatId, result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending follow-up message for GroupChatId: {GroupChatId}, ObjectId: {ObjectId}",
                groupChatId, objectId);
        }
    }
}