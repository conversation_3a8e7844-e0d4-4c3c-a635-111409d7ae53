﻿using MassTransit;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;

namespace Sleekflow.Events.ServiceBus;

public interface IServiceBusProvider
{
    IBus GetServiceBus(string serviceBusConfigType);
}

public class ServiceBusProvider
    : IServiceBusProvider
{
    private readonly IBus _bus;
    private readonly IHighTrafficServiceBus? _highTrafficServiceBus;

    public ServiceBusProvider(
        IBus bus,
        IHighTrafficServiceBus? highTrafficServiceBus = null)
    {
        _bus = bus;
        _highTrafficServiceBus = highTrafficServiceBus;
    }

    public virtual IBus GetServiceBus(string serviceBusConfigType)
    {
        return serviceBusConfigType switch
        {
            ServiceBusConfigTypes.HighTrafficServiceBus => _highTrafficServiceBus!,
            _ => _bus
        };
    }
}