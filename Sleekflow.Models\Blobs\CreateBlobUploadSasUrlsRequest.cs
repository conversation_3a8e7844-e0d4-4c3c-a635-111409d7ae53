using Newtonsoft.Json;

namespace Sleekflow.Models.Blobs;

public class CreateBlobUploadSasUrlsRequest
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("number_of_blobs")]
    public int NumberOfBlobs { get; set; }

    [JsonProperty("blob_type")]
    public string BlobType { get; set; }

    [JsonConstructor]
    public CreateBlobUploadSasUrlsRequest(
        string sleekflowCompanyId,
        int numberOfBlobs,
        string blobType)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        NumberOfBlobs = numberOfBlobs;
        BlobType = blobType;
    }
}