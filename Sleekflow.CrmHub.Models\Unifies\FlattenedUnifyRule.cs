using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Unifies;

public class FlattenedUnifyRule
{
    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("provider_field_name")]
    public string ProviderFieldName { get; set; }

    [JsonConstructor]
    public FlattenedUnifyRule(
        string fieldName,
        string providerFieldName)
    {
        FieldName = fieldName;
        ProviderFieldName = providerFieldName;
    }
}