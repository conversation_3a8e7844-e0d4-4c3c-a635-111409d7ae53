using GraphApi.Client.Models.MessageObjects.TemplateObjects;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Templates;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Template.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Templates)]
public class EditWhatsappCloudApiTemplate
    : ITrigger<
        EditWhatsappCloudApiTemplate.EditWhatsappCloudApiTemplateInput,
        EditWhatsappCloudApiTemplate.EditWhatsappCloudApiTemplateOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ITemplateService _templateService;
    private readonly ILogger<EditWhatsappCloudApiTemplate> _logger;

    public EditWhatsappCloudApiTemplate(
        IWabaService wabaService,
        ITemplateService templateService,
        ILogger<EditWhatsappCloudApiTemplate> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _templateService = templateService;
    }

    public class EditWhatsappCloudApiTemplateInput
    {
        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("template_id")]
        public string TemplateId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [Validations.ValidateArray]
        [JsonProperty("template_components")]
        public List<WhatsappCloudApiTemplateComponentObject> WhatsappCloudApiTemplateComponentObjects { get; set; }

        [JsonConstructor]
        public EditWhatsappCloudApiTemplateInput(
            string wabaId,
            string sleekflowCompanyId,
            string templateId,
            List<WhatsappCloudApiTemplateComponentObject> whatsappCloudApiTemplateComponentObjects)
        {
            WabaId = wabaId;
            SleekflowCompanyId = sleekflowCompanyId;
            TemplateId = templateId;
            WhatsappCloudApiTemplateComponentObjects = whatsappCloudApiTemplateComponentObjects;
        }
    }

    public class EditWhatsappCloudApiTemplateOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public EditWhatsappCloudApiTemplateOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<EditWhatsappCloudApiTemplateOutput> F(
        EditWhatsappCloudApiTemplateInput editWhatsappCloudApiTemplateInput)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(
            editWhatsappCloudApiTemplateInput.WabaId,
            editWhatsappCloudApiTemplateInput.SleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        _logger.LogInformation(
            "EditCloudApiWhatsappTemplateAsync with editWhatsappCloudApiTemplateInput.TemplateId {TemplateId} and editWhatsappCloudApiTemplateInput.WhatsappCloudApiTemplateComponentObjects {WhatsappCloudApiTemplateComponentObjects}",
            editWhatsappCloudApiTemplateInput.TemplateId,
            JsonConvert.SerializeObject(editWhatsappCloudApiTemplateInput.WhatsappCloudApiTemplateComponentObjects));

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        return new EditWhatsappCloudApiTemplateOutput(
            await _templateService.EditCloudApiWhatsappTemplateAsync(
                editWhatsappCloudApiTemplateInput.TemplateId,
                editWhatsappCloudApiTemplateInput.WhatsappCloudApiTemplateComponentObjects,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null));
    }
}