﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderObjectDirectRefUrl : ITrigger
{
    private readonly IEntityContextService _entityContextService;
    private readonly IEntityService _entityService;
    private readonly IProviderSelector _providerSelector;

    public GetProviderObjectDirectRefUrl(
        IEntityContextService entityContextService,
        IEntityService entityService,
        IProviderSelector providerSelector)
    {
        _entityContextService = entityContextService;
        _entityService = entityService;
        _providerSelector = providerSelector;
    }

    public class GetProviderObjectDirectRefUrlInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonConstructor]
        public GetProviderObjectDirectRefUrlInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string objectId,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
            ProviderName = providerName;
        }
    }

    public class GetProviderObjectDirectRefUrlOutput
    {
        [JsonProperty("object_direct_ref_url")]
        [Required]
        public string ObjectDirectRefUrl { get; set; }

        [JsonConstructor]
        public GetProviderObjectDirectRefUrlOutput(
            string objectDirectRefUrl)
        {
            ObjectDirectRefUrl = objectDirectRefUrl;
        }
    }

    public async Task<GetProviderObjectDirectRefUrlOutput> F(
        GetProviderObjectDirectRefUrlInput getProviderObjectDirectRefUrlInput)
    {
        var sleekflowCompanyId = getProviderObjectDirectRefUrlInput.SleekflowCompanyId;
        var entityTypeName = getProviderObjectDirectRefUrlInput.EntityTypeName;

        var providerService = _providerSelector.GetProviderService(getProviderObjectDirectRefUrlInput.ProviderName);

        var entityContext = await _entityContextService.GetEntityContextAsync(
            getProviderObjectDirectRefUrlInput.ObjectId,
            sleekflowCompanyId,
            entityTypeName);
        if (entityContext == null)
        {
            throw new SfUserFriendlyException("The object does not exist");
        }

        var entity = await _entityService.GetAsync(entityContext.Id, sleekflowCompanyId, entityTypeName);

        var providerObjectId = await providerService.ResolveObjectIdAsync(entity, entityTypeName);
        if (providerObjectId == null)
        {
            throw new SfUserFriendlyException("The object does not exist");
        }

        var getObjectDirectRefUrlOutput = await providerService.GetObjectDirectRefUrl(
            sleekflowCompanyId,
            providerObjectId,
            entityTypeName);

        return new GetProviderObjectDirectRefUrlOutput(getObjectDirectRefUrlOutput.ObjectDirectRefUrl);
    }
}