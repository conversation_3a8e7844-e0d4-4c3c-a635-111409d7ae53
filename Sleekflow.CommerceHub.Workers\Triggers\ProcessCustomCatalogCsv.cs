using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.CommerceHub.Workers.Utils;

namespace Sleekflow.CommerceHub.Workers.Triggers;

public class ProcessCustomCatalogCsv
{
    private readonly ILogger<ProcessCustomCatalogCsv> _logger;

    public ProcessCustomCatalogCsv(
        ILogger<ProcessCustomCatalogCsv> logger)
    {
        _logger = logger;
    }

    [Function("ProcessCustomCatalogCsv")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<ProcessCustomCatalogCsvInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (ProcessCustomCatalogCsvInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (processCustomCatalogCsvInput, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            "ProcessCustomCatalogCsv_Orchestrator",
            processCustomCatalogCsvInput);

        logger.LogInformation($"Started ProcessCustomCatalogCsv_Orchestrator with ID = [{instanceId}]");

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get ProcessCustomCatalogCsv_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}