﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class GoogleSheetsRowUpdatedEventRequestConsumerDefinition : ConsumerDefinition<GoogleSheetsRowUpdatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GoogleSheetsRowUpdatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GoogleSheetsRowUpdatedEventRequestConsumer : IConsumer<GoogleSheetsRowUpdatedEventRequest>
{
    private readonly IBus _bus;

    public GoogleSheetsRowUpdatedEventRequestConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<GoogleSheetsRowUpdatedEventRequest> context)
    {
        var googleSheetsRowUpdatedEventRequest = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnGoogleSheetsRowUpdatedEventBody(
                googleSheetsRowUpdatedEventRequest.CreatedAt,
                googleSheetsRowUpdatedEventRequest.ConnectionId,
                googleSheetsRowUpdatedEventRequest.SpreadsheetId,
                googleSheetsRowUpdatedEventRequest.WorksheetId,
                googleSheetsRowUpdatedEventRequest.RowId,
                googleSheetsRowUpdatedEventRequest.Row),
            googleSheetsRowUpdatedEventRequest.RowId,
            "Row",
            googleSheetsRowUpdatedEventRequest.SleekflowCompanyId));
    }
}