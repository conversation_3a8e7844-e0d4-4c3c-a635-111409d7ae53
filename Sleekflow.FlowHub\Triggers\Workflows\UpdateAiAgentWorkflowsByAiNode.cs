using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class UpdateAiAgentWorkflowsByAiNode : ITrigger
{
    private readonly IAiAgentWorkflowService _aiAgentWorkflowService;
    private readonly IWorkflowService _workflowService;
    private readonly string _aiAgentStepCall = EnterAiAgentStepArgs.CallName;

    public UpdateAiAgentWorkflowsByAiNode(
        IAiAgentWorkflowService aiAgentWorkflowService,
        IWorkflowService workflowService)
    {
        _aiAgentWorkflowService = aiAgentWorkflowService;
        _workflowService = workflowService;
    }

    public class UpdateAiAgentWorkflowsByAiNodeInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("labels")]
        [Required]
        [Validations.ValidateArray]

        public List<Dictionary<string, object>> Labels { get; set; }

        [JsonProperty("schemaful_object")]
        [Required]
        [Validations.ValidateObject]

        public Dictionary<string, object> SchemafulObject { get; set; }

        [JsonProperty("contact_property")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object> ContactProperty { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateAiAgentWorkflowsByAiNodeInput(
            string sleekflowCompanyId,
            List<Dictionary<string, object>> labels,
            Dictionary<string, object> schemafulObject,
            Dictionary<string, object> contactProperty,
            string sleekflowStaffId,
            string workflowId,
            List<string>? sleekflowStaffTeamIds = null
            )
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Labels = labels;
            SchemafulObject = schemafulObject;
            ContactProperty = contactProperty;
            SleekflowStaffId = sleekflowStaffId;
            WorkflowId = workflowId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateAiAgentWorkflowsByAiNodeOutput
    {
        [JsonProperty("add_workflow_ids")]
        public List<string> AddWorkflowIds { get; set; }

        [JsonProperty("remove_workflow_ids")]
        public List<string> RemoveWorkflowIds { get; set; }

        [JsonConstructor]
        public UpdateAiAgentWorkflowsByAiNodeOutput(
            List<string> addWorkflowIds,
            List<string> removeWorkflowIds)
        {
            AddWorkflowIds = addWorkflowIds;
            RemoveWorkflowIds = removeWorkflowIds;
        }
    }

    public async Task<UpdateAiAgentWorkflowsByAiNodeOutput> F(UpdateAiAgentWorkflowsByAiNodeInput updateAiAgentWorkflowsByAiNodeInput)
    {
        var workflowId = updateAiAgentWorkflowsByAiNodeInput.WorkflowId;
        var sleekflowCompanyId = updateAiAgentWorkflowsByAiNodeInput.SleekflowCompanyId;
        var labels = updateAiAgentWorkflowsByAiNodeInput.Labels;
        var schemafulObject = updateAiAgentWorkflowsByAiNodeInput.SchemafulObject;
        var contactProperty = updateAiAgentWorkflowsByAiNodeInput.ContactProperty;

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
                    updateAiAgentWorkflowsByAiNodeInput.SleekflowStaffId,
                    updateAiAgentWorkflowsByAiNodeInput.SleekflowStaffTeamIds);
        var (addWorkflowIds, removeWorkflowIds, updatedWorkflowIds) = await _aiAgentWorkflowService.UpdateAiAgentWorkflowsByAiNode(
            sleekflowCompanyId,
            workflowId,
            labels,
            schemafulObject,
            contactProperty,
            sleekflowStaff);

        return new UpdateAiAgentWorkflowsByAiNodeOutput(addWorkflowIds, removeWorkflowIds);
    }
}
