namespace Sleekflow.FlowHub.Models.Constants;

public static class SysTypeNames
{
    public const string FlowHubEvent = "FlowHubEvent";
    public const string FlowHubConfig = "FlowHubConfig";
    public const string State = "State";
    public const string StateSubscription = "StateSubsciption";
    public const string Workflow = "Workflow";
    public const string WorkflowGroup = "WorkflowGroup";
    public const string WorkflowExecution = "WorkflowExecution";
    public const string StepExecution = "StepExecution";
    public const string WorkflowWebhookTrigger = "WorkflowWebhookTrigger";
    public const string ZapierTriggerIntegration = "ZapierTriggerIntegration";
    public const string WorkflowStepCategoryStatistics = "WorkflowStepCategoryStatistics";
    public const string StateStepCategoryStatistics = "StateStepCategoryStatistics";
    public const string EmailSentRecord = "EmailSentRecord";

    public const string UserEventHubUserEvent = "UserEvent";
    public const string WorkflowAgentConfigMapping = "WorkflowAgentConfigMapping";
}