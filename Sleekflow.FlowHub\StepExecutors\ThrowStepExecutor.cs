using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors;

public interface IThrowStepExecutor : IStepExecutor
{
}

public class ThrowStepExecutor : IThrowStepExecutor, IScopedService
{
    public bool IsMatched(Step step)
    {
        return step is ThrowStep;
    }

    public Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var throwStep = (ThrowStep) step;

        throw new Exception(throwStep.ErrorMessage);
    }

    public Task<Step?> OnStepCompleteAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries)
    {
        throw new NotImplementedException();
    }
}