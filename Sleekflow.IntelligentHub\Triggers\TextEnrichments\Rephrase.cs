﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.TextEnrichments;

[TriggerGroup(ControllerNames.TextEnrichments)]
public class Rephrase : ITrigger<Rephrase.RephraseInput, Rephrase.RephraseOutput>
{
    private readonly ITextRephraseService _textRephraseService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public Rephrase(
        ITextRephraseService textRephraseService,
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService)
    {
        _textRephraseService = textRephraseService;
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    public class RephraseInput : IValidatableObject, IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("message")]
        public string Message { get; set; }

        [Required]
        [JsonProperty("rephrase_target_type")]
        public string RephraseTargetType { get; set; }

        [JsonProperty(IntelligentHubUsageFilter.ModelNameIntelligentHubUsageFilter)]
        [Validations.ValidateObject]
        public IntelligentHubUsageFilter? IntelligentHubUsageFilter { get; set; }

        [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
        [Validations.ValidateObject]
        public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public RephraseInput(
            string message,
            string rephraseTargetType,
            string sleekflowCompanyId,
            IntelligentHubUsageFilter? intelligentHubUsageFilter,
            AuditEntity.SleekflowStaff? createdBy)
        {
            Message = message;
            RephraseTargetType = rephraseTargetType;
            SleekflowCompanyId = sleekflowCompanyId;
            IntelligentHubUsageFilter = intelligentHubUsageFilter;
            CreatedBy = createdBy;
        }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var validationResults = new List<ValidationResult>();

            if (!TargetRephraseTypes.AllRephraseTargetTypes.Contains(RephraseTargetType))
            {
                validationResults.Add(
                    new ValidationResult(
                        "The Rephrase Target Type is not supported.",
                        new List<string>
                        {
                            nameof(RephraseTargetType)
                        }));
            }

            return validationResults;
        }
    }

    public class RephraseOutput
    {
        [JsonProperty("output_message")]
        public string? OutputMessage { get; set; }

        [JsonConstructor]
        public RephraseOutput(string outputMessage)
        {
            OutputMessage = outputMessage;
        }
    }

    public async Task<RephraseOutput> F(RephraseInput rephraseInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(rephraseInput.SleekflowCompanyId);

        var isUsageLimitExceeded = intelligentHubConfig == null || await _intelligentHubUsageService.IsUsageLimitExceeded(
            rephraseInput.SleekflowCompanyId,
            new Dictionary<string, int>
            {
                {
                    PriceableFeatures.AiFeaturesTotalUsage,
                    _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                        intelligentHubConfig,
                        PriceableFeatures.AiFeaturesTotalUsage)
                }
            },
            rephraseInput.IntelligentHubUsageFilter);

        if (isUsageLimitExceeded)
        {
            throw new SfUserFriendlyException("Cannot find IntelligentHubConfig or exceed max usage limit.");
        }

        var outputMessage = await _textRephraseService.RephraseAsync(
            rephraseInput.Message,
            rephraseInput.RephraseTargetType);

        await _intelligentHubUsageService.RecordUsageAsync(
            rephraseInput.SleekflowCompanyId,
            PriceableFeatures.Rephrase,
            rephraseInput.CreatedBy,
            new RephraseSnapshot(
                rephraseInput.Message,
                rephraseInput.RephraseTargetType,
                outputMessage));

        return new RephraseOutput(outputMessage);
    }
}