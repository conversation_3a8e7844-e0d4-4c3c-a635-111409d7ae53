using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Events;

namespace Sleekflow.IntelligentHub.Consumers;

public class AgentDeletedEventConsumerDefinition : ConsumerDefinition<AgentDeletedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<AgentDeletedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class AgentDeletedEventConsumer : IConsumer<AgentDeletedEvent>
{
    private readonly IKbDocumentService _kbDocumentService;
    private readonly ILogger<AgentDeletedEventConsumer> _logger;

    public AgentDeletedEventConsumer(
        IKbDocumentService kbDocumentService,
        ILogger<AgentDeletedEventConsumer> logger)
    {
        _kbDocumentService = kbDocumentService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<AgentDeletedEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var agentId = message.AgentId;

        _logger.LogInformation(
            "Processing agent deletion event for Agent {AgentId} in Company {CompanyId}",
            agentId,
            sleekflowCompanyId);

        try
        {
            // unassign the agent from all documents
            await _kbDocumentService.RemoveAgentAssignmentFromAllDocuments(
                sleekflowCompanyId,
                agentId);

            _logger.LogInformation(
                "Successfully unassigned Agent {AgentId} from all documents in Company {CompanyId}",
                agentId,
                sleekflowCompanyId);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to unassign Agent {AgentId} from documents in Company {CompanyId}",
                agentId,
                sleekflowCompanyId);
            throw; // Rethrowing to allow MassTransit to handle the exception according to its retry policy
        }
    }
}