using System.ComponentModel.DataAnnotations;
using System.Text;
using Newtonsoft.Json;
using Polly;
using Scriban;
using Scriban.Functions;
using Scriban.Runtime;
using Serilog;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Utils;
using Sleekflow.Mvc.Https;
using Sleekflow.Persistence.Abstractions;
using HttpPolicies = Sleekflow.FlowHub.Https.HttpPolicies;
using ILogger = Serilog.ILogger;

namespace Sleekflow.FlowHub.States.Functions;

public class SleekflowFunctions : ArrayFunctions
{
    private static readonly ILogger Logger = Log.Logger.ForContext<SleekflowFunctions>();
    private static readonly IAppConfig AppConfig = new AppConfig();

    private static readonly HttpClient HttpClient = new HttpClient(
        new PrivateNetworkHttpClientHandler
        {
            AllowAutoRedirect = false,
        });

    [SwaggerInclude]
    public class GetContactInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_contact_id")]
        [Required]
        public string SleekflowContactId { get; set; }

        [JsonConstructor]
        public GetContactInput(string sleekflowCompanyId, string sleekflowContactId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowContactId = sleekflowContactId;
        }
    }

    [SwaggerInclude]
    public class GetContactOutput
    {
        [JsonProperty("contact")]
        public Dictionary<string, object?>? Contact { get; set; }

        [JsonConstructor]
        public GetContactOutput(Dictionary<string, object?>? contact)
        {
            Contact = contact;
        }
    }

    [SwaggerInclude]
    public class GetStaffInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        [Required]
        public string SleekflowStaffId { get; set; }

        [JsonConstructor]
        public GetStaffInput(string sleekflowCompanyId, string sleekflowStaffId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
        }
    }

    [SwaggerInclude]
    public class GetStaffOutput
    {
        [JsonProperty("staff")]
        public Dictionary<string, object?>? Staff { get; set; }

        [JsonConstructor]
        public GetStaffOutput(Dictionary<string, object?>? staff)
        {
            Staff = staff;
        }
    }

    [SwaggerInclude]
    public class GetContactIdByPhoneNumberInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("phone_number")]
        [Required]
        public string PhoneNumber { get; set; }

        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty("workflow_name")]
        public string WorkflowName { get; set; }

        [Required]
        [JsonProperty("state_id")]
        public string StateId { get; set; }

        [JsonConstructor]
        public GetContactIdByPhoneNumberInput(
            string sleekflowCompanyId,
            string phoneNumber,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string stateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PhoneNumber = phoneNumber;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
            WorkflowName = workflowName;
            StateId = stateId;
        }
    }

    [SwaggerInclude]
    public class GetOrCreateContactIdByPhoneNumberInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("phone_number")]
        [Required]
        public string PhoneNumber { get; set; }

        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty("workflow_name")]
        public string WorkflowName { get; set; }

        [Required]
        [JsonProperty("state_id")]
        public string StateId { get; set; }

        [JsonConstructor]
        public GetOrCreateContactIdByPhoneNumberInput(
            string sleekflowCompanyId,
            string phoneNumber,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string stateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PhoneNumber = phoneNumber;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
            WorkflowName = workflowName;
            StateId = stateId;
        }
    }

    [SwaggerInclude]
    public class GetContactIdByEmailInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email")]
        [Required]
        public string Email { get; set; }

        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty("workflow_name")]
        public string WorkflowName { get; set; }

        [Required]
        [JsonProperty("state_id")]
        public string StateId { get; set; }

        [JsonConstructor]
        public GetContactIdByEmailInput(
            string sleekflowCompanyId,
            string email,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string stateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Email = email;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
            WorkflowName = workflowName;
            StateId = stateId;
        }
    }

    [SwaggerInclude]
    public class GetOrCreateContactIdByEmailInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email")]
        [Required]
        public string Email { get; set; }

        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty("workflow_name")]
        public string WorkflowName { get; set; }

        [Required]
        [JsonProperty("state_id")]
        public string StateId { get; set; }

        [JsonConstructor]
        public GetOrCreateContactIdByEmailInput(
            string sleekflowCompanyId,
            string email,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string stateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Email = email;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
            WorkflowName = workflowName;
            StateId = stateId;
        }
    }

    [SwaggerInclude]
    public class GetContactIdOutput
    {
        [JsonProperty("contact_id")]
        public string ContactId { get; set; }

        [JsonConstructor]
        public GetContactIdOutput(string contactId)
        {
            ContactId = contactId;
        }
    }

    [SwaggerInclude]
    public class GetOrCreateContactIdOutput
    {
        [JsonProperty("contact_id")]
        public string ContactId { get; set; }

        [JsonConstructor]
        public GetOrCreateContactIdOutput(string contactId)
        {
            ContactId = contactId;
        }
    }

    public static async Task<Dictionary<string, object?>?> GetContact(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;

        if (obj is string id)
        {
            var getContactOutput = await InvokeAsync<GetContactInput, GetContactOutput>(
                origin,
                "GetContact",
                new GetContactInput(sleekflowCompanyId, id));

            return getContactOutput.Contact;
        }

        return null;
    }

    public static async Task<string?> GetContactIdByPhoneNumber(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var workflowName = (string) ((ScriptObject) context.CurrentGlobal)["workflow_name"];
        var stateId = (string) ((ScriptObject) context.CurrentGlobal)["state_id"];

        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;
        var workflowId = evaluationContext.WorkflowId!;
        var workflowVersionedId = evaluationContext.WorkflowVersionedId!;

        if (obj is not string phoneNumber)
        {
            return null;
        }

        var getContactIdOutput = await InvokeAsync<GetContactIdByPhoneNumberInput, GetContactIdOutput>(
            origin,
            "GetContactIdByPhoneNumber",
            new GetContactIdByPhoneNumberInput(
                sleekflowCompanyId,
                phoneNumber,
                workflowId,
                workflowVersionedId,
                workflowName,
                stateId));

        return getContactIdOutput.ContactId;
    }

    public static async Task<string?> GetOrCreateContactIdByPhoneNumber(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var workflowName = (string) ((ScriptObject) context.CurrentGlobal)["workflow_name"];
        var stateId = (string) ((ScriptObject) context.CurrentGlobal)["state_id"];

        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;
        var workflowId = evaluationContext.WorkflowId!;
        var workflowVersionedId = evaluationContext.WorkflowVersionedId!;

        if (obj is not string phoneNumber)
        {
            return null;
        }

        var getOrCreateContactIdOutput = await InvokeAsync<GetOrCreateContactIdByPhoneNumberInput, GetOrCreateContactIdOutput>(
            origin,
            "GetOrCreateContactIdByPhoneNumber",
            new GetOrCreateContactIdByPhoneNumberInput(
                sleekflowCompanyId,
                phoneNumber,
                workflowId,
                workflowVersionedId,
                workflowName,
                stateId));

        return getOrCreateContactIdOutput.ContactId;
    }

    public static async Task<string?> GetContactIdByEmail(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var workflowName = (string) ((ScriptObject) context.CurrentGlobal)["workflow_name"];
        var stateId = (string) ((ScriptObject) context.CurrentGlobal)["state_id"];

        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;
        var workflowId = evaluationContext.WorkflowId!;
        var workflowVersionedId = evaluationContext.WorkflowVersionedId!;

        if (obj is not string email)
        {
            return null;
        }

        var getContactIdOutput = await InvokeAsync<GetContactIdByEmailInput, GetContactIdOutput>(
            origin,
            "GetContactIdByEmail",
            new GetContactIdByEmailInput(
                sleekflowCompanyId,
                email,
                workflowId,
                workflowVersionedId,
                workflowName,
                stateId));

        return getContactIdOutput.ContactId;
    }

    public static async Task<string?> GetOrCreateContactIdByEmail(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var workflowName = (string) ((ScriptObject) context.CurrentGlobal)["workflow_name"];
        var stateId = (string) ((ScriptObject) context.CurrentGlobal)["state_id"];

        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;
        var workflowId = evaluationContext.WorkflowId!;
        var workflowVersionedId = evaluationContext.WorkflowVersionedId!;

        if (obj is not string email)
        {
            return null;
        }

        var getOrCreateContactIdOutput = await InvokeAsync<GetOrCreateContactIdByEmailInput, GetOrCreateContactIdOutput>(
            origin,
            "GetOrCreateContactIdByEmail",
            new GetOrCreateContactIdByEmailInput(
                sleekflowCompanyId,
                email,
                workflowId,
                workflowVersionedId,
                workflowName,
                stateId));

        return getOrCreateContactIdOutput.ContactId;
    }

    [Obsolete("Use GetContact instead")]
    public static ContactLabel[] GetContactLabels(TemplateContext context, object? obj)
    {
        var sleekflowCompanyId = ((EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"]).SleekflowCompanyId;

        if (obj is string id)
        {
            return new ContactLabel[]
            {
            };
        }

        return Array.Empty<ContactLabel>();
    }

    public static async Task<Dictionary<string, object?>?> GetStaff(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;

        if (obj is string id)
        {
            var getStaffOutput = await InvokeAsync<GetStaffInput, GetStaffOutput>(
                origin,
                "GetStaff",
                new GetStaffInput(sleekflowCompanyId, id));

            return getStaffOutput.Staff;
        }

        if (obj is long idLong)
        {
            var getStaffOutput = await InvokeAsync<GetStaffInput, GetStaffOutput>(
                origin,
                "GetStaff",
                new GetStaffInput(sleekflowCompanyId, idLong.ToString()));

            return getStaffOutput.Staff;
        }

        return null;
    }

    [SwaggerInclude]
    public class GetContactListsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_contact_id")]
        [Required]
        public string SleekflowContactId { get; set; }

        [JsonConstructor]
        public GetContactListsInput(string sleekflowCompanyId, string sleekflowContactId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowContactId = sleekflowContactId;
        }
    }

    [SwaggerInclude]
    public class GetContactListsOutput
    {
        [JsonProperty("lists")]
        public List<ContactList> Lists { get; set; }

        [JsonConstructor]
        public GetContactListsOutput(List<ContactList> lists)
        {
            Lists = lists;
        }
    }

    public static async Task<ContactList[]> GetContactLists(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;

        if (obj is string id)
        {
            var getContactOutput = await InvokeAsync<GetContactListsInput, GetContactListsOutput>(
                origin,
                "GetContactLists",
                new GetContactListsInput(sleekflowCompanyId, id));

            return getContactOutput.Lists.ToArray();
        }

        return Array.Empty<ContactList>();
    }

    [SwaggerInclude]
    public class GetContactConversationInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_contact_id")]
        [Required]
        public string SleekflowContactId { get; set; }

        [JsonConstructor]
        public GetContactConversationInput(string sleekflowCompanyId, string sleekflowContactId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowContactId = sleekflowContactId;
        }
    }

    [SwaggerInclude]
    public class GetContactConversationOutput
    {
        [JsonProperty("conversation")]
        public ContactConversation? Conversation { get; set; }

        [JsonConstructor]
        public GetContactConversationOutput(ContactConversation? conversation)
        {
            Conversation = conversation;
        }
    }

    public static async Task<ContactConversation?> GetContactConversation(TemplateContext context, object? obj)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;

        if (obj is string id)
        {
            var getContactOutput = await InvokeAsync<GetContactConversationInput, GetContactConversationOutput>(
                origin,
                "GetContactConversation",
                new GetContactConversationInput(sleekflowCompanyId, id));

            return getContactOutput.Conversation;
        }

        return null;
    }

    [SwaggerInclude]
    public class GetConversationChannelLastMessageInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("channel")]
        [Required]
        public string Channel { get; set; }

        [JsonProperty("channel_id")]
        [Required]
        public string ChannelId { get; set; }

        [JsonProperty("conversation_id")]
        [Required]
        public string ConversationId { get; set; }

        [JsonProperty("is_sent_from_sleekflow")]
        [Required]
        public bool IsSentFromSleekflow { get; set; }

        [JsonConstructor]
        public GetConversationChannelLastMessageInput(
            string sleekflowCompanyId,
            string channel,
            string channelId,
            string conversationId,
            bool isSentFromSleekflow)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Channel = channel;
            ChannelId = channelId;
            ConversationId = conversationId;
            IsSentFromSleekflow = isSentFromSleekflow;
        }
    }

    [SwaggerInclude]
    public class GetConversationChannelLastMessageOutput
    {
        [JsonProperty("last_message")]
        public ConversationChannelLastMessage? LastMessage { get; set; }

        [JsonConstructor]
        public GetConversationChannelLastMessageOutput(
            ConversationChannelLastMessage? lastMessage)
        {
            LastMessage = lastMessage;
        }
    }

    public static async Task<ConversationChannelLastMessage?> GetConversationChannelLastMessage(
        TemplateContext context,
        string channel,
        string channelId,
        string conversationId,
        bool isSentFromSleekflow)
    {
        var evaluationContext = (EvaluationContext) ((ScriptObject) context.CurrentGlobal)["ctx"];
        var sleekflowCompanyId = evaluationContext.SleekflowCompanyId;
        var origin = evaluationContext.Origin;

        var getConversationChannelLastMessageOutput =
            await InvokeAsync<GetConversationChannelLastMessageInput, GetConversationChannelLastMessageOutput>(
            origin,
            "GetConversationChannelLastMessage",
            new GetConversationChannelLastMessageInput(
                sleekflowCompanyId,
                channel,
                channelId,
                conversationId,
                isSentFromSleekflow));

        return getConversationChannelLastMessageOutput.LastMessage;
    }

    private static async Task<TOutput> InvokeAsync<TInput, TOutput>(
        string? origin,
        string functionName,
        TInput input)
    {
        var baseUrl = string.IsNullOrWhiteSpace(origin)
            ? AppConfig.CoreInternalsEndpoint
            : $"{origin}/FlowHub/Internals";

        var url = baseUrl + "/Functions/" + functionName;
        var headers = new Dictionary<string, string?>
        {
            {
                "X-Sleekflow-Flow-Hub-Authorization", InternalsTokenUtils.CreateJwt(AppConfig.CoreInternalsKey)
            },
        };

        var argsJson = JsonConvert.SerializeObject(
            input,
            JsonConfig.DefaultJsonSerializerSettings);

        var resMsg = await HttpPolicies.HttpTransientErrorRetryPolicy
            .ExecuteAsync(
                async () =>
                {
                    var reqMsg = new HttpRequestMessage(
                        HttpMethod.Post,
                        url);

                    foreach (var (key, value) in headers)
                    {
                        reqMsg.Headers.Add(key, value ?? string.Empty);
                    }

                    reqMsg.Content = new StringContent(
                        argsJson,
                        Encoding.UTF8,
                        "application/json");

                    Logger.Information(
                        "SleekflowFunctions: {CommandName} {ArgsJson}",
                        functionName,
                        argsJson);

                    var response = await HttpClient.SendAsync(reqMsg);

                    Logger.Information(
                        "SleekflowFunctions: {CommandName} {ArgsJson} {StatusCode}",
                        functionName,
                        argsJson,
                        response.StatusCode);

                    response.EnsureSuccessStatusCode();

                    return response;
                });

        var responseStr = await resMsg.Content.ReadAsStringAsync();

        Logger.Information(
            "SleekflowFunctions: {CommandName} {ArgsJson} {ResponseStr}",
            functionName,
            argsJson,
            responseStr);

        return JsonConvert.DeserializeObject<TOutput>(responseStr, JsonConfig.DefaultJsonSerializerSettings)!;
    }
}