using System.Text;
using Sleekflow.Exceptions;

namespace Sleekflow.Utils;

public static class Utf8Utils
{
    public static string Utf8EncodeBase64(string sourceString)
    {
        var ut8EncodedSourceString = Encoding.UTF8.GetBytes(sourceString);
        if (ut8EncodedSourceString.Length > 1024)
        {
            throw new SfInternalErrorException("The length of the source string is greater than 1024");
        }

        return Convert.ToBase64String(ut8EncodedSourceString);
    }

    public static string Utf8DecodeBase64(string sourceString)
    {
        return Encoding.UTF8.GetString(Convert.FromBase64String(sourceString));
    }
}