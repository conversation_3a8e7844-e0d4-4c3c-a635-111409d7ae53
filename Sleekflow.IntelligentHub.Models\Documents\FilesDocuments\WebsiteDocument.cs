using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.FileDocument)]
public class WebsiteDocument : KbDocument
{
    [JsonProperty("base_url")]
    public string BaseUrl { get; set; }

    [JsonProperty("base_url_title")]
    public string? BaseUrlTitle { get; set; }

    [JsonProperty("selected_urls")]
    public List<SelectedUrl> SelectedUrls { get; set; }

    [JsonConstructor]
    public WebsiteDocument(
        string id,
        string sleekflowCompanyId,
        string contentType,
        string baseUrl,
        string? baseUrlTitle,
        List<SelectedUrl> selectedUrls,
        Dictionary<string, object?> metadata,
        string fileDocumentProcessStatus,
        double? fileDocumentProcessPercentage,
        List<AgentAssignment> agentAssignments,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string uploadedBy)
        : base(
            id,
            sleekflowCompanyId,
            contentType,
            metadata,
            fileDocumentProcessStatus,
            fileDocumentProcessPercentage,
            agentAssignments,
            createdAt,
            updatedAt,
            uploadedBy,
            SysTypeNames.WebsiteDocument)
    {
        BaseUrl = baseUrl;
        BaseUrlTitle = baseUrlTitle;
        SelectedUrls = selectedUrls;
    }

    public override int GetCharacterCount()
    {
        return SelectedUrls
            .Where(selectedUrl => selectedUrl.Status != SelectedUrlStatuses.Failed)
            .Sum(url => url.PageSize);
    }
}