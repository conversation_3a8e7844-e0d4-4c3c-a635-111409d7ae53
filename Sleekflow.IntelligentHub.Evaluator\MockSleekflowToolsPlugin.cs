using System.Collections.Concurrent;
using System.ComponentModel;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;
using Description = System.ComponentModel.DescriptionAttribute;

namespace Sleekflow.IntelligentHub.Evaluator;

public class MockSleekflowToolsPlugin : ISleekflowToolsPlugin
{
    private readonly ILogger<MockSleekflowToolsPlugin> _logger;
    private readonly SleekflowToolsPlugin _realPlugin;

    // Store calls by test ID
    private static readonly ConcurrentDictionary<string, SleekflowToolsCallInfo> CallInfo = new ();

    public MockSleekflowToolsPlugin(
        ILogger<MockSleekflowToolsPlugin> logger,
        SleekflowToolsPlugin realPlugin)
    {
        _logger = logger;
        _realPlugin = realPlugin;
    }

    [KernelFunction("add_lead_score_custom_object")]
    [Description("Adds the lead score custom object to the lead.")]
    public Task<string> AddLeadScoreCustomObject(
        Kernel kernel,
        [Description("The classification of the lead type.")]
        string classification,
        [Description("The reasong for the classification and the lead score.")]
        string reasoning,
        [Description("The latest lead score of the lead.")]
        string score)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult("Lead Score Custom Object Added (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.LeadScoreCustomObjectCalled = true;
        callInfo.LeadScoreParams = new LeadScoreParams
        {
            Classification = classification, Reasoning = reasoning, Score = score
        };

        _logger.LogInformation(
            "Mock lead score custom object call for test {TestId}: Classification: {Classification}, Score: {Score}",
            testId,
            classification,
            score);

        return Task.FromResult("Lead Score Custom Object Added (Mock)");
    }

    [KernelFunction("assign_to_team")]
    [Description("Assigns the lead to the specified team.")]
    public Task<string> AssignToTeam(
        Kernel kernel,
        [Description("The name of the team to which the lead will be assigned.")]
        string teamName,
        [Description("The reason for triggering assignment.")]
        string assignReason)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult("Assigned to Team (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.AssignToTeamCalled = true;
        callInfo.AssignToTeamParams = new AssignToTeamParams
        {
            TeamName = teamName, AssignReason = assignReason
        };

        _logger.LogInformation(
            "Mock assign to team call for test {TestId}: Team: {TeamName}, Reason: {Reason}",
            testId,
            teamName,
            assignReason);

        return Task.FromResult("Assigned to Team (Mock)");
    }

    [KernelFunction("add_hands_off_custom_object")]
    [Description("Adds the hands off custom object to the lead.")]
    public Task<string> AddHandsOffCustomObject(
        Kernel kernel,
        [Description("The reason for triggering assignment.")]
        string assignmentReason,
        [Description("The latest lead score of the lead.")]
        int leadScore,
        [Description("A comprehensive conversation summary between the lead and our company.")]
        string shortSummary)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult("Hands Off Custom Object Added (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.HandsOffCustomObjectCalled = true;
        callInfo.HandsOffParams = new HandsOffParams
        {
            AssignmentReason = assignmentReason, LeadScore = leadScore, ShortSummary = shortSummary
        };

        _logger.LogInformation(
            "Mock hands off custom object call for test {TestId}: Reason: {Reason}, Score: {Score}",
            testId,
            assignmentReason,
            leadScore);

        return Task.FromResult("Hands Off Custom Object Added (Mock)");
    }

    [KernelFunction("update_contact_properties")]
    [Description("Updates the contact properties with the specified values.")]
    public Task<string> UpdateContactProperties(
        Kernel kernel,
        [Description("List of contact properties to update, each with PropertyId and PropertyValue.")]
        List<ContactProperty> properties)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult("Contact Properties Updated (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.UpdateContactPropertiesCalled = true;
        callInfo.UpdateContactPropertiesParams = new UpdateContactPropertiesParams
        {
            Properties = properties.ToList()
        };

        _logger.LogInformation(
            "Mock update contact properties call for test {TestId} with {Count} properties",
            testId,
            properties.Count);

        foreach (var property in properties)
        {
            _logger.LogInformation("Property {PropertyId} = {PropertyValue}", property.PropertyId, property.PropertyValue);
        }

        return Task.FromResult("Contact Properties Updated (Mock)");
    }

    [KernelFunction("add_contact_labels")]
    [Description("Adds labels to a contact without removing existing labels.")]
    public Task<string> AddContactLabels(
        Kernel kernel,
        [Description("The list of labels to add to the contact.")]
        List<string> labels)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult("Contact Labels Added (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.AddContactLabelsCalled = true;

        // If AddContactLabelsParams already exists, add to existing labels
        if (callInfo.AddContactLabelsParams == null)
        {
            callInfo.AddContactLabelsParams = new AddContactLabelsParams
            {
                Labels = labels.ToList()
            };
        }
        else
        {
            // Add new labels to existing ones, avoiding duplicates
            var existingLabels = callInfo.AddContactLabelsParams.Labels.ToHashSet();
            foreach (var label in labels)
            {
                existingLabels.Add(label);
            }
            callInfo.AddContactLabelsParams.Labels = existingLabels.ToList();
        }

        _logger.LogInformation(
            "Mock add contact labels call for test {TestId}",
            testId);
        _logger.LogInformation("Labels to add: {Labels}", string.Join(", ", labels));

        return Task.FromResult("Contact Labels Added (Mock)");
    }

    [KernelFunction("remove_contact_labels")]
    [Description("Removes specific labels from a contact.")]
    public Task<string> RemoveContactLabels(
        Kernel kernel,
        [Description("The list of labels to remove from the contact.")]
        List<string> labels)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult("Contact Labels Removed (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.RemoveContactLabelsCalled = true;

        // If RemoveContactLabelsParams already exists, add to existing labels
        if (callInfo.RemoveContactLabelsParams == null)
        {
            callInfo.RemoveContactLabelsParams = new RemoveContactLabelsParams
            {
                Labels = labels.ToList()
            };
        }
        else
        {
            // Add new labels to existing ones, avoiding duplicates
            var existingLabels = callInfo.RemoveContactLabelsParams.Labels.ToHashSet();
            foreach (var label in labels)
            {
                existingLabels.Add(label);
            }
            callInfo.RemoveContactLabelsParams.Labels = existingLabels.ToList();
        }

        _logger.LogInformation(
            "Mock remove contact labels call for test {TestId}",
            testId);
        _logger.LogInformation("Labels to remove: {Labels}", string.Join(", ", labels));

        return Task.FromResult("Contact Labels Removed (Mock)");
    }

    [KernelFunction("set_contact_labels")]
    [Description("Removes all existing labels and sets the contact labels to the provided list. Use an empty list to remove all labels.")]
    public Task<string> SetContactLabels(
        Kernel kernel,
        [Description("The list of labels to set for the contact, replacing all existing labels.")]
        List<string> labels)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult(labels.Count > 0 ? "Contact Labels Set (Test ID Missing)" : "All Contact Labels Removed (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.SetContactLabelsCalled = true;
        callInfo.SetContactLabelsParams = new SetContactLabelsParams
        {
            Labels = labels.ToList()
        };

        _logger.LogInformation(
            "Mock set contact labels call for test {TestId}",
            testId);
        _logger.LogInformation("New labels: {Labels}", string.Join(", ", labels));

        return Task.FromResult(labels.Count > 0 ? "Contact Labels Set (Mock)" : "All Contact Labels Removed (Mock)");
    }

    [KernelFunction("add_internal_note")]
    [Description("Adds an internal note to a contact.")]
    public Task<string> AddInternalNote(
        Kernel kernel,
        [Description("The content of the internal note to add to the contact.")]
        string content)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult("Internal Note Added (Test ID Missing)");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.AddInternalNoteCalled = true;
        callInfo.AddInternalNoteParams = new AddInternalNoteParams
        {
            Content = content
        };

        _logger.LogInformation(
            "Mock add internal note call for test {TestId}: Content: {Content}",
            testId,
            content);

        return Task.FromResult("Internal Note Added (Mock)");
    }

    [KernelFunction("send_message")]
    [Description("Sends a message asynchronously to a recipient.")]
    public async Task<string> SendMessage(
        Kernel kernel,
        [Description("The message to send.")]
        string message)
    {
        var testId = ChatEvalTest.CurrentTestId.Value ?? ReActEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return await _realPlugin.SendMessage(kernel, message);
        }

        // Record the start time
        var startTime = DateTime.UtcNow;

        // Call the real plugin to send the message
        var result = await _realPlugin.SendMessage(kernel, message);

        // Calculate processing time
        var processingTime = DateTime.UtcNow - startTime;

        var callInfo = CallInfo.GetOrAdd(testId, _ => new SleekflowToolsCallInfo());
        callInfo.SendMessageAsyncCalled = true;
        callInfo.SendMessageAsyncParams = new SendMessageAsyncParams
        {
            Message = message,
        };
        callInfo.SendMessageProcessingTimeMs = processingTime.TotalMilliseconds;

        _logger.LogInformation(
            "Send message call for test {TestId}: Message: {Message}, ProcessingTime: {ProcessingTime}ms",
            testId,
            message,
            processingTime.TotalMilliseconds);

        return result;
    }

    /// <summary>
    /// Checks if AssignToTeam was called for a specific test.
    /// </summary>
    public static bool WasAssignToTeamCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.AssignToTeamCalled;
    }

    /// <summary>
    /// Checks if AddLeadScoreCustomObject was called for a specific test.
    /// </summary>
    public static bool WasLeadScoreCustomObjectCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.LeadScoreCustomObjectCalled;
    }

    /// <summary>
    /// Checks if AddHandsOffCustomObject was called for a specific test.
    /// </summary>
    public static bool WasHandsOffCustomObjectCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.HandsOffCustomObjectCalled;
    }

    /// <summary>
    /// Checks if UpdateContactProperties was called for a specific test.
    /// </summary>
    public static bool WasUpdateContactPropertiesCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.UpdateContactPropertiesCalled;
    }

    /// <summary>
    /// Checks if AddContactLabels was called for a specific test.
    /// </summary>
    public static bool WasAddContactLabelsCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.AddContactLabelsCalled;
    }

    /// <summary>
    /// Checks if RemoveContactLabels was called for a specific test.
    /// </summary>
    public static bool WasRemoveContactLabelsCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.RemoveContactLabelsCalled;
    }

    /// <summary>
    /// Checks if SetContactLabels was called for a specific test.
    /// </summary>
    public static bool WasSetContactLabelsCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.SetContactLabelsCalled;
    }

    /// <summary>
    /// Checks if SendMessageAsync was called for a specific test.
    /// </summary>
    public static bool WasSendMessageAsyncCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.SendMessageAsyncCalled;
    }

    /// <summary>
    /// Checks if AddInternalNote was called for a specific test.
    /// </summary>
    public static bool WasAddInternalNoteCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.AddInternalNoteCalled;
    }

    /// <summary>
    /// Get the parameters used in AssignToTeam for a specific test.
    /// </summary>
    public static AssignToTeamParams? GetAssignToTeamParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.AssignToTeamParams : null;
    }

    /// <summary>
    /// Get the parameters used in AddLeadScoreCustomObject for a specific test.
    /// </summary>
    public static LeadScoreParams? GetLeadScoreParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.LeadScoreParams : null;
    }

    /// <summary>
    /// Get the parameters used in AddHandsOffCustomObject for a specific test.
    /// </summary>
    public static HandsOffParams? GetHandsOffParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.HandsOffParams : null;
    }

    /// <summary>
    /// Get the parameters used in UpdateContactProperties for a specific test.
    /// </summary>
    public static UpdateContactPropertiesParams? GetUpdateContactPropertiesParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.UpdateContactPropertiesParams : null;
    }

    /// <summary>
    /// Get the parameters used in AddContactLabels for a specific test.
    /// </summary>
    public static AddContactLabelsParams? GetAddContactLabelsParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.AddContactLabelsParams : null;
    }

    /// <summary>
    /// Get the parameters used in RemoveContactLabels for a specific test.
    /// </summary>
    public static RemoveContactLabelsParams? GetRemoveContactLabelsParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.RemoveContactLabelsParams : null;
    }

    /// <summary>
    /// Get the parameters used in SetContactLabels for a specific test.
    /// </summary>
    public static SetContactLabelsParams? GetSetContactLabelsParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.SetContactLabelsParams : null;
    }

    /// <summary>
    /// Get the parameters used in SendMessageAsync for a specific test.
    /// </summary>
    public static SendMessageAsyncParams? GetSendMessageAsyncParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.SendMessageAsyncParams : null;
    }

    /// <summary>
    /// Get the parameters used in AddInternalNote for a specific test.
    /// </summary>
    public static AddInternalNoteParams? GetAddInternalNoteParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.AddInternalNoteParams : null;
    }

    /// <summary>
    /// Calculates a time efficiency bonus based on SendMessageAsync processing time (0.0 to 1.0).
    /// </summary>
    public static double GetSendMessageProcessingTimeBonus(string testId)
    {
        if (!CallInfo.TryGetValue(testId, out var info) || !info.SendMessageAsyncCalled)
        {
            return 0.0;
        }

        // Define thresholds for processing time
        const double fastThreshold = 500.0; // ms
        const double slowThreshold = 2000.0; // ms

        if (info.SendMessageProcessingTimeMs <= fastThreshold)
        {
            return 1.0; // Full bonus for fast processing
        }

        if (info.SendMessageProcessingTimeMs >= slowThreshold)
        {
            return 0.0; // No bonus for slow processing
        }

        // Linear scale between thresholds
        return 1.0 - ((info.SendMessageProcessingTimeMs - fastThreshold) / (slowThreshold - fastThreshold));
    }

    /// <summary>
    /// Clears the stored call info for a specific test.
    /// </summary>
    public static void ClearTestState(string testId)
    {
        CallInfo.TryRemove(testId, out _);
    }
}

public class SleekflowToolsCallInfo
{
    public bool AssignToTeamCalled { get; set; }

    public bool LeadScoreCustomObjectCalled { get; set; }

    public bool HandsOffCustomObjectCalled { get; set; }

    public bool UpdateContactPropertiesCalled { get; set; }

    public bool AddContactLabelsCalled { get; set; }

    public bool RemoveContactLabelsCalled { get; set; }

    public bool SetContactLabelsCalled { get; set; }

    public bool SendMessageAsyncCalled { get; set; }

    public bool AddInternalNoteCalled { get; set; }

    public AssignToTeamParams? AssignToTeamParams { get; set; }

    public LeadScoreParams? LeadScoreParams { get; set; }

    public HandsOffParams? HandsOffParams { get; set; }

    public UpdateContactPropertiesParams? UpdateContactPropertiesParams { get; set; }

    public AddContactLabelsParams? AddContactLabelsParams { get; set; }

    public RemoveContactLabelsParams? RemoveContactLabelsParams { get; set; }

    public SetContactLabelsParams? SetContactLabelsParams { get; set; }

    public SendMessageAsyncParams? SendMessageAsyncParams { get; set; }

    public AddInternalNoteParams? AddInternalNoteParams { get; set; }

    public double SendMessageProcessingTimeMs { get; set; }
}

public class AssignToTeamParams
{
    public string TeamName { get; set; } = string.Empty;

    public string AssignReason { get; set; } = string.Empty;
}

public class LeadScoreParams
{
    public string Classification { get; set; } = string.Empty;

    public string Reasoning { get; set; } = string.Empty;

    public string Score { get; set; } = string.Empty;
}

public class HandsOffParams
{
    public string AssignmentReason { get; set; } = string.Empty;

    public int LeadScore { get; set; }

    public string ShortSummary { get; set; } = string.Empty;
}

public class UpdateContactPropertiesParams
{
    public List<ContactProperty> Properties { get; set; } = new List<ContactProperty>();
}

public class AddContactLabelsParams
{
    public List<string> Labels { get; set; } = new List<string>();
}

public class RemoveContactLabelsParams
{
    public List<string> Labels { get; set; } = new List<string>();
}

public class SetContactLabelsParams
{
    public List<string> Labels { get; set; } = new List<string>();
}

public class SendMessageAsyncParams
{
    public string Message { get; set; } = string.Empty;
}

public class AddInternalNoteParams
{
    public string Content { get; set; } = string.Empty;
}