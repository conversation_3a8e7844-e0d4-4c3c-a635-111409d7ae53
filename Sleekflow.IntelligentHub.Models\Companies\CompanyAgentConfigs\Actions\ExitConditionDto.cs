using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class ExitConditionDto
{
    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("operator")]
    public string? Operator { get; set; }

    [JsonProperty("values")]
    public List<object>? Values { get; set; }

    [JsonConstructor]
    public ExitConditionDto(
        string title,
        string type,
        string description,
        string? @operator = null,
        List<object>? values = null)
    {
        Title = title;
        Type = type;
        Description = description;
        Operator = @operator;
        Values = values;
    }

    public ExitConditionDto(ExitCondition condition)
    {
        Title = condition.Title;
        Type = condition.Type;
        Description = condition.Description;
        Operator = condition.Operator;
        Values = condition.Values;
    }
} 