using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class HandoffTeamSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("handoff_team")]
    public HandoffTeam HandoffTeam { get; set; }

    [JsonConstructor]
    public HandoffTeamSnapshot(List<SfChatEntry> conversationContext, HandoffTeam handoffTeam)
    {
        ConversationContext = conversationContext;
        HandoffTeam = handoffTeam;
    }
}