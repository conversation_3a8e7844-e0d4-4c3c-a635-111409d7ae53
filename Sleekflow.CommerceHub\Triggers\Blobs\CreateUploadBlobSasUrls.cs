using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Blobs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Blobs;

[TriggerGroup(ControllerNames.Blobs)]
public class CreateUploadBlobSasUrls
    : ITrigger<
        CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsInput,
        CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsOutput>
{
    private readonly IBlobService _blobService;

    public CreateUploadBlobSasUrls(IBlobService blobService)
    {
        _blobService = blobService;
    }

    public class CreateUploadBlobSasUrlsInput
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        [Required]
        public string StoreId { get; set; }

        [Range(1, 16)]
        [JsonProperty("number_of_blobs")]
        [Required]
        public int NumberOfBlobs { get; set; }

        [JsonProperty("blob_type")]
        [Required]
        public string BlobType { get; set; }

        [JsonConstructor]
        public CreateUploadBlobSasUrlsInput(
            string sleekflowCompanyId,
            int numberOfBlobs,
            string storeId,
            string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            NumberOfBlobs = numberOfBlobs;
            StoreId = storeId;
            BlobType = blobType;
        }
    }

    public class UploadBlobDto
    {
        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonProperty("blob_url")]
        public string BlobUrl { get; set; }

        [JsonProperty("expires_on")]
        public DateTimeOffset ExpiresOn { get; set; }

        [JsonProperty("blob_name")]
        public string BlobName { get; set; }

        [JsonConstructor]
        public UploadBlobDto(
            string blobId,
            string blobUrl,
            DateTimeOffset expiresOn,
            string blobName)
        {
            BlobId = blobId;
            BlobUrl = blobUrl;
            ExpiresOn = expiresOn;
            BlobName = blobName;
        }

        public UploadBlobDto(
            IBlobService.GetBlobSasUrlOutput getBlobSasUrlOutput)
            : this(
                getBlobSasUrlOutput.BlobId,
                getBlobSasUrlOutput.Url,
                getBlobSasUrlOutput.ExpiresOn,
                getBlobSasUrlOutput.BlobName)
        {
        }
    }

    public class CreateUploadBlobSasUrlsOutput
    {
        [JsonProperty("upload_blobs")]
        public List<UploadBlobDto> UploadBlobs { get; set; }

        [JsonConstructor]
        public CreateUploadBlobSasUrlsOutput(
            List<UploadBlobDto> uploadBlobs)
        {
            UploadBlobs = uploadBlobs;
        }
    }

    public async Task<CreateUploadBlobSasUrlsOutput> F(CreateUploadBlobSasUrlsInput createUploadBlobSasUrlsInput)
    {
        var getBlobSasUrlOutputs = await _blobService.CreateUploadBlobSasUrlsAsync(
            createUploadBlobSasUrlsInput.SleekflowCompanyId,
            createUploadBlobSasUrlsInput.StoreId,
            createUploadBlobSasUrlsInput.BlobType,
            createUploadBlobSasUrlsInput.NumberOfBlobs);

        return new CreateUploadBlobSasUrlsOutput(
            getBlobSasUrlOutputs
                .Select(x => new UploadBlobDto(x))
                .ToList());
    }
}