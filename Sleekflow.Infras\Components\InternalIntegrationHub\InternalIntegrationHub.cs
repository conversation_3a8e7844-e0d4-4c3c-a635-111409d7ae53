﻿using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;

namespace Sleekflow.Infras.Components.InternalIntegrationHub;

public class InternalIntegrationHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly InternalIntegrationHubDb.InternalIntegrationHubDbOutput _internalIntegrationHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;
    private readonly CoreSqlDbConfig _coreSqlDbConfig;

    public InternalIntegrationHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        InternalIntegrationHubDb.InternalIntegrationHubDbOutput internalIntegrationHubDbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig,
        CoreSqlDbConfig coreSqlDbConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _internalIntegrationHubDbOutput = internalIntegrationHubDbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
        _coreSqlDbConfig = coreSqlDbConfig;
    }

    public List<App.ContainerApp> InitInternalIntegrationHub()
    {
        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.InternalIntegrationHub),
            _myConfig.BuildTime);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.InternalIntegrationHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var redis = managedEnvAndAppsTuple.Redis;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.InternalIntegrationHub));

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = 1,
                            MaxReplicas = 3,
                            Rules = new List<string>
                                {
                                    "omni-hr-employee-info-to-net-suite-integration-event",
                                    "sync-company-to-net-suite-integration-event"
                                }
                                .Select(
                                    queueName => new App.Inputs.ScaleRuleArgs
                                    {
                                        Name = $"azure-servicebus-{queueName}",
                                        Custom = new App.Inputs.CustomScaleRuleArgs
                                        {
                                            Type = "azure-servicebus",
                                            Metadata = new InputMap<string>
                                            {
                                                {
                                                    "queueName", queueName
                                                },
                                                {
                                                    "messageCount", "400"
                                                },
                                            },
                                            Auth = new InputList<App.Inputs.ScaleRuleAuthArgs>
                                            {
                                                new App.Inputs.ScaleRuleAuthArgs
                                                {
                                                    TriggerParameter = "connection",
                                                    SecretRef = "service-bus-keda-conn-str-secret"
                                                },
                                            }
                                        }
                                    })
                                .Concat(
                                    new List<App.Inputs.ScaleRuleArgs>()
                                    {
                                        new App.Inputs.ScaleRuleArgs
                                        {
                                            Name = "http",
                                            Http = new App.Inputs.HttpScaleRuleArgs
                                            {
                                                Metadata = new InputMap<string>
                                                {
                                                    {
                                                        "concurrentRequests", "300"
                                                    }
                                                }
                                            }
                                        }
                                    })
                                .ToList(),
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-iih-app",
                                Image = myImage.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 0.5, Memory = "1Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>
                                {
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment(),
                                        },

                                        #region InternalIntegrationHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_INTERNAL_INTEGRATION_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_internalIntegrationHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_INTERNAL_INTEGRATION_HUB_DB_KEY",
                                            Value = _internalIntegrationHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_INTERNAL_INTEGRATION_HUB_DB_DATABASE_ID",
                                            Value = _internalIntegrationHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EAS_TRAVIS_DATABASE_CONNECTION_STRING",
                                            Value = _coreSqlDbConfig.EasDbConnectionString,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EUS_TRAVIS_DATABASE_CONNECTION_STRING",
                                            Value = _coreSqlDbConfig.EusDbConnectionString,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SEAS_TRAVIS_DATABASE_CONNECTION_STRING",
                                            Value = _coreSqlDbConfig.SeasDbConnectionString,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "UAEN_TRAVIS_DATABASE_CONNECTION_STRING",
                                            Value = _coreSqlDbConfig.UaenDbConnectionString,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WEU_TRAVIS_DATABASE_CONNECTION_STRING",
                                            Value = _coreSqlDbConfig.WeuDbConnectionString,
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.InternalIntegrationHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, redis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region NetSuiteConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NETSUITE_CONSUMER_KEY",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? string.Empty // NetSuite not yet provided
                                                : "e9a0ef4c5bafe715ae884dd291f503632469b8780fdf5e545803d1d8f5a82159",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NETSUITE_CONSUMER_SECRET",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? string.Empty // NetSuite not yet provided
                                                : "cb7500c779d6febbb7cfe6552d2898c3e4fe213a412dfa86236ea8452bdae2ff",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NETSUITE_ACCESS_TOKEN",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? string.Empty // NetSuite not yet provided
                                                : "ed19adda6b8132f388074da18b9d2cbd88dd83362e533f2e4cf28f5368ed84d1",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NETSUITE_ACCESS_TOKEN_SECRET",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? string.Empty // NetSuite not yet provided
                                                : "6f4d8da162da1074dd809033d8b9e0a74c2c6ea7ae10e718ba80cabc42d70ba8",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NETSUITE_ACCOUNT_ID",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? string.Empty // NetSuite not yet provided
                                                : "7498133-sb1",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NETSUITE_REALM",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? string.Empty // NetSuite not yet provided
                                                : "7498133_SB1",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NETSUITE_BASE_URL",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? string.Empty // NetSuite not yet provided
                                                : "https://7498133-sb1.suitetalk.api.netsuite.com/services/rest/record/v1",
                                        },

                                        #endregion

                                        #region OmniHrConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OMNIHR_BASE_URL", Value = "https://api.omnihr.co/api/v1",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OMNIHR_PASSWORD", Value = "fmv5fpz7hfw7XTM_fjr",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "OMNIHR_USERNAME", Value = "<EMAIL>",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.InternalIntegrationHub, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }
}
