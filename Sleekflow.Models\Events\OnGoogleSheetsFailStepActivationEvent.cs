﻿using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.Events;

public class OnGoogleSheetsFailStepActivationEvent
{
    public string AggregateStepId { get; set; }

    public string ProxyStateId { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public string? AggregateStateContext { get; set; }

    public Exception? Exception { get; set; }

    public OnGoogleSheetsFailStepActivationEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string? aggregateStateContext,
        Exception? exception)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        AggregateStateContext = aggregateStateContext;
        Exception = exception;
    }
}