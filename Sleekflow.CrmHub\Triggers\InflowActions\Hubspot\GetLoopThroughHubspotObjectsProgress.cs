﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Hubspot;

[TriggerGroup(TriggerGroups.InflowActions)]
public class GetLoopThroughHubspotObjectsProgress : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetLoopThroughHubspotObjectsProgress(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetLoopThroughHubspotObjectsProgressInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [Json<PERSON>roperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public GetLoopThroughHubspotObjectsProgressInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class GetLoopThroughHubspotObjectsProgressOutput
    {
        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public GetLoopThroughHubspotObjectsProgressOutput(
            int count,
            DateTime lastUpdateTime,
            string status)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            Status = status;
        }
    }

    public async Task<GetLoopThroughHubspotObjectsProgressOutput?> F(
        GetLoopThroughHubspotObjectsProgressInput getLoopThroughHubspotObjectsProgressInput)
    {
        var providerService = _providerSelector.GetProviderService("hubspot-integrator");

        var output = await providerService.GetLoopThroughObjectsProgressAsync(
            getLoopThroughHubspotObjectsProgressInput.SleekflowCompanyId,
            "hubspot-integrator",
            getLoopThroughHubspotObjectsProgressInput.FlowHubWorkflowId,
            getLoopThroughHubspotObjectsProgressInput.FlowHubWorkflowVersionedId);

        if (output is null)
        {
            return null;
        }

        return new GetLoopThroughHubspotObjectsProgressOutput(
            output.Count,
            output.LastUpdateTime,
            output.Status);
    }
}