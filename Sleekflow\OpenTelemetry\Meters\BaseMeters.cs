using System.Diagnostics.Metrics;
using Microsoft.Extensions.Logging;
using Sleekflow.OpenTelemetry.Meters.Interfaces;

namespace Sleekflow.OpenTelemetry.Meters;

public interface IBasicMeters
{
    void IncrementCounter<T>(T name, string? option = null, int count = 1, IMeterTags? tags = null);
}

public class BaseMeters : IBasicMeters
{
    public const string SleekflowMeters = "sleekflow.meters";
    private readonly Meter? _meter;
    private readonly ILogger _logger;

    protected bool IsMeterEnabled { get; }

    protected BaseMeters(IMeterFactory meterFactory, ILogger logger)
    {
        _logger = logger;
        IsMeterEnabled = false;
        if (string.IsNullOrEmpty(Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_CONNECTION_STRING")))
        {
            return;
        }

        IsMeterEnabled = true;
        _meter = meterFactory.Create(SleekflowMeters);
    }

    protected Counter<T> CreateCounter<T>(string name, string? unit = null, string? description = null)
        where T : struct
    {
        return _meter!.CreateCounter<T>(name, unit, description);
    }

    public void IncrementCounter<T>(T name, string? option = null, int count = 1, IMeterTags? tags = null)
    {
        try
        {
            if (IsMeterEnabled)
            {
                var counter = GetCounter(name, option);
                if (tags != null)
                {
                    counter.Add(count, tags.ToMeterTags()!);
                }
                else
                {
                    counter.Add(count);
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "Error occur during increment counter");
        }
    }

    protected virtual Counter<int> GetCounter<T>(T name, string? option = null)
    {
        throw new NotImplementedException();
    }

    protected static string GetComposeKey(string name, string? option)
    {
        return option is null ? name : $"{name}.{option}";
    }
}