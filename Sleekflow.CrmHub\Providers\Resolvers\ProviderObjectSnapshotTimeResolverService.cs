﻿using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Providers.Resolvers;

public interface IProviderObjectSnapshotTimeResolverService
{
    ProviderObjectSnapshotTime Resolve(
        Dictionary<string, object?> dict,
        string providerName,
        DateTimeOffset? defaultDateTimeOffset);

    public class ProviderObjectSnapshotTime
    {
        public DateTimeOffset SnapshotTime { get; set; }

        public ProviderObjectSnapshotTime(
            DateTimeOffset snapshotTime)
        {
            SnapshotTime = snapshotTime;
        }
    }
}

public class ProviderObjectSnapshotTimeResolverService : ISingletonService, IProviderObjectSnapshotTimeResolverService
{
    private DateTimeOffset? GetDateTimeValueOrDefault(IReadOnlyDictionary<string, object?> dict, string key)
    {
        var valueOrDefault = dict.GetValueOrDefault(key);

        if (valueOrDefault is DateTimeOffset dateTimeOffset)
        {
            return dateTimeOffset;
        }

        if (valueOrDefault is JToken jToken && jToken is { Type: JTokenType.Date })
        {
            var dateTime = jToken!.Value<DateTimeOffset>();

            return dateTime;
        }

        return null;
    }

    public IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime Resolve(
        Dictionary<string, object?> dict,
        string providerName,
        DateTimeOffset? defaultDateTimeOffset)
    {
        if (providerName == "salesforce-integrator")
        {
            var dateTimes = new List<DateTimeOffset?>
            {
                GetDateTimeValueOrDefault(dict, "salesforce-integrator:LastModifiedDate"),
                GetDateTimeValueOrDefault(dict, "salesforce-integrator:CreatedDate"),
                GetDateTimeValueOrDefault(dict, "salesforce-integrator:SystemModstamp"),
                defaultDateTimeOffset,
                DateTimeOffset.UtcNow,
            };
            var dateTime = (DateTimeOffset) dateTimes.Find(l => l != null)!;

            return new IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime(
                dateTime.ToUniversalTime());
        }
        else if (providerName == "hubspot-integrator")
        {
            var dateTimes = new List<DateTimeOffset?>
            {
                GetDateTimeValueOrDefault(dict, "hubspot-integrator:lastmodifieddate"),
                GetDateTimeValueOrDefault(dict, "hubspot-integrator:createdate"),
                GetDateTimeValueOrDefault(dict, "hubspot-integrator:createdAt"),
                GetDateTimeValueOrDefault(dict, "hubspot-integrator:updatedAt"),
                defaultDateTimeOffset,
                DateTimeOffset.UtcNow,
            };
            var dateTime = (DateTimeOffset) dateTimes.Find(l => l != null)!;

            return new IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime(
                dateTime.ToUniversalTime());
        }
        else if (providerName == "sleekflow")
        {
            var dateTimes = new List<DateTimeOffset?>
            {
                GetDateTimeValueOrDefault(dict, "sleekflow:UpdatedAt"),
                GetDateTimeValueOrDefault(dict, "sleekflow:CreatedAt"),
                defaultDateTimeOffset,
                DateTimeOffset.UtcNow,
            };
            var dateTime = (DateTimeOffset) dateTimes.Find(l => l != null)!;

            return new IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime(dateTime);
        }

        return new IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime(DateTime.UtcNow);
    }
}