using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class ReInitProviderConnection : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public ReInitProviderConnection(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class ReInitProviderConnectionInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [Required]
        [JsonProperty("success_url")]
        public string SuccessUrl { get; set; }

        [Required]
        [JsonProperty("failure_url")]
        public string FailureUrl { get; set; }

        [JsonConstructor]
        public ReInitProviderConnectionInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId,
            string successUrl,
            string failureUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
        }
    }

    public class ReInitProviderConnectionOutput
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("is_re_authentication_required")]
        public bool IsReAuthenticationRequired { get; set; }

        [JsonProperty("context")]
        public dynamic? Context { get; set; }

        [JsonConstructor]
        public ReInitProviderConnectionOutput(
            string providerName,
            bool isReAuthenticationRequired,
            object? context)
        {
            ProviderName = providerName;
            IsReAuthenticationRequired = isReAuthenticationRequired;
            Context = context;
        }
    }

    public async Task<ReInitProviderConnectionOutput> F(
        ReInitProviderConnectionInput reInitProviderConnectionInput)
    {
        var providerService = _providerSelector.GetProviderService(reInitProviderConnectionInput.ProviderName);

        var reInitProviderConnectionOutput = await providerService.ReInitProviderConnectionAsync(
            reInitProviderConnectionInput.SleekflowCompanyId,
            reInitProviderConnectionInput.ProviderConnectionId,
            reInitProviderConnectionInput.SuccessUrl,
            reInitProviderConnectionInput.FailureUrl);

        return new ReInitProviderConnectionOutput(
            reInitProviderConnectionOutput.ProviderName,
            reInitProviderConnectionOutput.IsReAuthenticationRequired,
            reInitProviderConnectionOutput.Context);
    }
}