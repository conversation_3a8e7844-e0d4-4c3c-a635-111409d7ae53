﻿using System.Runtime.InteropServices.ComTypes;

namespace Sleekflow.IntelligentHub.Models.Workers;

public static class ProcessFileDocumentStatuses
{
    // deprecated - UI does not use these anymore
    public const string Pending = "pending";
    public const string Processing = "processing";
    public const string Completed = "completed";
    public const string Failed = "failed";

    public const string NotConverted = "not_converted";
    public const string Converting = "converting";
    public const string ReadyToAssign = "ready_to_assign";
    public const string FailedToConvert = "failed_to_convert";

    public static string ToOldStatus(string status)
    {
        return status switch
        {
            NotConverted => Pending,
            Converting => Processing,
            ReadyToAssign => Completed,
            FailedToConvert => Failed,
            _ => status
        };
    }

    public static string ToNewStatus(string status)
    {
        return status switch
        {
            Pending => NotConverted,
            Processing => NotConverted, // old in-progress files will need to be converted again using the new pipeline
            Completed => ReadyToAssign,
            Failed => FailedToConvert,
            _ => status
        };
    }
}