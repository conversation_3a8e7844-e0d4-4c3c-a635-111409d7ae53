﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateGoogleSheetsRowStepExecutor : IStepExecutor
{
}

public class CreateGoogleSheetsRowStepExecutor
    : GeneralStepExecutor<CallStep<CreateGoogleSheetsRowStepArgs>>,
        ICreateGoogleSheetsRowStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public CreateGoogleSheetsRowStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var createGoogleSheetsRowInput = await GetArgs(callStep, state);

            // Publish the create request
            await _serviceBusManager.PublishAsync(
                new CreateGoogleSheetsRowRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    createGoogleSheetsRowInput.StateIdentity.SleekflowCompanyId,
                    createGoogleSheetsRowInput.ConnectionId,
                    createGoogleSheetsRowInput.SpreadsheetId,
                    createGoogleSheetsRowInput.WorksheetId,
                    createGoogleSheetsRowInput.HeaderRowId,
                    createGoogleSheetsRowInput.FieldsDict));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnGoogleSheetsFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("GoogleSheets create row operation timed out after 5 minutes")),
                typeof(OnGoogleSheetsFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class CreateGoogleSheetsRowInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("spreadsheet_id")]
        [Required]
        public string SpreadsheetId { get; set; }

        [JsonProperty("worksheet_id")]
        [Required]
        public string WorksheetId { get; set; }

        [JsonProperty("header_row_id")]
        [Required]
        public string HeaderRowId { get; set; }

        [JsonProperty("fields_dict")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> FieldsDict { get; set; }

        [JsonConstructor]
        public CreateGoogleSheetsRowInput(
            string stateId,
            StateIdentity stateIdentity,
            string connectionId,
            string spreadsheetId,
            string worksheetId,
            string headerRowId,
            Dictionary<string, object?> fieldsDict)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ConnectionId = connectionId;
            SpreadsheetId = spreadsheetId;
            WorksheetId = worksheetId;
            HeaderRowId = headerRowId;
            FieldsDict = fieldsDict;
        }
    }

    private async Task<CreateGoogleSheetsRowInput> GetArgs(
        CallStep<CreateGoogleSheetsRowStepArgs> callStep,
        ProxyState state)
    {
        var connectionId = callStep.Args.ConnectionId;
        var spreadsheetId = callStep.Args.SpreadsheetId;
        var worksheetId = callStep.Args.WorksheetId;
        var headerRowId = callStep.Args.HeaderRowId;

        var fieldsKeyExprDict = callStep.Args.FieldsIdExprSet
            .GroupBy(x => x.FieldId)
            .ToDictionary(
                x => x.Key,
                g => g.Last().FieldValueExpr);

        var fieldsDict = new Dictionary<string, object?>();

        foreach (var entry in fieldsKeyExprDict)
        {
            fieldsDict[entry.Key] = string.IsNullOrWhiteSpace(entry.Value)
                ? null
                : await _stateEvaluator.EvaluateExpressionAsync(state, entry.Value);
        }

        return new CreateGoogleSheetsRowInput(
            state.Id,
            state.Identity,
            connectionId,
            spreadsheetId,
            worksheetId,
            headerRowId,
            fieldsDict);
    }
}