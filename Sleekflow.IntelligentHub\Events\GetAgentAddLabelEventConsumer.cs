using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Consumers;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Events;
using Sleekflow.Models.Prompts;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentAddLabelConsumerDefinition : ConsumerDefinition<GetAgentAddLabelConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentAddLabelConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentAddLabelConsumer :
    FlowHubAgentGenericConsumer<GetAddLabelEvent>,
    IConsumer<GetAddLabelEvent>
{
    private readonly IReviewerService _reviewerService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GetAgentAddLabelConsumer(
        IBus bus,
        IReviewerService reviewerService,
        ILogger<GetAgentAddLabelConsumer> logger,
        ICompanyAgentConfigService companyAgentConfigService,
        IIntelligentHubUsageService intelligentHubUsageService)
        : base(logger, bus)
    {
        _reviewerService = reviewerService;
        _companyAgentConfigService = companyAgentConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    protected override async Task HandleMessageAsync(ConsumeContext<GetAddLabelEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var conversationContext = message.ConversationContext;
        var agentId = message.AgentId;

        var companyAgentConfig = await _companyAgentConfigService.GetOrDefaultAsync(
            agentId,
            sleekflowCompanyId);

        var labels = companyAgentConfig?.Actions?.AddLabel?.Labels;
        var instructions = companyAgentConfig?.Actions?.AddLabel?.Instructions;
        if (labels == null || labels.Count == 0 || string.IsNullOrEmpty(instructions))
        {
            throw new Exception(
                $"No add label labels found for company {sleekflowCompanyId} and agent {agentId}. " +
                "Please configure the add label labels in the company agent config.");
        }


        using var d1 = Serilog.Context.LogContext.PushProperty("SleekflowCompanyId", sleekflowCompanyId);
        using var d2 = Serilog.Context.LogContext.PushProperty("StateId", message.ProxyStateId);

        _logger.LogInformation(
            "Getting add label for {SleekflowCompanyId} {ConversationContext}",
            sleekflowCompanyId,
            JsonConvert.SerializeObject(conversationContext, JsonConfig.DefaultLoggingJsonSerializerSettings));

        AddLabelResult? addLabel = null;
        try
        {
            addLabel = await _reviewerService.GetAddLabelAsync(
                conversationContext,
                labels,
                instructions);

            _logger.LogInformation("Add label: {AddLabel}", JsonConvert.SerializeObject(addLabel));

            await _intelligentHubUsageService.RecordUsageAsync(
                sleekflowCompanyId,
                PriceableFeatures.Scoring,
                null,
                new AddLabelSnapshot(
                    conversationContext,
                    addLabel));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get add label");
        }

        var response = new GetAddLabelEvent.Response(
            addLabel?.Hashtag ?? "Failed to add label",
            addLabel?.Reason ?? "Failed to add label");

        _logger.LogInformation(
            "Agent add label published to OnAgentCompleteStepActivationEvent {Response} {ProxyStateId} {AggregateStepId} {StackEntries}",
            JsonConvert.SerializeObject(response),
            message.ProxyStateId,
            message.AggregateStepId,
            JsonConvert.SerializeObject(message.StackEntries));

        await _bus.Publish(
           new OnAgentAddLabelCompleteEvent(
               message.AggregateStepId,
               message.ProxyStateId,
               message.StackEntries,
               JsonConvert.SerializeObject(response)));
    }
}