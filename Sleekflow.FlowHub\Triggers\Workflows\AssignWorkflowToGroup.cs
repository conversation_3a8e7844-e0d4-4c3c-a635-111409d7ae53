﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class AssignWorkflowToGroup
    : ITrigger<
        AssignWorkflowToGroup.AssignWorkflowToGroupInput,
        AssignWorkflowToGroup.AssignWorkflowToGroupOutput>
{
    private readonly IWorkflowService _workflowService;

    public AssignWorkflowToGroup(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class AssignWorkflowToGroupInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("workflow_group_id")]
        public string? WorkflowGroupId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public AssignWorkflowToGroupInput(
            string workflowVersionedId,
            string? workflowGroupId,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            WorkflowVersionedId = workflowVersionedId;
            WorkflowGroupId = workflowGroupId;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class AssignWorkflowToGroupOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public AssignWorkflowToGroupOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<AssignWorkflowToGroupOutput> F(AssignWorkflowToGroupInput input)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var updatedWorkflow = await _workflowService.AssignWorkflowToGroupAsync(
            input.WorkflowVersionedId,
            input.SleekflowCompanyId,
            input.WorkflowGroupId,
            sleekflowStaff);

        return new AssignWorkflowToGroupOutput(new WorkflowDto(updatedWorkflow));
    }
}