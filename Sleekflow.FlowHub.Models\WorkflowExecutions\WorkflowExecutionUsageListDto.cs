﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionUsageListDto
{
    [JsonProperty("workflow")]
    public LightweightWorkflowDto Workflow { get; set; }

    [JsonProperty("total_execution_count")]
    public long TotalExecutionCount { get; set; }

    [JsonProperty("failed_execution_count")]
    public long FailedExecutionCount { get; set; }

    [JsonProperty("last_enrolled_at")]
    public DateTimeOffset? LastEnrolledAt { get; set; }

    [JsonConstructor]
    public WorkflowExecutionUsageListDto(
        LightweightWorkflowDto workflow,
        long totalExecutionCount,
        long failedExecutionCount,
        DateTimeOffset? lastEnrolledAt)
    {
        Workflow = workflow;
        TotalExecutionCount = totalExecutionCount;
        FailedExecutionCount = failedExecutionCount;
        LastEnrolledAt = lastEnrolledAt;
    }
}