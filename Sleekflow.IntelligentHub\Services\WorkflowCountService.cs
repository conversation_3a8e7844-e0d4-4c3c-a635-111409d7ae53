using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.FlowHub;

namespace Sleekflow.IntelligentHub.Services;

public interface IWorkflowCountService
{
  Task<Dictionary<string, int>> GetActiveWorkflowCountsByAgentConfigIdsAsync(
    string sleekflowCompanyId,
    List<string> agentConfigIds);
}

public class WorkflowCountService : IWorkflowCountService, IScopedService
{
  private readonly IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest> _requestClient;
  private readonly ILogger<WorkflowCountService> _logger;

  public WorkflowCountService(
    IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest> requestClient,
    ILogger<WorkflowCountService> logger)
  {
    _requestClient = requestClient;
    _logger = logger;
  }

  public async Task<Dictionary<string, int>> GetActiveWorkflowCountsByAgentConfigIdsAsync(
    string sleekflowCompanyId,
    List<string> agentConfigIds)
  {
    var result = new Dictionary<string, int>();

    if (!agentConfigIds.Any())
    {
      return result;
    }

    // Initialize all counts to 0
    foreach (var agentConfigId in agentConfigIds)
    {
      result[agentConfigId] = 0;
    }

    try
    {
      var request = new GetActiveWorkflowCountsByAgentConfigIdsRequest(
        sleekflowCompanyId,
        agentConfigIds);

      var response = await _requestClient.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        request,
        CancellationToken.None,
        timeout: TimeSpan.FromSeconds(30));

      return response.Message.WorkflowCounts;
    }
    catch (Exception ex)
    {
      _logger.LogError(
        ex,
        "Failed to retrieve workflow counts for company {SleekflowCompanyId} and agent configs {AgentConfigIds}",
        sleekflowCompanyId,
        string.Join(", ", agentConfigIds));

      // Return zeros for all agent configs if there's an error
      // This ensures the API doesn't fail completely if FlowHub is unavailable
      return result;
    }
  }
} 