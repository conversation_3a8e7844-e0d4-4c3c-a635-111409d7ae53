﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.CrmHubConfigs;

public class UsageLimitOffset
{
    public const string PropertyNameCustomObjectMaximumSchemaNumOffset = "custom_object_maximum_schema_num_offset";
    public const string PropertyNameCustomObjectMaximumPropertyNumPerSchemaOffset = "custom_object_maximum_property_num_per_schema_offset";
    public const string PropertyNameCustomObjectMaximumSchemafulObjectNumPerSchemaOffset = "custom_object_maximum_schemaful_object_num_per_schema_offset";
    public const string PropertyNameCustomObjectMaximumSchemafulObjectNumPerCompanyOffset = "custom_object_maximum_schemaful_object_num_per_company_offset";
    public const string PropertyNameCustomObjectMaximumArrayObjectArraySizeOffset = "custom_object_maximum_array_object_array_size_offset";

    [JsonProperty(PropertyNameCustomObjectMaximumSchemaNumOffset)]
    public int? CustomObjectMaximumSchemaNumOffset { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumPropertyNumPerSchemaOffset)]
    public int? CustomObjectMaximumPropertyNumPerSchemaOffset { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumSchemafulObjectNumPerSchemaOffset)]
    public int? CustomObjectMaximumSchemafulObjectNumPerSchemaOffset { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumSchemafulObjectNumPerCompanyOffset)]
    public int? CustomObjectMaximumSchemafulObjectNumPerCompanyOffset { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumArrayObjectArraySizeOffset)]
    public int? CustomObjectMaximumArrayObjectArraySizeOffset { get; set; }

    [JsonConstructor]
    public UsageLimitOffset(
        int customObjectMaximumSchemaNumOffset,
        int customObjectMaximumPropertyNumPerSchemaOffset,
        int customObjectMaximumSchemafulObjectNumPerSchemaOffset,
        int customObjectMaximumSchemafulObjectNumPerCompanyOffset,
        int customObjectMaximumArrayObjectArraySizeOffset)
    {
        CustomObjectMaximumSchemaNumOffset = customObjectMaximumSchemaNumOffset;
        CustomObjectMaximumPropertyNumPerSchemaOffset = customObjectMaximumPropertyNumPerSchemaOffset;
        CustomObjectMaximumSchemafulObjectNumPerSchemaOffset = customObjectMaximumSchemafulObjectNumPerSchemaOffset;
        CustomObjectMaximumSchemafulObjectNumPerCompanyOffset = customObjectMaximumSchemafulObjectNumPerCompanyOffset;
        CustomObjectMaximumArrayObjectArraySizeOffset = customObjectMaximumArrayObjectArraySizeOffset;
    }

    public static UsageLimitOffset Default()
    {
        return new UsageLimitOffset(
            customObjectMaximumSchemaNumOffset: 0,
            customObjectMaximumPropertyNumPerSchemaOffset: 0,
            customObjectMaximumSchemafulObjectNumPerSchemaOffset: 0,
            customObjectMaximumSchemafulObjectNumPerCompanyOffset: 0,
            customObjectMaximumArrayObjectArraySizeOffset: 0);
    }
}