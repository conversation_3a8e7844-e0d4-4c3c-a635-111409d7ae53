﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class SearchObjectsOutput
{
    [JsonProperty("objects")]
    [Required]
    public List<Dictionary<string, object?>> Objects { get; set; }

    [JsonConstructor]
    public SearchObjectsOutput(
        List<Dictionary<string, object?>> objects)
    {
        Objects = objects;
    }
}