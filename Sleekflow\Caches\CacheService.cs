﻿﻿﻿﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.JsonConfigs;
using Sleekflow.Locks;
using StackExchange.Redis;

namespace Sleekflow.Caches;

public interface ICacheService
{
    Task<T> CacheAsync<T>(string key, Func<Task<T>> func, CancellationToken cancellationToken = default);

    Task<T> CacheAsync<T>(
        string key,
        Func<Task<T>> func,
        TimeSpan? timeSpan,
        CancellationToken cancellationToken = default);

    Task<T> UpsertAsync<T>(
        string key,
        Func<Task<T>> func,
        TimeSpan? timeSpan,
        CancellationToken cancellationToken = default);

    Task RemoveCacheAsync(string key, CancellationToken cancellationToken = default);
}

public class CacheService : ICacheService
{
    private readonly ILogger<CacheService> _logger;
    private readonly ICacheConfig _cacheConfig;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILockService _lockService;

    public CacheService(
        ILogger<CacheService> logger,
        ICacheConfig cacheConfig,
        IConnectionMultiplexer connectionMultiplexer,
        ILockService lockService)
    {
        _logger = logger;
        _cacheConfig = cacheConfig;
        _connectionMultiplexer = connectionMultiplexer;
        _lockService = lockService;
    }

    public Task<T> CacheAsync<T>(string key, Func<Task<T>> func, CancellationToken cancellationToken = default)
    {
        return CacheAsync(key, func, TimeSpan.FromSeconds(3600), cancellationToken);
    }

    public async Task<T> CacheAsync<T>(
        string key,
        Func<Task<T>> func,
        TimeSpan? timeSpan,
        CancellationToken cancellationToken = default)
    {
        var database = _connectionMultiplexer.GetDatabase();

        var cachedStringValue = await GetCachedStringValueAsync(database, key);

        if (!string.IsNullOrWhiteSpace(cachedStringValue))
        {
            var cachedValue = JsonConvert.DeserializeObject<T>(
                cachedStringValue,
                JsonConfig.DefaultJsonSerializerSettings);

            if (cachedValue is not null)
            {
                return cachedValue;
            }
        }

        Lock? @lock = null;

        try
        {
            @lock = await _lockService.WaitUnitLockAsync(
                new string[]
                {
                    key
                },
                minimumLockDuration: TimeSpan.FromSeconds(10),
                TimeSpan.FromSeconds(20),
                cancellationToken);

            var secondAttemptCachedStringValue = await GetCachedStringValueAsync(database, key);

            // Try to get again
            if (!string.IsNullOrWhiteSpace(secondAttemptCachedStringValue))
            {
                var cachedValue = JsonConvert.DeserializeObject<T>(
                    secondAttemptCachedStringValue,
                    JsonConfig.DefaultJsonSerializerSettings);

                if (cachedValue is not null)
                {
                    return cachedValue;
                }
            }

            var valueToCache = await func.Invoke();

            await SetCacheValueAsync(database, key, valueToCache, timeSpan);

            return valueToCache;
        }
        catch (TimeoutException ex) when (ex.Message == "Unable to acquire a lock.")
        {
            _logger.LogWarning(
                ex,
                "Unable to obtain the lock for key {Key}",
                key);

            var valueToCache = await func.Invoke();

            await SetCacheValueAsync(database, key, valueToCache, timeSpan);

            return valueToCache;
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock, cancellationToken);
            }
        }
    }

    public async Task<T> UpsertAsync<T>(
        string key,
        Func<Task<T>> func,
        TimeSpan? timeSpan,
        CancellationToken cancellationToken = default)
    {
        var database = _connectionMultiplexer.GetDatabase();

        Lock? @lock = null;

        try
        {
            @lock = await _lockService.WaitUnitLockAsync(
                new string[] { key },
                minimumLockDuration: TimeSpan.FromSeconds(10),
                TimeSpan.FromSeconds(20),
                cancellationToken);

            var valueToCache = await func.Invoke();

            try
            {
                await database.StringSetAsync(
                    $"{_cacheConfig.CachePrefix}-{key}",
                    JsonConvert.SerializeObject(valueToCache, JsonConfig.DefaultJsonSerializerSettings),
                    timeSpan,
                    When.Always);
            }
            catch (Exception e)
            {
                _logger.LogWarning(e, "Caught an exception when upserting the cache, key {Key}", key);
            }

            return valueToCache;
        }
        catch (TimeoutException ex) when (ex.Message == "Unable to acquire a lock.")
        {
            _logger.LogWarning(
                ex,
                "Unable to obtain the lock for key {Key}",
                key);

            var valueToCache = await func.Invoke();

            try
            {
                await database.StringSetAsync(
                    $"{_cacheConfig.CachePrefix}-{key}",
                    JsonConvert.SerializeObject(valueToCache, JsonConfig.DefaultJsonSerializerSettings),
                    timeSpan,
                    When.Always);
            }
            catch (Exception e)
            {
                _logger.LogWarning(e, "Caught an exception when upserting the cache, key {Key}", key);
            }

            return valueToCache;
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock, cancellationToken);
            }
        }
    }

    private async Task<string> GetCachedStringValueAsync(
        IDatabaseAsync database,
        string key)
    {
        try
        {
            var redisValue = await database.StringGetAsync($"{_cacheConfig.CachePrefix}-{key}");

            if (redisValue.HasValue)
            {
                return redisValue.ToString();
            }
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Caught an exception when getting the cache, key {Key}", key);
        }

        return string.Empty;
    }

    private async Task SetCacheValueAsync<T>(
        IDatabaseAsync database,
        string key,
        T cacheValue,
        TimeSpan? timeSpan)
    {
        try
        {
            await database.StringSetAsync(
                $"{_cacheConfig.CachePrefix}-{key}",
                JsonConvert.SerializeObject(cacheValue, JsonConfig.DefaultJsonSerializerSettings),
                timeSpan ?? TimeSpan.FromSeconds(3600),
                When.NotExists);
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Caught an exception when saving the cache, key {Key}", key);
        }
    }

    public async Task RemoveCacheAsync(string key, CancellationToken cancellationToken = default)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();

            await database.KeyDeleteAsync($"{_cacheConfig.CachePrefix}-{key}");
        }
        catch (Exception e)
        {
            _logger.LogWarning(e, "Caught an exception when removing the cache, key {Key}", key);
        }
    }
}
