<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.WebhookHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.WebhookHub/bin/Debug/net8.0/Sleekflow.WebhookHub.dll" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.WebhookHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="COSMOS_KEY" value="ndFR7aAc62GfCd6Mm1RHJzd2gbAHLc2XMs0SvtrxIm6HOM48MDBgjZ34GHZxl9TswQrFnWRNQRDcGzHij4xrVQ==" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7082;http://localhost:7083" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="CACHE_PREFIX" value="Sleekflow.WebhookHub" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
      <env name="SF_CORE_INTERNALS_WEBHOOK_KEY" value="O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.WebhookHub/Sleekflow.WebhookHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>