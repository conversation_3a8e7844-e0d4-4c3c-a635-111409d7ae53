using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class StaffRemovedFromTeamSystemLogData
{
    [JsonProperty("staff_id")]
    public string StaffId { get; set; }

    [JsonProperty("team_ids")]
    public List<string> TeamIds { get; set; }

    [JsonProperty("team_names")]
    public List<string> TeamNames { get; set; }

    [JsonConstructor]
    public StaffRemovedFromTeamSystemLogData(string staffId, List<string> teamIds, List<string> teamNames)
    {
        StaffId = staffId;
        TeamIds = teamIds;
        TeamNames = teamNames;
    }
}