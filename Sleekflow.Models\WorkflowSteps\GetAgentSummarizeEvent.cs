using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Events;

namespace Sleekflow.Models.WorkflowSteps;

public class GetAgentSummarizeEvent : AgentEventBase
{
    public string SleekflowCompanyId { get; set; }

    public string? AgentId { get; set; }

    public List<SfChatEntry> ConversationContext { get; set; }

    public string HandoverReason { get; set; }

    public Dictionary<string, string>? ContactProperties { get; set; }

    public string? LanguageIsoCode { get; set; }

    [JsonConstructor]
    public GetAgentSummarizeEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string? agentId,
        List<SfChatEntry> conversationContext,
        string handoverReason,
        Dictionary<string, string>? contactProperties = null,
        string? languageIsoCode = null)
        : base(aggregateStepId, proxyStateId, stackEntries)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        AgentId = agentId;
        ConversationContext = conversationContext;
        HandoverReason = handoverReason;
        ContactProperties = contactProperties;
        LanguageIsoCode = languageIsoCode;
    }

    public class Response
    {
        [JsonProperty("context")]
        public string Context { get; set; }

        [JsonProperty("handover_reason")]
        public string HandoverReason { get; set; }

        public Response(string context, string handoverReason)
        {
            Context = context;
            HandoverReason = handoverReason;
        }
    }
}