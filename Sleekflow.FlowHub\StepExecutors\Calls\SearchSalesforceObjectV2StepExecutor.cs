﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISearchSalesforceObjectV2StepExecutor : IStepExecutor
{
}

public class SearchSalesforceObjectV2StepExecutor
    : GeneralStepExecutor<CallStep<SearchSalesforceObjectV2StepArgs>>,
        ISearchSalesforceObjectV2StepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public SearchSalesforceObjectV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var searchSalesforceObjectV2Input = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                new SearchSalesforceObjectV2Request(
                    step.Id,
                    state.Id,
                    stackEntries,
                    searchSalesforceObjectV2Input.StateIdentity.SleekflowCompanyId,
                    searchSalesforceObjectV2Input.ConnectionId,
                    searchSalesforceObjectV2Input.EntityTypeName,
                    searchSalesforceObjectV2Input.Conditions));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnSalesforceFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("Salesforce search object V2 operation timed out after 5 minutes")),
                typeof(OnSalesforceFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class SearchSalesforceObjectV2Input
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("conditions")]
        [Required]
        [Validations.ValidateArray]
        public List<SearchObjectCondition> Conditions { get; set; }

        [JsonConstructor]
        public SearchSalesforceObjectV2Input(
            string stateId,
            StateIdentity stateIdentity,
            string connectionId,
            string entityTypeName,
            List<SearchObjectCondition> conditions)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            Conditions = conditions;
        }
    }

    private async Task<SearchSalesforceObjectV2Input> GetArgs(
        CallStep<SearchSalesforceObjectV2StepArgs> callStep,
        ProxyState state)
    {
        var connectionId = callStep.Args.ConnectionId;
        var entityTypeName = callStep.Args.EntityTypeName;

        var convertedConditions = new List<SearchObjectCondition>();
        var conditions = callStep.Args.Conditions;
        foreach (var condition in conditions)
        {
            object? value = null;
            if (condition.ValueExpr != null)
            {
                value = await _stateEvaluator.EvaluateExpressionAsync(state, condition.ValueExpr);
            }

            convertedConditions.Add(new SearchObjectCondition(condition.FieldName, condition.SearchOperator, value));
        }

        return new SearchSalesforceObjectV2Input(
            state.Id,
            state.Identity,
            connectionId,
            entityTypeName,
            convertedConditions);
    }
}