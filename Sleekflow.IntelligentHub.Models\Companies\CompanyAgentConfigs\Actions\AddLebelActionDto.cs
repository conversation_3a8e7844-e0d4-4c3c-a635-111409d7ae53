using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class AddLabelActionDto : BaseActionDto
{
    [JsonProperty("labels")]
    public List<LabelDto> Labels { get; set; }

    [JsonProperty("instructions")]
    public string Instructions { get; set; }

    [JsonConstructor]
    public AddLabelActionDto(bool enabled, List<LabelDto> labels, string instructions)
        : base(enabled)
    {
        Labels = labels;
        Instructions = instructions;
    }

    public AddLabelActionDto(AddLabelAction action)
        : base(action)
    {
        Labels = action.Labels.Select(l => l.ToDto()).ToList();
        Instructions = action.Instructions;
    }
}