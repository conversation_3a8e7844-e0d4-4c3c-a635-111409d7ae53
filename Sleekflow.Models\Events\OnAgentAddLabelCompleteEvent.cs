using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.Events;

public class OnAgentAddLabelCompleteEvent
{
    public string AggregateStepId { get; set; }

    public string ProxyStateId { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public string? AggregateStateContext { get; set; }

    public string? StepExecutionStatus { get; set; }

    public OnAgentAddLabelCompleteEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string? aggregateStateContext,
        string? stepExecutionStatus = null
       )
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        AggregateStateContext = aggregateStateContext;
        StepExecutionStatus = stepExecutionStatus;
    }
}