using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.CommerceHub.Payments.Stripe;
using Sleekflow.CommerceHub.ShortenLink;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Stripe;

namespace Sleekflow.CommerceHub.Stripe.Connect;

public interface IStripeConnectService
{
    Task<PaymentProviderConfig> CreateStripeConnectPaymentProviderConfigAsync(
        string sleekflowCompanyId,
        string platformCountry,
        decimal applicationFeeRate,
        bool isShippingEnabled,
        List<string>? shippingAllowedCountryIsoCodes,
        bool isInventoryEnabled,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    Task<string> GenerateStripeConnectOnboardingLinkAsync(
        string platformCountry,
        string stripeAccountId);
}

public class StripeConnectService : IStripeConnectService, IScopedService
{
    private readonly IStripeSecretConfig _stripeSecretConfig;
    private readonly IStripePaymentProviderExternalConfigResolver _stripePaymentProviderExternalConfigResolver;
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;
    private readonly IIdService _idService;
    private readonly IShortenLinkService _shortenLinkService;

    public StripeConnectService(
        IStripeSecretConfig stripeSecretConfig,
        IStripePaymentProviderExternalConfigResolver stripePaymentProviderExternalConfigResolver,
        IPaymentProviderConfigService paymentProviderConfigService,
        IIdService idService,
        IShortenLinkService shortenLinkService)
    {
        _stripeSecretConfig = stripeSecretConfig;
        _stripePaymentProviderExternalConfigResolver = stripePaymentProviderExternalConfigResolver;
        _paymentProviderConfigService = paymentProviderConfigService;
        _idService = idService;
        _shortenLinkService = shortenLinkService;
    }

    public async Task<PaymentProviderConfig> CreateStripeConnectPaymentProviderConfigAsync(
        string sleekflowCompanyId,
        string platformCountry,
        decimal applicationFeeRate,
        bool isShippingEnabled,
        List<string>? shippingAllowedCountryIsoCodes,
        bool isInventoryEnabled,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            sleekflowStaffId,
            sleekflowStaffTeamIds);

        var stripePaymentProviderExternalConfig = await _stripePaymentProviderExternalConfigResolver.ResolveAsync(
            GetApiKey(platformCountry),
            GetConnectWebhookSecret(platformCountry),
            GetPaymentWebhookSecret(platformCountry),
            applicationFeeRate,
            isShippingEnabled,
            shippingAllowedCountryIsoCodes,
            isInventoryEnabled);

        return await _paymentProviderConfigService.CreatePaymentProviderConfigAsync(
            new PaymentProviderConfig(
                _idService.GetId(SysTypeNames.PaymentProviderConfig),
                sleekflowCompanyId,
                PaymentProviderConfigStatuses.Pending,
                new List<string>(),
                new Dictionary<string, List<string>>(),
                new List<string>
                {
                    stripePaymentProviderExternalConfig.StripeAccount.DefaultCurrency
                },
                stripePaymentProviderExternalConfig,
                new List<string>
                {
                    "Active"
                },
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowStaff,
                sleekflowStaff),
            sleekflowStaff);
    }

    public async Task<string> GenerateStripeConnectOnboardingLinkAsync(
        string platformCountry,
        string stripeAccountId)
    {
        StripeConfiguration.ApiKey = GetApiKey(platformCountry);

        var accountLinkCreateOptions = new AccountLinkCreateOptions
        {
            Account = stripeAccountId,
            RefreshUrl =
                $"{Environment.GetEnvironmentVariable("STRIPE_ONBOARDING_REFRESH_DOMAIN")}/StripeConnectOnboardingLinkRefresh/refresh?platformCountry={platformCountry}&stripeAccountId={stripeAccountId}",
            ReturnUrl =
                $"{Environment.GetEnvironmentVariable("STRIPE_ONBOARDING_REDIRECT_DOMAIN")}/settings/paymentlink",
            Type = "account_onboarding",
        };
        var accountLinkService = new AccountLinkService();
        var accountLink = await accountLinkService.CreateAsync(accountLinkCreateOptions);

        var paymentProviderConfig =
            await _paymentProviderConfigService.GetPaymentProviderConfigByStripeAccountIdAsync(stripeAccountId);
        if (paymentProviderConfig.Status == "Registered")
        {
            throw new Exception($"Stripe Account {stripeAccountId} is already registered");
        }

        return await _shortenLinkService.GetShortenedUrl(
            accountLink.Url,
            paymentProviderConfig.SleekflowCompanyId,
            "Stripe Connect Onboarding Link");
    }

    private string GetApiKey(string platformCountry) => platformCountry switch
    {
        "HK" => _stripeSecretConfig.StripeApiKeyHk,
        "SG" => _stripeSecretConfig.StripeApiKeyHk,
        "MY" => _stripeSecretConfig.StripeApiKeyHk,
        "GB" => _stripeSecretConfig.StripeApiKeyHk,
        _ => throw new NotImplementedException(),
    };

    private string GetConnectWebhookSecret(string platformCountry) => platformCountry switch
    {
        "HK" => _stripeSecretConfig.StripeConnectWebhookSecretHk,
        "SG" => _stripeSecretConfig.StripeConnectWebhookSecretSg,
        "MY" => _stripeSecretConfig.StripeConnectWebhookSecretMy,
        "GB" => _stripeSecretConfig.StripeConnectWebhookSecretGb,
        _ => throw new NotImplementedException(),
    };

    private string GetPaymentWebhookSecret(string platformCountry) => platformCountry switch
    {
        "HK" => _stripeSecretConfig.StripePaymentWebhookSecretHk,
        "SG" => _stripeSecretConfig.StripePaymentWebhookSecretSg,
        "MY" => _stripeSecretConfig.StripePaymentWebhookSecretMy,
        "GB" => _stripeSecretConfig.StripePaymentWebhookSecretGb,
        _ => throw new NotImplementedException(),
    };
}