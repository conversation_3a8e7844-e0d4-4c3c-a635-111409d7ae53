using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetSubscriptions : ITrigger
{
    private readonly ISalesforceSubscriptionService _salesforceConnectionService;

    public GetSubscriptions(
        ISalesforceSubscriptionService salesforceConnectionService)
    {
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class GetSubscriptionsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public GetSubscriptionsInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
        }
    }

    public class SalesforceSubscriptionDto : IHasSleekflowCompanyId
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [JsonProperty("interval")]
        public int Interval { get; set; }

        [JsonProperty("is_flows_based")]
        public bool? IsFlowsBased { get; set; }

        [JsonProperty("connection_id")]
        public string? ConnectionId { get; set; }

        [JsonConstructor]
        public SalesforceSubscriptionDto(
            SalesforceSubscription subscription)
        {
            Id = subscription.Id;
            SleekflowCompanyId = subscription.SleekflowCompanyId;
            EntityTypeName = subscription.EntityTypeName;
            Interval = subscription.Interval;
            IsFlowsBased = subscription.IsFlowsBased;
            ConnectionId = subscription.ConnectionId;
        }
    }

    public class GetSubscriptionsOutput
    {
        [JsonProperty("subscriptions")]
        [Required]
        public List<SalesforceSubscriptionDto> Subscriptions { get; set; }

        [JsonConstructor]
        public GetSubscriptionsOutput(
            List<SalesforceSubscriptionDto> subscriptions)
        {
            Subscriptions = subscriptions;
        }
    }

    public async Task<GetSubscriptionsOutput> F(
        GetSubscriptionsInput getSubscriptionsInput)
    {
        var subscriptions =
            await _salesforceConnectionService.GetSubscriptionsAsync(
                getSubscriptionsInput.SleekflowCompanyId,
                getSubscriptionsInput.ConnectionId,
                getSubscriptionsInput.EntityTypeName);

        return new GetSubscriptionsOutput(subscriptions
            .Select(o => new SalesforceSubscriptionDto(o))
            .ToList());
    }
}