using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Configs;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Disposable.Authentications;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.EmailHub.Disposable.Authentications;

public interface IDisposableAuthenticationService : IEmailAuthenticationService
{
}

public class DisposableAuthenticationService : IScopedService, IDisposableAuthenticationService
{
    private readonly IDisposableConfig _disposableConfig;
    private readonly IIdService _idService;
    private readonly IEmailAuthenticationRepository _emailAuthenticationRepository;

    public DisposableAuthenticationService(
        IHttpClientFactory httpClientFactory,
        IDisposableConfig disposableConfig,
        IIdService idService,
        IEmailAuthenticationRepository emailAuthenticationRepository)
    {
        httpClientFactory.CreateClient("default-handler");
        _disposableConfig = disposableConfig;
        _idService = idService;
        _emailAuthenticationRepository = emailAuthenticationRepository;
    }

    public async Task<EmailAuthentication> GetAuthenticationAsync(
        string sleekflowCompanyId,
        string emailAddress,
        string? serverType = null,
        CancellationToken cancellationToken = default)
    {
        var authentication = (await _emailAuthenticationRepository
                .GetObjectsAsync(x =>
                        x.SleekflowCompanyId == sleekflowCompanyId &&
                        x.EmailAddress == emailAddress &&
                        x.EmailAuthenticationMetadata.ProviderName == ProviderNames.Disposable,
                    cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        return authentication;
    }

    public async Task<string> AuthenticateAsync(
        string sleekflowCompanyId,

#pragma warning disable SA1313
#pragma warning disable S927
        string _,
        // This is intentionally unused
        // Disable warning for unused parameter
#pragma warning restore S927
#pragma warning restore SA1313
        Dictionary<string, string>? extendedAuthMetadata,
        CancellationToken cancellationToken = default
        )
    {
        var localPart = _idService.GetId("DisposableLocalPart");
        var emailAddress = $"{localPart}@support.sleekflow.io";

        string authenticationId;

        try
        {
            authenticationId = (await GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken)).Id;
        }
        catch (SfUnauthorizedException)
        {
            authenticationId = _idService.GetId("EmailAuthentication");
        }

        var emailAuthentication = new EmailAuthentication(
            authenticationId,
            sleekflowCompanyId,
            emailAddress,
            true,
            new DisposableAuthenticationMetadata(
                _disposableConfig.ServerName,
                _disposableConfig.Port,
                _disposableConfig.Username,
                _disposableConfig.Password),
            ProviderNames.Disposable);
        await _emailAuthenticationRepository.UpsertAsync(emailAuthentication, emailAuthentication.Id, cancellationToken: cancellationToken);

        return emailAuthentication.EmailAddress;
    }
}