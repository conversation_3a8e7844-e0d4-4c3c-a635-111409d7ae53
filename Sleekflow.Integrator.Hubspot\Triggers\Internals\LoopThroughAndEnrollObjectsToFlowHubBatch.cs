﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Services;
using Sleekflow.Models.TriggerEvents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Internals;

[TriggerGroup("Internals")]
public class LoopThroughAndEnrollObjectsToFlowHubBatch : ITrigger
{
    private readonly ILogger<LoopThroughAndEnrollObjectsToFlowHubBatch> _logger;
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IBus _bus;

    public LoopThroughAndEnrollObjectsToFlowHubBatch(
        ILogger<LoopThroughAndEnrollObjectsToFlowHubBatch> logger,
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotConnectionService hubspotConnectionService,
        IBus bus)
    {
        _logger = logger;
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotConnectionService = hubspotConnectionService;
        _bus = bus;
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            bool isCustomObject,
            string? after,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            IsCustomObject = isCustomObject;
            After = after;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class LoopThroughAndEnrollObjectsToFlowHubBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("after")]
        public string? After { get; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubBatchOutput(long count, string? after)
        {
            Count = count;
            After = after;
        }
    }

    public async Task<LoopThroughAndEnrollObjectsToFlowHubBatchOutput> F(
        LoopThroughAndEnrollObjectsToFlowHubBatchInput loopThroughAndEnrollObjectsToFlowHubBatchInput)
    {
        var sleekflowCompanyId = loopThroughAndEnrollObjectsToFlowHubBatchInput.SleekflowCompanyId;
        var connectionId = loopThroughAndEnrollObjectsToFlowHubBatchInput.ConnectionId;
        var entityTypeName = loopThroughAndEnrollObjectsToFlowHubBatchInput.EntityTypeName;
        var isCustomObject = loopThroughAndEnrollObjectsToFlowHubBatchInput.IsCustomObject;
        var flowHubWorkflowId = loopThroughAndEnrollObjectsToFlowHubBatchInput.FlowHubWorkflowId;
        var flowHubWorkflowVersionedId = loopThroughAndEnrollObjectsToFlowHubBatchInput.FlowHubWorkflowVersionedId;

        var connection = await _hubspotConnectionService.GetByIdAsync(connectionId, sleekflowCompanyId);

        var authentication =
            await _hubspotAuthenticationService.GetAsync(connection.AuthenticationId, sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Start LoopThroughAndEnrollObjectsToFlowHubBatch: " +
            "sleekflowCompanyId {SleekflowCompanyId}, connectionId {ConnectionId} entityTypeName {EntityTypeName}, after {After}",
            sleekflowCompanyId,
            connectionId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.After);

        var (objects, after) = await _hubspotObjectService.GetObjectsAsync(
            authentication,
            100,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.After,
            entityTypeName,
            new List<SyncConfigFilterGroup>(),
            null);

        _logger.LogInformation(
            "End LoopThroughAndEnrollObjectsToFlowHubBatch: sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}, prevAfter {PrevAfter}, nextAfter {NextAfter}, count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.After,
            after,
            objects.Count);

        var events = objects
            .Select(
                dict =>
                {
                    var onHubspotObjectEnrollmentToFlowHubRequestedEvent = new OnHubspotObjectEnrollmentToFlowHubRequestedEvent(
                        DateTimeOffset.UtcNow,
                        sleekflowCompanyId,
                        connectionId,
                        (string) dict["Id"]!,
                        entityTypeName,
                        isCustomObject,
                        dict,
                        flowHubWorkflowId,
                        flowHubWorkflowVersionedId);

                    return onHubspotObjectEnrollmentToFlowHubRequestedEvent;
                })
            .ToList();

        var eventChunks = events.Chunk(100).ToList();
        var totalChunks = eventChunks.Count;

        for (var i = 0; i < totalChunks; i++)
        {
            await _bus.PublishBatch(
                eventChunks[i],
                context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });

            if (i < totalChunks - 1)
            {
                await Task.Delay(TimeSpan.FromSeconds(1));
            }
        }

        _logger.LogInformation(
            "Flushed LoopThroughAndEnrollObjectsToFlowHubBatch: sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}, prevAfter {PrevAfter}, nextAfter {NextAfter}, count {Count}",
            sleekflowCompanyId,
            entityTypeName,
            loopThroughAndEnrollObjectsToFlowHubBatchInput.After,
            after,
            objects.Count);

        return new LoopThroughAndEnrollObjectsToFlowHubBatchOutput(objects.Count, after);
    }
}