﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Locks;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.CrmHubConfigs;

public interface ICrmHubConfigService
{
    public Task<CrmHubConfig> GetCrmHubConfigAsync(string sleekflowCompanyId);

    public Task<CrmHubConfig> InitializeCrmHubConfigAsync(
        string sleekflowCompanyId,
        UsageLimit usageLimit,
        FeatureAccessibilitySettings? featureAccessibilitySettings,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    public Task<CrmHubConfig> UpdateCrmHubConfigAsync(
        string id,
        string sleekflowCompanyId,
        UsageLimit usageLimit,
        FeatureAccessibilitySettings? featureAccessibilitySettings,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    public Task<CrmHubConfig> UpdateCrmHubConfigUsageLimitOffsetAsync(
        string id,
        string sleekflowCompanyId,
        UsageLimitOffset usageLimitOffset,
        AuditEntity.SleekflowStaff? sleekflowStaff);
}

public class CrmHubConfigService : ICrmHubConfigService, IScopedService
{
    private readonly IIdService _idService;
    private readonly ILockService _lockService;
    private readonly ICrmHubConfigRepository _crmHubConfigRepository;

    public CrmHubConfigService(
        IIdService idService,
        ICrmHubConfigRepository crmHubConfigRepository,
        ILockService lockService)
    {
        _idService = idService;
        _crmHubConfigRepository = crmHubConfigRepository;
        _lockService = lockService;
    }

    public async Task<CrmHubConfig> GetCrmHubConfigAsync(string sleekflowCompanyId)
    {
        var crmHubConfig =
            (await _crmHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId))
            .FirstOrDefault();

        if (crmHubConfig != null)
        {
            return crmHubConfig;
        }

        return new CrmHubConfig(
            "NOT_INITIALIZED",
            new UsageLimit(0, 0, 0, 0, 0),
            UsageLimitOffset.Default(),
            FeatureAccessibilitySettings.Default(),
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            sleekflowCompanyId,
            null,
            null);
    }

    public async Task<CrmHubConfig> InitializeCrmHubConfigAsync(
        string sleekflowCompanyId,
        UsageLimit usageLimit,
        FeatureAccessibilitySettings? featureAccessibilitySettings,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(CrmHubConfigService),
                nameof(InitializeCrmHubConfigAsync),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(20),
            TimeSpan.Zero);

        try
        {
            var crmHubConfigs =
                await _crmHubConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

            if (crmHubConfigs.Any())
            {
                throw new SfUserFriendlyException("CrmHubConfig already existed.");
            }

            return await _crmHubConfigRepository.CreateAndGetAsync(
                new CrmHubConfig(
                    _idService.GetId(SysTypeNames.CrmHubConfig),
                    usageLimit,
                    UsageLimitOffset.Default(),
                    featureAccessibilitySettings,
                    null,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    sleekflowCompanyId,
                    sleekflowStaff,
                    null),
                sleekflowCompanyId);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<CrmHubConfig> UpdateCrmHubConfigAsync(
        string id,
        string sleekflowCompanyId,
        UsageLimit usageLimit,
        FeatureAccessibilitySettings? featureAccessibilitySettings,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var crmHubConfig = await _crmHubConfigRepository.GetAsync(id, sleekflowCompanyId);

        return await _crmHubConfigRepository.PatchAndGetAsync(
            crmHubConfig.Id,
            crmHubConfig.SleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set($"/{CrmHubConfig.PropertyNameUsageLimit}", usageLimit),
                PatchOperation.Set($"/{CrmHubConfig.PropertyNameFeatureAccessibilitySettings}", featureAccessibilitySettings),
                PatchOperation.Set($"/{AuditEntity.PropertyNameUpdatedBy}", sleekflowStaff),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
            },
            crmHubConfig.ETag);
    }

    public async Task<CrmHubConfig> UpdateCrmHubConfigUsageLimitOffsetAsync(
        string id,
        string sleekflowCompanyId,
        UsageLimitOffset usageLimitOffset,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var crmHubConfig = await _crmHubConfigRepository.GetAsync(id, sleekflowCompanyId);

        return await _crmHubConfigRepository.PatchAndGetAsync(
            crmHubConfig.Id,
            crmHubConfig.SleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set($"/{CrmHubConfig.PropertyNameUsageLimitOffset}", usageLimitOffset),
                PatchOperation.Set($"/{AuditEntity.PropertyNameUpdatedBy}", sleekflowStaff),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
            },
            crmHubConfig.ETag);
    }
}