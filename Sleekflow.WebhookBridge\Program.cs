using Serilog;
using Sleekflow;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;

const string appName = "SleekflowWebhookBridge";

MvcModules.BuildLogger(appName);

try
{
    Log.Information("Starting web host");

    var builder = WebApplication.CreateBuilder(args);
    builder.Host.UseSerilog();
    builder.Services.AddHttpContextAccessor();

    MvcModules.BuildHealthCheck(builder.Services);
    MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
    MvcModules.BuildApiBehaviors(builder);
    Modules.BuildConfigs(builder.Services);
    Modules.BuildHttpClients(builder.Services);
    Modules.BuildServices(builder.Services);

    Modules.BuildCacheServices(builder.Services);
    Modules.BuildDbServices(builder.Services);
    Modules.BuildServiceBusServices(builder.Services);

    var app = builder.Build();

    // app.UseHttpsRedirection();
    app.UseAuthorization();
    app.MapControllers();
    HealthCheckMapping.MapHealthChecks(app);

    ThreadPool.SetMinThreads(128, 128);
    ThreadPool.SetMaxThreads(512, 512);

    app.Run();

    return 0;
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");
    return 1;
}
finally
{
    Log.CloseAndFlush();
}




































