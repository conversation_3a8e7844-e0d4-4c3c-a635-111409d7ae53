﻿using Sleekflow.CrmHub.Configs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers.States;

namespace Sleekflow.CrmHub.Providers;

public class ZohoIntegratorService : GenericProviderService
{
    public const string ProviderName = "zoho-integrator";

    public ZohoIntegratorService(
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        IProviderStateService providerStateService,
        ILoopThroughObjectsProgressStateService loopThroughObjectsProgressStateService,
        ICustomSyncConfigService customSyncConfigService,
        ILogger<ZohoIntegratorService> logger)
        : base(
            providerStateService,
            loopThroughObjectsProgressStateService,
            httpClientFactory.CreateClient("default-handler"),
            customSyncConfigService,
            ProviderName,
            appConfig.ZohoIntegratorEndpoint,
            logger)
    {
    }
}