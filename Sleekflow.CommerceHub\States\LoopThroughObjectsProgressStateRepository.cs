﻿using Sleekflow.CommerceHub.Models.States.LoopThroughObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.States;

public interface ILoopThroughObjectsProgressStateRepository : IRepository<LoopThroughObjectsProgressState>
{
}

public class LoopThroughObjectsProgressStateRepository
    : BaseRepository<LoopThroughObjectsProgressState>,
        ILoopThroughObjectsProgressStateRepository,
        ISingletonService
{
    public LoopThroughObjectsProgressStateRepository(
        ILogger<LoopThroughObjectsProgressStateRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}