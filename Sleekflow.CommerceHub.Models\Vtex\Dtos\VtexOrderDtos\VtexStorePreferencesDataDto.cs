﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

public record VtexStorePreferencesDataDto
{
    [JsonProperty("countryCode")]
    public string CountryCode { get; set; }

    [JsonProperty("currencyCode")]
    public string CurrencyCode { get; set; }

    [JsonProperty("currencyFormatInfo")]
    public VtexCurrencyFormatInfoDto CurrencyFormatInfo { get; set; }

    [JsonProperty("currencyLocale")]
    public int CurrencyLocale { get; set; }

    [JsonProperty("currencySymbol")]
    public string CurrencySymbol { get; set; }

    [JsonProperty("timeZone")]
    public string TimeZone { get; set; }

    [JsonConstructor]
    public VtexStorePreferencesDataDto(
        string countryCode,
        string currencyCode,
        VtexCurrencyFormatInfoDto currencyFormatInfo,
        int currencyLocale,
        string currencySymbol,
        string timeZone)
    {
        CountryCode = countryCode;
        CurrencyCode = currencyCode;
        CurrencyFormatInfo = currencyFormatInfo;
        CurrencyLocale = currencyLocale;
        CurrencySymbol = currencySymbol;
        TimeZone = timeZone;
    }
}

public record VtexCurrencyFormatInfoDto
{
    [JsonProperty("CurrencyDecimalDigits")]
    public int CurrencyDecimalDigits { get; set; }

    [JsonProperty("CurrencyDecimalSeparator")]
    public string CurrencyDecimalSeparator { get; set; }

    [JsonProperty("CurrencyGroupSeparator")]
    public string CurrencyGroupSeparator { get; set; }

    [JsonProperty("CurrencyGroupSize")]
    public int CurrencyGroupSize { get; set; }

    [JsonProperty("StartsWithCurrencySymbol")]
    public bool StartsWithCurrencySymbol { get; set; }

    [JsonConstructor]
    public VtexCurrencyFormatInfoDto(
        int currencyDecimalDigits,
        string currencyDecimalSeparator,
        string currencyGroupSeparator,
        int currencyGroupSize,
        bool startsWithCurrencySymbol)
    {
        CurrencyDecimalDigits = currencyDecimalDigits;
        CurrencyDecimalSeparator = currencyDecimalSeparator;
        CurrencyGroupSeparator = currencyGroupSeparator;
        CurrencyGroupSize = currencyGroupSize;
        StartsWithCurrencySymbol = startsWithCurrencySymbol;
    }
}