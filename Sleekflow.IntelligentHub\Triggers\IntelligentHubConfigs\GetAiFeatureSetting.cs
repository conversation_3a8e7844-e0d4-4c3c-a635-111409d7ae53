using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class GetAiFeatureSetting
    : ITrigger<GetAiFeatureSetting.GetAiFeatureSettingInput, GetAiFeatureSetting.GetAiFeatureSettingOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public GetAiFeatureSetting(IIntelligentHubConfigService intelligentHubConfigService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public class GetAiFeatureSettingInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetAiFeatureSettingInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetAiFeatureSettingOutput
    {
        [JsonProperty(IntelligentHubConfig.PropertyNameEnableWritingAssistant)]
        public bool EnableWritingAssistant { get; set; }

        [JsonProperty(IntelligentHubConfig.PropertyNameEnableSmartReply)]
        public bool EnableSmartReply { get; set; }

        [JsonConstructor]
        public GetAiFeatureSettingOutput(bool enableWritingAssistant, bool enableSmartReply)
        {
            EnableWritingAssistant = enableWritingAssistant;
            EnableSmartReply = enableSmartReply;
        }
    }

    public async Task<GetAiFeatureSettingOutput> F(GetAiFeatureSettingInput getAiFeatureSettingInput)
    {
        var intelligentHubConfig = await _intelligentHubConfigService.GetAiFeatureSettingAsync(
            getAiFeatureSettingInput.SleekflowCompanyId);

        return new GetAiFeatureSettingOutput(
            intelligentHubConfig.EnableWritingAssistant,
            intelligentHubConfig.EnableSmartReply);
    }
}