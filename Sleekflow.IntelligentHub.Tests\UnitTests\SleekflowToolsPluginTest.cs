using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class SleekflowToolsPluginTest
{
    private Kernel _kernel;
    private ISleekflowToolsPlugin _sleekflowToolsPlugin;

    [SetUp]
    public void SetUp()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _sleekflowToolsPlugin = scope.ServiceProvider.GetRequiredService<ISleekflowToolsPlugin>();
    }

    [Test]
    public async Task SendMessageAsync_BasicIntegrationTest_ShouldProcessSuccessfully()
    {
        // Arrange
        var testMessage = "Hello, this is a test message from integration test!";
        var testContactId = "ce9ea20a-0f61-4e3e-ab8c-90322ffb3264";
        var testStateId = "NbcR4az2MJ1NAo6"; // Set to null to test simulation mode, or provide value for full flow

        var kernel = _kernel.Clone();

        // Configure kernel data - adjust these parameters as needed
        kernel.Data[KernelDataKeys.CONTACT_ID] = testContactId;
        kernel.Data[KernelDataKeys.STATE_ID] = testStateId; // Set to null to test simulation mode

        // Configure SendMessageTool - adjust channel and identity as needed
        var sendMessageTool = new SleekflowSendMessageTool("85298696051", "whatsappcloudapi", null);
        var toolsConfig = new ToolsConfig(
            sleekflowSendMessageTool: sendMessageTool,
            hubspotTool: null);

        kernel.Data[KernelDataKeys.TOOLS_CONFIG] = toolsConfig;

        // Act
        var result = await _sleekflowToolsPlugin.SendMessage(kernel, testMessage);

        // Assert
        Assert.That(result, Is.Not.Null);
        Assert.That(result, Is.Not.Empty);

        // Log result for inspection
        TestContext.WriteLine($"Send message result: {result}");
        TestContext.WriteLine($"Test message: {testMessage}");
        TestContext.WriteLine($"Contact ID: {testContactId}");
        TestContext.WriteLine($"State ID: {testStateId}");

        // Verify expected result based on configuration
        if (string.IsNullOrEmpty(testStateId))
        {
            Assert.That(result, Is.EqualTo("MessageSent_Simulated_NoStateId"));
        }
        else
        {
            Assert.That(result, Is.EqualTo("MessageSent_EventPublished"));
        }
    }
}