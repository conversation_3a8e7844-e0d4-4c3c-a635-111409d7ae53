using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class UserProfilePropertyUpdatedSystemLogData
{
    [JsonProperty("user_profile_id")]
    public string UserProfileId { get; set; }

    [JsonProperty("property_name")]
    public string PropertyName { get; set; }

    [JsonProperty("old_value")]
    public string OldValue { get; set; }

    [JsonProperty("new_value")]
    public string NewValue { get; set; }

    [JsonConstructor]
    public UserProfilePropertyUpdatedSystemLogData(string userProfileId, string propertyName, string oldValue, string newValue)
    {
        UserProfileId = userProfileId;
        PropertyName = propertyName;
        OldValue = oldValue;
        NewValue = newValue;
    }
}