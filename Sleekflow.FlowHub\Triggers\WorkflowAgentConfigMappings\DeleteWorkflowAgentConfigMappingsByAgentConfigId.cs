using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.WorkflowAgentConfigMappings;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowAgentConfigMappings;

[TriggerGroup(ControllerNames.WorkflowAgentConfigMappings)]
public class DeleteWorkflowAgentConfigMappingsByAgentConfigId : ITrigger
{
    private readonly IWorkflowAgentConfigMappingService _workflowAgentConfigMappingService;

    public DeleteWorkflowAgentConfigMappingsByAgentConfigId(
        IWorkflowAgentConfigMappingService workflowAgentConfigMappingService)
    {
        _workflowAgentConfigMappingService = workflowAgentConfigMappingService;
    }

    public class DeleteWorkflowAgentConfigMappingsByAgentConfigIdInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

    }

    public class DeleteWorkflowAgentConfigMappingsByAgentConfigIdOutput
    {

    }

    public async Task<DeleteWorkflowAgentConfigMappingsByAgentConfigIdOutput> F(DeleteWorkflowAgentConfigMappingsByAgentConfigIdInput deleteWorkflowAgentConfigMappingsByAgentConfigIdInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
          deleteWorkflowAgentConfigMappingsByAgentConfigIdInput.SleekflowStaffId,
          deleteWorkflowAgentConfigMappingsByAgentConfigIdInput.SleekflowStaffTeamIds);

        await _workflowAgentConfigMappingService.DeleteWorkflowAgentConfigMappingsAsync(
            deleteWorkflowAgentConfigMappingsByAgentConfigIdInput.SleekflowCompanyId,
            deleteWorkflowAgentConfigMappingsByAgentConfigIdInput.AgentConfigId,
            sleekflowStaff);

        return new DeleteWorkflowAgentConfigMappingsByAgentConfigIdOutput();
    }
}