﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IApifyConfig
{
    string Token { get; }

    string WebhookUri { get; }

    string ActorId { get; }
}

public class ApifyConfig : IConfig, IApifyConfig
{
    /// <summary>
    /// Apify user token.
    /// </summary>
    public string Token { get; }

    /// <summary>
    /// URI for Apify to send webhook to Sleekflow.
    /// </summary>
    public string WebhookUri { get; }

    public string ActorId { get; }

    public ApifyConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Token =
            Environment.GetEnvironmentVariable("APIFY_TOKEN", target)
            ?? throw new SfMissingEnvironmentVariableException("APIFY_TOKEN");

        WebhookUri =
            Environment.GetEnvironmentVariable("APIFY_WEBHOOKURI", target)
            ?? throw new SfMissingEnvironmentVariableException("APIFY_WEBHOOKURI");

        ActorId =
            Environment.GetEnvironmentVariable("APIFY_ACTOR_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("APIFY_ACTOR_ID");
    }
}