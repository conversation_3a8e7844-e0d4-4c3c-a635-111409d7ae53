﻿using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Base;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Configs;

namespace Sleekflow.MessagingHub.WhatsappCloudApis;

public interface ICloudApiClients
{
    IWhatsappCloudApiBspClient WhatsappCloudApiBspClient { get; set; }

    IWhatsappCloudApiAuthenticationClient WhatsappCloudApiAuthenticationClient { get; set; }

    IWhatsappCloudApiMessagingClient WhatsappCloudApiMessagingClient { get; set; }

    IWhatsappCloudApiConversationalAutomationClient WhatsappCloudApiConversationalAutomationClient { get; }

    IWhatsappCloudApiFlowClient WhatsappCloudApiFlowClient { get; set; }

    IFacebookCommerceClient FacebookCommerceClient { get; set; }

    IMetaConversionApiClient MetaConversionApiClient { get; set; }

    IWhatsappMMLiteApiClient WhatsappMMLiteApiClient { get; set; }
}

public class CloudApiClients : ICloudApiClients, ISingletonService
{
    public IWhatsappCloudApiBspClient WhatsappCloudApiBspClient { get; set; }

    public IWhatsappCloudApiAuthenticationClient WhatsappCloudApiAuthenticationClient { get; set; }

    public IWhatsappCloudApiMessagingClient WhatsappCloudApiMessagingClient { get; set; }

    public IWhatsappCloudApiConversationalAutomationClient WhatsappCloudApiConversationalAutomationClient { get; set; }

    public IWhatsappCloudApiFlowClient WhatsappCloudApiFlowClient { get; set; }

    public IFacebookCommerceClient FacebookCommerceClient { get; set; }

    public IMetaConversionApiClient MetaConversionApiClient { get; set; }

    public IWhatsappMMLiteApiClient WhatsappMMLiteApiClient { get; set; }

    public CloudApiClients(
        IHttpClientFactory httpClientFactory,
        ISecretConfig secretConfig)
    {
        WhatsappCloudApiBspClient =
            new WhatsappCloudApiBspClient(
                secretConfig.FacebookSystemUserAccessToken,
                httpClientFactory.CreateClient("default-handler"));
        WhatsappCloudApiAuthenticationClient =
            new WhatsappCloudApiAuthenticationClient(
                secretConfig.FacebookSystemUserAccessToken,
                httpClientFactory.CreateClient("default-handler"));
        WhatsappCloudApiMessagingClient =
            new WhatsappCloudApiMessagingClient(
                secretConfig.FacebookSystemUserAccessToken,
                httpClientFactory.CreateClient("default-handler"));
        WhatsappCloudApiConversationalAutomationClient = new WhatsappCloudApiConversationalAutomationClient(
            secretConfig.FacebookSystemUserAccessToken,
            httpClientFactory.CreateClient("default-handler"));
        WhatsappCloudApiFlowClient = new WhatsappCloudApiFlowClient(
            secretConfig.FacebookSystemUserAccessToken,
            httpClientFactory.CreateClient("default-handler"));
        FacebookCommerceClient = new FacebookCommerceClient(
            secretConfig.FacebookSystemUserAccessToken,
            httpClientFactory.CreateClient("default-handler"));
        MetaConversionApiClient = new MetaConversionApiClient(
            secretConfig.FacebookSystemUserAccessToken,
            httpClientFactory.CreateClient("default-handler"));
        WhatsappMMLiteApiClient = new WhatsappMMLiteApiClient(
            secretConfig.FacebookSystemUserAccessToken,
            httpClientFactory.CreateClient("default-handler"));
    }
}