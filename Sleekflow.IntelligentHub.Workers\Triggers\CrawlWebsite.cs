using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.WebCrawlingSessions;
using Sleekflow.IntelligentHub.Workers.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class CrawlWebsite : ITrigger
{
    private readonly ILogger<CrawlWebsite> _logger;
    private readonly IWebCrawlingSessionRepository _webCrawlingSessionRepository;
    private readonly IWebCrawlingService _webCrawlingService;

    public CrawlWebsite(
        ILogger<CrawlWebsite> logger,
        IWebCrawlingSessionRepository webCrawlingSessionRepository,
        IWebCrawlingService webCrawlingService)
    {
        _logger = logger;
        _webCrawlingSessionRepository = webCrawlingSessionRepository;
        _webCrawlingService = webCrawlingService;
    }

    public class CrawlWebsiteInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [JsonConstructor]
        public CrawlWebsiteInput(string sleekflowCompanyId, string sessionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SessionId = sessionId;
        }
    }

    public class CrawlWebsiteOutput
    {
        [JsonProperty("session")]
        public WebCrawlingSession Session { get; set; }

        [JsonConstructor]
        public CrawlWebsiteOutput(WebCrawlingSession session)
        {
            Session = session;
        }
    }

    [Function(nameof(CrawlWebsite))]
    public async Task<CrawlWebsiteOutput> Process(
        [ActivityTrigger]
        CrawlWebsiteInput crawlWebsiteInput)
    {
        _logger.LogInformation(
            "CrawlWebsite started for Company: {CompanyId}, SessionId: {SessionId}",
            crawlWebsiteInput.SleekflowCompanyId,
            crawlWebsiteInput.SessionId);

        // Fetch the current web crawling session
        var session = await _webCrawlingSessionRepository.GetAsync(
            crawlWebsiteInput.SessionId,
            crawlWebsiteInput.SleekflowCompanyId);

        if (session == null)
        {
            throw new InvalidOperationException($"WebCrawlingSession not found: {crawlWebsiteInput.SessionId}");
        }

        if (!session.IsCrawlingEnabled)
        {
            try
            {
                var scrapeResult = await _webCrawlingService.ScrapeUrlAsync(session.BaseUrl);

                session.CrawlingResults.Add(scrapeResult);
                session.UrlsToProcess = [];
                session.Status = WebCrawlingSessionStatuses.Finished;

                _logger.LogInformation(
                    "CrawlWebsite completed for SessionId: {SessionId}. Processed {Url}" +
                    "status: {Status}",
                    crawlWebsiteInput.SessionId,
                    session.BaseUrl,
                    session.Status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "CrawlWebsite failed {BaseUrl}", session.BaseUrl);
                session.Status = WebCrawlingSessionStatuses.Failed;
            }
        }
        else
        {
            // Check if session is paused
            if (session.IsSessionPaused())
            {
                _logger.LogInformation(
                    "WebCrawlingSession is paused, skipping crawling: {SessionId}",
                    crawlWebsiteInput.SessionId);
                return new CrawlWebsiteOutput(session);
            }

            // Get already processed URLs from crawling results
            var processedUrls = session.CrawlingResults.Select(r => r.Url).ToHashSet();

            // Perform crawling on the current batch
            var batchResult = await _webCrawlingService.CrawlBatchAsync(
                session.BaseUrl,
                session.UrlsToProcess.ToList(),
                processedUrls,
                50);

            // Update the session with new crawling results
            session.CrawlingResults.AddRange(batchResult.NewCrawlingResults);
            session.UrlsToProcess = batchResult.UpdatedUrlsToProcess.ToArray();

            // Determine the status based on crawling completion
            string status;
            if (session.CrawlingResults.Count >= 1000)
            {
                status = WebCrawlingSessionStatuses.LimitReached;
            }
            else if (session.UrlsToProcess.Length == 0)
            {
                status = WebCrawlingSessionStatuses.Finished;
            }
            else
            {
                status = WebCrawlingSessionStatuses.InProgress;
            }

            session.Status = status;

            _logger.LogInformation(
                "CrawlWebsite completed for SessionId: {SessionId}. Processed {ProcessedCount} URLs, " +
                "remaining {UrlsToProcessCount} new URLs, status: {Status}",
                crawlWebsiteInput.SessionId,
                session.CrawlingResults.Count,
                session.UrlsToProcess.Length,
                status);
        }

        return new CrawlWebsiteOutput(session);
    }
}