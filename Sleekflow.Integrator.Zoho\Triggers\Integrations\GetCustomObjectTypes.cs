﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetCustomObjectTypes : ITrigger
{
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;

    public GetCustomObjectTypes(
        IZohoObjectService zohoObjectService,
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoConnectionService zohoConnectionService)
    {
        _zohoObjectService = zohoObjectService;
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
    }

    public class GetCustomObjectTypesInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetCustomObjectTypesInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class GetCustomObjectTypesOutput
    {
        [JsonProperty("custom_object_types")]
        [Required]
        public List<CustomObjectType> CustomObjectTypes { get; set; }

        [JsonConstructor]
        public GetCustomObjectTypesOutput(
            List<CustomObjectType> customObjectTypes)
        {
            CustomObjectTypes = customObjectTypes;
        }
    }

    public async Task<GetCustomObjectTypesOutput> F(GetCustomObjectTypesInput getCustomObjectTypesInput)
    {
        var connection =
            await _zohoConnectionService.GetByIdAsync(
                getCustomObjectTypesInput.ConnectionId,
                getCustomObjectTypesInput.SleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getCustomObjectTypesInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        return new GetCustomObjectTypesOutput(
            await _zohoObjectService.GetCustomObjectTypesAsync(authentication));
    }
}