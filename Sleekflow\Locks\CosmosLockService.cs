﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Sleekflow.Locks;

public class CosmosLockService : ILockService
{
    private readonly ILogger<CosmosLockService> _logger;
    private readonly ILockRepository _lockRepository;

    public CosmosLockService(
        ILogger<CosmosLockService> logger,
        ILockRepository lockRepository)
    {
        _logger = logger;
        _lockRepository = lockRepository;
    }

    public async Task<(bool IsLocked, TimeSpan? ExpireIn)> IsLockedAsync(
        string[] strings, CancellationToken
            cancellationToken = default)
    {
        var id = string.Join("-", strings);

        var @lock = await _lockRepository.GetOrDefaultAsync(
            id,
            id,
            cancellationToken: cancellationToken);

        return (@lock is not null, null);
    }

    public async Task<Lock?> LockAsync(
        string[] strings,
        TimeSpan minimumDuration,
        CancellationToken cancellationToken = default)
    {
        var id = string.Join("-", strings);
        var totalSeconds = Math.Max(1, (int) Math.Ceiling(minimumDuration.TotalSeconds));

        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug(
                "Acquiring a lock for strings {Strings}.",
                JsonConvert.SerializeObject(strings));
        }

        Lock? @lock = null;
        try
        {
            @lock = await _lockRepository.CreateAndGetAsync(
                new Lock(id, totalSeconds),
                id,
                cancellationToken: cancellationToken);
        }
        catch
        {
            // ignored
        }

        if (@lock == null)
        {
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "Unable to acquire a lock for strings {Strings}.",
                    JsonConvert.SerializeObject(strings));
            }
        }
        else
        {
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "Acquired a lock {Lock} for strings {Strings}.",
                    JsonConvert.SerializeObject(@lock),
                    JsonConvert.SerializeObject(strings));
            }
        }

        return @lock;
    }

    public async Task<Lock> WaitUnitLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        var retryDelayDuration = TimeSpan.FromMilliseconds(500);
        var maxRetryCount = maximumWaitDuration / retryDelayDuration;

        var i = 0;
        while (true)
        {
            var @lock = await LockAsync(strings, minimumLockDuration, cancellationToken);
            if (@lock != null)
            {
                return @lock;
            }

            if (i++ > maxRetryCount)
            {
                throw new TimeoutException("Unable to acquire a lock.");
            }

            await Task.Delay(retryDelayDuration, cancellationToken);
        }
    }

    public async Task<int> ReleaseAsync(Lock @lock, CancellationToken cancellationToken = default)
    {
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("Released the lock {Lock}.", @lock);
        }

        return await _lockRepository.DeleteAsync(
            @lock.Id,
            @lock.Id,
            eTag: @lock.ETag,
            transactionalBatch: null,
            cancellationToken: cancellationToken);
    }

    public Task AcquireReadLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task AcquireWriteLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<bool> ReleaseReadLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<bool> ReleaseWriteLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

}