using System.Reflection;
using MassTransit;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Orleans.Hosting;
using Orleans.Serialization;
using Sleekflow;
using Sleekflow.FlowHub.Commons.Configs;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer.Consumers;
using Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer.Triggers;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Mvc;
using Sleekflow.Persistence.Abstractions;

const string name = "SleekflowFlowHubOnWorkflowExecutionEndedPostProcessRequestedEventConsumer";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildDbServices(services);
        Modules.BuildConfigs(services, Assembly.Load("Sleekflow.FlowHub"));
        Modules.BuildServices(services, Assembly.Load("Sleekflow.FlowHub"));
        Modules.BuildCacheServices(services);
        services.AddDurableTaskClient(
            _ =>
            {
            });

        services.AddScoped<IDynamicFiltersRepositoryContext, DynamicFiltersRepositoryContext>();
        services.AddHttpContextAccessor();

        services.AddScoped<OnWorkflowExecutionEndedPostProcessRequestedEventQueueTrigger>()
            .AddMassTransitForAzureFunctions(
                cfg =>
                {
                    cfg.AddConsumer(typeof(OnWorkflowExecutionEndedPostProcessRequestedEventConsumer));
                },
                "HIGH_TRAFFIC_SERVICE_BUS_CONN_STR");

        FlowHubModules.BuildFlowHubDbServices(services);

        FlowHubModules.BuildFlowHubLruMemoryCacheServices(
            services,
            WorkflowStepsService.WorkflowStepsContainerName,
            WorkflowMetadataService.WorkflowMetadataContainerName);

        services.AddScoped<IBaseProxyStateService<BaseProxyState>,
            BaseProxyStateService>();
    });
hostBuilder.ConfigureAppConfiguration(
    b =>
    {
        b.AddAzureAppConfiguration(Environment.GetEnvironmentVariable("APP_CONFIGURATION_CONN_STR"));
    });

hostBuilder.Build().Run();