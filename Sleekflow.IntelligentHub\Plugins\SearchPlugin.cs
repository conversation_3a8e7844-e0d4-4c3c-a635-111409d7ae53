using System.ComponentModel;
using System.Text.RegularExpressions;
using Azure.Search.Documents;
using Azure.Search.Documents.Models;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Configs;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries.Indexing;
using Sleekflow.IntelligentHub.Plugins.Models;

namespace Sleekflow.IntelligentHub.Plugins;

public interface ISearchPlugin
{
    Task<string> GetSources(
        Kernel kernel,
        string userQuestion,
        string chatHistory,
        string sleekflowCompanyId);

    Task<GenerateSearchQueryResponse> GenerateSearchQuery(
        Kernel kernel,
        string chatHistory,
        string question,
        string additionalPrompt);

    Task<DetermineSearchResultQualityResponse> DetermineSearchResultQuality(
        Kernel kernel,
        string chatHistory,
        string question,
        string searchResult,
        string searchQuery);

    Task<List<(string Sourcepage, string Content, string ContentEn)>> GetSearchResultsAsync(
        string sleekflowCompanyId,
        string searchText,
        string? chatHistory = null);

    Task<string> GenerateAgentSearchQuery(Kernel kernel, string chatHistory);

    Task<GenerateKeywordsResponse> GenerateKeywordsAsync(Kernel kernel, string chatHistory);
}

public partial class SearchPlugin : ISearchPlugin, IScopedService
{
    private readonly ILogger<SearchPlugin> _logger;
    private readonly Kernel _kernel;
    private readonly IFaqAgentConfigService _faqAgentConfigService;
    private readonly IKnowledgeBaseEntryIndexingService _knowledgeBaseEntryIndexingService;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public SearchPlugin(
        ILogger<SearchPlugin> logger,
        Kernel kernel,
        IFaqAgentConfigService faqAgentConfigService,
        IKnowledgeBaseEntryIndexingService knowledgeBaseEntryIndexingService,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _kernel = kernel;
        _faqAgentConfigService = faqAgentConfigService;
        _knowledgeBaseEntryIndexingService = knowledgeBaseEntryIndexingService;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("GetSources")]
    [Description("Get sources from the knowledge base")]
    [return: Description("The sources from the knowledge base")]
    public async Task<string> GetSources(
        Kernel kernel,
        string userQuestion,
        string chatHistory,
        string sleekflowCompanyId)
    {
        var previousResults =
            new List<(DetermineSearchResultQualityResponse DetermineSearchResultQualityResponse,
                List<(string Sourcepage, string Content, string ContentEn)> SearchResults)>();

        var generateSearchQueryResponse = await GenerateSearchQuery(
            _kernel,
            chatHistory,
            userQuestion,
            string.Empty);

        _logger.LogInformation(
            "Generated the search query: {SearchQuery}, Sleekflow Company ID: {SleekflowCompanyId}, User Question: {UserQuestion}",
            generateSearchQueryResponse.LuceneSearchQuery,
            sleekflowCompanyId,
            userQuestion);

        // Generate the initial search query.
        var searchQuery = generateSearchQueryResponse.LuceneSearchQuery;

        for (var i = 0; i < 3; i++)
        {
            // Search the knowledge base with the generated search query.
            List<(string Sourcepage, string Content, string ContentEn)> searchResults;
            try
            {
                searchResults =
                    await GetSearchResultsAsync(
                        sleekflowCompanyId,
                        searchQuery);
            }
            catch (Exception e)
            {
                searchResults = new List<(string Sourcepage, string Content, string ContentEn)>();

                // 404 (Not Found) is expected when the company doesn't create the knowledge base.
                if (!e.ToString().Contains("404 (Not Found)"))
                {
                    _logger.LogError(e, "Unable to get search results from the knowledge base");
                }
            }

            var determineSearchResultQualityResponse =
                await DetermineSearchResultQuality(
                    _kernel,
                    chatHistory,
                    userQuestion,
                    string.Join("\n", searchResults.Select(sr => $"[{sr.Sourcepage}]:```{sr.Content}```")),
                    searchQuery);

            _logger.LogInformation(
                "Evaluated the search query: {SearchQuery}, Sleekflow Company ID: {SleekflowCompanyId}, User Question: {UserQuestion}, Relevance Score: {RelevanceScore}, Relevance Score Comment: {RelevanceScoreComment}, Optimized Lucene Search Query: {OptimizedLuceneSearchQuery}, Optimized Lucene Search Query Comment: {OptimizedLuceneSearchQueryComment}",
                searchQuery,
                sleekflowCompanyId,
                userQuestion,
                determineSearchResultQualityResponse.RelevanceScore,
                determineSearchResultQualityResponse.RelevanceScoreComment,
                determineSearchResultQualityResponse.OptimizedLuceneSearchQuery,
                determineSearchResultQualityResponse.OptimizedLuceneSearchQueryComment);

            // Add the current results before checking for duplicates
            previousResults.Add((determineSearchResultQualityResponse, searchResults));

            var hasDuplicateQuery = previousResults.Count > 1 && previousResults.Take(previousResults.Count - 1)
                .Any(pr =>
                    pr.DetermineSearchResultQualityResponse.OptimizedLuceneSearchQuery ==
                    determineSearchResultQualityResponse.OptimizedLuceneSearchQuery);
            var isSameQuery = searchQuery == determineSearchResultQualityResponse.OptimizedLuceneSearchQuery;

            // If the optimized search query is the same as the previous one or same as not optimized, stop the loop.
            if (hasDuplicateQuery || isSameQuery)
            {
                _logger.LogInformation(
                    "The optimized search query is the same as the previous one. {SearchQuery}, Sleekflow Company ID: {SleekflowCompanyId}, User Question: {UserQuestion}, Relevance Score: {RelevanceScore}, Relevance Score Comment: {RelevanceScoreComment}, Optimized Lucene Search Query: {OptimizedLuceneSearchQuery}, Optimized Lucene Search Query Comment: {OptimizedLuceneSearchQueryComment}",
                    searchQuery,
                    sleekflowCompanyId,
                    userQuestion,
                    determineSearchResultQualityResponse.RelevanceScore,
                    determineSearchResultQualityResponse.RelevanceScoreComment,
                    determineSearchResultQualityResponse.OptimizedLuceneSearchQuery,
                    determineSearchResultQualityResponse.OptimizedLuceneSearchQueryComment);
                break;
            }

            // If the relevance score is greater than 5, return the current results
            if (determineSearchResultQualityResponse.RelevanceScore > 5)
            {
                return string.Join("\n", searchResults.Select(s => $"[{s.Sourcepage}]:```{s.Content}```"));
            }

            searchQuery = determineSearchResultQualityResponse.OptimizedLuceneSearchQuery;
        }

        var bestResults = previousResults
            .MaxBy(q => q.DetermineSearchResultQualityResponse.RelevanceScore)!
            .SearchResults;

        return string.Join(
            "\n",
            bestResults.Select(s => $"[{s.Sourcepage}]:```{s.Content}```"));
    }

    [KernelFunction("GenerateSearchQuery")]
    [Description("Generate a search query from the input text")]
    [return: Description("The generated search query")]
    public async Task<GenerateSearchQueryResponse> GenerateSearchQuery(
        Kernel kernel,
        string chatHistory,
        string question,
        string additionalPrompt)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1, true);

        var generateSearchQueryFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "GenerateSearchQuery",
                Description = "Generate a search query from the input text",
                Template =
                    """
                    <message role="system">
                    Your task is to generate a Lucene search query based on the conversation history and the user's new question.
                    The query should take into account the context of the conversation and any relevant keywords or phrases from the question.
                    Even the question is not in English, the generated query should be in English and the terms should be in English.

                    The Lucene search query should be optimized to retrieve the most relevant and accurate information from the knowledge base for the user's question by emphasizing the keywords and phrases that are most relevant to the user's question.

                    {{$ADDITIONAL_PROMPT}}

                    You MUST return the result in JSON format with two properties 1. reason 2. lucene_search_query.
                    </message>

                    <message role="user">
                    User Question:我想同女朋友浸溫泉，有無好推介呀？
                    </message>
                    <message role="assistant">
                    {
                        "reason": "The query focuses on hot springs and couples, using synonyms, proximity search, and boosting for relevance. The optional term \"recommendation\" is included to consider suggestions without detracting from the main focus.",
                        "lucene_search_query": "(\"hot spring\"~3^5 OR onsen^3 OR \"thermal bath\"~3^3 OR spa OR bathhouse OR bath) AND (couple~^5 OR pair~ OR duo~ OR twosome~ OR partners OR friendship OR romantic~^3 OR love~^3 OR passionate~) AND (recommendation OR \"\")"
                    }
                    </message>

                    <message role="user">
                    User Question:1a座29樓A1幾大？
                    </message>
                    <message role="assistant">
                    {
                        "reason": "The query focuses on the building, floor, and unit, using proximity search and term boosting for precision. It includes size-related terms to find apartment dimensions and presents the floor information in multiple ways (29th floor, 29/F, 29) for increased flexibility and accuracy.",
                        "lucene_search_query": "(\"1a block\"~3^5 OR \"1a tower\"~3^5 OR \"1a building\"~3^5 OR \"1a\") AND (\"29th floor\"~3^4 OR \"29/F\" OR \"29\") AND (\"A1 unit\"~3^4 OR \"A1 flat\"~3^4 OR \"A1 apartment\"~3^4 OR \"A1\") AND (size~^5 OR area~^5 OR square~^3 OR footage~^3 OR meters~^2 OR dimensions~)"
                    }
                    </message>

                    <message role="user">
                    User Question:Tell me some WhatsApp marketing examples from real-life retail brands.
                    </message>
                    <message role="assistant">
                    {
                        "reason": "The query effectively targets real-life examples (\"use cases\", \"scenarios\") of \"retail\" brands using \"WhatsApp marketing\". It prioritizes proximity and relevance (boosting via ^) of these key components, precisely addressing the question about real-life retail WhatsApp marketing instances.",
                        "lucene_search_query": "(\"use cases\"~5^5 OR \"case studies\"~5^3 OR scenarios~^2 OR examples~ OR situations~) AND (retail^10 OR fashion^2 OR electronics^2) AND (\"WhatsApp marketing\"~3^5 OR \"WhatsApp advertising\"~3^3 OR \"WhatsApp campaigns\"~3^2 OR \"WhatsApp promotions\"~3^2 OR \"WhatsApp\") AND (\"brand\" OR \"\")"
                    }
                    </message>

                    <message role="user">
                    還有不是來自 Rue Madame Fashion Group、Citylink 和連卡佛的零售用例嗎？
                    </message>
                    <message role="assistant">
                    {
                        "reason": "The query searches for retail use cases excluding specific companies, using term boosting, proximity search, and including/excluding relevant terms to make the query focused and relevant.",
                        "lucene_search_query": "(\"use cases\"~5^10 OR \"case studies\"~5^3 OR scenarios~^2 OR examples~ OR situations~) AND (retail^10 OR fashion^2 OR electronics^2) NOT (\"Rue Madame Fashion Group\" OR \"Citylink\" OR \"Lane Crawford\" OR \"連卡佛\")"
                    }
                    </message>

                    <message role="user">
                    Conversation History:
                    {{$CHAT_HISTORY}}

                    User Question:
                    {{$QUESTION}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "ADDITIONAL_PROMPT",
                        Description = "Additional prompt for the user to provide more context or information",
                        IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "CHAT_HISTORY", Description = "The conversation history", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "QUESTION", Description = "The user's new question", IsRequired = true
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The generated search query",
                    JsonSchema =
                        """
                        {
                            "type": "object",
                            "properties": {
                                "reason": {
                                    "type": "string",
                                    "description": "The reason for the generated search query"
                                },
                                "lucene_search_query": {
                                    "type": "string",
                                    "description": "The Lucene search query"
                                }
                            }
                        }
                        """
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await generateSearchQueryFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "ADDITIONAL_PROMPT", additionalPrompt
                },
                {
                    "CHAT_HISTORY", chatHistory
                },
                {
                    "QUESTION", question
                }
            });

        var unknownResponse = new GenerateSearchQueryResponse("Unable to generate search query", string.Empty);

        if (chatMessageContent?.Content is null)
        {
            return unknownResponse;
        }

        try
        {
            return JsonConvert.DeserializeObject<GenerateSearchQueryResponse>(chatMessageContent.Content) ??
                   unknownResponse;
        }
        catch (Exception)
        {
            return unknownResponse;
        }
    }

    [KernelFunction("DetermineSearchResultQuality")]
    [Description("Determines the quality of the search results and provide an optimized search query")]
    [return: Description("The relevance score and the optimized search query")]
    public async Task<DetermineSearchResultQualityResponse> DetermineSearchResultQuality(
        Kernel kernel,
        string chatHistory,
        string question,
        string searchResult,
        string searchQuery)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1, true);

        var determineSearchResultQualityFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "DetermineSearchResultQuality",
                Description = "Determines the quality of the search results and provide an optimized search query",
                Template =
                    """"
                    <message role="system">
                    You are an feedback-based optimizer to enhance the Lucene search query based on the context. The search query is used to retrieve the information from the knowledge base to answer the user's question.
                    Based on the provided context (Previous Optimization Results, Chat History, User Question, Search Result), you have four tasks:

                    1. Assess and rate the relevance of the given search results using a 1-10 scale, where 1 signifies no relevance and 10 indicates high relevance, to determine their effectiveness in delivering pertinent information to address the user's question.
                       - When there is no search results, the relevance score is 1.
                    2. Adjust the Lucene search query to get more relevant search results. Before adjusting the query, you should first understand the user's intent and the following some requirements:
                       - You must provide a valid Lucene search query.
                       - There should be at most 5 search results. If fewer results or no results are present, refine the search query to be more relaxed in order to include more search results in the next feedback loop.
                       - If the results are irrelevant, refine the query to be more focus in the next feedback loop.
                       - The query should be in English.
                    3. Offer a brief and clear explanation for the relevance rating.
                    4. Offer a brief and clear explanation why the optimized query is more effective.

                    Please answer according to the following JSON format:"""
                    {
                        "relevance_score_comment": "An within-30-words comment explaining the relevance rating.",
                        "optimized_lucene_search_query_comment": "An within-100-words comment explaining 1. what is optimized 2. why the optimized Lucene search query is more effective than the original one",
                        "relevance_score": "7",
                        "optimized_lucene_search_query": "The optimized Lucene query to have more relevance search results"
                    }
                    """
                    </message>

                    <message role="user">
                    Chat History:"""
                    {{$CHAT_HISTORY}}
                    """

                    User Question:"""
                    {{$USER_QUESTION}}
                    """

                    Search Result:"""
                    {{$SEARCH_RESULT}}
                    """

                    The Lucene Search Query of the Search Result:"""
                    {{$SEARCH_QUERY}}
                    """
                    </message>
                    """",
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "CHAT_HISTORY", Description = "The conversation history", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "USER_QUESTION", Description = "The user's new question", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "SEARCH_RESULT", Description = "The search result", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "SEARCH_QUERY",
                        Description = "The Lucene search query of the search result",
                        IsRequired = true
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The generated search query",
                    JsonSchema =
                        """
                        {
                            "type": "object",
                            "properties": {
                                "relevance_score_comment": {
                                    "type": "string",
                                    "description": "An within-30-words comment explaining the relevance rating."
                                },
                                "optimized_lucene_search_query_comment": {
                                    "type": "string",
                                    "description": "An within-100-words comment explaining 1. what is optimized 2. why the optimized Lucene search query is more effective than the original one"
                                },
                                "relevance_score": {
                                    "type": "number",
                                    "description": "The relevance score on a scale of 1-10"
                                },
                                "optimized_lucene_search_query": {
                                    "type": "string",
                                    "description": "The optimized Lucene search query"
                                }
                            }
                        }
                        """
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await determineSearchResultQualityFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "CHAT_HISTORY", chatHistory
                },
                {
                    "USER_QUESTION", question
                },
                {
                    "SEARCH_RESULT", searchResult
                },
                {
                    "SEARCH_QUERY", searchQuery
                }
            });

        var unknownResponse = new DetermineSearchResultQualityResponse(
            0,
            "Unable to determine search result quality",
            string.Empty,
            "Unable to determine search result quality");

        if (chatMessageContent?.Content is null)
        {
            return unknownResponse;
        }

        try
        {
            return JsonConvert.DeserializeObject<DetermineSearchResultQualityResponse>(chatMessageContent.Content) ??
                   unknownResponse;
        }
        catch (Exception)
        {
            return unknownResponse;
        }
    }

    // TODO: Need to fix the source page empty string issue
    public async Task<List<(string Sourcepage, string Content, string ContentEn)>> GetSearchResultsAsync(
        string sleekflowCompanyId,
        string searchText,
        string? chatHistory = null)
    {
        var generateSearchQueryResponse = await GenerateSearchQuery(
            _kernel,
            chatHistory ?? string.Empty,
            searchText,
            string.Empty);

        _logger.LogInformation(
            "Generated the search query: {SearchQuery}, Sleekflow Company ID: {SleekflowCompanyId}, User Question: {SearchText}",
            generateSearchQueryResponse.LuceneSearchQuery,
            sleekflowCompanyId,
            searchText);

        // Generate the initial search query.
        var searchQuery = generateSearchQueryResponse.LuceneSearchQuery;

        return await GetSearchResultsAsync(sleekflowCompanyId, searchQuery);
    }

    [KernelFunction("GenerateAgentSearchQuery")]
    [Description("Generate an optimized search query for Light RAG based on user question and chat history")]
    [return: Description("The generated search query optimized for Light RAG")]
    public async Task<string> GenerateAgentSearchQuery(Kernel kernel, string chatHistory)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var generateLightRagQueryFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "GenerateLightRagQuery",
                Description =
                    "Generate an optimized search query for Light RAG based on user question and chat history",
                Template =
                    """
                    <message role="system">
                    You are a query optimization expert specializing in Retrieval-Augmented Generation (RAG) systems. Your primary objective is to craft efficient, resource-conscious search queries derived from the chat history. Analyze the provided chat history to pinpoint the user's intent and generate an optimized search query that accurately reflects their needs.
                    In your analysis, please adhere to the following guidelines:
                    1. Identify relevant context from chat history, with special attention to chronological progression:
                       - Recent messages carry more weight than older ones
                       - If multiple messages build up to the current question, combine their context
                    2. Consider potential variations and synonyms of key terms
                    3. Maintain semantic meaning while optimizing for retrieval
                    4. Do not change the meaning of the question

                    Rules for handling chat history:
                    - Prioritize context from the most recent exchanges
                    - If previous messages are building up to the current question, incorporate their combined context
                    - Consider how previous answers might shape the interpretation of the current question
                    - Look for patterns of clarification or specification in the conversation flow

                    Return a focused search query that captures the essential information needs while maintaining context awareness.
                    The query should:
                    - Combine context from related messages
                    - Do not change the meaning of the user's question
                    </message>

                    <message role="user">
                    Chat History:
                    user: What plans do you offer for small businesses?
                    assistant: We have a range of plans for small businesses. Could you provide more details about your requirements?
                    user: I wanted to know about the pricing and features of your basic plan.
                    assistant: Our basic plan includes 1000 messages per month and basic customer support. Would you like more information?
                    user: Does the it include automated messaging and e-commerce integration?
                    </message>
                    <message role="assistant">
                    Does the basic plan include automated messaging and e-commerce integration?
                    </message>

                    <message role="user">
                    Chat History:
                    User: What are the benefits of your premium plan?
                    Bot: Our premium plan offers advanced features like automated messaging, e-commerce integration, and priority support.
                    User: How does it compare to the basic plan?
                    Bot: The premium plan includes unlimited messages, advanced analytics, and priority customer support.
                    User: What is the pricing ?
                    Bot: The premium plan is $99 per month. Would you like to know more?
                    User: Yes, please provide more details.
                    </message>
                    <message role="assistant">
                    What is the pricing and features of the premium plan?
                    </message>

                    <message role="user">
                    Chat History:
                    {{$CHAT_HISTORY}}
                    </message>
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "CHAT_HISTORY",
                        Description = "The conversation history including the user's question as the last message",
                        IsRequired = true
                    }
                ],
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await generateLightRagQueryFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "CHAT_HISTORY", string.Join("\n", chatHistory)
                }
            });

        return chatMessageContent?.Content ?? string.Empty;
    }

    [KernelFunction("GenerateKeywords")]
    [Description("Generate high-level and low-level keywords from the chat history")]
    [return: Description("The generated keywords in JSON format")]
    public async Task<GenerateKeywordsResponse> GenerateKeywordsAsync(Kernel kernel, string chatHistory)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1, true);

        var generateKeywordsFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "GenerateKeywords",
                Description = "Generate high-level and low-level keywords from the chat history",
                Template =
                    """
                    <message role="system">
                    ---Role---

                    You are a keyword extraction specialist tasked with analyzing conversations and identifying both high-level and low-level keywords, with a primary focus on the latest user message while considering relevant context from the conversation history.

                    ---Goal---

                    Extract meaningful keywords from the conversation, prioritizing the latest user message while incorporating context from previous exchanges when relevant. Categorize these into:
                    - High-level keywords: Overarching concepts, themes, or intentions
                    - Low-level keywords: Specific entities, details, or concrete terms

                    ---Instructions---

                    1. Primary Focus:
                       - Prioritize keywords from the latest user message
                       - Consider how the latest message builds upon previous context

                    2. Context Consideration:
                       - Analyze previous messages for contextual understanding
                       - Include relevant keywords from earlier messages that provide essential context
                       - Pay attention to how agent responses might have influenced the user's latest message

                    3. Output Format:
                       - Return results in JSON format with two keys:
                         - "high_level_keywords": Array of overarching concepts/themes
                         - "low_level_keywords": Array of specific entities/details
                       - Keep keywords in their original language(s) exactly as they appear in the input text
                       - For mixed language content, preserve both languages in the keywords
                       - Avoid unicode characters in output
                    </message>

                    <message role="user">
                    Chat History:
                    User: 我想了解一下你們的 premium plan 有什麼好處？
                    Bot: 我們的 premium plan 提供多項進階功能，包括 automated messaging、電商整合和優先支援。
                    User: 跟 basic plan 比較如何？
                    Bot: Premium plan 包含無限訊息量、進階分析和優先客戶支援。
                    User: 收費是多少？
                    Bot: Premium plan 是每月 $99。需要更多資訊嗎？
                    User: 好的，請提供更多細節。
                    </message>

                    <message role="assistant">
                    {
                        "high_level_keywords": ["進階服務", "功能比較", "pricing inquiry", "服務細節"],
                        "low_level_keywords": ["premium plan", "$99 per month", "無限訊息", "進階分析", "優先支援", "automated messaging", "電商整合"]
                    }
                    </message>

                    <message role="user">
                    Chat History:
                    {{$CHAT_HISTORY}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "CHAT_HISTORY",
                        Description = "The conversation history to extract keywords from",
                        IsRequired = true
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The generated keywords in JSON format",
                    JsonSchema =
                        """
                        {
                            "type": "object",
                            "properties": {
                                "high_level_keywords": {
                                    "type": "array",
                                    "items": {
                                        "type": "string"
                                    },
                                    "description": "High-level keywords focusing on overarching concepts or themes"
                                },
                                "low_level_keywords": {
                                    "type": "array",
                                    "items": {
                                        "type": "string"
                                    },
                                    "description": "Low-level keywords focusing on specific entities or details"
                                }
                            }
                        }
                        """
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await generateKeywordsFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "CHAT_HISTORY", chatHistory
                }
            });

        return
            JsonConvert.DeserializeObject<GenerateKeywordsResponse>(chatMessageContent?.Content ?? "{}") ??
            new GenerateKeywordsResponse([], []);
    }

    private async Task<List<(string Sourcepage, string Content, string ContentEn)>> GetSearchResultsAsync(
        string sleekflowCompanyId,
        string searchQuery)
    {
        var agentConfigOrDefault = _faqAgentConfigService.GetAgentConfigOrDefault(sleekflowCompanyId);

        var searchClient =
            _knowledgeBaseEntryIndexingService.CreateSearchClient(agentConfigOrDefault.AzureSearchIndexName);

        var searchOptions = new SearchOptions
        {
            QueryType = SearchQueryType.Full,
            SemanticSearch = new SemanticSearchOptions
            {
                SemanticConfigurationName = "default"
            },
            Size = agentConfigOrDefault.MaxNumOfSearchResults,
        };

        var searchResponse = await searchClient.SearchAsync<SearchDocument>(searchQuery, searchOptions);
        var documents = searchResponse.Value.GetResults().Select(result => result.Document).ToList();

        var searchResults = new List<(string Sourcepage, string Content, string ContentEn)>();
        foreach (var document in documents)
        {
            document.TryGetValue("sourcepage", out var sourcepageObj);
            var sourcepage = sourcepageObj as string ?? string.Empty;
            var content = document["content"] as string ?? string.Empty;
            var contentEn = document["content_en"] as string ?? string.Empty;

            if (string.IsNullOrWhiteSpace(sourcepage) && string.IsNullOrWhiteSpace(content))
            {
                continue;
            }

            var sanitizedContent = NewLineRegex().Replace(content, " ");

            searchResults.Add((sourcepage, sanitizedContent, contentEn));
        }

        return searchResults;
    }

    [GeneratedRegex("\\n|\\r")]
    private static partial Regex NewLineRegex();
}