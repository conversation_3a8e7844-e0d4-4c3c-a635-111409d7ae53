using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.WebCrawlingSession)]
public class WebCrawlingSession : Entity, IHasCreatedAt, IHasUpdatedAt, IHasSleekflowCompanyId
{
    public const string PropertyNameBaseUrl = "base_url";
    public const string PropertyNameBaseUrlTitle = "base_url_title";
    public const string PropertyNameStatus = "status";
    public const string PropertyNameIsCrawlingEnabled = "is_crawling_enabled";
    public const string PropertyNameCrawlingResults = "crawling_results";
    public const string PropertyNameUrlsToProcess = "urls_to_process";

    [JsonProperty(PropertyName = PropertyNameBaseUrl)]
    public string BaseUrl { get; set; }

    [JsonProperty(PropertyName = PropertyNameBaseUrlTitle)]
    public string? BaseUrlTitle { get; set; }

    [JsonProperty(PropertyName = PropertyNameStatus)]
    public string Status { get; set; }

    [JsonProperty(PropertyName = PropertyNameIsCrawlingEnabled)]
    public bool IsCrawlingEnabled { get; set; }

    [JsonProperty(PropertyName = PropertyNameCrawlingResults)]
    public List<CrawlingResult> CrawlingResults { get; set; }

    [JsonProperty(PropertyName = PropertyNameUrlsToProcess)]
    public string[] UrlsToProcess { get; set; }

    [JsonProperty(PropertyName = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyName = IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(PropertyName = IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public WebCrawlingSession(
        string id,
        string baseUrl,
        string? baseUrlTitle,
        string status,
        bool? isCrawlingEnabled,
        List<CrawlingResult> crawlingResults,
        string[] urlsToProcess,
        string sleekflowCompanyId,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, SysTypeNames.WebCrawlingSession)
    {
        BaseUrl = baseUrl;
        BaseUrlTitle = baseUrlTitle;
        Status = status;
        IsCrawlingEnabled = isCrawlingEnabled ?? true;
        CrawlingResults = crawlingResults;
        UrlsToProcess = urlsToProcess;
        SleekflowCompanyId = sleekflowCompanyId;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public bool IsSessionPaused()
    {
        if (IsCrawlingEnabled)
        {
            return Status == WebCrawlingSessionStatuses.Paused;
        }

        // If crawling is not enabled, we are only scraping 1 link, so we can't pause
        return false;
    }
}

public class CrawlingResult
{
    [JsonProperty(PropertyName = "url")]
    public string Url { get; set; }

    [JsonProperty(PropertyName = "character_count")]
    public int CharacterCount { get; set; }

    [JsonProperty(PropertyName = "webpage_title")]
    public string? WebpageTitle { get; set; }

    [JsonConstructor]
    public CrawlingResult(string url, int characterCount, string? webpageTitle)
    {
        Url = url;
        CharacterCount = characterCount;
        WebpageTitle = webpageTitle;
    }
}

public static class WebCrawlingSessionStatuses
{
    public const string InProgress = "in-progress";
    public const string Paused = "paused";
    public const string LimitReached = "limit-reached";
    public const string Finished = "finished";
    public const string Failed = "failed";  // only applicable to single url scraping
}