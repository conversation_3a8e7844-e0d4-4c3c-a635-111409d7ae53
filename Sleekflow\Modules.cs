using System.Reflection;
using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Polly;
using <PERSON><PERSON><PERSON>;
using Sleekflow.Caches;
using Sleekflow.Configs;
using Sleekflow.DependencyInjection;
using Sleekflow.Events;
using Sleekflow.Events.EventHub;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.JsonConfigs;
using Sleekflow.Loggings;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Persistence.CrmHubIntegrationDb;
using Sleekflow.Persistence.MessagingHubDb;
using Sleekflow.RateLimits.ThrottledConsumers;
using Sleekflow.Webhooks;
using Sleekflow.Workers;
using StackExchange.Redis;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class Modules
{
    public static void BuildConfigs(IServiceCollection b, params Assembly[] additionalAssemblies)
    {
        var entryAssembly = Assembly.GetCallingAssembly();

        var hashSet = new HashSet<Assembly>
        {
            entryAssembly,
        };
        foreach (var additionalAssembly in additionalAssemblies)
        {
            hashSet.Add(additionalAssembly);
        }

        b.Scan(
            scan => scan
                .FromAssemblies(hashSet)
                .AddClasses(classes => classes.AssignableTo<IConfig>())
                .UsingRegistrationStrategy(RegistrationStrategy.Throw)
                .AsMatchingInterface()
                .WithSingletonLifetime());
    }

    public static void BuildTriggers(IServiceCollection b, params Assembly[] additionalAssemblies)
    {
        var entryAssembly = Assembly.GetCallingAssembly();
        var hashSet = new HashSet<Assembly>
        {
            entryAssembly
        };
        foreach (var additionalAssembly in additionalAssemblies)
        {
            hashSet.Add(additionalAssembly);
        }

        b.Scan(
            scan => scan
                .FromAssemblies(
                    hashSet)
                .AddClasses(classes => classes.AssignableTo<ITrigger>())
                .UsingRegistrationStrategy(RegistrationStrategy.Throw)
                .AsSelf()
                .WithScopedLifetime());
    }

    public static void BuildServices(IServiceCollection b, params Assembly[] additionalAssemblies)
    {
        var entryAssembly = Assembly.GetCallingAssembly()!;
        var executingAssembly = Assembly.GetExecutingAssembly();

        var hashSet = new HashSet<Assembly>
        {
            entryAssembly, executingAssembly
        };
        foreach (var additionalAssembly in additionalAssemblies)
        {
            hashSet.Add(additionalAssembly);
        }

#if SWAGGERGEN
        foreach (var assembly in hashSet)
        {
            foreach (var t in assembly
                         .GetTypes()
                         .Where(type =>
                             typeof(ISingletonService).IsAssignableFrom(type) && type != typeof(ISingletonService)))
            {
                var @interface = t.GetInterfaces().FirstOrDefault(i => i.Name == "I" + t.Name);
                if (@interface == null) continue;

                var myGeneric = typeof(Mock<>);
                var constructedClass = myGeneric.MakeGenericType(@interface);
                var created = Activator.CreateInstance(constructedClass);
                dynamic comparer = created!;

                b.AddSingleton(@interface, () => comparer.Object);
            }
        }

        foreach (var assembly in hashSet)
        {
            foreach (var t in assembly
                         .GetTypes()
                         .Where(type =>
                             typeof(IScopedService).IsAssignableFrom(type) && type != typeof(IScopedService)))
            {
                var @interface = t.GetInterfaces().FirstOrDefault(i => i.Name == "I" + t.Name);
                if (@interface == null) continue;

                var myGeneric = typeof(Mock<>);
                var constructedClass = myGeneric.MakeGenericType(@interface);
                var created = Activator.CreateInstance(constructedClass);
                dynamic comparer = created!;

                b.AddSingleton(@interface, () => comparer.Object);
            }
        }

#else
        b.Scan(
            scan => scan
                .FromAssemblies(hashSet)
                .AddClasses(classes => classes.AssignableTo<ISingletonService>())
                .UsingRegistrationStrategy(RegistrationStrategy.Throw)
                .AsMatchingInterface()
                .WithSingletonLifetime());

        b.Scan(
            scan => scan
                .FromAssemblies(hashSet)
                .AddClasses(classes => classes.AssignableTo<IScopedService>())
                .UsingRegistrationStrategy(RegistrationStrategy.Throw)
                .AsMatchingInterface()
                .WithScopedLifetime());

#endif
    }

    public static void BuildHttpClients(IServiceCollection b)
    {
        b
            .AddHttpClient("default-handler")
            .ConfigureHttpClient((_, client) => { client.Timeout = TimeSpan.FromSeconds(60); })
            .ConfigurePrimaryHttpMessageHandler(
                () => new HttpClientHandler
                {
                    AllowAutoRedirect = false,
                });
    }

    public static void BuildDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<IPersistenceRetryPolicyService>(new Mock<IPersistenceRetryPolicyService>().Object);

        b.AddSingleton<IDbConfig>(new Mock<IDbConfig>().Object);

        b.AddSingleton<IDbContainerResolver>(new Mock<IDbContainerResolver>().Object);
        b.AddSingleton<IDbDirectContainerResolver>(new Mock<IDbDirectContainerResolver>().Object);

#else
        b.AddSingleton<IPersistenceRetryPolicyService, PersistenceRetryPolicyService>();

        b.AddSingleton<IDbConfig, DbConfig>();

        b.AddSingleton<IDbContainerResolver, DbContainerResolver>();
        b.AddSingleton<IDbDirectContainerResolver, DbDirectContainerResolver>();

#endif
    }

    public static void BuildCacheServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<ICacheConfig>(new Mock<ICacheConfig>().Object);
        b.AddSingleton<IConnectionMultiplexer>(new Mock<IConnectionMultiplexer>().Object);
        b.AddSingleton<ICacheService>(new Mock<ICacheService>().Object);

#else
        var cacheConfig = new CacheConfig();

        b.AddSingleton<ICacheConfig>(cacheConfig);
        b.AddSingleton<IConnectionMultiplexer>(ConnectionMultiplexer.Connect(cacheConfig.RedisConnStr));
        b.AddSingleton<ICacheService, CacheService>();

        // Add memory cache registration
        b.AddMemoryCache();

#endif
    }

    public static void BuildWorkerServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<IWebhookRepository>(new Mock<IWebhookRepository>().Object);
        b.AddSingleton<IWebhookEventRepository>(new Mock<IWebhookEventRepository>().Object);
        b.AddSingleton<IWorkerConfig>(new Mock<IWorkerConfig>().Object);
        b.AddSingleton<IWorkerService>(new Mock<IWorkerService>().Object);
        b.AddSingleton<IWebhookService>(new Mock<IWebhookService>().Object);

#else
        b.AddSingleton<IWebhookRepository, WebhookRepository>();
        b.AddSingleton<IWebhookEventRepository, WebhookEventRepository>();
        b.AddSingleton<IWorkerConfig, WorkerConfig>();
        b.AddSingleton<IWorkerService, WorkerService>();
        b.AddSingleton<IWebhookService, WebhookService>();
#endif
    }

    public static void BuildServiceBusServices(
        IServiceCollection b,
        Action<IBusRegistrationConfigurator>? additionalMassTransitConfiguration = null,
        Action<IRiderRegistrationContext, IEventHubFactoryConfigurator>? additionalRiderConfiguration = null,
        params Assembly[] additionalAssemblies)
    {
#if SWAGGERGEN
        b.AddSingleton<IServiceBusConfig>(new Mock<IServiceBusConfig>().Object);
        b.AddSingleton<IBus>(new Mock<IBus>().Object);

#else
        var serviceBusConfig = new ServiceBusConfig();

        var entryAssembly = Assembly.GetCallingAssembly()!;
        var executingAssembly = Assembly.GetExecutingAssembly();
        var consumerAssemblies = new HashSet<Assembly>
        {
            entryAssembly, executingAssembly
        };
        var eventAssemblies = new HashSet<Assembly>
        {
            entryAssembly, executingAssembly
        };
        foreach (var additionalAssembly in additionalAssemblies)
        {
            eventAssemblies.Add(additionalAssembly);
        }

        var riderConsumerAssemblies = eventAssemblies
            .SelectMany(a => a.GetTypes())
            .Where(type => type.GetInterfaces().Any(i => i == typeof(IRiderConsumer)))
            .ToArray();

        var nonRiderConsumerAssemblies = eventAssemblies
            .SelectMany(a => a.GetTypes())
            .Where(
                type => type.GetInterfaces().Contains(typeof(IConsumer)) &&
                        !type.GetInterfaces().Contains(typeof(IRiderConsumer)))
            .ToArray();

        b.AddOptions<MassTransitHostOptions>().Configure(
            options =>
            {
                options.WaitUntilStarted = true;
                options.StartTimeout = TimeSpan.FromSeconds(30);
                options.StopTimeout = TimeSpan.FromSeconds(30);
            });

        b.AddSingleton<IServiceBusConfig, ServiceBusConfig>(_ => serviceBusConfig);

        var messageDataService = new MessageDataService(new MassTransitStorageConfig());
        b.AddSingleton<IMessageDataService, MessageDataService>(_ => messageDataService);

        b.AddMassTransit(
            x =>
            {
                additionalMassTransitConfiguration?.Invoke(x);

                x.SetKebabCaseEndpointNameFormatter();
                x.AddConsumers(nonRiderConsumerAssemblies);

                x.AddServiceBusMessageScheduler();

                x.UsingAzureServiceBus(
                    (context, cfg) =>
                    {
                        cfg.UseNewtonsoftJsonSerializer();
                        cfg.UseNewtonsoftJsonDeserializer(true);
                        cfg.ConfigureNewtonsoftJsonSerializer(JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                        cfg.ConfigureNewtonsoftJsonDeserializer(JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                        cfg.UseMessageData(messageDataService.GetAzureStorageMessageDataRepository());

                        foreach (var eventType in eventAssemblies
                                     .SelectMany(a => a.GetTypes())
                                     .Where(type => type.GetInterfaces().Any(i => i == typeof(IEvent))))
                        {
                            var publishMethodInfo = typeof(IServiceBusBusFactoryConfigurator)
                                .GetMethods()
                                .First(m => m.Name == "Publish" && m.IsGenericMethod);
                            var concretePublishMethodInfo = publishMethodInfo!.MakeGenericMethod(eventType);

                            concretePublishMethodInfo.Invoke(
                                cfg,
                                new object?[]
                                {
                                    (Action<dynamic>) (configurator =>
                                    {
                                        configurator.EnablePartitioning = true;
                                        configurator.MaxSizeInMegabytes = 2048;
                                    })
                                });
                        }

                        cfg.UseServiceBusMessageScheduler();
                        cfg.Host(serviceBusConfig.ServiceBusConnStr);
                        cfg.ConfigureEndpoints(context);
                    });

                var eventHubConfig = new EventHubConfig();
                var massTransitStorageConfig = new MassTransitStorageConfig();

                x.AddSingleton<IEventHubConfig, EventHubConfig>(_ => eventHubConfig);
                x.AddSingleton<IMassTransitStorageConfig, MassTransitStorageConfig>(_ => massTransitStorageConfig);

                x.AddRider(
                    rider =>
                    {
                        rider.AddConsumers(riderConsumerAssemblies);

                        rider.UsingEventHub(
                            (context, k) =>
                            {
                                k.Host(eventHubConfig.EventHubConnStr);
                                k.Storage(massTransitStorageConfig.MessageDataConnStr);

                                additionalRiderConfiguration?.Invoke(context, k);
                            });
                    });
            });
#endif
    }

    public static void BuildHighTrafficServiceBusServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<IServiceBusConfig>(new Mock<IServiceBusConfig>().Object);
        b.AddSingleton<IHighTrafficServiceBus>(new Mock<IHighTrafficServiceBus>().Object);
#else
        var serviceBusConfig = new ServiceBusConfig();
        if (string.IsNullOrEmpty(serviceBusConfig.HighTrafficServiceBusConnStr))
        {
            throw new InvalidOperationException("HighTrafficServiceBusConnStr is not set in the configuration");
        }

        var entryAssembly = Assembly.GetCallingAssembly()!;
        var executingAssembly = Assembly.GetExecutingAssembly();
        var eventAssemblies = new HashSet<Assembly>
        {
            entryAssembly, executingAssembly
        };

        var highTrafficEventConsumerAssemblies = eventAssemblies
            .SelectMany(assembly => assembly.GetTypes())
            .Where(
                type => type.GetInterfaces().Any(
                    i =>
                        i.IsGenericType && i.GetGenericTypeDefinition() ==
                        typeof(IHighTrafficConsumer<>)))
            .ToArray();

        b.AddOptions<MassTransitHostOptions>().Configure(
            options =>
            {
                options.WaitUntilStarted = true;
                options.StartTimeout = TimeSpan.FromSeconds(30);
                options.StopTimeout = TimeSpan.FromSeconds(30);
            });

        b.AddSingleton<IServiceBusConfig, ServiceBusConfig>(_ => serviceBusConfig);

        var messageDataService = new MessageDataService(new MassTransitStorageConfig());
        b.AddSingleton<IMessageDataService, MessageDataService>(_ => messageDataService);

        b.AddMassTransit<IHighTrafficServiceBus>(
            x =>
            {
                x.SetKebabCaseEndpointNameFormatter();
                x.AddConsumers(highTrafficEventConsumerAssemblies);
                x.AddServiceBusMessageScheduler();

                x.UsingAzureServiceBus(
                    (context, cfg) =>
                    {
                        cfg.UseNewtonsoftJsonSerializer();
                        cfg.UseNewtonsoftJsonDeserializer(true);
                        cfg.ConfigureNewtonsoftJsonSerializer(JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                        cfg.ConfigureNewtonsoftJsonDeserializer(JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                        cfg.UseMessageData(messageDataService.GetAzureStorageMessageDataRepository());

                        foreach (var eventType in highTrafficEventConsumerAssemblies)
                        {
                            var publishMethodInfo = typeof(IServiceBusBusFactoryConfigurator)
                                .GetMethods()
                                .First(m => m.Name == "Publish" && m.IsGenericMethod);
                            var concretePublishMethodInfo = publishMethodInfo!.MakeGenericMethod(eventType);

                            concretePublishMethodInfo.Invoke(
                                cfg,
                                new object?[]
                                {
                                    (Action<dynamic>) (configurator =>
                                    {
                                        configurator.EnablePartitioning = true;
                                        configurator.MaxSizeInMegabytes = 2048;
                                    })
                                });
                        }

                        cfg.UseServiceBusMessageScheduler();
                        cfg.Host(serviceBusConfig.HighTrafficServiceBusConnStr);
                        cfg.ConfigureEndpoints(context);
                    });
            });
#endif
    }

    public static void BuildServiceBusManager(IServiceCollection b)
    {
        b.AddSingleton<IServiceBusProvider, ServiceBusProvider>();
        b.AddSingleton<IServiceBusManager, ServiceBusManager>();
        b.AddSingleton<ISlidingWindowNextAvailableSlotRateLimiter, ServiceBusManagerWithRateLimit>();
    }

    public static void BuildHttpRequestPollyRetry(IServiceCollection b)
    {
        // Define a resilience strategy for transient HTTP errors
        var retryPolicy = Policy
            .Handle<HttpRequestException>() // Handle network/connection issues
            .Or<TaskCanceledException>() // Handle potential timeouts (if HttpClient configured)
            .OrResult<HttpResponseMessage>(response => // Handle transient server errors
                (int)response.StatusCode >= 500 || response.StatusCode == System.Net.HttpStatusCode.RequestTimeout)
            .WaitAndRetryAsync(
                retryCount: 3, // Number of retries
                sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential backoff (2s, 4s, 8s)
                onRetryAsync: (outcome, timespan, retryAttempt, context) => // Optional: Logging on retry
                {
                    // Attempt to retrieve the logger from the Polly Context
                    ILogger? logger = null; // Default to null
                    if (context.TryGetValue("logger", out object? loggerObject) && loggerObject is ILogger contextLogger)
                    {
                        logger = contextLogger;
                    }

                    // Log using the retrieved logger if available, otherwise fallback or skip
                    if (logger != null)
                    {
                        // Log exception details if an exception occurred
                        if (outcome.Exception != null)
                        {
                            logger.LogError(
                                outcome.Exception,
                                "Polly Retry {RetryAttempt}/{TotalRetries}: Delaying for {DelaySeconds}s due to Exception: {ExceptionMessage}. Retrying HTTP request.",
                                retryAttempt, 3, timespan.TotalSeconds, outcome.Exception.Message);
                        }

                        // Log result details if it was a status code match
                        else if (outcome.Result != null)
                        {
                            logger.LogError(
                                "Polly Retry {RetryAttempt}/{TotalRetries}: Delaying for {DelaySeconds}s due to Status Code: {StatusCode}. Retrying HTTP request.",
                                retryAttempt, 3, timespan.TotalSeconds, outcome.Result.StatusCode);
                        }
                    }

                    return Task.CompletedTask;
                });

        // Register the policy so it can be injected
        b.AddSingleton<IAsyncPolicy<HttpResponseMessage>>(retryPolicy);
    }
}