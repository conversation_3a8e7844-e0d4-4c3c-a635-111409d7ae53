﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.WebScrapers;

public interface IWebScraperRunRepository : IRepository<WebScraperRun>
{
    public Task<WebScraperRun> GetRunAsync(string companyId, string apifyRunId);
}

public class WebScraperRunRepository
    : BaseRepository<WebScraperRun>,
        IWebScraperRunRepository,
        ISingletonService
{
    public WebScraperRunRepository(
        ILogger<WebScraperRunRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<WebScraperRun> GetRunAsync(string companyId, string apifyRunId)
    {
        var runs = await GetObjectsAsync(
            r =>
                r.SleekflowCompanyId == companyId &&
                r.ApifyRunId == apifyRunId);

        if (!runs.Any())
        {
            throw new ArgumentException("Invalid apifyRunId.", apifyRunId);
        }
        else if (runs.Count > 1)
        {
            throw new SfInternalErrorException($"Duplicate Run Founded! runId: {apifyRunId}");
        }

        return runs.First();
    }
}