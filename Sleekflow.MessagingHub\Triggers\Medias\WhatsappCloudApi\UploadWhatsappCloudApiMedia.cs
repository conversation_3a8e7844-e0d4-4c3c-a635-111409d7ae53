using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Blobs;
using Sleekflow.MessagingHub.Models.Blobs;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Medias;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Medias.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Medias)]
public class UploadWhatsappCloudApiMedia
    : ITrigger<
        UploadWhatsappCloudApiMedia.UploadWhatsappCloudApiMediaInput,
        UploadWhatsappCloudApiMedia.UploadWhatsappCloudApiMediaOutput>
{
    private readonly IBlobService _blobService;
    private readonly IWabaService _wabaService;
    private readonly ICloudApiMediaManagementFacade _cloudApiMediaManagementFacade;

    public UploadWhatsappCloudApiMedia(
        IBlobService blobService,
        IWabaService wabaService,
        ICloudApiMediaManagementFacade cloudApiMediaManagementFacade)
    {
        _blobService = blobService;
        _wabaService = wabaService;
        _cloudApiMediaManagementFacade = cloudApiMediaManagementFacade;
    }

    public class UploadWhatsappCloudApiMediaInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("waba_phone_number_id")]
        public string WabaPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonConstructor]
        public UploadWhatsappCloudApiMediaInput(
            string blobId,
            string wabaId,
            string wabaPhoneNumberId,
            string sleekflowCompanyId)
        {
            BlobId = blobId;
            WabaId = wabaId;
            WabaPhoneNumberId = wabaPhoneNumberId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class UploadWhatsappCloudApiMediaOutput
    {
        [JsonProperty("media_id")]
        public string MediaId { get; set; }

        [JsonConstructor]
        public UploadWhatsappCloudApiMediaOutput(string mediaId)
        {
            MediaId = mediaId;
        }
    }

    public async Task<UploadWhatsappCloudApiMediaOutput> F(
        UploadWhatsappCloudApiMediaInput uploadWhatsappCloudApiMediaInput)
    {
        var wabaPhoneNumberId = uploadWhatsappCloudApiMediaInput.WabaPhoneNumberId;
        var sleekflowCompanyId = uploadWhatsappCloudApiMediaInput.SleekflowCompanyId;
        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            uploadWhatsappCloudApiMediaInput.WabaId,
            sleekflowCompanyId,
            wabaPhoneNumberId);

        var wabaPhoneNumber =
            waba.WabaPhoneNumbers.FirstOrDefault(
                w =>
                    w.Id == wabaPhoneNumberId && w.SleekflowCompanyId == sleekflowCompanyId) ??
            throw new SfNotFoundObjectException("WabaPhoneNumber");

        var blobDetail =
            await _blobService.GetBlobDetailAsync(uploadWhatsappCloudApiMediaInput.BlobId, BlobStorageType.External);

        if (!blobDetail.HasValue)
        {
            throw new SfNotFoundObjectException("Unable to locate media");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var blobDownloadResult = blobDetail.Value;
        return new UploadWhatsappCloudApiMediaOutput(
            await _cloudApiMediaManagementFacade.UploadCloudApiMediaAsync(
                wabaPhoneNumber.FacebookPhoneNumberId,
                blobDownloadResult.Content.ToArray(),
                blobDownloadResult.Details.ContentType,
                blobDownloadResult.Details.ContentDisposition ?? uploadWhatsappCloudApiMediaInput.BlobId,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null));
    }
}