using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.IntelligentHub.Consumers;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentHandoffTeamEventConsumerDefinition : ConsumerDefinition<GetAgentHandoffTeamEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentHandoffTeamEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentHandoffTeamEventConsumer : FlowHubAgentGenericConsumer<GetAgentHandoffTeamEvent>, IConsumer<GetAgentHandoffTeamEvent>
{
    private readonly IReviewerService _reviewerService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GetAgentHandoffTeamEventConsumer(
        IBus bus,
        IReviewerService reviewerService,
        ILogger<GetAgentHandoffTeamEventConsumer> logger,
        IIntelligentHubUsageService intelligentHubUsageService)
        : base(logger, bus)
    {
        _reviewerService = reviewerService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    protected override async Task HandleMessageAsync(ConsumeContext<GetAgentHandoffTeamEvent> context)
    {
        var message = context.Message;
        _logger.LogInformation("Agent Handoff Team Request {Message}", JsonConvert.SerializeObject(message));

        var handoffTeam = await _reviewerService.GetHandOffTeamAsync(
            message.ConversationContext,
            message.TeamCategories);

        await _intelligentHubUsageService.RecordUsageAsync(
            message.SleekflowCompanyId,
            PriceableFeatures.HandoffTeam,
            null,
            new HandoffTeamSnapshot(message.ConversationContext, handoffTeam!));

        _logger.LogInformation("Agent Handoff Team: {HandoffTeam}", JsonConvert.SerializeObject(handoffTeam));
        var response = new GetAgentHandoffTeamEvent.Response(handoffTeam!.TeamRecommendation);

        await _bus.Publish(
            new OnAgentCompleteStepActivationEvent(
                message.AggregateStepId,
                message.ProxyStateId,
                message.StackEntries,
                JsonConvert.SerializeObject(response)));
    }
}
