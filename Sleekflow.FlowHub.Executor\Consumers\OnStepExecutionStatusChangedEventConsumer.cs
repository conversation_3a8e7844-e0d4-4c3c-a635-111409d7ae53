﻿using MassTransit;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnStepExecutionStatusChangedEventConsumerDefinition : ConsumerDefinition<OnStepExecutionStatusChangedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnStepExecutionStatusChangedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 4096;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnStepExecutionStatusChangedEventConsumer : IConsumer<OnStepExecutionStatusChangedEvent>, IHighTrafficConsumer<OnStepExecutionStatusChangedEvent>
{
    private readonly IStateService _stateService;
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowStepNodeLocator _workflowStepNodeLocator;
    private readonly IStepExecutionService _stepExecutionService;

    public OnStepExecutionStatusChangedEventConsumer(
        IStateService stateService,
        IWorkflowService workflowService,
        IWorkflowStepNodeLocator workflowStepNodeLocator,
        IStepExecutionService stepExecutionService)
    {
        _stateService = stateService;
        _workflowService = workflowService;
        _workflowStepNodeLocator = workflowStepNodeLocator;
        _stepExecutionService = stepExecutionService;
    }

    public async Task Consume(ConsumeContext<OnStepExecutionStatusChangedEvent> context)
    {
        var @event = context.Message;

        var stateId = @event.StateId;
        var stepId = @event.StepId;
        var stepExecutionStatus = @event.StepExecutionStatus;
        var workerInstanceId = @event.WorkerInstanceId;
        var occurredAt = @event.OccurredAt;
        var error = @event.Error;

        var stateIdentity = await _stateService.GetStateIdentityAsync(stateId);

        var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            stateIdentity.SleekflowCompanyId,
            stateIdentity.WorkflowVersionedId);

        var stepNodeId = _workflowStepNodeLocator.GetStepNodeId(
            workflow,
            stepId);

        await _stepExecutionService.CreateStepExecutionAsync(
            stateId,
            stateIdentity,
            stepId,
            stepNodeId,
            stepExecutionStatus,
            workerInstanceId,
            occurredAt,
            error);
    }
}