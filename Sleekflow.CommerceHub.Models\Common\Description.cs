using System.ComponentModel.DataAnnotations;
using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Common;

public class Description : IValidatableObject
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [SimpleField]
    [ValidateObject]
    [JsonProperty("text", NullValueHandling = NullValueHandling.Include)]
    public Multilingual? Text { get; set; }

    [SimpleField]
    [ValidateObject]
    [JsonProperty("image", NullValueHandling = NullValueHandling.Include)]
    public Image? Image { get; set; }

    [SimpleField]
    [JsonProperty("youtube", NullValueHandling = NullValueHandling.Include)]
    public string? Youtube { get; set; }

    [JsonConstructor]
    public Description(
        string type,
        Multilingual? text,
        Image? image,
        string? youtube)
    {
        Type = type;
        Text = text;
        Image = image;
        Youtube = youtube;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (Type == DescriptionTypes.Text)
        {
            if (Image != null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Image is required for type Text",
                        new[]
                        {
                            nameof(Image)
                        })
                };
            }

            if (Youtube != null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Youtube is required for type Text",
                        new[]
                        {
                            nameof(Youtube)
                        })
                };
            }

            if (Text == null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Text is required for type Text",
                        new[]
                        {
                            nameof(Text)
                        })
                };
            }

            return new List<ValidationResult>();
        }
        else if (Type == DescriptionTypes.Image)
        {
            if (Youtube != null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Youtube is required for type Text",
                        new[]
                        {
                            nameof(Youtube)
                        })
                };
            }

            if (Text != null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Text is required for type Text",
                        new[]
                        {
                            nameof(Text)
                        })
                };
            }

            if (Image == null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Image is required for type Text",
                        new[]
                        {
                            nameof(Image)
                        })
                };
            }

            return new List<ValidationResult>();
        }
        else if (Type == DescriptionTypes.Youtube)
        {
            if (Image != null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Image is required for type Text",
                        new[]
                        {
                            nameof(Image)
                        })
                };
            }

            if (Text != null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Text is required for type Text",
                        new[]
                        {
                            nameof(Text)
                        })
                };
            }

            if (Youtube == null)
            {
                return new List<ValidationResult>
                {
                    new (
                        "Youtube is required for type Text",
                        new[]
                        {
                            nameof(Youtube)
                        })
                };
            }

            return new List<ValidationResult>();
        }

        var results = new List<ValidationResult>
        {
            new (
                "The type is invalid",
                new List<string>
                {
                    nameof(Type)
                })
        };

        return results;
    }
}