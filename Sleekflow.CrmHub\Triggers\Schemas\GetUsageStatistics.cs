﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.SchemafulObjects.Utils;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Schemas;

[TriggerGroup(TriggerGroups.Schemas)]
public class GetUsageStatistics : ITrigger<GetUsageStatistics.GetUsageStatisticsInput, GetUsageStatistics.GetUsageStatisticsOutput>
{
    private readonly ISchemafulObjectValidator _schemafulObjectValidator;

    public GetUsageStatistics(ISchemafulObjectValidator schemafulObjectValidator)
    {
        _schemafulObjectValidator = schemafulObjectValidator;
    }

    public class GetUsageStatisticsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetUsageStatisticsInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetUsageStatisticsOutput
    {
        [JsonProperty("custom_object_schema_num")]
        public int CustomObjectSchemaNum { get; set; }

        [JsonProperty("custom_object_schemaful_object_num_per_company")]
        public int CustomObjectSchemafulObjectNumPerCompany { get; set; }

        [JsonConstructor]
        public GetUsageStatisticsOutput(int customObjectSchemaNum, int customObjectSchemafulObjectNumPerCompany)
        {
            CustomObjectSchemaNum = customObjectSchemaNum;
            CustomObjectSchemafulObjectNumPerCompany = customObjectSchemafulObjectNumPerCompany;
        }
    }

    public async Task<GetUsageStatisticsOutput> F(GetUsageStatisticsInput getUsageStatisticsInput)
    {
        var customObjectSchemaNum =
            await _schemafulObjectValidator.GetSchemaNumForUsageLimit(getUsageStatisticsInput.SleekflowCompanyId);

        var customObjectSchemafulObjectNumPerCompany =
            await _schemafulObjectValidator.GetSchemafulObjectNumPerCompanyForUsageLimitAsync(getUsageStatisticsInput.SleekflowCompanyId);

        return new GetUsageStatisticsOutput(customObjectSchemaNum, customObjectSchemafulObjectNumPerCompany);
    }
}