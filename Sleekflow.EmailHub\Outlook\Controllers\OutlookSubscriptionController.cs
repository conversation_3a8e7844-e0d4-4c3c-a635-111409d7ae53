using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Outlook.Communications;
using Sleekflow.EmailHub.Models.Outlook.Events;

namespace Sleekflow.EmailHub.Outlook.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class OutlookSubscriptionController : ControllerBase
{
    private readonly ILogger<OutlookSubscriptionController> _logger;
    private readonly IBus _bus;

    public OutlookSubscriptionController(
        ILogger<OutlookSubscriptionController> logger,
        IBus bus)
    {
        _logger = logger;
        _bus = bus;
    }

    [HttpPost]
    [Produces("text/plain")]
    [Route("OutlookSubscriptionCallBack")]
    public async Task<IActionResult> OutlookSubscriptionCallBack()
    {
        var validationToken = HttpContext.Request.Query["validationToken"].ToString();
        if (!string.IsNullOrEmpty(validationToken))
        {
            _logger.LogInformation(
                "OutlookSubscriptionCallBack is validating notificationUrl, validationToken:{token}",
                validationToken);
            return Ok(validationToken);
        }

        using var reader = new StreamReader(Request.Body);
        var content = await reader.ReadToEndAsync();

        var changeNotifications = JsonConvert.DeserializeObject<OutlookChangeNotifications>(content) !;
        _logger.LogInformation(
            "OutlookSubscriptionCallBack received change notification: payload{payload}",
            content);
        foreach (var notification in changeNotifications.ChangeNotifications)
        {
            await _bus.Publish(new OnOutlookChangeNotificationReceivedEvent(
                notification.ClientState,
                notification));
        }

        return Ok();
    }
}