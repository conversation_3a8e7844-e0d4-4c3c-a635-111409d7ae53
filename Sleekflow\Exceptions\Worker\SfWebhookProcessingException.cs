﻿namespace Sleekflow.Exceptions.Worker;

public class SfWebhookProcessingException : ErrorCodeException
{
    public string WebhookId { get; }

    public SfWebhookProcessingException(string webhookId)
        : base(
            ErrorCodeConstant.SfWebhookProcessingException,
            $"Unable to process the webhook",
            new Dictionary<string, object?>
            {
                {
                    "webhookId", webhookId
                }
            })
    {
        WebhookId = webhookId;
    }
}