using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Documents;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Workers;

public class StartUploadToAgentKnowledgeBasesEvent : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("document_id")]
    public string DocumentId { get; set; }

    [JsonConstructor]
    public StartUploadToAgentKnowledgeBasesEvent(
        string sleekflowCompanyId,
        string documentId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        DocumentId = documentId;
    }
}