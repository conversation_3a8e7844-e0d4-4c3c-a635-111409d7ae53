namespace Sleekflow.Locks;

public class InMemoryLockService : ILockService
{
    private static readonly Dictionary<string, Lock> Locks = new ();
    private static readonly SemaphoreSlim SyncLock = new (1, 1);

    public Task<(bool IsLocked, TimeSpan? ExpireIn)> IsLockedAsync(
        string[] strings,
        CancellationToken cancellationToken = default)
    {
        var key = string.Concat(strings);

        var isLockExists = Locks.TryGetValue(key, out _);

        return isLockExists
            ? Task.FromResult((true, null as TimeSpan?))
            : Task.FromResult((false, null as TimeSpan?));
    }

    public async Task<Lock?> LockAsync(
        string[] strings,
        TimeSpan minimumDuration,
        CancellationToken cancellationToken = default)
    {
        var key = string.Concat(strings);

        await SyncLock.WaitAsync(cancellationToken);

        try
        {
            if (Locks.ContainsKey(key))
            {
                return null;
            }

            var newLock = new Lock(key, Convert.ToInt32(minimumDuration.TotalSeconds))
            {
                ETag = string.Empty
            };

            Locks.TryAdd(key, newLock);

            _ = Task
                .Delay(minimumDuration, cancellationToken)
                .ContinueWith(async _ => await ReleaseAsync(newLock, cancellationToken));

            return newLock;
        }
        finally
        {
            SyncLock.Release();
        }
    }

    public async Task<Lock> WaitUnitLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
        cts.CancelAfter(maximumWaitDuration);

        var token = cts.Token;

        while (true)
        {
            var lockAcquired = await LockAsync(strings, minimumLockDuration, token);
            if (lockAcquired != null)
            {
                return lockAcquired;
            }

            await Task.Delay(100, token);
        }
    }

    public async Task<int> ReleaseAsync(Lock @lock, CancellationToken cancellationToken = default)
    {
        await SyncLock.WaitAsync(cancellationToken);

        int releasedCount = 0;
        try
        {
            Locks.TryGetValue(@lock.Id, out var existingLock);

            if (existingLock == @lock)
            {
                Locks.Remove(@lock.Id, out _);
                releasedCount++;
            }
        }
        finally
        {
            SyncLock.Release();
        }

        return releasedCount;
    }

    public Task AcquireReadLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task AcquireWriteLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<bool> ReleaseReadLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public Task<bool> ReleaseWriteLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}