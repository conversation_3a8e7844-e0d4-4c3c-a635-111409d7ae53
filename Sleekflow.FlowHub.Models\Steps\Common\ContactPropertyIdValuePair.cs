﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Steps.Common;

public class ContactPropertyIdValuePair
{
    [JsonProperty("property_id")]
    public string PropertyId { get; set; }

    [JsonProperty("property_value__expr")]
    public string? PropertyValueExpr { get; set; }

    [JsonConstructor]
    public ContactPropertyIdValuePair(
        string propertyId,
        string? propertyValueExpr)
    {
        PropertyId = propertyId;
        PropertyValueExpr = propertyValueExpr;
    }
}