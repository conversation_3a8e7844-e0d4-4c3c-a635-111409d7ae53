using Microsoft.AspNetCore.Cryptography.KeyDerivation;
using Newtonsoft.Json;

namespace Sleekflow.Auth0.BulkImporter;

public class DecodePasswordHashOutput
{
    [JsonProperty("salt")]
    public byte[] Salt { get; set; }

    [JsonProperty("prf")]
    public KeyDerivationPrf? Prf { get; set; }

    [JsonProperty("iterCoun")]
    public int IterCount { get; set; }

    [JsonProperty("subkeyLength")]
    public int SubkeyLength { get; set; }

    // public byte[] subkey { get; set; }
    [JsonProperty("expectedSubkey")]
    public byte[] ExpectedSubkey { get; set; }

    [JsonConstructor]
    public DecodePasswordHashOutput(byte[] salt, KeyDerivationPrf prf, int iterCount, int subkeyLength, byte[] subkey)
    {
        Salt = salt;
        Prf = prf;
        IterCount = iterCount;
        SubkeyLength = subkeyLength;
        ExpectedSubkey = subkey;
    }
}