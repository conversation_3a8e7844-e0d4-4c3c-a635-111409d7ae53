using Newtonsoft.Json;

namespace Sleekflow.UserEventHub.Models.Notifications;

public class DeviceRegistration
{
    public const string PropertyNameHandle = "handle";
    public const string PropertyNameTags = "tags";
    public const string PropertyNameDeviceId = "deviceId";
    public const string PropertyNamePlatform = "platform";

    public const string PropertyNameHubName = "hub_name";

    [JsonProperty(PropertyNameHandle)]
    public string Handle { get; set; }

    [JsonProperty(PropertyNameTags)]
    public string[] Tags { get; set; }

    [JsonProperty(PropertyNamePlatform)]
    public string? Platform { get; set; }

    [JsonProperty(PropertyNameHubName)]
    public string? HubName { get; set; }

    [JsonConstructor]
    public DeviceRegistration(string handle, string[] tags, string? platform, string? hubName)
    {
        Handle = handle;
        Tags = tags;
        Platform = platform;
        HubName = hubName;
    }
}