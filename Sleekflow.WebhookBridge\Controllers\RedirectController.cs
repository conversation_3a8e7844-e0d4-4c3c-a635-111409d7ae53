﻿using Microsoft.AspNetCore.Mvc;
using Sleekflow.WebhookBridge.Services;

namespace Sleekflow.WebhookBridge.Controllers;

[ApiController]
[Route("[Controller]")]
public class RedirectController : ControllerBase
{
    private readonly IHttpRedirectService _httpRedirectService;

    public RedirectController(IHttpRedirectService httpRedirectService)
    {
        _httpRedirectService = httpRedirectService;
    }

    [AcceptVerbs("GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD")]
    public async Task<IActionResult> Redirect()
    {
        await _httpRedirectService.Redirect(Request);

        return new OkResult();
    }
}