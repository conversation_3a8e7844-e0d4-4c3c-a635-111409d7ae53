using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents;
using Sleekflow.IntelligentHub.FaqAgents.ChatContexts;
using Sleekflow.IntelligentHub.FaqAgents.Configs;
using Sleekflow.IntelligentHub.HeadlessWhatsappIntegrations;

namespace Sleekflow.IntelligentHub.Controllers;

[ApiVersion("1.0")]
[Controller]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    private const string ClearContextReply =
        "Oh, of course! Let me just wave my magic wand and erase everything we've discussed so far. Poof! All clear and ready for a fresh start. Let's dive into that new topic, shall we?";

    private readonly IChatContextService _chatContextService;
    private readonly IHeadlessWhatsappMessageIntegrationService _headlessWhatsappMessageIntegrationService;
    private readonly IFaqAgentFileProcessingService _faqAgentFileProcessingService;
    private readonly IFaqAgentConfigService _faqAgentConfigService;
    private readonly OnHeadlessWhatsappMessageReceivedEventConsumer _onHeadlessWhatsappMessageReceivedEventConsumer;

    public PublicController(
        IChatContextService chatContextService,
        IHeadlessWhatsappMessageIntegrationService headlessWhatsappMessageIntegrationService,
        IFaqAgentFileProcessingService faqAgentFileProcessingService,
        IFaqAgentConfigService faqAgentConfigService,
        OnHeadlessWhatsappMessageReceivedEventConsumer onHeadlessWhatsappMessageReceivedEventConsumer)
    {
        _chatContextService = chatContextService;
        _headlessWhatsappMessageIntegrationService = headlessWhatsappMessageIntegrationService;
        _faqAgentFileProcessingService = faqAgentFileProcessingService;
        _faqAgentConfigService = faqAgentConfigService;
        _onHeadlessWhatsappMessageReceivedEventConsumer = onHeadlessWhatsappMessageReceivedEventConsumer;
    }

    [HttpPost]
    [Route("chat/{sleekflowCompanyId}")]
    public async Task Chat(string sleekflowCompanyId)
    {
        var body = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();

        dynamic? data = JsonConvert.DeserializeObject(body);

        string? phoneNumber = data?.value?.contacts?[0]?.wa_id;
        string? message = data?.value?.messages?[0]?.text?.body;
        string? timestamp = data?.value?.messages?[0]?.timestamp;

        if (string.IsNullOrEmpty(phoneNumber) || string.IsNullOrEmpty(message) || string.IsNullOrEmpty(timestamp))
        {
            return;
        }

        var isCleared = Regex.IsMatch(message, @"^/clear$");

        var contextId = await _chatContextService.GetContextIdAsync(sleekflowCompanyId, phoneNumber, isCleared);
        var agentConfigOrDefault = _faqAgentConfigService.GetAgentConfigOrDefault(sleekflowCompanyId);

        if (agentConfigOrDefault == null)
        {
            return;
        }

        if (isCleared)
        {
            await _headlessWhatsappMessageIntegrationService.SendMessageAsync(
                phoneNumber,
                ClearContextReply,
                sleekflowCompanyId);
        }
        else
        {
#if DEBUG
            await _onHeadlessWhatsappMessageReceivedEventConsumer.ReplyAsync(
                phoneNumber,
                sleekflowCompanyId,
                contextId,
                CancellationToken.None,
                message,
                DateTimeOffset.FromUnixTimeSeconds(long.Parse(timestamp)).ToUniversalTime());
#else
            await _headlessWhatsappMessageIntegrationService.OnMessageReceivedAsync(
                sleekflowCompanyId,
                contextId,
                phoneNumber,
                message,
                DateTimeOffset.FromUnixTimeSeconds(long.Parse(timestamp)).ToUniversalTime());
#endif
        }
    }

    [HttpPost]
    [Route("process-files/{sleekflowCompanyId}")]
    public async Task ProcessFiles(string sleekflowCompanyId)
    {
        await _faqAgentFileProcessingService.ProcessFilesAsync(sleekflowCompanyId);
    }

    [HttpGet]
    [Route("healthz")]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }
}