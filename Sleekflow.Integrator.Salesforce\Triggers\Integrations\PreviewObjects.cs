using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class PreviewObjects : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public PreviewObjects(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class PreviewObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public PreviewObjectsInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class PreviewObjectsOutput
    {
        [JsonProperty("objects")]
        [Required]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonConstructor]
        public PreviewObjectsOutput(
            List<Dictionary<string, object?>> objects)
        {
            Objects = objects;
        }
    }

    public async Task<PreviewObjectsOutput> F(
        PreviewObjectsInput previewObjectsInput)
    {
        var authentication =
            await _salesforceAuthenticationService.GetAsync(previewObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (objects, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            previewObjectsInput.EntityTypeName,
            previewObjectsInput.FilterGroups,
            previewObjectsInput.FieldFilters);

        return new PreviewObjectsOutput(objects);
    }
}