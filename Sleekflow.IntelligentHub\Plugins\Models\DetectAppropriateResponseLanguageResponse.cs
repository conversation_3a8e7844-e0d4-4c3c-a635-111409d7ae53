using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Plugins.Models;

public class DetectAppropriateResponseLanguageResponse
{
    [JsonProperty("reasoning")]
    public string Reasoning { get; set; }

    [JsonProperty("responseLanguageCode")]
    public string ResponseLanguageCode { get; set; }

    [JsonProperty("responseLanguageName")]
    public string ResponseLanguageName { get; set; }

    [JsonConstructor]
    public DetectAppropriateResponseLanguageResponse(string reasoning, string responseLanguageCode, string responseLanguageName)
    {
        Reasoning = reasoning;
        ResponseLanguageCode = responseLanguageCode;
        ResponseLanguageName = responseLanguageName;
    }
}