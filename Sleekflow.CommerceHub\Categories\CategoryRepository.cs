using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.CommerceHub.Categories;

public interface ICategoryRepository : IDynamicFiltersRepository<Category>
{
    Task<Category?> GetCategoryOrDefaultAsync(string categoryId, string sleekflowCompanyId, string storeId);

    Task<List<Category>> GetCategoriesAsync(string sleekflowCompanyId, string storeId);

    Task<List<Category>> GetCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        List<string> categoryIds,
        int limit);

    Task<(List<Category> Categories, string? NextContinuationToken)> GetCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        string? continuationToken,
        int limit);

    Task<Category> PatchAndGetCategoryAsync(
        Category category,
        List<Multilingual> names,
        List<Description> descriptions,
        AuditEntity.SleekflowStaff sleekflowStaff);
}

public class CategoryRepository : DynamicFiltersBaseRepository<Category>, ICategoryRepository, IScopedService
{
    public CategoryRepository(
        ILogger<CategoryRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }

    public async Task<Category?> GetCategoryOrDefaultAsync(string categoryId, string sleekflowCompanyId, string storeId)
    {
        return (await GetObjectsAsync(
                c =>
                    c.Id == categoryId
                    && c.SleekflowCompanyId == sleekflowCompanyId
                    && c.StoreId == storeId))
            .FirstOrDefault();
    }

    public Task<List<Category>> GetCategoriesAsync(string sleekflowCompanyId, string storeId)
    {
        return GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.StoreId == storeId);
    }

    public Task<List<Category>> GetCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        List<string> categoryIds,
        int limit)
    {
        return GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.StoreId == storeId
                && categoryIds.Contains(c.Id),
            limit);
    }

    public Task<(List<Category> Categories, string? NextContinuationToken)> GetCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        string? continuationToken,
        int limit)
    {
        return GetContinuationTokenizedObjectsAsync(
            category =>
                category.SleekflowCompanyId == sleekflowCompanyId
                && category.StoreId == storeId,
            continuationToken,
            limit);
    }

    public async Task<Category> PatchAndGetCategoryAsync(
        Category category,
        List<Multilingual> names,
        List<Description> descriptions,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        return await PatchAndGetAsync(
            category.Id,
            category.SleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace(Category.PropertyNameNames, names),
                Replace(Category.PropertyNameDescriptions, descriptions),
                Replace(AuditEntity.PropertyNameUpdatedBy, sleekflowStaff),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
            });
    }
}