using Sleekflow.Exceptions;

namespace Sleekflow.Configs;

public interface IMassTransitStorageConfig
{
    string MessageDataConnStr { get; }

    string MessageDataContainerName { get; }
}

public class MassTransitStorageConfig : IMassTransitStorageConfig
{
    public string MessageDataConnStr { get; }

    public string MessageDataContainerName { get; }

    public MassTransitStorageConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        MessageDataConnStr = Environment.GetEnvironmentVariable("MESSAGE_DATA_CONN_STR", target) ??
                             throw new SfMissingEnvironmentVariableException("MESSAGE_DATA_CONN_STR");
        MessageDataContainerName = Environment.GetEnvironmentVariable("MESSAGE_DATA_CONTAINER_NAME", target) ??
                                   throw new SfMissingEnvironmentVariableException("MESSAGE_DATA_CONTAINER_NAME");
    }
}