using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

public class ConversationUsageInsertState : IHasUpdatedAt
{
    public const string PropertyNameLastConversationUsageInsertTimestamp = "last_conversation_usage_insert_timestamp";
    public const string PropertyNameWabaConversationInsertionExceptions = "waba_conversation_insertion_exception";

    [JsonProperty(PropertyNameLastConversationUsageInsertTimestamp)]
    public long LastConversationUsageInsertTimestamp { get; set; }

    [JsonProperty(PropertyNameWabaConversationInsertionExceptions)]
    public Dictionary<string, WabaConversationInsertionException> WabaConversationInsertionExceptions { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public ConversationUsageInsertState(
        long lastConversationUsageInsertTimestamp,
        DateTimeOffset updatedAt,
        Dictionary<string, WabaConversationInsertionException> wabaConversationInsertionExceptions)
    {
        LastConversationUsageInsertTimestamp = lastConversationUsageInsertTimestamp;
        UpdatedAt = updatedAt;
        WabaConversationInsertionExceptions = wabaConversationInsertionExceptions;
    }
}