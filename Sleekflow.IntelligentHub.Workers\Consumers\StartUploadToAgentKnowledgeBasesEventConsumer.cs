using MassTransit;
using Microsoft.Extensions.Logging;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.Workers.Consumers;

public class StartUploadToAgentKnowledgeBasesEventConsumer : IConsumer<StartUploadToAgentKnowledgeBasesEvent>
{
    private readonly ILogger<StartUploadToAgentKnowledgeBasesEventConsumer> _logger;

    public StartUploadToAgentKnowledgeBasesEventConsumer(
        ILogger<StartUploadToAgentKnowledgeBasesEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<StartUploadToAgentKnowledgeBasesEvent> context)
    {
        _logger.LogInformation("Event received: StartUploadToAgentKnowledgeBasesEvent");
    }
}