﻿using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas.Dtos;
using Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.Schemas.Utils;

public interface IPropertyConstructor
{
    /// <summary>
    /// Construct a property from the given PropertyInput.
    /// </summary>
    /// <param name="propertyInput">propertyInput.</param>
    /// <returns>Property.</returns>
    Property Construct(PropertyInput propertyInput);

    /// <summary>
    /// Construct a property from the given Property<br />
    /// Used in<br />
    ///  1. <see cref="ISchemaService.UpdateAndGetAsync"/>><br />
    ///  2. <see cref="IDataTypeInnerSchemaService.Create"/>>.
    /// </summary>
    /// <param name="receivedProperty">propertyInput.</param>
    /// <returns>Property.</returns>
    Property Construct(Property receivedProperty);

    /// <summary>
    /// Update the property according to the received property
    /// Used in<br />
    ///  1. <see cref="ISchemaService.UpdateAndGetAsync"/>><br />
    ///  2. <see cref="IDataTypeInnerSchemaService.Update"/>>.
    /// </summary>
    /// <param name="originalProperty">Original property.</param>
    /// <param name="receivedProperty">Received property for update.</param>
    /// <returns>UpdatePropertyChangeContext.</returns>
    UpdatePropertyChangeContext Update(Property originalProperty, Property receivedProperty);
}

public class PropertyConstructor : IPropertyConstructor, ISingletonService
{
    private readonly IIdService _idService;
    private readonly ISingleChoicePropertyHook _singleChoicePropertyHook;
    private readonly IMultipleChoicePropertyHook _multipleChoicePropertyHook;
    private readonly IArrayObjectPropertyHook _arrayObjectPropertyHook;
    private readonly IDefaultPropertyHook _defaultPropertyHook;

    public PropertyConstructor(
        IIdService idService,
        ISingleChoicePropertyHook singleChoicePropertyHook,
        IMultipleChoicePropertyHook multipleChoicePropertyHook,
        IArrayObjectPropertyHook arrayObjectPropertyHook,
        IDefaultPropertyHook defaultPropertyHook)
    {
        _idService = idService;
        _singleChoicePropertyHook = singleChoicePropertyHook;
        _multipleChoicePropertyHook = multipleChoicePropertyHook;
        _arrayObjectPropertyHook = arrayObjectPropertyHook;
        _defaultPropertyHook = defaultPropertyHook;
    }

    public Property Construct(PropertyInput propertyInput)
    {
        var propertyHook = GetPropertyHook(propertyInput.DataType);

        var (dataType, options) = propertyHook.PreConstruct(propertyInput);

        return new Property(
            _idService.GetId(SysTypeNames.SchemaProperty),
            propertyInput.DisplayName,
            propertyInput.UniqueName,
            dataType,
            propertyInput.IsRequired,
            propertyInput.IsVisible,
            propertyInput.IsPinned,
            propertyInput.IsSearchable,
            propertyInput.DisplayOrder,
            propertyInput.CreatedBy,
            DateTimeOffset.UtcNow,
            options);
    }

    public Property Construct(Property receivedProperty)
    {
        var propertyHook = GetPropertyHook(receivedProperty.DataType);

        var (dataType, options) = propertyHook.PreConstruct(receivedProperty);

        return new Property(
            _idService.GetId(SysTypeNames.SchemaProperty),
            receivedProperty.DisplayName,
            receivedProperty.UniqueName,
            dataType,
            receivedProperty.IsRequired,
            receivedProperty.IsVisible,
            receivedProperty.IsPinned,
            receivedProperty.IsSearchable,
            receivedProperty.DisplayOrder,
            receivedProperty.CreatedBy,
            DateTimeOffset.UtcNow,
            options);
    }

    public UpdatePropertyChangeContext Update(Property originalProperty, Property receivedProperty)
    {
        var propertyHook = GetPropertyHook(receivedProperty.DataType);

        var updatePropertyChangeContext = propertyHook.PreUpdate(originalProperty, receivedProperty);

        originalProperty.DisplayName = receivedProperty.DisplayName;
        originalProperty.IsVisible = receivedProperty.IsVisible;
        originalProperty.IsPinned = receivedProperty.IsPinned;
        originalProperty.DisplayOrder = receivedProperty.DisplayOrder;
        originalProperty.IsRequired = receivedProperty.IsRequired;

        return updatePropertyChangeContext;
    }

    private IPropertyHook GetPropertyHook(IDataType dataType) =>
        dataType.Name switch
        {
            SchemaPropertyDataTypes.SingleChoice => _singleChoicePropertyHook,
            SchemaPropertyDataTypes.MultipleChoice => _multipleChoicePropertyHook,
            SchemaPropertyDataTypes.ArrayObject => _arrayObjectPropertyHook,
            _ => _defaultPropertyHook
        };
}