using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface ILeadNurturingDataPane
{
    Task<string> StoreData(
        [Description("Unique identifier for the conversation session")]
        string sessionId,
        [Description("Type of data being stored (classification, decision, strategy, knowledge, etc.)")]
        string dataType,
        [Description("The data to store as JSON string")]
        string data,
        [Description("Optional metadata about the data")]
        string? metadata = null);

    Task<string> GetData(
        [Description("Unique identifier for the conversation session")]
        string sessionId,
        [Description("Type of data to retrieve (classification, decision, strategy, knowledge, etc.)")]
        string dataType);

    Task<string> ListSessionData(
        [Description("Unique identifier for the conversation session")]
        string sessionId);

    Task<string> ClearSessionData(
        [Description("Unique identifier for the conversation session")]
        string sessionId);
}

public class LeadNurturingDataPane : IScopedService, ILeadNurturingDataPane
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ICacheConfig _cacheConfig;
    private readonly ILogger<LeadNurturingDataPane> _logger;
    private static readonly TimeSpan DataExpiration = TimeSpan.FromMinutes(30);

    public LeadNurturingDataPane(
        IConnectionMultiplexer connectionMultiplexer,
        ICacheConfig cacheConfig,
        ILogger<LeadNurturingDataPane> logger)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _cacheConfig = cacheConfig;
        _logger = logger;
    }

    [KernelFunction("store_data"), Description("Store data in the data pane for access by other tools")]
    public async Task<string> StoreData(
        [Description("Unique identifier for the conversation session")]
        string sessionId,
        [Description("Type of data being stored (classification, decision, strategy, knowledge, etc.)")]
        string dataType,
        [Description("The data to store as JSON string")]
        string data,
        [Description("Optional metadata about the data")]
        string? metadata = null)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var key = GetDataKey(sessionId, dataType);

            var dataRecord = new DataPaneRecord
            {
                SessionId = sessionId,
                DataType = dataType,
                Data = data,
                Metadata = metadata,
                Timestamp = DateTimeOffset.UtcNow
            };

            await database.StringSetAsync(
                key,
                JsonConvert.SerializeObject(dataRecord, JsonConfig.DefaultJsonSerializerSettings),
                DataExpiration);

            _logger.LogInformation("Stored {DataType} for session {SessionId}", dataType, sessionId);
            return "Success: Data stored successfully";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store {DataType} for session {SessionId}", dataType, sessionId);
            return $"Error: Failed to store data - {ex.Message}";
        }
    }

    [KernelFunction("get_data"), Description("Retrieve data from the data pane")]
    public async Task<string> GetData(
        [Description("Unique identifier for the conversation session")]
        string sessionId,
        [Description("Type of data to retrieve (classification, decision, strategy, knowledge, etc.)")]
        string dataType)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var key = GetDataKey(sessionId, dataType);

            var result = await database.StringGetAsync(key);
            if (!result.HasValue)
            {
                return $"No {dataType} data found for session {sessionId}";
            }

            var dataRecord = JsonConvert.DeserializeObject<DataPaneRecord>(
                result!,
                JsonConfig.DefaultJsonSerializerSettings);

            return dataRecord?.Data ?? $"Empty {dataType} data for session {sessionId}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve {DataType} for session {SessionId}", dataType, sessionId);
            return $"Error: Failed to retrieve data - {ex.Message}";
        }
    }

    [KernelFunction("list_session_data"), Description("List all data types available for a session")]
    public async Task<string> ListSessionData(
        [Description("Unique identifier for the conversation session")]
        string sessionId)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var pattern = GetDataKeyPattern(sessionId);

            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern).ToList();

            var dataTypes = keys.Select(key =>
                key.ToString().Split(':').Last()).ToList();

            return JsonConvert.SerializeObject(dataTypes, JsonConfig.DefaultJsonSerializerSettings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list session data for {SessionId}", sessionId);
            return $"Error: Failed to list session data - {ex.Message}";
        }
    }

    [KernelFunction("clear_session_data"), Description("Clear all data for a session")]
    public async Task<string> ClearSessionData(
        [Description("Unique identifier for the conversation session")]
        string sessionId)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var pattern = GetDataKeyPattern(sessionId);

            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern).ToArray();

            if (keys.Length > 0)
            {
                await database.KeyDeleteAsync(keys);
            }

            _logger.LogInformation("Cleared {Count} data entries for session {SessionId}", keys.Length, sessionId);
            return $"Success: Cleared {keys.Length} data entries";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear session data for {SessionId}", sessionId);
            return $"Error: Failed to clear session data - {ex.Message}";
        }
    }

    private string GetDataKey(string sessionId, string dataType)
    {
        return $"{_cacheConfig.CachePrefix}:lead-nurturing-data:{sessionId}:{dataType}";
    }

    private string GetDataKeyPattern(string sessionId)
    {
        return $"{_cacheConfig.CachePrefix}:lead-nurturing-data:{sessionId}:*";
    }

    public class DataPaneRecord
    {
        public string SessionId { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public string Data { get; set; } = string.Empty;
        public string? Metadata { get; set; }
        public DateTimeOffset Timestamp { get; set; }
    }
}

// Data types stored in the data pane
public static class DataPaneTypes
{
    public const string Classification = "classification";
    public const string Decision = "decision";
    public const string Strategy = "strategy";
    public const string Knowledge = "knowledge";
    public const string AssignmentPlan = "assignment_plan";
    public const string DemoPlan = "demo_plan";
    public const string ExecutionResult = "execution_result";
    public const string ResponseHistory = "response_history";
    public const string FieldStatus = "field_status";
}