﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Integrator.FlowHubs;
using Sleekflow.FlowHub.Integrator.Integrations;
using Sleekflow.FlowHub.Integrator.Utils;
using Sleekflow.FlowHub.Models.Constants.FlowHubIntegrators;
using Sleekflow.Ids;

namespace Sleekflow.FlowHub.Integrator.Integrators.ZapierTriggerIntegrator;

public interface IZapierTriggerOnSchemafulObjectUpdatedIntegrator
{
    Task CreateIntegrationAsync(string sleekflowCompanyId, string zapId, string hookUrl, string? sleekflowLocation, string schemaUniqueName);

    Task DeleteIntegrationAsync(string sleekflowCompanyId, string zapId);

    Task<List<object>> GetSampleAsync(string sleekflowCompanyId, string? sleekflowLocation, string schemaUniqueName);
}

public class ZapierTriggerOnSchemafulObjectUpdatedIntegrator : ZapierTriggerIntegratorBase, IZapierTriggerOnSchemafulObjectUpdatedIntegrator, IScopedService
{
    protected override string TriggerName => ZapierTriggerNames.OnSchemafulObjectUpdated;

    protected override string WorkflowTriggers { get; set; } =
        """
{"schemaful_object_updated":{"condition":"{{ ((event_body.schema_id | string.contains \"[[SCHEMA_ID]]\")) }}"}}
""";

    protected override string WorkflowSteps { get; set; } =
        """
[{"id":"setup-contact-and-conversation","name":"setup","next_step_id":null,"assign":{"contact":"{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact) }}","lists":"{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_lists) }}","conversation":"{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_conversation) }}"}},
{"id":"setup-contact-owner","name":"setup-contact-owner","next_step_id":null,"assign":{"contactOwner":"{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"}},
{"call":"http.post","args":{"url__expr":"{{ \"[[ZAP_HOOK_URL]]\" }}","headers__key_expr_dict":{"Content-Type":"application/json"},"body__key_expr_dict":null,"body__expr":"{{ '{\\n\\\"primaryPropertyValue\\\": \"{{ trigger_event_body.primary_property_value }}\",\\n\\\"propertyValues\\\": {{ trigger_event_body.property_values | json.serialize }},\\n\\\"referencedContactPhoneNumber\\\": \"{{usr_var_dict.contact[\\\"PhoneNumber\\\"] ?? usr_var_dict[\\\"PhoneNumber\\\"] ?? \\\"\\\"}}\",\\n\\\"referencedUserProfileId\\\": \"{{ trigger_event_body.contact_id }}\"\\n}' | template.eval }}"},"id":"30f080fe-f427-42c6-9389-d242358dc8d7","name":"Action 1","assign":null,"next_step_id":"6b1976cd-b480-436c-9808-d54151482e76"},
{"id":"6b1976cd-b480-436c-9808-d54151482e76","name":"end","assign":null,"next_step_id":null}]
""";

    protected override string? WorkflowMetadata { get; set; } = null;

    public ZapierTriggerOnSchemafulObjectUpdatedIntegrator(
        ILogger<ZapierTriggerOnSchemafulObjectUpdatedIntegrator> logger,
        IInternalWorkflowService internalWorkflowService,
        IZapierTriggerIntegrationService integrationService,
        IIdService idService)
        : base(logger, internalWorkflowService, integrationService, idService)
    {
    }

    public async Task CreateIntegrationAsync(
        string sleekflowCompanyId,
        string zapId,
        string hookUrl,
        string? sleekflowLocation,
        string schemaUniqueName)
    {
        var getSchemaIdByUniqueNameOutput = await TravisBackendFunctionsUtil
            .InvokeAsync<TravisBackendFunctionsUtil.GetSchemaIdByUniqueNameInput, TravisBackendFunctionsUtil.GetSchemaIdByUniqueNameOutput>(
                sleekflowLocation,
                "GetSchemaIdByUniqueName",
                new TravisBackendFunctionsUtil.GetSchemaIdByUniqueNameInput(schemaUniqueName, sleekflowCompanyId));

        WorkflowTriggers = WorkflowTriggers.Replace("[[SCHEMA_ID]]", getSchemaIdByUniqueNameOutput.SchemaId);
        WorkflowSteps = WorkflowSteps.Replace("[[ZAP_HOOK_URL]]", hookUrl);

        await IntegrateToFlowBuilderAsync(sleekflowCompanyId, zapId, hookUrl, sleekflowLocation);
    }

    public async Task DeleteIntegrationAsync(string sleekflowCompanyId, string zapId)
    {
        await SegregateWithFlowBuilderAsync(sleekflowCompanyId, zapId);
    }

    public async Task<List<object>> GetSampleAsync(string sleekflowCompanyId, string? sleekflowLocation, string schemaUniqueName)
    {
        var getSchemafulObjectZapierSampleOutput = await TravisBackendFunctionsUtil
            .InvokeAsync<TravisBackendFunctionsUtil.GetSchemafulObjectZapierSampleInput, TravisBackendFunctionsUtil.GetSchemafulObjectZapierSampleOutput>(
                sleekflowLocation,
                "GetSchemafulObjectZapierSample",
                new TravisBackendFunctionsUtil.GetSchemafulObjectZapierSampleInput(schemaUniqueName, sleekflowCompanyId));

        return new List<object>
        {
            getSchemafulObjectZapierSampleOutput.SchemafulObjectZapierViewModel
        };
    }
}