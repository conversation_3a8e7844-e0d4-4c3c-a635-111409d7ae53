﻿using System.Globalization;
using Newtonsoft.Json;
using Sleekflow.JsonConfigs;

namespace Sleekflow.Utils;

public static class JsonConvertExtensions
{
    public static object? ToObject(this string str)
    {
        try
        {
            return JsonConvert.DeserializeObject(str, JsonConfig.DefaultJsonSerializerSettings);
        }
        catch (Exception)
        {
            return null;
        }
    }

    public static T? ToObject<T>(this string str)
    {
        try
        {
            return JsonConvert.DeserializeObject<T>(str, JsonConfig.DefaultJsonSerializerSettings);
        }
        catch (Exception)
        {
            return default;
        }
    }

    public static object? ToObject(this string str, Type type)
    {
        try
        {
            return JsonConvert.DeserializeObject(str, type, JsonConfig.DefaultJsonSerializerSettings);
        }
        catch (Exception)
        {
            return default;
        }
    }

    public static DateTimeOffset? ToDateTimeOffset(this object? obj)
    {
        switch (obj)
        {
            case null:
                return null;
            case string str:
            {
                var dateTime = DateTimeOffset.Parse(str, CultureInfo.InvariantCulture, DateTimeStyles.None);

                return dateTime;
            }

            case DateTimeOffset dateTime:
                return dateTime;
            default:
                return null;
        }
    }

    public static Dictionary<string, object?> ToDictionary(object? @object)
    {
        if (@object == null)
        {
            return new Dictionary<string, object?>();
        }

        return JsonConvert.DeserializeObject<Dictionary<string, object?>>(
            JsonConvert.SerializeObject(@object, JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings)!;
    }
}

public abstract class JsonDictionaryDeserialize
{
    public Dictionary<string, object?> ToDictionary()
    {
        return JsonConvertExtensions.ToDictionary(this);
    }
}