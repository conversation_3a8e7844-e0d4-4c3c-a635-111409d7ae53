using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface IInstagramMessageBodyCreator : IMessageBodyCreator
{
}

public class InstagramMessageBodyCreator : BaseMessageBodyCreator, IInstagramMessageBodyCreator
{
    public InstagramMessageBodyCreator()
        : base(ChannelTypes.Instagram)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        return Task.FromResult((
            CreateBaseMessageBody(
                instagramMessengerMessage: new InstagramMessengerMessageObject(
                    message: new InstagramPageMessengerMessageObject(
                        attachment: null,
                        text: messageStr),
                    messagingType: "MESSAGE_TAG",
                    tag: "HUMAN_AGENT")),
            "text"));
    }
}