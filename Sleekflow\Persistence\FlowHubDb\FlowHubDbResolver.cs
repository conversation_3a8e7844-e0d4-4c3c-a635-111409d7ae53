using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.FlowHubDb;

public interface IFlowHubDbResolver : IContainerResolver
{
}

public class FlowHubDbResolver : IFlowHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public FlowHubDbResolver(IFlowHubDbConfig flowHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            flowHubDbConfig.Endpoint,
            flowHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}