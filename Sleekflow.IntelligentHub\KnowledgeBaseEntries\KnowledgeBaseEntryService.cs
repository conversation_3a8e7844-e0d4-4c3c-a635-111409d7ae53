using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Documents.WebPageDocuments;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;

namespace Sleekflow.IntelligentHub.KnowledgeBaseEntries;

public interface IKnowledgeBaseEntryService
{
    Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesAsync(
        string sleekflowCompanyId,
        GetKnowledgeBaseEntriesFilters filters,
        int? limit);

    Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesAsync(
            string sleekflowCompanyId,
            GetKnowledgeBaseEntriesFilters filters,
            string? continuationToken,
            int limit);

    Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesAsync(
        string sleekflowCompanyId,
        List<string> knowledgeBaseEntryIds);

    Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntrys, string? NextContinuationToken)> GetKnowledgeBaseEntriesAsync(
        string sleekflowCompanyId,
        KnowledgeBaseEntrySource knowledgeBaseEntrySource,
        string? continuationToken,
        int limit);

    Task DeleteKnowledgeBaseEntriesAsync(string sleekflowCompanyId, List<string> knowledgeBaseEntryIds);

    Task<KnowledgeBaseEntry> GetKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        string knowledgeBaseEntryId);

    Task<KnowledgeBaseEntry> CreateKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        KnowledgeBaseEntrySource knowledgeBaseEntrySource,
        string chunkId,
        string content,
        string contentEn,
        List<Category> categories);

    Task<KnowledgeBaseEntry> PatchAndGetKnowledgeBaseEntryAsync(
        string knowledgeBaseEntryId,
        string sleekflowCompanyId,
        string content,
        string contentEn,
        List<Category> categories);

    Task DeleteKnowledgeBaseEntryAsync(string knowledgeBaseEntryId, string sleekflowCompanyId);

    Task DeleteKnowledgeBaseEntriesByDocumentIdAsync(string sleekflowCompanyId, string documentId);
}

public class KnowledgeBaseEntryService : IScopedService, IKnowledgeBaseEntryService
{
    private readonly IKnowledgeBaseEntryRepository _knowledgeBaseEntryRepository;
    private readonly IIdService _idService;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;
    private readonly IWebPageDocumentChunkService _webPageDocumentChunkService;

    public KnowledgeBaseEntryService(
        IKnowledgeBaseEntryRepository knowledgeBaseEntryRepository,
        IIdService idService,
        IFileDocumentChunkService fileDocumentChunkService,
        IWebPageDocumentChunkService webPageDocumentChunkService)
    {
        _knowledgeBaseEntryRepository = knowledgeBaseEntryRepository;
        _idService = idService;
        _fileDocumentChunkService = fileDocumentChunkService;
        _webPageDocumentChunkService = webPageDocumentChunkService;
    }

    public async Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesAsync(
        string sleekflowCompanyId,
        GetKnowledgeBaseEntriesFilters filters,
        int? limit)
    {
        return await _knowledgeBaseEntryRepository.GetKnowledgeBaseEntriesByDocumentIdAsync(
            sleekflowCompanyId,
            filters.SourceId,
            filters.SourceType,
            limit);
    }

    public async Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesAsync(
            string sleekflowCompanyId,
            GetKnowledgeBaseEntriesFilters filters,
            string? continuationToken,
            int limit)
    {
        return await _knowledgeBaseEntryRepository.GetKnowledgeBaseEntriesByDocumentIdAsync(
            sleekflowCompanyId,
            filters.SourceId,
            filters.SourceType,
            continuationToken,
            limit);
    }

    public async Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesAsync(
        string sleekflowCompanyId,
        List<string> knowledgeBaseEntryIds)
    {
        return await _knowledgeBaseEntryRepository.GetKnowledgeBaseEntriesByIdsAsync(
            sleekflowCompanyId,
            knowledgeBaseEntryIds);
    }

    public async Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntrys, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesAsync(
            string sleekflowCompanyId,
            KnowledgeBaseEntrySource knowledgeBaseEntrySource,
            string? continuationToken,
            int limit)
    {
        var chunkId = string.Empty;
        switch (knowledgeBaseEntrySource.SourceType)
        {
            case KnowledgeBaseSourceTypes.FileDocument:
                var fileDocumentChunk = await _fileDocumentChunkService.GetFileDocumentChunkAsync(
                    sleekflowCompanyId,
                    knowledgeBaseEntrySource.SourceId);
                chunkId = fileDocumentChunk.Id;
                break;

            case KnowledgeBaseSourceTypes.WebPageDocument:
                var webPageDocumentChunk = await _webPageDocumentChunkService.GetWebPageDocumentChunkAsync(
                    sleekflowCompanyId,
                    knowledgeBaseEntrySource.SourceId);
                chunkId = webPageDocumentChunk.Id;
                break;
        }

        return await _knowledgeBaseEntryRepository.GetKnowledgeBaseEntriesAsync(
            sleekflowCompanyId,
            chunkId,
            continuationToken,
            limit);
    }

    public async Task DeleteKnowledgeBaseEntriesAsync(string sleekflowCompanyId, List<string> knowledgeBaseEntryIds)
    {
        await _knowledgeBaseEntryRepository.DeleteAsync(knowledgeBaseEntryIds, sleekflowCompanyId);
    }

    public async Task DeleteKnowledgeBaseEntriesByChunkIdsAsync(string sleekflowCompanyId, List<string> chunkIds)
    {
        var entryIds =
            await _knowledgeBaseEntryRepository.GetKnowledgeBaseEntryIdByChunkIdsAsync(sleekflowCompanyId, chunkIds);

        await _knowledgeBaseEntryRepository.DeleteAsync(entryIds, sleekflowCompanyId);
    }

    public async Task DeleteKnowledgeBaseEntriesByDocumentIdAsync(string sleekflowCompanyId, string documentId)
    {
        var fileDocumentChunkIds =
            await _fileDocumentChunkService.GetFileDocumentChunkIdsAsync(sleekflowCompanyId, documentId);
        await _fileDocumentChunkService.DeleteFileDocumentChunkAsync(fileDocumentChunkIds, sleekflowCompanyId);

        await DeleteKnowledgeBaseEntriesByChunkIdsAsync(
            sleekflowCompanyId,
            fileDocumentChunkIds);
    }

    public async Task<KnowledgeBaseEntry> GetKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        string knowledgeBaseEntryId)
    {
        return await _knowledgeBaseEntryRepository.GetAsync(knowledgeBaseEntryId, sleekflowCompanyId);
    }

    public async Task<KnowledgeBaseEntry> CreateKnowledgeBaseEntryAsync(
        string sleekflowCompanyId,
        KnowledgeBaseEntrySource knowledgeBaseEntrySource,
        string chunkId,
        string content,
        string contentEn,
        List<Category> categories)
    {
        var entries = await _knowledgeBaseEntryRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.ChunkId == chunkId);
        if (entries.Any())
        {
            return entries.First();
        }

        var knowledgeBaseEntry = await _knowledgeBaseEntryRepository.CreateAndGetAsync(
            new KnowledgeBaseEntry(
                _idService.GetId(SysTypeNames.KnowledgeBaseEntry),
                sleekflowCompanyId,
                chunkId,
                content,
                contentEn,
                knowledgeBaseEntrySource,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                categories,
                new List<string>
                {
                    "Active"
                }),
            sleekflowCompanyId);

        return knowledgeBaseEntry;
    }

    public async Task<KnowledgeBaseEntry> PatchAndGetKnowledgeBaseEntryAsync(
        string knowledgeBaseEntryId,
        string sleekflowCompanyId,
        string content,
        string contentEn,
        List<Category> categories)
    {
        var knowledgeBaseEntry = await _knowledgeBaseEntryRepository.GetAsync(
            knowledgeBaseEntryId,
            sleekflowCompanyId);
        if (knowledgeBaseEntry is null)
        {
            throw new SfNotFoundObjectException(knowledgeBaseEntryId, sleekflowCompanyId);
        }

        var patchedKnowledgeBaseEntry =
            await _knowledgeBaseEntryRepository.PatchAndGetKnowledgeBaseEntryAsync(
                knowledgeBaseEntry,
                content,
                contentEn,
                categories);

        return patchedKnowledgeBaseEntry;
    }

    public async Task DeleteKnowledgeBaseEntryAsync(string knowledgeBaseEntryId, string sleekflowCompanyId)
    {
        var deleteAsync = await _knowledgeBaseEntryRepository.DeleteAsync(knowledgeBaseEntryId, sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete entries, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}