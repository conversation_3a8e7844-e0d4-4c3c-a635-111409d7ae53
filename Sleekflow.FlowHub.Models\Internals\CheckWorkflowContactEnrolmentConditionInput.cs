using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;

public class CheckWorkflowContactEnrolmentConditionInput
{
    [JsonProperty("workflow_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("sleekflow_company_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("contact_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string ContactId { get; set; }

    [JsonProperty("contact_detail")]
    [Validations.ValidateObject]
    [System.ComponentModel.DataAnnotations.Required]
    public ContactDetail ContactDetail { get; set; }

    #region the following properties are used for applying enrolment condition that's retrieved from workflow once

    [JsonProperty("condition")]
    public string? Condition { get; set; }

    [JsonProperty("workflow_name")]
    public string? WorkflowName { get; set; }

    #endregion

    [JsonConstructor]
    public CheckWorkflowContactEnrolmentConditionInput(
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string contactId,
        ContactDetail contactDetail,
        string? condition,
        string? workflowName)
    {
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        SleekflowCompanyId = sleekflowCompanyId;
        ContactId = contactId;
        ContactDetail = contactDetail;
        Condition = condition;
        WorkflowName = workflowName;
    }
}