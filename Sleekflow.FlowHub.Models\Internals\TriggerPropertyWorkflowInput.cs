using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Models.Internals;

public class TriggerPropertyWorkflowInput
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [Json<PERSON>roperty("origin")]
    [Required]
    public string Origin { get; set; }

    [JsonProperty("workflow_id")]
    [Required]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    [Required]
    public string WorkflowVersionedId { get; set; }

    [Json<PERSON>roperty("contact_property_date_time_settings")]
    public ContactPropertyDateTimeSettings ContactPropertyDateTimeSettings { get; set; }

    [JsonProperty("workflow_version")]
    public string WorkflowVersion { get; set; }

    [JsonProperty("scheduled_type")]
    public string ScheduledType { get; set; }

    [JsonProperty("recurring_settings")]
    public WorkflowRecurringSettings WorkflowRecurringSettings { get; set; }

    [JsonProperty("custom_object_date_time_settings")]
    public CustomObjectDateTimeSettings CustomObjectDateTimeSettings { get; set; }

    [JsonConstructor]
    public TriggerPropertyWorkflowInput(
        string sleekflowCompanyId,
        string origin,
        string workflowId,
        string workflowVersionedId,
        ContactPropertyDateTimeSettings propertySettings,
        CustomObjectDateTimeSettings customObjectObjectDateTimeSettings,
        string workflowVersion,
        string scheduledType,
        WorkflowRecurringSettings workflowRecurringSettings)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Origin = origin;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        ContactPropertyDateTimeSettings = propertySettings;
        WorkflowVersion = workflowVersion;
        ScheduledType = scheduledType;
        WorkflowRecurringSettings = workflowRecurringSettings;
        CustomObjectDateTimeSettings = customObjectObjectDateTimeSettings;
    }
}