using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class JumpToStepArgs : TypedCallStepArgs
{
    public const string CallName = "sys.jump-to";

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => string.Empty;

    [JsonConstructor]
    public JumpToStepArgs()
    {
    }
}
