using MassTransit;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Consumers;

public class OnSendEmailTriggeredEventConsumerDefinition : ConsumerDefinition<OnSendEmailTriggeredEventConsumer>
{
    public const int LockDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnSendEmailTriggeredEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 40;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 40;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(LockDuration);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnSendEmailTriggeredEventConsumer : IConsumer<OnSendEmailTriggeredEvent>
{
    private readonly IEmailProviderSelector _providerSelector;

    public OnSendEmailTriggeredEventConsumer(
        IEmailProviderSelector providerSelector
    )
    {
        _providerSelector = providerSelector;
    }

    public async Task Consume(ConsumeContext<OnSendEmailTriggeredEvent> context)
    {
        var message = context.Message;
        var cancellationToken = context.CancellationToken;

        var emailProvider = _providerSelector.GetEmailProvider(message.ProviderName);

        await emailProvider.HandleSendEmailEventAsync(
            message.SleekflowCompanyId,
            message.Sender,
            message.Subject,
            message.To,
            message.Cc,
            message.Bcc,
            message.ReplyTo,
            message.HtmlBody,
            message.TextBody,
            message.EmailAttachments,
            message.EmailMetadata,
            cancellationToken);
    }
}