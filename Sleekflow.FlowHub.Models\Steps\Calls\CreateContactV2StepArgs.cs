﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Common;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateContactV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.create-contact";

    [Required]
    [JsonProperty("contact_identifier_type")]
    public string ContactIdentifierType { get; set; }

    [Required]
    [JsonProperty("contact_identifier")]
    public string ContactIdentifier { get; set; }

    [JsonProperty("properties__id_expr_set")]
    public HashSet<ContactPropertyIdValuePair> PropertiesIdExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public CreateContactV2StepArgs(
        string contactIdentifierType,
        string contactIdentifier,
        HashSet<ContactPropertyIdValuePair> propertiesIdExprSet)
    {
        ContactIdentifierType = contactIdentifierType;
        ContactIdentifier = contactIdentifier;
        PropertiesIdExprSet = propertiesIdExprSet;
    }
}