using Sleekflow.FlowHub.Triggers.Executions;
using Sleekflow.FlowHub.Triggers.States;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class ExecutionsCoreIntegrationTests
{
    [Test]
    public async Task GetStateStepExecutionsTest()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var mockWorkflowId = "aQPUxexxV2GYPVz";

        // /States/GetStates
        var getStatesInput =
            new GetStates.GetStatesInput(
                mockCompanyId,
                null,
                50,
                new GetStates.GetStatesInputFilters(
                    mockWorkflowId,
                    null,
                    null,
                    null));
        var getStatesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStatesInput).ToUrl("/States/GetStates");
            });
        var getStatesOutput =
            await getStatesScenarioResult.ReadAsJsonAsync<
                Output<GetStates.GetStatesOutput>>();

        // /Executions/GetStateStepExecutions
        var getStateStepExecutionsInput =
            new GetStateStepExecutions.GetStateStepExecutionsInput(
                mockCompanyId,
                null,
                50,
                getStatesOutput!.Data.States[0].Id);
        var getStateStepExecutionsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStateStepExecutionsInput).ToUrl("/Executions/GetStateStepExecutions");
            });
        var getStateStepExecutionsOutput =
            await getStateStepExecutionsScenarioResult.ReadAsJsonAsync<
                Output<GetStateStepExecutions.GetStateStepExecutionsOutput>>();

        Assert.That(getStateStepExecutionsOutput, Is.Not.Null);
        Assert.That(getStateStepExecutionsOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowExecutionsTest()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var mockWorkflowId = "aQPUxexxV2GYPVz";

        // /Executions/GetWorkflowExecutions
        var getWorkflowExecutionsInput =
            new GetWorkflowExecutions.GetWorkflowExecutionsInput(
                mockCompanyId,
                null,
                50,
                new GetWorkflowExecutions.GetWorkflowExecutionsInputFilters(
                    mockWorkflowId,
                    null,
                    null,
                    null));
        var getWorkflowExecutionsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getWorkflowExecutionsInput).ToUrl("/Executions/GetWorkflowExecutions");
            });
        var getWorkflowExecutionsOutput =
            await getWorkflowExecutionsScenarioResult.ReadAsJsonAsync<
                Output<GetWorkflowExecutions.GetWorkflowExecutionsOutput>>();

        Assert.That(getWorkflowExecutionsOutput, Is.Not.Null);
        Assert.That(getWorkflowExecutionsOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowExecutionsByStateTest()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var mockWorkflowId = "aQPUxexxV2GYPVz";

        // /Executions/GetWorkflowExecutionsByState
        var getWorkflowExecutionsByStateInput =
            new GetWorkflowExecutionsByState.GetWorkflowExecutionsByStateInput(
                mockCompanyId,
                null,
                50,
                new GetWorkflowExecutionsByState.GetWorkflowExecutionsByStateInputFilters(
                    mockWorkflowId,
                    null,
                    null,
                    null));
        var getWorkflowExecutionsByStateScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getWorkflowExecutionsByStateInput).ToUrl("/Executions/GetWorkflowExecutionsByState");
            });
        var getWorkflowExecutionsByStateOutput =
            await getWorkflowExecutionsByStateScenarioResult.ReadAsJsonAsync<
                Output<GetWorkflowExecutionsByState.GetWorkflowExecutionsByStateOutput>>();

        Assert.That(getWorkflowExecutionsByStateOutput, Is.Not.Null);
        Assert.That(getWorkflowExecutionsByStateOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowExecutionStatisticsTest()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var mockWorkflowId = "aQPUxexxV2GYPVz";

        // /Executions/GetWorkflowExecutionStatistics
        var getWorkflowExecutionStatisticsInput =
            new GetWorkflowExecutionStatistics.GetWorkflowExecutionStatisticsInput(
                mockCompanyId,
                new GetWorkflowExecutionStatistics.GetWorkflowExecutionStatisticsInputFilters(
                    null,
                    null,
                    null,
                    mockWorkflowId));
        var getWorkflowExecutionStatisticsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getWorkflowExecutionStatisticsInput).ToUrl("/Executions/GetWorkflowExecutionStatistics");
            });
        var getWorkflowExecutionStatisticsOutput =
            await getWorkflowExecutionStatisticsScenarioResult.ReadAsJsonAsync<
                Output<GetWorkflowExecutionStatistics.GetWorkflowExecutionStatisticsOutput>>();

        Assert.That(getWorkflowExecutionStatisticsOutput, Is.Not.Null);
        Assert.That(getWorkflowExecutionStatisticsOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowStepExecutionsTest()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var mockWorkflowId = "aQPUxexxV2GYPVz";
        var mockWorkflowVersionedId = "aQPUxexxV2GYPVz-418tMearygmM7XL";
        var mockStepId = "setup-contact-and-conversation";

        // /Executions/GetWorkflowStepExecutions
        var getWorkflowStepExecutionsInput =
            new GetWorkflowStepExecutions.GetWorkflowStepExecutionsInput(
                mockCompanyId,
                null,
                50,
                mockWorkflowId,
                mockWorkflowVersionedId,
                mockStepId);
        var getWorkflowStepExecutionsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getWorkflowStepExecutionsInput).ToUrl("/Executions/GetWorkflowStepExecutions");
            });
        var getWorkflowStepExecutionsOutput =
            await getWorkflowStepExecutionsScenarioResult.ReadAsJsonAsync<
                Output<GetWorkflowStepExecutions.GetWorkflowStepExecutionsOutput>>();

        Assert.That(getWorkflowStepExecutionsOutput, Is.Not.Null);
        Assert.That(getWorkflowStepExecutionsOutput!.HttpStatusCode, Is.EqualTo(200));
    }
}