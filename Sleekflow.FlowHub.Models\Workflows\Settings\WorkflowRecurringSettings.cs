﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;

namespace Sleekflow.FlowHub.Models.Workflows.Settings;

public class WorkflowRecurringSettings : IValidatableObject
{
    [Required]
    [JsonProperty("recurring_type")]
    public string RecurringType { get; set; }

    [JsonProperty("day")]
    public int? Day { get; set; }

    [JsonProperty("week")]
    public int? Week { get; set; }

    [JsonProperty("month")]
    public int? Month { get; set; }

    [JsonProperty("year")]
    public int? Year { get; set; }

    [JsonProperty("repeat_on_weekly")]
    public string? RepeatOnWeekly { get; set; }

    [JsonProperty("repeat_on_weekly_specific_day")]
    public string[]? RepeatOnWeeklySpecificDay { get; set; }

    [JsonProperty("repeat_on_monthly")]
    public int? RepeatOnMonthly { get; set; }

    [JsonProperty("minute")]
    public int? Minute { get; set; }

    [JsonProperty("hour")]
    public int? Hour { get; set; }

    [JsonConstructor]
    public WorkflowRecurringSettings(
        string recurringType,
        int? day,
        int? week,
        int? month,
        int? year,
        string? repeatOnWeekly,
        string[]? repeatOnWeeklySpecificDay,
        int? repeatOnMonthly,
        int? hour,
        int? minute)
    {
        RecurringType = recurringType;
        Day = day;
        Week = week;
        Month = month;
        Year = year;
        RepeatOnWeekly = repeatOnWeekly;
        RepeatOnWeeklySpecificDay = repeatOnWeeklySpecificDay;
        RepeatOnMonthly = repeatOnMonthly;
        Hour = hour;
        Minute = minute;
    }

    public WorkflowRecurringSettings(
        string recurringType,
        int? day,
        int? week,
        int? month,
        int? year)
        : this(recurringType, day, week, month, year, null, null, null, null, null)
    {
    }
    public WorkflowRecurringSettings()
    {
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (RecurringType is not (WorkflowRecurringTypes.Weekly
            or WorkflowRecurringTypes.Monthly
            or WorkflowRecurringTypes.Yearly
            or WorkflowRecurringTypes.Custom))
        {
            yield return new ValidationResult(
                $"Unrecognized recurring type '{RecurringType}'.",
                new List<string>
                {
                    nameof(RecurringType)
                });
        }

        if (RecurringType == WorkflowRecurringTypes.Custom)
        {
            if (Day.GetValueOrDefault(0) == 0
                && Week.GetValueOrDefault(0) == 0
                && Month.GetValueOrDefault(0) == 0
                && Year.GetValueOrDefault(0) == 0)
            {
                yield return new ValidationResult(
                    $"Invalid configuration for recurring type '{WorkflowRecurringTypes.Custom}'. No time unit is set.",
                    new List<string>
                    {
                        nameof(Day), nameof(Week), nameof(Month), nameof(Year),
                    });
            }

            var timeUnitValues = new List<int>
            {
                Day.GetValueOrDefault(0),
                Week.GetValueOrDefault(0),
                Month.GetValueOrDefault(0),
                Year.GetValueOrDefault(0)
            };

            if (timeUnitValues.Any(v => v < 0))
            {
                yield return new ValidationResult(
                    $"Invalid configuration for recurring type '{WorkflowRecurringTypes.Custom}'. Time unit value cannot be negative.",
                    new List<string>
                    {
                        nameof(Day), nameof(Week), nameof(Month), nameof(Year),
                    });
            }

            if (timeUnitValues.Count(v => v > 0) > 1)
            {
                yield return new ValidationResult(
                    $"Invalid configuration for recurring type '{WorkflowRecurringTypes.Custom}'. Only one time unit value can be set.",
                    new List<string>
                    {
                        nameof(Day), nameof(Week), nameof(Month), nameof(Year),
                    });
            }
        }
    }
}