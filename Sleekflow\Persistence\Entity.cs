﻿using Newtonsoft.Json;

namespace Sleekflow.Persistence;

public abstract class Entity
{
    public const string PropertyNameId = "id";
    public const string PropertyNameSysTypeName = "sys_type_name";
    public const string PropertyNameTtl = "ttl";

    protected Entity(string id, string sysTypeName)
    {
        Id = id;
        SysTypeName = sysTypeName;
    }

    protected Entity(string id, string sysTypeName, int? ttl)
    {
        Id = id;
        SysTypeName = sysTypeName;
        Ttl = ttl;
    }

    [JsonProperty(PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty(PropertyNameSysTypeName)]
    public string SysTypeName { get; set; }

    [JsonProperty(PropertyNameTtl, NullValueHandling = NullValueHandling.Ignore)]
    public int? Ttl { get; set; }
}

public abstract class EntityDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    protected EntityDto(string id)
    {
        Id = id;
    }
}