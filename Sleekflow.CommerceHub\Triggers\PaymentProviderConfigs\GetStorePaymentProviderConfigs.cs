using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;

[TriggerGroup(ControllerNames.PaymentProviderConfigs)]
public class GetStorePaymentProviderConfigs
    : ITrigger<
        GetStorePaymentProviderConfigs.GetStorePaymentProviderConfigsInput,
        GetStorePaymentProviderConfigs.GetStorePaymentProviderConfigsOutput>
{
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;

    public GetStorePaymentProviderConfigs(
        IPaymentProviderConfigService paymentProviderConfigService)
    {
        _paymentProviderConfigService = paymentProviderConfigService;
    }

    public class GetStorePaymentProviderConfigsInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [JsonConstructor]
        public GetStorePaymentProviderConfigsInput(
            string sleekflowCompanyId,
            string storeId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
        }
    }

    public class GetStorePaymentProviderConfigsOutput
    {
        [JsonProperty("payment_provider_configs")]
        public List<PaymentProviderConfigDto> PaymentProviderConfigs { get; set; }

        [JsonConstructor]
        public GetStorePaymentProviderConfigsOutput(
            List<PaymentProviderConfigDto> paymentProviderConfigs)
        {
            PaymentProviderConfigs = paymentProviderConfigs;
        }
    }

    public async Task<GetStorePaymentProviderConfigsOutput> F(
        GetStorePaymentProviderConfigsInput getStorePaymentProviderConfigsInput)
    {
        var paymentProviderConfigs = await _paymentProviderConfigService.GetStorePaymentProviderConfigsAsync(
            getStorePaymentProviderConfigsInput.SleekflowCompanyId,
            getStorePaymentProviderConfigsInput.StoreId);

        return new GetStorePaymentProviderConfigsOutput(
            paymentProviderConfigs.Select(ppc => new PaymentProviderConfigDto(ppc)).ToList());
    }
}