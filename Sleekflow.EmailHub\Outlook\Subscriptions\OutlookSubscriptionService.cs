using System.Net.Http.Headers;
using System.Text;
using MassTransit;
using Microsoft.Graph.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Configs;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Outlook.Authentications;
using Sleekflow.EmailHub.Models.Outlook.Events;
using Sleekflow.EmailHub.Models.Outlook.Subscriptions;
using Sleekflow.EmailHub.Models.Subscriptions;
using Sleekflow.EmailHub.Outlook.Authentications;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Outlook.Subscriptions;

public interface IOutlookSubscriptionService : IEmailSubscriptionService
{
    Task UpdateDeltaLink(
        string sleekflowCompanyId,
        string emailAddress,
        string deltaLink,
        CancellationToken cancellationToken = default);

    Task<(EmailSubscription Subscription, string SleekflowCompanyId, string EmailAddress)>
        GetSubscriptionByOutlookSubscriptionIdAsync(
            string outlookSubscriptionId,
            string clientState,
            CancellationToken cancellationToken = default);
}

public class OutlookSubscriptionService : IScopedService, IOutlookSubscriptionService
{
    private readonly IOutlookConfig _outlookConfig;
    private readonly ILogger<OutlookSubscriptionService> _logger;
    private readonly HttpClient _httpClient;
    private readonly IBus _bus;
    private readonly IProviderConfigService _providerConfigService;
    private readonly IOutlookAuthenticationService _outlookAuthenticationService;

    public OutlookSubscriptionService(
        IOutlookConfig outlookConfig,
        ILogger<OutlookSubscriptionService> logger,
        IHttpClientFactory httpClientFactory,
        IBus bus,
        IProviderConfigService providerConfigService,
        IOutlookAuthenticationService outlookAuthenticationService)
    {
        _outlookConfig = outlookConfig;
        _logger = logger;
        _bus = bus;
        _providerConfigService = providerConfigService;
        _outlookAuthenticationService = outlookAuthenticationService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<EmailSubscription> GetSubscriptionAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Outlook,
            cancellationToken: cancellationToken);

        if (providerConfig == null || !providerConfig.EmailSubscription.IsSubscribed)
        {
            _logger.LogError(
                "GetSubscriptionAsync fails due to not subscribed: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                emailAddress,
                sleekflowCompanyId);

            throw new SfUnauthorizedException();
        }

        return providerConfig.EmailSubscription;
    }

    public async Task SubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? subscriptionMetadata,
        CancellationToken cancellationToken = default)
    {
        if (!subscriptionMetadata!.ContainsKey("inbox_name"))
        {
            throw new ArgumentException("inbox name is not provided for outlook subscription request");
        }

        var inboxName = subscriptionMetadata["inbox_name"];

        var authentication = await _outlookAuthenticationService.GetAuthenticationAsync(
            sleekflowCompanyId,
            emailAddress,
            cancellationToken: cancellationToken);

        var outlookAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new SfInternalErrorException(
                $"Cannot parse outlook authentication metadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        // Subscription expiration can only be 10070 minutes (<7 days) in the future.
        var createSubscriptionRequestBody = new
        {
            changeType = "created,updated,deleted",
            notificationUrl = _outlookConfig.NotificationUrl,
            resource = "me/mailFolders('Inbox')/messages",
            expirationDateTime =
                (DateTime.UtcNow + TimeSpan.FromMinutes(10070)).ToString("yyyy-MM-ddTHH:mm:ss.fffffffK"),
            clientState = sleekflowCompanyId,
        };
        var serializedRequestBody = JsonConvert.SerializeObject(createSubscriptionRequestBody);

        var createSubscriptionContent = new StringContent(
            serializedRequestBody,
            Encoding.UTF8,
            "application/json");

        var createSubscriptionRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"https://graph.microsoft.com/v1.0/subscriptions"),
            Content = createSubscriptionContent
        };

        createSubscriptionRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            outlookAuthenticationMetadata.TokenType ?? "Bearer",
            outlookAuthenticationMetadata.AccessToken);

        var createSubscriptionResponse =
            await _httpClient.SendAsync(createSubscriptionRequestMessage, cancellationToken);
        var subscribeResponseContent = await createSubscriptionResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!createSubscriptionResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(subscribeResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} Outlook SubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                createSubscriptionResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"{errorMsg} Outlook SubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var createSubscriptionResponseModel = JsonConvert.DeserializeObject<Subscription>(subscribeResponseContent)!;
        string inboxFolderId = string.Empty;

        try
        {
            inboxFolderId = await GetMailFolderIdByNameAsync(
                inboxName,
                emailAddress,
                sleekflowCompanyId,
                cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(
                $"GetMailFolderIdByNameAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}: Exception: {e}");
        }

        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Outlook,
            cancellationToken: cancellationToken);

        providerConfig.EmailSubscription.IsSubscribed = true;
        providerConfig.EmailSubscription.LastSubscriptionTime = DateTimeOffset.UtcNow;

        var deltaLink = (providerConfig.EmailSubscription.EmailSubscriptionMetadata as OutlookSubscriptionMetadata)
            ?.DeltaLink;

        providerConfig.EmailSubscription.EmailSubscriptionMetadata = new OutlookSubscriptionMetadata(
            sleekflowCompanyId,
            createSubscriptionResponseModel.Id,
            createSubscriptionResponseModel.ApplicationId,
            createSubscriptionResponseModel.ExpirationDateTime,
            inboxFolderId,
            deltaLink);

        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);

        await _bus.Publish(
            new OnOutlookSyncAllTriggeredEvent(
                emailAddress,
                sleekflowCompanyId),
            cancellationToken);

        _logger.LogInformation(
            "SubscribeToAnEmail successes: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);
    }

    public async Task UnsubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var authentication = await _outlookAuthenticationService.GetAuthenticationAsync(
            sleekflowCompanyId,
            emailAddress,
            cancellationToken: cancellationToken);

        var outlookAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new SfInternalErrorException(
                $"Cannot parse outlook authentication metadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Outlook,
            cancellationToken: cancellationToken);

        var subscription = providerConfig.EmailSubscription;

        var subscriptionMetadata =
            subscription.EmailSubscriptionMetadata as OutlookSubscriptionMetadata ??
            throw new SfInternalErrorException(
                $"Cannot parse outlook subscription metadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var subscriptionRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Delete,
            RequestUri = new Uri(
                $"https://graph.microsoft.com/v1.0/subscriptions/{subscriptionMetadata.SubscriptionId}"),
        };

        subscriptionRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            outlookAuthenticationMetadata.TokenType ?? "Bearer",
            outlookAuthenticationMetadata.AccessToken);
        var subscriptionResponse = await _httpClient.SendAsync(subscriptionRequestMessage, cancellationToken);

        if (!subscriptionResponse.IsSuccessStatusCode)
        {
            _logger.LogError(
                "HTTP {code}: Outlook UnSubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                subscriptionResponse.StatusCode,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"Outlook UnSubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        subscription.IsSubscribed = false;
        subscriptionMetadata.ExpirationDateTime = DateTimeOffset.UtcNow;
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);

        _logger.LogInformation(
            "Email {email} of {companyId} unsubscribed at {time}",
            emailAddress,
            sleekflowCompanyId,
            DateTime.UtcNow);
    }

    public async Task RenewEmailSubscriptionAsync(CancellationToken cancellationToken)
    {
        var renewOutlookSubscriptionInput =
            await _providerConfigService.GetEmailAddressesByProviderNameAsync(ProviderNames.Outlook, cancellationToken);

        foreach (var input in renewOutlookSubscriptionInput)
        {
            EmailSubscription subscription;

            try
            {
                subscription = await GetSubscriptionAsync(
                    input["sleekflow_company_id"],
                    input["email_address"],
                    cancellationToken);
            }
            catch (Exception)
            {
                continue;
            }

            var subscriptionMetadata = subscription.EmailSubscriptionMetadata as OutlookSubscriptionMetadata ??
                                       throw new SfInternalErrorException(
                                           $"cannot parse outlook metadata: emailAddress {input["email_address"]}" +
                                           $" of sleekflowCompanyId {input["sleekflowCompanyId"]}");

            if (subscriptionMetadata.ExpirationDateTime + TimeSpan.FromSeconds(30) > DateTimeOffset.UtcNow)
            {
                await SubscribeAtEmailAddressAsync(
                    input["sleekflow_company_id"],
                    input["email_address"],
                    new Dictionary<string, string>(),
                    cancellationToken);
            }
        }
    }

    public async Task<List<string>> FilterSubscribedCompanies(
        List<string> companyIds,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var result = new List<string>();

        foreach (var companyId in companyIds)
        {
            try
            {
                _ = await GetSubscriptionAsync(
                    companyId,
                    emailAddress,
                    cancellationToken);
                result.Add(companyId);
            }
            catch (Exception)
            {
                // ignored unsubscribed company
            }
        }

        return result;
    }

    public async Task UpdateDeltaLink(
        string sleekflowCompanyId,
        string emailAddress,
        string deltaLink,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Outlook,
            cancellationToken: cancellationToken);

        var outlookSubscriptionMetadata =
            providerConfig.EmailSubscription.EmailSubscriptionMetadata as OutlookSubscriptionMetadata ??
            throw new SfInternalErrorException(
                $"Cannot parse outlook subscription metadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        outlookSubscriptionMetadata.DeltaLink = deltaLink;
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);
    }

    public async Task<(EmailSubscription Subscription, string SleekflowCompanyId, string EmailAddress)>
        GetSubscriptionByOutlookSubscriptionIdAsync(
            string outlookSubscriptionId,
            string clientState,
            CancellationToken cancellationToken = default)
    {
        var providerConfig = (await _providerConfigService.GetObjectsAsync(
                x =>
                    x.ProviderName == ProviderNames.Outlook &&
                    ((OutlookSubscriptionMetadata) x.EmailSubscription.EmailSubscriptionMetadata!).SubscriptionId ==
                    outlookSubscriptionId &&
                    ((OutlookSubscriptionMetadata) x.EmailSubscription.EmailSubscriptionMetadata!).ClientState ==
                    clientState,
                cancellationToken))
            .FirstOrDefault();

        if (providerConfig == null)
        {
            _logger.LogError(
                "GetSubscriptionByOutlookSubscriptionIdAsync fails: no such subscriptionId nor clientState");

            throw new SfUnauthorizedException();
        }

        return (providerConfig.EmailSubscription, providerConfig.SleekflowCompanyId, providerConfig.EmailAddress);
    }

    private async Task<string> GetMailFolderIdByNameAsync(
        string folderName,
        string emailAddress,
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default)
    {
        var authentication =
            await _outlookAuthenticationService.GetAuthenticationAsync(
                sleekflowCompanyId,
                emailAddress,
                cancellationToken: cancellationToken);

        var outlookAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new SfInternalErrorException(
                $"Cannot parse outlook authentication metadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = new Uri(
                $"https://graph.microsoft.com/v1.0/me/mailFolders?$filter=displayName eq '{folderName}'"),
        };

        requestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            outlookAuthenticationMetadata.TokenType ?? "Bearer",
            outlookAuthenticationMetadata.AccessToken);
        var response = await _httpClient.SendAsync(requestMessage, cancellationToken);
        var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogError(
                "HTTP {code}: Outlook GetMailFolderIdByName fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                response.StatusCode,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"Outlook GetMailFolderIdByName fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var responseModel = JsonConvert.DeserializeObject<MailFolderCollectionResponse>(responseContent)!;
        var mailFolder = responseModel.Value![0];

        return mailFolder.Id!;
    }
}