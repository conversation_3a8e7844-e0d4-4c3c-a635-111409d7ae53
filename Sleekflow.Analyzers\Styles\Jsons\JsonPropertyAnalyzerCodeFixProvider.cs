using System;
using System.Collections.Immutable;
using System.Composition;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CodeActions;
using Microsoft.CodeAnalysis.CodeFixes;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace Sleekflow.Analyzers.Styles;

[ExportCodeFixProvider(LanguageNames.CSharp, Name = nameof(JsonPropertyAnalyzerCodeFixProvider))]
[Shared]
public class JsonPropertyAnalyzerCodeFixProvider : CodeFixProvider
{
    private const string AddJsonPropertyAttributeTitle = "Add JsonProperty attribute";

    public sealed override ImmutableArray<string> FixableDiagnosticIds
    {
        get
        {
            return ImmutableArray.Create(
                JsonPropertyAnalyzer.MissingJsonPropertyDiagnosticId);
        }
    }

    public sealed override FixAllProvider GetFixAllProvider()
    {
        return WellKnownFixAllProviders.BatchFixer;
    }

    public sealed override async Task RegisterCodeFixesAsync(CodeFixContext context)
    {
        var root = await context.Document.GetSyntaxRootAsync(context.CancellationToken).ConfigureAwait(false);

        foreach (var diagnostic in context.Diagnostics)
        {
            var diagnosticSpan = diagnostic.Location.SourceSpan;

            if (diagnostic.Id == JsonPropertyAnalyzer.MissingJsonPropertyDiagnosticId)
            {
                try
                {
                    var propertyDeclaration = root!.FindToken(diagnosticSpan.Start)
                        .Parent!.AncestorsAndSelf()
                        .OfType<PropertyDeclarationSyntax>()
                        .First();

                    context.RegisterCodeFix(
                        CodeAction.Create(
                            title: AddJsonPropertyAttributeTitle,
                            createChangedDocument: c => AddJsonPropertyAttributeAsync(
                                context.Document,
                                propertyDeclaration,
                                c),
                            equivalenceKey: AddJsonPropertyAttributeTitle),
                        diagnostic);
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    private async Task<Document> AddJsonPropertyAttributeAsync(
        Document document,
        PropertyDeclarationSyntax propertyDeclaration,
        CancellationToken cancellationToken)
    {
        var attributeName = SyntaxFactory.ParseName("JsonProperty");
        var attributeArgumentList =
            SyntaxFactory.ParseAttributeArgumentList($"(\"{ToSnakeCase(propertyDeclaration.Identifier.Text)}\")");
        var newAttribute = SyntaxFactory.Attribute(attributeName, attributeArgumentList);
        var newAttributeList = SyntaxFactory.AttributeList(SyntaxFactory.SingletonSeparatedList(newAttribute));
        var newPropertyDeclaration = propertyDeclaration.AddAttributeLists(newAttributeList);

        var root = await document.GetSyntaxRootAsync(cancellationToken).ConfigureAwait(false);
        var newRoot = root!.ReplaceNode(propertyDeclaration, newPropertyDeclaration);

        return document.WithSyntaxRoot(newRoot);
    }

    private string ToSnakeCase(string text)
    {
        return string.Concat(text.Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x.ToString() : x.ToString()))
            .ToLower();
    }
}