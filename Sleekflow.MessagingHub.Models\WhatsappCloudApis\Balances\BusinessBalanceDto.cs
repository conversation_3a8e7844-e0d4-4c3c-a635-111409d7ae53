using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

public class BusinessBalanceDto : IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("facebook_business_name")]
    public string? FacebookBusinessName { get; set; }

    [JsonProperty("facebook_business_wabas")]
    public List<FacebookBusinessWaba> FacebookBusinessWabas { get; set; }

    [JsonProperty("total_credit")]
    public Money TotalCredit { get; set; }

    [JsonProperty("all_time_usage")]
    public Money AllTimeUsage { get; set; }

    [JsonProperty("balance")]
    public Money Balance { get; set; }

    [JsonProperty("waba_balances")]
    public List<WabaBalanceDto>? WabaBalances { get; set; }

    [JsonProperty("unallocated_credit")]
    public Money? UnallocatedCredit { get; set; }

    [JsonProperty("is_by_waba_billing_enabled")]
    public bool IsByWabaBillingEnabled { get; set; }

    [JsonProperty("un_calculated_credit_transfer_transaction_logs")]
    public List<CreditTransferTransactionLogDto> UnCalculatedCreditTransferTransactionLogs { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public BusinessBalanceDto(
        string facebookBusinessId,
        string? facebookBusinessName,
        List<FacebookBusinessWaba> facebookBusinessWabas,
        Money totalCredit,
        Money allTimeUsage,
        Money balance,
        List<WabaBalanceDto>? wabaBalances,
        Money? unallocatedCredit,
        bool? isByWabaBillingEnabled,
        List<CreditTransferTransactionLogDto> unCalculatedCreditTransferTransactionLogs,
        string? eTag,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
    {
        FacebookBusinessId = facebookBusinessId;
        FacebookBusinessName = facebookBusinessName;
        FacebookBusinessWabas = facebookBusinessWabas;
        TotalCredit = totalCredit;
        AllTimeUsage = allTimeUsage;
        Balance = balance;
        WabaBalances = wabaBalances;
        UnallocatedCredit = unallocatedCredit;
        IsByWabaBillingEnabled = isByWabaBillingEnabled ?? false;
        UnCalculatedCreditTransferTransactionLogs = unCalculatedCreditTransferTransactionLogs;
        ETag = eTag;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public BusinessBalanceDto(BusinessBalance businessBalance, List<Waba> wabas, List<CreditTransferTransactionLogDto> unCalculatedCreditTransferTransactionLogs)
        : this(
            businessBalance.FacebookBusinessId,
            wabas.Select(w => w.FacebookWabaBusinessName).FirstOrDefault(),
            wabas.Select(w => new FacebookBusinessWaba(w.FacebookWabaId, w.FacebookWabaName!, w.WabaPhoneNumbers))
                .ToList(),
            businessBalance.Credit,
            businessBalance.AllTimeUsage,
            businessBalance.Balance,
            businessBalance.WabaBalances?.Select(x => new WabaBalanceDto(x)).ToList(),
            businessBalance.UnallocatedCredit,
            businessBalance.IsByWabaBillingEnabled,
            unCalculatedCreditTransferTransactionLogs,
            businessBalance.ETag,
            businessBalance.CreatedAt,
            businessBalance.UpdatedAt)
    {
    }

    public class FacebookBusinessWaba
    {
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [JsonProperty("facebook_waba_name")]
        public string FacebookWabaName { get; set; }

        [JsonProperty("facebook_phone_numbers")]
        public List<string?> FacebookPhoneNumbers { get; set; }

        [JsonConstructor]
        public FacebookBusinessWaba(
            string facebookWabaId,
            string facebookWabaName,
            List<string?> facebookPhoneNumbers)
        {
            FacebookWabaId = facebookWabaId;
            FacebookWabaName = facebookWabaName;
            FacebookPhoneNumbers = facebookPhoneNumbers;
        }

        public FacebookBusinessWaba(
            string facebookWabaId,
            string facebookWabaName,
            HashSet<WabaPhoneNumber> wabaPhoneNumbers)
        {
            FacebookWabaId = facebookWabaId;
            FacebookWabaName = facebookWabaName;

            FacebookPhoneNumbers = wabaPhoneNumbers
                .Select(w => w.FacebookPhoneNumberDetail.DisplayPhoneNumber)
                .ToList();
        }
    }
}