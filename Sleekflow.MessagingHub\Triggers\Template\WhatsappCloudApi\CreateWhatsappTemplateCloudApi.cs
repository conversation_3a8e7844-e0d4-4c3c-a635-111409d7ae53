using GraphApi.Client.Models.MessageObjects.TemplateObjects;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Templates;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Template.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Templates)]
public class CreateWhatsappCloudApiTemplate
    : ITrigger<
        CreateWhatsappCloudApiTemplate.CreateWhatsappCloudApiTemplateInput,
        CreateWhatsappCloudApiTemplate.CreateWhatsappCloudApiTemplateOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ITemplateService _templateService;
    private readonly ILogger<CreateWhatsappCloudApiTemplate> _logger;

    public CreateWhatsappCloudApiTemplate(
        IWabaService wabaService,
        ITemplateService templateService,
        ILogger<CreateWhatsappCloudApiTemplate> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _templateService = templateService;
    }

    public class CreateWhatsappCloudApiTemplateInput
    {
        [JsonProperty("waba_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string WabaId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("whatsapp_cloud_api_create_template_object")]
        [System.ComponentModel.DataAnnotations.Required]
        public WhatsappCloudApiCreateTemplateObject WhatsappCloudApiCreateTemplateObject { get; set; }

        [JsonConstructor]
        public CreateWhatsappCloudApiTemplateInput(
            string wabaId,
            string sleekflowCompanyId,
            WhatsappCloudApiCreateTemplateObject whatsappCloudApiCreateTemplateObject)
        {
            WabaId = wabaId;
            SleekflowCompanyId = sleekflowCompanyId;
            WhatsappCloudApiCreateTemplateObject = whatsappCloudApiCreateTemplateObject;
        }
    }

    public class CreateWhatsappCloudApiTemplateOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateWhatsappCloudApiTemplateOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateWhatsappCloudApiTemplateOutput> F(
        CreateWhatsappCloudApiTemplateInput createWhatsappCloudApiTemplateInput)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(
            createWhatsappCloudApiTemplateInput.WabaId,
            createWhatsappCloudApiTemplateInput.SleekflowCompanyId);
        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        _logger.LogInformation(
            "Create cloud api whatsapp template with {FacebookBusinessId} and createWhatsappCloudApiTemplateInput.WhatsappCloudApiCreateTemplateObject {WhatsappCloudApiCreateTemplateObject}",
            waba.FacebookWabaId,
            JsonConvert.SerializeObject(createWhatsappCloudApiTemplateInput.WhatsappCloudApiCreateTemplateObject));

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var cloudApiWhatsappTemplateId = await _templateService.CreateCloudApiWhatsappTemplateAsync(
            waba.FacebookWabaId,
            createWhatsappCloudApiTemplateInput.WhatsappCloudApiCreateTemplateObject,
            hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null);

        return new CreateWhatsappCloudApiTemplateOutput(cloudApiWhatsappTemplateId);
    }
}