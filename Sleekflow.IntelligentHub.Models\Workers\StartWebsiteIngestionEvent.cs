using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Workers;

public class StartWebsiteIngestionEvent : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("document_id")]
    public string DocumentId { get; set; }

    [JsonConstructor]
    public StartWebsiteIngestionEvent(
        string sleekflowCompanyId,
        string documentId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        DocumentId = documentId;
    }
}