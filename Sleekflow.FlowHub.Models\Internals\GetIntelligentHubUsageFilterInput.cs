using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetIntelligentHubUsageFilterInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonConstructor]
    public GetIntelligentHubUsageFilterInput(string sleekflowCompanyId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
    }
}