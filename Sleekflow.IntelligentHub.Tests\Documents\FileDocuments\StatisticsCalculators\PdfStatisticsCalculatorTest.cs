using Moq;
using Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;
using Sleekflow.IntelligentHub.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.StatisticsCalculators;

[TestFixture]
[TestOf(typeof(PdfStatisticsCalculator))]
public class PdfStatisticsCalculatorTest
{
    // Relative path from the test execution directory to the Binaries folder
    const string PdfFilePath = "../../../Binaries/G2ZF03-001_20250307_202503070952.pdf";

    [Test]
    public void CalculateDocumentStatisticsTest()
    {
        var statisticsCalculator = new PdfStatisticsCalculator(new Mock<IDocumentCounterService>().Object);

        using var fileStream = new FileStream(
            PdfFilePath,
            FileMode.Open,
            FileAccess.Read,
            FileShare.Read); // Use FileShare.Read
        var documentStatistics = statisticsCalculator.CalculateDocumentStatistics(fileStream);

        Assert.That(documentStatistics.TotalPages, Is.EqualTo(2));
    }
}