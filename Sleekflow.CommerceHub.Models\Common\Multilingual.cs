using System.ComponentModel.DataAnnotations;
using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Common;

public class Multilingual
{
    [JsonConstructor]
    public Multilingual(
        string languageIsoCode,
        string value)
    {
        LanguageIsoCode = languageIsoCode;
        Value = value;
    }

    [Required]
    [StringLength(128, MinimumLength = 1)]
    [ValidateIsoLanguageCode]
    [SearchableField(IsFilterable = true, IsFacetable = true)]
    [JsonProperty("language_iso_code")]
    public string LanguageIsoCode { get; set; }

    [Required]
    [StringLength(4096, MinimumLength = 1)]
    [SearchableField(IsFilterable = true, IsFacetable = true)]
    [JsonProperty("value")]
    public string Value { get; set; }
}