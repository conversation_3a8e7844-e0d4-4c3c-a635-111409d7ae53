using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using Moq;
using NUnit.Framework;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Silos;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Tests.States;

[TestFixture]
public class StateServiceTests
{
    private Mock<IGrainFactory> _mockGrainFactory;
    private Mock<IIdService> _mockIdService;
    private Mock<IStateRepository> _mockStateRepository;
    private Mock<IAppConfig> _mockAppConfig;
    private Mock<IWorkflowService> _mockWorkflowService;
    private Mock<IDictGrain> _mockUsrVarDictGrain;
    private Mock<IDictGrain> _mockSysVarDictGrain;
    private Mock<IDictGrain> _mockSysCompanyVarDictGrain;
    private Mock<ISiloReadinessIndicator> _mockSiloReadinessIndicator;
    private Mock<ILogger<StateService>> _mockLogger;
    private Mock<IStateAggregator> _mockStateAggregator;
    private StateService _stateService;

    private string _stateId;
    private State _testState;
    private ProxyWorkflow _testWorkflow;
    private string _objectId;
    private string _objectType;
    private string _sleekflowCompanyId;
    private string _workflowId;
    private string _workflowVersionedId;
    private FlowHubConfig _flowHubConfig;
    private OnMessageReceivedEventBody _triggerEventBody;

    [SetUp]
    public void Setup()
    {
        _mockGrainFactory = new Mock<IGrainFactory>();
        _mockIdService = new Mock<IIdService>();
        _mockStateRepository = new Mock<IStateRepository>();
        _mockAppConfig = new Mock<IAppConfig>();
        _mockWorkflowService = new Mock<IWorkflowService>();
        _mockUsrVarDictGrain = new Mock<IDictGrain>();
        _mockSysVarDictGrain = new Mock<IDictGrain>();
        _mockSysCompanyVarDictGrain = new Mock<IDictGrain>();
        _mockSiloReadinessIndicator = new Mock<ISiloReadinessIndicator>();
        _mockLogger = new Mock<ILogger<StateService>>();
        _mockStateAggregator = new Mock<IStateAggregator>();

        _stateService = new StateService(
            _mockGrainFactory.Object,
            _mockIdService.Object,
            _mockStateRepository.Object,
            _mockAppConfig.Object,
            _mockWorkflowService.Object,
            _mockSiloReadinessIndicator.Object,
            _mockLogger.Object,
            _mockStateAggregator.Object);

        // Setup common test data
        _stateId = "state-123";
        _objectId = "object-123";
        _objectType = "Contact";
        _sleekflowCompanyId = "company-123";
        _workflowId = "workflow-123";
        _workflowVersionedId = "workflow-versioned-123";

        // Setup test state
        _testState = new State(
            _stateId,
            new StateIdentity(
                _objectId,
                _sleekflowCompanyId,
                _workflowId,
                _workflowVersionedId,
                _objectType),
            new StateWorkflowContext(new Workflow(
                _workflowId,
                _workflowVersionedId,
                "Test Workflow",
                "Standard",
                "group-123",
                null, // WorkflowTriggers
                null, // WorkflowEnrollmentSettings
                null, // WorkflowScheduleSettings
                null, // Steps
                "Active",
                "id-123",
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                _sleekflowCompanyId,
                null, // CreatedBy
                null, // UpdatedBy
                null, // Metadata
                "v1",
                null )),
            null, // TriggerEventBody
            "Running",
            null, // ReasonCode
            null, // StateTtl
            "https://example.com",
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false, // IsReenrollment
            null); // StateTtl

        // Setup test workflow
        _testWorkflow = new ProxyWorkflow(
            _testState.WorkflowContext.SnapshottedWorkflow,
            new List<Step>(),
            new Dictionary<string, object>());

        // Setup flow hub config - using constructor with required parameters
        _flowHubConfig = new FlowHubConfig(
            isEnrolled: true,
            usageLimit: null,
            usageLimitOffset: null,
            eTag: null,
            id: "config-123",
            origin: "https://example.com",
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow,
            sleekflowCompanyId: _sleekflowCompanyId,
            createdBy: null,
            updatedBy: null,
            isUsageLimitEnabled: false,
            isUsageLimitOffsetEnabled: true,
            isUsageLimitAutoScalingEnabled: false);

        // Setup trigger event body - using a concrete implementation instead of abstract EventBody
        _triggerEventBody = new OnMessageReceivedEventBody(
            DateTimeOffset.UtcNow,
            new OnMessageReceivedEventBodyMessage(
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                null,
                null,
                "text",
                "message-id",
                "Hello",
                "delivered",
                "direct"),
            "WhatsApp",
            "channel-123",
            "conversation-123",
            "message-123",
            "message-unique-123",
            "contact-123",
            new Dictionary<string, object?>
            {
                { "PhoneNumber", "+1234567890" },
                { "Email", "<EMAIL>" }
            },
            false);

        // Setup mock grain factory
        _mockGrainFactory
            .Setup(x => x.GetGrain<IDictGrain>(_stateId + "_usr", null))
            .Returns(_mockUsrVarDictGrain.Object);
        _mockGrainFactory
            .Setup(x => x.GetGrain<IDictGrain>(_stateId + "_sys", null))
            .Returns(_mockSysVarDictGrain.Object);
        _mockGrainFactory
            .Setup(x => x.GetGrain<IDictGrain>(_workflowId + "_" + _sleekflowCompanyId + "_sys_company", null))
            .Returns(_mockSysCompanyVarDictGrain.Object);

        // Setup mock state repository
        _mockStateRepository
            .Setup(x => x.GetAsync(_stateId, _stateId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Setup mock workflow service
        _mockWorkflowService
            .Setup(x => x.GetVersionedWorkflowOrDefaultAsync(
                _testState.Identity.SleekflowCompanyId,
                _testState.Identity.WorkflowVersionedId))
            .ReturnsAsync(_testWorkflow);

        // Setup mock app config
        _mockAppConfig
            .Setup(x => x.CoreInternalsEndpoint)
            .Returns("https://core-internals.example.com");

        // Setup mock id service
        _mockIdService
            .Setup(x => x.GetId("State"))
            .Returns(_stateId);
    }

    #region GetProxyStateAsync Tests

    [Test]
    public async Task GetProxyStateAsync_ShouldReturnProxyState()
    {
        // Act
        var result = await _stateService.GetProxyStateAsync(_stateId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(_stateId, result.Id);
        Assert.AreEqual(_testState.Identity.ObjectId, result.Identity.ObjectId);
        Assert.AreEqual(_testState.Identity.SleekflowCompanyId, result.Identity.SleekflowCompanyId);
        Assert.AreEqual(_testState.Identity.WorkflowId, result.Identity.WorkflowId);
        Assert.AreEqual(_testState.Identity.WorkflowVersionedId, result.Identity.WorkflowVersionedId);
        Assert.AreEqual(_testState.Identity.ObjectType, result.Identity.ObjectType);

        // Verify workflow context
        Assert.NotNull(result.WorkflowContext);
        Assert.NotNull(result.WorkflowContext.SnapshottedWorkflow);
        Assert.AreEqual(_testWorkflow.WorkflowId, result.WorkflowContext.SnapshottedWorkflow.WorkflowId);
        Assert.AreEqual(_testWorkflow.WorkflowVersionedId, result.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId);
        Assert.AreEqual(_testWorkflow.Name, result.WorkflowContext.SnapshottedWorkflow.Name);

        // Verify repository was called
        _mockStateRepository.Verify(x => x.GetAsync(_stateId, _stateId, It.IsAny<CancellationToken>()), Times.Once);

        // Verify workflow service was called
        _mockWorkflowService.Verify(x => x.GetVersionedWorkflowOrDefaultAsync(
            _testState.Identity.SleekflowCompanyId,
            _testState.Identity.WorkflowVersionedId), Times.Once);
    }

    [Test]
    public async Task GetProxyStateAsync_WhenWorkflowServiceReturnsNull_ShouldCreateProxyWorkflowFromState()
    {
        // Arrange
        _mockWorkflowService
            .Setup(x => x.GetVersionedWorkflowOrDefaultAsync(
                _testState.Identity.SleekflowCompanyId,
                _testState.Identity.WorkflowVersionedId))
            .ReturnsAsync((ProxyWorkflow)null);

        // Act
        var result = await _stateService.GetProxyStateAsync(_stateId);

        // Assert
        Assert.NotNull(result);
        Assert.NotNull(result.WorkflowContext);
        Assert.NotNull(result.WorkflowContext.SnapshottedWorkflow);
        Assert.AreEqual(_testState.WorkflowContext.SnapshottedWorkflow.WorkflowId, result.WorkflowContext.SnapshottedWorkflow.WorkflowId);
        Assert.AreEqual(_testState.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId, result.WorkflowContext.SnapshottedWorkflow.WorkflowVersionedId);
    }

    [Test]
    public void GetProxyStateAsync_WhenStateRepositoryThrowsException_ShouldPropagateException()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.GetAsync(_stateId, _stateId, It.IsAny<CancellationToken>()))
            .ThrowsAsync(new InvalidOperationException("Test exception"));

        // Act & Assert
        var exception = Assert.ThrowsAsync<InvalidOperationException>(
            async () => await _stateService.GetProxyStateAsync(_stateId));

        Assert.That(exception.Message, Is.EqualTo("Test exception"));
    }

    #endregion

    #region GetProxyStatesAsync Tests

    [Test]
    public async Task GetProxyStatesAsync_WithEmptyStatesList_ShouldReturnEmptyList()
    {
        // Arrange
        var states = new List<State>();
        var continuationToken = "token-123";
        var limit = 10;

        _mockStateRepository
            .Setup(x => x.GetContinuationTokenizedObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<string>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync((states, "next-token"));

        // Act
        var (proxyStates, nextContinuationToken) = await _stateService.GetProxyStatesAsync(
            _sleekflowCompanyId,
            continuationToken,
            limit,
            _objectId,
            _objectType);

        // Assert
        Assert.NotNull(proxyStates);
        Assert.AreEqual(0, proxyStates.Count);
        Assert.AreEqual("next-token", nextContinuationToken);
    }

    #endregion

    #region GetStateIdentityAsync Tests

    [Test]
    public async Task GetStateIdentityAsync_ShouldReturnStateIdentity()
    {
        // Act
        var result = await _stateService.GetStateIdentityAsync(_stateId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(_objectId, result.ObjectId);
        Assert.AreEqual(_sleekflowCompanyId, result.SleekflowCompanyId);
        Assert.AreEqual(_workflowId, result.WorkflowId);
        Assert.AreEqual(_workflowVersionedId, result.WorkflowVersionedId);
        Assert.AreEqual(_objectType, result.ObjectType);

        // Verify repository was called
        _mockStateRepository.Verify(x => x.GetAsync(_stateId, _stateId, It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region GetOrDefaultStateAsync Tests

    [Test]
    public async Task GetOrDefaultStateAsync_WhenStateExists_ShouldReturnProxyState()
    {
        // Arrange
        var states = new List<State> { _testState };

        _mockStateRepository
            .Setup(x => x.GetObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(states);

        // Act
        var result = await _stateService.GetOrDefaultStateAsync(_objectId, _objectType, _sleekflowCompanyId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(_stateId, result.Id);
    }

    #endregion

    #region GetOrDefaultStateByIdAsync Tests

    [Test]
    public async Task GetOrDefaultStateByIdAsync_WhenStateExistsAndCompanyMatches_ShouldReturnProxyState()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.GetOrDefaultAsync(_stateId, _stateId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Act
        var result = await _stateService.GetOrDefaultStateByIdAsync(_sleekflowCompanyId, _stateId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(_stateId, result.Id);
    }

    [Test]
    public async Task GetOrDefaultStateByIdAsync_WhenStateDoesNotExist_ShouldReturnNull()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.GetOrDefaultAsync(_stateId, _stateId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((State)null);

        // Act
        var result = await _stateService.GetOrDefaultStateByIdAsync(_sleekflowCompanyId, _stateId);

        // Assert
        Assert.Null(result);
    }

    [Test]
    public async Task GetOrDefaultStateByIdAsync_WhenCompanyDoesNotMatch_ShouldReturnNull()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.GetOrDefaultAsync(_stateId, _stateId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Act
        var result = await _stateService.GetOrDefaultStateByIdAsync("different-company", _stateId);

        // Assert
        Assert.Null(result);
    }

    #endregion

    #region GetRunningStatesAsync Tests

    [Test]
    public async Task GetRunningStatesAsync_ShouldReturnRunningStates()
    {
        // Arrange
        var states = new List<State> { _testState };

        _mockStateRepository
            .Setup(x => x.GetObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(states);

        // Act
        var result = await _stateService.GetRunningStatesAsync(_objectId, _objectType, _sleekflowCompanyId, _triggerEventBody);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(1, result.Count);
        Assert.AreEqual(_stateId, result[0].Id);
    }

    [Test]
    public async Task GetRunningStatesAsync_WithMessageReceivedEventBody_ShouldExtractContactFields()
    {
        // Arrange
        var states = new List<State> { _testState };

        _mockStateRepository
            .Setup(x => x.GetObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(states);

        // Act
        var result = await _stateService.GetRunningStatesAsync(_objectId, _objectType, _sleekflowCompanyId, _triggerEventBody);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(1, result.Count);

        // Verify repository was called with correct predicate that includes phone and email
        _mockStateRepository.Verify(
            x => x.GetObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()),
            Times.Once);
    }

    #endregion

    #region GetRunningStatesByWorkflowVersionedIdAsync Tests

    [Test]
    public async Task GetRunningStatesByWorkflowVersionedIdAsync_ShouldReturnRunningStates()
    {
        // Arrange
        var states = new List<State> { _testState };

        _mockStateRepository
            .Setup(x => x.GetObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(states);

        // Act
        var result = await _stateService.GetRunningStatesByWorkflowVersionedIdAsync(_sleekflowCompanyId, _workflowVersionedId);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(1, result.Count);
        Assert.AreEqual(_stateId, result[0].Id);
    }

    #endregion

    #region GetOrCreateStateAsync Tests

    [Test]
    public async Task GetOrCreateStateAsync_WhenStateExists_ShouldReturnExistingState()
    {
        // Arrange
        var states = new List<State> { _testState };

        _mockStateRepository
            .Setup(x => x.GetObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(states);

        // Act
        var result = await _stateService.GetOrCreateStateAsync(
            _objectId,
            _objectType,
            _sleekflowCompanyId,
            false,
            _testWorkflow,
            _triggerEventBody,
            _flowHubConfig);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(_stateId, result.Id);

        // Verify create was not called
        _mockStateRepository.Verify(
            x => x.CreateAndGetAsync(
                It.IsAny<State>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Test]
    public async Task GetOrCreateStateAsync_WhenStateDoesNotExist_ShouldCreateAndReturnNewState()
    {
        // Arrange
        var states = new List<State>();

        _mockStateRepository
            .Setup(x => x.GetObjectsAsync(
                It.IsAny<Expression<Func<State, bool>>>(),
                It.IsAny<int>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(states);

        _mockStateRepository
            .Setup(x => x.CreateAndGetAsync(
                It.IsAny<State>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Act
        var result = await _stateService.GetOrCreateStateAsync(
            _objectId,
            _objectType,
            _sleekflowCompanyId,
            false,
            _testWorkflow,
            _triggerEventBody,
            _flowHubConfig);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(_stateId, result.Id);

        // Verify create was called
        _mockStateRepository.Verify(x => x.CreateAndGetAsync(
            It.IsAny<State>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region CreateStateAsync Tests

    [Test]
    public async Task CreateStateAsync_ShouldCreateAndReturnNewState()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.CreateAndGetAsync(
                It.IsAny<State>(),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Act
        var result = await _stateService.CreateStateAsync(
            _objectId,
            _objectType,
            _sleekflowCompanyId,
            "Running",
            null,
            null,
            false,
            _testWorkflow,
            _triggerEventBody,
            _flowHubConfig);

        // Assert
        Assert.NotNull(result);
        Assert.AreEqual(_stateId, result.Id);

        // Verify id service was called
        _mockIdService.Verify(x => x.GetId("State"), Times.Once);

        // Verify grain factory was called
        _mockGrainFactory.Verify(x => x.GetGrain<IDictGrain>(_stateId + "_usr", null), Times.Once);
        _mockGrainFactory.Verify(x => x.GetGrain<IDictGrain>(_stateId + "_sys", null), Times.Once);
        _mockGrainFactory.Verify(x => x.GetGrain<IDictGrain>(_workflowId + "_" + _sleekflowCompanyId + "_sys_company", null), Times.Once);

        // Verify repository was called
        _mockStateRepository.Verify(x => x.CreateAndGetAsync(
            It.IsAny<State>(),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task CreateStateAsync_WithCustomOrigin_ShouldUseProvidedOrigin()
    {
        // Arrange
        var customOrigin = "https://custom.example.com";
        var customFlowHubConfig = new FlowHubConfig(
            isEnrolled: true,
            usageLimit: null,
            usageLimitOffset: null,
            eTag: null,
            id: "config-123",
            origin: customOrigin,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow,
            sleekflowCompanyId: _sleekflowCompanyId,
            createdBy: null,
            updatedBy: null,
            isUsageLimitEnabled: false,
            isUsageLimitOffsetEnabled: true,
            isUsageLimitAutoScalingEnabled: false);

        _mockStateRepository
            .Setup(x => x.CreateAndGetAsync(
                It.Is<State>(s => s.Origin == customOrigin),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Act
        var result = await _stateService.CreateStateAsync(
            _objectId,
            _objectType,
            _sleekflowCompanyId,
            "Running",
            null,
            null,
            false,
            _testWorkflow,
            _triggerEventBody,
            customFlowHubConfig);

        // Assert
        Assert.NotNull(result);

        // Verify repository was called with state having custom origin
        _mockStateRepository.Verify(x => x.CreateAndGetAsync(
            It.Is<State>(s => s.Origin == customOrigin),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task CreateStateAsync_WithEmptyOrigin_ShouldUseAppConfigOrigin()
    {
        // Arrange
        var appConfigEndpoint = "https://app-config.example.com";
        var emptyOriginFlowHubConfig = new FlowHubConfig(
            isEnrolled: true,
            usageLimit: null,
            usageLimitOffset: null,
            eTag: null,
            id: "config-123",
            origin: "",
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow,
            sleekflowCompanyId: _sleekflowCompanyId,
            createdBy: null,
            updatedBy: null,
            isUsageLimitEnabled: false,
            isUsageLimitOffsetEnabled: true,
            isUsageLimitAutoScalingEnabled: false);

        _mockAppConfig
            .Setup(x => x.CoreInternalsEndpoint)
            .Returns(appConfigEndpoint);

        _mockStateRepository
            .Setup(x => x.CreateAndGetAsync(
                It.Is<State>(s => s.Origin == appConfigEndpoint),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Act
        var result = await _stateService.CreateStateAsync(
            _objectId,
            _objectType,
            _sleekflowCompanyId,
            "Running",
            null,
            null,
            false,
            _testWorkflow,
            _triggerEventBody,
            emptyOriginFlowHubConfig);

        // Assert
        Assert.NotNull(result);

        // Verify repository was called with state having app config origin
        _mockStateRepository.Verify(x => x.CreateAndGetAsync(
            It.Is<State>(s => s.Origin == appConfigEndpoint),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task CreateStateAsync_WithNullOrigin_ShouldUseAppConfigOrigin()
    {
        // Arrange
        var appConfigEndpoint = "https://app-config.example.com";
        var nullOriginFlowHubConfig = new FlowHubConfig(
            isEnrolled: true,
            usageLimit: null,
            usageLimitOffset: null,
            eTag: null,
            id: "config-123",
            origin: null,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow,
            sleekflowCompanyId: _sleekflowCompanyId,
            createdBy: null,
            updatedBy: null,
            isUsageLimitEnabled: false,
            isUsageLimitOffsetEnabled: true,
            isUsageLimitAutoScalingEnabled: false);

        _mockAppConfig
            .Setup(x => x.CoreInternalsEndpoint)
            .Returns(appConfigEndpoint);

        _mockStateRepository
            .Setup(x => x.CreateAndGetAsync(
                It.Is<State>(s => s.Origin == appConfigEndpoint),
                It.IsAny<string>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        // Act
        var result = await _stateService.CreateStateAsync(
            _objectId,
            _objectType,
            _sleekflowCompanyId,
            "Running",
            null,
            null,
            false,
            _testWorkflow,
            _triggerEventBody,
            nullOriginFlowHubConfig);

        // Assert
        Assert.NotNull(result);

        // Verify repository was called with state having app config origin
        _mockStateRepository.Verify(x => x.CreateAndGetAsync(
            It.Is<State>(s => s.Origin == appConfigEndpoint),
            It.IsAny<string>(),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    #endregion

    #region CompleteStateAsync Tests

    [Test]
    public async Task CompleteStateAsync_ShouldUpdateStateStatusToComplete()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.PatchAsync(
                _stateId,
                _stateId,
                It.IsAny<List<PatchOperation>>(),
                null,
                null,
                CancellationToken.None))
            .Returns(Task.FromResult(1));

        // Act
        await _stateService.CompleteStateAsync(_stateId);

        // Assert
        _mockStateRepository.Verify(
            x => x.PatchAsync(
                _stateId,
                _stateId,
                It.Is<List<PatchOperation>>(
                    ops =>
                        ops.Any(op => op.Path == "/state_status")),
                null,
                null,
                CancellationToken.None),
            Times.Once);
    }

    #endregion

    #region FailStateAsync Tests

    [Test]
    public async Task FailStateAsync_ShouldUpdateStateStatusToFailed()
    {
        // Arrange
        var reasonCode = "test-reason";
        _mockStateRepository
            .Setup(x => x.PatchAsync(
                _stateId,
                _stateId,
                It.IsAny<List<PatchOperation>>(),
                null,
                null,
                CancellationToken.None))
            .Returns(Task.FromResult(1));

        // Act
        await _stateService.FailStateAsync(_stateId, reasonCode);

        // Assert
        _mockStateRepository.Verify(
            x => x.PatchAsync(
                _stateId,
                _stateId,
                It.Is<List<PatchOperation>>(
                    ops =>
                        ops.Any(op => op.Path == "/state_status") &&
                        ops.Any(op => op.Path == $"/{StateFieldNames.StateReasonCode}")),
                null,
                null,
                CancellationToken.None),
            Times.Once);
    }

    #endregion

    #region ArchiveStateAsync Tests

    [Test]
    public async Task ArchiveStateAsync_ShouldDeleteStateAndClearDictionaries()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.GetOrDefaultAsync(_stateId, _stateId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(_testState);

        _mockStateRepository
            .Setup(x => x.DeleteAsync(
                _stateId,
                _stateId,
                null,
                null,
                CancellationToken.None))
            .Returns(Task.FromResult(1));

        _mockUsrVarDictGrain
            .Setup(x => x.ClearAsync())
            .Returns(Task.CompletedTask);

        _mockSysVarDictGrain
            .Setup(x => x.ClearAsync())
            .Returns(Task.CompletedTask);

        _mockSysCompanyVarDictGrain
            .Setup(x => x.ClearAsync())
            .Returns(Task.CompletedTask);

        // Act
        await _stateService.ArchiveStateAsync(_stateId);

        // Assert
        _mockStateRepository.Verify(
            x => x.DeleteAsync(
                _stateId,
                _stateId,
                It.IsAny<string>(),
                It.IsAny<TransactionalBatch>(),
                It.IsAny<CancellationToken>()),
            Times.Once);

        _mockUsrVarDictGrain.Verify(x => x.ClearAsync(), Times.Once);
        _mockSysVarDictGrain.Verify(x => x.ClearAsync(), Times.Once);
        _mockSysCompanyVarDictGrain.Verify(x => x.ClearAsync(), Times.Once);
    }

    [Test]
    public async Task ArchiveStateAsync_WhenStateDoesNotExist_ShouldNotDeleteOrClearDictionaries()
    {
        // Arrange
        _mockStateRepository
            .Setup(x => x.GetOrDefaultAsync(_stateId, _stateId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((State)null);

        // Act
        await _stateService.ArchiveStateAsync(_stateId);

        // Assert
        _mockStateRepository.Verify(
            x => x.DeleteAsync(
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<TransactionalBatch>(),
                It.IsAny<CancellationToken>()),
            Times.Never);

        _mockUsrVarDictGrain.Verify(x => x.ClearAsync(), Times.Never);
        _mockSysVarDictGrain.Verify(x => x.ClearAsync(), Times.Never);
        _mockSysCompanyVarDictGrain.Verify(x => x.ClearAsync(), Times.Never);
    }

    #endregion
}
