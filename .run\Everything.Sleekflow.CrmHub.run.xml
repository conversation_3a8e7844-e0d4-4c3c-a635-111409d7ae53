﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Everything.Sleekflow.CrmHub" type="CompoundRunConfigurationType">
    <toRun name="Sleekflow.CrmHub" type="DotNetProject" />
    <toRun name="Sleekflow.Integrator.Dynamics365" type="DotNetProject" />
    <toRun name="Sleekflow.Integrator.Hubspot" type="DotNetProject" />
    <toRun name="Sleekflow.Integrator.Salesforce" type="DotNetProject" />
    <toRun name="Sleekflow.WebhookHub" type="DotNetProject" />
    <toRun name="Sleekflow.CrmHub.Workers" type="AzureFunctionsHost" />
    <method v="2" />
  </configuration>
</component>