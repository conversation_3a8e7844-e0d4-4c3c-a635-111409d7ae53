﻿namespace Sleekflow.Exceptions.FlowHub;

public class SfWorkflowGroupDuplicateNameException : ErrorCodeException
{
    public string WorkflowGroupId { get; }

    public string WorkflowGroupName { get; }

    public SfWorkflowGroupDuplicateNameException(
        string workflowGroupId,
        string workflowGroupName)
        : base(
            ErrorCodeConstant.SfWorkflowGroupNameDuplicateException,
            $"The name '{workflowGroupName}' is already in use by workflow group id '{workflowGroupId}'")
    {
        WorkflowGroupId = workflowGroupId;
        WorkflowGroupName = workflowGroupName;
    }
}