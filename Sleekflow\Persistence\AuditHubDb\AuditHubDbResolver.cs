using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.AuditHubDb;

public interface IAuditHubDbResolver : IContainerResolver
{
}

public class AuditHubDbResolver : IAuditHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public AuditHubDbResolver(IAuditHubDbConfig auditHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            auditHubDbConfig.Endpoint,
            auditHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),

                // Consistency, Session, Properties, and Triggers are not allowed when AllowBulkExecution is set to true.
                // AllowBulkExecution = true,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}