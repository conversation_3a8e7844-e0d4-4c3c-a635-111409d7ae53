using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Balances.SQL;

public interface ITransactionLogCalculatedBusinessBalanceService
{
    Task<TransactionLogCalculatedBusinessBalance?>
        AccumulateBusinessBalanceTransactionLogAsync(string facebookBusinessId);

    Task<TransactionLogCalculatedBusinessBalance?>
        AccumulateBusinessBalanceTransactionLogByFacebookWabaIdAsync(string facebookBusinessId, string facebookWabaId);
}

public class TransactionLogCalculatedBusinessBalanceService
    : ITransactionLogCalculatedBusinessBalanceService, ISingletonService
{
    private readonly
        ITransactionLogCalculatedBusinessBalanceRepository _transactionLogCalculatedBusinessBalanceRepository;

    public TransactionLogCalculatedBusinessBalanceService(
        ITransactionLogCalculatedBusinessBalanceRepository transactionLogCalculatedBusinessBalanceRepository)
    {
        _transactionLogCalculatedBusinessBalanceRepository = transactionLogCalculatedBusinessBalanceRepository;
    }

    public async Task<TransactionLogCalculatedBusinessBalance?>
        AccumulateBusinessBalanceTransactionLogAsync(string facebookBusinessId)
    {
        return await _transactionLogCalculatedBusinessBalanceRepository
            .AccumulateBusinessBalanceTransactionLogAsync(facebookBusinessId);
    }

    public async Task<TransactionLogCalculatedBusinessBalance?>
        AccumulateBusinessBalanceTransactionLogByFacebookWabaIdAsync(
            string facebookBusinessId,
            string facebookWabaId)
    {
        return await _transactionLogCalculatedBusinessBalanceRepository
            .AccumulateBusinessBalanceTransactionLogByFacebookWabaIdAsync(facebookBusinessId, facebookWabaId);
    }
}