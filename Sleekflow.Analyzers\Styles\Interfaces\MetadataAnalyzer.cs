using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class MetadataAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1001";
    public const string Category = "Design";

    private static readonly LocalizableString Title = "Class should implement IHasMetadata";

    private static readonly LocalizableString MessageFormat =
        "Class '{0}' has a Metadata field and should implement 'Sleekflow.Persistence.Abstractions.IHasMetadata'";

    private static readonly LocalizableString Description =
        "When a class has a Metadata field, it should implement the IHasMetadata interface.";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSymbolAction(AnalyzeSymbol, SymbolKind.NamedType);
    }

    private static void AnalyzeSymbol(SymbolAnalysisContext context)
    {
        var namedTypeSymbol = (INamedTypeSymbol) context.Symbol;

        // Check if the class has a Metadata property
        var metadataProperty = namedTypeSymbol.GetMembers()
            .FirstOrDefault(m => m.Kind == SymbolKind.Property && m.Name == "Metadata");
        if (metadataProperty == null)
        {
            return;
        }

        // Check if the class already implements IHasMetadata
        var iHasMetadataInterface = namedTypeSymbol.Interfaces.FirstOrDefault(
            i => i.ToDisplayString() == "Sleekflow.Persistence.Abstractions.IHasMetadata");
        if (iHasMetadataInterface != null)
        {
            return;
        }

        // Create a diagnostic and report it
        var diagnostic = Diagnostic.Create(Rule, namedTypeSymbol.Locations[0], namedTypeSymbol.Name);
        context.ReportDiagnostic(diagnostic);
    }
}