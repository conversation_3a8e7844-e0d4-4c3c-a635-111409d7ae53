﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Companies;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Locks;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.States;

[TriggerGroup(ControllerNames.States)]
public class BulkReenrollStates : ITrigger<
    BulkReenrollStates.BulkReenrollStatesInput,
    BulkReenrollStates.BulkReenrollStatesOutput>
{
    private readonly IBus _bus;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IWorkflowService _workflowService;
    private readonly ICompanyUsageCycleService _companyUsageCycleService;
    private readonly ILockService _lockService;

    public class BulkReenrollStatesInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [Required]
        [AllowedStringValues(
            isIgnoreCase: true,
            allowedValues: [
                Models.Constants.ReenrollmentPeriod.CurrentUsageCycle,
                Models.Constants.ReenrollmentPeriod.FromFirstOccurrence
            ])]
        [JsonProperty("reenrollment_period")]
        public string ReenrollmentPeriod { get; set; }

        [JsonConstructor]
        public BulkReenrollStatesInput(
            string sleekflowCompanyId,
            string workflowId,
            string reenrollmentPeriod)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            ReenrollmentPeriod = reenrollmentPeriod;
        }
    }

    public class BulkReenrollStatesOutput
    {

    }

    public BulkReenrollStates(
        IBus bus,
        IFlowHubConfigService flowHubConfigService,
        IWorkflowService workflowService,
        ICompanyUsageCycleService companyUsageCycleService,
        ILockService lockService)
    {
        _bus = bus;
        _flowHubConfigService = flowHubConfigService;
        _workflowService = workflowService;
        _companyUsageCycleService = companyUsageCycleService;
        _lockService = lockService;
    }

    public async Task<BulkReenrollStatesOutput> F(BulkReenrollStatesInput input)
    {
        var activeWorkflow = await _workflowService.GetActiveWorkflowAsync(
            input.WorkflowId,
            input.SleekflowCompanyId);

        if (activeWorkflow is null)
        {
            throw new SfWorkflowNotActiveException();
        }

        var (isLocked, expireIn) = await _lockService.IsLockedAsync(
            new[]
            {
                "reenrollment",
                input.SleekflowCompanyId,
                activeWorkflow.WorkflowVersionedId,
            });

        if (isLocked)
        {
            throw new SfConcurrentOperationProhibitedException(expireIn);
        }

        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(
            input.SleekflowCompanyId);

        var periodFrom = DateTimeOffset.MinValue;
        var periodTo = DateTimeOffset.UtcNow;

        if (input.ReenrollmentPeriod is ReenrollmentPeriod.CurrentUsageCycle)
        {
            var usageCycle = await _companyUsageCycleService.GetUsageCycleAsync(
                flowHubConfig);

            periodFrom = usageCycle.FromDateTime;
            periodTo = usageCycle.ToDateTime;
        }

        var @event = new OnWorkflowBulkReenrollmentInitiatedEvent(
            input.SleekflowCompanyId,
            activeWorkflow.WorkflowVersionedId,
            periodFrom,
            periodTo);

        await _bus.Publish(@event);

        return new();
    }
}