﻿using System.Text;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

public record SanitizedChatResult(List<string> EntryTexts, List<SfChatEntry> ChatEntries);

public static class GroupChatHistoryUtils
{
    public static SanitizedChatResult GetSanitizedChatEntries(List<SfChatEntry> chatEntries)
    {
        var entryTexts = new List<string>();
        var sanitizedChatEntries = new List<SfChatEntry>();

        foreach (var chatEntry in chatEntries)
        {
            // Add the current entry to the sanitized list
            sanitizedChatEntries.Add(chatEntry);

            var messageBuilder = new StringBuilder();

            if (chatEntry.Bot != null)
            {
                messageBuilder.AppendLine($"Our Company: {chatEntry.Bot}");
            }

            if (chatEntry.User != null)
            {
                messageBuilder.AppendLine($"Customer: {chatEntry.User}");

                // When the user sends a /clear command, clear both the chat history and entries
                if (chatEntry.User.Contains("/clear"))
                {
                    entryTexts.Clear();
                    sanitizedChatEntries.Clear();
                    continue;
                }
            }

            // Process attached files if available
            if (chatEntry.Files != null && chatEntry.Files.Count > 0)
            {
                foreach (var file in chatEntry.Files)
                {
                    messageBuilder.AppendLine($"[FILE ATTACHED: {file.MimeType}, Size: {file.FileSize} bytes, URL: {file.Url}]");
                }
            }

            if (messageBuilder.Length > 0)
            {
                entryTexts.Add(messageBuilder.ToString());
            }
        }

        return new SanitizedChatResult(entryTexts, sanitizedChatEntries);
    }

    /// <summary>
    /// Enhanced version that extracts text content from files using LLM
    /// </summary>
    /// <param name="chatEntries">The chat entries to process</param>
    /// <param name="kernel">Semantic kernel instance</param>
    /// <param name="fileContentExtractionPlugin">Plugin for extracting file content</param>
    /// <param name="background">Background information for the chat</param>
    /// <returns>Sanitized chat result with file content extracted</returns>
    public static async Task<SanitizedChatResult> GetSanitizedChatEntriesWithFileContentAsync(
        List<SfChatEntry> chatEntries,
        Kernel kernel,
        IFileContentExtractionPlugin fileContentExtractionPlugin,
        string background)
    {
        var entryTexts = new List<string>();
        var sanitizedChatEntries = new List<SfChatEntry>();

        foreach (var chatEntry in chatEntries)
        {
            // Add the current entry to the sanitized list
            sanitizedChatEntries.Add(chatEntry);

            var messageBuilder = new StringBuilder();

            if (chatEntry.Bot != null)
            {
                messageBuilder.AppendLine($"Our Company: {chatEntry.Bot}");
            }

            if (chatEntry.User != null)
            {
                messageBuilder.AppendLine($"Customer: {chatEntry.User}");

                // When the user sends a /clear command, clear both the chat history and entries
                if (chatEntry.User.Contains("/clear"))
                {
                    entryTexts.Clear();
                    sanitizedChatEntries.Clear();
                    continue;
                }
            }

            // Process attached files if available
            if (chatEntry.Files != null && chatEntry.Files.Count > 0)
            {
                foreach (var file in chatEntry.Files)
                {
                    try
                    {
                        // Extract text content from the file
                        var extractedContent = await fileContentExtractionPlugin.ExtractTextFromFileAsync(kernel, file, background);
                        messageBuilder.AppendLine($"[FILE CONTENT - {file.MimeType}]:\n{extractedContent}\n[END FILE CONTENT]");
                    }
                    catch (Exception ex)
                    {
                        // Fallback to basic file information if extraction fails
                        messageBuilder.AppendLine($"[FILE ATTACHED: {file.MimeType}, Size: {file.FileSize} bytes, URL: {file.Url}] (Content extraction failed: {ex.Message})");
                    }
                }
            }

            if (messageBuilder.Length > 0)
            {
                entryTexts.Add(messageBuilder.ToString());
            }
        }

        return new SanitizedChatResult(entryTexts, sanitizedChatEntries);
    }
}