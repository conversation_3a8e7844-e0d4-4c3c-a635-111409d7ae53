﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnContactRecurrentlyEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactRecurrentlyEnrolled; }
    }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [Required]
    [JsonProperty("next_recurrence")]
    public DateTimeOffset NextRecurrence { get; set; }

    [JsonConstructor]
    public OnContactRecurrentlyEnrolledEventBody(
        DateTimeOffset createdAt,
        string contactId,
        string workflowId,
        string workflowVersionedId,
        DateTimeOffset nextRecurrence)
        : base(createdAt)
    {
        ContactId = contactId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        NextRecurrence = nextRecurrence;
    }
}