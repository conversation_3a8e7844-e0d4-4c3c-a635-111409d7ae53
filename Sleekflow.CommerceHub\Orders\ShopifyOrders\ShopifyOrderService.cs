using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Orders.ShopifyOrders;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Orders.ShopifyOrders;

public interface IShopifyOrderService
{
    Task<Order?> GetShopifyOrderAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string storeId,
        string shopifyOrderName);

    Task<Order> CreateAndGetShopifyOrderAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        string storeId,
        Dictionary<string, object?> providerOrder,
        UserProfile userProfile,
        string conversionStatus,
        string? sleekflowPlatformCountry,
        DateTimeOffset? paymentLinkSentAt,
        Dictionary<string, object?> metadata);

    Task<Order> PatchAndGetShopifyOrderAsync(
        string orderId,
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        Dictionary<string, object?> providerOrder,
        string conversionStatus,
        string? sleekflowPlatformCountry,
        DateTimeOffset? paymentLinkSentAt,
        Dictionary<string, object?> metadata,
        List<string> recordStatuses);

    Task<ShopifyOrderStatistics> GetShopifyOrderStatisticsAsync(
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string? sleekflowStaffTeamId,
        string? conversionStatus,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime);
}

public class ShopifyOrderService : IShopifyOrderService, IScopedService
{
    private readonly IOrderRepository _orderRepository;
    private readonly IIdService _idService;

    public ShopifyOrderService(
        IOrderRepository orderRepository,
        IIdService idService)
    {
        _orderRepository = orderRepository;
        _idService = idService;
    }

    public async Task<Order?> GetShopifyOrderAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string storeId,
        string shopifyOrderName)
    {
        var orders = await _orderRepository.GetObjectsAsync(
            s =>
                s.SleekflowCompanyId == sleekflowCompanyId
                && s.SleekflowUserProfileId == sleekflowUserProfileId
                && s.StoreId == storeId
                && s.OrderExternalIntegrationInfo != null
                && s.OrderExternalIntegrationInfo.ProviderName == "shopify");

        foreach (var order in orders)
        {
            if (order.OrderExternalIntegrationInfo is ShopifyOrderExternalIntegrationInfo shopifyInfo
                && shopifyInfo.OrderName == shopifyOrderName)
            {
                return order;
            }
        }

        return null;
    }

    public async Task<Order> CreateAndGetShopifyOrderAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        string storeId,
        Dictionary<string, object?> providerOrder,
        UserProfile userProfile,
        string conversionStatus,
        string? sleekflowPlatformCountry,
        DateTimeOffset? paymentLinkSentAt,
        Dictionary<string, object?> metadata)
    {
        var createdAt = ((DateTimeOffset) providerOrder["CreatedAt"]!).UtcDateTime;
        var updatedAt = ((DateTimeOffset) providerOrder["UpdatedAt"]!).UtcDateTime;
        var shopifyPaymentStatus = (string) providerOrder["FinancialStatus"]!;
        var currencyIsoCode = (string) providerOrder["Currency"]!;
        var totalPrice = (decimal) providerOrder["CurrentTotalPrice"]!;
        var subtotalPrice = (decimal) providerOrder["CurrentSubtotalPrice"]!;
        var languageIsoCode = ((string) providerOrder["CustomerLocale"]!).ToLower();

        var orderDiscount = GetOrderDiscountFromShopifyProviderOrder(providerOrder);

        var orderStatus = GetOrderStatusFromShopifyProviderOrder(providerOrder);

        var countryIsoCode = GetOrderCountryIsoCodeFromShopifyProviderOrder(providerOrder);

        var shopifyOrderExternalIntegrationInfo = ConstructShopifyOrderExternalIntegrationInfo(
            providerOrder,
            conversionStatus,
            sleekflowPlatformCountry);

        var sleekflowStaff =
            sleekflowStaffId != null
                ? new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowStaffTeamIds)
                : null;

        return await _orderRepository.UpsertAndGetAsync(
            new Order(
                _idService.GetId(SysTypeNames.Order),
                sleekflowCompanyId,
                storeId,
                sleekflowUserProfileId,
                userProfile,
                null,
                new List<OrderLineItem>(),
                currencyIsoCode,
                languageIsoCode,
                totalPrice,
                subtotalPrice,
                orderDiscount,
                countryIsoCode,
                orderStatus,
                shopifyPaymentStatus,
                paymentLinkSentAt,
                null,
                metadata,
                shopifyOrderExternalIntegrationInfo,
                new List<string>
                {
                    "Active"
                },
                createdAt,
                updatedAt,
                sleekflowStaff,
                sleekflowStaff),
            sleekflowCompanyId);
    }

    public async Task<Order> PatchAndGetShopifyOrderAsync(
        string orderId,
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        Dictionary<string, object?> providerOrder,
        string conversionStatus,
        string? sleekflowPlatformCountry,
        DateTimeOffset? paymentLinkSentAt,
        Dictionary<string, object?> metadata,
        List<string> recordStatuses)
    {
        var updatedAt = ((DateTimeOffset) providerOrder["UpdatedAt"]!).UtcDateTime;
        var shopifyPaymentStatus = (string) providerOrder["FinancialStatus"]!;
        var totalPrice = (decimal) providerOrder["CurrentTotalPrice"]!;
        var subtotalPrice = (decimal) providerOrder["CurrentSubtotalPrice"]!;

        var orderDiscount = GetOrderDiscountFromShopifyProviderOrder(providerOrder);

        var orderStatus = GetOrderStatusFromShopifyProviderOrder(providerOrder);

        var countryIsoCode = GetOrderCountryIsoCodeFromShopifyProviderOrder(providerOrder);

        var shopifyOrderExternalIntegrationInfo = ConstructShopifyOrderExternalIntegrationInfo(
            providerOrder,
            conversionStatus,
            sleekflowPlatformCountry);

        var sleekflowStaff =
            sleekflowStaffId != null
                ? new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowStaffTeamIds)
                : null;

        return await _orderRepository.PatchAndGetAsync(
            orderId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/line_items", new List<OrderLineItem>()),
                PatchOperation.Replace("/total_price", totalPrice),
                PatchOperation.Replace("/subtotal_price", subtotalPrice),
                PatchOperation.Set("/order_discount", orderDiscount),
                PatchOperation.Set("/country_iso_code", countryIsoCode),
                PatchOperation.Replace("/order_status", orderStatus),
                PatchOperation.Replace("/payment_status", shopifyPaymentStatus),
                PatchOperation.Set("/payment_link_sent_at", paymentLinkSentAt),
                PatchOperation.Replace("/metadata", metadata),
                PatchOperation.Set("/order_external_integration_info", shopifyOrderExternalIntegrationInfo),
                PatchOperation.Replace("/record_statuses", recordStatuses),
                PatchOperation.Set("/updated_by", sleekflowStaff),
                PatchOperation.Replace("/updated_at", updatedAt)
            });
    }

    public async Task<ShopifyOrderStatistics> GetShopifyOrderStatisticsAsync(
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string? sleekflowStaffTeamId,
        string? conversionStatus,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime)
    {
        var shopifyOrders = await _orderRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.CreatedAt >= fromDateTime
                && c.CreatedAt <= toDateTime
                && c.OrderExternalIntegrationInfo != null
                && c.OrderExternalIntegrationInfo.ProviderName == "shopify");

        if (sleekflowStaffId is not null)
        {
            shopifyOrders = shopifyOrders
                .Where(c => c.CreatedBy?.SleekflowStaffId == sleekflowStaffId)
                .ToList();
        }

        if (sleekflowStaffTeamId is not null)
        {
            shopifyOrders = shopifyOrders
                .Where(c => c.CreatedBy?.SleekflowStaffTeamIds?.Contains(sleekflowStaffTeamId) == true)
                .ToList();
        }

        var shopifyOrderStatistics = new ShopifyOrderStatistics(0, 0);
        foreach (var shopifyOrder in shopifyOrders)
        {
            if (shopifyOrder.OrderExternalIntegrationInfo is ShopifyOrderExternalIntegrationInfo shopifyInfo
                && (conversionStatus is null
                    || shopifyInfo.ConversionStatus == conversionStatus))
            {
                shopifyOrderStatistics.Count++;
                shopifyOrderStatistics.TotalPrice += shopifyOrder.TotalPrice;
            }
        }

        return shopifyOrderStatistics;
    }

    private static ShopifyOrderExternalIntegrationInfo ConstructShopifyOrderExternalIntegrationInfo(
        IReadOnlyDictionary<string, object?> providerOrder,
        string conversionStatus,
        string? sleekflowPlatformCountry)
    {
        var orderName = (string)providerOrder["Name"]!;
        var url = (string)providerOrder["OrderStatusUrl"]!;
        var providerOrderId = (string)providerOrder["Id"]!;
        var fulfillmentStatus = (string)providerOrder["FulfillmentStatus"]!;
        var orderNote = (string)providerOrder["Note"]!;
        var shopifyPaymentStatus = (string)providerOrder["FinancialStatus"]!;
        var cartToken = (string)providerOrder["CartToken"]!;
        var totalTax = (decimal)providerOrder["CurrentTotalTax"]!;
        var tags = providerOrder["Tags"] is null
            ? new List<string>()
            : ((string)providerOrder["Tags"]!).Split(",").ToList();

        var orderStatus = GetOrderStatusFromShopifyProviderOrder(providerOrder);

        var shopifyCustomerId = GetShopifyCustomerIdFromShopifyProviderOrder(providerOrder);

        return new ShopifyOrderExternalIntegrationInfo(
            orderName,
            orderNote,
            url,
            shopifyCustomerId,
            orderStatus,
            shopifyPaymentStatus,
            fulfillmentStatus,
            conversionStatus,
            cartToken,
            sleekflowPlatformCountry,
            totalTax,
            tags,
            providerOrderId);
    }

    private static string GetOrderStatusFromShopifyProviderOrder(IReadOnlyDictionary<string, object?> providerOrder)
    {
        if (providerOrder["CancelledAt"] is not null)
        {
            return "cancelled";
        }

        if (providerOrder["ClosedAt"] is not null)
        {
            return "closed";
        }

        return "open";
    }

    private static string? GetOrderCountryIsoCodeFromShopifyProviderOrder(IReadOnlyDictionary<string, object?> providerOrder)
    {
        if (providerOrder["ShippingAddress"] is not null)
        {
            var shippingAddress = (ShopifySharp.Address) providerOrder["ShippingAddress"]!;

            if (shippingAddress.CountryCode is not null)
            {
                return shippingAddress.CountryCode;
            }
        }

        if (providerOrder["BillingAddress"] is not null)
        {
            var billingAddress = (ShopifySharp.Address) providerOrder["BillingAddress"]!;

            if (billingAddress.CountryCode is not null)
            {
                return billingAddress.CountryCode;
            }
        }

        return null;
    }

    private static string? GetShopifyCustomerIdFromShopifyProviderOrder(IReadOnlyDictionary<string, object?> providerOrder)
    {
        if (providerOrder["Customer"] is not null)
        {
            var customer = (ShopifySharp.Customer) providerOrder["Customer"]!;

            if (customer.Id is not null)
            {
                return customer.Id!.Value.ToString();
            }
        }

        return null;
    }

    private static Discount? GetOrderDiscountFromShopifyProviderOrder(IReadOnlyDictionary<string, object?> providerOrder)
    {
        if (providerOrder["CurrentTotalDiscounts"] is null)
        {
            return null;
        }

        return new Discount(
            null,
            null,
            (decimal) providerOrder["CurrentTotalDiscounts"]!,
            DiscountTypes.AbsoluteOff,
            new Dictionary<string, object?>());
    }
}