﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;
using Entity = Sleekflow.Persistence.Entity;

namespace Sleekflow.Integrator.Hubspot.HubspotQueues;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId(ContainerNames.CrmHubIntegrationDatabaseId)]
[ContainerId(ContainerNames.HubspotQueueItem)]
public class HubspotQueueItem : Entity, IHasCreatedAt, IHasUpdatedAt, IHasSleekflowCompanyId
{
    public const string PropertyNameEntityTypeName = "entity_type_name";
    public const string PropertyNameCrmHubObjectId = "crm_hub_object_id";
    public const string PropertyNameHubspotObjectId = "hubspot_object_id";
    public const string PropertyNameDict = "dict";
    public const string PropertyNameObjectOperation = "object_operation";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameEntityTypeName)]
    public string EntityTypeName { get; set; }

    [JsonProperty(PropertyNameCrmHubObjectId)]
    public string CrmHubObjectId { get; set; }

    [JsonProperty(PropertyNameHubspotObjectId)]
    public string? HubspotObjectId { get; set; }

    [JsonProperty(PropertyNameDict)]
    public Dictionary<string, object?> Dict { get; set; }

    [JsonProperty(PropertyNameObjectOperation)]
    public string ObjectOperation { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    public HubspotQueueItem(
        string id,
        int? ttl,
        string sleekflowCompanyId,
        string entityTypeName,
        string crmHubObjectId,
        string? hubspotObjectId,
        Dictionary<string, object?> dict,
        string objectOperation,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, SysTypeNames.HubspotQueue, ttl)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EntityTypeName = entityTypeName;
        CrmHubObjectId = crmHubObjectId;
        HubspotObjectId = hubspotObjectId;
        Dict = dict;
        ObjectOperation = objectOperation;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}