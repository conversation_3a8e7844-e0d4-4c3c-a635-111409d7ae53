using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Locks;

namespace Sleekflow.CommerceHub.Carts;

public interface ICartService
{
    Task<Cart?> GetOrDefaultStoreUserCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId);

    Task<Cart> GetOrCreateStoreUserCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId);

    Task<List<Cart>> GetUserCartsAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string cartStatus);

    Task<Cart> PatchAndGetCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        List<CartLineItem> lineItems,
        Discount? cartDiscount);

    Task<Cart> PatchAndGetCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        string productVariantId,
        string productId,
        int quantity);

    Task<Cart?> ClearAndGetCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId);
}

public class CartService : ICartService, IScopedService
{
    private readonly ICartRepository _cartRepository;
    private readonly IIdService _idService;
    private readonly ILockService _lockService;

    public CartService(
        ICartRepository cartRepository,
        IIdService idService,
        ILockService lockService)
    {
        _cartRepository = cartRepository;
        _idService = idService;
        _lockService = lockService;
    }

    public async Task<Cart?> GetOrDefaultStoreUserCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId)
    {
        var carts = await _cartRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.StoreId == storeId
                && c.SleekflowUserProfileId == sleekflowUserProfileId
                && c.CartStatus == CartStatuses.Active);

        if (carts.Count > 0)
        {
            return carts[0];
        }

        return null;
    }

    public async Task<Cart> GetOrCreateStoreUserCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            GetCartLockKey(sleekflowCompanyId, storeId, sleekflowUserProfileId),
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            return await GetOrCreateStoreUserCartUnsafeAsync(sleekflowCompanyId, storeId, sleekflowUserProfileId);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    private async Task<Cart> GetOrCreateStoreUserCartUnsafeAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId)
    {
        // TODO Invalidate Line Items
        var carts = await _cartRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.StoreId == storeId
                && c.SleekflowUserProfileId == sleekflowUserProfileId
                && c.CartStatus == CartStatuses.Active);
        if (carts.Count > 0)
        {
            return carts[0];
        }

        var cart = await _cartRepository.CreateAndGetAsync(
            new Cart(
                storeId,
                sleekflowUserProfileId,
                new List<CartLineItem>(),
                null,
                CartStatuses.Active,
                null,
                null,
                new List<string>
                {
                    RecordStatuses.Active
                },
                null,
                _idService.GetId("Cart"),
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowCompanyId,
                null,
                null),
            sleekflowUserProfileId);

        return cart;
    }

    private static string[] GetCartLockKey(string sleekflowCompanyId, string storeId, string sleekflowUserProfileId)
    {
        var lockKey = new[]
        {
            nameof(CartService),
            "Cart",
            sleekflowCompanyId,
            storeId,
            sleekflowUserProfileId
        };
        return lockKey;
    }

    public async Task<List<Cart>> GetUserCartsAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string cartStatus)
    {
        var carts = await _cartRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.SleekflowUserProfileId == sleekflowUserProfileId
                && c.CartStatus == cartStatus);

        return carts;
    }

    public async Task<Cart> PatchAndGetCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        List<CartLineItem> lineItems,
        Discount? cartDiscount)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            GetCartLockKey(sleekflowCompanyId, storeId, sleekflowUserProfileId),
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var storeUserCart = await GetOrCreateStoreUserCartUnsafeAsync(
                sleekflowCompanyId,
                storeId,
                sleekflowUserProfileId);

            if (storeUserCart.CartStatus != CartStatuses.Active)
            {
                throw new SfUserFriendlyException("The Cart is not active");
            }

            // TODO Invalidate Line Items
            storeUserCart.LineItems = lineItems;
            storeUserCart.CartDiscount = cartDiscount;

            var updatedCart = await _cartRepository.ReplaceAndGetAsync(
                storeUserCart.Id,
                storeUserCart.SleekflowUserProfileId,
                storeUserCart,
                eTag: storeUserCart.ETag);

            return updatedCart;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<Cart> PatchAndGetCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId,
        string productVariantId,
        string productId,
        int quantity)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            GetCartLockKey(sleekflowCompanyId, storeId, sleekflowUserProfileId),
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var storeUserCart = await GetOrCreateStoreUserCartUnsafeAsync(
                sleekflowCompanyId,
                storeId,
                sleekflowUserProfileId);

            var lineItem = storeUserCart.LineItems.Find(
                li =>
                    li.ProductId == productId
                    && li.ProductVariantId == productVariantId);

            // TODO Invalidate Line Items
            if (lineItem != null)
            {
                if (quantity == 0)
                {
                    storeUserCart.LineItems.Remove(lineItem);
                }
                else
                {
                    lineItem.Quantity = quantity;
                }
            }
            else
            {
                storeUserCart.LineItems.Add(
                    new CartLineItem(
                        productVariantId,
                        productId,
                        null,
                        quantity,
                        null,
                        new Dictionary<string, object?>()));
            }

            var updatedCart = await _cartRepository.ReplaceAndGetAsync(
                storeUserCart.Id,
                storeUserCart.SleekflowUserProfileId,
                storeUserCart,
                eTag: storeUserCart.ETag);

            return updatedCart;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<Cart?> ClearAndGetCartAsync(
        string sleekflowCompanyId,
        string storeId,
        string sleekflowUserProfileId)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            GetCartLockKey(sleekflowCompanyId, storeId, sleekflowUserProfileId),
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        var prevCart = await GetOrDefaultStoreUserCartAsync(
            sleekflowCompanyId,
            storeId,
            sleekflowUserProfileId);
        if (prevCart != null)
        {
            prevCart.CartStatus = CartStatuses.Cleared;

            await _cartRepository.ReplaceAsync(
                prevCart.Id,
                sleekflowUserProfileId,
                prevCart,
                eTag: prevCart.ETag);

            await _lockService.ReleaseAsync(@lock);

            return prevCart;
        }

        await _lockService.ReleaseAsync(@lock);

        return null;
    }
}