### Get Basic Object Information

GET {{instance_url}}/services/data/v54.0/sobjects/Order/ HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Get a List of Fields - Order

GET {{instance_url}}/services/data/v54.0/sobjects/Order/describe HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Execute a SOQL Query

GET {{instance_url}}/services/data/v54.0/query?q=SELECT+FIELDS(STANDARD)+from+Order+LIMIT+10 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Execute a SOQL Query

GET {{instance_url}}/services/data/v54.0/query?q=SELECT+FIELDS(STANDARD)+from+OrderItem+LIMIT+10 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Update a Field on a Record

PATCH {{instance_url}}/services/data/v54.0/sobjects/Order/0035i000005PauxAAC HTTP/1.1
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "Phone": "+852 610966233"
}

### Get an Object

GET {{instance_url}}/services/data/v54.0/sobjects/Order/0035i000005PauxAAC HTTP/1.1
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "Phone": "+852 61096623"
}

### Get Updated Objects

GET {{instance_url}}/services/data/v54.0/sobjects/Order/updated/?start=2022-05-05T00%3A00%3A00%2B00%3A00&end=2022-05-06T00%3A00%3A00%2B00%3A00 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Get Objects

GET {{instance_url}}/services/data/v30.0/commerce/sale/order/8015i000000JwTzAAK HTTP/1.1
Authorization: Bearer {{access_token}}
Content-Type: application/json
X-PrettyPrint: 1

### Place Order

POST {{instance_url}}/services/data/v30.0/commerce/sale/order HTTP/1.1
Authorization: Bearer {{access_token}}
Content-Type: application/json
X-PrettyPrint: 1

{
  "order": [
    {
      "attributes": {
        "type": "Order"
      },
      "EffectiveDate": "2022-07-11",
      "Status": "Draft",
      "billingCity": "SFO-Inside-OrderEntity-1",
      "accountId": "0015i000004mcaLAAQ",
      "Pricebook2Id": "01s5i000003BIPgAAO",
      "OrderItems": {
        "records": [
          {
            "attributes": {
              "type": "OrderItem"
            },
            "PricebookEntryId": "01sD0000000G2NjIAK",
            "quantity": "1",
            "UnitPrice": "135000.0"
          }
        ]
      }
    }
  ]
}