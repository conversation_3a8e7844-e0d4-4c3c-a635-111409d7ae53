using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetInstagramPageAccessTokenOutput
{
    [JsonProperty("instagram_page_access_token")]
    public string InstagramPageAccessToken { get; set; }

    [JsonConstructor]
    public GetInstagramPageAccessTokenOutput(string instagramPageAccessToken)
    {
        InstagramPageAccessToken = instagramPageAccessToken;
    }
}