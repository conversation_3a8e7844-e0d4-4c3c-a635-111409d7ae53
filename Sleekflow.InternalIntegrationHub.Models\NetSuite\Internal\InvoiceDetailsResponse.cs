using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

public class InvoiceDetailsResponse
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("amountPaid")]
    public double AmountPaid { get; set; }

    [JsonProperty("amountRemaining")]
    public double AmountRemaining { get; set; }

    [JsonProperty("currency")]
    public Currency Currency { get; set; }

    [JsonProperty("total")]
    public double Total { get; set; }

    [JsonConstructor]
    public InvoiceDetailsResponse(
        string id,
        double amountPaid,
        double amountRemaining,
        Currency currency,
        double total)
    {
        Id = id;
        AmountPaid = amountPaid;
        AmountRemaining = amountRemaining;
        Currency = currency;
        Total = total;
    }
}