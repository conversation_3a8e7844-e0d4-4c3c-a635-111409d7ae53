﻿using Microsoft.Azure.Cosmos;
using Polly.Retry;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.HubspotQueues;

public interface IHubspotQueueItemRepository : IRepository<HubspotQueueItem>
{
    Task<List<(string SleekflowCompanyId, string EntityTypeName)>> GetDistinctCompanyIdAndEntityTypeNameTuplesAsync();
}

public class HubspotQueueItemRepository
    : BaseRepository<HubspotQueueItem>, IHubspotQueueItemRepository, ISingletonService
{
    private readonly ILogger<BaseRepository<HubspotQueueItem>> _logger;
    private readonly AsyncRetryPolicy _retryPolicy;

    public HubspotQueueItemRepository(
        ILogger<BaseRepository<HubspotQueueItem>> logger,
        IServiceProvider serviceProvider,
        IPersistenceRetryPolicyService persistenceRetryPolicyService)
        : base(logger, serviceProvider)
    {
        _logger = logger;
        _retryPolicy = persistenceRetryPolicyService.GetAsyncRetryPolicy();
    }

    public async Task<List<(string SleekflowCompanyId, string EntityTypeName)>>
        GetDistinctCompanyIdAndEntityTypeNameTuplesAsync()
    {
        var queryDefinition = new QueryDefinition(
            "SELECT c.sleekflow_company_id, c.entity_type_name " +
            "FROM %%CONTAINER_NAME%% c " +
            "GROUP BY c.sleekflow_company_id, c.entity_type_name");

        var container = GetContainer();
        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        var policyResult = await _retryPolicy.ExecuteAndCaptureAsync(
            async (_) =>
            {
                using var itemQueryIterator = container.GetItemQueryIterator<Dictionary<string, object?>>(
                    qd,
                    requestOptions: new QueryRequestOptions
                    {
                        MaxItemCount = 1000, MaxBufferedItemCount = 1000, MaxConcurrency = 4,
                    });

                var count = 0;
                var objs = new List<Dictionary<string, object?>>();

                while (itemQueryIterator.HasMoreResults && count < 1000)
                {
                    var response = await itemQueryIterator.ReadNextAsync();
                    objs.AddRange(response);

                    if (_logger.IsEnabled(LogLevel.Debug))
                    {
                        _logger.LogDebug(
                            "GetAsync {QueryText} {RequestCharge}",
                            qd.QueryText,
                            response.RequestCharge);
                    }

                    count += response.Count;
                }

                return objs;
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException == null)
        {
            var tuples = policyResult.Result
                .Select(d => (d["sleekflow_company_id"]!.ToString()!, d["entity_type_name"]!.ToString()!))
                .ToList();

            return tuples;
        }

        throw new SfQueryException(
            policyResult.FinalException,
            nameof(GetDistinctCompanyIdAndEntityTypeNameTuplesAsync));
    }
}