using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Carts.ShopifyCarts;

public interface IShopifyCartService
{
    Task<Cart?> GetShopifyCartAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string storeId,
        string cartStatus,
        string shopifyCartToken);

    Task<Cart> UpdateAndGetShopifyCartAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string storeId,
        string originalCartStatus,
        string shopifyCartToken,
        string targetCartStatus,
        Discount? cartDiscount,
        string checkoutUrl,
        string conversionStatus,
        decimal totalPrice,
        decimal totalTax,
        decimal subtotalPrice,
        string? currencyIsoCode,
        string? shopifyCustomerId,
        DateTimeOffset? abandonedDate,
        DateTimeOffset? recoveredDate,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<ShopifyCartStatistics> GetShopifyCartStatisticsAsync(
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string? sleekflowStaffTeamId,
        string? conversionStatus,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime);
}

public class ShopifyCartService : IShopifyCartService, IScopedService
{
    private readonly ICartRepository _cartRepository;

    public ShopifyCartService(ICartRepository cartRepository)
    {
        _cartRepository = cartRepository;
    }

    public async Task<Cart?> GetShopifyCartAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string storeId,
        string cartStatus,
        string shopifyCartToken)
    {
        var carts = await _cartRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.SleekflowUserProfileId == sleekflowUserProfileId
                && c.StoreId == storeId
                && c.CartStatus == cartStatus
                && c.CartExternalIntegrationInfo != null
                && c.CartExternalIntegrationInfo.ProviderName == "shopify");

        foreach (var cart in carts)
        {
            if (cart.CartExternalIntegrationInfo is ShopifyCartExternalIntegrationInfo shopifyInfo
                && shopifyInfo.CartToken == shopifyCartToken)
            {
                return cart;
            }
        }

        return null;
    }

    public async Task<Cart> UpdateAndGetShopifyCartAsync(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        string storeId,
        string originalCartStatus,
        string shopifyCartToken,
        string targetCartStatus,
        Discount? cartDiscount,
        string checkoutUrl,
        string conversionStatus,
        decimal totalPrice,
        decimal totalTax,
        decimal subtotalPrice,
        string? currencyIsoCode,
        string? shopifyCustomerId,
        DateTimeOffset? abandonedDate,
        DateTimeOffset? recoveredDate,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var shopifyCart = await GetShopifyCartAsync(
            sleekflowCompanyId,
            sleekflowUserProfileId,
            storeId,
            originalCartStatus,
            shopifyCartToken);

        if (shopifyCart is null)
        {
            throw new SfNotFoundObjectException("Shopify cart not found");
        }

        var shopifyCartExternalIntegrationInfo = (shopifyCart.CartExternalIntegrationInfo as ShopifyCartExternalIntegrationInfo)!;
        shopifyCartExternalIntegrationInfo.CheckoutUrl = checkoutUrl;
        shopifyCartExternalIntegrationInfo.ConversionStatus = conversionStatus;
        shopifyCartExternalIntegrationInfo.TotalPrice = totalPrice;
        shopifyCartExternalIntegrationInfo.TotalTax = totalTax;
        shopifyCartExternalIntegrationInfo.SubtotalPrice = subtotalPrice;
        shopifyCartExternalIntegrationInfo.ShopifyCustomerId = shopifyCustomerId;
        shopifyCartExternalIntegrationInfo.AbandonedDate = abandonedDate;
        shopifyCartExternalIntegrationInfo.RecoveredDate = recoveredDate;

        return await _cartRepository.PatchAndGetAsync(
            shopifyCart.Id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/currency_iso_code", currencyIsoCode),
                PatchOperation.Replace("/cart_status", targetCartStatus),
                PatchOperation.Set("/cart_discount", cartDiscount),
                PatchOperation.Set("/cart_external_integration_info", shopifyCartExternalIntegrationInfo),
                PatchOperation.Replace("/updated_by", sleekflowStaff)
            });
    }

    public async Task<ShopifyCartStatistics> GetShopifyCartStatisticsAsync(
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string? sleekflowStaffTeamId,
        string? conversionStatus,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime)
    {
        var shopifyCarts = await _cartRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.CreatedAt >= fromDateTime
                && c.CreatedAt <= toDateTime
                && c.CartExternalIntegrationInfo != null
                && c.CartExternalIntegrationInfo.ProviderName == "shopify");

        if (sleekflowStaffId is not null)
        {
            shopifyCarts = shopifyCarts
                .Where(c => c.CreatedBy?.SleekflowStaffId == sleekflowStaffId)
                .ToList();
        }

        if (sleekflowStaffTeamId is not null)
        {
            shopifyCarts = shopifyCarts
                .Where(c => c.CreatedBy?.SleekflowStaffTeamIds?.Contains(sleekflowStaffTeamId) == true)
                .ToList();
        }

        var shopifyCartStatistics = new ShopifyCartStatistics(0, 0);
        foreach (var shopifyCart in shopifyCarts)
        {
            if (shopifyCart.CartExternalIntegrationInfo is ShopifyCartExternalIntegrationInfo shopifyInfo
                && (conversionStatus is null
                    || shopifyInfo.ConversionStatus == conversionStatus))
            {
                shopifyCartStatistics.Count++;
                shopifyCartStatistics.TotalPrice += shopifyInfo.TotalPrice;
            }
        }

        return shopifyCartStatistics;
    }
}