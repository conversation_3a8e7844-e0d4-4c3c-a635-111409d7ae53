using Azure.Messaging.ServiceBus;
using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Workers.Consumers;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class StartUploadToAgentKnowledgeBasesTrigger
{
    private const string QueueName = "start-upload-to-agent-knowledge-bases-event";
    private readonly ILogger<StartUploadToAgentKnowledgeBasesTrigger> _logger;
    private readonly IMessageReceiver _messagereceiver;

    public StartUploadToAgentKnowledgeBasesTrigger(
        ILogger<StartUploadToAgentKnowledgeBasesTrigger> logger,
        IMessageReceiver messagereceiver)
    {
        _logger = logger;
        _messagereceiver = messagereceiver;
    }

    [Function(nameof(StartUploadToAgentKnowledgeBasesTrigger))]
    public async Task RunAsync(
        [ServiceBusTrigger(QueueName, Connection = "SERVICE_BUS_CONN_STR")]
        ServiceBusReceivedMessage message,
        [DurableClient]
        DurableTaskClient starter,
        CancellationToken cancellationToken)
    {
        await _messagereceiver.HandleConsumer<StartUploadToAgentKnowledgeBasesEventConsumer>(
            QueueName,
            message,
            cancellationToken);

        try
        {
            _logger.LogInformation(
                "StartUploadToAgentKnowledgeBasesInput message: {Message}",
                message.Body.ToString());

            // Parse the full message to extract the nested "message" property
            var fullMessage = JObject.Parse(message.Body.ToString());
            var messageContent = fullMessage["message"]?.ToString();

            _logger.LogInformation(
                "StartUploadToAgentKnowledgeBasesInput message content: {Message}",
                messageContent);

            if (string.IsNullOrEmpty(messageContent))
            {
                _logger.LogError("Message content is missing or empty in the received message");
                return;
            }

            var input = JsonConvert.DeserializeObject<StartUploadToAgentKnowledgeBasesEvent>(messageContent);
            if (input == null)
            {
                _logger.LogError(
                    "Failed to deserialize StartUploadToAgentKnowledgeBasesInput");
                return;
            }

            var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                nameof(UploadFileDocumentToAllAssignedAgentsOrchestrator),
                input);

            _logger.LogInformation(
                $"Started UploadFileDocumentToAllAssignedAgentsOrchestrator with ID = [{instanceId}]");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing Service Bus message");
            throw;
        }
    }
}