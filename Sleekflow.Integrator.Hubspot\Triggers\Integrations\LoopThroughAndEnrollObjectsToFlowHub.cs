﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Workers;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class LoopThroughAndEnrollObjectsToFlowHub : ITrigger
{
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IWorkerService _workerService;
    private readonly IWorkerConfig _workerConfig;
    private readonly HttpClient _httpClient;

    public LoopThroughAndEnrollObjectsToFlowHub(
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotConnectionService hubspotConnectionService,
        IWorkerService workerService,
        IWorkerConfig workerConfig,
        IHttpClientFactory httpClientFactory)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotConnectionService = hubspotConnectionService;
        _workerService = workerService;
        _workerConfig = workerConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public class LoopThroughAndEnrollObjectsToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            bool isCustomObject,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            IsCustomObject = isCustomObject;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class LoopThroughAndEnrollObjectsToFlowHubOutput : DurablePayload
    {
        [JsonConstructor]
        public LoopThroughAndEnrollObjectsToFlowHubOutput(
            string id,
            string statusQueryGetUri,
            string sendEventPostUri,
            string terminatePostUri,
            string rewindPostUri,
            string purgeHistoryDeleteUri,
            string restartPostUri)
            : base(
                id,
                statusQueryGetUri,
                sendEventPostUri,
                terminatePostUri,
                rewindPostUri,
                purgeHistoryDeleteUri,
                restartPostUri)
        {
        }
    }

    public async Task<LoopThroughAndEnrollObjectsToFlowHubOutput> F(LoopThroughAndEnrollObjectsToFlowHubInput loopThroughAndEnrollObjectsToFlowHubInput)
    {
        var connection = await _hubspotConnectionService.GetByIdAsync(
            loopThroughAndEnrollObjectsToFlowHubInput.ConnectionId,
            loopThroughAndEnrollObjectsToFlowHubInput.SleekflowCompanyId);

        var authentication =
            await _hubspotAuthenticationService.GetAsync(
                connection.AuthenticationId,
                loopThroughAndEnrollObjectsToFlowHubInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (_, _, output) = await _workerService.PostAsync<LoopThroughAndEnrollObjectsToFlowHubOutput>(
            _httpClient,
            JsonConvert.SerializeObject(loopThroughAndEnrollObjectsToFlowHubInput),
            _workerConfig.WorkerHostname + "/api/Hubspot_LoopThroughAndEnrollObjectsToFlowHub");

        return output.Data!;
    }
}