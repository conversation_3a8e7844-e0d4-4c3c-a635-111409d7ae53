using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Moq;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.Ingestion;

[TestFixture]
[TestOf(typeof(CsvKnowledgeSourceTest))]
public class CsvKnowledgeSourceTest
{
    // Relative path from the test execution directory to the Binaries folder
    const string CsvFilePath = "../../../Binaries/business-operations-survey-2022-business-finance.csv";

    [Test]
    public async Task CsvKnowledgeSourceIngestTest()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test takes too long in git action");
        }

        using var scope = Application.Host.Services.CreateScope();
        var kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        var logger = new Mock<ILogger<CsvKnowledgeSource>>().Object;
        var csvKnowledgeSource = new CsvKnowledgeSource(logger, kernel);

        var allMarkdowns = new List<string>();
        IFileIngestionProgress? progress = null;

        do
        {
            await using var fileStream = new FileStream(
                CsvFilePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read);

            var (markdowns, updatedProgress) = await csvKnowledgeSource.Ingest(fileStream, progress);

            // Add new markdowns to our collection
            allMarkdowns.AddRange(markdowns);

            // Update progress for next iteration
            progress = updatedProgress;

            // Continue until all chunks are processed
        }
        while (!progress.IsCompleted());

        Assert.That(allMarkdowns, Is.Not.Empty);
        Assert.That(progress.IsCompleted(), Is.True);
        Assert.That(progress.GetProgressPercentage(), Is.EqualTo(100.0));
    }
}