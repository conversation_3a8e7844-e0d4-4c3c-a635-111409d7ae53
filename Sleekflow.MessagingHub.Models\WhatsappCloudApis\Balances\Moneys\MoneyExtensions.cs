namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;

public static class MoneyExtensions
{
    // Normal operators
    public static Money Add(Money left, Money right)
    {
        if (left.CurrencyIsoCode != right.CurrencyIsoCode)
        {
            throw new NotImplementedException();
        }

        return new Money(left.CurrencyIsoCode, left.Amount + right.Amount);
    }

    public static Money Minus(Money left, Money right)
    {
        if (left.CurrencyIsoCode != right.CurrencyIsoCode)
        {
            throw new NotImplementedException();
        }

        return new Money(left.CurrencyIsoCode, left.Amount - right.Amount);
    }

    public static Money Multiply(Money left, Money right)
    {
        if (left.CurrencyIsoCode != right.CurrencyIsoCode)
        {
            throw new NotImplementedException();
        }

        return new Money(left.CurrencyIsoCode, left.Amount * right.Amount);
    }

    // Amount
    public static Money MultiplyByAmount(Money left, int rightAmount)
    {
        return new Money(left.CurrencyIsoCode, left.Amount * rightAmount);
    }

    public static Money MultiplyByAmount(int leftAmount, Money right)
    {
        return new Money(right.CurrencyIsoCode, leftAmount * right.Amount);
    }

    public static Money MultiplyByAmount(Money left, decimal rightAmount)
    {
        return new Money(left.CurrencyIsoCode, left.Amount * rightAmount);
    }

    public static Money MultiplyByPercentage(Money left, decimal percentage)
    {
        return new Money(left.CurrencyIsoCode, left.Amount * percentage);
    }

    public static Money MultiplyByPercentage(decimal percentage, Money right)
    {
        return new Money(right.CurrencyIsoCode, percentage * right.Amount);
    }

    public static Money DivideByAmount(Money left, decimal rightAmount)
    {
        return new Money(left.CurrencyIsoCode, left.Amount / rightAmount);
    }

    public static Money AddAll(params Money?[] moneys)
    {
        var filteredMoney = moneys.Where(m => m is not null).ToList();
        var currencyIsoCodes = filteredMoney.Select(m => m.CurrencyIsoCode).Distinct().ToList();
        if (moneys.Length == 0 || currencyIsoCodes.Count == 0 || currencyIsoCodes.Count > 1)
        {
            throw new Exception();
        }

        if (filteredMoney.Count == 1)
        {
            return filteredMoney[0]!;
        }

        var result = new Money(currencyIsoCodes[0], 0);
        return filteredMoney.Aggregate(result, Add!);
    }
}