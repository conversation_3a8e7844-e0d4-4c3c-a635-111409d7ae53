using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;

public class UpdateWorkflowDurablePayloadOutput
{
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("update_success")]
    public bool UpdateSuccess { get; set; }

    [JsonConstructor]
    public UpdateWorkflowDurablePayloadOutput(
        string workflowVersionedId,
        bool updateSuccess)
    {
        WorkflowVersionedId = workflowVersionedId;
        UpdateSuccess = updateSuccess;
    }
}