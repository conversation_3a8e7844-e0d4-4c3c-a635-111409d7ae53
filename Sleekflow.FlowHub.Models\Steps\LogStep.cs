using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps;

public class LogStep : Step
{
    [Required]
    [JsonProperty("log_message")]
    public string LogMessage { get; set; }

    [Required]
    [JsonProperty("log_level")]
    public string LogLevel { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => string.Empty;

    [JsonConstructor]
    public LogStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId,
        string logMessage,
        string logLevel)
        : base(id, name, assign, nextStepId)
    {
        LogMessage = logMessage;
        LogLevel = logLevel;
    }
}