using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins;

public interface IPromptPlugin
{
    Task<string> CustomPromptRewriteText(Kernel kernel, string inputText, string inputPrompt);
}

public class PromptPlugin
    : IPromptPlugin, IScopedService
{
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public PromptPlugin(
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    [KernelFunction("CustomPromptRewriteText")]
    [Description("Rewrite sentences with the provided custom prompt")]
    [return: Description("The rewritten text")]
    public async Task<string> CustomPromptRewriteText(Kernel kernel, string inputText, string inputPrompt)
    {
        var promptExecutionSettings = _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);

        var customPromptRewriteTextFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "CustomPromptRewrite",
                Description =
                    "Rewrite sentences with the provided custom prompt",
                Template =
                    """
                    <message role="system">
                    Please rewrite the input text with the below prompt instruction.

                    You must:
                    - Follow the prompt instruction
                    - Use your own words
                    - Not change the meaning of the input text
                    - If the instruction is not clear, please ask for clarification
                    - If the instruction includes translation, please translate to the requested language and only include the translated text
                    - If the instruction includes rephrasing, please rephrase the text as instructed
                    - If the instruction includes a word count, please make sure the output does not exceed the maximum word count
                    - If the instruction includes a tone, please adjust the text to match the requested tone
                    - If the instruction includes a target audience, please adjust the text to match the requested audience
                    - If the instruction includes keywords, please include the keywords in the output
                    - If the instruction includes a source, please use the provided source
                    - If the instruction includes a special format, please make sure the output matches the format
                    - If the instruction includes a writing style, please adjust the text to match the requested writing style
                    </message>

                    <message role="user">
                    Prompt:
                    Please translate the text to English and make sure the tone is polite.

                    Original Text:
                    你必须马上离开这里。
                    </message>
                    <message role="assistant">
                    You must leave here immediately.
                    </message>

                    <message role="user">
                    Prompt:
                    {{$PROMPT}}

                    Original Text:
                    {{$TEXT}}
                    </message>
                    """,
                InputVariables = new List<InputVariable>
                {
                    new InputVariable
                    {
                        Name = "PROMPT"
                    },
                    new InputVariable
                    {
                        Name = "TEXT"
                    }
                },
                OutputVariable = new OutputVariable
                {
                    Description = "The rewritten text",
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                }
            });

        var chatMessageContent = await customPromptRewriteTextFunction.InvokeAsync<ChatMessageContent>(
            kernel,
            new KernelArguments(promptExecutionSettings)
            {
                {
                    "PROMPT", inputPrompt
                },
                {
                    "TEXT", inputText
                }
            });

        return chatMessageContent?.Content ?? "Custom prompt rewriting failed";
    }
}