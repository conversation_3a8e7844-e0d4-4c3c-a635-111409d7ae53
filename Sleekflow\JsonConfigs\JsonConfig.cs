﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Sleekflow.JsonConfigs;

public static class JsonConfig
{
    public static readonly JsonSerializerSettings DefaultJsonSerializerSettings = new JsonSerializerSettings()
    {
        Converters = new List<JsonConverter>()
        {
            new IsoDateTimeConverter
            {
                DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
            }
        },
        ReferenceLoopHandling = ReferenceLoopHandling.Error,
        DateParseHandling = DateParseHandling.DateTimeOffset,
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        MaxDepth = 32,
    };

    public static JsonSerializerSettings EnhanceWithDefaultJsonSerializerSettings(
        JsonSerializerSettings settings)
    {
        settings.ReferenceLoopHandling = ReferenceLoopHandling.Error;
        settings.Converters.Add(
            new IsoDateTimeConverter
            {
                DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
            });
        settings.DateParseHandling = DateParseHandling.DateTimeOffset;
        settings.DateTimeZoneHandling = DateTimeZoneHandling.Utc;
        settings.MaxDepth = 32;

        return settings;
    }

    public static JsonSerializerSettings EnhanceWithDefaultJsonSerializerSettings(
        Func<JsonSerializerSettings, JsonSerializerSettings> func)
    {
        var settings = new JsonSerializerSettings()
        {
            Converters = new List<JsonConverter>()
            {
                new IsoDateTimeConverter
                {
                    DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
                }
            },
            ReferenceLoopHandling = ReferenceLoopHandling.Error,
            DateParseHandling = DateParseHandling.DateTimeOffset,
            DateTimeZoneHandling = DateTimeZoneHandling.Utc,
            MaxDepth = 32,
        };

        return func.Invoke(settings);
    }

    public static readonly JsonSerializerSettings DefaultLoggingJsonSerializerSettings = new JsonSerializerSettings()
    {
        Converters = new List<JsonConverter>()
        {
            new IsoDateTimeConverter
            {
                DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
            }
        },
        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
        DateParseHandling = DateParseHandling.DateTimeOffset,
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        MaxDepth = 32,
    };
}