using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class InformationGatheringPluginTest
{
    private Kernel _kernel;
    private IInformationGatheringPlugin _informationGatheringPlugin;

    [SetUp]
    public void SetUp()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _informationGatheringPlugin = scope.ServiceProvider.GetRequiredService<IInformationGatheringPlugin>();
    }

    [Test]
    public async Task SummarizeConversationForHandoverAsync_WithNullContactProperties_ShouldUseDefaults()
    {
        // Act
        var extractFieldsOutput = await _informationGatheringPlugin.ExtractFieldsAsync(
            _kernel,
            """
            ====BACKGROUND====
            You are representing SleekFlow and you should name yourself `<PERSON>leek<PERSON>low AI` when you are asked. SleekFlow is the AI-powered Omnichannel Conversation Suite for customer engagement. The all-in-one SleekFlow platform creates seamless and personalized customer journeys across everyone’s go-to messaging channels, including WhatsApp, Instagram, live chat, and more. SleekFlow is dedicated to shaping the future of communication by empowering companies to center all their workflows around meaningful conversations.All the price are in HKD based
            ====BACKGROUND====

            ====REQUESTED TONE====
            Respond with a professional tone, keeping things clear and structured while staying approachable. Use formal language but ensure it's polite and easy to follow. Avoid overly casual expressions or emojis, but still encourage the conversation to continue with a helpful attitude.
            ====REQUESTED TONE====

            ====RESPONSE LANGUAGE====
            Traditional Chinese - Hong Kong
            ====RESPONSE LANGUAGE====

            ====CURRENT TIME====
            2025-05-08 08:33:45 UTC
            ====CURRENT TIME====

            ====CONTACT_PROPERTIES_SECTION====
            {"firstName":"Leo","lastName":"Choi","PhoneNumber":"+852 6109 6623","Email":"<EMAIL>"}
            ====CONTACT_PROPERTIES_SECTION====

            ====HUBSPOT_SECTION====
            # Contact Properties
            {
              "id": "116264368735",
              "properties": {
                "company": "SLEEKFLOW ENGINEERING Association",
                "createdate": "2025-04-23T02:18:00.200Z",
                "email": "<EMAIL>",
                "firstname": "Leo",
                "hs_object_id": "116264368735",
                "hubspot_owner_id": "*********",
                "lastmodifieddate": "2025-04-24T07:26:01.215Z",
                "lastname": "Choi",
                "lifecyclestage": "lead",
                "phone": "******-456-7890",
                "website": null
              },
              "createdAt": "2025-04-23T02:18:00.200Z",
              "updatedAt": "2025-04-24T07:26:01.215Z",
              "archived": false
            }
            # Scheduled Meetings
            [
              {
                "id": "77651448504",
                "properties": {
                  "hs_createdate": "2025-04-23T02:19:45Z",
                  "hs_internal_meeting_notes": null,
                  "hs_lastmodifieddate": "2025-04-24T08:01:37.509Z",
                  "hs_meeting_body": "If you need to reschedule please use the link below:\nhttps://sleekflow.chilipiper.com/reschedule/1a806e99-a83c-4881-b07c-eae805914538\n\nLocation not specified",
                  "hs_meeting_end_time": "2025-04-24T07:00:00Z",
                  "hs_meeting_start_time": "2025-04-24T06:30:00Z",
                  "hs_meeting_title": "Leo <> Josephine @ SleekFlow | Meeting",
                  "hs_object_id": "77651448504",
                  "hubspot_owner_id": "*********"
                },
                "createdAt": "2025-04-23T02:19:45Z",
                "updatedAt": "2025-04-24T08:01:37.509Z",
                "archived": false
              }
            ]

            ====HUBSPOT_SECTION====

            ====PLAN_TIER_SECTION====
            {"explanation":"The closest match of `Hong Kong SAR China` is `Hong Kong SAR`. `Hong Kong SAR,GCR & UAE,Y,,,,,HKD` is filtered.","country":"Hong Kong SAR","region":"GCR & UAE","tier":"1","local_currency":"HKD"}
            ====PLAN_TIER_SECTION====

            ====CONVERSATION CONTEXT====
            ======Past conversation summary======
            Background: Customer is initiating contact to learn more about SleekFlow. They have switched the conversation to Chinese.
            Important Points:
            - Customer wants to speak with a salesperson and schedule a demo.

            ======Past conversation summary======

            ======Latest 8 messages======
            Our Company: 你好！很高興你想約 demo 了解更多！😊

            為咗可以幫你安排最適合嘅 demo，我哋需要確認少少資料。可以麻煩你幫我睇吓以下資料啱唔啱，或者補充返嗎？🙏

            • 你嘅全名係咪 Henry Yeung？
            • 聯絡電郵係 <EMAIL>，電話係 +852 6482 5398 定 +852 6452 2442 呢？
            • 公司名係 Sleekflow？
            • 公司係咪喺 Hong Kong SAR？
            • 同埋想問吓你公司大約有幾多位同事呢？

            確認咗呢啲資料，我哋就可以更快幫你安排 demo 啦！多謝你嘅幫忙！✨
            --
            Customer: 啱哂
            --
            Our Company: 謝謝你確認！😊 仲差少少資料就可以幫你安排 demo 啦！

            想問吓你公司大約有幾多位同事呢？知道呢個可以幫我哋安排最適合你嘅 demo 內容！✨
            --
            Customer: 200個同事
            --
            Our Company: 好！收到晒你嘅資料啦！😊

            我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。

            你想我哋而家幫你安排demo嗎？😊
            --
            Customer: 係
            --
            Our Company: 好！收到晒你嘅資料啦！😊 我哋會用你提供嘅資料（Henry Yeung, <EMAIL>, +852 6482 5398, Sleekflow, 50-499位同事, Hong Kong SAR）幫你安排一個SleekFlow產品示範（demo）。 你想我哋而家幫你安排demo嗎？😊
            --
            Customer: 係呀

            ======Latest 8 messages======
            ====CONVERSATION CONTEXT====

            ====ADDITIONAL INSTRUCTION====
            No additional instructions.
            ====ADDITIONAL INSTRUCTION====

            BACKGROUND is a configuration specifying our company's background information.
            REQUESTED TONE is a configuration specifying the desired tone of the response.
            RESPONSE LANGUAGE is the language specifying the appropriate language for the final response.
            CURRENT TIME is the current UTC time when this conversation is happening. Reference this time when discussing time-sensitive topics, operating hours, or when the customer asks about current time-related information.
            CONTACT_PROPERTIES_SECTION is a section that contains information about the contact properties of the customer stored in our system.
            HUBSPOT_SECTION is a section that contains information about the contact's Hubspot data, including contact properties and scheduled meetings.
            PLAN_TIER_SECTION is a section that contains information about the plan tier. You should never disclose this information to the user. This plan tier is used to determine the plan tier of the user.
            CONVERSATION CONTEXT is the historical messages between our company and the customer. Please note that the conversation is very long, it is summarized.
            ADDITIONAL INSTRUCTION is a configuration specifying the additional instruction that each agent should understand and adjust your behaviour.
            """);

        // Assert
        Assert.That(extractFieldsOutput, Is.Not.Null);
    }
}