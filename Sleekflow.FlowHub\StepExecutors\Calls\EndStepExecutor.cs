﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IEndStepExecutor : IStepExecutor
{
}

public class EndStepExecutor
    : GeneralStepExecutor<CallStep<EndStepArgs>>,
      IEndStepExecutor,
      IScopedService
{
    private readonly IWorkflowRuntimeService _workflowRuntimeService;

    public EndStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider)
        : base(
            workflowStepLocator,
            workflowRuntimeService,
            serviceProvider)
    {
        _workflowRuntimeService = workflowRuntimeService;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        await onActivatedAsync(state, StepExecutionStatuses.Complete);
    }

    public override async Task<Step?> OnStepCompleteAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries)
    {
        await _workflowRuntimeService.CompleteWorkflowAsync(state);

        return null;
    }
}