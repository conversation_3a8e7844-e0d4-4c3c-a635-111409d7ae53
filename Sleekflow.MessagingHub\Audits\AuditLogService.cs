using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Models.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using static Sleekflow.MessagingHub.Triggers.AuditLogs.CreateWabaPhoneNumberStatusChangedAuditLog;

namespace Sleekflow.MessagingHub.Audits;

public interface IAuditLogService
{
    Task<(List<AuditLog> Objs, string? NextContinuationToken)> GetWhatsappCloudApiConnectionAuditLogsAsync(
        List<string> auditingOperations,
        string? sleekflowCompanyId,
        bool? hasCloudApiAuditsException,
        string? continuationToken,
        int limit);

    Task<TOutput> GetCloudApiAuditedResponse<TOutput>(
        Func<Task<TOutput>> action,
        BaseAuditingObject audit)
        where TOutput : GraphApiResponseBase;

    Task<AuditLog?> AuditWabaAsync(
        Waba? waba,
        string auditingPartitionId,
        string? sleekflowCompanyId,
        string operation,
        Dictionary<string, object?>? changes);

    Task<AuditLog?> AuditBusinessBalanceAsync(
        BusinessBalance? businessBalance,
        string auditingPartitionId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null);

    Task<AuditLog?> AuditBusinessBalanceTransactionLogAsync(
        BusinessBalanceTransactionLog businessBalanceTransactionLog,
        string auditingPartitionId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null);

    Task<AuditLog?> AuditCloudApiWebhookStatusUpdateEventAsync(
        string auditingPartitionId,
        string operation,
        AuditLogCloudApiWebhookValueObject auditLogCloudApiWebhookValueObject,
        string facebookWabaId,
        string? facebookBusinessId,
        string? facebookPhoneNumberId,
        string? facebookMessageTemplateId,
        int ttl);

    Task<List<AuditLog>> GetCloudApiWebhookStatusUpdateAuditLogsWithFiltersAsync(
        string facebookWabaId,
        WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters? filters,
        int offset,
        int limit);

    Task<AuditLog?> AuditBusinessWabaLevelCreditManagementSwitchBySystemAsync(
        BusinessBalance businessBalance,
        string auditingPartitionId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null);

    Task<AuditLog?> AuditBusinessWabaLevelCreditManagementSwitchByUserAsync(
        BusinessBalance businessBalance,
        string auditingPartitionId,
        string facebookBusinessId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null);

    Task<AuditLog?> AuditBusinessWabaLevelCreditTransferAsync(
        string auditingPartitionId,
        string facebookWabaId,
        string facebookBusinessId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null);

    Task<CreateWabaPhoneNumberStatusChangedAuditLogOutput> AuditWabaPhoneNumberStatusChangedAsync(CreateWabaPhoneNumberStatusChangedAuditLogInput input);
}

public class AuditLogService : IAuditLogService, ISingletonService
{
    private readonly IIdService _idService;
    private readonly ILogger<AuditLogService> _logger;
    private readonly IAuditLogRepository _auditLogRepository;

    public AuditLogService(
        IIdService idService,
        ILogger<AuditLogService> logger,
        IAuditLogRepository auditLogRepository)
    {
        _logger = logger;
        _idService = idService;
        _auditLogRepository = auditLogRepository;
    }

    // Get audit logs with continuation token
    public async Task<(List<AuditLog> Objs, string? NextContinuationToken)> GetWhatsappCloudApiConnectionAuditLogsAsync(
        List<string> auditingOperations,
        string? sleekflowCompanyId,
        bool? hasCloudApiAuditsException,
        string? continuationToken,
        int limit)
    {
        var queryDefinition = new QueryDefinition(
                $"""
                 SELECT *
                 FROM c
                 WHERE ARRAY_CONTAINS(@auditingOperations, c.auditing_operation)
                    {(sleekflowCompanyId != null ? "AND c.sleekflow_company_id = @sleekflow_company_id " : string.Empty)}
                    {(hasCloudApiAuditsException.HasValue ? $"AND c.cloud_api_audit.exception {(hasCloudApiAuditsException.Value ? "!=" : "=")} null " : string.Empty)}
                 ORDER BY c._ts DESC
                 """)
            .WithParameter("@auditingOperations", auditingOperations)
            .WithParameter("@sleekflow_company_id", sleekflowCompanyId);

        return await _auditLogRepository.GetContinuationTokenizedObjectsAsync(
            queryDefinition,

            continuationToken,
            limit);
    }

    // Auditing requests from cloud apis
    public async Task<TOutput> GetCloudApiAuditedResponse<TOutput>(
        Func<Task<TOutput>> action,
        BaseAuditingObject audit)
        where TOutput : GraphApiResponseBase
    {
        var auditing = await CreateAuditAsync(
            audit.AuditingPartitionId,
            audit.SleekflowCompanyId,
            audit.Operation,
            audit.Parameters);
        try
        {
            var res = await action.Invoke();
            return await GetAuditingAndResponse(res, auditing);
        }
        catch (Exception e)
        {
            _logger.LogError("Get cloud api audit response error {Exception}", JsonConvert.SerializeObject(e));
            var error = new AuditLog.AuditingError(e).ToDictionary();
            if (!await _auditLogRepository.PatchCloudApiAuditAsync(auditing, error: error))
            {
                _logger.LogError("Error occur during inserting auditing logs {Error}", error);
            }

            if (e is GraphApiClientException gx &&
                (audit.Operation == AuditingOperation.InitiatePhoneNumberWabaMigration ||
                 audit.Operation == AuditingOperation.RequestPhoneNumberVerificationCode ||
                 audit.Operation == AuditingOperation.VerifyPhoneNumberOwnership ||
                 audit.Operation == AuditingOperation.RegisterWabaPhoneNumber ||
                 audit.Operation == AuditingOperation.OnboardPartnersToMMLite))
            {
                throw gx;
            }

            throw new SfInternalErrorException($"Error occur during {audit.Operation}");
        }
    }

    // Auditing changes on waba management object
    public async Task<AuditLog?> AuditWabaAsync(
        Waba? waba,
        string auditingPartitionId,
        string? sleekflowCompanyId,
        string operation,
        Dictionary<string, object?>? changes)
    {
        try
        {
            return await _auditLogRepository.CreateAndGetAsync(
                new AuditLog(
                    _idService.GetId(SysTypeNames.AuditLog),
                    SysTypeNames.WabaAudit,
                    -1,
                    auditingPartitionId,
                    auditingPartitionId,
                    null,
                    null,
                    null,
                    sleekflowCompanyId,
                    null,
                    new AuditLog.WabaAudit(waba, changes, waba != null ? "patch" : "create"),
                    null,
                    null,
                    null,
                    operation,
                    AuditingGroup.WabaManagement,
                    DateTimeOffset.UtcNow),
                auditingPartitionId);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Messaging hub - Error occur during auditing changes from {SleekflowCompanyId} to waba {Waba} / {Changes} | error {Error}",
                sleekflowCompanyId,
                JsonConvert.SerializeObject(waba),
                changes,
                JsonConvert.SerializeObject(e));
            return null;
        }
    }

    public async Task<AuditLog?> AuditBusinessBalanceAsync(
        BusinessBalance? businessBalance,
        string auditingPartitionId,
        string operation,
        Dictionary<string, object?>? changes,
        int? ttl = null)
    {
        var auditLog = new AuditLog(
                _idService.GetId(SysTypeNames.AuditLog),
                SysTypeNames.BusinessBalanceAudit,
                ttl,
                auditingPartitionId,
                auditingPartitionId,
                null,
                null,
                null,
                null,
                null,
                null,
                new AuditLog.BusinessBalanceAudit(
                    businessBalance,
                    changes,
                    businessBalance != null ? "patch" : "create"),
                null,
                null,
                operation,
                AuditingGroup.CloudApiBusinessManagement,
                DateTimeOffset.UtcNow);

        return await CreateAuditLogAsync(auditLog, auditingPartitionId, ttl);
    }

    public async Task<AuditLog?> AuditBusinessBalanceTransactionLogAsync(
        BusinessBalanceTransactionLog? businessBalanceTransactionLog,
        string auditingPartitionId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null)
    {
        var auditLog = new AuditLog(
            _idService.GetId(SysTypeNames.AuditLog),
            SysTypeNames.BusinessBalanceTransactionLogAudit,
            ttl,
            auditingPartitionId,
            auditingPartitionId,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            new AuditLog.BusinessBalanceTransactionLogAudit(
                businessBalanceTransactionLog,
                changes,
                businessBalanceTransactionLog != null ? "patch" : "create"),
            null,
            operation,
            AuditingGroup.CloudApiBusinessManagement,
            DateTimeOffset.UtcNow);

        return await CreateAuditLogAsync(auditLog, auditingPartitionId);
    }

    public async Task<AuditLog?> AuditCloudApiWebhookStatusUpdateEventAsync(
        string auditingPartitionId,
        string operation,
        AuditLogCloudApiWebhookValueObject auditLogCloudApiWebhookValueObject,
        string facebookWabaId,
        string? facebookBusinessId,
        string? facebookPhoneNumberId,
        string? facebookMessageTemplateId,
        int ttl)
    {
        var auditLog = new AuditLog(
            _idService.GetId(SysTypeNames.AuditLog),
            SysTypeNames.CloudApiWebhookStatusUpdateAudit,
            ttl,
            auditingPartitionId,
            facebookWabaId,
            facebookBusinessId,
            facebookPhoneNumberId,
            facebookMessageTemplateId,
            null,
            null,
            null,
            null,
            null,
            new AuditLog.CloudApiWebhookStatusUpdateAudit(
                auditLogCloudApiWebhookValueObject,
                "incoming_webhook_status_update_received"),
            operation,
            AuditingGroup.CloudApiWabaWebhookStatusUpdate,
            DateTimeOffset.UtcNow);

        return await CreateAuditLogAsync(auditLog, auditingPartitionId, ttl);
    }

    public async Task<List<AuditLog>> GetCloudApiWebhookStatusUpdateAuditLogsWithFiltersAsync(
        string facebookWabaId,
        WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters? filters,
        int offset,
        int limit)
    {
        var wabaWebhookStatusUpdateAuditLogs = new List<AuditLog>();
        if (filters == null)
        {
            wabaWebhookStatusUpdateAuditLogs = await _auditLogRepository.GetObjectsAsync(
                new QueryDefinition(
                        "SELECT * " +
                        "FROM c " +
                        "WHERE c.auditing_partition_id = @auditingPartitionId AND c.auditing_operation = @auditingOperation " +
                        "ORDER BY c.created_at DESC " +
                        "OFFSET @offset LIMIT @limit ")
                    .WithParameter("@auditingPartitionId", facebookWabaId)
                    .WithParameter("@auditingOperation", AuditingOperation.OnCloudApiWebhookStatusUpdateEvent)
                    .WithParameter("@offset", offset)
                    .WithParameter("@limit", limit),
                limit);
        }
        else if (!string.IsNullOrEmpty(filters.FacebookPhoneNumberId) ||
                 !string.IsNullOrEmpty(filters.FacebookBusinessId) ||
                 !string.IsNullOrEmpty(filters.FacebookMessageTemplateId))
        {
            var orClauses = new List<string>();
            var facebookPhoneNumberId = string.Empty;
            var facebookBusinessId = string.Empty;
            var facebookMessageTemplateId = string.Empty;
            foreach (var propertyInfo in filters.GetType().GetProperties())
            {
                switch (propertyInfo.Name)
                {
                    case "FacebookPhoneNumberId":
                        if (!string.IsNullOrEmpty(filters.FacebookPhoneNumberId))
                        {
                            orClauses.Add("c.facebook_phone_number_id = @facebookPhoneNumberId OR ");
                            facebookPhoneNumberId = filters.FacebookPhoneNumberId;
                        }

                        break;
                    case "FacebookBusinessId":
                        if (!string.IsNullOrEmpty(filters.FacebookBusinessId))
                        {
                            orClauses.Add("c.facebook_business_id = @facebookBusinessId OR ");
                            facebookBusinessId = filters.FacebookBusinessId;
                        }

                        break;
                    case "FacebookMessageTemplateId":
                        if (!string.IsNullOrEmpty(filters.FacebookMessageTemplateId))
                        {
                            orClauses.Add("c.facebook_message_template_id = @facebookMessageTemplateId");
                            facebookMessageTemplateId = filters.FacebookMessageTemplateId;
                        }

                        break;
                }
            }

            orClauses[^1] = orClauses[^1].Replace(" OR ", string.Empty);
            var orClauseString = string.Join(" ", orClauses);

            var queryDefinition = new QueryDefinition(
                    $"""
                     SELECT *FROM c
                     WHERE c.auditing_partition_id = @auditingPartitionId AND ({orClauseString})
                     ORDER BY c.created_at DESC
                     """)
                .WithParameter("@auditingPartitionId", facebookWabaId)
                .WithParameter("@auditingOperation", AuditingOperation.OnCloudApiWebhookStatusUpdateEvent)
                .WithParameter("@offset", offset)
                .WithParameter("@limit", limit);
            if (facebookPhoneNumberId != string.Empty)
            {
                queryDefinition.WithParameter("@facebookPhoneNumberId", facebookPhoneNumberId);
            }

            if (facebookBusinessId != string.Empty)
            {
                queryDefinition.WithParameter("@facebookBusinessId", facebookBusinessId);
            }

            if (facebookMessageTemplateId != string.Empty)
            {
                queryDefinition.WithParameter("@facebookMessageTemplateId", facebookMessageTemplateId);
            }

            wabaWebhookStatusUpdateAuditLogs = await _auditLogRepository.GetObjectsAsync(
                queryDefinition,
                limit);
        }

        return wabaWebhookStatusUpdateAuditLogs;
    }

    public async Task<AuditLog?> AuditBusinessWabaLevelCreditManagementSwitchBySystemAsync(
        BusinessBalance businessBalance,
        string auditingPartitionId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null)
    {
        var auditLog = new AuditLog(
            _idService.GetId(SysTypeNames.AuditLog),
            SysTypeNames.BusinessWabaLevelCreditManagementSwitchBySystem,
            ttl,
            auditingPartitionId,
            null,
            auditingPartitionId,
            null,
            null,
            null,
            null,
            null,
            new AuditLog.BusinessBalanceAudit(businessBalance, changes, "patch"),
            null,
            null,
            operation,
            AuditingGroup.BusinessWabaLevelCreditManagement,
            DateTimeOffset.UtcNow);

        return await CreateAuditLogAsync(auditLog, auditingPartitionId, ttl);
    }

    public async Task<AuditLog?> AuditBusinessWabaLevelCreditManagementSwitchByUserAsync(
        BusinessBalance businessBalance,
        string auditingPartitionId,
        string facebookBusinessId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null)
    {
        var auditLog = new AuditLog(
            _idService.GetId(SysTypeNames.AuditLog),
            SysTypeNames.BusinessWabaLevelCreditManagementSwitchByUser,
            ttl,
            auditingPartitionId,
            null,
            facebookBusinessId,
            null,
            null,
            null,
            null,
            null,
            new AuditLog.BusinessBalanceAudit(businessBalance, changes, "patch"),
            null,
            null,
            operation,
            AuditingGroup.BusinessWabaLevelCreditManagement,
            DateTimeOffset.UtcNow);

        return await CreateAuditLogAsync(auditLog, auditingPartitionId, ttl);
    }

    public async Task<AuditLog?> AuditBusinessWabaLevelCreditTransferAsync(
        string auditingPartitionId,
        string facebookWabaId,
        string facebookBusinessId,
        string operation,
        Dictionary<string, object?> changes,
        int? ttl = null)
    {
        var auditLog = new AuditLog(
            _idService.GetId(SysTypeNames.AuditLog),
            SysTypeNames.BusinessWabaLevelCreditTransfer,
            ttl,
            auditingPartitionId,
            facebookWabaId,
            facebookBusinessId,
            null,
            null,
            null,
            null,
            null,
            null,
            new AuditLog.BusinessBalanceTransactionLogAudit(null, changes, "create"),
            null,
            operation,
            AuditingGroup.BusinessWabaLevelCreditManagement,
            DateTimeOffset.UtcNow);

        return await CreateAuditLogAsync(auditLog, auditingPartitionId, ttl);
    }

    public async Task<CreateWabaPhoneNumberStatusChangedAuditLogOutput> AuditWabaPhoneNumberStatusChangedAsync(
        CreateWabaPhoneNumberStatusChangedAuditLogInput input)
    {
        var auditLog = new AuditLog(
            _idService.GetId(SysTypeNames.AuditLog),
            SysTypeNames.PhoneNumberStatusChanged,
            -1,
            input.FacebookWabaId,
            input.FacebookWabaId,
            null,
            null,
            null,
            input.SleekflowCompanyId,
            null,
            null,
            null,
            null,
            null,
            input.Status,
            AuditingGroup.WabaManagement,
            DateTimeOffset.UtcNow,
            input.CompanyName);

        return new CreateWabaPhoneNumberStatusChangedAuditLogOutput(await CreateAuditLogAsync(auditLog, input.FacebookWabaId));
    }

    // Handling auditing on cloud api request
    private async Task<AuditLog> CreateAuditAsync(
        string auditingPartitionId,
        string sleekflowCompanyId,
        string type,
        Dictionary<string, object?>? parameter = null)
    {
        return await _auditLogRepository.CreateAndGetAsync(
            new AuditLog(
                _idService.GetId(SysTypeNames.AuditLog),
                SysTypeNames.OperationAudit,
                -1,
                auditingPartitionId,
                auditingPartitionId,
                null,
                null,
                null,
                sleekflowCompanyId,
                new AuditLog.CloudApiRequestAudit(parameter, DateTimeOffset.UtcNow),
                null,
                null,
                null,
                null,
                type,
                AuditingGroup.WabaManagement,
                DateTimeOffset.UtcNow),
            auditingPartitionId);
    }

    // Handling auditing on cloud api response
    private async Task<T> GetAuditingAndResponse<T>(T response, AuditLog auditLog)
        where T : GraphApiResponseBase
    {
        var requestObject = new AuditLog.AuditingRequest(response.HttpPayload).ToDictionary();
        var responseObject = new AuditLog.AuditingResponse(response.HttpPayload).ToDictionary();
        if (!await _auditLogRepository.PatchCloudApiAuditAsync(auditLog, requestObject, responseObject))
        {
            _logger.LogError(
                "Error occur during inserting auditing logs {Request}, {Response}",
                responseObject,
                responseObject);
        }

        return response;
    }

    private async Task<AuditLog?> CreateAuditLogAsync(
        AuditLog auditLog,
        string auditingPartitionId,
        int? ttl = null)
    {
        try
        {
            if (ttl is not null)
            {
                auditLog.Ttl = ttl;
            }

            return await _auditLogRepository.CreateAndGetAsync(auditLog, auditingPartitionId);
        }
        catch (Exception e)
        {
            _logger.LogError(
                "Messaging hub - Error occur during auditing {AuditingGroup}/{AuditingOperation}/{AuditLog} | error {Error}",
                auditLog.AuditingGroup,
                auditLog.AuditingOperation,
                JsonConvert.SerializeObject(auditLog),
                JsonConvert.SerializeObject(e));
            return null;
        }
    }
}

public class BaseAuditingObject
{
    public string AuditingPartitionId { get; set; }

    public string SleekflowCompanyId { get; set; }

    public Dictionary<string, object?>? Parameters { get; set; }

    public string Operation { get; set; }

    public BaseAuditingObject(
        string auditingPartitionId,
        string sleekflowCompanyId = "",
        Dictionary<string, object?>? parameters = null,
        string operation = "")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        AuditingPartitionId = auditingPartitionId;
        Parameters = parameters;
        Operation = operation;
    }
}