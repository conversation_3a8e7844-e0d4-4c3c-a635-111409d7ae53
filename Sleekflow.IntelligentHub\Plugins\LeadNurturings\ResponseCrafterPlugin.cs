using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IResponseCrafterPlugin
{
    [KernelFunction("craft_response")]
    [Description(
        "Crafts responses using data from data pane and stores response results with structured keys for efficient workflow management.")]
    [return:
        Description("Simplified response for manager with key decision points only.")]
    Task<string> CraftResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where strategy results are stored in the data pane.")]
        string strategyKey,
        [Description("Data key where knowledge results are stored in the data pane (optional).")]
        string knowledgeKey = "");
}

public class ResponseCrafterPlugin : BaseLeadNurturingPlugin, IResponseCrafterPlugin
{
    public ResponseCrafterPlugin(
        ILogger<ResponseCrafterPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingCollaborationChatCacheService chatCacheService,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel,
            chatCacheService)
    {
    }

    private async Task<string> CraftResponseAsync(
        Kernel kernel,
        string conversationContext,
        string strategyInfo,
        string knowledgeInfo = "",
        string responseLanguage = "English",
        string additionalInstructionResponse = "")
    {
        var responseCrafterAgent = _agentDefinitions.GetResponseCrafterAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH_2_5, true),
            responseLanguage,
            _chatCacheService!,
            additionalInstructionResponse);

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, strategyInfo);

        if (!string.IsNullOrEmpty(knowledgeInfo))
        {
            AddContextToThread(agentThread, knowledgeInfo);
        }

        return await ExecuteAgentWithTelemetryAsync(responseCrafterAgent, agentThread, "ResponseCrafterPlugin");
    }

    [KernelFunction("craft_response")]
    [Description(
        "Crafts responses using data from data pane and stores response results with structured keys for efficient workflow management.")]
    [return:
        Description("Simplified response for manager with key decision points only.")]
    public async Task<string> CraftResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where strategy results are stored in the data pane.")]
        string strategyKey,
        [Description("Data key where knowledge results are stored in the data pane (optional).")]
        string knowledgeKey = "")
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.ResponseCrafterAgentOutput>(
            sessionKey,
            "ResponseCrafter",
            dataRetrievalFunc: async () => await RetrieveDataWithConfiguration(
                sessionKey,
                conversationContextKey,
                strategyKey,
                knowledgeKey),
            agentExecutionFunc: async (data) => await CraftResponseAsync(
                _kernel.Clone(),
                data["conversationContext"],
                data["strategyInfo"],
                data["knowledgeInfo"],
                data["responseLanguage"],
                data["additionalInstruction"]),
            storageKey: _keyManager.GetResponseKey(sessionKey),
            storageDataType: AgentOutputKeys.ResponseComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreResponseComponents(sessionKey, rawResult, parsedResponse),
            outputTransformerFunc: response => TransformResponseForManager(response)
        );
    }

    private async Task<Dictionary<string, string>> RetrieveDataWithConfiguration(
        string sessionKey,
        string conversationContextKey,
        string strategyKey,
        string knowledgeKey)
    {
        var results = await RetrieveMultipleDataAsync(
            (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext"),
            (strategyKey, AgentOutputKeys.StrategyComplete, "strategyInfo")
        );

        // Add optional knowledge data
        if (!string.IsNullOrEmpty(knowledgeKey))
        {
            var knowledgeInfo = await _dataPane.GetData(knowledgeKey, AgentOutputKeys.KnowledgeComplete) ?? "";
            results["knowledgeInfo"] = knowledgeInfo;
        }
        else
        {
            results["knowledgeInfo"] = "";
        }

        // Retrieve configuration
        results["responseLanguage"] = await GetConfigurationAsync(
            sessionKey,
            AgentOutputKeys.ResponseLanguage,
            "English");
        results["additionalInstruction"] = await GetConfigurationAsync(
            sessionKey,
            AgentOutputKeys.AdditionalInstructionResponse,
            "");

        return results;
    }

    private async Task StoreResponseComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.ResponseCrafterAgentOutput? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store the actual response for later use
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "ResponseCrafterAgent", AgentOutputKeys.ResponseNatural),
                    AgentOutputKeys.ResponseNatural,
                    parsedResponse.Response);

                // Store structured response if available
                if (!string.IsNullOrEmpty(parsedResponse.StructuredResponse))
                {
                    await _dataPane.StoreData(
                        _keyManager.GetAgentOutputKey(
                            sessionKey,
                            "ResponseCrafterAgent",
                            AgentOutputKeys.ResponseStructured),
                        AgentOutputKeys.ResponseStructured,
                        parsedResponse.StructuredResponse);
                }

                // Store reasoning if available
                if (!string.IsNullOrEmpty(parsedResponse.NaturalResponseReasoning))
                {
                    await _dataPane.StoreData(
                        _keyManager.GetAgentOutputKey(
                            sessionKey,
                            "ResponseCrafterAgent",
                            AgentOutputKeys.ResponseReasoning),
                        AgentOutputKeys.ResponseReasoning,
                        parsedResponse.NaturalResponseReasoning);
                }

                _logger.LogInformation(
                    "Response crafting completed for session {SessionKey}. Decision: {Decision}",
                    sessionKey,
                    parsedResponse.Decision ?? "continue_to_review");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to store response components for session {SessionKey}", sessionKey);
        }
    }

    private string TransformResponseForManager(LeadNurturingAgentDefinitions.ResponseCrafterAgentOutput? response)
    {
        if (response == null)
        {
            return JsonConvert.SerializeObject(
                new
                {
                    decision = "continue_to_review", status = "parsing_failed"
                });
        }

        // For ResponseCrafter, we only expose the decision to the manager
        // The actual response is stored in data pane but not passed to manager
        var managerResponse = new
        {
            agent_name = response.AgentName,
            decision = response.Decision ?? "continue_to_review",
            has_response = !string.IsNullOrEmpty(response.Response),
            status = "completed"
        };

        return JsonConvert.SerializeObject(managerResponse);
    }
}