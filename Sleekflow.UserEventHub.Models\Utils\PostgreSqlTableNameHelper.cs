using System.Text.RegularExpressions;

namespace Sleekflow.UserEventHub.Utils;

public static class PostgreSqlTableNameHelper
{
    private static readonly Regex ValidTableNameRegex = new(@"^[a-zA-Z][a-zA-Z0-9_]*$", RegexOptions.Compiled);
    private static readonly Regex InvalidCharactersRegex = new(@"[^a-zA-Z0-9_]", RegexOptions.Compiled);

    public static string GetEventsTableName(string sleekflowCompanyId)
    {
        var sanitized = SanitizeIdentifier(sleekflowCompanyId);
        return $"events_{sanitized}";
    }

    public static string GetMigrationHistoryTableName(string sleekflowCompanyId)
    {
        var sanitized = SanitizeIdentifier(sleekflowCompanyId);
        return $"file_migration_history_{sanitized}";
    }

    public static string GetEventsTableIndexName(string sleekflowCompanyId, string indexType)
    {
        var sanitized = SanitizeIdentifier(sleekflowCompanyId);
        return $"idx_events_{sanitized}_{indexType}";
    }

    public static string GetMigrationHistoryIndexName(string sleekflowCompanyId, string indexType)
    {
        var sanitized = SanitizeIdentifier(sleekflowCompanyId);
        return $"idx_file_migration_history_{sanitized}_{indexType}";
    }

    private static string SanitizeIdentifier(string identifier)
    {
        if (string.IsNullOrWhiteSpace(identifier))
            throw new ArgumentException("Identifier cannot be null or empty", nameof(identifier));

        // Replace invalid characters with underscores
        var sanitized = InvalidCharactersRegex.Replace(identifier, "_");

        // Ensure it starts with a letter
        if (!char.IsLetter(sanitized[0]))
            sanitized = "c_" + sanitized;

        // PostgreSQL identifier limit is 63 characters
        if (sanitized.Length > 50) // Leave room for table prefix
            sanitized = sanitized.Substring(0, 50);

        return sanitized.ToLowerInvariant();
    }

    public static bool IsValidTableName(string tableName)
    {
        return !string.IsNullOrWhiteSpace(tableName) && ValidTableNameRegex.IsMatch(tableName);
    }
}