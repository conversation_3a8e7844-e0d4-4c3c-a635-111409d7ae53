using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Stores.ShopifyStores;

public class ShopifyMessageTemplate
{
    [JsonProperty("message_body")]
    public string MessageBody { get; set; }

    [JsonProperty("message_params")]
    public List<string> MessageParams { get; set; }

    [JsonConstructor]
    public ShopifyMessageTemplate(
        string messageBody,
        List<string> messageParams)
    {
        MessageBody = messageBody;
        MessageParams = messageParams;
    }
}