using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class DeleteVersionedWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public DeleteVersionedWorkflow(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class DeleteVersionedWorkflowInput : IHasSleekflowStaff, Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteVersionedWorkflowInput(
            string sleekflowCompanyId,
            string workflowId,
            string workflowVersionedId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DeleteVersionedWorkflowOutput
    {
    }

    public async Task<DeleteVersionedWorkflowOutput> F(DeleteVersionedWorkflowInput deleteVersionedWorkflowInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deleteVersionedWorkflowInput.SleekflowStaffId,
            deleteVersionedWorkflowInput.SleekflowStaffTeamIds);

        await _workflowService.DeleteVersionedWorkflowAsync(
            deleteVersionedWorkflowInput.WorkflowId,
            deleteVersionedWorkflowInput.WorkflowVersionedId,
            deleteVersionedWorkflowInput.SleekflowCompanyId,
            sleekflowStaff);

        return new DeleteVersionedWorkflowOutput();
    }
}