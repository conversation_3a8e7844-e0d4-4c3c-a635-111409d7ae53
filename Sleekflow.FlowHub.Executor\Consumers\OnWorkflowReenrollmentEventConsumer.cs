﻿using MassTransit;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.WorkflowExecutions;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowReenrollmentEventConsumerDefinition
    : ConsumerDefinition<OnWorkflowReenrollmentEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowReenrollmentEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(15);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowReenrollmentEventConsumer : IConsumer<OnWorkflowReenrollmentEvent>
{
    private readonly IStateService _stateService;
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IFlowHubEventHandler _flowHubEventHandler;

    public OnWorkflowReenrollmentEventConsumer(
        IStateService stateService,
        IWorkflowExecutionService workflowExecutionService,
        IFlowHubEventHandler flowHubEventHandler)
    {
        _stateService = stateService;
        _workflowExecutionService = workflowExecutionService;
        _flowHubEventHandler = flowHubEventHandler;
    }

    public async Task Consume(ConsumeContext<OnWorkflowReenrollmentEvent> context)
    {
        var @event = context.Message;

        var state = await _stateService.GetOrDefaultStateByIdAsync(
            @event.SleekflowCompanyId,
            @event.StateIdToReenroll);

        var isReenrolled = false;

        if (state is not null
            && state.StateStatus == StateStatuses.Restricted)
        {
            isReenrolled = await _flowHubEventHandler.HandleReenrollmentAsync(state);
        }

        if (isReenrolled
            || state is null
            || state.StateStatus == StateStatuses.Reenrolled)
        {
            await _stateService.MarkStateAsReenrolledAsync(@event.StateIdToReenroll);
            await _workflowExecutionService.MarkWorkflowExecutionsAsReenrolledAsync(
                @event.SleekflowCompanyId,
                @event.StateIdToReenroll);

            await Parallel.ForEachAsync(
                @event.StateIdsToMarkAsReenrolled,
                new ParallelOptions()
                {
                    MaxDegreeOfParallelism = 10
                },
                async (stateId, ct) =>
                {
                    await _stateService.MarkStateAsReenrolledAsync(stateId);
                    await _workflowExecutionService.MarkWorkflowExecutionsAsReenrolledAsync(
                        @event.SleekflowCompanyId,
                        stateId);
                });
        }
    }
}