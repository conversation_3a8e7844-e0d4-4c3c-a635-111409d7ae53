using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Renderings;

namespace Sleekflow.CommerceHub.Models.Categories;

public class CategoryRenderingDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty(Category.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonConstructor]
    public CategoryRenderingDto(
        string id,
        string name,
        List<Description> descriptions,
        string storeId)
    {
        Id = id;
        Name = name;
        Descriptions = descriptions;
        StoreId = storeId;
    }

    public CategoryRenderingDto(
        Category category,
        LanguageOption languageOption)
        : this(
            category.Id,
            category.Names.Find(n => n.LanguageIsoCode == languageOption.LanguageIsoCode)?.Value
            ?? category.Names.First(n => n.LanguageIsoCode == languageOption.DefaultLanguageIsoCode).Value,
            category.Descriptions,
            category.StoreId)
    {
    }

    public static CategoryRenderingDto Sample()
    {
        return new CategoryRenderingDto(
            "myCategoryId",
            "My Category Name",
            new List<Description>()
            {
                new Description(
                    DescriptionTypes.Text,
                    new Multilingual("en", "This is a sample category description"),
                    null,
                    null)
            },
            "myStoreId");
    }
}