using System.Text;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.CommerceHub.CustomCatalogs;

namespace Sleekflow.CommerceHub.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class CustomCatalogsController : ControllerBase
{
    private readonly ICustomCatalogFileService _customCatalogFileService;

    public CustomCatalogsController(
        ICustomCatalogFileService customCatalogFileService)
    {
        _customCatalogFileService = customCatalogFileService;
    }

    [Route("GetCsvTemplate")]
    [HttpGet]
    [Produces("text/csv")]
    public async Task<IActionResult> GetCsvTemplate()
    {
        var csvTemplate = await _customCatalogFileService.GetCustomCatalogCsvTemplateAsync();

        return File(Encoding.UTF8.GetBytes(csvTemplate), "text/csv", "csv_template.csv");
    }

    [Route("GetCsvTemplateSample")]
    [HttpGet]
    [Produces("text/csv")]
    public async Task<IActionResult> GetCsvTemplateSample()
    {
        var csvTemplate = await _customCatalogFileService.GetCustomCatalogCsvTemplateSampleAsync();

        return File(Encoding.UTF8.GetBytes(csvTemplate), "text/csv", "csv_template_sample.csv");
    }
}