using System.Net.Http.Headers;
using System.Text;
using Google.Apis.Gmail.v1.Data;
using MassTransit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Configs;
using Sleekflow.EmailHub.Gmail.Authentications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Gmail.Authentications;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.EmailHub.Models.Gmail.Subscriptions;
using Sleekflow.EmailHub.Models.Providers;
using Sleekflow.EmailHub.Models.Subscriptions;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Gmail.Subscriptions;

public interface IGmailSubscriptionService : IEmailSubscriptionService
{
    Task UpdateHistoryId(
        string sleekflowCompanyId,
        string emailAddress,
        ulong historyId,
        CancellationToken cancellationToken = default);

    Task UpdateHistoryId(
        ProviderConfig providerConfig,
        ulong historyId,
        string emailAddress,
        CancellationToken cancellationToken = default);
}

public class GmailSubscriptionService : IGmailSubscriptionService, IScopedService
{
    private readonly ILogger<GmailSubscriptionService> _logger;
    private readonly IBus _bus;
    private readonly IGmailConfig _gmailConfig;
    private readonly HttpClient _httpClient;
    private readonly IProviderConfigService _providerConfigService;
    private readonly IGmailAuthenticationService _gmailAuthenticationService;

    public GmailSubscriptionService(
        ILogger<GmailSubscriptionService> logger,
        IBus bus,
        IGmailConfig gmailConfig,
        IHttpClientFactory httpClientFactory,
        IProviderConfigService providerConfigService,
        IGmailAuthenticationService gmailAuthenticationService)
    {
        _logger = logger;
        _bus = bus;
        _gmailConfig = gmailConfig;
        _providerConfigService = providerConfigService;
        _gmailAuthenticationService = gmailAuthenticationService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task SubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? subscriptionMetadata,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Started SubscribeToAnEmailAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        var authentication = await _gmailAuthenticationService.GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken);

        var gmailAuthenticationMetadata = authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                                          throw new SfInternalErrorException($"Cannot parse gmailAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var subscriptionRequestBody = new
        {
            labelIds = new List<string>
            {
                "INBOX"
            },
            topicName = $"projects/{_gmailConfig.ProjectId}/topics/{_gmailConfig.TopicId}",
        };

        var subscriptionContent = new StringContent(
            JsonConvert.SerializeObject(subscriptionRequestBody),
            Encoding.UTF8,
            "application/json");

        var subscriptionRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"https://gmail.googleapis.com/gmail/v1/users/{emailAddress}/watch"),
            Content = subscriptionContent
        };

        subscriptionRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            gmailAuthenticationMetadata.TokenType ?? "Bearer",
            gmailAuthenticationMetadata.AccessToken);

        var subscriptionResponse = await _httpClient.SendAsync(subscriptionRequestMessage, cancellationToken);

        var subscriptionResponseContent = await subscriptionResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!subscriptionResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(subscriptionResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} SubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                subscriptionResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"SubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        var subscriptionResponseModel = JsonConvert.DeserializeObject<WatchResponse>(subscriptionResponseContent)!;

        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Gmail,
            cancellationToken: cancellationToken);

        providerConfig.EmailSubscription.IsSubscribed = true;
        providerConfig.EmailSubscription.LastSubscriptionTime = DateTimeOffset.UtcNow;
        providerConfig.EmailSubscription.EmailSubscriptionMetadata = new GmailSubscriptionMetadata(
            subscriptionResponseModel.HistoryId,
            DateTimeOffset.FromUnixTimeMilliseconds((long)subscriptionResponseModel.Expiration!));

        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);

        await _bus.Publish(
            new OnGmailSyncAllEmailsTriggeredEvent(emailAddress),
            cancellationToken);

        _logger.LogInformation(
            "SubscribeToAnEmail successes: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);
    }

    public async Task UnsubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var authentication = await _gmailAuthenticationService.GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken);

        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Gmail,
            cancellationToken: cancellationToken);

        var subscription = providerConfig.EmailSubscription;

        var gmailSubscriptionMetadata = subscription.EmailSubscriptionMetadata as GmailSubscriptionMetadata ??
                                        throw new SfInternalErrorException($"Cannot parse gmailSubscriptionMetadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        var isAlreadyExpired = !subscription.IsSubscribed ||
                               DateTimeOffset.UtcNow > gmailSubscriptionMetadata.ExpireIn;
        if (isAlreadyExpired)
        {
            return;
        }

        _logger.LogInformation(
            "Started UnSubscribeToAnEmailAsync. emailAddress {emailAddress}, sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        var unsubscriptionRequestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri($"https://gmail.googleapis.com/gmail/v1/users/{emailAddress}/stop"),
        };

        var gmailAuthenticationMetadata = authentication.EmailAuthenticationMetadata as GmailAuthenticationMetadata ??
                                          throw new NullReferenceException($"Cannot parse gmailAuthenticationMetaData: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        unsubscriptionRequestMessage.Headers.Authorization = new AuthenticationHeaderValue(
            gmailAuthenticationMetadata.TokenType ?? "Bearer",
            gmailAuthenticationMetadata.AccessToken);

        var unsubscriptionResponse = await _httpClient.SendAsync(unsubscriptionRequestMessage, cancellationToken);

        var unsubscriptionResponseContent = await unsubscriptionResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!unsubscriptionResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(unsubscriptionResponseContent);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} UnSubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                unsubscriptionResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException(
                $"UnSubscribeToAnEmail fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");
        }

        gmailSubscriptionMetadata.ExpireIn = DateTimeOffset.UtcNow;
        subscription.IsSubscribed = false;
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);

        _logger.LogInformation(
            "UnSubscribeToAnEmail successes: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);
    }

    public async Task RenewEmailSubscriptionAsync(CancellationToken cancellationToken = default)
    {
        var renewGmailSubscriptionInput = await _providerConfigService.GetEmailAddressesByProviderNameAsync(ProviderNames.Gmail, cancellationToken);

        foreach (var input in renewGmailSubscriptionInput)
        {
            EmailSubscription subscription;
            try
            {
                subscription = await GetSubscriptionAsync(
                    input["sleekflow_company_id"],
                    input["email_address"],
                    cancellationToken);
            }
            catch (Exception)
            {
                continue;
            }

            var subscriptionMetadata = subscription.EmailSubscriptionMetadata as GmailSubscriptionMetadata ??
                                       throw new SfInternalErrorException($"Cannot parse gmail metadata: emailAddress {input["input"]} of sleekflowCompanyId {input["sleekflow_company_id"]}");
            if (subscriptionMetadata.ExpireIn + TimeSpan.FromSeconds(30) > DateTimeOffset.UtcNow)
            {
                await SubscribeAtEmailAddressAsync(
                    input["sleekflow_company_id"],
                    input["email_address"],
                    new Dictionary<string, string>(),
                    cancellationToken);
            }
        }
    }

    public async Task<List<string>> FilterSubscribedCompanies(
        List<string> companyIds,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var result = new List<string>();

        foreach (var companyId in companyIds)
        {
            try
            {
                _ = await GetSubscriptionAsync(
                    companyId,
                    emailAddress,
                    cancellationToken);
                result.Add(companyId);
            }
            catch (Exception)
            {
                // ignored unsubscribed company
            }
        }

        return result;
    }

    public async Task<EmailSubscription> GetSubscriptionAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Gmail,
            cancellationToken: cancellationToken);

        if (providerConfig == null || !providerConfig.EmailSubscription.IsSubscribed)
        {
            _logger.LogError(
                "GetSubscriptionAsync fails due to not subscribed: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                emailAddress,
                sleekflowCompanyId);

            throw new SfUnauthorizedException();
        }

        return providerConfig.EmailSubscription;
    }

    public async Task UpdateHistoryId(
        string sleekflowCompanyId,
        string emailAddress,
        ulong historyId,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Gmail,
            cancellationToken: cancellationToken).ConfigureAwait(false);

        await UpdateHistoryId(providerConfig, historyId, emailAddress, cancellationToken);
    }

    public async Task UpdateHistoryId(
        ProviderConfig providerConfig,
        ulong historyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var gmailSubscriptionMetadata = providerConfig.EmailSubscription.EmailSubscriptionMetadata as GmailSubscriptionMetadata ??
                                        throw new SfInternalErrorException($"Cannot parse gmailSubscriptionMetaData: emailAddress {emailAddress}");
        gmailSubscriptionMetadata.HistoryId = historyId;
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);
    }
}