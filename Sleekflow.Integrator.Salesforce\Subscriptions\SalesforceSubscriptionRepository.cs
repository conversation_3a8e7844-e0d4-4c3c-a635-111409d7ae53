﻿using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Subscriptions;

public interface ISalesforceSubscriptionRepository : IRepository<SalesforceSubscription>
{
}

public class SalesforceSubscriptionRepository
    : BaseRepository<SalesforceSubscription>,
        ISalesforceSubscriptionRepository,
        ISingletonService
{
    public SalesforceSubscriptionRepository(
        ILogger<BaseRepository<SalesforceSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}