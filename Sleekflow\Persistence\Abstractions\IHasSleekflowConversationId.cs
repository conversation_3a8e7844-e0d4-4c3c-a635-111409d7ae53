using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Persistence.Abstractions;

public interface IHasSleekflowConversationId
{
    public const string PropertyNameSleekflowConversationId = "sleekflow_conversation_id";

    [Required]
    [StringLength(128, MinimumLength = 1)]
    [JsonProperty(PropertyNameSleekflowConversationId)]
    public string SleekflowConversationId { get; set; }
}