using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

namespace Sleekflow.FlowHub.Models.Messages;

public class InteractiveReplyMessageObject
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("button_reply")]
    public ButtonReplyMessageObject? ButtonReply { get; set; }

    [JsonProperty("list_reply")]
    public ListReplyMessageObject? ListReply { get; set; }

    /// <summary>
    /// Native Flow Response Message
    /// https://developers.facebook.com/docs/whatsapp/flows/reference/responsemsgwebhook
    /// </summary>
    [JsonProperty("nfm_reply")]
    public NfmReplyMessageObject? NfmReply { get; set; }

    [JsonConstructor]
    public InteractiveReplyMessageObject(
        string type,
        ButtonReplyMessageObject? buttonReply,
        ListReplyMessageObject? listReply,
        NfmReplyMessageObject? nfmReply)
    {
        Type = type;
        ButtonReply = buttonReply;
        ListReply = listReply;
        NfmReply = nfmReply;
    }
}