﻿using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileDeletedLogData
{
    [JsonProperty("trigger_details")]
    public UserProfileDeletedTriggerLogData TriggerDetails { get; set; }

    [JsonConstructor]
    public UserProfileDeletedLogData(UserProfileDeletedTriggerLogData triggerDetails)
    {
        TriggerDetails = triggerDetails;
    }
}

public class UserProfileDeletedTriggerLogData
{
    [JsonProperty("trigger_source")]
    public string TriggerSource { get; set; }

    [JsonProperty("staff_id")]
    public string? StaffId { get; set; }

    [JsonProperty("staff_name")]
    public string? StaffName { get; set; }

    [JsonConstructor]
    public UserProfileDeletedTriggerLogData(
        string triggerSource,
        string? staffId,
        string? staffName)
    {
        TriggerSource = triggerSource;
        StaffId = staffId;
        StaffName = staffName;
    }
}