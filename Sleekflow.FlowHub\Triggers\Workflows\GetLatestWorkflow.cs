using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class GetLatestWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public GetLatestWorkflow(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class GetLatestWorkflowInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonConstructor]
        public GetLatestWorkflowInput(
            string sleekflowCompanyId,
            string workflowId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
        }
    }

    public class GetLatestWorkflowOutput
    {
        [JsonProperty("latest_workflow")]
        public WorkflowDto LatestWorkflow { get; set; }

        [JsonConstructor]
        public GetLatestWorkflowOutput(WorkflowDto latestWorkflow)
        {
            LatestWorkflow = latestWorkflow;
        }
    }

    public async Task<GetLatestWorkflowOutput> F(GetLatestWorkflowInput getLatestWorkflowInput)
    {
        var latestWorkflow = await _workflowService.GetLatestWorkflowAsync(
            getLatestWorkflowInput.WorkflowId,
            getLatestWorkflowInput.SleekflowCompanyId);

        return new GetLatestWorkflowOutput(new WorkflowDto(latestWorkflow));
    }
}