using System.Net;
using System.Net.Sockets;

namespace Sleekflow.Mvc.Https;

public class PrivateNetworkHttpClientHandler : HttpClientHandler
{
    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        if (IsPrivateHost(request.RequestUri!.Host))
        {
            throw new PrivateNetworkAccessException("Access to private network IP addresses is not allowed.");
        }

        return await base.SendAsync(request, cancellationToken);
    }

    private static bool IsPrivateHost(string host)
    {
        return IPAddress.TryParse(host, out var ipAddress) && IsPrivateIpAddress(ipAddress);
    }

    /// <summary>
    /// Returns true if the IP address is in a private range.<br/>
    /// IPv4: Loopback, link local ("169.254.x.x"), class A ("10.x.x.x"), class B ("172.16.x.x" to "172.31.x.x") and class C ("192.168.x.x").<br/>
    /// IPv6: Loopback, link local, site local, unique local and private IPv4 mapped to IPv6.<br/>
    /// </summary>
    /// <param name="ip">The IP address.</param>
    /// <returns>True if the IP address was in a private range.</returns>
    /// <example><code>bool isPrivate = IPAddress.Parse("127.0.0.1").IsPrivate();</code></example>
    private static bool IsPrivateIpAddress(IPAddress ip)
    {
        // Map back to IPv4 if mapped to IPv6, for example "::ffff:*******" to "*******".
        if (ip.IsIPv4MappedToIPv6)
        {
            ip = ip.MapToIPv4();
        }

        // Checks loopback ranges for both IPv4 and IPv6.
        if (IPAddress.IsLoopback(ip))
        {
            return true;
        }

        // IPv4
        if (ip.AddressFamily == AddressFamily.InterNetwork)
        {
            return IsPrivateIPv4(ip.GetAddressBytes());
        }

        // IPv6
        if (ip.AddressFamily == AddressFamily.InterNetworkV6)
        {
            return ip.IsIPv6LinkLocal ||
                   ip.IsIPv6UniqueLocal ||
                   ip.IsIPv6SiteLocal;
        }

        throw new NotSupportedException(
            $"IP address family {ip.AddressFamily} is not supported, expected only IPv4 (InterNetwork) or IPv6 (InterNetworkV6).");
    }

    private static bool IsPrivateIPv4(byte[] ipv4Bytes)
    {
        // Link local (no IP assigned by DHCP): *********** to *************** (***********/16)
        bool IsLinkLocal() => ipv4Bytes[0] == 169 && ipv4Bytes[1] == 254;

        // Class A private range: 10.0.0.0 – ************** (10.0.0.0/8)
        bool IsClassA() => ipv4Bytes[0] == 10;

        // Class B private range: ********** – ************** (**********/12)
        bool IsClassB() => ipv4Bytes[0] == 172 && ipv4Bytes[1] >= 16 && ipv4Bytes[1] <= 31;

        // Class C private range: *********** – *************** (***********/16)
        bool IsClassC() => ipv4Bytes[0] == 192 && ipv4Bytes[1] == 168;

        bool IsAksReservedRange() =>
            (ipv4Bytes[0] == 169 && ipv4Bytes[1] == 254) || // ***********/16
            (ipv4Bytes[0] == 172 && (ipv4Bytes[1] == 30 || ipv4Bytes[1] == 31)) || // **********/16 and **********/16
            (ipv4Bytes[0] == 192 && ipv4Bytes[1] == 0 && ipv4Bytes[2] == 2); // *********/24

        bool IsContainerAppsReservedRange() =>
            ipv4Bytes[0] == 100 && ipv4Bytes[1] == 100 && (
                (ipv4Bytes[2] >= 0 && ipv4Bytes[2] <= 127) || // ***********/17
                (ipv4Bytes[2] >= 128 && ipv4Bytes[2] <= 159) || // *************/19
                (ipv4Bytes[2] >= 160 && ipv4Bytes[2] <= 191) || // *************/19
                (ipv4Bytes[2] >= 192 && ipv4Bytes[2] <= 223) // *************/19
            );

        return IsLinkLocal()
               || IsClassA() || IsClassC() || IsClassB()
               || IsAksReservedRange() || IsContainerAppsReservedRange();
    }
}