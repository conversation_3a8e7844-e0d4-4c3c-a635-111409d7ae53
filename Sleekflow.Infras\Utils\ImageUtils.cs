using Pulumi;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;

namespace Sleekflow.Infras.Utils;

public static class ImageUtils
{
    /// <summary>
    /// Creates a Docker image with standardized configuration.
    /// </summary>
    /// <param name="registry">The Azure Container Registry where the image will be stored.</param>
    /// <param name="registryUsername">The username for the container registry.</param>
    /// <param name="registryPassword">The password for the container registry.</param>
    /// <param name="sleekflowPrefixedShortName">The name of the service (e.g., 'sleekflow-email-hub', 'sleekflow-share-hub'). This is used to construct the full image name in the registry.</param>
    /// <param name="buildTime">The build timestamp or version tag (e.g., '20240315.1', 'v1.0.0'). This is used as the image tag in the registry.</param>
    /// <param name="options">Optional. Custom resource options for the Docker image.</param>
    /// <returns>A Docker image resource configured with standardized settings.</returns>
    public static Docker.Image CreateImage(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        string sleekflowPrefixedShortName,
        string buildTime,
        CustomResourceOptions? options = null)
    {
        return CreateAndGetImageWithImageName(
            registry,
            registryUsername,
            registryPassword,
            sleekflowPrefixedShortName,
            buildTime).Image;
    }

    public static (Docker.Image Image, Output<string> Name) CreateAndGetImageWithImageName(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        string sleekflowPrefixedShortName,
        string buildTime,
        CustomResourceOptions? options = null)
    {
        // Match the image name format from GitHub workflow
        var sourceImageName = $"ghcr.io/sleekflow/{sleekflowPrefixedShortName}:{buildTime}";
        var targetImageName = Output.Format($"{registry.LoginServer}/{sleekflowPrefixedShortName}:{buildTime}");

        var image = new Docker.Image(
            sleekflowPrefixedShortName,
            new Docker.ImageArgs
            {
                ImageName = targetImageName,
                Build = new Docker.Inputs.DockerBuildArgs
                {
                    Context = ".",
                    Dockerfile = "Dockerfile.copy",
                    Args = new InputMap<string>
                    {
                        {
                            "SRC_IMG", sourceImageName
                        }
                    }
                },
                Registry = new Docker.Inputs.RegistryArgs
                {
                    Server = registry.LoginServer, Username = registryUsername, Password = registryPassword
                },
            },
            options ?? new CustomResourceOptions
            {
                Parent = registry
            });
        return (image, targetImageName);
    }
}