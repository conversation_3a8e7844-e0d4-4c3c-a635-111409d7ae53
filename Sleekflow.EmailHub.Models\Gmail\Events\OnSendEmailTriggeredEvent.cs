using MimeKit;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.Events;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.EmailHub.Models.Gmail.Events;

public class OnSendEmailTriggeredEvent : IEvent, IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public EmailContact Sender { get; set; }

    public string Subject { get; set; }

    public List<EmailContact> To { get; set; }

    public List<EmailContact> Cc { get; set; }

    public List<EmailContact> Bcc { get; set; }

    public List<EmailContact> ReplyTo { get; set; }

    public string? HtmlBody { get; set; }

    public string? TextBody { get; set; }

    public Dictionary<string, string>? EmailMetadata { get; set; }

    public List<EmailAttachment> EmailAttachments { get; set; }

    public string ProviderName { get; set; }

    public OnSendEmailTriggeredEvent(
        string sleekflowCompanyId,
        string providerName,
        EmailContact sender,
        string subject,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string? htmlBody,
        string? textBody,
        List<EmailAttachment> emailAttachments,
        Dictionary<string, string>? emailMetadata)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ProviderName = providerName;
        Sender = sender;
        Subject = subject;
        To = to;
        Cc = cc;
        Bcc = bcc;
        ReplyTo = replyTo;
        HtmlBody = htmlBody;
        TextBody = textBody;
        EmailMetadata = emailMetadata;
        EmailAttachments = emailAttachments;
    }
}