using Microsoft.Azure.Cosmos;

namespace Sleekflow.Exceptions;

public class SfNotFoundObjectException : ErrorCodeException
{
    public SfNotFoundObjectException(string objectId)
        : base(
            ErrorCodeConstant.SfNotFoundObjectException,
            $"The object doesn't exist. objectId {objectId}",
            new Dictionary<string, object?>
            {
                {
                    "objectId", objectId
                }
            })
    {
    }

    public SfNotFoundObjectException(Exception exception, string objectId, string partitionKey)
        : base(
            ErrorCodeConstant.SfNotFoundObjectException,
            $"The object doesn't exist. objectId {objectId} partitionKey {partitionKey}",
            new Dictionary<string, object?>
            {
                {
                    "objectId", objectId
                },
                {
                    "partitionKey", partitionKey
                },
            },
            exception)
    {
    }

    public SfNotFoundObjectException(Exception exception, string objectId, PartitionKey partitionKey)
        : base(
            ErrorCodeConstant.SfNotFoundObjectException,
            $"The object doesn't exist. objectId {objectId} partitionKey {partitionKey}",
            new Dictionary<string, object?>
            {
                {
                    "objectId", objectId
                },
                {
                    "partitionKey", partitionKey
                },
            },
            exception)
    {
    }

    public SfNotFoundObjectException(string objectId, string partitionKey)
        : base(
            ErrorCodeConstant.SfNotFoundObjectException,
            $"The object doesn't exist. objectId {objectId} partitionKey {partitionKey}",
            new Dictionary<string, object?>
            {
                {
                    "objectId", objectId
                },
                {
                    "partitionKey", partitionKey
                },
            })
    {
    }

    public SfNotFoundObjectException(string objectId, PartitionKey partitionKey)
        : base(
            ErrorCodeConstant.SfNotFoundObjectException,
            $"The object doesn't exist. objectId {objectId} partitionKey {partitionKey}",
            new Dictionary<string, object?>
            {
                {
                    "objectId", objectId
                },
                {
                    "partitionKey", partitionKey
                },
            })
    {
    }

    public SfNotFoundObjectException(string[] objectIds, string partitionKey)
        : base(
            ErrorCodeConstant.SfNotFoundObjectException,
            $"The object doesn't exist. objectIds {string.Join(",", objectIds)} partitionKey {partitionKey}",
            new Dictionary<string, object?>
            {
                {
                    "objectIds", objectIds
                },
                {
                    "partitionKey", partitionKey
                },
            })
    {
    }
}