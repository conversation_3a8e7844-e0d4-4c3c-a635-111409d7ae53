using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactLabelRelationshipsStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-label-relationships";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty("add_labels__expr")]
    public string? AddLabelsExpr { get; set; }

    [JsonProperty("remove_labels__expr")]
    public string? RemoveLabelsExpr { get; set; }

    [JsonProperty("set_labels__expr")]
    public string? SetLabelsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactLabelRelationshipsStepArgs(
        string contactIdExpr,
        string? addLabelsExpr,
        string? removeLabelsExpr,
        string? setLabelsExpr)
    {
        ContactIdExpr = contactIdExpr;
        AddLabelsExpr = addLabelsExpr;
        RemoveLabelsExpr = removeLabelsExpr;
        SetLabelsExpr = setLabelsExpr;
    }
}