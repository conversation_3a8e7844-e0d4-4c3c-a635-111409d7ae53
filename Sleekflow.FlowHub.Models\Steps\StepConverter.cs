using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;

namespace Sleekflow.FlowHub.Models.Steps;

public class StepConcreteClassResolver : DefaultContractResolver
{
    protected override JsonConverter? ResolveContractConverter(Type objectType)
    {
        if (typeof(Step).IsAssignableFrom(objectType)
            && !objectType.IsAbstract)
        {
            return null;
        }

        return base.ResolveContractConverter(objectType);
    }
}

public class StepConverter : JsonConverter
{
    private static readonly JsonSerializerSettings ConcreteClassConversion =
        new JsonSerializerSettings()
        {
            ContractResolver = new StepConcreteClassResolver()
        };

    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(Step);
    }

    public override object ReadJson(
        JsonReader reader,
        Type objectType,
        object? existingValue,
        JsonSerializer serializer)
    {
        var jo = JObject.Load(reader);

        if (jo.ContainsKey("call") && jo.ContainsKey("args"))
        {
            var callStr = jo.GetValue("call")!.Value<string>()!;

            switch (callStr)
            {
                case AddInternalNoteToContactStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AddInternalNoteToContactStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CaptureUserEventStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CaptureUserEventStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateOrUpdateContactStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateOrUpdateContactStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case GenerateQrCodeStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<GenerateQrCodeStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case HttpGetStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<HttpGetStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case HttpPostStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<HttpPostStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case HttpPutStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<HttpPutStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case HttpPatchStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<HttpPatchStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case HttpDeleteStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<HttpDeleteStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case HttpV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<HttpV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case LikeFacebookPostCommentStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<LikeFacebookPostCommentStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case ReplyFacebookPostCommentStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<ReplyFacebookPostCommentStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case ReplyInstagramMediaCommentStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<ReplyInstagramMediaCommentStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SendMessageStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SendMessageStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SleepStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SleepStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactCollaboratorRelationshipsStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactCollaboratorRelationshipsStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactConversationStatusStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactConversationStatusStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactLabelRelationshipsStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactLabelRelationshipsStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactListRelationshipsStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactListRelationshipsStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactOwnerRelationshipsStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactOwnerRelationshipsStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactPropertiesStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactPropertiesStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactPropertiesByPropertyKeyStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactPropertiesByPropertyKeyStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case WaitForEventStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<WaitForEventStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateSalesforceObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateSalesforceObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateContactStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateContactStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateContactV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateContactV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateContactWithSalesforceUserMappingStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateContactWithSalesforceUserMappingStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SearchSalesforceObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SearchSalesforceObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateSalesforceObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateSalesforceObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateSchemafulObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateSchemafulObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateSchemafulObjectV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateSchemafulObjectV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateSchemafulObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateSchemafulObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case ScheduledTriggerConditionsCheckStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<ScheduledTriggerConditionsCheckStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case RecommendReplyStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<RecommendReplyStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case RecommendReplyStreamingStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<RecommendReplyStreamingStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AggregateStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AggregateStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AgentRecommendReplyStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AgentRecommendReplyStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AgentEvaluateScoreStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AgentEvaluateScoreStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AgentEvaluateIntentionStepExecutorStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AgentEvaluateIntentionStepExecutorStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AgentSummarizeStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AgentSummarizeStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SendMessageV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SendMessageV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactCollaboratorRelationshipsV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactCollaboratorRelationshipsV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactConversationStatusV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactConversationStatusV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactLabelRelationshipsV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactLabelRelationshipsV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactListRelationshipsV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactListRelationshipsV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactOwnerRelationshipsV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactOwnerRelationshipsV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactPropertiesV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactPropertiesV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AddInternalNoteToContactV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AddInternalNoteToContactV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SleepV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SleepV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case WaitForEventV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<WaitForEventV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case EndStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<EndStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateTicketStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateTicketStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateGoogleSheetsRowStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateGoogleSheetsRowStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateGoogleSheetsRowStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateGoogleSheetsRowStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SearchGoogleSheetsRowStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SearchGoogleSheetsRowStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateSchemafulObjectV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateSchemafulObjectV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateHubspotObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateHubspotObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateHubspotObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateHubspotObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SearchHubspotObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SearchHubspotObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateZohoObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateZohoObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateZohoObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateZohoObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SearchZohoObjectStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SearchZohoObjectStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SendMetaCapiEventStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SendMetaCapiEventStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case CreateSalesforceObjectV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<CreateSalesforceObjectV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateSalesforceObjectV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateSalesforceObjectV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SearchSalesforceObjectV2StepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SearchSalesforceObjectV2StepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case JumpToStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<JumpToStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case UpdateContactPropertiesWithRecordSourceStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<UpdateContactPropertiesWithRecordSourceStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case ReplyFacebookInstagramPostCommentStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<ReplyFacebookInstagramPostCommentStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case EnterAiAgentStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<EnterAiAgentStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case SendVariableToParentWorkflowStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<SendVariableToParentWorkflowStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case DmFbIgPostCommentStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<DmFbIgPostCommentStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case EvaluateExitConditionsStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<EvaluateExitConditionsStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AgentCalculateLeadScoreStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AgentCalculateLeadScoreStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;

                case AgentAddLabelStepArgs.CallName:
                    return JsonConvert.DeserializeObject<CallStep<AgentAddLabelStepArgs>>(
                        jo.ToString(),
                        ConcreteClassConversion)!;
            }

            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult("Not recognized call step")
                });
        }
        else if (jo.ContainsKey("log_level") && jo.ContainsKey("log_message"))
        {
            return JsonConvert.DeserializeObject<LogStep>(jo.ToString(), ConcreteClassConversion)!;
        }
        else if (jo.ContainsKey("parallel_branches"))
        {
            return JsonConvert.DeserializeObject<ParallelStep>(jo.ToString(), ConcreteClassConversion)!;
        }
        else if (jo.ContainsKey("substeps"))
        {
            return JsonConvert.DeserializeObject<SubFlowStep>(jo.ToString(), ConcreteClassConversion)!;
        }
        else if (jo.ContainsKey("switch"))
        {
            return JsonConvert.DeserializeObject<SwitchStep>(jo.ToString(), ConcreteClassConversion)!;
        }
        else if (jo.ContainsKey("error_code") && jo.ContainsKey("error_message"))
        {
            return JsonConvert.DeserializeObject<ThrowStep>(jo.ToString(), ConcreteClassConversion)!;
        }
        else if (jo.ContainsKey("try") && jo.ContainsKey("catch"))
        {
            return JsonConvert.DeserializeObject<TryCatchStep>(jo.ToString(), ConcreteClassConversion)!;
        }
        else
        {
            return JsonConvert.DeserializeObject<SimpleStep>(jo.ToString(), ConcreteClassConversion)!;
        }
    }

#pragma warning disable JA1001
    public override bool CanWrite => false;
#pragma warning restore JA1001

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        throw new NotImplementedException();
    }
}
