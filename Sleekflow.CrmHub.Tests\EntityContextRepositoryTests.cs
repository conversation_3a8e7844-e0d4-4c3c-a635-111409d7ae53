﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Serilog;
using Serilog.Enrichers.Span;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests;

public class EntityContextRepositoryTests
{
    private static readonly string PartitionId = $"TEST PARTITION-{Guid.NewGuid()}";

    public EntityContextRepositoryTests()
    {
        var loggerConfiguration = new LoggerConfiguration()
            .Enrich.WithSpan()
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .WriteTo.Async(
                wt => wt.Console(
                    outputTemplate:
                    "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
    }

    [TearDown]
    public async Task TearDown()
    {
        var entityContextRepository = GetEntityContextRepository();

        var entities = await entityContextRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sys_sleekflow_company_id = @sysSleekflowCompanyId AND c.sys_entity_type_name = @sysEntityTypeName")
                .WithParameter("@sysSleekflowCompanyId", PartitionId)
                .WithParameter("@sysEntityTypeName", "SalesOrder"));

        foreach (var crmHubEntityContext in entities)
        {
            await entityContextRepository.DeleteAsync(
                crmHubEntityContext.Id,
                crmHubEntityContext.Id);
        }
    }

    private EntityContextRepository GetEntityContextRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var entityContextRepository = new EntityContextRepository(
            NullLogger<BaseRepository<CrmHubEntityContext>>.Instance,
            serviceProvider);

        return entityContextRepository;
    }

    public class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }

    [Test]
    public async Task GetObjectsTest()
    {
        var entityContextRepository = GetEntityContextRepository();

        var entityContext1 = await entityContextRepository.CreateAndGetAsync(
            new CrmHubEntityContext(
                id: nameof(GetObjectsTest),
                sysSleekflowCompanyId: PartitionId,
                sysEntityTypeName: "SalesOrder",
                ctxPhoneNumber: null,
                ctxEmail: null,
                ctxExternalId: null,
                ctxExternalIds: null,
                sysTypeName: "Entity",
                eTag: null),
            nameof(GetObjectsTest));
        Assert.That(entityContext1, Is.Not.Null);
        Assert.That(entityContext1.Id, Is.EqualTo(nameof(GetObjectsTest)));
        Assert.That(entityContext1.SysSleekflowCompanyId, Is.EqualTo(PartitionId));
        Assert.That(entityContext1.SysEntityTypeName, Is.EqualTo("SalesOrder"));

        var objects1 = await entityContextRepository.GetObjectsAsync(
            ec => ec.CtxExternalIds == null && ec.SysEntityTypeName == "SalesOrder");
        Assert.That(objects1.Count, Is.EqualTo(1));

        for (int i = 0; i < 100; i++)
        {
            var ec = await entityContextRepository.CreateAndGetAsync(
                new CrmHubEntityContext(
                    id: nameof(GetObjectsTest) + i,
                    sysSleekflowCompanyId: PartitionId,
                    sysEntityTypeName: "SalesOrder",
                    ctxPhoneNumber: null,
                    ctxEmail: null,
                    ctxExternalId: null,
                    ctxExternalIds: new List<string>
                    {
                        "123", "456"
                    },
                    sysTypeName: "Entity",
                    eTag: null),
                nameof(GetObjectsTest) + i);
            Assert.That(ec, Is.Not.Null);
            Assert.That(ec.Id, Is.EqualTo(nameof(GetObjectsTest) + i));
            Assert.That(ec.SysSleekflowCompanyId, Is.EqualTo(PartitionId));
            Assert.That(ec.SysEntityTypeName, Is.EqualTo("SalesOrder"));
        }

        var objects2 = await entityContextRepository.GetObjectsAsync(
            ec => ec.CtxExternalIds!.Contains("123") && ec.SysEntityTypeName == "SalesOrder");
        Assert.That(objects2.Count, Is.EqualTo(100));

        var objects3 = await entityContextRepository.GetObjectsAsync(
            ec => ec.CtxExternalIds!.Contains("456") && ec.SysEntityTypeName == "SalesOrder");
        Assert.That(objects3.Count, Is.EqualTo(100));

        var objects4 = await entityContextRepository.GetObjectsAsync(
            ec => true && ec.SysEntityTypeName == "SalesOrder",
            limit: 1);
        Assert.That(objects4.Count, Is.EqualTo(1));

        var objects5 = await entityContextRepository.GetObjectsAsync(
            ec => true && ec.SysEntityTypeName == "SalesOrder",
            limit: 10);
        Assert.That(objects5.Count, Is.EqualTo(10));
    }

    [Test]
    public async Task UpsertWithIncorrectEtagTest()
    {
        var entityContextRepository = GetEntityContextRepository();

        var entityContext1 = await entityContextRepository.GetOrDefaultAsync(
            nameof(UpsertWithIncorrectEtagTest),
            nameof(UpsertWithIncorrectEtagTest));
        Assert.That(entityContext1, Is.Null);

        var entityContext2 = await entityContextRepository.CreateAndGetAsync(
            new CrmHubEntityContext(
                id: nameof(UpsertWithIncorrectEtagTest),
                sysSleekflowCompanyId: PartitionId,
                sysEntityTypeName: "SalesOrder",
                ctxPhoneNumber: null,
                ctxEmail: null,
                ctxExternalId: null,
                ctxExternalIds: null,
                sysTypeName: "Entity",
                eTag: null),
            nameof(UpsertWithIncorrectEtagTest));
        Assert.That(entityContext2, Is.Not.Null);
        Assert.That(entityContext2.Id, Is.EqualTo(nameof(UpsertWithIncorrectEtagTest)));
        Assert.That(entityContext2.SysSleekflowCompanyId, Is.EqualTo(PartitionId));
        Assert.That(entityContext2.SysEntityTypeName, Is.EqualTo("SalesOrder"));

        var upsertCount = await entityContextRepository.UpsertAsync(
            new CrmHubEntityContext(
                id: nameof(UpsertWithIncorrectEtagTest),
                sysSleekflowCompanyId: PartitionId,
                sysEntityTypeName: "SalesOrder",
                ctxPhoneNumber: null,
                ctxEmail: "TestTest",
                ctxExternalId: null,
                ctxExternalIds: null,
                sysTypeName: "Entity",
                eTag: null),
            nameof(UpsertWithIncorrectEtagTest),
            eTag: entityContext2.ETag);
        Assert.That(upsertCount, Is.EqualTo(1));

        var upsertCount2 = await entityContextRepository.UpsertAsync(
            new CrmHubEntityContext(
                id: nameof(UpsertWithIncorrectEtagTest),
                sysSleekflowCompanyId: PartitionId,
                sysEntityTypeName: "SalesOrder",
                ctxPhoneNumber: null,
                ctxEmail: "TestTest3",
                ctxExternalId: null,
                ctxExternalIds: null,
                sysTypeName: "Entity",
                eTag: null),
            nameof(UpsertWithIncorrectEtagTest),
            eTag: entityContext2.ETag);
        Assert.That(upsertCount2, Is.EqualTo(0));
    }

    [Test]
    public async Task DeleteWithCorrectEtagTest()
    {
        var entityContextRepository = GetEntityContextRepository();

        var entityContext1 = await entityContextRepository.GetOrDefaultAsync(
            nameof(DeleteWithCorrectEtagTest),
            nameof(DeleteWithCorrectEtagTest));
        Assert.That(entityContext1, Is.Null);

        var entityContext2 = await entityContextRepository.CreateAndGetAsync(
            new CrmHubEntityContext(
                id: nameof(DeleteWithCorrectEtagTest),
                sysSleekflowCompanyId: PartitionId,
                sysEntityTypeName: "SalesOrder",
                ctxPhoneNumber: null,
                ctxEmail: null,
                ctxExternalId: null,
                ctxExternalIds: null,
                sysTypeName: "Entity",
                eTag: null),
            nameof(DeleteWithCorrectEtagTest));
        Assert.That(entityContext2, Is.Not.Null);
        Assert.That(entityContext2.Id, Is.EqualTo(nameof(DeleteWithCorrectEtagTest)));
        Assert.That(entityContext2.SysSleekflowCompanyId, Is.EqualTo(PartitionId));
        Assert.That(entityContext2.SysEntityTypeName, Is.EqualTo("SalesOrder"));

        var deleteCount = await entityContextRepository.DeleteAsync(
            nameof(DeleteWithCorrectEtagTest),
            nameof(DeleteWithCorrectEtagTest),
            eTag: entityContext2.ETag);
        Assert.That(deleteCount, Is.EqualTo(1));
    }

    [Test]
    public async Task DeleteWithIncorrectEtagTest()
    {
        var entityContextRepository = GetEntityContextRepository();

        var entityContext1 = await entityContextRepository.GetOrDefaultAsync(
            nameof(DeleteWithIncorrectEtagTest),
            nameof(DeleteWithIncorrectEtagTest));
        Assert.That(entityContext1, Is.Null);

        var entityContext2 = await entityContextRepository.CreateAndGetAsync(
            new CrmHubEntityContext(
                id: nameof(DeleteWithIncorrectEtagTest),
                sysSleekflowCompanyId: PartitionId,
                sysEntityTypeName: "SalesOrder",
                ctxPhoneNumber: null,
                ctxEmail: null,
                ctxExternalId: null,
                ctxExternalIds: null,
                sysTypeName: "Entity",
                eTag: null),
            nameof(DeleteWithIncorrectEtagTest));
        Assert.That(entityContext2, Is.Not.Null);
        Assert.That(entityContext2.Id, Is.EqualTo(nameof(DeleteWithIncorrectEtagTest)));
        Assert.That(entityContext2.SysSleekflowCompanyId, Is.EqualTo(PartitionId));
        Assert.That(entityContext2.SysEntityTypeName, Is.EqualTo("SalesOrder"));

        var upsertCount = await entityContextRepository.UpsertAsync(
            new CrmHubEntityContext(
                id: nameof(DeleteWithIncorrectEtagTest),
                sysSleekflowCompanyId: PartitionId,
                sysEntityTypeName: "SalesOrder",
                ctxPhoneNumber: null,
                ctxEmail: "TestTest",
                ctxExternalId: null,
                ctxExternalIds: null,
                sysTypeName: "Entity",
                eTag: null),
            nameof(DeleteWithIncorrectEtagTest));
        Assert.That(upsertCount, Is.EqualTo(1));

        var deleteCount = await entityContextRepository.DeleteAsync(
            nameof(DeleteWithIncorrectEtagTest),
            nameof(DeleteWithIncorrectEtagTest),
            eTag: entityContext2.ETag);
        Assert.That(deleteCount, Is.EqualTo(0));
    }
}