﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Serilog;
using Serilog.Enrichers.Span;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests;

public class EntityContextServiceTests
{
    private static readonly string PartitionId = $"TEST PARTITION-{Guid.NewGuid()}";

    public EntityContextServiceTests()
    {
        var loggerConfiguration = new LoggerConfiguration()
            .Enrich.WithSpan()
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .WriteTo.Async(
                wt => wt.Console(
                    outputTemplate:
                    "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
    }

    [SetUp]
    public void Setup()
    {
        // Method intentionally left empty.
    }

    [TearDown]
    public async Task TearDown()
    {
        var entityContextRepository = GetEntityContextRepository();

        var entityContexts = await entityContextRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sys_sleekflow_company_id = @sysSleekflowCompanyId")
                .WithParameter("@sysSleekflowCompanyId", PartitionId)
                .WithParameter("@sysEntityTypeName", "Contact"));

        foreach (var entityContext in entityContexts)
        {
            await entityContextRepository.DeleteAsync(
                entityContext.Id,
                entityContext.Id);
        }
    }

    public class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }

    public class MyIdService : IIdService
    {
        private static int i = 0;

        public string GetId(string typeName)
        {
            return "MyIdService-" + typeName + "-" + (i++);
        }

        public string GetId(string typeName, string parentId)
        {
            throw new NotImplementedException();
        }

        public (string TypeName, long SnowflakeId) DecodeId(string id)
        {
            throw new NotImplementedException();
        }
    }

    private EntityContextRepository GetEntityContextRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var entityContextRepository = new EntityContextRepository(
            NullLogger<BaseRepository<CrmHubEntityContext>>.Instance,
            serviceProvider);

        return entityContextRepository;
    }

    [Test]
    public async Task GetOrInitEntityContextAsyncTest()
    {
        var entityContextRepository = GetEntityContextRepository();

        var entityContextService = new EntityContextService(
            new MyIdService(),
            NullLogger<EntityContextService>.Instance,
            entityContextRepository);

        var entityContext = await entityContextService.GetOrInitEntityContextAsync(
            PartitionId,
            "Contact",
            "85261096623",
            "<EMAIL>",
            "Sleekflow",
            "1");

        Assert.That(entityContext, Is.Not.Null);
        Assert.That(entityContext.CtxExternalIds, Is.Not.Empty);
        Assert.That(entityContext.CtxExternalIds![0], Is.EqualTo("Sleekflow:1"));
        Assert.That(entityContext.CtxExternalId, Is.Null);

        var entityContext2 = await entityContextService.GetOrInitEntityContextAsync(
            PartitionId,
            "Contact",
            "85261096623",
            "<EMAIL>",
            "Hubspot",
            "1");

        Assert.That(entityContext2, Is.Not.Null);
        Assert.That(entityContext2.CtxExternalIds, Is.Not.Empty);
        Assert.That(entityContext2.CtxExternalIds![1], Is.EqualTo("Hubspot:1"));
        Assert.That(entityContext2.CtxExternalId, Is.Null);

        var entityContext3 = await entityContextService.GetOrInitEntityContextAsync(
            PartitionId,
            "Contact",
            "85261096623",
            "<EMAIL>",
            "Hubspot",
            "2");

        Assert.That(entityContext3, Is.Not.Null);
        Assert.That(entityContext3.CtxExternalId, Is.Not.Null);
        Assert.That(entityContext3.CtxExternalIds, Is.Empty);
        Assert.That(entityContext3.CtxExternalId, Is.EqualTo("Hubspot:2"));
    }
}