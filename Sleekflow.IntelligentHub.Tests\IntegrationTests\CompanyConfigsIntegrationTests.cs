using Microsoft.Extensions.DependencyInjection;
using Sleekflow.IntelligentHub.Triggers.Authorized.CompanyConfigs;
using Sleekflow.Outputs;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class CompanyConfigsIntegrationTests
{
    private readonly string _mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";

    [Test]
    public async Task CompanyConfigsTest()
    {
        using var scope = Application.Host.Services.CreateScope();

        Application.TestAuthorizationContext.SleekflowCompanyId = _mockCompanyId;

        // /CompanyConfigs/GetCompanyConfig
        var getCompanyConfigInput =
            new GetCompanyConfig.GetCompanyConfigInput();

        var getCompanyConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCompanyConfigInput)
                    .ToUrl("/authorized/CompanyConfigs/GetCompanyConfig");
            });

        var getCompanyConfigOutput =
            await getCompanyConfigScenarioResult.ReadAsJsonAsync<
                Output<GetCompanyConfig.GetCompanyConfigOutput>>();

        Assert.That(getCompanyConfigOutput, Is.Not.Null);
        Assert.That(getCompanyConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyConfigOutput!.Data.CompanyConfig, Is.Not.Null);
        Assert.That(getCompanyConfigOutput!.Data.CompanyConfig.Name, Is.EqualTo("Default Company"));
        Assert.That(
            getCompanyConfigOutput!.Data.CompanyConfig.BackgroundInformation,
            Is.EqualTo(string.Empty));
        Assert.That(getCompanyConfigOutput!.Data.CompanyConfig.PreferredLanguage, Is.EqualTo("eng"));

        // /CompanyConfigs/PatchCompanyConfig
        var patchCompanyConfigInput =
            new PatchCompanyConfig.PatchCompanyConfigInput(
                "Test Company",
                "Test Background Information",
                "chi");

        var patchCompanyConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(patchCompanyConfigInput)
                    .ToUrl("/authorized/CompanyConfigs/PatchCompanyConfig");
            });

        var patchCompanyConfigOutput =
            await patchCompanyConfigScenarioResult.ReadAsJsonAsync<
                Output<PatchCompanyConfig.PatchCompanyConfigOutput>>();

        Assert.That(patchCompanyConfigOutput, Is.Not.Null);
        Assert.That(patchCompanyConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(patchCompanyConfigOutput!.Data.CompanyConfig, Is.Not.Null);
        Assert.That(patchCompanyConfigOutput!.Data.CompanyConfig.Name, Is.EqualTo("Test Company"));
        Assert.That(
            patchCompanyConfigOutput!.Data.CompanyConfig.BackgroundInformation,
            Is.EqualTo("Test Background Information"));
        Assert.That(patchCompanyConfigOutput!.Data.CompanyConfig.PreferredLanguage, Is.EqualTo("chi"));

        // /CompanyConfigs/GetCompanyConfig
        var getCompanyConfigScenarioResult2 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCompanyConfigInput)
                    .ToUrl("/authorized/CompanyConfigs/GetCompanyConfig");
            });

        var getCompanyConfigOutput2 =
            await getCompanyConfigScenarioResult2.ReadAsJsonAsync<
                Output<GetCompanyConfig.GetCompanyConfigOutput>>();

        Assert.That(getCompanyConfigOutput2, Is.Not.Null);
        Assert.That(getCompanyConfigOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyConfigOutput2!.Data.CompanyConfig, Is.Not.Null);
        Assert.That(getCompanyConfigOutput2!.Data.CompanyConfig.Name, Is.EqualTo("Test Company"));
        Assert.That(
            getCompanyConfigOutput2!.Data.CompanyConfig.BackgroundInformation,
            Is.EqualTo("Test Background Information"));
        Assert.That(getCompanyConfigOutput2!.Data.CompanyConfig.PreferredLanguage, Is.EqualTo("chi"));

        // /CompanyConfigs/ClearCompanyConfig
        var clearCompanyConfigInput =
            new ClearCompanyConfig.ClearCompanyConfigInput();

        var clearCompanyConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(clearCompanyConfigInput)
                    .ToUrl("/authorized/CompanyConfigs/ClearCompanyConfig");
            });

        var clearCompanyConfigOutput =
            await clearCompanyConfigScenarioResult.ReadAsJsonAsync<
                Output<ClearCompanyConfig.ClearCompanyConfigOutput>>();

        Assert.That(clearCompanyConfigOutput, Is.Not.Null);
        Assert.That(clearCompanyConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(clearCompanyConfigOutput!.Data, Is.Not.Null);

        // /CompanyConfigs/GetCompanyConfig
        var getCompanyConfigScenarioResult3 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCompanyConfigInput)
                    .ToUrl("/authorized/CompanyConfigs/GetCompanyConfig");
            });

        var getCompanyConfigOutput3 =
            await getCompanyConfigScenarioResult3.ReadAsJsonAsync<
                Output<GetCompanyConfig.GetCompanyConfigOutput>>();

        Assert.That(getCompanyConfigOutput3, Is.Not.Null);
        Assert.That(getCompanyConfigOutput3!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getCompanyConfigOutput3!.Data.CompanyConfig, Is.Not.Null);
        Assert.That(getCompanyConfigOutput3!.Data.CompanyConfig.Name, Is.EqualTo("Default Company"));
        Assert.That(
            getCompanyConfigOutput3!.Data.CompanyConfig.BackgroundInformation,
            Is.EqualTo(string.Empty));
        Assert.That(getCompanyConfigOutput3!.Data.CompanyConfig.PreferredLanguage, Is.EqualTo("eng"));
    }
}