using MassTransit;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.EntityEvents;
using Sleekflow.CrmHub.Metadatas;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Models.EntityEvents;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Webhooks.Payloads;
using Sleekflow.Utils;
using Sleekflow.Webhooks;

namespace Sleekflow.CrmHub.Events;

public class OnObjectPersistedEventConsumerDefinition : ConsumerDefinition<OnObjectPersistedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnObjectPersistedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnObjectPersistedEventConsumer : IConsumer<OnObjectPersistedEvent>
{
    private readonly IWebhookService _webhookService;
    private readonly IMetadataRepository _metadataRepository;

    public OnObjectPersistedEventConsumer(
        IWebhookService webhookService,
        IMetadataRepository metadataRepository)
    {
        _webhookService = webhookService;
        _metadataRepository = metadataRepository;
    }

    public async Task Consume(ConsumeContext<OnObjectPersistedEvent> context)
    {
        var @event = context.Message;
        var cancellationToken = context.CancellationToken;

        var dict = @event.Object;
        if (dict.ContainsKey("id") == false
            || dict["id"] == null
            || dict.ContainsKey("sys_sleekflow_company_id") == false
            || dict["sys_sleekflow_company_id"] == null
            || dict.ContainsKey("sys_type_name") == false
            || dict["sys_type_name"] == null)
        {
            return;
        }

        var id = (string) dict["id"]!;
        var sleekflowCompanyId = (string) dict["sys_sleekflow_company_id"]!;
        var entityTypeName = @event.EntityTypeName;
        var sysTypeName = dict["sys_type_name"];

        switch (sysTypeName)
        {
            case "Entity":
            {
                const string subscribedEventTypeName = "OnEntityCreated";

                var sysVersion = Convert.ToInt64(dict.GetValueOrDefault(CrmHubEntity.PropertyNameSysVersion, 0)!);
                if (sysVersion != 1)
                {
                    break;
                }

                var payload = new OnEntityCreatedWebhookPayload(
                    id,
                    DateTimeOffset.UtcNow,
                    sleekflowCompanyId,
                    entityTypeName,
                    EntityService.Sanitize(dict),
                    eventTypeName: subscribedEventTypeName);

                await _webhookService.SendWebhooksAsync(
                    sleekflowCompanyId,
                    entityTypeName,
                    subscribedEventTypeName,
                    payload,
                    cancellationToken);

                break;
            }

            case "EntityEvent":
            {
                const string subscribedEventTypeName = "OnEntityFieldsChanged";

                var changeEntries = ((JArray) dict[EntityEvent.PropertyNameChangeEntries]!)
                    .ToObject<List<EntityEventChangeEntry>>()!;

                await UpsertMetadata(entityTypeName, sleekflowCompanyId, changeEntries, cancellationToken);

                if (dict[EntityEvent.PropertyNameEntityEventTypeName] is EntityEventTypeNames.New)
                {
                    return;
                }

                var onEntityFieldsChangedWebhookPayloadChangeEntries = changeEntries
                    .Select(
                        ce => new OnEntityFieldsChangedWebhookPayloadChangeEntry(
                            ce.Name,
                            ce.FromValue,
                            ce.ToValue))

                    // HOTFIX Performance issue on diff
                    .Where(ce => ce.Name != "sleekflow:UpdatedAt")
                    .ToList();

                if (onEntityFieldsChangedWebhookPayloadChangeEntries.Count == 0)
                {
                    return;
                }

                var payload = new OnEntityFieldsChangedWebhookPayload(
                    id,
                    onEntityFieldsChangedWebhookPayloadChangeEntries,
                    (DateTimeOffset) dict[EntityEvent.PropertyNameCreatedTime]!.ToDateTimeOffset()!,
                    (string) dict[EntityEvent.PropertyNameProviderName]!,
                    sleekflowCompanyId,
                    (string) dict[EntityEvent.PropertyNameEntityId]!,
                    entityTypeName: entityTypeName,
                    eventTypeName: subscribedEventTypeName);

                await _webhookService.SendWebhooksAsync(
                    sleekflowCompanyId,
                    entityTypeName,
                    subscribedEventTypeName,
                    payload,
                    cancellationToken);

                break;
            }
        }
    }

    private async Task UpsertMetadata(
        string entityTypeName,
        string sleekflowCompanyId,
        List<EntityEventChangeEntry> sanitizedEntries,
        CancellationToken cancellationToken)
    {
        var dict = new Dictionary<string, object?>();

        foreach (var changeEntry in sanitizedEntries)
        {
            dict[changeEntry.Name] = changeEntry.ToValue;
        }

        await _metadataRepository.ExecuteUpsertMetadataStoredProcedureAsync(
            dict,
            sleekflowCompanyId,
            entityTypeName,
            cancellationToken);
    }
}