﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class EditFileDocumentChunks
    : ITrigger<EditFileDocumentChunks.EditFileDocumentChunksInput, EditFileDocumentChunks.EditFileDocumentChunksOutput>
{
    private readonly IFileDocumentChunkService _fileDocumentChunkService;

    public EditFileDocumentChunks(IFileDocumentChunkService fileDocumentChunkService)
    {
        _fileDocumentChunkService = fileDocumentChunkService;
    }

    public class EditFileDocumentChunksInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("editing_chunks")]
        public List<EditingChunkDto> EditingChunks { get; set; }

        [JsonConstructor]
        public EditFileDocumentChunksInput(
            string sleekflowCompanyId,
            string documentId,
            List<EditingChunkDto> editingChunks)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            EditingChunks = editingChunks;
        }
    }

    public class EditFileDocumentChunksOutput
    {
        [JsonProperty("edited_chunks")]
        public List<EditingChunkDto> EditedChunks { get; set; }

        [JsonConstructor]
        public EditFileDocumentChunksOutput(List<EditingChunkDto> editedChunks)
        {
            EditedChunks = editedChunks;
        }
    }

    public async Task<EditFileDocumentChunksOutput> F(EditFileDocumentChunksInput editFileDocumentChunksInput)
    {
        var editedChunks = new List<EditingChunkDto>();

        foreach (var editingChunk in editFileDocumentChunksInput.EditingChunks)
        {
            var editedChunk = await _fileDocumentChunkService.PatchAndGetFileDocumentChunkAsync(
                editFileDocumentChunksInput.SleekflowCompanyId,
                editFileDocumentChunksInput.DocumentId,
                editingChunk);
            editedChunks.Add(
                new EditingChunkDto(editedChunk.Id, editedChunk.Content, editedChunk.Categories, editedChunk.Metadata));
        }

        return new EditFileDocumentChunksOutput(editedChunks);
    }
}