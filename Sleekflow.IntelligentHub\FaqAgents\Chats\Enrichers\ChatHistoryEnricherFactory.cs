using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Factory interface for creating chat history enrichers.
/// </summary>
public interface IChatHistoryEnricherFactory
{
    /// <summary>
    /// Creates enrichers based on company configuration.
    /// </summary>
    /// <returns>List of chat history enrichers.</returns>
    List<IChatHistoryEnricher> CreateEnrichers(
        List<EnricherConfig> enricherConfigs,
        Kernel kernel);
}

/// <summary>
/// Factory for creating chat history enrichers based on configuration.
/// </summary>
public class ChatHistoryEnricherFactory : IChatHistoryEnricherFactory, IScopedService
{
    private readonly ILogger<ChatHistoryEnricherFactory> _logger;
    private readonly ILoggerFactory _loggerFactory;
    private readonly IHubspotPlugin _hubspotPlugin;
    private readonly IPlanTieringPlugin _planTieringPlugin;

    public ChatHistoryEnricherFactory(
        ILogger<ChatHistoryEnricherFactory> logger,
        ILoggerFactory loggerFactory,
        IHubspotPlugin hubspotPlugin,
        IPlanTieringPlugin planTieringPlugin)
    {
        _logger = logger;
        _loggerFactory = loggerFactory;
        _hubspotPlugin = hubspotPlugin;
        _planTieringPlugin = planTieringPlugin;
    }

    /// <summary>
    /// Creates enrichers based on company configuration.
    /// </summary>
    /// <returns>List of chat history enrichers.</returns>
    public List<IChatHistoryEnricher> CreateEnrichers(
        List<EnricherConfig> enricherConfigs,
        Kernel kernel)
    {
        var enrichers = new List<IChatHistoryEnricher>();

        if (!enricherConfigs.Any())
        {
            _logger.LogInformation("No enricher configs found in company config");
            return enrichers;
        }

        foreach (var config in enricherConfigs.Where(c => c.IsEnabled))
        {
            try
            {
                switch (config.Type.ToLowerInvariant())
                {
                    case "hubspot":
                        var hubspotEnricherLogger = _loggerFactory.CreateLogger<HubspotEnricher>();

                        enrichers.Add(new HubspotEnricher(hubspotEnricherLogger, _hubspotPlugin, config.Parameters));
                        _logger.LogInformation("Created Hubspot enricher");
                        break;

                    case "contact_properties":
                        var contactPropertiesEnricherLogger = _loggerFactory.CreateLogger<ContactPropertiesEnricher>();

                        enrichers.Add(
                            new ContactPropertiesEnricher(contactPropertiesEnricherLogger, config.Parameters));
                        _logger.LogInformation("Created ContactProperties enricher");
                        break;

                    case "plan_tier":
                        var planTierEnricherLogger = _loggerFactory.CreateLogger<PlanTierEnricher>();

                        enrichers.Add(
                            new PlanTierEnricher(planTierEnricherLogger, _planTieringPlugin, config.Parameters));
                        _logger.LogInformation("Created PlanTier enricher");
                        break;

                    // Add cases for other enricher types
                    default:
                        _logger.LogWarning("Unknown enricher type: {Type}", config.Type);
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating enricher of type {Type}", config.Type);
            }
        }

        return enrichers;
    }
}