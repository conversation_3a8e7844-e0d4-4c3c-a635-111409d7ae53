using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Invoices;

public class BusinessBalanceInvoice
{
    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("facebook_business_name")]
    public string? FacebookBusinessName { get; set; }

    [JsonProperty("invoice_pdf")]
    private string InvoicePdf { get; set; }

    [JsonProperty("status")]
    private string InvoiceStatus { get; set; }

    [JsonProperty("created_at")]
    private long CreatedAt { get; set; }

    [JsonProperty("pay_amount")]
    private Money PayAmount { get; set; }

    [JsonProperty("description")]
    private string Description { get; set; }

    [JsonConstructor]
    public BusinessBalanceInvoice(
        string invoicePdf,
        string invoiceStatus,
        long createdAt,
        Money payAmount,
        string description,
        string facebookBusinessId,
        string? facebookBusinessName)
    {
        InvoicePdf = invoicePdf;
        InvoiceStatus = invoiceStatus;
        CreatedAt = createdAt;
        PayAmount = payAmount;
        Description = description;
        FacebookBusinessId = facebookBusinessId;
        FacebookBusinessName = facebookBusinessName;
    }
}