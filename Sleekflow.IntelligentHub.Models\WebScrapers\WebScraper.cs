﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.WebScrapers;

[<PERSON>sol<PERSON>(typeof(IIntelligentHubDbResolver))]
[DatabaseId("intelligenthubdb")]
[ContainerId("web_scraper")]
public class WebScraper : Entity, IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyName = "code")]
    [StringLength(8, MinimumLength = 8)]
    public string Code { get; set; }

    [JsonProperty(PropertyName = "web_scraper_tasks")]
    public List<WebScraperTask>? WebScraperTasks { get; set; }

    [JsonConstructor]
    public WebScraper(
        string id,
        string sleekflowCompanyId,
        string code,
        List<WebScraperTask>? webScraperTasks)
        : base(id, SysTypeNames.WebScraper)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Code = code;
        WebScraperTasks = webScraperTasks;
    }
}