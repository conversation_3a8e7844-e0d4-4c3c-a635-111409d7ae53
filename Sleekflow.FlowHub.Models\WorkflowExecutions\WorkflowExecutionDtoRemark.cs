using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionDtoRemark
{
    [JsonProperty("remark")]
    public string Remark { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("step_id")]
    public string StepId { get; set; }

    [JsonProperty("step_node_id")]
    public string? StepNodeId { get; set; }

    [JsonConstructor]
    public WorkflowExecutionDtoRemark(
        string remark,
        string type,
        string stepId,
        string? stepNodeId)
    {
        Remark = remark;
        Type = type;
        StepId = stepId;
        StepNodeId = stepNodeId;
    }
}