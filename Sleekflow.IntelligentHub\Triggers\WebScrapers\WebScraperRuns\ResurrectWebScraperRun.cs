﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperRuns;

[TriggerGroup(ControllerNames.WebScraperRuns)]
public class ResurrectWebScraperRun : ITrigger<ResurrectWebScraperRun.ResurrectWebScraperRunInput, ResurrectWebScraperRun.ResurrectWebScraperRunOutput>
{
    private readonly IWebScraperService _webScraperService;

    public ResurrectWebScraperRun(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class ResurrectWebScraperRunInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperRun.PropertyNameApifyRunId)]
        public string ApifyRunId { get; set; }

        [JsonConstructor]
        public ResurrectWebScraperRunInput(string sleekflowCompanyId, string apifyRunId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyRunId = apifyRunId;
        }
    }

    public class ResurrectWebScraperRunOutput
    {
        [JsonProperty(WebScraperRun.PropertyNameWebScraperRun)]
        public WebScraperRun WebScraperRun { get; set; }

        [JsonConstructor]
        public ResurrectWebScraperRunOutput(WebScraperRun webScraperRun)
        {
            WebScraperRun = webScraperRun;
        }
    }

    public async Task<ResurrectWebScraperRunOutput> F(ResurrectWebScraperRunInput resurrectWebScraperRunInput)
    {
        var run = await _webScraperService.ResurrectRunAsync(
            resurrectWebScraperRunInput.SleekflowCompanyId,
            resurrectWebScraperRunInput.ApifyRunId);

        return new ResurrectWebScraperRunOutput(run);
    }
}