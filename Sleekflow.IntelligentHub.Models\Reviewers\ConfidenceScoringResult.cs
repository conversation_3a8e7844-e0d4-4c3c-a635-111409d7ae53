using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Reviewers;

public class ConfidenceScoringResult
{
    [JsonProperty("message_consistency")]
    public ScoringCriteria MessageConsistency { get; set; }

    [JsonProperty("ambiguity_detection")]
    public ScoringCriteria AmbiguityDetection { get; set; }

    [JsonProperty("tone_and_style_mismatch")]
    public ScoringCriteria ToneAndStyleMismatch { get; set; }

    [JsonProperty("relevancy")]
    public ScoringCriteria Relevancy { get; set; }

    [JsonProperty("temporal_relevance")]
    public ScoringCriteria TemporalRelevance { get; set; }

    [JsonProperty("contradiction")]
    public ScoringCriteria Contradiction { get; set; }

    [JsonProperty("escalation_detection")]
    public ScoringCriteria EscalationDetection { get; set; }

    [JsonConstructor]
    public ConfidenceScoringResult(
        ScoringCriteria messageConsistency,
        ScoringCriteria ambiguityDetection,
        ScoringCriteria toneAndStyleMismatch,
        ScoringCriteria relevancy,
        ScoringCriteria temporalRelevance,
        ScoringCriteria contradiction,
        ScoringCriteria escalationDetection)
    {
        MessageConsistency = messageConsistency;
        AmbiguityDetection = ambiguityDetection;
        ToneAndStyleMismatch = toneAndStyleMismatch;
        Relevancy = relevancy;
        TemporalRelevance = temporalRelevance;
        Contradiction = contradiction;
        EscalationDetection = escalationDetection;
    }

    public void Deconstruct(
        out ScoringCriteria messageConsistency,
        out ScoringCriteria ambiguityDetection,
        out ScoringCriteria toneAndStyleMismatch,
        out ScoringCriteria relevancy,
        out ScoringCriteria temporalRelevance,
        out ScoringCriteria contradiction,
        out ScoringCriteria escalationDetection)
    {
        messageConsistency = MessageConsistency;
        ambiguityDetection = AmbiguityDetection;
        toneAndStyleMismatch = ToneAndStyleMismatch;
        relevancy = Relevancy;
        temporalRelevance = TemporalRelevance;
        contradiction = Contradiction;
        escalationDetection = EscalationDetection;
    }
}