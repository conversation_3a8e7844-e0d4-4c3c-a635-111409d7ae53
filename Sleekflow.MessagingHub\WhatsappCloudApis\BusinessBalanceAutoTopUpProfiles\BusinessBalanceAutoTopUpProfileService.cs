using System.Globalization;
using Microsoft.Azure.Cosmos;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Payments;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUp;
using Sleekflow.Persistence;
using Stripe.Checkout;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;

public interface IBusinessBalanceAutoTopUpProfileService
{
    Task<BusinessBalanceAutoTopUpProfile?> GetOrDefaultBusinessBalanceAutoTopUpProfileAsync(
        string id,
        string facebookBusinessId);

    Task<BusinessBalanceAutoTopUpProfile?> GetWithFacebookBusinessIdAsync(string facebookBusinessId);

    Task<BusinessBalanceAutoTopUpProfile> UpsertBusinessBalanceAutoTopUpProfileAsync(
        string facebookBusinessId,
        string? customerId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<string> GenerateBusinessBalanceAutoTopUpProfilePaymentLinkAsync(
        string sleekflowCompanyId,
        string facebookBusinessId,
        string creditedBy,
        string creditedByDisplayName,
        string redirectToUrl,
        string phoneNumber,
        StripeWhatsAppCreditTopUpPlan stripeWhatsAppCreditTopUpPlan);

    Task UpdateBusinessBalanceAutoTopUpProfileCustomerIdAsync(string facebookBusinessId, string customerId);
}

public class BusinessBalanceAutoTopUpProfileService : IBusinessBalanceAutoTopUpProfileService, ISingletonService
{
    private readonly IBusinessBalanceAutoTopUpProfileRepository _businessBalanceAutoTopUpProfileRepository;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IStripeClient _stripeClient;
    private readonly IIdService _idService;
    private readonly ILogger<IBusinessBalanceAutoTopUpProfileService> _logger;
    private readonly IBusinessBalanceAutoTopUpService _businessBalanceAutoTopUpService;

    public BusinessBalanceAutoTopUpProfileService(
        IBusinessBalanceAutoTopUpProfileRepository businessBalanceAutoTopUpProfileRepository,
        IIdService idService,
        IStripeClient stripeClient,
        IBusinessBalanceService businessBalanceService,
        ILogger<BusinessBalanceAutoTopUpProfileService> logger,
        IBusinessBalanceAutoTopUpService businessBalanceAutoTopUpService)
    {
        _businessBalanceAutoTopUpProfileRepository = businessBalanceAutoTopUpProfileRepository;
        _idService = idService;
        _stripeClient = stripeClient;
        _businessBalanceService = businessBalanceService;
        _logger = logger;
        _businessBalanceAutoTopUpService = businessBalanceAutoTopUpService;
    }

    public async Task<BusinessBalanceAutoTopUpProfile?> GetOrDefaultBusinessBalanceAutoTopUpProfileAsync(
        string id,
        string facebookBusinessId)
    {
        return await _businessBalanceAutoTopUpProfileRepository.GetOrDefaultAsync(id, facebookBusinessId);
    }

    public async Task<BusinessBalanceAutoTopUpProfile?> GetWithFacebookBusinessIdAsync(string facebookBusinessId)
    {
        var businessBalanceAutoTopUpProfiles = await _businessBalanceAutoTopUpProfileRepository
            .GetObjectsAsync(
                new QueryDefinition(
                        "SELECT * " +
                        "FROM c " +
                        "WHERE c.facebook_business_id = @facebookBusinessId " +
                        "ORDER BY c._ts DESC")
                    .WithParameter("@facebookBusinessId", facebookBusinessId));

        return businessBalanceAutoTopUpProfiles.FirstOrDefault();
    }

    public async Task<BusinessBalanceAutoTopUpProfile> UpsertBusinessBalanceAutoTopUpProfileAsync(
        string facebookBusinessId,
        string? customerId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            _logger.LogError(
                "Unable to locate business account balance object with {FacebookBusinessId}",
                facebookBusinessId);

            throw new SfInternalErrorException(
                $"Unable to locate business account balance object with {facebookBusinessId}");
        }

        var upsertBusinessBalanceAutoTopUpProfile = await GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (upsertBusinessBalanceAutoTopUpProfile is null)
        {
            upsertBusinessBalanceAutoTopUpProfile = await CreateBusinessBalanceAutoTopUpProfileAsync(
                facebookBusinessId,
                businessBalance,
                customerId,
                minimumBalance,
                autoTopUpPlan,
                isAutoTopUpEnabled,
                sleekflowCompanyId,
                sleekflowStaff);
        }
        else
        {
            upsertBusinessBalanceAutoTopUpProfile.MinimumBalance = minimumBalance;
            upsertBusinessBalanceAutoTopUpProfile.AutoTopUpPlan = autoTopUpPlan;
            upsertBusinessBalanceAutoTopUpProfile.SleekflowCompanyId = sleekflowCompanyId;
            upsertBusinessBalanceAutoTopUpProfile.UpdatedAt = DateTimeOffset.UtcNow;
            upsertBusinessBalanceAutoTopUpProfile.UpdatedBy = sleekflowStaff;
            upsertBusinessBalanceAutoTopUpProfile.IsAutoTopUpEnabled = upsertBusinessBalanceAutoTopUpProfile.CustomerId is not null && isAutoTopUpEnabled;

            var performAutoTopUp =
                _businessBalanceAutoTopUpService.ShouldPerformAutoTopUp(
                    facebookBusinessId,
                    businessBalance,
                    upsertBusinessBalanceAutoTopUpProfile);

            if (performAutoTopUp)
            {
                await _businessBalanceAutoTopUpService.PublishAutoTopUpEvent(
                    facebookBusinessId,
                    businessBalance.Id,
                    upsertBusinessBalanceAutoTopUpProfile);
            }
        }

        return await _businessBalanceAutoTopUpProfileRepository.UpsertAndGetAsync(
            upsertBusinessBalanceAutoTopUpProfile,
            upsertBusinessBalanceAutoTopUpProfile.FacebookBusinessId);
    }

    private async Task<BusinessBalanceAutoTopUpProfile> CreateBusinessBalanceAutoTopUpProfileAsync(
        string facebookBusinessId,
        BusinessBalance businessBalance,
        string? customerId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        if (customerId is null)
        {
            isAutoTopUpEnabled = false;
        }

        var recordStatus = new List<string>
        {
            RecordStatuses.Active
        };

        var upsertBusinessBalanceAutoTopUpProfile = new BusinessBalanceAutoTopUpProfile(
            _idService.GetId("BusinessBalanceAutoTopUpProfile"),
            facebookBusinessId,
            customerId,
            minimumBalance,
            autoTopUpPlan,
            isAutoTopUpEnabled,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            sleekflowCompanyId,
            sleekflowStaff,
            sleekflowStaff,
            recordStatus);

        if (customerId is null)
        {
            return upsertBusinessBalanceAutoTopUpProfile;
        }

        var performAutoTopUp =
            _businessBalanceAutoTopUpService.ShouldPerformAutoTopUp(
                facebookBusinessId,
                businessBalance,
                upsertBusinessBalanceAutoTopUpProfile);

        if (performAutoTopUp)
        {
            await _businessBalanceAutoTopUpService.PublishAutoTopUpEvent(
                facebookBusinessId,
                businessBalance.Id,
                upsertBusinessBalanceAutoTopUpProfile);
        }

        return upsertBusinessBalanceAutoTopUpProfile;
    }

    public async Task<string> GenerateBusinessBalanceAutoTopUpProfilePaymentLinkAsync(
        string sleekflowCompanyId,
        string facebookBusinessId,
        string creditedBy,
        string creditedByDisplayName,
        string redirectToUrl,
        string phoneNumber,
        StripeWhatsAppCreditTopUpPlan stripeWhatsAppCreditTopUpPlan)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new NullReferenceException();
        }

        var businessBalanceId = businessBalance.Id;

        var lineItem = new List<SessionLineItemOptions>
        {
            new ()
            {
                Price = stripeWhatsAppCreditTopUpPlan.Id, Quantity = 1
            }
        };

        var metadata = new Dictionary<string, string>
        {
            {
                "source", "messaging-hub"
            },
            {
                "sleekflow_company_id", sleekflowCompanyId
            },
            {
                "facebook_business_id", facebookBusinessId
            },
            {
                "business_balance_id", businessBalanceId
            },
            {
                "type", TopUpTypes.WhatsappCloudApiAutoTopUp
            },
            {
                "credited_by", creditedBy
            },
            {
                "credited_by_display_name", creditedByDisplayName
            },
            {
                "whatsapp_cloud_api_top_up_plan_id", stripeWhatsAppCreditTopUpPlan.Id
            },
            {
                "whatsapp_cloud_api_top_up_plan_amount", stripeWhatsAppCreditTopUpPlan.Price.Amount.ToString(CultureInfo.InvariantCulture)
            },
            {
                "whatsapp_cloud_api_top_up_plan_currency", stripeWhatsAppCreditTopUpPlan.Price.CurrencyIsoCode
            }
        };

        const bool isSetupFutureUsage = true;

        var successUrl =
            BuildAutoTopUpStripeRedirectToUrl(redirectToUrl, "success", "autoTopUp", phoneNumber);

        var cancelUrl =
            BuildAutoTopUpStripeRedirectToUrl(redirectToUrl, "cancel", "autoTopUp", phoneNumber);

        return (await _stripeClient.GeneratePaymentLink(
            null,
            successUrl,
            cancelUrl,
            lineItem,
            metadata,
            isSetupFutureUsage)).Url;
    }

    public async Task UpdateBusinessBalanceAutoTopUpProfileCustomerIdAsync(string facebookBusinessId, string customerId)
    {
        var businessBalanceAutoTopUpProfile = await GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalanceAutoTopUpProfile is null)
        {
            throw new SfInternalErrorException(
                $"Business Balance Auto Top Up Profile not found! facebook business id:{facebookBusinessId}");
        }

        // It is a initial set up for auto top up profile with no customerId from Stripe linked
        var initialAutoTopUpProfileCustomerIdSetUp =
            IsInitialAutoTopUpProfileCustomerIdSetup(businessBalanceAutoTopUpProfile);

        if (initialAutoTopUpProfileCustomerIdSetUp)
        {
            // we will automatically enable the auto top up profile after a customerId is linked with the auto top up profile
            businessBalanceAutoTopUpProfile.IsAutoTopUpEnabled = true;
            businessBalanceAutoTopUpProfile.CustomerId = customerId;

            await _businessBalanceAutoTopUpProfileRepository.UpsertAndGetAsync(
                businessBalanceAutoTopUpProfile,
                businessBalanceAutoTopUpProfile.FacebookBusinessId);

            return;
        }

        if (businessBalanceAutoTopUpProfile.CustomerId != customerId)
        {
            businessBalanceAutoTopUpProfile.CustomerId = customerId;

            await _businessBalanceAutoTopUpProfileRepository.UpsertAndGetAsync(
                businessBalanceAutoTopUpProfile,
                businessBalanceAutoTopUpProfile.FacebookBusinessId);
        }
    }

    private static bool IsInitialAutoTopUpProfileCustomerIdSetup(BusinessBalanceAutoTopUpProfile businessBalanceAutoTopUpProfile)
    {
        return businessBalanceAutoTopUpProfile is { IsAutoTopUpEnabled: false, CustomerId: null };
    }

    private static string BuildAutoTopUpStripeRedirectToUrl(
        string baseUrl,
        string action,
        string info,
        string? phoneNumber = null)
    {
        var builder = new UriBuilder(baseUrl);

        // If baseUrl contain origin only, will provide a default path.
        if(builder.Path == "/")
        {
            builder.Path = $"/stripe/{action}";
        }

        var queryParams = new List<string>
        {
            $"info={info}",
        };

        if(phoneNumber != null)
        {
            queryParams.Add($"phoneNumber={phoneNumber}");
        }

        builder.Query = builder.Query == string.Empty
            ? string.Join("&", queryParams)
            : $"{builder.Query}&{string.Join("&", queryParams)}";

        return builder.ToString();
    }
}