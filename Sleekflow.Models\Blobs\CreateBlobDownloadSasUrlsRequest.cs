using Newtonsoft.Json;

namespace Sleekflow.Models.Blobs;

public class CreateBlobDownloadSasUrlsRequest
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("blob_names")]
    public List<string> BlobNames { get; set; }

    [JsonProperty("blob_type")]
    public string BlobType { get; set; }

    [JsonProperty("compress_properties")]
    public CompressProperties? CompressProperties { get; set; }

    [JsonProperty("reformat_properties")]
    public ReformatProperties? ReformatProperties { get; set; }

    [JsonConstructor]
    public CreateBlobDownloadSasUrlsRequest(
        string sleekflowCompanyId,
        List<string> blobNames,
        string blobType,
        CompressProperties? compressProperties,
        ReformatProperties? reformatProperties)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        BlobNames = blobNames;
        BlobType = blobType;
        CompressProperties = compressProperties;
        ReformatProperties = reformatProperties;
    }
}

public class CompressProperties
{
    [JsonProperty("quality")]
    public int Quality { get; set; }

    [JsonConstructor]
    public CompressProperties(int quality)
    {
        Quality = quality;
    }
}

public class ReformatProperties
{
    [JsonProperty("format")]
    public string Format { get; set; }

    [JsonConstructor]
    public ReformatProperties(string format)
    {
        Format = format;
    }
}