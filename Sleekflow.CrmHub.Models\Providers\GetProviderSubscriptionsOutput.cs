using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Providers;

public class ProviderSubscriptionDto : IHasSleekflowCompanyId
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("interval")]
    public int Interval { get; set; }

    [JsonProperty("is_flows_based")]
    public bool? IsFlowsBased { get; set; }

    [JsonProperty("connection_id")]
    public string? ConnectionId { get; set; }

    [JsonProperty("typed_ids")]
    public List<TypedId>? TypedIds { get; set; }

    [JsonConstructor]
    public ProviderSubscriptionDto(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        int interval,
        bool? isFlowsBased,
        string? connectionId,
        List<TypedId>? typedIds)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        EntityTypeName = entityTypeName;
        Interval = interval;
        IsFlowsBased = isFlowsBased;
        ConnectionId = connectionId;
        TypedIds = typedIds;
    }
}

public class GetProviderSubscriptionsOutput
{
    [JsonProperty("subscriptions")]
    public List<ProviderSubscriptionDto> Subscriptions { get; set; }

    [JsonConstructor]
    public GetProviderSubscriptionsOutput(
        List<ProviderSubscriptionDto> subscriptions)
    {
        Subscriptions = subscriptions;
    }
}