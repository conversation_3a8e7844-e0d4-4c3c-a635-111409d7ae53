﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowGroupDeletedEventConsumerDefinition
    : ConsumerDefinition<OnWorkflowGroupDeletedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowGroupDeletedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowGroupDeletedEventConsumer : IConsumer<OnWorkflowGroupDeletedEvent>
{
    private readonly IWorkflowService _workflowService;

    public OnWorkflowGroupDeletedEventConsumer(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public Task Consume(ConsumeContext<OnWorkflowGroupDeletedEvent> context)
    {
        var @event = context.Message;

        return _workflowService.UnsetWorkflowGroupIdAsync(
            @event.SleekflowCompanyId,
            @event.WorkflowGroupId);
    }
}