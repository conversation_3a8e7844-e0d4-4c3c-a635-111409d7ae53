using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Images;

public class ImageDto : IValidatableObject
{
    [StringLength(4096, MinimumLength = 1)]
    [JsonProperty("image_url")]
    public string? ImageUrl { get; set; }

    [StringLength(128, MinimumLength = 1)]
    [JsonProperty("blob_name")]
    public string? BlobName { get; set; }

    [JsonConstructor]
    public ImageDto(
        string? imageUrl,
        string? blobName)
    {
        ImageUrl = imageUrl;
        BlobName = blobName;
    }

    public ImageDto(
        Image image)
        : this(
            image.ImageUrl,
            image.BlobName)
    {
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if ((ImageUrl != null || BlobName != null)
            && (ImageUrl == null || BlobName == null))
        {
            return new List<ValidationResult>();
        }

        if (ImageUrl != null && ImageUrl.Contains("blob.core.windows.net"))
        {
            return new List<ValidationResult>();
        }

        var results = new List<ValidationResult>
        {
            new (
                "Either ImageUrl and BlobName should be null",
                new List<string>
                {
                    "ImageUrl", "BlobName"
                })
        };

        return results;
    }
}