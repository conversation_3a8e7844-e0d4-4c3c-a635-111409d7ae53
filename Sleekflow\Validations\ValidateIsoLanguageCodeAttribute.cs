using System.ComponentModel.DataAnnotations;
using Sleekflow.Utils;

namespace Sleekflow.Validations;

public class ValidateIsoLanguageCodeAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null)
        {
            throw new ArgumentException($"Invalid field input {value}");
        }

        if (value is not string str)
        {
            return ValidationResult.Success;
        }

        var cultureInfo = CultureUtils.GetCultureInfoByLanguageIsoCode(str);
        if (cultureInfo == null)
        {
            return new ValidationResult(
                "Language Iso Code must be a valid ISO 639-1 language code",
                new string[]
                {
                    validationContext.MemberName!
                });
        }

        return ValidationResult.Success;
    }
}