namespace Sleekflow.IntelligentHub.Models.Constants;

/// <summary>
/// Constants for kernel data dictionary keys used across the application.
/// </summary>
public static class KernelDataKeys
{
    public const string GROUP_CHAT_ID = "GROUP_CHAT_ID";
    public const string SLEEKFLOW_COMPANY_ID = "SLEEKFLOW_COMPANY_ID";
    public const string AGENT_ID = "AGENT_ID";
    public const string CONTACT_ID = "CONTACT_ID";
    public const string STATE_ID = "STATE_ID";

    // This is a special property for `SleekFlow`.
    public const string DETERMINE_PLAN_TIER_RESPONSE = "DETERMINE_PLAN_TIER_RESPONSE";

    /// <summary>
    /// Key for the LeadNurturing tools config.
    /// </summary>
    public const string LEAD_NURTURING_TOOLS_CONFIG = "lead_nurturing_tools_config";

    /// <summary>
    /// Key for the all tools config.
    /// </summary>
    public const string TOOLS_CONFIG = "tools_config";

    public const string WEB_SEARCH_PLUGIN_CONFIG = "WEB_SEARCH_PLUGIN_CONFIG";
    public const string STATIC_SEARCH_PLUGIN_CONFIG = "STATIC_SEARCH_PLUGIN_CONFIG";
}