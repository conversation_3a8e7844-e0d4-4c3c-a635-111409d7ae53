using Alba;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class HealthzTests
{
    [Test]
    public async Task HealthzTest()
    {
        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/healthz/liveness");
                _.StatusCodeShouldBeOk();
            });

        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/healthz/readiness");
                _.StatusCodeShouldBeOk();
            });

        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/healthz/startup");
                _.StatusCodeShouldBeOk();
            });
    }
}