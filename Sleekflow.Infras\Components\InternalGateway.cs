using Pulumi;
using Pulumi.AzureNative.ContainerRegistry;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using ConfigurationResponse = Pulumi.AzureNative.App.V20240301.Outputs.ConfigurationResponse;
using Docker = Pulumi.Docker;
using SecretArgs = Pulumi.AzureNative.App.V20240301.Inputs.SecretArgs;

namespace Sleekflow.Infras;

public class InternalGateway
{
    private readonly Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly MyConfig _myConfig;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;

    public InternalGateway(
        Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        MyConfig myConfig,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _myConfig = myConfig;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
    }

    public Dictionary<string, (App.ContainerApp ContainerApp, Output<string> ApplicationUrl)> InitInternalGateway()
    {
        const string igwImage = "sleekflow-igw";
        var image = GetKrakenDImage(igwImage);
        var apps = new Dictionary<string, (App.ContainerApp, Output<string>)>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            // Only enabled the internal gateway for east asia
            if (managedEnvAndAppsTuple.LocationName != LocationNames.EastAsia)
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName("igw");

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = true,
                            TargetPort = 8080,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            }
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 3 : 1,
                            MaxReplicas = 80,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "100"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-igw-app",
                                Image = image.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 1, Memory = "2.0Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/__health", Port = 8080, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 12,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/__health", Port = 8080, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 12,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/__health", Port = 8080, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 48,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FC_ENABLE", Value = "1"
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "USER_EVENT_HUB_HOST",
                                            Value = GetContainerAppsValues(
                                                ServiceNames.UserEventHub,
                                                containerApps,
                                                res => res.Apply(c => "https://" + c!.Ingress!.Fqdn))
                                        }
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            containerApps.Add(ServiceNames.InternalGateway, containerApp);
            apps.Add(
                managedEnvAndAppsTuple.LocationName,
                (containerApp, containerApp.Configuration.Apply(c => "https://" + c!.Ingress!.Fqdn)));
        }

        return apps;
    }

    private Docker.Image GetKrakenDImage(string igwImage)
    {
        return ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            igwImage,
            _myConfig.BuildTime);
    }

    private Output<string> GetContainerAppsValues(
        string name,
        Dictionary<string, App.ContainerApp> containerApps,
        Func<Output<ConfigurationResponse?>, Output<string>> func)
    {
        return containerApps.TryGetValue(name, out var app)
            ? func(app.Configuration)
            : Output.Create(string.Empty);
    }
}
