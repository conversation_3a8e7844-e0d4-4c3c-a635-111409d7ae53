using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Categories;

[TriggerGroup(ControllerNames.Categories)]
public class SuggestCategories
    : ITrigger<
        SuggestCategories.SuggestCategoriesInput,
        SuggestCategories.SuggestCategoriesOutput>
{
    private readonly ICategorySearchService _categorySearchService;
    private readonly ICategoryService _categoryService;

    public SuggestCategories(
        ICategorySearchService categorySearchService,
        ICategoryService categoryService)
    {
        _categorySearchService = categorySearchService;
        _categoryService = categoryService;
    }

    public class SuggestCategoriesInput
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(256, MinimumLength = 1)]
        [JsonProperty("search_text")]
        public string SearchText { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public SuggestCategoriesInput(
            string sleekflowCompanyId,
            string storeId,
            string searchText,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            SearchText = searchText;
            Limit = limit;
        }
    }

    public class SuggestCategoriesOutput
    {
        [JsonProperty("categories")]
        public List<CategoryDto> Categories { get; set; }

        [JsonConstructor]
        public SuggestCategoriesOutput(
            List<CategoryDto> categories)
        {
            Categories = categories;
        }
    }

    public async Task<SuggestCategoriesOutput> F(SuggestCategoriesInput suggestCategoriesInput)
    {
        var categoryIndexDtos =
            await _categorySearchService.SuggestCategoriesAsync(
                suggestCategoriesInput.SleekflowCompanyId,
                suggestCategoriesInput.StoreId,
                suggestCategoriesInput.SearchText,
                suggestCategoriesInput.Limit);

        var categories = await _categoryService.GetCategoriesAsync(
            suggestCategoriesInput.SleekflowCompanyId,
            suggestCategoriesInput.StoreId,
            suggestCategoriesInput.Limit,
            categoryIndexDtos.Select(c => c.Id).ToList());

        return new SuggestCategoriesOutput(categories.Select(c => new CategoryDto(c)).ToList());
    }
}