using System.Text.RegularExpressions;
using Scriban;
using Scriban.Functions;
using Scriban.Runtime;
using Scriban.Syntax;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.Extensions;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.ScribanExtensions;
using Sleekflow.FlowHub.States.Functions;

namespace Sleekflow.FlowHub.States;

public interface IStateEvaluator
{
    bool IsExpression(string str);

    Task<object?> EvaluateExpressionAsync(
        ProxyState state,
        string expression,
        EventBody? eventBody = null);

    Task<T?> EvaluateExpressionAsync<T>(ProxyState state, string expression, EventBody? eventBody = null);

    Task<object?> EvaluateExpressionAsync(
        string workflowName,
        object contactData,
        string expression);

    Task<object?> EvaluateTemplateStringExpressionAsync(
        ProxyState state,
        string? expression,
        EventBody? eventBody = null);

    Task<Dictionary<string, object?>> EvaluateDictExpressionAsync(
        ProxyState state,
        Dictionary<string, string?> dict);
}

public partial class StateEvaluator : IStateEvaluator, ISingletonService
{
    public bool IsExpression(string str)
    {
        var myRegex = MyRegex();
        var match = myRegex.Match(str);

        return match.Success;
    }

    public async Task<object?> EvaluateExpressionAsync(
        ProxyState state,
        string expression,
        EventBody? eventBody = null)
    {
        try
        {
            var myRegex = MyRegex();
            var match = myRegex.Match(expression);

            // Simple String
            if (!match.Success)
            {
                return expression;
            }

            expression = match.Groups[1].Value;

            var scriptObject = new ScriptObject
            {
                {
                    "ctx", new EvaluationContext(
                        state.Identity,
                        state.Origin)
                },
                {
                    "trigger_event_body", state.TriggerEventBody
                },
                {
                    "usr_var_dict", state.UsrVarDict
                },
                {
                    "sys_var_dict", state.SysVarDict
                },
                {
                    "event_body", eventBody
                },
                {
                    "json", new JsonFunctions()
                },
                {
                    "array", new ExtendedArrayFunctions()
                },
                {
                    "date", new ExtendedDateTimeFunctions()
                },
                {
                    "sleekflow", new SleekflowFunctions()
                },
                {
                    "template", new TemplateFunctions()
                },
                {
                    "workflow_name", state.WorkflowContext.SnapshottedWorkflow.Name
                },
                {
                    "state_id", state.Id
                }
            };

            // streaming variable cannot be evaluated, so we leave behind a placeholder
            scriptObject.Import("streaming_var", new Func<string, string>((id) => $"{{{{ streaming_var \"{id}\" }}}}"));

            scriptObject.IsReadOnly = true;

            var templateContext = new AsyncTemplateContext();
            templateContext.PushGlobal(scriptObject);
            ((DateTimeFunctions) templateContext.BuiltinObject["date"]).Format = "%FT%T%Z";

            var evaluatedExpression = await Template.EvaluateAsync(
                expression.Trim(),
                templateContext);

            if (evaluatedExpression is ScriptRange scriptRange)
            {
                return new ScriptArray(scriptRange);
            }

            return evaluatedExpression;
        }
        catch (InvalidOperationException ex)
        {
            // TODO Log
            // throw new Exception($"Unable to evaluate expression: {expression}", ex);
            if (ex.Message.Contains("This template has errors."))
            {
                return expression;
            }

            throw;
        }
        catch (ScriptRuntimeException ex)
        {
            throw new SfScriptingException(ex);
        }
    }

    public async Task<T?> EvaluateExpressionAsync<T>(
        ProxyState state,
        string expression,
        EventBody? eventBody = null)
    {
        return (T) (await EvaluateExpressionAsync(state, expression, eventBody) ?? expression);
    }

    public async Task<object?> EvaluateExpressionAsync(
        string workflowName,
        object contactData,
        string expression)
    {
        try
        {
            var myRegex = MyRegex();
            var match = myRegex.Match(expression);

            // Simple String
            if (!match.Success)
            {
                return expression;
            }

            expression = match.Groups[1].Value;

            var scriptObject = new ScriptObject
            {
                {
                    "usr_var_dict", contactData
                },
                {
                    "json", new JsonFunctions()
                },
                {
                    "array", new ExtendedArrayFunctions()
                },
                {
                    "date", new ExtendedDateTimeFunctions()
                },
                {
                    "sleekflow", new SleekflowFunctions()
                },
                {
                    "template", new TemplateFunctions()
                },
                {
                    "workflow_name", workflowName
                },
            };

            scriptObject.IsReadOnly = true;

            var templateContext = new AsyncTemplateContext();
            templateContext.PushGlobal(scriptObject);
            ((DateTimeFunctions) templateContext.BuiltinObject["date"]).Format = "%FT%T%Z";

            var evaluatedExpression = await Template.EvaluateAsync(
                expression.Trim(),
                templateContext);

            if (evaluatedExpression is ScriptRange scriptRange)
            {
                return new ScriptArray(scriptRange);
            }

            return evaluatedExpression;
        }
        catch (InvalidOperationException ex)
        {
            // TODO Log
            // throw new Exception($"Unable to evaluate expression: {expression}", ex);
            if (ex.Message.Contains("This template has errors."))
            {
                return expression;
            }

            throw;
        }
        catch (ScriptRuntimeException ex)
        {
            throw new SfScriptingException(ex);
        }
    }

    public async Task<object?> EvaluateTemplateStringExpressionAsync(ProxyState state, string? expression, EventBody? eventBody = null)
    {
        if (string.IsNullOrWhiteSpace(expression))
        {
            return expression;
        }

        var result = await EvaluateExpressionAsync(
            state,
            $"{{{{ '{expression.EscapeSequences()}' | template.eval }}}}",
            eventBody);

        return result;
    }

    public async Task<Dictionary<string, object?>> EvaluateDictExpressionAsync(
        ProxyState state,
        Dictionary<string, string?> dict)
    {
        var evaluatedDict = new Dictionary<string, object?>();

        foreach (var (key, value) in dict)
        {
            evaluatedDict[key] = value == null
                ? null
                : await EvaluateExpressionAsync(state, value);
        }

        return evaluatedDict;
    }

    [GeneratedRegex("^{{(.*)}}$")]
    private static partial Regex MyRegex();
}