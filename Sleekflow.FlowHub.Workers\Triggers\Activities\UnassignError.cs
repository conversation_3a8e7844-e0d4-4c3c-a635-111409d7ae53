using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class UnassignError
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public UnassignError(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    [Function("UnassignError")]
    public async Task<UnassignErrorOutput> RunAsync(
        [ActivityTrigger]
        UnassignErrorInput unassignErrorInput)
    {
        var inputJsonStr =
            JsonConvert.SerializeObject(unassignErrorInput, JsonConfig.DefaultJsonSerializerSettings);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.FlowHubInternalsEndpoint + "/UnassignError"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<UnassignErrorOutput>()!;
    }
}