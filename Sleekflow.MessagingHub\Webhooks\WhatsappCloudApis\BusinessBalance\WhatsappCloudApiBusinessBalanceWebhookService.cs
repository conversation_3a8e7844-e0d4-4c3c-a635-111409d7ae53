using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Worker;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.Webhooks;
using Sleekflow.Webhooks.Events;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;

public interface IWhatsappCloudApiBusinessBalanceWebhookService
{
    Task UpsertWebhooksAsync(
        string sleekflowCompanyId,
        List<string> facebookBusinessIds,
        string webhookUrl,
        CancellationToken cancellationToken = default);

    Task UpsertWebhookAsync(
        string sleekflowCompanyId,
        string facebookBusinessId,
        string webhookUrl,
        CancellationToken cancellationToken = default);

    Task<List<Webhook>> GetWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string facebookBusinessId,
        CancellationToken cancellationToken = default);

    Task<List<Webhook>> GetOnWhatsappCloudApiBusinessBalanceChangedWebhooksAsync(
        string entityTypeName,
        string eventTypeName,
        string facebookBusinessId,
        CancellationToken cancellationToken = default);

    Task SendWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string facebookBusinessId,
        CancellationToken cancellationToken = default);

    Task SendWebhooksAsync(
        List<Webhook> webhooks,
        object payloadObj,
        CancellationToken cancellationToken = default);

    Task SendWebhookAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string facebookBusinessId,
        Webhook webhook,
        CancellationToken cancellationToken = default);

    Task PatchWebhookAsync(
        string webhookId,
        string webhookUrl);

    Task RemoveWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string facebookBusinessId,
        CancellationToken cancellationToken = default);
}

public class WhatsappCloudApiBusinessBalanceWebhookService
    : IWhatsappCloudApiBusinessBalanceWebhookService,
        ISingletonService
{
    private const string PropertyNameFacebookBusinessId = "facebook_business_id";

    private readonly IIdService _idService;

    private readonly IWebhookRepository _webhookRepository;
    private readonly IBus _bus;

    public WhatsappCloudApiBusinessBalanceWebhookService(
        IWebhookRepository webhookRepository,
        IBus bus,
        IIdService idService)
    {
        _webhookRepository = webhookRepository;
        _bus = bus;
        _idService = idService;
    }

    public async Task UpsertWebhooksAsync(
        string sleekflowCompanyId,
        List<string> facebookBusinessIds,
        string webhookUrl,
        CancellationToken cancellationToken = default)
    {
        foreach (var facebookBusinessId in facebookBusinessIds)
        {
            await UpsertWebhookAsync(sleekflowCompanyId, facebookBusinessId, webhookUrl, cancellationToken);
        }
    }

    public async Task UpsertWebhookAsync(
        string sleekflowCompanyId,
        string facebookBusinessId,
        string webhookUrl,
        CancellationToken cancellationToken = default)
    {
        var whatsappCloudApiBusinessBalanceWebhookUrl = $"{webhookUrl}/{sleekflowCompanyId}/{facebookBusinessId}";

        var webhooks = await GetWebhooksAsync(
            sleekflowCompanyId,
            WebhookEntityTypeNames.BusinessBalance,
            WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiBusinessBalanceChanged,
            facebookBusinessId,
            cancellationToken);

        if (webhooks.Any())
        {
            foreach (var webhook in webhooks)
            {
                await PatchWebhookAsync(webhook.Id, whatsappCloudApiBusinessBalanceWebhookUrl);
            }

            return;
        }

        var newWebhook = new Webhook(
            _idService.GetId(SysTypeNames.Webhook),
            sleekflowCompanyId,
            WebhookEntityTypeNames.BusinessBalance,
            whatsappCloudApiBusinessBalanceWebhookUrl,
            WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiBusinessBalanceChanged,
            new Dictionary<string, object?>())
        {
            Context = new Dictionary<string, object?>
            {
                {
                    PropertyNameFacebookBusinessId, facebookBusinessId
                },
            }
        };

        await _webhookRepository.CreateAndGetAsync(
            newWebhook,
            newWebhook.Id,
            cancellationToken: cancellationToken);
    }

    public async Task<List<Webhook>> GetWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string facebookBusinessId,
        CancellationToken cancellationToken = default)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT * " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.sleekflow_company_id = @sleekflowCompanyId " +
                "AND e.entity_type_name = @entityTypeName " +
                "AND e.event_type_name = @eventTypeName " +
                "AND e.context[@PropertyNameFacebookBusinessId] = @FacebookBusinessId ")
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@entityTypeName", entityTypeName)
            .WithParameter("@eventTypeName", eventTypeName)
            .WithParameter("@PropertyNameFacebookBusinessId", PropertyNameFacebookBusinessId)
            .WithParameter("@FacebookBusinessId", facebookBusinessId);

        return await _webhookRepository.GetObjectsAsync(
            queryDefinition,
            cancellationToken: cancellationToken);
    }

    public async Task<List<Webhook>> GetOnWhatsappCloudApiBusinessBalanceChangedWebhooksAsync(
        string entityTypeName,
        string eventTypeName,
        string facebookBusinessId,
        CancellationToken cancellationToken = default)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT * " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.entity_type_name = @entityTypeName " +
                "AND e.event_type_name = @eventTypeName " +
                "AND e.context[@PropertyNameFacebookBusinessId] = @FacebookBusinessId ")
            .WithParameter("@entityTypeName", entityTypeName)
            .WithParameter("@eventTypeName", eventTypeName)
            .WithParameter("@PropertyNameFacebookBusinessId", PropertyNameFacebookBusinessId)
            .WithParameter("@FacebookBusinessId", facebookBusinessId);

        return await _webhookRepository.GetObjectsAsync(
            queryDefinition,
            cancellationToken: cancellationToken);
    }

    public async Task SendWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string facebookBusinessId,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksAsync(
            sleekflowCompanyId,
            entityTypeName,
            eventTypeName,
            facebookBusinessId,
            cancellationToken);

        var events = webhooks
            .Select(
                webhook => new OnWebhookRequestedEvent(
                    webhook.SleekflowCompanyId,
                    webhook.Context,
                    payloadObj,
                    webhook))
            .ToList();

        await _bus.PublishBatch(
            events,
            cancellationToken: cancellationToken);
    }

    public async Task SendWebhooksAsync(
        List<Webhook> webhooks,
        object payloadObj,
        CancellationToken cancellationToken = default)
    {
        var events = webhooks
            .Select(
                webhook => new OnWebhookRequestedEvent(
                    webhook.SleekflowCompanyId,
                    webhook.Context,
                    payloadObj,
                    webhook))
            .ToList();

        await _bus.PublishBatch(
            events,
            cancellationToken: cancellationToken);
    }

    public async Task SendWebhookAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string facebookBusinessId,
        Webhook webhook,
        CancellationToken cancellationToken = default)
    {
        var onWebhookRequestedEvent = new OnWebhookRequestedEvent(
            webhook.SleekflowCompanyId,
            webhook.Context,
            payloadObj,
            webhook);

        await _bus.Publish(
            onWebhookRequestedEvent,
            cancellationToken: cancellationToken);
    }

    public async Task PatchWebhookAsync(string webhookId, string webhookUrl)
    {
        var patchWebhook = await _webhookRepository.PatchAsync(
            webhookId,
            webhookId,
            new List<PatchOperation>
            {
                Replace(Webhook.PropertyNameUrl, webhookUrl)
            });
        if (patchWebhook == 0)
        {
            throw new SfWebhookProcessingException(webhookId);
        }
    }

    public async Task RemoveWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string facebookBusinessId,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksAsync(
            sleekflowCompanyId,
            entityTypeName,
            eventTypeName,
            facebookBusinessId,
            cancellationToken);

        foreach (var webhookId in webhooks.Select(w => w.Id).Distinct())
        {
            await _webhookRepository.DeleteAsync(
                webhookId,
                webhookId,
                cancellationToken: cancellationToken);
        }
    }
}