using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.Links;

namespace Sleekflow.CommerceHub.ShortenLink;

public interface IShortenLinkService
{
    Task<string> GetShortenedUrl(
        string url,
        string sleekflowCompanyId,
        string title);
}

public class ShortenLinkService : IShortenLinkService, IScopedService
{
    private readonly IRequestClient<ShortenLinkRequest> _shortenLinkRequestClient;

    public ShortenLinkService(
        IRequestClient<ShortenLinkRequest> shortenLinkRequestClient)
    {
        _shortenLinkRequestClient = shortenLinkRequestClient;
    }

    public async Task<string> GetShortenedUrl(
        string url,
        string sleekflowCompanyId,
        string title)
    {
        var shortenLinkReplyResponse = await _shortenLinkRequestClient.GetResponse<ShortenLinkReply>(
            new ShortenLinkRequest(
                url,
                sleekflowCompanyId,
                title,
                null));
        var shortenLinkReply = shortenLinkReplyResponse.Message;

        return shortenLinkReply.ShortUrl;
    }
}