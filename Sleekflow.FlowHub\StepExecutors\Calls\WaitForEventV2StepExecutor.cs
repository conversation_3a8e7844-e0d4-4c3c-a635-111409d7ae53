using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IWaitForEventV2StepExecutor : IStepExecutor;

public class WaitForEventV2StepExecutor
    : GeneralStepExecutor<CallStep<WaitForEventV2StepArgs>>, IWaitForEventV2StepExecutor, IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IStateSubscriptionService _stateSubscriptionService;
    private readonly IIdService _idService;
    private readonly IBus _bus;
    private readonly IExecutorContext _executorContext;

    public WaitForEventV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IStateSubscriptionService stateSubscriptionService,
        IIdService idService,
        IBus bus,
        IExecutorContext executorContext)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _stateSubscriptionService = stateSubscriptionService;
        _idService = idService;
        _bus = bus;
        _executorContext = executorContext;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        var eventName = callStep.Args.EventName;
        var condition = string.IsNullOrWhiteSpace(callStep.Args.ConditionExpr)
            ? "{{ true }}"
            : callStep.Args.ConditionExpr;

        var timeoutSecondsInt = await GetTimeoutSecondsInt(state, callStep);

        var stateSubscription = await _stateSubscriptionService.CreateStateSubscriptionAsync(
            new StateSubscription(
                _idService.GetId("StateSubscription"),
                state.Id,
                eventName,
                condition,
                step.Id,
                stackEntries,
                false,
                timeoutSecondsInt.HasValue
                    ? DateTimeOffset.UtcNow.AddSeconds(timeoutSecondsInt.Value)
                    : null,
                null,
                false,
                _executorContext.WorkerInstanceId));

        if (timeoutSecondsInt.HasValue)
        {
            await _bus.Publish(
                new OnWaitForEventStepTimeoutEvent(stateSubscription.Id, state.Id),
                context =>
                {
                    context.Delay = TimeSpan.FromSeconds(timeoutSecondsInt.Value);
                });
        }

        // The actions is Delayed Complete
        // Sleekflow.FlowHub.FlowHubEvents.FlowHubEventHandler.AggregateStateAndExecuteWorkflowSubscriptionsAsync
    }

    private async Task<int?> GetTimeoutSecondsInt(ProxyState state, CallStep<WaitForEventV2StepArgs> callStep)
    {
        if (string.IsNullOrEmpty(callStep.Args.TimeoutUnitsExpr) || string.IsNullOrEmpty(callStep.Args.TimeUnit))
        {
            return null;
        }

        var timeUnit = callStep.Args.TimeUnit;
        var timeoutUnitsObj =
            _stateEvaluator.IsExpression(callStep.Args.TimeoutUnitsExpr)
                ? await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.TimeoutUnitsExpr)
                : decimal.Parse(callStep.Args.TimeoutUnitsExpr);

        // Safely handle only `decimal` or valid numeric conversion scenarios
        var timeoutUnitsDecimal = timeoutUnitsObj switch
        {
            decimal d => d, // Already a decimal
            string s when decimal.TryParse(s, out var parsedDecimal) => parsedDecimal, // Parse string to decimal if possible
            _ => 0
        };

        var timeSpan = TimeUnitUtils.ConvertToTimeSpan(timeUnit, Convert.ToDouble(timeoutUnitsDecimal)) ??
                       default(TimeSpan);

        var timeoutSecondsInt = Convert.ToInt32(timeSpan.TotalSeconds);

        switch (timeoutSecondsInt)
        {
            case 0:
                return null;
            case > 604800:
                timeoutSecondsInt = 604800;
                break;
        }

        return timeoutSecondsInt;
    }
}