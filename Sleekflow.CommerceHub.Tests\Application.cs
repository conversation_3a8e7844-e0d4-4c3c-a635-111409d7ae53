using System.Text;
using System.Xml;
using Alba;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.CommerceHub.ShortenLink;
using Sleekflow.CommerceHub.Workers;
using Sleekflow.DurablePayloads;
using Sleekflow.Mvc.Tests;
using Sleekflow.Persistence.Abstractions;
using Formatting = Newtonsoft.Json.Formatting;

namespace Sleekflow.CommerceHub.Tests;

[SetUpFixture]
public class Application
{
    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }

    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.CommerceHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        Host = await AlbaHost.For<Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(
                    services =>
                    {
                        services.AddScoped<IDynamicFiltersRepositoryContext, TestRepositoryContext>();
                        services.AddScoped<ICommerceHubWorkerService, MockCommerceHubWorkerService>();
                        services.AddScoped<IShortenLinkService, MockShortenLinkService>();
                    });
            },
            new IAlbaExtension[]
            {
            });

        Host.AfterEachAsync(
            async context =>
            {
                await BaseTestHost.InterceptAfterEachAsync(context);
            });
    }

    public class MockCommerceHubWorkerService : ICommerceHubWorkerService
    {
        public Task<DurablePayload> ProcessCustomCatalogCsvAsync(string sleekflowCompanyId, string id)
        {
            return Task.FromResult((DurablePayload) null!);
        }

        public Task<DurablePayload> StartLoopThroughAndEnrollVtexOrdersToFlowHubAsync(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId,
            string vtexAuthenticationId,
            VtexGetOrdersSearchCondition condition)
        {
            throw new NotImplementedException();
        }
    }

    public class TestRepositoryContext : IDynamicFiltersRepositoryContext
    {
        public bool IsSoftDeleteEnabled { get; set; } = false;

        public string? SleekflowCompanyId { get; set; } = null;
    }

    public class MockShortenLinkService : IShortenLinkService
    {
        public Task<string> GetShortenedUrl(string url, string sleekflowCompanyId, string title)
        {
            return Task.FromResult(url);
        }
    }

    public static IAlbaHost Host { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        Host.Dispose();
    }
}