using Microsoft.AspNetCore.Mvc;

namespace Sleekflow.FlowHub.Integrator.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public partial class PublicController : ControllerBase
{
    [HttpGet]
    [Route("healthz")]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }
}