using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Workers;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class ExecuteTryCatchStepOrchestrator
{
    private readonly ILogger<ExecuteTryCatchStepOrchestrator> _logger;

    public ExecuteTryCatchStepOrchestrator(
        ILogger<ExecuteTryCatchStepOrchestrator> logger)
    {
        _logger = logger;
    }

    [Function("ExecuteTryCatchStep_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var executeTryCatchStepInput = context.GetInput<ExecuteTryCatchStepInput>();

        try
        {
            await ExecuteTryCatchStepAsync(context, executeTryCatchStepInput);
        }
        catch (Exception e)
        {
            if (e.InnerException is SfFlowHubUserFriendlyException)
            {
                // ignore
            }
            else
            {
                _logger.LogError(e, "Error executing TryCatchStep {TryCatchStepId}", executeTryCatchStepInput.StepId);
            }

            // Fails during CatchStep
            await context.CallActivityAsync(
                "CompleteStep",
                new CompleteStepInput(
                    executeTryCatchStepInput.StateId,
                    executeTryCatchStepInput.StepId,
                    executeTryCatchStepInput.StackEntries,
                    StepExecutionStatuses.Failed));
        }
    }

    private async Task ExecuteTryCatchStepAsync(
        TaskOrchestrationContext context,
        ExecuteTryCatchStepInput executeTryCatchStepInput)
    {
        try
        {
            await context.CallSubOrchestratorAsync(
                "SubmitStep_Orchestrator",
                new SubmitStepOrchestrator.SubmitStepOrchestratorInput(
                    executeTryCatchStepInput.StateId,
                    executeTryCatchStepInput.TryStepId,
                    executeTryCatchStepInput.StackEntries));
        }
        catch (Exception e)
        {
            UserFriendlyError userFriendlyError;
            if (e is TaskFailedException && e.InnerException is SfFlowHubUserFriendlyException sfFlowHubUserFriendlyException)
            {
                userFriendlyError = new UserFriendlyError(
                    sfFlowHubUserFriendlyException.UserFriendlyErrorCode,
                    sfFlowHubUserFriendlyException.UserFriendlyErrorMessage);
            }
            else
            {
                _logger.LogError(e, "Error executing TryCatchStep {TryCatchStepId}", executeTryCatchStepInput.StepId);

                userFriendlyError = new UserFriendlyError(
                    UserFriendlyErrorCodes.InternalError,
                    "Internal Server Error in Orchestration");
            }

            await context.CallActivityAsync(
                "AssignError",
                new AssignErrorInput(
                    executeTryCatchStepInput.StateId,
                    executeTryCatchStepInput.StepId,
                    executeTryCatchStepInput.StackEntries,
                    userFriendlyError));

            await context.CallSubOrchestratorAsync(
                "SubmitStep_Orchestrator",
                new SubmitStepOrchestrator.SubmitStepOrchestratorInput(
                    executeTryCatchStepInput.StateId,
                    executeTryCatchStepInput.CatchStepId,
                    executeTryCatchStepInput.StackEntries));

            await context.CallActivityAsync(
                "UnassignError",
                new UnassignErrorInput(
                    executeTryCatchStepInput.StateId,
                    executeTryCatchStepInput.StepId,
                    executeTryCatchStepInput.StackEntries));
        }
    }
}