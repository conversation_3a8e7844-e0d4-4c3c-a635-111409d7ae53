using Sleekflow.Persistence.Abstractions;
using Sleekflow.Events;

namespace Sleekflow.Webhooks.Events;

public class OnWebhookRequestedBaseEvent : IEvent, IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public Dictionary<string, object?> Context { get; set; }

    public object PayloadObj { get; set; }

    public Webhook Webhook { get; set; }

    protected OnWebhookRequestedBaseEvent(
        string sleekflowCompanyId,
        Dictionary<string, object?> context,
        object payloadObj,
        Webhook webhook)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Context = context;
        PayloadObj = payloadObj;
        Webhook = webhook;
    }
}