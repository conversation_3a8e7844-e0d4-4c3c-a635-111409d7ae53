using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.CustomCatalogs.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs.Configs;

[TriggerGroup(ControllerNames.CustomCatalogConfigs)]
public class UpdateCustomCatalogConfig
    : ITrigger<
        UpdateCustomCatalogConfig.UpdateCustomCatalogConfigInput,
        UpdateCustomCatalogConfig.UpdateCustomCatalogConfigOutput>
{
    private readonly ICustomCatalogConfigService _customCatalogConfigService;

    public UpdateCustomCatalogConfig(ICustomCatalogConfigService customCatalogConfigService)
    {
        _customCatalogConfigService = customCatalogConfigService;
    }

    public class UpdateCustomCatalogConfigInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("period_start")]
        public DateTimeOffset PeriodStart { get; set; }

        [Required]
        [JsonProperty("period_end")]
        public DateTimeOffset PeriodEnd { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateCustomCatalogConfigInput(
            string id,
            string sleekflowCompanyId,
            DateTimeOffset periodStart,
            DateTimeOffset periodEnd,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            PeriodStart = periodStart;
            PeriodEnd = periodEnd;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateCustomCatalogConfigOutput
    {
    }

    public async Task<UpdateCustomCatalogConfigOutput> F(UpdateCustomCatalogConfigInput updateCustomCatalogConfigInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateCustomCatalogConfigInput.SleekflowStaffId,
            updateCustomCatalogConfigInput.SleekflowStaffTeamIds);

        var customCatalogConfig =
            await _customCatalogConfigService.GetCustomCatalogConfigAsync(
                updateCustomCatalogConfigInput.Id,
                updateCustomCatalogConfigInput.SleekflowCompanyId);

        customCatalogConfig.PeriodStart = updateCustomCatalogConfigInput.PeriodStart;
        customCatalogConfig.PeriodEnd = updateCustomCatalogConfigInput.PeriodEnd;
        var upsertCustomCatalogConfigStatus =
            await _customCatalogConfigService.UpsertCustomCatalogConfigAsync(customCatalogConfig, sleekflowStaff);
        if (upsertCustomCatalogConfigStatus == 0)
        {
            throw new SfInternalErrorException("Unable to upsert custom catalog config");
        }

        return new UpdateCustomCatalogConfigOutput();
    }
}