namespace Sleekflow.Exceptions;

public class SfUnrecognizedEntityTypeException : ErrorCodeException
{
    public SfUnrecognizedEntityTypeException(string entityTypeName)
        : base(
            ErrorCodeConstant.SfUnrecognizedEntityTypeException,
            $"The entityTypeName is unrecognized. entityTypeName {entityTypeName}",
            new Dictionary<string, object?>
            {
                {
                    "entityTypeName", entityTypeName
                }
            })
    {
    }
}