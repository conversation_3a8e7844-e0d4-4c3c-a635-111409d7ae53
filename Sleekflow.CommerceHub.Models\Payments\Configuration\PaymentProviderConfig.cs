using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Payments.Configuration;

[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
[ContainerId(ContainerNames.PaymentProviderManagement)]
public class PaymentProviderConfig : AuditEntity, IHasRecordStatuses
{
    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("store_ids")]
    public List<string> StoreIds { get; set; }

    [JsonProperty("store_id_to_currency_iso_codes_dict")]
    public Dictionary<string, List<string>> StoreIdToCurrencyIsoCodesDict { get; set; }

    [JsonProperty("supported_currency_iso_codes")]
    public List<string> SupportedCurrencyIsoCodes { get; set; }

    [JsonProperty("payment_provider_external_config", TypeNameHandling = TypeNameHandling.Objects)]
    public PaymentProviderExternalConfig PaymentProviderExternalConfig { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonConstructor]
    public PaymentProviderConfig(
        string id,
        string sleekflowCompanyId,
        string status,
        List<string> storeIds,
        Dictionary<string, List<string>> storeIdToCurrencyIsoCodesDict,
        List<string> supportedCurrencyIsoCodes,
        PaymentProviderExternalConfig paymentProviderExternalConfig,
        List<string> recordStatuses,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy)
        : base(id, SysTypeNames.PaymentProviderConfig, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        Status = status;
        StoreIds = storeIds;
        StoreIdToCurrencyIsoCodesDict = storeIdToCurrencyIsoCodesDict;
        SupportedCurrencyIsoCodes = supportedCurrencyIsoCodes;
        PaymentProviderExternalConfig = paymentProviderExternalConfig;
        RecordStatuses = recordStatuses;
    }
}