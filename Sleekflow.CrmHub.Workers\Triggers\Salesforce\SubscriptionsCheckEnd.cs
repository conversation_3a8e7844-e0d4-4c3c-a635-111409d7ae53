﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.Salesforce;

public class SubscriptionsCheckEnd
{
    private readonly ISalesforceSubscriptionRepository _salesforceSubscriptionRepository;

    public SubscriptionsCheckEnd(
        ISalesforceSubscriptionRepository salesforceSubscriptionRepository)
    {
        _salesforceSubscriptionRepository = salesforceSubscriptionRepository;
    }

    public class SubscriptionsCheckEndInput
    {
        [JsonProperty("subscription")]
        [Required]
        public SalesforceSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonProperty("last_execution_start_time")]
        [Required]
        public DateTimeOffset LastExecutionStartTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckEndInput(
            SalesforceSubscription subscription,
            DateTimeOffset? lastObjectModificationTime,
            DateTimeOffset lastExecutionStartTime)
        {
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
            LastExecutionStartTime = lastExecutionStartTime;
        }
    }

    [Function("Salesforce_SubscriptionsCheck_End")]
    public async Task End(
        [ActivityTrigger]
        SubscriptionsCheckEndInput subscriptionsCheckEndInput)
    {
        var patchOperations = new List<PatchOperation>
        {
            PatchOperation.Replace(
                $"/{SalesforceSubscription.PropertyNameDurablePayload}",
                (HttpManagementPayload?) null),
            PatchOperation.Replace(
                $"/{SalesforceSubscription.PropertyNameLastExecutionStartTime}",
                subscriptionsCheckEndInput.LastExecutionStartTime),
        };
        if (subscriptionsCheckEndInput.LastObjectModificationTime != null)
        {
            patchOperations.Add(
                PatchOperation.Replace(
                    $"/{SalesforceSubscription.PropertyNameLastObjectModificationTime}",
                    subscriptionsCheckEndInput.LastObjectModificationTime));
        }

        await _salesforceSubscriptionRepository.PatchAsync(
            subscriptionsCheckEndInput.Subscription.Id,
            subscriptionsCheckEndInput.Subscription.SleekflowCompanyId,
            patchOperations);
    }
}