﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifySchedulerResponse
{
    [JsonProperty(PropertyName = "data")]
    public ApifySchedulerResponseData Data { get; set; }

    [JsonConstructor]
    public ApifySchedulerResponse(ApifySchedulerResponseData data)
    {
        Data = data;
    }
}

public sealed class ApifySchedulerResponseData
{
    [JsonProperty(PropertyName = "id")]
    public string Id { get; set; }

    [JsonProperty(PropertyName = "name")]
    public string Name { get; set; }

    [JsonProperty(PropertyName = "cronExpression")]
    public string CronExpression { get; set; }

    [JsonProperty(PropertyName = "timezone")]
    public string Timezone { get; set; }

    [JsonProperty(PropertyName = "isEnabled")]
    public bool IsEnabled { get; set; }

    [JsonProperty(PropertyName = "isExclusive")]
    public bool IsExclusive { get; set; }

    [JsonProperty(PropertyName = "description")]
    public string Description { get; set; }

    [JsonProperty(PropertyName = "createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty(PropertyName = "modifiedAt")]
    public DateTime? ModifiedAt { get; set; }

    [JsonProperty(PropertyName = "nextRunAt")]
    public DateTime? NextRunAt { get; set; }

    [JsonProperty(PropertyName = "lastRunAt")]
    public DateTime? LastRunAt { get; set; }

    [JsonProperty(PropertyName = "actions")]
    public List<ApifySchedulerResponseDataAction> Actions { get; set; }

    [JsonConstructor]
    public ApifySchedulerResponseData(
        string id,
        string name,
        string cronExpression,
        string timezone,
        bool isEnabled,
        bool isExclusive,
        string description,
        DateTime? createdAt,
        DateTime? modifiedAt,
        DateTime? nextRunAt,
        DateTime? lastRunAt,
        List<ApifySchedulerResponseDataAction> actions)
    {
        Id = id;
        Name = name;
        CronExpression = cronExpression;
        Timezone = timezone;
        IsEnabled = isEnabled;
        IsExclusive = isExclusive;
        Description = description;
        CreatedAt = createdAt;
        ModifiedAt = modifiedAt;
        NextRunAt = nextRunAt;
        LastRunAt = lastRunAt;
        Actions = actions;
    }
}

public sealed class ApifySchedulerResponseDataAction
{
    [JsonProperty(PropertyName = "id")]
    public string Id { get; set; }

    [JsonProperty(PropertyName = "type")]
    public string Type { get; set; }

    [JsonProperty(PropertyName = "actorId")]
    public string ActorId { get; set; }

    [JsonProperty(PropertyName = "actorTaskId")]
    public string ActorTaskId { get; set; }

    [JsonConstructor]
    public ApifySchedulerResponseDataAction(
        string id,
        string type,
        string actorId,
        string actorTaskId)
    {
        Id = id;
        Type = type;
        ActorId = actorId;
        ActorTaskId = actorTaskId;
    }
}