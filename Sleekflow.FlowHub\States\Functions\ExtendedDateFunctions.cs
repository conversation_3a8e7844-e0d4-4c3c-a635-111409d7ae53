using System.Globalization;
using Scriban.Functions;
using Scriban.Runtime;

namespace Sleekflow.FlowHub.States.Functions;

public class ExtendedDateTimeFunctions : DateTimeFunctions
{
    /// <summary>
    /// Converts a datetime object to the specified timezone.
    /// </summary>
    /// <param name="datetime">The datetime to convert timezone.</param>
    /// <param name="timeZoneId">Time zone identifier.</param>
    /// <returns>
    /// An array that contains 2 elements:<br/>
    /// 1. A datetime object in the requested timezone<br/>
    /// 2. A timespan object that indicates the UTC offset of the requested timezone
    /// </returns>
    /// <exception cref="ArgumentException">When the <paramref name="timeZoneId"/> is not a valid time zone identifier.</exception>
    public static ScriptArray ApplyTimeZone(DateTime datetime, string timeZoneId)
    {
        try
        {
            var utcDate = datetime.ToUniversalTime();
            var targetTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            var targetDate = TimeZoneInfo.ConvertTimeFromUtc(utcDate, targetTimeZone);
            var offset = new DateTimeOffset(targetDate, targetTimeZone.GetUtcOffset(targetDate)).Offset;

            return new ScriptArray { targetDate, offset };
        }
        catch (Exception)
        {
            throw new ArgumentException($"Invalid timezoneId `{timeZoneId}`.", nameof(timeZoneId));
        }
    }

    /// <summary>
    /// Get the textual representation of output from <see cref="ApplyTimeZone"/> using the specified format
    /// string in <paramref name="pattern"/>.
    /// </summary>
    /// <param name="dateTimeAndOffset">Output from <see cref="ApplyTimeZone"/>.</param>
    /// <param name="pattern">
    /// Output format. Refer to
    /// <a href="https://github.com/scriban/scriban/blob/master/doc/builtins.md#dateto_string">Scriban docs</a>.</param>
    /// <returns>String representation of the input datetime and offset.</returns>
    public static string ToStringWithOffset(ScriptArray dateTimeAndOffset, string pattern)
    {
        var datetime = (DateTime) dateTimeAndOffset[0];
        var offset = (TimeSpan) dateTimeAndOffset[1];
        var offsetStr = offset > TimeSpan.Zero ? $"+{offset.ToString()}" : offset.ToString();

        var dateTimeWithOffsetStr =
            $"{new DateTimeFunctions().ToString(datetime, pattern, CultureInfo.InvariantCulture)} (UTC{offsetStr})";

        return dateTimeWithOffsetStr;
    }

    /// <summary>
    /// Converts a string to a datetime object if matches the format
    /// provided optionally, otherwise null.
    /// </summary>
    /// <param name="input">Input string to be parsed.</param>
    /// <param name="format">
    /// The format to parse (optional).<br/>
    /// Example:<br/>
    /// <b>yyyy-MM-ddTHH:mmK</b> - 2023-12-25T18:00+01:00<br/>
    /// <b>yyyy-MM-ddTHH:mm:ssK</b> - 2023-12-25T18:00:00+01:00
    /// </param>
    /// <returns>Nullable local datetime object.</returns>
    public static DateTime? ParseDateTimeString(string? input, string format)
    {
        var isValidParse = string.IsNullOrWhiteSpace(format)
            ? DateTime.TryParse(input, DateTimeFormatInfo.InvariantInfo, out var result)
            : DateTime.TryParseExact(input, format, DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None, out result);

        return isValidParse ? result : null;
    }

    /// <summary>
    /// Converts a string to a datetime object, with the time part being 00:00:00 if
    /// matches the format provided optionally, otherwise null.
    /// </summary>
    /// <param name="input">Input string to be parsed.</param>
    /// <param name="format">
    /// The format to parse (optional).<br/>
    /// Example:<br/>
    /// <b>yyyy-MM-dd</b> - 2023-12-25
    /// </param>
    /// <returns>Nullable local datetime object.</returns>
    public static DateTime? ParseDateString(string? input, string format)
    {
        var isValidParse = string.IsNullOrWhiteSpace(format)
            ? DateOnly.TryParse(input, DateTimeFormatInfo.InvariantInfo, out var result)
            : DateOnly.TryParseExact(input, format, DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None, out result);

        return isValidParse ? result.ToDateTime(TimeOnly.MinValue) : null;
    }

    /// <summary>
    /// Converts a Unix timestamp (string / numbers) to a datetime object in UTC (since Unix Epoch is UTC-based).
    /// If the input is not convertible to numbers or is converted to a negative number, returns null.
    /// </summary>
    /// <param name="input">Input string to be parsed.</param>
    /// <returns>Nullable UTC datetime object.</returns>
    public static DateTime? ParseUnixTimestamp(object? input)
    {
        if (!long.TryParse(Convert.ToString(input), out var unixTimeStamp)
            || unixTimeStamp < 0)
        {
            return null;
        }

        return DateTime.UnixEpoch.AddSeconds(unixTimeStamp);
    }
}