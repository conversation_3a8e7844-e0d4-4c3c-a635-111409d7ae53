﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.HubspotQueues;
using Sleekflow.Integrator.Hubspot.Services;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class UpdateObject : ITrigger
{
    private readonly IIdService _idService;
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotQueueService _hubspotQueueService;

    public UpdateObject(
        IIdService idService,
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotQueueService hubspotQueueService)
    {
        _idService = idService;
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotQueueService = hubspotQueueService;
    }

    public class UpdateObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("crm_hub_object_id")]
        [Required]
        public string CrmHubObjectId { get; set; }

        [JsonConstructor]
        public UpdateObjectInput(
            string sleekflowCompanyId,
            string objectId,
            Dictionary<string, object?> dict,
            string entityTypeName,
            string crmHubObjectId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            Dict = dict;
            EntityTypeName = entityTypeName;
            CrmHubObjectId = crmHubObjectId;
        }
    }

    public class UpdateObjectOutput
    {
        [JsonConstructor]
        public UpdateObjectOutput(bool isAsyncOperation)
        {
            IsAsyncOperation = isAsyncOperation;
        }

        [JsonProperty("is_async_operation")]
        public bool IsAsyncOperation { get; set; }
    }

    public async Task<UpdateObjectOutput> F(UpdateObjectInput updateObjectInput)
    {
        var authentication =
            await _hubspotAuthenticationService.GetAsync(updateObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var obj = await _hubspotObjectService.GetObjectAsync(
            authentication,
            updateObjectInput.ObjectId,
            updateObjectInput.EntityTypeName);
        if (obj == null)
        {
            throw new SfNotFoundObjectException(updateObjectInput.ObjectId, updateObjectInput.SleekflowCompanyId);
        }

        var getFieldsOutput =
            await _hubspotObjectService.GetFieldsAsync(authentication, updateObjectInput.EntityTypeName);
        var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();

        var dict = updateObjectInput.Dict
            .Where(e => updatableFieldNames.Contains(e.Key))
            .ToDictionary(e => e.Key, e => e.Value);

        await _hubspotQueueService.EnqueueItemAsync(
            updateObjectInput.SleekflowCompanyId,
            new HubspotQueueItem(
                _idService.GetId("HubspotQueueItem"),
                null,
                updateObjectInput.SleekflowCompanyId,
                updateObjectInput.EntityTypeName,
                updateObjectInput.CrmHubObjectId,
                updateObjectInput.ObjectId,
                dict,
                ObjectOperations.UpdateObjectOperation,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow));

        return new UpdateObjectOutput(true);
    }
}