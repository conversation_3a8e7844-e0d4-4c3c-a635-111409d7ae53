﻿using Sleekflow.CrmHub.Models.CrmHubConfigs;

namespace Sleekflow.CrmHub.CrmHubConfigs;

public static class CrmHubConfigExtension
{
    public static UsageLimit GetOffsetAppliedUsageLimit(this CrmHubConfig crmHubConfig)
    {
        return new UsageLimit(
            crmHubConfig.UsageLimit.CustomObjectMaximumSchemaNum
            + crmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemaNumOffset ?? 0,
            crmHubConfig.UsageLimit.CustomObjectMaximumPropertyNumPerSchema
            + crmHubConfig.UsageLimitOffset.CustomObjectMaximumPropertyNumPerSchemaOffset ?? 0,
            crmHubConfig.UsageLimit.CustomObjectMaximumSchemafulObjectNumPerSchema
            + crmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemafulObjectNumPerSchemaOffset ?? 0,
            crmHubConfig.UsageLimit.CustomObjectMaximumSchemafulObjectNumPerCompany
            + crmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemafulObjectNumPerCompanyOffset ?? 0,
            crmHubConfig.UsageLimit.CustomObjectMaximumArrayObjectArraySize
            + crmHubConfig.UsageLimitOffset.CustomObjectMaximumArrayObjectArraySizeOffset ?? 0);
    }
}