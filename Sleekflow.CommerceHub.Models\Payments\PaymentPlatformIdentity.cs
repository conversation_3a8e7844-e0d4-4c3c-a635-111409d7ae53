using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Payments;

public class PaymentPlatformIdentity
{
    [JsonProperty("payment_provider_config_id")]
    public string PaymentProviderConfigId { get; set; }

    [JsonProperty("payment_identity_id")]
    public string PaymentIdentityId { get; set; }

    [JsonProperty("payment_platform_identity_type")]
    public string PaymentPlatformIdentityType { get; set; }

    [JsonConstructor]
    public PaymentPlatformIdentity(
        string paymentProviderConfigId,
        string paymentIdentityId,
        string paymentPlatformIdentityType)
    {
        PaymentProviderConfigId = paymentProviderConfigId;
        PaymentIdentityId = paymentIdentityId;
        PaymentPlatformIdentityType = paymentPlatformIdentityType;
    }
}