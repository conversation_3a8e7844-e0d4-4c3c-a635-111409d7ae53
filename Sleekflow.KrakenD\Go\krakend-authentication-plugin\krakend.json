{"version": 3, "plugin": {"pattern": ".so", "folder": "./plugins/"}, "endpoints": [{"endpoint": "/Authorized/UserWorkspaces/GetUserWorkspaces", "method": "POST", "input_headers": ["X-Sleekflow-User-Id", "X-Targeted-Company-Id", "X-Sleekflow-Email", "Content-Type"], "backend": [{"host": ["http://host.docker.internal:7104"], "encoding": "json", "url_pattern": "/Authorized/UserWorkspaces/GetUserWorkspaces", "extra_config": {"plugin/http-client": {"name": "krakend-authentication-plugin", "krakend-authentication-plugin": {"get_user_auth_details_url": "http://host.docker.internal:7104/Internals/GetUserAuthenticationDetails"}}}}], "extra_config": {"modifier/lua-proxy": {"allow_open_libs": false, "live": false, "post": "post_proxy()", "sources": ["post_proxy.lua"]}}}, {"endpoint": "/v1/user-event-hub/ReliableMessage/negotiate", "backend": [{"url_pattern": "/ReliableMessage/negotiate", "host": ["http://host.docker.internal:7108"], "encoding": "no-op", "extra_config": {"plugin/http-client": {"name": "krakend-authentication-plugin", "krakend-authentication-plugin": {"get_user_auth_details_url": "http://host.docker.internal:7104/Internals/GetUserAuthenticationDetails"}}}, "method": "POST"}], "extra_config": {"auth/validator": {"alg": "RS256", "audience": ["https://api.sleekflow.io"], "jwk_url": "https://sso.sleekflow.io/.well-known/jwks.json", "propagate_claims": [["https://app.sleekflow.io/email", "X-Sleekflow-Email"], ["https://app.sleekflow.io/user_id", "X-Sleekflow-User-Id"], ["https://app.sleekflow.io/roles", "X-<PERSON><PERSON>kflow-Roles"], ["https://app.sleekflow.io/connection_strategy", "X-Sleekflow-Connection-Strategy"], ["https://app.sleekflow.io/login_as_user", "X-Sleekflow-Login-As-User"]]}, "modifier/lua-proxy": {"allow_open_libs": false, "live": false, "post": "post_proxy()", "sources": ["post_proxy.lua"]}}, "input_headers": ["Content-Type", "X-<PERSON><PERSON>kflow-Roles", "X-Sleekflow-Email", "X-Sleekflow-User-Id", "X-Sleekflow-Login-As-User", "X-Sleekflow-Connection-Strategy"], "input_query_strings": ["data"], "method": "POST", "output_encoding": "no-op"}]}