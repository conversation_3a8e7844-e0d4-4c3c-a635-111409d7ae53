using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Synapse = Pulumi.AzureNative.Synapse;

namespace Sleekflow.Infras.Components;

public class MySynapse
{
    private readonly ResourceGroup _resourceGroup;
    private readonly MyConfig _myConfig;

    public MySynapse(
        ResourceGroup resourceGroup,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _myConfig = myConfig;
    }

    public class SynapseOutput
    {
        public Output<string> WorkspaceName { get; }

        public Output<string?> SqlAdministratorLogin { get; }

        public Output<string?> SqlAdministratorLoginPassword { get; }

        public SynapseOutput(
            Output<string> workspaceName,
            Output<string?> sqlAdministratorLogin,
            Output<string?> sqlAdministratorLoginPassword
        )
        {
            WorkspaceName = workspaceName;
            SqlAdministratorLogin = sqlAdministratorLogin;
            SqlAdministratorLoginPassword = sqlAdministratorLoginPassword;
        }
    }

    public SynapseOutput InitSynapse()
    {
        var datalakeRandomId = new Random.RandomId(
            "sleekflow-synapse-datalake-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "Synapse", "Datalake"
                    }
                },
            });

        var dataLakeStorageAccount = new Storage.StorageAccount(
            "sleekflow-synapse-datalake",
            new ()
            {
                Kind = Storage.Kind.StorageV2,
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                AccountName = datalakeRandomId.Hex.Apply(h => "s" + h),
                IsHnsEnabled = true
            });

        var synapseWorkspaceRandomId = new Random.RandomId(
            "sleekflow-synapse-workspace-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "Synapse", "Revision-1"
                    }
                },
            });

        var synapseWorkspace = new Synapse.Workspace(
            "sleekflow-synapse",
            new ()
            {
                DefaultDataLakeStorage = new Synapse.Inputs.DataLakeStorageAccountDetailsArgs
                {
                    AccountUrl = dataLakeStorageAccount.PrimaryEndpoints.Apply(endpoints => endpoints.Dfs),
                    Filesystem = "default",
                },
                Identity = new Synapse.Inputs.ManagedIdentityArgs
                {
                    Type = Synapse.ResourceIdentityType.SystemAssigned,
                },
                ManagedResourceGroupName = "synapse-managed-resource-group-" + _myConfig.Name,
                ManagedVirtualNetwork = "default",
                PublicNetworkAccess = "Enabled",
                ResourceGroupName = _resourceGroup.Name,
                WorkspaceName = synapseWorkspaceRandomId.Hex.Apply(h => "synapse" + h),
            });

        return new SynapseOutput(
            synapseWorkspace.Name,
            synapseWorkspace.SqlAdministratorLogin,
            synapseWorkspace.SqlAdministratorLoginPassword
        );
    }
}