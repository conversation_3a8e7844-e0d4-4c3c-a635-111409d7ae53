using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Blobs;
using Sleekflow.MessagingHub.Models.Blobs;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Templates;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Template.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Templates)]
public class UploadTemplateHeaderFile
    : ITrigger<
        UploadTemplateHeaderFile.UploadTemplateHeaderFileInput,
        UploadTemplateHeaderFile.UploadTemplateHeaderFileOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ITemplateService _templateService;
    private readonly ILogger<UploadTemplateHeaderFile> _logger;
    private readonly IBlobService _blobService;

    public UploadTemplateHeaderFile(
        IWabaService wabaService,
        ITemplateService templateService,
        ILogger<UploadTemplateHeaderFile> logger,
        IBlobService blobService)
    {
        _logger = logger;
        _wabaService = wabaService;
        _templateService = templateService;
        _blobService = blobService;
    }

    public class UploadTemplateHeaderFileInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonProperty("blob_storage_type")]
        public string? BlobStorageType { get; set; }

        [JsonConstructor]
        public UploadTemplateHeaderFileInput(string sleekflowCompanyId, string wabaId, string blobId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            BlobId = blobId;
        }
    }

    public class UploadTemplateHeaderFileOutput
    {
        [JsonProperty("header_handle")]
        public string HeaderHandle { get; set; }

        [JsonProperty("file_url")]
        public string FileUrl { get; set; }

        [JsonConstructor]
        public UploadTemplateHeaderFileOutput(string headerHandle, string fileUrl)
        {
            HeaderHandle = headerHandle;
            FileUrl = fileUrl;
        }
    }

    public async Task<UploadTemplateHeaderFileOutput> F(
        UploadTemplateHeaderFileInput uploadTemplateHeaderFileInput)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(
            uploadTemplateHeaderFileInput.WabaId,
            uploadTemplateHeaderFileInput.SleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            _logger.LogWarning(
                "Unable to locate any valid waba {Waba}",
                JsonConvert.SerializeObject(waba));
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var storageType = uploadTemplateHeaderFileInput.BlobStorageType ?? BlobStorageType.External;

        var blobDetail =
            await _blobService.GetBlobDetailAsync(uploadTemplateHeaderFileInput.BlobId, storageType);

        if (!blobDetail.HasValue)
        {
            throw new SfNotFoundObjectException("Unable to locate media");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var fileHandle = await _templateService.UploadTemplateHeaderFileAsync(
            waba.FacebookWabaId,
            blobDetail.Value.Details.ContentLength,
            blobDetail.Value.Details.ContentType,
            uploadTemplateHeaderFileInput.BlobId,
            blobDetail.Value.Content.ToArray(),
            hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null);

        return new UploadTemplateHeaderFileOutput(
            fileHandle,
            _blobService.GetBlobSasUrl(uploadTemplateHeaderFileInput.BlobId, storageType).Url);
    }
}