using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Scriban.Runtime;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactOwnerRelationshipsStepExecutor : IStepExecutor
{
}

public class UpdateContactOwnerRelationshipsStepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactOwnerRelationshipsStepArgs>>,
        IUpdateContactOwnerRelationshipsStepExecutor,
        IScopedService
{
    private readonly IStateAggregator _stateAggregator;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;
    private readonly ILogger<UpdateContactOwnerRelationshipsStepExecutor> _logger;

    public UpdateContactOwnerRelationshipsStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateAggregator stateAggregator,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander,
        ILogger<UpdateContactOwnerRelationshipsStepExecutor> logger)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateAggregator = stateAggregator;
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
        _logger = logger;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactOwnerRelationships",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateContactOwnerRelationshipsInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [JsonProperty("is_unassigned")]
        [Required]
        public bool IsUnassigned { get; set; }

        [JsonProperty("assignment_strategy")]
        [Required]
        public string AssignmentStrategy { get; set; }

        [JsonProperty("team_id")]
        public string? TeamId { get; set; }

        [JsonProperty("staff_id")]
        public string? StaffId { get; set; }

        [JsonProperty("assignment_counter")]
        public long? AssignmentCounter { get; set; }

        [JsonConstructor]
        public UpdateContactOwnerRelationshipsInput(
            string stateId,
            StateIdentity stateIdentity,
            string contactId,
            bool isUnassigned,
            string? teamId,
            string? staffId,
            long? assignmentCounter,
            string assignmentStrategy)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ContactId = contactId;
            IsUnassigned = isUnassigned;
            TeamId = teamId;
            StaffId = staffId;
            AssignmentCounter = assignmentCounter;
            AssignmentStrategy = assignmentStrategy;
        }
    }

    private async Task<UpdateContactOwnerRelationshipsInput> GetArgs(
        CallStep<UpdateContactOwnerRelationshipsStepArgs> callStep,
        ProxyState state)
    {
        ScriptArray? staffIds;
        ScriptArray? teamIds;
        string contactId;
        try
        {
            contactId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                                  ?? callStep.Args.ContactIdExpr);
            teamIds = callStep.Args.TeamIdsExpr == null
                ? null
                : (ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.TeamIdsExpr))!;
            staffIds = callStep.Args.StaffIdsExpr == null
                ? null
                : (ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.StaffIdsExpr))!;
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Failed to parse arguments for UpdateContactOwnerRelationshipsInput. JSON: {Args}",
                JsonConvert.SerializeObject(callStep));

            throw;
        }

        var assignmentStrategy = callStep.Args.Strategy;

        switch (assignmentStrategy)
        {
            case UpdateContactOwnerRelationshipsStepArgsStrategy.Unassigned:
            {
                return new UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    true,
                    null,
                    null,
                    null,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Company_RoundRobbin_All_Staffs:
            {
                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_company_staffs");

                return new UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    null,
                    null,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.RoundRobbin_TeamOnly:
            {
                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_team_id");
                var idx = Convert.ToInt32(increment % teamIds!.Count);
                var teamId = Convert.ToString(teamIds[idx]);

                return new UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    teamId,
                    null,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.RoundRobbin_StaffOnly:
            {
                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_staff_id");
                var idx = Convert.ToInt32(increment % staffIds!.Count);
                var staffId = Convert.ToString(staffIds[idx]);

                return new UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    null,
                    staffId,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_All_Staffs_In_Team:
            {
                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_team_staff_id");
                var teamId = Convert.ToString(teamIds![0]);

                return new UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    teamId,
                    null,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_Specific_Staffs_In_Team:
            {
                var increment = await _stateAggregator.IncrementSysCompanyVarAsync(
                    state,
                    nameof(UpdateContactOwnerRelationshipsStepExecutor) + "_team_specific_staff_id");
                var teamId = Convert.ToString(teamIds![0]);
                var idx = Convert.ToInt32(increment % staffIds!.Count);
                var staffId = Convert.ToString(staffIds[idx]);

                return new UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    teamId,
                    staffId,
                    increment,
                    assignmentStrategy);
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_Unassigned_Staff:
            {
                var teamId = Convert.ToString(teamIds![0]);

                return new UpdateContactOwnerRelationshipsInput(
                    state.Id,
                    state.Identity,
                    contactId,
                    false,
                    teamId,
                    null,
                    null,
                    assignmentStrategy);
            }
        }

#pragma warning disable S3928
        throw new ArgumentOutOfRangeException(nameof(callStep.Args.Strategy));
#pragma warning restore S3928
    }
}