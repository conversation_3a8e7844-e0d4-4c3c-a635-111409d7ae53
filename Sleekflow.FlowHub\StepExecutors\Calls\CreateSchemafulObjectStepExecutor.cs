﻿using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.ActionEvents.CrmHub;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateSchemafulObjectStepExecutor : IStepExecutor
{
}

public class CreateSchemafulObjectStepExecutor
    : GeneralStepExecutor<CallStep<CreateSchemafulObjectStepArgs>>,
        ICreateSchemafulObjectStepExecutor,
        IScopedService
{
    private const string AuditSource = "flow_builder";

    private readonly IStateEvaluator _stateEvaluator;
    private readonly IRequestClient<CreateSchemafulObjectRequest> _createSchemafulObjectRequestClient;

    public CreateSchemafulObjectStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IRequestClient<CreateSchemafulObjectRequest> createSchemafulObjectRequestClient)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _createSchemafulObjectRequestClient = createSchemafulObjectRequestClient;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _createSchemafulObjectRequestClient.GetResponse<CreateSchemafulObjectReply>(
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<CreateSchemafulObjectRequest> GetArgs(
        CallStep<CreateSchemafulObjectStepArgs> callStep,
        ProxyState state)
    {
        var schemaId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.SchemaIdExpr) ??
                                 callStep.Args.SchemaIdExpr);

        var primaryPropertyValue =
            callStep.Args.PrimaryPropertyValueExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.PrimaryPropertyValueExpr)
                            ?? callStep.Args.PrimaryPropertyValueExpr)
                : string.Empty;

        var propertyValues =
            callStep.Args.PropertyValuesKeyExprDict != null
                ? await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.PropertyValuesKeyExprDict)
                : new Dictionary<string, object?>();

        var sleekflowUserProfileId =
            callStep.Args.ContactIdExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                            ?? callStep.Args.ContactIdExpr)
                : string.Empty;

        var sleekflowCompanyId = state.Identity.SleekflowCompanyId;

        return new CreateSchemafulObjectRequest(
            schemaId,
            sleekflowCompanyId,
            primaryPropertyValue,
            propertyValues,
            sleekflowUserProfileId,
            AuditSource);
    }
}