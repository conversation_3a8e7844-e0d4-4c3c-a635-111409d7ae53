﻿using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using StackExchange.Redis;

namespace Sleekflow.FlowHub.Limiters;

public interface IRequestRateLimiter
{
    Task<bool> IsHitLimitAsync(
        string operationName,
        string requestKey,
        int windowSeconds,
        int maxRequestsAllowedWithinWindow);
}

public sealed class RequestRateLimiter : IRequestRateLimiter, ISingletonService
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ICacheConfig _cacheConfig;
    private readonly ILogger<RequestRateLimiter> _logger;

    private static LuaScript RateLimitScript => LuaScript.Prepare(
        """
        local current_time = redis.call('TIME')
        local trim_time = tonumber(current_time[1]) - @window
        redis.call('ZREMRANGEBYSCORE', @key, 0, trim_time)
        local request_count = redis.call('ZCARD',@key)

        if request_count < tonumber(@max_requests) then
            redis.call('ZADD', @key, current_time[1], current_time[1] .. current_time[2])
            redis.call('EXPIRE', @key, @window)
            return 0
        end
        return 1
        """);

    public RequestRateLimiter(
        IConnectionMultiplexer connectionMultiplexer,
        ICacheConfig cacheConfig,
        ILogger<RequestRateLimiter> logger)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _cacheConfig = cacheConfig;
        _logger = logger;
    }

    public async Task<bool> IsHitLimitAsync(
        string operationName,
        string requestKey,
        int windowSeconds,
        int maxRequestsAllowedWithinWindow)
    {
        if (windowSeconds <= 0
            || maxRequestsAllowedWithinWindow <= 0)
        {
            return false;
        }

        var database = _connectionMultiplexer.GetDatabase();
        requestKey = $"{_cacheConfig.CachePrefix}-{requestKey}";

        var result = (int)await database.ScriptEvaluateAsync(
            RateLimitScript,
            new
            {
                key = new RedisKey(requestKey),
                window = windowSeconds,
                max_requests = maxRequestsAllowedWithinWindow
            });

        var isHitLimit = result == 1;

        if (isHitLimit)
        {
            _logger.LogWarning(
                "{Operation} hit limit - Key: {Key}",
                operationName,
                requestKey);
        }

        return isHitLimit;
    }
}