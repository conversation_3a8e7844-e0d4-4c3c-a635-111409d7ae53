﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateContactV2StepExecutor : IStepExecutor
{
}

public class CreateContactV2StepExecutor
    : GeneralStepExecutor<CallStep<CreateContactV2StepArgs>>,
        ICreateContactV2StepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public CreateContactV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "CreateContact",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<CreateContactStepExecutor.CreateContactInput> GetArgs(
        CallStep<CreateContactV2StepArgs> callStep,
        ProxyState state)
    {
        var contactIdentifier = (string?) await _stateEvaluator.EvaluateTemplateStringExpressionAsync(
            state,
            callStep.Args.ContactIdentifier) ?? throw new InvalidOperationException("Missing contact identifier");

        var propertiesKeyExprDict = callStep.Args.PropertiesIdExprSet
            .GroupBy(x => x.PropertyId)
            .ToDictionary(x => x.Key, g => g.Last().PropertyValueExpr);

        var propertiesDict = new Dictionary<string, object?>();

        foreach (var entry in propertiesKeyExprDict)
        {
            propertiesDict[entry.Key] = string.IsNullOrWhiteSpace(entry.Value)
                ? null
                : await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, entry.Value);
        }

        return new CreateContactStepExecutor.CreateContactInput(
            state.Id,
            state.Identity,
            contactIdentifier,
            propertiesDict,
            state.TriggerEventBody.EventName);
    }
}