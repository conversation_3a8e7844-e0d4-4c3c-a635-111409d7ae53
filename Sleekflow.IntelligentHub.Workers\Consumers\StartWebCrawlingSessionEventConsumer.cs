using MassTransit;
using Microsoft.Extensions.Logging;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.Workers.Consumers;

public class StartWebCrawlingSessionEventConsumer : IConsumer<StartWebCrawlingSessionEvent>
{
    private readonly ILogger<StartWebCrawlingSessionEventConsumer> _logger;

    public StartWebCrawlingSessionEventConsumer(
        ILogger<StartWebCrawlingSessionEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<StartWebCrawlingSessionEvent> context)
    {
        _logger.LogInformation(
            "Event received: StartWebCrawlingSessionEvent for Company: {CompanyId}, URL: {Url}, SessionId: {SessionId}",
            context.Message.SleekflowCompanyId,
            context.Message.Url,
            context.Message.SessionId);
        
        // The actual orchestration will be started by the StartWebCrawlingSessionTrigger
        // This consumer is just for logging and potential additional processing
    }
} 