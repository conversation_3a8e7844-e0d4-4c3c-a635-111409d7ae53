using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Evaluator.Utils;

public static class AgentConfigUtils
{
    public static CompanyAgentConfig GetAgentConfig(
        string? tone = null,
        LeadNurturingTools? leadNurturingTools = null,
        List<EnricherConfig>? enricherConfigs = null,
        string? additionalInstructionCore = null,
        string? additionalInstructionStrategy = null,
        string? additionalInstructionResponse = null,
        string? additionalInstructionKnowledgeRetrieval = null,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        string? type = null,
        ToolsConfig? toolsConfig = null,
        List<Guardrail>? guardrails = null,
        bool isTranscriptionEnabled = false)
    {
        return new CompanyAgentConfig(
            "TEST",
            "Testing Agent",
            string.Empty,
            true,
            true,
            50,
            null,
            null,
            null,
            new PromptInstruction
            {
                Tone = tone ?? TargetToneTypes.Professional,
                AdditionalInstructionCore = additionalInstructionCore,
                AdditionalInstructionStrategy = additionalInstructionStrategy,
                AdditionalInstructionResponse = additionalInstructionResponse,
                AdditionalInstructionKnowledgeRetrieval = additionalInstructionKnowledgeRetrieval,
                Guardrails = guardrails
            },
            type ?? CompanyAgentTypes.Sales,
            AgentCollaborationModes.Default,
            leadNurturingTools: leadNurturingTools,
            createdAt: DateTimeOffset.Now,
            updatedAt: DateTimeOffset.Now,
            createdBy: null,
            updatedBy: null,
            eTag: null,
            enricherConfigs: enricherConfigs ?? [],
            knowledgeRetrievalConfig: knowledgeRetrievalConfig,
            toolsConfig: toolsConfig,
            actions: null,
            isTranscriptionEnabled: isTranscriptionEnabled);
    }
}