using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments;

public interface IFileDocumentChunkRepository : IRepository<FileDocumentChunk>
{
    Task<FileDocumentChunk?> GetFileDocumentChunkOrDefaultAsync(
        string fileDocumentChunkId,
        string sleekflowCompanyId,
        string documentId);

    Task<FileDocumentChunk> PatchAndGetFileDocumentChunkAsync(
        FileDocumentChunk fileDocumentChunk,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata);

    Task<(List<FileDocumentChunk> FileDocumentChunks, string? NextContinuationToken)> GetFileDocumentChunksAsync(
        string sleekflowCompanyId,
        string documentId,
        string? continuationToken,
        int limit);

    Task<List<FileDocumentChunk>> GetFileDocumentChunksAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> fileDocumentChunkIds,
        int limit);

    Task<List<FileDocumentChunk>> GetFileDocumentChunksAsync(string sleekflowCompanyId, string documentId);

    Task<List<string>> GetFileDocumentChunkIdsAsync(string sleekflowCompanyId, string documentId);
}

public class FileDocumentChunkRepository
    : BaseRepository<FileDocumentChunk>, IScopedService, IFileDocumentChunkRepository
{
    public FileDocumentChunkRepository(
        ILogger<FileDocumentChunkRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<FileDocumentChunk?> GetFileDocumentChunkOrDefaultAsync(
        string fileDocumentChunkId,
        string sleekflowCompanyId,
        string documentId)
    {
        return (await GetObjectsAsync(
                e =>
                    e.Id == fileDocumentChunkId
                    && e.SleekflowCompanyId == sleekflowCompanyId
                    && e.DocumentId == documentId))
            .FirstOrDefault();
    }

    public async Task<List<FileDocumentChunk>> GetFileDocumentChunksAsync(string sleekflowCompanyId, string documentId)
    {
        return await GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId);
    }

    public async Task<List<string>> GetFileDocumentChunkIdsAsync(string sleekflowCompanyId, string documentId)
    {
        var fileDocumentChunks = await GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId);
        return fileDocumentChunks.Select(e => e.Id).ToList();
    }

    public async Task<List<FileDocumentChunk>> GetFileDocumentChunksAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> fileDocumentChunkIds,
        int limit)
    {
        return await GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId
                && fileDocumentChunkIds.Contains(e.Id),
            limit);
    }

    public async Task<(List<FileDocumentChunk> FileDocumentChunks, string? NextContinuationToken)>
        GetFileDocumentChunksAsync(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit)
    {
        return await GetContinuationTokenizedObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId,
            continuationToken,
            limit);
    }

    public async Task<FileDocumentChunk> PatchAndGetFileDocumentChunkAsync(
        FileDocumentChunk fileDocumentChunk,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        return await PatchAndGetAsync(
            fileDocumentChunk.Id,
            fileDocumentChunk.SleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace(Chunk.PropertyNameContent, content),
                Replace(Chunk.PropertyNameContentEn, contentEn),
                Replace(Chunk.PropertyNameCategories, categories),
                Replace(IHasMetadata.PropertyNameMetadata, metadata),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
            });
    }
}