using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

public class CompensationCurrencyAttribute
{
    [JsonProperty(PropertyName = "id")]
    public int Id { get; set; }

    [JsonProperty(PropertyName = "name")]
    public string Name { get; set; }

    [JsonProperty(PropertyName = "attr_type")]
    public int AttrType { get; set; }

    [JsonProperty(PropertyName = "meta")]
    public CompensationCurrencyAttributeMeta Meta { get; set; }

    [JsonProperty(PropertyName = "value_display")]
    public CompensationCurrency ValueDisplay { get; set; }

    [JsonConstructor]
    public CompensationCurrencyAttribute(
        int id,
        string name,
        int attrType,
        CompensationCurrencyAttributeMeta meta,
        CompensationCurrency valueDisplay)
    {
        Id = id;
        Name = name;
        AttrType = attrType;
        Meta = meta;
        ValueDisplay = valueDisplay;
    }
}

public class CompensationCurrencyAttributeMeta
{
    [JsonProperty(PropertyName = "allow_blank")]
    public bool AllowBlank { get; set; }

    [JsonProperty(PropertyName = "options")]
    public List<CompensationCurrency> Options { get; set; }

    [JsonConstructor]
    public CompensationCurrencyAttributeMeta(
        bool allowBlank,
        List<CompensationCurrency> options)
    {
        AllowBlank = allowBlank;
        Options = options;
    }
}

public class CompensationCurrency
{
    [JsonProperty(PropertyName = "id")]
    public int Id { get; set; }

    [JsonProperty(PropertyName = "value")]
    public string Value { get; set; }

    [JsonProperty(PropertyName = "is_deletable")]
    public bool IsDeletable { get; set; }

    [JsonConstructor]
    public CompensationCurrency(
        int id,
        string value,
        bool isDeletable)
    {
        Id = id;
        Value = value;
        IsDeletable = isDeletable;
    }
}