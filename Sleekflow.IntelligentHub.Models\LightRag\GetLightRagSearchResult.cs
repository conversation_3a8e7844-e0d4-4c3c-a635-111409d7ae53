using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.LightRag;

public class GetLightRagSearchResultRequest
{
    [JsonProperty("query")]
    public string Query { get; set; }

    [JsonProperty("mode")]
    public string Mode { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("only_need_context")]
    public bool OnlyNeedContext { get; set; }

    [JsonProperty("high_level_keywords")]
    public List<string> HighLevelKeywords { get; set; }

    [JsonProperty("low_level_keywords")]
    public List<string> LowLevelKeywords { get; set; }

    [JsonProperty("top_k")]
    public int TopK { get; set; }

    [JsonConstructor]
    public GetLightRagSearchResultRequest(
        string query,
        string mode,
        string sleekflowCompanyId,
        bool onlyNeedContext,
        List<string> highLevelKeywords,
        List<string> lowLevelKeywords,
        int topK = 60)
    {
        Query = query;
        Mode = mode;
        SleekflowCompanyId = sleekflowCompanyId;
        OnlyNeedContext = onlyNeedContext;
        HighLevelKeywords = highLevelKeywords;
        LowLevelKeywords = lowLevelKeywords;
        TopK = topK;
    }
}

public class GetLightRagSearchResultResponse
{
    [JsonProperty("response")]
    public string Response { get; set; }

    [JsonConstructor]
    public GetLightRagSearchResultResponse(string response)
    {
        Response = response;
    }
}