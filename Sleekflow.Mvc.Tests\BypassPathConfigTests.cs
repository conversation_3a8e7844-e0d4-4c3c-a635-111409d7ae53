namespace Sleekflow.Mvc.Tests;

using Microsoft.AspNetCore.Http;
using NUnit.Framework;
using Sleekflow.Mvc.Configs;

[TestFixture]
public class BypassPathConfigTests
{
    [OneTimeSetUp]
    public void Init()
    {
        // Since BypassPathConfig is a static class with constant values,
        // we don't need any initialization.
        // This method is added for consistency with test structure pattern
    }

    [TestCase("/Internals/GetUserAuthenticationDetails", true)]
    [TestCase("/Internals/PropagateBatchToProvider", true)]
    [TestCase("/Management/EnabledFeatures/IsFeatureEnabledForCompany", true)]
    [TestCase("/Balances/GetWhatsappCloudApiBusinessBalances", true)]
    [TestCase("/Authorized/Rbac/IsRbacEnabled", true)]
    [TestCase("/authorized/Rbac/IsRbacEnabled", true)]
    [TestCase("/Management/Rbac/IsRbacEnabled", true)]
    [TestCase("/Management/Rbac/GetRolesByStaffId", true)]
    [TestCase("/management/Rbac/IsRbacEnabled", true)]
    [TestCase("/management/Rbac/GetRolesByStaffId", true)]
    [TestCase("/CrmHubConfigs/GetCrmHubConfig", true)]
    [TestCase("/SomeOtherPath", false)]
    [TestCase("/api/v1/users", false)]
    public void IsPathBypassed_ShouldReturnExpectedResult(string path, bool expected)
    {
        // Arrange
        var pathString = new PathString(path);

        // Act
        var result = BypassPathConfig.IsPathBypassed(pathString);

        // Assert
        Assert.That(result, Is.EqualTo(expected));
    }

    [TestCase("/INTERNALS/GETUSERAUTHENTICATIONDETAILS")] // uppercase
    [TestCase("/internals/getuserauthenticationdetails")] // lowercase
    public void IsPathBypassed_ShouldBeCaseInsensitive(string path)
    {
        // Arrange
        var pathString = new PathString(path);

        // Act
        var result = BypassPathConfig.IsPathBypassed(pathString);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public void IsPathBypassed_ShouldHandleTrailingSlashes()
    {
        // Arrange
        var paths = new[]
        {
            "/Internals/GetUserAuthenticationDetails/",
            "/Management/Rbac/IsRbacEnabled//",
            "/CrmHubConfigs/GetCrmHubConfig///",
        };

        foreach (var path in paths)
        {
            // Act
            var result = BypassPathConfig.IsPathBypassed(new PathString(path));

            // Assert
            Assert.That(result, Is.True, $"Path with trailing slashes should be bypassed: {path}");
        }
    }

    [Test]
    public void IsPathBypassed_ShouldHandleNullAndEmptyPaths()
    {
        // Arrange
        var nullPath = PathString.Empty;
        var emptyPath = new PathString(string.Empty);

        // Act & Assert
        Assert.Multiple(() =>
        {
            Assert.That(BypassPathConfig.IsPathBypassed(nullPath), Is.False, "Null path should not be bypassed");
            Assert.That(BypassPathConfig.IsPathBypassed(emptyPath), Is.False, "Empty path should not be bypassed");
        });
    }
}
