using Pulumi;
using Pulumi.AzureNative.DocumentDB.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.MessagingHub;

public class MessagingHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public MessagingHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class MessagingHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public MessagingHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public MessagingHubDbOutput InitMessagingHubDb()
    {
        const string cosmosDbId = "messaginghubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 30000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var containerParams = new ContainerParam[]
        {
            new (
                "sent_message",
                "sent_message",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                3600 * 24 * 7
            ),
            new (
                "state",
                "state",
                new List<string>()
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "waba",
                "waba",
                new List<string>
                {
                    "/facebook_waba_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/facebook_waba_id"
                    },
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/waba_phone_numbers/facebook_phone_number_id"
                    }
                }
            ),
            new (
                "audit_log",
                "audit_log",
                new List<string>
                {
                    "/auditing_partition_id"
                },
                3600 * 24 * 180
            ),
            new (
                "message",
                "message",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                3600 * 24 * 30
            ),
            new (
                "business_balance",
                "business_balance",
                new List<string>
                {
                    "/facebook_business_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/facebook_business_id"
                    }
                }
            ),
            new (
                "business_balance_transaction_log",
                "business_balance_transaction_log",
                new List<string>
                {
                    "/facebook_business_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/unique_id"
                    },
                },
                MaxThroughput: _myConfig.Name == "production" ? 5000 : 1000
            ),
            new (
                "business_balance_auto_top_up_profile",
                "business_balance_auto_top_up_profile",
                new List<string>
                {
                    "/facebook_business_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/facebook_business_id"
                    },
                },
                MaxThroughput: 1000
            ),
            new (
                "waba_balance_auto_top_up_profile",
                "waba_balance_auto_top_up_profile",
                new List<string>()
                {
                    "/facebook_waba_id"
                },
                UniqueKeyArgsList: new List<UniqueKeyArgs>
                {
                    new DocumentDB.Inputs.UniqueKeyArgs
                    {
                        Paths = "/facebook_waba_id"
                    },
                },
                MaxThroughput: 1000)
        };

        var _ = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new MessagingHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}