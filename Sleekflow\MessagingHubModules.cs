﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.OpenTelemetry.MessagingHub;
using Sleekflow.Persistence.MessagingHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class MessagingHubModules
{
    public static void BuildMessagingHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<IMessagingHubDbConfig>(new Mock<IMessagingHubDbConfig>().Object);
        b.<PERSON>d<PERSON><PERSON><PERSON><IMessagingHubDbResolver>(new Mock<IMessagingHubDbResolver>().Object);

#else
        var messagingHubDbConfig = new MessagingHubDbConfig();

        b.Add<PERSON><IMessagingHubDbConfig>(messagingHubDbConfig);
        b.Add<PERSON>ingleton<IMessagingHubDbResolver, MessagingHubDbResolver>();
#endif
    }

    public static void BuildOpenTelemetryServices(IServiceCollection b)
    {
        ModuleUtils.BuildOpenTelemetryServices(b);
#if SWAGGERGEN
        b.AddSingleton<IMessagingHubMeters>(new Mock<IMessagingHubMeters>().Object);
        b.AddSingleton<IMessagingChannelErrorMeters>(new Mock<IMessagingChannelErrorMeters>().Object);
#else
        b.AddSingleton<IMessagingHubMeters, MessagingHubMeters>();
        b.AddSingleton<IMessagingChannelErrorMeters, MessagingChannelErrorMeters>();
#endif
    }
}