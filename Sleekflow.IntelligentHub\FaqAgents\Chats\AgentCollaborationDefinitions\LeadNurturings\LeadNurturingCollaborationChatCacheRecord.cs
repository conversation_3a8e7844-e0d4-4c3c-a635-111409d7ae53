using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public class LeadNurturingCollaborationChatCacheRecord
{
    [JsonProperty("group_chat_id")]
    public string GroupChatIdStr { get; set; }

    [JsonProperty("all_confirmed_knowledge_records")]
    public Dictionary<string, string> AllConfirmedKnowledgeRecords { get; set; }

    [JsonProperty("last_response_agent_reply")]
    public string? LastResponseAgentReply { get; set; }

    [JsonConstructor]
    public LeadNurturingCollaborationChatCacheRecord(
        string groupChatIdStr,
        Dictionary<string, string>? allConfirmedKnowledgeRecords,
        string? lastResponseAgentReply = null)
    {
        GroupChatIdStr = groupChatIdStr;
        AllConfirmedKnowledgeRecords = allConfirmedKnowledgeRecords ?? new Dictionary<string, string>();
        LastResponseAgentReply = lastResponseAgentReply;
    }
}