﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.GoogleSheets;

public class SubscriptionsCheckEnd
{
    private readonly IGoogleSheetsSubscriptionRepository _googleSheetsSubscriptionRepository;

    public SubscriptionsCheckEnd(
        IGoogleSheetsSubscriptionRepository googleSheetsSubscriptionRepository)
    {
        _googleSheetsSubscriptionRepository = googleSheetsSubscriptionRepository;
    }

    public class SubscriptionsCheckEndInput
    {
        [JsonProperty("subscription")]
        [Required]
        public GoogleSheetsSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonProperty("last_execution_start_time")]
        [Required]
        public DateTimeOffset LastExecutionStartTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckEndInput(
            GoogleSheetsSubscription subscription,
            DateTimeOffset? lastObjectModificationTime,
            DateTimeOffset lastExecutionStartTime)
        {
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
            LastExecutionStartTime = lastExecutionStartTime;
        }
    }

    [Function("GoogleSheets_SubscriptionsCheck_End")]
    public async Task End(
        [ActivityTrigger]
        SubscriptionsCheckEndInput subscriptionsCheckEndInput)
    {
        var patchOperations = new List<PatchOperation>
        {
            PatchOperation.Replace(
                $"/{GoogleSheetsSubscription.PropertyNameDurablePayload}",
                (HttpManagementPayload?) null),
            PatchOperation.Replace(
                $"/{GoogleSheetsSubscription.PropertyNameLastExecutionStartTime}",
                subscriptionsCheckEndInput.LastExecutionStartTime),
        };
        if (subscriptionsCheckEndInput.LastObjectModificationTime != null)
        {
            patchOperations.Add(
                PatchOperation.Replace(
                    $"/{GoogleSheetsSubscription.PropertyNameLastObjectModificationTime}",
                    subscriptionsCheckEndInput.LastObjectModificationTime));
        }

        await _googleSheetsSubscriptionRepository.PatchAsync(
            subscriptionsCheckEndInput.Subscription.Id,
            subscriptionsCheckEndInput.Subscription.SleekflowCompanyId,
            patchOperations);
    }
}