﻿using System.Net.Mime;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Sleekflow.Mvc.Https.Builders;

namespace Sleekflow.FlowHub.Tests.Https.Builders;

public class HttpRequestBuilderTests
{
    private const string BaseUrl = "https://www.google.com/";
    private readonly IHttpRequestBuilder _requestBuilder = new HttpRequestBuilder();

    [Test]
    public void BuildAsync_GivenNullHeaders_ShouldThrowInvalidOperationException()
    {
        // Act
        var action = () => _requestBuilder.BuildAsync(
            BaseUrl,
            null,
            null,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            () =>
            {
                var exception = Assert.ThrowsAsync<InvalidOperationException>(
                    async () => await action());
                Assert.That(exception, Is.Not.Null);
                Assert.That(
                    exception!.Message,
                    Is.EqualTo($"Missing required header {HeaderNames.ContentType}"));
            });
    }

    [Test]
    public void BuildAsync_GivenContentTypeHeaderNotProvided_ShouldThrowInvalidOperationException()
    {
        // Act
        var action = () => _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>(),
            null,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            () =>
            {
                var exception = Assert.ThrowsAsync<InvalidOperationException>(
                    async () => await action());
                Assert.That(exception, Is.Not.Null);
                Assert.That(
                    exception!.Message,
                    Is.EqualTo($"Missing required header {HeaderNames.ContentType}"));
            });
    }

    [Test]
    public void BuildAsync_GivenUnsupportedContentTypeHeader_ShouldThrowNotImplementedException()
    {
        // Act
        var action = () => _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, MediaTypeNames.Application.Soap
                }
            },
            null,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            () =>
            {
                var exception = Assert.ThrowsAsync<NotImplementedException>(
                    async () => await action());
                Assert.That(exception, Is.Not.Null);
                Assert.That(
                    exception!.Message,
                    Is.EqualTo($"Content type {MediaTypeNames.Application.Soap} is not supported for HTTP POST step"));
            });
    }

    [Test]
    public async Task BuildAsync_GivenMultipleHeaders_ShouldPopulateRequestHeadersCorrectly()
    {
        // Arrange
        KeyValuePair<string, string> header = new("X-API-Key", "this-is-an-api-key");

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                [HeaderNames.ContentType] = MediaTypeNames.Application.Json,
                [header.Key] = header.Value
            },
            null,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<StringContent>());
                Assert.That(request.Headers.Count(), Is.EqualTo(1));
                Assert.That(request.Headers.Contains(header.Key), Is.True);
                Assert.That(request.Headers.GetValues(header.Key), Is.EqualTo(new[] { header.Value }));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStr_And_ApplicationJsonContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentObj = new
        {
            FirstName = "First name", LastName = "Last name", Age = 18
        };

        var contentStr = JsonConvert.SerializeObject(contentObj);

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, MediaTypeNames.Application.Json
                }
            },
            contentStr,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<StringContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo(MediaTypeNames.Application.Json));
                Assert.That(
                    JsonConvert.DeserializeObject(await request.Content!.ReadAsStringAsync(), contentObj.GetType()),
                    Is.EqualTo(contentObj));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyDict_And_ApplicationJsonContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentObj = new
        {
            FirstName = "First name", LastName = "Last name", Age = 18
        };

        var contentStr = JsonConvert.SerializeObject(contentObj);
        var bodyDict = JsonConvert.DeserializeObject<Dictionary<string, object?>>(contentStr);

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, MediaTypeNames.Application.Json
                }
            },
            null,
            bodyDict,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<StringContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo(MediaTypeNames.Application.Json));
                Assert.That(
                    JsonConvert.DeserializeObject(await request.Content!.ReadAsStringAsync(), contentObj.GetType()),
                    Is.EqualTo(contentObj));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStr_And_TextPlainContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        const string contentStr = "test string";

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, MediaTypeNames.Text.Plain
                }
            },
            contentStr,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<StringContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo(MediaTypeNames.Text.Plain));
                Assert.That(
                    await request.Content!.ReadAsStringAsync(),
                    Is.EqualTo(contentStr));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStr_And_TextHtmlContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        const string contentStr =
            """
            <html>
                <body>
                    <h1>Hello world</h1>
                </body>
            </html>
            """;

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, MediaTypeNames.Text.Html
                }
            },
            contentStr,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<StringContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo(MediaTypeNames.Text.Html));
                Assert.That(
                    await request.Content!.ReadAsStringAsync(),
                    Is.EqualTo(contentStr));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStr_And_TextXmlContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        const string contentStr =
            """
            <note>
                <to>Tove</to>
                <from>Jani</from>
                <heading>Reminder</heading>
                <body>Don't forget me this weekend!</body>
            </note>
            """;

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, MediaTypeNames.Text.Xml
                }
            },
            contentStr,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<StringContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo(MediaTypeNames.Text.Xml));
                Assert.That(
                    await request.Content!.ReadAsStringAsync(),
                    Is.EqualTo(contentStr));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStr_And_ApplicationXmlContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        const string contentStr =
            """
            <note>
                <to>Tove</to>
                <from>Jani</from>
                <heading>Reminder</heading>
                <body>Don't forget me this weekend!</body>
            </note>
            """;

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, MediaTypeNames.Application.Xml
                }
            },
            contentStr,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<StringContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo(MediaTypeNames.Application.Xml));
                Assert.That(
                    await request.Content!.ReadAsStringAsync(),
                    Is.EqualTo(contentStr));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStr_And_FormUrlEncodedContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentObj = new
        {
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            Age = 18,
            Address = new
            {
                ZipCode = "53900"
            }
        };

        var contentStr = JsonConvert.SerializeObject(contentObj);

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, "application/x-www-form-urlencoded"
                }
            },
            contentStr,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<FormUrlEncodedContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo("application/x-www-form-urlencoded"));
                Assert.That(
                    await request.Content!.ReadAsStringAsync(),
                    Is.EqualTo("FirstName=First+name&LastName=Last+name&Email=abc%40test.xyz&Age=18&Address=%7B%22ZipCode%22%3A%2253900%22%7D"));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyDict_And_FormUrlEncodedContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentDict = new Dictionary<string, object?>
        {
            ["FirstName"] = "First name",
            ["LastName"] = "Last name",
            ["Email"] = "<EMAIL>",
            ["Age"] = 18,
            ["Address"] = new
            {
                ZipCode = "53900"
            }
        };

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, "application/x-www-form-urlencoded"
                }
            },
            null,
            contentDict,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<FormUrlEncodedContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo("application/x-www-form-urlencoded"));
                Assert.That(
                    await request.Content!.ReadAsStringAsync(),
                    Is.EqualTo("FirstName=First+name&LastName=Last+name&Email=abc%40test.xyz&Age=18&Address=%7B%22ZipCode%22%3A%2253900%22%7D"));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStrAndBodyDict_And_FormUrlEncodedContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentObj = new
        {
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>"
        };

        var contentStr = JsonConvert.SerializeObject(contentObj);

        var contentDict = new Dictionary<string, object?>
        {
            ["Age"] = 18,
            ["Address"] = new
            {
                ZipCode = "53900"
            }
        };

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, "application/x-www-form-urlencoded"
                }
            },
            contentStr,
            contentDict,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<FormUrlEncodedContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo("application/x-www-form-urlencoded"));
                Assert.That(
                    await request.Content!.ReadAsStringAsync(),
                    Is.EqualTo("FirstName=First+name&LastName=Last+name&Email=abc%40test.xyz&Age=18&Address=%7B%22ZipCode%22%3A%2253900%22%7D"));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStr_And_FormDataContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentObj = new
        {
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>",
            Age = 18,
            Address = new
            {
                ZipCode = "53900"
            }
        };

        var contentStr = JsonConvert.SerializeObject(contentObj);

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, "multipart/form-data"
                }
            },
            contentStr,
            null,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<MultipartFormDataContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo("multipart/form-data"));
                Assert.That(request.Content!.Headers.ContentLength, Is.GreaterThan(0));

                var content = await request.Content.ReadAsStringAsync();

                Assert.That(content, Does.Contain("FirstName"));
                Assert.That(content, Does.Contain("LastName"));
                Assert.That(content, Does.Contain("Email"));
                Assert.That(content, Does.Contain("Age"));
                Assert.That(content, Does.Contain("Address"));
                Assert.That(content, Does.Contain("ZipCode"));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyDict_And_FormDataContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentDict = new Dictionary<string, object?>()
        {
            ["FirstName"] = "First name",
            ["LastName"] = "Last name",
            ["Email"] = "<EMAIL>",
            ["Age"] = 18,
            ["Address"] = new
            {
                ZipCode = "53900"
            }
        };

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, "multipart/form-data"
                }
            },
            null,
            contentDict,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<MultipartFormDataContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo("multipart/form-data"));
                Assert.That(request.Content!.Headers.ContentLength, Is.GreaterThan(0));

                var content = await request.Content.ReadAsStringAsync();

                Assert.That(content, Does.Contain("FirstName"));
                Assert.That(content, Does.Contain("LastName"));
                Assert.That(content, Does.Contain("Email"));
                Assert.That(content, Does.Contain("Age"));
                Assert.That(content, Does.Contain("Address"));
                Assert.That(content, Does.Contain("ZipCode"));
            });
    }

    [Test]
    public async Task BuildAsync_GivenBodyStrAndBodyDict_And_FormDataContentTypeHeader_ShouldReturnStringContent()
    {
        // Arrange
        var contentObj = new
        {
            FirstName = "First name",
            LastName = "Last name",
            Email = "<EMAIL>"
        };

        var contentStr = JsonConvert.SerializeObject(contentObj);

        var contentDict = new Dictionary<string, object?>
        {
            ["Age"] = 18,
            ["Address"] = new
            {
                ZipCode = "53900"
            }
        };

        // Act
        var request = await _requestBuilder.BuildAsync(
            BaseUrl,
            new Dictionary<string, object?>()
            {
                {
                    HeaderNames.ContentType, "multipart/form-data"
                }
            },
            contentStr,
            contentDict,
            HttpMethod.Post);

        // Assert
        Assert.Multiple(
            async () =>
            {
                Assert.That(request.RequestUri!.AbsoluteUri, Is.EqualTo(BaseUrl));
                Assert.That(request, Is.Not.Null);
                Assert.That(request.Content, Is.TypeOf<MultipartFormDataContent>());
                Assert.That(
                    request.Content!.Headers.ContentType!.MediaType,
                    Is.EqualTo("multipart/form-data"));
                Assert.That(request.Content!.Headers.ContentLength, Is.GreaterThan(0));

                var content = await request.Content.ReadAsStringAsync();

                Assert.That(content, Does.Contain("FirstName"));
                Assert.That(content, Does.Contain("LastName"));
                Assert.That(content, Does.Contain("Email"));
                Assert.That(content, Does.Contain("Age"));
                Assert.That(content, Does.Contain("Address"));
                Assert.That(content, Does.Contain("ZipCode"));
            });
    }
}