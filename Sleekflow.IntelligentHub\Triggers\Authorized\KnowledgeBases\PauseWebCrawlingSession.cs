using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.WebCrawlingSessions;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class PauseWebCrawlingSession
    : ITrigger<PauseWebCrawlingSession.PauseWebCrawlingSessionInput,
        PauseWebCrawlingSession.PauseWebCrawlingSessionOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IWebCrawlingSessionService _webCrawlingSessionService;

    public PauseWebCrawlingSession(
        ISleekflowAuthorizationContext authorizationContext,
        IWebCrawlingSessionService webCrawlingSessionService)
    {
        _authorizationContext = authorizationContext;
        _webCrawlingSessionService = webCrawlingSessionService;
    }

    public class PauseWebCrawlingSessionInput
    {
        [JsonProperty("web_crawling_session_id")]
        [Required]
        public string WebCrawlingSessionId { get; set; }

        [JsonConstructor]
        public PauseWebCrawlingSessionInput(string webCrawlingSessionId)
        {
            WebCrawlingSessionId = webCrawlingSessionId;
        }
    }

    public class PauseWebCrawlingSessionOutput
    {
        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonProperty("base_url")]
        public string BaseUrl { get; set; }

        [JsonProperty("crawling_results")]
        public List<CrawlingResult> CrawlingResults { get; set; }

        [JsonConstructor]
        public PauseWebCrawlingSessionOutput(string status, string baseUrl, List<CrawlingResult> crawlingResults)
        {
            Status = status;
            BaseUrl = baseUrl;
            CrawlingResults = crawlingResults;
        }
    }

    public async Task<PauseWebCrawlingSessionOutput> F(
        PauseWebCrawlingSessionInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;

        var session = await _webCrawlingSessionService.PauseWebCrawlingSessionAsync(
            sleekflowCompanyId,
            input.WebCrawlingSessionId);

        return new PauseWebCrawlingSessionOutput(
            session.Status,
            session.BaseUrl,
            session.CrawlingResults);
    }
}