using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class CollaboratorRemovedLogData
{
    [JsonProperty("collaborators_removed")]
    public List<AssigneeLogData> CollaboratorsRemoved { get; set; }

    [JsonConstructor]
    public CollaboratorRemovedLogData(List<AssigneeLogData> collaboratorsRemoved)
    {
        CollaboratorsRemoved = collaboratorsRemoved;
    }
}