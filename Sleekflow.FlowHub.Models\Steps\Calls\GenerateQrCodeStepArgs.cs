using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class GenerateQrCodeStepArgs : TypedCallStepArgs
{
    public const string CallName = "utils.v1.generate-qr-code";

    [Required]
    [JsonProperty("url__expr")]
    public string UrlExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => string.Empty;

    [JsonConstructor]
    public GenerateQrCodeStepArgs(string urlExpr)
    {
        UrlExpr = urlExpr;
    }
}