using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class ContactMessageObject : BaseMessageObject
{
    [JsonProperty("addresses")]
    public List<ContactMessageObjectAddress> Addresses { get; set; }

    [JsonProperty("birthday")]
    public string Birthday { get; set; }

    [JsonProperty("emails")]
    public List<ContactMessageObjectEmail> Emails { get; set; }

    [JsonProperty("name")]
    public ContactMessageObjectName Name { get; set; }

    [JsonProperty("org")]
    public ContactMessageObjectOrg Org { get; set; }

    [JsonProperty("ims")]
    public List<ContactMessageObjectIm> Ims { get; set; }

    [JsonProperty("phones")]
    public List<ContactMessageObjectPhone> Phones { get; set; }

    [JsonProperty("urls")]
    public List<ContactMessageObjectUrl> Urls { get; set; }

    [JsonConstructor]
    public ContactMessageObject(
        List<ContactMessageObjectAddress> addresses,
        string birthday,
        List<ContactMessageObjectEmail> emails,
        ContactMessageObjectName name,
        ContactMessageObjectOrg org,
        List<ContactMessageObjectIm> ims,
        List<ContactMessageObjectPhone> phones,
        List<ContactMessageObjectUrl> urls)
    {
        Addresses = addresses;
        Birthday = birthday;
        Emails = emails;
        Name = name;
        Org = org;
        Ims = ims;
        Phones = phones;
        Urls = urls;
    }
}

public class ContactMessageObjectEmail
{
    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonConstructor]
    public ContactMessageObjectEmail(string email, string type)
    {
        Email = email;
        Type = type;
    }
}

public class ContactMessageObjectAddress
{
    [JsonProperty("street")]
    public string Street { get; set; }

    [JsonProperty("city")]
    public string City { get; set; }

    [JsonProperty("state")]
    public string State { get; set; }

    [JsonProperty("zip")]
    public string Zip { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("country_code")]
    public string CountryCode { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonConstructor]
    public ContactMessageObjectAddress(
        string street,
        string city,
        string state,
        string zip,
        string country,
        string countryCode,
        string type)
    {
        Street = street;
        City = city;
        State = state;
        Zip = zip;
        Country = country;
        CountryCode = countryCode;
        Type = type;
    }
}

public class ContactMessageObjectName
{
    [JsonProperty("formatted_name")]
    public string FormattedName { get; set; }

    [JsonProperty("first_name")]
    public string FirstName { get; set; }

    [JsonProperty("last_name")]
    public string LastName { get; set; }

    [JsonProperty("middle_name")]
    public string MiddleName { get; set; }

    [JsonProperty("suffix")]
    public string Suffix { get; set; }

    [JsonProperty("prefix")]
    public string Prefix { get; set; }

    [JsonConstructor]
    public ContactMessageObjectName(
        string formattedName,
        string firstName,
        string lastName,
        string middleName,
        string suffix,
        string prefix)
    {
        FormattedName = formattedName;
        FirstName = firstName;
        LastName = lastName;
        MiddleName = middleName;
        Suffix = suffix;
        Prefix = prefix;
    }
}

public class ContactMessageObjectOrg
{
    [JsonProperty("company")]
    public string Company { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("department")]
    public string Department { get; set; }

    [JsonConstructor]
    public ContactMessageObjectOrg(string company, string title, string department)
    {
        Company = company;
        Title = title;
        Department = department;
    }
}

public class ContactMessageObjectIm
{
    [JsonProperty("service")]
    public string Service { get; set; }

    [JsonProperty("user_id")]
    public string UserId { get; set; }

    [JsonConstructor]
    public ContactMessageObjectIm(string service, string userId)
    {
        Service = service;
        UserId = userId;
    }
}

public class ContactMessageObjectPhone
{
    [JsonProperty("phone")]
    public string Phone { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("wa_id")]
    public string WaId { get; set; }

    [JsonConstructor]
    public ContactMessageObjectPhone(string phone, string type, string waId)
    {
        Phone = phone;
        Type = type;
        WaId = waId;
    }
}

public class ContactMessageObjectUrl
{
    [JsonProperty("url")]
    public string Url { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonConstructor]
    public ContactMessageObjectUrl(string url, string type)
    {
        Url = url;
        Type = type;
    }
}