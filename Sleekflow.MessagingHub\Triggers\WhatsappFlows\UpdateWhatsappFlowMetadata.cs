using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Models.MessageObjects.FlowObjects;
using GraphApi.Client.Payloads.WhatsappFlows;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.WhatsappFlows;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.MessagingHub.Triggers.WhatsappFlows;

[TriggerGroup(ControllerNames.WhatsappFlows)]
public class UpdateWhatsappFlowMetadata(IWabaService wabaService, IWhatsappFlowService whatsappFlowService)
    : ITrigger<UpdateWhatsappFlowMetadata.UpdateWhatsappFlowMetadataInput, UpdateWhatsappFlowMetadata.UpdateWhatsappFlowMetadataOutput>
{
    [method: JsonConstructor]
    public class UpdateWhatsappFlowMetadataInput(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        string flowId,
        WhatsappCloudApiUpdateFlowMetadataObject flowMetadata,
        string sleekflowStaffId) : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; } = sleekflowCompanyId;

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; } = messagingHubWabaId;

        [Required]
        [JsonProperty("flow_id")]
        public string FlowId { get; set; } = flowId;

        [Required]
        [ValidateObject]
        [JsonProperty("flow_metadata")]
        public WhatsappCloudApiUpdateFlowMetadataObject FlowMetadata { get; set; } = flowMetadata;

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; } = sleekflowStaffId;
    }

    [method: JsonConstructor]
    public class UpdateWhatsappFlowMetadataOutput(GetFlowResponse updatedFlowDetails)
    {
        [JsonProperty("updated_flow_details")]
        public GetFlowResponse UpdatedFlowDetails { get; set; } = updatedFlowDetails;
    }

    public async Task<UpdateWhatsappFlowMetadataOutput> F(UpdateWhatsappFlowMetadataInput input)
    {
        var waba = await wabaService.GetWabaOrDefaultAsync(input.MessagingHubWabaId, input.SleekflowCompanyId);

        if (waba == null)
        {
            throw new SfNotFoundObjectException(input.MessagingHubWabaId);
        }

        return new UpdateWhatsappFlowMetadataOutput(
            await whatsappFlowService.UpdateFlowMetadataAsync(
                waba.FacebookWabaId,
                input.FlowId,
                input.FlowMetadata,
                input.SleekflowStaffId));
    }
}