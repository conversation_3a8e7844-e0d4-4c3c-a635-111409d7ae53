﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderObjectsCountV2 : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderObjectsCountV2(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderObjectsCountV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public GetProviderObjectsCountV2Input(
            string sleekflowCompanyId,
            string providerName,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class GetProviderObjectsCountV2Output
    {
        [JsonProperty("count")]
        [Required]
        public long Count { get; set; }

        [JsonConstructor]
        public GetProviderObjectsCountV2Output(
            long count)
        {
            Count = count;
        }
    }

    public async Task<GetProviderObjectsCountV2Output> F(
        GetProviderObjectsCountV2Input getProviderObjectsCountV2Input)
    {
        var providerService = _providerSelector.GetProviderService(getProviderObjectsCountV2Input.ProviderName);

        var getObjectsCountOutput = await providerService.GetObjectsCountAsync(
            getProviderObjectsCountV2Input.SleekflowCompanyId,
            getProviderObjectsCountV2Input.EntityTypeName,
            getProviderObjectsCountV2Input.FilterGroups);

        return new GetProviderObjectsCountV2Output(getObjectsCountOutput.Count);
    }
}