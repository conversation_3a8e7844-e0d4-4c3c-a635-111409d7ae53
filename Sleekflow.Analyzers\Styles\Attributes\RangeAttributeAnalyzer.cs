using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class RangeAttributeAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1101";
    public const string Category = "Design";

    private static readonly LocalizableString Title =
        "Numeric properties in Input classes should have [Range] attribute";

    private static readonly LocalizableString MessageFormat = "The property '{0}' should have the [Range] attribute";

    private static readonly LocalizableString Description =
        "Numeric properties in Input classes should have [Range] attribute.";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSyntaxNodeAction(AnalyzeNode, SyntaxKind.PropertyDeclaration);
    }

    private static void AnalyzeNode(SyntaxNodeAnalysisContext context)
    {
        var propertyDeclaration = (PropertyDeclarationSyntax) context.Node;
        var propertySymbol = context.SemanticModel.GetDeclaredSymbol(propertyDeclaration);

        if (propertySymbol == null
            || propertySymbol.ContainingType == null)
        {
            return;
        }

        if (!propertySymbol.ContainingType.Name.EndsWith("Input"))
        {
            return;
        }

        if (!IsNumericType(propertySymbol.Type))
        {
            return;
        }

        var rangeAttribute = propertySymbol
            .GetAttributes()
            .FirstOrDefault(a => a.AttributeClass?.Name == "RangeAttribute");

        if (rangeAttribute != null)
        {
            return;
        }

        var diagnostic = Diagnostic.Create(Rule, propertyDeclaration.GetLocation(), propertySymbol.Name);
        context.ReportDiagnostic(diagnostic);
    }

    private static bool IsNumericType(ITypeSymbol typeSymbol)
    {
        switch (typeSymbol.SpecialType)
        {
            case SpecialType.System_Byte:
            case SpecialType.System_SByte:
            case SpecialType.System_UInt16:
            case SpecialType.System_UInt32:
            case SpecialType.System_UInt64:
            case SpecialType.System_Int16:
            case SpecialType.System_Int32:
            case SpecialType.System_Int64:
            case SpecialType.System_Decimal:
            case SpecialType.System_Single:
            case SpecialType.System_Double:
                return true;
            default:
                return false;
        }
    }
}