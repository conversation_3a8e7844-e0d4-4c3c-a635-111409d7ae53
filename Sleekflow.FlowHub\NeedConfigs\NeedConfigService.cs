﻿using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Scriban;
using Scriban.Runtime;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Blobs;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.States.Functions;
using Sleekflow.Locks;

namespace Sleekflow.FlowHub.NeedConfigs;

public interface INeedConfigService
{
    Task<List<ActionConfig>> GetActionsAsync(
        string? version);

    Task<List<ActionNeedConfig>> GetActionNeedsAsync(
        string sleekflowCompanyId,
        string actionGroup,
        string actionSubgroup,
        string? version,
        Dictionary<string, object?>? parameters);

    Task<List<TriggerConfig>> GetTriggersAsync(string? version);

    Task<List<TriggerNeedConfig>> GetTriggerNeedsAsync(
        string? version,
        string triggerId,
        Dictionary<string, object?>? parameters);

    Task<List<ActionConfigDto>> CreateActionsAsync(
        string? version,
        List<ActionConfigDto> actions);

    Task<List<ActionNeedConfig>> CreateActionNeedsAsync(
        string? version,
        List<ActionNeedConfig> needs);

    Task<List<TriggerConfig>> CreateTriggersAsync(
        string? version,
        List<TriggerConfig> triggers);

    Task<List<TriggerNeedConfig>> CreateTriggerNeedsAsync(
        string? version,
        List<TriggerNeedConfig> needs);

    Task<List<PostWorkflowPublishedEnrollmentConfig>> CreatePostWorkflowPublishedEnrollmentConfigsAsync(
        string? version,
        List<PostWorkflowPublishedEnrollmentConfig> configs);

    Task<List<PostWorkflowPublishedEnrollmentConfig>> GetPostWorkflowPublishedEnrollmentConfigsAsync(
        string? version,
        string triggerId);
}

public sealed class NeedConfigService : INeedConfigService, IScopedService
{
    private readonly ICacheService _cacheService;
    private readonly IBlobService _blobService;
    private readonly IEnumerable<IDynamicActionNeedsLoader> _dynamicActionNeedsLoaders;
    private readonly ILockService _lockService;

    private static readonly Regex ConditionRegex = new("^{{(.*)}}$", RegexOptions.Compiled);

    public NeedConfigService(
        ICacheService cacheService,
        IBlobService blobService,
        IEnumerable<IDynamicActionNeedsLoader> dynamicActionNeedsLoaders,
        ILockService lockService)
    {
        _cacheService = cacheService;
        _blobService = blobService;
        _dynamicActionNeedsLoaders = dynamicActionNeedsLoaders;
        _lockService = lockService;
    }

    public async Task<List<ActionConfig>> GetActionsAsync(
        string? version)
    {
        version ??= "v1";

        var cacheKey = $"NeedConfigs:SupportedActions:{version}";

        var supportedActions = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var needConfigs = await _blobService.DownloadJsonAsAsync<List<ActionConfig>>(
                    "need-configs",
                    $"supported-actions/{version}.json");

                return needConfigs;
            },
            TimeSpan.FromMinutes(5));

        return supportedActions ?? [];
    }

    public async Task<List<ActionNeedConfig>> GetActionNeedsAsync(
        string sleekflowCompanyId,
        string actionGroup,
        string actionSubgroup,
        string? version,
        Dictionary<string, object?>? parameters)
    {
        version ??= "v1";

        var cacheKey = $"NeedConfigs:Actions:{version}";

        var actionNeeds = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var needConfigs = await _blobService.DownloadJsonAsAsync<List<ActionNeedConfig>>(
                    "need-configs",
                    $"actions/{version}.json");

                return needConfigs;
            },
            TimeSpan.FromMinutes(5));

        if (actionNeeds is null)
        {
            return new List<ActionNeedConfig>();
        }

        var filteredNeeds = actionNeeds
            .Where(
                x =>
                    x.ActionGroup == actionGroup
                    && x.ActionSubgroup == actionSubgroup)
            .ToList();

        var scriptObject = new ScriptObject
        {
            {
                "json", new JsonFunctions()
            },
            {
                "array", new ExtendedArrayFunctions()
            },
            {
                "date", new ExtendedDateTimeFunctions()
            },
            {
                "sleekflow", new SleekflowFunctions()
            },
            {
                "template", new TemplateFunctions()
            },
            {
                "params", parameters ?? new Dictionary<string, object?>()
            }
        };

        var templateContext = new TemplateContext();
        templateContext.PushGlobal(scriptObject);

        List<ActionNeedConfig> results = new();

        foreach (var need in filteredNeeds)
        {
            if (await IsConditionsMatchedAsync(templateContext, need.ConditionsExpr))
            {
                results.Add(need);
            }
        }

        var dynamicNeedsLoader = _dynamicActionNeedsLoaders
            .FirstOrDefault(x =>
                x.ActionGroup == actionGroup
                && x.ActionSubgroup == actionSubgroup);

        if (dynamicNeedsLoader is not null)
        {
            var dynamicNeeds = await dynamicNeedsLoader.LoadAsync(
                sleekflowCompanyId,
                parameters);

            results.AddRange(dynamicNeeds);
        }

        return results;
    }

    public async Task<List<TriggerConfig>> GetTriggersAsync(string? version)
    {
        version ??= "v1";
        var cacheKey = $"NeedConfigs:Triggers:{version}";

        var supportedTriggers = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var needConfigs = await _blobService.DownloadJsonAsAsync<List<TriggerConfig>>(
                    "need-configs",
                    $"triggers/{version}.json");

                return needConfigs;
            },
            TimeSpan.FromMinutes(5));

        return supportedTriggers ?? [];
    }

    public async Task<List<TriggerNeedConfig>> GetTriggerNeedsAsync(
        string? version,
        string triggerId,
        Dictionary<string, object?>? parameters)
    {
        version ??= "v1";

        var cacheKey = $"NeedConfigs:TriggerNeeds:{version}";

        var triggerNeeds = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var needConfigs = await _blobService.DownloadJsonAsAsync<List<TriggerNeedConfig>>(
                    "need-configs",
                    $"trigger-needs/{version}.json");

                return needConfigs;
            },
            TimeSpan.FromMinutes(5));

        if (triggerNeeds is null)
        {
            return [];
        }

        var filteredNeeds = triggerNeeds
            .Where(
                x =>
                    x.TriggerId == triggerId)
            .ToList();

        var scriptObject = new ScriptObject
        {
            {
                "json", new JsonFunctions()
            },
            {
                "array", new ExtendedArrayFunctions()
            },
            {
                "date", new ExtendedDateTimeFunctions()
            },
            {
                "sleekflow", new SleekflowFunctions()
            },
            {
                "template", new TemplateFunctions()
            },
            {
                "params", parameters ?? new Dictionary<string, object?>()
            }
        };

        var templateContext = new TemplateContext();
        templateContext.PushGlobal(scriptObject);

        var results = new List<TriggerNeedConfig>();

        foreach (var need in filteredNeeds)
        {
            if (await IsConditionsMatchedAsync(templateContext, need.ConditionsExpr))
            {
                results.Add(need);
            }
        }

        return results;
    }

    public async Task<List<ActionConfigDto>> CreateActionsAsync(
        string? version,
        List<ActionConfigDto> actions)
    {
        version ??= "v1";

        return await CreateConfigsAsync(
            $"supported-actions/{version}.json",
            $"NeedConfigs:SupportedActions:{version}:Lock",
            actions,
            action => action);
    }

    public async Task<List<ActionNeedConfig>> CreateActionNeedsAsync(
        string? version,
        List<ActionNeedConfig> needs)
    {
        version ??= "v1";

        return await CreateConfigsAsync(
            $"actions/{version}.json",
            $"NeedConfigs:Actions:{version}:Lock",
            needs,
            need => need);
    }

    public async Task<List<TriggerConfig>> CreateTriggersAsync(
        string? version,
        List<TriggerConfig> triggers)
    {
        version ??= "v1";

        return await CreateConfigsAsync(
            $"triggers/{version}.json",
            $"NeedConfigs:Triggers:{version}:Lock",
            triggers,
            trigger => trigger);
    }

    public async Task<List<TriggerNeedConfig>> CreateTriggerNeedsAsync(
        string? version,
        List<TriggerNeedConfig> needs)
    {
        version ??= "v1";

        return await CreateConfigsAsync(
            $"trigger-needs/{version}.json",
            $"NeedConfigs:TriggerNeeds:{version}:Lock",
            needs,
            need => need);
    }

    public async Task<List<PostWorkflowPublishedEnrollmentConfig>> CreatePostWorkflowPublishedEnrollmentConfigsAsync(
        string? version,
        List<PostWorkflowPublishedEnrollmentConfig> configs)
    {
        version ??= "v1";

        return await CreateConfigsAsync(
            $"post-workflow-published-enrollment-configs/{version}.json",
            $"NeedConfigs:PostWorkflowPublishedEnrollmentConfigs:{version}:Lock",
            configs,
            config => config);
    }

    public async Task<List<PostWorkflowPublishedEnrollmentConfig>> GetPostWorkflowPublishedEnrollmentConfigsAsync(
        string? version,
        string triggerId)
    {
        version ??= "v1";

        var cacheKey = $"NeedConfigs:PostWorkflowPublishedEnrollmentConfigs:{version}";

        var postWorkflowPublishedEnrollmentConfigs = await _cacheService.CacheAsync(
            cacheKey,
            async () =>
            {
                var needConfigs = await _blobService.DownloadJsonAsAsync<List<PostWorkflowPublishedEnrollmentConfig>>(
                    "need-configs",
                    $"post-workflow-published-enrollment-configs/{version}.json");

                return needConfigs;
            },
            TimeSpan.FromMinutes(5));

        if (postWorkflowPublishedEnrollmentConfigs is null)
        {
            return new List<PostWorkflowPublishedEnrollmentConfig>();
        }

        var filteredConfigs = postWorkflowPublishedEnrollmentConfigs
            .Where(
                x =>
                    x.TriggerId == triggerId)
            .ToList();

        return filteredConfigs;
    }

    private static async Task<bool> IsConditionsMatchedAsync(
        TemplateContext templateContext,
        string? conditionExpression)
    {
        if (string.IsNullOrWhiteSpace(conditionExpression))
        {
            return true;
        }

        var match = ConditionRegex.Match(conditionExpression);

        // Simple String
        if (!match.Success)
        {
            return conditionExpression == "true";
        }

        var condition = match.Groups[1].Value;

        var evaluatedExpression = await Template.EvaluateAsync(
            condition.Trim(),
            templateContext);

        return evaluatedExpression is true;
    }

    private async Task<List<T>> CreateConfigsAsync<T>(
        string filePath,
        string lockKey,
        List<T> newConfigs,
        Func<T, T> mapConfig)
    {
        Lock? @lock = null;
        try
        {
            @lock = await _lockService.WaitUnitLockAsync(
                new[] { lockKey },
                minimumLockDuration: TimeSpan.FromSeconds(10),
                TimeSpan.FromSeconds(20));

            // Get current configs
            var currentConfigs = await _blobService.DownloadJsonAsAsync<List<T>>(
                "need-configs",
                filePath) ?? new List<T>();

            // Create snapshot of current state
            await _blobService.CreateSnapshotAsync(
                "need-configs",
                filePath);

            // Add new configs
            currentConfigs.AddRange(newConfigs.Select(mapConfig));

            // Upload updated file
            await _blobService.UploadAsJsonAsync(
                "need-configs",
                filePath,
                currentConfigs,
                new JsonSerializerSettings
                {
                    Formatting = Formatting.Indented
                });

            // Clear cache
            await _cacheService.RemoveCacheAsync(lockKey);

            return newConfigs;
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }
    }
}