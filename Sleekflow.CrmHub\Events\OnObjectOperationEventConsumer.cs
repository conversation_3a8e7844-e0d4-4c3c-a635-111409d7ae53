using MassTransit;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers.Resolvers;
using Sleekflow.CrmHub.Unifies;
using Sleekflow.CrmHub.Utils;
using Sleekflow.Locks;

namespace Sleekflow.CrmHub.Events;

public class OnObjectOperationEventConsumerDefinition : ConsumerDefinition<OnObjectOperationEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnObjectOperationEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(15);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnObjectOperationEventConsumer : IConsumer<OnObjectOperationEvent>
{
    private readonly IEntityContextService _entityContextService;
    private readonly IProviderObjectIdentityResolverService _providerObjectIdentityResolverService;
    private readonly IProviderObjectSnapshotTimeResolverService _providerObjectSnapshotTimeResolverService;
    private readonly IEntityService _entityService;
    private readonly ILogger<OnObjectOperationEventConsumer> _logger;
    private readonly IUnifyService _unifyService;
    private readonly IProviderConfigService _providerConfigService;
    private readonly ILockService _lockService;

    public OnObjectOperationEventConsumer(
        IEntityContextService entityContextService,
        IProviderObjectIdentityResolverService providerObjectIdentityResolverService,
        IProviderObjectSnapshotTimeResolverService providerObjectSnapshotTimeResolverService,
        IEntityService entityService,
        ILogger<OnObjectOperationEventConsumer> logger,
        IUnifyService unifyService,
        IProviderConfigService providerConfigService,
        ILockService lockService)
    {
        _entityContextService = entityContextService;
        _providerObjectIdentityResolverService = providerObjectIdentityResolverService;
        _providerObjectSnapshotTimeResolverService = providerObjectSnapshotTimeResolverService;
        _entityService = entityService;
        _logger = logger;
        _unifyService = unifyService;
        _providerConfigService = providerConfigService;
        _lockService = lockService;
    }

    public async Task Consume(ConsumeContext<OnObjectOperationEvent> context)
    {
        var @event = context.Message;
        var cancellationToken = context.CancellationToken;

        var sleekflowCompanyId = @event.SleekflowCompanyId;
        var providerName = @event.ProviderName;
        var providerObjectId = @event.ProviderObjectId;
        var entityTypeName = @event.EntityTypeName;
        var targetObjectId = @event.TargetObjectId;
        var operation = @event.Operation;

        var eventObj = @event.Object;
        var eventObjDict = eventObj.ToDictionary(
            c => providerName + ":" + c.Key,
            c => c.Value);

        var providerConfig =
            await _providerConfigService.GetProviderConfigOrDefaultAsync(sleekflowCompanyId, providerName);

        var resolvedObjectSnapshotTime =
            _providerObjectSnapshotTimeResolverService.Resolve(
                eventObjDict,
                providerName,
                defaultDateTimeOffset: context.SentTime == null
                    ? null
                    : DateTime.SpecifyKind(context.SentTime!.Value, DateTimeKind.Utc));
        var resolvedObjectIdentity =
            _providerObjectIdentityResolverService.Resolve(
                eventObjDict,
                entityTypeName,
                providerName,
                providerConfig);

        var @lock = resolvedObjectIdentity.PhoneNumber != null || resolvedObjectIdentity.Email != null
            ? await _lockService.LockAsync(
                new[]
                {
                    sleekflowCompanyId,
                    resolvedObjectIdentity.PhoneNumber ?? "EMPTY",
                    resolvedObjectIdentity.Email ?? "EMPTY"
                },
                TimeSpan.FromSeconds(
                    60 * (OnObjectOperationEventConsumerDefinition.LockDuration +
                          OnObjectOperationEventConsumerDefinition.MaxAutoRenewDuration)),
                cancellationToken)
            : await _lockService.LockAsync(
                new[]
                {
                    sleekflowCompanyId,
                    providerName,
                    providerObjectId
                },
                TimeSpan.FromSeconds(
                    60 * (OnObjectOperationEventConsumerDefinition.LockDuration +
                          OnObjectOperationEventConsumerDefinition.MaxAutoRenewDuration)),
                cancellationToken);
        if (@lock == null)
        {
            // If we can't acquire a lock for that identity, redeliver the message later
            await context.Redeliver(TimeSpan.FromSeconds(8));

            return;
        }

        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug(
                "Resolved CrmHubEntityContext for operation {Operation}, eventObjDict {EventObjDict}, resolvedObjectSnapshotTime {ResolvedObjectSnapshotTime}, resolvedObjectIdentity {ResolvedObjectIdentity}",
                operation,
                eventObjDict,
                resolvedObjectSnapshotTime,
                JsonConvert.SerializeObject(resolvedObjectIdentity));
        }

        try
        {
            await HandleAsync(
                operation,
                targetObjectId,
                sleekflowCompanyId,
                entityTypeName,
                providerName,
                providerObjectId,
                eventObjDict,
                resolvedObjectSnapshotTime,
                resolvedObjectIdentity,
                cancellationToken);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }

    private async Task HandleAsync(
        string operation,
        string? targetObjectId,
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName,
        string providerObjectId,
        Dictionary<string, object?> eventObjDict,
        IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime resolvedObjectSnapshotTime,
        IProviderObjectIdentityResolverService.ProviderObjectIdentity resolvedObjectIdentity,
        CancellationToken cancellationToken)
    {
        if (operation == OnObjectOperationEvent.OperationUnassociateObject)
        {
            var (prevEntityContext, newEntityContext) = await _entityContextService.GetAndUnassociateEntityContextAsync(
                targetObjectId!,
                sleekflowCompanyId,
                entityTypeName,
                providerName,
                providerObjectId);

            _logger.LogDebug(
                "Resolved CrmHubEntityContext for operation {Operation}, eventObjDict {EventObjDict}, prevEntityContext {PrevEntityContext}, newEntityContext {NewEntityContext}",
                operation,
                eventObjDict,
                newEntityContext,
                newEntityContext);

            var (prevUad, newUad) = await MoveProviderPropertiesBetweenEntityContextsAsync(
                prevEntityContext,
                newEntityContext,
                providerName,
                entityTypeName,
                await _unifyService.GetUnifyRulesAsync(sleekflowCompanyId, entityTypeName, cancellationToken),
                resolvedObjectSnapshotTime);

            await _entityService.UpsertAsync(
                entityTypeName,
                prevUad.Dict,
                prevUad.PropModDict,
                prevEntityContext.Id,
                providerName,
                sleekflowCompanyId);

            await _entityService.UpsertAsync(
                entityTypeName,
                newUad.Dict,
                newUad.PropModDict,
                newEntityContext.Id,
                providerName,
                sleekflowCompanyId);
        }
        else if (operation == OnObjectOperationEvent.OperationAssociateObject)
        {
            var (prevEntityContext, newEntityContext) = await _entityContextService.GetAndAssociateEntityContextAsync(
                targetObjectId!,
                sleekflowCompanyId,
                entityTypeName,
                providerName,
                providerObjectId);

            var hasPrevEntityContext = prevEntityContext != null;
            if (hasPrevEntityContext)
            {
                _logger.LogDebug(
                    "Resolved CrmHubEntityContext for operation {Operation}, eventObjDict {EventObjDict}, prevEntityContext {PrevEntityContext}, newEntityContext {NewEntityContext}",
                    operation,
                    eventObjDict,
                    newEntityContext,
                    newEntityContext);

                var (prevUad, newUad) = await MoveProviderPropertiesBetweenEntityContextsAsync(
                    prevEntityContext!,
                    newEntityContext,
                    providerName,
                    entityTypeName,
                    await _unifyService.GetUnifyRulesAsync(sleekflowCompanyId, entityTypeName, cancellationToken),
                    resolvedObjectSnapshotTime,
                    eventObjDict);

                await _entityService.UpsertAsync(
                    entityTypeName,
                    prevUad.Dict,
                    prevUad.PropModDict,
                    prevEntityContext!.Id,
                    providerName,
                    sleekflowCompanyId);

                await _entityService.UpsertAsync(
                    entityTypeName,
                    newUad.Dict,
                    newUad.PropModDict,
                    newEntityContext.Id,
                    providerName,
                    sleekflowCompanyId);

                await _entityContextService.CleanUpEntityContextAsync(
                    prevEntityContext.Id,
                    prevEntityContext.SysSleekflowCompanyId,
                    entityTypeName);
            }
            else
            {
                _logger.LogDebug(
                    "Resolved CrmHubEntityContext for operation {Operation}, eventObjDict {EventObjDict}, prevEntityContext {PrevEntityContext}, newEntityContext {NewEntityContext}",
                    operation,
                    eventObjDict,
                    newEntityContext,
                    newEntityContext);

                var entityId = newEntityContext.Id;

                var (dict, propModDict) = await UnifyAndDiff(
                    entityId,
                    sleekflowCompanyId,
                    entityTypeName,
                    eventObjDict,
                    resolvedObjectSnapshotTime,
                    resolvedObjectIdentity,
                    cancellationToken);

                await _entityService.UpsertAsync(
                    entityTypeName,
                    dict,
                    propModDict,
                    entityId,
                    providerName,
                    sleekflowCompanyId);
            }
        }
        else if (operation == OnObjectOperationEvent.OperationDeleteObject)
        {
            var entityContext = await _entityContextService.DeleteExternalIdFromEntityContextAsync(
                sleekflowCompanyId,
                entityTypeName,
                providerName,
                providerObjectId);
            if (entityContext != null)
            {
                _logger.LogDebug(
                    "Resolved CrmHubEntityContext for operation {Operation}, eventObjDict {EventObjDict}, entityContext {CrmHubEntityContext}",
                    operation,
                    eventObjDict,
                    entityContext);

                // There are other objects in the CrmHubEntityContext
                var uad = await RemoveProviderPropertiesFromEntityContextAsync(
                    entityContext,
                    providerName,
                    entityTypeName,
                    await _unifyService.GetUnifyRulesAsync(sleekflowCompanyId, entityTypeName, cancellationToken),
                    resolvedObjectSnapshotTime);

                await _entityService.UpsertAsync(
                    entityTypeName,
                    uad.Dict,
                    uad.PropModDict,
                    entityContext.Id,
                    providerName,
                    sleekflowCompanyId);
            }
        }
        else
        {
            var entityContext = await _entityContextService.GetOrInitEntityContextAsync(
                sleekflowCompanyId,
                entityTypeName,
                resolvedObjectIdentity.PhoneNumber,
                resolvedObjectIdentity.Email,
                providerName,
                providerObjectId);
            if (entityContext == null)
            {
                throw new Exception(
                    $"Unable to resolve CrmHubEntityContext for eventObjDict {eventObjDict}, entityContext {entityContext}");
            }

            _logger.LogDebug(
                "Resolved CrmHubEntityContext for eventObjDict operation {Operation}, {EventObjDict}, entityContext {CrmHubEntityContext}",
                operation,
                eventObjDict,
                entityContext);

            var entityId = entityContext.Id;

            var uad = await UnifyAndDiff(
                entityId,
                sleekflowCompanyId,
                entityTypeName,
                eventObjDict,
                resolvedObjectSnapshotTime,
                resolvedObjectIdentity,
                cancellationToken);

            await _entityService.UpsertAsync(
                entityTypeName,
                uad.Dict,
                uad.PropModDict,
                entityId,
                providerName,
                sleekflowCompanyId);
        }
    }

    private async Task<UnifyAndDiffOutput> RemoveProviderPropertiesFromEntityContextAsync(
        CrmHubEntityContext entityContext,
        string providerName,
        string entityTypeName,
        List<UnifyRule> unifyRules,
        IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime resolvedObjectSnapshotTime)
    {
        var dict =
            await _entityService.GetOrDefaultAsync(
                entityContext.Id,
                entityContext.SysSleekflowCompanyId,
                entityTypeName)
            ?? new Dictionary<string, object?>();
        var propModDict =
            new Dictionary<string, (object? FromValue, object? ToValue)>();
        var snapshottedValueDict = dict
            .Where(p => SnapshottedValue.IsSnapshottedValue(p.Value))
            .ToDictionary(p => p.Key, p => SnapshottedValue.FromObject(p.Value));

        foreach (var key in dict.Keys.Where(key => key.StartsWith(providerName + ":")).ToList())
        {
            propModDict[key] = (snapshottedValueDict[key]?.Value, null);
            dict.Remove(key);
            snapshottedValueDict.Remove(key);
        }

        UnifyUtils.UnifyAndTrack(
            snapshottedValueDict,
            propModDict,
            resolvedObjectSnapshotTime,
            unifyRules);

        foreach (var pair in snapshottedValueDict)
        {
            dict[pair.Key] = pair.Value;
        }

        dict[CrmHubEntity.PropertyNameSysResolvedPhoneNumber] = null;
        dict[CrmHubEntity.PropertyNameSysResolvedEmail] = null;
        dict[CrmHubEntity.PropertyNameSysTags] = new List<string>();
        dict[CrmHubEntity.PropertyNameSysVersion] =
            (long) dict.GetValueOrDefault(CrmHubEntity.PropertyNameSysVersion, 0L)! + 1;

        return new UnifyAndDiffOutput(dict, propModDict);
    }

    private async Task<(UnifyAndDiffOutput Prev, UnifyAndDiffOutput New)>
        MoveProviderPropertiesBetweenEntityContextsAsync(
            CrmHubEntityContext prevEntityContext,
            CrmHubEntityContext newEntityContext,
            string providerName,
            string entityTypeName,
            List<UnifyRule> unifyRules,
            IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime resolvedObjectSnapshotTime,
            Dictionary<string, object?>? eventObjDict = null)
    {
        var prevDict =
            await _entityService.GetOrDefaultAsync(
                prevEntityContext.Id,
                prevEntityContext.SysSleekflowCompanyId,
                entityTypeName)
            ?? new Dictionary<string, object?>();
        var prevPropModDict =
            new Dictionary<string, (object? FromValue, object? ToValue)>();
        var prevSnapshottedValueDict = prevDict
            .Where(p => SnapshottedValue.IsSnapshottedValue(p.Value))
            .ToDictionary(p => p.Key, p => SnapshottedValue.FromObject(p.Value));

        var newDict =
            await _entityService.GetOrDefaultAsync(
                newEntityContext.Id,
                newEntityContext.SysSleekflowCompanyId,
                entityTypeName)
            ?? new Dictionary<string, object?>();
        var newPropModDict =
            new Dictionary<string, (object? FromValue, object? ToValue)>();
        var newSnapshottedValueDict = newDict
            .Where(p => SnapshottedValue.IsSnapshottedValue(p.Value))
            .ToDictionary(p => p.Key, p => SnapshottedValue.FromObject(p.Value));

        foreach (var key in prevDict.Keys.Where(key => key.StartsWith(providerName + ":")).ToList())
        {
            newDict[key] = prevSnapshottedValueDict[key];
            newPropModDict[key] = (null, prevSnapshottedValueDict[key]?.Value);
            newSnapshottedValueDict[key] = prevSnapshottedValueDict[key];

            prevPropModDict[key] = (prevSnapshottedValueDict[key]?.Value, null);
            prevDict.Remove(key);
            prevSnapshottedValueDict.Remove(key);
        }

        if (eventObjDict != null)
        {
            MergeAndTrackEventObjDict(
                eventObjDict,
                newSnapshottedValueDict,
                newPropModDict,
                resolvedObjectSnapshotTime);
        }

        UnifyUtils.UnifyAndTrack(
            prevSnapshottedValueDict,
            prevPropModDict,
            resolvedObjectSnapshotTime,
            unifyRules);

        UnifyUtils.UnifyAndTrack(
            newSnapshottedValueDict,
            newPropModDict,
            resolvedObjectSnapshotTime,
            unifyRules);

        foreach (var pair in prevSnapshottedValueDict)
        {
            prevDict[pair.Key] = pair.Value;
        }

        foreach (var pair in newSnapshottedValueDict)
        {
            newDict[pair.Key] = pair.Value;
        }

        prevDict[CrmHubEntity.PropertyNameSysResolvedPhoneNumber] = null;
        prevDict[CrmHubEntity.PropertyNameSysResolvedEmail] = null;
        prevDict[CrmHubEntity.PropertyNameSysTags] = new List<string>();
        prevDict[CrmHubEntity.PropertyNameSysVersion] =
            (long) prevDict.GetValueOrDefault(CrmHubEntity.PropertyNameSysVersion, 0L)! + 1;

        newDict[CrmHubEntity.PropertyNameSysResolvedPhoneNumber] = null;
        newDict[CrmHubEntity.PropertyNameSysResolvedEmail] = null;
        newDict[CrmHubEntity.PropertyNameSysTags] = new List<string>();
        newDict[CrmHubEntity.PropertyNameSysVersion] =
            (long) newDict.GetValueOrDefault(CrmHubEntity.PropertyNameSysVersion, 0L)! + 1;

        return (
            new UnifyAndDiffOutput(prevDict, prevPropModDict),
            new UnifyAndDiffOutput(newDict, newPropModDict)
        );
    }

    private readonly record struct UnifyAndDiffOutput(
        Dictionary<string, object?> Dict,
        Dictionary<string, (object? FromValue, object? ToValue)> PropModDict);

    private async Task<UnifyAndDiffOutput> UnifyAndDiff(
        string entityId,
        string sleekflowCompanyId,
        string entityTypeName,
        Dictionary<string, object?> eventObjDict,
        IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime resolvedObjectSnapshotTime,
        IProviderObjectIdentityResolverService.ProviderObjectIdentity resolvedObjectIdentity,
        CancellationToken cancellationToken)
    {
        var dict =
            await _entityService.GetOrDefaultAsync(
                entityId,
                sleekflowCompanyId,
                entityTypeName)
            ?? new Dictionary<string, object?>();
        var propModDict =
            new Dictionary<string, (object? FromValue, object? ToValue)>();

        var snapshottedValueDict = dict
            .Where(p => SnapshottedValue.IsSnapshottedValue(p.Value))
            .ToDictionary(p => p.Key, p => SnapshottedValue.FromObject(p.Value));
        var unifyRules = await _unifyService.GetUnifyRulesAsync(sleekflowCompanyId, entityTypeName, cancellationToken);

        MergeAndTrackEventObjDict(
            eventObjDict,
            snapshottedValueDict,
            propModDict,
            resolvedObjectSnapshotTime);
        UnifyUtils.UnifyAndTrack(
            snapshottedValueDict,
            propModDict,
            resolvedObjectSnapshotTime,
            unifyRules);

        // Replace the dict with the entries in the snapshottedValueDict
        foreach (var snapshottedValuePair in snapshottedValueDict)
        {
            dict[snapshottedValuePair.Key] = snapshottedValuePair.Value;
        }

        // Clear the dict unified entries that doesn't have the corresponding UnifyRule
        var unifiedFieldNames = unifyRules
            .Select(ur => "unified:" + ur.FieldName)
            .ToHashSet();
        foreach (var key in snapshottedValueDict.Keys
                     .Where(
                         key =>
                             key.StartsWith("unified:")
                             && !unifiedFieldNames.Contains(key))
                     .ToList())
        {
            propModDict[key] = (snapshottedValueDict[key]!.Value, null);

            dict.Remove(key);
        }

        dict[CrmHubEntity.PropertyNameSysResolvedPhoneNumber] = resolvedObjectIdentity.PhoneNumber;
        dict[CrmHubEntity.PropertyNameSysResolvedEmail] = resolvedObjectIdentity.Email;
        dict[CrmHubEntity.PropertyNameSysTags] = new List<string>();
        dict[CrmHubEntity.PropertyNameSysVersion] =
            (long) dict.GetValueOrDefault(CrmHubEntity.PropertyNameSysVersion, 0L)! + 1;

        return new UnifyAndDiffOutput(dict, propModDict);
    }

    private static void MergeAndTrackEventObjDict(
        Dictionary<string, object?> eventObjDict,
        IDictionary<string, SnapshottedValue?> dict,
        IDictionary<string, (object? FromValue, object? ToValue)> propModDict,
        IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime resolvedObjectSnapshotTime)
    {
        // Merge the latest values from the provider into the dictionary
        foreach (var (key, eventValue) in eventObjDict)
        {
            // New Field
            var isMissingValueInDict = !dict.ContainsKey(key) || dict[key] == null;
            if (isMissingValueInDict)
            {
                propModDict[key] = (null, eventValue);
                dict[key] = new SnapshottedValue(resolvedObjectSnapshotTime.SnapshotTime, eventValue);

                continue;
            }

            var snapshottedValue = dict[key]!;

            // Existing Field
            var eventValueJToken = JTokenUtils.ToJToken(eventValue) ?? JValue.CreateNull();
            var snapshottedValueJToken = JTokenUtils.ToJToken(snapshottedValue.Value) ?? JValue.CreateNull();

            // Normalize numeric values
            if (eventValueJToken.Type == JTokenType.Integer && snapshottedValueJToken.Type == JTokenType.Float)
            {
                eventValueJToken = new JValue(Convert.ToDouble(eventValueJToken.Value<long>()));
            }
            else if (eventValueJToken.Type == JTokenType.Float && snapshottedValueJToken.Type == JTokenType.Integer)
            {
                snapshottedValueJToken = new JValue(Convert.ToDouble(snapshottedValueJToken.Value<long>()));
            }

            var isValueChanged = !JTokenUtils.DeepEquals(
                eventValueJToken as JObject ?? eventValueJToken,
                snapshottedValueJToken as JObject ?? snapshottedValueJToken);

            if (isValueChanged)
            {
                propModDict[key] = (snapshottedValue.Value, eventValue);
                dict[key] = new SnapshottedValue(
                    resolvedObjectSnapshotTime.SnapshotTime,
                    JTokenUtils.ToJToken(eventValue));
            }
        }
    }
}