﻿using MassTransit;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.CommerceHub.Events;

public class OnVtexOrderHookReceivedEventConsumerDefinition : ConsumerDefinition<OnVtexOrderHookReceivedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnVtexOrderHookReceivedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnVtexOrderHookReceivedEventConsumer : IConsumer<OnVtexOrderHookReceivedEvent>
{
    private readonly IVtexOrderCommander _vtexOrderCommander;
    private readonly IBus _bus;
    private readonly ILogger<OnVtexOrderHookReceivedEventConsumer> _logger;
    private readonly IVtexAuthenticationService _vtexAuthenticationService;

    public OnVtexOrderHookReceivedEventConsumer(
        IVtexOrderCommander vtexOrderCommander,
        IBus bus,
        ILogger<OnVtexOrderHookReceivedEventConsumer> logger,
        IVtexAuthenticationService vtexAuthenticationService)
    {
        _vtexOrderCommander = vtexOrderCommander;
        _bus = bus;
        _logger = logger;
        _vtexAuthenticationService = vtexAuthenticationService;
    }

    public async Task Consume(ConsumeContext<OnVtexOrderHookReceivedEvent> context)
    {
        var @event = context.Message;

        var sleekflowCompanyId = @event.SleekflowCompanyId;
        var vtexAuthenticationId = @event.VtexAuthenticationId;
        var orderId = @event.VtexOrderHook.OrderId;
        var orderStatus = @event.VtexOrderHook.State;

        // authentication
        var vtexAuthentication = await _vtexAuthenticationService.GetAsync(
            vtexAuthenticationId,
            sleekflowCompanyId,
            context.CancellationToken);

        if (vtexAuthentication == null)
        {
            _logger.LogWarning(
                "OnVtexOrderHookReceivedEventConsumer: vtex authentication not found. {VtexAuthenticationId} {CompanyId}",
                vtexAuthenticationId,
                sleekflowCompanyId);

            return;
        }

        // get order detail
        var vtexOrder = await _vtexOrderCommander.GetOrderAsync(
            vtexAuthentication.Credential,
            orderId,
            context.CancellationToken);

        // manually assign order status to align with the hook one
        vtexOrder.Status = orderStatus;

        var vtexOrderCreatedEventRequest = new VtexOrderHookReceivedEventRequest(
            sleekflowCompanyId,
            vtexAuthenticationId,
            orderStatus,
            vtexOrder.OrderId,
            vtexOrder.ToVtexOrderOverview());

        await _bus.Publish(vtexOrderCreatedEventRequest, context.CancellationToken);
    }
}