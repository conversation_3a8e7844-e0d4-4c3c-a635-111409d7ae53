using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Microsoft.SemanticKernel.Agents.Orchestration.GroupChat;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions;

public interface IAgentCollaborationDefinition
{
    Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig);

    GroupChatManager? GetGroupChatManager();

    SelectionStrategy? CreateSelectionStrategy(Kernel kernel);

    RegexTerminationStrategy? CreateTerminationStrategy(List<Agent> agents);

    Task<(string ChatHistoryStr, string Context)> InitializeChatHistoryAsync(
        AgentGroupChat? agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyAgentConfig? agentConfig);

    Task<bool> InterceptAgentReplyAsync(ChatMessageContent response, string groupChatIdStr);

    string GetFinalReplyTag();

    string GetSourceTag();

    Task<string> GetFinalReplyAsync(ChatHistory chatHistory);

    Task<string> GetSourceAsync(ChatHistory chatHistory, string groupChatIdStr);
}