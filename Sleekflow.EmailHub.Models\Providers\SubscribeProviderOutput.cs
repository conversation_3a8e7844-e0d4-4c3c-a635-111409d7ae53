using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Providers;

public class SubscribeProviderOutput
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("email_address")]
    [Required]
    public string EmailAddress { get; set; }

    [JsonProperty("provider_name")]
    [Required]
    public string ProviderName { get; set; }

    [JsonConstructor]
    public SubscribeProviderOutput(
        string sleekflowCompanyId,
        string providerName,
        string emailAddress)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EmailAddress = emailAddress;
        ProviderName = providerName;
    }
}