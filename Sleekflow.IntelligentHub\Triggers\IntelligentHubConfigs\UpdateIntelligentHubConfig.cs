﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class UpdateIntelligentHubConfig
    : ITrigger<UpdateIntelligentHubConfig.UpdateIntelligentHubConfigInput,
        UpdateIntelligentHubConfig.UpdateIntelligentHubConfigOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public UpdateIntelligentHubConfig(IIntelligentHubConfigService intelligentHubConfigService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public class UpdateIntelligentHubConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IntelligentHubConfig.PropertyNameUsageLimits)]
        [Validations.ValidateObject]
        public Dictionary<string, int> UsageLimits { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateIntelligentHubConfigInput(
            string sleekflowCompanyId,
            Dictionary<string, int> usageLimits,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UsageLimits = usageLimits;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateIntelligentHubConfigOutput
    {
        [JsonProperty("intelligent_hub_config")]
        public IntelligentHubConfig IntelligentHubConfig { get; set; }

        [JsonConstructor]
        public UpdateIntelligentHubConfigOutput(IntelligentHubConfig intelligentHubConfig)
        {
            IntelligentHubConfig = intelligentHubConfig;
        }
    }

    public async Task<UpdateIntelligentHubConfigOutput> F(
        UpdateIntelligentHubConfigInput updateIntelligentHubConfigInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(
                updateIntelligentHubConfigInput.SleekflowCompanyId);

        if (intelligentHubConfig is null)
        {
            throw new SfUserFriendlyException("IntelligentHub not enrolled yet.");
        }

        var updatedBy = string.IsNullOrWhiteSpace(updateIntelligentHubConfigInput.SleekflowStaffId)
            ? null
            : new AuditEntity.SleekflowStaff(
                updateIntelligentHubConfigInput.SleekflowStaffId,
                updateIntelligentHubConfigInput.SleekflowStaffTeamIds);

        intelligentHubConfig = await _intelligentHubConfigService.UpdateIntelligentHubConfigAsync(
            intelligentHubConfig.Id,
            intelligentHubConfig.SleekflowCompanyId,
            updateIntelligentHubConfigInput.UsageLimits,
            updatedBy);

        return new UpdateIntelligentHubConfigOutput(intelligentHubConfig);
    }
}