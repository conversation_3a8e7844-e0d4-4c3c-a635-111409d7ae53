﻿using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.Entities;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("entity")]
public class CrmHubEntity : Dictionary<string, object?>
{
    public const string PropertyNameSysResolvedPhoneNumber = "sys_resolved_phone_number";
    public const string PropertyNameSysResolvedEmail = "sys_resolved_email";
    public const string PropertyNameSysTags = "sys_tags";
    public const string PropertyNameSysVersion = "sys_version";
    public const string PropertyNameId = "id";
}