﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.WorkflowSteps;

public class UpdateSalesforceObjectRequest
{
    [JsonProperty("aggregate_step_id")]
    [Required]
    public string AggregateStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    [Required]
    public string ProxyStateId { get; set; }

    [JsonProperty("stack_entries")]
    [Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("connection_id")]
    [Required]
    public string ConnectionId { get; set; }

    [JsonProperty("entity_type_name")]
    [Required]
    public string EntityTypeName { get; set; }

    [JsonProperty("is_custom_object")]
    [Required]
    public bool IsCustomObject { get; set; }

    [JsonProperty("object_id")]
    [Required]
    public string ObjectId { get; set; }

    [JsonProperty("object_properties")]
    [Required]
    public Dictionary<string, object?> ObjectProperties { get; set; }

    [JsonConstructor]
    public UpdateSalesforceObjectRequest(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName,
        bool isCustomObject,
        string objectId,
        Dictionary<string, object?> objectProperties)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        IsCustomObject = isCustomObject;
        ObjectId = objectId;
        ObjectProperties = objectProperties;
    }
}