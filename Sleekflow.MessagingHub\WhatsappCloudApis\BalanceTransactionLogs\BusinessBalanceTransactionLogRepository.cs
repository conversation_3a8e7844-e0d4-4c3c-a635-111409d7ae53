using System.Linq.Expressions;
using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Expressions;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Filters;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;

public interface IBusinessBalanceTransactionLogRepository : IRepository<BusinessBalanceTransactionLog>
{
    Task<bool> IsDuplicateUniqueId(string uniqueId, string facebookBusinessId);

    Task<(List<BusinessBalanceTransactionLog> BusinessBalanceTransactionLogs, string? NextContinuationToken)>
        FilterConversationUsageTransactionLogs(
            BusinessBalanceTransactionLogFilter filter,
            string? continuationToken,
            int limit = 10000);

    public Task<
            (List<BusinessBalanceTransactionLog> BusinessBalanceTransactionLogs,
            string? NextContinuationToken)>
        FindBusinessBalanceTransactionLogsContainsInvoicePdfByFacebookBusinessId(
            List<string> facebookBusinessIds,
            DateTimeOffset? start,
            DateTimeOffset? end,
            string? continuationToken,
            int limit);

    Task<(List<BusinessBalanceTransactionLog> BusinessBalanceTransactionLogs, string? NextContinuationToken)>
        FilterCreditTransferTransactionLogs(
            BusinessBalanceTransactionLogFilter filter,
            string? continuationToken,
            int limit = 10000);

    Task<List<BusinessBalanceTransactionLog>> GetBusinessBalanceTransactionLogsByIdList(
        List<string> idList);
}

public class BusinessBalanceTransactionLogRepository
    : BaseRepository<BusinessBalanceTransactionLog>,
        IBusinessBalanceTransactionLogRepository,
        ISingletonService
{
    public BusinessBalanceTransactionLogRepository(
        ILogger<BusinessBalanceTransactionLogRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<bool> IsDuplicateUniqueId(string uniqueId, string facebookBusinessId)
    {
        var businessBalanceTransactionLog = await GetObjectsAsync(
            s =>
                s.UniqueId == uniqueId &&
                s.FacebookBusinessId == facebookBusinessId);

        return businessBalanceTransactionLog.Count > 0;
    }

    public async
        Task<(List<BusinessBalanceTransactionLog> BusinessBalanceTransactionLogs, string? NextContinuationToken)>
        FilterConversationUsageTransactionLogs(
            BusinessBalanceTransactionLogFilter filter,
            string? continuationToken,
            int limit = 10000)
    {
        Expression<Func<BusinessBalanceTransactionLog, bool>> predicate = log =>
            log.TransactionType == filter.TransactionType;

        if (!string.IsNullOrEmpty(filter.FacebookBusinessId))
        {
            predicate = predicate.AndAlso(log => log.FacebookBusinessId == filter.FacebookBusinessId);
        }

        if (!string.IsNullOrEmpty(filter.FacebookWabaId))
        {
            predicate = predicate.AndAlso(log => log.FacebookWabaId == filter.FacebookWabaId);
        }

        if (filter.HaveCreditAmount.HasValue)
        {
            predicate = filter.HaveCreditAmount switch
            {
                true => predicate.AndAlso(log => log.Credit.Amount != 0),
                false => predicate.AndAlso(log => log.Credit.Amount == 0),
                _ => predicate
            };
        }

        if (filter.HaveUsedAmount.HasValue)
        {
            predicate = filter.HaveUsedAmount switch
            {
                true => predicate.AndAlso(log => log.Used.Amount != 0),
                false => predicate.AndAlso(log => log.Used.Amount == 0),
                _ => predicate
            };
        }

        if (filter.HaveMarkupAmount.HasValue)
        {
            if (filter.HaveMarkupAmount == true)
            {
                predicate = predicate.AndAlso(log => log.Markup.Amount != 0);
            }

            if (filter.HaveMarkupAmount == false)
            {
                predicate = predicate.AndAlso(log => log.Markup.Amount == 0);
            }
        }

        if (filter.IsCalculated is not null)
        {
            predicate = predicate.AndAlso(log => log.IsCalculated == filter.IsCalculated);
        }

        if (filter.TopUpPaymentMethod is not null)
        {
            predicate = predicate.AndAlso(log => log.WabaTopUp!.PaymentMethod == filter.TopUpPaymentMethod);
        }

        if (filter.ConversationUsageTimeRange is not null)
        {
            predicate = predicate.AndAlso(
                log => log.WabaConversationUsage!.StartTimestamp >= filter.ConversationUsageTimeRange.Start &&
                       log.WabaConversationUsage!.EndTimestamp <= filter.ConversationUsageTimeRange.End);
        }

        if (filter.CreatedAtRange is not null)
        {
            predicate = predicate.AndAlso(
                log => log.UpdatedAt >= filter.CreatedAtRange!.Start && log.UpdatedAt <= filter.CreatedAtRange!.End);
        }

        if (filter.UpdatedAtRange is not null)
        {
            predicate = predicate.AndAlso(
                log => log.UpdatedAt >= filter.UpdatedAtRange!.Start && log.UpdatedAt <= filter.UpdatedAtRange!.End);
        }

        (List<BusinessBalanceTransactionLog> Objs, string? NextContinuationToken) businessBalanceTransactionLog;
        if (filter.OrderBy is not null)
        {
            Expression<Func<BusinessBalanceTransactionLog, DateTimeOffset>> orderBy;
            if (filter.OrderBy == IHasUpdatedAt.PropertyNameUpdatedAt)
            {
                orderBy = x => x.UpdatedAt;
            }
            else
            {
                orderBy = x => x.CreatedAt;
            }

            businessBalanceTransactionLog = await GetContinuationTokenizedObjectsAsync(
                predicate,
                orderBy,
                filter.Order is "ACS",
                continuationToken,
                limit);
        }
        else
        {
            businessBalanceTransactionLog =
                await GetContinuationTokenizedObjectsAsync(predicate, continuationToken, limit);
        }

        return businessBalanceTransactionLog;
    }

    public async Task<
            (List<BusinessBalanceTransactionLog> BusinessBalanceTransactionLogs,
            string? NextContinuationToken)>
        FindBusinessBalanceTransactionLogsContainsInvoicePdfByFacebookBusinessId(
            List<string> facebookBusinessIds,
            DateTimeOffset? start,
            DateTimeOffset? end,
            string? continuationToken,
            int limit)
    {
        var query = "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.transaction_type = @transactionType " +
                    "AND ARRAY_CONTAINS(@facebookBusinessIds, c.facebook_business_id ) " +
                    "AND c.waba_top_up.stripe_top_up_credit_detail.snapshot[\"invoice.paid\"].invoice_pdf != null ";

        if (start is not null && end is not null)
        {
            query +=
                $"AND c.waba_top_up.stripe_top_up_credit_detail.snapshot[\"invoice.paid\"].created >= {((DateTimeOffset) start).ToUniversalTime().ToUnixTimeSeconds()} ";

            query +=
                $"AND c.waba_top_up.stripe_top_up_credit_detail.snapshot[\"invoice.paid\"].created <= {((DateTimeOffset) end).ToUniversalTime().ToUnixTimeSeconds()} ";
        }

        query +=
            "ORDER BY c._ts DESC ";

        var queryDefinition = new QueryDefinition(query)
            .WithParameter("@transactionType", "TOP_UP")
            .WithParameter("@facebookBusinessIds", facebookBusinessIds);

        return await GetContinuationTokenizedObjectsAsync(queryDefinition, continuationToken, limit);
    }

    public async Task<(List<BusinessBalanceTransactionLog> BusinessBalanceTransactionLogs, string? NextContinuationToken
            )>
        FilterCreditTransferTransactionLogs(
            BusinessBalanceTransactionLogFilter filter,
            string? continuationToken,
            int limit = 10000)
    {
        Expression<Func<BusinessBalanceTransactionLog, bool>> predicate = log =>
            log.TransactionType == TransactionTypes.CreditTransfer;

        if (!string.IsNullOrEmpty(filter.FacebookBusinessId))
        {
            predicate = predicate.AndAlso(log => log.FacebookBusinessId == filter.FacebookBusinessId);
        }

        if (!string.IsNullOrEmpty(filter.FacebookWabaId))
        {
            predicate = predicate.AndAlso(log => log.FacebookWabaId == filter.FacebookWabaId);
        }

        if (filter.CreatedAtRange is not null)
        {
            predicate = predicate.AndAlso(
                log => log.CreatedAt >= filter.CreatedAtRange!.Start && log.CreatedAt <= filter.CreatedAtRange!.End);
        }

        if (filter.UpdatedAtRange is not null)
        {
            predicate = predicate.AndAlso(
                log => log.UpdatedAt >= filter.UpdatedAtRange!.Start && log.UpdatedAt <= filter.UpdatedAtRange!.End);
        }

        (List<BusinessBalanceTransactionLog> Objs, string? NextContinuationToken) businessBalanceTransactionLog;
        if (filter.OrderBy is not null)
        {
            if (filter.OrderBy is IHasUpdatedAt.PropertyNameUpdatedAt or IHasCreatedAt.PropertyNameCreatedAt)
            {
                Expression<Func<BusinessBalanceTransactionLog, DateTimeOffset>> orderBy;
                if (filter.OrderBy == IHasUpdatedAt.PropertyNameUpdatedAt)
                {
                    orderBy = x => x.UpdatedAt;
                }
                else
                {
                    orderBy = x => x.CreatedAt;
                }

                businessBalanceTransactionLog = await GetContinuationTokenizedObjectsAsync(
                    predicate,
                    orderBy,
                    filter.Order is "ACS",
                    continuationToken,
                    limit);
            }
            else
            {
                Expression<Func<BusinessBalanceTransactionLog, decimal>> orderBy = x => x.CreditTransferFromTo!.CreditTransferAmount.Amount;

                businessBalanceTransactionLog = await GetContinuationTokenizedObjectsAsync(
                    predicate,
                    orderBy,
                    filter.Order is "ACS",
                    continuationToken,
                    limit);
            }
        }
        else
        {
            businessBalanceTransactionLog =
                await GetContinuationTokenizedObjectsAsync(predicate, continuationToken, limit);
        }

        return businessBalanceTransactionLog;
    }

    public async Task<List<BusinessBalanceTransactionLog>> GetBusinessBalanceTransactionLogsByIdList(
        List<string> idList)
    {
        return await GetObjectsAsync(x => idList.Contains(x.Id));
    }
}