using MassTransit;
using Sleekflow.EmailHub.Models.Events;
using Sleekflow.EmailHub.Providers;
using Sleekflow.Locks;

namespace Sleekflow.EmailHub.Consumers;

public class OnEmailSubscriptionRenewTriggeredConsumerDefinition : ConsumerDefinition<OnEmailSubscriptionRenewTriggeredConsumer>
{
    public const int LockDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnEmailSubscriptionRenewTriggeredConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 40;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 40;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(LockDuration);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnEmailSubscriptionRenewTriggeredConsumer : IConsumer<OnEmailSubscriptionRenewTriggeredEvent>
{
    private readonly IEmailProviderSelector _emailProviderSelector;
    private readonly ILockService _lockService;
    private readonly ILogger<OnEmailSubscriptionRenewTriggeredConsumer> _logger;

    public OnEmailSubscriptionRenewTriggeredConsumer(
        ILockService lockService,
        ILogger<OnEmailSubscriptionRenewTriggeredConsumer> logger,
        IEmailProviderSelector emailProviderSelector)
    {
        _lockService = lockService;
        _logger = logger;
        _emailProviderSelector = emailProviderSelector;
    }

    public async Task Consume(ConsumeContext<OnEmailSubscriptionRenewTriggeredEvent> context)
    {
        var renewEmailSubscriptionEvent = context.Message;
        var cancellationToken = context.CancellationToken;
        var @lock = await _lockService.LockAsync(
            new[]
            {
                renewEmailSubscriptionEvent.ProviderName,
                "RenewEmailSubscription"
            },
            TimeSpan.FromSeconds(
                60 * OnEmailSubscriptionRenewTriggeredConsumerDefinition.LockDuration),
            cancellationToken);
        if (@lock is null)
        {
            return;
        }

        try
        {
            var providerService = _emailProviderSelector.GetEmailProvider(renewEmailSubscriptionEvent.ProviderName);
            await providerService.RenewEmailSubscriptionAsync(cancellationToken);
        }
        catch (Exception)
        {
            _logger.LogError(
                "OnEmailSubscriptionRenewTriggeredConsumer: subscription renewal for {providerName} fails" +
                             "at {time}",
                renewEmailSubscriptionEvent.ProviderName,
                DateTime.UtcNow);
            throw;
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}