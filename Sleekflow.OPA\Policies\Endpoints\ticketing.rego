package endpoints.ticketing

ticketing_mapping:={
  "access": access_ticketing,
  "manage": manage_ticketing
}

ticketing_tickets_mapping:={
   "view": access_ticketing,
   "edit": access_ticketing,
   "create": access_ticketing,
   "delete": access_ticketing
}

ticketing_tickets_teams_mapping:={
   "view": access_ticketing,
   "edit": access_ticketing,
   "create": access_ticketing,
   "delete": access_ticketing
}

ticketing_tickets_all_mapping:={
   "view": access_ticketing,
   "edit": access_ticketing,
   "create": access_ticketing,
   "delete": access_ticketing
}


ticketing_settings_mapping:={
   "view": manage_ticketing,
   "edit": manage_ticketing,
}

access_ticketing:=[
  {
    "path": "/TicketingHub/TicketCompanyConfigs/GetTicketCompanyConfig",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Tickets/GetTickets",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Tickets/GetTicket",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Tickets/GetTicketCount",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketStatues/GetTicketStatues",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketTypes/GetTicketTypes",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketPriorities/GetTicketPriorities",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Tickets/UpdateTicket",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Blobs/CreateBlobDownloadSasUrls",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Blobs/CreateBlobUploadSasUrls",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Blobs/DeleteBlobs",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Tickets/DeleteTicket",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/Tickets/CreateTicket",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketActivities/GetTicketActivities",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketComments/GetTicketComments",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketComments/CreateTicketComments",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketCompanyConfigs/GetTicketCompanyConfig",
    "method": "POST"
  }
  ]

manage_ticketing:=[
  {
    "path": "/TicketingHub/TicketCompanyConfigs/UpdateTicketCompanyConfig",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketTypes/CreateTicketType",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketTypes/UpdateTicketType",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketTypes/DeleteTicketType",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketTypes/UpdateTicketTypeOrder",
    "method": "POST"
  },
  {
    "path": "/TicketingHub/TicketTypes/GetTicketTypes",
    "method": "POST"
  }
]
