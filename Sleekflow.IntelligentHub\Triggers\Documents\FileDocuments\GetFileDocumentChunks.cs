﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class GetFileDocumentChunks
    : ITrigger<GetFileDocumentChunks.GetFileDocumentChunksInput, GetFileDocumentChunks.GetFileDocumentChunksOutput>
{
    private readonly IFileDocumentChunkService _fileDocumentChunkService;

    public GetFileDocumentChunks(
        IFileDocumentChunkService fileDocumentChunkService)
    {
        _fileDocumentChunkService = fileDocumentChunkService;
    }

    public class GetFileDocumentChunksInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetFileDocumentChunksInput(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            ContinuationToken = continuationToken;
            Limit = limit;
        }
    }

    public class GetFileDocumentChunksOutput
    {
        [JsonProperty("document_chunks")]
        public List<Chunk> DocumentChunks { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetFileDocumentChunksOutput(List<Chunk> documentChunks, string? nextContinuationToken)
        {
            DocumentChunks = documentChunks;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetFileDocumentChunksOutput> F(GetFileDocumentChunksInput getFileDocumentChunksInput)
    {
        var (fileDocumentChunks, nextContinuationToken) =
            await _fileDocumentChunkService.GetFileDocumentChunksAsync(
                getFileDocumentChunksInput.SleekflowCompanyId,
                getFileDocumentChunksInput.DocumentId,
                getFileDocumentChunksInput.ContinuationToken,
                getFileDocumentChunksInput.Limit);

        return new GetFileDocumentChunksOutput(fileDocumentChunks.Cast<Chunk>().ToList(), nextContinuationToken);
    }
}