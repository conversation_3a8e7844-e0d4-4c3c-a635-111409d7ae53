﻿using System;
using System.Collections.Generic;
using System.Linq;
using Bogus;
using Sleekflow.Auth0.BulkImporter.Models;

namespace Sleekflow.Auth0.BulkImporter.Tests
{
    public static class MockData
    {
        public static IList<string> GetRoleNames()
        {
            var originRolesType = typeof(ApplicationUserRoles);
            var originRoles = new List<string>();

            foreach (var item in originRolesType.GetFields())
            {
                originRoles.Add(item.GetValue(null)!.ToString());
            }

            return originRoles;
        }

        public static ImportUser GetFakeUser(bool haveEmail = true, bool haveUserName = true)
        {
            var faker = new Faker("en");
            var userId = faker.Random.Guid().ToString();
            return new ImportUser()
            {
                UserId = userId,
                Email = haveEmail ? faker.Internet.Email() : null,
                EmailVerified = faker.Random.Bool(),
                UserName = haveUserName ? faker.Internet.UserName() : null,
                DisplayName = faker.Name.FullName(),
                Picture = faker.Image.PicsumUrl(),
                /*
                // We need to keep this for custom password hash testing.
                CustomPasswordHash = new CustomPasswordHash()
                {
                    Algorithm = "pbkdf2",
                    Hash = new Hash()
                    {
                        Value =
                            PasswordUtils.ToAuth0PasswordHash(
                                "AQAAAAEAACcQAAAAEOA0ar+xeNmqnUdpwOyv6WB6uHi39pthb6aJm80d1zm2H5RodwW0f3HIX7JC4Mzf+g==")

                        // "$pbkdf2-sha256$i=10000,l=32$joylhPrQ0wn5Z20OkuAChw$IVHxkxsmVIwRJJrZiqOMgR5s9u2NDNgQWcCdHHfOPGA"
                    }
                }, */
                PasswordHash = BCrypt.Net.BCrypt.EnhancedHashPassword("P@ssw0rd", 10),
                AppMetadata = new MetaData()
                {
                    SleekflowId = userId,
                    Roles = GetRoleNames().Skip(new Random().Next(0, GetRoleNames().Count - 1)).Take(1).ToList(),
                    PhoneNumber = faker.Phone.PhoneNumber("852########")
                } // faker.PickRandomParam<ApplicationUserRole>()}
            };
        }

        public static List<ImportUser> GetFakeUsers(int count, bool haveEmail = true, bool haveUserName = true)
        {
            var items = new List<ImportUser>();
            for (int i = 0; i < count; i++)
            {
                items.Add(GetFakeUser(haveEmail, haveUserName));
            }

            return items;
        }
    }
}