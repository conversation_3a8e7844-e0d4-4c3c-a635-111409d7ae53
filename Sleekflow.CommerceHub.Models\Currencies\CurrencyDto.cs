using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Languages;

namespace Sleekflow.CommerceHub.Models.Currencies;

public class CurrencyDto
{
    [JsonProperty(Currency.PropertyNameCurrencyIsoCode)]
    public string CurrencyIsoCode { get; set; }

    [JsonProperty(Currency.PropertyNameCurrencyName)]
    public string CurrencyName { get; set; }

    [JsonProperty(Currency.PropertyNameCurrencySymbol)]
    public string CurrencySymbol { get; set; }

    [JsonConstructor]
    public CurrencyDto(
        string currencyIsoCode,
        string currencyName,
        string currencySymbol)
    {
        CurrencyIsoCode = currencyIsoCode;
        CurrencyName = currencyName;
        CurrencySymbol = currencySymbol;
    }

    public CurrencyDto(Currency currency)
    {
        CurrencyIsoCode = currency.CurrencyIsoCode;
        CurrencyName = currency.CurrencyName;
        CurrencySymbol = currency.CurrencySymbol;
    }
}