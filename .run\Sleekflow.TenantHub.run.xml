<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.TenantHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.TenantHub/bin/Debug/net8.0/Sleekflow.TenantHub.exe" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.TenantHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="ACTION_AUDIENCE" value="https://api-dev.sleekflow.io/" />
      <env name="ACTION_ISSUER" value="https://sso-dev.sleekflow.io/" />
      <env name="APP_CONFIGURATION_CONN_STR" value="Endpoint=https://sleekflow-app-configurationb83ec65c.azconfig.io;Id=0f3Z-lb-s0:hWLXNKUM3ATACVXeEqAT;Secret=Ub6/5/VjpnRCXqQ4JNhRVzVp350ymvs0QP4cbFm0ACo=" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="TRUE" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7103;http://localhost:7104" />
      <env name="AUTH0_CLIENT_ID" value="A8CAuyJshC04w6FYYZZdxAtfoXSvKFnU" />
      <env name="AUTH0_CLIENT_SECRET" value="****************************************************************" />
      <env name="AUTH0_DOMAIN" value="sleekflow-dev.eu.auth0.com" />
      <env name="CACHE_PREFIX" value="Sleekflow.TenantHub" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_KEY" value="**************************************************************************************==" />
      <env name="COSMOS_TENANT_HUB_DB_DATABASE_ID" value="tenanthubdb" />
      <env name="COSMOS_TENANT_HUB_DB_ENDPOINT" value="https://sleekflow-global95ca408e.documents.azure.com:443/" />
      <env name="COSMOS_TENANT_HUB_DB_KEY" value="**************************************************************************************==" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowc9e40e34.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=AFm8qHjyFme4dwrfbBKxWqCwDXcHd4klL58Db+FxG7c=" />
      <env name="FILE_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=s3dbb281f;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="OPA_SERVER_URL" value="http://localhost:8181" />
      <env name="OPA_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=s3b945c67;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="OPAL_SERVER_URL" value="http://localhost:7002" />
      <env name="PARTNERSTACK_API_KEY" value="pk_eeItTObgg6PY5VlABiY6bsV8jAxS0iip" />
      <env name="PARTNERSTACK_API_SECRET" value="sk_PyPggsn1BZemgNPaO0h9LPc9RdGMUNlH" />
      <env name="REDIS_CONN_STR" value="127.0.0.1:6379" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="SF_LOCATION" value="local" />
      <env name="TRAVIS_BACKEND_AUTH0_SECRET" value="6a9_nBt?)R#@_he=v2Eo!K3B3Ae0ao`x*I`m}ZSX*S~hsQ7bQD]k#gh8r38ad,9o" />
      <env name="TRAVIS_BACKEND_BASE_URL" value="https://localhost:5000" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.TenantHub/Sleekflow.TenantHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>