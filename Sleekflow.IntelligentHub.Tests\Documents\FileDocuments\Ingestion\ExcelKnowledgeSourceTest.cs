using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Moq;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.Ingestion;

[TestFixture]
[TestOf(typeof(ExcelKnowledgeSourceTest))]
public class ExcelKnowledgeSourceTest
{
    // Relative path from the test execution directory to the Binaries folder
    const string ExcelFilePath = "../../../Binaries/100-excel-rows.xlsx";

    [Test]
    public async Task ExcelKnowledgeSourceIngestTest()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test takes too long in git action");
        }

        using var scope = Application.Host.Services.CreateScope();
        var kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        var azureFormRecognizerConfig = scope.ServiceProvider.GetRequiredService<IAzureFormRecognizerConfig>();
        var logger = new Mock<ILogger<ExcelKnowledgeSource>>().Object;

        var excelKnowledgeSource = new ExcelKnowledgeSource(logger, kernel, azureFormRecognizerConfig);

        var allMarkdowns = new List<string>();
        IFileIngestionProgress? progress = null;

        do
        {
            await using var fileStream = new FileStream(
                ExcelFilePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read);

            var (markdowns, updatedProgress) = await excelKnowledgeSource.Ingest(fileStream, progress);

            // Add new markdowns to our collection
            allMarkdowns.AddRange(markdowns);

            // Update progress for next iteration
            progress = updatedProgress;

            // Continue until all worksheets are processed
        }
        while (!progress.IsCompleted());

        Assert.That(allMarkdowns, Is.Not.Empty);
        Assert.That(progress.IsCompleted(), Is.True);
        Assert.That(progress.GetProgressPercentage(), Is.EqualTo(100.0));
    }
}