using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Workers.Helpers;

public static class SplitContactDictionaryHelper
{
    // Helper method to split the contacts dictionary into the specified number of chunks
    public static Dictionary<string, ContactDetail>[] Chunk(
        Dictionary<string, ContactDetail> contacts,
        int numberOfChunks)
    {
        if (contacts == null)
        {
            throw new ArgumentNullException(nameof(contacts));
        }

        if (numberOfChunks <= 0)
        {
            throw new ArgumentOutOfRangeException(nameof(numberOfChunks), "Number of chunks must be greater than zero.");
        }

        if (contacts.Count == 0)
        {
            return[];
        }

        // If more chunks requested than items, return one item per chunk
        if (numberOfChunks >= contacts.Count)
        {
            return contacts.Select(kvp => new Dictionary<string, ContactDetail> { { kvp.Key, kvp.Value } }).ToArray();
        }

        var chunks = new List<Dictionary<string, ContactDetail>>(numberOfChunks);
        for (int i = 0; i < numberOfChunks; i++)
        {
            chunks.Add(new Dictionary<string, ContactDetail>());
        }

        int baseChunkSize = contacts.Count / numberOfChunks;
        int remainder = contacts.Count % numberOfChunks;
        int sourceIndex = 0;
        var sourceList = contacts.ToList(); // Convert once for indexed access

        for (int chunkIndex = 0; chunkIndex < numberOfChunks; chunkIndex++)
        {
            int currentChunkTargetSize = baseChunkSize + (remainder > 0 ? 1 : 0);

            for (int itemIndex = 0; itemIndex < currentChunkTargetSize && sourceIndex < contacts.Count; itemIndex++)
            {
                var kvp = sourceList[sourceIndex];
                chunks[chunkIndex].Add(kvp.Key, kvp.Value);
                sourceIndex++;
            }

            if (remainder > 0)
            {
                remainder--;
            }
        }

        return chunks.ToArray();
    }
}

