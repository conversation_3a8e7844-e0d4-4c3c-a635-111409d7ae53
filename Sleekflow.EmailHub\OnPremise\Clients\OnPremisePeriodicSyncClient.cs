using MailKit;
using MailKit.Security;
using Serilog;

namespace Sleekflow.EmailHub.OnPremise.Clients;

public class OnPremisePeriodicSyncClient : OnPremiseClient
{
    public IList<IMessageSummary> MessageSummaries { get; private set; }

    private readonly ILogger<OnPremisePeriodicSyncClient> _logger;
    private readonly int _startIndex;

    public OnPremisePeriodicSyncClient(
        string serverType,
        string hostName,
        string username,
        string password,
        int portNumber,
        SecureSocketOptions sslOptions,
        int startIndex,
        FetchRequest? fetchRequest = null)
        : base(
            serverType,
            hostName,
            username,
            password,
            portNumber,
            fetchRequest ?? new FetchRequest(MessageSummaryItems.UniqueId),
            sslOptions)
    {
        _startIndex = startIndex;
        MessageSummaries = new List<IMessageSummary>();
        _logger = new LoggerFactory().AddSerilog().CreateLogger<OnPremisePeriodicSyncClient>();
    }

    public override async Task RunAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await ReconnectAsync();
            MessageSummaries = await FetchMessageAndSummariesAsync(_startIndex);
        }
        catch (OperationCanceledException)
        {
            // ignored
        }

        _logger.LogInformation(
            "[OnPremisePeriodicSyncClient]: Finish RunAsync," +
            "serverName: {hostName}",
            HostName);
    }
}