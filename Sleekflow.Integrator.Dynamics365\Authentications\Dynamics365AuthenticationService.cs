using System.Net;
using AutoMapper;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Configs;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Dynamics365.Authentications;

public interface IDynamics365AuthenticationService
{
    Task<Dynamics365Authentication?> GetOrDefaultAsync(
        string sleekflowCompanyId);

    Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string returnToUrl,
        Dictionary<string, object?>? additionalDetails);

    Task<Dynamics365Authentication?> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId);

    Task<(Dynamics365Authentication Authentication, string? ReturnToUrl)> HandleAuthenticateCallbackAndStoreAsync(
        string code,
        string encryptedState);
}

public class Dynamics365AuthenticationService : ISingletonService, IDynamics365AuthenticationService
{
    private readonly ILogger<Dynamics365AuthenticationService> _logger;
    private readonly IDynamics365Config _dynamics365Config;
    private readonly IDynamics365AuthenticationRepository _dynamics365AuthenticationRepository;
    private readonly IMapper _mapper;
    private readonly IBus _bus;
    private readonly HttpClient _httpClient;

    public Dynamics365AuthenticationService(
        ILogger<Dynamics365AuthenticationService> logger,
        IDynamics365Config dynamics365Config,
        IHttpClientFactory httpClientFactory,
        IDynamics365AuthenticationRepository dynamics365AuthenticationRepository,
        IMapper mapper,
        IBus bus)
    {
        _logger = logger;
        _dynamics365Config = dynamics365Config;
        _dynamics365AuthenticationRepository = dynamics365AuthenticationRepository;
        _mapper = mapper;
        _bus = bus;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public class State
    {
        public string? SleekflowCompanyId { get; set; }

        public string? ReturnToUrl { get; set; }

        [JsonConstructor]
        public State(
            string? sleekflowCompanyId,
            string? returnToUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ReturnToUrl = returnToUrl;
        }
    }

    public class Oauth2TokenOutput
    {
        [JsonConstructor]
        public Oauth2TokenOutput(
            string tokenType,
            string scope,
            long expiresIn,
            long extExpiresIn,
            long expiresOn,
            long notBefore,
            string resource,
            string accessToken,
            string refreshToken)
        {
            TokenType = tokenType;
            Scope = scope;
            ExpiresIn = expiresIn;
            ExtExpiresIn = extExpiresIn;
            ExpiresOn = expiresOn;
            NotBefore = notBefore;
            Resource = resource;
            AccessToken = accessToken;
            RefreshToken = refreshToken;
        }

        [JsonProperty("token_type")]
        public string TokenType { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }

        [JsonProperty("expires_in")]
        public long ExpiresIn { get; set; }

        [JsonProperty("ext_expires_in")]
        public long ExtExpiresIn { get; set; }

        [JsonProperty("expires_on")]
        public long ExpiresOn { get; set; }

        [JsonProperty("not_before")]
        public long NotBefore { get; set; }

        [JsonProperty("resource")]
        public string Resource { get; set; }

        [JsonProperty("access_token")]
        public string AccessToken { get; set; }

        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; }
    }

    public async Task<Dynamics365Authentication?> GetOrDefaultAsync(string sleekflowCompanyId)
    {
        var authentication =
            await _dynamics365AuthenticationRepository.GetOrDefaultAsync(sleekflowCompanyId, sleekflowCompanyId);
        if (authentication == null)
        {
            return null;
        }

        if (DateTimeOffset.UtcNow - DateTimeOffset.FromUnixTimeSeconds(authentication.NotBefore)
            < TimeSpan.FromSeconds(authentication.ExpiresIn))
        {
            return authentication;
        }

        return await ReAuthenticateAndStoreAsync(sleekflowCompanyId);
    }

    public async Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string returnToUrl,
        Dictionary<string, object?>? additionalDetails)
    {
        string redirectUrl;
        var clientId = _dynamics365Config.Dynamics365ClientId;
        var dynamics365OauthCallbackUrl = _dynamics365Config.Dynamics365OauthCallbackUrl;

        var state = new State(sleekflowCompanyId, returnToUrl);
        var encryptedState = AesUtils.AesEncryptBase64(
            JsonConvert.SerializeObject(state),
            _dynamics365Config.Dynamics365OauthStateEncryptionKey);

        _logger.LogInformation(
            "Started AuthenticateAsync. encryptedState {EncryptedState}, state {State}, sleekflowCompanyId {SleekflowCompanyId}",
            encryptedState,
            state,
            sleekflowCompanyId);

        if (additionalDetails == null)
        {
            throw new SfInternalErrorException("Additional Details must be provided.");
        }

        var passCode = additionalDetails.GetValueOrDefault("pass_code") as string;

        // APM Monaco special treatment (use password_credential flow for that)
        if (passCode != null && passCode == "B1MXC9NMAXARR34QORLJ")
        {
            redirectUrl = await AuthenticateForApmMonacoAsync(sleekflowCompanyId);
        }
        else
        {
            var dynamics365CrmDomain = additionalDetails.ContainsKey("dynamics365_crm_domain")
                ? additionalDetails.GetValueOrDefault("dynamics365_crm_domain") as string
                : string.Empty;
            var regionCode = additionalDetails.ContainsKey("region_code")
                ? additionalDetails.GetValueOrDefault("region_code") as string
                : string.Empty;

            if (string.IsNullOrEmpty(dynamics365CrmDomain))
            {
                throw new SfInternalErrorException("dynamicsCrmDomain must be provided.");
            }

            if (string.IsNullOrEmpty(regionCode))
            {
                throw new SfInternalErrorException("Region code must be provided.");
            }

            redirectUrl =
                $"https://login.microsoftonline.com/common/oauth2/authorize?" +
                $"client_id={clientId}&" +
                $"redirect_uri={dynamics365OauthCallbackUrl}&" +
                $"response_type=code&" +
                $"resource=https://{dynamics365CrmDomain}.{regionCode}.dynamics.com" +
                $"&state={WebUtility.UrlEncode(encryptedState)}";
        }

        await _bus.Publish(
            new OnProviderInitializedEvent(
                sleekflowCompanyId,
                "d365"),
            context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });

        return redirectUrl;
    }

    private async Task<string> AuthenticateForApmMonacoAsync(string sleekflowCompanyId)
    {
        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "password"),
            new ("resource", "https://APMMONACO.crm5.dynamics.com"),
            new ("client_id", "ce9a83b8-6200-40d4-8e32-9fc7390af4a4"),
            new ("username", "<EMAIL>"),
            new ("password", "crmapm!123"),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
#pragma warning disable S1075
            "https://login.microsoftonline.com/common/oauth2/token")
#pragma warning restore S1075
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);

        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (httpResponseMessage.IsSuccessStatusCode == false)
        {
            _logger.LogError(
                "Unable to get a success /oauth2/token response from microsoft. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from microsoft");
        }

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput.AccessToken == null)
        {
            _logger.LogError(
                "Unable to get a success /oauth2/token response from salesforce. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from microsoft");
        }

        var authentication = new Dynamics365Authentication(
            sleekflowCompanyId,
            sleekflowCompanyId,
            oauth2TokenOutput.TokenType,
            oauth2TokenOutput.Scope,
            oauth2TokenOutput.ExpiresIn,
            oauth2TokenOutput.ExtExpiresIn,
            oauth2TokenOutput.ExpiresOn,
            oauth2TokenOutput.NotBefore,
            oauth2TokenOutput.Resource,
            oauth2TokenOutput.AccessToken,
            oauth2TokenOutput.RefreshToken,
            _mapper.Map<Dictionary<string, object?>>(oauth2TokenOutput),
            null);

        await _dynamics365AuthenticationRepository.UpsertAsync(
            authentication,
            sleekflowCompanyId);

        return "http://localhost";
    }

    public async Task<Dynamics365Authentication?> ReAuthenticateAndStoreAsync(string sleekflowCompanyId)
    {
        var authentication = await _dynamics365AuthenticationRepository.GetOrDefaultAsync(
            sleekflowCompanyId,
            sleekflowCompanyId);
        if (authentication == null || authentication.RefreshToken == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started ReAuthenticateAndStoreAsync. sleekflowCompanyId {SleekflowCompanyId}",
            sleekflowCompanyId);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "refresh_token"), new ("refresh_token", authentication.RefreshToken),
        };
        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
#pragma warning disable S1075
            "https://login.microsoftonline.com/common/oauth2/token");
#pragma warning restore S1075
        httpRequestMessage.Content = new FormUrlEncodedContent(nvc);
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);

        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (httpResponseMessage.IsSuccessStatusCode == false)
        {
            _logger.LogError(
                "Unable to get a success /oauth2/token response from microsoft. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from microsoft");
        }

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput.AccessToken == null)
        {
            _logger.LogError(
                "Unable to get a success /oauth2/token response from salesforce. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from microsoft");
        }

        authentication.ExpiresIn = oauth2TokenOutput.ExpiresIn;
        authentication.ExtExpiresIn = oauth2TokenOutput.ExtExpiresIn;
        authentication.ExpiresOn = oauth2TokenOutput.ExpiresOn;
        authentication.NotBefore = oauth2TokenOutput.NotBefore;
        authentication.AccessToken = oauth2TokenOutput.AccessToken;
        authentication.RefreshToken = oauth2TokenOutput.RefreshToken;
        authentication.RefreshRes = _mapper.Map<Dictionary<string, object?>>(oauth2TokenOutput);

        await _dynamics365AuthenticationRepository.UpsertAsync(
            authentication,
            sleekflowCompanyId);

        _logger.LogInformation(
            "Completed ReAuthenticateAndStoreAsync. sleekflowCompanyId {SleekflowCompanyId}",
            sleekflowCompanyId);

        return authentication;
    }

    public async Task<(Dynamics365Authentication Authentication, string? ReturnToUrl)>
        HandleAuthenticateCallbackAndStoreAsync(string code, string encryptedState)
    {
        var decryptStateStr =
            AesUtils.AesDecryptBase64(
                encryptedState,
                _dynamics365Config.Dynamics365OauthStateEncryptionKey);
        var state = decryptStateStr.ToObject<State>();
        if (state == null || string.IsNullOrEmpty(state.SleekflowCompanyId))
        {
            _logger.LogWarning("The SleekflowCompanyId is null. The state is invalid");

            throw new Exception("Unable to get a correct state.");
        }

        _logger.LogInformation(
            "Started HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new ("grant_type", "authorization_code"),
            new ("code", code),
            new ("client_id", _dynamics365Config.Dynamics365ClientId),
            new ("client_secret", _dynamics365Config.Dynamics365ClientSecret),
            new ("redirect_uri", _dynamics365Config.Dynamics365OauthCallbackUrl),
        };

        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
#pragma warning disable S1075
            "https://login.microsoftonline.com/common/oauth2/token")
#pragma warning restore S1075
        {
            Content = new FormUrlEncodedContent(nvc)
        };
        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();
        if (httpResponseMessage.IsSuccessStatusCode == false)
        {
            _logger.LogError(
                "Unable to get a success /oauth2/token response from Hubspot. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth2/token response from Dynamics365");
        }

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || oauth2TokenOutput.TokenType == null
            || oauth2TokenOutput.AccessToken == null
            || oauth2TokenOutput.RefreshToken == null)
        {
            _logger.LogError(
                "Unable to deserialize a success /oauth2/token response from Dynamics365. httpResponseMessage {HttpRequestMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to deserialize a success /oauth2/token response from Dynamics365");
        }

        var authentication = new Dynamics365Authentication(
            state.SleekflowCompanyId,
            state.SleekflowCompanyId,
            oauth2TokenOutput.TokenType,
            oauth2TokenOutput.Scope,
            oauth2TokenOutput.ExpiresIn,
            oauth2TokenOutput.ExtExpiresIn,
            oauth2TokenOutput.ExpiresOn,
            oauth2TokenOutput.NotBefore,
            oauth2TokenOutput.Resource,
            oauth2TokenOutput.AccessToken,
            oauth2TokenOutput.RefreshToken,
            _mapper.Map<Dictionary<string, object?>>(oauth2TokenOutput),
            null);

        var upsertAsync =
            await _dynamics365AuthenticationRepository.UpsertAsync(authentication, state.SleekflowCompanyId);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert Dynamics365Authentication.");
        }

        _logger.LogInformation(
            "Completed HandleAuthenticateCallbackAndStoreAsync. code {Code}, encryptedState {EncryptedState}, sleekflowCompanyId {SleekflowCompanyId}",
            code,
            encryptedState,
            state.SleekflowCompanyId);

        return (authentication, state.ReturnToUrl);
    }
}