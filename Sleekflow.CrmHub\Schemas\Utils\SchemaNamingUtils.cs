﻿using System.Text.RegularExpressions;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Schemas.Utils;

public static class SchemaNamingUtils
{
    /// <summary>
    /// Remove special chars, convert inner space to underscore, to lowercase.
    /// </summary>
    /// <param name="displayName">Display name.</param>
    /// <returns>Unique name.</returns>
    public static string GenerateUniqueName(string displayName)
    {
        const string patternSanitize = @"[^a-zA-Z0-9_]";
        const string patternMultiSpaces = @"\s+";

        var sanitized = Regex.Replace(displayName, patternSanitize, " ")
            .Trim()
            .ToLower();

        var uniqueName = Regex.Replace(sanitized, patternMultiSpaces, "_");

        if (string.IsNullOrEmpty(uniqueName))
        {
            throw new SfUserFriendlyException($"Invalid unique name [{uniqueName}] generated from display name [{displayName}]");
        }

        return uniqueName;
    }

    public static bool CheckStringDuplicate<TObj, TProperty>(List<TObj> objs, Func<TObj, TProperty> uniqueBy, string uniqueName)
    {
        return objs.Any() && objs.Exists(o => uniqueBy(o)?.ToString() == uniqueName);
    }
}