﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages;

public class InstagramMessengerMessageObject
{
    [JsonProperty("message")]
    public InstagramPageMessengerMessageObject Message { get; set; }

    /// <summary>
    /// By default, MESSAGE_TAG.
    /// </summary>
    [JsonProperty("messaging_type")]
    public string? MessagingType { get; set; }

    /// <summary>
    /// By default, HUMAN_AGENT. Required for Instagram Messaging API.
    /// </summary>
    [JsonProperty("tag")]
    public string? Tag { get; set; }

    [JsonConstructor]
    public InstagramMessengerMessageObject(
        InstagramPageMessengerMessageObject message,
        string? messagingType,
        string? tag)
    {
        Message = message;
        MessagingType = messagingType;
        Tag = tag;
    }
}

public class InstagramPageMessengerMessageObject
{
    [JsonProperty("attachment")]
    public InstagramPageMessengerAttachmentDataObject? Attachment { get; set; }

    /// <summary>
    /// 1000 bytes (characters) or less. Links must be valid formatted URLs.
    /// </summary>
    [JsonProperty("text")]
    public string? Text { get; set; }

    [JsonConstructor]
    public InstagramPageMessengerMessageObject(InstagramPageMessengerAttachmentDataObject? attachment, string? text)
    {
        Attachment = attachment;
        Text = text;
    }
}

public class InstagramPageMessengerAttachmentDataObject
{
    /// <summary>
    /// file (or template or MEDIA_SHARE or sticker,i.e. like_heart, which are not yet supported in sleekflow)
    /// type set to audio, image, or video
    /// Audio: acc, m4a, wav, mp4 formats, limited to 25 MB max size
    /// Image: png, jpeg, gif formats, limited to 8 MB max size
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("payload")]
    public InstagramPageMessengerPayloadObject? Payload { get; set; }

    /// <summary>
    /// if it is set true, it returns attachment_id in response.
    /// </summary>
    [JsonProperty("is_reusable")]
    public bool? IsReusable { get; set; }

    [JsonConstructor]
    public InstagramPageMessengerAttachmentDataObject(
        string type,
        InstagramPageMessengerPayloadObject? payload,
        bool? isReusable)
    {
        Type = type;
        Payload = payload;
        IsReusable = isReusable;
    }
}

public class InstagramPageMessengerPayloadObject
{
    /// <summary>
    /// POST-ID for MEDIA_SHARE, ATTACHMENT-ID for MEDIA_SHARE.
    /// </summary>
    [JsonProperty("id")]
    public string? Id { get; set; }

    /// <summary>
    /// Url of the media to send.
    /// </summary>
    [JsonProperty("url")]
    public string? Url { get; set; }

    [JsonConstructor]
    public InstagramPageMessengerPayloadObject(string? id, string? url)
    {
        Id = id;
        Url = url;
    }
}