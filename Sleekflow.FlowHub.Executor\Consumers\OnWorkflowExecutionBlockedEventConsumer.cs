﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowExecutionBlockedEventConsumerDefinition : ConsumerDefinition<OnWorkflowExecutionBlockedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowExecutionBlockedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowExecutionBlockedEventConsumer : IConsumer<OnWorkflowExecutionBlockedEvent>
{
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public OnWorkflowExecutionBlockedEventConsumer(
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
    }

    public async Task Consume(ConsumeContext<OnWorkflowExecutionBlockedEvent> context)
    {
        var @event = context.Message;

        var stateId = @event.StateId;
        var stateIdentity = @event.StateIdentity;
        var workflowExecutionReasonCode = @event.WorkflowExecutionReasonCode;
        var blockedAt = @event.BlockedAt;
        var workflowType = @event.WorkflowType;

        await _workflowExecutionService.CreateWorkflowExecutionAsync(
            stateId,
            stateIdentity,
            WorkflowExecutionStatuses.Blocked,
            workflowExecutionReasonCode,
            0,
            workflowType,
            null,
            blockedAt);
    }
}