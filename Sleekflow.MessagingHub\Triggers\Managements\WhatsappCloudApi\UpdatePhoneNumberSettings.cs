using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class UpdatePhoneNumberSettings
    : ITrigger<
        UpdatePhoneNumberSettings.UpdatePhoneNumberSettingsInput,
        UpdatePhoneNumberSettings.UpdatePhoneNumberSettingsOutput>
{

    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;
    private readonly ILogger<UpdatePhoneNumberSettings> _logger;

    public UpdatePhoneNumberSettings(
        IWabaService wabaService,
        IChannelService channelService,
        ILogger<UpdatePhoneNumberSettings> logger)
    {
        _wabaService = wabaService;
        _channelService = channelService;
        _logger = logger;
    }

    public class UpdatePhoneNumberSettingsInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_phone_number_id")]
        public string MessagingHubPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("storage_configuration")]
        public StorageConfiguration StorageConfiguration { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdatePhoneNumberSettingsInput(
            string sleekflowCompanyId,
            string messagingHubWabaId,
            string messagingHubPhoneNumberId,
            StorageConfiguration storageConfiguration,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            MessagingHubWabaId = messagingHubWabaId;
            MessagingHubPhoneNumberId = messagingHubPhoneNumberId;
            StorageConfiguration = storageConfiguration;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdatePhoneNumberSettingsOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public UpdatePhoneNumberSettingsOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<UpdatePhoneNumberSettingsOutput> F(
        UpdatePhoneNumberSettingsInput updatePhoneNumberSettingsInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            updatePhoneNumberSettingsInput.SleekflowStaffId,
            updatePhoneNumberSettingsInput.SleekflowStaffTeamIds);

        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            updatePhoneNumberSettingsInput.MessagingHubWabaId,
            updatePhoneNumberSettingsInput.SleekflowCompanyId,
            updatePhoneNumberSettingsInput.MessagingHubPhoneNumberId);

        if (waba == null)
        {
            return new UpdatePhoneNumberSettingsOutput(false);
        }

        var facebookPhoneNumberId = waba.WabaPhoneNumbers
            .Where(x => x.Id == updatePhoneNumberSettingsInput.MessagingHubPhoneNumberId)
            .Select(x => x.FacebookPhoneNumberId).FirstOrDefault();

        if (facebookPhoneNumberId == null)
        {
            return new UpdatePhoneNumberSettingsOutput(false);
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        UpdatePhoneNumberSettingsResponse response;
        try
        {
            response = await _channelService.UpdatePhoneNumberSettingsAsync(
                waba.FacebookWabaId,
                updatePhoneNumberSettingsInput.SleekflowCompanyId,
                facebookPhoneNumberId,
                updatePhoneNumberSettingsInput.StorageConfiguration,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Update phone number error {MessagingHubPhoneNumberId} with {Exception}",
                updatePhoneNumberSettingsInput.MessagingHubPhoneNumberId,
                ex.Message);

            throw;
        }

        return new UpdatePhoneNumberSettingsOutput(response.Success);
    }
}