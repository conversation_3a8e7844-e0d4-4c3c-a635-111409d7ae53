namespace Sleekflow.Infras.Components.Auth0s;

public class Auth0TenantHubConfig
{
    public string ActionAudience { get; }

    public string ActionIssuer { get; }

    public string ClientId { get; }

    public string ClientSecret { get; }

    public string Domain { get; }

    public string JwkUrl { get; }

    public string TravisBackendBaseUrl { get; }

    public Auth0TenantHubConfig()
    {
        var sleekflowConfig = new Pulumi.Config("sleekflow");
        ActionAudience = sleekflowConfig.Require("action_audience");
        ActionIssuer = sleekflowConfig.Require("action_issuer");
        JwkUrl = sleekflowConfig.Require("jwk_url");
        TravisBackendBaseUrl = sleekflowConfig.Require("travis_backend_base_url");
        ClientId = sleekflowConfig.Require("auth0_client_id");
        ClientSecret = sleekflowConfig.Require("auth0_client_secret");
        Domain = sleekflowConfig.Require("auth0_domain");
    }
}