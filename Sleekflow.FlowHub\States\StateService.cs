using System.Linq.Expressions;
using System.Reflection;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Sleekflow.Expressions;
using Sleekflow.FlowHub.Commons.Helpers;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Silos;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.States;

public interface IStateService : IBaseProxyStateService<ProxyState>
{
    Task<(List<ProxyState> ProxyStates, string? NextContinuationToken)> GetProxyStatesAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        StateFilters stateFilters);

    Task<(List<ProxyState> ProxyStates, string? NextContinuationToken)> GetProxyStatesAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        string objectId,
        string objectType);

    Task<StateIdentity> GetStateIdentityAsync(string stateId);

    Task<ProxyState?> GetOrDefaultStateAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId);

    Task<ProxyState?> GetOrDefaultStateByIdAsync(
        string sleekflowCompanyId,
        string stateId);

    Task<List<ProxyState>> GetRunningStatesAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        EventBody eventBody);

    Task<List<ProxyState>> GetRunningStatesByWorkflowVersionedIdAsync(
        string sleekflowCompanyId,
        string workflowVersionedId);

    Task<ProxyState> GetOrCreateStateAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        bool isReenrollment,
        ProxyWorkflow workflow,
        EventBody triggerEventBody,
        FlowHubConfig flowHubConfig);

    Task<ProxyState> CreateStateAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        string stateStatus,
        string? reasonCode,
        int? stateTtl,
        bool isReenrollment,
        ProxyWorkflow workflow,
        EventBody triggerEventBody,
        FlowHubConfig flowHubConfig,
        ContactDetail? contactDetail = null,
        string? parentStateId = null);

    Task ArchiveStateAsync(string stateId);

    Task CompleteStateAsync(string stateId);

    Task StartStateAsync(string stateId);

    Task FailStateAsync(
        string stateId,
        string? reasonCode = null);

    Task CancelStateAsync(
        string stateId,
        string reasonCode);

    Task AbandonStateAsync(
        string stateId,
        string reasonCode);

    Task BlockStateAsync(
        string stateId,
        string reasonCode);

    Task RestrictStateAsync(
        string stateId,
        string reasonCode);

    Task<(List<ProxyState> ProxyStates, string? ContinuationToken)> GetRestrictedStatesWithContinuationTokenAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        DateTimeOffset updatedAtFrom,
        DateTimeOffset updatedAtTo,
        int limit,
        string? continuationToken);

    Task MarkStateAsReenrolledAsync(
        string stateId);

    (ProxyState ParentState, CallStep<EnterAiAgentStepArgs> TargetStep) GetParentState(
        List<ProxyState> runningStates,
        string childrenWorkflowId);
}

public class StateService : BaseProxyStateService, IStateService
{
    private readonly IGrainFactory _grainFactory;
    private readonly IIdService _idService;
    private readonly IStateRepository _stateRepository;
    private readonly IAppConfig _appConfig;
    private readonly ISiloReadinessIndicator _siloReadinessIndicator;
    private readonly ILogger<StateService> _logger;
    private readonly IStateAggregator _stateAggregator;

    public StateService(
        IGrainFactory grainFactory,
        IIdService idService,
        IStateRepository stateRepository,
        IAppConfig appConfig,
        IWorkflowService workflowService,
        ISiloReadinessIndicator siloSiloReadinessIndicator,
        ILogger<StateService> logger,
        IStateAggregator stateAggregator)
    : base(stateRepository, workflowService)
    {
        _grainFactory = grainFactory;
        _idService = idService;
        _stateRepository = stateRepository;
        _appConfig = appConfig;
        _siloReadinessIndicator = siloSiloReadinessIndicator;
        _logger = logger;
        _stateAggregator = stateAggregator;
    }

    public new async Task<ProxyState> GetProxyStateAsync(string stateId)
    {
        var state = await _stateRepository.GetAsync(stateId, stateId);

        return await GetProxyStateAsync(state);
    }

    private async Task<List<ProxyState>> GetProxyStatesAsync(List<State> states)
    {
        if (states is not { Count: > 0 })
        {
            return new();
        }

        var proxyStates = await Task.WhenAll(
            states.Select(GetProxyStateAsync));

        return proxyStates.ToList();
    }

    private async Task<ProxyState> GetProxyStateAsync(State state)
    {
        // --- WAIT FOR SILO READINESS ---
        if (!_siloReadinessIndicator.IsReady)
        {
            _logger.LogWarning(
                "[{Method}] Silo not ready yet for state {StateId}. Waiting...",
                nameof(GetProxyStateAsync),
                state.Id);
            await _siloReadinessIndicator.WaitUntilReadyAsync();

            _logger.LogInformation(
                "[{Method}] Silo readiness confirmed for state {StateId}.",
                nameof(GetProxyStateAsync),
                state.Id);
        }

        var usrVarDictGrain = _grainFactory.GetGrain<IDictGrain>(state.Id + "_usr");
        var sysVarDictGrain = _grainFactory.GetGrain<IDictGrain>(state.Id + "_sys");
        var sysCompanyVarDictGrain =
            _grainFactory.GetGrain<IDictGrain>(
                state.WorkflowContext.SnapshottedWorkflow.WorkflowId
                + "_" + state.Identity.SleekflowCompanyId
                + "_sys_company");

        var proxyWorkflow = await GetProxyWorkflowAsync(state);

        return new ProxyState(
            state,
            proxyWorkflow,
            new AsyncDictionaryWrapper<string, object?>(usrVarDictGrain),
            new AsyncDictionaryWrapper<string, object?>(sysVarDictGrain),
            state.ETag,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyVarDictGrain),
            state.StateStatus);
    }

    public async Task<(List<ProxyState> ProxyStates, string? NextContinuationToken)> GetProxyStatesAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        StateFilters stateFilters)
    {
        Expression<Func<State, bool>> expression = o =>
            o.Identity.SleekflowCompanyId == sleekflowCompanyId
            && o.StateStatus != StateStatuses.Scheduled
            && o.StateStatus != StateStatuses.Abandoned
            && o.StateStatus != StateStatuses.Reenrolled;

        var (states, nextContinuationToken) = await _stateRepository.GetContinuationTokenizedObjectsAsync(
            expression
                .IfAndAlso(
                    () => stateFilters is { WorkflowId: not null },
                    s => s.Identity.WorkflowId == stateFilters.WorkflowId)
                .IfAndAlso(
                    () => stateFilters is { StateStatus: not null },
                    s => s.StateStatus == stateFilters.StateStatus)
                .IfAndAlso(
                    () => stateFilters is { FromDateTime: not null, ToDateTime: not null },
                    s => stateFilters.FromDateTime <= s.CreatedAt && s.CreatedAt <= stateFilters.ToDateTime),

            // Descending order by CreatedAt
            s => s.CreatedAt,
            false,
            continuationToken,
            limit);

        var proxyStates = await GetProxyStatesAsync(states);

        return (proxyStates, nextContinuationToken);
    }

    public async Task<(List<ProxyState> ProxyStates, string? NextContinuationToken)> GetProxyStatesAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        string objectId,
        string objectType)
    {
        var (states, nextContinuationToken) = await _stateRepository.GetContinuationTokenizedObjectsAsync(
            o =>
                o.Identity.SleekflowCompanyId == sleekflowCompanyId
                && o.Identity.ObjectId == objectId
                && o.Identity.ObjectType == objectType,
            continuationToken,
            limit);

        var proxyStates = await GetProxyStatesAsync(states);

        return (proxyStates, nextContinuationToken);
    }

    public async Task<StateIdentity> GetStateIdentityAsync(string stateId)
    {
        var state = await _stateRepository.GetAsync(stateId, stateId);

        return state.Identity;
    }

    public async Task<ProxyState?> GetOrDefaultStateAsync(string objectId, string objectType, string sleekflowCompanyId)
    {
        var states = await _stateRepository.GetObjectsAsync(
            s =>
                s.Identity.ObjectId == objectId
                && s.Identity.ObjectType == objectType
                && s.Identity.SleekflowCompanyId == sleekflowCompanyId
                && s.StateStatus == StateStatuses.Running);

        var state = states.SingleOrDefault();

        if (state == null)
        {
            return null;
        }

        return await GetProxyStateAsync(state);
    }

    public async Task<ProxyState?> GetOrDefaultStateByIdAsync(
        string sleekflowCompanyId,
        string stateId)
    {
        var state = await _stateRepository.GetOrDefaultAsync(stateId, stateId);

        if (state is null
            || state.Identity.SleekflowCompanyId != sleekflowCompanyId)
        {
            return null;
        }

        return await GetProxyStateAsync(state);
    }

    public async Task<List<ProxyState>> GetRunningStatesAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        EventBody eventBody)
    {
        var phoneNumber = ExtractContactFieldFromEventBody(eventBody, "PhoneNumber");
        var email = ExtractContactFieldFromEventBody(eventBody, "Email");

        var states = await _stateRepository.GetObjectsAsync(
            s =>
                ((s.Identity.ObjectId == objectId && (s.Identity.ObjectType == objectType || s.Identity.ObjectType == "Contact.Id"))
                 || (s.Identity.ObjectId == phoneNumber && s.Identity.ObjectType == "Contact.PhoneNumber")
                 || (s.Identity.ObjectId == email && s.Identity.ObjectType == "Contact.Email"))
                && s.Identity.SleekflowCompanyId == sleekflowCompanyId
                && (s.StateStatus == StateStatuses.Running
                    || s.StateStatus == StateStatuses.Scheduled));

        return await GetProxyStatesAsync(states);
    }

    public async Task<List<ProxyState>> GetRunningStatesByWorkflowVersionedIdAsync(
        string sleekflowCompanyId,
        string workflowVersionedId)
    {
        var states = await _stateRepository.GetObjectsAsync(
            s =>
                s.Identity.WorkflowVersionedId == workflowVersionedId
                && s.Identity.SleekflowCompanyId == sleekflowCompanyId
                && (s.StateStatus == StateStatuses.Running || s.StateStatus == StateStatuses.Scheduled));

        return await GetProxyStatesAsync(states);
    }

    public async Task<ProxyState> GetOrCreateStateAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        bool isReenrollment,
        ProxyWorkflow workflow,
        EventBody triggerEventBody,
        FlowHubConfig flowHubConfig)
    {
        var state = await GetOrDefaultStateAsync(objectId, objectType, sleekflowCompanyId);

        if (state != null)
        {
            return state;
        }

        return await CreateStateAsync(
            objectId,
            objectType,
            sleekflowCompanyId,
            StateStatuses.Running,
            null,
            null,
            isReenrollment,
            workflow,
            triggerEventBody,
            flowHubConfig);
    }

    public async Task<ProxyState> CreateStateAsync(
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        string stateStatus,
        string? reasonCode,
        int? stateTtl,
        bool isReenrollment,
        ProxyWorkflow workflow,
        EventBody triggerEventBody,
        FlowHubConfig flowHubConfig,
        ContactDetail? contactDetail = null,
        string? parentStateId = null)
    {
        var id = _idService.GetId("State");

        // --- WAIT FOR SILO READINESS ---
        if (!_siloReadinessIndicator.IsReady)
        {
            _logger.LogWarning(
                "[{Method}] Silo not ready yet for state {StateId}. Waiting...",
                nameof(CreateStateAsync),
                id);
            await _siloReadinessIndicator.WaitUntilReadyAsync();

            _logger.LogInformation(
                "[{Method}] Silo readiness confirmed for state {StateId}.",
                nameof(CreateStateAsync),
                id);
        }

        var usrVarDictGrain = _grainFactory.GetGrain<IDictGrain>(id + "_usr");
        var sysVarDictGrain = _grainFactory.GetGrain<IDictGrain>(id + "_sys");
        var sysCompanyVarDictGrain =
            _grainFactory.GetGrain<IDictGrain>(
                workflow.WorkflowId
                + "_" + sleekflowCompanyId
                + "_sys_company");

        var origin = string.IsNullOrWhiteSpace(flowHubConfig.Origin)
            ? new Uri(_appConfig.CoreInternalsEndpoint).GetLeftPart(UriPartial.Authority)
            : flowHubConfig.Origin;

        var snapshottedWorkflow = new Workflow(
            workflow.WorkflowId,
            workflow.WorkflowVersionedId,
            workflow.Name,
            workflow.WorkflowType,
            workflow.WorkflowGroupId,
            workflow.Triggers,
            workflow.WorkflowEnrollmentSettings,
            workflow.WorkflowScheduleSettings,
            workflow.Steps,
            workflow.ActivationStatus,
            workflow.Id,
            workflow.CreatedAt,
            workflow.UpdatedAt,
            workflow.SleekflowCompanyId,
            workflow.CreatedBy,
            workflow.UpdatedBy,
            workflow.Metadata,
            workflow.Version,
            workflow.ManualEnrollmentSource);

        var newState = new State(
            id,
            new StateIdentity(
                objectId,
                sleekflowCompanyId,
                workflow.WorkflowId,
                workflow.WorkflowVersionedId,
                objectType),
            new StateWorkflowContext(snapshottedWorkflow),
            triggerEventBody,
            stateStatus,
            reasonCode,
            null,
            origin,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            isReenrollment,
            stateTtl,
            parentStateId);

        var createdState = await _stateRepository.CreateAndGetAsync(newState, newState.Id);

        var proxyState = new ProxyState(
            createdState,
            workflow,
            new AsyncDictionaryWrapper<string, object?>(usrVarDictGrain),
            new AsyncDictionaryWrapper<string, object?>(sysVarDictGrain),
            createdState.ETag,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyVarDictGrain),
            createdState.StateStatus);

        if (contactDetail != null)
        {
            await _stateAggregator.AggregateStateContactDetailAsync(proxyState, contactDetail);
        }

        return proxyState;
    }

    public async Task ArchiveStateAsync(string stateId)
    {
        var state = await _stateRepository.GetOrDefaultAsync(stateId, stateId);

        if (state is not null)
        {
            // --- WAIT FOR SILO READINESS ---
            if (!_siloReadinessIndicator.IsReady)
            {
                _logger.LogWarning(
                    "[{Method}] Silo not ready yet for state {StateId}. Waiting...",
                    nameof(ArchiveStateAsync),
                    stateId);
                await _siloReadinessIndicator.WaitUntilReadyAsync();

                _logger.LogInformation(
                    "[{Method}] Silo readiness confirmed for state {StateId}.",
                    nameof(ArchiveStateAsync),
                    stateId);
            }

            var usrVarDictGrain = _grainFactory.GetGrain<IDictGrain>(stateId + "_usr");
            var sysVarDictGrain = _grainFactory.GetGrain<IDictGrain>(stateId + "_sys");
            var sysCompanyVarDictGrain =
                _grainFactory.GetGrain<IDictGrain>(
                    state.WorkflowContext.SnapshottedWorkflow.WorkflowId
                    + "_" + state.Identity.SleekflowCompanyId
                    + "_sys_company");

            await usrVarDictGrain.ClearAsync();
            await sysVarDictGrain.ClearAsync();
            await sysCompanyVarDictGrain.ClearAsync();

            await _stateRepository.DeleteAsync(
                stateId,
                stateId);
        }
    }

    public async Task CompleteStateAsync(string stateId)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Complete),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                PatchOperation.Set($"/{Entity.PropertyNameTtl}", TimeToLiveConstants.OneYearInSeconds)
            });
    }

    public async Task StartStateAsync(string stateId)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Running),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            });
    }

    public async Task FailStateAsync(
        string stateId,
        string? reasonCode = null)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Failed),
                PatchOperation.Set($"/{StateFieldNames.StateReasonCode}", reasonCode),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                PatchOperation.Set($"/{Entity.PropertyNameTtl}", TimeToLiveConstants.OneYearInSeconds)
            });
    }

    public async Task CancelStateAsync(
        string stateId,
        string reasonCode)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Cancelled),
                PatchOperation.Set($"/{StateFieldNames.StateReasonCode}", reasonCode),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                PatchOperation.Set($"/{Entity.PropertyNameTtl}", TimeToLiveConstants.OneYearInSeconds)
            });
    }

    public async Task AbandonStateAsync(
        string stateId,
        string reasonCode)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Abandoned),
                PatchOperation.Set($"/{StateFieldNames.StateReasonCode}", reasonCode),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                PatchOperation.Set($"/{Entity.PropertyNameTtl}", TimeToLiveConstants.OneYearInSeconds)
            });
    }

    public async Task BlockStateAsync(
        string stateId,
        string reasonCode)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Blocked),
                PatchOperation.Set($"/{StateFieldNames.StateReasonCode}", reasonCode),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                PatchOperation.Set($"/{Entity.PropertyNameTtl}", TimeToLiveConstants.OneYearInSeconds)
            });
    }

    public async Task RestrictStateAsync(
        string stateId,
        string reasonCode)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Restricted),
                PatchOperation.Set($"/{StateFieldNames.StateReasonCode}", reasonCode),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                PatchOperation.Set($"/{Entity.PropertyNameTtl}", TimeToLiveConstants.OneYearInSeconds)
            });
    }

    public async Task<(List<ProxyState> ProxyStates, string? ContinuationToken)> GetRestrictedStatesWithContinuationTokenAsync(
        string sleekflowCompanyId,
        string workflowVersionedId,
        DateTimeOffset updatedAtFrom,
        DateTimeOffset updatedAtTo,
        int limit,
        string? continuationToken)
    {
        var (states, nextContinuationToken) = await _stateRepository.GetContinuationTokenizedObjectsAsync(
            o =>
                o.Identity.SleekflowCompanyId == sleekflowCompanyId
                && o.Identity.WorkflowVersionedId == workflowVersionedId
                && o.StateStatus == StateStatuses.Restricted
                && o.UpdatedAt >= updatedAtFrom
                && o.UpdatedAt <= updatedAtTo,
            s => s.UpdatedAt,
            false,
            continuationToken,
            limit);

        var proxyStates = await GetProxyStatesAsync(states);

        return (proxyStates, nextContinuationToken);
    }

    public async Task MarkStateAsReenrolledAsync(string stateId)
    {
        await _stateRepository.PatchAsync(
            stateId,
            stateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/state_status", StateStatuses.Reenrolled),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                PatchOperation.Set($"/{Entity.PropertyNameTtl}", TimeToLiveConstants.OneYearInSeconds)
            });
    }

    private static string? ExtractContactFieldFromEventBody(
        EventBody eventBody,
        string contactFieldName)
        => eventBody switch
        {
            OnMessageReceivedEventBody messageReceivedEventBody =>
                (string?)messageReceivedEventBody.Contact.GetValueOrDefault(contactFieldName, string.Empty),
            _ => string.Empty
        };

    public (ProxyState ParentState, CallStep<EnterAiAgentStepArgs> TargetStep) GetParentState(
        List<ProxyState> runningStates,
        string childrenWorkflowId)
    {
        var matchingParentStates = runningStates
            .Where(x => x.StateStatus == StateStatuses.Running)
            .Where(s =>
                s.WorkflowContext.SnapshottedWorkflow.Steps
                    .OfType<CallStep<EnterAiAgentStepArgs>>()
                    .Where(stepArgs => stepArgs.Call == EnterAiAgentStepArgs.CallName)
                    .Any(stepArgs => stepArgs.Args.AiAgentWorkflowId == childrenWorkflowId))
            .ToList();

        if (matchingParentStates.Count == 0)
        {
            var errorMessage = $"No parent state found for childrenWorkflowId: '{childrenWorkflowId}' that is configured to call this workflow via EnterAiAgentStep.";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        if (matchingParentStates.Count > 1)
        {
            var errorMessage = $"Multiple parent states ({matchingParentStates.Count}) found for childrenWorkflowId: '{childrenWorkflowId}' that are configured to call this workflow via EnterAiAgentStep. Expected exactly one.";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        var uniqueParentState = matchingParentStates.Single();

        var targetStepsInParent = uniqueParentState.WorkflowContext.SnapshottedWorkflow.Steps
            .OfType<CallStep<EnterAiAgentStepArgs>>()
            .Where(s => s.Call == EnterAiAgentStepArgs.CallName && s.Args.AiAgentWorkflowId == childrenWorkflowId)
            .ToList();

        if (targetStepsInParent.Count == 0)
        {
            var errorMessage = $"Parent state '{uniqueParentState.Id}' was matched, but no specific EnterAiAgentStep pointing to child workflow '{childrenWorkflowId}' could be uniquely identified within it.";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        if (targetStepsInParent.Count > 1)
        {
            var errorMessage = $"Parent state '{uniqueParentState.Id}' was matched, but it contains multiple ({targetStepsInParent.Count}) EnterAiAgentStep entries pointing to child workflow '{childrenWorkflowId}'. Expected exactly one such step in the parent.";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        return (uniqueParentState, targetStepsInParent.Single());
    }
}