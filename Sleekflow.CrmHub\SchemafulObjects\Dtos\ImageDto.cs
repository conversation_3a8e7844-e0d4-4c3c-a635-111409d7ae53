﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.CrmHub.SchemafulObjects.Dtos;

[SwaggerInclude]
public class ImageDto
{
    [JsonProperty("download_url")]
    [StringLength(4096, MinimumLength = 1)]
    public string DownloadUrl { get; set; }

    [JsonProperty("public_blob")]
    public PublicBlobDto? PublicBlob { get; set; }

    [JsonConstructor]
    public ImageDto(
        string downloadUrl,
        PublicBlobDto? publicBlob)
    {
        DownloadUrl = downloadUrl;
        PublicBlob = publicBlob;
    }

    public static bool TryParse(string? value, out ImageDto image)
    {
        var res = false;
        try
        {
            image = JsonConvert.DeserializeObject<ImageDto>(value!) ?? throw new InvalidOperationException();
            res = true;
        }
        catch
        {
            image = new ImageDto("", null);
        }

        return res;
    }
}