using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class DocumentMessageObject : BaseMediaMessageObject
{
    [JsonConstructor]
    public DocumentMessageObject(
        string id,
        string link,
        string caption,
        string filename,
        MediaMessageObjectProvider provider)
        : base(id, link, caption, filename, provider)
    {
    }
}