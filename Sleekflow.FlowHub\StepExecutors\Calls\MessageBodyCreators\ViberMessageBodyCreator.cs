using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface IViberMessageBodyCreator : IMessageBodyCreator
{
}

public class ViberMessageBodyCreator : BaseMessageBodyCreator, IViberMessageBodyCreator
{
    public ViberMessageBodyCreator()
        : base(ChannelTypes.Viber)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        return Task.FromResult((
            CreateBaseMessageBody(
                viberMessage: new ViberMessageObject(
                    type: "text",
                    text: messageStr)),
            "text"));
    }
}