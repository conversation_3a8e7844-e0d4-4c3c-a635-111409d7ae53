using Microsoft.AspNetCore.Http;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CancellationTokens;

public interface ICancellationTokenService
{
    CancellationToken GetCancellationToken();
}

public class CancellationTokenService : ICancellationTokenService, IScopedService
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public CancellationTokenService(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public CancellationToken GetCancellationToken()
    {
        var requestAborted = _httpContextAccessor.HttpContext?.RequestAborted;
        return requestAborted ?? CancellationToken.None;
    }
}