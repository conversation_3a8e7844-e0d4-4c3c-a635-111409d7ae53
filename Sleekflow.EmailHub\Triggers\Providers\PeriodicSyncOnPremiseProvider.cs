using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Triggers.Providers;

[TriggerGroup(ControllerNames.Providers)]
public class PeriodicSyncOnPremiseProvider : ITrigger
{
    private readonly IEmailProviderSelector _emailProviderSelector;
    private readonly IProviderConfigService _providerConfigService;

    public PeriodicSyncOnPremiseProvider(
        IProviderConfigService providerConfigService,
        IEmailProviderSelector emailProviderSelector)
    {
        _providerConfigService = providerConfigService;
        _emailProviderSelector = emailProviderSelector;
    }

    public class PeriodicSyncOnPremiseProviderInput
    {
    }

    public class PeriodicSyncOnPremiseProviderOutput
    {
    }

    public async Task<PeriodicSyncOnPremiseProviderOutput> F(
        PeriodicSyncOnPremiseProviderInput input)
    {
        var periodicSyncInputs =
            await _providerConfigService.GetEmailAddressesByProviderNameAsync(ProviderNames.OnPremise);
        var providerService = _emailProviderSelector.GetEmailProvider(ProviderNames.OnPremise);
        foreach (var periodicSyncInput in periodicSyncInputs)
        {
            var sleekflowCompanyId = periodicSyncInput["sleekflow_company_id"];
            var emailAddress = periodicSyncInput["email_address"];
            await providerService.SyncEmailsAsync(sleekflowCompanyId, emailAddress);
        }

        return new PeriodicSyncOnPremiseProviderOutput();
    }
}