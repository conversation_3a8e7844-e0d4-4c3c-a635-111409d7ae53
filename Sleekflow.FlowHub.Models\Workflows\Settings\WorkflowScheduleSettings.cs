﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.DurablePayloads;
using Sleekflow.FlowHub.Models.Constants;

namespace Sleekflow.FlowHub.Models.Workflows.Settings;

public class WorkflowScheduleSettings : IValidatableObject
{
    [JsonProperty("durable_payload")]
    public DurablePayload? DurablePayload { get; set; }

    [JsonProperty("is_new_scheduled_workflow_schema")]
    public bool? IsNewScheduledWorkflowSchema { get; set; }

    [JsonProperty("is_old_scheduled_workflow_schema_first_recurring_completed")]
    public bool? IsOldScheduledWorkflowSchemaFirstRecurringCompleted { get; set; }

    [JsonProperty("scheduled_at")]
    public DateTimeOffset? ScheduledAt { get; set; }

    [JsonProperty("contact_property_id")]
    public string? ContactPropertyId { get; set; }

    [JsonProperty("schema_id")]
    public string? SchemaId { get; set; }

    [JsonProperty("schemaful_object_property_id")]
    public string? SchemafulObjectPropertyId { get; set; }

    [JsonProperty("schedule_type")]
    public string ScheduleType { get; set; }

    [JsonProperty("recurring_settings")]
    public WorkflowRecurringSettings? RecurringSettings { get; set; }

    [JsonProperty("contact_property_date_time_settings")]
    public ContactPropertyDateTimeSettings? ContactPropertyDateTimeSettings { get; set; }

    [JsonProperty("custom_object_date_time_settings")]
    public CustomObjectDateTimeSettings? CustomObjectDateTimeSettings { get; set; }

    [JsonConstructor]
    public WorkflowScheduleSettings(
        DurablePayload? durablePayload,
        bool? isNewScheduledWorkflowSchema,
        bool? isOldScheduledWorkflowSchemaFirstRecurringCompleted,
        DateTimeOffset? scheduledAt,
        string? contactPropertyId,
        string? schemaId,
        string? schemafulObjectPropertyId,
        string scheduleType,
        WorkflowRecurringSettings? recurringSettings,
        ContactPropertyDateTimeSettings? contactPropertyDateTimeSettings = null,
        CustomObjectDateTimeSettings? customObjectDateTimeSettings = null)
    {
        DurablePayload = durablePayload;
        IsNewScheduledWorkflowSchema = isNewScheduledWorkflowSchema ?? false;
        IsOldScheduledWorkflowSchemaFirstRecurringCompleted = isOldScheduledWorkflowSchemaFirstRecurringCompleted;
        ScheduledAt = scheduledAt;
        ScheduleType = scheduleType;
        RecurringSettings = recurringSettings;
        CustomObjectDateTimeSettings = customObjectDateTimeSettings;
        ContactPropertyDateTimeSettings = contactPropertyDateTimeSettings;
        ContactPropertyId = contactPropertyId;
        SchemaId = schemaId;
        SchemafulObjectPropertyId = schemafulObjectPropertyId;
    }

    public static WorkflowScheduleSettings Default()
        => new (
            durablePayload: null,
            isNewScheduledWorkflowSchema: null,
            isOldScheduledWorkflowSchemaFirstRecurringCompleted: null,
            scheduledAt: null,
            contactPropertyId: null,
            schemaId: null,
            schemafulObjectPropertyId: null,
            WorkflowScheduleTypes.None,
            null, null);

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (ScheduleType is not (WorkflowScheduleTypes.None
            or WorkflowScheduleTypes.PredefinedDateTime
            or WorkflowScheduleTypes.ContactPropertyBasedDateTime
            or WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime))
        {
            yield return new ValidationResult(
                $"Unrecognized workflow schedule type '{ScheduleType}'.",
                new[]
                {
                    nameof(ScheduleType)
                });
        }

        if (string.Equals(ScheduleType, WorkflowScheduleTypes.PredefinedDateTime, StringComparison.OrdinalIgnoreCase))
        {
            if (ScheduledAt is null)
            {
                yield return new ValidationResult(
                    $"ScheduledAt is required when ScheduleType is '{WorkflowScheduleTypes.PredefinedDateTime}'",
                    new[]
                    {
                        nameof(ScheduledAt)
                    });
            }
            else if (ScheduledAt < DateTimeOffset.UtcNow)
            {
                yield return new ValidationResult(
                    "ScheduledAt must be in the future",
                    new[]
                    {
                        nameof(ScheduledAt)
                    });
            }
        }

        if (string.Equals(ScheduleType, WorkflowScheduleTypes.ContactPropertyBasedDateTime, StringComparison.OrdinalIgnoreCase))
        {
            if ((ContactPropertyDateTimeSettings is null && string.IsNullOrWhiteSpace(ContactPropertyId))
                || (ContactPropertyDateTimeSettings is not null && ContactPropertyDateTimeSettings.ContactPropertyId is null))
            {
                yield return new ValidationResult(
                    $"ContactPropertyId is required when ScheduleType is '{WorkflowScheduleTypes.ContactPropertyBasedDateTime}'",
                    new[]
                    {
                        nameof(ContactPropertyId)
                    });
            }

            if (ContactPropertyDateTimeSettings is null && RecurringSettings is not null)
            {
                yield return new ValidationResult(
                    $"Recurring settings are not allowed when ScheduleType is '{WorkflowScheduleTypes.ContactPropertyBasedDateTime}'",
                    new[]
                    {
                        nameof(RecurringSettings)
                    });
            }
        }

        if (string.Equals(ScheduleType, WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime, StringComparison.OrdinalIgnoreCase))
        {
            bool isInvalidAsV1 = CustomObjectDateTimeSettings is null
                                      && (string.IsNullOrWhiteSpace(SchemafulObjectPropertyId) || string.IsNullOrWhiteSpace(SchemaId));
            bool isInvalidAsV2 = CustomObjectDateTimeSettings is not null
                                     && (string.IsNullOrWhiteSpace(CustomObjectDateTimeSettings.SchemaId) || string.IsNullOrWhiteSpace(CustomObjectDateTimeSettings.SchemafulObjectPropertyId));
            if (isInvalidAsV1 || isInvalidAsV2)
            {
                yield return new ValidationResult(
                    $"SchemaId and SchemafulObjectPropertyId are required when ScheduleType is '{WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime}'",
                    new[]
                    {
                        nameof(SchemaId),
                        nameof(SchemafulObjectPropertyId)
                    });
            }

            if (CustomObjectDateTimeSettings is null && RecurringSettings is not null)
            {
                yield return new ValidationResult(
                    $"Recurring settings are not allowed when ScheduleType is '{WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime}'",
                    new[]
                    {
                        nameof(RecurringSettings)
                    });
            }
        }
    }

    public bool IsNonOldScheduledWorkflowSchema()
    {
        if (IsNewScheduledWorkflowSchema is true // new scheduled workflow schema
            || IsOldScheduledWorkflowSchemaFirstRecurringCompleted is true) // using new scheduled workflow pre-condition check logic, with old scheduled workflow schema
        {
            return true;
        }

        // non-scheduled work flow
        if (string.IsNullOrWhiteSpace(ScheduleType) ||
            ScheduleType == WorkflowScheduleTypes.None)
        {
            return true;
        }

        // old scheduled workflow schema and also using in step enrolment condition check logic
        return false;
    }
}