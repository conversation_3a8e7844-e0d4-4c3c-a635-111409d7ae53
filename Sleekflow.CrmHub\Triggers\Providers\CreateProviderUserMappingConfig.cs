﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class CreateProviderUserMappingConfig : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public CreateProviderUserMappingConfig(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class CreateProviderUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [ValidateArray]
        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }

        [JsonConstructor]
        public CreateProviderUserMappingConfigInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId,
            List<UserMapping>? userMappings)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
            UserMappings = userMappings;
        }
    }

    public class CreateProviderUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public CreateProviderUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<CreateProviderUserMappingConfigOutput> F(
        CreateProviderUserMappingConfigInput createProviderUserMappingConfigInput)
    {
        var providerService = _providerSelector.GetProviderService(
            createProviderUserMappingConfigInput.ProviderName);

        var output = await providerService.CreateProviderUserMappingConfigAsync(
            createProviderUserMappingConfigInput.SleekflowCompanyId,
            createProviderUserMappingConfigInput.ProviderConnectionId,
            createProviderUserMappingConfigInput.UserMappings);

        return new CreateProviderUserMappingConfigOutput(
            output.UserMappingConfig);
    }
}