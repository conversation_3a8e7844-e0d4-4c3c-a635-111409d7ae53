﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnZohoObjectEnrollmentToFlowHubRequestedEventConsumerDefinition : ConsumerDefinition<OnZohoObjectEnrollmentToFlowHubRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnZohoObjectEnrollmentToFlowHubRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnZohoObjectEnrollmentToFlowHubRequestedEventConsumer : IConsumer<OnZohoObjectEnrollmentToFlowHubRequestedEvent>
{
    private readonly IBus _bus;

    public OnZohoObjectEnrollmentToFlowHubRequestedEventConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<OnZohoObjectEnrollmentToFlowHubRequestedEvent> context)
    {
        var onZohoObjectEnrollmentToFlowHubRequestedEvent = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnZohoObjectEnrolledEventBody(
                onZohoObjectEnrollmentToFlowHubRequestedEvent.CreatedAt,
                onZohoObjectEnrollmentToFlowHubRequestedEvent.ZohoConnectionId,
                onZohoObjectEnrollmentToFlowHubRequestedEvent.ObjectType,
                onZohoObjectEnrollmentToFlowHubRequestedEvent.ObjectDict,
                onZohoObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowId,
                onZohoObjectEnrollmentToFlowHubRequestedEvent.FlowHubWorkflowVersionedId),
            onZohoObjectEnrollmentToFlowHubRequestedEvent.ObjectId,
            onZohoObjectEnrollmentToFlowHubRequestedEvent.ObjectType,
            onZohoObjectEnrollmentToFlowHubRequestedEvent.SleekflowCompanyId));
    }
}