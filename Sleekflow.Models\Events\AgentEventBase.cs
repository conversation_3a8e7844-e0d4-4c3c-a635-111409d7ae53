using Sleekflow.FlowHub.Models.States; // Assuming StackEntry is here

namespace Sleekflow.Models.Events
{
    public abstract class AgentEventBase
    {
        public string AggregateStepId { get; set; }

        public string ProxyStateId { get; set; }

        public Stack<StackEntry> StackEntries { get; set; }

        protected AgentEventBase(string aggregateStepId, string proxyStateId, Stack<StackEntry> stackEntries)
        {
            AggregateStepId = aggregateStepId;
            ProxyStateId = proxyStateId;
            StackEntries = stackEntries;
        }
    }
} 