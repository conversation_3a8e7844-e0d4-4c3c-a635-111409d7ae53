using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Dynamics365.Authentications;

public interface IDynamics365AuthenticationRepository : IRepository<Dynamics365Authentication>
{
}

public class Dynamics365AuthenticationRepository
    : BaseRepository<Dynamics365Authentication>,
        IDynamics365AuthenticationRepository,
        ISingletonService
{
    public Dynamics365AuthenticationRepository(
        ILogger<BaseRepository<Dynamics365Authentication>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}