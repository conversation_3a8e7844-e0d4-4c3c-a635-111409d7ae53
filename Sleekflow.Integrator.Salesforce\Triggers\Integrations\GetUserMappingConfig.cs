﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetUserMappingConfig : ITrigger
{
    private readonly ISalesforceUserMappingConfigService _salesforceUserMappingConfigService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public GetUserMappingConfig(
        ISalesforceUserMappingConfigService salesforceUserMappingConfigService,
        ISalesforceConnectionService salesforceConnectionService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceUserMappingConfigService = salesforceUserMappingConfigService;
        _salesforceConnectionService = salesforceConnectionService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class GetUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetUserMappingConfigInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class GetUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public GetUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<GetUserMappingConfigOutput> F(
        GetUserMappingConfigInput getUserMappingConfigInput)
    {
        var sleekflowCompanyId = getUserMappingConfigInput.SleekflowCompanyId;
        var connectionId = getUserMappingConfigInput.ConnectionId;

        var connection =
            await _salesforceConnectionService.GetByIdAsync(
                connectionId,
                sleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var userMappingConfig =
            await _salesforceUserMappingConfigService.GetAsync(
                sleekflowCompanyId,
                connectionId);

        return new GetUserMappingConfigOutput(
            new ProviderUserMappingConfigDto(
                userMappingConfig.Id,
                userMappingConfig.SleekflowCompanyId,
                userMappingConfig.ConnectionId,
                userMappingConfig.UserMappings));
    }
}