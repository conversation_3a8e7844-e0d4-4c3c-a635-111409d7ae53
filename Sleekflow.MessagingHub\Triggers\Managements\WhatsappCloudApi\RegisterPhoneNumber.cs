using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class RegisterPhoneNumber
    : ITrigger<
        RegisterPhoneNumber.RegisterPhoneNumberInput,
        RegisterPhoneNumber.RegisterPhoneNumberOutput>
{

    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;
    private readonly ILogger<RegisterPhoneNumber> _logger;
    private readonly ICommonRetryPolicyService _commonRetryPolicyService;

    public RegisterPhoneNumber(
        IWabaService wabaService,
        IChannelService channelService,
        ILogger<RegisterPhoneNumber> logger,
        ICommonRetryPolicyService commonRetryPolicyService)
    {
        _wabaService = wabaService;
        _channelService = channelService;
        _logger = logger;
        _commonRetryPolicyService = commonRetryPolicyService;
    }

    public class RegisterPhoneNumberInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_phone_number_id")]
        public string MessagingHubPhoneNumberId { get; set; }

        [JsonProperty("Pin")]
        public string? Pin { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public RegisterPhoneNumberInput(
            string sleekflowCompanyId,
            string messagingHubWabaId,
            string messagingHubPhoneNumberId,
            string? pin,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            MessagingHubWabaId = messagingHubWabaId;
            MessagingHubPhoneNumberId = messagingHubPhoneNumberId;
            Pin = pin;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class RegisterPhoneNumberOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public RegisterPhoneNumberOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<RegisterPhoneNumberOutput> F(
        RegisterPhoneNumberInput registerPhoneNumberInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            registerPhoneNumberInput.SleekflowStaffId,
            registerPhoneNumberInput.SleekflowStaffTeamIds);

        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            registerPhoneNumberInput.MessagingHubWabaId,
            registerPhoneNumberInput.SleekflowCompanyId,
            registerPhoneNumberInput.MessagingHubPhoneNumberId);

        if (waba == null)
        {
            return new RegisterPhoneNumberOutput(false);
        }

        var facebookPhoneNumberId = waba.WabaPhoneNumbers
            .Where(x => x.Id == registerPhoneNumberInput.MessagingHubPhoneNumberId)
            .Select(x => x.FacebookPhoneNumberId).FirstOrDefault();

        if (facebookPhoneNumberId == null)
        {
            return new RegisterPhoneNumberOutput(false);
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var response = await _commonRetryPolicyService.GetAsyncRetryPolicy().ExecuteAsync(
            async () =>
            {
                try
                {
                    return await _channelService.RegisterPhoneNumber(
                        waba.FacebookWabaId,
                        registerPhoneNumberInput.SleekflowCompanyId,
                        facebookPhoneNumberId,
                        registerPhoneNumberInput.Pin ?? "000000",
                        hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null);
                }
                catch (Exception exception)
                {
                    _logger.LogError(
                        "Register phone number error {MessagingHubPhoneNumberId} with {Exception}",
                        registerPhoneNumberInput.MessagingHubPhoneNumberId,
                        JsonConvert.SerializeObject(exception));

                    throw;
                }
            });

        return new RegisterPhoneNumberOutput(response.Success);
    }
}