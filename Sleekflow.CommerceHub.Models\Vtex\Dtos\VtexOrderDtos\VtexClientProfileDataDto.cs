﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

public record VtexClientProfileDataDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("firstName")]
    public string FirstName { get; set; }

    [JsonProperty("lastName")]
    public string LastName { get; set; }

    [JsonProperty("documentType")]
    public string DocumentType { get; set; }

    [JsonProperty("phone")]
    public string Phone { get; set; }

    [JsonProperty("corporateName")]
    public string CorporateName { get; set; }

    [JsonProperty("tradeName")]
    public string TradeName { get; set; }

    [JsonProperty("corporatePhone")]
    public string CorporatePhone { get; set; }

    [JsonProperty("isCorporate")]
    public bool IsCorporate { get; set; }

    [<PERSON>son<PERSON>roperty("userProfileId")]
    public string UserProfileId { get; set; }

    [JsonProperty("customerCode")]
    public string CustomerCode { get; set; }

    [JsonConstructor]
    public VtexClientProfileDataDto(
        string id,
        string email,
        string firstName,
        string lastName,
        string documentType,
        string phone,
        string corporateName,
        string tradeName,
        string corporatePhone,
        bool isCorporate,
        string userProfileId,
        string customerCode)
    {
        Id = id;
        Email = email;
        FirstName = firstName;
        LastName = lastName;
        DocumentType = documentType;
        Phone = phone;
        CorporateName = corporateName;
        TradeName = tradeName;
        CorporatePhone = corporatePhone;
        IsCorporate = isCorporate;
        UserProfileId = userProfileId;
        CustomerCode = customerCode;
    }
}