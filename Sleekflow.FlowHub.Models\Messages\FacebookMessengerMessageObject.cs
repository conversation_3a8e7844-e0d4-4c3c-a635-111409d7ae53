using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages;

/// <summary>
/// Please refer to https://developers.facebook.com/docs/messenger-platform/reference/send-api/#parameters here.
/// </summary>
public class FacebookMessengerMessageObject
{
    [JsonProperty("message")]
    public FacebookPageMessengerMessageObject? Message { get; set; }

    /// <summary>
    /// RESPONSE – Message is in response to a received message. This includes promotional and non-promotional messages sent inside the 24-hour standard messaging window. For example, use this tag to respond if a person asks for a reservation confirmation or an status update.
    /// UPDATE – Message is being sent proactively and is not in response to a received message. This includes promotional and non-promotional messages sent inside the the 24-hour standard messaging window.
    /// MESSAGE_TAG - Message is non-promotional and is being sent outside the 24-hour standard messaging window with a message tag. The message must match the allowed use case for the tag.
    /// </summary>
    [JsonProperty("messaging_type")]
    public string? MessagingType { get; set; }

    /// <summary>
    /// Message Tag - enables your Page to send a message to a person outside the standard 24 hour messaging window with enum string values below:
    /// ACCOUNT_UPDATE - Tags the message you are sending to your customer as a non-recurring update to their application or account.
    /// CONFIRMED_EVENT_UPDATE - Tags the message you are sending to your customer as a reminder fo an upcoming event or an update for an event in progress for which the customer is registered.
    /// CUSTOMER_FEEDBACK - Tags the message you are sending to your customer as a Customer Feedback Survey . Customer feedback messages must be sent within 7 days of the customer's last message.
    /// HUMAN_AGENT - When this tag is added to a message being sent to a person, it allows a human agent to respond to the person's message. Messages can be sent within 7 days of the person's message. Human agent support is for issues that cannot be resolved within the standard messaging window.
    /// </summary>
    [JsonProperty("tag")]
    public string? Tag { get; set; }

    [JsonConstructor]
    public FacebookMessengerMessageObject(
        FacebookPageMessengerMessageObject? message,
        string? messagingType,
        string? tag)
    {
        Message = message;
        MessagingType = messagingType;
        Tag = tag;
    }
}

public class FacebookPageMessengerMessageObject
{
    [JsonProperty("attachment")]
    public FacebookPageMessengerAttachmentObject? Attachment { get; set; }

    /// <summary>
    /// text – A message containing text only. Must be UTF-8 and less than 2000 characters.
    /// </summary>
    [JsonProperty("text")]
    public string? Text { get; set; }

    [JsonConstructor]
    public FacebookPageMessengerMessageObject(FacebookPageMessengerAttachmentObject? attachment, string? text)
    {
        Attachment = attachment;
        Text = text;
    }
}

public class FacebookPageMessengerAttachmentObject
{
    /// <summary>
    /// Type of attachment. Can be audio, file, image, template, or video. Maximum file size is 25MB.
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    /// <summary>
    /// payload object that describes the attachment (or the template content, which is not yet supported in sleekflow).
    /// </summary>
    [JsonProperty("payload")]
    public FacebookPageMessengerPayloadObject? Payload { get; set; }

    [JsonConstructor]
    public FacebookPageMessengerAttachmentObject(string type, FacebookPageMessengerPayloadObject? payload)
    {
        Type = type;
        Payload = payload;
    }
}

public class FacebookPageMessengerPayloadObject
{
    /// <summary>
    /// URL of the file to upload. Max file size is 8MB for images and 25MB for all other file types (after encoding). A Timeout is set to 75 seconds for videos and 10 seconds for all other file type.
    /// </summary>
    [JsonProperty("url")]
    public string? Url { get; set; }

    /// <summary>
    /// Set to true to make the saved asset sendable to other message recipients. Defaults to false.
    /// </summary>
    [JsonProperty("is_reusable")]
    public bool? IsReusable { get; set; }

    [JsonConstructor]
    public FacebookPageMessengerPayloadObject(string? url, bool? isReusable)
    {
        Url = url;
        IsReusable = isReusable;
    }
}