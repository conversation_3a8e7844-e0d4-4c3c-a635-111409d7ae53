using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Categories;

[TriggerGroup(ControllerNames.Categories)]
public class UpdateCategory
    : ITrigger<
        UpdateCategory.UpdateCategoryInput,
        UpdateCategory.UpdateCategoryOutput>
{
    private readonly ICategoryService _categoryService;

    public UpdateCategory(ICategoryService categoryService)
    {
        _categoryService = categoryService;
    }

    public class UpdateCategoryInput : IHasSleekflowStaff
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [ValidateArray]
        [MinLength(1)]
        [MaxLength(32)]
        [JsonProperty(Category.PropertyNameNames)]
        public List<Multilingual> Names { get; set; }

        [Required]
        [ValidateArray]
        [MinLength(1)]
        [MaxLength(32)]
        [JsonProperty(Category.PropertyNameDescriptions)]
        public List<Description> Descriptions { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateCategoryInput(
            string id,
            string sleekflowCompanyId,
            List<Multilingual> names,
            List<Description> descriptions,
            string storeId,
            string sleekflowStaffId,
            List<string>? sleekflowTeamIds)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            Names = names;
            Descriptions = descriptions;
            StoreId = storeId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowTeamIds;
        }
    }

    public class UpdateCategoryOutput
    {
        [JsonProperty("category")]
        public CategoryDto Category { get; set; }

        [JsonConstructor]
        public UpdateCategoryOutput(CategoryDto category)
        {
            Category = category;
        }
    }

    public async Task<UpdateCategoryOutput> F(UpdateCategoryInput updateCategoryInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateCategoryInput.SleekflowStaffId,
            updateCategoryInput.SleekflowStaffTeamIds);

        var category = await _categoryService.PatchAndGetCategoryAsync(
            updateCategoryInput.Id,
            updateCategoryInput.SleekflowCompanyId,
            updateCategoryInput.Names,
            updateCategoryInput.Descriptions,
            updateCategoryInput.StoreId,
            sleekflowStaff);

        return new UpdateCategoryOutput(new CategoryDto(category));
    }
}