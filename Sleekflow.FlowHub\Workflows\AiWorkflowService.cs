using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Agents.Actions;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Steps.Common;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.WorkflowAgentConfigMappings;
using Sleekflow.Ids;
using Sleekflow.Locks;
using static Sleekflow.Persistence.AuditEntity;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.Workflows;

public interface IAiWorkflowService
{
    Task<ProxyWorkflow> CreateAiWorkflowTemplate(
        string sleekflowCompanyId,
        string agentConfigId,
        SleekflowStaff createdByStaff
    );

    Task<(List<string> updatedWorkflowIds, List<string> needEnableWorkflowIds)> UpdateAiWorkflows(
        string sleekflowCompanyId,
        string agentId,
        string publishType,
        SleekflowStaff updatedByStaff
    );

    Dictionary<string, object> generateMetadataByConditions(AgentConfig agentConfig, string enterAiAgentStepId);

    List<Step> generateStepsByConditions(AgentConfig agentConfig, string enterAiAgentStepId, string exitAiAgentStepId);
}

public partial class AiWorkflowService : IAiWorkflowService, IScopedService
{
    private readonly IWorkflowService _workflowService;
    private readonly ILogger<AiWorkflowService> _logger;
    private readonly IIdService _idService;
    private readonly IAgentConfigService _agentConfigService;
    private readonly IWorkflowAgentConfigMappingService _workflowAgentConfigMappingService;
    private readonly ILockService _lockService;
    private readonly IWorkflowRepository _workflowRepository;
    private readonly IWorkflowStepsService _workflowStepsService;
    private readonly IWorkflowMetadataService _workflowMetadataService;
    private readonly IAiAgentWorkflowService _aiAgentWorkflowService;

    public AiWorkflowService(
        IWorkflowService workflowService,
        ILogger<AiWorkflowService> logger,
        IIdService idService,
        IAgentConfigService agentConfigService,
        IWorkflowAgentConfigMappingService workflowAgentConfigMappingService,
        ILockService lockService,
        IWorkflowRepository workflowRepository,
        IWorkflowStepsService workflowStepsService,
        IWorkflowMetadataService workflowMetadataService,
        IAiAgentWorkflowService aiAgentWorkflowService
    )
    {
        _workflowService = workflowService;
        _logger = logger;
        _idService = idService;
        _agentConfigService = agentConfigService;
        _workflowAgentConfigMappingService = workflowAgentConfigMappingService;
        _lockService = lockService;
        _workflowRepository = workflowRepository;
        _workflowStepsService = workflowStepsService;
        _workflowMetadataService = workflowMetadataService;
        _aiAgentWorkflowService = aiAgentWorkflowService;
    }

    public async Task<ProxyWorkflow> CreateAiWorkflowTemplate(
        string sleekflowCompanyId,
        string agentConfigId,
        SleekflowStaff createdByStaff
    )
    {
        var agentConfig = await _agentConfigService.GetAsync(sleekflowCompanyId, agentConfigId);
        if (agentConfig == null)
        {
            throw new Exception($"Agent config with ID {agentConfigId} not found.");
        }

        _logger.LogInformation("Agent config: {agentConfig}", JsonConvert.SerializeObject(agentConfig));

        var workflowId = _idService.GetId("Workflow");
        var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflowId);
        var workflowCount = await _workflowAgentConfigMappingService.GetWorkflowCountByAgentConfigIdAsync(sleekflowCompanyId, agentConfigId);
        var name = $"Ai action flow - {agentConfig.Name} ({workflowCount + 1})";
        var messageReceived = new WorkflowTrigger("{{ (true) && (true) }}");

        var triggers = new WorkflowTriggers(
            null,
            null,
            null,
            null,
            null,
            messageReceived,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null
        );

        var (steps, enterAiAgentStepId, exitAiAgentStepId) = GenerateSteps(agentConfig);
        var metadata = GenerateMetadata(agentConfig, enterAiAgentStepId, exitAiAgentStepId);

        var metadtaStr = JsonConvert.SerializeObject(metadata["NS_v1"], JsonConfig.DefaultJsonSerializerSettings);

        var newMetaJson = new Dictionary<string, object> { { "NS_v1", metadtaStr } };

        var newWorkflow = new Workflow(
            workflowId: workflowId,
            workflowVersionedId: workflowVersionedId,
            name: name,
            workflowType: WorkflowType.Normal,
            workflowGroupId: null,
            triggers: triggers,
            workflowEnrollmentSettings: null,
            workflowScheduleSettings: null,
            steps: steps,
            activationStatus: WorkflowActivationStatuses.Draft,
            id: workflowVersionedId,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow,
            sleekflowCompanyId: sleekflowCompanyId,
            createdBy: createdByStaff,
            updatedBy: null,
            metadata: newMetaJson,
            dependencyWorkflowId: null,
            version: "v2"
        );

        var workflow = await _workflowService.CreateWorkflowAsync(newWorkflow, sleekflowCompanyId);

        return workflow;
    }

    public Dictionary<string, object> generateMetadataByConditions(AgentConfig agentConfig, string enterAiAgentStepId)
    {
        var exitAiAgentMetadata = new Dictionary<string, object> { ["switch"] = new List<object>() };

        var exitConversationStep = agentConfig.Actions?.ExitConversation;
        var conditions = exitConversationStep?.Conditions;
        var switchCases = new List<object>();

        if (conditions != null)
        {
            foreach (var condition in conditions)
            {
                switchCases.Add(
                    new Dictionary<string, object>
                    {
                        ["name"] = condition.Title,
                        ["condition_criteria"] = new List<object>
                        {
                            new Dictionary<string, object>
                            {
                                ["field_path"] = new Dictionary<string, object>
                                {
                                    ["field_path"] =
                                        $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                                    ["display_path"] = "Ai Agent/Exit Condition",
                                    ["sample_value"] = "Sample",
                                    ["field_type"] = "String",
                                },
                                ["operator"] = new Dictionary<string, object>
                                {
                                    ["label"] = "contains",
                                    ["value"] = "contains",
                                },
                                ["values"] = new List<object> { condition.Title },
                            },
                        },
                    }
                );
            }
        }

        switchCases.Add(
            new Dictionary<string, object>
            {
                ["name"] = "Time out",
                ["condition_criteria"] = new List<object>
                {
                    new Dictionary<string, object>
                    {
                        ["field_path"] = new Dictionary<string, object>
                        {
                            ["field_path"] =
                                $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                            ["display_path"] = "Ai Agent/Exit Condition",
                            ["sample_value"] = "Sample",
                            ["field_type"] = "String",
                        },
                        ["operator"] = new Dictionary<string, object>
                        {
                            ["label"] = "contains",
                            ["value"] = "contains",
                        },
                        ["values"] = new List<object> { "Timeout" },
                    },
                },
            }
        );

        switchCases.Add(
            new Dictionary<string, object>
            {
                ["name"] = "System error",
                ["condition_criteria"] = new List<object>
                {
                    new Dictionary<string, object>
                    {
                        ["field_path"] = new Dictionary<string, object>
                        {
                            ["field_path"] =
                                $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                            ["display_path"] = "Ai Agent/Exit Condition",
                            ["sample_value"] = "Sample",
                            ["field_type"] = "String",
                        },
                        ["operator"] = new Dictionary<string, object>
                        {
                            ["label"] = "contains",
                            ["value"] = "contains",
                        },
                        ["values"] = new List<object> { "Internal exception" },
                    },
                },
            }
        );

        switchCases.Add(new Dictionary<string, object> { ["name"] = "Else", ["condition_criteria"] = new List<object>() });

        exitAiAgentMetadata["switch"] = switchCases;

        return exitAiAgentMetadata;
    }

    private Dictionary<string, object?> GenerateMetadata(
        AgentConfig agentConfig,
        string enterAiAgentStepId,
        string exitAiAgentStepId
    )
    {
        var enterAiAgentMetadata = new Dictionary<string, object>
        {
            ["call"] = new Dictionary<string, object>
            {
                ["id"] = EnterAiAgentStepArgs.CallName,
                ["label"] = "ai-agent",
                ["action_group"] = "ai_agent",
                ["action_subgroup"] = "ai_agent_handle",
                ["uid"] = "sleekflow.v2.enter-ai-agent:ai_agent:ai_agent_handle",
            },
        };

        var exitAiAgentMetadata = generateMetadataByConditions(agentConfig, enterAiAgentStepId);

        var metadata = new Dictionary<string, object?>();
        metadata["NS_v1"] = new Dictionary<string, object>
        {
            ["trigger"] = new Dictionary<string, object>
            {
                ["event"] = new Dictionary<string, object>
                {
                    ["id"] = "incoming-message-received",
                    ["trigger_name"] = "Incoming message received",
                    ["trigger_group"] = "Interaction",
                    ["trigger_description"] = "incoming-message-received-desc",
                    ["is_external_integration"] = false,
                    ["condition_arg_name"] = "message_received",
                },
                ["setup"] = new List<object>(),
                ["filters"] = new List<object>(),
                ["isFilterByCondition"] = true,
                ["variables"] = new List<object>(),
            },
            ["trigger-step"] = new Dictionary<string, object> { ["hasError"] = false },
            [enterAiAgentStepId] = enterAiAgentMetadata,
            [exitAiAgentStepId] = exitAiAgentMetadata,
        };

        return metadata;
    }

    public List<Step> generateStepsByConditions(
        AgentConfig agentConfig,
        string enterAiAgentStepId,
        string exitAiAgentStepId
    )
    {
        var exitConversationAction = agentConfig.Actions?.ExitConversation;
        var conditions = exitConversationAction?.Conditions;

        _logger.LogInformation("Exit conditions: {conditions}", JsonConvert.SerializeObject(conditions));

        var switchCases = new List<SwitchStepCase>();
        var sysEndSteps = new List<CallStep<EndStepArgs>>();

        if (conditions != null)
        {
            foreach (var condition in conditions)
            {
                var endStepId = Guid.NewGuid().ToString();

                switchCases.Add(
                    new SwitchStepCase(
                        id: Guid.NewGuid().ToString(),
                        name: condition.Title,
                        condition: $"{{{{ ((((sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition | string.downcase) | string.contains \"{condition.Title}\")) }}}}",
                        conditionCriteria:
                        [
                            new ConditionCriterion(
                                fieldPath: $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                                @operator: "contains",
                                values: [$"{condition.Title}"]
                            ),
                        ],
                        nextStepId: endStepId
                    )
                );

                sysEndSteps.Add(
                    new CallStep<EndStepArgs>(
                        id: endStepId,
                        name: "End",
                        assign: null,
                        nextStepId: null,
                        call: "sys.end",
                        args: new EndStepArgs()
                    )
                );
            }
        }

        // add fixed condition
        var timeoutEndStepId = Guid.NewGuid().ToString();
        switchCases.Add(
            new SwitchStepCase(
                id: Guid.NewGuid().ToString(),
                name: "Time out",
                condition: $"{{{{ ((((sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition | string.downcase) | string.contains \"Timeout\")) }}}}",
                conditionCriteria:
                [
                    new ConditionCriterion(
                        fieldPath: $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                        @operator: "contains",
                        values: ["Timeout"]),
                ],
                nextStepId: timeoutEndStepId));

        sysEndSteps.Add(
            new CallStep<EndStepArgs>(
                id: timeoutEndStepId,
                name: "End",
                assign: null,
                nextStepId: null,
                call: "sys.end",
                args: new EndStepArgs()));

        var systemErrorStepId = Guid.NewGuid().ToString();
        switchCases.Add(
            new SwitchStepCase(
                id: Guid.NewGuid().ToString(),
                name: "System error",
                condition: $"{{{{ ((((sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition | string.downcase) | string.contains \"Internal exception\")) }}}}",
                conditionCriteria:
                [
                    new ConditionCriterion(
                        fieldPath: $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                        @operator: "contains",
                        values: ["Internal exception"]),
                ],
                nextStepId: systemErrorStepId));

        sysEndSteps.Add(
            new CallStep<EndStepArgs>(
                id: systemErrorStepId,
                name: "End",
                assign: null,
                nextStepId: null,
                call: "sys.end",
                args: new EndStepArgs()));

        var elseEndStepId = Guid.NewGuid().ToString();
        switchCases.Add(
            new SwitchStepCase(
                id: Guid.NewGuid().ToString(),
                name: "Else",
                condition: "{{ true }}",
                conditionCriteria: [],
                nextStepId: elseEndStepId));
        sysEndSteps.Add(
            new CallStep<EndStepArgs>(
                id: elseEndStepId,
                name: "End",
                assign: null,
                nextStepId: null,
                call: "sys.end",
                args: new EndStepArgs()));

        var conditionStep = new SwitchStep(
            id: exitAiAgentStepId,
            name: "AI agent exit",
            assign: null,
            nextStepId: null,
            switchCases: switchCases);

        var steps = new List<Step> { conditionStep };
        steps.AddRange(sysEndSteps);
        return steps;
    }

    private (List<Step> steps, string enterAiAgentStepId, string exitAiAgentStepId) GenerateSteps(
        AgentConfig agentConfig
    )
    {
        var enterAiAgentStepId = Guid.NewGuid().ToString();
        var exitAiAgentStepId = Guid.NewGuid().ToString();

        var enterAiAgentStep = new CallStep<EnterAiAgentStepArgs>(
            id: enterAiAgentStepId,
            name: "ai-agent",
            assign: null,
            nextStepId: exitAiAgentStepId,
            call: EnterAiAgentStepArgs.CallName,
            args: new EnterAiAgentStepArgs(
                aiAgentId: agentConfig.Id,
                aiAgentWorkflowId: null,
                channelIdentityId: null,
                channelType: null
            )
        );

        var exitAiAgentSteps = generateStepsByConditions(agentConfig, enterAiAgentStepId, exitAiAgentStepId);

        var steps = new List<Step> { enterAiAgentStep };
        steps.AddRange(exitAiAgentSteps);
        return (steps, enterAiAgentStepId, exitAiAgentStepId);
    }

    private List<List<Step>> FindRelatedSteps(List<Step> steps, string agentConfigId)
    {
        if (steps == null || string.IsNullOrEmpty(agentConfigId))
        {
            return new List<List<Step>>();
        }

        var relatedStepGroups = new List<List<Step>>();

        var stepLookup = steps.ToDictionary(s => s.Id, s => s);

        foreach (var step in steps)
        {
            if (step is not CallStep<EnterAiAgentStepArgs> callStep ||
                callStep.Call != EnterAiAgentStepArgs.CallName ||
                callStep.Args.AiAgentId != agentConfigId)
            {
                continue;
            }

            var stepPair = new List<Step> { callStep };

            if (!string.IsNullOrEmpty(callStep.NextStepId) &&
                stepLookup.TryGetValue(callStep.NextStepId, out var exitStep))
            {
                stepPair.Add(exitStep);
            }

            relatedStepGroups.Add(stepPair);
        }

        return relatedStepGroups;
    }

    private List<Step> UpdateExitCondition(List<Step> steps, AgentConfig agentConfig, List<List<Step>> relatedStepGroups)
    {
        if (!relatedStepGroups.Any())
        {
            return steps;
        }

        var updatedSteps = new List<Step>(steps);
        var newConditions = agentConfig.Actions?.ExitConversation?.Conditions ?? new List<ExitCondition>();

        foreach (var stepGroup in relatedStepGroups)
        {
            if (stepGroup.Count < 2) continue;

            var enterAiAgentStep = stepGroup[0] as CallStep<EnterAiAgentStepArgs>;
            var exitStep = stepGroup[1];

            if (enterAiAgentStep == null || exitStep is not SwitchStep switchStep) continue;

            var enterAiAgentStepId = enterAiAgentStep.Id;

            var existingUserCases = switchStep.SwitchCases.Where(c => !IsSystemCondition(c.Name)).ToList();
            var systemCases = switchStep.SwitchCases.Where(c => IsSystemCondition(c.Name)).ToList();

            var existingConditionNames = existingUserCases.Select(c => c.Name).ToHashSet();
            var newConditionNames = newConditions.Select(c => c.Title).ToHashSet();

            var stepsToRemove = new HashSet<string>();
            var updatedUserCases = new List<SwitchStepCase>();

            foreach (var existingCase in existingUserCases)
            {
                if (newConditionNames.Contains(existingCase.Name))
                {
                    updatedUserCases.Add(existingCase);
                }
                else
                {
                    CollectStepsToRemove(updatedSteps, existingCase.NextStepId, stepsToRemove);
                }
            }

            foreach (var condition in newConditions)
            {
                if (!existingConditionNames.Contains(condition.Title))
                {
                    var endStepId = Guid.NewGuid().ToString();
                    var endStep = new CallStep<EndStepArgs>(
                        id: endStepId,
                        name: "End",
                        assign: null,
                        nextStepId: null,
                        call: "sys.end",
                        args: new EndStepArgs()
                    );

                    updatedSteps.Add(endStep);

                    var newCase = new SwitchStepCase(
                        id: Guid.NewGuid().ToString(),
                        name: condition.Title,
                        condition: $"{{{{ ((((sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition | string.downcase) | string.contains \"{condition.Title}\")) }}}}",
                        conditionCriteria:
                        [
                            new ConditionCriterion(
                                fieldPath: $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                                @operator: "contains",
                                values: [condition.Title]
                            ),
                        ],
                        nextStepId: endStepId
                    );

                    updatedUserCases.Add(newCase);
                }
            }

            var allCases = new List<SwitchStepCase>();
            allCases.AddRange(updatedUserCases);
            allCases.AddRange(systemCases);

            var updatedSwitchStep = new SwitchStep(
                switchStep.Id,
                switchStep.Name,
                switchStep.Assign,
                switchStep.NextStepId,
                allCases
            );

            var switchStepIndex = updatedSteps.FindIndex(s => s.Id == switchStep.Id);
            if (switchStepIndex >= 0)
            {
                updatedSteps[switchStepIndex] = updatedSwitchStep;
            }

            updatedSteps.RemoveAll(s => stepsToRemove.Contains(s.Id));
        }

        return updatedSteps;
    }

    private bool IsSystemCondition(string? conditionName)
    {
        return conditionName is "Time out" or "System error" or "Else";
    }

    private void CollectStepsToRemove(List<Step> steps, string stepId, HashSet<string> stepsToRemove)
    {
        var step = steps.FirstOrDefault(s => s.Id == stepId);
        if (step == null) return;

        stepsToRemove.Add(stepId);

        if (!string.IsNullOrEmpty(step.NextStepId))
        {
            CollectStepsToRemove(steps, step.NextStepId, stepsToRemove);
        }
    }

    private async Task<(ProxyWorkflow updatedWorkflow, bool needEnable)> UpdateAiWorkflow(
        ProxyWorkflow workflow,
        AgentConfig agentConfig,
        string publishType,
        SleekflowStaff updatedByStaff
    )
    {
        var aiAgentSteps = workflow.Steps
            .Where(s => s is CallStep<EnterAiAgentStepArgs> callStep &&
                        callStep.Call == EnterAiAgentStepArgs.CallName &&
                        callStep.Args.AiAgentId == agentConfig.Id)
            .Cast<CallStep<EnterAiAgentStepArgs>>()
            .ToList();

        if (!aiAgentSteps.Any())
        {
            return (workflow, false);
        }

        var updatedSteps = new List<Step>(workflow.Steps);
        var updatedMetadata = new Dictionary<string, object?>();

        if (workflow.Metadata != null)
        {
            updatedMetadata = JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                JsonConvert.SerializeObject(workflow.Metadata, JsonConfig.DefaultJsonSerializerSettings),
                JsonConfig.DefaultJsonSerializerSettings) ?? new Dictionary<string, object?>();
        }

        var relatedStepGroups = FindRelatedSteps(updatedSteps, agentConfig.Id);

        updatedSteps = UpdateExitCondition(updatedSteps, agentConfig, relatedStepGroups);

        updatedMetadata = UpdateExitConditionMetadata(updatedMetadata, agentConfig, relatedStepGroups, updatedSteps);

        var metadataStr = JsonConvert.SerializeObject(updatedMetadata["NS_v1"], JsonConfig.DefaultJsonSerializerSettings);
        var updatedMetaJson = new Dictionary<string, object?> { { "NS_v1", metadataStr } };

        var updatedWorkflow = await _workflowService.UpdateWorkflowAsync(
            workflow.WorkflowId,
            workflow.SleekflowCompanyId,
            workflow.Triggers,
            workflow.WorkflowEnrollmentSettings,
            workflow.WorkflowScheduleSettings,
            updatedSteps,
            workflow.Name,
            workflow.WorkflowGroupId,
            updatedByStaff,
            metadata: updatedMetaJson
        );

        var activeWorkflow = await _workflowService.GetActiveWorkflowAsync(updatedWorkflow.WorkflowId, updatedWorkflow.SleekflowCompanyId);
        if (activeWorkflow != null)
        {
            await _workflowService.DisableWorkflowAsync(
                activeWorkflow.WorkflowVersionedId,
                updatedWorkflow.SleekflowCompanyId,
                updatedByStaff
            );
        }

        if (publishType == "keep-original-status" && workflow.ActivationStatus == WorkflowActivationStatuses.Active)
        {
            return (updatedWorkflow, true);
        }

        return (updatedWorkflow, false);
    }

    private Dictionary<string, object?> UpdateExitConditionMetadata(
        Dictionary<string, object?> metadata,
        AgentConfig agentConfig,
        List<List<Step>> relatedStepGroups,
        List<Step> updatedSteps)
    {
        if (!relatedStepGroups.Any() || metadata == null || !metadata.ContainsKey("NS_v1"))
        {
            return metadata;
        }

        var updatedMetadata = JsonConvert.DeserializeObject<Dictionary<string, object?>>(
            JsonConvert.SerializeObject(metadata, JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings) ?? new Dictionary<string, object?>();

        if (updatedMetadata["NS_v1"] is not string nsV1String)
        {
            return metadata;
        }

        var nsV1Data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(
            nsV1String,
            JsonConfig.DefaultJsonSerializerSettings) ?? new Dictionary<string, object?>();

        var newConditions = agentConfig.Actions?.ExitConversation?.Conditions ?? new List<ExitCondition>();

        var existingStepIds = updatedSteps.Select(s => s.Id).ToHashSet();

        var keysToRemove = nsV1Data.Keys
            .Where(key => !existingStepIds.Contains(key) &&
                         key != "trigger" &&
                         key != "trigger-step")
            .ToList();

        foreach (var key in keysToRemove)
        {
            nsV1Data.Remove(key);
        }

        foreach (var stepGroup in relatedStepGroups)
        {
            if (stepGroup.Count < 2) continue;

            var enterAiAgentStep = stepGroup[0] as CallStep<EnterAiAgentStepArgs>;
            var exitStep = stepGroup[1];

            if (enterAiAgentStep == null || exitStep is not SwitchStep) continue;

            var enterAiAgentStepId = enterAiAgentStep.Id;
            var exitAiAgentStepId = exitStep.Id;

            if (!nsV1Data.ContainsKey(exitAiAgentStepId))
            {
                continue;
            }

            nsV1Data[exitAiAgentStepId] = generateMetadataByConditions(agentConfig, enterAiAgentStepId);
        }

        updatedMetadata["NS_v1"] = nsV1Data;
        return updatedMetadata;
    }

    private Dictionary<string, object> UpdateSwitchCasesInMetadata(
        Dictionary<string, object> exitStepMetadata,
        List<ExitCondition> newConditions,
        string enterAiAgentStepId)
    {
        if (!exitStepMetadata.ContainsKey("switch"))
        {
            return exitStepMetadata;
        }

        var switchList = exitStepMetadata["switch"] as List<object> ?? new List<object>();
        var existingCases = switchList.Cast<Dictionary<string, object>>().ToList();

        var existingUserCases = existingCases.Where(c => !IsSystemConditionInMetadata(c)).ToList();
        var systemCases = existingCases.Where(c => IsSystemConditionInMetadata(c)).ToList();

        var existingConditionNames = existingUserCases
            .Where(c => c.ContainsKey("name"))
            .Select(c => c["name"]?.ToString())
            .Where(name => !string.IsNullOrEmpty(name))
            .ToHashSet();

        var newConditionNames = newConditions.Select(c => c.Title).ToHashSet();

        var updatedUserCases = existingUserCases
            .Where(c => c.ContainsKey("name") && newConditionNames.Contains(c["name"]?.ToString()))
            .ToList();

        foreach (var condition in newConditions)
        {
            if (!existingConditionNames.Contains(condition.Title))
            {
                var newCase = CreateMetadataSwitchCase(condition.Title, enterAiAgentStepId);
                updatedUserCases.Add(newCase);
            }
        }

        var allCases = new List<object>();
        allCases.AddRange(updatedUserCases);
        allCases.AddRange(systemCases);

        var updatedMetadata = new Dictionary<string, object>(exitStepMetadata)
        {
            ["switch"] = allCases
        };

        return updatedMetadata;
    }

    private bool IsSystemConditionInMetadata(Dictionary<string, object> switchCase)
    {
        if (!switchCase.ContainsKey("name"))
        {
            return false;
        }

        var name = switchCase["name"]?.ToString();
        return name is "Time out" or "System error" or "Else";
    }

    private Dictionary<string, object> CreateMetadataSwitchCase(string conditionTitle, string enterAiAgentStepId)
    {
        return new Dictionary<string, object>
        {
            ["name"] = conditionTitle,
            ["condition_criteria"] = new List<object>
            {
                new Dictionary<string, object>
                {
                    ["field_path"] = new Dictionary<string, object>
                    {
                        ["field_path"] = $"(sys_var_dict[\"{enterAiAgentStepId}\"] | json.deserialize)?.exit_condition",
                        ["display_path"] = "Ai Agent/Exit Condition",
                        ["sample_value"] = "Sample",
                        ["field_type"] = "String",
                    },
                    ["operator"] = new Dictionary<string, object>
                    {
                        ["label"] = "contains",
                        ["value"] = "contains",
                    },
                    ["values"] = new List<object> { conditionTitle },
                },
            },
        };
    }

    public async Task<(List<string> updatedWorkflowIds, List<string> needEnableWorkflowIds)> UpdateAiWorkflows(
        string sleekflowCompanyId,
        string agentId,
        string publishType,
        SleekflowStaff updatedByStaff
    )
    {
        var agentConfig = await _agentConfigService.GetAsync(sleekflowCompanyId, agentId);
        if (agentConfig == null)
        {
            throw new Exception($"Agent config with ID {agentId} not found.");
        }
        _logger.LogInformation("Agent config: {agentConfig}", JsonConvert.SerializeObject(agentConfig));
        var workflowIds = await _workflowAgentConfigMappingService.GetWorkflowIdsByAgentConfigIdAsync(
            sleekflowCompanyId,
            agentId
        );

        if (workflowIds == null || workflowIds.Count == 0)
        {
            return (new List<string>(), new List<string>());
        }

        if (workflowIds is not { Count: > 0 })
        {
            return (new List<string>(), new List<string>());
        }

        var @lock = await _lockService.WaitUnitLockAsync(
            new[] { nameof(WorkflowService), "batch_update" },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30)
        );

        try
        {
            var workflowIdBatches = workflowIds
                .Select((id, i) => new { Id = id, Index = i })
                .GroupBy(x => x.Index / 10)
                .Select(g => g.Select(x => x.Id).ToList())
                .ToList();

            var semaphore = new SemaphoreSlim(10);
            var allUpdatedWorkflows = new List<ProxyWorkflow>();
            var allNeedEnableWorkflows = new List<ProxyWorkflow>();
            var originalStepsMap = new Dictionary<string, List<Step>>();
            var originalMetadataMap = new Dictionary<string, Dictionary<string, object?>>();

            try
            {
                foreach (var batchIds in workflowIdBatches)
                {
                    await semaphore.WaitAsync();
                    try
                    {
                        var currentWorkflows = await _workflowRepository.GetWorkflowsByIdsAsync(
                            sleekflowCompanyId,
                            batchIds
                        );

                        // save original steps and metadata
                        foreach (var workflow in currentWorkflows)
                        {
                            var steps = await _workflowStepsService.GetWorkflowStepsAsync(
                                workflow.SleekflowCompanyId,
                                workflow.WorkflowVersionedId
                            );
                            var metadata = await _workflowMetadataService.GetWorkflowMetadataAsync(
                                workflow.SleekflowCompanyId,
                                workflow.WorkflowVersionedId
                            );
                            originalStepsMap[workflow.WorkflowVersionedId] = steps;
                            originalMetadataMap[workflow.WorkflowVersionedId] = metadata;
                        }

                        var updateTasks = currentWorkflows.Select(async workflow =>
                        {
                            var proxyWorkflow = new ProxyWorkflow(workflow, originalStepsMap[workflow.WorkflowVersionedId], originalMetadataMap[workflow.WorkflowVersionedId]);
                            var (updatedWorkflow, needEnable) = await UpdateAiWorkflow(proxyWorkflow, agentConfig, publishType, updatedByStaff);
                            return (updatedWorkflow, needEnable);
                        });

                        var updatedWorkflows = await Task.WhenAll(updateTasks);
                        allUpdatedWorkflows.AddRange(updatedWorkflows.Select(t => t.updatedWorkflow));
                        allNeedEnableWorkflows.AddRange(updatedWorkflows.Where(t => t.needEnable).Select(t => t.updatedWorkflow));
                    }
                    finally
                    {
                        semaphore.Release();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during batch update, attempting rollback");

                foreach (var workflow in allUpdatedWorkflows)
                {
                    try
                    {
                        if (originalStepsMap.TryGetValue(workflow.WorkflowVersionedId, out var originalSteps))
                        {
                            await _workflowStepsService.SaveWorkflowStepsAsync(
                                workflow.SleekflowCompanyId,
                                workflow.WorkflowVersionedId,
                                originalSteps
                            );
                        }

                        if (originalMetadataMap.TryGetValue(workflow.WorkflowVersionedId, out var originalMetadata))
                        {
                            await _workflowMetadataService.SaveWorkflowMetadataAsync(
                                workflow.SleekflowCompanyId,
                                workflow.WorkflowVersionedId,
                                originalMetadata
                            );
                        }
                    }
                    catch (Exception rollbackEx)
                    {
                        _logger.LogError(
                            rollbackEx,
                            "Failed to rollback workflow {WorkflowId}",
                            workflow.WorkflowVersionedId
                        );
                    }
                }

                throw new Exception("Failed to update workflows, attempted rollback", ex);
            }

            return (allUpdatedWorkflows.Select(w => w.WorkflowId).ToList(), allNeedEnableWorkflows.Select(w => w.WorkflowVersionedId).ToList());
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

}
