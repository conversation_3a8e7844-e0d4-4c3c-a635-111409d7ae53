using Newtonsoft.Json;

namespace Sleekflow.Models.Events;

public class OnRecommendedReplyStreamingEndpointStreamRequest
{
    [JsonProperty("id")]
    public Guid CorrelationId { get; set; }

    [JsonProperty("session_id")]
    public string SessionId { get; set; }

    [JsonProperty("client_request_id")]
    public string ClientRequestId { get; set; }

    [JsonProperty("partial_recommended_reply")]
    public string PartialRecommendedReply { get; set; }

    [JsonConstructor]
    public OnRecommendedReplyStreamingEndpointStreamRequest(
        Guid correlationId,
        string sessionId,
        string clientRequestId,
        string partialRecommendedReply)
    {
        CorrelationId = correlationId;
        SessionId = sessionId;
        ClientRequestId = clientRequestId;
        PartialRecommendedReply = partialRecommendedReply;
    }
}