﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.CrmHub.Workers.Subscriptions;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.GoogleSheets;

public class SubscriptionsCheckBatch
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;
    private readonly IGoogleSheetsSubscriptionRepository _googleSheetsSubscriptionRepository;

    public SubscriptionsCheckBatch(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig,
        IGoogleSheetsSubscriptionRepository googleSheetsSubscriptionRepository)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _googleSheetsSubscriptionRepository = googleSheetsSubscriptionRepository;
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public GoogleSheetsSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            GoogleSheetsSubscription subscription,
            DateTimeOffset lastObjectModificationTime)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonProperty("next_last_object_modification_time")]
        public DateTimeOffset NextLastObjectModificationTime { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            string? after,
            DateTimeOffset nextLastObjectModificationTime)
        {
            Count = count;
            After = after;
            NextLastObjectModificationTime = nextLastObjectModificationTime;
        }
    }

    /// <seealso cref="Sleekflow.Integrator.GoogleSheets.Triggers.Internals.SubscriptionsCheckBatch"/>
    [Function("GoogleSheets_SubscriptionsCheck_Batch")]
    public async Task<SubscriptionsCheckBatchOutput> Batch(
        [ActivityTrigger]
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var inputJsonStr =
            JsonConvert.SerializeObject(subscriptionsCheckBatchInput, JsonConfig.DefaultJsonSerializerSettings);

        const int maxRetries = 3;
        var retryCount = 0;
        var baseDelay = TimeSpan.FromSeconds(2);

        HttpResponseMessage response = null;

        while (retryCount <= maxRetries)
        {
            try
            {
                // Create a new request message for each attempt to avoid reuse issues
                var requestMessage = new HttpRequestMessage
                {
                    Method = HttpMethod.Post,
                    Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
                    RequestUri = new Uri(_appConfig.GoogleSheetsIntegratorInternalsEndpoint + "/SubscriptionsCheckBatch"),
                    Headers =
                    {
                        {
                            "X-Sleekflow-Key", _appConfig.InternalsKey
                        }
                    },
                };

                response = await _httpClient.SendAsync(requestMessage);

                // If we get a 504, we'll retry
                if (response.StatusCode == System.Net.HttpStatusCode.GatewayTimeout)
                {
                    if (retryCount >= maxRetries)
                    {
                        break; // Will be handled outside the loop
                    }

                    // Exponential backoff
                    var delayMs = (int)(baseDelay.TotalMilliseconds * Math.Pow(2.0, retryCount));
                    await Task.Delay(TimeSpan.FromMilliseconds(delayMs));
                    retryCount++;
                    continue;
                }

                // If we get here, we have a non-504 response
                response.EnsureSuccessStatusCode();
                var resStr = await response.Content.ReadAsStringAsync();

                var output = resStr.ToObject<Output<dynamic>>();
                if (output == null)
                {
                    await HandleExceptionalSubscriptionsCheckEnd(subscriptionsCheckBatchInput.Subscription);
                    throw new SfInternalErrorException(
                        $"The resMsg {response}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
                }

                if (output.Success == false)
                {
                    await HandleExceptionalSubscriptionsCheckEnd(subscriptionsCheckBatchInput.Subscription);
                    throw new ErrorCodeException(output);
                }

                return ((JObject) output.Data).ToObject<SubscriptionsCheckBatchOutput>()!;
            }
            catch (HttpRequestException ex) when (ex.StatusCode == System.Net.HttpStatusCode.GatewayTimeout)
            {
                if (retryCount >= maxRetries)
                {
                    break; // Will be handled outside the loop
                }

                // Exponential backoff
                var delayMs = (int)(baseDelay.TotalMilliseconds * Math.Pow(2.0, retryCount));
                await Task.Delay(TimeSpan.FromMilliseconds(delayMs));
                retryCount++;
            }
            catch (Exception)
            {
                // For all other exceptions, clean up and rethrow
                await HandleExceptionalSubscriptionsCheckEnd(subscriptionsCheckBatchInput.Subscription);
                throw;
            }
        }

        // If we've exhausted our retries, clean up and throw an appropriate exception
        await HandleExceptionalSubscriptionsCheckEnd(subscriptionsCheckBatchInput.Subscription);
        throw new SfInternalErrorException($"Gateway Timeout when calling GoogleSheets integrator after {maxRetries} retries");
    }

    public async Task HandleExceptionalSubscriptionsCheckEnd(
        GoogleSheetsSubscription subscription)
    {
        await _googleSheetsSubscriptionRepository.PatchAsync(
            subscription.Id,
            subscription.SleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace(
                    $"/{GoogleSheetsSubscription.PropertyNameDurablePayload}",
                    (HttpManagementPayload?)null)
            });
    }
}