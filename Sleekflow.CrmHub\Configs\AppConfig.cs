﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Configs;

public interface IAppConfig
{
    string SalesforceIntegratorEndpoint { get; }

    string HubspotIntegratorEndpoint { get; }

    string Dynamics365IntegratorEndpoint { get; }

    string GoogleSheetsIntegratorEndpoint { get; }

    string ZohoIntegratorEndpoint { get; }
}

public class AppConfig : IConfig, IAppConfig
{
    public string SalesforceIntegratorEndpoint { get; private set; }

    public string HubspotIntegratorEndpoint { get; private set; }

    public string Dynamics365IntegratorEndpoint { get; private set; }

    public string GoogleSheetsIntegratorEndpoint { get; private set; }

    public string ZohoIntegratorEndpoint { get; private set; }

    public AppConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        SalesforceIntegratorEndpoint =
            Environment.GetEnvironmentVariable("SALESFORCE_INTEGRATOR_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("SALESFORCE_INTEGRATOR_ENDPOINT");
        HubspotIntegratorEndpoint =
            Environment.GetEnvironmentVariable("HUBSPOT_INTEGRATOR_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("HUBSPOT_INTEGRATOR_ENDPOINT");
        Dynamics365IntegratorEndpoint =
            Environment.GetEnvironmentVariable("DYNAMICS365_INTEGRATOR_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("DYNAMICS365_INTEGRATOR_ENDPOINT");
        GoogleSheetsIntegratorEndpoint =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_INTEGRATOR_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("GOOGLE_SHEETS_INTEGRATOR_ENDPOINT");
        ZohoIntegratorEndpoint =
            Environment.GetEnvironmentVariable("ZOHO_INTEGRATOR_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("ZOHO_INTEGRATOR_ENDPOINT");
    }
}