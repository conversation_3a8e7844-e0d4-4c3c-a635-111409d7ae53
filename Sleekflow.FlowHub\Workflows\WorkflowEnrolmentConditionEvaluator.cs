using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowEnrolmentConditionEvaluator
{
    Task<bool> EvaluateScheduledWorkflowEnrolmentConditionAsync(
        ProxyWorkflow proxyWorkflow,
        ContactDetail contactDetail);

    Task<bool> EvaluateScheduledWorkflowEnrolmentConditionAsync(
        string condition,
        string workflowName,
        ContactDetail contactDetail);
}

public class WorkflowEnrolmentConditionEvaluator : IWorkflowEnrolmentConditionEvaluator, ISingletonService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ILogger<IWorkflowEnrolmentConditionEvaluator> _logger;

    public WorkflowEnrolmentConditionEvaluator(
        IStateEvaluator stateEvaluator,
        ILogger<WorkflowEnrolmentConditionEvaluator> logger)
    {
        _stateEvaluator = stateEvaluator;
        _logger = logger;
    }

    public async Task<bool> EvaluateScheduledWorkflowEnrolmentConditionAsync(
        ProxyWorkflow proxyWorkflow,
        ContactDetail contactDetail)
    {
        // New Workflow JSON Schema
        if (proxyWorkflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema == true)
        {
            var expression = proxyWorkflow.Triggers.ScheduledWorkflowContactEnrolled?.Condition;
            if (string.IsNullOrEmpty(expression))
            {
                _logger.LogWarning("Enrollment condition expression is null or empty for workflow: {WorkflowName}", proxyWorkflow.Name);
                return false;
            }

            var isConditionFulfilled =
                _stateEvaluator.IsExpression(expression)
                && (await _stateEvaluator.EvaluateExpressionAsync(
                    proxyWorkflow.Name,
                    contactDetail,
                    expression)) is true or "true"; // Used contactData as state

            return isConditionFulfilled;
        }
        else // Old JSON Schema
        {
            // find the call step from ProxyWorkflow for condition and the corresponding contact id in proxyGrainWorkflow.WorkflowContactsDict
            // use CallStep<ScheduledTriggerConditionsCheckStepArgs>.Args.ConditionsExpr as condition expression
            // take reference to Sleekflow.FlowHub/StepExecutors/Calls/ScheduledTriggerConditionsCheckStepExecutor.cs

            var callStep = proxyWorkflow.Steps.OfType<CallStep<ScheduledTriggerConditionsCheckStepArgs>>().FirstOrDefault();

            if (callStep is null)
            {
                return false;
            }

            var isConditionFulfilled =
                _stateEvaluator.IsExpression(callStep.Args.ConditionsExpr)
                && (await _stateEvaluator.EvaluateExpressionAsync(
                    proxyWorkflow.Name,
                    contactDetail,
                    callStep.Args.ConditionsExpr)) is true or "true"; // Used contactData as state

            return isConditionFulfilled;
        }
    }

    public async Task<bool> EvaluateScheduledWorkflowEnrolmentConditionAsync(string condition, string workflowName, ContactDetail contactDetail)
    {
        return _stateEvaluator.IsExpression(condition)
               && (await _stateEvaluator.EvaluateExpressionAsync(
                   workflowName,
                   contactDetail,
                   condition)) is true or "true";
    }
}