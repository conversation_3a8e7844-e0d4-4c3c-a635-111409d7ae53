using System.Collections.Immutable;

namespace Sleekflow.IntelligentHub.Models.Constants;

public static class KnowledgeBaseSourceTypes
{
    public const string FileDocument = "file_document";
    public const string WebPageDocument = "web_page_document";

    public static readonly ImmutableList<string> SupportedKnowledgeBaseSourceType =
        new List<string>()
            {
                FileDocument, WebPageDocument
            }
            .ToImmutableList();
}