﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Scriban.Runtime;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISearchSalesforceObjectStepExecutor : IStepExecutor
{
}

public class SearchSalesforceObjectStepExecutor
    : GeneralStepExecutor<CallStep<SearchSalesforceObjectStepArgs>>,
        ISearchSalesforceObjectStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public SearchSalesforceObjectStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var searchSalesforceObjectInput = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                new SearchSalesforceObjectRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    searchSalesforceObjectInput.StateIdentity.SleekflowCompanyId,
                    searchSalesforceObjectInput.SalesforceConnectionId,
                    searchSalesforceObjectInput.ObjectType,
                    searchSalesforceObjectInput.Conditions));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnSalesforceFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("Salesforce search object operation timed out after 5 minutes")),
                typeof(OnSalesforceFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class SearchSalesforceObjectInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("object_type")]
        [Required]
        public string ObjectType { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("conditions")]
        [Required]
        [Validations.ValidateArray]
        public List<SearchObjectCondition> Conditions { get; set; }

        [JsonConstructor]
        public SearchSalesforceObjectInput(
            string stateId,
            StateIdentity stateIdentity,
            string salesforceConnectionId,
            string objectType,
            bool isCustomObject,
            List<SearchObjectCondition> conditions)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            SalesforceConnectionId = salesforceConnectionId;
            ObjectType = objectType;
            IsCustomObject = isCustomObject;
            Conditions = conditions;
        }
    }

    private async Task<SearchSalesforceObjectInput> GetArgs(
        CallStep<SearchSalesforceObjectStepArgs> callStep,
        ProxyState state)
    {
        var salesforceConnectionId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.SalesforceConnectionIdExpr)
                        ?? callStep.Args.SalesforceConnectionIdExpr);

        var objectType =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ObjectTypeExpr)
                        ?? callStep.Args.ObjectTypeExpr);

        var isCustomObjectStr =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.IsCustomObjectExpr)
                        ?? callStep.Args.IsCustomObjectExpr);

        var isCustomObject = bool.Parse(isCustomObjectStr);

        var conditionsObject = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            callStep.Args.ConditionsExpr);

        var conditions = (await Task.WhenAll(((ScriptArray)conditionsObject!).Select(async x =>
        {
            var scriptObject = (ScriptObject)x;
            var scriptObjectStringValue = (string)scriptObject["value"];

            var searchObjectConditionValue = await _stateEvaluator.EvaluateExpressionAsync(
                state, scriptObjectStringValue, state.TriggerEventBody);

            return new SearchObjectCondition(
                (scriptObject["field_name"] as string)!,
                (scriptObject["operator"] as string)!,
                searchObjectConditionValue!);
        }))).ToList();

        return new SearchSalesforceObjectInput(
            state.Id,
            state.Identity,
            salesforceConnectionId,
            objectType,
            isCustomObject,
            conditions);
    }
}