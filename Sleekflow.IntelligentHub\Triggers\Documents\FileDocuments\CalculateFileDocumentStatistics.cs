using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class CalculateFileDocumentStatistics
    : ITrigger<
        CalculateFileDocumentStatistics.CalculateFileDocumentStatisticsInput,
        CalculateFileDocumentStatistics.CalculateFileDocumentStatisticsOutput>
{
    private readonly IDocumentProcessingService _documentProcessingService;

    public CalculateFileDocumentStatistics(IDocumentProcessingService documentProcessingService)
    {
        _documentProcessingService = documentProcessingService;
    }

    public class CalculateFileDocumentStatisticsInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(FileDocument.PropertyNameBlobId)]
        [Required]
        public string BlobId { get; set; }

        [JsonProperty(FileDocument.PropertyNameBlobType)]
        [Required]
        public string BlobType { get; set; }

        [JsonConstructor]
        public CalculateFileDocumentStatisticsInput(string sleekflowCompanyId, string blobId, string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
            BlobType = blobType;
        }
    }

    public class CalculateFileDocumentStatisticsOutput
    {
        [JsonProperty(FileDocument.PropertyNameDocumentStatistics)]
        public DocumentStatistics DocumentStatistics { get; set; }

        [JsonConstructor]
        public CalculateFileDocumentStatisticsOutput(DocumentStatistics documentStatistics)
        {
            DocumentStatistics = documentStatistics;
        }
    }

    public async Task<CalculateFileDocumentStatisticsOutput> F(
        CalculateFileDocumentStatisticsInput calculateFileDocumentStatisticsInput)
    {
        var documentStatistics = await _documentProcessingService.CalculateDocumentStatistics(
            calculateFileDocumentStatisticsInput.SleekflowCompanyId,
            calculateFileDocumentStatisticsInput.BlobId,
            calculateFileDocumentStatisticsInput.BlobType);

        return new CalculateFileDocumentStatisticsOutput(documentStatistics);
    }
}