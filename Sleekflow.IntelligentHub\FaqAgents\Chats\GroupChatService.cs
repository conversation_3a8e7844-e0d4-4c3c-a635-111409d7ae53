using System.Diagnostics.CodeAnalysis;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.Agents.Orchestration.GroupChat;
using Microsoft.SemanticKernel.Agents.Runtime.InProcess;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ManagerLeadNurturings;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ReActs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

public interface IGroupChatService
{
    Task<(string SourceStr, string Reply)> HandleMultiAgentChatStream(
        string sleekflowCompanyId,
        List<SfChatEntry> chatEntries,
        CompanyAgentConfig agentConfig,
        ReplyGenerationContext replyGenerationContext);
}

public class GroupChatService : IGroupChatService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly ILogger _logger;
    private readonly IAgentCollaborationDefinitionService _agentCollaborationDefinitionService;
    private readonly IAgentDurationTracker _agentDurationTracker;
    private readonly IUserInteractionTrackingService _userInteractionTracker;

    public GroupChatService(
        Kernel kernel,
        ILogger<GroupChatService> logger,
        IAgentCollaborationDefinitionService agentCollaborationDefinitionService,
        IAgentDurationTracker agentDurationTracker,
        IUserInteractionTrackingService userInteractionTracker)
    {
        _logger = logger;
        _agentCollaborationDefinitionService = agentCollaborationDefinitionService;
        _agentDurationTracker = agentDurationTracker;
        _kernel = kernel;
        _userInteractionTracker = userInteractionTracker;
    }

    [Experimental("SKEXP0070")]
    public async Task<(string SourceStr, string Reply)> HandleMultiAgentChatStream(
        string sleekflowCompanyId,
        List<SfChatEntry> chatEntries,
        CompanyAgentConfig agentConfig,
        ReplyGenerationContext replyGenerationContext)
    {
        var groupChatIdStr = (_kernel.Data[KernelDataKeys.GROUP_CHAT_ID] as string)!;

        // Track user message for follow-up messaging logic
        // Get the latest user message from chatEntries (last entry should be the current user message)
        var latestUserMessage = chatEntries?.LastOrDefault()?.User;
        if (!string.IsNullOrEmpty(latestUserMessage))
        {
            try
            {
                // Parse groupChatId to extract objectId for tracking
                var groupChatId = GroupChatId.Parse(groupChatIdStr);
                await _userInteractionTracker.RecordUserMessageAsync(
                    sleekflowCompanyId,
                    groupChatId.ObjectId,
                    latestUserMessage);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "Failed to track user message for follow-up logic in GroupChatService");
                // Don't fail the main chat processing if tracking fails
            }
        }

        // Select agent definition
        var agentCollaborationDefinition =
            _agentCollaborationDefinitionService.GetAgentCollaborationDefinition(
                agentConfig.EffectiveCollaborationMode);
        if (agentCollaborationDefinition is LeadNurturingAgentCollaborationDefinition
            || agentCollaborationDefinition is ManagerLeadNurturingCollaborationDefinition)
        {
            _kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] =
                agentConfig.LeadNurturingTools;
            _kernel.Data[KernelDataKeys.TOOLS_CONFIG] =
                agentConfig.ToolsConfig;
            _kernel.Data[KernelDataKeys.WEB_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.WebSearch;
            _kernel.Data[KernelDataKeys.STATIC_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.StaticSearch;
        }
        else if (agentCollaborationDefinition is IReActAgentCollaborationDefinition)
        {
            _kernel.Data[KernelDataKeys.TOOLS_CONFIG] =
                agentConfig.ToolsConfig;
            _kernel.Data[KernelDataKeys.WEB_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.WebSearch;
            _kernel.Data[KernelDataKeys.STATIC_SEARCH_PLUGIN_CONFIG] =
                agentConfig.KnowledgeRetrievalConfig?.StaticSearch;
        }

        var defaultPromptInstruction = new PromptInstruction();
        var promptInstruction = agentConfig.PromptInstruction ?? defaultPromptInstruction;

        // Create a copy of the enricher configs from agent config
        var enricherConfigs = new List<EnricherConfig>(agentConfig.EnricherConfigs);

        // Add ContactPropertiesEnricher if it doesn't already exist in the list
        if (agentCollaborationDefinition is LeadNurturingAgentCollaborationDefinition
            && !enricherConfigs.Any(e => e.Type.Equals("contact_properties", StringComparison.OrdinalIgnoreCase)))
        {
            enricherConfigs.Insert(
                0,
                new EnricherConfig
                {
                    Type = "contact_properties",
                    IsEnabled = true,
                    Parameters = new Dictionary<string, string>()
                    {
                        {
                            ContactPropertiesEnricherConstants.PERMITTED_CONTACT_PROPERTIES_KEY,
                            JsonConvert.SerializeObject(
                                new List<PermittedContactProperty>
                                {
                                    new ("firstName", "The customer's first name"),
                                    new ("lastName", "The customer's last name"),
                                    new ("PhoneNumber", "The customer's phone number"),
                                    new ("Email", "The customer's email address"),
                                })
                        }
                    }
                });
        }

        var agentCollaborationConfig = new AgentCollaborationConfig(
            promptInstruction.Tone ?? defaultPromptInstruction.Tone!,
            promptInstruction.RestrictivenessLevel ?? defaultPromptInstruction.RestrictivenessLevel!,
            "English",
            promptInstruction.AdditionalInstructionCore,
            promptInstruction.AdditionalInstructionStrategy,
            promptInstruction.AdditionalInstructionResponse,
            enricherConfigs,
            agentConfig.LeadNurturingTools?.IsFollowUpAgentEnabled ?? false,
            agentConfig.IsTranscriptionEnabled);

        // Create agents dynamically
        var agents = await agentCollaborationDefinition.CreateAgents(
            _kernel,
            chatEntries,
            sleekflowCompanyId,
            agentCollaborationConfig);

        // Track duration of agent responses
        _agentDurationTracker.StartTracking();

        var groupChatManager = agentCollaborationDefinition.GetGroupChatManager();
        if (groupChatManager is not null)
        {
            ChatHistory history = [];

            // Initialize chat history
            var (chatHistoryStr, context) = await agentCollaborationDefinition.InitializeChatHistoryAsync(
                null,
                groupChatIdStr,
                chatEntries,
                replyGenerationContext,
                agentCollaborationConfig,
                agentConfig);

            var orchestration = new GroupChatOrchestration(
                groupChatManager,
                agents.ToArray())
            {
                ResponseCallback = async (response) =>
                {
                    await agentCollaborationDefinition.InterceptAgentReplyAsync(response, groupChatIdStr);

                    history.Add(response);
                }
            };

            var runtime = new InProcessRuntime();
            await runtime.StartAsync();

            var result = await orchestration.InvokeAsync(
                context,
                runtime);

            var output = await result.GetValueAsync(TimeSpan.FromSeconds(120));

            _logger.LogInformation("result: {Result}", result);
            _logger.LogInformation("output: {Output}", output);

            var sourceStr = await agentCollaborationDefinition.GetSourceAsync(history, groupChatIdStr);
            var finalReply = await agentCollaborationDefinition.GetFinalReplyAsync(history);

            return (sourceStr, finalReply);
        }
        else
        {
            var selectionStrategy =
                agentCollaborationDefinition.CreateSelectionStrategy(_kernel)
                ?? throw new NotImplementedException();
            var terminationStrategy =
                agentCollaborationDefinition.CreateTerminationStrategy(agents)
                ?? throw new NotImplementedException();

            // Configure group chat with dynamic settings
            var agentGroupChat = new AgentGroupChat(agents.ToArray())
            {
                ExecutionSettings = new AgentGroupChatSettings
                {
                    SelectionStrategy = selectionStrategy, TerminationStrategy = terminationStrategy,
                }
            };

        // Initialize chat history
        await agentCollaborationDefinition.InitializeChatHistoryAsync(
            agentGroupChat,
            groupChatIdStr,
            chatEntries,
            replyGenerationContext,
            agentCollaborationConfig,
                agentConfig);

            // Process chat responses
            await foreach (var response in agentGroupChat.InvokeAsync())
            {
                var shouldContinue =
                    await agentCollaborationDefinition.InterceptAgentReplyAsync(response, groupChatIdStr);
                if (!shouldContinue)
                {
                    return (string.Empty, string.Empty);
                }
            }

            // Log the final results
            _logger.LogInformation(
                _agentDurationTracker.GetMetricReport(agentConfig.EffectiveCollaborationMode));

            var chatHistory = await agentGroupChat.GetChatMessagesAsync().ToListAsync();
            var sourceStr = await agentCollaborationDefinition.GetSourceAsync(
                new ChatHistory(chatHistory),
                groupChatIdStr);
            var finalReply = await agentCollaborationDefinition.GetFinalReplyAsync(new ChatHistory(chatHistory));

            if (!agentGroupChat.IsComplete)
            {
                _logger.LogWarning("No consensus reached. Using last reply: {FinalReply}", finalReply);
            }

            return (sourceStr, finalReply);
        }
    }
}