﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Sleekflow.CommerceHub.Models.States.StatusQueryGetOutput;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Workers.Triggers.Vtex;

public class LoopThroughAndEnrollVtexOrdersToFlowHubOrchestrator : ITrigger
{
    private const int MaxPage = 30; // VTEX API constrain
    private const int MaxIterations = 2000; // 2000 * 50 = 100,000 orders

    public class LoopThroughAndEnrollVtexOrdersToFlowHubOrchestratorOutput : Output
    {
        public LoopThroughAndEnrollVtexOrdersToFlowHubOrchestratorOutput(int totalCount)
            : base(totalCount)
        {
        }
    }

    [Function("LoopThroughAndEnrollVtexOrdersToFlowHub_Orchestrator")]
    public async Task<LoopThroughAndEnrollVtexOrdersToFlowHubOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var orchestratorInput = context.GetInput<LoopThroughAndEnrollVtexOrdersToFlowHubInput>();

        context.SetCustomStatus(
            new CustomStatus(
                0,
                context.CurrentUtcDateTime,
                new LoopThroughVtexOrdersCustomStatusMetaData(
                    null)));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(10), 2)));

        var totalCount = 0L;
        var currentPage = 1;
        var iterations = 0;
        var condition = new VtexGetOrdersSearchCondition(
            orchestratorInput!.Condition.CreatedAtFrom,
            orchestratorInput!.Condition.CreatedAtTo,
            orchestratorInput!.Condition.OrderStatusCode);

        // Loop descending based on Order.CreatedAt
        while (true)
        {
            var batchInput = new LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput(
                orchestratorInput.SleekflowCompanyId,
                orchestratorInput.FlowHubWorkflowId,
                orchestratorInput.FlowHubWorkflowVersionedId,
                orchestratorInput.VtexAuthenticationId,
                currentPage,
                condition);

            var batchOutput = await context.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
                "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
                batchInput,
                taskOptions);

            if (batchOutput.Count == 0)
            {
                break;
            }

            totalCount += batchOutput.Count;

            context.SetCustomStatus(
                new CustomStatus(
                    totalCount,
                    context.CurrentUtcDateTime,
                    new LoopThroughVtexOrdersCustomStatusMetaData(
                        batchOutput.ProcessedEarliestOrderCreatedAt)));

            var nextCreatedAtTo = batchOutput.ProcessedEarliestOrderCreatedAt!.Value.AddSeconds(-1);

            if (!batchOutput.HasNextPage ||
                iterations++ >= MaxIterations ||
                nextCreatedAtTo < condition.CreatedAtFrom)
            {
                break;
            }

            // assign new conditions
            if (++currentPage > MaxPage)
            {
                currentPage = 1;

                // move search filter to an earlier time window
                condition.CreatedAtTo = nextCreatedAtTo;
            }

            await context.CreateTimer(context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)), CancellationToken.None);
        }

        return new LoopThroughAndEnrollVtexOrdersToFlowHubOrchestratorOutput((int)totalCount);
    }
}