﻿namespace Sleekflow.Models.Events;

public class OnUserProfileEnrolledIntoFlowHubWorkflowEvent
{
    public string SleekflowCompanyId { get; set; }

    public string WorkflowId { get; set; }

    public string WorkflowVersionedId { get; set; }

    public string WorkflowName { get; set; }

    public string SleekflowUserProfileId { get; set; }

    public string StateId { get; set; }

    public OnUserProfileEnrolledIntoFlowHubWorkflowEvent(
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        string workflowName,
        string sleekflowUserProfileId,
        string stateId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        WorkflowName = workflowName;
        SleekflowUserProfileId = sleekflowUserProfileId;
        StateId = stateId;
    }
}