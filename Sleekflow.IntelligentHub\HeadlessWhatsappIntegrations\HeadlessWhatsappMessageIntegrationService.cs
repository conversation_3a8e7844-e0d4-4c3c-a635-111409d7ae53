using System.Text;
using MassTransit;
using Newtonsoft.Json;
using Polly;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.FaqAgents.Configs;
using Sleekflow.IntelligentHub.Models.HeadlessWhatsappIntegrations;

namespace Sleekflow.IntelligentHub.HeadlessWhatsappIntegrations;

public interface IHeadlessWhatsappMessageIntegrationService
{
    Task OnMessageReceivedAsync(
        string sleekflowCompanyId,
        string contextId,
        string phoneNumber,
        string userMessage,
        DateTimeOffset sentTime);

    Task SendMessageAsync(
        string phoneNumber,
        string text,
        string sleekflowCompanyId);
}

public class HeadlessWhatsappMessageIntegrationService : IHeadlessWhatsappMessageIntegrationService, IScopedService
{
    private readonly ILogger<HeadlessWhatsappMessageIntegrationService> _logger;
    private readonly IBus _bus;
    private readonly HttpClient _httpClient;
    private readonly IHeadlessWhatsappMessageIntegrationConfig _headlessWhatsappMessageIntegrationConfig;
    private readonly IFaqAgentConfigService _faqAgentConfigService;

    public HeadlessWhatsappMessageIntegrationService(
        ILogger<HeadlessWhatsappMessageIntegrationService> logger,
        IBus bus,
        IHttpClientFactory httpClientFactory,
        IHeadlessWhatsappMessageIntegrationConfig headlessWhatsappMessageIntegrationConfig,
        IFaqAgentConfigService faqAgentConfigService)
    {
        _logger = logger;
        _bus = bus;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _headlessWhatsappMessageIntegrationConfig = headlessWhatsappMessageIntegrationConfig;
        _faqAgentConfigService = faqAgentConfigService;
    }

    public async Task OnMessageReceivedAsync(
        string sleekflowCompanyId,
        string contextId,
        string phoneNumber,
        string userMessage,
        DateTimeOffset sentTime)
    {
        var onWhatsappMessageReceivedEvent = new OnHeadlessWhatsappMessageReceivedEvent(
            sleekflowCompanyId,
            contextId,
            phoneNumber,
            userMessage,
            sentTime);

        await _bus.Publish(onWhatsappMessageReceivedEvent);
    }

    public async Task SendMessageAsync(string phoneNumber, string text, string sleekflowCompanyId)
    {
        var agentConfigOrDefault = _faqAgentConfigService.GetAgentConfigOrDefault(sleekflowCompanyId);
        if (agentConfigOrDefault == null)
        {
            throw new Exception($"Agent Config should not be null {sleekflowCompanyId}");
        }

        var payload = new SendMessageInput
        {
            MessageObject = new MessageObject
            {
                RecipientType = "individual",
                To = phoneNumber,
                Type = "text",
                Text = new Text
                {
                    Body = text, PreviewUrl = false
                },
                MessagingProduct = "whatsapp"
            }
        };

        // Define a Polly retry policy with 3 retries
        var retryPolicy = Policy.Handle<Exception>().WaitAndRetryAsync(
            3,
            retryAttempt => TimeSpan.FromSeconds(retryAttempt * 2),
            onRetry: (exception, retryCount, _) =>
            {
                _logger.LogError(
                    exception,
                    "Error occurred during attempt {RetryCount}: {Message}",
                    retryCount,
                    exception.Message);
            });

        // Execute the SendMessage operation with the defined retry policy
        await retryPolicy.ExecuteAsync(
            async () =>
            {
                var payloadAsJson = JsonConvert.SerializeObject(payload);
                var contentData = new StringContent(payloadAsJson, Encoding.UTF8, "application/json");

                // RequestMessage can't be reused
                var requestMessage = new HttpRequestMessage(
                    HttpMethod.Post,
                    _headlessWhatsappMessageIntegrationConfig.Endpoint);
                requestMessage.Headers.Add("X-Sleekflow-Api-Key", agentConfigOrDefault.HeadlessApiKey);
                requestMessage.Content = contentData;

                return await _httpClient.SendAsync(requestMessage);
            });
    }
}