﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Triggers.CrmHubConfigs;
using Sleekflow.Outputs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests.IntegrationTests;

public class CrmHubConfigIntegrationTests
{
    private static readonly string MockCompanyId = $"aaaabbbb-cccc-dddd-eeee-{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[..12]}";

    [TearDown]
    public async Task TearDown()
    {
        var crmHubConfigRepository = GetCrmHubConfigRepository();

        var crmHubConfigs = await crmHubConfigRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId")
                .WithParameter("@sleekflowCompanyId", MockCompanyId));

        foreach (var crmHubConfig in crmHubConfigs)
        {
            await crmHubConfigRepository.DeleteAsync(
                crmHubConfig.Id,
                MockCompanyId);
        }
    }

    [Test]
    public async Task CrmHubConfigTest()
    {
        // /CrmHubConfigs/InitializeCrmHubConfig
        var initializeCrmHubConfigInput = new InitializeCrmHubConfig.InitializeCrmHubConfigInput(
            MockCompanyId,
            new UsageLimit(3, 10, 100, 200, 10),
            new FeatureAccessibilitySettings(true, true),
            "3880",
            null);

        var initializeCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(initializeCrmHubConfigInput).ToUrl("/CrmHubConfigs/InitializeCrmHubConfig");
            });

        var initializeCrmHubConfigOutput =
            await initializeCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<InitializeCrmHubConfig.InitializeCrmHubConfigOutput>>();

        Assert.That(initializeCrmHubConfigOutput, Is.Not.Null);
        Assert.That(initializeCrmHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            initializeCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumSchemaNum,
            Is.EqualTo(3));
        Assert.That(
            initializeCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumPropertyNumPerSchema,
            Is.EqualTo(10));
        Assert.That(
            initializeCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumSchemafulObjectNumPerSchema,
            Is.EqualTo(100));
        Assert.That(
            initializeCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumSchemafulObjectNumPerCompany,
            Is.EqualTo(200));
        Assert.That(
            initializeCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumArrayObjectArraySize,
            Is.EqualTo(10));
        Assert.That(
            initializeCrmHubConfigOutput.Data.CrmHubConfig.FeatureAccessibilitySettings.CanAccessCustomObject,
            Is.EqualTo(true));
        Assert.That(
            initializeCrmHubConfigOutput.Data.CrmHubConfig.FeatureAccessibilitySettings.CanAccessCustomObjectFlowBuilderComponents,
            Is.EqualTo(true));

        var crmHubConfigId = initializeCrmHubConfigOutput.Data.CrmHubConfig.Id;

        // duplicate initialization
        // /CrmHubConfigs/InitializeCrmHubConfig
        initializeCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(initializeCrmHubConfigInput).ToUrl("/CrmHubConfigs/InitializeCrmHubConfig");
            });

        initializeCrmHubConfigOutput =
            await initializeCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<InitializeCrmHubConfig.InitializeCrmHubConfigOutput>>();

        Assert.That(initializeCrmHubConfigOutput, Is.Not.Null);
        Assert.That(initializeCrmHubConfigOutput!.HttpStatusCode, Is.EqualTo(500));
        Assert.That(initializeCrmHubConfigOutput.ErrorCode, Is.EqualTo(1005));

        // /CrmHubConfigs/UpdateCrmHubConfig
        var updateCrmHubConfigInput = new UpdateCrmHubConfig.UpdateCrmHubConfigInput(
            crmHubConfigId,
            MockCompanyId,
            new UsageLimit(5, 55, 555, 5555, 55555),
            new FeatureAccessibilitySettings(true, false),
            "3880",
            null);

        var updateCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateCrmHubConfigInput).ToUrl("/CrmHubConfigs/UpdateCrmHubConfig");
            });

        var updateCrmHubConfigOutput =
            await updateCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<UpdateCrmHubConfig.UpdateCrmHubConfigOutput>>();

        Assert.That(updateCrmHubConfigOutput, Is.Not.Null);
        Assert.That(updateCrmHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateCrmHubConfigOutput.Data.CrmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemaNumOffset, Is.EqualTo(0));

        // /CrmHubConfigs/UpdateCrmHubConfigUsageLimitOffset
        var updateCrmHubConfigUsageLimitOffsetInput =
            new UpdateCrmHubConfigUsageLimitOffset.UpdateCrmHubConfigUsageLimitOffsetInput(
                crmHubConfigId,
                MockCompanyId,
                new UsageLimitOffset(1, 2, 3, 4, 5),
                null,
                null);

        var updateCrmHubConfigUsageLimitOffsetScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateCrmHubConfigUsageLimitOffsetInput).ToUrl("/CrmHubConfigs/UpdateCrmHubConfigUsageLimitOffset");
            });

        var updateCrmHubConfigUsageLimitOffsetOutput =
            await updateCrmHubConfigUsageLimitOffsetScenarioResult.ReadAsJsonAsync<
                Output<UpdateCrmHubConfigUsageLimitOffset.UpdateCrmHubConfigUsageLimitOffsetOutput>>();

        Assert.That(updateCrmHubConfigUsageLimitOffsetOutput, Is.Not.Null);
        Assert.That(updateCrmHubConfigUsageLimitOffsetOutput.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateCrmHubConfigUsageLimitOffsetOutput.Data.CrmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemaNumOffset, Is.EqualTo(1));

        // /CrmHubConfigs/GetCrmHubConfig
        var getCrmHubConfigInput = new GetCrmHubConfig.GetCrmHubConfigInput(MockCompanyId);

        var getCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCrmHubConfigInput).ToUrl("/CrmHubConfigs/GetCrmHubConfig");
            });

        var getCrmHubConfigOutput =
            await getCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<GetCrmHubConfig.GetCrmHubConfigOutput>>();

        Assert.That(getCrmHubConfigOutput, Is.Not.Null);
        Assert.That(getCrmHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));

        // usage limit
        Assert.That(
            getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumSchemaNum,
            Is.EqualTo(5));
        Assert.That(
            getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumPropertyNumPerSchema,
            Is.EqualTo(55));
        Assert.That(
            getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumSchemafulObjectNumPerSchema,
            Is.EqualTo(555));
        Assert.That(
            getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumSchemafulObjectNumPerCompany,
            Is.EqualTo(5555));
        Assert.That(
            getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimit.CustomObjectMaximumArrayObjectArraySize,
            Is.EqualTo(55555));

        // feature accessibility settings
        Assert.That(
            getCrmHubConfigOutput.Data.CrmHubConfig.FeatureAccessibilitySettings.CanAccessCustomObject,
            Is.EqualTo(true));
        Assert.That(
            getCrmHubConfigOutput.Data.CrmHubConfig.FeatureAccessibilitySettings.CanAccessCustomObjectFlowBuilderComponents,
            Is.EqualTo(false));

        // usage limit offset
        Assert.That(getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemaNumOffset,
            Is.EqualTo(1));
        Assert.That(getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimitOffset.CustomObjectMaximumPropertyNumPerSchemaOffset,
            Is.EqualTo(2));
        Assert.That(getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemafulObjectNumPerSchemaOffset,
            Is.EqualTo(3));
        Assert.That(getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimitOffset.CustomObjectMaximumSchemafulObjectNumPerCompanyOffset,
            Is.EqualTo(4));
        Assert.That(getCrmHubConfigOutput.Data.CrmHubConfig.UsageLimitOffset.CustomObjectMaximumArrayObjectArraySizeOffset,
            Is.EqualTo(5));
    }

    [Test]
    public void UsageLimit_WithOffsetApplied_WithNullUsageLimitOffset_ShouldReturnCorrectValue()
    {
        var crmHubConfig = new CrmHubConfig(
            "id",
            new UsageLimit(1, 2, 3, 4, 5),
            null,
            FeatureAccessibilitySettings.Default(),
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            "id",
            null,
            null);

        Assert.That(crmHubConfig.UsageLimitOffset, Is.Not.Null);

        var offsetAppliedUsageLimit = crmHubConfig.GetOffsetAppliedUsageLimit();

        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumSchemaNum, Is.EqualTo(1));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumPropertyNumPerSchema, Is.EqualTo(2));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumSchemafulObjectNumPerSchema, Is.EqualTo(3));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumSchemafulObjectNumPerCompany, Is.EqualTo(4));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumArrayObjectArraySize, Is.EqualTo(5));
    }

    [Test]
    public async Task UsageLimit_WithOffsetApplied_ShouldReturnCorrectValue()
    {
        // /CrmHubConfigs/InitializeCrmHubConfig
        var initializeCrmHubConfigInput = new InitializeCrmHubConfig.InitializeCrmHubConfigInput(
            MockCompanyId,
            new UsageLimit(3, 10, 100, 200, 10),
            new FeatureAccessibilitySettings(true, true),
            "3880",
            null);

        var initializeCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(initializeCrmHubConfigInput).ToUrl("/CrmHubConfigs/InitializeCrmHubConfig");
            });

        var initializeCrmHubConfigOutput =
            await initializeCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<InitializeCrmHubConfig.InitializeCrmHubConfigOutput>>();

        var crmHubConfigId = initializeCrmHubConfigOutput.Data.CrmHubConfig.Id;

        // /CrmHubConfigs/UpdateCrmHubConfigUsageLimitOffset
        var updateCrmHubConfigUsageLimitOffsetInput =
            new UpdateCrmHubConfigUsageLimitOffset.UpdateCrmHubConfigUsageLimitOffsetInput(
                crmHubConfigId,
                MockCompanyId,
                new UsageLimitOffset(0, 2, 3, 4, 5),
                null,
                null);

        var updateCrmHubConfigUsageLimitOffsetScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateCrmHubConfigUsageLimitOffsetInput).ToUrl("/CrmHubConfigs/UpdateCrmHubConfigUsageLimitOffset");
            });

        var updateCrmHubConfigUsageLimitOffsetOutput =
            await updateCrmHubConfigUsageLimitOffsetScenarioResult.ReadAsJsonAsync<
                Output<UpdateCrmHubConfigUsageLimitOffset.UpdateCrmHubConfigUsageLimitOffsetOutput>>();

        var crmHubConfig = updateCrmHubConfigUsageLimitOffsetOutput.Data.CrmHubConfig;

        var offsetAppliedUsageLimit = crmHubConfig.GetOffsetAppliedUsageLimit();

        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumSchemaNum, Is.EqualTo(3));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumPropertyNumPerSchema, Is.EqualTo(12));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumSchemafulObjectNumPerSchema, Is.EqualTo(103));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumSchemafulObjectNumPerCompany, Is.EqualTo(204));
        Assert.That(offsetAppliedUsageLimit.CustomObjectMaximumArrayObjectArraySize, Is.EqualTo(15));
    }

    private CrmHubConfigRepository GetCrmHubConfigRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var crmHubConfigRepository = new CrmHubConfigRepository(
            NullLogger<CrmHubConfigRepository>.Instance,
            serviceProvider);

        return crmHubConfigRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }
}