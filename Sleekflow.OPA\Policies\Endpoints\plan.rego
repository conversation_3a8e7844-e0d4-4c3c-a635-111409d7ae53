package endpoints.plan

plan_summary_view_endpoints := [
      {
          "path": "/app/feature-info",
          "method": "GET"
      },
      {
          "path": "/company/usage",
          "method": "GET"
      },
      {
          "path": "/company/whatsapp/cloudapi/top-up/invoices",
          "method": "GET"
      },
      {
          "path": "/CrmHub/CrmHubConfigs/GetCrmHubConfig",
          "method": "POST"
      },
      {
          "path": "https://pay.stripe.com/invoice",
          "method": "GET"
      },
      {
          "path": "/company/usage",
          "method": "GET"
      },
      {
          "path": "/company/apiKeys/Make",
          "method": "POST"
      },
      {
          "path": "/company/apiKeys/JourneyBuilder",
          "method": "POST"
      },
      {
          "path": "/company/apiKeys/Zapier",
          "method": "POST"
      },
      {
          "path": "/company/apiKeys/PublicApi",
          "method": "POST"
      },
      {
          "path": "/subscription/add-ons",
          "method": "GET"
      },
      {
          "path": "/app/feature-info",
          "method": "GET"
      },
      {
          "path": "/CrmHub/CrmHubConfigs/GetCrmHubConfig",
          "method": "POST"
      },
      {
          "path": "/stripe/setup",
          "method": "GET"
      }
]

plan_and_subscriptions_manage_endpoints := [
    {"path": "/app/feature-info", "method": "GET"},
    {"path": "/company/usage", "method": "GET"},
    {"path": "/subscription/add-ons", "method": "GET"},
    {"path": "/company", "method": "GET"},
    {"path": "/CrmHub/CrmHubConfigs/GetCrmHubConfig", "method": "POST"}
]

invoices_view_endpoints := [
    {"path": "/app/feature-info", "method": "GET"},
    {"path": "/company/usage", "method": "GET"},
    {"path": "/company/whatsapp/cloudapi/top-up/invoices", "method": "GET"},
    {"path": "/CrmHub/CrmHubConfigs/GetCrmHubConfig", "method": "POST"},
    {"path": "https://pay.stripe.com/invoice/", "method": "GET"}
]

plan_endpoints := {
    "plan_summary_view": plan_summary,
    "plan_and_subscriptions_manage": plan_and_subscriptions_manage_permission,
    "invoices_view": invoices_permission
}

plan_summary :={
  "view":plan_summary_view_endpoints
}

plan_and_subscriptions_manage_permission :={
  "manage":plan_and_subscriptions_manage_endpoints
}

invoices_permission  :={
  "view":invoices_view_endpoints
}