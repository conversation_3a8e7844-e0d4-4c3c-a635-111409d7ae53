﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.CrmHubConfigs;

[TriggerGroup(TriggerGroups.CrmHubConfigs)]
public class UpdateCrmHubConfigUsageLimitOffset
    : ITrigger<UpdateCrmHubConfigUsageLimitOffset.UpdateCrmHubConfigUsageLimitOffsetInput,
        UpdateCrmHubConfigUsageLimitOffset.UpdateCrmHubConfigUsageLimitOffsetOutput>
{
    private readonly ICrmHubConfigService _crmHubConfigService;

    public UpdateCrmHubConfigUsageLimitOffset(ICrmHubConfigService crmHubConfigService)
    {
        _crmHubConfigService = crmHubConfigService;
    }

    public class UpdateCrmHubConfigUsageLimitOffsetInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CrmHubConfig.PropertyNameUsageLimitOffset)]
        [Validations.ValidateObject]
        public UsageLimitOffset UsageLimitOffset { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateCrmHubConfigUsageLimitOffsetInput(
            string id,
            string sleekflowCompanyId,
            UsageLimitOffset usageLimitOffset,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            UsageLimitOffset = usageLimitOffset;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateCrmHubConfigUsageLimitOffsetOutput
    {
        [JsonProperty("crm_hub_config")]
        public CrmHubConfig CrmHubConfig { get; set; }

        [JsonConstructor]
        public UpdateCrmHubConfigUsageLimitOffsetOutput(CrmHubConfig crmHubConfig)
        {
            CrmHubConfig = crmHubConfig;
        }
    }

    public async Task<UpdateCrmHubConfigUsageLimitOffsetOutput> F(
        UpdateCrmHubConfigUsageLimitOffsetInput input)
    {
        var updatedBy = string.IsNullOrWhiteSpace(input.SleekflowStaffId)
            ? null
            : new AuditEntity.SleekflowStaff(
                input.SleekflowStaffId,
                input.SleekflowStaffTeamIds);

        var crmHubConfig = await _crmHubConfigService.UpdateCrmHubConfigUsageLimitOffsetAsync(
            input.Id,
            input.SleekflowCompanyId,
            input.UsageLimitOffset,
            updatedBy);

        return new UpdateCrmHubConfigUsageLimitOffsetOutput(crmHubConfig);
    }
}