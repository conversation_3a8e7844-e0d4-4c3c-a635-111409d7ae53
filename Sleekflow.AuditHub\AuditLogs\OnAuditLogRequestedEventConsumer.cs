using MassTransit;
using Newtonsoft.Json;
using Sleekflow.AuditHub.Models.AuditLogs;
using Sleekflow.AuditLogs;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.AuditLogs;

public class OnAuditLogRequestedEventConsumerDefinition : ConsumerDefinition<OnAuditLogRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnAuditLogRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 40;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 40;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnAuditLogRequestedEventConsumer : IConsumer<OnAuditLogRequestedEvent>
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly IIdService _idService;

    public OnAuditLogRequestedEventConsumer(
        IAuditLogRepository auditLogRepository,
        IIdService idService)
    {
        _auditLogRepository = auditLogRepository;
        _idService = idService;
    }

    public async Task Consume(ConsumeContext<OnAuditLogRequestedEvent> context)
    {
        var onAuditLogRequestedEvent = context.Message;
        var messageDataValue = await onAuditLogRequestedEvent.MessageData!.Value;
        var details = JsonConvert.DeserializeObject<Dictionary<string, object?>>(
            messageDataValue,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore, MissingMemberHandling = MissingMemberHandling.Ignore,
            })!;

        var auditLog = new AuditLog(
            _idService.GetId("AuditLog"),
            onAuditLogRequestedEvent.SleekflowCompanyId,
            onAuditLogRequestedEvent.TypeName,
            onAuditLogRequestedEvent.TypeDescription,
            details,
            onAuditLogRequestedEvent.DateTime);

        await _auditLogRepository.CreateAsync(auditLog, onAuditLogRequestedEvent.SleekflowCompanyId);
    }
}