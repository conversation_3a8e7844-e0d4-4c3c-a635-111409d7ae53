using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class GetPaymentResponse
{
    [Required]
    [JsonProperty("customer_id")]
    public string? CustomerId { get; set; }

    [Required]
    [JsonProperty("bill_record_id")]
    public string? BillRecordId { get; set; }

    [Required]
    [JsonProperty("subscription_fee")]
    public decimal? SubscriptionFee { get; set; }

    [Required]
    [JsonProperty("one_time_setup_fee")]
    public decimal? OneTimeSetupFee { get; set; }

    [Required]
    [JsonProperty("whatsapp_credit_amount")]
    public decimal? WhatsappCreditAmount { get; set; }

    [Required]
    [JsonProperty("currency")]
    public string? Currency { get; set; }

    [Required]
    [JsonProperty("payment_date")]
    public DateTime? PaidAt { get; set; }

    [Required]
    [JsonProperty("invoice_id")]
    public string? InvoiceId { get; set; }

    [Required]
    [JsonProperty("payment_terms")]
    public string? PaymentTerms { get; set; }

    [JsonConstructor]
    public GetPaymentResponse(
        string? customerId,
        string? billRecordId,
        decimal? subscriptionFee,
        decimal? oneTimeSetupFee,
        decimal? whatsappCreditAmount,
        string? currency,
        DateTime? paidAt,
        string? invoiceId,
        string? paymentTerms)
    {
        CustomerId = customerId;
        BillRecordId = billRecordId;
        SubscriptionFee = subscriptionFee;
        OneTimeSetupFee = oneTimeSetupFee;
        WhatsappCreditAmount = whatsappCreditAmount;
        Currency = currency;
        PaidAt = paidAt;
        InvoiceId = invoiceId;
        PaymentTerms = paymentTerms;
    }
}