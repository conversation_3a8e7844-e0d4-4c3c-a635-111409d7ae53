using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Blobs;

namespace Sleekflow.CommerceHub.Models.Images;

public class Image : Blob
{
    [JsonConstructor]
    public Image(
        string? containerName,
        string? blobName,
        string? blobId,
        string imageUrl)
        : base(containerName, blobName, blobId)
    {
        ContainerName = containerName;
        BlobName = blobName;
        BlobId = blobId;
        ImageUrl = imageUrl;
    }

    [SimpleField]
    [JsonProperty("image_url")]
    public string ImageUrl { get; set; }
}