﻿namespace Sleekflow.Exceptions.FlowHub;

public class SfWorkflowDeletedException : ErrorCodeException
{
    public string WorkflowId { get; }

    public SfWorkflowDeletedException(string workflowId)
        : base(
            ErrorCodeConstant.SfWorkflowDeletedException,
            $"The workflow {workflowId} has been deleted.",
            new Dictionary<string, object?>
            {
            })
    {
        WorkflowId = workflowId;
    }
}