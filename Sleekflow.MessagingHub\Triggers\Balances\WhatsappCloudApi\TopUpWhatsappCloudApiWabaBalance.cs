using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps.Stripe;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class TopUpWhatsappCloudApiWabaBalance :
    ITrigger<TopUpWhatsappCloudApiWabaBalance.TopUpWhatsappCloudApiWabaBalanceInput,
        TopUpWhatsappCloudApiWabaBalance.TopUpWhatsappCloudApiWabaBalanceOutput>
{
    private readonly IBus _bus;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;

    public TopUpWhatsappCloudApiWabaBalance(
        IBus bus,
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    {
        _bus = bus;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public class TopUpWhatsappCloudApiWabaBalanceInput : IHasMetadata
    {
        [Required]
        [JsonProperty("unique_id")]
        public string UniqueId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("top_up_method")]
        [Required]
        public string TopUpMethod { get; set; }

        [JsonProperty("credit")]
        [Required]
        [Validations.ValidateObject]
        public Money Credit { get; set; }

        [Required]
        [JsonProperty("credited_by")]
        public string CreditedBy { get; set; }

        [JsonProperty("credited_by_display_name")]
        public string? CreditedByDisplayName { get; set; }

        [JsonProperty("stripe_top_up_credit_detail")]
        [Validations.ValidateObject]
        public StripeTopUpCreditDetail? StripeTopUpCreditDetail { get; set; }

        [JsonProperty("metadata")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> Metadata { get; set; }

        [JsonConstructor]
        public TopUpWhatsappCloudApiWabaBalanceInput(
            string uniqueId,
            string facebookWabaId,
            string facebookBusinessId,
            string topUpMethod,
            Money credit,
            string creditedBy,
            Dictionary<string, object?> metadata)
        {
            UniqueId = uniqueId;
            FacebookWabaId = facebookWabaId;
            FacebookBusinessId = facebookBusinessId;
            TopUpMethod = topUpMethod;
            Credit = credit;
            CreditedBy = creditedBy;
            Metadata = metadata;
        }
    }

    public class TopUpWhatsappCloudApiWabaBalanceOutput
    {
        [JsonProperty("waba_balance_transaction_log")]
        public BusinessBalanceTransactionLog BusinessBalanceTransactionLog { get; set; }

        [JsonConstructor]
        public TopUpWhatsappCloudApiWabaBalanceOutput(
            BusinessBalanceTransactionLog businessBalanceTransactionLog)
        {
            BusinessBalanceTransactionLog = businessBalanceTransactionLog;
        }
    }

    public async Task<TopUpWhatsappCloudApiWabaBalanceOutput> F(TopUpWhatsappCloudApiWabaBalanceInput input)
    {
        var facebookBusinessId = input.FacebookBusinessId;

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);

        if (wabas.Count == 0)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        if (businessBalance.IsByWabaBillingEnabled != true)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        $"this FB business id {facebookBusinessId} business balance has not enabled WABA level credit management",
                        new[]
                        {
                            "FacebookBusinessId",
                            "FacebookWabaId"
                        })
                });
        }

        var businessBalanceTransactionLog =
            await _businessBalanceTransactionLogService
                .CreateAndGetWithBusinessWabaTopUpAsync(
                    facebookBusinessId,
                    input.FacebookWabaId,
                    businessBalance.MarkupProfile,

                    // For PowerFlow manual top up flow Travis_backend will provide the uniqueId else
                    // The unique Id should be using WabaTransactionItem.GetUniqueId
                    input.UniqueId,
                    input.TopUpMethod,
                    input.Credit,

                    // This is the staff Id from the Travis_Backend database AspNetUsers
                    input.CreditedBy,

                    // This is the display name of the staff from the Travis_Backend database UserProfile
                    input.CreditedByDisplayName,

                    // This provide a snapshot of the stripe checkout session
                    input.StripeTopUpCreditDetail,
                    input.Metadata);

        var onCloudApiBusinessBalancePendingTransactionLogCreatedEvent =
            new OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent(
                businessBalanceTransactionLog.FacebookBusinessId);
        await _bus.Publish(onCloudApiBusinessBalancePendingTransactionLogCreatedEvent);

        return new TopUpWhatsappCloudApiWabaBalanceOutput(businessBalanceTransactionLog);
    }
}