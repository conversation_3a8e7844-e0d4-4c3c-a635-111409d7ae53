using System.Diagnostics;
using Sleekflow.IntelligentHub.Evaluator.AddLabel.Methods;

namespace Sleekflow.IntelligentHub.Evaluator.AddLabel;

public class AddLabelFixture
{
    private readonly DefaultAddLabelMethod _method = new();

    public async Task<AddLabelTestResult[]> EvaluateAsync(
        AddLabelTestCase testCase,
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        AddLabelTestResult output;

        try
        {
            output = await _method.CompleteAsync(
                testCase.ChatMessageContents,
                testCase.Labels,
                testCase.Instructions);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            throw;
        }

        stopwatch.Stop();

        var result = new AddLabelTestResult(
            _method.MethodName,
            output.Answer,
            stopwatch.ElapsedMilliseconds);

        return new[] { result };
    }
}