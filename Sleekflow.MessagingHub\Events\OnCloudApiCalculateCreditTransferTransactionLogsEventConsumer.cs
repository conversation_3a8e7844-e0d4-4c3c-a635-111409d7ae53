﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiCalculateCreditTransferTransactionLogsEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiCalculateCreditTransferTransactionLogsEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiCalculateCreditTransferTransactionLogsEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiCalculateCreditTransferTransactionLogsEventConsumer
    : IConsumer<OnCloudApiCalculateCreditTransferTransactionLogsEvent>
{
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiCalculateCreditTransferTransactionLogsEventConsumer> _logger;
    private readonly ILockService _lockService;
    private readonly IBusinessBalanceTransactionLogRepository _businessBalanceTransactionLogRepository;


    public OnCloudApiCalculateCreditTransferTransactionLogsEventConsumer(
        IWabaLevelCreditManagementService wabaLevelCreditManagementService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IBusinessBalanceService businessBalanceService,
        ILogger<OnCloudApiCalculateCreditTransferTransactionLogsEventConsumer> logger,
        ILockService lockService,
        IBusinessBalanceTransactionLogRepository businessBalanceTransactionLogRepository)
    {
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _businessBalanceService = businessBalanceService;
        _logger = logger;
        _lockService = lockService;
        _businessBalanceTransactionLogRepository = businessBalanceTransactionLogRepository;
    }

    public async Task Consume(ConsumeContext<OnCloudApiCalculateCreditTransferTransactionLogsEvent> context)
    {
        var onCloudApiCalculateCreditTransferTransactionLogsEvent = context.Message;
        var cancellationToken = context.CancellationToken;
        var retryCount = context.GetRedeliveryCount();

        if (retryCount > 3)
        {
            _logger.LogError(
                "Over the max retry limited {OnCloudApiCalculateCreditTransferTransactionLogsEvent}",
                JsonConvert.SerializeObject(onCloudApiCalculateCreditTransferTransactionLogsEvent));

            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var facebookBusinessId =
            onCloudApiCalculateCreditTransferTransactionLogsEvent.BusinessBalance.FacebookBusinessId;

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookBusinessId
            },
            TimeSpan.FromSeconds(
                60 * (OnCloudApiCalculateCreditTransferTransactionLogsEventConsumerDefinition
                          .LockDuration +
                      OnCloudApiCalculateCreditTransferTransactionLogsEventConsumerDefinition
                          .MaxAutoRenewDuration)),
            cancellationToken);

        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));

            return;
        }

        var businessBalance = onCloudApiCalculateCreditTransferTransactionLogsEvent.BusinessBalance;

        var creditTransferTransactionLogs =
            onCloudApiCalculateCreditTransferTransactionLogsEvent.CreditTransferTransactionLogs;
        var creditTransfers = onCloudApiCalculateCreditTransferTransactionLogsEvent.CreditTransfers;
        var sleekflowCompanyId = onCloudApiCalculateCreditTransferTransactionLogsEvent.SleekflowCompanyId;
        var sleekflowStaffId = onCloudApiCalculateCreditTransferTransactionLogsEvent.SleekflowStaffId;
        var sleekflowStaffTeamIds = onCloudApiCalculateCreditTransferTransactionLogsEvent.SleekflowStaffTeamIds;

        try
        {
           var creditTransferTransactionLogIds = creditTransferTransactionLogs.Select(x => x.Id).ToList();

           creditTransferTransactionLogs =
               await _businessBalanceTransactionLogRepository.GetBusinessBalanceTransactionLogsByIdList(
                   creditTransferTransactionLogIds);

           var refetchedCreditTransferTransactionLogs =
               JsonConvert.DeserializeObject<List<BusinessBalanceTransactionLog>>(
                   JsonConvert.SerializeObject(creditTransferTransactionLogs));

           if (refetchedCreditTransferTransactionLogs == null || !refetchedCreditTransferTransactionLogs.Any())
           {
               return;
           }

           var facebookWabaIdListInBusinessBalance =
               businessBalance.WabaBalances?.Select(x => x.FacebookWabaId).ToList();

           if (facebookWabaIdListInBusinessBalance is null || !facebookWabaIdListInBusinessBalance.Any())
           {
               return;
           }

           // filter out credit transfer transaction logs that facebook waba id involved is not in business balance
           // filter out credit transfer transaction logs that is already calculated
           // filter out credit transfer transaction logs that is not credit transfer
           refetchedCreditTransferTransactionLogs = refetchedCreditTransferTransactionLogs
               .Where(
                   x =>
                       x is { IsCalculated: false, TransactionType: TransactionTypes.CreditTransfer, CreditTransferFromTo: not null, FacebookWabaId: not null } &&
                       facebookWabaIdListInBusinessBalance?.Contains(x.FacebookWabaId) == true)
               .ToList();

           await _wabaLevelCreditManagementService.CalculateCreditTransferTransactionLogsAsync(
                businessBalance,
                refetchedCreditTransferTransactionLogs,
                creditTransfers,
                sleekflowCompanyId,
                sleekflowStaffId,
                sleekflowStaffTeamIds,
                cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "{Exception} on calculating {OnCloudApiCalculateCreditTransferTransactionLogsEvent}",
                JsonConvert.SerializeObject(ex),
                JsonConvert.SerializeObject(onCloudApiCalculateCreditTransferTransactionLogsEvent));

            var unCalculatedFacebookBusinessCreditTransferTransactionLogs =
                await _businessBalanceTransactionLogService.GetUnCalculatedLogsWithFacebookBusinessIdAsync(
                    businessBalance.FacebookBusinessId,
                    new List<string>
                    {
                        TransactionTypes.CreditTransfer
                    });

            if (unCalculatedFacebookBusinessCreditTransferTransactionLogs.Any())
            {
                var unCalculatedCreditTransfers = unCalculatedFacebookBusinessCreditTransferTransactionLogs.Where(
                        x => x is { TransactionType: TransactionTypes.CreditTransfer, CreditTransferFromTo: not null })
                    .Select(x => x.CreditTransferFromTo).ToList();

                var currentBusinessBalance = await _businessBalanceService.GetWithIdAsync(businessBalance.Id);

                if (currentBusinessBalance != null && unCalculatedCreditTransfers.Any())
                {
                    context.Message.BusinessBalance = currentBusinessBalance;

                    context.Message.CreditTransferTransactionLogs =
                        unCalculatedFacebookBusinessCreditTransferTransactionLogs;
                    context.Message.CreditTransfers = unCalculatedCreditTransfers!;
                    await _lockService.ReleaseAsync(@lock, cancellationToken);
                    await context.Redeliver(TimeSpan.FromSeconds(8));
                }
            }
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}