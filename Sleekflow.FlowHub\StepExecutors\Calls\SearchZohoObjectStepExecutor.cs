﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISearchZohoObjectStepExecutor : IStepExecutor
{
}

public class SearchZohoObjectStepExecutor
    : GeneralStepExecutor<CallStep<SearchZohoObjectStepArgs>>,
        ISearchZohoObjectStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public SearchZohoObjectStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var searchZohoObjectInput = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                new SearchZohoObjectRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    searchZohoObjectInput.StateIdentity.SleekflowCompanyId,
                    searchZohoObjectInput.ConnectionId,
                    searchZohoObjectInput.EntityTypeName,
                    searchZohoObjectInput.Conditions));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnZohoFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("Zoho search object operation timed out after 5 minutes")),
                typeof(OnZohoFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class SearchZohoObjectInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("conditions")]
        [Required]
        [Validations.ValidateArray]
        public List<SearchObjectCondition> Conditions { get; set; }

        [JsonConstructor]
        public SearchZohoObjectInput(
            string stateId,
            StateIdentity stateIdentity,
            string connectionId,
            string entityTypeName,
            List<SearchObjectCondition> conditions)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            Conditions = conditions;
        }
    }

    private async Task<SearchZohoObjectInput> GetArgs(
        CallStep<SearchZohoObjectStepArgs> callStep,
        ProxyState state)
    {
        var connectionId = callStep.Args.ConnectionId;
        var entityTypeName = callStep.Args.EntityTypeName;

        var convertedConditions = new List<SearchObjectCondition>();
        var conditions = callStep.Args.Conditions;
        foreach (var condition in conditions)
        {
            object? value = null;
            if (condition.ValueExpr != null)
            {
                value = await _stateEvaluator.EvaluateExpressionAsync(state, condition.ValueExpr);
            }

            convertedConditions.Add(new SearchObjectCondition(condition.FieldName, condition.SearchOperator, value));
        }

        return new SearchZohoObjectInput(
            state.Id,
            state.Identity,
            connectionId,
            entityTypeName,
            convertedConditions);
    }
}