using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals.Methods;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.Models.Prompts;
using System.Diagnostics;
using Serilog.Context;

namespace Sleekflow.IntelligentHub.Evaluator;

public class ReActEvalFixture
{
    private readonly ReActMethod _method;

    public ReActEvalFixture()
    {
        _method = new ReActMethod();
    }

    public async Task<ReActEvalResult> RunTestAsync(ReActEvalQuestion tc, string testId)
    {
        // Set the current test ID for AsyncLocal tracking for both AsyncLocals
        ReActEvalTest.CurrentTestId.Value = testId;
        ChatEvalTest.CurrentTestId.Value = testId;

        using var property = LogContext.PushProperty("TestId", testId + "_" + _method.MethodName);

        var stopwatch = Stopwatch.StartNew();
        var response = string.Empty;
        var score = 0.0;

        try
        {
            // Execute the ReAct method
            var output = await _method.CompleteAsync(
                tc,
                tc.QuestionContexts,
                sourceFilenames: null,
                replyGenerationContext: tc.ReplyGenerationContext,
                agentConfig: tc.AgentConfig,
                tc.SfChatEntriesQuestionContexts);

            // Get the response from the output
            response = output.Answer;

            // Calculate a score based on tool calls
            score = CalculateScore(tc, testId);

            return new ReActEvalResult(response, stopwatch.ElapsedMilliseconds, score);
        }
        catch (Exception ex)
        {
            // Return an error result
            return new ReActEvalResult($"Error: {ex.Message}", stopwatch.ElapsedMilliseconds, 0.0);
        }
        finally
        {
            stopwatch.Stop();

            // Clear the test IDs to avoid cross-contamination
            ReActEvalTest.CurrentTestId.Value = null;
            ChatEvalTest.CurrentTestId.Value = null;
        }
    }

    private double CalculateScore(ReActEvalQuestion tc, string testId)
    {
        var score = 0.0;
        var maxScore = 0.0;

        // Check if this test should send a message
        if (tc.ShouldSendMessage)
        {
            // For message-sending tests, we want to check if SendMessageAsync was called
            maxScore += 5.0; // Base score for message handling

            var wasSendMessageAsyncCalled = MockSleekflowToolsPlugin.WasSendMessageAsyncCalled(testId);
            if (wasSendMessageAsyncCalled)
            {
                score += 4.0; // +4 for calling SendMessageAsync

                // Check if message contains required elements based on event type
                var messageParams = MockSleekflowToolsPlugin.GetSendMessageAsyncParams(testId);
                if (messageParams != null && !string.IsNullOrEmpty(tc.Input))
                {
                    // Check if this is a webhook test and determine the event type
                    var isWebhookTest = tc.Input.Contains("\"event_type\"");
                    var eventType = string.Empty;

                    if (isWebhookTest)
                    {
                        eventType = tc.Input.Contains("booking_confirmed") ? "confirmed" :
                                    tc.Input.Contains("booking_cancelled") ? "cancelled" :
                                    tc.Input.Contains("booking_rescheduled") ? "rescheduled" : string.Empty;
                    }

                    // Award a bonus point for a personalized message with context-specific content
                    if (!string.IsNullOrEmpty(messageParams.Message))
                    {
                        var message = messageParams.Message.ToLower();

                        // For webhook tests, check for event-specific content
                        if (isWebhookTest && !string.IsNullOrEmpty(eventType))
                        {
                            if ((eventType == "confirmed" && (message.Contains("thank") || message.Contains("confirm"))) ||
                                (eventType == "cancelled" && (message.Contains("cancel") || message.Contains("reschedule"))) ||
                                (eventType == "rescheduled" && (message.Contains("reschedule") || message.Contains("new time"))))
                            {
                                score += 1.0; // +1 for event-specific personalization
                            }
                        }
                        else
                        {
                            // For non-webhook message tests
                            score += 1.0; // +1 for sending any message
                        }
                    }
                }
            }
            else
            {
                score = 0.0; // No points if SendMessageAsync was not called for tests that should send messages
            }
        }
        // Check for Information Gathering
        else if (tc.ExpectExtractInformation)
        {
            maxScore += 2.0; // Base score for extracting information and updating properties

            // Check if information extraction was called
            var wasExtractFieldsCalled = MockInformationGatheringPlugin.WasExtractFieldsCalled(testId);
            if (wasExtractFieldsCalled)
            {
                score += 1.0; // +1 for calling ExtractFields

                // Check if contact properties were updated
                if (MockSleekflowToolsPlugin.WasUpdateContactPropertiesCalled(testId))
                {
                    score += 1.0; // +1 for updating contact properties

                    // Bonus for correctly extracting and updating properties
                    if (tc.ExpectedContactProperties != null && tc.ExpectedContactProperties.Count > 0)
                    {
                        maxScore += 3.0; // Up to 3 bonus points for accurate property extraction

                        var contactProperties = MockSleekflowToolsPlugin.GetUpdateContactPropertiesParams(testId);
                        if (contactProperties != null && contactProperties.Properties.Count > 0)
                        {
                            // Calculate how many expected properties were correctly updated
                            var correctProperties = 0;
                            foreach (var expectedProperty in tc.ExpectedContactProperties)
                            {
                                var propertyUpdated = contactProperties.Properties.Any(p =>
                                    p.PropertyId == expectedProperty.Key &&
                                    p.PropertyValue == expectedProperty.Value);

                                if (propertyUpdated)
                                {
                                    correctProperties++;
                                }
                            }

                            // Award proportional bonus points based on correctness
                            var proportionCorrect = (double) correctProperties / tc.ExpectedContactProperties.Count;
                            score += 3.0 * proportionCorrect;
                        }
                    }
                }
            }
        }
        else
        {
            // When extraction should not be performed and it's not a webhook test
            maxScore += 2.0;

            var wasExtractFieldsCalled = MockInformationGatheringPlugin.WasExtractFieldsCalled(testId);
            var wasUpdateContactPropertiesCalled = MockSleekflowToolsPlugin.WasUpdateContactPropertiesCalled(testId);

            if (!wasExtractFieldsCalled && !wasUpdateContactPropertiesCalled)
            {
                // +2 for correctly not extracting information when not needed
                score += 2.0;
            }
            else if (!wasExtractFieldsCalled)
            {
                // +1 for at least not calling ExtractFields
                score += 1.0;
            }
        }

        // Add time efficiency bonus (max 1 point)
        maxScore += 1.0;
        var timeBonus = MockInformationGatheringPlugin.GetProcessingTimeBonus(testId);
        score += timeBonus;

        // Return a normalized score (0-10 scale)
        var normalizedScore = maxScore > 0 ? (score / maxScore) * 10.0 : 0.0;

        // Ensure score is between 0 and 10
        return Math.Min(10.0, Math.Max(0.0, normalizedScore));
    }
}