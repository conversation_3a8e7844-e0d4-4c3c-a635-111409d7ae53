using Sleekflow.Persistence.Abstractions;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.States;

namespace Sleekflow.FlowHub.Triggers.States;

[TriggerGroup(ControllerNames.States)]
public class GetObjectStates : ITrigger
{
    private readonly IStateService _stateService;

    public GetObjectStates(
        IStateService stateService)
    {
        _stateService = stateService;
    }

    public class GetObjectStatesInput : Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("object_type")]
        [Required]
        public string ObjectType { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("limit")]
        [Required]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetObjectStatesInput(
            string sleekflowCompanyId,
            string objectId,
            string objectType,
            string? continuationToken,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            ObjectType = objectType;
            ContinuationToken = continuationToken;
            Limit = limit;
        }
    }

    public class GetObjectStatesOutput
    {
        [JsonProperty("states")]
        public List<ProxyStateDto> States { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetObjectStatesOutput(List<ProxyStateDto> states, string? nextContinuationToken)
        {
            States = states;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetObjectStatesOutput> F(GetObjectStatesInput getObjectStatesInput)
    {
        var (proxyStates, nextContinuationToken) = await _stateService.GetProxyStatesAsync(
            getObjectStatesInput.SleekflowCompanyId,
            getObjectStatesInput.ContinuationToken,
            getObjectStatesInput.Limit,
            getObjectStatesInput.ObjectId,
            getObjectStatesInput.ObjectType);

        return new GetObjectStatesOutput(
            proxyStates.Select(ps => new ProxyStateDto(ps)).ToList(),
            nextContinuationToken);
    }
}