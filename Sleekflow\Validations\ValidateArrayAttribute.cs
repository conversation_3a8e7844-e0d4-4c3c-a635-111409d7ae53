using System.Collections;
using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Validations;

public class ValidateArrayAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null)
        {
            return ValidationResult.Success;
        }

        var compositeResults =
            new CompositeValidationResult(
                $"Validations for {validationContext.MemberName} have failed.",
                new[]
                {
                    validationContext.MemberName!
                });

        if (value is IEnumerable enumerable)
        {
            foreach (var o in enumerable)
            {
                var results = new List<ValidationResult>();
                var context = new ValidationContext(o, null, null);

                Validator.TryValidateObject(o, context, results, true);

                foreach (var validationResult in results)
                {
                    compositeResults.AddResult(validationResult);
                }
            }
        }

        if (compositeResults.Results.Any())
        {
            return compositeResults;
        }

        return ValidationResult.Success;
    }
}