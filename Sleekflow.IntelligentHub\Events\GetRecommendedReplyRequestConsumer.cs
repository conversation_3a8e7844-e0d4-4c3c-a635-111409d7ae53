using System.Text;
using System.Text.RegularExpressions;
using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Events;

public class GetRecommendedReplyRequestConsumerDefinition : ConsumerDefinition<GetRecommendedReplyRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetRecommendedReplyRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetRecommendedReplyRequestConsumer : IConsumer<GetRecommendedReplyRequest>
{
    private readonly Kernel _kernel;
    private readonly ILogger _logger;
    private readonly IChatService _chatService;
    private readonly IReviewerService _reviewerService;
    private readonly ITextTranslationService _textTranslationService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public GetRecommendedReplyRequestConsumer(
        Kernel kernel,
        IChatService chatService,
        IReviewerService reviewerService,
        ITextTranslationService textTranslationService,
        ILogger<GetRecommendedReplyRequestConsumer> logger,
        IIntelligentHubUsageService intelligentHubUsageService,
        IIntelligentHubConfigService intelligentHubConfigService)
    {
        _logger = logger;
        _kernel = kernel;
        _chatService = chatService;
        _reviewerService = reviewerService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _textTranslationService = textTranslationService;
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public async Task Consume(ConsumeContext<GetRecommendedReplyRequest> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var conversationContext = message.ConversationContext;

        // Check if the usage limit is reached
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(message.SleekflowCompanyId);

        var isUsageLimitExceeded = intelligentHubConfig == null ||
                                   await _intelligentHubUsageService.IsUsageLimitExceeded(
                                       message.SleekflowCompanyId,
                                       new Dictionary<string, int>
                                       {
                                           {
                                               PriceableFeatures.AiAgentsTotalUsage, _intelligentHubUsageService
                                                   .GetFeatureTotalUsageLimit(
                                                       intelligentHubConfig,
                                                       PriceableFeatures.AiAgentsTotalUsage)
                                           }
                                       },
                                       new IntelligentHubUsageFilter(
                                           message.IntelligentHubUsageFilterFromDateTime,
                                           message.IntelligentHubUsageFilterToDateTime));

        if (isUsageLimitExceeded)
        {
            _logger.LogWarning(
                "Cannot find IntelligentHubConfig or exceed max usage limit for SleekflowCompanyId {SleekflowCompanyId}.",
                sleekflowCompanyId);

            await context.RespondAsync(
                new GetRecommendedReplyResponse("Cannot find IntelligentHubConfig or exceed max usage limit", 0));
            return;
        }

        var (asyncEnumerable, _, sourcesStr) = await _chatService.StreamAnswerAsync(
            conversationContext,
            sleekflowCompanyId);

        var answerSb = new StringBuilder();
        await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
        {
            if (partialAnswer is null)
            {
                continue;
            }

            answerSb.Append(partialAnswer);
        }

        var recommendedReply = answerSb.ToString();
        recommendedReply = Regex.Replace(recommendedReply, @"\[source\]", string.Empty, RegexOptions.IgnoreCase);

        await _intelligentHubUsageService.RecordUsageAsync(
            sleekflowCompanyId,
            PriceableFeatures.RecommendReply,
            null,
            new RecommendReplySnapshot(
                JsonConvert.SerializeObject(
                    conversationContext,
                    JsonConfig.DefaultLoggingJsonSerializerSettings),
                sourcesStr ?? string.Empty,
                recommendedReply));

        var confidenceScoring = await GetConfidenceScoring(conversationContext, recommendedReply);
        await context.RespondAsync(new GetRecommendedReplyResponse(recommendedReply, confidenceScoring));
    }

    private async Task<int> GetConfidenceScoring(List<SfChatEntry> conversationContext, string recommendedReply)
    {
        try
        {
            _logger.LogInformation(
                "Getting confidence scoring for recommended reply: {RecommendedReply}",
                recommendedReply);

            var confidenceScore = await _reviewerService.GetConfidenceScoringAsync(
                conversationContext,
                recommendedReply);

            return confidenceScore;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to get confidence scoring");
        }

        return 0;
    }
}