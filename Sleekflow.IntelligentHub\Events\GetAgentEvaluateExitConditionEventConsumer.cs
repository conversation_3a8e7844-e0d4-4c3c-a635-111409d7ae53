using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Consumers;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Events;
using Sleekflow.Models.Prompts;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentEvaluateExitConditionsEventConsumerDefinition
    : ConsumerDefinition<GetAgentEvaluateExitConditionsEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentEvaluateExitConditionsEventConsumer> consumerConfigurator,
        IRegistrationContext context
    )
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentEvaluateExitConditionsEventConsumer :
    FlowHubAgentGenericConsumer<GetAgentEvaluateExitConditionsEvent>,
    IConsumer<GetAgentEvaluateExitConditionsEvent>
{
    private readonly IReviewerService _reviewerService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GetAgentEvaluateExitConditionsEventConsumer(
        IBus bus,
        IReviewerService reviewerService,
        ILogger<GetAgentEvaluateExitConditionsEventConsumer> logger,
        ICompanyAgentConfigService companyAgentConfigService,
        IIntelligentHubUsageService intelligentHubUsageService)
        : base(logger, bus)
    {
        _reviewerService = reviewerService;
        _companyAgentConfigService = companyAgentConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    protected override async Task HandleMessageAsync(ConsumeContext<GetAgentEvaluateExitConditionsEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var conversationContext = message.ConversationContext;
        var score = message.Score;
        var confidenceScore = message.ConfidenceScore;
        var companyAgentConfigId = message.AgentConfigId;
        var companyAgentConfig = await _companyAgentConfigService.GetOrDefaultAsync(companyAgentConfigId, sleekflowCompanyId);
        var exitConditions = companyAgentConfig?.Actions?.ExitConversation?.Conditions;
        if (exitConditions == null || exitConditions.Count == 0)
        {
            _logger.LogInformation(
                "No exit conditions found for company agent config {CompanyAgentConfigId} in company {SleekflowCompanyId}",
                companyAgentConfigId,
                sleekflowCompanyId);
            throw new Exception(
                "No exit conditions found for the agent configuration. Please check the configuration."
            );
        }

        using var d1 = Serilog.Context.LogContext.PushProperty("SleekflowCompanyId", sleekflowCompanyId);
        using var d2 = Serilog.Context.LogContext.PushProperty("StateId", message.ProxyStateId);

        _logger.LogInformation(
            "Getting evaluate ExitCondition for {SleekflowCompanyId} {ConversationContext}",
            sleekflowCompanyId,
            JsonConvert.SerializeObject(conversationContext, JsonConfig.DefaultLoggingJsonSerializerSettings));

        ExitConditionResult? exitConditionResult = null;
        try
        {
            exitConditionResult = await _reviewerService.GetExitConditionResultAsync(
                conversationContext,
                score,
                confidenceScore,
                exitConditions);

            _logger.LogInformation("Evaluated exit condition: {ExitConditionResult}", JsonConvert.SerializeObject(exitConditionResult));

            await _intelligentHubUsageService.RecordUsageAsync(
                sleekflowCompanyId,
                PriceableFeatures.Scoring,
                null,
                new EvaluatedExitConditionSnapshot(
                    conversationContext,
                    exitConditionResult));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get evaluate exit condition");
        }

        var response = new GetAgentEvaluateExitConditionsEvent.Response(
            exitConditionResult.IsMatchExitCondition,
            exitConditionResult.ExitConditionTitle,
            exitConditionResult.Reason
        );

        _logger.LogInformation(
            "Agent evaluate exit condition published to OnAgentCompleteStepActivationEvent {Response} {ProxyStateId} {AggregateStepId} {StackEntries}",
            JsonConvert.SerializeObject(response),
            message.ProxyStateId,
            message.AggregateStepId,
            JsonConvert.SerializeObject(message.StackEntries)
        );

        await _bus.Publish(
            new OnAgentCompleteStepActivationEvent(
                message.AggregateStepId,
                message.ProxyStateId,
                message.StackEntries,
                JsonConvert.SerializeObject(response)
            )
        );
    }
}
