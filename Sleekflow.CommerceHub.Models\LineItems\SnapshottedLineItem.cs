using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.Products.Variants;

namespace Sleekflow.CommerceHub.Models.LineItems;

public class SnapshottedLineItem : LineItem
{
    [JsonProperty("product_variant_snapshot")]
    public ProductVariant ProductVariantSnapshot { get; set; }

    [JsonConstructor]
    public SnapshottedLineItem(
        string productVariantId,
        string productId,
        string? description,
        int quantity,
        Discount? lineItemDiscount,
        Dictionary<string, object?> metadata,
        ProductVariant productVariantSnapshot)
        : base(
            productVariantId,
            productId,
            description,
            quantity,
            lineItemDiscount,
            metadata)
    {
        ProductVariantSnapshot = productVariantSnapshot;
    }
}