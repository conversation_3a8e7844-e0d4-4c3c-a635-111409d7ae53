using MassTransit;
using Moq;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Tests;

[TestFixture]
public class ServiceBusManagerUnitTest
{
    private ServiceBusProvider _serviceBusProvider;
    private ServiceBusManager _serviceBusManager;
    private Mock<IHighTrafficServiceBus> _mockHighTrafficServiceBus;
    private Mock<IBus> _mockBus;

    [SetUp]
    public void ServiceBusManagerUnitTestSetup()
    {
        _mockBus = new Mock<IBus>();
        _mockHighTrafficServiceBus = new Mock<IHighTrafficServiceBus>();
        _serviceBusProvider = new ServiceBusProvider(_mockBus.Object, _mockHighTrafficServiceBus.Object);
        _serviceBusManager = new ServiceBusManager(_serviceBusProvider);
    }

    [Test]
    public async Task PublishAsync_ShouldCallPublishOnDefaultServiceBus()
    {
        // Arrange
        var testEvent = new OnWorkflowExecutionEndedV2Event(
            "test_sleekflow_company_id",
            "test_state_id",
            "test_workflow_execution_status",
            null,
            new StateIdentity(
                "test_state_id",
                "test_sleekflow_company_id",
                "test_workflow_id",
                "test_workflow_version_id",
                "test_object_type"),
            null,
            null,
            "normal");

        // Act
        await _serviceBusManager.PublishAsync(testEvent);

        // Assert
        _mockBus.Verify(b => b.Publish(testEvent, It.IsAny<CancellationToken>()), Times.Once);
        _mockHighTrafficServiceBus.Verify(b => b.Publish(testEvent, It.IsAny<CancellationToken>()), Times.Never);
    }

    [Test]
    public async Task PublishAsync_ShouldCallPublishOnHighTrafficServiceBus()
    {
        // Arrange
        var highTrafficTestEvent = new OnStepRequestedEvent(
            "test_state_id",
            "test_step_id",
            new Stack<StackEntry>(),
            null);

        // Act
        await _serviceBusManager.PublishAsync(highTrafficTestEvent);

        // Assert
        _mockHighTrafficServiceBus.Verify(
            b => b.Publish(highTrafficTestEvent, It.IsAny<CancellationToken>()),
            Times.Once);
        _mockBus.Verify(b => b.Publish(highTrafficTestEvent, It.IsAny<CancellationToken>()), Times.Never);
    }
}