﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Subscriptions;
using Sleekflow.Integrator.Zoho.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeleteConnection : ITrigger
{
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoSubscriptionService _zohoSubscriptionService;
    private readonly IZohoUserMappingConfigService _zohoUserMappingConfigService;

    public DeleteConnection(
        IZohoConnectionService zohoConnectionService,
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoSubscriptionService zohoSubscriptionService,
        IZohoUserMappingConfigService zohoUserMappingConfigService)
    {
        _zohoConnectionService = zohoConnectionService;
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoSubscriptionService = zohoSubscriptionService;
        _zohoUserMappingConfigService = zohoUserMappingConfigService;
    }

    public class DeleteConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public DeleteConnectionInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class DeleteConnectionOutput
    {
    }

    public async Task<DeleteConnectionOutput> F(DeleteConnectionInput deleteConnectionInput)
    {
        var connection = await _zohoConnectionService.GetByIdAsync(
            deleteConnectionInput.ConnectionId,
            deleteConnectionInput.SleekflowCompanyId);

        if (connection is null)
        {
            throw new SfNotFoundObjectException(
                deleteConnectionInput.ConnectionId,
                deleteConnectionInput.SleekflowCompanyId);
        }

        await _zohoSubscriptionService.ClearByConnectionIdAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        await _zohoUserMappingConfigService.ClearAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        await _zohoAuthenticationService.DeleteAsync(
            connection.AuthenticationId,
            connection.SleekflowCompanyId);

        await _zohoConnectionService.DeleteAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        return new DeleteConnectionOutput();
    }
}