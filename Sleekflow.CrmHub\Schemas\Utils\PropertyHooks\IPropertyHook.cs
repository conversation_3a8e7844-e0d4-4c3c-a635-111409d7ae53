﻿using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas.Dtos;

namespace Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;

public interface IPropertyHook
{
    (IDataType DataType, List<Option>? Options) PreConstruct(PropertyInput propertyInput);

    (IDataType DataType, List<Option>? Options) PreConstruct(Property property);

    UpdatePropertyChangeContext PreUpdate(
        Property originalProperty, Property receivedProperty);
}