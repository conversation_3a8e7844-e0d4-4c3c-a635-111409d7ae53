using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class CreateTriggers : ITrigger<
    CreateTriggers.CreateTriggersInput,
    CreateTriggers.CreateTriggersOutput>
{
    private readonly INeedConfigService _needConfigService;

    public CreateTriggers(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class CreateTriggersInput
    {
        [Required]
        [JsonProperty("triggers")]
        public List<TriggerConfig> Triggers { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonConstructor]
        public CreateTriggersInput(
            List<TriggerConfig> triggers,
            string? version)
        {
            Triggers = triggers;
            Version = version;
        }
    }

    public class CreateTriggersOutput
    {
        [JsonProperty("triggers")]
        public List<TriggerConfig> Triggers { get; set; }

        [JsonConstructor]
        public CreateTriggersOutput(List<TriggerConfig> triggers)
        {
            Triggers = triggers;
        }
    }

    public async Task<CreateTriggersOutput> F(CreateTriggersInput input)
    {
        return new CreateTriggersOutput(
            await _needConfigService.CreateTriggersAsync(
                input.Version,
                input.Triggers));
    }
}