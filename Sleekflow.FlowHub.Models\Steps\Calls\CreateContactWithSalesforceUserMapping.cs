using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateContactWithSalesforceUserMappingStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-contact-with-salesforce-user-mapping";

    [Required]
    [JsonProperty("contact_phone_number__expr")]
    public string ContactPhoneNumberExpr { get; set; }

    [Required]
    [JsonProperty("salesforce_user_id_for_mapping__expr")]
    public string SalesforceUserIdForMappingExpr { get; set; }

    [Required]
    [JsonProperty("salesforce_connection_id__expr")]
    public string SalesforceConnectionIdExpr { get; set; }

    [JsonProperty("contact_properties__expr_dict")]
    public Dictionary<string, string?>? ContactPropertiesExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public CreateContactWithSalesforceUserMappingStepArgs(
        string contactPhoneNumberExpr,
        string salesforceUserIdForMappingExpr,
        string salesforceConnectionIdExpr,
        Dictionary<string, string?>? contactPropertiesExprDict)
    {
        ContactPhoneNumberExpr = contactPhoneNumberExpr;
        SalesforceUserIdForMappingExpr = salesforceUserIdForMappingExpr;
        SalesforceConnectionIdExpr = salesforceConnectionIdExpr;
        ContactPropertiesExprDict = contactPropertiesExprDict;
    }
}