using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;


public class GetTriggerConditionInput
{
    [JsonProperty("trigger_id")]
    [Required]
    public string TriggerId { get; set; }

    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public GetTriggerConditionInput(
        string sleekflowCompanyId,
        string workflowVersionedId,
        string triggerId)
    {
        TriggerId = triggerId;
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionedId = workflowVersionedId;
    }
}