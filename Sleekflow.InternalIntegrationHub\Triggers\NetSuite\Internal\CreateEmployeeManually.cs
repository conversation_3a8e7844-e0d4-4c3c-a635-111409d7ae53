using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;
using Sleekflow.InternalIntegrationHub.NetSuite;
using Sleekflow.Validations;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class CreateEmployeeManually
    : ITrigger<CreateEmployeeManually.CreateEmployeeManuallyInput,
        CreateEmployeeManually.CreateEmployeeManuallyOutput>
{
    private readonly INetSuiteEmployeeService _netSuiteEmployeeService;

    public CreateEmployeeManually(
        INetSuiteEmployeeService netSuiteEmployeeService)
    {
        _netSuiteEmployeeService = netSuiteEmployeeService;
    }

    public class CreateEmployeeManuallyInput
    {
        [Required]
        [ValidateArray]
        [JsonProperty("employees")]
        public List<CreateEmployeeRequest> Employees { get; set; }

        [JsonConstructor]
        public CreateEmployeeManuallyInput(
            List<CreateEmployeeRequest> employees)
        {
            Employees = employees;
        }
    }

    public class CreateEmployeeManuallyOutput
    {
        [JsonProperty("success_count")]
        public int SuccessCount { get; set; }

        [JsonProperty("failed_employee_ids")]
        public List<string> FailedEmployeeIds { get; set; }

        [JsonConstructor]
        public CreateEmployeeManuallyOutput(
            int successCount,
            List<string> failedEmployeeIds)
        {
            SuccessCount = successCount;
            FailedEmployeeIds = failedEmployeeIds;
        }
    }

    public async Task<CreateEmployeeManuallyOutput> F(CreateEmployeeManuallyInput input)
    {
        var successCount = 0;
        var failedEmployeeIds = new List<string>();
        foreach (var request in input.Employees)
        {
            var isSuccess = await _netSuiteEmployeeService.CreateEmployeeAsync(request);
            if (isSuccess)
            {
                successCount++;
            }
            else
            {
                failedEmployeeIds.Add(request.ExternalId ?? "null");
            }
        }

        return new CreateEmployeeManuallyOutput(successCount, failedEmployeeIds);
    }
}