using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;
using Sleekflow.Validations;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class CreatePaymentFromStripe
    : ITrigger<CreatePaymentFromStripe.CreatePaymentFromStripeInput,
        CreatePaymentFromStripe.CreatePaymentFromStripeOutput>
{
    private readonly INetSuiteCustomerService _netSuiteCustomerService;

    public CreatePaymentFromStripe(INetSuiteCustomerService netSuiteCustomerService)
    {
        _netSuiteCustomerService = netSuiteCustomerService;
    }

    public class CreatePaymentFromStripeInput
    {
        [Required]
        [JsonProperty("external_id")]
        public string ExternalId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("items")]
        public List<BillItem> Items { get; set; }

        [Required]
        [JsonProperty("currency")]
        public string Currency { get; set; }

        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [JsonConstructor]
        public CreatePaymentFromStripeInput(
            string externalId,
            List<BillItem> items,
            string currency,
            string companyId)
        {
            ExternalId = externalId;
            Items = items;
            Currency = currency;
            CompanyId = companyId;
        }
    }

    public class BillItem
    {
        [Required]
        [JsonProperty("bill_record_id")]
        public string BillRecordId { get; set; }

        [Required]
        [JsonProperty("amount")]
        [Range(0, double.MaxValue)]
        public decimal Amount { get; set; }

        [JsonConstructor]
        public BillItem(
            string billRecordId,
            decimal amount)
        {
            BillRecordId = billRecordId;
            Amount = amount;
        }
    }

    public class CreatePaymentFromStripeOutput
    {
    }

    public async Task<CreatePaymentFromStripeOutput> F(CreatePaymentFromStripeInput input)
    {
        var response = await _netSuiteCustomerService.CreatePaymentFromStripeAsync(input);
        if (response == false)
        {
            throw new Exception("Failed to create payment");
        }

        return new CreatePaymentFromStripeOutput();
    }
}