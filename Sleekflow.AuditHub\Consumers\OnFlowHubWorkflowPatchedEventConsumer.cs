using MassTransit;
using Newtonsoft.Json;
using Sleekflow.AuditHub.Models.SystemAuditLogs;
using Sleekflow.AuditHub.SystemAuditLogs;
using Sleekflow.Ids;
using Sleekflow.Models.Events;

namespace Sleekflow.AuditHub.Consumers;

public class OnFlowHubWorkflowPatchedEventConsumerDefinition : ConsumerDefinition<OnFlowHubWorkflowPatchedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnFlowHubWorkflowPatchedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(1);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnFlowHubWorkflowPatchedEventConsumer : IConsumer<OnFlowHubWorkflowPatchedEvent>
{
    private readonly IIdService _idService;
    private readonly ISystemAuditLogService _systemAuditLogService;

    public OnFlowHubWorkflowPatchedEventConsumer(
        IIdService idService,
        ISystemAuditLogService systemAuditLogService)
    {
        _idService = idService;
        _systemAuditLogService = systemAuditLogService;
    }

    public async Task Consume(ConsumeContext<OnFlowHubWorkflowPatchedEvent> context)
    {
        var @event = context.Message;

        var id = _idService.GetId("SystemAuditLog");

        await _systemAuditLogService.CreateSystemAuditLogAsync(
            new SystemAuditLog(
                id,
                @event.SleekflowCompanyId,
                @event.SleekflowStaffId,
                null,
                SystemAuditLogTypes.FlowBuilderWorkflowPatched,
                JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                    JsonConvert.SerializeObject(
                        @event.OriginalWorkflowSnapshot)),
                DateTimeOffset.UtcNow,
                null));
    }
}