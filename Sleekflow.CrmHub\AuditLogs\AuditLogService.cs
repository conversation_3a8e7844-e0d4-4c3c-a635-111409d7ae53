using MassTransit;
using Sleekflow.AuditLogs;
using Sleekflow.Constants;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Events;

namespace Sleekflow.CrmHub.AuditLogs;

public interface IAuditLogService
{
    Task CreateAuditLogAsync(
        string sleekflowCompanyId,
        AuditLogType auditLogType,
        Dictionary<string, object?> dict,
        string entityTypeName,
        string objectId,
        string providerName,
        string objectOperation);
}

public class AuditLogService : IScopedService, IAuditLogService
{
    private readonly IMessageDataService _messageDataService;
    private readonly IEventHubProducerProvider _eventHubProducerProvider;

    public AuditLogService(
        IMessageDataService messageDataService,
        IEventHubProducerProvider eventHubProducerProvider)
    {
        _messageDataService = messageDataService;
        _eventHubProducerProvider = eventHubProducerProvider;
    }

    public async Task CreateAuditLogAsync(
        string sleekflowCompanyId,
        AuditLogType auditLogType,
        Dictionary<string, object?> dict,
        string entityTypeName,
        string objectId,
        string providerName,
        string objectOperation)
    {
        var producer = await _eventHubProducerProvider.GetProducer(
            EventHubNames.SleekflowOnAuditLogRequestedEventHubEvent);
        var onAuditLogRequestedEventHubEvent = new OnAuditLogRequestedEventHubEvent(
            sleekflowCompanyId,
            auditLogType.Name,
            auditLogType.Description,
            await _messageDataService.PutString(
                new Dictionary<string, object?>
                {
                    {
                        "dict", dict
                    },
                    {
                        "entityTypeName", entityTypeName
                    },
                    {
                        "objectId", objectId
                    },
                    {
                        "providerName", providerName
                    },
                    {
                        "operation", objectOperation
                    }
                }),
            DateTimeOffset.UtcNow);

        await producer.Produce(onAuditLogRequestedEventHubEvent);
    }
}