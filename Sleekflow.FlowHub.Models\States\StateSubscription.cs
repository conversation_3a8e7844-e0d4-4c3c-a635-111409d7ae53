using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.States;

[ContainerId(ContainerNames.StateSubscription)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class StateSubscription : Entity, IHasETag
{
    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonProperty("event_name")]
    public string EventName { get; set; }

    [JsonProperty("condition")]
    public string Condition { get; set; }

    [JsonProperty("step_id")]
    public string StepId { get; set; }

    [JsonProperty("stack_entries")]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("is_executed")]
    public bool IsExecuted { get; set; }

    [JsonProperty("expired_at")]
    public DateTimeOffset? ExpiredAt { get; set; }

    [JsonProperty("is_timeout")]
    public bool IsTimeout { get; set; }

    [JsonProperty("worker_instance_id")]
    public string? WorkerInstanceId { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public StateSubscription(
        string id,
        string stateId,
        string eventName,
        string condition,
        string stepId,
        Stack<StackEntry> stackEntries,
        bool isExecuted,
        DateTimeOffset? expiredAt,
        string? eTag,
        bool isTimeout,
        string? workerInstanceId)
        : base(id, SysTypeNames.StateSubscription)
    {
        WorkerInstanceId = workerInstanceId;
        StateId = stateId;
        EventName = eventName;
        Condition = condition;
        StepId = stepId;
        StackEntries = stackEntries;
        IsExecuted = isExecuted;
        ExpiredAt = expiredAt;
        ETag = eTag;
        IsTimeout = isTimeout;
    }
}