﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

[ContainerId(ContainerNames.IntelligentHubConfig)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IIntelligentHubDbResolver))]
public class IntelligentHubConfig
    : Entity, IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt, IHasETag, IHasUpdatedBy
{
    public const string PropertyNameUsageLimits = "usage_limits";
    public const string PropertyNameUsageLimitOffsets = "usage_limit_offsets";
    public const string PropertyNameEnableWritingAssistant = "enable_writing_assistant";
    public const string PropertyNameEnableSmartReply = "enable_smart_reply";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameUsageLimits)]
    public Dictionary<string, int> UsageLimits { get; set; }

    [JsonProperty(PropertyNameUsageLimitOffsets)]
    public Dictionary<string, int>? UsageLimitOffsets { get; set; }

    [JsonProperty(PropertyNameEnableWritingAssistant)]
    public bool EnableWritingAssistant { get; set; }

    [JsonProperty(PropertyNameEnableSmartReply)]
    public bool EnableSmartReply { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasUpdatedBy.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonConstructor]
    public IntelligentHubConfig(
        string id,
        string sleekflowCompanyId,
        Dictionary<string, int> usageLimits,
        Dictionary<string, int>? usageLimitOffsets,
        string? eTag,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        AuditEntity.SleekflowStaff? updatedBy = null,
        bool enableWritingAssistant = true,
        bool enableSmartReply = true)
        : base(id, SysTypeNames.IntelligentHubConfig)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        UsageLimits = usageLimits;
        UsageLimitOffsets = usageLimitOffsets;
        EnableWritingAssistant = enableWritingAssistant;
        EnableSmartReply = enableSmartReply;
        ETag = eTag;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        UpdatedBy = updatedBy;
    }
}