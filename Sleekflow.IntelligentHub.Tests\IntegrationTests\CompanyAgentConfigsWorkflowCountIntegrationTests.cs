using MassTransit;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.IntelligentHub.Triggers.Authorized.CompanyAgentConfigs;
using Sleekflow.Models.Constants;
using Sleekflow.Models.FlowHub;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Outputs;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class CompanyAgentConfigsWorkflowCountIntegrationTests
{
  private Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>> _mockRequestClient;

  [OneTimeSetUp]
  public void OneTimeSetUp()
  {
    // Set required environment variables for FlowHub integration tests
    Environment.SetEnvironmentVariable("FLOW_HUB_INTERNALS_ENDPOINT", "https://test-flowhub-integration.com");
    Environment.SetEnvironmentVariable(
      "CORE_INTERNALS_KEY",
      "test-internals-key-that-is-long-enough-for-hmac-sha256-integration");
  }

  [SetUp]
  public void SetUp()
  {
    _mockRequestClient = new Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>>();
  }

  [Test]
  public async Task GetCompanyAgentConfigs_WithWorkflowCounts_ReturnsCorrectActiveWorkflowCount()
  {
    var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
    using var scope = Application.Host.Services.CreateScope();

    // Override the MassTransit request client with our mock
    var serviceProvider = scope.ServiceProvider;
    var workflowCountService = new WorkflowCountService(
      _mockRequestClient.Object,
      serviceProvider.GetRequiredService<ILogger<WorkflowCountService>>());

    Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

    // Create test agent configs
    var agentConfig1Id = "agent-config-1";
    var agentConfig2Id = "agent-config-2";

    // Create agent configs first
    var createCompanyAgentConfigInput1 = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
      "TestAgentConfig1",
      CompanyAgentTypes.Sales,
      true,
      true,
      10,
      null,
      null,
      AgentCollaborationModes.Default,
      null, // leadNurturingTools
      null, // toolsConfig
      null, // enricherConfigs
      null, // knowledgeRetrievalConfig
      null, // description
      null, // actions
      null); // promptInstruction

    var createCompanyAgentConfigInput2 = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
      "TestAgentConfig2",
      CompanyAgentTypes.Support,
      true,
      true,
      15,
      null,
      null,
      AgentCollaborationModes.Default,
      null, // leadNurturingTools
      null, // toolsConfig
      null, // enricherConfigs
      null, // knowledgeRetrievalConfig
      null, // description
      null, // actions
      null); // promptInstruction

    // Create first agent config
    var createResult1 = await Application.Host.Scenario(_ =>
    {
      _.WithRequestHeader("X-Sleekflow-Record", "true");
      _.Post.Json(createCompanyAgentConfigInput1)
        .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
    });

    var createOutput1 =
      await createResult1.ReadAsJsonAsync<Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();
    Assert.That(createOutput1?.Data?.CompanyAgentConfig?.Id, Is.Not.Null);
    agentConfig1Id = createOutput1!.Data!.CompanyAgentConfig!.Id;

    // Create second agent config
    var createResult2 = await Application.Host.Scenario(_ =>
    {
      _.WithRequestHeader("X-Sleekflow-Record", "true");
      _.Post.Json(createCompanyAgentConfigInput2)
        .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
    });

    var createOutput2 =
      await createResult2.ReadAsJsonAsync<Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();
    Assert.That(createOutput2?.Data?.CompanyAgentConfig?.Id, Is.Not.Null);
    agentConfig2Id = createOutput2!.Data!.CompanyAgentConfig!.Id;

    // Mock MassTransit response for workflow counts
    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      { agentConfig1Id, 3 },
      { agentConfig2Id, 1 }
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == mockCompanyId &&
          req.AgentConfigIds.Contains(agentConfig1Id) &&
          req.AgentConfigIds.Contains(agentConfig2Id)),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(mockResponse.Object);

    // Test the GetCompanyAgentConfigs endpoint with our custom scope
    using var testScope = Application.Host.Services.CreateScope();
    var testServiceProvider = testScope.ServiceProvider;

    // Manually create the trigger with our mocked workflow count service
    var authContext = testServiceProvider.GetRequiredService<ISleekflowAuthorizationContext>();
    var agentConfigService = testServiceProvider.GetRequiredService<ICompanyAgentConfigService>();
    var characterCountService = new Mock<IIntelligentHubCharacterCountService>();

    var trigger = new GetCompanyAgentConfigs(authContext, agentConfigService, workflowCountService, characterCountService.Object);

    // Set the auth context
    authContext.SleekflowCompanyId = mockCompanyId;

    // Execute the trigger directly
    var input = new GetCompanyAgentConfigs.GetCompanyAgentConfigsInput();
    var result = await trigger.F(input);

    // Assert the results
    Assert.That(result, Is.Not.Null);
    Assert.That(result.CompanyAgentConfigs, Is.Not.Null);
    Assert.That(result.CompanyAgentConfigs.Count, Is.EqualTo(2));

    // Find the configs and verify their workflow counts
    var config1 = result.CompanyAgentConfigs.FirstOrDefault(c => c.Id == agentConfig1Id);
    var config2 = result.CompanyAgentConfigs.FirstOrDefault(c => c.Id == agentConfig2Id);

    Assert.That(config1, Is.Not.Null);
    Assert.That(config2, Is.Not.Null);

    Assert.That(config1!.ActiveWorkflowCount, Is.EqualTo(3));
    Assert.That(config2!.ActiveWorkflowCount, Is.EqualTo(1));

    // Verify that the request client was called correctly
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == mockCompanyId &&
          req.AgentConfigIds.Count == 2 &&
          req.AgentConfigIds.Contains(agentConfig1Id) &&
          req.AgentConfigIds.Contains(agentConfig2Id)),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Once);
  }

  [Test]
  public async Task GetCompanyAgentConfigs_WithMassTransitError_ReturnsZeroWorkflowCounts()
  {
    var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
    using var scope = Application.Host.Services.CreateScope();

    // Override the MassTransit request client with our mock
    var serviceProvider = scope.ServiceProvider;
    var workflowCountService = new WorkflowCountService(
      _mockRequestClient.Object,
      serviceProvider.GetRequiredService<ILogger<WorkflowCountService>>());

    Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

    // Create a test agent config
    var createCompanyAgentConfigInput = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
      "TestAgentConfigWithError",
      CompanyAgentTypes.Sales,
      true,
      true,
      10,
      null,
      null,
      AgentCollaborationModes.Default,
      null, // leadNurturingTools
      null, // toolsConfig
      null, // enricherConfigs
      null, // knowledgeRetrievalConfig
      null, // description
      null, // actions
      null); // promptInstruction

    var createResult = await Application.Host.Scenario(_ =>
    {
      _.WithRequestHeader("X-Sleekflow-Record", "true");
      _.Post.Json(createCompanyAgentConfigInput)
        .ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
    });

    var createOutput =
      await createResult.ReadAsJsonAsync<Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();
    Assert.That(createOutput?.Data?.CompanyAgentConfig?.Id, Is.Not.Null);
    var agentConfigId = createOutput!.Data!.CompanyAgentConfig!.Id;

    // Mock MassTransit to throw an exception
    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ThrowsAsync(new RequestTimeoutException("MassTransit timeout"));

    // Test the GetCompanyAgentConfigs endpoint
    using var testScope = Application.Host.Services.CreateScope();
    var testServiceProvider = testScope.ServiceProvider;

    var authContext = testServiceProvider.GetRequiredService<ISleekflowAuthorizationContext>();
    var agentConfigService = testServiceProvider.GetRequiredService<ICompanyAgentConfigService>();
    var characterCountService = new Mock<IIntelligentHubCharacterCountService>();

    var trigger = new GetCompanyAgentConfigs(authContext, agentConfigService, workflowCountService, characterCountService.Object);
    authContext.SleekflowCompanyId = mockCompanyId;

    var input = new GetCompanyAgentConfigs.GetCompanyAgentConfigsInput();
    var result = await trigger.F(input);

    // Assert that the endpoint still works but returns 0 for workflow count
    Assert.That(result, Is.Not.Null);
    Assert.That(result.CompanyAgentConfigs, Is.Not.Null);
    Assert.That(result.CompanyAgentConfigs.Count, Is.EqualTo(1));

    var config = result.CompanyAgentConfigs.First();
    Assert.That(config.Id, Is.EqualTo(agentConfigId));
    Assert.That(config.ActiveWorkflowCount, Is.EqualTo(0)); // Should be 0 due to error

    // Verify that the request client was called despite the error
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Once);
  }

  [Test]
  public async Task GetCompanyAgentConfigs_WithNoAgentConfigs_ReturnsEmptyListWithoutCallingMassTransit()
  {
    var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
    using var scope = Application.Host.Services.CreateScope();

    var serviceProvider = scope.ServiceProvider;
    var workflowCountService = new WorkflowCountService(
      _mockRequestClient.Object,
      serviceProvider.GetRequiredService<ILogger<WorkflowCountService>>());

    Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

    // Don't create any agent configs

    // Test the GetCompanyAgentConfigs endpoint
    using var testScope = Application.Host.Services.CreateScope();
    var testServiceProvider = testScope.ServiceProvider;

    var authContext = testServiceProvider.GetRequiredService<ISleekflowAuthorizationContext>();
    var agentConfigService = testServiceProvider.GetRequiredService<ICompanyAgentConfigService>();
    var characterCountService = new Mock<IIntelligentHubCharacterCountService>();

    var trigger = new GetCompanyAgentConfigs(authContext, agentConfigService, workflowCountService, characterCountService.Object);
    authContext.SleekflowCompanyId = mockCompanyId;

    var input = new GetCompanyAgentConfigs.GetCompanyAgentConfigsInput();
    var result = await trigger.F(input);

    // Assert that no agent configs are returned
    Assert.That(result, Is.Not.Null);
    Assert.That(result.CompanyAgentConfigs, Is.Not.Null);
    Assert.That(result.CompanyAgentConfigs.Count, Is.EqualTo(0));

    // Verify that MassTransit was not called since there are no agent configs
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Never);
  }

  [Test]
  public async Task GetCompanyAgentConfigs_WithMixedResults_HandlesPartialFailuresGracefully()
  {
    var mockCompanyId = $"TestCompanyId-{Guid.NewGuid()}";
    using var scope = Application.Host.Services.CreateScope();

    var serviceProvider = scope.ServiceProvider;
    var workflowCountService = new WorkflowCountService(
      _mockRequestClient.Object,
      serviceProvider.GetRequiredService<ILogger<WorkflowCountService>>());

    Application.TestAuthorizationContext.SleekflowCompanyId = mockCompanyId;

    // Create two agent configs
    var createInput1 = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
      "SuccessfulAgentConfig",
      CompanyAgentTypes.Sales,
      true,
      true,
      10,
      null,
      null,
      AgentCollaborationModes.Default,
      null, // leadNurturingTools
      null, // toolsConfig
      null, // enricherConfigs
      null, // knowledgeRetrievalConfig
      null, // description
      null, // actions
      null); // promptInstruction
    var createInput2 = new CreateCompanyAgentConfig.CreateCompanyAgentConfigInput(
      "FailingAgentConfig",
      CompanyAgentTypes.Support,
      true,
      true,
      10,
      null,
      null,
      AgentCollaborationModes.Default,
      null, // leadNurturingTools
      null, // toolsConfig
      null, // enricherConfigs
      null, // knowledgeRetrievalConfig
      null, // description
      null, // actions
      null); // promptInstruction

    var createResult1 = await Application.Host.Scenario(_ =>
    {
      _.WithRequestHeader("X-Sleekflow-Record", "true");
      _.Post.Json(createInput1).ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
    });

    var createResult2 = await Application.Host.Scenario(_ =>
    {
      _.WithRequestHeader("X-Sleekflow-Record", "true");
      _.Post.Json(createInput2).ToUrl("/authorized/CompanyAgentConfigs/CreateCompanyAgentConfig");
    });

    var createOutput1 =
      await createResult1.ReadAsJsonAsync<Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();
    var createOutput2 =
      await createResult2.ReadAsJsonAsync<Output<CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>>();

    var agentConfig1Id = createOutput1!.Data!.CompanyAgentConfig!.Id;
    var agentConfig2Id = createOutput2!.Data!.CompanyAgentConfig!.Id;

    // Set up mock: FlowHub consumer handles partial failures internally and returns partial results
    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      { agentConfig1Id, 2 }, // Successful
      { agentConfig2Id, 0 }  // Failed internally in FlowHub consumer
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(mockResponse.Object);

    // Test the endpoint
    using var testScope = Application.Host.Services.CreateScope();
    var testServiceProvider = testScope.ServiceProvider;

    var authContext = testServiceProvider.GetRequiredService<ISleekflowAuthorizationContext>();
    var agentConfigService = testServiceProvider.GetRequiredService<ICompanyAgentConfigService>();
    var characterCountService = new Mock<IIntelligentHubCharacterCountService>();

    var trigger = new GetCompanyAgentConfigs(authContext, agentConfigService, workflowCountService, characterCountService.Object);
    authContext.SleekflowCompanyId = mockCompanyId;

    var input = new GetCompanyAgentConfigs.GetCompanyAgentConfigsInput();
    var result = await trigger.F(input);

    // Assert results
    Assert.That(result, Is.Not.Null);
    Assert.That(result.CompanyAgentConfigs.Count, Is.EqualTo(2));

    var successfulConfig = result.CompanyAgentConfigs.First(c => c.Id == agentConfig1Id);
    var failingConfig = result.CompanyAgentConfigs.First(c => c.Id == agentConfig2Id);

    Assert.That(successfulConfig.ActiveWorkflowCount, Is.EqualTo(2)); // Successful call
    Assert.That(failingConfig.ActiveWorkflowCount, Is.EqualTo(0)); // Failed call returns 0

    // Verify the request was made
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Once);
  }
}