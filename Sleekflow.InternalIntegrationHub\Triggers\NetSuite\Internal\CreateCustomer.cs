using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class CreateCustomer
    : ITrigger<CreateCustomer.CreateCustomerInput,
        CreateCustomer.CreateCustomerOutput>
{
    private readonly INetSuiteCustomerService _netSuiteCustomerService;

    public CreateCustomer(INetSuiteCustomerService netSuiteCustomerService)
    {
        _netSuiteCustomerService = netSuiteCustomerService;
    }

    public class CreateCustomerInput
    {
        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [Required]
        [JsonProperty("company_name")]
        public string CompanyName { get; set; }

        [Required]
        [JsonProperty("company_country")]
        public string CompanyCountry { get; set; }

        [JsonProperty("company_owner_email")]
        public string? CompanyOwnerEmail { get; set; }

        [JsonProperty("company_owner_phone")]
        public string? CompanyOwnerPhone { get; set; }

        [JsonProperty("sales_rep_email")]
        public string? SalesRepEmail { get; set; }

        [JsonConstructor]
        public CreateCustomerInput(
            string companyId,
            string companyName,
            string companyCountry,
            string? companyOwnerEmail = null,
            string? companyOwnerPhone = null,
            string? salesRepEmail = null)
        {
            CompanyId = companyId;
            CompanyName = companyName;
            CompanyCountry = companyCountry;
            CompanyOwnerEmail = companyOwnerEmail;
            CompanyOwnerPhone = companyOwnerPhone;
            SalesRepEmail = salesRepEmail;
        }
    }

    public class CreateCustomerOutput
    {
    }


    public async Task<CreateCustomerOutput> F(CreateCustomerInput input)
    {
        var isSuccess = await _netSuiteCustomerService.CreateCustomerAsync(input);
        if (!isSuccess)
        {
            throw new Exception("Failed to create customer in NetSuite");
        }

        return new CreateCustomerOutput();
    }
}