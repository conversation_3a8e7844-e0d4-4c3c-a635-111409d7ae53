using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Sleekflow.EmailHub.Workers.Configs;

namespace Sleekflow.EmailHub.Workers.Triggers.OnPremise;

public class OnPremiseEmailSync
{
    private readonly ILogger<OnPremiseEmailSync> _logger;
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public OnPremiseEmailSync(
        ILogger<OnPremiseEmailSync> logger,
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    [Function("OnPremise_Email_Periodic_Sync")]
    public async Task OnPremiseEmailPeriodicSync(
        [TimerTrigger("0 * * * * *")] TimerInfo timerInfo)
    {
        _logger.LogInformation($"OnPremiseEmailPeriodicSync timer trigger function executed at {DateTime.UtcNow}");
        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri(_appConfig.EmailHubInternalEndpoint + "/Providers/PeriodicSyncOnPremiseProvider"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var _ = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        _logger.LogInformation($"OnPremiseEmailPeriodicSync timer trigger function finished execution at {DateTime.UtcNow}");
    }
}