using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Stores.ShopifyStores;

public class ShopifySyncConfig
{
    [JsonProperty("sync_only_with_phone_number")]
    public bool SyncOnlyWithPhoneNumber { get; set; }

    [JsonProperty("sync_product_tags")]
    public bool SyncProductTags { get; set; }

    [JsonProperty("sync_order_tags")]
    public bool SyncOrderTags { get; set; }

    [JsonProperty("sync_customer_tags")]
    public bool SyncCustomerTags { get; set; }

    [JsonConstructor]
    public ShopifySyncConfig(
        bool syncOnlyWithPhoneNumber,
        bool syncProductTags,
        bool syncOrderTags,
        bool syncCustomerTags)
    {
        SyncOnlyWithPhoneNumber = syncOnlyWithPhoneNumber;
        SyncProductTags = syncProductTags;
        SyncOrderTags = syncOrderTags;
        SyncCustomerTags = syncCustomerTags;
    }
}