using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Queries;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Orders;

[TriggerGroup(ControllerNames.Orders)]
public class GetOrders
    : ITrigger<
        GetOrders.GetOrdersInput,
        GetOrders.GetOrdersOutput>
{
    private readonly IOrderService _orderService;

    public GetOrders(
        IOrderService orderService)
    {
        _orderService = orderService;
    }

    public class GetOrdersInput
    {
        [JsonProperty("continuation_token")]
        [StringLength(1024, MinimumLength = 1)]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("sorts")]
        public List<QueryBuilder.Sort> Sorts { get; set; }

        [JsonConstructor]
        public GetOrdersInput(
            string? continuationToken,
            string sleekflowCompanyId,
            int limit,
            List<QueryBuilder.Sort> sorts)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            Limit = limit;
            Sorts = sorts;
        }
    }

    public class GetOrdersOutput
    {
        [JsonProperty("orders")]
        public List<OrderDto> Orders { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetOrdersOutput(
            List<Order> orders,
            string? nextContinuationToken)
        {
            Orders = orders.Select(o => new OrderDto(o)).ToList();
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetOrdersOutput> F(GetOrdersInput createProductsInput)
    {
        var (orders, nextContinuationToken) = await _orderService.GetOrdersAsync(
            createProductsInput.SleekflowCompanyId,
            createProductsInput.ContinuationToken,
            createProductsInput.Limit,
            createProductsInput.Sorts);

        return new GetOrdersOutput(orders, nextContinuationToken);
    }
}