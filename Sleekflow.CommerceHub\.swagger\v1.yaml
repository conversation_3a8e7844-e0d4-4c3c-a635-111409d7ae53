openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7080
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/commerce-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub
    description: Prod Apigw
paths:
  /Blobs/CreateUploadBlobSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUploadBlobSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUploadBlobSasUrlsOutputOutput'
  /Carts/ClearCart:
    post:
      tags:
        - Carts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClearCartInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClearCartOutputOutput'
  /Carts/GetCalculatedCart:
    post:
      tags:
        - Carts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCalculatedCartInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCalculatedCartOutputOutput'
  /Carts/GetCart:
    post:
      tags:
        - Carts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCartInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCartOutputOutput'
  /Carts/GetUserCarts:
    post:
      tags:
        - Carts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserCartsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserCartsOutputOutput'
  /Carts/UpdateCart:
    post:
      tags:
        - Carts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCartInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCartOutputOutput'
  /Carts/UpdateCartItem:
    post:
      tags:
        - Carts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCartItemInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCartItemOutputOutput'
  /Categories/AutocompleteCategories:
    post:
      tags:
        - Categories
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutocompleteCategoriesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutocompleteCategoriesOutputOutput'
  /Categories/CreateCategory:
    post:
      tags:
        - Categories
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCategoryInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCategoryOutputOutput'
  /Categories/DeleteCategory:
    post:
      tags:
        - Categories
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteCategoryInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteCategoryOutputOutput'
  /Categories/GetCategories:
    post:
      tags:
        - Categories
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCategoriesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCategoriesOutputOutput'
  /Categories/SearchCategories:
    post:
      tags:
        - Categories
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchCategoriesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchCategoriesOutputOutput'
  /Categories/SuggestCategories:
    post:
      tags:
        - Categories
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SuggestCategoriesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestCategoriesOutputOutput'
  /Categories/UpdateCategory:
    post:
      tags:
        - Categories
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCategoryInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCategoryOutputOutput'
  /Currencies/GetCurrencies:
    post:
      tags:
        - Currencies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCurrenciesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCurrenciesOutputOutput'
  /Currencies/GetSupportedCurrencies:
    post:
      tags:
        - Currencies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSupportedCurrenciesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSupportedCurrenciesOutputOutput'
  /CustomCatalogConfigs/CreateCustomCatalogConfig:
    post:
      tags:
        - CustomCatalogConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomCatalogConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCustomCatalogConfigOutputOutput'
  /CustomCatalogConfigs/GetCustomCatalogConfigs:
    post:
      tags:
        - CustomCatalogConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomCatalogConfigsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomCatalogConfigsOutputOutput'
  /CustomCatalogConfigs/GetCustomCatalogLimits:
    post:
      tags:
        - CustomCatalogConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomCatalogLimitsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomCatalogLimitsOutputOutput'
  /CustomCatalogConfigs/UpdateCustomCatalogConfig:
    post:
      tags:
        - CustomCatalogConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomCatalogConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCustomCatalogConfigOutputOutput'
  /CustomCatalogs/GetCsvTemplate:
    get:
      tags:
        - CustomCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /CustomCatalogs/GetCsvTemplateSample:
    get:
      tags:
        - CustomCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /CustomCatalogs/GetCustomCatalogFiles:
    post:
      tags:
        - CustomCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomCatalogFilesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomCatalogFilesOutputOutput'
  /CustomCatalogs/GetCustomCatalogUsages:
    post:
      tags:
        - CustomCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetCustomCatalogUsagesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetCustomCatalogUsagesOutputOutput'
  /CustomCatalogs/ProcessCustomCatalogCsv:
    post:
      tags:
        - CustomCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProcessCustomCatalogCsvInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessCustomCatalogCsvOutputOutput'
  /CustomCatalogs/VerifyCustomCatalogCsv:
    post:
      tags:
        - CustomCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyCustomCatalogCsvInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifyCustomCatalogCsvOutputOutput'
  /Internals/PatchCustomCatalogFileProcessStatus:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchCustomCatalogFileProcessStatusInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PatchCustomCatalogFileProcessStatusOutputOutput'
  /Internals/ProcessCustomCatalogCsvBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProcessCustomCatalogCsvBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessCustomCatalogCsvBatchOutputOutput'
  /Languages/GetLanguages:
    post:
      tags:
        - Languages
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetLanguagesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLanguagesOutputOutput'
  /Orders/CreateDraftOrder:
    post:
      tags:
        - Orders
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDraftOrderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateDraftOrderOutputOutput'
  /Orders/GetOrders:
    post:
      tags:
        - Orders
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetOrdersInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetOrdersOutputOutput'
  /Orders/GetUserOrders:
    post:
      tags:
        - Orders
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserOrdersInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserOrdersOutputOutput'
  /Orders/UpdateOrder:
    post:
      tags:
        - Orders
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOrderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateOrderOutputOutput'
  /PaymentProviderConfigs/CreateStripeConnectPaymentProviderConfig:
    post:
      tags:
        - PaymentProviderConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStripeConnectPaymentProviderConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateStripeConnectPaymentProviderConfigOutputOutput'
  /PaymentProviderConfigs/CreateStripePaymentProviderConfig:
    post:
      tags:
        - PaymentProviderConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStripePaymentProviderConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateStripePaymentProviderConfigOutputOutput'
  /PaymentProviderConfigs/DeletePaymentProviderConfig:
    post:
      tags:
        - PaymentProviderConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeletePaymentProviderConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeletePaymentProviderConfigOutputOutput'
  /PaymentProviderConfigs/GetPaymentProviderConfig:
    post:
      tags:
        - PaymentProviderConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentProviderConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentProviderConfigOutputOutput'
  /PaymentProviderConfigs/GetPaymentProviderConfigs:
    post:
      tags:
        - PaymentProviderConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentProviderConfigsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentProviderConfigsOutputOutput'
  /PaymentProviderConfigs/GetStorePaymentProviderConfigs:
    post:
      tags:
        - PaymentProviderConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetStorePaymentProviderConfigsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetStorePaymentProviderConfigsOutputOutput'
  /PaymentProviderConfigs/LinkPaymentProviderConfig:
    post:
      tags:
        - PaymentProviderConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LinkPaymentProviderConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LinkPaymentProviderConfigOutputOutput'
  /Payments/CreateOrderPayment:
    post:
      tags:
        - Payments
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderPaymentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrderPaymentOutputOutput'
  /Payments/RefundOrderPayment:
    post:
      tags:
        - Payments
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefundOrderPaymentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefundOrderPaymentOutputOutput'
  /Products/AutocompleteProducts:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AutocompleteProductsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AutocompleteProductsOutputOutput'
  /Products/CreateDefaultProduct:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateDefaultProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateDefaultProductOutputOutput'
  /Products/CreateProduct:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateProductOutputOutput'
  /Products/DeleteProduct:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteProductOutputOutput'
  /Products/DeleteProducts:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteProductsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteProductsOutputOutput'
  /Products/DuplicateProducts:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DuplicateProductsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DuplicateProductsOutputOutput'
  /Products/GetProduct:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductOutputOutput'
  /Products/GetProductMessagePreview:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProductMessagePreviewInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductMessagePreviewOutputOutput'
  /Products/GetProducts:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProductsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductsOutputOutput'
  /Products/SearchProducts:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchProductsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchProductsOutputOutput'
  /Products/SuggestProducts:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SuggestProductsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuggestProductsOutputOutput'
  /Products/UpdateDefaultProduct:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDefaultProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateDefaultProductOutputOutput'
  /Products/UpdateProduct:
    post:
      tags:
        - Products
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateProductOutputOutput'
  /ProductVariants/CreateProductVariant:
    post:
      tags:
        - ProductVariants
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductVariantInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateProductVariantOutputOutput'
  /ProductVariants/DeleteProductVariant:
    post:
      tags:
        - ProductVariants
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteProductVariantInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteProductVariantOutputOutput'
  /ProductVariants/GetProductVariants:
    post:
      tags:
        - ProductVariants
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProductVariantsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductVariantsOutputOutput'
  /ProductVariants/UpdateProductVariant:
    post:
      tags:
        - ProductVariants
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProductVariantInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateProductVariantOutputOutput'
  /Public/healthz:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Shopify/CreateShopifyDraftOrder:
    post:
      tags:
        - Shopify
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateShopifyDraftOrderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateShopifyDraftOrderOutputOutput'
  /ShopifyCarts/GetShopifyCart:
    post:
      tags:
        - ShopifyCarts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetShopifyCartInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShopifyCartOutputOutput'
  /ShopifyCarts/GetShopifyCartExternalIntegrationInfo:
    post:
      tags:
        - ShopifyCarts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetShopifyCartExternalIntegrationInfoInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShopifyCartExternalIntegrationInfoOutputOutput'
  /ShopifyCarts/GetShopifyCartStatistics:
    post:
      tags:
        - ShopifyCarts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetShopifyCartStatisticsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShopifyCartStatisticsOutputOutput'
  /ShopifyCarts/UpdateShopifyCart:
    post:
      tags:
        - ShopifyCarts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateShopifyCartInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateShopifyCartOutputOutput'
  /ShopifyOrders/CreateOrUpdateShopifyOrder:
    post:
      tags:
        - ShopifyOrders
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrUpdateShopifyOrderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrUpdateShopifyOrderOutputOutput'
  /ShopifyOrders/CreateOrUpdateShopifyProduct:
    post:
      tags:
        - ShopifyOrders
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrUpdateShopifyProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateOrUpdateShopifyProductOutputOutput'
  /ShopifyOrders/GetShopifyOrderStatistics:
    post:
      tags:
        - ShopifyOrders
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetShopifyOrderStatisticsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShopifyOrderStatisticsOutputOutput'
  /ShopifyProducts/GetShopifyProduct:
    post:
      tags:
        - ShopifyProducts
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetShopifyProductInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShopifyProductOutputOutput'
  /ShopifyStores/GetShopifyStoreIntegrationExternalConfig:
    post:
      tags:
        - ShopifyStores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetShopifyStoreIntegrationExternalConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetShopifyStoreIntegrationExternalConfigOutputOutput'
  /ShopifyStores/IntegrateOutOfBoxShopifyStore:
    post:
      tags:
        - ShopifyStores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IntegrateOutOfBoxShopifyStoreInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegrateOutOfBoxShopifyStoreOutputOutput'
  /ShopifyStores/IntegratePublicShopifyStore:
    post:
      tags:
        - ShopifyStores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IntegratePublicShopifyStoreInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IntegratePublicShopifyStoreOutputOutput'
  /ShopifyStores/UpdateShopifyStoreIntegrationExternalConfig:
    post:
      tags:
        - ShopifyStores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateShopifyStoreIntegrationExternalConfigInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateShopifyStoreIntegrationExternalConfigOutputOutput'
  /Stores/CreateStore:
    post:
      tags:
        - Stores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStoreInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateStoreOutputOutput'
  /Stores/DeleteStore:
    post:
      tags:
        - Stores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteStoreInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteStoreOutputOutput'
  /Stores/GetStore:
    post:
      tags:
        - Stores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetStoreInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetStoreOutputOutput'
  /Stores/GetStores:
    post:
      tags:
        - Stores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetStoresInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetStoresOutputOutput'
  /Stores/PreviewStoreTemplates:
    post:
      tags:
        - Stores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewStoreTemplatesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewStoreTemplatesOutputOutput'
  /Stores/PreviewTemplate:
    post:
      tags:
        - Stores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewTemplateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewTemplateOutputOutput'
  /Stores/UpdateStore:
    post:
      tags:
        - Stores
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStoreInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateStoreOutputOutput'
  /StripeConnectOnboardingLinkRefresh/refresh:
    get:
      tags:
        - StripeConnectOnboardingLinkRefresh
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /StripeWebhook/connect:
    post:
      tags:
        - StripeWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /StripeWebhook/payment:
    post:
      tags:
        - StripeWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /StripeWebhook/refund:
    post:
      tags:
        - StripeWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Webhooks/RegisterWebhook:
    post:
      tags:
        - Webhooks
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterWebhookInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterWebhookOutputOutput'
components:
  schemas:
    AutocompleteCategoriesInput:
      required:
        - limit
        - search_text
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        search_text:
          maxLength: 256
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    AutocompleteCategoriesOutput:
      type: object
      properties:
        autocomplete_items:
          type: array
          items:
            $ref: '#/components/schemas/AutocompleteItem'
          nullable: true
      additionalProperties: false
    AutocompleteCategoriesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AutocompleteCategoriesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AutocompleteItem:
      type: object
      properties:
        text:
          type: string
          nullable: true
          readOnly: true
        queryPlusText:
          type: string
          nullable: true
          readOnly: true
      additionalProperties: false
    AutocompleteProductsInput:
      required:
        - limit
        - search_text
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        search_text:
          maxLength: 256
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    AutocompleteProductsOutput:
      type: object
      properties:
        autocomplete_items:
          type: array
          items:
            $ref: '#/components/schemas/AutocompleteItem'
          nullable: true
      additionalProperties: false
    AutocompleteProductsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AutocompleteProductsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Blob:
      type: object
      properties:
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
      additionalProperties: false
    CalculatedCartDto:
      type: object
      properties:
        calculated_line_items:
          type: array
          items:
            $ref: '#/components/schemas/CalculatedLineItem'
          nullable: true
        subtotal_price:
          type: number
          format: decimal
        total_price:
          type: number
          format: decimal
        store_id:
          type: string
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/CartLineItemDto'
          nullable: true
        cart_discount:
          $ref: '#/components/schemas/Discount'
        cart_status:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    CalculatedLineItem:
      type: object
      properties:
        applied_discounts:
          type: array
          items:
            $ref: '#/components/schemas/CalculatedLineItemAppliedDiscount'
          nullable: true
        line_item_pre_calculated_amount:
          type: number
          format: decimal
        line_item_calculated_amount:
          type: number
          format: decimal
        message_preview:
          $ref: '#/components/schemas/RenderedTemplate'
        product_variant_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
        line_item_discount:
          $ref: '#/components/schemas/Discount'
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    CalculatedLineItemAppliedDiscount:
      type: object
      properties:
        level:
          type: string
          nullable: true
        applied_discount:
          $ref: '#/components/schemas/Discount'
        pre_calculated_amount:
          type: number
          format: decimal
        post_calculated_amount:
          type: number
          format: decimal
      additionalProperties: false
    CartDto:
      type: object
      properties:
        store_id:
          type: string
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/CartLineItemDto'
          nullable: true
        cart_discount:
          $ref: '#/components/schemas/Discount'
        cart_status:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    CartLineItem:
      type: object
      properties:
        product_variant_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
        line_item_discount:
          $ref: '#/components/schemas/Discount'
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    CartLineItemDto:
      type: object
      properties:
        product_variant_snapshot:
          $ref: '#/components/schemas/ProductVariantDto'
        product_snapshot:
          $ref: '#/components/schemas/ProductDto'
        product_variant_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
        line_item_discount:
          $ref: '#/components/schemas/Discount'
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    CategoryDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        store_id:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
          nullable: true
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
          nullable: true
        platform_data:
          $ref: '#/components/schemas/PlatformData'
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    ClearCartInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_user_profile_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ClearCartOutput:
      type: object
      properties:
        cart:
          $ref: '#/components/schemas/CartDto'
      additionalProperties: false
    ClearCartOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ClearCartOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateCategoryInput:
      required:
        - descriptions
        - metadata
        - names
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        names:
          maxItems: 32
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          maxItems: 32
          type: array
          items:
            $ref: '#/components/schemas/Description'
        platform_data:
          $ref: '#/components/schemas/PlatformData'
        metadata:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateCategoryOutput:
      type: object
      properties:
        category:
          $ref: '#/components/schemas/CategoryDto'
      additionalProperties: false
    CreateCategoryOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateCategoryOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateCustomCatalogConfigInput:
      required:
        - count
        - period_end
        - period_start
        - sleekflow_company_id
        - sleekflow_staff_id
        - type
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        type:
          minLength: 1
          type: string
        count:
          type: integer
          format: int32
        period_start:
          type: string
          format: date-time
        period_end:
          type: string
          format: date-time
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateCustomCatalogConfigOutput:
      type: object
      additionalProperties: false
    CreateCustomCatalogConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateCustomCatalogConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateDefaultProductInput:
      required:
        - attributes
        - category_ids
        - descriptions
        - images
        - metadata
        - names
        - prices
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantAttribute'
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        category_ids:
          type: array
          items:
            type: string
        sku:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateDefaultProductOutput:
      type: object
      properties:
        product:
          $ref: '#/components/schemas/ProductDto'
      additionalProperties: false
    CreateDefaultProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateDefaultProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateDraftOrderInput:
      required:
        - country_iso_code
        - currency_iso_code
        - language_iso_code
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_user_profile_id
        - store_id
        - user_profile
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        user_profile:
          $ref: '#/components/schemas/UserProfile'
        store_id:
          minLength: 1
          type: string
        currency_iso_code:
          minLength: 1
          type: string
        country_iso_code:
          minLength: 1
          type: string
        language_iso_code:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateDraftOrderOutput:
      type: object
      properties:
        order:
          $ref: '#/components/schemas/OrderDto'
      additionalProperties: false
    CreateDraftOrderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateDraftOrderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateOrUpdateShopifyOrderInput:
      required:
        - conversion_status
        - metadata
        - provider_order
        - sleekflow_company_id
        - sleekflow_user_profile_id
        - store_id
        - user_profile
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        store_id:
          minLength: 1
          type: string
        provider_order:
          type: object
          additionalProperties:
            nullable: true
        user_profile:
          $ref: '#/components/schemas/UserProfile'
        sleekflow_platform_country:
          type: string
          nullable: true
        conversion_status:
          minLength: 1
          type: string
        payment_link_sent_at:
          type: string
          format: date-time
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateOrUpdateShopifyOrderOutput:
      type: object
      properties:
        shopify_order:
          $ref: '#/components/schemas/ShopifyOrderDto'
      additionalProperties: false
    CreateOrUpdateShopifyOrderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateOrUpdateShopifyOrderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateOrUpdateShopifyProductInput:
      required:
        - images
        - is_view_enabled
        - metadata
        - provider_product
        - provider_product_variants
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        is_view_enabled:
          type: boolean
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        provider_product:
          type: object
          additionalProperties:
            nullable: true
        provider_product_variants:
          type: array
          items:
            type: object
            additionalProperties: { }
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateOrUpdateShopifyProductOutput:
      type: object
      properties:
        product:
          $ref: '#/components/schemas/ProductDto'
      additionalProperties: false
    CreateOrUpdateShopifyProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateOrUpdateShopifyProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateOrderPaymentInput:
      required:
        - expired_at
        - language_iso_code
        - order_id
        - payment_provider_name
        - return_to_url
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        order_id:
          minLength: 1
          type: string
        payment_provider_name:
          minLength: 1
          pattern: ^(Stripe)$
          type: string
        language_iso_code:
          maxLength: 128
          minLength: 1
          type: string
        expired_at:
          type: string
          format: date-time
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        return_to_url:
          minLength: 1
          type: string
      additionalProperties: false
    CreateOrderPaymentOutput:
      type: object
      properties:
        payment_redirection_link:
          type: string
          nullable: true
      additionalProperties: false
    CreateOrderPaymentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateOrderPaymentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateProductInput:
      required:
        - category_ids
        - descriptions
        - images
        - is_view_enabled
        - metadata
        - names
        - product_variants
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        product_variants:
          type: array
          items:
            $ref: '#/components/schemas/CreateProductInputProductVariant'
        platform_data:
          $ref: '#/components/schemas/PlatformData'
        is_view_enabled:
          type: boolean
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        category_ids:
          type: array
          items:
            type: string
        sku:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateProductInputProductVariant:
      required:
        - attributes
        - descriptions
        - images
        - names
        - position
        - prices
      type: object
      properties:
        sku:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        url:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
        position:
          type: integer
          format: int32
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantAttribute'
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        platform_data:
          $ref: '#/components/schemas/PlatformData'
      additionalProperties: false
    CreateProductOutput:
      type: object
      properties:
        product:
          $ref: '#/components/schemas/ProductDto'
      additionalProperties: false
    CreateProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateProductVariantInput:
      required:
        - attributes
        - descriptions
        - images
        - names
        - position
        - prices
        - product_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        product_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        sku:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        url:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
        position:
          type: integer
          format: int32
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantAttribute'
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        platform_data:
          $ref: '#/components/schemas/PlatformData'
      additionalProperties: false
    CreateProductVariantOutput:
      type: object
      properties:
        product_variant:
          $ref: '#/components/schemas/ProductVariantDto'
      additionalProperties: false
    CreateProductVariantOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateProductVariantOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateShopifyDraftOrderInput:
      required:
        - order_line_items
        - shopify_customer_email
        - shopify_customer_phone_number
        - shopify_store_id
        - sleekflow_company_id
      type: object
      properties:
        shopify_store_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        shopify_customer_email:
          minLength: 1
          type: string
        shopify_customer_phone_number:
          minLength: 1
          type: string
        order_line_items:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyDraftOrderLineItem'
        order_note:
          type: string
          nullable: true
        order_tags:
          type: string
          nullable: true
      additionalProperties: false
    CreateShopifyDraftOrderOutput:
      type: object
      properties:
        order_note:
          type: string
          nullable: true
        invoice_url:
          type: string
          nullable: true
      additionalProperties: false
    CreateShopifyDraftOrderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateShopifyDraftOrderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateStoreInput:
      required:
        - currencies
        - descriptions
        - is_payment_enabled
        - is_view_enabled
        - languages
        - metadata
        - names
        - sleekflow_company_id
        - sleekflow_staff_id
        - template_dict
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        names:
          maxItems: 4
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          maxItems: 16
          type: array
          items:
            $ref: '#/components/schemas/Description'
        is_view_enabled:
          type: boolean
        is_payment_enabled:
          type: boolean
        languages:
          maxItems: 4
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/LanguageInputDto'
        currencies:
          maxItems: 4
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/CurrencyInputDto'
        template_dict:
          $ref: '#/components/schemas/StoreTemplateDict'
        metadata:
          type: object
          additionalProperties:
            nullable: true
        store_integration_external_config_input:
          $ref: '#/components/schemas/StoreIntegrationExternalConfigInput'
        subscription_status:
          $ref: '#/components/schemas/StoreSubscriptionStatus'
      additionalProperties: false
    CreateStoreOutput:
      type: object
      properties:
        store:
          $ref: '#/components/schemas/StoreDto'
      additionalProperties: false
    CreateStoreOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateStoreOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateStripeConnectPaymentProviderConfigInput:
      required:
        - application_fee_rate
        - is_inventory_enabled
        - is_shipping_enabled
        - platform_country
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        platform_country:
          minLength: 1
          type: string
        application_fee_rate:
          type: number
          format: decimal
        is_shipping_enabled:
          type: boolean
        shipping_allowed_country_iso_codes:
          type: array
          items:
            type: string
          nullable: true
        is_inventory_enabled:
          type: boolean
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateStripeConnectPaymentProviderConfigOutput:
      type: object
      properties:
        stripe_connect_onboarding_link:
          type: string
          nullable: true
      additionalProperties: false
    CreateStripeConnectPaymentProviderConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateStripeConnectPaymentProviderConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateStripePaymentProviderConfigInput:
      required:
        - api_key
        - application_fee_rate
        - connect_webhook_secret
        - is_inventory_enabled
        - is_shipping_enabled
        - payment_webhook_secret
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        api_key:
          minLength: 1
          type: string
        connect_webhook_secret:
          minLength: 1
          type: string
        payment_webhook_secret:
          minLength: 1
          type: string
        application_fee_rate:
          type: number
          format: decimal
        is_shipping_enabled:
          type: boolean
        shipping_allowed_country_iso_codes:
          type: array
          items:
            type: string
          nullable: true
        is_inventory_enabled:
          type: boolean
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateStripePaymentProviderConfigOutput:
      required:
        - payment_provider_config_id
      type: object
      properties:
        payment_provider_config_id:
          minLength: 1
          type: string
      additionalProperties: false
    CreateStripePaymentProviderConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateStripePaymentProviderConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUploadBlobSasUrlsInput:
      required:
        - blob_type
        - number_of_blobs
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        number_of_blobs:
          maximum: 16
          minimum: 1
          type: integer
          format: int32
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateUploadBlobSasUrlsOutput:
      type: object
      properties:
        upload_blobs:
          type: array
          items:
            $ref: '#/components/schemas/UploadBlobDto'
          nullable: true
      additionalProperties: false
    CreateUploadBlobSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUploadBlobSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Currency:
      type: object
      properties:
        currency_iso_code:
          type: string
          nullable: true
        currency_name:
          type: string
          nullable: true
        currency_symbol:
          type: string
          nullable: true
      additionalProperties: false
    CurrencyDto:
      type: object
      properties:
        currency_iso_code:
          type: string
          nullable: true
        currency_name:
          type: string
          nullable: true
        currency_symbol:
          type: string
          nullable: true
      additionalProperties: false
    CurrencyInputDto:
      type: object
      properties:
        currency_iso_code:
          type: string
          nullable: true
      additionalProperties: false
    CustomCatalogConfig:
      type: object
      properties:
        type:
          type: string
          nullable: true
        count:
          type: integer
          format: int32
        period_start:
          type: string
          format: date-time
        period_end:
          type: string
          format: date-time
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    CustomCatalogCsvRecord:
      type: object
      properties:
        type:
          type: string
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    CustomCatalogFileDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        store_id:
          type: string
          nullable: true
        blob:
          $ref: '#/components/schemas/Blob'
        file_process_status:
          type: string
          nullable: true
        progress:
          type: integer
          format: int32
        log_sas_url:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    CustomCatalogLimitation:
      type: object
      properties:
        type:
          type: string
          nullable: true
        total_quota:
          type: integer
          format: int64
        current_usage:
          type: integer
          format: int64
        remaining_quota:
          type: integer
          format: int64
      additionalProperties: false
    DeleteCategoryInput:
      required:
        - id
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteCategoryOutput:
      type: object
      additionalProperties: false
    DeleteCategoryOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteCategoryOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeletePaymentProviderConfigInput:
      required:
        - payment_provider_config_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        payment_provider_config_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeletePaymentProviderConfigOutput:
      type: object
      additionalProperties: false
    DeletePaymentProviderConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeletePaymentProviderConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteProductInput:
      required:
        - id
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteProductOutput:
      type: object
      additionalProperties: false
    DeleteProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteProductVariantInput:
      required:
        - id
        - product_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        product_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteProductVariantOutput:
      type: object
      additionalProperties: false
    DeleteProductVariantOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteProductVariantOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteProductsInput:
      required:
        - product_ids
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        product_ids:
          maxItems: 50
          type: array
          items:
            type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteProductsOutput:
      type: object
      additionalProperties: false
    DeleteProductsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteProductsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteStoreInput:
      required:
        - id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteStoreOutput:
      type: object
      additionalProperties: false
    DeleteStoreOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteStoreOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Description:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          $ref: '#/components/schemas/Multilingual'
        image:
          $ref: '#/components/schemas/Image'
        youtube:
          type: string
          nullable: true
      additionalProperties: false
    Discount:
      type: object
      properties:
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        value:
          type: number
          format: decimal
        type:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    DuplicateProductsInput:
      required:
        - product_ids
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        product_ids:
          maxItems: 32
          minItems: 1
          type: array
          items:
            type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DuplicateProductsOutput:
      type: object
      properties:
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductDto'
          nullable: true
      additionalProperties: false
    DuplicateProductsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DuplicateProductsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Filter:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          minLength: 1
          pattern: '^[a-zA-Z0-9_]+$'
          type: string
        operator:
          minLength: 1
          pattern: ^(=|>|<|>=|<=|!=|contains|array_contains|startswith|in)$
          type: string
        value:
          nullable: true
      additionalProperties: false
    GetCalculatedCartInput:
      required:
        - currency_iso_code
        - language_iso_code
        - sleekflow_company_id
        - sleekflow_user_profile_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        currency_iso_code:
          minLength: 1
          type: string
        language_iso_code:
          minLength: 1
          type: string
      additionalProperties: false
    GetCalculatedCartOutput:
      type: object
      properties:
        calculated_cart:
          $ref: '#/components/schemas/CalculatedCartDto'
      additionalProperties: false
    GetCalculatedCartOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCalculatedCartOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCartInput:
      required:
        - sleekflow_company_id
        - sleekflow_user_profile_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCartOutput:
      type: object
      properties:
        cart:
          $ref: '#/components/schemas/CartDto'
      additionalProperties: false
    GetCartOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCartOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCategoriesInput:
      required:
        - limit
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetCategoriesOutput:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        categories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          nullable: true
        count:
          type: integer
          format: int32
      additionalProperties: false
    GetCategoriesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCategoriesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCurrenciesInput:
      type: object
      additionalProperties: false
    GetCurrenciesOutput:
      type: object
      properties:
        currencies:
          type: array
          items:
            $ref: '#/components/schemas/CurrencyDto'
          nullable: true
      additionalProperties: false
    GetCurrenciesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCurrenciesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomCatalogConfigsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomCatalogConfigsOutput:
      type: object
      properties:
        custom_catalog_configs:
          type: array
          items:
            $ref: '#/components/schemas/CustomCatalogConfig'
          nullable: true
      additionalProperties: false
    GetCustomCatalogConfigsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomCatalogConfigsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomCatalogFilesInput:
      required:
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomCatalogFilesOutput:
      type: object
      properties:
        custom_catalog_files:
          type: array
          items:
            $ref: '#/components/schemas/CustomCatalogFileDto'
          nullable: true
      additionalProperties: false
    GetCustomCatalogFilesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomCatalogFilesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomCatalogLimitsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomCatalogLimitsOutput:
      type: object
      properties:
        product_limit:
          type: integer
          format: int32
        product_variant_limit:
          type: integer
          format: int32
      additionalProperties: false
    GetCustomCatalogLimitsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomCatalogLimitsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCustomCatalogUsagesInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCustomCatalogUsagesOutput:
      type: object
      properties:
        product_count:
          type: integer
          format: int32
        product_variant_count:
          type: integer
          format: int32
      additionalProperties: false
    GetCustomCatalogUsagesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetCustomCatalogUsagesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetLanguagesInput:
      type: object
      additionalProperties: false
    GetLanguagesOutput:
      type: object
      properties:
        languages:
          type: array
          items:
            $ref: '#/components/schemas/LanguageDto'
          nullable: true
      additionalProperties: false
    GetLanguagesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetLanguagesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetOrdersInput:
      required:
        - limit
        - sleekflow_company_id
        - sorts
      type: object
      properties:
        continuation_token:
          maxLength: 1024
          minLength: 1
          type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/Sort'
      additionalProperties: false
    GetOrdersOutput:
      type: object
      properties:
        orders:
          type: array
          items:
            $ref: '#/components/schemas/OrderDto'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetOrdersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetOrdersOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetPaymentProviderConfigInput:
      required:
        - payment_provider_config_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        payment_provider_config_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetPaymentProviderConfigOutput:
      type: object
      properties:
        payment_provider_config:
          $ref: '#/components/schemas/PaymentProviderConfigDto'
      additionalProperties: false
    GetPaymentProviderConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetPaymentProviderConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetPaymentProviderConfigsInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetPaymentProviderConfigsOutput:
      type: object
      properties:
        payment_provider_configs:
          type: array
          items:
            $ref: '#/components/schemas/PaymentProviderConfigDto'
          nullable: true
      additionalProperties: false
    GetPaymentProviderConfigsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetPaymentProviderConfigsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProductInput:
      required:
        - product_id
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        product_id:
          maxLength: 128
          minLength: 1
          type: string
      additionalProperties: false
    GetProductMessagePreviewInput:
      required:
        - currency_iso_code
        - language_iso_code
        - product_id
        - product_variant_id
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        product_variant_id:
          minLength: 1
          type: string
        product_id:
          minLength: 1
          type: string
        currency_iso_code:
          minLength: 1
          type: string
        language_iso_code:
          minLength: 1
          type: string
      additionalProperties: false
    GetProductMessagePreviewOutput:
      type: object
      properties:
        message_preview:
          $ref: '#/components/schemas/RenderedTemplate'
      additionalProperties: false
    GetProductMessagePreviewOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProductMessagePreviewOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProductOutput:
      type: object
      properties:
        product:
          $ref: '#/components/schemas/ProductDto'
      additionalProperties: false
    GetProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProductVariantsInput:
      required:
        - product_id
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        product_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProductVariantsOutput:
      type: object
      properties:
        product_variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantDto'
          nullable: true
      additionalProperties: false
    GetProductVariantsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProductVariantsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProductsInput:
      required:
        - filter_groups
        - limit
        - sleekflow_company_id
        - sorts
        - store_id
      type: object
      properties:
        continuation_token:
          maxLength: 1024
          minLength: 1
          type: string
          nullable: true
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/GetProductsInputFilterGroup'
        sorts:
          type: array
          items:
            $ref: '#/components/schemas/Sort'
      additionalProperties: false
    GetProductsInputFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/Filter'
      additionalProperties: false
    GetProductsOutput:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductDto'
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetProductsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProductsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetShopifyCartExternalIntegrationInfoInput:
      required:
        - cart_status
        - shopify_cart_token
        - sleekflow_company_id
        - sleekflow_user_profile_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        cart_status:
          minLength: 1
          type: string
        shopify_cart_token:
          minLength: 1
          type: string
      additionalProperties: false
    GetShopifyCartExternalIntegrationInfoOutput:
      type: object
      properties:
        shopify_cart_external_integration_info:
          $ref: '#/components/schemas/ShopifyCartExternalIntegrationInfo'
      additionalProperties: false
    GetShopifyCartExternalIntegrationInfoOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetShopifyCartExternalIntegrationInfoOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetShopifyCartInput:
      required:
        - cart_status
        - shopify_cart_token
        - sleekflow_company_id
        - sleekflow_user_profile_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        cart_status:
          minLength: 1
          type: string
        shopify_cart_token:
          minLength: 1
          type: string
      additionalProperties: false
    GetShopifyCartOutput:
      type: object
      properties:
        shopify_cart:
          $ref: '#/components/schemas/ShopifyCartDto'
      additionalProperties: false
    GetShopifyCartOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetShopifyCartOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetShopifyCartStatisticsInput:
      required:
        - from_date_time
        - sleekflow_company_id
        - to_date_time
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_id:
          type: string
          nullable: true
        conversion_status:
          type: string
          nullable: true
        from_date_time:
          type: string
          format: date-time
        to_date_time:
          type: string
          format: date-time
      additionalProperties: false
    GetShopifyCartStatisticsOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        total_price:
          type: number
          format: decimal
      additionalProperties: false
    GetShopifyCartStatisticsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetShopifyCartStatisticsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetShopifyOrderStatisticsInput:
      required:
        - from_date_time
        - sleekflow_company_id
        - to_date_time
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_id:
          type: string
          nullable: true
        conversion_status:
          type: string
          nullable: true
        from_date_time:
          type: string
          format: date-time
        to_date_time:
          type: string
          format: date-time
      additionalProperties: false
    GetShopifyOrderStatisticsOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        total_price:
          type: number
          format: decimal
      additionalProperties: false
    GetShopifyOrderStatisticsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetShopifyOrderStatisticsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetShopifyProductInput:
      required:
        - platform_data_id
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        platform_data_id:
          maxLength: 128
          minLength: 1
          type: string
      additionalProperties: false
    GetShopifyProductOutput:
      type: object
      properties:
        shopify_product:
          $ref: '#/components/schemas/ProductDto'
      additionalProperties: false
    GetShopifyProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetShopifyProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetShopifyStoreIntegrationExternalConfigInput:
      required:
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetShopifyStoreIntegrationExternalConfigOutput:
      type: object
      properties:
        shopify_store_integration_external_config:
          $ref: '#/components/schemas/ShopifyStoreIntegrationExternalConfig'
      additionalProperties: false
    GetShopifyStoreIntegrationExternalConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetShopifyStoreIntegrationExternalConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetStoreInput:
      required:
        - id
        - sleekflow_company_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetStoreOutput:
      type: object
      properties:
        store:
          $ref: '#/components/schemas/StoreDto'
      additionalProperties: false
    GetStoreOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetStoreOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetStorePaymentProviderConfigsInput:
      required:
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetStorePaymentProviderConfigsOutput:
      type: object
      properties:
        payment_provider_configs:
          type: array
          items:
            $ref: '#/components/schemas/PaymentProviderConfigDto'
          nullable: true
      additionalProperties: false
    GetStorePaymentProviderConfigsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetStorePaymentProviderConfigsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetStoresInput:
      required:
        - limit
        - sleekflow_company_id
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        is_view_enabled:
          type: boolean
          nullable: true
        is_payment_enabled:
          type: boolean
          nullable: true
        provider_name:
          type: string
          nullable: true
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetStoresOutput:
      type: object
      properties:
        next_continuation_token:
          type: string
          nullable: true
        stores:
          type: array
          items:
            $ref: '#/components/schemas/StoreDto'
          nullable: true
        count:
          type: integer
          format: int32
      additionalProperties: false
    GetStoresOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetStoresOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSupportedCurrenciesInput:
      required:
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSupportedCurrenciesOutput:
      type: object
      properties:
        currencies:
          type: array
          items:
            $ref: '#/components/schemas/CurrencyDto'
          nullable: true
      additionalProperties: false
    GetSupportedCurrenciesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSupportedCurrenciesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserCartsInput:
      required:
        - cart_status
        - sleekflow_company_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        cart_status:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserCartsOutput:
      type: object
      properties:
        carts:
          type: array
          items:
            $ref: '#/components/schemas/CartDto'
          nullable: true
      additionalProperties: false
    GetUserCartsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserCartsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserOrdersInput:
      required:
        - sleekflow_company_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserOrdersOutput:
      type: object
      properties:
        orders:
          type: array
          items:
            $ref: '#/components/schemas/OrderDto'
          nullable: true
      additionalProperties: false
    GetUserOrdersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserOrdersOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Image:
      type: object
      properties:
        image_url:
          type: string
          nullable: true
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
      additionalProperties: false
    ImageDto:
      type: object
      properties:
        image_url:
          maxLength: 4096
          minLength: 1
          type: string
          nullable: true
        blob_name:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
      additionalProperties: false
    IntegrateOutOfBoxShopifyStoreInput:
      required:
        - is_payment_enabled
        - is_view_enabled
        - metadata
        - shop_access_token
        - shopify_payment_config
        - shopify_sync_config
        - shopify_url
        - sleekflow_company_id
        - sleekflow_staff_id
        - template_dict
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        shop_access_token:
          minLength: 1
          type: string
        sleekflow_staff_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        shopify_url:
          minLength: 1
          type: string
        is_view_enabled:
          type: boolean
        is_payment_enabled:
          type: boolean
        subscription_status:
          $ref: '#/components/schemas/StoreSubscriptionStatus'
        metadata:
          type: object
          additionalProperties:
            nullable: true
        template_dict:
          $ref: '#/components/schemas/StoreTemplateDict'
        shopify_sync_config:
          $ref: '#/components/schemas/ShopifySyncConfig'
        shopify_payment_config:
          $ref: '#/components/schemas/ShopifyPaymentConfig'
        shopify_sync_status:
          $ref: '#/components/schemas/ShopifySyncStatus'
        shopify_message_templates:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyMessageTemplate'
          nullable: true
        is_shopify_billing_owner:
          type: boolean
          nullable: true
        charge_updated_at:
          type: string
          format: date-time
          nullable: true
        charge_id:
          type: string
          nullable: true
      additionalProperties: false
    IntegrateOutOfBoxShopifyStoreOutput:
      type: object
      properties:
        store:
          $ref: '#/components/schemas/StoreDto'
      additionalProperties: false
    IntegrateOutOfBoxShopifyStoreOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IntegrateOutOfBoxShopifyStoreOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IntegratePublicShopifyStoreInput:
      required:
        - authorization_code
        - is_payment_enabled
        - is_view_enabled
        - metadata
        - shopify_payment_config
        - shopify_sync_config
        - shopify_url
        - sleekflow_company_id
        - sleekflow_staff_id
        - template_dict
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        authorization_code:
          minLength: 1
          type: string
        sleekflow_staff_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        shopify_url:
          minLength: 1
          type: string
        is_view_enabled:
          type: boolean
        is_payment_enabled:
          type: boolean
        subscription_status:
          $ref: '#/components/schemas/StoreSubscriptionStatus'
        metadata:
          type: object
          additionalProperties:
            nullable: true
        template_dict:
          $ref: '#/components/schemas/StoreTemplateDict'
        shopify_sync_config:
          $ref: '#/components/schemas/ShopifySyncConfig'
        shopify_payment_config:
          $ref: '#/components/schemas/ShopifyPaymentConfig'
        shopify_sync_status:
          $ref: '#/components/schemas/ShopifySyncStatus'
        shopify_message_templates:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyMessageTemplate'
          nullable: true
        is_shopify_billing_owner:
          type: boolean
          nullable: true
        charge_updated_at:
          type: string
          format: date-time
          nullable: true
        charge_id:
          type: string
          nullable: true
      additionalProperties: false
    IntegratePublicShopifyStoreOutput:
      type: object
      properties:
        store:
          $ref: '#/components/schemas/StoreDto'
      additionalProperties: false
    IntegratePublicShopifyStoreOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IntegratePublicShopifyStoreOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Language:
      type: object
      properties:
        language_iso_code:
          type: string
          nullable: true
        language_name:
          type: string
          nullable: true
        native_language_name:
          type: string
          nullable: true
        is_default:
          type: boolean
      additionalProperties: false
    LanguageDto:
      type: object
      properties:
        language_iso_code:
          type: string
          nullable: true
        language_name:
          type: string
          nullable: true
        native_language_name:
          type: string
          nullable: true
      additionalProperties: false
    LanguageInputDto:
      type: object
      properties:
        language_iso_code:
          type: string
          nullable: true
        is_default:
          type: boolean
      additionalProperties: false
    LinkPaymentProviderConfigInput:
      required:
        - currency_iso_codes
        - payment_provider_config_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        payment_provider_config_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        currency_iso_codes:
          type: array
          items:
            type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    LinkPaymentProviderConfigOutput:
      type: object
      additionalProperties: false
    LinkPaymentProviderConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/LinkPaymentProviderConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Multilingual:
      required:
        - language_iso_code
        - value
      type: object
      properties:
        language_iso_code:
          maxLength: 128
          minLength: 1
          type: string
        value:
          maxLength: 4096
          minLength: 1
          type: string
      additionalProperties: false
    MyCustomCatalogCsvReaderState:
      type: object
      properties:
        last_byte_position:
          type: integer
          format: int64
        num_of_records:
          type: integer
          format: int64
        headers:
          type: array
          items:
            type: string
          nullable: true
        is_completed:
          type: boolean
      additionalProperties: false
    OrderDto:
      required:
        - currency_iso_code
      type: object
      properties:
        store_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/OrderLineItem'
          nullable: true
        currency_iso_code:
          maxLength: 3
          minLength: 3
          type: string
        total_price:
          type: number
          format: decimal
        subtotal_price:
          type: number
          format: decimal
        order_discount:
          $ref: '#/components/schemas/Discount'
        order_status:
          type: string
          nullable: true
        payment_status:
          type: string
          nullable: true
        payment_link_sent_at:
          type: string
          format: date-time
          nullable: true
        completed_at:
          type: string
          format: date-time
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    OrderLineItem:
      type: object
      properties:
        product_variant_snapshot:
          $ref: '#/components/schemas/ProductVariant'
        product_variant_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
        line_item_discount:
          $ref: '#/components/schemas/Discount'
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    OrderLineItemInputDto:
      type: object
      properties:
        product_variant_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
        line_item_discount:
          $ref: '#/components/schemas/Discount'
      additionalProperties: false
    PatchCustomCatalogFileProcessStatusInput:
      required:
        - file_process_status
        - id
        - sleekflow_company_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        file_process_status:
          minLength: 1
          type: string
      additionalProperties: false
    PatchCustomCatalogFileProcessStatusOutput:
      type: object
      additionalProperties: false
    PatchCustomCatalogFileProcessStatusOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PatchCustomCatalogFileProcessStatusOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PaymentProviderConfigDto:
      type: object
      properties:
        status:
          type: string
          nullable: true
        store_ids:
          type: array
          items:
            type: string
          nullable: true
        store_id_to_currency_iso_codes_dict:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          nullable: true
        supported_currency_iso_codes:
          type: array
          items:
            type: string
          nullable: true
        payment_provider_external_config:
          $ref: '#/components/schemas/PaymentProviderExternalConfigDto'
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    PaymentProviderExternalConfigDto:
      required:
        - provider_id
        - provider_name
      type: object
      properties:
        provider_name:
          minLength: 1
          type: string
        provider_id:
          minLength: 1
          type: string
      additionalProperties: false
    PlatformData:
      required:
        - metadata
        - type
      type: object
      properties:
        id:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        type:
          maxLength: 128
          minLength: 1
          type: string
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    PreviewStoreTemplatesInput:
      required:
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        store_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    PreviewStoreTemplatesOutput:
      type: object
      properties:
        rendered_template_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/RenderedTemplate'
          nullable: true
      additionalProperties: false
    PreviewStoreTemplatesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewStoreTemplatesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreviewTemplateInput:
      required:
        - sleekflow_company_id
        - store_id
        - template_name
        - templates
      type: object
      properties:
        store_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        template_name:
          minLength: 1
          type: string
        templates:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
      additionalProperties: false
    PreviewTemplateOutput:
      type: object
      properties:
        rendered_template_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/RenderedTemplate'
          nullable: true
      additionalProperties: false
    PreviewTemplateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewTemplateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Price:
      required:
        - amount
        - currency_iso_code
      type: object
      properties:
        currency_iso_code:
          maxLength: 3
          minLength: 3
          type: string
        amount:
          type: number
          format: decimal
      additionalProperties: false
    ProcessCustomCatalogCsvBatchInput:
      required:
        - custom_catalog_file_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        custom_catalog_file_id:
          minLength: 1
          type: string
        my_custom_catalog_csv_reader_state:
          $ref: '#/components/schemas/MyCustomCatalogCsvReaderState'
      additionalProperties: false
    ProcessCustomCatalogCsvBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        my_custom_catalog_csv_reader_state:
          $ref: '#/components/schemas/MyCustomCatalogCsvReaderState'
      additionalProperties: false
    ProcessCustomCatalogCsvBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ProcessCustomCatalogCsvBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ProcessCustomCatalogCsvInput:
      required:
        - blob_name
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        blob_name:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ProcessCustomCatalogCsvOutput:
      type: object
      properties:
        custom_catalog_file_id:
          type: string
          nullable: true
      additionalProperties: false
    ProcessCustomCatalogCsvOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ProcessCustomCatalogCsvOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ProductAttribute:
      type: object
      properties:
        name:
          type: string
          nullable: true
        values:
          type: array
          items:
            $ref: '#/components/schemas/ProductAttributeValue'
          nullable: true
      additionalProperties: false
    ProductAttributeValue:
      type: object
      properties:
        value:
          type: string
          nullable: true
      additionalProperties: false
    ProductDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        store_id:
          type: string
          nullable: true
        category_ids:
          type: array
          items:
            type: string
          nullable: true
        sku:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
          nullable: true
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
          nullable: true
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
          nullable: true
        product_variant_attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductAttribute'
          nullable: true
        product_variant_prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
          nullable: true
        is_view_enabled:
          type: boolean
        default_product_variant:
          $ref: '#/components/schemas/ProductVariantDto'
        product_variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantDto'
          nullable: true
        platform_data:
          $ref: '#/components/schemas/PlatformData'
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    ProductVariant:
      type: object
      properties:
        store_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        sku:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
          nullable: true
        position:
          type: integer
          format: int32
        is_default_variant_product:
          type: boolean
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantAttribute'
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
          nullable: true
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
          nullable: true
        images:
          type: array
          items:
            $ref: '#/components/schemas/Image'
          nullable: true
        platform_data:
          $ref: '#/components/schemas/PlatformData'
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    ProductVariantAttribute:
      type: object
      properties:
        name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    ProductVariantDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        store_id:
          type: string
          nullable: true
        product_id:
          type: string
          nullable: true
        sku:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
          nullable: true
        position:
          type: integer
          format: int32
        is_default_variant_product:
          type: boolean
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantAttribute'
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
          nullable: true
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
          nullable: true
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
          nullable: true
      additionalProperties: false
    RefundOrderPaymentInput:
      required:
        - amount
        - order_id
        - payment_provider_name
        - reason
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_user_profile_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        order_id:
          minLength: 1
          type: string
        payment_provider_name:
          minLength: 1
          pattern: ^(Stripe)$
          type: string
        amount:
          type: number
          format: decimal
        reason:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    RefundOrderPaymentOutput:
      type: object
      properties:
        refund_id:
          type: string
          nullable: true
      additionalProperties: false
    RefundOrderPaymentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RefundOrderPaymentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RegisterWebhookInput:
      required:
        - context
        - entity_type_name
        - event_type_name
        - sleekflow_company_id
        - url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        event_type_name:
          minLength: 1
          type: string
        url:
          minLength: 1
          type: string
        context:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    RegisterWebhookOutput:
      type: object
      additionalProperties: false
    RegisterWebhookOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RegisterWebhookOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RenderedTemplate:
      type: object
      properties:
        coverImageUrl:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    SearchCategoriesInput:
      required:
        - filter_groups
        - limit
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        filter_groups:
          maxItems: 16
          type: array
          items:
            $ref: '#/components/schemas/SearchFilterGroup'
        continuation_token:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        search_text:
          maxLength: 1024
          minLength: 1
          type: string
          nullable: true
      additionalProperties: false
    SearchCategoriesOutput:
      type: object
      properties:
        categories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          nullable: true
        total_count:
          type: integer
          format: int64
        next_continuation_token:
          type: string
          nullable: true
        facets:
          type: object
          additionalProperties:
            type: array
            items:
              type: object
              additionalProperties: { }
          nullable: true
      additionalProperties: false
    SearchCategoriesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SearchCategoriesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SearchFilter:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          maxLength: 128
          minLength: 1
          pattern: '^[a-zA-Z0-9_\[\]\.:]+$'
          type: string
        operator:
          minLength: 1
          pattern: ^(=|>|<|>=|<=|!=|contains|startsWith)$
          type: string
        value:
          nullable: true
      additionalProperties: false
    SearchFilterGroup:
      required:
        - filters
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SearchFilter'
      additionalProperties: false
    SearchProductsInput:
      required:
        - filter_groups
        - limit
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        filter_groups:
          maxItems: 16
          type: array
          items:
            $ref: '#/components/schemas/SearchFilterGroup'
        continuation_token:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        search_text:
          maxLength: 1024
          minLength: 1
          type: string
          nullable: true
      additionalProperties: false
    SearchProductsOutput:
      type: object
      properties:
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductDto'
          nullable: true
        total_count:
          type: integer
          format: int64
        next_continuation_token:
          type: string
          nullable: true
        facets:
          type: object
          additionalProperties:
            type: array
            items:
              type: object
              additionalProperties: { }
          nullable: true
      additionalProperties: false
    SearchProductsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SearchProductsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyCartDto:
      type: object
      properties:
        shopify_cart_external_integration_info:
          $ref: '#/components/schemas/ShopifyCartExternalIntegrationInfo'
        store_id:
          type: string
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/CartLineItemDto'
          nullable: true
        cart_discount:
          $ref: '#/components/schemas/Discount'
        cart_status:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyCartExternalIntegrationInfo:
      type: object
      properties:
        cart_token:
          type: string
          nullable: true
        checkout_url:
          type: string
          nullable: true
        conversion_status:
          type: string
          nullable: true
        total_price:
          type: number
          format: decimal
        total_tax:
          type: number
          format: decimal
        subtotal_price:
          type: number
          format: decimal
        shopify_customer_id:
          type: string
          nullable: true
        abandoned_date:
          type: string
          format: date-time
          nullable: true
        recovered_date:
          type: string
          format: date-time
          nullable: true
        provider_name:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyDraftOrderLineItem:
      type: object
      properties:
        shopify_product_variant_id:
          type: integer
          format: int64
        quantity:
          type: integer
          format: int32
        discount_option:
          $ref: '#/components/schemas/ShopifyDraftOrderLineItemDiscount'
      additionalProperties: false
    ShopifyDraftOrderLineItemDiscount:
      type: object
      properties:
        title:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        value:
          type: number
          format: decimal
        max_amount:
          type: number
          format: decimal
      additionalProperties: false
    ShopifyMessageTemplate:
      type: object
      properties:
        message_body:
          type: string
          nullable: true
        message_params:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ShopifyOrderDto:
      required:
        - currency_iso_code
      type: object
      properties:
        shopify_order_external_integration_info:
          $ref: '#/components/schemas/ShopifyOrderExternalIntegrationInfo'
        store_id:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/OrderLineItem'
          nullable: true
        currency_iso_code:
          maxLength: 3
          minLength: 3
          type: string
        total_price:
          type: number
          format: decimal
        subtotal_price:
          type: number
          format: decimal
        order_discount:
          $ref: '#/components/schemas/Discount'
        order_status:
          type: string
          nullable: true
        payment_status:
          type: string
          nullable: true
        payment_link_sent_at:
          type: string
          format: date-time
          nullable: true
        completed_at:
          type: string
          format: date-time
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyOrderExternalIntegrationInfo:
      type: object
      properties:
        order_name:
          type: string
          nullable: true
        order_note:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        shopify_customer_id:
          type: string
          nullable: true
        shopify_order_status:
          type: string
          nullable: true
        shopify_payment_status:
          type: string
          nullable: true
        fulfillment_status:
          type: string
          nullable: true
        conversion_status:
          type: string
          nullable: true
        cart_token:
          type: string
          nullable: true
        sleekflow_platform_country:
          type: string
          nullable: true
        total_tax:
          type: number
          format: decimal
          nullable: true
        tags:
          type: array
          items:
            type: string
          nullable: true
        provider_name:
          type: string
          nullable: true
        provider_order_id:
          type: string
          nullable: true
      additionalProperties: false
    ShopifyPaymentConfig:
      type: object
      properties:
        payment_link_option:
          type: string
          nullable: true
        is_discounts_enabled:
          type: boolean
      additionalProperties: false
    ShopifyStoreIntegrationExternalConfig:
      type: object
      properties:
        shopify_url:
          type: string
          nullable: true
        access_token:
          type: string
          nullable: true
        integration_type:
          type: string
          nullable: true
        integration_status:
          type: string
          nullable: true
        sync_config:
          $ref: '#/components/schemas/ShopifySyncConfig'
        payment_config:
          $ref: '#/components/schemas/ShopifyPaymentConfig'
        sync_status:
          $ref: '#/components/schemas/ShopifySyncStatus'
        message_templates:
          type: array
          items:
            $ref: '#/components/schemas/ShopifyMessageTemplate'
          nullable: true
        is_shopify_billing_owner:
          type: boolean
          nullable: true
        charge_updated_at:
          type: string
          format: date-time
          nullable: true
        charge_id:
          type: string
          nullable: true
        provider_name:
          type: string
          nullable: true
      additionalProperties: false
    ShopifySyncConfig:
      type: object
      properties:
        sync_only_with_phone_number:
          type: boolean
        sync_product_tags:
          type: boolean
        sync_order_tags:
          type: boolean
        sync_customer_tags:
          type: boolean
      additionalProperties: false
    ShopifySyncStatus:
      type: object
      properties:
        latest_synced_order_created_at:
          type: string
          format: date-time
          nullable: true
        latest_synced_customer_created_at:
          type: string
          format: date-time
          nullable: true
        sync_shopify_order_job_id:
          type: string
          nullable: true
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Sort:
      required:
        - direction
        - field_name
        - is_case_sensitive
      type: object
      properties:
        field_name:
          minLength: 1
          type: string
        direction:
          minLength: 1
          type: string
        is_case_sensitive:
          type: boolean
      additionalProperties: false
    StoreDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
          nullable: true
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
          nullable: true
        platform_data:
          $ref: '#/components/schemas/PlatformData'
        is_view_enabled:
          type: boolean
        is_payment_enabled:
          type: boolean
        languages:
          type: array
          items:
            $ref: '#/components/schemas/Language'
          nullable: true
        currencies:
          type: array
          items:
            $ref: '#/components/schemas/Currency'
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        template_dict:
          $ref: '#/components/schemas/StoreTemplateDict'
        store_integration_external_config:
          $ref: '#/components/schemas/StoreIntegrationExternalConfig'
        subscription_status:
          $ref: '#/components/schemas/StoreSubscriptionStatus'
      additionalProperties: false
    StoreIntegrationExternalConfig:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
      additionalProperties: false
    StoreIntegrationExternalConfigInput:
      required:
        - provider_name
      type: object
      properties:
        provider_name:
          minLength: 1
          type: string
        shopify_config:
          $ref: '#/components/schemas/ShopifyStoreIntegrationExternalConfig'
      additionalProperties: false
    StoreSubscriptionStatus:
      type: object
      properties:
        payment_status:
          type: string
          nullable: true
        sleekflow_bill_record_id:
          type: string
          nullable: true
      additionalProperties: false
    StoreTemplateDict:
      required:
        - message_preview_templates
      type: object
      properties:
        message_preview_templates:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
      additionalProperties: false
    SuggestCategoriesInput:
      required:
        - limit
        - search_text
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        search_text:
          maxLength: 256
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    SuggestCategoriesOutput:
      type: object
      properties:
        categories:
          type: array
          items:
            $ref: '#/components/schemas/CategoryDto'
          nullable: true
      additionalProperties: false
    SuggestCategoriesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SuggestCategoriesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SuggestProductsInput:
      required:
        - limit
        - search_text
        - sleekflow_company_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        search_text:
          maxLength: 256
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    SuggestProductsOutput:
      type: object
      properties:
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductDto'
          nullable: true
      additionalProperties: false
    SuggestProductsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SuggestProductsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateCartInput:
      required:
        - line_items
        - sleekflow_company_id
        - sleekflow_user_profile_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        line_items:
          type: array
          items:
            $ref: '#/components/schemas/CartLineItem'
        cart_discount:
          $ref: '#/components/schemas/Discount'
      additionalProperties: false
    UpdateCartItemInput:
      required:
        - product_id
        - product_variant_id
        - quantity
        - sleekflow_company_id
        - sleekflow_user_profile_id
        - store_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        product_variant_id:
          minLength: 1
          type: string
        product_id:
          minLength: 1
          type: string
        quantity:
          type: integer
          format: int32
      additionalProperties: false
    UpdateCartItemOutput:
      type: object
      properties:
        cart:
          $ref: '#/components/schemas/CartDto'
      additionalProperties: false
    UpdateCartItemOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateCartItemOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateCartOutput:
      type: object
      properties:
        cart:
          $ref: '#/components/schemas/CartDto'
      additionalProperties: false
    UpdateCartOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateCartOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateCategoryInput:
      required:
        - descriptions
        - id
        - names
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_staff_team_ids
        - store_id
      type: object
      properties:
        id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        names:
          maxItems: 32
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          maxItems: 32
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/Description'
        sleekflow_staff_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          maxLength: 128
          minLength: 1
          type: array
          items:
            type: string
      additionalProperties: false
    UpdateCategoryOutput:
      type: object
      properties:
        category:
          $ref: '#/components/schemas/CategoryDto'
      additionalProperties: false
    UpdateCategoryOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateCategoryOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateCustomCatalogConfigInput:
      required:
        - id
        - period_end
        - period_start
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        period_start:
          type: string
          format: date-time
        period_end:
          type: string
          format: date-time
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateCustomCatalogConfigOutput:
      type: object
      additionalProperties: false
    UpdateCustomCatalogConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateCustomCatalogConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateDefaultProductInput:
      required:
        - attributes
        - category_ids
        - descriptions
        - id
        - images
        - is_view_enabled
        - metadata
        - names
        - prices
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        is_view_enabled:
          type: boolean
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantAttribute'
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        category_ids:
          type: array
          items:
            type: string
        sku:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateDefaultProductOutput:
      type: object
      properties:
        product:
          $ref: '#/components/schemas/ProductDto'
      additionalProperties: false
    UpdateDefaultProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateDefaultProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateOrderInput:
      required:
        - country_iso_code
        - currency_iso_code
        - id
        - language_iso_code
        - line_items
        - metadata
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_user_profile_id
        - store_id
        - user_profile
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        user_profile:
          $ref: '#/components/schemas/UserProfile'
        store_id:
          minLength: 1
          type: string
        line_items:
          maxItems: 64
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/OrderLineItemInputDto'
        discount:
          $ref: '#/components/schemas/Discount'
        country_iso_code:
          minLength: 1
          type: string
        language_iso_code:
          minLength: 1
          type: string
        currency_iso_code:
          minLength: 1
          type: string
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateOrderOutput:
      type: object
      properties:
        order:
          $ref: '#/components/schemas/OrderDto'
      additionalProperties: false
    UpdateOrderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateOrderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateProductInput:
      required:
        - category_ids
        - descriptions
        - id
        - images
        - is_view_enabled
        - metadata
        - names
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        is_view_enabled:
          type: boolean
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        category_ids:
          type: array
          items:
            type: string
        sku:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateProductOutput:
      type: object
      properties:
        product:
          $ref: '#/components/schemas/ProductDto'
      additionalProperties: false
    UpdateProductOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateProductOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateProductVariantInput:
      required:
        - attributes
        - descriptions
        - id
        - images
        - names
        - position
        - prices
        - product_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - store_id
      type: object
      properties:
        id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        store_id:
          maxLength: 128
          minLength: 1
          type: string
        product_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        sku:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        url:
          maxLength: 128
          minLength: 1
          type: string
          nullable: true
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
        position:
          type: integer
          format: int32
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariantAttribute'
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Description'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ImageDto'
        platform_data:
          $ref: '#/components/schemas/PlatformData'
      additionalProperties: false
    UpdateProductVariantOutput:
      type: object
      properties:
        product_variant:
          $ref: '#/components/schemas/ProductVariantDto'
      additionalProperties: false
    UpdateProductVariantOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateProductVariantOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateShopifyCartInput:
      required:
        - checkout_url
        - conversion_status
        - original_cart_status
        - shopify_cart_token
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_staff_team_ids
        - sleekflow_user_profile_id
        - store_id
        - subtotal_price
        - target_cart_status
        - total_price
        - total_tax
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_user_profile_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        original_cart_status:
          minLength: 1
          type: string
        shopify_cart_token:
          minLength: 1
          type: string
        target_cart_status:
          minLength: 1
          type: string
        cart_discount:
          $ref: '#/components/schemas/Discount'
        checkout_url:
          minLength: 1
          type: string
        conversion_status:
          minLength: 1
          type: string
        total_price:
          maximum: 1000000000
          minimum: 1
          type: number
          format: decimal
        total_tax:
          maximum: 1000000000
          minimum: 1
          type: number
          format: decimal
        subtotal_price:
          maximum: 1000000000
          minimum: 1
          type: number
          format: decimal
        currency_iso_code:
          type: string
          nullable: true
        shopify_customer_id:
          type: string
          nullable: true
        abandoned_date:
          type: string
          format: date-time
          nullable: true
        recovered_date:
          type: string
          format: date-time
          nullable: true
        sleekflow_staff_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          maxLength: 128
          minLength: 1
          type: array
          items:
            type: string
      additionalProperties: false
    UpdateShopifyCartOutput:
      type: object
      properties:
        shopify_cart:
          $ref: '#/components/schemas/ShopifyCartDto'
      additionalProperties: false
    UpdateShopifyCartOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateShopifyCartOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateShopifyStoreIntegrationExternalConfigInput:
      required:
        - sleekflow_company_id
        - store_id
        - store_integration_external_config
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        store_id:
          minLength: 1
          type: string
        store_integration_external_config:
          $ref: '#/components/schemas/StoreIntegrationExternalConfigInput'
      additionalProperties: false
    UpdateShopifyStoreIntegrationExternalConfigOutput:
      type: object
      properties:
        shopify_store_integration_external_config:
          $ref: '#/components/schemas/ShopifyStoreIntegrationExternalConfig'
      additionalProperties: false
    UpdateShopifyStoreIntegrationExternalConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateShopifyStoreIntegrationExternalConfigOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateStoreInput:
      required:
        - currencies
        - descriptions
        - id
        - is_payment_enabled
        - is_view_enabled
        - languages
        - metadata
        - names
        - sleekflow_company_id
        - sleekflow_staff_id
        - template_dict
      type: object
      properties:
        id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_id:
          maxLength: 128
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        names:
          maxItems: 4
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          maxItems: 16
          type: array
          items:
            $ref: '#/components/schemas/Description'
        is_view_enabled:
          type: boolean
        is_payment_enabled:
          type: boolean
        languages:
          maxItems: 4
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/LanguageInputDto'
        currencies:
          maxItems: 4
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/CurrencyInputDto'
        template_dict:
          $ref: '#/components/schemas/StoreTemplateDict'
        metadata:
          type: object
          additionalProperties:
            nullable: true
        store_integration_external_config_input:
          $ref: '#/components/schemas/StoreIntegrationExternalConfigInput'
        subscription_status:
          $ref: '#/components/schemas/StoreSubscriptionStatus'
      additionalProperties: false
    UpdateStoreOutput:
      type: object
      properties:
        store:
          $ref: '#/components/schemas/StoreDto'
      additionalProperties: false
    UpdateStoreOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateStoreOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UploadBlobDto:
      type: object
      properties:
        blob_id:
          type: string
          nullable: true
        blob_url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
        blob_name:
          type: string
          nullable: true
      additionalProperties: false
    UserProfile:
      type: object
      properties:
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
      additionalProperties: false
    VerifyCustomCatalogCsvInput:
      required:
        - blob_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
      additionalProperties: false
    VerifyCustomCatalogCsvOutput:
      type: object
      properties:
        is_within_limitations:
          type: boolean
        csv_records:
          type: array
          items:
            $ref: '#/components/schemas/CustomCatalogCsvRecord'
          nullable: true
        limitations:
          type: array
          items:
            $ref: '#/components/schemas/CustomCatalogLimitation'
          nullable: true
      additionalProperties: false
    VerifyCustomCatalogCsvOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/VerifyCustomCatalogCsvOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false