﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IScheduledTriggerConditionsCheckStepExecutor : IStepExecutor
{
}

public class ScheduledTriggerConditionsCheckStepExecutor
    : GeneralStepExecutor<CallStep<ScheduledTriggerConditionsCheckStepArgs>>,
        IScheduledTriggerConditionsCheckStepExecutor,
        IScopedService
{
    private readonly IWorkflowRuntimeService _workflowRuntimeService;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IStateAggregator _stateAggregator;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IWorkflowExecutionService _workflowExecutionService;

    public ScheduledTriggerConditionsCheckStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IStateAggregator stateAggregator,
        IFlowHubConfigService flowHubConfigService,
        IWorkflowExecutionService workflowExecutionService)
        : base(
            workflowStepLocator,
            workflowRuntimeService,
            serviceProvider)
    {
        _workflowRuntimeService = workflowRuntimeService;
        _stateEvaluator = stateEvaluator;
        _stateAggregator = stateAggregator;
        _flowHubConfigService = flowHubConfigService;
        _workflowExecutionService = workflowExecutionService;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        await onActivatedAsync(state, StepExecutionStatuses.Complete);
    }

    public override async Task<Step?> OnStepCompleteAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries)
    {
        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(state.Identity.SleekflowCompanyId);

        if (!flowHubConfig.IsEnrolled)
        {
            throw new FlowHubDisabledException(
                state.Identity.SleekflowCompanyId,
                state.Id);
        }

        var callStep = step as CallStep<ScheduledTriggerConditionsCheckStepArgs> ?? throw new InvalidOperationException();

        var isConditionFulfilled =
            _stateEvaluator.IsExpression(callStep.Args.ConditionsExpr)
            && (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ConditionsExpr)) is true or "true";

        if (!isConditionFulfilled)
        {
            throw new ScheduledWorkflowConditionNotFulfilledException(
                state.Id,
                callStep.Args.ConditionsExpr);
        }

        var monthlyWorkflowExecutionUsageInfo = await _workflowExecutionService.GetMonthlyWorkflowExecutionUsageInfoAsync(flowHubConfig);

        if (monthlyWorkflowExecutionUsageInfo.IsExceedExecutionLimit)
        {
            throw new FlowHubMonthlyWorkflowExecutionLimitExceededException(
                state.Identity.SleekflowCompanyId,
                state.Id);
        }

        await _stateAggregator.SetSysVarStringAsync(state, StateSystemVarNames.ScheduledEnrollmentStarted, "true");
        await _workflowRuntimeService.StartWorkflowAsync(state.Identity.SleekflowCompanyId, state.Id);

        return await base.OnStepCompleteAsync(
            workflow,
            state,
            step,
            stackEntries);
    }
}