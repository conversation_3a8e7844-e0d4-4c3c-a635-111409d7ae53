﻿using Serilog;
using Sleekflow;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;

#if SWAGGERGEN
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using Moq;
#endif

const string appName = "SleekflowEmailHub";

MvcModules.BuildLogger(appName);

try
{
    Log.Information("Starting web host");

    var builder = WebApplication.CreateBuilder(args);
    builder.Host.UseSerilog();
    builder.Services.AddHttpContextAccessor();

    MvcModules.BuildHealthCheck(builder.Services);
    MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
#if SWAGGERGEN
    MvcModules.BuildApiBehaviors(builder, list =>
    {

        list.AddRange(new List<OpenApiServer>()
        {
            new OpenApiServer()
            {
                Description = "Local",
                Url = $"https://localhost:7099",
            },
            new OpenApiServer()
            {
                Description = "Dev Apigw",
                Url = $"https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/email-hub",
            },
            new OpenApiServer()
            {
                Description = "Prod Apigw",
                Url = $"https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/email-hub",
            }
        });
    });
#else
    MvcModules.BuildApiBehaviors(builder);
#endif
    Modules.BuildHttpClients(builder.Services);
    Modules.BuildConfigs(builder.Services);
    Modules.BuildServices(builder.Services);
    Modules.BuildTriggers(builder.Services);
    Modules.BuildServiceBusServices(builder.Services);
    Modules.BuildCacheServices(builder.Services);
    Modules.BuildDbServices(builder.Services);
    MvcModules.BuildFuncServices(builder.Services, appName);
    EmailHubModules.BuildEmailHubDbServices(builder.Services);

    var app = builder.Build();

    // app.UseHttpsRedirection();
    app.UseAuthorization();
    app.MapControllers();
    HealthCheckMapping.MapHealthChecks(app);

#if SWAGGERGEN
    app.UseSwagger();
    app.UseSwaggerUI(
        options =>
        {
            var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

            foreach (var description in provider.ApiVersionDescriptions)
            {
                options.SwaggerEndpoint(
                    $"/swagger/{description.GroupName}/swagger.json",
                    description.GroupName.ToUpperInvariant());
            }
        });
#endif

    ThreadPool.SetMinThreads(128, 128);
    ThreadPool.SetMaxThreads(512, 512);

    app.Run();

    return 0;
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");
    return 1;
}
finally
{
    Log.CloseAndFlush();
}