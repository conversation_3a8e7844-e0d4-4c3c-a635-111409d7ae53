﻿using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.WorkflowGroups;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.WorkflowGroups;

public interface IWorkflowGroupService
{
    Task<WorkflowGroup?> GetOrDefaultAsync(
        string sleekflowCompanyId,
        string workflowGroupId);

    Task<List<WorkflowGroup>> GetWorkflowGroupsAsync(
        string sleekflowCompanyId);

    Task<WorkflowGroup> CreateWorkflowGroupAsync(
        WorkflowGroup workflowGroup);

    Task<WorkflowGroup> UpdateWorkflowGroupAsync(
        string sleekflowCompanyId,
        string workflowGroupId,
        string workflowGroupName,
        AuditEntity.SleekflowStaff updatedBy);

    Task DeleteWorkflowGroupAsync(
        string sleekflowCompanyId,
        string workflowGroupId);
}

public class WorkflowGroupService : IWorkflowGroupService, IScopedService
{
    private readonly IWorkflowGroupRepository _workflowGroupRepository;
    private readonly IBus _bus;

    public WorkflowGroupService(
        IWorkflowGroupRepository workflowGroupRepository,
        IBus bus)
    {
        _workflowGroupRepository = workflowGroupRepository;
        _bus = bus;
    }

    public Task<WorkflowGroup?> GetOrDefaultAsync(string sleekflowCompanyId, string workflowGroupId)
    {
        return _workflowGroupRepository.GetOrDefaultAsync(
            workflowGroupId,
            sleekflowCompanyId);
    }

    public Task<List<WorkflowGroup>> GetWorkflowGroupsAsync(
        string sleekflowCompanyId)
    {
        return _workflowGroupRepository.GetObjectsAsync(
            group =>
                group.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<WorkflowGroup> CreateWorkflowGroupAsync(
        WorkflowGroup workflowGroup)
    {
        var existingWorkflowGroup = await GetWorkflowGroupOrDefaultByNameAsync(
            workflowGroup.SleekflowCompanyId,
            workflowGroup.Name);

        if (existingWorkflowGroup is not null)
        {
            throw new SfWorkflowGroupDuplicateNameException(
                existingWorkflowGroup.Id,
                workflowGroup.Name);
        }

        var createdWorkflowGroup = await _workflowGroupRepository.CreateAndGetAsync(
            workflowGroup,
            workflowGroup.SleekflowCompanyId);

        return createdWorkflowGroup;
    }

    public async Task<WorkflowGroup> UpdateWorkflowGroupAsync(
        string sleekflowCompanyId,
        string workflowGroupId,
        string workflowGroupName,
        AuditEntity.SleekflowStaff updatedBy)
    {
        var workflowGroup = await _workflowGroupRepository.GetOrDefaultAsync(
            workflowGroupId,
            partitionKey: sleekflowCompanyId);

        if (workflowGroup is null)
        {
            throw new SfNotFoundObjectException(
                workflowGroupId,
                partitionKey: sleekflowCompanyId);
        }

        var existingWorkflowGroup = await GetWorkflowGroupOrDefaultByNameAsync(
            sleekflowCompanyId,
            workflowGroupName);

        if (existingWorkflowGroup is not null
            && existingWorkflowGroup.Id != workflowGroupId)
        {
            throw new SfWorkflowGroupDuplicateNameException(
                existingWorkflowGroup.Id,
                existingWorkflowGroup.Name);
        }

        var updatedWorkflowGroup = await _workflowGroupRepository.PatchAndGetAsync(
            workflowGroupId,
            sleekflowCompanyId,
            new List<PatchOperation>()
            {
                PatchOperation.Set("/name", workflowGroupName),
                PatchOperation.Set($"/{IHasUpdatedBy.PropertyNameUpdatedBy}", updatedBy),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTime.UtcNow)
            });

        return updatedWorkflowGroup;
    }

    public async Task DeleteWorkflowGroupAsync(
        string sleekflowCompanyId,
        string workflowGroupId)
    {
        var workflowGroup = await _workflowGroupRepository.GetOrDefaultAsync(
            workflowGroupId,
            partitionKey: sleekflowCompanyId);

        if (workflowGroup is null)
        {
            throw new SfNotFoundObjectException(
                workflowGroupId,
                partitionKey: sleekflowCompanyId);
        }

        await _workflowGroupRepository.DeleteAsync(
            workflowGroupId,
            partitionKey: sleekflowCompanyId);

        await _bus.Publish(
            new OnWorkflowGroupDeletedEvent(
                sleekflowCompanyId,
                workflowGroupId));
    }

    private async Task<WorkflowGroup?> GetWorkflowGroupOrDefaultByNameAsync(
        string sleekflowCompanyId,
        string workflowGroupName)
    {
        var workflowGroups = await _workflowGroupRepository.GetObjectsAsync(
            new QueryDefinition(
                    """
                    SELECT *
                    FROM %%CONTAINER_NAME%% r
                    WHERE r.sleekflow_company_id = @sleekflow_company_id AND STRINGEQUALS(r.name, @workflow_group_name, false)
                    ORDER BY r.created_at
                    OFFSET 0 LIMIT 1
                    """)
                .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
                .WithParameter("@workflow_group_name", workflowGroupName));

        return workflowGroups.FirstOrDefault();
    }
}