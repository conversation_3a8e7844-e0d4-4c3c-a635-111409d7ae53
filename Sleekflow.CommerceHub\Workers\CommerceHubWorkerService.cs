using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Workers;

namespace Sleekflow.CommerceHub.Workers;

public interface ICommerceHubWorkerService
{
    Task<DurablePayload> ProcessCustomCatalogCsvAsync(
        string sleekflowCompanyId,
        string id);

    Task<DurablePayload> StartLoopThroughAndEnrollVtexOrdersToFlowHubAsync(
        string sleekflowCompanyId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string vtexAuthenticationId,
        VtexGetOrdersSearchCondition condition);
}

public class CommerceHubWorkerService : ICommerceHubWorkerService, ISingletonService
{
    private readonly IWorkerService _workerService;
    private readonly HttpClient _httpClient;
    private readonly IWorkerConfig _workerConfig;

    public CommerceHubWorkerService(
        IWorkerService workerService,
        IHttpClientFactory httpClientFactory,
        IWorkerConfig workerConfig)
    {
        _workerService = workerService;
        _workerConfig = workerConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<DurablePayload> ProcessCustomCatalogCsvAsync(
        string sleekflowCompanyId,
        string id)
    {
        var (_, _, output) = await _workerService.PostAsync<DurablePayload>(
            _httpClient,
            JsonConvert.SerializeObject(
                new ProcessCustomCatalogCsvInput(
                    sleekflowCompanyId,
                    id)),
            _workerConfig.WorkerHostname + "/api/ProcessCustomCatalogCsv");

        return output.Data!;
    }

    public async Task<DurablePayload> StartLoopThroughAndEnrollVtexOrdersToFlowHubAsync(
        string sleekflowCompanyId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string vtexAuthenticationId,
        VtexGetOrdersSearchCondition condition)
    {
        var (_, _, output) = await _workerService.PostAsync<DurablePayload>(
            _httpClient,
            JsonConvert.SerializeObject(
                new LoopThroughAndEnrollVtexOrdersToFlowHubInput(
                    sleekflowCompanyId,
                    flowHubWorkflowId,
                    flowHubWorkflowVersionedId,
                    vtexAuthenticationId,
                    condition)),
            _workerConfig.WorkerHostname + "/api/LoopThroughAndEnrollVtexOrdersToFlowHub");

        return output.Data!;
    }
}