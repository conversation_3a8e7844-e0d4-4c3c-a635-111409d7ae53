﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class CreateObject : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public CreateObject(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class CreateObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public CreateObjectInput(
            string sleekflowCompanyId,
            Dictionary<string, object?> dict,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Dict = dict;
            EntityTypeName = entityTypeName;
        }
    }

    public class CreateObjectOutput
    {
        [JsonConstructor]
        public CreateObjectOutput(bool isAsyncOperation)
        {
            IsAsyncOperation = isAsyncOperation;
        }

        [JsonProperty("is_async_operation")]
        public bool IsAsyncOperation { get; set; }
    }

    public async Task<CreateObjectOutput> F(CreateObjectInput createObjectInput)
    {
        var authentication =
            await _salesforceAuthenticationService.GetAsync(createObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var getFieldsOutput =
            await _salesforceObjectService.GetFieldsAsync(authentication, createObjectInput.EntityTypeName);
        var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();

        var dict = createObjectInput.Dict
            .Where(e => updatableFieldNames.Contains(e.Key))
            .ToDictionary(e => e.Key, e => e.Value);

        await _salesforceObjectService.CreateAsync(
            authentication,
            dict,
            createObjectInput.EntityTypeName);

        return new CreateObjectOutput(false);
    }
}