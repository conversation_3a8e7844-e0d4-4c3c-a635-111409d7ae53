using MassTransit;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiWabaLowBalanceEventConsumerDefinition : ConsumerDefinition<OnCloudApiWabaLowBalanceEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiWabaLowBalanceEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiWabaLowBalanceEventConsumer : IConsumer<OnCloudApiWabaLowBalanceEvent>
{
    private readonly ILogger<OnCloudApiWabaLowBalanceEventConsumer> _logger;

    public OnCloudApiWabaLowBalanceEventConsumer(
        ILogger<OnCloudApiWabaLowBalanceEventConsumer> logger)
    {
        _logger = logger;
    }

    public Task Consume(ConsumeContext<OnCloudApiWabaLowBalanceEvent> context)
    {
        var onCloudApiWabaLowBalanceEvent = context.Message;
        var facebookWabaId = onCloudApiWabaLowBalanceEvent.FacebookWabaId;
        var facebookBusinessId = onCloudApiWabaLowBalanceEvent.FacebookBusinessId;

        _logger.LogWarning(
            "Business {FacebookBusinessId} Waba {FacebookWabaId} low balance",
            facebookBusinessId,
            facebookWabaId);

        return Task.CompletedTask;
    }
}