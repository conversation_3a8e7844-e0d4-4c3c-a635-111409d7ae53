using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Integrator.Dynamics365.Objects;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Internals;

[TriggerGroup("Internals")]
public class SubscriptionsCheckBatch : ITrigger
{
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;
    private readonly IDynamics365ObjectService _dynamics365ObjectService;
    private readonly IBus _bus;
    private readonly ILogger<SubscriptionsCheckBatch> _logger;

    public SubscriptionsCheckBatch(
        IDynamics365AuthenticationService dynamics365AuthenticationService,
        IDynamics365ObjectService dynamics365ObjectService,
        IBus bus,
        ILogger<SubscriptionsCheckBatch> logger)
    {
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
        _dynamics365ObjectService = dynamics365ObjectService;
        _bus = bus;
        _logger = logger;
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public Dynamics365Subscription Subscription { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            Dynamics365Subscription subscription,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters,
            DateTimeOffset lastObjectModificationTime,
            string? nextRecordsUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
            LastObjectModificationTime = lastObjectModificationTime;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_last_object_modification_time")]
        public DateTimeOffset NextLastObjectModificationTime { get; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            DateTimeOffset nextLastObjectModificationTime,
            string? nextRecordsUrl)
        {
            Count = count;
            NextLastObjectModificationTime = nextLastObjectModificationTime;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public async Task<SubscriptionsCheckBatchOutput> F(
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var subscription = subscriptionsCheckBatchInput.Subscription;
        var entityTypeName = subscriptionsCheckBatchInput.EntityTypeName;
        var lastObjectModificationTime = subscriptionsCheckBatchInput.LastObjectModificationTime;
        var after = subscriptionsCheckBatchInput.NextRecordsUrl;

        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(subscriptionsCheckBatchInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime);

        var (objects, nextRecordsUrl) = await _dynamics365ObjectService.GetRecentlyUpdatedObjectsAsync(
            authentication,
            entityTypeName,
            lastObjectModificationTime,
            subscriptionsCheckBatchInput.FilterGroups,
            subscriptionsCheckBatchInput.FieldFilters,
            after);

        _logger.LogInformation(
            "Ended sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}, count {Count}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime,
            objects.Count);

        var nextLastObjectModificationTime =
            subscription.LastObjectModificationTime ?? subscription.LastExecutionStartTime;
        var events = new List<OnObjectOperationEvent>();
        foreach (var dict in objects)
        {
            var modifiedon = dict["modifiedon"] as DateTimeOffset?;
            if (modifiedon == null)
            {
                _logger.LogError(
                    "The modifiedon is invalid. modifiedon {Modifiedon}",
                    modifiedon);

                continue;
            }

            nextLastObjectModificationTime = (DateTimeOffset) modifiedon > nextLastObjectModificationTime
                ? (DateTimeOffset) modifiedon
                : nextLastObjectModificationTime;

            var providerObjectId = _dynamics365ObjectService.ResolveObjectId(dict, entityTypeName);
            if (dict.ContainsKey("id") == false)
            {
                dict["id"] = providerObjectId;
            }

            var onObjectOperationEvent = new OnObjectOperationEvent(
                dict,
                OnObjectOperationEvent.OperationCreateOrUpdateObject,
                "d365",
                subscriptionsCheckBatchInput.SleekflowCompanyId,
                providerObjectId,
                subscriptionsCheckBatchInput.EntityTypeName,
                null);

            events.Add(onObjectOperationEvent);
        }

        foreach (var onObjectOperationEvents in events.Chunk(30))
        {
            await _bus.PublishBatch(
                onObjectOperationEvents,
                context => { context.ConversationId = Guid.Parse(subscriptionsCheckBatchInput.SleekflowCompanyId); });
        }

        _logger.LogInformation(
            "Flushed sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}, count {Count}, nextLastObjectModificationTime {NextLastObjectModificationTime}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime,
            objects.Count,
            nextLastObjectModificationTime);

        return new SubscriptionsCheckBatchOutput(objects.Count, nextLastObjectModificationTime, nextRecordsUrl);
    }
}