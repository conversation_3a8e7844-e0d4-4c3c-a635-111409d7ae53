using System.Collections.Concurrent;
using Azure;
using Azure.AI.DocumentIntelligence;
using HtmlAgilityPack;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.AzureOpenAI;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface IWordKnowledgeSource : IKnowledgeSource
{
}

public class WordKnowledgeSource : IWordKnowledgeSource, IScopedService
{
    private readonly ILogger<WordKnowledgeSource> _logger;
    private readonly Kernel _kernel;
    private readonly IAzureFormRecognizerConfig _azureFormRecognizerConfig;

    public WordKnowledgeSource(
        ILogger<WordKnowledgeSource> logger,
        Kernel kernel,
        IAzureFormRecognizerConfig azureFormRecognizerConfig)
    {
        _logger = logger;
        _kernel = kernel;
        _azureFormRecognizerConfig = azureFormRecognizerConfig;
    }

    public async Task<IKnowledgeSource.IngestionResult> Ingest(
        Stream blobStream,
        object? fileIngestionProgress)
    {
        // Initialize or use existing progress state
        WordFileIngestionProgress wordFileIngestionProgress;

        switch (fileIngestionProgress)
        {
            case null:
                // Initialize a new progress object if none is provided
                wordFileIngestionProgress = new WordFileIngestionProgress(false);
                break;
            case WordFileIngestionProgress progress:
                // Direct instance of WordFileIngestionProgress
                wordFileIngestionProgress = progress;
                break;
            default:
                try
                {
                    // Try to deserialize as JSON
                    var jsonString = JsonConvert.SerializeObject(fileIngestionProgress);
                    wordFileIngestionProgress = JsonConvert.DeserializeObject<WordFileIngestionProgress>(jsonString)
                                                ?? throw new Exception(
                                                    "Failed to deserialize fileIngestionProgress to WordFileIngestionProgress");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to interpret fileIngestionProgress as WordFileIngestionProgress");
                    throw new Exception("FileIngestionProgress could not be interpreted as WordFileIngestionProgress", ex);
                }

                break;
        }

        var credential = new AzureKeyCredential(_azureFormRecognizerConfig.AzureFormRecognizerKey);
        var client = new DocumentIntelligenceClient(
            new Uri(_azureFormRecognizerConfig.AzureFormRecognizerEndpoint),
            credential);

        var options = new AnalyzeDocumentOptions("prebuilt-layout", await BinaryData.FromStreamAsync(blobStream))
        {
            OutputContentFormat = DocumentContentFormat.Markdown
        };

        var operation = await client.AnalyzeDocumentAsync(
            WaitUntil.Started,
            options);

        var analyzeResponse = await operation.WaitForCompletionAsync();
        var content = analyzeResponse.Value.Content!;

        var result = await ReplaceTablesWithProcessedContentAsync(content);

        // Update progress
        wordFileIngestionProgress.IsIngestionCompleted = true;

        return new IKnowledgeSource.IngestionResult([result], wordFileIngestionProgress);
    }

    private async Task<string> ReplaceTablesWithProcessedContentAsync(string htmlContent)
    {
        // Method 1: Using HTML Agility Pack (Recommended)
        try
        {
            var doc = new HtmlDocument();
            doc.LoadHtml(htmlContent);

            var tables = doc.DocumentNode.SelectNodes("//table");
            if (tables != null)
            {
                var tableProcessingResults = new ConcurrentDictionary<HtmlNode, string>();
                var parallelOptions = new ParallelOptions
                {
                    MaxDegreeOfParallelism = 8
                };

                await Parallel.ForEachAsync(
                    tables,
                    parallelOptions,
                    async (table, cancellationToken) =>
                    {
                        var originalTableHtml = table.OuterHtml;
                        var processedContent = await TransformHtmlToTextAsync(originalTableHtml);
                        tableProcessingResults.TryAdd(table, processedContent);
                    });

                // Perform replacements sequentially after parallel processing
                foreach (var table in tables)
                {
                    if (tableProcessingResults.TryGetValue(table, out var processedContent))
                    {
                        // Ensure the node still exists and has a parent before replacing
                        if (table.ParentNode != null)
                        {
                            var textNode = HtmlNode.CreateNode(processedContent);
                            table.ParentNode.ReplaceChild(textNode, table);
                        }
                        else
                        {
                            // Handle cases where the node might have been removed or modified indirectly
                            _logger.LogWarning(
                                "Could not replace table as its ParentNode was null. Original table HTML: {OriginalTableHtml}",
                                table.OuterHtml);
                        }
                    }
                    else
                    {
                        // Log cases where processing might have failed for a specific table
                        _logger.LogWarning(
                            "Processed content not found for table. Original table HTML: {OriginalTableHtml}",
                            table.OuterHtml);
                    }
                }

                return doc.DocumentNode.OuterHtml;
            }

            return htmlContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process tables using HTML Agility Pack.");
            throw;
        }
    }

    private async Task<string> TransformHtmlToTextAsync(string htmlContent)
    {
        var chatCompletionService =
            _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_GPT_4o);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """"
            Transform the HTML table containing colspan and rowspan into human-readable paragraphs. Ensure the paragraphs are well-organized, engaging, and easy to understand without using bullet points or lists.
            Additional Instructions:"""
             - Determine the result language based on the language of given table.
             - Make sure the result covering the given table's structure and specific data points.
             - Each paragraph should clearly explain the table's information for optimal indexing and searching in ElasticSearch.

            Output language:
            - The output should use the same language as the HTML table content.
            """
            """");
        chatHistory.AddUserMessage(
        [
            new Microsoft.SemanticKernel.TextContent(
                "<table><tr><th>大廈名稱</th><th>樓層</th><th>單位</th><th>每個住宅物業的非結構的預製外牆的總面積 【平方米】</th></tr><tr><td rowspan=\"12\">Charlot Tower 1B Charlot 第 1B 座</td><td rowspan=\"9\">29/F-33/F 29 樓至 33 樓</td><td>Al</td><td>0.929</td></tr><tr><td>A2</td><td>0.545</td></tr><tr><td>B</td><td>0.814</td></tr><tr><td>C</td><td>1.074</td></tr><tr><td>D</td><td>0.632</td></tr><tr><td>E</td><td>0.755</td></tr><tr><td>F</td><td>0.776</td></tr><tr><td>G</td><td>0.626</td></tr><tr><td>H</td><td>0.779</td></tr><tr><td rowspan=\"3\">Penthouse Floor (35/F) 頂層 (35 樓)</td><td>AT</td><td>0.539</td></tr><tr><td>A2</td><td>0.000</td></tr><tr><td>A3</td><td>0.000</td></tr></table>")
        ]);
        chatHistory.AddAssistantMessage(
            """
            在 Charlot Tower 1B Charlot 第 1B 座大廈中，29/F-33/F 29 樓至 33 樓的各個單位的非結構的預製外牆的總面積如下：單位 A1 的面積為 0.929 平方米；單位 A2 的面積為 0.545 平方米；單位 B 的面積為 0.814 平方米；單位 C 的面積為 1.074 平方米；單位 D 的面積為 0.632 平方米；單位 E 的面積為 0.755 平方米；單位 F 的面積為 0.776 平方米；單位 G 的面積為 0.626 平方米；單位 H 的面積為 0.779 平方米。
            此外，在 Charlot Tower 1B Charlot 第 1B 座大廈的 Penthouse Floor (35/F) 頂層 (35 樓)，非結構的預製外牆的總面積如下：單位 AT 的面積為 0.539 平方米；單位 A2 的面積為 0.000 平方米；單位 A3 的面積為 0.000 平方米。
            """);
        chatHistory.AddUserMessage(htmlContent);

        var completeOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            new AzureOpenAIPromptExecutionSettings()
            {
                Temperature = 0.1f
            },
            _kernel);

        var content = completeOutput.Content;
        if (content == null)
        {
            throw new Exception("TransformHtmlToText completion failed.");
        }

        return content;
    }
}