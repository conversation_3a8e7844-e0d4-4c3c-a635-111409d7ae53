using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Configs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CommerceHub;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.CustomCatalogs.Configs;

public interface ICustomCatalogConfigService
{
    Task CreateCustomCatalogConfigAsync(
        string sleekflowCompanyId,
        string type,
        int count,
        DateTimeOffset periodStart,
        DateTimeOffset periodEnd,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<CustomCatalogConfig> GetCustomCatalogConfigAsync(string id, string sleekflowCompanyId);

    Task<int> GetCustomCatalogConfigLimitAsync(string sleekflowCompanyId, string type);

    Task<List<CustomCatalogConfig>> GetCustomCatalogConfigsAsync(string sleekflowCompanyId);

    Task<int> UpsertCustomCatalogConfigAsync(
        CustomCatalogConfig customCatalogConfig,
        AuditEntity.SleekflowStaff sleekflowStaff);
}

public class CustomCatalogConfigService : ICustomCatalogConfigService, IScopedService
{
    private readonly IIdService _idService;
    private readonly ICustomCatalogConfigRepository _customCatalogConfigRepository;

    public CustomCatalogConfigService(
        IIdService idService,
        ICustomCatalogConfigRepository customCatalogConfigRepository)
    {
        _idService = idService;
        _customCatalogConfigRepository = customCatalogConfigRepository;
    }

    public async Task CreateCustomCatalogConfigAsync(
        string sleekflowCompanyId,
        string type,
        int count,
        DateTimeOffset periodStart,
        DateTimeOffset periodEnd,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var utcNow = DateTimeOffset.UtcNow;
        await _customCatalogConfigRepository.CreateAsync(
            new CustomCatalogConfig(
                _idService.GetId(SysTypeNames.CustomCatalogConfig),
                sleekflowCompanyId,
                type,
                count,
                utcNow,
                utcNow,
                periodStart,
                periodEnd,
                sleekflowStaff,
                sleekflowStaff),
            sleekflowCompanyId);
    }

    public async Task<CustomCatalogConfig> GetCustomCatalogConfigAsync(string id, string sleekflowCompanyId)
    {
        return await _customCatalogConfigRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<int> GetCustomCatalogConfigLimitAsync(string sleekflowCompanyId, string type)
    {
        var utcNow = DateTimeOffset.UtcNow;
        var customCatalogConfigs = await _customCatalogConfigRepository
            .GetObjectsAsync(
                c =>
                    c.SleekflowCompanyId == sleekflowCompanyId
                    && c.Type == type && c.PeriodStart < utcNow && c.PeriodEnd > utcNow);
        var customCatalogConfigsSum = customCatalogConfigs.Select(c => c.Count).Sum();
        return type switch
        {
            SysTypeNames.Product => 500 + customCatalogConfigsSum,
            SysTypeNames.ProductVariant => 5000 + customCatalogConfigsSum,
            _ => throw new SfUnsupportedDiscountTypeException(type)
        };
    }

    public async Task<List<CustomCatalogConfig>> GetCustomCatalogConfigsAsync(string sleekflowCompanyId)
    {
        return await _customCatalogConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<int> UpsertCustomCatalogConfigAsync(
        CustomCatalogConfig customCatalogConfig,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        customCatalogConfig.UpdatedBy = sleekflowStaff;
        return await _customCatalogConfigRepository.UpsertAsync(
            customCatalogConfig,
            customCatalogConfig.SleekflowCompanyId);
    }
}