using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.WebsiteDocumentChunk)]
public class WebsiteDocumentChunk : Chunk
{
    public const string PropertyNameDocumentId = "document_id";
    public const string PropertyNamePageUrl = "page_url";

    [JsonProperty(PropertyNameDocumentId)]
    public string DocumentId { get; set; }

    [JsonProperty(PropertyNamePageUrl)]
    public string PageUrl { get; set; }

    [JsonConstructor]
    public WebsiteDocumentChunk(
        string id,
        string sleekflowCompanyId,
        string documentId,
        string pageUrl,
        string content,
        string contentEn,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<Category> categories,
        Dictionary<string, object?> metadata)
        : base(
            id,
            SysTypeNames.WebsiteDocumentChunk,
            sleekflowCompanyId,
            content,
            contentEn,
            createdAt,
            updatedAt,
            categories,
            metadata)
    {
        DocumentId = documentId;
        PageUrl = pageUrl;
    }
}