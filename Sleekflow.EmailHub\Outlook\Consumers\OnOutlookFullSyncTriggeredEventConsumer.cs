using MassTransit;
using Sleekflow.EmailHub.Models.Outlook.Events;
using Sleekflow.EmailHub.Outlook.Communications;
using Sleekflow.Locks;

namespace Sleekflow.EmailHub.Outlook.Consumers;

public class OnOutlookFullSyncTriggeredEventConsumerDefinition : ConsumerDefinition<OnOutlookFullSyncTriggeredEventConsumer>
{
    public const int LockDuration = 30;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnOutlookFullSyncTriggeredEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(30);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnOutlookFullSyncTriggeredEventConsumer : IConsumer<OnOutlookSyncAllTriggeredEvent>
{
    private readonly ILockService _lockService;
    private readonly IOutlookCommunicationService _outlookCommunicationService;
    private readonly ILogger<OnOutlookFullSyncTriggeredEventConsumer> _logger;

    public OnOutlookFullSyncTriggeredEventConsumer(
        ILockService lockService,
        IOutlookCommunicationService outlookCommunicationService,
        ILogger<OnOutlookFullSyncTriggeredEventConsumer> logger)
    {
        _lockService = lockService;
        _outlookCommunicationService = outlookCommunicationService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnOutlookSyncAllTriggeredEvent> context)
    {
        var outlookFullSyncEvent = context.Message;
        var cancellationToken = context.CancellationToken;

        var @lock = await _lockService.LockAsync(
            new[]
            {
                $"{outlookFullSyncEvent.EmailAddress}",
                "OutlookFullSyncLock"
            },
            TimeSpan.FromSeconds(
                60 * OnOutlookFullSyncTriggeredEventConsumerDefinition.LockDuration),
            cancellationToken);

        if (@lock is null)
        {
            return;
        }

        try
        {
            await _outlookCommunicationService.SyncAllEmailsAsync(
                outlookFullSyncEvent.EmailAddress,
                cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogInformation(
                "[OnOutlookFullSyncTriggeredEventConsumer]: encounter error while full sync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}: ex: {e}",
                outlookFullSyncEvent.EmailAddress,
                outlookFullSyncEvent.SleekflowCompanyId,
                e);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}