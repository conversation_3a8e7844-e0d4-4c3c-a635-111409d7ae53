using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class SchemafulObjectCreatedEventRequest
{
    public string SleekflowCompanyId { get; set; }

    public string SchemafulObjectId { get; set; }

    public string SchemaId { get; set; }

    public string PrimaryPropertyValue { get; set; }

    public string SleekflowUserProfileId { get; set; }

    public Dictionary<string, object?> PropertyValues { get; set; }

    public DateTimeOffset CreatedAt { get; set; }

    public string? SleekflowStaffId { get; set; }

    public List<string>? SleekflowStaffTeamIds { get; set; }

    [JsonConstructor]
    public SchemafulObjectCreatedEventRequest(
        string sleekflowCompanyId,
        string schemafulObjectId,
        string schemaId,
        string primaryPropertyValue,
        string sleekflowUserProfileId,
        Dictionary<string, object?> propertyValues,
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemafulObjectId = schemafulObjectId;
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        SleekflowUserProfileId = sleekflowUserProfileId;
        PropertyValues = propertyValues;
        CreatedAt = createdAt;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
    }
}