﻿namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

using Newtonsoft.Json;

public class ApifyWebhook
{
    [JsonProperty(PropertyName = "userId")]
    public string UserId { get; set; }

    [JsonProperty(PropertyName = "createdAt")]
    public DateTime CreatedAt { get; set; }

    [JsonProperty(PropertyName = "eventType")]
    public string EventType { get; set; }

    [JsonProperty(PropertyName = "eventData")]
    public ApifyWebhookData Data { get; set; }

    [JsonProperty(PropertyName = "resource")]
    public ApifyWebhookResource Resource { get; set; }

    [JsonConstructor]
    public ApifyWebhook(
        string userId,
        DateTime createdAt,
        string eventType,
        ApifyWebhookData data,
        ApifyWebhookResource resource)
    {
        UserId = userId;
        CreatedAt = createdAt;
        EventType = eventType;
        Data = data;
        Resource = resource;
    }
}

public class ApifyWebhookData
{
    [JsonProperty(PropertyName = "actorId")]
    public string ActorId { get; set; }

    [JsonProperty(PropertyName = "actorTaskId")]
    public string ActorTaskId { get; set; }

    [JsonProperty(PropertyName = "actorRunId")]
    public string ActorRunId { get; set; }

    [JsonConstructor]
    public ApifyWebhookData(
        string actorId,
        string actorTaskId,
        string actorRunId)
    {
        ActorId = actorId;
        ActorTaskId = actorTaskId;
        ActorRunId = actorRunId;
    }
}

public class ApifyWebhookResource
{
    [JsonProperty(PropertyName = "id")]
    public string Id { get; set; }

    [JsonProperty(PropertyName = "actId")]
    public string ActId { get; set; }

    [JsonProperty(PropertyName = "userId")]
    public string UserId { get; set; }

    [JsonProperty(PropertyName = "actorTaskId")]
    public string ActorTaskId { get; set; }

    [JsonProperty(PropertyName = "startedAt")]
    public DateTime? StartedAt { get; set; }

    [JsonProperty(PropertyName = "finishedAt")]
    public DateTime? FinishedAt { get; set; }

    [JsonProperty(PropertyName = "status")]
    public string Status { get; set; }

    [JsonProperty(PropertyName = "statusMessage")]
    public string StatusMessage { get; set; }

    [JsonProperty(PropertyName = "isStatusMessageTerminal")]
    public bool IsStatusMessageTerminal { get; set; }

    [JsonProperty(PropertyName = "stats")]
    public ApifyWebhookResourceStats Stats { get; set; }

    [JsonProperty(PropertyName = "options")]
    public ApifyWebhookResourceOptions Options { get; set; }

    [JsonProperty(PropertyName = "defaultKeyValueStoreId")]
    public string DefaultKeyValueStoreId { get; set; }

    [JsonProperty(PropertyName = "defaultDatasetId")]
    public string DefaultDatasetId { get; set; }

    [JsonProperty(PropertyName = "defaultRequestQueueId")]
    public string DefaultRequestQueueId { get; set; }

    [JsonConstructor]
    public ApifyWebhookResource(
        string id,
        string actId,
        string userId,
        string actorTaskId,
        DateTime? startedAt,
        DateTime? finishedAt,
        string status,
        string statusMessage,
        bool isStatusMessageTerminal,
        ApifyWebhookResourceStats stats,
        ApifyWebhookResourceOptions options,
        string defaultKeyValueStoreId,
        string defaultDatasetId,
        string defaultRequestQueueId)
    {
        Id = id;
        ActId = actId;
        UserId = userId;
        ActorTaskId = actorTaskId;
        StartedAt = startedAt;
        FinishedAt = finishedAt;
        Status = status;
        StatusMessage = statusMessage;
        IsStatusMessageTerminal = isStatusMessageTerminal;
        Stats = stats;
        Options = options;
        DefaultKeyValueStoreId = defaultKeyValueStoreId;
        DefaultDatasetId = defaultDatasetId;
        DefaultRequestQueueId = defaultRequestQueueId;
    }
}

public class ApifyWebhookResourceStats
{
    [JsonProperty(PropertyName = "inputBodyLen")]
    public int InputBodyLength { get; set; }

    [JsonProperty(PropertyName = "restartCount")]
    public int RestartCount { get; set; }

    [JsonProperty(PropertyName = "durationMillis")]
    public int DurationMillis { get; set; }

    [JsonProperty(PropertyName = "resurrectCount")]
    public int ResurrectCount { get; set; }

    [JsonProperty(PropertyName = "runTimeSecs")]
    public double RunTimeSeconds { get; set; }

    [JsonConstructor]
    public ApifyWebhookResourceStats(
        int inputBodyLen,
        int restartCount,
        int durationMillis,
        int resurrectCount,
        double runTimeSecs)
    {
        InputBodyLength = inputBodyLen;
        RestartCount = restartCount;
        DurationMillis = durationMillis;
        ResurrectCount = resurrectCount;
        RunTimeSeconds = runTimeSecs;
    }
}

public class ApifyWebhookResourceOptions
{
    [JsonProperty(PropertyName = "build")]
    public string Build { get; set; }

    [JsonProperty(PropertyName = "timeoutSecs")]
    public int TimeoutSeconds { get; set; }

    [JsonProperty(PropertyName = "memoryMbytes")]
    public int MemoryMBytes { get; set; }

    [JsonProperty(PropertyName = "diskMbytes")]
    public int DiskMBytes { get; set; }

    [JsonConstructor]
    public ApifyWebhookResourceOptions(
        string build,
        int timeoutSecs,
        int memoryMbytes,
        int diskMbytes)
    {
        Build = build;
        TimeoutSeconds = timeoutSecs;
        MemoryMBytes = memoryMbytes;
        DiskMBytes = diskMbytes;
    }
}