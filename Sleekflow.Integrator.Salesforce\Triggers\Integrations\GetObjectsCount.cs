using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetObjectsCount : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public GetObjectsCount(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class GetObjectsCountInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonConstructor]
        public GetObjectsCountInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
        }
    }

    public class GetObjectsCountOutput
    {
        [JsonProperty("count")]
        [Required]
        public long Count { get; set; }

        [JsonConstructor]
        public GetObjectsCountOutput(
            long count)
        {
            Count = count;
        }
    }

    public async Task<GetObjectsCountOutput> F(
        GetObjectsCountInput getObjectsCountInput)
    {
        var authentication =
            await _salesforceAuthenticationService.GetAsync(getObjectsCountInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var count = await _salesforceObjectService.GetObjectsCountAsync(
            authentication,
            getObjectsCountInput.EntityTypeName,
            getObjectsCountInput.FilterGroups);

        return new GetObjectsCountOutput(count);
    }
}