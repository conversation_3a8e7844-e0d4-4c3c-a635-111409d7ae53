using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.ConversationUsages;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IMessagingHubDbResolver))]
[ContainerId(ContainerNames.BusinessBalanceTransactionLog)]
public class BusinessBalanceTransactionLog : Entity, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    [JsonProperty("facebook_waba_id")]
    public string? FacebookWabaId { get; set; }

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("unique_id")]
    public string UniqueId { get; set; }

    [JsonProperty("credit")]
    public Money Credit { get; set; }

    [JsonProperty("used")]
    public Money Used { get; set; }

    [JsonProperty("markup")]
    public Money Markup { get; set; }

    [JsonProperty("transaction_handling_fee")]
    public Money? TransactionHandlingFee { get; set; }

    [JsonProperty("transaction_type")]
    public string TransactionType { get; set; }

    [JsonProperty("is_calculated")]
    public bool IsCalculated { get; set; }

    [JsonProperty("calculation_status")]
    public string? CalculationStatus { get; set; }

    [JsonProperty("waba_top_up")]
    public WabaTopUpTransactionItem? WabaTopUp { get; set; }

    [JsonProperty("waba_conversation_usage")]
    public WabaConversationUsageTransactionItem? WabaConversationUsage { get; set; }

    [JsonProperty("markup_profile_snapshot")]
    public MarkupProfile? MarkupProfileSnapshot { get; set; }

    [JsonProperty("credit_transfer_from_to")]
    public CreditTransferFromTo? CreditTransferFromTo { get; set; }

    [JsonProperty("resynchronize_at_histories")]
    public List<DateTimeOffset>? ResynchronizeAtHistories { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public BusinessBalanceTransactionLog(
        string id,
        string? facebookWabaId,
        string facebookBusinessId,
        string uniqueId,
        Money credit,
        Money used,
        Money markup,
        Money? transactionHandlingFee,
        string transactionType,
        bool isCalculated,
        WabaTopUpTransactionItem? wabaTopUp,
        WabaConversationUsageTransactionItem? wabaConversationUsage,
        MarkupProfile? markupProfileSnapshot,
        CreditTransferFromTo? creditTransferFromTo,
        List<DateTimeOffset>? resynchronizeAtHistories,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string calculationStatus = BusinessBalanceTransactionLogCalculationStatus.Pending)
        : base(id, SysTypeNames.BusinessBalanceTransactionLog)
    {
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        UniqueId = uniqueId;
        Credit = credit;
        Used = used;
        Markup = markup;
        TransactionHandlingFee = transactionHandlingFee;
        TransactionType = transactionType;
        IsCalculated = isCalculated;
        CalculationStatus = calculationStatus;
        WabaTopUp = wabaTopUp;
        WabaConversationUsage = wabaConversationUsage;
        MarkupProfileSnapshot = markupProfileSnapshot;
        CreditTransferFromTo = creditTransferFromTo;
        ResynchronizeAtHistories = resynchronizeAtHistories;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public void SetCalculationStatusToSuccess()
    {
        IsCalculated = true;
        CalculationStatus = BusinessBalanceTransactionLogCalculationStatus.Success;
    }

    public void SetCalculationStatusToFailed()
    {
        IsCalculated = true;
        CalculationStatus = BusinessBalanceTransactionLogCalculationStatus.Failed;
    }
}