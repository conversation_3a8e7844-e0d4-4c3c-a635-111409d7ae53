using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class GetProductCatalogProductItem
    : ITrigger<
        GetProductCatalogProductItem.GetProductCatalogProductItemInput,
        GetProductCatalogProductItem.GetProductCatalogProductItemOutput>
{
    private readonly IProductCatalogService _productCatalogService;
    private readonly ILogger<GetProductCatalogProductItem> _logger;

    public GetProductCatalogProductItem(
        IProductCatalogService productCatalogService,
        ILogger<GetProductCatalogProductItem> logger)
    {
        _productCatalogService = productCatalogService;
        _logger = logger;
    }

    public class GetProductCatalogProductItemInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("facebook_product_catalog_id")]
        public string FacebookProductCatalogId { get; set; }

        [Required]
        [JsonProperty("facebook_retailer_id")]
        public string FacebookRetailerId { get; set; }

        [JsonConstructor]
        public GetProductCatalogProductItemInput(
            string sleekflowCompanyId,
            string wabaId,
            string facebookProductCatalogId,
            string facebookRetailerId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            FacebookProductCatalogId = facebookProductCatalogId;
            FacebookRetailerId = facebookRetailerId;
        }
    }

    public class GetProductCatalogProductItemOutput
    {
        [JsonProperty("product_item")]
        public ProductItemDto ProductItem { get; set; }

        [JsonConstructor]
        public GetProductCatalogProductItemOutput(ProductItemDto productItem)
        {
            ProductItem = productItem;
        }
    }

    public async Task<GetProductCatalogProductItemOutput> F(
        GetProductCatalogProductItemInput getProductCatalogProductItemInput)
    {
        _logger.LogInformation(
            "Getting phone number id {WabaId} product catalog id {ProductCatalogId} facebook retailer id {FacebookRetailerId}",
            getProductCatalogProductItemInput.WabaId,
            getProductCatalogProductItemInput.FacebookProductCatalogId,
            getProductCatalogProductItemInput.FacebookRetailerId);

        return new GetProductCatalogProductItemOutput(
            await _productCatalogService.GetProductItemByRetailerIdAsync(
                getProductCatalogProductItemInput.SleekflowCompanyId,
                getProductCatalogProductItemInput.WabaId,
                getProductCatalogProductItemInput.FacebookProductCatalogId,
                getProductCatalogProductItemInput.FacebookRetailerId));
    }
}