﻿using Microsoft.SemanticKernel;

namespace Sleekflow.IntelligentHub.Utils;

public static class AgentGroupChatUtils
{
    public static KernelFunction CreatePromptFunctionForStrategy(
        string template,
        IPromptTemplateFactory? templateFactory = null,
        string? name = null,
        params string[] safeParameterNames)
    {
        PromptTemplateConfig config =
            new (template)
            {
                Name = name ?? "StrategyPrompt",
                InputVariables = safeParameterNames.Select(
                    parameterName => new InputVariable
                    {
                        Name = parameterName, AllowDangerouslySetContent = true
                    }).ToList()
            };

        return KernelFunctionFactory.CreateFromPrompt(config, promptTemplateFactory: templateFactory);
    }
}