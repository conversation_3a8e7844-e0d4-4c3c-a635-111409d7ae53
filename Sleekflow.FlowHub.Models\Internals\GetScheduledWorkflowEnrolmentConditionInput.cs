﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;

public class GetScheduledWorkflowEnrolmentConditionInput
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    [Required]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public GetScheduledWorkflowEnrolmentConditionInput(string sleekflowCompanyId, string workflowVersionedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionedId = workflowVersionedId;
    }
}