using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class AutomationTriggeredLogData
{
    [JsonProperty("automation_name")]
    public string AutomationName { get; set; }

    [JsonProperty("automation_action_type")]
    public string AutomationActionType { get; set; }

    [JsonProperty("automation_action_log_id")]
    public long? AutomationActionLogId { get; set; }

    [JsonProperty("automation_status")]
    public string AutomationActionStatus { get; set; }

    [JsonProperty("automation_failed_error_message")]
    public string? AutomationErrorMessage { get; set; }

    [JsonConstructor]
    public AutomationTriggeredLogData(
        string automationName,
        string automationActionType,
        long? automationActionLogId,
        string automationActionStatus,
        string? automationErrorMessage)
    {
        AutomationName = automationName;
        AutomationActionType = automationActionType;
        AutomationActionLogId = automationActionLogId;
        AutomationActionStatus = automationActionStatus;
        AutomationErrorMessage = automationErrorMessage;
    }
}