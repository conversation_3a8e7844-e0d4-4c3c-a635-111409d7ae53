/**
 * Handler that will be called during the execution of a PostLogin flow.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
exports.onExecutePostLogin = async (event, api) => {
  /**
   * This custom scripts is update using pulumi.
   */
  const namespace = 'https://app.sleekflow.io';

  if (
    event.user == null
    || (event.user.app_metadata?.sleekflow_id == null && event.user.app_metadata?.tenanthub_user_id == null)
    || (event.user.app_metadata?.sleekflow_id === undefined && event.user.app_metadata?.tenanthub_user_id === undefined)) {
    api.access.deny('Internal Server Error.')
  }

  let signin_checked = event.request.body['ulp-keep-me-sign-in'];
  let login_user_id = event.user.app_metadata?.sleekflow_id;
  let tenanthub_user_id = event.user.app_metadata?.tenanthub_user_id;
  let is_rbac_enabled = event.user.app_metadata?.is_rbac_enabled;

  api.idToken.setCustomClaim(`${namespace}/email`, event.user.email);
  api.idToken.setCustomClaim(`${namespace}/roles`, JSON.stringify(event.user.app_metadata.roles));
  api.idToken.setCustomClaim(`${namespace}/user_id`, login_user_id);
  api.idToken.setCustomClaim(`${namespace}/user_name`, event.user.username);
  api.idToken.setCustomClaim(`${namespace}/name`, event.user.name);
  api.idToken.setCustomClaim(`${namespace}/connection`, event.connection.name);
  api.idToken.setCustomClaim(`${namespace}/connection_strategy`, event.connection.strategy);
  api.idToken.setCustomClaim(`${namespace}/tenanthub_user_id`, tenanthub_user_id);
  api.idToken.setCustomClaim(`${namespace}/is_keep_me_signin`, signin_checked? null: new Date());
  api.idToken.setCustomClaim(`${namespace}/is_rbac_enabled`, is_rbac_enabled);

  api.accessToken.setCustomClaim(`${namespace}/name`, event.user.name);
  api.accessToken.setCustomClaim(`${namespace}/email`, event.user.email);
  api.accessToken.setCustomClaim(`${namespace}/user_id`, login_user_id);
  api.accessToken.setCustomClaim(`${namespace}/user_name`, event.user.username);
  api.accessToken.setCustomClaim(`${namespace}/roles`, JSON.stringify(event.user.app_metadata.roles));
  api.accessToken.setCustomClaim(`${namespace}/connection`, event.connection.name);
  api.accessToken.setCustomClaim(`${namespace}/connection_strategy`, event.connection.strategy);
  api.accessToken.setCustomClaim(`${namespace}/tenanthub_user_id`, tenanthub_user_id);
  api.accessToken.setCustomClaim(`${namespace}/is_keep_me_signin`, signin_checked? null: new Date());
  api.accessToken.setCustomClaim(`${namespace}/is_rbac_enabled`, is_rbac_enabled);

  if (
    event.user.app_metadata
    && event.user.app_metadata.login_as_user
    && event.client.name !== 'sleekflow-client-powerflow-app') {
    api.accessToken.setCustomClaim(`${namespace}/login_as_tenanthub_user_id`, event.user.app_metadata.login_as_user?.tenanthub_user_id);
    api.accessToken.setCustomClaim(`${namespace}/login_as_user_id`, event.user.app_metadata.login_as_user.user_id);
    api.accessToken.setCustomClaim(`${namespace}/login_as_user`, JSON.stringify(event.user.app_metadata.login_as_user));
    api.idToken.setCustomClaim(`${namespace}/login_as_tenanthub_user_id`, event.user.app_metadata.login_as_user?.tenanthub_user_id);
    api.idToken.setCustomClaim(`${namespace}/login_as_user_id`, event.user.app_metadata.login_as_user.user_id)
    api.idToken.setCustomClaim(`${namespace}/login_as_user`, JSON.stringify(event.user.app_metadata.login_as_user));
  }
};


/**
 * Handler that will be invoked when this action is resuming after an external redirect. If your
 * onExecutePostLogin function does not perform a redirect, this function can be safely ignored.
 *
 * @param {Event} event - Details about the user and the context in which they are logging in.
 * @param {PostLoginAPI} api - Interface whose methods can be used to change the behavior of the login.
 */
// exports.onContinuePostLogin = async (event, api) => {
// };
