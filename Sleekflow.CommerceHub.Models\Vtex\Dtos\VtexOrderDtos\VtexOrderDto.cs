﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

/// <summary>
/// Data structure of a VTEX order.
/// <br/> This only contains part of the fields based on requirements
/// <br/><br/> doc see https://developers.vtex.com/docs/api-reference/orders-api#get-/api/oms/pvt/orders/-orderId-
/// </summary>
public record VtexOrderDto
{
    [JsonProperty("orderId")]
    public string OrderId { get; set; }

    [JsonProperty("origin")]
    public string Origin { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("workflowIsInError")]
    public bool WorkflowIsInError { get; set; }

    [JsonProperty("value")]
    public int? Value { get; set; }

    [JsonProperty("creationDate")]
    public DateTime CreationDate { get; set; }

    [JsonProperty("lastChange")]
    public DateTime LastChange { get; set; }

    [JsonProperty("orderGroup")]
    public string OrderGroup { get; set; }

    [JsonProperty("hostname")]
    public string Hostname { get; set; }

    [JsonProperty("isCompleted")]
    public bool IsCompleted { get; set; }

    [JsonProperty("orderFormId")]
    public string OrderFormId { get; set; }

    [JsonProperty("allowCancellation")]
    public bool AllowCancellation { get; set; }

    [JsonProperty("isCheckedIn")]
    public bool IsCheckedIn { get; set; }

    [JsonProperty("authorizedDate")]
    public DateTime? AuthorizedDate { get; set; }

    [JsonProperty("invoicedDate")]
    public DateTime? InvoicedDate { get; set; }

    [JsonProperty("cancelReason")]
    public string CancelReason { get; set; }

    [JsonProperty("totals")]
    public List<VtexOrderTotalDto> Totals { get; set; }

    [JsonProperty("sellers")]
    public List<VtexOrderSellerDto> Sellers { get; set; }

    [JsonProperty("clientPreferencesData")]
    public VtexClientPreferencesDataDto? ClientPreferencesData { get; set; }

    [JsonProperty("itemMetadata")]
    public VtexItemMetadataDto? ItemMetadata { get; set; }

    [JsonProperty("storePreferencesData")]
    public VtexStorePreferencesDataDto? StorePreferencesData { get; set; }

    [JsonProperty("shippingData")]
    public VtexShippingDataDto? ShippingData { get; set; }

    [JsonProperty("clientProfileData")]
    public VtexClientProfileDataDto? ClientProfileData { get; set; }

    [JsonProperty("items")]
    public List<VtexOrderItemDto> Items { get; set; }

    [JsonConstructor]
    public VtexOrderDto(
        string orderId,
        string origin,
        string status,
        bool workflowIsInError,
        int value,
        DateTime creationDate,
        DateTime lastChange,
        string orderGroup,
        string hostname,
        bool isCompleted,
        string orderFormId,
        bool allowCancellation,
        bool isCheckedIn,
        DateTime? authorizedDate,
        DateTime? invoicedDate,
        string cancelReason,
        List<VtexOrderTotalDto> totals,
        List<VtexOrderSellerDto> sellers,
        VtexClientPreferencesDataDto? clientPreferencesData,
        VtexItemMetadataDto? itemMetadata,
        VtexStorePreferencesDataDto? storePreferencesData,
        VtexShippingDataDto? shippingData,
        VtexClientProfileDataDto? clientProfileData,
        List<VtexOrderItemDto> items)
    {
        OrderId = orderId;
        Origin = origin;
        Status = status;
        WorkflowIsInError = workflowIsInError;
        Value = value;
        CreationDate = creationDate;
        LastChange = lastChange;
        OrderGroup = orderGroup;
        Hostname = hostname;
        IsCompleted = isCompleted;
        OrderFormId = orderFormId;
        AllowCancellation = allowCancellation;
        IsCheckedIn = isCheckedIn;
        AuthorizedDate = authorizedDate;
        InvoicedDate = invoicedDate;
        CancelReason = cancelReason;
        Totals = totals;
        Sellers = sellers;
        ClientPreferencesData = clientPreferencesData;
        ItemMetadata = itemMetadata;
        StorePreferencesData = storePreferencesData;
        ShippingData = shippingData;
        ClientProfileData = clientProfileData;
        Items = items;
    }
}