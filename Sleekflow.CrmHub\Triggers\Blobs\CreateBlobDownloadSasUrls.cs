using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Blobs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.Blobs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Blobs;

[TriggerGroup(TriggerGroups.Blobs)]
public class CreateBlobDownloadSasUrls
    : ITrigger<
        CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsInput,
        CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsOutput>
{
    private readonly IBlobService _blobService;

    public CreateBlobDownloadSasUrls(IBlobService blobService)
    {
        _blobService = blobService;
    }

    public class CreateBlobDownloadSasUrlsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [Required]
        [JsonProperty("blob_type")]
        public string BlobType { get; set; }

        [JsonConstructor]
        public CreateBlobDownloadSasUrlsInput(string sleekflowCompanyId, List<string> blobNames, string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobNames = blobNames;
            BlobType = blobType;
        }
    }

    public class CreateBlobDownloadSasUrlsOutput
    {
        [JsonProperty("download_blobs")]
        public List<PublicBlob> DownloadBlobs { get; set; }

        [JsonConstructor]
        public CreateBlobDownloadSasUrlsOutput(
            List<PublicBlob> downloadBlobs)
        {
            DownloadBlobs = downloadBlobs;
        }
    }

    public async Task<CreateBlobDownloadSasUrlsOutput> F(
        CreateBlobDownloadSasUrlsInput createBlobDownloadSasUrlsInput)
    {
        var downloadBlobs = await _blobService.CreateBlobDownloadSasUrls(
            createBlobDownloadSasUrlsInput.SleekflowCompanyId,
            createBlobDownloadSasUrlsInput.BlobNames,
            createBlobDownloadSasUrlsInput.BlobType);
        return new CreateBlobDownloadSasUrlsOutput(
            downloadBlobs);
    }
}