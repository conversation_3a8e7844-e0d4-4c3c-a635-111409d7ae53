using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.TravisBackend;

public class CompanyBillRecord
{
    [JsonProperty("id")]
    public long Id { get; set; }

    [JsonProperty("subscription_plan_id")]
    public string SubscriptionPlanId { get; set; }

    [JsonProperty("period_start")]
    public DateTime PeriodStart { get; set; }

    [JsonProperty("period_end")]
    public DateTime PeriodEnd { get; set; }

    [JsonProperty("pay_amount")]
    public double PayAmount { get; set; }

    [JsonProperty("payment_status")]
    public int PaymentStatus { get; set; }

    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("stripe_subscription_id")]
    public string StripeSubscriptionId { get; set; }

    [JsonProperty("created")]
    public DateTime Created { get; set; }

    [JsonConstructor]
    public CompanyBillRecord(
        long id,
        string subscriptionPlanId,
        DateTime periodStart,
        DateTime periodEnd,
        double payAmount,
        int paymentStatus,
        string currency,
        string stripeSubscriptionId,
        DateTime created)
    {
        Id = id;
        SubscriptionPlanId = subscriptionPlanId;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
        PayAmount = payAmount;
        PaymentStatus = paymentStatus;
        Currency = currency;
        StripeSubscriptionId = stripeSubscriptionId;
        Created = created;
    }
}