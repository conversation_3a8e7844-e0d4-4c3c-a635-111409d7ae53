using Newtonsoft.Json;

namespace Sleekflow.KrakenD.Generator.InternalObjects;

internal class Auth0ManagedEndpoint : Endpoint
{
    [JsonIgnore]
    public string Group { get; }

    [JsonIgnore]
    public string Subgroup { get; }

    [JsonIgnore]
    public string HostEnvName { get; }

    [JsonIgnore]
    public string KeyEnvName { get; }

    public Auth0ManagedEndpoint(
        string group,
        string subgroup,
        string hostEnvName,
        string keyEnvName)
    {
        Group = group;
        Subgroup = subgroup;
        HostEnvName = hostEnvName;
        KeyEnvName = keyEnvName;
        EndpointEndpoint = "/v1/" + group + "/" + subgroup + "/{method}";
        Method = Generator.Method.Post;
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/" + subgroup + "/{method}",
                Method = Generator.Method.Post,
                Host = new object[]
                {
                    "{{ env \"" + hostEnvName + "\" }}"
                },
                ExtraConfig = new BackendExtraConfiguration()
                {
                    PluginHttpClient = new HttpClientPluginsSeeHttpsWwwKrakendIoDocsExtendingInjectingPlugins()
                    {
                        Name = "krakend-authentication-plugin",
                        KrakendAuthenticationPlugin = new HttpClientPluginsKrakendAuthenticationPluginConfig()
                        {
                            GetUserAuthDetailsUrl =
                                "{{ env \"TENANT_HUB_HOST\" }}{{ env \"TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS\" }}"
                        }
                    },
                }
            }
        };
        ExtraConfig = new EndpointExtraConfig
        {
            AuthValidator = ConstructAuthValidator(), ModifierLuaProxy = ConstructModifierLuaProxy()
        };
        InputHeaders = new string[]
        {
            "Content-Type",
            "Traceparent",
            "X-Sleekflow-Roles",
            "X-Sleekflow-Email",
            "X-Sleekflow-TenantHub-User-Id",
            "X-Sleekflow-User-Id",
            "X-Sleekflow-Login-As-User",
            "X-Sleekflow-Connection-Strategy",
            "X-Sleekflow-Distributed-Invocation-Context"
        };
    }

    public static ModifierLuaEndpointClass ConstructModifierLuaProxy()
    {
        var sources = new List<object>
        {
            "lua/post_proxy.lua",
        };

        return new ModifierLuaEndpointClass
        {
            Sources = sources.ToArray(), Post = "post_proxy()", Live = false, AllowOpenLibs = false
        };
    }

    public static JwtValidator ConstructAuthValidator()
    {
        return new JwtValidator
        {
            Alg = Algorithm.Rs256,
            Audience = new[]
            {
                "AUTH_0_AUDIENCE"
            }.Select(a => "{{ env \"" + a + "\" }}").ToArray(),
            JwkUrl = "{{ env \"" + "AUTH_0_JWK_URL" + "\" }}",
            Cache = true,
            PropagateClaims = new[]
            {
                new[]
                {
                    "https://app.sleekflow.io/email",
                    "X-Sleekflow-Email"
                },
                new[]
                {
                    "https://app.sleekflow.io/tenanthub_user_id",
                    "X-Sleekflow-TenantHub-User-Id"
                },
                new[]
                {
                    "https://app.sleekflow.io/user_id",
                    "X-Sleekflow-User-Id"
                },
                new[]
                {
                    "https://app.sleekflow.io/roles",
                    "X-Sleekflow-Roles"
                },
                new[]
                {
                    "https://app.sleekflow.io/connection_strategy",
                    "X-Sleekflow-Connection-Strategy"
                },
                new[]
                {
                    "https://app.sleekflow.io/login_as_user",
                    "X-Sleekflow-Login-As-User"
                }
            }
        };
    }
}