﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class UpdateDefaultProduct
    : ITrigger<
        UpdateDefaultProduct.UpdateDefaultProductInput,
        UpdateDefaultProduct.UpdateDefaultProductOutput>
{
    private readonly IDefaultProductService _defaultProductService;

    public UpdateDefaultProduct(
        IDefaultProductService defaultProductService)
    {
        _defaultProductService = defaultProductService;
    }

    public class UpdateDefaultProductInput : ProductInput, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty(Product.PropertyNameIsViewEnabled)]
        public bool IsViewEnabled { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty(ProductVariant.PropertyNamePrices)]
        public List<Price> Prices { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty(ProductVariant.PropertyNameAttributes)]
        public List<ProductVariant.ProductVariantAttribute> Attributes { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateDefaultProductInput(
            List<string> categoryIds,
            string? sku,
            string? url,
            List<Multilingual> names,
            List<Description> descriptions,
            List<ImageDto> images,
            Dictionary<string, object?> metadata,
            string id,
            string sleekflowCompanyId,
            string storeId,
            bool isViewEnabled,
            List<Price> prices,
            List<ProductVariant.ProductVariantAttribute> attributes,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(categoryIds, sku, url, names, descriptions, images, metadata)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            IsViewEnabled = isViewEnabled;
            Prices = prices;
            Attributes = attributes;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateDefaultProductOutput
    {
        [JsonProperty("product")]
        public ProductDto Product { get; set; }

        [JsonConstructor]
        public UpdateDefaultProductOutput(ProductDto product)
        {
            Product = product;
        }
    }

    public async Task<UpdateDefaultProductOutput> F(UpdateDefaultProductInput updateDefaultProductInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateDefaultProductInput.SleekflowStaffId,
            updateDefaultProductInput.SleekflowStaffTeamIds);

        var (product, productVariants) = await _defaultProductService.UpdateDefaultProductAsync(
            updateDefaultProductInput.Id,
            updateDefaultProductInput,
            updateDefaultProductInput.SleekflowCompanyId,
            updateDefaultProductInput.StoreId,
            updateDefaultProductInput.IsViewEnabled,
            updateDefaultProductInput.Prices,
            updateDefaultProductInput.Attributes,
            sleekflowStaff);

        return new UpdateDefaultProductOutput(new ProductDto(product, productVariants));
    }
}