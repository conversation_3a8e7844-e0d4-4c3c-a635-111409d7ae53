using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Authentications;

public abstract class EmailAuthenticationMetadata
{
    [Required]
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [Required]
    [JsonProperty("provider_id")]
    public string ProviderId { get; set; }

    [JsonConstructor]
    protected EmailAuthenticationMetadata(
        string providerName,
        string providerId)
    {
        ProviderName = providerName;
        ProviderId = providerId;
    }
}