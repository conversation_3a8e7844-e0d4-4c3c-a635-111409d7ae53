using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Managements;

public class ManagementBusinessBalanceDto : BusinessBalanceDto
{
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty("sleekflow_company_ids", NullValueHandling = NullValueHandling.Ignore)]
    public List<string> SleekflowCompanyIds { get; set; }

    [JsonProperty("total_used", NullValueHandling = NullValueHandling.Ignore)]
    public Money TotalUsed { get; set; }

    [JsonProperty("mark_up", NullValueHandling = NullValueHandling.Ignore)]
    public Money Markup { get; set; }

    [JsonProperty("transaction_handling_fee", NullValueHandling = NullValueHandling.Ignore)]
    public Money? TransactionHandlingFee { get; set; }

    [JsonProperty("waba_markup_profile", NullValueHandling = NullValueHandling.Ignore)]
    public MarkupProfile MarkupProfile { get; set; }

    [JsonProperty("conversation_usage_insert_state", NullValueHandling = NullValueHandling.Ignore)]
    public ConversationUsageInsertState ConversationUsageInsertState { get; set; }

    [JsonConstructor]
    public ManagementBusinessBalanceDto(
        string facebookBusinessId,
        string? facebookBusinessName,
        List<FacebookBusinessWaba> facebookBusinessWabas,
        Money totalCredit,
        Money allTimeUsage,
        Money balance,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string id,
        List<string> sleekflowCompanyIds,
        Money totalUsed,
        Money markup,
        Money? transactionHandlingFee,
        MarkupProfile markupProfile,
        ConversationUsageInsertState conversationUsageInsertState,
        List<WabaBalance>? wabaBalances,
        Money? unallocatedCredit,
        List<CreditTransferTransactionLogDto> unCalculatedCreditTransferTransactionLogs,
        string? eTag,
        bool? isByWabaBillingEnabled)
        : base(
            facebookBusinessId,
            facebookBusinessName,
            facebookBusinessWabas,
            totalCredit,
            allTimeUsage,
            balance,
            wabaBalances?.Select(w => new WabaBalanceDto(w)).ToList(),
            unallocatedCredit,
            isByWabaBillingEnabled,
            unCalculatedCreditTransferTransactionLogs,
            eTag,
            createdAt,
            updatedAt)
    {
        Id = id;
        SleekflowCompanyIds = sleekflowCompanyIds;
        TotalUsed = totalUsed;
        Markup = markup;
        MarkupProfile = markupProfile;
        TransactionHandlingFee = transactionHandlingFee;
        ConversationUsageInsertState = conversationUsageInsertState;
    }

    public ManagementBusinessBalanceDto(
        BusinessBalance businessBalance,
        List<Waba> wabas,
        List<CreditTransferTransactionLogDto> unCalculatedCreditTransferTransactionLogs)
        : base(businessBalance, wabas, unCalculatedCreditTransferTransactionLogs)
    {
        Id = businessBalance.Id;
        SleekflowCompanyIds = wabas.SelectMany(w => w.SleekflowCompanyIds).Distinct().ToList();
        TotalUsed = businessBalance.Used;
        Markup = businessBalance.Markup;
        TransactionHandlingFee = businessBalance.TransactionHandlingFee;
        MarkupProfile = businessBalance.MarkupProfile;
        ConversationUsageInsertState = businessBalance.ConversationUsageInsertState;
    }
}