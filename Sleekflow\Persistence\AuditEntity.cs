using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence;

public abstract class AuditEntity : Entity, IHasCreatedAt, IHasUpdatedAt
{
    public const string PropertyNameCreatedBy = "created_by";
    public const string PropertyNameUpdatedBy = "updated_by";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameCreatedBy)]
    public SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(PropertyNameUpdatedBy)]
    public SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    protected AuditEntity(
        string id,
        string sysTypeName,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(id, sysTypeName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public class SleekflowStaff : IHasSleekflowStaff
    {
        [JsonProperty("sleekflow_staff_id")]
        public string SleekflowStaffId { get; set; }

        [JsonProperty("sleekflow_staff_team_ids")]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        public SleekflowStaff(
            string sleekflowStaffId,
            List<string>? sleekflowTeamIds)
        {
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowTeamIds;
        }
    }

    public static SleekflowStaff? ConstructSleekflowStaff(string? sleekflowStaffId, List<string>? sleekflowStaffTeamIds)
    {
        return sleekflowStaffId is not null ? new SleekflowStaff(sleekflowStaffId, sleekflowStaffTeamIds) : null;
    }
}

public abstract class AuditEntityDto : EntityDto
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(AuditEntity.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(AuditEntity.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    protected AuditEntityDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}