﻿using MassTransit;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events.InternalActionEvents;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Executor.Consumers.InternalActionEventConsumers;

public class EnableInternalWorkflowRequestConsumerDefinition
    : ConsumerDefinition<EnableInternalWorkflowRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<EnableInternalWorkflowRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class EnableInternalWorkflowRequestConsumer : IConsumer<EnableInternalWorkflowRequest>
{
    private readonly IWorkflowService _workflowService;

    public EnableInternalWorkflowRequestConsumer(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public async Task Consume(ConsumeContext<EnableInternalWorkflowRequest> context)
    {
        var enableInternalWorkflowRequest = context.Message;

        await ValidateWorkflowType(enableInternalWorkflowRequest.WorkflowVersionedId, enableInternalWorkflowRequest.SleekflowCompanyId);

        var workflow = await _workflowService.EnableWorkflowAsync(
            enableInternalWorkflowRequest.WorkflowVersionedId,
            enableInternalWorkflowRequest.SleekflowCompanyId,
            null);

        await context.RespondAsync(new EnableInternalWorkflowReply(new WorkflowDto(workflow)));
    }

    private async Task ValidateWorkflowType(string workflowVersionedId, string sleekflowCompanyId)
    {
        var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(sleekflowCompanyId, workflowVersionedId);

        if (workflow is { WorkflowType: WorkflowType.Normal })
        {
            throw new SfWorkflowTypeException("Normal workflow cannot be enabled here.");
        }
    }
}