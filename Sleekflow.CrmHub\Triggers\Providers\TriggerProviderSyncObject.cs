﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class TriggerProviderSyncObject : ITrigger
{
    private readonly IEntityContextService _entityContextService;
    private readonly IProviderSelector _providerSelector;

    public TriggerProviderSyncObject(
        IEntityContextService entityContextService,
        IProviderSelector providerSelector)
    {
        _entityContextService = entityContextService;
        _providerSelector = providerSelector;
    }

    public class TriggerProviderSyncObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonConstructor]
        public TriggerProviderSyncObjectInput(
            string sleekflowCompanyId,
            string providerName,
            string entityTypeName,
            string objectId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
        }
    }

    public class TriggerProviderSyncObjectOutput
    {
    }

    public async Task<TriggerProviderSyncObjectOutput> F(
        TriggerProviderSyncObjectInput triggerProviderSyncObjectInput)
    {
        var providerName = triggerProviderSyncObjectInput.ProviderName;

        var entityContext = await _entityContextService.GetEntityContextAsync(
            triggerProviderSyncObjectInput.ObjectId,
            triggerProviderSyncObjectInput.SleekflowCompanyId,
            triggerProviderSyncObjectInput.EntityTypeName);
        if (entityContext == null)
        {
            throw new SfUserFriendlyException("The object does not exist");
        }

        var providerService = _providerSelector.GetProviderService(providerName);

        var externalIds = new List<string?>
        {
            entityContext.CtxExternalId,
        };
        if (entityContext.CtxExternalIds != null)
        {
            externalIds.AddRange(entityContext.CtxExternalIds);
        }

        var providerObjectIds = externalIds
            .Where(id => id != null && id.StartsWith($"{providerName}:"))
            .Select(id => id!.Replace($"{providerName}:", string.Empty));
        foreach (var providerObjectId in providerObjectIds)
        {
            await providerService.SyncObjectAsync(
                triggerProviderSyncObjectInput.SleekflowCompanyId,
                providerObjectId,
                triggerProviderSyncObjectInput.EntityTypeName);
        }

        return new TriggerProviderSyncObjectOutput();
    }
}