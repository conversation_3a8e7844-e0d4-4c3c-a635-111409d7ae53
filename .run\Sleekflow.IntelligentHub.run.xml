<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.IntelligentHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.IntelligentHub/bin/Debug/net8.0/Sleekflow.IntelligentHub.exe" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.IntelligentHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="APIFY_ACTOR_ID" value="aYG0l9s7dbB7j3gbS" />
      <env name="APIFY_TOKEN" value="**********************************************" />
      <env name="APIFY_WEBHOOKURI" value="https://webhook.site/cb370659-7adc-4789-9f95-4280065e745e" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7101;http://localhost:7102" />
      <env name="AZURE_COSMOS_VECTOR_DB_DATABASE_ID" value="intelligenthubvectordb" />
      <env name="AZURE_COSMOS_VECTOR_DB_ENDPOINT" value="https://sleekflow-vector413980ef.documents.azure.com:443/" />
      <env name="AZURE_COSMOS_VECTOR_DB_KEY" value="****************************************************************************************" />
      <env name="AZURE_FORM_RECOGNIZER_ENDPOINT" value="https://eastasia.api.cognitive.microsoft.com/" />
      <env name="AZURE_FORM_RECOGNIZER_KEY" value="ea210e9b15644d968fb3247236a632d3" />
      <env name="AZURE_TEXT_TRANSLATOR_KEY" value="d2275a86c8c84137b7a1ff1d6e2f2dae" />
      <env name="AZURECOGNITIVESEARCH_ADMIN_KEY" value="****************************************************" />
      <env name="AZURECOGNITIVESEARCH_ADMIN_KEY_2" value="****************************************************" />
      <env name="AZURECOGNITIVESEARCH_ADMIN_KEY_3" value="****************************************************" />
      <env name="AZURECOGNITIVESEARCH_ENDPOINT" value="https://sleekflow-ih-searchc58e8305.search.windows.net/" />
      <env name="AZURECOGNITIVESEARCH_ENDPOINT_2" value="https://sleekflow-ih-searchc58e8305.search.windows.net/" />
      <env name="AZURECOGNITIVESEARCH_ENDPOINT_3" value="https://sleekflow-ih-searchc58e8305.search.windows.net/" />
      <env name="AZUREOPENAI_EMBEDDING_DEPLOYMENT_NAME" value="embedding" />
      <env name="AZUREOPENAI_EMBEDDING_ENDPOINT" value="https://sleekflow-ih-openai216b80d4.openai.azure.com" />
      <env name="AZUREOPENAI_EMBEDDING_KEY" value="********************************" />
      <env name="AZUREOPENAI_ENDPOINT" value="https://sleekflow-ih-openai216b80d4.openai.azure.com/" />
      <env name="AZUREOPENAI_EUS_ENDPOINT" value="https://sleekflow-openai-eus2.openai.azure.com/" />
      <env name="AZUREOPENAI_EUS_KEY" value="0b0b5fec514f4a3da1a2c966874684a5" />
      <env name="AZUREOPENAI_KEY" value="********************************" />
      <env name="CACHE_PREFIX" value="Sleekflow.IntelligentHub" />
      <env name="COSMOS_CHANGE_FEED_ENV_ID" value="default" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_INTELLIGENT_HUB_DB_DATABASE_ID" value="intelligentHubdb" />
      <env name="COSMOS_INTELLIGENT_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_INTELLIGENT_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="HEADLESS_WHATSAPP_MESSAGE_INTEGRATION_ENDPOINT" value="https://gw.sleekflow.io/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Messages/SendMessage" />
      <env name="LIGHT_RAG_API_KEY" value="cCY28CaFFBeNtJq5lSeNXErADNE7euUw" />
      <env name="LIGHT_RAG_DOMAIN" value="https://sleekflow-ihlr-light-rag-webapp-dev.azurewebsites.net" />
      <env name="LIGHT_RAG_SUPPORTED_COMPANIES" value="b6d7e442-38ae-4b9a-b100-2951729768bc,0254e8a3-3a5b-4bf8-a7ce-78f7fe861b85,cddf6a22-0751-47ff-94c8-4d78c17b37b1,5873424d-5f26-4c81-9e16-5b248f7944de,5406fdfa-1e8c-4a76-91f6-89301c0d95a1,aecdf113-2b29-4ee1-90d2-ff061b6f073d,b5148215-4473-45f5-b820-89a7abbe88b5,0112a3be-6eda-4bf7-b36f-0ef59edcac9e,d218796b-b954-4afb-82c5-6735a7f3a162,d88d6bca-8d18-491e-9654-c9623705e3fc,18e2da38-52a7-4729-8102-6f5673fd3952,577da285-4d3d-410a-a40b-5c1857d1516a,532376d8-3d72-458d-b19e-1326c3130502,1b6c02ab-8554-4078-a6aa-6539b4256092,154ec87c-0761-41b7-bbba-6a3e81988928,f841ab07-c0c9-44f8-98e7-addd0f8aa757" />
      <env name="LIGHT_RAG_WITH_NEO4J_DOMAIN" value="https://sleekflow-ihlrn-light-rag-webapp-dev.azurewebsites.net" />
      <env name="LIGHT_RAG_WITH_NEO4J_SUPPORTED_COMPANIES" value="d218796b-b954-4afb-82c5-6735a7f3a162" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="SOURCE_FILE_STORAGE_ACCOUNT_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=sebd9353e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="WORKER_FUNCTIONS_KEY" value="PLACEHOLDER" />
      <env name="WORKER_HOSTNAME" value="http://localhost:7190" />
      <env name="PROMPT_FILTER_PRINT_FUNCTIONS" value="EvaluateScoring" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.IntelligentHub/Sleekflow.IntelligentHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>