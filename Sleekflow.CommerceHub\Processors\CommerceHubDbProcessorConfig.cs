using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Processors;

public interface ICommerceHubDbProcessorConfig
{
    string CosmosChangeFeedEnvId { get; }
}

public class CommerceHubDbProcessorConfig : IConfig, ICommerceHubDbProcessorConfig
{
    public string CosmosChangeFeedEnvId { get; private set; }

    public CommerceHubDbProcessorConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        CosmosChangeFeedEnvId = Environment.GetEnvironmentVariable("COSMOS_CHANGE_FEED_ENV_ID", target) ??
                                throw new SfMissingEnvironmentVariableException("COSMOS_CHANGE_FEED_ENV_ID");
    }
}