using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Outlook.Communications;

public class OutlookInboxDeltaResponse
{
    [JsonProperty("@odata.context")]
    public string ODataContext { get; set; }

    [JsonProperty("value")]
    public List<Message> Value { get; set; }

    [JsonProperty("@odata.nextLink")]
    public string? ODateNextLink { get; set; }

    [JsonProperty("@odata.deltaLink")]
    public string? ODataDeltaLink { get; set; }

    [JsonConstructor]
    public OutlookInboxDeltaResponse(
        string oDataContext,
        List<Message> value,
        string? oDateNextLink,
        string? oDataDeltaLink)
    {
        ODataContext = oDataContext;
        Value = value;
        ODateNextLink = oDateNextLink;
        ODataDeltaLink = oDataDeltaLink;
    }
}