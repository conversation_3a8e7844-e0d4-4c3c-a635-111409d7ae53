﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetObjectsV2 : ITrigger
{
    private readonly IEntityRepository _entityRepository;

    public GetObjectsV2(
        IEntityRepository entityRepository)
    {
        _entityRepository = entityRepository;
    }

    public class GetObjectsV2InputFilterGroup
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<EntityQueryBuilder.Filter> Filters { get; set; }

        [JsonConstructor]
        public GetObjectsV2InputFilterGroup(
            List<EntityQueryBuilder.Filter> filters)
        {
            Filters = filters;
        }
    }

    public class GetObjectsV2InputExpand
    {
        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("from_field_name")]
        public string FromFieldName { get; set; }

        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("to_entity_name")]
        public string ToEntityName { get; set; }

        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("to_field_name")]
        public string ToFieldName { get; set; }

        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("as_field_name")]
        public string AsFieldName { get; set; }

        [JsonConstructor]
        public GetObjectsV2InputExpand(
            string fromFieldName,
            string toEntityName,
            string toFieldName,
            string asFieldName)
        {
            FromFieldName = fromFieldName;
            ToEntityName = toEntityName;
            ToFieldName = toFieldName;
            AsFieldName = asFieldName;
        }
    }

    public class GetObjectsV2Input : IValidatableObject
    {
        [StringLength(16384, MinimumLength = 1)]
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        [StringLength(1024)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("entity_type_name")]
        [StringLength(128)]
        public string EntityTypeName { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetObjectsV2InputFilterGroup> FilterGroups { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("sorts")]
        public List<EntityQueryBuilder.Sort> Sorts { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("expands")]
        public List<GetObjectsV2InputExpand> Expands { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            var crmHubDbEntityService = validationContext.GetRequiredService<ICrmHubDbEntityService>();
            if (crmHubDbEntityService.IsNotSupportedEntityTypeName(EntityTypeName))
            {
                results.Add(
                    new ValidationResult(
                        "The EntityTypeName is not supported.",
                        new List<string>
                        {
                            nameof(EntityTypeName)
                        }));
            }

            return results;
        }

        [JsonConstructor]
        public GetObjectsV2Input(
            string? continuationToken,
            string sleekflowCompanyId,
            string entityTypeName,
            int limit,
            List<GetObjectsV2InputFilterGroup> filterGroups,
            List<EntityQueryBuilder.Sort> sorts,
            List<GetObjectsV2InputExpand> expands)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            Limit = limit;
            FilterGroups = filterGroups;
            Sorts = sorts;
            Expands = expands;
        }
    }

    public class GetObjectsV2Output
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("records")]
        public List<CrmHubEntity> Records { get; set; }

        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetObjectsV2Output(
            string? continuationToken,
            List<CrmHubEntity> records,
            long count)
        {
            ContinuationToken = continuationToken;
            Records = records;
            Count = count;
        }
    }

    public async Task<GetObjectsV2Output> F(
        GetObjectsV2Input getObjectsV2Input)
    {
        var filterGroups = getObjectsV2Input
            .FilterGroups
            .Select(fg => new EntityQueryBuilder.FilterGroup(fg.Filters.Cast<EntityQueryBuilder.IFilter>().ToList()))
            .ToList();

        var queryDefinition =
            EntityQueryBuilder.BuildQueryDef(
                new List<EntityQueryBuilder.ISelect>(),
                getObjectsV2Input.EntityTypeName,
                filterGroups,
                getObjectsV2Input.Sorts,
                new List<EntityQueryBuilder.GroupBy>(),
                getObjectsV2Input.SleekflowCompanyId);

        var (rawRecords, nextContinuationToken) =
            await _entityRepository.GetContinuationTokenizedObjectsAsync(
                queryDefinition,
                getObjectsV2Input.ContinuationToken,
                getObjectsV2Input.Limit);

        var records = rawRecords
            .Select(EntityService.Sanitize)
            .ToList();

        foreach (var expand in getObjectsV2Input.Expands)
        {
            var fromFieldName = expand.FromFieldName;
            var toEntityName = expand.ToEntityName;
            var toFieldName = expand.ToFieldName;
            var asFieldName = expand.AsFieldName;

            var fromFieldValues = records
                .Select(r => ((JValue?) r.GetValueOrDefault(fromFieldName))?.Value<string>())
                .Where(r => r != null)
                .ToList();

            var expandFilters = fromFieldValues
                .Select(fv => new EntityQueryBuilder.Filter(toFieldName, "=", fv))
                .ToList();
            if (expandFilters.Count == 0)
            {
                foreach (var record in records)
                {
                    record[$"expanded:{asFieldName}"] = new List<CrmHubEntity>();
                }

                continue;
            }

            var expandQd =
                EntityQueryBuilder.BuildQueryDef(
                    new List<EntityQueryBuilder.ISelect>(),
                    toEntityName,
                    new List<EntityQueryBuilder.FilterGroup>
                    {
                        new (expandFilters.Cast<EntityQueryBuilder.IFilter>().ToList())
                    },
                    new List<EntityQueryBuilder.Sort>(),
                    new List<EntityQueryBuilder.GroupBy>(),
                    getObjectsV2Input.SleekflowCompanyId);

            var expandObjects =
                await _entityRepository.GetObjectsAsync(expandQd, Math.Min(fromFieldValues.Count * 5, 1000));
            var expandObjectDict = expandObjects
                .Select(EntityService.Sanitize)
                .Where(e => e.ContainsKey(toFieldName))
                .GroupBy(e => e[toFieldName]!)
                .ToDictionary(e => e.Key, e => e.ToList());

            foreach (var record in records)
            {
                var fromFieldValue = record.GetValueOrDefault(fromFieldName);
                if (fromFieldValue != null && expandObjectDict.ContainsKey(fromFieldValue))
                {
                    record[$"expanded:{asFieldName}"] = expandObjectDict[fromFieldValue];
                }
                else
                {
                    record[$"expanded:{asFieldName}"] = new List<CrmHubEntity>();
                }
            }
        }

        return new GetObjectsV2Output(nextContinuationToken, records, records.Count);
    }
}