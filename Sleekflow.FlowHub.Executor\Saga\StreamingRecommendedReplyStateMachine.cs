using System.Security.Cryptography;
using System.Text;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.Models.WorkflowSteps;
using State = MassTransit.State;

namespace Sleekflow.FlowHub.Executor.Saga;

public class StreamingRecommendedReplyState : SagaStateMachineInstance
{
    [JsonProperty("id")]
    public Guid CorrelationId { get; set; }

    public int CurrentState { get; set; }

    public string? ProxyStateId { get; set; }

    public string? RecommendReplyStepId { get; set; }

    public int NextSequenceNumberToSend { get; set; } = 0;

    // sequence number to partial answer
    public SortedDictionary<int, string> PartialAnswers { get; set; } = new ();

    public int? ConfidenceScoring { get; set; }

    public int? FinalSequenceNumber { get; set; } = null;

    public List<StreamingSendMessageSubscription> Subscriptions { get; set; } = [];

    public StreamingSendMessageContext StreamingSendMessageContext { get; set; } = new ([]);

    [JsonProperty("_etag")]
    public string ETag { get; set; }
}

public class StreamingRecommendedReplyStateMachine : MassTransitStateMachine<StreamingRecommendedReplyState>
{
    private readonly ILogger<StreamingRecommendedReplyStateMachine> _logger;

    public State RecommendedReplyGenerationStarted { get; private set; }

    public State RecommendedReplyGenerationFinished { get; private set; }

    public Event<OnRecommendedReplyGenerationEmitEvent> OnRecommendedReplyGenerationEmitEvent { get; private set; }

    public Event<OnRecommendedReplyGenerationFinishedEvent> OnRecommendedReplyGenerationFinishedEvent
    {
        get;
        private set;
    }

    public Event<StartStreamingSendMessageEvent> StartStreamingSendMessage { get; private set; }

    public Request<StreamingRecommendedReplyState, StreamingSendMessageRequest, StreamingSendMessageResponse>
        StreamingSendMessageRequest
    {
        get;
        private set;
    }

    public StreamingRecommendedReplyStateMachine(ILogger<StreamingRecommendedReplyStateMachine> logger)
    {
        _logger = logger;

        InstanceState(x => x.CurrentState, RecommendedReplyGenerationStarted, RecommendedReplyGenerationFinished);

        Event(
            () => OnRecommendedReplyGenerationEmitEvent,
            x =>
                x.CorrelateById(
                    x => GenerateCorrelationId(
                        x.Message.ProxyStateId,
                        x.Message.RecommendReplyStepId)));

        Event(
            () => OnRecommendedReplyGenerationFinishedEvent,
            x =>
                x.CorrelateById(
                    x => GenerateCorrelationId(
                        x.Message.ProxyStateId,
                        x.Message.RecommendReplyStepId)));

        Event(
            () => StartStreamingSendMessage,
            x =>
                x.CorrelateById(
                    x => GenerateCorrelationId(
                        x.Message.ProxyStateId,
                        x.Message.RecommendReplyStepId)));

        Request(() => StreamingSendMessageRequest);

        HandleInitialState();
        HandleRecommendedReplyGenerationStartedState();
        HandleRecommendedReplyGenerationFinishedState();
        HandleStreamingSendMessageRequestPendingState();
    }

    private void HandleInitialState()
    {
        Initially(
            When(OnRecommendedReplyGenerationEmitEvent)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "Initially: OnRecommendedReplyGenerationEmitEvent: {CorrelationId} {SequenceNumber}",
                            x.Saga.CorrelationId,
                            x.Message.StreamSequenceNumber);

                        x.Saga.ProxyStateId = x.Message.ProxyStateId;
                        x.Saga.RecommendReplyStepId = x.Message.RecommendReplyStepId;

                        x.Saga.PartialAnswers[x.Message.StreamSequenceNumber] =
                            x.Message.PartialRecommendedReply;
                    })
                .TransitionTo(RecommendedReplyGenerationStarted),
            When(StartStreamingSendMessage)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "Initially: StartStreamingSendMessage: {CorrelationId}",
                            x.Saga.CorrelationId);

                        x.Saga.ProxyStateId = x.Message.ProxyStateId;
                        x.Saga.RecommendReplyStepId = x.Message.RecommendReplyStepId;

                        var newSubscription = new StreamingSendMessageSubscription(
                            x.Message.ProxyStateId,
                            x.Message.SendMessageInput,
                            x.Message.SendMessageStepId,
                            x.Message.StackEntries);
                        x.Saga.Subscriptions.Add(newSubscription);
                    })
                .TransitionTo(RecommendedReplyGenerationStarted));
    }

    private void HandleRecommendedReplyGenerationStartedState()
    {
        During(
            RecommendedReplyGenerationStarted,
            When(OnRecommendedReplyGenerationEmitEvent)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "RecommendedReplyGenerationStarted: OnRecommendedReplyGenerationEmitEvent: {CorrelationId}, {SequenceNumber}",
                            x.Saga.CorrelationId,
                            x.Message.StreamSequenceNumber);

                        x.Saga.PartialAnswers[x.Message.StreamSequenceNumber] =
                            x.Message.PartialRecommendedReply;
                    })
                .If(
                    x => ShouldSendMessageToSubscribers(x.Saga),
                    x => x
                        .Request(
                            StreamingSendMessageRequest,
                            x =>
                                new StreamingSendMessageRequest(
                                    x.Saga.CorrelationId,
                                    x.Saga.Subscriptions,
                                    AdvanceStream(x.Saga)))
                        .IfElse(
                            x => IsStreamFinished(x.Saga),
                            x =>
                                x.TransitionTo(RecommendedReplyGenerationFinished),
                            x =>
                                x.TransitionTo(StreamingSendMessageRequest.Pending))),
            When(OnRecommendedReplyGenerationFinishedEvent)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "RecommendedReplyGenerationStarted: OnRecommendedReplyGenerationFinishedEvent: {CorrelationId}, {FinalSequenceNumber}",
                            x.Saga.CorrelationId,
                            x.Message.FinalSequenceNumber);

                        x.Saga.FinalSequenceNumber = x.Message.FinalSequenceNumber;
                        x.Saga.ConfidenceScoring = x.Message.ConfidenceScoring;
                    })
                .IfElse(
                    x => !HasSubscribers(x.Saga),
                    x => x.TransitionTo(RecommendedReplyGenerationFinished),
                    x =>
                        x.If(
                            x => IsStreamFinished(x.Saga),
                            x => x.TransitionTo(RecommendedReplyGenerationFinished))),
            When(StartStreamingSendMessage)
                .Then(
                    (x) =>
                    {
                        _logger.LogInformation(
                            "RecommendedReplyGenerationStarted: StartStreamingSendMessage: {CorrelationId}",
                            x.Saga.CorrelationId);

                        var newSubscription = new StreamingSendMessageSubscription(
                            x.Message.ProxyStateId,
                            x.Message.SendMessageInput,
                            x.Message.SendMessageStepId,
                            x.Message.StackEntries);
                        x.Saga.Subscriptions.Add(newSubscription);
                    })

                // the new subscription needs to catch up to the rest of the subscribers
                .If(
                    x => x.Saga.NextSequenceNumberToSend > 0,
                    x => x
                        .Request(
                            StreamingSendMessageRequest,
                            x =>
                                new StreamingSendMessageRequest(
                                    x.Saga.CorrelationId,
                                    [x.Saga.Subscriptions[^1]], // the new subscription we just added
                                    GetStreamedPartialAnswers(x.Saga)))
                        .TransitionTo(StreamingSendMessageRequest.Pending)),
            Ignore(StreamingSendMessageRequest.TimeoutExpired));
    }

    private void HandleRecommendedReplyGenerationFinishedState()
    {
        WhenEnter(
            RecommendedReplyGenerationFinished,
            x => x
                .Publish(
                    x => new OnStreamingSendMessageFinishedEvent(
                        x.Saga.ProxyStateId!,
                        x.Saga.RecommendReplyStepId!,
                        string.Join('\n', x.Saga.PartialAnswers.Values),
                        x.Saga.Subscriptions)));

        // After generation is finished, all streaming requests will get the entire answer immediately
        During(
            RecommendedReplyGenerationFinished,
            When(StartStreamingSendMessage)
                .Then(
                    (x) =>
                    {
                        _logger.LogInformation(
                            "RecommendedReplyGenerationFinished: RecommendedReplyGenerationFinished: {CorrelationId}",
                            x.Saga.CorrelationId);

                        var newSubscription = new StreamingSendMessageSubscription(
                            x.Message.ProxyStateId,
                            x.Message.SendMessageInput,
                            x.Message.SendMessageStepId,
                            x.Message.StackEntries);
                        x.Saga.Subscriptions = [newSubscription];
                    })
                .Request(
                    StreamingSendMessageRequest,
                    x =>
                        new StreamingSendMessageRequest(
                            x.Saga.CorrelationId,
                            x.Saga.Subscriptions,
                            string.Join('\n', x.Saga.PartialAnswers.Values)))
                .Publish(
                    x => new OnStreamingSendMessageFinishedEvent(
                        x.Saga.ProxyStateId!,
                        x.Saga.RecommendReplyStepId!,
                        string.Join('\n', x.Saga.PartialAnswers.Values),
                        x.Saga.Subscriptions)),

            // we don't need to wait for the response anymore as the stream finishes immediately
            Ignore(StreamingSendMessageRequest.Completed),
            Ignore(StreamingSendMessageRequest.TimeoutExpired));
    }

    private void HandleStreamingSendMessageRequestPendingState()
    {
        WhenEnter(
            StreamingSendMessageRequest.Pending,
            x => x.Then(x => x.Saga.StreamingSendMessageContext = new StreamingSendMessageContext([])));

        During(
            StreamingSendMessageRequest.Pending,
            When(StreamingSendMessageRequest.Completed)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "StreamingSendMessageRequest.Pending: StreamingSendMessageRequest.Completed: {CorrelationId}, {StreamingSendMessageContext}",
                            x.Saga.CorrelationId,
                            x.Saga.StreamingSendMessageContext);
                    })
                .IfElse(
                    x => x.Saga.StreamingSendMessageContext.NewSubscribers.Count > 0,
                    MakeNewSubscriptionsCatchUpToStreamProgress,
                    x => x
                        .IfElse(
                            x => ShouldSendMessageToSubscribers(x.Saga),
                            x => x
                                .Request(
                                    StreamingSendMessageRequest,
                                    x =>
                                        new StreamingSendMessageRequest(
                                            x.Saga.CorrelationId,
                                            x.Saga.Subscriptions,
                                            AdvanceStream(x.Saga)))
                                .If(
                                    x => IsStreamFinished(x.Saga),
                                    x => x.TransitionTo(RecommendedReplyGenerationFinished)),
                            x => x
                                .IfElse(
                                    x => IsStreamFinished(x.Saga),
                                    x => x.TransitionTo(RecommendedReplyGenerationFinished),
                                    x => x.TransitionTo(RecommendedReplyGenerationStarted)))),
            When(OnRecommendedReplyGenerationEmitEvent)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "StreamingSendMessageRequest.Pending: OnRecommendedReplyGenerationEmitEvent: {CorrelationId}, {SequenceNumber}",
                            x.Saga.CorrelationId,
                            x.Message.StreamSequenceNumber);

                        x.Saga.PartialAnswers[x.Message.StreamSequenceNumber] =
                            x.Message.PartialRecommendedReply;
                    }),
            When(OnRecommendedReplyGenerationFinishedEvent)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "StreamingSendMessageRequest.Pending: OnRecommendedReplyGenerationFinishedEvent: {CorrelationId} {FinalSequenceNumber}",
                            x.Saga.CorrelationId,
                            x.Saga.FinalSequenceNumber);

                        x.Saga.FinalSequenceNumber = x.Message.FinalSequenceNumber;
                        x.Saga.ConfidenceScoring = x.Message.ConfidenceScoring;
                    }),
            When(StartStreamingSendMessage)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "StreamingSendMessageRequest.Pending: StartStreamingSendMessage: {CorrelationId}",
                            x.Saga.CorrelationId);

                        var newSubscription = new StreamingSendMessageSubscription(
                            x.Message.ProxyStateId,
                            x.Message.SendMessageInput,
                            x.Message.SendMessageStepId,
                            x.Message.StackEntries);
                        x.Saga.StreamingSendMessageContext.NewSubscribers.Add(newSubscription);
                    }),
            Ignore(StreamingSendMessageRequest.TimeoutExpired));
    }

    private EventActivityBinder<StreamingRecommendedReplyState, StreamingSendMessageResponse>
        MakeNewSubscriptionsCatchUpToStreamProgress(
            EventActivityBinder<StreamingRecommendedReplyState, StreamingSendMessageResponse> x)
    {
        return x
            .Request(
                StreamingSendMessageRequest,
                x =>
                    new StreamingSendMessageRequest(
                        x.Saga.CorrelationId,
                        x.Saga.StreamingSendMessageContext.NewSubscribers,
                        string.Join(
                            "\n",
                            GetStreamedPartialAnswers(x.Saga))))
            .Then(
                x =>
                {
                    x.Saga.Subscriptions.AddRange(
                        x.Saga.StreamingSendMessageContext.NewSubscribers);
                    x.Saga.StreamingSendMessageContext.NewSubscribers.Clear();
                });
    }

    private string AdvanceStream(StreamingRecommendedReplyState saga)
    {
        _logger.LogInformation(
            "AdvanceStream: {NextSequenceNumberToSend}, {PartialAnswersCount}",
            saga.NextSequenceNumberToSend,
            saga.PartialAnswers.Count);

        var sb = new StringBuilder();
        for (var i = saga.NextSequenceNumberToSend;
             i < saga.PartialAnswers.Count;
             i++)
        {
            sb.Append(saga.PartialAnswers[i]);
            sb.Append('\n');
        }

        saga.NextSequenceNumberToSend = saga.PartialAnswers.Count;

        return sb.ToString().Trim();
    }

    private string GetStreamedPartialAnswers(StreamingRecommendedReplyState saga)
    {
        _logger.LogInformation(
            "GetStreamedPartialAnswers: {NextSequenceNumberToSend}, {PartialAnswersCount}",
            saga.NextSequenceNumberToSend,
            saga.PartialAnswers.Count);

        var sb = new StringBuilder();
        for (var i = 0;
             i < saga.NextSequenceNumberToSend;
             i++)
        {
            sb.Append(saga.PartialAnswers[i]);
            sb.Append('\n');
        }

        return sb.ToString().Trim();
    }

    private static bool ShouldSendMessageToSubscribers(StreamingRecommendedReplyState saga)
    {
        return AreAllPartialAnswersInOrder(saga) && HasUnsentPartialAnswers(saga) && HasSubscribers(saga);
    }

    private static bool HasSubscribers(StreamingRecommendedReplyState saga)
    {
        return saga.Subscriptions.Count > 0;
    }

    private static bool AreAllPartialAnswersInOrder(StreamingRecommendedReplyState saga)
    {
        return saga.PartialAnswers.Count - 1 == saga.PartialAnswers.Keys.Max();
    }

    private static bool HasUnsentPartialAnswers(StreamingRecommendedReplyState saga)
    {
        return saga.NextSequenceNumberToSend < saga.PartialAnswers.Count;
    }

    private static bool IsStreamFinished(StreamingRecommendedReplyState saga)
    {
        return saga.FinalSequenceNumber != null &&
               saga.FinalSequenceNumber == saga.NextSequenceNumberToSend - 1;
    }

    private Guid GenerateCorrelationId(string proxyStateId, string recommendReplyStepId)
    {
        // we need an id that is unique to the flow state and the recommend reply step
        var id = $"{proxyStateId}:{recommendReplyStepId}";
        var hash = MD5.HashData(Encoding.UTF8.GetBytes(id));
        var guid = new Guid(hash);

        _logger.LogInformation(
            "GenerateEventCorrelationId: {ProxyStateId}, {RecommendReplyStepId}, {Guid}",
            proxyStateId,
            recommendReplyStepId,
            guid);

        return guid;
    }
}