﻿using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.CommerceHub.Models.Vtex.ViewModels;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Vtex;

[TriggerGroup(ControllerNames.Vtex)]
public class CreateVtexAuthentication
    : ITrigger<
        CreateVtexAuthentication.CreateVtexAuthenticationInput,
        CreateVtexAuthentication.CreateVtexAuthenticationOutput>
{
    private readonly IVtexAuthenticationService _vtexAuthenticationService;

    public CreateVtexAuthentication(IVtexAuthenticationService vtexAuthenticationService)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
    }

    public class CreateVtexAuthenticationInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("credential")]
        public VtexCredential Credential { get; set; }

        public CreateVtexAuthenticationInput(string sleekflowCompanyId, VtexCredential credential)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Credential = credential;
        }
    }

    public class CreateVtexAuthenticationOutput
    {
        [JsonProperty("vtex_authentication")]
        public VtexAuthenticationViewModel VtexAuthentication { get; set; }

        [JsonConstructor]
        public CreateVtexAuthenticationOutput(VtexAuthenticationViewModel vtexAuthentication)
        {
            VtexAuthentication = vtexAuthentication;
        }
    }

    public async Task<CreateVtexAuthenticationOutput> F(
        CreateVtexAuthenticationInput input)
    {
        var title = ExtractStoreNameFromDomain(input.Credential.Domain);

        var authentication = await _vtexAuthenticationService.AuthenticateAsync(
            input.SleekflowCompanyId,
            title,
            input.Credential);

        return new CreateVtexAuthenticationOutput(
            new VtexAuthenticationViewModel(authentication, true));
    }

    private static string ExtractStoreNameFromDomain(string domain)
    {
        // VTEX domains pattern:
        // https://{accountName}.{environment}.com
        try
        {
            return Regex.Match(domain, @"https?://([^.]+)\.").Groups[1].Value;
        }
        catch
        {
            return domain;
        }
    }
}