﻿using System.Collections.Concurrent;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.CrmHub.Models.Sequences;
using Sleekflow.CrmHub.Sequences;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests;

public class SequenceServiceTests
{
    private static readonly string MockId = $"mocked-id-{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}";

    [SetUp]
    public void TestSetup()
    {
        // Method intentionally left empty.
    }

    [TearDown]
    public async Task TearDown()
    {
        var sequenceRepository = GetSequenceRepository();

        await sequenceRepository.DeleteAsync(
            MockId,
            MockId);
    }

    [Test]
    public async Task GetSequenceNextValueTests()
    {
        var sequenceRepository = GetSequenceRepository();

        var sequenceService = new SequenceService(
            NullLogger<SequenceService>.Instance,
            sequenceRepository);

        var firstValue = await sequenceService.GetNextValueAsync(MockId);
        var secondValue = await sequenceService.GetNextValueAsync(MockId);
        var thirdValue = await sequenceService.GetNextValueAsync(MockId);

        Assert.That(firstValue, Is.EqualTo(1));
        Assert.That(secondValue, Is.EqualTo(2));
        Assert.That(thirdValue, Is.EqualTo(3));

        await sequenceService.DeleteAsync(MockId);
        var sequence = await sequenceRepository.GetOrDefaultAsync(MockId, MockId);

        Assert.That(sequence, Is.Null);
    }

    [Test]
    public async Task ConcurrencyTests()
    {
        var sequenceRepository = GetSequenceRepository();

        var sequenceService = new SequenceService(
            NullLogger<SequenceService>.Instance,
            sequenceRepository);

        // Concurrent initialize
        Task[] tasks = new Task[5];
        for (int i = 0; i < tasks.Length; i++)
        {
            tasks[i] = sequenceRepository.InitializeSequenceSafeAsync(
                new Sequence(
                    MockId,
                    0,
                    null));
        }

        Assert.DoesNotThrowAsync(
            async () =>
            {
                await Task.WhenAll(tasks);
            });

        await sequenceService.DeleteAsync(MockId);

        // Concurrent get value
        var values = new ConcurrentBag<long>();
        var exceptedValues = Enumerable.Range(1, 10);

        tasks = new Task[10];
        for (int i = 0; i < tasks.Length; i++)
        {
            tasks[i] = sequenceService.GetNextValueAsync(MockId)
                .ContinueWith(
                    t =>
                    {
                        values.Add(t.Result);
                    });
        }

        await Task.WhenAll(tasks);

        Assert.That(values.OrderBy(v => v).ToList(), Is.EquivalentTo(exceptedValues));
        await sequenceService.DeleteAsync(MockId);
    }

    private SequenceRepository GetSequenceRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var sequenceRepository = new SequenceRepository(
            NullLogger<BaseRepository<Sequence>>.Instance,
            serviceProvider);

        return sequenceRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }
}