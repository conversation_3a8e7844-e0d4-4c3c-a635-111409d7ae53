using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Wabas;

[TriggerGroup(ControllerNames.Wabas)]
public class GetWhatsappCloudApiWaba
    : ITrigger<
        GetWhatsappCloudApiWaba.GetWhatsappCloudApiWabaInput,
        GetWhatsappCloudApiWaba.GetWhatsappCloudApiWabaOutput>
{
    private readonly IWabaService _wabaService;

    public GetWhatsappCloudApiWaba(IWabaService wabaService)
    {
        _wabaService = wabaService;
    }

    public class GetWhatsappCloudApiWabaInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiWabaInput(string sleekflowCompanyId, string wabaId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
        }
    }

    public class GetWhatsappCloudApiWabaOutput
    {
        [JsonProperty("waba")]
        public WabaDto Waba { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiWabaOutput(WabaDto waba)
        {
            Waba = waba;
        }
    }

    public async Task<GetWhatsappCloudApiWabaOutput> F(GetWhatsappCloudApiWabaInput getWhatsappCloudApiWabaInput)
    {
        var wabaId = getWhatsappCloudApiWabaInput.WabaId;
        var sleekflowCompanyId = getWhatsappCloudApiWabaInput.SleekflowCompanyId;
        var waba = await _wabaService.GetWabaOrDefaultAsync(wabaId, sleekflowCompanyId);
        if (waba is null)
        {
            throw new SfNotFoundObjectException(wabaId);
        }

        var wabaDto = new WabaDto(waba);
        wabaDto.WabaPhoneNumbers = wabaDto.WabaPhoneNumbers
            .Where(p => p.SleekflowCompanyId == sleekflowCompanyId || p.SleekflowCompanyId == null).ToList();
        return new GetWhatsappCloudApiWabaOutput(wabaDto);
    }
}