﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.SchemafulObjects;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId(ContainerNames.SchemafulObject)]
public class SchemafulObject : AuditEntity, IHasETag, IHasSleekflowUserProfileId
{
    public const string PropertyNameSchemaId = "schema_id";
    public const string PropertyNamePrimaryPropertyValue = "primary_property_value";
    public const string PropertyNamePropertyValues = "property_values";
    public const string PropertyNameIndexedPropertyValues = "indexed_property_values";
    public const string PropertyNameCreatedVia = "created_via";
    public const string PropertyNameUpdatedVia = "updated_via";

    [JsonProperty(PropertyNameSchemaId)]
    public string SchemaId { get; }

    [JsonProperty(PropertyNamePrimaryPropertyValue)]
    public string PrimaryPropertyValue { get; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty(PropertyNamePropertyValues)]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [JsonProperty(PropertyNameIndexedPropertyValues)]
    public Dictionary<string, object?> IndexedPropertyValues { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty(PropertyNameCreatedVia)]
    public string CreatedVia { get; set; }

    [JsonProperty(PropertyNameUpdatedVia)]
    public string UpdatedVia { get; set; }

    [JsonConstructor]
    public SchemafulObject(
        string id,
        string schemaId,
        string primaryPropertyValue,
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        Dictionary<string, object?> propertyValues,
        Dictionary<string, object?> indexedPropertyValues,
        string? eTag,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string? createdVia,
        string? updatedVia,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(id, SysTypeNames.SchemafulObject, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        SleekflowUserProfileId = sleekflowUserProfileId;
        PropertyValues = propertyValues;
        IndexedPropertyValues = indexedPropertyValues;
        ETag = eTag;
        CreatedVia = createdVia ?? string.Empty;
        UpdatedVia = updatedVia ?? string.Empty;
    }
}