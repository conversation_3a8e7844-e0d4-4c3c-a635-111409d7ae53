using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class GetCompanyAgentConfigList(ICompanyAgentConfigService companyAgentConfigService)
    : ITrigger<GetCompanyAgentConfigList.GetCompanyAgentConfigListInput, GetCompanyAgentConfigList.GetCompanyAgentConfigListOutput>
{
    [method: JsonConstructor]
    public class GetCompanyAgentConfigListInput(string sleekflowCompanyId) : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; } = sleekflowCompanyId;
    }

    [method: JsonConstructor]
    public class GetCompanyAgentConfigListOutput(List<CompanyAgentConfigDto> companyAgentConfigs)
    {
        [JsonProperty("company_agent_configs")]
        public List<CompanyAgentConfigDto> CompanyAgentConfigs { get; set; } = companyAgentConfigs;
    }

    public async Task<GetCompanyAgentConfigListOutput> F(GetCompanyAgentConfigListInput input)
    {
        var configs = await companyAgentConfigService.GetObjectsAsync(input.SleekflowCompanyId);
        return new GetCompanyAgentConfigListOutput(configs.Select(c => new CompanyAgentConfigDto(c)).ToList());
    }
}