﻿using Newtonsoft.Json.Linq;

namespace Sleekflow.CrmHub.Utils;

public static class JTokenUtils
{
    public static JToken? ToJToken(object? o)
    {
        return o == null ? null : JToken.FromObject(o);
    }

    public static bool DeepEquals(JToken? jToken1, JToken? jToken2)
    {
        if (jToken1 == null && jToken2 == null)
        {
            return true;
        }

        var isJToken1Date = jToken1 is { Type: JTokenType.Date };
        var isJToken2Date = jToken2 is { Type: JTokenType.Date };
        if (!isJToken1Date || !isJToken2Date)
        {
            return JToken.DeepEquals(jToken1, jToken2);
        }

        var dateTime1 = jToken1!.Value<DateTimeOffset>();
        var dateTime2 = jToken2!.Value<DateTimeOffset>();

        var seconds1 = dateTime1.Ticks / TimeSpan.TicksPerSecond;
        var seconds2 = dateTime2.Ticks / TimeSpan.TicksPerSecond;

        return seconds1.CompareTo(seconds2) == 0;
    }
}