using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.TransactionLogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.TransactionLogs)]
public class BulkPatchBusinessBalanceTransactionLog
    : ITrigger<
        BulkPatchBusinessBalanceTransactionLog.BulkPatchBusinessBalanceTransactionLogInput,
        BulkPatchBusinessBalanceTransactionLog.BulkPatchBusinessBalanceTransactionLogOutput>
{
    private readonly IBus _bus;
    private readonly IWabaService _wabaService;
    private readonly IAuditLogService _auditLogService;
    private readonly ILogger<BulkPatchBusinessBalanceTransactionLog> _logger;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;

    public BulkPatchBusinessBalanceTransactionLog(
        IBus bus,
        IWabaService wabaService,
        IAuditLogService auditLogService,
        ILogger<BulkPatchBusinessBalanceTransactionLog> logger,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    {
        _bus = bus;
        _logger = logger;
        _wabaService = wabaService;
        _auditLogService = auditLogService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public class BulkPatchBusinessBalanceTransactionLogInput
    {
        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("default_transaction_handling_fee_rate")]
        public decimal? DefaultTransactionHandlingFeeRate { get; set; }

        [JsonConstructor]
        public BulkPatchBusinessBalanceTransactionLogInput(
            string facebookBusinessId,
            decimal? defaultTransactionHandlingFeeRate)
        {
            FacebookBusinessId = facebookBusinessId;
            DefaultTransactionHandlingFeeRate = defaultTransactionHandlingFeeRate;
        }
    }

    public class BulkPatchBusinessBalanceTransactionLogOutput
    {
        [JsonProperty("total_count")]
        public int TotalCount { get; set; }

        [JsonProperty("un_patched_count")]
        public int UnPatchedCount { get; set; }

        [JsonProperty("un_patched_ids")]
        public List<string> UnPatchedIds { get; set; }

        [JsonProperty("patched_count")]
        public int PatchedCount { get; set; }

        [JsonProperty("patched_ids")]
        public List<string> PatchedIds { get; set; }

        [JsonConstructor]
        public BulkPatchBusinessBalanceTransactionLogOutput(
            int totalCount,
            int unPatchedCount,
            List<string> unPatchedIds,
            int patchedCount,
            List<string> patchedIds)
        {
            TotalCount = totalCount;
            UnPatchedCount = unPatchedCount;
            UnPatchedIds = unPatchedIds;
            PatchedCount = patchedCount;
            PatchedIds = patchedIds;
        }
    }

    public async Task<BulkPatchBusinessBalanceTransactionLogOutput> F(
        BulkPatchBusinessBalanceTransactionLogInput bulkPatchBusinessBalanceTransactionLogInput)
    {
        var facebookBusinessId = bulkPatchBusinessBalanceTransactionLogInput.FacebookBusinessId;
        var defaultTransactionHandlingFeeRate =
            bulkPatchBusinessBalanceTransactionLogInput.DefaultTransactionHandlingFeeRate ??
            MarkupProfile.DefaultTransactionHandlingFeeRate;
        int patchedCount = 0, unPatchedCount = 0;
        List<string> patchedIds = new (), unPatchedIds = new ();
        var businessBalanceTransactionLogs = await _businessBalanceTransactionLogService
            .GetConversationUsageTransactionLogAsync(facebookBusinessId);
        var unPatchedBusinessBalanceTransactionLog = businessBalanceTransactionLogs.Where(
            b =>
                b.MarkupProfileSnapshot.TransactionHandlingFeeRate is null or 0).ToList();
        foreach (var businessBalanceTransactionLog in unPatchedBusinessBalanceTransactionLog)
        {
            try
            {
                var businessTransactionLogInstance = await _businessBalanceTransactionLogService
                    .GetOrDefaultBusinessBalanceTransactionLogAsync(
                        businessBalanceTransactionLog.Id,
                        businessBalanceTransactionLog.FacebookBusinessId);

                var businessBalanceTransactionLogInstanceSnapshot =
                    JsonConvert.DeserializeObject<BusinessBalanceTransactionLog>(
                        JsonConvert.SerializeObject(businessTransactionLogInstance));

                var markupProfile = businessTransactionLogInstance.MarkupProfileSnapshot;

                if (markupProfile.BusinessInitiatedConversationFeePriceMarkupPercentage -
                    defaultTransactionHandlingFeeRate >= 0
                    && markupProfile.UserInitiatedConversationFeePriceMarkupPercentage -
                    defaultTransactionHandlingFeeRate >= 0)
                {
                    markupProfile.BusinessInitiatedConversationFeePriceMarkupPercentage -=
                        defaultTransactionHandlingFeeRate;
                    markupProfile.UserInitiatedConversationFeePriceMarkupPercentage -=
                        defaultTransactionHandlingFeeRate;
                    markupProfile.TransactionHandlingFeeRate = defaultTransactionHandlingFeeRate;
                }
                else
                {
                    throw new Exception($"Unable to deduct {MarkupProfile.DefaultTransactionHandlingFeeRate}");
                }

                var facebookWabaId = businessTransactionLogInstance.FacebookWabaId;
                var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);

                var reconstructedTransactionLog =
                    _businessBalanceTransactionLogService.ConstructBusinessBalanceTransactionLog(
                        facebookWabaId,
                        businessTransactionLogInstance.FacebookBusinessId,
                        waba?.FacebookWabaSnapshot?["timezone_id"]?.ToString(),
                        markupProfile,
                        0,
                        businessTransactionLogInstance.WabaConversationUsage);

                businessTransactionLogInstance.Used = reconstructedTransactionLog.Used;
                businessTransactionLogInstance.Markup = reconstructedTransactionLog.Markup;
                businessTransactionLogInstance.TransactionHandlingFee =
                    reconstructedTransactionLog.TransactionHandlingFee;
                businessTransactionLogInstance.MarkupProfileSnapshot =
                    reconstructedTransactionLog.MarkupProfileSnapshot;
                businessTransactionLogInstance.UpdatedAt = DateTimeOffset.UtcNow;

                await _auditLogService.AuditBusinessBalanceTransactionLogAsync(
                    businessBalanceTransactionLogInstanceSnapshot,
                    businessBalanceTransactionLogInstanceSnapshot.FacebookBusinessId,
                    AuditingOperation.ResetBusinessBalanceTransactionLogCalculatedState,
                    new Dictionary<string, object?>
                    {
                        {
                            "change", businessTransactionLogInstance
                        },
                        {
                            "markup_profile", businessTransactionLogInstance.MarkupProfileSnapshot
                        }
                    });

                var upsertStatus = await _businessBalanceTransactionLogService
                    .UpsertBusinessBalanceTransactionLogAsync(businessTransactionLogInstance);

                if (upsertStatus == 0)
                {
                    throw new Exception($"Unable to deduct {MarkupProfile.DefaultTransactionHandlingFeeRate}");
                }

                patchedCount++;
                patchedIds.Add(businessBalanceTransactionLog.Id);
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    exception,
                    "Unhandled transaction logs {TransactionLog}",
                    JsonConvert.SerializeObject(businessBalanceTransactionLog));
                unPatchedCount++;
                unPatchedIds.Add(businessBalanceTransactionLog.Id);
            }
        }

        if (patchedCount > 0)
        {
            await _bus.Publish(new OnCloudApiBusinessBalanceResynchronizationEvent(facebookBusinessId));
        }

        _logger.LogInformation(
            "Completed bulk patch business balance transaction log {FacebookBusinessId}/{Rate} - Results {LogCount}/{UnPatchedCount}/{UnPatchedId}/{PatchedCount}/{PatchedIds}",
            facebookBusinessId,
            defaultTransactionHandlingFeeRate,
            businessBalanceTransactionLogs.Count,
            unPatchedCount,
            string.Join(',', unPatchedIds),
            patchedCount,
            string.Join(',', patchedIds));

        return new BulkPatchBusinessBalanceTransactionLogOutput(
            businessBalanceTransactionLogs.Count,
            unPatchedCount,
            unPatchedIds,
            patchedCount,
            patchedIds);
    }
}