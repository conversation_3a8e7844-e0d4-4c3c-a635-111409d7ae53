using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyConfigs;

[TriggerGroup(
    ControllerNames.CompanyConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class ClearCompanyConfig
    : ITrigger<ClearCompanyConfig.ClearCompanyConfigInput, ClearCompanyConfig.ClearCompanyConfigOutput>
{
    private readonly ILogger _logger;
    private readonly ICompanyConfigService _companyConfigService;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public ClearCompanyConfig(
        ILogger<ClearCompanyConfig> logger,
        ICompanyConfigService companyConfigService,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _logger = logger;
        _companyConfigService = companyConfigService;
        _authorizationContext = authorizationContext;
    }

    public class ClearCompanyConfigInput
    {
    }

    public class ClearCompanyConfigOutput
    {
    }

    public async Task<ClearCompanyConfigOutput> F(ClearCompanyConfigInput input)
    {
        _logger.LogInformation(
            "Deleting company config with id {CompanyId} {StaffId} {TeamIds}",
            _authorizationContext.SleekflowCompanyId,
            _authorizationContext.SleekflowStaffId,
            _authorizationContext.SleekflowTeamIds);

        await _companyConfigService.ClearAsync(_authorizationContext.SleekflowCompanyId!);

        return new ClearCompanyConfigOutput();
    }
}