using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetConnections : ITrigger
{
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public GetConnections(
        ISalesforceConnectionService salesforceConnectionService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceConnectionService = salesforceConnectionService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class GetConnectionsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetConnectionsInput(
            string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class SalesforceConnectionDto : IHasSleekflowCompanyId
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("organization_id")]
        public string OrganizationId { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("environment")]
        public string Environment { get; set; }

        [JsonProperty("is_active")]
        public bool IsActive { get; set; }

        [JsonProperty("is_api_request_limit_exceeded")]
        public bool IsApiRequestLimitExceeded { get; set; }

        [JsonConstructor]
        public SalesforceConnectionDto(
            SalesforceConnection connection)
        {
            Id = connection.Id;
            SleekflowCompanyId = connection.SleekflowCompanyId;
            OrganizationId = connection.OrganizationId;
            Name = connection.Name;
            Environment = connection.Environment;
            IsActive = connection.IsActive;
        }
    }

    public class GetConnectionsOutput
    {
        [JsonProperty("connections")]
        [Required]
        public List<SalesforceConnectionDto> Connections { get; set; }

        [JsonConstructor]
        public GetConnectionsOutput(
            List<SalesforceConnectionDto> connections)
        {
            Connections = connections;
        }
    }

    public async Task<GetConnectionsOutput> F(
        GetConnectionsInput getConnectionsInput)
    {
        var connections =
            await _salesforceConnectionService.GetConnectionsAsync(getConnectionsInput.SleekflowCompanyId);

        var connectionDtos = new List<SalesforceConnectionDto>();
        foreach (var connection in connections)
        {
            var connectionDto = new SalesforceConnectionDto(connection);

            connectionDto.IsApiRequestLimitExceeded =
                await _salesforceAuthenticationService.IsApiRequestLimitExceededAsync(
                    connection.AuthenticationId,
                    connection.SleekflowCompanyId);

            connectionDtos.Add(connectionDto);
        }

        return new GetConnectionsOutput(connectionDtos);
    }
}