using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class CreateObjectV2 : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public CreateObjectV2(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class CreateObjectV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public CreateObjectV2Input(
            string sleekflowCompanyId,
            string connectionId,
            Dictionary<string, object?> dict,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            Dict = dict;
            EntityTypeName = entityTypeName;
        }
    }

    public class CreateObjectV2Output
    {
        [JsonProperty("is_async_operation")]
        public bool IsAsyncOperation { get; set; }

        [JsonConstructor]
        public CreateObjectV2Output(bool isAsyncOperation)
        {
            IsAsyncOperation = isAsyncOperation;
        }
    }

    public async Task<CreateObjectV2Output> F(CreateObjectV2Input createObjectV2Input)
    {
        var connection = await _salesforceConnectionService.GetByIdAsync(
            createObjectV2Input.ConnectionId,
            createObjectV2Input.SleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                createObjectV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var getFieldsOutput =
            await _salesforceObjectService.GetFieldsAsync(authentication, createObjectV2Input.EntityTypeName);
        var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();
        var updatableBooleanFieldNames = getFieldsOutput.UpdatableFields
            .Where(f => f.Type == "boolean")
            .Select(f => f.Name)
            .ToList();
        var updatableDateFieldNames = getFieldsOutput.UpdatableFields
            .Where(f => f.Type == "date")
            .Select(f => f.Name)
            .ToList();
        var updatableDateTimeFieldNames = getFieldsOutput.UpdatableFields
            .Where(f => f.Type == "datetime")
            .Select(f => f.Name)
            .ToList();

        var dict = createObjectV2Input.Dict
            .Where(e => updatableFieldNames.Contains(e.Key))
            .ToDictionary(e => e.Key, e =>
            {
                if (updatableBooleanFieldNames.Contains(e.Key))
                {
                    if (e.Value is string value && !string.IsNullOrEmpty(value))
                    {
                        return value.ToLower();
                    }

                    return null;
                }

                if (updatableDateFieldNames.Contains(e.Key))
                {
                    if (e.Value is DateTimeOffset dateValue)
                    {
                        return dateValue.ToString("yyyy-MM-dd");
                    }

                    if (e.Value is string dateString && DateTimeOffset.TryParse(dateString, out var parsedDateValue))
                    {
                        return parsedDateValue.ToString("yyyy-MM-dd");
                    }

                    return null;
                }

                if (updatableDateTimeFieldNames.Contains(e.Key))
                {
                    if (e.Value is DateTimeOffset dateTimeValue)
                    {
                        return dateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                    }

                    if (e.Value is string dateTimeString && DateTimeOffset.TryParse(dateTimeString, out var parsedDateTimeValue))
                    {
                        return parsedDateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                    }

                    return null;
                }

                return e.Value;
            });

        await _salesforceObjectService.CreateAsync(
            authentication,
            dict,
            createObjectV2Input.EntityTypeName);

        return new CreateObjectV2Output(false);
    }
}