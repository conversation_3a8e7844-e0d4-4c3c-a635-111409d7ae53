﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.CommerceHub.Payments;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Payments;

[TriggerGroup(ControllerNames.Payments)]
public class CreateOrderPayment
    : ITrigger<
        CreateOrderPayment.CreateOrderPaymentInput,
        CreateOrderPayment.CreateOrderPaymentOutput>
{
    private readonly IOrderService _orderService;
    private readonly IPaymentService _paymentService;

    public CreateOrderPayment(
        IOrderService orderService,
        IPaymentService paymentService)
    {
        _orderService = orderService;
        _paymentService = paymentService;
    }

    public class CreateOrderPaymentInput : IHasSleekflowStaff, IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty("order_id")]
        public string OrderId { get; set; }

        [Required]
        [JsonProperty("payment_provider_name")]
        [RegularExpression("^(Stripe)$")]
        public string PaymentProviderName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [ValidateIsoLanguageCode]
        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }

        [JsonProperty("expired_at")]
        [Required]
        public DateTimeOffset ExpiredAt { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty("return_to_url")]
        [Required]
        public string ReturnToUrl { get; set; }

        [JsonConstructor]
        public CreateOrderPaymentInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string orderId,
            string paymentProviderName,
            string languageIsoCode,
            DateTimeOffset expiredAt,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string returnToUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            OrderId = orderId;
            PaymentProviderName = paymentProviderName;
            LanguageIsoCode = languageIsoCode;
            ExpiredAt = expiredAt;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            ReturnToUrl = returnToUrl;
        }
    }

    public class CreateOrderPaymentOutput
    {
        [JsonProperty("payment_redirection_link")]
        public string PaymentRedirectionLink { get; set; }

        [JsonConstructor]
        public CreateOrderPaymentOutput(
            string paymentRedirectionLink)
        {
            PaymentRedirectionLink = paymentRedirectionLink;
        }
    }

    public async Task<CreateOrderPaymentOutput> F(
        CreateOrderPaymentInput createOrderPaymentInput)
    {
        var order = await _orderService.GetAsync(
            createOrderPaymentInput.OrderId,
            createOrderPaymentInput.SleekflowCompanyId);

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createOrderPaymentInput.SleekflowStaffId,
            createOrderPaymentInput.SleekflowStaffTeamIds);

        var paymentOutput = await _paymentService.CreateOrderPaymentAsync(
            order,
            createOrderPaymentInput.PaymentProviderName,
            createOrderPaymentInput.LanguageIsoCode,
            sleekflowStaff,
            createOrderPaymentInput.ReturnToUrl);

        await _orderService.PatchAndGetOrderAsync(
            order.Id,
            order.SleekflowCompanyId,
            order.StoreId,
            order.SleekflowUserProfileId,
            OrderStatuses.PendingPayment,
            PaymentStatuses.GeneratedPaymentLink,
            order.Metadata,
            DateTimeOffset.UtcNow,
            null,
            sleekflowStaff);

        return new CreateOrderPaymentOutput(paymentOutput.PaymentLink);
    }
}