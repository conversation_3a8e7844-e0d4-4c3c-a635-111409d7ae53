using System;

namespace Sleekflow.IntelligentHub.Models.Events;

public class ScheduleFollowUpMessage
{
    public string GroupChatId { get; set; }

    public string FollowUpMessage { get; set; }

    public string CompanyId { get; set; }

    public string ObjectId { get; set; }

    public DateTime ScheduledTimeUtc { get; set; }

    // Additional context data needed for proper follow-up messaging
    public string? StateId { get; set; }

    public string? ContactId { get; set; }

    public string? AgentId { get; set; }

    public string? ToolsConfigJson { get; set; }

    public string? LeadNurturingToolsConfigJson { get; set; }

    // User message at the time follow-up was scheduled (for comparison)
    public string? OriginalUserMessage { get; set; }

    public ScheduleFollowUpMessage(
        string groupChatId,
        string followUpMessage,
        string companyId,
        string objectId,
        DateTime scheduledTimeUtc,
        string? stateId = null,
        string? contactId = null,
        string? agentId = null,
        string? toolsConfigJson = null,
        string? leadNurturingToolsConfigJson = null,
        string? originalUserMessage = null)
    {
        GroupChatId = groupChatId;
        FollowUpMessage = followUpMessage;
        CompanyId = companyId;
        ObjectId = objectId;
        ScheduledTimeUtc = scheduledTimeUtc;
        StateId = stateId;
        ContactId = contactId;
        AgentId = agentId;
        ToolsConfigJson = toolsConfigJson;
        LeadNurturingToolsConfigJson = leadNurturingToolsConfigJson;
        OriginalUserMessage = originalUserMessage;
    }
}