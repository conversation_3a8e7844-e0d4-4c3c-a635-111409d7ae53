using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public interface ILeadNurturingAgentDefinitions
{
    string LeadClassifierAgentName { get; }

    string DecisionAgentName { get; }

    string StrategyAgentName { get; }

    string KnowledgeRetrievalAgentName { get; }

    string ReviewerAgentName { get; }

    string ResponseCrafterAgentName { get; }

    string TransitioningResponseCrafterAgentName { get; }

    string InformationGatheringResponseCrafterAgentName { get; }

    string ActionAgentName { get; }

    string ConfirmationAgentName { get; }

    string LeadAssignmentPlanningAgentName { get; }

    string DemoSchedulingPlanningAgentName { get; }

    string FollowUpAgentName { get; }

    /*
     * CoreAgents
     */

    ChatCompletionAgent GetLeadClassifierAgent(Kernel kernel, PromptExecutionSettings settings);

    ChatCompletionAgent GetDecisionAgent(Kernel kernel, PromptExecutionSettings settings);

    ChatCompletionAgent GetStrategyAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string additionalInstructionStrategy);

    ChatCompletionAgent GetKnowledgeRetrievalAgent(
        Kernel kernel,
        IAgenticKnowledgePlugin knowledgePlugin,
        PromptExecutionSettings settings,
        ILeadNurturingCollaborationChatCacheService leadNurturingCollaborationChatCacheService);

    ChatCompletionAgent GetReviewerAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService);

    /*
     * ResponseAgents
     */

    ChatCompletionAgent GetResponseCrafterAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService,
        string additionalInstructionResponse);

    ChatCompletionAgent GetTransitioningResponseCrafterAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService);

    ChatCompletionAgent GetInformationGatheringResponseCrafterAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService);

    /*
     * ActionAgents
     */

    ChatCompletionAgent GetConfirmationAgent(Kernel kernel, PromptExecutionSettings settings);

    ChatCompletionAgent GetActionAgent(Kernel kernel, PromptExecutionSettings settings);

    /*
     * PlanningAgents
     */

    ChatCompletionAgent GetLeadAssignmentPlanningAgent(Kernel kernel, PromptExecutionSettings settings);

    ChatCompletionAgent GetDemoSchedulingPlanningAgent(Kernel kernel, PromptExecutionSettings settings);

    ChatCompletionAgent GetFollowUpAgent(
        Kernel kernel,
        PromptExecutionSettings settings);
}

public partial class LeadNurturingAgentDefinitions : ILeadNurturingAgentDefinitions, IScopedService
{
    public string LeadClassifierAgentName => "LeadClassifierAgent";

    public string DecisionAgentName => "DecisionAgent";

    public string StrategyAgentName => "StrategyAgent";

    public string ResponseCrafterAgentName => "ResponseCrafterAgent";

    public string TransitioningResponseCrafterAgentName => "TransitioningResponseCrafterAgent";

    public string KnowledgeRetrievalAgentName => "KnowledgeRetrievalAgent";

    public string LeadAssignmentPlanningAgentName => "LeadAssignmentPlanningAgent";

    public string DemoSchedulingPlanningAgentName => "DemoSchedulingPlanningAgent";

    public string ReviewerAgentName => "ReviewerAgent";

    public string InformationGatheringResponseCrafterAgentName => "InformationGatheringResponseCrafterAgent";

    public string ConfirmationAgentName => "ConfirmationAgent";

    public string ActionAgentName => "ActionAgent";

    public string FollowUpAgentName => "FollowUpAgent";

    protected string GetSharedSystemPrompt() =>
        """
        Context: A collaborative group chat where agents work together to qualify, nurture, and manage customer leads through conversations.
        Goal: Nurture leads or perform tasks efficiently, adapting to various situations while maintaining a natural conversational flow.

        Overview of agents:

        Core Agents:
        - LeadClassifierAgent: Classifies leads into hot, warm, cold, or existing_customer based on conversation analysis. Produces a lead score and detailed reasoning.
        - DecisionAgent: Determines next action based on lead classification and conversation context. Can override classification based on specific indicators. Decides whether to continue nurturing, assign to a team, or initiate demo scheduling.
        - StrategyAgent: Advises on nurturing strategies for warm leads. Identifies needed knowledge and provides communication style guidance.
        - KnowledgeRetrievalAgent: Retrieves relevant information from knowledge base. Evaluates if retrieved knowledge answers queries and handles knowledge insufficiency scenarios.
        - ReviewerAgent: Reviews proposed responses for quality. Checks for placeholder content and proper knowledge integration. Ensures correct language is used.

        Response Generation Agents:
        - ResponseCrafterAgent: Creates personalized customer responses based on strategy and knowledge. Ensures responses are relevant and engaging. Handles standard conversational responses.
        - TransitioningResponseCrafterAgent: Crafts responses for conversation transitions. Manages handoff messaging when lead is assigned to teams.
        - InformationGatheringResponseCrafterAgent: Crafts messages specifically to gather additional customer information. Tracks gathered and missing information. Creates natural, conversational information requests.

        Planning Agents:
        - LeadAssignmentPlanningAgent: Focuses specifically on team assignment based on lead classification. Determines appropriate team based on classification and context. Creates detailed assignment records with conversation summaries.
        - DemoSchedulingPlanningAgent: Specializes in handling demo requests. Performs thorough analysis of entire conversation to extract field information. Identifies both direct and indirect mentions. Can combine partial field information across messages. Validates each field value against its requirements.

        Action Agents:
        - ConfirmationAgent: Check if the customer has confirmed to proceed the actions we proposed. If not, it will ask the customer to confirm.
        - ActionAgent: Executes actions based on outputs from other planning agents. Use different tools to perform operations. Provides structured feedback on actions taken and their outcomes.

        Session Flow:
        - Each agent group chat session processes one customer message
        - Produces exactly one response (information request, transition message, or conversational response)
        - Ends after the ReviewerAgent approves the response
        - Each new customer message starts a fresh agent group chat session

        p.s. Each message contains a field "agent_name" to identify the author of the message.
        p.s. CUSTOMER CONVERSATION CONTEXT refers to conversation history between the customer and the assistant. INTERNAL CONVERSATION HISTORY refers to the internal conversation between different agents.

        ALWAYS adhere to the following guidelines:

        """;

    private readonly ISleekflowToolsPlugin _sleekflowToolsPlugin;
    private readonly IChiliPiperPlugin _chiliPiperPlugin;
    private readonly IInformationGatheringPlugin _informationGatheringPlugin;
    private readonly ILoggerFactory _loggerFactory;

    public LeadNurturingAgentDefinitions(
        ISleekflowToolsPlugin sleekflowToolsPlugin,
        IChiliPiperPlugin chiliPiperPlugin,
        IInformationGatheringPlugin informationGatheringPlugin,
        ILoggerFactory loggerFactory)
    {
        _sleekflowToolsPlugin = sleekflowToolsPlugin;
        _chiliPiperPlugin = chiliPiperPlugin;
        _informationGatheringPlugin = informationGatheringPlugin;
        _loggerFactory = loggerFactory;
    }
}