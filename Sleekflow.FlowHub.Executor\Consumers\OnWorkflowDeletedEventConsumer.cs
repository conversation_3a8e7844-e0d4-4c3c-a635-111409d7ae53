using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Statistics;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowDeletedEventConsumerDefinition
    : ConsumerDefinition<OnWorkflowDeletedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowDeletedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowDeletedEventConsumer : IConsumer<OnWorkflowDeletedEvent>
{
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;
    private readonly IWorkflowStepCategoryStatisticsService _workflowStepCategoryStatisticsService;

    public OnWorkflowDeletedEventConsumer(
        IWorkflowWebhookTriggerService workflowWebhookTriggerService,
        IWorkflowStepCategoryStatisticsService workflowStepCategoryStatisticsService)
    {
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
        _workflowStepCategoryStatisticsService = workflowStepCategoryStatisticsService;
    }

    public async Task Consume(ConsumeContext<OnWorkflowDeletedEvent> context)
    {
        var @event = context.Message;

        var triggers = await _workflowWebhookTriggerService.GetWorkflowWebhookTriggersAsync(
            @event.WorkflowId,
            @event.SleekflowCompanyId);

        foreach (var workflowWebhookTrigger in triggers)
        {
            await _workflowWebhookTriggerService.DeleteWorkflowWebhookTriggerAsync(
                workflowWebhookTrigger.Id,
                workflowWebhookTrigger.SleekflowCompanyId);
        }

        await _workflowStepCategoryStatisticsService.MarkWorkflowStepCategoriesInvalidAsync(
            @event.SleekflowCompanyId,
            @event.WorkflowId);


    }
}