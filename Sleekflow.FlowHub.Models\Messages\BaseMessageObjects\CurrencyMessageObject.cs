using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class CurrencyMessageObject : BaseMessageObject
{
    [JsonProperty("fallback_value")]
    public string FallbackValue { get; set; }

    [JsonProperty("code")]
    public string Code { get; set; }

    [JsonProperty("amount_1000")]
    public int Amount1000 { get; set; }

    [JsonConstructor]
    public CurrencyMessageObject(string fallbackValue, string code, int amount1000)
    {
        FallbackValue = fallbackValue;
        Code = code;
        Amount1000 = amount1000;
    }
}