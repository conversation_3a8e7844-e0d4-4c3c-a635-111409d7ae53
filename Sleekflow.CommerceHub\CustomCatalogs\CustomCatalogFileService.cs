using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Blobs;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.CustomCatalogs.Readers;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.Workers;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.CustomCatalogs;

public interface ICustomCatalogFileService
{
    Task<CustomCatalogFile> GetCustomCatalogFileAsync(string id, string sleekflowCompanyId);

    Task<List<CustomCatalogFile>> GetCustomCatalogFilesAsync(string sleekflowCompanyId, string storeId);

    Task<string> GetCustomCatalogCsvTemplateAsync();

    Task<string> GetCustomCatalogCsvTemplateSampleAsync();

    Task PatchCustomCatalogFileFileProcessStatusAsync(
        string id,
        string sleekflowCompanyId,
        string fileProcessStatus);

    Task<string> ProcessCustomCatalogCsvAsync(
        string sleekflowCompanyId,
        string storeId,
        string blobName,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<long> ReadCustomCatalogCsvLineCountAsync(string blobId);

    Task ReadCustomCatalogCsvHeadersAsync(string id, string sleekflowCompanyId);

    Task<(int Count, MyCustomCatalogCsvReaderState? MyCustomCatalogCsvReaderState)> ProcessCustomCatalogCsvBatchAsync(
        string id,
        string sleekflowCompanyId,
        MyCustomCatalogCsvReaderState? myCustomCatalogCsvReaderState);

    Task<bool> DeleteCustomCatalogCsvAsync(string blobId);
}

public class CustomCatalogFileService : ICustomCatalogFileService, IScopedService
{
    private readonly IIdService _idService;
    private readonly IBlobService _blobService;
    private readonly ICustomCatalogFileRepository _customCatalogFileRepository;
    private readonly ICategoryService _categoryService;
    private readonly ICommerceHubWorkerService _commerceHubWorkerService;
    private readonly ILogger<MyCustomCatalogCsvReader> _myCustomCatalogCsvReaderLogger;
    private readonly IStoreService _storeService;
    private readonly IDefaultProductService _defaultProductService;

    public CustomCatalogFileService(
        IIdService idService,
        IBlobService blobService,
        ICustomCatalogFileRepository customCatalogFileRepository,
        ICategoryService categoryService,
        ICommerceHubWorkerService commerceHubWorkerService,
        ILogger<MyCustomCatalogCsvReader> myCustomCatalogCsvReaderLogger,
        IStoreService storeService,
        IDefaultProductService defaultProductService)
    {
        _idService = idService;
        _blobService = blobService;
        _customCatalogFileRepository = customCatalogFileRepository;
        _categoryService = categoryService;
        _commerceHubWorkerService = commerceHubWorkerService;
        _myCustomCatalogCsvReaderLogger = myCustomCatalogCsvReaderLogger;
        _storeService = storeService;
        _defaultProductService = defaultProductService;
    }

    public async Task<CustomCatalogFile> GetCustomCatalogFileAsync(string id, string sleekflowCompanyId)
    {
        var customCatalogFile = await _customCatalogFileRepository.GetAsync(id, sleekflowCompanyId);

        return customCatalogFile;
    }

    public async Task<List<CustomCatalogFile>> GetCustomCatalogFilesAsync(string sleekflowCompanyId, string storeId)
    {
        var customCatalogFiles = await _customCatalogFileRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId AND c.store_id = @storeId AND ARRAY_CONTAINS(c.record_statuses, @recordStatuses) " +
                    "ORDER BY c._ts DESC")
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@storeId", storeId)
                .WithParameter("@recordStatuses", RecordStatuses.Active),
            100);

        return customCatalogFiles;
    }

    public Task<string> GetCustomCatalogCsvTemplateAsync()
    {
        return Task.FromResult(MyCustomCatalogCsvReader.GetCsvTemplate());
    }

    public Task<string> GetCustomCatalogCsvTemplateSampleAsync()
    {
        return Task.FromResult(MyCustomCatalogCsvReader.GetCsvTemplateSample());
    }

    public async Task PatchCustomCatalogFileFileProcessStatusAsync(
        string id,
        string sleekflowCompanyId,
        string fileProcessStatus)
    {
        var customCatalogFile = await _customCatalogFileRepository.GetAsync(
            id,
            sleekflowCompanyId);

        customCatalogFile.FileProcessStatus = fileProcessStatus;

        await _customCatalogFileRepository.UpsertAsync(customCatalogFile, sleekflowCompanyId);
    }

    public async Task<string> ProcessCustomCatalogCsvAsync(
        string sleekflowCompanyId,
        string storeId,
        string blobName,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var blobs = await _blobService.ConvertFileBlobNamesToFilesAsync(
            new List<string>
            {
                blobName
            },
            sleekflowCompanyId,
            storeId);
        var blob = blobs.First();

        var id = _idService.GetId(SysTypeNames.CustomCatalogFile);

        var durablePayload = await _commerceHubWorkerService.ProcessCustomCatalogCsvAsync(sleekflowCompanyId, id);

        var customCatalogFile = await _customCatalogFileRepository.CreateAndGetAsync(
            new CustomCatalogFile(
                storeId,
                blob,
                CustomCatalogFileProcessStatuses.Pending,
                durablePayload,
                "Pending",
                new List<string>
                {
                    "Active"
                },
                id,
                sleekflowCompanyId,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowStaff,
                sleekflowStaff),
            sleekflowCompanyId);

        return customCatalogFile.Id;
    }

    public async Task<long> ReadCustomCatalogCsvLineCountAsync(string blobId)
    {
        var fileBlobServiceClient = _blobService.GetFileBlobServiceClient();
        var blobClient = fileBlobServiceClient.GetBlobClient(blobId);

        var myCustomCatalogCsvReader = new MyCustomCatalogCsvReader(
            blobClient,
            new MyCustomCatalogCsvReaderState(0, 0, null, false),
            _myCustomCatalogCsvReaderLogger);

        var _ = await myCustomCatalogCsvReader.GetHeadersAsync();
        return await myCustomCatalogCsvReader.ReadLineCountAsync();
    }

    public async Task ReadCustomCatalogCsvHeadersAsync(string id, string sleekflowCompanyId)
    {
        var customCatalogFile = await _customCatalogFileRepository.GetAsync(id, sleekflowCompanyId);

        var storeId = customCatalogFile.StoreId;
        var blobId = customCatalogFile.Blob.BlobId;

        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);
        var defaultLanguage = store.Languages.First(l => l.IsDefault);

        var fileBlobServiceClient = _blobService.GetFileBlobServiceClient();
        var blobClient = fileBlobServiceClient.GetBlobClient(blobId);

        var myCustomCatalogCsvReader = new MyCustomCatalogCsvReader(
            blobClient,
            new MyCustomCatalogCsvReaderState(0, 0, null, false),
            _myCustomCatalogCsvReaderLogger);

        var _ = await myCustomCatalogCsvReader.GetHeadersAsync();
    }

    public async Task<(int Count, MyCustomCatalogCsvReaderState? MyCustomCatalogCsvReaderState)>
        ProcessCustomCatalogCsvBatchAsync(
            string id,
            string sleekflowCompanyId,
            MyCustomCatalogCsvReaderState? myCustomCatalogCsvReaderState)
    {
        var customCatalogFile = await _customCatalogFileRepository.GetAsync(id, sleekflowCompanyId);

        var storeId = customCatalogFile.StoreId;
        var blobId = customCatalogFile.Blob.BlobId;

        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);
        var defaultLanguage = store.Languages.First(l => l.IsDefault);

        var fileBlobServiceClient = _blobService.GetFileBlobServiceClient();
        var customCatalogCsvBlobClient = fileBlobServiceClient.GetBlobClient(blobId);

        var myCustomCatalogCsvReader = new MyCustomCatalogCsvReader(
            customCatalogCsvBlobClient,
            myCustomCatalogCsvReaderState,
            _myCustomCatalogCsvReaderLogger);

        var _ = await myCustomCatalogCsvReader.GetHeadersAsync();
        var myReaderEntries = await myCustomCatalogCsvReader.ReadLinesAsync(25);

        var customCatalogFileProcessingLogger = await new CustomCatalogFileProcessLogger(
            _blobService,
            sleekflowCompanyId,
            storeId,
            id).InitAsync();

        foreach (var myReaderEntry in myReaderEntries)
        {
            var myReaderProductDto = myReaderEntry.Product;
            if (myReaderProductDto == null)
            {
                await customCatalogFileProcessingLogger.LogAsync(
                    myReaderEntry.ErrorMessage ?? "Unknown Error",
                    myReaderEntry.Row);

                continue;
            }

            try
            {
                // Categories
                var categoryMultilinguals = myReaderProductDto.CategoryNames
                    .Select(cn => new Multilingual(defaultLanguage.LanguageIsoCode, cn.Value))
                    .DistinctBy(m => m.Value)
                    .ToList();
                var categories = new List<Category>();
                foreach (var multilingual in categoryMultilinguals)
                {
                    var category = await _categoryService.CreateOrGetCategoryAsync(
                        sleekflowCompanyId,
                        storeId,
                        multilingual,
                        PlatformData.CustomCatalog(),
                        customCatalogFile.CreatedBy);
                    categories.Add(category);
                }

                var imageDtos = myReaderProductDto.ImageUrls
                    .Select(url => new ImageDto(url, null))
                    .ToList();

                var names = myReaderProductDto.Names
                    .Select(n => new Multilingual(n.Key, n.Value))
                    .ToList();

                var descriptions = myReaderProductDto.Descriptions
                    .Select(
                        d =>
                            new Description("Text", new Multilingual(d.LanguageCode, d.Value), null, null))
                    .ToList();

                var prices = myReaderProductDto.Prices
                    .Select(p => new Price(p.Key, decimal.Parse(p.Value)))
                    .ToList();

                // TODO Attributes
                await _defaultProductService.CreateDefaultProductAsync(
                    new ProductInput(
                        categories.Select(c => c.Id).ToList(),
                        myReaderProductDto.Sku,
                        myReaderProductDto.Url,
                        names,
                        descriptions,
                        imageDtos,
                        myReaderProductDto.Metadata.ToDictionary(p => p.Key, p => (object?) p.Value)),
                    sleekflowCompanyId,
                    storeId,
                    customCatalogFile.CreatedBy!,
                    prices,
                    new List<ProductVariant.ProductVariantAttribute>());
            }
            catch (SfValidationException e)
            {
                await customCatalogFileProcessingLogger.LogAsync(
                    string.Join(", ", e.ValidationResults.Select(vr => vr.ErrorMessage)),
                    myReaderEntry.Row);
            }
            catch (Exception e)
            {
                await customCatalogFileProcessingLogger.LogAsync(e.Message, myReaderEntry.Row);
            }
        }

        return (myReaderEntries.Count, myCustomCatalogCsvReader.GetState());
    }

    public async Task<bool> DeleteCustomCatalogCsvAsync(string blobId)
    {
        var fileBlobServiceClient = _blobService.GetFileBlobServiceClient();
        var blobClient = fileBlobServiceClient.GetBlobClient(blobId);
        var response = await blobClient.DeleteAsync();
        return response.IsError;
    }
}