﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiDeleteWabaEventConsumerDefinition : ConsumerDefinition<OnCloudApiDeleteWabaEventConsumer>
{

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiDeleteWabaEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiDeleteWabaEventConsumer : IConsumer<OnCloudApiDeleteWabaEvent>
{
    private readonly IWabaService _wabaService;
    private readonly IWabaRepository _wabaRepository;
    private readonly IAuditLogService _auditLogService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;
    private readonly ILogger<OnCloudApiDeleteWabaEventConsumer> _logger;

    public OnCloudApiDeleteWabaEventConsumer(
        IWabaService wabaService,
        IWabaRepository wabaRepository,
        IAuditLogService auditLogService,
        IBusinessBalanceService businessBalanceService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService,
        ILogger<OnCloudApiDeleteWabaEventConsumer> logger)
    {
        _wabaService = wabaService;
        _wabaRepository = wabaRepository;
        _auditLogService = auditLogService;
        _businessBalanceService = businessBalanceService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnCloudApiDeleteWabaEvent> context)
    {
        var retryCount = context.GetRedeliveryCount();
        if (retryCount > 3)
        {
            _logger.LogError(
                "Over the max retry limited {OnCloudApiDeleteWabaEvent}",
                JsonConvert.SerializeObject(context.Message));
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var facebookWabaId = context.Message.FacebookWabaId;
        var facebookBusinessId = context.Message.FacebookBusinessId;
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);

        var newWaba = JsonConvert.DeserializeObject<Waba>(JsonConvert.SerializeObject(waba));

        if (newWaba != null)
        {
            newWaba.RecordStatus = WabaStatuses.Deleted;

            var isReplaced = await _wabaRepository.ReplaceWabaAsync(waba, newWaba);

            if (isReplaced)
            {
                var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

                if (businessBalance is { IsByWabaBillingEnabled: true })
                {
                    var wabaBalance = businessBalance.WabaBalances?.Find(wb => wb.FacebookWabaId == facebookWabaId);

                    if (wabaBalance is { Balance.Amount: > 0 })
                    {
                        await _wabaLevelCreditManagementService.CreditTransferFromWabaDeletionWebhookAsync(
                            facebookWabaId,
                            facebookBusinessId,
                            businessBalance.ETag!);
                    }
                }

                await _auditLogService.AuditWabaAsync(
                    waba,
                    waba.FacebookWabaId,
                    null,
                    AuditingOperation.OnCloudApiWabaDeletionWebhookEvent,
                    JsonConvertExtensions.ToDictionary(newWaba));
            }
            else
            {
                await context.Redeliver(TimeSpan.FromSeconds(8));
            }
        }
    }
}