using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Configs;

public interface IShopifySecretConfig
{
    string ShopifyClientId { get; }

    string ShopifyClientSecret { get; }
}

public class ShopifySecretConfig : IConfig, IShopifySecretConfig
{
    public string ShopifyClientId { get; }

    public string ShopifyClientSecret { get; }

    public ShopifySecretConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ShopifyClientId = GetEnvironmentVariable(target, "SHOPIFY_CLIENT_ID");
        ShopifyClientSecret = GetEnvironmentVariable(target, "SHOPIFY_CLIENT_SECRET");
    }

    private static string GetEnvironmentVariable(
        EnvironmentVariableTarget target,
        string environmentVariable,
        string? message = null)
    {
        return Environment.GetEnvironmentVariable(environmentVariable, target)
               ?? throw new SfMissingEnvironmentVariableException(message ?? environmentVariable);
    }
}