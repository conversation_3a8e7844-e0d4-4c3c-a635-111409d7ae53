﻿using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.Utils;

namespace Sleekflow.FlowHub.Workers.Services;

public interface IContactBatchService
{
    Task<GetContactsByBatchOutput> GetContactsByBatchAsync(
        string origin,
        string sleekflowCompanyId,
        DateTimeOffset? lastContactCreatedAt,
        string? lastContactId,
        int? batchSize,
        string workflowVersionedId);
}

public class ContactBatchService : IContactBatchService
{
    private readonly ISleekflowCoreConfig _sleekflowCoreConfig;
    private readonly HttpClient _httpClient;
    private readonly ILogger<ContactBatchService> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;

    public ContactBatchService(
        ISleekflowCoreConfig sleekflowCoreConfig,
        IHttpClientFactory httpClientFactory,
        ILogger<ContactBatchService> logger,
        IAsyncPolicy<HttpResponseMessage> retryPolicy)
    {
        _sleekflowCoreConfig = sleekflowCoreConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _logger = logger;
        _retryPolicy = retryPolicy;
    }

    public async Task<GetContactsByBatchOutput> GetContactsByBatchAsync(
        string origin,
        string sleekflowCompanyId,
        DateTimeOffset? lastContactCreatedAt,
        string? lastContactId,
        int? batchSize,
        string workflowVersionedId)
    {
        var getContactsByBatchInternalInput = new GetContactsByBatchInput(
            sleekflowCompanyId,
            lastContactCreatedAt,
            lastContactId,
            batchSize);

        var getContactsInputJsonStr = JsonConvert.SerializeObject(getContactsByBatchInternalInput);
        var getContactsTargetUri = new Uri(origin + "/FlowHub/Internals/Functions/GetContactsByBatch");

        try
        {
            var pollyContext =
                new Context();
            pollyContext["logger"] = _logger;

            var getContactsResMsg = await _retryPolicy.ExecuteAsync(
                async (context) =>
                {
                    var reqMsg = new HttpRequestMessage
                    {
                        Method = HttpMethod.Post,
                        Content = new StringContent(getContactsInputJsonStr, Encoding.UTF8, "application/json"),
                        RequestUri = getContactsTargetUri,
                        Headers =
                        {
                            {
                                "X-Sleekflow-Flow-Hub-Authorization",
                                InternalsTokenUtils.CreateJwt(_sleekflowCoreConfig.CoreInternalsKey)
                            }
                        }
                    };
                    _logger.LogInformation("Attempting to get contacts batch from {Uri}", getContactsTargetUri);

                    return await _httpClient.SendAsync(reqMsg);
                },
                pollyContext);

            getContactsResMsg.EnsureSuccessStatusCode();
            var getContactsResStr = await getContactsResMsg.Content.ReadAsStringAsync();

            return JsonConvert.DeserializeObject<GetContactsByBatchOutput>(getContactsResStr)!;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to get or deserialize contacts batch for Company {CompanyId}, Workflow {WorkflowVersionedId}. Input: {InputJson}",
                sleekflowCompanyId,
                workflowVersionedId,
                getContactsInputJsonStr);

            throw; // Rethrow to be caught by the orchestrator
        }
    }
}