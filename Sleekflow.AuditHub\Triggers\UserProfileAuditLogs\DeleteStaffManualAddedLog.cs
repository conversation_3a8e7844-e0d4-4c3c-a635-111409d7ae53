﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class DeleteStaffManualAddedLog : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;

    public DeleteStaffManualAddedLog(
        IUserProfileAuditLogService userProfileAuditLogService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
    }

    public class DeleteStaffManualAddedLogInput : IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [JsonConstructor]
        public DeleteStaffManualAddedLogInput(
            string id,
            string sleekflowUserProfileId)
        {
            Id = id;
            SleekflowUserProfileId = sleekflowUserProfileId;
        }
    }

    public class DeleteStaffManualAddedLogOutput
    {
        [JsonProperty("deleted_count")]
        public int DeletedCount { get; set; }

        [JsonConstructor]
        public DeleteStaffManualAddedLogOutput(int deletedCount)
        {
            DeletedCount = deletedCount;
        }
    }

    public async Task<DeleteStaffManualAddedLogOutput> F(DeleteStaffManualAddedLogInput deleteUserProfileAuditLogInput)
    {
        await ValidateUserProfileAuditLogType(
            deleteUserProfileAuditLogInput.Id,
            deleteUserProfileAuditLogInput.SleekflowUserProfileId);

        var deletedCount = await _userProfileAuditLogService.DeleteUserProfileAuditLogsAsync(
            deleteUserProfileAuditLogInput.Id,
            deleteUserProfileAuditLogInput.SleekflowUserProfileId);

        return new DeleteStaffManualAddedLogOutput(deletedCount);
    }

    private async Task ValidateUserProfileAuditLogType(string id, string sleekflowUserProfileId)
    {
        var userProfileAuditLog =
            await _userProfileAuditLogService.GetUserProfileAuditLogAsync(id, sleekflowUserProfileId);

        if (userProfileAuditLog.Type != UserProfileAuditLogTypes.ManualLog)
        {
            throw new SfValidationException(new List<ValidationResult>
            {
                new ($"Only {UserProfileAuditLogTypes.ManualLog} log is allow to be deleted.")
            });
        }
    }
}