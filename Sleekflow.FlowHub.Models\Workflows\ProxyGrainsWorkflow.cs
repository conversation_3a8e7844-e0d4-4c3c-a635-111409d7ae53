using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Workflows;

public class ProxyGrainWorkflow : BaseProxyWorkflow, IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty("workflow_contacts_dict")]
    public AsyncDictionaryWrapper<string, object?> WorkflowContactsDict { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("workflow_type")]
    public string WorkflowType { get; set; }

    [JsonProperty("workflow_group_id")]
    public string? WorkflowGroupId { get; set; }

    [JsonProperty("triggers")]
    public WorkflowTriggers Triggers { get; set; }

    [JsonProperty("workflow_enrollment_settings")]
    public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

    [JsonProperty("workflow_schedule_settings")]
    public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

    [JsonProperty("activation_status")]
    public string ActivationStatus { get; set; }

    [JsonProperty("steps")]
    public List<Step> Steps { get; set; }

    [JsonProperty("version")]
    public string Version { get; set; }

    [JsonProperty("created_by")]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty("updated_by")]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public ProxyGrainWorkflow(
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        AsyncDictionaryWrapper<string, object?> workflowContactsDict,
        string name,
        string workflowType,
        string? workflowGroupId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings workflowEnrollmentSettings,
        WorkflowScheduleSettings workflowScheduleSettings,
        string activationStatus,
        List<Step> steps,
        string version,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
    : base(sleekflowCompanyId, workflowId, workflowVersionedId)
    {
        WorkflowContactsDict = workflowContactsDict;
        Name = name;
        WorkflowType = workflowType;
        WorkflowGroupId = workflowGroupId;
        Triggers = triggers;
        WorkflowEnrollmentSettings = workflowEnrollmentSettings;
        WorkflowScheduleSettings = workflowScheduleSettings;
        ActivationStatus = activationStatus;
        Steps = steps;
        Version = version;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public ProxyGrainWorkflow(ProxyWorkflow proxyWorkflow, AsyncDictionaryWrapper<string, object?> workflowContactsDict)
        : this(
            proxyWorkflow.SleekflowCompanyId,
            proxyWorkflow.WorkflowId,
            proxyWorkflow.WorkflowVersionedId,
            workflowContactsDict,
            proxyWorkflow.Name,
            proxyWorkflow.WorkflowType,
            proxyWorkflow.WorkflowGroupId,
            proxyWorkflow.Triggers,
            proxyWorkflow.WorkflowEnrollmentSettings,
            proxyWorkflow.WorkflowScheduleSettings,
            proxyWorkflow.ActivationStatus,
            proxyWorkflow.Steps,
            proxyWorkflow.Version,
            null,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow)
    {
    }
}