using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Tools;

namespace Sleekflow.IntelligentHub.Models.Twilio;

public class TwilioAdditionalFields : RealTimeAssistantTool.AdditionalFields
{
    [JsonProperty("caller")]
    public string Caller { get; set; }

    [JsonProperty("receiver")]
    public string Receiver { get; set; }

    [JsonConstructor]
    public TwilioAdditionalFields(string caller, string receiver, string sleekflowCompanyId)
        : base(sleekflowCompanyId)
    {
        Caller = caller;
        Receiver = receiver;
    }

    public TwilioAdditionalFields()
        : base(string.Empty)
    {
    }
}