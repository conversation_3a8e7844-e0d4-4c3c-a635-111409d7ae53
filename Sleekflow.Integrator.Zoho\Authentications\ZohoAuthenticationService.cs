using System.Net;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Integrator.Zoho.Configs;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Zoho.Authentications;

public interface IZohoAuthenticationService
{
    Task<ZohoAuthentication?> GetAsync(string id, string sleekflowCompanyId);

    Task<string> AuthenticateAsync(string sleekflowCompanyId, string successUrl, string failureUrl, Dictionary<string, object?>? additionalDetails);

    Task<ZohoAuthentication> ReAuthenticateAndStoreAsync(string id, string sleekflowCompanyId);

    Task<(ZohoAuthentication Authentication, string SuccessUrl, string FailureUrl, bool IsSandbox)> HandleAuthenticateCallbackAndStoreAsync(string code, string encryptedState);

    Task DeleteAsync(string id, string sleekflowCompanyId);
}

public class ZohoAuthenticationService : ISingletonService, IZohoAuthenticationService
{
    private readonly IZohoConfig _zohoConfig;
    private readonly ILogger<ZohoAuthenticationService> _logger;
    private readonly IZohoAuthenticationRepository _zohoAuthenticationRepository;
    private readonly HttpClient _httpClient;
    private readonly IIdService _idService;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;

    public ZohoAuthenticationService(
        IZohoConfig zohoConfig,
        IHttpClientFactory httpClientFactory,
        ILogger<ZohoAuthenticationService> logger,
        IZohoAuthenticationRepository zohoAuthenticationRepository,
        IIdService idService,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer)
    {
        _zohoConfig = zohoConfig;
        _logger = logger;
        _zohoAuthenticationRepository = zohoAuthenticationRepository;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _idService = idService;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
    }

    private sealed class State
    {
        public string? SleekflowCompanyId { get; set; }

        public string? SuccessUrl { get; set; }

        public string? FailureUrl { get; set; }

        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public State(
            string? sleekflowCompanyId,
            string? successUrl,
            string? failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    private sealed class Oauth2TokenOutput
    {
        [JsonProperty("access_token")]
        public string? AccessToken { get; set; }

        [JsonProperty("refresh_token")]
        public string? RefreshToken { get; set; }

        [JsonProperty("api_domain")]
        public string? ApiDomain { get; set; }

        [JsonProperty("token_type")]
        public string? TokenType { get; set; }

        [JsonProperty("expires_in")]
        public string? ExpiresIn { get; set; }
    }

    public async Task<ZohoAuthentication?> GetAsync(string id, string sleekflowCompanyId)
    {
        var authentication = await _zohoAuthenticationRepository.GetOrDefaultAsync(
            id,
            sleekflowCompanyId);
        if (authentication == null)
        {
            return null;
        }

        var authenticationIssuedAt = long.Parse(authentication.IssuedAt);
        if (DateTimeOffset.UtcNow.ToUnixTimeMilliseconds() - authenticationIssuedAt < 1000 * 60 * 15)
        {
            return authentication;
        }

        return await ReAuthenticateAndStoreAsync(id, sleekflowCompanyId);
    }

    public async Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string successUrl,
        string failureUrl,
        Dictionary<string, object?>? additionalDetails)
    {
        const string responseType = "code";
        const string accessType = "offline";
        const string scope = "ZohoCRM.users.ALL,ZohoCRM.org.READ,ZohoCRM.settings.ALL,ZohoCRM.settings.fields.ALL,ZohoCRM.modules.ALL";

        var state = new State(sleekflowCompanyId, successUrl, failureUrl, additionalDetails);
        var encryptedState = AesUtils.AesEncryptBase64(
            JsonConvert.SerializeObject(state),
            _zohoConfig.ZohoOauthStateEncryptionKey);

        var authorizeUrl = $"{_zohoConfig.ZohoAccountsDomain}/oauth/v2/auth?" +
                          $"response_type={responseType}" +
                          $"&scope={scope}" +
                          $"&client_id={_zohoConfig.ZohoClientId}" +
                          $"&redirect_uri={_zohoConfig.ZohoOauthCallbackUrl}" +
                          $"&access_type={accessType}" +
                          $"&state={WebUtility.UrlEncode(encryptedState)}";

        var httpResponseMsg = await _httpClient.PostAsync(authorizeUrl, null);

        var redirectUrl = httpResponseMsg.Headers.Location?.ToString();
        if (redirectUrl == null || httpResponseMsg.StatusCode != HttpStatusCode.Found)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoOauthInitialAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMsg.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogWarning(
                "The httpResponseMsg doesn't contain the authentication url from Zoho. httpResponseMsg {HttpResponseMsg}",
                httpResponseMsg);

            throw new Exception($"Unable to get the redirectUrl. authorizeUrl {authorizeUrl}");
        }

        _logger.LogInformation(
            "Completed AuthenticateAsync. encryptedState {EncryptedState}, state {State}, redirectUrl {RedirectUrl}",
            encryptedState,
            state,
            redirectUrl);

        return redirectUrl;
    }

    public async Task<ZohoAuthentication> ReAuthenticateAndStoreAsync(string id, string sleekflowCompanyId)
    {
        var authentication = await _zohoAuthenticationRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (authentication == null || authentication.RefreshToken == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation("Started ReAuthenticateAndStoreAsync. id {Id}", id);

        var nvc = new List<KeyValuePair<string, string>>
        {
            new("grant_type", "refresh_token"),
            new("client_id", _zohoConfig.ZohoClientId),
            new("client_secret", _zohoConfig.ZohoClientSecret),
            new("refresh_token", authentication.RefreshToken),
        };

        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            $"{_zohoConfig.ZohoAccountsDomain}/oauth/v2/token")
        {
            Content = new FormUrlEncodedContent(nvc)
        };

        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();

        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoOauthReAuthenticationErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", sleekflowCompanyId },
                });

            _logger.LogError(
                "Unable to get a success /oauth/v2/token response from Zoho. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth/v2/token response from Zoho");
        }

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null || oauth2TokenOutput.AccessToken == null)
        {
            _logger.LogError(
                "Unable to get a success /oauth/v2/token response from Zoho. httpResponseMessage {HttpResponseMessage}, readAsStringAsync {ReadAsStringAsync}",
                httpRequestMessage,
                readAsStringAsync);

            throw new Exception("Unable to get a success /oauth/v2/token response from Zoho");
        }

        authentication.AccessToken = oauth2TokenOutput.AccessToken;
        authentication.IssuedAt = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        authentication.RefreshRes = oauth2TokenOutput;

        if (!string.IsNullOrEmpty(oauth2TokenOutput.ExpiresIn))
        {
            authentication.ExpiresIn = oauth2TokenOutput.ExpiresIn;
        }

        var upsertAsync = await _zohoAuthenticationRepository.UpsertAsync(authentication, authentication.SleekflowCompanyId);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert ZohoAuthentication.");
        }

        return authentication;
    }

    public async Task<(ZohoAuthentication Authentication, string SuccessUrl, string FailureUrl, bool IsSandbox)>
        HandleAuthenticateCallbackAndStoreAsync(string code, string encryptedState)
    {
        var decryptStateStr =
            AesUtils.AesDecryptBase64(
                encryptedState,
                _zohoConfig.ZohoOauthStateEncryptionKey);
        var state = decryptStateStr.ToObject<State>();
        if (state == null || string.IsNullOrEmpty(state.SleekflowCompanyId))
        {
            _logger.LogWarning("The SleekflowCompanyId is null. The state is invalid");

            throw new Exception("Unable to get a correct state.");
        }

        var nvc = new List<KeyValuePair<string, string>>
        {
            new("grant_type", "authorization_code"),
            new("code", code),
            new("client_id", _zohoConfig.ZohoClientId),
            new("client_secret", _zohoConfig.ZohoClientSecret),
            new("redirect_uri", _zohoConfig.ZohoOauthCallbackUrl),
        };

        var httpRequestMessage = new HttpRequestMessage(
            HttpMethod.Post,
            $"{_zohoConfig.ZohoAccountsDomain}/oauth/v2/token")
        {
            Content = new FormUrlEncodedContent(nvc)
        };

        var httpResponseMessage = await _httpClient.SendAsync(httpRequestMessage);
        var readAsStringAsync = await httpResponseMessage.Content.ReadAsStringAsync();

        var oauth2TokenOutput = readAsStringAsync.ToObject<Oauth2TokenOutput>();
        if (oauth2TokenOutput == null
            || string.IsNullOrEmpty(oauth2TokenOutput.AccessToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.RefreshToken)
            || string.IsNullOrEmpty(oauth2TokenOutput.ApiDomain)
            || string.IsNullOrEmpty(oauth2TokenOutput.TokenType)
            || string.IsNullOrEmpty(oauth2TokenOutput.ExpiresIn))
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.ZohoOauthCallbackErrorResponseReceived,
                new Dictionary<string, string>
                {
                    { "status_code", httpResponseMessage.StatusCode.ToString() },
                    { "sleekflow_company_id", state.SleekflowCompanyId },
                });

            _logger.LogWarning(
                "The oauth2TokenOutput is null or has null. sleekflowCompanyId {SleekflowCompanyId}, readAsStringAsync {ReadAsStringAsync}, httpResponseMessage {HttpResponseMessage}",
                state.SleekflowCompanyId,
                readAsStringAsync,
                httpResponseMessage);

            throw new Exception("Unable to deserialize a success /oauth/v2/token response from Zoho");
        }

        var authentication = new ZohoAuthentication(
            _idService.GetId("ZohoAuthentication"),
            state.SleekflowCompanyId,
            oauth2TokenOutput.AccessToken,
            oauth2TokenOutput.RefreshToken,
            oauth2TokenOutput.ApiDomain,
            oauth2TokenOutput.TokenType,
            oauth2TokenOutput.ExpiresIn,
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString(),
            oauth2TokenOutput,
            null);

        var upsertAsync = await _zohoAuthenticationRepository.UpsertAsync(authentication, authentication.SleekflowCompanyId);
        if (upsertAsync == 0)
        {
            throw new SfInternalErrorException("Unable to upsert ZohoAuthentication.");
        }

        return (authentication, state.SuccessUrl!, state.FailureUrl!, state.AdditionalDetails?["is_sandbox"] is true);
    }

    public async Task DeleteAsync(string id, string sleekflowCompanyId)
    {
        var authentication = await _zohoAuthenticationRepository.GetAsync(id, sleekflowCompanyId);
        if (authentication is null)
        {
            throw new SfNotFoundObjectException(id, id);
        }

        var deleteAsync = await _zohoAuthenticationRepository.DeleteAsync(id, sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException($"Unable to delete the ZohoAuthentication with id {id} and sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}