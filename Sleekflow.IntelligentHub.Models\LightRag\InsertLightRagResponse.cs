using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.LightRag;

public class InsertLightRagResponse
{
    public const string STATUS_SUCCESS = "success";

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("document_count")]
    public int DocumentCount { get; set; }

    [JsonConstructor]
    public InsertLightRagResponse(string status, string message, int documentCount)
    {
        Status = status;
        Message = message;
        DocumentCount = documentCount;
    }
}