<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
        <ServerGarbageCollection>true</ServerGarbageCollection>
        <ConcurrentGarbageCollection>true</ConcurrentGarbageCollection>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
        <PackageReference Include="Microsoft.Orleans.Clustering.AzureStorage" Version="8.2.0" />
        <PackageReference Include="Microsoft.Orleans.Persistence.AzureStorage" Version="8.2.0" />
        <PackageReference Include="Microsoft.Orleans.Server" Version="8.2.0" />
        <PackageReference Include="Mime-Detective" Version="24.12.2" />
        <PackageReference Include="Scriban" Version="5.12.1" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.FlowHub.Models\Sleekflow.FlowHub.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.FlowHub\Sleekflow.FlowHub.csproj" />
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
        <ProjectReference Include="..\Sleekflow.FlowHub\Sleekflow.FlowHub.csproj" />
        <ProjectReference Include="..\Sleekflow.FlowHub.Commons\Sleekflow.FlowHub.Commons.csproj" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="Sleekflow.FlowHub.Tests" />
    </ItemGroup>

    <ItemGroup>
      <Reference Include="GraphApi.Client">
        <HintPath>..\Sleekflow.FlowHub.Models\Binaries\GraphApi.Client.dll</HintPath>
      </Reference>
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>