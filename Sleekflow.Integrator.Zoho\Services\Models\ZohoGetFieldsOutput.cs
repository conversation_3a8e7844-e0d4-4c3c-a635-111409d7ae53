using Newtonsoft.Json;

namespace Sleekflow.Integrator.Zoho.Services.Models;

public class ZohoGetFieldsOutput
{
    [JsonProperty("fields")]
    public List<ZohoField>? Fields { get; set; }
}

public class ZohoField
{
    [JsonProperty("api_name")]
    public string? ApiName { get; set; }

    [JsonProperty("field_label")]
    public string? FieldLabel { get; set; }

    [JsonProperty("data_type")]
    public string? DataType { get; set; }

    [JsonProperty("system_mandatory")]
    public bool? SystemMandatory { get; set; }

    [JsonProperty("custom_field")]
    public bool? CustomField { get; set; }

    [JsonProperty("read_only")]
    public bool? ReadOnly { get; set; }

    [JsonProperty("required")]
    public bool? Required { get; set; }
}