using System.ComponentModel.DataAnnotations;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Payloads.WhatsappFlows;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.WhatsappFlows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.WhatsappFlows;

[TriggerGroup(ControllerNames.WhatsappFlows)]
public class GetWhatsappFlowsByWabaId(IWabaService wabaService, IWhatsappFlowService whatsappFlowService)
    : ITrigger<GetWhatsappFlowsByWabaId.GetWhatsappFlowsByWabaIdInput, GetWhatsappFlowsByWabaId.GetWhatsappFlowsByWabaIdOutput>
{
    [method: JsonConstructor]
    public class GetWhatsappFlowsByWabaIdInput(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        CursorBasedPaginationParam? paginationParam = null)
        : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; } = sleekflowCompanyId;

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; } = messagingHubWabaId;

        [JsonProperty("pagination_Param")]
        [Validations.ValidateObject]
        public CursorBasedPaginationParam? PaginationParam { get; set; } = paginationParam;
    }

    [method: JsonConstructor]
    public class GetWhatsappFlowsByWabaIdOutput(GetFlowsResponse flows)
    {
        [Required]
        [JsonProperty("flows")]
        public GetFlowsResponse Flows { get; set; } = flows;
    }

    public async Task<GetWhatsappFlowsByWabaIdOutput> F(GetWhatsappFlowsByWabaIdInput input)
    {
        var sleekflowCompanyId = input.SleekflowCompanyId;

        var waba = await wabaService.GetWabaOrDefaultAsync(
            input.MessagingHubWabaId,
            sleekflowCompanyId);

        if (waba == null)
        {
            throw new SfNotFoundObjectException(input.MessagingHubWabaId);
        }

        return new GetWhatsappFlowsByWabaIdOutput(
            await whatsappFlowService.GetFlowsAsync(waba.FacebookWabaId, input.PaginationParam));
    }
}