﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.SchemafulObjects;

public class SchemafulObjectDto : AuditEntityDto, IHasSleekflowUserProfileId
{
    [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
    public string SchemaId { get; }

    [JsonProperty(SchemafulObject.PropertyNamePrimaryPropertyValue)]
    public string PrimaryPropertyValue { get; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty(SchemafulObject.PropertyNamePropertyValues)]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [JsonProperty(SchemafulObject.PropertyNameCreatedVia)]
    public string CreatedVia { get; set; }

    [Json<PERSON>roperty(SchemafulObject.PropertyNameUpdatedVia)]
    public string UpdatedVia { get; set; }

    [JsonConstructor]
    public SchemafulObjectDto(
        string id,
        string schemaId,
        string primaryPropertyValue,
        string sleekflowUserProfileId,
        Dictionary<string, object?> propertyValues,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        string? createdVia,
        string? updatedVia,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, sleekflowCompanyId, createdBy, updatedBy, createdAt, updatedAt)
    {
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        SleekflowUserProfileId = sleekflowUserProfileId;
        PropertyValues = propertyValues;
        CreatedVia = createdVia ?? string.Empty;
        UpdatedVia = updatedVia ?? string.Empty;
    }

    public SchemafulObjectDto(SchemafulObject schemafulObject)
        : this(
            schemafulObject.Id,
            schemafulObject.SchemaId,
            schemafulObject.PrimaryPropertyValue,
            schemafulObject.SleekflowUserProfileId,
            schemafulObject.PropertyValues,
            schemafulObject.SleekflowCompanyId,
            schemafulObject.CreatedBy,
            schemafulObject.UpdatedBy,
            schemafulObject.CreatedVia,
            schemafulObject.UpdatedVia,
            schemafulObject.CreatedAt,
            schemafulObject.UpdatedAt)
    {
    }
}