using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Salesforce;

[TriggerGroup(TriggerGroups.InflowActions)]
public class GetSalesforceCustomObjectTypes : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetSalesforceCustomObjectTypes(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetSalesforceCustomObjectTypesInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonConstructor]
        public GetSalesforceCustomObjectTypesInput(
            string sleekflowCompanyId,
            string salesforceConnectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SalesforceConnectionId = salesforceConnectionId;
        }
    }

    public class GetSalesforceCustomObjectTypesOutput
    {
        [JsonProperty("custom_object_types")]
        public List<CustomObjectType> CustomObjectTypes { get; set; }

        [JsonConstructor]
        public GetSalesforceCustomObjectTypesOutput(
            List<CustomObjectType> customObjectTypes)
        {
            CustomObjectTypes = customObjectTypes;
        }
    }

    public async Task<GetSalesforceCustomObjectTypesOutput> F(
        GetSalesforceCustomObjectTypesInput getCustomObjectTypesInput)
    {
        var salesforceProviderService = _providerSelector.GetProviderService(
            "salesforce-integrator");

        var output = await salesforceProviderService.GetCustomObjectTypesAsync(
            getCustomObjectTypesInput.SleekflowCompanyId,
            getCustomObjectTypesInput.SalesforceConnectionId);

        return new GetSalesforceCustomObjectTypesOutput(output.CustomObjectTypes);
    }
}