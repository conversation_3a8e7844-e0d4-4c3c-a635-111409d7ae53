using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.TopicAnalytics;
using Sleekflow.IntelligentHub.TopicAnalytics;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.TopicAnalytics.Topics;

[TriggerGroup(ControllerNames.TopicAnalytics)]
public class CreateTopic : ITrigger<CreateTopic.CreateTopicInput, CreateTopic.CreateTopicOutput>
{
    private readonly ITopicAnalyticsService _topicAnalyticsService;

    public CreateTopic(ITopicAnalyticsService topicAnalyticsService)
    {
        _topicAnalyticsService = topicAnalyticsService;
    }

    public class CreateTopicInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty("topic_name")]
        public string TopicName { get; set; }

        [JsonProperty("terms")]
        public List<TopicAnalyticsTerm> Terms { get; set; }


        [JsonConstructor]
        public CreateTopicInput(
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string topicName,
            List<TopicAnalyticsTerm> terms)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            TopicName = topicName;
            Terms = terms;
        }
    }

    public class CreateTopicOutput
    {
        [JsonProperty("topic")]
        public TopicAnalyticsTopic Topic { get; set; }

        [JsonConstructor]
        public CreateTopicOutput(TopicAnalyticsTopic topic)
        {
            Topic = topic;
        }
    }

    public async Task<CreateTopicOutput> F(CreateTopicInput input)
    {
        var result = await _topicAnalyticsService.CreateTopicAnalyticsTopic(
            input.SleekflowCompanyId,
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds,
            input.TopicName,
            input.Terms);

        return new CreateTopicOutput(result);
    }
}