﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Webhooks.Payloads;

public class OnEntityFieldsChangedWebhookPayloadChangeEntry
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("from_value")]
    public object? FromValue { get; set; }

    [JsonProperty("to_value")]
    public object? ToValue { get; set; }

    [JsonConstructor]
    public OnEntityFieldsChangedWebhookPayloadChangeEntry(
        string name,
        object? fromValue,
        object? toValue)
    {
        Name = name;
        FromValue = fromValue;
        ToValue = toValue;
    }
}

public class OnEntityFieldsChangedWebhookPayload
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SysSleekflowCompanyId { get; set; }

    [JsonProperty("change_entries")]
    public List<OnEntityFieldsChangedWebhookPayloadChangeEntry> ChangeEntries { get; set; }

    [JsonProperty("date_time")]
    public DateTimeOffset DateTime { get; set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("entity_id")]
    public string EntityId { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("event_type_name")]
    public string EventTypeName { get; set; }

    [JsonConstructor]
    public OnEntityFieldsChangedWebhookPayload(
        string id,
        List<OnEntityFieldsChangedWebhookPayloadChangeEntry> changeEntries,
        DateTimeOffset dateTime,
        string providerName,
        string sysSleekflowCompanyId,
        string entityId,
        string entityTypeName,
        string eventTypeName)
    {
        Id = id;
        ChangeEntries = changeEntries;
        DateTime = dateTime;
        ProviderName = providerName;
        SysSleekflowCompanyId = sysSleekflowCompanyId;
        EntityId = entityId;
        EntityTypeName = entityTypeName;
        EventTypeName = eventTypeName;
    }
}