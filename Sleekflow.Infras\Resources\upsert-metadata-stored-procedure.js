function upsertMetadata(doc, entityTypeName, sleekflowCompanyId) {
    const context = getContext();
    const container = context.getCollection();
    const response = context.getResponse();

    const metadataId = entityTypeName;
    const documentLink = container.getAltLink() + '/docs/' + metadataId;

    const requestOptions = {
        partitionKey: sleekflowCompanyId
    };

    container.readDocument(documentLink, requestOptions, readDocumentCallback);

    function readDocumentCallback(err, item, responseOptions) {
        if (err) {
            if (err.number === 404) {
                diffMetadataItems([]);
            } else {
                throw `Unable to read metadata document, abort. Message=[${err.message}].`;
            }
        } else {
            diffMetadataItems([item]);
        }
    }

    function diffMetadataItems(items) {
        if (items.length === 0) {
            createMetadata();
        } else if (items.length > 1) {
            throw "Too many metadata items, abort.";
        } else {
            updateMetadata(items[0]);
        }
    }

    function createMetadata() {
        const metadataItem = createMetadataItem();
        const isAccepted = container.createDocument(container.getSelfLink(), metadataItem, undefined, function (err) {
            if (err) throw `Unable to create metadata, abort. Message=[${err.message}].`;
        });
        if (!isAccepted) throw "Unable to create metadata, abort.";
        response.setBody(1);
    }

    function createMetadataItem() {
        const docKeys = Object.keys(doc);
        const metadataItem = {
            "id": metadataId,
            "sleekflow_company_id": sleekflowCompanyId,
            "sys_type_name": "Metadata",
            "keys": [...new Set(docKeys)],
            "fields": {},
        };

        for (const docKey of docKeys) {
            const fieldType = getObjType(doc[docKey], 0);
            metadataItem.fields[docKey] = {
                name: docKey,
                types: [fieldType],
            };
        }

        return metadataItem;
    }

    function updateMetadata(existingMetadataItem) {
        const newMetadataItem = JSON.parse(JSON.stringify(existingMetadataItem));
        const docKeys = Object.keys(doc);

        for (const docKey of docKeys) {
            if (!newMetadataItem.keys.includes(docKey)) {
                newMetadataItem.keys.push(docKey);
            }

            if (!newMetadataItem.fields[docKey]) {
                newMetadataItem.fields[docKey] = {
                    name: docKey,
                    types: [],
                };
            }

            const fieldType = getObjType(doc[docKey], 0);
            const fieldTypeJson = JSON.stringify(fieldType);
            if (!newMetadataItem.fields[docKey].types.some(t => JSON.stringify(t) === fieldTypeJson)) {
                newMetadataItem.fields[docKey].types.push(fieldType);
            }
        }

        if (JSON.stringify(existingMetadataItem) !== JSON.stringify(newMetadataItem)) {
            const isAccepted = container.replaceDocument(newMetadataItem._self, newMetadataItem, undefined, function (err) {
                if (err) throw `Unable to update metadata, abort. Message=[${err.message}].`;
            });
            if (!isAccepted) throw "Unable to update metadata, abort";
            response.setBody(1);
        } else {
            response.setBody(0);
        }
    }

    function getObjType(obj, depth) {
        if (obj === null || obj === undefined) {
            return "null";
        }

        const t = typeof obj;
        const d = depth + 1;

        if (t === 'string') {
            if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(obj) || /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}Z$/.test(obj)) return 'datetime';
            if (/^\d{4}-\d{2}-\d{2}$/.test(obj)) return 'date';

            return t;
        }

        // Recursively traverse the object
        if (t === 'object' && depth < 3) {
            if (Object.keys(obj).length === 0) {
                return 'object';
            }

            const objectType = {};
            for (const key in obj) {
                objectType[key] = getObjType(obj[key], d);
            }

            return objectType
        }

        return t;
    }
}