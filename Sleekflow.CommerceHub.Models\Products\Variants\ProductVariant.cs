using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Products.Variants;

[ContainerId(ContainerNames.Product)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class ProductVariant : AuditEntity, IHasRecordStatuses, IHasMetadata
{
    public const string PropertyNameProductId = "product_id";
    public const string PropertyNameSku = "sku";
    public const string PropertyNameUrl = "url";
    public const string PropertyNamePrices = "prices";
    public const string PropertyNamePosition = "position";
    public const string PropertyNameIsDefaultVariantProduct = "is_default_variant_product";
    public const string PropertyNameAttributes = "attributes";
    public const string PropertyNameNames = "names";
    public const string PropertyNameDescriptions = "descriptions";
    public const string PropertyNameImages = "images";
    public const string PropertyNamePlatformData = "platform_data";

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(PropertyNameProductId)]
    public string ProductId { get; set; }

    [JsonProperty(PropertyNameSku)]
    public string? Sku { get; set; }

    [JsonProperty(PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty(PropertyNamePrices)]
    public List<Price> Prices { get; set; }

    [JsonProperty(PropertyNamePosition)]
    public int Position { get; set; }

    [JsonProperty(PropertyNameIsDefaultVariantProduct)]
    public bool IsDefaultVariantProduct { get; set; }

    [JsonProperty(PropertyNameAttributes)]
    public List<ProductVariantAttribute> Attributes { get; set; }

    [JsonProperty(PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(PropertyNameImages)]
    public List<Image> Images { get; set; }

    [JsonProperty(PropertyNamePlatformData)]
    public PlatformData PlatformData { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public ProductVariant(
        string id,
        string sleekflowCompanyId,
        string storeId,
        string productId,
        string? sku,
        string? url,
        List<Price> prices,
        int position,
        bool isDefaultVariantProduct,
        List<ProductVariantAttribute> attributes,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        PlatformData platformData,
        List<string> recordStatuses,
        Dictionary<string, object?> metadata,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, SysTypeNames.ProductVariant, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        StoreId = storeId;
        ProductId = productId;
        Sku = sku;
        Url = url;
        Prices = prices;
        Position = position;
        IsDefaultVariantProduct = isDefaultVariantProduct;
        Attributes = attributes;
        Names = names;
        Descriptions = descriptions;
        Images = images;
        PlatformData = platformData;
        RecordStatuses = recordStatuses;
        Metadata = metadata;
    }

    public class ProductVariantAttribute
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonConstructor]
        public ProductVariantAttribute(
            string name,
            string value)
        {
            Name = name;
            Value = value;
        }
    }
}