using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;

public class WabaProductCatalog : Entity, IHasCreatedAt, IHasUpdatedAt
{
    public const string PropertyNameFacebookProductCatalogId = "facebook_product_catalog_id";
    public const string PropertyNameFacebookProductCatalogName = "facebook_product_catalog_name";
    public const string PropertyNameSleekflowCompanyId = "sleekflow_company_id";
    public const string PropertyNameCreatedAt = "created_at";
    public const string PropertyNameUpdatedAt = "updated_at";
    public const string PropertyNameCreatedBy = "created_by";
    public const string PropertyNameUpdatedBy = "updated_by";

    [JsonProperty(PropertyNameFacebookProductCatalogId)]
    public string FacebookProductCatalogId { get; set; }

    [JsonProperty(PropertyNameFacebookProductCatalogName)]
    public string FacebookProductCatalogName { get; set; }

    [JsonProperty(PropertyNameSleekflowCompanyId)]
    public string? SleekflowCompanyId { get; set; }

    [JsonProperty("default_image_url")]
    public string? DefaultImageUrl { get; set; }

    [JsonProperty("product_count")]
    public int? ProductCount { get; set; }

    [JsonProperty("vertical")] // the type of catalog (for example: hotels, commerce, etc)
    public string? Vertical { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; } // active || inactive

    [JsonProperty(PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public WabaProductCatalog(
        string id,
        string facebookProductCatalogId,
        string facebookProductCatalogName,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string? sleekflowCompanyId,
        string? defaultImageUrl,
        int? productCount,
        string? vertical,
        string status,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy)
        : base(id, Constants.SysTypeNames.WabaProductCatalog)
    {
        FacebookProductCatalogId = facebookProductCatalogId;
        FacebookProductCatalogName = facebookProductCatalogName;
        SleekflowCompanyId = sleekflowCompanyId;
        DefaultImageUrl = defaultImageUrl;
        ProductCount = productCount;
        Vertical = vertical;
        Status = status;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}