using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

namespace Sleekflow.FlowHub.Models.Messages;

/// <summary>
/// Please refer to https://core.telegram.org/bots/api#sendmessage here.
/// </summary>
public class TelegramMessengerMessageObject
{
    /// <summary>
    /// Text of the message to be sent, 1-4096 characters after entities parsing.
    /// </summary>
    [JsonProperty("text")]
    public string? Text { get; set; }

    [JsonConstructor]
    public TelegramMessengerMessageObject(
        string? text = null)
    {
        Text = text;
    }
}