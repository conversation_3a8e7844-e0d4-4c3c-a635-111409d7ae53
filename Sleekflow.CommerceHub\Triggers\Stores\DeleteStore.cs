using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Queries;

namespace Sleekflow.CommerceHub.Triggers.Stores;

[TriggerGroup(ControllerNames.Stores)]
public class DeleteStore : ITrigger<DeleteStore.DeleteStoreInput, DeleteStore.DeleteStoreOutput>
{
    private readonly IStoreService _storeService;
    private readonly IProductService _productService;

    public DeleteStore(
        IStoreService storeService,
        IProductService productService)
    {
        _storeService = storeService;
        _productService = productService;
    }

    public class DeleteStoreInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteStoreInput(
            string id,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowTeamIds)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowTeamIds;
        }
    }

    public class DeleteStoreOutput
    {
    }

    public async Task<DeleteStoreOutput> F(DeleteStoreInput deleteStoreInput)
    {
        var storeId = deleteStoreInput.Id;
        var sleekflowCompanyId = deleteStoreInput.SleekflowCompanyId;
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deleteStoreInput.SleekflowStaffId,
            deleteStoreInput.SleekflowStaffTeamIds);

        await _storeService.DeleteStoreAsync(
            deleteStoreInput.Id,
            deleteStoreInput.SleekflowCompanyId,
            sleekflowStaff);

        var continuationToken = string.Empty;
        var productIds = new List<string>();

        while (continuationToken != null)
        {
            var (products, nextContinuationToken) = await _productService.GetProductsAsync(
                sleekflowCompanyId,
                storeId,
                100,
                new List<QueryBuilder.FilterGroup>(),
                new List<QueryBuilder.Sort>(),
                continuationToken == string.Empty ? null : continuationToken);
            productIds = productIds.Concat(products.Select(p => p.Id)).ToList();
            continuationToken = nextContinuationToken;
        }

        foreach (var productId in productIds)
        {
            await _productService.DeleteProductAsync(productId, sleekflowCompanyId, storeId, sleekflowStaff);
        }

        return new DeleteStoreOutput();
    }
}