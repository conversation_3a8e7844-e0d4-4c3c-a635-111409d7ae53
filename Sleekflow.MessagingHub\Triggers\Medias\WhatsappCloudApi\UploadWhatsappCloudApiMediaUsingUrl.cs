using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Files;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Medias;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Medias.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Medias)]
public class UploadWhatsappCloudApiMediaUsingUrl
    : ITrigger<
        UploadWhatsappCloudApiMediaUsingUrl.UploadWhatsappCloudApiMediaUsingUrlInput,
        UploadWhatsappCloudApiMediaUsingUrl.UploadWhatsappCloudApiMediaUsingUrlOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IFileDownloadService _fileDownloadService;

    private readonly ICloudApiMediaManagementFacade _cloudApiMediaManagementFacade;

    public UploadWhatsappCloudApiMediaUsingUrl(
        IWabaService wabaService,
        ICloudApiMediaManagementFacade cloudApiMediaManagementFacade,
        IFileDownloadService fileDownloadService)
    {
        _wabaService = wabaService;
        _cloudApiMediaManagementFacade = cloudApiMediaManagementFacade;
        _fileDownloadService = fileDownloadService;
    }

    public class UploadWhatsappCloudApiMediaUsingUrlInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("waba_phone_number_id")]
        public string WabaPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("file_name")]
        public string FileName { get; set; }

        [Required]
        [JsonProperty("content_type")]
        public string ContentType { get; set; }

        [Required]
        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonConstructor]
        public UploadWhatsappCloudApiMediaUsingUrlInput(
            string wabaId,
            string wabaPhoneNumberId,
            string sleekflowCompanyId,
            string url,
            string fileName,
            string contentType)
        {
            WabaId = wabaId;
            WabaPhoneNumberId = wabaPhoneNumberId;
            SleekflowCompanyId = sleekflowCompanyId;
            Url = url;
            FileName = fileName;
            ContentType = contentType;
        }
    }

    public class UploadWhatsappCloudApiMediaUsingUrlOutput
    {
        [JsonProperty("media_id")]
        public string MediaId { get; set; }

        [JsonConstructor]
        public UploadWhatsappCloudApiMediaUsingUrlOutput(string mediaId)
        {
            MediaId = mediaId;
        }
    }

    public async Task<UploadWhatsappCloudApiMediaUsingUrlOutput> F(
        UploadWhatsappCloudApiMediaUsingUrlInput uploadWhatsappCloudApiMediaUsingUrlInput)
    {
        var wabaPhoneNumberId = uploadWhatsappCloudApiMediaUsingUrlInput.WabaPhoneNumberId;
        var sleekflowCompanyId = uploadWhatsappCloudApiMediaUsingUrlInput.SleekflowCompanyId;

        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            uploadWhatsappCloudApiMediaUsingUrlInput.WabaId,
            sleekflowCompanyId,
            wabaPhoneNumberId);

        var wabaPhoneNumber =
            waba.WabaPhoneNumbers.FirstOrDefault(
                w =>
                    w.Id == wabaPhoneNumberId && w.SleekflowCompanyId == sleekflowCompanyId) ??
            throw new SfNotFoundObjectException("WabaPhoneNumber");

        var downloadFileFromUrlOutput =
            await _fileDownloadService.DownloadFileFromUrlAsync(uploadWhatsappCloudApiMediaUsingUrlInput.Url);

        if (downloadFileFromUrlOutput.FileContent.Length == 0)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        $"An empty file is found")
                });
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        return new UploadWhatsappCloudApiMediaUsingUrlOutput(
            await _cloudApiMediaManagementFacade.UploadCloudApiMediaAsync(
                wabaPhoneNumber.FacebookPhoneNumberId,
                downloadFileFromUrlOutput.FileContent,
                downloadFileFromUrlOutput.ContentType,
                downloadFileFromUrlOutput.FileName ?? uploadWhatsappCloudApiMediaUsingUrlInput.FileName,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null));
    }
}