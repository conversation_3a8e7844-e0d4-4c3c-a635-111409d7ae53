using MassTransit;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.WorkflowSteps;
using State = MassTransit.State;

namespace Sleekflow.FlowHub.Executor.Saga;

public class AggregateState : SagaStateMachineInstance
{
    [JsonProperty("id")]
    public Guid CorrelationId { get; set; }

    public int CurrentState { get; set; }

    public string? AggregateStepId { get; set; }

    public string? ProxyStateId { get; set; }

    public int Duration { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public Guid? ScheduleToken { get; set; }

    [JsonProperty("_etag")]
    public string ETag { get; set; }
}

public class AggregateStateMachine : MassTransitStateMachine<AggregateState>
{
    private readonly ILogger<AggregateStateMachine> _logger;

    public State Aggregating { get; private set; }

    public Event<OnAggregateStepEvent> OnAggregateStepEvent { get; private set; }

    public Schedule<AggregateState, OnAggregateStepFinishEvent> OnAggregateStepFinishEvent { get; private set; }

    public AggregateStateMachine(ILogger<AggregateStateMachine> logger)
    {
        _logger = logger;

        InstanceState(x => x.CurrentState, Aggregating);

        Event(
            () => OnAggregateStepEvent,
            x =>
                x.CorrelateById(x => x.Message.GetCorrelationId()));

        Schedule(
            () => OnAggregateStepFinishEvent,
            x => x.ScheduleToken,
            r =>
            {
                r.Received = r => r.CorrelateById(context => context.Message.CorrelationId);
            });

        HandleInitialState();
        HandleAggregatingState();

        // delete the saga instance once the aggregation timer is up,
        // receiving another OnAggregateStepEvent will create a new aggregation saga
        SetCompletedWhenFinalized();
    }

    private void HandleInitialState()
    {
        Initially(
            When(OnAggregateStepEvent)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "Initially: OnAggregateStepEvent: {CorrelationId}",
                            x.Saga.CorrelationId);

                        x.Saga.AggregateStepId = x.Message.AggregateStepId;
                        x.Saga.ProxyStateId = x.Message.ProxyStateId;
                        x.Saga.Duration = x.Message.Duration;
                        x.Saga.StackEntries = x.Message.StackEntries;
                    })
                .Schedule(
                    OnAggregateStepFinishEvent,
                    x => new OnAggregateStepFinishEvent(x.Saga.CorrelationId),
                    x => TimeSpan.FromSeconds(x.Message.Duration))
                .TransitionTo(Aggregating));
    }

    private void HandleAggregatingState()
    {
        During(
            Aggregating,
            When(OnAggregateStepEvent)
                .Then(
                    x =>
                    {
                        _logger.LogInformation(
                            "Aggregating: OnAggregateStepEvent: {CorrelationId}",
                            x.Saga.CorrelationId);
                    })
                .Publish(
                    x => new OnAggregateStepAggregatedEvent(
                        x.Message.AggregateStepId,
                        x.Message.ProxyStateId,
                        x.Message.StackEntries)),
            When(OnAggregateStepFinishEvent.Received)
                .Publish(
                    x => new OnAggregateStepAggregatedEvent(
                        x.Saga.AggregateStepId!,
                        x.Saga.ProxyStateId!,
                        x.Saga.StackEntries))
                .Finalize());
    }
}