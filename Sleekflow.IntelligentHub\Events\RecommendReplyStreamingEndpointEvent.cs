using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Triggers.RecommendedReplies;

namespace Sleekflow.IntelligentHub.Events;

public class RecommendReplyStreamingEndpointEvent
{
    [JsonProperty]
    public RecommendReplyStreaming.RecommendReplyStreamingInput Input { get; set; }

    [JsonConstructor]
    public RecommendReplyStreamingEndpointEvent(RecommendReplyStreaming.RecommendReplyStreamingInput input)
    {
        Input = input;
    }
}