using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.States;

public class StateIdentityDto : IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("object_id")]
    public string ObjectId { get; set; }

    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Json<PERSON>roperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [JsonProperty("object_label")]
    public string ObjectLabel { get; set; }

    /// <summary>
    /// This is mainly to store the resolved ID of an object, so that FE can navigate
    /// to the object if applicable.
    /// <example>
    /// 1. If the <c>object_type</c> is Contact or Contact.Id, the value of this field should be set to
    /// same as <c>object_id</c>. However, if the <c>object_type</c> is Contact.PhoneNumber or Contact.Email,
    /// this should be resolved to the contact's id.
    /// </example>
    /// </summary>
    [JsonProperty("object_resolved_id")]
    public string ObjectResolvedId { get; set; }

    [JsonConstructor]
    public StateIdentityDto(
        string sleekflowCompanyId,
        string objectId,
        string workflowId,
        string workflowVersionedId,
        string objectType,
        string objectLabel,
        string objectResolvedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ObjectId = objectId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        ObjectType = objectType;
        ObjectLabel = objectLabel;
        ObjectResolvedId = objectResolvedId;
    }

    public StateIdentityDto(StateIdentity stateIdentity)
        : this(
            stateIdentity.SleekflowCompanyId,
            stateIdentity.ObjectId,
            stateIdentity.WorkflowId,
            stateIdentity.WorkflowVersionedId,
            stateIdentity.ObjectType,
            string.Empty,
            string.Empty)
    {
    }
}