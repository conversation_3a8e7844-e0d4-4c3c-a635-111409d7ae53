using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

public class WabaBalanceAutoTopUpProfileDto
{
    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("minimum_balance")]
    public Money MinimumBalance { get; set; }

    [JsonProperty("auto_top_up_plan")]
    public StripeWhatsAppCreditTopUpPlan AutoTopUpPlan { get; set; }

    [JsonProperty("is_auto_top_up_enabled")]
    public bool IsAutoTopUpEnabled { get; set; }

    [JsonConstructor]
    public WabaBalanceAutoTopUpProfileDto(
        string facebookWabaId,
        string facebookBusinessId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled)
    {
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        MinimumBalance = minimumBalance;
        AutoTopUpPlan = autoTopUpPlan;
        IsAutoTopUpEnabled = isAutoTopUpEnabled;
    }

    public WabaBalanceAutoTopUpProfileDto(WabaBalanceAutoTopUpProfile wabaBalanceAutoTopUpProfile)
        : this(
            wabaBalanceAutoTopUpProfile.FacebookWabaId,
            wabaBalanceAutoTopUpProfile.FacebookBusinessId,
            wabaBalanceAutoTopUpProfile.MinimumBalance,
            wabaBalanceAutoTopUpProfile.AutoTopUpPlan,
            wabaBalanceAutoTopUpProfile.IsAutoTopUpEnabled)
    {
    }
}