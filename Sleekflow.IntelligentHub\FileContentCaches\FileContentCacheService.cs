using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.FileContentCaches;
using Sleekflow.Locks;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.FileContentCaches;

public interface IFileContentCacheService
{
    Task<FileContentCache?> GetCachedFileContentAsync(string fileUrl, string? background = null);
    Task<FileContentCache> SaveFileContentToCache(
        string fileUrl,
        string fileMimeType,
        long? fileSize,
        string extractedContent,
        string? background = null);
}

public class FileContentCacheService : IFileContentCacheService, IScopedService
{
    private readonly IIdService _idService;
    private readonly ILockService _lockService;
    private readonly IFileContentCacheRepository _fileContentCacheRepository;
    private readonly ILogger<FileContentCacheService> _logger;

    public FileContentCacheService(
        IIdService idService,
        ILockService lockService,
        IFileContentCacheRepository fileContentCacheRepository,
        ILogger<FileContentCacheService> logger)
    {
        _idService = idService;
        _lockService = lockService;
        _fileContentCacheRepository = fileContentCacheRepository;
        _logger = logger;
    }

    public async Task<FileContentCache?> GetCachedFileContentAsync(string fileUrl, string? background = null)
    {
        try
        {
            _logger.LogInformation("Looking for cached content. FileUrl: {FileUrl}, Background: {Background}",
                fileUrl, background);

            // Query by file URL and background directly
            var cacheEntries = await _fileContentCacheRepository.GetObjectsAsync(
                c => c.FileUrl == fileUrl && c.Background == background,
                1);

            var cache = cacheEntries.FirstOrDefault();

            if (cache == null)
            {
                _logger.LogInformation("No cache entry found for file: {FileUrl}", fileUrl);
                return null;
            }

            _logger.LogInformation("Found cached content for file: {FileUrl}", fileUrl);
            return cache;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached file content for: {FileUrl}", fileUrl);
            return null;
        }
    }

    public async Task<FileContentCache> SaveFileContentToCache(
        string fileUrl,
        string fileMimeType,
        long? fileSize,
        string extractedContent,
        string? background = null)
    {
        var cacheKey = background != null ? $"{fileUrl}:{background}" : fileUrl;

        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(FileContentCacheService),
                cacheKey
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            _logger.LogInformation(
                "Saving file content to cache. FileUrl: {FileUrl}, MimeType: {MimeType}, Size: {FileSize}",
                fileUrl, fileMimeType, fileSize);

            // Check if cache entry already exists and delete it to avoid duplicates
            var existingEntries = await _fileContentCacheRepository.GetObjectsAsync(
                c => c.FileUrl == fileUrl && c.Background == background,
                1);

            var existingEntry = existingEntries.FirstOrDefault();
            if (existingEntry != null)
            {
                _logger.LogInformation("Updating existing cache entry: {CacheId}", existingEntry.Id);
                await _fileContentCacheRepository.DeleteAsync(existingEntry.Id, existingEntry.FileUrl);
            }

            var cache = FileContentCache.Create(
                _idService.GetId(SysTypeNames.FileContentCache),
                fileUrl,
                fileMimeType,
                fileSize,
                extractedContent,
                background);

            return await _fileContentCacheRepository.UpsertAndGetAsync(cache, cache.FileUrl);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }
}