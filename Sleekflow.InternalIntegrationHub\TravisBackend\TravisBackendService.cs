using Sleekflow.DependencyInjection;
using Sleekflow.**********************.CompanyServerLocation;
using Sleekflow.**********************.Models.NetSuite.Integrations;
using Sleekflow.**********************.Models.TravisBackend;
using Sleekflow.**********************.NetSuite;
using Sleekflow.**********************.Repositories;
using Sleekflow.Models.Constants;

namespace Sleekflow.**********************.TravisBackend;

public interface ITravisBackendService
{
    public Task<List<(Company Company, CompanyOwner? CompanyOwner, CompanyOwner? SalesRep, List<CompanyBillRecord>?
            BillRecords)>>
        GetAllCompanies();

    public Task<int> CreatePaymentRecordAsync(List<CreatePaymentRequest> requests);

    public Task<List<GetPaymentResponse>?> GetPaymentRecord(string billRecordId, string companyId);
}

public class TravisBackendService : ITravisBackendService, IScopedService
{
    private readonly ITravisBackendRepository _travisBackendRepository;
    private readonly ICompanyServerLocationService _companyServerLocationService;
    private readonly ILogger<TravisBackendService> _logger;
    private readonly INetSuiteSettingService _netSuiteSettingService;

    public TravisBackendService(
        ITravisBackendRepository travisBackendRepository,
        ICompanyServerLocationService companyServerLocationService,
        ILogger<TravisBackendService> logger,
        INetSuiteSettingService netSuiteSettingService)
    {
        _travisBackendRepository = travisBackendRepository;
        _companyServerLocationService = companyServerLocationService;
        _logger = logger;
        _netSuiteSettingService = netSuiteSettingService;
    }

    public async Task<List<(Company Company, CompanyOwner? CompanyOwner, CompanyOwner? SalesRep, List<CompanyBillRecord>
            ?
            BillRecords)>>
        GetAllCompanies()
    {
        var result =
            new List<(Company Company, CompanyOwner? CompanyOwner, CompanyOwner? SalesRep, List<CompanyBillRecord>?
                BillRecords)>();

        foreach (var location in ServerLocations.GetAll())
        {
            var offset = 0;
            while (true)
            {
                var companies = await _travisBackendRepository.GetCompanies(offset, location);
                if (companies.Count == 0)
                {
                    break;
                }

                foreach (var company in companies)
                {
                    var companyOwner = await _travisBackendRepository.GetCompanyOwner(company.Id, location);
                    var salesRep =
                        await _travisBackendRepository.GetCompanySalesRepresentative(
                            company.CmsActivationOwnerId,
                            location);
                    var billRecord = await _travisBackendRepository.GetCompanyBillRecords(company.Id, location);
                    result.Add((company, companyOwner, salesRep, billRecord));
                }

                offset += 1000;
            }
        }

        return result;
    }

    public async Task<int> CreatePaymentRecordAsync(List<CreatePaymentRequest> requests)
    {
        var (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails) =
            await _netSuiteSettingService.NetSuitePreparation(
                CancellationToken.None,
                showEmployee: false,
                showSubsidiary: false);

        var rowAffected = 0;
        foreach (var request in requests)
        {
            var companyId = request.CustomerId;
            var companyServerLocation = await _companyServerLocationService.GetCompanyServerLocation(companyId);

            var currency = currencyDetails.FirstOrDefault(
                x => string.Equals(x.Id, request.Currency, StringComparison.OrdinalIgnoreCase),
                currencyDetails.First());

            var term = termDetails.FirstOrDefault(x => string.Equals(x.Id, request.PaymentTerms), termDetails.First());
            var timestamp = ((DateTimeOffset)DateTime.Now).ToUnixTimeMilliseconds();

            var invoiceId = "INV-" + request.BillRecordId;
            if (request.SubscriptionFee < 0 || request.OneTimeSetupFee < 0 || request.WhatsappCreditAmount < 0)
            {
                invoiceId = "INV-REFUND-" + request.BillRecordId;
            }

            var billRecordId = request.BillRecordId.Split("_")[0];

            var paymentRecord = new PaymentRecord(
                request.CustomerId,
                long.Parse(billRecordId),
                request.SubscriptionFee,
                request.OneTimeSetupFee,
                request.WhatsappCreditAmount,
                currency.Name.ToLower(),
                1, // bank payment method
                request.PaidAt,
                invoiceId,
                DateTime.Now,
                DateTime.Now,
                Convert.ToInt32(term.DaysUntilNetDue));
            var result = await _travisBackendRepository.UpsertPaymentRecord(paymentRecord, companyServerLocation);
            if (result == 0)
            {
                _logger.LogError("Failed to upsert payment record for customer {CustomerId}", request.CustomerId);
            }
            else
            {
                _logger.LogInformation("Payment record upserted for customer {CustomerId}", request.CustomerId);
            }

            rowAffected += result;
        }

        return rowAffected;
    }

    public async Task<List<GetPaymentResponse>?> GetPaymentRecord(string billRecordId, string companyId)
    {
        var companyServerLocation = await _companyServerLocationService.GetCompanyServerLocation(companyId);
        var paymentRecords = await _travisBackendRepository.GetPaymentRecords(billRecordId, companyServerLocation);
        if (paymentRecords.Count == 0)
        {
            return null;
        }

        var (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails) =
            await _netSuiteSettingService.NetSuitePreparation(
                CancellationToken.None,
                showEmployee: false,
                showSubsidiary: false,
                showCurrency: false);

        var response = new List<GetPaymentResponse>();
        foreach (var record in paymentRecords)
        {
            var termId = termDetails.FirstOrDefault(
                x => x.DaysUntilNetDue == record.PaymentTermInt,
                termDetails.First()).Id;
            response.Add(
                new GetPaymentResponse(
                    record.CompanyId,
                    record.BillRecordId.ToString(),
                    record.SubscriptionFee,
                    record.OneTimeSetupFee,
                    record.WhatsappCreditAmount,
                    record.Currency,
                    record.PaidAt,
                    record.InvoiceId,
                    termId));
        }

        return response;
    }
}