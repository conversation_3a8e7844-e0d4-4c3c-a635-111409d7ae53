using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using StackExchange.Redis;
using Newtonsoft.Json;
using Sleekflow.JsonConfigs;

namespace Sleekflow.IntelligentHub.Services;

public interface IUserInteractionTrackingService
{
    /// <summary>
    /// Records the latest user message for a specific object ID
    /// </summary>
    Task RecordUserMessageAsync(string companyId, string objectId, string userMessage);

    /// <summary>
    /// Gets the last user message for a specific object ID
    /// </summary>
    Task<string?> GetLastUserMessageAsync(string companyId, string objectId);

    /// <summary>
    /// Checks if user has sent a new message since the original message (for follow-up purposes)
    /// </summary>
    Task<bool> HasUserSentNewMessageAsync(string companyId, string objectId, string originalMessage);
}

public class UserInteractionTrackingService : IUserInteractionTrackingService, ISingletonService
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILogger<UserInteractionTrackingService> _logger;
    private static readonly TimeSpan DataExpiration = TimeSpan.FromDays(7); // Keep tracking data for 7 days

    public UserInteractionTrackingService(
        IConnectionMultiplexer connectionMultiplexer,
        ILogger<UserInteractionTrackingService> logger)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _logger = logger;
    }

    public async Task RecordUserMessageAsync(string companyId, string objectId, string userMessage)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var key = GetUserMessageKey(companyId, objectId);

            await database.StringSetAsync(
                key,
                userMessage,
                DataExpiration);

            _logger.LogDebug("Recorded user message for {CompanyId}/{ObjectId}: {Message}",
                companyId, objectId, userMessage);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to record user message for {CompanyId}/{ObjectId}", companyId, objectId);
        }
    }

    public async Task<string?> GetLastUserMessageAsync(string companyId, string objectId)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var key = GetUserMessageKey(companyId, objectId);

            var result = await database.StringGetAsync(key);
            return result.HasValue ? result!.ToString() : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get last user message for {CompanyId}/{ObjectId}", companyId, objectId);
            return null;
        }
    }

    public async Task<bool> HasUserSentNewMessageAsync(string companyId, string objectId, string originalMessage)
    {
        try
        {
            var lastUserMessage = await GetLastUserMessageAsync(companyId, objectId);

            // If no user message recorded, assume user has not sent new message
            if (string.IsNullOrEmpty(lastUserMessage))
            {
                return false;
            }

            // If the last user message is different from the original, user has sent a new message
            bool hasSentNewMessage = !string.Equals(lastUserMessage, originalMessage, StringComparison.OrdinalIgnoreCase);

            _logger.LogDebug("User new message check for {CompanyId}/{ObjectId}: Original='{Original}', Current='{Current}', HasSentNew={HasSentNew}",
                companyId, objectId, originalMessage, lastUserMessage, hasSentNewMessage);

            return hasSentNewMessage;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to check user new message for {CompanyId}/{ObjectId}", companyId, objectId);
            return true; // Assume user sent new message to avoid sending unwanted follow-ups
        }
    }

    private static string GetUserMessageKey(string companyId, string objectId)
    {
        return $"user_message:{companyId}:{objectId}";
    }
}