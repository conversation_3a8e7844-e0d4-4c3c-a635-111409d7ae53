﻿using System.Collections.Immutable;

namespace Sleekflow.CommerceHub.Models.Constants;

/// <summary>
/// Integrated VTEX Order Statuses.
/// <br/><br/>
/// Official Documentation: https://help.vtex.com/en/tutorial/fluxo-de-pedido/#order-status-details
/// </summary>
public static class VtexOrderStatusCodes
{
    // SleekFlow captured statuses
    public const string OrderCreated = "order-created";
    public const string Canceled = "canceled";
    public const string PaymentApproved = "payment-approved";
    public const string ReadyForHandling = "ready-for-handling";
    public const string StartHandling = "start-handling";
    public const string Invoiced = "invoiced";

    // Other statuses
    public const string OrderAccepted = "order-accepted";
    public const string OnOrderCompleted = "on-order-completed";
    public const string OnOrderCompletedFfm = "on-order-completed-ffm";
    public const string AuthorizeFulfillment = "authorize-fulfillment";
    public const string Handling = "handling";
    public const string Invoice = "invoice";
    public const string PaymentDenied = "payment-denied";
    public const string PaymentPending = "payment-pending";
    public const string RequestCancel = "request-cancel";
    public const string WaitingForAuthorization = "waiting-for-authorization";
    public const string WaitingForSellerDecision = "waiting-for-seller-decision";
    public const string WindowToCancel = "window-to-cancel";
    public const string WindowToChangePayment = "window-to-change-payment";
    public const string WindowToChangeSeller = "window-to-change-seller";
    public const string WaitingForFulfillment = "waiting-for-fulfillment";
    public const string WaitingFfmtAuthorization = "waiting-ffmt-authorization";
    public const string WaitingForManualAuthorization = "waiting-for-manual-authorization";
    public const string ReadyForInvoicing = "ready-for-invoicing";
    public const string CancellationRequested = "cancellation-requested";
    public const string WaitingForMktAuthorization = "waiting-for-mkt-authorization";
    public const string WaitingSellerHandling = "waiting-seller-handling";

    public static readonly ImmutableList<string> Captured = ImmutableList.Create(
        OrderCreated,
        Canceled,
        PaymentApproved,
        ReadyForHandling,
        StartHandling,
        Invoiced);

    public static readonly ImmutableList<string> All = ImmutableList.Create(
        OrderCreated,
        Canceled,
        PaymentApproved,
        ReadyForHandling,
        StartHandling,
        Invoiced,
        OrderAccepted,
        OnOrderCompleted,
        OnOrderCompletedFfm,
        AuthorizeFulfillment,
        Handling,
        Invoice,
        PaymentDenied,
        PaymentPending,
        RequestCancel,
        WaitingForAuthorization,
        WaitingForSellerDecision,
        WindowToCancel,
        WindowToChangePayment,
        WindowToChangeSeller,
        WaitingForFulfillment,
        WaitingFfmtAuthorization,
        WaitingForManualAuthorization,
        ReadyForInvoicing,
        CancellationRequested,
        WaitingForMktAuthorization,
        WaitingSellerHandling);

    public static string ToStatusDisplayName(string status)
    {
        return status switch
        {
            OrderCreated => "Order Created",
            Canceled => "Canceled",
            PaymentApproved => "Payment Approved",
            ReadyForHandling => "Ready for Handling",
            StartHandling => "Start Handling",
            Invoiced => "Invoiced",
            OrderAccepted => "Order Accepted",
            OnOrderCompleted => "On Order Completed",
            OnOrderCompletedFfm => "On Order Completed FFM",
            AuthorizeFulfillment => "Authorize Fulfillment",
            Handling => "Handling",
            Invoice => "Invoice",
            PaymentDenied => "Payment Denied",
            PaymentPending => "Payment Pending",
            RequestCancel => "Request Cancel",
            WaitingForAuthorization => "Waiting for Authorization",
            WaitingForSellerDecision => "Waiting for Seller Decision",
            WindowToCancel => "Window to Cancel",
            WindowToChangePayment => "Window to Change Payment",
            WindowToChangeSeller => "Window to Change Seller",
            WaitingForFulfillment => "Waiting for Fulfillment",
            WaitingFfmtAuthorization => "Waiting Ffmt Authorization",
            WaitingForManualAuthorization => "Waiting for Manual Authorization",
            ReadyForInvoicing => "Ready for Invoicing",
            CancellationRequested => "Cancellation Requested",
            WaitingForMktAuthorization => "Waiting for Mkt Authorization",
            WaitingSellerHandling => "Waiting Seller Handling",

            _ => "Unknown"
        };
    }
}