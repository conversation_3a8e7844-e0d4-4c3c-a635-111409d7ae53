using System.Diagnostics;
using Sleekflow.IntelligentHub.Evaluator.LeadScoreCalculator.Methods;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScoreCalculator;

public class LeadScoreCalculatorFixture
{
    private readonly DefaultLeadScoringCalculatorMethod _method = new();

    public async Task<LeadScoreCalculatorResult[]> EvaluateAsync(
        LeadScoreCalculatorTestCase testCase,
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        LeadScoreCalculatorResult output;

        try
        {
            output = await _method.CompleteAsync(
                testCase.ChatMessageContents,
                testCase.Criteria);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            throw;
        }

        stopwatch.Stop();

        var result = new LeadScoreCalculatorResult(
            output.AgentName,
            output.Answer,
            stopwatch.ElapsedMilliseconds);

        return new[] { result };
    }
}