﻿using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Persistence.CrmHubIntegrationDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class CrmHubModules
{
    public static void BuildCrmHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.AddSingleton<ICrmHubDbConfig>(new Mock<ICrmHubDbConfig>().Object);
        b.Add<PERSON><PERSON><PERSON><ICrmHubIntegrationDbConfig>(new Mock<ICrmHubIntegrationDbConfig>().Object);
        b.Add<PERSON><PERSON><PERSON><ICrmHubDbEntityService>(new Mock<ICrmHubDbEntityService>().Object);
        b.Add<PERSON><PERSON><PERSON><ICrmHubDbResolver>(new Mock<ICrmHubDbResolver>().Object);
        b.<PERSON><PERSON><ICrmHubIntegrationDbResolver>(new Mock<ICrmHubIntegrationDbResolver>().Object);

#else
        var crmHubDbConfig = new CrmHubDbConfig();
        var crmHubIntegrationDbConfig = new CrmHubIntegrationDbConfig();

        b.AddSingleton<ICrmHubDbConfig>(crmHubDbConfig);
        b.AddSingleton<ICrmHubIntegrationDbConfig>(crmHubIntegrationDbConfig);
        b.AddSingleton<ICrmHubDbEntityService, CrmHubDbEntityService>();
        b.AddSingleton<ICrmHubDbResolver, CrmHubDbResolver>();
        b.AddSingleton<ICrmHubIntegrationDbResolver, CrmHubIntegrationDbResolver>();
#endif
    }
}