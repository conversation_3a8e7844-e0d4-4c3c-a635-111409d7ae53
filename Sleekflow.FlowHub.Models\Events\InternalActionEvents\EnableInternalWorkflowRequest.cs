﻿using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Events.InternalActionEvents;

public class EnableInternalWorkflowRequest : IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public string WorkflowVersionedId { get; set; }

    public EnableInternalWorkflowRequest(string sleekflowCompanyId, string workflowVersionedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionedId = workflowVersionedId;
    }
}