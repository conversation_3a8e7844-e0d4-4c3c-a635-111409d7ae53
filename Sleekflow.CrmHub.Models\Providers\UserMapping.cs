﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class UserMapping
{
    [JsonProperty("provider_user_id")]
    public string ProviderUserId { get; set; }

    [JsonProperty("sleekflow_user_id")]
    public string SleekflowUserId { get; set; }

    [JsonConstructor]
    public UserMapping(
        string providerUserId,
        string sleekflowUserId)
    {
        ProviderUserId = providerUserId;
        SleekflowUserId = sleekflowUserId;
    }
}