# Sleekflow UserEventAnalyticsHub DataCompactor Implementation Plan

## Overview
Migrate existing Parquet files from Azure Blob Storage to PostgreSQL to improve query performance by reducing the number of small files that DuckDB needs to process.

## Architecture

### Core Components

1. **CompactorConfig** - Configuration POCO classes using .NET Configuration system to manage Azure Storage credentials, PostgreSQL connection strings, and processing parameters
2. **BlobDiscoveryService** - Uses Azure.Storage.Files.DataLake SDK to enumerate and inspect Parquet files in Azure Data Lake Storage, parsing blob paths to extract company IDs and partition information
3. **PostgreSqlSchemaService** - Uses Npgsql (PostgreSQL .NET driver) to execute DDL statements for creating company-specific tables, indexes, and managing database schema evolution
4. **FileTrackingService** - Uses Npgsql with parameterized queries to insert/update migration tracking records in PostgreSQL, managing transaction state and retry logic
5. **ParquetCompactionService** - Uses DuckDB.NET.Data to orchestrate data migration by leveraging DuckDB's PostgreSQL extension to transfer data directly from Parquet files to PostgreSQL tables
6. **DualLocationQueryService** - Extends existing SqlJobProcessingService to use both DuckDB (for PostgreSQL queries) and Azure blob storage (for unmigrated data) with intelligent query routing

## Database Design

### PostgreSQL Schema
Each company will have dedicated tables in PostgreSQL:

#### Data Table: `events_{sleekflowCompanyId}`
```sql
-- Schema matches Azure Stream Analytics transformation query output
CREATE TABLE events_{sleekflowCompanyId} (
    id VARCHAR PRIMARY KEY,
    eventType VARCHAR,
    sleekflowCompanyId VARCHAR NOT NULL,
    objectId VARCHAR,
    objectType VARCHAR,
    source VARCHAR,
    properties JSONB,  -- JSON Record from Stream Analytics
    metadata JSONB,    -- JSON Record from Stream Analytics
    timestamp BIGINT NOT NULL, -- Milliseconds since epoch from DATEDIFF calculation
    -- Partition columns extracted from blob path (sleekflowCompanyId=xxx/year=yyyy/month=mm/day=dd/hour=hh/)
    year INTEGER NOT NULL,
    month INTEGER NOT NULL,
    day INTEGER NOT NULL,
    hour INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Indexes for query performance
CREATE INDEX idx_events_{sleekflowCompanyId}_timestamp ON events_{sleekflowCompanyId} (timestamp);
CREATE INDEX idx_events_{sleekflowCompanyId}_eventtype ON events_{sleekflowCompanyId} (eventType);
CREATE INDEX idx_events_{sleekflowCompanyId}_objectid ON events_{sleekflowCompanyId} (objectId);
CREATE INDEX idx_events_{sleekflowCompanyId}_partitions ON events_{sleekflowCompanyId} (year, month, day, hour);
```

#### Tracking Table: `file_migration_history_{sleekflowCompanyId}`
```sql
CREATE TABLE file_migration_history_{sleekflowCompanyId} (
    id SERIAL PRIMARY KEY,
    blob_path VARCHAR NOT NULL UNIQUE,
    blob_last_modified TIMESTAMP NOT NULL,
    blob_size_bytes BIGINT NOT NULL,
    records_count INTEGER NOT NULL,
    migration_started_at TIMESTAMP NOT NULL,
    migration_completed_at TIMESTAMP,
    migration_status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, completed, failed
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_file_migration_history_{sleekflowCompanyId}_status ON file_migration_history_{sleekflowCompanyId} (migration_status);
CREATE INDEX idx_file_migration_history_{sleekflowCompanyId}_blob_path ON file_migration_history_{sleekflowCompanyId} (blob_path);
```

## Implementation Steps

### Phase 1: Core Infrastructure Setup

1. **Project Dependencies**
   - Add NuGet packages: DuckDB.NET.Data, Azure.Storage.Blobs, Npgsql, Microsoft.Extensions.Hosting
   - Reference Sleekflow core packages for configuration and logging

2. **Configuration Classes**
   - `CompactorConfig`: Azure Storage connection, PostgreSQL connection, batch sizes
   - `PostgreSqlConfig`: Connection strings, database name, schema settings
   - `ProcessingConfig`: Concurrency limits, retry policies, batch sizes

3. **Service Registration**
   - Configure dependency injection
   - Set up logging and configuration
   - Register all services as scoped/singleton appropriately

### Phase 2: Discovery and Tracking Services

4. **BlobDiscoveryService**
   - Use Azure.Storage.Files.DataLake DataLakeFileSystemClient to enumerate files with hierarchical namespace support
   - Enumerate paths using GetPathsAsync() with recursive=true to discover all parquet files
   - Parse blob path patterns (sleekflowCompanyId={id}/year={yyyy}/month={mm}/day={dd}/hour={hh}/) using System.Text.RegularExpressions to extract partition values
   - Extract file metadata (LastModified, ContentLength) via DataLakeFileClient.GetPropertiesAsync()

5. **PostgreSqlSchemaService**
   - Use Npgsql NpgsqlConnection to execute CREATE TABLE IF NOT EXISTS statements with company-specific table names
   - Generate DDL dynamically using string interpolation for table/index names
   - Execute CREATE INDEX IF NOT EXISTS statements for timestamp, eventType, objectId, and partition columns

6. **FileTrackingService**
   - Use Npgsql with parameterized queries (INSERT/UPDATE/SELECT) to manage file_migration_history tables
   - Implement NpgsqlTransaction for atomic updates to migration status
   - Use exponential backoff with Polly library for retry policies on failed migrations
   - Execute DELETE statements to clean up tracking records for blobs that no longer exist

### Phase 3: Core Compaction Logic

7. **ParquetCompactionService**
   - Use Task.Run() with SemaphoreSlim to control concurrent company processing
   - Implement unified batch processing by grouping files into chunks (e.g., 50-100 files per batch) regardless of file size
   - Use DuckDB's INSERT INTO postgres_table SELECT * FROM parquet_scan() with UNION ALL for multiple files
   - Wrap all operations in try-catch blocks with structured logging and NpgsqlTransaction rollback

8. **DuckDB Integration**
   - Use DuckDB.NET.Data DuckDBConnection with PostgreSQL extension loaded via "LOAD postgres;" command
   - Configure DuckDB settings: SET memory_limit='1536MB', SET threads TO 16 for optimal performance
   - Use DuckDB's ATTACH DATABASE syntax to connect to PostgreSQL: ATTACH 'dbname=sleekflow_events host=...' AS postgres_db (TYPE postgres)
   - Execute bulk transfer using: INSERT INTO postgres_db.events_{companyId} SELECT * FROM parquet_scan('abfss://account.dfs.core.windows.net/container/path/*.parquet')
   - Use UNION ALL pattern for batch processing: SELECT * FROM parquet_scan('file1.parquet') UNION ALL SELECT * FROM parquet_scan('file2.parquet')

### Phase 4: Query Service Updates

9. **DualLocationQueryService**
   - Extend SqlJobProcessingService.SetUpEventsDbAsync() to create UNION ALL views combining PostgreSQL and Parquet data
   - Use DuckDB's ATTACH DATABASE to connect to PostgreSQL, then create federated queries across both sources
   - Implement date range filtering to route queries: PostgreSQL for migrated date ranges, Parquet for unmigrated data
   - Use DuckDB's query optimizer to push down predicates and optimize cross-source joins
   - Add migration status checks using FileTrackingService to determine which data ranges are available in PostgreSQL

### Future Phases (Post-E2E Implementation)

**Phase 5: Advanced Schema Management**
- Implement information_schema queries to validate existing table structures and detect schema drift
- Add automatic schema migration for evolving data structures
- Implement schema version tracking and backward compatibility

**Phase 6: Maintenance and Monitoring**
- Add comprehensive data integrity validation with row count verification
- Implement automated cleanup of successfully migrated Parquet files
- Add real-time monitoring dashboard for migration progress and performance metrics

## Processing Algorithm

### Company-Level Processing
```
FOR each company_id:
    1. Discover all blob files for company
    2. Query migration history from PostgreSQL
    3. Identify unprocessed files
    4. Sort files by date/size for optimal processing
    5. Process files in batches
    6. Update migration tracking
    7. Verify data integrity
```

### File-Level Processing
```
FOR each file batch:
    1. Start NpgsqlTransaction with IsolationLevel.ReadCommitted
    2. INSERT INTO file_migration_history with status='in_progress' using parameterized query
    3. Use DuckDB parquet_scan() function with Azure blob SAS token for authentication
    4. Execute DuckDB INSERT INTO postgres_table SELECT * FROM parquet_scan('abfss://...')
    5. UPDATE file_migration_history SET status='completed', records_count=COUNT(*)
    6. Commit NpgsqlTransaction
    7. On exception: ROLLBACK transaction, UPDATE status='failed', INCREMENT retry_count
```

## Performance Considerations

1. **Batch Processing**
   - Group 50-100 files into single DuckDB transaction using UNION ALL of parquet_scan() calls regardless of file size
   - Use DuckDB's parallel parquet reader with SET threads TO 16 for concurrent file processing
   - Implement Task.WhenAll() with SemaphoreSlim(MaxConcurrentCompanies) to process companies in parallel

2. **Memory Management**
   - Configure DuckDB with SET memory_limit='1536MB' and SET max_memory='2GB'
   - Use DuckDB streaming execution by avoiding SELECT * and using column projection
   - Monitor System.GC memory usage and force collection between large batch operations

3. **PostgreSQL Optimization**
   - Use Npgsql prepared statements with NpgsqlCommand.Prepare() for repeated INSERT operations
   - Disable autocommit with NpgsqlConnection.BeginTransaction() for batch operations
   - Configure PostgreSQL: SET synchronous_commit=OFF, SET wal_compression=ON for bulk loading

4. **Error Handling**
   - Implement Polly exponential backoff: RetryPolicy.WaitAndRetry(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)))
   - Log structured errors with Serilog including company_id, file_path, and exception details
   - Use circuit breaker pattern to skip problematic companies and continue processing others

## Potential Concerns for Future Optimization

1. **File Size Variations**
   - Large files (>10MB) may cause memory pressure when processed with many small files in same batch
   - Unified batching approach may not be optimal for extreme file size differences
   - Consider implementing adaptive batch sizing based on total batch memory footprint

2. **DuckDB Memory Management**
   - Processing 50-100 files simultaneously may exceed DuckDB memory limits for large datasets
   - UNION ALL queries with many parquet_scan() calls could impact query planning performance
   - May need dynamic batch size adjustment based on file sizes and available memory

3. **PostgreSQL Connection Limits**
   - Multiple concurrent company processing may exhaust PostgreSQL connection pool
   - DuckDB PostgreSQL extension connection management may conflict with direct Npgsql connections
   - Consider connection pooling strategies and connection lifecycle management

4. **Transaction Size Limits**
   - Very large batches may exceed PostgreSQL transaction size limits or cause lock contention
   - Long-running transactions may impact concurrent query performance
   - May need transaction chunking for very large migration batches

## Monitoring and Observability

1. **Logging**
   - Use Serilog with structured logging: logger.Information("Processing {CompanyId} with {FileCount} files", companyId, fileCount)
   - Implement custom performance counters using System.Diagnostics.Stopwatch for timing metrics
   - Use ILogger with LogLevel.Error for exceptions and LogLevel.Information for progress milestones

2. **Progress Tracking**
   - Query file_migration_history tables with aggregate SQL: SELECT COUNT(*) WHERE status='completed' to calculate progress percentages
   - Use Console.WriteLine() with progress indicators for real-time feedback during console application execution
   - Execute row count comparisons between source Parquet files and destination PostgreSQL tables for data integrity verification

3. **Health Checks**
   - Test PostgreSQL connectivity using NpgsqlConnection.Open() with timeout handling
   - Validate Azure Data Lake access using DataLakeFileSystemClient.ExistsAsync() before starting migration
   - Verify DuckDB functionality by executing simple SELECT 1 query and loading PostgreSQL extension

## Configuration Examples

### appsettings.json Structure
```json
{
  "CompactorConfig": {
    "StorageAccountName": "...",
    "StorageAccountKey": "...",
    "EventsContainerName": "events",
    "MaxConcurrentCompanies": 3,
    "FileBatchSize": 100,
    "RetryAttempts": 3,
    "ProcessingTimeoutMinutes": 30
  },
  "PostgreSqlConfig": {
    "ConnectionString": "...",
    "DatabaseName": "sleekflow_events",
    "SchemaName": "public",
    "MaxConnections": 10
  }
}
```

## Deployment Strategy

1. **Initial Migration**
   - Run as console application for one-time migration
   - Process companies in order of data volume (smallest first)
   - Verify data integrity after each company

2. **Incremental Processing**
   - Later can be enhanced for ongoing file compaction
   - Implement as background service or scheduled job
   - Add reactive monitoring for new files

## Success Criteria

1. All existing Parquet files successfully migrated to PostgreSQL
2. Query performance improved (target: <2s for typical queries)
3. Data integrity verified (row counts, sample data validation)
4. Zero data loss during migration
5. Minimal impact on existing query operations

## Risk Mitigation

1. **Data Backup**: Ensure PostgreSQL has proper backup strategies
2. **Rollback Plan**: Keep original Parquet files until migration verified
3. **Gradual Rollout**: Start with test companies before production migration
4. **Monitoring**: Implement comprehensive logging and alerting
5. **Performance Testing**: Validate query performance improvements

## Current Implementation Status

The initial implementation has been completed with a focus on simplicity and core functionality. The following services have been implemented:

- **Configuration System**: Environment variable-based configuration following hub patterns
- **BlobDiscoveryService**: Azure Data Lake file discovery and metadata extraction
- **PostgreSqlSchemaService**: Database schema management and table creation
- **FileTrackingService**: Migration progress tracking and status management
- **ParquetCompactionService**: Core data migration orchestration using DuckDB

## Remaining Concerns and Future Enhancements

### Performance Optimizations (Future)

1. **Adaptive Batch Sizing**
   - Current implementation uses fixed batch sizes which may not be optimal for varying file sizes
   - Future: Implement dynamic batch sizing based on file sizes and memory usage
   - Consider separating large files (>10MB) into individual batches

2. **Memory Management**
   - Current implementation may exceed memory limits with large batches
   - Future: Add memory monitoring and dynamic adjustment of DuckDB settings
   - Implement streaming for very large datasets

3. **Connection Pooling**
   - Current implementation creates new connections for each operation
   - Future: Implement connection pooling for PostgreSQL and optimize DuckDB connection lifecycle
   - Add connection health checks and reconnection logic

### Error Handling and Resilience (Future)

1. **Retry Logic**
   - Current implementation has basic error handling but limited retry mechanisms
   - Future: Implement exponential backoff with Polly library for transient failures
   - Add circuit breaker pattern for problematic companies

2. **Partial Failure Recovery**
   - Current implementation fails entire batches on any error
   - Future: Implement individual file processing with batch-level rollback
   - Add support for resuming from specific failure points

3. **Data Validation**
   - Current implementation assumes data integrity
   - Future: Add row count validation between source and destination
   - Implement data quality checks and schema validation

### Monitoring and Observability (Future)

1. **Metrics and Dashboards**
   - Current implementation has basic logging
   - Future: Add structured metrics using System.Diagnostics.Metrics
   - Implement real-time progress monitoring with health checks

2. **Alerting**
   - Current implementation lacks alerting capabilities
   - Future: Add email/Slack notifications for failures and completion
   - Implement SLA monitoring and automated escalation

3. **Audit and Compliance**
   - Current implementation has basic tracking
   - Future: Add comprehensive audit logging for compliance requirements
   - Implement data lineage tracking and retention policies

### Scalability Improvements (Future)

1. **Distributed Processing**
   - Current implementation processes on single machine
   - Future: Add support for distributed processing across multiple workers
   - Implement work queue pattern with message brokers

2. **Incremental Updates**
   - Current implementation is designed for one-time migration
   - Future: Add support for incremental updates as new files arrive
   - Implement file watching and automatic processing triggers

3. **Schema Evolution**
   - Current implementation assumes static schema
   - Future: Add support for schema migration and backward compatibility
   - Implement versioning for table structures

### Security Enhancements (Future)

1. **Authentication and Authorization**
   - Current implementation uses basic storage account keys
   - Future: Implement Azure AD authentication and role-based access
   - Add support for managed identities and key vault integration

2. **Data Encryption**
   - Current implementation relies on transport-level encryption
   - Future: Add support for field-level encryption for sensitive data
   - Implement encryption key rotation and management

### Operational Features (Future)

1. **Configuration Management**
   - Current implementation uses environment variables
   - Future: Add support for configuration files and Azure App Configuration
   - Implement configuration validation and hot-reloading

2. **Deployment and Maintenance**
   - Current implementation is a console application
   - Future: Add support for containerization and Kubernetes deployment
   - Implement blue-green deployment strategies

3. **Testing and Quality Assurance**
   - Current implementation lacks comprehensive testing
   - Future: Add unit tests, integration tests, and end-to-end validation
   - Implement test data generation and synthetic data testing

The current implementation provides a solid foundation for the data migration process while maintaining simplicity and reliability. Future enhancements should be prioritized based on actual usage patterns and performance requirements observed in production.