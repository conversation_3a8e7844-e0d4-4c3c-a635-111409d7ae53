using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class StaffRoleUpdatedSystemLogData
{
    [JsonProperty("staff_id")]
    public string StaffId { get; set; }

    [JsonProperty("role")]
    public string Role { get; set; }

    [JsonConstructor]
    public StaffRoleUpdatedSystemLogData(string staffId, string role)
    {
        StaffId = staffId;
        Role = role;
    }
}