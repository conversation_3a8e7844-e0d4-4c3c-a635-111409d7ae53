using System.ComponentModel;
using System.Text;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FileContentCaches;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Plugins;

public interface IFileContentExtractionPlugin
{
    Task<string> ExtractTextFromFileAsync(Kernel kernel, SfChatEntryFile file, string? background, bool useCache = true);

    Task<List<string>> ExtractTextFromFilesAsync(Kernel kernel, List<SfChatEntryFile> files, string? background, bool useCache = true);
}

public class FileContentExtractionPlugin : IFileContentExtractionPlugin, IScopedService
{
    private readonly ILogger<FileContentExtractionPlugin> _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly IFileContentCacheService _fileContentCacheService;

    // Supported text-based file types that can be processed directly
    private static readonly HashSet<string> SupportedTextMimeTypes = new(StringComparer.OrdinalIgnoreCase)
    {
        "text/plain",
        "text/csv",
        "text/html",
        "text/xml",
        "application/json",
        "application/xml",
        "text/markdown",
        "text/rtf"
    };

    // Image file types that can be processed by vision models
    private static readonly HashSet<string> SupportedImageMimeTypes = new(StringComparer.OrdinalIgnoreCase)
    {
        "image/png", "image/jpeg", "image/webp"
    };

    // Audio file types
    private static readonly HashSet<string> SupportedAudioMimeTypes = new(StringComparer.OrdinalIgnoreCase)
    {
        "audio/x-aac",
        "audio/flac",
        "audio/mp3",
        "audio/m4a",
        "audio/mpeg",
        "audio/mpga",
        "audio/mp4",
        "audio/opus",
        "audio/pcm",
        "audio/wav",
        "audio/webm"
    };

    public FileContentExtractionPlugin(
        ILogger<FileContentExtractionPlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IHttpClientFactory httpClientFactory,
        IFileContentCacheService fileContentCacheService)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _httpClientFactory = httpClientFactory;
        _fileContentCacheService = fileContentCacheService;
    }

    [KernelFunction("extract_text_from_file")]
    [Description(
        "Extracts text content from a file using LLM processing. Supports text files, images with text, and documents.")]
    [return: Description("The extracted text content from the file")]
    public async Task<string> ExtractTextFromFileAsync(
        Kernel kernel,
        [Description("The file to extract text content from")]
        SfChatEntryFile file,
        string? background,
        bool useCache = true)
    {
        if (file == null)
        {
            _logger.LogWarning("File is null, cannot extract text");
            return "Error: No file provided";
        }

        _logger.LogInformation(
            "Extracting text from file. URL: {Url}, MimeType: {MimeType}, Size: {FileSize}, UseCache: {UseCache}",
            file.Url,
            file.MimeType,
            file.FileSize,
            useCache);

        try
        {
            // Try to get from cache first if enabled
            if (useCache)
            {
                var cachedContent = await _fileContentCacheService.GetCachedFileContentAsync(file.Url, background);
                if (cachedContent != null)
                {
                    _logger.LogInformation("Found cached content for file: {Url}", file.Url);
                    return cachedContent.ExtractedContent;
                }
            }

            // Download file content
            var fileContent = await DownloadFileAsync(file.Url);
            if (fileContent == null || fileContent.Length == 0)
            {
                _logger.LogWarning("Failed to download file or file is empty: {Url}", file.Url);
                return "Error: Could not download file or file is empty";
            }

            // Get ChatCompletionService using Gemini 2.0 Flash
            var promptExecutionSettings =
                _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH_2_5);
            var chatCompletionService =
                kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH_2_5);

            string extractedContent;

            // Process based on file type
            if (SupportedTextMimeTypes.Contains(file.MimeType))
            {
                extractedContent = await ProcessTextFileAsync(
                    kernel,
                    chatCompletionService,
                    fileContent,
                    file.MimeType,
                    file.Url,
                    promptExecutionSettings,
                    background);
            }
            else if (SupportedImageMimeTypes.Contains(file.MimeType))
            {
                extractedContent = await ProcessImageFileAsync(
                    kernel,
                    chatCompletionService,
                    fileContent,
                    file.MimeType,
                    file.Url,
                    promptExecutionSettings,
                    background);
            }
            else if (SupportedAudioMimeTypes.Contains(file.MimeType))
            {
                extractedContent = await ProcessAudioFileAsync(
                    kernel,
                    chatCompletionService,
                    fileContent,
                    file.MimeType,
                    file.Url,
                    promptExecutionSettings,
                    background);
            }
            else
            {
                // For unknown file types, return simple message
                extractedContent = $"Unknown file: {file.Url}";
            }

            // Save to cache if enabled and content was successfully extracted
            if (useCache && !string.IsNullOrEmpty(extractedContent) && !extractedContent.StartsWith("Error:"))
            {
                try
                {
                    await _fileContentCacheService.SaveFileContentToCache(
                        file.Url,
                        file.MimeType,
                        file.FileSize,
                        extractedContent,
                        background);

                    _logger.LogInformation("Saved extracted content to cache for file: {Url}", file.Url);
                }
                catch (Exception cacheEx)
                {
                    _logger.LogWarning(cacheEx, "Failed to save content to cache for file: {Url}", file.Url);
                    // Don't fail the extraction if caching fails
                }
            }

            return extractedContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting text from file: {Url}", file.Url);
            return $"Error: Failed to extract text from file. {ex.Message}";
        }
    }

    [KernelFunction("extract_text_from_files")]
    [Description("Extracts text content from multiple files using LLM processing.")]
    [return: Description("A list of extracted text content from each file")]
    public async Task<List<string>> ExtractTextFromFilesAsync(
        Kernel kernel,
        [Description("The list of files to extract text content from")]
        List<SfChatEntryFile> files,
        string? background,
        bool useCache = true)
    {
        if (files == null || files.Count == 0)
        {
            _logger.LogWarning("No files provided for text extraction");
            return new List<string>
            {
                "Error: No files provided"
            };
        }

        var results = new List<string>();

        foreach (var file in files)
        {
            var extractedText = await ExtractTextFromFileAsync(kernel, file, background, useCache);
            results.Add(extractedText);
        }

        return results;
    }

    private async Task<byte[]?> DownloadFileAsync(string url)
    {
        try
        {
            using var httpClient = _httpClientFactory.CreateClient("default-handler");
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            // Add user agent to avoid blocking
            httpClient.DefaultRequestHeaders.Add(
                "User-Agent",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

            var response = await httpClient.GetAsync(url);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to download file: {StatusCode} - {Url}", response.StatusCode, url);
                return null;
            }

            return await response.Content.ReadAsByteArrayAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error downloading file from URL: {Url}", url);
            return null;
        }
    }

    private async Task<string> ProcessTextFileAsync(
        Kernel kernel,
        IChatCompletionService chatCompletionService,
        byte[] fileContent,
        string mimeType,
        string url,
        PromptExecutionSettings promptExecutionSettings,
        string? background)
    {
        try
        {
            // Convert bytes to text
            var textContent = Encoding.UTF8.GetString(fileContent);

            // If content is too large, summarize it
            if (textContent.Length > 10000)
            {
                return await SummarizeTextContentAsync(
                    kernel,
                    chatCompletionService,
                    textContent,
                    mimeType,
                    promptExecutionSettings,
                    background);
            }

            return textContent;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing text file: {Url}", url);
            return $"Error: Could not process text file. {ex.Message}";
        }
    }

    private async Task<string> ProcessImageFileAsync(
        Kernel kernel,
        IChatCompletionService chatCompletionService,
        byte[] fileContent,
        string mimeType,
        string url,
        PromptExecutionSettings promptExecutionSettings,
        string? background)
    {
        try
        {
            // Create a chat history with system message for comprehensive image analysis
            var chatHistory = new ChatHistory();

            chatHistory.AddSystemMessage(
                """
                You are an expert at comprehensive image analysis and interpretation. Analyze the provided image and provide a detailed response in the following structured format.

                OUTPUT FORMAT:
                Use exactly this structure with markdown headings and bullet points:

                ## Image Context & Type
                - Type: [specify image type: screenshot, photograph, document scan, selfie, portrait, chart/graph, diagram, artwork, etc.]
                - Context: [describe the general context and setting]

                ## Content Summary & Interpretation
                - Summary: [comprehensive summary of what the image shows]
                - Subject Matter: [interpret the main subject matter and purpose]
                - Key Elements: [explain significant details, objects, people, or scenes]

                ## Text Readability Assessment
                - Status: [state: READABLE / PARTIALLY_READABLE / UNREADABLE / NO_TEXT]
                - Notes: [if unreadable, briefly explain why (blurry, small, poor contrast, unclear handwriting, etc.); if no text, state "No text content found"; if readable, state "Text is clear and readable"]

                ## Additional Insights
                - Interpretation: [provide relevant interpretations or insights]
                - Technical Details: [mention technical aspects if relevant: UI elements, chart data, diagram components, etc.]

                Focus on understanding and interpreting the image rather than extracting all text. Prioritize meaningful analysis and insights over exhaustive text extraction. Only mention key text elements that are essential for understanding the image's purpose and content.
                """);

            AddBackgroundContextIfProvided(chatHistory, background, "image");

            // Add user message with both text and image content
            chatHistory.AddUserMessage(
            [
                new TextContent("Please provide a comprehensive analysis of this image following the structured format:"),
                new ImageContent(fileContent, mimeType)
            ]);

            // Invoke the chat completion model
            var reply = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                promptExecutionSettings,
                kernel);

            return reply.Content ?? "No content could be analyzed from the image";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing image file: {Url}", url);
            return $"Error: Could not process image file. {ex.Message}";
        }
    }

    private async Task<string> ProcessAudioFileAsync(
        Kernel kernel,
        IChatCompletionService chatCompletionService,
        byte[] fileContent,
        string mimeType,
        string url,
        PromptExecutionSettings promptExecutionSettings,
        string? background)
    {
        try
        {
            // Create a chat history with system message for image analysis
            var chatHistory = new ChatHistory();

            chatHistory.AddSystemMessage(
                """
                You are an expert at analyzing and transcribing audio files. Analyze the provided audio file and extract any textual content through transcription.

                OUTPUT FORMAT:
                Use exactly this structure with markdown headings and bullet points:

                ## Audio Analysis
                - Format: [identify audio format and quality]
                - Duration: [estimate duration if detectable]
                - Quality: [assess audio quality: CLEAR / MODERATE / POOR / UNCLEAR]

                ## Language Detection
                - Primary Language: [identify the main language spoken]
                - Additional Languages: [list any other languages detected, or state "None"]

                ## Transcription
                - Status: [state: TRANSCRIBED / PARTIALLY_TRANSCRIBED / UNTRANSCRIBABLE / NO_SPEECH]
                - Content: [provide the transcription preserving speaker changes and structure; if untranscribable, explain why (poor quality, background noise, unclear speech, etc.); if no speech, describe what audio contains]

                ## Additional Insights
                - Content Type: [describe type of audio: conversation, presentation, music, ambient sound, etc.]
                - Key Information: [highlight important information or context from the audio]
                """);

            AddBackgroundContextIfProvided(chatHistory, background, "audio");

            // Add user message with both text and audio content
            chatHistory.AddUserMessage(
            [
                new TextContent("Please first identify the languages of the audio and then extract all audio content from this audio:"),
                new AudioContent(fileContent, mimeType)
            ]);


            var reply = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                promptExecutionSettings,
                kernel);

            return reply.Content ??
                   $"Could not transcribe {mimeType} audio file - specialized audio processing tools may be required";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing audio file: {Url}", url);
            return $"Error: Could not process audio file. {ex.Message}";
        }
    }

    private async Task<string> SummarizeTextContentAsync(
        Kernel kernel,
        IChatCompletionService chatCompletionService,
        string textContent,
        string mimeType,
        PromptExecutionSettings promptExecutionSettings,
        string? background)
    {
        try
        {
            var chatHistory = new ChatHistory(
                """
                You are an expert at analyzing and summarizing document content. Analyze and summarize the provided text content with structured output.

                OUTPUT FORMAT:
                Use exactly this structure with markdown headings and bullet points:

                ## Document Analysis
                - Type: [identify document type: report, article, code, email, manual, etc.]
                - Length: [assess content length: SHORT / MEDIUM / LONG / VERY_LONG]
                - Complexity: [assess complexity: SIMPLE / MODERATE / COMPLEX / TECHNICAL]

                ## Content Summary
                - Main Topic: [identify the primary subject matter]
                - Key Themes: [list 3-5 main themes or topics covered]
                - Purpose: [describe the document's purpose or intent]

                ## Important Information
                - Key Points: [list the most important findings, conclusions, or information]
                - Data Points: [highlight significant numbers, statistics, or metrics]
                - Action Items: [identify any tasks, recommendations, or next steps mentioned]

                ## Structure & Organization
                - Sections: [describe main sections or organization]
                - Format: [note any special formatting, tables, lists, etc.]
                - Quality: [assess content quality and readability]

                Keep the summary comprehensive but concise, focusing on information that would be useful for understanding the content.
                """);

            AddBackgroundContextIfProvided(chatHistory, background, "text");

            chatHistory.AddUserMessage($"File Type: {mimeType}\n\nContent to summarize:\n{textContent}");

            var reply = await chatCompletionService.GetChatMessageContentAsync(
                chatHistory,
                promptExecutionSettings,
                kernel);

            return reply.Content ?? "Could not summarize file content";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error summarizing text content");
            return $"Error: Could not summarize content. {ex.Message}";
        }
    }

    /// <summary>
    /// Adds background context information to chat history if provided, with instructions optimized for content extraction
    /// </summary>
    /// <param name="chatHistory">The chat history to add background context to</param>
    /// <param name="background">The background context information</param>
    /// <param name="contentType">The type of content being processed (image, audio, text)</param>
    private static void AddBackgroundContextIfProvided(ChatHistory chatHistory, string? background, string contentType)
    {
        if (background is not null)
        {
            chatHistory.AddUserMessage(
                $"""
                BACKGROUND CONTEXT FOR {contentType.ToUpper()} ANALYSIS:
                The following background information provides context about this {contentType} file and how it should be interpreted:

                ==== BACKGROUND CONTEXT ====
                {background}
                ==== END BACKGROUND CONTEXT ====

                Please use this context to:
                - Better understand the purpose and significance of the {contentType} content
                - Provide more relevant interpretation and analysis
                - Focus on aspects that align with the stated context
                - Identify content that may be particularly important given this background
                """);
        }
    }
}