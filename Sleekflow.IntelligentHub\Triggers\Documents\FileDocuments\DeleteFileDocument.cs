using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class DeleteFileDocument
    : ITrigger<DeleteFileDocument.DeleteFileDocumentInput,
        DeleteFileDocument.DeleteFileDocumentOutput>
{
    private readonly IFileDocumentService _fileDocumentService;

    public DeleteFileDocument(IFileDocumentService fileDocumentService)
    {
        _fileDocumentService = fileDocumentService;
    }

    public class DeleteFileDocumentInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [Required]
        [JsonProperty(FileDocument.PropertyNameBlobId)]
        public string BlobId { get; set; }

        [JsonConstructor]
        public DeleteFileDocumentInput(string sleekflowCompanyId, string documentId, string blobId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            BlobId = blobId;
        }
    }

    public class DeleteFileDocumentOutput
    {
        [JsonConstructor]
        public DeleteFileDocumentOutput()
        {
        }
    }

    public async Task<DeleteFileDocumentOutput> F(
        DeleteFileDocumentInput deleteFileDocumentInput)
    {
        await _fileDocumentService.DeleteFileDocumentAsync(
            deleteFileDocumentInput.DocumentId,
            deleteFileDocumentInput.SleekflowCompanyId,
            deleteFileDocumentInput.BlobId);
        return new DeleteFileDocumentOutput();
    }
}