using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.IntelligentHub.KnowledgeBaseEntries;

public interface IKnowledgeBaseEntryRepository : IDynamicFiltersRepository<KnowledgeBaseEntry>
{
    Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesByDocumentIdAsync(
        string sleekflowCompanyId,
        string sourceId,
        string resourceType,
        int? limit);

    Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesByDocumentIdAsync(
            string sleekflowCompanyId,
            string sourceId,
            string resourceType,
            string? continuationToken,
            int limit);

    Task<List<string>> GetKnowledgeBaseEntryIdByChunkIdsAsync(string sleekflowCompanyId, List<string> documentChunkId);

    Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)> GetKnowledgeBaseEntriesAsync(
        string sleekflowCompanyId,
        string documentChunkId,
        string? continuationToken,
        int limit);

    Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesByIdsAsync(
        string sleekflowCompanyId,
        List<string> knowledgeBaseEntryIds);

    Task<KnowledgeBaseEntry> PatchAndGetKnowledgeBaseEntryAsync(
        KnowledgeBaseEntry knowledgeBaseEntry,
        string content,
        string contentEn,
        List<Category> categories);
}

public class KnowledgeBaseEntryRepository
    : DynamicFiltersBaseRepository<KnowledgeBaseEntry>, IKnowledgeBaseEntryRepository, IScopedService
{
    public KnowledgeBaseEntryRepository(
        ILogger<KnowledgeBaseEntryRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }

    public async Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesByDocumentIdAsync(
        string sleekflowCompanyId,
        string sourceId,
        string resourceType,
        int? limit)
    {
        return await GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.KnowledgeBaseEntrySource.SourceId == sourceId
                && e.KnowledgeBaseEntrySource.SourceType == resourceType,
            limit);
    }

    public async Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesByDocumentIdAsync(
            string sleekflowCompanyId,
            string sourceId,
            string resourceType,
            string? continuationToken,
            int limit)
    {
        return await GetContinuationTokenizedObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.KnowledgeBaseEntrySource.SourceId == sourceId
                && e.KnowledgeBaseEntrySource.SourceType == resourceType,
            continuationToken,
            limit);
    }

    public async Task<List<string>> GetKnowledgeBaseEntryIdByChunkIdsAsync(
        string sleekflowCompanyId,
        List<string> documentChunkId)
    {
        var entryIds = new List<string>();

        foreach (var id in documentChunkId)
        {
            var knowledgeBaseEntries = await GetObjectsAsync(
                e =>
                    e.SleekflowCompanyId == sleekflowCompanyId
                    && e.ChunkId == id);
            if (knowledgeBaseEntries.Count > 0)
            {
                entryIds.Add(knowledgeBaseEntries[0].Id);
            }
        }

        return entryIds;
    }

    public async Task<(List<KnowledgeBaseEntry> KnowledgeBaseEntries, string? NextContinuationToken)>
        GetKnowledgeBaseEntriesAsync(
            string sleekflowCompanyId,
            string documentChunkId,
            string? continuationToken,
            int limit)
    {
        return await GetContinuationTokenizedObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.KnowledgeBaseEntrySource.SourceId == documentChunkId,
            continuationToken,
            limit);
    }

    public async Task<List<KnowledgeBaseEntry>> GetKnowledgeBaseEntriesByIdsAsync(
        string sleekflowCompanyId,
        List<string> knowledgeBaseEntryIds)
    {
        return await GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && knowledgeBaseEntryIds.Contains(e.Id));
    }

    public async Task<KnowledgeBaseEntry> PatchAndGetKnowledgeBaseEntryAsync(
        KnowledgeBaseEntry knowledgeBaseEntry,
        string content,
        string contentEn,
        List<Category> categories)
    {
        return await PatchAndGetAsync(
            knowledgeBaseEntry.Id,
            knowledgeBaseEntry.SleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace(KnowledgeBaseEntry.PropertyNameContent, content),
                Replace(KnowledgeBaseEntry.PropertyNameContentEn, contentEn),
                Replace(KnowledgeBaseEntry.PropertyNameCategories, categories),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
            });
    }
}