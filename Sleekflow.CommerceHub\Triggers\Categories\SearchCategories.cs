using System.ComponentModel.DataAnnotations;
using Azure.Search.Documents.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Searches;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Categories;

[TriggerGroup(ControllerNames.Categories)]
public class SearchCategories
    : ITrigger<
        SearchCategories.SearchCategoriesInput,
        SearchCategories.SearchCategoriesOutput>
{
    private readonly ICategorySearchService _categorySearchService;
    private readonly ICategoryService _categoryService;

    public SearchCategories(
        ICategorySearchService categorySearchService,
        ICategoryService categoryService)
    {
        _categorySearchService = categorySearchService;
        _categoryService = categoryService;
    }

    public class SearchCategoriesInput
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [ValidateArray]
        [MaxLength(16)]
        [JsonProperty("filter_groups")]
        public List<SearchFilterGroup> FilterGroups { get; set; }

        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("continuation_token")]
        public string? ContinuationToken
        {
            get => ContinuationTokenDto == null ? null : JsonConvert.SerializeObject(ContinuationTokenDto);
            set => ContinuationTokenDto =
                value == null
                    ? null
                    : JsonConvert.DeserializeObject<ICategorySearchService.ContinuationTokenDto>(value);
        }

        [JsonIgnore]
#pragma warning disable JA1001
        public ICategorySearchService.ContinuationTokenDto? ContinuationTokenDto { get; set; }
#pragma warning restore JA1001

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [StringLength(1024, MinimumLength = 1)]
        [JsonProperty("search_text")]
        public string? SearchText { get; set; }

        [JsonConstructor]
        public SearchCategoriesInput(
            string sleekflowCompanyId,
            string storeId,
            List<SearchFilterGroup> filterGroups,
            string? continuationToken,
            int limit,
            string? searchText)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            FilterGroups = filterGroups;
            ContinuationToken = continuationToken;
            Limit = limit;
            SearchText = searchText;
        }
    }

    public class SearchCategoriesOutput
    {
        [JsonProperty("categories")]
        public List<CategoryDto> Categories { get; set; }

        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken
        {
            get => NextContinuationTokenDto == null ? null : JsonConvert.SerializeObject(NextContinuationTokenDto);
            set => NextContinuationTokenDto =
                value == null
                    ? null
                    : JsonConvert.DeserializeObject<ICategorySearchService.ContinuationTokenDto>(value);
        }

        [JsonIgnore]
        [JsonProperty("next_continuation_token_dto")]
        public ICategorySearchService.ContinuationTokenDto? NextContinuationTokenDto { get; set; }

        [JsonProperty("facets")]
        public IDictionary<string, IList<FacetResult>> Facets { get; set; }

        [JsonConstructor]
        public SearchCategoriesOutput(
            List<CategoryDto> categories,
            long totalCount,
            string? nextContinuationToken,
            IDictionary<string, IList<FacetResult>> facets)
        {
            Categories = categories;
            TotalCount = totalCount;
            NextContinuationToken = nextContinuationToken;
            Facets = facets;
        }

        public SearchCategoriesOutput(
            List<CategoryDto> categories,
            long totalCount,
            ICategorySearchService.ContinuationTokenDto? nextContinuationTokenDto,
            IDictionary<string, IList<FacetResult>> facets)
        {
            Categories = categories;
            TotalCount = totalCount;
            NextContinuationTokenDto = nextContinuationTokenDto;
            Facets = facets;
        }
    }

    public async Task<SearchCategoriesOutput> F(SearchCategoriesInput searchCategoriesInput)
    {
        var (categoryIndexDtos, facets, totalCount, nextContinuationToken) =
            await _categorySearchService.SearchCategoriesAsync(
                searchCategoriesInput.SleekflowCompanyId,
                searchCategoriesInput.StoreId,
                searchCategoriesInput.FilterGroups,
                searchCategoriesInput.ContinuationTokenDto,
                searchCategoriesInput.Limit,
                searchCategoriesInput.SearchText);

        var categories = await _categoryService.GetCategoriesAsync(
            searchCategoriesInput.SleekflowCompanyId,
            searchCategoriesInput.StoreId,
            searchCategoriesInput.Limit,
            categoryIndexDtos.Select(c => c.Id).ToList());

        return new SearchCategoriesOutput(
            categories.Select(c => new CategoryDto(c)).ToList(),
            totalCount,
            nextContinuationToken,
            facets);
    }
}