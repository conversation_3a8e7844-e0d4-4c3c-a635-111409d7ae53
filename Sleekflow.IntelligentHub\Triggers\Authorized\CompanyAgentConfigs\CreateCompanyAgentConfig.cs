using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyAgentConfigs;

[TriggerGroup(
    ControllerNames.CompanyAgentConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class CreateCompanyAgentConfig
    : ITrigger<CreateCompanyAgentConfig.CreateCompanyAgentConfigInput,
        CreateCompanyAgentConfig.CreateCompanyAgentConfigOutput>
{
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public CreateCompanyAgentConfig(
        ICompanyAgentConfigService companyAgentConfigService,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _companyAgentConfigService = companyAgentConfigService;
        _authorizationContext = authorizationContext;
    }

    public class CreateCompanyAgentConfigInput
    {
        [Required]
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty(CompanyAgentConfig.PropertyNameDescription)]
        public string? Description { get; set; }

        [Required]
        [JsonProperty("type")]
        [RegularExpression($"{CompanyAgentTypes.Sales}|{CompanyAgentTypes.Support}|{CompanyAgentTypes.Custom}")]
        public string Type { get; set; }

        [Required]
        [JsonProperty("is_chat_history_enabled_as_context")]
        public bool IsChatHistoryEnabledAsContext { get; set; }

        [Required]
        [JsonProperty("is_contact_properties_enabled_as_context")]
        public bool IsContactPropertiesEnabledAsContext { get; set; }

        [Required]
        [Range(0, 100)]
        [JsonProperty("number_of_previous_messages_in_chat_history_available_as_context")]
        public int NumberOfPreviousMessagesInChatHistoryAvailableAsContext { get; set; }

        [AllowedStringValues(false, ChannelTypes.WhatsAppCloudApi)]
        [JsonProperty("channel_type")]
        public string? ChannelType { get; set; }

        [JsonProperty("channel_id")]
        public string? ChannelId { get; set; }

        [JsonProperty("collaboration_mode")]
        public string? CollaborationMode { get; set; }

        [JsonProperty("demo")]
        public bool? Demo { get; set; }

        [ValidateObject]
        [JsonProperty("lead_nurturing_tools")]
        public LeadNurturingTools? LeadNurturingTools { get; set; }

        [ValidateObject]
        [JsonProperty("tools_config")]
        public ToolsConfig? ToolsConfig { get; set; }

        [ValidateArray]
        [JsonProperty("enricher_configs")]
        public List<EnricherConfig>? EnricherConfigs { get; set; }

        [ValidateObject]
        [JsonProperty("knowledge_retrieval_config")]
        public KnowledgeRetrievalConfig? KnowledgeRetrievalConfig { get; set; }

        [ValidateObject]
        [JsonProperty(CompanyAgentConfig.PropertyNameActions)]
        public CompanyAgentConfigActionsDto? Actions { get; set; }

        [ValidateObject]
        [JsonProperty(CompanyAgentConfig.PropertyNamePromptInstruction)]
        public PromptInstructionDto? PromptInstruction { get; set; }

        [JsonConstructor]
        public CreateCompanyAgentConfigInput(
            string name,
            string type,
            bool isChatHistoryEnabledAsContext,
            bool isContactPropertiesEnabledAsContext,
            int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
            string? channelType,
            string? channelId,
            string? collaborationMode,
            LeadNurturingTools? leadNurturingTools,
            ToolsConfig? toolsConfig = null,
            List<EnricherConfig>? enricherConfigs = null,
            KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
            string? description = null,
            CompanyAgentConfigActionsDto? actions = null,
            PromptInstructionDto? promptInstruction = null,
            bool? demo = null)
        {
            Name = name;
            Type = type;
            IsChatHistoryEnabledAsContext = isChatHistoryEnabledAsContext;
            IsContactPropertiesEnabledAsContext = isContactPropertiesEnabledAsContext;
            NumberOfPreviousMessagesInChatHistoryAvailableAsContext =
                numberOfPreviousMessagesInChatHistoryAvailableAsContext;
            ChannelType = channelType;
            ChannelId = channelId;
            CollaborationMode = collaborationMode;
            LeadNurturingTools = leadNurturingTools;
            ToolsConfig = toolsConfig;
            EnricherConfigs = enricherConfigs;
            KnowledgeRetrievalConfig = knowledgeRetrievalConfig;
            Description = description;
            Actions = actions;
            PromptInstruction = promptInstruction;
            Demo = demo;
        }
    }

    public class CreateCompanyAgentConfigOutput
    {
        [JsonProperty("company_agent_config")]
        public CompanyAgentConfigDto CompanyAgentConfig { get; set; }

        [JsonConstructor]
        public CreateCompanyAgentConfigOutput(CompanyAgentConfigDto companyAgentConfig)
        {
            CompanyAgentConfig = companyAgentConfig;
        }
    }

    public async Task<CreateCompanyAgentConfigOutput> F(CreateCompanyAgentConfigInput input)
    {
        if (input.Demo != true &&
            (await _companyAgentConfigService.GetObjectsAsync(_authorizationContext.SleekflowCompanyId!)).Count > 10)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ValidationResult("Only ten company agent configs are allowed.")
                });
        }

        var promptInstructionDomain = GetPromptInstructionDomain(input.PromptInstruction);
        var actionsDomain = input.Actions != null ? new CompanyAgentConfigActions(input.Actions) : null;

        var config = await _companyAgentConfigService.CreateAndGetAsync(
            input.Name,
            input.Type,
            _authorizationContext.SleekflowCompanyId!,
            input.IsChatHistoryEnabledAsContext,
            input.IsContactPropertiesEnabledAsContext,
            input.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            input.ChannelType,
            input.ChannelId,
            input.CollaborationMode,
            input.LeadNurturingTools,
            input.ToolsConfig,
            input.EnricherConfigs,
            input.KnowledgeRetrievalConfig,
            _authorizationContext.SleekflowStaffId!,
            _authorizationContext.SleekflowTeamIds,
            input.Description,
            actionsDomain,
            promptInstructionDomain);

        return new CreateCompanyAgentConfigOutput(new CompanyAgentConfigDto(config));
    }

    private static PromptInstruction? GetPromptInstructionDomain(PromptInstructionDto? promptInstructionDto)
    {
        if (promptInstructionDto is null)
        {
            return null;
        }

        return new PromptInstruction(
            promptInstructionDto.Objective,
            promptInstructionDto.Tone,
            promptInstructionDto.DiscloseLevel,
            promptInstructionDto.ResponseLevel,
            promptInstructionDto.RestrictivenessLevel,
            promptInstructionDto.GreetingMessage,
            promptInstructionDto.AdditionalInstructionCore,
            promptInstructionDto.AdditionalInstructionStrategy,
            promptInstructionDto.AdditionalInstructionResponse,
            promptInstructionDto.AdditionalInstructionKnowledgeRetrieval,
            promptInstructionDto.Guardrails);
    }
}