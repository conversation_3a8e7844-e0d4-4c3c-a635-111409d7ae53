﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Events;
using Sleekflow.IntelligentHub.Playgrounds;
using Sleekflow.Models.Chats;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.Playgrounds;

[TriggerGroup(
    ControllerNames.Playgrounds,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class EnqueueGeneratePlaygroundRecommendedReplyEvent
    : ITrigger<EnqueueGeneratePlaygroundRecommendedReplyEvent.EnqueueGeneratePlaygroundRecommendedReplyEventInput,
        EnqueueGeneratePlaygroundRecommendedReplyEvent.EnqueueGeneratePlaygroundRecommendedReplyEventOutput>
{
    private readonly IIdService _idService;
    private readonly IPlaygroundService _playgroundService;
    private readonly IServiceBusManager _serviceBusManager;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public EnqueueGeneratePlaygroundRecommendedReplyEvent(
        IIdService idService,
        IPlaygroundService playgroundService,
        IServiceBusManager serviceBusManager,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _idService = idService;
        _playgroundService = playgroundService;
        _serviceBusManager = serviceBusManager;
        _authorizationContext = authorizationContext;
    }

    public class EnqueueGeneratePlaygroundRecommendedReplyEventInput
    {
        [Required]
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [Required]
        [Validations.ValidateArray]
        [JsonProperty("conversation_context")]
        public List<SfChatEntry> ConversationContext { get; set; }

        [JsonConstructor]
        public EnqueueGeneratePlaygroundRecommendedReplyEventInput(
            string sessionId,
            List<SfChatEntry> conversationContext)
        {
            SessionId = sessionId;
            ConversationContext = conversationContext;
        }
    }

    public class EnqueueGeneratePlaygroundRecommendedReplyEventOutput
    {
        [JsonProperty("message_id")]
        public string MessageId { get; set; }

        [JsonConstructor]
        public EnqueueGeneratePlaygroundRecommendedReplyEventOutput(string messageId)
        {
            MessageId = messageId;
        }
    }

    public async Task<EnqueueGeneratePlaygroundRecommendedReplyEventOutput> F(
        EnqueueGeneratePlaygroundRecommendedReplyEventInput eventInput)
    {
        var playground = await _playgroundService.GetOrDefaultAsync(
            eventInput.SessionId,
            _authorizationContext.SleekflowCompanyId!,
            _authorizationContext.SleekflowUserId!);

        if (playground is null)
        {
            throw new SfNotFoundObjectException("Unable to find the playground session.");
        }

        var messageId = _idService.GetId(SysTypeNames.PlaygroundRecommendedReply);

        var @event = new GeneratePlaygroundRecommendedReplyEvent(
            playground.Id,
            messageId,
            _authorizationContext.SleekflowCompanyId!,
            _authorizationContext.SleekflowUserId!,
            eventInput.ConversationContext);

        await _serviceBusManager.PublishAsync(@event);
        return new EnqueueGeneratePlaygroundRecommendedReplyEventOutput(messageId);
    }
}