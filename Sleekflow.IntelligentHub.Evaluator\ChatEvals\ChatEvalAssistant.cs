using Microsoft.Extensions.AI;
using Microsoft.Extensions.AI.Evaluation;
using Microsoft.Extensions.AI.Evaluation.Reporting;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals.Methods;
using Sleekflow.IntelligentHub.Evaluator.Evaluators;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals;

public class ChatEvalAssistant
{
    private readonly IMethod<ChatEvalConfig, ChatEvalOutput> _method;

    public ChatEvalAssistant(IMethod<ChatEvalConfig, ChatEvalOutput> method)
    {
        _method = method;
    }

    public async Task<(string Answer, EvaluationResult EvaluationResult)> Evaluate(
        ChatEvalQuestion chatEvalQuestion,
        ReportingConfiguration reportingConfiguration,
        string answer,
        string ragSourceStr,
        CancellationToken cancellationToken)
    {
        await using ScenarioRun scenario = await reportingConfiguration.CreateScenarioRunAsync(
            $"Question_{chatEvalQuestion.GenerateId()}_{_method.MethodName}",
            cancellationToken: cancellationToken);

        var sourceFileTexts = (chatEvalQuestion.SourceFilenames ?? new List<string>()).Select(
            f => File.ReadAllText(Path.Combine(chatEvalQuestion.ChatEvalConfig.SourceDir, f))).ToArray();

        // evaluate the AI response
        var evaluationResult = await scenario.EvaluateAsync(
            chatEvalQuestion.QuestionContexts?.Select(ConvertToChatMessage) ?? [],
            new ChatResponse(new ChatMessage(ChatRole.Assistant, answer)),
            additionalContext:
            [
                new AnswerScoringEvaluationContext(chatEvalQuestion.Scenario, chatEvalQuestion.ModelAnswer),
                new RagOutputScoringEvaluationContext(string.Join("\n", sourceFileTexts), ragSourceStr),
            ],
            cancellationToken);

        // Assert that the evaluator was able to successfully generate an analysis
        Assert.That(
            evaluationResult.Metrics.Values.Any(m => m.Interpretation?.Rating == EvaluationRating.Inconclusive),
            Is.False,
            "Model response was inconclusive");

        // Assert that the evaluators did not report any diagnostic errors
        if (evaluationResult.ContainsDiagnostics(d => d.Severity == EvaluationDiagnosticSeverity.Error))
        {
            Console.WriteLine(
                JsonConvert.SerializeObject(
                    evaluationResult.Metrics.Where(
                        m => m.Value.ContainsDiagnostics(d => d.Severity == EvaluationDiagnosticSeverity.Error))));
        }

        Assert.That(
            evaluationResult.ContainsDiagnostics(d => d.Severity == EvaluationDiagnosticSeverity.Error),
            Is.False,
            "Evaluation had errors.");

        return (answer, evaluationResult);
    }

    private static ChatMessage ConvertToChatMessage(ChatMessageContent chatMessageContent)
    {
        if (chatMessageContent.Role == AuthorRole.System)
        {
            return new ChatMessage(ChatRole.System, chatMessageContent.Content);
        }

        if (chatMessageContent.Role == AuthorRole.Assistant)
        {
            return new ChatMessage(ChatRole.Assistant, chatMessageContent.Content);
        }

        if (chatMessageContent.Role == AuthorRole.User)
        {
            return new ChatMessage(ChatRole.User, chatMessageContent.Content);
        }

        if (chatMessageContent.Role == AuthorRole.Tool)
        {
            return new ChatMessage(ChatRole.Tool, chatMessageContent.Content);
        }

        throw new Exception($"Unexpected role: {chatMessageContent.Role}");
    }
}