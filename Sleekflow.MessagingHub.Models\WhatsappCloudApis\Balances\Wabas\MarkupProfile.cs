using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;

public class MarkupProfile
{
    private const decimal DefaultConversationFeePriceMarkupPercentage = 0.12m;
    public const decimal DefaultTransactionHandlingFeeRate = 0.03m;

    [JsonProperty("per_paid_business_initiated_conversation_fee_markup")]
    public Money PerPaidBusinessInitiatedConversationFeeMarkup { get; set; }

    [JsonProperty("per_paid_user_initiated_conversation_fee_markup")]
    public Money PerPaidUserInitiatedConversationFeeMarkup { get; set; }

    [JsonProperty("business_initiated_conversation_fee_price_markup_percentage")]
    public decimal BusinessInitiatedConversationFeePriceMarkupPercentage { get; set; }

    [JsonProperty("user_initiated_conversation_fee_price_markup_percentage")]
    public decimal UserInitiatedConversationFeePriceMarkupPercentage { get; set; }

    [JsonProperty("transaction_handling_fee_rate")]
    public decimal? TransactionHandlingFeeRate { get; set; }

    [JsonConstructor]
    public MarkupProfile(
        Money perPaidBusinessInitiatedConversationFeeMarkup,
        Money perPaidUserInitiatedConversationFeeMarkup,
        decimal businessInitiatedConversationFeePriceMarkupPercentage = DefaultConversationFeePriceMarkupPercentage,
        decimal userInitiatedConversationFeePriceMarkupPercentage = DefaultConversationFeePriceMarkupPercentage,
        decimal transactionHandlingFeeRate = DefaultTransactionHandlingFeeRate)
    {
        PerPaidBusinessInitiatedConversationFeeMarkup = perPaidBusinessInitiatedConversationFeeMarkup;
        PerPaidUserInitiatedConversationFeeMarkup = perPaidUserInitiatedConversationFeeMarkup;
        BusinessInitiatedConversationFeePriceMarkupPercentage = businessInitiatedConversationFeePriceMarkupPercentage;
        UserInitiatedConversationFeePriceMarkupPercentage = userInitiatedConversationFeePriceMarkupPercentage;
        TransactionHandlingFeeRate = transactionHandlingFeeRate;
    }
}