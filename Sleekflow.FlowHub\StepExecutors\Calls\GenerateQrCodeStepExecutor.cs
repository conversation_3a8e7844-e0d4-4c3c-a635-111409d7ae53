using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Links;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IGenerateQrCodeStepExecutor : IStepExecutor
{
}

public class GenerateQrCodeStepExecutor
    : GeneralStepExecutor<CallStep<GenerateQrCodeStepArgs>>,
        IGenerateQrCodeStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IRequestClient<GetUrlQrCodeRequest> _getUrlQrCodeRequestClient;
    private readonly IStateAggregator _stateAggregator;

    public GenerateQrCodeStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IRequestClient<GetUrlQrCodeRequest> getUrlQrCodeRequestClient,
        IStateAggregator stateAggregator)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _getUrlQrCodeRequestClient = getUrlQrCodeRequestClient;
        _stateAggregator = stateAggregator;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var response = await _getUrlQrCodeRequestClient.GetResponse<GetUrlQrCodeReply>(
                await GetArgs(callStep, state));

            await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                response.Message.Url);

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<GetUrlQrCodeRequest> GetArgs(
        CallStep<GenerateQrCodeStepArgs> callStep,
        ProxyState state)
    {
        var url =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.UrlExpr)
                      ?? callStep.Args.UrlExpr);

        return new GetUrlQrCodeRequest(
            url,
            new QrCodeConfig(256));
    }
}