﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class InitProviderOutput
{
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("context")]
    public dynamic Context { get; set; }

    [JsonConstructor]
    public InitProviderOutput(string providerName, object context)
    {
        ProviderName = providerName;
        Context = context;
    }
}