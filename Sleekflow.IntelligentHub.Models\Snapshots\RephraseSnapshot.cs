﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class RephraseSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("input_message")]
    public string InputMessage { get; set; }

    [JsonProperty("rephrase_target_type")]
    public string RephraseTargetType { get; set; }

    [JsonProperty( "output_message")]
    public string OutputMessage { get; set; }

    [JsonConstructor]
    public RephraseSnapshot(string inputMessage, string rephraseTargetType, string outputMessage)
    {
        InputMessage = inputMessage;
        RephraseTargetType = rephraseTargetType;
        OutputMessage = outputMessage;
    }
}