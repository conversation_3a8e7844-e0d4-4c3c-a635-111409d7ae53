﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.States;
using Sleekflow.Locks;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWorkflowBulkReenrollmentInitiatedEventConsumerDefinition
    : ConsumerDefinition<OnWorkflowBulkReenrollmentInitiatedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWorkflowBulkReenrollmentInitiatedEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(15);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWorkflowBulkReenrollmentInitiatedEventConsumer : IConsumer<OnWorkflowBulkReenrollmentInitiatedEvent>
{
    private readonly IBus _bus;
    private readonly IStateService _stateService;
    private readonly ILockService _lockService;
    private readonly ILogger<OnWorkflowBulkReenrollmentInitiatedEventConsumer> _logger;

    public OnWorkflowBulkReenrollmentInitiatedEventConsumer(
        IBus bus,
        IStateService stateService,
        ILockService lockService,
        ILogger<OnWorkflowBulkReenrollmentInitiatedEventConsumer> logger)
    {
        _bus = bus;
        _stateService = stateService;
        _lockService = lockService;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnWorkflowBulkReenrollmentInitiatedEvent> context)
    {
        Lock? @lock = null;

        var @event = context.Message;

        var sleekflowCompanyId = @event.SleekflowCompanyId;
        var workflowVersionedId = @event.WorkflowVersionedId;
        var periodFrom = @event.PeriodFrom;
        var periodTo = @event.PeriodTo;

        try
        {
            string? continuationToken = null;
            var restrictedStates = new List<ProxyState>();

            @lock = await _lockService.LockAsync(
                new[]
                {
                    "reenrollment",
                    sleekflowCompanyId,
                    workflowVersionedId,
                },
                TimeSpan.FromMinutes(15));

            // Duplicate action while the current one is still running
            if (@lock is null)
            {
                return;
            }

            do
            {
                var (proxyStates, nextContinuationToken) =
                    await _stateService.GetRestrictedStatesWithContinuationTokenAsync(
                        sleekflowCompanyId,
                        workflowVersionedId,
                        periodFrom,
                        periodTo,
                        100,
                        continuationToken);

                restrictedStates.AddRange(proxyStates);

                continuationToken = nextContinuationToken;
            }
            while (!string.IsNullOrEmpty(continuationToken));

            var eventChunks = restrictedStates
                .GroupBy(x => x.Identity.ObjectId)
                .ToDictionary(
                    g => g.Key,
                    g => g.OrderByDescending(x => x.UpdatedAt))
                .Select(
                    kv =>
                        new OnWorkflowReenrollmentEvent(
                            sleekflowCompanyId,
                            kv.Value.First().Id,
                            kv.Value
                                .Skip(1)
                                .Select(x => x.Id)
                                .ToList()))
                .Chunk(50);

            foreach (var eventChunk in eventChunks)
            {
                await _bus.PublishBatch(eventChunk);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[{ConsumerName}] Error processing event for company {SleekflowCompanyId} and workflow {WorkflowVersionedId}. Period from: {PeriodFrom}, period to: {PeriodTo}",
                nameof(OnWorkflowBulkReenrollmentInitiatedEventConsumer),
                sleekflowCompanyId,
                workflowVersionedId,
                periodFrom,
                periodTo);
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }
    }
}