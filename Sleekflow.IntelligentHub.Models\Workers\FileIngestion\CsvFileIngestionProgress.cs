using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

public class CsvFileIngestionProgress : IFileIngestionProgress
{
    public const int BatchSize = 10; // Number of chunks to process per batch
    public const int ChunkSize = 40; // Number of rows per chunk

    [JsonProperty("processed_chunks")]
    public int ProcessedChunks { get; set; }

    [JsonProperty("total_chunks")]
    public int TotalChunks { get; set; }

    [JsonConstructor]
    public CsvFileIngestionProgress(int processedChunks, int totalChunks)
    {
        ProcessedChunks = processedChunks;
        TotalChunks = totalChunks;
    }

    public bool IsCompleted()
    {
        return ProcessedChunks >= TotalChunks;
    }

    public double GetProgressPercentage()
    {
        return TotalChunks == 0 ? 100.0 : (ProcessedChunks * 100.0 / TotalChunks);
    }
}