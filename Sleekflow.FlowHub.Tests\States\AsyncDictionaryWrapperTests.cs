using NSubstitute;
using NSubstitute.ExceptionExtensions;
using Sleekflow.FlowHub.Models.States.Abstractions;

namespace Sleekflow.FlowHub.Tests.States.Unit;

[TestFixture]
public class AsyncDictionaryWrapperTests
{
    private IAsyncDictionary<string, string> _mockInnerDictionary;
    private AsyncDictionaryWrapper<string, string> _wrapper;

    [SetUp]
    public void SetUp()
    {
        _mockInnerDictionary = Substitute.For<IAsyncDictionary<string, string>>();
        _wrapper = new AsyncDictionaryWrapper<string, string>(_mockInnerDictionary);
    }

    [Test]
    public void Constructor_ValidInnerDictionary_SetsInnerDictionary()
    {
        // Act
        var innerDictionary = _wrapper.GetInnerDictionary();

        // Assert
        Assert.That(innerDictionary, Is.EqualTo(_mockInnerDictionary));
    }

    [Test]
    public void Add_ValidKeyValue_CallsSetAsync()
    {
        // Arrange
        const string key = "testKey";
        const string value = "testValue";
        _mockInnerDictionary.SetAsync(key, value).Returns(Task.CompletedTask);

        // Act
        _wrapper.Add(key, value);

        // Assert
        _mockInnerDictionary.Received(1).SetAsync(key, value);
    }

    [Test]
    public void Add_KeyTooLong_ThrowsArgumentException()
    {
        // Arrange
        var longKey = new string('a', 129); // Exceeds MaxKeyLength of 128
        const string value = "testValue";

        // Act & Assert
        var ex = Assert.Throws<ArgumentException>(() => _wrapper.Add(longKey, value));
        Assert.That(ex.Message, Does.Contain("Key length must not exceed 128 characters"));
        Assert.That(ex.ParamName, Is.EqualTo("key"));
    }

    [Test]
    public void ContainsKey_ValidKey_ReturnsTrue()
    {
        // Arrange
        const string key = "testKey";
        _mockInnerDictionary.ContainsKeyAsync(key).Returns(Task.FromResult(true));

        // Act
        var result = _wrapper.ContainsKey(key);

        // Assert
        Assert.That(result, Is.True);
        _mockInnerDictionary.Received(1).ContainsKeyAsync(key);
    }

    [Test]
    public void ContainsKey_KeyTooLong_ThrowsArgumentException()
    {
        // Arrange
        var longKey = new string('b', 129);

        // Act & Assert
        var ex = Assert.Throws<ArgumentException>(() => _wrapper.ContainsKey(longKey));
        Assert.That(ex.Message, Does.Contain("Key length must not exceed 128 characters"));
    }

    [Test]
    public void Remove_ValidKey_ReturnsTrue()
    {
        // Arrange
        const string key = "testKey";
        _mockInnerDictionary.RemoveAsync(key).Returns(Task.FromResult(true));

        // Act
        var result = _wrapper.Remove(key);

        // Assert
        Assert.That(result, Is.True);
        _mockInnerDictionary.Received(1).RemoveAsync(key);
    }

    [Test]
    public void TryGetValue_ExistingKey_ReturnsTrue()
    {
        // Arrange
        const string key = "testKey";
        const string expectedValue = "testValue";
        _mockInnerDictionary.GetAsync(key).Returns(Task.FromResult(expectedValue));

        // Act
        var result = _wrapper.TryGetValue(key, out var value);

        // Assert
        Assert.That(result, Is.True);
        Assert.That(value, Is.EqualTo(expectedValue));
        _mockInnerDictionary.Received(1).GetAsync(key);
    }

    [Test]
    public void TryGetValue_NonExistingKey_ReturnsFalse()
    {
        // Arrange
        const string key = "nonExistentKey";
        _mockInnerDictionary.GetAsync(key).Throws(new KeyNotFoundException());

        // Act
        var result = _wrapper.TryGetValue(key, out var value);

        // Assert
        Assert.That(result, Is.False);
        Assert.That(value, Is.EqualTo(default(string)));
    }

    [Test]
    public void Indexer_Get_ValidKey_ReturnsValue()
    {
        // Arrange
        const string key = "testKey";
        const string expectedValue = "testValue";
        _mockInnerDictionary.GetAsync(key).Returns(Task.FromResult(expectedValue));

        // Act
        var value = _wrapper[key];

        // Assert
        Assert.That(value, Is.EqualTo(expectedValue));
        _mockInnerDictionary.Received(1).GetAsync(key);
    }

    [Test]
    public void Indexer_Set_ValidKey_CallsSetAsync()
    {
        // Arrange
        const string key = "testKey";
        const string value = "testValue";
        _mockInnerDictionary.SetAsync(key, value).Returns(Task.CompletedTask);

        // Act
        _wrapper[key] = value;

        // Assert
        _mockInnerDictionary.Received(1).SetAsync(key, value);
    }

    [Test]
    public void Keys_CallsKeysAsync()
    {
        // Arrange
        var expectedKeys = new List<string>
        {
            "key1", "key2"
        };
        _mockInnerDictionary.KeysAsync().Returns(Task.FromResult<ICollection<string>>(expectedKeys));

        // Act
        var keys = _wrapper.Keys;

        // Assert
        Assert.That(keys, Is.EqualTo(expectedKeys));
        _mockInnerDictionary.Received(1).KeysAsync();
    }

    [Test]
    public void Values_CallsValuesAsync()
    {
        // Arrange
        var expectedValues = new List<string>
        {
            "value1", "value2"
        };
        _mockInnerDictionary.ValuesAsync().Returns(Task.FromResult<ICollection<string>>(expectedValues));

        // Act
        var values = _wrapper.Values;

        // Assert
        Assert.That(values, Is.EqualTo(expectedValues));
        _mockInnerDictionary.Received(1).ValuesAsync();
    }

    [Test]
    public void Clear_CallsClearAsync()
    {
        // Arrange
        _mockInnerDictionary.ClearAsync().Returns(Task.CompletedTask);

        // Act
        _wrapper.Clear();

        // Assert
        _mockInnerDictionary.Received(1).ClearAsync();
    }

    [Test]
    public void Count_CallsCountAsync()
    {
        // Arrange
        const int expectedCount = 5;
        _mockInnerDictionary.CountAsync().Returns(Task.FromResult(expectedCount));

        // Act
        var count = _wrapper.Count;

        // Assert
        Assert.That(count, Is.EqualTo(expectedCount));
        _mockInnerDictionary.Received(1).CountAsync();
    }

    [Test]
    public void IsReadOnly_ReturnsFalse()
    {
        // Act
        var isReadOnly = _wrapper.IsReadOnly;

        // Assert
        Assert.That(isReadOnly, Is.False);
    }

    [Test]
    public void Add_OrleansException_RetriesAndSucceeds()
    {
        // Arrange
        const string key = "testKey";
        const string value = "testValue";

        _mockInnerDictionary.SetAsync(key, value)
            .Returns(
                Task.FromException(new InvalidOperationException("Orleans hop limit is reached")),
                Task.FromException(new InvalidOperationException("Orleans silo is not owner")),
                Task.CompletedTask);

        // Act
        _wrapper.Add(key, value);

        // Assert
        _mockInnerDictionary.Received(3).SetAsync(key, value);
    }

    [Test]
    public void ContainsKey_OrleansException_RetriesAndSucceeds()
    {
        // Arrange
        const string key = "testKey";

        _mockInnerDictionary.ContainsKeyAsync(key)
            .Returns(
                Task.FromException<bool>(new InvalidOperationException("Orleans grain directory lookup failed")),
                Task.FromResult(true));

        // Act
        var result = _wrapper.ContainsKey(key);

        // Assert
        Assert.That(result, Is.True);
        _mockInnerDictionary.Received(2).ContainsKeyAsync(key);
    }

    [Test]
    public void TryGetValue_OrleansException_RetriesAndSucceeds()
    {
        // Arrange
        const string key = "testKey";
        const string expectedValue = "testValue";

        _mockInnerDictionary.GetAsync(key)
            .Returns(
                Task.FromException<string>(new InvalidOperationException("Orleans cannot forward LookUpAsync")),
                Task.FromResult(expectedValue));

        // Act
        var result = _wrapper.TryGetValue(key, out var value);

        // Assert
        Assert.That(result, Is.True);
        Assert.That(value, Is.EqualTo(expectedValue));
        _mockInnerDictionary.Received(2).GetAsync(key);
    }

    [Test]
    public void Remove_OrleansException_RetriesAndSucceeds()
    {
        // Arrange
        const string key = "testKey";

        _mockInnerDictionary.RemoveAsync(key)
            .Returns(
                Task.FromException<bool>(new InvalidOperationException("Orleans silo is not owner of dict")),
                Task.FromResult(true));

        // Act
        var result = _wrapper.Remove(key);

        // Assert
        Assert.That(result, Is.True);
        _mockInnerDictionary.Received(2).RemoveAsync(key);
    }

    [Test]
    public void Add_NonOrleansException_ThrowsImmediately()
    {
        // Arrange
        const string key = "testKey";
        const string value = "testValue";

        _mockInnerDictionary.SetAsync(key, value)
            .Returns(Task.FromException(new ArgumentException("Regular exception")));

        // Act & Assert
        var ex = Assert.Throws<ArgumentException>(() => _wrapper.Add(key, value));
        Assert.That(ex.Message, Is.EqualTo("Regular exception"));
        _mockInnerDictionary.Received(1).SetAsync(key, value);
    }

    [Test]
    public void TryGetValue_NonOrleansException_ReturnsFalse()
    {
        // Arrange
        const string key = "testKey";

        _mockInnerDictionary.GetAsync(key)
            .Returns(Task.FromException<string>(new ArgumentException("Regular exception")));

        // Act
        var result = _wrapper.TryGetValue(key, out var value);

        // Assert
        Assert.That(result, Is.False);
        Assert.That(value, Is.EqualTo(default(string)));
        _mockInnerDictionary.Received(1).GetAsync(key);
    }

    [Test]
    public void GetEnumerator_CallsToDictionaryAsync()
    {
        // Arrange
        var expectedDictionary = new Dictionary<string, string>
        {
            {
                "key1", "value1"
            },
            {
                "key2", "value2"
            }
        };
        _mockInnerDictionary.ToDictionaryAsync().Returns(Task.FromResult(expectedDictionary));

        // Act
        var enumerator = _wrapper.GetEnumerator();
        var result = new Dictionary<string, string>();
        while (enumerator.MoveNext())
        {
            result.Add(enumerator.Current.Key, enumerator.Current.Value);
        }

        // Assert
        Assert.That(result, Is.EqualTo(expectedDictionary));
        _mockInnerDictionary.Received(1).ToDictionaryAsync();
    }

    [Test]
    public void CopyTo_ValidArray_CopiesElements()
    {
        // Arrange
        var expectedDictionary = new Dictionary<string, string>
        {
            {
                "key1", "value1"
            },
            {
                "key2", "value2"
            }
        };
        _mockInnerDictionary.ToDictionaryAsync().Returns(Task.FromResult(expectedDictionary));

        var array = new KeyValuePair<string, string>[5];

        // Act
        _wrapper.CopyTo(array, 1);

        // Assert
        Assert.That(array[1].Key, Is.EqualTo("key1"));
        Assert.That(array[1].Value, Is.EqualTo("value1"));
        Assert.That(array[2].Key, Is.EqualTo("key2"));
        Assert.That(array[2].Value, Is.EqualTo("value2"));
        _mockInnerDictionary.Received(1).ToDictionaryAsync();
    }

    [Test]
    public void CopyTo_NullArray_ThrowsArgumentNullException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _wrapper.CopyTo(null!, 0));
    }

    [Test]
    public void CopyTo_InvalidArrayIndex_ThrowsArgumentOutOfRangeException()
    {
        // Arrange
        var array = new KeyValuePair<string, string>[5];

        // Act & Assert
        Assert.Throws<ArgumentOutOfRangeException>(() => _wrapper.CopyTo(array, -1));
        Assert.Throws<ArgumentOutOfRangeException>(() => _wrapper.CopyTo(array, 6));
    }
}