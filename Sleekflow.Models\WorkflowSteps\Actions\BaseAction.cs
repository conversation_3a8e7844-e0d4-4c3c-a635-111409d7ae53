using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps.Actions;

public abstract class BaseAction
{
    [JsonProperty("enabled")]
    public bool Enabled { get; set; }
}

public class ActionConfig
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("value")]
    public object Value { get; set; }

    [JsonConstructor]
    public ActionConfig(string type, object value)
    {
        Type = type;
        Value = value;
    }
}