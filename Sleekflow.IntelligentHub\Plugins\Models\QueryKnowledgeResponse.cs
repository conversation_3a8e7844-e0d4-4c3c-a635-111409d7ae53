using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Plugins.Models;

[method: JsonConstructor]
public abstract class QueryKnowledgeResponse(string knowledge, string id, string type)
{
    [JsonProperty("id")]
    public string Id { get; set; } = id;

    [JsonProperty("type")]
    public string Type { get; set; } = type;

    [JsonProperty("knowledge")]
    public string Knowledge { get; set; } = knowledge;
}