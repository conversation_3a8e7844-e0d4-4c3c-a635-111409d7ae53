using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Subscriptions;

public class EmailSubscription
{
    [JsonProperty("is_subscribed")]
    public bool IsSubscribed { get; set; }

    [JsonProperty("last_subscription_time")]
    public DateTimeOffset? LastSubscriptionTime { get; set; }

    [JsonProperty("email_subscription_metadata", TypeNameHandling = TypeNameHandling.Objects)]
    public EmailSubscriptionMetadata? EmailSubscriptionMetadata { get; set; }

    [JsonConstructor]
    public EmailSubscription(
        bool isSubscribed = false,
        DateTimeOffset? lastSubscriptionTime = null,
        EmailSubscriptionMetadata? emailSubscriptionMetadata = null)
    {
        IsSubscribed = isSubscribed;
        LastSubscriptionTime = lastSubscriptionTime;
        EmailSubscriptionMetadata = emailSubscriptionMetadata;
    }
}