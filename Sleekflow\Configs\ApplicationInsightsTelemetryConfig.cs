using Sleekflow.DependencyInjection;

namespace Sleekflow.Configs;

public interface IApplicationInsightsTelemetryConfig
{
    string IsTelemetryTracerEnabled { get; }

    string IsSamplingDisabled { get; }
}

public class ApplicationInsightsTelemetryConfig : IConfig, IApplicationInsightsTelemetryConfig
{
    public string IsTelemetryTracerEnabled { get; private set; }

    public string IsSamplingDisabled { get; private set; }

    public ApplicationInsightsTelemetryConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        IsTelemetryTracerEnabled =
            Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", target)
            ?? "FALSE";

        IsSamplingDisabled =
            Environment.GetEnvironmentVariable("APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", target)
            ?? "FALSE";
    }
}