using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Dynamics365.Authentications;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ReAuthenticate : ITrigger
{
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;

    public ReAuthenticate(
        IDynamics365AuthenticationService dynamics365AuthenticationService)
    {
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
    }

    public class ReAuthenticateInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public ReAuthenticateInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class ReAuthenticateOutput
    {
    }

    public async Task<ReAuthenticateOutput> F(
        ReAuthenticateInput reAuthenticateInput)
    {
        await _dynamics365AuthenticationService.ReAuthenticateAndStoreAsync(
            reAuthenticateInput.SleekflowCompanyId);

        return new ReAuthenticateOutput();
    }
}