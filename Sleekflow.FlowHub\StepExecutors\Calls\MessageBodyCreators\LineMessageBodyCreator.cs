using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface ILineMessageBodyCreator : IMessageBodyCreator
{
}

public class LineMessageBodyCreator : BaseMessageBodyCreator, ILineMessageBodyCreator
{
    public LineMessageBodyCreator()
        : base(ChannelTypes.Line)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        return Task.FromResult((
            CreateBaseMessageBody(
                lineMessage: new LineMessageObject(
                    type: "text",
                    text: messageStr)),
            "text"));
    }
}