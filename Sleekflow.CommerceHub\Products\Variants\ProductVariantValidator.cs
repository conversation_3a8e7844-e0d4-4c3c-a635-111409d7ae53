using System.ComponentModel.DataAnnotations;
using Sleekflow.CommerceHub.Descriptions;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.Utils;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Products.Variants;

public interface IProductVariantValidator
{
    Task ValidateProductVariantPropertiesAsync(
        string sleekflowCompanyId,
        string storeId,
        List<Multilingual> names,
        List<Description> descriptions,
        List<ProductVariant.ProductVariantAttribute> attributes,
        List<Price> prices,
        object? input = null);
}

public class ProductVariantValidator : IProductVariantValidator, IScopedService
{
    private readonly IDescriptionValidator _descriptionValidator;
    private readonly IStoreService _storeService;

    public ProductVariantValidator(
        IDescriptionValidator descriptionValidator,
        IStoreService storeService)
    {
        _descriptionValidator = descriptionValidator;
        _storeService = storeService;
    }

    // TODO Make sure the product has no default ProductVariant
    public async Task ValidateProductVariantPropertiesAsync(
        string sleekflowCompanyId,
        string storeId,
        List<Multilingual> names,
        List<Description> descriptions,
        List<ProductVariant.ProductVariantAttribute> attributes,
        List<Price> prices,
        object? input = null)
    {
        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);
        if (store is null)
        {
            throw new SfNotFoundObjectException(storeId, sleekflowCompanyId);
        }

        var languageIsoCodes = store.Languages.Select(l => l.LanguageIsoCode).ToHashSet();

        if (!MultilingualUtils.AreValidMultilinguals(names, languageIsoCodes))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Unsupported language found in the Name",
                        new[]
                        {
                            "Names"
                        })
                });
        }

        var currencyIsoCodes = store.Currencies.Select(l => l.CurrencyIsoCode).ToHashSet();

        foreach (var currencyIsoCode in currencyIsoCodes)
        {
            var numOfMatchedPrices = prices.Count(p => p.CurrencyIsoCode == currencyIsoCode);

            if (numOfMatchedPrices > 1)
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            "Multiple prices found for the same currency",
                            new[]
                            {
                                "Prices"
                            })
                    });
            }

            if (numOfMatchedPrices == 0)
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            $"Missing currency has been located currency iso code {currencyIsoCode} ",
                            new[]
                            {
                                "Prices"
                            })
                    });
            }
        }

        foreach (var price in prices)
        {
            if (!currencyIsoCodes.Contains(price.CurrencyIsoCode))
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            "Unsupported currency found in the price",
                            new[]
                            {
                                "Prices"
                            })
                    });
            }
        }

        await _descriptionValidator.AssertValidDescriptionsAsync(
            descriptions,
            languageIsoCodes);
    }
}