﻿using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowExecutionRestrictedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string StateId { get; set; }

    public StateIdentity StateIdentity { get; set; }

    public string? WorkflowType { get; set; }

    public DateTimeOffset RestrictedAt { get; set; } = DateTimeOffset.UtcNow;

    public OnWorkflowExecutionRestrictedEvent(
        string sleekflowCompanyId,
        string stateId,
        StateIdentity stateIdentity,
        string? workflowType)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        StateIdentity = stateIdentity;
        WorkflowType = workflowType;
    }
}