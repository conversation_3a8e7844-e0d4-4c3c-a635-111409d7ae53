using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyConfigs;

public class CompanyConfigDto
    : IHasSleekflowCompanyId, IHasCreatedBy, IHasUpdatedBy, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("background_information")]
    public string BackgroundInformation { get; set; }

    [JsonProperty("preferred_language")]
    public string PreferredLanguage { get; set; }

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasUpdatedBy.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public CompanyConfigDto(
        string id,
        string sleekflowCompanyId,
        string name,
        string backgroundInformation,
        string preferredLanguage,
        DateTimeOffset createdAt,
        AuditEntity.SleekflowStaff? createdBy,
        DateTimeOffset updatedAt,
        AuditEntity.SleekflowStaff? updatedBy,
        string? eTag = null)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        Name = name;
        BackgroundInformation = backgroundInformation;
        PreferredLanguage = preferredLanguage;
        CreatedAt = createdAt;
        CreatedBy = createdBy;
        UpdatedAt = updatedAt;
        UpdatedBy = updatedBy;
        ETag = eTag;
    }

    public CompanyConfigDto(CompanyConfig config)
        : this(
            config.Id,
            config.SleekflowCompanyId,
            config.Name,
            config.BackgroundInformation,
            config.PreferredLanguage,
            config.CreatedAt,
            config.CreatedBy,
            config.UpdatedAt,
            config.UpdatedBy,
            config.ETag)
    {
    }
}