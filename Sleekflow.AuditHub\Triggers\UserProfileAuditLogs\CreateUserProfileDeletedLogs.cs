﻿using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileDeletedLogs
    : ITrigger<
        CreateUserProfileDeletedLogs.CreateUserProfileDeletedLogsInput,
        CreateUserProfileDeletedLogs.CreateUserProfileDeletedLogsOutput>
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;

    public CreateUserProfileDeletedLogs(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
    }

    public class CreateUserProfileDeletedLogsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_ids")]
        public List<string> SleekflowUserProfileIds { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        public UserProfileDeletedLogData Data { get; set; }

        [JsonConstructor]
        public CreateUserProfileDeletedLogsInput(
            string sleekflowCompanyId,
            List<string> sleekflowUserProfileIds,
            string? sleekflowStaffId,
            string auditLogText)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileIds = sleekflowUserProfileIds;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
        }
    }

    public class CreateUserProfileDeletedLogsOutput
    {
        [JsonProperty("ids")]
        public List<string> Ids { get; set; }

        [JsonConstructor]
        public CreateUserProfileDeletedLogsOutput(List<string> ids)
        {
            Ids = ids;
        }
    }

    public async Task<CreateUserProfileDeletedLogsOutput> F(
        CreateUserProfileDeletedLogsInput createUserProfileDeletedLogsInput)
    {
        const int maxDegreeOfParallelism = 3;

        var sleekflowCompanyId = createUserProfileDeletedLogsInput.SleekflowCompanyId;
        var sleekflowStaffId = createUserProfileDeletedLogsInput.SleekflowStaffId;
        var auditLogText = createUserProfileDeletedLogsInput.AuditLogText;
        var dataStr = JsonConvert.SerializeObject(createUserProfileDeletedLogsInput.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var auditLogIds = new ConcurrentBag<string>();

        await Parallel.ForEachAsync(
            createUserProfileDeletedLogsInput.SleekflowUserProfileIds,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = maxDegreeOfParallelism
            },
            async (sleekflowUserProfileId, _) =>
            {
                var id = _idService.GetId("UserProfileAuditLog");
                await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
                    new UserProfileAuditLog(
                        id,
                        sleekflowCompanyId,
                        sleekflowStaffId,
                        sleekflowUserProfileId,
                        UserProfileAuditLogTypes.UserProfileDeleted,
                        auditLogText,
                        data,
                        DateTimeOffset.UtcNow,
                        null));

                auditLogIds.Add(id);
            });

        return new CreateUserProfileDeletedLogsOutput(auditLogIds.ToList());
    }
}