﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IAzureEmbeddingConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DeploymentName { get; }
}

public class AzureEmbeddingConfig : IConfig, IAzureEmbeddingConfig
{
    /// <summary>
    /// Azure OpenAI endpoint.
    /// </summary>
    public string Endpoint { get; }

    /// <summary>
    /// Key to access the AI service.
    /// </summary>
    public string Key { get; }

    /// <summary>
    /// Azure OpenAI deployment name to use for completions.
    /// </summary>
    public string DeploymentName { get; }

    public AzureEmbeddingConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("AZUREOPENAI_EMBEDDING_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("AZUREOPENAI_EMBEDDING_ENDPOINT");

        Key =
            Environment.GetEnvironmentVariable("AZUREOPENAI_EMBEDDING_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("AZUREOPENAI_EMBEDDING_KEY");

        DeploymentName =
            Environment.GetEnvironmentVariable("AZUREOPENAI_EMBEDDING_DEPLOYMENT_NAME", target) ??
            throw new SfMissingEnvironmentVariableException("AZUREOPENAI_EMBEDDING_DEPLOYMENT_NAME");
    }
}