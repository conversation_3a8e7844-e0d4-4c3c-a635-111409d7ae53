using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.ServerLocations;

namespace Sleekflow.InternalIntegrationHub.CompanyServerLocation;

public interface ICompanyServerLocationService
{
    public Task<string> GetCompanyServerLocation(string companyId);
}

public class CompanyServerLocationService : IScopedService, ICompanyServerLocationService
{
    private readonly IRequestClient<GetCompanyServerLocationRequest> _getCompanyServerLocationRequestClient;

    public CompanyServerLocationService(
        IRequestClient<GetCompanyServerLocationRequest> getCompanyServerLocationRequestClient)
    {
        _getCompanyServerLocationRequestClient = getCompanyServerLocationRequestClient;
    }

    public async Task<string> GetCompanyServerLocation(string companyId)
    {
        var getCompanyServerLocationResponse =
            await _getCompanyServerLocationRequestClient.GetResponse<GetCompanyServerLocationReply>(
                new GetCompanyServerLocationRequest(
                    companyId));

        return getCompanyServerLocationResponse.Message.ServerLocation;
    }
}