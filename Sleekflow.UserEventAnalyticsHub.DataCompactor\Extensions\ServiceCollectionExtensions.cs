using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;
using Sleekflow.UserEventAnalyticsHub.DataCompactor.Utils;
using Sleekflow.UserEventHub.Models.SqlJobs;
using Sleekflow.Locks;
using Sleekflow.DependencyInjection;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Extensions;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddDataCompactorServices(this IServiceCollection services)
    {
        // Register configurations following hub pattern
        services.AddSingleton<ICompactorConfig, CompactorConfig>();
        services.AddSingleton<IPostgreSqlConfig, PostgreSqlConfig>();

        // Register services
        services.AddScoped<IBlobDiscoveryService, BlobDiscoveryService>();
        services.AddScoped<IPostgreSqlSchemaService, PostgreSqlSchemaService>();
        services.AddScoped<IFileTrackingService, FileTrackingService>();
        services.AddScoped<IParquetCompactionService, ParquetCompactionService>();

        // Register mock services for testing
        services.AddScoped<ISqlJobService, MockSqlJobService>();
        services.AddScoped<ILockService, MockLockService>();
        services.AddScoped<ISqlJobProcessingService, SqlJobProcessingService>();

        // Register utility services
        services.AddScoped<DataIntegrityValidator>();

        return services;
    }
}