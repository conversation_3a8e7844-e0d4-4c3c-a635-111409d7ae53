using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

/// <summary>
/// An entry in the static search pages list
/// </summary>
public class StaticPageEntry
{
    /// <summary>
    /// The page name or title
    /// </summary>
    [JsonProperty("page")]
    public string Page { get; set; } = string.Empty;

    /// <summary>
    /// The URL of the page
    /// </summary>
    [JsonProperty("url")]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// A description of the page content
    /// </summary>
    [JsonProperty("description")]
    public string Description { get; set; } = string.Empty;
}

/// <summary>
/// Configuration for Static Search integration.
/// </summary>
public class StaticSearchConfig
{
    /// <summary>
    /// List of static pages available for search
    /// </summary>
    [JsonProperty("pages")]
    public List<StaticPageEntry> Pages { get; set; } = new List<StaticPageEntry>();

    [JsonConstructor]
    public StaticSearchConfig(List<StaticPageEntry>? pages = null)
    {
        Pages = pages ?? new List<StaticPageEntry>();
    }
}