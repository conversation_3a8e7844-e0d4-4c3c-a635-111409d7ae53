using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionFilters : IValidatableObject
{
    [JsonProperty("workflow_id")]
    public string? WorkflowId { get; set; }

    [JsonProperty("workflow_execution_status")]
    public string? WorkflowExecutionStatus { get; set; }

    [JsonProperty("from_date_time")]
    public DateTimeOffset? FromDateTime { get; set; }

    [JsonProperty("to_date_time")]
    public DateTimeOffset? ToDateTime { get; set; }

    [JsonConstructor]
    public WorkflowExecutionFilters(
        string? workflowId,
        string? workflowExecutionStatus,
        DateTimeOffset? fromDateTime,
        DateTimeOffset? toDateTime)
    {
        WorkflowId = workflowId;
        WorkflowExecutionStatus = workflowExecutionStatus;
        FromDateTime = fromDateTime;
        ToDateTime = toDateTime;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (FromDateTime.HasValue && !ToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both FromDateTime and ToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(FromDateTime)
                });
        }

        if (!FromDateTime.HasValue && ToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both FromDateTime and ToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(ToDateTime)
                });
        }

        if (FromDateTime.HasValue && ToDateTime.HasValue)
        {
            if (FromDateTime.Value > ToDateTime.Value)
            {
                yield return new ValidationResult(
                    "FromDateTime can't be greater than ToDateTime",
                    new[]
                    {
                        nameof(FromDateTime),
                        nameof(ToDateTime)
                    });
            }

            if ((ToDateTime.Value - FromDateTime.Value).TotalDays > 31)
            {
                yield return new ValidationResult(
                    "The duration between FromDateTime and ToDateTime can't be more than 31 days",
                    new[]
                    {
                        nameof(FromDateTime),
                        nameof(ToDateTime)
                    });
            }
        }
    }
}