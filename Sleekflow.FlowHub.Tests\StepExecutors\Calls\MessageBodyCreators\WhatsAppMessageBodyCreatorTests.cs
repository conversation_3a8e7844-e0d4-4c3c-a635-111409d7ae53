using NUnit.Framework;
using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

namespace Sleekflow.FlowHub.Tests.StepExecutors.Calls.MessageBodyCreators;

[TestFixture]
public class WhatsAppMessageBodyCreatorTests
{
    private WhatsAppMessageBodyCreator _creator;
    private SendMessageV2StepArgs _args;

    [SetUp]
    public void Setup()
    {
        _creator = new WhatsAppMessageBodyCreator();
        _args = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageInteractiveType = "button",
                InteractiveTitle = "Test Title",
                InteractiveOptions = new List<InteractiveOption>
                {
                    new("1", "Option 1", "Description 1"),
                    new("2", "Option 2", "Description 2")
                }
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);
    }

    [Test]
    public void CanHandle_GivenWhatsAppChannel_ShouldReturnTrue()
    {
        // Act
        var result = _creator.CanHandle(ChannelTypes.WhatsAppCloudApi);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public void CanHandle_GivenNonWhatsAppChannel_ShouldReturnFalse()
    {
        // Act
        var result = _creator.CanHandle(ChannelTypes.Facebook);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CreateMessageBodyAsync_GivenTextMessage_ShouldCreateCorrectMessageBody()
    {
        // Arrange
        const string messageText = "Hello, WhatsApp!";
        var textArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "manual_message",
                MessageInteractiveType = "none"
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync(messageText, textArgs);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.TextMessage, Is.Not.Null);
        Assert.That(body.TextMessage!.Text, Is.EqualTo(messageText));
        Assert.That(type, Is.EqualTo("text"));
    }

    [Test]
    public async Task CreateMessageBodyAsync_GivenTemplateMessage_ShouldDeserializeCorrectly()
    {
        // Arrange
        var templateArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "template_message"
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        var templateMessage = new MessageBody(
            audioMessage: null,
            contactsMessage: null,
            currencyMessage: null,
            documentMessage: null,
            imageMessage: null,
            locationMessage: null,
            reactionMessage: null,
            textMessage: null,
            videoMessage: null,
            interactiveMessage: null,
            templateMessage: new TemplateMessageObject(
                templateName: "template_name",
                language: "en",
                components: new List<TemplateMessageObjectComponent>()),
            interactiveReplyMessage: null,
            dateTimeMessage: null,
            facebookMessengerMessage: null,
            instagramMessengerMessage: null,
            orderMessage: null,
            telegramMessengerMessage: null,
            weChatMessengerMessage: null,
            liveChatMessage: null,
            viberMessage: null,
            lineMessage: null,
            smsMessage: null);
        var messageStr = Newtonsoft.Json.JsonConvert.SerializeObject(templateMessage);

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync(messageStr, templateArgs);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.TemplateMessage, Is.Not.Null);
        Assert.That(body.TemplateMessage!.TemplateName, Is.EqualTo("template_name"));
        Assert.That(body.TemplateMessage.Language, Is.EqualTo("en"));
        Assert.That(type, Is.EqualTo("template"));
    }

    [Test]
    public async Task CreateMessageBodyAsync_GivenInteractiveMessage_ShouldCreateCorrectMessageBody()
    {
        // Arrange
        const string messageText = "Choose an option:";
        var interactiveArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "manual_message",
                MessageInteractiveType = "button",
                InteractiveTitle = "Test Title",
                InteractiveOptions = new List<InteractiveOption>
                {
                    new("1", "Option 1", "Description 1"),
                    new("2", "Option 2", "Description 2")
                }
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync(messageText, interactiveArgs);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.InteractiveMessage, Is.Not.Null);
        Assert.That(body.InteractiveMessage!.Type, Is.EqualTo("button"));
        Assert.That(body.InteractiveMessage.Body.Text, Is.EqualTo(messageText));
        Assert.That(body.InteractiveMessage.Action.Buttons, Has.Count.EqualTo(2));
        Assert.That(body.InteractiveMessage.Action.Buttons![0].Reply.Id, Is.EqualTo("1"));
        Assert.That(body.InteractiveMessage.Action.Buttons[0].Reply.Title, Is.EqualTo("Option 1"));
        Assert.That(type, Is.EqualTo("interactive"));
    }

    [Test]
    public void CreateMessageBodyAsync_GivenUnsupportedMessageType_ShouldThrowException()
    {
        // Arrange
        const string messageText = "Hello, WhatsApp!";
        var unsupportedArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "unsupported"
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        // Act & Assert
        var exception = Assert.ThrowsAsync<InvalidOperationException>(
            async () => await _creator.CreateMessageBodyAndMessageTypeAsync(messageText, unsupportedArgs));

        Assert.That(exception.Message, Is.EqualTo("Unsupported message type  for whatsappcloudapi."));
    }

    [Test]
    public async Task CreateMessageBodyAsync_WithTemplateMessageType_ShouldCreateTemplateMessage()
    {
        // Arrange
        var templateArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "template_message"
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        var templateMessage = new MessageBody(
            audioMessage: null,
            contactsMessage: null,
            currencyMessage: null,
            documentMessage: null,
            imageMessage: null,
            locationMessage: null,
            reactionMessage: null,
            textMessage: null,
            videoMessage: null,
            interactiveMessage: null,
            templateMessage: new TemplateMessageObject(
                templateName: "template_name",
                language: "en",
                components: new List<TemplateMessageObjectComponent>()),
            interactiveReplyMessage: null,
            dateTimeMessage: null,
            facebookMessengerMessage: null,
            instagramMessengerMessage: null,
            orderMessage: null,
            telegramMessengerMessage: null,
            weChatMessengerMessage: null,
            liveChatMessage: null,
            viberMessage: null,
            lineMessage: null,
            smsMessage: null);
        var messageStr = Newtonsoft.Json.JsonConvert.SerializeObject(templateMessage);

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync(messageStr, templateArgs);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.TemplateMessage, Is.Not.Null);
        Assert.That(body.TemplateMessage!.TemplateName, Is.EqualTo("template_name"));
        Assert.That(type, Is.EqualTo("template"));
    }

    [Test]
    public async Task CreateMessageBodyAsync_WithManualMessageAndNoneInteractiveType_ShouldCreateTextMessage()
    {
        // Arrange
        var textArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Text message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "manual_message",
                MessageInteractiveType = "none"
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync("Hello World", textArgs);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.TextMessage, Is.Not.Null);
        Assert.That(body.TextMessage!.Text, Is.EqualTo("Hello World"));
        Assert.That(type, Is.EqualTo("text"));
    }

    [Test]
    public async Task CreateMessageBodyAsync_WithManualMessageAndButtonInteractiveType_ShouldCreateButtonInteractiveMessage()
    {
        // Arrange
        var buttonArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Button message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "manual_message",
                MessageInteractiveType = "button",
                InteractiveTitle = "Button Title",
                InteractiveOptions = new List<InteractiveOption>
                {
                    new("1", "Button 1", "Description 1"),
                    new("2", "Button 2", "Description 2")
                }
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync("Click a button", buttonArgs);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.InteractiveMessage, Is.Not.Null);
        Assert.That(body.InteractiveMessage!.Type, Is.EqualTo("button"));
        Assert.That(body.InteractiveMessage.Body.Text, Is.EqualTo("Click a button"));
        Assert.That(body.InteractiveMessage.Action.Buttons, Has.Count.EqualTo(2));
        Assert.That(body.InteractiveMessage.Action.Buttons![0].Reply.Id, Is.EqualTo("1"));
        Assert.That(body.InteractiveMessage.Action.Buttons[0].Reply.Title, Is.EqualTo("Button 1"));
        Assert.That(type, Is.EqualTo("interactive"));
    }

    [Test]
    public async Task CreateMessageBodyAsync_WithManualMessageAndListInteractiveType_ShouldCreateListInteractiveMessage()
    {
        // Arrange
        var listArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "List message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "manual_message",
                MessageInteractiveType = "list",
                InteractiveTitle = "List Title",
                InteractiveOptions = new List<InteractiveOption>
                {
                    new("1", "List Item 1", "Description 1"),
                    new("2", "List Item 2", "Description 2")
                }
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync("Choose from list", listArgs);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.InteractiveMessage, Is.Not.Null);
        Assert.That(body.InteractiveMessage!.Type, Is.EqualTo("list"));
        Assert.That(body.InteractiveMessage.Body.Text, Is.EqualTo("Choose from list"));
        Assert.That(body.InteractiveMessage.Action.Sections, Has.Count.EqualTo(1));
        Assert.That(body.InteractiveMessage.Action.Sections![0].Rows, Has.Count.EqualTo(2));
        Assert.That(body.InteractiveMessage.Action.Sections[0].Rows![0].Id, Is.EqualTo("1"));
        Assert.That(body.InteractiveMessage.Action.Sections[0].Rows[0].Title, Is.EqualTo("List Item 1"));
        Assert.That(body.InteractiveMessage.Action.Sections[0].Rows[0].Description, Is.EqualTo("Description 1"));
        Assert.That(type, Is.EqualTo("interactive"));
    }

    [Test]
    public void CreateMessageBodyAsync_WithUnknownMessageType_ShouldThrowException()
    {
        // Arrange
        var unknownArgs = new SendMessageV2StepArgs(
            channelType: ChannelTypes.WhatsAppCloudApi,
            channelIdentityId: "test-channel-id",
            messageExpr: "Unknown message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: new WhatsAppCloudApiMessageParameters
            {
                MessageType = "unknown_type"
            },
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);

        // Act & Assert
        var exception = Assert.ThrowsAsync<InvalidOperationException>(
            async () => await _creator.CreateMessageBodyAndMessageTypeAsync("test", unknownArgs));

        Assert.That(exception.Message, Is.EqualTo("Unsupported message type  for whatsappcloudapi."));
    }
}