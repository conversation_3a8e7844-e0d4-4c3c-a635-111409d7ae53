﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Subscriptions;
using Sleekflow.Integrator.Salesforce.UserMappingConfigs;

namespace Sleekflow.Integrator.Salesforce.Services;

public interface ISalesforceMigrationToFlowHubService
{
    Task PrepareMigrationToFlowHubAsync(
        string sleekflowCompanyId,
        List<string> entityTypesToMigrate);
}

public class SalesforceMigrationToFlowHubService : ISingletonService, ISalesforceMigrationToFlowHubService
{
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceSubscriptionRepository _salesforceSubscriptionRepository;
    private readonly ISalesforceUserMappingConfigService _salesforceUserMappingConfigService;

    public SalesforceMigrationToFlowHubService(
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService,
        ISalesforceObjectService salesforceObjectService,
        ISalesforceSubscriptionRepository salesforceSubscriptionRepository,
        ISalesforceUserMappingConfigService salesforceUserMappingConfigService)
    {
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
        _salesforceObjectService = salesforceObjectService;
        _salesforceSubscriptionRepository = salesforceSubscriptionRepository;
        _salesforceUserMappingConfigService = salesforceUserMappingConfigService;
    }

    public async Task PrepareMigrationToFlowHubAsync(
        string sleekflowCompanyId,
        List<string> entityTypesToMigrate)
    {
        var authentication = await _salesforceAuthenticationService.GetAsync(sleekflowCompanyId);

        if (authentication is null)
        {
            throw new SfUnauthorizedException();
        }

        var createdConnection = await CreateAndGetConnectionForMigrationToFlowHub(
            authentication);

        await _salesforceUserMappingConfigService.CreateAndGetAsync(
            sleekflowCompanyId,
            createdConnection.Id,
            new List<UserMapping>());

        await ModifySubscriptionsForMigrationToFlowHub(
            sleekflowCompanyId,
            createdConnection.Id,
            entityTypesToMigrate);
    }

    private async Task<SalesforceConnection> CreateAndGetConnectionForMigrationToFlowHub(
        SalesforceAuthentication authentication)
    {
        var organizationId = authentication.InstanceUrl.Replace("https://", string.Empty);
        var organizationName = await _salesforceObjectService.GetOrganizationNameAsync(authentication);
        var environment = authentication.InstanceUrl.Contains("test.salesforce.com") ? "sandbox" : "production";

        return await _salesforceConnectionService.CreateAndGetAsync(
            authentication.SleekflowCompanyId,
            organizationId,
            authentication.Id,
            organizationName,
            environment,
            true);
    }

    private async Task ModifySubscriptionsForMigrationToFlowHub(
        string sleekflowCompanyId,
        string connectionId,
        List<string> entityTypesToMigrate)
    {
        var subscriptions = (await _salesforceSubscriptionRepository.GetObjectsAsync(
                s => s.SleekflowCompanyId == sleekflowCompanyId))
            .ToList();

        foreach (var subscription in subscriptions)
        {
            if (entityTypesToMigrate.Contains(subscription.EntityTypeName))
            {
                await _salesforceSubscriptionRepository.PatchAsync(
                    subscription.Id,
                    sleekflowCompanyId,
                    new List<PatchOperation>
                    {
                        PatchOperation.Set("/connection_id", connectionId),
                        PatchOperation.Set("/is_flows_based", true),
                    });
            }
            else
            {
                await _salesforceSubscriptionRepository.DeleteAsync(
                    subscription.Id,
                    subscription.SleekflowCompanyId);
            }
        }
    }
}