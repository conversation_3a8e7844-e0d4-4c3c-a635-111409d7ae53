using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class UserProfileLabelCreatedsSystemLogData
{
    [JsonProperty("label_name")]
    public string LabelName { get; set; }

    [JsonProperty("label_color")]
    public string LabelColor { get; set; }

    [JsonConstructor]
    public UserProfileLabelCreatedsSystemLogData(string labelName, string labelColor)
    {
        LabelName = labelName;
        LabelColor = labelColor;
    }
}