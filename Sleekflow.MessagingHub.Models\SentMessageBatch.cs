﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;

namespace Sleekflow.MessagingHub.Models;

[Resolver(typeof(IMessagingHubDbResolver))]
[DatabaseId("messaginghubdb")]
[ContainerId("sent_message")]
public class SentMessageBatch : Entity
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sent_messages")]
    [Required]
    public List<SentMessage> SentMessages { get; set; }

    [JsonProperty("orchestrator_id")]
    [Required]
    public Guid OrchestratorId { get; }

    [JsonConstructor]
    public SentMessageBatch(
        string id,
        string sleekflowCompanyId,
        List<SentMessage> sentMessages,
        Guid orchestratorId)
        : base(id, "SentMessageBatch")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SentMessages = sentMessages;
        OrchestratorId = orchestratorId;
    }
}