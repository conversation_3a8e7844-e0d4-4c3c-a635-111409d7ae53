using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments;

public interface IFileDocumentRepository : IDynamicFiltersRepository<FileDocument>
{
    Task<List<FileDocument>> GetFileDocumentsByBlobIdAsync(
        string sleekflowCompanyId,
        string blobId);

    Task<FileDocument> PatchAndGetDocumentAsync(
        FileDocument fileDocument,
        DocumentStatistics documentStatistics);

    Task<FileDocument> PatchFileDocumentProcessStatusByBlobIdAsync(
        string sleekflowCompanyId,
        string blobId,
        string newStatus);

    Task<int> GetUsedPagesAsync(string sleekflowCompanyId);
}

public class FileDocumentRepository
    : DynamicFiltersBaseRepository<FileDocument>, IFileDocumentRepository, IScopedService
{
    private readonly ILogger<FileDocumentRepository> _logger;

    public FileDocumentRepository(
        ILogger<FileDocumentRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
        _logger = logger;
    }

    public async Task<List<FileDocument>> GetFileDocumentsByBlobIdAsync(string sleekflowCompanyId, string blobId)
    {
        return await GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.BlobId == blobId);
    }

    public async Task<FileDocument> PatchAndGetDocumentAsync(
        FileDocument fileDocument,
        DocumentStatistics documentStatistics)
    {
        return await PatchAndGetAsync(
            fileDocument.Id,
            fileDocument.SleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace($"{FileDocument.PropertyNameDocumentStatistics}", documentStatistics),
                Replace($"{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            });
    }

    public async Task<FileDocument> PatchFileDocumentProcessStatusByBlobIdAsync(
        string sleekflowCompanyId,
        string blobId,
        string newStatus)
    {
        var documents = await GetFileDocumentsByBlobIdAsync(sleekflowCompanyId, blobId);
        if (documents.Count < 1)
        {
            throw new SfNotFoundObjectException("Document not found");
        }

        return await PatchAndGetAsync(
            documents[0].Id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace($"{KbDocument.PropertyNameFileDocumentProcessStatus}", newStatus),
                Replace($"{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            });
    }

    public async Task<int> GetUsedPagesAsync(string sleekflowCompanyId)
    {
        var fileDocuments = await GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId);

        return fileDocuments.Sum(fileDocument => fileDocument.DocumentStatistics.TotalPages);
    }
}