using Microsoft.Azure.Cosmos;
using Moq;
using NUnit.Framework;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.FlowHub.Blobs;
using Sleekflow.Caches;
using Microsoft.Extensions.Logging;
using Sleekflow.Ids;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

[TestFixture]
public class AiAgentWorkflowTests
{
    private Mock<IWorkflowService> _mockWorkflowService;
    private Mock<IWorkflowRepository> _mockWorkflowRepository;
    private Mock<IBlobService> _mockBlobService;
    private Mock<ICacheService> _mockCacheService;
    private IAiAgentWorkflowService _aiAgentWorkflowService;
    private Mock<ILogger<AiAgentWorkflowService>> _mockLogger;
    private Mock<IWorkflowStepValidator> _mockWorkflowStepValidator;
    private Mock<IIdService> _mockIdService;
    private Mock<Internals.Agents.IAgentConfigService> _mockAgentConfigService;

    [SetUp]
    public void Setup()
    {
        _mockWorkflowService = new Mock<IWorkflowService>();
        _mockBlobService = new Mock<IBlobService>();
        _mockCacheService = new Mock<ICacheService>();
        _mockLogger = new Mock<ILogger<AiAgentWorkflowService>>();
        _mockWorkflowStepValidator = new Mock<IWorkflowStepValidator>();
        _mockIdService = new Mock<IIdService>();
        _mockAgentConfigService = new Mock<Internals.Agents.IAgentConfigService>();
        _aiAgentWorkflowService = new AiAgentWorkflowService(
            _mockWorkflowService.Object,
            _mockBlobService.Object,
            _mockCacheService.Object,
            _mockLogger.Object,
            _mockWorkflowStepValidator.Object,
            _mockIdService.Object,
            _mockAgentConfigService.Object
            );
    }

    [Test]
    public async Task CreateAiAgentWorkflow_WhenTemplatesExist_ShouldCreateAndReturnWorkflow()
    {
        // Arrange
        var sleekflowCompanyId = "test-company-id";
        var sleekflowStaffId = "test-staff-id";
        var workflowName = "Test AI Agent Workflow";
        var workflowType = "Standard";
        var userId = "test-user-id";
        var version = "v1";

        var createdBy = new AuditEntity.SleekflowStaff(userId, null);

        var whatsappCloudApiConfigs = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "channelIdentityId", "12345678901" },
                { "name", "Test WhatsApp" },
                { "channelType", "whatsapp" }
            }
        };

        var labels = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "label-id-1" },
                { "hashtag", "important" },
                { "hashTagColor", "#ff0000" }
            }
        };

        var schemafulObject = new Dictionary<string, object>
        {
            { "id", "schema-id-1" },
            { "name", "Test Schema" },
            { "properties", new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        { "id", "prop-id-1" },
                        { "unique_name", "score" },
                        { "name", "Score" }
                    },
                    new Dictionary<string, object>
                    {
                        { "id", "prop-id-2" },
                        { "unique_name", "category" },
                        { "name", "Category" }
                    }
                }
            }
        };

        var agents = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "agent-id-1" },
                { "name", "Fast Agent" },
                { "collaboration_mode", "Short" }
            },
            new Dictionary<string, object>
            {
                { "id", "agent-id-2" },
                { "name", "Comprehensive Agent" },
                { "collaboration_mode", "Long" }
            }
        };

        var contactPropertie = new Dictionary<string, object>
        {
            { "id", "contact-prop-id-1" },
            { "name", "EmailAddress" }
        };

        // Mock template files
        var flowJson = new Dictionary<string, object>
        {
            { "metadata", new Dictionary<string, object>() },
            { "steps", new List<object>() },
            { "triggers", new Dictionary<string, object>
                {
                    { "message_received", new Dictionary<string, object>
                        {
                            { "condition", "channel_identity_id eq 85230087001" }
                        }
                    }
                }
            }
        };

        var flowStepsJson = new List<Step>();

        var flowMetaFormattedJson = new Dictionary<string, object>
        {
            { "v6", new Dictionary<string, object>
                {
                    { "nodes", new List<Dictionary<string, object>>
                        {
                            new Dictionary<string, object>
                            {
                                { "id", "step-1" },
                                { "type", "agentRecommendReply" },
                                { "data", new Dictionary<string, object>
                                    {
                                        { "formValues", new Dictionary<string, object>
                                            {
                                                { "title", "Fast AI" },
                                                { "companyAgentConfigId", "old-agent-id" },
                                                { "actionType", "agentRecommendReply" }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    { "edges", new List<Dictionary<string, object>>() }
                }
            }
        };

        // Setup mock responses
        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<Dictionary<string, object>>("ai-flow-template", "flow.json"))
            .ReturnsAsync(flowJson);

        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<List<Step>>("ai-flow-template", "flow_steps.json"))
            .ReturnsAsync(flowStepsJson);

        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<Dictionary<string, object>>("ai-flow-template", "flow_meta_formatted.json"))
            .ReturnsAsync(flowMetaFormattedJson);

        // Setup repository to return workflow on creation
        _mockWorkflowRepository
            .Setup(x => x.CreateAndGetAsync(It.IsAny<Workflow>(), sleekflowCompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Workflow w, string _, CancellationToken __) => w);


        // Act
        var result = await _aiAgentWorkflowService.CreateAiAgentWorkflow(
            sleekflowCompanyId,
            whatsappCloudApiConfigs,
            labels,
            schemafulObject,
            agents,
            contactPropertie,
            sleekflowStaffId,
            "step-1",
            "dependency-workflow-id");

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null);
            Assert.That(result.Id, Is.Not.EqualTo(string.Empty));
            Assert.That(result.WorkflowVersionedId, Is.Not.EqualTo(string.Empty));
            Assert.That(result.Name, Is.EqualTo(workflowName));
            Assert.That(result.WorkflowType, Is.EqualTo(workflowType));
            Assert.That(result.SleekflowCompanyId, Is.EqualTo(sleekflowCompanyId));
            Assert.That(result.CreatedBy, Is.Not.Null);
            Assert.That(result.ActivationStatus, Is.EqualTo(WorkflowActivationStatuses.Draft));
        });

        // Verify repository was called to create workflow
        _mockWorkflowRepository.Verify(x => x.CreateAndGetAsync(It.IsAny<Workflow>(), sleekflowCompanyId, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Test]
    public async Task CreateAiAgentWorkflow_WhenTemplatesNotFound_ShouldThrowException()
    {
        // Arrange
        var sleekflowCompanyId = "test-company-id";
        var sleekflowStaffId = "test-staff-id";

        // Setup mock responses - return null for templates
        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<Dictionary<string, object>>("ai-flow-template", "flow.json"))
            .ReturnsAsync((Dictionary<string, object>) null);

        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<List<Step>>("ai-flow-template", "flow_steps.json"))
            .ReturnsAsync((List<Step>) null);

        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<Dictionary<string, object>>("ai-flow-template", "flow_meta_formatted.json"))
            .ReturnsAsync((Dictionary<string, object>) null);

        // Act & Assert
        Assert.ThrowsAsync<SfInternalErrorException>(async () =>
            await _aiAgentWorkflowService.CreateAiAgentWorkflow(
                sleekflowCompanyId,
                new List<Dictionary<string, object>>(),
                new List<Dictionary<string, object>>(),
                new Dictionary<string, object>(),
                new List<Dictionary<string, object>>(),
                new Dictionary<string, object>(),
                sleekflowStaffId,
                "step-1",
                "dependency-workflow-id"));
    }

    [Test]
    public async Task CreateAiAgentWorkflow_ShouldReplacePhoneNumbersInTriggers()
    {
        // Arrange
        var sleekflowCompanyId = "test-company-id";
        var sleekflowStaffId = "test-staff-id";

        var whatsappCloudApiConfigs = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "channelIdentityId", "12345678901" },
                { "name", "Test WhatsApp" },
                { "channelType", "whatsapp" }
            }
        };

        // Mock template files with trigger conditions
        var flowJson = new Dictionary<string, object>
        {
            { "metadata", new Dictionary<string, object>() },
            { "steps", new List<object>() },
            { "triggers", new Dictionary<string, object>
                {
                    { "message_received", new Dictionary<string, object>
                        {
                            { "condition", "channel_identity_id eq 85230087001" }
                        }
                    }
                }
            }
        };

        var flowStepsJson = new List<Step>();
        var flowMetaFormattedJson = new Dictionary<string, object>
        {
            { "v6", new Dictionary<string, object>
                {
                    { "nodes", new List<Dictionary<string, object>>() },
                    { "edges", new List<Dictionary<string, object>>() }
                }
            }
        };

        // Setup mock responses
        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<Dictionary<string, object>>("ai-flow-template", "flow.json"))
            .ReturnsAsync(flowJson);

        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<List<Step>>("ai-flow-template", "flow_steps.json"))
            .ReturnsAsync(flowStepsJson);

        _mockBlobService
            .Setup(x => x.DownloadJsonAsAsync<Dictionary<string, object>>("ai-flow-template", "flow_meta_formatted.json"))
            .ReturnsAsync(flowMetaFormattedJson);

        Workflow capturedWorkflow = null;

        // Setup repository to capture created workflow
        _mockWorkflowRepository
            .Setup(x => x.CreateAndGetAsync(It.IsAny<Workflow>(), sleekflowCompanyId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((Workflow w, string _, CancellationToken __) =>
            {
                capturedWorkflow = w;
                return w;
            });

        // Act
        await _aiAgentWorkflowService.CreateAiAgentWorkflow(
            sleekflowCompanyId,
            whatsappCloudApiConfigs,
            new List<Dictionary<string, object>>(),
            new Dictionary<string, object>(),
            new List<Dictionary<string, object>>(),
            new Dictionary<string, object>(),
            sleekflowStaffId,
            "step-1",
            "dependency-workflow-id");

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(capturedWorkflow, Is.Not.Null);
            Assert.That(capturedWorkflow.SleekflowCompanyId, Is.EqualTo(sleekflowCompanyId));
        });

        // Verify repository was called to create workflow
        _mockWorkflowRepository.Verify(x => x.CreateAndGetAsync(It.IsAny<Workflow>(), sleekflowCompanyId, It.IsAny<CancellationToken>()), Times.Once);
    }
}

public class TypedCallStepArgsImpl : TypedCallStepArgs
{
    public override string StepCategory => string.Empty;
}
