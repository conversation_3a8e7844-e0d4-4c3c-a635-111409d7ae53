using Newtonsoft.Json;

namespace Sleekflow.Integrator.Zoho.Services.Models;

public class ZohoGetModulesOutput
{
    [JsonProperty("modules")]
    public List<ModuleInfo>? Modules { get; set; }

    public class ModuleInfo
    {
        [JsonProperty("api_name")]
        public string? ApiName { get; set; }

        [JsonProperty("module_name")]
        public string? ModuleName { get; set; }

        [JsonProperty("custom_module")]
        public bool? CustomModule { get; set; }

        [JsonProperty("plural_label")]
        public string? PluralLabel { get; set; }
    }
}