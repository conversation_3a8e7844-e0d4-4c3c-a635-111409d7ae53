using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.DurableTask.Client;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public static class PropagateBatchToProviderStart
{
    [Function("Hubspot_PropagateBatchToProvider_Start")]
    public static async Task OrchestrationTrigger(
        [TimerTrigger("0 */1 * * * *")]
        TimerInfo timerInfo,
        [DurableClient]
        DurableTaskClient starter)
    {
        var instanceId = "StaticId";

        var durableOrchestrationStatus = await starter.GetInstanceAsync(instanceId);
        if (durableOrchestrationStatus == null ||
            durableOrchestrationStatus.RuntimeStatus == OrchestrationRuntimeStatus.Failed ||
            durableOrchestrationStatus.RuntimeStatus == OrchestrationRuntimeStatus.Terminated ||
            durableOrchestrationStatus.RuntimeStatus == OrchestrationRuntimeStatus.Canceled ||
            durableOrchestrationStatus.RuntimeStatus == OrchestrationRuntimeStatus.Completed)
        {
            await starter.ScheduleNewOrchestrationInstanceAsync(
                "Hu<PERSON>pot_PropagateBatchToProvider_Orchestrator",
                new StartOrchestrationOptions(instanceId));
        }
    }
}