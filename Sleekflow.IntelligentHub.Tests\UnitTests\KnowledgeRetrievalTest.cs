﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

public class KnowledgeRetrievalTest
{
    private Kernel _kernel;
    private IKnowledgePlugin _knowledgePlugin;

    [SetUp]
    public void SetUp()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _knowledgePlugin = scope.ServiceProvider.GetRequiredService<IKnowledgePlugin>();
    }

    public class ValidateQueries
    {
        public string Query { get; set; } = string.Empty;

        public bool ShouldMatch { get; set; } = true;
    }

    public class KnowledgeRetrievalTestCase : IHasSleekflowCompanyId
    {
        public string SleekflowCompanyId { get; set; } = string.Empty;

        public string BaseQuery { get; set; } = string.Empty;

        public ValidateQueries[] Queries { get; set; } = [];
    }

    private static IEnumerable<KnowledgeRetrievalTestCase> HkbnTestCases
    {
        get
        {
            yield return new KnowledgeRetrievalTestCase
            {
                SleekflowCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc",
                BaseQuery = "What are the discount policies for yearly subscriptions for the SleekFlow Plan?",
                Queries =
                [
                    new ValidateQueries
                    {
                        Query =
                            "Can you describe the discount options for annual SleekFlow subscriptions, including any percentage savings or qualifying terms?",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query =
                            "Tell me about the yearly subscription discounts for SleekFlow plans and their conditions.",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query = "What are the pricing details for monthly subscriptions?", ShouldMatch = false
                    },
                    new ValidateQueries
                    {
                        Query = "How much does SleekFlow cost per month?", ShouldMatch = false
                    }
                ]
            };

            yield return new KnowledgeRetrievalTestCase
            {
                SleekflowCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc",
                BaseQuery =
                    "Please provide a detailed breakdown of the HKBN x Evercare Home Care Plan pricing structure. Specifically: 1. What is included in the standard $399 per month charge? 2. Are there any additional costs for extra services (e.g., extended care hours, specialized equipment, or other add-ons)? 3. Are there tiered pricing options or conditions that might affect the charges? 4. Any other relevant details that would help clarify the 'exact charges' for the customer.",
                Queries =
                [
                    new ValidateQueries
                    {
                        Query =
                            "Could you please elaborate on the cost breakdown for the HKBN x Evercare Home Care Plan? I'd like to know: 1. What services are covered in the base $399 monthly fee? 2. Are there any additional fees for services beyond the standard package, such as longer care times, special equipment, or other extras? 3. Does the plan offer different pricing tiers, and what criteria determine these tiers? 4. Are there any other details about the costs that would help a customer understand the total charges?",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query =
                            "Can you give me a detailed explanation of how the HKBN x Evercare Home Care Plan is priced? I need to understand: 1. What is covered by the standard $399 monthly fee? 2. Are there any extra charges for additional services (like longer care or special equipment)? 3. Does the pricing change based on different levels of service or other factors? 4.  What other cost-related details should I know to understand the final charges?",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query =
                            "Explain the HKBN x Evercare Home Care Plan's pricing structure. I'm interested in: 1.  The specifics of what's included in the $399 monthly fee. 2. Any potential extra costs for things like extended hours or special equipment. 3. Whether there are different price levels and what determines them. 4. Any other pricing information that would help me understand the total cost.",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query =
                            "I need a complete breakdown of the HKBN x Evercare Home Care Plan's costs.  Specifically: 1. What is included in the standard monthly charge of $399? 2. What additional costs might I incur for extra services (like longer care visits, specialized equipment, etc.)? 3. Are there different pricing tiers or situations that would affect the final cost? 4. What other cost details should I be aware of?",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query =
                            "Regarding the HKBN x Evercare Home Care Plan, please clarify the 'exact charges' for the customer by detailing: 1. What is covered under the standard $399 monthly rate? 2. Are there any additional charges for add-on services such as extended care hours, special equipment, or other extras? 3. Are there any tiered pricing options or conditions that might affect the charges? 4. Any other relevant details?",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query = "What services are offered under HKBN x Evercare home care plan?", ShouldMatch = false
                    },
                    new ValidateQueries
                    {
                        Query = "How does HKBN collaborate with Evercare?", ShouldMatch = false
                    },
                    new ValidateQueries
                    {
                        Query = "What are the benefits of Bowtie and HKBN collaboration?", ShouldMatch = false
                    }
                ]
            };
            yield return new KnowledgeRetrievalTestCase
            {
                SleekflowCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc",
                BaseQuery =
                    "process for obtaining medicine after a video consultation through HKBN's healthcare partners.",
                Queries =
                [
                    new ValidateQueries
                    {
                        Query =
                            "How do I get my medication after a video consultation with a doctor through HKBN's healthcare plan?",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query =
                            "Explain the steps to receive medicine after a telehealth appointment via HKBN's medical partners.",
                        ShouldMatch = true
                    },
                    new ValidateQueries
                    {
                        Query = "Tell me about HKBN's healthcare service plans.", ShouldMatch = false
                    },
                    new ValidateQueries
                    {
                        Query = "What are the benefits of HKBN's healthcare plan?", ShouldMatch = false
                    },
                    new ValidateQueries
                    {
                        Query = "How do I book a video consultation with HKBN?", ShouldMatch = false
                    }
                ]
            };
        }
    }

    [TestCaseSource(nameof(HkbnTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public async Task QueryKnowledgeWithKnowledgeRetrievalCacheTest(KnowledgeRetrievalTestCase testCase)
    {
        var kernel = _kernel.Clone();
        kernel.Data.Add("SLEEKFLOW_COMPANY_ID", testCase.SleekflowCompanyId);

        TestContext.WriteLine("\n=== Test Results ===");
        TestContext.WriteLine($"Base Message: {testCase.BaseQuery}");
        var baseQueryKnowledgeResponse = await _knowledgePlugin.QueryKnowledgeAsync(kernel, testCase.BaseQuery);
        var baseResponse = baseQueryKnowledgeResponse.Knowledge;
        TestContext.WriteLine($"Base Response: \n {baseResponse}\n");

        foreach (var message in testCase.Queries)
        {
            TestContext.WriteLine($"Test Message: {message.Query}");
            var queryKnowledgeResponse = await _knowledgePlugin.QueryKnowledgeAsync(kernel, testCase.BaseQuery);
            var response = queryKnowledgeResponse.Knowledge;
            TestContext.WriteLine($"Test Response: \n {response}");

            if (message.ShouldMatch)
            {
                var matched = response.Equals(baseResponse);
                TestContext.WriteLine($"Expected Match: Yes");
                TestContext.WriteLine($"Actual Match: {matched}\n");
                Assert.That(
                    response,
                    Is.EqualTo(baseResponse),
                    $"Expected message '{message.Query}' to match base response.\nBase Response: {baseResponse}\nActual Response: {response}");
            }
            else
            {
                var matched = response.Equals(baseResponse);
                TestContext.WriteLine($"Expected Match: No");
                TestContext.WriteLine($"Actual Match: {matched}\n");
                Assert.That(
                    response,
                    Is.Not.EqualTo(baseResponse),
                    $"Expected message '{message.Query}' to NOT match base response.\nBase Response: {baseResponse}\nActual Response: {response}");
            }
        }
    }
}