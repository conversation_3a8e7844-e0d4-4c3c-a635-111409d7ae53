using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

public class EmployeeDetailsResponse
{
    [JsonProperty("currency")]
    public Currency? Currency { get; set; }

    [JsonProperty("custentity_cv_entity_ex_id")]
    public string? CustentityCvEntityExId { get; set; }

    [JsonProperty("customForm")]
    public CustomForm? CustomForm { get; set; }

    [JsonProperty("dateCreated")]
    public DateTime? DateCreated { get; set; }

    [JsonProperty("defaultexpensereportcurrency")]
    public DefaultExpenseReportCurrency? Defaultexpensereportcurrency { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("entityId")]
    public string? EntityId { get; set; }

    [JsonProperty("externalId")]
    public string? ExternalId { get; set; }

    [JsonProperty("firstName")]
    public string? FirstName { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("issalesrep")]
    public bool? Issalesrep { get; set; }

    [JsonProperty("lastName")]
    public string? LastName { get; set; }

    [JsonProperty("subsidiary")]
    public Subsidiary? Subsidiary { get; set; }

    [JsonProperty("supervisor")]
    public Supervisor? Supervisor { get; set; }

    [JsonConstructor]
    public EmployeeDetailsResponse(
        Currency? currency,
        string? custentityCvEntityExId,
        CustomForm? customForm,
        DateTime? dateCreated,
        DefaultExpenseReportCurrency? defaultexpensereportcurrency,
        string? email,
        string? entityId,
        string? externalId,
        string? firstName,
        string? id,
        bool? issalesrep,
        string? lastName,
        Subsidiary? subsidiary,
        Supervisor? supervisor)
    {
        Currency = currency;
        CustentityCvEntityExId = custentityCvEntityExId;
        CustomForm = customForm;
        DateCreated = dateCreated;
        Defaultexpensereportcurrency = defaultexpensereportcurrency;
        Email = email;
        EntityId = entityId;
        ExternalId = externalId;
        FirstName = firstName;
        Id = id;
        Issalesrep = issalesrep;
        LastName = lastName;
        Subsidiary = subsidiary;
        Supervisor = supervisor;
    }
}

public class CustomForm
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("refName")]
    public string RefName { get; set; }

    [JsonConstructor]
    public CustomForm(string id, string refName)
    {
        Id = id;
        RefName = refName;
    }
}

public class Supervisor
{
    [JsonProperty("links")]
    public List<Link> Links { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("refName")]
    public string RefName { get; set; }

    [JsonConstructor]
    public Supervisor(List<Link> links, string id, string refName)
    {
        Links = links;
        Id = id;
        RefName = refName;
    }
}