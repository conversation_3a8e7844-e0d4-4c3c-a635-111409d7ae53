using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Evaluations;

[SwaggerInclude]
public class ContactConversation
{
    [Required]
    [JsonProperty("conversation_status")]
    public string ConversationStatus { get; set; }

    [Required]
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [JsonProperty("last_message_channel")]
    public string? LastMessageChannel { get; set; }

    [JsonProperty("last_message_channel_id")]
    public string? LastMessageChannelId { get; set; }

    [JsonConstructor]
    public ContactConversation(
        string conversationStatus,
        string conversationId,
        string? lastMessageChannel,
        string? lastMessageChannelId)
    {
        ConversationStatus = conversationStatus;
        ConversationId = conversationId;
        LastMessageChannel = lastMessageChannel;
        LastMessageChannelId = lastMessageChannelId;
    }
}