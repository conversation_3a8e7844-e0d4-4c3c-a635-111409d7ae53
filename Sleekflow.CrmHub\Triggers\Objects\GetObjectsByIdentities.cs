﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetObjectsByIdentities : ITrigger
{
    private readonly IEntityService _entityService;

    public GetObjectsByIdentities(
        IEntityService entityService)
    {
        _entityService = entityService;
    }

    public class GetObjectsByIdentitiesInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("identities")]
        public List<Identity> Identities { get; set; }

        [Required]
        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [Required]
        [JsonProperty("sorts")]
        public List<EntityQueryBuilder.Sort> Sorts { get; set; }

        [JsonConstructor]
        public GetObjectsByIdentitiesInput(
            string sleekflowCompanyId,
            List<Identity> identities,
            string entityTypeName,
            List<EntityQueryBuilder.Sort> sorts)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Identities = identities;
            EntityTypeName = entityTypeName;
            Sorts = sorts;
        }
    }

    public class GetObjectsByIdentitiesOutput
    {
        [JsonProperty("records")]
        public List<CrmHubEntity> Records { get; set; }

        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetObjectsByIdentitiesOutput(
            List<CrmHubEntity> records,
            long count)
        {
            Records = records;
            Count = count;
        }
    }

    public async Task<GetObjectsByIdentitiesOutput> F(
        GetObjectsByIdentitiesInput getObjectsByIdentitiesInput)
    {
        var rawRecords = await _entityService.GetObjectsByIdentitiesAsync(
            getObjectsByIdentitiesInput.Identities,
            getObjectsByIdentitiesInput.SleekflowCompanyId,
            getObjectsByIdentitiesInput.EntityTypeName);

        var records = rawRecords
            .Select(e => EntityService.Sanitize(e))
            .ToList();

        return new GetObjectsByIdentitiesOutput(records, records.Count);
    }
}