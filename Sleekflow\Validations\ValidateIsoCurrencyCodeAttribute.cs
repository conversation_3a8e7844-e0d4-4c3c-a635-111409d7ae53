using System.ComponentModel.DataAnnotations;
using Sleekflow.Utils;

namespace Sleekflow.Validations;

public class ValidateIsoCurrencyCodeAttribute : ValidationAttribute
{
    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value == null)
        {
            throw new ArgumentException($"Invalid field input {value}");
        }

        if (value is not string str)
        {
            return ValidationResult.Success;
        }

        var regionInfo = CultureUtils.GetRegionInfoByCurrencyIsoCode(str);
        if (regionInfo == null)
        {
            return new ValidationResult(
                "Currency Iso Code must be a valid currency code",
                new string[]
                {
                    validationContext.MemberName!
                });
        }

        return ValidationResult.Success;
    }
}