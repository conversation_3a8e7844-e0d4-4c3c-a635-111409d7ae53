using MassTransit;
using Sleekflow.EmailHub.Models.Outlook.Events;
using Sleekflow.EmailHub.Outlook.Communications;
using Sleekflow.EmailHub.Outlook.Subscriptions;
using Sleekflow.Locks;

namespace Sleekflow.EmailHub.Outlook.Consumers;

public class OnOutlookChangeNotificationReceivedEventConsumerDefinition : ConsumerDefinition<OnOutlookChangeNotificationReceivedEventConsumer>
{
    public const int LockDuration = 2;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnOutlookChangeNotificationReceivedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromSeconds(15);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromSeconds(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnOutlookChangeNotificationReceivedEventConsumer : IConsumer<OnOutlookChangeNotificationReceivedEvent>
{
    private readonly IOutlookCommunicationService _outlookCommunicationService;
    private readonly ILockService _lockService;
    private readonly ILogger<OnOutlookChangeNotificationReceivedEventConsumer> _logger;
    private readonly IOutlookSubscriptionService _outlookSubscriptionService;

    public OnOutlookChangeNotificationReceivedEventConsumer(
        IOutlookCommunicationService outlookCommunicationService,
        ILogger<OnOutlookChangeNotificationReceivedEventConsumer> logger,
        ILockService lockService,
        IOutlookSubscriptionService outlookSubscriptionService)
    {
        _outlookCommunicationService = outlookCommunicationService;
        _logger = logger;
        _lockService = lockService;
        _outlookSubscriptionService = outlookSubscriptionService;
    }

    public async Task Consume(ConsumeContext<OnOutlookChangeNotificationReceivedEvent> context)
    {
        var notification = context.Message.OutlookChangeNotification;
        var cancellationToken = context.CancellationToken;
        var @lock = await _lockService.LockAsync(
            new[]
            {
                $"{notification.ResourceData.Id}",
                $"{notification.ChangeType}",
                "OutlookHandleNotificationLock"
            },
            TimeSpan.FromSeconds(
                OnOutlookChangeNotificationReceivedEventConsumerDefinition.LockDuration),
            cancellationToken);
        if (@lock is null)
        {
            return;
        }

        var (_, sleekflowCompanyId, emailAddress) = await _outlookSubscriptionService.GetSubscriptionByOutlookSubscriptionIdAsync(
            notification.SubscriptionId,
            notification.ClientState,
            cancellationToken);
        try
        {
            var emailMetadata = new Dictionary<string, string>
            {
                { "changeType", notification.ChangeType },
                { "msgId", notification.ResourceData.Id }
            };
            await _outlookCommunicationService.OnReceiveEmailAsync(
                new List<string> { sleekflowCompanyId },
                emailAddress,
                emailMetadata,
                cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogInformation(
                "encounter error while handling {changeType}: outlookSubscriptionId {id} :ex: {e}",
                notification.ChangeType,
                notification.SubscriptionId,
                e);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}