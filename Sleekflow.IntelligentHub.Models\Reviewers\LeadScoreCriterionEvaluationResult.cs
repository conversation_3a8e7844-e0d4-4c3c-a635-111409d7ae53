using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Reviewers;

public class LeadScoreCriterionEvaluationResult
{
    [JsonProperty("criterion_scores")]
    public List<CriterionScore> CriterionScores { get; set; } = new();

    [JsonProperty("overall_reason")]
    public string OverallReason { get; set; } = string.Empty;
}

public class CriterionScore
{
    [JsonProperty("criterion_index")]
    public int CriterionIndex { get; set; }

    [JsonProperty("score")]
    public int Score { get; set; }

    [JsonProperty("reason")]
    public string Reason { get; set; } = string.Empty;
}