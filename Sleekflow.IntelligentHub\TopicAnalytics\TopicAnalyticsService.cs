using Microsoft.Azure.Cosmos;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.TopicAnalytics;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.TopicAnalytics;

public interface ITopicAnalyticsService
{
    Task<TopicAnalyticsTopic> GetTopicAnalyticsTopic(
        string sleekflowCompanyId,
        string id,
        CancellationToken cancellationToken = default);

    Task<List<TopicAnalyticsTopic>> GetTopicAnalyticsTopics(
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default);

    Task<TopicAnalyticsTopic> CreateTopicAnalyticsTopic(
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        string topicName,
        List<TopicAnalyticsTerm> terms,
        CancellationToken cancellationToken = default);

    Task<TopicAnalyticsTopic> UpdateTopicAnalyticsTopic(
        string sleekflowCompanyId,
        string id,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        Dictionary<string, object?> updatedProperties,
        CancellationToken cancellationToken = default);

    Task<List<TopicAnalyticsTopic>> UpdateTopicAnalyticsTopics(
        string sleekflowCompanyId,
        Dictionary<string, TopicAnalyticsTopic?> updatedTopics,
        CancellationToken cancellationToken = default);

    Task DeleteTopicAnalyticsTopics(
        string sleekflowCompanyId,
        List<string> ids,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        CancellationToken cancellationToken = default);
}

public class TopicAnalyticsService
    : ITopicAnalyticsService, IScopedService
{
    private readonly ITopicAnalyticsTopicRepository _topicRepository;
    private readonly IIdService _idService;
    private readonly ILogger<TopicAnalyticsService> _logger;

    public TopicAnalyticsService(
        ITopicAnalyticsTopicRepository topicRepository,
        IIdService idService,
        ILogger<TopicAnalyticsService> logger)
    {
        _topicRepository = topicRepository;
        _idService = idService;
        _logger = logger;
    }

    public async Task<TopicAnalyticsTopic> GetTopicAnalyticsTopic(
        string sleekflowCompanyId,
        string id,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "[GetTopicAnalyticsTopic] Retrieving topic. Topic Id: {id}, CompanyId: {sleekflowCompanyId}",
            id,
            sleekflowCompanyId);
        var topic = await _topicRepository.GetAsync(id, sleekflowCompanyId, cancellationToken: cancellationToken);

        if (topic is not null && !topic.RecordStatuses.Contains(RecordStatuses.Active) && topic.RecordStatuses.Any())
        {
            throw new SfNotFoundObjectException($"{topic.Id} was deleted");
        }

        return topic!;
    }

    public async Task<List<TopicAnalyticsTopic>> GetTopicAnalyticsTopics(
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "[GetTopicAnalyticsTopics] Retrieving all topics. CompanyId: {sleekflowCompanyId}",
            sleekflowCompanyId);
        return await _topicRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId && (x.RecordStatuses.Contains(RecordStatuses.Active) || !x.RecordStatuses.Any()),
            cancellationToken: cancellationToken);
    }

    public async Task<TopicAnalyticsTopic> CreateTopicAnalyticsTopic(
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        string topicName,
        List<TopicAnalyticsTerm> terms,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "[CreateTopicAnalyticsTopic] Creating topic. Topic Name: {topicName}, CompanyId: {sleekflowCompanyId}, StaffId: {sleekflowStaffId}",
            topicName,
            sleekflowCompanyId,
            sleekflowStaffId);

        var topic = new TopicAnalyticsTopic(
            _idService.GetId("TopicAnalyticsTopic"),
            sleekflowCompanyId,
            topicName,
            terms,
            DateTime.UtcNow,
            DateTime.UtcNow,
            new List<string>
            {
                RecordStatuses.Active
            },
            new Dictionary<string, object?>(),
            createdBy: new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowStaffTeamIds),
            updatedBy: new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowStaffTeamIds)
        );
        await CheckTermExisted(sleekflowCompanyId, topic, cancellationToken);
        var result = await _topicRepository.CreateAndGetAsync(
            topic,
            sleekflowCompanyId,
            cancellationToken: cancellationToken);

        _logger.LogInformation(
            "[CreateTopicAnalyticsTopic] Topic created successfully. Topic Id: {id}, Name: {name}, CompanyId: {sleekflowCompanyId}",
            result.Id,
            result.Name,
            sleekflowCompanyId);

        return result;
    }

    public async Task<TopicAnalyticsTopic> UpdateTopicAnalyticsTopic(
        string sleekflowCompanyId,
        string id,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        Dictionary<string, object?> updatedProperties,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "[UpdateTopicAnalyticsTopic] Updating topic. Topic Id: {id}, CompanyId: {sleekflowCompanyId}, StaffId: {sleekflowStaffId}",
            id,
            sleekflowCompanyId,
            sleekflowStaffId);

        var topic = await GetTopicAnalyticsTopic(sleekflowCompanyId, id, cancellationToken);



        foreach (KeyValuePair<string, object?> entry in updatedProperties)
        {
            switch (entry.Key)
            {
                case "name":
                    var name = entry.Value as string ?? throw new Exception("Invalid type");
                    topic.Name = name;
                    break;

                case "terms":
                    var updatedTerms = ObjectUtils.ConvertTo<List<TopicAnalyticsTerm>>(entry.Value) ??
                                       new List<TopicAnalyticsTerm>();
                    topic.Terms = updatedTerms;
                    await CheckTermExisted(sleekflowCompanyId, topic, cancellationToken);
                    break;

                default:
                    throw new InvalidOperationException("Key was not found.");
            }
        }

        topic.UpdatedBy = new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowStaffTeamIds);
        topic.UpdatedAt = DateTime.UtcNow;

        var result = await _topicRepository.UpsertAndGetAsync(
            topic,
            sleekflowCompanyId,
            cancellationToken: cancellationToken);

        _logger.LogInformation(
            "[UpdateTopicAnalyticsTopic] Topic updated successfully. Topic Id: {id}, Name: {name}, CompanyId: {sleekflowCompanyId}",
            result.Id,
            result.Name,
            sleekflowCompanyId);

        return result;
    }

    public async Task<List<TopicAnalyticsTopic>> UpdateTopicAnalyticsTopics(
        string sleekflowCompanyId,
        Dictionary<string, TopicAnalyticsTopic?> updatedTopics,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "[UpdateTopicAnalyticsTopics] Updating multiple topics. Count: {count}, CompanyId: {sleekflowCompanyId}",
            updatedTopics.Count,
            sleekflowCompanyId);

        var topics = await GetTopicAnalyticsTopics(sleekflowCompanyId, cancellationToken);
        foreach (KeyValuePair<string, TopicAnalyticsTopic?> entry in updatedTopics)
        {
            var topic = updatedTopics[entry.Key];
            if (topic == null)
            {
                await DeleteTopicAnalyticsTopics(
                    sleekflowCompanyId,
                    new List<string>()
                    {
                        entry.Key
                    },
                    null, // sleekflowStaffId
                    null, // sleekflowStaffTeamIds
                    cancellationToken);

                topics.RemoveAll(x => x.Id == entry.Key);
            }
            else
            {
                await _topicRepository.UpsertAsync(topic, sleekflowCompanyId, cancellationToken: cancellationToken);
            }
        }

        topics = await GetTopicAnalyticsTopics(sleekflowCompanyId, cancellationToken);

        _logger.LogInformation(
            "[UpdateTopicAnalyticsTopics] Multiple topics updated successfully. Count: {count}, CompanyId: {sleekflowCompanyId}",
            topics.Count,
            sleekflowCompanyId);

        return topics;
    }

    public async Task DeleteTopicAnalyticsTopics(
        string sleekflowCompanyId,
        List<string> ids,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "[DeleteTopicAnalyticsTopics] Deleting topics. Count: {count}, CompanyId: {sleekflowCompanyId}, StaffId: {sleekflowStaffId}",
            ids.Count,
            sleekflowCompanyId,
            sleekflowStaffId);

        var now = DateTimeOffset.UtcNow;
        var staff = new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowStaffTeamIds);
        foreach (var id in ids)
        {
            await _topicRepository.PatchAsync(
                id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set(
                        $"/{IHasRecordStatuses.PropertyNameRecordStatuses}",
                        new List<string>
                        {
                            RecordStatuses.Deleted
                        }),
                    PatchOperation.Set(
                        $"/{AuditEntity.PropertyNameUpdatedBy}",
                        staff),
                    PatchOperation.Set(
                        $"/{IHasUpdatedAt.PropertyNameUpdatedAt}",
                        now)
                },
                cancellationToken: cancellationToken);

            _logger.LogInformation(
                "[DeleteTopic] Topic deleted successfully. Topic Id: {id}, CompanyId: {sleekflowCompanyId}",
                id,
                sleekflowCompanyId);
        }
    }

    public async Task CheckTermExisted(
        string sleekflowCompanyId,
        TopicAnalyticsTopic topic,
        CancellationToken cancellationToken = default)
    {
        var existTopics =
            (await GetTopicAnalyticsTopics(sleekflowCompanyId, cancellationToken)).Where(x => x.Id != topic.Id);
        var allTerms = new HashSet<string>(existTopics.SelectMany(t => t.Terms).Select(term => term.Text));

        foreach (var term in topic.Terms)
        {
            if (allTerms.Contains(term.Text.ToLower()))
            {
                throw new SfQueryException($"Term '{term.Text}' already exists in another topic.", string.Empty);
            }
        }
    }
}