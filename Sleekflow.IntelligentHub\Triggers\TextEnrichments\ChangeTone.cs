﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.TextEnrichments;

[TriggerGroup(ControllerNames.TextEnrichments)]
public class ChangeTone : ITrigger<ChangeTone.ChangeToneInput, ChangeTone.ChangeToneOutput>
{
    private readonly ITextToneChangeService _textToneChangeService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public ChangeTone(
        ITextToneChangeService textToneChangeService,
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService)
    {
        _textToneChangeService = textToneChangeService;
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    public class ChangeToneInput : IValidatableObject, IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("message")]
        public string Message { get; set; }

        [Required]
        [JsonProperty("tone_type")]
        public string ToneType { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IntelligentHubUsageFilter.ModelNameIntelligentHubUsageFilter)]
        [Validations.ValidateObject]
        public IntelligentHubUsageFilter? IntelligentHubUsageFilter { get; set; }

        [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
        [Validations.ValidateObject]
        public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

        [JsonConstructor]
        public ChangeToneInput(
            string message,
            string toneType,
            string sleekflowCompanyId,
            IntelligentHubUsageFilter? intelligentHubUsageFilter,
            AuditEntity.SleekflowStaff? createdBy)
        {
            Message = message;
            ToneType = toneType;
            SleekflowCompanyId = sleekflowCompanyId;
            IntelligentHubUsageFilter = intelligentHubUsageFilter;
            CreatedBy = createdBy;
        }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var validationResults = new List<ValidationResult>();

            if (!TargetToneTypes.AllToneTypes.Contains(ToneType))
            {
                validationResults.Add(
                    new ValidationResult(
                        "The Tone Type is not supported.",
                        new List<string>
                        {
                            nameof(ToneType)
                        }));
            }

            return validationResults;
        }
    }

    public class ChangeToneOutput
    {
        [JsonProperty("output_message")]
        public string OutputMessage { get; set; }

        [JsonConstructor]
        public ChangeToneOutput(string outputMessage)
        {
            OutputMessage = outputMessage;
        }
    }

    public async Task<ChangeToneOutput> F(ChangeToneInput changeToneInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(changeToneInput.SleekflowCompanyId);

        var isUsageLimitExceeded = intelligentHubConfig == null || await _intelligentHubUsageService.IsUsageLimitExceeded(
            changeToneInput.SleekflowCompanyId,
            new Dictionary<string, int>
            {
                {
                    PriceableFeatures.AiFeaturesTotalUsage,
                    _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                        intelligentHubConfig,
                        PriceableFeatures.AiFeaturesTotalUsage)
                }
            },
            changeToneInput.IntelligentHubUsageFilter);

        if (isUsageLimitExceeded)
        {
            throw new SfUserFriendlyException("Cannot find IntelligentHubConfig or exceed max usage limit.");
        }

        var outputMessage = await _textToneChangeService.ChangeToneAsync(
            changeToneInput.Message,
            changeToneInput.ToneType);

        await _intelligentHubUsageService.RecordUsageAsync(
            changeToneInput.SleekflowCompanyId,
            PriceableFeatures.ChangeTone,
            changeToneInput.CreatedBy,
            new ChangeToneSnapshot(
                changeToneInput.Message,
                changeToneInput.ToneType,
                outputMessage));

        return new ChangeToneOutput(outputMessage);
    }
}