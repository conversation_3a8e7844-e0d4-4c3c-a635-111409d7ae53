using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Common;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class WaitForEventV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sys.v2.wait-for-event";

    [Required]
    [JsonProperty("event_name")]
    public string EventName { get; set; }

    [JsonProperty("condition__expr")]
    public string? ConditionExpr { get; set; }

    [JsonProperty("time_unit")]
    public string? TimeUnit { get; set; }

    [JsonProperty("timeout_units__expr")]
    public string? TimeoutUnitsExpr { get; set; }

    [JsonProperty("condition_criteria")]
    public List<ConditionCriterion>? ConditionCriteria { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => GetStepCategory(EventName);

    [JsonConstructor]
    public WaitForEventV2StepArgs(
        string eventName,
        string? conditionExpr,
        string? timeUnit,
        string? timeoutUnitsExpr,
        List<ConditionCriterion>? conditionCriteria)
    {
        EventName = eventName;
        ConditionExpr = conditionExpr;
        TimeUnit = timeUnit;
        TimeoutUnitsExpr = timeoutUnitsExpr;
        ConditionCriteria = conditionCriteria;
    }

    private static string GetStepCategory(string eventName)
    {
        return eventName switch
        {
            EventNames.OnMessageReceived => WorkflowStepCategories.Messaging,
            _ => string.Empty
        };
    }
}