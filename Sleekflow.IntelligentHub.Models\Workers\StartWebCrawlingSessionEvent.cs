using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Workers;

public class StartWebCrawlingSessionEvent : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("url")]
    public string Url { get; set; }

    [Required]
    [JsonProperty("session_id")]
    public string SessionId { get; set; }

    [JsonConstructor]
    public StartWebCrawlingSessionEvent(
        string sleekflowCompanyId,
        string url,
        string sessionId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Url = url;
        SessionId = sessionId;
    }
}