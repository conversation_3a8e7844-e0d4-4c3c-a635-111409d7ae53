using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.WebPageDocuments;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Documents.WebPageDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class PopulateWebPageDocumentFromBlob
    : ITrigger<
        PopulateWebPageDocumentFromBlob.PopulateWebPageDocumentFromBlobInput,
        PopulateWebPageDocumentFromBlob.PopulateWebPageDocumentFromBlobOutput>
{
    private readonly IDocumentProcessingService _documentProcessingService;

    public PopulateWebPageDocumentFromBlob(IDocumentProcessingService documentProcessingService)
    {
        _documentProcessingService = documentProcessingService;
    }

    public class PopulateWebPageDocumentFromBlobInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebPageDocument.PropertyNameBlobId)]
        public string BlobId { get; set; }

        [Required]
        [JsonProperty(WebPageDocument.PropertyNameBlobType)]
        public string BlobType { get; set; }

        [Required]
        [JsonProperty(WebPageDocument.PropertyNamePageUri)]
        public string PageUri { get; set; }

        [Required]
        [JsonProperty(WebPageDocument.PropertyNameLanguageIsoCode)]
        public string Language { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("operations")]
        public List<string> Operations { get; set; }

        [JsonConstructor]
        public PopulateWebPageDocumentFromBlobInput(
            string sleekflowCompanyId,
            string blobId,
            string blobType,
            string pageUri,
            string language,
            List<string> operations)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
            BlobType = blobType;
            PageUri = pageUri;
            Language = language;
            Operations = operations;
        }
    }

    public class PopulateWebPageDocumentFromBlobOutput
    {
        [JsonConstructor]
        public PopulateWebPageDocumentFromBlobOutput()
        {
        }
    }

    public async Task<PopulateWebPageDocumentFromBlobOutput> F(
        PopulateWebPageDocumentFromBlobInput populateWebPageDocumentFromBlobInput)
    {
        await _documentProcessingService.ProcessWebPageDocument(
            populateWebPageDocumentFromBlobInput.SleekflowCompanyId,
            populateWebPageDocumentFromBlobInput.BlobId,
            populateWebPageDocumentFromBlobInput.BlobType,
            populateWebPageDocumentFromBlobInput.PageUri,
            populateWebPageDocumentFromBlobInput.Language,
            populateWebPageDocumentFromBlobInput.Operations);

        return new PopulateWebPageDocumentFromBlobOutput();
    }
}