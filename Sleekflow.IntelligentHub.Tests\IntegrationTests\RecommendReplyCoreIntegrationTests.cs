using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Triggers.RecommendedReplies;
using Sleekflow.Models.Chats;
using Sleekflow.Outputs;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class RecommendReplyCoreIntegrationTests
{
    private const string MockCompanyId = "sample-company-id";
    private readonly IntelligentHubUsageFilter _intelligentHubUsageFilter;
    private readonly AuditEntity.SleekflowStaff _sleekflowStaff;

    public RecommendReplyCoreIntegrationTests()
    {
        var utcNow = DateTimeOffset.UtcNow;
        _intelligentHubUsageFilter = new IntelligentHubUsageFilter(
            new DateTimeOffset(utcNow.Year, utcNow.Month, 1, 0, 0, 0, TimeSpan.Zero),
            new DateTimeOffset(
                utcNow.Year,
                utcNow.Month,
                DateTime.DaysInMonth(utcNow.Year, utcNow.Month),
                23,
                59,
                59,
                TimeSpan.Zero));
        _sleekflowStaff = new AuditEntity.SleekflowStaff(
            "3880",
            new List<string>
            {
                "233", "282"
            });
    }

    [SetUp]
    public void TestSetUp()
    {
    }

    [Test]
    public async Task RecommendReplyTest()
    {
        var sfChatTest1Entry1 = new SfChatEntry()
        {
            User = "Hello, what does your company do?", Bot = "Hi, we are hong kong sue yan university",
        };
        var sfChatTest1Entry2 = new SfChatEntry()
        {
            User = "Ok, can you provide the course lists that you offer?",
        };

        var recommendReplyInput1 =
            new Triggers.RecommendedReplies.RecommendReply.RecommendReplyInput(
                new List<SfChatEntry>()
                {
                    sfChatTest1Entry1, sfChatTest1Entry2,
                },
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var recommendReplyScenarioResult1 = await Application.InMemoryBusHost.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(recommendReplyInput1).ToUrl("/RecommendedReplies/RecommendReply");
            });
        var recommendReplyOutput1 =
            await recommendReplyScenarioResult1.ReadAsJsonAsync<
                Output<RecommendReply.RecommendReplyOutput>>();

        Assert.That(recommendReplyOutput1, Is.Not.Null);
        Assert.That(recommendReplyOutput1!.HttpStatusCode, Is.EqualTo(200));

        var sfChatTest2Entry1 = new SfChatEntry()
        {
            User = "Hello, are you a legit school?",
        };
        var sfChatTest2Entry2 = new SfChatEntry()
        {
            User = "I'm deciding which school to attend, can you explain why would I choose you over CUHK?",
        };

        var recommendReplyInput2 =
            new Triggers.RecommendedReplies.RecommendReply.RecommendReplyInput(
                new List<SfChatEntry>()
                {
                    sfChatTest2Entry1, sfChatTest2Entry2,
                },
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var recommendReplyScenarioResult2 = await Application.InMemoryBusHost.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(recommendReplyInput2).ToUrl("/RecommendedReplies/RecommendReply");
            });
        var recommendReplyOutput2 =
            await recommendReplyScenarioResult2.ReadAsJsonAsync<
                Output<Triggers.RecommendedReplies.RecommendReply.RecommendReplyOutput>>();

        Assert.That(recommendReplyOutput2, Is.Not.Null);
        Assert.That(recommendReplyOutput2!.HttpStatusCode, Is.EqualTo(200));


        var sfChatTest3Entry1 = new SfChatEntry()
        {
            User = "Hello, whats your tuition fee?", Bot = "Hi, it's $100000 per year",
        };
        var sfChatTest3Entry2 = new SfChatEntry()
        {
            User = "Why so expensive?", Bot = "We offer best quality education",
        };

        var sfChatTest3Entry3 = new SfChatEntry()
        {
            User = "Too expensive, I'm quitting",
        };

        var recommendReplyInput3 =
            new Triggers.RecommendedReplies.RecommendReply.RecommendReplyInput(
                new List<SfChatEntry>()
                {
                    sfChatTest3Entry1, sfChatTest3Entry2, sfChatTest3Entry3
                },
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var recommendReplyScenarioResult3 = await Application.InMemoryBusHost.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(recommendReplyInput3).ToUrl("/RecommendedReplies/RecommendReply");
            });
        var recommendReplyOutput3 =
            await recommendReplyScenarioResult3.ReadAsJsonAsync<
                Output<Triggers.RecommendedReplies.RecommendReply.RecommendReplyOutput>>();

        Assert.That(recommendReplyOutput3, Is.Not.Null);
        Assert.That(recommendReplyOutput3!.HttpStatusCode, Is.EqualTo(200));


        var sfChatTest4Entry1 = new SfChatEntry()
        {
            User = "你好，你是什么机构?", Bot = "我们是香港樹仁⼤學",
        };
        var sfChatTest4Entry2 = new SfChatEntry()
        {
            User = "你们做什么?", Bot = "我们有专业的培训课程",
        };

        var sfChatTest4Entry3 = new SfChatEntry()
        {
            User = "给我看看你们的课程计划",
        };

        var recommendReplyInput4 =
            new Triggers.RecommendedReplies.RecommendReply.RecommendReplyInput(
                new List<SfChatEntry>()
                {
                    sfChatTest4Entry1, sfChatTest4Entry2, sfChatTest4Entry3
                },
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var recommendReplyScenarioResult4 = await Application.InMemoryBusHost.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(recommendReplyInput4).ToUrl("/RecommendedReplies/RecommendReply");
            });
        var recommendReplyOutput4 =
            await recommendReplyScenarioResult4.ReadAsJsonAsync<
                Output<Triggers.RecommendedReplies.RecommendReply.RecommendReplyOutput>>();

        Assert.That(recommendReplyOutput4, Is.Not.Null);
        Assert.That(recommendReplyOutput4!.HttpStatusCode, Is.EqualTo(200));
    }
}