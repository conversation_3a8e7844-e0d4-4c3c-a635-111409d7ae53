<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.23.0" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.DurableTask" Version="1.2.2" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.Extensions.Http.AspNetCore" Version="2.0.1" />
        <PackageReference Include="Microsoft.Azure.Functions.Worker.ApplicationInsights" Version="2.0.0" />
        <PackageReference Include="Microsoft.Extensions.Configuration.AzureAppConfiguration" Version="5.2.0" />

        <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />

        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="Serilog" Version="4.0.1" />
        <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
        <PackageReference Include="Serilog.Enrichers.Span" Version="3.1.0" />
        <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.Async" Version="2.0.0" />
        <PackageReference Include="Serilog.Sinks.AzureAnalytics" Version="5.0.0" />
        <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="8.0.2" />
        <PackageReference Include="Serilog.Sinks.Map" Version="2.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.7.0" />
    </ItemGroup>

    <ItemGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <PackageReference Include="Microsoft.OpenApi" Version="1.6.17" />
        <PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.7.0" />
    </ItemGroup>

    <ItemGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <PackageReference Include="Moq" Version="4.18.1" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow\Sleekflow.csproj" />
    </ItemGroup>

</Project>
