﻿using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.Datasets;

public class WabaDataset
{
    public const string PropertyNameCreatedAt = "created_at";
    public const string PropertyNameUpdatedAt = "updated_at";

    [JsonProperty("facebook_dataset_id")]
    public string FacebookDatasetId { get; set; }

    [JsonProperty("facebook_dataset_name")]
    public string? FacebookDatasetName { get; set; }

    [JsonProperty(PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public WabaDataset(string facebookDatasetId, string? facebookDatasetName, DateTimeOffset createdAt, DateTimeOffset updatedAt)
    {
        FacebookDatasetId = facebookDatasetId;
        FacebookDatasetName = facebookDatasetName;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}