using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Webhooks;

[<PERSON><PERSON><PERSON>(typeof(IDbContainerResolver))]
[DatabaseId("db")]
[ContainerId("webhook")]
public class WebhookEvent : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("created_time")]
    public DateTimeOffset CreatedTime { get; set; }

    [JsonProperty("webhook")]
    public Webhook Webhook { get; set; }

    [JsonProperty("request_body")]
    public object? RequestBody { get; set; }

    [JsonProperty("response")]
    public WebhookEventResponse? Response { get; set; }

    [JsonConstructor]
    public WebhookEvent(
        string id,
        string sleekflowCompanyId,
        DateTimeOffset createdTime,
        Webhook webhook,
        object? requestBody,
        int? ttl)
        : base(id, "WebhookEvent", ttl)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        this.CreatedTime = createdTime;
        Webhook = webhook;
        RequestBody = requestBody;
        Response = new WebhookEventResponse();
    }
}

public class WebhookEventResponse
{
    [JsonProperty("status_code")]
    public int? StatusCode { get; set; }
}