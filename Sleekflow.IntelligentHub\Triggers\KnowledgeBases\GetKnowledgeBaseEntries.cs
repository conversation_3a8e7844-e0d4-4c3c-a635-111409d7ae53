﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.KnowledgeBases;

[TriggerGroup(ControllerNames.KnowledgeBases)]
public class GetKnowledgeBaseEntries
    : ITrigger<
        GetKnowledgeBaseEntries.GetKnowledgeBaseEntriesInput,
        GetKnowledgeBaseEntries.GetKnowledgeBaseEntriesOutput>
{
    private readonly IKnowledgeBaseEntryService _knowledgeBaseEntryService;

    public GetKnowledgeBaseEntries(IKnowledgeBaseEntryService knowledgeBaseEntryService)
    {
        _knowledgeBaseEntryService = knowledgeBaseEntryService;
    }

    public class GetKnowledgeBaseEntriesInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("filters")]
        public GetKnowledgeBaseEntriesFilters Filters { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseEntriesInput(
            string sleekflowCompanyId,
            GetKnowledgeBaseEntriesFilters filters,
            int limit,
            string? continuationToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Filters = filters;
            Limit = limit;
            ContinuationToken = continuationToken;
        }
    }

    public class GetKnowledgeBaseEntriesOutput
    {
        [JsonProperty("knowledge_base_entries")]
        public List<KnowledgeBaseEntry> KnowledgeBaseEntries { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseEntriesOutput(
            List<KnowledgeBaseEntry> knowledgeBaseEntries,
            string? nextContinuationToken)
        {
            KnowledgeBaseEntries = knowledgeBaseEntries;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetKnowledgeBaseEntriesOutput> F(GetKnowledgeBaseEntriesInput getKnowledgeBaseEntriesInput)
    {
        var (entries, nextContinuationToken) = await _knowledgeBaseEntryService.GetKnowledgeBaseEntriesAsync(
            getKnowledgeBaseEntriesInput.SleekflowCompanyId,
            getKnowledgeBaseEntriesInput.Filters,
            getKnowledgeBaseEntriesInput.ContinuationToken,
            getKnowledgeBaseEntriesInput.Limit);

        return new GetKnowledgeBaseEntriesOutput(entries, nextContinuationToken);
    }
}