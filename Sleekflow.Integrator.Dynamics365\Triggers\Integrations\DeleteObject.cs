using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Integrator.Dynamics365.Objects;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeleteObject : ITrigger
{
    private readonly IDynamics365ObjectService _dynamics365ObjectService;
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;

    public DeleteObject(
        IDynamics365ObjectService dynamics365ObjectService,
        IDynamics365AuthenticationService dynamics365AuthenticationService)
    {
        _dynamics365ObjectService = dynamics365ObjectService;
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
    }

    public class DeleteObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public DeleteObjectInput(
            string sleekflowCompanyId,
            string objectId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            EntityTypeName = entityTypeName;
        }
    }

    public class DeleteObjectOutput
    {
    }

    public async Task<DeleteObjectOutput> F(DeleteObjectInput deleteObjectInput)
    {
        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(deleteObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        await _dynamics365ObjectService.DeleteAsync(
            authentication,
            deleteObjectInput.ObjectId,
            deleteObjectInput.EntityTypeName);

        return new DeleteObjectOutput();
    }
}