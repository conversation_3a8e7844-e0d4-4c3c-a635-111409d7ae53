using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class ReplyFacebookInstagramPostCommentStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.reply-facebook-instagram-post-comment";

    [Required]
    [JsonProperty("comment_body__expr")]
    public string CommentBodyExpr { get; set; }

    [JsonProperty("should_like_comment__expr")]
    public string ShouldLikeCommentExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.FacebookIntegration;

    [JsonConstructor]
    public ReplyFacebookInstagramPostCommentStepArgs(string commentBodyExpr, string shouldLikeCommentExpr)
    {
        CommentBodyExpr = commentBodyExpr;
        ShouldLikeCommentExpr = shouldLikeCommentExpr;
    }
}