﻿using DynamicExpresso;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.CrmHub.Providers.Resolvers;
using Sleekflow.CrmHub.Utils;
using Sleekflow.JsonConfigs;

namespace Sleekflow.CrmHub.Unifies;

public class UnifyUtils
{
    public const string ExprPrefix = "%%EXPR%%";

    public static void UnifyAndTrack(
        Dictionary<string, SnapshottedValue?> dict,
        IDictionary<string, (object? FromValue, object? ToValue)> propModDict,
        IProviderObjectSnapshotTimeResolverService.ProviderObjectSnapshotTime resolvedObjectSnapshotTime,
        List<UnifyRule> unifyRules)
    {
        if (unifyRules.Count == 0)
        {
            return;
        }

        var flattenedDictionary = FlattenDictionary(dict);
        var dictWrapper = new SnapshottedValueDictWrapper(dict);

        // Unify properties in the dictionary based on the unifyRules
        foreach (var unifyRule in unifyRules)
        {
            var (key, snapshottedValue) =
                UnifyField(
                    unifyRule,
                    flattenedDictionary,
                    dictWrapper,
                    resolvedObjectSnapshotTime.SnapshotTime);

            if (dict.ContainsKey(key))
            {
                var currSnapshottedValue = dict[key]!;
                var isValueChanged = !JTokenUtils.DeepEquals(
                    currSnapshottedValue.Value as JObject ?? JTokenUtils.ToJToken(currSnapshottedValue.Value),
                    snapshottedValue.Value as JObject ?? JTokenUtils.ToJToken(snapshottedValue.Value));
                if (isValueChanged)
                {
                    propModDict[key] = (currSnapshottedValue.Value, snapshottedValue.Value);
                    dict[key] = snapshottedValue;
                }
            }
            else
            {
                propModDict[key] = (null, snapshottedValue.Value);
                dict[key] = snapshottedValue;
            }
        }
    }

    public static Dictionary<string, SnapshottedValue?> FlattenDictionary(
        Dictionary<string, SnapshottedValue?> dict)
    {
        var flattenedDict = dict
            .SelectMany(
                e =>
                {
                    if (e.Value is { Value: JObject jObject })
                    {
                        var dictionary = jObject.ToObject<Dictionary<string, object>>();
                        if (dictionary != null)
                        {
                            return dictionary.Select(
                                ne =>
                                    new KeyValuePair<string, SnapshottedValue?>(
                                        e.Key + ":" + ne.Key,
                                        new SnapshottedValue(e.Value.SnapshotTime, ne.Value)));
                        }
                    }

                    return new List<KeyValuePair<string, SnapshottedValue?>>
                    {
                        e,
                    };
                })
            .ToDictionary(e => e.Key, e => e.Value);

        return flattenedDict;
    }

    public static KeyValuePair<string, SnapshottedValue> UnifyField(
        UnifyRule unifyRule,
        IReadOnlyDictionary<string, SnapshottedValue?> dict,
        SnapshottedValueDictWrapper dictWrapper,
        DateTimeOffset snapshotTime)
    {
        switch (unifyRule.Strategy)
        {
            case "time":
            {
                var providerPrecedenceSnapshottedValue = unifyRule
                    .ProviderPrecedences
                    .Where(p => dict.ContainsKey(p) || p.StartsWith(ExprPrefix))
                    .Select(p => EvaluateProviderPrecedence(dict, dictWrapper, p))
                    .DefaultIfEmpty(
                        new PrecedenceRecordAndSnapshottedValue(
                            "Default",
                            new SnapshottedValue(snapshotTime, null)))
                    .OrderByDescending(fv => fv.SnapshottedValue.SnapshotTime)
                    .First();

                return new KeyValuePair<string, SnapshottedValue>(
                    $"{UnifyService.UnifiedNamespaceName}:{unifyRule.FieldName}",
                    providerPrecedenceSnapshottedValue.SnapshottedValue!);
            }

            case "priority":
            {
                var providerPrecedenceSnapshottedValue = unifyRule
                    .ProviderPrecedences
                    .Where(p => dict.ContainsKey(p) || p.StartsWith(ExprPrefix))
                    .Select(p => EvaluateProviderPrecedence(dict, dictWrapper, p))
                    .DefaultIfEmpty(
                        new PrecedenceRecordAndSnapshottedValue(
                            "Default",
                            new SnapshottedValue(snapshotTime, null)))
                    .First();

                return new KeyValuePair<string, SnapshottedValue>(
                    $"{UnifyService.UnifiedNamespaceName}:{unifyRule.FieldName}",
                    providerPrecedenceSnapshottedValue.SnapshottedValue!);
            }

            default:
                throw new NotImplementedException();
        }
    }

    private sealed record PrecedenceRecordAndSnapshottedValue(
        string ProviderPrecedence,
        SnapshottedValue SnapshottedValue);

    private static PrecedenceRecordAndSnapshottedValue EvaluateProviderPrecedence(
        IReadOnlyDictionary<string, SnapshottedValue?> dict,
        SnapshottedValueDictWrapper dictWrapper,
        string providerPrecedence)
    {
        if (providerPrecedence.StartsWith(ExprPrefix))
        {
            var substring = providerPrecedence.Substring(ExprPrefix.Length).Trim();
            var myDictWrapper = dictWrapper.NewInstance();

            object? result;
            try
            {
                var interpreter = new Interpreter()
                    .Reference(typeof(DateTimeOffset))
                    .SetVariable("d", myDictWrapper);
                var parsedExpression = interpreter.Parse(substring);
                var invoke = parsedExpression.Invoke();

                if (Equals(invoke, myDictWrapper))
                {
                    result = "d";
                }
                else if (invoke == null)
                {
                    result = null;
                }
                else
                {
                    result = JsonConvert
                        .SerializeObject(
                            invoke,
                            JsonConfig.DefaultJsonSerializerSettings)
                        .Trim('\"');
                }
            }
            catch (Exception)
            {
                result = "Not Evaluatable Expression";
            }

            return new PrecedenceRecordAndSnapshottedValue(
                providerPrecedence,
                new SnapshottedValue(
                    myDictWrapper.GetLatestSnapshotTime() ?? DateTimeOffset.MinValue,
                    result));
        }

        return new PrecedenceRecordAndSnapshottedValue(
            providerPrecedence,
            dict[providerPrecedence]!);
    }
}