using Pulumi;
using Pulumi.AzureNative.EventHub;
using Pulumi.AzureNative.EventHub.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Constants;
using Sleekflow.Infras.Constants;

namespace Sleekflow.Infras.Components;

public class MyEventHub
{
    private readonly ResourceGroup _resourceGroup;

    public MyEventHub(
        ResourceGroup resourceGroup)
    {
        _resourceGroup = resourceGroup;
    }

    public class EventHubOutput
    {
        public Output<string> NamespacePrimaryConnStr { get; }

        public EventHub OnAuditLogRequestedEventHub { get; }

        public EventHub OnStepExecutionStatusChangedEventHub { get; }

        public EventHub OnUserEventHappenedEventHub { get; }

        public string? EventHubSuffix { get; }

        public Namespace Namespace { get; }

        public Output<(string KeyName, string PrimaryKey, string PrimaryConnectionString)> Key { get; }

        public EventHubOutput(
            Output<string> namespacePrimaryConnStr,
            EventHub onAuditLogRequestedEventHub,
            EventHub onStepExecutionStatusChangedEventHub,
            EventHub onUserEventHappenedEventHub,
            string? eventHubSuffix,
            Namespace @namespace,
            Output<(string KeyName, string PrimaryKey, string PrimaryConnectionString)> key)
        {
            NamespacePrimaryConnStr = namespacePrimaryConnStr;
            OnAuditLogRequestedEventHub = onAuditLogRequestedEventHub;
            OnStepExecutionStatusChangedEventHub = onStepExecutionStatusChangedEventHub;
            OnUserEventHappenedEventHub = onUserEventHappenedEventHub;
            EventHubSuffix = eventHubSuffix;
            Namespace = @namespace;
            Key = key;
        }

        public string ConstructConsumerGroupName(string consumerGroupName)
        {
            return EventHubSuffix is not null ? $"{consumerGroupName}-{EventHubSuffix}" : consumerGroupName;
        }
    }

    public EventHubOutput InitEventHub(string? envName = null, string? locationName = null)
    {
        var @namespace = new Namespace(
            ConstructResourceName("sleekflow-event-hub", envName),
            new NamespaceArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location =
                    locationName is not null
                        ? LocationNames.GetAzureLocation(locationName)
                        : _resourceGroup.Location,
                Sku = new SkuArgs
                {
                    Name = SkuName.Standard, Tier = SkuTier.Standard,
                },
                IsAutoInflateEnabled = true,
                MaximumThroughputUnits = 20,
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        const string authorizationRuleName = "sleekflow-event-hub-default-policy";

        var defaultNamespaceAuthorizationRule = new NamespaceAuthorizationRule(
            ConstructResourceName(authorizationRuleName, envName),
            new NamespaceAuthorizationRuleArgs
            {
                AuthorizationRuleName = authorizationRuleName,
                NamespaceName = @namespace.Name,
                ResourceGroupName = _resourceGroup.Name,
                Rights =
                {
                    AccessRights.Listen, AccessRights.Send,
                },
            },
            new CustomResourceOptions
            {
                Parent = @namespace
            });

        var defaultNamespaceKey = ListNamespaceKeys.Invoke(
            new ListNamespaceKeysInvokeArgs
            {
                AuthorizationRuleName = defaultNamespaceAuthorizationRule.Name,
                NamespaceName = @namespace.Name,
                ResourceGroupName = _resourceGroup.Name,
            });
        var defaultNamespacePrimaryConnStr = defaultNamespaceKey.Apply(nk => nk.PrimaryConnectionString);
        var defaultNamespacePrimaryKey = defaultNamespaceKey.Apply(nk => nk.PrimaryKey);

        var onAuditLogRequestedEventHub = new EventHub(
            ConstructResourceName(EventHubNames.SleekflowOnAuditLogRequestedEventHubEvent, envName),
            new EventHubArgs
            {
                EventHubName = EventHubNames.SleekflowOnAuditLogRequestedEventHubEvent,
                MessageRetentionInDays = 1,
                NamespaceName = @namespace.Name,
                PartitionCount = 10,
                ResourceGroupName = _resourceGroup.Name,
                Status = EntityStatus.Active,
            },
            new CustomResourceOptions
            {
                Parent = @namespace
            });

        var onStepExecutionStatusChangedEventHub = new EventHub(
            ConstructResourceName(EventHubNames.FlowHubOnStepExecutionStatusChangedEventHubEvent, envName),
            new EventHubArgs
            {
                EventHubName = EventHubNames.FlowHubOnStepExecutionStatusChangedEventHubEvent,
                MessageRetentionInDays = 1,
                NamespaceName = @namespace.Name,
                PartitionCount = 10,
                ResourceGroupName = _resourceGroup.Name,
                Status = EntityStatus.Active,
            },
            new CustomResourceOptions
            {
                Parent = @namespace
            });

        var onUserEventHappenedEventHub = new EventHub(
            ConstructResourceName(EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent, envName),
            new EventHubArgs
            {
                EventHubName = EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent,
                MessageRetentionInDays = 1,
                NamespaceName = @namespace.Name,
                PartitionCount = 20,
                ResourceGroupName = _resourceGroup.Name,
                Status = EntityStatus.Active,
            },
            new CustomResourceOptions
            {
                Parent = @namespace
            });

        return new EventHubOutput(
            defaultNamespacePrimaryConnStr,
            onAuditLogRequestedEventHub,
            onStepExecutionStatusChangedEventHub,
            onUserEventHappenedEventHub,
            envName,
            @namespace,
            defaultNamespaceKey.Apply(k => (k.KeyName, k.PrimaryKey, k.PrimaryConnectionString)));
    }

    private static string ConstructResourceName(string resourceName, string? envName)
    {
        return envName is not null ? $"{resourceName}-{envName}" : resourceName;
    }
}