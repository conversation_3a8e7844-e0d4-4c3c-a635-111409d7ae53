using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnSalesforceObjectEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnSalesforceObjectEnrolled; }
    }

    [Required]
    [JsonProperty("salesforce_connection_id")]
    public string SalesforceConnectionId { get; set; }

    [Required]
    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [Required]
    [JsonProperty("is_custom_object")]
    public bool IsCustomObject { get; set; }

    [Required]
    [JsonProperty("object_dict")]
    public Dictionary<string, object?> ObjectDict { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnSalesforceObjectEnrolledEventBody(
        DateTimeOffset createdAt,
        string salesforceConnectionId,
        string objectType,
        bool isCustomObject,
        Dictionary<string, object?> objectDict,
        string workflowId,
        string workflowVersionedId)
        : base(createdAt)
    {
        SalesforceConnectionId = salesforceConnectionId;
        ObjectType = objectType;
        IsCustomObject = isCustomObject;
        ObjectDict = objectDict;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}