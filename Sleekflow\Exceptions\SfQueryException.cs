namespace Sleekflow.Exceptions;

public class SfQueryException : ErrorCodeException
{
    public SfQueryException(string message, string operation)
        : base(
            ErrorCodeConstant.SfQueryException,
            $"{message}. operation=[{operation}]",
            new Dictionary<string, object?>()
            {
                {
                    "operation", operation
                }
            })
    {
    }

    public SfQueryException(Exception exception, string operation)
        : base(
            ErrorCodeConstant.SfQueryException,
            $"Unable to query the database. operation=[{operation}]",
            new Dictionary<string, object?>()
            {
                {
                    "operation", operation
                }
            },
            exception)
    {
    }
}