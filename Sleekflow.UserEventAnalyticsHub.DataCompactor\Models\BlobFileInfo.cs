namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Models;

public class BlobFileInfo
{
    public string BlobPath { get; set; } = string.Empty;
    public DateTime LastModified { get; set; }
    public long SizeBytes { get; set; }
    public string SleekflowCompanyId { get; set; } = string.Empty;
    public int Year { get; set; }
    public int Month { get; set; }
    public int Day { get; set; }
    public int Hour { get; set; }

    public override string ToString()
    {
        return $"BlobFileInfo [Path={BlobPath}, Company={SleekflowCompanyId}, Size={SizeBytes}, LastModified={LastModified}]";
    }
}