using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnInstagramMediaCommentReceivedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnInstagramMediaCommentReceived; }
    }

    [Required]
    [JsonProperty("instagram_media_comment")]
    public OnInstagramMediaCommentReceivedEventBodyMessage InstagramMediaComment { get; set; }

    [Required]
    [JsonProperty("instagram_page_id")]
    public string InstagramPageId { get; set; }

    [Required]
    [JsonProperty("comment_id")]
    public string CommentId { get; set; }

    [Required]
    [JsonProperty("is_new_contact")]
    public bool IsNewContact { get; set; }

    [JsonProperty("conversation_id")]
    public string? ConversationId { get; set; }

    [JsonProperty("contact_id")]
    public string? ContactId { get; set; }

    [JsonProperty("contact")]
    public Dictionary<string, object?>? Contact { get; set; }

    [JsonConstructor]
    public OnInstagramMediaCommentReceivedEventBody(
        DateTimeOffset createdAt,
        OnInstagramMediaCommentReceivedEventBodyMessage instagramMediaComment,
        string instagramPageId,
        string commentId,
        bool isNewContact,
        string? conversationId,
        string? contactId,
        Dictionary<string, object?>? contact)
        : base(createdAt)
    {
        InstagramMediaComment = instagramMediaComment;
        InstagramPageId = instagramPageId;
        CommentId = commentId;
        IsNewContact = isNewContact;
        ConversationId = conversationId;
        ContactId = contactId;
        Contact = contact;
    }
}

public class OnInstagramMediaCommentReceivedEventBodyMessage
{
    [JsonProperty("comment_id")]
    public string CommentId { get; set; }

    [JsonProperty("media_id")]
    public string MediaId { get; set; }

    [JsonProperty("From")]
    public FromInstagramSender From { get; set; }

    /// <summary>
    /// FEED or REELS
    /// </summary>
    [JsonProperty("media_product_type")]
    public string MediaProductType { get; set; }

    /// <summary>
    /// The comment content, created by the instagram user, under the media
    /// </summary>
    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonConstructor]
    public OnInstagramMediaCommentReceivedEventBodyMessage(
        string commentId,
        string mediaId,
        FromInstagramSender from,
        string mediaProductType,
        string text)
    {
        CommentId = commentId;
        MediaId = mediaId;
        From = from;
        MediaProductType = mediaProductType;
        Text = text;
    }
}

public class FromInstagramSender
{
    [JsonProperty("instagram_id")]
    public string InstagramId { get; set; }

    [JsonProperty("instagram_user_name")]
    public string? InstagramUserName { get; set; }
}