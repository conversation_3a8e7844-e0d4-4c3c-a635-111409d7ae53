using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class GetWabasConnectedProductCatalogs
    : ITrigger<
        GetWabasConnectedProductCatalogs.GetWabasConnectedProductCatalogsInput,
        GetWabasConnectedProductCatalogs.GetWabasConnectedProductCatalogsOutput>
{
    private readonly ILogger<GetWabasConnectedProductCatalogs> _logger;
    private readonly IProductCatalogService _productCatalogService;

    public GetWabasConnectedProductCatalogs(
        ILogger<GetWabasConnectedProductCatalogs> logger,
        IProductCatalogService productCatalogService)
    {
        _logger = logger;
        _productCatalogService = productCatalogService;
    }

    public class GetWabasConnectedProductCatalogsInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("user_access_token")]
        public string? UserAccessToken { get; set; }

        [JsonProperty("should_refresh")]
        public bool? ShouldRefresh { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public GetWabasConnectedProductCatalogsInput(
            string sleekflowCompanyId,
            string? userAccessToken,
            bool? shouldRefresh,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UserAccessToken = userAccessToken;
            ShouldRefresh = shouldRefresh;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class GetWabasConnectedProductCatalogsOutput
    {
        [JsonProperty("facebook_connected_waba_product_catalog_mappings")]
        public List<FacebookConnectedWabaProductCatalogMappingDto> FacebookConnectedWabaProductCatalogMappings
        {
            get;
            set;
        }

        [JsonConstructor]
        public GetWabasConnectedProductCatalogsOutput(
            List<FacebookConnectedWabaProductCatalogMappingDto> facebookConnectedWabaProductCatalogMappings)
        {
            FacebookConnectedWabaProductCatalogMappings = facebookConnectedWabaProductCatalogMappings;
        }
    }

    public async Task<GetWabasConnectedProductCatalogsOutput> F(
        GetWabasConnectedProductCatalogsInput getWabasConnectedProductCatalogsInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            getWabasConnectedProductCatalogsInput.SleekflowStaffId,
            getWabasConnectedProductCatalogsInput.SleekflowStaffTeamIds);

        var userAccessToken = getWabasConnectedProductCatalogsInput.UserAccessToken;
        var sleekflowCompanyId = getWabasConnectedProductCatalogsInput.SleekflowCompanyId;
        var shouldRefresh = getWabasConnectedProductCatalogsInput.ShouldRefresh is true;

        _logger.LogInformation(
            "getting {SleekflowCompanyId} wabas and product catalogs information with {UserAccessToken} and {ShouldRefresh} shouldRefresh flag by {@SleekflowStaff}",
            sleekflowCompanyId,
            userAccessToken,
            shouldRefresh.ToString(),
            sleekflowStaff);

        var facebookConnectedWabaProductCatalogMappings = !string.IsNullOrEmpty(userAccessToken)
            ? await _productCatalogService.GetWabasConnectedFacebookProductCatalogsWithUserAccessTokenAsync(
                userAccessToken,
                sleekflowCompanyId,
                sleekflowStaff)
            : await _productCatalogService.GetWabasConnectedFacebookProductCatalogsAsync(
                sleekflowCompanyId,
                shouldRefresh,
                sleekflowStaff);

        return new GetWabasConnectedProductCatalogsOutput(facebookConnectedWabaProductCatalogMappings);
    }
}