using System.Collections.Concurrent;
using System.Text;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;
using Sleekflow.IntelligentHub.Evaluator.Evaluators;
using Sleekflow.IntelligentHub.Evaluator.LeadScores;
using Sleekflow.IntelligentHub.Evaluator.Plugins;

namespace Sleekflow.IntelligentHub.Evaluator;

public class OverallScore
{
    private readonly DiagnosticsSummaryPlugin _diagnosticsSummaryPlugin = new ();
    private readonly ConcurrentDictionary<string, ChatEvalResult[]> evalResults = new ();
    private readonly ConcurrentDictionary<string, LeadScoreEvaluatorResult[]> leadScoreResults = new ();
    private readonly ConcurrentDictionary<string, ReActEvalResult[]> reactEvalResults = new ();

    public void AddChatEvalResults(ChatEvalResult[] chatEvalResults)
    {
        foreach (var chatEvalResult in chatEvalResults)
        {
            evalResults.AddOrUpdate(
                chatEvalResult.Label,
                new[]
                {
                    chatEvalResult
                }, // Initial value if the key does not exist
                (key, existingValue) => existingValue.Append(chatEvalResult).ToArray() // Update function
            );
        }
    }

    public void AddLeadScoreResults(LeadScoreEvaluatorResult[] results)
    {
        foreach (var result in results)
        {
            leadScoreResults.AddOrUpdate(
                result.AgentName,
                new[]
                {
                    result
                }, // Initial value if the key does not exist
                (key, existingValue) => existingValue.Append(result).ToArray() // Update function
            );
        }
    }

    public void AddReActEvalResult(ReActEvalResult result)
    {
        // Use "ReAct" as the label for all ReAct evaluation results
        const string label = "ReAct";

        reactEvalResults.AddOrUpdate(
            label,
            new[]
            {
                result
            }, // Initial value if the key does not exist
            (key, existingValue) => existingValue.Append(result).ToArray() // Update function
        );
    }

    public async Task<string> GetReportString()
    {
        var sb = new StringBuilder();

        // Process ChatEval results
        if (evalResults.Count > 0)
        {
            sb.AppendLine("=== ChatEval Results ===");

            var scoreOutputStrings = await Task.WhenAll(
                evalResults.Keys.Select(
                    async label =>
                    {
                        var results = evalResults[label];
                        var fullAnswerScoringScore = results.Length * ChatEvalAnswerScoringEvaluator.FullScore;
                        var fullRagOutputScoringScore = results.Length * RagOutputScoringEvaluator.FullScore;
                        var fullCombinedScore = fullAnswerScoringScore + fullRagOutputScoringScore;

                        var totalAnswerScoringScore = results.Sum(evalResult => evalResult.AnswerScoringScore);
                        var totalRagOutputScoringScore = results.Sum(evalResult => evalResult.RagOutputScoringScore);
                        var avgDuration = results.Average(evalResult => evalResult.ElapsedMilliseconds);

                        // Sum to get total score
                        var totalScore = totalAnswerScoringScore + totalRagOutputScoringScore;

                        // Calculate the overall percentage relative to the combined full score
                        string percentageText;
                        if (fullCombinedScore == 0)
                        {
                            percentageText = "N/A";
                        }
                        else
                        {
                            var percentage = (totalScore / fullCombinedScore) * 100.0;
                            percentageText = $"{percentage:F1}%";
                        }

                        // Output format: Total Score (Answer Score, RAG Score) (percentage)
                        var scoreText =
                            $"{label} ({Math.Round(avgDuration / 1000d, 2)}s): {Math.Round(totalScore, 2)} (A:{Math.Round(totalAnswerScoringScore, 2)}, RAG:{Math.Round(totalRagOutputScoringScore, 2)}) ({percentageText})\n";

                        string diagnosticsListString;
                        try
                        {
                            var diagnosticsSummary = await _diagnosticsSummaryPlugin.GetDiagnosticsSummary(results);
                            diagnosticsListString = string.Join(
                                "\n",
                                diagnosticsSummary.Select(d => $"- {d.AreaOfImprovement} ({d.Occurences})"));
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);

                            diagnosticsListString = "Error occurred while generating diagnostics summary.";
                        }

                        return scoreText;
                    }).ToArray());

        sb.AppendJoin("\n", scoreOutputStrings);

        // 添加LeadScore的输出
        if (leadScoreResults.Count > 0)
        {
            sb.AppendLine("\nLead Score Results:");
            foreach (var agentName in leadScoreResults.Keys)
            {
                var results = leadScoreResults[agentName];
                var avgScore = results.Average(r => r.AnswerScoringScore);
                var avgDuration = results.Average(r => r.ElapsedMilliseconds);

                sb.AppendLine($"{agentName} ({Math.Round(avgDuration / 1000d, 2)}s): Avg Score: {Math.Round(avgScore, 2)}");
            }
        }

            sb.AppendJoin("\n", scoreOutputStrings);
        }

        // Process ReActEval results
        if (reactEvalResults.Count > 0)
        {
            if (evalResults.Count > 0)
            {
                sb.AppendLine("\n");
            }

            sb.AppendLine("=== ReAct Evaluation Results ===");

            foreach (var label in reactEvalResults.Keys)
            {
                var results = reactEvalResults[label];
                var avgScore = results.Average(result => result.Score);
                var avgDuration = results.Average(result => result.ElapsedMilliseconds);
                var totalTests = results.Length;
                var successfulTests = results.Count(r => r.Score > 0);

                sb.AppendLine($"{label} ({Math.Round(avgDuration / 1000d, 2)}s): Avg Score: {Math.Round(avgScore, 2)}/10");
                sb.AppendLine($"Tests: {totalTests}, Successful: {successfulTests}, Success Rate: {(totalTests > 0 ? (successfulTests * 100.0 / totalTests).ToString("F1") : "0")}%");
            }
        }

        return sb.ToString();
    }
}