using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Scriban.Runtime;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactLabelRelationshipsStepExecutor : IStepExecutor
{
}

public class UpdateContactLabelRelationshipsStepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactLabelRelationshipsStepArgs>>,
        IUpdateContactLabelRelationshipsStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public UpdateContactLabelRelationshipsStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactLabelRelationships",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateContactLabelRelationshipsInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [JsonProperty("add_labels")]
        public List<string>? AddLabels { get; set; }

        [JsonProperty("remove_labels")]
        public List<string>? RemoveLabels { get; set; }

        [JsonProperty("set_labels")]
        public List<string>? SetLabels { get; set; }

        [JsonConstructor]
        public UpdateContactLabelRelationshipsInput(
            string stateId,
            StateIdentity stateIdentity,
            string contactId,
            List<string>? addLabels,
            List<string>? removeLabels,
            List<string>? setLabels)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ContactId = contactId;
            AddLabels = addLabels;
            RemoveLabels = removeLabels;
            SetLabels = setLabels;
        }
    }

    private async Task<UpdateContactLabelRelationshipsInput> GetArgs(
        CallStep<UpdateContactLabelRelationshipsStepArgs> callStep,
        ProxyState state)
    {
        var contactId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                      ?? callStep.Args.ContactIdExpr);
        var addLabels =
            callStep.Args.AddLabelsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.AddLabelsExpr))!).Cast<string>().ToList();
        var removeLabels =
            callStep.Args.RemoveLabelsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.RemoveLabelsExpr))!).Cast<string>().ToList();
        var setLabels =
            callStep.Args.SetLabelsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.SetLabelsExpr))!).Cast<string>().ToList();

        return new UpdateContactLabelRelationshipsInput(
            state.Id,
            state.Identity,
            contactId,
            addLabels,
            removeLabels,
            setLabels);
    }
}