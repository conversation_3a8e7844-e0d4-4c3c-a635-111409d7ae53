﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.SchemafulObjects.FileProcessors;

public class MyReaderSchemafulObjectDto : IHasSleekflowUserProfileId
{
    [JsonProperty(SchemafulObject.PropertyNamePrimaryPropertyValue)]
    public string PrimaryPropertyValue { get; set; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty(SchemafulObject.PropertyNamePropertyValues)]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [JsonConstructor]
    public MyReaderSchemafulObjectDto(
        string primaryPropertyValue,
        string sleekflowUserProfileId,
        Dictionary<string, object?> propertyValues)
    {
        PrimaryPropertyValue = primaryPropertyValue;
        SleekflowUserProfileId = sleekflowUserProfileId;
        PropertyValues = propertyValues;
    }
}