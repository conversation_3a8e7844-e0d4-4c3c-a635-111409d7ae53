﻿using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowExecutionBlockedEvent
{
    public string SleekflowCompanyId { get; set; }

    public string StateId { get; set; }

    public string? WorkflowExecutionReasonCode { get; set; }

    public StateIdentity StateIdentity { get; set; }

    public string? WorkflowType { get; set; }

    public DateTimeOffset BlockedAt { get; set; } = DateTimeOffset.UtcNow;

    public OnWorkflowExecutionBlockedEvent(
        string sleekflowCompanyId,
        string stateId,
        string? workflowExecutionReasonCode,
        StateIdentity stateIdentity,
        string? workflowType)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        WorkflowExecutionReasonCode = workflowExecutionReasonCode;
        StateIdentity = stateIdentity;
        WorkflowType = workflowType;
    }
}