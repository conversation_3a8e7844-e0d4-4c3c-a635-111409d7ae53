using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.ProductVariants;

[TriggerGroup(ControllerNames.ProductVariants)]
public class CreateProductVariant
    : ITrigger<
        CreateProductVariant.CreateProductVariantInput,
        CreateProductVariant.CreateProductVariantOutput>
{
    private readonly IProductVariantService _productVariantService;
    private readonly IIdService _idService;
    private readonly IImageService _imageService;

    public CreateProductVariant(
        IProductVariantService productVariantService,
        IIdService idService,
        IImageService imageService)
    {
        _productVariantService = productVariantService;
        _idService = idService;
        _imageService = imageService;
    }

    public class CreateProductVariantInput : ProductVariantInput, IHasSleekflowStaff
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(ProductVariant.PropertyNameProductId)]
        public string ProductId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateProductVariantInput(
            string? sku,
            string? url,
            List<Price> prices,
            int position,
            List<ProductVariant.ProductVariantAttribute> attributes,
            List<Multilingual> names,
            List<Description> descriptions,
            List<ImageDto> images,
            PlatformData? platformData,
            string sleekflowCompanyId,
            List<string> categoryIds,
            string storeId,
            string productId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(
                sku,
                url,
                prices,
                position,
                attributes,
                names,
                descriptions,
                images,
                platformData)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductId = productId;
            Sku = sku;
            Url = url;
            Prices = prices;
            Attributes = attributes;
            Names = names;
            Descriptions = descriptions;
            Images = images;
            PlatformData = platformData;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateProductVariantOutput
    {
        [JsonProperty("product_variant")]
        public ProductVariantDto ProductVariant { get; set; }

        [JsonConstructor]
        public CreateProductVariantOutput(
            ProductVariantDto productVariant)
        {
            ProductVariant = productVariant;
        }

        public CreateProductVariantOutput(ProductVariant productVariant)
            : this(new ProductVariantDto(productVariant))
        {
        }
    }

    public async Task<CreateProductVariantOutput> F(CreateProductVariantInput createProductVariantInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createProductVariantInput.SleekflowStaffId,
            createProductVariantInput.SleekflowStaffTeamIds);
        var sleekflowCompanyId = createProductVariantInput.SleekflowCompanyId;

        var images = await _imageService.GetImagesAsync(
            createProductVariantInput.Images,
            sleekflowCompanyId,
            createProductVariantInput.StoreId);

        var productVariant = await _productVariantService.CreateAndGetProductVariantAsync(
            new ProductVariant(
                _idService.GetId(SysTypeNames.ProductVariant),
                sleekflowCompanyId,
                createProductVariantInput.StoreId,
                createProductVariantInput.ProductId,
                createProductVariantInput.Sku,
                createProductVariantInput.Url,
                createProductVariantInput.Prices,
                createProductVariantInput.Position,
                false,
                createProductVariantInput.Attributes
                    .Select(
                        a => new ProductVariant.ProductVariantAttribute(
                            a.Name.ToLowerInvariant(),
                            a.Value))
                    .ToList(),
                createProductVariantInput.Names,
                createProductVariantInput.Descriptions,
                images,
                createProductVariantInput.PlatformData ?? PlatformData.CustomCatalog(),
                new List<string>
                {
                    RecordStatuses.Active
                },
                new Dictionary<string, object?>(),
                sleekflowStaff,
                sleekflowStaff,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
            sleekflowStaff);

        return new CreateProductVariantOutput(productVariant);
    }
}