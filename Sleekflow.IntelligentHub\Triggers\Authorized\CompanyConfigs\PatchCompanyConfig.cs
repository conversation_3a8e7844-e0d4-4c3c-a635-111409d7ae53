using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyConfigs;

[TriggerGroup(
    ControllerNames.CompanyConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class PatchCompanyConfig
    : ITrigger<PatchCompanyConfig.PatchCompanyConfigInput, PatchCompanyConfig.PatchCompanyConfigOutput>
{
    private readonly ICompanyConfigService _companyConfigService;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public PatchCompanyConfig(
        ICompanyConfigService companyConfigService,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _companyConfigService = companyConfigService;
        _authorizationContext = authorizationContext;
    }

    public class PatchCompanyConfigInput
    {
        [JsonProperty("name")]
        public string? Name { get; set; }

        [JsonProperty("background_information")]
        public string? BackgroundInformation { get; set; }

        [JsonProperty("preferred_language")]
        public string? PreferredLanguage { get; set; }

        [JsonProperty("collaboration_mode")]
        public string? CollaborationMode { get; set; }

        [JsonConstructor]
        public PatchCompanyConfigInput(
            string? name = null,
            string? backgroundInformation = null,
            string? preferredLanguage = null,
            string? collaborationMode = null)
        {
            Name = name;
            BackgroundInformation = backgroundInformation;
            PreferredLanguage = preferredLanguage;
            CollaborationMode = collaborationMode;
        }
    }

    public class PatchCompanyConfigOutput
    {
        [JsonProperty("company_config")]
        public CompanyConfigDto CompanyConfig { get; set; }

        [JsonConstructor]
        public PatchCompanyConfigOutput(CompanyConfigDto companyConfig)
        {
            CompanyConfig = companyConfig;
        }
    }

    public async Task<PatchCompanyConfigOutput> F(PatchCompanyConfigInput input)
    {
        var config = await _companyConfigService.PatchAndGetAsync(
            _authorizationContext.SleekflowCompanyId!,
            _authorizationContext.SleekflowStaffId!,
            _authorizationContext.SleekflowTeamIds,
            input.Name,
            input.BackgroundInformation,
            input.PreferredLanguage,
            input.CollaborationMode);

        return new PatchCompanyConfigOutput(new CompanyConfigDto(config));
    }
}