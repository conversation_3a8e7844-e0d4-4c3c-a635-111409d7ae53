using System.Reflection;
using Serilog;
using Sleekflow;
using Sleekflow.Integrator.Hubspot.Models.Fields;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;

#if SWAGGERGEN
using Moq;
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
#endif

const string appName = "SleekflowIntegratorHubspot";

MvcModules.BuildLogger(appName);

try
{
    Log.Information("Starting web host");

    var builder = WebApplication.CreateBuilder(args);
    builder.Host.UseSerilog();
    builder.Services.AddHttpContextAccessor();

#if SWAGGERGEN
#else
    builder.Host.ConfigureAppConfiguration(
        b =>
        {
            b.AddAzureAppConfiguration(Environment.GetEnvironmentVariable("APP_CONFIGURATION_CONN_STR"));
        });
#endif

    builder.Services.AddAutoMapper(expression => { expression.AddProfile<FieldProfile>(); });

    MvcModules.BuildHealthCheck(builder.Services);
    MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
#if SWAGGERGEN
    MvcModules.BuildApiBehaviors(builder, list =>
    {
        list.AddRange(new List<OpenApiServer>()
        {
            new OpenApiServer()
            {
                Description = "Local",
                Url = $"https://localhost:7074",
            }
        });
    });
#else
    MvcModules.BuildApiBehaviors(builder);
#endif
    Modules.BuildConfigs(builder.Services);
    Modules.BuildHttpClients(builder.Services);
    Modules.BuildServices(builder.Services, Assembly.Load("Sleekflow.CrmHub.Models"));
    Modules.BuildTriggers(builder.Services);
    Modules.BuildServiceBusServices(builder.Services);
    Modules.BuildCacheServices(builder.Services);
    Modules.BuildDbServices(builder.Services);
    Modules.BuildWorkerServices(builder.Services);
    MvcModules.BuildFuncServices(builder.Services, appName);
    CrmHubModules.BuildCrmHubDbServices(builder.Services);

    var app = builder.Build();

    // app.UseHttpsRedirection();
    app.UseAuthorization();
    app.MapControllers();
    HealthCheckMapping.MapHealthChecks(app);

#if SWAGGERGEN
    app.UseSwagger();
    app.UseSwaggerUI(
        options =>
        {
            var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

            foreach (var description in provider.ApiVersionDescriptions)
            {
                options.SwaggerEndpoint(
                    $"/swagger/{description.GroupName}/swagger.json",
                    description.GroupName.ToUpperInvariant());
            }
        });
#endif

    ThreadPool.SetMinThreads(128, 128);
    ThreadPool.SetMaxThreads(512, 512);

    app.Run();

    return 0;
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");
    return 1;
}
finally
{
    Log.CloseAndFlush();
}