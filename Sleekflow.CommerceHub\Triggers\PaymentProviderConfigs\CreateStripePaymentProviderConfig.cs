using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.CommerceHub.Payments.Stripe;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;

[TriggerGroup(ControllerNames.PaymentProviderConfigs)]
public class CreateStripePaymentProviderConfig
    : ITrigger<
        CreateStripePaymentProviderConfig.CreateStripePaymentProviderConfigInput,
        CreateStripePaymentProviderConfig.CreateStripePaymentProviderConfigOutput>
{
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;
    private readonly IIdService _idService;
    private readonly IStripePaymentProviderExternalConfigResolver _stripePaymentProviderExternalConfigResolver;
    private readonly IStripeSecretConfig _stripeSecretConfig;

    public CreateStripePaymentProviderConfig(
        IPaymentProviderConfigService paymentProviderConfigService,
        IIdService idService,
        IStripePaymentProviderExternalConfigResolver stripePaymentProviderExternalConfigResolver,
        IStripeSecretConfig stripeSecretConfig)
    {
        _paymentProviderConfigService = paymentProviderConfigService;
        _idService = idService;
        _stripePaymentProviderExternalConfigResolver = stripePaymentProviderExternalConfigResolver;
        _stripeSecretConfig = stripeSecretConfig;
    }

    public class CreateStripePaymentProviderConfigInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("api_key")]
        public string ApiKey { get; set; }

        [Required]
        [JsonProperty("connect_webhook_secret")]
        public string ConnectWebhookSecret { get; set; }

        [Required]
        [JsonProperty("payment_webhook_secret")]
        public string PaymentWebhookSecret { get; set; }

        [Required]
        [JsonProperty("application_fee_rate")]
        public decimal ApplicationFeeRate { get; set; }

        [Required]
        [JsonProperty("is_shipping_enabled")]
        public bool IsShippingEnabled { get; set; }

        [JsonProperty("shipping_allowed_country_iso_codes")]
        public List<string>? ShippingAllowedCountryIsoCodes { get; set; }

        [Required]
        [JsonProperty("is_inventory_enabled")]
        public bool IsInventoryEnabled { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateStripePaymentProviderConfigInput(
            string sleekflowCompanyId,
            string apiKey,
            string connectWebhookSecret,
            string paymentWebhookSecret,
            decimal applicationFeeRate,
            bool isShippingEnabled,
            List<string>? shippingAllowedCountryIsoCodes,
            bool isInventoryEnabled,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApiKey = apiKey;
            ConnectWebhookSecret = connectWebhookSecret;
            PaymentWebhookSecret = paymentWebhookSecret;
            ApplicationFeeRate = applicationFeeRate;
            IsShippingEnabled = isShippingEnabled;
            ShippingAllowedCountryIsoCodes = shippingAllowedCountryIsoCodes;
            IsInventoryEnabled = isInventoryEnabled;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateStripePaymentProviderConfigOutput
    {
        [Required]
        [JsonProperty("payment_provider_config_id")]
        public string PaymentProviderConfigId { get; set; }

        [JsonConstructor]
        public CreateStripePaymentProviderConfigOutput(
            string paymentProviderConfigId)
        {
            PaymentProviderConfigId = paymentProviderConfigId;
        }
    }

    // SleekFlow.SleekFlowPaymentStripe.Service.StripeOnBoardingService.GenerateStripeOnBoardingLink
    public async Task<CreateStripePaymentProviderConfigOutput> F(
        CreateStripePaymentProviderConfigInput createStripePaymentProviderConfigInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createStripePaymentProviderConfigInput.SleekflowStaffId,
            createStripePaymentProviderConfigInput.SleekflowStaffTeamIds);

        var stripePaymentProviderExternalConfig = await _stripePaymentProviderExternalConfigResolver.ResolveAsync(
            createStripePaymentProviderConfigInput.ApiKey,
            createStripePaymentProviderConfigInput.ConnectWebhookSecret,
            createStripePaymentProviderConfigInput.PaymentWebhookSecret,
            createStripePaymentProviderConfigInput.ApplicationFeeRate,
            createStripePaymentProviderConfigInput.IsShippingEnabled,
            createStripePaymentProviderConfigInput.ShippingAllowedCountryIsoCodes,
            createStripePaymentProviderConfigInput.IsInventoryEnabled);

        var paymentProviderConfig = await _paymentProviderConfigService.CreatePaymentProviderConfigAsync(
            new PaymentProviderConfig(
                _idService.GetId(SysTypeNames.PaymentProviderConfig),
                createStripePaymentProviderConfigInput.SleekflowCompanyId,
                PaymentProviderConfigStatuses.Pending,
                new List<string>(),
                new Dictionary<string, List<string>>(),
                new List<string>
                {
                    stripePaymentProviderExternalConfig.StripeAccount.DefaultCurrency
                },
                stripePaymentProviderExternalConfig,
                new List<string>
                {
                    "Active"
                },
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowStaff,
                sleekflowStaff),
            sleekflowStaff);

        return new CreateStripePaymentProviderConfigOutput(paymentProviderConfig.Id);
    }
}