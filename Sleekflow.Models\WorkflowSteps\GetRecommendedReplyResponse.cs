using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class GetRecommendedReplyResponse
{
    [JsonProperty("recommended_reply")]
    public string RecommendedReply { get; set; }

    [JsonProperty("confidence_scoring")]
    public int ConfidenceScoring { get; set; }

    [JsonConstructor]
    public GetRecommendedReplyResponse(string recommendedReply, int confidenceScoring)
    {
        RecommendedReply = recommendedReply;
        ConfidenceScoring = confidenceScoring;
    }
}