﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateHubspotObjectStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-hubspot-object";

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("fields__name_expr_set")]
    public HashSet<ObjectFieldNameValuePair> FieldsNameExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.HubspotIntegration;

    [JsonConstructor]
    public CreateHubspotObjectStepArgs(
        string connectionId,
        string entityTypeName,
        HashSet<ObjectFieldNameValuePair> fieldsNameExprSet)
    {
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        FieldsNameExprSet = fieldsNameExprSet;
    }
}