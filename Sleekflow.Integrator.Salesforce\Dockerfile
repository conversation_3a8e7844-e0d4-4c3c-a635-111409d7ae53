﻿ARG COMMON_IMAGE
FROM ${COMMON_IMAGE} AS build

# Copy all source code at hub level for faster builds
COPY . .

WORKDIR "/src/Sleekflow.Integrator.Salesforce"
RUN dotnet publish "Sleekflow.Integrator.Salesforce.csproj" -c Release -o /app/publish -v normal

FROM mcr.microsoft.com/dotnet/aspnet:8.0.7
WORKDIR /app
EXPOSE 80
EXPOSE 443
COPY --from=build /app/publish .
ENTRYPOINT ["dotnet", "Sleekflow.Integrator.Salesforce.dll"]
