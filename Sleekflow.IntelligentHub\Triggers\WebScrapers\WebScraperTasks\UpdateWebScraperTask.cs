﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class UpdateWebScraperTask : ITrigger<UpdateWebScraperTask.UpdateWebScraperTaskInput, UpdateWebScraperTask.UpdateWebScraperTaskOutput>
{
    private readonly IWebScraperService _webScraperService;

    public UpdateWebScraperTask(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class UpdateWebScraperTaskInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameApifyTaskId)]
        public string ApifyTaskId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameDisplayName)]
        public string DisplayName { get; set; }

        [Required]
        [JsonProperty(WebScraperSetting.PropertyNameWebScraperSetting)]
        [Validations.ValidateObject]
        public WebScraperSetting WebScraperSetting { get; set; }

        [JsonConstructor]
        public UpdateWebScraperTaskInput(
            string sleekflowCompanyId,
            string displayName,
            WebScraperSetting webScraperSetting,
            string apifyTaskId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DisplayName = displayName;
            WebScraperSetting = webScraperSetting;
            ApifyTaskId = apifyTaskId;
        }
    }

    public class UpdateWebScraperTaskOutput
    {
        [JsonConstructor]
        public UpdateWebScraperTaskOutput(WebScraperTask webScraperTask)
        {
            WebScraperTask = webScraperTask;
        }

        [JsonProperty(WebScraperTask.PropertyNameWebScraperTask)]
        public WebScraperTask WebScraperTask { get; set; }
    }

    public async Task<UpdateWebScraperTaskOutput> F(UpdateWebScraperTaskInput updateWebScraperTaskInput)
    {
        var webScraperTask = await _webScraperService.UpdateWebScraperTaskAsync(
            updateWebScraperTaskInput.SleekflowCompanyId,
            updateWebScraperTaskInput.ApifyTaskId,
            updateWebScraperTaskInput.DisplayName,
            updateWebScraperTaskInput.WebScraperSetting);

        return new UpdateWebScraperTaskOutput(webScraperTask);
    }
}