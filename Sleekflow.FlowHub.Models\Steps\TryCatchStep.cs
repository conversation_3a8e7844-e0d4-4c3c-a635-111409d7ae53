using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Models.Steps;

public class TryCatchStep : Step
{
    [Required]
    [ValidateObject]
    [JsonProperty("try")]
    public TryCatchStepTry Try { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty("catch")]
    public TryCatchStepCatch Catch { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public override string Category => string.Empty;

    [JsonConstructor]
    public TryCatchStep(
        string id,
        string name,
        Assign? assign,
        string? nextStepId,
        TryCatchStepTry @try,
        TryCatchStepCatch @catch)
        : base(id, name, assign, nextStepId)
    {
        Try = @try;
        Catch = @catch;
    }
}

public class TryCatchStepTry
{
    [Required]
    [ValidateObject]
    [JsonProperty("step")]
    public Step Step { get; set; }

    [JsonConstructor]
    public TryCatchStepTry(Step step)
    {
        Step = step;
    }
}

public class TryCatchStepCatch
{
    [Required]
    [JsonProperty("as")]
    public string As { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty("step")]
    public Step Step { get; set; }

    [JsonConstructor]
    public TryCatchStepCatch(string @as, Step step)
    {
        As = @as;
        Step = step;
    }
}