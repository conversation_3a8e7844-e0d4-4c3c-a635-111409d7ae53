﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnVtexOrderCreatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnVtexOrderCreated; }
    }

    [JsonProperty("vtex_authentication_id")]
    public string VtexAuthenticationId { get; set; }

    [JsonProperty("status_code")]
    public string StatusCode { get; set; }

    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty("order")]
    public VtexOrderOverview Order { get; set; }

    [JsonConstructor]
    public OnVtexOrderCreatedEventBody(
        DateTimeOffset createdAt, // create time for the event, not the Order
        string vtexAuthenticationId,
        string statusCode,
        string orderId,
        VtexOrderOverview order)
        : base(createdAt)
    {
        VtexAuthenticationId = vtexAuthenticationId;
        StatusCode = statusCode;
        OrderId = orderId;
        Order = order;
    }
}