using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.WebCrawlingSessions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class GetWebCrawlingSession : ITrigger
{
    private readonly IWebCrawlingSessionService _webCrawlingSessionService;

    public GetWebCrawlingSession(IWebCrawlingSessionService webCrawlingSessionService)
    {
        _webCrawlingSessionService = webCrawlingSessionService;
    }

    public class GetWebCrawlingSessionInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("session_id")]
        [Required]
        public string SessionId { get; set; }

        [JsonConstructor]
        public GetWebCrawlingSessionInput(
            string sleekflowCompanyId,
            string sessionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SessionId = sessionId;
        }
    }

    public class GetWebCrawlingSessionOutput
    {
        [JsonProperty("web_crawling_session")]
        public WebCrawlingSession WebCrawlingSession { get; set; }

        [JsonConstructor]
        public GetWebCrawlingSessionOutput(WebCrawlingSession webCrawlingSession)
        {
            WebCrawlingSession = webCrawlingSession;
        }
    }

    [Function(nameof(GetWebCrawlingSession))]
    public async Task<GetWebCrawlingSessionOutput> Run(
        [ActivityTrigger]
        GetWebCrawlingSessionInput input)
    {
        var webCrawlingSession = await _webCrawlingSessionService.GetWebCrawlingSessionAsync(
            input.SleekflowCompanyId,
            input.SessionId);

        return new GetWebCrawlingSessionOutput(webCrawlingSession);
    }
}