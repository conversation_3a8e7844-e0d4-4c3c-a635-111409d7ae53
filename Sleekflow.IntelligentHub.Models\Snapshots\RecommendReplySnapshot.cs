using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class RecommendReplySnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public string ConversationContext { get; set; }

    [JsonProperty("knowledge_base_entries")]
    public string KnowledgeBaseEntries { get; set; }

    [JsonProperty("output_message")]
    public string OutputMessage { get; set; }

    [JsonConstructor]
    public RecommendReplySnapshot(string conversationContext, string knowledgeBaseEntries, string outputMessage)
    {
        ConversationContext = conversationContext;
        KnowledgeBaseEntries = knowledgeBaseEntries;
        OutputMessage = outputMessage;
    }
}