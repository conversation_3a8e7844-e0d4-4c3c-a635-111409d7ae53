﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScores;

public class LeadScoreEvaluatorScoringOutput(RawScores rawScores)
{
    [JsonProperty("raw_scores")]
    public RawScores RawScores { get; set; } = rawScores;
}

public class ReasoningMatch
{
    [JsonProperty("matchScore")]
    public double MatchScore { get; set; }

    [JsonProperty("expectedPath")]
    public List<string> ExpectedPath { get; set; } = new();

    [JsonProperty("actualPath")]
    public List<string> ActualPath { get; set; } = new();

    [JsonProperty("missedSteps")]
    public List<string> MissedSteps { get; set; } = new();

    [JsonProperty("extraSteps")]
    public List<string> ExtraSteps { get; set; } = new();

    [JsonProperty("stepMatchScore")]
    public double StepMatchScore { get; set; }
}

public class ReasoningQuality
{
    [JsonProperty("logicStructure")]
    public double LogicStructure { get; set; }

    [JsonProperty("validations")]
    public List<ValidationResult> Validations { get; set; } = new();

    [JsonProperty("contextAccuracy")]
    public double ContextAccuracy { get; set; }

    [JsonProperty("completeness")]
    public double Completeness { get; set; }

    [JsonProperty("totalQualityScore")]
    public double TotalQualityScore { get; set; }
}

public class ValidationResult
{
    [JsonProperty("step")]
    public string Step { get; set; } = string.Empty;

    [JsonProperty("expected")]
    public string Expected { get; set; } = string.Empty;

    [JsonProperty("actual")]
    public string Actual { get; set; } = string.Empty;

    [JsonProperty("isValid")]
    public bool IsValid { get; set; }
}

public class ReasoningIssues
{
    [JsonProperty("logicGaps")]
    public List<string> LogicGaps { get; set; } = new();

    [JsonProperty("invalidAssumptions")]
    public List<string> InvalidAssumptions { get; set; } = new();

    [JsonProperty("missingValidations")]
    public List<string> MissingValidations { get; set; } = new();

    [JsonProperty("totalDeduction")]
    public double TotalDeduction { get; set; }
}

public class RawScores
{
    [JsonConstructor]
    public RawScores(
        ReasoningMatch reasoningMatch,
        ReasoningQuality reasoningQuality,
        ReasoningIssues reasoningIssues,
        double baseScore,
        double finalScore,
        string reasoningAnalysis)
    {
        ReasoningMatch = reasoningMatch;
        ReasoningQuality = reasoningQuality;
        ReasoningIssues = reasoningIssues;
        BaseScore = baseScore;
        FinalScore = finalScore;
        ReasoningAnalysis = reasoningAnalysis;
    }

    [JsonProperty("reasoningMatch")]
    public ReasoningMatch ReasoningMatch { get; set; }

    [JsonProperty("reasoningQuality")]
    public ReasoningQuality ReasoningQuality { get; set; }

    [JsonProperty("reasoningIssues")]
    public ReasoningIssues ReasoningIssues { get; set; }

    [JsonProperty("baseScore")]
    public double BaseScore { get; set; }

    [JsonProperty("finalScore")]
    public double FinalScore { get; set; }

    [JsonProperty("reasoningAnalysis")]
    public string ReasoningAnalysis { get; set; }
}

public class LeadScoreCalculator
{
    private const double LogicGapPenalty = 0.5;
    private const double InvalidAssumptionPenalty = 0.3;
    private const double MissingValidationPenalty = 0.2;

    public class FinalScore
    {
        public double StepMatchScore { get; set; }
        public double QualityScore { get; set; }
        public double Deductions { get; set; }
        public double FinalAdjustedScore { get; set; }
    }

    public FinalScore CalculateFinalScore(RawScores rawScores)
    {
        // Calculate total reasoning issues penalties
        var totalPenalty =
            (rawScores.ReasoningIssues.LogicGaps.Count * LogicGapPenalty) +
            (rawScores.ReasoningIssues.InvalidAssumptions.Count * InvalidAssumptionPenalty) +
            (rawScores.ReasoningIssues.MissingValidations.Count * MissingValidationPenalty);

        // Calculate base score from reasoning match and quality
        var baseScore = CalculateBaseScore(rawScores.ReasoningMatch, rawScores.ReasoningQuality);

        var finalScore = new FinalScore
        {
            StepMatchScore = rawScores.ReasoningMatch.StepMatchScore,
            QualityScore = rawScores.ReasoningQuality.TotalQualityScore,
            Deductions = totalPenalty,
            FinalAdjustedScore = Math.Min(5, Math.Max(1, baseScore - totalPenalty))
        };

        return finalScore;
    }

    private static double CalculateBaseScore(ReasoningMatch match, ReasoningQuality quality)
    {
        // Step matching and reasoning structure (60%)
        double matchingScore = (
            (match.MatchScore * 0.25) +          // Overall match quality
            (match.StepMatchScore * 0.20) +      // Step-by-step match accuracy
            (quality.LogicStructure * 0.15)      // Logical flow and structure
        );

        // Quality and accuracy (40%)
        double qualityScore = (
            (quality.ContextAccuracy * 0.20) +    // Context understanding
            (quality.Completeness * 0.20)         // Solution completeness
        );

        return (matchingScore + qualityScore);
    }
}
