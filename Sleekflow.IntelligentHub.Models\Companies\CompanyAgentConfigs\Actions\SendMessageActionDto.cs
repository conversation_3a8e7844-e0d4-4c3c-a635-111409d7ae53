using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class SendMessageActionDto : BaseActionDto
{
    [JsonProperty("response_type")]
    public string ResponseType { get; set; }

    [JsonProperty("instructions")]
    public string Instructions { get; set; }

    [JsonConstructor]
    public SendMessageActionDto(bool enabled, string responseType, string instructions)
        : base(enabled)
    {
        ResponseType = responseType;
        Instructions = instructions;
    }

    public SendMessageActionDto(SendMessageAction action)
        : base(action)
    {
        ResponseType = action.ResponseType;
        Instructions = action.Instructions;
    }
}