using Sleekflow.IntelligentHub.Triggers.Blobs;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class BlobsCoreIntegrationTests
{
    private const string SleekflowCompanyId = "sample-company-id";

    [SetUp]
    public void TestSetUp()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test requires ShareHub to run in the background");
        }
    }

    [Test]
    public async Task BlobsTest()
    {
        var createBlobUploadSasUrlsInput = new CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsInput(
            SleekflowCompanyId,
            2,
            "File");

        var createBlobUploadSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createBlobUploadSasUrlsInput).ToUrl("/Blobs/CreateBlobUploadSasUrls");
            });

        var createBlobUploadSasUrlsOutputOutput =
            await createBlobUploadSasUrlsScenarioResult
                .ReadAsJsonAsync<Output<CreateBlobUploadSasUrls.CreateBlobUploadSasUrlsOutput>>();

        var createBlobUploadSasUrlsOutput = createBlobUploadSasUrlsOutputOutput?.Data;

        Assert.That(createBlobUploadSasUrlsOutputOutput, Is.Not.Null);
        Assert.That(createBlobUploadSasUrlsOutput?.UploadBlobs, Is.Not.Null);
        Assert.That(createBlobUploadSasUrlsOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var uploadBlobs = createBlobUploadSasUrlsOutput!.UploadBlobs;
        var blobNames = uploadBlobs
            .Where(e => e.BlobName != null)
            .Select(e => e.BlobName!)
            .ToList();

        var createBlobDownloadSasUrlsInput = new CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsInput(
            SleekflowCompanyId,
            blobNames,
            "File");

        var createBlobDownloadSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createBlobDownloadSasUrlsInput).ToUrl("/Blobs/CreateBlobDownloadSasUrls");
            });

        var createBlobDownloadSasUrlsOutputOutput =
            await createBlobDownloadSasUrlsScenarioResult
                .ReadAsJsonAsync<Output<CreateBlobDownloadSasUrls.CreateBlobDownloadSasUrlsOutput>>();

        var createBlobDownloadSasUrlsOutput = createBlobDownloadSasUrlsOutputOutput?.Data;

        Assert.That(createBlobDownloadSasUrlsOutput, Is.Not.Null);
        Assert.That(createBlobDownloadSasUrlsOutput?.DownloadBlobs, Is.Not.Null);
        Assert.That(createBlobDownloadSasUrlsOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var createDeleteBlobsInput = new DeleteBlobs.DeleteBlobsInput(
            SleekflowCompanyId,
            blobNames,
            "File");

        var createDeleteBlobsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createDeleteBlobsInput).ToUrl("/Blobs/DeleteBlobs");
            });

        var createDeleteBlobsOutputOutput =
            await createDeleteBlobsScenarioResult
                .ReadAsJsonAsync<Output<DeleteBlobs.DeleteBlobsOutput>>();

        var createDeleteBlobsOutput = createDeleteBlobsOutputOutput?.Data;

        Assert.That(createDeleteBlobsOutput, Is.Not.Null);
        Assert.That(createDeleteBlobsOutput?.BlobNames, Is.Not.Null);
        Assert.That(createDeleteBlobsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }
}