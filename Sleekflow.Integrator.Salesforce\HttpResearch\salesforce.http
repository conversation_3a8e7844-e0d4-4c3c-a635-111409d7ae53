POST https://sleekflow-dev-ed.my.salesforce.com/services/oauth2/token HTTP/1.1
Content-Type: application/x-www-form-urlencoded

grant_type=password
&client_id=3MVG9wt4IL4O5wvJVHcTe2hOnRGRb51O49poWGUQodkxM2vKip5gpWgjay2CBcYWk04wiZIJI8ifQuX80dNUp
&client_secret=****************************************************************
&username=<EMAIL>
&password=Fd#DHSL!8s

### Get a List of Salesforce Versions

GET {{instance_url}}/services/data/ HTTP/1.1

### Get a List of Resources (Modules)

GET {{instance_url}}/services/data/v54.0/ HTTP/1.1
Authorization: Bearer {{access_token}}

### Get a List of Available Objects

GET {{instance_url}}/services/data/v54.0/sobjects/ HTTP/1.1
Authorization: Bearer {{access_token}}
