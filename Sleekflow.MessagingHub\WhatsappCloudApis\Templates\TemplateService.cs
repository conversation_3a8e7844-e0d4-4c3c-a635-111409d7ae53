using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Models.MessageObjects.TemplateObjects;
using GraphApi.Client.Payloads;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Templates;

public interface ITemplateService
{
    Task<string> CreateCloudApiWhatsappTemplateAsync(
        string facebookWabaId,
        WhatsappCloudApiCreateTemplateObject whatsappCloudApiCreateTemplateObject,
        string? businessIntegrationSystemUserAccessToken);

    Task<List<WhatsappCloudApiTemplate>> GetCloudApiWhatsappTemplateAsync(
        string facebookWabaId,
        string? businessIntegrationSystemUserAccessToken);

    Task<bool> EditCloudApiWhatsappTemplateAsync(
        string templateId,
        List<WhatsappCloudApiTemplateComponentObject> whatsappCloudApiTemplateComponentObjects,
        string? businessIntegrationSystemUserAccessToken);

    Task<bool> DeleteCloudApiWhatsappTemplateAsync(string facebookWabaId, string templateName,
        string? businessIntegrationSystemUserAccessToken);

    Task<string> UploadTemplateHeaderFileAsync(
        string facebookWabaId,
        long fileLength,
        string fileType,
        string fileName,
        byte[] fileBytes,
        string? businessIntegrationSystemUserAccessToken);
}

public class TemplateService : ITemplateService, ISingletonService
{
    private readonly ILogger<TemplateService> _logger;
    private readonly IWhatsappCloudApiMessagingClient _whatsappCloudApiMessagingClient;
    private readonly IWhatsappCloudApiBspClient _whatsappCloudApiBspClient;
    private readonly HttpClient _httpClient;

    public TemplateService(
        ILogger<TemplateService> logger,
        ICloudApiClients cloudApiClients,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _whatsappCloudApiMessagingClient = cloudApiClients.WhatsappCloudApiMessagingClient;
        _whatsappCloudApiBspClient = cloudApiClients.WhatsappCloudApiBspClient;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<string> CreateCloudApiWhatsappTemplateAsync(
        string facebookWabaId,
        WhatsappCloudApiCreateTemplateObject whatsappCloudApiCreateTemplateObject,
        string? businessIntegrationSystemUserAccessToken)
    {
        try
        {
            var whatsappCloudApiMessagingClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new WhatsappCloudApiMessagingClient(
                    businessIntegrationSystemUserAccessToken,
                    _httpClient)
                : _whatsappCloudApiMessagingClient;

            var createCloudApiWhatsappTemplateResponse =
                await whatsappCloudApiMessagingClient.CreateMessageTemplateAsync(
                    facebookWabaId,
                    whatsappCloudApiCreateTemplateObject);
            return createCloudApiWhatsappTemplateResponse.Id;
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException g)
            {
                throw new SfGraphApiErrorException(
                    $"Unable to create template.",
                    JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
            }

            throw;
        }
    }

    public async Task<List<WhatsappCloudApiTemplate>> GetCloudApiWhatsappTemplateAsync(
        string facebookWabaId,
        string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiMessagingClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new WhatsappCloudApiMessagingClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient)
            : _whatsappCloudApiMessagingClient;

        var res = new List<WhatsappCloudApiTemplate>();

        const int limit = 2000;
        string? after = null;
        while (true)
        {
            var templates = await whatsappCloudApiMessagingClient.GetTemplatesAsync(
                facebookWabaId,
                paginationParam: new CursorBasedPaginationParam
                {
                    After = after,
                    Limit = limit
                });
            after = templates.Paging?.Cursors?.After;
            res.AddRange(templates.Data);
            if (templates.Data.Count < limit || string.IsNullOrEmpty(after))
            {
                break;
            }
        }

        return res;
    }

    public async Task<bool> EditCloudApiWhatsappTemplateAsync(
        string templateId,
        List<WhatsappCloudApiTemplateComponentObject> whatsappCloudApiTemplateComponentObjects,
        string? businessIntegrationSystemUserAccessToken)
    {
        try
        {
            var whatsappCloudApiMessagingClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new WhatsappCloudApiMessagingClient(
                    businessIntegrationSystemUserAccessToken,
                    _httpClient)
                : _whatsappCloudApiMessagingClient;

            var editMessageTemplateResponse = await whatsappCloudApiMessagingClient.EditMessageTemplateAsync(
                templateId,
                whatsappCloudApiTemplateComponentObjects);
            return editMessageTemplateResponse.Success;
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException g)
            {
                throw new SfGraphApiErrorException(
                    $"Unable to create template.",
                    JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
            }

            throw;
        }
    }

    public async Task<bool> DeleteCloudApiWhatsappTemplateAsync(string facebookWabaId, string templateName,
        string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiMessagingClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new WhatsappCloudApiMessagingClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient)
            : _whatsappCloudApiMessagingClient;

        var deleteMessageTemplateStatus = await whatsappCloudApiMessagingClient.DeleteMessageTemplateAsync(
            facebookWabaId,
            templateName);
        return deleteMessageTemplateStatus.Success;
    }

    public async Task<string> UploadTemplateHeaderFileAsync(
        string facebookWabaId,
        long fileLength,
        string fileType,
        string fileName,
        byte[] fileBytes,
        string? businessIntegrationSystemUserAccessToken)
    {
        var resumableUploadInitiateUploadResponse =
            new ResumableUploadInitiateUploadResponse();

        var uploadSessionId = string.Empty;

        try
        {
            var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new WhatsappCloudApiBspClient(
                    businessIntegrationSystemUserAccessToken,
                    _httpClient)
                : _whatsappCloudApiBspClient;

            var createTemplateHeaderFileUploadSessionResponse =
                await whatsappCloudApiBspClient.ResumableUploadCreateSessionAsync(fileLength, fileType, fileName);

            if (!string.IsNullOrEmpty(createTemplateHeaderFileUploadSessionResponse.Id))
            {
                uploadSessionId = createTemplateHeaderFileUploadSessionResponse.Id;

                resumableUploadInitiateUploadResponse =
                    await whatsappCloudApiBspClient.ResumableUploadInitiateUploadAsync(
                        uploadSessionId,
                        0,
                        fileBytes,
                        fileType);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                "Caught error for facebook waba id {FacebookWabaId}, Upload Session Id {UploadSessionId}, File Type {FileType}, File Name {FileName} Exception {Ex}",
                facebookWabaId,
                uploadSessionId,
                fileType,
                fileName,
                JsonConvert.SerializeObject(ex));

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    "Unable to upload the file",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }
        }

        return resumableUploadInitiateUploadResponse.H;
    }
}