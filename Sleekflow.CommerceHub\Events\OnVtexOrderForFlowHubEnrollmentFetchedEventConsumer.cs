﻿using MassTransit;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.CommerceHub.Events;

public class OnVtexOrderForFlowHubEnrollmentFetchedEventConsumerDefinition : ConsumerDefinition<OnVtexOrderForFlowHubEnrollmentFetchedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnVtexOrderForFlowHubEnrollmentFetchedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnVtexOrderForFlowHubEnrollmentFetchedEventConsumer : IConsumer<OnVtexOrderForFlowHubEnrollmentFetchedEvent>
{
    private readonly IVtexOrderCommander _vtexOrderCommander;
    private readonly IBus _bus;
    private readonly ILogger<OnVtexOrderForFlowHubEnrollmentFetchedEventConsumer> _logger;
    private readonly IVtexAuthenticationService _vtexAuthenticationService;

    public OnVtexOrderForFlowHubEnrollmentFetchedEventConsumer(
        IVtexOrderCommander vtexOrderCommander,
        IBus bus,
        ILogger<OnVtexOrderForFlowHubEnrollmentFetchedEventConsumer> logger,
        IVtexAuthenticationService vtexAuthenticationService)
    {
        _vtexOrderCommander = vtexOrderCommander;
        _bus = bus;
        _logger = logger;
        _vtexAuthenticationService = vtexAuthenticationService;
    }

    public async Task Consume(ConsumeContext<OnVtexOrderForFlowHubEnrollmentFetchedEvent> context)
    {
        var @event = context.Message;

        var sleekflowCompanyId = @event.SleekflowCompanyId;
        var vtexAuthenticationId = @event.VtexAuthenticationId;
        var orderId = @event.OrderId;

        // authentication
        var vtexAuthentication = await _vtexAuthenticationService.GetAsync(
            vtexAuthenticationId,
            sleekflowCompanyId,
            context.CancellationToken);

        if (vtexAuthentication == null)
        {
            _logger.LogWarning(
                "OnVtexOrderForFlowHubEnrollmentFetchedEventConsumer: vtex authentication not found. {VtexAuthenticationId} {CompanyId}",
                vtexAuthenticationId,
                sleekflowCompanyId);

            return;
        }

        // get order detail
        var vtexOrder = await _vtexOrderCommander.GetOrderAsync(
            vtexAuthentication.Credential,
            orderId,
            context.CancellationToken);

        var vtexOrderCreatedEventRequest = new OnVtexOrderEnrollmentToFlowHubRequestedEvent(
            sleekflowCompanyId,
            @event.FlowHubWorkflowId,
            @event.FlowHubWorkflowVersionedId,
            vtexAuthenticationId,
            vtexOrder.Status,
            vtexOrder.OrderId,
            vtexOrder.ToVtexOrderOverview());

        await _bus.Publish(vtexOrderCreatedEventRequest, context.CancellationToken);
    }
}