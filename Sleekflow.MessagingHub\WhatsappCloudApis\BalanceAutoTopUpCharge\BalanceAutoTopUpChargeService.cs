﻿using System.Globalization;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Events.OnCloudApiBalanceAutoTopUpEvents;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Stripe;
using IStripeClient = Sleekflow.MessagingHub.Payments.IStripeClient;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.BalanceAutoTopUpCharge;

public interface IBalanceAutoTopUpChargeService
{
    Task ChargeAutoTopUpFee(OnCloudApiBusinessBalanceAutoTopUpEvent onCloudApiBusinessBalanceAutoTopUpEvent);

    Task ChargeAutoTopUpFee(OnCloudApiWabaBalanceAutoTopUpEvent onCloudApiWabaBalanceAutoTopUpEvent);
}

public class BalanceAutoTopUpChargeService : IBalanceAutoTopUpChargeService, ISingletonService
{
    private readonly IStripeClient _stripeClient;
    private readonly ILogger<BalanceAutoTopUpChargeService> _logger;

    public BalanceAutoTopUpChargeService(IStripeClient stripeClient, ILogger<BalanceAutoTopUpChargeService> logger)
    {
        _stripeClient = stripeClient;
        _logger = logger;
    }

    public async Task ChargeAutoTopUpFee(OnCloudApiBusinessBalanceAutoTopUpEvent onCloudApiBusinessBalanceAutoTopUpEvent)
    {
        var facebookBusinessId = onCloudApiBusinessBalanceAutoTopUpEvent.FacebookBusinessId;
        var businessBalanceId = onCloudApiBusinessBalanceAutoTopUpEvent.BusinessBalanceId;
        var customerId = onCloudApiBusinessBalanceAutoTopUpEvent.CustomerId;
        var autoTopUpPlan = onCloudApiBusinessBalanceAutoTopUpEvent.AutoTopUpPlan;

        await ChargeAutoTopUpFeeWithStripeAsync(facebookBusinessId, null, businessBalanceId, customerId, autoTopUpPlan);
    }

    public async Task ChargeAutoTopUpFee(OnCloudApiWabaBalanceAutoTopUpEvent onCloudApiWabaBalanceAutoTopUpEvent)
    {
        var facebookBusinessId = onCloudApiWabaBalanceAutoTopUpEvent.FacebookBusinessId;
        var facebookWabaId = onCloudApiWabaBalanceAutoTopUpEvent.FacebookWabaId;
        var businessBalanceId = onCloudApiWabaBalanceAutoTopUpEvent.BusinessBalanceId;
        var customerId = onCloudApiWabaBalanceAutoTopUpEvent.CustomerId;
        var autoTopUpPlan = onCloudApiWabaBalanceAutoTopUpEvent.AutoTopUpPlan;

        await ChargeAutoTopUpFeeWithStripeAsync(
            facebookBusinessId,
            facebookWabaId,
            businessBalanceId,
            customerId,
            autoTopUpPlan);
    }

    private async Task ChargeAutoTopUpFeeWithStripeAsync(
        string facebookBusinessId,
        string? facebookWabaId,
        string businessBalanceId,
        string customerId,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan)
    {
        ArgumentNullException.ThrowIfNull(customerId);

        // Stripe has two types of payment methods: PaymentMethod and Source both can be used to pay invoices
        var hasDefaultPaymentMethod =
            await _stripeClient.HasDefaultPaymentMethodAsync(customerId);
        InvoicePayOptions? invoicePayOptions = null;

        var (hasDefaultSource, sourceId) =
            await _stripeClient.FindCustomerDefaultSourceAsync(customerId);

        if (!hasDefaultPaymentMethod && hasDefaultSource)
        {
            invoicePayOptions = new InvoicePayOptions
            {
                Source = sourceId
            };
        }

        if (!hasDefaultPaymentMethod && !hasDefaultSource)
        {
            var latestUsedPaymentMethodId =
                await FindLatestPaymentMethodIdUsedForSubscriptionForCustomerAsync(
                    customerId);

            var latestUsedSourceId = await FindLatestSourceIdUsedForSubscriptionForCustomerAsync(customerId);

            if (latestUsedPaymentMethodId is not null)
            {
                invoicePayOptions = new InvoicePayOptions
                {
                    PaymentMethod = latestUsedPaymentMethodId
                };
            }
            else if (latestUsedSourceId is not null)
            {
                invoicePayOptions = new InvoicePayOptions
                {
                    Source = latestUsedSourceId
                };
            }

            if (latestUsedPaymentMethodId is null && latestUsedSourceId is null)
            {
                throw new SfNotFoundObjectException(
                    "Cannot find any default payment method or payment method used for subscriptions");
            }
        }

        var invoiceId = await CreateAutoTopUpFeeInvoiceAsync(
            facebookBusinessId,
            facebookWabaId,
            businessBalanceId,
            customerId,
            autoTopUpPlan);

        await PayAutoTopUpFeeInvoiceAsync(invoiceId, invoicePayOptions);
    }

    private static async Task<string> CreateAutoTopUpFeeInvoiceAsync(
        string facebookBusinessId,
        string? facebookWabaId,
        string businessBalanceId,
        string customerId,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan)
    {
        var metadata = ConstructAutoTopUpFeeInvoiceMetadata(
            facebookBusinessId,
            facebookWabaId,
            businessBalanceId,
            customerId,
            autoTopUpPlan);

        var options = new InvoiceCreateOptions
        {
            Customer = customerId,
            CollectionMethod = "charge_automatically",
            Metadata = metadata,
            Currency = autoTopUpPlan.Price.CurrencyIsoCode
        };

        var service = new InvoiceService();

        var invoice = await service.CreateAsync(options);

        var invoiceItemOptions = new InvoiceItemCreateOptions
        {
            Price = autoTopUpPlan.Id,
            Currency = autoTopUpPlan.Price.CurrencyIsoCode,
            Invoice = invoice.Id,
            Customer = customerId
        };

        var invoiceItemService = new InvoiceItemService();

        await invoiceItemService.CreateAsync(invoiceItemOptions);

        return invoice.Id;
    }

    private async Task<string?> FindLatestPaymentMethodIdUsedForSubscriptionForCustomerAsync(string customerId)
    {
        var paymentMethodsUsedForSubscriptions =
            await _stripeClient.FindPaymentMethodsUsedForSubscriptionsForCustomerAsync(customerId);

        if (paymentMethodsUsedForSubscriptions.Count == 0)
        {
            return null;
        }

        var latestPaymentMethodUsedForSubscription = GetLatestPaymentMethod(paymentMethodsUsedForSubscriptions);

        return latestPaymentMethodUsedForSubscription?.Id;
    }

    private async Task<string?> FindLatestSourceIdUsedForSubscriptionForCustomerAsync(string customerId)
    {
        var sourcesUsedForSubscriptions =
            await _stripeClient.FindSourcesUsedForSubscriptionsForCustomerAsync(customerId);

        if (sourcesUsedForSubscriptions.Count == 0)
        {
            return null;
        }

        var latestSourcesUsedForSubscription = sourcesUsedForSubscriptions.MaxBy(x => x.Created);

        return latestSourcesUsedForSubscription?.Id;
    }

    private static Dictionary<string, string> ConstructAutoTopUpFeeInvoiceMetadata(
        string facebookBusinessId,
        string? facebookWabaId,
        string businessBalanceId,
        string customerId,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan)
    {
        var metadata = new Dictionary<string, string>
        {
            {
                "source", "messaging-hub"
            },
            {
                "facebook_business_id", facebookBusinessId
            },
            {
                "business_balance_id", businessBalanceId
            },
            {
                "customer_id", customerId
            },
            {
                "type", TopUpTypes.WhatsappCloudApiAutoTopUp
            },
            {
                "whatsapp_cloud_api_top_up_plan_id", autoTopUpPlan.Id
            },
            {
                "whatsapp_cloud_api_top_up_plan_amount",
                autoTopUpPlan.Price.Amount.ToString(CultureInfo.InvariantCulture)
            },
            {
                "whatsapp_cloud_api_top_up_plan_currency", autoTopUpPlan.Price.CurrencyIsoCode
            }
        };

        if (!string.IsNullOrEmpty(facebookWabaId))
        {
            metadata.Add("facebook_waba_id", facebookWabaId);
        }

        return metadata;
    }

    private static async Task PayAutoTopUpFeeInvoiceAsync(string invoiceId, InvoicePayOptions? invoicePayOptions = null)
    {
        var service = new InvoiceService();
        await service.PayAsync(invoiceId, invoicePayOptions);
    }

    private static PaymentMethod? GetLatestPaymentMethod(List<PaymentMethod> paymentMethods)
    {
        return paymentMethods.MaxBy(x => x.Created);
    }
}