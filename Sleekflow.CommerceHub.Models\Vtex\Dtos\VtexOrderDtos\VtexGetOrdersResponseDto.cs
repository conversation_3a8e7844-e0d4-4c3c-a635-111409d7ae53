﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

/// <summary>
/// Partial response body of
/// <br/><br/>https://developers.vtex.com/docs/api-reference/orders-api#get-/api/oms/pvt/orders
/// </summary>
public class VtexGetOrdersResponseDto
{
    [JsonProperty("list")]
    public List<SimplifiedOrderDto> Orders { get; set; }

    [JsonProperty("paging")]
    public PagingDto Paging { get; set; }

    [JsonConstructor]
    public VtexGetOrdersResponseDto(List<SimplifiedOrderDto> orders, PagingDto paging)
    {
        Orders = orders;
        Paging = paging;
    }

    public class SimplifiedOrderDto
    {
        [JsonProperty("orderId")]
        public string OrderId { get; set; }

        [JsonProperty("status")]
        public string StatusCode { get; set; }

        [JsonProperty("clientName")]
        public string ClientName { get; set; }

        [JsonProperty("creationDate")]
        public DateTime CreationDate { get; set; }

        [JsonConstructor]
        public SimplifiedOrderDto(string orderId, string statusCode, string clientName, DateTime creationDate)
        {
            OrderId = orderId;
            StatusCode = statusCode;
            ClientName = clientName;
            CreationDate = creationDate;
        }
    }

    public class PagingDto
    {
        [JsonProperty("total")]
        public int Total { get; set; }

        [JsonProperty("Pages")]
        public int Pages { get; set; }

        [JsonProperty("currentPage")]
        public int CurrentPage { get; set; }

        [JsonProperty("perPage")]
        public int PerPage { get; set; }

        [JsonConstructor]
        public PagingDto(int total, int pages, int currentPage, int perPage)
        {
            Total = total;
            Pages = pages;
            CurrentPage = currentPage;
            PerPage = perPage;
        }
    }
}