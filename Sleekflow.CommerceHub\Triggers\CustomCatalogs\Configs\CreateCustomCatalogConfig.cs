using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.CustomCatalogs.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs.Configs;

[TriggerGroup(ControllerNames.CustomCatalogConfigs)]
public class CreateCustomCatalogConfig
    : ITrigger<
        CreateCustomCatalogConfig.CreateCustomCatalogConfigInput,
        CreateCustomCatalogConfig.CreateCustomCatalogConfigOutput>
{
    private readonly ICustomCatalogConfigService _customCatalogConfigService;

    public CreateCustomCatalogConfig(ICustomCatalogConfigService customCatalogConfigService)
    {
        _customCatalogConfigService = customCatalogConfigService;
    }

    public class CreateCustomCatalogConfigInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("type")]
        public string Type { get; set; }

        [Required]
        [JsonProperty("count")]
        // [MaxLength(100000)]
        public int Count { get; set; }

        [Required]
        [JsonProperty("period_start")]
        public DateTimeOffset PeriodStart { get; set; }

        [Required]
        [JsonProperty("period_end")]
        public DateTimeOffset PeriodEnd { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateCustomCatalogConfigInput(
            string sleekflowCompanyId,
            string type,
            int count,
            DateTimeOffset periodStart,
            DateTimeOffset periodEnd,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Type = type;
            Count = count;
            PeriodStart = periodStart;
            PeriodEnd = periodEnd;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateCustomCatalogConfigOutput
    {
    }

    public async Task<CreateCustomCatalogConfigOutput> F(CreateCustomCatalogConfigInput createCustomCatalogConfigInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createCustomCatalogConfigInput.SleekflowStaffId,
            createCustomCatalogConfigInput.SleekflowStaffTeamIds);

        await _customCatalogConfigService.CreateCustomCatalogConfigAsync(
            createCustomCatalogConfigInput.SleekflowCompanyId,
            createCustomCatalogConfigInput.Type,
            createCustomCatalogConfigInput.Count,
            createCustomCatalogConfigInput.PeriodStart,
            createCustomCatalogConfigInput.PeriodEnd,
            sleekflowStaff);
        return new CreateCustomCatalogConfigOutput();
    }
}