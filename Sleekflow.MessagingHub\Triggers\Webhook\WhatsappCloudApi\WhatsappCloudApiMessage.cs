using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Webhooks;

namespace Sleekflow.MessagingHub.Triggers.Webhook.WhatsappCloudApi;

public class WhatsappCloudApiMessage : ITrigger
{
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;

    public WhatsappCloudApiMessage(IMessagingHubWebhookService messagingHubWebhookService)
    {
        _messagingHubWebhookService = messagingHubWebhookService;
    }

    public async Task<bool> F(string message)
    {
        return await _messagingHubWebhookService.HandleWhatsappCloudApiWebhookMessage(message);
    }
}