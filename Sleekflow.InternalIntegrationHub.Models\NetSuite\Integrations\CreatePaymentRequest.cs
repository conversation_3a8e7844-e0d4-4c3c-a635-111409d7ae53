using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class CreatePaymentRequest
{
    // Customer ID
    // Need to use this ID to fetch Customer Details from NetSuite
    // This field in NetSuite Customer record field is called "externalId"
    [Required]
    [JsonProperty("customer_id")]
    public string CustomerId { get; set; }

    // Bill Record ID
    // This field in NetSuite Invoice record field is called "externalId"
    [Required]
    [JsonProperty("bill_record_id")]
    public string BillRecordId { get; set; }

    // Amount
    // See would it possible they can split to three categories
    // (Subscription Fee, One Time Setup Fee, Whatsapp Credit Amount)
    [Required]
    [JsonProperty("subscription_fee")]
    public decimal SubscriptionFee { get; set; }

    [Required]
    [JsonProperty("one_time_setup_fee")]
    public decimal OneTimeSetupFee { get; set; }

    [Required]
    [JsonProperty("whatsapp_credit_amount")]
    public decimal WhatsappCreditAmount { get; set; }

    // Currency
    [Required]
    [JsonProperty("currency")]
    public string Currency { get; set; }

    // Payment Date
    [Required]
    [JsonProperty("payment_date")]
    public DateTime PaidAt { get; set; }

    // Invoice ID
    [Required]
    [JsonProperty("invoice_id")]
    public string InvoiceId { get; set; }

    // Payment Terms
    [Required]
    [JsonProperty("payment_terms")]
    public string PaymentTerms { get; set; }

    // CreateUserId
    // Need to use this ID to fetch Employee Details from NetSuite
    // This field in NetSuite Employee record field is called "externalId"
    [Required]
    [JsonProperty("sales_rep_id")]
    public string CreateUserId { get; set; }

    [JsonConstructor]
    public CreatePaymentRequest(
        string customerId,
        string billRecordId,
        decimal subscriptionFee,
        decimal oneTimeSetupFee,
        decimal whatsappCreditAmount,
        string currency,
        DateTime paidAt,
        string invoiceId,
        string paymentTerms,
        string createUserId)
    {
        CustomerId = customerId;
        BillRecordId = billRecordId;
        SubscriptionFee = subscriptionFee;
        OneTimeSetupFee = oneTimeSetupFee;
        WhatsappCreditAmount = whatsappCreditAmount;
        Currency = currency;
        PaidAt = paidAt;
        InvoiceId = invoiceId;
        PaymentTerms = paymentTerms;
        CreateUserId = createUserId;
    }
}