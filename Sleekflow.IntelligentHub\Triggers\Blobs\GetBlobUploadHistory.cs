using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Blobs;

[TriggerGroup(ControllerNames.Blobs)]
public class GetBlobUploadHistory
    : ITrigger<
        GetBlobUploadHistory.GetBlobUploadHistoryInput,
        GetBlobUploadHistory.GetBlobUploadHistoryOutput>
{
    private readonly IBlobUploadHistoryService _blobUploadHistoryService;

    public GetBlobUploadHistory(IBlobUploadHistoryService blobUploadHistoryService)
    {
        _blobUploadHistoryService = blobUploadHistoryService;
    }

    public class GetBlobUploadHistoryInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(BlobUploadHistory.PropertyNameBlobId)]
        public string BlobId { get; set; }

        [JsonConstructor]
        public GetBlobUploadHistoryInput(string sleekflowCompanyId, string blobId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
        }
    }

    public class GetBlobUploadHistoryOutput
    {
        [JsonProperty(SysTypeNames.BlobUploadHistory)]
        public BlobUploadHistory BlobUploadHistory { get; set; }

        [JsonConstructor]
        public GetBlobUploadHistoryOutput(BlobUploadHistory blobUploadHistory)
        {
            BlobUploadHistory = blobUploadHistory;
        }
    }

    public async Task<GetBlobUploadHistoryOutput> F(GetBlobUploadHistoryInput getBlobUploadHistoryInput)
    {
        var getBlobUploadHistory = await _blobUploadHistoryService.GetBlobUploadHistory(
            getBlobUploadHistoryInput.SleekflowCompanyId,
            getBlobUploadHistoryInput.BlobId);

        return new GetBlobUploadHistoryOutput(getBlobUploadHistory);
    }
}