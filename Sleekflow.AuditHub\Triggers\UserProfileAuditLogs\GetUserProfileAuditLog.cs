﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class GetUserProfileAuditLog : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;

    public GetUserProfileAuditLog(
        IUserProfileAuditLogService userProfileAuditLogService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
    }

    public class GetUserProfileAuditLogInput : IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [JsonConstructor]
        public GetUserProfileAuditLogInput(string id, string sleekflowUserProfileId)
        {
            Id = id;
            SleekflowUserProfileId = sleekflowUserProfileId;
        }
    }

    public class GetUserProfileAuditLogOutput
    {
        [JsonProperty("user_profile_audit_log")]
        public UserProfileAuditLog UserProfileAuditLog { get; set; }

        [JsonConstructor]
        public GetUserProfileAuditLogOutput(UserProfileAuditLog userProfileAuditLog)
        {
            UserProfileAuditLog = userProfileAuditLog;
        }
    }

    public async Task<GetUserProfileAuditLogOutput> F(GetUserProfileAuditLogInput getUserProfileAuditLogInput)
    {
        var userProfileAuditLog = await _userProfileAuditLogService.GetUserProfileAuditLogAsync(
            getUserProfileAuditLogInput.Id,
            getUserProfileAuditLogInput.SleekflowUserProfileId);

        return new GetUserProfileAuditLogOutput(userProfileAuditLog);
    }
}