using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SleepV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sys.v2.sleep";

    [Required]
    [JsonProperty("time_unit")]
    public string TimeUnit { get; set; }

    [Required]
    [JsonProperty("units__expr")]
    public string UnitsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => string.Empty;

    [JsonConstructor]
    public SleepV2StepArgs(
        string timeUnit,
        string unitsExpr)
    {
        TimeUnit = timeUnit;
        UnitsExpr = unitsExpr;
    }
}