﻿using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.StepExecutions;

public interface IStepExecutionRepository : IRepository<StepExecution>
{
    Task<int> CountStateStepNodeExecutionsAsync(
        string sleekflowCompanyId,
        string stateId);
}

public class StepExecutionRepository : BaseRepository<StepExecution>, IStepExecutionRepository, IScopedService
{
    public StepExecutionRepository(
        ILogger<StepExecutionRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<int> CountStateStepNodeExecutionsAsync(
        string sleekflowCompanyId,
        string stateId)
    {
        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
                """
                SELECT VALUE COUNT(1)
                FROM (SELECT r.id
                    FROM %%CONTAINER_NAME%% r
                    WHERE r.sleekflow_company_id = @sleekflow_company_id
                        AND r.state_id = @state_id
                        AND r.step_node_id != null
                        AND r.step_id = r.step_node_id
                        AND r.step_execution_status != 'Started')
                """)
            .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
            .WithParameter("@state_id", stateId);

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int?>(qd);
        var queryResult = await itemQueryIterator.ReadNextAsync();

        return queryResult.FirstOrDefault() ?? 0;
    }
}