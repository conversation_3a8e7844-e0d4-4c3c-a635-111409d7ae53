﻿using Sleekflow.Exceptions;

namespace Sleekflow.Events.ServiceBus;

public interface IServiceBusConfig
{
    string ServiceBusConnStr { get; }

    string? HighTrafficServiceBusConnStr { get; }
}

public class ServiceBusConfig : IServiceBusConfig
{
    public string ServiceBusConnStr { get; private set; }

    public string? HighTrafficServiceBusConnStr { get; private set; }

    public ServiceBusConfig()
    {
        ServiceBusConnStr = Environment.GetEnvironmentVariable(
                                "SERVICE_BUS_CONN_STR",
                                EnvironmentVariableTarget.Process) ??
                            throw new SfMissingEnvironmentVariableException("SERVICE_BUS_CONN_STR");

        HighTrafficServiceBusConnStr = Environment.GetEnvironmentVariable(
            "HIGH_TRAFFIC_SERVICE_BUS_CONN_STR",
            EnvironmentVariableTarget.Process);
    }
}