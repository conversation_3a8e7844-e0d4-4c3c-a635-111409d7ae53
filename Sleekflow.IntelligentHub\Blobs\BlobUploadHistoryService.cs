using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents;

namespace Sleekflow.IntelligentHub.Blobs;

public interface IBlobUploadHistoryService
{
    Task<BlobUploadHistory> RecordBlobUploadHistory(
        string sleekflowCompanyId,
        string blobId,
        string fileName,
        string uploadedBy,
        string sourceName,
        string blobType);

    Task<BlobUploadHistory> GetBlobUploadHistory(
        string sleekflowCompanyId,
        string blobId);

    Task DeleteBlobUploadHistory(
        string sleekflowCompanyId,
        string blobId);
}

public class BlobUploadHistoryService : IScopedService, IBlobUploadHistoryService
{
    private readonly ILogger<BlobUploadHistoryService> _logger;
    private readonly IIdService _idService;
    private readonly IBlobUploadHistoryRepository _blobUploadHistoryRepository;

    public BlobUploadHistoryService(
        ILogger<BlobUploadHistoryService> logger,
        IIdService idService,
        IBlobUploadHistoryRepository blobUploadHistoryRepository)
    {
        _logger = logger;
        _idService = idService;
        _blobUploadHistoryRepository = blobUploadHistoryRepository;
    }

    public async Task<BlobUploadHistory> RecordBlobUploadHistory(
        string sleekflowCompanyId,
        string blobId,
        string fileName,
        string uploadedBy,
        string sourceName,
        string blobType)
    {
        var histories = await _blobUploadHistoryRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.BlobId == blobId);
        if (histories.Any())
        {
            return histories.First();
        }

        var blobUploadHistory = await _blobUploadHistoryRepository.CreateAndGetAsync(
            new BlobUploadHistory(
                _idService.GetId(SysTypeNames.BlobUploadHistory),
                sleekflowCompanyId,
                blobId,
                fileName,
                uploadedBy,
                sourceName,
                blobType,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
            sleekflowCompanyId);


        return blobUploadHistory;
    }

    public async Task<BlobUploadHistory> GetBlobUploadHistory(
        string sleekflowCompanyId,
        string blobId)
    {
        var histories = await _blobUploadHistoryRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId && c.BlobId == blobId);

        return histories[0];
    }

    public async Task DeleteBlobUploadHistory(
        string sleekflowCompanyId,
        string blobId)
    {
        var blobUploadHistories = await _blobUploadHistoryRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId && c.BlobId == blobId);

        if (blobUploadHistories.Count > 0)
        {
            await _blobUploadHistoryRepository.DeleteAsync(blobUploadHistories[0].Id, sleekflowCompanyId);
        }
    }
}