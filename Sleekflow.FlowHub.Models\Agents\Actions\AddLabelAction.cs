using Newtonsoft.Json;


namespace Sleekflow.FlowHub.Models.Agents.Actions;

public class Label
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("hashtag")]
    public string Hashtag { get; set; }

    [JsonProperty("hashTagColor")]
    public string HashTagColor { get; set; }

    [JsonConstructor]
    public Label(string id, string hashtag, string hashTagColor)
    {
        Id = id;
        Hashtag = hashtag;
        HashTagColor = hashTagColor;
    }

}

public class AddLabelAction : BaseAction
{
    [JsonProperty("labels")]
    public List<Label> Labels { get; set; }

    [JsonProperty("instructions")]
    public string Instructions { get; set; }

    [JsonConstructor]
    public AddLabelAction(bool enabled, List<Label> labels, string instructions)
    {
        Enabled = enabled;
        Labels = labels;
        Instructions = instructions;
    }
}
