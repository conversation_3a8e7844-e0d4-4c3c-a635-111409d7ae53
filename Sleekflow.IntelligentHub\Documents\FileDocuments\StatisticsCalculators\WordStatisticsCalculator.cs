using DocumentFormat.OpenXml.ExtendedProperties;
using DocumentFormat.OpenXml.Packaging;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;

public class WordStatisticsCalculator : IDocumentStatisticsCalculator
{
    private readonly IDocumentCounterService _documentCounterService;

    public WordStatisticsCalculator(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public DocumentStatistics CalculateDocumentStatistics(Stream stream)
    {
        using var wordDoc = WordprocessingDocument.Open(stream, false);
        var mainPart = wordDoc.MainDocumentPart;

        if (mainPart?.Document.Body == null)
        {
            return new DocumentStatistics(0, 0, 0, 0, 0);
        }

        var documentText = mainPart.Document.Body.InnerText;

        if (string.IsNullOrEmpty(documentText))
        {
            return new DocumentStatistics(0, 0, 0, 0, 0);
        }

        var totalTokenCount = _documentCounterService.CountTokens(documentText);
        var totalWordCount = _documentCounterService.CountWords(documentText);
        var totalCharacters = _documentCounterService.CountCharacters(documentText);

        // Get page count from document properties
        var totalPages = GetPageCount(wordDoc, totalWordCount);

        return new DocumentStatistics(
            totalTokenCount,
            totalWordCount,
            totalCharacters,
            totalPages,
            (int) stream.Length);
    }

    private static int GetPageCount(WordprocessingDocument wordDoc, int totalWordCount)
    {
        var propertiesPart = wordDoc.ExtendedFilePropertiesPart;
        if (propertiesPart?.Properties != null)
        {
            var pagesElement = propertiesPart.Properties.Elements<Pages>().FirstOrDefault();
            if (pagesElement?.Text != null && int.TryParse(pagesElement.Text, out var pageCount))
            {
                return pageCount;
            }
        }

        // you may expect the page count to be available because when opened in Word or printed, there's a definitive
        // page count. However, pagination is dependent on settings and output device characteristics.
        // It is a dynamic property dependent upon rendering.

        // Fallback: If the page count property is not available, we assume 1 page = 1000 words
        if (totalWordCount == 0)
        {
            return 0;
        }

        return Math.Max(1, (int) Math.Ceiling(totalWordCount / 1000.0));
    }
}