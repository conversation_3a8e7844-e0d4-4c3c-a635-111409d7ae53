namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public static class WorkflowExecutionStatuses
{
    public const string Started = "Started";
    public const string Complete = "Complete";
    public const string Failed = "Failed";
    public const string Cancelled = "Cancelled";
    public const string Blocked = "Blocked";
    public const string Scheduled = "Scheduled";
    public const string Abandoned = "Abandoned";
    public const string Restricted = "Restricted";
    public const string Reenrolled = "Reenrolled";
}