using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class UnassignError : ITrigger
{
    private readonly IStateAggregator _stateAggregator;
    private readonly IWorkflowStepLocator _workflowStepLocator;
    private readonly IStateService _stateService;

    public UnassignError(
        IStateAggregator stateAggregator,
        IWorkflowStepLocator workflowStepLocator,
        IStateService stateService)
    {
        _stateAggregator = stateAggregator;
        _workflowStepLocator = workflowStepLocator;
        _stateService = stateService;
    }

    public class UnassignErrorInput : Sleekflow.FlowHub.Models.Internals.UnassignErrorInput
    {
        [JsonConstructor]
        public UnassignErrorInput(
            string stateId,
            string tryCatchStepId,
            Stack<StackEntry> stackEntries)
            : base(stateId, tryCatchStepId, stackEntries)
        {
        }
    }

    public class UnassignErrorOutput : Sleekflow.FlowHub.Models.Internals.UnassignErrorOutput
    {
        [JsonConstructor]
        public UnassignErrorOutput()
        {
        }
    }

    public async Task<UnassignErrorOutput> F(UnassignErrorInput unassignErrorInput)
    {
        var proxyState = await _stateService.GetProxyStateAsync(unassignErrorInput.StateId);
        var step = _workflowStepLocator.GetStep(
            proxyState.WorkflowContext.SnapshottedWorkflow,
            unassignErrorInput.TryCatchStepId);

        if (step is TryCatchStep tryStep)
        {
            await _stateAggregator.AggregateStateError(proxyState, tryStep.Catch.As, null);
        }
        else
        {
            throw new NotImplementedException();
        }

        return new UnassignErrorOutput();
    }
}