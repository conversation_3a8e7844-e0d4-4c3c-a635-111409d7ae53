# Proposal Guidelines

This document outlines the guidelines and best practices for creating technical proposals in the PROPOSALS folder. Following these guidelines will help ensure proposals are comprehensive, clear, and actionable.

## Purpose of Proposals

Proposals serve several key purposes:

1. **Document Design Decisions**: Capture the reasoning behind technical choices
2. **Facilitate Discussion**: Enable team members to provide feedback before implementation
3. **Guide Implementation**: Serve as a reference during development
4. **Preserve Knowledge**: Document the thought process for future team members

## Rules for Creating Proposals

### 1. Naming and Organization

- Use clear, descriptive filenames in the format: `[SYSTEM_PREFIX]_[FEATURE_NAME].md`
  - Example: `IH_CHAT_HISTORY_ENRICHER.md` for an IntelligentHub feature
- Place all proposals in the `PROPOSALS` directory at the project root
- For proposals with supporting files, create a subdirectory with the same name

### 2. Structure and Top-Down Approach

Proposals should follow a top-down approach, starting with broad concepts and gradually drilling down into details:

1. **Overview**: Begin with the big picture and problem statement
2. **Goals**: Clearly define what you aim to achieve
3. **Key Considerations**: Address important constraints early
4. **High-Level Architecture**: Present the overall structure before details
5. **Detailed Design**: Dive into specific components after establishing context
6. **Integration**: Explain how it fits with existing systems
7. **Examples and Implementation**: Show concrete applications and plan

This approach ensures that readers understand the context and reasoning before encountering implementation details.

### 3. Content Requirements

#### Must Have:
- **Overview**: Clear problem statement and proposed solution
- **Goals**: Explicit list of what the proposal aims to achieve
- **Key Considerations**: Important constraints and requirements
- **High-Level Architecture**: Description of the components and their relationships
- **Detailed Design**: Enough technical detail to understand the implementation
- **Implementation Plan**: Phased approach to delivering the feature with checklists and expected outcomes

#### Should Have:
- **Component Diagram**: Visual representation of the architecture using Mermaid
- **Code Samples**: Illustrative implementation of key components
- **Usage Examples**: Concrete examples of how the system will work
- **Testing Strategy**: Approach to ensuring quality
- **Alternatives Considered**: Other approaches that were evaluated

#### Nice to Have:
- **Performance Considerations**: Analysis of performance impacts
- **Security Analysis**: Detailed security implications
- **Future Enhancements**: Potential follow-up improvements

### 4. Code References and Path Inclusion

When referencing classes, interfaces, or components:

- **Always include relative file paths**: Use the format `ClassName (path/to/file.cs)`
- **Link to existing code** when possible: Use markdown links to reference existing files
- **Be specific about changes**: When modifying existing code, specify exactly what needs to change

Example:
```markdown
The `HubspotEnricher` class (`Sleekflow.IntelligentHub/Enrichers/HubspotEnricher.cs`) implements the `IChatHistoryEnricher` interface (`Sleekflow.IntelligentHub.Models/Enrichers/IChatHistoryEnricher.cs`).
```

### 5. Diagrams and Visualization

- **Use Mermaid for diagrams**: Mermaid is preferred for its readability and compatibility with Markdown
- **Include a high-level component diagram**: Show major components and their relationships
- **Add sequence diagrams** for complex interactions: Illustrate flow between components
- **Keep diagrams focused**: Each diagram should have a clear purpose

Example Mermaid diagram:

```markdown
```mermaid
graph TD
    A[Component A] --> B[Component B]
    A --> C[Component C]
    B --> D[Component D]
```
```

### 6. Implementation Plan Structure

Implementation plans should be detailed and structured to guide development:

- **Use checklists**: Format tasks as markdown checklists (`- [ ] Task description`) to track progress
- **Group by phases**: Organize tasks into logical phases or sprints
- **Include dependencies**: Note any prerequisites for each phase
- **Define expected outcomes**: Clearly state what will be completed at the end of each phase
- **Add time estimates**: Provide rough time estimates for planning purposes
- **Include summary checklist**: Add a summary checklist that captures major milestones

Example:

```markdown
### Phase 1: Core Infrastructure

**Tasks:**
- [ ] Task 1: Create interface definitions
- [ ] Task 2: Implement base classes

**Expected Outcome:**
Core components with unit tests that can be built upon in Phase 2.

**Time Estimate:** 2 weeks
```

### 7. Writing Style

- Write in clear, concise language
- Use active voice when possible (e.g., "The system validates the input" vs "The input is validated by the system")
- Define acronyms and technical terms on first use
- Use headers and sections to organize content logically
- Include code samples in appropriate language syntax highlighting

### 8. Review Process

1. Create the proposal using the standard template
2. Request feedback from relevant stakeholders
3. Iterate on the proposal based on feedback
4. Finalize the proposal before beginning implementation
5. Update the proposal if significant design changes occur during implementation

## Best Practices

### For Content:

1. **Start with Why**: Begin by explaining the problem and motivation
2. **Be Specific**: Provide concrete details rather than generalities
3. **Consider Edge Cases**: Address failure modes and exceptional conditions
4. **Reference Existing Systems**: Explain how the proposal relates to existing code
5. **Focus on Interfaces**: Pay special attention to the contracts between components
6. **Illustrate with Examples**: Use real-world examples to clarify complex points

### For Process:

1. **Draft Early**: Start documenting design ideas before they're fully formed
2. **Seek Diverse Input**: Get feedback from different roles and perspectives
3. **Update Iteratively**: Revise the proposal as understanding evolves
4. **Link Related Documents**: Reference architecture docs, requirements, and other proposals
5. **Preserve History**: Keep major revisions to show the evolution of thinking

## Common Pitfalls to Avoid

1. **Too Vague**: Lacking specific implementation details
2. **Too Complex**: Overengineering the solution beyond what's necessary
3. **Ignoring Constraints**: Not considering limitations like performance, security, or maintainability
4. **Scope Creep**: Expanding the proposal beyond its core purpose
5. **Missing Context**: Assuming readers have specific knowledge without explanation
6. **No Examples**: Proposing abstractions without concrete usage examples
7. **No Alternatives**: Presenting only one possible solution without considering options
8. **Bottom-Up Approach**: Diving into implementation details before establishing context
9. **Missing File Paths**: Referencing classes or interfaces without specifying their location
10. **Vague Implementation Plan**: Not including clear outcomes, dependencies, or checklist items

## Template Usage

Start new proposals by copying `PROPOSAL_TEMPLATE.md` and filling in each section:

```bash
cp PROPOSALS/PROPOSAL_TEMPLATE.md PROPOSALS/[SYSTEM_PREFIX]_[FEATURE_NAME].md
```

See the template for detailed structure and guidance on what to include in each section.