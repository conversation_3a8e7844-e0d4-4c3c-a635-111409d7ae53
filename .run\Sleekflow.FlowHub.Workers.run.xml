<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.FlowHub.Workers" type="AzureFunctionAppRun" factoryName="Azure - Run Function">
    <option name="PROJECT_FILE_PATH" value="$PROJECT_DIR$/Sleekflow.FlowHub.Workers/Sleekflow.FlowHub.Workers.csproj" />
    <option name="LAUNCH_PROFILE_NAME" value="" />
    <option name="PROJECT_TFM" value="net8.0" />
    <option name="FUNCTION_NAMES" value="" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="0" />
    <option name="PROGRAM_PARAMETERS" value="host start --pause-on-error --port 7097 --dotnet-isolated-debug" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.FlowHub.Workers/bin/Debug/net8.0" />
    <option name="TRACK_ENVS" value="0" />
    <envs>
      <env name="HUBSPOT_INTEGRATOR_KEY" value="a12da7c775d00cada5b1ee611d3f6dca" />
      <env name="APP_CONFIGURATION_CONN_STR" value="Endpoint=https://sleekflow-app-configurationb83ec65c.azconfig.io;Id=0f3Z-lb-s0:hWLXNKUM3ATACVXeEqAT;Secret=Ub6/5/VjpnRCXqQ4JNhRVzVp350ymvs0QP4cbFm0ACo=" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="CORE_INTERNALS_KEY" value="O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="FLOW_HUB_INTERNALS_ENDPOINT" value="https://localhost:7095/Internals" />
      <env name="INTERNALS_KEY" value="BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="HIGH_TRAFFIC_SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="THROTTLED_SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="CORE_INTERNALS_KEY" value="O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="FLOW_HUB_INTERNALS_ENDPOINT" value="https://localhost:7095/Internals" />
      <env name="HIGH_TRAFFIC_SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="INTERNALS_KEY" value="BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="THROTTLED_SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="TRACK_URL" value="1" />
    <method v="2">
      <option name="Build" />
      <option name="RunAzuriteTask" enabled="true" />
    </method>
  </configuration>
</component>