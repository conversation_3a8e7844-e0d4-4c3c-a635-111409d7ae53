using Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.Splitters;

[TestFixture]
[TestOf(typeof(CsvDocumentSplitter))]
public class CsvDocumentSplitterTest
{
    const string CsvFilePath = "../../../Binaries/business-operations-survey-2022-business-finance.csv";

    [Test]
    public async Task SplitDocumentIntoChunksTest()
    {
        var documentSplitter = new CsvDocumentSplitter();

        var numOfLinesInFile = File.ReadLines(CsvFilePath).Count();

        var fileStream = new FileStream(CsvFilePath, FileMode.Open, FileAccess.Read, FileShare.None);
        var numberOfContentPerFile = 2;
        var splitChunksStreams = await documentSplitter.SplitDocumentIntoChunksAsync(fileStream, numberOfContentPerFile);

        var expectedChunks = (numOfLinesInFile / numberOfContentPerFile) + (numOfLinesInFile % numberOfContentPerFile == 0 ? 0 : 1);
        Assert.That(splitChunksStreams.Count, Is.EqualTo(expectedChunks));
    }
}