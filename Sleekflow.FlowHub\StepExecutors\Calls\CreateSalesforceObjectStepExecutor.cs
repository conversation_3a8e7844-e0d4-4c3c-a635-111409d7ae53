﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateSalesforceObjectStepExecutor : IStepExecutor
{
}

public class CreateSalesforceObjectStepExecutor
    : GeneralStepExecutor<CallStep<CreateSalesforceObjectStepArgs>>,
        ICreateSalesforceObjectStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public CreateSalesforceObjectStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var createSalesforceObjectInput = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                new CreateSalesforceObjectRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    createSalesforceObjectInput.StateIdentity.SleekflowCompanyId,
                    createSalesforceObjectInput.SalesforceConnectionId,
                    createSalesforceObjectInput.ObjectType,
                    createSalesforceObjectInput.IsSetOwnerByUserMappingConfig,
                    createSalesforceObjectInput.SleekflowUserIdForMapping,
                    createSalesforceObjectInput.ObjectProperties));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnSalesforceFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("Salesforce create object operation timed out after 5 minutes")),
                typeof(OnSalesforceFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class CreateSalesforceObjectInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("object_type")]
        [Required]
        public string ObjectType { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("is_set_owner_by_user_mapping_config")]
        [Required]
        public bool IsSetOwnerByUserMappingConfig { get; set; }

        [JsonProperty("sleekflow_user_id_for_mapping")]
        public string? SleekflowUserIdForMapping { get; set; }

        [JsonProperty("object_properties")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> ObjectProperties { get; set; }

        [JsonConstructor]
        public CreateSalesforceObjectInput(
            string stateId,
            StateIdentity stateIdentity,
            string salesforceConnectionId,
            string objectType,
            bool isCustomObject,
            bool isSetOwnerByUserMappingConfig,
            string? sleekflowUserIdForMapping,
            Dictionary<string, object?> objectProperties)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            SalesforceConnectionId = salesforceConnectionId;
            ObjectType = objectType;
            IsCustomObject = isCustomObject;
            IsSetOwnerByUserMappingConfig = isSetOwnerByUserMappingConfig;
            SleekflowUserIdForMapping = sleekflowUserIdForMapping;
            ObjectProperties = objectProperties;
        }
    }

    private async Task<CreateSalesforceObjectInput> GetArgs(
        CallStep<CreateSalesforceObjectStepArgs> callStep,
        ProxyState state)
    {
        var salesforceConnectionId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.SalesforceConnectionIdExpr)
                        ?? callStep.Args.SalesforceConnectionIdExpr);
        var objectType =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ObjectTypeExpr)
                        ?? callStep.Args.ObjectTypeExpr);

        var isCustomObjectStr =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.IsCustomObjectExpr)
                      ?? callStep.Args.IsCustomObjectExpr);

        var isCustomObject = bool.Parse(isCustomObjectStr);

        var isSetOwnerByUserMappingConfigStr =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.IsSetOwnerByUserMappingConfigExpr)
                      ?? callStep.Args.IsSetOwnerByUserMappingConfigExpr);

        var isSetOwnerByUserMappingConfig = bool.Parse(isSetOwnerByUserMappingConfigStr);

        var sleekflowUserIdForMapping =
            callStep.Args.SleekflowUserIdForMappingExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.SleekflowUserIdForMappingExpr)
                            ?? callStep.Args.SleekflowUserIdForMappingExpr)
                : null;

        var objectProperties =
            await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.ObjectPropertiesExprDict);

        return new CreateSalesforceObjectInput(
            state.Id,
            state.Identity,
            salesforceConnectionId,
            objectType,
            isCustomObject,
            isSetOwnerByUserMappingConfig,
            sleekflowUserIdForMapping,
            objectProperties);
    }
}