using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.Models.Constants;
using Sleekflow.Models.FlowHub;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class WorkflowCountServiceTests
{
  private Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>> _mockRequestClient;
  private Mock<ILogger<WorkflowCountService>> _mockLogger;
  private WorkflowCountService _workflowCountService;

  [SetUp]
  public void SetUp()
  {
    _mockRequestClient = new Mock<IRequestClient<GetActiveWorkflowCountsByAgentConfigIdsRequest>>();
    _mockLogger = new Mock<ILogger<WorkflowCountService>>();
    _workflowCountService = new WorkflowCountService(_mockRequestClient.Object, _mockLogger.Object);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithValidAgentConfigIds_ReturnsCorrectCounts()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2", "agent3"
    };

    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      {
        "agent1", 2
      },
      {
        "agent2", 1
      },
      {
        "agent3", 0
      }
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == sleekflowCompanyId &&
          req.AgentConfigIds.SequenceEqual(agentConfigIds)),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(mockResponse.Object);

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Count, Is.EqualTo(3));
    Assert.That(result["agent1"], Is.EqualTo(2));
    Assert.That(result["agent2"], Is.EqualTo(1));
    Assert.That(result["agent3"], Is.EqualTo(0));

    // Verify that the request client was called with correct parameters
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == sleekflowCompanyId &&
          req.AgentConfigIds.SequenceEqual(agentConfigIds)),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Once);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithEmptyAgentConfigList_ReturnsEmptyDictionary()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>();

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Count, Is.EqualTo(0));

    // Verify that the request client was not called for empty list
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Never);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithMassTransitException_ReturnsZerosAndLogsError()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2"
    };

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ThrowsAsync(new RequestTimeoutException("FlowHub request timeout"));

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Count, Is.EqualTo(2));
    Assert.That(result["agent1"], Is.EqualTo(0));
    Assert.That(result["agent2"], Is.EqualTo(0));

    // Verify error was logged
    _mockLogger.Verify(
      x => x.Log(
        LogLevel.Error,
        It.IsAny<EventId>(),
        It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to retrieve workflow counts for company")),
        It.IsAny<Exception>(),
        It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
      Times.Once);
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_WithPartialFailureInFlowHub_ReturnsPartialResults()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = new List<string>
    {
      "agent1", "agent2"
    };

    // FlowHub returns partial results (one agent config has count, other is 0 due to error)
    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      {
        "agent1", 1
      },
      {
        "agent2", 0
      } // FlowHub consumer handled the error and returned 0
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(mockResponse.Object);

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Count, Is.EqualTo(2));
    Assert.That(result["agent1"], Is.EqualTo(1));
    Assert.That(result["agent2"], Is.EqualTo(0));
  }

  [Test]
  public async Task GetActiveWorkflowCountsByAgentConfigIdsAsync_VerifiesCorrectRequestParameters()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigId = "test-agent-config";
    var agentConfigIds = new List<string>
    {
      agentConfigId
    };

    var expectedWorkflowCounts = new Dictionary<string, int>
    {
      {
        agentConfigId, 0
      }
    };

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(mockResponse.Object);

    // Act
    await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    // Assert - Verify the request parameters
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.Is<GetActiveWorkflowCountsByAgentConfigIdsRequest>(req =>
          req.SleekflowCompanyId == sleekflowCompanyId &&
          req.AgentConfigIds.Count == 1 &&
          req.AgentConfigIds.Contains(agentConfigId)),
        It.IsAny<CancellationToken>(),
        It.Is<RequestTimeout>(timeout => timeout.Value == TimeSpan.FromSeconds(30))),
      Times.Once);
  }

  [Test]
  public async Task
    GetActiveWorkflowCountsByAgentConfigIdsAsync_WithLargeNumberOfAgentConfigs_ProcessesAllConcurrently()
  {
    // Arrange
    var sleekflowCompanyId = "test-company-id";
    var agentConfigIds = Enumerable.Range(1, 10).Select(i => $"agent{i}").ToList();

    var expectedWorkflowCounts = agentConfigIds.ToDictionary(id => id, _ => 1);

    var mockResponse = new Mock<Response<GetActiveWorkflowCountsByAgentConfigIdsReply>>();
    mockResponse.Setup(x => x.Message)
      .Returns(new GetActiveWorkflowCountsByAgentConfigIdsReply(expectedWorkflowCounts));

    _mockRequestClient
      .Setup(x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()))
      .ReturnsAsync(mockResponse.Object);

    // Act
    var result = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
      sleekflowCompanyId,
      agentConfigIds);

    // Assert
    Assert.That(result, Is.Not.Null);
    Assert.That(result.Count, Is.EqualTo(10));

    // Verify all agent configs have counts
    foreach (var agentConfigId in agentConfigIds)
    {
      Assert.That(result.ContainsKey(agentConfigId), Is.True);
      Assert.That(result[agentConfigId], Is.EqualTo(1));
    }

    // Verify request client was called once (not once per agent config like HTTP version)
    _mockRequestClient.Verify(
      x => x.GetResponse<GetActiveWorkflowCountsByAgentConfigIdsReply>(
        It.IsAny<GetActiveWorkflowCountsByAgentConfigIdsRequest>(),
        It.IsAny<CancellationToken>(),
        It.IsAny<RequestTimeout>()),
      Times.Once);
  }

  [Test]
  public void CompanyAgentConfigDto_DefaultConstructor_SetsActiveWorkflowCountToZero()
  {
    // Arrange & Act
    var config = new CompanyAgentConfig(
      "test-id",
      "Test Config",
      "test-company-id",
      true,
      true,
      10,
      null,
      null,
      null,
      null,
      CompanyAgentTypes.Sales,
      AgentCollaborationModes.Default,
      null,
      DateTimeOffset.UtcNow,
      DateTimeOffset.UtcNow,
      null,
      null,
      null,
      null,
      null,
      null,
      null);

    var dto = new CompanyAgentConfigDto(config);

    // Assert
    Assert.That(dto.ActiveWorkflowCount, Is.EqualTo(0));
  }
} 