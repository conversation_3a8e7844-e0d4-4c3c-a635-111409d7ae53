using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactPropertiesByPropertyKeyStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-properties-by-property-key";

    [Required]
    [JsonProperty("contact_property_key_id__expr")]
    public string ContactPropertyKeyIdExpr { get; set; }

    [Required]
    [JsonProperty("contact_property_key_value__expr")]
    public string ContactPropertyKeyValueExpr { get; set; }

    [Required]
    [JsonProperty("properties__key_expr_dict")]
    public Dictionary<string, string?> PropertiesKeyExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactPropertiesByPropertyKeyStepArgs(
        string contactPropertyKeyIdExpr,
        string contactPropertyKeyValueExpr,
        Dictionary<string, string?> propertiesKeyExprDict)
    {
        ContactPropertyKeyIdExpr = contactPropertyKeyIdExpr;
        ContactPropertyKeyValueExpr = contactPropertyKeyValueExpr;
        PropertiesKeyExprDict = propertiesKeyExprDict;
    }
}