﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Evaluator.Constants;
using Sleekflow.IntelligentHub.Evaluator.Utils;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScores.Methods;

public class DefaultLeadScoringMethod : ILeadScoreMethod<LeadScoreEvalOutput>
{
    private readonly IReviewerService _reviewerService;

    public DefaultLeadScoringMethod()
    {
        using var scope = Application.Host.Services.CreateScope();
        _reviewerService = scope.ServiceProvider.GetRequiredService<IReviewerService>();
    }

    public string MethodName => MethodNames.LeadScoring;

    public async Task<LeadScoreEvalOutput> CompleteAsync(
        string additionalPrompt,
        ChatMessageContent[] questionContexts)
    {
        var chatEntries = questionContexts.Select(ChatEntriesUtil.ToChatEntries).ToList();
        var evaluatedScore = await _reviewerService.GetEvaluateScoreAsync(chatEntries, additionalPrompt);
        return new LeadScoreEvalOutput(evaluatedScore);
    }
}