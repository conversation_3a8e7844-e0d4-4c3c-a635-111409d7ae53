using System;
using System.Collections.Immutable;
using System.Composition;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CodeActions;
using Microsoft.CodeAnalysis.CodeFixes;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;

namespace Sleekflow.Analyzers.Styles;

[ExportCodeFixProvider(LanguageNames.CSharp, Name = nameof(JsonConstructorAnalyzerCodeFixProvider))]
[Shared]
public class JsonConstructorAnalyzerCodeFixProvider : CodeFixProvider
{
    private const string AddJsonConstructorAttributeTitle = "Add JsonConstructor attribute";

    public sealed override ImmutableArray<string> FixableDiagnosticIds
    {
        get
        {
            return ImmutableArray.Create(
                JsonConstructorAnalyzer.MissingJsonConstructorDiagnosticId);
        }
    }

    public sealed override FixAllProvider GetFixAllProvider()
    {
        return WellKnownFixAllProviders.BatchFixer;
    }

    public sealed override async Task RegisterCodeFixesAsync(CodeFixContext context)
    {
        var root = await context.Document.GetSyntaxRootAsync(context.CancellationToken).ConfigureAwait(false);

        foreach (var diagnostic in context.Diagnostics)
        {
            var diagnosticSpan = diagnostic.Location.SourceSpan;

            if (diagnostic.Id == JsonConstructorAnalyzer.MissingJsonConstructorDiagnosticId)
            {
                try
                {
                    var constructorDeclaration = root!.FindToken(diagnosticSpan.Start)
                        .Parent!.AncestorsAndSelf()
                        .OfType<ConstructorDeclarationSyntax>()
                        .First();

                    context.RegisterCodeFix(
                        CodeAction.Create(
                            title: AddJsonConstructorAttributeTitle,
                            createChangedDocument: c =>
                                AddJsonConstructorAttributeAsync(context.Document, constructorDeclaration, c),
                            equivalenceKey: AddJsonConstructorAttributeTitle),
                        diagnostic);
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }
    }

    private async Task<Document> AddJsonConstructorAttributeAsync(
        Document document,
        ConstructorDeclarationSyntax constructorDeclaration,
        CancellationToken cancellationToken)
    {
        var attributeName = SyntaxFactory.ParseName("JsonConstructor");
        var newAttribute = SyntaxFactory.Attribute(attributeName);
        var newAttributeList = SyntaxFactory.AttributeList(SyntaxFactory.SingletonSeparatedList(newAttribute));
        var newConstructorDeclaration = constructorDeclaration.AddAttributeLists(newAttributeList);

        var root = await document.GetSyntaxRootAsync(cancellationToken).ConfigureAwait(false);
        var newRoot = root!.ReplaceNode(constructorDeclaration, newConstructorDeclaration);

        return document.WithSyntaxRoot(newRoot);
    }

    private string ToSnakeCase(string text)
    {
        return string.Concat(text.Select((x, i) => i > 0 && char.IsUpper(x) ? "_" + x.ToString() : x.ToString()))
            .ToLower();
    }
}