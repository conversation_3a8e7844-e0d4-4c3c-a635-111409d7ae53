using Sleekflow.Infras.Constants;

namespace Sleekflow.Infras.Tests;

public class ManagedEnvAndAppsTupleTests
{
    private readonly ManagedEnvAndAppsTuple _managedEnvAndAppsTuple = InitManagedEnvAndAppsTuple();

    [Test]
    public void FormatContainerAppName()
    {
        var containerAppName =
            _managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.CrmHub));
        Assert.That(containerAppName, Is.EqualTo("sleekflow-crm-hub-app"));

        var secondaryManagedEnvAndAppsTuple = InitManagedEnvAndAppsTuple("sec");
        var secondaryContainerAppName = secondaryManagedEnvAndAppsTuple.FormatContainerAppName(
            ServiceNames.GetShortName(ServiceNames.CrmHub));
        Assert.That(secondaryContainerAppName, Is.EqualTo("sleekflow-crm-hub-app-sec"));
    }

    private static ManagedEnvAndAppsTuple InitManagedEnvAndAppsTuple(string? name = null)
    {
        return new ManagedEnvAndAppsTuple(
            null!,
            null!,
            null!,
            null!,
            null!,
            name ?? "pri",
            LocationNames.EastAsia,
            null!,
            null!,
            null!,
            null!,
            null!,
            null!);
    }
}