namespace Sleekflow.Analyzers;

public static class StringExtensions
{
    public static string EnsureEndsWith(
        this string source,
        string suffix)
    {
        if (source.EndsWith(suffix))
        {
            return source;
        }

        return source + suffix;
    }

    public static string ToLowerFirstChar(this string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        if (input.Length == 1)
        {
            return char.ToLower(input[0]).ToString();
        }

        return char.ToLower(input[0]) + input.Substring(1);
    }
}