using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Events;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnTriggerEventRequestedEventConsumerDefinition
    : ConsumerDefinition<OnTriggerEventRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnTriggerEventRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnTriggerEventRequestedEventConsumer
    : IHighTrafficConsumer<OnTriggerEventRequestedEvent>,
        IConsumer<OnTriggerEventRequestedEvent>
{
    private readonly IFlowHubEventHandler _flowHubEventHandler;
    private readonly ILogger<OnTriggerEventRequestedEventConsumer> _logger;

    public OnTriggerEventRequestedEventConsumer(
        IFlowHubEventHandler flowHubEventHandler,
        ILogger<OnTriggerEventRequestedEventConsumer> logger)
    {
        _flowHubEventHandler = flowHubEventHandler;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnTriggerEventRequestedEvent> context)
    {
        var onTriggerEventRequestEvent = context.Message;
        var serializeObject = JsonConvert.SerializeObject(
            onTriggerEventRequestEvent.EventBody);
        _logger.LogInformation("begin to handle event in executor, eventBody: {EventBody}", serializeObject);
        await _flowHubEventHandler.HandleAsync(
            onTriggerEventRequestEvent.EventBody,
            onTriggerEventRequestEvent.ObjectId,
            onTriggerEventRequestEvent.ObjectType,
            onTriggerEventRequestEvent.SleekflowCompanyId);
    }
}