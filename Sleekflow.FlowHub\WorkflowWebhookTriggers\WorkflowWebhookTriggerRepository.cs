using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.WorkflowWebhookTriggers;

public interface IWorkflowWebhookTriggerRepository : IRepository<WorkflowWebhookTrigger>
{
}

public class WorkflowWebhookTriggerRepository
    : BaseRepository<WorkflowWebhookTrigger>, IWorkflowWebhookTriggerRepository, IScopedService
{
    public WorkflowWebhookTriggerRepository(
        ILogger<WorkflowWebhookTriggerRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}