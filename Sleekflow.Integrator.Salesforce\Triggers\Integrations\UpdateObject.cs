﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class UpdateObject : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public UpdateObject(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class UpdateObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public UpdateObjectInput(
            string sleekflowCompanyId,
            string objectId,
            Dictionary<string, object?> dict,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            Dict = dict;
            EntityTypeName = entityTypeName;
        }
    }

    public class UpdateObjectOutput
    {
        [JsonConstructor]
        public UpdateObjectOutput(bool isAsyncOperation)
        {
            IsAsyncOperation = isAsyncOperation;
        }

        [JsonProperty("is_async_operation")]
        public bool IsAsyncOperation { get; set; }
    }

    public async Task<UpdateObjectOutput> F(UpdateObjectInput updateObjectInput)
    {
        var authentication =
            await _salesforceAuthenticationService.GetAsync(updateObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var obj = await _salesforceObjectService.GetObjectAsync(
            authentication,
            updateObjectInput.ObjectId,
            updateObjectInput.EntityTypeName);
        if (obj == null)
        {
            throw new SfNotFoundObjectException(updateObjectInput.ObjectId);
        }

        var getFieldsOutput =
            await _salesforceObjectService.GetFieldsAsync(authentication, updateObjectInput.EntityTypeName);
        var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();

        var dict = updateObjectInput.Dict
            .Where(e => updatableFieldNames.Contains(e.Key))
            .ToDictionary(e => e.Key, e => e.Value);

        await _salesforceObjectService.UpdateAsync(
            authentication,
            dict,
            updateObjectInput.ObjectId,
            updateObjectInput.EntityTypeName);

        return new UpdateObjectOutput(false);
    }
}