using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.FileContentCaches;

[ContainerId(ContainerNames.FileContentCache)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IIntelligentHubDbResolver))]
public class FileContentCache
    : Entity, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    public const string PropertyNameFileUrl = "file_url";
    public const string PropertyNameFileMimeType = "file_mime_type";
    public const string PropertyNameFileSize = "file_size";
    public const string PropertyNameExtractedContent = "extracted_content";
    public const string PropertyNameBackground = "background";

    [JsonProperty(PropertyNameFileUrl)]
    public string FileUrl { get; set; }

    [JsonProperty(PropertyNameFileMimeType)]
    public string FileMimeType { get; set; }

    [JsonProperty(PropertyNameFileSize)]
    public long? FileSize { get; set; }

    [JsonProperty(PropertyNameExtractedContent)]
    public string ExtractedContent { get; set; }

    [JsonProperty(PropertyNameBackground)]
    public string? Background { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public FileContentCache(
        string id,
        string fileUrl,
        string fileMimeType,
        long? fileSize,
        string extractedContent,
        string? background,
        string? eTag,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, SysTypeNames.FileContentCache)
    {
        FileUrl = fileUrl;
        FileMimeType = fileMimeType;
        FileSize = fileSize;
        ExtractedContent = extractedContent;
        Background = background;
        ETag = eTag;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    // Factory method for easier creation
    public static FileContentCache Create(
        string id,
        string fileUrl,
        string fileMimeType,
        long? fileSize,
        string extractedContent,
        string? background = null)
    {
        var now = DateTimeOffset.UtcNow;

        return new FileContentCache(
            id,
            fileUrl,
            fileMimeType,
            fileSize,
            extractedContent,
            background,
            null,
            now,
            now);
    }
}