namespace Sleekflow.FlowHub.Silos;

public class SiloReadinessObserver : ILifecycleParticipant<ISiloLifecycle>
{
    private readonly ISiloReadinessIndicator _siloReadinessIndicator;
    private readonly ILogger<SiloReadinessObserver> _logger;

    public SiloReadinessObserver(ISiloReadinessIndicator siloReadinessIndicator, ILogger<SiloReadinessObserver> logger)
    {
        _siloReadinessIndicator = siloReadinessIndicator;
        _logger = logger;
    }

    public void Participate(ISiloLifecycle lifecycle)
    {
        _logger.LogDebug(
            "Subscribing SiloReadinessObserver to silo lifecycle stage {Stage}",
            ServiceLifecycleStage.ApplicationServices);

        // The SiloReadinessObserver subscribes and waits for the ApplicationServices stage
        lifecycle.Subscribe(
            observerName: nameof(SiloReadinessObserver),
            stage: ServiceLifecycleStage.ApplicationServices,
            onStart: OnSiloReadyAsync, // onStart delegate can return Task
            onStop: null);
    }

    // onStart delegate can still be async Task
    private Task OnSiloReadyAsync(CancellationToken ct)
    {
        _logger.LogInformation(
            "Silo has reached readiness stage ({Stage}). Signaling readiness.",
            ServiceLifecycleStage.ApplicationServices);
        _siloReadinessIndicator.SignalReady();

        return Task.CompletedTask;
    }
}