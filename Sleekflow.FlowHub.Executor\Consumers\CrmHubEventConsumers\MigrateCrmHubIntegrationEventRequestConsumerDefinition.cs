﻿using MassTransit;
using Sleekflow.FlowHub.Migrations;
using Sleekflow.Models.CrmHubToFlowHubMigrations;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class MigrateCrmHubIntegrationEventRequestConsumerDefinition : ConsumerDefinition<MigrateCrmHubIntegrationEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<MigrateCrmHubIntegrationEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class MigrateCrmHubIntegrationEventRequestConsumer : IConsumer<MigrateCrmHubIntegrationToFlowHubEventRequest>
{
    private readonly ICrmHubToFlowHubMigrationService _crmHubToFlowHubMigrationService;

    public MigrateCrmHubIntegrationEventRequestConsumer(
        ICrmHubToFlowHubMigrationService crmHubToFlowHubMigrationService)
    {
        _crmHubToFlowHubMigrationService = crmHubToFlowHubMigrationService;
    }

    public async Task Consume(ConsumeContext<MigrateCrmHubIntegrationToFlowHubEventRequest> context)
    {
        var request = context.Message;

        await _crmHubToFlowHubMigrationService.CreateWorkflowsForCrmHubIntegration(
            request.SleekflowCompanyId,
            request.EntityTypeNameToSyncConfigDict,
            request.EntityTypeNameToFieldMappingsDict,
            request.ProviderName,
            request.ProviderConnectionId,
            request.SleekflowStaffId,
            request.SleekflowStaffTeamIds);
    }
}