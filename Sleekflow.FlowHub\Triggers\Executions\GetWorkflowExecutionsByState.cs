using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetWorkflowExecutionsByState : ITrigger
{
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IStateService _stateService;
    private readonly IStepExecutionService _stepExecutionService;

    public GetWorkflowExecutionsByState(
        IWorkflowExecutionService workflowExecutionService,
        IStateService stateService,
        IStepExecutionService stepExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
        _stateService = stateService;
        _stepExecutionService = stepExecutionService;
    }

    public class GetWorkflowExecutionsByStateInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("limit")]
        [Required]
        [Range(1, 1000)]
        public int Limit { get; set; }

        [JsonProperty("filters")]
        [Required]
        [ValidateObject]
        public GetWorkflowExecutionsByStateInputFilters Filters { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionsByStateInput(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            GetWorkflowExecutionsByStateInputFilters filters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContinuationToken = continuationToken;
            Limit = limit;
            Filters = filters;
        }
    }

    public class GetWorkflowExecutionsByStateInputFilters : StateFilters
    {
        [JsonConstructor]
        public GetWorkflowExecutionsByStateInputFilters(
            string? workflowId,
            string? stateStatus,
            DateTimeOffset? fromDateTime,
            DateTimeOffset? toDateTime)
            : base(workflowId, stateStatus, fromDateTime, toDateTime)
        {
        }
    }

    public class GetWorkflowExecutionsByStateOutput
    {
        [JsonProperty("workflow_executions")]
        public List<WorkflowExecutionDto> WorkflowExecutions { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetWorkflowExecutionsByStateOutput(
            List<WorkflowExecutionDto> workflowExecutions,
            string? nextContinuationToken)
        {
            WorkflowExecutions = workflowExecutions;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetWorkflowExecutionsByStateOutput> F(
        GetWorkflowExecutionsByStateInput getWorkflowExecutionsByStateInput)
    {
        var sleekflowCompanyId = getWorkflowExecutionsByStateInput.SleekflowCompanyId;

        var (states, nextContinuationToken) = await _stateService.GetProxyStatesAsync(
            sleekflowCompanyId,
            getWorkflowExecutionsByStateInput.ContinuationToken,
            getWorkflowExecutionsByStateInput.Limit,
            getWorkflowExecutionsByStateInput.Filters);

        var workflowExecutions = await _workflowExecutionService.GetWorkflowExecutionsAsync(
            sleekflowCompanyId,
            states.Select(s => s.Id).ToList());

        var failedStateIdToRemarks = await GetFailedStateIdToRemarks(states, sleekflowCompanyId);

        var workflowExecutionDtos = workflowExecutions
            .GroupBy(we => we.StateId)
            .Select(
                g =>
                {
                    var targetState = states.First(s => s.Id == g.Key);

                    var remarks = !string.IsNullOrWhiteSpace(targetState.StateReasonCode)
                        ? GetRemarkFromStateReasonCode(targetState)
                        : failedStateIdToRemarks.GetValueOrDefault(
                            g.Key,
                            GetRemarkFromStateReasonCode(targetState));

                    return new WorkflowExecutionDto(
                        g.Where(x => x.WorkflowExecutionStatus is
                                WorkflowExecutionStatuses.Started
                                or WorkflowExecutionStatuses.Blocked
                                or WorkflowExecutionStatuses.Restricted)
                            .MinBy(we => we.CreatedAt)!,
                        g.MaxBy(we => we.CreatedAt)!,
                        remarks);
                })
            .OrderByDescending(we => we.CreatedAt)
            .ToList();

        return new GetWorkflowExecutionsByStateOutput(workflowExecutionDtos, nextContinuationToken);
    }

    private static List<WorkflowExecutionDtoRemark> GetRemarkFromStateReasonCode(ProxyState? state)
    {
        var result = new List<WorkflowExecutionDtoRemark>();

        var workflowExecutionDtoRemark
            = state?.StateStatus switch
            {
                StateStatuses.Cancelled => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? StateReasonCodes.ManualCancellation,
                    string.Empty,
                    null),
                StateStatuses.Blocked => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? StateReasonCodes.EnrollmentRateLimited,
                    string.Empty,
                    null),
                StateStatuses.Restricted => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? StateReasonCodes.EnrollmentUsageLimitExceeded,
                    string.Empty,
                    null),
                StateStatuses.Failed => new WorkflowExecutionDtoRemark(
                    string.Empty,
                    state.StateReasonCode ?? UserFriendlyErrorCodes.InternalError, // Step execution could be created slightly late after a step failed which led to enrollment fail. In this case, we will use InternalError as the error code for temporary display
                    string.Empty,
                    null),
                _ => null
            };

        if (workflowExecutionDtoRemark is not null)
        {
            result.Add(workflowExecutionDtoRemark);
        }

        return result;
    }

    private async Task<Dictionary<string, List<WorkflowExecutionDtoRemark>>> GetFailedStateIdToRemarks(
        IReadOnlyList<ProxyState> states,
        string sleekflowCompanyId)
    {
        var failedStateId = states
            .Where(s => s.StateStatus == StateStatuses.Failed)
            .Select(s => s.Id)
            .ToList();

        var failedStateStepExecutions = await _stepExecutionService.GetStateStepExecutionsAsync(
            sleekflowCompanyId,
            failedStateId,
            new List<string>()
            {
                StepExecutionStatuses.Failed, StepExecutionStatuses.Timeout,
            });
        var failedStateIdToRemarks = failedStateStepExecutions
            .GroupBy(se => se.StateId)
            .ToDictionary(
                g => g
                    .Key,
                g => g
                    .Select(
                        se => new WorkflowExecutionDtoRemark(
                            se.Error?.ErrorMessage ?? string.Empty,
                            se.Error?.ErrorCode ?? string.Empty,
                            se.StepId,
                            se.StepNodeId))
                    .ToList());

        return failedStateIdToRemarks;
    }
}