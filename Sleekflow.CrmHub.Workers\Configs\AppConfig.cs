﻿using Microsoft.Extensions.Configuration;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Workers.Configs;

public interface IAppConfig
{
    string HubspotIntegratorInternalsEndpoint { get; }

    string SalesforceIntegratorInternalsEndpoint { get; }

    string Dynamics365IntegratorInternalsEndpoint { get; }

    string GoogleSheetsIntegratorInternalsEndpoint { get; }

    string ZohoIntegratorInternalsEndpoint { get; }

    string InternalsKey { get; }

    string CrmHubInternalsEndpoint { get; }
}

public class AppConfig : IAppConfig
{
    public string HubspotIntegratorInternalsEndpoint { get; }

    public string SalesforceIntegratorInternalsEndpoint { get; }

    public string Dynamics365IntegratorInternalsEndpoint { get; }

    public string GoogleSheetsIntegratorInternalsEndpoint { get; }

    public string ZohoIntegratorInternalsEndpoint { get; }

    public string InternalsKey { get; }

    public string CrmHubInternalsEndpoint { get; }

    public AppConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        HubspotIntegratorInternalsEndpoint =
            Environment.GetEnvironmentVariable("HUBSPOT_INTEGRATOR_INTERNALS_ENDPOINT", target)
            ?? configuration["HUBSPOT_INTEGRATOR_INTERNALS_ENDPOINT"];
        SalesforceIntegratorInternalsEndpoint =
            Environment.GetEnvironmentVariable("SALESFORCE_INTEGRATOR_INTERNALS_ENDPOINT", target)
            ?? configuration["SALESFORCE_INTEGRATOR_INTERNALS_ENDPOINT"];
        Dynamics365IntegratorInternalsEndpoint =
            Environment.GetEnvironmentVariable("DYNAMICS365_INTEGRATOR_INTERNALS_ENDPOINT", target)
            ?? configuration["DYNAMICS365_INTEGRATOR_INTERNALS_ENDPOINT"];
        GoogleSheetsIntegratorInternalsEndpoint =
            Environment.GetEnvironmentVariable("GOOGLE_SHEETS_INTEGRATOR_INTERNALS_ENDPOINT", target)
            ?? configuration["GOOGLE_SHEETS_INTEGRATOR_INTERNALS_ENDPOINT"];
        ZohoIntegratorInternalsEndpoint =
            Environment.GetEnvironmentVariable("ZOHO_INTEGRATOR_INTERNALS_ENDPOINT", target)
            ?? configuration["ZOHO_INTEGRATOR_INTERNALS_ENDPOINT"];
        InternalsKey =
            Environment.GetEnvironmentVariable("INTERNALS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("INTERNALS_KEY");
        CrmHubInternalsEndpoint =
            Environment.GetEnvironmentVariable("CRM_HUB_INTERNALS_ENDPOINT", target)
            ?? configuration["CRM_HUB_INTERNALS_ENDPOINT"];
    }
}