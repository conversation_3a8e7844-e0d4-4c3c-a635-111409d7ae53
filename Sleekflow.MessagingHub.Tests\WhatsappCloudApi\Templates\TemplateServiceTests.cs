using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Models;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Payloads;
using GraphApi.Client.Payloads.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.MessagingHub.WhatsappCloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Templates;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi.Templates;

[TestFixture]
public class TemplateServiceTests
{
    private Mock<ILogger<TemplateService>> _loggerMock;
    private Mock<ICloudApiClients> _cloudApiClientsMock;
    private Mock<IWhatsappCloudApiMessagingClient> _whatsappCloudApiMessagingClientMock;
    private Mock<IHttpClientFactory> _httpClientFactoryMock;
    private TemplateService _templateService;

    [SetUp]
    public void Setup()
    {
        _loggerMock = new Mock<ILogger<TemplateService>>();
        _cloudApiClientsMock = new Mock<ICloudApiClients>();
        _whatsappCloudApiMessagingClientMock = new Mock<IWhatsappCloudApiMessagingClient>();
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();

        _cloudApiClientsMock.Setup(c => c.WhatsappCloudApiMessagingClient)
            .Returns(_whatsappCloudApiMessagingClientMock.Object);
        _httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(new HttpClient());

        _templateService = new TemplateService(
            _loggerMock.Object,
            _cloudApiClientsMock.Object,
            _httpClientFactoryMock.Object);
    }

    [Test]
    public async Task GetCloudApiWhatsappTemplateAsync_ShouldReturnTemplates_WhenApiCallSucceedsAndNoTokenProvided()
    {
        // Arrange
        var facebookWabaId = "test_waba_id";
        string? businessIntegrationSystemUserAccessToken = null;

        var expectedTemplates = new List<WhatsappCloudApiTemplate>
        {
            new WhatsappCloudApiTemplate
            {
                Name = "template1"
            },
            new WhatsappCloudApiTemplate
            {
                Name = "template2"
            }
        };

        var getTemplatesResponse = new GetTemplatesResponse()
        {
            Data = expectedTemplates,
            Paging = new CursorBasedPaginationResult()
            {
                Cursors = new Cursors
                {
                    After = null // Simulating no pagination for this test
                }
            }
        };

        _whatsappCloudApiMessagingClientMock
            .Setup(client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.IsAny<CursorBasedPaginationParam>(),
                CancellationToken.None))
            .ReturnsAsync(getTemplatesResponse);

        // Act
        var result = await _templateService.GetCloudApiWhatsappTemplateAsync(
            facebookWabaId,
            businessIntegrationSystemUserAccessToken);

        // Assert
        Assert.IsNotNull(result);
        Assert.That(result.Count, Is.EqualTo(expectedTemplates.Count));
        for (int i = 0; i < expectedTemplates.Count; i++)
        {
            Assert.That(result[i].Name, Is.EqualTo(expectedTemplates[i].Name));
        }

        _whatsappCloudApiMessagingClientMock.Verify(
            client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.Is<CursorBasedPaginationParam>(p => p.Limit == 2000 && p.After == null),
                CancellationToken.None),
            Times.Once);
    }

    [Test]
    public async Task GetCloudApiWhatsappTemplateAsync_ShouldHandlePagination_WhenMultiplePagesExist()
    {
        // Arrange
        var facebookWabaId = "test_waba_id";
        string? businessIntegrationSystemUserAccessToken = null;

        var templatesPage1 = Enumerable.Range(1, 2000).Select(i => new WhatsappCloudApiTemplate()
        {
            Name = $"template{i}"
        }).ToList();
        var templatesPage2 = Enumerable.Range(1, 1500).Select(i => new WhatsappCloudApiTemplate()
        {
            Name = $"template{i}"
        }).ToList();

        var responsePage1 = new GetTemplatesResponse()
        {
            Data = templatesPage1,
            Paging = new CursorBasedPaginationResult()
            {
                Cursors = new Cursors
                {
                    After = "cursor_page_1"
                }
            }
        };

        var responsePage2 = new GetTemplatesResponse()
        {
            Data = templatesPage2,
            Paging = new CursorBasedPaginationResult()
            {
                Cursors = new Cursors
                {
                    After = "cursor_page_2"
                }
            }
        };

        var responsePageEnd = new GetTemplatesResponse()
        {
            Data = new List<WhatsappCloudApiTemplate>(),
            Paging = new CursorBasedPaginationResult()
            {
                Cursors = new Cursors
                {
                    After = null // Simulating no pagination after cursor_page_2
                }
            }
        };

        _whatsappCloudApiMessagingClientMock
            .SetupSequence(client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.Is<CursorBasedPaginationParam>(p => p.Limit == 2000 && p.After == null),
                CancellationToken.None))
            .ReturnsAsync(responsePage1);

        _whatsappCloudApiMessagingClientMock
            .Setup(client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.Is<CursorBasedPaginationParam>(p => p.Limit == 2000 && p.After == "cursor_page_1"),
                CancellationToken.None))
            .ReturnsAsync(responsePage2);

        _whatsappCloudApiMessagingClientMock
            .Setup(client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.Is<CursorBasedPaginationParam>(p => p.Limit == 2000 && p.After == "cursor_page_2"),
                CancellationToken.None))
            .ReturnsAsync(responsePageEnd);

        // Act
        var result = await _templateService.GetCloudApiWhatsappTemplateAsync(
            facebookWabaId,
            businessIntegrationSystemUserAccessToken);

        // Assert
        Assert.IsNotNull(result);
        Assert.That(result.Count, Is.EqualTo(3500));

        _whatsappCloudApiMessagingClientMock.Verify(
            client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.Is<CursorBasedPaginationParam>(p => p.Limit == 2000 && p.After == null),
                CancellationToken.None),
            Times.Once);
        _whatsappCloudApiMessagingClientMock.Verify(
            client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.Is<CursorBasedPaginationParam>(p => p.Limit == 2000 && p.After == "cursor_page_1"),
                CancellationToken.None),
            Times.Once);
        _whatsappCloudApiMessagingClientMock.Verify(
            client => client.GetTemplatesAsync(
                facebookWabaId,
                null,
                It.Is<CursorBasedPaginationParam>(p => p.Limit == 2000 && p.After == "cursor_page_2"),
                CancellationToken.None),
            Times.Never);
    }
}