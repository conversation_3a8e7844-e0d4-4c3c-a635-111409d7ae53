﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetUserMappingConfig : ITrigger
{
    private readonly IZohoUserMappingConfigService _zohoUserMappingConfigService;
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;

    public GetUserMappingConfig(
        IZohoUserMappingConfigService zohoUserMappingConfigService,
        IZohoConnectionService zohoConnectionService,
        IZohoAuthenticationService zohoAuthenticationService)
    {
        _zohoUserMappingConfigService = zohoUserMappingConfigService;
        _zohoConnectionService = zohoConnectionService;
        _zohoAuthenticationService = zohoAuthenticationService;
    }

    public class GetUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetUserMappingConfigInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class GetUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public GetUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<GetUserMappingConfigOutput> F(
        GetUserMappingConfigInput getUserMappingConfigInput)
    {
        var sleekflowCompanyId = getUserMappingConfigInput.SleekflowCompanyId;
        var connectionId = getUserMappingConfigInput.ConnectionId;

        var connection =
            await _zohoConnectionService.GetByIdAsync(
                connectionId,
                sleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var userMappingConfig =
            await _zohoUserMappingConfigService.GetAsync(
                sleekflowCompanyId,
                connectionId);

        return new GetUserMappingConfigOutput(
            new ProviderUserMappingConfigDto(
                userMappingConfig.Id,
                userMappingConfig.SleekflowCompanyId,
                userMappingConfig.ConnectionId,
                userMappingConfig.UserMappings));
    }
}