using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Models;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class SleekflowPriceSheetPluginResponse : QueryKnowledgeResponse
{
    public SleekflowPriceSheetPluginResponse(string knowledge, string id)
        : base(knowledge, id, "PriceSheet")
    {
    }
}

public interface ISleekflowPriceSheetPlugin
{
    [KernelFunction("get_features_and_usage_limits")]
    [Description("Gets features and usage limits.")]
    Task<SleekflowPriceSheetPluginResponse> GetFeaturesAndUsageLimitsAsync(
        Kernel kernel,
        [Description("The region tier number (1-5)")]
        string regionTier,
        [Description(
            "The plan tier to get features for. Available options: Startup, Pro, Premium, Enterprise, all plans.")]
        string planTier);

    [KernelFunction("get_plan_and_addon_pricing")]
    [Description(
        "Gets pricing information.")]
    Task<SleekflowPriceSheetPluginResponse> GetPlanAndAddOnPricingAsync(
        Kernel kernel,
        [Description("The region tier number (1-5)")]
        string regionTier,
        [Description(
            "The plan tier to get features for. Available options: Startup, Pro, Premium, Enterprise, all plans.")]
        string planTier,
        [Description("The currency e.g. USD, HKD, CNY, EUR, GBP, CAD, AUD, AED.")]
        string? currency);

    [KernelFunction("get_whatsapp_conversation_rates")]
    [Description("Gets WhatsApp conversation rates for different countries and message types.")]
    Task<SleekflowPriceSheetPluginResponse> GetWhatsAppConversationRatesAsync(
        Kernel kernel,
        [Description("Optional country or region to filter rates for (e.g., 'India', 'United Kingdom', 'North America')")]
        string? country,
        [Description("Optional message type to filter rates for (e.g., 'Marketing', 'Utility', 'Authentication', 'Service')")]
        string? messageType);
}

public class SleekflowPriceSheetPlugin : ISleekflowPriceSheetPlugin, IScopedService
{
    private readonly string _assetsDirectory;
    private readonly ILogger<SleekflowPriceSheetPlugin> _logger;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public SleekflowPriceSheetPlugin(
        ILogger<SleekflowPriceSheetPlugin> logger,
        IPromptExecutionSettingsService promptExecutionSettingsService)
    {
        _logger = logger;
        _promptExecutionSettingsService = promptExecutionSettingsService;

        // Get the current assembly's location and navigate to the Assets directory
        var assemblyLocation = typeof(SleekflowPriceSheetPlugin).Assembly.Location;
        var assemblyDirectory = Path.GetDirectoryName(assemblyLocation);
        _assetsDirectory = Path.Combine(assemblyDirectory, "Plugins", "Assets");
    }

    [KernelFunction("get_features_and_usage_limits")]
    [Description("Gets features and usage limits.")]
    public async Task<SleekflowPriceSheetPluginResponse> GetFeaturesAndUsageLimitsAsync(
        Kernel kernel,
        [Description("The region tier number (1-5)")]
        string regionTier,
        [Description(
            "The plan tier to get features for. Available options: Startup, Pro, Premium, Enterprise, all plans.")]
        string planTier)
    {
        var id = Guid.NewGuid().ToString();

        if (regionTier != "1" && regionTier != "2" && regionTier != "3" && regionTier != "4" && regionTier != "5")
        {
            return new SleekflowPriceSheetPluginResponse(
                "Invalid plan tier. Please provide a valid tier number (1-5).",
                id);
        }

        try
        {
            var fileName = $"SleekFlow Features & Usage Limits - Plan Tier {regionTier}.csv";
            var csvContent = await ReadCsvFileAsync(fileName);

            // If any filtering parameter is provided, use LLM to summarize with the appropriate focus
            if (!string.IsNullOrEmpty(planTier))
            {
                var queryPrompt = $"Provide a detailed summary of SleekFlow features for {planTier}";

                return new SleekflowPriceSheetPluginResponse(
                    $"<CONFIRMED_KNOWLEDGE>{await SummarizeAsync(kernel, queryPrompt, csvContent)}</CONFIRMED_KNOWLEDGE>",
                    id);
            }

            return new SleekflowPriceSheetPluginResponse(
                $"<CONFIRMED_KNOWLEDGE>{csvContent}</CONFIRMED_KNOWLEDGE>",
                id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting price sheet data");

            return new SleekflowPriceSheetPluginResponse(
                "Error occurred while processing pricing information.",
                id);
        }
    }

    [KernelFunction("get_plan_and_addon_pricing")]
    [Description(
        "Gets pricing information.")]
    public async Task<SleekflowPriceSheetPluginResponse> GetPlanAndAddOnPricingAsync(
        Kernel kernel,
        [Description("The region tier number (1-5)")]
        string regionTier,
        [Description(
            "The plan tier to get features for. Available options: Startup, Pro, Premium, Enterprise, all plans.")]
        string planTier,
        [Description("The currency e.g. USD, HKD, CNY, EUR, GBP, CAD, AUD, AED.")]
        string? currency)
    {
        var id = Guid.NewGuid().ToString();

        if (regionTier != "1" && regionTier != "2" && regionTier != "3" && regionTier != "4" && regionTier != "5")
        {
            return new SleekflowPriceSheetPluginResponse(
                "Invalid plan tier. Please provide a valid tier number (1-5).",
                id);
        }

        try
        {
            var fileName = $"SleekFlow Plan & Add-On Pricing - Plan Tier {regionTier}.csv";
            var csvContent = await ReadCsvFileAsync(fileName);

            // If any filtering parameter is provided, use LLM to summarize with the appropriate focus
            if (!string.IsNullOrEmpty(currency))
            {
                var queryPrompt = $"Provide a detailed summary of SleekFlow pricing for {planTier}";

                if (!string.IsNullOrEmpty(currency))
                {
                    queryPrompt += $" in {currency} currency";
                }

                return new SleekflowPriceSheetPluginResponse(
                    $"<CONFIRMED_KNOWLEDGE>{await SummarizeAsync(kernel, queryPrompt, csvContent)}</CONFIRMED_KNOWLEDGE>",
                    id);
            }

            return new SleekflowPriceSheetPluginResponse(
                $"<CONFIRMED_KNOWLEDGE>{csvContent}</CONFIRMED_KNOWLEDGE>",
                id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting price sheet data");

            return new SleekflowPriceSheetPluginResponse(
                "Error occurred while processing pricing information.",
                id);
        }
    }

    [KernelFunction("get_whatsapp_conversation_rates")]
    [Description("Gets WhatsApp conversation rates for different countries and message types.")]
    public async Task<SleekflowPriceSheetPluginResponse> GetWhatsAppConversationRatesAsync(
        Kernel kernel,
        [Description("Optional country or region to filter rates for (e.g., 'India', 'United Kingdom', 'North America')")]
        string? country,
        [Description("Optional message type to filter rates for (e.g., 'Marketing', 'Utility', 'Authentication', 'Service')")]
        string? messageType)
    {
        var id = Guid.NewGuid().ToString();

        try
        {
            const string fileName = "SleekFlow Meta WhatsApp Conversation Rates.csv";
            var csvContent = await ReadCsvFileAsync(fileName);

            // Create query prompt based on provided parameters
            if (!string.IsNullOrEmpty(country) || !string.IsNullOrEmpty(messageType))
            {
                var queryPrompt = "Provide a detailed summary of SleekFlow WhatsApp conversation rates";

                if (!string.IsNullOrEmpty(country))
                {
                    queryPrompt += $" for {country}";
                }

                if (!string.IsNullOrEmpty(messageType))
                {
                    queryPrompt += $" for {messageType} message type";
                }

                return new SleekflowPriceSheetPluginResponse(
                    $"<CONFIRMED_KNOWLEDGE>{await SummarizeAsync(kernel, queryPrompt, csvContent)}</CONFIRMED_KNOWLEDGE>",
                    id);
            }

            return new SleekflowPriceSheetPluginResponse(
                $"<CONFIRMED_KNOWLEDGE>{csvContent}</CONFIRMED_KNOWLEDGE>",
                id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting WhatsApp conversation rates");

            return new SleekflowPriceSheetPluginResponse(
                "Error occurred while processing WhatsApp conversation rates.",
                id);
        }
    }

    private async Task<string> ReadCsvFileAsync(string fileName)
    {
        var filePath = Path.Combine(_assetsDirectory, fileName);

        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"Could not find price sheet file: {fileName}", filePath);
        }

        return await File.ReadAllTextAsync(filePath);
    }

    private async Task<string> SummarizeAsync(
        Kernel kernel,
        string query,
        string data)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH);

        var summarizeFunction = kernel.CreateFunctionFromPrompt(
            new PromptTemplateConfig
            {
                Name = "SummarizePriceSheetContent",
                Template =
                    """
                    You are a pricing expert for SleekFlow, a workflow and messaging platform.

                    Summarize the following CSV data in a clear, structured format suitable for a customer query.
                    Focus on providing accurate and relevant information that addresses the user's query specifically.
                    When currency, plan type, or specific topics are mentioned in the query, focus your response on just those aspects.

                    - If a currency is specified, only include pricing information in that currency.
                    - If a specific feature, add-on, or topic is mentioned, highlight that information.

                    Use English sentences to explain the data, and avoid using bullet points or lists or tables.
                    Ensure that the summary is concise and easy to understand.

                    ====CSV DATA====
                    {{$DATA}}
                    ====CSV DATA====

                    ====QUERY====
                    {{$QUERY}}
                    ====QUERY====
                    """,
                InputVariables =
                [
                    new InputVariable
                    {
                        Name = "QUERY", IsRequired = true
                    },
                    new InputVariable
                    {
                        Name = "DATA", IsRequired = true
                    },
                ],
                OutputVariable = new OutputVariable
                {
                    Description = "A summarized result relevant to the query about SleekFlow pricing or features."
                },
                ExecutionSettings = new Dictionary<string, PromptExecutionSettings>
                {
                    {
                        promptExecutionSettings.ServiceId!, promptExecutionSettings
                    }
                },
            });

        // Invoke the function with the provided input
        try
        {
            var chatMessageContent = await summarizeFunction.InvokeAsync<ChatMessageContent>(
                kernel,
                new KernelArguments(promptExecutionSettings)
                {
                    {
                        "DATA", data
                    },
                    {
                        "QUERY", query
                    }
                });

            return chatMessageContent?.Content ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error summarizing price sheet data");
            return "Error occurred while processing pricing information.";
        }
    }
}