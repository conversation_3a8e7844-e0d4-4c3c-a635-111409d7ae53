﻿namespace Sleekflow.Exceptions.Salesforce;

public class SfSObjectQueryOperationException : ErrorCodeException
{
    public string ResponseStr { get; }

    public string SObjectTypeName { get; }

    public SfSObjectQueryOperationException(string responseStr, string sObjectTypeName)
        : base(
            ErrorCodeConstant.SfSObjectQueryOperationException,
            $"The query request has failed. sObjectTypeName=[{sObjectTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "sObjectTypeName", sObjectTypeName
                }
            })
    {
        ResponseStr = responseStr;
        SObjectTypeName = sObjectTypeName;
    }
}