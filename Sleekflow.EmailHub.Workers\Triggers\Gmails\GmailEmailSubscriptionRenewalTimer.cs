using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Workers.Configs;
using Sleekflow.JsonConfigs;

namespace Sleekflow.EmailHub.Workers.Triggers.Gmails;

public class GmailEmailSubscriptionRenewalsTimer
{
    private readonly ILogger<GmailEmailSubscriptionRenewalsTimer> _logger;
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public GmailEmailSubscriptionRenewalsTimer(
        ILogger<GmailEmailSubscriptionRenewalsTimer> logger,
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    [Function("Gmail_EmailSubscriptionRenewals_Timer")]
    public async Task GmailSubscriptionRenewalTimerTrigger(
        [TimerTrigger("0 0 0 * * *")] TimerInfo timerInfo)
    {
        _logger.LogInformation($"GmailEmailSubscriptionRenewal timer trigger function executed at {DateTime.UtcNow}");
        var inputJsonStr =
            JsonConvert.SerializeObject(
                new
            {
                ProviderName = ProviderNames.Gmail
            }, JsonConfig.DefaultJsonSerializerSettings);
        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri(_appConfig.EmailHubInternalEndpoint + "/Subscriptions/RenewProviderEmailSubscription"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
        };
        var _ = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        _logger.LogInformation($"GmailEmailSubscriptionRenewal timer trigger function finished execution at {DateTime.UtcNow}");
    }
}