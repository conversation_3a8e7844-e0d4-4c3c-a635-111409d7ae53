﻿using GraphApi.Client.ApiClients;
using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Tests.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi;

public class BusinessIntegrationSystemUserTokenTest
{
    private WabaService _wabaService;
    private Mock<IHttpClientFactory> _httpClientFactoryMock;
    private Mock<IBus> _busMock;
    private Mock<ILogger<WabaService>> _loggerMock;
    private Mock<IIdService> _idServiceMock;
    private SecretConfig _secretConfig;
    private Mock<IWabaRepository> _wabaRepositoryMock;
    private Mock<IAuditLogService> _auditLogServiceMock;
    private Mock<ICloudApiClients> _cloudApiClientsMock;
    private Mock<ICommonRetryPolicyService> _commonRetryPolicyServiceMock;
    private Mock<IWhatsappCloudApiWebhookConfig> _whatsappCloudApiWebhookConfigMock;
    private Mock<IAppConfig> _appConfigMock;
    private Mock<IWabaAssetsManager> _wabaAssetsManagerMock;

    [SetUp]
    public void Setup()
    {
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
        _busMock = new Mock<IBus>();
        _loggerMock = new Mock<ILogger<WabaService>>();
        _idServiceMock = new Mock<IIdService>();
        _secretConfig = new SecretConfig();
        _wabaRepositoryMock = new Mock<IWabaRepository>();
        _auditLogServiceMock = new Mock<IAuditLogService>();
        _cloudApiClientsMock = new Mock<ICloudApiClients>();
        _commonRetryPolicyServiceMock = new Mock<ICommonRetryPolicyService>();
        _whatsappCloudApiWebhookConfigMock = new Mock<IWhatsappCloudApiWebhookConfig>();
        _appConfigMock = new Mock<IAppConfig>();
        _wabaAssetsManagerMock = new Mock<IWabaAssetsManager>();
        _wabaService = new WabaService(_busMock.Object, _idServiceMock.Object, _secretConfig, _loggerMock.Object, _wabaRepositoryMock.Object, _auditLogServiceMock.Object, _cloudApiClientsMock.Object, _commonRetryPolicyServiceMock.Object, _whatsappCloudApiWebhookConfigMock.Object, _appConfigMock.Object, _httpClientFactoryMock.Object, _wabaAssetsManagerMock.Object);
    }

    [Test]
    public void TestDecrypt()
    {
        var encryptedToken =
            "MtypHkUYJ/pmYdcWxGu301su2thaF5rO3GN1WnlMHPBTXYO7PzJNl3YT/KWF9r+JrLObpuLHF69JEPGNSEZp6MW/IXr6Shrih3f8cePqUdeqIgQl1dXimvj5jUyJf8QQuylzEhwghx7p9mFqFz0Ab/Egdvc+2GW0+naqxdJJenMHrkVDZUFHM46wNcEQ7Jo4jg+D0bCBurInfNsXL5CZJCwFUFAt8aAbIHYZCwGrbd/g0Ez9SU22Aep17hD9I0KUBY/C3cejYna1ESRLd/2a/XFe2xXAH5xb/MYNYXYfW8hVWtCh2WQ8wuj1rLYX8uO3viqrlRMXlRfIah98oZHDMggsx4/hgfd6+6LyhAO2F5Q=i4X9Meeyd=4dkgsT";

        var decryptedToken = AesUtils.AesDecryptBase64(
            encryptedToken,
            _secretConfig.FacebookBusinessIntegrationSystemUserAccessTokenSecret);

        Assert.Multiple(
            () =>
            {
                Assert.That(decryptedToken, Is.Not.Null);
                Assert.That(decryptedToken, Is.Not.Empty);
            });
    }

    // Test cases are disabled due to tests need valid Waba tokens to send messages, as tokens will automatically expire after certain time period
    // Team: Ironman
    // Require Access Token
    // [Test]
    // public async Task EncryptedBusinessIntegrationSystemUserAccessTokenTest()
    // {
    //     var businessIntegrationSystemUserAccessToken = Secrets.FacebookBusinessIntegrationSystemUserAccessToken;
    //
    //     var httpClient = _httpClientFactoryMock.Object.CreateClient("default-handler");
    //
    //     var debugTokenResponse = await new WhatsappCloudApiAuthenticationClient(
    //         Secrets.FacebookTestingAppUserAccessToken,
    //         httpClient).DebugTokenAsync(businessIntegrationSystemUserAccessToken);
    //
    //     Assert.That(debugTokenResponse.Data.IsValid, Is.True);
    //     Assert.That(debugTokenResponse.Data.AppId, Is.EqualTo(Secrets.FacebookTestingAppId));
    //
    //     var scopes = new List<string>()
    //     {
    //         "catalog_management",
    //         "pages_show_list",
    //         "ads_management",
    //         "business_management",
    //         "whatsapp_business_management",
    //         "pages_read_engagement",
    //         "whatsapp_business_messaging",
    //         "public_profile"
    //     };
    //
    //     Assert.That(debugTokenResponse.Data.Scopes, Is.EquivalentTo(scopes));
    //
    //     var wabaTargetIds = new List<string>()
    //     {
    //         "204578129404425",
    //         "157195750819481",
    //         "170012979528742",
    //         "117966677845309",
    //         "102301196164109",
    //         "102645852696034",
    //         "107305245672915",
    //         "113936408276909",
    //         "***************",
    //         "116742654727279",
    //         "120227277709052",
    //         "193916607146327",
    //         "218812744648356",
    //         "266651566528059",
    //         "311507488709490",
    //         "365480769986391",
    //         "425455547315353"
    //     };
    //
    //     Assert.That(
    //         debugTokenResponse.Data.GranularScopes.Where(x => x.Scope == "whatsapp_business_management")
    //             .SelectMany(x =>
    //             {
    //                 if (x.TargetIds != null)
    //                 {
    //                     return x.TargetIds;
    //                 }
    //
    //                 return new List<string>();
    //             }),
    //         Is.EquivalentTo(wabaTargetIds));
    //
    //     var wabaEncryptedBusinessIntegrationSystemUserAccessToken =
    //         _wabaService.GetEncryptedFacebookBusinessIntegrationSystemUserAccessToken(
    //             businessIntegrationSystemUserAccessToken,
    //             debugTokenResponse);
    //
    //     var decryptedBusinessIntegrationSystemUserAccessToken =
    //         new DecryptedBusinessIntegrationSystemUserAccessTokenDto(
    //             wabaEncryptedBusinessIntegrationSystemUserAccessToken,
    //             _secretConfig.FacebookBusinessIntegrationSystemUserAccessTokenSecret);
    //
    //     Assert.That(
    //         businessIntegrationSystemUserAccessToken,
    //         Is.EqualTo(decryptedBusinessIntegrationSystemUserAccessToken.DecryptedToken));
    // }

    [TestCase("{\"facebook_waba_id\":\"***************\",\"facebook_waba_business_id\":\"***************\",\"facebook_business_id\":\"***************\",\"sleekflow_company_ids\":[\"3e4556b4-35d8-4b9b-a60f-583f056521ff\",\"b6d7e442-38ae-4b9a-b100-2951729768bc\",\"5f12ad66-c8e6-4421-9773-b7d47c7abc7c\",\"72bf1000-e5f5-4ced-aa14-1e0ace717391\",\"6984c6f3-659e-46ff-9cef-b033b9d39cd2\"],\"facebook_waba_name\":\"Cloud API Test\",\"facebook_waba_account_review_status\":\"APPROVED\",\"facebook_waba_business_name\":\"WABA Migration Business\",\"facebook_waba_primary_funding_id\":\"****************\",\"facebook_waba_message_template_namespace\":\"5df47fd9_4f94_4fef_87b9_b2a2a17a662c\",\"facebook_waba_long_lived_access_token\":{\"encrypted_token\":\"ny1dz0jPSXgxXzggxaShlT+dVeIzsrHfd19pwJLyGD3U/Wdbnz5v13aWyZVnas7aDx+4jwXCQCKNwUtW7fz0Ffjvb+KVQ6eMXhqUJ2qRVuHTlMNV1QEDFIpiJ1Ck1bRGtdD/Zvre2mzE1HGNhVscOXWJ5Y+n0cxrlpayxfy0IL7pWY4/N1rIxPPLfbMO/Dasj4fuK5O96Y6Jk12x+qkbZ1zqe/bJEwn7XBB8MVhe8iVNuEOcnK2P8tyL30lSEWaFP4VFrwYsmySW2IgjRqYteQ==SdD4K6gj=yOGyzeY\",\"token_type\":\"bearer\",\"expiry_datetime\":null,\"is_valid\":true},\"facebook_business_integration_system_user_access_tokens\":[{\"encrypted_token\":\"QM6N1R1d+DMOABYTLygfwGghB8B7RUUfY0rKgEWLPz8CV3bvt1YqIrUORn0cKY5tHYWZkGlEDrWr/lzATWXWWCr7SPK1g3hk0B6Hxc52q9gcR+VdGgmszhVKgcmNeldo8a++ivWd80+sbzfqZZFYw+zWiU7S1ccre5+xCj8gG0w3Bt3BuZ6JJH8l9dv3dNpKhBQ/Tj9lWOBZu6m9zl3bqsdAMvI1Ok/uArntbrl27lGsuWOpo4i3t8+9k4llWgK9D23xmp9ncVDNbi0PUSsqwGN90MAu5FXSrb1L7S6gG6/Uoat7BaQgsQA+CR+dFlm2QKGlgnMbQBQ+yoVqP5Hveg==mTjZSAzfL8Heh1SU\",\"scopes\":[\"catalog_management\",\"pages_show_list\",\"ads_management\",\"business_management\",\"whatsapp_business_management\",\"pages_read_engagement\",\"whatsapp_business_messaging\",\"public_profile\"],\"granular_scopes\":[{\"scope\":\"pages_show_list\",\"target_ids\":null},{\"scope\":\"ads_management\",\"target_ids\":null},{\"scope\":\"business_management\",\"target_ids\":null},{\"scope\":\"whatsapp_business_management\",\"target_ids\":[\"167692543098079\",\"165381826664510\",\"***************\"]},{\"scope\":\"pages_read_engagement\",\"target_ids\":null},{\"scope\":\"whatsapp_business_messaging\",\"target_ids\":[\"167692543098079\",\"165381826664510\",\"***************\"]}],\"facebook_app_id\":\"812364635796464\",\"facebook_application\":\"Sleekflow\",\"facebook_business_system_user_id\":\"122132192942084069\",\"created_at\":\"2024-01-18T13:15:24.541Z\"},{\"encrypted_token\":\"pAPFoF83w76cU1gEb44MSKg1GGOn2u/DzabHk4v5RaW0NKc4cVjnNCXSTXWAGEtXxGOhDCsMlnDFgjcicdR3PgHazkfbb1qrO6GZV3tCs0JreRs1bf81rPa8nU6o6GU7Z6WRNIgxUpJnkuUJoFGBYVZx19dAu/X7+vzDG15gJR0zRwcPbhoP65oetw/ZjlATfa5AMdfsxbCCRhJEgMx5lfxCkuKILZKYaDVLw36LUAjBRUpfsK13Rpk23jrWXq7693t0rtOh98eb1zdx+3v5IexY1dnG/njSnQg6LahIc4PGME42rOk4Da4Qjnb3zWtJjKO/V87nsMpU/K0qTXPzUKQuZ0blVlD+udQJqNTvDfE=wYxP0CVM7Ip2qFlo\",\"scopes\":[\"catalog_management\",\"pages_show_list\",\"ads_management\",\"business_management\",\"whatsapp_business_management\",\"pages_read_engagement\",\"whatsapp_business_messaging\",\"public_profile\"],\"granular_scopes\":[{\"scope\":\"pages_show_list\",\"target_ids\":null},{\"scope\":\"ads_management\",\"target_ids\":null},{\"scope\":\"business_management\",\"target_ids\":null},{\"scope\":\"whatsapp_business_management\",\"target_ids\":[\"167692543098079\",\"165381826664510\",\"***************\"]},{\"scope\":\"pages_read_engagement\",\"target_ids\":null},{\"scope\":\"whatsapp_business_messaging\",\"target_ids\":[\"167692543098079\",\"165381826664510\",\"***************\"]}],\"facebook_app_id\":\"812364635796464\",\"facebook_application\":\"Sleekflow\",\"facebook_business_system_user_id\":\"122132192942084069\",\"created_at\":\"2024-01-18T12:55:04.805Z\"}],\"facebook_waba_business_detail_snapshot\":{\"id\":\"***************\",\"block_offline_analytics\":true,\"timezone_id\":0,\"name\":\"WABA Migration Business\",\"profile_picture_uri\":\"https://scontent-hkg4-1.xx.fbcdn.net/v/t39.30808-1/307572445_113607468193701_5657815327093283721_n.png?stp=dst-png_p100x100&_nc_cat=106&ccb=1-7&_nc_sid=5f2048&_nc_ohc=8zJC0eVIJSEAX8OwmKM&_nc_ht=scontent-hkg4-1.xx&edm=AHdzj10EAAAA&oh=00_AfBGDeTvcKuPnbgLDTDxSNfWsq7jxkdDP11NI-UmK8y3CA&oe=661284E9\",\"vertical\":\"NOT_SET\",\"verification_status\":\"not_verified\",\"link\":\"https://www.facebook.com/***************\",\"is_hidden\":false},\"facebook_waba_business_account_snapshot\":{\"id\":\"***************\",\"name\":\"Cloud API Test\",\"account_review_status\":\"APPROVED\",\"on_behalf_of_business_info\":{\"name\":\"WABA Migration Business\",\"id\":\"***************\",\"status\":\"APPROVED\",\"type\":\"SELF\"},\"message_template_namespace\":\"5df47fd9_4f94_4fef_87b9_b2a2a17a662c\",\"timezone_id\":\"62\",\"primary_funding_id\":\"****************\",\"currency\":\"USD\",\"purchase_order_number\":null},\"waba_phone_numbers\":[{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"GREEN\",\"new_certificate\":null,\"name_status\":\"AVAILABLE_WITHOUT_REVIEW\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":\"CmwKKAjhyZK/8aH1AhIGZW50OndhIg9DaGF0aHUgQnVzaW5lc3NQz8SBpgYaQOfYFZSjfdn2JaKZjiQo0JTkHl/6Gpu7f+JWr6APY8r3WMPC8+T9vvtgEn1W9/vpRuV+Ee8f16xgXOBNGzTFZQsSLW1OPona6OIn4EOIspmrayiWW+LgWu0P2cdqh2iNcRuPuQEsF8oSxNIE6zhkqA==\",\"code_verification_status\":\"EXPIRED\",\"display_phone_number\":\"+60 17-613 1656\",\"verified_name\":\"Chathu Business\",\"is_pin_enabled\":true,\"is_official_business_account\":false,\"messaging_limit_tier\":\"TIER_250\",\"quality_score\":{\"date\":null,\"reasons\":null,\"score\":\"GREEN\"},\"status\":\"CONNECTED\",\"whatsapp_commerce_settings\":{\"data\":[{\"id\":\"***************\",\"is_catalog_visible\":true,\"is_cart_enabled\":true}]}},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"in_active\",\"whatsapp_commerce_setting\":{\"facebook_whatsapp_commerce_setting_id\":\"***************\",\"is_catalog_visible\":true,\"is_cart_enabled\":true},\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-06-26T17:00:02.932Z\",\"updated_at\":\"2024-04-03T03:23:35.756Z\",\"id\":\"DPioWLbWQM7mLK\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"GREEN\",\"new_certificate\":null,\"name_status\":\"NONE\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":\"CmsKJwjUqM+v+dG7AxIGZW50OndhIg5UaW0ncyBCdXNpbmVzc1DeoeKfBhpAvtbHtNDJcsEaG3DeDy5QJmox/n9oEw2UEQSZViBKlPpsQgJ0Jl2kIu7EhMUHueS2JtMHLasn6kmgPJd/ulK+BBItbU4+idro4ifgQ4a3mqpsLpBe4+Fe7R68pFOHaI2LtHKXtRmZurJMQo6YztaL\",\"code_verification_status\":\"NOT_VERIFIED\",\"display_phone_number\":\"+852 6175 4742\",\"verified_name\":\"Tim's Business\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":\"TIER_250\",\"quality_score\":null,\"status\":\"CONNECTED\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":\"b6d7e442-38ae-4b9a-b100-2951729768bc\",\"webhook_url\":null,\"record_status\":\"in_active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2022-11-24T03:10:51.903Z\",\"updated_at\":\"2024-04-03T03:23:35.756Z\",\"id\":\"08iNgAb2pZEAEy\",\"sys_type_name\":\"WabaPhoneNumber\"}],\"messaging_function_limitation\":null,\"waba_active_product_catalog\":null,\"waba_product_catalog\":{\"facebook_product_catalog_id\":\"***************\",\"facebook_product_catalog_name\":\"cloud api catalog\",\"sleekflow_company_id\":\"b6d7e442-38ae-4b9a-b100-2951729768bc\",\"default_image_url\":\"\",\"product_count\":13,\"vertical\":\"commerce\",\"status\":\"inactive\",\"created_by\":null,\"updated_by\":{\"sleekflow_staff_id\":\"73d7ea1a-1c85-40da-91ee-723eb2604088\",\"sleekflow_staff_team_ids\":null},\"created_at\":\"2023-05-15T17:00:02.381Z\",\"updated_at\":\"2024-04-03T02:41:15.987Z\",\"id\":\"AniqaZR0zLzV0a\",\"sys_type_name\":\"WabaProductCatalog\"},\"created_by\":null,\"updated_by\":null,\"created_at\":\"2022-11-24T03:10:51.895Z\",\"updated_at\":\"2024-04-03T03:23:35.756Z\",\"_etag\":\"\\\"3e03b1a5-0000-1900-0000-660ccbb70000\\\"\",\"id\":\"vXU63Pgn5rZeLG\",\"sys_type_name\":\"Waba\",\"_rid\":\"x18TAKNRhw0BAAAAAAAAAA==\",\"_self\":\"dbs/x18TAA==/colls/x18TAKNRhw0=/docs/x18TAKNRhw0BAAAAAAAAAA==/\",\"_attachments\":\"attachments/\",\"_ts\":1712114615}")]
    public void WabaIdGranularScopesTest_HasEnabledAllGranularScopes(string wabaJsonString)
    {
        var waba = JsonConvert.DeserializeObject<Waba>(
            wabaJsonString);

        Assert.That(waba, Is.Not.Null);
        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba!);

        Assert.That(hasEnabledFLFB, Is.True);
        Assert.That(decryptedBusinessIntegrationSystemUserAccessTokenDto, Is.Not.Null);
    }

    [TestCase("{\"facebook_waba_id\":\"***************\",\"facebook_waba_business_id\":\"****************\",\"facebook_business_id\":\"****************\",\"sleekflow_company_ids\":[\"3e4556b4-35d8-4b9b-a60f-583f056521ff\",\"b6d7e442-38ae-4b9a-b100-2951729768bc\",\"4f7ce52d-1542-4616-b5d1-2cc483635456\",\"0f042b14-32c4-4042-b39c-69c524cef9cd\"],\"facebook_waba_name\":\"Migration Testing\",\"facebook_waba_account_review_status\":\"APPROVED\",\"facebook_waba_business_name\":\"Phone Number Migration Testing\",\"facebook_waba_primary_funding_id\":\"****************\",\"facebook_waba_message_template_namespace\":\"24527159_41fa_4612_a2fd_fc78574f4a18\",\"facebook_waba_long_lived_access_token\":{\"encrypted_token\":\"v1RYuWwsGDlwR0RVmmbzCq5SfSQzFjTZbdsmp0ReEfxbm42gVyu3x9hItFu1MWA578LmEEpkeOOHH0KWfIHBLtc0LlpDFRC2UWXq90c/Q8eWrOlNDF6lY9woH3DM0XUSOSzA/yPohFd3NuX+h6hUOUt8YtiXn/7aT9WrSisan1CQ2WCfkG5hSKfRfL8bBkFXbUkNTSpOTOepouc83abonbcLbFKbYUJe3Ob71gOprpsOOxKJa4H8VBk9zfGoAuZ4tSIDfpz5CUSysYJeVHxLxg==85857yUJ8vAQi1vX\",\"token_type\":\"bearer\",\"expiry_datetime\":null,\"is_valid\":true},\"facebook_business_integration_system_user_access_tokens\":[{\"encrypted_token\":\"3EGMMi2hl4tlujeuQcBoh/bQKkrpKUoGxgx9+RFmVrqlQHAdiZGmg42LmmpD+lOxxftAcYOafjVU5onuENkahbZJYeahVuFDPskquIrMj6oknfaeV3YVUxeegat9I1yW55XICfbruhjK+WM0CxAwhRQH43JglSJqe5Ytl7sURkWyDnqEHZBJfh5V52OnPbX1XNmIxwA2oAUnp/oQLNKpJZY4d25zt/CREs7W4/7Xx/9cK4RtHhf9+AqprIiz034wMdsdkjWV57m0Ulz02ECIAtbD8nVXZevi/FXXgoSmrH4sNSLiZST0lZpq6zPZ811/VA0Zh6baRIJimAUGBBUPVD1MhrABlNVR89yQuN1SzLw=EFedaHlbCzdrERQZ\",\"scopes\":[\"catalog_management\",\"pages_show_list\",\"ads_management\",\"business_management\",\"whatsapp_business_management\",\"pages_read_engagement\",\"whatsapp_business_messaging\",\"public_profile\"],\"granular_scopes\":[{\"scope\":\"pages_show_list\",\"target_ids\":null},{\"scope\":\"ads_management\",\"target_ids\":null},{\"scope\":\"business_management\",\"target_ids\":null},{\"scope\":\"whatsapp_business_management\",\"target_ids\":[\"266651566528059\",\"102645852696034\",\"203078266219699\",\"193916607146327\",\"218812744648356\",\"120227277709052\",\"157195750819481\",\"204578129404425\",\"117966677845309\",\"193124547221585\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"***************\",\"116742654727279\",\"113936408276909\",\"107305245672915\"]},{\"scope\":\"pages_read_engagement\",\"target_ids\":null},{\"scope\":\"whatsapp_business_messaging\",\"target_ids\":[\"266651566528059\",\"102645852696034\",\"203078266219699\",\"193916607146327\",\"218812744648356\",\"120227277709052\",\"157195750819481\",\"204578129404425\",\"117966677845309\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"116742654727279\",\"113936408276909\",\"107305245672915\"]}],\"facebook_app_id\":\"812364635796464\",\"facebook_application\":\"Sleekflow\",\"facebook_business_system_user_id\":\"122122929788090465\",\"created_at\":\"2024-03-07T08:09:54.887Z\"},{\"encrypted_token\":\"w6Q5Z3PzVEYGLox+6lS6uwJwSudLvcfwxS+v8Xv4WoixcPflqGa7csZ8KgLZQ+9L65fsBtvmlzaDb1fkx3zoKc2gPHWYqruBgSI8Sr+WArGSpMaRRLLRGnlodkOHMIsh1tmUtPFAS478qm+/vFbVm436HhF0VKgq2NrrD58P/VeSEqv2j5GsiWoHvoKqmWr+GVeCGb91Eg9dkTP4xqmw85T3X5rSOOs0vlKY9ZsnmC+NVllHQYI5JujRR8THm2r4bjhqvdcYOEYvhWP4URpTCe5hyDcJV50oEaHQRV5Y5fuL9kJPU8BhqLy8l13gYPli2/GHgbC3l72Sf/gpTLKXSSF3Ej2iKqcKzZLrwBZmtf8=ha1R9FlQpo2NrPCO\",\"scopes\":[\"catalog_management\",\"pages_show_list\",\"ads_management\",\"business_management\",\"whatsapp_business_management\",\"pages_read_engagement\",\"whatsapp_business_messaging\",\"public_profile\"],\"granular_scopes\":[{\"scope\":\"pages_show_list\",\"target_ids\":null},{\"scope\":\"ads_management\",\"target_ids\":null},{\"scope\":\"business_management\",\"target_ids\":null},{\"scope\":\"whatsapp_business_management\",\"target_ids\":[\"102645852696034\",\"203078266219699\",\"193916607146327\",\"218812744648356\",\"120227277709052\",\"157195750819481\",\"204578129404425\",\"117966677845309\",\"193124547221585\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"***************\",\"116742654727279\",\"113936408276909\",\"107305245672915\"]},{\"scope\":\"pages_read_engagement\",\"target_ids\":null},{\"scope\":\"whatsapp_business_messaging\",\"target_ids\":[\"102645852696034\",\"203078266219699\",\"193916607146327\",\"218812744648356\",\"120227277709052\",\"157195750819481\",\"204578129404425\",\"117966677845309\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"116742654727279\",\"113936408276909\",\"107305245672915\"]}],\"facebook_app_id\":\"812364635796464\",\"facebook_application\":\"Sleekflow\",\"facebook_business_system_user_id\":\"122122929788090465\",\"created_at\":\"2024-03-01T09:16:11.159Z\"},{\"encrypted_token\":\"neRA64iCp9aA090cPtmzTR85H4bwxZNKaUFye1UoDDXVWgifQPqsJoKPXBSNDyZNR2Mh9woyRMn68LwfrYOvk87xquabi4WR8Hl3JvnzHICQy2WEBbdr90Q+wrCZq5JCaZ8VBtR813CPsAs4pg0r5AcFb37g9Ngn+fqD+VylKg4pOiVV9f/0GDKwlhiLLyv4k6DUa/if5bX0JE0sYPMequ3/OXFpt5yVtfqpc/7ez1nmZioVMj0Qarn7YjLBudcRmmw6JKM+Azv+bs0vdqAOS1kJoDCRA8V+QXEJDw6NFl0JtFtPzhIpqazjD7+JgMFdl0Z6LxoSf47VtxfBOg7PLoeQ/DOVg/0ur/FaZPXU2Vg=UaUKr3bfv6BRxMrh\",\"scopes\":[\"catalog_management\",\"pages_show_list\",\"ads_management\",\"business_management\",\"whatsapp_business_management\",\"pages_read_engagement\",\"whatsapp_business_messaging\",\"public_profile\"],\"granular_scopes\":[{\"scope\":\"pages_show_list\",\"target_ids\":null},{\"scope\":\"ads_management\",\"target_ids\":null},{\"scope\":\"business_management\",\"target_ids\":null},{\"scope\":\"whatsapp_business_management\",\"target_ids\":[\"120227277709052\",\"157195750819481\",\"204578129404425\",\"203078266219699\",\"117966677845309\",\"193124547221585\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"***************\",\"116742654727279\",\"113936408276909\",\"102645852696034\",\"107305245672915\"]},{\"scope\":\"pages_read_engagement\",\"target_ids\":null},{\"scope\":\"whatsapp_business_messaging\",\"target_ids\":[\"120227277709052\",\"157195750819481\",\"204578129404425\",\"203078266219699\",\"117966677845309\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"116742654727279\",\"113936408276909\",\"102645852696034\",\"107305245672915\"]}],\"facebook_app_id\":\"812364635796464\",\"facebook_application\":\"Sleekflow\",\"facebook_business_system_user_id\":\"122122929788090465\",\"created_at\":\"2024-01-19T04:12:34.288Z\"},{\"encrypted_token\":\"MHeFQglFvzbZby/rTU68R8Ji7hi6wMCkjZqvVHbFLWacAC0ypSvAIwAGbI8nuLpZp2VROJ+9LUOV8msruoE1CCLTR/6MUH7nz3lhNECVZfcAo6ZJWH6x/eE93nLnKwlWPPI8KUtqj02yqRjgUUl6jm+epCuehAG/K5vr7kplzeJ43g9tmUMpfjfDAoyMcyAwrjWlQDZy+fv3ziMMfart7Bqg8OCjDU8+LVS9tNIcIwMA+RvPC0B8BfC9koAvL9QDpbRlOSH3JLl1tP9AtCjsLrWgo1M9HWG6BNSc3WqVjS/AphTrfH9ksLcVRaKOObZTSsAGmXe6jvZbVqAVT+UeDoyNvBafflTIyP6VALA5Ok0=jKfnOftyfZKGPv1=\",\"scopes\":[\"business_management\",\"whatsapp_business_management\",\"whatsapp_business_messaging\",\"public_profile\"],\"granular_scopes\":[{\"scope\":\"business_management\",\"target_ids\":null},{\"scope\":\"whatsapp_business_management\",\"target_ids\":[\"204578129404425\",\"203078266219699\",\"157195750819481\",\"117966677845309\",\"193124547221585\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"***************\",\"120227277709052\",\"116742654727279\",\"113936408276909\",\"102645852696034\",\"107305245672915\"]},{\"scope\":\"whatsapp_business_messaging\",\"target_ids\":[\"204578129404425\",\"203078266219699\",\"157195750819481\",\"117966677845309\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"120227277709052\",\"116742654727279\",\"113936408276909\",\"102645852696034\",\"107305245672915\"]}],\"facebook_app_id\":\"812364635796464\",\"facebook_application\":\"Sleekflow\",\"facebook_business_system_user_id\":\"122122929788090465\",\"created_at\":\"2024-01-19T02:46:00.540Z\"},{\"encrypted_token\":\"M1zLWYTn9mQPa3bQtMd7oytbd2vIa76CM71NLc40KB94zYLtPt39cg6PtMYN+AvBV77iRbVR0826JmE4gWeRb54delxhnZe/YOZdV5WVY/1sGAaj0JSp4ao6eDm1rpDwmHtW1JFdWNkLD74s/8giTGaMSCGvPFUhImwkchli8JhYutxphQhF7UkWqEp4yEr1f46ApoQ7mDjRIa2FVSFYiRGrBH8KBi+tTKcrBaCFGWmgdnhSfP8mDBDRqBw4TEgkbADt9wT3+4OgF6LXbrmrOwGHIJ5VtWN2aik3hJsESQRlShU59dzZgrpkqz96Khml7n2dwyvN0rigKXiZo1an2F1r5YcIbYnHjyIj6HOrulE=pbejbN3MrVJm6m6E\",\"scopes\":[\"business_management\",\"whatsapp_business_management\",\"whatsapp_business_messaging\",\"public_profile\"],\"granular_scopes\":[{\"scope\":\"business_management\",\"target_ids\":null},{\"scope\":\"whatsapp_business_management\",\"target_ids\":[\"157195750819481\",\"204578129404425\",\"203078266219699\",\"117966677845309\",\"193124547221585\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"***************\",\"120227277709052\",\"116742654727279\",\"113936408276909\",\"102645852696034\",\"107305245672915\"]},{\"scope\":\"whatsapp_business_messaging\",\"target_ids\":[\"157195750819481\",\"204578129404425\",\"203078266219699\",\"117966677845309\",\"106483902463923\",\"154078304458072\",\"170012979528742\",\"102301196164109\",\"120227277709052\",\"116742654727279\",\"113936408276909\",\"102645852696034\",\"107305245672915\"]}],\"facebook_app_id\":\"812364635796464\",\"facebook_application\":\"Sleekflow\",\"facebook_business_system_user_id\":\"122122929788090465\",\"created_at\":\"2024-01-18T09:04:39.431Z\"}],\"facebook_waba_business_detail_snapshot\":{\"id\":\"****************\",\"block_offline_analytics\":true,\"timezone_id\":1,\"name\":\"Phone Number Migration Testing\",\"profile_picture_uri\":\"https://scontent-hkg1-2.xx.fbcdn.net/v/t31.18172-1/14241451_1001469880061_6878272705818104760_o.png?stp=dst-png_p100x100&_nc_cat=104&ccb=1-7&_nc_sid=5f2048&_nc_ohc=Th-aIGNMzRMAX8f_G1I&_nc_ht=scontent-hkg1-2.xx&edm=AHdzj10EAAAA&oh=00_AfDshy28ieQ0ez74tqRHXPUwAaY0YZSGBSonUQC2FqboBw&oe=********\",\"vertical\":\"OTHER\",\"verification_status\":\"verified\",\"link\":\"https://business.facebook.com/business/****************\",\"is_hidden\":false},\"facebook_waba_business_account_snapshot\":{\"id\":\"***************\",\"name\":\"Migration Testing\",\"account_review_status\":\"APPROVED\",\"on_behalf_of_business_info\":{\"name\":\"Phone Number Migration Testing\",\"id\":\"****************\",\"status\":\"APPROVED\",\"type\":\"SELF\"},\"message_template_namespace\":\"24527159_41fa_4612_a2fd_fc78574f4a18\",\"timezone_id\":\"42\",\"primary_funding_id\":\"****************\",\"currency\":\"USD\",\"purchase_order_number\":null},\"waba_phone_numbers\":[{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"UNKNOWN\",\"new_certificate\":null,\"name_status\":\"APPROVED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"EXPIRED\",\"display_phone_number\":\"******-318-6123\",\"verified_name\":\"SleekFlow\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":null,\"quality_score\":null,\"status\":\"PENDING\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":\"3e4556b4-35d8-4b9b-a60f-583f056521ff\",\"webhook_url\":null,\"record_status\":\"active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-02-16T17:00:06.543Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"qYirmN7e2zdynN\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"GREEN\",\"new_certificate\":null,\"name_status\":\"DECLINED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"EXPIRED\",\"display_phone_number\":\"******-628-7511\",\"verified_name\":\"Cloud Api Migration\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":\"TIER_250\",\"quality_score\":{\"date\":null,\"reasons\":null,\"score\":\"GREEN\"},\"status\":\"CONNECTED\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-02-20T17:00:06.174Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"JPiWM6a1m6KgmA\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"UNKNOWN\",\"new_certificate\":null,\"name_status\":\"DECLINED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"EXPIRED\",\"display_phone_number\":\"******-556-8862\",\"verified_name\":\"Sleekflow Product Catalogs\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":null,\"quality_score\":null,\"status\":\"MIGRATED\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-04-18T11:04:07.624Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"DPiJaGMdVK6moX\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"UNKNOWN\",\"new_certificate\":null,\"name_status\":\"DECLINED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"NOT_VERIFIED\",\"display_phone_number\":\"******-515-3803\",\"verified_name\":\"Testing\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":null,\"quality_score\":null,\"status\":\"PENDING\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"in_active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-02-13T17:00:06.432Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"VGiV0PJXm30b1L\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"UNKNOWN\",\"new_certificate\":null,\"name_status\":\"DECLINED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"NOT_VERIFIED\",\"display_phone_number\":\"******-756-3116\",\"verified_name\":\"Cloud Api Migration\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":null,\"quality_score\":null,\"status\":\"PENDING\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"in_active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2022-11-29T06:15:21.804Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"zYiprY364NAPG6\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"UNKNOWN\",\"new_certificate\":null,\"name_status\":\"DECLINED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"NOT_VERIFIED\",\"display_phone_number\":\"******-258-8183\",\"verified_name\":\"Phone Number Migration Testing\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":null,\"quality_score\":null,\"status\":\"PENDING\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"in_active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-02-13T17:00:06.432Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"GPiJDxEd2PDWR1\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"UNKNOWN\",\"new_certificate\":null,\"name_status\":\"APPROVED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"NOT_VERIFIED\",\"display_phone_number\":\"******-602-9520\",\"verified_name\":\"Cloud Api Migration\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":null,\"quality_score\":null,\"status\":\"PENDING\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"in_active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-02-20T17:00:06.174Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"rEig3NAJoPWWGa\",\"sys_type_name\":\"WabaPhoneNumber\"},{\"facebook_phone_number_id\":\"***************\",\"facebook_phone_number_detail\":{\"id\":\"***************\",\"quality_rating\":\"UNKNOWN\",\"new_certificate\":null,\"name_status\":\"APPROVED\",\"new_name_status\":\"NONE\",\"account_mode\":\"LIVE\",\"certificate\":null,\"code_verification_status\":\"NOT_VERIFIED\",\"display_phone_number\":\"******-661-4105\",\"verified_name\":\"Testing\",\"is_pin_enabled\":false,\"is_official_business_account\":false,\"messaging_limit_tier\":null,\"quality_score\":null,\"status\":\"PENDING\",\"whatsapp_commerce_settings\":null},\"sleekflow_company_id\":null,\"webhook_url\":null,\"record_status\":\"in_active\",\"whatsapp_commerce_setting\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-02-10T17:00:06.864Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"id\":\"4DiaDZ3BgP4Ee4\",\"sys_type_name\":\"WabaPhoneNumber\"}],\"messaging_function_limitation\":null,\"waba_active_product_catalog\":null,\"waba_product_catalog\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2022-11-28T09:08:13.612Z\",\"updated_at\":\"2024-04-03T03:23:38.308Z\",\"_etag\":\"\\\"3e03ada6-0000-1900-0000-660ccbba0000\\\"\",\"id\":\"prUeG5dv130gQd\",\"sys_type_name\":\"Waba\",\"_rid\":\"x18TAKNRhw0IAAAAAAAAAA==\",\"_self\":\"dbs/x18TAA==/colls/x18TAKNRhw0=/docs/x18TAKNRhw0IAAAAAAAAAA==/\",\"_attachments\":\"attachments/\",\"_ts\":**********}")]
    public void WabaIdGranularScopesTest_HasNotEnabledAllGranularScopes(string wabaJsonString)
    {
        var waba = JsonConvert.DeserializeObject<Waba>(
            wabaJsonString);

        Assert.That(waba, Is.Not.Null);
        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba!);

        Assert.That(hasEnabledFLFB, Is.False);
        Assert.That(decryptedBusinessIntegrationSystemUserAccessTokenDto, Is.Null);
    }

    [TestCase("{\"facebook_waba_id\":\"***************\",\"facebook_waba_business_id\":\"***************\",\"facebook_business_id\":\"***************\",\"sleekflow_company_ids\":[\"b6d7e442-38ae-4b9a-b100-2951729768bc\"],\"facebook_waba_name\":\"Dsvdsv\",\"facebook_waba_account_review_status\":\"REJECTED\",\"facebook_waba_business_name\":\"Dsvdsv\",\"facebook_waba_primary_funding_id\":\"****************\",\"facebook_waba_message_template_namespace\":\"50f40953_6c69_4f2a_bef1_ab9c1aeb440f\",\"facebook_waba_long_lived_access_token\":{\"encrypted_token\":\"fsXonSTxPrZRPcLSZ/3DjDtHqBs12Ta4R6Tlqs6y+YmCPoGazy0CM4jnUZDlvJPlUpweKiufqqS/bJUXtyAus3iD+FLSvZcjpQTNL6R52Pv243Q2Ag82taO2ta6szf12Vd3KUgWsTfvYcg4tPMWh8ARvwNRzFlfhRScTbPPdnMrb4IW4mPCQkiOINgZ4ulnN2zB5DwcctnnC2m3uoXaXXYiKLqPaxCfB+pjoVh4NCzGkj2WZ6+s40pyMQc9B8eq4OzhirYGvKid2OaM6\",\"token_type\":\"bearer\",\"expiry_datetime\":null,\"is_valid\":null},\"facebook_business_integration_system_user_access_tokens\":null,\"facebook_waba_business_detail_snapshot\":{\"id\":\"***************\",\"block_offline_analytics\":true,\"timezone_id\":1,\"name\":\"Dsvdsv\",\"profile_picture_uri\":\"https://scontent-hkg1-2.xx.fbcdn.net/v/t31.18172-1/14257718_1001469870638_5649067323506070663_o.png?stp=dst-png_p100x100&_nc_cat=103&ccb=1-7&_nc_sid=5f2048&_nc_ohc=8jxTnZP5xV4AX9ich5t&_nc_ht=scontent-hkg1-2.xx&edm=AHdzj10EAAAA&oh=00_AfCr2cHRDwhipMJVEw5pfzmWAdNatnHrTt-3aBlibC3tBA&oe=6634537F\",\"vertical\":\"OTHER\",\"verification_status\":\"not_verified\",\"link\":\"https://business.facebook.com/business/***************\",\"is_hidden\":false},\"facebook_waba_business_account_snapshot\":{\"id\":\"***************\",\"name\":\"Dsvdsv\",\"account_review_status\":\"REJECTED\",\"on_behalf_of_business_info\":{\"name\":\"Dsvdsv\",\"id\":\"***************\",\"status\":\"APPROVED\",\"type\":\"SELF\"},\"message_template_namespace\":\"50f40953_6c69_4f2a_bef1_ab9c1aeb440f\",\"timezone_id\":\"1\",\"primary_funding_id\":\"****************\",\"currency\":\"USD\",\"purchase_order_number\":null},\"waba_phone_numbers\":[],\"messaging_function_limitation\":null,\"waba_active_product_catalog\":null,\"waba_product_catalog\":null,\"created_by\":null,\"updated_by\":null,\"created_at\":\"2023-02-10T06:23:25.437Z\",\"updated_at\":\"2024-04-03T03:23:43.113Z\",\"_etag\":\"\\\"3e03f2a7-0000-1900-0000-660ccbbf0000\\\"\",\"id\":\"AAUgQWB0p25aWP\",\"sys_type_name\":\"Waba\",\"_rid\":\"x18TAKNRhw02AAAAAAAAAA==\",\"_self\":\"dbs/x18TAA==/colls/x18TAKNRhw0=/docs/x18TAKNRhw02AAAAAAAAAA==/\",\"_attachments\":\"attachments/\",\"_ts\":**********}")]
    public void WabaIdGranularScopesTest_HasNotEnabledFLFB(string wabaJsonString)
    {
        var waba = JsonConvert.DeserializeObject<Waba>(
            wabaJsonString);

        Assert.That(waba, Is.Not.Null);
        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba!);

        Assert.That(hasEnabledFLFB, Is.False);
        Assert.That(decryptedBusinessIntegrationSystemUserAccessTokenDto, Is.Null);
    }

}