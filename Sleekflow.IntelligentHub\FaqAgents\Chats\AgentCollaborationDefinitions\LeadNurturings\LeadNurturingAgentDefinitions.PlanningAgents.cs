using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

// Planning Agents: LeadAssignmentPlanningAgent, DemoSchedulingPlanningAgent
public partial class LeadNurturingAgentDefinitions
{
    [method: JsonConstructor]
    public class LeadAssignmentPlanningAgentResponse(
        string agentName,
        string actionType,
        LeadAssignmentInfo? leadAssignmentAction = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("plan_type")]
        public string ActionType { get; set; } = actionType;

        [JsonProperty("lead_assignment_action")]
        public LeadAssignmentInfo? LeadAssignmentAction { get; set; } = leadAssignmentAction;
    }

    [method: JsonConstructor]
    public class LeadAssignmentInfo(
        string? leadClassification = null,
        string? leadScore = null,
        string? targetTeam = null,
        string? assignmentReason = null,
        string? conversationSummary = null)
    {
        [JsonProperty("lead_classification")]
        public string? LeadClassification { get; set; } = leadClassification;

        [JsonProperty("lead_score")]
        public string? LeadScore { get; set; } = leadScore;

        [JsonProperty("target_team")]
        public string? TargetTeam { get; set; } = targetTeam;

        [JsonProperty("assignment_reason")]
        public string? AssignmentReason { get; set; } = assignmentReason;

        [JsonProperty("conversation_summary")]
        public string? ConversationSummary { get; set; } = conversationSummary;
    }

    [method: JsonConstructor]
    public class RequiredFieldInfo(string type, string reason)
    {
        [JsonProperty("type")]
        public string Type { get; set; } = type;

        [JsonProperty("reason")]
        public string Reason { get; set; } = reason;
    }

    public ChatCompletionAgent GetLeadAssignmentPlanningAgent(
        Kernel kernel,
        PromptExecutionSettings settings)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("plan_type", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "lead_assignment_action",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("lead_classification", "string"),
                        new PromptExecutionSettingsUtils.Property("lead_score", "string"),
                        new PromptExecutionSettingsUtils.Property("target_team_reasoning", "string"),
                        new PromptExecutionSettingsUtils.Property("target_team", "string"),
                        new PromptExecutionSettingsUtils.Property("assignment_reason", "string"),
                        new PromptExecutionSettingsUtils.Property("conversation_summary", "string")
                    ])
            ]);

        var leadNurturingTools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;

        string teamAssignmentInstructions;
        if (leadNurturingTools?.AssignmentTool == null || leadNurturingTools.AssignmentTool.Assignments.Count == 0)
        {
            teamAssignmentInstructions =
                """
                For team assignments, use the following guidelines:
                - For any type of lead, assign to the "supervisor" team.
                """;
        }
        else
        {
            var assignmentTool = leadNurturingTools.AssignmentTool;
            var assignments = assignmentTool.Assignments;

            teamAssignmentInstructions =
                $"""
                 For team assignments, use the following guidelines:
                 - Classify the conversation context based on CUSTOMER CONVERSATION CONTEXT to determine the team assignment.
                 {string.Join("\n", assignments.Select(a => a.Rule).Select(r => $"  - {r}"))}
                   - If there is no significant indication, or if there are no messages related to any specific team criteria, assign to the "supervisor" team. The supervisor team should be the last resort.
                 """;
        }

        var instructions =
            $$"""
              {{GetSharedSystemPrompt()}}
              You are {{LeadAssignmentPlanningAgentName}}. Your role is to analyze CUSTOMER CONVERSATION CONTEXT to prepare structured information for team assignment and record-keeping.

              Instructions:
              1. Look for the most recent message from the {{DecisionAgentName}} or {{KnowledgeRetrievalAgentName}} or {{ResponseCrafterAgentName}} in the conversation history.
              2. If the message contains { "decision": "assign_hot" } or { "decision": "assign_cold" } or { "decision": "assign_human" } or { "decision": "assign_drop_off" } or { "decision": "assign_insufficient_info" }, prepare a structured output with the information needed for team assignment.
              3. Analyze the entire CUSTOMER CONVERSATION CONTEXT and prepare a comprehensive output with the following fields:

              Output Format:
              {
                "agent_name": "{{LeadAssignmentPlanningAgentName}}",
                "plan_type": "assign_lead",
                "lead_assignment_action": {
                  "lead_classification": "[the hot/warm/cold by the LeadClassifierAgent]",
                  "lead_score": "[the numeric score (0-100) by the LeadClassifierAgent]",
                  "target_team_reasoning": "[reasoning for the team assignment according to the guidelines]",
                  "target_team": "[the team name]",
                  "assignment_reason": "[detailed explanation of why this assignment is happening, e.g. why DecisionAgent made this decision]",
                  "conversation_summary": "[comprehensive summary of the conversation between the lead and our company]",
                }
              }

              {{teamAssignmentInstructions}}
              """;

        return new ChatCompletionAgent
        {
            Name = LeadAssignmentPlanningAgentName,
            Description =
                "Analyzes conversation context to prepare structured information for team assignment and record-keeping based on lead classification and configurable assignment rules.",
            HistoryReducer = new ActionAgentGeminiChatHistoryReducer(this),
            Instructions = instructions,
            Kernel = kernel,
            Arguments = new KernelArguments(settings),
        };
    }

    [method: JsonConstructor]
    public class DemoSchedulingPlanningAgentResponse(
        string agentName,
        string actionType,
        DemoSchedulingInfo? demoSchedulingAction = null,
        List<InformationGatheringPlugin.ExtractedField>? extractedFields = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("plan_type")]
        public string ActionType { get; set; } = actionType;

        [JsonProperty("demo_scheduling_action")]
        public DemoSchedulingInfo? DemoSchedulingAction { get; set; } = demoSchedulingAction;

        [JsonProperty("extracted_fields")]
        public List<InformationGatheringPlugin.ExtractedField>? ExtractedFields { get; set; } = extractedFields;

        // Helper method to check if there are any missing required fields
        public bool HasMissingRequiredFields(IEnumerable<string> requiredFieldNames)
        {
            if (ExtractedFields == null || !ExtractedFields.Any())
            {
                return true; // If no gathered fields, then all required fields are missing
            }

            return requiredFieldNames.Any(requiredField =>
                !ExtractedFields.Any(field =>
                    field.FieldName == requiredField && field.FieldValue != null && field.IsValidFieldValue));
        }
    }

    public class DemoSchedulingInfo
    {
        [JsonProperty("lead_classification")]
        public string? LeadClassification { get; set; }

        [JsonProperty("lead_score")]
        public string? LeadScore { get; set; }

        [JsonProperty("fields")]
        public List<FieldInfo> Fields { get; set; } = new List<FieldInfo>();

        [JsonConstructor]
        public DemoSchedulingInfo(
            string? leadClassification = null,
            string? leadScore = null,
            List<FieldInfo>? fields = null)
        {
            LeadClassification = leadClassification;
            LeadScore = leadScore;
            Fields = fields ?? new List<FieldInfo>();
        }

        // Helper method to get field value by field name
        public string? GetFieldValue(string fieldName)
        {
            return Fields.FirstOrDefault(f => f.FieldName == fieldName)?.FieldValue;
        }
    }

    [method: JsonConstructor]
    public class FieldInfo(string fieldName, string fieldValue)
    {
        [JsonProperty("field_name")]
        public string FieldName { get; set; } = fieldName;

        [JsonProperty("field_value")]
        public string FieldValue { get; set; } = fieldValue;
    }

    public ChatCompletionAgent GetDemoSchedulingPlanningAgent(
        Kernel kernel,
        PromptExecutionSettings settings)
    {
        // Define the structured output schema for when the agent needs to craft its own responses
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("previous_schedules", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "extracted_fields",
                    "array",
                    false,
                    null,
                    new PromptExecutionSettingsUtils.Property(
                        string.Empty,
                        "object",
                        false,
                        [
                            new PromptExecutionSettingsUtils.Property("field_name", "string"),
                            new PromptExecutionSettingsUtils.Property("is_required", "boolean"),
                            new PromptExecutionSettingsUtils.Property("extraction_reasoning", "string"),
                            new PromptExecutionSettingsUtils.Property("field_value", "string", true),
                            new PromptExecutionSettingsUtils.Property("validation_reasoning", "string"),
                            new PromptExecutionSettingsUtils.Property("is_valid_field_value", "boolean"),
                        ])),
                new PromptExecutionSettingsUtils.Property(
                    "demo_scheduling_action",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("lead_classification", "string"),
                        new PromptExecutionSettingsUtils.Property("lead_score", "string"),
                        new PromptExecutionSettingsUtils.Property(
                            "fields",
                            "array",
                            false,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("field_name", "string"),
                                    new PromptExecutionSettingsUtils.Property("field_value", "string", true)
                                ]))
                    ]),
                new PromptExecutionSettingsUtils.Property("plan_type", "string"),
            ]);

        // Define the agent's instructions with refined structure
        var instructions =
            $$"""
              {{GetSharedSystemPrompt()}}
              You are {{DemoSchedulingPlanningAgentName}}. Your task is to process customer conversations to schedule demos by reviewing the field information extracted from CUSTOMER CONVERSATION CONTEXT.

              ### Instructions

              Follow these steps carefully:

              **Step 1: See previous schedules**
              - Now is {{DateTimeOffset.UtcNow}}
              - If previous schedules exist, set `previous_schedules` to "Book A: Booking time: XXX, Owner: YYY, Is Expired: true / false; Book B: Booking time: XXX, Owner: YYY, Is Past: true / false".
              - If there are no previous schedules, set `previous_schedules` to "No previous schedules".
              - Is Past means the booking start time is before now.

              **Step 2: Review the Extracted Fields**
              - A message from InformationGatheringPlugin will be included in your chat history with already extracted fields.
              - The extracted fields contain details about information found in CUSTOMER CONVERSATION CONTEXT like names, emails, etc.
              - Each field has a validation status and extraction reasoning already analyzed for you.

              **Step 3: Check Required Fields**
              - Review the extracted fields to determine if all required fields are present and valid.
              - If any required field is missing or invalid, proceed to **Step 4a**.
              - If all required fields are present and valid, proceed to **Step 4b**.

              **Step 4: Generate the Output**

              - **Step 4a: If there is an existing demo scheduled and all the demos are active (not expired)**:
                - Set `plan_type` to "existing_demo_scheduled".
                - Set `demo_scheduling_action` to `null`.
                - Include the `extracted_fields` from the InformationGatheringPlugin message.

              - **Step 4b: If Any Required Field is Missing or Invalid**:
                - Set `plan_type` to "modification_required".
                - Set `demo_scheduling_action` to `null`.
                - Include the `extracted_fields` from the InformationGatheringPlugin message.

              - **Step 4c: If All Required Fields are Present and Valid**:
                - Retrieve the most recent message from LeadClassifierAgent.
                - Extract `lead_classification` (e.g., "hot") and `lead_score` (e.g., "85").
                - Set `plan_type` to "schedule_demo".
                - Create `demo_scheduling_action` with:
                  - `lead_classification`
                  - `lead_score`
                  - `fields`: Array of objects with `field_name` and `field_value` for fields with non-null values.
                - Include the `extracted_fields` from the InformationGatheringPlugin message.

              ### Notes
              - The InformationGatheringPlugin has already performed complex field extraction and validation for you.
              - Your role is to review the extraction results and make decisions based on the provided information.
              - DO NOT perform your own field extraction as this has already been done for you.
              """;

        // Return the configured agent with the specialized reducer
        return new ChatCompletionAgent
        {
            Name = DemoSchedulingPlanningAgentName,
            Description =
                "Processes customer conversations for demo scheduling by extracting and validating field information, identifying both direct and indirect mentions across conversation history.",
            HistoryReducer =
                new DemoSchedulingPlanningAgentReducer(
                    this,
                    _informationGatheringPlugin,
                    _loggerFactory.CreateLogger<DemoSchedulingPlanningAgentReducer>(),
                    kernel),
            Instructions = instructions,
            Kernel = kernel,
            Arguments = new KernelArguments(settings),
        };
    }
}