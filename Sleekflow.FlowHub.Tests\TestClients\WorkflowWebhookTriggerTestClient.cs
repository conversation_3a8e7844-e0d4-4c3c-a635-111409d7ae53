﻿using Sleekflow.FlowHub.Triggers.Workflows;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.TestClients;

public static class WorkflowWebhookTriggerTestClient
{
    public static async Task<Output<CreateWorkflowWebhookTrigger.CreateWorkflowWebhookTriggerOutput>?>
        CreateWorkflowWebhookTriggerAsync(
            string sleekflowCompanyId,
            string workflowId,
            string objectIdExpression,
            string objectType,
            string staffId,
            List<string>? staffTeamIds = null)
    {
        var createWorkflowWebhookTriggerInput = new CreateWorkflowWebhookTrigger.CreateWorkflowWebhookTriggerInput(
            sleekflowCompanyId,
            workflowId,
            objectIdExpression,
            staffId,
            staffTeamIds,
            objectType);

        var createWorkflowWebhookTriggerScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.<PERSON><PERSON>Json(createWorkflowWebhookTriggerInput).ToUrl("/Workflows/CreateWorkflowWebhookTrigger");
            });

        var createWorkflowWebhookTriggerOutput = await createWorkflowWebhookTriggerScenarioResult
            .ReadAsJsonAsync<Output<CreateWorkflowWebhookTrigger.CreateWorkflowWebhookTriggerOutput>>();

        return createWorkflowWebhookTriggerOutput;
    }

    public static async Task<Output<GetOrCreateWorkflowWebhookTrigger.GetOrCreateWorkflowWebhookTriggerOutput>?>
        GetOrCreateWorkflowWebhookTriggerAsync(
            string sleekflowCompanyId,
            string workflowId,
            string staffId,
            List<string>? staffTeamIds = null)
    {
        var getOrCreateWorkflowWebhookTriggerInput = new GetOrCreateWorkflowWebhookTrigger.GetOrCreateWorkflowWebhookTriggerInput(
            sleekflowCompanyId,
            workflowId,
            staffId,
            staffTeamIds);

        var getOrCreateWorkflowWebhookTriggerScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getOrCreateWorkflowWebhookTriggerInput).ToUrl("/Workflows/GetOrCreateWorkflowWebhookTrigger");
            });

        var getOrCreateWorkflowWebhookTriggerOutput = await getOrCreateWorkflowWebhookTriggerScenarioResult
            .ReadAsJsonAsync<Output<GetOrCreateWorkflowWebhookTrigger.GetOrCreateWorkflowWebhookTriggerOutput>>();

        return getOrCreateWorkflowWebhookTriggerOutput;
    }
}