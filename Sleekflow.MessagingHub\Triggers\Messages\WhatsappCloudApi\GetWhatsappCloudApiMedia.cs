using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Messages;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Messages.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class GetWhatsappCloudApiMedia
    : ITrigger<
        GetWhatsappCloudApiMedia.GetWhatsappCloudApiMediaInput,
        GetWhatsappCloudApiMedia.GetWhatsappCloudApiMediaOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IMessageService _messageService;
    private readonly ILogger<GetWhatsappCloudApiMedia> _logger;

    public GetWhatsappCloudApiMedia(
        IWabaService wabaService,
        IMessageService messageService,
        ILogger<GetWhatsappCloudApiMedia> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _messageService = messageService;
    }

    public class GetWhatsappCloudApiMediaInput
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("waba_phone_number_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string WabaPhoneNumberId { get; set; }

        [JsonProperty("media_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string MediaId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiMediaInput(string sleekflowCompanyId, string wabaPhoneNumberId, string mediaId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaPhoneNumberId = wabaPhoneNumberId;
            MediaId = mediaId;
        }
    }

    public class GetWhatsappCloudApiMediaOutput
    {
        [JsonProperty("blob_id")]
        public string BlobId { get; set; }

        [JsonProperty("blob_url")]
        public string BlobUrl { get; set; }

        [JsonProperty("expires_on")]
        public DateTimeOffset ExpiresOn { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiMediaOutput(string blobId, string blobUrl, DateTimeOffset expiresOn)
        {
            BlobId = blobId;
            BlobUrl = blobUrl;
            ExpiresOn = expiresOn;
        }
    }

    public async Task<GetWhatsappCloudApiMediaOutput> F(GetWhatsappCloudApiMediaInput getWhatsappCloudApiMediaInput)
    {
        var sleekflowCompanyId = getWhatsappCloudApiMediaInput.SleekflowCompanyId;
        var wabaPhoneNumberId = getWhatsappCloudApiMediaInput.WabaPhoneNumberId;

        var waba = await _wabaService.GetWabaWithWabaPhoneNumberIdAsync(sleekflowCompanyId, wabaPhoneNumberId);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var blob = await _messageService.GetWhatsappCloudApiMediaUrlAsync(
            getWhatsappCloudApiMediaInput.MediaId,
            hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null);
        return new GetWhatsappCloudApiMediaOutput(blob.BlobId, blob.Url, blob.ExpiresOn);
    }
}