﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Polly;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class CheckWorkflowBatchContactsEnrolmentEligibility
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;
    private readonly IServiceBusManager _serviceBusManager;
    private readonly ILogger<CheckWorkflowBatchContactsEnrolmentEligibility> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;

    public CheckWorkflowBatchContactsEnrolmentEligibility(
        IAppConfig appConfig,
        HttpClient httpClient,
        IServiceBusManager serviceBusManager,
        ILogger<CheckWorkflowBatchContactsEnrolmentEligibility> logger,
        IAsyncPolicy<HttpResponseMessage> retryPolicy)
    {
        _appConfig = appConfig;
        _httpClient = httpClient;
        _serviceBusManager = serviceBusManager;
        _logger = logger;
        _retryPolicy = retryPolicy;
    }

    public class CheckWorkflowBatchContactsEnrolmentEligibilityInput
    {
        [Required]
        [JsonProperty("contacts")]
        [Validations.ValidateObject]
        public Dictionary<string, ContactDetail> Contacts { get; set; }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonConstructor]
        public CheckWorkflowBatchContactsEnrolmentEligibilityInput(
            Dictionary<string, ContactDetail> contacts,
            string sleekflowCompanyId,
            string workflowVersionedId,
            string workflowId)
        {
            Contacts = contacts;
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowVersionedId = workflowVersionedId;
            WorkflowId = workflowId;
        }
    }

    [Function("CheckWorkflowBatchContactsEnrolmentEligibility")]
    public async Task RunAsync(
        [ActivityTrigger]
        CheckWorkflowBatchContactsEnrolmentEligibilityInput checkWorkflowBatchContactsEnrolmentEligibilityInput)
    {
        var pollyContext = new Context();
        pollyContext["logger"] = _logger; // Key "logger" must match what onRetryAsync expects

        foreach (var contact in checkWorkflowBatchContactsEnrolmentEligibilityInput.Contacts)
        {
            var workflowContactEnrolmentConditionCheckInput = new CheckWorkflowContactEnrolmentConditionInput(
                checkWorkflowBatchContactsEnrolmentEligibilityInput.WorkflowId,
                checkWorkflowBatchContactsEnrolmentEligibilityInput.WorkflowVersionedId,
                checkWorkflowBatchContactsEnrolmentEligibilityInput.SleekflowCompanyId,
                contact.Key,
                contact.Value,
                null,
                null);

            var inputJsonStr =
                JsonConvert.SerializeObject(
                    workflowContactEnrolmentConditionCheckInput,
                    JsonConfig.DefaultJsonSerializerSettings);

            var reqMsg = new HttpRequestMessage
            {
                Method = HttpMethod.Post,
                Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
                RequestUri = new Uri(_appConfig.FlowHubInternalsEndpoint + "/CheckWorkflowContactEnrolmentCondition"),
                Headers =
                {
                    {
                        "X-Sleekflow-Key", _appConfig.InternalsKey
                    }
                },
            };

            var resMsg = await _retryPolicy.ExecuteAsync(
                async (context) => await _httpClient.SendAsync(reqMsg),
                pollyContext);
            var resStr = await resMsg.Content.ReadAsStringAsync();

            var output = resStr.ToObject<Output<dynamic>>();

            if (output == null || output.Success == false)
            {
                _logger.LogError(
                    "WorkflowContactEnrolmentConditionCheck failed for contact {ContactId} with error: {Error}",
                    contact.Key,
                    JsonConvert.SerializeObject(output));

                continue;
            }

            var workflowContactEnrolmentConditionCheckOutput =
                ((JObject) output.Data).ToObject<CheckWorkflowContactEnrolmentConditionOutput>()!;

            if (workflowContactEnrolmentConditionCheckOutput.EnrolmentConditionSatisfied)
            {
                // Start the workflow enrolment process
                await _serviceBusManager.PublishAsync(
                    new OnTriggerEventRequestedEvent(
                        new OnScheduledWorkflowEnrollmentEventBody(
                            DateTimeOffset.UtcNow,
                            contact.Key,
                            checkWorkflowBatchContactsEnrolmentEligibilityInput.WorkflowId,
                            checkWorkflowBatchContactsEnrolmentEligibilityInput.WorkflowVersionedId,
                            contact.Value.Contact,
                            contact.Value.ContactOwner,
                            contact.Value.Lists,
                            contact.Value.Conversation),
                        contact.Key,
                        "Contact",
                        checkWorkflowBatchContactsEnrolmentEligibilityInput.SleekflowCompanyId));
            }
        }
    }
}