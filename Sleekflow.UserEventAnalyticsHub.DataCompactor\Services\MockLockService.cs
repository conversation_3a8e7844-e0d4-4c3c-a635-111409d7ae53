using Sleekflow.Locks;
using Microsoft.Extensions.Logging;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Services;

public class MockLockService : ILockService
{
    private readonly ILogger<MockLockService> _logger;

    public MockLockService(ILogger<MockLockService> logger)
    {
        _logger = logger;
    }

    public Task<(bool IsLocked, TimeSpan? ExpireIn)> IsLockedAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        var lockKey = string.Join(":", strings);
        _logger.LogInformation("Mock: Checking if locked '{LockKey}'", lockKey);
        return Task.FromResult((false, (TimeSpan?)null));
    }

    public Task<Lock?> LockAsync(string[] strings, TimeSpan minimumDuration, CancellationToken cancellationToken = default)
    {
        var lockKey = string.Join(":", strings);
        _logger.LogInformation("Mock: Acquiring lock '{LockKey}' for {Duration}", lockKey, minimumDuration);

        // Return a mock lock that always succeeds
        var mockLock = new Lock(lockKey, (int)minimumDuration.TotalSeconds);
        return Task.FromResult<Lock?>(mockLock);
    }

    public Task<Lock> WaitUnitLockAsync(string[] strings, TimeSpan minimumLockDuration, TimeSpan maximumWaitDuration, CancellationToken cancellationToken = default)
    {
        var lockKey = string.Join(":", strings);
        _logger.LogInformation("Mock: Wait unit lock '{LockKey}' for {Duration}", lockKey, minimumLockDuration);

        var mockLock = new Lock(lockKey, (int)minimumLockDuration.TotalSeconds);
        return Task.FromResult(mockLock);
    }

    public Task<int> ReleaseAsync(Lock @lock, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Mock: Releasing lock '{LockKey}'", @lock.Id);
        return Task.FromResult(1);
    }

    public Task AcquireReadLockAsync(string[] strings, TimeSpan minimumLockDuration, TimeSpan maximumWaitDuration, CancellationToken cancellationToken = default)
    {
        var lockKey = string.Join(":", strings);
        _logger.LogInformation("Mock: Acquiring read lock '{LockKey}'", lockKey);
        return Task.CompletedTask;
    }

    public Task AcquireWriteLockAsync(string[] strings, TimeSpan minimumLockDuration, TimeSpan maximumWaitDuration, CancellationToken cancellationToken = default)
    {
        var lockKey = string.Join(":", strings);
        _logger.LogInformation("Mock: Acquiring write lock '{LockKey}'", lockKey);
        return Task.CompletedTask;
    }

    public Task<bool> ReleaseReadLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        var lockKey = string.Join(":", strings);
        _logger.LogInformation("Mock: Releasing read lock '{LockKey}'", lockKey);
        return Task.FromResult(true);
    }

    public Task<bool> ReleaseWriteLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        var lockKey = string.Join(":", strings);
        _logger.LogInformation("Mock: Releasing write lock '{LockKey}'", lockKey);
        return Task.FromResult(true);
    }
}