using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetObjectDirectRefUrl : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public GetObjectDirectRefUrl(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class GetObjectDirectRefUrlInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonConstructor]
        public GetObjectDirectRefUrlInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string objectId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
        }
    }

    public class GetObjectDirectRefUrlOutput
    {
        [JsonProperty("object_direct_ref_url")]
        [Required]
        public string ObjectDirectRefUrl { get; set; }

        [JsonConstructor]
        public GetObjectDirectRefUrlOutput(
            string objectDirectRefUrl)
        {
            ObjectDirectRefUrl = objectDirectRefUrl;
        }
    }

    public async Task<GetObjectDirectRefUrlOutput> F(
        GetObjectDirectRefUrlInput getObjectDirectRefUrlInput)
    {
        var authentication =
            await _salesforceAuthenticationService.GetAsync(getObjectDirectRefUrlInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var objectDirectRefUrl =
            await _salesforceObjectService.GetObjectDirectRefUrlAsync(
                authentication,
                getObjectDirectRefUrlInput.EntityTypeName,
                getObjectDirectRefUrlInput.ObjectId);

        return new GetObjectDirectRefUrlOutput(objectDirectRefUrl);
    }
}