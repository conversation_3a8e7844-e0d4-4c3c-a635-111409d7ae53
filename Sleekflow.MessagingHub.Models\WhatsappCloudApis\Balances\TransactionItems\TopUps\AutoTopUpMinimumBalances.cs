using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

public static class AutoTopUpMinimumBalances
{
    private static readonly Money AutoTopUpMinimumBalance100 = new (CurrencyIsoCodes.USD, 100m);
    private static readonly Money AutoTopUpMinimumBalance50 = new (CurrencyIsoCodes.USD, 50m);
    private static readonly Money AutoTopUpMinimumBalance20 = new (CurrencyIsoCodes.USD, 20m);

    public static List<Money> MinimumBalances { get; } =
        new ()
        {
            AutoTopUpMinimumBalance100, AutoTopUpMinimumBalance50, AutoTopUpMinimumBalance20
        };
}