using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.EmailHub.Triggers.Providers;

[TriggerGroup(ControllerNames.Providers)]
public class SyncProviderEmail : ITrigger
{
    private readonly IEmailProviderSelector _providerSelector;

    public SyncProviderEmail(IEmailProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class SyncProviderEmailInput : IHasSleekflowCompanyId
    {
        [JsonConstructor]
        public SyncProviderEmailInput(
            string sleekflowCompanyId,
            string providerName,
            string emailAddress)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EmailAddress = emailAddress;
        }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email_address")]
        [Required]
        public string EmailAddress { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }
    }

    public class SyncProviderEmailOutput
    {
    }

    public async Task<SyncProviderEmailOutput> F(
        SyncProviderEmailInput syncProviderEmailInput)
    {
        var provider = _providerSelector.GetEmailProvider(syncProviderEmailInput.ProviderName);
        await provider.SyncEmailsAsync(syncProviderEmailInput.SleekflowCompanyId, syncProviderEmailInput.EmailAddress);
        return new SyncProviderEmailOutput();
    }
}