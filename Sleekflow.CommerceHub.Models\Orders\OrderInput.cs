using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Orders;

public abstract class OrderInput : IHasSleekflowCompanyId, IHasSleekflowUserProfileId, IHasMetadata
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty("user_profile")]
    public UserProfile UserProfile { get; set; }

    [Required]
    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [Required]
    [ValidateArray]
    [MaxLength(64)]
    [MinLength(1)]
    [JsonProperty("line_items")]
    public List<OrderLineItemInputDto> LineItems { get; set; }

    [ValidateObject]
    [JsonProperty("discount")]
    public Discount? Discount { get; set; }

    [Required]
    [JsonProperty("country_iso_code")]
    public string CountryIsoCode { get; set; }

    [Required]
    [JsonProperty("language_iso_code")]
    public string LanguageIsoCode { get; set; }

    [Required]
    [JsonProperty("currency_iso_code")]
    public string CurrencyIsoCode { get; set; }

    [Required]
    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    protected OrderInput(
        string sleekflowCompanyId,
        string sleekflowUserProfileId,
        UserProfile userProfile,
        string storeId,
        List<OrderLineItemInputDto> lineItems,
        Discount? discount,
        string countryIsoCode,
        string languageIsoCode,
        string currencyIsoCode,
        Dictionary<string, object?> metadata)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowUserProfileId = sleekflowUserProfileId;
        UserProfile = userProfile;
        StoreId = storeId;
        LineItems = lineItems;
        Discount = discount;
        CountryIsoCode = countryIsoCode;
        LanguageIsoCode = languageIsoCode;
        CurrencyIsoCode = currencyIsoCode;
        Metadata = metadata;
    }
}