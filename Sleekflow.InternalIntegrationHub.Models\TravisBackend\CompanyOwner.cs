using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.TravisBackend;

public class CompanyOwner
{
    [JsonProperty("display_name")]
    public string DisplayName { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("phone_number")]
    public string PhoneNumber { get; set; }

    [JsonConstructor]
    public CompanyOwner(string displayName, string email, string phoneNumber)
    {
        DisplayName = displayName;
        Email = email;
        PhoneNumber = phoneNumber;
    }
}