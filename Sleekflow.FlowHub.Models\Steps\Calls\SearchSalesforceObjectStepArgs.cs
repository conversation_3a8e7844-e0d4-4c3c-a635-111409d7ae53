﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SearchSalesforceObjectStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.search-salesforce-object";

    [Required]
    [JsonProperty("salesforce_connection_id__expr")]
    public string SalesforceConnectionIdExpr { get; set; }

    [Required]
    [JsonProperty("object_type__expr")]
    public string ObjectTypeExpr { get; set; }

    [Required]
    [JsonProperty("is_custom_object__expr")]
    public string IsCustomObjectExpr { get; set; }

    [Required]
    [JsonProperty("conditions__expr")]
    public string ConditionsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.SalesforceIntegration;

    [JsonConstructor]
    public SearchSalesforceObjectStepArgs(
        string salesforceConnectionIdExpr,
        string objectTypeExpr,
        string isCustomObjectExpr,
        string conditionsExpr)
    {
        SalesforceConnectionIdExpr = salesforceConnectionIdExpr;
        ObjectTypeExpr = objectTypeExpr;
        IsCustomObjectExpr = isCustomObjectExpr;
        ConditionsExpr = conditionsExpr;
    }
}