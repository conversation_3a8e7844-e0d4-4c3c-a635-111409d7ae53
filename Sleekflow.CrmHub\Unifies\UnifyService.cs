﻿using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Unifies;

public interface IUnifyService
{
    Task<Unify?> GetAsync(string sleekflowCompanyId, string typeName, CancellationToken cancellationToken = default);

    Task<int> UpsertAsync(Unify unify, CancellationToken cancellationToken = default);

    Task<List<UnifyRule>> GetUnifyRulesAsync(
        string sleekflowCompanyId,
        string typeName,
        CancellationToken cancellationToken = default);

    Task<List<FlattenedUnifyRule>> GetFlattenedUnifyRules(
        string sleekflowCompanyId,
        string typeName,
        CancellationToken cancellationToken = default);
}

public class UnifyService : ISingletonService, IUnifyService
{
    public const string UnifiedNamespaceName = "unified";

    private readonly IUnifyRepository _unifyRepository;

    public UnifyService(
        IUnifyRepository unifyRepository)
    {
        _unifyRepository = unifyRepository;
    }

    public async Task<Unify?> GetAsync(
        string sleekflowCompanyId,
        string typeName,
        CancellationToken cancellationToken = default)
    {
        var unifies =
            await _unifyRepository.GetObjectsAsync(
                unify => unify.SleekflowCompanyId == sleekflowCompanyId && unify.EntityTypeName == typeName,
                cancellationToken: cancellationToken);
        if (unifies.Count == 0)
        {
            return null;
        }

        return unifies[0];
    }

    public Task<int> UpsertAsync(
        Unify unify,
        CancellationToken cancellationToken = default)
    {
        return _unifyRepository.UpsertAsync(
            unify,
            unify.SleekflowCompanyId,
            cancellationToken: cancellationToken);
    }

    public async Task<List<UnifyRule>> GetUnifyRulesAsync(
        string sleekflowCompanyId,
        string typeName,
        CancellationToken cancellationToken = default)
    {
        var unifyRuleDefinition = await GetAsync(sleekflowCompanyId, typeName, cancellationToken);
        if (unifyRuleDefinition == null)
        {
            return new List<UnifyRule>();
        }

        return unifyRuleDefinition.UnifyRules;
    }

    public async Task<List<FlattenedUnifyRule>> GetFlattenedUnifyRules(
        string sleekflowCompanyId,
        string typeName,
        CancellationToken cancellationToken = default)
    {
        var unifyRuleDefinition = await GetAsync(sleekflowCompanyId, typeName, cancellationToken);
        if (unifyRuleDefinition == null)
        {
            return new List<FlattenedUnifyRule>();
        }

        return unifyRuleDefinition.FlattenedUnifyRules;
    }
}