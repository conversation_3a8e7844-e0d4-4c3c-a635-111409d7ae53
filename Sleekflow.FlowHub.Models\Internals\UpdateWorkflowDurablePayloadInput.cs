using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.DurablePayloads;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Internals;

public class UpdateWorkflowDurablePayloadInput : IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    [Required]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("durable_payload")]
    [Required]
    [Validations.ValidateObject]
    public DurablePayload DurablePayload { get; set; }

    [JsonConstructor]
    public UpdateWorkflowDurablePayloadInput(
        string sleekflowCompanyId,
        string workflowVersionedId,
        DurablePayload durablePayload)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionedId = workflowVersionedId;
        DurablePayload = durablePayload;
    }
}