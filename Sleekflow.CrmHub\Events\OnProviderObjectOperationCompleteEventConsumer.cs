using MassTransit;
using Sleekflow.CrmHub.AuditLogs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Events;

namespace Sleekflow.CrmHub.Events;

public class OnProviderObjectOperationCompleteEventConsumerDefinition
    : ConsumerDefinition<OnProviderObjectOperationCompleteEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnProviderObjectOperationCompleteEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(1);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnProviderObjectOperationCompleteEventConsumer
    : IConsumer<OnProviderObjectOperationCompleteEvent>
{
    private readonly IAuditLogService _auditLogService;

    public OnProviderObjectOperationCompleteEventConsumer(
        IAuditLogService auditLogService)
    {
        _auditLogService = auditLogService;
    }

    public async Task Consume(ConsumeContext<OnProviderObjectOperationCompleteEvent> context)
    {
        var @event = context.Message;
        var cancellationToken = context.CancellationToken;

        var dict = @event.Dict;
        var sleekflowCompanyId = @event.SleekflowCompanyId;
        var objectId = @event.CrmHubObjectId;
        var entityTypeName = @event.EntityTypeName;
        var operation = @event.ObjectOperation;
        var providerName = @event.ProviderName;

        await _auditLogService.CreateAuditLogAsync(
            sleekflowCompanyId,
            AuditLogTypes.PropagatedObjectUpdateToProvider,
            dict,
            entityTypeName,
            objectId,
            providerName,
            operation);
    }
}