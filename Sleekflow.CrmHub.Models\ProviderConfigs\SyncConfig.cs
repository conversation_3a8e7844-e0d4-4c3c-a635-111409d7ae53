﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;

namespace Sleekflow.CrmHub.Models.ProviderConfigs;

// TODO Validation
public sealed class SyncConfig : IEquatable<SyncConfig>
{
    [JsonProperty("filter_groups")]
    public List<SyncConfigFilterGroup> FilterGroups { get; set; }

    [JsonProperty("filters")]
    public List<SyncConfigFilter> Filters
    {
        set => FilterGroups =
            value.Count == 0
                ? new List<SyncConfigFilterGroup>()
                : value
                    .Select(
                        v => new SyncConfigFilterGroup(
                            new List<SyncConfigFilter>
                            {
                                v
                            }))
                    .ToList();
    }

    [JsonProperty("field_filters")]
    public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

    [JsonProperty("interval")]
    [Range(3600, 3600 * 24)]
    [Required]
    public int Interval { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("sync_mode")]
    public string? SyncMode { get; set; }

    [JsonConstructor]
    public SyncConfig(
        List<SyncConfigFieldFilter>? fieldFilters,
        int interval,
        string entityTypeName,
        List<SyncConfigFilterGroup> filterGroups,
        string? syncMode
    )
    {
        FieldFilters = fieldFilters;
        Interval = interval;
        EntityTypeName = entityTypeName;
        FilterGroups = filterGroups;
        SyncMode = syncMode;
    }

    public bool Equals(SyncConfig? other)
    {
        if (other == null)
        {
            return false;
        }

        if (FieldFilters != null && other.FieldFilters != null)
        {
            return FilterGroups.SequenceEqual(other.FilterGroups)
                   && Enumerable.SequenceEqual(FieldFilters, other.FieldFilters)
                   && Interval == other.Interval
                   && EntityTypeName == other.EntityTypeName
                   && SyncMode == other.SyncMode;
        }
        else if (FieldFilters != null && other.FieldFilters == null)
        {
            return false;
        }
        else if (FieldFilters == null && other.FieldFilters != null)
        {
            return false;
        }
        else if (FieldFilters == null && other.FieldFilters == null)
        {
            return FilterGroups.SequenceEqual(other.FilterGroups)
                   && Interval == other.Interval
                   && EntityTypeName == other.EntityTypeName
                   && SyncMode == other.SyncMode;
        }

        return false;
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(null, obj))
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        if (obj.GetType() != this.GetType())
        {
            return false;
        }

        return Equals((SyncConfig) obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(FilterGroups, FieldFilters, Interval, EntityTypeName, SyncMode);
    }
}