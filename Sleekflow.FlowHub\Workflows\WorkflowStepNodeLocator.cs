﻿using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowStepNodeLocator
{
    string? GetStepNodeId(ProxyWorkflow? workflow, string stepId);

    Dictionary<string, string> GetStepNodeIds(
        IReadOnlyList<Step>? steps,
        IReadOnlyDictionary<string, object?>? metadata);
}

public record WorkflowStepNodeEntry(Step Step, string? StepNodeId);

public class WorkflowStepNodeLocator : IWorkflowStepNodeLocator, ISingletonService
{
    public string? GetStepNodeId(ProxyWorkflow? workflow, string stepId)
    {
        if (workflow is null
            || string.IsNullOrWhiteSpace(stepId))
        {
            return null;
        }

        var stepNodeEntries = GetStepNodeIds(
            workflow.Steps,
            workflow.Metadata);

        return stepNodeEntries
            .FirstOrDefault(x => x.Key == stepId)
            .Value;
    }

    public Dictionary<string, string> GetStepNodeIds(
        IReadOnlyList<Step>? steps,
        IReadOnlyDictionary<string, object?>? metadata)
    {
        if (metadata is not { Count: > 0 }
            || steps is not { Count: > 0 })
        {
            return new Dictionary<string, string>();
        }

        var metadataRawValue = Convert.ToString(metadata.First().Value) ?? string.Empty;
        var workflowMetadata = JsonConvert.DeserializeObject<WorkflowMetadata>(metadataRawValue);

        if (workflowMetadata is null)
        {
            return new Dictionary<string, string>();
        }

        var nodeIds = workflowMetadata.Nodes
            .Where(x => x.IsNode)
            .Select(x => x.Id)
            .ToHashSet();

        var stepNodeIdEntries = new List<WorkflowStepNodeEntry>();

        foreach (var step in steps)
        {
            stepNodeIdEntries.AddRange(
                GetStepNodeEntries(
                    new WorkflowStepNodeEntry(step, null),
                    nodeIds));
        }

        return stepNodeIdEntries
            .Where(x => !string.IsNullOrWhiteSpace(x.StepNodeId))
            .ToDictionary(
                x => x.Step.Id,
                x => x.StepNodeId!);
    }

    private static IEnumerable<WorkflowStepNodeEntry> GetStepNodeEntries(
        WorkflowStepNodeEntry workflowStepNodeEntry,
        IReadOnlySet<string> actionNodeIds)
    {
        var step = workflowStepNodeEntry.Step;
        var stepNodeId = actionNodeIds.Contains(workflowStepNodeEntry.Step.Id)
            ? workflowStepNodeEntry.Step.Id
            : workflowStepNodeEntry.StepNodeId;
        return step switch
        {
            LogStep logStep => new List<WorkflowStepNodeEntry>
            {
                new (logStep, stepNodeId)
            },
            ParallelStep parallelStep =>
                new List<WorkflowStepNodeEntry>
                    {
                        new (parallelStep, stepNodeId)
                    }
                    .Concat(
                        parallelStep.ParallelBranches
                            .SelectMany(
                                (b, i) =>
                                    GetStepNodeEntries(new WorkflowStepNodeEntry(b.Step, stepNodeId), actionNodeIds))
                            .ToList()),
            SimpleStep simpleStep => new List<WorkflowStepNodeEntry>
            {
                new (simpleStep, stepNodeId)
            },
            SubFlowStep subFlowStep =>
                new List<WorkflowStepNodeEntry>
                    {
                        new (subFlowStep, stepNodeId)
                    }
                    .Concat(
                        subFlowStep.Substeps
                            .SelectMany(
                                s => GetStepNodeEntries(new WorkflowStepNodeEntry(s, stepNodeId), actionNodeIds))
                            .ToList()),
            SwitchStep switchStep => new List<WorkflowStepNodeEntry>
            {
                new (switchStep, stepNodeId)
            },
            ThrowStep throwStep => new List<WorkflowStepNodeEntry>
            {
                new (throwStep, stepNodeId)
            },
            TryCatchStep tryCatchStep =>
                new List<WorkflowStepNodeEntry>
                    {
                        new (tryCatchStep, stepNodeId)
                    }
                    .Concat(
                        GetStepNodeEntries(
                            new WorkflowStepNodeEntry(tryCatchStep.Try.Step, stepNodeId),
                            actionNodeIds))
                    .Concat(
                        GetStepNodeEntries(
                            new WorkflowStepNodeEntry(tryCatchStep.Catch.Step, stepNodeId),
                            actionNodeIds)),
            // CallStep
            object callStep when step.GetType().IsGenericType
                                 && step.GetType().GetGenericTypeDefinition() == typeof(CallStep<>) =>
                new List<WorkflowStepNodeEntry>
                {
                    new ((Step) callStep, stepNodeId)
                },
            _ => throw new ArgumentOutOfRangeException(nameof(workflowStepNodeEntry))
        };
    }
}