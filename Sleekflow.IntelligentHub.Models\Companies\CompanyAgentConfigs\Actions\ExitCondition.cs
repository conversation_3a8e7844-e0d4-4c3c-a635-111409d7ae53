using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class ExitCondition
{
    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonProperty("operator")]
    public string? Operator { get; set; }

    [JsonProperty("values")]
    public List<object>? Values { get; set; }

    [JsonConstructor]
    public ExitCondition(
        string title,
        string type,
        string description,
        string? @operator = null,
        List<object>? values = null)
    {
        Title = title;
        Type = type;
        Description = description;
        Operator = @operator;
        Values = values;
    }

    public ExitCondition(ExitConditionDto dto)
    {
        Title = dto.Title;
        Type = dto.Type;
        Description = dto.Description;
        Operator = dto.Operator;
        Values = dto.Values;
    }

    public ExitConditionDto ToDto()
    {
        return new ExitConditionDto(this);
    }
}