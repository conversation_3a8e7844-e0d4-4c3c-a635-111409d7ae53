using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IMessagingHubDbResolver))]
[ContainerId(ContainerNames.BusinessBalanceTransactionLog)]
public class TransactionLogCalculatedBusinessBalance
{
    [JsonProperty("total")]
    public int Total { get; set; }

    [JsonProperty("credit")]
    public decimal Credit { get; set; }

    [JsonProperty("used")]
    public decimal Used { get; set; }

    [JsonProperty("markup")]
    public decimal Markup { get; set; }

    [JsonProperty("transaction_handling_fee")]
    public decimal TransactionHandlingFee { get; set; }

    [JsonProperty("business_initiated_cost")]
    public decimal BusinessInitiatedCost { get; set; }

    [JsonProperty("user_initiated_cost")]
    public decimal UserInitiatedCost { get; set; }

    [JsonProperty("business_initiated_paid_quantity")]
    public decimal BusinessInitiatedPaidQuantity { get; set; }

    [JsonProperty("business_initiated_free_tier_quantity")]
    public decimal BusinessInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("user_initiated_paid_quantity")]
    public decimal UserInitiatedPaidQuantity { get; set; }

    [JsonProperty("user_initiated_free_tier_quantity")]
    public decimal UserInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("user_initiated_free_entry_point_quantity")]
    public decimal UserInitiatedFreeEntryPointQuantity { get; set; }

    [JsonConstructor]
    public TransactionLogCalculatedBusinessBalance(
        int total,
        decimal used,
        decimal markup,
        decimal transactionHandlingFee,
        decimal businessInitiatedCost,
        decimal userInitiatedCost,
        decimal businessInitiatedPaidQuantity,
        decimal businessInitiatedFreeTierQuantity,
        decimal userInitiatedPaidQuantity,
        decimal userInitiatedFreeTierQuantity,
        decimal userInitiatedFreeEntryPointQuantity)
    {
        Total = total;
        Used = used;
        Markup = markup;
        TransactionHandlingFee = transactionHandlingFee;
        BusinessInitiatedCost = businessInitiatedCost;
        UserInitiatedCost = userInitiatedCost;
        BusinessInitiatedPaidQuantity = businessInitiatedPaidQuantity;
        BusinessInitiatedFreeTierQuantity = businessInitiatedFreeTierQuantity;
        UserInitiatedPaidQuantity = userInitiatedPaidQuantity;
        UserInitiatedFreeTierQuantity = userInitiatedFreeTierQuantity;
        UserInitiatedFreeEntryPointQuantity = userInitiatedFreeEntryPointQuantity;
    }
}