using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Moq;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Sleekflow.Mvc.Tests;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.Ingestion;

[TestFixture]
[TestOf(typeof(WordKnowledgeSourceTest))]
public class WordKnowledgeSourceTest
{
    // Relative path from the test execution directory to the Binaries folder
    const string WordFilePath = "../../../Binaries/OT&P - Flu Shot Summary 2024 for Sleekflow.docx";

    [Test]
    public async Task WordKnowledgeSourceIngestTest()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test takes too long in git action");
        }

        using var scope = Application.Host.Services.CreateScope();
        var kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        var azureFormRecognizerConfig = scope.ServiceProvider.GetRequiredService<IAzureFormRecognizerConfig>();
        var logger = new Mock<ILogger<WordKnowledgeSource>>().Object;

        var wordKnowledgeSource = new WordKnowledgeSource(
            logger,
            kernel,
            azureFormRecognizerConfig);

        var allMarkdowns = new List<string>();
        IFileIngestionProgress? progress = null;

        do
        {
            await using var fileStream = new FileStream(
                WordFilePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read);

            var (markdowns, updatedProgress) = await wordKnowledgeSource.Ingest(fileStream, progress);

            // Add new markdowns to our collection
            allMarkdowns.AddRange(markdowns);

            // Update progress for next iteration
            progress = updatedProgress;

            // Continue until document is fully processed
        }
        while (!progress.IsCompleted());

        Assert.That(allMarkdowns, Is.Not.Empty);
        Assert.That(progress.IsCompleted(), Is.True);
        Assert.That(progress.GetProgressPercentage(), Is.EqualTo(100.0));
    }
}