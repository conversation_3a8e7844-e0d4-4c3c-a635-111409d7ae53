using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Plugins.Models;

public class DetermineSearchResultQualityResponse
{
    [JsonProperty("relevance_score")]
    public int RelevanceScore { get; set; }

    [JsonProperty("relevance_score_comment")]
    public string RelevanceScoreComment { get; set; }

    [JsonProperty("optimized_lucene_search_query")]
    public string OptimizedLuceneSearchQuery { get; set; }

    [Json<PERSON>roperty("optimized_lucene_search_query_comment")]
    public string OptimizedLuceneSearchQueryComment { get; set; }

    [JsonConstructor]
    public DetermineSearchResultQualityResponse(
        int relevanceScore,
        string relevanceScoreComment,
        string optimizedLuceneSearchQuery,
        string optimizedLuceneSearchQueryComment)
    {
        RelevanceScoreComment = relevanceScoreComment;
        OptimizedLuceneSearchQueryComment = optimizedLuceneSearchQueryComment;
        RelevanceScore = relevanceScore;
        OptimizedLuceneSearchQuery = optimizedLuceneSearchQuery;
    }
}