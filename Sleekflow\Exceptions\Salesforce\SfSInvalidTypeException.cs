﻿namespace Sleekflow.Exceptions.Salesforce;

public class SfSInvalidTypeException : ErrorCodeException
{
    public SfSInvalidTypeException(string type)
        : base(
            ErrorCodeConstant.SfSInvalidTypeException,
            $"Invalid Salesforce type {type}",
            new Dictionary<string, object?>
            {
                {
                    "type", type
                }
            })
    {
    }
}