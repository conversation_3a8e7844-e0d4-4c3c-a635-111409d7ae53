﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class StartWebScraperTask : ITrigger<StartWebScraperTask.StartWebScraperTaskInput, StartWebScraperTask.StartWebScraperTaskOutput>
{
    private readonly IWebScraperService _webScraperService;

    public StartWebScraperTask(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class StartWebScraperTaskInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameApifyTaskId)]
        public string ApifyTaskId { get; set; }

        [JsonConstructor]
        public StartWebScraperTaskInput(string sleekflowCompanyId, string apifyTaskId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyTaskId = apifyTaskId;
        }
    }

    public class StartWebScraperTaskOutput
    {
        [JsonProperty(WebScraperRun.PropertyNameWebScraperRun)]
        public WebScraperRun WebScraperRun { get; set; }

        [JsonConstructor]
        public StartWebScraperTaskOutput(WebScraperRun webScraperRun)
        {
            WebScraperRun = webScraperRun;
        }
    }

    public async Task<StartWebScraperTaskOutput> F(StartWebScraperTaskInput startWebScraperTaskInput)
    {
        var run = await _webScraperService.StartWebScraperTaskAsync(
            startWebScraperTaskInput.SleekflowCompanyId,
            startWebScraperTaskInput.ApifyTaskId);

        return new StartWebScraperTaskOutput(run);
    }
}