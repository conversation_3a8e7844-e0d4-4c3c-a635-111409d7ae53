using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Payments;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;
using Stripe.Checkout;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLink
    : ITrigger<
        GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLink.
        GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput,
        GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLink.
        GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IStripeConfig _stripeConfig;
    private readonly IStripeClient _stripeClient;
    private readonly IBusinessBalanceService _businessBalanceService;

    public GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLink(
        IWabaService wabaService,
        IStripeConfig stripeConfig,
        IStripeClient stripeClient,
        IBusinessBalanceService businessBalanceService)
    {
        _wabaService = wabaService;
        _stripeConfig = stripeConfig;
        _stripeClient = stripeClient;
        _businessBalanceService = businessBalanceService;
    }

    public class GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("top_up_plan_id")]
        public string TopUpPlanId { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("facebook_waba_id")]
        public string? FacebookWabaId { get; set; }

        [JsonProperty("stripe_customer_id")]
        public string? StripeCustomerId { get; set; }

        [Required]
        [JsonProperty("credited_by")]
        public string CreditedBy { get; set; }

        [JsonProperty("credited_by_display_name")]
        public string? CreditedByDisplayName { get; set; }

        // Since it is a new param, it will be moment the param not pass correctly at first,
        // so we make it a optional field
        [JsonProperty("redirect_to_url")]
        public string? RedirectToUrl { get; set; }

        [JsonConstructor]
        public GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput(
            string sleekflowCompanyId,
            string topUpPlanId,
            string facebookBusinessId,
            string? facebookWabaId,
            string? stripeCustomerId,
            string creditBy,
            string? creditByDisplayName,
            string? redirectToUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            TopUpPlanId = topUpPlanId;
            FacebookBusinessId = facebookBusinessId;
            FacebookWabaId = facebookWabaId;
            StripeCustomerId = stripeCustomerId;
            CreditedBy = creditBy;
            CreditedByDisplayName = creditByDisplayName;
            RedirectToUrl = redirectToUrl;
        }
    }

    public class GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput
    {
        [JsonProperty("payment_url")]
        public string PaymentUrl { get; set; }

        [JsonConstructor]
        public GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput(string paymentUrl)
        {
            PaymentUrl = paymentUrl;
        }
    }

    public async Task<GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput> F(
        GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput request)
    {
        var topUpPlan = StripeWhatsAppCreditTopUpPlans.TopUpPlans.Find(x => x.Id == request.TopUpPlanId);

        var validTopUpPlanCount = StripeWhatsAppCreditTopUpPlans.TopUpPlans
            .Count(x => x.Id == request.TopUpPlanId);

        if (validTopUpPlanCount != 1)
        {
            throw new SfNotFoundObjectException($"Unable to find top-up planId {request.TopUpPlanId}");
        }

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(request.FacebookBusinessId);

        if (wabas.Count == 0)
        {
            throw new SfNotFoundObjectException(request.FacebookBusinessId);
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(request.FacebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(request.FacebookBusinessId);
        }

        var lineItem = new List<SessionLineItemOptions>
        {
            new ()
            {
                Price = request.TopUpPlanId, Quantity = 1
            }
        };

        // In order to prevent the case of missing redirect to url since it is nullable
        // I will place the _stripeConfig.PaymentGatewayRedirectUrl to replace it
        request.RedirectToUrl ??= _stripeConfig.PaymentGatewayRedirectUrl;
        var successUrl = BuildManualTopUpStripeRedirectToUrl(request.RedirectToUrl, "success", request.TopUpPlanId);
        var cancelUrl = BuildManualTopUpStripeRedirectToUrl(request.RedirectToUrl, "cancel", request.TopUpPlanId);

        // Pass BusinessBalance Id and FacebookBusinessId to metadata
        var metadata = new Dictionary<string, string>
        {
            {
                "source", "messaging-hub"
            },
            {
                "sleekflow_company_id", request.SleekflowCompanyId
            },
            {
                "facebook_business_id", request.FacebookBusinessId
            },
            {
                "business_balance_id", businessBalance.Id
            },
            {
                "type", TopUpTypes.WhatsappCloudApiManualTopUp
            },
            {
                "credited_by", request.CreditedBy
            },
            {
                "credited_by_display_name", request.CreditedByDisplayName
            },
            {
                "whatsapp_cloud_api_top_up_plan_id", request.TopUpPlanId
            },
            {
                "whatsapp_cloud_api_top_up_plan_amount", topUpPlan?.Price.Amount.ToString(CultureInfo.InvariantCulture)!
            },
            {
                "whatsapp_cloud_api_top_up_plan_currency", topUpPlan?.Price.CurrencyIsoCode!
            }
        };

        if (businessBalance.IsByWabaBillingEnabled == true && !string.IsNullOrEmpty(request.FacebookWabaId))
        {
            metadata.Add("facebook_waba_id", request.FacebookWabaId!);
            metadata.Add("is_by_waba_billing_enabled", "true");
        }

        var stripeSession = await _stripeClient.GeneratePaymentLink(
            request.StripeCustomerId,
            successUrl,
            cancelUrl,
            lineItem,
            metadata);

        return new GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput(stripeSession.Url);
    }

    private static string BuildManualTopUpStripeRedirectToUrl(
        string baseUrl,
        string action,
        string topUpId)
    {
        var builder = new UriBuilder(baseUrl);

        // If baseUrl contain origin only, will provide a default path.
        if(builder.Path == "/")
        {
            builder.Path = $"/stripe/whatsapp/topup/{action}";
        }

        var queryParams = new List<string>
        {
            $"topupId={topUpId}",
            $"status={action}"
        };

        builder.Query = builder.Query == string.Empty
                ? string.Join("&", queryParams)
                : $"{builder.Query}&{string.Join("&", queryParams)}";

        return builder.ToString();
    }
}