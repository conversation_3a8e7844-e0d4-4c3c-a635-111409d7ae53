using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Subscriptions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitTypeSync : ITrigger
{
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotSubscriptionService _hubspotSubscriptionService;

    public InitTypeSync(
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotSubscriptionService hubspotSubscriptionService)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotSubscriptionService = hubspotSubscriptionService;
    }

    public class InitTypeSyncInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("sync_config")]
        public SyncConfig? SyncConfig { get; set; }

        [JsonConstructor]
        public InitTypeSyncInput(string sleekflowCompanyId, string entityTypeName, SyncConfig? syncConfig)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            SyncConfig = syncConfig;
        }
    }

    public class InitTypeSyncOutput
    {
    }

    public async Task<InitTypeSyncOutput> F(
        InitTypeSyncInput initTypeSyncInput)
    {
        var authentication = await _hubspotAuthenticationService.GetAsync(initTypeSyncInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        if (initTypeSyncInput.SyncConfig == null)
        {
            await _hubspotSubscriptionService.ClearAsync(
                initTypeSyncInput.EntityTypeName,
                initTypeSyncInput.SleekflowCompanyId);

            return new InitTypeSyncOutput();
        }

        if (initTypeSyncInput.SyncConfig.FieldFilters != null)
        {
            var necessaryNames = new List<string>()
            {
                "hs_object_id",
                "lastmodifieddate",
                "createdate",
                "mobilephone",
                "phone",
                "work_email",
                "email"
            };

            var missingNames = necessaryNames
                .Where(n => initTypeSyncInput.SyncConfig.FieldFilters.Select(ff => ff.Name).Contains(n) == false)
                .ToList();
            if (missingNames.Any())
            {
                throw new SfMissingNecessaryFieldFiltersException(missingNames);
            }
        }

        // This is a special handling of the default value
        // By default, the frontend app sends 7200 to us
        if (initTypeSyncInput.SyncConfig.Interval == 7200)
        {
            await _hubspotSubscriptionService.UpsertAsync(
                initTypeSyncInput.EntityTypeName,
                initTypeSyncInput.SleekflowCompanyId,
                60 * 10);
        }
        else
        {
            await _hubspotSubscriptionService.UpsertAsync(
                initTypeSyncInput.EntityTypeName,
                initTypeSyncInput.SleekflowCompanyId,
                initTypeSyncInput.SyncConfig.Interval);
        }

        return new InitTypeSyncOutput();
    }
}