using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class PreviewObjectsOutput
{
    [JsonProperty("objects")]
    [Required]
    public List<Dictionary<string, object?>> Objects { get; set; }

    [JsonConstructor]
    public PreviewObjectsOutput(
        List<Dictionary<string, object?>> objects)
    {
        Objects = objects;
    }
}