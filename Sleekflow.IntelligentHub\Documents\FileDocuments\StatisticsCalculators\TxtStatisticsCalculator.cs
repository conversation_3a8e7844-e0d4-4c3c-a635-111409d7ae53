using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;

public class TxtStatisticsCalculator : IDocumentStatisticsCalculator
{
    private readonly IDocumentCounterService _documentCounterService;

    public TxtStatisticsCalculator(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public DocumentStatistics CalculateDocumentStatistics(Stream stream)
    {
        var reader = new StreamReader(stream);
        var content = reader.ReadToEnd();
        var totalTokens = _documentCounterService.CountTokens(content);
        var totalWords = _documentCounterService.CountWords(content);
        var totalCharacters = _documentCounterService.CountCharacters(content);
        stream.Position = 0;
        return new DocumentStatistics(totalTokens, totalWords, totalCharacters, 0, (int) stream.Length);
    }
}