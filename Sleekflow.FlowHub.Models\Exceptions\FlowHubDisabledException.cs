﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Exceptions;

public class FlowHubDisabledException : Exception
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonConstructor]
    public FlowHubDisabledException(
        string sleekflowCompanyId,
        string stateId)
        : base($"FlowHub is disabled for company {sleekflowCompanyId}. (State Id: {stateId})")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
    }
}