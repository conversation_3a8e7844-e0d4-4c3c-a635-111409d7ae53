<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <RootNamespace>Sleekflow.Auth0.BulkImporter.Tests</RootNamespace>
        <AssemblyName>Sleekflow.Auth0.BulkImporter.Tests</AssemblyName>
        <TargetFramework>net8.0</TargetFramework>
    </PropertyGroup>
    <ItemGroup>
        <PackageReference Include="Bogus" Version="35.6.1" />
        <PackageReference Include="Bogus.Tools.Analyzer" Version="35.6.1">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
        <PackageReference Include="NUnit" Version="3.14.0" />
        <PackageReference Include="coverlet.collector" Version="6.0.4">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="NUnit.Analyzers" Version="4.6.0">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NUnit3TestAdapter" Version="5.0.0" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.Auth0.BulkImporter\Sleekflow.Auth0.BulkImporter.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc.Tests\Sleekflow.Mvc.Tests.csproj" />
    </ItemGroup>
</Project>