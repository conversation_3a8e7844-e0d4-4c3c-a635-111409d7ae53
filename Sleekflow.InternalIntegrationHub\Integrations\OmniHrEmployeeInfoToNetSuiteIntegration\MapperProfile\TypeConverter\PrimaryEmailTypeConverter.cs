using AutoMapper;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

namespace Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile.TypeConverter;

public class PrimaryEmailTypeConverter : ITypeConverter<string, OmniHrEmail>
{
    public OmniHrEmail Convert(string source, OmniHrEmail destination, ResolutionContext context)
    {
        return new OmniHrEmail(value: source);
    }
}