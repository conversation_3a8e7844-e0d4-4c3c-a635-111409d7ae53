﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class GetIntelligentHubConfig : ITrigger<GetIntelligentHubConfig.GetIntelligentHubConfigInput, GetIntelligentHubConfig.GetIntelligentHubConfigOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public GetIntelligentHubConfig(IIntelligentHubConfigService intelligentHubConfigService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public class GetIntelligentHubConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetIntelligentHubConfigInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetIntelligentHubConfigOutput
    {
        [JsonProperty("intelligent_hub_config")]
        public IntelligentHubConfig? IntelligentHubConfig { get; set; }

        [JsonConstructor]
        public GetIntelligentHubConfigOutput(IntelligentHubConfig? intelligentHubConfig)
        {
            IntelligentHubConfig = intelligentHubConfig;
        }
    }

    public async Task<GetIntelligentHubConfigOutput> F(GetIntelligentHubConfigInput getIntelligentHubConfigInput)
    {
        var intelligentHubConfig = await _intelligentHubConfigService.GetIntelligentHubConfigAsync(
            getIntelligentHubConfigInput.SleekflowCompanyId);

        return new GetIntelligentHubConfigOutput(intelligentHubConfig);
    }
}