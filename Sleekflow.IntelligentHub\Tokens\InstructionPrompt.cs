using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Tokens;

public class InstructionPrompt : Prompt
{
    private readonly string _instruction;

    public InstructionPrompt(string instruction)
    {
        _instruction = instruction;
    }

    public override
        (List<SfChatEntry> ChatEntries, int ConsumedNumOfTokens)
        GetChatEntries(
            int maxNumOfTokens,
            ITokenService tokenService)
    {
        var remainingNumOfTokens = maxNumOfTokens - GetMinimumNumOfTokens(tokenService);

        return (
            new List<SfChatEntry>
            {
                new SfChatEntry
                {
                    Sys = _instruction
                }
            },
            maxNumOfTokens - remainingNumOfTokens);
    }

    public override int GetMinimumNumOfTokens(ITokenService tokenService)
    {
        return tokenService.Count(_instruction);
    }
}