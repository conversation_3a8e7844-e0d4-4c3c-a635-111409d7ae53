using Newtonsoft.Json;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Models.Languages;

public class Language
{
    public const string PropertyNameLanguageIsoCode = "language_iso_code";
    public const string PropertyNameLanguageName = "language_name";
    public const string PropertyNameNativeLanguageName = "native_language_name";
    public const string PropertyNameIsDefault = "is_default";

    [JsonProperty(PropertyNameLanguageIsoCode)]
    public string LanguageIsoCode { get; set; }

    [JsonProperty(PropertyNameLanguageName)]
    public string LanguageName { get; set; }

    [JsonProperty(PropertyNameNativeLanguageName)]
    public string NativeLanguageName { get; set; }

    [JsonProperty(PropertyNameIsDefault)]
    public bool IsDefault { get; set; }

    [JsonConstructor]
    public Language(
        string languageIsoCode,
        string languageName,
        string nativeLanguageName,
        bool isDefault)
    {
        LanguageIsoCode = languageIsoCode;
        LanguageName = languageName;
        NativeLanguageName = nativeLanguageName;
        IsDefault = isDefault;
    }

    public static Language DefaultLanguage()
    {
        var cultureInfo = CultureUtils.GetCultureInfoByLanguageIsoCode("en")!;

        return new Language("en", cultureInfo.EnglishName, cultureInfo.NativeName, true);
    }
}