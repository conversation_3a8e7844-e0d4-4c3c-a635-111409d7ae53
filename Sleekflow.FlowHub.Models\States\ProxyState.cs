using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.States;

public class ProxyState : BaseProxyState, IHasETag
{
    [JsonProperty("trigger_event_body", TypeNameHandling = TypeNameHandling.Objects)]
    public EventBody TriggerEventBody { get; set; }

    [JsonProperty("origin")]
    public string? Origin { get; set; }

    [JsonProperty("usr_var_dict")]
    public AsyncDictionaryWrapper<string, object?> UsrVarDict { get; set; }

    [JsonProperty("sys_var_dict")]
    public AsyncDictionaryWrapper<string, object?> SysVarDict { get; set; }

    [JsonProperty("sys_company_var_dict")]
    public AsyncDictionaryWrapper<string, object?> SysCompanyVarDict { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty("state_status")]
    public string StateStatus { get; set; }

    [JsonProperty(StateFieldNames.StateReasonCode)]
    public string? StateReasonCode { get; set; }

    [JsonProperty("is_reenrollment")]
    public bool IsReenrolllment { get; set; }

    [JsonConstructor]
    public ProxyState(
        string id,
        StateIdentity identity,
        ProxyStateWorkflowContext workflowContext,
        EventBody triggerEventBody,
        string? origin,
        AsyncDictionaryWrapper<string, object?> usrVarDict,
        AsyncDictionaryWrapper<string, object?> sysVarDict,
        string? eTag,
        AsyncDictionaryWrapper<string, object?> sysCompanyVarDict,
        string stateStatus,
        string? stateReasonCode,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        bool isReenrolllment)
        : base(id, identity, workflowContext, createdAt, updatedAt)
    {
        TriggerEventBody = triggerEventBody;
        Origin = origin;
        UsrVarDict = usrVarDict;
        SysVarDict = sysVarDict;
        ETag = eTag;
        SysCompanyVarDict = sysCompanyVarDict;
        StateStatus = stateStatus;
        StateReasonCode = stateReasonCode;
        IsReenrolllment = isReenrolllment;
    }

    public ProxyState(
        State state,
        ProxyWorkflow workflow,
        AsyncDictionaryWrapper<string, object?> usrVarDict,
        AsyncDictionaryWrapper<string, object?> sysVarDict,
        string? eTag,
        AsyncDictionaryWrapper<string, object?> sysCompanyVarDict,
        string stateStatus)
        : base(state.Id, state.Identity, new(workflow), state.CreatedAt, state.UpdatedAt)
    {
        TriggerEventBody = state.TriggerEventBody;
        Origin = state.Origin;
        UsrVarDict = usrVarDict;
        SysVarDict = sysVarDict;
        ETag = eTag;
        SysCompanyVarDict = sysCompanyVarDict;
        StateStatus = stateStatus;
        StateReasonCode = state.StateReasonCode;
        IsReenrolllment = state.IsReenrolllment;
    }
}