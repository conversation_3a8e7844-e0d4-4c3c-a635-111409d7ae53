﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Workers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Workers.Triggers.CustomObject;

public class LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestrator : ITrigger
{
    public class LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorCustomStatusOutput(long count, DateTime lastUpdateTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
        }
    }

    public class LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("LoopThroughAndEnrollSchemafulObjectsToFlowHub_Orchestrator")]
    public async Task<LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger] TaskOrchestrationContext context)
    {
        var loopThroughAndEnrollSchemafulObjectsToFlowHubInput = context
            .GetInput<LoopThroughAndEnrollSchemafulObjectsToFlowHubInput>();

        context.SetCustomStatus(
            new LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorCustomStatusOutput(
                0,
                context.CurrentUtcDateTime));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        var totalCount = 0L;
        var nextContinuationToken = null as string;
        while (true)
        {
            var loopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput =
                await context
                    .CallActivityAsync<LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput>(
                        "LoopThroughAndEnrollSchemafulObjectsToFlowHub_Batch",
                        new LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput(
                                loopThroughAndEnrollSchemafulObjectsToFlowHubInput.SleekflowCompanyId,
                                loopThroughAndEnrollSchemafulObjectsToFlowHubInput.SchemaId,
                                nextContinuationToken,
                                loopThroughAndEnrollSchemafulObjectsToFlowHubInput.FlowHubWorkflowId,
                                loopThroughAndEnrollSchemafulObjectsToFlowHubInput.FlowHubWorkflowVersionedId),
                        taskOptions);

            totalCount += loopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput.Count;
            nextContinuationToken = loopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput.NextContinuationToken;

            context.SetCustomStatus(
                new LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorCustomStatusOutput(
                    totalCount,
                    context.CurrentUtcDateTime));

            if (loopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput.Count == 0 ||
                string.IsNullOrEmpty(nextContinuationToken))
            {
                break;
            }

            await context.CreateTimer(context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)), CancellationToken.None);
        }

        return new LoopThroughAndEnrollSchemafulObjectsToFlowHubOrchestratorOutput(totalCount);
    }
}