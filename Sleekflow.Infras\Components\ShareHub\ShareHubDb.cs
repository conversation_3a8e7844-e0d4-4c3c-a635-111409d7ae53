using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.ShareHub;

public class ShareHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public ShareHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class ShareHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public ShareHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public ShareHubDbOutput InitShareHubDb()
    {
        const string cosmosDbId = "sharehubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 10000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.ShareHubDb.IShareHubDbService
        var containerParams = new ContainerParam[]
        {
            new (
                "link",
                "link",
                new List<string>
                {
                    "/id"
                }
            ),
            new (
                "link_click",
                "link_click",
                new List<string>
                {
                    "/id"
                },
                IsAnalyticalStorageEnabled: true
            ),
            new (
                "domain",
                "domain",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new ShareHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}