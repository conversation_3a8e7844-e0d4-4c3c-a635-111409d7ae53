﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class ExternalResource
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("resource_type")]
    public string ResourceType { get; set; }

    [JsonProperty("sub_resources")]
    public List<ExternalResource>? SubResources { get; set; }

    public ExternalResource(
        string id,
        string name,
        string resourceType,
        List<ExternalResource>? subResources)
    {
        Id = id;
        Name = name;
        ResourceType = resourceType;
        SubResources = subResources;
    }
}