using Alba;
using Azure.Storage.Blobs.Models;
using Azure.Storage.Blobs.Specialized;
using Sleekflow.CommerceHub.Models.Blobs;
using Sleekflow.CommerceHub.Triggers.Blobs;
using Sleekflow.CommerceHub.Triggers.Categories;
using Sleekflow.CommerceHub.Triggers.CustomCatalogs;
using Sleekflow.CommerceHub.Triggers.Products;
using Sleekflow.CommerceHub.Triggers.Stores;
using Sleekflow.Outputs;

namespace Sleekflow.CommerceHub.Tests;

public class CustomCatalogCsvIntegrationTests
{
    private CreateStore.CreateStoreOutput? _createStoreOutput;
    private CreateCategory.CreateCategoryOutput? _createCategoryOutput;
    private CreateProduct.CreateProductOutput? _createProductOutput;

    [SetUp]
    public async Task Setup()
    {
        var createStoreOutputOutput = await Mocks.CreateTestStoreAsync();
        var createCategoryOutputOutput = await Mocks.CreateTestCategoryAsync(createStoreOutputOutput!.Data);
        var createProductOutputOutput = await Mocks.CreateTestProductAsync(
            createStoreOutputOutput.Data,
            createCategoryOutputOutput!.Data);

        _createStoreOutput = createStoreOutputOutput.Data;
        _createCategoryOutput = createCategoryOutputOutput.Data;
        _createProductOutput = createProductOutputOutput.Data;
    }

    [TearDown]
    public async Task TearDown()
    {
        await Mocks.DeleteTestProductAsync(_createStoreOutput!, _createProductOutput!);
        await Mocks.DeleteTestCategoryAsync(_createStoreOutput!, _createCategoryOutput!);
        await Mocks.DeleteTestStoreAsync(_createStoreOutput!);
    }

    [Test]
    public async Task HealthzTest()
    {
        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/Public/healthz");
                _.ContentShouldBe("HEALTH");
                _.StatusCodeShouldBeOk();
            });
    }

    [Test]
    public async Task BlobsTest()
    {
        // /Blobs/CreateUploadBlobSasUrls
        var createUploadBlobSasUrlsInput =
            new CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsInput(
                Mocks.SleekflowCompanyId,
                1,
                _createStoreOutput!.Store.Id,
                BlobTypes.File);
        var createUploadBlobSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createUploadBlobSasUrlsInput).ToUrl("/Blobs/CreateUploadBlobSasUrls");
            });
        var createUploadBlobSasUrlsOutput =
            await createUploadBlobSasUrlsScenarioResult.ReadAsJsonAsync<
                Output<CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsOutput>>();

        Assert.That(createUploadBlobSasUrlsOutput, Is.Not.Null);
        Assert.That(createUploadBlobSasUrlsOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetCsvTemplateTest()
    {
        // /CustomCatalogs/GetCsvTemplate
        var getCsvTemplateScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/CustomCatalogs/GetCsvTemplate");
            });
        var text =
            await getCsvTemplateScenarioResult.ReadAsTextAsync();

        Assert.That(text, Is.Not.Null);
        Assert.That(
            text,
            Is.EqualTo(
                "category_name_{ISO-639-1-lang-code},sku_code,url,product_name_{ISO-639-1-lang-code},description__{idx}_text_{ISO-639-1-lang-code},price_{3-letter-currency-code},image_urls,image_blob_ids,attribute__{attribute_name}_{ISO-639-1-lang-code},metadata_{name}"));
    }

    [Test]
    public async Task GetCsvTemplateSampleTest()
    {
        // /CustomCatalogs/GetCsvTemplateSample
        var getCsvTemplateSampleScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/CustomCatalogs/GetCsvTemplateSample");
            });
        var text =
            await getCsvTemplateSampleScenarioResult.ReadAsTextAsync();

        Assert.That(text, Is.Not.Null);
        Assert.That(
            text,
            Is.EqualTo(
                """
sku_code,url,product_name_en,description__1_text_en,price_HKD,image_urls
SKU-001,https://example.com/p/SKU-001,Hello Drink 1,Drink description 1,100.0,"https://sc32add32.blob.core.windows.net/public-container/drink-1.jpg"
SKU-002,https://example.com/p/SKU-002,Hello Drink 2,Drink description 2,250.0,"https://sc32add32.blob.core.windows.net/public-container/drink-2.jpg"
SKU-003,https://example.com/p/SKU-003,Hello Drink 3,Drink description 3,500.0,"https://sc32add32.blob.core.windows.net/public-container/drink-3.jpg"
SKU-004,https://example.com/p/SKU-004,Hello Drink 4,Drink description 4,100.0,"https://sc32add32.blob.core.windows.net/public-container/drink-4.jpg"

""".ReplaceLineEndings("\n")));
    }

    [Test]
    public async Task CustomCatalogsTest()
    {
        // /Blobs/CreateUploadBlobSasUrls
        var createUploadBlobSasUrlsInput =
            new CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsInput(
                Mocks.SleekflowCompanyId,
                1,
                _createStoreOutput!.Store.Id,
                BlobTypes.File);
        var createUploadBlobSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createUploadBlobSasUrlsInput).ToUrl("/Blobs/CreateUploadBlobSasUrls");
            });
        var createUploadBlobSasUrlsOutput =
            await createUploadBlobSasUrlsScenarioResult.ReadAsJsonAsync<
                Output<CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsOutput>>();

        Assert.That(createUploadBlobSasUrlsOutput, Is.Not.Null);
        Assert.That(createUploadBlobSasUrlsOutput!.HttpStatusCode, Is.EqualTo(200));

        var uploadBlobs = createUploadBlobSasUrlsOutput.Data.UploadBlobs;

        Assert.That(uploadBlobs, Is.Not.Null);
        Assert.That(uploadBlobs.Count, Is.EqualTo(1));

        var targetUploadBlob = uploadBlobs[0];

        var uri = new Uri(targetUploadBlob.BlobUrl);
        var blockBlobClient = new BlockBlobClient(uri);

        await using var fileStream = File.OpenRead("sample.csv");
        await blockBlobClient.UploadAsync(
            fileStream,
            new BlobHttpHeaders()
            {
                ContentType = "text/csv"
            });

        // /CustomCatalogs/ProcessCustomCatalogCsv
        var processCustomCatalogCsvInput =
            new ProcessCustomCatalogCsv.ProcessCustomCatalogCsvInput(
                Mocks.SleekflowCompanyId,
                _createStoreOutput!.Store.Id,
                targetUploadBlob.BlobName,
                Mocks.SleekflowStaffId,
                null);
        var processCustomCatalogCsvScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(processCustomCatalogCsvInput).ToUrl("/CustomCatalogs/ProcessCustomCatalogCsv");
            });
        var processCustomCatalogCsvOutput =
            await processCustomCatalogCsvScenarioResult.ReadAsJsonAsync<
                Output<ProcessCustomCatalogCsv.ProcessCustomCatalogCsvOutput>>();

        // /CustomCatalogs/GetCustomCatalogFiles
        var getCustomCatalogFilesInput =
            new GetCustomCatalogFiles.GetCustomCatalogFilesInput(
                Mocks.SleekflowCompanyId,
                _createStoreOutput!.Store.Id);
        var getCustomCatalogFilesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCustomCatalogFilesInput).ToUrl("/CustomCatalogs/GetCustomCatalogFiles");
            });
        var getCustomCatalogFilesOutput =
            await getCustomCatalogFilesScenarioResult.ReadAsJsonAsync<
                Output<GetCustomCatalogFiles.GetCustomCatalogFilesOutput>>();
    }

    [Test]
    public async Task VerifyCustomCatalogCsv()
    {
        var createUploadBlobSasUrlsInput =
            new CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsInput(
                Mocks.SleekflowCompanyId,
                1,
                _createStoreOutput!.Store.Id,
                BlobTypes.File);
        var createUploadBlobSasUrlsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createUploadBlobSasUrlsInput).ToUrl("/Blobs/CreateUploadBlobSasUrls");
            });
        var createUploadBlobSasUrlsOutput =
            await createUploadBlobSasUrlsScenarioResult.ReadAsJsonAsync<
                Output<CreateUploadBlobSasUrls.CreateUploadBlobSasUrlsOutput>>();

        Assert.That(createUploadBlobSasUrlsOutput, Is.Not.Null);
        Assert.That(createUploadBlobSasUrlsOutput!.HttpStatusCode, Is.EqualTo(200));

        var uploadBlobs = createUploadBlobSasUrlsOutput.Data.UploadBlobs;

        Assert.That(uploadBlobs, Is.Not.Null);
        Assert.That(uploadBlobs.Count, Is.EqualTo(1));

        var targetUploadBlob = uploadBlobs[0];

        var uri = new Uri(targetUploadBlob.BlobUrl);
        var blockBlobClient = new BlockBlobClient(uri);

        await using var fileStream = File.OpenRead("sample.csv");
        await blockBlobClient.UploadAsync(
            fileStream,
            new BlobHttpHeaders()
            {
                ContentType = "text/csv"
            });

        var verifyCustomCatalogCsvInput = new VerifyCustomCatalogCsv.VerifyCustomCatalogCsvInput(
            Mocks.SleekflowCompanyId,
            targetUploadBlob.BlobId);

        var verifyCustomCatalogCsvResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(verifyCustomCatalogCsvInput).ToUrl("/CustomCatalogs/VerifyCustomCatalogCsv");
            });
    }
}