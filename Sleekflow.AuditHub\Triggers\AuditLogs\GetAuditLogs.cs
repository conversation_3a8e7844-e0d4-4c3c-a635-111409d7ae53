﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.AuditLogs;
using Sleekflow.AuditHub.Models.AuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Queries;

namespace Sleekflow.AuditHub.Triggers.AuditLogs;

[TriggerGroup("AuditLogs")]
public class GetAuditLogs : ITrigger
{
    private readonly ILogger<GetAuditLogs> _logger;
    private readonly IAuditLogRepository _auditLogRepository;

    public GetAuditLogs(
        ILogger<GetAuditLogs> logger,
        IAuditLogRepository auditLogRepository)
    {
        _logger = logger;
        _auditLogRepository = auditLogRepository;
    }

    public class GetAuditLogsInputFilterGroup
    {
        [Required]
        [JsonProperty("filters")]
        public List<QueryBuilder.Filter> Filters { get; set; }

        [JsonConstructor]
        public GetAuditLogsInputFilterGroup(
            List<QueryBuilder.Filter> filters)
        {
            Filters = filters;
        }
    }

    public class GetAuditLogsInput : IValidatableObject
    {
        [StringLength(16384, MinimumLength = 1)]
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [JsonProperty("filter_groups")]
        public List<GetAuditLogsInputFilterGroup> FilterGroups { get; set; }

        [Required]
        [JsonProperty("sorts")]
        public List<QueryBuilder.Sort> Sorts { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            var filters = FilterGroups.SelectMany(fg => fg.Filters).ToList();
            if (filters.Any(f => f.FieldName == "sys_sleekflow_company_id" || f.FieldName == "sys_type_name"))
            {
                results.Add(
                    new ValidationResult(
                        "The Filters is not supported.",
                        new List<string>
                        {
                            "Filters"
                        }));
            }

            foreach (var filter in filters)
            {
                Validator.TryValidateObject(filter, new ValidationContext(filter), results, true);
            }

            foreach (var sort in Sorts)
            {
                Validator.TryValidateObject(sort, new ValidationContext(sort), results, true);
            }

            return results;
        }

        [JsonConstructor]
        public GetAuditLogsInput(
            string? continuationToken,
            string sleekflowCompanyId,
            int limit,
            List<GetAuditLogsInputFilterGroup> filterGroups,
            List<QueryBuilder.Sort> sorts)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            Limit = limit;
            FilterGroups = filterGroups;
            Sorts = sorts;
        }
    }

    public class GetAuditLogsOutput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("records")]
        public List<AuditLog> Records { get; set; }

        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetAuditLogsOutput(
            string? continuationToken,
            List<AuditLog> records,
            long count)
        {
            ContinuationToken = continuationToken;
            Records = records;
            Count = count;
        }
    }

    public async Task<GetAuditLogsOutput> F(
        GetAuditLogsInput getAuditLogsInput)
    {
        var filterGroups = getAuditLogsInput
            .FilterGroups
            .Select(fg => new QueryBuilder.FilterGroup(fg.Filters.Cast<QueryBuilder.IFilter>().ToList()))
            .ToList();
        filterGroups.Add(
            new QueryBuilder.FilterGroup(
                new List<QueryBuilder.IFilter>()
                {
                    new QueryBuilder.Filter(
                        "sys_sleekflow_company_id",
                        "=",
                        getAuditLogsInput.SleekflowCompanyId)
                }));
        filterGroups.Add(
            new QueryBuilder.FilterGroup(
                new List<QueryBuilder.IFilter>()
                {
                    new QueryBuilder.Filter(
                        "sys_type_name",
                        "=",
                        "AuditLog")
                }));

        var queryDefinition =
            QueryBuilder.BuildQueryDef(
                new List<QueryBuilder.ISelect>(),
                filterGroups,
                getAuditLogsInput.Sorts);

        _logger.LogInformation(
            "Executing queryText {QueryText}, queryParameters {QueryParameters}",
            queryDefinition.QueryText,
            queryDefinition.GetQueryParameters());

        var (records, nextContinuationToken) =
            await _auditLogRepository.GetContinuationTokenizedObjectsAsync(
                queryDefinition,
                getAuditLogsInput.ContinuationToken,
                getAuditLogsInput.Limit);

        return new GetAuditLogsOutput(nextContinuationToken, records, records.Count);
    }
}