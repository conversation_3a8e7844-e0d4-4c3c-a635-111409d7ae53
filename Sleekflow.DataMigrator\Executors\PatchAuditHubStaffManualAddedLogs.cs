using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

public class PatchAuditHubStaffManualAddedLogs : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private string? _containerId;

    public PatchAuditHubStaffManualAddedLogs(DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Patch Audit Hub Staff Manual Added Logs";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerId = Prompt.Select(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerId = selectedContainerId;
    }

    public async Task ExecuteAsync()
    {
        if (!string.IsNullOrEmpty(_containerId))
        {
            var count = await MigrateObjectsAsync(_containerId);
            Console.WriteLine($"Completed {_containerId} for {count} objects");
        }
        else
        {
            Console.WriteLine("Unable to start PatchAuditHubStaffManualAddedLogs");
        }
    }

    private async Task<int> MigrateObjectsAsync(
        string containerId)
    {
        var retryPolicy = CosmosUtils.GetDefaultRetryPolicy(containerId);

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerId);
        var partitionKeyPaths =
            (await CosmosUtils.GetContainerPartitionKeyPathsAsync(_cosmosClient, _databaseId!, containerId))!;

        var i = 0;
        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(
                container,
                "SELECT * FROM root c WHERE c.type = 'manual-log' ORDER BY c._ts ASC"),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 2000
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(
                        async () => await MigrateObjectAsync(dict, container, partitionKeyPaths, token));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }

    private static async Task<int> MigrateObjectAsync(
        Dictionary<string, object?> dict,
        Container container,
        IReadOnlyList<string> partitionKeyPaths,
        CancellationToken token)
    {
        var partitionKeyBuilder = new PartitionKeyBuilder();
        foreach (var partitionKeyPath in partitionKeyPaths!)
        {
            var keyParts = partitionKeyPath.TrimStart('/').Split('/');
            var currentValue = CosmosUtils.GetValueFromDictionary(dict, keyParts, 0);
            partitionKeyBuilder.Add(currentValue);
        }

        if (dict["audit_log_text"] is string t1
            && t1.StartsWith("Switched channel to "))
        {
            dict["type"] = "conversation-channel-switched";
            dict.Remove("ttl");
        }
        else if (dict["audit_log_text"] is string t2
                 && t2.Contains("viewed unread messages"))
        {
            dict["type"] = "conversation-read";
            dict.Remove("ttl");
        }
        else if (dict["audit_log_text"] is string t3
                 && t3.Contains("The chat history has been backed up successfully"))
        {
            dict["type"] = "user-profile-chat-history-backed-up";
            dict.Remove("ttl");
        }
        else if (dict["audit_log_text"] is string t4
                 && t4.Contains("Conversation Status:"))
        {
            dict["type"] = "conversation-status-changed";
            dict.Remove("ttl");
        }
        else
        {
            dict["ttl"] = -1;
        }

        await container.UpsertItemAsync(
            dict,
            partitionKeyBuilder.Build(),
            new ItemRequestOptions
            {
                EnableContentResponseOnWrite = false,
            },
            token);

        return 1;
    }
}