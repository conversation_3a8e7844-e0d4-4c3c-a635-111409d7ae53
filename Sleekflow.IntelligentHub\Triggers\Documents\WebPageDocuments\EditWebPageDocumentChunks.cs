﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.WebPageDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Documents.WebPageDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class EditWebPageDocumentChunks
    : ITrigger<
        EditWebPageDocumentChunks.EditWebPageDocumentChunksInput,
        EditWebPageDocumentChunks.EditWebPageDocumentChunksOutput>
{
    private readonly IWebPageDocumentChunkService _webPageDocumentChunkService;

    public EditWebPageDocumentChunks(IWebPageDocumentChunkService webPageDocumentChunkService)
    {
        _webPageDocumentChunkService = webPageDocumentChunkService;
    }

    public class EditWebPageDocumentChunksInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [Required]
        [JsonProperty("editing_chunks")]
        [ValidateArray]
        public List<EditingChunkDto> EditingChunks { get; set; }

        [JsonConstructor]
        public EditWebPageDocumentChunksInput(
            string sleekflowCompanyId,
            string documentId,
            List<EditingChunkDto> editingChunks)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            EditingChunks = editingChunks;
        }
    }

    public class EditWebPageDocumentChunksOutput
    {
        [JsonProperty("edited_chunks")]
        public List<EditingChunkDto> EditedChunks { get; set; }

        [JsonConstructor]
        public EditWebPageDocumentChunksOutput(List<EditingChunkDto> editedChunks)
        {
            EditedChunks = editedChunks;
        }
    }

    public async Task<EditWebPageDocumentChunksOutput> F(EditWebPageDocumentChunksInput editWebPageDocumentChunksInput)
    {
        var editedChunks = new List<EditingChunkDto>();

        foreach (var editingChunk in editWebPageDocumentChunksInput.EditingChunks)
        {
            var editedChunk = await _webPageDocumentChunkService.PatchAndGetWebPageDocumentChunkAsync(
                editWebPageDocumentChunksInput.SleekflowCompanyId,
                editWebPageDocumentChunksInput.DocumentId,
                editingChunk);
            editedChunks.Add(
                new EditingChunkDto(editedChunk.Id, editedChunk.Content, editedChunk.Categories, editedChunk.Metadata));
        }

        return new EditWebPageDocumentChunksOutput(editedChunks);
    }
}