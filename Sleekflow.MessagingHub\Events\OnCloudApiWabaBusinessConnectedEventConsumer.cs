using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiWabaBusinessConnectedEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiWabaBusinessConnectedEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiWabaBusinessConnectedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiWabaBusinessConnectedEventConsumer : IConsumer<OnCloudApiWabaBusinessConnectedEvent>
{
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiWabaBusinessConnectedEventConsumer> _logger;
    private readonly ILockService _lockService;
    private readonly IAuditLogService _auditLogService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;
    private readonly IWhatsappCloudApiBusinessBalanceWebhookService _whatsappCloudApiBusinessBalanceWebhookService;

    public OnCloudApiWabaBusinessConnectedEventConsumer(
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        ILogger<OnCloudApiWabaBusinessConnectedEventConsumer> logger,
        ILockService lockService,
        IAuditLogService auditLogService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService,
        IWhatsappCloudApiBusinessBalanceWebhookService whatsappCloudApiBusinessBalanceWebhookService)
    {
        _logger = logger;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _lockService = lockService;
        _auditLogService = auditLogService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
        _whatsappCloudApiBusinessBalanceWebhookService = whatsappCloudApiBusinessBalanceWebhookService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiWabaBusinessConnectedEvent> context)
    {
        var cancellationToken = context.CancellationToken;
        var onCloudApiWabaBusinessConnectedEvent = context.Message;
        var facebookBusinessId = onCloudApiWabaBusinessConnectedEvent.FacebookBusinessId;
        var retryCount = context.GetRedeliveryCount();
        if (retryCount > 3)
        {
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);
        if (wabas.Count == 0)
        {
            _logger.LogWarning(
                "Unable to locate any waba associate to given facebookBusinessId {Context}",
                JsonConvert.SerializeObject(context));
            await context.Redeliver(TimeSpan.FromSeconds(8));
            return;
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is not null)
        {
            _logger.LogInformation(
                "Business balances record already exists for given business balances {BusinessBalance}",
                JsonConvert.SerializeObject(businessBalance));

            if (businessBalance.IsByWabaBillingEnabled.HasValue && businessBalance.IsByWabaBillingEnabled.Value)
            {
                // creating waba balance
                var wabasNotInBusinessWabaBalance = wabas.Where(
                    waba =>
                        businessBalance.WabaBalances?.TrueForAll(
                            wabaBalance => wabaBalance.FacebookWabaId != waba.FacebookWabaId) ?? true).ToList();

                if (wabasNotInBusinessWabaBalance.Any())
                {
                    var @lock = await _lockService.LockAsync(
                        new[]
                        {
                            businessBalance.FacebookBusinessId
                        },
                        TimeSpan.FromSeconds(
                            60 * (OnCloudApiWabaBusinessConnectedEventConsumerDefinition.LockDuration +
                                  OnCloudApiWabaBusinessConnectedEventConsumerDefinition.MaxAutoRenewDuration)),
                        cancellationToken);

                    if (@lock is null)
                    {
                        await context.Redeliver(TimeSpan.FromSeconds(8));
                    }

                    try
                    {
                        _logger.LogInformation(
                            "Initiating Waba Balances To {FacebookBusinessId} BusinessBalance {WabasNotInBusinessWabaBalance}",
                            facebookBusinessId,
                            JsonConvert.SerializeObject(wabasNotInBusinessWabaBalance));
                        var wabaIdsNotInBusinessWabaBalance =
                            wabasNotInBusinessWabaBalance.Select(x => x.FacebookWabaId).ToHashSet();
                        var businessBalanceSnapshot =
                            JsonConvert.DeserializeObject<BusinessBalance>(
                                JsonConvert.SerializeObject(businessBalance));
                        businessBalance = await _wabaLevelCreditManagementService.InitWabaBalancesInBusinessBalanceAsync(
                            wabaIdsNotInBusinessWabaBalance,
                            businessBalance);

                        var businessBalanceUpsertState = await _businessBalanceService.UpsertBusinessBalanceAsync(
                            businessBalance);

                        if (businessBalanceUpsertState == 0)
                        {
                            throw new SfInternalErrorException($"Unable to upsert business balance for facebook business id {facebookBusinessId} facebook waba ids {JsonConvert.SerializeObject(wabaIdsNotInBusinessWabaBalance)}");
                        }

                        await _auditLogService.AuditBusinessBalanceAsync(
                            businessBalanceSnapshot,
                            businessBalance.FacebookBusinessId,
                            AuditingOperation.OnCloudApiWabaBalanceConnectedEvent,
                            new Dictionary<string, object?>
                            {
                                {
                                    "changes", businessBalance
                                }
                            });
                    }
                    catch (Exception exception)
                    {
                        _logger.LogError(
                            exception,
                            "Error occur during InitWabaBalancesToBusinessBalanceAsync {Context}/{Exception}",
                            JsonConvert.SerializeObject(context),
                            JsonConvert.SerializeObject(exception));
                        await context.Redeliver(TimeSpan.FromSeconds(8));
                    }
                    finally
                    {
                        if (@lock != null)
                        {
                            await _lockService.ReleaseAsync(@lock, cancellationToken);
                        }
                    }
                }
            }

            return;
        }

        var earliestCreatedWaba = wabas.OrderBy(w => w.CreatedAt).First();
        var createdAt = earliestCreatedWaba.CreatedAt;
        var lastConversationUsageInsertTimestamp =
            new DateTimeOffset(createdAt.Year, createdAt.Month, createdAt.Day, createdAt.Hour, 0, 0, TimeSpan.Zero)
                .AddDays(-2)
                .ToUnixTimeSeconds();
        var conversationUsageInsertState = new ConversationUsageInsertState(
            lastConversationUsageInsertTimestamp,
            DateTimeOffset.UtcNow,
            new Dictionary<string, WabaConversationInsertionException>());
        try
        {
            await _businessBalanceService.CreateAndGetBusinessBalanceAsync(
                facebookBusinessId,
                new MarkupProfile(new Money(Currencies.Usd, 0), new Money(Currencies.Usd, 0)),
                conversationUsageInsertState);

            var webhookUrl = onCloudApiWabaBusinessConnectedEvent.WebhookUrl;

            if (!string.IsNullOrEmpty(webhookUrl))
            {
                var sleekflowCompanyIds = wabas.SelectMany(x => x.SleekflowCompanyIds).ToHashSet();
                foreach (var sleekflowCompanyId in sleekflowCompanyIds)
                {
                    await _whatsappCloudApiBusinessBalanceWebhookService.UpsertWebhookAsync(
                        sleekflowCompanyId,
                        facebookBusinessId,
                        webhookUrl);
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(
                exception,
                "Error occur during CreateAndGetBusinessBalanceAsync {Context}/{Exception}",
                JsonConvert.SerializeObject(context),
                JsonConvert.SerializeObject(exception));
            await context.Redeliver(TimeSpan.FromSeconds(8));
        }
    }
}