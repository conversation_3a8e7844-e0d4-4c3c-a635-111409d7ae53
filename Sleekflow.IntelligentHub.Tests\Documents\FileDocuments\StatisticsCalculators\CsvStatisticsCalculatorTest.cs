using Moq;
using Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;
using Sleekflow.IntelligentHub.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.StatisticsCalculators;

[TestFixture]
[TestOf(typeof(CsvStatisticsCalculator))]
public class CsvStatisticsCalculatorTest
{
    const string CsvFilePath = "../../../Binaries/business-operations-survey-2022-business-finance.csv";

    [Test]
    public void CalculateDocumentStatisticsTest()
    {
        var statisticsCalculator = new CsvStatisticsCalculator(new Mock<IDocumentCounterService>().Object);

        using var fileStream = new FileStream(CsvFilePath, FileMode.Open, FileAccess.Read, FileShare.None);
        var documentStatistics = statisticsCalculator.CalculateDocumentStatistics(fileStream);

        Assert.That(documentStatistics.TotalPages, Is.EqualTo(133));
    }
}