﻿using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.Audits;

public class WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters
{
    [JsonProperty("facebook_business_id")]
    public string? FacebookBusinessId { get; set; }

    [JsonProperty("facebook_phone_number_id")]
    public string? FacebookPhoneNumberId { get; set; }

    [JsonProperty("facebook_message_template_id")]
    public string? FacebookMessageTemplateId { get; set; }

    [JsonConstructor]
    public WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters(
        string? facebookBusinessId,
        string? facebookPhoneNumberId,
        string? facebookMessageTemplateId)
    {
        FacebookBusinessId = facebookBusinessId;
        FacebookPhoneNumberId = facebookPhoneNumberId;
        FacebookMessageTemplateId = facebookMessageTemplateId;
    }
}