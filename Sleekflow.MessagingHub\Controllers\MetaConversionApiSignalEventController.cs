using GraphApi.Client.Payloads.Models.ConversionApi;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.WhatsappCloudApis.MetaConversionApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("[Controller]")]
public class MetaConversionApiSignalEventController : ControllerBase
{
    private const string FacebookWabaIdHeaderName = "X-Sleekflow-Facebook-Waba-Id";

    private readonly IWabaService _wabaService;
    private readonly ILogger<MetaConversionApiSignalEventController> _logger;
    private readonly IWabaAssetsManager _wabaAssetsManager;
    private readonly IMetaConversionApiService _metaConversionApiService;

    public MetaConversionApiSignalEventController(
        IWabaService wabaService,
        ILogger<MetaConversionApiSignalEventController> logger,
        IWabaAssetsManager wabaAssetsManager,
        IMetaConversionApiService metaConversionApiService)
    {
        _wabaService = wabaService;
        _logger = logger;
        _wabaAssetsManager = wabaAssetsManager;
        _metaConversionApiService = metaConversionApiService;
    }

    [HttpPost]
    [Route("SendConversionApiSignalEvent")]
    public async Task<IActionResult> SendConversionApiSignalEvent()
    {
        try
        {
            var facebookWabaId = GetRequiredHeader(FacebookWabaIdHeaderName);

            var contentType = GetRequiredHeader(HeaderNames.ContentType);

            if (contentType.Contains("application/json", StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogError(
                    "Not supported Content-Type: {ContentType}, waba id: {FacebookWabaId}",
                    contentType,
                    facebookWabaId);

                return new BadRequestResult();
            }

            var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);
            var wabaDataset = _wabaAssetsManager.GetExistingWabaDataset(waba);

            if (wabaDataset is null)
            {
                return new NotFoundObjectResult($"Waba {facebookWabaId} dataset not found");
            }

            var reqBodyStr = await new StreamReader(Request.Body).ReadToEndAsync();

            var sendConversionApiEvent = JsonConvert.DeserializeObject<SendConversionApiEventObject>(reqBodyStr);

            if (sendConversionApiEvent == null)
            {
                _logger.LogError(
                    "Invalid Conversion API signal event: {ConversionApiEvent}, waba id: {FacebookWabaId}",
                    reqBodyStr,
                    facebookWabaId);

                return new BadRequestObjectResult("Invalid Conversion API signal event");
            }

            var (_, tokenDto) = _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

            var sendConversionApiEventResponse = await _metaConversionApiService.SendConversionApiEventAsync(
                wabaDataset.FacebookDatasetId,
                sendConversionApiEvent,
                tokenDto?.DecryptedToken);

            _logger.LogInformation(
                "Conversion API signal event sent for Waba {FacebookWabaId} {FacebookDatasetId} with {RequestPayload} and {Response}",
                facebookWabaId,
                wabaDataset.FacebookDatasetId,
                JsonConvert.SerializeObject(sendConversionApiEvent),
                JsonConvert.SerializeObject(sendConversionApiEventResponse));

            return new OkObjectResult(sendConversionApiEventResponse);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[MessagingHub] Error when processing Conversion API signal event");

            throw;
        }
    }

    private string GetRequiredHeader(string headerName)
    {
        if (!Request.Headers.TryGetValue(headerName, out var headerValue))
        {
            throw new SfInternalErrorException($"Header {headerName} must be provided");
        }

        return headerValue.ToString();
    }
}