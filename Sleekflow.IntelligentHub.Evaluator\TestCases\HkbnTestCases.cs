using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class HkbnTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetHkbnTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/HKBN/",
            SleekflowCompanyId: "b6d7e442-38ae-4b9a-b100-2951729768bc");

        var indicaidDocuments =
            new List<string>
            {
                "INDICAID「月月測」產品小冊子.md", "RS_sales briefing_INDICAID_20241204.docx.md"
            };
        var bowtieDocuments =
            new List<string>
            {
                "HKBN x Bowtie_4-in-1 Healthcare Service_WhatsApp guide for customer_20241008.md",
                "RS_sales briefing_Bowtie_20241204.docx.md"
            };
        var evercareDocuments =
            new List<string>
            {
                "HKBN x Evercare Whatsapp Guide_20241204.md", "RS_sales briefing_Evercare_20241204.docx.md"
            };
        var mobilePlanDocuments =
            new List<string>
            {
                "RS_sales briefing_N mobile_42mbps $78 $98 China data_20241204.docx.md",
                "RS_sales briefing_N mobile_APAC_20241204.docx.md",
                "RS_sales briefing_tri-area_20241204.docx.md",
                "RS_sales briefing_WW plan_20241204.docx.md"
            };

        yield return new ChatEvalQuestion(
            testConfig,
            "Greetings",
            [
                new ChatMessageContent(AuthorRole.User, "你好。"),
            ],
            "你好，有咩可以幫到你？",
            SourceFilenames: []);
        yield return new ChatEvalQuestion(
            testConfig,
            "Product Enquiry",
            [
                new ChatMessageContent(AuthorRole.User, "Are there any other healthcare products or promotions?\n\n"),
            ],
            "Thank you for your inquiry about our healthcare products and promotions! We currently have several exciting options available:\n\n1. **Evercare Home Care Programme**: This program offers essential home care services starting at just $399 per month. It includes options for wound care and escort services, ensuring you receive the support you need in the comfort of your home.\n\n2. **Bowtie 4-in-1 Medical Service Plan**: For only $99 per month, this plan provides unlimited GP consultations, dental services, body screenings, and vaccinations. It's a comprehensive solution for your healthcare needs.\n\n3. **INDICAID Rapid Test Package Plan**: We offer two flexible plans for health monitoring, including cancer screening kits. The Advanced Plan is priced at $68 per month, making it an affordable option for proactive health management.\n\nThese products reflect our commitment to providing accessible healthcare solutions tailored to your needs. If you have any specific questions or need further assistance, feel free to ask!\n",
            SourceFilenames: []);
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer inquiring about INDICAID monthly testing plan and product details",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我最近看到香港寬頻有個「月月測」自選自測計劃，想了解一下基本計劃的詳情。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎查詢！基本計劃是12個月合約，每月$48就可以自選2盒檢測試劑盒。每個月更可享1次免費送貨服務（限香港境內，偏遠地區除外）。請問您對哪種檢測試劑盒較感興趣？"),
                new ChatMessageContent(AuthorRole.User, "這個基本計劃裡面可以揀什麼試劑盒？")
            ],
            "基本計劃可以在以下試劑盒中選擇：\n1. 新冠病毒/甲型及乙型流感快速抗原檢測試劑盒（建議零售價$40）\n2. 呼吸道6合1快速抗原檢測試劑盒（建議零售價$68）\n3. 腸道3合1快速抗原檢測試劑盒（建議零售價$128）\n4. HIV抗原/抗體快速檢測試劑盒（建議零售價$298）\n5. 特早即知驗孕棒（建議零售價$58）\n6. 排卵快速測試（建議零售價$88）\n7. HIV/梅毒快速抗體檢測試劑盒（建議零售價$368）",
            SourceFilenames: indicaidDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "Parent seeking health monitoring solutions for a kindergarten-aged child during flu season",
            [
                new ChatMessageContent(AuthorRole.User, "最近流感高發，想問有沒有比較適合小朋友用的快速檢測方案？我家小朋友讀幼稚園，經常與同學密切接觸。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "完全明白你的擔心！針對幼童的健康監測，我建議可以考慮以下方案：\n1. 我們的呼吸道6合1快速檢測特別適合小童使用，15分鐘就能同時檢測出6種最常見的呼吸道病毒，包括流感和新冠等。\n2. 檢測過程簡單，準確度高達99%，更是本港私家醫院最常用的品牌。\n你想了解一下這個檢測的具體操作方式嗎？"),
                new ChatMessageContent(AuthorRole.User, "聽起來不錯，但一個檢測要幾錢？因為可能要定期檢查。")
            ],
            "其實我們有專門為需要定期檢測的家庭設計的方案。以每月$48起的預算，你便可以獲得定期的檢測試劑，而且包括免費送貨服務，不用特地外出購買。這樣不僅比較經濟實惠，更可以確保家中隨時都有檢測試劑以備不時之需。要我跟你詳細介紹這個家庭計劃嗎？",
            SourceFilenames: indicaidDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "Customer inquiring about HKBN x Bowtie 4-in-1 Medical Service Plan features",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'd like to know more about the HKBN x Bowtie medical plan. What services are included?"),
            ],
            "The HKBN x Bowtie 4-in-1 Medical Service Plan includes these key benefits:\n1. Unlimited video consultations with western medicine doctors\n2. 2 dental scaling and polishing services (suggested retail price: $1,920)\n3. 1 full body check-up (suggested retail price: $1,280)\n4. 1 flu vaccination service (suggested retail price: $300)\n\nWould you like to know more about any specific service?",
            SourceFilenames: bowtieDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "HKBN x Bowtie_4-in-1 Healthcare Service_WhatsApp guide for customer_20241008.md - Customer asking about video consultation details",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How does the video consultation work? Does it include medicine?"),
            ],
            "Yes, the video consultation service includes:\n- Free consultation fee\n- 2 days of basic medication included\n- Free medical certificates if needed\n- Medicine delivery within 4 hours after consultation\n- Service available Monday to Friday (9:00 AM - 7:00 PM) and Saturday (9:00 AM - 1:00 PM)\n- Excludes public holidays and during black rainstorm/typhoon signal 8 or above\n\nThe medicine will be delivered to your doorstep within 4 hours after consultation. Would you like to know about the activation process?",
            SourceFilenames: bowtieDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "HKBN x Bowtie_4-in-1 Healthcare Service_WhatsApp guide for customer_20241008.md - Customer requesting video consultation assistance",
            [
                new ChatMessageContent(AuthorRole.User, "I'm feeling unwell. How can I arrange a video consultation?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'll help you arrange a video consultation. Here's the 5-step process:\n1. Login to app.bowtie.com.hk\n2. Select 'Video Consultation' under Health Services\n3. You'll be directed to the MyDoc service page\n4. Click 'Go to waiting room' when ready\n5. Have your HKID ready for verification\n\nWould you like me to guide you through the process?"),
                new ChatMessageContent(AuthorRole.User, "Yes, but what about the medicine delivery?"),
            ],
            "After your consultation:\n- The doctor will prescribe appropriate medication\n- Medicine, medical certificates, and receipts will be delivered within 4 hours\n- Delivery hours: Mon-Fri (9:00 AM - 11:00 PM), Sat (9:00 AM - 5:00 PM)\n- Service excludes public holidays and severe weather conditions\n\nNote: Special medications requiring refrigeration and deliveries to outlying islands are not available.",
            SourceFilenames: bowtieDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "Product Enquiry",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想詢問一下有關你們的居家護理服務計劃。我爸爸最近行動不便，需要在家中協助扶行、防跌和簡單的洗澡護理。"),
            ],
            "Evercare 提供多種居家護理服務計劃，專為需要在家中獲得協助的長者或行動不便人士設計。這些計劃包括基本和進階版本，旨在提供個人化的護理服務。\\\\n\\\\n**基本居家護理計劃**每月費用為 $399，提供每月一次的服務。這項服務包括個人身體護理，如扶行、防跌、協助洗澡、換尿布和簡單的烹飪（不超過 15 分鐘）。此外，該計劃還包括兩次營養諮詢和兩次專業個案諮詢。\\\\n\\\\n**進階居家護理計劃**每月費用為 $499，提供更深入的服務，每次訪問不超過四小時。這項服務適合需要特別護理的長者或個人，並包括與基本計劃相同的護理內容，以及額外的營養和個案諮詢。\\\\n\\\\nEvercare 的護理人員均接受過至少 50 小時的護理培訓，確保提供高質量的護理服務。這些計劃的靈活性使得服務可以根據個人需求進行安排，並且可以通過線上預約系統輕鬆預訂。\\\\n\\\\n如果您需要更多信息或想要註冊這些服務，請訪問 Evercare 的網站或聯繫他們的客戶服務。",
            SourceFilenames: evercareDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢香港寬頻 x Evercare居家護理服務的價格及詳情",
            [
                new ChatMessageContent(AuthorRole.User, "你好，想問下香港寬頻的居家護理服務計劃價錢係幾多？")
            ],
            "你好！香港寬頻 x Evercare目前提供幾個居家護理服務計劃：\n\n1. 居家護理服務基本版：\n- 每月$399（12個月合約）\n- 包括每次2小時服務\n- 標準價$610，現在享66折優惠\n\n2. 居家護理服務進階版：\n- 每月$499（12個月合約）\n- 包括每次4小時服務\n- 標準價$770，現在享65折優惠\n- 額外贈送護理計劃及營養諮詢（價值$5,200）\n\n請問你較有興趣哪一個計劃？",
            SourceFilenames: evercareDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢香港寬頻 x Evercare居家護理服務的價格及詳情",
            [
                new ChatMessageContent(AuthorRole.User, "你好嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "你好！我很好，謝謝你。請問有什麼我可以幫助你的嗎？"),
                new ChatMessageContent(AuthorRole.User, "我想了解一下上門護理服務"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我們提供的上門護理服務由Evercare與香港寬頻網絡（HKBN）合作提供，旨在為有需要的人士提供專業和個性化的護理。以下是我們的幾個主要服務計劃：\n\n1. **Evercare 基本家居護理計劃**：每月$399，提供基本的家居護理服務，包括個人身體護理、步行協助、防跌、淋浴幫助、換尿片和簡單烹飪。此外，還包括兩次營養諮詢和兩次專業個案諮詢。\n\n2. **Evercare 高級家居護理計劃**：每月$499，提供更全面的服務，除了基本計劃中的個人護理外，還增加了營養和個案諮詢。\n\n3. **Evercare 傷口護理計劃**：原價$850，現價$499，提供專業護士的專門傷口護理服務，並包括營養和個案諮詢。\n\n4. **Evercare 陪診服務計劃**：每月$499，提供陪同長者前往醫療約診的服務，並包括營養和個案諮詢。\n\n這些計劃旨在確保長者或有特定護理需求的人士在家中得到安全和舒適的照顧。服務時間靈活，可安排在週一至週日。\n\n如果您有興趣了解更多或需要進一步的幫助，請隨時告訴我！"),
                new ChatMessageContent(AuthorRole.User, "我媽媽，她現在行動不太方便"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我很理解您媽媽行動不便的情況，這確實需要特別的關注和支持。我們的上門護理服務專門設計來幫助像您媽媽這樣需要額外協助的人士。\n\n我們的護理計劃包括個人身體護理、步行協助和防跌措施，這些都可以幫助改善行動不便的情況。我們的專業護理人員會確保您媽媽在家中得到安全和舒適的照顧。\n\n您可以考慮我們的Evercare 基本家居護理計劃或高級家居護理計劃，這些計劃都包括步行協助和其他個人護理服務。\n\n如果您有任何具體的需求或疑問，請隨時告訴我，我們可以進一步討論如何最好地滿足您媽媽的需求。"),
                new ChatMessageContent(AuthorRole.User, "我好辛苦呀"),
            ],
            "我很抱歉聽到您感到辛苦。照顧家人可能會帶來很大的壓力和挑戰。請記住，尋求幫助是完全可以理解的，並且有很多資源可以支持您。\n\n我們的上門護理服務可以在這方面提供幫助，讓專業的護理人員協助照顧您媽媽，這樣您可以有更多的時間和空間來照顧自己。\n\n如果您需要更多的信息或想要討論如何開始使用這些服務，請隨時告訴我。我們在這裡支持您。",
            SourceFilenames: evercareDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢香港寬頻居家護理服務計劃價格和細節",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下香港寬頻的居家護理服務計劃價錢"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！我們現有兩個居家護理服務計劃：基本版每月$399（2小時服務）及進階版每月$499（4小時服務）。請問你比較有興趣哪一種計劃？"),
                new ChatMessageContent(AuthorRole.User, "可以詳細介紹一下基本版計劃嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "基本版計劃月費$399，合約期12個月，每次提供2小時服務。服務範圍包括：\n- 上門居家照顧服務\n- 個人身體護理（如扶行、防跌）\n- 協助沖涼、更換尿片\n- 不多於15分鐘簡單煮食\n這個計劃特別適合有基本照顧需要的長者或人士，而且現在優惠低至66折（原價$610）。需要了解更多詳情嗎？"),
                new ChatMessageContent(AuthorRole.User, "這個服務是由什麼人提供的？")
            ],
            "這個服務是由經過專業訓練的護理人員提供，他們都已接受最少50小時的護理訓練。而且，Evercare平台擁有超過30,000名護理人員，每位都經過嚴格的背景調查和審核，確保服務質素。您可以放心，我們會根據您的個人需要和喜好，為您配對最合適的護理人員。",
            SourceFilenames: evercareDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢上門護理服務價格和細節",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下上門護理服務"),
            ],
            "你好！很高興為你服務。我們現正推出香港寬頻 x Evercare「關愛在家」居家照顧計劃，每月低至$399就可以享有專業的居家護理服務。請問你需要照顧的是長者還是其他家庭成員？",
            SourceFilenames: evercareDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢上門護理服務價格和細節",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下上門護理服務"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興為你服務。我們現正推出香港寬頻 x Evercare「關愛在家」居家照顧計劃，每月低至$399就可以享有專業的居家護理服務。請問你需要照顧的是長者還是其他家庭成員？"),
                new ChatMessageContent(AuthorRole.User, "是我媽媽，她現在行動不太方便。可以告訴我具體收費嗎？"),
            ],
            "明白。我們有兩個居家護理服務計劃可供選擇：\n1. 基本版：每月$399，提供2小時服務\n2. 進階版：每月$499，提供4小時服務\n兩個計劃都包括個人護理、扶行、協助沖涼等服務。而且現在是特別優惠價，比原價便宜很多。想了解哪個計劃的詳情？",
            SourceFilenames: evercareDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢上門護理服務價格和細節",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下上門護理服務"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興為你服務。我們現正推出香港寬頻 x Evercare「關愛在家」居家照顧計劃，每月低至$399就可以享有專業的居家護理服務。請問你需要照顧的是長者還是其他家庭成員？"),
                new ChatMessageContent(AuthorRole.User, "是我媽媽，她現在行動不太方便。可以告訴我具體收費嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白。我們有兩個居家護理服務計劃可供選擇：\n1. 基本版：每月$399，提供2小時服務\n2. 進階版：每月$499，提供4小時服務\n兩個計劃都包括個人護理、扶行、協助沖涼等服務。而且現在是特別優惠價，比原價便宜很多。想了解哪個計劃的詳情？"),
                new ChatMessageContent(AuthorRole.User, "2小時那個計劃包括什麼服務？")
            ],
            "基本版計劃（2小時服務）的服務範圍包括：\n- 專業護理人員上門服務\n- 協助扶行和防跌\n- 個人護理（如協助沖涼、更換尿片）\n- 簡單煮食（不多於15分鐘）\n現在優惠價每月$399（原價$610），合約期12個月。這個計劃特別適合需要基本照顧的長者。我們的護理人員都經過最少50小時專業訓練，您可以放心。需要了解更多嗎？",
            SourceFilenames: evercareDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問中國內地數據計劃並進行價格協商",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
            ],
            "您好！是的，我們最近推出了「42Mbps本地數據+中國內地及澳門數據流動通訊計劃」，有多個方案可供選擇。請問您平均每月會使用多少本地數據？",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問中國內地數據計劃並進行價格協商",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！是的，我們最近推出了「42Mbps本地數據+中國內地及澳門數據流動通訊計劃」，有多個方案可供選擇。請問您平均每月會使用多少本地數據？"),
                new ChatMessageContent(AuthorRole.User, "我每月大概用10GB左右。另外我經常去內地，所以都需要內地數據。"),
            ],
            "非常好！根據您的使用需求，我建議您考慮月費$78的計劃，包括：\n- 10GB本地數據(4.5G網速)\n- 每月1GB中國內地及澳門數據\n- 24個月合約期間豁免行政費",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問中國內地數據計劃並進行價格協商",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！是的，我們最近推出了「42Mbps本地數據+中國內地及澳門數據流動通訊計劃」，有多個方案可供選擇。請問您平均每月會使用多少本地數據？"),
                new ChatMessageContent(AuthorRole.User, "我每月大概用10GB左右。另外我經常去內地，所以都需要內地數據。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "非常好！根據您的使用需求，我建議您考慮月費$78的計劃，包括：\n- 10GB本地數據(4.5G網速)\n- 每月1GB中國內地及澳門數據\n- 24個月合約期間豁免行政費"),
                new ChatMessageContent(AuthorRole.User, "可以便宜一點嗎？而且1GB內地數據好像太少。"),
            ],
            "了解您的考慮。如果您需要更多內地數據，我們有$98的升級方案，提供：\n- 16GB本地數據\n- 每月2GB中國內地及澳門數據\n另外，現在簽約還可享有6個月免費HKBN SAFE服務(支援3個裝置)。",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問中國內地數據計劃並進行價格協商",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！是的，我們最近推出了「42Mbps本地數據+中國內地及澳門數據流動通訊計劃」，有多個方案可供選擇。請問您平均每月會使用多少本地數據？"),
                new ChatMessageContent(AuthorRole.User, "我每月大概用10GB左右。另外我經常去內地，所以都需要內地數據。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "非常好！根據您的使用需求，我建議您考慮月費$78的計劃，包括：\n- 10GB本地數據(4.5G網速)\n- 每月1GB中國內地及澳門數據\n- 24個月合約期間豁免行政費"),
                new ChatMessageContent(AuthorRole.User, "可以便宜一點嗎？而且1GB內地數據好像太少。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "了解您的考慮。如果您需要更多內地數據，我們有$98的升級方案，提供：\n- 16GB本地數據\n- 每月2GB中國內地及澳門數據\n另外，現在簽約還可享有6個月免費HKBN SAFE服務(支援3個裝置)。"),
                new ChatMessageContent(AuthorRole.User, "那如果我想要中國手機號碼呢？")
            ],
            "可以的。我們有包含中國手機號碼的專屬計劃：\n- $98方案：10GB本地數據 + 1GB中國內地數據 + 中國手機號碼\n- $118方案：16GB本地數據 + 2GB中國內地數據 + 中國手機號碼\n兩個計劃都包含3,000分鐘本地通話時間。",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "銷售人員引導客戶選擇較高價值方案的對話",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
            ],
            "您好！是的，我們現正推出針對經常往來中港兩地客戶的特別優惠計劃。請問您經常到內地嗎？",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "銷售人員引導客戶選擇較高價值方案的對話",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
                new ChatMessageContent(AuthorRole.Assistant, "您好！是的，我們現正推出針對經常往來中港兩地客戶的特別優惠計劃。請問您經常到內地嗎？"),
                new ChatMessageContent(AuthorRole.User, "對，我每個月都會去內地工作一兩個星期。"),
            ],
            "明白了，作為經常往返內地的商務客戶，建議您考慮我們最受歡迎的全方位方案，現在有限時優惠：\n- 16GB本地數據（4.5G高速網絡）\n- 每月2GB中國內地及澳門數據\n- 贈送中國手機號碼\n月費只需$118，比市面上類似計劃更划算呢！",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "銷售人員引導客戶選擇較高價值方案的對話",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
                new ChatMessageContent(AuthorRole.Assistant, "您好！是的，我們現正推出針對經常往來中港兩地客戶的特別優惠計劃。請問您經常到內地嗎？"),
                new ChatMessageContent(AuthorRole.User, "對，我每個月都會去內地工作一兩個星期。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白了，作為經常往返內地的商務客戶，建議您考慮我們最受歡迎的全方位方案，現在有限時優惠：\n- 16GB本地數據（4.5G高速網絡）\n- 每月2GB中國內地及澳門數據\n- 贈送中國手機號碼\n月費只需$118，比市面上類似計劃更划算呢！"),
                new ChatMessageContent(AuthorRole.User, "這個價錢對我來說有點貴，有沒有便宜一點的計劃？"),
            ],
            "理解您想精打細算。不過讓我分析一下：如果您每月在內地逗留1-2星期，2GB內地數據其實最划算。選擇$78或$98的計劃雖然每月便宜$20-40，但：\n1. 內地數據只有1GB，很容易用完\n2. 沒有中國手機號碼，內地客戶聯絡您會比較不方便\n3. 超出用量收費會更貴\n\n現在選擇$118計劃，還可以享有：\n- 6個月免費HKBN SAFE服務\n- 豁免行政費（節省$28/月）\n- 3,000分鐘本地通話\n綜合這些優惠，實際上是最划算的選擇。",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "銷售人員引導客戶選擇較高價值方案的對話",
            [
                new ChatMessageContent(AuthorRole.User, "你好，聽說最近有新推出針對經常去中國內地的數據計劃？"),
                new ChatMessageContent(AuthorRole.Assistant, "您好！是的，我們現正推出針對經常往來中港兩地客戶的特別優惠計劃。請問您經常到內地嗎？"),
                new ChatMessageContent(AuthorRole.User, "對，我每個月都會去內地工作一兩個星期。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白了，作為經常往返內地的商務客戶，建議您考慮我們最受歡迎的全方位方案，現在有限時優惠：\n- 16GB本地數據（4.5G高速網絡）\n- 每月2GB中國內地及澳門數據\n- 贈送中國手機號碼\n月費只需$118，比市面上類似計劃更划算呢！"),
                new ChatMessageContent(AuthorRole.User, "這個價錢對我來說有點貴，有沒有便宜一點的計劃？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "理解您想精打細算。不過讓我分析一下：如果您每月在內地逗留1-2星期，2GB內地數據其實最划算。選擇$78或$98的計劃雖然每月便宜$20-40，但：\n1. 內地數據只有1GB，很容易用完\n2. 沒有中國手機號碼，內地客戶聯絡您會比較不方便\n3. 超出用量收費會更貴\n\n現在選擇$118計劃，還可以享有：\n- 6個月免費HKBN SAFE服務\n- 豁免行政費（節省$28/月）\n- 3,000分鐘本地通話\n綜合這些優惠，實際上是最划算的選擇。"),
                new ChatMessageContent(AuthorRole.User, "我再考慮一下其他計劃好了。")
            ],
            "完全明白您想好好比較。不如這樣，我先為您預留這個$118的優惠價30天，讓您比較後再決定。同時我也可以先為您計算一下不同計劃的實際使用支出，幫您找出最適合的方案。請問您的用量大約是怎樣的呢？",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "環球數據計劃間接議價場景",
            [
                new ChatMessageContent(AuthorRole.User, "最近想轉電訊公司呢...不知道現在有什麼適合經常出外公幹的計劃？"),
            ],
            "現在其實是轉台的好時機呢！我們剛推出了一些環球數據計劃，讓我先了解一下您平常主要去哪些地方？",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "環球數據計劃間接議價場景",
            [
                new ChatMessageContent(AuthorRole.User, "最近想轉電訊公司呢...不知道現在有什麼適合經常出外公幹的計劃？"),
                new ChatMessageContent(AuthorRole.Assistant, "現在其實是轉台的好時機呢！我們剛推出了一些環球數據計劃，讓我先了解一下您平常主要去哪些地方？"),
                new ChatMessageContent(AuthorRole.User, "主要是日本同歐洲那邊...但這類計劃通常都幾貴，而且我聽朋友說你們有些優惠？"),
            ],
            "啊！原來您也聽說了呢。是的，我們最近確實推出了一些很受歡迎的計劃。以月費$168的計劃為例，已經包括了本地20GB數據加上環球10GB數據，而且...要不要讓我跟您詳細介紹一下？",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "環球數據計劃間接議價場景",
            [
                new ChatMessageContent(AuthorRole.User, "最近想轉電訊公司呢...不知道現在有什麼適合經常出外公幹的計劃？"),
                new ChatMessageContent(AuthorRole.Assistant, "現在其實是轉台的好時機呢！我們剛推出了一些環球數據計劃，讓我先了解一下您平常主要去哪些地方？"),
                new ChatMessageContent(AuthorRole.User, "主要是日本同歐洲那邊...但這類計劃通常都幾貴，而且我聽朋友說你們有些優惠？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "啊！原來您也聽說了呢。是的，我們最近確實推出了一些很受歡迎的計劃。以月費$168的計劃為例，已經包括了本地20GB數據加上環球10GB數據，而且...要不要讓我跟您詳細介紹一下？"),
                new ChatMessageContent(AuthorRole.User, "嗯...其實我之前看過A公司的計劃，感覺比較相宜...不過聽說你們的網絡覆蓋比較好。")
            ],
            "謝謝您對我們網絡質素的認同！其實很多客戶最初都會這樣比較...不過他們後來都發現，如果計算埋我們這邊送的3個月pandapro會籍，還有KKday的旅遊折扣優惠，實際上反而更划算呢！而且我們可以為特別客戶提供一些額外驚喜...",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶以委婉方式表達對5G計劃價格的顧慮",
            [
                new ChatMessageContent(AuthorRole.User, "唉...最近物價那麼貴，我看看你們這個5G計劃... $168一個月喔？朋友都說現在其他地方好像比較便宜..."),
            ],
            "明白您在比較不同選擇。這個月我們正好有些獨特的優惠，這個$168計劃包含了20GB本地數據，再加上10GB環球漫遊數據，可以用在超過50個地方呢。",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶以委婉方式表達對5G計劃價格的顧慮",
            [
                new ChatMessageContent(AuthorRole.User, "唉...最近物價那麼貴，我看看你們這個5G計劃... $168一個月喔？朋友都說現在其他地方好像比較便宜..."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白您在比較不同選擇。這個月我們正好有些獨特的優惠，這個$168計劃包含了20GB本地數據，再加上10GB環球漫遊數據，可以用在超過50個地方呢。"),
                new ChatMessageContent(AuthorRole.User, "嗯...我cousin剛剛轉了另一家，好像平了很多。而且聽說你們的網絡...不知道是不是真的..."),
            ],
            "感謝您坦誠分享。或許我可以先了解您平常的使用習慣？這樣能夠幫您找到最合適的方案。另外，我們現在的網絡是由3HK提供，穩定性方面都有不錯的評價。",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶以委婉方式表達對5G計劃價格的顧慮",
            [
                new ChatMessageContent(AuthorRole.User, "唉...最近物價那麼貴，我看看你們這個5G計劃... $168一個月喔？朋友都說現在其他地方好像比較便宜..."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白您在比較不同選擇。這個月我們正好有些獨特的優惠，這個$168計劃包含了20GB本地數據，再加上10GB環球漫遊數據，可以用在超過50個地方呢。"),
                new ChatMessageContent(AuthorRole.User, "嗯...我cousin剛剛轉了另一家，好像平了很多。而且聽說你們的網絡...不知道是不是真的..."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您坦誠分享。或許我可以先了解您平常的使用習慣？這樣能夠幫您找到最合適的方案。另外，我們現在的網絡是由3HK提供，穩定性方面都有不錯的評價。"),
                new ChatMessageContent(AuthorRole.User, "其實我主要都是在市區用，但有時候用量會比較大...不過這價錢還是讓我有點猶豫呢...")
            ],
            "這樣的話，我建議您可以考慮一下我們的優惠組合。除了基本服務外，現在登記還可享有pandapro會籍優惠，還有旅遊折扣...不知道您最近有沒有打算外出旅遊？我可以跟您詳細分析一下這些額外福利的價值。",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對5G計劃價格表示不滿並質疑服務質素",
            [
                new ChatMessageContent(AuthorRole.User, "你們這個5G 20GB的計劃也太貴了吧？一個月要$168，根本就是在搶錢。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "理解您對價格的考慮。這個$168的計劃除了20GB本地數據外，還包含每月10GB環球旅遊數據，覆蓋超過50個熱門地區，而且合約期內豁免行政費。"),
                new ChatMessageContent(AuthorRole.User, "其他電訊商都在打折，你們卻這麼貴。而且我聽說你們網絡覆蓋很差，根本不值這個價錢！"),
            ],
            "感謝您的意見。我們的網絡由3HK提供支援，覆蓋穩定。另外，如果您現在登記，可以享受以下優惠：\n1. 高達3個月pandapro會籍（價值$297）\n2. KKday旅遊產品折扣優惠\n3. pandapro一年會籍75折\n要不要讓我為您詳細介紹這些優惠？",
            SourceFilenames: mobilePlanDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對5G計劃價格表示不滿並質疑服務質素",
            [
                new ChatMessageContent(AuthorRole.User, "你們這個5G 20GB的計劃也太貴了吧？一個月要$168，根本就是在搶錢。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "理解您對價格的考慮。這個$168的計劃除了20GB本地數據外，還包含每月10GB環球旅遊數據，覆蓋超過50個熱門地區，而且合約期內豁免行政費。"),
                new ChatMessageContent(AuthorRole.User, "其他電訊商都在打折，你們卻這麼貴。而且我聽說你們網絡覆蓋很差，根本不值這個價錢！"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您的意見。我們的網絡由3HK提供支援，覆蓋穩定。另外，如果您現在登記，可以享受以下優惠：\n1. 高達3個月pandapro會籍（價值$297）\n2. KKday旅遊產品折扣優惠\n3. pandapro一年會籍75折\n要不要讓我為您詳細介紹這些優惠？"),
                new ChatMessageContent(AuthorRole.User, "那如果我超過20GB用量會怎樣？到時候是不是還要加錢？")
            ],
            "超出20GB後，數據速度會被調整至不高於1Mbps，但不會額外收費。您也可以考慮升級到30GB計劃，每月只需多付$30，提供更大用量，讓您使用更安心。",
            SourceFilenames: mobilePlanDocuments);
    }

    public static IEnumerable<ChatEvalQuestion> GetHkbnHandsOffTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/HKBN/",
            SleekflowCompanyId: "b6d7e442-38ae-4b9a-b100-2951729768bc");

        var mobilePlanDocuments =
            new List<string>
            {
                "RS_sales briefing_N mobile_42mbps $78 $98 China data_20241204.docx.md",
                "RS_sales briefing_N mobile_APAC_20241204.docx.md",
                "RS_sales briefing_tri-area_20241204.docx.md",
                "RS_sales briefing_WW plan_20241204.docx.md"
            };

        // Test Cases for "human" Scenario
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶明確請求與真人客服對話",
            [
                new ChatMessageContent(AuthorRole.User, "我想跟真人客服講話。"),
            ],
            "好的，我馬上為您轉接真人客服。");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶在多次詢問後請求與真人客服對話",
            [
                new ChatMessageContent(AuthorRole.User, "你們的寬頻計劃有什麼優惠？"),
                new ChatMessageContent(AuthorRole.Assistant, "我們目前有多種寬頻計劃優惠，包括..."),
                new ChatMessageContent(AuthorRole.User, "我不太明白，能解釋一下嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "當然，請問您對哪一部分有疑問？"),
                new ChatMessageContent(AuthorRole.User, "算了，我還是想跟真人講話。"),
            ],
            "我明白，我會馬上為您轉接真人客服。");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶請求投訴",
            [
                new ChatMessageContent(AuthorRole.User, "我要投訴你們的服務。"),
            ],
            "很抱歉我們的服務沒有達到您的期望。我會將您轉接給客服代表處理您的投訴。");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶請求與經理對話",
            [
                new ChatMessageContent(AuthorRole.User, "我想跟你的經理講話。"),
            ],
            "抱歉給您帶來麻煩。我會將此事轉交給經理處理。");

        // Test Cases for "drop_off" Scenario
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶表示已獲得所需資訊並希望結束對話",
            [
                new ChatMessageContent(AuthorRole.User, "謝謝，我已經得到我需要的資訊了。"),
            ],
            "不客氣！如果您有其他問題，隨時歡迎再聯繫。祝您有愉快的一天！");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶在了解計劃後表示不感興趣",
            [
                new ChatMessageContent(AuthorRole.User, "你們的寬頻計劃多少錢？"),
                new ChatMessageContent(AuthorRole.Assistant, "我們的寬頻計劃起價為每月$199。"),
                new ChatMessageContent(AuthorRole.User, "有沒有更便宜的？"),
                new ChatMessageContent(AuthorRole.Assistant, "目前最低價格是$199，但我們有其他優惠..."),
                new ChatMessageContent(AuthorRole.User, "不了，謝謝，我不感興趣。"),
            ],
            "我明白了。感謝您的查詢，如果您將來有需要，歡迎再聯繫。");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶明確表示不希望繼續對話",
            [
                new ChatMessageContent(AuthorRole.User, "請停止這個對話。"),
            ],
            "好的，我會結束這次對話。祝您有愉快的一天。");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶表示對服務不感興趣",
            [
                new ChatMessageContent(AuthorRole.User, "我對這個服務不感興趣。"),
            ],
            "感謝您的反饋。如果您將來有興趣，我們隨時歡迎您的查詢。");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對5G計劃價格表示不滿並質疑服務質素",
            [
                new ChatMessageContent(AuthorRole.User, "你們這個5G 20GB的計劃也太貴了吧？一個月要$168，根本就是在搶錢。"),
            ],
            "理解您對價格的考慮。這個$168的計劃除了20GB本地數據外，還包含每月10GB環球旅遊數據，覆蓋超過50個熱門地區，而且合約期內豁免行政費。",
            SourceFilenames: mobilePlanDocuments);

        yield return new ChatEvalQuestion(
            testConfig,
            "Conversation mostly about healthcare",
            [
                new ChatMessageContent(AuthorRole.User, "I'm interested in your healthcare plans."),
                new ChatMessageContent(AuthorRole.Assistant, "We have two healthcare plans: Basic and Advanced."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "What are the benefits of your healthcare services? 算了，我還是想跟真人講話。")
            ],
            "healthcare");

        yield return new ChatEvalQuestion(
            testConfig,
            "Conversation mostly about mobile",
            [
                new ChatMessageContent(AuthorRole.User, "Do you have any mobile phone deals?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We don't have any current deals, but we offer a range of mobile plans."),
                new ChatMessageContent(AuthorRole.User, "算了，我還是想跟真人講話。")
            ],
            "mobile");

        yield return new ChatEvalQuestion(
            testConfig,
            "Mixed contexts, no majority",
            [
                new ChatMessageContent(AuthorRole.User, "I need help with my account."),
                new ChatMessageContent(AuthorRole.User, "Also, do you have healthcare options?"),
                new ChatMessageContent(AuthorRole.User, "And what about mobile plans?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I can help you with your account. For healthcare options, we have Basic and Advanced plans. For mobile plans, we offer a range of options."),
                new ChatMessageContent(AuthorRole.User, "算了，我還是想跟真人講話。")
            ],
            "supervisor");
    }
}