using System.Diagnostics;
using MassTransit;
using Sleekflow.EmailHub.Models.OnPremise.Events;
using Sleekflow.EmailHub.OnPremise.Communications;
using Sleekflow.Locks;

namespace Sleekflow.EmailHub.OnPremise.Consumers;

public class OnOnPremiseSyncTriggeredEventConsumerDefinition : ConsumerDefinition<OnOnPremiseSyncTriggeredEventConsumer>
{
    public const int LockDuration = 30;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnOnPremiseSyncTriggeredEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(30);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnOnPremiseSyncTriggeredEventConsumer : IConsumer<OnOnPremiseSyncAllEmailsTriggeredEvent>
{
    private readonly IOnPremiseCommunicationService _onPremiseCommunicationService;
    private readonly ILogger<OnOnPremiseSyncTriggeredEventConsumer> _logger;
    private readonly ILockService _lockService;

    public OnOnPremiseSyncTriggeredEventConsumer(
        IOnPremiseCommunicationService onPremiseCommunicationService,
        ILogger<OnOnPremiseSyncTriggeredEventConsumer> logger,
        ILockService lockService)
    {
        _onPremiseCommunicationService = onPremiseCommunicationService;
        _logger = logger;
        _lockService = lockService;
    }

    public async Task Consume(ConsumeContext<OnOnPremiseSyncAllEmailsTriggeredEvent> context)
    {
        var onPremiseSyncEvent = context.Message;
        var cancellationToken = context.CancellationToken;
        var @lock = await _lockService.LockAsync(
            new[]
            {
                $"{onPremiseSyncEvent.EmailAddress}",
                "OnPremiseSyncLock"
            },
            TimeSpan.FromSeconds(
                60 * OnOnPremiseSyncTriggeredEventConsumerDefinition.LockDuration),
            cancellationToken);
        if (@lock is null)
        {
            return;
        }

        try
        {
            _logger.LogInformation(
                "[OnPremiseFullSyncEventConsumer]: OnPremise Full Sync starts at {time}: emailAddress {emailAddress}",
                DateTime.UtcNow,
                onPremiseSyncEvent.EmailAddress);
            var watch = new Stopwatch();
            watch.Start();
            await _onPremiseCommunicationService.SyncAllEmailsAsync(
                onPremiseSyncEvent.EmailAddress,
                cancellationToken);
            watch.Stop();
            _logger.LogInformation(
                "[OnPremiseFullSyncEventConsumer]: OnPremise Full Sync Successes: emailAddress {emailAddress}: time used: {time}",
                onPremiseSyncEvent.EmailAddress,
                watch.Elapsed);
        }
        catch (Exception e)
        {
            _logger.LogInformation("[OnPremiseFullSyncEventConsumer]: fails due to {e}", e);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}