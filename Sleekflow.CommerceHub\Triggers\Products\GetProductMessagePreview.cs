using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Models.Renderings;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.TemplateRenderers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class GetProductMessagePreview
    : ITrigger<
        GetProductMessagePreview.GetProductMessagePreviewInput,
        GetProductMessagePreview.GetProductMessagePreviewOutput>
{
    private readonly IProductVariantService _productVariantService;
    private readonly IProductService _productService;
    private readonly ITemplateRenderer _templateRenderer;
    private readonly ICategoryService _categoryService;
    private readonly IStoreService _storeService;

    public GetProductMessagePreview(
        IProductVariantService productVariantService,
        IProductService productService,
        ITemplateRenderer templateRenderer,
        ICategoryService categoryService,
        IStoreService storeService)
    {
        _productVariantService = productVariantService;
        _productService = productService;
        _templateRenderer = templateRenderer;
        _categoryService = categoryService;
        _storeService = storeService;
    }

    public class GetProductMessagePreviewInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("product_variant_id")]
        public string ProductVariantId { get; set; }

        [Required]
        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [Required]
        [ValidateIsoCurrencyCode]
        [JsonProperty("currency_iso_code")]
        public string CurrencyIsoCode { get; set; }

        [Required]
        [ValidateIsoLanguageCode]
        [JsonProperty("language_iso_code")]
        public string LanguageIsoCode { get; set; }

        [JsonConstructor]
        public GetProductMessagePreviewInput(
            string sleekflowCompanyId,
            string storeId,
            string productVariantId,
            string productId,
            string currencyIsoCode,
            string languageIsoCode)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductVariantId = productVariantId;
            ProductId = productId;
            CurrencyIsoCode = currencyIsoCode;
            LanguageIsoCode = languageIsoCode;
        }
    }

    public class GetProductMessagePreviewOutput
    {
        [JsonProperty("message_preview")]
        public RenderedTemplate MessagePreview { get; set; }

        [JsonConstructor]
        public GetProductMessagePreviewOutput(
            RenderedTemplate messagePreview)
        {
            MessagePreview = messagePreview;
        }
    }

    public async Task<GetProductMessagePreviewOutput> F(GetProductMessagePreviewInput getProductMessagePreviewInput)
    {
        var sleekflowCompanyId = getProductMessagePreviewInput.SleekflowCompanyId;
        var storeId = getProductMessagePreviewInput.StoreId;
        var languageIsoCode = getProductMessagePreviewInput.LanguageIsoCode;

        var store = await _storeService.GetStoreAsync(
            storeId,
            sleekflowCompanyId);
        var messagePreviewTemplateStr = store.TemplateDict
            .MessagePreviewTemplates
            .Find(t => t.LanguageIsoCode == languageIsoCode);
        var language = store.Languages.First(l => l.IsDefault);

        var product =
            await _productService.GetProductAsync(getProductMessagePreviewInput.ProductId, sleekflowCompanyId, storeId);
        var categories =
            await _categoryService.GetCategoriesAsync(sleekflowCompanyId, storeId, 200, product.CategoryIds);
        var productVariant = await _productVariantService.GetProductVariantAsync(
            getProductMessagePreviewInput.ProductVariantId,
            storeId,
            sleekflowCompanyId,
            getProductMessagePreviewInput.ProductId);

        var messagePreviewRenderedTemplate = await _templateRenderer.RenderProductVariantTemplateAsync(
            messagePreviewTemplateStr == null ? string.Empty : messagePreviewTemplateStr.Value,
            new ProductVariantRenderingDto(
                productVariant,
                store,
                product,
                categories,
                new LanguageOption(languageIsoCode, language.LanguageIsoCode)));

        return new GetProductMessagePreviewOutput(messagePreviewRenderedTemplate);
    }
}