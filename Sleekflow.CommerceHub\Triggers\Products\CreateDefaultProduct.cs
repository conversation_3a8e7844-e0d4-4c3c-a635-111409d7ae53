﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class CreateDefaultProduct
    : ITrigger<
        CreateDefaultProduct.CreateDefaultProductInput,
        CreateDefaultProduct.CreateDefaultProductOutput>
{
    private readonly IDefaultProductService _defaultProductService;

    public CreateDefaultProduct(
        IDefaultProductService defaultProductService)
    {
        _defaultProductService = defaultProductService;
    }

    public class CreateDefaultProductInput : ProductInput, IHasSleekflowStaff
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty(ProductVariant.PropertyNamePrices)]
        public List<Price> Prices { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty(ProductVariant.PropertyNameAttributes)]
        public List<ProductVariant.ProductVariantAttribute> Attributes { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateDefaultProductInput(
            List<string> categoryIds,
            string? sku,
            string? url,
            List<Multilingual> names,
            List<Description> descriptions,
            List<ImageDto> images,
            Dictionary<string, object?> metadata,
            string sleekflowCompanyId,
            string storeId,
            List<Price> prices,
            List<ProductVariant.ProductVariantAttribute> attributes,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(categoryIds, sku, url, names, descriptions, images, metadata)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            Prices = prices;
            Attributes = attributes;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateDefaultProductOutput
    {
        [JsonProperty("product")]
        public ProductDto Product { get; set; }

        [JsonConstructor]
        public CreateDefaultProductOutput(ProductDto product)
        {
            Product = product;
        }
    }

    public async Task<CreateDefaultProductOutput> F(CreateDefaultProductInput createDefaultProductInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createDefaultProductInput.SleekflowStaffId,
            createDefaultProductInput.SleekflowStaffTeamIds);

        var (product, productVariants) = await _defaultProductService.CreateDefaultProductAsync(
            createDefaultProductInput,
            createDefaultProductInput.SleekflowCompanyId,
            createDefaultProductInput.StoreId,
            sleekflowStaff,
            createDefaultProductInput.Prices,
            createDefaultProductInput.Attributes);

        return new CreateDefaultProductOutput(new ProductDto(product, productVariants));
    }
}