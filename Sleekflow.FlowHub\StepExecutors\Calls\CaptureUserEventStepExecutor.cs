﻿using MassTransit;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICaptureUserEventStepExecutor : IStepExecutor
{
}

public class CaptureUserEventStepExecutor
    : GeneralStepExecutor<CallStep<CaptureUserEventStepArgs>>,
        ICaptureUserEventStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IEventHubProducerProvider _eventHubProducerProvider;
    private readonly IIdService _idService;

    public CaptureUserEventStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IEventHubProducerProvider eventHubProducerProvider,
        IIdService idService)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _eventHubProducerProvider = eventHubProducerProvider;
        _idService = idService;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var producer =
                await _eventHubProducerProvider.GetProducer(EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent);
            var onUserEventHappenedEventHubEvent = await GetOnUserEventHappenedEventHubEvent(callStep, state);
            await producer.Produce(onUserEventHappenedEventHubEvent);

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<OnUserEventHappenedEventHubEvent> GetOnUserEventHappenedEventHubEvent(
        CallStep<CaptureUserEventStepArgs> callStep,
        ProxyState state)
    {
        var eventType = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.EventTypeExpr) ??
                                  callStep.Args.EventTypeExpr);
        var objectId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ObjectIdExpr) ??
                                 callStep.Args.ObjectIdExpr);
        var objectType = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ObjectTypeExpr) ??
                                   callStep.Args.ObjectTypeExpr);
        var source = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.SourceExpr) ??
                               "sleekflow.flowhub");
        var campaignId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.CampaignIdExpr) ??
                                   callStep.Args.CampaignIdExpr);

        var sleekflowCompanyId = state.Identity.SleekflowCompanyId;

        return new OnUserEventHappenedEventHubEvent(
            _idService.GetId(SysTypeNames.UserEventHubUserEvent),
            eventType,
            sleekflowCompanyId,
            objectId,
            objectType,
            source,
            new OnUserEventHappenedEventHubEventProperties(campaignId),
            new OnUserEventHappenedEventHubEventMetadata(
                state.Identity.WorkflowId,
                state.Identity.WorkflowVersionedId,
                null,
                null,
                null,
                null));
    }
}