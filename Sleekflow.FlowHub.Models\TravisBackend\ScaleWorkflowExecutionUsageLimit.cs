using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Models.TravisBackend;

[SwaggerInclude]
public class ScaleWorkflowExecutionLimitInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonConstructor]
    public ScaleWorkflowExecutionLimitInput(string sleekflowCompanyId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
    }
}

[SwaggerInclude]
public class ScaleWorkflowExecutionLimitOutput
{
    [JsonProperty("is_success")]
    public bool IsSuccess { get; set; }

    [JsonConstructor]
    public ScaleWorkflowExecutionLimitOutput(bool isSuccess)
    {
        IsSuccess = isSuccess;
    }
}