using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.InternalIntegrationHub.Configs;

public interface ITravisDatabaseConfigs
{
    string EasDbConnectionString { get; }

    string EusDbConnectionString { get; }

    string SeasDbConnectionString { get; }

    string UaenDbConnectionString { get; }

    string WeuDbConnectionString { get; }
}

public class TravisDatabaseConfigs : IConfig, ITravisDatabaseConfigs
{
    public string EasDbConnectionString { get; }

    public string EusDbConnectionString { get; }

    public string SeasDbConnectionString { get; }

    public string UaenDbConnectionString { get; }

    public string WeuDbConnectionString { get; }

    public TravisDatabaseConfigs()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        EasDbConnectionString =
            Environment.GetEnvironmentVariable("EAS_TRAVIS_DATABASE_CONNECTION_STRING", target)
            ?? throw new SfMissingEnvironmentVariableException("EAS_TRAVIS_DATABASE_CONNECTION_STRING");

        EusDbConnectionString =
            Environment.GetEnvironmentVariable("EUS_TRAVIS_DATABASE_CONNECTION_STRING", target)
            ?? throw new SfMissingEnvironmentVariableException("EUS_TRAVIS_DATABASE_CONNECTION_STRING");

        SeasDbConnectionString =
            Environment.GetEnvironmentVariable("SEAS_TRAVIS_DATABASE_CONNECTION_STRING", target)
            ?? throw new SfMissingEnvironmentVariableException("SEAS_TRAVIS_DATABASE_CONNECTION_STRING");

        UaenDbConnectionString =
            Environment.GetEnvironmentVariable("UAEN_TRAVIS_DATABASE_CONNECTION_STRING", target)
            ?? throw new SfMissingEnvironmentVariableException("UAEN_TRAVIS_DATABASE_CONNECTION_STRING");

        WeuDbConnectionString =
            Environment.GetEnvironmentVariable("WEU_TRAVIS_DATABASE_CONNECTION_STRING", target)
            ?? throw new SfMissingEnvironmentVariableException("WEU_TRAVIS_DATABASE_CONNECTION_STRING");
    }
}