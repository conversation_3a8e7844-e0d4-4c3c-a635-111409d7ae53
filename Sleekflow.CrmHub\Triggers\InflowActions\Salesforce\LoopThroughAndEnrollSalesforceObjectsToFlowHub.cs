using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Salesforce;

[TriggerGroup(TriggerGroups.InflowActions)]
public class LoopThroughAndEnrollSalesforceObjectsToFlowHub : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public LoopThroughAndEnrollSalesforceObjectsToFlowHub(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class LoopThroughAndEnrollSalesforceObjectsToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSalesforceObjectsToFlowHubInput(
            string sleekflowCompanyId,
            string salesforceConnectionId,
            string entityTypeName,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId,
            bool isCustomObject)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SalesforceConnectionId = salesforceConnectionId;
            EntityTypeName = entityTypeName;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
            IsCustomObject = isCustomObject;
        }
    }

    public class LoopThroughAndEnrollSalesforceObjectsToFlowHubOutput
    {
        [JsonProperty("loop_through_objects_progress_state_id")]
        public string LoopThroughObjectsProgressStateId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSalesforceObjectsToFlowHubOutput(string loopThroughObjectsProgressStateId)
        {
            LoopThroughObjectsProgressStateId = loopThroughObjectsProgressStateId;
        }
    }

    public async Task<LoopThroughAndEnrollSalesforceObjectsToFlowHubOutput> F(
        LoopThroughAndEnrollSalesforceObjectsToFlowHubInput loopThroughAndEnrollSalesforceObjectsToFlowHubInput)
    {
        var salesforceProviderService = _providerSelector.GetProviderService(
            "salesforce-integrator");

        var sleekflowCompanyId = loopThroughAndEnrollSalesforceObjectsToFlowHubInput.SleekflowCompanyId;
        var salesforceConnectionId = loopThroughAndEnrollSalesforceObjectsToFlowHubInput.SalesforceConnectionId;
        var entityTypeName = loopThroughAndEnrollSalesforceObjectsToFlowHubInput.EntityTypeName;
        var flowHubWorkflowId = loopThroughAndEnrollSalesforceObjectsToFlowHubInput.FlowHubWorkflowId;
        var flowHubWorkflowVersionedId = loopThroughAndEnrollSalesforceObjectsToFlowHubInput.FlowHubWorkflowVersionedId;
        var isCustomObject = loopThroughAndEnrollSalesforceObjectsToFlowHubInput.IsCustomObject;

        await salesforceProviderService.TerminateInProgressLoopThroughExecutionsAsync(
            flowHubWorkflowId,
            sleekflowCompanyId);

        var output = await salesforceProviderService.LoopThroughAndEnrollObjectsToFlowHubAsync(
            sleekflowCompanyId,
            salesforceConnectionId,
            entityTypeName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            isCustomObject);

        return new LoopThroughAndEnrollSalesforceObjectsToFlowHubOutput(output.LoopThroughObjectsProgressStateId);
    }
}