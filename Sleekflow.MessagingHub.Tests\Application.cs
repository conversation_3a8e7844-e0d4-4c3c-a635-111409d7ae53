using System.Text;
using System.Xml;
using Alba;
using GraphApi.Client.ApiClients;
using GraphApi.Client.Models.MessageObjects;
using GraphApi.Client.Models.MessageObjects.TemplateObjects;
using GraphApi.Client.Payloads;
using Microsoft.ApplicationInsights.AspNetCore.Extensions;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Newtonsoft.Json;
using Sleekflow.MessagingHub.Tests.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis;
using Sleekflow.Mvc.Tests;
using Formatting = Newtonsoft.Json.Formatting;

namespace Sleekflow.MessagingHub.Tests;

[SetUpFixture]
public class Application
{
    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.MessagingHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        Host = await AlbaHost.For<Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(
                    services =>
                    {
                        services.AddSingleton<ICloudApiClients, MockCloudApiClients>();
                    });
            });

        Host.AfterEachAsync(
            async context =>
            {
                await BaseTestHost.InterceptAfterEachAsync(context);
            });
    }

    private class MockCloudApiClients : ICloudApiClients
    {
        public IWhatsappCloudApiBspClient WhatsappCloudApiBspClient { get; set; }

        public IWhatsappCloudApiAuthenticationClient WhatsappCloudApiAuthenticationClient { get; set; }

        public IWhatsappCloudApiMessagingClient WhatsappCloudApiMessagingClient { get; set; }

        public IWhatsappCloudApiConversationalAutomationClient WhatsappCloudApiConversationalAutomationClient { get; }

        public IWhatsappCloudApiFlowClient WhatsappCloudApiFlowClient { get; set; }

        public IFacebookCommerceClient FacebookCommerceClient { get; set; }

        public IMetaConversionApiClient MetaConversionApiClient { get; set; }

        public IWhatsappMMLiteApiClient WhatsappMMLiteApiClient { get; set; }

        public MockCloudApiClients(IHttpClientFactory httpClientFactory)
        {
            WhatsappCloudApiBspClient =
                new WhatsappCloudApiBspClient(
                    Secrets.FacebookSystemUserAccessToken,
                    httpClientFactory.CreateClient("default-handler"));
            WhatsappCloudApiAuthenticationClient =
                new WhatsappCloudApiAuthenticationClient(
                    Secrets.FacebookSystemUserAccessToken,
                    httpClientFactory.CreateClient("default-handler"));
            WhatsappCloudApiConversationalAutomationClient=
                new WhatsappCloudApiConversationalAutomationClient(
                    Secrets.FacebookSystemUserAccessToken,
                    httpClientFactory.CreateClient("default-handler"));
            WhatsappCloudApiFlowClient = new WhatsappCloudApiFlowClient(
                Secrets.FacebookSystemUserAccessToken,
                httpClientFactory.CreateClient("default-handler"));
            FacebookCommerceClient = new FacebookCommerceClient(
                Secrets.FacebookSystemUserAccessToken,
                httpClientFactory.CreateClient("default-handler"));

            MetaConversionApiClient = new MetaConversionApiClient(
                Secrets.FacebookSystemUserAccessToken,
                httpClientFactory.CreateClient("default-handler"));

            var mockWhatsappCloudApiMessagingClient = new Mock<IWhatsappCloudApiMessagingClient>();
            mockWhatsappCloudApiMessagingClient
                .Setup(
                    m =>
                        m.SendMessageAsync(
                            It.IsAny<string>(),
                            It.IsAny<WhatsappCloudApiMessageObject>(),
                            It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult((SendMessageResponse) null!));

            mockWhatsappCloudApiMessagingClient
                .Setup(
                    m =>
                        m.CreateMessageTemplateAsync(
                            It.IsAny<string>(),
                            It.IsAny<WhatsappCloudApiCreateTemplateObject>(),
                            It.IsAny<CancellationToken>()))
                .Returns(Task.FromResult((CreateMessageTemplateResponse) null!));
            WhatsappCloudApiMessagingClient = mockWhatsappCloudApiMessagingClient.Object;
        }
    }

    public static IAlbaHost Host { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        Host.Dispose();
    }

    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }
}