﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.SchemafulObjects.FileProcessors;

public class MyReaderEntry
{
    [JsonProperty("error_message")]
    public string? ErrorMessage { get; set; }

    [JsonProperty("row")]
    public int Row { get; }

    [JsonProperty("schemaful_object")]
    public MyReaderSchemafulObjectDto? SchemafulObject { get; set; }

    [JsonConstructor]
    public MyReaderEntry(
        string? errorMessage,
        MyReaderSchemafulObjectDto? schemafulObject,
        int row)
    {
        ErrorMessage = errorMessage;
        SchemafulObject = schemafulObject;
        Row = row;
    }
}