using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Models.Workflows;

public class WorkflowListDto : AuditEntityDto
{
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("workflow_type")]
    public string WorkflowType { get; set; }

    [JsonProperty("workflow_group_id")]
    public string? WorkflowGroupId { get; set; }

    [JsonProperty("triggers")]
    public WorkflowTriggers Triggers { get; set; }

    [JsonProperty("workflow_enrollment_settings")]
    public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

    [Json<PERSON>roperty("workflow_schedule_settings")]
    public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

    [JsonProperty("activation_status")]
    public string ActivationStatus { get; set; }

    [JsonProperty("version")]
    public string Version { get; set; }

    [JsonProperty("manual_enrollment_source")]
    public string? ManualEnrollmentSource { get; set; }

    [JsonConstructor]
    public WorkflowListDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string workflowId,
        string workflowVersionedId,
        string name,
        string workflowType,
        string? workflowGroupId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings workflowEnrollmentSettings,
        WorkflowScheduleSettings workflowScheduleSettings,
        string activationStatus,
        string version,
        string? manualEnrollmentSource)
        : base(id, sleekflowCompanyId, createdBy, updatedBy, createdAt, updatedAt)
    {
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        Name = name;
        WorkflowType = workflowType;
        WorkflowGroupId = workflowGroupId;
        Triggers = triggers;
        WorkflowEnrollmentSettings = workflowEnrollmentSettings;
        WorkflowScheduleSettings = workflowScheduleSettings;
        ActivationStatus = activationStatus;
        Version = version;
        ManualEnrollmentSource = manualEnrollmentSource;
    }

    public WorkflowListDto(LightWeightProxyWorkflow workflow)
        : this(
            workflow.Id,
            workflow.SleekflowCompanyId,
            workflow.CreatedBy,
            workflow.UpdatedBy,
            workflow.CreatedAt,
            workflow.UpdatedAt,
            workflow.WorkflowId,
            workflow.WorkflowVersionedId,
            workflow.Name,
            workflow.WorkflowType,
            workflow.WorkflowGroupId,
            workflow.Triggers,
            workflow.WorkflowEnrollmentSettings,
            workflow.WorkflowScheduleSettings,
            workflow.ActivationStatus,
            workflow.Version,
            workflow.ManualEnrollmentSource)
    {
    }
}