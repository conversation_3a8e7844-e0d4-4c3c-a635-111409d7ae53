using System;
using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class RepositoryInConstructorAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1002";
    public const string Category = "Usage";

    private static readonly LocalizableString Title = "Repository injection detected";

    private static readonly LocalizableString MessageFormat =
        "Injection of IRepository implementation detected in class implementing ITrigger interface";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics => ImmutableArray.Create(Rule);

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSyntaxNodeAction(AnalyzeConstructorDeclaration, SyntaxKind.ConstructorDeclaration);
    }

    private void AnalyzeConstructorDeclaration(SyntaxNodeAnalysisContext context)
    {
        var constructorDeclaration = (ConstructorDeclarationSyntax) context.Node;
        var classDeclaration = constructorDeclaration.Parent as ClassDeclarationSyntax;

        if (classDeclaration == null)
        {
            return;
        }

        var classSymbol = context.SemanticModel.GetDeclaredSymbol(classDeclaration);
        if (classSymbol == null || !classSymbol.AllInterfaces.Any(i => i.Name == "ITrigger"))
        {
            return;
        }

        foreach (var parameter in constructorDeclaration.ParameterList.Parameters)
        {
            if (parameter.Type == null)
            {
                continue;
            }

            var parameterType = context.SemanticModel.GetTypeInfo(parameter.Type).Type;

            if (parameterType != null && parameterType.Name.EndsWith("Repository", StringComparison.Ordinal))
            {
                var diagnostic = Diagnostic.Create(Rule, constructorDeclaration.Identifier.GetLocation());
                context.ReportDiagnostic(diagnostic);
                break;
            }
        }
    }
}