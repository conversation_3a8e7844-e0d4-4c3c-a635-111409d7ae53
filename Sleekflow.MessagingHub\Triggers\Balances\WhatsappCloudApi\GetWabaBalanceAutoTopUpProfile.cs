using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopUpProfiles;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GetWabaBalanceAutoTopUpProfile
    : ITrigger<
        GetWabaBalanceAutoTopUpProfile.GetWabaBalanceAutoTopUpProfileInput,
        GetWabaBalanceAutoTopUpProfile.GetWabaBalanceAutoTopUpProfileOutput>
{
    private readonly IWabaBalanceAutoTopUpProfileService _wabaBalanceAutoTopUpProfileService;

    public GetWabaBalanceAutoTopUpProfile(
        IWabaBalanceAutoTopUpProfileService wabaBalanceAutoTopUpProfileService)
    {
        _wabaBalanceAutoTopUpProfileService = wabaBalanceAutoTopUpProfileService;
    }

    [method: JsonConstructor]
    public class GetWabaBalanceAutoTopUpProfileInput(
        string facebookBusinessId,
        string? facebookWabaId)
    {
        [JsonProperty("facebook_business_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookBusinessId { get; set; } = facebookBusinessId;

        [JsonProperty("facebook_waba_id")]
        public string? FacebookWabaId { get; set; } = facebookWabaId;
    }

    [method: JsonConstructor]
    public class GetWabaBalanceAutoTopUpProfileOutput(List<WabaBalanceAutoTopUpProfile> wabaAutoTopUpProfiles)
    {
        [JsonProperty("business_balance_auto_top_up_profiles")]
        public List<WabaBalanceAutoTopUpProfile> WabaAutoTopUpProfiles { get; set; } = wabaAutoTopUpProfiles;
    }

    public async Task<GetWabaBalanceAutoTopUpProfileOutput> F(
        GetWabaBalanceAutoTopUpProfileInput input)
    {
        if (input.FacebookWabaId == null)
        {
           return new GetWabaBalanceAutoTopUpProfileOutput(await _wabaBalanceAutoTopUpProfileService.GetWithFacebookBusinessIdAsync(input.FacebookBusinessId));
        }

        var result = new List<WabaBalanceAutoTopUpProfile>();

        var profile =
            await _wabaBalanceAutoTopUpProfileService.GetWithFacebookBusinessAndWabaIdAsync(input.FacebookBusinessId, input.FacebookWabaId);

        if (profile != null)
        {
            result.Add(profile);
        }

        return new GetWabaBalanceAutoTopUpProfileOutput(result);
    }
}