﻿using Sleekflow.Events;

namespace Sleekflow.CrmHub.Models.Events;

public class OnProviderTypeSyncInitializedEvent : IEvent
{
    public string SleekflowCompanyId { get; set; }

    public string ProviderName { get; set; }

    public string EntityTypeSync { get; set; }

    public OnProviderTypeSyncInitializedEvent(
        string sleekflowCompanyId,
        string providerName,
        string entityTypeSync)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ProviderName = providerName;
        EntityTypeSync = entityTypeSync;
    }
}