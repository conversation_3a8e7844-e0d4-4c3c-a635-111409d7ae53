﻿using Microsoft.AspNetCore.Http;
using Sleekflow.FlowHub.Triggers.NeedConfigs;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class NeedConfigIntegrationTests
{
    [Test]
    public async Task GetActionsTest()
    {
        // Arrange
        var getActionsInput = new GetActions.GetActionsInput("v1");

        // Act
        var getActionsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getActionsInput).ToUrl("/NeedConfigs/GetActions");
            });

        var getActionsOutput = await getActionsScenarioResult
            .ReadAsJsonAsync<Output<GetActions.GetActionsOutput>>();

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(getActionsOutput, Is.Not.Null);
                Assert.That(getActionsOutput.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(getActionsOutput.Data, Is.Not.Null);
                Assert.That(getActionsOutput.Data.Actions, Is.Not.Empty);
            });
    }

    [Test]
    public async Task GetActionNeedsTest()
    {
        // Arrange
        const string actionGroup = "messaging";
        const string actionSubgroup = "text";
        const string version = "v1";
        const string companyId = "test";

        var getActionNeedsInput = new GetActionNeeds.GetActionNeedsInput(
            companyId,
            actionGroup,
            actionSubgroup,
            version,
            null);

        var secondGetActionNeedsInput = new GetActionNeeds.GetActionNeedsInput(
            companyId,
            actionGroup,
            actionSubgroup,
            version,
            new Dictionary<string, object?>()
            {
                ["channel_type"] = "facebook"
            });

        // Act
        var getActionNeedsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getActionNeedsInput).ToUrl("/NeedConfigs/GetActionNeeds");
            });

        var getActionNeedsOutput = await getActionNeedsScenarioResult
            .ReadAsJsonAsync<Output<GetActionNeeds.GetActionNeedsOutput>>();

        var secondGetActionNeedsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(secondGetActionNeedsInput).ToUrl("/NeedConfigs/GetActionNeeds");
            });

        var secondGetActionNeedsOutput = await secondGetActionNeedsScenarioResult
            .ReadAsJsonAsync<Output<GetActionNeeds.GetActionNeedsOutput>>();

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(getActionNeedsOutput, Is.Not.Null);
                Assert.That(getActionNeedsOutput.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(getActionNeedsOutput.Data, Is.Not.Null);
                Assert.That(getActionNeedsOutput.Data.Needs, Is.Not.Empty);

                Assert.That(secondGetActionNeedsOutput, Is.Not.Null);
                Assert.That(secondGetActionNeedsOutput.HttpStatusCode, Is.EqualTo(StatusCodes.Status200OK));
                Assert.That(secondGetActionNeedsOutput.Data, Is.Not.Null);
                Assert.That(secondGetActionNeedsOutput.Data.Needs, Is.Not.Empty);

                Assert.That(secondGetActionNeedsOutput.Data.Needs, Has.Count.GreaterThan(getActionNeedsOutput.Data.Needs.Count));
            });
    }
}