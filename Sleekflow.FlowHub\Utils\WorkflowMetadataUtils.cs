using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows.Triggers;

namespace Sleekflow.FlowHub.Utils;

/// <summary>
/// Utility class for working with workflow metadata.
/// </summary>
public static class WorkflowMetadataUtils
{
    /// <summary>
    /// Attempts to extract the EventMetadata from workflow metadata.
    /// </summary>
    /// <param name="metadata">The workflow metadata dictionary.</param>
    /// <returns>The EventMetadata if found, otherwise null.</returns>
    public static EventMetadata? TryExtractEventMetadataFromWorkflow(Dictionary<string, object?> metadata)
    {
        if (!metadata.TryGetValue("NS_v1", out var nsV1Object) ||
            nsV1Object == null ||
            !(nsV1Object.ToString() is string nsV1Json))
        {
            return null;
        }

        var nsV1 = JsonConvert.DeserializeObject<Dictionary<string, object>>(nsV1Json);
        if (nsV1 == null ||
            !nsV1.TryGetValue("trigger", out var triggerObject) ||
            triggerObject == null ||
            !(triggerObject.ToString() is string triggerJson))
        {
            return null;
        }

        var trigger = JsonConvert.DeserializeObject<Dictionary<string, object>>(triggerJson);
        if (trigger == null ||
            !trigger.TryGetValue("event", out var eventObject) ||
            eventObject == null ||
            !(eventObject.ToString() is string eventJson))
        {
            return null;
        }

        return JsonConvert.DeserializeObject<EventMetadata>(eventJson);
    }
}