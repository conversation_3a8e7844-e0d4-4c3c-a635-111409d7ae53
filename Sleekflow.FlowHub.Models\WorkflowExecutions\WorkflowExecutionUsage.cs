﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowExecutionUsage
{
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("total_execution_count")]
    public long TotalExecutionCount { get; set; }

    [JsonProperty("failed_execution_count")]
    public long FailedExecutionCount { get; set; }

    [JsonProperty("last_enrolled_at")]
    public DateTimeOffset? LastEnrolledAt { get; set; }

    [JsonConstructor]
    public WorkflowExecutionUsage(
        string workflowId,
        long totalExecutionCount,
        long failedExecutionCount,
        DateTimeOffset? lastEnrolledAt)
    {
        WorkflowId = workflowId;
        TotalExecutionCount = totalExecutionCount;
        FailedExecutionCount = failedExecutionCount;
        LastEnrolledAt = lastEnrolledAt;
    }
}