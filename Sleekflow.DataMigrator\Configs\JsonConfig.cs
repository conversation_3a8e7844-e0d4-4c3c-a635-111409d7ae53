﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Sleekflow.DataMigrator.Configs;

public static class JsonConfig
{
    public static readonly JsonSerializerSettings DefaultJsonSerializerSettings = new JsonSerializerSettings()
    {
        Converters = new List<JsonConverter>()
        {
            new IsoDateTimeConverter
            {
                DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
            }
        },
        ReferenceLoopHandling = ReferenceLoopHandling.Error,
        DateParseHandling = DateParseHandling.DateTimeOffset,
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        MaxDepth = 12,
    };
}