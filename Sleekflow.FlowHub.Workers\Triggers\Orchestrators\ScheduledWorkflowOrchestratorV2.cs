using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.FlowHub.Workers.Triggers.Activities;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class ScheduledWorkflowOrchestratorV2
{
    private readonly ILogger<ScheduledWorkflowOrchestratorV2> _logger;
    private readonly IWorkflowRecurringSettingsParser _workflowRecurringSettingsParser;

    private const int InitialBatchSize = 500;
    private const int MinBatchSize = 10; // Minimum batch size for retries

    public ScheduledWorkflowOrchestratorV2(
        ILogger<ScheduledWorkflowOrchestratorV2> logger,
        IWorkflowRecurringSettingsParser workflowRecurringSettingsParser)
    {
        _logger = logger;
        _workflowRecurringSettingsParser = workflowRecurringSettingsParser;
    }

    [Function("ScheduledWorkflow_Orchestrator_V2")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var scheduleWorkflowInput = context.GetInput<TriggerScheduleWorkflowInput>();

        _logger.LogInformation(
            "Starting data processing for company {SleekflowCompanyId} with workflow {WorkflowVersionedId}",
            scheduleWorkflowInput!.SleekflowCompanyId,
            scheduleWorkflowInput!.WorkflowVersionedId);

        try
        {
            var getScheduledWorkflowEnrolmentConditionOutput =
                await context.CallActivityAsync<GetScheduledWorkflowEnrolmentConditionOutput>(
                    "GetScheduledWorkflowEnrolmentCondition",
                    new GetScheduledWorkflowEnrolmentConditionInput(
                        scheduleWorkflowInput!.SleekflowCompanyId,
                        scheduleWorkflowInput.WorkflowVersionedId));

            DateTimeOffset? currentLastContactCreatedAt = null;
            string? currentLastContactId = null;
            var moreDataToProcess = true;
            var currentConfiguredBatchSize = InitialBatchSize; // This is the "target" batch size for a new segment

            while (moreDataToProcess)
            {
                var attemptBatchSize = currentConfiguredBatchSize;
                var segmentSuccessfullyProcessed = false;
                ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchOutput? activityOutput;

                // Inner loop for retrying the current segment with decreasing batch sizes
                while (!segmentSuccessfullyProcessed && attemptBatchSize >= MinBatchSize)
                {
                    var activityInput =
                        new ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchInput(
                            scheduleWorkflowInput.Origin,
                            scheduleWorkflowInput.SleekflowCompanyId,
                            currentLastContactCreatedAt, // Cursor for the current segment
                            currentLastContactId, // Cursor for the current segment
                            attemptBatchSize,
                            scheduleWorkflowInput.WorkflowId,
                            scheduleWorkflowInput.WorkflowVersionedId,
                            getScheduledWorkflowEnrolmentConditionOutput.WorkflowName,
                            getScheduledWorkflowEnrolmentConditionOutput.Condition);

                    try
                    {
                        _logger.LogInformation(
                            "Attempting to process contact batch for workflow {WorkflowVersionedId}. BatchSize: {AttemptBatchSize}, LastContactCreatedAt: {LastContactCreatedAt}, LastContactId: {LastContactId}",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        activityOutput = await context
                            .CallActivityAsync<ProcessContactEligibilityCheckByBatch.
                                ProcessContactEligibilityCheckByBatchOutput>(
                                "ProcessContactEligibilityCheckByBatch",
                                activityInput);

                        // If successful, update cursors for the *next* segment
                        currentLastContactCreatedAt = activityOutput.NextBatchLastContactCreatedAt;
                        currentLastContactId = activityOutput.NextBatchLastContactId;
                        segmentSuccessfullyProcessed = true;

                        moreDataToProcess = activityOutput.NextBatchLastContactId != null ||
                                            activityOutput.NextBatchLastContactCreatedAt != null;

                        _logger.LogInformation(
                            "Successfully processed batch for workflow {WorkflowVersionedId}. Contacts in fetched batch: {ContactsFetched}. Contacts enrolled: {ContactsEnrolled}. Next cursor: CreatedAt={NextCreatedAt}, Id={NextId}",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            activityOutput.ContactsInFetchedBatch,
                            activityOutput.ContactsEnrolled,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        // If this attempt was successful, especially if it was with a reduced batch size,
                        // we might want to reset `currentConfiguredBatchSize` to `InitialBatchSize` for the *next* segment,
                        // or adapt it based on success. For now, let's reset it for the next segment if it was reduced.
                        currentConfiguredBatchSize = InitialBatchSize;
                    }
                    catch (OutOfMemoryException oomEx) // Catch OOM specifically
                    {
                        _logger.LogError(
                            oomEx,
                            "OutOfMemoryException processing contact batch for workflow {WorkflowVersionedId} with BatchSize {AttemptBatchSize}. Reducing batch size. Current segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);

                        attemptBatchSize /= 2;

                        if (attemptBatchSize < MinBatchSize)
                        {
                            attemptBatchSize = MinBatchSize;
                        }

                        currentConfiguredBatchSize = attemptBatchSize;

                        if (attemptBatchSize == MinBatchSize && !segmentSuccessfullyProcessed)
                        {
                            _logger.LogCritical(
                                oomEx,
                                "Critical error: OutOfMemoryException persisted for workflow {WorkflowVersionedId} even with MinBatchSize {MinBatchSize}. Stopping processing. Segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                                scheduleWorkflowInput.WorkflowVersionedId,
                                MinBatchSize,
                                currentLastContactCreatedAt,
                                currentLastContactId);
                            moreDataToProcess = false;

                            throw new Exception(
                                $"Failed to process segment due to OutOfMemoryException for workflow {scheduleWorkflowInput.WorkflowVersionedId} even with MinBatchSize {MinBatchSize}. Cursors: CreatedAt={currentLastContactCreatedAt}, Id={currentLastContactId}",
                                oomEx);
                        }
                    }
                    catch (Exception e) // Catch other exceptions from the activity
                    {
                        _logger.LogError(
                            e,
                            "Exception occurred in ProcessContactEligibilityCheckByBatch for workflow {WorkflowVersionedId} with BatchSize {AttemptBatchSize}. Current segment cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}. This segment will be marked as failed, and the orchestration will stop processing further segments for this run.",
                            scheduleWorkflowInput.WorkflowVersionedId,
                            attemptBatchSize,
                            currentLastContactCreatedAt,
                            currentLastContactId);
                        moreDataToProcess = false; // Stop processing further segments.
                    }
                } // End of inner retry loop for the segment

                if (!segmentSuccessfullyProcessed && moreDataToProcess)
                {
                    // This state implies the inner loop exited because batch size fell below MinBatchSize
                    // without success, and the exception above (rethrow) should have already propagated.
                    // If somehow reached, ensure we stop.
                    _logger.LogError(
                        "Failed to process segment for workflow {WorkflowVersionedId} after all retries. Stopping. Last attempted cursors: CreatedAt={LastContactCreatedAt}, Id={LastContactId}",
                        scheduleWorkflowInput.WorkflowVersionedId,
                        currentLastContactCreatedAt,
                        currentLastContactId);
                    moreDataToProcess = false;
                }

            } // End of while(moreDataToProcess)

            _logger.LogInformation(
                "Completed all data processing for workflow {WorkflowVersionedId} for company {SleekflowCompanyId}.",
                scheduleWorkflowInput.WorkflowVersionedId,
                scheduleWorkflowInput.SleekflowCompanyId);

        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Orchestration failed for ScheduledWorkflow {WorkflowVersionedId} for {SleekflowCompanyId}",
                scheduleWorkflowInput.WorkflowVersionedId,
                scheduleWorkflowInput.SleekflowCompanyId);

            // Optionally, decide if we still want to reschedule even if there was an error
            // For now, we will reschedule regardless of transient errors in the main logic.
        }
        finally // Ensure rescheduling happens even if exceptions occur in the main try block
        {
            // --- Recurrence Logic ---

            // Check if recurring settings are provided
            if (scheduleWorkflowInput.RecurringSettings == null)
            {
                _logger.LogInformation(
                    "No recurring settings found for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Orchestration will complete.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId);
            }
            else
            {
                // Directly calculate the next run time using the new method
                var nextRunTime = _workflowRecurringSettingsParser.GetNextOccurrenceAfter(
                    scheduleWorkflowInput.ScheduledDatetime,
                    scheduleWorkflowInput.RecurringSettings,
                    context.CurrentUtcDateTime);

                _logger.LogInformation(
                    "Scheduling next run for Workflow {WorkflowVersionedId} for Company {SleekflowCompanyId} at {NextRunTime} UTC based on recurring settings.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId,
                    nextRunTime);

                // Wait until the next scheduled time
                await context.CreateTimer(nextRunTime.UtcDateTime, CancellationToken.None);

                // Continue as a new instance to keep history clean and implement the recurrence
                _logger.LogInformation(
                    "Timer fired for Workflow {WorkflowVersionedId} / Company {SleekflowCompanyId}. Continuing as new.",
                    scheduleWorkflowInput.WorkflowVersionedId,
                    scheduleWorkflowInput.SleekflowCompanyId);
                context.ContinueAsNew(scheduleWorkflowInput);
            }
        }
    }
}