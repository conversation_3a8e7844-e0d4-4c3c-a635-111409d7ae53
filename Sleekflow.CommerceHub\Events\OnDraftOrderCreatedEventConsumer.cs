using MassTransit;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Orders;

namespace Sleekflow.CommerceHub.Events;

public class OnDraftOrderCreatedEventConsumerDefinition : ConsumerDefinition<OnDraftOrderCreatedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnDraftOrderCreatedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnDraftOrderCreatedEventConsumer : IConsumer<OnDraftOrderCreatedEvent>
{
    private readonly IOrderService _orderService;

    public OnDraftOrderCreatedEventConsumer(IOrderService orderService)
    {
        _orderService = orderService;
    }

    public async Task Consume(ConsumeContext<OnDraftOrderCreatedEvent> context)
    {
        var @event = context.Message;

        var order =
            await _orderService.GetAsync(@event.OrderId, @event.SleekflowCompanyId);

        await _orderService.DeleteOrderAsync(@event.OrderId, @event.SleekflowCompanyId);
    }
}