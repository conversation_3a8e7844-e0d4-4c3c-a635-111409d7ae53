using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetTicketExportDataInput
{
    [JsonProperty("state_id")]
    [Required]
    public string StateId { get; set; }

    [JsonProperty("state_identity")]
    [Required]
    [Validations.ValidateObject]
    public StateIdentity StateIdentity { get; set; }

    [JsonProperty("id")]
    [Required]
    public string Id { get; set; }

    [JsonConstructor]
    public GetTicketExportDataInput(
        string stateId,
        StateIdentity stateIdentity,
        string id)
    {
        StateId = stateId;
        StateIdentity = stateIdentity;
        Id = id;
    }
}