using MassTransit;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Models.ImportUser;
using Sleekflow.TenantHub.Blobs;
using Sleekflow.TenantHub.Client;
using Sleekflow.TenantHub.Events;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.Events;
using Sleekflow.TenantHub.Models.ImportUser;
using Sleekflow.TenantHub.Models.TravisBackend;
using Sleekflow.TenantHub.Models.Users;
using Sleekflow.TenantHub.Roles;
using Sleekflow.TenantHub.Users;
using ImportUserFromCsvRequest = Sleekflow.Models.ImportUser.ImportUserFromCsvRequest;

namespace Sleekflow.TenantHub.ImportUser;

public interface IImportUserFromCsvService
{
    // dispatch a event to import users from CSV
    public Task<string> DispatchImportUsersFromCsvEventAsync(
        List<string> blobNames,
        string sleekflowCompanyId,
        string location,
        bool? isEnterpriseUsers = false);

    public Task<ImportUsersFromCsvResponse> ImportUsersFromCsvAsync(
        string taskId,
        List<string> blobNames,
        string sleekflowCompanyId,
        string location,
        bool? isEnterpriseUsers = false);

    public Task<ImportUserProgress> GetImportUserProgressAsync(string taskId);

    public record ImportUsersFromCsvResponse
    {
        public int RecordCount { get; init; }

        public Dictionary<string, string> FailedRecordRowNumbers { get; init; }
    }
}

public class ImportUserFromCsvService : IImportUserFromCsvService, IScopedService
{
    private readonly IBlobService _blobService;
    private readonly IImportUserCsvProcessor _importUserCsvProcessor;
    private readonly IIdService _idService;
    private readonly IUserService _userService;
    private readonly IRoleService _roleService;
    private readonly ITravisBackendClient _travisBackendClient;
    private readonly ILogger<ImportUserFromCsvService> _logger;
    private readonly IRequestClient<ImportUserFromCsvRequest> _onImportUserFromCsvEventRequestClient;
    private readonly ICacheService _cacheService;

    public ImportUserFromCsvService(
        IBlobService blobService,
        IImportUserCsvProcessor importUserCsvProcessor,
        IIdService idService,
        IUserService userService,
        IRoleService roleService,
        ITravisBackendClient travisBackendClient,
        ILogger<ImportUserFromCsvService> logger,
        IRequestClient<ImportUserFromCsvRequest> onImportUserFromCsvEventRequestClient,
        ICacheService cacheService)
    {
        _blobService = blobService;
        _importUserCsvProcessor = importUserCsvProcessor;
        _idService = idService;
        _userService = userService;
        _roleService = roleService;
        _travisBackendClient = travisBackendClient;
        _logger = logger;
        _onImportUserFromCsvEventRequestClient = onImportUserFromCsvEventRequestClient;
        _cacheService = cacheService;
    }

    public async Task<string> DispatchImportUsersFromCsvEventAsync(
        List<string> blobNames,
        string sleekflowCompanyId,
        string location,
        bool? isEnterpriseUsers)
    {
        var eventResponse = await _onImportUserFromCsvEventRequestClient.GetResponse<ImportUserFromCsvReply>(
            new ImportUserFromCsvRequest(
                blobNames,
                location,
                sleekflowCompanyId,
                isEnterpriseUsers));

        return eventResponse.Message.TaskId;
    }

    public async Task<IImportUserFromCsvService.ImportUsersFromCsvResponse> ImportUsersFromCsvAsync(
        string taskId,
        List<string> blobNames,
        string sleekflowCompanyId,
        string location,
        bool? isEnterpriseUsers = false)
    {
        var blobDownloadSasUrls = await _blobService.CreateBlobDownloadSasUrls(
            sleekflowCompanyId,
            blobNames);

        if (blobDownloadSasUrls.Count == 0)
        {
            throw new Exception("No blob download SAS URLs were returned.");
        }

        if (blobDownloadSasUrls.Count > 1)
        {
            throw new Exception("More than one blob download SAS URL was returned.");
        }

        int recordCount = 0;
        Dictionary<string, string> failedRecordRowNumbers = new Dictionary<string, string>();
        List<ImportUserFromCsvObject> importUserInputObjects = new List<ImportUserFromCsvObject>();

        var allRoles = await _roleService.GetAllDefaultRolesAsync();

        // always only one blob
        var records = await _importUserCsvProcessor.ProcessCsvStreamAsync(blobDownloadSasUrls[0].Url);

        // create details list with records
        var details = records.Select(
            x => new Details(x, "Queued", string.Empty)).ToList();

        await _cacheService.UpsertAsync(
            taskId,
            () =>
            {
                var progress = new ImportUserProgress(
                    taskId,
                    "In Progress",
                    details);

                return Task.FromResult(progress);
            },
            TimeSpan.FromDays(30));

        try
        {
            foreach (var record in records)
            {
                // update progress status for this record
                details[recordCount].Status = "In Progress";

                await _cacheService.UpsertAsync(
                    taskId,
                    () =>
                    {
                        var progress = new ImportUserProgress(
                            taskId,
                            "In Progress",
                            details);

                        return Task.FromResult(progress);
                    },
                    TimeSpan.FromDays(30));


                recordCount++; // csv row number
                var username = record.Username;
                var firstName = record.FirstName;
                var lastName = record.LastName;
                var email = record.Email;
                var password = record.Password;
                var roleStr = record.Role;
                var position = record.Position;

                // validate record
                if (string.IsNullOrWhiteSpace(username) && !string.IsNullOrWhiteSpace(email))
                {
                    username = email;
                }

                if (string.IsNullOrWhiteSpace(username))
                {
                    failedRecordRowNumbers.Add(
                        email ?? username ?? recordCount.ToString(),
                        "Username is required if email is not provided.");

                    // update progress status for this record
                    details[recordCount - 1].Status = "Failed";
                    details[recordCount - 1].Message = "Username is required if email is not provided.";
                    await _cacheService.UpsertAsync(
                        taskId,
                        () =>
                        {
                            var progress = new ImportUserProgress(
                                taskId,
                                "In Progress",
                                details);

                            return Task.FromResult(progress);
                        },
                        TimeSpan.FromDays(30));

                    continue;
                }

                if (string.IsNullOrWhiteSpace(password) && !isEnterpriseUsers.GetValueOrDefault())
                {
                    failedRecordRowNumbers.Add(email ?? username, "Password is required for non-enterprise user.");

                    // update progress status for this record
                    details[recordCount - 1].Status = "Failed";
                    details[recordCount - 1].Message = "Password is required for non-enterprise user.";
                    await _cacheService.UpsertAsync(
                        taskId,
                        () =>
                        {
                            var progress = new ImportUserProgress(
                                taskId,
                                "In Progress",
                                details);

                            return Task.FromResult(progress);
                        },
                        TimeSpan.FromDays(30));

                    continue;
                }

                if (string.IsNullOrWhiteSpace(password))
                {
                    password = GenerateRandomString(80);
                }

                var newTenantHubUserId = _idService.GetId(SysTypeNames.User);
                var newSleekflowUserId = Guid.NewGuid().ToString();

                var isUserRegistered = await _userService.GetOrDefaultUserByEmailAsync(email!);

                if (isUserRegistered != null)
                {
                    failedRecordRowNumbers.Add(email ?? username, "Email already exists.");

                    // update progress status for this record
                    details[recordCount - 1].Status = "Failed";
                    details[recordCount - 1].Message = "Email already exists.";
                    await _cacheService.UpsertAsync(
                        taskId,
                        () =>
                        {
                            var progress = new ImportUserProgress(
                                taskId,
                                "In Progress",
                                details);

                            return Task.FromResult(progress);
                        },
                        TimeSpan.FromDays(30));

                    continue;
                }

                // create user in tenant hub
                var createdUser = await _userService.CreateAndGetAsync(
                    tenantHubUserId: newTenantHubUserId,
                    username: username,
                    firstName: firstName,
                    lastName: lastName ?? string.Empty,
                    displayName: $"{firstName} {lastName}",
                    email: email!,
                    phoneNumber: string.Empty,
                    userWorkspaces: new List<UserWorkspace>(),
                    profilePictureUrl: string.Empty,
                    metadata: new Dictionary<string, object?>(),
                    createdBy: null,
                    updatedBy: null,
                    createdAt: DateTimeOffset.UtcNow,
                    updatedAt: DateTimeOffset.UtcNow);

                username = createdUser.Username;
                var tenantHubUserId = createdUser.Id;
                var sleekflowUserId = newSleekflowUserId;

                var teamStr = record.Team?.Split(";").ToList();

                var teamIds = await _travisBackendClient.GetCompanyTeamIdsByNamesAsync(
                    sleekflowCompanyId,
                    teamStr ?? new List<string>(),
                    location);

                var teamIdsLong = teamIds?.TeamIds.Select(long.Parse).ToList();

                // create user in sleekflow
                var importUserInputObject = new ImportUserFromCsvObject(
                    sleekflowUserId: sleekflowUserId,
                    email: email!,
                    password: password,
                    userName: username,
                    lastname: lastName ?? string.Empty,
                    firstname: firstName,
                    userRole: roleStr,
                    position: position,
                    timeZoneInfoId: null,
                    teamIds: teamIdsLong ?? new List<long>(),
                    tenantHubUserId: tenantHubUserId);

                importUserInputObjects.Add(importUserInputObject);

                // update progress status for this record
                details[recordCount - 1].Status = "Tenant Hub User Created";
                await _cacheService.UpsertAsync(
                    taskId,
                    () =>
                    {
                        var progress = new ImportUserProgress(
                            taskId,
                            "In Progress",
                            details);

                        return Task.FromResult(progress);
                    },
                    TimeSpan.FromDays(30));
            }

            if (importUserInputObjects.Count > 0)
            {
                // Process each user import individually
                foreach (var userObject in importUserInputObjects)
                {
                    var singleImportList = new List<ImportUserFromCsvObject>
                    {
                        userObject
                    };

                    // Import one user at a time
                    ImportUserFromCsvResponse? response;
                    try
                    {
                        response = await _travisBackendClient.ImportUserFromCsvAsync(
                            sleekflowCompanyId,
                            singleImportList,
                            location);

                        if (response is { Success: false })
                        {
                            throw new Exception(response.Message);
                        }
                    }
                    catch (Exception ex)
                    {
                        var failedUser = await _userService.GetUserAsync(userObject.TenantHubUserId);
                        if (failedUser != null)
                        {
                            await _userService.DeleteAsync(userObject.TenantHubUserId);
                        }

                        _logger.LogError(
                            ex,
                            "Error importing user {Email} from CSV to Travis backend. {Message}",
                            userObject.Email,
                            ex.Message);

                        // Find the record by email instead of using direct index
                        var failedRecord = details.Find(
                            x => string.Equals(
                                x.CsvRecord.Email,
                                userObject.Email,
                                StringComparison.OrdinalIgnoreCase));
                        if (failedRecord != null)
                        {
                            failedRecord.Status = "Failed";
                            failedRecord.Message = ex.Message;
                            await _cacheService.UpsertAsync(
                                taskId,
                                () =>
                                {
                                    var progress = new ImportUserProgress(
                                        taskId,
                                        "In Progress",
                                        details);

                                    return Task.FromResult(progress);
                                },
                                TimeSpan.FromDays(30));
                        }

                        continue;
                    }

                    // Check if response is null or has no imported users
                    if (response?.ImportedUsers == null || response.ImportedUsers.Count == 0)
                    {
                        _logger.LogWarning(
                            "No imported users returned for user {Email}. Skipping workspace update.",
                            userObject.Email);
                        continue;
                    }

                    // Safely get the first imported user
                    var responseItem = response.ImportedUsers[0];
                    var userInput = singleImportList[0];

                    var user = await _userService.GetUserAsync(userInput?.TenantHubUserId ?? string.Empty);
                    if (user is null)
                    {
                        throw new SfInternalErrorException(
                            $"[{nameof(ImportUsersFromCsvAsync)}] User {userInput?.Email} not found.");
                    }

                    var userRoleIds = allRoles
                        .Where(r => r.Name == responseItem.UserRole)
                        .Select(r => r.Id).ToList();

                    var teamStr = records.Where(
                        x => x.Email == responseItem.Email).Select(
                        x => x.Team).ToString()?.Split(";").ToList();

                    var teamIds = await _travisBackendClient.GetCompanyTeamIdsByNamesAsync(
                        sleekflowCompanyId,
                        teamStr ?? new List<string>(),
                        location);

                    var returnWorkspace = new UserWorkspace(
                        sleekflowCompanyId,
                        responseItem.SleekflowUserId,
                        responseItem.StaffId.ToString(),
                        userRoleIds,
                        teamIds?.TeamIds ?? new List<string>(),
                        true,
                        new List<string>(),
                        new Dictionary<string, object?>());

                    user.UserWorkspaces.Add(returnWorkspace);

                    await _userService.UpdateAndGetAsync(user);

                    // update progress status for this record
                    var successRecord = details.Find(
                        x => string.Equals(x.CsvRecord.Email, responseItem.Email, StringComparison.OrdinalIgnoreCase));
                    if (successRecord != null)
                    {
                        successRecord.Status = "Success";
                        await _cacheService.UpsertAsync(
                            taskId,
                            () =>
                            {
                                var progress = new ImportUserProgress(
                                    taskId,
                                    "In Progress",
                                    details);

                                return Task.FromResult(progress);
                            },
                            TimeSpan.FromDays(30));
                    }
                    else
                    {
                        _logger.LogWarning(
                            "Could not find matching record for email {Email} in details list. This may indicate email format was modified during import.",
                            responseItem.Email);
                    }
                }
            }

            await _cacheService.UpsertAsync(
                taskId,
                () =>
                {
                    var progress = new ImportUserProgress(
                        taskId,
                        details.Exists(x => x.Status == "Failed") ? "Failed" : "Completed",
                        details);

                    return Task.FromResult(progress);
                },
                TimeSpan.FromDays(30));

            return new IImportUserFromCsvService.ImportUsersFromCsvResponse
            {
                RecordCount = recordCount,
                FailedRecordRowNumbers = failedRecordRowNumbers
            };
        }
        catch (Exception ex)
        {
            foreach (var importUserInputObject in importUserInputObjects)
            {
                var user = await _userService.GetUserAsync(importUserInputObject.TenantHubUserId);
                if (user != null)
                {
                    await _userService.DeleteAsync(importUserInputObject.TenantHubUserId);
                }
            }

            _logger.LogError(
                ex,
                "Error importing users from CSV. {RecordCount} records processed. {ErrorMessage}",
                recordCount - 1,
                ex.Message);

            await _cacheService.UpsertAsync(
                taskId,
                () =>
                {
                    var progress = new ImportUserProgress(
                        taskId,
                        "Failed",
                        details);

                    return Task.FromResult(progress);
                },
                TimeSpan.FromDays(30));

            throw new SfInternalErrorException(ex, "Error importing users from CSV.");
        }
    }

    public async Task<ImportUserProgress> GetImportUserProgressAsync(string taskId)
    {
        try
        {
            var progress = await _cacheService.CacheAsync<ImportUserProgress>(
                taskId,
                () => throw new SfNotFoundObjectException($"Import progress not found for task ID: {taskId}"),
                TimeSpan.Zero);
            if (progress == null)
            {
                throw new SfNotFoundObjectException($"Import progress not found for task ID: {taskId}");
            }

            return progress;
        }
        catch (SfNotFoundObjectException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving import progress for task ID: {TaskId}", taskId);
            throw new SfInternalErrorException(ex, "Error retrieving import progress.");
        }
    }

    private static string GenerateRandomString(int length = 10, bool includeSymbols = true)
    {
        var rnd = new Random(Guid.NewGuid().GetHashCode());
        var chars = includeSymbols
            ? "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
            : "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

        return new string(
            Enumerable.Repeat(chars, length)
                .Select(s => s[rnd.Next(s.Length)]).ToArray());
    }
}