﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Schemas.Properties;

public sealed class PropertyInput : IProperty
{
    [Required]
    [JsonProperty(IProperty.PropertyNameDisplayName)]
    public string DisplayName { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameUniqueName)]
    public string UniqueName { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameDataType)]
    [Validations.ValidateObject]
    public IDataType DataType { get; }

    [Required]
    [JsonProperty(IProperty.PropertyNameIsRequired)]
    public bool IsRequired { get; }

    [Required]
    [JsonProperty(IProperty.PropertyNameIsVisible)]
    public bool IsVisible { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameIsPinned)]
    public bool IsPinned { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameIsSearchable)]
    public bool IsSearchable { get; set; }

    [Required]
    [JsonProperty(IProperty.PropertyNameDisplayOrder)]
    [Range(0, int.MaxValue)]
    public int DisplayOrder { get; set; }

    [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
    [Validations.ValidateObject]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(IProperty.PropertyNameOptions)]
    [Validations.ValidateArray]
    public List<Option>? Options { get; set; }

    [JsonConstructor]
    public PropertyInput(
        string displayName,
        string uniqueName,
        IDataType dataType,
        bool isPrimaryKey,
        bool isRequired,
        bool isVisible,
        bool isPinned,
        bool isSearchable,
        int displayOrder,
        AuditEntity.SleekflowStaff? createdBy,
        List<Option>? options)
    {
        DisplayName = displayName;
        UniqueName = uniqueName;
        DataType = dataType;
        IsRequired = isRequired;
        IsVisible = isVisible;
        IsPinned = isPinned;
        IsSearchable = isSearchable;
        DisplayOrder = displayOrder;
        CreatedBy = createdBy;
        Options = options;
    }
}