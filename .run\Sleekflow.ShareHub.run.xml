﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.ShareHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.ShareHub/bin/Debug/net8.0/Sleekflow.ShareHub.exe" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.ShareHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="AFD_PROFILE_NAME" value="sleekflow" />
      <env name="AFD_RESOURCE_GROUP_NAME" value="sleekflow-resource-group-devf9af1d41" />
      <env name="AFD_SUBSCRIPTION_ID" value="c19c9b56-93e9-4d4c-bc81-838bd3f72ad6" />
      <env name="APP_CONFIGURATION_CONN_STR" value="Endpoint=https://sleekflow-app-configurationb83ec65c.azconfig.io;Id=0f3Z-lb-s0:hWLXNKUM3ATACVXeEqAT;Secret=Ub6/5/VjpnRCXqQ4JNhRVzVp350ymvs0QP4cbFm0ACo=" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7092;http://localhost:7093" />
      <env name="AZURE_AD_CLIENT_ID" value="19956950-83e2-4f21-98fc-0cc3942416f4" />
      <env name="AZURE_AD_CLIENT_SECRET" value="****************************************" />
      <env name="AZURE_AD_TENANT_ID" value="d66fa1cc-347d-42e9-9444-19c5fd0bbcce" />
      <env name="CACHE_PREFIX" value="Sleekflow.ShareHub" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="COSMOS_SHARE_HUB_DB_DATABASE_ID" value="sharehubdb" />
      <env name="COSMOS_SHARE_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_SHARE_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="DEFAULT_DOMAIN_PROFILE_NAME" value="sleekflow" />
      <env name="DEFAULT_DOMAIN_PROVIDER_ID" value="dev.sf.chat" />
      <env name="DEFAULT_DOMAIN_RESOURCE_GROUP_NAME" value="sleekflow-resource-group-devf9af1d41" />
      <env name="DEFAULT_DOMAIN_SUBSCRIPTION_ID" value="c19c9b56-93e9-4d4c-bc81-838bd3f72ad6" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="QR_CODE_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=sbd780c76;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="IMAGE_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=sfd6c2495;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="FILE_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=s152e7960;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.ShareHub/Sleekflow.ShareHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>