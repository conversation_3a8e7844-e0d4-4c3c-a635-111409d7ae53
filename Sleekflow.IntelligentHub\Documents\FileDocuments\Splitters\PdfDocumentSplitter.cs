﻿using PdfSharp.Pdf;
using PdfSharp.Pdf.IO;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters;

public class PdfDocumentSplitter : IDocumentSplitter
{
    public Task<List<(Stream Stream, int StartPage, int EndPage)>> SplitDocumentIntoChunksAsync(
        Stream stream,
        int numberOfContentPerFile)
    {
        var outputStreams = new List<(Stream Stream, int StartPage, int EndPage)>();

        using var inputPdf = PdfReader.Open(stream, PdfDocumentOpenMode.Import);

        var pageCount = inputPdf.PageCount;
        for (var startPage = 0; startPage < pageCount; startPage += numberOfContentPerFile)
        {
            using var outputPdf = new PdfDocument();

            for (var i = startPage; i < Math.Min(startPage + numberOfContentPerFile, pageCount); i++)
            {
                outputPdf.AddPage(inputPdf.Pages[i]);
            }

            // Should not dispose this stream.
            // To be returned to the caller.
            var outputStream = new MemoryStream();
            outputPdf.Save(outputStream);
            outputStream.Position = 0;

            var endPage = Math.Min(startPage + numberOfContentPerFile, pageCount) - 1;

            outputStreams.Add((outputStream, startPage, endPage));
        }

        return Task.FromResult(outputStreams);
    }
}