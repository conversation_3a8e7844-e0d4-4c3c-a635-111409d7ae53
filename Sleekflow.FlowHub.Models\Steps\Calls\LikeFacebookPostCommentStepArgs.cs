using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class LikeFacebookPostCommentStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.like-facebook-post-comment";

    [Required]
    [JsonProperty("facebook_page_id__expr")]
    public string FacebookPageIdExpr { get; set; }

    [Required]
    [JsonProperty("facebook_comment_id__expr")]
    public string FacebookCommentIdExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.FacebookIntegration;

    [JsonConstructor]
    public LikeFacebookPostCommentStepArgs(string facebookPageIdExpr, string facebookCommentIdExpr)
    {
        FacebookPageIdExpr = facebookPageIdExpr;
        FacebookCommentIdExpr = facebookCommentIdExpr;
    }
}