using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Consumers.CrmHubEventConsumers;

public class SalesforceObjectUpdatedEventRequestConsumerDefinition : ConsumerDefinition<SalesforceObjectUpdatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SalesforceObjectUpdatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SalesforceObjectUpdatedEventRequestConsumer : IConsumer<SalesforceObjectUpdatedEventRequest>
{
    private readonly IServiceBusManager _serviceBusManager;

    public SalesforceObjectUpdatedEventRequestConsumer(
        IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<SalesforceObjectUpdatedEventRequest> context)
    {
        var salesforceObjectUpdatedEventRequest = context.Message;

        await _serviceBusManager.PublishAsync(new OnTriggerEventRequestedEvent(
            new OnSalesforceObjectUpdatedEventBody(
                salesforceObjectUpdatedEventRequest.CreatedAt,
                salesforceObjectUpdatedEventRequest.ConnectionId,
                salesforceObjectUpdatedEventRequest.ConnectionId,
                salesforceObjectUpdatedEventRequest.ObjectType,
                salesforceObjectUpdatedEventRequest.ObjectType,
                salesforceObjectUpdatedEventRequest.IsCustomObject,
                salesforceObjectUpdatedEventRequest.ObjectDict),
            salesforceObjectUpdatedEventRequest.ObjectId,
            salesforceObjectUpdatedEventRequest.ObjectType,
            salesforceObjectUpdatedEventRequest.SleekflowCompanyId));
    }
}