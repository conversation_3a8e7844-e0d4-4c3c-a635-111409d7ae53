﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SearchZohoObjectStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.search-zoho-object";

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [Required]
    [JsonProperty("conditions")]
    public List<SearchZohoObjectCondition> Conditions { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.ZohoIntegration;

    [JsonConstructor]
    public SearchZohoObjectStepArgs(
        string connectionId,
        string entityTypeName,
        List<SearchZohoObjectCondition> conditions)
    {
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        Conditions = conditions;
    }
}

public class SearchZohoObjectCondition
{
    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("search_operator")]
    public string SearchOperator { get; set; }

    [JsonProperty("value__expr")]
    public string? ValueExpr { get; set; }

    [JsonConstructor]
    public SearchZohoObjectCondition(
        string fieldName,
        string searchOperator,
        string? valueExpr)
    {
        FieldName = fieldName;
        SearchOperator = searchOperator;
        ValueExpr = valueExpr;
    }
}