using Pulumi;

namespace Sleekflow.Infras.Components.Configs;

public class CoreSqlDbConfig
{
    public Output<string> EasDbConnectionString { get; set; }

    public Output<string> EusDbConnectionString { get; set; }

    public Output<string> SeasDbConnectionString { get; set; }

    public Output<string> UaenDbConnectionString { get; set; }

    public Output<string> WeuDbConnectionString { get; set; }

    public CoreSqlDbConfig()
    {
        var config = new Pulumi.Config("core_sql_db");
        EasDbConnectionString = config.RequireSecret("connection_string_eas");
        EusDbConnectionString = config.RequireSecret("connection_string_eus");
        SeasDbConnectionString = config.RequireSecret("connection_string_seas");
        UaenDbConnectionString = config.RequireSecret("connection_string_uaen");
        WeuDbConnectionString = config.RequireSecret("connection_string_weu");
    }
}