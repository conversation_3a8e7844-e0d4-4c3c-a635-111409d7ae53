using Newtonsoft.Json;
using Sleekflow.KrakenD.Generator.Constants;

namespace Sleekflow.KrakenD.Generator.InternalObjects;

internal class PublicApiGatewayEndpoint : Endpoint
{
    [JsonIgnore]
    public string Group { get; }

    [JsonIgnore]
    public string ServiceType { get; }

    [JsonIgnore]
    public string Subgroup { get; }

    [JsonIgnore]
    public string HostEnvName { get; }

    public PublicApiGatewayEndpoint(
        string group,
        string serviceType,
        string subgroup,
        string hostEnvName,
        string? inputKeyEnvName = default,
        string? inputAuthenticationType = default)
    {
        Group = group;
        ServiceType = serviceType;
        Subgroup = subgroup;
        HostEnvName = hostEnvName;
        var (preEndpointSources, inputHeaders, keyEnvName) =
            inputAuthenticationType switch
            {
                AuthenticationTypes.ApiKey => (
                    new object[]
                    {
                        "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
                    },
                    new string[]
                    {
                        "X-Sleekflow-Api-Key",
                        "Content-Type",
                        "Traceparent",
                    },
                    string.Empty),
                AuthenticationTypes.JwtToken => (
                    new object[]
                    {
                        "lua/PublicApiGateway/JwtToken/pre_endpoint.lua"
                    },
                    new string[]
                    {
                        "Authorization",
                        "Content-Type",
                        "Traceparent",
                    },
                    string.Empty),
                AuthenticationTypes.ManagementKey => (
                    new object[]
                    {
                        "lua/pre_endpoint.lua"
                    },
                    new string[]
                    {
                        "Content-Type",
                        "Traceparent",
                    },
                    "'{{ env \"" + inputKeyEnvName + "\" }}'"),
                _ => (
                    Array.Empty<object>(),
                    Array.Empty<string>(),
                    string.Empty)
            };

        EndpointEndpoint = "/v1/"
                           + group
                           + "/" + serviceType
                           + "/" + subgroup
                           + "/{method}";
        InputHeaders = inputHeaders;
        Method = Generator.Method.Post;
        Backend = new[]
        {
            new Backend
            {
                UrlPattern = "/" + serviceType
                                 + "/" + subgroup + "/{method}",
                Method = Generator.Method.Post,
                Encoding = BackendEncoding.Json,
                Host = new object[]
                {
                    "{{ env \"" + hostEnvName + "\" }}"
                },
            }
        };
        ExtraConfig = new EndpointExtraConfig
        {
            ModifierLuaEndpoint = preEndpointSources.Length != 0
                ? new ModifierLuaEndpointClass
                {
                    Sources = preEndpointSources,
                    Pre = $"pre_endpoint({keyEnvName})",
                    Live = false,
                    AllowOpenLibs = false
                }
                : null,
            ModifierLuaProxy = new ModifierLuaEndpointClass
            {
                Sources = new object[]
                {
                    "lua/post_proxy.lua"
                },
                Post = "post_proxy()",
                Live = false,
                AllowOpenLibs = false
            }
        };
    }
}