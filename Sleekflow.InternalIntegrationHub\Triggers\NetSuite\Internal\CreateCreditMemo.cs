using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class CreateCreditMemo
    : ITrigger<CreateCreditMemo.CreateCreditMemoInput, CreateCreditMemo.CreateCreditMemoOutput>
{
    private readonly INetSuiteCustomerService _netSuiteCustomerService;

    public CreateCreditMemo(INetSuiteCustomerService netSuiteCustomerService)
    {
        _netSuiteCustomerService = netSuiteCustomerService;
    }

    public class CreateCreditMemoInput
    {
        [JsonConstructor]
        public CreateCreditMemoInput(string companyId, decimal amount, string invoiceExternalId, string itemName)
        {
            Amount = amount;
            CompanyId = companyId;
            InvoiceExternalId = invoiceExternalId;
            ItemName = itemName;
        }

        [Required]
        [JsonProperty("amount")]
        [Range(0, double.MaxValue)]
        public decimal Amount { get; set; }

        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }


        [Required]
        [JsonProperty("invoice_external_id")]
        public string InvoiceExternalId { get; set; }

        [Required]
        [JsonProperty("item_name")]
        public string ItemName { get; set; }
    }

    public class CreateCreditMemoOutput
    {
        [JsonConstructor]
        public CreateCreditMemoOutput(string externalId)
        {
            ExternalId = externalId;
        }

        [JsonProperty("external_id")]
        public string ExternalId { get; set; }
    }

    public async Task<CreateCreditMemoOutput> F(CreateCreditMemoInput input)
    {
        var response = await _netSuiteCustomerService.CreateCreditMemo(input);
        if (string.IsNullOrEmpty(response))
        {
            throw new Exception("Failed to create credit memo");
        }

        return new CreateCreditMemoOutput(input.InvoiceExternalId);
    }
}