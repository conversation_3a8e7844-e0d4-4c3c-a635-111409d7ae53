using Pulumi;
using Pulumi.AzureNative.DataBoxEdge.V20220301;
using Pulumi.AzureNative.Resources;
using Pulumi.AzureNative.Web.Inputs;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using EnvironmentVarArgs = Pulumi.AzureNative.App.V20240301.Inputs.EnvironmentVarArgs;
using EventGrid = Pulumi.AzureNative.EventGrid;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using ScaleRuleArgs = Pulumi.AzureNative.App.V20240301.Inputs.ScaleRuleArgs;
using SecretArgs = Pulumi.AzureNative.App.V20240301.Inputs.SecretArgs;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.Opa;

public class Opa
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;

    private readonly string _blobContainerName = "opa-policies";

    public Opa(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
    }

    public (Dictionary<string, App.ContainerApp> Apps, Storage.StorageAccount StorageAccount) InitOpa()
    {
        // Step 1: Get OPA and OPAL images
        var opaImage = GetOpaImage("sleekflow-opa");
        var opalImage = GetOpalImage("sleekflow-opal");

        #region Create Blob Storage

        // Step 2: Create an Azure Storage Account
        var randomId = new Random.RandomId(
            $"sleekflow-opa-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "opa-storage", "opa-opal-storage-value"
                    }
                },
            });

        var storageAccount = new Storage.StorageAccount(
            "sleekflow-opa-storage-account",
            new Storage.StorageAccountArgs
            {
                AccountName = randomId.Hex.Apply(h => "s" + h),
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    // Name = Storage.SkuName.Standard_LRS
                    Name = Storage.SkuName.Standard_RAGRS
                },
                Kind = Storage.Kind.StorageV2,
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-opa-file-storage-{_myConfig.Name}"
                    }
                },
            },
            new CustomResourceOptions()
            {
                Parent = _resourceGroup,
            });

        var blobServiceProperties = new Storage.BlobServiceProperties(
            "sleekflow-opa-blob-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                AccountName = storageAccount.Name,
                ResourceGroupName = _resourceGroup.Name,
                BlobServicesName = "default",
                ChangeFeed = new Storage.Inputs.ChangeFeedArgs
                {
                    Enabled = true
                },
                IsVersioningEnabled = true
            },
            new CustomResourceOptions
            {
                Parent = storageAccount
            });

        // Step 3: Create a Blob Container for policy storage
        var policyStorage = new Storage.BlobContainer(
            "sleekflow-opa-policy-storage",
            new Storage.BlobContainerArgs
            {
                AccountName = storageAccount.Name,
                ResourceGroupName = _resourceGroup.Name,
                PublicAccess = Storage.PublicAccess.None,
                ContainerName = _blobContainerName
            },
            new CustomResourceOptions
            {
                Parent = storageAccount
            });

        _resourceGroup.Location.Apply(
            location =>
            {
                Console.WriteLine($"Created BlobContainer at Location: {location}");
                return location;
            });

        #endregion

        var apps = new Dictionary<string, App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.OpenPolicyAgent))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var containerOpaAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.OpenPolicyAgent));
            Console.WriteLine($" containerOpaAppName : {containerOpaAppName}");

            var containerOpalAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.OpenPolicyAdministrationLayer));

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            #region Opal Container

            // Step 5: Deploy OPAL container
            var opalApp = new App.ContainerApp(
                containerOpalAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerOpalAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 7002,
                            Transport = "Auto", // This allows WebSocket connections
                            Traffic = new App.Inputs.TrafficWeightArgs()
                            {
                                LatestRevision = true, Weight = 100
                            }
                        },
                        Registries = new App.Inputs.RegistryCredentialsArgs
                        {
                            Server = _registry.LoginServer,
                            Username = _registryUsername,
                            PasswordSecretRef = "registry-password-secret"
                        },
                        Secrets = new SecretArgs
                        {
                            Name = "registry-password-secret", Value = _registryPassword
                        },
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs()
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 2 : 1,
                            MaxReplicas = 5,
                            Rules = new List<ScaleRuleArgs>()
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "100"
                                            }
                                        }
                                    }
                                },
                            }
                        },
                        Containers = new App.Inputs.ContainerArgs
                        {
                            Name = "sleekflow-opal-app",
                            Image = opalImage.BaseImageName,
                            Resources = new App.Inputs.ContainerResourcesArgs
                            {
                                Cpu = 1, Memory = "2.0Gi"
                            },
                            Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                            [
                                new EnvironmentVarArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                    Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                },

                                #region OPAL configs

                                new EnvironmentVarArgs
                                {
                                    Name = "BLOB_STORAGE_URL",
                                    Value = Output.Format(
                                        $"https://{storageAccount.Name}.blob.core.windows.net/{policyStorage.Name}")
                                },

                                new EnvironmentVarArgs()
                                {
                                    Name = "OPAL_DATA_PATH",
                                    Value = StorageUtils.GetConnectionString(
                                        _resourceGroup.Name,
                                        storageAccount.Name)
                                },

                                #endregion

                                #region LoggerConfig

                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_AUTHENTICATION_ID",
                                    Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", Value = _gcpConfig.CredentialJson,
                                },

                                #endregion
                            ]),
                        },
                    }
                });

            #endregion

            #region OPA Container

            // Step 6: Deploy OPA container
            var opaApp = new App.ContainerApp(
                containerOpaAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerOpaAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 8181,
                            Transport = "Auto", // This allows WebSocket connections
                            Traffic = new App.Inputs.TrafficWeightArgs()
                            {
                                LatestRevision = true, Weight = 100
                            }
                        },
                        Registries = new App.Inputs.RegistryCredentialsArgs
                        {
                            Server = _registry.LoginServer,
                            Username = _registryUsername,
                            PasswordSecretRef = "registry-password-secret"
                        },
                        Secrets = new SecretArgs
                        {
                            Name = "registry-password-secret", Value = _registryPassword
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs()
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 4 : 2,
                            MaxReplicas = 10,
                            Rules = new List<ScaleRuleArgs>()
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "80"
                                            }
                                        }
                                    }
                                },
                            }
                        },
                        Containers = new App.Inputs.ContainerArgs
                        {
                            Name = "sleekflow-opa-app",
                            Image = opaImage.BaseImageName,
                            Resources = new App.Inputs.ContainerResourcesArgs
                            {
                                Cpu = 2, Memory = "4.0Gi"
                            },
                            Probes = new List<App.Inputs.ContainerAppProbeArgs>
                            {
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "liveness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/v1/data/health/health_status",
                                        Port = 8181,
                                        Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 12,
                                    PeriodSeconds = 2,
                                },
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "readiness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/v1/data/health/health_status",
                                        Port = 8181,
                                        Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 12,
                                    PeriodSeconds = 2,
                                }
                            },
                            Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                            [
                                new EnvironmentVarArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                    Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                },
                                new EnvironmentVarArgs
                                {
                                    Name = "BLOB_STORAGE_URL",
                                    Value = Output.Format(
                                        $"https://{storageAccount.Name}.blob.core.windows.net/{policyStorage.Name}")
                                },
                                new EnvironmentVarArgs()
                                {
                                    Name = "OPA_DATA_PATH",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name)
                                },
                                new EnvironmentVarArgs()
                                {
                                    Name = "OPA_BLOB_CONTAINER_NAME", Value = _blobContainerName
                                },
                                new EnvironmentVarArgs()
                                {
                                    Name = "OPAL_SERVER_URL",
                                    Value = opalApp.LatestRevisionFqdn.Apply(fqdn => $"wss://{fqdn}")
                                },

                                #region LoggerConfig

                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_AUTHENTICATION_ID",
                                    Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                },
                                new App.Inputs.EnvironmentVarArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", Value = _gcpConfig.CredentialJson,
                                },

                                #endregion
                            ]),
                        },
                    }
                });

            #endregion

            #region Blob Trigger

            // Step 7: Create an App Service Plan
            // var appServicePlan = new Web.AppServicePlan(
            //     "sleekflow-opa-blob-trigger-app-service-plan",
            //     new Web.AppServicePlanArgs
            //     {
            //         ResourceGroupName = _resourceGroup.Name,
            //         Kind = string.Empty,
            //         Reserved = true,
            //         Sku = new Web.Inputs.SkuDescriptionArgs
            //         {
            //             Tier = "Dynamic",
            //             Name = "Y1",
            //         }
            //     },
            //     new CustomResourceOptions
            //     {
            //         Parent = _resourceGroup
            //     });

            // Step 8: Create a WebApp
            // var webApp = new Web.WebApp(
            //     "sleekflow-opa-web-app-blob-trigger",
            //     new Web.WebAppArgs()
            //     {
            //         Kind = "FunctionApp",
            //         ResourceGroupName = _resourceGroup.Name,
            //         ServerFarmId = appServicePlan.Id,
            //         SiteConfig = new Web.Inputs.SiteConfigArgs()
            //         {
            //             AppSettings = new InputList<NameValuePairArgs>()
            //             {
            //                 new Web.Inputs.NameValuePairArgs
            //                 {
            //                     Name = "AzureWebJobsStorage",
            //                     Value = Output.Format(
            //                         $"https://{storageAccount.Name}.blob.core.windows.net/{policyStorage.Name}")
            //                 },
            //                 new Web.Inputs.NameValuePairArgs
            //                 {
            //                     Name = "FUNCTIONS_EXTENSION_VERSION", Value = "~3"
            //                 },
            //                 new Web.Inputs.NameValuePairArgs
            //                 {
            //                     Name = "WEBSITE_RUN_FROM_PACKAGE", Value = "1"
            //                 }
            //             }
            //         }
            //     },
            //     new CustomResourceOptions()
            //     {
            //         // The web app depends on the storage account and policy storage to ensure they are created first
            //         Parent = appServicePlan,
            //         DependsOn =[storageAccount, policyStorage]
            //     });

            #endregion

            #region Event Grid

            // Step 9: Create an Event Grid Topic
            // We temporarily disable this feature because we haven't implement it yet
            /*
            var blobUpdatedEventSubscription = new EventGrid.EventSubscription(
                "sleekflow-opa-blob-updated-event-subscription",
                new EventGrid.EventSubscriptionArgs
                {
                    // Scope = policyStorage.Id,
                    Scope = storageAccount.Id,
                    EventSubscriptionName = "sleekflow-opa-blob-updated-event-subscription",
                    EventDeliverySchema = "EventGridSchema",
                    Destination = new EventGrid.Inputs.WebHookEventSubscriptionDestinationArgs
                    {
                        EndpointType = "WebHook",
                        EndpointUrl = webApp.DefaultHostName.Apply(
                            n => !string.IsNullOrEmpty(n)
                                ? $"https://{n}/api/BlobTriggerFunction"
                                : throw new Exception("Error on EventSubscription: Invalid hostname"))
                    },
                    Filter = new EventGrid.Inputs.EventSubscriptionFilterArgs
                    {
                        IncludedEventTypes = new InputList<string>
                        {
                            "Microsoft.Storage.BlobCreated"
                        },
                        IsSubjectCaseSensitive = false
                    }
                },
                new CustomResourceOptions()
                {
                    Parent = webApp,
                    DependsOn = { storageAccount, policyStorage }
                });
                */

            #endregion

            opalApp.LatestRevisionFqdn.Apply(
                fqdn =>
                {
                    Pulumi.Log.Warn($"Opal app location: ws://{fqdn}");
                    return fqdn;
                });

            containerApps.Add(ServiceNames.OpenPolicyAgent, opaApp);
            containerApps.Add(ServiceNames.OpenPolicyAdministrationLayer, opalApp);
            apps.Add($"{ServiceNames.OpenPolicyAgent}-{managedEnvAndAppsTuple.LocationName}", opaApp);
            apps.Add($"{ServiceNames.OpenPolicyAdministrationLayer}-{managedEnvAndAppsTuple.LocationName}", opalApp);
        }

        return (apps, storageAccount);
    }

    private Docker.Image GetOpaImage(string customImageName)
    {
        return ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            customImageName,
            _myConfig.BuildTime);
    }

    private Docker.Image GetOpalImage(string customImageName)
    {
        return ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            customImageName,
            _myConfig.BuildTime);
    }
}