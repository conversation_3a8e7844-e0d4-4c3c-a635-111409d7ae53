﻿using Azure.Storage.Blobs;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Blobs;

public interface IAzureBlobClientFactory
{
    BlobServiceClient GetBlobServiceClient(string blobServiceSasUri);

    BlobClient GetBlobClient(string blobSasUri);
}

public class AzureBlobClientFactory : IAzureBlobClientFactory, ISingletonService
{
    public BlobServiceClient GetBlobServiceClient(string blobServiceSasUri)
    {
        return new BlobServiceClient(blobServiceSasUri);
    }

    public BlobClient GetBlobClient(string blobSasUri)
    {
        var blobClient = new BlobClient(new Uri(blobSasUri));
        return blobClient;
    }
}