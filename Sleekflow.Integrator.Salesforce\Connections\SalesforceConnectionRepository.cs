using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Connections;

public interface ISalesforceConnectionRepository : IRepository<SalesforceConnection>
{
}

public class SalesforceConnectionRepository
    : BaseRepository<SalesforceConnection>,
        ISalesforceConnectionRepository,
        ISingletonService
{
    public SalesforceConnectionRepository(
        ILogger<BaseRepository<SalesforceConnection>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}