using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers;

public class TriggerScheduleWorkflow
{
    private readonly ILogger<TriggerScheduleWorkflow> _logger;

    public TriggerScheduleWorkflow(ILogger<TriggerScheduleWorkflow> logger)
    {
        _logger = logger;
    }

    [Function("TriggerScheduleWorkflow")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<TriggerScheduleWorkflowInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (TriggerScheduleWorkflowInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        _logger.LogInformation("TriggerScheduleWorkflowInput: {TriggerScheduleWorkflowInput}.", JsonConvert.SerializeObject(tuple.Input));
        var (scheduleWorkflowInput, logger, starter) = tuple;

        var orchestratorName = scheduleWorkflowInput.WorkflowVersion switch
        {
            "v2" => "ScheduledDataTimeWorkflow_Orchestrator",  // for scheduled data and time trigger of advanced builder
            _ => "ScheduledWorkflow_Orchestrator_V2"  // for class builder
        };
        _logger.LogInformation(
                "Starting data processing for company {SleekflowCompanyId} with workflow {WorkflowVersionedId} with orchestrator {OrchestratorName}",
                scheduleWorkflowInput.SleekflowCompanyId,
                scheduleWorkflowInput.WorkflowVersionedId,
                orchestratorName);
        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            orchestratorName,
            scheduleWorkflowInput,
            new StartOrchestrationOptions(StartAt: scheduleWorkflowInput.ScheduledDatetime));

        logger.LogInformation(
            "Started {OrchestratorName} with ID = [{InstanceId}]",
            orchestratorName,
            instanceId);

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        logger.LogInformation("Instance {InstanceId} Payload: {Payload}", instanceId, JsonConvert.SerializeObject(httpManagementPayload));
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get ExecuteSleepStep_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}