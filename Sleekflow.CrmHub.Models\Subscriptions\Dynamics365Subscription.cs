﻿using Newtonsoft.Json;
using Sleekflow.DurablePayloads;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;
using Entity = Sleekflow.Persistence.Entity;

namespace Sleekflow.CrmHub.Models.Subscriptions;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("dynamics365_subscription")]
public class Dynamics365Subscription : Entity
{
    public const string SysTypeNameValue = "Dynamics365Subscription";
    public const string PropertyNameLastExecutionStartTime = "last_execution_start_time";
    public const string PropertyNameLastObjectModificationTime = "last_object_modification_time";
    public const string PropertyNameDurablePayload = "durable_payload";

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("interval")]
    public int Interval { get; set; }

    [JsonProperty(PropertyNameLastExecutionStartTime)]
    public DateTimeOffset LastExecutionStartTime { get; set; }

    [JsonProperty(PropertyNameLastObjectModificationTime)]
    public DateTimeOffset? LastObjectModificationTime { get; set; }

    [JsonProperty(PropertyNameDurablePayload)]
    public DurablePayload? DurablePayload { get; set; }

    [JsonConstructor]
    public Dynamics365Subscription(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        int interval,
        DateTimeOffset lastExecutionStartTime,
        DurablePayload? durablePayload,
        DateTimeOffset? lastObjectModificationTime)
        : base(id, SysTypeNameValue)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EntityTypeName = entityTypeName;
        Interval = interval;
        LastExecutionStartTime = lastExecutionStartTime;
        DurablePayload = durablePayload;
        LastObjectModificationTime = lastObjectModificationTime;
    }
}