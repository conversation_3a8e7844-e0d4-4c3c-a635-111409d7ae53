﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetUniqueWorkflowExecutionCount : ITrigger<
    GetUniqueWorkflowExecutionCount.GetUniqueWorkflowExecutionCountInput,
    GetUniqueWorkflowExecutionCount.GetUniqueWorkflowExecutionCountOutput>
{
    public class GetUniqueWorkflowExecutionCountInput : IHasSleekflowCompanyId, IValidatableObject
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("execution_from_date_time")]
        public DateTimeOffset ExecutionFromDateTime { get; set; }

        [Required]
        [JsonProperty("execution_to_date_time")]
        public DateTimeOffset ExecutionToDateTime { get; set; }

        [JsonProperty("workflow_type")]
        public string? WorkflowType { get; set; }

        [JsonConstructor]
        public GetUniqueWorkflowExecutionCountInput(
            string sleekflowCompanyId,
            DateTimeOffset executionFromDateTime,
            DateTimeOffset executionToDateTime,
            string? workflowType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ExecutionFromDateTime = executionFromDateTime;
            ExecutionToDateTime = executionToDateTime;
            WorkflowType = workflowType;
        }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (ExecutionFromDateTime > ExecutionToDateTime)
            {
                yield return new ValidationResult(
                    "ExecutionFromDateTime can't be greater than ExecutionToDateTime",
                    new[]
                    {
                        nameof(ExecutionFromDateTime),
                        nameof(ExecutionToDateTime)
                    });
            }

            if ((ExecutionToDateTime - ExecutionFromDateTime).TotalDays > 31)
            {
                yield return new ValidationResult(
                    "The duration between ExecutionFromDateTime and ExecutionToDateTime can't be more than 31 days",
                    new[]
                    {
                        nameof(ExecutionFromDateTime),
                        nameof(ExecutionToDateTime)
                    });
            }
        }
    }

    private readonly IWorkflowExecutionService _workflowExecutionService;

    public class GetUniqueWorkflowExecutionCountOutput
    {
        [JsonProperty("unique_workflow_execution_count")]
        public int UniqueWorkflowExecutionCount { get; set; }

        [JsonConstructor]
        public GetUniqueWorkflowExecutionCountOutput(
            int uniqueWorkflowExecutionCount)
        {
            UniqueWorkflowExecutionCount = uniqueWorkflowExecutionCount;
        }
    }

    public GetUniqueWorkflowExecutionCount(
        IWorkflowExecutionService workflowExecutionService)
    {
        _workflowExecutionService = workflowExecutionService;
    }

    public async Task<GetUniqueWorkflowExecutionCountOutput> F(GetUniqueWorkflowExecutionCountInput input)
    {
        var uniqueWorkflowExecutionCount = await _workflowExecutionService.GetUniqueWorkflowExecutionCountAsync(
            input.SleekflowCompanyId,
            input.WorkflowType,
            input.ExecutionFromDateTime,
            input.ExecutionToDateTime);

        return new GetUniqueWorkflowExecutionCountOutput(uniqueWorkflowExecutionCount);
    }
}