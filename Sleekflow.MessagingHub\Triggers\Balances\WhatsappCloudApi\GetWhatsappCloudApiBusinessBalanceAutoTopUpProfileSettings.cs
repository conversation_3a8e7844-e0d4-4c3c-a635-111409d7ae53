using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettings
    : ITrigger<
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettings.
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput,
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettings.
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput>
{
    public class GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput
    {
        [JsonProperty("minimum_balances")]
        public List<Money> MinimumBalances { get; set; }

        [JsonProperty("auto_top_up_plans")]
        public List<StripeWhatsAppCreditTopUpPlan> AutoTopUpPlans { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput(
            List<Money> minimumBalances,
            List<StripeWhatsAppCreditTopUpPlan> autoTopUpValues)
        {
            MinimumBalances = minimumBalances;
            AutoTopUpPlans = autoTopUpValues;
        }
    }

    public Task<GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput> F(
        GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput input)
    {
        return Task.FromResult(new GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput(
            AutoTopUpMinimumBalances.MinimumBalances,
            StripeWhatsAppCreditTopUpPlans.AutoTopPlans));
    }
}