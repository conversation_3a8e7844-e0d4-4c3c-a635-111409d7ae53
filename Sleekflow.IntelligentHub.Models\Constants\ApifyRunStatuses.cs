﻿namespace Sleekflow.IntelligentHub.Models.Constants;

/// <summary>
/// https://docs.apify.com/platform/actors/running/runs-and-builTds#lifecycle.
/// </summary>
public static class ApifyRunStatuses
{
    public const string Ready = "READY";
    public const string Running = "RUNNING";
    public const string Succeeded = "SUCCEEDED";
    public const string Failed = "FAILED";
    public const string TimingOut = "TIMING_OUT";
    public const string TimedOut = "TIMED_OUT";
    public const string Aborting = "ABORTING";
    public const string Aborted = "ABORTED";

    public static readonly List<string> TransitionalStatuses = new List<string>
    {
        Ready,
        Running,
        TimingOut,
        Aborting,
    };

    public static readonly List<string> TerminalStatuses = new List<string>
    {
        Succeeded,
        Failed,
        TimedOut,
        Aborted
    };
}