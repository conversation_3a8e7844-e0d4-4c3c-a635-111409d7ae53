using GraphApi.Client.Models.WebhookObjects;
using MassTransit;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Exceptions.Worker;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.OpenTelemetry.MessagingHub;
using Sleekflow.OpenTelemetry.MessagingHub.MeterNames;
using Sleekflow.Webhooks;
using Sleekflow.Webhooks.Events;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.MessagingHub.Webhooks;

public interface IMessagingHubWebhookService
{
    public bool VerifyFacebookWebhookToken(string token);

    Task<bool> HandleWhatsappCloudApiWebhookMessage(string message);

    Task RegisterAsync(Webhook webhook, string wabaPhoneNumberId, CancellationToken cancellationToken = default);

    Task<List<Webhook>> GetWebhooksWithWabaPhoneNumberIdAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default);

    Task<List<Webhook>> GetWebhooksWithSleekflowCompanyIdAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        CancellationToken cancellationToken = default);

    Task SendWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default);

    bool IsSendCloudApiWebhookAsExpressWebhooks(
        string channelCompanyId,
        CloudApiWebhookValueObject cloudApiWebhookValueObject);

    Task SendExpressWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default);

    Task PatchWebhookAsync(string webhooksId, string webhookUrl);

    Task RemoveWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default);
}

public class MessagingHubWebhookService : IMessagingHubWebhookService, ISingletonService
{
    private const string PropertyNameWabaPhoneNumberId = "waba_phone_number_id";

    private readonly IBus _bus;
    private readonly ISecretConfig _secretConfig;
    private readonly IWebhookRepository _webhookRepository;
    private readonly ILogger<MessagingHubWebhookService> _logger;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IMessagingHubMeters _messagingHubMeters;

    public MessagingHubWebhookService(
        IBus bus,
        ISecretConfig secretConfig,
        IWebhookRepository webhookRepository,
        ILogger<MessagingHubWebhookService> logger,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IMessagingHubMeters messagingHubMeters)
    {
        _bus = bus;
        _logger = logger;
        _secretConfig = secretConfig;
        _webhookRepository = webhookRepository;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _messagingHubMeters = messagingHubMeters;
    }

    public bool VerifyFacebookWebhookToken(string token)
    {
        return _secretConfig.FacebookWebhookVerificationToken.Equals(token);
    }

    public async Task<bool> HandleWhatsappCloudApiWebhookMessage(string message)
    {
        _logger.LogInformation(
            "Handling facebook webhook message {Message}",
            message);

        var whatsappCloudApiWebhookMessages = JsonConvert.DeserializeObject<WhatsappCloudApiWebhookMessages>(message);
        await HandleWhatsappCloudApiWebhookMessagesAsync(whatsappCloudApiWebhookMessages);

        return true;
    }

    public async Task RegisterAsync(
        Webhook webhook,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksWithWabaPhoneNumberIdAsync(
            webhook.SleekflowCompanyId,
            webhook.EntityTypeName,
            webhook.EventTypeName,
            wabaPhoneNumberId,
            cancellationToken);

        if (webhooks.Any())
        {
            throw new SfDuplicateWebhookException(webhook.EntityTypeName, webhook.EventTypeName);
        }

        webhook.Context = new Dictionary<string, object?>
        {
            {
                PropertyNameWabaPhoneNumberId, wabaPhoneNumberId
            }
        };

        webhook.MaxRetryCount = 5;

        await _webhookRepository.CreateAsync(
            webhook,
            webhook.Id,
            cancellationToken: cancellationToken);
    }

    public async Task<List<Webhook>> GetWebhooksWithWabaPhoneNumberIdAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT * " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.sleekflow_company_id = @sleekflowCompanyId AND e.entity_type_name = @entityTypeName " +
                "AND e.event_type_name = @eventTypeName AND e.context[@PropertyNameWabaPhoneNumberId] = @WabaPhoneNumberId")
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@entityTypeName", entityTypeName)
            .WithParameter("@eventTypeName", eventTypeName)
            .WithParameter("@PropertyNameWabaPhoneNumberId", PropertyNameWabaPhoneNumberId)
            .WithParameter("@WabaPhoneNumberId", wabaPhoneNumberId);

        return await _webhookRepository.GetObjectsAsync(
            queryDefinition,
            cancellationToken: cancellationToken);
    }

    public async Task<List<Webhook>> GetWebhooksWithSleekflowCompanyIdAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        CancellationToken cancellationToken = default)
    {
        var queryDefinition = new QueryDefinition(
                "SELECT * " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.sleekflow_company_id = @sleekflowCompanyId AND e.entity_type_name = @entityTypeName " +
                "AND e.event_type_name = @eventTypeName")
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@entityTypeName", entityTypeName)
            .WithParameter("@eventTypeName", eventTypeName);

        return await _webhookRepository.GetObjectsAsync(
            queryDefinition,
            cancellationToken: cancellationToken);
    }

    public async Task SendWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksWithWabaPhoneNumberIdAsync(
            sleekflowCompanyId,
            entityTypeName,
            eventTypeName,
            wabaPhoneNumberId,
            cancellationToken);

        var events = webhooks
            .Select(
                webhook => new OnWebhookRequestedEvent(
                    webhook.SleekflowCompanyId,
                    webhook.Context,
                    payloadObj,
                    webhook))
            .ToList();

        await _bus.PublishBatch(
            events,
            cancellationToken: cancellationToken);
    }

    public bool IsSendCloudApiWebhookAsExpressWebhooks(
        string channelCompanyId,
        CloudApiWebhookValueObject cloudApiWebhookValueObject)
    {
        return channelCompanyId
                   is "5bf3f9e2-e893-416b-a0e2-0c5c481b9918" // Cosmax Survey
                   or "820a5a3f-1f6a-4d58-8247-cb8b81655c80" // COSMAX
               || (cloudApiWebhookValueObject.Messages != null && cloudApiWebhookValueObject.Messages.Any());
    }

    public async Task SendExpressWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        object payloadObj,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksWithWabaPhoneNumberIdAsync(
            sleekflowCompanyId,
            entityTypeName,
            eventTypeName,
            wabaPhoneNumberId,
            cancellationToken);

        var events = webhooks
            .Select(
                webhook => new OnExpressWebhookRequestedEvent(
                    webhook.SleekflowCompanyId,
                    webhook.Context,
                    payloadObj,
                    webhook))
            .ToList();

        await _bus.PublishBatch(
            events,
            cancellationToken: cancellationToken);
    }

    public async Task PatchWebhookAsync(string webhooksId, string webhookUrl)
    {
        var patchWebhook = await _webhookRepository.PatchAsync(
            webhooksId,
            webhooksId,
            new List<PatchOperation>
            {
                Replace(Webhook.PropertyNameUrl, webhookUrl)
            });
        if (patchWebhook == 0)
        {
            throw new SfWebhookProcessingException(webhooksId);
        }
    }

    public async Task RemoveWebhooksAsync(
        string sleekflowCompanyId,
        string entityTypeName,
        string eventTypeName,
        string wabaPhoneNumberId,
        CancellationToken cancellationToken = default)
    {
        var webhooks = await GetWebhooksWithWabaPhoneNumberIdAsync(
            sleekflowCompanyId,
            entityTypeName,
            eventTypeName,
            wabaPhoneNumberId,
            cancellationToken);

        foreach (var webhookId in webhooks.Select(w => w.Id).Distinct())
        {
            await _webhookRepository.DeleteAsync(
                webhookId,
                webhookId,
                cancellationToken: cancellationToken);
        }
    }

    private async Task HandleWhatsappCloudApiWebhookMessagesAsync(WhatsappCloudApiWebhookMessages webhookMessages)
    {
        foreach (var entry in webhookMessages.WhatsappCloudApiEntries)
        {
            foreach (var change in entry.Changes)
            {
                switch (change.Field)
                {
                    case WhatsappCloudApiWebhookTypeNames.Messages:
                        await PublishOnCloudApiHandleMessagesWebhookEventAsync(
                            new OnCloudApiHandleMessagesWebhookEvent(
                                entry.FacebookWabaId,
                                webhookMessages,
                                entry,
                                change));
                        _messagingHubMeters.IncrementCounter(
                            MessagingHubChannelMeterNames.WhatsappCloudApi,
                            MessagingHubWebhookMeterOptions.MessagesWebhookReceived);
                        break;
                    case WhatsappCloudApiWebhookTypeNames.AccountAlerts:
                    case WhatsappCloudApiWebhookTypeNames.AccountReviewUpdate:
                    case WhatsappCloudApiWebhookTypeNames.AccountUpdate:
                    case WhatsappCloudApiWebhookTypeNames.BusinessCapabilityUpdate:
                    case WhatsappCloudApiWebhookTypeNames.BusinessStatusUpdate:
                    case WhatsappCloudApiWebhookTypeNames.TemplateCategoryUpdate:
                    case WhatsappCloudApiWebhookTypeNames.MessageTemplateQualityUpdate:
                    case WhatsappCloudApiWebhookTypeNames.MessageTemplateStatusUpdate:
                    case WhatsappCloudApiWebhookTypeNames.PhoneNumberNameUpdate:
                    case WhatsappCloudApiWebhookTypeNames.PhoneNumberQualityUpdate:
                    case WhatsappCloudApiWebhookTypeNames.Security:
                        await PublishOnCloudApiHandleWebhookStatusUpdateEventAsync(
                            new OnCloudApiHandleWebhookStatusUpdateEvent(
                                entry.FacebookWabaId,
                                entry,
                                change));
                        break;
                    case WhatsappCloudApiWebhookTypeNames.DetectedOutcomes:
                        await PublishOnCloudApiHandleDetectedOutcomeWebhookEventAsync(
                            new OnCloudApiHandleMetaDetectedOutcomeWebhookEvent(
                                entry.FacebookWabaId,
                                webhookMessages,
                                entry,
                                change));
                        break;
                    default:
                        _logger.LogWarning(
                            "Received unhandled field type webhook from Cloud Api. Field type:{FieldType}, WabaId:{WabaId}, Webhook:{Webhook} ",
                            change.Field,
                            entry.FacebookWabaId,
                            JsonConvert.SerializeObject(webhookMessages));
                        break;
                }

                _applicationInsightsTelemetryTracer.TraceEvent(
                    TraceEventNames.WhatsappCloudApiWebhookReceived,
                    new Dictionary<string, string>()
                    {
                        {
                            "webhook_type", change.Field
                        },
                    });
                _messagingHubMeters.IncrementCounter(
                    MessagingHubChannelMeterNames.WhatsappCloudApi,
                    MessagingHubWebhookMeterOptions.WebhookReceived);
            }
        }
    }

    private async Task PublishOnCloudApiHandleMessagesWebhookEventAsync(
        OnCloudApiHandleMessagesWebhookEvent onCloudApiHandleMessagesWebhookEvent,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _bus.Publish(
                onCloudApiHandleMessagesWebhookEvent,
                cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Unable to publish OnCloudApiHandleMessageWebhookEvent:{OnCloudApiHandleMessageWebhookEvent}",
                JsonConvert.SerializeObject(onCloudApiHandleMessagesWebhookEvent));
        }
    }

    private async Task PublishOnCloudApiHandleWebhookStatusUpdateEventAsync(
        OnCloudApiHandleWebhookStatusUpdateEvent onCloudApiHandleWebhookStatusUpdateEvent,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _bus.Publish(
                onCloudApiHandleWebhookStatusUpdateEvent,
                cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Unable to publish onCloudApiHandleWebhookStatusUpdateEvent:{OnCloudApiHandleWebhookStatusUpdateEvent}",
                JsonConvert.SerializeObject(onCloudApiHandleWebhookStatusUpdateEvent));
        }
    }

    private async Task PublishOnCloudApiHandleDetectedOutcomeWebhookEventAsync(
        OnCloudApiHandleMetaDetectedOutcomeWebhookEvent onCloudApiHandleMetaDetectedOutcomeWebhookEvent,
        CancellationToken cancellationToken = default)
    {
        try
        {
            await _bus.Publish(
                onCloudApiHandleMetaDetectedOutcomeWebhookEvent,
                cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Unable to publish OnCloudApiHandleMetaDetectedOutcomeWebhookEvent:{OnCloudApiHandleMetaDetectedOutcomeWebhookEvent}",
                JsonConvert.SerializeObject(onCloudApiHandleMetaDetectedOutcomeWebhookEvent));
        }
    }
}