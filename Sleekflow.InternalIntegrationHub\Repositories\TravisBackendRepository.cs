using Dapper;
using Sleekflow.DependencyInjection;
using Sleekflow.**********************.Configs;
using Sleekflow.**********************.Database;
using Sleekflow.**********************.Models.Constants.TravisBackend;
using Sleekflow.**********************.Models.TravisBackend;

namespace Sleekflow.**********************.Repositories;

public interface ITravisBackendRepository
{
    public Task<List<Company>> GetCompanies(int offset, string location);

    public Task<CompanyOwner?> GetCompanyOwner(string companyId, string location);

    public Task<CompanyOwner?> GetCompanySalesRepresentative(string cmsActivationOwnerId, string location);

    public Task<List<CompanyBillRecord>?> GetCompanyBillRecords(string companyId, string location);

    public Task<Company?> GetCompanyById(string companyId, string location);

    public Task<int> UpsertPaymentRecord(PaymentRecord paymentRecord, string location);

    public Task<List<PaymentRecord>> GetPaymentRecords(string billRecordId, string location);
}

public class TravisBackendRepository : IScopedService, ITravisBackendRepository
{
    private readonly ILogger<TravisBackendRepository> _logger;
    private readonly TravisDatabase _travisDatabase;

    public TravisBackendRepository(ILogger<TravisBackendRepository> logger, ITravisDatabaseConfigs databaseConfigs)
    {
        _logger = logger;
        _travisDatabase = new TravisDatabase(databaseConfigs);
    }


    public async Task<List<Company>> GetCompanies(int offset, string location)
    {
        var sql =
            "SELECT id, CompanyName, CompanyCountry, CmsActivationOwnerId FROM CompanyCompanies WHERE IsDeleted = 0 ORDER BY CreatedAt OFFSET @offset ROWS FETCH NEXT 1000 ROWS ONLY";
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return [];
        }

        var companies = (await connection.QueryAsync<Company>(sql, offset)).ToList();

        return companies;
    }

    public async Task<CompanyOwner?> GetCompanyOwner(string companyId, string location)
    {
        var sql =
            "SELECT DisplayName, Email, PhoneNumber FROM AspNetUsers WHERE Id IN( SELECT TOP 1 IdentityId FROM UserRoleStaffs WHERE CompanyId = @companyId order by [Order], id)";
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return null;
        }

        var companyOwner = await connection.QueryFirstOrDefaultAsync<CompanyOwner>(
            sql,
            new
            {
                companyId
            });
        return companyOwner;
    }

    public async Task<CompanyOwner?> GetCompanySalesRepresentative(string cmsActivationOwnerId, string location)
    {
        var sql = "SELECT DisplayName, Email, PhoneNumber FROM AspNetUsers WHERE Id = @cmsActivationOwnerId";
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return null;
        }

        var salesRep = await connection.QueryFirstOrDefaultAsync<CompanyOwner>(
            sql,
            new
            {
                cmsActivationOwnerId
            });
        return salesRep;
    }

    public async Task<List<CompanyBillRecord>?> GetCompanyBillRecords(string companyId, string location)
    {
        var sql =
            "SELECT Id, SubscriptionPlanId, PeriodStart, PeriodEnd, PayAmount, PaymentStatus ,Currency, stripe_subscriptionId AS StripeSubscriptionId, created AS Created FROM CompanyBillRecords where CompanyId = @companyId order by created desc";
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return null;
        }

        var billRecords = (await connection.QueryAsync<CompanyBillRecord>(
            sql,
            new
            {
                companyId
            })).ToList();
        try
        {
            var validRecord = billRecords.Where(b => Plans.AllSubscriptionPlans.Contains(b.SubscriptionPlanId)).ToList();
            return validRecord;
        }
        catch (Exception e)
        {
            return null;
        }
    }

    public async Task<Company?> GetCompanyById(string companyId, string location)
    {
        var sql =
            "SELECT id, CompanyName, CompanyCountry, CmsActivationOwnerId FROM CompanyCompanies WHERE id = @companyId";
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return null;
        }

        var company = await connection.QueryFirstOrDefaultAsync<Company>(sql, new { companyId });
        return company;
    }

    public async Task<int> UpsertPaymentRecord(PaymentRecord paymentRecord, string location)
    {
        var sql =
            """
            IF NOT EXISTS (SELECT 1 FROM CmsSalesPaymentRecords WHERE CompanyId = @CompanyId AND BillRecordId = @BillRecordId AND InvoiceId = @InvoiceId)
                        INSERT INTO CmsSalesPaymentRecords (CompanyId, BillRecordId, SubscriptionFee, OneTimeSetupFee, WhatsappCreditAmount, Currency, PaymentMethod, PaidAt, InvoiceId, LastModifiedDate, CreatedAt, PaymentTerm) VALUES (@CompanyId, @BillRecordId, @SubscriptionFee, @OneTimeSetupFee, @WhatsappCreditAmount, @Currency, @PaymentMethod, @PaidAt, @InvoiceId, @LastModifiedDate, @CreatedAt, @PaymentTermInt)
                    ELSE
                        UPDATE CmsSalesPaymentRecords SET
                            SubscriptionFee = COALESCE(@SubscriptionFee, SubscriptionFee),
                            OneTimeSetupFee = COALESCE(@OneTimeSetupFee, OneTimeSetupFee),
                            WhatsappCreditAmount = COALESCE(@WhatsappCreditAmount, WhatsappCreditAmount),
                            Currency = COALESCE(@Currency, Currency),
                            PaymentMethod = COALESCE(@PaymentMethod, PaymentMethod),
                            PaidAt = COALESCE(@PaidAt, PaidAt),
                            InvoiceId = COALESCE(@InvoiceId, InvoiceId),
                            LastModifiedDate = @LastModifiedDate,
                            CreatedAt = @CreatedAt,
                            PaymentTermInt = COALESCE(@PaymentTermInt, PaymentTermInt)
                        WHERE CompanyId = @CompanyId AND BillRecordId = @BillRecordId
            """;
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return 0;
        }

        var rowsAffected = await connection.ExecuteAsync(sql, paymentRecord);
        return rowsAffected;
    }

    public async Task<List<PaymentRecord>> GetPaymentRecords(string billRecordId, string location)
    {
        var sql =
            "SELECT CompanyId, BillRecordId, SubscriptionFee, OneTimeSetupFee, WhatsappCreditAmount, Currency, PaymentMethod, PaidAt, InvoiceId, LastModifiedDate, CreatedAt, PaymentTermInt FROM CmsSalesPaymentRecords WHERE BillRecordId = @billRecordId";
        var connection = _travisDatabase.GetConnectionByRegion(location);
        if (connection == null)
        {
            return new List<PaymentRecord>();
        }

        var paymentRecords = (await connection.QueryAsync<PaymentRecord>(
            sql,
            new
            {
                billRecordId
            })).ToList();
        return paymentRecords;
    }
}