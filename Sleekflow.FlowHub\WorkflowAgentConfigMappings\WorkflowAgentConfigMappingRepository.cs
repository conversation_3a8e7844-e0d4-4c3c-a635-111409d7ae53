using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.WorkflowAgentConfigMappings;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.WorkflowAgentConfigMappings;

public interface IWorkflowAgentConfigMappingRepository : IRepository<WorkflowAgentConfigMapping>
{
    Task<List<WorkflowAgentConfigMapping>> GetWorkflowIdsByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId);

    Task<List<WorkflowAgentConfigMapping>> GetAgentConfigIdsByWorkflowIdAsync(
        string sleekflowCompanyId,
        string workflowId);

    Task<List<WorkflowAgentConfigMapping>> BatchCreateAsync(
       List<WorkflowAgentConfigMapping> mappings,
       string sleekflowCompanyId);

    Task BatchDeleteByWorkflowIdAsync(
        string sleekflowCompanyId,
        string workflowId);
}

public class WorkflowAgentConfigMappingRepository : BaseRepository<WorkflowAgentConfigMapping>, IWorkflowAgentConfigMappingRepository, IScopedService
{
    public WorkflowAgentConfigMappingRepository(
        ILogger<WorkflowAgentConfigMappingRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<List<WorkflowAgentConfigMapping>> GetWorkflowIdsByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId)
    {
        var queryDefinition = new QueryDefinition(
        "SELECT * FROM c WHERE c.sleekflow_company_id = @sleekflowCompanyId AND c.agent_config_id = @agentConfigId")
        .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
        .WithParameter("@agentConfigId", agentConfigId);

        var mappings = await GetObjectsAsync(queryDefinition);

        if (mappings == null || !mappings.Any())
        {
            return new List<WorkflowAgentConfigMapping>();
        }

        return mappings;
    }

    public async Task<List<WorkflowAgentConfigMapping>> GetAgentConfigIdsByWorkflowIdAsync(
        string sleekflowCompanyId,
        string workflowId)
    {
        var queryDefinition = new QueryDefinition(
            "SELECT * FROM c WHERE c.sleekflow_company_id = @sleekflowCompanyId AND c.workflow_id = @workflowId")
            .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@workflowId", workflowId);

        var mappings = await GetObjectsAsync(queryDefinition);

        if (mappings == null || !mappings.Any())
        {
            return new List<WorkflowAgentConfigMapping>();
        }

        return mappings;
    }

    public async Task<List<WorkflowAgentConfigMapping>> BatchCreateAsync(
       List<WorkflowAgentConfigMapping> mappings,
       string sleekflowCompanyId)
    {
        if (mappings == null || !mappings.Any())
        {
            return new List<WorkflowAgentConfigMapping>();
        }

        var response = await ExecuteTransactionalBatchAsync(
            sleekflowCompanyId,
            async batch =>
            {
                foreach (var mapping in mappings)
                {
                    batch.CreateItem(mapping);
                }
            });

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"Failed to batch create workflow agent config mappings: {response.StatusCode}");
        }

        return mappings;
    }

    public async Task BatchDeleteByWorkflowIdAsync(
        string sleekflowCompanyId,
        string workflowId)
    {
        var existingMappings = await GetAgentConfigIdsByWorkflowIdAsync(sleekflowCompanyId, workflowId);

        if (!existingMappings.Any())
        {
            return;
        }

        var response = await ExecuteTransactionalBatchAsync(
            sleekflowCompanyId,
            async batch =>
            {
                foreach (var mapping in existingMappings)
                {
                    batch.DeleteItem(mapping.Id);
                }
            });

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"Failed to batch delete workflow agent config mappings for workflow {workflowId}: {response.StatusCode}");
        }
    }
}