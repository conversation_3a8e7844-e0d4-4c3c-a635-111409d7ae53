using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Abstractions;

public interface IStepExecutor
{
    public bool IsMatched(
        Step step);

    public Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync);

    public Task<Step?> OnStepCompleteAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries);
}