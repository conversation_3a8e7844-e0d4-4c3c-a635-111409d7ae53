# Project Structure

## Directory Layout

```
Sleekflow.UserEventAnalyticsHub.DataCompactor/
├── Program.cs                          # Main entry point with 3 modes: migration, discovery, SQL testing
├── appsettings.json                    # Configuration settings
├── appsettings.Development.json        # Development-specific settings
├── setup-test-env.sh                  # Interactive environment setup script
├── Configs/
│   ├── CompactorConfig.cs             # Azure Storage and processing configuration
│   ├── ICompactorConfig.cs            # Configuration interface
│   ├── PostgreSqlConfig.cs            # PostgreSQL connection configuration
│   └── IPostgreSqlConfig.cs           # PostgreSQL configuration interface
├── Services/
│   ├── BlobDiscoveryService.cs        # Discovers Parquet files in Azure Data Lake Storage
│   ├── PostgreSqlSchemaService.cs     # Manages PostgreSQL schema and table creation
│   ├── FileTrackingService.cs         # Tracks migration progress and status
│   ├── ParquetCompactionService.cs    # Main migration orchestration service
│   ├── SqlJobProcessingService.cs     # 🆕 PostgreSQL-based query processor for testing
│   ├── MockSqlJobService.cs           # 🆕 Mock SQL job service for isolated testing
│   └── MockLockService.cs             # 🆕 Mock locking service for testing
├── Models/
│   ├── BlobFileInfo.cs                # Represents blob file metadata
│   ├── MigrationStatus.cs             # Enumeration for migration states
│   ├── CompanyMigrationSummary.cs     # Summary of company migration progress
│   └── ProcessingResult.cs            # Result of file processing operations
├── Extensions/
│   ├── ServiceCollectionExtensions.cs # DI container configuration with all services
│   └── DuckDbExtensions.cs           # DuckDB helper methods with PostgreSQL integration
├── Utils/
│   ├── PostgreSqlTableNameHelper.cs   # Helper for generating sanitized table names
│   ├── BlobPathParser.cs              # Parses blob paths to extract metadata
│   └── DataIntegrityValidator.cs      # Validates data consistency
└── Documentation/
    ├── README.md                       # Project overview and usage
    ├── QUICK_START_TESTING.md          # 5-minute getting started guide
    ├── PROJECT_STRUCTURE.md            # This file - project architecture
    ├── IMPLEMENTATION_PLAN.md          # Detailed implementation guidance
    └── TESTING_GUIDE.md               # Comprehensive testing instructions
```

## Key Classes and Interfaces

### Configuration Classes

```csharp
// Configs/ICompactorConfig.cs
public interface ICompactorConfig
{
    string StorageAccountName { get; }
    string StorageAccountKey { get; }
    string EventsContainerName { get; }
    string StorageConnStr { get; }
    string ResultsContainerName { get; }
    int MaxConcurrentCompanies { get; }
    int FileBatchSize { get; }
    int RetryAttempts { get; }
    int ProcessingTimeoutMinutes { get; }
}

// Configs/CompactorConfig.cs
public class CompactorConfig : IConfig, ICompactorConfig
{
    public string StorageAccountName { get; }
    public string StorageAccountKey { get; }
    public string EventsContainerName { get; }
    public string StorageConnStr { get; }
    public string ResultsContainerName { get; }
    public int MaxConcurrentCompanies { get; }
    public int FileBatchSize { get; }
    public int RetryAttempts { get; }
    public int ProcessingTimeoutMinutes { get; }
}

// Configs/IPostgreSqlConfig.cs (aligned with UserEventHub)
public interface IPostgreSqlConfig
{
    string ConnectionString { get; }
    string ServerName { get; }
    string DatabaseName { get; }
}

// Configs/PostgreSqlConfig.cs
public class PostgreSqlConfig : IConfig, IPostgreSqlConfig
{
    public string ConnectionString { get; }
    public string ServerName { get; }
    public string DatabaseName { get; }
}
```

### Service Interfaces

#### Migration Services

```csharp
// Services/IBlobDiscoveryService.cs
public interface IBlobDiscoveryService
{
    Task<List<BlobFileInfo>> DiscoverCompanyFilesAsync(string sleekflowCompanyId);
    Task<List<string>> GetAllCompanyIdsAsync();
    Task<BlobFileInfo> GetBlobMetadataAsync(string blobPath);
}

// Services/IPostgreSqlSchemaService.cs
public interface IPostgreSqlSchemaService
{
    Task EnsureCompanyTablesExistAsync(string sleekflowCompanyId);
    Task<bool> ValidateTableSchemaAsync(string sleekflowCompanyId);
    Task CreateIndexesAsync(string sleekflowCompanyId);
}

// Services/IFileTrackingService.cs
public interface IFileTrackingService
{
    Task<List<string>> GetProcessedFilePathsAsync(string sleekflowCompanyId);
    Task<List<string>> GetUnprocessedFilePathsAsync(string sleekflowCompanyId, List<BlobFileInfo> allFiles);
    Task RecordMigrationAttemptAsync(string sleekflowCompanyId, BlobFileInfo fileInfo);
    Task RecordMigrationSuccessAsync(string sleekflowCompanyId, string blobPath, int recordCount);
    Task RecordMigrationFailureAsync(string sleekflowCompanyId, string blobPath, string errorMessage);
}

// Services/IParquetCompactionService.cs
public interface IParquetCompactionService
{
    Task<CompanyMigrationSummary> ProcessCompanyAsync(string sleekflowCompanyId);
    Task<ProcessingResult> ProcessFileBatchAsync(string sleekflowCompanyId, List<BlobFileInfo> files);
    Task<bool> ValidateDataIntegrityAsync(string sleekflowCompanyId);
}
```

#### 🆕 Testing Services

```csharp
// From Sleekflow.UserEventAnalyticsHub (copied and modified)
public interface ISqlJobProcessingService
{
    Task<CsvJobResult> ProcessUserEventAnalyticsJobAsync(
        string sleekflowCompanyId,
        string sqlJobId,
        string sql,
        CancellationToken cancellationToken);
}

// Services/MockSqlJobService.cs
public interface ISqlJobService
{
    Task<SqlJob?> GetAsync(string id, CancellationToken cancellationToken = default);
    Task<SqlJob> CreateAsync(SqlJob sqlJob, CancellationToken cancellationToken = default);
    Task<SqlJob> UpdateAsync(SqlJob sqlJob, CancellationToken cancellationToken = default);
}

// Services/MockLockService.cs
public interface ILockService
{
    Task<ILock?> TryAcquireReadLockAsync(string name, CancellationToken cancellationToken);
    Task<ILock?> TryAcquireWriteLockAsync(string name, CancellationToken cancellationToken);
    Task<ILock?> AcquireReadLockAsync(string name, CancellationToken cancellationToken);
    Task<ILock?> AcquireWriteLockAsync(string name, CancellationToken cancellationToken);
}
```

### Model Classes

```csharp
// Models/BlobFileInfo.cs
public class BlobFileInfo
{
    public string BlobPath { get; set; }
    public DateTime LastModified { get; set; }
    public long SizeBytes { get; set; }
    public string SleekflowCompanyId { get; set; }
    public int Year { get; set; }
    public int Month { get; set; }
    public int Day { get; set; }
    public int Hour { get; set; }
}

// Models/MigrationStatus.cs
public enum MigrationStatus
{
    Pending,
    InProgress,
    Completed,
    Failed
}

// Models/CompanyMigrationSummary.cs
public class CompanyMigrationSummary
{
    public string SleekflowCompanyId { get; set; }
    public int TotalFiles { get; set; }
    public int ProcessedFiles { get; set; }
    public int FailedFiles { get; set; }
    public long TotalRecords { get; set; }
    public TimeSpan ProcessingDuration { get; set; }
    public List<string> Errors { get; set; } = new();
}

// Models/ProcessingResult.cs
public class ProcessingResult
{
    public bool Success { get; set; }
    public int FilesProcessed { get; set; }
    public long RecordsProcessed { get; set; }
    public string ErrorMessage { get; set; }
    public TimeSpan Duration { get; set; }
}
```

## Implementation Status

### ✅ Phase 1: Core Infrastructure (Complete)
1. `Program.cs` - Host configuration with 3 modes: migration, discovery, SQL testing
2. `Configs/` classes - Configuration management with interfaces
3. `Extensions/ServiceCollectionExtensions.cs` - Service registration for all components

### ✅ Phase 2: Discovery and Schema Management (Complete)
4. `Services/BlobDiscoveryService.cs` - Azure blob file discovery
5. `Services/PostgreSqlSchemaService.cs` - PostgreSQL schema management
6. `Models/` classes - Data transfer objects
7. `Utils/PostgreSqlTableNameHelper.cs` - Table name sanitization

### ✅ Phase 3: Migration Logic (Complete)
8. `Services/FileTrackingService.cs` - Migration progress tracking
9. `Services/ParquetCompactionService.cs` - Main migration orchestration
10. `Extensions/DuckDbExtensions.cs` - DuckDB PostgreSQL integration

### ✅ Phase 4: Testing and Performance (Complete)
11. `Services/SqlJobProcessingService.cs` - PostgreSQL-based query testing
12. `Services/MockSqlJobService.cs` - Mock services for isolated testing
13. `Services/MockLockService.cs` - Mock locking for testing
14. Performance validation and metrics collection

### 🎯 Next: Production Optimization
15. Resource usage optimization based on test results
16. Automated monitoring and alerting setup
17. Production deployment configuration
18. Data cleanup automation after successful migration

## Key Dependencies

### NuGet Packages (Current)
```xml
<PackageReference Include="DuckDB.NET.Data.Full" Version="1.2.0" />
<PackageReference Include="Azure.Storage.Files.DataLake" Version="12.18.0" />
<PackageReference Include="Azure.Storage.Blobs" Version="12.18.0" />
<PackageReference Include="CsvHelper" Version="33.0.1" />
<PackageReference Include="Dapper" Version="2.1.66" />
<PackageReference Include="Npgsql" Version="8.0.3" />
<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
<PackageReference Include="Polly" Version="8.5.2" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
```

### Sleekflow Project References (Current)
```xml
<ProjectReference Include="../Sleekflow/Sleekflow.csproj" />
<ProjectReference Include="../Sleekflow.Models/Sleekflow.Models.csproj" />
<ProjectReference Include="../Sleekflow.UserEventHub.Models/Sleekflow.UserEventHub.Models.csproj" />
```

## Configuration

### Environment Variables (Required)

The service is configured entirely through environment variables for flexibility and security:

#### Migration Mode
```bash
# Azure Storage
export ANALYTICS_STORAGE_ACCOUNT_NAME="your-storage-account"
export ANALYTICS_STORAGE_ACCOUNT_KEY="your-storage-key"
export EVENTS_CONTAINER_NAME="events"

# PostgreSQL
export POSTGRES_CONNECTION_STRING="Host=host;Database=db;Username=user;Password=****"
export POSTGRES_SERVER_NAME="your-postgres-server"
export POSTGRES_DATABASE_NAME="sleekflow_events"

# Processing (Optional)
export COMPACTOR_MAX_CONCURRENT_COMPANIES="3"
export COMPACTOR_FILE_BATCH_SIZE="100"
export COMPACTOR_RETRY_ATTEMPTS="3"
export COMPACTOR_PROCESSING_TIMEOUT_MINUTES="30"
```

#### Testing Mode (Additional)
```bash
# For SQL Job Testing
export ANALYTICS_STORAGE_CONN_STR="your-full-connection-string"
export RESULTS_CONTAINER_NAME="results"

# Test Configuration
export TEST_COMPANY_ID="company-123"
export TEST_JOB_ID="job-456"
export TEST_SQL_QUERY="SELECT COUNT(*) FROM events"
```

#### Control Flags
```bash
# Modes
export COMPACTOR_DISCOVERY_ONLY="true"          # Discovery without processing
export COMPACTOR_SQL_JOB_TEST="true"            # SQL performance testing
export COMPACTOR_TEST_SCHEMA_CREATION="true"    # Test schema creation

# Discovery Settings
export COMPACTOR_SHOW_FILE_DETAILS="true"       # Show detailed file info
export COMPACTOR_MAX_DISCOVERY_COMPANIES="5"    # Limit discovery scope
export COMPACTOR_MAX_FILES_TO_SHOW="3"          # Files to show per company
```

### appsettings.json (Basic Logging Only)
```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
```

> **Note**: All operational configuration is done via environment variables to support containerized deployments and secure credential management.

## Usage Examples

### Quick Testing Workflow
```bash
# 1. Set up environment
export POSTGRES_CONNECTION_STRING="Host=localhost;Database=sleekflow_events;Username=user;Password=****"
export POSTGRES_SERVER_NAME="localhost"
export POSTGRES_DATABASE_NAME="sleekflow_events"
export ANALYTICS_STORAGE_ACCOUNT_NAME="test-storage"
export ANALYTICS_STORAGE_ACCOUNT_KEY="test-key"

# 2. Discovery mode
export COMPACTOR_DISCOVERY_ONLY="true"
dotnet run

# 3. SQL testing mode
unset COMPACTOR_DISCOVERY_ONLY
export COMPACTOR_SQL_JOB_TEST="true"
export TEST_COMPANY_ID="company-123"
dotnet run

# 4. Migration mode (default)
unset COMPACTOR_SQL_JOB_TEST
dotnet run
```

This structure provides a robust, production-ready data compaction service with comprehensive testing capabilities and clear separation between migration and testing concerns.