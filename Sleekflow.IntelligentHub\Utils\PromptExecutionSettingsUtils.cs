using System.Text.Json;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.Google;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using OpenAI.Chat;

namespace Sleekflow.IntelligentHub.Utils;

public static class PromptExecutionSettingsUtils
{
    public record GeminiJsonSchema(
        [property: JsonProperty("type")]
        string Type,
        [property: JsonProperty("required")]
        string[] Required,
        [property: JsonProperty("propertyOrdering")]
        string[] PropertyOrdering,
        [property: JsonProperty("properties")]
        Dictionary<string, GeminiJsonSchemaProperty> Properties);

    public record GeminiJsonSchemaProperty(
        [property: JsonProperty("type")]
        string Type,
        [property: JsonProperty("nullable")]
        bool Nullable = false,
        [property: JsonProperty("properties")]
        Dictionary<string, GeminiJsonSchemaProperty>? Properties = null,
        [property: JsonProperty("items")]
        GeminiJsonSchemaProperty? Items = null,
        [property: JsonProperty("propertyOrdering")]
        string[]? PropertyOrdering = null,
        [property: JsonProperty("required")]
        string[]? Required = null);

    public record Property(
        string Name,
        string Type,
        bool Nullable = false,
        List<Property>? Properties = null,
        Property? Items = null);

    public static void EnrichPromptExecutionSettingsWithStructuredOutput(
        PromptExecutionSettings settings,
        List<Property> properties)
    {
#pragma warning disable SKEXP0070
        if (settings is GeminiPromptExecutionSettings geminiPromptExecutionSettings)
        {
            var jsonSchema = new GeminiJsonSchema(
                "object",
                properties.Where(p => !p.Nullable).Select(p => p.Name).ToArray(),
                properties.Select(p => p.Name).ToArray(),
                CreateGeminiPropertiesDictionary(properties));

            var outputSchema = JsonConvert.SerializeObject(jsonSchema);

            geminiPromptExecutionSettings.ResponseMimeType = "application/json";
            geminiPromptExecutionSettings.ResponseSchema = JsonDocument.Parse(outputSchema);
        }
#pragma warning restore SKEXP0070
#pragma warning disable SKEXP0010
        else if (settings is OpenAIPromptExecutionSettings openAiPromptExecutionSettings)
        {
            var jsonSchema = CreateOpenAiJsonSchema(properties);

            openAiPromptExecutionSettings.ResponseFormat = ChatResponseFormat.CreateJsonSchemaFormat(
                jsonSchemaFormatName: "StructuredOutput",
                jsonSchema: BinaryData.FromString(jsonSchema),
                jsonSchemaIsStrict: true);
        }
#pragma warning restore SKEXP0010
    }

    private static string CreateOpenAiJsonSchema(List<Property> properties)
    {
        // Root schema object
        var schema = new Dictionary<string, object>
        {
            {
                "$schema", "http://json-schema.org/draft-07/schema#"
            },
            {
                "type", "object"
            },
            {
                "additionalProperties", false
            }, // Required for structured outputs
            {
                "strict", true
            } // Ensure strict adherence to schema
        };

        // Create properties dictionary
        var propertiesDict = new Dictionary<string, object>();

        // ALWAYS include ALL properties in required array regardless of nullable
        var requiredProperties = properties.Select(p => p.Name).ToList();

        foreach (var property in properties)
        {
            var propSchema = CreateOpenAiPropertySchema(property);

            // If the property is nullable, we need to handle that with type, but it's still required
            if (property.Nullable)
            {
                propSchema["type"] = new[]
                {
                    "null",
                    property.Type.ToLowerInvariant()
                };
            }

            propertiesDict[property.Name] = propSchema;
        }

        // Add properties to root schema
        schema["properties"] = propertiesDict;

        // Always add required array with all properties
        schema["required"] = requiredProperties;

        return JsonConvert.SerializeObject(schema, Formatting.Indented);
    }

    private static Dictionary<string, object> CreateOpenAiPropertySchema(Property property)
    {
        var schema = new Dictionary<string, object>();

        if (property.Type.Equals("array", StringComparison.OrdinalIgnoreCase))
        {
            schema["type"] = "array";

            // Every array must have an items property defined
            if (property.Items != null)
            {
                // For arrays, we need to handle the items specially
                var itemSchema = new Dictionary<string, object>();

                if (property.Items.Type.Equals("object", StringComparison.OrdinalIgnoreCase) &&
                    property.Items.Properties != null)
                {
                    itemSchema["type"] = "object";
                    itemSchema["additionalProperties"] = false;

                    var propsDict = new Dictionary<string, object>();

                    // For array items, ALWAYS include ALL properties in required array regardless of nullable
                    var requiredProps = property.Items.Properties.Select(p => p.Name).ToList();

                    foreach (var prop in property.Items.Properties)
                    {
                        var propSchema = CreateOpenAiPropertySchema(prop);

                        // If the property is nullable, we need to handle that, but it's still required
                        if (prop.Nullable)
                        {
                            propSchema["type"] = new[]
                            {
                                "null",
                                prop.Type.ToLowerInvariant()
                            };
                        }

                        propsDict[prop.Name] = propSchema;
                    }

                    itemSchema["properties"] = propsDict;

                    // Always add required for array items
                    itemSchema["required"] = requiredProps;
                }
                else
                {
                    // For non-object items
                    if (property.Items.Nullable)
                    {
                        itemSchema["type"] = new[]
                        {
                            "null",
                            property.Items.Type.ToLowerInvariant()
                        };
                    }
                    else
                    {
                        itemSchema["type"] = property.Items.Type.ToLowerInvariant();
                    }
                }

                schema["items"] = itemSchema;
            }
            else
            {
                // Default to string items if not specified
                schema["items"] = new Dictionary<string, object>
                {
                    {
                        "type", "string"
                    }
                };
            }
        }
        else if (property.Type.Equals("object", StringComparison.OrdinalIgnoreCase) && property.Properties != null)
        {
            schema["type"] = "object";
            schema["additionalProperties"] = false;

            var propsDict = new Dictionary<string, object>();

            // ALWAYS include ALL properties in required array regardless of nullable
            var requiredProps = property.Properties.Select(p => p.Name).ToList();

            foreach (var prop in property.Properties)
            {
                var propSchema = CreateOpenAiPropertySchema(prop);

                // If the property is nullable, we need to handle that with type, but it's still required
                if (prop.Nullable)
                {
                    propSchema["type"] = new[]
                    {
                        "null",
                        prop.Type.ToLowerInvariant()
                    };
                }

                propsDict[prop.Name] = propSchema;
            }

            schema["properties"] = propsDict;

            // Always add required array with all properties
            schema["required"] = requiredProps;
        }
        else
        {
            // For nullable properties, use a union type ["null", "type"]
            if (property.Nullable)
            {
                schema["type"] = new[]
                {
                    "null",
                    property.Type.ToLowerInvariant()
                };
            }
            else
            {
                schema["type"] = property.Type.ToLowerInvariant();
            }
        }

        return schema;
    }

    private static Dictionary<string, GeminiJsonSchemaProperty> CreateGeminiPropertiesDictionary(List<Property> properties)
    {
        var result = new Dictionary<string, GeminiJsonSchemaProperty>();

        foreach (var property in properties)
        {
            Dictionary<string, GeminiJsonSchemaProperty>? nestedProperties = null;
            string[]? propertyOrdering = null;
            string[]? required = null;
            GeminiJsonSchemaProperty? items = null;

            // Handle nested properties for objects
            if (property.Properties != null && property.Properties.Count > 0)
            {
                nestedProperties = CreateGeminiPropertiesDictionary(property.Properties);
                propertyOrdering = property.Properties.Select(p => p.Name).ToArray();
                required = property.Properties.Where(p => !p.Nullable).Select(p => p.Name).ToArray();
                required = required.Length > 0 ? required : null;
            }

            // Handle array items
            if (property.Items != null)
            {
                Dictionary<string, GeminiJsonSchemaProperty>? itemProperties = null;
                string[]? itemPropertyOrdering = null;
                string[]? itemRequired = null;

                if (property.Items.Properties != null && property.Items.Properties.Count > 0)
                {
                    itemProperties = CreateGeminiPropertiesDictionary(property.Items.Properties);
                    itemPropertyOrdering = property.Items.Properties.Select(p => p.Name).ToArray();
                    itemRequired = property.Items.Properties.Where(p => !p.Nullable).Select(p => p.Name).ToArray();
                    itemRequired = itemRequired.Length > 0 ? itemRequired : null;
                }

                items = new GeminiJsonSchemaProperty(
                    property.Items.Type,
                    property.Items.Nullable,
                    itemProperties,
                    null,
                    itemPropertyOrdering,
                    itemRequired);
            }

            result.Add(
                property.Name,
                new GeminiJsonSchemaProperty(
                    property.Type,
                    property.Nullable,
                    nestedProperties,
                    items,
                    propertyOrdering,
                    required));
        }

        return result;
    }

    public static void EnrichPromptExecutionSettingsWithToolCalling(PromptExecutionSettings settings)
    {
#pragma warning disable SKEXP0070
        switch (settings)
        {
            case GeminiPromptExecutionSettings geminiPromptExecutionSettings:
                geminiPromptExecutionSettings.ToolCallBehavior = GeminiToolCallBehavior.AutoInvokeKernelFunctions;
                break;
            case OpenAIPromptExecutionSettings openAiPromptExecutionSettings:
                openAiPromptExecutionSettings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto();
                break;
        }
#pragma warning restore SKEXP0070
    }
}