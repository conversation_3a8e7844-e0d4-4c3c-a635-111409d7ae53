using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class CalculateLeadScoreSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("lead_scoring")]
    public CalculateLeadScoreResult? CalculateLeadScore { get; set; }

    [JsonConstructor]
    public CalculateLeadScoreSnapshot(List<SfChatEntry> conversationContext, CalculateLeadScoreResult? calculateLeadScore)
    {
        ConversationContext = conversationContext;
        CalculateLeadScore = calculateLeadScore;
    }
}