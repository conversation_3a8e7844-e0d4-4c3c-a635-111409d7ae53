﻿using System.Collections.Concurrent;
using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Polly;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

internal class ContactContextValidate : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private List<string>? _containerIds;

    public ContactContextValidate(DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Contact Context Validate";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerIds = Prompt.MultiSelect(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerIds = selectedContainerIds.ToList();
    }

    public async Task ExecuteAsync()
    {
        foreach (var containerId in _containerIds!)
        {
            var count = await ValidateObjectsAsync(containerId);
            Console.WriteLine($"Completed {containerId} for {count} objects");
        }
    }

    private async Task<int> ValidateObjectsAsync(
        string containerName)
    {
        var retryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                9,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 2)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    return TimeSpan.FromSeconds(1.37) * retryCount;
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    Console.WriteLine(
                        $"TooManyRequests retryCount=[{retryCount}], timeSpan=[{timeSpan}], containerName=[{containerName}]");

                    return Task.CompletedTask;
                });

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerName);
        var i = 0;

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(container),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(async () => await ValidateObjectAsync(dict));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        Console.WriteLine(
            JsonConvert.SerializeObject(
                ExternalIdToIds
                    .Where(e => e.Value.Count > 1)
                    .ToDictionary(e => e.Key, e => e.Value)));

        return i;
    }

    private static readonly ConcurrentDictionary<string, List<string>> ExternalIdToIds =
        new ConcurrentDictionary<string, List<string>>();

    private static Task<int> ValidateObjectAsync(Dictionary<string, object?> dict)
    {
        string? objectId = null;
        var ctxExternalIds = new List<string>();

        foreach (var keyValuePair in dict)
        {
            if (keyValuePair.Key == "id")
            {
                objectId = (string) keyValuePair.Value!;
            }

            if (keyValuePair.Key == "unified:SleekflowId")
            {
                var id = (keyValuePair.Value as JObject)?.Value<string>("v");
                if (id != null)
                {
                    ctxExternalIds.Add("sleekflow:" + id);
                }
            }

            if (keyValuePair.Key == "unified:HubspotIntegratorId")
            {
                var id = (keyValuePair.Value as JObject)?.Value<string>("v");
                if (id != null)
                {
                    ctxExternalIds.Add("hubspot-integrator:" + id);
                }
            }

            if (keyValuePair.Key == "unified:SalesforceIntegratorId")
            {
                var id = (keyValuePair.Value as JObject)?.Value<string>("v");
                if (id != null)
                {
                    ctxExternalIds.Add("salesforce-integrator:" + id);
                }
            }

            if (keyValuePair.Key == "unified:D365Id")
            {
                var id = (keyValuePair.Value as JObject)?.Value<string>("v");
                if (id != null)
                {
                    ctxExternalIds.Add("d365:" + id);
                }
            }
        }

        foreach (var ctxExternalId in ctxExternalIds)
        {
            if (ExternalIdToIds.ContainsKey(ctxExternalId) == false)
            {
                ExternalIdToIds[ctxExternalId] = new List<string>();
            }

            ExternalIdToIds[ctxExternalId].Add(objectId!);
        }

        return Task.FromResult(1);
    }
}