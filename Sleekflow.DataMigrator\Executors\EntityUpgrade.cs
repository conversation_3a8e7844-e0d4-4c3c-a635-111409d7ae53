﻿using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json.Linq;
using Polly;
using Sharprompt;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

internal class EntityUpgrade : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private List<string>? _containerIds;

    public EntityUpgrade(DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Entity Upgrade";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerIds = Prompt.MultiSelect(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerIds = selectedContainerIds.ToList();
    }

    public async Task ExecuteAsync()
    {
        foreach (var containerId in _containerIds!)
        {
            var count = await MigrateObjectsAsync(containerId);
            Console.WriteLine($"Completed {containerId} for {count} objects");
        }
    }

    private async Task<int> MigrateObjectsAsync(
        string containerName)
    {
        var retryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                9,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 2)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    return TimeSpan.FromSeconds(1.37) * retryCount;
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    Console.WriteLine(
                        $"TooManyRequests retryCount=[{retryCount}], timeSpan=[{timeSpan}], containerName=[{containerName}]");

                    return Task.CompletedTask;
                });

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerName);
        var i = 0;

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(container),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(async () => await MigrateObject(dict, container, token));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }

    private static async Task<int> MigrateObject(
        Dictionary<string, object?> dict,
        Container container,
        CancellationToken token)
    {
        var jToken = new JObject();

        var newDict = new Dictionary<string, object?>();
        var isNewVersion = true;

        foreach (var keyValuePair in dict)
        {
            if (keyValuePair.Value is JObject jObject)
            {
                if (jObject.ContainsKey("snapshot_time") && jObject.ContainsKey("value"))
                {
                    isNewVersion = false;

                    var snapshottedValue = SnapshottedValue.FromObject(jObject);

                    newDict[keyValuePair.Key] = snapshottedValue;
                }
                else if (jObject.ContainsKey("i") && jObject["i"] is JObject i
                                                  && JToken.DeepEquals(i, jToken) == false)
#pragma warning disable S1871
                {
                    isNewVersion = false;

                    var snapshottedValue = SnapshottedValue.FromObject(jObject);

                    newDict[keyValuePair.Key] = snapshottedValue;
                }
#pragma warning restore S1871
                else
                {
                    newDict[keyValuePair.Key] = keyValuePair.Value;
                }
            }
            else if (keyValuePair.Key == "sys_version")
            {
                newDict[keyValuePair.Key] = (long) keyValuePair.Value! + 1;
            }
            else
            {
                newDict[keyValuePair.Key] = keyValuePair.Value;
            }
        }

        if (isNewVersion == false)
        {
            var id = (string) newDict["id"]!;

            var itemResponse = await container.ReplaceItemAsync(
                newDict,
                id,
                new PartitionKeyBuilder()
                    .Add(id)
                    .Build(),
                new ItemRequestOptions()
                {
                    IfMatchEtag = (string) newDict["_etag"]!, EnableContentResponseOnWrite = false,
                },
                token);

            Console.WriteLine(
                $"Patched id {id} requestCharge {itemResponse.RequestCharge}");
        }
        else
        {
            var id = (string) newDict["id"]!;

            Console.WriteLine(
                $"Skipped id {id}");
        }

        return 1;
    }
}