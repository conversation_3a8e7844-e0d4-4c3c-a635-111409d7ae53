using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.FaqAgents.ChatContexts;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.FaqAgents.ChatContexts;

public interface IChatContextRepository : IRepository<ChatContext>
{
}

public class ChatContextRepository
    : BaseRepository<ChatContext>, IChatContextRepository, IScopedService
{
    public ChatContextRepository(
        ILogger<ChatContextRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}