using System.Text;
using Azure.Storage.Blobs.Specialized;
using Azure.Storage.Sas;
using Sleekflow.CommerceHub.Blobs;

namespace Sleekflow.CommerceHub.CustomCatalogs;

public class CustomCatalogFileProcessLogger
{
    private readonly IBlobService _blobService;
    private readonly string _sleekflowCompanyId;
    private readonly string _storeId;
    private readonly string _customCatalogId;

    private AppendBlobClient? _blobClient;

    public CustomCatalogFileProcessLogger(
        IBlobService blobService,
        string sleekflowCompanyId,
        string storeId,
        string customCatalogId)
    {
        _blobService = blobService;
        _sleekflowCompanyId = sleekflowCompanyId;
        _storeId = storeId;
        _customCatalogId = customCatalogId;
    }

    public async Task<CustomCatalogFileProcessLogger> InitAsync()
    {
        var blobContainerClient = _blobService.GetFileBlobServiceClient();

        var blobName = _customCatalogId + ".csv";
        var blobId = _blobService.GetBlobId(_sleekflowCompanyId, _storeId, blobName);

        _blobClient = blobContainerClient.GetAppendBlobClient(blobId);
        if (await _blobClient.ExistsAsync())
        {
            // skip
        }
        else
        {
            await _blobClient.CreateAsync();
            await _blobClient.AppendBlockAsync(
                new MemoryStream(Encoding.UTF8.GetBytes("Row,Error Message" + Environment.NewLine)));
        }

        return this;
    }

    public async Task<bool> AnyAsync()
    {
        if (_blobClient == null)
        {
            throw new InvalidOperationException("CustomCatalogFileProcessingLogger is not initialized.");
        }

        var properties = await _blobClient.GetPropertiesAsync();
        var size = properties.Value.BlobCommittedBlockCount;

        if (size > 1)
        {
            return true;
        }
        else
        {
            return false;
        }
    }

    public async Task LogAsync(string message, int row)
    {
        if (_blobClient == null)
        {
            throw new InvalidOperationException("CustomCatalogFileProcessingLogger is not initialized.");
        }

        await _blobClient.AppendBlockAsync(
            new MemoryStream(Encoding.UTF8.GetBytes($"{row},{message}" + Environment.NewLine)));
    }

    public string GetSasUrl()
    {
        if (_blobClient == null)
        {
            throw new InvalidOperationException("CustomCatalogFileProcessingLogger is not initialized.");
        }

        var expiresDelay = TimeSpan.FromHours(1);
        var expiresOn = DateTimeOffset.UtcNow.Add(expiresDelay);

        var uri = _blobClient.GenerateSasUri(
            BlobSasPermissions.Read,
            expiresOn);

        return uri.ToString();
    }
}