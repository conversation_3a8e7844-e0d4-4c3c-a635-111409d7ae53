using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class GetProcessFileDocumentStatus
    : ITrigger<GetProcessFileDocumentStatus.GetProcessFileDocumentStatusInput,
        GetProcessFileDocumentStatus.GetProcessFileDocumentStatusOutput>
{
    private readonly IKbDocumentService _kbDocumentService;

    public GetProcessFileDocumentStatus(IKbDocumentService kbDocumentService)
    {
        _kbDocumentService = kbDocumentService;
    }

    public class GetProcessFileDocumentStatusInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public GetProcessFileDocumentStatusInput(string sleekflowCompanyId, string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class GetProcessFileDocumentStatusOutput
    {
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty(FileDocument.PropertyNameFileDocumentProcessStatus)]
        public string FileDocumentProcessStatus { get; set; }

        [JsonConstructor]
        public GetProcessFileDocumentStatusOutput(string documentId, string fileDocumentProcessStatus)
        {
            DocumentId = documentId;
            FileDocumentProcessStatus = fileDocumentProcessStatus;
        }
    }

    public async Task<GetProcessFileDocumentStatusOutput> F(
        GetProcessFileDocumentStatusInput getProcessFileDocumentStatusInput)
    {
        var status = await _kbDocumentService.GetFileDocumentProcessStatusAsync(
            getProcessFileDocumentStatusInput.SleekflowCompanyId,
            getProcessFileDocumentStatusInput.DocumentId);

        return new GetProcessFileDocumentStatusOutput(
            getProcessFileDocumentStatusInput.DocumentId,
            status);
    }
}