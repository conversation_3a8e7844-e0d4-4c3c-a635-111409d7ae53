using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class ReviewerPluginTest
{
    private Kernel _kernel;
    private IReviewerPlugin _reviewerPlugin;

    [SetUp]
    public void SetUp()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _reviewerPlugin = scope.ServiceProvider.GetRequiredService<IReviewerPlugin>();
    }

    public class ScoringTestCase
    {
        public string ChatHistory { get; set; } = string.Empty;

        public string MessageToEvaluate { get; set; } = string.Empty;

        public int ExpectedMinScore { get; set; } // Minimum acceptable score (0-100)

        public int ExpectedMaxScore { get; set; } // Maximum acceptable score (0-100)
    }

    private static IEnumerable<ScoringTestCase> ScoringTestCases
    {
        get
        {
            // Test Case 1: Perfect Alignment (English)
            yield return new ScoringTestCase
            {
                ChatHistory = "User: What is SleekFlow?\nBot: SleekFlow is a platform for business communication.",
                MessageToEvaluate = "Bot: SleekFlow helps businesses manage their communication efficiently.",
                ExpectedMinScore = 90, // High score due to perfect alignment across all criteria
                ExpectedMaxScore = 100 // Maximum possible score
            };

            // Test Case 2: Contact Support Hint (English) -> Escalation Detection
            yield return new ScoringTestCase
            {
                ChatHistory = "User: How do I reset my password?\nBot: Go to settings and click 'Reset Password'.",
                MessageToEvaluate = "Bot: If that doesn't work, try again or contact support.",
                ExpectedMinScore = 10,
                ExpectedMaxScore = 40
            };

            // Test Case 3: Tone Mismatch (English)
            yield return new ScoringTestCase
            {
                ChatHistory =
                    "User: Can you help me with my account?\nBot: Sure, I'd be happy to assist you professionally.",
                MessageToEvaluate = "Bot: Yo, what's up with your account?",
                ExpectedMinScore = 20, // Adjusted higher due to high relevancy despite tone mismatch
                ExpectedMaxScore = 50 // Upper limit reflects partial strengths
            };

            // Test Case 4: Irrelevant Response (English)
            yield return new ScoringTestCase
            {
                ChatHistory =
                    "User: What are the pricing plans?\nBot: We have three plans: Basic, Standard, and Premium.",
                MessageToEvaluate = "Bot: Our office is located in downtown.",
                ExpectedMinScore = 20, // Adjusted higher due to moderate scores in other criteria
                ExpectedMaxScore = 40 // Upper limit reflects minimal relevance
            };

            // Test Case 5: Outdated Information (English)
            yield return new ScoringTestCase
            {
                ChatHistory = "User: What are the current features?\nBot: We recently added feature X.",
                MessageToEvaluate = "Bot: We added feature Y last year.",
                ExpectedMinScore = 50, // Moderate due to outdated info and partial relevance
                ExpectedMaxScore = 75 // Upper limit reflects reasonable consistency
            };

            // Test Case 6: Contradiction (English)
            yield return new ScoringTestCase
            {
                ChatHistory = "User: Is the service free?\nBot: No, it's subscription-based.",
                MessageToEvaluate = "Bot: Yes, the service is completely free.",
                ExpectedMinScore = 20, // Adjusted higher due to high scores in relevancy and tone
                ExpectedMaxScore = 40 // Upper limit reflects severe contradiction
            };

            // Test Case 7: Escalation Mention (English)
            yield return new ScoringTestCase
            {
                ChatHistory = "User: I'm having trouble with my account.\nBot: Let me check that for you.",
                MessageToEvaluate = "Bot: I'll escalate this to our manager and get back to you.",
                ExpectedMinScore = 0, // Adjusted higher due to relevance despite escalation
                ExpectedMaxScore = 30 // Upper limit reflects poor resolution
            };

            // Test Case 8: Perfect Alignment (Chinese)
            yield return new ScoringTestCase
            {
                ChatHistory = @"用户: SleekFlow 是什么？
机器人: SleekFlow 是一个商业通信平台。",
                MessageToEvaluate = @"机器人: SleekFlow 帮助企业高效管理通信。",
                ExpectedMinScore = 90, // High score due to perfect alignment across all criteria
                ExpectedMaxScore = 100 // Maximum possible score
            };

            // Test Case 9: Ambiguity in Chinese and Escalation Detection
            yield return new ScoringTestCase
            {
                ChatHistory = @"用户: 如何重置密码？
机器人: 转到设置并点击重置密码。",
                MessageToEvaluate = @"机器人: 如果不行，再试一次或联系支持。",
                ExpectedMinScore = 15,
                ExpectedMaxScore = 50
            };

            // Test Case 10: Escalation Mention (Chinese)
            yield return new ScoringTestCase
            {
                ChatHistory = @"用户: 我的账户有问题。
机器人: 让我为您检查一下。",
                MessageToEvaluate = @"机器人: 我会将此事升级给我们的经理并稍后回复您。",
                ExpectedMinScore = 0,
                ExpectedMaxScore = 35 // Upper limit reflects poor resolution
            };
        }
    }

    [TestCaseSource(nameof(ScoringTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public async Task GetConfidenceScoringAsyncTest(ScoringTestCase testCase)
    {
        // Act
        var score = await _reviewerPlugin.GetConfidenceScoringAsync(
            _kernel,
            testCase.ChatHistory,
            testCase.MessageToEvaluate);

        // Assert
        TestContext.WriteLine($"Chat History: {testCase.ChatHistory}");
        TestContext.WriteLine($"Message to Evaluate: {testCase.MessageToEvaluate}");
        TestContext.WriteLine($"Calculated Score: {score}");
        TestContext.WriteLine($"Expected Score Range: {testCase.ExpectedMinScore} - {testCase.ExpectedMaxScore}\n");

        // Verify the score is within the expected range
        Assert.That(score, Is.GreaterThanOrEqualTo(testCase.ExpectedMinScore));
        Assert.That(score, Is.LessThanOrEqualTo(testCase.ExpectedMaxScore));
    }

    #region CheckEarlyExitConditions Tests

    public class CheckEarlyExitConditionsTestCase
    {
        public string TestName { get; set; } = string.Empty;
        public List<ExitCondition> ExitConditions { get; set; } = new ();
        public int? Score { get; set; }
        public int ConfidenceScore { get; set; } = 50; // Default confidence score
        public bool ExpectedIsMatch { get; set; }
        public string? ExpectedTitle { get; set; }
        public string? ExpectedReasonContains { get; set; }
    }

    private static IEnumerable<CheckEarlyExitConditionsTestCase> CheckEarlyExitConditionsTestCases
    {
        get
        {
            // Test Case 1: Lead Score - Larger Than (Condition Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Lead Score Larger Than - Condition Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "High Lead Score",
                        "lead_score",
                        "Exit when lead score is high",
                        "larger_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 75,
                ExpectedIsMatch = true,
                ExpectedTitle = "High Lead Score",
                ExpectedReasonContains = "75 meets the larger_than condition with threshold 50"
            };

            // Test Case 2: Lead Score - Larger Than (Condition Not Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Lead Score Larger Than - Condition Not Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "High Lead Score",
                        "lead_score",
                        "Exit when lead score is high",
                        "larger_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 30,
                ExpectedIsMatch = false
            };

            // Test Case 3: Lead Score - Smaller Than (Condition Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Lead Score Smaller Than - Condition Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Low Lead Score",
                        "lead_score",
                        "Exit when lead score is low",
                        "smaller_than",
                        new List<object>
                        {
                            30
                        })
                },
                Score = 20,
                ExpectedIsMatch = true,
                ExpectedTitle = "Low Lead Score",
                ExpectedReasonContains = "20 meets the smaller_than condition with threshold 30"
            };

            // Test Case 4: Lead Score - Smaller Than (Condition Not Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Lead Score Smaller Than - Condition Not Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Low Lead Score",
                        "lead_score",
                        "Exit when lead score is low",
                        "smaller_than",
                        new List<object>
                        {
                            30
                        })
                },
                Score = 50,
                ExpectedIsMatch = false
            };

            // Test Case 5: Lead Score - Is Between (Condition Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Lead Score Is Between - Condition Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Medium Lead Score",
                        "lead_score",
                        "Exit when lead score is medium",
                        "is_between",
                        new List<object>
                        {
                            30, 70
                        })
                },
                Score = 50,
                ExpectedIsMatch = true,
                ExpectedTitle = "Medium Lead Score",
                ExpectedReasonContains = "50 meets the is_between condition with threshold 30, 70"
            };

            // Test Case 6: Lead Score - Is Between (Condition Not Met - Below Range)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Lead Score Is Between - Below Range",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Medium Lead Score",
                        "lead_score",
                        "Exit when lead score is medium",
                        "is_between",
                        new List<object>
                        {
                            30, 70
                        })
                },
                Score = 20,
                ExpectedIsMatch = false
            };

            // Test Case 7: Lead Score - Is Between (Condition Not Met - Above Range)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Lead Score Is Between - Above Range",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Medium Lead Score",
                        "lead_score",
                        "Exit when lead score is medium",
                        "is_between",
                        new List<object>
                        {
                            30, 70
                        })
                },
                Score = 80,
                ExpectedIsMatch = false
            };

            // Test Case 8: Low Confidence - Smaller Than (Condition Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Low Confidence Smaller Than - Condition Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Low Confidence Exit",
                        "low_confidence",
                        "Exit when confidence is low",
                        "smaller_than",
                        new List<object>
                        {
                            25
                        })
                },
                Score = 75, // High score but low confidence
                ConfidenceScore = 15, // Low confidence score
                ExpectedIsMatch = true,
                ExpectedTitle = "Low Confidence Exit",
                ExpectedReasonContains = "Confidence score of 15 meets the smaller_than condition with threshold 25"
            };

            // Test Case 9: Multiple Conditions - First One Matches
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Multiple Conditions - First Matches",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Low Lead Score",
                        "lead_score",
                        "Exit when lead score is low",
                        "smaller_than",
                        new List<object>
                        {
                            30
                        }),
                    new ExitCondition(
                        "High Lead Score",
                        "lead_score",
                        "Exit when lead score is high",
                        "larger_than",
                        new List<object>
                        {
                            80
                        })
                },
                Score = 20,
                ExpectedIsMatch = true,
                ExpectedTitle = "Low Lead Score",
                ExpectedReasonContains = "20 meets the smaller_than condition with threshold 30"
            };

            // Test Case 10: Multiple Conditions - Second One Matches
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Multiple Conditions - Second Matches",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Low Lead Score",
                        "lead_score",
                        "Exit when lead score is low",
                        "smaller_than",
                        new List<object>
                        {
                            30
                        }),
                    new ExitCondition(
                        "High Lead Score",
                        "lead_score",
                        "Exit when lead score is high",
                        "larger_than",
                        new List<object>
                        {
                            80
                        })
                },
                Score = 90,
                ExpectedIsMatch = true,
                ExpectedTitle = "High Lead Score",
                ExpectedReasonContains = "90 meets the larger_than condition with threshold 80"
            };

            // Test Case 11: Custom Type - Should Be Ignored
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Custom Type - Should Be Ignored",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Custom Condition",
                        "custom",
                        "Custom exit condition",
                        "larger_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 75,
                ExpectedIsMatch = false
            };

            // Test Case 12: Mixed Types - Only Valid Types Processed
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Mixed Types - Only Valid Types Processed",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Custom Condition",
                        "custom",
                        "Custom exit condition",
                        "larger_than",
                        new List<object>
                        {
                            50
                        }),
                    new ExitCondition(
                        "Low Confidence",
                        "low_confidence",
                        "Low confidence condition",
                        "smaller_than",
                        new List<object>
                        {
                            30
                        })
                },
                Score = 80, // High score but low confidence
                ConfidenceScore = 20, // Low confidence score
                ExpectedIsMatch = true,
                ExpectedTitle = "Low Confidence",
                ExpectedReasonContains = "Confidence score of 20 meets the smaller_than condition with threshold 30"
            };

            // Test Case 13: Null Score
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Null Score",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "High Lead Score",
                        "lead_score",
                        "Exit when lead score is high",
                        "larger_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = null,
                ExpectedIsMatch = false
            };

            // Test Case 14: Empty Conditions List
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Empty Conditions List",
                ExitConditions = new List<ExitCondition>(),
                Score = 50,
                ExpectedIsMatch = false
            };

            // Test Case 15: Condition with Null Operator
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Condition with Null Operator",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Invalid Condition",
                        "lead_score",
                        "Invalid condition with null operator",
                        null,
                        new List<object>
                        {
                            50
                        })
                },
                Score = 75,
                ExpectedIsMatch = false
            };

            // Test Case 16: Condition with Empty Operator
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Condition with Empty Operator",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Invalid Condition",
                        "lead_score",
                        "Invalid condition with empty operator",
                        "",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 75,
                ExpectedIsMatch = false
            };

            // Test Case 17: Condition with Null Values
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Condition with Null Values",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Invalid Condition",
                        "lead_score",
                        "Invalid condition with null values",
                        "larger_than",
                        null)
                },
                Score = 75,
                ExpectedIsMatch = false
            };

            // Test Case 18: Condition with Empty Values
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Condition with Empty Values",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Invalid Condition",
                        "lead_score",
                        "Invalid condition with empty values",
                        "larger_than",
                        new List<object>())
                },
                Score = 75,
                ExpectedIsMatch = false
            };

            // Test Case 19: Invalid Operator
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Invalid Operator",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Invalid Operator Condition",
                        "lead_score",
                        "Condition with invalid operator",
                        "invalid_operator",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 75,
                ExpectedIsMatch = false
            };

            // Test Case 20: Non-numeric Values
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Non-numeric Values",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Non-numeric Values",
                        "lead_score",
                        "Condition with non-numeric values",
                        "larger_than",
                        new List<object>
                        {
                            "not_a_number"
                        })
                },
                Score = 75,
                ExpectedIsMatch = false
            };

            // Test Case 21: Is Between with Insufficient Values
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Is Between with Insufficient Values",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Insufficient Values",
                        "lead_score",
                        "Is between with only one value",
                        "is_between",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 60,
                ExpectedIsMatch = false
            };

            // Test Case 22: Edge Case - Score Equals Threshold (Larger Than)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Score Equals Threshold - Larger Than",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Equal Threshold",
                        "lead_score",
                        "Score equals threshold",
                        "larger_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 50,
                ExpectedIsMatch = false // 50 is NOT larger than 50
            };

            // Test Case 23: Edge Case - Score Equals Threshold (Smaller Than)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Score Equals Threshold - Smaller Than",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Equal Threshold",
                        "lead_score",
                        "Score equals threshold",
                        "smaller_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 50,
                ExpectedIsMatch = false // 50 is NOT smaller than 50
            };

            // Test Case 24: Edge Case - Score Equals Boundary (Is Between)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Score Equals Lower Boundary - Is Between",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Boundary Test",
                        "lead_score",
                        "Score equals lower boundary",
                        "is_between",
                        new List<object>
                        {
                            50, 70
                        })
                },
                Score = 50,
                ExpectedIsMatch = true, // 50 is between 50 and 70 (inclusive)
                ExpectedTitle = "Boundary Test",
                ExpectedReasonContains = "50 meets the is_between condition with threshold 50, 70"
            };

            // Test Case 25: Edge Case - Score Equals Upper Boundary (Is Between)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Score Equals Upper Boundary - Is Between",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Boundary Test",
                        "lead_score",
                        "Score equals upper boundary",
                        "is_between",
                        new List<object>
                        {
                            30, 50
                        })
                },
                Score = 50,
                ExpectedIsMatch = true, // 50 is between 30 and 50 (inclusive)
                ExpectedTitle = "Boundary Test",
                ExpectedReasonContains = "50 meets the is_between condition with threshold 30, 50"
            };

            // Test Case 26: Low Confidence - Larger Than (Condition Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Low Confidence Larger Than - Condition Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "High Confidence Exit",
                        "low_confidence",
                        "Exit when confidence is high",
                        "larger_than",
                        new List<object>
                        {
                            70
                        })
                },
                Score = 30, // Low score but high confidence
                ConfidenceScore = 85, // High confidence score
                ExpectedIsMatch = true,
                ExpectedTitle = "High Confidence Exit",
                ExpectedReasonContains = "Confidence score of 85 meets the larger_than condition with threshold 70"
            };

            // Test Case 27: Low Confidence - Larger Than (Condition Not Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Low Confidence Larger Than - Condition Not Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "High Confidence Exit",
                        "low_confidence",
                        "Exit when confidence is high",
                        "larger_than",
                        new List<object>
                        {
                            70
                        })
                },
                Score = 80, // High score but low confidence
                ConfidenceScore = 50, // Low confidence score
                ExpectedIsMatch = false
            };

            // Test Case 28: Low Confidence - Smaller Than (Condition Not Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Low Confidence Smaller Than - Condition Not Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Low Confidence Exit",
                        "low_confidence",
                        "Exit when confidence is low",
                        "smaller_than",
                        new List<object>
                        {
                            25
                        })
                },
                Score = 20, // Low score but high confidence
                ConfidenceScore = 80, // High confidence score
                ExpectedIsMatch = false
            };

            // Test Case 29: Low Confidence - Is Between (Condition Met)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Low Confidence Is Between - Condition Met",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Medium Confidence Exit",
                        "low_confidence",
                        "Exit when confidence is medium",
                        "is_between",
                        new List<object>
                        {
                            40, 70
                        })
                },
                Score = 90, // High score but medium confidence
                ConfidenceScore = 55, // Medium confidence score
                ExpectedIsMatch = true,
                ExpectedTitle = "Medium Confidence Exit",
                ExpectedReasonContains = "Confidence score of 55 meets the is_between condition with threshold 40, 70"
            };

            // Test Case 30: Low Confidence - Is Between (Condition Not Met - Below Range)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Low Confidence Is Between - Below Range",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Medium Confidence Exit",
                        "low_confidence",
                        "Exit when confidence is medium",
                        "is_between",
                        new List<object>
                        {
                            40, 70
                        })
                },
                Score = 90, // High score but very low confidence
                ConfidenceScore = 20, // Very low confidence score
                ExpectedIsMatch = false
            };

            // Test Case 31: Low Confidence - Is Between (Condition Not Met - Above Range)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Low Confidence Is Between - Above Range",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Medium Confidence Exit",
                        "low_confidence",
                        "Exit when confidence is medium",
                        "is_between",
                        new List<object>
                        {
                            40, 70
                        })
                },
                Score = 20, // Low score but very high confidence
                ConfidenceScore = 90, // Very high confidence score
                ExpectedIsMatch = false
            };

            // Test Case 32: Mixed Lead Score and Confidence - Lead Score Matches First
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Mixed Lead Score and Confidence - Lead Score Matches First",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "High Lead Score",
                        "lead_score",
                        "Exit when lead score is high",
                        "larger_than",
                        new List<object>
                        {
                            80
                        }),
                    new ExitCondition(
                        "Low Confidence",
                        "low_confidence",
                        "Exit when confidence is low",
                        "smaller_than",
                        new List<object>
                        {
                            30
                        })
                },
                Score = 90, // High score
                ConfidenceScore = 20, // Low confidence - both conditions would match, but lead score is first
                ExpectedIsMatch = true,
                ExpectedTitle = "High Lead Score",
                ExpectedReasonContains = "Score of 90 meets the larger_than condition with threshold 80"
            };

            // Test Case 33: Mixed Lead Score and Confidence - Confidence Matches First
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Mixed Lead Score and Confidence - Confidence Matches First",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Low Confidence",
                        "low_confidence",
                        "Exit when confidence is low",
                        "smaller_than",
                        new List<object>
                        {
                            30
                        }),
                    new ExitCondition(
                        "High Lead Score",
                        "lead_score",
                        "Exit when lead score is high",
                        "larger_than",
                        new List<object>
                        {
                            80
                        })
                },
                Score = 90, // High score
                ConfidenceScore = 20, // Low confidence - both conditions would match, but confidence is first
                ExpectedIsMatch = true,
                ExpectedTitle = "Low Confidence",
                ExpectedReasonContains = "Confidence score of 20 meets the smaller_than condition with threshold 30"
            };

            // Test Case 34: Edge Case - Confidence Score Equals Threshold (Larger Than)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Confidence Score Equals Threshold - Larger Than",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Equal Confidence Threshold",
                        "low_confidence",
                        "Confidence score equals threshold",
                        "larger_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 75,
                ConfidenceScore = 50, // Equals threshold
                ExpectedIsMatch = false // 50 is NOT larger than 50
            };

            // Test Case 35: Edge Case - Confidence Score Equals Threshold (Smaller Than)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Confidence Score Equals Threshold - Smaller Than",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Equal Confidence Threshold",
                        "low_confidence",
                        "Confidence score equals threshold",
                        "smaller_than",
                        new List<object>
                        {
                            50
                        })
                },
                Score = 75,
                ConfidenceScore = 50, // Equals threshold
                ExpectedIsMatch = false // 50 is NOT smaller than 50
            };

            // Test Case 36: Edge Case - Confidence Score Equals Boundary (Is Between)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Confidence Score Equals Lower Boundary - Is Between",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Confidence Boundary Test",
                        "low_confidence",
                        "Confidence score equals lower boundary",
                        "is_between",
                        new List<object>
                        {
                            50, 70
                        })
                },
                Score = 75,
                ConfidenceScore = 50, // Equals lower boundary
                ExpectedIsMatch = true, // 50 is between 50 and 70 (inclusive)
                ExpectedTitle = "Confidence Boundary Test",
                ExpectedReasonContains = "Confidence score of 50 meets the is_between condition with threshold 50, 70"
            };

            // Test Case 37: Edge Case - Confidence Score Equals Upper Boundary (Is Between)
            yield return new CheckEarlyExitConditionsTestCase
            {
                TestName = "Confidence Score Equals Upper Boundary - Is Between",
                ExitConditions = new List<ExitCondition>
                {
                    new ExitCondition(
                        "Confidence Boundary Test",
                        "low_confidence",
                        "Confidence score equals upper boundary",
                        "is_between",
                        new List<object>
                        {
                            30, 50
                        })
                },
                Score = 75,
                ConfidenceScore = 50, // Equals upper boundary
                ExpectedIsMatch = true, // 50 is between 30 and 50 (inclusive)
                ExpectedTitle = "Confidence Boundary Test",
                ExpectedReasonContains = "Confidence score of 50 meets the is_between condition with threshold 30, 50"
            };
        }
    }

    [TestCaseSource(nameof(CheckEarlyExitConditionsTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public void CheckEarlyExitConditionsTest(CheckEarlyExitConditionsTestCase testCase)
    {
        // Arrange
        var reviewerPlugin = _reviewerPlugin as ReviewerPlugin;
        Assert.That(reviewerPlugin, Is.Not.Null, "ReviewerPlugin should be castable to concrete type for testing");

        // Act
        var result = reviewerPlugin.CheckEarlyExitConditions(
            testCase.ExitConditions,
            testCase.Score,
            testCase.ConfidenceScore);

        // Assert
        TestContext.WriteLine($"Test: {testCase.TestName}");
        TestContext.WriteLine($"Score: {testCase.Score}");
        TestContext.WriteLine($"Confidence Score: {testCase.ConfidenceScore}");
        TestContext.WriteLine($"Exit Conditions: {testCase.ExitConditions.Count}");

        if (testCase.ExitConditions.Any())
        {
            var condition = testCase.ExitConditions.First();
            TestContext.WriteLine(
                $"First Condition - Type: {condition.Type}, Operator: {condition.Operator}, Values: [{string.Join(", ", condition.Values ?? new List<object>())}]");
        }

        TestContext.WriteLine($"Expected Match: {testCase.ExpectedIsMatch}");
        TestContext.WriteLine(
            $"Actual Result: {(result != null ? $"Match={result.IsMatchExitCondition}, Title={result.ExitConditionTitle}" : "null")}");

        if (testCase.ExpectedIsMatch)
        {
            Assert.That(result, Is.Not.Null, "Result should not be null when condition is expected to match");
            Assert.That(result.IsMatchExitCondition, Is.True, "IsMatchExitCondition should be true");
            Assert.That(
                result.ExitConditionTitle,
                Is.EqualTo(testCase.ExpectedTitle),
                "Exit condition title should match expected");

            if (!string.IsNullOrEmpty(testCase.ExpectedReasonContains))
            {
                Assert.That(
                    result.Reason,
                    Does.Contain(testCase.ExpectedReasonContains),
                    "Reason should contain expected text");
            }
        }
        else
        {
            Assert.That(result, Is.Null, "Result should be null when no condition matches");
        }
    }

    #endregion
}