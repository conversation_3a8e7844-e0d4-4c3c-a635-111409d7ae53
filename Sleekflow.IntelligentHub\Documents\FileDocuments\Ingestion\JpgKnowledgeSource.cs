using Azure;
using Azure.AI.DocumentIntelligence;
using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface IJpgKnowledgeSource : IKnowledgeSource
{
}

public class JpgKnowledgeSource : IJpgKnowledgeSource, IScopedService
{
    private readonly ILogger<JpgKnowledgeSource> _logger;
    private readonly Kernel _kernel;
    private readonly IAzureFormRecognizerConfig _azureFormRecognizerConfig;

    public JpgKnowledgeSource(
        ILogger<JpgKnowledgeSource> logger,
        Kernel kernel,
        IAzureFormRecognizerConfig azureFormRecognizerConfig)
    {
        _logger = logger;
        _kernel = kernel;
        _azureFormRecognizerConfig = azureFormRecognizerConfig;
    }

    public async Task<IKnowledgeSource.IngestionResult> Ingest(
        Stream blobStream,
        object? fileIngestionProgress)
    {
        // Initialize or use existing progress state
        JpgFileIngestionProgress jpgFileIngestionProgress;

        switch (fileIngestionProgress)
        {
            case null:
                // Initialize a new progress object if none is provided
                jpgFileIngestionProgress = new JpgFileIngestionProgress(false);
                break;
            case JpgFileIngestionProgress progress:
                // Direct instance of JpgFileIngestionProgress
                jpgFileIngestionProgress = progress;
                break;
            default:
                try
                {
                    // Try to deserialize as JSON
                    var jsonString = JsonConvert.SerializeObject(fileIngestionProgress);
                    jpgFileIngestionProgress = JsonConvert.DeserializeObject<JpgFileIngestionProgress>(jsonString)
                                               ?? throw new Exception(
                                                   "Failed to deserialize fileIngestionProgress to JpgFileIngestionProgress");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to interpret fileIngestionProgress as JpgFileIngestionProgress");
                    throw new Exception("FileIngestionProgress could not be interpreted as JpgFileIngestionProgress", ex);
                }

                break;
        }

        try
        {
            // Create Azure Document Intelligence client
            var credential = new AzureKeyCredential(_azureFormRecognizerConfig.AzureFormRecognizerKey);
            var client = new DocumentIntelligenceClient(
                new Uri(_azureFormRecognizerConfig.AzureFormRecognizerEndpoint),
                credential);

            // Convert image stream to BinaryData
            var imageData = await BinaryData.FromStreamAsync(blobStream);

            // Set up analysis options
            // Use "prebuilt-layout" to extract text and layout information, requesting Markdown output
            var options = new AnalyzeDocumentOptions("prebuilt-layout", imageData)
            {
                OutputContentFormat = DocumentContentFormat.Markdown
            };

            // Start the analysis operation
            _logger.LogInformation("Starting JPG document analysis.");
            var operation = await client.AnalyzeDocumentAsync(
                WaitUntil.Started,
                options);

            // Wait for the operation to complete
            var analyzeResponse = await operation.WaitForCompletionAsync();

            // Extract the content from the analysis result
            var markdownContent = analyzeResponse.Value?.Content;

            if (string.IsNullOrEmpty(markdownContent))
            {
                _logger.LogWarning("Document intelligence returned empty or null content for the JPG.");
                markdownContent = string.Empty; // Return empty string if analysis yields no content
            }
            else
            {
                _logger.LogInformation(
                    "Successfully converted JPG to markdown: {Length} characters",
                    markdownContent.Length);
            }

            // Update progress
            jpgFileIngestionProgress.IsIngestionCompleted = true;

            return new IKnowledgeSource.IngestionResult([markdownContent], jpgFileIngestionProgress);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An unexpected error occurred during JPG ingestion.");
            throw;
        }
    }
}