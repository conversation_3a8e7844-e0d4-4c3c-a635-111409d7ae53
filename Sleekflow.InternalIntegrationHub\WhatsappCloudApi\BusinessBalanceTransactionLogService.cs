using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.WhatsappCloudApi;

namespace Sleekflow.InternalIntegrationHub.WhatsappCloudApi;

public interface IBusinessBalanceTransactionLogService
{
    public Task<(List<BusinessBalanceTransactionLog> TransactionLogs, string? NextContinuationToken)>
        GetBusinessBalanceTransactionLogs(string companyId, string? continuationToken);
}

public class BusinessBalanceTransactionLogService : IScopedService, IBusinessBalanceTransactionLogService
{
    private readonly IRequestClient<GetBusinessBalanceTransactionLogsRequest>
        _getBusinessBalanceTransactionLogsRequestClient;

    public BusinessBalanceTransactionLogService(
        IRequestClient<GetBusinessBalanceTransactionLogsRequest> getBusinessBalanceTransactionLogsRequestClient)
    {
        _getBusinessBalanceTransactionLogsRequestClient = getBusinessBalanceTransactionLogsRequestClient;
    }

    public async Task<(List<BusinessBalanceTransactionLog> TransactionLogs, string? NextContinuationToken)>
        GetBusinessBalanceTransactionLogs(string companyId, string? continuationToken)
    {
        var getBusinessBalanceTransactionLogsResponse =
            await _getBusinessBalanceTransactionLogsRequestClient.GetResponse<GetBusinessBalanceTransactionLogsReply>(
                new GetBusinessBalanceTransactionLogsRequest(companyId, "TOP_UP", continuationToken));

        return (getBusinessBalanceTransactionLogsResponse.Message.BusinessBalanceTransactionLogs,
            getBusinessBalanceTransactionLogsResponse.Message.NextContinuationToken);
    }
}