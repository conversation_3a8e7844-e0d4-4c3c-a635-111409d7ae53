using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Plugins.Models;

public class DetectTextLanguageResponse
{
    [JsonProperty("reasoning")]
    public string Reasoning { get; set; }

    [<PERSON>son<PERSON>roperty("inputTextLanguageCode")]
    public string InputTextLanguageCode { get; set; }

    [JsonProperty("inputTextLanguageName")]
    public string InputTextLanguageName { get; set; }

    [JsonConstructor]
    public DetectTextLanguageResponse(string reasoning, string inputTextLanguageCode, string inputTextLanguageName)
    {
        Reasoning = reasoning;
        InputTextLanguageCode = inputTextLanguageCode;
        InputTextLanguageName = inputTextLanguageName;
    }
}