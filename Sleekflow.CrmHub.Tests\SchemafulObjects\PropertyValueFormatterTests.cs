﻿using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.SchemafulObjects.Dtos;
using Sleekflow.CrmHub.SchemafulObjects.Utils;

namespace Sleekflow.CrmHub.Tests.SchemafulObjects;

public class PropertyValueFormatterTests
{
    private const string SingleLineTextPropertyId = "single_line_text_id";
    private const string SingleChoicePropertyId = "single_choice_id";
    private const string MultipleChoicePropertyId = "multiple_choice_id";
    private const string ArrayObjectPropertyId = "array_object_id";

    private const string SingleLineTextPropertyUniqueName = "single_line_text";
    private const string SingleChoicePropertyUniqueName = "single_choice";
    private const string MultipleChoicePropertyUniqueName = "multiple_choice";
    private const string ArrayObjectPropertyUniqueName = "array_object";

    private const string OptionId1 = "option_id_1";
    private const string OptionId2 = "option_id_2";
    private const string OptionId3 = "option_id_3";

    private const string OptionValue1 = "option_value_1";
    private const string OptionValue2 = "option_value_2";
    private const string OptionValue3 = "option_value_3";

   [Test]
   public void FormatArrayObjectPropertyValue_WhenValueIsNull_ReturnsNull()
   {
       var property = GenerateArrayObjectProperty();
       object? value = null;

       var formattedValues = PropertyValueFormatter.FormatPropertyValueForFlowHubTriggerEventBody(
           new List<Property>{property},
           new Dictionary<string, object?>{{ArrayObjectPropertyId, value}},
           NullLogger.Instance);

       Assert.IsNull(formattedValues[ArrayObjectPropertyId]);
   }

   [Test]
   public void FormatArrayObjectPropertyValue_WhenValueIsNotInnerSchemafulObject_ReturnsRawValue()
   {
       var property = GenerateArrayObjectProperty();
       var value = "not_inner_schemaful_object";

       var formattedValues = PropertyValueFormatter.FormatPropertyValueForFlowHubTriggerEventBody(
           new List<Property>
           {
               property
           },
           new Dictionary<string, object?>
           {
               {
                   ArrayObjectPropertyId, value
               }
           },
           NullLogger.Instance);

       Assert.That(formattedValues[ArrayObjectPropertyId], Is.EqualTo(value));
   }

    [Test]
    public void FormatArrayObjectPropertyValue_WithCorrectInput_ReturnsFormattedValue()
    {
        var property = GenerateArrayObjectProperty();
        var value = new List<InnerSchemafulObjectDto>
        {
            new InnerSchemafulObjectDto(
                "1",
                new Dictionary<string, object?>
                {
                    {
                        SingleLineTextPropertyId, "text value"
                    },
                    {
                        SingleChoicePropertyId, OptionId1
                    },
                    {
                        MultipleChoicePropertyId, new List<string>
                        {
                            OptionId1, OptionId2
                        }
                    }
                },
                DateTimeOffset.UtcNow),
            new InnerSchemafulObjectDto(
                "2",
                new Dictionary<string, object?>
                {
                    {
                        SingleLineTextPropertyId, "text value"
                    },
                    {
                        SingleChoicePropertyId, OptionId2
                    },
                    {
                        MultipleChoicePropertyId, new List<string>
                        {
                            OptionId2, OptionId3
                        }
                    }
                },
                DateTimeOffset.UtcNow)
        };

        var formattedValues = PropertyValueFormatter.FormatPropertyValueForFlowHubTriggerEventBody(
            new List<Property>
            {
                property
            },
            new Dictionary<string, object?>
            {
                {
                    ArrayObjectPropertyId, value
                }
            },
            NullLogger.Instance);

        var formattedValue = (List<InnerSchemafulObjectDto>) formattedValues[ArrayObjectPropertyId]!;

        Assert.That(formattedValue.Count, Is.EqualTo(2));

        Assert.Multiple(() =>
        {
            var first = formattedValue[0];

            Assert.That(first.PropertyValues[SingleLineTextPropertyId], Is.EqualTo("text value"));
            Assert.That(first.PropertyValues[SingleLineTextPropertyUniqueName], Is.EqualTo("text value"));

            Assert.That(first.PropertyValues[SingleChoicePropertyId], Is.EqualTo(OptionId1));
            Assert.That(first.PropertyValues[SingleChoicePropertyUniqueName], Is.EqualTo(OptionValue1));

            Assert.That(first.PropertyValues[MultipleChoicePropertyId], Is.EqualTo(new List<string>{OptionId1, OptionId2}));
            Assert.That(first.PropertyValues[MultipleChoicePropertyUniqueName], Is.EqualTo(new List<string>{OptionValue1, OptionValue2}));
        });

        Assert.Multiple(() =>
        {
            var second = formattedValue[1];

            Assert.That(second.PropertyValues.Count, Is.EqualTo(6));
            Assert.That(second.PropertyValues[SingleLineTextPropertyUniqueName], Is.EqualTo("text value"));
            Assert.That(second.PropertyValues[SingleChoicePropertyUniqueName], Is.EqualTo(OptionValue2));
            Assert.That(second.PropertyValues[MultipleChoicePropertyUniqueName], Is.EqualTo(new List<string>{OptionValue2, OptionValue3}));
        });
    }

    private static Property GenerateArrayObjectProperty()
   {
       var innerSchema = new InnerSchema(GenerateProperties());

       return new Property(
           ArrayObjectPropertyId,
           ArrayObjectPropertyUniqueName,
           ArrayObjectPropertyUniqueName,
           new ArrayObjectDataType(innerSchema),
           false,
           true,
           true,
           true,
           0,
           null,
           DateTimeOffset.UtcNow,
           null);
   }

   private static List<Property> GenerateProperties()
   {
       var options = new List<Option>
       {
           new Option(OptionId1, OptionValue1, 0),
           new Option(OptionId2, OptionValue2, 1),
           new Option(OptionId3, OptionValue3, 2),
       };

       return new List<Property>
       {
           new Property(
               SingleLineTextPropertyId,
               SingleLineTextPropertyUniqueName,
               SingleLineTextPropertyUniqueName,
               new SingleLineTextDataType(),
               false,
               true,
               true,
               true,
               0,
               null,
               DateTimeOffset.UtcNow,
               null),
           new Property(
               SingleChoicePropertyId,
               SingleChoicePropertyUniqueName,
               SingleChoicePropertyUniqueName,
               new SingleChoiceDataType(),
               false,
               true,
               true,
               true,
               0,
               null,
               DateTimeOffset.UtcNow,
               options),
           new Property(
               MultipleChoicePropertyId,
               MultipleChoicePropertyUniqueName,
               MultipleChoicePropertyUniqueName,
               new MultipleChoiceDataType(),
               false,
               true,
               true,
               true,
               0,
               null,
               DateTimeOffset.UtcNow,
               options)
       };
   }
}