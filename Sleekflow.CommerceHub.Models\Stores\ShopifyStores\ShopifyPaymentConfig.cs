using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Stores.ShopifyStores;

public class ShopifyPaymentConfig
{
    [JsonProperty("payment_link_option")]
    public string PaymentLinkOption { get; set; }

    [JsonProperty("is_discounts_enabled")]
    public bool IsDiscountsEnabled { get; set; }

    [JsonConstructor]
    public ShopifyPaymentConfig(
        string paymentLinkOption,
        bool isDiscountsEnabled)
    {
        PaymentLinkOption = paymentLinkOption;
        IsDiscountsEnabled = isDiscountsEnabled;
    }
}