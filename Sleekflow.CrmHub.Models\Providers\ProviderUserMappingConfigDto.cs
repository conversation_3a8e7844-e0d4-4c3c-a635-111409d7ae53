﻿using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Providers;

public class ProviderUserMappingConfigDto : IHasSleekflowCompanyId
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [JsonProperty("user_mappings")]
    public List<UserMapping>? UserMappings { get; set; }

    [JsonConstructor]
    public ProviderUserMappingConfigDto(
        string id,
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        UserMappings = userMappings;
    }
}