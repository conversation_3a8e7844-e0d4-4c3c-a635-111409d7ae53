using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactPropertiesStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-properties";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [Required]
    [JsonProperty("properties__key_expr_dict")]
    public Dictionary<string, string?> PropertiesKeyExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactPropertiesStepArgs(
        string contactIdExpr,
        Dictionary<string, string?> propertiesKeyExprDict)
    {
        ContactIdExpr = contactIdExpr;
        PropertiesKeyExprDict = propertiesKeyExprDict;
    }
}