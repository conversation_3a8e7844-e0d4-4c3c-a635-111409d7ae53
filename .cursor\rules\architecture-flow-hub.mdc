---
description: This rule could help you understand the flow hub architecture.
globs:
alwaysApply: false
---
# FlowHub Architecture

Sleekflow.FlowHub is an asynchronous workflow engine that orchestrates business flows and custom logic.

## Core Components

### Workflows
- **WorkflowService**: Manages workflow definitions, versioning and activation status
- **WorkflowExecutionService**: Orchestrates workflow execution
- **WorkflowRuntimeService**: Handles workflow lifecycle events and state transitions

### Steps
Step executors implement the execution logic for different step types:
- **GeneralStepExecutor**: Base class for step executors
- **HttpGetStepExecutor**: Example step executor for making HTTP requests
- **StepExecutorMatcher**: Matches steps to their appropriate executors
- **StepExecutorActivator**: Activates step executor instances

### State Management
- **IStateEvaluator**: Evaluates expressions in step arguments using Scriban templating
- **IStateAggregator**: Updates workflow state with step results

### Triggers
- **WorkflowWebhookTrigger**: Entry point for webhook-triggered workflows
- **PublicController**: Handles incoming webhook requests

## Execution Flow

1. A trigger event activates a workflow (via webhook, API, etc.)
2. WorkflowExecutionService creates a state and initiates execution
3. StepExecutorMatcher identifies the appropriate executor for each step
4. Step executors evaluate expressions and perform actions
5. Results are added to the workflow state
6. Workflow progresses to next step or completes

## Key Documentation

The project includes several guides:
- Main [README.md](mdc:Sleekflow.FlowHub/README.md): Overview and architecture
- [GETTING_STARTED.md](mdc:Sleekflow.FlowHub/GETTING_STARTED.md): Implementing custom step executors
- [STATE_EVALUATION.md](mdc:Sleekflow.FlowHub/STATE_EVALUATION.md): Working with expressions and state evaluation

