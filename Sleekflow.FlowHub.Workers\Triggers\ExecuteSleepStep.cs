using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.FlowHub.Workers.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers;

public class ExecuteSleepStep
{
    private readonly ILogger<ExecuteSleepStep> _logger;

    public ExecuteSleepStep(
        ILogger<ExecuteSleepStep> logger)
    {
        _logger = logger;
    }

    [Function("ExecuteSleepStep")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<ExecuteSleepStepInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (ExecuteSleepStepInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (executeSleepStepInput, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            "ExecuteSleepStep_Orchestrator",
            executeSleepStepInput);

        logger.LogInformation($"Started ExecuteSleepStep_Orchestrator with ID = [{instanceId}]");

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get ExecuteSleepStep_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}