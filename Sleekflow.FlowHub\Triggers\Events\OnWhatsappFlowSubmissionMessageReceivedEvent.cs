﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnWhatsappFlowSubmissionMessageReceivedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;

    public OnWhatsappFlowSubmissionMessageReceivedEvent(IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnWhatsappFlowSubmissionMessageReceivedEventInput
        : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [Required]
        public OnWhatsappFlowSubmissionMessageReceivedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnWhatsappFlowSubmissionMessageReceivedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnWhatsappFlowSubmissionMessageReceivedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnWhatsappFlowSubmissionMessageReceivedEventOutput
    {
    }

    public async Task<OnWhatsappFlowSubmissionMessageReceivedEventOutput> F(
        OnWhatsappFlowSubmissionMessageReceivedEventInput onWhatsappFlowSubmissionMessageReceivedEventInput)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                onWhatsappFlowSubmissionMessageReceivedEventInput.EventBody,
                onWhatsappFlowSubmissionMessageReceivedEventInput.ContactId,
                "Contact",
                onWhatsappFlowSubmissionMessageReceivedEventInput.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onWhatsappFlowSubmissionMessageReceivedEventInput.SleekflowCompanyId));

        return new OnWhatsappFlowSubmissionMessageReceivedEventOutput();
    }
}