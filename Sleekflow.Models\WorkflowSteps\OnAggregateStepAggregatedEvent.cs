using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.WorkflowSteps;

public class OnAggregateStepAggregatedEvent
{
    [JsonProperty("aggregate_step_id")]
    public string AggregateStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("stack_entries")]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public OnAggregateStepAggregatedEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
    }
}