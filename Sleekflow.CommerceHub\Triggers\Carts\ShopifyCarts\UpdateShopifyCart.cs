using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Carts.ShopifyCarts;

[TriggerGroup(ControllerNames.ShopifyCarts)]
public class UpdateShopifyCart
    : ITrigger<
        UpdateShopifyCart.UpdateShopifyCartInput,
        UpdateShopifyCart.UpdateShopifyCartOutput>
{
    private readonly IShopifyCartService _shopifyCartService;
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;

    public UpdateShopifyCart(
        IShopifyCartService shopifyCartService,
        IProductService productService,
        IProductVariantService productVariantService)
    {
        _shopifyCartService = shopifyCartService;
        _productService = productService;
        _productVariantService = productVariantService;
    }

    public class UpdateShopifyCartInput :
        IHasSleekflowCompanyId, IHasSleekflowUserProfileId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("original_cart_status")]
        public string OriginalCartStatus { get; set; }

        [Required]
        [JsonProperty("shopify_cart_token")]
        public string ShopifyCartToken { get; set; }

        [Required]
        [JsonProperty("target_cart_status")]
        public string TargetCartStatus { get; set; }

        [ValidateObject]
        [JsonProperty("cart_discount")]
        public Discount? CartDiscount { get; set; }

        [Required]
        [JsonProperty("checkout_url")]
        public string CheckoutUrl { get; set; }

        [Required]
        [JsonProperty("conversion_status")]
        public string ConversionStatus { get; set; }

        [Range(1, 1000000000)]
        [Required]
        [ValidateObject]
        [JsonProperty("total_price")]
        public decimal TotalPrice { get; set; }

        [Range(1, 1000000000)]
        [Required]
        [ValidateObject]
        [JsonProperty("total_tax")]
        public decimal TotalTax { get; set; }

        [Range(1, 1000000000)]
        [Required]
        [ValidateObject]
        [JsonProperty("subtotal_price")]
        public decimal SubtotalPrice { get; set; }

        [JsonProperty("currency_iso_code")]
        public string? CurrencyIsoCode { get; set; }

        [JsonProperty("shopify_customer_id")]
        public string? ShopifyCustomerId { get; set; }

        [ValidateObject]
        [JsonProperty("abandoned_date")]
        public DateTimeOffset? AbandonedDate { get; set; }

        [ValidateObject]
        [JsonProperty("recovered_date")]
        public DateTimeOffset? RecoveredDate { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [Required]
        [ValidateArray]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateShopifyCartInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId,
            string originalCartStatus,
            string shopifyCartToken,
            string targetCartStatus,
            Discount? cartDiscount,
            string checkoutUrl,
            string conversionStatus,
            decimal totalPrice,
            decimal totalTax,
            decimal subtotalPrice,
            string? currencyIsoCode,
            string? shopifyCustomerId,
            DateTimeOffset? abandonedDate,
            DateTimeOffset? recoveredDate,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
            OriginalCartStatus = originalCartStatus;
            ShopifyCartToken = shopifyCartToken;
            TargetCartStatus = targetCartStatus;
            CartDiscount = cartDiscount;
            CheckoutUrl = checkoutUrl;
            ConversionStatus = conversionStatus;
            TotalPrice = totalPrice;
            TotalTax = totalTax;
            SubtotalPrice = subtotalPrice;
            CurrencyIsoCode = currencyIsoCode;
            ShopifyCustomerId = shopifyCustomerId;
            AbandonedDate = abandonedDate;
            RecoveredDate = recoveredDate;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateShopifyCartOutput
    {
        [JsonProperty("shopify_cart")]
        public ShopifyCartDto ShopifyCart { get; }

        [JsonConstructor]
        public UpdateShopifyCartOutput(ShopifyCartDto shopifyCart)
        {
            ShopifyCart = shopifyCart;
        }
    }

    public async Task<UpdateShopifyCartOutput> F(
        UpdateShopifyCartInput updateShopifyCartInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateShopifyCartInput.SleekflowStaffId,
            updateShopifyCartInput.SleekflowStaffTeamIds);

        var updatedShopifyCart = await _shopifyCartService.UpdateAndGetShopifyCartAsync(
            updateShopifyCartInput.SleekflowCompanyId,
            updateShopifyCartInput.SleekflowUserProfileId,
            updateShopifyCartInput.StoreId,
            updateShopifyCartInput.OriginalCartStatus,
            updateShopifyCartInput.ShopifyCartToken,
            updateShopifyCartInput.TargetCartStatus,
            updateShopifyCartInput.CartDiscount,
            updateShopifyCartInput.CheckoutUrl,
            updateShopifyCartInput.ConversionStatus,
            updateShopifyCartInput.TotalPrice,
            updateShopifyCartInput.TotalTax,
            updateShopifyCartInput.SubtotalPrice,
            updateShopifyCartInput.CurrencyIsoCode,
            updateShopifyCartInput.ShopifyCustomerId,
            updateShopifyCartInput.AbandonedDate,
            updateShopifyCartInput.RecoveredDate,
            sleekflowStaff);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            updatedShopifyCart.LineItems.Select(x => x.ProductVariantId).ToList(),
            updatedShopifyCart.SleekflowCompanyId,
            updatedShopifyCart.StoreId);
        var productVariantIdToProductVariantDtoDict = productVariants
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var products = await _productService.GetProductsAsync(
            productVariants.Select(x => x.ProductId).Distinct().ToList(),
            updatedShopifyCart.SleekflowCompanyId,
            updatedShopifyCart.StoreId);
        var productIdToProductDict = products
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        return new UpdateShopifyCartOutput(
            new ShopifyCartDto(
                updatedShopifyCart,
                productVariantIdToProductVariantDtoDict,
                productIdToProductDict));
    }
}