using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments.Ingestion;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Sleekflow.IntelligentHub.TextEnrichments;

namespace Sleekflow.IntelligentHub.Workers.Services;

public interface IWebsiteIngestionService
{
    Task ProcessWebsiteDocument(
        string sleekflowCompanyId,
        string documentId);

    Task<IFileIngestionProgress> ProcessWebsiteFileDocument(
        string sleekflowCompanyId,
        string documentId,
        string url,
        string mimeType,
        object? fileIngestionProgress);
}

public class WebsiteIngestionService : IScopedService, IWebsiteIngestionService
{
    private readonly ILogger<WebsiteIngestionService> _logger;
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IWebsiteDocumentService _websiteDocumentService;
    private readonly IWebsiteDocumentChunkService _websiteDocumentChunkService;
    private readonly ITextTranslationService _textTranslationService;
    private readonly HttpClient _httpClient; // Reuse HttpClient for better performance
    private readonly IKnowledgeSourceFactory _knowledgeSourceFactory;
    private readonly IWebsiteKnowledgeSource _websiteKnowledgeSource;

    public WebsiteIngestionService(
        ILogger<WebsiteIngestionService> logger,
        IKbDocumentService kbDocumentService,
        IWebsiteDocumentService websiteDocumentService,
        IWebsiteDocumentChunkService websiteDocumentChunkService,
        ITextTranslationService textTranslationService,
        IHttpClientFactory httpClientFactory,
        IKnowledgeSourceFactory knowledgeSourceFactory,
        IWebsiteKnowledgeSource websiteKnowledgeSource)
    {
        _logger = logger;
        _kbDocumentService = kbDocumentService;
        _websiteDocumentService = websiteDocumentService;
        _websiteDocumentChunkService = websiteDocumentChunkService;
        _textTranslationService = textTranslationService;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _knowledgeSourceFactory = knowledgeSourceFactory;
        _websiteKnowledgeSource = websiteKnowledgeSource;
    }

    public async Task<IFileIngestionProgress> ProcessWebsiteFileDocument(
        string sleekflowCompanyId,
        string documentId,
        string url,
        string mimeType,
        object? fileIngestionProgress)
    {
        _logger.LogInformation(
            "ProcessWebsiteFileDocument converting {DocumentId} from URL {Url} with MIME type {MimeType}",
            documentId,
            url,
            mimeType);

        // Download the file from the URL using HTTP GET
        // Note: we are downloading every single time, maybe we should cache the file somewhere
        using var response = await _httpClient.GetAsync(url);
        response.EnsureSuccessStatusCode();

        using var fileStream = await response.Content.ReadAsStreamAsync();

        // Get the appropriate knowledge source for this MIME type
        var knowledgeSource = _knowledgeSourceFactory.GetKnowledgeSourceByMimeType(mimeType);

        // Use the knowledge source to ingest the file
        var (markdowns, updatedFileIngestionProgress) =
            await knowledgeSource.Ingest(fileStream, fileIngestionProgress);

        _logger.LogInformation(
            "ProcessWebsiteFileDocument conversion finished for {DocumentId} from URL {Url}. Generated {MarkdownCount} markdown chunks",
            documentId,
            url,
            markdowns.Length);

        // Store the markdowns as website document chunks
        if (markdowns.Length > 0)
        {
            var chunkIds = await StoreMarkdownsAsWebsiteDocumentChunks(
                sleekflowCompanyId,
                documentId,
                markdowns.Select(markdown => (url, markdown)).ToArray());

            _logger.LogInformation(
                "Stored {ChunkCount} chunks for document {DocumentId} from URL {Url}",
                chunkIds.Count,
                documentId,
                url);
        }

        return updatedFileIngestionProgress;
    }

    public async Task ProcessWebsiteDocument(
        string sleekflowCompanyId,
        string documentId)
    {
        var websiteDocument =
            (WebsiteDocument) await _kbDocumentService.GetDocumentAsync(sleekflowCompanyId, documentId);

        _logger.LogInformation(
            "Processing website document: {DocumentId}, baseUrl: {BaseUrl}, selectedUrls count: {SelectedUrls}",
            documentId,
            websiteDocument.BaseUrl,
            websiteDocument.SelectedUrls.Count);

        // Process URLs in batches
        var pendingSelectedUrls =
            websiteDocument.SelectedUrls.Where(selectedUrl => selectedUrl.Status == SelectedUrlStatuses.Pending)
                .ToList();

        _logger.LogInformation(
            "Processing website document: {DocumentId}, pending selected url count: {SelectedUrls}",
            documentId,
            pendingSelectedUrls.Count);

        if (pendingSelectedUrls.Count == 0)
        {
            return;
        }

        const int maxBatchSize = 10;
        var batchSize = Math.Min(maxBatchSize, pendingSelectedUrls.Count);
        var batch = pendingSelectedUrls.Take(batchSize)
            .Select(selectedUrl => selectedUrl.PageUrl)
            .ToList();

        // Update URL statuses to "converting" before processing
        await _websiteDocumentService.UpdateUrlStatusesAsync(
            sleekflowCompanyId,
            documentId,
            batch,
            SelectedUrlStatuses.Converting);

        var successfulResults = new ConcurrentBag<(string Url, string Markdown)>();
        var failedUrls = new ConcurrentBag<string>();

        await Parallel.ForEachAsync(
            batch,
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = batchSize
            },
            async (url, token) =>
            {
                _logger.LogInformation("Processing URL: {Url}", url);

                try
                {
                    var markdown = await _websiteKnowledgeSource.Ingest(url);

                    successfulResults.Add((url, markdown));
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing URL {Url}: {Error}", url, ex.Message);
                    failedUrls.Add(url);
                }
            });

        // Store markdowns as WebsiteDocumentChunks for successful URLs
        if (successfulResults.Any())
        {
            await StoreMarkdownsAsWebsiteDocumentChunks(
                sleekflowCompanyId,
                documentId,
                successfulResults.ToArray());
        }

        // Update URL statuses based on processing results
        if (successfulResults.Any())
        {
            await _websiteDocumentService.UpdateUrlStatusesAsync(
                sleekflowCompanyId,
                documentId,
                successfulResults.Select(r => r.Url).ToList(),
                SelectedUrlStatuses.Converted);
        }

        if (failedUrls.Any())
        {
            await _websiteDocumentService.UpdateUrlStatusesAsync(
                sleekflowCompanyId,
                documentId,
                failedUrls.ToList(),
                SelectedUrlStatuses.Failed);
        }

        _logger.LogInformation(
            "ProcessWebsiteDocument batch completed for {DocumentId}. Processed {ProcessedCount}/{TotalCount} URLs. Remaining: {RemainingUrls}",
            documentId,
            successfulResults.Count,
            successfulResults.Count + failedUrls.Count,
            pendingSelectedUrls.Count - batchSize);
    }

    private async Task<List<string>> StoreMarkdownsAsWebsiteDocumentChunks(
        string sleekflowCompanyId,
        string documentId,
        (string Url, string Markdown)[] markdownResults)
    {
        var chunkIds = new ConcurrentBag<string>();

        await Parallel.ForEachAsync(
            markdownResults,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 8
            },
            async (result, ct) =>
            {
                // translate
                var contentEn = await _textTranslationService.TranslateByAzureTranslationServiceAsync(
                    TranslationSupportedLanguages.English,
                    result.Markdown);

                var documentChunk = await _websiteDocumentChunkService.CreateWebsiteDocumentChunkAsync(
                    sleekflowCompanyId,
                    documentId,
                    result.Url,
                    result.Markdown,
                    contentEn,
                    new List<Category>(),
                    new Dictionary<string, object?>());

                chunkIds.Add(documentChunk.Id);
            });

        return chunkIds.ToList();
    }
}