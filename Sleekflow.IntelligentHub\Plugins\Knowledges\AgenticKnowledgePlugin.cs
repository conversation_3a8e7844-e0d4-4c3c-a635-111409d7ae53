using System.ComponentModel;
using System.Text;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using OpenAI.Chat;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Plugins.Models;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class AgenticKnowledgePluginResponse : QueryKnowledgeResponse
{
    public AgenticKnowledgePluginResponse(string knowledge, string id)
        : base(knowledge, id, "AgenticKnowledgeBase")
    {
    }
}

public interface IAgenticKnowledgePlugin : IKnowledgePlugin
{
}

public class AgenticKnowledgePlugin : IAgenticKnowledgePlugin, IScopedService
{
    private readonly ILogger<AgenticKnowledgePlugin> _logger;
    private readonly IKnowledgeRetrievalAgentDefinition _knowledgeRetrievalAgentDefinition;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly IAgentDurationTracker _agentDurationTracker;
    private readonly IAgenticKnowledgePluginStorageService _agenticKnowledgePluginStorageService;

    public AgenticKnowledgePlugin(
        ILogger<AgenticKnowledgePlugin> logger,
        IKnowledgeRetrievalAgentDefinition knowledgeRetrievalAgentDefinition,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        IAgenticKnowledgePluginStorageService agenticKnowledgePluginStorageService)
    {
        _logger = logger;
        _knowledgeRetrievalAgentDefinition = knowledgeRetrievalAgentDefinition;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _agentDurationTracker = agentDurationTracker;
        _agenticKnowledgePluginStorageService = agenticKnowledgePluginStorageService;
    }

    [KernelFunction("query_knowledge")]
    [Description(
        "Retrieves information from the knowledge base.")]
    [return: Description(
        "Tailored information from the knowledge base.")]
    public async Task<QueryKnowledgeResponse> QueryKnowledgeAsync(
        Kernel kernel,
        [Description(
            "Express your information needs naturally, whether as direct questions, scenario descriptions, or specific information requests. Provide clear context, specific requirements, and any relevant background details for the most accurate and useful results. ")]
        string query)
    {
        var knowledgeRetrievalAgent = _knowledgeRetrievalAgentDefinition.GetKnowledgeRetrievalAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1_MINI, true),
            query);

        var agentThread = new ChatHistoryAgentThread();

        var sb = new StringBuilder();

        while (true)
        {
            var isCompleted = false;

            await foreach (var agentResponseItem in knowledgeRetrievalAgent
                               .InvokeAsync(
                                   agentThread,
                                   new AgentInvokeOptions
                                   {
                                       OnIntermediateMessage = chatMessageContent =>
                                       {
                                           if (chatMessageContent.Role == AuthorRole.Tool)
                                           {
                                               _agenticKnowledgePluginStorageService.StoreToolResult(
                                                   chatMessageContent.Content ?? string.Empty);
                                           }

                                           if (chatMessageContent is OpenAIChatMessageContent
                                               {
                                                   InnerContent: ChatCompletion chatCompletion
                                               })
                                           {
                                               var usageInputTokenCount = chatCompletion.Usage.InputTokenCount;
                                               var usageOutputTokenCount = chatCompletion.Usage.OutputTokenCount;

                                               _agentDurationTracker.TrackResponse(
                                                   usageInputTokenCount,
                                                   usageOutputTokenCount,
                                                   "AgenticKnowledgePlugin",
                                                   chatMessageContent.Content ?? string.Empty,
                                                   chatCompletion.Usage.InputTokenDetails.CachedTokenCount);
                                           }

                                           return Task.CompletedTask;
                                       }
                                   }))
            {
                if (agentResponseItem.Message.Content != null)
                {
                    var jsonObjects = SeparateMultipleJsonObjects(agentResponseItem.Message.Content);
                    foreach (var jsonString in jsonObjects)
                    {
                        if (JsonUtils.TryParseJson<KnowledgeRetrievalAgentResponse>(jsonString, out var json)
                            && json?.Phase == "report")
                        {
                            isCompleted = true;
                            break;
                        }
                    }
                }
            }

            if (isCompleted)
            {
                break;
            }
        }

        var contents = agentThread.ChatHistory
            .Where(c => c.Role == AuthorRole.Tool)
            .Select(c => c.Content)
            .ToList();
        foreach (var content in contents.Where(content => !string.IsNullOrEmpty(content)))
        {
            sb.AppendLine(content);
        }

        sb.AppendLine($"<CONFIRMED_KNOWLEDGE>{agentThread.ChatHistory[^1]}</CONFIRMED_KNOWLEDGE>");

        return new AgenticKnowledgePluginResponse(sb.ToString(), Guid.NewGuid().ToString());
    }

    /// <summary>
    /// Separates multiple JSON objects that may be concatenated in a single string.
    /// This method intelligently parses through the content to identify complete JSON objects
    /// by tracking opening and closing braces while properly handling string literals and escape sequences.
    /// </summary>
    /// <param name="content">The input string that may contain one or more JSON objects concatenated together</param>
    /// <returns>
    /// A list of individual JSON object strings. If no valid JSON structure is detected,
    /// returns a list containing the original content as a single item (fallback behavior).
    /// </returns>
    /// <remarks>
    /// This method is designed to handle cases where LLM providers send multiple JSON responses
    /// concatenated together, which causes standard JSON parsing to fail. The method correctly:
    /// - Tracks brace nesting levels to identify complete JSON objects
    /// - Ignores braces that appear within string literals
    /// - Handles escaped characters and quotes properly
    /// - Preserves the content of each individual JSON object
    /// </remarks>
    private static List<string> SeparateMultipleJsonObjects(string content)
    {
        var jsonObjects = new List<string>();
        var braceCount = 0;
        var startIndex = 0;
        var inString = false;
        var escapeNext = false;

        for (var i = 0; i < content.Length; i++)
        {
            var currentChar = content[i];

            if (escapeNext)
            {
                escapeNext = false;
                continue;
            }

            if (currentChar == '\\')
            {
                escapeNext = true;
                continue;
            }

            if (currentChar == '"')
            {
                inString = !inString;
                continue;
            }

            if (inString)
            {
                continue;
            }

            switch (currentChar)
            {
                case '{':
                    if (braceCount == 0)
                    {
                        startIndex = i;
                    }
                    braceCount++;
                    break;
                case '}':
                    braceCount--;
                    if (braceCount == 0)
                    {
                        var jsonString = content.Substring(startIndex, i - startIndex + 1).Trim();
                        if (!string.IsNullOrEmpty(jsonString))
                        {
                            jsonObjects.Add(jsonString);
                        }
                    }
                    break;
            }
        }

        // If no valid JSON objects were found, return the original content as a single item
        if (jsonObjects.Count == 0 && !string.IsNullOrWhiteSpace(content))
        {
            jsonObjects.Add(content.Trim());
        }

        return jsonObjects;
    }
}