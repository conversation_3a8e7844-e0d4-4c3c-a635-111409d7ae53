using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Renderings;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.TemplateRenderers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Stores;

[TriggerGroup(ControllerNames.Stores)]
public class PreviewStoreTemplates
    : ITrigger<
        PreviewStoreTemplates.PreviewStoreTemplatesInput,
        PreviewStoreTemplates.PreviewStoreTemplatesOutput>
{
    private readonly IStoreService _storeService;
    private readonly ITemplateRenderer _templateRenderer;

    public PreviewStoreTemplates(
        IStoreService storeService,
        ITemplateRenderer templateRenderer)
    {
        _storeService = storeService;
        _templateRenderer = templateRenderer;
    }

    public class PreviewStoreTemplatesInput
    {
        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public PreviewStoreTemplatesInput(
            string storeId,
            string sleekflowCompanyId)
        {
            StoreId = storeId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class PreviewStoreTemplatesOutput
    {
        [JsonProperty("rendered_template_dict")]
        public Dictionary<string, RenderedTemplate> RenderedTemplateDict { get; set; }

        [JsonConstructor]
        public PreviewStoreTemplatesOutput(Dictionary<string, RenderedTemplate> renderedTemplateDict)
        {
            RenderedTemplateDict = renderedTemplateDict;
        }
    }

    public async Task<PreviewStoreTemplatesOutput> F(PreviewStoreTemplatesInput previewStoreTemplatesInput)
    {
        var store = await _storeService.GetStoreAsync(
            previewStoreTemplatesInput.StoreId,
            previewStoreTemplatesInput.SleekflowCompanyId);

        var renderedTemplateDict = new Dictionary<string, RenderedTemplate>();

        foreach (var multilingualTemplate in store.TemplateDict.MessagePreviewTemplates)
        {
            var renderedTemplate = await _templateRenderer.PreviewProductVariantTemplateAsync(
                multilingualTemplate.Value,
                multilingualTemplate.LanguageIsoCode);

            renderedTemplateDict.Add(
                $"MessagePreview_{multilingualTemplate.LanguageIsoCode}",
                renderedTemplate);
        }

        return new PreviewStoreTemplatesOutput(renderedTemplateDict);
    }
}