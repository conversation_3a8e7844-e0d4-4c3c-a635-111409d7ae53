﻿using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public sealed class GetCustomObjectSchemaReply
{
    [JsonProperty("id")]
    public string Id { get; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("display_name")]
    public string DisplayName { get; set; }

    [JsonProperty("unique_name")]
    public string UniqueName { get; set; }

    [JsonProperty("primary_property")]
    public CustomObjectSchemaPrimaryProperty PrimaryProperty { get; set; }

    [JsonProperty("properties")]
    public List<CustomObjectProperty> Properties { get; set; }

    [JsonConstructor]
    public GetCustomObjectSchemaReply(
        string id,
        string sleekflowCompanyId,
        string displayName,
        string uniqueName,
        CustomObjectSchemaPrimaryProperty primaryProperty,
        List<CustomObjectProperty> properties)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        DisplayName = displayName;
        UniqueName = uniqueName;
        PrimaryProperty = primaryProperty;
        Properties = properties;
        Id = id;
    }
}

public sealed class CustomObjectSchemaPrimaryProperty
{
    [JsonProperty("id")]
    public string Id { get; }

    [JsonProperty("display_name")]
    public string DisplayName { get; set; }

    [JsonProperty("unique_name")]
    public string UniqueName { get; set; }

    [JsonProperty("data_type")]
    public CustomObjectPropertyDataType DataType { get; }

    [JsonProperty("primary_property_config")]
    public CustomObjectSchemaPrimaryPropertyConfig Config { get; set; }

    [JsonConstructor]
    public CustomObjectSchemaPrimaryProperty(
        string id,
        string displayName,
        string uniqueName,
        CustomObjectPropertyDataType dataType,
        CustomObjectSchemaPrimaryPropertyConfig config)
    {
        Id = id;
        DisplayName = displayName;
        UniqueName = uniqueName;
        DataType = dataType;
        Config = config;
    }
}

public sealed class CustomObjectPropertyDataType
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonConstructor]
    public CustomObjectPropertyDataType(string name)
    {
        Name = name;
    }
}

public sealed class CustomObjectSchemaPrimaryPropertyConfig
{
    [JsonProperty("is_auto_generated")]
    public bool IsAutoGenerated { get; set; }

    [JsonConstructor]
    public CustomObjectSchemaPrimaryPropertyConfig(
        bool isAutoGenerated)
    {
        IsAutoGenerated = isAutoGenerated;
    }
}

public sealed class CustomObjectProperty
{
    [JsonProperty("id")]
    public string Id { get; }

    [JsonProperty("display_name")]
    public string DisplayName { get; set; }

    [JsonProperty("unique_name")]
    public string UniqueName { get; set; }

    [JsonProperty("data_type")]
    public CustomObjectPropertyDataType DataType { get; }

    [JsonProperty("is_required")]
    public bool IsRequired { get; set; }

    [JsonProperty("display_order")]
    public int DisplayOrder { get; set; }

    [JsonProperty("options")]
    public List<CustomObjectPropertyOption>? Options { get; set; }

    [JsonConstructor]
    public CustomObjectProperty(
        string id,
        string displayName,
        string uniqueName,
        CustomObjectPropertyDataType dataType,
        bool isRequired,
        int displayOrder,
        List<CustomObjectPropertyOption>? options)
    {
        Id = id;
        DisplayName = displayName;
        UniqueName = uniqueName;
        DataType = dataType;
        IsRequired = isRequired;
        DisplayOrder = displayOrder;
        Options = options;
    }
}

public sealed class CustomObjectPropertyOption
{
    public const string PropertyNameId = "id";
    public const string PropertyNameValue = "value";
    public const string PropertyNameDisplayOrder = "display_order";

    [JsonProperty("id")]
    public string Id { get; }

    [JsonProperty("value")]
    public string Value { get; set; }

    [JsonProperty("display_order")]
    public int DisplayOrder { get; set; }

    [JsonConstructor]
    public CustomObjectPropertyOption(
        string id,
        string value,
        int displayOrder)
    {
        Id = id;
        Value = value;
        DisplayOrder = displayOrder;
    }
}