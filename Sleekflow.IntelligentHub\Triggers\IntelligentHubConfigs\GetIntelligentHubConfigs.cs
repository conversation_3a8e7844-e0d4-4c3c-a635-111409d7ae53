using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class GetIntelligentHubConfigs
    : ITrigger<GetIntelligentHubConfigs.GetIntelligentHubConfigsInput,
        GetIntelligentHubConfigs.GetIntelligentHubConfigsOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public GetIntelligentHubConfigs(IIntelligentHubConfigService intelligentHubConfigService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    public class GetIntelligentHubConfigsInput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetIntelligentHubConfigsInput(
            string? continuationToken,
            int limit)
        {
            ContinuationToken = continuationToken;
            Limit = limit;
        }
    }

    public class GetIntelligentHubConfigsOutput
    {
        [JsonProperty("intelligent_hub_configs")]
        public List<IntelligentHubConfig> IntelligentHubConfigs { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetIntelligentHubConfigsOutput(
            List<IntelligentHubConfig> intelligentHubConfigs,
            string? continuationToken)
        {
            IntelligentHubConfigs = intelligentHubConfigs;
            ContinuationToken = continuationToken;
        }
    }

    public async Task<GetIntelligentHubConfigsOutput> F(GetIntelligentHubConfigsInput getIntelligentHubConfigInput)
    {
        var (intelligentHubConfigs, continuationToken) =
            await _intelligentHubConfigService.GetIntelligentHubConfigsAsync(
                getIntelligentHubConfigInput.ContinuationToken,
                getIntelligentHubConfigInput.Limit);

        return new GetIntelligentHubConfigsOutput(intelligentHubConfigs, continuationToken);
    }
}