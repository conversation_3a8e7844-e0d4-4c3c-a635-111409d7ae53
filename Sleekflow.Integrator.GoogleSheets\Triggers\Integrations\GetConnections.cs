﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetConnections : ITrigger
{
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;

    public GetConnections(
        IGoogleSheetsConnectionService googleSheetsConnectionService)
    {
        _googleSheetsConnectionService = googleSheetsConnectionService;
    }

    public class GetConnectionsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetConnectionsInput(
            string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GoogleSheetsConnectionDto : IHasSleekflowCompanyId
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("organization_id")]
        public string OrganizationId { get; set; }

        [JsonProperty("is_active")]
        public bool IsActive { get; set; }

        [JsonConstructor]
        public GoogleSheetsConnectionDto(
            GoogleSheetsConnection connection)
        {
            Id = connection.Id;
            SleekflowCompanyId = connection.SleekflowCompanyId;
            Name = connection.Name;
            OrganizationId = connection.OrganizationId;
            IsActive = connection.IsActive;
        }
    }

    public class GetConnectionsOutput
    {
        [JsonProperty("connections")]
        [Required]
        public List<GoogleSheetsConnectionDto> Connections { get; set; }

        [JsonConstructor]
        public GetConnectionsOutput(
            List<GoogleSheetsConnectionDto> connections)
        {
            Connections = connections;
        }
    }

    public async Task<GetConnectionsOutput> F(
        GetConnectionsInput getConnectionsInput)
    {
        var connections =
            await _googleSheetsConnectionService.GetConnectionsAsync(getConnectionsInput.SleekflowCompanyId);

        return new GetConnectionsOutput(
            connections.Select(c => new GoogleSheetsConnectionDto(c)).ToList());
    }
}