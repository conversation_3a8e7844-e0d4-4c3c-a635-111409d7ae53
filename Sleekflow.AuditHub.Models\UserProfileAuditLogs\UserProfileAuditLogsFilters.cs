using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs;

public class UserProfileAuditLogsFilters
{
    [JsonProperty("types")]
    public List<string>? Types { get; set; }

    [JsonProperty("has_sleekflow_staff_id")]
    public bool? HasSleekflowStaffId { get; set; }

    [JsonConstructor]
    public UserProfileAuditLogsFilters(
        List<string>? types,
        bool? hasSleekflowStaffId)
    {
        Types = types;
        HasSleekflowStaffId = hasSleekflowStaffId;
    }
}