using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.JsonConfigs;
using Sleekflow.Locks;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Companies.CompanyConfigs;

public interface ICompanyConfigService
{
    Task<CompanyConfig> GetConfigAsync(string sleekflowCompanyId);

    Task ClearAsync(string sleekflowCompanyId);

    Task<CompanyConfig> PatchAndGetAsync(
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? name = null,
        string? backgroundInformation = null,
        string? preferredLanguage = null,
        string? collaborationMode = null);
}

public class CompanyConfigService : ICompanyConfigService, IScopedService
{
    private readonly ILogger _logger;
    private readonly IIdService _idService;
    private readonly ICompanyConfigRepository _companyConfigRepository;
    private readonly ILockService _lockService;

    public CompanyConfigService(
        IIdService idService,
        ILogger<CompanyConfigService> logger,
        ICompanyConfigRepository companyConfigRepository,
        ILockService lockService)
    {
        _logger = logger;
        _idService = idService;
        _companyConfigRepository = companyConfigRepository;
        _lockService = lockService;
    }

    public async Task<CompanyConfig> GetConfigAsync(string sleekflowCompanyId)
    {
        var companyConfigs =
            await _companyConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);
        if (companyConfigs.Count == 0)
        {
            var createdAt = DateTimeOffset.UtcNow;

            if (sleekflowCompanyId == "b6d7e442-38ae-4b9a-b100-2951729768bc")
            {
                var hkbnCompanyConfig = new CompanyConfig(
                    _idService.GetId(SysTypeNames.CompanyConfig),
                    sleekflowCompanyId,
                    "HKBN",
                    """
                    You are representing HKBN and you should name yourself `HKBN AI` when you are asked.
                    HKBN is a leading telecommunications company in Hong Kong.
                    The company offers a wide range of services, including broadband, fixed line, mobile services, and partner with different companies to provide personalized services.
                    The company is known for its high-quality customer service and innovative products.
                    This channel is used to provide customer service on WhatsApp Messaging and answer questions about third-party collaborations with HKBN including Bowtie, INDICAID, Evercare.
                    """,
                    "eng",
                    createdAt,
                    createdAt);

                _logger.LogInformation(
                    "Using the default company config {Config}",
                    JsonConvert.SerializeObject(hkbnCompanyConfig, JsonConfig.DefaultLoggingJsonSerializerSettings));

                return hkbnCompanyConfig;
            }

            var companyConfig = new CompanyConfig(
                _idService.GetId(SysTypeNames.CompanyConfig),
                sleekflowCompanyId,
                "Default Company",
                string.Empty,
                "eng",
                createdAt,
                createdAt);

            _logger.LogInformation(
                "Using the default company config {Config}",
                JsonConvert.SerializeObject(companyConfig, JsonConfig.DefaultLoggingJsonSerializerSettings));

            return companyConfig;
        }

        return companyConfigs.Single();
    }

    public async Task ClearAsync(string sleekflowCompanyId)
    {
        _logger.LogInformation("Deleting CompanyConfig with id {CompanyId}", sleekflowCompanyId);

        var companyConfigs =
            await _companyConfigRepository.GetObjectsAsync(cc => cc.SleekflowCompanyId == sleekflowCompanyId);
        if (companyConfigs.Count == 0)
        {
            return;
        }

        foreach (var companyConfig in companyConfigs)
        {
            await _companyConfigRepository.DeleteAsync(companyConfig.Id, sleekflowCompanyId, eTag: companyConfig.ETag);
        }
    }

    public async Task<CompanyConfig> PatchAndGetAsync(
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? name = null,
        string? backgroundInformation = null,
        string? preferredLanguage = null,
        string? collaborationMode = null)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            [
                nameof(CompanyConfigService),
                nameof(PatchAndGetAsync),
                sleekflowCompanyId
            ],
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));
        try
        {
            var companyConfigs =
                await _companyConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);

            if (companyConfigs.Count == 0)
            {
                var createdAt = DateTimeOffset.UtcNow;
                var staff = new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowTeamIds);

                var creatingCompanyConfig = new CompanyConfig(
                    _idService.GetId(SysTypeNames.CompanyConfig),
                    sleekflowCompanyId,
                    name ?? "Default Company",
                    backgroundInformation ?? string.Empty,
                    preferredLanguage ?? "eng",
                    createdAt,
                    createdAt,
                    staff,
                    staff);

                _logger.LogInformation(
                    "Creating company config {Config}",
                    JsonConvert.SerializeObject(
                        creatingCompanyConfig,
                        JsonConfig.DefaultLoggingJsonSerializerSettings));

                return await _companyConfigRepository.CreateAndGetAsync(creatingCompanyConfig, sleekflowCompanyId);
            }

            var companyConfig = companyConfigs.Single();

            _logger.LogInformation(
                "Patching company config with id {Id} {CompanyId} {Name} {BackgroundInformation} {PreferredLanguage}",
                companyConfig.Id,
                sleekflowCompanyId,
                name,
                backgroundInformation,
                preferredLanguage);

            return await _companyConfigRepository.PatchAndGetAsync(
                companyConfig.Id,
                sleekflowCompanyId,
                companyConfig.ETag,
                new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowTeamIds),
                name,
                backgroundInformation,
                preferredLanguage,
                collaborationMode);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }
}