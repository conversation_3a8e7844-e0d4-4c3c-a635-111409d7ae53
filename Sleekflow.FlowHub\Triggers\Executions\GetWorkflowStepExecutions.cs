using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Executions;

[TriggerGroup(ControllerNames.Executions)]
public class GetWorkflowStepExecutions : ITrigger
{
    private readonly IStepExecutionService _stepExecutionService;

    public GetWorkflowStepExecutions(
        IStepExecutionService stepExecutionService)
    {
        _stepExecutionService = stepExecutionService;
    }

    public class GetWorkflowStepExecutionsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("limit")]
        [Required]
        [Range(1, 1000)]
        public int Limit { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [JsonProperty("step_id")]
        [Required]
        public string StepId { get; set; }

        [JsonConstructor]
        public GetWorkflowStepExecutionsInput(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            string workflowId,
            string workflowVersionedId,
            string stepId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContinuationToken = continuationToken;
            Limit = limit;
            WorkflowId = workflowId;
            WorkflowVersionedId = workflowVersionedId;
            StepId = stepId;
        }
    }

    public class GetWorkflowStepExecutionsOutput
    {
        [JsonProperty("step_executions")]
        public List<StepExecutionDto> StepExecutions { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetWorkflowStepExecutionsOutput(List<StepExecutionDto> stepExecutions, string? nextContinuationToken)
        {
            StepExecutions = stepExecutions;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetWorkflowStepExecutionsOutput> F(GetWorkflowStepExecutionsInput getWorkflowStepExecutionsInput)
    {
        var (stepExecutions, nextContinuationToken) = await _stepExecutionService.GetWorkflowStepExecutionsAsync(
            getWorkflowStepExecutionsInput.SleekflowCompanyId,
            getWorkflowStepExecutionsInput.ContinuationToken,
            getWorkflowStepExecutionsInput.Limit,
            getWorkflowStepExecutionsInput.WorkflowId,
            getWorkflowStepExecutionsInput.WorkflowVersionedId,
            getWorkflowStepExecutionsInput.StepId);

        return new GetWorkflowStepExecutionsOutput(
            stepExecutions.Select(se => new StepExecutionDto(se)).ToList(),
            nextContinuationToken);
    }
}