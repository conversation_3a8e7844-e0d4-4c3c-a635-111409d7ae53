﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISearchGoogleSheetsRowStepExecutor : IStepExecutor
{
}

public class SearchGoogleSheetsRowStepExecutor
    : GeneralStepExecutor<CallStep<SearchGoogleSheetsRowStepArgs>>,
        ISearchGoogleSheetsRowStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public SearchGoogleSheetsRowStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var searchGoogleSheetsRowInput = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                 new SearchGoogleSheetsRowRequest(
                     step.Id,
                     state.Id,
                     stackEntries,
                     searchGoogleSheetsRowInput.StateIdentity.SleekflowCompanyId,
                     searchGoogleSheetsRowInput.ConnectionId,
                     searchGoogleSheetsRowInput.SpreadsheetId,
                     searchGoogleSheetsRowInput.WorksheetId,
                     searchGoogleSheetsRowInput.HeaderRowId,
                     searchGoogleSheetsRowInput.Conditions));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnGoogleSheetsFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("GoogleSheets search row operation timed out after 5 minutes")),
                typeof(OnGoogleSheetsFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class SearchGoogleSheetsRowInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("spreadsheet_id")]
        [Required]
        public string SpreadsheetId { get; set; }

        [JsonProperty("worksheet_id")]
        [Required]
        public string WorksheetId { get; set; }

        [JsonProperty("header_row_id")]
        [Required]
        public string HeaderRowId { get; set; }

        [JsonProperty("conditions")]
        [Required]
        [Validations.ValidateArray]
        public List<SearchObjectCondition> Conditions { get; set; }

        [JsonConstructor]
        public SearchGoogleSheetsRowInput(
            string stateId,
            StateIdentity stateIdentity,
            string connectionId,
            string spreadsheetId,
            string worksheetId,
            string headerRowId,
            List<SearchObjectCondition> conditions)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ConnectionId = connectionId;
            SpreadsheetId = spreadsheetId;
            WorksheetId = worksheetId;
            HeaderRowId = headerRowId;
            Conditions = conditions;
        }
    }

    private async Task<SearchGoogleSheetsRowInput> GetArgs(
        CallStep<SearchGoogleSheetsRowStepArgs> callStep,
        ProxyState state)
    {
        var connectionId = callStep.Args.ConnectionId;
        var spreadsheetId = callStep.Args.SpreadsheetId;
        var worksheetId = callStep.Args.WorksheetId;
        var headerRowId = callStep.Args.HeaderRowId;

        var convertedConditions = new List<SearchObjectCondition>();
        var conditions = callStep.Args.Conditions;
        foreach (var condition in conditions)
        {
            object? value = null;
            if (condition.ValueExpr != null)
            {
                value = await _stateEvaluator.EvaluateExpressionAsync(state, condition.ValueExpr);
            }

            convertedConditions.Add(new SearchObjectCondition(condition.FieldId, condition.SearchOperator, value));
        }

        return new SearchGoogleSheetsRowInput(
            state.Id,
            state.Identity,
            connectionId,
            spreadsheetId,
            worksheetId,
            headerRowId,
            convertedConditions);
    }
}