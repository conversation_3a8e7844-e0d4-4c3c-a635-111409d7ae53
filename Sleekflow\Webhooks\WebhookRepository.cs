﻿using Microsoft.Extensions.Logging;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Webhooks;

public interface IWebhookRepository : IRepository<Webhook>
{
}

public class WebhookRepository : BaseRepository<Webhook>, IWebhookRepository
{
    public WebhookRepository(
        ILogger<BaseRepository<Webhook>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}