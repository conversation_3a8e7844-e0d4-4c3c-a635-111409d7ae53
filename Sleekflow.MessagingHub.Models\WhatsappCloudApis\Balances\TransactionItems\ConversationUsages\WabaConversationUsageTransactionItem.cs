using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.ConversationUsages;

public class WabaConversationUsageTransactionItem : TransactionItem
{
    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty("start_timestamp")]
    public long StartTimestamp { get; set; }

    [JsonProperty("end_timestamp")]
    public long EndTimestamp { get; set; }

    [JsonProperty("granularity")]
    public string Granularity { get; set; } // HALF_HOUR, DAILY, MONTHLY

    [JsonProperty("business_initiated_paid_quantity")]
    public int BusinessInitiatedPaidQuantity { get; set; }

    [JsonProperty("business_initiated_free_tier_quantity")]
    public int BusinessInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("business_initiated_cost")]
    public decimal BusinessInitiatedCost { get; set; }

    [JsonProperty("user_initiated_paid_quantity")]
    public int UserInitiatedPaidQuantity { get; set; }

    [JsonProperty("user_initiated_free_tier_quantity")]
    public int UserInitiatedFreeTierQuantity { get; set; }

    [JsonProperty("user_initiated_free_entry_point_quantity")]
    public int UserInitiatedFreeEntryPointQuantity { get; set; }

    [JsonProperty("user_initiated_cost")]
    public decimal UserInitiatedCost { get; set; }

    [JsonProperty("conversation_analytics_data_points")]
    public List<WhatsappConversationAnalyticsResultDataPoint> ConversationAnalyticsDataPoints { get; set; }

    [JsonProperty("conversation_category_quantities")]
    public Dictionary<string, int>? ConversationCategoryQuantities { get; set; }

    [JsonProperty("conversation_category_costs")]
    public Dictionary<string, decimal>? ConversationCategoryCosts { get; set; }

    [JsonIgnore]
    public decimal TotalCost => BusinessInitiatedCost + UserInitiatedCost;

    [JsonConstructor]
    public WabaConversationUsageTransactionItem(
        string facebookWabaId,
        long startTimestamp,
        long endTimestamp,
        string granularity,
        int businessInitiatedPaidQuantity,
        int businessInitiatedFreeTierQuantity,
        decimal businessInitiatedCost,
        int userInitiatedPaidQuantity,
        int userInitiatedFreeTierQuantity,
        int userInitiatedFreeEntryPointQuantity,
        decimal userInitiatedCost,
        List<WhatsappConversationAnalyticsResultDataPoint> conversationAnalyticsDataPoints,
        Dictionary<string, int>? conversationCategoryQuantities,
        Dictionary<string, decimal>? conversationCategoryCosts)
    {
        FacebookWabaId = facebookWabaId;
        StartTimestamp = startTimestamp;
        EndTimestamp = endTimestamp;
        Granularity = granularity;
        BusinessInitiatedPaidQuantity = businessInitiatedPaidQuantity;
        BusinessInitiatedFreeTierQuantity = businessInitiatedFreeTierQuantity;
        BusinessInitiatedCost = businessInitiatedCost;
        UserInitiatedPaidQuantity = userInitiatedPaidQuantity;
        UserInitiatedFreeTierQuantity = userInitiatedFreeTierQuantity;
        UserInitiatedFreeEntryPointQuantity = userInitiatedFreeEntryPointQuantity;
        UserInitiatedCost = userInitiatedCost;
        ConversationAnalyticsDataPoints = conversationAnalyticsDataPoints;
        ConversationCategoryQuantities = conversationCategoryQuantities;
        ConversationCategoryCosts = conversationCategoryCosts;
    }

    [JsonIgnore]
    public override string GetUniqueId => $"{FacebookWabaId}/{StartTimestamp}/{EndTimestamp}";
}