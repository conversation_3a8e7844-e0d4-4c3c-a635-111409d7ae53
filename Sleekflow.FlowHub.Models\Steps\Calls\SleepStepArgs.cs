using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SleepStepArgs : TypedCallStepArgs
{
    public const string CallName = "sys.sleep";

    [Required]
    [JsonProperty("seconds__expr")]
    public string SecondsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => string.Empty;

    [JsonConstructor]
    public SleepStepArgs(string secondsExpr)
    {
        SecondsExpr = secondsExpr;
    }
}