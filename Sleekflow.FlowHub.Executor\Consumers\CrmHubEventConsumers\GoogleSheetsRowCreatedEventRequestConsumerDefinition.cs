﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class GoogleSheetsRowCreatedEventRequestConsumerDefinition : ConsumerDefinition<GoogleSheetsRowCreatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GoogleSheetsRowCreatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GoogleSheetsRowCreatedEventRequestConsumer : IConsumer<GoogleSheetsRowCreatedEventRequest>
{
    private readonly IBus _bus;

    public GoogleSheetsRowCreatedEventRequestConsumer(
        IBus bus)
    {
        _bus = bus;
    }

    public async Task Consume(ConsumeContext<GoogleSheetsRowCreatedEventRequest> context)
    {
        var googleSheetsRowCreatedEventRequest = context.Message;

        await _bus.Publish(new OnTriggerEventRequestedEvent(
            new OnGoogleSheetsRowCreatedEventBody(
                googleSheetsRowCreatedEventRequest.CreatedAt,
                googleSheetsRowCreatedEventRequest.ConnectionId,
                googleSheetsRowCreatedEventRequest.SpreadsheetId,
                googleSheetsRowCreatedEventRequest.WorksheetId,
                googleSheetsRowCreatedEventRequest.RowId,
                googleSheetsRowCreatedEventRequest.Row),
            googleSheetsRowCreatedEventRequest.RowId,
            "Row",
            googleSheetsRowCreatedEventRequest.SleekflowCompanyId));
    }
}