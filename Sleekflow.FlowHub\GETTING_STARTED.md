# Getting Started with FlowHub

## Overview

For developers working with FlowHub, start by understanding:
1. Workflow definitions and structure
2. Step implementation and custom step development
3. Trigger configurations and event handling
4. State management and execution flow

## Implementing Custom Step Executors

The FlowHub system allows you to extend functionality by creating custom step executors. Step executors are responsible for executing specific types of workflow steps. The following guide demonstrates how to implement a custom step executor based on the `HttpGetStepExecutor` example.

### 1. Define Step Arguments

First, create a class that defines the arguments for your step:

```csharp
public class MyCustomStepArgs
{
    // Required parameters
    [Required]
    public string SomeRequiredParameter { get; set; }

    // Expression properties - these will be evaluated at runtime
    // Can contain template expressions like "{{ state.someValue }}"
    public string ExpressionProperty { get; set; }

    // Dictionary expressions for dynamic key-value pairs
    public Dictionary<string, string> KeyExprDict { get; set; }

    // Optional parameters
    public string? OptionalParameter { get; set; }
}
```

### 2. Create the Step Executor Interface

Define an interface for your step executor:

```csharp
public interface IMyCustomStepExecutor : IStepExecutor
{
    // You can add specific methods here if needed
}
```

### 3. Implement the Step Executor

Create a class that inherits from `GeneralStepExecutor<CallStep<TArgs>>` and implements your interface:

```csharp
public class MyCustomStepExecutor
    : GeneralStepExecutor<CallStep<MyCustomStepArgs>>, IMyCustomStepExecutor, IScopedService
{
    // Dependency injection for required services
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IStateAggregator _stateAggregator;
    // Add other dependencies as needed

    public MyCustomStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IStateAggregator stateAggregator)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _stateAggregator = stateAggregator;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        // Convert the generic Step to your concrete step type
        var callStep = ToConcreteStep(step);

        try
        {
            // 1. Evaluate expressions in step arguments
            var evaluatedValue = (string)(await _stateEvaluator.EvaluateExpressionAsync(
                state,
                callStep.Args.ExpressionProperty) ?? callStep.Args.ExpressionProperty);

            // For dictionary expressions
            var evaluatedDict = callStep.Args.KeyExprDict == null
                ? new Dictionary<string, object?>()
                : await _stateEvaluator.EvaluateDictExpressionAsync(
                    state,
                    callStep.Args.KeyExprDict);

            // 2. Perform your custom step logic here
            // ...

            // 3. Update the workflow state with results
            var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                "result data"); // Replace with your result

            // 4. Mark step as complete and continue workflow execution
            await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            // Error handling with user-friendly exceptions
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }
}
```

### 4. Understanding Key Components

#### Expression Evaluation

The `IStateEvaluator` service allows you to evaluate expressions embedded in the step arguments:

* `EvaluateExpressionAsync`: Evaluates a single expression string
* `EvaluateDictExpressionAsync`: Evaluates expressions in a dictionary of keys and values

Expressions can refer to values in the workflow state using syntax like `{{ state.someValue }}`.

#### State Management

The `IStateAggregator` service is used to update the workflow state with the results of the step:

* `AggregateStateStepBodyAsync`: Updates state with the step's result data

#### Error Handling

Use `SfFlowHubUserFriendlyException` to provide clear error messages that can be displayed to users.

### 5. Registration in Dependency Injection

Ensure your executor is registered with the dependency injection system:

* The `IScopedService` interface marks the service for automatic registration with scoped lifetime
* If using manual registration, add to the service collection in `Program.cs`:

```csharp
services.AddScoped<IMyCustomStepExecutor, MyCustomStepExecutor>();
```

### 6. Complete Implementation Process

1. Define your step arguments class
2. Implement the step executor and interface
3. Register with dependency injection
4. Add any necessary validation logic
5. Test with workflows that use your custom step

### 7. Step Execution Lifecycle

When your step is executed:

1. The workflow engine requests the step to be executed
2. `StepExecutorMatcher` identifies your executor as matching the step type
3. `StepExecutorActivator` creates and activates an instance of your executor
4. Your `OnStepActivateAsync` method executes
5. Expression evaluation and business logic occur
6. State is updated with the result
7. `onActivatedAsync` callback is called to complete the step
8. Workflow continues to the next step (or completes)

## Example: HTTP GET Step Implementation

The `HttpGetStepExecutor` implements a step that makes HTTP GET requests:

```csharp
// Step Arguments
public class HttpGetStepArgs
{
    // URL with possible expression syntax: "https://api.example.com/{{ state.resourceId }}"
    public string UrlExpr { get; set; }

    // Headers as key-value pairs, can contain expressions
    public Dictionary<string, string>? HeadersKeyExprDict { get; set; }
}

// Step Executor (simplified)
public class HttpGetStepExecutor : GeneralStepExecutor<CallStep<HttpGetStepArgs>>
{
    // Implementation resolves expressions, makes HTTP requests, and updates state
    public override async Task OnStepActivateAsync(/* parameters */)
    {
        // 1. Resolve URL and header expressions
        // 2. Make HTTP request
        // 3. Process response
        // 4. Update state with response content
        // 5. Complete step
    }
}
```

## Registering a New Step Type

After creating your custom step executor, you need to register it in the FlowHub system for proper deserialization and matching. This involves modifications to two key files:

### 1. Update StepConverter.cs

The `StepConverter` handles JSON deserialization for all step types. You need to add your step type to the converter:

```csharp
// In StepConverter.cs, locate the ReadJson method's switch statement
switch (callStr)
{
    // ... existing step types ...

    case MyCustomStepArgs.CallName:
        return JsonConvert.DeserializeObject<CallStep<MyCustomStepArgs>>(
            jo.ToString(),
            ConcreteClassConversion)!;
}
```

Make sure your step arguments class defines a static `CallName` constant:

```csharp
public class MyCustomStepArgs
{
    public const string CallName = "my-company.v1.custom-step";

    // ... properties ...
}
```

### 2. Update StepExecutorMatcher.cs

The `StepExecutorMatcher` is responsible for locating the right executor for each step. You need to:

1. Define an interface for your executor:

```csharp
public interface IMyCustomStepExecutor : IStepExecutor
{
}
```

2. Add your executor to the StepExecutorMatcher constructor parameters:

```csharp
public StepExecutorMatcher(
    // ... existing parameters ...
    IMyCustomStepExecutor myCustomStepExecutor)
{
    _stepExecutors = new List<IStepExecutor>
    {
        // ... existing executors ...
        myCustomStepExecutor
    };
}
```

### 3. Complete Registration

With these changes, your new step type will be:
1. Properly deserialized from JSON in workflow definitions
2. Matched to your custom executor when encountered during workflow execution

This registration process is necessary for any new step type to be recognized by the FlowHub workflow engine.