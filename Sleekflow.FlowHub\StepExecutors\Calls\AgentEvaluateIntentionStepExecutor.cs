﻿using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Internals.ChatHistory;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Chats;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IAgentEvaluateIntentionStepExecutor : IStepExecutor
{
}

public class AgentEvaluateIntentionStepExecutor
    : GeneralStepExecutor<CallStep<AgentEvaluateIntentionStepExecutorStepArgs>>,
        IAgentEvaluateIntentionStepExecutor,
        IScopedService
{
    private readonly IServiceBusManager _serviceBusManager;
    private readonly ILogger _logger;
    private readonly ICoreCommander _coreCommander;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IAgentConfigService _agentConfigService;
    private readonly IStateService _stateService;
    private readonly IChatHistoryService _chatHistoryService;

    public AgentEvaluateIntentionStepExecutor(
        IServiceBusManager serviceBusManager,
        IServiceProvider serviceProvider,
        ICoreCommander coreCommander,
        IStateEvaluator stateEvaluator,
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        ILogger<AgentEvaluateIntentionStepExecutorStepArgs> logger,
        IAgentConfigService agentConfigService,
        IStateService stateService,
        IChatHistoryService chatHistoryService)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _serviceBusManager = serviceBusManager;
        _logger = logger;
        _coreCommander = coreCommander;
        _stateEvaluator = stateEvaluator;
        _agentConfigService = agentConfigService;
        _stateService = stateService;
        _chatHistoryService = chatHistoryService;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        try
        {
            var callStep = ToConcreteStep(step);
            var config = await _agentConfigService.GetAsync(state, callStep.Args.CompanyAgentConfigIdExpr);
            var contactId = await _stateEvaluator.EvaluateExpressionAsync<string>(state, callStep.Args.ContactIdExpr);
            var runningStates = await _stateService.GetRunningStatesAsync(
                                       state.Identity.ObjectId,
                                       state.Identity.ObjectType,
                                       state.Identity.SleekflowCompanyId,
                                       state.TriggerEventBody);
            var channelId = StateUtils
                    .GetChannelIdFromParentState(runningStates, state.WorkflowContext.SnapshottedWorkflow.WorkflowId, _logger);

            // Obtain the conversation information from SleekflowCore
            var conversationMessages = await _chatHistoryService.GetConversationMessagesAsync(state, contactId, config, callStep, channelId);

            await _serviceBusManager.PublishAsync(
                new GetAgentHandoffTeamEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    state.Identity.SleekflowCompanyId,
                    conversationMessages,
                    callStep.Args.TeamCategories));
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error occured on agent intention evaluation {StepId} {SleekflowCompanyId}",
                step.Id,
                state.Identity.SleekflowCompanyId);
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

}
