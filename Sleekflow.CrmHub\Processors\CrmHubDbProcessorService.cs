﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Processors.ChangeFeedHandlers.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Processors;

public interface ICosmosProcessorService
{
}

public class CrmHubDbProcessorService : BackgroundService, ICosmosProcessorService
{
    private readonly ILogger<CrmHubDbProcessorService> _logger;
    private readonly ICrmHubDbResolver _crmHubDbResolver;
    private readonly ICrmHubDbProcessorConfig _crmHubDbProcessorConfig;
    private readonly List<IChangeFeedHandler> _changeFeedHandlers;

    private readonly List<ChangeFeedProcessor> _changeFeedProcessors = new List<ChangeFeedProcessor>();

    public CrmHubDbProcessorService(
        ILogger<CrmHubDbProcessorService> logger,
        ICrmHubDbResolver crmHubDbResolver,
        ICrmHubDbProcessorConfig crmHubDbProcessorConfig,
        List<IChangeFeedHandler> changeFeedHandlers)
    {
        _logger = logger;
        _crmHubDbResolver = crmHubDbResolver;
        _crmHubDbProcessorConfig = crmHubDbProcessorConfig;
        _changeFeedHandlers = changeFeedHandlers;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        foreach (var changeFeedHandler in _changeFeedHandlers)
        {
            _logger.LogInformation(
                "Starting Change Feed Processor for changeFeedHandler.GetEntityTypeName {EntityTypeName}",
                changeFeedHandler.GetContainerName());

            var containerName = changeFeedHandler.GetContainerName();
            var processorName =
                $"{containerName.ToLower()}-change-feed-processor-{_crmHubDbProcessorConfig.CosmosChangeFeedEnvId}";

            var changeFeedProcessor = _crmHubDbResolver.Resolve("crmhubdb", containerName)
                .GetChangeFeedProcessorBuilder<CrmHubEntity>(
                    processorName: processorName,
                    onChangesDelegate: changeFeedHandler.OnChangesDelegate)
                .WithMaxItems(100)
                .WithInstanceName(Environment.MachineName)
                .WithLeaseContainer(_crmHubDbResolver.Resolve("crmhubdb", "sys_changefeed_lease"))
                .Build();

            try
            {
                await changeFeedProcessor.StartAsync();

                _logger.LogInformation(
                    "Started Change Feed Processor for changeFeedHandler.GetTableName {TableName}",
                    changeFeedHandler.GetContainerName());
            }
            catch (TaskCanceledException e)
            {
                // This is expected if the cancellation token is
                // signaled.
                _logger.LogError(e, "There is an exception thrown");
            }

            _changeFeedProcessors.Add(changeFeedProcessor);
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        foreach (var processor in _changeFeedProcessors)
        {
            try
            {
                await processor.StopAsync();
            }
            catch
            {
                // ignored
            }
        }
    }
}