using System.Text;
using System.Text.RegularExpressions;
using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Microsoft.Azure.Cosmos;
using Sleekflow.IntelligentHub.FaqAgents.ChatContexts;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Configs;
using Sleekflow.IntelligentHub.Models.FaqAgents.ChatContexts;
using Sleekflow.IntelligentHub.Models.HeadlessWhatsappIntegrations;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.IntelligentHub.Tokens;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.HeadlessWhatsappIntegrations;

public class OnHeadlessWhatsappMessageReceivedEventConsumerDefinition
    : ConsumerDefinition<OnHeadlessWhatsappMessageReceivedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnHeadlessWhatsappMessageReceivedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 40;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 40;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(15);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public partial class OnHeadlessWhatsappMessageReceivedEventConsumer : IConsumer<OnHeadlessWhatsappMessageReceivedEvent>
{
    private const string UndetectedLanguageFallbackReply =
        "I can’t recognize the language you are using. Try asking again in a different language.\n";

    private readonly IChatContextHistoryRepository _chatContextHistoryRepository;
    private readonly IChatService _chatService;
    private readonly IHeadlessWhatsappMessageIntegrationService _headlessWhatsappMessageIntegrationService;
    private readonly IFaqAgentConfigService _faqAgentConfigService;
    private readonly ITokenService _tokenService;
    private readonly ITextTranslationService _textTranslationService;

    public OnHeadlessWhatsappMessageReceivedEventConsumer(
        IChatContextHistoryRepository messagingHistoryRepository,
        IChatService chatService,
        IHeadlessWhatsappMessageIntegrationService headlessWhatsappMessagingService,
        IFaqAgentConfigService faqAgentConfigService,
        ITokenService tokenService,
        ITextTranslationService textTranslationService)
    {
        _chatContextHistoryRepository = messagingHistoryRepository;
        _chatService = chatService;
        _headlessWhatsappMessageIntegrationService = headlessWhatsappMessagingService;
        _faqAgentConfigService = faqAgentConfigService;
        _tokenService = tokenService;
        _textTranslationService = textTranslationService;
    }

    public async Task Consume(ConsumeContext<OnHeadlessWhatsappMessageReceivedEvent> context)
    {
        var onWhatsappMessageReceivedEvent = context.Message;
        var cancellationToken = context.CancellationToken;

        var userPhoneNumber = onWhatsappMessageReceivedEvent.PhoneNumber;
        var contextId = onWhatsappMessageReceivedEvent.ContextId;
        var sleekflowCompanyId = onWhatsappMessageReceivedEvent.SleekflowCompanyId;
        var userMessage = onWhatsappMessageReceivedEvent.User;
        var userMessageSentTime = onWhatsappMessageReceivedEvent.SentTime;

        // User message tracking moved to GroupChatService for universal channel support

        await ReplyAsync(
            userPhoneNumber,
            sleekflowCompanyId,
            contextId,
            cancellationToken,
            userMessage,
            userMessageSentTime);
    }

    private string Sanitize(string str, string sleekflowCompanyId)
    {
        str = MarkdownSyntaxRegex().Replace(str, "*$1*");
        var agentConfig = _faqAgentConfigService.GetAgentConfigOrDefault(sleekflowCompanyId)!;
        if (agentConfig.ShouldShowCitation)
        {
            return str;
        }

        return CitationRegex().Replace(str, string.Empty);
    }

    public async Task ReplyAsync(
        string userPhoneNumber,
        string sleekflowCompanyId,
        string contextId,
        CancellationToken cancellationToken,
        string userMessage,
        DateTimeOffset userMessageSentTime)
    {
        var detectedLanguageCode = await _textTranslationService.DetectByLlmAsync(userMessage);

        var queryDefinition = new QueryDefinition(
                "SELECT * " +
                "FROM %%CONTAINER_NAME%% c " +
                "WHERE c.phone_number = @phone_number AND c.sleekflow_company_id = @sleekflow_company_id AND c.context_id = @context_id " +
                "ORDER BY c._ts")
            .WithParameter("@phone_number", userPhoneNumber)
            .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
            .WithParameter("@context_id", contextId);

        var messageProcessingHistories = await _chatContextHistoryRepository.GetObjectsAsync(
            queryDefinition,
            cancellationToken: cancellationToken);

        var sfChatEntries = messageProcessingHistories
            .Select(
                x => new SfChatEntry
                {
                    User = x.User, Bot = x.Bot,
                })
            .Append(
                new SfChatEntry
                {
                    User = userMessage
                })
            .ToList();

        if (string.IsNullOrEmpty(detectedLanguageCode))
        {
            var messageId = Guid.NewGuid().ToString();
            await _headlessWhatsappMessageIntegrationService.SendMessageAsync(
                userPhoneNumber,
                Sanitize(UndetectedLanguageFallbackReply, sleekflowCompanyId),
                sleekflowCompanyId);

            // No need to track system messages in simplified approach

            await _chatContextHistoryRepository.UpsertAsync(
                new ChatContextHistory(
                    messageId,
                    sleekflowCompanyId,
                    contextId,
                    userPhoneNumber,
                    userMessage,
                    UndetectedLanguageFallbackReply,
                    userMessageSentTime,
                    0,
                    0),
                sleekflowCompanyId,
                cancellationToken: cancellationToken);
        }
        else
        {
            var answerSb = new StringBuilder();
            var chunkSb = new StringBuilder();

            var (asyncEnumerable, promptNumOfTokens, _) = await _chatService.StreamAnswerAsync(
                sfChatEntries,
                sleekflowCompanyId);

            await foreach (var partialAnswer in asyncEnumerable.WithCancellation(cancellationToken))
            {
                if (partialAnswer is null)
                {
                    continue;
                }

                chunkSb.Append(partialAnswer);
                answerSb.Append(partialAnswer);

                if (chunkSb.ToString() is { } chunkStr && chunkStr.Contains('\n'))
                {
                    await _headlessWhatsappMessageIntegrationService.SendMessageAsync(
                        userPhoneNumber,
                        Sanitize(chunkStr, sleekflowCompanyId),
                        sleekflowCompanyId);
                    chunkSb.Clear();
                }
            }

            if (chunkSb.Length > 0)
            {
                await _headlessWhatsappMessageIntegrationService.SendMessageAsync(
                    userPhoneNumber,
                    Sanitize(chunkSb.ToString(), sleekflowCompanyId),
                    sleekflowCompanyId);
                chunkSb.Clear();
            }

            var answer = answerSb.ToString();
            var answerNumOfTokens = _tokenService.Count(answer);

            var messageId = Guid.NewGuid().ToString();

            // No need to track system messages in simplified approach

            await _chatContextHistoryRepository.UpsertAsync(
                new ChatContextHistory(
                    messageId,
                    sleekflowCompanyId,
                    contextId,
                    userPhoneNumber,
                    userMessage,
                    answer,
                    userMessageSentTime,
                    promptNumOfTokens,
                    answerNumOfTokens),
                sleekflowCompanyId,
                cancellationToken: cancellationToken);
        }
    }

    [GeneratedRegex(@"\[.*?\.(pdf)\]")]
    private static partial Regex CitationRegex();

    [GeneratedRegex(@"\*\*(.*?)\*\*")]
    private static partial Regex MarkdownSyntaxRegex();
}