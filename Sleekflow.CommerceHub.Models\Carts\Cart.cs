using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Carts;

[ContainerId(ContainerNames.Cart)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class Cart : AuditEntity, IHasSleekflowUserProfileId, IHasRecordStatuses, IHasETag
{
    public const string PropertyNameLineItems = "line_items";
    public const string PropertyNameCartDiscount = "cart_discount";
    public const string PropertyNameCartStatus = "cart_status";
    public const string PropertyNameCurrencyIsoCode = "currency_iso_code";
    public const string PropertyNameCartExternalIntegrationInfo = "cart_external_integration_info";

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty(PropertyNameLineItems)]
    public List<CartLineItem> LineItems { get; set; }

    [JsonProperty(PropertyNameCartDiscount)]
    public Discount? CartDiscount { get; set; }

    [JsonProperty(PropertyNameCartStatus)]
    public string CartStatus { get; set; }

    [JsonProperty(PropertyNameCurrencyIsoCode)]
    public string? CurrencyIsoCode { get; set; }

    [JsonProperty(PropertyNameCartExternalIntegrationInfo)]
    public CartExternalIntegrationInfo? CartExternalIntegrationInfo { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public Cart(
        string storeId,
        string sleekflowUserProfileId,
        List<CartLineItem> lineItems,
        Discount? cartDiscount,
        string cartStatus,
        string? currencyIsoCode,
        CartExternalIntegrationInfo? cartExternalIntegrationInfo,
        List<string> recordStatuses,
        string? eTag,
        string id,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(
            id,
            SysTypeNames.Cart,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        StoreId = storeId;
        SleekflowUserProfileId = sleekflowUserProfileId;
        LineItems = lineItems;
        CartDiscount = cartDiscount;
        CartStatus = cartStatus;
        CurrencyIsoCode = currencyIsoCode;
        CartExternalIntegrationInfo = cartExternalIntegrationInfo;
        RecordStatuses = recordStatuses;
        ETag = eTag;
    }
}