using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

public class WabaBalance : Entity, IHasCreatedAt, IHasUpdatedAt, IHasETag
{
    private const string PropertyNameCredit = "credit";
    private const string PropertyNameUsed = "used";
    private const string PropertyNameMarkup = "markup";

    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty(PropertyNameCredit)]
    public Money Credit { get; set; }

    [JsonProperty(PropertyNameUsed)]
    public Money Used { get; set; }

    [JsonProperty(PropertyNameMarkup)]
    public Money Markup { get; set; }

    [JsonProperty("transaction_handling_fee")]
    public Money? TransactionHandlingFee { get; set; }

    [JsonIgnore]
    public Money AllTimeUsage => MoneyExtensions.AddAll(
        Used,
        Markup,
        TransactionHandlingFee);

    [JsonIgnore]
    public Money Balance => MoneyExtensions.Minus(Credit, AllTimeUsage);

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public WabaBalance(
        string id,
        string facebookWabaId,
        Money credit,
        Money used,
        Money markup,
        Money? transactionHandlingFee,
        bool isByWabaBillingEnabled,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, Constants.SysTypeNames.WabaBalance)
    {
        FacebookWabaId = facebookWabaId;
        Credit = credit;
        Used = used;
        Markup = markup;
        TransactionHandlingFee = transactionHandlingFee;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public WabaBalance(
        string id,
        string facebookWabaId,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<BusinessBalanceTransactionLog>? historyTransactionLogs = null)
        : this(
            id,
            facebookWabaId,
            new Money(Currencies.Usd, 0),
            new Money(Currencies.Usd, 0),
            new Money(Currencies.Usd, 0),
            new Money(Currencies.Usd, 0),
            true,
            createdAt,
            updatedAt)
    {
        if (historyTransactionLogs is { Count: > 0 })
        {
            Used = MoneyExtensions.AddAll(historyTransactionLogs.Select(x => x.Used).ToArray());
            Markup = MoneyExtensions.AddAll(historyTransactionLogs.Select(x => x.Markup).ToArray());
            TransactionHandlingFee = MoneyExtensions.AddAll(historyTransactionLogs.Select(x => x.TransactionHandlingFee).ToArray());

            // There are no logs for adding credit for waba before init, so it is not necessary to calculate credit.
            // Instead, we need to ensure the amount of waba balance will not be < 0 when init.
            Credit = AllTimeUsage;
        }
    }
}