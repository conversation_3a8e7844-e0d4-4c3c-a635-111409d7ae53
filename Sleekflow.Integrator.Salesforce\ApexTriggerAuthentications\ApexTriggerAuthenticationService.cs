using Sleekflow.Caches;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Integrator.Salesforce.ApexTriggerAuthentications;

public interface IApexTriggerAuthenticationService
{
    Task UpsertAsync(SalesforceApexTriggerAuthentication authentication);

    Task<List<SalesforceApexTriggerAuthentication>> GetObjectsAsync(string sleekflowCompanyId, string sObjectTypeName);

    Task<SalesforceApexTriggerAuthentication?> GetAsync(string sleekflowCompanyId, string sObjectTypeName, string key);

    Task ClearAsync(string sleekflowCompanyId, string sObjectTypeName);
}

public class ApexTriggerAuthenticationService : ISingletonService, IApexTriggerAuthenticationService
{
    private readonly IApexTriggerAuthenticationRepository _apexTriggerAuthenticationRepository;
    private readonly ICacheService _cacheService;

    public ApexTriggerAuthenticationService(
        IApexTriggerAuthenticationRepository apexTriggerAuthenticationRepository,
        ICacheService cacheService)
    {
        _apexTriggerAuthenticationRepository = apexTriggerAuthenticationRepository;
        _cacheService = cacheService;
    }

    public async Task UpsertAsync(SalesforceApexTriggerAuthentication authentication)
    {
        var upsertCount = await _apexTriggerAuthenticationRepository.UpsertAsync(
            authentication,
            authentication.SleekflowCompanyId);
        if (upsertCount == 0)
        {
            throw new SfInternalErrorException("Unable to create ApexTriggerAuthentication");
        }
    }

    public Task<List<SalesforceApexTriggerAuthentication>> GetObjectsAsync(
        string sleekflowCompanyId,
        string sObjectTypeName)
    {
        return _cacheService.CacheAsync(
            GetCacheKey(sleekflowCompanyId, sObjectTypeName),
            async () =>
            {
                return await _apexTriggerAuthenticationRepository.GetObjectsAsync(
                    a =>
                        a.SleekflowCompanyId == sleekflowCompanyId
                        && a.SObjectTypeName == sObjectTypeName);
            });
    }

    public async Task<SalesforceApexTriggerAuthentication?> GetAsync(
        string sleekflowCompanyId,
        string sObjectTypeName,
        string key)
    {
        var apexTriggerAuthentications = await _cacheService.CacheAsync(
            GetCacheKey(sleekflowCompanyId, sObjectTypeName),
            async () =>
            {
                return await _apexTriggerAuthenticationRepository.GetObjectsAsync(
                    a =>
                        a.SleekflowCompanyId == sleekflowCompanyId
                        && a.SObjectTypeName == sObjectTypeName);
            });

        return apexTriggerAuthentications.Find(a => a.Key == key);
    }

    public async Task ClearAsync(string sleekflowCompanyId, string sObjectTypeName)
    {
        var asyncEnumerable = _apexTriggerAuthenticationRepository.GetObjectEnumerableAsync(
            a =>
                a.SleekflowCompanyId == sleekflowCompanyId
                && a.SObjectTypeName == sObjectTypeName);
        await foreach (var apexTriggerAuthentication in asyncEnumerable)
        {
            await _apexTriggerAuthenticationRepository.DeleteAsync(
                apexTriggerAuthentication.Id,
                apexTriggerAuthentication.SleekflowCompanyId);
        }

        await _cacheService.RemoveCacheAsync(
            GetCacheKey(sleekflowCompanyId, sObjectTypeName));
    }

    private string GetCacheKey(string sleekflowCompanyId, string sObjectTypeName)
    {
        return $"{nameof(ApexTriggerAuthenticationService)}-{sleekflowCompanyId}-{sObjectTypeName}";
    }
}