﻿using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.CommerceHub.Models.Workers;

[SwaggerInclude]
public class VtexGetOrdersSearchCondition
{
    [JsonProperty("created_at_from")]
    public DateTimeOffset CreatedAtFrom { get; set; }

    [JsonProperty("created_at_to")]
    public DateTimeOffset CreatedAtTo { get; set; }

    [JsonProperty("order_status_code")]
    public string? OrderStatusCode { get; set; }

    [JsonConstructor]
    public VtexGetOrdersSearchCondition(
        DateTimeOffset createdAtFrom,
        DateTimeOffset createdAtTo,
        string? orderStatusCode)
    {
        CreatedAtFrom = createdAtFrom;
        CreatedAtTo = createdAtTo;
        OrderStatusCode = orderStatusCode;
    }
}