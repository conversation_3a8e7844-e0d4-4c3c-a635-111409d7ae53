using System.Security.Cryptography;
using System.Text;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;

namespace Sleekflow.Integrator.Salesforce.Services;

public interface ISalesforceRemoteSiteSettingService
{
    Task CreateAsync(SalesforceAuthentication authentication, string url);
}

public class SalesforceRemoteSiteSettingService : ISingletonService, ISalesforceRemoteSiteSettingService
{
    private readonly HttpClient _httpClient;

    public SalesforceRemoteSiteSettingService(
        IHttpClientFactory httpClientFactory)
    {
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task CreateAsync(SalesforceAuthentication authentication, string url)
    {
        var c = @"
<env:Envelope xmlns:env=""http://schemas.xmlsoap.org/soap/envelope/""
              xmlns:xsi=""http://www.w3.org/2001/XMLSchema-instance"">
    <env:Header>
        <urn:SessionHeader xmlns:urn=""http://soap.sforce.com/2006/04/metadata"">
            <urn:sessionId>{{PARAM_ACCESS_TOKEN}}</urn:sessionId>
        </urn:SessionHeader>
    </env:Header>
    <env:Body>
        <createMetadata xmlns=""http://soap.sforce.com/2006/04/metadata"">
            <metadata xsi:type=""RemoteSiteSetting"">
                <fullName>{{PARAM_NAME}}</fullName>
                <isActive>true</isActive>
                <url>{{PARAM_URL}}</url>
            </metadata>
        </createMetadata>
    </env:Body>
</env:Envelope>
"
            .Replace("{{PARAM_ACCESS_TOKEN}}", authentication.AccessToken)
            .Replace("{{PARAM_NAME}}", "SF_" + GetMd5Hash(url))
            .Replace("{{PARAM_URL}}", "https://" + new Uri(url).Host);

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(
                c,
                Encoding.UTF8,
                "text/xml"),
            RequestUri = new Uri(authentication.InstanceUrl + "/services/Soap/m/54.0/")
        };
        requestMessage.Headers.Add("SOAPAction", "RemoteSiteSetting");

        var httpResponseMsg = await _httpClient.SendAsync(requestMessage);
        var str = await httpResponseMsg.Content.ReadAsStringAsync();
        if (!httpResponseMsg.IsSuccessStatusCode || str.Contains("<success>false</success>"))
        {
            if (str.Contains("This Remote Site Name already exists or has been previously used."))
            {
                // Skip the error response.
                return;
            }

            throw new Exception(
                $"The httpResponseMsg {httpResponseMsg}, str {str} is not working");
        }
    }

    public static string GetMd5Hash(string str)
    {
        using var md5 = MD5.Create();
        var bytes = Encoding.Default.GetBytes(str);
        var encoded = md5.ComputeHash(bytes);

        var sb = new StringBuilder();
        foreach (var t in encoded)
        {
            sb.Append(t.ToString("x2"));
        }

        return sb.ToString();
    }
}