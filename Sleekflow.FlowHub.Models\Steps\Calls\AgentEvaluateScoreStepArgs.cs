using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class AgentEvaluateScoreStepArgs : TypedCallStepArgs, IHasCompanyAgentConfigIdExpr
{
    public const string CallName = "sleekflow.v1.agent-evaluate-score";

    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty(IHasCompanyAgentConfigIdExpr.PropertyNameCompanyAgentConfigIdExpr)]
    public string? CompanyAgentConfigIdExpr { get; set; }

    [JsonProperty("additional_prompts__expr")]
    public string AdditionalPromptsExpr { get; set; }

    [JsonProperty("retrieval_window_timestamp__expr")]
    public string? RetrievalWindowTimestampExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonConstructor]
    public AgentEvaluateScoreStepArgs(
        string contactIdExpr,
        string? companyAgentConfigIdExpr,
        string additionalPromptsExpr,
        string? retrievalWindowTimestampExpr = null)
    {
        ContactIdExpr = contactIdExpr;
        CompanyAgentConfigIdExpr = companyAgentConfigIdExpr;
        AdditionalPromptsExpr = additionalPromptsExpr;
        RetrievalWindowTimestampExpr = retrievalWindowTimestampExpr;
    }
}