﻿using Microsoft.Extensions.Logging;
using Serilog;
using Sleekflow.Caches;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.RateLimits.LuaScripts;
using StackExchange.Redis;

namespace Sleekflow.RateLimits.RateLimiters;

public interface IRateLimiterFactory
{
    IRateLimiter CreateRateLimiter(string rateLimitAlgorithm);
}

public class RateLimiterFactory : ISingletonService, IRateLimiterFactory
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILuaScriptRepositoryService _luaScriptRepositoryService;
    private readonly ICacheConfig _cacheConfig;


    public RateLimiterFactory(
        IConnectionMultiplexer connectionMultiplexer,
        ILuaScriptRepositoryService luaScriptRepositoryService,
        ICacheConfig cacheConfig)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _luaScriptRepositoryService = luaScriptRepositoryService;
        _cacheConfig = cacheConfig;
    }

    public IRateLimiter CreateRateLimiter(string rateLimitAlgorithm)
    {
        var loggerFactory = new LoggerFactory().AddSerilog(Log.Logger);
        switch (rateLimitAlgorithm)
        {
            case RateLimitAlgorithms.SlidingWindowAllowance:
                return new SlidingWindowAllowanceRateLimiter(
                    _connectionMultiplexer,
                    _luaScriptRepositoryService,
                    loggerFactory.CreateLogger<SlidingWindowAllowanceRateLimiter>());
            case RateLimitAlgorithms.SlidingWindowNextAvailableSlot:
                return new SlidingWindowNextAvailableSlotRateLimiter(
                    _connectionMultiplexer,
                    _luaScriptRepositoryService,
                    loggerFactory.CreateLogger<SlidingWindowNextAvailableSlotRateLimiter>(),
                    _cacheConfig);
            default:
                throw new SfRateLimitAlgorithmNotFoundException(rateLimitAlgorithm);
        }
    }
}