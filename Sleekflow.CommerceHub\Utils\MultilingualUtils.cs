using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Stores;

namespace Sleekflow.CommerceHub.Utils;

public static class MultilingualUtils
{
    public static bool AreValidMultilinguals(IEnumerable<Multilingual> multilinguals, Store store)
    {
        var storeLanguageIsoCodes = store.Languages
            .Select(lang => lang.LanguageIsoCode)
            .ToHashSet();

        return AreValidMultilinguals(multilinguals, storeLanguageIsoCodes);
    }

    public static bool AreValidMultilinguals(IEnumerable<Multilingual> multilinguals, HashSet<string> languageIsoCodes)
    {
        return multilinguals.All(m => languageIsoCodes.Contains(m.LanguageIsoCode));
    }
}