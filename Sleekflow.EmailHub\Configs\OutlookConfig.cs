using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Configs;

public interface IOutlookConfig
{
    string ClientId { get; }

    string ClientSecret { get; }

    string RedirectUri { get; }

    string Tenant { get; }

    string NotificationUrl { get; }
}

public class OutlookConfig : IOutlookConfig, IConfig
{
    public string ClientId { get; }

    public string ClientSecret { get; }

    public string RedirectUri { get; }

    public string Tenant { get; }

    public string NotificationUrl { get; }

    public OutlookConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;
        ClientId =
            Environment.GetEnvironmentVariable("OUTLOOK_CLIENT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("OUTLOOK_CLIENT_ID");
        ClientSecret =
            Environment.GetEnvironmentVariable("OUTLOOK_CLIENT_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("OUTLOOK_CLIENT_SECRET");
        RedirectUri =
            Environment.GetEnvironmentVariable("OUTLOOK_REDIRECT_URI", target)
            ?? throw new SfMissingEnvironmentVariableException("OUTLOOK_REDIRECT_URI");
        Tenant =
            Environment.GetEnvironmentVariable("OUTLOOK_TENANT", target)
            ?? throw new SfMissingEnvironmentVariableException("OUTLOOK_TENANT");
        NotificationUrl =
            Environment.GetEnvironmentVariable("OUTLOOK_NOTIFICATION_URL", target)
            ?? throw new SfMissingEnvironmentVariableException("OUTLOOK_NOTIFICATION_URL");
    }
}