using MassTransit;
using Microsoft.Extensions.Logging;
using Sleekflow.IntelligentHub.Models.Workers;

namespace Sleekflow.IntelligentHub.Workers.Consumers;

public class StartWebsiteIngestionEventConsumer : IConsumer<StartWebsiteIngestionEvent>
{
    private readonly ILogger<StartWebsiteIngestionEventConsumer> _logger;

    public StartWebsiteIngestionEventConsumer(
        ILogger<StartWebsiteIngestionEventConsumer> logger)
    {
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<StartWebsiteIngestionEvent> context)
    {
        _logger.LogInformation("Event received: StartWebsiteIngestionEvent");
    }
} 