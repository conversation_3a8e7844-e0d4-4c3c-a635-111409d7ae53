using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.Dynamics365;

public class SubscriptionsCheckBatch
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public SubscriptionsCheckBatch(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public Dynamics365Subscription Subscription { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            Dynamics365Subscription subscription,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters,
            DateTimeOffset lastObjectModificationTime,
            string? nextRecordsUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
            LastObjectModificationTime = lastObjectModificationTime;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_last_object_modification_time")]
        public DateTimeOffset NextLastObjectModificationTime { get; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            DateTimeOffset nextLastObjectModificationTime,
            string? nextRecordsUrl)
        {
            Count = count;
            NextLastObjectModificationTime = nextLastObjectModificationTime;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    /// <seealso cref="Sleekflow.Integrator.Dynamics365.Triggers.Internals.SubscriptionsCheckBatch"/>
    [Function("Dynamics365_SubscriptionsCheck_Batch")]
    public async Task<SubscriptionsCheckBatchOutput> Batch(
        [ActivityTrigger]
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var inputJsonStr =
            JsonConvert.SerializeObject(subscriptionsCheckBatchInput, JsonConfig.DefaultJsonSerializerSettings);

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.Dynamics365IntegratorInternalsEndpoint + "/SubscriptionsCheckBatch"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(requestMessage)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();
        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<SubscriptionsCheckBatchOutput>()!;
    }
}