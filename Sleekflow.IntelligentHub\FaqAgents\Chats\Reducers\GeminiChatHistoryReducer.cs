using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;

public class GeminiChatHistoryReducer : IChatHistoryReducer
{
    // This reducer replaces the last message in the chat history with a user message
    // To ensure gemini would treat the last message as a more important message
    // This is to deal with an issue that gemini would not issue any function call if the last few messages are also model (assistant) messages
    public virtual Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        // replace the last message as user message
        var chatHistoryList = chatHistory.ToList();
        var lastMessage = chatHistoryList.LastOrDefault();
        if (lastMessage != null)
        {
#pragma warning disable SKEXP0070
            if (lastMessage is GeminiChatMessageContent geminiChatMessageContent)
            {
                var newMessage = new ChatMessageContent(
                    AuthorRole.User,
                    geminiChatMessageContent.Items,
                    geminiChatMessageContent.ModelId,
                    geminiChatMessageContent.InnerContent,
                    geminiChatMessageContent.Encoding,
                    geminiChatMessageContent.Metadata)
                {
                    AuthorName = geminiChatMessageContent.AuthorName,
                };

                chatHistoryList[^1] = newMessage;
            }
            else
            {
                var newMessage = new ChatMessageContent(
                    AuthorRole.User,
                    lastMessage.Items,
                    lastMessage.ModelId,
                    lastMessage.InnerContent,
                    lastMessage.Encoding,
                    lastMessage.Metadata)
                {
                    AuthorName = lastMessage.AuthorName,
                };

                chatHistoryList[^1] = newMessage;
            }
#pragma warning restore SKEXP0070
        }

        return Task.FromResult<IEnumerable<ChatMessageContent>?>(chatHistoryList);
    }
}