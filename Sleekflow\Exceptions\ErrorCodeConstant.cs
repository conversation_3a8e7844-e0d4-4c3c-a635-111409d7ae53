﻿namespace Sleekflow.Exceptions;

public static class ErrorCodeConstant
{
    // General
    public const int SfMissingEnvironmentVariableException = 1000;
    public const int SfValidationException = 1001;
    public const int SfUnauthorizedException = 1002;
    public const int SfNotFoundObjectException = 1003;
    public const int SfUnrecognizedEntityTypeException = 1004;
    public const int SfUserFriendlyException = 1005;
    public const int SfInternalException = 1006;
    public const int SfQueryException = 1007;
    public const int SfCognitiveSearchException = 1008;
    public const int SfExternalCallException = 1009;
    public const int SfServiceUnavailableException = 1010;
    public const int SfTransactionalBatchException = 1011;
    public const int SfAccessDeniedException = 1012;
    public const int SfDuplicateEnvVarException = 1013;
    public const int SfConcurrentOperationProhibitedException = 1014;
    public const int SfExceedUsageLimitException = 1015;
    public const int SfRateLimitAlgorithmNotFoundException = 1016;
    public const int SfInvalidChannelException = 1017;
    public const int SfRateLimitConfigNotFoundException = 1018;

    // Salesforce
    public const int SfSObjectOperationException = 2000;
    public const int SfSObjectQueryOperationException = 2001;
    public const int SfUnhandledApexOperationException = 2002;
    public const int SfUnrecognizedSObjectTypeNameException = 2003;
    public const int SfSApiRequestLimitExceededException = 2004;
    public const int SfSNotFoundException = 2005;
    public const int SfSInvalidTypeException = 2006;

    // Crm Hub
    public const int SfNotInitializedException = 3000;
    public const int SfDuplicateUnifyRuleException = 3001;
    public const int SfDuplicateWebhookException = 3002;
    public const int SfIdUnresolvableException = 3003;
    public const int SfReadOnlyIntegrationException = 3004;
    public const int SfCustomObjectConcurrentCallException = 3005;
    public const int SfCrmHubExceedUsageException = 3006;
    public const int SfCustomObjectPrimaryPropertyValueOccupiedException = 3007;
    public const int SfCustomObjectSleekflowUserProfileIdOccupiedException = 3008;
    public const int SfCustomObjectNestedInnerSchemaException = 3009;

    // Hubspot
    public const int SfMissingNecessaryFieldFiltersException = 4001;
    public const int SfNotSupportedOperationException = 4002;

    // CrmHubWorker
    public const int SfWebhookProcessingException = 5000;

    // MessagingHub
    public const int SfStillInProgressException = 6000;
    public const int SfGraphApiErrorException = 6001;
    public const int SfMessageFunctionRestrictedFunctionException = 6002;
    public const int SfIncorrectStateIdException = 6003;
    public const int SfFacebookBusinessNegativeBalanceException = 6004;
    public const int SfWabaPhoneNumberStillAssociatedException = 6005;
    public const int SfConcurrentETagException = 6006;
    public const int SfCreditTransferOutOfCreditException = 6007;

    // Dynamics365
    public const int SfIntegratorOperationException = 7000;

    // CommerceHub
    public const int SfUnsupportedDiscountTypeException = 8000;
    public const int SfExceedAvailableCountException = 8001;
    public const int SfVtexInvalidCredentialException = 8002;
    public const int SfVtexHttpRequestException = 8003;

    // FlowHub
    public const int SfScriptingException = 9000;
    public const int SfMultipleWorkflowActiveException = 9001;
    public const int SfWorkflowIdsNotMatchException = 9002;
    public const int SfWorkflowNotActiveException = 9003;
    public const int SfWorkflowVersionedIdNotMatchedException = 9004;
    public const int SfInfiniteDetectionException = 9005;
    public const int SfWebhookEventMatchingException = 9006;
    public const int SfFlowHubUserFriendlyException = 9007;
    public const int SfWorkflowWebhookTriggerNotFoundException = 9008;
    public const int SfFlowHubExceedUsageException = 9009;
    public const int SfWorkflowExecutionStatusNotCancellableException = 9010;
    public const int SfWorkflowGroupNameDuplicateException = 9011;
    public const int SfWorkflowStateNotFoundException = 9012;
    public const int SfWorkflowTypeException = 9013;
    public const int SfWorkflowDeletedException = 9014;

    // IntelligentHub
    public const int SfKnowledgeBaseSourceTypeNotSupportedException = 10000;
    public const int SfKnowledgeBaseDocumentTypeNotSupportedException = 10001;

    // TenantHub
    public const int SfExceedSubscriptionPlanLimitException = 11000;
    public const int SfInvalidIpAddressException = 11001;
    public const int SfReachIpAddressLimitException = 11002;
    public const int SfInvalidTravisBackendModelStateException = 11003;
    public const int SfInvalidTravisBackendRequestException = 11004;
    public const int SfInvalidEventTokenException = 11005;
    public const int SfAuth0ActionFlowsEventException = 11006;
    public const int SfAuth0UserManagementException = 11007;
    public const int SfCoreInvalidModelStateException = 11008;
    public const int SfDuplicateUserException = 11009;
    public const int SfDuplicateUserEmailException = 11010;
    public const int SfInvalidValueException = 11011;

    // TicketingHub
    public const int SfInvalidTicketStatusException = 12000;
    public const int SfInvalidTicketPriorityException = 12001;
    public const int SfInvalidTicketTypeException = 12002;
    public const int SfDuplicateLabelException = 12003;
    public const int SfExceedLimitException = 12004;

    // UserEventHub
    public const int SfNotificationEventNotEnabledException = 13000;
    public const int SfSendNotificationFail = 13001;
    public const int SfNotificationInvalidPlatformException = 13002;

    // UserEventAnalyticsHub
    public const int SfSqlException = 14000;

    // Zoho
    public const int SfZObjectOperationException = 15000;
    public const int SfZObjectQueryOperationException = 15001;

    // Travis_backend error code reserved for 20000 - 30000
}