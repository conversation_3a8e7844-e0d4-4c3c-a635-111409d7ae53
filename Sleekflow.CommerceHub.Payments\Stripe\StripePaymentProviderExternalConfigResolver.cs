using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.DependencyInjection;
using Stripe;

namespace Sleekflow.CommerceHub.Payments.Stripe;

public interface IStripePaymentProviderExternalConfigResolver
{
    Task<StripePaymentProviderExternalConfig> ResolveAsync(
        string apiKey,
        string connectWebhookSecret,
        string paymentWebhookSecret,
        decimal applicationFeeRate,
        bool isShippingEnabled,
        List<string>? shippingEnabledCountryIsoCodes,
        bool isInventoryEnabled);
}

public class StripePaymentProviderExternalConfigResolver
    : IStripePaymentProviderExternalConfigResolver, ISingletonService
{
    private readonly IStripeClients _stripeClients;

    public StripePaymentProviderExternalConfigResolver(
        IStripeClients stripeClients)
    {
        _stripeClients = stripeClients;
    }

    public async Task<StripePaymentProviderExternalConfig> ResolveAsync(
        string apiKey,
        string connectWebhookSecret,
        string paymentWebhookSecret,
        decimal applicationFeeRate,
        bool isShippingEnabled,
        List<string>? shippingEnabledCountryIsoCodes,
        bool isInventoryEnabled)
    {
        var stripeClient = _stripeClients.GetCustomStripeClient(apiKey);

        var accountService = new AccountService(stripeClient);

        var accountCreateOptions = new AccountCreateOptions()
        {
            Type = "express",
            Metadata = new Dictionary<string, string>()
            {
                {
                    "source", "commerce-hub"
                }
            },
        };
        var account = await accountService.CreateAsync(accountCreateOptions);

        return new StripePaymentProviderExternalConfig(
            "Stripe",
            account.Id,
            apiKey,
            connectWebhookSecret,
            paymentWebhookSecret,
            account,
            false,
            applicationFeeRate,
            isShippingEnabled,
            shippingEnabledCountryIsoCodes,
            isInventoryEnabled);
    }
}