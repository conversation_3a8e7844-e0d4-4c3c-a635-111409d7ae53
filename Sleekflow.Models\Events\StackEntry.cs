using Newtonsoft.Json;

// ReSharper disable once CheckNamespace
namespace Sleekflow.FlowHub.Models.States;

public class StackEntry
{
    [JsonProperty("step_id")]
    public string StepId { get; set; }

    [JsonProperty("worker_instance_id")]
    public string? WorkerInstanceId { get; set; }

    [JsonConstructor]
    public StackEntry(string stepId, string? workerInstanceId)
    {
        StepId = stepId;
        WorkerInstanceId = workerInstanceId;
    }
}