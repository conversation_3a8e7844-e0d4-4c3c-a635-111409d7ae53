﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Documents.Chunks;

public class Chunk : Entity, IHasSleekflowCompanyId, IHasMetadata, IHasCreatedAt, IHasUpdatedAt
{
    public const string PropertyNameContent = "content";
    public const string PropertyNameContentEn = "content_en";
    public const string PropertyNameCategories = "categories";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameContent)]
    public string Content { get; set; }

    [JsonProperty(PropertyNameContentEn)]
    public string ContentEn { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(PropertyNameCategories)]
    public List<Category> Categories { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public Chunk(
        string id,
        string sysTypeName,
        string sleekflowCompanyId,
        string content,
        string contentEn,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<Category> categories,
        Dictionary<string, object?> metadata)
        : base(id, sysTypeName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Content = content;
        ContentEn = contentEn;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        Categories = categories;
        Metadata = metadata;
    }
}