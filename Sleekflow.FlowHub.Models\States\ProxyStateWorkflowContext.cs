﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows;

namespace Sleekflow.FlowHub.Models.States;

public class ProxyStateWorkflowContext
{
    [JsonProperty("snapshotted_workflow")]
    public ProxyWorkflow SnapshottedWorkflow { get; set; }

    [JsonConstructor]
    public ProxyStateWorkflowContext(ProxyWorkflow snapshottedWorkflow)
    {
        SnapshottedWorkflow = snapshottedWorkflow;
    }
}