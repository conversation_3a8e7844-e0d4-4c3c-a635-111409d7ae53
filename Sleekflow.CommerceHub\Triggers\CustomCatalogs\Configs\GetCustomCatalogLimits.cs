using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.CustomCatalogs.Configs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs.Configs;

[TriggerGroup(ControllerNames.CustomCatalogConfigs)]
public class GetCustomCatalogLimits
    : ITrigger<
        GetCustomCatalogLimits.GetCustomCatalogLimitsInput,
        GetCustomCatalogLimits.GetCustomCatalogLimitsOutput>
{
    private readonly ICustomCatalogConfigService _customCatalogConfigService;

    public GetCustomCatalogLimits(ICustomCatalogConfigService customCatalogConfigService)
    {
        _customCatalogConfigService = customCatalogConfigService;
    }

    public class GetCustomCatalogLimitsInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetCustomCatalogLimitsInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetCustomCatalogLimitsOutput
    {
        [JsonProperty("product_limit")]
        public int ProductLimit { get; set; }

        [JsonProperty("product_variant_limit")]
        public int ProductVariantLimit { get; set; }

        [JsonConstructor]
        public GetCustomCatalogLimitsOutput(int productLimit, int productVariantLimit)
        {
            ProductLimit = productLimit;
            ProductVariantLimit = productVariantLimit;
        }
    }

    public async Task<GetCustomCatalogLimitsOutput> F(
        GetCustomCatalogLimitsInput getCustomCatalogLimitsInput)
    {
        var sleekflowCompanyId = getCustomCatalogLimitsInput.SleekflowCompanyId;
        var productLimit = await _customCatalogConfigService.GetCustomCatalogConfigLimitAsync(
            sleekflowCompanyId,
            SysTypeNames.Product);
        var productVariantLimit = await _customCatalogConfigService.GetCustomCatalogConfigLimitAsync(
            sleekflowCompanyId,
            SysTypeNames.ProductVariant);
        return new GetCustomCatalogLimitsOutput(productLimit, productVariantLimit);
    }
}