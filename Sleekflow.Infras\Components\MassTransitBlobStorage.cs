using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;

namespace Sleekflow.Infras.Components;

public class MassTransitBlobStorage
{
    public record MassTransitBlobStorageOutput(Output<string> StorageAccountConnStr, string ContainerName);

    private readonly MyConfig _myConfig;
    private readonly ResourceGroup _resourceGroup;

    public MassTransitBlobStorage(
        MyConfig myConfig,
        ResourceGroup resourceGroup)
    {
        _myConfig = myConfig;
        _resourceGroup = resourceGroup;
    }

    public MassTransitBlobStorageOutput InitMassTransitBlobStorage(string? name = null, string? locationName = null)
    {
        const string randomIdName = "sleekflow-mass-transit-storage-account-random-id";
        var randomId = new Random.RandomId(
            name is not null ? $"{randomIdName}-{name}" : randomIdName,
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "MassTransit", $"MessageData{locationName ?? string.Empty}"
                    }
                },
            });
        const string storageAccountName = "sleekflow-mass-transit-storage-account";
        var storageAccount = new Storage.StorageAccount(
            name is not null ? $"{storageAccountName}-{name}" : storageAccountName,
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location =
                    locationName is not null
                        ? LocationNames.GetAzureLocation(locationName)
                        : _resourceGroup.Location,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-mass-transit-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = randomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        const string blobContainerName = "sleekflow-mass-transit-container";
        var _ = new Storage.BlobContainer(
            name is not null ? $"{blobContainerName}-{name}" : blobContainerName,
            new Storage.BlobContainerArgs
            {
                AccountName = storageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "message-data",
            },
            new CustomResourceOptions
            {
                Parent = storageAccount
            });

        return new MassTransitBlobStorageOutput(
            StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
            "message-data");
    }
}