using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class GetFileDocumentInformation
    : ITrigger<
        GetFileDocumentInformation.GetFileDocumentInformationInput,
        GetFileDocumentInformation.GetFileDocumentInformationOutput>
{
    private readonly IFileDocumentService _fileDocumentService;

    public GetFileDocumentInformation(IFileDocumentService fileDocumentService)
    {
        _fileDocumentService = fileDocumentService;
    }

    public class GetFileDocumentInformationInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(FileDocument.PropertyNameBlobId)]
        public string BlobId { get; set; }

        [JsonConstructor]
        public GetFileDocumentInformationInput(string sleekflowCompanyId, string blobId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
        }
    }

    public class GetFileDocumentInformationOutput
    {
        [JsonProperty("document")]
        public KbDocument Document { get; set; }

        [JsonConstructor]
        public GetFileDocumentInformationOutput(KbDocument document)
        {
            Document = document;
        }
    }

    public async Task<GetFileDocumentInformationOutput> F(
        GetFileDocumentInformationInput getFileDocumentInformationInput)
    {
        var fileDocument = await _fileDocumentService.GetDocumentByBlobIdAsync(
            getFileDocumentInformationInput.SleekflowCompanyId,
            getFileDocumentInformationInput.BlobId);
        return new GetFileDocumentInformationOutput(fileDocument);
    }
}