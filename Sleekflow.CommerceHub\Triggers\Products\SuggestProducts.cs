using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class SuggestProducts
    : ITrigger<
        SuggestProducts.SuggestProductsInput,
        SuggestProducts.SuggestProductsOutput>
{
    private readonly IProductSearchService _productSearchService;
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;

    public SuggestProducts(
        IProductSearchService productSearchService,
        IProductService productService,
        IProductVariantService productVariantService)
    {
        _productSearchService = productSearchService;
        _productService = productService;
        _productVariantService = productVariantService;
    }

    public class SuggestProductsInput
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(256, MinimumLength = 1)]
        [JsonProperty("search_text")]
        public string SearchText { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public SuggestProductsInput(
            string sleekflowCompanyId,
            string storeId,
            string searchText,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            SearchText = searchText;
            Limit = limit;
        }
    }

    public class SuggestProductsOutput
    {
        [JsonProperty("products")]
        public List<ProductDto> Products { get; set; }

        [JsonConstructor]
        public SuggestProductsOutput(
            List<ProductDto> products)
        {
            Products = products;
        }
    }

    public async Task<SuggestProductsOutput> F(SuggestProductsInput suggestProductsInput)
    {
        var productIndexDtos =
            await _productSearchService.SuggestProductsAsync(
                suggestProductsInput.SleekflowCompanyId,
                suggestProductsInput.StoreId,
                suggestProductsInput.SearchText,
                suggestProductsInput.Limit);

        var products = await _productService.GetProductsAsync(
            productIndexDtos.Select(p => p.Id).ToList(),
            suggestProductsInput.SleekflowCompanyId,
            suggestProductsInput.StoreId);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            suggestProductsInput.SleekflowCompanyId,
            suggestProductsInput.StoreId,
            products.Select(p => p.Id).ToList());

        var productIdToProductVariantsDict = productVariants
            .GroupBy(pv => pv.ProductId)
            .ToDictionary(e => e.Key, e => e.ToList());

        return new SuggestProductsOutput(
            products
                .Select(
                    c => new ProductDto(
                        c,
                        productIdToProductVariantsDict.GetValueOrDefault(c.Id, new List<ProductVariant>())))
                .ToList());
    }
}