﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnZohoObjectEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnZohoObjectEnrolled; }
    }

    [Required]
    [JsonProperty("zoho_connection_id")]
    public string ZohoConnectionId { get; set; }

    [Required]
    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [Required]
    [JsonProperty("object_dict")]
    public Dictionary<string, object?> ObjectDict { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnZohoObjectEnrolledEventBody(
        DateTimeOffset createdAt,
        string zohoConnectionId,
        string objectType,
        Dictionary<string, object?> objectDict,
        string workflowId,
        string workflowVersionedId)
        : base(createdAt)
    {
        ZohoConnectionId = zohoConnectionId;
        ObjectType = objectType;
        ObjectDict = objectDict;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}