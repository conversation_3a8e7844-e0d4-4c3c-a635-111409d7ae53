﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Providers.Models;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Ids;
using Sleekflow.Models.Constants;

namespace Sleekflow.CrmHub.Providers.States;

public interface ILoopThroughObjectsProgressStateService
{
    Task<LoopThroughObjectsProgressState?> GetStateAsync<T>(
        string id,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId);

    Task<LoopThroughObjectsProgressState?> GetStateAsync<T>(
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId);

    Task PatchLoopThroughObjectsProgressStateAsync(
        string id,
        string sleekflowCompanyId,
        StatusQueryGetOutput<StatusQueryGetOutputInput, StatusQueryGetOutputCustomStatus, StatusQueryGetOutputOutput>
            statusQueryGetOutput);

    Task<string> CreateOrUpdateLoopThroughObjectsProgressStateAsync(
        DurablePayload durablePayload,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId);

    Task<List<LoopThroughObjectsProgressState>> GetInProgressLoopThroughObjectsProgressStatesAsync(
        string providerName,
        string flowHubWorkflowId,
        string sleekflowCompanyId);

    bool IsTerminalRuntimeStatus(string? runtimeStatus);
}

public class LoopThroughObjectsProgressStateService : ISingletonService, ILoopThroughObjectsProgressStateService
{
    private List<string> TerminalRuntimeStatuses { get; } = new()
    {
        AzureDurableFunctionRuntimeStatuses.Failed,
        AzureDurableFunctionRuntimeStatuses.Canceled,
        AzureDurableFunctionRuntimeStatuses.Terminated,
        AzureDurableFunctionRuntimeStatuses.Completed
    };

    private readonly IIdService _idService;
    private readonly ILoopThroughObjectsProgressStateRepository _loopThroughObjectsProgressStateRepository;

    public LoopThroughObjectsProgressStateService(
        IIdService idService,
        ILoopThroughObjectsProgressStateRepository loopThroughObjectsProgressStateRepository)
    {
        _idService = idService;
        _loopThroughObjectsProgressStateRepository = loopThroughObjectsProgressStateRepository;
    }

    public async Task<LoopThroughObjectsProgressState?> GetStateAsync<T>(
        string id,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE c.id = @id "
                    + "AND c.provider_name = @providerName "
                    + "AND c.flow_hub_workflow_id = @flowHubWorkflowId "
                    + "AND c.flow_hub_workflow_versioned_id = @flowHubWorkflowVersionedId "
                    + "AND c.sleekflow_company_id = @sleekflowCompanyId ")
                .WithParameter("@id", id)
                .WithParameter("@providerName", providerName)
                .WithParameter("@flowHubWorkflowId", flowHubWorkflowId)
                .WithParameter("@flowHubWorkflowVersionedId", flowHubWorkflowVersionedId)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId);

        var objects = await _loopThroughObjectsProgressStateRepository.GetObjectsAsync(queryDefinition);
        if (objects.Count == 0)
        {
            return default;
        }

        return objects[0];
    }

    public async Task<LoopThroughObjectsProgressState?> GetStateAsync<T>(
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE c.provider_name = @providerName "
                    + "AND c.flow_hub_workflow_id = @flowHubWorkflowId "
                    + "AND c.flow_hub_workflow_versioned_id = @flowHubWorkflowVersionedId "
                    + "AND c.sleekflow_company_id = @sleekflowCompanyId ")
                .WithParameter("@providerName", providerName)
                .WithParameter("@flowHubWorkflowId", flowHubWorkflowId)
                .WithParameter("@flowHubWorkflowVersionedId", flowHubWorkflowVersionedId)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId);

        var objects = await _loopThroughObjectsProgressStateRepository.GetObjectsAsync(queryDefinition);
        if (objects.Count == 0)
        {
            return default;
        }

        return objects[0];
    }

    public async Task PatchLoopThroughObjectsProgressStateAsync(
        string id,
        string sleekflowCompanyId,
        StatusQueryGetOutput<
                StatusQueryGetOutputInput,
                StatusQueryGetOutputCustomStatus,
                StatusQueryGetOutputOutput>
            statusQueryGetOutput)
    {
        await _loopThroughObjectsProgressStateRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace(
                    $"/{SyncObjectsProgressState.PropertyNameStateObj}/query_output",
                    statusQueryGetOutput)
            });
    }

    public async Task<string> CreateOrUpdateLoopThroughObjectsProgressStateAsync(
        DurablePayload durablePayload,
        string providerName,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId)
    {
        var state = await GetStateAsync<LoopThroughObjectsProgressState?>(
            providerName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            sleekflowCompanyId);

        if (state is null)
        {
            var loopThroughObjectsProgressState = new LoopThroughObjectsProgressState(
                _idService.GetId("LoopThroughObjectsProgressState"),
                stateName: "LoopThroughObjectsProgressState",
                providerName,
                flowHubWorkflowId,
                flowHubWorkflowVersionedId,
                new LoopThroughObjectsProgressState.LoopThroughObjectsProgressStateObj(durablePayload, queryOutput: null),
                sleekflowCompanyId);

            loopThroughObjectsProgressState = await _loopThroughObjectsProgressStateRepository.CreateAndGetAsync(
                loopThroughObjectsProgressState, sleekflowCompanyId);

            return loopThroughObjectsProgressState.Id;
        }

        if (!IsTerminalRuntimeStatus(state.StateObj.QueryOutput?.RuntimeStatus))
        {
            throw new InvalidOperationException(
                $"LoopThroughObjectsProgressState in progress for " +
                $"provider {providerName}, " +
                $"workflow {flowHubWorkflowId}, " +
                $"version {flowHubWorkflowVersionedId}" +
                $"sleekflowCompanyId {sleekflowCompanyId}");
        }

        await _loopThroughObjectsProgressStateRepository.PatchAsync(
            state.Id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace(
                    $"/{SyncObjectsProgressState.PropertyNameStateObj}",
                    new LoopThroughObjectsProgressState.LoopThroughObjectsProgressStateObj(durablePayload, queryOutput: null))
            });

        return state.Id;
    }

    public async Task<List<LoopThroughObjectsProgressState>> GetInProgressLoopThroughObjectsProgressStatesAsync(
        string providerName,
        string flowHubWorkflowId,
        string sleekflowCompanyId)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE c.provider_name = @providerName "
                    + "AND c.flow_hub_workflow_id = @flowHubWorkflowId "
                    + "AND c.sleekflow_company_id = @sleekflowCompanyId "
                    + "AND (c.state_obj.query_output = null "
                    + "OR NOT array_contains(@terminalStatuses, c.state_obj.query_output.runtimeStatus))")
                .WithParameter("@providerName", providerName)
                .WithParameter("@flowHubWorkflowId", flowHubWorkflowId)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@terminalStatuses", TerminalRuntimeStatuses);

        return await _loopThroughObjectsProgressStateRepository.GetObjectsAsync(queryDefinition);
    }

    public bool IsTerminalRuntimeStatus(string? runtimeStatus)
    {
        return runtimeStatus is not null && TerminalRuntimeStatuses.Contains(runtimeStatus);
    }
}