using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.CommerceHub;

public class CommerceHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public CommerceHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class CommerceHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public CommerceHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public CommerceHubDbOutput InitCommerceHubDb()
    {
        const string cosmosDbId = "commercehubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 10000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.CommerceHubDb.ICommerceHubDbService
        var containerParams = new ContainerParam[]
        {
            new (
                "cart",
                "cart",
                new List<string>
                {
                    "/sleekflow_user_profile_id"
                }
            ),
            new (
                "category",
                "category",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "product",
                "product",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "order",
                "order",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "payment_provider_config",
                "payment_provider_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "store",
                "store",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new ContainerParam(
                "sys_changefeed_lease",
                "sys_changefeed_lease",
                new List<string>
                {
                    "/id"
                },
                MaxThroughput: 1000),
            new ContainerParam(
                "custom_catalog_config",
                "custom_catalog_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new ContainerParam(
                "custom_catalog_file",
                "custom_catalog_file",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new ContainerParam(
                "vtex_authentication",
                "vtex_authentication",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new ContainerParam(
                "sys_state_loop_through_objects_progress",
                "sys_state_loop_through_objects_progress",
                new List<string>
                {
                    "/sleekflow_company_id"
                },
                Ttl: 3600 * 24 * 365,
                MaxThroughput: 1000),
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

#pragma warning disable S1848
        new DocumentDB.SqlResourceSqlTrigger(
            $"{cosmosDbId}-upsert-product-procedure",
            new ()
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                DatabaseName = cosmosDb.Name,
                ContainerName = "product",
                Resource = new DocumentDB.Inputs.SqlTriggerResourceArgs
                {
                    Body = string.Join("\n", File.ReadAllLines("./Resources/upsert-product-procedure.js")),
                    Id = "upsert-product",
                    TriggerOperation = DocumentDB.TriggerOperation.All,
                    TriggerType = DocumentDB.TriggerType.Post
                },
                TriggerName = "upsert-product"
            },
            new CustomResourceOptions
            {
                Parent = containerIdToContainer["product"]
            });
#pragma warning restore S1848

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new CommerceHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}