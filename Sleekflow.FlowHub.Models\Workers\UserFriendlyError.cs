using Newtonsoft.Json;
using Orleans;

namespace Sleekflow.FlowHub.Models.Workers;

[GenerateSerializer]
public class UserFriendlyError
{
    [Id(0)]
    [JsonProperty("error_code")]
    public string ErrorCode { get; set; }

    [Id(1)]
    [JsonProperty("error_message")]
    public string ErrorMessage { get; set; }

    [JsonConstructor]
    public UserFriendlyError(string errorCode, string errorMessage)
    {
        ErrorCode = errorCode;
        ErrorMessage = errorMessage;
    }
}