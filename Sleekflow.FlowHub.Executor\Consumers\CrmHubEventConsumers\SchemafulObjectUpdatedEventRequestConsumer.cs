﻿using MassTransit;
using Newtonsoft.Json.Linq;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CrmHubEventConsumers;

public class SchemafulObjecUpdatedEventRequestConsumerDefinition
    : ConsumerDefinition<SchemafulObjectUpdatedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SchemafulObjectUpdatedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SchemafulObjectUpdatedEventRequestConsumer : IConsumer<SchemafulObjectUpdatedEventRequest>
{
    private readonly IServiceBusManager _serviceBusManager;

    public SchemafulObjectUpdatedEventRequestConsumer(
        IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<SchemafulObjectUpdatedEventRequest> context)
    {
        var schemafulObjecUpdatedEventRequest = context.Message;

        // DEVS-7841 changed to use Contact.Id
        // var identityObjectId
        // $"{schemafulObjecUpdatedEventRequest.SchemaId}:{schemafulObjecUpdatedEventRequest.SchemafulObjectId}";
        var identityObjectId = schemafulObjecUpdatedEventRequest.PostUpdatedSleekflowUserProfileId;

        var changeEntries = GetChangeEntries(
            schemafulObjecUpdatedEventRequest.PreUpdatedSleekflowUserProfileId,
            schemafulObjecUpdatedEventRequest.PostUpdatedSleekflowUserProfileId,
            schemafulObjecUpdatedEventRequest.PreUpdatedPropertyValues,
            schemafulObjecUpdatedEventRequest.PostUpdatedPropertyValues);

        if (changeEntries.Count == 0)
        {
            return;
        }

        await _serviceBusManager.PublishAsync(
            new OnTriggerEventRequestedEvent(
                new OnSchemafulObjectUpdatedEventBody(
                    schemafulObjecUpdatedEventRequest.CreatedAt,
                    schemafulObjecUpdatedEventRequest.SchemafulObjectId,
                    schemafulObjecUpdatedEventRequest.SchemaId,
                    schemafulObjecUpdatedEventRequest.PrimaryPropertyValue,
                    schemafulObjecUpdatedEventRequest.PreUpdatedSleekflowUserProfileId,
                    schemafulObjecUpdatedEventRequest.PostUpdatedSleekflowUserProfileId,
                    schemafulObjecUpdatedEventRequest.PreUpdatedPropertyValues,
                    schemafulObjecUpdatedEventRequest.PostUpdatedPropertyValues,
                    changeEntries,
                    schemafulObjecUpdatedEventRequest.SleekflowStaffId,
                    schemafulObjecUpdatedEventRequest.SleekflowStaffTeamIds),
                identityObjectId,
                "Contact",
                schemafulObjecUpdatedEventRequest.SleekflowCompanyId));
    }

    private List<OnSchemafulObjectUpdatedEventBodyChangeEntry> GetChangeEntries(
        string preUpdatedSleekflowUserProfileId,
        string postUpdatedSleekflowUserProfileId,
        Dictionary<string, object?> preUpdatedPropertyValues,
        Dictionary<string, object?> postUpdatedPropertyValues)
    {
        var changeEntries = new List<OnSchemafulObjectUpdatedEventBodyChangeEntry>();

        if (!preUpdatedSleekflowUserProfileId.Equals(postUpdatedSleekflowUserProfileId))
        {
            changeEntries.Add(new OnSchemafulObjectUpdatedEventBodyChangeEntry(
                "contact_id",
                "contact_id",
                preUpdatedSleekflowUserProfileId,
                postUpdatedSleekflowUserProfileId));
        }

        foreach (var key in preUpdatedPropertyValues.Keys)
        {
            if (postUpdatedPropertyValues.ContainsKey(key))
            {
                if (Equals(preUpdatedPropertyValues[key], postUpdatedPropertyValues[key]))
                {
                    continue;
                }

                changeEntries.Add(
                    new OnSchemafulObjectUpdatedEventBodyChangeEntry(
                        key,
                        key,
                        preUpdatedPropertyValues[key],
                        postUpdatedPropertyValues[key]));
            }
            else if (preUpdatedPropertyValues[key] == null)
            {
                // when property is deleted from this schema && no value is set before
            }
            else
            {
                changeEntries.Add(
                    new OnSchemafulObjectUpdatedEventBodyChangeEntry(
                        key,
                        key,
                        preUpdatedPropertyValues[key],
                        null));
            }
        }

        foreach (var key in postUpdatedPropertyValues.Keys)
        {
            if (!preUpdatedPropertyValues.ContainsKey(key) &&

                // when new property is added to this schema && no value is set
                postUpdatedPropertyValues[key] != null)
            {
                changeEntries.Add(
                    new OnSchemafulObjectUpdatedEventBodyChangeEntry(
                        key,
                        key,
                        null,
                        postUpdatedPropertyValues[key]));
            }
        }

        return changeEntries;
    }

    private static new bool Equals(object? value1, object? value2)
    {
        if (value1 == null && value2 == null)
        {
            return true;
        }

        if (value1 == null || value2 == null)
        {
            return false;
        }

        if (value1 is DateTimeOffset dateTimeOffset1 && value2 is DateTimeOffset dateTimeOffset2)
        {
            var totalSeconds1 = dateTimeOffset1.Ticks / TimeSpan.TicksPerSecond;
            var totalSeconds2 = dateTimeOffset2.Ticks / TimeSpan.TicksPerSecond;

            return totalSeconds1.CompareTo(totalSeconds2) == 0;
        }

        return JToken.DeepEquals(JToken.FromObject(value1), JToken.FromObject(value2));
    }
}