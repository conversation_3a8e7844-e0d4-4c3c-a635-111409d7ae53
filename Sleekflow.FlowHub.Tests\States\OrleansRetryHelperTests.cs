using System.Diagnostics;
using Sleekflow.FlowHub.Models.States.Abstractions;

namespace Sleekflow.FlowHub.Tests.States.Unit;

[TestFixture]
public class OrleansRetryHelperTests
{
    [Test]
    public async Task ExecuteWithRetryAsync_SuccessfulOperation_ReturnsResult()
    {
        // Arrange
        const string expectedResult = "success";
        var operation = () => Task.FromResult(expectedResult);

        // Act
        var result = await OrleansRetryHelper.ExecuteWithRetryAsync(operation, operationName: "TestOperation");

        // Assert
        Assert.That(result, Is.EqualTo(expectedResult));
    }

    [Test]
    public async Task ExecuteWithRetryAsync_VoidOperation_CompletesSuccessfully()
    {
        // Arrange
        var executed = false;
        var operation = () =>
        {
            executed = true;
            return Task.CompletedTask;
        };

        // Act
        await OrleansRetryHelper.ExecuteWithRetryAsync(operation, operationName: "VoidTestOperation");

        // Assert
        Assert.That(executed, Is.True);
    }

    [Test]
    public async Task ExecuteWithRetryAsync_OrleansExceptionWithRetries_RetriesAndSucceeds()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            if (attemptCount < 3)
            {
                throw new InvalidOperationException(
                    "Orleans.Runtime.OrleansException: Silo is not owner of dict/test, hop limit is reached");
            }

            return Task.FromResult("success");
        };

        // Act
        var result = await OrleansRetryHelper.ExecuteWithRetryAsync(operation, operationName: "RetryTest");

        // Assert
        Assert.That(result, Is.EqualTo("success"));
        Assert.That(attemptCount, Is.EqualTo(3));
    }

    [Test]
    public void ExecuteWithRetryAsync_NonOrleansException_ThrowsImmediately()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            throw new ArgumentException("This is not an Orleans exception");
        };

        // Act & Assert
        var ex = Assert.ThrowsAsync<ArgumentException>(() => OrleansRetryHelper.ExecuteWithRetryAsync(
            () =>
            {
                operation();
                return Task.CompletedTask;
            },
            operationName: "NonOrleansTest"));

        Assert.That(ex.Message, Is.EqualTo("This is not an Orleans exception"));
        Assert.That(attemptCount, Is.EqualTo(1)); // Should not retry
    }

    [Test]
    public void ExecuteWithRetryAsync_OrleansExceptionExceedsMaxRetries_ThrowsWithContext()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            throw new InvalidOperationException("Orleans persistent error with hop limit is reached");
        };

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => OrleansRetryHelper.ExecuteWithRetryAsync(
            () =>
            {
                operation();
                return Task.CompletedTask;
            },
            maxRetries: 2,
            operationName: "MaxRetriesTest"));

        Assert.That(ex.Message, Does.Contain("Orleans MaxRetriesTest failed after 2 retries"));
        Assert.That(ex.Message, Does.Contain("grain directory issues"));
        Assert.That(ex.Message, Does.Contain("hop limit is reached"));
        Assert.That(attemptCount, Is.EqualTo(2));
    }

    [Test]
    public void ExecuteWithRetryAsync_VoidOperationWithMaxRetries_ThrowsWithContext()
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            throw new InvalidOperationException("Orleans persistent error with grain directory issues");
        };

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => OrleansRetryHelper.ExecuteWithRetryAsync(
            () =>
            {
                operation();
                return Task.CompletedTask;
            },
            maxRetries: 2,
            operationName: "VoidMaxRetriesTest"));

        Assert.That(ex.Message, Does.Contain("Orleans VoidMaxRetriesTest failed after 2 retries"));
        Assert.That(ex.Message, Does.Contain("grain directory issues"));
        Assert.That(attemptCount, Is.EqualTo(2));
    }

    [Test]
    public async Task ExecuteWithRetryAsync_CustomRetrySettings_UsesCorrectTiming()
    {
        // Arrange
        var attemptCount = 0;
        var stopwatch = Stopwatch.StartNew();
        var operation = () =>
        {
            attemptCount++;
            if (attemptCount < 2)
            {
                throw new InvalidOperationException(
                    "Orleans.Runtime.OrleansException: cannot forward due to hop limit");
            }

            return Task.FromResult("success");
        };

        // Act
        var result = await OrleansRetryHelper.ExecuteWithRetryAsync(
            operation,
            maxRetries: 3,
            initialDelay: TimeSpan.FromMilliseconds(50),
            operationName: "TimingTest");

        // Assert
        stopwatch.Stop();
        Assert.That(result, Is.EqualTo("success"));
        Assert.That(attemptCount, Is.EqualTo(2));
        // Should have at least 50ms delay (first retry)
        Assert.That(stopwatch.ElapsedMilliseconds, Is.GreaterThan(40));
    }

    [TestCase("Orleans.Runtime.OrleansException: hop limit is reached")]
    [TestCase("Orleans silo is not owner of dict/test")]
    [TestCase("Orleans cannot forward LookUpAsync to owner")]
    [TestCase("Orleans grain directory lookup failed")]
    [TestCase("Orleans silo is not owner of the key")]
    public async Task ExecuteWithRetryAsync_VariousOrleansExceptions_RetriesCorrectly(string exceptionMessage)
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            if (attemptCount < 3) // Fail first 2 attempts, succeed on 3rd
            {
                throw new InvalidOperationException(exceptionMessage);
            }

            return Task.FromResult("success");
        };

        // Act
        var result = await OrleansRetryHelper.ExecuteWithRetryAsync(operation, operationName: "VariousExceptionsTest");

        // Assert
        Assert.That(result, Is.EqualTo("success"));
        Assert.That(attemptCount, Is.EqualTo(3)); // Should retry twice, succeed on third attempt
    }

    [TestCase("Regular exception without Orleans")]
    [TestCase("Database connection failed")]
    [TestCase("Network timeout")]
    public void ExecuteWithRetryAsync_NonOrleansExceptions_DoesNotRetry(string exceptionMessage)
    {
        // Arrange
        var attemptCount = 0;
        var operation = () =>
        {
            attemptCount++;
            throw new InvalidOperationException(exceptionMessage);
        };

        // Act & Assert
        var ex = Assert.ThrowsAsync<InvalidOperationException>(() => OrleansRetryHelper.ExecuteWithRetryAsync(
            () =>
            {
                operation();
                return Task.CompletedTask;
            },
            operationName: "NonRetryTest"));

        Assert.That(ex.Message, Is.EqualTo(exceptionMessage));
        Assert.That(attemptCount, Is.EqualTo(1)); // Should not retry
    }

    [Test]
    public async Task ExecuteWithRetryAsync_ExponentialBackoff_IncreasesTiming()
    {
        // Arrange
        var attemptCount = 0;
        var timings = new List<long>();
        var stopwatch = Stopwatch.StartNew();

        var operation = () =>
        {
            attemptCount++;
            timings.Add(stopwatch.ElapsedMilliseconds);

            if (attemptCount < 3)
            {
                throw new InvalidOperationException("Orleans grain directory issues");
            }

            return Task.FromResult("success");
        };

        // Act
        var result = await OrleansRetryHelper.ExecuteWithRetryAsync(
            operation,
            maxRetries: 3,
            initialDelay: TimeSpan.FromMilliseconds(10),
            operationName: "BackoffTest");

        // Assert
        Assert.That(result, Is.EqualTo("success"));
        Assert.That(attemptCount, Is.EqualTo(3));
        Assert.That(timings.Count, Is.EqualTo(3));

        // Verify exponential backoff: second attempt should be after ~10ms, third after ~20ms more
        // We allow some tolerance for timing variations
        var firstRetryDelay = timings[1] - timings[0];
        var secondRetryDelay = timings[2] - timings[1];

        Assert.That(firstRetryDelay, Is.GreaterThan(5)); // At least 5ms delay
        Assert.That(secondRetryDelay, Is.GreaterThan(firstRetryDelay)); // Second delay should be longer
    }
}