using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Managements;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class GetAllManagementWhatsappCloudApiBusinessBalances
    : ITrigger<
        GetAllManagementWhatsappCloudApiBusinessBalances.GetAllManagementWhatsappCloudApiBusinessBalancesInput,
        GetAllManagementWhatsappCloudApiBusinessBalances.GetAllManagementWhatsappCloudApiBusinessBalancesOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<GetAllManagementWhatsappCloudApiBusinessBalances> _logger;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;

    public GetAllManagementWhatsappCloudApiBusinessBalances(
        IWabaService wabaService,
        ILogger<GetAllManagementWhatsappCloudApiBusinessBalances> logger,
        IBusinessBalanceService businessBalanceService,
        IWabaLevelCreditManagementService wabaLevelCreditManagementService)
    {
        _logger = logger;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
    }

    public class GetAllManagementWhatsappCloudApiBusinessBalancesInput
    {
    }

    public class GetAllManagementWhatsappCloudApiBusinessBalancesOutput
    {
        [JsonProperty("business_balances")]
        public List<ManagementBusinessBalanceDto> BusinessBalances { get; set; }

        [JsonConstructor]
        public GetAllManagementWhatsappCloudApiBusinessBalancesOutput(
            List<ManagementBusinessBalanceDto> businessBalances)
        {
            BusinessBalances = businessBalances;
        }
    }

    public async Task<GetAllManagementWhatsappCloudApiBusinessBalancesOutput> F(
        GetAllManagementWhatsappCloudApiBusinessBalancesInput input)
    {
        var facebookBusinesses = await _businessBalanceService.GetAllAsync();
        var wabas = await _wabaService.GetAllAsync();

        var facebookBusinessIdToWabasDict =
            wabas
                .Where(w => w.FacebookBusinessId != null)
                .GroupBy(w => w.FacebookBusinessId!)
                .ToDictionary(g => g.Key, g => g.ToList());

        var managementBusinessBalances = new List<ManagementBusinessBalanceDto>();

        foreach (var (facebookBusinessId, relatedWabas) in facebookBusinessIdToWabasDict)
        {
            var businessBalance = facebookBusinesses.FirstOrDefault(x => x.FacebookBusinessId == facebookBusinessId);

            if (businessBalance is not null)
            {
                var unCalculatedCreditTransferTransactionLogs =
                    await _wabaLevelCreditManagementService.GetUnCalculatedCreditTransferTransactionLogsAsync(
                        businessBalance);

                managementBusinessBalances.Add(
                    new ManagementBusinessBalanceDto(businessBalance, relatedWabas, unCalculatedCreditTransferTransactionLogs));
            }
            else
            {
                _logger.LogWarning(
                    "Unable to locate BusinessBalance with {FacebookBusinessId}. {RelatedWabas}",
                    facebookBusinessId,
                    JsonConvert.SerializeObject(relatedWabas));
            }
        }

        return new GetAllManagementWhatsappCloudApiBusinessBalancesOutput(
            managementBusinessBalances);
    }
}