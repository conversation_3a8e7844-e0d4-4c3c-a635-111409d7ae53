using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Triggers.TransactionLogs;

[TriggerGroup(ControllerNames.TransactionLogs)]
public class EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransaction
    : ITrigger<
        EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransaction.
        EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput,
        EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransaction.
        EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutput>
{
    private readonly IBus _bus;

    public EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransaction(IBus bus)
    {
        _bus = bus;
    }

    public class EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput
    {
        [JsonProperty("facebook_business_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookBusinessId { get; set; }

        [JsonConstructor]
        public EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput(string facebookBusinessId)
        {
            FacebookBusinessId = facebookBusinessId;
        }
    }

    public class EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutput
    {
    }

    public async Task<EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutput> F(
        EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput
            enqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput)
    {
        var onCloudApiAccumulateHalfHourConversationUsageTransactionEvent =
            new OnCloudApiAccumulateHalfHourConversationUsageTransactionEvent(
                enqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput.FacebookBusinessId);
        await _bus.Publish(onCloudApiAccumulateHalfHourConversationUsageTransactionEvent);

        return new EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutput();
    }
}