using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.InternalIntegrationHubDb;
#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class InternalIntegrationHubModules
{
    public static void BuildInternalIntegrationHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.Add<PERSON><PERSON>leton<IInternalIntegrationHubDbConfig>(new Mock<IInternalIntegrationHubDbConfig>().Object);
        b.Add<PERSON><PERSON><PERSON><IInternalIntegrationHubDbResolver>(new Mock<IInternalIntegrationHubDbResolver>().Object);

#else
        var internalIntegrationHubDbConfig = new InternalIntegrationHubDbConfig();

        b.Add<PERSON><PERSON><PERSON><IInternalIntegrationHubDbConfig>(internalIntegrationHubDbConfig);
        b.Add<PERSON><PERSON>leton<IInternalIntegrationHubDbResolver, InternalIntegrationHubDbResolver>();
#endif
    }

    public static void BuildInternalIntegrationHubIntegrationDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.Add<PERSON><PERSON>leton<IInternalIntegrationHubDbConfig>(new Mock<IInternalIntegrationHubDbConfig>().Object);
        b.AddSingleton<IInternalIntegrationHubDbResolver>(new Mock<IInternalIntegrationHubDbResolver>().Object);

#else
        var internalIntegrationHubDbConfig = new InternalIntegrationHubDbConfig();

        b.AddSingleton<IInternalIntegrationHubDbConfig>(internalIntegrationHubDbConfig);
        b.AddSingleton<IInternalIntegrationHubDbResolver, InternalIntegrationHubDbResolver>();
#endif
    }
}