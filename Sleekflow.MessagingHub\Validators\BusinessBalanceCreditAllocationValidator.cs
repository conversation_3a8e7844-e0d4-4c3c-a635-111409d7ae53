using System.Globalization;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

namespace Sleekflow.MessagingHub.Validators;

public static class BusinessBalanceCreditAllocationValidator
{
    public static void ValidateCreditAllocation(
        this BusinessBalance businessBalance,
        CreditAllocationObject creditAllocation,
        List<BusinessBalanceTransactionLog> unCalculatedCreditTransferTransactionLogs)
    {
        // Construct the most updated business balance with un-calculated credit transfer transaction logs
        unCalculatedCreditTransferTransactionLogs.ForEach(businessBalance.CalculateTransactionLog);

        var creditTransferType = creditAllocation.CreditTransfers.Select(x => x.CreditTransferType).First();

        switch (creditTransferType)
        {
            case CreditTransferTypes.NormalTransferFromWabaToBusiness:
                foreach (var creditTransfer in creditAllocation.CreditTransfers)
                {
                    var existingWabaBalance = businessBalance.WabaBalances?.Find(
                        wb => wb.FacebookWabaId == creditTransfer.CreditTransferFrom.FacebookWabaId);

                    if (existingWabaBalance is null)
                    {
                        throw new SfNotFoundObjectException(
                            $"Waba balance not found for Facebook Waba Id {creditTransfer.CreditTransferFrom.FacebookWabaId}");
                    }

                    if (existingWabaBalance.Balance.Amount < creditTransfer.CreditTransferAmount.Amount)
                    {
                        throw new SfCreditTransferOutOfCreditException(
                            "facebook_waba",
                            creditTransfer.CreditTransferFrom.FacebookWabaId!,
                            "facebook_business",
                            businessBalance.FacebookBusinessId,
                            existingWabaBalance.Balance.Amount.ToString(CultureInfo.InvariantCulture),
                            creditTransfer.CreditTransferAmount.Amount.ToString(CultureInfo.InvariantCulture));
                    }
                }

                break;

            case CreditTransferTypes.NormalTransferFromBusinessToWaba:
                var totalCreditTransferAmount =
                    creditAllocation.CreditTransfers.Sum(x => x.CreditTransferAmount.Amount);
                var existingUnallocatedCredit = businessBalance.UnallocatedCredit?.Amount;

                if (existingUnallocatedCredit is null)
                {
                    throw new SfInternalErrorException(
                        $"facebook business id {businessBalance.FacebookBusinessId} unallocated credit is null");
                }

                if (existingUnallocatedCredit.Value < totalCreditTransferAmount)
                {
                    throw new SfCreditTransferOutOfCreditException(
                        "facebook_business",
                        businessBalance.FacebookBusinessId,
                        "facebook_wabas",
                        JsonConvert.SerializeObject(
                            creditAllocation.CreditTransfers.Select(x => x.CreditTransferTo.FacebookWabaId)
                                .ToList()),
                        existingUnallocatedCredit.Value.ToString(CultureInfo.InvariantCulture),
                        totalCreditTransferAmount.ToString(CultureInfo.InvariantCulture));
                }

                break;
        }
    }
}