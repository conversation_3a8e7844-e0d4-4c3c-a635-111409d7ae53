using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows.Settings;

namespace Sleekflow.FlowHub.Models.Internals;

public class TriggerScheduleWorkflowInput
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("origin")]
    [Required]
    public string Origin { get; set; }

    [JsonProperty("workflow_id")]
    [Required]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    [Required]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("scheduled_datetime")]
    [Required]
    public DateTimeOffset ScheduledDatetime { get; set; }

    [JsonProperty("recurrent_settings")]
    public WorkflowRecurringSettings? RecurringSettings { get; set; }

    [JsonProperty("workflow_version")]
    public string WorkflowVersion { get; set; }

    [JsonConstructor]
    public TriggerScheduleWorkflowInput(
        string sleekflowCompanyId,
        string origin,
        string workflowId,
        string workflowVersionedId,
        DateTimeOffset scheduledDatetime,
        WorkflowRecurringSettings? recurringSettings,
        string workflowVersion)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Origin = origin;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        ScheduledDatetime = scheduledDatetime;
        RecurringSettings = recurringSettings;
        WorkflowVersion = workflowVersion;
    }
}