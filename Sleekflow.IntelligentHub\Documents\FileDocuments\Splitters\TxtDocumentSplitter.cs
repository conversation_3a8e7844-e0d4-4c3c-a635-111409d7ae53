﻿using System.Text;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters;

public class TxtDocumentSplitter : IDocumentSplitter
{
    public Task<List<(Stream Stream, int StartPage, int EndPage)>> SplitDocumentIntoChunksAsync(
        Stream stream,
        int numberOfContentPerFile)
    {
        var outputStreams = new List<(Stream Stream, int StartPage, int EndPage)>();
        using var reader = new StreamReader(stream);

        Stream currentStream = new MemoryStream();
        var counter = 0;

        for (var line = reader.ReadLine(); line != null; line = reader.ReadLine())
        {
            counter++;

            if (counter == numberOfContentPerFile)
            {
                outputStreams.Add((currentStream, 0, 0));

                // Should not dispose this stream.
                // To be returned to the caller.
                currentStream.Position = 0;
                currentStream = new MemoryStream();
                counter = 0;
            }

            var buffer = Encoding.UTF8.GetBytes(line + "\n");
            currentStream.Write(buffer, 0, buffer.Length);
        }

        if (counter > 0)
        {
            outputStreams.Add((currentStream, 0, 0));
            currentStream.Position = 0;
        }

        return Task.FromResult(outputStreams);
    }
}