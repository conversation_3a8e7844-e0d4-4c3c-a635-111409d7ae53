﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Configs;

public interface IPaymentConfig
{
    string PaymentGatewayRedirectUrl { get; }

    string StripeApiKey { get; }
}

public class PaymentConfig : IConfig, IPaymentConfig
{
    public string PaymentGatewayRedirectUrl { get; }

    public string StripeApiKey { get; }

    public PaymentConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        PaymentGatewayRedirectUrl = Environment.GetEnvironmentVariable("PAYMENT_GATEWAY_REDIRECT_URL", target)
                                    ?? throw new SfMissingEnvironmentVariableException("PAYMENT_GATEWAY_REDIRECT_URL");

        StripeApiKey = Environment.GetEnvironmentVariable("STRIPE_API_KEY", target)
                       ?? throw new SfMissingEnvironmentVariableException("STRIPE_API_KEY");
    }
}