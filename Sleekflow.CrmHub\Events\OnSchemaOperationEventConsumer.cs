﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Sequences;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.Events;

public class OnSchemaOperationEventConsumerDefinition : ConsumerDefinition<OnSchemaOperationEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnSchemaOperationEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnSchemaOperationEventConsumer : IConsumer<OnSchemaOperationEvent>
{
    private readonly ISchemafulObjectService _schemafulObjectService;
    private readonly ISequenceService _sequenceService;
    private readonly ILogger<OnSchemaOperationEventConsumer> _logger;
    private readonly ISchemaRepository _schemaRepository;

    private readonly int _batchSize = 200;
    private readonly TimeSpan _batchDelay = TimeSpan.FromMilliseconds(500);

    public OnSchemaOperationEventConsumer(
        ISchemafulObjectService schemafulObjectService,
        ISequenceService sequenceService,
        ILogger<OnSchemaOperationEventConsumer> logger,
        ISchemaRepository schemaRepository)
    {
        _schemafulObjectService = schemafulObjectService;
        _sequenceService = sequenceService;
        _logger = logger;
        _schemaRepository = schemaRepository;
    }

    public async Task Consume(ConsumeContext<OnSchemaOperationEvent> context)
    {
        var @event = context.Message;

        var retryCount = context.GetRedeliveryCount();
        if (context.GetRedeliveryCount() > 10)
        {
            _logger.LogError(
                "Over the max retry limited {OnSchemaOperationEvent}",
                JsonConvert.SerializeObject(@event));
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            @event.SleekflowCompanyId,
            @event.SchemaId,
            true);

        string? continuationToken = null;
        List<SchemafulObject> schemafulObjects;
        do
        {
            (schemafulObjects, continuationToken) = await _schemafulObjectService.GetContinuationTokenizedSchemafulObjectsAsync(
                queryDefinition,
                continuationToken,
                _batchSize);

            var schemafulObjectIds = schemafulObjects.Select(so => so.Id).ToList();

            switch (@event.Operation)
            {
                case OnSchemaOperationEvent.OperationDeleteSchema:
                    await _schemafulObjectService.DeleteSchemafulObjectsAsync(
                        schemafulObjectIds,
                        @event.SchemaId,
                        @event.SleekflowCompanyId);
                    break;

                case OnSchemaOperationEvent.OperationDeleteSchemaProperties:
                    foreach (var schemafulObjectId in schemafulObjectIds)
                    {
                        await _schemafulObjectService.ProcessDeleteSchemaPropertiesAsync(
                            schemafulObjectId,
                            @event.SchemaId,
                            @event.SleekflowCompanyId,
                            @event.ToBeDeletedPropertyIds!);
                    }

                    break;
                case OnSchemaOperationEvent.OperationDeleteSchemaPropertyOptions:
                    foreach (var schemafulObjectId in schemafulObjectIds)
                    {
                        await _schemafulObjectService.ProcessDeleteSchemaPropertyOptionsAsync(
                            schemafulObjectId,
                            @event.SchemaId,
                            @event.SleekflowCompanyId,
                            @event.ToBeDeletedOptionIdDictionary!);
                    }

                    break;
                case OnSchemaOperationEvent.OperationReindexSchemaPropertyValues:
                    foreach (var schemafulObjectId in schemafulObjectIds)
                    {
                        await _schemafulObjectService.ProcessIndexPropertiesAsync(
                            schemafulObjectId,
                            @event.SchemaId,
                            @event.SleekflowCompanyId,
                            @event.IndexedPropertyIds!);
                    }

                    break;
                default:
                    throw new SfInternalErrorException($"Unhandled schema operation {@event.Operation}");
            }

            await Task.Delay(_batchDelay);
        }
        while (schemafulObjects.Any() && continuationToken != null);

        if (@event.Operation == OnSchemaOperationEvent.OperationDeleteSchema)
        {
            await _sequenceService.DeleteAsync(@event.SchemaId);
            await _schemaRepository.DeleteAsync(@event.SchemaId, @event.SleekflowCompanyId);
        }
    }
}