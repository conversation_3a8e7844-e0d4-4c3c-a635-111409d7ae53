﻿using Microsoft.Extensions.Logging;
using Sleekflow.DependencyInjection;
using StackExchange.Redis;

namespace Sleekflow.Locks;

public partial interface ILockService
{
    Task AcquireReadLockAsync(string[] strings, TimeSpan minimumLockDuration, TimeSpan maximumWaitDuration, CancellationToken cancellationToken = default);

    Task AcquireWriteLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default);

    Task<bool> ReleaseReadLockAsync(string[] strings, CancellationToken cancellationToken = default);

    Task<bool> ReleaseWriteLockAsync(string[] strings, CancellationToken cancellationToken = default);
}

public partial class LockService : ILockService, ISingletonService
{
    public async Task AcquireReadLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        var retryDelayDuration = TimeSpan.FromMilliseconds(100);
        var maxRetryCount = maximumWaitDuration / retryDelayDuration;

        var i = 0;
        while (true)
        {
            if (await TryAcquireReadLockAsync(strings, minimumLockDuration, cancellationToken))
            {
                return;
            }

            if (i++ > maxRetryCount)
            {
                throw new TimeoutException("Unable to acquire a read lock.");
            }

            await Task.Delay(retryDelayDuration, cancellationToken);
        }
    }

    public async Task AcquireWriteLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        var retryDelayDuration = TimeSpan.FromMilliseconds(100);
        var maxRetryCount = maximumWaitDuration / retryDelayDuration;

        var i = 0;
        while (true)
        {
            if (await TryAcquireWriteLockAsync(strings, minimumLockDuration, cancellationToken))
            {
                return;
            }

            if (i++ > maxRetryCount)
            {
                throw new TimeoutException("Unable to acquire a write lock.");
            }

            await Task.Delay(retryDelayDuration, cancellationToken);
        }
    }

    public async Task<bool> ReleaseReadLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        var lockId = GetReadWriteLockId(strings);
        var database = _connectionMultiplexer.GetDatabase();
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("Released the read lock {LockId}.", lockId);
        }

        const string script = @"
            local value = redis.call('GET', @key)
            if value ~= false then
                value = tonumber(value)
                if value < 0 then
                    return false
                elseif value < 2 then
                    redis.call('DEL', @key)
                else
                    redis.call('DECR', @key)
                end
            end
            return true";
        var luaScript = LuaScript.Prepare(script);

        var redisResult = await database.ScriptEvaluateAsync(
            luaScript,
            new
            {
                key = new RedisKey(lockId),
            });

        return (bool) redisResult;
    }

    public async Task<bool> ReleaseWriteLockAsync(string[] strings, CancellationToken cancellationToken = default)
    {
        var lockId = GetReadWriteLockId(strings);
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("Released the write lock {LockId}.", lockId);
        }

        var database = _connectionMultiplexer.GetDatabase();

        var tran = database.CreateTransaction();
        tran.AddCondition(Condition.StringEqual(lockId, "-1"));
#pragma warning disable CS4014
        tran.KeyDeleteAsync(lockId);
#pragma warning restore CS4014
        return await tran.ExecuteAsync();
    }

    private async Task<bool> TryAcquireReadLockAsync(string[] strings, TimeSpan minimumLockDuration, CancellationToken cancellationToken = default)
    {
        var lockId = GetReadWriteLockId(strings);
        var totalSeconds = Math.Max(1, (int) Math.Ceiling(minimumLockDuration.TotalSeconds));
        var database = _connectionMultiplexer.GetDatabase();

        const string script = @"
            local value = redis.call('GET', @key)
            if value ~= false then
                value = tonumber(value)
                if value < 0 then
                    return false
                else
                    local current_ttl = tonumber(redis.call('TTL', @key))
                    redis.call('INCR', @key)
                    redis.call('EXPIRE', @key, math.ceil(current_ttl) + @ttl)
                end
            else
                redis.call('SET', @key, 1)
                redis.call('EXPIRE', @key, @ttl)
            end
            return true";
        var luaScript = LuaScript.Prepare(script);

        var redisResult = await database.ScriptEvaluateAsync(
            luaScript,
            new
            {
                key = new RedisKey(lockId),
                ttl = totalSeconds
            });

        if (_logger.IsEnabled(LogLevel.Debug))
        {
            var currentTtl = await database.KeyTimeToLiveAsync(lockId);
            var currentCount = await database.StringGetAsync(lockId);
            _logger.LogDebug("Current count: {Count}, TTL: {TTL}.", (int) currentCount, currentTtl?.TotalSeconds ?? 0);
        }

        return (bool) redisResult;
    }

    private async Task<bool> TryAcquireWriteLockAsync(string[] strings, TimeSpan minimumLockDuration, CancellationToken cancellationToken = default)
    {
        var lockId = GetReadWriteLockId(strings);
        var totalSeconds = Math.Max(1, (int) Math.Ceiling(minimumLockDuration.TotalSeconds));
        var database = _connectionMultiplexer.GetDatabase();

        return await _connectionMultiplexer.GetDatabase().StringSetAsync(
            lockId,
            -1,
            minimumLockDuration,
            When.NotExists);
    }

    private string GetReadWriteLockId(string[] strings)
    {
        return "ReadWriteLock-" + string.Join("-", strings);
    }
}