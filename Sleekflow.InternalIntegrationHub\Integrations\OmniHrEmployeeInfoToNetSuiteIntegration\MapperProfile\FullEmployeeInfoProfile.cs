using AutoMapper;
using Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile.TypeConverter;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Integrations;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;
using EmployeeDetailsResponse = Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.EmployeeDetailsResponse;

namespace Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile;

public class FullEmployeeInfoProfile : Profile
{
#pragma warning disable JA1002
    public FullEmployeeInfoProfile()
#pragma warning restore JA1002
    {
        CreateMap<string, OmniHrEmail>().ConvertUsing(new PrimaryEmailTypeConverter());
        CreateMap<EmployeeDetailsResponse, FullEmployeeInfo>()
            .ForMember(f => f.ExternalId, opt => opt.MapFrom(src => src.ExternalId))
            .ForMember(f => f.FirstName, opt => opt.MapFrom(src => src.FirstName))
            .ForMember(f => f.LastName, opt => opt.MapFrom(src => src.LastName))
            .ForMember(f => f.PrimaryEmail, opt => opt.MapFrom(src => src.Email))
            .ForMember(f => f.LocationName, opt => opt.MapFrom(src => src.Subsidiary!.RefName))
            .ForMember(f => f.Currency, opt => opt.MapFrom(src => src.Defaultexpensereportcurrency!.Id));
    }
}