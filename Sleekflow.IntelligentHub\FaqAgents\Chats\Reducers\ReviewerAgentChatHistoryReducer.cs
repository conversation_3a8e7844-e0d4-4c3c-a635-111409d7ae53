using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;

public class ReviewerAgentChatHistoryReducer : IChatHistoryReducer
{
    public Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        var reducedHistory = chatHistory
            .Where((h, i) =>
                (h is { AuthorName: LongAgentDefinitions.SalesAgentName, Content: not null }
                 && h.Content.Contains("PROPOSED_REPLY_TO_CUSTOMER"))
                || h.AuthorName == LongAgentDefinitions.ReviewerAgentName
                || h.Role == AuthorRole.System)
            .ToList();
        return Task.FromResult<IEnumerable<ChatMessageContent>?>(reducedHistory);
    }
}