namespace Sleekflow.FlowHub.Models.Constants;

public static class ContainerNames
{
    public const string DatabaseId = "flowhubdb";
    public const string FlowHubIntegrationDatabaseId = "flowhubintegrationdb";

    public const string State = "state";
    public const string StateSubscription = "state_subscription";

    public const string Workflow = "workflow";
    public const string WorkflowGroup = "workflow_group";
    public const string WorkflowExecution = "workflow_execution";
    public const string WorkflowWebhookTrigger = "workflow_webhook_trigger";

    public const string WorkflowAgentConfigMapping = "workflow_agent_config_mapping";

    public const string StepExecution = "step_execution";

    public const string FlowHubEvent = "flow_hub_event";
    public const string FlowHubConfig = "flow_hub_config";

    public const string ZapierTriggerIntegration = "zapier_trigger_integration";

    public const string WorkflowStepCategoryStatistics = "workflow_step_category_statistics";
    public const string StateStepCategoryStatistics = "state_step_category_statistics";

    public const string EmailSentRecord = "email_sent_record";
    public const string AggregateSagaInstances = "aggregate_saga_instances";
    public const string StreamingRecommendedReplySagaInstances = "streaming_recommended_reply_saga_instances";
}