﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class GetSchemafulObject : ITrigger<GetSchemafulObject.GetSchemafulObjectInput, GetSchemafulObject.GetSchemafulObjectOutput>
{
    private readonly ISchemafulObjectService _schemafulObjectService;

    public GetSchemafulObject(ISchemafulObjectService schemafulObjectService)
    {
        _schemafulObjectService = schemafulObjectService;
    }

    public class GetSchemafulObjectInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(Entity.PropertyNameId)]
        public string Id { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectInput(string sleekflowCompanyId, string schemaId, string id)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SchemaId = schemaId;
            Id = id;
        }
    }

    public class GetSchemafulObjectOutput
    {
        [JsonProperty("schemaful_object")]
        public SchemafulObjectDto SchemafulObject { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectOutput(SchemafulObjectDto schemafulObject)
        {
            SchemafulObject = schemafulObject;
        }
    }

    public async Task<GetSchemafulObjectOutput> F(GetSchemafulObjectInput getSchemafulObjectInput)
    {
        return new GetSchemafulObjectOutput(
            new SchemafulObjectDto(
                await _schemafulObjectService.GetSchemafulObjectAsync(
                    getSchemafulObjectInput.Id,
                    getSchemafulObjectInput.SchemaId,
                    getSchemafulObjectInput.SleekflowCompanyId)));
    }
}