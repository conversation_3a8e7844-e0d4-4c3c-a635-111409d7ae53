using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;

namespace Sleekflow.CommerceHub.Models.LineItems;

public class CalculatedLineItemAppliedDiscount
{
    [JsonProperty("level")]
    public string Level { get; set; }

    [JsonProperty("applied_discount")]
    public Discount? AppliedDiscount { get; set; }

    [JsonProperty("pre_calculated_amount")]
    public decimal PreCalculatedAmount { get; set; }

    [JsonProperty("post_calculated_amount")]
    public decimal PostCalculatedAmount { get; set; }

    [JsonConstructor]
    public CalculatedLineItemAppliedDiscount(
        string level,
        Discount? appliedDiscount,
        decimal preCalculatedAmount,
        decimal postCalculatedAmount)
    {
        Level = level;
        AppliedDiscount = appliedDiscount;
        PreCalculatedAmount = preCalculatedAmount;
        PostCalculatedAmount = postCalculatedAmount;
    }
}