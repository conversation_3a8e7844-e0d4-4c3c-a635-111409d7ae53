﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class PreviewObjects : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public PreviewObjects(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class PreviewObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filters")]
        [Required]
        public List<SyncConfigFilter> Filters { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public PreviewObjectsInput(
            string sleekflowCompanyId,
            string providerName,
            string entityTypeName,
            List<SyncConfigFilter> filters,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            Filters = filters;
            FieldFilters = fieldFilters;
        }
    }

    public class PreviewObjectsOutput
    {
        [JsonProperty("records")]
        public List<Dictionary<string, object?>> Records { get; set; }

        [JsonConstructor]
        public PreviewObjectsOutput(
            List<Dictionary<string, object?>> records)
        {
            Records = records;
        }
    }

    public async Task<PreviewObjectsOutput> F(
        PreviewObjectsInput previewObjectsInput)
    {
        var sleekflowCompanyId = previewObjectsInput.SleekflowCompanyId;
        var providerName = previewObjectsInput.ProviderName;
        var entityTypeName = previewObjectsInput.EntityTypeName;

        var providerService = _providerSelector.GetProviderService(providerName);

        var previewObjectsOutput = await providerService.PreviewObjectsAsync(
            sleekflowCompanyId,
            entityTypeName,
            new List<SyncConfigFilterGroup>()
            {
                new SyncConfigFilterGroup(previewObjectsInput.Filters)
            },
            previewObjectsInput.FieldFilters);

        return new PreviewObjectsOutput(
            previewObjectsOutput.Objects);
    }
}