using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

public class Link
{
    [JsonProperty("rel", NullValueHandling = NullValueHandling.Ignore)]
    public string Rel { get; set; }

    [JsonProperty("href", NullValueHandling = NullValueHandling.Ignore)]
    public string Href { get; set; }

    [JsonConstructor]
    public Link(
        string rel,
        string href)
    {
        Rel = rel;
        Href = href;
    }
}