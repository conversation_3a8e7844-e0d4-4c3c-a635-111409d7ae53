﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.Ids;
using Sleekflow.Models.Events;

namespace Sleekflow.AuditHub.Consumers;

public class OnUserProfileEnrolledIntoFlowHubWorkflowEventConsumerDefinition
    : ConsumerDefinition<OnUserProfileEnrolledIntoFlowHubWorkflowEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnUserProfileEnrolledIntoFlowHubWorkflowEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(1);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Interval(6, TimeSpan.FromSeconds(30)));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnUserProfileEnrolledIntoFlowHubWorkflowEventConsumer : IConsumer<OnUserProfileEnrolledIntoFlowHubWorkflowEvent>
{
    private readonly IIdService _idService;
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;

    public OnUserProfileEnrolledIntoFlowHubWorkflowEventConsumer(
        IIdService idService,
        IUserProfileAuditLogService userProfileAuditLogService)
    {
        _idService = idService;
        _userProfileAuditLogService = userProfileAuditLogService;
    }

    public async Task Consume(ConsumeContext<OnUserProfileEnrolledIntoFlowHubWorkflowEvent> context)
    {
        var @event = context.Message;

        var dataStr = JsonConvert.SerializeObject(
            new UserProfileEnrolledIntoFlowHubWorkflowLogData(
                @event.WorkflowId,
                @event.WorkflowVersionedId,
                @event.WorkflowName,
                @event.StateId));

        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var id = _idService.GetId("UserProfileAuditLog");
        var auditLog = new UserProfileAuditLog(
            id,
            @event.SleekflowCompanyId,
            null,
            @event.SleekflowUserProfileId,
            UserProfileAuditLogTypes.UserProfileEnrolledIntoFlowHubWorkflow,
            $"Enrolled into flow {@event.WorkflowName} (Flow ID: {@event.WorkflowId}, State ID: {@event.StateId})",
            data,
            DateTimeOffset.UtcNow,
            null);

        await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(auditLog);
    }
}