using Newtonsoft.Json;

namespace Sleekflow.Auth0.BulkImporter.Models;

public class Auth0JobErrorDetailsResponse
{
    [JsonProperty("user")]
    public ImportUser? User { get; set; }

    [JsonProperty("errors")]
    public List<Auth0ImportErrorDetails?>? Errors { get; set; }
}

public class Auth0ImportErrorDetails
{
    [JsonProperty("code")]
    public string? Code { get; set; }

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("path", NullValueHandling = NullValueHandling.Ignore)]
    public string? Path { get; set; }
}

public class ImportUser
{
    [JsonProperty("user_id")]
    public string? UserId { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }

    [JsonProperty("email_verified")]
    public bool? EmailVerified { get; set; }

    [JsonProperty("name")]
    public string? DisplayName { get; set; }

    [JsonProperty("username")]
    public string? UserName { get; set; }

    [JsonProperty("given_name")]
    public string? FirstName { get; set; }

    [JsonProperty("family_name")]
    public string? LastName { get; set; }

    [JsonProperty("picture")]
    public string? Picture { get; set; }

    [JsonProperty("custom_password_hash", NullValueHandling = NullValueHandling.Ignore)]
    public CustomPasswordHash? CustomPasswordHash { get; set; }

    [JsonProperty("password_hash", NullValueHandling = NullValueHandling.Ignore)]
    public string? PasswordHash { get; set; }

    [JsonProperty("user_metadata", NullValueHandling = NullValueHandling.Ignore)]
    public dynamic? UserMetadata { get; set; }

    [JsonProperty("app_metadata")]
    public MetaData? AppMetadata { get; set; }
}

public class CustomPasswordHash
{
    [JsonProperty("algorithm")]
    public string? Algorithm { get; set; }

    [JsonProperty("hash")]
    public Hash? Hash { get; set; }
}

public class Hash
{
    [JsonProperty("value")]
    public string? Value { get; set; }

    [JsonProperty("encoding", NullValueHandling = NullValueHandling.Ignore)]
    public string? Encoding { get; set; }
}

public class MetaData
{
    [JsonProperty("roles")]
    public List<string?>? Roles { get; set; }

    [JsonProperty("phone_number")]
    public string? PhoneNumber { get; set; }

    [JsonProperty("sleekflow_id")]
    public string? SleekflowId { get; set; }
}