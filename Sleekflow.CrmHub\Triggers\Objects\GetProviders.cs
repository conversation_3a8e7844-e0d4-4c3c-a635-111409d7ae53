﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetProviders : ITrigger
{
    public class GetProvidersInput
    {
    }

    public class Provider
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("capability_matrix")]
        public Dictionary<string, bool> CapabilityMatrix { get; set; }

        [JsonConstructor]
        public Provider(
            string name,
            Dictionary<string, bool> capabilityMatrix)
        {
            Name = name;
            CapabilityMatrix = capabilityMatrix;
        }
    }

    public class GetProvidersOutput
    {
        [JsonProperty("providers")]
        public List<Provider> Providers { get; set; }

        [JsonConstructor]
        public GetProvidersOutput(List<Provider> providers)
        {
            Providers = providers;
        }
    }

    public Task<GetProvidersOutput> F(GetProvidersInput getProvidersInput)
    {
        return Task.FromResult(
            new GetProvidersOutput(
                new List<Provider>
                {
                    new Provider(
                        "salesforce-integrator",
                        new Dictionary<string, bool>
                        {
                            {
                                "GetProviderTypeFields", true
                            },
                            {
                                "GetProviderSupportedTypes", true
                            },
                            {
                                "InitProvider", true
                            },
                            {
                                "InitProviderTypesSync", true
                            },
                            {
                                "PropagateObjectUpdateToProvider", true
                            },
                            {
                                "TriggerProviderSyncObject", true
                            },
                            {
                                "TriggerProviderSyncObjects", true
                            },
                        }),
                    new Provider(
                        "hubspot-integrator",
                        new Dictionary<string, bool>
                        {
                            {
                                "GetProviderTypeFields", true
                            },
                            {
                                "GetProviderSupportedTypes", true
                            },
                            {
                                "InitProvider", true
                            },
                            {
                                "InitProviderTypesSync", true
                            },
                            {
                                "PropagateObjectUpdateToProvider", true
                            },
                            {
                                "TriggerProviderSyncObject", true
                            },
                            {
                                "TriggerProviderSyncObjects", true
                            },
                        }),
                    new Provider(
                        "sleekflow",
                        new Dictionary<string, bool>
                        {
                            {
                                "GetProviderTypeFields", false
                            },
                            {
                                "GetProviderSupportedTypes", false
                            },
                            {
                                "InitProvider", false
                            },
                            {
                                "InitProviderTypesSync", false
                            },
                            {
                                "PropagateObjectUpdateToProvider", false
                            },
                            {
                                "TriggerProviderSyncObject", false
                            },
                            {
                                "TriggerProviderSyncObjects", false
                            },
                        })
                }));
    }
}