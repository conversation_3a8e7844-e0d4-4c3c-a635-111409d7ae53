using MassTransit.Internals;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.Ids;
using Sleekflow.RateLimits.LuaScripts;

namespace Sleekflow.FlowHub.FlowHubEvents;

public interface IFlowHubEventRateLimitProducer
{
    Task PublishWithRateLimitAsync<T>(
        T @event,
        string keyName,
        SlidingWindowParam? slidingWindowParam = null,
        CancellationToken cancellationToken = default)
        where T : class;
}

public class FlowHubEventRateLimitProducer : ISingletonService, IFlowHubEventRateLimitProducer
{
    private readonly ISlidingWindowNextAvailableSlotRateLimiter _slidingWindowNextAvailableSlotRateLimiter;
    private readonly IIdService _idService;
    private readonly IHostEnvironment _env;

    public FlowHubEventRateLimitProducer(
        ISlidingWindowNextAvailableSlotRateLimiter slidingWindowNextAvailableSlotRateLimiter,
        IIdService idService,
        IHostEnvironment env)
    {
        _slidingWindowNextAvailableSlotRateLimiter = slidingWindowNextAvailableSlotRateLimiter;
        _idService = idService;
        _env = env;
    }

    private readonly Dictionary<string, SlidingWindowParam> _slidingWindowParamsDev =
        new ()
        {
            {
                typeof(OnTriggerEventRequestedEvent).GetTypeName(), new SlidingWindowParam(1, 50)
            }
        };

    private readonly Dictionary<string, SlidingWindowParam> _slidingWindowParamsStag =
        new ()
        {
            {
                typeof(OnTriggerEventRequestedEvent).GetTypeName(), new SlidingWindowParam(1, 100)
            }
        };

    private readonly Dictionary<string, SlidingWindowParam> _slidingWindowParamsProd =
        new ()
        {
            {
                typeof(OnTriggerEventRequestedEvent).GetTypeName(), new SlidingWindowParam(1, 20000)
            }
        };

    public async Task PublishWithRateLimitAsync<T>(
        T @event,
        string keyName,
        SlidingWindowParam? slidingWindowParam = null,
        CancellationToken cancellationToken = default)
        where T : class
    {
        var eventTypeName = typeof(T).GetTypeName();
        if (slidingWindowParam == null)
        {
            if (_env.IsDevelopment())
            {
                _slidingWindowParamsDev.TryGetValue(eventTypeName, out slidingWindowParam);
            }
            else if (_env.IsStaging())
            {
                _slidingWindowParamsStag.TryGetValue(eventTypeName, out slidingWindowParam);
            }
            else
            {
                _slidingWindowParamsProd.TryGetValue(eventTypeName, out slidingWindowParam);
            }
        }

        await _slidingWindowNextAvailableSlotRateLimiter.PublishWithRateLimitAsync(
            @event,
            keyName,
            new SlidingWindowNextAvailableSlotParam(
                slidingWindowParam,
                DateTimeOffset.Now,
                _idService.GetId(eventTypeName)),
            cancellationToken);
    }
}