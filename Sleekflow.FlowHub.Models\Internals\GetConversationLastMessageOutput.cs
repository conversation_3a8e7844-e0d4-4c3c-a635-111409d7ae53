using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetConversationLastMessagesOutput
{
    [JsonProperty("conversation_messages")]
    public List<ConversationMessageOutput> ConversationMessages { get; set; }

    [JsonConstructor]
    public GetConversationLastMessagesOutput(List<ConversationMessageOutput> conversationMessages)
    {
        ConversationMessages = conversationMessages;
    }

    public class ConversationMessageOutputFileDto
    {

        [JsonProperty("url")]
        public string Url { get; set; }

        [JsonProperty("mime_type")]
        public string MimeType { get; set; }

        [JsonProperty("file_size")]
        public long FileSize { get; set; }

        [JsonConstructor]
        public ConversationMessageOutputFileDto(string url, string mimeType, long fileSize)
        {
            Url = url;
            MimeType = mimeType;
            FileSize = fileSize;
        }
    }

    public class ConversationMessageOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("message_unique_id")]
        public string MessageUniqueId { get; set; }

        [JsonProperty("message_content")]
        public string MessageContent { get; set; }

        [JsonProperty("is_sent_from_sleekflow")]
        public bool IsSentFromSleekflow { get; set; }

        [JsonProperty("created_at")]
        public DateTimeOffset? CreatedAt { get; set; }

        [JsonProperty("files")]
        public List<ConversationMessageOutputFileDto>? Files { get; set; }

        [JsonConstructor]
        public ConversationMessageOutput(
            string id,
            string messageUniqueId,
            string messageContent,
            bool isSentFromSleekflow,
            DateTimeOffset? createdAt,
            List<ConversationMessageOutputFileDto>? files)
        {
            Id = id;
            MessageUniqueId = messageUniqueId;
            MessageContent = messageContent;
            IsSentFromSleekflow = isSentFromSleekflow;
            CreatedAt = createdAt;
            Files = files;
        }
    }
}