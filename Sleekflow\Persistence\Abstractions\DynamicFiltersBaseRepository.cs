﻿using System.Linq.Expressions;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.Logging;
using Sleekflow.Constants;
using Sleekflow.Exceptions;
using Sleekflow.Expressions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.Persistence.Abstractions;

public interface IDynamicFiltersRepository<TObj> : IRepository<TObj>
{
    bool IsValidQueryDefinition(QueryDefinition queryDefinition, string propertyName, string value);
}

public abstract class DynamicFiltersBaseRepository<TObj>
    : BaseRepository<TObj>, IDynamicFiltersRepository<TObj>
{
    private readonly IDynamicFiltersRepositoryContext _dynamicFiltersRepositoryContext;

    protected DynamicFiltersBaseRepository(
        ILogger<DynamicFiltersBaseRepository<TObj>> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider)
    {
        _dynamicFiltersRepositoryContext = dynamicFiltersRepositoryContext;
    }

    public override IAsyncEnumerable<TObj> GetObjectEnumerableAsync(
        QueryDefinition queryDefinition,
        CancellationToken cancellationToken = default)
    {
        if (IsSoftDeleteEnabled()
            && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null
            && !queryDefinition.QueryText.Contains(IHasRecordStatuses.PropertyNameRecordStatuses))
        {
            throw new SfQueryException(
                IHasRecordStatuses.PropertyNameRecordStatuses + " is required",
                nameof(GetObjectEnumerableAsync));
        }

        if (IsSleekflowCompanyIdFilterEnabled()
            && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null
            && !IsValidQueryDefinition(
                queryDefinition,
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                _dynamicFiltersRepositoryContext.SleekflowCompanyId!))
        {
            throw new SfQueryException(
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId + " is required",
                nameof(GetObjectEnumerableAsync));
        }

        return base.GetObjectEnumerableAsync(queryDefinition, cancellationToken);
    }

    public override IAsyncEnumerable<TObj> GetObjectEnumerableAsync(
        Expression<Func<TObj, bool>> predicate,
        CancellationToken cancellationToken = default)
    {
        return base.GetObjectEnumerableAsync(
            predicate
                .IfAndAlso(
                    () => IsSoftDeleteEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null,
                    obj => ((IHasRecordStatuses) obj!).RecordStatuses.Any(rs => rs == RecordStatuses.Active))
                .IfAndAlso(
                    () => IsSleekflowCompanyIdFilterEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null,
                    obj => ((IHasSleekflowCompanyId) obj!).SleekflowCompanyId
                           == _dynamicFiltersRepositoryContext.SleekflowCompanyId),
            cancellationToken);
    }

    public override Task<List<TObj>> GetObjectsAsync(
        QueryDefinition queryDefinition,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        if (IsSoftDeleteEnabled()
            && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null
            && !queryDefinition.QueryText.Contains(IHasRecordStatuses.PropertyNameRecordStatuses))
        {
            throw new SfQueryException(
                IHasRecordStatuses.PropertyNameRecordStatuses + " is required",
                nameof(GetObjectsAsync));
        }

        if (IsSleekflowCompanyIdFilterEnabled()
            && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null
            && !IsValidQueryDefinition(
                queryDefinition,
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                _dynamicFiltersRepositoryContext.SleekflowCompanyId!))
        {
            throw new SfQueryException(
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId + " is required",
                nameof(GetObjectsAsync));
        }

        return base.GetObjectsAsync(queryDefinition, limit, cancellationToken);
    }

    public override Task<List<T>> GetObjectsAsync<T>(
        QueryDefinition queryDefinition,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        return base.GetObjectsAsync<T>(queryDefinition, limit, cancellationToken);
    }

    public override Task<List<TObj>> GetObjectsAsync(
        Expression<Func<TObj, bool>> predicate,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        return base.GetObjectsAsync(
            predicate
                .IfAndAlso(
                    () => IsSoftDeleteEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null,
                    obj => ((IHasRecordStatuses) obj!).RecordStatuses.Any(rs => rs == RecordStatuses.Active))
                .IfAndAlso(
                    () => IsSleekflowCompanyIdFilterEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null,
                    obj => ((IHasSleekflowCompanyId) obj!).SleekflowCompanyId
                           == _dynamicFiltersRepositoryContext.SleekflowCompanyId),
            limit,
            cancellationToken);
    }

    public override Task<List<TObj>> GetObjectsAsync<TOrderByKey>(
        Expression<Func<TObj, bool>> predicate,
        Expression<Func<TObj, TOrderByKey>> orderBy,
        bool orderByAscending,
        int? limit = 10000,
        CancellationToken cancellationToken = default)
    {
        return base.GetObjectsAsync(
            predicate
                .IfAndAlso(
                    () => IsSoftDeleteEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null,
                    obj => ((IHasRecordStatuses) obj!).RecordStatuses.Any(rs => rs == RecordStatuses.Active))
                .IfAndAlso(
                    () => IsSleekflowCompanyIdFilterEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null,
                    obj => ((IHasSleekflowCompanyId) obj!).SleekflowCompanyId
                           == _dynamicFiltersRepositoryContext.SleekflowCompanyId),
            orderBy,
            orderByAscending,
            limit,
            cancellationToken);
    }

    public override Task<(List<TObj> Objs, string? NextContinuationToken)> GetContinuationTokenizedObjectsAsync(
        QueryDefinition queryDefinition,
        string? encryptedContinuationToken,
        int limit,
        CancellationToken cancellationToken = default)
    {
        if (IsSoftDeleteEnabled()
            && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null
            && !queryDefinition.QueryText.Contains(IHasRecordStatuses.PropertyNameRecordStatuses))
        {
            throw new SfQueryException(
                IHasRecordStatuses.PropertyNameRecordStatuses + " is required",
                nameof(GetContinuationTokenizedObjectsAsync));
        }

        if (IsSleekflowCompanyIdFilterEnabled()
            && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null
            && !IsValidQueryDefinition(
                queryDefinition,
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                _dynamicFiltersRepositoryContext.SleekflowCompanyId!))
        {
            throw new SfQueryException(
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId + " is required",
                nameof(GetContinuationTokenizedObjectsAsync));
        }

        return base.GetContinuationTokenizedObjectsAsync(queryDefinition, encryptedContinuationToken, limit, cancellationToken);
    }

    public override Task<(List<TObj> Objs, string? NextContinuationToken)> GetContinuationTokenizedObjectsAsync(
        Expression<Func<TObj, bool>> predicate,
        string? encryptedContinuationToken,
        int limit,
        CancellationToken cancellationToken = default)
    {
        return base.GetContinuationTokenizedObjectsAsync(
            predicate
                .IfAndAlso(
                    () => IsSoftDeleteEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null,
                    obj => ((IHasRecordStatuses) obj!).RecordStatuses.Any(rs => rs == RecordStatuses.Active))
                .IfAndAlso(
                    () => IsSleekflowCompanyIdFilterEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null,
                    obj =>
                        ((IHasSleekflowCompanyId) obj!).SleekflowCompanyId
                        == _dynamicFiltersRepositoryContext.SleekflowCompanyId),
            encryptedContinuationToken,
            limit,
            cancellationToken);
    }

    public override Task<(List<TObj> Objs, string? NextContinuationToken)>
        GetContinuationTokenizedObjectsAsync<TOrderByKey>(
            Expression<Func<TObj, bool>> predicate,
            Expression<Func<TObj, TOrderByKey>> orderBy,
            bool orderByAscending,
            string? continuationToken,
            int limit,
            CancellationToken cancellationToken = default)
    {
        return base.GetContinuationTokenizedObjectsAsync(
            predicate
                .IfAndAlso(
                    () => IsSoftDeleteEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasRecordStatuses)) != null,
                    obj => ((IHasRecordStatuses) obj!).RecordStatuses.Any(rs => rs == RecordStatuses.Active))
                .IfAndAlso(
                    () => IsSleekflowCompanyIdFilterEnabled()
                          && typeof(TObj).GetInterface(nameof(IHasSleekflowCompanyId)) != null,
                    obj =>
                        ((IHasSleekflowCompanyId) obj!).SleekflowCompanyId
                        == _dynamicFiltersRepositoryContext.SleekflowCompanyId),
            orderBy,
            orderByAscending,
            continuationToken,
            limit,
            cancellationToken);
    }

    public override Task<TObj> GetAsync(
        string id,
        string partitionKey,
        CancellationToken cancellationToken = default)
    {
        return GetAsync(id, new PartitionKey(partitionKey), cancellationToken);
    }

    public override async Task<TObj> GetAsync(
        string id,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default)
    {
        var obj = await base.GetAsync(id, partitionKey, cancellationToken);

        if (IsSleekflowCompanyIdFilterEnabled()
            && obj is IHasSleekflowCompanyId hasSleekflowCompanyId
            && hasSleekflowCompanyId.SleekflowCompanyId != _dynamicFiltersRepositoryContext.SleekflowCompanyId)
        {
            throw new SfNotFoundObjectException(id, partitionKey);
        }

        if (IsSoftDeleteEnabled()
            && obj is IHasRecordStatuses hasRecordStatuses
            && hasRecordStatuses.RecordStatuses.Any(rs => rs == RecordStatuses.Deleted))
        {
            throw new SfNotFoundObjectException(id, partitionKey);
        }

        return obj;
    }

    public override Task<TObj?> GetOrDefaultAsync(
        string id,
        string partitionKey,
        CancellationToken cancellationToken = default)
    {
        return GetOrDefaultAsync(id, new PartitionKey(partitionKey), cancellationToken);
    }

    public override async Task<TObj?> GetOrDefaultAsync(
        string id,
        PartitionKey partitionKey,
        CancellationToken cancellationToken = default)
    {
        var obj = await base.GetOrDefaultAsync(id, partitionKey, cancellationToken);
        if (obj == null)
        {
            return obj;
        }

        if (IsSleekflowCompanyIdFilterEnabled()
            && obj is IHasSleekflowCompanyId hasSleekflowCompanyId
            && hasSleekflowCompanyId.SleekflowCompanyId != _dynamicFiltersRepositoryContext.SleekflowCompanyId)
        {
            return default;
        }

        if (IsSoftDeleteEnabled()
            && obj is IHasRecordStatuses hasRecordStatuses
            && hasRecordStatuses.RecordStatuses.Any(rs => rs == RecordStatuses.Deleted))
        {
            return default;
        }

        return obj;
    }

    public override Task<int> DeleteAsync(
        string id,
        string partitionKey,
        string? eTag = null,
        TransactionalBatch? transactionalBatch = null,
        CancellationToken cancellationToken = default)
    {
        return DeleteAsync(id, new PartitionKey(partitionKey), eTag, transactionalBatch, cancellationToken);
    }

    public override async Task<int> DeleteAsync(
        string id,
        PartitionKey partitionKey,
        string? eTag = null,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default)
    {
        var obj = await GetAsync(id, partitionKey, cancellationToken);

        if (IsSoftDeleteEnabled()
            && obj is IHasRecordStatuses hasRecordStatuses)
        {
            var recordStatuses = new List<string>(hasRecordStatuses.RecordStatuses);
            recordStatuses.Remove(RecordStatuses.Active);
            recordStatuses.Add(RecordStatuses.Deleted);

            return await PatchAsync(
                id,
                partitionKey,
                new List<PatchOperation>
                {
                    Set(IHasRecordStatuses.PropertyNameRecordStatuses, recordStatuses)
                },
                eTag: eTag,
                cancellationToken: cancellationToken);
        }

        return await base.DeleteAsync(id, partitionKey, eTag, transactionalBatch, cancellationToken);
    }

    public override Task<int> DeleteAsync(
        List<string> ids,
        string partitionKey,
        TransactionalBatch? transactionalBatch = null,
        CancellationToken cancellationToken = default)
    {
        return DeleteAsync(ids, new PartitionKey(partitionKey), transactionalBatch, cancellationToken);
    }

    public override async Task<int> DeleteAsync(
        List<string> ids,
        PartitionKey partitionKey,
        TransactionalBatch? transactionalBatch = default,
        CancellationToken cancellationToken = default)
    {
        var deleteCount = 0;

        foreach (var id in ids)
        {
            if (await DeleteAsync(id, partitionKey, null, transactionalBatch, cancellationToken) == 1)
            {
                deleteCount += 1;
            }
        }

        return deleteCount;
    }

    private bool IsSoftDeleteEnabled()
    {
        return _dynamicFiltersRepositoryContext.IsSoftDeleteEnabled;
    }

    private bool IsSleekflowCompanyIdFilterEnabled()
    {
        return _dynamicFiltersRepositoryContext.SleekflowCompanyId != null;
    }

    private sealed record QueryParameter(string Name, object Value)
    {
        public string Name { get; } = Name;

        public object Value { get; } = Value;
    }

    /// <summary>
    /// Make sure the queryDefinition containing the required dynamic filter.
    /// </summary>
    /// <param name="queryDefinition">Query Definition.</param>
    /// <param name="propertyName">Property Name.</param>
    /// <param name="value">Value.</param>
    /// <returns>Is Valid.</returns>
    public bool IsValidQueryDefinition(QueryDefinition queryDefinition, string propertyName, string value)
    {
        var queryParameters = queryDefinition.GetQueryParameters();
        if (queryParameters.Count == 0)
        {
            return queryDefinition.QueryText.Contains(propertyName)
                   && queryDefinition.QueryText.Contains(value);
        }

        var queryParameter = queryParameters
            .Select(qp => new QueryParameter(qp.Name, qp.Value))
            .Where(qp => qp.Value is string)
            .FirstOrDefault(p => (string) p.Value == value);
        if (queryParameter == null)
        {
            return false;
        }

        if (!queryDefinition.QueryText.Contains(queryParameter.Name))
        {
            return false;
        }

        if (!queryDefinition.QueryText.Contains(propertyName))
        {
            return false;
        }

        return true;
    }
}