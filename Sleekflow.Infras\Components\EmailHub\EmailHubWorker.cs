using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using AppConfiguration = Pulumi.AzureNative.AppConfiguration;
using Insights = Pulumi.AzureNative.Insights;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.EmailHub;

public class EmailHubWorker
{
    private readonly ResourceGroup _resourceGroup;
    private readonly EmailHubDb.EmailHubDbOutput _emailHubDbOutput;
    private readonly AppConfiguration.ConfigurationStore _appConfig;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly GcpConfig _gcpConfig;

    public EmailHubWorker(
        ResourceGroup resourceGroup,
        EmailHubDb.EmailHubDbOutput emailHubDbOutput,
        AppConfiguration.ConfigurationStore appConfig,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        GcpConfig gcpConfig)
    {
        _resourceGroup = resourceGroup;
        _emailHubDbOutput = emailHubDbOutput;
        _appConfig = appConfig;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _gcpConfig = gcpConfig;
    }

    public Dictionary<string, Web.WebApp> InitWorker()
    {
        var apps = new Dictionary<string, Web.WebApp>();

        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.EmailHub))
            {
                continue;
            }

            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventHub = managedEnvAndAppsTuple.EventHub;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            // Primary,Secondary,Primary Read Only,Secondary Read Only
            var primaryAppConfigReadOnlyConnStr = Output
                .Tuple(_resourceGroup.Name, _appConfig.Name)
                .Apply(
                    items => AppConfiguration.ListConfigurationStoreKeys.Invoke(
                        new AppConfiguration.ListConfigurationStoreKeysInvokeArgs
                        {
                            ResourceGroupName = items.Item1, ConfigStoreName = items.Item2
                        }))
                .Apply(o => o.Value.First(v => v.Name == "Primary Read Only").ConnectionString);

            var randomId = new Random.RandomId(
                "sleekflow-eh-worker-storage-account-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "eh-worker", "eh-value"
                        }
                    },
                });
            var storageAccount = new Storage.StorageAccount(
                "sleekflow-eh-worker-storage-account",
                new Storage.StorageAccountArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new Storage.Inputs.SkuArgs
                    {
                        Name = Storage.SkuName.Standard_LRS,
                    },
                    Kind = Storage.Kind.StorageV2,
                    AccountName = randomId.Hex.Apply(h => "s" + h)
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });
            var container = new Storage.BlobContainer(
                "sleekflow-eh-worker-zips-container",
                new Storage.BlobContainerArgs
                {
                    AccountName = storageAccount.Name,
                    PublicAccess = Storage.PublicAccess.None,
                    ResourceGroupName = _resourceGroup.Name,
                    ContainerName = "zips-container"
                },
                new CustomResourceOptions
                {
                    Parent = storageAccount
                });
            var path = "../Sleekflow.EmailHub.Workers/bin/Release/net8.0/publish";
            var ymdhms = new DirectoryInfo(path)
                .EnumerateFiles("*", SearchOption.AllDirectories)
                .Max(fi => fi.CreationTimeUtc)
                .ToString("yyyyMMddHHmmss");

            var blob = new Storage.Blob(
                "eh_worker_zip_" + ymdhms,
                new Storage.BlobArgs
                {
                    AccountName = storageAccount.Name,
                    ContainerName = container.Name,
                    ResourceGroupName = _resourceGroup.Name,
                    Type = Storage.BlobType.Block,
                    Source = new FileArchive(path)
                },
                new CustomResourceOptions
                {
                    Parent = container
                });
            var codeBlobUrl = StorageUtils.SignedBlobReadUrl(blob, container, storageAccount, _resourceGroup);

            var appInsights = new Insights.Component(
                "sleekflow-eh-worker-app-insight",
                new Insights.ComponentArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var appServicePlan = new Web.AppServicePlan(
                "sleekflow-eh-worker-app-service-plan",
                new Web.AppServicePlanArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Kind = string.Empty,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Tier = "Dynamic", Name = "Y1"
                    }
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });
            var functionAppName = "sleekflow-eh-worker-app";
            var functionApp = new Web.WebApp(
                functionAppName,
                new Web.WebAppArgs
                {
                    Kind = "FunctionApp",
                    ResourceGroupName = _resourceGroup.Name,
                    ServerFarmId = appServicePlan.Id,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateNameValuePairs(
                            new[]
                            {
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_EXTENSION_VERSION", Value = "~4",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_WORKER_RUNTIME", Value = "dotnet-isolated",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_INPROC_NET8_ENABLED", Value = "1",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPINSIGHTS_INSTRUMENTATIONKEY", Value = appInsights.InstrumentationKey
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                    Value = appInsights.ConnectionString,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AzureWebJobsStorage",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTSHARE", Value = functionAppName + "96ed",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_RUN_FROM_PACKAGE", Value = codeBlobUrl,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APP_CONFIGURATION_CONN_STR", Value = primaryAppConfigReadOnlyConnStr,
                                },

                                #region AppConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "INTERNALS_KEY",
                                    Value =
                                        "w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q",
                                },

                                #endregion

                                #region DbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                },

                                #endregion

                                #region EmailHubDbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_EMAIL_HUB_DB_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_emailHubDbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_EMAIL_HUB_DB_KEY", Value = _emailHubDbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_EMAIL_HUB_DB_DATABASE_ID", Value = _emailHubDbOutput.DatabaseId,
                                },

                                #endregion

                                #region ServiceBusConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "SERVICE_BUS_CONN_STR", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr,
                                },

                                #endregion

                                #region EventHubConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "EVENT_HUB_CONN_STR", Value = eventHub.NamespacePrimaryConnStr,
                                },

                                #endregion

                                #region Loggings

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_AUTHENTICATION_ID",
                                    Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", Value = _gcpConfig.CredentialJson,
                                },

                                #endregion

                                #region MassTransitStorageConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONN_STR", Value = massTransitBlobStorage.StorageAccountConnStr
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONTAINER_NAME", Value = massTransitBlobStorage.ContainerName
                                },

                                #endregion
                            }),
                        Use32BitWorkerProcess = true,
                        NetFrameworkVersion = "v8.0",
                    },
                },
                new CustomResourceOptions
                {
                    Parent = appServicePlan
                });

            managedEnvAndAppsTuple.WorkerApps.Add(ServiceNames.EmailHub, functionApp);
            apps.Add(ServiceNames.GetWorkerName(ServiceNames.EmailHub), functionApp);
        }

        return apps;
    }
}