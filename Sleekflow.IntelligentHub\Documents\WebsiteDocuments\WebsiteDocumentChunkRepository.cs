using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Documents.WebsiteDocuments;

public interface IWebsiteDocumentChunkRepository : IRepository<WebsiteDocumentChunk>
{
    Task<List<string>> GetWebsiteDocumentChunkIdsAsync(string sleekflowCompanyId, string documentId);
}

public class WebsiteDocumentChunkRepository
    : BaseRepository<WebsiteDocumentChunk>, IScopedService, IWebsiteDocumentChunkRepository
{
    public WebsiteDocumentChunkRepository(
        ILogger<WebsiteDocumentChunkRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<List<string>> GetWebsiteDocumentChunkIdsAsync(string sleekflowCompanyId, string documentId)
    {
        var websiteDocumentChunks = await GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId);
        return websiteDocumentChunks.Select(e => e.Id).ToList();
    }
}