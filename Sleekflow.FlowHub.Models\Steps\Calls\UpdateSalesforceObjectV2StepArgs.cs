﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateSalesforceObjectV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.update-salesforce-object";

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [Required]
    [JsonProperty("object_id_source")]
    public string ObjectIdSource { get; set; }

    [JsonProperty("object_id")]
    public string? ObjectId { get; set; }

    [JsonProperty("object_id__expr")]
    public string? ObjectIdExpr { get; set; }

    [JsonProperty("fields__name_expr_set")]
    public HashSet<ObjectFieldNameValuePair> FieldsNameExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.SalesforceIntegration;

    [JsonConstructor]
    public UpdateSalesforceObjectV2StepArgs(
        string connectionId,
        string entityTypeName,
        string objectIdSource,
        string? objectId,
        string? objectIdExpr,
        HashSet<ObjectFieldNameValuePair> fieldsNameExprSet)
    {
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        ObjectIdSource = objectIdSource;
        ObjectId = objectId;
        ObjectIdExpr = objectIdExpr;
        FieldsNameExprSet = fieldsNameExprSet;
    }
}