﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnHubspotObjectEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnHubspotObjectEnrolled; }
    }

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [Required]
    [JsonProperty("object_dict")]
    public Dictionary<string, object?> ObjectDict { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnHubspotObjectEnrolledEventBody(
        DateTimeOffset createdAt,
        string connectionId,
        string entityTypeName,
        Dictionary<string, object?> objectDict,
        string workflowId,
        string workflowVersionedId)
        : base(createdAt)
    {
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        ObjectDict = objectDict;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}