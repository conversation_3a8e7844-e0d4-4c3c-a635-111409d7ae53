using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SyncObject : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly IBus _bus;

    public SyncObject(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        IBus bus)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _bus = bus;
    }

    public class SyncObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public SyncObjectInput(
            string sleekflowCompanyId,
            string objectId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            EntityTypeName = entityTypeName;
        }
    }

    public class SyncObjectOutput
    {
    }

    public async Task<SyncObjectOutput> F(
        SyncObjectInput syncObjectInput)
    {
        var authentication =
            await _salesforceAuthenticationService.GetAsync(syncObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var @object = await _salesforceObjectService.GetObjectAsync(
            authentication,
            syncObjectInput.ObjectId,
            syncObjectInput.EntityTypeName);
        if (@object == null)
        {
            throw new SfUserFriendlyException("The object does not exist");
        }

        await _bus.Publish(
            new OnObjectOperationEvent(
                @object,
                OnObjectOperationEvent.OperationCreateOrUpdateObject,
                "salesforce-integrator",
                syncObjectInput.SleekflowCompanyId,
                _salesforceObjectService.ResolveObjectId(@object),
                syncObjectInput.EntityTypeName,
                null),
            context => { context.ConversationId = Guid.Parse(syncObjectInput.SleekflowCompanyId); });

        return new SyncObjectOutput();
    }
}