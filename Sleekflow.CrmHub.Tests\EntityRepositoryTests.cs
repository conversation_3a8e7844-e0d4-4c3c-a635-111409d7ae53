﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Serilog;
using Serilog.Enrichers.Span;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests;

public class EntityRepositoryTests
{
    private static readonly string PartitionId = $"TEST PARTITION-{Guid.NewGuid()}";

    public EntityRepositoryTests()
    {
        var loggerConfiguration = new LoggerConfiguration()
            .Enrich.WithSpan()
            .Enrich.FromLogContext()
            .Enrich.WithMachineName()
            .WriteTo.Async(
                wt => wt.Console(
                    outputTemplate:
                    "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
    }

    private EntityRepository GetEntityRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var entityRepository = new EntityRepository(
            NullLogger<BaseRepository<CrmHubEntity>>.Instance,
            serviceProvider);

        return entityRepository;
    }

    [TearDown]
    public async Task TearDown()
    {
        var entityRepository = GetEntityRepository();

        var entities = await entityRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sys_sleekflow_company_id = @sysSleekflowCompanyId AND c.sys_entity_type_name = @sysEntityTypeName")
                .WithParameter("@sysSleekflowCompanyId", PartitionId)
                .WithParameter("@sysEntityTypeName", "SalesOrder"));

        foreach (var crmHubEntity in entities)
        {
            await entityRepository.DeleteAsync(
                (string) crmHubEntity["id"]!,
                (string) crmHubEntity["id"]!);
        }
    }

    public class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }

    [Test]
    public async Task GetObjectsTest()
    {
        var entityRepository = GetEntityRepository();

        var createCount = await entityRepository.CreateAsync(
            new CrmHubEntity
            {
                ["id"] = nameof(GetObjectsTest),
                ["sys_sleekflow_company_id"] = PartitionId,
                ["sys_entity_type_name"] = "SalesOrder",
            },
            nameof(GetObjectsTest));
        Assert.That(createCount, Is.EqualTo(1));

        var objects = await entityRepository.GetObjectsAsync(
            crmHubEntity =>
                (string) crmHubEntity["id"]! == nameof(GetObjectsTest) &&
                (string) crmHubEntity["sys_sleekflow_company_id"]! == PartitionId &&
                (string) crmHubEntity["sys_entity_type_name"]! == "SalesOrder");
        Assert.That(objects.Count, Is.EqualTo(1));
    }

    [Test]
    public async Task CreateTest()
    {
        var entityRepository = GetEntityRepository();

        var entity1 = await entityRepository.GetOrDefaultAsync(
            nameof(CreateTest),
            nameof(CreateTest));
        Assert.That(entity1, Is.Null);

        var createCount = await entityRepository.CreateAsync(
            new CrmHubEntity
            {
                ["id"] = nameof(CreateTest),
                ["sys_sleekflow_company_id"] = PartitionId,
                ["sys_entity_type_name"] = "SalesOrder",
            },
            nameof(CreateTest));
        Assert.That(createCount, Is.EqualTo(1));

        var entity2 = await entityRepository.GetOrDefaultAsync(
            nameof(CreateTest),
            nameof(CreateTest));
        Assert.That(entity2, Is.Not.Null);
        Assert.That(entity2!["id"], Is.EqualTo(nameof(CreateTest)));
        Assert.That(entity2!["sys_sleekflow_company_id"], Is.EqualTo(PartitionId));
    }

    [Test]
    public async Task CreateAndGetTest()
    {
        var entityRepository = GetEntityRepository();

        var entity1 = await entityRepository.GetOrDefaultAsync(
            nameof(CreateAndGetTest),
            nameof(CreateAndGetTest));
        Assert.That(entity1, Is.Null);

        var entity2 = await entityRepository.CreateAndGetAsync(
            new CrmHubEntity
            {
                ["id"] = nameof(CreateAndGetTest),
                ["sys_sleekflow_company_id"] = PartitionId,
                ["sys_entity_type_name"] = "SalesOrder",
            },
            nameof(CreateAndGetTest));
        Assert.That(entity2, Is.Not.Null);
        Assert.That(entity2!["id"], Is.EqualTo(nameof(CreateAndGetTest)));
        Assert.That(entity2!["sys_sleekflow_company_id"], Is.EqualTo(PartitionId));

        var entity3 = await entityRepository.GetOrDefaultAsync(
            nameof(CreateAndGetTest),
            nameof(CreateAndGetTest));
        Assert.That(entity3, Is.Not.Null);
        Assert.That(entity3!["id"], Is.EqualTo(nameof(CreateAndGetTest)));
        Assert.That(entity3!["sys_sleekflow_company_id"], Is.EqualTo(PartitionId));
    }

    [Test]
    public async Task UpsertTest()
    {
        var entityRepository = GetEntityRepository();

        var entity1 = await entityRepository.GetOrDefaultAsync(
            nameof(UpsertTest),
            nameof(UpsertTest));
        Assert.That(entity1, Is.Null);

        var upsertCount = await entityRepository.UpsertAsync(
            new CrmHubEntity
            {
                ["id"] = nameof(UpsertTest),
                ["sys_sleekflow_company_id"] = PartitionId,
                ["sys_entity_type_name"] = "SalesOrder",
            },
            nameof(UpsertTest));
        Assert.That(upsertCount, Is.EqualTo(1));

        var entity2 = await entityRepository.GetOrDefaultAsync(
            nameof(UpsertTest),
            nameof(UpsertTest));
        Assert.That(entity2, Is.Not.Null);
        Assert.That(entity2!["id"], Is.EqualTo(nameof(UpsertTest)));
        Assert.That(entity2!["sys_sleekflow_company_id"], Is.EqualTo(PartitionId));
    }

    [Test]
    public async Task ReplaceTest()
    {
        var entityRepository = GetEntityRepository();

        var entity1 = await entityRepository.GetOrDefaultAsync(
            nameof(ReplaceTest),
            nameof(ReplaceTest));
        Assert.That(entity1, Is.Null);

        var createCount = await entityRepository.CreateAsync(
            new CrmHubEntity
            {
                ["id"] = nameof(ReplaceTest),
                ["sys_sleekflow_company_id"] = PartitionId,
                ["sys_entity_type_name"] = "SalesOrder",
                ["prop"] = "ABC",
            },
            nameof(ReplaceTest));
        Assert.That(createCount, Is.EqualTo(1));

        var replaceCount = await entityRepository.ReplaceAsync(
            nameof(ReplaceTest),
            nameof(ReplaceTest),
            new CrmHubEntity
            {
                ["id"] = nameof(ReplaceTest),
                ["sys_sleekflow_company_id"] = PartitionId,
                ["sys_entity_type_name"] = "SalesOrder",
                ["prop"] = "ABC",
            });
        Assert.That(replaceCount, Is.EqualTo(1));

        var entity2 = await entityRepository.GetOrDefaultAsync(
            nameof(ReplaceTest),
            nameof(ReplaceTest));
        Assert.That(entity2, Is.Not.Null);
        Assert.That(entity2!["id"], Is.EqualTo(nameof(ReplaceTest)));
        Assert.That(entity2!["sys_sleekflow_company_id"], Is.EqualTo(PartitionId));
        Assert.That(entity2!["prop"], Is.EqualTo("ABC"));
    }

    [Test]
    public async Task DeleteTest()
    {
        var entityRepository = GetEntityRepository();

        var entity1 = await entityRepository.GetOrDefaultAsync(
            nameof(DeleteTest),
            nameof(DeleteTest));
        Assert.That(entity1, Is.Null);

        var createCount = await entityRepository.CreateAsync(
            new CrmHubEntity
            {
                ["id"] = nameof(DeleteTest),
                ["sys_sleekflow_company_id"] = PartitionId,
                ["sys_entity_type_name"] = "SalesOrder",
            },
            nameof(DeleteTest));
        Assert.That(createCount, Is.EqualTo(1));

        var deleteCount = await entityRepository.DeleteAsync(
            nameof(DeleteTest),
            nameof(DeleteTest));
        Assert.That(deleteCount, Is.EqualTo(1));

        var entity2 = await entityRepository.GetOrDefaultAsync(
            nameof(DeleteTest),
            nameof(DeleteTest));
        Assert.That(entity2, Is.Null);
    }
}