using Newtonsoft.Json;
using Sleekflow.Models.Chats;

namespace Sleekflow.Models.WorkflowSteps;

public class GetRecommendedReplyRequest
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("intelligent_hub_usage_filter_from_date_time")]
    public DateTimeOffset? IntelligentHubUsageFilterFromDateTime { get; set; }

    [JsonProperty("intelligent_hub_usage_filter_to_date_time")]
    public DateTimeOffset? IntelligentHubUsageFilterToDateTime { get; set; }

    [JsonConstructor]
    public GetRecommendedReplyRequest(
        string sleekflowCompanyId,
        List<SfChatEntry> conversationContext,
        DateTimeOffset? intelligentHubUsageFilterFromDateTime = null,
        DateTimeOffset? intelligentHubUsageFilterToDateTime = null)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ConversationContext = conversationContext;
        IntelligentHubUsageFilterFromDateTime = intelligentHubUsageFilterFromDateTime;
        IntelligentHubUsageFilterToDateTime = intelligentHubUsageFilterToDateTime;
    }
}