using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;
using CsvHelper;
using CsvHelper.Configuration.Attributes;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.LeadScoreCalculator;

namespace Sleekflow.IntelligentHub.Evaluator;

[TestFixture]
[Parallelizable(ParallelScope.Children)]
public partial class LeadScoreCalculatorTest
{
    private static readonly List<(string TestId, string Scenario, LeadScoreCalculatorTestCase TestCase, LeadScoreCalculatorResult[] Results)> AllTestResults = [];

    [TestCaseSource(nameof(GetTestCases))]
    [Parallelizable(ParallelScope.All)]
    public async Task CalculateLeadScoreTest(LeadScoreCalculatorTestCase testCase)
    {
        var testId = Guid.NewGuid();
        var fixture = new LeadScoreCalculatorFixture();
        var results = await fixture.EvaluateAsync(testCase, CancellationToken.None);

        var resultsWithScores = results.Select(r => new
        {
            Result = r,
            MatchingScore = r.CalculateMatchingScore(testCase.ExpectedEvaluatedScore)
        }).ToArray();

        AllTestResults.Add((testId.ToString(), testCase.Scenario, testCase, resultsWithScores.Select(r =>
            new LeadScoreCalculatorResult(
                r.Result.AgentName,
                r.Result.Answer,
                r.Result.ElapsedMilliseconds)).ToArray()));

        Console.WriteLine("Chat messages scenarios:");
        foreach (var chatMessageContent in testCase.ChatMessageContents)
        {
            Console.WriteLine($"{chatMessageContent.Role}: {chatMessageContent.Content}");
        }

        Console.WriteLine("----------------------- Best Agent ---------------------");

        var bestOutput = resultsWithScores.OrderByDescending(r => r.MatchingScore).First();
        Console.WriteLine($"Best Scoring Agent: {bestOutput.Result.AgentName}");

        Console.WriteLine("----------------------- Scores ------------------------");

        foreach (var result in resultsWithScores.OrderByDescending(r => r.MatchingScore))
        {
            Console.WriteLine($"Agent Name: {result.Result.AgentName}");
            Console.WriteLine($"Elapsed Time: {result.Result.ElapsedMilliseconds} ms");

            Console.WriteLine("-------------------------------------------------------");

            Console.WriteLine(
                $"Expected Answer: {JsonConvert.SerializeObject(testCase.ExpectedEvaluatedScore, Formatting.Indented)}");
            Console.WriteLine(
                $"Generated Answer: {JsonConvert.SerializeObject(result.Result.Answer, Formatting.Indented)}");
            Console.WriteLine($"Matching Score: {result.MatchingScore:F2}");

            Console.WriteLine("-------------------------------------------------------");
        }
    }

    [OneTimeTearDown]
    public async Task OneTimeTearDown()
    {
        // Generate CSV output
        var csvPath = Path.Combine(
            GetRootPath(),
            $"lead_score_calculator_test_results_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

        await using var writer = new StreamWriter(csvPath);
        await using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

        // Write test results
        var records = AllTestResults.SelectMany(result =>
        {
            var (testId, scenario, testCase, results) = result;

            return results.Select(r => new LeadScoreCalculatorTestResult
            {
                TestId = testId,
                Scenario = scenario,
                Question = string.Join(" ", testCase.ChatMessageContents.Select(q => $"{q.Role}: {q.Content}")),
                ExpectedCategory = testCase.ExpectedEvaluatedScore.Category,
                ExpectedScore = testCase.ExpectedEvaluatedScore.Score,
                ExpectedExplanation = testCase.ExpectedEvaluatedScore.Reason,
                AgentCategory = r.Answer?.Score.ToString() ?? string.Empty,
                AgentScore = r.Answer?.Score.ToString() ?? string.Empty,
                AgentExplanation = r.Answer?.Reason.Replace("\r\n", " ").Replace("\n", " ") ?? string.Empty,
                MatchingScore = r.CalculateMatchingScore(testCase.ExpectedEvaluatedScore).ToString("F2"),
                ElapsedTimeMs = r.ElapsedMilliseconds.ToString()
            });
        }).ToList();

        // Sort by Scenario
        records = records.OrderBy(r => r.Scenario).ThenBy(r => r.TestId).ToList();

        // Calculate statistics
        var scenarioStats = records
            .GroupBy(r => r.Scenario)
            .Select(g => new
            {
                Scenario = g.Key,
                TotalCount = g.Count(),
                MatchCount = g.Count(r => IsScoreInSameRange(r.AgentCategory, r.ExpectedScore)),
                AvgMatchingScore = g.Average(r => double.Parse(r.MatchingScore)),
                AvgElapsedTime = g.Average(r => double.Parse(r.ElapsedTimeMs))
            })
            .ToList();

        // Add statistics records
        var statsRecords = scenarioStats.Select(s => new LeadScoreCalculatorTestResult
        {
            TestId = "Statistics",
            Scenario = s.Scenario,
            Question = "",
            ExpectedCategory = "",
            ExpectedScore = 0,
            ExpectedExplanation = "",
            AgentCategory = $"Match: {s.MatchCount}/{s.TotalCount} ({(double) s.MatchCount / s.TotalCount:P2})",
            AgentScore = $"Avg Score: {s.AvgMatchingScore:F2}",
            AgentExplanation = $"Avg Time: {s.AvgElapsedTime:F0}ms",
            MatchingScore = "",
            ElapsedTimeMs = ""
        });

        // Merge records with statistics
        records = records.Concat(statsRecords).ToList();
        await csv.WriteRecordsAsync(records);
        Console.WriteLine($"\nTest results exported to: {csvPath}");
    }

    private string GetRootPath()
    {
        var exePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var appPathMatcher = new Regex(@"(?<!file)[A-Za-z]:\\+[\S\s]*?(?=\\+bin)");
        return appPathMatcher.Match(exePath).Value;
    }

    public static IEnumerable<LeadScoreCalculatorTestCase> GetTestCases()
    {
        return ScoreCalculatorTestCases.GetTestCases();
    }

    private static bool IsScoreInSameRange(string agentScoreStr, int expectedScore)
    {
        if (!int.TryParse(agentScoreStr, out var agentScore))
            return false;

        return GetScoreRange(agentScore) == GetScoreRange(expectedScore);
    }

    private static string GetScoreRange(int score)
    {
        return score switch
        {
            >= 80 => "High",
            <= 40 => "Low",
            _ => "Medium"
        };
    }
}

public class LeadScoreCalculatorTestResult
{
    [Name("test_id")]
    public string TestId { get; init; } = string.Empty;

    [Name("scenario")]
    public string Scenario { get; init; } = string.Empty;

    [Name("question")]
    public string Question { get; init; } = string.Empty;

    [Name("expected_category")]
    public string ExpectedCategory { get; init; } = string.Empty;

    [Name("agent_category")]
    public string AgentCategory { get; init; } = string.Empty;

    [Name("expected_score")]
    public int ExpectedScore { get; init; }

    [Name("agent_score")]
    public string AgentScore { get; init; } = string.Empty;

    [Name("expected_explanation")]
    public string ExpectedExplanation { get; init; } = string.Empty;

    [Name("agent_explanation")]
    public string AgentExplanation { get; init; } = string.Empty;

    [Name("matching_score")]
    public string MatchingScore { get; init; } = string.Empty;

    [Name("elapsed_time_ms")]
    public string ElapsedTimeMs { get; init; } = string.Empty;
}