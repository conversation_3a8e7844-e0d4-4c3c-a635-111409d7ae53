﻿using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.GoogleSheets.Subscriptions;

public interface IGoogleSheetsSubscriptionRepository : IRepository<GoogleSheetsSubscription>
{
}

public class GoogleSheetsSubscriptionRepository
    : BaseRepository<GoogleSheetsSubscription>,
        IGoogleSheetsSubscriptionRepository,
        ISingletonService
{
    public GoogleSheetsSubscriptionRepository(
        ILogger<BaseRepository<GoogleSheetsSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}