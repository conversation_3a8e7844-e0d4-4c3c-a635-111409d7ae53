namespace Sleekflow.InternalIntegrationHub.Constants.NetSuite;

public static class Endpoints
{
    // credit memo endpoints
    public const string CreditMemoEndpoint = "/creditMemo";

    // currency endpoints
    public const string GetCurrencyEndpoint = "/currency";

    // currency details endpoints
    public const string GetCurrencyDetailsEndpoint = "/currency/{0}";

    // Create Customer endpoint
    public const string CreateCustomerEndpoint = "/customer";

    // Update Customer endpoint
    public const string UpdateCustomerEndpoint = "/customer/{0}";

    // Create Payment endpoint
    public const string CreatePaymentEndpoint = "/customerPayment";

    // Get Payment endpoint
    public const string GetPaymentEndpoint = "/customerPayment/{0}";

    // Customer Refund endpoint
    public const string CustomerRefundEndpoint = "/customerRefund";

    // Employee
    public const string GetEmployeeEndpoint = "/employee";

    public const string GetEmployeeDetailsEndpoint = "/employee/{0}";

    public const string CreateEmployeeEndpoint = "/employee";

    public const string UpdateEmployeeEndpoint = "/employee/{0}";

    // Get Invoice endpoint
    public const string GetInvoiceEndpoint = "/invoice/{0}";

    // Create Invoice endpoint
    public const string CreateInvoiceEndpoint = "/invoice";

    // Non Inventory Sale Item
    public const string NonInventorySaleItemEndpoint = "/nonInventorySaleItem";

    // subsidiary endpoints
    public const string GetSubsidiaryEndpoint = "/subsidiary";

    // subsidiary details endpoints
    public const string GetSubsidiaryDetailsEndpoint = "/subsidiary/{0}";

    // Get Terms endpoint
    public const string GetTermEndpoint = "/term";

    // Get Terms Details endpoint
    public const string GetTermDetailsEndpoint = "/term/{0}";
}