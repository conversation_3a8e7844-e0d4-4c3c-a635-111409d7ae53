using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Integrator.Dynamics365.Objects;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetTypeFields : ITrigger
{
    private readonly IDynamics365ObjectService _dynamics365ObjectService;
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;

    public GetTypeFields(
        IDynamics365ObjectService dynamics365ObjectService,
        IDynamics365AuthenticationService dynamics365AuthenticationService)
    {
        _dynamics365ObjectService = dynamics365ObjectService;
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
    }

    public class GetTypeFieldsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public GetTypeFieldsInput(
            string sleekflowCompanyId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
        }
    }

    public class GetTypeFieldsOutput
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetTypeFieldsOutput(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public async Task<GetTypeFieldsOutput> F(GetTypeFieldsInput getTypeFieldsInput)
    {
        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(getTypeFieldsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var getFieldsOutput =
            await _dynamics365ObjectService.GetFieldsAsync(authentication, getTypeFieldsInput.EntityTypeName);

        var creatableFields = getFieldsOutput.CreatableFields;
        var updatableFields = getFieldsOutput.UpdatableFields;
        var viewableFields = getFieldsOutput.ViewableFields;

        return new GetTypeFieldsOutput(updatableFields, creatableFields, viewableFields);
    }
}