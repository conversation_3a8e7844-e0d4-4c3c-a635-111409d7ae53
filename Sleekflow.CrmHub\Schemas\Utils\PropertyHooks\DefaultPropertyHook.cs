﻿using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.Schemas.Dtos;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Schemas.Utils.PropertyHooks;

public interface IDefaultPropertyHook : IPropertyHook
{
}

public class DefaultPropertyHook : IDefaultPropertyHook, ISingletonService
{
    public (IDataType DataType, List<Option>? Options) PreConstruct(PropertyInput propertyInput)
    {
        return (propertyInput.DataType, null);
    }

    public (IDataType DataType, List<Option>? Options) PreConstruct(Property property)
    {
        return (property.DataType, null);
    }

    public UpdatePropertyChangeContext PreUpdate(Property originalProperty, Property receivedProperty)
    {
        return new UpdatePropertyChangeContext(
            new List<string>(),
            NeedReindexPropertyValues(originalProperty, receivedProperty));
    }

    private static bool NeedReindexPropertyValues(Property originalProperty, Property receivedProperty)
        => originalProperty.IsSearchable != receivedProperty.IsSearchable;
}