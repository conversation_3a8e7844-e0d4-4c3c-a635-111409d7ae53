﻿using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Triggers.FlowHubConfigs;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.TestClients;

public static class FlowHubConfigTestClient
{
    public static async Task<Output<GetFlowHubConfig.GetFlowHubConfigOutput>?> GetAsync(
        string sleekflowCompanyId)
    {
        var getFlowHubConfigInput =
            new GetFlowHubConfig.GetFlowHubConfigInput(
                sleekflowCompanyId);

        var getFlowHubConfigScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getFlowHubConfigInput).ToUrl("/FlowHubConfigs/GetFlowHubConfig");
            });

        var getFlowHubConfigOutput =
            await getFlowHubConfigScenarioResult.ReadAsJsonAsync<
                Output<GetFlowHubConfig.GetFlowHubConfigOutput>>();

        return getFlowHubConfigOutput;
    }

    public static async Task<Output<EnrollFlowHub.EnrollFlowHubOutput>?> EnrollAsync(
        string sleekflowCompanyId,
        string staffId,
        List<string>? staffTeamIds = null,
        string origin = "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net")
    {
        var enrollFlowHubInput = new EnrollFlowHub.EnrollFlowHubInput(
            sleekflowCompanyId,
            staffId,
            staffTeamIds,
            origin);

        var enrollFlowHubScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(enrollFlowHubInput).ToUrl("/FlowHubConfigs/EnrollFlowHub");
            });

        var enrollFlowHubOutput = await enrollFlowHubScenarioResult
            .ReadAsJsonAsync<Output<EnrollFlowHub.EnrollFlowHubOutput>>();

        return enrollFlowHubOutput;
    }

    public static async Task<Output<UnenrollFlowHub.UnenrollFlowHubOutput>?> UnenrollAsync(
        string sleekflowCompanyId,
        string staffId,
        List<string>? staffTeamIds = null)
    {
        var unenrollFlowHubInput =
            new UnenrollFlowHub.UnenrollFlowHubInput(
                sleekflowCompanyId,
                staffId,
                staffTeamIds);

        var unenrollFlowHubScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(unenrollFlowHubInput).ToUrl("/FlowHubConfigs/UnenrollFlowHub");
            });

        var unenrollFlowHubOutput =
            await unenrollFlowHubScenarioResult.ReadAsJsonAsync<
                Output<UnenrollFlowHub.UnenrollFlowHubOutput>>();

        return unenrollFlowHubOutput;
    }

    public static async Task<Output<UpdateFlowHubConfig.UpdateFlowHubConfigOutput>?> UpdateFlowHubConfigAsync(
        string sleekflowCompanyId,
        string staffId,
        UsageLimit usageLimit,
        List<string>? staffTeamIds = null,
        UsageLimitOffset? usageLimitOffset = null)
    {
        var updateFlowHubConfigInput =
            new UpdateFlowHubConfig.UpdateFlowHubConfigInput(
                sleekflowCompanyId,
                staffId,
                staffTeamIds,
                usageLimit,
                usageLimitOffset,
                origin: "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net");

        var updateFlowHubConfigScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(updateFlowHubConfigInput).ToUrl("/FlowHubConfigs/UpdateFlowHubConfig");
            });

        var updateFlowHubConfigOutput = await updateFlowHubConfigScenarioResult
            .ReadAsJsonAsync<Output<UpdateFlowHubConfig.UpdateFlowHubConfigOutput>>();

        return updateFlowHubConfigOutput;
    }

    public static async Task<Output<GetEnrolledFlowHubConfigs.GetEnrolledFlowHubConfigsOutput>?>
        GetEnrolledFlowHubConfigsAsync(
            string? continuationToken,
            int limit)
    {
        var getEnrolledFlowHubConfigsInput =
            new GetEnrolledFlowHubConfigs.GetEnrolledFlowHubConfigsInput(
                continuationToken,
                limit);

        var getEnrolledFlowHubConfigsScenarioResult = await Application.Host.Scenario(
            s =>
            {
                s.WithRequestHeader("X-Sleekflow-Record", "true");
                s.Post.Json(getEnrolledFlowHubConfigsInput).ToUrl("/FlowHubConfigs/GetEnrolledFlowHubConfigs");
            });

        var getEnrolledFlowHubConfigsOutput =
            await getEnrolledFlowHubConfigsScenarioResult.ReadAsJsonAsync<
                Output<GetEnrolledFlowHubConfigs.GetEnrolledFlowHubConfigsOutput>>();

        return getEnrolledFlowHubConfigsOutput;
    }
}