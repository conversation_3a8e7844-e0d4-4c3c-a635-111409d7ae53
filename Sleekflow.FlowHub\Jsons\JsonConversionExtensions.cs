using Newtonsoft.Json.Linq;

namespace Sleekflow.FlowHub.Jsons;

public static class JsonConversionExtensions
{
    public static IDictionary<string, object> ToNativeDictionary(this JObject json)
    {
        var propertyValuePairs = json.ToObject<Dictionary<string, object>>()!;
        ProcessJObjectProperties(propertyValuePairs);
        ProcessJArrayProperties(propertyValuePairs);
        return propertyValuePairs;
    }

    private static void ProcessJObjectProperties(IDictionary<string, object> propertyValuePairs)
    {
        var objectPropertyNames = propertyValuePairs
            .Select(
                property => new
                {
                    property, propertyName = property.Key
                })
            .Select(
                t => new
                {
                    t, value = t.property.Value
                })
            .Where(t => t.value is JObject)
            .Select(t => t.t.propertyName)
            .ToList();

        objectPropertyNames.ForEach(
            propertyName =>
                propertyValuePairs[propertyName] = ToNativeDictionary((JObject) propertyValuePairs[propertyName]));
    }

    private static void ProcessJArrayProperties(IDictionary<string, object> propertyValuePairs)
    {
        var arrayPropertyNames = propertyValuePairs
            .Select(
                property => new
                {
                    property, propertyName = property.Key
                })
            .Select(
                t => new
                {
                    t, value = t.property.Value
                })
            .Where(t => t.value is JArray)
            .Select(t => t.t.propertyName)
            .ToList();

        arrayPropertyNames.ForEach(
            propertyName =>
                propertyValuePairs[propertyName] = ToNativeArray((JArray) propertyValuePairs[propertyName]));
    }

    public static object[] ToNativeArray(this JArray array)
    {
        return array.ToObject<object[]>()!.Select(ProcessArrayEntry).ToArray();
    }

    private static object ProcessArrayEntry(object value)
    {
        if (value is JObject jObject)
        {
            return ToNativeDictionary(jObject);
        }

        if (value is JArray jArray)
        {
            return ToNativeArray(jArray);
        }

        return value;
    }
}