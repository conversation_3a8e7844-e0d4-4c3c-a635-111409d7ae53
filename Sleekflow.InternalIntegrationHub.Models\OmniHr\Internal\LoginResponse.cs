using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

public class LoginResponse
{
    [JsonProperty(PropertyName = "refresh")]
    public string Refresh { get; set; }

    [JsonProperty(PropertyName = "access")]
    public string Access { get; set; }

    [JsonProperty(PropertyName = "refresh_exp")]
    public string RefreshExp { get; set; }

    [JsonProperty(PropertyName = "access_exp")]
    public string AccessExp { get; set; }

    [JsonConstructor]
    public LoginResponse(string refresh, string access, string refreshExp, string accessExp)
    {
        Refresh = refresh;
        Access = access;
        RefreshExp = refreshExp;
        AccessExp = accessExp;
    }
}