using Serilog;
using Sleekflow;
using Sleekflow.IntelligentHub;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries.Indexing;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;
using Sleekflow.Persistence.IntelligentHubDb;

#if SWAGGERGEN
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using System.Reflection;
using Moq;
#endif

static void BuildIntelligentHubDbProcessorServices(IServiceCollection b)
{
#if SWAGGERGEN
#else
    b.AddSingleton<IDbProcessorConfig, DbProcessorConfig>();
    b.AddHostedService<KnowledgeBaseEntryDbProcessorService>(sp => new KnowledgeBaseEntryDbProcessorService(
        sp.GetRequiredService<ILogger<KnowledgeBaseEntryDbProcessorService>>(),
        sp.GetRequiredService<IIntelligentHubDbResolver>(),
        sp.GetRequiredService<IDbProcessorConfig>(),
        sp.GetRequiredService<IKnowledgeBaseEntryIndexingService>()));
#endif
}


const string appName = "SleekflowIntelligentHub";

MvcModules.BuildLogger(appName);

Log.Information("Starting web host");

var builder = WebApplication.CreateBuilder(args);
builder.Host.UseSerilog();
builder.Services.AddHttpContextAccessor();

MvcModules.BuildHealthCheck(builder.Services);
MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
#if SWAGGERGEN
MvcModules.BuildApiBehaviors(builder, list =>
{
    list.AddRange(new List<OpenApiServer>()
    {
        new OpenApiServer()
        {
            Description = "Local",
            Url = $"https://localhost:7080",
        },
        new OpenApiServer()
        {
            Description = "Dev Apigw",
            Url = $"https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/intelligent-hub",
        },
        new OpenApiServer()
        {
            Description = "Prod Apigw",
            Url = $"https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/intelligent-hub",
        }
    });
}, Assembly.Load("Sleekflow.IntelligentHub"), Assembly.Load("Sleekflow.IntelligentHub.Models"));
#else
MvcModules.BuildApiBehaviors(builder);
#endif
Modules.BuildHttpClients(builder.Services);
Modules.BuildConfigs(builder.Services);
Modules.BuildServices(builder.Services);
Modules.BuildTriggers(builder.Services);
Modules.BuildServiceBusServices(builder.Services);
Modules.BuildServiceBusManager(builder.Services);
Modules.BuildDbServices(builder.Services);
Modules.BuildCacheServices(builder.Services);
Modules.BuildWorkerServices(builder.Services);
MvcModules.BuildFuncServices(builder.Services, appName);
IntelligentHubModules.BuildIntelligentHubDbServices(builder.Services);
BuildIntelligentHubDbProcessorServices(builder.Services);

#if SWAGGERGEN
#else
builder.Services.AddSemanticKernelServices();
#endif

// Register FlowHub-related services
builder.Services.AddScoped<IWorkflowCountService, WorkflowCountService>();

builder.Services
    .AddHttpClient("default-handler")
    .ConfigurePrimaryHttpMessageHandler(() => new HttpClientHandler
    {
        AllowAutoRedirect = false,
    });

var app = builder.Build();

app.UseAuthorization();
app.MapControllers();
HealthCheckMapping.MapHealthChecks(app);

#if SWAGGERGEN
app.UseSwagger();
app.UseSwaggerUI(
    options =>
    {
        var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

        foreach (var description in provider.ApiVersionDescriptions)
        {
            options.SwaggerEndpoint(
                $"/swagger/{description.GroupName}/swagger.json",
                description.GroupName.ToUpperInvariant());
        }
    });
#endif

ThreadPool.SetMinThreads(128, 128);
ThreadPool.SetMaxThreads(512, 512);

app.Run();

Log.CloseAndFlush();