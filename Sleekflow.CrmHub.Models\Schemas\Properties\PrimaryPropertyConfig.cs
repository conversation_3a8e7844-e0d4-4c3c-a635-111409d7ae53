﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Schemas.Properties;

public record PrimaryPropertyConfig
{
    public const string PropertyNameIsAutoGenerated = "is_auto_generated";
    public const string PropertyNameSequential = "sequential";

    [JsonProperty(PropertyNameIsAutoGenerated)]
    public bool IsAutoGenerated { get; set; }

    [JsonProperty(PropertyNameSequential, NullValueHandling = NullValueHandling.Include)]
    public Sequential? Sequential { get; set; }

    [JsonConstructor]
    public PrimaryPropertyConfig(
        bool isAutoGenerated,
        Sequential? sequential)
    {
        IsAutoGenerated = isAutoGenerated;
        Sequential = sequential;
    }
}

public record Sequential
{
    public const string PropertyNameSequencePrefix = "sequence_prefix";
    public const string PropertyNameSequenceDigitLength = "sequence_digit_length";

    [JsonProperty(PropertyNameSequencePrefix)]
    [StringLength(maximumLength: 10, MinimumLength = 1)]
    public string SequencePrefix { get; set; }

    [JsonProperty(PropertyNameSequenceDigitLength)]
    [Range(5, 20)]
    public int SequenceDigitLength { get; set; }

    [JsonConstructor]
    public Sequential(string sequencePrefix, int sequenceDigitLength)
    {
        SequencePrefix = sequencePrefix;
        SequenceDigitLength = sequenceDigitLength;
    }
}