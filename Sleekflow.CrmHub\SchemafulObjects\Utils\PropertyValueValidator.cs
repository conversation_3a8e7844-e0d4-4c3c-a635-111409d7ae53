﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.SchemafulObjects.Dtos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.SchemafulObjects.Utils;

public interface IPropertyValueValidator
{
    void Validate(
        List<Property> properties,
        Dictionary<string, object?> values,
        string schemaId,
        CrmHubConfig crmHubConfig);

    void Validate(Property property, object? value, CrmHubConfig crmHubConfig);
}

public class PropertyValueValidator : IPropertyValueValidator, ISingletonService
{
    private readonly ILogger<PropertyValueValidator> _logger;

    public PropertyValueValidator(ILogger<PropertyValueValidator> logger)
    {
        _logger = logger;
    }

    public void Validate(
        List<Property> properties,
        Dictionary<string, object?> values,
        string schemaId,
        CrmHubConfig crmHubConfig)
    {
        properties
            .FindAll(p => p.IsRequired)
            .ForEach(
                p =>
                {
                    if (!values.ContainsKey(p.Id))
                    {
                        ThrowSfValidationException("Missing required property value", p.Id, p.UniqueName, p.DataType.Name);
                    }
                });

        foreach (var value in values)
        {
            try
            {
                Validate(properties.First(p => p.Id == value.Key), value.Value, crmHubConfig);
            }
            catch (InvalidOperationException)
            {
                values.Remove(value.Key);

                _logger.LogInformation(
                    "Property Value Validator - removed non-existing property {SchemaId} {PropertyId}",
                    schemaId,
                    value.Key);
            }
        }
    }

    public void Validate(Property property, object? value, CrmHubConfig crmHubConfig)
    {
        if (value is null)
        {
            if (property.IsRequired)
            {
                ThrowSfValidationException(
                    "Property value is required",
                    property.Id,
                    property.UniqueName,
                    property.DataType.Name);
            }
            else
            {
                return;
            }
        }

        switch (property.DataType.Name)
        {
            case SchemaPropertyDataTypes.SingleLineText:
                if (value is not string || string.IsNullOrEmpty(value.ToString()))
                {
                    ThrowSfValidationException(
                        "Value must be a string",
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }

                break;
            case SchemaPropertyDataTypes.Numeric:
                if(!IsNumeric(value!.GetType()))
                {
                    ThrowSfValidationException(
                        "Value must be an integer",
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }

                break;
            case SchemaPropertyDataTypes.Decimal:
                if(!IsDecimal(value!.GetType()))
                {
                    ThrowSfValidationException(
                        "Value must be a decimal number",
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }

                break;
            case SchemaPropertyDataTypes.SingleChoice:
                if (value is not string ||
                    string.IsNullOrEmpty(value.ToString()) ||
                    !property.Options!.Select(o => o.Id).Contains(value.ToString()))
                {
                    ThrowSfValidationException(
                        "Invalid option",
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }

                break;

            case SchemaPropertyDataTypes.MultipleChoice:
                try
                {
                    if (value is JArray arr)
                    {
                        List<string> receivedOptionIds = arr
                            .Children<JValue>()
                            .Select(opt => opt.Value<string>())
                            .ToList()!;

                        var optionIds = property.Options!.Select(o => o.Id).ToList();

                        if (receivedOptionIds.Count != new HashSet<string>(receivedOptionIds).Count)
                        {
                            throw new SfUserFriendlyException("Duplicate options selected");
                        }

                        if (receivedOptionIds.Count == 0 ||
                            optionIds.Count != optionIds.Union(receivedOptionIds).ToList().Count)
                        {
                            throw new SfUserFriendlyException("Invalid options selected");
                        }
                    }
                    else
                    {
                        throw new SfUserFriendlyException("Cannot parse the input");
                    }
                }
                catch (SfUserFriendlyException sufe)
                {
                    ThrowSfValidationException(
                        sufe.Message,
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }
                catch (Exception)
                {
                    ThrowSfValidationException(
                        "Cannot parse the input",
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }

                break;

            case SchemaPropertyDataTypes.Boolean:
                if(!IsBoolean(value!.GetType()))
                {
                    ThrowSfValidationException(
                        "Value must be a boolean",
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }

                break;
            case SchemaPropertyDataTypes.Date:
            case SchemaPropertyDataTypes.DateTime:
                if (value is not DateTimeOffset)
                {
                    ThrowSfValidationException(
                        "Value must be a valid DateTimeOffset",
                        property.Id,
                        property.UniqueName,
                        property.DataType.Name);
                }

                break;
            case SchemaPropertyDataTypes.ArrayObject:
                ValidateArrayObject(property, value, crmHubConfig);

                break;
            case SchemaPropertyDataTypes.Image:
                ValidateImage(property, value);

                break;
            default:
                ThrowSfValidationException(
                    "Unsupported data type",
                    property.Id,
                    property.UniqueName,
                    property.DataType.Name);

                break;
        }
    }

    private static void ThrowSfValidationException(
        string errorMessage,
        string propertyId,
        string propertyUniqueName,
        string dataType)
    {
        throw new SfValidationException(
            validationResults: new List<ValidationResult>
            {
                new ValidationResult(
                    $"{errorMessage} in property [{propertyUniqueName}]",
                    new[]
                    {
                        propertyId,
                        propertyUniqueName,
                        dataType
                    })
            });
    }

    private static bool IsNumeric(Type? type)
    {
        if (type == null)
        {
            return false;
        }

        // from http://stackoverflow.com/a/5182747/172132
        switch (Type.GetTypeCode(type))
        {
            case TypeCode.Byte:
            case TypeCode.Int16:
            case TypeCode.Int32:
            case TypeCode.Int64:
            case TypeCode.SByte:
            case TypeCode.Single:
            case TypeCode.UInt16:
            case TypeCode.UInt32:
            case TypeCode.UInt64:
                return true;
        }

        return false;
    }

    private static bool IsDecimal(Type? type)
    {
        if (type == null)
        {
            return false;
        }

        switch (Type.GetTypeCode(type))
        {
            case TypeCode.Byte:
            case TypeCode.Decimal:
            case TypeCode.Double:
            case TypeCode.Int16:
            case TypeCode.Int32:
            case TypeCode.Int64:
            case TypeCode.SByte:
            case TypeCode.Single:
            case TypeCode.UInt16:
            case TypeCode.UInt32:
            case TypeCode.UInt64:
                return true;
        }

        return false;
    }

    private static bool IsBoolean(Type? type)
    {
        return Type.GetTypeCode(type) == TypeCode.Boolean;
    }

    private void ValidateArrayObject(Property property, object? value, CrmHubConfig crmHubConfig)
    {
        if (!InnerSchemafulObjectDto.TryParse(value, out var innerSchemafulObjects))
        {
            ThrowSfValidationException(
                "Value must be a valid InnerSchamfulObject Array",
                property.Id,
                property.UniqueName,
                property.DataType.Name);

            return; // will throw error in this block, add return only for passing ide checking
        }

        if (innerSchemafulObjects.Count > crmHubConfig.GetOffsetAppliedUsageLimit().CustomObjectMaximumArrayObjectArraySize)
        {
            throw new SfCrmHubExceedUsageException(UsageLimit.PropertyNameCustomObjectMaximumArrayObjectArraySize);
        }

        var innerSchema = property.DataType.GetInnerSchema();

        foreach (var innerSchemafulObjectDto in innerSchemafulObjects)
        {
            if (string.IsNullOrWhiteSpace(innerSchemafulObjectDto.Id))
            {
                ThrowSfValidationException(
                    "Invalid Id detected in InnerSchamfulObject Array",
                    property.Id,
                    property.UniqueName,
                    property.DataType.Name);
            }

            Validate(
                innerSchema.Properties,
                innerSchemafulObjectDto.PropertyValues,
                string.Empty,
                crmHubConfig);
        }
    }

    private static void ValidateImage(Property property, object? value)
    {
        if (!ImageDto.TryParse(value?.ToString(), out var image))
        {
            ThrowSfValidationException(
                "Invalid image input.",
                property.Id,
                property.UniqueName,
                property.DataType.Name);
        }

        if (!Uri.IsWellFormedUriString(image.DownloadUrl, UriKind.Absolute))
        {
            ThrowSfValidationException(
                "Value must be a valid Uri",
                property.Id,
                property.UniqueName,
                property.DataType.Name);
        }
    }
}