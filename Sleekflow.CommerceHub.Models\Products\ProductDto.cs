using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Products;

public class ProductDto : IHasMetadata
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(Product.PropertyNameCategoryIds)]
    public List<string> CategoryIds { get; set; }

    [JsonProperty(Product.PropertyNameSku)]
    public string? Sku { get; set; }

    [JsonProperty(Product.PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty(Product.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(Product.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(Product.PropertyNameImages)]
    public List<ImageDto> Images { get; set; }

    [JsonProperty(Product.PropertyNameProductVariantAttributes)]
    public List<Product.ProductAttribute> ProductVariantAttributes { get; set; }

    [JsonProperty(Product.PropertyNameProductVariantPrices)]
    public List<Price> ProductVariantPrices { get; set; }

    [JsonProperty(Product.PropertyNameIsViewEnabled)]
    public bool IsViewEnabled { get; set; }

    [JsonProperty("default_product_variant")]
    public ProductVariantDto? DefaultProductVariant { get; set; }

    [JsonProperty("product_variants")]
    public List<ProductVariantDto> ProductVariants { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNamePlatformData)]
    public PlatformData PlatformData { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public ProductDto(
        string id,
        string storeId,
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<ImageDto> images,
        List<Product.ProductAttribute> productVariantAttributes,
        List<Price> productVariantPrices,
        bool isViewEnabled,
        ProductVariantDto? defaultProductVariant,
        List<ProductVariantDto> productVariants,
        PlatformData platformData,
        Dictionary<string, object?> metadata)
    {
        Id = id;
        StoreId = storeId;
        CategoryIds = categoryIds;
        Sku = sku;
        Url = url;
        Names = names;
        Descriptions = descriptions;
        Images = images;
        ProductVariantAttributes = productVariantAttributes;
        ProductVariantPrices = productVariantPrices;
        IsViewEnabled = isViewEnabled;
        DefaultProductVariant = defaultProductVariant;
        ProductVariants = productVariants;
        PlatformData = platformData;
        Metadata = metadata;
    }

    public ProductDto(Product product, List<ProductVariant> productVariants)
        : this(
            product.Id,
            product.StoreId,
            product.CategoryIds,
            product.Sku,
            product.Url,
            product.Names,
            product.Descriptions,
            product.Images.Select(i => new ImageDto(i)).ToList(),
            product.ProductVariantAttributes,
            product.ProductVariantPrices,
            product.IsViewEnabled,
            productVariants.Any()
                ? new ProductVariantDto(productVariants.OrderByDescending(pv => pv.Position).First())
                : null,
            productVariants.Select(pv => new ProductVariantDto(pv)).ToList(),
            product.PlatformData,
            product.Metadata)
    {
    }
}