using GraphApi.Client.Payloads;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

public class FacebookBusinessIntegrationSystemUserAccessToken : IHasCreatedAt
{
    [JsonProperty("encrypted_token")]
    public string EncryptedToken { get; set; }

    [JsonProperty("scopes")]
    public List<string> Scopes { get; set; }

    [JsonProperty("granular_scopes")]
    public List<GranularScope> GranularScopes { get; set; }

    [JsonProperty("facebook_app_id")]
    public string FacebookAppId { get; set; }

    [JsonProperty("facebook_application")]
    public string FacebookApplication { get; set; }

    [JsonProperty("facebook_business_system_user_id")]
    public string FacebookBusinessSystemUserId { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public FacebookBusinessIntegrationSystemUserAccessToken(
        string encryptedToken,
        List<string> scopes,
        List<GranularScope> granularScopes,
        string facebookAppId,
        string facebookApplication,
        string facebookBusinessSystemUserId,
        DateTimeOffset createdAt)
    {
        EncryptedToken = encryptedToken;
        Scopes = scopes;
        GranularScopes = granularScopes;
        FacebookAppId = facebookAppId;
        FacebookApplication = facebookApplication;
        FacebookBusinessSystemUserId = facebookBusinessSystemUserId;
        CreatedAt = createdAt;
    }
}

public class GranularScope
{
    [JsonProperty("scope")]
    public string Scope { get; set; }

    [JsonProperty("target_ids")]
    public List<string>? TargetIds { get; set; }

    [JsonConstructor]
    public GranularScope(string scope, List<string>? targetIds)
    {
        Scope = scope;
        TargetIds = targetIds;
    }

    public GranularScope(DebugTokenData.GranularScope granularScope)
        : this(granularScope.Scope, granularScope.TargetIds)
    {
    }
}

public class DecryptedBusinessIntegrationSystemUserAccessTokenDto
{
    [JsonProperty("decrypted_token")]
    public string DecryptedToken { get; set; }

    [JsonProperty("scopes")]
    public List<string> Scopes { get; set; }

    [JsonProperty("granular_scopes")]
    public List<GranularScope> GranularScopes { get; set; }

    [JsonProperty("facebook_business_system_user_id")]
    public string FacebookBusinessSystemUserId { get; set; }

    [JsonConstructor]
    public DecryptedBusinessIntegrationSystemUserAccessTokenDto(
        string decryptedToken,
        List<string> scopes,
        List<GranularScope> granularScopes,
        string facebookBusinessSystemUserId)
    {
        DecryptedToken = decryptedToken;
        Scopes = scopes;
        GranularScopes = granularScopes;
        FacebookBusinessSystemUserId = facebookBusinessSystemUserId;
    }

    public DecryptedBusinessIntegrationSystemUserAccessTokenDto(
        FacebookBusinessIntegrationSystemUserAccessToken facebookBusinessIntegrationSystemUserAccessToken,
        string facebookBusinessIntegrationSystemUserAccessTokenSecret)
        : this(
            AesUtils.AesDecryptBase64(
        facebookBusinessIntegrationSystemUserAccessToken.EncryptedToken,
        facebookBusinessIntegrationSystemUserAccessTokenSecret),
            facebookBusinessIntegrationSystemUserAccessToken.Scopes,
            facebookBusinessIntegrationSystemUserAccessToken.GranularScopes,
            facebookBusinessIntegrationSystemUserAccessToken.FacebookBusinessSystemUserId)
    {
    }
}