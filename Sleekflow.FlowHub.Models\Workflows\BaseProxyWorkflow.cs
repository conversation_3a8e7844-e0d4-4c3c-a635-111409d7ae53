using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Workflows;

public abstract class BaseProxyWorkflow
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonConstructor]
    protected BaseProxyWorkflow(string sleekflowCompanyId, string workflowId, string workflowVersionedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
    }
}