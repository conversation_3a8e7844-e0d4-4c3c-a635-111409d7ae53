using GraphApi.Client.ApiClients;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Payloads.Models;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi.TransactionLog;

[TestFixture]
public class WhatsappCloudApiConversationUsageTests
{
    private Mock<IIdService> _idServiceMock;
    private Mock<IAuditLogService> _auditLogServiceMock;
    private Mock<ICloudApiClients> _cloudApiClientsMock;
    private Mock<ILogger<BusinessBalanceTransactionLogService>> _loggerMock;
    private Mock<IBusinessBalanceTransactionLogValidator> _businessBalanceTransactionLogValidatorMock;
    private Mock<IBusinessBalanceTransactionLogRepository> _businessBalanceTransactionLogRepositoryMock;
    private Mock<IWabaService> _wabaServiceMock;
    private Mock<IHttpClientFactory> _httpClientFactoryMock;

    [SetUp]
    public void Setup()
    {
        _idServiceMock = new Mock<IIdService>();
        _auditLogServiceMock = new Mock<IAuditLogService>();
        _cloudApiClientsMock = new Mock<ICloudApiClients>();
        _cloudApiClientsMock.Setup(x => x.WhatsappCloudApiBspClient)
            .Returns(new Mock<IWhatsappCloudApiBspClient>().Object);
        _cloudApiClientsMock.Setup(x => x.WhatsappCloudApiAuthenticationClient)
            .Returns(new Mock<IWhatsappCloudApiAuthenticationClient>().Object);
        _cloudApiClientsMock.Setup(x => x.WhatsappCloudApiMessagingClient)
            .Returns(new Mock<IWhatsappCloudApiMessagingClient>().Object);
        _cloudApiClientsMock.Setup(x => x.FacebookCommerceClient).Returns(new Mock<IFacebookCommerceClient>().Object);
        _loggerMock = new Mock<ILogger<BusinessBalanceTransactionLogService>>();
        _businessBalanceTransactionLogValidatorMock = new Mock<IBusinessBalanceTransactionLogValidator>();
        _businessBalanceTransactionLogRepositoryMock = new Mock<IBusinessBalanceTransactionLogRepository>();
        _wabaServiceMock = new Mock<IWabaService>();
        _httpClientFactoryMock = new Mock<IHttpClientFactory>();
    }


    [Test]
    public void CloudApiUtils_SumConversationCategoryQuantities()
    {
        var conversationCategoryQuantitiesList = new List<Dictionary<string, int>>()
        {
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                },
            },
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                },
            },
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                },
            },
        };

        var result = CloudApiUtils.SumConversationCategoryQuantities(conversationCategoryQuantitiesList);
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION], Is.EqualTo(3));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.MARKETING], Is.EqualTo(6));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE], Is.EqualTo(6));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.UTILITY], Is.EqualTo(9));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.SERVICE], Is.EqualTo(12));
    }

    [Test]
    public void CloudApiUtils_SumConversationCategoryQuantities_2()
    {
        var conversationCategoryQuantitiesList = new List<Dictionary<string, int>>()
        {
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                },
                {
                    "new_conversation_category", 5
                },
            },
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    "new_conversation_category", 5
                },
            },
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                }
            },
        };

        var result = CloudApiUtils.SumConversationCategoryQuantities(conversationCategoryQuantitiesList);
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION], Is.EqualTo(3));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.MARKETING], Is.EqualTo(6));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.UTILITY], Is.EqualTo(9));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.SERVICE], Is.EqualTo(4));
        Assert.That(result["new_conversation_category"], Is.EqualTo(10));
    }

    [Test]
    public void CloudApiUtils_IsConversationCategoryQuantitiesEqual_2()
    {
        var conversationCategoryQuantitiesList = new List<Dictionary<string, int>>()
        {
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                },
                {
                    "new_conversation_category", 5
                },
            },
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    "new_conversation_category", 5
                },
            },
            new ()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    "new_conversation_category", 5
                },
            },
        };

        var result = CloudApiUtils.SumConversationCategoryQuantities(conversationCategoryQuantitiesList);
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION], Is.EqualTo(3));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.MARKETING], Is.EqualTo(6));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.UTILITY], Is.EqualTo(9));
        Assert.That(result[WhatsappConversationAnalyticConversationCategoryConst.SERVICE], Is.EqualTo(4));
        Assert.That(result["new_conversation_category"], Is.EqualTo(15));
    }

    [Test]
    public void CloudApiUtils_IsConversationCategoryQuantitiesEqual()
    {
        var conversationCategoryQuantities1 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                }
            };

        var conversationCategoryQuantities2 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                }
            };

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                conversationCategoryQuantities1,
                conversationCategoryQuantities2),
            Is.True);

        var conversationCategoryQuantities3 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 0
                }
            };

        var conversationCategoryQuantities4 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 0
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 0
                }
            };

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                conversationCategoryQuantities3,
                conversationCategoryQuantities4),
            Is.True);
    }

    [Test]
    public void CloudApiUtils_IsConversationCategoryQuantitiesEqual_Null_Check()
    {
        var conversationCategoryQuantities1 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                }
            };

        var conversationCategoryQuantities2 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                }
            };

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                conversationCategoryQuantities1,
                null),
            Is.False);

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                null,
                conversationCategoryQuantities2),
            Is.False);

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                null,
                null),
            Is.True);
    }

    [Test]
    public void CloudApiUtils_IsConversationCategoryQuantitiesEqual_UnMatch()
    {
        var conversationCategoryQuantities =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                }
            };

        var conversationCategoryQuantitiesUnMatch1 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 2 // UnMatch
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                },
            };

        var conversationCategoryQuantitiesUnMatch2 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                },
                {
                    "new_conversation_category", 5
                },
            };

        var conversationCategoryQuantitiesUnMatch3 =
            new Dictionary<string, int>()
            {
                {
                    WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 2
                },
                {
                    WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 3
                },

                // Missing category
                // {
                //     WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 4
                // },
            };

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                conversationCategoryQuantities,
                conversationCategoryQuantitiesUnMatch1),
            Is.False);

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                conversationCategoryQuantities,
                conversationCategoryQuantitiesUnMatch2),
            Is.False);

        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                conversationCategoryQuantities,
                conversationCategoryQuantitiesUnMatch3),
            Is.False);
    }

    [Test]
    public async Task
        BusinessBalanceTransactionLogService_ConstructWabaConversationUsageTransactionItem_New_Pricing_Without()
    {
        var dataPoints = JsonConvert.DeserializeObject<List<WhatsappConversationAnalyticsResultDataPoint>>(
            """
            [
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "AUTHENTICATION",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "MARKETING",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "MARKETING_LITE",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "UTILITY",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "SERVICE",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 20,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "SERVICE",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 30,
                    "conversation_type": "FREE_ENTRY_POINT",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "SERVICE",
                    "cost": 0
                }
            ]
            """);


        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object);

        var wabaConversationUsageTransactionItem =
            businessBalanceTransactionLogService.ConstructWabaConversationUsageTransactionItem(
                "1",
                1,
                dataPoints!);

        Assert.That(wabaConversationUsageTransactionItem.FacebookWabaId, Is.EqualTo("1"));
        Assert.That(wabaConversationUsageTransactionItem.StartTimestamp, Is.EqualTo(1682956800));
        Assert.That(wabaConversationUsageTransactionItem.EndTimestamp, Is.EqualTo(1682958600));
        Assert.That(
            wabaConversationUsageTransactionItem.Granularity,
            Is.EqualTo(WhatsappConversationAnalyticGranularityConst.HALF_HOUR));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedPaidQuantity, Is.EqualTo(40));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedFreeTierQuantity, Is.EqualTo(0));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedCost, Is.EqualTo(4));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedPaidQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedFreeTierQuantity, Is.EqualTo(20));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedFreeEntryPointQuantity, Is.EqualTo(30));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedCost, Is.EqualTo(1));
        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                wabaConversationUsageTransactionItem.ConversationCategoryQuantities,
                new Dictionary<string, int>()
                {
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 60
                    },
                }),
            Is.True);
        Assert.That(
            CloudApiUtils.IsConversationCategoryCostsEqual(
                wabaConversationUsageTransactionItem.ConversationCategoryCosts,
                new Dictionary<string, decimal>()
                {
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 1
                    },
                }),
            Is.True);

        var businessBalanceTransactionLog =
            businessBalanceTransactionLogService.ConstructBusinessBalanceTransactionLog(
                "1",
                "1",
                "54",
                new MarkupProfile(new Money(Currencies.Usd, 0), new Money(Currencies.Usd, 0)),
                wabaConversationUsageTransactionItem.StartTimestamp,
                wabaConversationUsageTransactionItem);

        Assert.That(businessBalanceTransactionLog.Used.Amount, Is.EqualTo(5));
        Assert.That(businessBalanceTransactionLog.Markup.Amount, Is.EqualTo(5m * 0.12m));
        Assert.That(businessBalanceTransactionLog.TransactionHandlingFee.Amount, Is.EqualTo(5m * 0.03m));
    }

    [Test]
    public async Task
        BusinessBalanceTransactionLogService_ConstructWabaConversationUsageTransactionItem_New_Pricing_Exclude_conversation_direction()
    {
        var dataPoints = JsonConvert.DeserializeObject<List<WhatsappConversationAnalyticsResultDataPoint>>(
            """
            [
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_category": "AUTHENTICATION",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_category": "MARKETING",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_category": "MARKETING_LITE",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_category": "UTILITY",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_category": "SERVICE",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_category": "SERVICE",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_ENTRY_POINT",
                    "conversation_category": "SERVICE",
                    "cost": 0
                }
            ]
            """);


        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object);

        var wabaConversationUsageTransactionItem =
            businessBalanceTransactionLogService.ConstructWabaConversationUsageTransactionItem(
                "1",
                1,
                dataPoints!);

        Assert.That(wabaConversationUsageTransactionItem.FacebookWabaId, Is.EqualTo("1"));
        Assert.That(wabaConversationUsageTransactionItem.StartTimestamp, Is.EqualTo(1682956800));
        Assert.That(wabaConversationUsageTransactionItem.EndTimestamp, Is.EqualTo(1682958600));
        Assert.That(
            wabaConversationUsageTransactionItem.Granularity,
            Is.EqualTo(WhatsappConversationAnalyticGranularityConst.HALF_HOUR));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedPaidQuantity, Is.EqualTo(40));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedFreeTierQuantity, Is.EqualTo(0));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedCost, Is.EqualTo(4));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedPaidQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedFreeTierQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedFreeEntryPointQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedCost, Is.EqualTo(1));
        Assert.That(
            CloudApiUtils.IsConversationCategoryQuantitiesEqual(
                wabaConversationUsageTransactionItem.ConversationCategoryQuantities,
                new Dictionary<string, int>()
                {
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 10
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 30
                    },
                }),
            Is.True);
        Assert.That(
            CloudApiUtils.IsConversationCategoryCostsEqual(
                wabaConversationUsageTransactionItem.ConversationCategoryCosts,
                new Dictionary<string, decimal>()
                {
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.AUTHENTICATION, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.MARKETING_LITE, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.UTILITY, 1
                    },
                    {
                        WhatsappConversationAnalyticConversationCategoryConst.SERVICE, 1
                    },
                }),
            Is.True);

        var businessBalanceTransactionLog =
            businessBalanceTransactionLogService.ConstructBusinessBalanceTransactionLog(
                "1",
                "1",
                "54",
                new MarkupProfile(new Money(Currencies.Usd, 0), new Money(Currencies.Usd, 0)),
                wabaConversationUsageTransactionItem.StartTimestamp,
                wabaConversationUsageTransactionItem);

        Assert.That(businessBalanceTransactionLog.Used.Amount, Is.EqualTo(5));
        Assert.That(businessBalanceTransactionLog.Markup.Amount, Is.EqualTo(5m * 0.12m));
        Assert.That(businessBalanceTransactionLog.TransactionHandlingFee.Amount, Is.EqualTo(5m * 0.03m));
    }

    [Test]
    public async Task
        BusinessBalanceTransactionLogService_ConstructWabaConversationUsageTransactionItem_Old_Pricing_Without()
    {
        var dataPoints = JsonConvert.DeserializeObject<List<WhatsappConversationAnalyticsResultDataPoint>>(
            """
            [
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "BUSINESS_INITIATED",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "BUSINESS_INITIATED",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_ENTRY_POINT",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 0
                }
            ]
            """);

        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object
        );

        var wabaConversationUsageTransactionItem =
            businessBalanceTransactionLogService.ConstructWabaConversationUsageTransactionItem(
                "1",
                1,
                dataPoints!);

        Assert.That(wabaConversationUsageTransactionItem.FacebookWabaId, Is.EqualTo("1"));
        Assert.That(wabaConversationUsageTransactionItem.StartTimestamp, Is.EqualTo(1682956800));
        Assert.That(wabaConversationUsageTransactionItem.EndTimestamp, Is.EqualTo(1682958600));
        Assert.That(
            wabaConversationUsageTransactionItem.Granularity,
            Is.EqualTo(WhatsappConversationAnalyticGranularityConst.HALF_HOUR));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedPaidQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedFreeTierQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.BusinessInitiatedCost, Is.EqualTo(1));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedPaidQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedFreeTierQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedFreeEntryPointQuantity, Is.EqualTo(10));
        Assert.That(wabaConversationUsageTransactionItem.UserInitiatedCost, Is.EqualTo(1));
        Assert.That(wabaConversationUsageTransactionItem.ConversationCategoryQuantities, Is.Null);
        Assert.That(wabaConversationUsageTransactionItem.ConversationCategoryCosts, Is.Null);

        var businessBalanceTransactionLog =
            businessBalanceTransactionLogService.ConstructBusinessBalanceTransactionLog(
                "1",
                "1",
                "54",
                new MarkupProfile(new Money(Currencies.Usd, 0), new Money(Currencies.Usd, 0)),
                wabaConversationUsageTransactionItem.StartTimestamp,
                wabaConversationUsageTransactionItem);

        Assert.That(businessBalanceTransactionLog.Used.Amount, Is.EqualTo(2));
        Assert.That(businessBalanceTransactionLog.Markup.Amount, Is.EqualTo(2m * 0.12m));
        Assert.That(businessBalanceTransactionLog.TransactionHandlingFee.Amount, Is.EqualTo(2m * 0.03m));
    }

    [Test]
    public void BusinessBalanceTransactionLogService_HasWabaConversationUsageModified_Old_Pricing()
    {
        var dataPoints = JsonConvert.DeserializeObject<List<WhatsappConversationAnalyticsResultDataPoint>>(
            """
            [
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "BUSINESS_INITIATED",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "BUSINESS_INITIATED",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_ENTRY_POINT",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 0
                }
            ]
            """);

        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object
        );

        var wabaConversationUsageTransactionItemLeft =
            businessBalanceTransactionLogService.ConstructWabaConversationUsageTransactionItem(
                "1",
                1,
                dataPoints!);

        var wabaConversationUsageTransactionItemRight =
            businessBalanceTransactionLogService.ConstructWabaConversationUsageTransactionItem(
                "1",
                1,
                dataPoints!);

        Assert.That(
            businessBalanceTransactionLogService.HasWabaConversationUsageModified(
                wabaConversationUsageTransactionItemLeft,
                wabaConversationUsageTransactionItemRight),
            Is.False);
    }

    [Test]
    public async Task BusinessBalanceTransactionLogService_HasWabaConversationUsageModified_New_Pricing()
    {
        var dataPoints = JsonConvert.DeserializeObject<List<WhatsappConversationAnalyticsResultDataPoint>>(
            """
            [
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "AUTHENTICATION",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "MARKETING",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "UTILITY",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "SERVICE",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "SERVICE",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_ENTRY_POINT",
                    "conversation_direction": "UNKNOWN",
                    "conversation_category": "SERVICE",
                    "cost": 0
                }
            ]
            """);

        var dataPoints2 = JsonConvert.DeserializeObject<List<WhatsappConversationAnalyticsResultDataPoint>>(
            """
            [
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "BUSINESS_INITIATED",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "REGULAR",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 1
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "BUSINESS_INITIATED",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_TIER",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 0
                },
                {
                    "start": 1682956800,
                    "end": 1682958600,
                    "conversation": 10,
                    "conversation_type": "FREE_ENTRY_POINT",
                    "conversation_direction": "USER_INITIATED",
                    "cost": 0
                }
            ]
            """);

        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object);

        var wabaConversationUsageTransactionItemLeft =
            businessBalanceTransactionLogService.ConstructWabaConversationUsageTransactionItem(
                "1",
                1,
                dataPoints!);

        var wabaConversationUsageTransactionItemRight =
            businessBalanceTransactionLogService.ConstructWabaConversationUsageTransactionItem(
                "1",
                1,
                dataPoints2!);

        Assert.That(
            businessBalanceTransactionLogService.HasWabaConversationUsageModified(
                wabaConversationUsageTransactionItemLeft,
                wabaConversationUsageTransactionItemRight),
            Is.True);
    }

    [Test]
    [TestCase(8)]
    [TestCase(-5)]
    [TestCase(0)]
    public void BusinessBalanceTransactionLogService_GetPeriodForDifferentPricing_Both_Pricing_Included(
        int timezoneOffsetHour)
    {
        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object);

        var timezoneOffset = TimeSpan.FromHours(timezoneOffsetHour);
        var newPricingTimezoneOffsetTimestamp = (long) (BusinessBalanceTransactionLogService.NewPricingUtcTimestamp -
                                                        timezoneOffset.TotalSeconds);
        var start = new DateTimeOffset(2023, 5, 20, 0, 0, 0, timezoneOffset).ToUnixTimeSeconds();
        var end = new DateTimeOffset(2023, 6, 20, 0, 0, 0, timezoneOffset).ToUnixTimeSeconds();

        var periodForDifferentPricing =
            businessBalanceTransactionLogService.GetPeriodForDifferentPricing(
                timezoneOffset,
                start,
                end);

        Assert.That(periodForDifferentPricing.DirectionBased, Is.Not.Null);
        Assert.That(periodForDifferentPricing.CategoryBased, Is.Not.Null);

        Assert.That(periodForDifferentPricing.DirectionBased.Value.Start, Is.EqualTo(start));
        Assert.That(
            periodForDifferentPricing.DirectionBased.Value.End,
            Is.EqualTo(newPricingTimezoneOffsetTimestamp));

        Assert.That(
            periodForDifferentPricing.CategoryBased.Value.Start,
            Is.EqualTo(newPricingTimezoneOffsetTimestamp));
        Assert.That(periodForDifferentPricing.CategoryBased.Value.End, Is.EqualTo(end));
    }

    [Test]
    [TestCase(8)]
    [TestCase(-5)]
    [TestCase(0)]
    public void BusinessBalanceTransactionLogService_GetPeriodForDifferentPricing_DirectionBased_Pricing_Included(
        int timezoneOffsetHour)
    {
        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object);

        var timezoneOffset = TimeSpan.FromHours(timezoneOffsetHour);
        var start = new DateTimeOffset(2023, 5, 20, 0, 0, 0, timezoneOffset).ToUnixTimeSeconds();
        var end = new DateTimeOffset(2023, 5, 31, 0, 0, 0, timezoneOffset).ToUnixTimeSeconds();

        var periodForDifferentPricing =
            businessBalanceTransactionLogService.GetPeriodForDifferentPricing(
                timezoneOffset,
                start,
                end);

        Assert.That(periodForDifferentPricing.DirectionBased, Is.Not.Null);
        Assert.That(periodForDifferentPricing.CategoryBased, Is.Null);

        Assert.That(periodForDifferentPricing.DirectionBased.Value.Start, Is.EqualTo(start));
        Assert.That(periodForDifferentPricing.DirectionBased.Value.End, Is.EqualTo(end));
    }

    [Test]
    [TestCase(8)]
    [TestCase(-5)]
    [TestCase(0)]
    public void BusinessBalanceTransactionLogService_GetPeriodForDifferentPricing_CategoryBased_Pricing_Included(
        int timezoneOffsetHour)
    {
        var businessBalanceTransactionLogService = new BusinessBalanceTransactionLogService(
            _idServiceMock.Object,
            _auditLogServiceMock.Object,
            _cloudApiClientsMock.Object,
            _loggerMock.Object,
            _businessBalanceTransactionLogValidatorMock.Object,
            _businessBalanceTransactionLogRepositoryMock.Object,
            _wabaServiceMock.Object,
            _httpClientFactoryMock.Object);

        var timezoneOffset = TimeSpan.FromHours(timezoneOffsetHour);
        var start = new DateTimeOffset(2023, 6, 1, 0, 0, 0, timezoneOffset).ToUnixTimeSeconds();
        var end = new DateTimeOffset(2023, 6, 30, 0, 0, 0, timezoneOffset).ToUnixTimeSeconds();

        var periodForDifferentPricing =
            businessBalanceTransactionLogService.GetPeriodForDifferentPricing(
                timezoneOffset,
                start,
                end);

        Assert.That(periodForDifferentPricing.CategoryBased, Is.Not.Null);
        Assert.That(periodForDifferentPricing.DirectionBased, Is.Null);

        Assert.That(periodForDifferentPricing.CategoryBased.Value.Start, Is.EqualTo(start));
        Assert.That(periodForDifferentPricing.CategoryBased.Value.End, Is.EqualTo(end));
    }
}