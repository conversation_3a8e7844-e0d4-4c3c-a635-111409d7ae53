﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Models.UserMappingConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Salesforce.UserMappingConfigs;

public interface ISalesforceUserMappingConfigService
{
    Task<SalesforceUserMappingConfig> GetAsync(
        string sleekflowCompanyId,
        string connectionId);

    Task<SalesforceUserMappingConfig> GetByIdAsync(
        string id,
        string sleekflowCompanyId);

    Task<SalesforceUserMappingConfig> CreateAndGetAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings);

    Task<SalesforceUserMappingConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings);

    Task ClearAsync(string connectionId, string sleekflowCompanyId);
}

public class SalesforceUserMappingConfigService : ISingletonService, ISalesforceUserMappingConfigService
{
    private readonly ISalesforceUserMappingConfigRepository _salesforceUserMappingConfigRepository;
    private readonly IIdService _idService;

    public SalesforceUserMappingConfigService(
        ISalesforceUserMappingConfigRepository salesforceUserMappingConfigRepository,
        IIdService idService)
    {
        _salesforceUserMappingConfigRepository = salesforceUserMappingConfigRepository;
        _idService = idService;
    }

    public async Task<SalesforceUserMappingConfig> GetAsync(
        string sleekflowCompanyId,
        string connectionId)
    {
        var userMappingConfig = (await _salesforceUserMappingConfigRepository.GetObjectsAsync(
            c => c.SleekflowCompanyId == sleekflowCompanyId
                 && c.ConnectionId == connectionId)).FirstOrDefault();

        if (userMappingConfig is null)
        {
            throw new SfNotFoundObjectException(connectionId, sleekflowCompanyId);
        }

        return userMappingConfig;
    }

    public async Task<SalesforceUserMappingConfig> GetByIdAsync(
        string id,
        string sleekflowCompanyId)
    {
        return await _salesforceUserMappingConfigRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<SalesforceUserMappingConfig> CreateAndGetAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings)
    {
        var userMappingConfig = new SalesforceUserMappingConfig(
            _idService.GetId("SalesforceUserMappingConfig"),
            sleekflowCompanyId,
            connectionId,
            userMappings);

        return await _salesforceUserMappingConfigRepository.CreateAndGetAsync(
            userMappingConfig,
            sleekflowCompanyId);
    }

    public async Task<SalesforceUserMappingConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings)
    {
        return await _salesforceUserMappingConfigRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/user_mappings", userMappings)
            });
    }

    public async Task ClearAsync(string connectionId, string sleekflowCompanyId)
    {
        await foreach (var config in _salesforceUserMappingConfigRepository.GetObjectEnumerableAsync(
                           c =>
                               c.SysTypeName == "UserMappingConfig"
                               && c.ConnectionId == connectionId
                               && c.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _salesforceUserMappingConfigRepository.DeleteAsync(
                config.Id,
                config.SleekflowCompanyId);
        }
    }
}