openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7074
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/user-event-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/user-event-hub
    description: Prod Apigw
paths:
  /Associations/AreGroupsConnected:
    post:
      tags:
        - Associations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AreGroupsConnectedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreGroupsConnectedOutputOutput'
  /Associations/AreUsersConnected:
    post:
      tags:
        - Associations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AreUsersConnectedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AreUsersConnectedOutputOutput'
  /Associations/IsGroupConnected:
    post:
      tags:
        - Associations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsGroupConnectedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsGroupConnectedOutputOutput'
  /Associations/IsUserConnected:
    post:
      tags:
        - Associations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsUserConnectedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsUserConnectedOutputOutput'
  /authorized/Notifications/DeleteDeviceInstallation:
    post:
      tags:
        - AuthorizedNotifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteDeviceInstallationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteDeviceInstallationOutputOutput'
  /authorized/Notifications/GetNotificationInstallation:
    post:
      tags:
        - AuthorizedNotifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetNotificationInstallationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetNotificationInstallationOutputOutput'
  /authorized/Notifications/GetNotificationSettings:
    post:
      tags:
        - AuthorizedNotifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetNotificationSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetNotificationSettingsOutputOutput'
  /authorized/Notifications/RegisterAndEnableMobilePushNotification:
    post:
      tags:
        - AuthorizedNotifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterAndEnableMobilePushNotificationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterAndEnableMobilePushNotificationOutputOutput'
  /authorized/Notifications/UnregisterMobilePushNotification:
    post:
      tags:
        - AuthorizedNotifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UnregisterMobilePushNotificationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnregisterMobilePushNotificationOutputOutput'
  /authorized/Notifications/UpdateNotificationSettingsByPlatform:
    post:
      tags:
        - AuthorizedNotifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateNotificationSettingsByPlatformInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateNotificationSettingsByPlatformOutputOutput'
  /authorized/SqlJobs/GetSqlJob:
    post:
      tags:
        - AuthorizedSqlJobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSqlJobInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSqlJobOutputOutput'
  /authorized/SqlJobs/GetSqlJobPaginatedResult:
    post:
      tags:
        - AuthorizedSqlJobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSqlJobPaginatedResultInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSqlJobPaginatedResultOutputOutput'
  /authorized/SqlJobs/GetSqlJobResultSasUrl:
    post:
      tags:
        - AuthorizedSqlJobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSqlJobResultSasUrlInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSqlJobResultSasUrlOutputOutput'
  /authorized/SqlJobs/GetSqlJobs:
    post:
      tags:
        - AuthorizedSqlJobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSqlJobsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSqlJobsOutputOutput'
  /authorized/SqlJobs/SubmitShortSqlJob:
    post:
      tags:
        - AuthorizedSqlJobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitShortSqlJobInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitShortSqlJobOutputOutput'
  /authorized/SqlJobs/SubmitSqlJob:
    post:
      tags:
        - AuthorizedSqlJobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitSqlJobInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitSqlJobOutputOutput'
  /authorized/UserEvents/CaptureUserEvent:
    post:
      tags:
        - AuthorizedUserEvents
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CaptureUserEventInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CaptureUserEventOutputOutput'
  /authorized/UserEventTypes/CreateUserEventType:
    post:
      tags:
        - AuthorizedUserEventTypes
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserEventTypeInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUserEventTypeOutputOutput'
  /authorized/UserEventTypes/DeleteUserEventTypes:
    post:
      tags:
        - AuthorizedUserEventTypes
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteUserEventTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteUserEventTypesOutputOutput'
  /authorized/UserEventTypes/GetUserEventTypes:
    post:
      tags:
        - AuthorizedUserEventTypes
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserEventTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserEventTypesOutputOutput'
  /authorized/UserEventTypes/UpdateUserEventType:
    post:
      tags:
        - AuthorizedUserEventTypes
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserEventTypeInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateUserEventTypeOutputOutput'
  /Messages/RedeliverSessionUnackedMessages:
    post:
      tags:
        - Messages
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RedeliverSessionUnackedMessagesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RedeliverSessionUnackedMessagesOutputOutput'
  /Messages/SendMessageToGroup:
    post:
      tags:
        - Messages
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendMessageToGroupInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendMessageToGroupOutputOutput'
  /Messages/SendMessageToUser:
    post:
      tags:
        - Messages
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendMessageToUserInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendMessageToUserOutputOutput'
  /Messages/SendMessageToUsers:
    post:
      tags:
        - Messages
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendMessageToUsersInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendMessageToUsersOutputOutput'
  /Notifications/GetAllInstallationIds:
    post:
      tags:
        - Notifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAllInstallationIdsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllInstallationIdsOutputOutput'
  /Notifications/GetInstallation:
    post:
      tags:
        - Notifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetInstallationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetInstallationOutputOutput'
  /Notifications/GetRegistration:
    post:
      tags:
        - Notifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetRegistrationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRegistrationOutputOutput'
  /Notifications/SendPushNotification:
    post:
      tags:
        - Notifications
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendPushNotificationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendPushNotificationOutputOutput'
  /ReliableMessage/negotiate:
    post:
      tags:
        - SignalRNegotiate
      parameters:
        - name: data
          in: query
          schema:
            type: string
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /SignalRWebhook:
    post:
      tags:
        - SignalRWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
components:
  schemas:
    AreGroupsConnectedInput:
      required:
        - user_ids
      type: object
      properties:
        user_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    AreGroupsConnectedOutput:
      type: object
      properties:
        group_id_to_is_connected_object_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/IsConnectedObject'
          nullable: true
      additionalProperties: false
    AreGroupsConnectedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AreGroupsConnectedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AreUsersConnectedInput:
      required:
        - user_ids
      type: object
      properties:
        user_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    AreUsersConnectedOutput:
      type: object
      properties:
        user_id_to_is_connected_object_dict:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/IsConnectedObject'
          nullable: true
      additionalProperties: false
    AreUsersConnectedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AreUsersConnectedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    BroadcastMessageStatus:
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
      type: integer
      format: int32
    CaptureUserEventInput:
      required:
        - event_type
        - metadata
        - object_id
        - object_type
        - properties
        - source
      type: object
      properties:
        event_type:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        source:
          minLength: 1
          type: string
        properties:
          $ref: '#/components/schemas/UserEventProperties'
        metadata:
          $ref: '#/components/schemas/UserEventMetadata'
      additionalProperties: false
    CaptureUserEventOutput:
      type: object
      additionalProperties: false
    CaptureUserEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CaptureUserEventOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Condition:
      type: object
      properties:
        containHashTag:
          type: string
          nullable: true
        fieldName:
          type: string
          nullable: true
        conditionOperator:
          $ref: '#/components/schemas/SupportedOperator'
        values:
          type: array
          items:
            type: string
          nullable: true
        timeValueType:
          $ref: '#/components/schemas/TimeValueType'
        nextOperator:
          $ref: '#/components/schemas/SupportedNextOperator'
        companyMessageTemplateId:
          type: string
          nullable: true
        broadcastMessageStatus:
          $ref: '#/components/schemas/BroadcastMessageStatus'
      additionalProperties: false
    CreateUserEventTypeInput:
      required:
        - event_type
      type: object
      properties:
        event_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateUserEventTypeOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateUserEventTypeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateUserEventTypeOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteDeviceInstallationInput:
      required:
        - deviceId
      type: object
      properties:
        deviceId:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteDeviceInstallationOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    DeleteDeviceInstallationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteDeviceInstallationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteUserEventTypesInput:
      required:
        - ids
      type: object
      properties:
        ids:
          type: array
          items:
            type: string
      additionalProperties: false
    DeleteUserEventTypesOutput:
      type: object
      additionalProperties: false
    DeleteUserEventTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteUserEventTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetAllInstallationIdsInput:
      type: object
      additionalProperties: false
    GetAllInstallationIdsOutput:
      type: object
      properties:
        installationIds:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetAllInstallationIdsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAllInstallationIdsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetInstallationInput:
      required:
        - installationId
      type: object
      properties:
        installationId:
          minLength: 1
          type: string
      additionalProperties: false
    GetInstallationOutput:
      type: object
      properties:
        installation:
          $ref: '#/components/schemas/Installation'
      additionalProperties: false
    GetInstallationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetInstallationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetNotificationInstallationInput:
      required:
        - installationId
      type: object
      properties:
        installationId:
          minLength: 1
          type: string
      additionalProperties: false
    GetNotificationInstallationOutput:
      type: object
      properties:
        installation:
          $ref: '#/components/schemas/Installation'
      additionalProperties: false
    GetNotificationInstallationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetNotificationInstallationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetNotificationSettingsInput:
      type: object
      additionalProperties: false
    GetNotificationSettingsOutput:
      type: object
      properties:
        notification_settings:
          $ref: '#/components/schemas/NotificationSettingsDto'
      additionalProperties: false
    GetNotificationSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetNotificationSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetRegistrationInput:
      required:
        - registrationId
      type: object
      properties:
        registrationId:
          minLength: 1
          type: string
      additionalProperties: false
    GetRegistrationOutput:
      type: object
      properties:
        registration:
          $ref: '#/components/schemas/RegistrationDescription'
      additionalProperties: false
    GetRegistrationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetRegistrationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSqlJobInput:
      required:
        - job_id
      type: object
      properties:
        job_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSqlJobOutput:
      type: object
      properties:
        sql_job:
          $ref: '#/components/schemas/SqlJobDto'
      additionalProperties: false
    GetSqlJobOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSqlJobOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSqlJobPaginatedResultInput:
      required:
        - job_id
        - limit
        - offset
      type: object
      properties:
        job_id:
          minLength: 1
          type: string
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
        offset:
          maximum: 5000
          minimum: 0
          type: integer
          format: int32
      additionalProperties: false
    GetSqlJobPaginatedResultOutput:
      type: object
      properties:
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
      additionalProperties: false
    GetSqlJobPaginatedResultOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSqlJobPaginatedResultOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSqlJobResultSasUrlInput:
      required:
        - job_id
      type: object
      properties:
        job_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSqlJobResultSasUrlOutput:
      type: object
      properties:
        url:
          type: string
          nullable: true
      additionalProperties: false
    GetSqlJobResultSasUrlOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSqlJobResultSasUrlOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSqlJobsInput:
      required:
        - limit
      type: object
      properties:
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetSqlJobsOutput:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        records:
          type: array
          items:
            $ref: '#/components/schemas/SqlJobDto'
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetSqlJobsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSqlJobsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserEventTypesInput:
      required:
        - limit
      type: object
      properties:
        continuation_token:
          maxLength: 16384
          minLength: 1
          type: string
          nullable: true
        limit:
          maximum: 200
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetUserEventTypesOutput:
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        records:
          type: array
          items:
            $ref: '#/components/schemas/UserEventTypeDto'
          nullable: true
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetUserEventTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserEventTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserProfileIdsInput:
      required:
        - conditions
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
      additionalProperties: false
    GetUserProfileIdsOutput:
      type: object
      properties:
        user_profile_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    Installation:
      type: object
      properties:
        installationId:
          type: string
          nullable: true
        userId:
          type: string
          nullable: true
        pushChannel:
          type: string
          nullable: true
        pushChannelExpired:
          type: boolean
          nullable: true
        platform:
          $ref: '#/components/schemas/NotificationPlatform'
        expirationTime:
          type: string
          format: date-time
          nullable: true
        tags:
          type: array
          items:
            type: string
          nullable: true
        pushVariables:
          type: object
          additionalProperties:
            type: string
          nullable: true
        templates:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/InstallationTemplate'
          nullable: true
        secondaryTiles:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/WnsSecondaryTile'
          nullable: true
          deprecated: true
      additionalProperties: false
    InstallationTemplate:
      type: object
      properties:
        body:
          type: string
          nullable: true
        headers:
          type: object
          additionalProperties:
            type: string
          nullable: true
        expiry:
          type: string
          nullable: true
        tags:
          type: array
          items:
            type: string
          nullable: true
          deprecated: true
      additionalProperties: false
    IsConnectedObject:
      type: object
      properties:
        is_connected:
          type: boolean
        num_of_sessions:
          type: integer
          format: int32
      additionalProperties: false
    IsGroupConnectedInput:
      required:
        - group_id
      type: object
      properties:
        group_id:
          minLength: 1
          type: string
      additionalProperties: false
    IsGroupConnectedOutput:
      type: object
      properties:
        is_connected:
          type: boolean
        num_of_sessions:
          type: integer
          format: int32
      additionalProperties: false
    IsGroupConnectedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsGroupConnectedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsUserConnectedInput:
      required:
        - user_id
      type: object
      properties:
        user_id:
          minLength: 1
          type: string
      additionalProperties: false
    IsUserConnectedOutput:
      type: object
      properties:
        is_connected:
          type: boolean
        num_of_sessions:
          type: integer
          format: int32
      additionalProperties: false
    IsUserConnectedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsUserConnectedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    MobileNotificationSettingsDto:
      type: object
      properties:
        banner:
          type: boolean
        ios_sound:
          type: string
          nullable: true
        android_sound:
          type: string
          nullable: true
        badge:
          type: boolean
        is_notification_enabled:
          type: boolean
        enabled_notification_events:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    NotificationPlatform:
      enum:
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 9
      type: integer
      format: int32
    NotificationSettingsDto:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        web:
          $ref: '#/components/schemas/PlatformSpecificNotificationSettingsDto'
        mobile:
          $ref: '#/components/schemas/MobileNotificationSettingsDto'
        id:
          type: string
          nullable: true
      additionalProperties: false
    PlatformSpecificNotificationSettingsDto:
      type: object
      properties:
        is_notification_enabled:
          type: boolean
        enabled_notification_events:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    RedeliverSessionUnackedMessagesInput:
      required:
        - session_id
      type: object
      properties:
        session_id:
          minLength: 1
          type: string
      additionalProperties: false
    RedeliverSessionUnackedMessagesOutput:
      type: object
      additionalProperties: false
    RedeliverSessionUnackedMessagesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RedeliverSessionUnackedMessagesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RegisterAndEnableMobilePushNotificationInput:
      required:
        - handle
        - tags
      type: object
      properties:
        handle:
          minLength: 1
          type: string
        tags:
          type: array
          items:
            type: string
        deviceId:
          type: string
          nullable: true
        platform:
          type: string
          nullable: true
      additionalProperties: false
    RegisterAndEnableMobilePushNotificationOutput:
      type: object
      properties:
        notification_settings:
          $ref: '#/components/schemas/NotificationSettingsDto'
      additionalProperties: false
    RegisterAndEnableMobilePushNotificationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RegisterAndEnableMobilePushNotificationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RegistrationDescription:
      type: object
      properties:
        ETag:
          type: string
          nullable: true
        ExpirationTime:
          type: string
          format: date-time
          nullable: true
        RegistrationId:
          type: string
          nullable: true
        Tags:
          type: string
        PushVariables:
          type: string
      additionalProperties: false
    SendMessageToGroupInput:
      required:
        - group_id
        - message_object
        - message_type
      type: object
      properties:
        group_id:
          minLength: 1
          type: string
        message_type:
          minLength: 1
          type: string
        message_object: { }
      additionalProperties: false
    SendMessageToGroupOutput:
      type: object
      additionalProperties: false
    SendMessageToGroupOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SendMessageToGroupOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SendMessageToUserInput:
      required:
        - message_object
        - message_type
        - user_id
      type: object
      properties:
        user_id:
          minLength: 1
          type: string
        message_type:
          minLength: 1
          type: string
        message_object: { }
      additionalProperties: false
    SendMessageToUserOutput:
      type: object
      additionalProperties: false
    SendMessageToUserOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SendMessageToUserOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SendMessageToUsersInput:
      required:
        - message_object
        - message_type
        - user_ids
      type: object
      properties:
        user_ids:
          type: array
          items:
            type: string
        message_type:
          minLength: 1
          type: string
        message_object: { }
      additionalProperties: false
    SendMessageToUsersOutput:
      type: object
      additionalProperties: false
    SendMessageToUsersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SendMessageToUsersOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SendPushNotificationInput:
      required:
        - notification_event
        - tags
      type: object
      properties:
        notification_event:
          minLength: 1
          type: string
        tags:
          type: array
          items:
            type: string
        title:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        badge:
          type: integer
          format: int32
          nullable: true
        conversation_id:
          type: string
          nullable: true
        company_id:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
      additionalProperties: false
    SendPushNotificationOutput:
      type: object
      additionalProperties: false
    SendPushNotificationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SendPushNotificationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    SqlJobDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sql_query:
          type: string
          nullable: true
        sql_job_status:
          type: string
          nullable: true
        submitted_at:
          type: string
          format: date-time
        started_at:
          type: string
          format: date-time
          nullable: true
        completed_at:
          type: string
          format: date-time
          nullable: true
        result_account_name:
          type: string
          nullable: true
        result_container_name:
          type: string
          nullable: true
        error_message:
          type: string
          nullable: true
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
          nullable: true
        id:
          type: string
          nullable: true
      additionalProperties: false
    SubmitShortSqlJobInput:
      required:
        - conditions
        - sql_query
      type: object
      properties:
        sql_query:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
      additionalProperties: false
    SubmitShortSqlJobOutput:
      type: object
      properties:
        records:
          type: array
          items:
            type: object
            additionalProperties: { }
          nullable: true
      additionalProperties: false
    SubmitShortSqlJobOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SubmitShortSqlJobOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SubmitSqlJobInput:
      required:
        - conditions
        - sql_query
      type: object
      properties:
        sql_query:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/Condition'
      additionalProperties: false
    SubmitSqlJobOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    SubmitSqlJobOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SubmitSqlJobOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SupportedNextOperator:
      enum:
        - 0
        - 1
      type: integer
      format: int32
    SupportedOperator:
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
        - 8
        - 9
        - 10
        - 11
        - 12
        - 13
        - 14
        - 15
        - 16
        - 17
        - 18
        - 19
        - 20
        - 21
        - 22
        - 23
        - 24
        - 25
        - 26
        - 500
        - 501
        - 502
        - 503
        - 504
        - 505
        - 506
        - 507
        - 509
        - 510
        - 511
        - 512
        - 513
        - 514
        - 515
        - 516
        - 517
        - 518
      type: integer
      format: int32
    TimeValueType:
      enum:
        - 0
        - 1
        - 2
        - 3
      type: integer
      format: int32
    UnregisterMobilePushNotificationInput:
      type: object
      additionalProperties: false
    UnregisterMobilePushNotificationOutput:
      type: object
      additionalProperties: false
    UnregisterMobilePushNotificationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UnregisterMobilePushNotificationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateNotificationSettingsByPlatformInput:
      required:
        - platform
      type: object
      properties:
        platform:
          minLength: 1
          type: string
        is_notification_enabled:
          type: boolean
          nullable: true
        banner:
          type: boolean
          nullable: true
        android_sound:
          type: string
          nullable: true
        ios_sound:
          type: string
          nullable: true
        badge:
          type: boolean
          nullable: true
        enabled_notification_events:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateNotificationSettingsByPlatformOutput:
      type: object
      properties:
        notification_settings:
          $ref: '#/components/schemas/NotificationSettingsDto'
      additionalProperties: false
    UpdateNotificationSettingsByPlatformOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateNotificationSettingsByPlatformOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateUserEventTypeInput:
      required:
        - id
        - updated_event_type
      type: object
      properties:
        id:
          minLength: 1
          type: string
        updated_event_type:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateUserEventTypeOutput:
      type: object
      additionalProperties: false
    UpdateUserEventTypeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateUserEventTypeOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UserEventMetadata:
      type: object
      properties:
        source_workflow_id:
          type: string
          nullable: true
        source_workflow_versioned_id:
          type: string
          nullable: true
        source_channel:
          type: string
          nullable: true
        source_device:
          type: string
          nullable: true
        source_location:
          type: string
          nullable: true
        source_ip_address:
          type: string
          nullable: true
      additionalProperties: false
    UserEventProperties:
      type: object
      properties:
        campaign_id:
          type: string
          nullable: true
      additionalProperties: false
    UserEventTypeDto:
      type: object
      properties:
        event_type:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    WnsSecondaryTile:
      required:
        - pushChannel
      type: object
      properties:
        pushChannel:
          type: string
        pushChannelExpired:
          type: boolean
          nullable: true
        tags:
          type: array
          items:
            type: string
          nullable: true
        templates:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/InstallationTemplate'
          nullable: true
      additionalProperties: false