using System.Reflection;

namespace Sleekflow.MessagingHub.Extensions;

public static class ObjectExtension
{
    public static Dictionary<string, object?> AsDictionary(
        this object source,
        BindingFlags bindingAttr = BindingFlags.DeclaredOnly | BindingFlags.Public | BindingFlags.Instance)
    {
        return source.GetType().GetProperties(bindingAttr).ToDictionary(
            propInfo => propInfo.Name,
            propInfo => propInfo.GetValue(source, null));
    }
}