using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;
using Sleekflow.DistributedInvocations;
using Sleekflow.Outputs;

namespace Sleekflow.AuditHub.Tests;

public class UserProfileAuditLogTests
{
    [Test]
    public async Task Test_CreateStaffManualAddedLog()
    {
        const string mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        const string mockUserProfileId = "a5005219-8f9b-4362-bd45-b80007ea67a9";
        const string mockStaffId = "1";

        // /AuditLogs/CreateStaffManualAddedLog
        var createStaffManualAddedLogInput = new CreateStaffManualAddedLog.CreateStaffManualAddedLogInput(
            mockCompanyId,
            mockUserProfileId,
            mockStaffId,
            "Hello World",
            null);

        var createStaffManualAddedLogScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.WithRequestHeader(
                    IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
                    "eyJzbGVla2Zsb3dfY29tcGFueV9pZCI6ImI2ZDdlNDQyLTM4YWUtNGI5YS1iMTAwLTI5NTE3Mjk3NjhiYyIsInNsZWVrZmxvd19zdGFmZl9pZCI6IjEiLCJzbGVla2Zsb3dfc3RhZmZfdGVhbV9pZHMiOlsiMSJdfQ==");
                _.Post.Json(createStaffManualAddedLogInput).ToUrl("/AuditLogs/CreateStaffManualAddedLog");
            });
        var createStaffManualAddedLogOutput =
            await createStaffManualAddedLogScenarioResult.ReadAsJsonAsync<
                Output<CreateStaffManualAddedLog.CreateStaffManualAddedLogOutput>>();

        Assert.That(createStaffManualAddedLogOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task Test_CreateAutomationTriggeredLog()
    {
        const string mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        const string mockUserProfileId = "a5005219-8f9b-4362-bd45-b80007ea67a9";
        const string mockStaffId = "1";

        // /AuditLogs/CreateAutomationTriggeredLog
        var createAutomationTriggeredLogInput = new CreateAutomationTriggeredLog.CreateAutomationTriggeredLogInput(
            mockCompanyId,
            mockUserProfileId,
            mockStaffId,
            "testing",
            new AutomationTriggeredLogData("testingrule", "send message", 15, "success", null));

        var createAutomationTriggeredLogScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.WithRequestHeader(
                    IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
                    "eyJzbGVla2Zsb3dfY29tcGFueV9pZCI6ImI2ZDdlNDQyLTM4YWUtNGI5YS1iMTAwLTI5NTE3Mjk3NjhiYyIsInNsZWVrZmxvd19zdGFmZl9pZCI6IjEiLCJzbGVla2Zsb3dfc3RhZmZfdGVhbV9pZHMiOlsiMSJdfQ==");
                _.Post.Json(createAutomationTriggeredLogInput).ToUrl("/AuditLogs/CreateAutomationTriggeredLog");
            });
        var createAutomationTriggeredLogOutput =
            await createAutomationTriggeredLogScenarioResult.ReadAsJsonAsync<
                Output<CreateAutomationTriggeredLog.CreateAutomationTriggeredLogOutput>>();

        Assert.That(createAutomationTriggeredLogOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task Test_CreateConversationAssignedTeamChangedLog()
    {
        const string mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        const string mockUserProfileId = "a5005219-8f9b-4362-bd45-b80007ea67a9";
        const string mockStaffId = "1";

        // /AuditLogs/CreateConversationAssignedTeamChangedLog
        var createConversationAssignedTeamChangedLogInput =
            new CreateConversationAssignedTeamChangedLog.CreateConversationAssignedTeamChangedLogInput(
                mockCompanyId,
                mockUserProfileId,
                mockStaffId,
                "Test Logging Text",
                new AssignedTeamChangedLogData(
                    new TeamLogData(15, "peter"),
                    new TeamLogData(16, "paul")));

        var createConversationAssignedTeamChangedLogScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.WithRequestHeader(
                    IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
                    "eyJzbGVla2Zsb3dfY29tcGFueV9pZCI6ImI2ZDdlNDQyLTM4YWUtNGI5YS1iMTAwLTI5NTE3Mjk3NjhiYyIsInNsZWVrZmxvd19zdGFmZl9pZCI6IjEiLCJzbGVla2Zsb3dfc3RhZmZfdGVhbV9pZHMiOlsiMSJdfQ==");
                _
                    .Post
                    .Json(createConversationAssignedTeamChangedLogInput)
                    .ToUrl("/AuditLogs/CreateConversationAssignedTeamChangedLog");
            });
        var createConversationAssignedTeamChangedLogOutputOutput =
            await createConversationAssignedTeamChangedLogScenarioResult.ReadAsJsonAsync<
                Output<CreateConversationAssignedTeamChangedLog.CreateConversationAssignedTeamChangedLogOutput>>();

        Assert.That(createConversationAssignedTeamChangedLogOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task Test_GetUserProfileAuditLogsV2()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var mockUserProfileId = "a5005219-8f9b-4362-bd45-b80007ea67a9";

        // /AuditLogs/GetUserProfileAuditLogsV2
        var getUserProfileAuditLogsV2Input =
            new GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2Input(
                mockCompanyId,
                mockUserProfileId,
                null,
                new GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2InputFilters(
                    null,
                    null),
                10);

        var getUserProfileAuditLogsV2ScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getUserProfileAuditLogsV2Input).ToUrl("/AuditLogs/GetUserProfileAuditLogsV2");
            });
        var getUserProfileAuditLogsV2OutputOutput =
            await getUserProfileAuditLogsV2ScenarioResult.ReadAsJsonAsync<
                Output<GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2Output>>();
        var getUserProfileAuditLogsV2Output = getUserProfileAuditLogsV2OutputOutput!.Data;

        Assert.That(getUserProfileAuditLogsV2OutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getUserProfileAuditLogsV2Output.UserProfileAuditLogs.Count, Is.GreaterThan(0));
    }

    [Test]
    public async Task Test_GetUserProfileAuditLogsV2_Filters()
    {
        const string mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        const string mockUserProfileId = "a5005219-8f9b-4362-bd45-b80007ea67a9";

        // /AuditLogs/GetUserProfileAuditLogsV2
        var getUserProfileAuditLogsV2Input1 =
            new GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2Input(
                mockCompanyId,
                mockUserProfileId,
                null,
                new GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2InputFilters(
                    new List<string>()
                    {
                        "automation-triggered", "conversation-read"
                    },
                    null),
                10);

        var getUserProfileAuditLogsV2ScenarioResult1 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getUserProfileAuditLogsV2Input1).ToUrl("/AuditLogs/GetUserProfileAuditLogsV2");
            });
        var getUserProfileAuditLogsV2OutputOutput1 =
            await getUserProfileAuditLogsV2ScenarioResult1.ReadAsJsonAsync<
                Output<GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2Output>>();
        var getUserProfileAuditLogsV2Output1 = getUserProfileAuditLogsV2OutputOutput1!.Data;

        Assert.That(getUserProfileAuditLogsV2OutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getUserProfileAuditLogsV2Output1.UserProfileAuditLogs.Count, Is.GreaterThan(0));

        // /AuditLogs/GetUserProfileAuditLogsV2
        var getUserProfileAuditLogsV2Input2 =
            new GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2Input(
                mockCompanyId,
                mockUserProfileId,
                null,
                new GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2InputFilters(
                    new List<string>()
                    {
                        "manual-log"
                    },
                    true),
                10);

        var getUserProfileAuditLogsV2ScenarioResult2 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getUserProfileAuditLogsV2Input2).ToUrl("/AuditLogs/GetUserProfileAuditLogsV2");
            });
        var getUserProfileAuditLogsV2OutputOutput2 =
            await getUserProfileAuditLogsV2ScenarioResult2.ReadAsJsonAsync<
                Output<GetUserProfileAuditLogsV2.GetUserProfileAuditLogsV2Output>>();
        var getUserProfileAuditLogsV2Output2 = getUserProfileAuditLogsV2OutputOutput2!.Data;

        Assert.That(getUserProfileAuditLogsV2OutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getUserProfileAuditLogsV2Output2.UserProfileAuditLogs.Count, Is.GreaterThan(0));
    }
}