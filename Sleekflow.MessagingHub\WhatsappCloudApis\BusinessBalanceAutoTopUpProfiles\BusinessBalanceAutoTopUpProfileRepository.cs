using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;

public interface IBusinessBalanceAutoTopUpProfileRepository : IRepository<BusinessBalanceAutoTopUpProfile>
{
}

public class BusinessBalanceAutoTopUpProfileRepository
    : BaseRepository<BusinessBalanceAutoTopUpProfile>,
        IBusinessBalanceAutoTopUpProfileRepository,
        ISingletonService
{
    public BusinessBalanceAutoTopUpProfileRepository(
        IServiceProvider serviceProvider,
        ILogger<BusinessBalanceAutoTopUpProfileRepository> logger)
        : base(logger, serviceProvider)
    {
    }
}