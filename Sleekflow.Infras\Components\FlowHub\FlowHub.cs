using Pulumi;
using Pulumi.AzureNative.App.V20240301.Inputs;
using Pulumi.AzureNative.Cache.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Deployment = Pulumi.Deployment;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.FlowHub;

public class FlowHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly FlowHubDb.FlowHubDbOutput _flowHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;

    public FlowHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        FlowHubDb.FlowHubDbOutput flowHubDbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _flowHubDbOutput = flowHubDbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
    }

    public (List<App.ContainerApp>, FlowHubSharedResources) InitFlowHub()
    {
        var fileStorageAccount = InitFileStorageAccount();
        var flowHubRedis = InitFlowHubRedis();

        var tableRandomId = new Random.RandomId(
            "sleekflow-fh-table-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "flow hub table storage"
                    }
                },
            });
        var tableStorageAccount = new Storage.StorageAccount(
            "sleekflow-fh-table-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-flow-hub-table-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = tableRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var flowHubSharedResources = new FlowHubSharedResources(flowHubRedis, fileStorageAccount);

        var apiImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.FlowHub),
            _myConfig.BuildTime);

        var executorImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.FlowHubExecutor),
            _myConfig.BuildTime);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.FlowHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var highTrafficServiceBus = managedEnvAndAppsTuple.HighTrafficServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, flowHubRedis.Name, flowHubRedis.Id)
                .Apply(t => Cache.ListRedisKeys.InvokeAsync(
                    new Cache.ListRedisKeysArgs
                    {
                        ResourceGroupName = t.Item1, Name = t.Item2
                    }));
            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(items => OperationalInsights.GetSharedKeys.InvokeAsync(
                    new OperationalInsights.GetSharedKeysArgs
                    {
                        ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                    }));

            var worker = managedEnvAndAppsTuple.GetWorkerApp(ServiceNames.FlowHub);

            var listWorkerHostKeysResult = Output
                .Tuple(worker!.Name, _resourceGroup.Name, worker.Id)
                .Apply(items => Web.ListWebAppHostKeys.Invoke(
                    new Web.ListWebAppHostKeysInvokeArgs
                    {
                        Name = items.Item1, ResourceGroupName = items.Item2,
                    }));

            var apiClusterContainerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.FlowHub));

            var executorClusterContainerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.FlowHubExecutor));

            var flowStackReference = new StackReference(
                "flow-stack-ref",
                new StackReferenceArgs
                {
                    Name =
                        $"{Deployment.Instance.OrganizationName}/{Deployment.Instance.ProjectName}/{Deployment.Instance.StackName}"
                });

            var frontdoorHostname = flowStackReference.GetOutput("FrontDoorEndpoint")
                .Apply(fe => fe as string ?? string.Empty);

            var apiClusterContainerApp = CreateContainerApp(
                managedEnvAndAppsTuple,
                managedEnvironment,
                ServiceNames.FlowHub,
                apiImage.BaseImageName,
                apiClusterContainerAppName,
                serviceBus,
                highTrafficServiceBus,
                eventhub,
                logAnalyticsWorkspace,
                workspaceSharedKeys,
                worker,
                listWorkerHostKeysResult,
                flowHubRedis,
                listRedisKeysOutput,
                massTransitBlobStorage,
                tableStorageAccount,
                fileStorageAccount,
                frontdoorHostname);

            var executorClusterContainerApp = CreateContainerApp(
                managedEnvAndAppsTuple,
                managedEnvironment,
                ServiceNames.FlowHubExecutor,
                executorImage.BaseImageName,
                executorClusterContainerAppName,
                serviceBus,
                highTrafficServiceBus,
                eventhub,
                logAnalyticsWorkspace,
                workspaceSharedKeys,
                worker,
                listWorkerHostKeysResult,
                flowHubRedis,
                listRedisKeysOutput,
                massTransitBlobStorage,
                tableStorageAccount,
                fileStorageAccount,
                frontdoorHostname);


            containerApps.Add(ServiceNames.FlowHub, apiClusterContainerApp);
            containerApps.Add(ServiceNames.FlowHubExecutor, executorClusterContainerApp);
            apps.Add(apiClusterContainerApp);
            apps.Add(executorClusterContainerApp);
        }

        return (apps, flowHubSharedResources);
    }

    private Cache.Redis InitFlowHubRedis()
    {
        var redis = new Cache.Redis(
            "sleekflow-fh-redis",
            new Cache.RedisArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = _resourceGroup.Location,
                Sku = new Cache.Inputs.SkuArgs
                {
                    Capacity = _myConfig.Name == "production" ? 1 : 0,
                    Family = _myConfig.Name == "production" ? "P" : "C",
                    Name = _myConfig.Name == "production" ? "Premium" : "Standard",
                },
                RedisConfiguration = new RedisCommonPropertiesRedisConfigurationArgs(),
                EnableNonSslPort = false,
                RedisVersion = "6",
            });

        return redis;
    }

    private static IEnumerable<string> HighTrafficQueueNames =>
    [
        "on-step-requested-event",
        "on-trigger-event-requested-event"
    ];

    private static IEnumerable<ScaleRuleArgs> CustomQueueBasedScaleRules
        => new[]
            {
                "on-step-requested-event",
                "on-trigger-event-requested-event",
                "on-wait-for-event-step-timeout-event",
                "on-versioned-workflow-deleted-event",
                "on-schedule-workflow-delete-event",
                "on-workflow-execution-ended-v2-event"
            }
            .Select(queueName => new App.Inputs.ScaleRuleArgs
            {
                Name = $"azure-servicebus-{queueName}",
                Custom = new App.Inputs.CustomScaleRuleArgs()
                {
                    Type = "azure-servicebus",
                    Metadata = new InputMap<string>()
                    {
                        {
                            "queueName", queueName
                        },
                        {
                            "messageCount", queueName == "on-step-requested-event" ? "100" : "400"
                        }
                    },
                    Auth = new InputList<App.Inputs.ScaleRuleAuthArgs>()
                    {
                        new App.Inputs.ScaleRuleAuthArgs()
                        {
                            TriggerParameter = "connection",
                            SecretRef = HighTrafficQueueNames.Contains(queueName)
                                ? "high-traffic-service-bus-conn-str-secret"
                                : "service-bus-keda-conn-str-secret"
                        }
                    }
                }
            });

    private Storage.StorageAccount InitFileStorageAccount()
    {
        var fileStorageRandomId = new Random.RandomId(
            "sleekflow-fh-file-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "hello", "flow hub file storage"
                    }
                },
            });

        var fileStorageAccount = new Storage.StorageAccount(
            "sleekflow-fh-file-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-flow-hub-file-storage-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = fileStorageRandomId.Hex.Apply(h => "s" + h)
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        _ = new Storage.BlobServiceProperties(
            "sleekflow-fh-file-storage-account-blob-service-properties",
            new Storage.BlobServicePropertiesArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = fileStorageAccount.Name,
                BlobServicesName = "default",
            });

        _ = new Storage.BlobContainer(
            "sleekflow-fh-workflow-steps-container",
            new Storage.BlobContainerArgs()
            {
                AccountName = fileStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "workflow-steps"
            },
            new CustomResourceOptions()
            {
                Parent = fileStorageAccount
            });

        _ = new Storage.BlobContainer(
            "sleekflow-fh-workflow-metadata-container",
            new Storage.BlobContainerArgs()
            {
                AccountName = fileStorageAccount.Name,
                PublicAccess = Storage.PublicAccess.None,
                ResourceGroupName = _resourceGroup.Name,
                ContainerName = "workflow-metadata"
            },
            new CustomResourceOptions()
            {
                Parent = fileStorageAccount
            });

        return fileStorageAccount;
    }

    private App.ContainerApp CreateContainerApp(
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        App.ManagedEnvironment managedEnvironment,
        string serviceName,
        Output<string> baseImageName,
        string containerAppName,
        MyServiceBus.ServiceBusOutput serviceBus,
        MyServiceBus.ServiceBusOutput? highTrafficServiceBus,
        MyEventHub.EventHubOutput eventhub,
        OperationalInsights.Workspace logAnalyticsWorkspace,
        Output<OperationalInsights.GetSharedKeysResult> workspaceSharedKeys,
        Web.WebApp worker,
        Output<Web.ListWebAppHostKeysResult> listWorkerHostKeysResult,
        Cache.Redis flowHubRedis,
        Output<Cache.ListRedisKeysResult> listRedisKeysOutput,
        MassTransitBlobStorage.MassTransitBlobStorageOutput massTransitBlobStorage,
        Storage.StorageAccount tableStorageAccount,
        Storage.StorageAccount fileStorageAccount,
        Output<string> frontdoorHostname)
    {
        if (serviceName is ServiceNames.FlowHub)
        {
            return new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "high-traffic-service-bus-conn-str-secret",
                                Value = highTrafficServiceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "high-traffic-service-bus-keda-conn-str-secret",
                                Value = highTrafficServiceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = 5,
                            MaxReplicas = 60,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "80"
                                            }
                                        }
                                    }
                                },
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "cpu-usage",
                                    Custom = new App.Inputs.CustomScaleRuleArgs()
                                    {
                                        Type = "cpu",
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "type", "Utilization"
                                            },
                                            {
                                                "value", "70"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-fh-app",
                                Image = baseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 2.0, Memory = "4.0Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 60,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },

                                        #region FlowHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_FLOW_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_flowHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_FLOW_HUB_DB_KEY", Value = _flowHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_FLOW_HUB_DB_DATABASE_ID",
                                            Value = _flowHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region HighTrafficServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "HIGH_TRAFFIC_SERVICE_BUS_CONN_STR",
                                            SecretRef = "high-traffic-service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region WorkerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_HOSTNAME",
                                            Value = worker.DefaultHostName.Apply(hn => "https://" + hn),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_FUNCTIONS_KEY",
                                            Value = listWorkerHostKeysResult
                                                .Apply(l => l.FunctionKeys!["default"]),
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.FlowHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, flowHubRedis.HostName)
                                                .Apply(t =>
                                                    $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region AppConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CORE_INTERNALS_ENDPOINT",
                                            Value =
                                                _myConfig.Name == "production"
                                                    ? "https://sleekflow-core-app-eas-production.azurewebsites.net/FlowHub/Internals"
                                                    : "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/FlowHub/Internals",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CORE_INTERNALS_KEY",
                                            Value = "O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1",
                                        },

                                        #endregion

                                        #region OrleansStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ORLEANS_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                tableStorageAccount.Name)
                                        },

                                        #endregion

                                        #region WorkflowRateLimitConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "WORKFLOW_ENROLLMENT_RATE_LIMIT_WINDOW_SECONDS", Value = "60"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "WORKFLOW_ENROLLMENT_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW",
                                            Value = "30"
                                        },

                                        #endregion WorkflowRateLimitConfig

                                        #region WorkflowEnrollmentRateLimitEmailNotificationConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name =
                                                "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_WINDOW_SECONDS",
                                            Value = "1800"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name =
                                                "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW",
                                            Value = "1"
                                        },

                                        #endregion WorkflowEnrollmentRateLimitEmailNotificationConfig

                                        #region StateAllStepsRequestRateCountConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "STATE_ALL_STEPS_REQUEST_WINDOW_SECONDS", Value = "60"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "STATE_ALL_STEPS_REQUEST_WARNING_MAX_WITHIN_WINDOW", Value = "100"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "STATE_ALL_STEPS_REQUEST_ERROR_MAX_WITHIN_WINDOW", Value = "500"
                                        },

                                        #endregion StateAllStepsRequestRateCountConfig

                                        #region WorkflowPotentialInfiniteLoopConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "SPECIFIC_STEP_REQUEST_RATE_LIMIT_WINDOW_SECONDS", Value = "60"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "SPECIFIC_STEP_REQUEST_MAX_ALLOWED_WITHIN_WINDOW", Value = "10"
                                        },

                                        #endregion WorkflowPotentialInfiniteLoopConfig

                                        #region WorkflowDeletionConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "WORKFLOW_VERSION_DELETION_CLEANUP_DELAY_DAYS", Value = "30"
                                        },

                                        #endregion WorkflowDeletionConfig

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "FILE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                fileStorageAccount.Name)
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion

                                        #region FrontdoorConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "FRONTDOOR_HOSTNAME", Value = frontdoorHostname
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });
        }
        else if (serviceName is ServiceNames.FlowHubExecutor)
        {
            return new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "high-traffic-service-bus-conn-str-secret",
                                Value = highTrafficServiceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "high-traffic-service-bus-keda-conn-str-secret",
                                Value = highTrafficServiceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = 5,
                            MaxReplicas = 120,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                                {
                                    new App.Inputs.ScaleRuleArgs
                                    {
                                        Name = "http",
                                        Http = new App.Inputs.HttpScaleRuleArgs
                                        {
                                            Metadata = new InputMap<string>
                                            {
                                                {
                                                    "concurrentRequests", "80"
                                                }
                                            }
                                        }
                                    },
                                    new App.Inputs.ScaleRuleArgs
                                    {
                                        Name = "cpu-usage",
                                        Custom = new App.Inputs.CustomScaleRuleArgs()
                                        {
                                            Type = "cpu",
                                            Metadata = new InputMap<string>
                                            {
                                                {
                                                    "type", "Utilization"
                                                },
                                                {
                                                    "value", "70"
                                                }
                                            }
                                        }
                                    }
                                }
                                .Concat(CustomQueueBasedScaleRules.ToList())
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-fhe-app",
                                Image = baseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 2.0, Memory = "4.0Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>()
                                {
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs()
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs()
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 60,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment()
                                        },

                                        #region FlowHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_FLOW_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_flowHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_FLOW_HUB_DB_KEY", Value = _flowHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_FLOW_HUB_DB_DATABASE_ID",
                                            Value = _flowHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region HighTrafficServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "HIGH_TRAFFIC_SERVICE_BUS_CONN_STR",
                                            SecretRef = "high-traffic-service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region WorkerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_HOSTNAME",
                                            Value = worker.DefaultHostName.Apply(hn => "https://" + hn),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "WORKER_FUNCTIONS_KEY",
                                            Value = listWorkerHostKeysResult
                                                .Apply(l => l.FunctionKeys!["default"]),
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.FlowHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, flowHubRedis.HostName)
                                                .Apply(t =>
                                                    $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region AppConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CORE_INTERNALS_ENDPOINT",
                                            Value =
                                                _myConfig.Name == "production"
                                                    ? "https://sleekflow-core-app-eas-production.azurewebsites.net/FlowHub/Internals"
                                                    : "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/FlowHub/Internals",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CORE_INTERNALS_KEY",
                                            Value = "O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1",
                                        },

                                        #endregion

                                        #region OrleansStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ORLEANS_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                tableStorageAccount.Name)
                                        },

                                        #endregion

                                        #region WorkflowRateLimitConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "WORKFLOW_ENROLLMENT_RATE_LIMIT_WINDOW_SECONDS", Value = "60"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "WORKFLOW_ENROLLMENT_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW",
                                            Value = "30"
                                        },

                                        #endregion WorkflowRateLimitConfig

                                        #region WorkflowEnrollmentRateLimitEmailNotificationConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name =
                                                "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_WINDOW_SECONDS",
                                            Value = "1800"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name =
                                                "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW",
                                            Value = "1"
                                        },

                                        #endregion WorkflowEnrollmentRateLimitEmailNotificationConfig

                                        #region StateAllStepsRequestRateCountConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "STATE_ALL_STEPS_REQUEST_WINDOW_SECONDS", Value = "60"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "STATE_ALL_STEPS_REQUEST_WARNING_MAX_WITHIN_WINDOW", Value = "100"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "STATE_ALL_STEPS_REQUEST_ERROR_MAX_WITHIN_WINDOW", Value = "500"
                                        },

                                        #endregion StateAllStepsRequestRateCountConfig

                                        #region WorkflowPotentialInfiniteLoopConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "SPECIFIC_STEP_REQUEST_RATE_LIMIT_WINDOW_SECONDS", Value = "60"
                                        },
                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "SPECIFIC_STEP_REQUEST_MAX_ALLOWED_WITHIN_WINDOW", Value = "10"
                                        },

                                        #endregion WorkflowPotentialInfiniteLoopConfig

                                        #region WorkflowDeletionConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "WORKFLOW_VERSION_DELETION_CLEANUP_DELAY_DAYS", Value = "30"
                                        },

                                        #endregion WorkflowDeletionConfig

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs()
                                        {
                                            Name = "FILE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                fileStorageAccount.Name)
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });
        }

        throw new ArgumentException("Invalid service name");
    }
}