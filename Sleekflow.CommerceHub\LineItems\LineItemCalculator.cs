using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.LineItems;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Models.Renderings;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.TemplateRenderers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CommerceHub;

namespace Sleekflow.CommerceHub.LineItems;

public interface ILineItemCalculator
{
    public record CalculatorContext(string CurrencyIsoCode, string LanguageIsoCode);

    Task<List<CalculatedLineItem>> CalculateAsync(
        string sleekflowCompanyId,
        string storeId,
        IEnumerable<SnapshottedLineItem> snapshottedLineItems,
        Discount? orderDiscount,
        CalculatorContext calculatorContext);
}

public class LineItemCalculator : ILineItemCalculator, IScopedService
{
    private readonly ITemplateRenderer _templateRenderer;
    private readonly IStoreService _storeService;
    private readonly IProductService _productService;
    private readonly ICategoryService _categoryService;

    public LineItemCalculator(
        ITemplateRenderer templateRenderer,
        IStoreService storeService,
        IProductService productService,
        ICategoryService categoryService)
    {
        _templateRenderer = templateRenderer;
        _storeService = storeService;
        _productService = productService;
        _categoryService = categoryService;
    }

    public async Task<List<CalculatedLineItem>> CalculateAsync(
        string sleekflowCompanyId,
        string storeId,
        IEnumerable<SnapshottedLineItem> snapshottedLineItems,
        Discount? orderDiscount,
        ILineItemCalculator.CalculatorContext calculatorContext)
    {
        var currencyIsoCode = calculatorContext.CurrencyIsoCode;
        var languageIsoCode = calculatorContext.LanguageIsoCode;

        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);
        var storeTemplateDict = store.TemplateDict;

        var messagePreviewTemplateStr = storeTemplateDict
            .MessagePreviewTemplates
            .Find(t => t.LanguageIsoCode == languageIsoCode);

        var calculatedLineItems = new List<CalculatedLineItem>();
        foreach (var li in snapshottedLineItems)
        {
            var (appliedDiscounts, preCalculatedAmount, postCalculatedAmount) =
                CalculatedCartLineItem(
                    orderDiscount,
                    li,
                    li.ProductVariantSnapshot.Prices.First(p => p.CurrencyIsoCode == currencyIsoCode).Amount);

            var product =
                await _productService.GetProductAsync(li.ProductId, sleekflowCompanyId, storeId);
            var categories =
                await _categoryService.GetCategoriesAsync(sleekflowCompanyId, storeId, 200, product.CategoryIds);
            var language = store.Languages.First(l => l.IsDefault);

            var messagePreviewRenderedTemplate = messagePreviewTemplateStr == null
                ? new RenderedTemplate(null, string.Empty)
                : await _templateRenderer.RenderProductVariantTemplateAsync(
                    messagePreviewTemplateStr.Value,
                    new ProductVariantRenderingDto(
                        li.ProductVariantSnapshot,
                        store,
                        product,
                        categories,
                        new LanguageOption(languageIsoCode, language.LanguageIsoCode)));

            calculatedLineItems.Add(
                new CalculatedLineItem(
                    li.ProductVariantId,
                    li.ProductId,
                    li.Description,
                    li.Quantity,
                    new Dictionary<string, object?>(),
                    appliedDiscounts,
                    Math.Round(preCalculatedAmount, 2),
                    Math.Round(postCalculatedAmount, 2),
                    messagePreviewRenderedTemplate,
                    li.LineItemDiscount));
        }

        return calculatedLineItems;
    }

    private (
        List<CalculatedLineItemAppliedDiscount> AppliedDiscounts,
        decimal PreCalculatedAmount,
        decimal PostCalculatedAmount)
        CalculatedCartLineItem(
            Discount? appliedDiscount,
            LineItem li,
            decimal initialAmount)
    {
        var appliedDiscounts = new List<CalculatedLineItemAppliedDiscount>();
        var amount = initialAmount;

        // Apply li.LineItemDiscount
        if (li.LineItemDiscount != null)
        {
            var preCalculatedAmount = amount;
            var postCalculatedAmount = ApplyDiscountToAmount(amount, li.LineItemDiscount);

            appliedDiscounts.Add(
                new CalculatedLineItemAppliedDiscount(
                    CalculatedLineItemAppliedDiscountLevels.ItemLevel,
                    li.LineItemDiscount,
                    Math.Round(preCalculatedAmount, 2),
                    Math.Round(postCalculatedAmount, 2)));

            amount = postCalculatedAmount;
        }

        // Apply orderAppliedDiscount
        if (appliedDiscount != null)
        {
            var preCalculatedAmount = amount;
            var postCalculatedAmount = ApplyDiscountToAmount(amount, appliedDiscount);

            appliedDiscounts.Add(
                new CalculatedLineItemAppliedDiscount(
                    CalculatedLineItemAppliedDiscountLevels.OrderLevel,
                    appliedDiscount,
                    Math.Round(preCalculatedAmount, 2),
                    Math.Round(postCalculatedAmount, 2)));

            amount = postCalculatedAmount;
        }

        return (
            appliedDiscounts,
            Math.Round(initialAmount, 2) * li.Quantity,
            Math.Round(amount, 2) * li.Quantity);
    }

    private decimal ApplyDiscountToAmount(decimal amount, Discount discount)
    {
        if (discount.Type == DiscountTypes.AbsoluteOff)
        {
            return amount - discount.Value;
        }
        else if (discount.Type == DiscountTypes.RateOff)
        {
            return amount * (1 - discount.Value);
        }

        throw new SfUnsupportedDiscountTypeException(discount.Type);
    }
}