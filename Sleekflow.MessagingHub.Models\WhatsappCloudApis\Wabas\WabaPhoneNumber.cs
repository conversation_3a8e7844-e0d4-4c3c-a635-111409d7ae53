using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

public class WabaPhoneNumber : Entity, IHasCreatedAt, IHasUpdatedAt
{
    public const string PropertyNameSleekflowCompanyId = "sleekflow_company_id";

    public const string PropertyNameUpdatedAt = "updated_at";

    public const string PropertyNameUpdatedBy = "updated_by";

    [JsonProperty("facebook_phone_number_id")]
    public string FacebookPhoneNumberId { get; }

    [JsonProperty("facebook_phone_number_detail")]
    public WhatsappPhoneNumberDetail FacebookPhoneNumberDetail { get; set; }

    [JsonProperty(PropertyNameSleekflowCompanyId)]
    public string? SleekflowCompanyId { get; set; }

    [JsonProperty("webhook_url")]
    public string? WebhookUrl { get; set; }

    [JsonProperty("record_status")]
    public string RecordStatus { get; set; }

    [JsonProperty("whatsapp_commerce_setting")]
    public WhatsappCommerceSetting? WhatsappCommerceSetting { get; set; }

    [JsonProperty("created_by")]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public WabaPhoneNumber(
        string id,
        string? sleekflowCompanyId,
        string facebookPhoneNumberId,
        string webhookUrl,
        string recordStatus,
        WhatsappPhoneNumberDetail facebookPhoneNumberDetail,
        WhatsappCommerceSetting? whatsappCommerceSetting,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, Constants.SysTypeNames.WabaPhoneNumber)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        FacebookPhoneNumberId = facebookPhoneNumberId;
        WebhookUrl = webhookUrl;
        RecordStatus = recordStatus;
        FacebookPhoneNumberDetail = facebookPhoneNumberDetail;
        WhatsappCommerceSetting = whatsappCommerceSetting;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public WabaPhoneNumber(
        string id,
        string? sleekflowCompanyId,
        string facebookPhoneNumberId,
        string recordStatus,
        WhatsappPhoneNumberDetail facebookPhoneNumberDetail,
        WhatsappCommerceSetting? whatsappCommerceSetting,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, Constants.SysTypeNames.WabaPhoneNumber)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        FacebookPhoneNumberId = facebookPhoneNumberId;
        RecordStatus = recordStatus;
        FacebookPhoneNumberDetail = facebookPhoneNumberDetail;
        WhatsappCommerceSetting = whatsappCommerceSetting;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public override bool Equals(object? obj)
    {
        return obj is WabaPhoneNumber config && FacebookPhoneNumberId == config.FacebookPhoneNumberId;
    }

    public override int GetHashCode()
    {
        return FacebookPhoneNumberId.GetHashCode();
    }
}