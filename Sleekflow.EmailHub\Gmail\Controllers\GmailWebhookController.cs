using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Webhooks;
using Sleekflow.EmailHub.Providers;
using Sleekflow.JsonConfigs;

namespace Sleekflow.EmailHub.Gmail.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class GmailWebhookController : ControllerBase
{
    private readonly IEmailProviderSelector _providerSelector;
    private readonly ILogger<GmailWebhookController> _logger;

    public GmailWebhookController(
        ILogger<GmailWebhookController> logger,
        IEmailProviderSelector providerSelector)
    {
        _logger = logger;
        _providerSelector = providerSelector;
    }

    public class NotifyOnGmailReceiveInput
    {
        [JsonProperty("message")]
        [Required]
        public NotifyOnGmailReceiveMessage Message { get; }

        [JsonProperty("subscription")]
        [Required]
        public string Subscription { get; }

        [JsonConstructor]
        public NotifyOnGmailReceiveInput(
            NotifyOnGmailReceiveMessage message,
            string subscription)
        {
            Message = message;
            Subscription = subscription;
        }
    }

    [HttpPost]
    [Route("NotifyOnGmailReceive")]
    public async Task<IActionResult> NotifyOnGmailReceive(
        [FromBody]
        NotifyOnGmailReceiveInput notifyOnGmailReceiveInput)
    {
        _logger.LogInformation(
            $"Receive gmail notification on mailbox. Payload " +
            $"{JsonConvert.SerializeObject(notifyOnGmailReceiveInput, JsonConfig.DefaultLoggingJsonSerializerSettings)}");
        var data = Convert.FromBase64String(notifyOnGmailReceiveInput.Message.Data);
        var decodedStr = Encoding.UTF8.GetString(data);
        var notifyOnGmailReceiveData = JsonConvert.DeserializeObject<NotifyOnGmailReceiveData>(decodedStr);
        var emailProvider = _providerSelector.GetEmailProvider(ProviderNames.Gmail);

        await emailProvider.ReceiveEmailAndStoreAsync(
            emailAddress: notifyOnGmailReceiveData!.EmailAddress,
            emailMetadata: new Dictionary<string, string>());

        return Ok();
    }
}