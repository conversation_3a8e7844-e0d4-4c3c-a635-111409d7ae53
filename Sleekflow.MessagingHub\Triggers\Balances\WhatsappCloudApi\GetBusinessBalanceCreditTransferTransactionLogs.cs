using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Commons;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GetBusinessBalanceCreditTransferTransactionLogs
    : ITrigger<GetBusinessBalanceCreditTransferTransactionLogs.GetBusinessBalanceCreditTransferTransactionLogsInput,
        GetBusinessBalanceCreditTransferTransactionLogs.GetBusinessBalanceCreditTransferTransactionLogsOutput>
{
    private readonly IWabaLevelCreditManagementService _wabaLevelCreditManagementService;
    private readonly IBusinessBalanceService _businessBalanceService;

    public GetBusinessBalanceCreditTransferTransactionLogs(
        IWabaLevelCreditManagementService wabaLevelCreditManagementService,
        IBusinessBalanceService businessBalanceService)
    {
        _wabaLevelCreditManagementService = wabaLevelCreditManagementService;
        _businessBalanceService = businessBalanceService;
    }

    public class GetBusinessBalanceCreditTransferTransactionLogsInput
    {
        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonProperty("facebook_waba_id")]
        public string? FacebookWabaId { get; set; }

        [JsonProperty("created_at_range")]
        [Validations.ValidateObject]
        public DateTimeOffsetRange? CreatedAtRange { get; set; }

        [JsonProperty("updated_at_range")]
        [Validations.ValidateObject]
        public DateTimeOffsetRange? UpdatedAtRange { get; set; }

        [JsonProperty("limit")]
        [Validations.ValidateObject]
        public int? Limit { get; set; }

        [JsonProperty("order_by")]
        [RegularExpression(
            $"^({IHasCreatedAt.PropertyNameCreatedAt}|{IHasUpdatedAt.PropertyNameUpdatedAt}|credit_transfer_amount)$")]
        public string? OrderBy { get; set; }

        [JsonProperty("order")]
        [RegularExpression("^(ACS|DESC)$")]
        public string? Order { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetBusinessBalanceCreditTransferTransactionLogsInput(
            string facebookBusinessId,
            string? facebookWabaId,
            DateTimeOffsetRange? createdAtRange,
            DateTimeOffsetRange? updatedAtRange,
            int? limit,
            string? orderBy,
            string? order,
            string? continuationToken)
        {
            FacebookBusinessId = facebookBusinessId;
            FacebookWabaId = facebookWabaId;
            CreatedAtRange = createdAtRange;
            UpdatedAtRange = updatedAtRange;
            Limit = limit;
            OrderBy = orderBy;
            Order = order;
            ContinuationToken = continuationToken;
        }
    }

    public class GetBusinessBalanceCreditTransferTransactionLogsOutput
    {
        [JsonProperty("credit_transfer_transaction_logs")]
        public List<CreditTransferTransactionLogDto> CreditTransferTransactionLogs { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonConstructor]
        public GetBusinessBalanceCreditTransferTransactionLogsOutput(
            List<CreditTransferTransactionLogDto> creditTransferTransactionLogs,
            string? nextContinuationToken,
            int count)
        {
            CreditTransferTransactionLogs = creditTransferTransactionLogs;
            NextContinuationToken = nextContinuationToken;
            Count = count;
        }
    }

    public async Task<GetBusinessBalanceCreditTransferTransactionLogsOutput> F(
        GetBusinessBalanceCreditTransferTransactionLogsInput input)
    {
        var facebookBusinessId = input.FacebookBusinessId;

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        var (creditTransferTransactionLogs, nextContinuationToken) =
            await _wabaLevelCreditManagementService.GetCreditTransferTransactionLogsAsync(
                facebookBusinessId,
                input.FacebookWabaId,
                input.CreatedAtRange,
                input.UpdatedAtRange,
                input.Limit,
                input.OrderBy,
                input.Order,
                input.ContinuationToken);

        return new GetBusinessBalanceCreditTransferTransactionLogsOutput(
            creditTransferTransactionLogs,
            nextContinuationToken,
            creditTransferTransactionLogs.Count);
    }
}