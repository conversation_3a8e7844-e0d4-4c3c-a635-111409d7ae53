using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads.Whatsapp.ConversationalComponent;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.ConversationalComponents;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ConversationalComponent.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ConversationalAutomations)]
public class GetPhoneNumberConversationalAutomation : ITrigger<
    GetPhoneNumberConversationalAutomation.GetPhoneNumberConversationalAutomationInput,
    GetPhoneNumberConversationalAutomation.GetPhoneNumberConversationalAutomationOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IConversationalAutomationService _conversationalAutomationService;
    private readonly ILogger<GetPhoneNumberConversationalAutomation> _logger;

    public GetPhoneNumberConversationalAutomation(
        IWabaService wabaService,
        IConversationalAutomationService conversationalAutomationService,
        ILogger<GetPhoneNumberConversationalAutomation> logger)
    {
        _wabaService = wabaService;
        _conversationalAutomationService = conversationalAutomationService;
        _logger = logger;
    }

    public class GetPhoneNumberConversationalAutomationInput
        : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("facebook_phone_number_id")]
        public string FacebookPhoneNumberId { get; set; }

        [JsonConstructor]
        public GetPhoneNumberConversationalAutomationInput(
            string sleekflowCompanyId,
            string facebookWabaId,
            string facebookPhoneNumberId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookWabaId = facebookWabaId;
            FacebookPhoneNumberId = facebookPhoneNumberId;
        }
    }

    public class GetPhoneNumberConversationalAutomationOutput
    {
        [JsonProperty("conversational_automation_response")]
        public GetConversationalAutomationByPhoneNumberIdResponse ConversationalAutomationsResponse { get; set; }

        [JsonConstructor]
        public GetPhoneNumberConversationalAutomationOutput(
            GetConversationalAutomationByPhoneNumberIdResponse conversationalAutomationsResponse)
        {
            ConversationalAutomationsResponse = conversationalAutomationsResponse;
        }
    }

    public async Task<GetPhoneNumberConversationalAutomationOutput> F(
        GetPhoneNumberConversationalAutomationInput input)
    {
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAndFacebookPhoneNumberIdAsync(
            input.FacebookWabaId,
            input.FacebookPhoneNumberId);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        return new GetPhoneNumberConversationalAutomationOutput(
            await _conversationalAutomationService.GetConversationalAutomationAsync(
                waba!,
                input.FacebookPhoneNumberId));
    }
}