using Microsoft.AspNetCore.Mvc;
using Sleekflow.EmailHub.Gmail.Authentications;

namespace Sleekflow.EmailHub.Gmail.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class GmailAuthenticationController : ControllerBase
{
    private readonly IGmailAuthenticationService _gmailAuthenticationService;
    private readonly ILogger<GmailAuthenticationController> _logger;

    public GmailAuthenticationController(
        ILogger<GmailAuthenticationController> logger,
        IGmailAuthenticationService gmailAuthenticationService)
    {
        _logger = logger;
        _gmailAuthenticationService = gmailAuthenticationService;
    }

    [Route("GmailAuthCallback")]
    [HttpGet]
    public async Task<IActionResult> GmailAuthCallback()
    {
        var code = HttpContext.Request.Query["code"].ToString();
        var state = HttpContext.Request.Query["state"].ToString();
        var scope = HttpContext.Request.Query["scope"].ToString();
        _logger.LogInformation(
            "received gmail GmailAuthCallback, code:{code}, state: {state}",
            code,
            state);
        if (string.IsNullOrEmpty(code))
        {
            return BadRequest("Authorization code not found");
        }

        await _gmailAuthenticationService.HandleAuthCallbackAndStoreAsync(code, state);

        return Ok();
    }
}