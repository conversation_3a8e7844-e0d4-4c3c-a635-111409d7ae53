using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.CrmHub;

public class CrmHubIntegrationDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public CrmHubIntegrationDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class CrmHubIntegrationDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public CrmHubIntegrationDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public CrmHubIntegrationDbOutput InitCrmHubIntegrationDb()
    {
        const string cosmosDbId = "crmhubintegrationdb";

        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 2000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var containerParams = new ContainerParam[]
        {
            new ContainerParam(
                "salesforce_authentication",
                "salesforce_authentication",
                new List<string>
                {
                    "/id"
                }),
            new ContainerParam(
                "salesforce_apex_trigger_authentication",
                "salesforce_apex_trigger_authentication",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "salesforce_connection",
                "salesforce_connection",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "salesforce_subscription",
                "salesforce_subscription",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "hubspot_authentication",
                "hubspot_authentication",
                new List<string>
                {
                    "/id"
                }),
            new ContainerParam(
                "hubspot_subscription",
                "hubspot_subscription",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "hubspot_queue_item",
                "hubspot_queue_item",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "hubspot_connection",
                "hubspot_connection",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "dynamics365_authentication",
                "dynamics365_authentication",
                new List<string>
                {
                    "/id"
                }),
            new ContainerParam(
                "dynamics365_subscription",
                "dynamics365_subscription",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "salesforce_user_mapping_config",
                "salesforce_user_mapping_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "hubspot_user_mapping_config",
                "hubspot_user_mapping_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "google_sheets_authentication",
                "google_sheets_authentication",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "google_sheets_connection",
                "google_sheets_connection",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "google_sheets_subscription",
                "google_sheets_subscription",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "zoho_authentication",
                "zoho_authentication",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "zoho_connection",
                "zoho_connection",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "zoho_user_mapping_config",
                "zoho_user_mapping_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
            new ContainerParam(
                "zoho_subscription",
                "zoho_subscription",
                new List<string>
                {
                    "/sleekflow_company_id"
                }),
        };

        var _ = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new CrmHubIntegrationDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}