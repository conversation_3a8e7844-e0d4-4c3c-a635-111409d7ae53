using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ManagerLeadNurturings;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ReActs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Shorts;
using Sleekflow.IntelligentHub.Models.Constants;
using static Sleekflow.IntelligentHub.Models.Constants.AgentCollaborationModes;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions;

public interface IAgentCollaborationDefinitionService
{
    IAgentCollaborationDefinition GetAgentCollaborationDefinition(string mode);
}

public class AgentCollaborationDefinitionService : IAgentCollaborationDefinitionService, IScopedService
{
    private readonly ILongAgentCollaborationDefinition _longAgentCollaborationDefinition;
    private readonly IShortAgentCollaborationDefinition _shortAgentCollaborationDefinition;
    private readonly ILeadNurturingAgentCollaborationDefinition _leadNurturingAgentCollaborationDefinition;
    private readonly IManagerLeadNurturingCollaborationDefinition _managerLeadNurturingCollaborationDefinition;
    private readonly IReActAgentCollaborationDefinition _reActAgentCollaborationDefinition;

    public AgentCollaborationDefinitionService(
        ILongAgentCollaborationDefinition longAgentCollaborationDefinition,
        IShortAgentCollaborationDefinition shortAgentCollaborationDefinition,
        ILeadNurturingAgentCollaborationDefinition leadNurturingAgentCollaborationDefinition,
        IManagerLeadNurturingCollaborationDefinition managerLeadNurturingCollaborationDefinition,
        IReActAgentCollaborationDefinition reActAgentCollaborationDefinition)
    {
        _longAgentCollaborationDefinition = longAgentCollaborationDefinition;
        _shortAgentCollaborationDefinition = shortAgentCollaborationDefinition;
        _leadNurturingAgentCollaborationDefinition = leadNurturingAgentCollaborationDefinition;
        _managerLeadNurturingCollaborationDefinition = managerLeadNurturingCollaborationDefinition;
        _reActAgentCollaborationDefinition = reActAgentCollaborationDefinition;
    }

    public IAgentCollaborationDefinition GetAgentCollaborationDefinition(string mode)
    {
        return mode switch
        {
            Long => _longAgentCollaborationDefinition,
            Medium => _shortAgentCollaborationDefinition,
            Short => _shortAgentCollaborationDefinition,
            LeadNurturing => _leadNurturingAgentCollaborationDefinition,
            ManagerLeadNurturing => _managerLeadNurturingCollaborationDefinition,
            AgentCollaborationModes.ReAct => _reActAgentCollaborationDefinition,
            _ => throw new Exception($"Unable to find agent collaboration definition. Mode: {mode}")
        };
    }
}