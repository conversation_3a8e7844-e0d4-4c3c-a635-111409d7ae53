﻿using System.Text.RegularExpressions;

namespace Sleekflow.MessagingHub.Webhooks.Helper;

public static partial class PurePhoneNumberFormatter
{
    public static string FormatPhoneNumber(string phoneNumber)
    {
        // Use regular expression to replace non-digits with an empty string
        var purePhoneNumber = PhoneNumberRegex().Replace(phoneNumber, string.Empty);
        return purePhoneNumber;
    }

    [GeneratedRegex("\\D")]
    private static partial Regex PhoneNumberRegex();
}