namespace Sleekflow.MessagingHub.Models.Events;

public class OnCloudApiHalfHourConversationUsageTransactionResynchronizationEvent
{
    public string FacebookWabaId { get; set; }

    public string FacebookBusinessId { get; set; }

    public long LastConversationUsageInsertTimestamp { get; set; }

    public long LatestConversationUsageInsertTimestamp { get; set; }

    public OnCloudApiHalfHourConversationUsageTransactionResynchronizationEvent(
        string facebookWabaId,
        string facebookBusinessId,
        long lastConversationUsageInsertTimestamp,
        long latestConversationUsageInsertTimestamp)
    {
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        LastConversationUsageInsertTimestamp = lastConversationUsageInsertTimestamp;
        LatestConversationUsageInsertTimestamp = latestConversationUsageInsertTimestamp;
    }
}