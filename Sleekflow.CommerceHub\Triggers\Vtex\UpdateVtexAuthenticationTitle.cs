﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex.ViewModels;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Vtex;

[TriggerGroup(ControllerNames.Vtex)]
public class UpdateVtexAuthenticationTitle
    : ITrigger<
        UpdateVtexAuthenticationTitle.UpdateVtexAuthenticationTitleInput,
        UpdateVtexAuthenticationTitle.UpdateVtexAuthenticationTitleOutput>
{
    private readonly IVtexAuthenticationService _vtexAuthenticationService;
    private readonly IVtexOrderHookRegister _vtexOrderHookRegister;

    public UpdateVtexAuthenticationTitle(IVtexAuthenticationService vtexAuthenticationService, IVtexOrderHookRegister vtexOrderHookRegister)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
        _vtexOrderHookRegister = vtexOrderHookRegister;
    }

    public class UpdateVtexAuthenticationTitleInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("vtex_authentication_id")]
        public string VtexAuthenticationId { get; set; }

        [Required]
        [JsonProperty("title")]
        public string Title { get; set; }

        [JsonConstructor]
        public UpdateVtexAuthenticationTitleInput(string sleekflowCompanyId, string vtexAuthenticationId, string title)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            VtexAuthenticationId = vtexAuthenticationId;
            Title = title;
        }
    }

    public class UpdateVtexAuthenticationTitleOutput
    {
        [JsonProperty("vtex_authentication")]
        public VtexAuthenticationViewModel VtexAuthentication { get; set; }

        [JsonConstructor]
        public UpdateVtexAuthenticationTitleOutput(VtexAuthenticationViewModel vtexAuthentication)
        {
            VtexAuthentication = vtexAuthentication;
        }
    }

    public async Task<UpdateVtexAuthenticationTitleOutput> F(
        UpdateVtexAuthenticationTitleInput input)
    {
        var vtexAuthentication = await _vtexAuthenticationService.UpdateTitleAsync(
            input.VtexAuthenticationId,
            input.SleekflowCompanyId,
            input.Title);
        var isActive = await _vtexOrderHookRegister.ValidateRegistrationAsync(vtexAuthentication.Credential);

        return new UpdateVtexAuthenticationTitleOutput(
            new VtexAuthenticationViewModel(vtexAuthentication, isActive));
    }
}