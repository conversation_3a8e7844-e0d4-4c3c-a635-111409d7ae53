using Microsoft.Azure.Cosmos;

namespace Sleekflow.Persistence;

public class PatchVariableType
{
    private const string Path = "/";

    private string? Variable { get; set; }

    private List<string>? Variables { get; set; }

    public static implicit operator PatchVariableType(string variable) => new ()
    {
        Variable = variable
    };

    public static implicit operator PatchVariableType(List<string> variables) => new ()
    {
        Variables = variables
    };

    public override string? ToString()
    {
        if (Variable != null)
        {
            return Path + Variable;
        }

        if (Variables != null)
        {
            return Path + string.Join(Path, Variables);
        }

        return base.ToString();
    }
}

public static class PatchVariable
{
    public static PatchOperation Add<T>(
        PatchVariableType variable,
        T value,
        bool isAppend = true)
    {
        return PatchOperation.Add(
            variable
            + (isAppend

                // If the index specified is equal to the length of the array,
                // it will append an element to the array.
                // Instead of specifying an index, you can also use the - character.
                // It will also result in the element being appended to the array.
                ? "-"
                : string.Empty),
            value);
    }

    public static PatchOperation Replace<T>(
        PatchVariableType variable,
        T value)
    {
        return PatchOperation.Replace(
            variable.ToString(),
            value);
    }

    public static PatchOperation Set<T>(
        PatchVariableType variable,
        T value)
    {
        return PatchOperation.Set(
            variable.ToString(),
            value);
    }

    public static PatchOperation Increment(
        PatchVariableType variable,
        long value)
    {
        return PatchOperation.Increment(
            variable.ToString(),
            value);
    }

    public static PatchOperation Increment(
        PatchVariableType variable,
        double value)
    {
        return PatchOperation.Increment(
            variable.ToString(),
            value);
    }

    public static PatchOperation Remove(
        PatchVariableType variable)
    {
        return PatchOperation.Remove(
            variable.ToString());
    }
}