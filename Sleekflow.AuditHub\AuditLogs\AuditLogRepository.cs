﻿using Sleekflow.AuditHub.Models.AuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.AuditLogs;

public interface IAuditLogRepository : IRepository<AuditLog>
{
}

public class AuditLogRepository : BaseRepository<AuditLog>, IAuditLogRepository, ISingletonService
{
    public AuditLogRepository(
        ILogger<BaseRepository<AuditLog>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}