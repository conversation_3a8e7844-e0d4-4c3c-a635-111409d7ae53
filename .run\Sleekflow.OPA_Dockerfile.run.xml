<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.OPA/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="containerName" value="opa" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="8181" />
              <option name="hostIp" value="0.0.0.0" />
              <option name="hostPort" value="8181" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="sourceFilePath" value="Sleekflow.OPA/Dockerfile" />
      </settings>
    </deployment>
    <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="false" isSslEnabled="false" />
    <method v="2" />
  </configuration>
  <configuration default="false" name="Sleekflow.OPA/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="containerName" value="opa" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="8181" />
              <option name="hostIp" value="0.0.0.0" />
              <option name="hostPort" value="8181" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="sourceFilePath" value="Sleekflow.OPA/Dockerfile" />
      </settings>
    </deployment>
    <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="false" isSslEnabled="false" />
    <method v="2" />
  </configuration>
</component>