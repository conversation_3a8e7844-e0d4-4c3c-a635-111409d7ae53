using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Integrator.Hubspot.Configs;

public interface IHubspotConfig
{
    string HubspotClientId { get; }

    string HubspotClientSecret { get; }

    string HubspotOauthCallbackUrl { get; }

    string HubspotOauthStateEncryptionKey { get; }

    string HubspotApiUrl { get; }
}

public class HubspotConfig : IConfig, IHubspotConfig
{
    public string HubspotClientId { get; private set; }

    public string HubspotClientSecret { get; private set; }

    public string HubspotOauthCallbackUrl { get; private set; }

    public string HubspotOauthStateEncryptionKey { get; private set; }

    public string HubspotApiUrl { get; private set; }

    public HubspotConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        HubspotClientId =
            Environment.GetEnvironmentVariable("HUBSPOT_CLIENT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("HUBSPOT_CLIENT_ID");
        HubspotClientSecret =
            Environment.GetEnvironmentVariable("HUBSPOT_CLIENT_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("HUBSPOT_CLIENT_SECRET");
        HubspotOauthCallbackUrl =
            Environment.GetEnvironmentVariable("HUBSPOT_OAUTH_CALLBACK_URL", target)
            ?? configuration["HUBSPOT_OAUTH_CALLBACK_URL"];
        HubspotOauthStateEncryptionKey =
            Environment.GetEnvironmentVariable("HUBSPOT_OAUTH_STATE_ENCRYPTION_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("HUBSPOT_OAUTH_STATE_ENCRYPTION_KEY");
        HubspotApiUrl =
            Environment.GetEnvironmentVariable("HUBSPOT_API_URL", target)
            ?? throw new SfMissingEnvironmentVariableException("HUBSPOT_API_URL");
    }
}