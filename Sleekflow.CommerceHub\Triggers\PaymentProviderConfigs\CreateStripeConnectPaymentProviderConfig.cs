using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.Stripe.Connect;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;

[TriggerGroup(ControllerNames.PaymentProviderConfigs)]
public class CreateStripeConnectPaymentProviderConfig
    : ITrigger<
        CreateStripeConnectPaymentProviderConfig.CreateStripeConnectPaymentProviderConfigInput,
        CreateStripeConnectPaymentProviderConfig.CreateStripeConnectPaymentProviderConfigOutput>
{
    private readonly IStripeConnectService _stripeConnectService;

    public CreateStripeConnectPaymentProviderConfig(
        IStripeConnectService stripeConnectService)
    {
        _stripeConnectService = stripeConnectService;
    }

    public class CreateStripeConnectPaymentProviderConfigInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("platform_country")]
        public string PlatformCountry { get; set; }

        [Required]
        [JsonProperty("application_fee_rate")]
        public decimal ApplicationFeeRate { get; set; }

        [Required]
        [JsonProperty("is_shipping_enabled")]
        public bool IsShippingEnabled { get; set; }

        [JsonProperty("shipping_allowed_country_iso_codes")]
        public List<string>? ShippingAllowedCountryIsoCodes { get; set; }

        [Required]
        [JsonProperty("is_inventory_enabled")]
        public bool IsInventoryEnabled { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateStripeConnectPaymentProviderConfigInput(
            string sleekflowCompanyId,
            string platformCountry,
            decimal applicationFeeRate,
            bool isShippingEnabled,
            List<string>? shippingAllowedCountryIsoCodes,
            bool isInventoryEnabled,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PlatformCountry = platformCountry;
            ApplicationFeeRate = applicationFeeRate;
            IsShippingEnabled = isShippingEnabled;
            ShippingAllowedCountryIsoCodes = shippingAllowedCountryIsoCodes;
            IsInventoryEnabled = isInventoryEnabled;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateStripeConnectPaymentProviderConfigOutput
    {
        [JsonProperty("stripe_connect_onboarding_link")]
        public string StripeConnectOnboardingLink { get; set; }

        [JsonConstructor]
        public CreateStripeConnectPaymentProviderConfigOutput(
            string stripeConnectOnboardingLink)
        {
            StripeConnectOnboardingLink = stripeConnectOnboardingLink;
        }
    }

    public async Task<CreateStripeConnectPaymentProviderConfigOutput> F(
        CreateStripeConnectPaymentProviderConfigInput createStripeConnectPaymentProviderConfigInput)
    {
        var paymentProviderConfig = await _stripeConnectService.CreateStripeConnectPaymentProviderConfigAsync(
            createStripeConnectPaymentProviderConfigInput.SleekflowCompanyId,
            createStripeConnectPaymentProviderConfigInput.PlatformCountry,
            createStripeConnectPaymentProviderConfigInput.ApplicationFeeRate,
            createStripeConnectPaymentProviderConfigInput.IsShippingEnabled,
            createStripeConnectPaymentProviderConfigInput.ShippingAllowedCountryIsoCodes,
            createStripeConnectPaymentProviderConfigInput.IsInventoryEnabled,
            createStripeConnectPaymentProviderConfigInput.SleekflowStaffId,
            createStripeConnectPaymentProviderConfigInput.SleekflowStaffTeamIds);

        var stripePaymentProviderExternalConfig =
            (StripePaymentProviderExternalConfig) paymentProviderConfig.PaymentProviderExternalConfig;

        var stripeConnectOnboardingLink = await _stripeConnectService.GenerateStripeConnectOnboardingLinkAsync(
            createStripeConnectPaymentProviderConfigInput.PlatformCountry,
            stripePaymentProviderExternalConfig.StripeAccount.Id);

        return new CreateStripeConnectPaymentProviderConfigOutput(stripeConnectOnboardingLink);
    }
}