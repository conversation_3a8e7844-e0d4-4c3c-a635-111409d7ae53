using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

public static partial class JsonUtils
{
    public static bool TryParseJson<T>(string content, out T? json)
    {
        try
        {
            var myContent = JsonStartRegex().Replace(content, string.Empty);
            myContent = JsonEndRegex().Replace(myContent, string.Empty);

            json = JsonConvert.DeserializeObject<T>(myContent)!;
            return true;
        }
        catch
        {
            json = default;
            return false;
        }
    }

    public static Dictionary<string, object>? ParseJson(string content)
    {
        var myContent = JsonStartRegex().Replace(content, string.Empty);
        myContent = JsonEndRegex().Replace(myContent, string.Empty);

        return JsonConvert.DeserializeObject<Dictionary<string, object>>(myContent);
    }

    [GeneratedRegex("^```json")]
    private static partial Regex JsonStartRegex();

    [GeneratedRegex("```$")]
    private static partial Regex JsonEndRegex();
}