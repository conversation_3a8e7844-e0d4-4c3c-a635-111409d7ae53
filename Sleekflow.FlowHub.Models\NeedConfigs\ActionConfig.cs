﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class ActionConfig
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("action_group")]
    public string ActionGroup { get; set; }

    [JsonProperty("action_subgroup")]
    public string ActionSubgroup { get; set; }

    [JsonConstructor]
    public ActionConfig(
        string id,
        string label,
        string actionGroup,
        string actionSubgroup)
    {
        Id = id;
        Label = label;
        ActionGroup = actionGroup;
        ActionSubgroup = actionSubgroup;
    }
}