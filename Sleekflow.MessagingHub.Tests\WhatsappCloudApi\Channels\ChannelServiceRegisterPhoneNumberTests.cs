using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Payloads;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.WhatsappCloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi.Channels;

[TestFixture]
public class ChannelServiceRegisterPhoneNumberTests
{
    [Test]
    public async Task RegisterPhoneNumber_WithoutError()
    {
        var loggerMock = new Mock<ILogger<ChannelService>>();
        var whatsappCloudApiBspClientMock = new Mock<IWhatsappCloudApiBspClient>();
        var cloudApiClientsMock = new Mock<ICloudApiClients>();
        var auditLogServiceMock = new Mock<IAuditLogService>();
        var httpClientFactoryMock = new Mock<IHttpClientFactory>();

        // Setup CloudApiClients to return our mocked WhatsappCloudApiBspClient
        cloudApiClientsMock.Setup(c => c.WhatsappCloudApiBspClient)
            .Returns(whatsappCloudApiBspClientMock.Object);

        // Use the real CommonRetryPolicyService implementation
        var commonRetryPolicyService =
            new CommonRetryPolicyService(new Mock<ILogger<CommonRetryPolicyService>>().Object);

        var mockHttpClient = new HttpClient(new MockHttpMessageHandler());
        // Create HttpClient mock
        httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>()))
            .Returns(mockHttpClient);

        // Create ChannelService instance
        var channelService = new ChannelService(
            null, // IIdService - not needed for this test
            null, // IWabaService - not needed for this test
            loggerMock.Object,
            null, // IWabaRepository - not needed for this test
            auditLogServiceMock.Object,
            cloudApiClientsMock.Object,
            commonRetryPolicyService,
            null, // IMessagingHubWebhookService - not needed for this test
            httpClientFactoryMock.Object
        );

        // Arrange
        string facebookWabaId = "*********";
        string sleekflowCompanyId = "company123";
        string facebookPhoneNumberId = "*********";
        string pin = "123456";


        // Setup first call to RegisterPhoneNumberAsync to throw the exception
        whatsappCloudApiBspClientMock.SetupSequence(
                c => c.RegisterPhoneNumberAsync(facebookPhoneNumberId, pin, CancellationToken.None))
            .ReturnsAsync(
                new RegisterPhoneNumberResponse
                {
                    Success = true
                });

        // Setup AuditLogService to return the provided value
        auditLogServiceMock.Setup(
                a => a.GetCloudApiAuditedResponse(
                    It.IsAny<Func<Task<RegisterPhoneNumberResponse>>>(),
                    It.IsAny<BaseAuditingObject>()))
            .Returns<Func<Task<RegisterPhoneNumberResponse>>, BaseAuditingObject>(
                async (func, _) => await func());

        auditLogServiceMock.Setup(
                a => a.GetCloudApiAuditedResponse(
                    It.IsAny<Func<Task<UpdatePhoneNumberSettingsResponse>>>(),
                    It.IsAny<BaseAuditingObject>()))
            .Returns<Func<Task<UpdatePhoneNumberSettingsResponse>>, BaseAuditingObject>(
                async (func, _) => await func());

        // Act
        var result = await channelService.RegisterPhoneNumber(
            facebookWabaId,
            sleekflowCompanyId,
            facebookPhoneNumberId,
            pin,
            null); // No business integration token

        // Assert
        Assert.IsTrue(result.Success);

        // Verify RegisterPhoneNumberAsync was called twice (first throws, second succeeds)
        whatsappCloudApiBspClientMock.Verify(
            c => c.RegisterPhoneNumberAsync(facebookPhoneNumberId, pin, CancellationToken.None),
            Times.Once);
    }

    [Test]
    public async Task RegisterPhoneNumber_WhenOtherErrorOccurs_Should_Not_Retry()
    {
        var loggerMock = new Mock<ILogger<ChannelService>>();
        var whatsappCloudApiBspClientMock = new Mock<IWhatsappCloudApiBspClient>();
        var cloudApiClientsMock = new Mock<ICloudApiClients>();
        var auditLogServiceMock = new Mock<IAuditLogService>();
        var httpClientFactoryMock = new Mock<IHttpClientFactory>();

        // Setup CloudApiClients to return our mocked WhatsappCloudApiBspClient
        cloudApiClientsMock.Setup(c => c.WhatsappCloudApiBspClient)
            .Returns(whatsappCloudApiBspClientMock.Object);

        // Use the real CommonRetryPolicyService implementation
        var commonRetryPolicyService =
            new CommonRetryPolicyService(new Mock<ILogger<CommonRetryPolicyService>>().Object);

        var mockHttpClient = new HttpClient(new MockHttpMessageHandler());

        // Create HttpClient mock
        httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>()))
            .Returns(mockHttpClient);

        // Create ChannelService instance
        var channelService = new ChannelService(
            null, // IIdService - not needed for this test
            null, // IWabaService - not needed for this test
            loggerMock.Object,
            null, // IWabaRepository - not needed for this test
            auditLogServiceMock.Object,
            cloudApiClientsMock.Object,
            commonRetryPolicyService,
            null, // IMessagingHubWebhookService - not needed for this test
            httpClientFactoryMock.Object
        );

        // Arrange
        string facebookWabaId = "*********";
        string sleekflowCompanyId = "company123";
        string facebookPhoneNumberId = "*********";
        string pin = "123456";
        string dataLocalizationRegion = "DE";

        // Create the Graph API error with data localization details
        string errorResponseBody =
            @"{""error"":{""message"":""(#100) Invalid parameter"",""type"":""OAuthException"",""code"":100,""error_data"":{""messaging_product"":""whatsapp"",""details"":""Other errors""},""fbtrace_id"":""AdPrXdHfO5YPB3bSvo79SEm""}}";
        var errorResponse = JsonConvert.DeserializeObject<GraphApiErrorResponse>(errorResponseBody);
        var exception = new GraphApiClientException(
            errorResponse,
            HttpMethod.Post,
            "phonenumberid/register",
            400,
            "{\"messaging_product\":\"whatsapp\",\"pin\":\"123456\"}",
            errorResponseBody);

        // Setup first call to RegisterPhoneNumberAsync to throw the exception
        whatsappCloudApiBspClientMock.SetupSequence(
                c => c.RegisterPhoneNumberAsync(facebookPhoneNumberId, pin, CancellationToken.None))
            .ThrowsAsync(exception)
            .ReturnsAsync(
                new RegisterPhoneNumberResponse
                {
                    Success = true
                });

        // Setup UpdatePhoneNumberSettings to return success
        whatsappCloudApiBspClientMock.Setup(
                c => c.UpdatePhoneNumberSettings(
                    facebookPhoneNumberId,
                    It.Is<StorageConfiguration>(
                        sc =>
                            sc.Status == StorageConfigurationStatus.IN_COUNTRY_STORAGE_ENABLED &&
                            sc.DataLocalizationRegion == dataLocalizationRegion),
                    CancellationToken.None))
            .ReturnsAsync(
                new UpdatePhoneNumberSettingsResponse
                {
                    Success = true
                });

        // Setup AuditLogService to return the provided value
        auditLogServiceMock.Setup(
                a => a.GetCloudApiAuditedResponse(
                    It.IsAny<Func<Task<RegisterPhoneNumberResponse>>>(),
                    It.IsAny<BaseAuditingObject>()))
            .Returns<Func<Task<RegisterPhoneNumberResponse>>, BaseAuditingObject>(
                async (func, _) => await func());

        // Act
        Assert.ThrowsAsync<GraphApiClientException>(
            async () => await channelService.RegisterPhoneNumber(
                facebookWabaId,
                sleekflowCompanyId,
                facebookPhoneNumberId,
                pin,
                null));

        // Verify RegisterPhoneNumberAsync was called twice (first throws, second succeeds)
        whatsappCloudApiBspClientMock.Verify(
            c => c.RegisterPhoneNumberAsync(facebookPhoneNumberId, pin, CancellationToken.None),
            Times.Exactly(1));

        // Verify UpdatePhoneNumberSettings was never call
        whatsappCloudApiBspClientMock.Verify(
            c => c.UpdatePhoneNumberSettings(
                facebookPhoneNumberId,
                It.Is<StorageConfiguration>(
                    sc =>
                        sc.Status == StorageConfigurationStatus.IN_COUNTRY_STORAGE_ENABLED &&
                        sc.DataLocalizationRegion == dataLocalizationRegion),
                CancellationToken.None),
            Times.Never);
    }

    [Test]
    public async Task RegisterPhoneNumber_WhenDataLocalizationErrorOccurs_ShouldUpdateSettingsAndRetry()
    {
        var loggerMock = new Mock<ILogger<ChannelService>>();
        var whatsappCloudApiBspClientMock = new Mock<IWhatsappCloudApiBspClient>();
        var cloudApiClientsMock = new Mock<ICloudApiClients>();
        var auditLogServiceMock = new Mock<IAuditLogService>();
        var httpClientFactoryMock = new Mock<IHttpClientFactory>();

        // Setup CloudApiClients to return our mocked WhatsappCloudApiBspClient
        cloudApiClientsMock.Setup(c => c.WhatsappCloudApiBspClient)
            .Returns(whatsappCloudApiBspClientMock.Object);

        // Use the real CommonRetryPolicyService implementation
        var commonRetryPolicyService =
            new CommonRetryPolicyService(new Mock<ILogger<CommonRetryPolicyService>>().Object);

        var mockHttpClient = new HttpClient(new MockHttpMessageHandler());
        // Create HttpClient mock
        httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>()))
            .Returns(mockHttpClient);

        // Create ChannelService instance
        var channelService = new ChannelService(
            null, // IIdService - not needed for this test
            null, // IWabaService - not needed for this test
            loggerMock.Object,
            null, // IWabaRepository - not needed for this test
            auditLogServiceMock.Object,
            cloudApiClientsMock.Object,
            commonRetryPolicyService,
            null, // IMessagingHubWebhookService - not needed for this test
            httpClientFactoryMock.Object
        );

        // Arrange
        string facebookWabaId = "*********";
        string sleekflowCompanyId = "company123";
        string facebookPhoneNumberId = "*********";
        string pin = "123456";
        string dataLocalizationRegion = "DE";

        // Create the Graph API error with data localization details
        string errorResponseBody =
            @"{""error"":{""message"":""(#100) Invalid parameter"",""type"":""OAuthException"",""code"":100,""error_data"":{""messaging_product"":""whatsapp"",""details"":""Migrated phone number should be first registered with the same data localization region used on the source WABA. Data localization region: 'DE'.""},""fbtrace_id"":""AdPrXdHfO5YPB3bSvo79SEm""}}";
        var errorResponse = JsonConvert.DeserializeObject<GraphApiErrorResponse>(errorResponseBody);
        var exception = new GraphApiClientException(
            errorResponse,
            HttpMethod.Post,
            "phonenumberid/register",
            400,
            "{\"messaging_product\":\"whatsapp\",\"pin\":\"123456\"}",
            errorResponseBody);

        // Setup first call to RegisterPhoneNumberAsync to throw the exception
        whatsappCloudApiBspClientMock.SetupSequence(
                c => c.RegisterPhoneNumberAsync(facebookPhoneNumberId, pin, CancellationToken.None))
            .ThrowsAsync(exception)
            .ReturnsAsync(
                new RegisterPhoneNumberResponse
                {
                    Success = true
                });

        // Setup UpdatePhoneNumberSettings to return success
        whatsappCloudApiBspClientMock.Setup(
                c => c.UpdatePhoneNumberSettings(
                    facebookPhoneNumberId,
                    It.Is<StorageConfiguration>(
                        sc =>
                            sc.Status == StorageConfigurationStatus.IN_COUNTRY_STORAGE_ENABLED &&
                            sc.DataLocalizationRegion == dataLocalizationRegion),
                    CancellationToken.None))
            .ReturnsAsync(
                new UpdatePhoneNumberSettingsResponse
                {
                    Success = true
                });

        // Setup AuditLogService to return the provided value
        auditLogServiceMock.Setup(
                a => a.GetCloudApiAuditedResponse(
                    It.IsAny<Func<Task<RegisterPhoneNumberResponse>>>(),
                    It.IsAny<BaseAuditingObject>()))
            .Returns<Func<Task<RegisterPhoneNumberResponse>>, BaseAuditingObject>(
                async (func, _) => await func());

        auditLogServiceMock.Setup(
                a => a.GetCloudApiAuditedResponse(
                    It.IsAny<Func<Task<UpdatePhoneNumberSettingsResponse>>>(),
                    It.IsAny<BaseAuditingObject>()))
            .Returns<Func<Task<UpdatePhoneNumberSettingsResponse>>, BaseAuditingObject>(
                async (func, _) => await func());

        // Act
        var result = await channelService.RegisterPhoneNumber(
            facebookWabaId,
            sleekflowCompanyId,
            facebookPhoneNumberId,
            pin,
            null); // No business integration token

        // Assert
        Assert.IsTrue(result.Success);

        // Verify RegisterPhoneNumberAsync was called twice (first throws, second succeeds)
        whatsappCloudApiBspClientMock.Verify(
            c => c.RegisterPhoneNumberAsync(facebookPhoneNumberId, pin, CancellationToken.None),
            Times.Exactly(2));

        // Verify UpdatePhoneNumberSettings was called once with the correct parameters
        whatsappCloudApiBspClientMock.Verify(
            c => c.UpdatePhoneNumberSettings(
                facebookPhoneNumberId,
                It.Is<StorageConfiguration>(
                    sc =>
                        sc.Status == StorageConfigurationStatus.IN_COUNTRY_STORAGE_ENABLED &&
                        sc.DataLocalizationRegion == dataLocalizationRegion),
                CancellationToken.None),
            Times.Once);
    }
}

// Mock HTTP handler for the HttpClient
public class MockHttpMessageHandler : HttpMessageHandler
{
    protected override Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        return Task.FromResult(
            new HttpResponseMessage(System.Net.HttpStatusCode.OK)
            {
                Content = new StringContent("{}")
            });
    }
}