using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.Models.MessageObjects;
using GraphApi.Client.Payloads;
using Newtonsoft.Json;
using Polly;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.Ids;
using Sleekflow.JsonConfigs;
using Sleekflow.MessagingHub.Blobs;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Blobs;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Messages;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.OpenTelemetry.MessagingHub;
using Sleekflow.OpenTelemetry.MessagingHub.MeterNames;
using Sleekflow.OpenTelemetry.MessagingHub.MeterTags;
using Sleekflow.Persistence;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Messages;

public interface IMessageService
{
    Task<SendMessageResponse> SendWhatsappCloudApiMessageAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        WhatsappCloudApiMessageObject messageObject,
        AuditEntity.SleekflowStaff sleekflowStaff,
        bool isMMLite);

    Task<IBlobService.GetBlobSasUrlOutput> GetWhatsappCloudApiMediaUrlAsync(
        string mediaId,
        string? businessIntegrationSystemUserAccessToken);
}

public class MessageService : IMessageService, ISingletonService
{
    private readonly IIdService _idService;
    private readonly IBlobService _blobService;
    private readonly ISecretConfig _secretConfig;
    private readonly ILogger<MessageService> _logger;
    private readonly IMessageRepository _messageRepository;
    private readonly IWhatsappCloudApiMessagingClient _whatsappCloudApiMessagingClient;
    private readonly IWabaService _wabaService;
    private readonly IMessagingChannelErrorMeters _messagingChannelErrorMeters;
    private readonly HttpClient _httpClient;

    public MessageService(
        IIdService idService,
        IBlobService blobService,
        ISecretConfig secretConfig,
        ILogger<MessageService> logger,
        ICloudApiClients cloudApiClients,
        IMessageRepository messageRepository,
        IWabaService wabaService,
        IHttpClientFactory httpClientFactory,
        IMessagingChannelErrorMeters messagingChannelErrorMeters)
    {
        _logger = logger;
        _idService = idService;
        _blobService = blobService;
        _secretConfig = secretConfig;
        _messageRepository = messageRepository;
        _whatsappCloudApiMessagingClient = cloudApiClients.WhatsappCloudApiMessagingClient;
        _wabaService = wabaService;
        _messagingChannelErrorMeters = messagingChannelErrorMeters;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<SendMessageResponse> SendWhatsappCloudApiMessageAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        WhatsappCloudApiMessageObject messageObject,
        AuditEntity.SleekflowStaff sleekflowStaff,
        bool isMMLite = false)
    {
        var jsonSerializerSettings = JsonConfig.DefaultJsonSerializerSettings;

        var wabaPhoneNumber = waba.WabaPhoneNumbers.FirstOrDefault(
            x =>
                x.SleekflowCompanyId == sleekflowCompanyId
                && x.Id == wabaPhoneNumberId && x.RecordStatus == WabaPhoneNumberStatuses.Active);

        if (wabaPhoneNumber == null)
        {
            throw new SfNotFoundObjectException(
                $"Unable to get WabaPhoneNumber with sendWhatsappCloudApiMessageInput.WabaPhoneNumberId {wabaPhoneNumberId}");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var whatsappCloudApiMessagingClient =
            hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null
                ? new WhatsappCloudApiMessagingClient(
                    decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken,
                    _httpClient)
                : _whatsappCloudApiMessagingClient;

        return await AuditSendMessageResponseAsync(
            messageObject,
            sleekflowCompanyId,
            SysTypeNames.SendWhatsappCloudApiMessage,
            async () =>
            {
                SendMessageResponse sendMessageResponse;
                if (isMMLite)
                {
                    sendMessageResponse = await whatsappCloudApiMessagingClient.SendMMLiteMessageAsync(
                        wabaPhoneNumber.FacebookPhoneNumberId,
                        messageObject);

                    _logger.LogInformation(
                        "Sent MMLite Message sendWhatsappCloudApiMessageInput {SendWhatsappCloudApiMessageInput} wabaPhoneNumber {WabaPhoneNumber} in {Waba}, sendMessageResponse {SendMessageResponse}",
                        JsonConvert.SerializeObject(messageObject, jsonSerializerSettings),
                        JsonConvert.SerializeObject(wabaPhoneNumber, jsonSerializerSettings),
                        JsonConvert.SerializeObject(waba, jsonSerializerSettings),
                        JsonConvert.SerializeObject(sendMessageResponse, jsonSerializerSettings));
                }
                else
                {
                    sendMessageResponse = await whatsappCloudApiMessagingClient.SendMessageAsync(
                        wabaPhoneNumber.FacebookPhoneNumberId,
                        messageObject);

                    _logger.LogInformation(
                        "Sent Message sendWhatsappCloudApiMessageInput {SendWhatsappCloudApiMessageInput} wabaPhoneNumber {WabaPhoneNumberId} in {WabaId}, sendMessageResponse {SendMessageResponse}",
                        JsonConvert.SerializeObject(messageObject, jsonSerializerSettings),
                        wabaPhoneNumber.Id,
                        waba.Id,
                        JsonConvert.SerializeObject(sendMessageResponse, jsonSerializerSettings));

                }


                return sendMessageResponse;
            },
            sleekflowStaff);
    }

    public async Task<IBlobService.GetBlobSasUrlOutput> GetWhatsappCloudApiMediaUrlAsync(
        string mediaId,
        string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiMessagingClient =
            !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new WhatsappCloudApiMessagingClient(
                    businessIntegrationSystemUserAccessToken,
                    _httpClient)
                : _whatsappCloudApiMessagingClient;

        var getMediaUrlResponse = await whatsappCloudApiMessagingClient.GetMediaUrlAsync(mediaId);

        var token = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? businessIntegrationSystemUserAccessToken
            : _secretConfig.FacebookSystemUserAccessToken;

        var blobId = await _blobService.UploadUrlToBlobAsync(
            new HttpRequestMessage
            {
                Headers =
                {
                    {
                        "Authorization", $"Bearer {token}"
                    },
                    {
                        "User-Agent", "Other"
                    }
                },
                RequestUri = new Uri(getMediaUrlResponse.Url)
            });

        return _blobService.GetBlobSasUrl(blobId, BlobStorageType.Internal);
    }

    // TODO Need refactor
    private async Task<TOutput> AuditSendMessageResponseAsync<TOutput>(
        object payload,
        string sleekflowCompanyId,
        string sysTypeName,
        Func<Task<TOutput>> func,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var message = await _messageRepository.CreateAndGetAsync(
            new Message(
                _idService.GetId(SysTypeNames.Message),
                sleekflowCompanyId,
                messagePayload: JsonConvertExtensions.ToDictionary(payload),
                messageResponse: null,
                exception: null,
                sysTypeName,
                sleekflowStaff,
                sleekflowStaff,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
            sleekflowCompanyId);

        // Define a Polly retry policy with 3 retries and 5 seconds delay
        var retryPolicy = Policy.Handle<GraphApiClientException>(
                graphApiClientException =>
                    graphApiClientException.ErrorApiResponse is { Error.Code: 130429 })
            .WaitAndRetryAsync(
                3,
                _ => TimeSpan.FromSeconds(5),
                onRetry: (exception, retryCount, _) =>
                {
                    _logger.LogError(
                        exception,
                        "130429 Cloud API Rate Limit Hit Error occurred when sending {CloudApiMessage} for {SleekflowCompanyId} during attempt {RetryCount}",
                        JsonConvert.SerializeObject(message),
                        sleekflowCompanyId,
                        retryCount);
                });

        try
        {
            // Execute the SendMessage operation with the defined retry policy
            var res = await retryPolicy.ExecuteAsync(
                async () =>
                {
                    var res = await func.Invoke();

                    await _messageRepository.PatchMessageResponse(
                        message,
                        JsonConvertExtensions.ToDictionary(res!));

                    return res;
                });

            return res;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Caught an exception for sysTypeName {SysTypeName}", sysTypeName);
            await _messageRepository.PatchMessageResponse(message, error: JsonConvertExtensions.ToDictionary(e));

            if (e is GraphApiClientException g)
            {
                IncrementWhatsappCloudApiSendMessageErrorCounter(g);
                throw new SfGraphApiErrorException(
                    $"Unable to send message. sysTypeName {sysTypeName}",
                    JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
            }

            throw new SfInternalErrorException($"Messaging hub - Error occur during {sysTypeName}");
        }
    }

    private void IncrementWhatsappCloudApiSendMessageErrorCounter(
        GraphApiClientException exception)
    {
        try
        {
            if (exception.ErrorApiResponse == null)
            {
                _messagingChannelErrorMeters.IncrementCounter(
                    MessagingHubChannelMeterNames.WhatsappCloudApi,
                    MessagingChannelErrorMetersOptions.SendMessage);
                return;
            }

            if (exception.ErrorApiResponse.Error is { Code: not null, ErrorSubcode: not null })
            {
                _messagingChannelErrorMeters.IncrementCounter(
                    MessagingHubChannelMeterNames.WhatsappCloudApi,
                    MessagingChannelErrorMetersOptions.SendMessage,
                    1,
                    new MessagingChannelErrorMeterTags(exception.ErrorApiResponse.Error.Code.Value.ToString(), exception.ErrorApiResponse.Error.ErrorSubcode.Value.ToString()));
                return;
            }

            if (exception.ErrorApiResponse.Error.Code.HasValue)
            {
                _messagingChannelErrorMeters.IncrementCounter(
                    MessagingHubChannelMeterNames.WhatsappCloudApi,
                    MessagingChannelErrorMetersOptions.SendMessage,
                    1,
                    new MessagingChannelErrorMeterTags(exception.ErrorApiResponse.Error.Code.Value.ToString(), null));
                return;
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error occur during increment counter for send message error");
        }
    }
}