using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISleepV2StepExecutor : IStepExecutor;

public class SleepV2StepExecutor : GeneralStepExecutor<CallStep<SleepV2StepArgs>>, ISleepV2StepExecutor, IScopedService
{
    private readonly IStepOrchestrationService _stepOrchestrationService;
    private readonly IStateEvaluator _stateEvaluator;

    public SleepV2StepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IStepOrchestrationService stepOrchestrationService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stepOrchestrationService = stepOrchestrationService;
        _stateEvaluator = stateEvaluator;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = step as CallStep<SleepV2StepArgs> ?? throw new InvalidOperationException();

        var units =
            _stateEvaluator.IsExpression(callStep.Args.UnitsExpr)
                ? await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.UnitsExpr)
                : callStep.Args.UnitsExpr;

        var timeUnit = callStep.Args.TimeUnit;
        var unitsDecimal = decimal.TryParse(units?.ToString(), out var parsedUnits)
            ? parsedUnits
            : 0;

        var timeSpan = TimeUnitUtils.ConvertToTimeSpan(timeUnit, Convert.ToDouble(unitsDecimal)) ?? default(TimeSpan);

        await _stepOrchestrationService.ExecuteSleepSleep(
            state.Id,
            step.Id,
            timeSpan,
            stackEntries);

        // The actions is Delayed Complete
    }
}