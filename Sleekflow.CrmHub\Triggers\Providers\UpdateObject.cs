﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class UpdateObject : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public UpdateObject(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class UpdateObjectInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("dict")]
        [ValidateObject]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("typed_ids")]
        [ValidateArray]
        public List<TypedId>? TypedIds { get; set; }

        [JsonConstructor]
        public UpdateObjectInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId,
            string entityTypeName,
            Dictionary<string, object?> dict,
            List<TypedId>? typedIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
            EntityTypeName = entityTypeName;
            Dict = dict;
            TypedIds = typedIds;
        }
    }

    public class UpdateObjectOutput
    {
    }

    public async Task<UpdateObjectOutput> F(
        UpdateObjectInput updateObjectInput)
    {
        var providerService = _providerSelector.GetProviderService(
            updateObjectInput.ProviderName);

        await providerService.UpdateObjectV2Async(
            updateObjectInput.SleekflowCompanyId,
            updateObjectInput.ProviderConnectionId,
            null,
            updateObjectInput.Dict,
            updateObjectInput.EntityTypeName,
            typedIds: updateObjectInput.TypedIds);

        return new UpdateObjectOutput();
    }
}