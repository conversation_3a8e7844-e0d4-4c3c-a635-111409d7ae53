﻿using System;

namespace Sleekflow.Analyzers;

[AttributeUsage(AttributeTargets.Class)]
public class TriggerGroupAttribute : Attribute
{
    public TriggerGroupAttribute(
        string groupName,
        string? baseRoute = default,
        string[]? filterNames = default)
    {
        GroupName = groupName;
        BaseRoute = baseRoute;
        FilterNames = filterNames;
    }

    private string GroupName { get; }

    private string? BaseRoute { get; }

    private string[]? FilterNames { get; }
}