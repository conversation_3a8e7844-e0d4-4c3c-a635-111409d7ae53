﻿using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Models.ObjectIdResolvers;

public interface IHubspotObjectIdResolver
{
    string ResolveObjectId(Dictionary<string, object?> dict);
}

public class HubspotObjectIdResolver : IHubspotObjectIdResolver, ISingletonService
{
    public string ResolveObjectId(Dictionary<string, object?> dict)
    {
        var isCrmHubDict = dict.Any(e => e.Key.Contains(":"));

        if (isCrmHubDict)
        {
            if (dict.ContainsKey("hubspot-integrator:hs_object_id"))
            {
                var entry = dict["hubspot-integrator:hs_object_id"]!;
                if (entry is JObject jObject)
                {
                    if (jObject.ContainsKey(SnapshottedValue.PropertyNameValue))
                    {
                        return jObject.Value<string>(SnapshottedValue.PropertyNameValue)!;
                    }
                    else if (jObject.ContainsKey("value"))
                    {
                        return jObject.Value<string>("value")!;
                    }
                }
                else if (entry is string s)
                {
                    return s;
                }
            }
            else if (dict.ContainsKey("hubspot-integrator:id"))
            {
                var entry = dict["hubspot-integrator:id"]!;
                if (entry is JObject jObject)
                {
                    if (jObject.ContainsKey(SnapshottedValue.PropertyNameValue))
                    {
                        return jObject.Value<string>(SnapshottedValue.PropertyNameValue)!;
                    }
                    else if (jObject.ContainsKey("value"))
                    {
                        return jObject.Value<string>("value")!;
                    }
                }
                else if (entry is string s)
                {
                    return s;
                }
            }
        }
        else
        {
            if (dict.ContainsKey("hs_object_id"))
            {
                return (string) dict["hs_object_id"]!;
            }
            else if (dict.ContainsKey("id"))
            {
                return (string) dict["id"]!;
            }
        }

        throw new SfIdUnresolvableException(dict);
    }
}