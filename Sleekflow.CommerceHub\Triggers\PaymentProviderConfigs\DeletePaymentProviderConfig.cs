using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;

[TriggerGroup(ControllerNames.PaymentProviderConfigs)]
public class DeletePaymentProviderConfig
    : ITrigger<
        DeletePaymentProviderConfig.DeletePaymentProviderConfigInput,
        DeletePaymentProviderConfig.DeletePaymentProviderConfigOutput>
{
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;

    public DeletePaymentProviderConfig(IPaymentProviderConfigService paymentProviderConfigService)
    {
        _paymentProviderConfigService = paymentProviderConfigService;
    }

    public class DeletePaymentProviderConfigInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("payment_provider_config_id")]
        public string PaymentProviderConfigId { get; set; }

        [Required]
        [JsonProperty("sleekflow_staff_id")]
        public string SleekflowStaffId { get; set; }

        [JsonProperty("sleekflow_staff_team_ids")]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeletePaymentProviderConfigInput(
            string sleekflowCompanyId,
            string paymentProviderConfigId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            PaymentProviderConfigId = paymentProviderConfigId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DeletePaymentProviderConfigOutput
    {
    }

    public async Task<DeletePaymentProviderConfigOutput> F(
        DeletePaymentProviderConfigInput deletePaymentProviderConfigInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deletePaymentProviderConfigInput.SleekflowStaffId,
            deletePaymentProviderConfigInput.SleekflowStaffTeamIds);

        await _paymentProviderConfigService.DeletePaymentProviderConfigAsync(
            deletePaymentProviderConfigInput.PaymentProviderConfigId,
            deletePaymentProviderConfigInput.SleekflowCompanyId,
            sleekflowStaff);

        return new DeletePaymentProviderConfigOutput();
    }
}