using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.WebPageDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.WebPageDocument)]
public class WebPageDocument : KbDocument
{
    public const string PropertyNameBlobId = "blob_Id";
    public const string PropertyNameBlobType = "blob_type";
    public const string PropertyNamePageUri = "page_uri";
    public const string PropertyNameLanguageIsoCode = "language_iso_code";

    [JsonProperty(PropertyNameBlobId)]
    public string BlobId { get; set; }

    [JsonProperty(PropertyNameBlobType)]
    public string BlobType { get; set; }

    [JsonProperty(PropertyNamePageUri)]
    public string PageUri { get; set; }

    [JsonProperty(PropertyNameLanguageIsoCode)]
    public string LanguageIsoCode { get; set; }

    [JsonConstructor]
    public WebPageDocument(
        string id,
        string sleekflowCompanyId,
        string blobId,
        string blobType,
        string pageUri,
        string languageIsoCode,
        DocumentStatistics documentStatistics,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(
            id,
            sleekflowCompanyId,
            "",
            new Dictionary<string, object?>(),
            ProcessFileDocumentStatuses.Pending,
            0.0,
            new List<AgentAssignment>(),
            createdAt,
            updatedAt,
            "",
            SysTypeNames.FileDocument)
    {
        BlobId = blobId;
        BlobType = blobType;
        PageUri = pageUri;
        LanguageIsoCode = languageIsoCode;
    }

    public override int GetCharacterCount()
    {
        return 0;
    }
}