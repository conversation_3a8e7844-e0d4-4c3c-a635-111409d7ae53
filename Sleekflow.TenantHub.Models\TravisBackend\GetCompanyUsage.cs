using Newtonsoft.Json;

namespace Sleekflow.TenantHub.Models.TravisBackend;

public class GetCompanyUsageRequest
{
    [JsonProperty("company_id")]
    public string CompanyId { get; set; }

    [JsonConstructor]
    public GetCompanyUsageRequest(string companyId)
    {
        CompanyId = companyId;
    }
}

public class GetCompanyUsageResponse
{
    [JsonProperty("company_id")]
    public string CompanyId { get; set; }

    [JsonProperty("usage")]
    public CompanyUsage Usage { get; set; }
}

public class CompanyUsage
{
    [JsonProperty("company_id")]
    public string CompanyId { get; set; }

    [JsonProperty("total_contacts")]
    public int TotalContacts { get; set; }

    [JsonProperty("total_conversations")]
    public int TotalConversations { get; set; }

    [JsonProperty("total_messages")]
    public int TotalMessages { get; set; }

    [JsonProperty("total_messages_sent_from_sleek_flow")]
    public int TotalMessagesSentFromSleekFlow { get; set; }

    [JsonProperty("total_channel_added")]
    public int TotalChannelAdded { get; set; }

    [JsonProperty("total_agents")]
    public int TotalAgents { get; set; }

    [JsonProperty("total_api_calls")]
    public int TotalAPICalls { get; set; }

    [JsonProperty("base_maximum_api_calls")]
    public int BaseMaximumApiCalls { get; set; }

    [JsonProperty("base_maximum_contacts")]
    public int BaseMaximumContacts { get; set; }

    [JsonProperty("base_maximum_automated_messages")]
    public int BaseMaximumAutomatedMessages { get; set; }

    [JsonProperty("base_maximum_agents")]
    public int BaseMaximumAgents { get; set; }

    [JsonProperty("base_maximum_automations")]
    public int BaseMaximumAutomations { get; set; }

    [JsonProperty("base_maximum_number_of_whatsapp_cloud_api_channels")]
    public int BaseMaximumNumberOfWhatsappCloudApiChannels { get; set; }

    [JsonProperty("base_maximum_number_of_channel")]
    public int BaseMaximumNumberOfChannel { get; set; }

    [JsonProperty("is_exceeded_wa_limit")]
    public bool IsExceededWALimit { get; set; }

    [JsonProperty("current_number_of_whatsapp_cloud_api_channels")]
    public int CurrentNumberOfWhatsappCloudApiChannels { get; set; }

    [JsonProperty("current_number_of_channels")]
    public int CurrentNumberOfChannels { get; set; }

    [JsonProperty("last_login")]
    public DateTime? LastLogin { get; set; }

    [JsonProperty("billing_period_usages")]
    public List<object> BillingPeriodUsages { get; set; }

    [JsonProperty("usage_limit_offset_profile")]
    public object UsageLimitOffsetProfile { get; set; }

    [JsonProperty("maximum_api_calls")]
    public int MaximumAPICalls { get; set; }

    [JsonProperty("maximum_contacts")]
    public int MaximumContacts { get; set; }

    [JsonProperty("maximum_automated_messages")]
    public int MaximumAutomatedMessages { get; set; }

    [JsonProperty("maximum_agents")]
    public int MaximumAgents { get; set; }

    [JsonProperty("maximum_automations")]
    public int MaximumAutomations { get; set; }

    [JsonProperty("maximum_number_of_whatsapp_cloud_api_channels")]
    public int MaximumNumberOfWhatsappCloudApiChannels { get; set; }

    [JsonProperty("maximum_number_of_channel")]
    public int MaximumNumberOfChannel { get; set; }

    [JsonProperty("usage_cycle_date_time_range")]
    public object UsageCycleDateTimeRange { get; set; }

    [JsonProperty("current_flow_builder_flow_enrolment_usage")]
    public decimal CurrentFlowBuilderFlowEnrolmentUsage { get; set; }

    [JsonProperty("base_flow_builder_flow_enrolment_usage")]
    public long BaseFlowBuilderFlowEnrolmentUsage { get; set; }

    [JsonProperty("maximum_flow_builder_flow_enrolment_usage")]
    public long MaximumFlowBuilderFlowEnrolmentUsage { get; set; }

    [JsonProperty("flow_hub_usage_limit_offset")]
    public object FlowHubUsageLimitOffset { get; set; }
}
