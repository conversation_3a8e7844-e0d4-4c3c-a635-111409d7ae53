using System.Net;
using MassTransit;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Companies;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.EmailSentRecords;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Limiters;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Locks;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.FlowHubEvents;

public interface IFlowHubEventHandler
{
    Task HandleAsync(EventBody eventBody, string objectId, string objectType, string sleekflowCompanyId);

    Task<bool> HandleReenrollmentAsync(ProxyState state);
}

public class FlowHubEventHandler : IFlowHubEventHandler, IScopedService
{
    private readonly IStateService _stateService;
    private readonly IStateAggregator _stateAggregator;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowExecutionService _workflowExecutionService;
    private readonly IWorkflowRuntimeService _workflowRuntimeService;
    private readonly IWorkflowEnrollmentFilter _workflowEnrollmentFilter;
    private readonly IStepExecutorActivator _stepExecutorActivator;
    private readonly IStateSubscriptionService _stateSubscriptionService;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly ICompanyUsageCycleService _companyUsageCycleService;
    private readonly IWorkflowRateLimitConfig _workflowRateLimitConfig;
    private readonly IWorkflowEnrollmentRateLimitEmailNotificationConfig _workflowEnrollmentRateLimitEmailNotificationConfig;
    private readonly IRequestRateLimiter _requestRateLimiter;
    private readonly ICacheService _cacheService;
    private readonly IBus _bus;
    private readonly IWorkflowRecurringSettingsParser _workflowRecurringSettingsParser;
    private readonly IEmailSentRecordService _emailSentRecordService;
    private readonly ILockService _lockService;
    private readonly ILogger<FlowHubEventHandler> _logger;
    private readonly IProxyGrainWorkflowService _proxyGrainWorkflowService;

    public FlowHubEventHandler(
        IStateService stateService,
        IStateAggregator stateAggregator,
        IStateEvaluator stateEvaluator,
        IWorkflowService workflowService,
        IWorkflowExecutionService workflowExecutionService,
        IWorkflowRuntimeService workflowRuntimeService,
        IWorkflowEnrollmentFilter workflowEnrollmentFilter,
        IStepExecutorActivator stepExecutorActivator,
        IStateSubscriptionService stateSubscriptionService,
        IFlowHubConfigService flowHubConfigService,
        ICompanyUsageCycleService companyUsageCycleService,
        IWorkflowRateLimitConfig workflowRateLimitConfig,
        IWorkflowEnrollmentRateLimitEmailNotificationConfig workflowEnrollmentRateLimitEmailNotificationConfig,
        IRequestRateLimiter requestRateLimiter,
        ICacheService cacheService,
        IBus bus,
        IWorkflowRecurringSettingsParser workflowRecurringSettingsParser,
        IEmailSentRecordService emailSentRecordService,
        ILockService lockService,
        ILogger<FlowHubEventHandler> logger,
        IProxyGrainWorkflowService proxyGrainWorkflowService)
    {
        _stateService = stateService;
        _stateAggregator = stateAggregator;
        _stateEvaluator = stateEvaluator;
        _workflowService = workflowService;
        _workflowExecutionService = workflowExecutionService;
        _workflowRuntimeService = workflowRuntimeService;
        _workflowEnrollmentFilter = workflowEnrollmentFilter;
        _stepExecutorActivator = stepExecutorActivator;
        _stateSubscriptionService = stateSubscriptionService;
        _flowHubConfigService = flowHubConfigService;
        _companyUsageCycleService = companyUsageCycleService;
        _workflowRateLimitConfig = workflowRateLimitConfig;
        _workflowEnrollmentRateLimitEmailNotificationConfig = workflowEnrollmentRateLimitEmailNotificationConfig;
        _requestRateLimiter = requestRateLimiter;
        _cacheService = cacheService;
        _bus = bus;
        _workflowRecurringSettingsParser = workflowRecurringSettingsParser;
        _emailSentRecordService = emailSentRecordService;
        _lockService = lockService;
        _logger = logger;
        _proxyGrainWorkflowService = proxyGrainWorkflowService;
    }

    public async Task HandleAsync(EventBody eventBody, string objectId, string objectType, string sleekflowCompanyId)
    {
        _logger.LogInformation("[FlowHubEventHandler: HandleAsync] eventBody: {EventBody}, ObjectId, {ObjectId}, ObjectType: {ObjectType}", eventBody, objectId, objectType);
        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(sleekflowCompanyId);

        if (!flowHubConfig.IsEnrolled)
        {
            return;
        }

        var monthlyWorkflowExecutionUsageInfo = await _workflowExecutionService.GetMonthlyWorkflowExecutionUsageInfoAsync(flowHubConfig);

        if (monthlyWorkflowExecutionUsageInfo.IsExceedExecutionLimit
            || monthlyWorkflowExecutionUsageInfo.UsagePercentage >= 75.00d)
        {
            await HandleWorkflowExecutionUsageReachedThresholdAsync(
                flowHubConfig,
                monthlyWorkflowExecutionUsageInfo.IsExceedExecutionLimit
                    ? 100.00d
                    : monthlyWorkflowExecutionUsageInfo.UsagePercentage);
        }

        // If there exists a workflow executing for the contact, then aggregate the existing state
        var existingStates = await _stateService.GetRunningStatesAsync(
            objectId,
            objectType,
            sleekflowCompanyId,
            eventBody);

        foreach (var existingState in existingStates.Where(s => s.StateStatus == StateStatuses.Running))
        {
            await AggregateStateAndExecuteWorkflowSubscriptionsAsync(
                eventBody,
                existingState);
        }

        // Get workflows with matched conditions
        _logger.LogInformation("begin to match workflow");
        var matchedCompactWorkflows = await _workflowService.MatchWorkflowsAsync(
            sleekflowCompanyId,
            eventBody,
            flowHubConfig);

        _logger.LogInformation("matched {MatchedWorkFlowCount} workflow(s)", matchedCompactWorkflows.Count);
        if (matchedCompactWorkflows is not { Count: > 0 }
            && existingStates is not { Count: > 0 })
        {
            return;
        }

        var workflowEnrollmentFilterResult = await HandleWorkflowEnrollmentAsync(
            eventBody,
            objectId,
            objectType,
            existingStates,
            matchedCompactWorkflows,
            flowHubConfig,
            monthlyWorkflowExecutionUsageInfo,
            isReenrollment: false);

        // Handle re-enrollment of workflows that trigger based on contact property
        if (eventBody is OnContactUpdatedEventBody onContactUpdatedEventBody)
        {
            await ReEnrollWorkflowsAsync(
                WorkflowScheduleTypes.ContactPropertyBasedDateTime,
                StateReasonCodes.ContactPropertyChanged,
                compactWorkflowToReEnrollPredicate: w =>
                    onContactUpdatedEventBody.ChangeEntries.Exists(
                        x => x.PropertyId == w.WorkflowScheduleSettings.ContactPropertyId),
                targetPropertyChangedPredicate: state =>
                    onContactUpdatedEventBody.ChangeEntries.Exists(
                    x => x.PropertyId == state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings
                        .ContactPropertyId));
        }

        // Handle re-enrollment of workflows that trigger based on schemaful object property
        if (eventBody is OnSchemafulObjectUpdatedEventBody onSchemafulObjectUpdatedEventBody)
        {
            await ReEnrollWorkflowsAsync(
                WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime,
                StateReasonCodes.SchemafulObjectPropertyChanged,
                compactWorkflowToReEnrollPredicate: w =>
                    onSchemafulObjectUpdatedEventBody.ChangeEntries.Exists(
                    x => x.PropertyId == w.WorkflowScheduleSettings.SchemafulObjectPropertyId),
                targetPropertyChangedPredicate: state =>
                    onSchemafulObjectUpdatedEventBody.ChangeEntries.Exists(
                    x => x.PropertyId == state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings
                        .SchemafulObjectPropertyId));
        }

        async Task ReEnrollWorkflowsAsync(
            string scheduleType,
            string stateReasonCode,
            Func<CompactWorkflow, bool> compactWorkflowToReEnrollPredicate,
            Func<ProxyState, bool> targetPropertyChangedPredicate)
        {
            var compactWorkflowsToCheck = matchedCompactWorkflows
                .Where(w => w.WorkflowScheduleSettings.ScheduleType == scheduleType)
                .Except(workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnce)
                .Except(workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnFailure)
                .ToList();

            foreach (var state in existingStates)
            {
                /*
                 * The specialized sleep step will set the `scheduled_enrollment_started` flag after done, so that we can
                 * know that the current active enrollment has already passed the scheduled date and
                 * proceed with subsequent actions.
                 * If this happens, when user updates the contact property again at this moment,
                 * it shouldn't fail the current active enrollment.
                 */
                if (state.SysVarDict.ContainsKey(StateSystemVarNames.ScheduledEnrollmentStarted))
                {
                    continue;
                }

                var compactWorkflowToReEnroll = compactWorkflowsToCheck.Find(
                    w =>
                        w.WorkflowId == state.Identity.WorkflowId
                        && compactWorkflowToReEnrollPredicate(w));

                if (compactWorkflowToReEnroll is not null)
                {
                    await _workflowRuntimeService.AbandonWorkflowAsync(
                        state.Identity.SleekflowCompanyId,
                        state.Id,
                        stateReasonCode);

                    var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                        compactWorkflowToReEnroll.SleekflowCompanyId,
                        compactWorkflowToReEnroll.WorkflowVersionedId);

                    if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
                    {
                        continue;
                    }

                    await CreateStateAndExecutedWorkflowAsync(
                        eventBody,
                        objectId,
                        objectType,
                        sleekflowCompanyId,
                        StateStatuses.Scheduled,
                        workflow,
                        flowHubConfig,
                        null,
                        false);
                }
                else if (state.WorkflowContext.SnapshottedWorkflow.WorkflowScheduleSettings.ScheduleType == scheduleType
                         && workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnce
                             .All(x => x.WorkflowId != state.Identity.WorkflowId)
                         && workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnFailure
                             .All(x => x.WorkflowId != state.Identity.WorkflowId)
                         && targetPropertyChangedPredicate(state))
                {
                    await _workflowRuntimeService.AbandonWorkflowAsync(
                        state.Identity.SleekflowCompanyId,
                        state.Id,
                        stateReasonCode);
                }
            }
        }
    }

    public async Task<bool> HandleReenrollmentAsync(ProxyState state)
    {
        var targetWorkflow =
            await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                state.Identity.SleekflowCompanyId,
                state.Identity.WorkflowVersionedId);

        if (targetWorkflow?.ActivationStatus is not WorkflowActivationStatuses.Active)
        {
            return false;
        }

        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(state.Identity.SleekflowCompanyId);

        var monthlyWorkflowExecutionUsageInfo = await _workflowExecutionService.GetMonthlyWorkflowExecutionUsageInfoAsync(flowHubConfig);

        if (monthlyWorkflowExecutionUsageInfo.IsExceedExecutionLimit
            || monthlyWorkflowExecutionUsageInfo.UsagePercentage >= 75.00d)
        {
            await HandleWorkflowExecutionUsageReachedThresholdAsync(
                flowHubConfig,
                monthlyWorkflowExecutionUsageInfo.IsExceedExecutionLimit
                    ? 100.00d
                    : monthlyWorkflowExecutionUsageInfo.UsagePercentage);
        }

        if (monthlyWorkflowExecutionUsageInfo.IsExceedExecutionLimit
            && (string.IsNullOrWhiteSpace(targetWorkflow.WorkflowScheduleSettings.ScheduleType)
                || targetWorkflow.WorkflowScheduleSettings.ScheduleType == WorkflowScheduleTypes.None))
        {
            _logger.LogWarning(
                "Reenrollment failed for company {CompanyId} workflow {WorkflowId} due to usage limit exceeded. StateId: {StateId}",
                state.Identity.SleekflowCompanyId,
                targetWorkflow.WorkflowId,
                state.Id);

            return false;
        }

        var existingStates = await _stateService.GetRunningStatesAsync(
            state.Identity.ObjectId,
            state.Identity.ObjectType,
            state.Identity.SleekflowCompanyId,
            state.TriggerEventBody);

        var workflowEnrollmentFilterResult = await HandleWorkflowEnrollmentAsync(
            state.TriggerEventBody,
            state.Identity.ObjectId,
            state.Identity.ObjectType,
            existingStates,
            [new CompactWorkflow(targetWorkflow)],
            flowHubConfig,
            monthlyWorkflowExecutionUsageInfo,
            isReenrollment: true);

        return workflowEnrollmentFilterResult.WorkflowsToEnroll
            .Any(x => x.WorkflowVersionedId == state.Identity.WorkflowVersionedId);
    }

    private async Task<WorkflowEnrollmentFilterResult> HandleWorkflowEnrollmentAsync(
        EventBody eventBody,
        string objectId,
        string objectType,
        List<ProxyState> existingStates,
        List<CompactWorkflow> matchedCompactWorkflows,
        FlowHubConfig flowHubConfig,
        MonthlyWorkflowExecutionUsageInfo monthlyWorkflowExecutionUsageInfo,
        bool isReenrollment)
    {
        _logger.LogInformation("[HandleWorkflowEnrollment] eventBody: {EventBoby}, objectId: {ObjectId}, objectType: {ObjectType}", eventBody, objectId, objectType);
        // Get running workflow ids
        var runningWorkflowIds = existingStates
            .Select(x => x.WorkflowContext.SnapshottedWorkflow.WorkflowId)
            .ToList();

        // Get workflows that the contact has enrolled in before this
        var completeExecutionWorkflowIds =
            await _workflowExecutionService.GetCompleteExecutionWorkflowIdsAsync(
                objectId,
                objectType,
                flowHubConfig.SleekflowCompanyId);

        // Filter workflows based on enrollment settings
        var workflowEnrollmentFilterResult = _workflowEnrollmentFilter.Filter(
            matchedCompactWorkflows,
            runningWorkflowIds,
            completeExecutionWorkflowIds);
        _logger.LogInformation("[HandleWorkflowEnrollment] workflowEnrollmentFilterResult: {WorkflowEnrollmentFilterResult}", workflowEnrollmentFilterResult);
        // Parallel flow enrollments
        foreach (var compactWorkflow in workflowEnrollmentFilterResult.WorkflowsToEnroll)
        {
            var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                compactWorkflow.SleekflowCompanyId,
                compactWorkflow.WorkflowVersionedId);

            if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
            {
                continue;
            }

            var isWorkflowEnrollmentHitRateLimit = await IsWorkflowEnrollmentHitRateLimitAsync(
                workflow,
                objectId);

            if (isWorkflowEnrollmentHitRateLimit)
            {
                await HandleBlockedWorkflowEnrollmentAsync(
                    objectId,
                    objectType,
                    eventBody,
                    workflow,
                    flowHubConfig,
                    isReenrollment);
            }
            else if (monthlyWorkflowExecutionUsageInfo.IsExceedExecutionLimit
                     && workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
            {
                // Create restricted state only for non-old-scheduled workflow trigger
                // New Scheduled Workflow Usage Limit Exceeded Handling (Original Handling is on ScheduledTriggerConditionsCheckStepExecutor)
                var state = await _stateService.CreateStateAsync(
                    objectId,
                    objectType,
                    workflow.SleekflowCompanyId,
                    StateStatuses.Restricted,
                    StateReasonCodes.EnrollmentUsageLimitExceeded,
                    TimeToLiveConstants.OneYearInSeconds,
                    isReenrollment,
                    workflow,
                    eventBody,
                    flowHubConfig);

                await _bus.Publish(
                    new OnWorkflowExecutionRestrictedEvent(
                        state.Identity.SleekflowCompanyId,
                        state.Id,
                        state.Identity,
                        state.WorkflowContext.SnapshottedWorkflow.WorkflowType));
            }
            else
            {
                var executionReasonCode = GetWorkflowExecutionReasonCode(workflow, workflowEnrollmentFilterResult);

                if (workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
                {
                    if ((workflow.WorkflowScheduleSettings.IsNewScheduledWorkflowSchema is true
                         || workflow.WorkflowScheduleSettings
                             .IsOldScheduledWorkflowSchemaFirstRecurringCompleted is true) && (
                        eventBody is OnScheduledWorkflowEnrollmentEventBody
                        || eventBody is OnDateAndTimeArrivedCommonEventBody))
                    {
                        ContactDetail CreateContactDetail(object evt)
                        {
                            if (evt is OnScheduledWorkflowEnrollmentEventBody enrollment)
                            {
                                return new ContactDetail(enrollment.Contact, enrollment.ContactOwner, enrollment.Lists, enrollment.Conversation);
                            }
                            if (evt is OnDateAndTimeArrivedCommonEventBody dateTime)
                            {
                                return new ContactDetail(dateTime.Contact, dateTime.ContactOwner, dateTime.Lists, dateTime.Conversation);
                            }

                            return null;
                        }

                        ContactDetail contactDetail = CreateContactDetail(eventBody);
                        await CreateStateAndExecutedWorkflowAsync(
                            eventBody,
                            objectId,
                            objectType,
                            flowHubConfig.SleekflowCompanyId,
                            StateStatuses.Running,
                            workflow,
                            flowHubConfig,
                            executionReasonCode,
                            isReenrollment,
                            contactDetail);
                    }
                    else
                    {
                        await CreateStateAndExecutedWorkflowAsync(
                            eventBody,
                            objectId,
                            objectType,
                            flowHubConfig.SleekflowCompanyId,
                            StateStatuses.Running,
                            workflow,
                            flowHubConfig,
                            executionReasonCode,
                            isReenrollment);
                    }
                }
                else // Old Scheduled Workflow Schema && using in step enrollment condition check logic
                {
                    await CreateStateAndExecutedWorkflowAsync(
                        eventBody,
                        objectId,
                        objectType,
                        flowHubConfig.SleekflowCompanyId,
                        StateStatuses.Scheduled,
                        workflow,
                        flowHubConfig,
                        executionReasonCode,
                        isReenrollment);
                }
            }
        }

        foreach (var compactWorkflow in workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnce)
        {
            var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                compactWorkflow.SleekflowCompanyId,
                compactWorkflow.WorkflowVersionedId);

            if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
            {
                continue;
            }

            var state = await _stateService.CreateStateAsync(
                objectId,
                objectType,
                workflow.SleekflowCompanyId,
                StateStatuses.Blocked,
                StateReasonCodes.EnrollOnlyOncePolicy,
                TimeToLiveConstants.OneYearInSeconds,
                isReenrollment,
                workflow,
                eventBody,
                flowHubConfig);

            await _bus.Publish(
                new OnWorkflowExecutionBlockedEvent(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    null,
                    state.Identity,
                    workflow.WorkflowType));
        }

        foreach (var compactWorkflow in workflowEnrollmentFilterResult.WorkflowsSkippedDueToCanEnrollOnlyOnFailure)
        {
            var workflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
                compactWorkflow.SleekflowCompanyId,
                compactWorkflow.WorkflowVersionedId);

            if (workflow is not { ActivationStatus: WorkflowActivationStatuses.Active })
            {
                continue;
            }

            var state = await _stateService.CreateStateAsync(
                objectId,
                objectType,
                workflow.SleekflowCompanyId,
                StateStatuses.Blocked,
                StateReasonCodes.EnrollAgainOnFailureOnlyPolicy,
                TimeToLiveConstants.OneYearInSeconds,
                isReenrollment,
                workflow,
                eventBody,
                flowHubConfig);

            await _bus.Publish(
                new OnWorkflowExecutionBlockedEvent(
                    state.Identity.SleekflowCompanyId,
                    state.Id,
                    null,
                    state.Identity,
                    workflow.WorkflowType));
        }

        return workflowEnrollmentFilterResult;
    }

    private static string? GetWorkflowExecutionReasonCode(
        ProxyWorkflow workflow,
        WorkflowEnrollmentFilterResult workflowEnrollmentFilterResult)
    {
        return workflowEnrollmentFilterResult.WorkflowsIncludedDueToCanEnrollInParallel
            .Any(w => w.WorkflowId == workflow.WorkflowId)
            ? WorkflowExecutionReasonCodes.ParallelismEnabled
            : null;
    }

    private async Task CreateStateAndExecutedWorkflowAsync(
        EventBody eventBody,
        string objectId,
        string objectType,
        string sleekflowCompanyId,
        string initialStateStatus,
        ProxyWorkflow workflow,
        FlowHubConfig flowHubConfig,
        string? workflowExecutionReasonCode,
        bool isReenrollment,
        ContactDetail? contactDetail = null)
    {
        string? parentStateId = null;
        if (workflow.WorkflowType == WorkflowType.AIAgent)
        {
            try
            {
                var runningStates = await _stateService.GetRunningStatesAsync(
                    objectId,
                    objectType,
                    sleekflowCompanyId,
                    eventBody);
                parentStateId = _stateService.GetParentState(runningStates, workflow.WorkflowId).ParentState.Id;
            }
            catch (Exception exception)
            {
                _logger.LogWarning(exception, "No Parent State Found.");
            }
        }

        _logger.LogInformation("begin to create state, eventBody {EventBody}", JsonConvert.SerializeObject(eventBody));
        var state = await _stateService.CreateStateAsync(
            objectId,
            objectType,
            sleekflowCompanyId,
            initialStateStatus,
            null,
            null,
            isReenrollment,
            workflow,
            eventBody,
            flowHubConfig,
            contactDetail,
            parentStateId);

        if (workflow.WorkflowScheduleSettings is { ScheduleType: WorkflowScheduleTypes.PredefinedDateTime })
        {
            var waitUntil = workflow.WorkflowScheduleSettings.ScheduledAt!.Value;

            // old scheduled workflow schema && using in step enrollment condition check logic
            if (eventBody is OnContactCreatedEventBody onContactCreatedEventBody
                && workflow.WorkflowScheduleSettings.RecurringSettings is not null
                && onContactCreatedEventBody.CreatedAt > workflow.WorkflowScheduleSettings.ScheduledAt.Value
                && !workflow.WorkflowScheduleSettings.IsNonOldScheduledWorkflowSchema())
            {
                waitUntil = _workflowRecurringSettingsParser.GetNextOccurrence(
                    workflow.WorkflowScheduleSettings.ScheduledAt.Value,
                    workflow.WorkflowScheduleSettings.RecurringSettings);
            }
            else if (eventBody is OnContactRecurrentlyEnrolledEventBody onContactRecurrentlyEnrolledEventBody)
            {
                waitUntil = onContactRecurrentlyEnrolledEventBody.NextRecurrence;
            }

            await _stateAggregator.SetSysVarStringAsync(
                state,
                StateSystemVarNames.PredefinedDateTimeWorkflowEnrollment,
                waitUntil.ToString("o"));
        }

        await _workflowRuntimeService.ExecuteWorkflowAsync(
            state,
            workflowExecutionReasonCode);
    }

    private async Task AggregateStateAndExecuteWorkflowSubscriptionsAsync(
        EventBody eventBody,
        ProxyState existingState)
    {
        var stateSubscriptions =
            await _stateSubscriptionService.GetStateSubscriptionsAsync(existingState.Id);
        var matchedStateSubscriptions = stateSubscriptions
            .Where(s => s.EventName == eventBody.EventName)
            .ToList();

        // Evaluate the condition for each state subscription one by one
        foreach (var subscription in matchedStateSubscriptions)
        {
            var hasFulfilledSubscriptionCondition = await _stateEvaluator.EvaluateExpressionAsync(
                existingState,
                subscription.Condition,
                eventBody);

            if (hasFulfilledSubscriptionCondition is true)
            {
                try
                {
                    // If the ETag is matched, then the following will be executed
                    await _stateSubscriptionService.UpdateStateSubscriptionIsExecutedAsync(
                        subscription,
                        true);
                }
                catch (CosmosException e) when (e.StatusCode == HttpStatusCode.PreconditionFailed)
                {
                    // This means the other instance has already executed based on the subscription
                    continue;
                }

                await _stateAggregator.AggregateStateStepBodyAsync(
                    existingState,
                    subscription.StepId,
                    JsonConvert.SerializeObject(eventBody, JsonConfig.DefaultJsonSerializerSettings));

                await _stepExecutorActivator.CompleteStepAsync(
                    existingState.Id,
                    subscription.StepId,
                    subscription.StackEntries,
                    StepExecutionStatuses.Complete,
                    eventBody);
            }
        }
    }

    private Task<bool> IsWorkflowEnrollmentHitRateLimitAsync(
        ProxyWorkflow workflow,
        string objectId)
    {
        return _requestRateLimiter.IsHitLimitAsync(
            $"workflow-enrollment-{workflow.SleekflowCompanyId}",
            $"workflow-enrollment:{workflow.SleekflowCompanyId}:{workflow.WorkflowVersionedId}:{objectId}",
            _workflowRateLimitConfig.EnrollmentRateLimitWindowSeconds,
            _workflowRateLimitConfig.MaxEnrollmentAllowedWithinWindow);
    }

    private Task<bool> IsSleekflowEmailNotificationSendingHitRateLimitAsync(ProxyWorkflow workflow)
    {
        return _requestRateLimiter.IsHitLimitAsync(
            $"workflow-enrollment-email-notification:{workflow.SleekflowCompanyId}",
            $"workflow-enrollment-email-notification:{workflow.SleekflowCompanyId}:{workflow.WorkflowVersionedId}",
            _workflowEnrollmentRateLimitEmailNotificationConfig.BlockedEnrollmentEmailNotificationRateLimitWindowSeconds,
            _workflowEnrollmentRateLimitEmailNotificationConfig.MaxBlockedEnrollmentEmailAllowedWithinWindow);
    }

    private async Task HandleBlockedWorkflowEnrollmentAsync(
        string objectId,
        string objectType,
        EventBody eventBody,
        ProxyWorkflow workflow,
        FlowHubConfig flowHubConfig,
        bool isReenrollment)
    {
        var state = await _stateService.CreateStateAsync(
            objectId,
            objectType,
            workflow.SleekflowCompanyId,
            StateStatuses.Blocked,
            StateReasonCodes.EnrollmentRateLimited,
            TimeToLiveConstants.OneYearInSeconds,
            isReenrollment,
            workflow,
            eventBody,
            flowHubConfig);

        await _bus.Publish(
            new OnWorkflowExecutionBlockedEvent(
                state.Identity.SleekflowCompanyId,
                state.Id,
                null,
                state.Identity,
                state.WorkflowContext.SnapshottedWorkflow.WorkflowType));

        var isSleekflowEmailNotificationSendingHitRateLimit =
            await IsSleekflowEmailNotificationSendingHitRateLimitAsync(workflow);

        if (!isSleekflowEmailNotificationSendingHitRateLimit)
        {
            await SleekflowEmailNotificationServiceCall.SendWorkflowInfiniteLoopEmailAsync(
                workflow.SleekflowCompanyId,
                workflow.WorkflowId,
                workflow.Name,
                flowHubConfig.Origin,
                DateTimeOffset.UtcNow);
        }
    }

    public async Task HandleWorkflowExecutionUsageReachedThresholdAsync(FlowHubConfig flowHubConfig, double percentage)
    {
        if (flowHubConfig.IsUsageLimitAutoScalingEnabled)
        {
            return;
        }

        var threshold = percentage switch
        {
            >= 75.00d and < 90.00d => 75.00d,
            >= 90.00d and < 100.00d => 90.00d,
            >= 100.00d => 100.00d,
            _ => 0.00d
        };

        if (threshold <= 0d)
        {
            return;
        }

        var tempDataKey = await GetDataKeyForSendUsageReachedThresholdEmailAsync(flowHubConfig, threshold);

        Lock? @lock = null;

        try
        {
            if (await _emailSentRecordService.IsEmailSentAsync(tempDataKey))
            {
                return;
            }

            @lock = await _lockService.WaitUnitLockAsync(
                [
                    nameof(FlowHubEventHandler),
                    nameof(HandleWorkflowExecutionUsageReachedThresholdAsync),
                    tempDataKey
                ],
                TimeSpan.FromSeconds(5),
                TimeSpan.FromSeconds(15));

            if (await _emailSentRecordService.IsEmailSentAsync(tempDataKey))
            {
                return;
            }

            await SleekflowEmailNotificationServiceCall.SendExecutionUsageReachedThresholdEmailAsync(
                flowHubConfig.Origin,
                flowHubConfig.SleekflowCompanyId,
                threshold);

            await _emailSentRecordService.CreateRecordAsync(tempDataKey);

            _logger.LogInformation(
                "Triggered ExecutionUsageReachedThreshold Email Successfully. CompanyId: {CompanyId}, Origin: {Origin}, UsagePercentage: {UsagePercentage}, Threshold: {Threshold}",
                flowHubConfig.SleekflowCompanyId,
                flowHubConfig.Origin,
                percentage,
                threshold);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to Trigger Send ExecutionUsageReachedThreshold Email. CompanyId: {CompanyId}, Origin: {Origin}, UsagePercentage: {UsagePercentage}, Threshold: {Threshold}",
                flowHubConfig.SleekflowCompanyId,
                flowHubConfig.Origin,
                percentage,
                threshold);
        }
        finally
        {
            if (@lock != null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }
    }

    private async Task<string> GetDataKeyForSendUsageReachedThresholdEmailAsync(FlowHubConfig flowHubConfig, double threshold)
    {
        var (fromDateTime, toDateTime) = await _companyUsageCycleService.GetUsageCycleAsync(flowHubConfig);

        var usagePeriod = $"{fromDateTime:yyyyMMdd}{toDateTime:yyyyMMdd}";

        return $"{flowHubConfig.SleekflowCompanyId}:SendUsageReachedThresholdEmail:{usagePeriod}:{threshold:0.00}";
    }
}