using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Workers.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.Salesforce;

public class SyncObjects
{
    private readonly ILogger<SyncObjects> _logger;

    public SyncObjects(
        ILogger<SyncObjects> logger)
    {
        _logger = logger;
    }

    public class SyncObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SyncObjectsInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    [Function("Salesforce_SyncObjects")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<SyncObjectsInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (SyncObjectsInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (syncObjectsInput, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync("Salesforce_SyncObjects_Orchestrator", syncObjectsInput);

        logger.LogInformation($"Started Salesforce_SyncObjects_Orchestrator with ID = [{instanceId}]");

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get Salesforce_SyncObjects_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}