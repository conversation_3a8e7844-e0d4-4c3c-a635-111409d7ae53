using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence.CrmHubDb;

public interface ICrmHubDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class CrmHubDbConfig : IConfig, ICrmHubDbConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public CrmHubDbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_CRM_HUB_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_CRM_HUB_DB_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_CRM_HUB_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_CRM_HUB_DB_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_CRM_HUB_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_CRM_HUB_DB_DATABASE_ID");
    }
}