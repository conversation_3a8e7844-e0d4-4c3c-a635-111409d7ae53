﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.FlowHubEvents;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.FlowHubEvents;

public interface IFlowHubEventRepository : IRepository<FlowHubEvent>
{
}

public class FlowHubEventRepository : BaseRepository<FlowHubEvent>, IFlowHubEventRepository, IScopedService
{
    public FlowHubEventRepository(
        ILogger<FlowHubEventRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}