﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateConversationStatusChangedLog : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateConversationStatusChangedLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateConversationStatusChangedLogInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public ConversationStatusChangedLogData Data { get; set; }

        [JsonConstructor]
        public CreateConversationStatusChangedLogInput(
            string? sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string auditLogText,
            ConversationStatusChangedLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class CreateConversationStatusChangedLogOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateConversationStatusChangedLogOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateConversationStatusChangedLogOutput> F(
        CreateConversationStatusChangedLogInput createConversationStatusChangedLogInput)
    {
        var dataStr = JsonConvert.SerializeObject(createConversationStatusChangedLogInput.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var id = _idService.GetId("UserProfileAuditLog");
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
            new UserProfileAuditLog(
                id,
                (distributedInvocationContext?.SleekflowCompanyId
                 ?? createConversationStatusChangedLogInput.SleekflowCompanyId)
                ?? throw new InvalidOperationException(),
                distributedInvocationContext?.SleekflowStaffId
                ?? createConversationStatusChangedLogInput.SleekflowStaffId,
                createConversationStatusChangedLogInput.SleekflowUserProfileId,
                UserProfileAuditLogTypes.ConversationStatusChanged,
                createConversationStatusChangedLogInput.AuditLogText,
                data,
                DateTimeOffset.UtcNow,
                null));

        return new CreateConversationStatusChangedLogOutput(id);
    }
}