using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

// Core Agents: LeadClassifierAgent, DecisionAgent, StrategyAgent, KnowledgeRetrievalAgent, ReviewerAgent
public partial class LeadNurturingAgentDefinitions
{
    [method: JsonConstructor]
    public class LeadClassifierAgentResponse(string agentName, string reasoning, string score, string classification)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("score")]
        public string Score { get; set; } = score;

        [JsonProperty("classification")]
        public string Classification { get; set; } = classification;
    }

    [method: JsonConstructor]
    public class DecisionAgentResponse(
        string agentName,
        string area1Reasoning,
        string area2Reasoning,
        string area3Reasoning,
        string step4Reasoning,
        string step5Reasoning,
        string decision)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("area1_reasoning")]
        public string Area1Reasoning { get; set; } = area1Reasoning;

        [JsonProperty("area2_reasoning")]
        public string Area2Reasoning { get; set; } = area2Reasoning;

        [JsonProperty("area3_reasoning")]
        public string Area3Reasoning { get; set; } = area3Reasoning;

        [JsonProperty("step4_reasoning")]
        public string Step4Reasoning { get; set; } = step4Reasoning;

        [JsonProperty("step5_reasoning")]
        public string Step5Reasoning { get; set; } = step5Reasoning;

        [JsonProperty("decision")]
        public string Decision { get; set; } = decision;
    }

    [method: JsonConstructor]
    public class StrategyAgentResponse(
        string agentName,
        string needKnowledgeReasoning,
        string? needKnowledge,
        string strategyReasoning,
        string strategy)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("need_knowledge_reasoning")]
        public string NeedKnowledgeReasoning { get; set; } = needKnowledgeReasoning;

        [JsonProperty("need_knowledge")]
        public string? NeedKnowledge { get; set; } = needKnowledge;

        [JsonProperty("strategy_reasoning")]
        public string StrategyReasoning { get; set; } = strategyReasoning;

        [JsonProperty("strategy")]
        public string Strategy { get; set; } = strategy;
    }

    [method: JsonConstructor]
    public class KnowledgeRetrievalAgentResponse(
        string agentName,
        string knowledgeOverviewReasoning,
        string knowledgeOverview,
        string? decision = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("knowledge_overview_reasoning")]
        public string KnowledgeOverviewReasoning { get; set; } = knowledgeOverviewReasoning;

        [JsonProperty("knowledge_overview")]
        public string KnowledgeOverview { get; set; } = knowledgeOverview;

        [JsonProperty("decision")]
        public string? Decision { get; set; } = decision;
    }

    [method: JsonConstructor]
    public class ReviewerAgentResponse(
        string agentName,
        string placeholderAbsenceReasoning,
        string knowledgeIntegrationReasoning,
        string responseLanguageReasoning,
        string naturalnessReasoning,
        string review)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("placeholder_absence_reasoning")]
        public string PlaceholderAbsenceReasoning { get; set; } = placeholderAbsenceReasoning;

        [JsonProperty("knowledge_integration_reasoning")]
        public string KnowledgeIntegrationReasoning { get; set; } = knowledgeIntegrationReasoning;

        [JsonProperty("response_language_reasoning")]
        public string ResponseLanguageReasoning { get; set; } = responseLanguageReasoning;

        [JsonProperty("naturalness_reasoning")]
        public string NaturalnessReasoning { get; set; } = naturalnessReasoning;

        [JsonProperty("review")]
        public string Review { get; set; } = review;
    }

    public ChatCompletionAgent GetLeadClassifierAgent(Kernel kernel, PromptExecutionSettings settings)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("score", "string"),
                new PromptExecutionSettingsUtils.Property("classification", "string"),
            ]);

        return new ChatCompletionAgent
        {
            Name = LeadClassifierAgentName,
            Description =
                "Classifies leads as hot, warm, cold, or existing_customer using a weighted scoring algorithm based on intent, buying signals, question depth, sentiment, and customer fit.",
            HistoryReducer = new GeminiChatHistoryReducer(),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{LeadClassifierAgentName}}. Your task is to classify leads as **hot**, **warm**, or **cold** based on the CUSTOMER CONVERSATION CONTEXT provided.

                  ---Core Responsibilities---
                  1. Read the CUSTOMER CONVERSATION CONTEXT
                  2. If the context is limited to an initial greeting or simple message (e.g., "Hi," "Is this available?"), classify as **warm**.
                  3. If the context has not enough information (only 1-5 messages), classify as **warm**.
                  4. If the context shows the lead is an existing customer to ask for further details of purchased products and the customer has no interest in a new product/service, classify as **existing_customer**. The score should be 50.
                     e.g. the lead asks for support on a product they have already purchased, the lead mentioned they have subscribed to a service, etc.
                  5. If not matching the above rules, analyze the CUSTOMER CONVERSATION CONTEXT and follow the following definitions to determine the lead's classification.

                  ---General Lead Definitions---
                  - **Hot Lead**: Ready to purchase or very likely to convert (no concerns or all concerns have been addressed).
                  - **Warm Lead**: Moderately likely to convert.
                  - **Cold Lead**: Unlikely to convert.

                  ---In-depth Classification Criteria---
                  Take the three buyer journey stages, awareness (cold to warm), consideration (warm), and decision (hot) as a reference, to classify the leads.
                  Evaluate the lead using the following weighted criteria. Score each out of 100, then calculate the total score as a weighted sum.

                  1. **Intent & Interest Level (30%)**
                     - **80-100**: Intent to purchase.
                     - **60-79**: Multiple specific questions about features, specifications, or clear purchase intent (e.g., "Will this work for X?").
                     - **30-59**: Basic questions (e.g., "Do you have this?") without strong intent.
                     - **<30**: No interest shown.

                  2. **Buying Signals (35%)**
                     - **80-100**: Explicit purchase intent, urgency, or negotiation (e.g., "I need it by Friday, what's the price?").
                     - **50-79**: Pricing/availability questions without clear intent (e.g., "How much is it?").
                     - **<50**: No buying signals.

                  3. **Depth & Specificity of Questions (25%)**
                     - **80-100**: Detailed follow-up questions or deep discussion (e.g., "How does this compare to Y?").
                     - **50-79**: Basic questions without follow-up.
                     - **<50**: No or vague questions.

                  4. **Sentiment & Engagement Tone (5%)**
                     - **80-100**: Consistently enthusiastic/positive tone.
                     - **50-79**: Neutral or mixed tone.
                     - **<50**: Negative tone.

                  5. **Customer Fit & Alignment (5%)**
                     - Score based on alignment with target persona (e.g., role, industry). **100** for perfect fit, lower for deviations.

                  ---Scoring and Classification---
                  - Calculate the total score: (Intent × 0.3) + (Buying Signals × 0.35) + (Depth × 0.25) + (Sentiment × 0.05) + (Fit × 0.05).
                  - Classify based on the total score:
                    - **Hot**: ≥ 90
                    - **Warm**: 40 to <90
                    - **Cold**: < 40

                  #### Output Format
                  {
                    "agent_name": "{{LeadClassifierAgentName}}",
                    "reasoning": "[Concise reasoning citing key indicators. Max 150 words.]",
                    "score": "[Total score out of 100]",
                    "classification": "[hot/warm/cold/existing_customer]"
                  }
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }

    public ChatCompletionAgent GetDecisionAgent(Kernel kernel, PromptExecutionSettings settings)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("area1_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("area2_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("area3_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("step4_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("step5_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("decision", "string"),
            ]);

        var leadNurturingTools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;

        // Format additional classification rules if they exist
        var additionalClassificationRules = leadNurturingTools?.AdditionalHandsOffClassificationRules == null
            ? """
              ====ADDITIONAL CLASSIFICATION RULES====
              No additional classification rules.
              ====ADDITIONAL CLASSIFICATION RULES====
              """
            : $"""
               ====ADDITIONAL CLASSIFICATION RULES====
               Check the following conditions in order, applying the first match:
               {string.Join("\n", leadNurturingTools.AdditionalHandsOffClassificationRules.Rules.Select(r => $"- {r.Rule}"))}
               ====ADDITIONAL CLASSIFICATION RULES====
               """;

        // Check demo tool availability and set the note
        var demoToolAvailable = leadNurturingTools?.DemoTool != null;
        var demoToolClassificationRules = demoToolAvailable
            ? $"""
               - If the lead expresses a desire or confirms interest in scheduling a demo or participating a demo, classify as "request_demo". Note that "demo" can be synonymous with "appointment", "call", "meeting", etc.
               - If the lead is providing information to schedule a demo, classify as "request_demo". Note that "demo" can be synonymous with "appointment", "call", "meeting", etc.
               - If the lead is confirming the demo information, classify as "request_demo". Note that "demo" can be synonymous with "appointment", "call", "meeting", etc.
               - If the lead is highly engaged ({DecisionAgentName}'s score > 70) and asking for interacting with a human representative, classify as "request_demo".
               """
            : string.Empty;

        var instruction =
            $$"""
              {{GetSharedSystemPrompt()}}
              You are {{DecisionAgentName}}. Your job is to decide the next step in the lead nurturing process based on the CUSTOMER CONVERSATION CONTEXT and the lead's classification.

              ### Steps to Follow:

              **1. Evaluate different areas**

                 Area 1: Evaluate Overriding Classification Rules:**
                 Check the additional classification rules provided below. When applying these rules, consider the intent behind the customer's messages and the context of the CUSTOMER CONVERSATION CONTEXT.
                 {{additionalClassificationRules}}

                 Area 2: Evaluate Special Classification Rules:**
                 Check the following conditions in order, applying the first match:
                 {{demoToolClassificationRules}}
                 - If the lead expresses a desire or confirms interest in interacting with a human representative, such as requesting to speak to a person, or being contacted by a person, or being helped by the another team, or similar intents, classify as "human". However, simply asking helps or requesting information does not indicate a desire to interact with a human.
                 - If the lead expresses a desire or confirms interest in interacting over the phone or requests information to be sent to their phone, classify as "phone".
                 - If the lead expresses a desire or confirms interest in interacting over the email or requests information to be sent to their email, classify as "email".
                 - If the lead expresses a desire or confirms interest in filing a complaint, classify as "complaint".
                 - If the lead indicates they are not interested, want to end the conversation, or are done with the interaction, classify as "drop_off".

                 Area 3: Get {{LeadClassifierAgentName}}'s Classification:**
                 - Search for the most recent message from {{LeadClassifierAgentName}}.
                 - Extract the classification value (e.g., "hot", "cold", "warm") from this message, expecting it in a structured format like {"classification": "value"}.
                 - If no classification is found or the format is invalid, default to "warm" and note this in the reasoning.

              **2. Compute the Final Classification:**
              - If any overriding classification rules apply, use that classification. (Highest Precedence)
              - Else, if any special classification rules apply, use that classification.
              - Else, use the {{LeadClassifierAgentName}}'s classification. (Lowest Precedence)

              **3. Apply the Decision Table:**
              Based on the final classification, determine the decision using the following table:
              - "hot" -> "assign_hot"
              - "cold" -> "assign_cold"
              - "warm" -> "continue_nurturing"
              - "existing_customer" -> "continue_nurturing"
              - "human" -> "assign_human"
              - "phone" -> "assign_human"
              - "email" -> "assign_human"
              - "complaint" -> "assign_human"
              - "drop_off" -> "assign_drop_off"
              - "request_demo" -> "{{(demoToolAvailable ? "schedule_demo" : "assign_human")}}"
              - If the final classification is not listed -> "continue_nurturing".

              After performing these steps, return a JSON object containing the reasoning for each step and the final decision:

              {
                  "agent_name": "{{DecisionAgentName}}",
                  "area1_reasoning": "Area 1: Evaluate Overriding Classification Rules - [Detail which overriding rules apply or do not apply and why, based on the CUSTOMER CONVERSATION CONTEXT and the lead's intent.]",
                  "area2_reasoning": "Area 2: Evaluate Special Classification Rules - [Detail which special rules apply or do not apply and why, based on the CUSTOMER CONVERSATION CONTEXT and the lead's intent. The first match takes precedence.]",
                  "area3_reasoning": "Area 3: Get {{LeadClassifierAgentName}}'s Classification - [Explain how you obtained the classification or why you defaulted to 'warm'.]",
                  "step2_reasoning": "Step 2: Compute the Final Classification - [Explain how you determined the final classification based on the precedence: overriding rules > special rules > {{LeadClassifierAgentName}}'s classification.]",
                  "step3_reasoning": "Step 3: Apply Decision Table - [Explain which decision was chosen based on the final classification and the decision table.]",
                  "decision": "[The final decision based on the decision table.]"
              }

              **Notes:**
              - When evaluating the special or overriding classification rules, focus on the intent behind the lead's messages rather than specific keywords. Consider the context of the CUSTOMER CONVERSATION CONTEXT, including previous messages and offers made by us.
              - For example, if the agent offers a demo and the lead responds positively (e.g., "好", "sure", "let's do it"), interpret this as confirmation of interest in the demo.
              - Area 1's, Area 2's, and Area 3's reasoning should be independent and self-contained. Each relies only on the CUSTOMER CONVERSATION CONTEXT and the corresponding rules provided.
              """;

        return new ChatCompletionAgent
        {
            Name = DecisionAgentName,
            Description =
                "Determines the next action in the lead nurturing process based on lead classification and conversation context, applying decision table mapping with overrides for special conditions.",
            HistoryReducer = new GeminiChatHistoryReducer(),
            Instructions = instruction.TrimEnd(),
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }

    public ChatCompletionAgent GetStrategyAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string additionalInstructionStrategy)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("need_knowledge_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("need_knowledge", "string", true),
                new PromptExecutionSettingsUtils.Property("strategy_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("strategy", "string"),
            ]);

        return new ChatCompletionAgent
        {
            Name = StrategyAgentName,
            Description =
                "Provides response crafting guidance for nurturing warm leads by evaluating knowledge requirements and generating structured strategy with customer journey analysis.",
            HistoryReducer = new GeminiChatHistoryReducer(),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{StrategyAgentName}}. Your role is to provide response crafting guidance for nurturing warm leads based on the {{DecisionAgentName}}'s decision and the CUSTOMER CONVERSATION CONTEXT.

                  **Steps:**
                  1. Review the most recent message from the {{DecisionAgentName}} and the entire CUSTOMER CONVERSATION CONTEXT, including the latest customer message.
                  2. Determine if additional knowledge is needed and provide reasoning
                     - Analyze the recent messages to see if they are general greetings (e.g., "Hi", "Hello", "Good morning").
                     - If they are greetings, set `"need_knowledge"` to `null` and provide reasoning such as "The recent message is a general greeting, so no specific knowledge is required."
                     - If they are not greetings, evaluate what specific knowledge might be needed based on the content of the messages. Provide reasoning that explains why certain knowledge is required, for example, "The customer is asking about product features, so knowledge about the product's specifications and benefits is needed."
                     - If unsure, provide reasoning that explains the uncertainty and suggests possible knowledge that might be helpful, for example, "The customer mentioned a technical term I'm not familiar with, so knowledge about that term or related concepts might be necessary."
                  3. Provide styling guidance for crafting the response to nurture the lead

                  Your output should be in the format:
                  {
                    "agent_name": "{{StrategyAgentName}}",
                    "need_knowledge_reasoning": "[Your reasoning here]",
                    "need_knowledge": "[What's needed from the knowledge base]" or null,
                    "strategy_reasoning": "[Your reasoning here]",
                    "strategy": "[Your styling guidance]"
                  }

                  [Your reasoning here] should include:
                  Observations (Customer Journey Snapshot)
                  - A quick picture of where the customer's at based on our chat so far.
                  - Pick out what stands out about them—their curiosity, their goals, or even a hint of hesitation.
                  - Pinpoint their stage (Awareness, Interest, Desire, Action) with a light touch.
                  - Highlight opportunities (e.g., something they're seeking that we can deliver), risks (e.g., a concern we can smooth over), and unique traits (e.g., what makes their story theirs).

                  [What's needed from the knowledge base] should include two parts:
                  1. The context for the lead's inquiry so the {{KnowledgeRetrievalAgentName}} can provide the right information. For each pieces of context, you should mention whether it is necessary.
                     - For example, "The customer is asking about product features, so knowledge about the product's specifications and benefits is needed."
                  2. The keywords or phrases that the {{KnowledgeRetrievalAgentName}} should focus on to retrieve the most relevant information.

                  [Your styling guidance] should include:
                  1. Communication and Styling Guidance
                     - Suggest a tone (e.g., warm, curious), language (e.g., casual or polished), and vibe (e.g., encouraging questions) that fits their personality and the conversation.
                       - Please take ====REQUESTED TONE==== as the reference. It is a configuration set by the users.
                       - Guide on a general strategy for responding (e.g., "Be friendly and open, but not too pushy").
                     - The messaging channel is on WhatsApp. Guide how to message
                  2. Personalization Suggestions
                     - Highlight the key points to personalize the conversation.
                     - Suggest questions to ask the customer to gather more information.
                     - Ensure your suggestions are unique across responses.
                       - When you find there are repeated words / topics in the CUSTOMER CONVERSATION CONTEXT, suggest not to include them in the current response.
                       - e.g. if the last response already mentions "product features", suggest not to include "product features" in the current response.

                  ### Additional Instructions
                  {{(string.IsNullOrEmpty(additionalInstructionStrategy) ? string.Empty : additionalInstructionStrategy)}}

                  ### Important Notes
                  - Connect your suggestions with the observations.
                  - The {{ResponseCrafterAgentName}} follows your guidance to craft the response. Please ensure your guidance would lead to a suitable response for the context. And make it natural and unique.
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }

    public ChatCompletionAgent GetKnowledgeRetrievalAgent(
        Kernel kernel,
        IAgenticKnowledgePlugin knowledgePlugin,
        PromptExecutionSettings settings,
        ILeadNurturingCollaborationChatCacheService leadNurturingCollaborationChatCacheService)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("knowledge_overview_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("knowledge_overview", "string"),
                new PromptExecutionSettingsUtils.Property("decision", "string", true)
            ]);

        return new ChatCompletionAgent
        {
            Name = KnowledgeRetrievalAgentName,
            Description =
                "Retrieves relevant information from the knowledge base and evaluates query-knowledge alignment to determine sufficiency for response generation.",
            HistoryReducer = new KnowledgeRetrievalAgentGeminiChatHistoryReducer(
                [
                    "Context",
                    StrategyAgentName,
                    KnowledgeRetrievalAgentName,
                ],
                leadNurturingCollaborationChatCacheService,
                (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!,
                knowledgePlugin,
                kernel,
                this),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{KnowledgeRetrievalAgentName}}. Your role is to retrieve information from the knowledge base through the provided tools.

                  ---Instructions---
                  1. Look for the most recent message from the {{StrategyAgentName}}. The message contains a field "need_knowledge". This field indicates the specific information required from the knowledge base.
                  2. The system will process the "need_knowledge". The latest message from the system will contain the field "knowledge" with the retrieved information.
                  3. Evaluate the "knowledge" provided by the system and determine if it directly answers the query, indirectly answers the query, partially answers the query, or is completely unhelpful.
                  4. In the "knowledge_overview_reasoning" field, provide a detailed explanation of how you evaluated the knowledge. Include specific reasons why the knowledge is classified as directly answering, indirectly relevant, partially answering, or completely unhelpful. Reference specific parts of the knowledge and the query in your reasoning.
                  5. In the "knowledge_overview" field, provide a concise summary of your evaluation based on the reasoning.
                  6. Decide on the "decision" field if necessary (e.g., "assign_insufficient_info" if the knowledge is completely unhelpful).

                  ---Output Format---
                  - If the knowledge directly answers the query:
                    { "agent_name": "{{KnowledgeRetrievalAgentName}}", "knowledge_overview_reasoning": "The retrieved knowledge directly answers the query because [detailed reasoning].", "knowledge_overview": "The knowledge directly provides the required information.", "decision": null }
                  - If the knowledge does not directly answer the query but contains relevant information:
                    { "agent_name": "{{KnowledgeRetrievalAgentName}}", "knowledge_overview_reasoning": "The knowledge does not directly address the query, but it contains relevant information because [detailed reasoning].", "knowledge_overview": "The knowledge provides relevant information that can be applied to the query.", "decision": null }
                  - If the knowledge partially answers the query:
                    { "agent_name": "{{KnowledgeRetrievalAgentName}}", "knowledge_overview_reasoning": "The knowledge partially answers the query by providing [partial information], but lacks [missing information] because [reasoning].", "knowledge_overview": "The knowledge partially addresses the query but is incomplete.", "decision": null }
                  - If the knowledge is completely unhelpful:
                    { "agent_name": "{{KnowledgeRetrievalAgentName}}", "knowledge_overview_reasoning": "The retrieved knowledge is completely unhelpful because [detailed reasoning].", "knowledge_overview": "The knowledge is irrelevant to the query.", "decision": "assign_insufficient_info" }

                  ---Note---
                  - Ensure that your "knowledge_overview_reasoning" is thorough and references specific content from the knowledge and the query.
                  - The "knowledge_overview" should be a concise summary based on your reasoning.
                  - Do not provide information from your internal knowledge; always rely only on the retrieved knowledge.
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }

    public ChatCompletionAgent GetReviewerAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        string responseLanguage,
        ILeadNurturingCollaborationChatCacheService chatCacheService)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("placeholder_absence_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("knowledge_integration_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("response_language_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("naturalness_reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("review", "string"),
            ]);

        return new ChatCompletionAgent
        {
            Name = ReviewerAgentName,
            Description =
                "Reviews proposed customer responses for quality, validates against placeholders, knowledge integration, language requirements, and WhatsApp naturalness before approval.",
            HistoryReducer = new ResponseCrafterAgentGeminiChatHistoryReducer(
                [
                    "Context",
                    KnowledgeRetrievalAgentName,
                    ActionAgentName,
                    ResponseCrafterAgentName,
                    TransitioningResponseCrafterAgentName,
                    InformationGatheringResponseCrafterAgentName
                ],
                chatCacheService,
                (string) kernel.Data[KernelDataKeys.GROUP_CHAT_ID]!,
                this),
            Instructions =
                $$"""
                  {{GetSharedSystemPrompt()}}
                  You are {{ReviewerAgentName}}. You review the proposed customer response for quality before it is sent to the customer. You are the final checkpoint in the agent collaboration process.

                  ---Core Responsibilities---
                  1. Identify which agent has generated the response (one of: {{ResponseCrafterAgentName}}, {{TransitioningResponseCrafterAgentName}}, or {{InformationGatheringResponseCrafterAgentName}})
                  2. Apply appropriate review criteria based on the response type
                  3. Make a final decision on whether the response is ready to be sent to the customer
                  4. Your approval finalizes the chat session and sends the response to the customer

                  ---Review Process---
                  For each type of response, look for the most recent message containing { "response": "[response text]" } and review as follows:

                  ### For {{ResponseCrafterAgentName}} responses:
                  Apply strict review criteria with special focus on the NEW TWO-PHASE approach:

                  1. **Placeholder Absence**
                     - Ensure no placeholders (e.g., [add details], "[insert X]", or common patterns like "1234 5678" for phone numbers) are present
                     - If placeholders are found, provide specific feedback on what needs to be changed. Reasoning should include:
                       - The placeholder content
                       - The reason why it is not customer-ready
                       - What needs to be changed i.e. "Please remove the placeholder. It is not customer-ready."

                  2. **Knowledge Integration**
                     - Verify that factual content matches the knowledge from {{KnowledgeRetrievalAgentName}}
                     - Ensure no unverified information is presented as fact
                     - Check that the structured_response properly incorporated all available knowledge
                     - Verify the final response maintains accuracy while being natural

                  3. **Response Language**
                     - Confirm the response is in `{{responseLanguage}}`

                  4. **WhatsApp Naturalness (NEW)**
                     - Evaluate if the response sounds natural for WhatsApp conversation:
                       - Does it avoid overly formal language?
                       - Is it appropriately concise (30-120 words typically)?
                       - Does it avoid repetitive patterns like always starting with "Hi" or ending with "Thank you"?
                       - Does it sound like a helpful human rather than a corporate bot?
                       - Is the tone conversational and engaging?
                     - Check if the natural_response_reasoning properly addressed making the response human-like
                     - If the response still sounds too formal, robotic, or verbose, provide specific feedback

                  ### For {{TransitioningResponseCrafterAgentName}} responses:
                  Apply moderate review criteria:
                  1. **Clarity and Completeness**
                     - Ensure the transition message clearly explains what happens next
                     - Check that it doesn't contain mistaken commitments or promises

                  2. **Tone and Professionalism**
                     - Verify the message maintains a professional tone
                     - Ensure it's responsive to the customer's needs

                  3. **Response Language**
                     - Confirm the response is in `{{responseLanguage}}`

                  ### For {{InformationGatheringResponseCrafterAgentName}} responses:
                  Apply focused review criteria:
                  1. **Clarity of Request**
                     - Ensure the information request is clear and specific
                     - Check that it explains why the information is needed

                  2. **Privacy Sensitivity**
                     - Verify the request respects customer privacy
                     - Ensure it doesn't ask for excessive or unnecessary information

                  3. **Response Language**
                     - Confirm the response is in `{{responseLanguage}}`

                  ---Output Format---
                  After review, output a structured response:

                  If the response passes review:
                  ```json
                  {
                    "agent_name": "{{ReviewerAgentName}}",
                    "placeholder_absence_reasoning": "[Your reasoning for placeholder check]",
                    "knowledge_integration_reasoning": "[Your reasoning for knowledge check]",
                    "response_language_reasoning": "[Your verification of language]",
                    "naturalness_reasoning": "[For ResponseCrafterAgent: evaluation of WhatsApp naturalness, conversational tone, and human-like quality. For others: brief note on appropriateness]",
                    "review": "approved"
                  }
                  ```

                  If the response fails review:
                  ```json
                  {
                    "agent_name": "{{ReviewerAgentName}}",
                    "placeholder_absence_reasoning": "[Your reasoning for placeholder check]",
                    "knowledge_integration_reasoning": "[Your reasoning for knowledge check]",
                    "response_language_reasoning": "[Your verification of language]",
                    "naturalness_reasoning": "[Specific feedback on what makes the response unnatural, too formal, or not WhatsApp-appropriate]",
                    "review": "rejected with feedback: [specific actionable feedback focusing on naturalness, placeholders, knowledge accuracy, or language]"
                  }
                  ```

                  ---Important Notes---
                  - Your decision should be appropriate to the type of response
                  - For {{ResponseCrafterAgentName}}, pay special attention to the naturalness and human-like quality of the final response
                  - Reject responses that sound too corporate, robotic, or overly formal for WhatsApp
                  - Look for conversational flow and appropriate length
                  - For {{InformationGatheringResponseCrafterAgentName}} and {{TransitioningResponseCrafterAgentName}}, focus more on clarity and appropriateness
                  - Remember that your approval finalizes the response that will be sent to the customer and ends the current chat session
                  """,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }
}