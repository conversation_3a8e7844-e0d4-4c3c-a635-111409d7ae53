using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

/// <summary>
/// Session state for a lead nurturing agent group.
/// </summary>
[method: JsonConstructor]
public class AgentSessionState(
    string companyId,
    string objectId,
    string sessionId,
    bool hasPerformedActions,
    bool shouldCancel)
{
    public string CompanyId { get; set; } = companyId;

    public string ObjectId { get; set; } = objectId;

    public string SessionId { get; set; } = sessionId;

    /// <summary>
    /// Timestamp when this session was created.
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; } = DateTimeOffset.UtcNow;

    /// <summary>
    /// Whether this agent group has performed any actions.
    /// </summary>
    public bool HasPerformedActions { get; set; } = hasPerformedActions;

    /// <summary>
    /// Whether this session should be canceled.
    /// </summary>
    public bool ShouldCancel { get; set; } = shouldCancel;
}

public interface ILeadNurturingAgentSessionManager
{
    /// <summary>
    /// Registers a new agent group session.
    /// </summary>
    Task<bool> RegisterAgentSessionAsync(GroupChatId groupChatId);

    /// <summary>
    /// Marks the specified session as having performed actions.
    /// </summary>
    Task MarkSessionActionsPerformedAsync(GroupChatId groupChatId);

    /// <summary>
    /// Checks if a session should be canceled.
    /// </summary>
    Task<bool> ShouldCancelSessionAsync(GroupChatId groupChatId);

    /// <summary>
    /// Cancels any previous sessions that share the same objectId that haven't performed actions yet.
    /// </summary>
    Task CancelPreviousSessionsAsync(GroupChatId groupChatId);
}

/// <summary>
/// Manages the lifecycle of lead nurturing agent sessions to prevent multiple concurrent responses.
/// </summary>
/// <remarks>
/// This service solves the problem of multiple agent groups processing the same conversation
/// when a customer sends multiple messages in quick succession. It:
/// <list type="bullet">
/// <item>Tracks agent session state in Redis for distributed coordination</item>
/// <item>Allows newer sessions to cancel older ones that haven't performed critical actions</item>
/// <item>Prevents duplicate responses to customers when multiple agent groups are processing</item>
/// <item>Uses Redis transactions and Lua scripts to ensure operations are atomic</item>
/// </list>
///
/// When a new message arrives, older sessions that haven't performed any meaningful actions are
/// marked for cancellation, ensuring only the newest agent group responds to the customer's most
/// recent message. This improves the customer experience by avoiding out-of-sequence or duplicate
/// responses.
/// </remarks>
public class LeadNurturingAgentSessionManager : ILeadNurturingAgentSessionManager, IScopedService
{
    private readonly ILogger<LeadNurturingAgentSessionManager> _logger;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ICacheConfig _cacheConfig;

    private static readonly TimeSpan SessionExpirationTimeSpan = TimeSpan.FromMinutes(30);

    // Lua script for atomically marking a session's actions as performed
    private const string MarkActionsPerformedScript = @"
        local key = KEYS[1]
        local sessionJson = redis.call('GET', key)
        if not sessionJson then
            return 0
        end

        local updatedJson = string.gsub(sessionJson, '""HasPerformedActions"":false', '""HasPerformedActions"":true')
        redis.call('SET', key, updatedJson, 'EX', ARGV[1])
        return 1
    ";

    // Lua script for atomically canceling previous sessions
    // that haven't performed any actions (IMPORTANT)
    private const string CancelPreviousSessionsScript = @"
        local agentSessionIdsKey = KEYS[1]
        local currentAgentSessionId = ARGV[1]
        local expireSeconds = ARGV[2]
        local sessionPrefix = ARGV[3]
        local canceledCount = 0

        local agentSessionIds = redis.call('SMEMBERS', agentSessionIdsKey)
        for i, agentSessionId in ipairs(agentSessionIds) do
            if agentSessionId ~= currentAgentSessionId then
                local sessionKey = sessionPrefix .. agentSessionId
                local sessionJson = redis.call('GET', sessionKey)

                if sessionJson then
                    if not string.match(sessionJson, '""HasPerformedActions"":true') then
                        local updatedJson = string.gsub(sessionJson, '""ShouldCancel"":false', '""ShouldCancel"":true')
                        redis.call('SET', sessionKey, updatedJson, 'EX', expireSeconds)
                        canceledCount = canceledCount + 1
                    end
                end
            end
        end
        return canceledCount
    ";

    public LeadNurturingAgentSessionManager(
        ILogger<LeadNurturingAgentSessionManager> logger,
        IConnectionMultiplexer connectionMultiplexer,
        ICacheConfig cacheConfig)
    {
        _logger = logger;
        _connectionMultiplexer = connectionMultiplexer;
        _cacheConfig = cacheConfig;
    }

    // Strongly typed primary implementations
    public async Task<bool> RegisterAgentSessionAsync(GroupChatId groupChatId)
    {
        var database = _connectionMultiplexer.GetDatabase();
        var objectId = groupChatId.ObjectId;
        var sessionId = groupChatId.SessionId;

        try
        {
            // Store session state for this group
            var sessionState = new AgentSessionState(
                groupChatId.CompanyId,
                objectId,
                sessionId,
                hasPerformedActions: false,
                shouldCancel: false);

            var sessionJson = JsonConvert.SerializeObject(sessionState, JsonConfig.DefaultJsonSerializerSettings);
            var agentSessionKey = GetAgentSessionKey(groupChatId);
            var allAgentSessionIdsKey = GetAllAgentSessionIdsKey(groupChatId);

            // Use a transaction to ensure atomicity for the session registration and set membership
            var transaction = database.CreateTransaction();

            // Set the session state in Redis
            transaction.StringSetAsync(
                agentSessionKey,
                sessionJson,
                SessionExpirationTimeSpan,
                When.Always,
                CommandFlags.None);

            // Add this session to the list of sessions for this chat
            transaction.SetAddAsync(
                allAgentSessionIdsKey,
                sessionId);

            // Ensure the set expires eventually
            transaction.KeyExpireAsync(allAgentSessionIdsKey, SessionExpirationTimeSpan);

            // Execute the transaction atomically
            var success = await transaction.ExecuteAsync();

            if (!success)
            {
                _logger.LogWarning(
                    "Failed to register agent session transaction for objectId {ObjectId}, groupChatId {GroupChatId}",
                    objectId,
                    groupChatId.ToString());
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to register agent session for objectId {ObjectId}, groupChatId {GroupChatId}",
                objectId,
                groupChatId.ToString());
            return false;
        }
    }

    public async Task MarkSessionActionsPerformedAsync(GroupChatId groupChatId)
    {
        var database = _connectionMultiplexer.GetDatabase();

        try
        {
            // Use Lua script to atomically check if session exists and update it
            var result = await database.ScriptEvaluateAsync(
                MarkActionsPerformedScript,
                new RedisKey[]
                {
                    GetAgentSessionKey(groupChatId)
                },
                new RedisValue[]
                {
                    (int) SessionExpirationTimeSpan.TotalSeconds
                });

            var success = (long) result == 1;

            if (!success)
            {
                _logger.LogWarning(
                    "Attempted to mark actions performed for non-existent session {GroupChatId}",
                    groupChatId.ToString());
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to mark session actions performed for groupChatId {GroupChatId}",
                groupChatId.ToString());
        }
    }

    public async Task<bool> ShouldCancelSessionAsync(GroupChatId groupChatId)
    {
        var database = _connectionMultiplexer.GetDatabase();

        try
        {
            // This is a simple read operation which is already atomic
            var sessionStateJson = await database.StringGetAsync(GetAgentSessionKey(groupChatId));
            if (!sessionStateJson.HasValue)
            {
                // If session doesn't exist, don't cancel it (it's probably already completed)
                return false;
            }

            var sessionState = JsonConvert.DeserializeObject<AgentSessionState>(
                sessionStateJson!,
                JsonConfig.DefaultJsonSerializerSettings);

            return sessionState?.ShouldCancel ?? false;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to check if session should be canceled for groupChatId {GroupChatId}",
                groupChatId.ToString());
            return false;
        }
    }

    public async Task CancelPreviousSessionsAsync(GroupChatId groupChatId)
    {
        var database = _connectionMultiplexer.GetDatabase();
        var objectId = groupChatId.ObjectId;
        var currentSessionId = groupChatId.SessionId;

        try
        {
            // Use Lua script to atomically cancel previous sessions
            var canceledCount = await database.ScriptEvaluateAsync(
                CancelPreviousSessionsScript,
                new RedisKey[]
                {
                    GetAllAgentSessionIdsKey(groupChatId)
                },
                new RedisValue[]
                {
                    currentSessionId,
                    (int) SessionExpirationTimeSpan.TotalSeconds,
                    $"{_cacheConfig.CachePrefix}:agent-session:"
                });

            if ((long) canceledCount > 0)
            {
                _logger.LogInformation(
                    "Canceled {Count} previous agent sessions for objectId {ObjectId} that haven't performed actions",
                    (long) canceledCount,
                    objectId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to cancel previous sessions for objectId {ObjectId}", objectId);
        }
    }

    private string GetAgentSessionKey(GroupChatId groupChatId)
    {
        return $"{_cacheConfig.CachePrefix}:agent-session:{groupChatId.SessionId}";
    }

    private string GetAllAgentSessionIdsKey(GroupChatId groupChatId)
    {
        return $"{_cacheConfig.CachePrefix}:all-agent-session-ids:{groupChatId.ObjectId}";
    }
}