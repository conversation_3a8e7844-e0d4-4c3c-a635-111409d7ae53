using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Configs;

namespace Sleekflow.Integrator.Hubspot.Services;

public interface IHubspotSecurityService
{
    public bool IsHubspotCallbackWebhookValid(string payloadStr, string xHubspotSignature);
}

public class HubspotSecurityService : IHubspotSecurityService, ISingletonService
{
    private readonly IHubspotConfig _hubspotConfig;

    public HubspotSecurityService(IHubspotConfig hubspotConfig)
    {
        _hubspotConfig = hubspotConfig;
    }

    public bool IsHubspotCallbackWebhookValid(string payloadStr, string xHubspotSignature)
    {
        var hubspotCallbackPayloadsStr = Regex.Replace(payloadStr, @"\s+", string.Empty);

        var clientSecret = _hubspotConfig.HubspotClientSecret;
        var sourceString = clientSecret + hubspotCallbackPayloadsStr;

        var sha256Hasher = SHA256.Create();
        var hash = sha256Hasher.ComputeHash(Encoding.Default.GetBytes(sourceString));
        var hexHashStringBuilder = new StringBuilder(hash.Length * 2);
        foreach (var t in hash)
        {
            hexHashStringBuilder.Append(t.ToString("x2"));
        }

        var hexHashString = hexHashStringBuilder.ToString();
        return hexHashString == xHubspotSignature;
    }
}