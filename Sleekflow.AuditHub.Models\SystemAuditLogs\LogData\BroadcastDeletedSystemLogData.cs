using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class BroadcastDeletedSystemLogData
{
    [JsonProperty("broadcast_id")]
    public string BroadcastId { get; set; }

    [JsonProperty("broadcast_name")]
    public string BroadcastName { get; set; }

    [JsonConstructor]
    public BroadcastDeletedSystemLogData(string broadcastId, string broadcastName)
    {
        BroadcastId = broadcastId;
        BroadcastName = broadcastName;
    }
}