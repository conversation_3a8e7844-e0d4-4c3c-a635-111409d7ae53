using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactConversationStatusStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-conversation-status";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [Required]
    [JsonProperty("status__expr")]
    public string StatusExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Conversation;

    [JsonConstructor]
    public UpdateContactConversationStatusStepArgs(
        string contactIdExpr,
        string statusExpr)
    {
        ContactIdExpr = contactIdExpr;
        StatusExpr = statusExpr;
    }
}