﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class GetTypeFieldsOutput
{
    [JsonProperty("updatable_fields")]
    public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

    [JsonProperty("creatable_fields")]
    public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

    [JsonProperty("viewable_fields")]
    public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

    [JsonConstructor]
    public GetTypeFieldsOutput(
        List<GetTypeFieldsOutputFieldDto> updatableFields,
        List<GetTypeFieldsOutputFieldDto> creatableFields,
        List<GetTypeFieldsOutputFieldDto> viewableFields)
    {
        UpdatableFields = updatableFields;
        CreatableFields = creatableFields;
        ViewableFields = viewableFields;
    }
}