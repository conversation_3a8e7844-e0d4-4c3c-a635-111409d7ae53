using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class SendMessageAction : BaseAction
{
    [JsonProperty("response_type")]
    public string ResponseType { get; set; }

    [JsonProperty("instructions")]
    public string Instructions { get; set; }

    [JsonConstructor]
    public SendMessageAction(bool enabled, string responseType, string instructions)
        : base(enabled)
    {
        ResponseType = responseType;
        Instructions = instructions;
    }

    public SendMessageAction(SendMessageActionDto dto)
        : base(dto)
    {
        ResponseType = dto.ResponseType;
        Instructions = dto.Instructions;
    }

    public override BaseActionDto ToDto()
    {
        return new SendMessageActionDto(this);
    }
}