﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Unifies;

public class UnifyRule
{
    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("strategy")]
    public string Strategy { get; set; }

    [JsonProperty("provider_precedences")]
    public List<string> ProviderPrecedences { get; set; }

    [JsonProperty("is_system")]
    public bool IsSystem { get; set; }

    [JsonConstructor]
    public UnifyRule(
        string fieldName,
        string strategy,
        List<string> providerPrecedences,
        bool isSystem)
    {
        FieldName = fieldName;
        Strategy = strategy;
        ProviderPrecedences = providerPrecedences;
        IsSystem = isSystem;
    }
}