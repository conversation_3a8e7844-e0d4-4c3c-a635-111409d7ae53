using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Integrator.Dynamics365.Objects;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SyncObject : ITrigger
{
    private readonly IDynamics365ObjectService _dynamics365ObjectService;
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;
    private readonly IBus _bus;

    public SyncObject(
        IDynamics365ObjectService dynamics365ObjectService,
        IDynamics365AuthenticationService dynamics365AuthenticationService,
        IBus bus)
    {
        _dynamics365ObjectService = dynamics365ObjectService;
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
        _bus = bus;
    }

    public class SyncObjectInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public SyncObjectInput(
            string sleekflowCompanyId,
            string objectId,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ObjectId = objectId;
            EntityTypeName = entityTypeName;
        }
    }

    public class SyncObjectOutput
    {
    }

    public async Task<SyncObjectOutput> F(
        SyncObjectInput syncObjectInput)
    {
        var authentication =
            await _dynamics365AuthenticationService.GetOrDefaultAsync(syncObjectInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var @object = await _dynamics365ObjectService.GetObjectAsync(
            authentication,
            syncObjectInput.ObjectId,
            syncObjectInput.EntityTypeName);
        if (@object == null)
        {
            throw new SfUserFriendlyException("The object does not exist");
        }

        var providerObjectId = _dynamics365ObjectService.ResolveObjectId(@object, syncObjectInput.EntityTypeName);
        if (@object.ContainsKey("id") == false)
        {
            @object["id"] = providerObjectId;
        }

        await _bus.Publish(
            new OnObjectOperationEvent(
                @object,
                OnObjectOperationEvent.OperationCreateOrUpdateObject,
                "d365",
                syncObjectInput.SleekflowCompanyId,
                _dynamics365ObjectService.ResolveObjectId(@object, syncObjectInput.EntityTypeName),
                syncObjectInput.EntityTypeName,
                null),
            context => { context.ConversationId = Guid.Parse(syncObjectInput.SleekflowCompanyId); });

        return new SyncObjectOutput();
    }
}