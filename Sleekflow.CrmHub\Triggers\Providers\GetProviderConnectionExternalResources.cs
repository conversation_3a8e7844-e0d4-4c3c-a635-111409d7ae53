﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderConnectionExternalResources : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderConnectionExternalResources(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderConnectionExternalResourcesInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [JsonConstructor]
        public GetProviderConnectionExternalResourcesInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
        }
    }

    public class GetProviderConnectionExternalResourcesOutput
    {
        [JsonProperty("external_resources")]
        public List<ExternalResource> ExternalResources { get; set; }

        [JsonConstructor]
        public GetProviderConnectionExternalResourcesOutput(
            List<ExternalResource> externalResources)
        {
            ExternalResources = externalResources;
        }
    }

    public async Task<GetProviderConnectionExternalResourcesOutput> F(
        GetProviderConnectionExternalResourcesInput getProviderConnectionsInput)
    {
        var providerService = _providerSelector.GetProviderService(
            getProviderConnectionsInput.ProviderName);

        var output = await providerService.GetProviderConnectionExternalResourcesAsync(
            getProviderConnectionsInput.SleekflowCompanyId,
            getProviderConnectionsInput.ProviderConnectionId);

        return new GetProviderConnectionExternalResourcesOutput(output.ExternalResources);
    }
}