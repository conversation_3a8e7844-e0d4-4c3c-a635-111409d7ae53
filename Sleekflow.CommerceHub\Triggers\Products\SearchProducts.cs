using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.CommerceHub.Searches;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class SearchProducts
    : ITrigger<
        SearchProducts.SearchProductsInput,
        SearchProducts.SearchProductsOutput>
{
    private readonly IProductSearchService _productSearchService;
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;

    public SearchProducts(
        IProductSearchService productSearchService,
        IProductService productService,
        IProductVariantService productVariantService)
    {
        _productSearchService = productSearchService;
        _productService = productService;
        _productVariantService = productVariantService;
    }

    public class SearchProductsInput
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [ValidateArray]
        [MaxLength(16)]
        [JsonProperty("filter_groups")]
        public List<SearchFilterGroup> FilterGroups { get; set; }

        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("continuation_token")]
        public string? ContinuationToken
        {
            get => ContinuationTokenDto == null ? null : JsonConvert.SerializeObject(ContinuationTokenDto);
            set => ContinuationTokenDto =
                value == null ? null : JsonConvert.DeserializeObject<IProductSearchService.ContinuationTokenDto>(value);
        }

        [JsonIgnore]
#pragma warning disable JA1001
        public IProductSearchService.ContinuationTokenDto? ContinuationTokenDto { get; set; }
#pragma warning restore JA1001

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [StringLength(1024, MinimumLength = 1)]
        [JsonProperty("search_text")]
        public string? SearchText { get; set; }

        [JsonConstructor]
        public SearchProductsInput(
            string sleekflowCompanyId,
            string storeId,
            List<SearchFilterGroup> filterGroups,
            string? continuationToken,
            int limit,
            string? searchText)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            FilterGroups = filterGroups;
            ContinuationToken = continuationToken;
            Limit = limit;
            SearchText = searchText;
        }
    }

    public class SearchProductsOutput
    {
        [JsonProperty("products")]
        public List<ProductDto> Products { get; set; }

        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken
        {
            get => NextContinuationTokenDto == null ? null : JsonConvert.SerializeObject(NextContinuationTokenDto);
            set => NextContinuationTokenDto =
                value == null ? null : JsonConvert.DeserializeObject<IProductSearchService.ContinuationTokenDto>(value);
        }

        [JsonIgnore]
        [JsonProperty("next_continuation_token_dto")]
        public IProductSearchService.ContinuationTokenDto? NextContinuationTokenDto { get; set; }

        [JsonProperty("facets")]
        public IDictionary<string, IList<IReadOnlyDictionary<string, object>>> Facets { get; set; }

        [JsonConstructor]
        public SearchProductsOutput(
            List<ProductDto> products,
            long totalCount,
            string? nextContinuationToken,
            IDictionary<string, IList<IReadOnlyDictionary<string, object>>> facets)
        {
            Products = products;
            TotalCount = totalCount;
            NextContinuationToken = nextContinuationToken;
            Facets = facets;
        }

        public SearchProductsOutput(
            List<ProductDto> products,
            long totalCount,
            IProductSearchService.ContinuationTokenDto? nextContinuationTokenDto,
            IDictionary<string, IList<IReadOnlyDictionary<string, object>>> facets)
        {
            Products = products;
            TotalCount = totalCount;
            NextContinuationTokenDto = nextContinuationTokenDto;
            Facets = facets;
        }
    }

    public async Task<SearchProductsOutput> F(SearchProductsInput searchProductsInput)
    {
        var (productIndexDtos, facets, totalCount, nextContinuationToken) =
            await _productSearchService.SearchProductsAsync(
                searchProductsInput.SleekflowCompanyId,
                searchProductsInput.StoreId,
                searchProductsInput.FilterGroups,
                searchProductsInput.ContinuationTokenDto,
                searchProductsInput.Limit,
                searchProductsInput.SearchText);

        var products = await _productService.GetProductsAsync(
            productIndexDtos.Select(p => p.Id).ToList(),
            searchProductsInput.SleekflowCompanyId,
            searchProductsInput.StoreId);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            searchProductsInput.SleekflowCompanyId,
            searchProductsInput.StoreId,
            products.Select(p => p.Id).ToList());

        var productIdToProductVariantsDict = productVariants
            .GroupBy(pv => pv.ProductId)
            .ToDictionary(e => e.Key, e => e.ToList());

        return new SearchProductsOutput(
            products
                .Select(
                    c => new ProductDto(
                        c,
                        productIdToProductVariantsDict.GetValueOrDefault(c.Id, new List<ProductVariant>())))
                .ToList(),
            totalCount,
            nextContinuationToken,
            facets
                .Select(
                    f => new KeyValuePair<string, IList<IReadOnlyDictionary<string, object>>>(
                        f.Key,
                        f.Value.Select(v => (IReadOnlyDictionary<string, object>) v).ToList()))
                .ToDictionary(e => e.Key, e => e.Value));
    }
}