using Alba;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Triggers.Carts;
using Sleekflow.CommerceHub.Triggers.Categories;
using Sleekflow.CommerceHub.Triggers.Orders;
using Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;
using Sleekflow.CommerceHub.Triggers.Payments;
using Sleekflow.CommerceHub.Triggers.Products;
using Sleekflow.CommerceHub.Triggers.Stores;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;

namespace Sleekflow.CommerceHub.Tests;

public class PaymentIntegrationTests
{
    private CreateStore.CreateStoreOutput? _createStoreOutput;
    private CreateCategory.CreateCategoryOutput? _createCategoryOutput;
    private CreateProduct.CreateProductOutput? _createProductOutput;

    [SetUp]
    public async Task Setup()
    {
        var createStoreOutputOutput = await Mocks.CreateTestStoreAsync();
        var createCategoryOutputOutput = await Mocks.CreateTestCategoryAsync(createStoreOutputOutput!.Data);
        var createProductOutputOutput = await Mocks.CreateTestProductAsync(
            createStoreOutputOutput.Data,
            createCategoryOutputOutput!.Data);

        _createStoreOutput = createStoreOutputOutput.Data;
        _createCategoryOutput = createCategoryOutputOutput.Data;
        _createProductOutput = createProductOutputOutput.Data;
    }

    [TearDown]
    public async Task TearDown()
    {
        await Mocks.DeleteTestProductAsync(_createStoreOutput!, _createProductOutput!);
        await Mocks.DeleteTestCategoryAsync(_createStoreOutput!, _createCategoryOutput!);
        await Mocks.DeleteTestStoreAsync(_createStoreOutput!);
    }

    [Test]
    public async Task HealthzTest()
    {
        await Application.Host.Scenario(
            _ =>
            {
                _.Get.Url("/Public/healthz");
                _.ContentShouldBe("HEALTH");
                _.StatusCodeShouldBeOk();
            });
    }

    [Test]
    public async Task OrdersTest()
    {
        // /Carts/GetCart
        var getCartInput =
            new GetCart.GetCartInput(
                Mocks.SleekflowCompanyId,
                "MyUser",
                _createStoreOutput!.Store.Id);

        var getCartScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getCartInput).ToUrl("/Carts/GetCart");
            });
        var getCartOutputOutput =
            await getCartScenarioResult.ReadAsJsonAsync<
                Output<GetCart.GetCartOutput>>();

        // /Carts/UpdateCart
        var updateCartInput =
            new UpdateCart.UpdateCartInput(
                Mocks.SleekflowCompanyId,
                "MyUser",
                _createStoreOutput!.Store.Id,
                new List<CartLineItem>
                {
                    new (
                        _createProductOutput!.Product.ProductVariants[0].Id,
                        _createProductOutput.Product.Id,
                        null,
                        1,
                        null,
                        new Dictionary<string, object?>())
                },
                null);

        var updateCartScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateCartInput).ToUrl("/Carts/UpdateCart");
            });
        var updateCartOutputOutput =
            await getCartScenarioResult.ReadAsJsonAsync<
                Output<UpdateCart.UpdateCartOutput>>();

        // /Orders/CreateDraftOrder
        var createDraftOrderInput =
            new CreateDraftOrder.CreateDraftOrderInput(
                Mocks.SleekflowCompanyId,
                "MyUser",
                new UserProfile("Leo", "Choi", "+85261096623", "<EMAIL>"),
                _createStoreOutput!.Store.Id,
                "HKD",
                "HK",
                "en",
                "MyStaffId",
                null);

        var createDraftOrderScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createDraftOrderInput).ToUrl("/Orders/CreateDraftOrder");
            });
        var createDraftOrderOutputOutput =
            await createDraftOrderScenarioResult.ReadAsJsonAsync<
                Output<CreateDraftOrder.CreateDraftOrderOutput>>();

        Assert.That(createDraftOrderOutputOutput, Is.Not.Null);
        Assert.That(createDraftOrderOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Orders/GetUserOrdersInput
        var getUserOrdersInput =
            new GetUserOrders.GetUserOrdersInput(
                Mocks.SleekflowCompanyId,
                "MyUser");
        var getUserOrdersScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getUserOrdersInput).ToUrl("/Orders/GetUserOrders");
            });
        var getUserOrdersOutputOutput =
            await getUserOrdersScenarioResult.ReadAsJsonAsync<
                Output<GetUserOrders.GetUserOrdersOutput>>();

        Assert.That(getUserOrdersOutputOutput, Is.Not.Null);
        Assert.That(getUserOrdersOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var order = createDraftOrderOutputOutput.Data.Order;

        // /Orders/UpdateOrder
        var updateOrderInput =
            new UpdateOrder.UpdateOrderInput(
                order.Id,
                Mocks.SleekflowCompanyId,
                "MyUser",
                new UserProfile("Leo", "Choi", "+85261096623", "<EMAIL>"),
                _createStoreOutput!.Store.Id,
                new List<OrderLineItemInputDto>
                {
                    new OrderLineItemInputDto(
                        _createProductOutput!.Product.ProductVariants[0].Id,
                        _createProductOutput.Product.Id,
                        3,
                        null)
                },
                null,
                "HK",
                "en",
                "HKD",
                new Dictionary<string, object?>(),
                "MyStaffId",
                null);
        var updateOrderScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateOrderInput).ToUrl("/Orders/UpdateOrder");
            });
        var updateOrderOutputOutput =
            await updateOrderScenarioResult.ReadAsJsonAsync<
                Output<UpdateOrder.UpdateOrderOutput>>();

        Assert.That(updateOrderOutputOutput, Is.Not.Null);
        Assert.That(updateOrderOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        if (BaseTestHost.IsGithubAction)
        {
            return;
        }

        // /Payments/CreateOrderPayment
        var createOrderPaymentInput =
            new CreateOrderPayment.CreateOrderPaymentInput(
                Mocks.SleekflowCompanyId,
                "MyUser",
                order.Id,
                "Stripe",
                "en",
                DateTimeOffset.UtcNow,
                "MyStaffId",
                null,
                "http://localhost");
        var createOrderPaymentScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createOrderPaymentInput).ToUrl("/Payments/CreateOrderPayment");
            });
        var createOrderPaymentOutputOutput =
            await createOrderPaymentScenarioResult.ReadAsJsonAsync<
                Output<CreateOrderPayment.CreateOrderPaymentOutput>>();

        Assert.That(createOrderPaymentOutputOutput, Is.Not.Null);
        Assert.That(createOrderPaymentOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task PaymentProviderConfigsTest()
    {
        // /PaymentProviderConfigs/CreateStripePaymentProviderConfig
        var createStripePaymentProviderConfigInput =
            new CreateStripePaymentProviderConfig.CreateStripePaymentProviderConfigInput(
                Mocks.SleekflowCompanyId,
                "sk_test_51MFxOGCBUT9DU3GexijdHj6Q9neElh6oNKiI0KZqGakcff9Ae3PuwECXmvWZ2mwmqB0WgQ8pe0yso9q6w0wNVNGz00ZnuhHUEm",
                "whsec_ee7477c4e1a0fe5b12743517157c499a3f476f4ab74eadb2e4286ee2ee6616be",
                "whsec_ee7477c4e1a0fe5b12743517157c499a3f476f4ab74eadb2e4286ee2ee6616be",
                0.03m,
                false,
                null,
                false,
                "MyStaffId",
                null);
        var createStripePaymentProviderConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createStripePaymentProviderConfigInput)
                    .ToUrl("/PaymentProviderConfigs/CreateStripePaymentProviderConfig");
            });
        var createStripePaymentProviderConfigOutputOutput =
            await createStripePaymentProviderConfigScenarioResult.ReadAsJsonAsync<
                Output<CreateStripePaymentProviderConfig.CreateStripePaymentProviderConfigOutput>>();

        Assert.That(createStripePaymentProviderConfigOutputOutput, Is.Not.Null);
        Assert.That(createStripePaymentProviderConfigOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var createStripePaymentProviderConfigOutput = createStripePaymentProviderConfigOutputOutput!.Data;

        Assert.That(createStripePaymentProviderConfigOutput, Is.Not.Null);

        // /PaymentProviderConfigs/GetPaymentProviderConfigs
        var getPaymentProviderConfigsInput =
            new GetPaymentProviderConfigs.GetPaymentProviderConfigsInput(
                Mocks.SleekflowCompanyId,
                "MyStaffId",
                null);
        var getPaymentProviderConfigsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getPaymentProviderConfigsInput).ToUrl("/PaymentProviderConfigs/GetPaymentProviderConfigs");
            });
        var getPaymentProviderConfigsOutputOutput =
            await getPaymentProviderConfigsScenarioResult.ReadAsJsonAsync<
                Output<GetPaymentProviderConfigs.GetPaymentProviderConfigsOutput>>();

        Assert.That(getPaymentProviderConfigsOutputOutput, Is.Not.Null);
        Assert.That(getPaymentProviderConfigsOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var paymentProviderConfigs = getPaymentProviderConfigsOutputOutput!.Data.PaymentProviderConfigs;
        var paymentProviderConfig = paymentProviderConfigs.Find(
            ppc =>
                ppc.Id == createStripePaymentProviderConfigOutput.PaymentProviderConfigId);

        Assert.That(paymentProviderConfig, Is.Not.Null);

        // /PaymentProviderConfigs/GetPaymentProviderConfig
        var getPaymentProviderConfigInput =
            new GetPaymentProviderConfig.GetPaymentProviderConfigInput(
                Mocks.SleekflowCompanyId,
                paymentProviderConfig!.Id);
        var getPaymentProviderConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getPaymentProviderConfigInput).ToUrl("/PaymentProviderConfigs/GetPaymentProviderConfig");
            });
        var getPaymentProviderConfigOutputOutput =
            await getPaymentProviderConfigScenarioResult.ReadAsJsonAsync<
                Output<GetPaymentProviderConfig.GetPaymentProviderConfigOutput>>();

        Assert.That(getPaymentProviderConfigOutputOutput, Is.Not.Null);
        Assert.That(getPaymentProviderConfigOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getPaymentProviderConfigOutputOutput!.Data.PaymentProviderConfig, Is.Not.Null);

        // /PaymentProviderConfigs/LinkPaymentProviderConfigInput
        var linkPaymentProviderConfigInput =
            new LinkPaymentProviderConfig.LinkPaymentProviderConfigInput(
                paymentProviderConfig!.Id,
                _createStoreOutput!.Store.Id,
                new List<string>()
                {
                    "usd"
                },
                Mocks.SleekflowCompanyId,
                "MyStaffId",
                null);
        var linkPaymentProviderConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(linkPaymentProviderConfigInput).ToUrl("/PaymentProviderConfigs/LinkPaymentProviderConfig");
            });
        var linkPaymentProviderConfigOutputOutput =
            await linkPaymentProviderConfigScenarioResult.ReadAsJsonAsync<
                Output<LinkPaymentProviderConfig.LinkPaymentProviderConfigOutput>>();

        Assert.That(linkPaymentProviderConfigOutputOutput, Is.Not.Null);
        Assert.That(linkPaymentProviderConfigOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /PaymentProviderConfigs/GetStorePaymentProviderConfigs
        var getStorePaymentProviderConfigsInput =
            new GetStorePaymentProviderConfigs.GetStorePaymentProviderConfigsInput(
                Mocks.SleekflowCompanyId,
                _createStoreOutput!.Store.Id);
        var getStorePaymentProviderConfigsScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _
                    .Post
                    .Json(getStorePaymentProviderConfigsInput)
                    .ToUrl("/PaymentProviderConfigs/GetStorePaymentProviderConfigs");
            });
        var getStorePaymentProviderConfigsOutputOutput =
            await getStorePaymentProviderConfigsScenarioResult.ReadAsJsonAsync<
                Output<GetStorePaymentProviderConfigs.GetStorePaymentProviderConfigsOutput>>();

        Assert.That(getStorePaymentProviderConfigsOutputOutput, Is.Not.Null);
        Assert.That(getStorePaymentProviderConfigsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getStorePaymentProviderConfigsOutputOutput!.Data.PaymentProviderConfigs.Count, Is.EqualTo(1));
        Assert.That(
            getStorePaymentProviderConfigsOutputOutput!.Data.PaymentProviderConfigs[0].Id,
            Is.EqualTo(createStripePaymentProviderConfigOutput.PaymentProviderConfigId));

        // /PaymentProviderConfigs/DeletePaymentProviderConfig
        var deletePaymentProviderConfigInput =
            new DeletePaymentProviderConfig.DeletePaymentProviderConfigInput(
                Mocks.SleekflowCompanyId,
                createStripePaymentProviderConfigOutput.PaymentProviderConfigId,
                "MyStaffId",
                null);
        var deletePaymentProviderConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deletePaymentProviderConfigInput)
                    .ToUrl("/PaymentProviderConfigs/DeletePaymentProviderConfig");
            });
        var deletePaymentProviderConfigOutputOutput =
            await deletePaymentProviderConfigScenarioResult.ReadAsJsonAsync<
                Output<DeletePaymentProviderConfig.DeletePaymentProviderConfigOutput>>();

        Assert.That(deletePaymentProviderConfigOutputOutput, Is.Not.Null);
        Assert.That(deletePaymentProviderConfigOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task StripeConnectPaymentProviderConfigsTest()
    {
        // /PaymentProviderConfigs/CreateStripePaymentProviderConfigInput
        var createStripePaymentProviderConfigInput =
            new CreateStripeConnectPaymentProviderConfig.CreateStripeConnectPaymentProviderConfigInput(
                Mocks.SleekflowCompanyId,
                "HK",
                0.03m,
                false,
                null,
                false,
                "MyStaffId",
                null);
        var createStripePaymentProviderConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _
                    .Post
                    .Json(createStripePaymentProviderConfigInput)
                    .ToUrl("/PaymentProviderConfigs/CreateStripeConnectPaymentProviderConfig");
            });
        var createStripePaymentProviderConfigOutputOutput =
            await createStripePaymentProviderConfigScenarioResult.ReadAsJsonAsync<
                Output<CreateStripeConnectPaymentProviderConfig.CreateStripeConnectPaymentProviderConfigOutput>>();

        Assert.That(createStripePaymentProviderConfigOutputOutput, Is.Not.Null);
        Assert.That(createStripePaymentProviderConfigOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        var createStripePaymentProviderConfigOutput = createStripePaymentProviderConfigOutputOutput!.Data;

        Assert.That(createStripePaymentProviderConfigOutput, Is.Not.Null);
        Assert.That(createStripePaymentProviderConfigOutput.StripeConnectOnboardingLink, Is.Not.Null);
    }
}