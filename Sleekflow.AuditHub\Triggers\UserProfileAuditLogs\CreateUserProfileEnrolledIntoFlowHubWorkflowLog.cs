﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileEnrolledIntoFlowHubWorkflowLog : ITrigger
{
    private readonly IIdService _idService;
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateUserProfileEnrolledIntoFlowHubWorkflowLog(
        IIdService idService,
        IUserProfileAuditLogService userProfileAuditLogService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _idService = idService;
        _userProfileAuditLogService = userProfileAuditLogService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateUserProfileEnrolledIntoFlowHubWorkflowLogInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("workflow_id")]
        public string WorkflowId { get; set; }

        [Required]
        [JsonProperty("workflow_name")]
        public string WorkflowName { get; set; }

        [Required]
        [JsonProperty("workflow_versioned_id")]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty("state_id")]
        public string StateId { get; set; }

        [JsonConstructor]
        public CreateUserProfileEnrolledIntoFlowHubWorkflowLogInput(
            string? sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string workflowId,
            string workflowName,
            string workflowVersionedId,
            string stateId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            WorkflowId = workflowId;
            WorkflowName = workflowName;
            WorkflowVersionedId = workflowVersionedId;
            StateId = stateId;
        }
    }

    public class CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutput> F(
        CreateUserProfileEnrolledIntoFlowHubWorkflowLogInput input)
    {
        var dataStr = JsonConvert.SerializeObject(
            new UserProfileEnrolledIntoFlowHubWorkflowLogData(
                input.WorkflowId,
                input.WorkflowVersionedId,
                input.WorkflowName,
                input.StateId));

        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var id = _idService.GetId("UserProfileAuditLog");

        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
            new UserProfileAuditLog(
                id,
                (distributedInvocationContext?.SleekflowCompanyId
                 ?? input.SleekflowCompanyId)
                ?? throw new InvalidOperationException(),
                distributedInvocationContext?.SleekflowStaffId
                ?? input.SleekflowStaffId,
                input.SleekflowUserProfileId,
                UserProfileAuditLogTypes.UserProfileEnrolledIntoFlowHubWorkflow,
                $"Enrolled into flow {input.WorkflowName} (Flow ID: {input.WorkflowId}, State ID: {input.StateId})",
                data,
                DateTimeOffset.UtcNow,
                null));

        return new CreateUserProfileEnrolledIntoFlowHubWorkflowLogOutput(id);
    }
}