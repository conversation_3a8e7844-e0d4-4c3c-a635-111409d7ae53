using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;

public class ResponseCrafterAgentGeminiChatHistoryReducer(
    List<string> authorNames,
    ILeadNurturingCollaborationChatCacheService leadNurturingCollaborationChatCacheService,
    string groupChatIdStr,
    ILeadNurturingAgentDefinitions leadNurturingAgentDefinitions)
    : AuthorNamesGeminiChatHistoryReducer(authorNames)
{
    public override async Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        var chatMessageContents =
            chatHistory.ToList();

        var allConfirmedKnowledges =
            await leadNurturingCollaborationChatCacheService.GetAllConfirmedKnowledgesAsync(groupChatIdStr);

        var strategyAgentChatMessageContents = chatMessageContents
            .Where(c => c.AuthorName == leadNurturingAgentDefinitions.StrategyAgentName)
            .ToList();

        try
        {
            // Insert knowledge retrieval agent's knowledge into the chat history
            for (var i = 0; i < strategyAgentChatMessageContents.Count; i++)
            {
                var strategyAgentChatMessageContent = strategyAgentChatMessageContents[i];
                var strategyAgentChatMessageContentIndex = chatMessageContents.IndexOf(strategyAgentChatMessageContent);

                var strategyAgentDictionary = JsonUtils.ParseJson(strategyAgentChatMessageContent.Content!);

                chatMessageContents.Insert(
                    strategyAgentChatMessageContentIndex,
                    new ChatMessageContent
                    {
                        Role = AuthorRole.Assistant,
                        AuthorName = leadNurturingAgentDefinitions.KnowledgeRetrievalAgentName,
                        Content = JsonConvert.SerializeObject(
                            new Dictionary<string, string>()
                            {
                                {
                                    "agent_name", leadNurturingAgentDefinitions.KnowledgeRetrievalAgentName
                                },
                                {
                                    "knowledge",
                                    allConfirmedKnowledges[(string) strategyAgentDictionary!["need_knowledge"]]
                                },
                            })
                    });
            }
        }
        catch
        {
            // ignored
        }

        return await base.ReduceAsync(chatMessageContents, cancellationToken);
    }
}