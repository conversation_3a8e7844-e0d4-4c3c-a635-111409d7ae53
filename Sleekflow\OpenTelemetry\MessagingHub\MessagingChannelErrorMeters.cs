using System.Diagnostics.Metrics;
using Microsoft.Extensions.Logging;
using Sleekflow.OpenTelemetry.MessagingHub.MeterNames;
using Sleekflow.OpenTelemetry.Meters;

namespace Sleekflow.OpenTelemetry.MessagingHub;

public interface IMessagingChannelErrorMeters : IBasicMeters
{
}

public static class MessagingChannelErrorMetersOptions
{
    public const string SendMessage = "send_message";
}

public class MessagingChannelErrorMeters : BaseMeters, IMessagingChannelErrorMeters
{
    private const string MeterName = "messaging_channel_error.trigger_type";
    private readonly Dictionary<string, Counter<int>>? _counters;

    private readonly List<string> _optionsParams = new ()
    {
        MessagingChannelErrorMetersOptions.SendMessage,
    };

    public MessagingChannelErrorMeters(
        IMeterFactory meterFactory,
        ILogger<MessagingChannelErrorMeters> logger)
        : base(meterFactory, logger)
    {
        if (!IsMeterEnabled)
        {
            return;
        }

        _counters = _optionsParams.SelectMany(
                o => _channelTypes.Select(
                    c => (Key: GetComposeKey(c.Key, o), Value: GetComposeKey(c.Value, o))))
            .ToDictionary(
                k => k.Key,
                k => CreateCounter<int>($"{MeterName}.{k.Value}"));
    }

    protected override Counter<int> GetCounter<T>(T name, string? option = null)
    {
        if (name is string triggerType &&
            _counters!.TryGetValue(GetComposeKey(triggerType, option), out var counter))
        {
            return counter;
        }

        throw new NotImplementedException();
    }

    private readonly Dictionary<string, string> _channelTypes = new ()
    {
        {
            MessagingHubChannelMeterNames.WhatsappCloudApi, "whatsapp_cloud_api"
        }
    };
}