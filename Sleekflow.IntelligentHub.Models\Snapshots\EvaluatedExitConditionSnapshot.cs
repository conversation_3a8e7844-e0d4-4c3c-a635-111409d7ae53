using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Snapshots;

public class EvaluatedExitConditionSnapshot : IntelligentHubUsageSnapshot
{
    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("exit_condition_result")]
    public ExitConditionResult? ExitConditionResult { get; set; }

    [JsonConstructor]
    public EvaluatedExitConditionSnapshot(List<SfChatEntry> conversationContext, ExitConditionResult? exitConditionResult)
    {
        ConversationContext = conversationContext;
        ExitConditionResult = exitConditionResult;
    }
}