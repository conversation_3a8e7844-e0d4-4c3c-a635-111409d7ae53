using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Workers;

public class ProcessCustomCatalogCsvInput
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    [System.ComponentModel.DataAnnotations.Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("custom_catalog_file_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string CustomCatalogFileId { get; set; }

    [JsonConstructor]
    public ProcessCustomCatalogCsvInput(
        string sleekflowCompanyId,
        string customCatalogFileId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        CustomCatalogFileId = customCatalogFileId;
    }
}