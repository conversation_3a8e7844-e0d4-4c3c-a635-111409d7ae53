using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Configs;

public interface IStateAllStepsRequestRateCountConfig
{
    int StateAllStepsRequestWindowSeconds { get; }

    int StateAllStepsRequestWarningMaxWithinWindow { get; }

    int StateAllStepsRequestErrorMaxWithinWindow { get; }
}

public class StateAllStepsRequestRateCountConfig : IStateAllStepsRequestRateCountConfig, IConfig
{
    public int StateAllStepsRequestWindowSeconds { get; }

    public int StateAllStepsRequestWarningMaxWithinWindow { get; }

    public int StateAllStepsRequestErrorMaxWithinWindow { get; }

    public StateAllStepsRequestRateCountConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        StateAllStepsRequestWindowSeconds =
            int.TryParse(
                Environment.GetEnvironmentVariable(
                    "STATE_ALL_STEPS_REQUEST_WINDOW_SECONDS",
                    target),
                out var stateAllStepsRequestWindowSeconds)
                ? stateAllStepsRequestWindowSeconds
                : throw new SfMissingEnvironmentVariableException(
                    "STATE_ALL_STEPS_REQUEST_WINDOW_SECONDS");

        StateAllStepsRequestWarningMaxWithinWindow =
            int.TryParse(
                Environment.GetEnvironmentVariable(
                    "STATE_ALL_STEPS_REQUEST_WARNING_MAX_WITHIN_WINDOW",
                    target),
                out var stateAllStepsRequestWarningMaxWithinWindow)
                ? stateAllStepsRequestWarningMaxWithinWindow
                : throw new SfMissingEnvironmentVariableException(
                    "STATE_ALL_STEPS_REQUEST_WARNING_MAX_WITHIN_WINDOW");

        StateAllStepsRequestErrorMaxWithinWindow =
            int.TryParse(
                Environment.GetEnvironmentVariable(
                    "STATE_ALL_STEPS_REQUEST_ERROR_MAX_WITHIN_WINDOW",
                    target),
                out var stateAllStepsRequestErrorMaxWithinWindow)
                ? stateAllStepsRequestErrorMaxWithinWindow
                : throw new SfMissingEnvironmentVariableException(
                    "STATE_ALL_STEPS_REQUEST_ERROR_MAX_WITHIN_WINDOW");
    }
}