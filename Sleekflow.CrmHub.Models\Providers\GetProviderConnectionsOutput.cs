using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Providers;

public class ProviderConnectionDto : IHasSleekflowCompanyId
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [Json<PERSON>roperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("organization_id")]
    public string? OrganizationId { get; set; }

    [JsonProperty("name")]
    public string? Name { get; set; }

    [JsonProperty("environment")]
    public string? Environment { get; set; }

    [JsonProperty("is_active")]
    public bool IsActive { get; set; }

    [JsonProperty("is_api_request_limit_exceeded")]
    public bool? IsApiRequestLimitExceeded { get; set; }

    [JsonConstructor]
    public ProviderConnectionDto(
        string id,
        string sleekflowCompanyId,
        string? organizationId,
        string? name,
        string? environment,
        bool isActive,
        bool? isApiRequestLimitExceeded)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        OrganizationId = organizationId;
        Name = name;
        Environment = environment;
        IsActive = isActive;
        IsApiRequestLimitExceeded = isApiRequestLimitExceeded;
    }
}

public class GetProviderConnectionsOutput
{
    [JsonProperty("connections")]
    public List<ProviderConnectionDto> Connections { get; set; }

    [JsonConstructor]
    public GetProviderConnectionsOutput(
        List<ProviderConnectionDto> connections)
    {
        Connections = connections;
    }
}