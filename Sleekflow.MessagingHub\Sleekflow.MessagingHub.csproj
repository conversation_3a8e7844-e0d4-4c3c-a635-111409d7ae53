<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
        <PackageReference Include="Slack.Webhooks" Version="1.1.5" />
        <PackageReference Include="Stripe.net" Version="41.27.0" />
        <PackageReference Include="WABA360Dialog.NET" Version="1.0.0-beta.20" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.MessagingHub.Models\Sleekflow.MessagingHub.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Reference Include="GraphApi.Client">
            <HintPath>Binaries\GraphApi.Client\GraphApi.Client.dll</HintPath>
        </Reference>
        <Reference Include="GraphApi.Client.Const">
            <HintPath>Binaries\GraphApi.Client\GraphApi.Client.Const.dll</HintPath>
        </Reference>
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="Sleekflow.MessagingHub.Tests" />
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>
