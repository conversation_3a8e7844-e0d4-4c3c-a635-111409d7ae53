using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnScheduledDateTimeArrivedEventBody : EventBody
{
    [JsonConstructor]
    public OnScheduledDateTimeArrivedEventBody(
        DateTimeOffset createdAt,
        string triggerDate,
        string triggerTime,
        bool repeated,
        RecurringSettings recurringSettings,
        string? contactId,
        Dictionary<string, object?>? contact,
        string? conversationId,
        bool isNewContact)
        : base(createdAt)
    {
        TriggerDate = triggerDate;
        TriggerTime = triggerTime;
        Repeated = repeated;
        RecurringSettings = recurringSettings;
        ContactId = contactId;
        Contact = contact;
        ConversationId = conversationId;
        IsNewContact = isNewContact;
    }

    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnScheduledDateTimeArrived; }
    }

    [Required]
    [JsonProperty("trigger_date")]
    public string TriggerDate { get; set; }

    [Required]
    [JsonProperty("trigger_time")]
    public string TriggerTime { get; set; }

    [Required]
    [JsonProperty("repeated")]
    public bool Repeated { get; set; }

    [JsonProperty("recurring_settings")]
    public RecurringSettings RecurringSettings { get; set; }

    [Required]
    [JsonProperty("is_new_contact")]
    public bool IsNewContact { get; set; }

    [JsonProperty("conversation_id")]
    public string? ConversationId { get; set; }

    [JsonProperty("contact_id")]
    public string? ContactId { get; set; }

    [JsonProperty("contact")]
    public Dictionary<string, object?>? Contact { get; set; }
}

public class RecurringSettings
{
    [JsonProperty("duration")]
    private int Duration { get; set; }

    [JsonProperty("unit")]
    private string Unit { get; set; }

    [JsonConstructor]
    public RecurringSettings(int duration, string unit)
    {
        Duration = duration;
        Unit = unit;
    }
}