using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetContactsByBatchInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("last_contact_created_at")] // key set pagination purpose, optional for first page, but required for subsequent pages
    [Validations.ValidateObject]
    public DateTimeOffset? LastContactCreatedAt { get; set; }

    [JsonProperty("last_contact_id")] // key set pagination purpose, optional for first page, but required for subsequent pages
    public string? LastContactId { get; set; }

    [JsonProperty("batch_size")] // travis backend default is 100, but we can set it to lower for performance consideration
    [Validations.ValidateObject]
    public int? BatchSize { get; set; }

    [JsonConstructor]
    public GetContactsByBatchInput(
        string sleekflowCompanyId,
        DateTimeOffset? lastContactCreatedAt,
        string? lastContactId,
        int? batchSize)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        LastContactCreatedAt = lastContactCreatedAt;
        LastContactId = lastContactId;
        BatchSize = batchSize;
    }
}

[SwaggerInclude]
public class GetContactsByBatchOutput
{
    [Required]
    [JsonProperty("contacts")]
    public Dictionary<string, ContactDetail> Contacts { get; set; }

    [JsonProperty("next_batch")]
    public NextBatch? NextBatch { get; set; }

    [JsonConstructor]
    public GetContactsByBatchOutput(
        Dictionary<string, ContactDetail> contacts,
        NextBatch? nextBatch)
    {
        Contacts = contacts;
        NextBatch = nextBatch;
    }
}

[SwaggerInclude]
public class ContactDetail
{
    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("contact_owner")]
    public Dictionary<string, object?>? ContactOwner { get; set; }

    [JsonProperty("lists")]
    public ContactList[] Lists { get; set; }

    [JsonProperty("conversation")]
    public ContactConversation? Conversation { get; set; }

    [JsonConstructor]
    public ContactDetail(
        Dictionary<string, object?> contact,
        Dictionary<string, object?>? contactOwner,
        ContactList[] lists,
        ContactConversation? conversation)
    {
        Contact = contact;
        ContactOwner = contactOwner;
        Lists = lists;
        Conversation = conversation;
    }
}

[SwaggerInclude]
public class NextBatch
{
    [JsonProperty("last_contact_created_at")]
    [Validations.ValidateObject]
    public DateTimeOffset? LastContactCreatedAt { get; set; }

    [JsonProperty("last_contact_id")]
    public string? LastContactId { get; set; }

    [JsonConstructor]
    public NextBatch(
        DateTimeOffset? lastContactCreatedAt,
        string? lastContactId)
    {
        LastContactCreatedAt = lastContactCreatedAt;
        LastContactId = lastContactId;
    }
}