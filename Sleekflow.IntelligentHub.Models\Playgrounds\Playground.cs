﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Models.Prompts;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Playgrounds;

[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.Playground)]
[Resolver(typeof(IIntelligentHubDbResolver))]
public class Playground : Entity, IHasSleekflowCompanyId, IHasETag
{
    public const string PropertyNamePlaygroundRecommendedReplies = "recommended_replies";
    public const string PropertyNameCollaborationMode = "collaboration_mode";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sleekflow_user_id")]
    public string SleekflowUserId { get; set; }

    [JsonProperty(PropertyNamePlaygroundRecommendedReplies)]
    public List<PlaygroundRecommendedReply> RecommendedReplies { get; set; }

    [JsonProperty("agent_config")]
    public CompanyAgentConfigDto AgentConfig { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public Playground(
        string id,
        string sleekflowCompanyId,
        string sleekflowUserId,
        List<PlaygroundRecommendedReply> recommendedReplies,
        CompanyAgentConfigDto agentConfig,
        string? eTag)
        : base(id, SysTypeNames.Playground, 3600 * 24 * 31)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowUserId = sleekflowUserId;
        RecommendedReplies = recommendedReplies;
        AgentConfig = agentConfig;
        ETag = eTag;
    }
}