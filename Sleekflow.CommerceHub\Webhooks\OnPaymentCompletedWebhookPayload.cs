using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Webhooks;

public class OnPaymentCompletedWebhookPayload : IHasSleekflowUserProfileId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty("payment_record")]
    public PaymentRecord PaymentRecord { get; set; }

    [JsonProperty("payment_complete_time")]
    public DateTimeOffset PaymentCompleteTime { get; set; }

    [JsonConstructor]
    public OnPaymentCompletedWebhookPayload(
        PaymentRecord paymentRecord,
        DateTimeOffset paymentCompleteTime,
        string sleekflowCompanyId,
        string sleekflowUserProfileId)
    {
        PaymentRecord = paymentRecord;
        PaymentCompleteTime = paymentCompleteTime;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowUserProfileId = sleekflowUserProfileId;
    }
}