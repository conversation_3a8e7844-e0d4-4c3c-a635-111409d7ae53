using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;

public class CompanyAgentConfigActions
{
    [JsonProperty("send_message")]
    public SendMessageAction? SendMessage { get; set; }

    [JsonProperty("calculate_lead_score")]
    public CalculateLeadScoreAction? CalculateLeadScore { get; set; }

    [JsonProperty("exit_conversation")]
    public ExitConversationAction? ExitConversation { get; set; }

    [JsonProperty("add_label")]
    public AddLabelAction? AddLabel { get; set; }

    [JsonConstructor]
    public CompanyAgentConfigActions(
        SendMessageAction? sendMessage,
        CalculateLeadScoreAction? calculateLeadScore,
        ExitConversationAction? exitConversation,
        AddLabelAction? addLabel)
    {
        SendMessage = sendMessage;
        CalculateLeadScore = calculateLeadScore;
        ExitConversation = exitConversation;
        AddLabel = addLabel;
    }

    public CompanyAgentConfigActions(CompanyAgentConfigActionsDto? dto)
    {
        if (dto == null) return;
        SendMessage = dto.SendMessage != null ? new SendMessageAction(dto.SendMessage) : null;
        CalculateLeadScore =
            dto.CalculateLeadScore != null ? new CalculateLeadScoreAction(dto.CalculateLeadScore) : null;
        ExitConversation = dto.ExitConversation != null ? new ExitConversationAction(dto.ExitConversation) : null;
        AddLabel = dto.AddLabel != null ? new AddLabelAction(dto.AddLabel) : null;
    }

    public CompanyAgentConfigActionsDto ToDto()
    {
        return new CompanyAgentConfigActionsDto(
            SendMessage != null ? new SendMessageActionDto(SendMessage) : null,
            CalculateLeadScore != null ? new CalculateLeadScoreActionDto(CalculateLeadScore) : null,
            ExitConversation != null ? new ExitConversationActionDto(ExitConversation) : null,
            AddLabel != null ? new AddLabelActionDto(AddLabel) : null);
    }
}