﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetObjectsV3 : ITrigger
{
    private readonly IEntityRepository _entityRepository;

    public GetObjectsV3(
        IEntityRepository entityRepository)
    {
        _entityRepository = entityRepository;
    }

    public class GetObjectsV3InputFilterQueryFilterGroup
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<EntityQueryBuilder.Filter> Filters { get; set; }

        [JsonConstructor]
        public GetObjectsV3InputFilterQueryFilterGroup(
            List<EntityQueryBuilder.Filter> filters)
        {
            Filters = filters;
        }
    }

    public class GetObjectsV3InputFilterQuery
    {
        [Required]
        [JsonProperty("entity_type_name")]
        [StringLength(128)]
        public string EntityTypeName { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetObjectsV3InputFilterQueryFilterGroup> FilterGroups { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("select")]
        public EntityQueryBuilder.Select Select { get; set; }

        [JsonConstructor]
        public GetObjectsV3InputFilterQuery(
            string entityTypeName,
            List<GetObjectsV3InputFilterQueryFilterGroup> filterGroups,
            EntityQueryBuilder.Select select)
        {
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            Select = select;
        }
    }

    public class GetObjectsV3InputFilter : IValidatableObject
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                "sleekflow_company_id",
                "sys_type_name",
                "sys_entity_type_name"
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^(=|>|<|>=|<=|!=|contains|in|startsWith|query)$")]
        [JsonProperty("operator")]
        public string Operator { get; set; }

        [JsonProperty("value")]
        public object? Value { get; set; }

        [JsonConstructor]
        public GetObjectsV3InputFilter(
            string fieldName,
            string @operator,
            object? value)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
        }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (Operator == "query")
            {
                if (Value == null)
                {
                    return new List<ValidationResult>()
                    {
                        new (
                            "Value is required when operator is query.",
                            new[]
                            {
                                nameof(Value)
                            })
                    };
                }

                if (Value is JObject jObject && jObject.ToObject<GetObjectsV3InputFilterQuery>() == null)
                {
                    return new List<ValidationResult>()
                    {
                        new (
                            "Value is not a valid query.",
                            new[]
                            {
                                nameof(Value)
                            })
                    };
                }
            }

            return new List<ValidationResult>();
        }
    }

    public class GetObjectsV3InputFilterGroup : IValidatableObject
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<GetObjectsV3InputFilter> Filters { get; set; }

        [JsonConstructor]
        public GetObjectsV3InputFilterGroup(
            List<GetObjectsV3InputFilter> filters)
        {
            Filters = filters;
        }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (Filters.Any(f => f.Operator == "query") && Filters.Count > 1)
            {
                return new List<ValidationResult>
                {
                    new (
                        "The query Filter can only be used alone.",
                        new[]
                        {
                            nameof(Filters)
                        })
                };
            }

            return new List<ValidationResult>();
        }
    }

    public class GetObjectsV3InputExpand
    {
        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("from_field_name")]
        public string FromFieldName { get; set; }

        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("to_entity_type_name")]
        public string ToEntityTypeName { get; set; }

        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("to_field_name")]
        public string ToFieldName { get; set; }

        [Required]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [StringLength(255, MinimumLength = 2)]
        [JsonProperty("as_field_name")]
        public string AsFieldName { get; set; }

        [JsonConstructor]
        public GetObjectsV3InputExpand(
            string fromFieldName,
            string toEntityTypeName,
            string toFieldName,
            string asFieldName)
        {
            FromFieldName = fromFieldName;
            ToEntityTypeName = toEntityTypeName;
            ToFieldName = toFieldName;
            AsFieldName = asFieldName;
        }
    }

    public class GetObjectsV3Input : IValidatableObject
    {
        [StringLength(16384, MinimumLength = 1)]
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        [StringLength(1024)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("entity_type_name")]
        [StringLength(128)]
        public string EntityTypeName { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetObjectsV3InputFilterGroup> FilterGroups { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("sorts")]
        public List<EntityQueryBuilder.Sort> Sorts { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("expands")]
        public List<GetObjectsV3InputExpand> Expands { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            var crmHubDbEntityService = validationContext.GetRequiredService<ICrmHubDbEntityService>();
            if (crmHubDbEntityService.IsNotSupportedEntityTypeName(EntityTypeName))
            {
                results.Add(
                    new ValidationResult(
                        "The EntityTypeName is not supported.",
                        new List<string>
                        {
                            nameof(EntityTypeName)
                        }));
            }

            return results;
        }

        [JsonConstructor]
        public GetObjectsV3Input(
            string? continuationToken,
            string sleekflowCompanyId,
            string entityTypeName,
            int limit,
            List<GetObjectsV3InputFilterGroup> filterGroups,
            List<EntityQueryBuilder.Sort> sorts,
            List<GetObjectsV3InputExpand> expands)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            Limit = limit;
            FilterGroups = filterGroups;
            Sorts = sorts;
            Expands = expands;
        }
    }

    public class GetObjectsV3Output
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("records")]
        public List<CrmHubEntity> Records { get; set; }

        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetObjectsV3Output(
            string? continuationToken,
            List<CrmHubEntity> records,
            long count)
        {
            ContinuationToken = continuationToken;
            Records = records;
            Count = count;
        }
    }

    public async Task<GetObjectsV3Output> F(
        GetObjectsV3Input getObjectsV3Input)
    {
        var queryFilterGroups = new List<EntityQueryBuilder.FilterGroup>();
        foreach (var filter in getObjectsV3Input.FilterGroups
                     .SelectMany(fg => fg.Filters)
                     .Where(f => f.Operator == "query"))
        {
            var filterGroup = await GetFilterGroupForQueryFilterAsync(
                getObjectsV3Input.SleekflowCompanyId,
                ((JObject) filter.Value!).ToObject<GetObjectsV3InputFilterQuery>()!,
                filter.FieldName);

            queryFilterGroups.Add(filterGroup);
        }

        var filterGroups = new List<EntityQueryBuilder.FilterGroup>()
            .Concat(
                getObjectsV3Input
                    .FilterGroups
                    .Select(
                        fg => new EntityQueryBuilder.FilterGroup(
                            fg
                                .Filters
                                .Where(f => f.Operator != "query")
                                .Select(f => new EntityQueryBuilder.Filter(f.FieldName, f.Operator, f.Value))
                                .Cast<EntityQueryBuilder.IFilter>()
                                .ToList())))
            .Concat(queryFilterGroups)
            .ToList();

        var queryDefinition =
            EntityQueryBuilder.BuildQueryDef(
                new List<EntityQueryBuilder.ISelect>(),
                getObjectsV3Input.EntityTypeName,
                filterGroups,
                getObjectsV3Input.Sorts,
                new List<EntityQueryBuilder.GroupBy>(),
                getObjectsV3Input.SleekflowCompanyId);

        var (rawRecords, nextContinuationToken) =
            await _entityRepository.GetContinuationTokenizedObjectsAsync(
                queryDefinition,
                getObjectsV3Input.ContinuationToken,
                getObjectsV3Input.Limit);

        var records = rawRecords
            .Select(EntityService.Sanitize)
            .ToList();

        foreach (var expand in getObjectsV3Input.Expands)
        {
            var fromFieldName = expand.FromFieldName;
            var toEntityName = expand.ToEntityTypeName;
            var toFieldName = expand.ToFieldName;
            var asFieldName = expand.AsFieldName;

            var fromFieldValues = records
                .Select(r => ((JValue?) r.GetValueOrDefault(fromFieldName))?.Value<string>())
                .Where(r => r != null)
                .ToList();

            var expandFilters = fromFieldValues
                .Select(fv => new EntityQueryBuilder.Filter(toFieldName, "=", fv))
                .ToList();
            if (expandFilters.Count == 0)
            {
                foreach (var record in records)
                {
                    record[$"expanded:{asFieldName}"] = new List<CrmHubEntity>();
                }

                continue;
            }

            var expandQd =
                EntityQueryBuilder.BuildQueryDef(
                    new List<EntityQueryBuilder.ISelect>(),
                    toEntityName,
                    new List<EntityQueryBuilder.FilterGroup>
                    {
                        new (expandFilters.Cast<EntityQueryBuilder.IFilter>().ToList())
                    },
                    new List<EntityQueryBuilder.Sort>(),
                    new List<EntityQueryBuilder.GroupBy>(),
                    getObjectsV3Input.SleekflowCompanyId);

            var expandObjects =
                await _entityRepository.GetObjectsAsync(expandQd, Math.Min(fromFieldValues.Count * 5, 1000));
            var expandObjectDict = expandObjects
                .Select(EntityService.Sanitize)
                .Where(e => e.ContainsKey(toFieldName))
                .GroupBy(e => e[toFieldName]!)
                .ToDictionary(e => e.Key, e => e.ToList());

            foreach (var record in records)
            {
                var fromFieldValue = record.GetValueOrDefault(fromFieldName);
                if (fromFieldValue != null && expandObjectDict.ContainsKey(fromFieldValue))
                {
                    record[$"expanded:{asFieldName}"] = expandObjectDict[fromFieldValue];
                }
                else
                {
                    record[$"expanded:{asFieldName}"] = new List<CrmHubEntity>();
                }
            }
        }

        return new GetObjectsV3Output(nextContinuationToken, records, records.Count);
    }

    private async Task<EntityQueryBuilder.FilterGroup> GetFilterGroupForQueryFilterAsync(
        string sleekflowCompanyId,
        GetObjectsV3InputFilterQuery queryFilter,
        string fieldName)
    {
        var queryDefinition =
            EntityQueryBuilder.BuildQueryDef(
                new List<EntityQueryBuilder.ISelect>
                {
                    queryFilter.Select
                },
                queryFilter.EntityTypeName,
                queryFilter
                    .FilterGroups
                    .Select(
                        fg => new EntityQueryBuilder.FilterGroup(
                            fg
                                .Filters
                                .Cast<EntityQueryBuilder.IFilter>()
                                .ToList()))
                    .ToList(),
                new List<EntityQueryBuilder.Sort>(),
                new List<EntityQueryBuilder.GroupBy>(),
                sleekflowCompanyId);

        var objects = await _entityRepository.GetObjectsAsync(queryDefinition, 10000);
        var sanitizedObjects = objects
            .Select(EntityService.Sanitize)
            .ToList();

        var values = sanitizedObjects
            .Select(o => ((JValue) o[queryFilter.Select.FieldName]!).Value<string?>())
            .Where(v => v != null)
            .ToList();

        return new EntityQueryBuilder.FilterGroup(
            new List<EntityQueryBuilder.IFilter>()
            {
                new EntityQueryBuilder.Filter(
                    fieldName,
                    "in",
                    values)
            });
    }
}