using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workers;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class SubmitStepOrchestrator
{
    private readonly ILogger<SubmitStepOrchestrator> _logger;

    public SubmitStepOrchestrator(
        ILogger<SubmitStepOrchestrator> logger)
    {
        _logger = logger;
    }

    public class SubmitStepOrchestratorInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("step_id")]
        [Required]
        public string StepId { get; set; }

        [JsonProperty("stack_entries")]
        [Required]
        public Stack<StackEntry> StackEntries { get; set; }

        [JsonConstructor]
        public SubmitStepOrchestratorInput(
            string stateId,
            string stepId,
            Stack<StackEntry> stackEntries)
        {
            StateId = stateId;
            StepId = stepId;
            StackEntries = stackEntries;
        }
    }

    [Function("SubmitStep_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var submitStepInput = context.GetInput<SubmitStepInput>();

        await context.CallActivityAsync(
            "SubmitStep",
            new SubmitStepInput(
                submitStepInput.StateId,
                submitStepInput.StepId,
                submitStepInput.StackEntries,
                context.InstanceId));

        var complete = context.WaitForExternalEvent<NotifyStatusInput>("Complete");
        var failed = context.WaitForExternalEvent<NotifyStatusInput>("Failed");

        var status = await Task.WhenAny(complete, failed);

        _logger.LogInformation(
            "[{OrchestratorName}] State {StateId} step {StepId} has been notified with event Complete = {CompleteStatus}, Failed = {FailedStatus}",
            nameof(SubmitStepOrchestrator),
            submitStepInput.StateId,
            submitStepInput.StepId,
            complete.IsCompleted,
            failed.IsCompleted);

        if (status == complete)
        {
            _logger.LogInformation("Completed the step");
        }
        else if (status == failed)
        {
            var notifyStatusInput = await failed;

            throw new SfFlowHubUserFriendlyException(
                notifyStatusInput.Error == null ? string.Empty : notifyStatusInput.Error.ErrorCode,
                notifyStatusInput.Error == null ? string.Empty : notifyStatusInput.Error.ErrorMessage);
        }
    }
}