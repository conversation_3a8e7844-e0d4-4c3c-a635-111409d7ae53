﻿using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnScheduleWorkflowDeleteEventConsumerDefinition
    : ConsumerDefinition<OnScheduleWorkflowDeleteEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnScheduleWorkflowDeleteEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
            serviceBusReceiveEndpointConfiguration.UseMessageRetry(r => r.Immediate(5));
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnScheduleWorkflowDeleteEventConsumer : IConsumer<OnScheduleWorkflowDeleteEvent>
{
    private readonly IWorkflowService _workflowService;

    public OnScheduleWorkflowDeleteEventConsumer(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public async Task Consume(ConsumeContext<OnScheduleWorkflowDeleteEvent> context)
    {
        var @event = context.Message;

        await _workflowService.DeleteWorkflowAsync(
            @event.WorkflowId,
            @event.SleekflowCompanyId,
            @event.SleekflowStaff);
    }
}