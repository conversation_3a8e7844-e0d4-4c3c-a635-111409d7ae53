using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IConfirmationPlugin
{
    [KernelFunction("check_confirmation")]
    [Description(
        "Checks confirmation status using data from data pane and stores confirmation results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with confirmation status and reasoning.")]
    Task<string> CheckConfirmationWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where proposed action is stored in the data pane.")]
        string proposedActionKey);
}

public class ConfirmationPlugin : BaseLeadNurturingPlugin, IConfirmationPlugin
{
    public ConfirmationPlugin(
        ILogger<ConfirmationPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel)
    {
    }

    private async Task<string> CheckConfirmationAsync(
        Kernel kernel,
        string conversationContext,
        string proposedAction)
    {
        var confirmationAgent = _agentDefinitions.GetConfirmationAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true));

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, proposedAction);

        return await ExecuteAgentWithTelemetryAsync(confirmationAgent, agentThread, "ConfirmationPlugin");
    }

    [KernelFunction("check_confirmation")]
    [Description(
        "Checks confirmation status using data from data pane and stores confirmation results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with confirmation status and reasoning.")]
    public async Task<string> CheckConfirmationWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where proposed action is stored in the data pane.")]
        string proposedActionKey)
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.ConfirmationAgentResponse>(
            sessionKey,
            "Confirmation",
            dataRetrievalFunc: async () => await RetrieveMultipleDataAsync(
                (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext"),
                (proposedActionKey, AgentOutputKeys.PlanningAssignmentComplete, "proposedAction")
            ),
            agentExecutionFunc: async (data) => await CheckConfirmationAsync(
                _kernel.Clone(),
                data["conversationContext"],
                data["proposedAction"]),
            storageKey: _keyManager.GetConfirmationKey(sessionKey),
            storageDataType: AgentOutputKeys.ConfirmationComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreConfirmationComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task StoreConfirmationComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.ConfirmationAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "ConfirmationAgent", AgentOutputKeys.ConfirmationStatus),
                    AgentOutputKeys.ConfirmationStatus,
                    parsedResponse.ConfirmationStatus);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "ConfirmationAgent",
                        AgentOutputKeys.ConfirmationReasoning),
                    AgentOutputKeys.ConfirmationReasoning,
                    parsedResponse.Reasoning);

                _logger.LogInformation(
                    "Confirmation check completed for session {SessionKey}: {Status}",
                    sessionKey,
                    parsedResponse.ConfirmationStatus);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to store confirmation components for session {SessionKey}", sessionKey);
        }
    }
}