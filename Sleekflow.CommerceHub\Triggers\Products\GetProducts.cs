using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Queries;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class GetProducts
    : ITrigger<
        GetProducts.GetProductsInput,
        GetProducts.GetProductsOutput>
{
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;

    public GetProducts(
        IProductService productService,
        IProductVariantService productVariantService)
    {
        _productService = productService;
        _productVariantService = productVariantService;
    }

    public class GetProductsInputFilterGroup
    {
        [Required]
        [JsonProperty("filters")]
        public List<QueryBuilder.Filter> Filters { get; set; }

        [JsonConstructor]
        public GetProductsInputFilterGroup(
            List<QueryBuilder.Filter> filters)
        {
            Filters = filters;
        }
    }

    public class GetProductsInput
    {
        [JsonProperty("continuation_token")]
        [StringLength(1024, MinimumLength = 1)]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [StringLength(128, MinimumLength = 1)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetProductsInputFilterGroup> FilterGroups { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("sorts")]
        public List<QueryBuilder.Sort> Sorts { get; set; }

        [JsonConstructor]
        public GetProductsInput(
            string? continuationToken,
            string sleekflowCompanyId,
            string storeId,
            int limit,
            List<GetProductsInputFilterGroup> filterGroups,
            List<QueryBuilder.Sort> sorts)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            Limit = limit;
            FilterGroups = filterGroups;
            Sorts = sorts;
        }
    }

    public class GetProductsOutput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("products")]
        public List<ProductDto> Products { get; set; }

        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetProductsOutput(
            string? continuationToken,
            List<ProductDto> products,
            long count)
        {
            ContinuationToken = continuationToken;
            Products = products;
            Count = count;
        }
    }

    public async Task<GetProductsOutput> F(GetProductsInput getProductsInput)
    {
        var (products, nextContinuationToken) = await _productService.GetProductsAsync(
            getProductsInput.SleekflowCompanyId,
            getProductsInput.StoreId,
            getProductsInput.Limit,
            getProductsInput.FilterGroups
                .Select(x => new QueryBuilder.FilterGroup(x.Filters.Cast<QueryBuilder.IFilter>().ToList()))
                .ToList(),
            getProductsInput.Sorts,
            getProductsInput.ContinuationToken);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            getProductsInput.SleekflowCompanyId,
            getProductsInput.StoreId,
            products.Select(p => p.Id).ToList());

        var productIdToProductVariantsDict = productVariants
            .GroupBy(pv => pv.ProductId)
            .ToDictionary(e => e.Key, e => e.ToList());

        return new GetProductsOutput(
            nextContinuationToken,
            products
                .Select(
                    c => new ProductDto(
                        c,
                        productIdToProductVariantsDict.GetValueOrDefault(c.Id, new List<ProductVariant>())))
                .ToList(),
            products.Count);
    }
}