﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class ScheduleWebScraperTask : ITrigger<ScheduleWebScraperTask.ScheduleWebScraperTaskInput, ScheduleWebScraperTask.ScheduleWebScraperTaskOutput>
{
    private readonly IWebScraperService _webScraperService;

    public ScheduleWebScraperTask(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class ScheduleWebScraperTaskInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameApifyTaskId)]
        public string ApifyTaskId { get; set; }

        [Required]
        [JsonProperty(WebScraperTaskScheduler.PropertyNameCronExpression)]
        public string CronExpression { get; set; }

        [Required]
        [JsonProperty(WebScraperTaskScheduler.PropertyNameIsEnabled)]
        public bool IsEnabled { get; set; }

        [JsonConstructor]
        public ScheduleWebScraperTaskInput(
            string sleekflowCompanyId,
            string apifyTaskId,
            string cronExpression,
            bool isEnabled)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyTaskId = apifyTaskId;
            CronExpression = cronExpression;
            IsEnabled = isEnabled;
        }
    }

    public class ScheduleWebScraperTaskOutput
    {
        [JsonConstructor]
        public ScheduleWebScraperTaskOutput(WebScraperTask webScraperTask)
        {
            WebScraperTask = webScraperTask;
        }

        [JsonProperty(WebScraperTask.PropertyNameWebScraperTask)]
        public WebScraperTask WebScraperTask { get; set; }
    }

    public async Task<ScheduleWebScraperTaskOutput> F(ScheduleWebScraperTaskInput scheduleWebScraperTaskInput)
    {
        var webScraperTask = await _webScraperService.GetWebScraperTaskAsync(
            scheduleWebScraperTaskInput.SleekflowCompanyId,
            scheduleWebScraperTaskInput.ApifyTaskId);

        if (webScraperTask.WebScraperTaskScheduler == null)
        {
            webScraperTask = await _webScraperService.CreateScheduleForWebScraperTaskAsync(
                scheduleWebScraperTaskInput.SleekflowCompanyId,
                scheduleWebScraperTaskInput.ApifyTaskId,
                scheduleWebScraperTaskInput.CronExpression);
        }
        else
        {
            webScraperTask = await _webScraperService.UpdateScheduleForWebScraperTaskAsync(
                scheduleWebScraperTaskInput.SleekflowCompanyId,
                scheduleWebScraperTaskInput.ApifyTaskId,
                scheduleWebScraperTaskInput.CronExpression,
                scheduleWebScraperTaskInput.IsEnabled);
        }

        return new ScheduleWebScraperTaskOutput(webScraperTask);
    }
}