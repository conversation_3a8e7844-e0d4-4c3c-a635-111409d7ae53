﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class PatchProcessFileDocumentStatus : ITrigger
{
    private readonly ILogger<PatchProcessFileDocumentStatus> _logger;
    private readonly IKbDocumentService _kbDocumentService;

    public PatchProcessFileDocumentStatus(
        ILogger<PatchProcessFileDocumentStatus> logger,
        IKbDocumentService kbDocumentService)
    {
        _logger = logger;
        _kbDocumentService = kbDocumentService;
    }

    public class PatchProcessFileDocumentStatusInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string DocumentId { get; set; }

        [JsonProperty(KbDocument.PropertyNameFileDocumentProcessStatus)]
        [System.ComponentModel.DataAnnotations.Required]
        public string FileDocumentProcessStatus { get; set; }

        [JsonProperty(KbDocument.PropertyNameFileDocumentProcessPercentage)]
        [System.ComponentModel.DataAnnotations.Required]
        [Range(0.0, 100.0)]
        public double FileDocumentProcessPercentage { get; set; }

        [JsonConstructor]
        public PatchProcessFileDocumentStatusInput(
            string sleekflowCompanyId,
            string documentId,
            string fileDocumentProcessStatus,
            double fileDocumentProcessPercentage)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            FileDocumentProcessStatus = fileDocumentProcessStatus;
            FileDocumentProcessPercentage = fileDocumentProcessPercentage;
        }
    }

    public class PatchProcessFileDocumentStatusOutput
    {
    }

    [Function(nameof(PatchProcessFileDocumentStatus))]
    public async Task<PatchProcessFileDocumentStatusOutput> Batch(
        [ActivityTrigger]
        PatchProcessFileDocumentStatusInput patchProcessFileDocumentStatusInput)
    {
        _logger.LogInformation(
            "PatchProcessFileDocumentStatus {SleekflowCompanyId} {DocumentId} {Status} {ProgressPercentage}",
            patchProcessFileDocumentStatusInput.SleekflowCompanyId,
            patchProcessFileDocumentStatusInput.DocumentId,
            patchProcessFileDocumentStatusInput.FileDocumentProcessStatus,
            patchProcessFileDocumentStatusInput.FileDocumentProcessPercentage);

        await _kbDocumentService.PatchFileDocumentProcessStatusAsync(
            patchProcessFileDocumentStatusInput.SleekflowCompanyId,
            patchProcessFileDocumentStatusInput.DocumentId,
            patchProcessFileDocumentStatusInput.FileDocumentProcessStatus,
            patchProcessFileDocumentStatusInput.FileDocumentProcessPercentage);

        return new PatchProcessFileDocumentStatusOutput();
    }
}