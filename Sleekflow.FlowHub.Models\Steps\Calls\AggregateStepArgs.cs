using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class AggregateStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.aggregate";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [Required]
    [JsonProperty("duration__expr")]
    public string DurationExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonConstructor]
    public AggregateStepArgs(string contactIdExpr, string durationExpr)
    {
        ContactIdExpr = contactIdExpr;
        DurationExpr = durationExpr;
    }
}