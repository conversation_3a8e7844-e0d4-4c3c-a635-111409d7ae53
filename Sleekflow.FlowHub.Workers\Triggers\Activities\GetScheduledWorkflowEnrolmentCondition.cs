using System.Text;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class GetScheduledWorkflowEnrolmentCondition
{
    private readonly ILogger<GetScheduledWorkflowEnrolmentCondition> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
    private readonly HttpClient _httpClient;
    private readonly IAppConfig _appConfig;

    public GetScheduledWorkflowEnrolmentCondition(
        ILogger<GetScheduledWorkflowEnrolmentCondition> logger,
        IHttpClientFactory httpClientFactory,
        IAsyncPolicy<HttpResponseMessage> retryPolicy,
        IAppConfig appConfig)
    {
        _logger = logger;
        _retryPolicy = retryPolicy;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _appConfig = appConfig;
    }

    [Function("GetScheduledWorkflowEnrolmentCondition")]
    public async Task<GetScheduledWorkflowEnrolmentConditionOutput> RunAsync(
        [ActivityTrigger] GetScheduledWorkflowEnrolmentConditionInput input)
    {
        var pollyContext = new Context();
        pollyContext["logger"] = _logger;

        var getScheduledWorkflowEnrolmentConditionInputJsonStr =
            JsonConvert.SerializeObject(input);

        var resMsg = await _retryPolicy.ExecuteAsync(
            async (context) =>
            {
                var reqMsg = new HttpRequestMessage
                {
                    Method = HttpMethod.Post,
                    Content = new StringContent(
                        getScheduledWorkflowEnrolmentConditionInputJsonStr,
                        Encoding.UTF8,
                        "application/json"),
                    RequestUri = new Uri(
                        _appConfig.FlowHubInternalsEndpoint + "/GetScheduledWorkflowEnrolmentCondition"),
                    Headers =
                    {
                        {
                            "X-Sleekflow-Key", _appConfig.InternalsKey
                        }
                    }
                };

                _logger.LogInformation("Sending request to FlowHub Internals: {RequestUri}", reqMsg.RequestUri);

                return await _httpClient.SendAsync(reqMsg);
            },
            pollyContext);

        resMsg.EnsureSuccessStatusCode();
        var resMsgContent = await resMsg.Content.ReadAsStringAsync();

        var getScheduledWorkflowEnrolmentConditionOutput =
            JsonConvert.DeserializeObject<GetScheduledWorkflowEnrolmentConditionOutput>(resMsgContent)!;

        return getScheduledWorkflowEnrolmentConditionOutput;
    }
}