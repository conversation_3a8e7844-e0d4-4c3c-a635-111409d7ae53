﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class InitProviderTypesSync : ITrigger
{
    private readonly IProviderConfigService _providerConfigService;
    private readonly ILogger<InitProviderTypesSync> _logger;
    private readonly IProviderSelector _providerSelector;

    public InitProviderTypesSync(
        IProviderConfigService providerConfigService,
        ILogger<InitProviderTypesSync> logger,
        IProviderSelector providerSelector)
    {
        _providerConfigService = providerConfigService;
        _logger = logger;
        _providerSelector = providerSelector;
    }

    public class InitProviderTypesSyncInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name_to_sync_config_dict")]
        [Required]
        public Dictionary<string, SyncConfig> EntityTypeNameToSyncConfigDict { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public InitProviderTypesSyncInput(
            string sleekflowCompanyId,
            Dictionary<string, SyncConfig> entityTypeNameToSyncConfigDict,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeNameToSyncConfigDict = entityTypeNameToSyncConfigDict;
            ProviderName = providerName;
        }
    }

    public class InitStatus
    {
        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [JsonProperty("is_updated")]
        public bool IsUpdated { get; set; }

        [JsonProperty("message", NullValueHandling = NullValueHandling.Ignore)]
        public string? Message { get; set; }

        public InitStatus(string entityTypeName, bool isUpdated, string? message)
        {
            EntityTypeName = entityTypeName;
            IsUpdated = isUpdated;
            Message = message;
        }
    }

    public class InitProviderTypesSyncOutput
    {
        [JsonProperty("entity_type_name_to_init_status_dict")]
        public Dictionary<string, InitStatus> EntityTypeNameToInitStatusDict { get; set; }

        [JsonConstructor]
        public InitProviderTypesSyncOutput(Dictionary<string, InitStatus> entityTypeNameToInitStatusDict)
        {
            EntityTypeNameToInitStatusDict = entityTypeNameToInitStatusDict;
        }
    }

    public async Task<InitProviderTypesSyncOutput> F(
        InitProviderTypesSyncInput initProviderTypesSyncInput)
    {
        var entityTypeNameToSyncConfigDict = initProviderTypesSyncInput.EntityTypeNameToSyncConfigDict;
        var providerName = initProviderTypesSyncInput.ProviderName;
        var sleekflowCompanyId = initProviderTypesSyncInput.SleekflowCompanyId;

        var emptyFieldFiltersSyncConfig = entityTypeNameToSyncConfigDict
            .Select(p => p.Value)
            .FirstOrDefault(p => p.FieldFilters != null && p.FieldFilters.Count == 0);
        if (emptyFieldFiltersSyncConfig != null)
        {
            throw new SfUserFriendlyException("The FieldFilters should be null instead of an empty array.");
        }

        var providerService = _providerSelector.GetProviderService(providerName);

        var providerConfig = await _providerConfigService.GetProviderConfigOrDefaultAsync(
            sleekflowCompanyId,
            providerName);
        if (providerConfig == null || providerConfig.IsAuthenticated == false)
        {
            throw new SfNotInitializedException(providerName);
        }

        var dict = new Dictionary<string, InitStatus>();

        var getSupportedTypesOutput = await providerService.GetSupportedTypesAsync(sleekflowCompanyId);

        foreach (var entityTypeName in getSupportedTypesOutput.SupportedTypes.Select(st => st.Name))
        {
            var syncConfig =
                entityTypeNameToSyncConfigDict.GetValueOrDefault(entityTypeName);
            var prevSyncConfig =
                providerConfig.EntityTypeNameToSyncConfigDict.GetValueOrDefault(entityTypeName);
            if (syncConfig == null && prevSyncConfig == null)
            {
                continue;
            }

            if (syncConfig != null && prevSyncConfig != null && syncConfig.Equals(prevSyncConfig))
            {
                continue;
            }

            await _providerConfigService.UpdateOrRemoveSyncConfigAsync(
                providerConfig.Id,
                providerConfig.SleekflowCompanyId,
                entityTypeName,
                syncConfig);

            try
            {
                await providerService.InitTypeSyncAsync(
                    sleekflowCompanyId,
                    entityTypeName,
                    syncConfig);

                dict[entityTypeName] = new InitStatus(entityTypeName, true, null);
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    exception,
                    "Unable to InitTypeSyncAsync. sleekflowCompanyId {SleekflowCompanyId}, entityTypeName {EntityTypeName}",
                    sleekflowCompanyId,
                    entityTypeName);

                // Rollback to the previous SyncConfig
                await _providerConfigService.UpdateOrRemoveSyncConfigAsync(
                    providerConfig.Id,
                    providerConfig.SleekflowCompanyId,
                    entityTypeName,
                    providerConfig.EntityTypeNameToSyncConfigDict.GetValueOrDefault(entityTypeName));

                dict[entityTypeName] = new InitStatus(entityTypeName, false, exception.Message);
            }
        }

        return new InitProviderTypesSyncOutput(dict);
    }
}