﻿using System.ComponentModel.DataAnnotations;
using DurableTask.Core;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Services;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.Integrator.Hubspot.Triggers.Internals;

[TriggerGroup("Internals")]
public class SubscriptionsCheckBatch : ITrigger
{
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IBus _bus;
    private readonly ILogger<SubscriptionsCheckBatch> _logger;

    public SubscriptionsCheckBatch(
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotConnectionService hubspotConnectionService,
        IHubspotObjectService hubspotObjectService,
        IBus bus,
        ILogger<SubscriptionsCheckBatch> logger)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotConnectionService = hubspotConnectionService;
        _hubspotObjectService = hubspotObjectService;
        _bus = bus;
        _logger = logger;
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public HubspotSubscription Subscription { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            HubspotSubscription subscription,
            string? after,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            EntityTypeName = entityTypeName;
            After = after;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("after")]
        public string? After { get; set; }

        [JsonProperty("last_object_modification_time")]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            string? after,
            DateTimeOffset lastObjectModificationTime)
        {
            Count = count;
            After = after;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public async Task<SubscriptionsCheckBatchOutput> F(
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var after = subscriptionsCheckBatchInput.After;
        var subscription = subscriptionsCheckBatchInput.Subscription;
        var entityTypeName = subscriptionsCheckBatchInput.EntityTypeName;
        var sleekflowCompanyId = subscriptionsCheckBatchInput.SleekflowCompanyId;
        var filterGroups = subscriptionsCheckBatchInput.FilterGroups;
        var fieldFilters = subscriptionsCheckBatchInput.FieldFilters;
        var lastExecutionStartTime = subscription.LastExecutionStartTime;

        HubspotAuthentication? authentication;
        if (subscription.ConnectionId is null)
        {
            authentication =
                await _hubspotAuthenticationService.GetAsync(subscriptionsCheckBatchInput.SleekflowCompanyId);
        }
        else
        {
            var connection = await _hubspotConnectionService.GetByIdAsync(
                subscription.ConnectionId,
                subscription.SleekflowCompanyId);

            authentication =
                await _hubspotAuthenticationService.GetAsync(connection.AuthenticationId, subscriptionsCheckBatchInput.SleekflowCompanyId);
        }

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Start after {After}, hubspotSubscription.Id {SubscriptionId}, hubspotSubscription.LastExecutedTime {LastExecutionStartTime}",
            after,
            subscription.Id,
            subscription.LastExecutionStartTime);

        // For User, this syncs every users
        var (objects, nextAfter) = entityTypeName == "User"
            ? await _hubspotObjectService.GetObjectsAsync(
                authentication,
                200,
                after,
                entityTypeName,
                filterGroups,
                fieldFilters)
            : await _hubspotObjectService.GetRecentlyUpdatedObjectsAsync(
                authentication,
                lastExecutionStartTime,
                entityTypeName,
                filterGroups,
                fieldFilters,
                after);

        _logger.LogInformation(
            "End after {After}, hubspotSubscription.Id {SubscriptionId}, hubspotSubscription.LastExecutedTime {LastExecutionStartTime}, count=[{Count}], nextAfter=[{NextAfter}]",
            after,
            subscription.Id,
            subscription.LastExecutionStartTime,
            objects.Count,
            nextAfter);

        var lastObjectModificationTime = lastExecutionStartTime;
        var events = new List<OnObjectOperationEvent>();
        var objectCreatedEventRequests = new List<HubspotObjectCreatedEventRequest>();
        var objectUpdatedEventRequests = new List<HubspotObjectUpdatedEventRequest>();

        foreach (var dict in objects)
        {
            DateTimeOffset? lastModifiedDate;
            if (dict.ContainsKey("lastmodifieddate"))
            {
                lastModifiedDate = dict.GetValueOrDefault("lastmodifieddate") as DateTimeOffset?;
            }
            else if (dict.ContainsKey("updatedAt"))
            {
                lastModifiedDate = dict.GetValueOrDefault("updatedAt") as DateTimeOffset?;
            }
            else
            {
                lastModifiedDate = null;
            }

            if (lastModifiedDate == null)
            {
                _logger.LogError(
                    "The lastModifiedDate is invalid. lastModifiedDate {LastModifiedDate}",
                    lastModifiedDate);

                continue;
            }

            lastObjectModificationTime = (DateTimeOffset) lastModifiedDate > lastObjectModificationTime
                ? (DateTimeOffset) lastModifiedDate
                : lastObjectModificationTime;

            if (subscription.IsFlowsBased is true)
            {
                DateTimeOffset? createDate;
                if (dict.ContainsKey("createdate"))
                {
                    createDate = dict.GetValueOrDefault("createdate") as DateTimeOffset?;
                }
                else
                {
                    createDate = null;
                }

                if (createDate == null)
                {
                    continue;
                }

                if (lastModifiedDate > createDate.Value.AddMinutes(1))
                {
                    objectUpdatedEventRequests.Add(
                        new HubspotObjectUpdatedEventRequest(
                            DateTimeOffset.UtcNow,
                            subscriptionsCheckBatchInput.SleekflowCompanyId,
                            subscriptionsCheckBatchInput.Subscription.ConnectionId!,
                            _hubspotObjectService.ResolveObjectId(dict),
                            entityTypeName,
                            dict));
                }
                else
                {
                    objectCreatedEventRequests.Add(
                        new HubspotObjectCreatedEventRequest(
                            DateTimeOffset.UtcNow,
                            subscriptionsCheckBatchInput.SleekflowCompanyId,
                            subscriptionsCheckBatchInput.Subscription.ConnectionId!,
                            _hubspotObjectService.ResolveObjectId(dict),
                            entityTypeName,
                            dict));
                }

                continue;
            }

            var onObjectOperationEvent = new OnObjectOperationEvent(
                dict,
                OnObjectOperationEvent.OperationCreateOrUpdateObject,
                "hubspot-integrator",
                sleekflowCompanyId,
                _hubspotObjectService.ResolveObjectId(dict),
                entityTypeName,
                null);

            events.Add(onObjectOperationEvent);
        }

        if (subscription.IsFlowsBased is true)
        {
            foreach (var eventRequests in objectCreatedEventRequests.Chunk(30))
            {
                await _bus.PublishBatch(
                    eventRequests,
                    context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
            }

            foreach (var eventRequests in objectUpdatedEventRequests.Chunk(30))
            {
                await _bus.PublishBatch(
                    eventRequests,
                    context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
            }
        }
        else
        {
            foreach (var onObjectOperationEvents in events.Chunk(30))
            {
                await _bus.PublishBatch(
                    onObjectOperationEvents,
                    context => { context.ConversationId = Guid.Parse(sleekflowCompanyId); });
            }
        }

        _logger.LogInformation(
            "Flushed after {After}, hubspotSubscription.Id {SubscriptionId}, hubspotSubscription.LastExecutedTime {LastExecutionStartTime}, count {Count}, nextAfter {NextAfter}",
            after,
            subscription.Id,
            subscription.LastExecutionStartTime,
            objects.Count,
            nextAfter);

        return new SubscriptionsCheckBatchOutput(objects.Count, nextAfter, lastObjectModificationTime);
    }
}