using Newtonsoft.Json;
using Sleekflow.Utils;

namespace Sleekflow.Exceptions.TenantHub;

public class SfCoreInvalidModelState
{
    [JsonRequired]
    public int Code { get; set; }

    [JsonRequired]
    public string Message { get; set; }

    [JsonRequired]
    public List<string> Errors { get; set; }

    public SfCoreInvalidModelState(int code, string message, List<string> errors)
    {
        Code = code;
        Message = message;
        Errors = errors;
    }
}

public class SfCoreInvalidModelStateException : ErrorCodeException
{
    public SfCoreInvalidModelStateException(string message, Dictionary<string, object?> context)
        : base(
            ErrorCodeConstant.SfInvalidTravisBackendModelStateException,
            message,
            context)
    {
    }

    public SfCoreInvalidModelStateException(SfCoreInvalidModelState state)
        : base(
            ErrorCodeConstant.SfInvalidTravisBackendModelStateException,
            state.Message,
            state.Errors.ToDictionary(
                i => state.Errors.IndexOf(i).ToString(),
                i=> (object?)i ))
    {
    }
}