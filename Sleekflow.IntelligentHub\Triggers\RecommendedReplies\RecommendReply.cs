using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.RegularExpressions;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.IntelligentHub.TextEnrichments;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.RecommendedReplies;

[TriggerGroup(ControllerNames.RecommendedReplies)]
public class RecommendReply : ITrigger<RecommendReply.RecommendReplyInput, RecommendReply.RecommendReplyOutput>
{
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IChatService _chatService;
    private readonly ITextTranslationService _textTranslationService;

    public RecommendReply(
        IIntelligentHubConfigService intelligentHubConfigService,
        IIntelligentHubUsageService intelligentHubUsageService,
        IChatService chatService,
        ITextTranslationService textTranslationService)
    {
        _intelligentHubConfigService = intelligentHubConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _chatService = chatService;
        _textTranslationService = textTranslationService;
    }

    public class RecommendReplyInput : IHasSleekflowCompanyId
    {
        [Required]
        [ValidateArray]
        [JsonProperty("conversation_context")]
        public List<SfChatEntry> ConversationContext { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IntelligentHubUsageFilter.ModelNameIntelligentHubUsageFilter)]
        [ValidateObject]
        public IntelligentHubUsageFilter? IntelligentHubUsageFilter { get; set; }

        [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
        [ValidateObject]
        public AuditEntity.SleekflowStaff? CreatedBy { get; set; }


        [JsonConstructor]
        public RecommendReplyInput(
            List<SfChatEntry> conversationContext,
            string sleekflowCompanyId,
            IntelligentHubUsageFilter? intelligentHubUsageFilter,
            AuditEntity.SleekflowStaff? createdBy)
        {
            ConversationContext = conversationContext;
            SleekflowCompanyId = sleekflowCompanyId;
            IntelligentHubUsageFilter = intelligentHubUsageFilter;
            CreatedBy = createdBy;
        }
    }

    public class RecommendReplyOutput
    {
        [JsonProperty("recommended_reply")]
        public string RecommendedReply { get; set; }

        [JsonConstructor]
        public RecommendReplyOutput(string recommendedReply)
        {
            RecommendedReply = recommendedReply;
        }
    }

    public async Task<RecommendReplyOutput> F(RecommendReplyInput recommendReplyInput)
    {
        var intelligentHubConfig =
            await _intelligentHubConfigService.GetIntelligentHubConfigAsync(recommendReplyInput.SleekflowCompanyId);

        var isUsageLimitExceeded = intelligentHubConfig == null ||
                                   await _intelligentHubUsageService.IsUsageLimitExceeded(
                                       recommendReplyInput.SleekflowCompanyId,
                                       new Dictionary<string, int>
                                       {
                                           {
                                               PriceableFeatures.AiFeaturesTotalUsage,
                                               _intelligentHubUsageService.GetFeatureTotalUsageLimit(
                                                   intelligentHubConfig,
                                                   PriceableFeatures.AiFeaturesTotalUsage)
                                           }
                                       },
                                       recommendReplyInput.IntelligentHubUsageFilter);

        if (isUsageLimitExceeded)
        {
            throw new SfUserFriendlyException("Cannot find IntelligentHubConfig or exceed max usage limit.");
        }

        var answerSb = new StringBuilder();

        var (asyncEnumerable, _, sourcesStr) = await _chatService.StreamAnswerAsync(
            recommendReplyInput.ConversationContext,
            recommendReplyInput.SleekflowCompanyId);

        await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
        {
            if (partialAnswer is null)
            {
                continue;
            }

            answerSb.Append(partialAnswer);
        }

        var recommendedReply = answerSb.ToString();
        recommendedReply = Regex.Replace(recommendedReply, @"\[source\]", string.Empty, RegexOptions.IgnoreCase);

        await _intelligentHubUsageService.RecordUsageAsync(
            recommendReplyInput.SleekflowCompanyId,
            PriceableFeatures.RecommendReply,
            recommendReplyInput.CreatedBy,
            new RecommendReplySnapshot(
                JsonConvert.SerializeObject(
                    recommendReplyInput.ConversationContext,
                    JsonConfig.DefaultLoggingJsonSerializerSettings),
                sourcesStr ?? string.Empty,
                recommendedReply));

        return new RecommendReplyOutput(recommendedReply);
    }
}