namespace Sleekflow.FlowHub.Models.States;

public static class StateStatuses
{
    public const string Running = "Running";
    public const string Complete = "Complete";
    public const string Failed = "Failed";
    public const string Cancelled = "Cancelled";
    public const string Blocked = "Blocked";
    public const string Scheduled = "Scheduled";
    public const string Abandoned = "Abandoned";
    public const string Restricted = "Restricted";
    public const string Reenrolled = "Reenrolled";
}