using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface ILiveChatMessageBodyCreator : IMessageBodyCreator
{
}

public class LiveChatMessageBodyCreator : BaseMessageBodyCreator, ILiveChatMessageBodyCreator
{
    public LiveChatMessageBodyCreator()
        : base(ChannelTypes.LiveChat)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        return Task.FromResult((
            CreateBaseMessageBody(
                liveChatMessage: new LiveChatMessageObject(messageStr)),
            "text"));
    }
}