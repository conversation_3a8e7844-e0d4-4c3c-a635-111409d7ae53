using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Payments.Stripe;

public interface IStripePaymentConfig
{
    string ApiKeyHk { get; set; }

    string ApiKeySg { get; set; }

    string ApiKeyMy { get; set; }

    string ApiKeyGb { get; set; }
}

public class StripePaymentConfig : IStripePaymentConfig, IConfig
{
    public string ApiKeyHk { get; set; }

    public string ApiKeySg { get; set; }

    public string ApiKeyMy { get; set; }

    public string ApiKeyGb { get; set; }

    public StripePaymentConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ApiKeyHk = Environment.GetEnvironmentVariable("STRIPE_API_KEY_HK", target)
                   ?? throw new SfMissingEnvironmentVariableException("STRIPE_API_KEY_HK");
        ApiKeySg = Environment.GetEnvironmentVariable("STRIPE_API_KEY_SG", target)
                   ?? throw new SfMissingEnvironmentVariableException("STRIPE_API_KEY_SG");
        ApiKeyMy = Environment.GetEnvironmentVariable("STRIPE_API_KEY_MY", target)
                   ?? throw new SfMissingEnvironmentVariableException("STRIPE_API_KEY_MY");
        ApiKeyGb = Environment.GetEnvironmentVariable("STRIPE_API_KEY_GB", target)
                   ?? throw new SfMissingEnvironmentVariableException("STRIPE_API_KEY_GB");
    }
}