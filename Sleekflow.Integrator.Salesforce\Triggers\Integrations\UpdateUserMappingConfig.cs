﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class UpdateUserMappingConfig : ITrigger
{
    private readonly ISalesforceUserMappingConfigService _salesforceUserMappingConfigService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public UpdateUserMappingConfig(
        ISalesforceUserMappingConfigService salesforceUserMappingConfigService,
        ISalesforceConnectionService salesforceConnectionService,
        ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceUserMappingConfigService = salesforceUserMappingConfigService;
        _salesforceConnectionService = salesforceConnectionService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class UpdateUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [JsonProperty("user_mapping_config_id")]
        [Required]
        public string UserMappingConfigId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [ValidateArray]
        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }

        [JsonConstructor]
        public UpdateUserMappingConfigInput(
            string userMappingConfigId,
            string sleekflowCompanyId,
            List<UserMapping>? userMappings)
        {
            UserMappingConfigId = userMappingConfigId;
            SleekflowCompanyId = sleekflowCompanyId;
            UserMappings = userMappings;
        }
    }

    public class UpdateUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public UpdateUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<UpdateUserMappingConfigOutput> F(
        UpdateUserMappingConfigInput updateUserMappingConfigInput)
    {
        var id = updateUserMappingConfigInput.UserMappingConfigId;
        var sleekflowCompanyId = updateUserMappingConfigInput.SleekflowCompanyId;
        var userMappings = updateUserMappingConfigInput.UserMappings;

        var userMappingConfig = await _salesforceUserMappingConfigService.GetByIdAsync(
            id,
            sleekflowCompanyId);

        var connection =
            await _salesforceConnectionService.GetByIdAsync(
                userMappingConfig.ConnectionId,
                sleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        userMappingConfig = await _salesforceUserMappingConfigService.PatchAndGetAsync(
                id,
                sleekflowCompanyId,
                userMappings);

        return new UpdateUserMappingConfigOutput(
            new ProviderUserMappingConfigDto(
                userMappingConfig.Id,
                userMappingConfig.SleekflowCompanyId,
                userMappingConfig.ConnectionId,
                userMappingConfig.UserMappings));
    }
}