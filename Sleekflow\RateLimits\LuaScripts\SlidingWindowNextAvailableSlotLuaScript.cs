using StackExchange.Redis;

namespace Sleekflow.RateLimits.LuaScripts;

public class SlidingWindowNextAvailableSlotLuaScript : ILuaScript
{
    public LuaScript GetScript()
    {
        var rateLimitScript = LuaScript.Prepare(
            """
            local function unquote_if_needed(s)
                if type(s) == "string" then
                    local content_inside_quotes = string.match(s, '^"(.*)"$')
                    if content_inside_quotes then
                        return content_inside_quotes
                    end
                end
                return s
            end

            local current_time_num = tonumber(@current_time)
            local default_window_size_ms_num = tonumber(@window_size_ms)
            local default_rate_limit_num = tonumber(@rate_limit)
            local earliest_desired_publish_time_ms_num = tonumber(@earliest_desired_publish_time_ms)

            -- Code to check if default rate limit is override
            local config_window_key = @cache_config_prefix .. "-" .. @key_name .. ":config:window_size_ms"
            local config_rate_limit_key = @cache_config_prefix .. "-" .. @key_name .. ":config:rate_limit"

            -- Try to fetch override values from Redis
            local window_size_ms_override_str = unquote_if_needed(redis.call('GET', config_window_key))
            local rate_limit_override_str = unquote_if_needed(redis.call('GET', config_rate_limit_key))

            local effective_window_size_ms_num
            if window_size_ms_override_str then
                effective_window_size_ms_num = tonumber(window_size_ms_override_str)
                if effective_window_size_ms_num == nil then -- Handle potential non-numeric value
                    effective_window_size_ms_num = default_window_size_ms_num
                end
            else
                effective_window_size_ms_num = default_window_size_ms_num
            end

            local effective_rate_limit_num
            if rate_limit_override_str then
                effective_rate_limit_num = tonumber(rate_limit_override_str)
                if effective_rate_limit_num == nil then -- Handle potential non-numeric value
                    effective_rate_limit_num = default_rate_limit_num
                end
            else
                effective_rate_limit_num = default_rate_limit_num
            end

            -- Sliding Window Algorithm
            local trim_time = current_time_num - effective_window_size_ms_num
            redis.call('ZREMRANGEBYSCORE', @key_name, 0, trim_time)

            local current_count = tonumber(redis.call('ZCARD', @key_name))

            local final_scheduled_time_ms

            if current_count < effective_rate_limit_num then
                final_scheduled_time_ms = earliest_desired_publish_time_ms_num
            else
                local rank_to_fetch = current_count - effective_rate_limit_num

                local result = redis.call('ZRANGE', @key_name, rank_to_fetch, rank_to_fetch, 'WITHSCORES')

                if result == nil or #result < 2 then
                    final_scheduled_time_ms = earliest_desired_publish_time_ms_num
                else
                    local slot_defining_timestamp_ms = tonumber(result[2])
                    local calculated_next_available_slot_ms = slot_defining_timestamp_ms + effective_window_size_ms_num

                    if earliest_desired_publish_time_ms_num > calculated_next_available_slot_ms then
                        final_scheduled_time_ms = earliest_desired_publish_time_ms_num
                    else
                        final_scheduled_time_ms = calculated_next_available_slot_ms
                    end
                end
            end

            redis.call('ZADD', @key_name, final_scheduled_time_ms, @unique_message_id)

            local window_duration_seconds = math.ceil(effective_window_size_ms_num / 1000)
            local key_ttl_seconds = math.max(60, window_duration_seconds * 3)
            redis.call('EXPIRE', @key_name, key_ttl_seconds)

            return {final_scheduled_time_ms, effective_rate_limit_num - current_count - 1}
            """);

        return rateLimitScript;
    }
}