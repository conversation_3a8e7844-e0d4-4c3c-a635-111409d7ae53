using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Hosting;
using Sleekflow;
using Sleekflow.Mvc;

const string name = "SleekflowInternalIntegrationHubWorker";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildServiceBusServices(services);
        services.AddDurableTaskClient(
            _ =>
            {
            });
    });

hostBuilder.Build().Run();