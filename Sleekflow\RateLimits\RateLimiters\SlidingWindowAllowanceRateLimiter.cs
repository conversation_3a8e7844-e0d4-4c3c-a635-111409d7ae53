﻿using Microsoft.Extensions.Logging;
using Sleekflow.Constants;
using Sleekflow.RateLimits.LuaScripts;
using StackExchange.Redis;

namespace Sleekflow.RateLimits.RateLimiters;

public class SlidingWindowAllowanceRateLimiter : IAllowanceRateLimiter
{
    private const string RateLimitAlgorithm = RateLimitAlgorithms.SlidingWindowAllowance;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILuaScriptRepositoryService _luaScriptRepositoryService;
    private readonly ILogger<SlidingWindowAllowanceRateLimiter> _logger;

    public SlidingWindowAllowanceRateLimiter(
        IConnectionMultiplexer connectionMultiplexer,
        ILuaScriptRepositoryService luaScriptRepositoryService,
        ILogger<SlidingWindowAllowanceRateLimiter> logger)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _luaScriptRepositoryService = luaScriptRepositoryService;
        _logger = logger;
    }

    public async Task<bool> IsAllowedAsync(
        string keyName,
        ILuaScriptParam luaScriptParam)
    {
        var (isAllowed, remainLimit) = await GetRemainingAsync(keyName, luaScriptParam);

        return isAllowed;
    }

    public async Task<(bool IsAllowed, int RemainLimit)> GetRemainingAsync(
        string keyName,
        ILuaScriptParam luaScriptParam)
    {
        var slidingWindowParam = (SlidingWindowParam) luaScriptParam;

        var luaScript = _luaScriptRepositoryService.GetScript(RateLimitAlgorithm);
        var windowSeconds = slidingWindowParam.WindowSeconds;
        var maxRequestsAllowedWithinWindow = slidingWindowParam.MaxRequestsAllowedWithinWindow;
        if (windowSeconds <= 0
            || maxRequestsAllowedWithinWindow <= 0)
        {
            return (false, 0);
        }

        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var result = (RedisValue[]) await database.ScriptEvaluateAsync(
                luaScript,
                new
                {
                    key = new RedisKey(keyName), window = windowSeconds, max_requests = maxRequestsAllowedWithinWindow
                }
            );

            var isAllowed = (bool) result[0];
            var remaining = (int) result[1];
            return (isAllowed, remaining);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Evaluate Lua Script] error occured when evaluating rate limit Lua Script: {Message}",
                ex.ToString());
            return (true, 1);
        }
    }
}