using Microsoft.Extensions.Logging;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Evaluator;

using System.Xml;
using Alba;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Mvc.Tests;
using Sleekflow.Persistence.Abstractions;

[SetUpFixture]
public class Application
{
    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }

    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.IntelligentHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        await SetUpHost();
    }

    private static async Task SetUpHost()
    {
        Host = await AlbaHost.For<Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(services =>
                {
                    services.AddScoped<
                        IDynamicFiltersRepositoryContext,
                        TestRepositoryContext>();

                    services.AddTransient<InformationGatheringPlugin>();
                    services.AddTransient<SleekflowToolsPlugin>();

                    // Mock plugins
                    services.AddSingleton<ISleekflowToolsPlugin, MockSleekflowToolsPlugin>();
                    services.AddSingleton<IChiliPiperPlugin, MockChiliPiperPlugin>();
                    services.AddSingleton<IHubspotPlugin, MockHubspotPlugin>();

                    // Wrap the InformationGatheringPlugin with a mock
                    services.AddSingleton<IInformationGatheringPlugin>(sp => new MockInformationGatheringPlugin(
                        sp.GetRequiredService<ILogger<MockInformationGatheringPlugin>>(),
                        sp.GetRequiredService<InformationGatheringPlugin>()));

                    // Wrap the SleekflowToolsPlugin with a mock
                    services.AddSingleton<ISleekflowToolsPlugin>(sp => new MockSleekflowToolsPlugin(
                        sp.GetRequiredService<ILogger<MockSleekflowToolsPlugin>>(),
                        sp.GetRequiredService<SleekflowToolsPlugin>()));
                });
            },
            Array.Empty<IAlbaExtension>());

        Host.AfterEachAsync(async context =>
        {
            await BaseTestHost.InterceptAfterEachAsync(context);
        });
    }

    public class TestRepositoryContext : IDynamicFiltersRepositoryContext
    {
        // The change feed processor relies on the status(active/deleted) of knowledge base entries to manage indexers,
        // if soft delete is not enable, the change feed processor cannot capture the change.
        public bool IsSoftDeleteEnabled { get; set; } = true;

        public string? SleekflowCompanyId { get; set; } = null;
    }

    public static IAlbaHost Host { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        Host.Dispose();
    }
}