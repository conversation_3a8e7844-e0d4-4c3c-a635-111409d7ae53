using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Migrations.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Migrations)]
public class VerifyWhatsappCloudApiPhoneNumberOwnership
    : ITrigger<
        VerifyWhatsappCloudApiPhoneNumberOwnership.VerifyWhatsappCloudApiPhoneNumberOwnershipInput,
        VerifyWhatsappCloudApiPhoneNumberOwnership.VerifyWhatsappCloudApiPhoneNumberOwnershipOutput>
{
    private readonly IMigrationService _migrationService;
    private readonly IWabaService _wabaService;

    public VerifyWhatsappCloudApiPhoneNumberOwnership(IMigrationService migrationService, IWabaService wabaService)
    {
        _migrationService = migrationService;
        _wabaService = wabaService;
    }

    public class VerifyWhatsappCloudApiPhoneNumberOwnershipInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("facebook_phone_number_id")]
        public string FacebookPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("code")]
        public string Code { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public VerifyWhatsappCloudApiPhoneNumberOwnershipInput(
            string sleekflowCompanyId,
            string facebookWabaId,
            string facebookPhoneNumberId,
            string code,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookWabaId = facebookWabaId;
            FacebookPhoneNumberId = facebookPhoneNumberId;
            Code = code;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class VerifyWhatsappCloudApiPhoneNumberOwnershipOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public VerifyWhatsappCloudApiPhoneNumberOwnershipOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<VerifyWhatsappCloudApiPhoneNumberOwnershipOutput> F(
        VerifyWhatsappCloudApiPhoneNumberOwnershipInput verifyWhatsappCloudApiPhoneNumberOwnershipInput)
    {
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(
            verifyWhatsappCloudApiPhoneNumberOwnershipInput.FacebookWabaId);

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        return new VerifyWhatsappCloudApiPhoneNumberOwnershipOutput(
            await _migrationService.VerifyWhatsappCloudApiPhoneNumberOwnershipAsync(
                verifyWhatsappCloudApiPhoneNumberOwnershipInput.SleekflowCompanyId,
                verifyWhatsappCloudApiPhoneNumberOwnershipInput.FacebookWabaId,
                verifyWhatsappCloudApiPhoneNumberOwnershipInput.FacebookPhoneNumberId,
                verifyWhatsappCloudApiPhoneNumberOwnershipInput.Code,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null,
                verifyWhatsappCloudApiPhoneNumberOwnershipInput.SleekflowStaffId,
                verifyWhatsappCloudApiPhoneNumberOwnershipInput.SleekflowStaffTeamIds));
    }
}