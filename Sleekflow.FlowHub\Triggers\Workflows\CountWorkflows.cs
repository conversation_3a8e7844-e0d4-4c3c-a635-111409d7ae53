using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class CountWorkflows : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public CountWorkflows(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class CountWorkflowsInput : Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_type")]
        public string? WorkflowType { get; set; }

        [JsonConstructor]
        public CountWorkflowsInput(string sleekflowCompanyId, string? workflowType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowType = workflowType;
        }
    }

    public class CountWorkflowsOutput
    {
        [JsonProperty("num_of_workflows")]
        public int NumOfWorkflows { get; set; }

        [JsonProperty("num_of_active_workflows")]
        public int NumOfActiveWorkflows { get; set; }

        [JsonConstructor]
        public CountWorkflowsOutput(int numOfWorkflows, int numOfActiveWorkflows)
        {
            NumOfWorkflows = numOfWorkflows;
            NumOfActiveWorkflows = numOfActiveWorkflows;
        }
    }

    public async Task<CountWorkflowsOutput> F(CountWorkflowsInput countWorkflowsInput)
    {
        var numOfWorkflows = await _workflowService.CountWorkflowsAsync(
            sleekflowCompanyId: countWorkflowsInput.SleekflowCompanyId,
            workflowType: countWorkflowsInput.WorkflowType);

        var numOfActiveWorkflows = await _workflowService.CountWorkflowsAsync(
            sleekflowCompanyId: countWorkflowsInput.SleekflowCompanyId,
            activationStatus: WorkflowActivationStatuses.Active,
            workflowType: countWorkflowsInput.WorkflowType);

        return new CountWorkflowsOutput(numOfWorkflows, numOfActiveWorkflows);
    }
}