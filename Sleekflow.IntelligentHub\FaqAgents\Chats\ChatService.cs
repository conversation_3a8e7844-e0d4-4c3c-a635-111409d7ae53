using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.FaqAgents.Configs;
using Sleekflow.IntelligentHub.Models.Chats;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

public interface IChatService
{
    Task<(IAsyncEnumerable<string> AsyncEnumerable, int ConsumedNumOfTokens, string? SourcesStr)> StreamAnswerAsync(
        List<SfChatEntry> sfChatEntries,
        string sleekflowCompanyId);

    Task<(IAsyncEnumerable<string> MultiAgentChatStream, string? SourceStr, string GroupChatIdStr)>
        StreamAgentAnswerAsync(
            List<SfChatEntry> sfChatEntries,
            string sleekflowCompanyId,
            ReplyGenerationContext replyGenerationContext,
            CompanyAgentConfig agentConfig);

    Task<int> GetConfidenceScoring(List<SfChatEntry> conversationContext, string recommendedReply);
}

public class ChatService : IChatService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly ILogger _logger;
    private readonly IFaqAgentConfigService _faqAgentConfigService;
    private readonly ISearchPlugin _searchPlugin;
    private readonly ISmartReplyPlugin _smartReplyPlugin;
    private readonly IReviewerService _reviewerService;
    private readonly ILanguagePlugin _languagePlugin;

    public ChatService(
        Kernel kernel,
        ILogger<ChatService> logger,
        IFaqAgentConfigService faqAgentConfigService,
        ISearchPlugin searchPlugin,
        ISmartReplyPlugin smartReplyPlugin,
        IReviewerService reviewerService,
        ILanguagePlugin languagePlugin)
    {
        _logger = logger;
        _kernel = kernel;
        _faqAgentConfigService = faqAgentConfigService;
        _searchPlugin = searchPlugin;
        _smartReplyPlugin = smartReplyPlugin;
        _reviewerService = reviewerService;
        _languagePlugin = languagePlugin;
    }

    public async Task<(IAsyncEnumerable<string> AsyncEnumerable, int ConsumedNumOfTokens, string? SourcesStr)>
        StreamAnswerAsync(
            List<SfChatEntry> sfChatEntries,
            string sleekflowCompanyId)
    {
        var agentConfigOrDefault = _faqAgentConfigService.GetAgentConfigOrDefault(sleekflowCompanyId);
        var sourcesStr = await GetSourcesStr(sfChatEntries, sleekflowCompanyId);

        var companyName = string.IsNullOrEmpty(agentConfigOrDefault.CompanyName)
            ? "the company"
            : agentConfigOrDefault.CompanyName;
        var (userQuestion, chatHistoryStr) = SfChatEntryUtils.ToQuestionAndChatHistoryStr(sfChatEntries);
        var additionalPromptForChat = string.Join(
            "\n",
            agentConfigOrDefault.AdditionalPromptsForChat.Select(p => $" - {p}").ToList());

        var detectAppropriateResponseLanguageResponse =
            await _languagePlugin.DetectAppropriateResponseLanguageAsync(_kernel, chatHistoryStr);
        var replyLanguageCode = detectAppropriateResponseLanguageResponse.ResponseLanguageName;

        IAsyncEnumerable<string> asyncEnumerable;
        if (agentConfigOrDefault.HeadlessApiKey == string.Empty)
        {
            // SmartReplyPlugin SmartReply
            asyncEnumerable = _smartReplyPlugin.SmartReplyStreaming(
                _kernel,
                companyName,
                replyLanguageCode,
                sourcesStr,
                chatHistoryStr,
                additionalPromptForChat);
        }
        else
        {
            // SmartReplyPlugin FactualSmartReply
            asyncEnumerable = _smartReplyPlugin.FactualSmartReplyStreaming(
                _kernel,
                companyName,
                replyLanguageCode,
                sourcesStr,
                chatHistoryStr,
                additionalPromptForChat);
        }

        return (
            asyncEnumerable,
            0,
            sourcesStr);
    }

    public async
        Task<(IAsyncEnumerable<string> MultiAgentChatStream, string? SourceStr, string GroupChatIdStr)>
        StreamAgentAnswerAsync(
            List<SfChatEntry> sfChatEntries,
            string sleekflowCompanyId,
            ReplyGenerationContext replyGenerationContext,
            CompanyAgentConfig agentConfig)
    {
        var groupChatIdStr = EnrichKernelData(_kernel, replyGenerationContext, agentConfig);

        var (sourceStr, multiAgentChatStream, tokenTokens)
            = await _smartReplyPlugin.ChatAgentSmartReplyStreaming(
                _kernel,
                sfChatEntries,
                replyGenerationContext,
                agentConfig);

        return (multiAgentChatStream, sourceStr, groupChatIdStr);
    }

    private async Task<string> GetSourcesStr(List<SfChatEntry> sfChatEntries, string sleekflowCompanyId)
    {
        var (userQuestion, chatHistoryInStr) = SfChatEntryUtils.ToQuestionAndChatHistoryStr(sfChatEntries);

        return await _searchPlugin.GetSources(_kernel, userQuestion, chatHistoryInStr, sleekflowCompanyId);
    }

    public async Task<int> GetConfidenceScoring(List<SfChatEntry> conversationContext, string recommendedReply)
    {
        try
        {
            _logger.LogInformation(
                "Getting confidence scoring for recommended reply: {RecommendedReply}",
                recommendedReply);

            var confidenceScore = await _reviewerService.GetConfidenceScoringAsync(
                conversationContext,
                recommendedReply);

            return confidenceScore;
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to get confidence scoring");
        }

        return 0;
    }

    private string EnrichKernelData(
        Kernel kernel,
        ReplyGenerationContext replyGenerationContext,
        CompanyAgentConfig agentConfig)
    {
        var groupChatId = GroupChatId.Create(
            replyGenerationContext.SleekflowCompanyId,
            replyGenerationContext.ContactId);
        var groupChatIdStr = groupChatId.ToString();

        kernel.Data[KernelDataKeys.GROUP_CHAT_ID] = groupChatIdStr;
        kernel.Data[KernelDataKeys.SLEEKFLOW_COMPANY_ID] = replyGenerationContext.SleekflowCompanyId;
        kernel.Data[KernelDataKeys.CONTACT_ID] = replyGenerationContext.ContactId;
        kernel.Data[KernelDataKeys.STATE_ID] = replyGenerationContext.StateId;
        kernel.Data[KernelDataKeys.AGENT_ID] = agentConfig.Id;

        return groupChatIdStr;
    }
}