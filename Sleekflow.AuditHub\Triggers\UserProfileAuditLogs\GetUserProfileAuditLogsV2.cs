using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class GetUserProfileAuditLogsV2 : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;

    public GetUserProfileAuditLogsV2(IUserProfileAuditLogService userProfileAuditLogService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
    }

    public class GetUserProfileAuditLogsV2Input : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("filters")]
        public GetUserProfileAuditLogsV2InputFilters Filters { get; set; }

        [Required]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetUserProfileAuditLogsV2Input(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? continuationToken,
            GetUserProfileAuditLogsV2InputFilters filters,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            ContinuationToken = continuationToken;
            Filters = filters;
            Limit = limit;
        }
    }

    public class GetUserProfileAuditLogsV2InputFilters : UserProfileAuditLogsFilters
    {
        [JsonConstructor]
        public GetUserProfileAuditLogsV2InputFilters(List<string>? types, bool? hasSleekflowStaffId)
            : base(types, hasSleekflowStaffId)
        {
        }
    }

    public class GetUserProfileAuditLogsV2Output
    {
        [JsonProperty("user_profile_audit_logs")]
        public List<UserProfileAuditLog> UserProfileAuditLogs { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetUserProfileAuditLogsV2Output(
            List<UserProfileAuditLog> userProfileAuditLogs,
            string? nextContinuationToken)
        {
            UserProfileAuditLogs = userProfileAuditLogs;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetUserProfileAuditLogsV2Output> F(
        GetUserProfileAuditLogsV2Input getUserProfileAuditLogsV2Input)
    {
        var (nextContinuationToken, userProfileAuditLogs) =
            await _userProfileAuditLogService.GetUserProfileAuditLogsAsync(
                getUserProfileAuditLogsV2Input.SleekflowCompanyId,
                getUserProfileAuditLogsV2Input.SleekflowUserProfileId,
                getUserProfileAuditLogsV2Input.ContinuationToken,
                getUserProfileAuditLogsV2Input.Filters,
                getUserProfileAuditLogsV2Input.Limit);

        return new GetUserProfileAuditLogsV2Output(
            userProfileAuditLogs,
            nextContinuationToken);
    }
}