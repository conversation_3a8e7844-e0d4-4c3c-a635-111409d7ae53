﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifyWebhookIntegrationRequest
{
    [JsonProperty(PropertyName = "eventTypes")]
    public List<string> EventTypes { get; set; }

    [JsonProperty(PropertyName = "condition")]
    public ApifyWebhookIntegrationRequestCondition Condition { get; set; }

    [JsonProperty(PropertyName = "requestUrl")]
    public string RequestUrl { get; set; }

    [JsonConstructor]
    public ApifyWebhookIntegrationRequest(
        List<string> eventTypes,
        string apifyTaskId,
        string requestUrl)
    {
        EventTypes = eventTypes;
        Condition = new ApifyWebhookIntegrationRequestCondition(apifyTaskId);
        RequestUrl = requestUrl;
    }
}

public class ApifyWebhookIntegrationRequestCondition
{
    [JsonConstructor]
    public ApifyWebhookIntegrationRequestCondition(string actorTaskId)
    {
        ActorTaskId = actorTaskId;
    }

    [JsonProperty(PropertyName = "actorTaskId")]
    public string ActorTaskId { get; set; }
}