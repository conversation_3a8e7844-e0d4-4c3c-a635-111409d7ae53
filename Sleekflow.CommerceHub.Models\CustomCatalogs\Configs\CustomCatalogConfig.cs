using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.CustomCatalogs.Configs;

[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
[ContainerId(ContainerNames.CustomCatalogConfig)]
public class CustomCatalogConfig : AuditEntity
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("count")]
    public int Count { get; set; }

    [JsonProperty("period_start")]
    public DateTimeOffset PeriodStart { get; set; }

    [JsonProperty("period_end")]
    public DateTimeOffset PeriodEnd { get; set; }

    [JsonConstructor]
    public CustomCatalogConfig(
        string id,
        string sleekflowCompanyId,
        string type,
        int count,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        DateTimeOffset periodStart,
        DateTimeOffset periodEnd,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(
            id,
            SysTypeNames.CustomCatalogConfig,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        Type = type;
        Count = count;
        PeriodStart = periodStart;
        PeriodEnd = periodEnd;
    }
}