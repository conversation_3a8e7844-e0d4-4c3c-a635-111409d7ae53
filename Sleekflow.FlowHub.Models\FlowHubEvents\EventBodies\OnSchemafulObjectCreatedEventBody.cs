using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnSchemafulObjectCreatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnSchemafulObjectCreated; }
    }

    [Required]
    [JsonProperty("schemaful_object_id")]
    public string SchemafulObjectId { get; set; }

    [Required]
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    [Required]
    [JsonProperty("primary_property_value")]
    public string PrimaryPropertyValue { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; } // Not named as [SleekflowUserProfileId] to align with other event body

    [Required]
    [JsonProperty("property_values")]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [Required]
    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [JsonConstructor]
    public OnSchemafulObjectCreatedEventBody(
        DateTimeOffset createdAt,
        string schemafulObjectId,
        string schemaId,
        string primaryPropertyValue,
        string contactId,
        Dictionary<string, object?> propertyValues,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
        : base(createdAt)
    {
        SchemafulObjectId = schemafulObjectId;
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        ContactId = contactId;
        PropertyValues = propertyValues;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
    }
}