namespace Sleekflow.Persistence.Abstractions;

public interface IHasSleekflowStaff
{
    public const string PropertyNameSleekflowStaffId = "sleekflow_staff_id";
    public const string PropertyNameSleekflowStaffTeamIds = "sleekflow_staff_team_ids";
    public const string PropertyNameSleekflowStaffIdentityId = "sleekflow_staff_identity_id";

    public string SleekflowStaffId { get; set; }

    public List<string>? SleekflowStaffTeamIds { get; set; }
}