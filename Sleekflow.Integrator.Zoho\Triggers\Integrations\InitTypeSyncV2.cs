﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Subscriptions;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitTypeSyncV2 : ITrigger
{
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoSubscriptionService _zohoSubscriptionService;
    private readonly IZohoConnectionService _zohoConnectionService;

    public InitTypeSyncV2(
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoSubscriptionService zohoSubscriptionService,
        IZohoConnectionService zohoConnectionService)
    {
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoSubscriptionService = zohoSubscriptionService;
        _zohoConnectionService = zohoConnectionService;
    }

    public class InitTypeSyncV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("sync_interval")]
        public int? SyncInterval { get; set; }

        [JsonProperty("is_flows_based")]
        public bool? IsFlowsBased { get; set; }

        [JsonConstructor]
        public InitTypeSyncV2Input(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            int? syncInterval,
            bool? isFlowsBased)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            SyncInterval = syncInterval;
            IsFlowsBased = isFlowsBased;
        }
    }

    public class InitTypeSyncV2Output
    {
    }

    public async Task<InitTypeSyncV2Output> F(
        InitTypeSyncV2Input initTypeSyncV2Input)
    {
        var connection = await _zohoConnectionService.GetByIdAsync(
            initTypeSyncV2Input.ConnectionId,
            initTypeSyncV2Input.SleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                initTypeSyncV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        // This is a special handling of the default value
        // By default, the frontend app sends 7200 to us
        if (initTypeSyncV2Input.SyncInterval == 7200)
        {
            await _zohoSubscriptionService.UpsertAsync(
                initTypeSyncV2Input.EntityTypeName,
                initTypeSyncV2Input.SleekflowCompanyId,
                60 * 10,
                initTypeSyncV2Input.ConnectionId,
                initTypeSyncV2Input.IsFlowsBased ?? true);
        }
        else
        {
            await _zohoSubscriptionService.UpsertAsync(
                initTypeSyncV2Input.EntityTypeName,
                initTypeSyncV2Input.SleekflowCompanyId,
                initTypeSyncV2Input.SyncInterval ?? 60,
                initTypeSyncV2Input.ConnectionId,
                initTypeSyncV2Input.IsFlowsBased ?? true);
        }

        return new InitTypeSyncV2Output();
    }
}