using System.ComponentModel.DataAnnotations;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.CommerceHub.Shopify;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Stores.ShopifyStores;

public static class ShopifyStoreValidator
{
    public static async Task AssertValidShopifyStoreExternalIntegrationConfigAsync(Store store)
    {
        if (store.StoreIntegrationExternalConfig is null)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "This store does not have a StoreIntegrationExternalConfig for Shopify.",
                        new List<string>
                        {
                            nameof(store)
                        })
                });
        }

        if (store.StoreIntegrationExternalConfig.ProviderName is not "shopify")
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Invalid StoreIntegrationExternalConfig for Shopify.",
                        new List<string>
                        {
                            nameof(store)
                        })
                });
        }

        if (store.StoreIntegrationExternalConfig is not
            ShopifyStoreIntegrationExternalConfig shopifyStoreIntegrationExternalConfig)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Invalid StoreIntegrationExternalConfig for Shopify.",
                        new List<string> { nameof(store) })
                });
        }

        if (shopifyStoreIntegrationExternalConfig.ShopifyUrl is null || shopifyStoreIntegrationExternalConfig.AccessToken is null)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "This store does not have a URL or access token.",
                        new List<string>
                        {
                            nameof(store)
                        })
                });
        }

        await ShopifyClientValidator.AssertValidShopifyCredentialAsync(
            shopifyStoreIntegrationExternalConfig.ShopifyUrl,
            shopifyStoreIntegrationExternalConfig.AccessToken);
    }
}