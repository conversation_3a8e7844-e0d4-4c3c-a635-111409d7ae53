using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.Integrator.Dynamics365.Authentications;
using Sleekflow.Mvc.SwaggerConfiguration.Parameter;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Dynamics365.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    private readonly ILogger<PublicController> _logger;
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;
    private readonly IBus _bus;

    public PublicController(
        ILogger<PublicController> logger,
        IDynamics365AuthenticationService dynamics365AuthenticationService,
        IBus bus)
    {
        _logger = logger;
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
        _bus = bus;
    }

    [HttpGet]
    [Route("healthz")]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }

    [HttpGet]
    [Route("AuthenticateCallback")]
    [SwaggerQuery(
        new[]
        {
            "code",
            "state"
        })]
    public async Task<IActionResult> RunAsync()
    {
        var code = HttpContext.Request.Query["code"];
        var encryptedState = HttpContext.Request.Query["state"];

        try
        {
            var (authentication, returnToUrl) =
                await _dynamics365AuthenticationService.HandleAuthenticateCallbackAndStoreAsync(
                    code,
                    encryptedState);
            if (authentication == null)
            {
                throw new Exception("Unable to handle the authentication callback");
            }

            await _bus.Publish(
                new OnProviderInitializedEvent(
                    authentication.SleekflowCompanyId,
                    "d365"),
                context => { context.ConversationId = Guid.Parse(authentication.SleekflowCompanyId); });

            return new ContentResult
            {
                ContentType = "text/html",
                Content =
                    $"Authenticated. You will be redirected back to our main app shortly. Thank you. <meta http-equiv=\"refresh\" content=\"5;URL='{returnToUrl}'\" />"
            };
        }
        catch (Exception exception)
        {
            var gen = RandomStringUtils.Gen(10);

            _logger.LogError(exception, "Caught an exception. requestId {RequestId}", gen);

            return new ContentResult
            {
                ContentType = "text/html",
                Content = $"Still Unauthenticated. Please contact our support team. Thank you. RequestId = {gen}"
            };
        }
    }
}