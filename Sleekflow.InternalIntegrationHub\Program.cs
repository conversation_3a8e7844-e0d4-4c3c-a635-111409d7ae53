using Serilog;
using Sleekflow;
using Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;

#if SWAGGERGEN
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Sleekflow.Mvc.SwaggerConfiguration;
using System.Reflection;
using Microsoft.OpenApi.Models;
using Moq;
#endif

const string appName = "SleekflowInternalIntegrationHub";

MvcModules.BuildLogger(appName);

Log.Information("Starting web host");

var builder = WebApplication.CreateBuilder(args);
builder.Host.UseSerilog();
builder.Services.AddHttpContextAccessor();

if (builder.Environment.IsProduction())
{
    builder.Services.AddApplicationInsightsTelemetry();
}

builder.Services.AddAutoMapper(expression => { expression.AddProfile<CreateEmployeeProfile>(); });
builder.Services.AddAutoMapper(expression => { expression.AddProfile<FullEmployeeInfoProfile>(); });

MvcModules.BuildHealthCheck(builder.Services);
MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
#if SWAGGERGEN
MvcModules.BuildApiBehaviors(builder, list =>
{
    list.AddRange(new List<OpenApiServer>()
    {
        new OpenApiServer()
        {
            Description = "Local",
            Url = $"https://localhost:7074",
        },
        new OpenApiServer()
        {
            Description = "Dev Apigw",
            Url = $"https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/internal-integration-hub/",
        },
        new OpenApiServer()
        {
            Description = "Prod Apigw",
            Url = $"https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub/",
        }
    });
}, Assembly.Load("Sleekflow.InternalIntegrationHub.Models"));
#else
MvcModules.BuildApiBehaviors(builder);
#endif
Modules.BuildHttpClients(builder.Services);
Modules.BuildConfigs(builder.Services);
Modules.BuildServices(builder.Services);
Modules.BuildTriggers(builder.Services);
Modules.BuildServiceBusServices(builder.Services);
Modules.BuildCacheServices(builder.Services);
Modules.BuildDbServices(builder.Services);
Modules.BuildWorkerServices(builder.Services);
MvcModules.BuildFuncServices(builder.Services, appName);
InternalIntegrationHubModules.BuildInternalIntegrationHubDbServices(builder.Services);

#if !SWAGGERGEN

#endif

var app = builder.Build();

#if DEBUG
app.UseCors("DEBUG");
#endif

app.UseAuthorization();
app.MapControllers();
HealthCheckMapping.MapHealthChecks(app);

#if SWAGGERGEN
    app.UseSwagger();
    app.UseSwaggerUI(
        options =>
        {
            var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

            foreach (var description in provider.ApiVersionDescriptions)
            {
                options.SwaggerEndpoint(
                    $"/swagger/{description.GroupName}/swagger.json",
                    description.GroupName.ToUpperInvariant());
            }
        });
#endif

app.Run();

Log.CloseAndFlush();