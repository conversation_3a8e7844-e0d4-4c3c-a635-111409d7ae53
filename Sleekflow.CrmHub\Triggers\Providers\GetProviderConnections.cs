using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderConnections : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderConnections(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderConnectionsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public GetProviderConnectionsInput(
            string sleekflowCompanyId,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
        }
    }

    public class GetProviderConnectionsOutput
    {
        [JsonProperty("connections")]
        public List<ProviderConnectionDto> Connections { get; set; }

        [JsonConstructor]
        public GetProviderConnectionsOutput(
            List<ProviderConnectionDto> connections)
        {
            Connections = connections;
        }
    }

    public async Task<GetProviderConnectionsOutput> F(
        GetProviderConnectionsInput getProviderConnectionsInput)
    {
        var providerService = _providerSelector.GetProviderService(
            getProviderConnectionsInput.ProviderName);

        var output = await providerService.GetProviderConnectionsAsync(
            getProviderConnectionsInput.SleekflowCompanyId);

        return new GetProviderConnectionsOutput(output.Connections);
    }
}