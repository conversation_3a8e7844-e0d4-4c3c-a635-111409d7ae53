using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

public class ApplyElement
{
    [JsonConstructor]
    public ApplyElement(double amount, bool apply, Doc doc)
    {
        Amount = amount;
        Apply = apply;
        Doc = doc;
    }

    [JsonProperty("amount")]
    public double Amount { get; set; }

    [JsonProperty("apply")]
    public bool Apply { get; set; }

    [JsonProperty("doc")]
    public Doc Doc { get; set; }

    [JsonProperty("line", NullValueHandling = NullValueHandling.Ignore)]
    public int? Line { get; set; }
}