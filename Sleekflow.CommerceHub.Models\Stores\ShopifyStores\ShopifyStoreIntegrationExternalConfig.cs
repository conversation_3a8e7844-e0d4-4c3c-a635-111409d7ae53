using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Stores.ShopifyStores;

public class ShopifyStoreIntegrationExternalConfig : StoreIntegrationExternalConfig
{
    [JsonProperty("shopify_url")]
    public string ShopifyUrl { get; set; }

    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("integration_type")]
    public string IntegrationType { get; set; }

    [JsonProperty("integration_status")]
    public string IntegrationStatus { get; set; }

    [JsonProperty("sync_config")]
    public ShopifySyncConfig SyncConfig { get; set; }

    [JsonProperty("payment_config")]
    public ShopifyPaymentConfig PaymentConfig { get; set; }

    [JsonProperty("sync_status")]
    public ShopifySyncStatus? SyncStatus { get; set; }

    [JsonProperty("message_templates")]
    public List<ShopifyMessageTemplate>? MessageTemplates { get; set; }

    [JsonProperty("is_shopify_billing_owner")]
    public bool? IsShopifyBillingOwner { get; set; }

    [JsonProperty("charge_updated_at")]
    public DateTimeOffset? ChargeUpdatedAt { get; set; }

    [JsonProperty("charge_id")]
    public string? ChargeId { get; set; }

    [JsonConstructor]
    public ShopifyStoreIntegrationExternalConfig(
        string shopifyUrl,
        string accessToken,
        string integrationType,
        string integrationStatus,
        ShopifySyncConfig syncConfig,
        ShopifyPaymentConfig paymentConfig,
        ShopifySyncStatus? syncStatus,
        List<ShopifyMessageTemplate>? messageTemplates,
        bool? isShopifyBillingOwner,
        DateTimeOffset? chargeUpdatedAt,
        string? chargeId)
        : base("shopify")
    {
        ShopifyUrl = shopifyUrl;
        AccessToken = accessToken;
        IntegrationType = integrationType;
        IntegrationStatus = integrationStatus;
        SyncConfig = syncConfig;
        PaymentConfig = paymentConfig;
        SyncStatus = syncStatus;
        MessageTemplates = messageTemplates;
        IsShopifyBillingOwner = isShopifyBillingOwner;
        ChargeUpdatedAt = chargeUpdatedAt;
        ChargeId = chargeId;
    }
}