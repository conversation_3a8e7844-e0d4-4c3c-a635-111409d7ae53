﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Newtonsoft.Json;
using Serilog;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Integrator.Configs;
using Sleekflow.FlowHub.Integrator.Https;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;
using ILogger = Serilog.ILogger;

namespace Sleekflow.FlowHub.Integrator.Utils;

public class TravisBackendFunctionsUtil
{
    [SwaggerInclude]
    public class AuthenticateZapierApiKeyInput
    {
        [Required]
        [JsonProperty("api_key")]
        public string ApiKey { get; set; }

        [JsonConstructor]
        public AuthenticateZapierApiKeyInput(string apiKey)
        {
            ApiKey = apiKey;
        }
    }

    [SwaggerInclude]
    public class AuthenticateZapierApiKeyOutput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public AuthenticateZapierApiKeyOutput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    [SwaggerInclude]
    public class GetSchemafulObjectZapierSampleInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("schema_unique_name")]
        public string SchemaUniqueName { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectZapierSampleInput(string schemaUniqueName, string sleekflowCompanyId)
        {
            SchemaUniqueName = schemaUniqueName;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    [SwaggerInclude]
    public class GetSchemafulObjectZapierSampleOutput
    {
        [JsonProperty]
        public object SchemafulObjectZapierViewModel { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectZapierSampleOutput(object schemafulObjectZapierViewModel)
        {
            SchemafulObjectZapierViewModel = schemafulObjectZapierViewModel;
        }
    }

    [SwaggerInclude]
    public class GetSchemaIdByUniqueNameInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("schema_unique_name")]
        public string SchemaUniqueName { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetSchemaIdByUniqueNameInput(string schemaUniqueName, string sleekflowCompanyId)
        {
            SchemaUniqueName = schemaUniqueName;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    [SwaggerInclude]
    public class GetSchemaIdByUniqueNameOutput
    {
        [JsonProperty]
        public string SchemaId { get; set; }

        [JsonConstructor]
        public GetSchemaIdByUniqueNameOutput(string schemaId)
        {
            SchemaId = schemaId;
        }
    }

    private static readonly IAppConfig AppConfig = new AppConfig();
    private static readonly ILogger Logger = Log.Logger.ForContext<TravisBackendFunctionsUtil>();
    private static readonly HttpClient HttpClient = new ();

    public static async Task<TOutput> InvokeAsync<TInput, TOutput>(
        string? sleekflowLocation,
        string functionName,
        TInput input)
    {
        var baseUrl = AppConfig.CoreInternalsEndpoint;

        var url = baseUrl + "/Functions/" + functionName;
        var headers = new Dictionary<string, string?>
        {
            {
                "X-Sleekflow-Flow-Hub-Authorization", InternalsTokenUtils.CreateJwt(AppConfig.CoreInternalsKey)
            },
            {
                "X-Sleekflow-Location", string.IsNullOrWhiteSpace(sleekflowLocation) ? "eastasia" : sleekflowLocation
            }
        };

        var argsJson = JsonConvert.SerializeObject(
            input,
            JsonConfig.DefaultJsonSerializerSettings);

        var resMsg = await HttpPolicies.HttpTransientErrorRetryPolicy
            .ExecuteAsync(
                async () =>
                {
                    var reqMsg = new HttpRequestMessage(
                        HttpMethod.Post,
                        url);

                    foreach (var (key, value) in headers)
                    {
                        reqMsg.Headers.Add(key, value ?? string.Empty);
                    }

                    reqMsg.Content = new StringContent(
                        argsJson,
                        Encoding.UTF8,
                        "application/json");

                    Logger.Information(
                        "SleekflowFunctions: {CommandName} {ArgsJson}",
                        functionName,
                        argsJson);

                    var response = await HttpClient.SendAsync(reqMsg);

                    Logger.Information(
                        "SleekflowFunctions: {CommandName} {ArgsJson} {StatusCode}",
                        functionName,
                        argsJson,
                        response.StatusCode);

                    response.EnsureSuccessStatusCode();

                    return response;
                });

        var responseStr = await resMsg.Content.ReadAsStringAsync();

        Logger.Information(
            "SleekflowFunctions: {CommandName} {ArgsJson} {ResponseStr}",
            functionName,
            argsJson,
            responseStr);

        return JsonConvert.DeserializeObject<TOutput>(responseStr, JsonConfig.DefaultJsonSerializerSettings)!;
    }
}