using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public interface ITelegramMessageBodyCreator : IMessageBodyCreator
{
}

public class TelegramMessageBodyCreator : BaseMessageBodyCreator, ITelegramMessageBodyCreator
{
    public TelegramMessageBodyCreator()
        : base(ChannelTypes.Telegram)
    {
    }

    public override Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args)
    {
        return Task.FromResult((
            CreateBaseMessageBody(
                telegramMessengerMessage: new TelegramMessengerMessageObject(messageStr)),
            "text"));
    }
}