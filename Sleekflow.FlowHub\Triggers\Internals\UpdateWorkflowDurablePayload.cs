using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class UpdateWorkflowDurablePayload
    : ITrigger<UpdateWorkflowDurablePayload.UpdateWorkflowDurablePayloadInput,
        UpdateWorkflowDurablePayload.UpdateWorkflowDurablePayloadOutput>
{
    private readonly IWorkflowService _workflowService;

    public UpdateWorkflowDurablePayload(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class UpdateWorkflowDurablePayloadInput
        : Sleekflow.FlowHub.Models.Internals.UpdateWorkflowDurablePayloadInput
    {
        [JsonConstructor]
        public UpdateWorkflowDurablePayloadInput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            DurablePayload durablePayload)
            : base(sleekflowCompanyId, workflowVersionedId, durablePayload)
        {
        }
    }

    public class UpdateWorkflowDurablePayloadOutput
        : Sleekflow.FlowHub.Models.Internals.UpdateWorkflowDurablePayloadOutput
    {
        [JsonConstructor]
        public UpdateWorkflowDurablePayloadOutput(string workflowVersionedId, bool updateSuccess)
            : base(workflowVersionedId, updateSuccess)
        {
        }
    }

    public async Task<UpdateWorkflowDurablePayloadOutput> F(
        UpdateWorkflowDurablePayloadInput input)
    {
        var proxyWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            input.SleekflowCompanyId,
            input.WorkflowVersionedId);

        if (proxyWorkflow == null)
        {
            throw new ValidationException($"Workflow with versioned ID {input.WorkflowVersionedId} not found.");
        }

        var updateSuccess = await _workflowService.PatchWorkflowAsync(
            input.WorkflowVersionedId,
            input.SleekflowCompanyId,
            new List<PatchOperation>()
            {
                PatchOperation.Set(
                    "/workflow_schedule_settings/durable_payload",
                    input.DurablePayload),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            },
            proxyWorkflow.ETag!) is 1;

        return new UpdateWorkflowDurablePayloadOutput(input.WorkflowVersionedId, updateSuccess);
    }
}