function getId(machineName) {
    const context = getContext();
    const container = context.getCollection();
    const response = getContext().getResponse();

    response.setBody(null);

    const machineNameQuery = {
        query:
            `SELECT *
             FROM root r
             WHERE r.machine_name = @machineName
               AND r.type = @type`,
        parameters: [
            {name: "@machineName", value: machineName},
            {name: "@type", value: 'id'},
        ]
    };
    const isAccepted = container.queryDocuments(
        container.getSelfLink(),
        machineNameQuery,
        undefined,
        function (err, items, responseOptions) {
            if (items.length > 0) {
                response.setBody(items[0].id);

                return;
            }

            const countQuery = {
                query:
                    `SELECT MAX(r.id_int) max
                     FROM root r
                     WHERE r.type = @type`,
                parameters: [
                    {name: "@type", value: 'id'},
                ]
            };

            const isAccepted = container.queryDocuments(
                container.getSelfLink(),
                countQuery,
                undefined,
                function (err, items, responseOptions) {
                    const max = items[0].max ? parseInt(items[0].max) : 0;
                    const newId = (max + 1);

                    const item = {
                        "id": newId + '',
                        "id_int": newId,
                        "machine_name": machineName,
                        "type": "id",
                        "ttl": 3600 * 24 * 120
                    };

                    // Create metadata
                    const isAccepted = container.createDocument(
                        container.getSelfLink(),
                        item,
                        undefined,
                        function (err) {
                            if (err) throw `Unable to create item, abort. Message=[${err.message}].`;
                        });
                    if (!isAccepted) throw "Unable to create item, abort.";

                    response.setBody(item.id);
                });
            if (!isAccepted) throw "Unable to execute countQuery, abort.";
        });
    if (!isAccepted) throw "Unable to execute machineNameQuery, abort.";
}