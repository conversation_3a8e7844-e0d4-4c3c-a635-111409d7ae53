﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.IntegrationObjects;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.GoogleSheets.IntegrationObjects;

public interface IIntegrationObjectService
{
    Task<IntegrationObject?> GetAsync(
        string sleekflowCompanyId,
        string providerName,
        List<TypedId> typedIds,
        string integrationEntityTypeName);

    Task<IntegrationObject> CreateAndGetAsync(
        string sleekflowCompanyId,
        string providerName,
        string integrationEntityTypeName,
        object? data,
        List<TypedId> typedIds,
        DateTimeOffset? lastModificationTime);

    Task<IntegrationObject> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        object data,
        DateTimeOffset lastModificationTime);
}

public class IntegrationObjectService : IScopedService, IIntegrationObjectService
{
    private readonly IIntegrationObjectRepository _integrationObjectRepository;
    private readonly IIdService _idService;

    public IntegrationObjectService(
        IIntegrationObjectRepository integrationObjectRepository,
        IIdService idService)
    {
        _integrationObjectRepository = integrationObjectRepository;
        _idService = idService;
    }

    public async Task<IntegrationObject?> GetAsync(
        string sleekflowCompanyId,
        string providerName,
        List<TypedId> typedIds,
        string integrationEntityTypeName)
    {
        return (await _integrationObjectRepository.GetObjectsAsync(
            o =>
                o.SleekflowCompanyId == sleekflowCompanyId
                && o.ProviderName == providerName
                && o.IntegrationEntityTypeName == integrationEntityTypeName
                && o.TypedIds != null)).Find(o => o.TypedIds!.SequenceEqual(typedIds));
    }

    public async Task<IntegrationObject> CreateAndGetAsync(
        string sleekflowCompanyId,
        string providerName,
        string integrationEntityTypeName,
        object? data,
        List<TypedId> typedIds,
        DateTimeOffset? lastModificationTime)
    {
        return await _integrationObjectRepository.CreateAndGetAsync(
            new IntegrationObject(
                _idService.GetId("IntegrationObject"),
                SysTypeNames.IntegrationObject,
                sleekflowCompanyId,
                providerName,
                integrationEntityTypeName,
                data,
                typedIds,
                lastModificationTime),
            sleekflowCompanyId);
    }

    public async Task<IntegrationObject> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        object data,
        DateTimeOffset lastModificationTime)
    {
        return await _integrationObjectRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>()
            {
                PatchOperation.Set("/data", data),
                PatchOperation.Set("/last_modification_time", lastModificationTime),
            });
    }
}