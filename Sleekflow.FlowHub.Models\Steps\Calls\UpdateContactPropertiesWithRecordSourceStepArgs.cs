﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Common;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactPropertiesWithRecordSourceStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-properties-with-record-source";

    [JsonProperty("contact_record_source_key")]
    public string ContactRecordSourceKey { get; set; }

    [JsonProperty("contact_record_source_value__expr")]
    public string ContactRecordSourceValueExpr { get; set; }

    [JsonProperty("properties__id_expr_set")]
    public HashSet<ContactPropertyIdValuePair> PropertiesIdExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactPropertiesWithRecordSourceStepArgs(
        string contactRecordSourceKey,
        string contactRecordSourceValueExpr,
        HashSet<ContactPropertyIdValuePair> propertiesIdExprSet)
    {
        ContactRecordSourceKey = contactRecordSourceKey;
        ContactRecordSourceValueExpr = contactRecordSourceValueExpr;
        PropertiesIdExprSet = propertiesIdExprSet;
    }
}