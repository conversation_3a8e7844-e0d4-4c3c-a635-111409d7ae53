using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Constants;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using EventHub = Pulumi.AzureNative.EventHub;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using SignalRService = Pulumi.AzureNative.SignalRService;
using Storage = Pulumi.AzureNative.Storage;
using StreamAnalytics = Pulumi.Azure.StreamAnalytics;

namespace Sleekflow.Infras.Components.UserEventHub;

public class UserEventHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly UserEventHubDb.UserEventHubDbOutput _userEventHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly SignalRService.SignalR _signalR;
    private readonly GcpConfig _gcpConfig;

    public UserEventHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        UserEventHubDb.UserEventHubDbOutput userEventHubDbOutput,
        MyConfig myConfig,
        SignalRService.SignalR signalR,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _userEventHubDbOutput = userEventHubDbOutput;
        _myConfig = myConfig;
        _signalR = signalR;
        _gcpConfig = gcpConfig;
    }

    public List<App.ContainerApp> InitUserEventHub()
    {
        var storageRandomId = new Random.RandomId(
            "sleekflow-ueh-storage-account-random-id",
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "Name", $"sleekflow-ueh-storage-account-{_myConfig.Name}"
                    }
                },
            });
        var storageAccount = new Storage.StorageAccount(
            "sleekflow-ueh-storage-account",
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_LRS,
                },
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName", $"sleekflow-ueh-storage-account-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = storageRandomId.Hex.Apply(h => "s" + h),
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });
        var storageAccountDefaultManagementPolicy = new Storage.ManagementPolicy(
            "sleekflow-ueh-storage-account-default-management-policy",
            new ()
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = storageAccount.Name,
                ManagementPolicyName = "default",
                Policy = new Storage.Inputs.ManagementPolicySchemaArgs
                {
                    Rules = new[]
                    {
                        new Storage.Inputs.ManagementPolicyRuleArgs
                        {
                            Definition = new Storage.Inputs.ManagementPolicyDefinitionArgs
                            {
                                Actions = new Storage.Inputs.ManagementPolicyActionArgs
                                {
                                    BaseBlob = new Storage.Inputs.ManagementPolicyBaseBlobArgs
                                    {
                                        Delete = new Storage.Inputs.DateAfterModificationArgs
                                        {
                                            DaysAfterModificationGreaterThan = 7,
                                        },
                                    },
                                },
                                Filters = new Storage.Inputs.ManagementPolicyFilterArgs
                                {
                                    BlobTypes = new List<string>
                                    {
                                        "blockBlob"
                                    }
                                },
                            },
                            Enabled = true,
                            Name = "ttl",
                            Type = "Lifecycle",
                        },
                    },
                },
            });

        var userEventHubRedis = InitUserEventHubRedis();

        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.UserEventHub),
            _myConfig.BuildTime);

        var apps = new List<App.ContainerApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.UserEventHub))
            {
                continue;
            }

            var containerApps = managedEnvAndAppsTuple.ContainerApps;
            var managedEnvironment = managedEnvAndAppsTuple.ManagedEnvironment;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventhub = managedEnvAndAppsTuple.EventHub;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, userEventHubRedis.Name, userEventHubRedis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            var (
                uehAnalyticsStorageAccount,
                uehAnalyticsStorageAccountEventsContainer,
                uehAnalyticsStorageAccountResultsContainer) = InitUehAnalyticsStorageAccount(managedEnvAndAppsTuple);

            var containerAppName = managedEnvAndAppsTuple.FormatContainerAppName(
                ServiceNames.GetShortName(ServiceNames.UserEventHub));

            var containerApp = new App.ContainerApp(
                containerAppName,
                new App.ContainerAppArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ManagedEnvironmentId = managedEnvironment.Id,
                    ContainerAppName = containerAppName,
                    Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                    Configuration = new App.Inputs.ConfigurationArgs
                    {
                        Ingress = new App.Inputs.IngressArgs
                        {
                            External = false,
                            TargetPort = 80,
                            Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                            {
                                new App.Inputs.TrafficWeightArgs
                                {
                                    LatestRevision = true, Weight = 100
                                }
                            },
                        },
                        Registries =
                        {
                            new App.Inputs.RegistryCredentialsArgs
                            {
                                Server = _registry.LoginServer,
                                Username = _registryUsername,
                                PasswordSecretRef = "registry-password-secret",
                            }
                        },
                        Secrets =
                        {
                            new App.Inputs.SecretArgs
                            {
                                Name = "registry-password-secret", Value = _registryPassword
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "service-bus-keda-conn-str-secret",
                                Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                            },
                            new App.Inputs.SecretArgs
                            {
                                Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                            },
                        },
                        ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                    },
                    Template = new App.Inputs.TemplateArgs
                    {
                        TerminationGracePeriodSeconds = 5 * 60,
                        Scale = new App.Inputs.ScaleArgs
                        {
                            MinReplicas = _myConfig.Name.ToLower() == "production" ? 3 : 1,
                            MaxReplicas = 20,
                            Rules = new InputList<App.Inputs.ScaleRuleArgs>
                            {
                                new App.Inputs.ScaleRuleArgs
                                {
                                    Name = "http",
                                    Http = new App.Inputs.HttpScaleRuleArgs
                                    {
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "concurrentRequests", "300"
                                            }
                                        }
                                    }
                                }
                            }
                        },
                        Containers =
                        {
                            new App.Inputs.ContainerArgs
                            {
                                Name = "sleekflow-ueh-app",
                                Image = myImage.BaseImageName,
                                Resources = new App.Inputs.ContainerResourcesArgs
                                {
                                    Cpu = 2.0, Memory = "4.0Gi"
                                },
                                Probes = new List<App.Inputs.ContainerAppProbeArgs>
                                {
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "liveness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "readiness",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 8,
                                        TimeoutSeconds = 8,
                                        PeriodSeconds = 2,
                                    },
                                    new App.Inputs.ContainerAppProbeArgs
                                    {
                                        Type = "startup",
                                        HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                        {
                                            Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                        },
                                        InitialDelaySeconds = 12,
                                        TimeoutSeconds = 8,
                                    }
                                },
                                Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                    new List<App.Inputs.EnvironmentVarArgs>
                                    {
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                            Value = managedEnvAndAppsTuple.InsightsComponent.ConnectionString,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SF_ENVIRONMENT",
                                            Value = managedEnvAndAppsTuple.FormatSfEnvironment(),
                                        },

                                        #region AuditHubDbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_USER_EVENT_HUB_DB_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_userEventHubDbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_USER_EVENT_HUB_DB_KEY",
                                            Value = _userEventHubDbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_USER_EVENT_HUB_DB_DATABASE_ID",
                                            Value = _userEventHubDbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region CacheConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "CACHE_PREFIX", Value = "Sleekflow.UserEventHub",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "REDIS_CONN_STR",
                                            Value = Output
                                                .Tuple(listRedisKeysOutput, userEventHubRedis.HostName)
                                                .Apply(
                                                    t =>
                                                        $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                        },

                                        #endregion

                                        #region DbConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_ENDPOINT",
                                            Value = Output.Format(
                                                $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                        },

                                        #endregion

                                        #region ServiceBusConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                        },

                                        #endregion

                                        #region EventHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                        },

                                        #endregion

                                        #region LoggerConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_AUTHENTICATION_ID",
                                            Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON",
                                            Value = _gcpConfig.CredentialJson,
                                        },

                                        #endregion

                                        #region MassTransitStorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONN_STR",
                                            Value = massTransitBlobStorage.StorageAccountConnStr
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_DATA_CONTAINER_NAME",
                                            Value = massTransitBlobStorage.ContainerName
                                        },

                                        #endregion

                                        #region SignalRConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SIGNAL_R_CONN_STR",
                                            Value = Output
                                                .Tuple(_resourceGroup.Name, _signalR.Name)
                                                .Apply(
                                                    t =>
                                                        SignalRService.ListSignalRKeys.InvokeAsync(
                                                            new SignalRService.ListSignalRKeysArgs
                                                            {
                                                                ResourceGroupName = t.Item1, ResourceName = t.Item2,
                                                            }))
                                                .Apply(r => r.PrimaryConnectionString!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SIGNAL_R_PRIMARY_KEY",
                                            Value = Output
                                                .Tuple(_resourceGroup.Name, _signalR.Name)
                                                .Apply(
                                                    t =>
                                                        SignalRService.ListSignalRKeys.InvokeAsync(
                                                            new SignalRService.ListSignalRKeysArgs
                                                            {
                                                                ResourceGroupName = t.Item1, ResourceName = t.Item2,
                                                            }))
                                                .Apply(r => r.PrimaryKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SIGNAL_R_SECONDARY_KEY",
                                            Value = Output
                                                .Tuple(_resourceGroup.Name, _signalR.Name)
                                                .Apply(
                                                    t =>
                                                        SignalRService.ListSignalRKeys.InvokeAsync(
                                                            new SignalRService.ListSignalRKeysArgs
                                                            {
                                                                ResourceGroupName = t.Item1, ResourceName = t.Item2,
                                                            }))
                                                .Apply(r => r.SecondaryKey!),
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SIGNAL_R_SESSION_DURATION_IN_SECONDS",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? "300"
                                                : "60",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "SIGNAL_R_RANDOM_ERROR_RATE",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? "0"
                                                : "10",
                                        },

                                        #endregion

                                        #region StorageConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "MESSAGE_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                storageAccount.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ANALYTICS_STORAGE_CONN_STR",
                                            Value = StorageUtils.GetConnectionString(
                                                _resourceGroup.Name,
                                                uehAnalyticsStorageAccount.Name)
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ANALYTICS_STORAGE_ACCOUNT_NAME",
                                            Value = uehAnalyticsStorageAccount.Name
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "ANALYTICS_STORAGE_ACCOUNT_KEY",
                                            Value = Storage.ListStorageAccountKeys
                                                .Invoke(
                                                    new Storage.ListStorageAccountKeysInvokeArgs
                                                    {
                                                        ResourceGroupName = _resourceGroup.Name,
                                                        AccountName = uehAnalyticsStorageAccount.Name
                                                    })
                                                .Apply(
                                                    keys =>
                                                    {
                                                        var primaryStorageKey = keys.Keys[0].Value;

                                                        // Build the connection string to the storage account.
                                                        return primaryStorageKey;
                                                    })
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "RESULTS_CONTAINER_NAME",
                                            Value = uehAnalyticsStorageAccountResultsContainer.Name
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "EVENTS_CONTAINER_NAME",
                                            Value = uehAnalyticsStorageAccountEventsContainer.Name
                                        },

                                        #endregion

                                        #region NotificationHubConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NOTIFICATION_HUB_CONNECTION_STRING",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? "Endpoint=sb://SleekflowProduction.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=pbzIBQQoNB8rGgrHdt1vmCIuTIB/Aaey5iION3eiCbQ="
                                                : "Endpoint=sb://sleekflowtesting.servicebus.windows.net/;SharedAccessKeyName=DefaultFullSharedAccessSignature;SharedAccessKey=l5XR33aDeefA79lau7IjY252oJRpdB/h4pP6v3F6hVY=",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "NOTIFICATION_HUB_NAME",
                                            Value = _myConfig.Name.ToLower() == "production"
                                                ? "sleekflowproduction"
                                                : "sleekflowTesting",
                                        },

                                        #endregion

                                        #region ApplicationInsightTelemetryConfig

                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "TRUE",
                                        },
                                        new App.Inputs.EnvironmentVarArgs
                                        {
                                            Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                        },

                                        #endregion
                                    })
                            }
                        }
                    }
                },
                new CustomResourceOptions
                {
                    Parent = managedEnvironment
                });

            InitStreamingJob(
                managedEnvAndAppsTuple,
                uehAnalyticsStorageAccount,
                uehAnalyticsStorageAccountEventsContainer);

            new UserEventAnalyticsHub(
                    _registry,
                    _registryUsername,
                    _registryPassword,
                    _resourceGroup,
                    managedEnvAndAppsTuple,
                    _dbOutput,
                    _userEventHubDbOutput,
                    _myConfig,
                    _gcpConfig)
                .InitUserEventAnalyticsHub(
                    userEventHubRedis,
                    uehAnalyticsStorageAccount,
                    uehAnalyticsStorageAccountEventsContainer,
                    uehAnalyticsStorageAccountResultsContainer);

            containerApps.Add(ServiceNames.UserEventHub, containerApp);
            apps.Add(containerApp);
        }

        return apps;
    }

    private (
        Storage.StorageAccount UehAnalyticsStorageAccount,
        Storage.BlobContainer UehAnalyticsStorageAccountEventsContainer,
        Storage.BlobContainer UehAnalyticsStorageAccountResultsContainer) InitUehAnalyticsStorageAccount(
            ManagedEnvAndAppsTuple managedEnvAndAppsTuple)
    {
        var uehAnalyticsStorageAccountRandomId = new Random.RandomId(
            managedEnvAndAppsTuple.FormatResourceName($"sleekflow-ueh-analytics-storage-account-random-id"),
            new Random.RandomIdArgs
            {
                ByteLength = 4,
                Keepers =
                {
                    {
                        "Name",
                        $"{managedEnvAndAppsTuple.FormatResourceName($"sleekflow-ueh-analytics-storage-account-random-id")}-{_myConfig.Name}"
                    }
                },
            });
        var uehAnalyticsStorageAccount = new Storage.StorageAccount(
            managedEnvAndAppsTuple.FormatResourceName($"sleekflow-ueh-analytics-storage-account"),
            new Storage.StorageAccountArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Sku = new Storage.Inputs.SkuArgs
                {
                    Name = Storage.SkuName.Standard_RAGRS,
                },
                Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                Tags = new InputMap<string>
                {
                    {
                        "Environment", _myConfig.Name
                    },
                    {
                        "StorageAccountName",
                        $"{managedEnvAndAppsTuple.FormatResourceName($"sleekflow-ueh-analytics-storage-account")}-{_myConfig.Name}"
                    }
                },
                Kind = Storage.Kind.StorageV2,
                AccountName = uehAnalyticsStorageAccountRandomId.Hex.Apply(h => "s" + h),

                // Properties
                DnsEndpointType = "Standard",
                DefaultToOAuthAuthentication = false,
                PublicNetworkAccess = "Enabled",
                AllowCrossTenantReplication = false,
                IsSftpEnabled = false,
                MinimumTlsVersion = "TLS1_2",
                AllowBlobPublicAccess = false,
                AllowSharedKeyAccess = true,
                LargeFileSharesState = "Enabled",
                IsHnsEnabled = true,
                NetworkRuleSet = new Storage.Inputs.NetworkRuleSetArgs
                {
                    Bypass = "AzureServices", DefaultAction = Storage.DefaultAction.Allow,
                },
                Encryption = new Storage.Inputs.EncryptionArgs
                {
                    RequireInfrastructureEncryption = false,
                    Services = new Storage.Inputs.EncryptionServicesArgs
                    {
                        Blob = new Storage.Inputs.EncryptionServiceArgs
                        {
                            KeyType = "Account", Enabled = true
                        }
                    },
                    KeySource = Storage.KeySource.Microsoft_Storage
                },
                AccessTier = Storage.AccessTier.Hot
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var blobService = new Storage.BlobServiceProperties(
            managedEnvAndAppsTuple.FormatResourceName("sleekflow-ueh-analytics-storage-account-blob-service"),
            new Storage.BlobServicePropertiesArgs
            {
                AccountName = uehAnalyticsStorageAccount.Name,
                BlobServicesName = "default",
                ResourceGroupName = _resourceGroup.Name,
                DeleteRetentionPolicy = new Storage.Inputs.DeleteRetentionPolicyArgs
                {
                    AllowPermanentDelete = false, Enabled = true, Days = 7
                },
                ContainerDeleteRetentionPolicy = new Storage.Inputs.DeleteRetentionPolicyArgs()
                {
                    Enabled = true, Days = 7
                }
            });

        var uehAnalyticsStorageAccountEventsContainer = new Storage.BlobContainer(
            managedEnvAndAppsTuple.FormatResourceName("sleekflow-ueh-analytics-storage-account-events-container"),
            new Storage.BlobContainerArgs
            {
                AccountName = uehAnalyticsStorageAccount.Name,
                ContainerName = "events",
                ResourceGroupName = _resourceGroup.Name,
                PublicAccess = Storage.PublicAccess.None,
                DefaultEncryptionScope = "$account-encryption-key",
                DenyEncryptionScopeOverride = false
            });

        var uehAnalyticsStorageAccountResultsContainer = new Storage.BlobContainer(
            managedEnvAndAppsTuple.FormatResourceName("sleekflow-ueh-analytics-storage-account-results-container"),
            new Storage.BlobContainerArgs
            {
                AccountName = uehAnalyticsStorageAccount.Name,
                ContainerName = "results",
                ResourceGroupName = _resourceGroup.Name,
                PublicAccess = Storage.PublicAccess.None,
                DefaultEncryptionScope = "$account-encryption-key",
                DenyEncryptionScopeOverride = false
            });
        return (uehAnalyticsStorageAccount, uehAnalyticsStorageAccountEventsContainer,
            uehAnalyticsStorageAccountResultsContainer);
    }

    private Cache.Redis InitUserEventHubRedis()
    {
        var redis = new Cache.Redis(
            "sleekflow-ueh-redis",
            new Cache.RedisArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = _resourceGroup.Location,
                Sku = new Cache.Inputs.SkuArgs
                {
                    Capacity = _myConfig.Name == "production" ? 1 : 0,
                    Family = _myConfig.Name == "production" ? "P" : "C",
                    Name = _myConfig.Name == "production" ? "Premium" : "Standard",
                },
                RedisConfiguration = new Cache.Inputs.RedisCommonPropertiesRedisConfigurationArgs(),
                EnableNonSslPort = false,
                RedisVersion = "6",
            });

        return redis;
    }

    private void InitStreamingJob(
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Storage.StorageAccount uehAnalyticsStorageAccount,
        Storage.BlobContainer uehAnalyticsStorageAccountEventsContainer)
    {
        var eventHub = managedEnvAndAppsTuple.EventHub;

        var streamingJobConsumerGroupName = "streaming-job";
        var streamingJobConsumerGroup = new EventHub.ConsumerGroup(
            eventHub.ConstructConsumerGroupName(
                $"{EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent}-{streamingJobConsumerGroupName}"),
            new EventHub.ConsumerGroupArgs
            {
                ConsumerGroupName =
                    $"{EventHubNames.SleekflowOnAuditLogRequestedEventHubEvent}-{streamingJobConsumerGroupName}",
                EventHubName = eventHub.OnUserEventHappenedEventHub.Name,
                NamespaceName = eventHub.Namespace.Name,
                ResourceGroupName = _resourceGroup.Name,
            },
            new CustomResourceOptions
            {
                Parent = eventHub.OnUserEventHappenedEventHub
            });

        var streamingJob = new StreamAnalytics.Job(
            managedEnvAndAppsTuple.FormatResourceName(
                $"{EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent}-streaming-job"),
            new ()
            {
                CompatibilityLevel = "1.2",
                DataLocale = "en-US",
                EventsLateArrivalMaxDelayInSeconds = 5,
                EventsOutOfOrderMaxDelayInSeconds = 0,
                EventsOutOfOrderPolicy = "Adjust",
                Name = EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent + "-streaming-job",
                Type = "Cloud",
                Location = LocationNames.GetAzureLocation(managedEnvAndAppsTuple.LocationName),
                OutputErrorPolicy = "Stop",
                ResourceGroupName = _resourceGroup.Name,
                SkuName = "StandardV2",
                TransformationQuery =
                    """
                    /*
                    Here are links to help you get started with Stream Analytics Query Language:
                    Common query patterns - https://go.microsoft.com/fwLink/?LinkID=619153
                    Query language - https://docs.microsoft.com/stream-analytics-query/query-language-elements-azure-stream-analytics
                    */
                    WITH InputData AS (
                        SELECT
                            TRY_CAST(input.[message] AS Record) AS [message],

                            [message].[id] AS [id],
                            [message].[eventType] AS [eventType],
                            [message].[sleekflowCompanyId] AS [sleekflowCompanyId],
                            [message].[objectId] AS [objectId],
                            [message].[objectType] AS [objectType],
                            [message].[source] AS [source],

                            -- Store 'properties' and 'metadata' as JSON strings
                            TRY_CAST([message].properties AS Record) AS [properties],
                            TRY_CAST([message].metadata AS Record) AS [metadata],

                            DATEDIFF(millisecond, '1970-01-01T00:00:00Z', input.[EventEnqueuedUtcTime]) AS [timestamp]
                        FROM [on-user-event-happened-event] AS input
                        TIMESTAMP BY input.[EventEnqueuedUtcTime]
                    )

                    SELECT
                        [id],
                        [eventType],
                        [sleekflowCompanyId],
                        [objectId],
                        [objectType],
                        [source],
                        [properties],
                        [metadata],
                        [timestamp]
                    INTO [onusereventhappenedevent-events]
                    FROM InputData
                    """,
                StreamingUnits = 3
            });

        var streamingJobInput = new StreamAnalytics.StreamInputEventHub(
            managedEnvAndAppsTuple.FormatResourceName(
                $"{EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent}-streaming-job-input"),
            new StreamAnalytics.StreamInputEventHubArgs
            {
                Name = "on-user-event-happened-event",
                StreamAnalyticsJobName = streamingJob.Name,
                ResourceGroupName = _resourceGroup.Name,
                EventhubName = "on-user-event-happened-event",
                ServicebusNamespace = eventHub.Namespace.Name,
                SharedAccessPolicyName = eventHub.Key.Apply(k => k.KeyName),
                SharedAccessPolicyKey = eventHub.Key.Apply(k => k.PrimaryKey),
                EventhubConsumerGroupName = streamingJobConsumerGroup.Name,
                Serialization = new StreamAnalytics.Inputs.StreamInputEventHubSerializationArgs()
                {
                    Encoding = "UTF8", Type = "Json"
                },
            });

        var storageAccountKeys = Storage.ListStorageAccountKeys.Invoke(
            new Storage.ListStorageAccountKeysInvokeArgs
            {
                ResourceGroupName = _resourceGroup.Name, AccountName = uehAnalyticsStorageAccount.Name
            });

        var streamingJobOutput = new StreamAnalytics.OutputBlob(
            managedEnvAndAppsTuple.FormatResourceName(
                $"{EventHubNames.UserEventHubOnUserEventHappenedEventHubEvent}-streaming-job-output"),
            new StreamAnalytics.OutputBlobArgs
            {
                Name = "onusereventhappenedevent-events",
                StreamAnalyticsJobName = streamingJob.Name,
                ResourceGroupName = _resourceGroup.Name,
                StorageAccountName = uehAnalyticsStorageAccount.Name,
                StorageAccountKey = storageAccountKeys.Apply(
                    keys =>
                    {
                        var primaryStorageKey = keys.Keys[0].Value;

                        // Build the connection string to the storage account.
                        return primaryStorageKey;
                    }),
                StorageContainerName = uehAnalyticsStorageAccountEventsContainer.Name,
                PathPattern =
                    "sleekflowCompanyId={sleekflowCompanyId}/year={datetime:yyyy}/month={datetime:MM}/day={datetime:dd}/hour={datetime:HH}/",
                DateFormat = "yyyy/MM/dd",
                TimeFormat = "HH",
                Serialization = new StreamAnalytics.Inputs.OutputBlobSerializationArgs
                {
                    Type = "Parquet"
                },
                BatchMinRows = 500,
                BatchMaxWaitTime = "00:05:00",
                AuthenticationMode = "ConnectionString",
                BlobWriteMode = "Append",
            });
    }
}
