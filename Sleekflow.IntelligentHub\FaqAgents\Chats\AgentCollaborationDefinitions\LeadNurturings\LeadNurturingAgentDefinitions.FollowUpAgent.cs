using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public partial class LeadNurturingAgentDefinitions
{
    [method: JsonConstructor]
    public class FollowUpAgentResponse(
        string agentName,
        string reasoning,
        bool shouldFollowup,
        int? followupDelayMinutes = null,
        string? followupType = null,
        string? followupMessage = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("should_followup")]
        public bool ShouldFollowup { get; set; } = shouldFollowup;

        [JsonProperty("followup_delay_minutes")]
        public int? FollowupDelayMinutes { get; set; } = followupDelayMinutes;

        [JsonProperty("followup_type")]
        public string? FollowupType { get; set; } = followupType;

        [JsonProperty("followup_message")]
        public string? FollowupMessage { get; set; } = followupMessage;
    }

    public ChatCompletionAgent GetFollowUpAgent(
        Kernel kernel,
        PromptExecutionSettings settings)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("should_followup", "boolean"),
                new PromptExecutionSettingsUtils.Property("followup_delay_minutes", "integer"),
                new PromptExecutionSettingsUtils.Property("followup_type", "string", true),
                new PromptExecutionSettingsUtils.Property("followup_message", "string", true)
            ]);

        var instructions =
            $$"""
              {{GetSharedSystemPrompt()}}
              You are the FollowUpAgent. Your job is to determine if a follow-up message should be sent to the customer if they haven't replied, how long to wait (in minutes, within 1 hour), and what the message should be.
              Analyze the internal conversation context to craft an appropriate follow-up message.

              Output a JSON object with:
              - agent_name: 'FollowUpAgent'
              - reasoning: your reasoning for whether a follow-up is needed and the delay.
              - should_followup: true/false
              - followup_delay_minutes: integer between 0 and 60. Only set if should_followup is true.
              - followup_type: e.g., 'reminder', 'info_request', 'gentle_nudge'. Only set if should_followup is true.
              - followup_message: the message to send. Only set if should_followup is true.

              Only set should_followup to true if a follow-up is appropriate based on the conversation flow and lack of customer response.
              The followup_message should be generated based on the internal group context to ensure it is relevant and continues the conversation naturally.
              """;

        return new ChatCompletionAgent
        {
            Name = "FollowUpAgent",
            Description =
                "Determines if a follow-up message should be sent to inactive customers, calculates appropriate delay timing, and crafts contextual follow-up messages based on conversation history.",
            HistoryReducer = new GeminiChatHistoryReducer(),
            Instructions = instructions,
            Kernel = kernel,
            Arguments = new KernelArguments(settings)
        };
    }
}