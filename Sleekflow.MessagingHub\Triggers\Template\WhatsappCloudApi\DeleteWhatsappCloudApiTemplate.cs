using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Templates;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Template.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Templates)]
public class DeleteWhatsappCloudApiTemplate
    : ITrigger<
        DeleteWhatsappCloudApiTemplate.DeleteWhatsappCloudApiTemplateInput,
        DeleteWhatsappCloudApiTemplate.DeleteWhatsappCloudApiTemplateOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ITemplateService _templateService;
    private readonly ILogger<DeleteWhatsappCloudApiTemplate> _logger;

    public DeleteWhatsappCloudApiTemplate(
        IWabaService wabaService,
        ITemplateService templateService,
        ILogger<DeleteWhatsappCloudApiTemplate> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _templateService = templateService;
    }

    public class DeleteWhatsappCloudApiTemplateInput
    {
        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("template_name")]
        public string TemplateName { get; set; }

        [JsonConstructor]
        public DeleteWhatsappCloudApiTemplateInput(string wabaId, string sleekflowCompanyId, string templateName)
        {
            WabaId = wabaId;
            SleekflowCompanyId = sleekflowCompanyId;
            TemplateName = templateName;
        }
    }

    public class DeleteWhatsappCloudApiTemplateOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public DeleteWhatsappCloudApiTemplateOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<DeleteWhatsappCloudApiTemplateOutput> F(
        DeleteWhatsappCloudApiTemplateInput deleteWhatsappCloudApiTemplateInput)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(
            deleteWhatsappCloudApiTemplateInput.WabaId,
            deleteWhatsappCloudApiTemplateInput.SleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        return new DeleteWhatsappCloudApiTemplateOutput(
            await _templateService.DeleteCloudApiWhatsappTemplateAsync(
                waba.FacebookWabaId,
                deleteWhatsappCloudApiTemplateInput.TemplateName,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null));
    }
}