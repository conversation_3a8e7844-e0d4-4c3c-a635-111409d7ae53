using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.TravisBackend;

public class Company
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("company_name")]
    public string CompanyName { get; set; }

    [JsonProperty("company_country")]
    public string CompanyCountry { get; set; }

    [JsonProperty("cms_activation_owner_id")]
    public string CmsActivationOwnerId { get; set; }

    [JsonConstructor]
    public Company(string id, string companyName, string companyCountry, string cmsActivationOwnerId)
    {
        Id = id;
        CompanyName = companyName;
        CompanyCountry = companyCountry;
        CmsActivationOwnerId = cmsActivationOwnerId;
    }
}