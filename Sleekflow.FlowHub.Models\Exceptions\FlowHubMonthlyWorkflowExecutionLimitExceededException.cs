﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Exceptions;

public class FlowHubMonthlyWorkflowExecutionLimitExceededException : Exception
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonConstructor]
    public FlowHubMonthlyWorkflowExecutionLimitExceededException(
        string sleekflowCompanyId,
        string stateId)
        : base($"Monthly workflow execution limit exceeded for company {sleekflowCompanyId}. (State Id: {stateId})")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
    }
}