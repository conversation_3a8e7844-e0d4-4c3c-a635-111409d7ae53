using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

public class CreditTransferTransactionLogDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("unique_id")]
    public string UniqueId { get; set; }

    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("transaction_type")]
    public string TransactionType { get; set; }

    [JsonProperty("is_calculated")]
    public bool IsCalculated { get; set; }

    [JsonProperty("calculation_status")]
    public string? CalculationStatus { get; set; }

    [JsonProperty("credit_transfer_from_to")]
    public CreditTransferFromTo CreditTransferFromTo { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string ETag { get; set; }

    [JsonConstructor]
    public CreditTransferTransactionLogDto(
        string id,
        string uniqueId,
        string facebookWabaId,
        string facebookBusinessId,
        string transactionType,
        bool isCalculated,
        CreditTransferFromTo creditTransferFromTo,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string eTag,
        string? calculationStatus = null)
    {
        Id = id;
        UniqueId = uniqueId;
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        TransactionType = transactionType;
        IsCalculated = isCalculated;
        CreditTransferFromTo = creditTransferFromTo;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        ETag = eTag;
        CalculationStatus = calculationStatus;
    }

    public CreditTransferTransactionLogDto(BusinessBalanceTransactionLog businessBalanceTransactionLog)
        : this(
            businessBalanceTransactionLog.Id,
            businessBalanceTransactionLog.UniqueId,
            businessBalanceTransactionLog.FacebookWabaId!,
            businessBalanceTransactionLog.FacebookBusinessId,
            businessBalanceTransactionLog.TransactionType,
            businessBalanceTransactionLog.IsCalculated,
            businessBalanceTransactionLog.CreditTransferFromTo!,
            businessBalanceTransactionLog.CreatedAt,
            businessBalanceTransactionLog.UpdatedAt,
            businessBalanceTransactionLog.ETag!,
            businessBalanceTransactionLog.CalculationStatus)
    {
    }
}