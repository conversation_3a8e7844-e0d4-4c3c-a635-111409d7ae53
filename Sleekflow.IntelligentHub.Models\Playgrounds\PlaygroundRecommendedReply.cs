﻿using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Models.Playgrounds;

public class PlaygroundRecommendedReply
{
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("conversation_context")]
    public List<SfChatEntry> ConversationContext { get; set; }

    [JsonProperty("recommended_reply")]
    public string RecommendedReply { get; set; }

    [JsonConstructor]
    public PlaygroundRecommendedReply(
        string messageId,
        List<SfChatEntry> conversationContext,
        string recommendedReply)
    {
        MessageId = messageId;
        ConversationContext = conversationContext;
        RecommendedReply = recommendedReply;
    }
}