using GraphApi.Client.Models.WebhookObjects;
using MassTransit;
using Newtonsoft.Json;
using Slack.Webhooks;
using Sleekflow.JsonConfigs;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;
using Sleekflow.MessagingHub.Webhooks;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.OpenTelemetry.MessagingHub;
using Sleekflow.OpenTelemetry.MessagingHub.MeterNames;
using Sleekflow.OpenTelemetry.MessagingHub.MeterTags;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiHandleMetaDetectedOutcomesEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiHandleMetaDetectedOutcomeWebhookEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiHandleMetaDetectedOutcomeWebhookEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromSeconds(30);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiHandleMetaDetectedOutcomeWebhookEventConsumer : IConsumer<OnCloudApiHandleMetaDetectedOutcomeWebhookEvent>
{
    private const int DefaultMaxRetryCount = 5;
    private readonly ILogger<OnCloudApiHandleMetaDetectedOutcomeWebhookEventConsumer> _logger;
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;
    private readonly IWabaService _wabaService;
    private readonly ISlackIncomingWebhooksConfig _slackIncomingWebhooksConfig;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IMessagingHubMeters _messagingHubMeters;

    public OnCloudApiHandleMetaDetectedOutcomeWebhookEventConsumer(
        ILogger<OnCloudApiHandleMetaDetectedOutcomeWebhookEventConsumer> logger,
        IMessagingHubWebhookService messagingHubWebhookService,
        IWabaService wabaService,
        ISlackIncomingWebhooksConfig slackIncomingWebhooksConfig,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IMessagingHubMeters messagingHubMeters)
    {
        _logger = logger;
        _messagingHubWebhookService = messagingHubWebhookService;
        _wabaService = wabaService;
        _slackIncomingWebhooksConfig = slackIncomingWebhooksConfig;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _messagingHubMeters = messagingHubMeters;
    }

    public async Task Consume(ConsumeContext<OnCloudApiHandleMetaDetectedOutcomeWebhookEvent> context)
    {
        var onCloudApiHandleMetaDetectedOutcomeWebhookEvent = context.Message;
        var retryCount = context.GetRedeliveryCount();
        var maxRetryCount = DefaultMaxRetryCount;

        try
        {
            var status = await HandleCloudApiMetaDetectedOutcomeWebhookEventAsync(onCloudApiHandleMetaDetectedOutcomeWebhookEvent);

            _logger.LogInformation(
                "Handled the detected outcome webhook from Cloud Api {Change}/{Status}. Entry:{Entry}, WebhookEvent{WebhookEvent}.",
                JsonConvert.SerializeObject(
                    onCloudApiHandleMetaDetectedOutcomeWebhookEvent.Change,
                    JsonConfig.DefaultJsonSerializerSettings),
                status,
                JsonConvert.SerializeObject(
                    onCloudApiHandleMetaDetectedOutcomeWebhookEvent.Entry,
                    JsonConfig.DefaultJsonSerializerSettings),
                JsonConvert.SerializeObject(
                    onCloudApiHandleMetaDetectedOutcomeWebhookEvent.WebhookMessages,
                    JsonConfig.DefaultJsonSerializerSettings));
        }
        catch (Exception e)
        {
            await HandleCloudApiMetaDetectedOutcomeWebhookEventFailureAsync(
                context,
                onCloudApiHandleMetaDetectedOutcomeWebhookEvent,
                e,
                retryCount,
                maxRetryCount);
        }
    }

    private async ValueTask<bool> HandleCloudApiMetaDetectedOutcomeWebhookEventAsync(
        OnCloudApiHandleMetaDetectedOutcomeWebhookEvent onCloudApiHandleMetaDetectedOutcomeWebhookEvent)
    {
        var wabaId = onCloudApiHandleMetaDetectedOutcomeWebhookEvent.WabaId;
        var whatsappCloudApiChange = onCloudApiHandleMetaDetectedOutcomeWebhookEvent.Change;

        var value =
            JsonConvert.DeserializeObject<CloudApiWebhookValueObject>(
                JsonConvert.SerializeObject(whatsappCloudApiChange.Value));

        if (value == null)
        {
            return false;
        }

        var waba =
            await _wabaService.GetWabaWithFacebookWabaIdAndFacebookPhoneNumberIdAsync(
                wabaId,
                value.Metadata.PhoneNumberId);

        if (waba == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(TraceEventNames.WhatsappCloudApiWebhookDropped);
            return false;
        }

        var wabaPhoneNumberConfig =
            waba.WabaPhoneNumbers.FirstOrDefault(
                c =>
                    c.FacebookPhoneNumberId == value.Metadata.PhoneNumberId);

        if (wabaPhoneNumberConfig?.SleekflowCompanyId == null || wabaPhoneNumberConfig.WebhookUrl == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(TraceEventNames.WhatsappCloudApiWebhookDropped);
            _messagingHubMeters.IncrementCounter(MessagingHubChannelMeterNames.WhatsappCloudApi, MessagingHubWebhookMeterOptions.MessagesWebhookDropped);

            return false;
        }

        if (_messagingHubWebhookService.IsSendCloudApiWebhookAsExpressWebhooks(
                wabaPhoneNumberConfig.SleekflowCompanyId,
                value))
        {
            // Enqueue Express Webhook for incoming messages
            await _messagingHubWebhookService.SendExpressWebhooksAsync(
                wabaPhoneNumberConfig.SleekflowCompanyId,
                WebhookEntityTypeNames.WabaPhoneNumber,
                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                new WhatsappCloudApiWebhookTravisMessage(
                    waba.Id,
                    wabaPhoneNumberConfig.Id,
                    value),
                wabaPhoneNumberConfig.Id);
        }
        else
        {
            await _messagingHubWebhookService.SendWebhooksAsync(
                wabaPhoneNumberConfig.SleekflowCompanyId,
                WebhookEntityTypeNames.WabaPhoneNumber,
                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                new WhatsappCloudApiWebhookTravisMessage(
                    waba.Id,
                    wabaPhoneNumberConfig.Id,
                    value),
                wabaPhoneNumberConfig.Id);
        }

        TraceMessageWebhookDeliveredEvent(value);
        IncrementMessageWebhookDeliveredCounter(wabaPhoneNumberConfig.WebhookUrl, value);

        return true;
    }

    private async Task HandleCloudApiMetaDetectedOutcomeWebhookEventFailureAsync(
        ConsumeContext<OnCloudApiHandleMetaDetectedOutcomeWebhookEvent> context,
        OnCloudApiHandleMetaDetectedOutcomeWebhookEvent onCloudApiHandleMetaDetectedOutcomeWebhookEvent,
        Exception e,
        int retryCount,
        int maxRetryCount)
    {
        if (retryCount < maxRetryCount)
        {
            _logger.LogError(
                e,
                "Unable to handle the detected outcome webhook from Cloud Api, scheduling retry. WabaId: {WabaId}, Change: {Change}, Webhook Event: {WebhookEvent}, Retry Attempts: {RetryCount}",
                onCloudApiHandleMetaDetectedOutcomeWebhookEvent.WabaId,
                JsonConvert.SerializeObject(
                    onCloudApiHandleMetaDetectedOutcomeWebhookEvent.Change,
                    JsonConfig.DefaultJsonSerializerSettings),
                JsonConvert.SerializeObject(
                    onCloudApiHandleMetaDetectedOutcomeWebhookEvent,
                    JsonConfig.DefaultJsonSerializerSettings),
                retryCount);

            await context.Redeliver(delay: GetRetryTimeSpan(retryCount));

            return;
        }

        _logger.LogError(
            e,
            "Max retry limit reached. Failed to process detected outcome webhook from Cloud Api. WabaId: {WabaId}, Change: {Change}, Webhook Event: {WebhookEvent}, Retry Attempts: {RetryCount}",
            onCloudApiHandleMetaDetectedOutcomeWebhookEvent.WabaId,
            JsonConvert.SerializeObject(
                onCloudApiHandleMetaDetectedOutcomeWebhookEvent.Change,
                JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(onCloudApiHandleMetaDetectedOutcomeWebhookEvent, JsonConfig.DefaultJsonSerializerSettings),
            retryCount);

        await SendMaxRetriesReachedAlertAsync(onCloudApiHandleMetaDetectedOutcomeWebhookEvent, e);
    }

    private static TimeSpan GetRetryTimeSpan(int retryCount)
    {
        // Max 1800 second (30 minutes)
        // This event should be retry immediately after the first failure
        const int maxRetrySeconds = 60 * 30;

        var calculatedRetrySeconds = Math.Pow(1.25, retryCount) * retryCount * 30;

        var finalRetrySeconds = calculatedRetrySeconds > maxRetrySeconds ? maxRetrySeconds : calculatedRetrySeconds;

        return TimeSpan.FromSeconds(finalRetrySeconds);
    }

    private async Task SendMaxRetriesReachedAlertAsync(
        OnCloudApiHandleMetaDetectedOutcomeWebhookEvent onCloudApiHandleMetaDetectedOutcomeWebhookEvent,
        Exception exception)
    {
        var slackClient = new SlackClient(_slackIncomingWebhooksConfig.WhatsappCloudApiErrorAlertsWebhookUrl);

        // Send a message to the webhook.
        var message = new SlackMessage
        {
            Text = "*WhatsApp Cloud API Error Occurred*",
            Attachments = new List<SlackAttachment>
            {
                new ()
                {
                    Color = "#FF0000",
                    Fields = new List<SlackField>
                    {
                        new ()
                        {
                            Title = "Application", Value = "MessagingHub", Short = true
                        },
                        new ()
                        {
                            Title = "Environment", Value = _slackIncomingWebhooksConfig.Env.ToUpper(), Short = true
                        },
                        new ()
                        {
                            Title = "Facebook Waba ID",
                            Value = onCloudApiHandleMetaDetectedOutcomeWebhookEvent.WabaId,
                            Short = true
                        },
                        new ()
                        {
                            Title = "DateTime", Value = DateTimeOffset.Now.ToString("u"), Short = true
                        },
                        new ()
                        {
                            Title = "Error Message",
                            Value = "Max retries reached for processing detected outcome webhook",
                            Short = false
                        },
                        new ()
                        {
                            Title = "Error Context", Value = "```" + exception + "```", Short = false
                        },
                        new ()
                        {
                            Title = "Webhook Payload",
                            Value = "```" + JsonConvert.SerializeObject(
                                onCloudApiHandleMetaDetectedOutcomeWebhookEvent.WebhookMessages,
                                JsonConfig.DefaultJsonSerializerSettings) + "```",
                            Short = false
                        }
                    }
                }
            }
        };

        await slackClient.PostAsync(message);
    }

    private void TraceMessageWebhookDeliveredEvent(CloudApiWebhookValueObject value)
    {
        try
        {
            var customProperties = value switch
            {
                { Messages: { } messages } when messages.Count != 0 => new Dictionary<string, string>()
                {
                    {
                        "type", "message"
                    },
                    {
                        "message_type", messages.FirstOrDefault()?.Type ?? "unknown"
                    }
                },
                { Statuses: { } statuses } when statuses.Count != 0 => new Dictionary<string, string>()
                {
                    {
                        "type", "status"
                    },
                    {
                        "message_status", statuses.FirstOrDefault()?.Status ?? "unknown"
                    }
                },
                _ => null
            };

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.WhatsappCloudApiWebhookHandled,
                customProperties);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to trace messages webhook delivered event");
        }
    }

    private void IncrementMessageWebhookDeliveredCounter(string webhookUrl, CloudApiWebhookValueObject value)
    {
        // Track messages webhook delivered metrics for non private cloud deployments
        try
        {
            if (webhookUrl.StartsWith("https://api.sleekflow.io") ||
                webhookUrl.StartsWith("https://sleekflow-core-app-"))
            {
                _messagingHubMeters.IncrementCounter(
                    MessagingHubChannelMeterNames.WhatsappCloudApi,
                    MessagingHubWebhookMeterOptions.MessagesWebhookDelivered,
                    tags: value switch
                    {
                        { Messages: { } messages } when messages.Count != 0 => new WebhookTypeMeterTags(
                            "message",
                            messageType: messages.FirstOrDefault()?.Type),
                        { Statuses: { } statuses } when statuses.Count != 0 => new WebhookTypeMeterTags(
                            "status",
                            messageStatus: statuses.FirstOrDefault()?.Status),
                        _ => new WebhookTypeMeterTags("unknown")
                    });
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to increment messages webhook delivered counter");
        }
    }
}