using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Configs;

public interface IWorkflowEnrollmentRateLimitEmailNotificationConfig
{
    int BlockedEnrollmentEmailNotificationRateLimitWindowSeconds { get; }

    int MaxBlockedEnrollmentEmailAllowedWithinWindow { get; }
}

public class WorkflowEnrollmentRateLimitEmailNotificationConfig : IWorkflowEnrollmentRateLimitEmailNotificationConfig, IConfig
{
    public int BlockedEnrollmentEmailNotificationRateLimitWindowSeconds { get; }

    public int MaxBlockedEnrollmentEmailAllowedWithinWindow { get; }

    public WorkflowEnrollmentRateLimitEmailNotificationConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        BlockedEnrollmentEmailNotificationRateLimitWindowSeconds =
            int.TryParse(
                Environment.GetEnvironmentVariable(
                    "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_WINDOW_SECONDS",
                    target),
                out var rateLimitWindowSeconds)
                ? rateLimitWindowSeconds
                : throw new SfMissingEnvironmentVariableException(
                    "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_WINDOW_SECONDS");

        MaxBlockedEnrollmentEmailAllowedWithinWindow =
            int.TryParse(
                Environment.GetEnvironmentVariable(
                    "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW",
                    target),
                out var maxAllowedWithinWindow)
                ? maxAllowedWithinWindow
                : throw new SfMissingEnvironmentVariableException(
                    "WORKFLOW_BLOCKED_ENROLLMENT_EMAIL_NOTIFICATION_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW");
    }
}