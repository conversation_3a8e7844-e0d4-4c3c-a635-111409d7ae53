{"version": "2.0", "functionTimeout": "00:10:00", "logging": {"applicationInsights": {"samplingSettings": {"isEnabled": true, "maxTelemetryItemsPerSecond": 100, "excludedTypes": "Request;Exception"}}}, "extensions": {"durableTask": {"maxConcurrentActivityFunctions": 5, "maxConcurrentOrchestratorFunctions": 10, "storageProvider": {"partitionCount": 12}, "useGracefulShutdown": true}, "serviceBus": {"maxConcurrentCalls": 2, "maxAutoLockRenewalDuration": "00:10:00"}, "queues": {"batchSize": 2, "maxPollingInterval": "00:00:02"}}, "concurrency": {"dynamicConcurrencyEnabled": false}}