using System.Collections.Immutable;
using System.Composition;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CodeActions;
using Microsoft.CodeAnalysis.CodeFixes;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Editing;

namespace Sleekflow.Analyzers.Styles;

[ExportCodeFixProvider(LanguageNames.CSharp, Name = nameof(ValidateObjectAttributeAnalyzerCodeFixProvider))]
[Shared]
public class ValidateObjectAttributeAnalyzerCodeFixProvider : CodeFixProvider
{
    private const string Title = "Add [ValidateObject] attribute";

    public sealed override ImmutableArray<string> FixableDiagnosticIds
    {
        get { return ImmutableArray.Create(ValidateObjectAttributeAnalyzer.DiagnosticId); }
    }

    public sealed override FixAllProvider GetFixAllProvider()
    {
        return WellKnownFixAllProviders.BatchFixer;
    }

    public sealed override async Task RegisterCodeFixesAsync(CodeFixContext context)
    {
        var root = await context.Document.GetSyntaxRootAsync(context.CancellationToken).ConfigureAwait(false);

        var diagnostic = context.Diagnostics.First();
        var diagnosticSpan = diagnostic.Location.SourceSpan;

        var declaration = root!.FindToken(diagnosticSpan.Start).Parent!.AncestorsAndSelf()
            .OfType<PropertyDeclarationSyntax>().First();

        context.RegisterCodeFix(
            CodeAction.Create(
                title: Title,
                createChangedDocument: c => AddValidateObjectAttributeAsync(context.Document, declaration, c),
                equivalenceKey: Title),
            diagnostic);
    }

    private async Task<Document> AddValidateObjectAttributeAsync(
        Document document,
        PropertyDeclarationSyntax propertyDeclaration,
        CancellationToken cancellationToken)
    {
        var editor = await DocumentEditor.CreateAsync(document, cancellationToken).ConfigureAwait(false);
        var generator = editor.Generator;

        var validateObjectAttribute = generator.Attribute("Sleekflow.Validations.ValidateObjectAttribute");
        editor.AddAttribute(propertyDeclaration, validateObjectAttribute);

        return editor.GetChangedDocument();
    }
}