package endpoints.inbox

# Inbox settings endpoints
inbox_settings_endpoints := {
    "view": [
        {
            # Notifications, Collaborator, Contacts, Channels,
            "path": "/Company/UserRole/permission",
            "method": "GET"
        },
        {
            "path": "/company/Tags",
            "method": "GET"
        },
        {
            "path": "/Company",
            "method": "GET"
        }
    ],
    "edit": [
        {
            # Notifications, Collaborator, Contacts, Channels,
            "path": "/Company/UserRole/permission",
            "method": "POST"
        },
        {   # Sort messages
            "path": "/Company/default-inbox-order/[^/]+",
            "method": "POST"
        }
    ]
}

inbox_settings_quick_repiles_endpoints := {
    "view": [
       {
           "path": "/Company/QuickReply",
           "method": "GET"
       }
    ],
    "create": [
       {
           "path": "/Company/QuickReply",
           "method": "POST"
       },
       {
           "path": "/Company/QuickReply/attachment/[^/]+",
           "method": "POST"
       },
       {
            "path": "/Company/QuickReply/attachment/[^/]+",
            "method": "DELETE"
       }
    ],
    "edit": [
       {
           "path": "/Company/QuickReply",
           "method": "POST"
       },
       {
           "path": "/Company/QuickReply/attachment/[^/]+",
           "method": "POST"
       },
       {
            "path": "/Company/QuickReply/attachment/[^/]+",
            "method": "DELETE"
       }
    ],
    "delete": [
       {
           "path": "/Company/QuickReply",
           "method": "DELETE"
       }
    ]
}


# Inbox endpoints
inbox_conversation_endpoints := {
    "view": [
        {
            "path": "/v2/Company/Team",
            "method": "GET"
        },
        {
            "path": "/v3/conversation/summary/[^/]+",
            "method": "GET"
        },
        {
            "path": "/v3/conversations/[^/]+",
            "method": "GET"
        },
        {
            "path": " /v2/Conversation/[^/]+/Search/Message",
            "method": "GET"
        },
        {
            "path": "/ConversationMessages/GetMessage",
            "method": "POST"
        },
        {
            "path": "/ConversationMessages/GetMessages",
            "method": "POST"
        },
        {
            "path": "/conversation/accessible",
            "method": "POST"
        },
        {
            "path": "/v3/conversation/unreadSummary",
            "method": "GET"
        },
        {
            "path": "/v3/Conversation/[^/]+/Search/Message",
            "method": "POST"
        },
        {
            "path": "/Conversation/Tags/Add/[^/]+",
            "method": "POST"
        },
        {
            "path": "/Conversation/Tags/Remove/[^/]+",
            "method": "POST"
        }
    ],
    "edit": [
        {
            "path": "/Conversation/Bookmark",
            "method": "POST"
        },
        {
            "path": "/v3/conversation/unread/[^/]+",
            "method": "POST"
        },
        {
            "path": "/Conversation/Status/[^/]+",
            "method": "POST"
        }
    ]
}

inbox_conversation_send_message_endpoints := {
    "create": [
        {
            "path": "/ConversationMessages/SendMessage",
            "method": "POST"
        },
        {
            "path": "/ConversationMessages/SendFileMessage",
            "method": "POST"
        },
        {
            "path": "/ConversationMessages/ForwardMessage",
            "method": "POST"
        },
        {
            "path": "/Conversation/Note",
            "method": "POST"
        },
        {
            "path": "/conversation/typing",
            "method": "POST"
        },
    ]
}

inbox_conversation_send_product_link_endpoints := {
    "create": [
        # Send product link
        {
            "path": "/Shopify/Product/[^/]+",
            "method": "POST"
        },
    ]
}

inbox_conversation_send_payment_link_endpoints := {
    "create": [
        # Send payment link
        {
            "path": "/SleekPay/Generate/Payment",
            "method": "POST"
        },
    ]
}

inbox_conversation_assignment_endpoints := {
    "edit": [
        {
            "path": "/v2/conversation/Assign/[^/]+",
            "method": "POST"
        },
        {
            "path": "/v2/conversation/assignee/[^/]+",
            "method": "POST"
        },
        {
            "path": "/v2/conversation/collaborator/[^/]+",
            "method": "POST"
        }
    ],
}