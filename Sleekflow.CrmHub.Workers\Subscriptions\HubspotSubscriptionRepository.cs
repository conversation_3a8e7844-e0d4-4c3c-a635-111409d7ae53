﻿using Microsoft.Extensions.Logging;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Workers.Subscriptions;

public interface IHubspotSubscriptionRepository : IRepository<HubspotSubscription>
{
}

public class HubspotSubscriptionRepository : BaseRepository<HubspotSubscription>, IHubspotSubscriptionRepository
{
    public HubspotSubscriptionRepository(
        ILogger<BaseRepository<HubspotSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}