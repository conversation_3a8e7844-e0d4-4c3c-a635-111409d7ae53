using MassTransit;
using Sleekflow.CrmHub.Models.Events;

namespace Sleekflow.CrmHub.Events;

public class OnProviderTypeSyncInitializedEventConsumerDefinition
    : ConsumerDefinition<OnProviderTypeSyncInitializedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnProviderTypeSyncInitializedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnProviderTypeSyncInitializedEventConsumer : IConsumer<OnProviderTypeSyncInitializedEvent>
{
    public Task Consume(ConsumeContext<OnProviderTypeSyncInitializedEvent> context)
    {
        return Task.CompletedTask;
    }
}