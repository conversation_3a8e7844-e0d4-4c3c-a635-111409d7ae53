using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class EnrollFlowHub : ITrigger
{
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IAppConfig _appConfig;

    public EnrollFlowHub(
        IFlowHubConfigService flowHubConfigService,
        IAppConfig appConfig)
    {
        _flowHubConfigService = flowHubConfigService;
        _appConfig = appConfig;
    }

    public class EnrollFlowHubInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty("origin")]
        public string? Origin { get; set; }

        [JsonConstructor]
        public EnrollFlowHubInput(
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string origin)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            Origin = origin;
        }
    }

    public class EnrollFlowHubOutput
    {
        [JsonProperty("flow_hub_config")]
        public FlowHubConfig FlowHubConfig { get; set; }

        [JsonConstructor]
        public EnrollFlowHubOutput(
            FlowHubConfig flowHubConfig)
        {
            FlowHubConfig = flowHubConfig;
        }
    }

    public async Task<EnrollFlowHubOutput> F(EnrollFlowHubInput enrollFlowHubInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            enrollFlowHubInput.SleekflowStaffId,
            enrollFlowHubInput.SleekflowStaffTeamIds);

        var enrollmentOrigin = string.IsNullOrWhiteSpace(enrollFlowHubInput.Origin)
            ? new Uri(_appConfig.CoreInternalsEndpoint).GetLeftPart(UriPartial.Authority)
            : enrollFlowHubInput.Origin;

        var flowHubConfig = await _flowHubConfigService.EnrollFlowHubAsync(
            enrollFlowHubInput.SleekflowCompanyId,
            enrollmentOrigin,
            sleekflowStaff);

        return new EnrollFlowHubOutput(flowHubConfig);
    }
}