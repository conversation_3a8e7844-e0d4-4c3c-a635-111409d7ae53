using System.Reflection;
using Serilog;
using Sleekflow;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Processors;
using Sleekflow.CommerceHub.Products;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;
using Sleekflow.Persistence.CommerceHubDb;

#if SWAGGERGEN
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using Moq;
#endif

static void BuildCommerceHubDbProcessorServices(IServiceCollection b)
{
#if SWAGGERGEN
#else
    b.AddSingleton<ICommerceHubDbProcessorConfig, CommerceHubDbProcessorConfig>();
    b.AddHostedService<CommerceHubDbProcessorService>(
        sp => new CommerceHubDbProcessorService(
            sp.GetRequiredService<ILogger<CommerceHubDbProcessorService>>(),
            sp.GetRequiredService<ICommerceHubDbResolver>(),
            sp.GetRequiredService<ICommerceHubDbProcessorConfig>(),
            sp.GetRequiredService<IProductSearchService>(),
            sp.GetRequiredService<ICategorySearchService>()));
#endif
}

const string appName = "SleekflowCommerceHub";

MvcModules.BuildLogger(appName);

Log.Information("Starting web host");

var builder = WebApplication.CreateBuilder(args);
builder.Host.UseSerilog();
builder.Services.AddHttpContextAccessor();

MvcModules.BuildHealthCheck(builder.Services);
MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
#if SWAGGERGEN
MvcModules.BuildApiBehaviors(builder, list =>
{

    list.AddRange(new List<OpenApiServer>()
    {
        new OpenApiServer()
        {
            Description = "Local",
            Url = $"https://localhost:7080",
        },
        new OpenApiServer()
        {
            Description = "Dev Apigw",
            Url = $"https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/commerce-hub",
        },
        new OpenApiServer()
        {
            Description = "Prod Apigw",
            Url = $"https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/commerce-hub",
        }
    });
});
#else
MvcModules.BuildApiBehaviors(builder);
#endif
Modules.BuildHttpClients(builder.Services);
Modules.BuildConfigs(builder.Services, Assembly.Load("Sleekflow.CommerceHub.Payments"));
Modules.BuildServices(builder.Services, Assembly.Load("Sleekflow.CommerceHub.Payments"));
Modules.BuildTriggers(builder.Services);
Modules.BuildServiceBusServices(builder.Services);
Modules.BuildServiceBusManager(builder.Services);
Modules.BuildDbServices(builder.Services);
Modules.BuildCacheServices(builder.Services);
Modules.BuildWorkerServices(builder.Services);
MvcModules.BuildFuncServices(builder.Services, appName);
CommerceHubModules.BuildCommerceHubDbServices(builder.Services);
BuildCommerceHubDbProcessorServices(builder.Services);

var app = builder.Build();

// app.UseHttpsRedirection();
app.UseAuthorization();
app.MapControllers();
HealthCheckMapping.MapHealthChecks(app);

#if SWAGGERGEN
app.UseSwagger();
app.UseSwaggerUI(
    options =>
    {
        var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

        foreach (var description in provider.ApiVersionDescriptions)
        {
            options.SwaggerEndpoint(
                $"/swagger/{description.GroupName}/swagger.json",
                description.GroupName.ToUpperInvariant());
        }
    });
#endif

ThreadPool.SetMinThreads(128, 128);
ThreadPool.SetMaxThreads(512, 512);

app.Run();

Log.CloseAndFlush();