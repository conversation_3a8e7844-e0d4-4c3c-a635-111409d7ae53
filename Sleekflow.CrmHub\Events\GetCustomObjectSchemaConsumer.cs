﻿using MassTransit;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.CrmHub.Events;

public class GetCustomObjectSchemaConsumerDefinition
    : ConsumerDefinition<GetCustomObjectSchemaConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetCustomObjectSchemaConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetCustomObjectSchemaConsumer : IConsumer<GetCustomObjectSchemaRequest>
{
    private readonly ISchemaService _schemaService;

    public GetCustomObjectSchemaConsumer(ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public async Task Consume(ConsumeContext<GetCustomObjectSchemaRequest> context)
    {
        var message = context.Message;

        var schema = await _schemaService.GetAsync(
            message.SchemaId,
            message.SleekflowCompanyId);

        await context.RespondAsync(
            new GetCustomObjectSchemaReply(
                schema.Id,
                schema.SleekflowCompanyId,
                schema.DisplayName,
                schema.UniqueName,
                new CustomObjectSchemaPrimaryProperty(
                    schema.PrimaryProperty.Id,
                    schema.PrimaryProperty.DisplayName,
                    schema.PrimaryProperty.UniqueName,
                    new CustomObjectPropertyDataType(
                        schema.PrimaryProperty.DataType.Name),
                    new CustomObjectSchemaPrimaryPropertyConfig(
                        schema.PrimaryProperty.PrimaryPropertyConfig.IsAutoGenerated)),
                schema.Properties
                    .Select(
                        p =>
                            new CustomObjectProperty(
                                p.Id,
                                p.DisplayName,
                                p.UniqueName,
                                new CustomObjectPropertyDataType(
                                    p.DataType.Name),
                                p.IsRequired,
                                p.DisplayOrder,
                                p.Options?
                                    .OrderBy(o => o.DisplayOrder)
                                    .Select(
                                        o =>
                                            new CustomObjectPropertyOption(
                                                o.Id,
                                                o.Value,
                                                o.DisplayOrder))
                                    .ToList()))
                    .ToList()));
    }
}