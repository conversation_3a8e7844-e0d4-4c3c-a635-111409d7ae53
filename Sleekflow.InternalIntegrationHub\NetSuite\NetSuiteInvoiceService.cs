using System.Text.RegularExpressions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.**********************.Clients;
using Sleekflow.**********************.CompanyServerLocation;
using Sleekflow.**********************.Constants.NetSuite;
using Sleekflow.**********************.Models.Constants.NetSuite;
using Sleekflow.**********************.Models.Constants.TravisBackend;
using Sleekflow.**********************.Models.NetSuite.Integrations;
using Sleekflow.**********************.Models.NetSuite.Internal;
using Sleekflow.**********************.Repositories;
using Sleekflow.**********************.Triggers.NetSuite.Internal;
using Sleekflow.**********************.WhatsappCloudApi;
using Currency = Sleekflow.**********************.Models.NetSuite.Internal.Common.Currency;
using Entity = Sleekflow.**********************.Models.NetSuite.Internal.Common.Entity;

namespace Sleekflow.**********************.NetSuite;

public interface INetSuiteInvoiceService
{
    public Task<string?> CreateInvoiceAsync(CreateInvoice.CreateInvoiceInput createInvoiceInput);

    public Task<int> CreateInvoiceBatchAsync(
        string companyId,
        bool isEnableCheckBillRecord,
        bool isEnableCheckWhatsappCredit);

    public Task<int> SyncInvoiceByCompanyIds(
        SyncInvoiceByCompanyIds.SyncInvoiceByCompanyIdsInput syncInvoiceByCompanyIdsInput);
}

public partial class NetSuiteInvoiceService : INetSuiteInvoiceService, IScopedService
{
    private readonly ILogger<NetSuiteInvoiceService> _logger;
    private readonly INetSuiteClient _client;
    private readonly ITravisBackendRepository _travisBackendRepository;
    private readonly INetSuiteSettingService _netSuiteSettingService;
    private readonly INetSuiteEmployeeService _netSuiteEmployeeService;
    private readonly INetSuiteCustomerService _netSuiteCustomerService;
    private readonly ICompanyServerLocationService _companyServerLocationService;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWabaService _wabaService;

    public NetSuiteInvoiceService(
        ILogger<NetSuiteInvoiceService> logger,
        INetSuiteClient client,
        ITravisBackendRepository travisBackendRepository,
        INetSuiteSettingService netSuiteSettingService,
        INetSuiteEmployeeService netSuiteEmployeeService,
        INetSuiteCustomerService netSuiteCustomerService,
        ICompanyServerLocationService companyServerLocationService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IWabaService wabaService)
    {
        _logger = logger;
        _client = client;
        _travisBackendRepository = travisBackendRepository;
        _netSuiteSettingService = netSuiteSettingService;
        _netSuiteEmployeeService = netSuiteEmployeeService;
        _netSuiteCustomerService = netSuiteCustomerService;
        _companyServerLocationService = companyServerLocationService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _wabaService = wabaService;
    }

    public async Task<string?> CreateInvoiceAsync(CreateInvoice.CreateInvoiceInput createInvoiceInput)
    {
        const string dateFormat = "yyyy-MM-dd";
        var tranDate = createInvoiceInput.CreatedDate == null
            ? DateTime.Now.ToString(dateFormat)
            : createInvoiceInput.CreatedDate.Value.ToString(dateFormat);
        var invoiceItems = new List<InvoiceItem>();
        var externalId = createInvoiceInput.ExternalId == null
            ? null
            : ExternalIdRegex().Replace(createInvoiceInput.ExternalId, "_");

        // Subscription Fee
        await CreateInvoiceOrRefundAsync(
            new CreateInvoiceOrRefundInput(
                ItemName.SubscriptionFee,
                createInvoiceInput.SubscriptionFee,
                externalId,
                createInvoiceInput.CompanyId,
                createInvoiceInput.SubscriptionStartDate?.ToString(dateFormat),
                createInvoiceInput.SubscriptionStartDate?.ToString(dateFormat),
                createInvoiceInput.SubscriptionDescription),
            invoiceItems);

        // One Time Setup Fee
        await CreateInvoiceOrRefundAsync(
            new CreateInvoiceOrRefundInput(
                ItemName.OneTimeSetupFee,
                createInvoiceInput.OneTimeSetupFee,
                externalId,
                createInvoiceInput.CompanyId,
                DateTime.Now.ToString(dateFormat),
                DateTime.Now.ToString(dateFormat),
                createInvoiceInput.OneTimeSetupDescription),
            invoiceItems);

        // Whatsapp Credit
        await CreateInvoiceOrRefundAsync(
            new CreateInvoiceOrRefundInput(
                ItemName.WhatsappCredit,
                createInvoiceInput.WhatsappCreditAmount,
                externalId,
                createInvoiceInput.CompanyId,
                DateTime.Now.ToString(dateFormat),
                DateTime.Now.ToString(dateFormat),
                createInvoiceInput.WhatsappCreditDescription),
            invoiceItems);

        if (invoiceItems.Count == 0)
        {
            // no item
            return string.Empty;
        }

        var (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails) =
            await _netSuiteSettingService.NetSuitePreparation(
                CancellationToken.None,
                showEmployee: false,
                showSubsidiary: false);

        var currency = currencyDetails.FirstOrDefault(
            x => string.Equals(x.Name, createInvoiceInput.Currency, StringComparison.OrdinalIgnoreCase),
            currencyDetails.First());

        var termId = termDetails.FirstOrDefault(
            x => x.DaysUntilNetDue == createInvoiceInput.PaymentTerm,
            termDetails.First()).Id;

        // search for sales rep
        var companyId = createInvoiceInput.CompanyId;

        var companyServerLocation = await _companyServerLocationService.GetCompanyServerLocation(companyId);
        var company = await _travisBackendRepository.GetCompanyById(companyId, companyServerLocation);
        if (company == null)
        {
            throw new SfNotFoundObjectException("Company not found");
        }

        var salesRep =
            await _travisBackendRepository.GetCompanySalesRepresentative(
                company.CmsActivationOwnerId,
                companyServerLocation);

        if (salesRep == null)
        {
            throw new SfNotFoundObjectException("Sales rep not found");
        }

        var salesRepId = await _netSuiteEmployeeService.GetEmployeeIdByEmailAsync(salesRep.Email);

        if (externalId == null)
        {
            var request = new CreateInvoiceRequest(
                null,
                null,
                tranDate,
                new Entity(companyId),
                new InvoiceItems(invoiceItems),
                new Currency(currency.Id),
                new Term(termId),
                new SalesRep(salesRepId));
            var response = await _client.PostAsync<object>(
                Endpoints.CreateInvoiceEndpoint,
                request);
            if (!response.IsSuccessful)
            {
                _logger.LogError(
                    "Failed to create invoice to NetSuite: {Content} Request: {Request}",
                    response.Content,
                    request.ToString());
            }

            return "Successfully created invoice without externalId to NetSuite";
        }

        _logger.LogDebug("Creating OR Updating the invoice {ExternalId}", createInvoiceInput.ExternalId);
        var createInvoiceReq = new CreateInvoiceRequest(
            externalId,
            null,
            tranDate,
            new Entity(companyId),
            new InvoiceItems(invoiceItems),
            new Currency(currency.Id),
            new Term(termId),
            new SalesRep(salesRepId));
        var createInvoiceParams = new Dictionary<string, string>
        {
            {
                "replaceSelectedFields", "true"
            },
            {
                "replace", "item"
            }
        };

        var createInvoiceRes = await _client.PutAsync<object>(
            $"{Endpoints.CreateInvoiceEndpoint}/eid:{externalId}",
            createInvoiceReq,
            createInvoiceParams);
        if (createInvoiceRes.IsSuccessful)
        {
            if (createInvoiceInput.PaidAmount is <= 0 or null)
            {
                return createInvoiceInput.ExternalId;
            }

            var createPaymentResult = await _netSuiteCustomerService.CreatePaymentAsync(
                externalId,
                createInvoiceInput.PaidAmount.Value,
                companyId,
                createInvoiceInput.CreatedDate);
            if (!createPaymentResult)
            {
                _logger.LogError("Create payment failed {ExternalId}", createInvoiceInput.ExternalId);
            }

            return createInvoiceInput.ExternalId;
        }

        _logger.LogError(
            "Failed to update invoice to NetSuite: {Content} Request: {Request}",
            createInvoiceRes.Content,
            createInvoiceReq.ToString());
        return string.Empty;
    }

    public async Task<int> CreateInvoiceBatchAsync(
        string companyId,
        bool isEnableCheckBillRecord,
        bool isEnableCheckWhatsappCredit)
    {
        if (!isEnableCheckBillRecord && !isEnableCheckWhatsappCredit)
        {
            _logger.LogError("Both CheckBillRecord and CheckWhatsappCredit is disabled");
            return 0;
        }

        var successCount = 0;
        string? subscriptionDescription = null;
        string? oneOffDescription = null;

        if (isEnableCheckBillRecord)
        {
            // Travis backend bill records only
            var companyServerLocation = await _companyServerLocationService.GetCompanyServerLocation(companyId);
            var billRecords = await _travisBackendRepository.GetCompanyBillRecords(companyId, companyServerLocation);
            if (billRecords == null)
            {
                throw new SfNotFoundObjectException("Bill records not found");
            }

            _logger.LogDebug("Total bill records: {Count}", billRecords.Count);

            await Parallel.ForEachAsync(
                billRecords,
                new ParallelOptions
                {
                    MaxDegreeOfParallelism = 1
                },
                async (billRecord, _) =>
                {
                    var subscriptionFee = 0.0;
                    if (Plans.AllSubscriptionPlans.Contains(billRecord.SubscriptionPlanId) &&
                        !billRecord.SubscriptionPlanId.Contains("_oneoff"))
                    {
                        subscriptionFee += billRecord.PayAmount;
                        subscriptionDescription = billRecord.SubscriptionPlanId;
                    }

                    var oneOffFee = 0.0;
                    if (billRecord.SubscriptionPlanId.Contains("_oneoff"))
                    {
                        oneOffFee += billRecord.PayAmount;
                        oneOffDescription = billRecord.SubscriptionPlanId;
                    }

                    var createInvoiceInput = new CreateInvoice.CreateInvoiceInput(
                        billRecord.Id.ToString(),
                        companyId,
                        (decimal) subscriptionFee,
                        subscriptionDescription,
                        (decimal) oneOffFee,
                        oneOffDescription,
                        decimal.Zero,
                        null,
                        billRecord.PeriodStart,
                        billRecord.PeriodEnd,
                        30,
                        billRecord.Currency,
                        billRecord.PaymentStatus == 1 ? billRecord.PayAmount : null,
                        billRecord.Created);

                    var result = await CreateInvoiceAsync(createInvoiceInput);
                    if (string.Equals(result, string.Empty))
                    {
                        _logger.LogError("Failed to create invoice for bill record {BillRecordId}", billRecord.Id);
                        return;
                    }

                    successCount++;
                });
        }

        // Whatsapp Credit Transaction from CosmosDB
        // Get Whatsapp Transaction
        if (isEnableCheckWhatsappCredit)
        {
            var wabas = await _wabaService.GetWabas(companyId);

            if (wabas.Count == 0)
            {
                throw new SfNotFoundObjectException($"No Waba found for {companyId}");
            }

            foreach (var waba in wabas)
            {
                string? continuationToken = null;
                do
                {
                    var (transactionLogs, nextContinuationToken) =
                        await _businessBalanceTransactionLogService
                            .GetBusinessBalanceTransactionLogs(waba.FacebookBusinessId, continuationToken);

                    await Parallel.ForEachAsync(
                        transactionLogs,
                        new ParallelOptions
                        {
                            MaxDegreeOfParallelism = 1
                        },
                        async (log, _) =>
                        {
                            var createInvoiceInput = new CreateInvoice.CreateInvoiceInput(
                                $"wa-{log.UniqueId}",
                                companyId,
                                0,
                                subscriptionDescription,
                                0,
                                oneOffDescription,
                                log.WabaTopUp!.PayAmount.Amount,
                                log.WabaTopUp.PaymentMethod,
                                null,
                                null,
                                30,
                                log.WabaTopUp.PayAmount.CurrencyIsoCode,
                                (double) log.WabaTopUp!.PayAmount.Amount,
                                log.CreatedAt.DateTime);

                            var result = await CreateInvoiceAsync(createInvoiceInput);

                            if (string.Equals(result, string.Empty))
                            {
                                _logger.LogError("Failed to create invoice for {LogId}", log.UniqueId);
                            }

                            successCount++;
                        });

                    continuationToken = nextContinuationToken;
                }
                while (continuationToken != null);
            }
        }

        _logger.LogDebug("Total CreateInvoiceBatchAsync success count: {Count}", successCount);
        return successCount;
    }

    public async Task<int> SyncInvoiceByCompanyIds(
        SyncInvoiceByCompanyIds.SyncInvoiceByCompanyIdsInput syncInvoiceByCompanyIdsInput)
    {
        if (syncInvoiceByCompanyIdsInput.CompanyIds.Length <= 0)
        {
            throw new Exception("No Company provided");
        }

        var successCount = 0;
        await Parallel.ForEachAsync(
            syncInvoiceByCompanyIdsInput.CompanyIds.Distinct(),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 1
            },
            async (companyId, _) =>
            {
                successCount += await CreateInvoiceBatchAsync(
                    companyId,
                    syncInvoiceByCompanyIdsInput.IsEnableCheckBillRecord,
                    syncInvoiceByCompanyIdsInput.IsEnableCheckWhatsappCredit);
            });

        return successCount;
    }

    private async Task CreateInvoiceOrRefundAsync(
        CreateInvoiceOrRefundInput input,
        List<InvoiceItem> invoiceItems)
    {
        switch (input.Amount)
        {
            case < 0:
            {
                if (input.ExternalId != null)
                {
                    var creditMemoReq = new CreateCreditMemo.CreateCreditMemoInput(
                        input.CompanyId,
                        Math.Abs(input.Amount),
                        input.ExternalId,
                        input.FeeType);
                    var creditMemoExternalId = await _netSuiteCustomerService.CreateCreditMemo(creditMemoReq);
                    if (!string.IsNullOrEmpty(creditMemoExternalId))
                    {
                        await _netSuiteCustomerService.CreateRefundAsync(
                            new CreateRefund.CreateRefundInput(
                                input.CompanyId,
                                Math.Abs(input.Amount),
                                creditMemoExternalId));
                    }
                }

                break;
            }

            case > 0:
                invoiceItems.Add(
                    new InvoiceItem
                    {
                        Item = await _netSuiteSettingService.GetInvoiceItemIdAsync(ItemName.SubscriptionFee),
                        Quantity = 1,
                        Amount = decimal.ToDouble(input.Amount),
                        CustcolCvRrRevStartDate = input.SubscriptionStartDate,
                        CustcolCvRrRevEndDate = input.SubscriptionEndDate,
                        Description = input.SubscriptionDescription
                    });
                break;
        }
    }

    [GeneratedRegex("[^0-9A-Za-z _-]")]
    private static partial Regex ExternalIdRegex();
}