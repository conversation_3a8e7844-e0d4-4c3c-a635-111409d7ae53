using MassTransit;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Steps;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnWaitForEventStepTimeoutEventConsumerDefinition
    : ConsumerDefinition<OnWaitForEventStepTimeoutEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWaitForEventStepTimeoutEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 16;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWaitForEventStepTimeoutEventConsumer : IConsumer<OnWaitForEventStepTimeoutEvent>
{
    private readonly IStateSubscriptionService _stateSubscriptionService;
    private readonly IStepExecutorActivator _stepExecutorActivator;

    public OnWaitForEventStepTimeoutEventConsumer(
        IStateSubscriptionService stateSubscriptionService,
        IStepExecutorActivator stepExecutorActivator)
    {
        _stateSubscriptionService = stateSubscriptionService;
        _stepExecutorActivator = stepExecutorActivator;
    }

    public async Task Consume(ConsumeContext<OnWaitForEventStepTimeoutEvent> context)
    {
        var @event = context.Message;

        // Update the state subscription to be expired
        var stateSubscription =
            await _stateSubscriptionService.GetStateSubscriptionAsync(
                @event.StateSubscriptionId,
                @event.StateId);
        if (stateSubscription.IsExecuted || stateSubscription.IsTimeout)
        {
            return;
        }

        await _stateSubscriptionService.UpdateStateSubscriptionIsExpiredAsync(
            stateSubscription,
            true);

        await _stepExecutorActivator.FailStepAsync(
            stateSubscription.StateId,
            stateSubscription.StepId,
            stateSubscription.StackEntries,
            stateSubscription.WorkerInstanceId,
            new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.WaitForEventTimeout,
                "Timeout"));
    }
}