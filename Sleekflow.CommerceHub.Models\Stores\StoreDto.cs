using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Stores;

public class StoreDto : IHasMetadata
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty(Store.PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty(Store.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(Store.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(Store.PropertyNamePlatformData)]
    public PlatformData PlatformData { get; set; }

    [JsonProperty(Store.PropertyNameIsViewEnabled)]
    public bool IsViewEnabled { get; set; }

    [JsonProperty(Store.PropertyNameIsPaymentEnabled)]
    public bool IsPaymentEnabled { get; set; }

    [JsonProperty(Store.PropertyNameLanguages)]
    public List<Language> Languages { get; set; }

    [JsonProperty(Store.PropertyNameCurrencies)]
    public List<Currency> Currencies { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonProperty(Store.PropertyNameTemplateDict)]
    public StoreTemplateDict TemplateDict { get; set; }

    [JsonProperty(Store.PropertyNameStoreIntegrationExternalConfig)]
    public StoreIntegrationExternalConfig? StoreIntegrationExternalConfig { get; set; }

    [JsonProperty(Store.PropertyNameSubscriptionStatus)]
    public StoreSubscriptionStatus? SubscriptionStatus { get; set; }

    [JsonConstructor]
    public StoreDto(
        string id,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        PlatformData platformData,
        bool isViewEnabled,
        bool isPaymentEnabled,
        List<Language> languages,
        List<Currency> currencies,
        Dictionary<string, object?> metadata,
        StoreTemplateDict templateDict,
        StoreIntegrationExternalConfig? storeIntegrationExternalConfig,
        StoreSubscriptionStatus? subscriptionStatus)
    {
        Id = id;
        Url = url;
        Names = names;
        Descriptions = descriptions;
        PlatformData = platformData;
        IsViewEnabled = isViewEnabled;
        IsPaymentEnabled = isPaymentEnabled;
        Languages = languages;
        Currencies = currencies;
        Metadata = metadata;
        TemplateDict = templateDict;
        StoreIntegrationExternalConfig = storeIntegrationExternalConfig;
        SubscriptionStatus = subscriptionStatus;
    }

    public StoreDto(Store store)
        : this(
            store.Id,
            store.Url,
            store.Names,
            store.Descriptions,
            store.PlatformData,
            store.IsViewEnabled,
            store.IsPaymentEnabled,
            store.Languages,
            store.Currencies,
            store.Metadata,
            store.TemplateDict,
            store.StoreIntegrationExternalConfig,
            store.SubscriptionStatus)
    {
    }
}