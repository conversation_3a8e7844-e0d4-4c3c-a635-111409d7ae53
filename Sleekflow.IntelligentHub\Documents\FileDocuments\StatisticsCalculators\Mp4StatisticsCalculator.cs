using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;

public class Mp4StatisticsCalculator : IDocumentStatisticsCalculator
{
    private readonly IDocumentCounterService _documentCounterService;

    public Mp4StatisticsCalculator(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public DocumentStatistics CalculateDocumentStatistics(Stream stream)
    {
        string base64String;
        using (var memoryStream = new MemoryStream())
        {
            stream.CopyTo(memoryStream);
            var fileBytes = memoryStream.ToArray();
            base64String = Convert.ToBase64String(fileBytes);
        }

        if (string.IsNullOrEmpty(base64String))
        {
            return new DocumentStatistics(0, 0, 0, 0, 0);
        }

        var totalTokenCount = _documentCounterService.CountTokens(base64String);
        var totalWordCount = _documentCounterService.CountWords(base64String);
        var totalCharacters = _documentCounterService.CountCharacters(base64String);
        const int totalPages = 1; // MP4 is considered a single page/unit

        return new DocumentStatistics(
            totalTokenCount,
            totalWordCount,
            totalCharacters,
            totalPages,
            (int) stream.Length);
    }
}