using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.SystemAuditLogs;
using Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;
using Sleekflow.AuditHub.SystemAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.Triggers.SystemAuditLogs;

[TriggerGroup("SystemAuditLogs")]
public class CreateSystemAuditLog : ITrigger
{
    private readonly ISystemAuditLogService _systemAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateSystemAuditLog(
        ISystemAuditLogService systemAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _systemAuditLogService = systemAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateSystemAuditLogInput
    {
        [JsonProperty("sleekflow_user_profile_id")]
        public string? SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("data")]
        [Required]
        public object Data { get; set; }

        [JsonConstructor]
        public CreateSystemAuditLogInput(
            string? sleekflowUserProfileId,
            string type,
            object data)
        {
            SleekflowUserProfileId = sleekflowUserProfileId;
            Type = type;
            Data = data;
        }
    }

    public class CreateSystemAuditLogOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateSystemAuditLogOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateSystemAuditLogOutput> F(
        CreateSystemAuditLogInput createSystemAuditLogInput)
    {
        var dataStr = JsonConvert.SerializeObject(createSystemAuditLogInput.Data);

        object? data;
        if (createSystemAuditLogInput.Type == SystemAuditLogTypes.BroadcastDeleted)
        {
            data = JsonConvert.DeserializeObject<BroadcastDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.FlowBuilderEnrolled)
        {
            data = JsonConvert.DeserializeObject<FlowBuilderEnrolledSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.FlowBuilderUnenrolled)
        {
            data = JsonConvert.DeserializeObject<FlowBuilderUnenrolledSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.FlowBuilderWorkflowDeleted)
        {
            data = JsonConvert.DeserializeObject<FlowBuilderWorkflowDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.MessageTemplateUnbookmarked)
        {
            data = JsonConvert.DeserializeObject<MessageTemplateUnbookmarkedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.PiiMaskingConfigCreated)
        {
            data = JsonConvert.DeserializeObject<PiiMaskingConfigCreatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.PiiMaskingConfigDeleted)
        {
            data = JsonConvert.DeserializeObject<PiiMaskingConfigDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.PiiMaskingConfigUpdated)
        {
            data = JsonConvert.DeserializeObject<PiiMaskingConfigUpdatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.Staff2faRevoked)
        {
            data = JsonConvert.DeserializeObject<Staff2faRevokedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffAddedAsCollaborator)
        {
            data = JsonConvert.DeserializeObject<StaffAddedAsCollaboratorSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffAddedToTeams)
        {
            data = JsonConvert.DeserializeObject<StaffAddedToTeamsSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffAssignedToConversation)
        {
            data = JsonConvert.DeserializeObject<StaffAssignedToConversationSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffDeleted)
        {
            data = JsonConvert.DeserializeObject<StaffDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffRemovedAsCollaborator)
        {
            data = JsonConvert.DeserializeObject<StaffRemovedAsCollaboratorSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffRemovedFromTeams)
        {
            data = JsonConvert.DeserializeObject<StaffRemovedFromTeamSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffRoleUpdated)
        {
            data = JsonConvert.DeserializeObject<StaffRoleUpdatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffStatusUpdated)
        {
            data = JsonConvert.DeserializeObject<StaffStatusUpdatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.StaffUnassignedFromConversation)
        {
            data = JsonConvert.DeserializeObject<StaffUnassignedFromConversationSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.TeamDeleted)
        {
            data = JsonConvert.DeserializeObject<TeamDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileCreated)
        {
            data = JsonConvert.DeserializeObject<UserProfileCreatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileDeleted)
        {
            data = JsonConvert.DeserializeObject<UserProfileDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileLabelCreated)
        {
            data = JsonConvert.DeserializeObject<UserProfileLabelCreatedsSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileLabelDeleted)
        {
            data = JsonConvert.DeserializeObject<UserProfileLabelDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileLabelUpdated)
        {
            data = JsonConvert.DeserializeObject<UserProfileLabelUpdatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileListCreated)
        {
            data = JsonConvert.DeserializeObject<UserProfileListCreatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileListDeleted)
        {
            data = JsonConvert.DeserializeObject<UserProfileListDeletedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfileListExported)
        {
            data = JsonConvert.DeserializeObject<UserProfileListExportedSystemLogData>(dataStr);
        }

        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.UserProfilePropertyUpdated)
        {
            data = JsonConvert.DeserializeObject<UserProfilePropertyUpdatedSystemLogData>(dataStr);
        }
        else if (createSystemAuditLogInput.Type == SystemAuditLogTypes.BusinessHourConfigUpdated)
        {
            data = JsonConvert.DeserializeObject<BusinessHourConfigUpdatedSystemLogData>(dataStr);
        }

        else
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult("Type is invalid.")
                });
        }

        if (data == null)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ValidationResult("Data is invalid.")
                });
        }

        try
        {
            Validator.ValidateObject(data, new ValidationContext(data), true);
        }
        catch (ValidationException e)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    e.ValidationResult
                });
        }

        var id = _idService.GetId("SystemAuditLog");
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await _systemAuditLogService.CreateSystemAuditLogAsync(
            new SystemAuditLog(
                id,
                distributedInvocationContext?.SleekflowCompanyId ?? throw new InvalidOperationException(),
                distributedInvocationContext.SleekflowStaffId,
                createSystemAuditLogInput.SleekflowUserProfileId,
                createSystemAuditLogInput.Type,
                JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr),
                DateTimeOffset.UtcNow,
                null));

        return new CreateSystemAuditLogOutput(id);
    }
}