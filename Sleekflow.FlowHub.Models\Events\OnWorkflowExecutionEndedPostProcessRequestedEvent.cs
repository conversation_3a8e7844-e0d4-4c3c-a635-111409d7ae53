﻿using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowExecutionEndedPostProcessRequestedEvent : IHighTrafficEvent
{
    public string SleekflowCompanyId { get; set; }

    public string StateId { get; set; }

    public string WorkflowExecutionEndId { get; set; }

    public OnWorkflowExecutionEndedPostProcessRequestedEvent(
        string sleekflowCompanyId,
        string stateId,
        string workflowExecutionEndId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        WorkflowExecutionEndId = workflowExecutionEndId;
    }
}