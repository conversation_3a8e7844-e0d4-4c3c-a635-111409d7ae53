﻿using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Models.ObjectIdResolvers;

public interface ISalesforceObjectIdResolver
{
    string ResolveObjectId(Dictionary<string, object?> dict);
}

public class SalesforceObjectIdResolver : ISalesforceObjectIdResolver, ISingletonService
{
    public string ResolveObjectId(Dictionary<string, object?> dict)
    {
        if (dict.ContainsKey("salesforce-integrator:Id"))
        {
            var entry = dict["salesforce-integrator:Id"]!;
            if (entry is JObject jObject)
            {
                if (jObject.ContainsKey(SnapshottedValue.PropertyNameValue))
                {
                    return jObject.Value<string>(SnapshottedValue.PropertyNameValue)!;
                }
                else if (jObject.ContainsKey("value"))
                {
                    return jObject.Value<string>("value")!;
                }
            }
            else if (entry is string s)
            {
                return s;
            }
        }
        else if (dict.ContainsKey("Id"))
        {
            return (string) dict["Id"]!;
        }

        throw new SfIdUnresolvableException(dict);
    }
}