﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Services;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.Hubspot.Consumers;

public class SearchHubspotObjectRequestConsumerDefinition : ConsumerDefinition<SearchHubspotObjectRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SearchHubspotObjectRequestConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SearchHubspotObjectRequestConsumer : IConsumer<SearchHubspotObjectRequest>
{
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IBus _bus;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly ILogger<SearchHubspotObjectRequestConsumer> _logger;

    public SearchHubspotObjectRequestConsumer(
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotConnectionService hubspotConnectionService,
        IBus bus,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        ILogger<SearchHubspotObjectRequestConsumer> logger)
    {
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotConnectionService = hubspotConnectionService;
        _bus = bus;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SearchHubspotObjectRequest> context)
    {
        var request = context.Message;

        var consumeId = Guid.NewGuid().ToString();

        try
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotSearchObjectRequestReceived,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            var connection = await _hubspotConnectionService.GetByIdAsync(
                request.ConnectionId,
                request.SleekflowCompanyId);

            var authentication =
                await _hubspotAuthenticationService.GetAsync(
                    connection.AuthenticationId,
                    request.SleekflowCompanyId);
            if (authentication == null)
            {
                throw new SfUnauthorizedException();
            }

            var objects = await _hubspotObjectService.SearchObjectsAsync(
                authentication,
                request.EntityTypeName,
                request.Conditions.Select(
                    c =>
                        new Sleekflow.CrmHub.Models.InflowActions.SearchObjectCondition(
                            c.FieldName,
                            c.Operator,
                            c.Value)).ToList());

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotSearchObjectRequestHandled,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    },
                });

            var result = objects.Count == 0 ? new Dictionary<string, object?>() : objects[0];
            await _bus.Publish(
                new OnHubspotCompleteStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    JsonConvert.SerializeObject(result)));
        }
        catch (Exception ex)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.HubspotSearchObjectRequestFailed,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "entity_type_name", request.EntityTypeName
                    }
                });

            _logger.LogError(
                ex,
                "SearchHubspotObjectRequest failed,"
                + " ConsumeId: {ConsumeId},"
                + " SleekflowCompanyId: {SleekflowCompanyId},"
                + " ConnectionId: {ConnectionId},"
                + " EntityTypeName: {EntityTypeName},"
                + " Conditions: {Conditions},"
                + " AggregateStepId: {AggregateStepId},"
                + " ProxyStateId: {ProxyStateId},"
                + " StackEntries: {StackEntries},",
                consumeId,
                request.SleekflowCompanyId,
                request.ConnectionId,
                request.EntityTypeName,
                JsonConvert.SerializeObject(request.Conditions),
                request.AggregateStepId,
                request.ProxyStateId,
                request.StackEntries);

            await _bus.Publish(
                new OnHubspotFailStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null,
                    ex));
        }
    }
}