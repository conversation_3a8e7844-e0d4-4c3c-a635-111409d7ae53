﻿using MassTransit;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.Models.ActionEvents.CrmHub;
using Sleekflow.Persistence;

namespace Sleekflow.CrmHub.Events;

public class CreateSchemafulObjectConsumerDefinition
    : ConsumerDefinition<CreateSchemafulObjectConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<CreateSchemafulObjectConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class CreateSchemafulObjectConsumer : IConsumer<CreateSchemafulObjectRequest>
{
    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;

    public CreateSchemafulObjectConsumer(
        ISchemaService schemaService,
        ISchemafulObjectService schemafulObjectService)
    {
        _schemaService = schemaService;
        _schemafulObjectService = schemafulObjectService;
    }

    public async Task Consume(ConsumeContext<CreateSchemafulObjectRequest> context)
    {
        var createSchemafulObjectRequest = context.Message;

        var schema = await _schemaService.GetAsync(
            createSchemafulObjectRequest.SchemaId,
            createSchemafulObjectRequest.SleekflowCompanyId);

        await _schemafulObjectService.CreateAndGetSchemafulObjectAsync(
            schema,
            createSchemafulObjectRequest.SleekflowCompanyId,
            createSchemafulObjectRequest.PrimaryPropertyValue,
            createSchemafulObjectRequest.PropertyValues,
            createSchemafulObjectRequest.SleekflowUserProfileId,
            createSchemafulObjectRequest.CreatedVia,
            new AuditEntity.SleekflowStaff(
                string.Empty,
                null));

        await context.RespondAsync(new CreateSchemafulObjectReply());
    }
}