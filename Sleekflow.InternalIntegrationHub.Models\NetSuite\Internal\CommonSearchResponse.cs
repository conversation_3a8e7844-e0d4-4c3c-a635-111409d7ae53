using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

public class CommonSearchResponse
{
    [JsonProperty("links")]
    public List<Link> Links { get; set; }

    [JsonProperty("count")]
    public int Count { get; set; }

    [JsonProperty("hasMore")]
    public bool HasMore { get; set; }

    [JsonProperty("items")]
    public List<Item> Items { get; set; }

    [JsonProperty("offset")]
    public int Offset { get; set; }

    [JsonProperty("totalResults")]
    public int TotalResults { get; set; }

    [JsonConstructor]
    public CommonSearchResponse(
        List<Link> links,
        int count,
        bool hasMore,
        List<Item> items,
        int offset,
        int totalResults)
    {
        Links = links;
        Count = count;
        HasMore = hasMore;
        Items = items;
        Offset = offset;
        TotalResults = totalResults;
    }
}