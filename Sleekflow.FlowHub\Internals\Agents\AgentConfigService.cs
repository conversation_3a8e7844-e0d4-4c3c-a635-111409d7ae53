using System.Threading.Channels;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Agents.Actions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.States;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.Internals.Agents;

public interface IAgentConfigService
{
    Task<AgentConfig> GetAsync(ProxyState state, string? configIdExpr);

    Task<AgentConfig> GetAsync(string sleekflowCompanyId, string agentConfigId);
}

public class AgentConfigService : IAgentConfigService, IScopedService
{
    private const int DefaultNumberOfPreviousMessagesInChatHistoryAvailableAsContext = 10;
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IRequestClient<GetAgentConfigRequest> _getAgentConfigRequestClient;

    public AgentConfigService(
        IStateEvaluator stateEvaluator,
        IRequestClient<GetAgentConfigRequest> getAgentConfigRequestClient)
    {
        _stateEvaluator = stateEvaluator;
        _getAgentConfigRequestClient = getAgentConfigRequestClient;
    }

    public async Task<AgentConfig> GetAsync(ProxyState state, string? configIdExpr)
    {
        if (string.IsNullOrEmpty(configIdExpr))
        {
            return DefaultAgentConfig;
        }

        var companyAgentConfigId = await _stateEvaluator.EvaluateExpressionAsync<string>(state, configIdExpr);

        var agentConfigReply = await _getAgentConfigRequestClient.GetResponse<GetAgentConfigResponse>(
            new GetAgentConfigRequest(
                companyAgentConfigId!,
                state.Identity.SleekflowCompanyId));
        return GetConfig(agentConfigReply.Message);
    }

    // Agent Config Mapper method
    private static AgentConfig GetConfig(GetAgentConfigResponse config)
    {
        AgentActions? actions = null;
        if (config.Actions != null)
        {
            var json = JsonConvert.SerializeObject(config.Actions);
            actions = JsonConvert.DeserializeObject<AgentActions>(json);
        }
        return new AgentConfig(
            config.Id,
            config.Name,
            config.IsChatHistoryEnabledAsContext,
            config.IsContactPropertiesEnabledAsContext,
            config.NumberOfPreviousMessagesInChatHistoryAvailableAsContext
            ?? DefaultNumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            config.ChannelType ?? ChannelTypes.WhatsAppCloudApi,
            config.ChannelId,
            actions,
            config.PromptInstruction);
    }

    public async Task<AgentConfig> GetAsync(string sleekflowCompanyId, string agentConfigId)
    {
        var agentConfigReply = await _getAgentConfigRequestClient.GetResponse<GetAgentConfigResponse>(
            new GetAgentConfigRequest(agentConfigId, sleekflowCompanyId));
        return GetConfig(agentConfigReply.Message);
    }

    // Default Agent Config
    private static AgentConfig DefaultAgentConfig => new(
        string.Empty,
        string.Empty,
        isChatHistoryEnabledAsContext: false,
        isContactPropertiesEnabledAsContext: false,
        numberOfPreviousMessagesInChatHistoryAvailableAsContext:
        DefaultNumberOfPreviousMessagesInChatHistoryAvailableAsContext,
        channelType: ChannelTypes.WhatsAppCloudApi,
        channelId: null,
        actions: null,
        promptInstruction: null);
}