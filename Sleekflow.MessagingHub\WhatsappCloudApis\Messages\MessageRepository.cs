using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Messages;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Messages;

public interface IMessageRepository : IRepository<Message>
{
    Task PatchMessageResponse(
        Message message,
        Dictionary<string, object?>? response = default,
        Dictionary<string, object?>? error = default);
}

public class MessageRepository : BaseRepository<Message>, IMessageRepository, ISingletonService
{
    private readonly ILogger<MessageRepository> _logger;

    public MessageRepository(
        ILogger<MessageRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
        _logger = logger;
    }

    public async Task PatchMessageResponse(
        Message message,
        Dictionary<string, object?>? response = default,
        Dictionary<string, object?>? error = default)
    {
        var isPatched = await <PERSON><PERSON><PERSON>(
            message.Id,
            message.SleekflowCompanyId,
            error == default
                ? new List<PatchOperation>
                {
                    Replace(Message.PropertyNameMessageResponse, response),
                    Replace(Message.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
                }
                : new List<PatchOperation>
                {
                    Replace(Message.PropertyNameException, error),
                    Replace(Message.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
                }) == 1;
        if (isPatched == false)
        {
            _logger.LogError("Unable to patch the message {Message}, error {Error}", response, error);
        }
    }
}