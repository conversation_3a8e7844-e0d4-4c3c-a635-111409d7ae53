﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetTypeFieldValues : ITrigger
{
    private readonly IEntityRepository _entityRepository;

    public GetTypeFieldValues(
        IEntityRepository entityRepository)
    {
        _entityRepository = entityRepository;
    }

    public class GetTypeFieldValuesInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [Required]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonConstructor]
        public GetTypeFieldValuesInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string fieldName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FieldName = fieldName;
        }
    }

    public class GetTypeFieldValuesOutput
    {
        [JsonProperty("values")]
        public List<object?> Values { get; set; }

        [JsonConstructor]
        public GetTypeFieldValuesOutput(
            List<object?> values)
        {
            Values = values;
        }
    }

    public async Task<GetTypeFieldValuesOutput> F(
        GetTypeFieldValuesInput getTypeFieldValuesInput)
    {
        var sleekflowCompanyId = getTypeFieldValuesInput.SleekflowCompanyId;
        var fieldName = getTypeFieldValuesInput.FieldName;
        var entityTypeName = getTypeFieldValuesInput.EntityTypeName;

        var queryDefinition = new QueryDefinition(
                $"SELECT DISTINCT e[@fieldName][\"{SnapshottedValue.PropertyNameValue}\"] v " +
                "FROM %%CONTAINER_NAME%% e " +
                "WHERE e.sys_sleekflow_company_id = @sysSleekflowCompanyId AND e.sys_entity_type_name = @sysEntityTypeName AND e.sys_type_name = 'Entity' ")
            .WithParameter("@sysSleekflowCompanyId", sleekflowCompanyId)
            .WithParameter("@fieldName", fieldName)
            .WithParameter("@sysEntityTypeName", entityTypeName);

        var objs = await _entityRepository.GetObjectsAsync(queryDefinition, 200);

        var distinctValues = objs
            .Select(
                obj =>
                {
                    obj.TryGetValue("v", out var value);
                    return value ?? null;
                })
            .Distinct()
            .ToList();

        return new GetTypeFieldValuesOutput(distinctValues);
    }
}