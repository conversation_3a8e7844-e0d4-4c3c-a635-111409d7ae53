﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.CustomObjects;

[TriggerGroup(TriggerGroups.InflowActions)]
public class GetLoopThroughSchemafulObjectsProgress
    : ITrigger<
        GetLoopThroughSchemafulObjectsProgress.GetLoopThroughSchemafulObjectsProgressInput,
        GetLoopThroughSchemafulObjectsProgress.GetLoopThroughSchemafulObjectsProgressOutput>
{
    private readonly ICustomObjectIntegratorService _customObjectIntegratorService;

    public GetLoopThroughSchemafulObjectsProgress(ICustomObjectIntegratorService customObjectIntegratorService)
    {
        _customObjectIntegratorService = customObjectIntegratorService;
    }

    public class GetLoopThroughSchemafulObjectsProgressInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public GetLoopThroughSchemafulObjectsProgressInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class GetLoopThroughSchemafulObjectsProgressOutput
    {
        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public GetLoopThroughSchemafulObjectsProgressOutput(
            int count,
            DateTime lastUpdateTime,
            string status)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            Status = status;
        }
    }

    public async Task<GetLoopThroughSchemafulObjectsProgressOutput?> F(
        GetLoopThroughSchemafulObjectsProgressInput getLoopThroughSchemafulObjectsProgressInput)
    {
        var output = await _customObjectIntegratorService.GetLoopThroughObjectsProgressAsync(
            getLoopThroughSchemafulObjectsProgressInput.SleekflowCompanyId,
            getLoopThroughSchemafulObjectsProgressInput.FlowHubWorkflowId,
            getLoopThroughSchemafulObjectsProgressInput.FlowHubWorkflowVersionedId);

        if (output is null)
        {
            return null;
        }

        return new GetLoopThroughSchemafulObjectsProgressOutput(
            output.Count,
            output.LastUpdateTime,
            output.Status);
    }
}