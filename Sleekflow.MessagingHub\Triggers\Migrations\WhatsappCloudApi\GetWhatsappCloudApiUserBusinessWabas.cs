using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;

namespace Sleekflow.MessagingHub.Triggers.Migrations.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Migrations)]
public class GetWhatsappCloudApiUserBusinessWabas
    : ITrigger<
        GetWhatsappCloudApiUserBusinessWabas.GetWhatsappCloudApiUserBusinessWabasInput,
        GetWhatsappCloudApiUserBusinessWabas.GetWhatsappCloudApiUserBusinessWabasOutput>
{
    private readonly IMigrationService _migrationService;

    public GetWhatsappCloudApiUserBusinessWabas(IMigrationService migrationService)
    {
        _migrationService = migrationService;
    }

    public class GetWhatsappCloudApiUserBusinessWabasInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("user_access_token")]
        public string UserAccessToken { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiUserBusinessWabasInput(
            string sleekflowCompanyId,
            string userAccessToken,
            string facebookBusinessId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UserAccessToken = userAccessToken;
            FacebookBusinessId = facebookBusinessId;
        }
    }

    public class GetWhatsappCloudApiUserBusinessWabasOutput
    {
        [JsonProperty("user_business_wabas")]
        public List<GetWhatsappBusinessAccountSubscribedAppsResponse> UserBusinessWabas { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiUserBusinessWabasOutput(
            List<GetWhatsappBusinessAccountSubscribedAppsResponse> userBusinessWabas)
        {
            UserBusinessWabas = userBusinessWabas;
        }
    }

    public async Task<GetWhatsappCloudApiUserBusinessWabasOutput> F(
        GetWhatsappCloudApiUserBusinessWabasInput getUserBusinessWabasInput)
    {
        return new GetWhatsappCloudApiUserBusinessWabasOutput(
            await _migrationService.GetWhatsappCloudApiUserBusinessWabasAsync(
                getUserBusinessWabasInput.SleekflowCompanyId,
                getUserBusinessWabasInput.UserAccessToken,
                getUserBusinessWabasInput.FacebookBusinessId));
    }
}