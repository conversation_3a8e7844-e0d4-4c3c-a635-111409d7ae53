﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.KrakenD/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="buildArgs">
          <list>
            <DockerEnvVarImpl>
              <option name="name" value="FILE_NAME" />
              <option name="value" value="krakend.json" />
            </DockerEnvVarImpl>
          </list>
        </option>
        <option name="containerName" value="krakend" />
        <option name="envVars">
          <list>
            <DockerEnvVarImpl>
              <option name="name" value="AUDIT_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7093" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="AUDIT_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="AUTH_0_AUDIENCE" />
              <option name="value" value="https://api-dev.sleekflow.io" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="AUTH_0_JWK_URL" />
              <option name="value" value="https://sso-dev.sf.chat/.well-known/jwks.json" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="COMMERCE_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7085" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="COMMERCE_HUB_KEY" />
              <option name="value" value="w7zJUyirdVnNom4M6YTyxgtbTQDQL2gxHPRbKy9sSXaraX6GC4Ru8I1ejjpFS0LnSnJrRxAtO9YxxrRMGqj2H5EycUywMgQc0RhKWmQvmAe93j5jMJqunIG8aYilMp8Q" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="CRM_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7071" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="CRM_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="DYNAMICS365_INTEGRATOR_HOST" />
              <option name="value" value="http://host.docker.internal:7089" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="DYNAMICS365_INTEGRATOR_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="EMAIL_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7083" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="EMAIL_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="FC_ENABLE" />
              <option name="value" value="1" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="FLOW_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7083" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="FLOW_HUB_INTEGRATOR_HOST" />
              <option name="value" value="http://host.docker.internal:7116" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="FLOW_HUB_INTEGRATOR_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="FLOW_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="HUBSPOT_INTEGRATOR_HOST" />
              <option name="value" value="http://host.docker.internal:7075" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="HUBSPOT_INTEGRATOR_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="INTELLIGENT_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7102" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="INTELLIGENT_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="INTERNAL_INTEGRATION_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7118" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="INTERNAL_INTEGRATION_HUB_KEY" />
              <option name="value" value="LxiRidS8SN9XY2rqu2GpQPih9kuxfNbcn5nxewNwmHmxdMDundJVbf4BfwZVMcjX" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="KRAKEND_ALLOW_INSECURE_CONNECTIONS" />
              <option name="value" value="true" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="MESSAGING_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7081" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="MESSAGING_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="PUBLIC_API_GATEWAY_HOST" />
              <option name="value" value="http://host.docker.internal:7094" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="PUBLIC_API_GATEWAY_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="SALESFORCE_INTEGRATOR_HOST" />
              <option name="value" value="http://host.docker.internal:7073" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="SALESFORCE_INTEGRATOR_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="SHARE_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7093" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="SHARE_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="SUPPORT_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7111" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="SUPPORT_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" />
              <option name="value" value="/Internals/GetUserAuthenticationDetails" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="TENANT_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7104" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="TENANT_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="TICKETING_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7114" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="USER_EVENT_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7108" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="USER_EVENT_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="WEBHOOK_HUB_HOST" />
              <option name="value" value="http://host.docker.internal:7083" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="WEBHOOK_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="TICKETING_HUB_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="OPA_ENDPOINT" />
              <option name="value" value="http://host.docker.internal:8181" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="GOOGLE_SHEETS_INTEGRATOR_HOST" />
              <option name="value" value="http://host.docker.internal:7079" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="GOOGLE_SHEETS_INTEGRATOR_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="ZOHO_INTEGRATOR_HOST" />
              <option name="value" value="http://host.docker.internal:7106" />
            </DockerEnvVarImpl>
            <DockerEnvVarImpl>
              <option name="name" value="ZOHO_INTEGRATOR_KEY" />
              <option name="value" value="a12da7c775d00cada5b1ee611d3f6dca" />
            </DockerEnvVarImpl>
          </list>
        </option>
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="8080" />
              <option name="hostPort" value="8080" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="sourceFilePath" value="Sleekflow.KrakenD/Dockerfile" />
      </settings>
    </deployment>
    <EXTENSION ID="com.jetbrains.rider.docker.debug" isFastModeEnabled="false" isSslEnabled="false" />
    <method v="2" />
  </configuration>
</component>