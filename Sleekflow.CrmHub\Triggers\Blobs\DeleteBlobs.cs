﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Blobs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Blobs;

[TriggerGroup(TriggerGroups.Blobs)]
public class DeleteBlobs
    : ITrigger<
        DeleteBlobs.DeleteBlobsInput,
        DeleteBlobs.DeleteBlobsOutput>
{
    private readonly IBlobService _blobsService;

    public DeleteBlobs(IBlobService blobsService)
    {
        _blobsService = blobsService;
    }

    public class DeleteBlobsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [Required]
        [JsonProperty("blob_type")]
        public string BlobType { get; set; }

        [JsonConstructor]
        public DeleteBlobsInput(string sleekflowCompanyId, List<string> blobNames, string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobNames = blobNames;
            BlobType = blobType;
        }
    }

    public class DeleteBlobsOutput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("blob_names")]
        public List<string> BlobNames { get; set; }

        [JsonProperty("blob_type")]
        public string BlobType { get; set; }

        [JsonConstructor]
        public DeleteBlobsOutput(string sleekflowCompanyId, List<string> blobNames, string blobType)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobNames = blobNames;
            BlobType = blobType;
        }
    }

    public async Task<DeleteBlobsOutput> F(DeleteBlobsInput deleteBlobsInput)
    {
        await _blobsService.DeleteBlobs(
            deleteBlobsInput.SleekflowCompanyId,
            deleteBlobsInput.BlobNames,
            deleteBlobsInput.BlobType);

        return new DeleteBlobsOutput(
            deleteBlobsInput.SleekflowCompanyId,
            deleteBlobsInput.BlobNames,
            deleteBlobsInput.BlobType);
    }
}