using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Migrations.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Migrations)]
public class RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCode
    : ITrigger<
        RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCode.
        RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput,
        RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCode.
        RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutput>
{
    private readonly IMigrationService _migrationService;
    private readonly IWabaService _wabaService;

    public RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCode(IMigrationService migrationService, IWabaService wabaService)
    {
        _migrationService = migrationService;
        _wabaService = wabaService;
    }

    public class RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Required]
        [JsonProperty("facebook_phone_number_id")]
        public string FacebookPhoneNumberId { get; set; }

        [Required]
        [JsonProperty("code_method")]
        public string CodeMethod { get; set; }

        [Required]
        [JsonProperty("language")]
        public string Language { get; set; }

        [JsonConstructor]
        public RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput(
            string sleekflowCompanyId,
            string facebookWabaId,
            string facebookPhoneNumberId,
            string codeMethod,
            string language)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookWabaId = facebookWabaId;
            FacebookPhoneNumberId = facebookPhoneNumberId;
            CodeMethod = codeMethod;
            Language = language;
        }
    }

    public class RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutput> F(
        RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput
            requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput)
    {
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(
            requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput.FacebookWabaId);

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        return new RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutput(
            await _migrationService.RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeAsync(
                requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput.SleekflowCompanyId,
                requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput.FacebookWabaId,
                requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput.FacebookPhoneNumberId,
                requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput.CodeMethod,
                requestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput.Language,
                hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null));
    }
}