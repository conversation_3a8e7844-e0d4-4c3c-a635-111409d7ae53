using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class DisableWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;

    public DisableWorkflow(
        IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public class DisableWorkflowInput : IHasSleekflowStaff, Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DisableWorkflowInput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowVersionedId = workflowVersionedId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DisableWorkflowOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public DisableWorkflowOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<DisableWorkflowOutput> F(DisableWorkflowInput disableWorkflowInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            disableWorkflowInput.SleekflowStaffId,
            disableWorkflowInput.SleekflowStaffTeamIds);

        var workflow = await _workflowService.DisableWorkflowAsync(
            disableWorkflowInput.WorkflowVersionedId,
            disableWorkflowInput.SleekflowCompanyId,
            sleekflowStaff);

        return new DisableWorkflowOutput(new WorkflowDto(workflow));
    }
}