using Microsoft.AspNetCore.Mvc;
using Sleekflow.MessagingHub.Triggers.Webhook.WhatsappCloudApi;
using Sleekflow.Mvc.SwaggerConfiguration.Parameter;

namespace Sleekflow.MessagingHub.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("[Controller]")]
public class WhatsappCloudApiWebhookController : ControllerBase
{
    private readonly WhatsappCloudApiMessage _whatsappCloudApiMessage;
    private readonly ILogger<WhatsappCloudApiWebhookController> _logger;
    private readonly GetWhatsappCloudApiWebhook _getWhatsappCloudApiWebhook;

    public WhatsappCloudApiWebhookController(
        WhatsappCloudApiMessage whatsappCloudApiMessage,
        ILogger<WhatsappCloudApiWebhookController> logger,
        GetWhatsappCloudApiWebhook getWhatsappCloudApiWebhook)
    {
        _logger = logger;
        _whatsappCloudApiMessage = whatsappCloudApiMessage;
        _getWhatsappCloudApiWebhook = getWhatsappCloudApiWebhook;
    }

    [HttpGet]
    [SwaggerQuery(
        new[]
        {
            "hub.mode",
            "hub.challenge",
            "hub.verify_token"
        })]
    public Task<ActionResult> GetWhatsappCloudApiWebhook()
    {
        var query = HttpContext.Request.Query;
        var mode = query["hub.mode"].FirstOrDefault()!;
        var challenge = query["hub.challenge"].FirstOrDefault()!;
        var verifyToken = query["hub.verify_token"].FirstOrDefault()!;
        return !_getWhatsappCloudApiWebhook.F(verifyToken)
            ? Task.FromResult<ActionResult>(BadRequest("Invalid verify token"))
            : Task.FromResult<ActionResult>(Ok(challenge));
    }

    [HttpPost]
    public async Task<ActionResult> WhatsappCloudApiWebhook()
    {
        var requestBody = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
        return await _whatsappCloudApiMessage.F(requestBody) ? Ok() : BadRequest();
    }
}