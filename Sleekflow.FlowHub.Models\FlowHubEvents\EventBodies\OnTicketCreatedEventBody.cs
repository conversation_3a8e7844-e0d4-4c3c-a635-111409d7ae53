using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnTicketCreatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnTicketCreated; }
    }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [Required]
    [JsonProperty("ticket")]
    public OnTicketCommonEventBody Ticket { get; set; }

    [JsonConstructor]
    public OnTicketCreatedEventBody(
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        string contactId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId,
        OnTicketCommonEventBody ticket)
        : base(createdAt)
    {
        SleekflowStaffId = sleekflowStaffId;
        ContactId = contactId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
        Ticket = ticket;
    }
}

