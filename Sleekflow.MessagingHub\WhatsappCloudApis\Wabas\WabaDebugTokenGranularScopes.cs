using GraphApi.Client.Payloads;
using Sleekflow.JsonConfigs;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

public class WabaDebugTokenGranularScopes
{
    private const string WhatsappBusinessManagementScope = "whatsapp_business_management";
    private const string BusinessManagementScope = "business_management";

    public List<string> WabaIds { get; set; }

    public List<string>? BusinessIds { get; set; }

    public int? ExpiresAt { get; set; }

    public bool? IsValid { get; set; }

    public WabaDebugTokenGranularScopes(
        ILogger logger,
        DebugTokenResponse debugTokenResponse)
    {
        var jsonSerializerSettings = JsonConfig.DefaultJsonSerializerSettings;

        BusinessIds = GetGranularScopes(debugTokenResponse, BusinessManagementScope);
        BusinessIds ??= new List<string>();
        var whatsappBusinessManagementScopes = GetGranularScopes(debugTokenResponse, WhatsappBusinessManagementScope);
        WabaIds = whatsappBusinessManagementScopes;
        ExpiresAt = debugTokenResponse.Data.ExpiresAt;
        IsValid = debugTokenResponse.Data.IsValid;
    }

    private static List<string> GetGranularScopes(
        DebugTokenResponse debugTokenResponse,
        string scope)
    {
        var scopeValue = debugTokenResponse.Data.GranularScopes.Find(x => x.Scope == scope);
        return scopeValue?.TargetIds ?? new List<string>();
    }
}