using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class AssignedTeamChangedLogData
{
    [JsonProperty("original_team")]
    public TeamLogData? OriginalTeam { get; set; }

    [JsonProperty("new_team")]
    public TeamLogData? NewTeam { get; set; }

    [JsonConstructor]
    public AssignedTeamChangedLogData(
        TeamLogData? originalTeam,
        TeamLogData? newTeam)
    {
        OriginalTeam = originalTeam;
        NewTeam = newTeam;
    }
}

public class TeamLogData
{
    [JsonProperty("team_id")]
    public long TeamId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonConstructor]
    public TeamLogData(long teamId, string name)
    {
        TeamId = teamId;
        Name = name;
    }
}