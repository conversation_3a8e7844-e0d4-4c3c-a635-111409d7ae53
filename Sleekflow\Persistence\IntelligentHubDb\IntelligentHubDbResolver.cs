﻿using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.IntelligentHubDb;

public interface IIntelligentHubDbResolver : IContainerResolver
{
}

public class IntelligentHubDbResolver : IIntelligentHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public IntelligentHubDbResolver(IIntelligentHubDbConfig intelligentHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            intelligentHubDbConfig.Endpoint,
            intelligentHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}