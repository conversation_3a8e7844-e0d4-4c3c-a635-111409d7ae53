﻿namespace Sleekflow.Exceptions.Hubspot;

public class SfMissingNecessaryFieldFiltersException : ErrorCodeException
{
    public SfMissingNecessaryFieldFiltersException(List<string> fieldFilterNames)
        : base(
            ErrorCodeConstant.SfMissingNecessaryFieldFiltersException,
            $"{string.Join(",", fieldFilterNames)} are necessary.",
            new Dictionary<string, object?>()
            {
                {
                    "fieldFilterNames", fieldFilterNames
                }
            })
    {
    }
}