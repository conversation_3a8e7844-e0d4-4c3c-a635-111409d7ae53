using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class OnRecommendedReplyGenerationEmitEvent
{
    [JsonProperty("recommended_reply_step_id")]
    public string RecommendReplyStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("stream_sequence_number")]
    public int StreamSequenceNumber { get; set; }

    [JsonProperty("partial_recommended_reply")]
    public string PartialRecommendedReply { get; set; }

    [JsonConstructor]
    public OnRecommendedReplyGenerationEmitEvent(
        string recommendReplyStepId,
        string proxyStateId,
        int streamSequenceNumber,
        string partialRecommendedReply)
    {
        RecommendReplyStepId = recommendReplyStepId;
        ProxyStateId = proxyStateId;
        StreamSequenceNumber = streamSequenceNumber;
        PartialRecommendedReply = partialRecommendedReply;
    }
}