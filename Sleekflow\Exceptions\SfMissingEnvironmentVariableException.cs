namespace Sleekflow.Exceptions;

public class SfMissingEnvironmentVariableException : ErrorCodeException
{
    public SfMissingEnvironmentVariableException(string envVarName)
        : base(
            ErrorCodeConstant.SfMissingEnvironmentVariableException,
            $"The envVarName {envVarName} is missing",
            new Dictionary<string, object?>
            {
                {
                    "envVarName", envVarName
                }
            })
    {
    }
}