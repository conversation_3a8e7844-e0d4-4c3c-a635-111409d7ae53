using System.Web;
using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Models.Webhook;
using Sleekflow.Integrator.Hubspot.Services;
using Sleekflow.Integrator.Hubspot.UserMappingConfigs;
using Sleekflow.JsonConfigs;
using Sleekflow.Mvc.SwaggerConfiguration.Parameter;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Hubspot.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    private readonly ILogger<PublicController> _logger;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotSecurityService _hubspotSecurityService;
    private readonly IBus _bus;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IHubspotUserMappingConfigService _hubspotUserMappingConfigService;

    public PublicController(
        ILogger<PublicController> logger,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotSecurityService hubspotSecurityService,
        IBus bus,
        IHubspotConnectionService hubspotConnectionService,
        IHubspotUserMappingConfigService hubspotUserMappingConfigService)
    {
        _logger = logger;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotSecurityService = hubspotSecurityService;
        _bus = bus;
        _hubspotConnectionService = hubspotConnectionService;
        _hubspotUserMappingConfigService = hubspotUserMappingConfigService;
    }

    [HttpGet]
    [Route("healthz")]
    public Task<ContentResult> Healthz()
    {
        return Task.FromResult(
            new ContentResult
            {
                ContentType = "text/plain", Content = "HEALTH"
            });
    }

    [HttpGet]
    [Route("AuthenticateCallback")]
    [SwaggerQuery(
        new[]
        {
            "code",
            "state"
        })]
    public async Task<IActionResult> RunAsync()
    {
        var code = HttpContext.Request.Query["code"];
        var encryptedState = HttpContext.Request.Query["state"];

        var (authentication, isV2Authentication, returnToUrl, successUrl, failureUrl) =
            await _hubspotAuthenticationService.HandleAuthenticateCallbackAndStoreAsync(
                code!,
                encryptedState!);

        try
        {
            if (authentication == null)
            {
                throw new Exception("Unable to handle the authentication callback");
            }

            if (isV2Authentication)
            {
                var connection = await _hubspotConnectionService.GetByAuthenticationIdAsync(
                    authentication.SleekflowCompanyId,
                    authentication.Id);

                if (connection is null)
                {
                    connection = await _hubspotConnectionService.CreateAndGetAsync(
                        authentication.SleekflowCompanyId,
                        authentication.AccountInformation?.PortalId.ToString(),
                        authentication.Id,
                        authentication.AccountInformation?.PortalId.ToString(),
                        "production",
                        true);

                    await _hubspotUserMappingConfigService.CreateAndGetAsync(
                        authentication.SleekflowCompanyId,
                        connection.Id,
                        new List<UserMapping>());
                }
                else
                {
                    await _hubspotConnectionService.PatchAsync(
                        connection.Id,
                        authentication.SleekflowCompanyId,
                        authentication.AccountInformation?.PortalId.ToString(),
                        true);
                }

                string encodedSuccessUrl;

                var successUri = new Uri(successUrl!);

                var existingQueryStrings = HttpUtility.ParseQueryString(successUri.Query);
                if (existingQueryStrings.Count == 0)
                {
                    encodedSuccessUrl = successUrl + $"?connection_id={HttpUtility.HtmlEncode(connection.Id)}";
                }
                else
                {
                    var queryStrings = existingQueryStrings;
                    queryStrings["connection_id"] = HttpUtility.HtmlEncode(connection.Id);

                    encodedSuccessUrl = successUri.GetLeftPart(UriPartial.Path) + "?" + queryStrings;
                }

                return new ContentResult
                {
                    ContentType = "text/html",
                    Content = $"<meta http-equiv=\"refresh\" content=\"0;URL='{encodedSuccessUrl}'\" />"
                };
            }

            await _bus.Publish(
                new OnProviderInitializedEvent(
                    authentication.SleekflowCompanyId,
                    "hubspot-integrator"),
                context => { context.ConversationId = Guid.Parse(authentication.SleekflowCompanyId); });

            return new ContentResult
            {
                ContentType = "text/html",
                Content =
                    $"Authenticated. You will be redirected back to our main app shortly. Thank you. <meta http-equiv=\"refresh\" content=\"5;URL='{returnToUrl}'\" />"
            };
        }
        catch (Exception exception)
        {
            var gen = RandomStringUtils.Gen(10);

            _logger.LogError(exception, "Caught an exception. requestId {RequestId}", gen);

            if (isV2Authentication)
            {
                return new ContentResult
                {
                    ContentType = "text/html",
                    Content = "<meta http-equiv=\"refresh\" content=\"0;URL='" + failureUrl + "'\" />"
                };
            }

            return new ContentResult
            {
                ContentType = "text/html",
                Content = $"Still Unauthenticated. Please contact our support team. Thank you. RequestId = {gen}"
            };
        }
    }

    [HttpPost]
    [Route("HubspotCallback")]
    public async Task HubspotCallback()
    {
        var hasXHubspotSignatureHeader = HttpContext.Request.Headers.ContainsKey("X-HubSpot-Signature");
        if (!hasXHubspotSignatureHeader)
        {
            _logger.LogError("X-HubSpot-Signature should be provided");
            throw new SfInternalErrorException("X-HubSpot-Signature should be provided");
        }

        var xHubspotSignature = HttpContext.Request.Headers["X-HubSpot-Signature"].ToString();
        var hubspotCallbackPayloadsStr = await new StreamReader(Request.Body).ReadToEndAsync();

        var isHubspotCallbackWebhookValid =
            _hubspotSecurityService.IsHubspotCallbackWebhookValid(
                hubspotCallbackPayloadsStr,
                xHubspotSignature);
        if (!isHubspotCallbackWebhookValid)
        {
            _logger.LogError(
                "The request doesn't match the signature, X-HubSpot-Signature {XHubSpotSignature}",
                xHubspotSignature);
            throw new SfInternalErrorException("The request doesn't match the signature");
        }

        var hubspotCallbackPayloads =
            JsonConvert.DeserializeObject<List<HubspotCallbackPayload>>(
                hubspotCallbackPayloadsStr,
                JsonConfig.DefaultJsonSerializerSettings);
        foreach (var hubspotCallbackPayload in hubspotCallbackPayloads!)
        {
            var subscriptionType = hubspotCallbackPayload.SubscriptionType;
            if (subscriptionType != "contact.privacyDeletion")
            {
                _logger.LogWarning(
                    "Incorrect SubscriptionType. hubspotCallbackPayload {HubspotCallbackPayload}, subscriptionType {SubscriptionType}",
                    hubspotCallbackPayloadsStr,
                    subscriptionType);
                continue;
            }

            var hubspotAuthentication =
                await _hubspotAuthenticationService.GetHubspotAuthenticationWithPortalIdAsync(
                    hubspotCallbackPayload.PortalId);
            if (hubspotAuthentication == null)
            {
                _logger.LogError(
                    "Unable to locate the HubspotAuthentication with PortalId {PortalId}",
                    hubspotCallbackPayload.PortalId);
                throw new SfInternalErrorException(
                    $"Unable to locate the HubspotAuthentication with PortalId {hubspotCallbackPayload.PortalId}");
            }

            var onObjectOperationEvent = new OnObjectOperationEvent(
                new Dictionary<string, object?>(),
                OnObjectOperationEvent.OperationDeleteObject,
                "hubspot-integrator",
                hubspotAuthentication.SleekflowCompanyId,
                hubspotCallbackPayload.ObjectId.ToString(),
                "Contact",
                null);
            await _bus.Publish(onObjectOperationEvent);
        }
    }
}