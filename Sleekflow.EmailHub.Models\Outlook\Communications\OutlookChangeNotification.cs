using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Outlook.Communications;

public class OutlookChangeNotifications
{
    [JsonProperty("value")]
    public List<OutlookChangeNotification> ChangeNotifications { get; set; }

    [JsonConstructor]
    public OutlookChangeNotifications(List<OutlookChangeNotification> changeNotifications)
    {
        ChangeNotifications = changeNotifications;
    }
}

public class OutlookChangeNotification
{
    [JsonProperty("subscriptionId")]
    public string SubscriptionId { get; set; }

    [JsonProperty("subscriptionExpirationDateTime")]
    public DateTimeOffset SubscriptionExpirationDateTime { get; set; }

    [JsonProperty("changeType")]
    public string ChangeType { get; set; }

    [JsonProperty("resource")]
    public string Resource { get; set; }

    [JsonProperty("resourceData")]
    public ResourceData ResourceData { get; set; }

    [JsonProperty("clientState")]
    public string ClientState { get; set; }

    [JsonProperty("tenantId")]
    public string TenantId { get; set; }

    [JsonConstructor]
    public OutlookChangeNotification(
        string subscriptionId,
        DateTimeOffset subscriptionExpirationDateTime,
        string changeType,
        string resource,
        ResourceData resourceData,
        string clientState,
        string tenantId)
    {
        SubscriptionId = subscriptionId;
        SubscriptionExpirationDateTime = subscriptionExpirationDateTime;
        ChangeType = changeType;
        Resource = resource;
        ResourceData = resourceData;
        ClientState = clientState;
        TenantId = tenantId;
    }
}

public class ResourceData
{
    [JsonProperty("@odata.type")]
    public string ODateType { get; set; }

    [JsonProperty("@odata.id")]
    public string ODateId { get; set; }

    [JsonProperty("@odata.etag")]
    public string ODateETag { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonConstructor]
    public ResourceData(
        string oDateType,
        string oDateId,
        string oDateETag,
        string id)
    {
        ODateType = oDateType;
        ODateId = oDateId;
        ODateETag = oDateETag;
        Id = id;
    }
}