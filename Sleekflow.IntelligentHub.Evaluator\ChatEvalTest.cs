using System.Globalization;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using CsvHelper;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

[Parallelizable(ParallelScope.Children)]
public partial class ChatEvalTest
{
    // AsyncLocal to hold the TestId for the current execution context
    internal static readonly AsyncLocal<string?> CurrentTestId = new ();

    private static readonly OverallScore OverallScoreCalculator = new ();

    private static readonly List<(string TestId, string Scenario, ChatEvalQuestion Question, ChatEvalResult[] Results)>
        AllTestResults = [];

    [TestCaseSource(nameof(GetTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public async Task EvaluateChatPerformanceTest(ChatEvalQuestion chatEvalQuestion)
    {
        var testId = Guid.NewGuid();
        CurrentTestId.Value = testId.ToString(); // Set the test ID in AsyncLocal

        // Calculate and consolidate results
        var evalResults = await RunEvaluationAsync(chatEvalQuestion, testId);
        var (highestScore, bestPerformers) = CalculateBestPerformers(evalResults);

        // Store results for reporting
        OverallScoreCalculator.AddChatEvalResults(evalResults);
        AllTestResults.Add((testId.ToString(), chatEvalQuestion.Scenario, chatEvalQuestion, evalResults));

        // Print results to console
        PrintResults(chatEvalQuestion, testId, evalResults, highestScore, bestPerformers);

        // Verify tool calls
        AssertToolCalls(chatEvalQuestion, testId);

        // Clear the test ID after the test completes
        CurrentTestId.Value = null;
    }

    private async Task<ChatEvalResult[]> RunEvaluationAsync(ChatEvalQuestion chatEvalQuestion, Guid testId)
    {
        var fixture = new ChatEvalFixture();
        return await fixture.EvaluateAsync(
            chatEvalQuestion,
            testId,
            CancellationToken.None);
    }

    private (double HighestScore, ChatEvalResult[] BestPerformers) CalculateBestPerformers(ChatEvalResult[] evalResults)
    {
        var highestScore = evalResults.Max(result => result.AnswerScoringScore);
        var bestPerformers = evalResults.Where(result => Math.Abs(result.AnswerScoringScore - highestScore) < 0.01)
            .ToArray();

        return (highestScore, bestPerformers);
    }

    private void PrintResults(
        ChatEvalQuestion chatEvalQuestion,
        Guid testId,
        ChatEvalResult[] evalResults,
        double highestScore,
        ChatEvalResult[] bestPerformers)
    {
        var sb = new StringBuilder();

        // Header information
        sb.AppendLine($"Test Id: {testId}");
        sb.AppendLine($"Scenario: {chatEvalQuestion.Scenario}");
        sb.AppendLine();

        // Scores section
        sb.AppendLine("----------------------- Scores ------------------------");
        foreach (var evalResult in evalResults)
        {
            sb.AppendLine(
                $"{evalResult.Label} ({evalResult.ElapsedMilliseconds / 1000d}s); Primary Score: {Math.Round(evalResult.AnswerScoringScore, 2)}; RAG Score: {Math.Round(evalResult.RagOutputScoringScore, 2)}");
        }

        sb.AppendLine();

        // Diagnostics section
        sb.AppendLine("----------------------- Diagnostics ------------------------");
        foreach (var evalResult in evalResults)
        {
            sb.AppendLine($"{evalResult.Label} diagnostics:");
            sb.AppendLine(
                $"Primary Diagnostic: {JsonConvert.SerializeObject(evalResult.AnswerScoringDiagnostic.QualitativeAnalysis.DetailedFeedback)}");
            sb.AppendLine(
                $"RAG Diagnostic: {JsonConvert.SerializeObject(evalResult.RagOutputScoringDiagnostic)}");
        }

        sb.AppendLine();

        // Question section
        sb.AppendLine("----------------------- Question ------------------------");
        if (chatEvalQuestion.QuestionContexts is not null)
        {

            foreach (var chatMessageContent in chatEvalQuestion.QuestionContexts)
            {
                sb.AppendLine($"{chatMessageContent.Role}: {chatMessageContent.Content}");
            }
        }

        if (chatEvalQuestion.SfChatEntriesQuestionContexts is not null)
        {
            foreach (var chatMessageContent in chatEvalQuestion.SfChatEntriesQuestionContexts)
            {
                if (chatMessageContent.Bot is not null)
                {
                    sb.AppendLine($"Bot: {chatMessageContent.Bot}");
                }

                if (chatMessageContent.User is not null)
                {
                    sb.AppendLine($"User: {chatMessageContent.User}");
                }

                if (chatMessageContent.Files is not null)
                {
                    sb.AppendLine($"Files: {string.Join(", ", chatMessageContent.Files.Select(f => f.Url))}");
                }
            }
        }

        sb.AppendLine();

        // Expected answer section
        sb.AppendLine("----------------------- Expected answer ------------------------");
        sb.AppendLine(chatEvalQuestion.ModelAnswer);
        sb.AppendLine();

        // Test Results section
        sb.AppendLine("----------------------- Test Results ------------------------");
        sb.AppendLine($"Best performers: {string.Join(", ", bestPerformers.Select(e => e.Label))}");
        sb.AppendLine();

        // Best answers section
        foreach (var bestPerformer in bestPerformers)
        {
            sb.AppendLine("----------------------- Best answer ------------------------");
            sb.AppendLine($"{bestPerformer.Label} answer:");
            sb.AppendLine($"{bestPerformer.Answer}");
            sb.AppendLine();
        }

        // Remaining answers section
        sb.AppendLine("----------------------- Remaining answer ------------------------");
        foreach (var evalResult in evalResults.Where(evalResult => !bestPerformers.Contains(evalResult)))
        {
            sb.AppendLine($"{evalResult.Label} answer:");
            sb.AppendLine($"{evalResult.Answer}");
            sb.AppendLine();
        }

        // Output the entire result
        Console.Write(sb.ToString());
    }

    private static void AssertToolCalls(ChatEvalQuestion chatEvalQuestion, Guid testId)
    {
        // Verification for Lead Assignment
        if (chatEvalQuestion.ShouldAssignLead is not null)
        {
            Assert.That(
                chatEvalQuestion.AgentConfig?.LeadNurturingTools,
                Is.Not.Null,
                "LeadNurturingTools should be configured");
            Assert.That(
                chatEvalQuestion.AgentConfig?.LeadNurturingTools.AssignmentTool,
                Is.Not.Null,
                "AssignmentTool should be configured");

            switch (chatEvalQuestion.ShouldAssignLead)
            {
                case true:
                {
                    Assert.That(
                        MockSleekflowToolsPlugin.WasAssignToTeamCalled(testId.ToString()),
                        Is.True,
                        $"AssignToTeam should be called for test {testId}");
                    break;
                }

                case false:
                {
                    Assert.That(
                        MockSleekflowToolsPlugin.WasAssignToTeamCalled(testId.ToString()),
                        Is.False,
                        $"AssignToTeam should not be called for test {testId}");
                    break;
                }
            }
        }

        // Verification for Demo Scheduling
        if (chatEvalQuestion.ShouldScheduleDemo is not null)
        {
            Assert.That(
                chatEvalQuestion.AgentConfig?.LeadNurturingTools,
                Is.Not.Null,
                "LeadNurturingTools should be configured");
            Assert.That(
                chatEvalQuestion.AgentConfig?.LeadNurturingTools.DemoTool,
                Is.Not.Null,
                "DemoTool should be configured");

            switch (chatEvalQuestion.ShouldScheduleDemo)
            {
                case true:
                {
                    // Verify Chili Piper parameters if expected fields are defined
                    if (chatEvalQuestion is ChatEvalQuestion
                            {
                                ExpectedChiliPiperFields: not null
                            }
                            questionWithExpectedFields)
                    {
                        // Check that ScheduleDemoWithChiliPiper was called
                        var requestParams = MockChiliPiperPlugin.GetRequestParams(testId.ToString());
                        Assert.That(
                            requestParams,
                            Is.Not.Null,
                            $"ScheduleDemoWithChiliPiper should be called for test {testId}");

                        // Verify original fields contain all expected fields
                        foreach (var expectedField in questionWithExpectedFields.ExpectedChiliPiperFields)
                        {
                            Assert.That(
                                requestParams.OriginalFields.ContainsKey(expectedField.Key),
                                Is.True,
                                $"Original fields should contain key '{expectedField.Key}'");

                            Assert.That(
                                requestParams.OriginalFields[expectedField.Key],
                                Is.EqualTo(expectedField.Value),
                                $"Value for '{expectedField.Key}' should be '{expectedField.Value}' but was '{requestParams.OriginalFields[expectedField.Key]}'");
                        }

                        // If there's a mapping configuration, verify mapped fields
                        if (chatEvalQuestion.AgentConfig?.LeadNurturingTools.DemoTool.ChiliPiperConfig != null)
                        {
                            var mappings = chatEvalQuestion.AgentConfig?.LeadNurturingTools
                                .DemoTool
                                .ChiliPiperConfig
                                .FieldMappings;

                            foreach (var expectedField in questionWithExpectedFields.ExpectedChiliPiperFields)
                            {
                                if (mappings.TryGetValue(expectedField.Key, out var mappedFieldName))
                                {
                                    Assert.That(
                                        requestParams.MappedFields.ContainsKey(mappedFieldName),
                                        Is.True,
                                        $"Mapped fields should contain key '{mappedFieldName}' (mapped from '{expectedField.Key}')");

                                    Assert.That(
                                        requestParams.MappedFields[mappedFieldName],
                                        Is.EqualTo(expectedField.Value),
                                        $"Mapped value for '{mappedFieldName}' should be '{expectedField.Value}' but was '{requestParams.MappedFields[mappedFieldName]}'");
                                }
                            }
                        }
                    }
                    else
                    {
                        // Check that ScheduleDemoWithChiliPiper was called
                        var requestParams = MockChiliPiperPlugin.GetRequestParams(testId.ToString());
                        Assert.That(
                            requestParams,
                            Is.Not.Null,
                            $"ScheduleDemoWithChiliPiper should be called for test {testId}");
                    }

                    break;
                }

                case false:
                {
                    var requestParams = MockChiliPiperPlugin.GetRequestParams(testId.ToString());
                    Assert.That(
                        requestParams,
                        Is.Null,
                        $"ScheduleDemoWithChiliPiper should not be called for test {testId}");
                    break;
                }
            }
        }

        // Verification for Hubspot Contact Properties
        if (chatEvalQuestion.ShouldGetContactProperties is not null)
        {
            switch (chatEvalQuestion.ShouldGetContactProperties)
            {
                case true:
                {
                    Assert.That(
                        MockHubspotPlugin.WasGetContactPropertiesCalled(testId.ToString()),
                        Is.True,
                        $"GetContactProperties should be called for test {testId}");

                    // Verify parameters if expected
                    if (chatEvalQuestion.ExpectedHubspotParams is not null)
                    {
                        var contactProps = MockHubspotPlugin.GetContactPropertiesParams(testId.ToString());
                        Assert.That(contactProps, Is.Not.Null, "GetContactProperties parameters should not be null");

                        if (chatEvalQuestion.ExpectedHubspotParams.TryGetValue("email", out var expectedEmail))
                        {
                            Assert.That(
                                contactProps?.Email,
                                Is.EqualTo(expectedEmail),
                                $"Email parameter should be '{expectedEmail}' but was '{contactProps?.Email}'");
                        }

                        if (chatEvalQuestion.ExpectedHubspotParams.TryGetValue("phone", out var expectedPhone))
                        {
                            Assert.That(
                                contactProps?.Phone,
                                Is.EqualTo(expectedPhone),
                                $"Phone parameter should be '{expectedPhone}' but was '{contactProps?.Phone}'");
                        }
                    }

                    break;
                }

                case false:
                {
                    Assert.That(
                        MockHubspotPlugin.WasGetContactPropertiesCalled(testId.ToString()),
                        Is.False,
                        $"GetContactProperties should not be called for test {testId}");
                    break;
                }
            }
        }

        // Verification for Hubspot Contact Meetings
        if (chatEvalQuestion.ShouldGetContactMeetings is not null)
        {
            switch (chatEvalQuestion.ShouldGetContactMeetings)
            {
                case true:
                {
                    Assert.That(
                        MockHubspotPlugin.WasGetContactMeetingsCalled(testId.ToString()),
                        Is.True,
                        $"GetContactMeetings should be called for test {testId}");

                    // Verify parameters if expected
                    if (chatEvalQuestion.ExpectedHubspotParams is not null)
                    {
                        var contactMeetings = MockHubspotPlugin.GetContactMeetingsParams(testId.ToString());
                        Assert.That(contactMeetings, Is.Not.Null, "GetContactMeetings parameters should not be null");

                        if (chatEvalQuestion.ExpectedHubspotParams.TryGetValue("email", out var expectedEmail))
                        {
                            Assert.That(
                                contactMeetings?.Email,
                                Is.EqualTo(expectedEmail),
                                $"Email parameter should be '{expectedEmail}' but was '{contactMeetings?.Email}'");
                        }

                        if (chatEvalQuestion.ExpectedHubspotParams.TryGetValue("phone", out var expectedPhone))
                        {
                            Assert.That(
                                contactMeetings?.Phone,
                                Is.EqualTo(expectedPhone),
                                $"Phone parameter should be '{expectedPhone}' but was '{contactMeetings?.Phone}'");
                        }
                    }

                    break;
                }

                case false:
                {
                    Assert.That(
                        MockHubspotPlugin.WasGetContactMeetingsCalled(testId.ToString()),
                        Is.False,
                        $"GetContactMeetings should not be called for test {testId}");
                    break;
                }
            }
        }

        // Clear the state for this test ID after checking
        MockSleekflowToolsPlugin.ClearTestState(testId.ToString());
        MockChiliPiperPlugin.ClearTestState(testId.ToString());
        MockHubspotPlugin.ClearTestState(testId.ToString());
    }

    [OneTimeTearDown]
    public async Task OneTimeTearDown()
    {
        Console.WriteLine("Total score:");
        Console.WriteLine(await OverallScoreCalculator.GetReportString());

        // Generate CSV output
        var csvPath = Path.Combine(
            GetRootPath(),
            $"chat_eval_test_results_{DateTime.Now:yyyyMMdd_HHmmss}.csv");

        await using var writer = new StreamWriter(csvPath);
        await using var csv = new CsvWriter(writer, CultureInfo.InvariantCulture);

        // Write test results
        var records = AllTestResults.Select(result =>
        {
            var (testId, scenario, question, results) = result;
            var resultDict = results.ToDictionary(r => r.Label, r => r);

            var noRagResult = resultDict.GetValueOrDefault("NoRAG");
            var smartReplyResult = resultDict.GetValueOrDefault("SmartReply");
            var lightRagLongResult = resultDict.GetValueOrDefault("LightRag_Long");
            var lightRagMediumResult = resultDict.GetValueOrDefault("LightRag_Medium");
            var lightRagShortResult = resultDict.GetValueOrDefault("LightRag_Short");

            return new ChatEvalTestResult
            {
                TestId = testId,
                Scenario = scenario,
                Question = string.Join(" ", question.QuestionContexts?.Select(q => $"{q.Role}: {q.Content}") ?? question.SfChatEntriesQuestionContexts.Select(c =>
                {
                    if (c.Bot is not null)
                        return $"Bot: {c.Bot}";

                    if (c.User is not null)
                        return $"User: {c.User}";

                    return "System: No content";
                })),
                ExpectedAnswer = question.ModelAnswer,
                NoRagAnswer = FormatAnswer(noRagResult),
                SmartReplyAnswer = FormatAnswer(smartReplyResult),
                LightRagLongAnswer = FormatAnswer(lightRagLongResult),
                LightRagMediumAnswer = FormatAnswer(lightRagMediumResult),
                LightRagShortAnswer = FormatAnswer(lightRagShortResult),
                SourceFiles = string.Join(
                    "|",
                    question.SourceFilenames ??
                    [])
            };

            string FormatAnswer(ChatEvalResult? r) =>
                r?.Answer.Replace("\r\n", " ").Replace("\n", " ") ?? string.Empty;
        });

        await csv.WriteRecordsAsync(records);
        Console.WriteLine($"\nTest results exported to: {csvPath}");
    }

    private string GetRootPath()
    {
        var exePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var appPathMatcher = new Regex(@"(?<!file)[A-Za-z]:\\+[\S\s]*?(?=\\+bin)");
        return appPathMatcher.Match(exePath).Value;
    }
}