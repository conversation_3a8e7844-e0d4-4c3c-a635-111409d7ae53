namespace Sleekflow.CommerceHub.Models.Payments;

public static class PaymentStatuses
{
    public const string PaymentPending = "PaymentPending";
    public const string GeneratedPaymentLink = "GeneratedPaymentLink";
    public const string Paid = "Paid";
    public const string PaymentFailed = "Failed";
    public const string FullyRefunded = "FullyRefunded";
    public const string PartiallyRefunded = "PartiallyRefunded";
    public const string RefundPending = "RefundPending";
    public const string RefundFailed = "RefundFailed";
}