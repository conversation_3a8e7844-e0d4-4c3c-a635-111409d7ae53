namespace Sleekflow.UserEventHub.Tests.Notifications;

public static class Mocks
{
    public const string FakeUserId = "testUserId_1";
    public const string FakeCompanyId = "testCompanyId_1";
    public const string FakeStaffId = "9999";
    public const string FakeDeviceId = "testDeviceId_1";

    public const string NotificationToken =
        "f8nsxyDlnEpOkjLYrnphis:APA91bFjmLU1Tk-2NC35X1T_PLlVcshmGE4cbjqOCgmEuhBXCJOi1bLayknFc-ccD41IdnNOWIOhx8pJPJP0N2DEnBtqaUZyNExXkZNzljh__T7ORkTBwNKOsAzXkRlRhKUObGAJV69T";

    public const string FakePlatform = "fcm";

    public const string FakeHubName = "testHubName_1";
}