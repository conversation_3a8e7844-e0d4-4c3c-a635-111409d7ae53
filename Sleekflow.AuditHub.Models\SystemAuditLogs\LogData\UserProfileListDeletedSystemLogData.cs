using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class UserProfileListDeletedSystemLogData
{
    [JsonProperty("list_id")]
    public string ListId { get; set; }

    [JsonProperty("list_name")]
    public string ListName { get; set; }

    [JsonConstructor]
    public UserProfileListDeletedSystemLogData(string listId, string listName)
    {
        ListId = listId;
        ListName = listName;
    }
}