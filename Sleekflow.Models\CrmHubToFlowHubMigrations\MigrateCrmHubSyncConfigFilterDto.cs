﻿using System.Text.RegularExpressions;
using Newtonsoft.Json;

namespace Sleekflow.Models.CrmHubToFlowHubMigrations;

public class MigrateCrmHubSyncConfigFilterDto
{
    public static readonly Regex ValueRegex = new Regex("^[a-zA-Z0-9_\\-]+$");

    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("provider_field_type")]
    public string ProviderFieldType { get; set; }

    [JsonProperty("value")]
    public string Value { get; set; }

    [JsonProperty("operator")]
    public string Operator { get; set; }

    [JsonConstructor]
    public MigrateCrmHubSyncConfigFilterDto(
        string fieldName,
        string providerFieldType,
        string value,
        string @operator)
    {
        FieldName = fieldName;
        ProviderFieldType = providerFieldType;
        Value = value;
        Operator = @operator;
    }
}