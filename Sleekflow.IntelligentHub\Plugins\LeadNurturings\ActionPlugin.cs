using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IActionPlugin
{
    [KernelFunction("execute_action")]
    [Description(
        "Executes actions using data from data pane and stores execution results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with action execution results.")]
    Task<string> ExecuteActionWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where action plan is stored in the data pane.")]
        string actionPlanKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey);
}

public class ActionPlugin : BaseLeadNurturingPlugin, IActionPlugin
{
    public ActionPlugin(
        ILogger<ActionPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel)
    {
    }

    private async Task<string> ExecuteActionAsync(
        Kernel kernel,
        string actionPlan,
        string conversationContext)
    {
        var actionAgent = _agentDefinitions.GetActionAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1, true));

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, actionPlan);

        return await ExecuteAgentWithTelemetryAsync(actionAgent, agentThread, "ActionPlugin");
    }

    [KernelFunction("execute_action")]
    [Description(
        "Executes actions using data from data pane and stores execution results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with action execution results.")]
    public async Task<string> ExecuteActionWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where action plan is stored in the data pane.")]
        string actionPlanKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey)
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.ActionAgentResponse>(
            sessionKey,
            "Action",
            dataRetrievalFunc: async () => await RetrieveActionData(actionPlanKey, conversationContextKey),
            agentExecutionFunc: async (data) => await ExecuteActionAsync(
                _kernel.Clone(),
                data["actionPlan"],
                data["conversationContext"]),
            storageKey: _keyManager.GetActionKey(sessionKey),
            storageDataType: AgentOutputKeys.ActionComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreActionComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task<Dictionary<string, string>> RetrieveActionData(
        string actionPlanKey,
        string conversationContextKey)
    {
        var results = new Dictionary<string, string>();

        // Try assignment plan first, then demo plan
        var actionPlan = await _dataPane.GetData(actionPlanKey, AgentOutputKeys.PlanningAssignmentComplete);
        if (string.IsNullOrEmpty(actionPlan))
        {
            actionPlan = await _dataPane.GetData(actionPlanKey, AgentOutputKeys.PlanningDemoComplete);
        }

        if (string.IsNullOrEmpty(actionPlan))
        {
            throw new InvalidOperationException($"No action plan found for key: {actionPlanKey}");
        }

        var conversationContext = await _dataPane.GetData(conversationContextKey, AgentOutputKeys.Conversation);
        if (string.IsNullOrEmpty(conversationContext))
        {
            throw new InvalidOperationException($"No conversation context found for key: {conversationContextKey}");
        }

        results["actionPlan"] = actionPlan;
        results["conversationContext"] = conversationContext;

        return results;
    }

    private async Task StoreActionComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.ActionAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "ActionAgent", AgentOutputKeys.ActionResult),
                    AgentOutputKeys.ActionResult,
                    parsedResponse.Result);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "ActionAgent", AgentOutputKeys.ActionPhaseReasoning),
                    AgentOutputKeys.ActionPhaseReasoning,
                    parsedResponse.PhaseReasoning);

                // Store phase information
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "ActionAgent", AgentOutputKeys.ActionPhase),
                    AgentOutputKeys.ActionPhase,
                    parsedResponse.Phase);

                _logger.LogInformation(
                    "Action execution completed for session {SessionKey}: {Result}",
                    sessionKey,
                    parsedResponse.Result);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to store action components for session {SessionKey}", sessionKey);
        }
    }
}