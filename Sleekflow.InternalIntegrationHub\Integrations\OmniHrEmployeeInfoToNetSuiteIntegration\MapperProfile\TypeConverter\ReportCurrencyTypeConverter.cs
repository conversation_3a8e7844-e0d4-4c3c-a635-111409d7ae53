using AutoMapper;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

namespace Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile.TypeConverter;

public class ReportCurrencyTypeConverter : ITypeConverter<string, DefaultExpenseReportCurrency>
{
    public DefaultExpenseReportCurrency Convert(
        string source,
        DefaultExpenseReportCurrency destination,
        ResolutionContext context)
    {
        return new DefaultExpenseReportCurrency(source);
    }
}