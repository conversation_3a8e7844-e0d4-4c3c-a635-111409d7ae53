using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Newtonsoft.Json;
using OpenAI.Chat;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

/// <summary>
/// Base class for all Lead Nurturing plugins that provides common infrastructure and patterns.
/// This class encapsulates:
/// - Common dependencies and their initialization
/// - Standardized error handling and logging
/// - Agent duration tracking and telemetry
/// - Flow control response generation
/// - Data retrieval and storage patterns
/// - Configuration retrieval helpers
/// </summary>
public abstract class BaseLeadNurturingPlugin : IScopedService
{
    protected readonly ILogger _logger;
    protected readonly ILeadNurturingAgentDefinitions _agentDefinitions;
    protected readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    protected readonly IAgentDurationTracker _agentDurationTracker;
    protected readonly ILeadNurturingDataPane _dataPane;
    protected readonly IDataPaneKeyManager _keyManager;
    protected readonly Kernel _kernel;

    /// <summary>
    /// Optional dependencies that not all plugins require
    /// </summary>
    protected readonly ILeadNurturingCollaborationChatCacheService? _chatCacheService;

    protected BaseLeadNurturingPlugin(
        ILogger logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel,
        ILeadNurturingCollaborationChatCacheService? chatCacheService = null)
    {
        _logger = logger;
        _agentDefinitions = agentDefinitions;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _agentDurationTracker = agentDurationTracker;
        _dataPane = dataPane;
        _keyManager = keyManager;
        _kernel = kernel;
        _chatCacheService = chatCacheService;
    }

    /// <summary>
    /// Executes a plugin operation with standardized error handling, data retrieval,
    /// agent execution, result storage, and output transformation.
    /// </summary>
    /// <typeparam name="TResponse">The type of the agent response for parsing</typeparam>
    /// <param name="sessionKey">Session key for data isolation</param>
    /// <param name="operationName">Name of the operation for logging purposes</param>
    /// <param name="dataRetrievalFunc">Function to retrieve required data from data pane</param>
    /// <param name="agentExecutionFunc">Function to execute the agent logic</param>
    /// <param name="storageKey">Key where the complete result should be stored</param>
    /// <param name="storageDataType">Data type constant for storage</param>
    /// <param name="outputInterceptorFunc">Function to intercept agent output and store specific properties</param>
    /// <param name="outputTransformerFunc">Function to transform agent response for manager consumption</param>
    /// <returns>Transformed output for manager agent</returns>
    protected async Task<string> ExecutePluginOperationAsync<TResponse>(
        string sessionKey,
        string operationName,
        Func<Task<Dictionary<string, string>>> dataRetrievalFunc,
        Func<Dictionary<string, string>, Task<string>> agentExecutionFunc,
        string storageKey,
        string storageDataType,
        Func<string, TResponse?, Task>? outputInterceptorFunc = null,
        Func<TResponse?, string>? outputTransformerFunc = null)
        where TResponse : class
    {
        var myKernel = _kernel.Clone();

        try
        {
            // 1. Retrieve data from data pane
            var retrievedData = await dataRetrievalFunc();

            // 2. Validate retrieved data
            ValidateRetrievedData(retrievedData);

            // 3. Execute agent logic
            var agentResult = await agentExecutionFunc(retrievedData);

            // 4. Validate agent result
            if (string.IsNullOrEmpty(agentResult))
            {
                throw new InvalidOperationException($"{operationName} Agent returned empty response");
            }

            // 5. Store complete result
            await _dataPane.StoreData(storageKey, storageDataType, agentResult);

            // 6. Parse agent response
            TResponse? parsedResponse = null;
            try
            {
                parsedResponse = JsonConvert.DeserializeObject<TResponse>(agentResult);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(
                    ex,
                    "Failed to parse {OperationName} result for structured processing",
                    operationName);
            }

            // 7. Execute output interceptor for property storage
            if (outputInterceptorFunc != null)
            {
                await outputInterceptorFunc(agentResult, parsedResponse);
            }

            // 8. Transform output for manager agent
            string managerOutput;
            if (outputTransformerFunc != null)
            {
                managerOutput = outputTransformerFunc(parsedResponse);
            }
            else
            {
                // Default: return the original agent result
                managerOutput = agentResult;
            }

            _logger.LogInformation(
                "{OperationName} completed for session {SessionKey}",
                operationName,
                sessionKey);

            return managerOutput;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in {OperationName} Plugin for session {SessionKey}", operationName, sessionKey);
            throw;
        }
    }

    /// <summary>
    /// Executes an agent with standardized telemetry tracking and error handling.
    /// </summary>
    protected async Task<string> ExecuteAgentWithTelemetryAsync(
        ChatCompletionAgent agent,
        ChatHistoryAgentThread agentThread,
        string pluginName)
    {
        await foreach (var agentResponseItem in agent.InvokeAsync(
                           agentThread,
                           new AgentInvokeOptions
                           {
                               OnIntermediateMessage = chatMessageContent =>
                               {
                                   // Handle tool execution logging
                                   if (chatMessageContent.Role == AuthorRole.Tool)
                                   {
                                       _logger.LogInformation(
                                           "Tool execution result: {ToolResult}",
                                           chatMessageContent.Content ?? string.Empty);
                                   }

                                   // Track usage and performance
                                   if (chatMessageContent is OpenAIChatMessageContent
                                       {
                                           InnerContent: ChatCompletion chatCompletion
                                       })
                                   {
                                       var usageInputTokenCount = chatCompletion.Usage.InputTokenCount;
                                       var usageOutputTokenCount = chatCompletion.Usage.OutputTokenCount;

                                       _agentDurationTracker.TrackResponse(
                                           usageInputTokenCount,
                                           usageOutputTokenCount,
                                           pluginName,
                                           chatMessageContent.Content ?? string.Empty,
                                           chatCompletion.Usage.InputTokenDetails.CachedTokenCount);
                                   }

                                   return Task.CompletedTask;
                               }
                           }))
        {
            if (agentResponseItem.Message.Content != null)
            {
                return agentResponseItem.Message.Content;
            }
        }

        return string.Empty;
    }

    /// <summary>
    /// Retrieves configuration values from the data pane with fallback defaults.
    /// </summary>
    protected async Task<string> GetConfigurationAsync(
        string sessionKey,
        string configType,
        string defaultValue = "")
    {
        try
        {
            var configKey = _keyManager.GetAgentOutputKey(sessionKey, "Configuration", configType);
            var configValue = await _dataPane.GetData(configKey, configType);
            return string.IsNullOrEmpty(configValue) ? defaultValue : configValue;
        }
        catch
        {
            return defaultValue;
        }
    }

    /// <summary>
    /// Helper method to retrieve multiple data items with validation.
    /// </summary>
    protected async Task<Dictionary<string, string>> RetrieveMultipleDataAsync(
        params (string key, string dataType, string parameterName)[] dataRequests)
    {
        var results = new Dictionary<string, string>();

        foreach (var (key, dataType, parameterName) in dataRequests)
        {
            var data = await _dataPane.GetData(key, dataType);
            if (string.IsNullOrEmpty(data))
            {
                throw new InvalidOperationException($"No {parameterName} found for key: {key}");
            }

            results[parameterName] = data;
        }

        return results;
    }

    /// <summary>
    /// Validates that all required data has been retrieved successfully.
    /// </summary>
    private static void ValidateRetrievedData(Dictionary<string, string> retrievedData)
    {
        foreach (var (key, value) in retrievedData)
        {
            if (string.IsNullOrEmpty(value))
            {
                throw new InvalidOperationException($"Retrieved data for {key} is null or empty");
            }
        }
    }


    /// <summary>
    /// Creates a standardized agent thread with conversation context.
    /// </summary>
    protected static ChatHistoryAgentThread CreateAgentThread(string conversationContext)
    {
        var agentThread = new ChatHistoryAgentThread();
        agentThread.ChatHistory.AddUserMessage($"CUSTOMER CONVERSATION CONTEXT:\n{conversationContext}");
        return agentThread;
    }

    /// <summary>
    /// Adds additional context to the agent thread.
    /// </summary>
    protected static void AddContextToThread(ChatHistoryAgentThread agentThread, string context)
    {
        if (!string.IsNullOrEmpty(context))
        {
            agentThread.ChatHistory.AddAssistantMessage(context);
        }
    }
}