using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class CreateActionNeeds : ITrigger<
    CreateActionNeeds.CreateActionNeedsInput,
    CreateActionNeeds.CreateActionNeedsOutput>
{
    private readonly INeedConfigService _needConfigService;

    public CreateActionNeeds(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class CreateActionNeedsInput
    {
        [Required]
        [JsonProperty("needs")]
        public List<ActionNeedConfig> Needs { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonConstructor]
        public CreateActionNeedsInput(
            List<ActionNeedConfig> needs,
            string? version)
        {
            Needs = needs;
            Version = version;
        }
    }

    public class CreateActionNeedsOutput
    {
        [JsonProperty("needs")]
        public List<ActionNeedConfig> Needs { get; set; }

        [JsonConstructor]
        public CreateActionNeedsOutput(List<ActionNeedConfig> needs)
        {
            Needs = needs;
        }
    }

    public async Task<CreateActionNeedsOutput> F(CreateActionNeedsInput input)
    {
        return new CreateActionNeedsOutput(
            await _needConfigService.CreateActionNeedsAsync(
                input.Version,
                input.Needs));
    }
}