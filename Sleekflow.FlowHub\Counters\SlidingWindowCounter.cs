﻿using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using StackExchange.Redis;

namespace Sleekflow.FlowHub.Counters;

public interface ISlidingWindowCounter
{
    Task<int> CountAsync(
        string requestKey,
        int windowSeconds);
}

public sealed class SlidingWindowCounter : ISlidingWindowCounter, ISingletonService
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ICacheConfig _cacheConfig;

    private static LuaScript SlidingWindowCountScript => LuaScript.Prepare(
        """
        local current_time = redis.call('TIME')
        local trim_time = tonumber(current_time[1]) - @window
        redis.call('ZREMRANGEBYSCORE', @key, 0, trim_time)
        redis.call('ZADD', @key, current_time[1], current_time[1] .. current_time[2])
        redis.call('EXPIRE', @key, @window)
        local count = redis.call('ZCARD',@key)
        return count
        """);

    public SlidingWindowCounter(
        IConnectionMultiplexer connectionMultiplexer,
        ICacheConfig cacheConfig)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _cacheConfig = cacheConfig;
    }

    public async Task<int> CountAsync(
        string requestKey,
        int windowSeconds)
    {
        var database = _connectionMultiplexer.GetDatabase();
        requestKey = $"{_cacheConfig.CachePrefix}-{requestKey}";

        var result = (int)await database.ScriptEvaluateAsync(
            SlidingWindowCountScript,
            new
            {
                key = new RedisKey(requestKey),
                window = windowSeconds
            });

        return result;
    }
}