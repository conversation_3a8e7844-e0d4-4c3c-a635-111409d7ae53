using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages;

/// <summary>
/// Line text message object following Line's API format.
/// Reference: https://developers.line.biz/en/reference/messaging-api/#text-message
/// </summary>
public class LineMessageObject
{
    /// <summary>
    /// Message type. For text messages, this should be "text".
    /// </summary>
    [JsonProperty("type")]
    public string Type { get; set; }

    /// <summary>
    /// Message text. Max: 5000 characters.
    /// </summary>
    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonConstructor]
    public LineMessageObject(string type, string text)
    {
        Type = type;
        Text = text;
    }
}