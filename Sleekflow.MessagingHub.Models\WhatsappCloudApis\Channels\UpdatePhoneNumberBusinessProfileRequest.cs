﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Validations;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;

public class UpdatePhoneNumberBusinessProfileRequest
{
    [JsonProperty("messaging_product")]
    public string MessagingProduct { get; set; }

    [JsonProperty("about")]
    [MaxLength(139)]
    [MinLength(1)]
    public string? About { get; set; }

    [JsonProperty("address")]
    [MaxLength(256)]
    public string? Address { get; set; }

    [JsonProperty("description")]
    [MaxLength(512)]
    public string? Description { get; set; }

    /// <summary>
    /// https://developers.facebook.com/docs/whatsapp/cloud-api/reference/business-profiles/#parameters
    /// </summary>
    [JsonProperty("vertical")]
    [AllowedStringValues(
        isIgnoreCase: true,
        "",
        "UNDEFINED",
        "OTHER",
        "AUTO",
        "BEAUTY",
        "APPAREL",
        "EDU",
        "ENTER<PERSON>IN",
        "EVENT_PLAN",
        "FINANCE",
        "GROCERY",
        "GOVT",
        "HOTEL",
        "HEALTH",
        "NONPROFIT",
        "PROF_SERVICES",
        "RETAIL",
        "TRAVEL",
        "RESTAURANT",
        "NOT_A_BIZ")]
    public string? Vertical { get; set; }

    [JsonProperty("email")]
    [MaxLength(128)]
    [RegularExpression(
        @"^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$",
        ErrorMessage = "Invalid email format")]
    public string? Email { get; set; }

    [JsonProperty("websites")]
    [MaxLength(2)]
    [StringArrayElementValidation(7, 256, @"^https?:\/\/")]
    public List<string>? Websites { get; set; }

    [JsonProperty("profile_picture_handle")]
    public string? ProfilePictureHandle { get; set; }

    [JsonConstructor]
    public UpdatePhoneNumberBusinessProfileRequest(
        string messagingProduct,
        string? about,
        string? address,
        string? description,
        string? vertical,
        string? email,
        List<string>? websites,
        string? profilePictureHandle)
    {
        MessagingProduct = messagingProduct;
        About = about;
        Address = address;
        Description = description;
        Vertical = vertical;
        Email = email;
        Websites = websites;
        ProfilePictureHandle = profilePictureHandle;
    }
}