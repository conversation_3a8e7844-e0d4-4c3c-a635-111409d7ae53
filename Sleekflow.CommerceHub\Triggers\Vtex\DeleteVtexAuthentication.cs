﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Vtex.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;

namespace Sleekflow.CommerceHub.Triggers.Vtex;

[TriggerGroup(ControllerNames.Vtex)]
public class DeleteVtexAuthentication
    : ITrigger<
        DeleteVtexAuthentication.DeleteVtexAuthenticationInput,
        DeleteVtexAuthentication.DeleteVtexAuthenticationOutput>
{
    private readonly IVtexAuthenticationService _vtexAuthenticationService;
    private readonly ILogger<DeleteVtexAuthentication> _logger;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public DeleteVtexAuthentication(
        IVtexAuthenticationService vtexAuthenticationService,
        ILogger<DeleteVtexAuthentication> logger,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _vtexAuthenticationService = vtexAuthenticationService;
        _logger = logger;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class DeleteVtexAuthenticationInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("vtex_authentication_id")]
        public string VtexAuthenticationId { get; set; }

        [JsonConstructor]
        public DeleteVtexAuthenticationInput(string sleekflowCompanyId, string vtexAuthenticationId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            VtexAuthenticationId = vtexAuthenticationId;
        }
    }

    public class DeleteVtexAuthenticationOutput
    {
        [JsonProperty("is_success")]
        public bool IsSuccess { get; set; }

        [JsonConstructor]
        public DeleteVtexAuthenticationOutput(bool isSuccess)
        {
            IsSuccess = isSuccess;
        }
    }

    public async Task<DeleteVtexAuthenticationOutput> F(
        DeleteVtexAuthenticationInput input)
    {
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        _logger.LogWarning(
            "Delete Vtex authentication request received. {CompanyId}, {VtexAuthenticationId}, {StaffId}",
            input.SleekflowCompanyId,
            input.VtexAuthenticationId,
            distributedInvocationContext?.SleekflowStaffId);

        var isSuccess = false;
        try
        {
            await _vtexAuthenticationService.DeleteAsync(
                input.VtexAuthenticationId,
                input.SleekflowCompanyId);

            isSuccess = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to delete Vtex authentication for Sleekflow company ID: {SleekflowCompanyId}, Vtex authentication ID: {VtexAuthenticationId}",
                input.SleekflowCompanyId,
                input.VtexAuthenticationId);
        }

        return new DeleteVtexAuthenticationOutput(isSuccess);
    }
}