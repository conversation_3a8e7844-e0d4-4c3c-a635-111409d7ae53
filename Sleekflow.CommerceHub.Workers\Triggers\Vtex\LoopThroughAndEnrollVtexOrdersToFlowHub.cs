﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.CommerceHub.Workers.Utils;

namespace Sleekflow.CommerceHub.Workers.Triggers.Vtex;

public class LoopThroughAndEnrollVtexOrdersToFlowHub
{
    private readonly ILogger<LoopThroughAndEnrollVtexOrdersToFlowHub> _logger;

    public LoopThroughAndEnrollVtexOrdersToFlowHub(
        ILogger<LoopThroughAndEnrollVtexOrdersToFlowHub> logger)
    {
        _logger = logger;
    }

    [Function("LoopThroughAndEnrollVtexOrdersToFlowHub")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<LoopThroughAndEnrollVtexOrdersToFlowHubInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (LoopThroughAndEnrollVtexOrdersToFlowHubInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (input, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            "LoopThroughAndEnrollVtexOrdersToFlowHub_Orchestrator",
            input);

        logger.LogInformation(
            "Started LoopThroughAndEnrollVtexOrdersToFlowHub with ID = [{InstanceId}]",
            instanceId);

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get LoopThroughAndEnrollVtexOrdersToFlowHub_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}