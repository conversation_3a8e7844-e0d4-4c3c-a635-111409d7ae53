using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Models.MessageObjects.FlowObjects;
using GraphApi.Client.Payloads.WhatsappFlows;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.WhatsappFlows;

public interface IWhatsappFlowService
{
    Task<GetFlowsResponse> GetFlowsAsync(
        string whatsappBusinessAccountId,
        CursorBasedPaginationParam? paginationParam);

    Task<GetFlowResponse> GetFlowAsync(
        string whatsappBusinessAccountId,
        string flowId);

    Task<GetFlowAssetsResponse> GetFlowAssets(
        string whatsappBusinessAccountId,
        string flowId);

    Task<GetFlowWebPreviewUrlResponse> GetFlowWebPreviewUrlAsync(
        string whatsappBusinessAccountId,
        string flowId,
        bool isInvalidate = false);

    Task<CreateFlowResponse> CreateFlowAsync(
        string whatsappBusinessAccountId,
        WhatsappCloudApiCreateFlowObject flow,
        string staffId);

    /// <summary>
    /// Only allow the flows to be updated if the flow is in draft status
    /// </summary>
    /// <param name="whatsappBusinessAccountId">The WhatsApp Business Account ID.</param>
    /// <param name="flowId">The ID of the flow to be updated.</param>
    /// <param name="metadata">The metadata to update the flow with.</param>
    /// <param name="staffId">The ID of the staff identity id performing the update.</param>
    /// <returns> Flow Details.</returns>
    Task<GetFlowResponse> UpdateFlowMetadataAsync(
        string whatsappBusinessAccountId,
        string flowId,
        WhatsappCloudApiUpdateFlowMetadataObject metadata,
        string staffId);

    Task<DeleteFlowResponse> DeleteFlowAsync(
        string whatsappBusinessAccountId,
        string flowId,
        string staffId);

    Task<GetFlowResponse> PublishFlowAsync(
        string whatsappBusinessAccountId,
        string flowId,
        string staffId);

    Task<GetFlowResponse> DeprecateFlowAsync(
        string whatsappBusinessAccountId,
        string flowId,
        string staffId);
}

public class WhatsappFlowService(
    IWabaService wabaService,
    ICloudApiClients cloudApiClients,
    IHttpClientFactory httpClientFactory,
    ILogger<WhatsappFlowService> logger)
    : IWhatsappFlowService, ISingletonService
{
    private readonly IWhatsappCloudApiFlowClient _whatsappCloudApiFlowClient = cloudApiClients.WhatsappCloudApiFlowClient;

    public async Task<GetFlowsResponse> GetFlowsAsync(
        string whatsappBusinessAccountId,
        CursorBasedPaginationParam? paginationParam)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        var flows = await GetWhatsappCloudApiFlowClient(waba).GetFlowsAsync(
            whatsappBusinessAccountId,
            paginationParam: paginationParam ?? new CursorBasedPaginationParam()
            {
                Limit = 500
            });

        return flows;
    }

    public async Task<GetFlowResponse> GetFlowAsync(
        string whatsappBusinessAccountId,
        string flowId)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        var flow = await GetWhatsappCloudApiFlowClient(waba).GetFlowAsync(flowId);

        return flow;
    }

    public async Task<GetFlowAssetsResponse> GetFlowAssets(
        string whatsappBusinessAccountId,
        string flowId)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        return await GetWhatsappCloudApiFlowClient(waba).GetFlowAssetsAsync(flowId);
    }

    public async Task<GetFlowWebPreviewUrlResponse> GetFlowWebPreviewUrlAsync(
        string whatsappBusinessAccountId,
        string flowId,
        bool isInvalidate = false)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);
        return await GetWhatsappCloudApiFlowClient(waba).GetFlowWebPreviewUrlAsync(flowId, isInvalidate);
    }

    public async Task<CreateFlowResponse> CreateFlowAsync(
        string whatsappBusinessAccountId,
        WhatsappCloudApiCreateFlowObject flow,
        string staffId)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        logger.LogInformation(
            "Creating flow {Flow} for waba {FacebookWabaId} by staff {StaffId}",
            JsonConvert.SerializeObject(flow),
            whatsappBusinessAccountId,
            staffId);

        try
        {
            return await GetWhatsappCloudApiFlowClient(waba).CreateFlowAsync(whatsappBusinessAccountId, flow);
        }
        catch (GraphApiClientException g)
        {
            throw new SfGraphApiErrorException(
                "Unable to create flow.",
                JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
        }
    }

    public async Task<GetFlowResponse> UpdateFlowMetadataAsync(
        string whatsappBusinessAccountId,
        string flowId,
        WhatsappCloudApiUpdateFlowMetadataObject metadata,
        string staffId)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        logger.LogInformation(
            "Updating flow metadata {Metadata} for waba {FacebookWabaId} flow {FlowId} by staff {StaffId}",
            JsonConvert.SerializeObject(metadata),
            whatsappBusinessAccountId,
            flowId,
            staffId);

        try
        {
            var successGraphApiResponse = await GetWhatsappCloudApiFlowClient(waba).UpdateFlowMetadataAsync(flowId, metadata);

            if (!successGraphApiResponse.Success)
            {
                throw new SfGraphApiErrorException(
                    "Unable to update flow metadata.",
                    JsonConvertExtensions.ToDictionary(successGraphApiResponse));
            }
        }
        catch (GraphApiClientException g)
        {
            throw new SfGraphApiErrorException(
                "Unable to update flow metadata.",
                JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
        }

        return await GetFlowAsync(whatsappBusinessAccountId, flowId);
    }

    public async Task<DeleteFlowResponse> DeleteFlowAsync(
        string whatsappBusinessAccountId,
        string flowId,
        string staffId)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        logger.LogInformation(
            "Deleting flow {FlowId} for waba {FacebookWabaId} by staff {StaffId}",
            flowId,
            whatsappBusinessAccountId,
            staffId);

        try
        {
            var deleteFlowResponse = await GetWhatsappCloudApiFlowClient(waba).DeleteFlowAsync(flowId);

            if (!deleteFlowResponse.Success)
            {
                throw new SfGraphApiErrorException(
                    "Unable to delete flow.",
                    JsonConvertExtensions.ToDictionary(deleteFlowResponse));
            }

            return deleteFlowResponse;
        }
        catch (GraphApiClientException g)
        {
            throw new SfGraphApiErrorException(
                "Unable to delete flow.",
                JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
        }
    }

    public async Task<GetFlowResponse> PublishFlowAsync(
        string whatsappBusinessAccountId,
        string flowId,
        string staffId)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        logger.LogInformation(
            "Publishing flow {FlowId} for waba {FacebookWabaId} by staff {StaffId}",
            flowId,
            whatsappBusinessAccountId,
            staffId);

        try
        {
            var successGraphApiResponse = await GetWhatsappCloudApiFlowClient(waba).PublishFlowAsync(flowId);

            if (!successGraphApiResponse.Success)
            {
                throw new SfGraphApiErrorException(
                    "Unable to publish flow.",
                    JsonConvertExtensions.ToDictionary(successGraphApiResponse));
            }
        }
        catch (GraphApiClientException g)
        {
            throw new SfGraphApiErrorException(
                "Unable to publish flow.",
                JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
        }

        return await GetFlowAsync(whatsappBusinessAccountId, flowId);
    }

    public async Task<GetFlowResponse> DeprecateFlowAsync(
        string whatsappBusinessAccountId,
        string flowId,
        string staffId)
    {
        var waba = await wabaService.GetWabaWithFacebookWabaIdAsync(whatsappBusinessAccountId);

        logger.LogInformation(
            "Deprecating flow {FlowId} for waba {FacebookWabaId} by staff {StaffId}",
            flowId,
            whatsappBusinessAccountId,
            staffId);

        try
        {
            var successGraphApiResponse = await GetWhatsappCloudApiFlowClient(waba).PublishFlowAsync(flowId);

            if (!successGraphApiResponse.Success)
            {
                throw new SfGraphApiErrorException(
                    "Unable to deprecate flow.",
                    JsonConvertExtensions.ToDictionary(successGraphApiResponse));
            }
        }
        catch (GraphApiClientException g)
        {
            throw new SfGraphApiErrorException(
                "Unable to deprecate flow.",
                JsonConvertExtensions.ToDictionary(g.ErrorApiResponse));
        }

        return await GetFlowAsync(whatsappBusinessAccountId, flowId);
    }

    private IWhatsappCloudApiFlowClient GetWhatsappCloudApiFlowClient(Waba waba)
    {
        var (_, decryptedBusinessIntegrationSystemUserAccessTokenDto) = wabaService
            .GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var accessToken = decryptedBusinessIntegrationSystemUserAccessTokenDto?.DecryptedToken;

        return string.IsNullOrEmpty(accessToken)
            ? _whatsappCloudApiFlowClient
            : new WhatsappCloudApiFlowClient(accessToken, httpClientFactory.CreateClient("default-handler"));
    }
}