﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Salesforce;

[TriggerGroup(TriggerGroups.InflowActions)]
public class TerminateLoopThroughSalesforceObjects
    : ITrigger<
        TerminateLoopThroughSalesforceObjects.TerminateLoopThroughSalesforceObjectsInput,
        TerminateLoopThroughSalesforceObjects.TerminateLoopThroughSalesforceObjectsOutput>
{
    private readonly IProviderSelector _providerSelector;

    public TerminateLoopThroughSalesforceObjects(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class TerminateLoopThroughSalesforceObjectsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughSalesforceObjectsInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class TerminateLoopThroughSalesforceObjectsOutput
    {
        [JsonProperty("is_terminated")]
        public bool IsTerminated { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughSalesforceObjectsOutput(bool isTerminated)
        {
            IsTerminated = isTerminated;
        }
    }

    public async Task<TerminateLoopThroughSalesforceObjectsOutput> F(
        TerminateLoopThroughSalesforceObjectsInput input)
    {
        var providerService = _providerSelector.GetProviderService("salesforce-integrator");

        var isTerminated = await providerService.TerminateInProgressLoopThroughExecutionAsync(
            input.FlowHubWorkflowId,
            input.FlowHubWorkflowVersionedId,
            input.SleekflowCompanyId);

        return new TerminateLoopThroughSalesforceObjectsOutput(isTerminated);
    }
}