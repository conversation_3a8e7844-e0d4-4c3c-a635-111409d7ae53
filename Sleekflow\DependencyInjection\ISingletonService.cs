﻿namespace Sleekflow.DependencyInjection;

/// <summary>
/// Marker interface for services with a singleton lifetime in the dependency injection container.
/// </summary>
/// <remarks>
/// <para>
/// Services implementing this interface are automatically registered with a singleton lifetime.
/// A single instance is created and shared throughout the application's lifetime, making it suitable
/// for stateless services or those that maintain application-wide shared state.
/// </para>
/// <para>
/// Use this interface for services that:
/// <list type="bullet">
///   <li>Do not store request-specific state</li>
///   <li>Are thread-safe and can be safely shared across requests</li>
///   <li>Would be expensive to create per request (e.g. repositories, configuration providers)</li>
///   <li>Need to maintain shared state for the application lifetime (e.g. caches)</li>
/// </list>
/// </para>
/// <para>
/// Common examples include repositories, configuration services, and utility services.
/// The registration is automatically handled through assembly scanning in the Modules class.
/// </para>
/// </remarks>
public interface ISingletonService
{
}