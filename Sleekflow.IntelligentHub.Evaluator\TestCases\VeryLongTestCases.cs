using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class VeryLongTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetVeryLongTestCases()
    {
        var hkbnTestConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/HKBN/",
            SleekflowCompanyId: "b6d7e442-38ae-4b9a-b100-2951729768bc");

        var hkbnDocuments =
            new List<string>
            {
                "HKBN x Bowtie_4-in-1 Healthcare Service_WhatsApp guide for customer_20241008.md",
                "RS_sales briefing_Bowtie_20241204.docx.md",
                "HKBN x Evercare Whatsapp Guide_20241204.md",
                "RS_sales briefing_Evercare_20241204.docx.md",
                "INDICAID「月月測」產品小冊子.md",
                "RS_sales briefing_INDICAID_20241204.docx.md"
            };

        yield return new ChatEvalQuestion(
            hkbnTestConfig,
            "客戶查詢香港寬頻居家護理服務計劃價格和細節",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下香港寬頻的居家護理服務計劃價錢"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！我們現有兩個居家護理服務計劃：基本版每月$399（2小時服務）及進階版每月$499（4小時服務）。請問你比較有興趣哪一種計劃？"),
                new ChatMessageContent(AuthorRole.User, "可以詳細介紹一下基本版計劃嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "基本版計劃月費$399，合約期12個月，每次提供2小時服務。服務範圍包括：\n- 上門居家照顧服務\n- 個人身體護理（如扶行、防跌）\n- 協助沖涼、更換尿片\n- 不多於15分鐘簡單煮食\n這個計劃特別適合有基本照顧需要的長者或人士，而且現在優惠低至66折（原價$610）。需要了解更多詳情嗎？"),
                new ChatMessageContent(AuthorRole.User, "這個服務是由什麼人提供的？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "這個服務是由經過專業訓練的護理人員提供，他們都已接受最少50小時的護理訓練。而且，Evercare平台擁有超過30,000名護理人員，每位都經過嚴格的背景調查和審核，確保服務質素。您可以放心，我們會根據您的個人需要和喜好，為您配對最合適的護理人員。"),
                new ChatMessageContent(AuthorRole.User, "除左 Evercare，聽說最近有新推出針對經常去中國內地的數據計劃？"),
                new ChatMessageContent(AuthorRole.Assistant, "是的，我們現正推出針對經常往來中港兩地客戶的特別優惠計劃。請問您經常到內地嗎？"),
                new ChatMessageContent(AuthorRole.User, "對，我每個月都會去內地工作一兩個星期。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明白了，作為經常往返內地的商務客戶，建議您考慮我們最受歡迎的全方位方案，現在有限時優惠：\n- 16GB本地數據（4.5G高速網絡）\n- 每月2GB中國內地及澳門數據\n- 贈送中國手機號碼\n月費只需$118，比市面上類似計劃更划算呢！"),
                new ChatMessageContent(AuthorRole.User, "這個價錢對我來說有點貴，有沒有便宜一點的計劃？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "理解您想精打細算。不過讓我分析一下：如果您每月在內地逗留1-2星期，2GB內地數據其實最划算。選擇$78或$98的計劃雖然每月便宜$20-40，但：\n1. 內地數據只有1GB，很容易用完\n2. 沒有中國手機號碼，內地客戶聯絡您會比較不方便\n3. 超出用量收費會更貴\n\n現在選擇$118計劃，還可以享有：\n- 6個月免費HKBN SAFE服務\n- 豁免行政費（節省$28/月）\n- 3,000分鐘本地通話\n綜合這些優惠，實際上是最划算的選擇。"),
                new ChatMessageContent(AuthorRole.User, "仲有個「月月測」自選自測計劃，想了解一下基本計劃的詳情。"),
            ],
            "基本計劃是12個月合約，每月$48就可以自選2盒檢測試劑盒。每個月更可享1次免費送貨服務（限香港境內，偏遠地區除外）。請問您對哪種檢測試劑盒較感興趣？",
            SourceFilenames: hkbnDocuments);

        yield return new ChatEvalQuestion(
            hkbnTestConfig,
            "客戶查詢不同的服務計劃",
            [
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "多謝你的耐心等待。我們正在為你安排專員協助進一步處理，請稍等一會。\n\nThank you for your patience. We’re connecting you with a specialist who can assist you further. Please hold on for a moment."),
                new ChatMessageContent(AuthorRole.User, "Hi HKBN what services and products do you offer?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "mobile"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "多謝你的耐心等待。我們正在為你安排專員協助進一步處理，請稍等一會。\n\nThank you for your patience. We’re connecting you with a specialist who can assist you further. Please hold on for a moment."),
                new ChatMessageContent(AuthorRole.User, "Hi HKBN, can you introduce me to more mobile plans"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "mobile"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您聯繫我們！我很高興向您介紹HKBN的流動計劃，特別是為像您這樣經常旅行的人士設計的方案。\n\n我們的流動計劃提供無縫的連接體驗，無論是本地還是國際。以下是一些您可能感興趣的亮點：\n\n1. **東南亞及中國內地的國際漫遊**：我們的許多計劃包括全球SIM服務，提供每月的旅遊數據配額（例如10GB、20GB或30GB），適用於熱門目的地如新加坡、泰國、馬來西亞和中國內地，確保您無論身在何處都能保持連接。\n\n2. **專屬中國內地號碼**：對於前往中國內地的用戶，一些計劃附帶專屬的中國內地手機號碼，讓您更方便地與當地聯繫人保持聯絡。\n\n3. **靈活的數據選項**：您可以選擇包含豐富本地數據配額（每月20GB、30GB或50GB）並結合國際漫遊福利的計劃。\n\n4. **獨家優惠**：享受增值服務，如Global Talk+、語音信箱和來電顯示。一些計劃還包括與旅遊相關的促銷，例如KKday折扣或免費的HKBN SAFE月費，保護您的設備。\n\n如果您想進一步了解這些計劃或有具體問題，我建議您聯繫我們的客戶服務團隊。他們可以提供個性化的協助，幫助您找到最適合您需求的計劃。\n\n如果還有其他需要協助的地方，請隨時告訴我。我們隨時為您服務，確保您無論身在何處都能保持聯繫！"),
                new ChatMessageContent(AuthorRole.User, "English please"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "多謝你的耐心等待。我們正在為你安排專員協助進一步處理，請稍等一會。\n\nThank you for your patience. We’re connecting you with a specialist who can assist you further. Please hold on for a moment."),
                new ChatMessageContent(AuthorRole.User, "Hi HKBN, can you recommend me some healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "Hi HKBN, can you recommend me some plans?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for reaching out to HKBN! I’d be delighted to share more about our mobile plans, especially those designed for frequent travelers like yourself.\n\nOur plans are tailored to keep you seamlessly connected, whether you're in Southeast Asia, Mainland China, or beyond. Here are some highlights:\n\n1. **International Roaming & Travel Data**: Many of our plans include Global SIM services and monthly travel data vouchers (ranging from 10GB to 30GB) that can be used in popular destinations like Thailand, Singapore, Malaysia, and Mainland China. This ensures you stay connected without worrying about high roaming charges.\n\n2. **Dedicated Mainland China Number**: For those traveling to Mainland China, some plans come with a dedicated Chinese mobile number, making it easier to stay in touch with local contacts.\n\n3. **Flexible Data Options**: Choose from plans offering generous local data allowances (20GB, 30GB, or 50GB) combined with international roaming benefits.\n\n4. **Exclusive Promotions**: Enjoy waived administrative fees during the contract period, flexible data sharing options across Mainland China, Hong Kong, and Macau, and bonus services like 6 months of HKBN SAFE to protect your devices.\n\nIf you’d like to explore these plans further or have specific questions, I recommend connecting with our customer service team. They can provide personalized assistance and help you find the perfect plan for your needs. You can reach them via our hotline, email, or online chat on our website.\n\nLet me know if there’s anything else I can assist you with. We’re here to ensure you stay connected wherever your travels take you!"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Tell me more details for a 50GB package for mainland china travel and travels to overseas."),
                new ChatMessageContent(AuthorRole.User, "Tell me more details"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi HKBN, tell me more details for 50GB plan for mainland china travel and travel to overseas"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your interest in our 50GB mobile plan! I’d be happy to share more details about how this plan can keep you seamlessly connected during your travels to Mainland China and overseas.\n\nHere’s what the plan offers:\n1. **Generous Data Allowances**:\n   - 50GB of local 5G data for use in Hong Kong.\n   - 3GB of data specifically for Mainland China and Macau each month, ensuring you stay connected while traveling.\n   - Optional global travel data add-ons (10GB, 20GB, or 30GB per month) for seamless connectivity in popular international destinations.\n\n2. **International Roaming Benefits**:\n   - A bundled Global SIM service and Global Talk+ service, along with a dedicated Mainland China number, making it easier to stay in touch with local contacts while abroad.\n   - Free day passes for international roaming, depending on the global travel data option you choose.\n\n3. **Additional Features**:\n   - 3,000 minutes of local calling and SMS services.\n   - Value-added services like voicemail, call waiting, caller ID, and more.\n   - Complimentary HKBN SAFE service for up to 3 devices for the first 6 months.\n\n4. **Pricing**:\n   - The monthly fee starts at HK$208 for the 10GB global travel data option, HK$238 for 20GB, and HK$268 for 30GB.\n   - A 24-month contract applies, with waived administrative fees for number porting users during the contract period.\n\n5. **Promotions**:\n   - Enjoy exclusive travel-related discounts, such as KKday offers, and up to 3 months of complimentary PandaPro membership.\n\nThis plan is designed to provide flexibility and convenience for frequent travelers like you. If you’d like to explore this plan further or have specific questions, I recommend connecting with our customer service team. They can provide personalized assistance and help you finalize the best option for your needs.\n\nLet me know if there’s anything else I can assist you with. We’re here to ensure you stay connected wherever your travels take you!"),
                new ChatMessageContent(AuthorRole.User, "Any other promotions that can be offered?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your interest in our 50GB mobile plan! I’d be happy to share more details about the benefits and promotions that make this plan a great choice for frequent travelers like you.\n\nHere’s what the plan offers:\n1. **Generous Data Allowances**:\n   - 50GB of local 5G data for use in Hong Kong.\n   - 3GB of data specifically for Mainland China and Macau each month, ensuring you stay connected while traveling.\n   - Optional global travel data add-ons (10GB, 20GB, or 30GB per month) for seamless connectivity in popular international destinations.\n\n2. **Exclusive Features for Travelers**:\n   - A dedicated Mainland China mobile number, making it easier to stay in touch with local contacts.\n   - Bundled Global SIM and Global Talk+ services, which include popular global travel data day passes.\n\n3. **Value-Added Services**:\n   - Complimentary HKBN SAFE service for up to 3 devices for the first 6 months.\n   - Waived administrative fees for number porting users during the contract period.\n\n4. **Flexible Pricing**:\n   - The monthly fee starts at HK$208 for the 10GB global travel data option, HK$238 for 20GB, and HK$268 for 30GB.\n\nWhile there are no specific limited-time promotions at the moment, the plan’s bundled benefits, such as the dedicated Mainland China number and flexible global travel data options, are designed to provide exceptional value for frequent travelers. Additionally, the low upgrade fee for enhanced global coverage (as little as HK$1 per day) makes it even more convenient for international trips.\n\nIf you’d like to explore this plan further or have any specific questions, feel free to let me know. I’d be delighted to assist you in finding the perfect solution for your travel needs!"),
                new ChatMessageContent(AuthorRole.User, "Do you have any healthcare promotions?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您對我們醫療計劃的關注！我很樂意與您分享我們目前的促銷詳情，這些計劃旨在提供全面且實惠的醫療解決方案：\n\n1. **HKBN x Bowtie 4合1醫療服務計劃**：\n   - 每月只需$99起（12個月合約），即可享受不限次數的普通科醫生視像診症、一次全身健康檢查、兩次牙齒清潔服務及一次流感疫苗接種，總價值高達$3,500。此計劃非常適合以方便且實惠的方式維持健康。\n\n2. **HKBN x INDICAID「每月測試」快速測試組合計劃**：\n   - 每月$48起，您可每月獲得兩套快速測試套裝。升級計劃（每月$68）還包括額外的癌症篩查自測套裝，讓您在家中進行測試時更安心。\n\n3. **HKBN x Evercare家居護理計劃**：\n   - 為家庭量身定制，這些計劃提供家居護理服務，每月$399起（2小時服務）或每月$499（4小時服務）。升級計劃還包括營養諮詢，為您的家人提供全面的關懷。\n\n雖然這些計劃提供了極具價值的福利，但目前尚未包含針對經常旅行者的特定功能，例如國際醫療保障。不過，如果您需要與旅行相關的服務，我們的國際漫遊流動通訊計劃可能是您的理想補充。\n\n如果您希望進一步了解這些醫療計劃或有任何具體問題，請隨時告訴我。我很樂意協助您找到最適合您需求的解決方案！"),
                new ChatMessageContent(AuthorRole.User, "English please"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "感謝您對我們醫療計劃的關注！我很樂意與您分享我們目前提供的選項詳情，這些計劃旨在提供全面且實惠的醫療解決方案：\n\n1. **HKBN x Bowtie 4合1醫療服務計劃**：\n   - 每月只需港幣99元起（12個月合約），此計劃包括不限次數的普通科醫生視像診症、一個全身健康檢查、兩次牙齒清潔服務，以及一次流感疫苗接種，總價值高達港幣3,500元。這是一個方便又實惠的健康維護選擇。\n\n2. **HKBN x Evercare 家居護理計劃**：\n   - 專為家庭設計，這些計劃提供家居護理服務，每月港幣399元起（2小時服務）或港幣499元起（4小時服務）。升級版計劃還包括營養諮詢，為您的家人提供全面的關懷。\n\n雖然這些計劃為一般醫療需求提供了寶貴的福利，但目前尚未包括針對經常旅行者的特定功能，例如國際醫療保障或與旅行相關的醫療服務。不過，如果您正在尋找旅行時保持聯繫的解決方案，我們的國際漫遊流動計劃可能是您的理想補充。\n\n如果您希望進一步了解這些醫療計劃或有任何具體問題，請隨時告訴我。我很樂意協助您找到最適合您需求的解決方案！"),
                new ChatMessageContent(AuthorRole.User, "how can I contact your customer service"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your patience! I’d be delighted to share more details about our offerings that cater to your needs as a frequent traveler and someone interested in healthcare services.\n\n### 50GB Mobile Plan for Mainland China and Overseas Travel:\nOur 50GB mobile plan is designed to keep you seamlessly connected, whether you're in Hong Kong, Mainland China, or Southeast Asia. Here are the highlights:\n1. **Generous Data Allowances**:\n   - 50GB of local 5G data for use in Hong Kong.\n   - 3GB of data specifically for Mainland China and Macau each month.\n   - Optional global travel data add-ons (10GB, 20GB, or 30GB per month) for popular international destinations, starting at HK$208/month.\n\n2. **Exclusive Features for Travelers**:\n   - A dedicated Mainland China mobile number for easier communication with local contacts.\n   - Bundled Global SIM and Global Talk+ services for added convenience.\n\n3. **Promotions**:\n   - Enjoy up to 3 months of complimentary PandaPro membership (valued at HK$297) and exclusive discounts on travel products like KKday tours when you register by August 31, 2024.\n\n### Healthcare Promotions:\nWe also have some fantastic healthcare plans that might interest you:\n1. **HKBN x Bowtie 4-in-1 Medical Service Plan**:\n   - For just HK$99/month (12-month contract), enjoy unlimited video consultations with general practitioners, a full body check-up, two dental cleanings, and a flu vaccination—valued at up to HK$3,500.\n\n2. **HKBN x INDICAID “Monthly Test” Rapid Test Combo Plan**:\n   - Starting at HK$48/month, receive two rapid test kits monthly. The advanced plan (HK$68/month) includes a cancer screening self-test package valued at over HK$1,300.\n\n3. **HKBN x Evercare Home Care Programme**:\n   - Professional at-home care services starting at HK$399/month, with options for additional services like nutrition consultations.\n\nIf you’d like to explore these plans further or have any specific questions, I’d be happy to assist you. Alternatively, you can connect with our customer service team for personalized assistance. Let me know how I can help!"),
                new ChatMessageContent(AuthorRole.User, "How can I talk to your customer service"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "多謝你的耐心等待。我們正在為你安排專員協助進一步處理，請稍等一會。\n\nThank you for your patience. We’re connecting you with a specialist who can assist you further. Please hold on for a moment."),
                new ChatMessageContent(AuthorRole.User, "我想查詢關於服務計劃的優惠和服務詳情"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "流動服務 Mobile"),
                new ChatMessageContent(AuthorRole.User, "我想查詢關於服務計劃的優惠和服務詳情"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "醫療計劃 Healthcare"),
                new ChatMessageContent(AuthorRole.User, "我想查詢關於服務計劃的優惠和服務詳情"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "流動服務 Mobile"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for reaching out! 😊 To contact HKBN’s customer service, we recommend visiting our official website or reaching out through our social media channels for the most accurate and timely assistance. \n\nCould you let us know a bit more about what you need help with? For example:\n- Are you looking for more details about our mobile or healthcare plans?\n- Do you need assistance with billing or technical support?\n\nThis will help us guide you to the right team or resource. Let us know how we can assist you further!"),
                new ChatMessageContent(AuthorRole.User, "Hi HKBN, I would like to inquire about your promotions"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "醫療計劃 Healthcare"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！😊 感謝您對HKBN醫療計劃的興趣！以下是我們與合作夥伴提供的一些熱門醫療服務計劃：\n\n1. **HKBN x Bowtie 4合1醫療服務計劃**（每月$99，12個月合約）：\n   - 無限次西醫視像診症\n   - 2次牙齒清潔及全面牙科檢查\n   - 1次全身健康檢查（包括糖尿病篩查）\n   - 1次流感疫苗接種\n\n2. **HKBN x Evercare家居護理計劃**（每月$399起，12個月合約）：\n   - 基本計劃：每月2小時家居護理服務\n   - 高級計劃：每月4小時護理服務，另加營養及專業個案諮詢\n   - 傷口護理計劃：專業傷口清潔及敷料更換\n\n3. **HKBN x INDICAID快速測試計劃**（每月$48起，12個月合約）：\n   - 每月2個快速測試包（如COVID-19、癌症篩查等）\n   - 測試包直接送到府上，方便又私密\n\n這些計劃均提供優惠價格及靈活服務，適合個人或家庭需要！請問您對哪一類服務特別感興趣？我們很樂意為您提供更詳細的資訊！🌟"),
                new ChatMessageContent(AuthorRole.User, "I would like to learn more about Bowtie"),
                new ChatMessageContent(AuthorRole.User, "Hi HKBN"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "流動服務 Mobile"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your interest in HKBN's mobile services! 😊 We offer a variety of plans tailored to different needs, including:\n\n- **5G Local Data Plans**: Starting from *HK$188/month* for 20GB, with options for global travel data and features like Global Talk+ and data sharing across regions.\n- **Exclusive Promotions**: Enjoy up to *3 months of free pandapro membership* and discounts on travel bookings with KKday.\n- **Bundled Options**: Combine mobile services with healthcare plans, such as the Bowtie 4-in-1 Medical Service Plan, for added convenience.\n\nTo help you find the perfect plan, could you share a bit more about your needs? For example:\n- Are you looking for unlimited data or a budget-friendly option?\n- Do you need international roaming or family-sharing features?\n\nFeel free to let me know, and I’ll be happy to assist further! 🌟"),
                new ChatMessageContent(AuthorRole.User, "我想查詢關於服務計劃的優惠和服務詳情"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "歡迎來到HKBN, 你想了解更多我們的流動服務還是醫療計劃?\n\nWelcome to HKBN, would you like to inquire more about our mobile services or healthcare plans?"),
                new ChatMessageContent(AuthorRole.User, "流動服務 Mobile"),
            ],
            "我們提供多種流動服務，包括5G本地數據計劃、國際漫遊、全球SIM服務和獨家優惠。請問您對哪一類服務特別感興趣？",
            SourceFilenames: hkbnDocuments);

        var beameTestConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/beame/",
            SleekflowCompanyId: "cddf6a22-0751-47ff-94c8-4d78c17b37b1");

        var beameDocuments =
            new List<string>
            {
                "https%3a%2f%2fmybeame.com%2f.md",
                "https%3a%2f%2fmybeame.com%2faligner.md",
                "https%3a%2f%2fmybeame.com%2faligner-flow.md",
                "https%3a%2f%2fmybeame.com%2finvisible.md",
                "https%3a%2f%2fmybeame.com%2fretainer.md",
                "https%3a%2f%2fmybeame.com%2fbeame-student-discount.md",
                "https%3a%2f%2fmybeame.com%2fpromotion.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2finvisible-braces-price.md"
            };

        yield return new ChatEvalQuestion(
            beameTestConfig,
            "客戶詢問隱形牙箍的詳細資訊和價格",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下隱形牙箍的資訊。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！很高興為你介紹我們的隱形牙箍服務。我們提供專業的隱形矯正方案，包括免費的初步諮詢和3D掃描。你有什麼具體想了解的嗎？"),
                new ChatMessageContent(AuthorRole.User, "主要想知道價格，還有整個療程需要多久？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "隱形牙箍的價格會根據你的牙齒情況而定，一般療程為6-12個月。我們現在提供網上預約優惠，最高可減$3,820。建議你先預約免費諮詢，讓我們的專業團隊為你進行評估。"),
                new ChatMessageContent(AuthorRole.User, "我是學生，有沒有特別優惠？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，我們有專門的學生優惠！只要出示有效的學生證，就可以享受特別折扣，而且還包含免費的超聲波洗牙服務。"),
                new ChatMessageContent(AuthorRole.User, "那整個療程的流程是怎樣的？需要經常去覆診嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "療程分為幾個步驟：首先是免費諮詢和3D掃描，然後我們的牙醫會根據掃描結果設計個人化的矯正方案。確認方案後，你就可以開始配戴牙箍。期間我們會定期跟進，確保矯正進度理想。"),
                new ChatMessageContent(AuthorRole.User, "如果矯正完成後，牙齒會不會又變回去？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "為了維持矯正效果，完成療程後需要配戴固定器（retainer）。我們提供be@me Care+服務，如果配戴固定器後牙齒仍有移位，我們會提供免費重箍服務，確保你的笑容持久美麗。"),
                new ChatMessageContent(AuthorRole.User, "那如果我想找不同牙醫諮詢意見，可以嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然可以！我們歡迎你諮詢不同的牙醫。初步諮詢是免費的，你可以充分了解不同牙醫的建議，選擇最適合你的方案。"),
                new ChatMessageContent(AuthorRole.User, "我聽說有些人箍牙會很痛，是真的嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "隱形牙箍相比傳統牙箍，不適感會較輕。剛開始配戴時可能會有輕微壓迫感，但大多數人幾天後就能適應。我們的牙醫會詳細指導你如何正確配戴和護理，減少不適感。"),
                new ChatMessageContent(AuthorRole.User, "如果我想約時間諮詢，怎麼預約？可以網上預約嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "可以的！你可以透過我們的網站進行預約，選擇合適的時間和分店。網上預約還可以享受特別優惠。你想我幫你了解一下預約詳情嗎？"),
                new ChatMessageContent(AuthorRole.User, "好的，不過我還想知道，如果開始療程後覺得效果不理想，可以中途停止嗎？"),
            ],
            "我們會在療程開始前讓你清楚了解整個治療計劃和預期效果。如果在療程中有任何疑慮，我們的專業團隊會即時跟進和調整。至於中途停止，建議先與牙醫討論，因為這可能影響最終效果。",
            SourceFilenames: beameDocuments);

        yield return new ChatEvalQuestion(
            beameTestConfig,
            "客戶用廣東話詢問隱形牙箍的資訊",
            [
                new ChatMessageContent(AuthorRole.User, "喂，想問下你哋隱形牙箍要幾錢㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好呀！我哋嘅隱形牙箍價錢會視乎你嘅牙齒情況而定。而家網上預約仲有優惠，最高可以慳到$3,820添！不如預約個免費諮詢，等我哋嘅專業團隊幫你睇下？"),
                new ChatMessageContent(AuthorRole.User, "咁大約要幾耐先搞掂㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "一般嚟講，療程大約需時6-12個月，視乎你牙齒嘅情況。我哋會用3D掃描幫你睇下牙齒狀況，然後畀個詳細嘅療程建議你。"),
                new ChatMessageContent(AuthorRole.User, "我係學生黎㗎，有冇平啲嘅？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係有啦！我哋有專門嘅學生優惠，只要帶埋有效嘅學生證就可以享受特別折扣，仲包埋免費超聲波洗牙添！"),
                new ChatMessageContent(AuthorRole.User, "咁成個療程係點㗎？要成日覆診咩？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "療程分幾個步驟：首先係免費諮詢同3D掃描，然後牙醫會幫你設計個人化嘅矯正方案。確認咗方案之後就可以開始戴牙箍啦。期間我哋會定期跟進，確保矯正進度理想。"),
                new ChatMessageContent(AuthorRole.User, "矯正完之後，啲牙會唔會又甩番位？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "唔使擔心！完成療程之後會配戴固定器（retainer）。我哋仲有be@me Care+服務，如果戴固定器之後牙齒仍然走位，我哋會提供免費重箍服務，確保你嘅笑容持久靚靚！"),
                new ChatMessageContent(AuthorRole.User, "咁如果我想搵唔同牙醫傾下意見得唔得？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係得啦！我哋歡迎你諮詢唔同嘅牙醫。初步諮詢係免費㗎，你可以充分了解唔同牙醫嘅建議，揀個最啱你嘅方案。"),
                new ChatMessageContent(AuthorRole.User, "我聽講有啲人箍牙好痛㗎喎，係咪真㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "隱形牙箍相比傳統牙箍，唔舒服嘅感覺會輕好多。剛開始戴嗰陣可能會有少少壓迫感，但大部分人幾日之後就會適應。我哋嘅牙醫會詳細教你點樣正確戴同護理，減少唔舒服嘅感覺。"),
                new ChatMessageContent(AuthorRole.User, "如果我想預約，點樣搞？可以網上預約咩？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "冇問題㗎！你可以喺我哋網站預約，揀個啱你嘅時間同分店。網上預約仲可以享受特別優惠添。你想我幫你了解下預約詳情？"),
                new ChatMessageContent(AuthorRole.User, "好啊，不過我仲想問，如果開始咗療程之後覺得效果唔理想，可唔可以中途停？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我哋會喺療程開始前同你詳細講解成個治療計劃同預期效果。如果你喺療程中有任何疑慮，我哋嘅專業團隊會即時跟進同調整。至於中途停止，建議你同牙醫傾下，因為咁樣可能會影響最終效果。"),
                new ChatMessageContent(AuthorRole.User, "明白啦。最後想問，除咗隱形牙箍，你哋仲有咩牙科服務㗎？"),
            ],
            "係㗎，我哋提供好多牙科服務，包括洗牙、美白、植牙等等。喺香港，我哋主要提供箍牙諮詢同體驗服務。如果你需要其他牙科服務，我哋嘅深圳診所提供更全面嘅服務，包括牙貼面、激光美白等。你對邊樣服務特別有興趣呀？",
            SourceFilenames: beameDocuments);

        yield return new ChatEvalQuestion(
            beameTestConfig,
            "客戶用廣東話詢問箍完牙後嘅固定器",
            [
                new ChatMessageContent(AuthorRole.User, "喂，我啱啱箍完牙，聽講要戴固定器㗎喎，想問下係點㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好呀！冇錯，箍完牙一定要戴固定器㗎，咁樣先可以保持到牙齒唔會再移位。我哋有be@me Care+服務，如果戴住固定器都走位嘅話，我哋會提供免費重箍服務㗎！"),
                new ChatMessageContent(AuthorRole.User, "咁要戴幾耐㗎？夜晚瞓覺都要戴咩？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "固定器要長期戴㗎，尤其係夜晚瞓覺一定要戴，因為呢個時候牙齒最容易移位。唔使擔心，我哋會教你點樣正確戴同清潔，好快就會習慣㗎啦！"),
                new ChatMessageContent(AuthorRole.User, "咁如果唔記得戴，會唔會有事㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "如果一兩日唔記得戴，牙齒可能會開始慢慢移位。所以最好養成習慣，每日都記得戴，特別係瞓覺嗰陣。我哋會幫你設定提醒，等你唔會唔記得戴。"),
                new ChatMessageContent(AuthorRole.User, "固定器會唔會好貴㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "固定器嘅價錢會視乎你嘅需求同選擇。不過我哋而家有優惠，仲有學生折扣添！你可以預約免費諮詢，我哋會詳細解釋晒所有收費。"),
                new ChatMessageContent(AuthorRole.User, "咁我想預約，點樣搞？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好簡單㗎！你可以喺我哋網站預約，揀個啱你嘅時間同分店。網上預約會有額外優惠添。你想我幫你了解下預約詳情？"),
                new ChatMessageContent(AuthorRole.User, "好啊，不過我仲想問，如果固定器整爛咗點算？"),
            ],
            "唔使擔心！如果固定器有任何問題，即刻搵我哋客服就得。我哋會盡快幫你處理，確保你嘅牙齒維持喺最佳狀態。",
            SourceFilenames: beameDocuments);

        yield return new ChatEvalQuestion(
            beameTestConfig,
            "客戶擔心農曆新年期間箍牙嘅問題",
            [
                new ChatMessageContent(AuthorRole.User, "喂，我想箍牙，但係農曆新年就到啦，唔知可唔可以箍？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好呀！農曆新年箍牙都冇問題㗎！我哋嘅隱形牙箍可以喺食飯前除咗佢，食完再戴返，所以唔會影響你食團年飯同拜年。而且而家預約仲有新年優惠添！"),
                new ChatMessageContent(AuthorRole.User, "但係我驚戴住個箍影相唔靚喎..."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "唔使擔心！我哋嘅隱形牙箍係透明㗎，戴住都好難睇到。影相嗰陣你都可以暫時除低，影完再戴返都得。而且我哋會教你點樣正確咁除同戴返個牙箍。"),
                new ChatMessageContent(AuthorRole.User, "咁如果新年期間有咩問題點算？你哋開唔開門㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我哋新年期間都有特別嘅客戶支援服務，如果你有任何問題都可以透過WhatsApp聯絡我哋。至於門市開放時間，我哋會預先喺網站公布，等你可以提早安排。"),
                new ChatMessageContent(AuthorRole.User, "咁新年期間有冇咩特別要注意㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "新年期間最主要係要注意食糖果同零食嘅時間，最好係除咗牙箍先至食，食完漱完口就即刻戴返。同埋記住帶埋牙箍盒出街，等你可以隨時除低個箍。"),
                new ChatMessageContent(AuthorRole.User, "咁而家預約有咩優惠㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "而家網上預約可以即減高達$3,820，仲有新春限定優惠。如果你係學生嘅話，仲可以享有額外折扣同埋免費超聲波洗牙服務添！"),
                new ChatMessageContent(AuthorRole.User, "咁我想預約，大約幾時可以開始箍㗎？"),
            ],
            "你可以而家就預約免費諮詢，我哋會幫你做3D掃描，然後設計個人化嘅箍牙方案。如果你OK嘅話，最快可以喺新年前就開始箍㗎啦！",
            SourceFilenames: beameDocuments);

        yield return new ChatEvalQuestion(
            new ChatEvalConfig(
                SourceDir: "../../../KnowledgeSources/Sleekflow/",
                SleekflowCompanyId: "5406fdfa-1e8c-4a76-91f6-89301c0d95a1"),
            "客戶查詢如何使用Flow Builder設計捉精靈遊戲",
            [
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "對話已經結束。如果您想繼續交流，請回覆關鍵字 \"Hi Sleekflow AI\"。\n\nThe conversation session has ended. If you'd like to continue chatting, please reply with the keyword \"Hi Sleekflow AI\"."),
                new ChatMessageContent(AuthorRole.User, "Hi sleekflow ai"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi there! 😊 Regarding *workflow re-enrollment settings* in SleekFlow, you can customize how contacts re-enroll based on your needs. For example, you can allow re-enrollment for contacts who exited the flow, those who didn't complete it, or even those still in progress. Alternatively, you can disable re-enrollment entirely.\n\nA quick tip: If you enable re-enrollment for contacts still in progress, be cautious as it might create multiple active enrollments, which could be confusing.\n\nIf you're facing any challenges or need help navigating these settings, feel free to let me know—I'm here to assist! 💡\n\n(Balanced Response Bot)"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！🎮 SleekFlow 的 Flow Builder 可以幫助你設計一個模擬捉精靈的互動流程！你可以利用以下功能來實現：\n\n1. **條件邏輯**：根據用戶的選擇或行為，設置不同的流程分支，例如「成功捕捉」或「逃走」。\n2. **用戶輸入收集**：透過對話提示收集用戶的選擇，例如選擇精靈球或技能。\n3. **動態回應**：根據用戶的行動，發送個性化的回應或結果。\n\n雖然目前沒有專門的「捉精靈」模板，但你可以利用現有的模板（例如「表單提交整合」或「關鍵字回應」）作為基礎，並進行創意設計。如果需要更詳細的指導或協助，隨時告訴我！😊\n\n(Balanced Response Bot)"),
                new ChatMessageContent(AuthorRole.User, "可唔可以詳細 d 點整到 step by step please"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "你好！以下係一個詳細嘅步驟教學，幫你用 SleekFlow 嘅 Flow Builder 模擬捉精靈嘅互動流程：\n\n1. **建立新工作流程**：\n   - 登入 SleekFlow，進入「Flow Builder」。\n   - 點擊「新增工作流程」，命名為「捉精靈模擬」。\n\n2. **設置觸發條件**：\n   - 選擇觸發條件，例如「用戶輸入關鍵字 '捉精靈'」。\n   - 確保觸發條件能啟動整個流程。\n\n3. **設置用戶選擇**：\n   - 加入「用戶輸入收集」模組，提示用戶選擇精靈球或技能。\n   - 設定選項，例如「普通球」、「高級球」或「大師球」。\n\n4. **條件邏輯分支**：\n   - 根據用戶選擇，設置不同分支，例如：\n     - 成功捕捉：發送「恭喜你捉到精靈！」。\n     - 捕捉失敗：發送「可惜，精靈逃走咗！」。\n\n5. **動態回應**：\n   - 使用「動態變數」加入用戶名稱或精靈名稱，令回應更個性化。\n   - 例如：「_用戶名_，你成功捕捉咗 _精靈名_！」\n\n6. **測試及啟用**：\n   - 測試整個流程，確保每個分支運作正常。\n   - 啟用工作流程，並通知用戶可以開始互動。\n\n如果你需要更深入嘅協助，或者有其他創意想法，隨時話俾我知！😊\n\n(Balanced Response Bot)"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "對話已經結束。如果您想繼續交流，請回覆關鍵字 \"Hi Sleekflow AI\"。\n\nThe conversation session has ended. If you'd like to continue chatting, please reply with the keyword \"Hi Sleekflow AI\"."),
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "Hi HKBN AI"),
                new ChatMessageContent(AuthorRole.User, "/clear"),
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(AuthorRole.Assistant, "Hi there! We will get back to you soon."),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(AuthorRole.Assistant, "Hi there! We will get back to you soon."),
                new ChatMessageContent(AuthorRole.User, "有咩好介紹？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！SleekFlow AI很高興為您服務！SleekFlow 是一個 AI 驅動的全渠道對話平台，可以幫助您的企業自動化增長並簡化客戶互動。我們將所有銷售和支援對話集中在一個平台中，管理客戶數據並自動化消息傳遞工作流程。\n\n為了給您更精確的建議，請問您的業務屬於哪個行業？您目前面臨的最大挑戰是什麼？"),
                new ChatMessageContent(AuthorRole.User, "我哋做美容㗎，我哋要請好多人嚟覆客"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您服務。了解到您在美容行業，並且需要聘請大量人手回覆客戶。SleekFlow 是一個 AI 驅動的全渠道平台，可以幫助您自動化客戶互動，從而減少對大量人手的需求。\n\n透過我們的全渠道 CRM，您可以將來自 WhatsApp、Facebook、Instagram 等渠道的客戶訊息整合在一個平台中。我們的 Chatbot 能夠處理常見查詢、預約管理、並提供個人化的售後跟進。例如，Weirdo Beauty 和 Sun and Moon Massage 都透過 SleekFlow 提升了客戶互動效率。\n\n想了解更多關於 SleekFlow 如何幫助您的美容業務嗎？"),
                new ChatMessageContent(AuthorRole.User, "想呀想呀，你講多啲呀"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然想啦！SleekFlow 可以幫到你好多㗎！\n\n透過我哋嘅全渠道 CRM，你可以將 WhatsApp、Facebook、Instagram 等渠道嘅客戶訊息整合喺一個平台管理，唔使再喺唔同地方切換。\n\n我哋嘅 Chatbot 可以 24/7 全天候回覆常見問題、處理預約，仲可以自動做售後跟進。例如，Weirdo Beauty 同 Sun and Moon Massage 都用 SleekFlow 提升咗效率。\n\n想唔想了解下 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？"),
                new ChatMessageContent(AuthorRole.User, "有冇啲咩實際例子啊？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "緊係有例子啦！😎 Weirdo Beauty 就用 SleekFlow 設立咗 24/7 全天候嘅虛擬禮賓服務，提升咗客戶體驗！💅 另外，我哋喺美容同健康服務方面都有唔少成功案例，可以幫你哋統一管理客戶資料、最大化預約時段、提升售後服務，仲可以透過適時嘅更新同埋個人化嘅優惠嚟提高客戶忠誠度！✨ 仲有 Retens Medical 都用 SleekFlow 提高咗 WhatsApp 嘅客戶透明度！想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務更上一層樓？🚀"),
                new ChatMessageContent(AuthorRole.User, "想"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係想知多啲啦！🤩 SleekFlow 可以幫你哋嘅美容業務更上一層樓！✨\n\n> 透過我哋嘅全方位收件箱，你可以喺一個地方管理所有客戶嘅訊息，唔使再喺唔同平台之間切換！📱\n> 預約管理功能可以自動提醒客戶預約時間，減少 no-show！🗓️\n> 我哋嘅 AI 工具可以幫你哋快速回覆客戶，節省時間！🤖\n\n想唔想了解更多關於 SleekFlow 點樣幫你哋嘅美容業務？💖"),
                new ChatMessageContent(AuthorRole.User, "有無咩自動化工具？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "梗係有自動化工具啦！😎 SleekFlow嘅 Flow Builder 可以幫你哋自動化好多嘢，好似預約提示、收集客戶資料咁，超方便！✨ 透過我哋嘅全方位收件箱，所有訊息都喺同一個地方管理，唔使周圍搵！仲有 AI Smart Reply，幫你哋快速覆客！🚀 想唔想知多啲 SleekFlow 點樣幫你哋嘅美容業務慳人力？😉"),
                new ChatMessageContent(AuthorRole.User, "點用㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "明晒！✨ Flow Builder 可以幫你哋自動化好多嘢，例如自動發送預約提示、收集客戶資料、甚至可以根據客戶嘅購買紀錄自動推薦產品！😎 雖然我暫時冇美容行業嘅具體例子，但我可以話你知，好多美容院都用佢嚟提升效率同客戶體驗！👍 想唔想我搵啲更詳細嘅美容行業例子俾你參考下？😉"),
                new ChatMessageContent(AuthorRole.User, "搵下 我想知"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay! ✨ 雖然我哋暫時冇美容行業嘅超詳細案例，但係我可以話你知，SleekFlow 喺美容同健康行業都好掂㗎！😎 我哋可以幫你哋將唔同渠道嘅客戶資料整合埋一齊，自動處理預約，仲可以搞埋客戶忠誠度計劃！🤩 另外，我哋喺其他行業嘅客戶，用 WhatsApp 自動化之後，lead 嘅轉換率提升咗 50% 㗎！🚀 想唔想了解多啲我哋可以點樣幫你哋嘅美容業務？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅Flow，真係好有創意呀！✨雖然我哋冇直接嘅捉精靈教學，但係我可以話你知點樣用Flow Builder整一個類似嘅體驗！\n\n首先，你可以用Flow Builder嘅基本功能，例如設定觸發條件（好似係用戶點擊某個按鈕），然後加啲條件判斷（好似係隨機出現唔同嘅精靈）。之後，你可以用「Action」節點嚟設定獎勵，例如送優惠券或者積分！\n\n雖然冇得直接捉精靈，但係你可以用類似嘅方法嚟增加互動性同趣味性！想唔想我哋再深入啲傾下點樣設定？😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以 step by step 教我"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "唔好意思呀！🙏 捉精靈嘅Flow嘅設定比較複雜，我未必可以一步一步咁教你。🥺 我會將你嘅問題轉交俾我哋嘅主管，佢哋可以提供更詳細嘅指導！💪 請稍等一下！"),
                new ChatMessageContent(AuthorRole.User, "/clear"),
                new ChatMessageContent(AuthorRole.User, "Hi Sleekflow Ai"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi there! 👋 I'm SleekFlow AI. So glad you reached out! What are you hoping to achieve with SleekFlow today? 😊"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我點設定一個 flow可以模擬 Pokemon捉精靈？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 捉精靈嘅flow聽落好有趣呀！✨ 雖然我哋冇一個現成嘅template可以直接模擬捉精靈，但係SleekFlow嘅Flow Builder功能非常靈活，可以俾你發揮創意㗎！你可以試吓用唔同嘅triggers、actions同埋conditions，砌一個類似嘅體驗出嚟！💪 探索吓唔同嘅features同埋templates，睇吓點樣可以將你嘅諗法變成真！如果喺設定嘅過程中有任何問題，隨時問我哋！😉"),
                new ChatMessageContent(AuthorRole.User, "可唔可以教我呀 step by step"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈嘅flow真係好有創意！🤩 雖然我哋冇現成嘅捉精靈step-by-step教學，但係我可以俾啲tips你，等你可以自己砌個類似嘅flow出嚟！😉\n\n首先，你可以用「收到訊息」做trigger，當有人send訊息嚟，個flow就會開始。之後，你可以用「隨機選擇」嘅action，模擬捉到唔同嘅精靈。例如，你可以設定唔同嘅機率，捉到稀有精靈嘅機率低啲，捉到普通精靈嘅機率高啲。最後，你可以用「send訊息」嘅action，send返個結果俾個user，例如「恭喜你捉到比卡超！⚡️」\n\n你可以試吓用唔同嘅triggers、actions同埋conditions，砌一個屬於你嘅捉精靈flow！💪 如果喺設定嘅過程中有任何問題，隨時問我哋！😉"),
                new ChatMessageContent(AuthorRole.User, "有咩Trigger提供㗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "哈囉！👋 當然可以！SleekFlow 有好多唔同嘅 triggers 可以用㗎！🤩\n\n*   *互動觸發 (Interaction Triggers)*: 例如「收到訊息」，當有人send訊息嚟就會觸發！啱晒捉精靈遊戲！\n*   *聯絡人觸發 (Contact Triggers)*: 當聯絡人資料更新嘅時候觸發，例如新加入嘅玩家。\n*   *整合觸發 (Integration Triggers)*: 可以同其他系統連接，例如連接Pokemon Go嘅API！\n*   *時間觸發 (Schedule Triggers)*: 定時觸發，例如每日更新精靈出現嘅地點！\n\n仲有好多其他triggers，例如表單提交、WhatsApp opt-in等等！你可以喺Flow Builder入面慢慢探索！💪 試吓用唔同嘅triggers砌你嘅捉精靈flow啦！😉 如果有任何問題，隨時問我哋！"),
                new ChatMessageContent(AuthorRole.User, "你鐘唔鐘意捉精靈㗎"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "捉精靈好好玩㗎！🤩 我都好鍾意！✨ 雖然我哋SleekFlow唔係直接捉精靈，但係我哋嘅Flow Builder可以幫你整啲類似嘅互動體驗俾你嘅客戶！💪 你可以用唔同嘅triggers同埋actions，砌一啲好有趣嘅遊戲或者活動，增加客戶嘅參與度！😉 想唔想了解多啲點樣用SleekFlow整互動體驗呀？"),
                new ChatMessageContent(AuthorRole.User, "係地有無d客都用flow builder"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然有好多客人都用緊Flow Builder㗎！✨ 例如，Be@me用我哋嘅Flow Builder，喺WhatsApp上面搞咗個自動預約系統，book多咗六倍嘅appointment！📣 STACCATO都用WhatsApp變成咗佢哋嘅sales machine！💪 雖然冇直接捉精靈嘅例子，但係佢哋都係用Flow Builder整咗好多唔同嘅互動體驗！你可以參考下，諗下點樣將Flow Builder變成你嘅「捉精靈」遊戲！😉 想唔想我哋再深入啲傾下點樣用Flow Builder實現你嘅捉精靈idea呀？"),
                new ChatMessageContent(AuthorRole.User, "Beame咩黎㗎"),
            ],
            "Be@me係一間提供牙齒矯正服務嘅公司，專門做隱形牙箍㗎！佢哋用我哋嘅WhatsApp自動化同埋Flow Builder，自動幫客人預約同埋諮詢，令效率提高咗好多！雖然佢哋唔係做捉精靈遊戲，但係佢哋嘅成功例子證明Flow Builder可以幫你做到好多唔同嘅自動化功能，令客戶互動更加吸引！你有冇諗過點樣用Flow Builder喺你嘅美容業務度實現啲咩特別功能呢？",
            SourceFilenames: new List<string>
            {
            });
    }
}