using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

// Action Agents: ConfirmationAgent, ActionAgent
public partial class LeadNurturingAgentDefinitions
{
    [method: JsonConstructor]
    public class ActionAgentResponse(
        string agentName,
        string phase,
        string phaseReasoning = "",
        ConfirmationPhase? confirmationPhase = null,
        ReportPhase? reportPhase = null,
        string result = "")
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; } = agentName;

        [JsonProperty("phase_reasoning")]
        public string PhaseReasoning { get; set; } = phaseReasoning;

        [JsonProperty("phase")]
        public string Phase { get; set; } = phase;

        [JsonProperty("confirmation_phase")]
        public ConfirmationPhase? ConfirmationPhase { get; set; } = confirmationPhase;

        [JsonProperty("report_phase")]
        public ReportPhase? ReportPhase { get; set; } = reportPhase;

        [JsonProperty("result")]
        public string Result { get; set; } = result;
    }

    [method: JsonConstructor]
    public class ConfirmationAgentResponse(
        string agentName,
        string reasoning,
        string confirmationStatus,
        List<string>? confirmationQuestions = null)
    {
        [JsonProperty("agent_name")]
        public string AgentName { get; set; } = agentName;

        [JsonProperty("reasoning")]
        public string Reasoning { get; set; } = reasoning;

        [JsonProperty("confirmation_status")]
        public string ConfirmationStatus { get; set; } = confirmationStatus;

        [JsonProperty("confirmation_questions")]
        public List<string>? ConfirmationQuestions { get; set; } = confirmationQuestions;
    }

    [method: JsonConstructor]
    public class ConfirmationPhase(
        string pendingActionDescription,
        string confirmationQuestion)
    {
        [JsonProperty("pending_action_description")]
        public string PendingActionDescription { get; set; } = pendingActionDescription;

        [JsonProperty("confirmation_question")]
        public string ConfirmationQuestion { get; set; } = confirmationQuestion;
    }

    [method: JsonConstructor]
    public class ReportPhase(List<ExecutedTool> executedTools)
    {
        [JsonProperty("executed_tools")]
        public List<ExecutedTool> ExecutedTools { get; set; } = executedTools;
    }

    [method: JsonConstructor]
    public class ExecutedTool(string toolName, Dictionary<string, object> args, string outcome)
    {
        [JsonProperty("tool_name")]
        public string ToolName { get; set; } = toolName;

        [JsonProperty("args")]
        public Dictionary<string, object> Args { get; set; } = args;

        [JsonProperty("outcome")]
        public string Outcome { get; set; } = outcome;
    }

    public ChatCompletionAgent GetConfirmationAgent(Kernel kernel, PromptExecutionSettings settings)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                new PromptExecutionSettingsUtils.Property("confirmation_status", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "confirmation_questions",
                    "array",
                    true,
                    null,
                    new PromptExecutionSettingsUtils.Property("confirmation_question", "string")),
            ]);

        var instructions =
            $$"""
              {{GetSharedSystemPrompt()}}
              You are the ConfirmationAgent. Your role is to determine if the user has explicitly confirmed or declined the most recent proposed action in the conversation. Follow these steps to analyze the conversation and provide the appropriate response.

              **Step 1: Identify the most recent confirmation question.**
              - A confirmation question is a message from the assistant that explicitly asks the user to confirm or agree to a proposed action. Examples include: "Do you want us to arrange a demo for you?", "Would you like us to proceed with scheduling the demo?", "Shall we go ahead and assign a lead to your company?"
              - Note: Questions asking for or confirming specific information (e.g., "Could you confirm your phone number?", "Is your email address correct?") are *not* confirmation questions for the action, even if the information is necessary for the action.

              **Step 2: Analyze the user's response to the confirmation question.**
              - If there is a confirmation question about the action, examine the user's subsequent messages.
              - Look for explicit confirmation of the action (e.g., 'yes', 'sure', 'okay', 'proceed') or declination (e.g., 'no', 'not now', 'stop').
              - If the user changes the topic, asks a different question, or provides information without explicitly confirming the action, consider it as not confirmed.
              - Only explicit agreement to the proposed action counts as confirmation.

              **Step 3: Determine the confirmation status.**
              - If the user explicitly confirms the proposed action in response to a confirmation question, set 'confirmation_status' to 'confirmed'.
              - If the user explicitly declines, does not directly respond to the confirmation question, or if no confirmation question was asked about the action, set 'confirmation_status' to 'not confirmed'.

              **Step 4: Provide additional information if necessary.**
              - Always include a 'reasoning' field in English explaining the confirmation status.
              - If no confirmation question was asked by the assistant or if the user's response does not explicitly confirm the action, provide a list of 'confirmation_questions' that could be asked to seek explicit confirmation of the action.

              **Step 5: Output the JSON response.**
              - Prepare a JSON object with the following fields:
                - 'agent_name': "ConfirmationAgent"
                - 'reasoning': A string explaining the reasoning (in English).
                - 'confirmation_status': "confirmed" or "not confirmed"
                - 'confirmation_questions': An array of strings with suggested confirmation questions, if applicable.
              - Ensure that the JSON is correctly formatted and includes all required fields based on the situation.

              **Important Notes:**
              - Confirming specific details (e.g., phone number, email) does not constitute confirmation of the proposed action. Explicit confirmation of the action is always required.
              - If the assistant has collected all necessary information but has not yet asked for explicit confirmation of the action, the status should be 'not confirmed', and suggested confirmation questions should be provided.

              **Examples:**
              - **Confirmed:**
                Assistant: Shall we proceed with scheduling the demo using the provided information?
                User: Yes, please.
                ```json
                {
                    "agent_name": "ConfirmationAgent",
                    "reasoning": "User explicitly agreed to proceed with scheduling the demo.",
                    "confirmation_status": "confirmed"
                }
                ```
              - **Not Confirmed (Declined):**
                Assistant: Would you like us to assign a lead to your company?
                User: Not at this time.
                ```json
                {
                    "agent_name": "ConfirmationAgent",
                    "reasoning": "User declined to assign a lead at this time.",
                    "confirmation_status": "not confirmed"
                }
                ```
              - **Not Confirmed (No Confirmation Question Asked):**
                Assistant: Could you confirm your phone number with the country code?
                User: Yes, it's ******-777-8888.
                ```json
                {
                    "agent_name": "ConfirmationAgent",
                    "reasoning": "The assistant asked the user to confirm their phone number, and the user provided the information. However, no explicit confirmation question was asked about proceeding with the proposed action (e.g., scheduling a demo or assigning a lead).",
                    "confirmation_status": "not confirmed",
                    "confirmation_questions": ["Shall we proceed with scheduling the demo using the provided information?", "Would you like us to assign a lead to your company based on the details you've shared?"]
                }
                ```
              - **Not Confirmed (Unclear Intent):**
                Assistant: 你想我哋幫你安排中文專員聯絡你嗎？
                User: 會唔會好難 set up？
                ```json
                {
                    "agent_name": "ConfirmationAgent",
                    "reasoning": "User did not directly respond to the confirmation question about arranging a Chinese specialist; instead, asked about setup difficulty.",
                    "confirmation_status": "not confirmed"
                }
                ```
              """;

        return new ChatCompletionAgent
        {
            Name = ConfirmationAgentName,
            Description =
                "Determines if the user has explicitly confirmed or declined a proposed action by analyzing conversation context and provides confirmation questions when needed.",
            HistoryReducer = new AuthorNamesGeminiChatHistoryReducer(
            [
                "Context",
                DemoSchedulingPlanningAgentName,
                LeadAssignmentPlanningAgentName,
            ]),
            Instructions = instructions,
            Kernel = kernel,
            Arguments = new KernelArguments(settings),
        };
    }

    public ChatCompletionAgent GetActionAgent(Kernel kernel, PromptExecutionSettings settings)
    {
        // Configure structured output format for ActionAgent
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "executed_tools",
                    "array",
                    false,
                    null,
                    new PromptExecutionSettingsUtils.Property(
                        string.Empty,
                        "object",
                        false,
                        [
                            new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                            new PromptExecutionSettingsUtils.Property(
                                "args",
                                "array",
                                false,
                                null,
                                new PromptExecutionSettingsUtils.Property(
                                    string.Empty,
                                    "object",
                                    false,
                                    [
                                        new PromptExecutionSettingsUtils.Property("name", "string"),
                                        new PromptExecutionSettingsUtils.Property("value", "string")
                                    ])),
                            new PromptExecutionSettingsUtils.Property("tool_result", "string"),
                            new PromptExecutionSettingsUtils.Property("outcome", "string")
                        ])),
                new PromptExecutionSettingsUtils.Property("result", "string")
            ]);

        var actionKernel = kernel.Clone();
        actionKernel.Plugins.AddFromObject(_sleekflowToolsPlugin);
        actionKernel.Plugins.AddFromObject(_chiliPiperPlugin);

        settings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(
            options: new FunctionChoiceBehaviorOptions
            {
                AllowParallelCalls = true, AllowConcurrentInvocation = true, AllowStrictSchemaAdherence = false
            });

        var leadNurturingTools = kernel.Data[KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG] as LeadNurturingTools;

        var hasAssignmentTool = leadNurturingTools?.AssignmentTool is not null
                                && leadNurturingTools.AssignmentTool.Assignments.Count > 0;

        var instructions =
            $$"""
              {{GetSharedSystemPrompt()}}
              You are the {{ActionAgentName}}. Your job is to use the tools to achieve the planned actions by other planning agents.

              ### Lead Assignment Action (The PlanningAgent's action_type: "assign_lead"):
              1. Extract from lead_assignment_action: target_team, assignment_reason, lead_score, conversation_summary
              2. Execute these exact functions with the extracted data:
                 - AddHandsOffCustomObject(assignment_reason, lead_score_as_int, conversation_summary)
                   - Parse lead_score to int, default to 50 if parsing fails
                 {{(hasAssignmentTool ? "- AssignToTeam(target_team, assignment_reason)" : string.Empty)}}

              ### Demo Scheduling Action (The PlanningAgent's action_type: "schedule_demo"):
              1. Extract from demo_scheduling_action: fields
              2. Execute these exact functions with the extracted data:
                 - ScheduleDemoWithChiliPiper(fields)

              ### Response Format

              - **Execution Phase**: Just execute the tools and capture the results.
              - **Report Phase**:

                ```json
                {
                  "agent_name": "{{ActionAgentName}}",
                  "executed_tools": [
                    {{(hasAssignmentTool ?
                            """
                            {
                              "tool_name": "AssignToTeam",
                              "args": [
                                {"name": "target_team", "value": "sales"},
                                {"name": "reason", "value": "Hot lead"}
                              ],
                              "tool_result": "Assigned",
                              "outcome": "Team assignment successful"
                            },
                            {
                              "tool_name": "AddHandsOffCustomObject",
                              "args": [
                                {"name": "reason", "value": "Hot lead"},
                                {"name": "score", "value": "85"},
                                {"name": "summary", "value": "Customer inquired about product X."}
                              ],
                              "tool_result": "Created",
                              "outcome": "Record created successfully"
                            }
                            """
                            :
                            """
                            {
                              "tool_name": "AddHandsOffCustomObject",
                              "args": [
                                {"name": "reason", "value": "Hot lead"},
                                {"name": "score", "value": "85"},
                                {"name": "summary", "value": "Customer inquired about product X."}
                              ],
                              "tool_result": "Created",
                              "outcome": "Record created successfully"
                            }
                            """
                        )}}
                  ],
                  "result": "success"
                }
                ```

                Please note:
                - Include all executed tools in the report phase, even if they failed.
                - Include the full result of each tool execution in the `tool_result` field and give a brief description of the impact in the `outcome` field.
                - Other agents will use the `tool_result` and `outcome` fields to understand the results of the actions taken.

              ### Error Handling
              - If a tool fails, note the error in `outcome` and set `result` to 'failure'.
              - Keep `phase_reasoning` in English.

              ### Notes
              - Ensure you first execute the tools before returning the JSON.
              """;

        return new ChatCompletionAgent
        {
            Name = ActionAgentName,
            Description =
                "Executes system operations using plugins for lead assignments and demo scheduling, implementing a two-phase process of execution and structured reporting.",
            HistoryReducer = new AuthorNamesChatHistoryReducer(
            [
                "Context",
                DemoSchedulingPlanningAgentName,
                LeadAssignmentPlanningAgentName,
                ActionAgentName
            ]),
            Instructions = instructions,
            Kernel = actionKernel,
            Arguments = new KernelArguments(settings)
        };
    }
}