﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Workers;

public class LoopThroughAndEnrollVtexOrdersToFlowHubInput : IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("flow_hub_workflow_id")]
    [Required]
    public string FlowHubWorkflowId { get; set; }

    [JsonProperty("flow_hub_workflow_versioned_id")]
    [Required]
    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonProperty("vtex_authentication_id")]
    [Required]
    public string VtexAuthenticationId { get; set; }

    [JsonProperty("filter")]
    [Required]
    public VtexGetOrdersSearchCondition Condition { get; set; }

    [JsonConstructor]
    public LoopThroughAndEnrollVtexOrdersToFlowHubInput(
        string sleekflowCompanyId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string vtexAuthenticationId,
        VtexGetOrdersSearchCondition condition)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        VtexAuthenticationId = vtexAuthenticationId;
        Condition = condition;
    }
}