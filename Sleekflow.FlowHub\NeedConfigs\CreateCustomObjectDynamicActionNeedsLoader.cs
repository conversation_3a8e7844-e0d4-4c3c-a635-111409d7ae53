﻿using MassTransit;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.NeedConfigs;

public sealed class CreateCustomObjectDynamicActionNeedsLoader : IDynamicActionNeedsLoader
{
    private readonly IRequestClient<GetCustomObjectSchemaRequest> _requestClient;

    public CreateCustomObjectDynamicActionNeedsLoader(
        IRequestClient<GetCustomObjectSchemaRequest> requestClient)
    {
        _requestClient = requestClient;
    }

    public string ActionGroup => "custom_object";

    public string ActionSubgroup => "record_create";

    public async Task<List<ActionNeedConfig>> LoadAsync(
        string sleekflowCompanyId,
        Dictionary<string, object?>? parameters)
    {
        var results = new List<ActionNeedConfig>();

        if (parameters is null
            || !parameters.TryGetValue("schema_id", out var schemaId)
            || schemaId is not string schemaIdStr)
        {
            return results;
        }

        var response = await _requestClient.GetResponse<GetCustomObjectSchemaReply>(
            new GetCustomObjectSchemaRequest(
                sleekflowCompanyId,
                schemaIdStr));

        var schema = response.Message;

        if (!schema.PrimaryProperty.Config.IsAutoGenerated)
        {
            var primaryProperty = schema.PrimaryProperty;

            results.Add(
                new ActionNeedConfig(
                    primaryProperty.UniqueName,
                    $"{CreateSchemafulObjectV2StepArgs.PrimaryPropertyValueName}",
                    primaryProperty.DisplayName,
                    ActionGroup,
                    ActionSubgroup,
                    GetInputType(primaryProperty.DataType.Name),
                    "Primary Property",
                    new()
                    {
                        ["required"] = null
                    },
                    null,
                    null,
                    null,
                    null,
                    null,
                    null,
                    true,
                    null));
        }

        foreach (var property in schema.Properties)
        {
            var needConfig = new ActionNeedConfig(
                property.UniqueName,
                $"{(property.IsRequired
                    ? CreateSchemafulObjectV2StepArgs.MandatoryPropertiesName
                    : CreateSchemafulObjectV2StepArgs.OptionalPropertiesName)}.{property.Id}",
                property.DisplayName,
                ActionGroup,
                ActionSubgroup,
                GetInputType(property.DataType.Name),
                property.IsRequired ? "Mandatory" : "Optional",
                new(),
                null,
                property.Options?
                    .OrderBy(x => x.DisplayOrder)
                    .Select(
                        x => new NeedConfigOption(
                            x.Id,
                            x.Value,
                            x.Id,
                            null,
                            null))
                    .ToList(),
                null,
                null,
                null,
                null,
                true,
                null);

            if (property.IsRequired)
            {
                needConfig.Validations!.Add("required", null);
            }

            if (property.DataType.Name is "boolean")
            {
                needConfig.Options =
                [
                    new NeedConfigOption(
                        "true",
                        "True",
                        "true",
                        null,
                        null),

                    new NeedConfigOption(
                        "false",
                        "False",
                        "false",
                        null,
                        null)
                ];
            }

            // This has to be the last action in loop
            if (needConfig.InputType is not "unknown")
            {
                results.Add(needConfig);
            }
        }

        return results;
    }

    private static string GetInputType(string dataType)
        => dataType switch
        {
            "numeric" or "decimal" => "number",
            "single_line_text" => "text",
            "single_choice" => "single_select",
            "multiple_choice" => "multi_select",
            "boolean" => "single_select",
            "date" => "date",
            "datetime" => "datetime",
            _ => "unknown"
        };
}