using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Categories;

public class CategoryIndexDto
{
    [SimpleField(IsKey = true, IsHidden = false)]
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [SimpleField(IsFilterable = true, IsHidden = false)]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [SimpleField(IsHidden = true)]
    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [SimpleField(IsHidden = true)]
    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [SearchableField(IsFilterable = true, IsFacetable = true, IsSortable = true)]
    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [SimpleField]
    [JsonProperty(Category.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [SimpleField]
    [JsonProperty(Category.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [SimpleField]
    [JsonProperty(Category.PropertyNamePlatformData)]
    public PlatformDataIndexDto PlatformData { get; set; }

    [SimpleField(IsFilterable = true)]
    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonConstructor]
    public CategoryIndexDto(
        string id,
        string sleekflowCompanyId,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string storeId,
        List<Multilingual> names,
        List<Description> descriptions,
        PlatformDataIndexDto platformData,
        List<string> recordStatuses)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        StoreId = storeId;
        Names = names;
        Descriptions = descriptions;
        PlatformData = platformData;
        RecordStatuses = recordStatuses;
    }

    public CategoryIndexDto(Category category)
    {
        Id = category.Id;
        SleekflowCompanyId = category.SleekflowCompanyId;
        CreatedAt = category.CreatedAt;
        UpdatedAt = category.UpdatedAt;
        StoreId = category.StoreId;
        Names = category.Names;
        Descriptions = category.Descriptions;
        PlatformData = new PlatformDataIndexDto(category.PlatformData);
        RecordStatuses = category.RecordStatuses;
    }
}