using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class CreateTriggerNeeds : ITrigger<
    CreateTriggerNeeds.CreateTriggerNeedsInput,
    CreateTriggerNeeds.CreateTriggerNeedsOutput>
{
    private readonly INeedConfigService _needConfigService;

    public CreateTriggerNeeds(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class CreateTriggerNeedsInput
    {
        [Required]
        [JsonProperty("needs")]
        public List<TriggerNeedConfig> Needs { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonConstructor]
        public CreateTriggerNeedsInput(
            List<TriggerNeedConfig> needs,
            string? version)
        {
            Needs = needs;
            Version = version;
        }
    }

    public class CreateTriggerNeedsOutput
    {
        [JsonProperty("needs")]
        public List<TriggerNeedConfig> Needs { get; set; }

        [JsonConstructor]
        public CreateTriggerNeedsOutput(List<TriggerNeedConfig> needs)
        {
            Needs = needs;
        }
    }

    public async Task<CreateTriggerNeedsOutput> F(CreateTriggerNeedsInput input)
    {
        return new CreateTriggerNeedsOutput(
            await _needConfigService.CreateTriggerNeedsAsync(
                input.Version,
                input.Needs));
    }
}