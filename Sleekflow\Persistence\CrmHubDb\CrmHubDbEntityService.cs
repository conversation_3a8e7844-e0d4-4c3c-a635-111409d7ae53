namespace Sleekflow.Persistence.CrmHubDb;

public interface ICrmHubDbEntityService
{
    bool IsSupportedEntityTypeName(string entityTypeName);

    bool IsNotSupportedEntityTypeName(string entityTypeName);
}

public class CrmHubDbEntityService : ICrmHubDbEntityService
{
    public bool IsSupportedEntityTypeName(string entityTypeName)
    {
        var entityTypeNames = new HashSet<string>()
        {
            "Activity",
            "Company",
            "Contact",
            "Lead",
            "Note",
            "Opportunity",
            "User",
            "SalesOrder",
            "SalesOrderItem",
            "Store",
            "Campaign",
            "CampaignMember",
            "Language",
            "Product",
            "ProductVariant",
            "Currency",
            "Salesperson",
            "AwarenessSource"
        };

        return entityTypeNames.Contains(entityTypeName);
    }

    public bool IsNotSupportedEntityTypeName(string entityTypeName)
    {
        return !IsSupportedEntityTypeName(entityTypeName);
    }
}