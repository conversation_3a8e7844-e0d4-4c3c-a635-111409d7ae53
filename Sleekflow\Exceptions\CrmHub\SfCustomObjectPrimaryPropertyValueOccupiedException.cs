﻿namespace Sleekflow.Exceptions.CrmHub;

public class SfCustomObjectPrimaryPropertyValueOccupiedException : ErrorCodeException
{
    public string PrimaryPropertyValue { get; set; }

    public SfCustomObjectPrimaryPropertyValueOccupiedException(string primaryPropertyValue)
        : base(
            ErrorCodeConstant.SfCustomObjectPrimaryPropertyValueOccupiedException,
            "Schemaful object with inputted primary property value already exists.")
    {
        PrimaryPropertyValue = primaryPropertyValue;
    }
}