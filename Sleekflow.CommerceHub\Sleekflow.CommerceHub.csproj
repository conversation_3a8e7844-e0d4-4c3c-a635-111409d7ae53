<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
        <PackageReference Include="Mime-Detective" Version="24.12.2" />
        <PackageReference Include="Scriban" Version="5.12.1" />
        <PackageReference Include="ShopifySharp" Version="6.8.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
        <ProjectReference Include="..\Sleekflow.CommerceHub.Payments\Sleekflow.CommerceHub.Payments.csproj" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="Sleekflow.CommerceHub.Tests" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Shopify" />
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>