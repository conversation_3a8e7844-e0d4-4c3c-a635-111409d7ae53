using System.ComponentModel.DataAnnotations;
using Azure.Storage.Blobs.Specialized;
using Sleekflow.CommerceHub.Blobs;
using Sleekflow.CommerceHub.Models.Blobs;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Images;

public interface IImageService
{
    Task<List<Image>> GetImagesAsync(
        List<ImageDto> imageDtos,
        string sleekflowCompanyId,
        string storeId);

    Task ClearUnusedImagesAsync(
        List<Image> images,
        List<Image> existingImages,
        string sleekflowCompanyId,
        string storeId);
}

public class ImageService : IImageService, ISingletonService
{
    private readonly IBlobService _blobService;

    public ImageService(IBlobService blobService)
    {
        _blobService = blobService;
    }

    public async Task<List<Image>> GetImagesAsync(List<ImageDto> imageDtos, string sleekflowCompanyId, string storeId)
    {
        var images = new List<Image>
        {
            Capacity = imageDtos.Count
        };
        foreach (var imageDto in imageDtos)
        {
            if (imageDto.ImageUrl != null)
            {
                if (imageDto.ImageUrl.Contains(_blobService.GetImageBlobServiceClient().Uri.ToString()))
                {
                    if (imageDto.BlobName == null)
                    {
                        throw new SfValidationException(
                            new List<ValidationResult>()
                            {
                                new ValidationResult(
                                    "The blob name is required when the image url is a blob url.",
                                    new List<string>()
                                    {
                                        nameof(imageDto.BlobName)
                                    })
                            });
                    }

                    var convertedImages = await _blobService.ConvertImageBlobNamesToImagesAsync(
                        new List<string>
                        {
                            imageDto.BlobName
                        },
                        sleekflowCompanyId,
                        storeId);
                    if (convertedImages.Any())
                    {
                        images.Add(convertedImages.First());
                    }
                }
                else
                {
                    var blobNames = await CopyImagesFromUrlsAndGetBlobNames(
                        sleekflowCompanyId,
                        storeId,
                        new List<string>
                        {
                            imageDto.ImageUrl
                        });

                    var convertedImages = await _blobService.ConvertImageBlobNamesToImagesAsync(
                        blobNames,
                        sleekflowCompanyId,
                        storeId);
                    if (convertedImages.Any())
                    {
                        images.Add(convertedImages.First());
                    }
                }
            }
            else if (imageDto.BlobName != null)
            {
                var convertedImages = await _blobService.ConvertImageBlobNamesToImagesAsync(
                    new List<string>
                    {
                        imageDto.BlobName
                    },
                    sleekflowCompanyId,
                    storeId);
                if (convertedImages.Any())
                {
                    images.Add(convertedImages.First());
                }
            }
        }

        return images;
    }

    public async Task ClearUnusedImagesAsync(
        List<Image> images,
        List<Image> existingImages,
        string sleekflowCompanyId,
        string storeId)
    {
        var usedImageBlobNames = images
            .Where(x => x.BlobName != null)
            .Select(x => x.BlobName!)
            .ToHashSet();

        var unusedImages = existingImages
            .Where(ei => ei.BlobName != null)
            .Where(ei => !usedImageBlobNames.Contains(ei.BlobName!))
            .ToList();

        await _blobService.DeleteBlobsAsync(
            unusedImages.Select(i => i.BlobName!).ToList(),
            sleekflowCompanyId,
            storeId,
            BlobTypes.Image);
    }

    private async Task<List<string>> CopyImagesFromUrlsAndGetBlobNames(
        string sleekflowCompanyId,
        string storeId,
        IReadOnlyCollection<string> imageUrls)
    {
        var imageBlobSasUrls = await _blobService.CreateUploadBlobSasUrlsAsync(
            sleekflowCompanyId,
            storeId,
            "Image",
            imageUrls.Count);

        var imageBlobIds = imageBlobSasUrls.Select(bsu => bsu.BlobId).ToList();
        var imageUrlsAndBlobIds = imageUrls.Zip(imageBlobIds);

        var blobServiceClient = _blobService.GetImageBlobServiceClient();

        await Parallel.ForEachAsync(
            imageUrlsAndBlobIds,
            async (tuple, cancellationToken) =>
            {
                var (imageUrl, imageBlobId) = tuple;

                var blobClient = blobServiceClient.GetBlockBlobClient(imageBlobId);

                var copyFromUriOperation = await blobClient.StartCopyFromUriAsync(
                    new Uri(imageUrl),
                    cancellationToken: cancellationToken);
                await copyFromUriOperation.WaitForCompletionAsync(
                    TimeSpan.FromMilliseconds(500.0),
                    CancellationToken.None);
            });

        return imageBlobSasUrls.Select(i => i.BlobName).ToList();
    }
}