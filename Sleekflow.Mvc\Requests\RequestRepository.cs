﻿using Microsoft.Extensions.Logging;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Mvc.Requests;

public interface IRequestRepository : IRepository<Request>
{
}

public class RequestRepository : BaseRepository<Request>, IRequestRepository
{
    public RequestRepository(
        ILogger<BaseRepository<Request>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}