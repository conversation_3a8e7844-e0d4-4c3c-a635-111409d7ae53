﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.ProviderConfigs;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("sys_config")]
public class ProviderConfig : Entity, IHasRecordStatuses
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("key")]
    public string Key { get; set; }

    [JsonProperty("entity_type_name_to_sync_config_dict")]
    public Dictionary<string, SyncConfig> EntityTypeNameToSyncConfigDict { get; set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("is_authenticated")]
    public bool IsAuthenticated { get; set; }

    [JsonProperty("default_region_code")]
    public string DefaultRegionCode { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty("provider_limit")]
    public ProviderLimit? ProviderLimit { get; set; }

    [JsonConstructor]
    public ProviderConfig(
        string id,
        string sleekflowCompanyId,
        string key,
        Dictionary<string, SyncConfig> entityTypeNameToSyncConfigDict,
        string providerName,
        bool isAuthenticated,
        string defaultRegionCode,
        List<string> recordStatuses,
        ProviderLimit? providerLimit)
        : base(id, "ProviderConfig")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        Key = key;
        EntityTypeNameToSyncConfigDict = entityTypeNameToSyncConfigDict;
        ProviderName = providerName;
        IsAuthenticated = isAuthenticated;
        DefaultRegionCode = defaultRegionCode;
        RecordStatuses = recordStatuses;
        ProviderLimit = providerLimit;
    }
}