using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Agents;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.States;
using Sleekflow.Models.Chats;

namespace Sleekflow.FlowHub.Internals.ChatHistory;

public interface IChatHistoryService
{
    Task<List<SfChatEntry>> GetConversationMessagesAsync<T>(
        ProxyState state,
        string? contactId,
        AgentConfig config,
        CallStep<T> callStep,
        string? channelId) where T : TypedCallStepArgs;
}

public class ChatHistoryService : IChatHistoryService, IScopedService
{
    private readonly ICoreCommander _coreCommander;
    private readonly IStateEvaluator _stateEvaluator;

    public ChatHistoryService(
        ICoreCommander coreCommander,
        IStateEvaluator stateEvaluator
    )
    {
        _coreCommander = coreCommander;
        _stateEvaluator = stateEvaluator;
    }

    public async Task<List<SfChatEntry>> GetConversationMessagesAsync<T>(
        ProxyState state,
        string? contactId,
        AgentConfig config,
        CallStep<T> callStep,
        string? channelId) where T : TypedCallStepArgs
    {
        var retrievalWindowTimestamp = await GetRetrievalWindowTimestampAsync(state, callStep);
        var finalChannelId = channelId ?? config.ChannelId;
        var response = await _coreCommander.ExecuteAsync<GetConversationLastMessagesOutput>(
            state.Origin,
            "GetConversionLastMessages",
            new GetConversationLastMessagesInput(
                state.Identity,
                contactId!,
                [ChannelTypes.WhatsAppCloudApi],
                finalChannelId,
                0,
                config.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
                retrievalWindowTimestamp));

        return response?.ConversationMessages.Select(
            c =>
            {
                var chatEntry = new SfChatEntry();
                if (c.IsSentFromSleekflow)
                {
                    chatEntry.Bot = c.MessageContent;
                }
                else
                {
                    chatEntry.User = c.MessageContent;
                }

                return chatEntry;
            }).ToList() ?? [];
    }

    private async Task<DateTimeOffset?> GetRetrievalWindowTimestampAsync<T>(
        ProxyState state,
        CallStep<T> callStep) where T : TypedCallStepArgs
    {
        var property = callStep.Args.GetType().GetProperty("RetrievalWindowTimestampExpr");
        if (property == null)
        {
            return null;
        }

        var retrievalWindowTimestampExpr = property.GetValue(callStep.Args) as string;
        if (retrievalWindowTimestampExpr == null)
        {
            return null;
        }

        var retrievalWindowTimestamp = await _stateEvaluator.EvaluateExpressionAsync<string>(
            state,
            retrievalWindowTimestampExpr);

        return DateTimeOffset.TryParse(retrievalWindowTimestamp, out var result) ? result : null;
    }
}
