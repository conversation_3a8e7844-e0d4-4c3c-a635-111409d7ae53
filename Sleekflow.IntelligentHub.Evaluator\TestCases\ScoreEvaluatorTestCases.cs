using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.LeadScores;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator;

public partial class LeadScoreEvaluatorTest
{
    private static readonly LeadScoreConfig DefaultLeadScoreConfig = new LeadScoreConfig(
        "Default",
        """
        Use the following guidelines and framework:

        ### Framework for Lead Scoring and Category:

        Assign a lead score from **0 to 100** and provide the following lead stage as follows:

        - **80-100**: Hot Lead (Category: High), ready to purchase or very likely to convert.
        - **60-79**: Warm Lead (Category: Medium), moderately likely to convert.
        - **40-59**: Lukewarm lead (Category: Medium), low likelihood to convert.
        - **0-39**: Cold Lead (Category: Low), unlikely to convert.

        ### Criteria and Weightage for Lead Score:

        1. **Intent & Interest Level (30%)**:
            - Does the lead explicitly ask about features, specifications, availability, or compatibility?
            - Do they compare your product/service to competitors or alternatives?
            - Do they indicate a potential decision to purchase?
        2. **Buying Signals (35%)**:
            - Does the lead express urgency or mention a specific timeframe for a decision?
            - Does the lead inquire about pricing models, discounts, promotions, or payment options?
            - Does the lead try to negotiate the price?
        3. **Depth & Specificity of Questions (25%)**:
            - Do their questions show a deeper understanding of the product's domain or industry?
            - Do they ask follow-up questions or seek clarification?
            - Are they actively engaged in meaningful discussion?
        4. **Sentiment & Engagement Tone (5%)**:
            - Is the lead's reaction positive or enthusiastic?
            - Does their tone remain positive or shift to positive after clarifications?
        5. **Customer Fit & Alignment (5%)**:
            - Based on their demographic or profile, how well do they align with the target customer persona (e.g., role, industry)?

        ### Instructions for Lead Scoring Response:

        1. Calculate the overall lead score using the weighted criteria.
        2. Assign a category from the following:
            - **High**: Score ≥80 (Hot Lead).
            - **Medium**: Score 40-79 (Warm or Lukewarm Lead).
            - **Low**: Score <40 (Cold Lead), and only after multiple warming attempts with no response or interest.
        3. Provide a concise explanation (up to 50 words) citing key indicators from the conversation that justify the score and category.

        ### Special Cases for Category:

        - **Human** (Category: Human): If the user explicitly requests to speak to a human agent, file a complaint, or escalate to a manager, categorize the interaction as Human, and still provide a lead score. If an interaction triggers both a high score and a request for human escalation, you should prioritize the Human category but still show the numeric lead score.
        - **Drop Off** (Category: Drop Off): If the user explicitly indicates that they have found the information they are looking for and would like to end the conversation, have no interest, or do not want to continue the conversation, categorize the interaction as Drop Off, and still provide a lead score.

        ### Category Restrictions:
        Use only the following categories: "High", "Medium", "Low", "Human", or "Drop Off". Do not introduce any other categories.
        """);

    private static readonly LeadScoreConfig NewLeadScoreConfig = new LeadScoreConfig(
        "New",
        """
        Use the following guidelines and framework:
        ### Criteria and Weightage for Lead Score:
        1. **Intent & Interest Level (0-100)**:
            - Does the lead explicitly ask about features, specifications, availability, or compatibility?
            - Do they compare your product/service to competitors or alternatives?
            - Do they indicate a potential decision to purchase?
        2. **Buying Signals (0-100)**:
            - Does the lead express urgency or mention a specific timeframe for a decision?
            - Does the lead inquire about pricing models, discounts, promotions, or payment options?
            - Does the lead try to negotiate the price?
        3. **Depth & Specificity of Questions (0-100)**:
            - Do their questions show a deeper understanding of the product's domain or industry?
            - Do they ask follow-up questions or seek clarification?
        4. **Sentiment & Engagement Tone (0-100)**:
            - Is the lead's reaction positive or enthusiastic?
            - Does their tone remain positive or shift to positive after clarifications?
        5. **Customer Fit & Alignment (0-100)**:
            - Based on their demographic or profile, how well do they align with the target customer persona (e.g., role, industry)?
        ### Instructions for Lead Scoring Response:
        1. Calculate the overall lead score using the weighted criteria.
        2. Assign a category from the following:
            - **High**: Score ≥80 (Hot Lead).
            - **Medium**: Score 40-79 (Warm or Lukewarm Lead).
            - **Low**: Score <40 (Cold Lead), and only after multiple warming attempts with no response or interest.
        3. Provide a concise explanation (up to 50 words) citing key indicators from the conversation that justify the score and category.
        ### Special Cases for Category:
        - **Human** (Category: Human): If the user explicitly requests to speak to a human agent, file a complaint, or escalate to a manager, categorize the interaction as Human, and still provide a lead score. If an interaction triggers both a high score and a request for human escalation, you should prioritize the Human category but still show the numeric lead score.
        - **Drop Off** (Category: Drop Off): If the user explicitly indicates that they have found the information they are looking for and would like to end the conversation, have no interest, or do not want to continue the conversation, categorize the interaction as Drop Off, and still provide a lead score.
        ### Category Restrictions:
        Use only the following categories: "High", "Medium", "Low", "Human", or "Drop Off". Do not introduce any other categories.
        """);

    private static IEnumerable<LeadScoreTestCase> GetTestCases()
    {
        return GetSimpleTestCases();
    }

    private static IEnumerable<LeadScoreTestCase> GetDefaultTestCases()
    {
        yield return new LeadScoreTestCase(
            "Hot Lead - Interested in all features, urgent timeframe",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm interested in the HKBN x Bowtie plan. If I sign up for a new broadband plan, can I add this healthcare package? I'm particularly interested in the unlimited video consultations and dental cleaning. Is the $99 monthly price guaranteed for the whole year, and what payment methods do you accept? I need to decide by the end of the week."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, you can add the healthcare package to a new broadband plan. The $99 monthly price is guaranteed for 12 months. We accept various payment methods including credit card, autopay, and PPS (note: each PPS can only register this service 5 times). Shall we proceed with the broadband signup and healthcare package?")
            ],
            new EvaluatedScore(
                "High",
                85,
                "Asks about key features (video consults, dental), price guarantee, payment options, and indicates an urgent decision timeline. High likelihood to convert."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Warm Lead - Existing customer, specific questions about body check",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm a current HKBN customer. Can you tell me more about the full body check? What does it include, and where can I get it done? Also, how do I activate the plan after I register?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The full body check is provided by Bowtie & JP Health (Bowtie Clinic, located at Wan Chai). It includes a diabetes check (fasting glucose requires 9-hour fasting, water is allowed), urine test, and blood draw. You can activate the plan via My HKBN - go to the Message Center and look for the 'Activate Bowtie Healthcare Service' message, then get the activation code.")
            ],
            new EvaluatedScore(
                "Medium",
                70,
                "Shows interest with specific questions about the body check and activation. Lacks strong urgency or price-related inquiries."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Lukewarm Lead - Basic Inquiry",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What is this Bowtie healthcare plan about?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "It's a 4-in-1 healthcare service managed by Bowtie. It's not an insurance product but a health membership plan, and it includes unlimited video consultations, dental cleaning & checkup, a full body check, and a flu vaccine for $99/month. Would you like to know more about any of these services in particular?")
            ],
            new EvaluatedScore(
                "Medium",
                50,
                "Shows initial interest but asks a very general question. Requires further probing to determine actual intent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Cold Lead - Explicitly not interested",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm not interested in healthcare plans, just browsing."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay, no problem. Let me know if you have any questions about our broadband or other services.")
            ],
            new EvaluatedScore(
                "Low",
                20,
                "Explicitly states lack of interest."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Request for Human Agent",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "This is too complicated. Can I speak to someone directly about this plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Of course! I can connect you with a customer service representative who can explain the plan in detail. Please wait while I transfer you.")
            ],
            new EvaluatedScore(
                "Human",
                65,
                "Requires escalation. Customer is somewhat interested but needs human assistance."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Customer indicates no interest",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "No thanks, I am not interested"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your time.")
            ],
            new EvaluatedScore(
                "Drop Off",
                10,
                "The customer is not interested in the plan and wants to drop off."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Warm Lead - Specific Compatibility Question",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm already on a discounted HKBN plan. Can I still add the Bowtie healthcare service for $99?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, even with a discounted plan, you can add the Bowtie healthcare service for $99/month. Please note that each customer(PPS) can only register this service for maximum 5 times.")
            ],
            new EvaluatedScore(
                "Medium",
                75,
                "Shows specific interest in compatibility with their existing plan and confirms pricing."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Lukewarm Lead - Negative Sentiment, Needs Clarification",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "This sounds too good to be true. What's the catch with the unlimited video consultations?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "There's no catch! The video consultations are truly unlimited during service hours (Mon-Fri 9am-7pm, Sat 9am-1pm, excluding public holidays and typhoon/black rain warnings). They are for non-emergency issues, and basic medication with free delivery in HK is included. Does this clarify things for you?")
            ],
            new EvaluatedScore(
                "Medium",
                60,
                "Expresses skepticism but engages with a specific question. Addressing concern could improve sentiment."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Warm Lead - Detailed Question About Dental Service",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "For the dental cleaning, what exactly is included? Is it just a basic cleaning, or does it include scaling and polishing? Also, can I go to any CMER Dental clinic?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The dental cleaning (provided by CMER Dental) includes a checkup, scaling, polishing, and it *might* include X-rays depending on the dentist's assessment. You can go to designated CMER Dental clinics in Central, Diamond Hill, Kwun Tong, and Sha Tin.")
            ],
            new EvaluatedScore(
                "Medium",
                72,
                "Demonstrates deeper interest by inquiring about specifics of dental cleaning and clinic options."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Warm Lead - Exploring Options, Price Sensitive",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'm looking at the HKBN broadband plans and saw something about a healthcare thing with Bowtie. What's the deal?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi there! Yes, we offer the HKBN x Bowtie 4-in-1 Healthcare Service for just $99/month when you sign up for a new broadband plan or if you're an existing customer. It includes unlimited video doctor visits, dental cleaning, a body check, and a flu shot. Is that something you'd be interested in?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hmm, sounds good. $99 a month on top of the broadband though... is there any discount if I take a longer broadband contract?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Let me check on that for you. While there isn't a direct discount on the healthcare plan for longer broadband contracts, we often have promotional bundles that could save you money overall. Could you tell me which broadband plan you're considering, and I'll see what options are available?")
            ],
            new EvaluatedScore(
                "Medium",
                68,
                "Shows interest but is price-sensitive.  Exploring options and looking for discounts. Needs encouragement to convert."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Lukewarm Lead - Hesitant due to lack of familiarity",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I keep seeing ads for this Bowtie healthcare with HKBN. Is it any good? I've never heard of Bowtie before."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Bowtie is Hong Kong's first virtual insurer licensed by the Insurance Authority. The healthcare plan isn't insurance *per se*, but a health membership plan for $99 a month. It covers video consultations, dental, a checkup, and a flu jab, providing peace of mind. You can learn more about Bowtie at bowtie.com.hk. What are your health concerns?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "So it's not insurance? What's the catch with the video doctor? Are they real doctors?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Absolutely real, registered doctors! The video consultations are for minor ailments -- coughs, colds, skin rashes etc. If it's serious, they'll tell you to seek in-person care. The service is available Mon-Fri 9am-7pm, Sat 9am-1pm (except holidays and during storms). Does that help clarify things? Basic medication with free delivery included, with doctor notes.")
            ],
            new EvaluatedScore(
                "Medium",
                55,
                "Hesitant due to lack of familiarity with the brand, needs reassurance and information. Potential to convert with trust-building."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "High Lead - Specific Need, Ready to Purchase",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "My kid needs a flu shot, and I need a dental cleaning. Can this Bowtie thing help with that, and how quickly can we get it done?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes! The plan includes a flu vaccine and two dental cleanings per year. The dental cleanings are at CMER Dental, which has clinics in Central, Diamond Hill, Kwun Tong, and Sha Tin.  The flu vaccine can be done at Bowtie & JP Health in Wan Chai.  All services require booking in advance. Are you an existing HKBN customer?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "No, but I'm moving soon. Can I sign up for broadband *and* this healthcare thing at the same time, and how do I book the appointments?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Absolutely! You can sign up for both together. As soon as your service is activated, you'll get an email with instructions on how to activate the Bowtie service and book your appointments through their platform.  Would you like me to help you find a broadband plan that suits your needs?")
            ],
            new EvaluatedScore(
                "High",
                88,
                "Has specific needs (flu shot, dental cleaning), asks about timing and process, and is ready to sign up for both services. Very high likelihood to convert."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Lukewarm Lead - Concerned about Data Privacy",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm worried about my data. If I use the video doctor, does Bowtie share my medical information with HKBN?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's a valid concern.  Bowtie and HKBN are separate companies, and Bowtie has to adhere to strict data privacy regulations as a virtual insurer, they won't share medical info with HKBN. MyDoc provides the consultation service and they also have strict policy to protect customer data. You can find more details about the policy in Bowtie website.  HKBN just provides the platform for you to access the service. Does that help ease your concern?")
            ],
            new EvaluatedScore(
                "Medium",
                45,
                "Expresses concern about data privacy, needs reassurance. Addressing concerns is crucial to moving forward."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Warm Lead - Exploring for family, but wants to know age limit",
            [
                new ChatMessageContent(AuthorRole.User, "Hi, can my parents use the video consultation?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi! Yes, the service are designed for people age 18-80 years old. Is there anything that you want to consult for with our doctor now?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "ok, great! that means my parents are eligible, is there any other limitation?")
            ],
            new EvaluatedScore(
                "Medium",
                65,
                "Looking for information for family and considering the service is a great sign of a warm lead"),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Warm Lead - Broadband Customer Exploring Healthcare, Needs Step-by-Step Guidance",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'm an existing HKBN broadband customer. I saw something about a healthcare plan you offer, but I'm a bit confused. What's included, and how does it work?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi! Great to hear you're interested. As an HKBN customer, you can add the Bowtie 4-in-1 Healthcare Service for $99/month. It includes unlimited video consultations, two dental cleanings a year at CMER Dental clinics, one full body check at Bowtie & JP Health, and one flu vaccine. Would you like me to break down each service?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, please. Start with the video consultations. How do I actually *use* them?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Okay! First, after you sign up and activate the plan through My HKBN, you'll receive a separate email with instructions on how to activate your Bowtie account. Then, you log in to the Bowtie platform and select 'Video Consultation.' This will redirect you to the MyDoc platform, where you can book a consultation with a registered doctor. They can provide medical advice, prescriptions (with free delivery), and even sick leave certificates if needed. The service are designed for people age 18-80 years old.The service hours are Mon-Fri 9am-7pm, Sat 9am-1pm, excluding holidays and storm warnings. Any questions so far?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Okay, that makes sense. And what about the dental cleaning? Where can I go, and what's included?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "For dental cleanings, you can visit designated CMER Dental clinics in Central, Diamond Hill, Kwun Tong, and Sha Tin. The cleaning includes a checkup, scaling, polishing, and it *might* include X-rays, depending on the dentist's assessment. To book, you'll need to do it through the Bowtie platform. Also, for new HKBN customer, each customer(PPS) can only register this service for maximum 5 times, would you like me to check on your registration status first?")
            ],
            new EvaluatedScore(
                "Warm",
                73,
                "Existing customer showing genuine interest but needs detailed, step-by-step guidance. High potential for conversion with clear explanations and support."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Lukewarm Lead - Comparing to Alternatives, Concerned About Limitations",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm looking at different healthcare options, and this HKBN/Bowtie thing seems cheap. But is it *really* unlimited video consultations? What if I need to see a specialist?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "It's unlimited video consultations with general practitioners for non-emergency issues. If the doctor feels you need to see a specialist, they can provide a referral letter. However, the specialist consultation wouldn't be covered under this plan. This plan managed by Bowtie and not an insurance product."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "So, I'd still have to pay for the specialist myself? What about the medication? Is that really free, or are there hidden costs?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, you would need to pay for the specialist consultation separately. For the video consultation, the basic medication is included with free delivery to your Hong Kong address. However, there are some limitations: the delivery not available during public holiday & black rain warning, also it is not applicable at outlying islands like (Lantau Island, Cheung Chau) & restricted area. Also, the doctor will determine the medication based on your condition. Do you have any chronic diseases? The service are designed for people age 18-80 years old"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hmm, so I am 81 year old, does this mean I am not eligible"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, due to the age limit you will not be able to apply for the plan")
            ],
            new EvaluatedScore(
                "Lukewarm",
                52,
                "Comparing options, skeptical about limitations and potential extra costs. Needs reassurance and a clear understanding of what's covered."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "High Lead - Has Specific Questions After Researching, Ready to Sign Up",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I've read about the HKBN x Bowtie plan, and it sounds good. I just have a few questions before I sign up. First, how long does it take to activate the Bowtie service after I get HKBN broadband? Also, my office is at Central, is it convenient for me to have a dental check-up?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Great! The Bowtie service is usually activated within 1-2 business days after your HKBN broadband is set up. You'll receive an email with activation instructions. And yes, there are CMER Dental clinics at Central in Central Building (1-3 Lower Ground Floor, 1102, Central Building, Central) so it's convenient for your checkup and scaling. Does the $99 plan sounds good for you now?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Perfect! And does the $99 monthly plan auto renew after 12 months and does each customer(PPS) can only register this service for maximum 5 times, and if so can I check on my registration status?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thanks for reminding! each customer(PPS) can only register this service for maximum 5 times, so let me check on your registration status. And the plan will auto-cancel, if you are still interested to use the service, you will need to register again")
            ],
            new EvaluatedScore(
                "High",
                82,
                "Has researched the plan, has a detailed question and ready to sign up"),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop off Lead - Already have other plan, explicitly not interested",
            [
                new ChatMessageContent(AuthorRole.User, "I already have insurance plan, thank you"),
                new ChatMessageContent(AuthorRole.Assistant, "Thank you for your time")
            ],
            new EvaluatedScore(
                "Drop Off",
                10,
                "Drop off, customer has insurance and not interested"),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Weird Lead - Conspiracy Theorist with Healthcare Concerns",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Is it true that this Bowtie healthcare plan is actually a government plot to track our dental records and control the population through fluoride in the scaling process? Asking for a friend...from outer space."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I can assure you that our HKBN x Bowtie 4-in-1 Healthcare Service is purely designed to provide convenient and affordable access to healthcare services. The dental cleaning is performed by qualified professionals at CMER Dental clinics, following strict hygiene and safety protocols. There's no secret agenda or government conspiracy involved! Each customer(PPS) can only register this service for maximum 5 times. Is there another medical services you are looking for?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Okay, but can the video doctor prescribe medication to counteract the effects of 5G radiation on my chakras? It's making my teeth tingle."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The video doctors can prescribe medication for common ailments such as colds, flu, and skin rashes. The service are designed for people age 18-80 years old. However, they are not qualified to provide advice on or prescribe medication related to 5G radiation or chakra alignment. But for a valid case, you may request doctor leave and doctor notes too. Perhaps seeing a specialist in that area?")
            ],
            new EvaluatedScore(
                "Lukewarm",
                40,
                "Expresses far-fetched concerns, but engages with the offer. A very difficult lead to convert, but still technically engaged."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Weird Lead - Pet Healthcare Inquiry (Again!)",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can I use the Bowtie flu shot for my chihuahua, Princess Fluffybutt? She's been sneezing a lot lately, and I'm worried she'll miss the dog show."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I understand your concern for Princess Fluffybutt, but the Bowtie 4-in-1 Healthcare Service is designed for *human* use only. It's not suitable for pets, the service are designed for people age 18-80 years old. I recommend consulting with a veterinarian for Princess Fluffybutt's healthcare needs. Is there any other medical services that I can provide assistance with?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "But she *identifies* as a human! She wears tiny hats and watches K-dramas all day. Surely there's an exception?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I apologize, but there are no exceptions. The video consultations are also design for people with human age 18-80 years old, you might want to consider bringing Princess Fluffybutt to register an veterinary. Is there any other services I can help you with?")
            ],
            new EvaluatedScore(
                "Cold",
                30,
                "Expresses strange and inappropriate demands, does not understand basic limitations. Unlikely to convert."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Weird Lead - Time Traveler Needing Ancient Remedies",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Greetings, I am a time traveler from the year 1788. I require leech therapy and a bloodletting ritual to cure my 'vapors.' Does this 'Bowtie' offer such archaic treatments?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Greetings, time traveler! While our HKBN x Bowtie 4-in-1 Healthcare Service doesn't offer leech therapy or bloodletting, our video doctors *can* provide modern medical advice and prescriptions for a variety of ailments. In addition, we have CMER Dental clinic for scaling and polishing too! The service are designed for people age 18-80 years old. Perhaps if there's any service I can provide assistance for?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Blast! Very well. Can your 'video doctor' at least prescribe laudanum for my melancholia? And does the 'MyDoc' have a horse for quick delivery?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our video doctors do not prescribe laudanum as it is no longer considered a safe or appropriate treatment. The service is manage through MyDoc and provide express for medication delivery. They can prescribe other medication and provide delivery to your Hong Kong addresses, but the location has some limitations with the Islands and restricted area. But the doctors can do sick leave applications and doctor notes.")
            ],
            new EvaluatedScore(
                "Lukewarm",
                48,
                "Presents a bizarre scenario, but engages with modern options after initial rejection. Low conversion likelihood, but strangely persistent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "高意向 - 現有客戶，積極詢問開通流程，關心PPS登記限制",
            new ChatMessageContent[]
            {
                new ChatMessageContent(AuthorRole.User, "你好，我已經是香港寬頻的客戶了，對你們這個Bowtie四合一醫療服務很有興趣，想瞭解一下開通流程。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！非常感謝您對我們服務的興趣。請問您是否知道每個PPS帳戶最多只能登記5次此服務？另外，每位香港身份證持有人在Bowtie指定網頁只能登記一次。"),
                new ChatMessageContent(AuthorRole.User, "我知道有這個限制，但我不太確定我的PPS帳號已經登記過幾次了，可以幫我查詢一下嗎？如果還可以登記，我就想立刻開通。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "好的，請您稍等，我幫您查詢一下您的PPS帳號登記次數。查詢結果顯示[假設結果：您還可以登記]。請問您是否需要我協助您完成開通程序？"),
            },
            new EvaluatedScore(
                "High",
                87,
                "客戶是現有客戶，主動詢問開通流程，並關心PPS登記限制，有明確的開通意願。"),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "中等意向 - 猶豫是否需要，詢問視像會診服務細節，關注藥物配送範圍",
            new ChatMessageContent[]
            {
                new ChatMessageContent(AuthorRole.User, "你們這個視像會診服務，是不是真的無限次？如果我住在離島，藥物可以送到嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！視像會診服務在服務時間內是無限次的。但藥物快遞服務範圍不包括離島區（如大嶼山、南丫島、長洲、馬灣及珀麗灣；東涌除外）及可能需要有關政府部門的許可才能進入之邊境禁區。"),
                new ChatMessageContent(AuthorRole.User, "那如果我只是普通的感冒，醫生可以開醫生紙嗎？"),
                new ChatMessageContent(AuthorRole.Assistant, "是的，如果醫生診斷您需要休息，是可以開立醫生紙的。"),
            },
            new EvaluatedScore(
                "Medium",
                65,
                "客戶對服務細節有疑問，但持續詢問，表示有一定興趣，但需要確認是否符合自身需求。"),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "低意向 - 詢問年齡限制，發現不符合資格",
            new ChatMessageContent[]
            {
                new ChatMessageContent(AuthorRole.User, "請問這個服務有沒有年齡限制？"),
                new ChatMessageContent(AuthorRole.Assistant, "您好！Bowtie醫療保健服務適用於 18 - 80歲人仕。"),
                new ChatMessageContent(AuthorRole.User, "不好意思，我今年82歲了，看來不符合資格。"),
                new ChatMessageContent(AuthorRole.Assistant, "非常抱歉，由於您已超過年齡限制，暫時無法使用此服務。感謝您的諮詢。"),
            },
            new EvaluatedScore(
                "Low",
                30,
                "客戶詢問年齡限制，發現不符合資格，明確表示無法使用，無轉化可能。"),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "怪異對話 - 詢問可否用作寵物醫療",
            new ChatMessageContent[]
            {
                new ChatMessageContent(AuthorRole.User, "請問這個服務可以用於我的狗狗嗎？牠最近好像感冒了。"),
                new ChatMessageContent(AuthorRole.Assistant, "非常抱歉，我們的Bowtie四合一醫療服務僅限於人類使用，不適用於寵物。建議您諮詢獸醫。"),
            },
            new EvaluatedScore(
                "Cold",
                15,
                "客戶提出不合理要求，不符合服務對象，無轉化可能。"),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "中等意向 - 詢問激活碼獲取及使用流程，準備註冊",
            new ChatMessageContent[]
            {
                new ChatMessageContent(AuthorRole.User, "請問啟動碼要怎麼獲得？在My HKBN的哪裡找？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！在計劃生效日，您會透過電郵收到啟動服務通知。然後登入My HKBN，在「信息中心」→「服務信息」,打開標題為「啟動Bowtie 醫療保健服務」的信息，按照指示獲取啟用碼。"),
                new ChatMessageContent(AuthorRole.User, "有了啟用碼之後呢？去哪裡啟動？"),
                new ChatMessageContent(AuthorRole.Assistant, "有了啟用碼後，請到Bowtie指定網頁，輸入啟用碼並填妥所需資料以完成啟動流程。"),
            },
            new EvaluatedScore(
                "Medium",
                68,
                "客戶詢問激活碼流程，表現出對服務的興趣，並準備開始註冊。"),
            [DefaultLeadScoreConfig]);
    }

    private static IEnumerable<LeadScoreTestCase> GetHKBNHandoffTestCasesFromKB()
    {
        // Hot Lead (Likely to Convert)
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Hot Lead - N Mobile APAC Plan",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I travel frequently to Asia and need a plan that works well in multiple countries. What do you recommend?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Our N Mobile APAC Plan offers 20GB local 5G data plus 10-day APAC travel data for $138 per month. You can also add a China mobile number for an additional $20"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "That's great. Can I start using it immediately?")
                ],
                new EvaluatedScore(
                    "High",
                    85,
                    "Shows strong interest in the N Mobile APAC Plan and ready to start using it immediately, indicating high conversion likelihood."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Hot Lead - Global SIM Plan",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Do you have a global data plan? I need coverage for multiple countries."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "We have a Global SIM plan with options for 10GB, 20GB, or 30GB monthly data, starting at $168"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "What's included for travel to Europe?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "It includes coverage in over 50 destinations, such as the UK, France, and Germany."),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Yes, this is perfect.")
                ],
                new EvaluatedScore(
                    "High",
                    87,
                    "Shows specific interest in product features, asks follow-up questions about coverage, and expresses clear satisfaction with the offering."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Hot Lead - INDICAID Testing Plan",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I'm interested in your INDICAID testing kits. Can I customize my monthly plan?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Absolutely! Our plans let you select two test kits monthly, starting at $48. The advanced plan includes a full cancer screening kit"),
                    // new ChatMessageContent(
                    //     AuthorRole.User,
                    //     "I like the advanced plan. How do I sign up?")
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I like the advanced plan. How do I buy this?")
                ],
                new EvaluatedScore(
                    "High",
                    90,
                    "Shows direct interest in a specific plan and explicitly asks how to sign up, indicating imminent conversion."),
                [NewLeadScoreConfig]);
        }

        // Cold Lead (Unlikely to Convert)
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Cold Lead - Just Browsing",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I'm just browsing. Not really looking to buy anything."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "That's okay! Would you like to explore some of our popular options for future reference?"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "No, I'm not interested right now."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "No problem. Have a great day!")
                ],
                new EvaluatedScore(
                    "Low",
                    20,
                    "Explicitly states lack of interest multiple times with no buying signals or engagement."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Cold Lead - Price Concern",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Your prices seem a bit high. I've seen cheaper options elsewhere."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Our plans include added benefits like travel data and bundled discounts with partners like KKday. Would you like to learn more?"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "No, thanks. I'm sticking with my current provider."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Understood. Feel free to contact us if you change your mind.")
                ],
                new EvaluatedScore(
                    "Low",
                    25,
                    "Expresses price objections and explicitly states preference for current provider with no interest in further engagement."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Cold Lead - Bowtie Healthcare Plan",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Do you have a really basic healthcare plan?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Yes, we offer the Bowtie Healthcare Plan for $99, including unlimited virtual consultations and check-ups"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "That's still more than I want to pay."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "I understand. Let me know if you'd like to revisit this later!")
                ],
                new EvaluatedScore(
                    "Low",
                    30,
                    "Shows initial interest but rejects the offering based on price with no further engagement."),
                [NewLeadScoreConfig]);
        }

        // Human (Requesting Agent)
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Human - Direct Request for Agent",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I'd like to discuss this in detail with someone. Can you connect me to an agent?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Of course! I'll transfer you to an agent immediately"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Thanks.")
                ],
                new EvaluatedScore(
                    "Human",
                    60,
                    "Explicitly requests to speak with a human agent, indicating potential interest but need for personal assistance."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Human - Dissatisfaction with AI",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "This conversation isn't helpful. I need to speak to a human agent."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "My apologies. Let me connect you to a specialist who can assist further"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Thank you.")
                ],
                new EvaluatedScore(
                    "Human",
                    45,
                    "Expresses dissatisfaction with the AI conversation and requests human assistance, with unclear level of interest in products."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Human - Specific Inquiry Requiring Human",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I have very specific questions about your Tri-Area Plan. Can I talk to someone?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Sure! I'll connect you to a representative who can provide detailed answers"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "That's great.")
                ],
                new EvaluatedScore(
                    "Human",
                    70,
                    "Shows interest in a specific plan and requests human assistance for detailed information, indicating moderate to high interest."),
                [NewLeadScoreConfig]);
        }

        // Drop Off (Customer Ending Due to Disinterest)
        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Drop Off - INDICAID Testing Plan Disinterest",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Can you tell me about your INDICAID monthly testing plans?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "We offer customizable plans starting at $48 per month for two kits"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Actually, I'm not interested anymore."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "No problem. Feel free to contact us if you change your mind. Goodbye!")
                ],
                new EvaluatedScore(
                    "Drop Off",
                    35,
                    "Initially shows interest but explicitly ends conversation due to disinterest after learning about offerings."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Drop Off - Evercare Wound Care Plan",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "What's the cost of your Evercare wound care plan?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "It's $499 per month and includes home visits by professional nurses"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "That's too expensive. I'll pass."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Understood. Thank you for considering us.")
                ],
                new EvaluatedScore(
                    "Drop Off",
                    30,
                    "Shows initial interest but explicitly rejects offering due to price and ends conversation."),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 50; i++)
        {
            yield return new LeadScoreTestCase(
                "Drop Off - Global SIM Plan",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I'm curious about your global travel data plans."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Our Global SIM plans cover 50+ destinations starting at $168"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I'll think about it. Bye."),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Thanks for your time! Have a nice day.")
                ],
                new EvaluatedScore(
                    "Drop Off",
                    40,
                    "Shows initial curiosity but ends conversation without commitment, though leaves possibility open for future interest."),
                [NewLeadScoreConfig]);
        }
    }

    private static IEnumerable<LeadScoreTestCase> GetHKBNHandoffTestCasesHKBNConversation()
    {
        // Additional Hot Lead Test Cases
        yield return new LeadScoreTestCase(
            "Hot Lead - 5G Plan with Roaming",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'm interested in the $188 5G plan with the roaming data option."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for your interest! Could you please provide your account number or phone number for verification?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, my account number is ********."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you! I've checked your eligibility, and you can apply for the $188 plan today. Would you like me to proceed with the application?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, please go ahead.")
            ],
            new EvaluatedScore(
                "High",
                88,
                "Directly expresses interest in a specific product, provides account details for verification, and clearly indicates intention to proceed with the application, showing extremely high conversion potential."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Broadband Switch",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Does your broadband offer include free installation? I'm thinking of switching."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, we're offering free installation for new customers signing up for our 1000M broadband plan. May I know your current provider and what plan you're using?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm currently with Provider X, and I pay $150/month for 500M."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Great! Our plan is $109/month for 1000M, which is faster and cheaper. Shall I help you start the application?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, let's do it.")
            ],
            new EvaluatedScore(
                "High",
                90,
                "Customer compares prices and services, then clearly indicates readiness to start the application process, showing very high purchase intent and immediate conversion potential."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Plan Upgrade with Roaming",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, can I upgrade my plan to include roaming data?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Absolutely! We have a roaming add-on for $60/month, including 10GB data for popular destinations. Would you like me to add it to your current plan?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That sounds good. Will I be charged starting this month?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, the charge will begin immediately, and your roaming data will activate within 24 hours. Shall I proceed?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, go ahead.")
            ],
            new EvaluatedScore(
                "High",
                85,
                "Existing customer explicitly requests plan upgrade, inquires about specific billing details, and confirms the upgrade, demonstrating strong purchase intent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Plan Switch Request",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can I switch to your $118 unlimited mobile data plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Sure! Let me check if you're eligible. Could you share your account number, please?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "It's ********."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you. You're eligible to switch, and there's no additional charge for the upgrade. Would you like me to proceed?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, please.")
            ],
            new EvaluatedScore(
                "High",
                87,
                "Customer directly inquires about a specific plan, provides account information, and agrees to immediate change after confirming no additional fees, showing high purchase intent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Fast Broadband with Installation",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What's the fastest broadband speed you offer? I need something for heavy streaming."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We offer 1000M broadband, perfect for streaming, gaming, and downloads. It's $109/month. Would you like to schedule installation?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That sounds good. When can the installation be done?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our earliest slot is this Saturday. Would that work for you?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, let's go with Saturday.")
            ],
            new EvaluatedScore(
                "High",
                89,
                "Customer states clear needs, expresses satisfaction with product details and pricing, and schedules a specific installation time, indicating extremely high conversion potential."),
            [DefaultLeadScoreConfig]);

        // Cold Lead Test Cases
        yield return new LeadScoreTestCase(
            "Cold Lead - Affordable Internet Plans",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Do you have any internet plans cheaper than $100?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Currently, our best offer is $109 for 1000M broadband. It's a great deal for the speed and reliability. Would you like more details?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll think about it."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "No problem. Let me know if you have any questions!")
            ],
            new EvaluatedScore(
                "Medium",
                52,
                "Customer inquires about products in a specific price range but shows no interest in the options provided, gives vague responses, and shows no intention to continue the conversation."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Cold Lead - Mobile Plan Data Needs",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I saw an ad for your mobile plans. What's the cheapest one?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our most affordable plan is $118 for 20GB of 5G data. It's great for moderate usage. Would you like to learn more?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I don't think I need that much data."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We also have a $98 plan with 10GB of data. Does that suit your needs better?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll get back to you.")
            ],
            new EvaluatedScore(
                "Medium",
                52,
                "Customer initially inquires about pricing, indicates first proposal has too much data, and still defers decision after receiving an alternative option, showing no clear purchase intent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Cold Lead - Just Browsing with Brochure Request",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm just browsing options right now. Do you have any brochures?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We don't have physical brochures, but I can share all the plan details and pricing with you via WhatsApp. Let me know if that works for you!"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, just send it over."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Done! Let me know if you'd like help choosing the right plan.")
            ],
            new EvaluatedScore(
                "Low",
                32,
                "Customer explicitly states they are just browsing, only requests information materials, and shows no interest in specific products or purchase intent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Cold Lead - Price Matching Inquiry",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Do you match competitor prices?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We offer competitive pricing, and I'd be happy to see if we can find a plan that fits your budget. Could you share the competitor's offer?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll need to check and let you know."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Sure, take your time. I'm here to help whenever you're ready.")
            ],
            new EvaluatedScore(
                "Medium",
                55,
                "Customer inquires about price matching policy, showing concern about pricing, but doesn't provide specific information or indicate a clear purchase timeline."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Cold Lead - Just Curious",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm not really looking to switch right now, just curious about your mobile plans."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's okay! We offer flexible plans starting from $98 for 5G data. I can send you the details if you'd like."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "No, that's fine for now."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Alright. Let me know if you have questions in the future!")
            ],
            new EvaluatedScore(
                "Low",
                25,
                "Customer explicitly states no intention to switch providers, asks questions only out of curiosity, and declines further information, showing no buying signals."),
            [DefaultLeadScoreConfig]);

        // Drop Off Test Cases
        yield return new LeadScoreTestCase(
            "Drop Off - Staying with Current Provider",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I've decided to stick with my current provider. Please stop contacting me."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Understood. We'll update our records to stop further contact. Thank you for your time!")
            ],
            new EvaluatedScore(
                "Drop Off",
                15,
                "Customer clearly states they will not switch providers and requests to stop contact, indicating no possibility of purchase."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Not Interested",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm not interested in any plans right now."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's fine. If you ever need assistance in the future, feel free to reach out. Have a great day!")
            ],
            new EvaluatedScore(
                "Drop Off",
                20,
                "Customer directly states no interest in any plans, showing no intention to continue the conversation or learn more details."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Do Not Message",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Please don't message me again."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Apologies for any inconvenience caused. We'll make sure no further messages are sent to you.")
            ],
            new EvaluatedScore(
                "Drop Off",
                10,
                "Customer explicitly requests to stop communication, showing no purchase intent and intending to end all contact."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Stop Contacting",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Stop calling and messaging me."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We understand and will ensure no further contact. Thank you for letting us know.")
            ],
            new EvaluatedScore(
                "Drop Off",
                5,
                "Customer strongly requests to stop all forms of contact, with a firm attitude showing no conversion possibility."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Not Looking for Provider",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm not looking for a new provider. Goodbye."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Got it. If you ever change your mind, we'll be here to help. Have a great day!")
            ],
            new EvaluatedScore(
                "Drop Off",
                15,
                "Customer clearly states they are not looking for a new provider and actively ends the conversation, with no further communication intent."),
            [DefaultLeadScoreConfig]);

        // Human Test Cases
        yield return new LeadScoreTestCase(
            "Human - Broadband Plans Inquiry",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I want to speak to a real person about your broadband plans."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Of course! I'll arrange for a customer service representative to contact you. When is a good time for them to call?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Tomorrow morning works for me."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Got it. They'll reach out to you tomorrow morning.")
            ],
            new EvaluatedScore(
                "Human",
                65,
                "Customer is interested in broadband plans but requests to speak with a real person, and arranges a specific time, indicating some purchase potential."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Direct Human Transfer Request",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can you transfer me to a human?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Certainly! Please hold while I connect you to a customer service agent."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks, I'll wait."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "You're now connected to an agent.")
            ],
            new EvaluatedScore(
                "Human",
                50,
                "Customer directly requests transfer to a human but doesn't specify reason or area of interest, making purchase intent unclear."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Billing Issue",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I have an issue with my bill. I need to speak to someone."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'll escalate this to our billing team. Can I have your account number so they can assist you?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "It's ********."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you. They'll reach out shortly.")
            ],
            new EvaluatedScore(
                "Human",
                60,
                "Existing customer has a billing issue requiring specialist handling and provides account information, indicating this is a request related to a specific service issue."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Bot Resistance",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I don't want to talk to a bot."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "No problem! A customer service representative will assist you. Can you provide your phone number for them to call?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "It's ********."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you. They'll call you soon.")
            ],
            new EvaluatedScore(
                "Human",
                45,
                "Customer explicitly refuses to communicate with a bot but is willing to provide a phone number for human contact, with unclear purchase intent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Manager Request",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'd like to talk to a manager."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Sure, I'll escalate this for you. May I know the issue so they can assist better?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "It's about my contract renewal."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Got it. A manager will call you soon to resolve this.")
            ],
            new EvaluatedScore(
                "Human",
                70,
                "Customer wishes to discuss contract renewal with management, indicating they are an existing customer with potential to continue using the service."),
            [DefaultLeadScoreConfig]);
    }

    private static IEnumerable<LeadScoreTestCase> GetHKBNHandoffTestCasesImplicitSignals()
    {
        // Hot Lead Test Cases
        yield return new LeadScoreTestCase(
            "Hot Lead - High-Speed Connection Interest",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I've been looking into broadband plans. Do you have any offers for high-speed connections?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, the 1 Gbps fiber broadband offer is still available! It comes with free installation and a complimentary Wi-Fi 6 router. Would you like to proceed with the subscription?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "That sounds interesting. What's the process?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Let me guide you through it. May I have your address and contact details?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, here's my info: [Details shared]. Can I also choose the installation date?")
            ],
            new EvaluatedScore(
                "High",
                90,
                "The user expressed clear interest in high-speed broadband plans, asked about the process, and confirmed their intent to proceed, indicating strong buying signals and readiness to convert."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Service Interest and Area Check",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I've heard good things about your service. How fast is your fastest plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our fastest plan is 1 Gbps fiber broadband. It includes free installation and a Wi-Fi 6 router. Shall we check availability for your area?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, please. What do you need from me?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'll need your address and contact details. Let's get started!"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Alright, here it is: [Details shared].")
            ],
            new EvaluatedScore(
                "High",
                85,
                "Customer shows high interest in fastest plan, engages with area check process, and readily shares contact details, indicating strong conversion potential."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Provider Switch with Add-ons",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hello, I'm considering switching providers. What's your top plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our top plan is 1 Gbps fiber broadband with great perks. Would you like to know more?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, what's included?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "It includes free installation and a Wi-Fi 6 router. Let me help you get started."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sounds good. I might also need some add-ons. Can we discuss that?")
            ],
            new EvaluatedScore(
                "High",
                82,
                "Customer explicitly mentions switching providers, shows interest in top plan details, and inquires about add-ons, showing strong intent to purchase."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Installation Timeline Inquiry",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I've been comparing broadband plans. Can you tell me about your best offer?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Of course! Our 1 Gbps fiber broadband is highly popular. It comes with complimentary installation. Interested?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sounds promising. How soon can I have it installed?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Let's check the schedule for you. Could you share your address?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, it's [Details shared]. Let me know the earliest slot.")
            ],
            new EvaluatedScore(
                "High",
                88,
                "Customer has been comparing plans, shows specific interest in installation timeline, and shares address details to expedite the process."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Hot Lead - Cost Inquiry with Address Sharing",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, your broadband speeds look impressive. What's the cost of your fastest plan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The 1 Gbps fiber broadband is $39/month with added perks. Would you like more details?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Yes, I'd like to know more."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Great! Let's begin with your location to confirm availability."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Alright, here's my address: [Details shared].")
            ],
            new EvaluatedScore(
                "High",
                80,
                "Customer shows specific interest in pricing for fastest plan and readily shares address information, indicating high conversion potential."),
            [DefaultLeadScoreConfig]);

        // Cold Lead Test Cases
        yield return new LeadScoreTestCase(
            "Cold Lead - Just Browsing",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Just browsing broadband options. What do you recommend?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our most affordable plan is the 100 Mbps broadband at $19/month. Would you like more details about this plan?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Maybe, I'm not sure yet."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "No problem! Let us know if you have questions. We're here to help."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Will do. Thanks.")
            ],
            new EvaluatedScore(
                "Low",
                32,
                "The user showed minimal engagement and no clear intent to purchase, expressing uncertainty and ending the conversation without further inquiries."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Lukewarm Lead - Provider Comparison",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm trying to decide between a few providers. What makes you different?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We offer competitive speeds and free installation with our plans. Can I help you explore a plan?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll need to think it over."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Take your time. Feel free to reach out if you'd like more details."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Alright, I'll do that.")
            ],
            new EvaluatedScore(
                "Medium",
                52,
                "The user showed some interest by asking about differentiators but did not engage further or express urgency, indicating a low likelihood to convert at this stage."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Cold Lead - Entry-Level Exploration",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What are your entry-level broadband options?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Our entry-level plan is 100 Mbps broadband at $19/month. Would you like to know about its features?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Not sure yet, just exploring."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's okay! Let me know if you have questions."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks, will do.")
            ],
            new EvaluatedScore(
                "Low",
                32,
                "The user is in the exploratory phase with no clear intent or urgency to purchase. They expressed uncertainty and did not engage further after receiving plan details."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Lukewarm Lead - Competitor Comparison",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How does your broadband compare to others in the area?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "We're known for reliability and speed. Our 1 Gbps plan is a top choice. Would you like to hear more?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'll need some time to compare options."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Understood! We're here when you're ready."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Appreciate it.")
            ],
            new EvaluatedScore(
                "Medium",
                45,
                "The user asked a general comparison question and acknowledged the information but showed no urgency or strong intent to proceed, indicating a low likelihood of immediate conversion."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Cold Lead - Website Research",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm just researching broadband for now. Do you have a website?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, you can find all our plans at [website link]. Would you like a quick overview?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Not right now, thanks."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "No problem! Let us know if you need assistance later."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, will do.")
            ],
            new EvaluatedScore(
                "Low",
                25,
                "The user expressed no immediate interest and is only in the research phase, with no specific questions or buying signals."),
            [DefaultLeadScoreConfig]);

        // Drop Off Test Cases
        yield return new LeadScoreTestCase(
            "Drop Off - Not Ready to Commit",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm not ready to commit to any broadband plans right now."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's perfectly fine. If you ever need assistance, feel free to contact us. Have a great day!"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks, bye.")
            ],
            new EvaluatedScore(
                "Drop Off",
                25,
                "The user expressed no immediate interest in committing to a broadband plan, showing low intent and no buying signals."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Price Objection",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Your plans are a bit expensive for me. I'll look elsewhere."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Understood. If you'd like to revisit us in the future, we're always here to help."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Okay, thank you.")
            ],
            new EvaluatedScore(
                "Drop Off",
                22,
                "The user expressed that the plans were too expensive and decided to look elsewhere, indicating low intent to convert."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Competitor Selection",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I've decided to go with another provider. Thanks for your help."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for considering us. We wish you the best with your new provider."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Appreciate it.")
            ],
            new EvaluatedScore(
                "Drop Off",
                15,
                "Customer has explicitly chosen another provider, indicating no possibility of conversion at this time."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - No Interest",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm not interested in broadband plans at the moment."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "No worries! If your needs change, don't hesitate to contact us. Have a good day."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks, bye.")
            ],
            new EvaluatedScore(
                "Drop Off",
                10,
                "The user explicitly stated they are not interested in broadband plans, indicating a lack of intent or engagement."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Drop Off - Happy with Current Provider",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm happy with my current provider, so I'm not looking to switch."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's great to hear. If you ever change your mind, we'll be here to assist you."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks.")
            ],
            new EvaluatedScore(
                "Drop Off",
                18,
                "Customer explicitly states satisfaction with current provider and no intention to switch, indicating zero conversion potential."),
            [DefaultLeadScoreConfig]);

        // Human Handoff Test Cases
        yield return new LeadScoreTestCase(
            "Human - Technical Questions",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I have a few technical questions about broadband plans. Who can I ask?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I'll connect you with one of our representatives right away. Please hold on for a moment."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Okay, thanks.")
            ],
            new EvaluatedScore(
                "Human",
                60,
                "Customer has specific technical questions requiring expert knowledge, showing genuine interest but needing human assistance."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Chatbot Limitation",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "This chatbot isn't answering my questions. Can I speak to someone?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Certainly! I'll transfer you to an agent now. Please hold on."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Alright, I'll wait.")
            ],
            new EvaluatedScore(
                "Human",
                50,
                "Customer expresses frustration with automated responses and requests human intervention, with unclear purchase intent."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Detailed Answers Request",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can you put me in touch with a person? I need detailed answers."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Of course! Let me connect you with a representative. Hold on, please."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thank you.")
            ],
            new EvaluatedScore(
                "Human",
                55,
                "Customer seeking detailed information through human contact, indicating genuine interest that requires personalized assistance."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Information Insufficient",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "I'm not finding the information I need. Can I talk to an agent?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Sure thing! I'll transfer you to an agent right now. One moment, please."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks.")
            ],
            new EvaluatedScore(
                "Human",
                52,
                "Customer unable to find needed information, requesting human assistance to continue their purchase journey."),
            [DefaultLeadScoreConfig]);

        yield return new LeadScoreTestCase(
            "Human - Setup Assistance",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can I talk to a human about setting up my broadband?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Absolutely! Let me connect you with one of our agents. Please wait a moment."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Thanks, I'll hold.")
            ],
            new EvaluatedScore(
                "Human",
                65,
                "Customer specifically interested in setup process and requesting human guidance, showing high intention toward purchase completion."),
            [DefaultLeadScoreConfig]);
    }


    private static IEnumerable<LeadScoreTestCase> GetSimpleTestCases()
    {
        for (int i = 0; i < 1; i++)
        {
            yield return new LeadScoreTestCase(
                $"EGL Tours - 西澳行程咨询",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Hi EGL tours high"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "感謝您提供家庭成員年齡資料。根據您的需要，EGL Tours現時提供多個適合10歲小朋友及60歲長者的日本家庭旅遊套票，熱門地點包括東京、大阪、京都、北海道及九州。這些地區設有主題公園、水族館、動物園及文化體驗等親子活動，並可安排自駕或包車服務，方便長者及小朋友出行。部分行程亦包含溫泉酒店，惟如對無障礙設施或住宿舒適度有特別要求，歡迎隨時告知，我們會為您個別查詢及安排。  如您有特定活動、飲食或行程節奏上的需要，請隨時提出。我們亦會為您查詢六月期間的最新優惠，並度身訂造最合適的行程建議及報價。期待您的回覆，讓我們為您安排一個舒適愉快的家庭日本之旅。"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Thank you for reaching out to EGL Tours. I am EGL Tours AI, here to assist you with your travel plans. We offer a wide range of services including online tour bookings, airline tickets, hotel reservations, cruises, and more, all designed to provide you with a seamless travel experience. May I know if you have a specific destination or type of travel you are interested in? I would be happy to provide you with tailored information and options."),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "我想帶小朋友summer 去long haul trip, any recommendation?"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "感謝您的查詢！EGL Tours專為家庭設計多元長途旅遊方案，包括歐洲、澳洲及美國等熱門地點。我們特別推薦澳洲作為親子旅遊熱點，結合自駕遊及多項適合小朋友的活動，讓家長和小朋友都能享受靈活舒適的假期。此外，我們亦提供歐洲及北美自駕遊、親子探索計劃，以及機票、酒店和私人接送等一站式服務，方便您按家庭需要靈活安排行程。  如您有特定預算、出發日期或心儀活動，歡迎告知，我們可為您度身訂造行程並查詢最新暑假優惠。期待您的回覆，讓我們協助您規劃一個難忘的家庭暑假長途之旅。"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "澳洲你有咩選擇？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "感謝您的查詢！EGL Tours專為家庭旅客提供多元澳洲自由行及度身訂造服務，包括機票、酒店、主題公園門票及包車/租車等一站式安排。您可靈活組合黃金海岸主題公園、大堡礁自然探索、悉尼城市及墨爾本自駕文化體驗，讓小朋友盡情享受親子活動。  如您有預計出發日期、預算範圍或特別想參加的活動，歡迎告知，我們可根據您的需要度身設計行程及報價，確保旅程舒適愉快。期待您的回覆，讓我們為您安排理想的暑假澳洲家庭之旅。"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "如果想去西澳有咩行程推介？")
                ],
                new EvaluatedScore(
                    "Medium",
                    75,
                    "客户展示明确的旅游需求，从初步咨询到具体询问澳洲西部行程，并提及带小朋友出行的需求，表现出较高的购买意向。"),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 100; i++)
        {
            yield return new LeadScoreTestCase(
                $"EGL Tours - 日本繡球花行程咨詢",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Hi EGL tours highest"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Thank you for reaching out to EGL Tours. I noticed you mentioned \"highest\" in your message—if you are looking for our top-rated tours, premium travel experiences, or have a specific request in mind, I would be glad to assist.  At EGL Tours, we provide a full spectrum of travel services, including *organized tours, customizable free independent travel (FIT) packages, airline tickets, hotel bookings, and cruises*, with a strong focus on destinations such as *Japan, Southeast Asia, and more*.  Could you please share a bit more about what you are looking for, or clarify your interests? I am here to help you plan a travel experience that meets your expectations.  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I want to bring my family to Japan in June, any recommendation"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Thank you for your interest in a family trip to Japan this June. While we do not have a fixed family package for that period, EGL Tours offers *flexible travel solutions* that can be tailored to your needs. Our free independent travel (FIT) packages to destinations such as Osaka and Kyoto include flights and hotel stays, and we provide car rental and transfer services for added comfort and convenience—especially helpful for families.  June in Japan features beautiful hydrangea blooms and local festivals, and we can help you plan an itinerary that makes the most of these seasonal highlights. To ensure we recommend the most suitable options, could you share more about your family's interests, preferred destinations, and any specific requirements (such as room types or transportation needs)? We are committed to creating a travel experience that is both enjoyable and comfortable for your family.  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "去睇繡球花好似幾好喎，老人家坐輪椅會唔會唔方便？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "感謝您的查詢及關心長者出行的便利性。雖然現時未能即時提供日本賞繡球花地點的詳細無障礙設施資訊，但EGL Tours在東京、大阪、京都等主要城市均有*點對點接送及租車服務*，非常適合輪椅使用者及長者家庭。我們亦有豐富經驗為有特殊需要的旅客安排合適的交通及住宿，確保旅程舒適安全。  如您有心儀的賞花地點，歡迎告知，我們可協助查詢該地點的無障礙設施，並為您度身訂造行程。如需協助選擇適合的賞花地點或有其他特別需要，請隨時告訴我們，EGL Tours樂意為您的家庭提供貼心支援。  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "咁有咩地方方便輪椅又可以有花海睇？")
                ],
                new EvaluatedScore(
                    "Medium",
                    60,
                    "客户展示明确的旅游需求，从初步咨询到具体询问适合轮椅的赏花地点，并提及家庭出行需求，表现出较高的购买意向。"),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 1; i++)
        {
            yield return new LeadScoreTestCase(
                $"OT&P - 流感疫苗咨询",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Hi OT&P highest"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Hi there! I noticed you said \"Hi OT&P highest\"—thanks for reaching out! 😊 Could you share a bit more about what you're looking for, or if there's something specific you had in mind? At OT&P, we offer a wide range of healthcare services with a personal touch, and I'm here to help with any questions or needs you might have.  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "我都唔知你講乜"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "唔緊要呀，唔明都好正常，我哋可以慢慢傾！😊 OT&P係香港做咗超過30年嘅醫療機構，有7間診所，團隊有好多經驗豐富嘅醫生同護士，幫你同家人睇病、做身體檢查、打疫苗、睇專科、照顧身心健康都得。你可以網上預約，診所之間資料都連接晒，仲有保險直付，方便又慳時間。你有咩想知或者需要幫手，隨時同我講，我哋好樂意幫你！  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "我屋企有個小朋友想打流感針，你哋有冇呢個Service？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "多謝你查詢！我哋OT&P有提供小朋友流感針接種服務，適合6個月以上嘅小朋友，仲有注射同噴鼻式疫苗選擇。每次接種前，醫生都會幫小朋友做簡單健康檢查，確保安全同舒適。你可以用WhatsApp或者網上表格預約，診所遍佈港九新界，仲有保險直付，方便又慳時間。如果你想，我可以幫你查下最近嘅預約時間，或者有咩疑問都可以隨時問我呀！  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "我個小朋友九歲你會建議注射定噴鼻？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "多謝你信任OT&P！你9歲小朋友可以選擇注射式或噴鼻式流感疫苗。噴鼻式係無痛選擇，特別適合怕針嘅小朋友；注射式就適合大部分兒童，效果都好好。不過，最重要係我哋醫生會先幫小朋友做個健康評估，了解身體狀況同過敏史，再同你一齊揀最合適嘅疫苗，確保安全同安心。  你可以預約我哋嘅「兒童流感疫苗及健康檢查套餐」，到時有咩疑問都可以直接問醫生。如果你想查詢最近預約時間，或者想了解多啲疫苗資料，隨時話我知，我好樂意幫你安排！  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "兩款嘅效果一樣？")
                ],
                new EvaluatedScore(
                    "Medium",
                    75,
                    "客户展示明确的医疗需求，从初步咨询到具体询问流感疫苗类型，并提及为9岁小孩接种的需求，表现出较高的购买意向。"),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 1; i++)
        {
            yield return new LeadScoreTestCase(
                $"Weirdo Beauty - 美白咨询",
                [
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Hey there! 👋 Thanks for saying hi to Weirdo Beauty! We're all about *personalized skincare*—using medical-grade tech and even AI skin analysis to find what's best for you. You can try a treatment first, no pressure to commit! Our app also gives you points and special perks. 😊 Is there something about your skin you're curious about, or a goal you have in mind? I'd love to help you find the perfect fit—just let me know what you're looking for!  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "我近排想美白有咩推介？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "你好呀！😊 其實我哋有好多針對膚色暗沉、色斑同膚質不均嘅療程，例如「Flawless Skin Solution」同埋專門針對黑眼圈美白嘅Teosyal R1注射。最特別係我哋會用AI皮膚分析，幫你了解自己膚質，再由專人幫你度身訂造最啱你嘅美白方案～✨  而家新客仲有免費皮膚分析同首次體驗優惠，完全無壓力！如果你想知多啲，或者有咩特別想改善，隨時同我講啦～我可以幫你安排分析或試做，等你親身感受下效果！💬  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Flawless skin solution即係做乜？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "多謝你問得咁仔細呀！🙌 「Flawless Skin Solution」其實係我哋Weirdo Beauty嘅招牌療程，專為改善膚色暗沉、色斑同膚質不均而設。不過，呢個療程唔係一式一樣，每位客人都會先用美國醫學級VISIA皮膚分析儀做個全面檢查，然後由治療師根據你嘅膚質同目標（例如美白、均勻膚色）度身訂造最啱你嘅方案～✨  你可以先試做一次，午市仲有$980優惠，完全無壓力！到時治療師會同你詳細解釋療程內容、預期效果同注意事項，咩都可以問晒佢哋！如果你想預約或者有其他問題，隨時話我知啦～😊  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "嘩，咁我咪唔知自己買緊咩treatment"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "有冇效㗎？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "你好呀！你問得好啱，唔知自己買緊咩真係會唔安心～我哋「Flawless Skin Solution」其實係針對膚色暗沉、色斑、乾燥等問題，會先用AI皮膚分析同專業治療師幫你度身訂造方案，唔會一式一樣亂賣療程俾你！✨  療程會用MagicPeel、50%杏仁酸、光學療法等技術，針對性改善膚質，部分客人每次做完都話膚色均勻咗、暗啞減淡咗～雖然我哋未有公開臨床數據，但你可以先試做一次（午市$980），親身感受下效果，完全無壓力！有咩唔明或者想知多啲，隨時問我啦～我好樂意幫你解答！😊  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "你好呀！你問得好啱，其實「Flawless Skin Solution」係我哋Weirdo Beauty嘅皇牌療程，最大特色就係*度身訂造*，唔係一式一樣㗎！你可以先用$980午市優惠試做，治療師會用AI皮膚分析同你傾清楚，再根據你嘅膚質同目標設計最啱你嘅方案～✨  好多客人做完都話膚色均勻咗、暗沉有改善，但最重要係你可以親身體驗，無壓力先感受下效果！如果你仲有疑問，或者想知其他療程（例如Bubble Peel、Hydrafacial等），都可以問我呀～我哋真係好著重透明同專業，等你安心選擇！😊  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "塊面做完之後會唔會好乾？")
                ],
                new EvaluatedScore(
                    "Medium",
                    60,
                    "客户展示明确的美容需求，从初步咨询到详细询问特定疗程的效果和后续反应，表现出较高的购买意向。"),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 1; i++)
        {
            yield return new LeadScoreTestCase(
                $"Prudential - 旅游保险咨询",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Hi prudential high"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Thank you for your message and for reaching out to Prudential. We have proudly served Hong Kong since 1964, offering a comprehensive range of financial planning and insurance solutions, including life insurance, investment-linked products, retirement planning, health and medical protection, general insurance, and employee benefits. In 2023, we expanded our services to Macau, further supporting the Greater Bay Area community.\n\nMay I ask if there is a particular area you are interested in, or any questions you have in mind? I am here to provide any information or guidance you may need.\n\n(Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "我想买旅游保险")
                ],
                new EvaluatedScore(
                    "Medium",
                    60,
                    "用户表现出明确的旅游需求，从初步咨询到具体指定日本短途团，并提供了家庭成员包含老人的关键信息，显示出较高的购买意向。"),
                [NewLeadScoreConfig]);
        }

        for (int i = 0; i < 1; i++)
        {
            yield return new LeadScoreTestCase(
                $"EGL Tours - 明确购买意图",
                [
                    new ChatMessageContent(
                        AuthorRole.User,
                        "Hi EGL tours highest"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Thank you for reaching out to EGL Tours. I noticed you mentioned \"highest\" in your message—if you are looking for our top-rated tours, premium travel experiences, or have a specific request in mind, I would be glad to assist.  At EGL Tours, we provide a full spectrum of travel services, including *organized tours, customizable free independent travel (FIT) packages, airline tickets, hotel bookings, and cruises*, with a strong focus on destinations such as *Japan, Southeast Asia, and more*.  Could you please share a bit more about what you are looking for, or clarify your interests? I am here to help you plan a travel experience that meets your expectations.  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "I want to bring my family to Japan in June, any recommendation"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Thank you for your interest in a family trip to Japan this June. While we do not have a fixed family package for that period, EGL Tours offers *flexible travel solutions* that can be tailored to your needs. Our free independent travel (FIT) packages to destinations such as Osaka and Kyoto include flights and hotel stays, and we provide car rental and transfer services for added comfort and convenience—especially helpful for families.  June in Japan features beautiful hydrangea blooms and local festivals, and we can help you plan an itinerary that makes the most of these seasonal highlights. To ensure we recommend the most suitable options, could you share more about your family's interests, preferred destinations, and any specific requirements (such as room types or transportation needs)? We are committed to creating a travel experience that is both enjoyable and comfortable for your family.  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "去睇繡球花好似幾好喎，老人家坐輪椅會唔會唔方便？"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "感謝您的查詢及關心長者出行的便利性。雖然現時未能即時提供日本賞繡球花地點的詳細無障礙設施資訊，但EGL Tours在東京、大阪、京都等主要城市均有*點對點接送及租車服務*，非常適合輪椅使用者及長者家庭。我們亦有豐富經驗為有特殊需要的旅客安排合適的交通及住宿，確保旅程舒適安全。  如您有心儀的賞花地點，歡迎告知，我們可協助查詢該地點的無障礙設施，並為您度身訂造行程。如需協助選擇適合的賞花地點或有其他特別需要，請隨時告訴我們，EGL Tours樂意為您的家庭提供貼心支援。  (Highest Quality Response Bot)"),
                    new ChatMessageContent(
                        AuthorRole.User,
                        "我想购买这趟旅程，如何付款")
                ],
                new EvaluatedScore(
                    "High",
                    90,
                    "客户展示明确的旅游需求，从初步咨询到具体询问适合轮椅的赏花地点，并提及家庭出行需求，表现出较高的购买意向。"),
                [NewLeadScoreConfig]);
        }
    }

}