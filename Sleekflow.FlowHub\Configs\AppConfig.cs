using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Configs;

public interface IAppConfig
{
    string CoreInternalsEndpoint { get; }

    string CoreInternalsKey { get; }
}

public class AppConfig : IAppConfig, IConfig
{
    public string CoreInternalsEndpoint { get; }

    public string CoreInternalsKey { get; }

    public AppConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        CoreInternalsEndpoint =
            Environment.GetEnvironmentVariable("CORE_INTERNALS_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("CORE_INTERNALS_ENDPOINT");
        CoreInternalsKey =
            Environment.GetEnvironmentVariable("CORE_INTERNALS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("CORE_INTERNALS_KEY");
    }
}