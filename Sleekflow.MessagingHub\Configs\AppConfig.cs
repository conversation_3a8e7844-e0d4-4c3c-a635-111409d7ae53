using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.MessagingHub.Configs;

public interface IAppConfig
{
    bool IsProduction { get; }
}

public class AppConfig : IConfig, IAppConfig
{
    public bool IsProduction { get; private set; }

    public AppConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        IsProduction = (Environment.GetEnvironmentVariable("SF_ENV_NAME", target) ??
                        throw new SfMissingEnvironmentVariableException("SF_ENV_NAME")) == "production";
    }
}