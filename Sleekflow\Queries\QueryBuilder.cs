﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Queries;

public static class QueryBuilder
{
    public class FilterGroup
    {
        [Required]
        [JsonProperty("filters")]
        public List<IFilter> Filters { get; set; }

        [JsonConstructor]
        public FilterGroup(
            List<IFilter> filters)
        {
            Filters = filters;
        }
    }

    public interface IFilter
    {
        public string FieldName { get; set; }

        public string Operator { get; set; }

        public object? Value { get; set; }
    }

    public class Filter : IFilter
    {
        [Required]
        [RegularExpression("^[a-zA-Z0-9_]+$")]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [RegularExpression("^(=|>|<|>=|<=|!=|contains|array_contains|startswith|in)$")]
        [JsonProperty("operator")]
        public string Operator { get; set; }

        [JsonProperty("value")]
        public object? Value { get; set; }

        [JsonConstructor]
        public Filter(string fieldName, string @operator, object? value)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
        }
    }

    public class Sort
    {
        [Required]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [JsonProperty("direction")]
        public string Direction { get; set; }

        [Required]
        [JsonProperty("is_case_sensitive")]
        public bool IsCaseSensitive { get; set; }

        [JsonConstructor]
        public Sort(string fieldName, string direction, bool isCaseSensitive)
        {
            FieldName = fieldName;
            Direction = direction;
            IsCaseSensitive = isCaseSensitive;
        }
    }

    private sealed class Param
    {
        public string Name { get; set; }

        public object? Value { get; set; }

        public Param(string name, object? value)
        {
            Name = name;
            Value = value;
        }
    }

    public interface ISelect
    {
        public string FieldName { get; set; }

        public string? As { get; set; }
    }

    public class Select : ISelect
    {
        [Required]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonProperty("as")]
        public string? As { get; set; }

        [JsonConstructor]
        public Select(string fieldName, string? @as)
        {
            FieldName = fieldName;
            As = @as;
        }
    }

    public class PlainSelect : ISelect
    {
        public string FieldName { get; set; }

        public string? As { get; set; }

        public PlainSelect(string fieldName, string? @as)
        {
            FieldName = fieldName;
            As = @as;
        }
    }

    public class GroupBy
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonConstructor]
        public GroupBy(string fieldName)
        {
            FieldName = fieldName;
        }
    }

    public static QueryDefinition BuildQueryDef(
        List<ISelect> selects,
        List<FilterGroup> filterGroups,
        List<Sort> sorts)
    {
        return BuildQueryDef(selects, filterGroups, sorts, new List<GroupBy>(), string.Empty);
    }

    public static QueryDefinition BuildQueryDef(
        List<ISelect> selects,
        List<FilterGroup> filterGroups,
        List<Sort> sorts,
        List<GroupBy> groupBys,
        string sleekflowCompanyId)
    {
        var selectExpressions = new List<string>()
            {
                Capacity = selects.Count + groupBys.Count
            }
            .Concat(
                selects.Select(
                    f => f is PlainSelect ? $"{f.FieldName} {f.As}" : $"m[\"{f.FieldName}\"] {f.As}"))
            .Concat(
                groupBys.Select(
                    f => $"m[\"{f.FieldName}\"] as \"{f.FieldName}\""))
            .ToList();

        var selectClause = selectExpressions.Any()
            ? "SELECT "
              + string.Join(", ", selectExpressions)
            : "SELECT *";

        filterGroups = string.IsNullOrEmpty(sleekflowCompanyId)
            ? filterGroups
            : filterGroups.Concat(
                new List<FilterGroup>
                {
                    new (
                        new List<IFilter>
                        {
                            new Filter(
                                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                                "=",
                                sleekflowCompanyId)
                        })
                }).ToList();

        var (@params, whereClause) = GetWhereClause(filterGroups);

        var sortClause = sorts.Count == 0
            ? string.Empty
            : "ORDER BY "
              + string.Join(
                  ", ",
                  sorts.Select(
                      f =>
                      {
                          if (f.IsCaseSensitive)
                          {
                              return $"m[\"{f.FieldName}\"] {f.Direction}";
                          }

                          return $"m[\"{f.FieldName}\"] {f.Direction}";
                      }));

        var groupByClause = groupBys.Count == 0
            ? string.Empty
            : "GROUP BY "
              + string.Join(
                  ", ",
                  groupBys.Select(
                      f => $"m[\"{f.FieldName}\"]"));

        var clauses = new List<string>
            {
                selectClause,
                $"FROM %%CONTAINER_NAME%% m",
                whereClause,
                sortClause,
                groupByClause
            }
            .Where(l => !string.IsNullOrWhiteSpace(l))
            .ToList();

        var queryDefinition = @params
            .Aggregate(
                new QueryDefinition(string.Join("\n", clauses)),
                (qd, param) =>
                {
                    if (param.Value is JArray jArray)
                    {
                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<string>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<long>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<double>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        throw new SfValidationException(
                            new List<ValidationResult>()
                            {
                                new ValidationResult(
                                    "Invalid parameter value",
                                    new[]
                                    {
                                        param.Name
                                    })
                            });
                    }

                    return qd.WithParameter(param.Name, param.Value);
                });

        return queryDefinition;
    }

    private static (List<Param> Params, string Where) GetWhereClause(IReadOnlyCollection<FilterGroup> filterGroups)
    {
        var @params = new List<Param>();

        if (filterGroups.Count <= 0)
        {
            return (@params, string.Empty);
        }

        var stringBuilder = new StringBuilder();
        var i = 0;

        stringBuilder.Append("WHERE ");
        stringBuilder.AppendJoin(
            " AND ",
            filterGroups
                .Where(fg => fg.Filters.Any())
                .Select(
                    fg =>
                    {
                        var filterClauseStrs = fg.Filters.Select(
                            f =>
                            {
                                return f.Operator switch
                                {
                                    "contains" =>
                                        $"CONTAINS(m{GetNestedFieldName(f.FieldName)}, {GetSanitizedParamName(f.FieldName, i++)}, true)",
                                    "array_contains" =>
                                        $"ARRAY_CONTAINS(m{GetNestedFieldName(f.FieldName)}, {GetSanitizedParamName(f.FieldName, i++)}, false)",
                                    "startswith" =>
                                        $"STARTSWITH(m{GetNestedFieldName(f.FieldName)}, {GetSanitizedParamName(f.FieldName, i++)}, false)",
                                    "in" =>
                                        $"ARRAY_CONTAINS({GetSanitizedParamName(f.FieldName, i++)}, m{GetNestedFieldName(f.FieldName)}, false)",
                                    _ =>
                                        $"m{GetNestedFieldName(f.FieldName)} {f.Operator} {GetSanitizedParamName(f.FieldName, i++)}"
                                };
                            });

                        var sb = new StringBuilder();
                        sb.Append('(');
                        sb.AppendJoin(" OR ", filterClauseStrs);
                        sb.Append(')');

                        return sb.ToString();
                    }));

        i = 0;

        @params.AddRange(
            filterGroups
                .SelectMany(fg => fg.Filters)
                .Select(f => new Param(GetSanitizedParamName(f.FieldName, i++), f.Value)));

        return (@params, stringBuilder.ToString());
    }

    private static string GetSanitizedParamName(string name, int index)
    {
        return "@"
               + GetSanitizedName(name)
               + "_" + index;
    }


    private static string GetSanitizedName(string name)
    {
        return name
            .Replace("[", "_")
            .Replace("]", "_")
            .Replace(".", "_")
            .Replace(":", "_");
    }


    private static string GetNestedFieldName(string field)
    {
        var result = string.Empty;

        var fieldStrings = field.Split('.');
        foreach (var fieldString in fieldStrings)
        {
            result += $"[\"{GetSanitizedName(fieldString)}\"]";
        }

        return result;
    }
}