﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateSalesforceObjectStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-salesforce-object";

    [Required]
    [JsonProperty("salesforce_connection_id__expr")]
    public string SalesforceConnectionIdExpr { get; set; }

    [Required]
    [JsonProperty("object_type__expr")]
    public string ObjectTypeExpr { get; set; }

    [Required]
    [JsonProperty("is_custom_object__expr")]
    public string IsCustomObjectExpr { get; set; }

    [Required]
    [JsonProperty("is_set_owner_by_user_mapping_config__expr")]
    public string IsSetOwnerByUserMappingConfigExpr { get; set; }

    [JsonProperty("sleekflow_user_id_for_mapping__expr")]
    public string? SleekflowUserIdForMappingExpr { get; set; }

    [Required]
    [JsonProperty("object_properties__expr_dict")]
    public Dictionary<string, string?> ObjectPropertiesExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.SalesforceIntegration;

    [JsonConstructor]
    public CreateSalesforceObjectStepArgs(
        string salesforceConnectionIdExpr,
        string objectTypeExpr,
        string isCustomObjectExpr,
        string isSetOwnerByUserMappingConfigExpr,
        string? sleekflowUserIdForMappingExpr,
        Dictionary<string, string?> objectPropertiesExprDict)
    {
        SalesforceConnectionIdExpr = salesforceConnectionIdExpr;
        ObjectTypeExpr = objectTypeExpr;
        IsCustomObjectExpr = isCustomObjectExpr;
        IsSetOwnerByUserMappingConfigExpr = isSetOwnerByUserMappingConfigExpr;
        SleekflowUserIdForMappingExpr = sleekflowUserIdForMappingExpr;
        ObjectPropertiesExprDict = objectPropertiesExprDict;
    }
}