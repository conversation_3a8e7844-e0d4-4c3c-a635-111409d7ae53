using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Events;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyAgentConfigs;

[TriggerGroup(
    ControllerNames.CompanyAgentConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class DeleteCompanyAgentConfig
    : ITrigger<DeleteCompanyAgentConfig.DeleteCompanyAgentConfigInput,
        DeleteCompanyAgentConfig.DeleteCompanyAgentConfigOutput>
{
    private readonly ILogger _logger;
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IBus _bus;

    public DeleteCompanyAgentConfig(
        ILogger<DeleteCompanyAgentConfig> logger,
        ISleekflowAuthorizationContext authorizationContext,
        ICompanyAgentConfigService companyAgentConfigService,
        IBus bus)
    {
        _logger = logger;
        _authorizationContext = authorizationContext;
        _companyAgentConfigService = companyAgentConfigService;
        _bus = bus;
    }

    public class DeleteCompanyAgentConfigInput : IHasETag
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasETag.PropertyNameETag)]
        public string? ETag { get; set; }

        [JsonConstructor]
        public DeleteCompanyAgentConfigInput(string id, string? eTag)
        {
            Id = id;
            ETag = eTag;
        }
    }

    public class DeleteCompanyAgentConfigOutput
    {
    }

    public async Task<DeleteCompanyAgentConfigOutput> F(DeleteCompanyAgentConfigInput input)
    {
        _logger.LogInformation(
            "Deleting companyAgentConfig with id {Id} {CompanyId} {StaffId} {TeamIds}",
            input.Id,
            _authorizationContext.SleekflowCompanyId,
            _authorizationContext.SleekflowStaffId,
            _authorizationContext.SleekflowTeamIds);

        // Delete the agent config first
        await _companyAgentConfigService.DeleteAsync(input.Id, _authorizationContext.SleekflowCompanyId!, input.ETag!);

        await _bus.Publish(new AgentDeletedEvent(_authorizationContext.SleekflowCompanyId!, input.Id));

        return new DeleteCompanyAgentConfigOutput();
    }
}