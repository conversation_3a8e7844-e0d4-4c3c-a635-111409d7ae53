﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;

public class IntelligentHubUsageFilter : IValidatableObject
{
    public const string ModelNameIntelligentHubUsageFilter = "intelligent_hub_usage_filter";

    public const string PropertyNameFromDateTime = "from_date_time";
    public const string PropertyNameToDateTime = "to_date_time";

    [JsonProperty(PropertyNameFromDateTime)]
    public DateTimeOffset? FromDateTime { get; set; }

    [JsonProperty(PropertyNameToDateTime)]
    public DateTimeOffset? ToDateTime { get; set; }

    [JsonConstructor]
    public IntelligentHubUsageFilter(
        DateTimeOffset? fromDateTime,
        DateTimeOffset? toDateTime)
    {
        FromDateTime = fromDateTime;
        ToDateTime = toDateTime;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (FromDateTime.HasValue && !ToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both FromDateTime and ToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(FromDateTime)
                });
        }

        if (!FromDateTime.HasValue && ToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both FromDateTime and ToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(ToDateTime)
                });
        }

        if (FromDateTime.HasValue && ToDateTime.HasValue)
        {
            if (FromDateTime.Value > ToDateTime.Value)
            {
                yield return new ValidationResult(
                    "FromDateTime can't be greater than ToDateTime",
                    new[]
                    {
                        nameof(FromDateTime),
                        nameof(ToDateTime)
                    });
            }
        }
    }
}