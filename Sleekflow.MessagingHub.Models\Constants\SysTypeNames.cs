namespace Sleekflow.MessagingHub.Models.Constants;

public static class SysTypeNames
{
    public const string Waba = "Waba";
    public const string WabaPhoneNumber = "WabaPhoneNumber";
    public const string WabaProductCatalog = "WabaProductCatalog";
    public const string BusinessBalance = "BusinessBalance";
    public const string WabaBalance = "WabaBalance";
    public const string BusinessBalanceTransactionLog = "BusinessBalanceTransactionLog";

    public const string Message = "Message";

    public const string AuditLog = "MessagingHubAuditLog";
    public const string WabaAudit = "waba_audit";
    public const string BusinessBalanceAudit = "business_balance_audit";
    public const string BusinessBalanceTransactionLogAudit = "business_balance_transaction_log_audit";
    public const string OperationAudit = "operation_audit";
    public const string CloudApiWebhookStatusUpdateAudit = "cloud_api_webhook_status_update_audit";

    public const string Webhook = "Webhook";
    public const string SendWhatsappCloudApiMessage = "send_whatapp_cloud_api_message";

    public const string Blob = "Blob";
    public const string BusinessBalanceAutoTopUpProfile = "business_balance_auto_top_up_profile";
    public const string WabaBalanceAutoTopUpProfile = "Waba_Balance_Auto_Top_Up_Profile";

    public const string BusinessWabaLevelCreditManagementSwitchBySystem = "business_waba_level_credit_management_switch_by_system";
    public const string BusinessWabaLevelCreditManagementSwitchByUser = "business_waba_level_credit_management_switch_by_user";
    public const string BusinessWabaLevelCreditTransfer = "business_waba_level_credit_transfer";

    public const string PhoneNumberStatusChanged = "phone_number_status_changed";
}