using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Models.Internals;

public class CheckWorkflowContactEnrolmentConditionV2Input : CheckWorkflowContactEnrolmentConditionInput
{
    [JsonConstructor]
    public CheckWorkflowContactEnrolmentConditionV2Input(
        OnDateAndTimeArrivedCommonEventBody eventBody,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string contactId,
        ContactDetail contactDetail,
        string? condition,
        string? workflowName,
        string origin)
        : base(
            workflowId,
            workflowVersionedId,
            sleekflowCompanyId,
            contactId,
            contactDetail,
            condition,
            workflowName)
    {
        EventBody = eventBody;
        Origin = origin;
    }
    [Required]
    [ValidateObject]
    [JsonProperty("event_body")]
    public OnDateAndTimeArrivedCommonEventBody EventBody { get; }

    [JsonProperty("flow_hub_config")]
    [Required]
    [ValidateObject]
    public string Origin { get; }

}