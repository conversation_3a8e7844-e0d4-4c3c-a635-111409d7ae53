﻿using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;

public class CreditTransferFromTo
{
    [JsonProperty("credit_transfer_from")]
    public CreditTransferTargetObject CreditTransferFrom { get; set; }

    [JsonProperty("credit_transfer_to")]
    public CreditTransferTargetObject CreditTransferTo { get; set; }

    [JsonProperty("credit_transfer_amount")]
    public Money CreditTransferAmount { get; set; }

    [JsonProperty("credit_transfer_type")]
    public string CreditTransferType { get; set; }

    [JsonConstructor]
    public CreditTransferFromTo(CreditTransferTargetObject creditTransferFrom, CreditTransferTargetObject creditTransferTo, Money creditTransferAmount, string creditTransferType)
    {
        CreditTransferFrom = creditTransferFrom;
        CreditTransferTo = creditTransferTo;
        CreditTransferAmount = creditTransferAmount;
        CreditTransferType = creditTransferType;
    }
}