﻿using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Polly;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.FlowHub.Workers.Triggers.Activities;
using Sleekflow.FlowHub.Workers.Triggers.Orchestrators;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.FlowHub.Workers.Services;

public interface IContactEligibilityService
{
    Task<int> ProcessAndEnrollEligibleContactsAsync(
        Dictionary<string, ContactDetail> contacts,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string condition,
        string workflowName,
        IContactEnrollmentService enrollmentService);

    Task<bool> CheckContactEligibilityAsync(
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string contactId,
        ContactDetail contactDetail,
        string condition,
        string workflowName);

    Task<int> CheckTimeAndEligibilityThenEnrollAsync(
        ContactDatetime[] contactDateTimeArray,
        Dictionary<string, ContactDetail> contacts,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string condition,
        string workflowName,
        IContactEnrollmentService enrollmentService,
        BaseDateTimeSettings baseDateTimeSettings,
        WorkflowRecurringSettings workflowRecurringSettings,
        DateTimeOffset dateTimeOffset,
        string origin);

    Task<int> ProcessAndEnrollEligibleContactsAsyncV2(
        Dictionary<string, ContactDetail> contacts,
        string inputWorkflowId,
        string inputWorkflowVersionedId,
        string inputSleekflowCompanyId,
        string inputCondition,
        string inputWorkflowName,
        IContactEnrollmentService contactEnrollmentService,
        string inputOrigin);
}

public class ContactEligibilityService : IContactEligibilityService
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;
    private readonly ILogger<ContactEligibilityService> _logger;
    private readonly IAsyncPolicy<HttpResponseMessage> _retryPolicy;
    private readonly DateTimePropertySettingsParser _dateTimePropertySettingsParser;


    public ContactEligibilityService(
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        ILogger<ContactEligibilityService> logger,
        IAsyncPolicy<HttpResponseMessage> retryPolicy,
        DateTimePropertySettingsParser dateTimePropertySettingsParser)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _logger = logger;
        _retryPolicy = retryPolicy;
        _dateTimePropertySettingsParser = dateTimePropertySettingsParser;
    }

    public async Task<int> ProcessAndEnrollEligibleContactsAsync(
        Dictionary<string, ContactDetail> contacts,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string condition,
        string workflowName,
        IContactEnrollmentService enrollmentService)
    {
        var contactsEnrolled = 0;

        foreach (var contactEntry in contacts)
        {
            var contactId = contactEntry.Key;
            var contactDetail = contactEntry.Value;

            bool isEligible = await CheckContactEligibilityAsync(
                workflowId,
                workflowVersionedId,
                sleekflowCompanyId,
                contactId,
                contactDetail,
                condition,
                workflowName);

            if (isEligible)
            {
                try
                {
                    await enrollmentService.PublishContactEnrollmentEventAsync(
                        contactId,
                        contactDetail,
                        workflowId,
                        workflowVersionedId,
                        sleekflowCompanyId);

                    contactsEnrolled++;

                    _logger.LogInformation(
                        "Contact {ContactId} enrolled for workflow {WorkflowVersionedId}",
                        contactId,
                        workflowVersionedId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error publishing enrollment for contact {ContactId}, workflow {WorkflowVersionedId}",
                        contactId,
                        workflowVersionedId);
                    // Continuing to next contact despite publish error
                }
            }
        }

        return contactsEnrolled;
    }

    public async Task<bool> CheckContactEligibilityAsync(
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string contactId,
        ContactDetail contactDetail,
        string condition,
        string workflowName)
    {
        var checkConditionInput = new CheckWorkflowContactEnrolmentConditionInput(
            workflowId,
            workflowVersionedId,
            sleekflowCompanyId,
            contactId,
            contactDetail,
            condition,
            workflowName);

        var checkConditionInputJsonStr = JsonConvert.SerializeObject(
            checkConditionInput,
            JsonConfig.DefaultJsonSerializerSettings);

        try
        {
            var pollyContext = new Context();
            pollyContext["logger"] = _logger;

            var checkResMsg = await _retryPolicy.ExecuteAsync(
                async (context) =>
                {
                    var reqMsg = new HttpRequestMessage
                    {
                        Method = HttpMethod.Post,
                        Content = new StringContent(
                            checkConditionInputJsonStr,
                            Encoding.UTF8,
                            "application/json"),
                        RequestUri = new Uri(
                            _appConfig.FlowHubInternalsEndpoint + "/CheckWorkflowContactEnrolmentCondition"),
                        Headers =
                        {
                            {
                                "X-Sleekflow-Key", _appConfig.InternalsKey
                            }
                        }
                    };

                    return await _httpClient.SendAsync(reqMsg);
                },
                pollyContext);

            var checkResStr = await checkResMsg.Content.ReadAsStringAsync();
            _logger.LogInformation("checkstr: {CheckStr}", checkResStr);
            var output = checkResStr.ToObject<Output<dynamic>>();

            if (output == null || output.Success == false)
            {
                _logger.LogError(
                    "Eligibility check failed for contact {ContactId} for workflow {WorkflowVersionedId}. Error: {Error}",
                    contactId,
                    workflowVersionedId,
                    JsonConvert.SerializeObject(output));

                return false;
            }

            var checkOutput = ((JObject) output.Data).ToObject<CheckWorkflowContactEnrolmentConditionOutput>()!;
            return checkOutput.EnrolmentConditionSatisfied;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error during eligibility check for contact {ContactId}, workflow {WorkflowVersionedId}",
                contactId,
                workflowVersionedId);

            return false;
        }
    }

    public async Task<int> CheckTimeAndEligibilityThenEnrollAsync(
        ContactDatetime[] contactDateTimeArray,
        Dictionary<string, ContactDetail> contacts,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string condition,
        string workflowName,
        IContactEnrollmentService enrollmentService,
        BaseDateTimeSettings dateTimeSettings,
        WorkflowRecurringSettings workflowRecurringSettings,
        DateTimeOffset checkBaseTime,
        string inputOrigin)
    {
        var contactsEnrolled = 0;

        foreach (var contactEntry in contacts)
        {
            var contactId = contactEntry.Key;
            var contactDetail = contactEntry.Value;
            ContactDatetime correspondingItem = null;
            // check if the next occurrence time is in next 24 hours
            foreach (var contactDatetime in contactDateTimeArray)
            {
                if (contactDatetime.ContactId == contactId)
                {
                    correspondingItem = contactDatetime;
                }
            }

            if (correspondingItem == null)
            {
                _logger.LogError("[Time Checking] Could not find time value for contact {ContactId}", contactId);
                continue;
            }
            _logger.LogInformation("[Time Checking] Time values for contact {ContactId} is {DateTimeList}", contactId, JsonConvert.SerializeObject(correspondingItem.DateTimeList));
            foreach (var propertyValueDateTime in correspondingItem.DateTimeList)
            {
                var (isInThisPeriod, nextOccurrence) = CheckTimeEligibility(
                    propertyValueDateTime,
                    dateTimeSettings,
                    workflowRecurringSettings,
                    checkBaseTime);
                _logger.LogInformation("[Time Checking] isTimeEligibility: {IsTimeEligibility}, propertyValueDateTime: {PropertyValueDateTime}, nextOccurrence: {NextOccurrence}", isInThisPeriod, propertyValueDateTime, nextOccurrence);
                if (!isInThisPeriod || nextOccurrence >= DateTimeOffset.MaxValue)
                {
                    _logger.LogInformation("[Time Checking] Time checking failed, ignored in this period");
                    continue;
                }

                var onDateAndTimeArrivedCommonEventBody = new OnDateAndTimeArrivedCommonEventBody(
                    DateTimeOffset.UtcNow,
                    contactId,
                    workflowId,
                    workflowVersionedId,
                    contactDetail.Contact,
                    contactDetail.ContactOwner,
                    contactDetail.Lists,
                    contactDetail.Conversation);
                bool isEligible = await CheckContactEligibilityAsyncV2(
                    onDateAndTimeArrivedCommonEventBody,
                    workflowId,
                    workflowVersionedId,
                    sleekflowCompanyId,
                    contactId,
                    contactDetail,
                    condition,
                    workflowName,
                    inputOrigin);

                if (isEligible)
                {
                    try
                    {
                        _logger.LogInformation("[Enroll] Begin to schedule event for contact with id {ContactId} at {NextOccurrence}.", contactId, nextOccurrence);
                        // send a scheduled message to service bus
                        await enrollmentService.ScheduleContactEnrollmentEventAsync(
                            onDateAndTimeArrivedCommonEventBody,
                            contactId,
                            sleekflowCompanyId,
                            workflowVersionedId,
                            nextOccurrence);

                        contactsEnrolled++;

                        _logger.LogInformation(
                            "[Time Checking] Contact Time Pair [{ContactId}:{DateTime}] enrolled for workflow {WorkflowVersionedId} at {NextOccurrence}",
                            contactId,
                            propertyValueDateTime,
                            workflowVersionedId,
                            nextOccurrence);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "[Time Checking] Error schedule enrollment for Contact Time Pair [{ContactId}:{DateTime}], workflow {WorkflowVersionedId} at {NextOccurrence}",
                            contactId,
                            propertyValueDateTime,
                            workflowVersionedId,
                            nextOccurrence);
                        // Continuing to next contact despite publish error
                    }
                }
            }
        }

        return contactsEnrolled;
    }

    public async Task<int> ProcessAndEnrollEligibleContactsAsyncV2(
        Dictionary<string, ContactDetail> contacts,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string condition,
        string workflowName,
        IContactEnrollmentService enrollmentService,
        string inputOrigin)
    {
        var contactsEnrolled = 0;

        foreach (var contactEntry in contacts)
        {
            var contactId = contactEntry.Key;
            var contactDetail = contactEntry.Value;

            var onDateAndTimeArrivedCommonEventBody = new OnDateAndTimeArrivedCommonEventBody(
                DateTimeOffset.UtcNow,
                contactId,
                workflowId,
                workflowVersionedId,
                contactDetail.Contact,
                contactDetail.ContactOwner,
                contactDetail.Lists,
                contactDetail.Conversation);
            _logger.LogInformation("[Check Eligibility] Begin to check eligibility for contact {ContactId}", contactId);
            bool isEligible = await CheckContactEligibilityAsyncV2(
                onDateAndTimeArrivedCommonEventBody,
                workflowId,
                workflowVersionedId,
                sleekflowCompanyId,
                contactId,
                contactDetail,
                condition,
                workflowName,
                inputOrigin
            );

            if (isEligible)
            {
                try
                {
                    onDateAndTimeArrivedCommonEventBody.ContactId = contactId;
                    _logger.LogInformation("Begin to publish message for contact {ContactId}", contactId);
                    await enrollmentService.PublishContactEnrollmentEventAsync(
                        onDateAndTimeArrivedCommonEventBody,
                        contactId,
                        sleekflowCompanyId,
                        workflowVersionedId);

                    contactsEnrolled++;

                    _logger.LogInformation(
                        "Contact {ContactId} enrolled for workflow {WorkflowVersionedId}",
                        contactId,
                        workflowVersionedId);
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        "Error publishing enrollment for contact {ContactId}, workflow {WorkflowVersionedId}",
                        contactId,
                        workflowVersionedId);
                }
            }
        }

        return contactsEnrolled;
    }

    private async Task<bool> CheckContactEligibilityAsyncV2(
        OnDateAndTimeArrivedCommonEventBody eventBody,
        string workflowId,
        string workflowVersionedId,
        string sleekflowCompanyId,
        string contactId,
        ContactDetail contactDetail,
        string condition,
        string workflowName,
        string origin)
    {
        var checkConditionInput = new CheckWorkflowContactEnrolmentConditionV2Input(
            eventBody,
            workflowId,
            workflowVersionedId,
            sleekflowCompanyId,
            contactId,
            contactDetail,
            condition,
            workflowName,
            origin
        );
        var checkConditionInputJsonStr = JsonConvert.SerializeObject(
            checkConditionInput,
            JsonConfig.DefaultJsonSerializerSettings);

        try
        {
            var pollyContext = new Context();
            pollyContext["logger"] = _logger;

            var checkResMsg = await _retryPolicy.ExecuteAsync(
                async (context) =>
                {
                    var reqMsg = new HttpRequestMessage
                    {
                        Method = HttpMethod.Post,
                        Content = new StringContent(
                            checkConditionInputJsonStr,
                            Encoding.UTF8,
                            "application/json"),
                        RequestUri = new Uri(
                            _appConfig.FlowHubInternalsEndpoint + "/CheckWorkflowContactEnrolmentConditionV2"),
                        Headers =
                        {
                            {
                                "X-Sleekflow-Key", _appConfig.InternalsKey
                            }
                        }
                    };

                    return await _httpClient.SendAsync(reqMsg);
                },
                pollyContext);

            var checkResStr = await checkResMsg.Content.ReadAsStringAsync();
            _logger.LogInformation("Eligibility check, result of Calling CheckWorkflowContactEnrolmentConditionV2 is: {CheckResStr}", checkResMsg);
            var output = checkResStr.ToObject<Output<dynamic>>();

            if (output == null || output.Success == false)
            {
                _logger.LogError(
                    "Eligibility check failed for contact {ContactId} for workflow {WorkflowVersionedId}. Error: {Error}",
                    contactId,
                    workflowVersionedId,
                    JsonConvert.SerializeObject(output));

                return false;
            }

            var checkOutput = ((JObject) output.Data).ToObject<CheckWorkflowContactEnrolmentConditionOutput>()!;
            return checkOutput.EnrolmentConditionSatisfied;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Error during eligibility check for contact {ContactId}, workflow {WorkflowVersionedId}",
                contactId,
                workflowVersionedId);

            return false;
        }
    }

    private (bool IsEligible, DateTimeOffset NextOccurrenceAfter) CheckTimeEligibility(
        DateTimeOffset propertyValueDateTime,
        BaseDateTimeSettings dateTimeSettings,
        WorkflowRecurringSettings workflowRecurringSettings,
        DateTimeOffset checkBaseTime)
    {
        _logger.LogInformation("Begin to calculate the next occurence");
        var nextOccurrenceAfter = _dateTimePropertySettingsParser.GetNextOccurrenceAfter(
            propertyValueDateTime,
            dateTimeSettings,
            workflowRecurringSettings,
            checkBaseTime);
        _logger.LogInformation(
            "the next occurence is {NextOccurrence}, the checking time: {CheckingTime}",
            nextOccurrenceAfter,
            checkBaseTime);
        // if next occurrence time is in before the next recurring time, should be eligible.
        var isInThisPeriod = true;
        if (workflowRecurringSettings != null)
        {
            isInThisPeriod = nextOccurrenceAfter - checkBaseTime <
                             TimeSpan.FromHours(PropertyDateTimeWorkflowOrchestrator.RECURRING_INTEVAL_HOURS);
            _logger.LogInformation("It's a recurring job, isInThisPeriod: {IsInThisPeriod}", isInThisPeriod);
        }
        else
        {
            _logger.LogInformation("It's a time job, isInThisPeriod always be true.");
        }
        return (isInThisPeriod, nextOccurrenceAfter);
    }
}