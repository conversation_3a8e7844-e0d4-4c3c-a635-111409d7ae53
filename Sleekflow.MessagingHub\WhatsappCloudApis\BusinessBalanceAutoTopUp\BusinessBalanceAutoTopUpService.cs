using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Events.OnCloudApiBalanceAutoTopUpEvents;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUp;

public interface IBusinessBalanceAutoTopUpService
{
    bool ShouldPerformAutoTopUp(
        string facebookBusinessId,
        BusinessBalance businessBalance,
        BusinessBalanceAutoTopUpProfile businessBalanceAutoTopUpProfile);

    Task PublishAutoTopUpEvent(
        string facebookBusinessId,
        string businessBalanceId,
        BusinessBalanceAutoTopUpProfile businessBalanceAutoTopUpProfile,
        CancellationToken cancellationToken = default);
}

public class BusinessBalanceAutoTopUpService : IBusinessBalanceAutoTopUpService, ISingletonService
{
    private readonly ILogger<BusinessBalanceAutoTopUpService> _logger;
    private readonly IBus _bus;

    public BusinessBalanceAutoTopUpService(
        IBus bus,
        ILogger<BusinessBalanceAutoTopUpService> logger)
    {
        _bus = bus;
        _logger = logger;
    }

    public async Task PublishAutoTopUpEvent(
        string facebookBusinessId,
        string businessBalanceId,
        BusinessBalanceAutoTopUpProfile businessBalanceAutoTopUpProfile,
        CancellationToken cancellationToken = default)
    {
        var onCloudApiBusinessBalanceAutoTopUpEvent = new OnCloudApiBusinessBalanceAutoTopUpEvent(
            businessBalanceId,
            facebookBusinessId,
            businessBalanceAutoTopUpProfile.CustomerId!,
            businessBalanceAutoTopUpProfile.AutoTopUpPlan);

        await _bus.Publish(onCloudApiBusinessBalanceAutoTopUpEvent, cancellationToken);
    }

    public bool ShouldPerformAutoTopUp(
        string facebookBusinessId,
        BusinessBalance businessBalance,
        BusinessBalanceAutoTopUpProfile businessBalanceAutoTopUpProfile)
    {
        if (businessBalanceAutoTopUpProfile.IsAutoTopUpEnabled is false)
        {
            return false;
        }

        if (businessBalanceAutoTopUpProfile.MinimumBalance.CurrencyIsoCode != businessBalance.Balance.CurrencyIsoCode)
        {
            _logger.LogError(
                "facebookBusinessId: {FacebookBusinessId}," +
                " businessBalanceAutoTopUpProfileId: {BusinessBalanceAutoTopUpProfileId} minimum balance currency iso code: {MinimumBalanceCurrencyIsoCode} " +
                " does not match the business balance: {BusinessBalanceId} currency iso code:{BalanceCurrencyIsoCode}",
                facebookBusinessId,
                businessBalanceAutoTopUpProfile.Id,
                businessBalanceAutoTopUpProfile.MinimumBalance.CurrencyIsoCode,
                businessBalance.Id,
                businessBalance.Balance.CurrencyIsoCode);

            return false;
        }

        if (businessBalanceAutoTopUpProfile.MinimumBalance.Amount <= businessBalance.Balance.Amount)
        {
            return false;
        }

        return true;
    }
}