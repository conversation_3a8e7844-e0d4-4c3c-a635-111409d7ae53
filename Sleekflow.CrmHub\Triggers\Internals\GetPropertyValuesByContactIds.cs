using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Internals;

[TriggerGroup(TriggerGroups.Internals)]
public class GetPropertyValuesByContactIds : ITrigger<GetPropertyValuesByContactIds.GetPropertyValuesByContactIdsInput, GetPropertyValuesByContactIds.GetPropertyValuesByContactIdsOutput>
{
    private const int BatchSize = 500;
    private readonly ISchemafulObjectService _schemafulObjectService;

    public GetPropertyValuesByContactIds(ISchemafulObjectService schemafulObjectService)
    {
        _schemafulObjectService = schemafulObjectService;
    }

    public class GetPropertyValuesByContactIdsInput : Sleekflow.Models.Crm.GetPropertyValuesByContactIdsInput
    {
        [JsonConstructor]
        public GetPropertyValuesByContactIdsInput(string sleekflowCompanyId, string schemaId, List<string> contactIds, string propertyId)
        : base(sleekflowCompanyId, schemaId, contactIds, propertyId)
        {
        }
    }

    public class GetPropertyValuesByContactIdsOutput : Sleekflow.Models.Crm.GetPropertyValuesByContactIdsOutput
    {
        [JsonConstructor]
        public GetPropertyValuesByContactIdsOutput(Dictionary<string, List<object?>> propertyValues)
        : base(propertyValues)
        {
            PropertyValues = propertyValues;
        }
    }

    public async Task<GetPropertyValuesByContactIdsOutput> F(GetPropertyValuesByContactIdsInput input)
    {
        if (!input.ContactIds.Any())
        {
            return new GetPropertyValuesByContactIdsOutput(new Dictionary<string, List<object?>>());
        }

        var selectFields = new List<SchemafulObjectQueryBuilder.ISelect>
        {
            new SchemafulObjectQueryBuilder.Select(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId, null, false),
            new SchemafulObjectQueryBuilder.Select(SchemafulObject.PropertyNamePropertyValues, null, false)
        };

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            selectFields,
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
          new SchemafulObjectQueryBuilder.FilterGroup(
              new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
              {
                  new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                      IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                      "in",
                      input.ContactIds,
                      false)
              })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            input.SleekflowCompanyId,
            input.SchemaId,
            true);

        var allObjects = new List<SchemafulObject>();
        string? continuationToken = null;

        do
        {
            var tokenizedResult = await _schemafulObjectService.GetContinuationTokenizedSchemafulObjectsAsync(queryDefinition, continuationToken, BatchSize);

            if (tokenizedResult.SchemafulObjects.Count == 0)
            {
                break;
            }

            allObjects.AddRange(tokenizedResult.SchemafulObjects);
            continuationToken = tokenizedResult.NextContinuationToken;
        }
        while (continuationToken != null);

        var result = new Dictionary<string, List<object?>>();

        foreach (var contactId in input.ContactIds)
        {
            result[contactId] = new List<object?>();
        }

        foreach (var schemafulObject in allObjects)
        {
            var contactId = schemafulObject.SleekflowUserProfileId;

            if (schemafulObject.PropertyValues.TryGetValue(input.PropertyId, out var propertyValue))
            {
                result[contactId].Add(propertyValue);
            }
        }

        return new GetPropertyValuesByContactIdsOutput(result);
    }
}