using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Subscriptions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.EmailHubDb;

namespace Sleekflow.EmailHub.Models.Providers;

[Resolver(typeof(IEmailHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.ProviderConfig)]
public class ProviderConfig : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; private set; }

    [JsonProperty("email_address")]
    public string EmailAddress { get; private set; }

    [JsonProperty("provider_name")]
    public string ProviderName { get; private set; }

    [JsonProperty("email_subscription")]
    public EmailSubscription EmailSubscription { get; set; }

    [JsonConstructor]
    public ProviderConfig(
        string id,
        string sleekflowCompanyId,
        string providerName,
        string emailAddress,
        EmailSubscription emailSubscription)
        : base(id, ContainerNames.ProviderConfig)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ProviderName = providerName;
        EmailAddress = emailAddress;
        EmailSubscription = emailSubscription;
    }
}