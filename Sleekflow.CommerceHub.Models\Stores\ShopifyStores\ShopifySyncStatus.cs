using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Stores.ShopifyStores;

public class ShopifySyncStatus
{
    [JsonProperty("latest_synced_order_created_at")]
    public DateTimeOffset? LatestSyncedOrderCreatedAt { get; set; }

    [JsonProperty("latest_synced_customer_created_at")]
    public DateTimeOffset? LatestSyncedCustomerCreatedAt { get; set; }

    [JsonProperty("sync_shopify_order_job_id")]
    public string SyncShopifyOrderJobId { get; set; }

    [JsonConstructor]
    public ShopifySyncStatus(
        DateTimeOffset? latestSyncedOrderCreatedAt,
        DateTimeOffset? latestSyncedCustomerCreatedAt,
        string syncShopifyOrderJobId)
    {
        LatestSyncedOrderCreatedAt = latestSyncedOrderCreatedAt;
        LatestSyncedCustomerCreatedAt = latestSyncedCustomerCreatedAt;
        SyncShopifyOrderJobId = syncShopifyOrderJobId;
    }
}