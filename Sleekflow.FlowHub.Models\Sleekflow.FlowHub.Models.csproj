<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.1</TargetFramework>
        <LangVersion>10</LangVersion>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow\Sleekflow.csproj" />
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Orleans.Serialization.Abstractions" Version="8.2.0" />
        <PackageReference Include="Microsoft.Orleans.Serialization.NewtonsoftJson" Version="8.2.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.7.0" />
    </ItemGroup>

    <ItemGroup>
        <Reference Include="GraphApi.Client">
            <HintPath>Binaries\GraphApi.Client.dll</HintPath>
        </Reference>
    </ItemGroup>

</Project>