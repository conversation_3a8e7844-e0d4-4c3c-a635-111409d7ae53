using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.WebCrawlingSessions;
using Sleekflow.IntelligentHub.Workers;

namespace Sleekflow.IntelligentHub.WebCrawlingSessions;

public interface IWebCrawlingSessionService
{
    Task<string> StartWebCrawlingSessionAsync(string sleekflowCompanyId, string url, bool isCrawlingEnabled);

    Task<WebCrawlingSession> GetWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId);

    Task<WebCrawlingSession> PauseWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId);

    Task<WebCrawlingSession> ResumeWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId);

    Task<WebCrawlingSession> PatchWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string sessionId,
        List<CrawlingResult> crawlingResults,
        string[] urlsToProcess,
        string status);
}

public class WebCrawlingSessionService : IWebCrawlingSessionService, IScopedService
{
    private readonly IWebCrawlingSessionRepository _webCrawlingSessionRepository;
    private readonly IIdService _idService;
    private readonly ILogger<WebCrawlingSessionService> _logger;
    private readonly IIntelligentHubWorkerService _workerService;

    public WebCrawlingSessionService(
        IWebCrawlingSessionRepository webCrawlingSessionRepository,
        IIdService idService,
        ILogger<WebCrawlingSessionService> logger,
        IIntelligentHubWorkerService workerService)
    {
        _webCrawlingSessionRepository = webCrawlingSessionRepository;
        _idService = idService;
        _logger = logger;
        _workerService = workerService;
    }

    public async Task<string> StartWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string url,
        bool isCrawlingEnabled)
    {
        try
        {
            var sessionId = _idService.GetId(SysTypeNames.WebCrawlingSession);
            var session = new WebCrawlingSession(
                sessionId,
                url,
                null,
                WebCrawlingSessionStatuses.InProgress,
                isCrawlingEnabled,
                new List<CrawlingResult>(),
                [url],
                sleekflowCompanyId,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow);

            await _webCrawlingSessionRepository.CreateAsync(session, sleekflowCompanyId);

            // Start background crawling
            await _workerService.StartWebCrawlingSessionAsync(sleekflowCompanyId, url, sessionId);

            return sessionId;
        }
        catch (Exception e)
        {
            _logger.LogError(
                "StartWebCrawlingSessionAsync Failed to start web crawling session! {CompanyId} {Url} {Exception}",
                sleekflowCompanyId,
                url,
                e.Message);
            throw;
        }
    }

    public async Task<WebCrawlingSession> GetWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId)
    {
        var session =
            await _webCrawlingSessionRepository.GetAsync(webCrawlingSessionId, sleekflowCompanyId);
        if (session == null)
        {
            throw new SfNotFoundObjectException(webCrawlingSessionId, sleekflowCompanyId);
        }

        return session;
    }

    public async Task<WebCrawlingSession> PauseWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId)
    {
        var session =
            await _webCrawlingSessionRepository.GetAsync(webCrawlingSessionId, sleekflowCompanyId);
        if (session == null)
        {
            throw new SfNotFoundObjectException(webCrawlingSessionId, sleekflowCompanyId);
        }

        if (!session.IsCrawlingEnabled)
        {
            throw new Exception("Cannot pause web crawling session for single URL.");
        }

        if (session.Status == WebCrawlingSessionStatuses.InProgress)
        {
            var updatedSession = await _webCrawlingSessionRepository.PatchAndGetAsync(
                webCrawlingSessionId,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace("/status", WebCrawlingSessionStatuses.Paused),
                    PatchOperation.Replace("/updated_at", DateTimeOffset.UtcNow)
                });

            return updatedSession;
        }

        return session;
    }

    public async Task<WebCrawlingSession> ResumeWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string webCrawlingSessionId)
    {
        var session =
            await _webCrawlingSessionRepository.GetAsync(webCrawlingSessionId, sleekflowCompanyId);
        if (session == null)
        {
            throw new SfNotFoundObjectException(webCrawlingSessionId, sleekflowCompanyId);
        }

        if (!session.IsCrawlingEnabled)
        {
            throw new Exception("Cannot resume web crawling session for single URL.");
        }

        if (session.Status == WebCrawlingSessionStatuses.Paused)
        {
            var updatedSession = await _webCrawlingSessionRepository.PatchAndGetAsync(
                webCrawlingSessionId,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace("/status", WebCrawlingSessionStatuses.InProgress),
                    PatchOperation.Replace("/updated_at", DateTimeOffset.UtcNow)
                });

            // Resume background crawling
            await _workerService.StartWebCrawlingSessionAsync(
                sleekflowCompanyId,
                session.BaseUrl,
                webCrawlingSessionId);

            return updatedSession;
        }

        return session;
    }

    public async Task<WebCrawlingSession> PatchWebCrawlingSessionAsync(
        string sleekflowCompanyId,
        string sessionId,
        List<CrawlingResult> crawlingResults,
        string[] urlsToProcess,
        string status)
    {
        _logger.LogInformation(
            "PatchWebCrawlingSession started for Company: {CompanyId}, SessionId: {SessionId}, " +
            "CrawlingResults: {ResultCount}, UrlsToProcess: {UrlCount}, Status: {Status}",
            sleekflowCompanyId,
            sessionId,
            crawlingResults.Count,
            urlsToProcess.Length,
            status);

        var session =
            await _webCrawlingSessionRepository.GetAsync(sessionId, sleekflowCompanyId);
        if (session == null)
        {
            throw new SfNotFoundObjectException(sessionId, sleekflowCompanyId);
        }

        var baseUrl = session.BaseUrl;
        var baseUrlTitle = session.BaseUrlTitle ??
                           crawlingResults.FirstOrDefault(crawlingResult => crawlingResult.Url == baseUrl)
                               ?.WebpageTitle;

        var updatedSession = await _webCrawlingSessionRepository.PatchAndGetAsync(
            sessionId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/base_url_title", baseUrlTitle),
                PatchOperation.Replace("/crawling_results", crawlingResults),
                PatchOperation.Replace("/urls_to_process", urlsToProcess),
                PatchOperation.Replace("/updated_at", DateTimeOffset.UtcNow),
                PatchOperation.Replace("/status", status),
            });

        _logger.LogInformation(
            "PatchWebCrawlingSession completed for SessionId: {SessionId}",
            sessionId);

        return updatedSession;
    }
}