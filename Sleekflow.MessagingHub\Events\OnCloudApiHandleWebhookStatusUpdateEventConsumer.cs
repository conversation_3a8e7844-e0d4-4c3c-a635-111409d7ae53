﻿using GraphApi.Client.Models.WebhookObjects;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Webhooks;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.Webhooks.Helper;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiHandleWebhookStatusUpdateEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiHandleWebhookStatusUpdateEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiHandleWebhookStatusUpdateEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiHandleWebhookStatusUpdateEventConsumer : IConsumer<OnCloudApiHandleWebhookStatusUpdateEvent>
{
    private readonly ILogger<OnCloudApiHandleWebhookStatusUpdateEventConsumer> _logger;
    private readonly IWabaRepository _wabaRepository;
    private readonly IWabaService _wabaService;
    private readonly IAuditLogService _auditLogService;
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;
    private readonly ISecretConfig _secretConfig;
    private readonly IBus _bus;

    public OnCloudApiHandleWebhookStatusUpdateEventConsumer(
        ILogger<OnCloudApiHandleWebhookStatusUpdateEventConsumer> logger,
        IWabaService wabaService,
        IWabaRepository wabaRepository,
        IAuditLogService auditLogService,
        ISecretConfig secretConfig,
        IBus bus,
        IMessagingHubWebhookService messagingHubWebhookService)
    {
        _logger = logger;
        _wabaService = wabaService;
        _wabaRepository = wabaRepository;
        _auditLogService = auditLogService;
        _secretConfig = secretConfig;
        _bus = bus;
        _messagingHubWebhookService = messagingHubWebhookService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiHandleWebhookStatusUpdateEvent> context)
    {
        var onCloudApiHandleWebhookStatusUpdateEvent = context.Message;

        var whatsappCloudApiChange = onCloudApiHandleWebhookStatusUpdateEvent.Change;

        var change =
            JsonConvert.DeserializeObject<CloudApiWebhookChangeObject>(
                JsonConvert.SerializeObject(whatsappCloudApiChange));

        var value =
            JsonConvert.DeserializeObject<AuditLogCloudApiWebhookValueObject>(
                JsonConvert.SerializeObject(whatsappCloudApiChange.Value));

        if (change == null || value == null)
        {
            return;
        }

        var wabaId = onCloudApiHandleWebhookStatusUpdateEvent.WabaId;

        if (wabaId == _secretConfig.FacebookBusinessId)
        {
            wabaId = WhatsappCloudApiWebhookObjectIdentityHelper.ResolveWabaIdFromFacebookBusinessIdEntryWebhook(
                change,
                value);

            if (wabaId == null)
            {
                return;
            }
        }

        Waba waba;

        try
        {
            // Get Waba configs
            waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(wabaId);
        }
        catch (SfNotFoundObjectException)
        {
            return;
        }

        var webhookObjectIdentity =
            WhatsappCloudApiWebhookObjectIdentityHelper.ResolveWebhookObjectIdentity(waba, change, value);

        if (webhookObjectIdentity == null)
        {
            return;
        }

        try
        {
            await _auditLogService.AuditCloudApiWebhookStatusUpdateEventAsync(
                wabaId,
                AuditingOperation.OnCloudApiWebhookStatusUpdateEvent,
                value,
                wabaId,
                webhookObjectIdentity.FacebookBusinessId,
                webhookObjectIdentity.FacebookPhoneNumberId,
                webhookObjectIdentity.FacebookMessageTemplateId,
                -1);

            if (value.Event == WhatsappCloudApiWebhookEventTypeNames.AccountDeleted &&
                onCloudApiHandleWebhookStatusUpdateEvent.Entry.FacebookWabaId == waba.FacebookWabaId)
            {
                var newWaba = JsonConvert.DeserializeObject<Waba>(JsonConvert.SerializeObject(waba));

                if (newWaba != null)
                {
                    newWaba.RecordStatus = WabaStatuses.PendingToDelete;
                    var isReplaced = await _wabaRepository.ReplaceWabaAsync(waba, newWaba);

                    if (isReplaced)
                    {
                        var messageScheduler = _bus.CreateMessageScheduler();

                        await messageScheduler.SchedulePublish(
                            DateTime.UtcNow.AddDays(1),
                            new OnCloudApiDeleteWabaEvent(waba.FacebookWabaId, waba.FacebookBusinessId!));
                    }
                }
            }

            if (value.Event == WhatsappCloudApiWebhookEventTypeNames.AdAccountLinked) {
                _logger.LogInformation("MM Lite API is onboarded, scheduling resynchronization for waba {Waba} in 10 minutes due to template sync delay", waba.FacebookWabaId);

                await _bus.Publish(
                    new OnWabaResyncEvent(waba.FacebookWabaId),
                    context =>
                    {
                        context.SetScheduledEnqueueTime(DateTime.UtcNow.AddMinutes(10));
                    });
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(
                exception,
                "Exception occur during OnCloudApiHandleWebhookStatusUpdateEvent {Waba}/{Change}/{Value}/{Exception},",
                JsonConvert.SerializeObject(waba),
                JsonConvert.SerializeObject(change),
                JsonConvert.SerializeObject(value),
                JsonConvert.SerializeObject(exception));
        }
    }
}