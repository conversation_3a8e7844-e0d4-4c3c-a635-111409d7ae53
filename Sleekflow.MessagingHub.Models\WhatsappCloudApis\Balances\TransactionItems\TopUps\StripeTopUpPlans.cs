using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;

public static class StripeWhatsAppCreditTopUpPlans
{
    private const string SleekFlowWhatsAppCredit2000Id = "sleekflow_whatsapp_credit_2000";
    private const string SleekFlowWhatsAppCredit1000Id = "sleekflow_whatsapp_credit_1000";
    private const string SleekFlowWhatsAppCredit500Id = "sleekflow_whatsapp_credit_500";
    private const string SleekFlowWhatsAppCredit100Id = "sleekflow_whatsapp_credit_100";
    private const string SleekFlowWhatsAppCredit50Id = "sleekflow_whatsapp_credit_50";
    private const string SleekFlowWhatsAppCredit20Id = "sleekflow_whatsapp_credit_20";

    // Currency for the credit is USD
    // They are the price Id of the top up plans
    private static readonly StripeWhatsAppCreditTopUpPlan SleekFlowWhatsAppCredit2000 = new (
        SleekFlowWhatsAppCredit2000Id,
        "WhatsApp Cloud API Credits - 2000",
        new Money(CurrencyIsoCodes.USD, 2000m));

    private static readonly StripeWhatsAppCreditTopUpPlan SleekFlowWhatsAppCredit1000 = new (
        SleekFlowWhatsAppCredit1000Id,
        "WhatsApp Cloud API Credits - 1000",
        new Money(CurrencyIsoCodes.USD, 1000m));

    private static readonly StripeWhatsAppCreditTopUpPlan SleekFlowWhatsAppCredit500 = new (
        SleekFlowWhatsAppCredit500Id,
        "WhatsApp Cloud API Credits - 500",
        new Money(CurrencyIsoCodes.USD, 500m));

    private static readonly StripeWhatsAppCreditTopUpPlan SleekFlowWhatsAppCredit100 = new (
        SleekFlowWhatsAppCredit100Id,
        "WhatsApp Cloud API Credits - 100",
        new Money(CurrencyIsoCodes.USD, 100m));

    private static readonly StripeWhatsAppCreditTopUpPlan SleekFlowWhatsAppCredit50 = new (
        SleekFlowWhatsAppCredit50Id,
        "WhatsApp Cloud API Credits - 50",
        new Money(CurrencyIsoCodes.USD, 50m));

    private static readonly StripeWhatsAppCreditTopUpPlan SleekFlowWhatsAppCredit20 = new (
        SleekFlowWhatsAppCredit20Id,
        "WhatsApp Cloud API Credits - 20",
        new Money(CurrencyIsoCodes.USD, 20m));

    public static List<StripeWhatsAppCreditTopUpPlan> TopUpPlans { get; } =
        new ()
        {
            SleekFlowWhatsAppCredit2000,
            SleekFlowWhatsAppCredit1000,
            SleekFlowWhatsAppCredit500,
            SleekFlowWhatsAppCredit100,
            SleekFlowWhatsAppCredit50,
            SleekFlowWhatsAppCredit20
        };

    public static List<StripeWhatsAppCreditTopUpPlan> AutoTopPlans { get; } =
        new ()
        {
            SleekFlowWhatsAppCredit2000,
            SleekFlowWhatsAppCredit1000,
            SleekFlowWhatsAppCredit500,
            SleekFlowWhatsAppCredit100,
            SleekFlowWhatsAppCredit50,
            SleekFlowWhatsAppCredit20
        };
}

public class StripeWhatsAppCreditTopUpPlan
{
    [JsonConstructor]
    public StripeWhatsAppCreditTopUpPlan(string id, string name, Money price)
    {
        Id = id;
        Name = name;
        Price = price;
    }

    [JsonProperty("id")]
    public string Id { get; }

    [JsonProperty("name")]
    public string Name { get; }

    // Unlike Twilio we will get the mark up fee from conversation fee 0.03%
    [JsonProperty("price")]
    public Money Price { get; }
}