﻿namespace Sleekflow.Exceptions.Zoho;

public class SfZObjectOperationException : ErrorCodeException
{
    public string ResponseStr { get; }

    public string ZObjectTypeName { get; }

    public HttpResponseMessage HttpResponseMessage { get; }

    public SfZObjectOperationException(
        string responseStr,
        string zObjectTypeName,
        HttpResponseMessage httpResponseMessage)
        : base(
            ErrorCodeConstant.SfZObjectOperationException,
            $"The operation request has failed. zObjectTypeName=[{zObjectTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "zObjectTypeName", zObjectTypeName
                }
            })
    {
        ResponseStr = responseStr;
        ZObjectTypeName = zObjectTypeName;
        HttpResponseMessage = httpResponseMessage;
    }

    public SfZObjectOperationException(
        Exception exception,
        string responseStr,
        string zObjectTypeName,
        HttpResponseMessage httpResponseMessage)
        : base(
            ErrorCodeConstant.SfZObjectOperationException,
            $"The operation request has failed. zObjectTypeName=[{zObjectTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "zObjectTypeName", zObjectTypeName
                }
            },
            exception)
    {
        ResponseStr = responseStr;
        ZObjectTypeName = zObjectTypeName;
        HttpResponseMessage = httpResponseMessage;
    }
}