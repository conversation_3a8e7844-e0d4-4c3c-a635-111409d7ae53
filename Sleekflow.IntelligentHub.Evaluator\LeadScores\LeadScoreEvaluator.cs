using System.Text.Json;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.AI.Evaluation;
using Microsoft.Extensions.AI.Evaluation.Quality;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScores;

public class LeadScoreEvaluator : IEvaluator
{
    public const string ScoreMetricName = "ScoreEvaluation";
    public const int FullScore = 5;

    public IReadOnlyCollection<string> EvaluationMetricNames { get; } =
    [
        ScoreMetricName
    ];

    private const string JsonSchemaInput =
        """
        {
          "$schema": "http://json-schema.org/draft-07/schema#",
          "type": "object",
          "strict": true,
          "properties": {
            "reasoningMatch": {
              "type": "object",
              "properties": {
                "matchScore": {
                  "type": "number",
                  "description": "Overall match score between expected and actual reasoning (1-5)"
                },
                "expectedPath": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  },
                  "description": "List of expected reasoning steps"
                },
                "actualPath": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  },
                  "description": "List of actual reasoning steps taken"
                },
                "missedSteps": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  },
                  "description": "Expected steps that were missed"
                },
                "extraSteps": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  },
                  "description": "Additional steps not in expected path"
                },
                "stepMatchScore": {
                  "type": "number",
                  "description": "Score for step-by-step matching (1-5)"
                }
              },
              "required": ["matchScore", "expectedPath", "actualPath", "missedSteps", "extraSteps", "stepMatchScore"],
              "additionalProperties": false
            },
            "reasoningQuality": {
              "type": "object",
              "properties": {
                "logicStructure": {
                  "type": "number",
                  "description": "Score for logical structure and flow (1-5)"
                },
                "validations": {
                  "type": "array",
                  "items": {
                    "type": "object",
                    "properties": {
                      "step": {
                        "type": "string",
                        "description": "Validation step name"
                      },
                      "expected": {
                        "type": "string",
                        "description": "Expected validation outcome"
                      },
                      "actual": {
                        "type": "string",
                        "description": "Actual validation outcome"
                      },
                      "isValid": {
                        "type": "boolean",
                        "description": "Whether validation passed"
                      }
                    },
                    "required": ["step", "expected", "actual", "isValid"],
                    "additionalProperties": false
                  },
                  "description": "List of validation step comparisons",
                  "additionalProperties": false
                },
                "contextAccuracy": {
                  "type": "number",
                  "description": "Score for context understanding (1-5)"
                },
                "completeness": {
                  "type": "number",
                  "description": "Score for solution completeness (1-5)"
                },
                "totalQualityScore": {
                  "type": "number",
                  "description": "Overall quality score (1-5)"
                }
              },
              "required": ["logicStructure", "validations", "contextAccuracy", "completeness", "totalQualityScore"],
              "additionalProperties": false
            },
            "reasoningIssues": {
              "type": "object",
              "properties": {
                "logicGaps": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  },
                  "description": "Critical gaps in reasoning (-0.5 each)"
                },
                "invalidAssumptions": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  },
                  "description": "Invalid assumptions made (-0.3 each)"
                },
                "missingValidations": {
                  "type": "array",
                  "items": {
                    "type": "string"
                  },
                  "description": "Missing validation steps (-0.2 each)"
                },
                "totalDeduction": {
                  "type": "number",
                  "description": "Total deduction from issues"
                }
              },
              "required": ["logicGaps", "invalidAssumptions", "missingValidations", "totalDeduction"],
              "additionalProperties": false
            },
            "baseScore": {
              "type": "number",
              "description": "Base score before deductions (1-5)"
            },
            "finalScore": {
              "type": "number",
              "description": "Final score after deductions (1-5)"
            },
            "reasoningAnalysis": {
              "type": "string",
              "description": "Detailed analysis of reasoning evaluation"
            }
          },
          "required": [
            "reasoningMatch",
            "reasoningQuality",
            "reasoningIssues",
            "baseScore",
            "finalScore",
            "reasoningAnalysis"
          ],
          "additionalProperties": false
        }
        """;

    protected ChatOptions? ChatOptions { get; } = new ()
    {
        MaxOutputTokens = 2048,
        Temperature = 0.0f,
        TopP = 1f,
        PresencePenalty = 0.0f,
        FrequencyPenalty = 0.0f,
        ResponseFormat = ChatResponseFormat.ForJsonSchema(
            JsonSerializer.SerializeToElement(
                JsonDocument.Parse(JsonSchemaInput))),
    };

    public async ValueTask<EvaluationResult> EvaluateAsync(
        IEnumerable<ChatMessage> messages,
        ChatResponse modelResponse,
        ChatConfiguration? chatConfiguration = null,
        IEnumerable<EvaluationContext>? additionalContext = null,
        CancellationToken cancellationToken = new CancellationToken())
    {
        var context = additionalContext?.OfType<ScoreEvaluationContext>().FirstOrDefault() ??
                      throw new Exception("Expect to find context in additionalContext.");

        var prompt =
            $$"""
              # AI Agent Response Reviewer System

              You are a specialized reviewer agent tasked with evaluating AI responses by analyzing the reasoning alignment between the provided explanation and expected outcomes. Your primary focus is to assess whether the agent's reasoning process matches the expected logical path and solution approach.

              ## Evaluation Framework:

              ### Reasoning Accuracy Score (Scale 1-5):
              1 = **Misaligned Reasoning**: Logic completely deviates from expected path
              2 = **Partial Alignment**: Some correct reasoning steps but significant logical gaps
              3 = **Basic Alignment**: Core reasoning follows expected path with some deviations
              4 = **Strong Alignment**: Reasoning closely matches expected approach with minor variations
              5 = **Perfect Alignment**: Reasoning exactly follows expected logical progression

              ### Key Assessment Areas (Scale 1-5):

              #### 1. Logical Process Evaluation (60%):
              - **Reasoning Structure (25%)**:
                - Clear logical progression
                - Valid inference chains
                - Proper use of given information

              - **Solution Path (20%)**:
                - Alignment with expected solution approach
                - Efficiency of reasoning path
                - Correctness of logical steps

              - **Knowledge Application (15%)**:
                - Appropriate use of domain knowledge
                - Proper application of rules/constraints
                - Relevant context consideration

              #### 2. Explanation Quality (40%):
              - **Reasoning Clarity (20%)**:
                - Clear explanation of thought process
                - Well-structured argumentation
                - Transparent decision-making

              - **Justification Strength (15%)**:
                - Support for conclusions
                - Evidence-based reasoning
                - Valid assumptions

              - **Technical Accuracy (5%)**:
                - Correct use of technical concepts
                - Accurate terminology
                - Proper domain-specific reasoning

              ## Reasoning Deviations (Cumulative Penalties):

              ### Critical Deviations (-0.5 each):
              - Invalid logical leaps
              - Incorrect causal relationships
              - Fundamental misunderstanding of problem
              - Wrong rule application

              ### Major Deviations (-0.3 each):
              - Skipped logical steps
              - Incomplete reasoning chains
              - Questionable assumptions
              - Insufficient justification

              ### Minor Deviations (-0.2 each):
              - Minor logical inconsistencies
              - Unclear explanations
              - Imprecise terminology
              - Non-optimal reasoning paths

              ## Reasoning Quality Indicators:
              1. ✓ Clear logical progression from premises to conclusion
              2. ✓ Proper consideration of all relevant factors
              3. ✓ Valid inference patterns
              4. ✓ Appropriate use of domain knowledge
              5. ✓ Sound problem-solving approach
              6. ✓ Well-supported conclusions
              7. ✓ Transparent decision-making process

              ## Review Process:
              1. Analyze reasoning path alignment with expected approach
              2. Evaluate logical structure and progression
              3. Assess explanation quality and justification
              4. Identify and categorize any reasoning deviations
              5. Provide detailed analysis of alignment and deviations

              ## Context Analysis:
              Expected Reasoning Path:
              ===
              {{context.Scenario}}
              ===

              User Input:
              ===
              {{messages.RenderText()}}
              ===

              Agent's Response & Reasoning:
              ===
              {{modelResponse}}
              ===

              ## Please analyze the response and output a valid JSON object containing the following fields:
              """;

        var result = new EvaluationResult(new NumericMetric(ScoreMetricName));

        var evaluationResponse =
            await chatConfiguration.ChatClient.GetResponseAsync(
                new List<ChatMessage>
                {
                    new ChatMessage(ChatRole.System, prompt),
                },
                ChatOptions,
                cancellationToken: cancellationToken).ConfigureAwait(false);

        var modelResponseForEvaluationPrompt = evaluationResponse.Text.Trim();

        var metric = result.Get<NumericMetric>(ScoreMetricName);

        var scores = JsonConvert.DeserializeObject<RawScores>(modelResponseForEvaluationPrompt)!;

        // Calculate final scores
        var calculator = new LeadScoreCalculator();
        var finalScore = calculator.CalculateFinalScore(scores);

        var finalScoring = finalScore.FinalAdjustedScore;
        metric.Value = finalScoring;

        // Set interpretation based on final adjusted score
        metric.Interpretation = Convert.ToInt32(Math.Round(finalScoring, 0)) switch
        {
            1 => new EvaluationMetricInterpretation(EvaluationRating.Unacceptable),
            2 => new EvaluationMetricInterpretation(EvaluationRating.Poor),
            3 => new EvaluationMetricInterpretation(EvaluationRating.Average),
            4 => new EvaluationMetricInterpretation(EvaluationRating.Good),
            5 => new EvaluationMetricInterpretation(EvaluationRating.Exceptional),
            _ => new EvaluationMetricInterpretation(EvaluationRating.Inconclusive)
        };

        // Add detailed diagnostics
        var details = new
        {
            ReasoningMatch = new
            {
                scores.ReasoningMatch.MatchScore,
                ExpectedPath = scores.ReasoningMatch.ExpectedPath,
                ActualPath = scores.ReasoningMatch.ActualPath,
                MissedSteps = scores.ReasoningMatch.MissedSteps,
                ExtraSteps = scores.ReasoningMatch.ExtraSteps,
                StepMatchScore = scores.ReasoningMatch.StepMatchScore
            },
            ReasoningQuality = new
            {
                scores.ReasoningQuality.LogicStructure,
                Validations = scores.ReasoningQuality.Validations,
                scores.ReasoningQuality.ContextAccuracy,
                scores.ReasoningQuality.Completeness,
                scores.ReasoningQuality.TotalQualityScore
            },
            ReasoningIssues = new
            {
                LogicGaps = scores.ReasoningIssues.LogicGaps,
                InvalidAssumptions = scores.ReasoningIssues.InvalidAssumptions,
                MissingValidations = scores.ReasoningIssues.MissingValidations,
                TotalDeduction = scores.ReasoningIssues.TotalDeduction
            },
            BaseScore = scores.BaseScore,
            FinalScore = scores.FinalScore,
            ReasoningAnalysis = scores.ReasoningAnalysis
        };

        metric.Diagnostics = new List<EvaluationDiagnostic>
        {
            new (
                EvaluationDiagnosticSeverity.Informational,
                JsonConvert.SerializeObject(details))
        };

        return result;
    }
}

public class ScoreEvaluationContext(string scenario) : EvaluationContext("ScoreEvaluationContext")
{
    public readonly string Scenario = scenario;
}