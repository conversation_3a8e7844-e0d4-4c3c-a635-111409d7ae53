﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects.Dtos;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Sequences;
using Sleekflow.CrmHub.Triggers.CrmHubConfigs;
using Sleekflow.CrmHub.Triggers.SchemafulObjects;
using Sleekflow.CrmHub.Triggers.Schemas;
using Sleekflow.Outputs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Tests.IntegrationTests;

public class SchemafulObjectArrayObjectIntegrationTests
{
    private static readonly string MockCompanyId = $"aaaabbbb-cccc-dddd-eeee-{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[..12]}";
    private SchemaDto MockSchema;
    private InnerSchema MockInnerSchema;
    private CrmHubConfig MockCrmHubConfig;

    private string MockSingleLineTextPropertyId;
    private string MockArrayObjectPropertyId;
    private string MockNumericPropertyId;

    // property Ids of InnerSchema
    private string MockSingleLineTextInnerPropertyId;
    private string MockSingleChoiceInnerPropertyId;
    private string MockMultipleChoiceInnerPropertyId;
    private string MockDateTimeInnerPropertyId;

    private List<string> MockSingleChoiceOptionIds;
    private List<string> MockMultipleChoiceOptionIds;

    [SetUp]
    public async Task TestSetUp()
    {
        MockCrmHubConfig = await CreateCrmHubConfig();
        MockSchema = await CreateSchema();
        MockInnerSchema = MockSchema.Properties.First(p => p.DisplayName == "Test ArrayObject").DataType.GetInnerSchema();

        MockSingleLineTextPropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test SingleLineText").Id;
        MockArrayObjectPropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test ArrayObject").Id;
        MockNumericPropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test Numeric").Id;

        MockSingleLineTextInnerPropertyId = MockInnerSchema.Properties.First(p => p.DisplayName == "Test SingleLineText").Id;
        MockSingleChoiceInnerPropertyId = MockInnerSchema.Properties.First(p => p.DisplayName == "Test SingleChoice").Id;
        MockMultipleChoiceInnerPropertyId = MockInnerSchema.Properties.First(p => p.DisplayName == "Test MultipleChoice").Id;
        MockDateTimeInnerPropertyId = MockInnerSchema.Properties.First(p => p.DisplayName == "Test DateTime").Id;

        MockSingleChoiceOptionIds = MockInnerSchema.Properties.First(p => p.DisplayName == "Test SingleChoice")
            .Options!.Select(o => o.Id).ToList();
        MockMultipleChoiceOptionIds = MockInnerSchema.Properties.First(p => p.DisplayName == "Test MultipleChoice")
            .Options!.Select(o => o.Id).ToList();
    }

    [TearDown]
    public async Task TearDown()
    {
        var schemaRepository = GetSchemaRepository();

        var schemas = await schemaRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId")
                .WithParameter("@sleekflowCompanyId", MockCompanyId));

        foreach (var schema in schemas)
        {
            await schemaRepository.DeleteAsync(
                schema.Id,
                MockCompanyId);
        }

        var schemafulObjectRepository = GetSchemafulObjectRepository();

        var schemafulObjects = await schemafulObjectRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId")
                .WithParameter("@sleekflowCompanyId", MockCompanyId));

        foreach (var schemafulObject in schemafulObjects)
        {
            await schemafulObjectRepository.DeleteAsync(
                schemafulObject.Id,
                new PartitionKeyBuilder()
                    .Add(MockCompanyId)
                    .Add(MockSchema.Id)
                    .Add(schemafulObject.Id)
                    .Build());
        }

        var sequenceRepository = GetSequenceRepository();
        await sequenceRepository.DeleteAsync(MockSchema.Id, MockSchema.Id);

        var crmHubConfigRepository = GetCrmHubConfigRepository();
        await crmHubConfigRepository.DeleteAsync(MockCrmHubConfig.Id, MockCompanyId);
    }

    [Test]
    public async Task BasicLifeCycle_WithCorrectInputs_ShouldNotThrowError()
    {
        /*
         * create schemaful object - with array object property - with array size 3
         */

        var innerSchemafulObjectInput_1 = new InnerSchemafulObjectInputDto(
            null,
            new Dictionary<string, object?>
            {
                {
                    MockSingleLineTextInnerPropertyId, "inner single line text 1"
                },
                {
                    MockSingleChoiceInnerPropertyId, MockSingleChoiceOptionIds[0]
                },
                {
                    MockMultipleChoiceInnerPropertyId, new List<string> { MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[1] }
                }
            },
            null);

        var innerSchemafulObjectInput_2 = new InnerSchemafulObjectInputDto(
            null,
            new Dictionary<string, object?>
            {
                {
                    MockSingleLineTextInnerPropertyId, "inner single line text 2"
                }
            },
            null);

        var innerSchemafulObjectInput_3 = new InnerSchemafulObjectInputDto(
            null,
            new Dictionary<string, object?>
            {
                {
                    MockSingleLineTextInnerPropertyId, "inner single line text 3"
                },
                {
                    MockSingleChoiceInnerPropertyId, MockSingleChoiceOptionIds[0]
                },
                {
                    MockMultipleChoiceInnerPropertyId, new List<string> { MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[1] }
                },
                {
                    MockDateTimeInnerPropertyId, "2023-01-01T00:00:00Z"
                }
            },
            null);

        var createSchemafulObjectInput = new CreateSchemafulObject.CreateSchemafulObjectInput(
            MockSchema.Id,
            MockCompanyId,
            string.Empty,
            new Dictionary<string, object?>
            {
                {
                    MockSingleLineTextPropertyId, "single line text"
                },
                {
                    MockArrayObjectPropertyId, new List<InnerSchemafulObjectInputDto>
                    {
                        innerSchemafulObjectInput_1,
                        innerSchemafulObjectInput_2,
                        innerSchemafulObjectInput_3
                    }
                }
            },
            "mock_user_profile_id",
            "123",
            null,
            string.Empty);

        var createSchemaOutput = await CreateSchemafulObjectAsync(createSchemafulObjectInput);

        Assert.That(createSchemaOutput, Is.Not.Null);
        Assert.That(createSchemaOutput!.HttpStatusCode, Is.EqualTo(200));

        var createdSchemafulObject = createSchemaOutput.Data.SchemafulObject;
        var canParse = InnerSchemafulObjectDto.TryParse(
            createdSchemafulObject.PropertyValues[MockArrayObjectPropertyId],
            out var createdInnserSchemafulObjects);

        Assert.Multiple(() =>
        {
            Assert.That(canParse, Is.True);
            Assert.That(createdInnserSchemafulObjects.Count, Is.EqualTo(3));
            Assert.That(string.IsNullOrWhiteSpace(createdInnserSchemafulObjects[0].Id), Is.False);

            var createdInnerSchemafulObject_3 = createdInnserSchemafulObjects[2];
            Assert.That(createdInnerSchemafulObject_3.PropertyValues[MockSingleLineTextInnerPropertyId], Is.EqualTo("inner single line text 3"));
            Assert.That(createdInnerSchemafulObject_3.PropertyValues[MockSingleChoiceInnerPropertyId], Is.EqualTo(MockSingleChoiceOptionIds[0]));
            Assert.That(
                createdInnerSchemafulObject_3.PropertyValues[MockMultipleChoiceInnerPropertyId],
                Is.EqualTo(JArray.FromObject(new List<string> { MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[1] })));
            Assert.That(
                createdInnerSchemafulObject_3.PropertyValues[MockDateTimeInnerPropertyId],
                Is.EqualTo(DateTimeOffset.Parse("2023-01-01T00:00:00Z")));
        });

        /*
         * partial update value of the array object property - update 1st item, delete 2nd & 3rd items, and add 1 new
         */
        var createdInnerSchemafulObject_1 = createdInnserSchemafulObjects[0];
        createdInnerSchemafulObject_1.PropertyValues[MockSingleLineTextInnerPropertyId] = "updated inner single line text 1";
        createdInnerSchemafulObject_1.PropertyValues.Remove(MockSingleChoiceInnerPropertyId);
        createdInnerSchemafulObject_1.PropertyValues[MockMultipleChoiceInnerPropertyId] = new List<string> { MockMultipleChoiceOptionIds[2] };
        createdInnerSchemafulObject_1.PropertyValues[MockDateTimeInnerPropertyId] = "2023-11-11T00:00:00Z";

        // use JObject for the 4th item - to test without [id] and [created_at]
        var innerSchemafulObjectInput_JObj_4 = new JObject
        {
            ["property_values"] = JToken.FromObject(
                new Dictionary<string, object?>
                {
                    {
                        MockSingleLineTextInnerPropertyId, "inner single line text 4"
                    },
                    {
                        MockSingleChoiceInnerPropertyId, MockSingleChoiceOptionIds[1]
                    }
                })
        };

        var partialUpdatePropertyValuesInput = new PartialUpdatePropertyValues.PartialUpdatePropertyValuesInput(
            createdSchemafulObject.Id,
            MockSchema.Id,
            MockCompanyId,
            new Dictionary<string, object?>
            {
                {
                    MockArrayObjectPropertyId, new List<object>
                    {
                        createdInnerSchemafulObject_1,
                        innerSchemafulObjectInput_JObj_4
                    }
                }
            },
            "123",
            null,
            string.Empty);

        var partialUpdatePropertyValuesOutput = await PartialUpdatePropertyValuesAsync(partialUpdatePropertyValuesInput);

        Assert.That(partialUpdatePropertyValuesOutput, Is.Not.Null);
        Assert.That(partialUpdatePropertyValuesOutput!.HttpStatusCode, Is.EqualTo(200));

        var updatedSchemafulObject = partialUpdatePropertyValuesOutput.Data.SchemafulObject;
        canParse = InnerSchemafulObjectDto.TryParse(
            updatedSchemafulObject.PropertyValues[MockArrayObjectPropertyId],
            out var updatedInnserSchemafulObjects);

        Assert.Multiple(() =>
        {
            Assert.That(updatedSchemafulObject.PropertyValues[MockSingleLineTextPropertyId], Is.EqualTo("single line text"));

            Assert.That(canParse, Is.True);
            Assert.That(updatedInnserSchemafulObjects.Count, Is.EqualTo(2));
            Assert.That(string.IsNullOrWhiteSpace(updatedInnserSchemafulObjects[1].Id), Is.False);

            var updatedInnerSchemafulObject_1 = updatedInnserSchemafulObjects[0];
            Assert.That(updatedInnerSchemafulObject_1.PropertyValues[MockSingleLineTextInnerPropertyId], Is.EqualTo("updated inner single line text 1"));
            Assert.That(updatedInnerSchemafulObject_1.PropertyValues.ContainsKey(MockSingleChoiceInnerPropertyId), Is.False);
            Assert.That(
                updatedInnerSchemafulObject_1.PropertyValues[MockMultipleChoiceInnerPropertyId],
                Is.EqualTo(JArray.FromObject(new List<string> { MockMultipleChoiceOptionIds[2] })));
            Assert.That(
                updatedInnerSchemafulObject_1.PropertyValues[MockDateTimeInnerPropertyId],
                Is.EqualTo(DateTimeOffset.Parse("2023-11-11T00:00:00Z")));

            var updatedInnerSchemafulObject_4 = updatedInnserSchemafulObjects[1];
            Assert.That(updatedInnerSchemafulObject_4.PropertyValues[MockSingleLineTextInnerPropertyId], Is.EqualTo("inner single line text 4"));
            Assert.That(updatedInnerSchemafulObject_4.PropertyValues[MockSingleChoiceInnerPropertyId], Is.EqualTo(MockSingleChoiceOptionIds[1]));
        });

        /*
         * update schemaful object
         *  - delete array object property value, update single line property, add numeric property
         *  - update referenced user profile id
         */

        updatedSchemafulObject.PropertyValues[MockArrayObjectPropertyId] = null;
        updatedSchemafulObject.PropertyValues[MockSingleLineTextPropertyId] = "updated single line text";
        updatedSchemafulObject.PropertyValues[MockNumericPropertyId] = 123;

        updatedSchemafulObject.SleekflowUserProfileId = "updated_user_profile_id";

        var updateSchemafulObjectInput = new UpdateSchemafulObject.UpdateSchemafulObjectInput(
            updatedSchemafulObject.Id,
            updatedSchemafulObject.SchemaId,
            updatedSchemafulObject.SleekflowCompanyId,
            updatedSchemafulObject.PropertyValues,
            updatedSchemafulObject.SleekflowUserProfileId,
            "123",
            null,
            string.Empty);

        var updateSchemafulObjectOutput = await UpdateSchemafulObjectAsync(updateSchemafulObjectInput);

        Assert.That(updateSchemafulObjectOutput, Is.Not.Null);
        Assert.That(updateSchemafulObjectOutput!.HttpStatusCode, Is.EqualTo(200));

        var updatedSchemafulObject_2 = updateSchemafulObjectOutput.Data.SchemafulObject;
        Assert.Multiple(() =>
        {
            Assert.That(updatedSchemafulObject_2.SleekflowUserProfileId, Is.EqualTo("updated_user_profile_id"));
            Assert.That(updatedSchemafulObject_2.PropertyValues[MockSingleLineTextPropertyId], Is.EqualTo("updated single line text"));
            Assert.That(updatedSchemafulObject_2.PropertyValues[MockArrayObjectPropertyId], Is.Null);
            Assert.That(updatedSchemafulObject_2.PropertyValues[MockNumericPropertyId], Is.EqualTo(123));
        });
    }

    [Test]
    public async Task FilterTests_WithArrayObject_ShouldReturnCorrectResults()
    {
        await GenerateSampleSchemafulObjects();

        /*
         * Array Property
         *  - contains value of MockDateTimeInnerPropertyId = "2023-01-01T00:00:00Z"
         */

        var getSchemafulObjectsInput = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.ArrayExist>
            {
                new SchemafulObjectQueryBuilder.ArrayExist(
                    MockArrayObjectPropertyId,
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDateTimeInnerPropertyId,
                            "=",
                            "2023-01-01T00:00:00Z",
                            true)
                    })
            });

        var getSchemafulObjectsOutput = await GetSchemafulObjectsAsync(getSchemafulObjectsInput);

        Assert.Multiple(
            () =>
            {
                Assert.That(getSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(getSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(getSchemafulObjectsOutput.Data.SchemafulObjects.Count, Is.EqualTo(4));

                Assert.That(
                    getSchemafulObjectsOutput.Data.SchemafulObjects.Exists(
                        s => s.PropertyValues[MockSingleLineTextPropertyId]!.ToString() == "single line text 0"),
                    Is.True);
            });

        var countSchemafulObjectsOutput = await CountSchemafulObjectsAsync(
            new CountSchemafulObjects.CountSchemafulObjectsInput(
                MockCompanyId,
                MockSchema.Id,
                true,
                new List<CountSchemafulObjects.CountSchemafulObjectsFilterGroup>(),
                getSchemafulObjectsInput.ArrayExists));

        Assert.Multiple(
            () =>
            {
                Assert.That(countSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(countSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(countSchemafulObjectsOutput.Data.Count, Is.EqualTo(4));
            });

        /*
         * Array Property
         *  - have items satisfy: value of MockDateTimeInnerPropertyId = "2023-01-01T00:00:00Z"
         *  - OR have items satisfy: value of MockMultipleChoiceInnerPropertyId contains MockMultipleChoiceOptionIds[1]
         */
        getSchemafulObjectsInput = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.ArrayExist>
            {
                new SchemafulObjectQueryBuilder.ArrayExist(
                    MockArrayObjectPropertyId,
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDateTimeInnerPropertyId,
                            "=",
                            "2023-01-01T00:00:00Z",
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockMultipleChoiceInnerPropertyId,
                            "arrayContains",
                            MockMultipleChoiceOptionIds[1],
                            true)
                    })
            });

        getSchemafulObjectsOutput = await GetSchemafulObjectsAsync(getSchemafulObjectsInput);

        Assert.Multiple(
            () =>
            {
                Assert.That(getSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(getSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(getSchemafulObjectsOutput.Data.SchemafulObjects.Count, Is.EqualTo(6));

                Assert.That(
                    getSchemafulObjectsOutput.Data.SchemafulObjects.Exists(
                        s => s.PropertyValues[MockSingleLineTextPropertyId]!.ToString() == "single line text 5"),
                    Is.True);
            });

        countSchemafulObjectsOutput = await CountSchemafulObjectsAsync(
            new CountSchemafulObjects.CountSchemafulObjectsInput(
                MockCompanyId,
                MockSchema.Id,
                true,
                new List<CountSchemafulObjects.CountSchemafulObjectsFilterGroup>(),
                getSchemafulObjectsInput.ArrayExists));

        Assert.Multiple(
            () =>
            {
                Assert.That(countSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(countSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(countSchemafulObjectsOutput.Data.Count, Is.EqualTo(6));
            });

        /*
         * Array Property
         *  - have items satisfy: value of MockDateTimeInnerPropertyId > "2023-01-01T00:00:00Z"
         *  - and
         *      - have items satisfy: value of MockSingleLineTextInnerPropertyId = "inner single line text 3"
         *      - or have have items satisfy: value of MockSingleLineTextInnerPropertyId = "inner single line text 4"
         */
        getSchemafulObjectsInput = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.ArrayExist>
            {
                new SchemafulObjectQueryBuilder.ArrayExist(
                    MockArrayObjectPropertyId,
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDateTimeInnerPropertyId,
                            ">",
                            "2023-01-01T00:00:00Z",
                            true)
                    }),
                new SchemafulObjectQueryBuilder.ArrayExist(
                    MockArrayObjectPropertyId,
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockSingleLineTextInnerPropertyId,
                            "=",
                            "inner single line text 3",
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockSingleLineTextInnerPropertyId,
                            "=",
                            "inner single line text 4",
                            true)
                    })
            });

        getSchemafulObjectsOutput = await GetSchemafulObjectsAsync(getSchemafulObjectsInput);

        Assert.Multiple(
            () =>
            {
                Assert.That(getSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(getSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(getSchemafulObjectsOutput.Data.SchemafulObjects.Count, Is.EqualTo(1));

                Assert.That(
                    getSchemafulObjectsOutput.Data.SchemafulObjects.Exists(
                        s => s.PropertyValues[MockSingleLineTextPropertyId]!.ToString() == "single line text 4"),
                    Is.True);
            });

        countSchemafulObjectsOutput = await CountSchemafulObjectsAsync(
            new CountSchemafulObjects.CountSchemafulObjectsInput(
                MockCompanyId,
                MockSchema.Id,
                true,
                new List<CountSchemafulObjects.CountSchemafulObjectsFilterGroup>(),
                getSchemafulObjectsInput.ArrayExists));

        Assert.Multiple(
            () =>
            {
                Assert.That(countSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(countSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(countSchemafulObjectsOutput.Data.Count, Is.EqualTo(1));
            });

        /*
         * - SingleLineText Property
         *   - value in ["single line text 0", "single line text 4", "single line text 5"]
         * AND
         * - Array Property
         *  - have items satisfy: value of MockDateTimeInnerPropertyId > "2023-01-01T00:00:00Z"
         */
        getSchemafulObjectsInput = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockSingleLineTextPropertyId,
                            "in",
                            new List<string>
                            {
                                "single line text 0", "single line text 4", "single line text 5"
                            },
                            true)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.ArrayExist>
            {
                new SchemafulObjectQueryBuilder.ArrayExist(
                    MockArrayObjectPropertyId,
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDateTimeInnerPropertyId,
                            ">",
                            "2023-01-01T00:00:00Z",
                            true)
                    })
            });

        getSchemafulObjectsOutput = await GetSchemafulObjectsAsync(getSchemafulObjectsInput);

        Assert.Multiple(
            () =>
            {
                Assert.That(getSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(getSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(getSchemafulObjectsOutput.Data.SchemafulObjects.Count, Is.EqualTo(2));

                Assert.That(
                    getSchemafulObjectsOutput.Data.SchemafulObjects.Exists(
                        s => s.PropertyValues[MockSingleLineTextPropertyId]!.ToString() == "single line text 0"),
                    Is.False);
            });

        countSchemafulObjectsOutput = await CountSchemafulObjectsAsync(
            new CountSchemafulObjects.CountSchemafulObjectsInput(
                MockCompanyId,
                MockSchema.Id,
                true,
                getSchemafulObjectsInput.FilterGroups
                    .Select(fg => new CountSchemafulObjects.CountSchemafulObjectsFilterGroup(fg.Filters))
                    .ToList(),
                getSchemafulObjectsInput.ArrayExists));

        Assert.Multiple(
            () =>
            {
                Assert.That(countSchemafulObjectsOutput, Is.Not.Null);
                Assert.That(countSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
                Assert.That(countSchemafulObjectsOutput.Data.Count, Is.EqualTo(2));
            });
    }

    private async Task<SchemaDto> CreateSchema()
    {
        var mockInnerSchema = new InnerSchema(
            new List<Property>
            {
                new Property(
                    string.Empty,
                    "Test SingleLineText",
                    "test_single_line_text",
                    new SingleLineTextDataType(),
                    true,
                    true,
                    true,
                    true,
                    1,
                    null,
                    DateTimeOffset.UtcNow,
                    null),
                new Property(
                    string.Empty,
                    "Test SingleChoice",
                    "test_single_choice",
                    new SingleChoiceDataType(),
                    false, // set to not required
                    false,
                    true,
                    true,
                    4,
                    null,
                    DateTimeOffset.UtcNow,
                    new List<Option>
                    {
                        new Option(string.Empty, "Male", 0), new Option(string.Empty, "Female", 1)
                    }),
                new Property(
                    string.Empty,
                    "Test MultipleChoice",
                    "test_multiple_choice",
                    new MultipleChoiceDataType(),
                    false,
                    true,
                    false,
                    false,
                    5,
                    null,
                    DateTimeOffset.UtcNow,
                    new List<Option>
                    {
                        new Option(string.Empty, "aaa", 0),
                        new Option(string.Empty, "bbb", 1),
                        new Option(string.Empty, "ccc", 2)
                    }),
                new Property(
                    string.Empty,
                    "Test DateTime",
                    "test_date_time",
                    new DateTimeDataType(),
                    false, // set to not required
                    false,
                    true,
                    true,
                    8,
                    null,
                    DateTimeOffset.UtcNow,
                    null),
            });

        var mockPropertyInputs = new List<PropertyInput>
        {
            new PropertyInput(
                "Test SingleLineText",
                "test_single_line_text",
                new SingleLineTextDataType(),
                false,
                true,
                true,
                true,
                true,
                1,
                null,
                null),
            new PropertyInput(
                "Test ArrayObject",
                "test_arrayobject",
                new ArrayObjectDataType(mockInnerSchema),
                false,
                false, // set to not required
                true,
                true,
                true,
                2,
                null,
                null),
            new PropertyInput(
                "Test Numeric",
                "test_numeric",
                new NumericDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                3,
                null,
                null)
        };

        var mockPrimaryPropertyInput = new PrimaryPropertyInput(
            "Primary Property",
            "primary_property",
            new SingleLineTextDataType(),
            true,
            true,
            true,
            new PrimaryPropertyConfig(true, new Sequential("LOL", 10)),
            null);

        var schemaAccessibilitySettings = new SchemaAccessibilitySettings("custom");

        // /Schemas/CreateSchema
        var createSchemaInput = new CreateSchema.CreateSchemaInput(
            MockCompanyId,
            $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            "one-to-one",
            mockPropertyInputs,
            mockPrimaryPropertyInput,
            schemaAccessibilitySettings);

        var createSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
            });

        var createSchemaOutput =
            await createSchemaScenarioResult.ReadAsJsonAsync<
                Output<CreateSchema.CreateSchemaOutput>>();

        return createSchemaOutput!.Data.Schema;
    }

    private async Task GenerateSampleSchemafulObjects()
    {
        var tasks = new List<Task>();

        for (int i = 0; i < 10; i++)
        {
            var innerSchemafulObjectInput_1 = new InnerSchemafulObjectInputDto(
                null,
                new Dictionary<string, object?>
                {
                    {
                        MockSingleLineTextInnerPropertyId, $"inner single line text {i}"
                    },
                    {
                        MockSingleChoiceInnerPropertyId, MockSingleChoiceOptionIds[0]
                    },
                    {
                        MockMultipleChoiceInnerPropertyId, new List<string>
                        {
                            MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[i < 6 ? 1 : 2]
                        }
                    },
                    {
                        MockDateTimeInnerPropertyId, i < 4 ? "2023-01-01T00:00:00Z" : "2024-01-01T00:00:00Z"
                    }
                },
                null);

            var innerSchemafulObjectInput_2 = new InnerSchemafulObjectInputDto(
                null,
                new Dictionary<string, object?>
                {
                    {
                        MockSingleLineTextInnerPropertyId, $"inner single line text {i}"
                    }
                },
                null);

            var createSchemafulObjectInput = new CreateSchemafulObject.CreateSchemafulObjectInput(
                MockSchema.Id,
                MockCompanyId,
                string.Empty,
                new Dictionary<string, object?>
                {
                    {
                        MockSingleLineTextPropertyId, $"single line text {i}"
                    },
                    {
                        MockArrayObjectPropertyId, new List<InnerSchemafulObjectInputDto>
                        {
                            innerSchemafulObjectInput_1, innerSchemafulObjectInput_2
                        }
                    }
                },
                $"mock_user_profile_id_{i}",
                "123",
                null,
                string.Empty);

            tasks.Add(CreateSchemafulObjectAsync(createSchemafulObjectInput));
        }

        await Task.WhenAll(tasks);
    }

    private async Task<Output<CreateSchemafulObject.CreateSchemafulObjectOutput>?> CreateSchemafulObjectAsync(
        CreateSchemafulObject.CreateSchemafulObjectInput createSchemafulObjectInput)
    {
        var createSchemafulObjectScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemafulObjectInput).ToUrl("/SchemafulObjects/CreateSchemafulObject");
            });

        return await createSchemafulObjectScenarioResult.ReadAsJsonAsync<
                Output<CreateSchemafulObject.CreateSchemafulObjectOutput>>();
    }

    private async Task<Output<UpdateSchemafulObject.UpdateSchemafulObjectOutput>?> UpdateSchemafulObjectAsync(
        UpdateSchemafulObject.UpdateSchemafulObjectInput updateSchemafulObjectInput)
    {
        var updateSchemafulObjectResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateSchemafulObjectInput).ToUrl("/SchemafulObjects/UpdateSchemafulObject");
            });

        return await updateSchemafulObjectResult.ReadAsJsonAsync<
                Output<UpdateSchemafulObject.UpdateSchemafulObjectOutput>>();
    }

    private async Task<Output<PartialUpdatePropertyValues.PartialUpdatePropertyValuesOutput>?> PartialUpdatePropertyValuesAsync(
        PartialUpdatePropertyValues.PartialUpdatePropertyValuesInput partialUpdatePropertyValuesInput)
    {
        var updateSchemafulObjectResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(partialUpdatePropertyValuesInput).ToUrl("/SchemafulObjects/PartialUpdatePropertyValues");
            });

        return await updateSchemafulObjectResult.ReadAsJsonAsync<
            Output<PartialUpdatePropertyValues.PartialUpdatePropertyValuesOutput>>();
    }

    private async Task<Output<GetSchemafulObjects.GetSchemafulObjectsOutput>?> GetSchemafulObjectsAsync(
        GetSchemafulObjects.GetSchemafulObjectsInput getSchemafulObjectsInput)
    {
        var getSchemafulObjectResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsInput).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        return await getSchemafulObjectResult.ReadAsJsonAsync<
               Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();
    }

    private async Task<Output<CountSchemafulObjects.CountSchemafulObjectsOutput>?> CountSchemafulObjectsAsync(
        CountSchemafulObjects.CountSchemafulObjectsInput countSchemafulObjectsInput)
    {
        var countSchemafulObjectsResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(countSchemafulObjectsInput).ToUrl("/SchemafulObjects/CountSchemafulObjects");
            });

        return await countSchemafulObjectsResult.ReadAsJsonAsync<
            Output<CountSchemafulObjects.CountSchemafulObjectsOutput>>();
    }

    private async Task<CrmHubConfig> CreateCrmHubConfig()
    {
        var initializeCrmHubConfigInput = new InitializeCrmHubConfig.InitializeCrmHubConfigInput(
            MockCompanyId,
            new UsageLimit(100, 100, 100, 11, 10),
            null,
            "3880",
            null);

        var initializeCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(initializeCrmHubConfigInput).ToUrl("/CrmHubConfigs/InitializeCrmHubConfig");
            });

        var initializeCrmHubConfigOutput =
            await initializeCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<InitializeCrmHubConfig.InitializeCrmHubConfigOutput>>();

        return initializeCrmHubConfigOutput!.Data.CrmHubConfig;
    }

    private SchemaRepository GetSchemaRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        return new SchemaRepository(
            NullLogger<SchemaRepository>.Instance,
            serviceProvider);
    }

    private SchemafulObjectRepository GetSchemafulObjectRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        return new SchemafulObjectRepository(
            NullLogger<SchemafulObjectRepository>.Instance,
            serviceProvider);
    }

    private SequenceRepository GetSequenceRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        return new SequenceRepository(
            NullLogger<SequenceRepository>.Instance,
            serviceProvider);
    }

    private CrmHubConfigRepository GetCrmHubConfigRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var crmHubConfigRepository = new CrmHubConfigRepository(
            NullLogger<CrmHubConfigRepository>.Instance,
            serviceProvider);

        return crmHubConfigRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }
}