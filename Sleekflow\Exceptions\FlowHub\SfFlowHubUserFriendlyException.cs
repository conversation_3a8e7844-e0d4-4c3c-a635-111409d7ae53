using Newtonsoft.Json;

namespace Sleekflow.Exceptions.FlowHub;

public class SfFlowHubUserFriendlyException : ErrorCodeException
{
    public string UserFriendlyErrorCode { get; set; }

    public string UserFriendlyErrorMessage { get; set; }

    [JsonConstructor]
    public SfFlowHubUserFriendlyException(
        string userFriendlyErrorCode,
        string userFriendlyErrorMessage)
        : base(
            ErrorCodeConstant.SfFlowHubUserFriendlyException,
            userFriendlyErrorMessage)
    {
        UserFriendlyErrorCode = userFriendlyErrorCode;
        UserFriendlyErrorMessage = userFriendlyErrorMessage;
    }

    public SfFlowHubUserFriendlyException(
        string userFriendlyErrorCode,
        string userFriendlyErrorMessage,
        Exception innerException)
        : base(
            ErrorCodeConstant.SfFlowHubUserFriendlyException,
            userFriendlyErrorMessage,
            innerException)
    {
        UserFriendlyErrorCode = userFriendlyErrorCode;
        UserFriendlyErrorMessage = userFriendlyErrorMessage;
    }
}