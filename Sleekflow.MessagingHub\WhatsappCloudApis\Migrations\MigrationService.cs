using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Models.MigrationObjects;
using GraphApi.Client.Payloads;
using GraphApi.Client.Payloads.Models;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;

public interface IMigrationService
{
    Task<List<GetBusinessDetailResponse>> GetWhatsappCloudApiUserBusinessesAsync(
        string sleekflowCompanyId,
        string userAccessToken);

    Task<List<GetWhatsappBusinessAccountSubscribedAppsResponse>> GetWhatsappCloudApiUserBusinessWabasAsync(
        string sleekflowCompanyId,
        string userAccessToken,
        string facebookBusinessId);

    Task<List<WhatsappPhoneNumberDetail>> GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdAsync(
        string sleekflowCompanyId,
        string userAccessToken,
        string facebookWabaId);

    Task<bool> MigrateWhatsappCloudApiPhoneNumberToCloudApiAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookPhoneNumberId,
        string pin,
        string userAccessToken,
        string? backUpData,
        string? backUpPassword);

    Task<string> InitiateWhatsappCloudApiPhoneNumberWabaMigrationAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string countryCode,
        string phoneNumber,
        string? businessIntegrationSystemUserAccessToken);

    Task<bool> RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookPhoneNumberId,
        string codeMethod,
        string language,
        string? businessIntegrationSystemUserAccessToken);

    Task<bool> VerifyWhatsappCloudApiPhoneNumberOwnershipAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookPhoneNumberId,
        string code,
        string? businessIntegrationSystemUserAccessToken,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);
}

public class MigrationService : IMigrationService, ISingletonService
{
    private readonly ILogger<MigrationService> _logger;
    private readonly IAuditLogService _auditLogService;
    private readonly IWhatsappCloudApiBspClient _whatsappCloudApiBspClient;
    private readonly HttpClient _httpClient;

    public MigrationService(
        ILogger<MigrationService> logger,
        IAuditLogService auditLogService,
        ICloudApiClients cloudApiClients,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _auditLogService = auditLogService;
        _whatsappCloudApiBspClient = cloudApiClients.WhatsappCloudApiBspClient;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<List<GetBusinessDetailResponse>> GetWhatsappCloudApiUserBusinessesAsync(
        string sleekflowCompanyId,
        string userAccessToken)
    {
        var bspClient = new WhatsappCloudApiBspClient(userAccessToken, new HttpClient());
        var businessesResponse = await bspClient.GetUserBusinesses();
        var businesses = businessesResponse.Data.ToList();
        do
        {
            businessesResponse = await bspClient.GetUserBusinesses(
                paginationParam: new CursorBasedPaginationParam()
                {
                    After = businessesResponse.Paging?.Cursors?.After
                });

            businesses = businesses.Concat(businessesResponse.Data).ToList();
        }
        while (businessesResponse.Data.Count > 0);
        return businesses;
    }

    public async Task<List<GetWhatsappBusinessAccountSubscribedAppsResponse>> GetWhatsappCloudApiUserBusinessWabasAsync(
        string sleekflowCompanyId,
        string userAccessToken,
        string facebookBusinessId)
    {
        var bspClient = new WhatsappCloudApiBspClient(userAccessToken, new HttpClient());
        var wabasResponse = await bspClient.GetUserBusinessWabas(facebookBusinessId);
        var wabas = wabasResponse.Data.ToList();

        do
        {
            wabasResponse = await bspClient.GetUserBusinessWabas(
                facebookBusinessId,
                paginationParam: new CursorBasedPaginationParam()
                {
                    After = wabasResponse.Paging?.Cursors?.After
                });

            wabas = wabas.Concat(wabasResponse.Data).ToList();
        }
        while (wabasResponse.Data.Count > 0);
        return wabas;
    }

    public async Task<List<WhatsappPhoneNumberDetail>> GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdAsync(
        string sleekflowCompanyId,
        string userAccessToken,
        string facebookWabaId)
    {
        var bspClient = new WhatsappCloudApiBspClient(userAccessToken, new HttpClient());
        var phoneNumbersResponse = await bspClient.GetPhoneNumbersByWabaId(
            facebookWabaId,
            paginationParam: new CursorBasedPaginationParam()
            {
                Limit = 500
            });

        var phoneNumbers = phoneNumbersResponse.Data.ToList();

        return phoneNumbers;
    }

    public async Task<bool> MigrateWhatsappCloudApiPhoneNumberToCloudApiAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookPhoneNumberId,
        string pin,
        string userAccessToken,
        string? backUpData,
        string? backUpPassword)
    {
        var migrateAccountObject = new WhatsappPhoneNumberMigrateAccountObject
        {
            Pin = pin
        };

        if (!string.IsNullOrEmpty(backUpData) && !string.IsNullOrEmpty(backUpPassword))
        {
            migrateAccountObject.Backup = new WhatsappBackupPhoneNumberObject()
            {
                Data = backUpData, Password = backUpPassword
            };
        }

        var response = new MigrateAccountToCloudApiResponse();
        var bspClient = new WhatsappCloudApiBspClient(userAccessToken, new HttpClient());

        try
        {
            response = await _auditLogService.GetCloudApiAuditedResponse(
                () => bspClient.MigrateAccountToCloudApiAsync(
                    migrateAccountObject,
                    facebookPhoneNumberId),
                new BaseAuditingObject(facebookWabaId)
                {
                    SleekflowCompanyId = sleekflowCompanyId,
                    Parameters = new Dictionary<string, object?>
                    {
                        {
                            "facebook_waba_id", facebookWabaId
                        },
                        {
                            "facebook_waba_phone_number_id", facebookPhoneNumberId
                        },
                        {
                            "back_up_data", backUpData
                        },
                        {
                            "back_up_password", backUpPassword
                        }
                    },
                    Operation = AuditingOperation.MigratePhoneNumberToCloudApi
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for cloud api migration with sleekflow company id {SleekflowCompanyId}, facebook waba id {FacebookWabaId}, facebook phone number id {FacebookPhoneNumberId}, pin {Pin}, back up data {BackUpData}, back up password {BackUpPassword}",
                sleekflowCompanyId,
                facebookWabaId,
                facebookPhoneNumberId,
                pin,
                backUpData,
                backUpPassword);

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    "Unable migrate to cloud api",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }
        }

        return response.Success;
    }

    public async Task<string> InitiateWhatsappCloudApiPhoneNumberWabaMigrationAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string countryCode,
        string phoneNumber,
        string? businessIntegrationSystemUserAccessToken)
    {
        _logger.LogInformation(
            "Initiate WhatsApp Cloud Api Phone Number {CountryCode} {PhoneNumber} Migration into destination waba {FacebookWabaId} for company id {SleekflowCompanyId}",
            countryCode,
            phoneNumber,
            facebookWabaId,
            sleekflowCompanyId);

        var response = new InitiatePhoneNumberMigrationResponse();

        try
        {
            var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new WhatsappCloudApiBspClient(
                    businessIntegrationSystemUserAccessToken,
                    _httpClient)
                : _whatsappCloudApiBspClient;

            response = await _auditLogService.GetCloudApiAuditedResponse(
                () => whatsappCloudApiBspClient.InitiatePhoneNumberMigrationAsync(
                    new WhatsappPhoneNumberMigratePhoneNumberObject()
                    {
                        CountryCode = countryCode, PhoneNumber = phoneNumber, MigratePhoneNumber = true
                    },
                    facebookWabaId),
                new BaseAuditingObject(facebookWabaId)
                {
                    SleekflowCompanyId = sleekflowCompanyId,
                    Parameters = new Dictionary<string, object?>
                    {
                        {
                            "facebook_waba_id", facebookWabaId
                        },
                        {
                            "country_code", countryCode
                        },
                        {
                            "phone_number", phoneNumber
                        }
                    },
                    Operation = AuditingOperation.InitiatePhoneNumberWabaMigration
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for initiating phone number migration with sleekflow company id {SleekflowCompanyId}, facebook waba id {FacebookWabaId}, country code {CountryCode}, phone number {PhoneNumber}",
                sleekflowCompanyId,
                facebookWabaId,
                countryCode,
                phoneNumber);

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    $"Unable to initiate phone number migration",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }
        }

        return response.Id;
    }

    public async Task<bool> RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookPhoneNumberId,
        string codeMethod,
        string language,
        string? businessIntegrationSystemUserAccessToken)
    {
        var response = new VerifyPhoneOwnershipRequestCodeResponse();

        try
        {
            var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new WhatsappCloudApiBspClient(
                    businessIntegrationSystemUserAccessToken,
                    _httpClient)
                : _whatsappCloudApiBspClient;

            response = await _auditLogService.GetCloudApiAuditedResponse(
                () => whatsappCloudApiBspClient.VerifyPhoneOwnershipRequestAsync(
                    codeMethod,
                    language,
                    facebookPhoneNumberId),
                new BaseAuditingObject(facebookWabaId)
                {
                    SleekflowCompanyId = sleekflowCompanyId,
                    Parameters = new Dictionary<string, object?>()
                    {
                        {
                            "facebook_waba_id", facebookWabaId
                        },
                        {
                            "facebook_waba_phone_number_id", facebookPhoneNumberId
                        },
                        {
                            "code_method", codeMethod
                        },
                        {
                            "language", language
                        }
                    },
                    Operation = AuditingOperation.RequestPhoneNumberVerificationCode
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for requesting phone number verification code with sleekflow company id {SleekflowCompanyId}, facebook waba id {FacebookWabaId}, facebook phone number id {FacebookPhoneNumberId}, code method {CodeMethod}Ï, language {Language}",
                sleekflowCompanyId,
                facebookWabaId,
                facebookPhoneNumberId,
                codeMethod,
                language);

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    "Unable to request phone number verification code",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }
        }

        return response.Success;
    }

    public async Task<bool> VerifyWhatsappCloudApiPhoneNumberOwnershipAsync(
        string sleekflowCompanyId,
        string facebookWabaId,
        string facebookPhoneNumberId,
        string code,
        string? businessIntegrationSystemUserAccessToken,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var response = new VerifyPhoneOwnershipCodeResponse();

        try
        {
            var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
                ? new WhatsappCloudApiBspClient(
                    businessIntegrationSystemUserAccessToken,
                    _httpClient)
                : _whatsappCloudApiBspClient;

            response = await _auditLogService.GetCloudApiAuditedResponse(
                () => whatsappCloudApiBspClient.VerifyPhoneOwnershipCodeAsync(
                    code,
                    facebookPhoneNumberId),
                new BaseAuditingObject(facebookWabaId)
                {
                    SleekflowCompanyId = sleekflowCompanyId,
                    Parameters = new Dictionary<string, object?>()
                    {
                        {
                            "facebook_waba_id", facebookWabaId
                        },
                        {
                            "facebook_waba_phone_number_id", facebookPhoneNumberId
                        },
                        {
                            "code", code
                        }
                    },
                    Operation = AuditingOperation.VerifyPhoneNumberOwnership
                });
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Caught error for verifying phone number code with input sleekflow company id {SleekflowCompanyId}, facebook waba id {FacebookWabaId}, facebook phone number id {FacebookPhoneNumberId}, code {Code}, staff id {StaffId}, staff team ids {StaffTeamIds}",
                sleekflowCompanyId,
                facebookWabaId,
                facebookPhoneNumberId,
                code,
                sleekflowStaffId,
                sleekflowStaffTeamIds?.ToString());

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    "Unable to verify phone number code with migration",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }
        }

        return response.Success;
    }
}