using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors;

public interface IParallelStepExecutor : IStepExecutor
{
}

public class ParallelStepExecutor : GeneralStepExecutor<ParallelStep>, IParallelStepExecutor, IScopedService
{
    private readonly IStepOrchestrationService _stepOrchestrationService;

    public ParallelStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IStepOrchestrationService stepOrchestrationService,
        IServiceProvider serviceProvider)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stepOrchestrationService = stepOrchestrationService;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var parallelStep = (ParallelStep) step;

        await _stepOrchestrationService.ExecuteParallelStepAsync(
            state.Id,
            step.Id,
            parallelStep.ParallelBranches.Select(b => b.Step.Id).ToList(),
            stackEntries);

        // The actions is Delayed Complete
    }
}