using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator.AddLabel;

public class AddLabelTestResult(
    string methodName,
    AddLabelResult? answer,
    long elapsedMilliseconds)
{
    public string MethodName { get; init; } = methodName;

    public AddLabelResult? Answer { get; init; } = answer;

    public long ElapsedMilliseconds { get; init; } = elapsedMilliseconds;
}