using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class StaffUnassignedFromConversationSystemLogData
{
    [JsonProperty("staff_id")]
    public string StaffId { get; set; }

    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [JsonProperty("user_profile_id")]
    public string UserProfileId { get; set; }

    [JsonConstructor]
    public StaffUnassignedFromConversationSystemLogData(string staffId, string conversationId, string userProfileId)
    {
        StaffId = staffId;
        ConversationId = conversationId;
        UserProfileId = userProfileId;
    }
}