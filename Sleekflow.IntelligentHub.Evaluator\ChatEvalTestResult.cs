﻿using CsvHelper.Configuration.Attributes;

namespace Sleekflow.IntelligentHub.Evaluator;

public class ChatEvalTestResult
{
    [Name("test_id")]
    public string TestId { get; init; } = string.Empty;

    [Name("scenario")]
    public string <PERSON>ena<PERSON> { get; init; } = string.Empty;

    [Name("question")]
    public string Question { get; init; } = string.Empty;

    [Name("expected_answer")]
    public string ExpectedAnswer { get; init; } = string.Empty;

    [Name("no_rag_answer")]
    public string NoRagAnswer { get; init; } = string.Empty;

    [Name("smart_reply_answer")]
    public string SmartReplyAnswer { get; init; } = string.Empty;

    [Name("advance_smart_reply_(60s)_answer")]
    public string LightRagLongAnswer { get; init; } = string.Empty;

    [Name("advance_smart_reply_(30s)_answer")]
    public string LightRagMediumAnswer { get; init; } = string.Empty;

    [Name("advance_smart_reply_(15s)_answer")]
    public string LightRagShortAnswer { get; init; } = string.Empty;

    [Name("source_files")]
    public string SourceFiles { get; init; } = string.Empty;
}