﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class LoopThroughAndEnrollObjectsToFlowHubOutput
{
    [JsonProperty("loop_through_objects_progress_state_id")]
    public string LoopThroughObjectsProgressStateId { get; set; }

    [JsonConstructor]
    public LoopThroughAndEnrollObjectsToFlowHubOutput(string loopThroughObjectsProgressStateId)
    {
        LoopThroughObjectsProgressStateId = loopThroughObjectsProgressStateId;
    }
}