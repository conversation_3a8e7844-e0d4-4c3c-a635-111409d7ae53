using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class ReInitProviderConnectionOutput
{
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("is_re_authentication_required")]
    public bool IsReAuthenticationRequired { get; set; }

    [JsonProperty("context")]
    public dynamic? Context { get; set; }

    [JsonConstructor]
    public ReInitProviderConnectionOutput(
        string providerName,
        bool isReAuthenticationRequired,
        object? context)
    {
        ProviderName = providerName;
        IsReAuthenticationRequired = isReAuthenticationRequired;
        Context = context;
    }
}