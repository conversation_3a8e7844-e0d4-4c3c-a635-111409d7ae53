using Sleekflow.FlowHub.TravisBackend.Handlers;
using Sleekflow.Mvc.Https;

namespace Sleekflow.FlowHub.TravisBackend;

public static class TravisBackendModule
{
    public static IServiceCollection AddTravisBackendHttpClient(this IServiceCollection services)
    {
        services.AddTransient<FlowHubTravisBackendAuthorizationHandler>();

        services.AddHttpClient("travis-backend")
            .ConfigureHttpClient((_, client) => { client.Timeout = TimeSpan.FromSeconds(60); })
            .ConfigurePrimaryHttpMessageHandler(() => new PrivateNetworkHttpClientHandler
            {
                AllowAutoRedirect = false,
            })
            .AddHttpMessageHandler<FlowHubTravisBackendAuthorizationHandler>();

        return services;
    }
}