using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Stores;

public class StoreInput : IHasMetadata
{
    [Required]
    [ValidateArray]
    [MinLength(1)]
    [MaxLength(4)]
    [JsonProperty(Store.PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [Required]
    [ValidateArray]
    [MaxLength(16)]
    [JsonProperty(Store.PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [Required]
    [JsonProperty(Store.PropertyNameIsViewEnabled)]
    public bool IsViewEnabled { get; set; }

    [Required]
    [JsonProperty(Store.PropertyNameIsPaymentEnabled)]
    public bool IsPaymentEnabled { get; set; }

    [Required]
    [ValidateArray]
    [MinLength(1)]
    [MaxLength(4)]
    [JsonProperty(Store.PropertyNameLanguages)]
    public List<LanguageInputDto> Languages { get; set; }

    [Required]
    [ValidateArray]
    [MinLength(1)]
    [MaxLength(4)]
    [JsonProperty(Store.PropertyNameCurrencies)]
    public List<CurrencyInputDto> Currencies { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty(Store.PropertyNameTemplateDict)]
    public StoreTemplateDict TemplateDict { get; set; }

    [Required]
    [ValidateObject]
    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [ValidateObject]
    [JsonProperty("store_integration_external_config_input")]
    public StoreIntegrationExternalConfigInput? StoreIntegrationExternalConfigInput { get; set; }

    [ValidateObject]
    [JsonProperty(Store.PropertyNameSubscriptionStatus)]
    public StoreSubscriptionStatus? SubscriptionStatus { get; set; }

    [JsonConstructor]
    public StoreInput(
        List<Multilingual> names,
        List<Description> descriptions,
        bool isViewEnabled,
        bool isPaymentEnabled,
        List<LanguageInputDto> languages,
        List<CurrencyInputDto> currencies,
        StoreTemplateDict templateDict,
        Dictionary<string, object?> metadata,
        StoreIntegrationExternalConfigInput? storeIntegrationExternalConfigInput,
        StoreSubscriptionStatus? subscriptionStatus)
    {
        Names = names;
        Descriptions = descriptions;
        IsViewEnabled = isViewEnabled;
        IsPaymentEnabled = isPaymentEnabled;
        Languages = languages;
        Currencies = currencies;
        TemplateDict = templateDict;
        Metadata = metadata;
        StoreIntegrationExternalConfigInput = storeIntegrationExternalConfigInput;
        SubscriptionStatus = subscriptionStatus;
    }
}