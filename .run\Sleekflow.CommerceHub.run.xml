﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.CommerceHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.CommerceHub/bin/Debug/net8.0/Sleekflow.CommerceHub.exe" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.CommerceHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="APP_CONFIGURATION_CONN_STR" value="Endpoint=https://sleekflow-app-configurationb83ec65c.azconfig.io;Id=0f3Z-lb-s0:hWLXNKUM3ATACVXeEqAT;Secret=Ub6/5/VjpnRCXqQ4JNhRVzVp350ymvs0QP4cbFm0ACo=" />
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7084;http://localhost:7085" />
      <env name="CACHE_PREFIX" value="Sleekflow.CommerceHub" />
      <env name="COGNITIVE_SEARCH_COMMERCE_HUB_CATEGORY_INDEX" value="commercehubdb-category-index" />
      <env name="COGNITIVE_SEARCH_COMMERCE_HUB_PRODUCT_INDEX" value="commercehubdb-product-index" />
      <env name="COGNITIVE_SEARCH_CREDENTIAL_KEY" value="sKa3Y5E4IzPhrsWsYTChzFMykA0RB9lG20hhHN36zKAzSeAX8HN2" />
      <env name="COGNITIVE_SEARCH_SERVICE" value="https://sleekflow-commh-search-service.search.windows.net" />
      <env name="COMMERCE_HUB_ENDPOINT" value="https://localhost:7084" />
      <env name="COSMOS_CHANGE_FEED_ENV_ID" value="default" />
      <env name="COSMOS_COMMERCE_HUB_DB_DATABASE_ID" value="commercehubdb" />
      <env name="COSMOS_COMMERCE_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_COMMERCE_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="FILE_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=s3dbb281f;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="IMAGE_STORAGE_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=se519af6c;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="INTERNALS_KEY" value="" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="PAYMENT_GATEWAY_REDIRECT_URL" value="http://localhost:7078" />
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="SQL_CONNECTION" value="Server=tcp:traviscrmdbhk.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=travis;Password=*********************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Column Encryption Setting=Enabled;Connection Timeout=30;" />
      <env name="STRIPE_API_KEY" value="sk_test_51Klpk8I2ilBBY57ZDMKI33PKdi1uuyCunMQEqRmPdoyRgeBAG1zGH4Lw1DFNecaAgQZJy8yWENVMSo2IEx2bsBi20085GLAlzT" />
      <env name="STRIPE_API_KEY_GB" value="sk_test_51LBDmrD9JBREupA8NA9jMA4igzfP1cobkZaFDwLSzM4A6NGqT5EoPRBZwxOc09ELrysTrjJSr2S2JbnAwbUzhTUd00OyV4vbbY" />
      <env name="STRIPE_API_KEY_HK" value="sk_test_51Klpk8I2ilBBY57ZDMKI33PKdi1uuyCunMQEqRmPdoyRgeBAG1zGH4Lw1DFNecaAgQZJy8yWENVMSo2IEx2bsBi20085GLAlzT" />
      <env name="STRIPE_API_KEY_MY" value="sk_test_51LBDj8GBiw7eM4IGl9EetqAZyX28K63bVNe7WGgxQKxgyBGWDzVvB6Pl5ZgJo9lwHkScCALGcTMbAZYuzuyfJyKp00jzoqWzw4" />
      <env name="STRIPE_API_KEY_SG" value="sk_test_51L8iI0HRmviISgVc5jfusO5bURwVRNqHWffm14fELQ1TPDJFr83PHejoQdDipVjRIphP70P6T0SuRsKgjKceStyR0089XUcIQL" />
      <env name="STRIPE_CONNECT_WEBHOOK_SECRET_GB" value="whsec_SwYlpU05IDQ6nsnEK4PP7RftHzYBzBRt" />
      <env name="STRIPE_CONNECT_WEBHOOK_SECRET_HK" value="whsec_nsHRWAGGg9a81JCV0zBUSJ6nMSVS4RLK" />
      <env name="STRIPE_CONNECT_WEBHOOK_SECRET_MY" value="whsec_O9oayfFMrhshzF03VT33rOyte85NTKLg" />
      <env name="STRIPE_CONNECT_WEBHOOK_SECRET_SG" value="whsec_jyd6U7fI6KzbjEaKU0HdSZg0TKCRmJEQ" />
      <env name="STRIPE_ONBOARDING_REDIRECT_DOMAIN" value="https://uat.sleekflow.io" />
      <env name="STRIPE_ONBOARDING_REFRESH_DOMAIN" value="https://localhost:7084" />
      <env name="STRIPE_PAYMENT_WEBHOOK_SECRET_GB" value="whsec_14NFTb4N3LY6qcHBs9OHggXLljv0sP4n" />
      <env name="STRIPE_PAYMENT_WEBHOOK_SECRET_HK" value="whsec_3IgyjBciyudjHkzy4RrbZSKzHVGs34Za" />
      <env name="STRIPE_PAYMENT_WEBHOOK_SECRET_MY" value="whsec_fsqYmYwcj60WtP5ja2J7qbUBpYQQf86N" />
      <env name="STRIPE_PAYMENT_WEBHOOK_SECRET_SG" value="whsec_aiuHjZNs27Ix2lm32NLnf3t98I957DHR" />
      <env name="WORKER_FUNCTIONS_KEY" value="PLACEHOLDER" />
      <env name="WORKER_HOSTNAME" value="http://localhost:7090" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.CommerceHub/Sleekflow.CommerceHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>