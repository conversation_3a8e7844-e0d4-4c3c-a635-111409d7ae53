using Azure;
using Azure.AI.Translation.Text;
using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.TextEnrichments;

public interface ITextTranslationService
{
    Task<string> DetectByLlmAsync(string text);

    Task<string> TranslateByAzureTranslationServiceAsync(string targetLanguageCode, string text);

    Task<string> TranslateByLlmAsync(string targetLanguageCode, string text);
}

public class TextTranslationService : ITextTranslationService, IScopedService
{
    private readonly TextTranslationClient _client;
    private readonly Kernel _kernel;
    private readonly ILogger<TextTranslationService> _logger;
    private readonly ILanguagePlugin _languagePlugin;

    public TextTranslationService(
        IAzureTextTranslatorConfig config,
        Kernel kernel,
        ILogger<TextTranslationService> logger,
        ILanguagePlugin languagePlugin)
    {
        _client = new TextTranslationClient(new AzureKeyCredential(config.Key));
        _kernel = kernel;
        _logger = logger;
        _languagePlugin = languagePlugin;
    }

    public async Task<string> TranslateByAzureTranslationServiceAsync(string targetLanguageCode, string text)
    {
        var sourceLanguageCode = await DetectByLlmBcp47Async(text);
        if (targetLanguageCode == sourceLanguageCode)
        {
            return text;
        }

        _logger.LogInformation(
            "Translating to {TargetLanguageCode} from {SourceLanguageCode}: {Text}",
            targetLanguageCode,
            sourceLanguageCode,
            text);

        try
        {
            var translatedTextItemsResponse =
                await _client.TranslateAsync(targetLanguageCode, text, sourceLanguageCode);
            var translatedTextItems = translatedTextItemsResponse.Value;
            var translatedText = translatedTextItems[0].Translations[0].Text ?? string.Empty;

            _logger.LogInformation("Translated: {TranslatedText}", translatedText);

            return translatedText;
        }
        catch (Exception e)
        {
            var translatedTextItemsResponse =
                await _client.TranslateAsync(targetLanguageCode, text);
            var translatedTextItems = translatedTextItemsResponse.Value;
            var translatedText = translatedTextItems[0].Translations[0].Text ?? string.Empty;

            _logger.LogInformation(e, "Translated: {TranslatedText}", translatedText);

            return translatedText;
        }
    }

    public async Task<string> TranslateByLlmAsync(string targetLanguageCode, string text)
    {
        return await _languagePlugin.TranslateText(_kernel, text, targetLanguageCode);
    }

    public async Task<string> DetectByLlmAsync(string text)
    {
        var detectTextLanguageResponse = await _languagePlugin.DetectTextLanguage(_kernel, text);

        return detectTextLanguageResponse.InputTextLanguageCode;
    }

    private async Task<string?> DetectByLlmBcp47Async(string text)
    {
        try
        {
            var detectTextLanguageResponse = await _languagePlugin.DetectTextLanguageBCP47(_kernel, text);
            return detectTextLanguageResponse.InputTextLanguageCode;
        }
        catch
        {
            return null;
        }
    }
}