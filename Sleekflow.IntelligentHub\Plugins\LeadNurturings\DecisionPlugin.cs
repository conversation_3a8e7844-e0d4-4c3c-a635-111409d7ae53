using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IDecisionPlugin
{
    [KernelFunction("make_decision")]
    [Description(
        "Determines the next action using data from data pane and stores decision results with structured keys for efficient workflow management.")]
    [return: Description("Success message confirming decision completed and stored in data pane.")]
    Task<string> MakeDecisionWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where classification results are stored in the data pane.")]
        string classificationKey);
}

public class DecisionPlugin : BaseLeadNurturingPlugin, IDecisionPlugin
{
    public DecisionPlugin(
        ILogger<DecisionPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel)
    {
    }

    private async Task<string> MakeDecisionAsync(
        Kernel kernel,
        string conversationContext,
        string classificationInfo)
    {
        var decisionAgent = _agentDefinitions.GetDecisionAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH_2_5, true));

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, classificationInfo);

        return await ExecuteAgentWithTelemetryAsync(decisionAgent, agentThread, "DecisionPlugin");
    }

    [KernelFunction("make_decision")]
    [Description(
        "Determines the next action using data from data pane and stores decision results with structured keys for efficient workflow management.")]
    [return: Description("Original agent response with decision details.")]
    public async Task<string> MakeDecisionWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where classification results are stored in the data pane.")]
        string classificationKey)
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.DecisionAgentResponse>(
            sessionKey,
            "Decision",
            dataRetrievalFunc: async () => await RetrieveMultipleDataAsync(
                (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext"),
                (classificationKey, AgentOutputKeys.LeadClassifierComplete, "classificationInfo")
            ),
            agentExecutionFunc: async (data) => await MakeDecisionAsync(
                _kernel,
                data["conversationContext"],
                data["classificationInfo"]),
            storageKey: _keyManager.GetDecisionKey(sessionKey),
            storageDataType: AgentOutputKeys.DecisionComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreDecisionComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task StoreDecisionComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.DecisionAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "DecisionAgent", AgentOutputKeys.DecisionReasoning),
                    AgentOutputKeys.DecisionReasoning,
                    parsedResponse.Step4Reasoning + " " + parsedResponse.Step5Reasoning);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "DecisionAgent", AgentOutputKeys.DecisionDecision),
                    AgentOutputKeys.DecisionDecision,
                    parsedResponse.Decision);

                _logger.LogInformation(
                    "Decision completed for session {SessionKey}: {Decision}",
                    sessionKey,
                    parsedResponse.Decision);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to store decision components for session {SessionKey}", sessionKey);
        }
    }
}