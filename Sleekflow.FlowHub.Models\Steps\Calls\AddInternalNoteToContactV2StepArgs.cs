﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class AddInternalNoteToContactV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.add-internal-note-to-contact";

    [Required]
    [JsonProperty("content__expr")]
    public string ContentExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Messaging;

    [JsonConstructor]
    public AddInternalNoteToContactV2StepArgs(string contentExpr)
    {
        ContentExpr = contentExpr;
    }
}