using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;

/**
 * This class stores states for the ongoing streaming send message.
 */
public class StreamingSendMessageContext
{
    [JsonProperty("new_subscribers")]
    public List<StreamingSendMessageSubscription> NewSubscribers { get; set; }

    [JsonConstructor]
    public StreamingSendMessageContext(
        List<StreamingSendMessageSubscription> newSubscribers)
    {
        NewSubscribers = newSubscribers;
    }
}