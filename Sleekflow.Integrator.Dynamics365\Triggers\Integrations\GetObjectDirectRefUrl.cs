using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetObjectDirectRefUrl : ITrigger
{
    public class GetObjectDirectRefUrlInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonConstructor]
        public GetObjectDirectRefUrlInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string objectId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
        }
    }

    public class GetObjectDirectRefUrlOutput
    {
        [JsonProperty("object_direct_ref_url")]
        [Required]
        public string ObjectDirectRefUrl { get; set; }

        [JsonConstructor]
        public GetObjectDirectRefUrlOutput(
            string objectDirectRefUrl)
        {
            ObjectDirectRefUrl = objectDirectRefUrl;
        }
    }

    public Task<GetObjectDirectRefUrlOutput> F(
        GetObjectDirectRefUrlInput getObjectDirectRefUrlInput)
    {
        throw new NotImplementedException();
    }
}