﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileSoftDeletedLog
    : ITrigger<
        CreateUserProfileSoftDeletedLog.CreateUserProfileSoftDeletedLogInput,
        CreateUserProfileSoftDeletedLog.CreateUserProfileSoftDeletedLogOutput>
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateUserProfileSoftDeletedLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateUserProfileSoftDeletedLogInput : IHasSleekflowUserProfileId
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public UserProfileSoftDeletedLogData Data { get; set; }

        [JsonConstructor]
        public CreateUserProfileSoftDeletedLogInput(
            string? sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string auditLogText,
            UserProfileSoftDeletedLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class CreateUserProfileSoftDeletedLogOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateUserProfileSoftDeletedLogOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateUserProfileSoftDeletedLogOutput> F(CreateUserProfileSoftDeletedLogInput input)
    {
        var dataStr = JsonConvert.SerializeObject(input.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var id = _idService.GetId("UserProfileAuditLog");
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
            new UserProfileAuditLog(
                id,
                (distributedInvocationContext?.SleekflowCompanyId
                 ?? input.SleekflowCompanyId)
                ?? throw new InvalidOperationException(),
                distributedInvocationContext?.SleekflowStaffId
                ?? input.SleekflowStaffId,
                input.SleekflowUserProfileId,
                UserProfileAuditLogTypes.UserProfileSoftDeleted,
                input.AuditLogText,
                data,
                DateTimeOffset.UtcNow,
                null));

        return new CreateUserProfileSoftDeletedLogOutput(id);
    }
}