using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.LineItems;

public abstract class LineItem : IHasMetadata
{
    [JsonProperty("product_variant_id")]
    public string ProductVariantId { get; set; }

    [JsonProperty("product_id")]
    public string ProductId { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("quantity")]
    public int Quantity { get; set; }

    [JsonProperty("line_item_discount")]
    public Discount? LineItemDiscount { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    protected LineItem(
        string productVariantId,
        string productId,
        string? description,
        int quantity,
        Discount? lineItemDiscount,
        Dictionary<string, object?> metadata)
    {
        ProductVariantId = productVariantId;
        ProductId = productId;
        Description = description;
        Quantity = quantity;
        LineItemDiscount = lineItemDiscount;
        Metadata = metadata;
    }
}