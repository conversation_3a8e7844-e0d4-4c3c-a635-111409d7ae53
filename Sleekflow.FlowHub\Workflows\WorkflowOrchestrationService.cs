using System.Net;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.Workers;

namespace Sleekflow.FlowHub.Workflows;

public interface IWorkflowOrchestrationService
{
    Task<DurablePayload> TriggerScheduleWorkflowAsync(
        string sleekflowCompanyId,
        string origin,
        string workflowId,
        string workflowVersionedId,
        DateTimeOffset scheduledDatetime,
        WorkflowRecurringSettings? recurringSettings,
        string workflowVersion);

    Task<bool> TerminateWorkflowDurableFunctionAsync(
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        DurablePayload durablePayload);
}

public class WorkflowOrchestrationService : IWorkflowOrchestrationService, IScopedService
{
    private readonly IWorkerService _workerService;
    private readonly IWorkerConfig _workerConfig;
    private readonly HttpClient _httpClient;
    private readonly ILogger<WorkflowOrchestrationService> _logger;

    public WorkflowOrchestrationService(
        IWorkerService workerService,
        IWorkerConfig workerConfig,
        HttpClient httpClient,
        ILogger<WorkflowOrchestrationService> logger)
    {
        _workerService = workerService;
        _workerConfig = workerConfig;
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<DurablePayload> TriggerScheduleWorkflowAsync(
        string sleekflowCompanyId,
        string origin,
        string workflowId,
        string workflowVersionedId,
        DateTimeOffset scheduledDatetime,
        WorkflowRecurringSettings? recurringSettings,
        string workflowVersion)
    {
        _logger.LogInformation(
            "TriggerScheduleWorkflowAsync called with parameters: {SleekflowCompanyId}, {Origin}, {WorkflowId}, {WorkflowVersionedId}, {ScheduledDatetime}, {recurringSettings}, {WorkflowVersion}",
            sleekflowCompanyId,
            origin,
            workflowId,
            workflowVersionedId,
            scheduledDatetime.ToString(),
            JsonConvert.SerializeObject(recurringSettings),
            workflowVersion);

        var inputJsonStr = JsonConvert.SerializeObject(
            new TriggerScheduleWorkflowInput(
                sleekflowCompanyId,
                origin,
                workflowId,
                workflowVersionedId,
                scheduledDatetime,
                recurringSettings,
                workflowVersion),
            JsonConfig.DefaultJsonSerializerSettings);
        var (_, _, output) = await _workerService.PostAsync<DurablePayload>(
            _httpClient,
            inputJsonStr,
            _workerConfig.WorkerHostname + "/api/TriggerScheduleWorkflow");

        _logger.LogInformation(
           "TriggerScheduleWorkflowAsync {WorkflowVersionedId} response: {Output}",
           workflowVersionedId,
           JsonConvert.SerializeObject(output));

        return output.Data!;
    }

    public async Task<bool> TerminateWorkflowDurableFunctionAsync(
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        DurablePayload durablePayload)
    {
        bool isTerminated;

        try
        {
            var requestMessage = new HttpRequestMessage
            {
                Method = HttpMethod.Post, RequestUri = new Uri(durablePayload.TerminatePostUri),
            };
            var resMsg = await _httpClient.SendAsync(requestMessage);
            var resStr = await resMsg.Content.ReadAsStringAsync();

            if (!resMsg.IsSuccessStatusCode
                && resMsg.StatusCode != HttpStatusCode.Gone)
            {
                throw new SfInternalErrorException(
                    $"Failed to terminate workflow version id {workflowVersionedId} durable function for company {sleekflowCompanyId}. Status code: {resMsg.StatusCode}, Response: {resStr}");
            }

            isTerminated = true;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to terminate durable function: {CompanyId} {WorkflowId} {WorkflowVersionedId}",
                sleekflowCompanyId,
                workflowId,
                workflowVersionedId);

            throw new SfInternalErrorException(
                ex,
                $"Failed to terminate workflow version id {workflowVersionedId} durable function for company {sleekflowCompanyId}");
        }

        return isTerminated;
    }
}