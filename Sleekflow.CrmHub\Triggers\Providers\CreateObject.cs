﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class CreateObject : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public CreateObject(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class CreateObjectInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("dict")]
        [ValidateObject]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("typed_ids")]
        [ValidateArray]
        public List<TypedId>? TypedIds { get; set; }

        [JsonConstructor]
        public CreateObjectInput(
            string sleekflowCompanyId,
            string providerConnectionId,
            string providerName,
            string entityTypeName,
            Dictionary<string, object?> dict,
            List<TypedId>? typedIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderConnectionId = providerConnectionId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            Dict = dict;
            TypedIds = typedIds;
        }
    }

    public class CreateObjectOutput
    {
    }

    public async Task<CreateObjectOutput> F(
        CreateObjectInput createObjectInput)
    {
        var sleekflowCompanyId = createObjectInput.SleekflowCompanyId;
        var providerConnectionId = createObjectInput.ProviderConnectionId;
        var providerName = createObjectInput.ProviderName;
        var entityTypeName = createObjectInput.EntityTypeName;
        var dict = createObjectInput.Dict;
        var typedIds = createObjectInput.TypedIds;

        var providerService = _providerSelector.GetProviderService(providerName);

        await providerService.CreateObjectV2Async(
            sleekflowCompanyId,
            providerConnectionId,
            dict,
            entityTypeName,
            typedIds);

        return new CreateObjectOutput();
    }
}