﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;
using Sleekflow.CrmHub.Models.Constants;

namespace Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;

public class DataTypeConcreteClassResolver : DefaultContractResolver
{
    protected override JsonConverter? ResolveContractConverter(Type objectType)
    {
        if (typeof(IDataType).IsAssignableFrom(objectType)
            && !objectType.IsInterface)
        {
            return null;
        }

        return base.ResolveContractConverter(objectType);
    }
}

public class DataTypeConverter : JsonConverter
{
    private static readonly JsonSerializerSettings ConcreteClassConversion =
        new JsonSerializerSettings()
        {
            ContractResolver = new DataTypeConcreteClassResolver()
        };

#pragma warning disable JA1001
    public override bool CanWrite => false;
#pragma warning restore JA1001

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        throw new NotImplementedException();
    }

    public override object ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        var jo = JObject.Load(reader);
        var name = jo.GetValue("name")!.Value<string>()!;

        switch (name)
        {
            case SchemaPropertyDataTypes.SingleLineText:
                return JsonConvert.DeserializeObject<SingleLineTextDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.Numeric:
                return JsonConvert.DeserializeObject<NumericDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.Decimal:
                return JsonConvert.DeserializeObject<DecimalDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.SingleChoice:
                return JsonConvert.DeserializeObject<SingleChoiceDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.MultipleChoice:
                return JsonConvert.DeserializeObject<MultipleChoiceDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.Boolean:
                return JsonConvert.DeserializeObject<BooleanDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.Date:
                return JsonConvert.DeserializeObject<DateDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.DateTime:
                return JsonConvert.DeserializeObject<DateTimeDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.ArrayObject:
                return JsonConvert.DeserializeObject<ArrayObjectDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            case SchemaPropertyDataTypes.Image:
                return JsonConvert.DeserializeObject<ImageDataType>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            default:
                throw new NotImplementedException();
        }
    }

    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(IDataType);
    }
}