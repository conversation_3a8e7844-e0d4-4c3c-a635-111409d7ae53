using System.Web;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;
using Sleekflow.Integrator.Zoho.UserMappingConfigs;
using Sleekflow.Mvc.SwaggerConfiguration.Parameter;
using Sleekflow.Utils;

namespace Sleekflow.Integrator.Zoho.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class PublicController : ControllerBase
{
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IZohoObjectService _zohoObjectService;
    private readonly ILogger<PublicController> _logger;
    private readonly IZohoUserMappingConfigService _zohoUserMappingConfigService;

    public PublicController(
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoConnectionService zohoConnectionService,
        IZohoObjectService zohoObjectService,
        ILogger<PublicController> logger,
        IZohoUserMappingConfigService zohoUserMappingConfigService)
    {
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
        _zohoObjectService = zohoObjectService;
        _logger = logger;
        _zohoUserMappingConfigService = zohoUserMappingConfigService;
    }

    [SwaggerQuery(
        new[]
        {
            "code",
            "state",
            "location",
            "accounts-server"
        })]
    [Route("AuthenticateCallback")]
    [HttpGet]
    public async Task<IActionResult> AuthenticateCallback()
    {
        var code = HttpContext.Request.Query["code"];
        var encryptedState = HttpContext.Request.Query["state"];

        var (authentication, successUrl, failureUrl, isSandbox) =
            await _zohoAuthenticationService.HandleAuthenticateCallbackAndStoreAsync(
                code!,
                encryptedState!);

        try
        {
            if (authentication == null)
            {
                throw new Exception("Unable to handle the authentication callback");
            }

            var connection = await _zohoConnectionService.GetByAuthenticationIdAsync(
                    authentication.SleekflowCompanyId,
                    authentication.Id);

            var organizationName = await _zohoObjectService.GetOrganizationNameAsync(authentication);
            var organizationId = await _zohoObjectService.GetOrganizationIdAsync(authentication);

            if (connection is null)
            {
                connection = await _zohoConnectionService.CreateAndGetAsync(
                    authentication.SleekflowCompanyId,
                    organizationId,
                    authentication.Id,
                    organizationName,
                    isSandbox ? "sandbox" : "production",
                    true);

                await _zohoUserMappingConfigService.CreateAndGetAsync(
                    authentication.SleekflowCompanyId,
                    connection.Id,
                    new List<UserMapping>());
            }
            else
            {
                await _zohoConnectionService.PatchAsync(
                    connection.Id,
                    authentication.SleekflowCompanyId,
                    organizationName,
                    true);
            }

            string encodedSuccessUrl;

            var successUri = new Uri(successUrl);

            var existingQueryStrings = HttpUtility.ParseQueryString(successUri.Query);
            if (existingQueryStrings.Count == 0)
            {
                encodedSuccessUrl = successUrl + $"?connection_id={HttpUtility.HtmlEncode(connection.Id)}";
            }
            else
            {
                var queryStrings = existingQueryStrings;
                queryStrings["connection_id"] = HttpUtility.HtmlEncode(connection.Id);

                encodedSuccessUrl = successUri.GetLeftPart(UriPartial.Path) + "?" + queryStrings;
            }

            return new ContentResult
            {
                ContentType = "text/html",
                Content = $"<meta http-equiv=\"refresh\" content=\"0;URL='{encodedSuccessUrl}'\" />"
            };
        }
        catch (Exception exception)
        {
            var gen = RandomStringUtils.Gen(10);

            _logger.LogError(exception, "Caught an exception. requestId {RequestId}", gen);

            return new ContentResult
            {
                ContentType = "text/html",
                Content = "<meta http-equiv=\"refresh\" content=\"0;URL='" + failureUrl + "'\" />"
            };
        }
    }
}