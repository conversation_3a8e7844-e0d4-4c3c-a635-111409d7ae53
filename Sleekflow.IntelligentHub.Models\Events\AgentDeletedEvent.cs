using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Events;

public class AgentDeletedEvent : IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("agent_id")]
    public string AgentId { get; set; }

    [JsonConstructor]
    public AgentDeletedEvent(string sleekflowCompanyId, string agentId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        AgentId = agentId;
    }
}