using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class SleekflowUserProfileIdAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1005";
    public const string Category = "Design";

    private static readonly LocalizableString Title = "Class should implement IHasSleekflowUserProfileId";

    private static readonly LocalizableString MessageFormat =
        "Class '{0}' has a '{1}' property but does not implement the corresponding interface";

    private static readonly LocalizableString Description =
        "If a class has a SleekflowUserProfileId property, it should implement the IHasSleekflowUserProfileId interface.";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSymbolAction(AnalyzeSymbol, SymbolKind.NamedType);
    }

    private static void AnalyzeSymbol(SymbolAnalysisContext context)
    {
        var namedTypeSymbol = (INamedTypeSymbol) context.Symbol;

        // Check if the class has a SleekflowUserProfileId property
        var sleekflowUserProfileIdProperty = namedTypeSymbol.GetMembers()
            .FirstOrDefault(m => m.Kind == SymbolKind.Property && m.Name == "SleekflowUserProfileId");
        if (sleekflowUserProfileIdProperty == null)
        {
            return;
        }

        // Check if the class already implements IHasSleekflowUserProfileId
        var iHasSleekflowUserProfileIdInterface = namedTypeSymbol.Interfaces.FirstOrDefault(
            i => i.ToDisplayString() == "Sleekflow.Persistence.Abstractions.IHasSleekflowUserProfileId");
        if (iHasSleekflowUserProfileIdInterface != null)
        {
            return;
        }

        // Create a diagnostic and report it
        var sleekflowUserProfileIdDiagnostic = Diagnostic.Create(
            Rule,
            namedTypeSymbol.Locations[0],
            namedTypeSymbol.Name,
            "SleekflowUserProfileId");
        context.ReportDiagnostic(sleekflowUserProfileIdDiagnostic);
    }
}