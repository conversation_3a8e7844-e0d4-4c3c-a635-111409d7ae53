﻿using Microsoft.Extensions.Logging;
using Sleekflow.Constants;
using Sleekflow.RateLimits.LuaScripts;
using Sleekflow.RateLimits.RateLimiters;
using Sleekflow.RateLimits.ThrottledConsumers;
using Sleekflow.Utils;

namespace Sleekflow.Events.ServiceBus;

public interface ISlidingWindowNextAvailableSlotRateLimiter : IServiceBusManager
{
    Task PublishWithRateLimitAsync<T>(
        T @event,
        string keyName,
        SlidingWindowNextAvailableSlotParam slidingWindowNextAvailableSlotParam,
        CancellationToken cancellationToken = default)
        where T : class;
}

public class ServiceBusManagerWithRateLimit : ServiceBusManager, ISlidingWindowNextAvailableSlotRateLimiter
{
    private const string RateLimitAlgorithm = RateLimitAlgorithms.SlidingWindowNextAvailableSlot;
    private const int TimeDifferenceToleranceInMs = 1000;
    private const int TimeRoundUpInSecond = 5;
    private readonly IRateLimiterFactory _rateLimiterFactory;
    private readonly ILogger<ServiceBusManagerWithRateLimit> _logger;

    public ServiceBusManagerWithRateLimit(
        IServiceBusProvider serviceBusProvider,
        IRateLimiterFactory rateLimiterFactory,
        ILogger<ServiceBusManagerWithRateLimit> logger)
        : base(serviceBusProvider)
    {
        _rateLimiterFactory = rateLimiterFactory;
        _logger = logger;
    }

    public async Task PublishWithRateLimitAsync<T>(
        T @event,
        string keyName,
        SlidingWindowNextAvailableSlotParam slidingWindowNextAvailableSlotParam,
        CancellationToken cancellationToken = default)
        where T : class
    {
        var rateLimiter =
            (SlidingWindowNextAvailableSlotRateLimiter) _rateLimiterFactory.CreateRateLimiter(RateLimitAlgorithm);
        var (nextAvailableSlot, remainCount) =
            await rateLimiter.GetNextAvailableSlotAsync(keyName, slidingWindowNextAvailableSlotParam);

        var monitorLimits = new List<int>()
        {
            10000,
            5000,
            2000,
            1000,
            500,
            200,
            100,
        };

        var limitMonitor = new LimitMonitor(
            remainCount,
            slidingWindowNextAvailableSlotParam.SlidingWindowParam.MaxRequestsAllowedWithinWindow,
            monitorLimits);

        if (limitMonitor.ReachLimit() > 0)
        {
            _logger.LogInformation(
                "[Event Rate Limit Hit] {EventName} reached limit: {Limit}",
                typeof(T).Name,
                limitMonitor.ReachLimit());
        }

        var difference = Math.Abs(
            nextAvailableSlot.ToUnixTimeMilliseconds() -
            slidingWindowNextAvailableSlotParam.EarliestDesiredPublishTimeMs);
        if (difference > TimeDifferenceToleranceInMs)
        {
            _logger.LogInformation(
                "[Event Rescheduled] {EventName} is rescheduled from {OriginalTime} to {ScheduledTime}, difference {Difference}",
                typeof(T).Name,
                slidingWindowNextAvailableSlotParam.EarliestDesiredPublishTimeMs,
                nextAvailableSlot.ToUnixTimeMilliseconds(),
                difference);

            // Reschedule the event in batch
            await PublishAsync(
                @event,
                DateTimeOffsetUtils.RoundUp(nextAvailableSlot, TimeRoundUpInSecond),
                cancellationToken);
        }
        else
        {
            // non-throttle event
            await PublishAsync(@event, cancellationToken);
        }
    }
}