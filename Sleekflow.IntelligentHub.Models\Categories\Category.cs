using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Categories;

public class Category : IHasMetadata
{
    [JsonProperty("category_name")]
    public string CategoryName { get; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public Category(string categoryName, Dictionary<string, object?> metadata)
    {
        CategoryName = categoryName;
        Metadata = metadata;
    }
}