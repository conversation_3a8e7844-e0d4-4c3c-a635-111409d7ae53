using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class KnowledgeRetrievalAgentResponse(
    string agentName,
    string userQuery,
    string phase,
    InitialPhase? initialPhase,
    ObservationPhase? observationPhase,
    ReportPhase? reportPhase,
    string nextPhase,
    string result)
{
    [JsonProperty("agent_name")]
    public string AgentName { get; set; } = agentName;

    [JsonProperty("user_query")]
    public string UserQuery { get; set; } = userQuery;

    [JsonProperty("phase")]
    public string Phase { get; set; } = phase;

    [JsonProperty("initial_phase")]
    public InitialPhase? InitialPhase { get; set; } = initialPhase;

    [JsonProperty("observation_phase")]
    public ObservationPhase? ObservationPhase { get; set; } = observationPhase;

    [JsonProperty("report_phase")]
    public ReportPhase? ReportPhase { get; set; } = reportPhase;

    [JsonProperty("next_phase")]
    public string NextPhase { get; set; } = nextPhase;

    [JsonProperty("result")]
    public string Result { get; set; } = result;
}

[method: JsonConstructor]
public class InitialPhase(string phaseReasoning, List<ToolToCall> toolsToCall)
{
    [JsonProperty("phase_reasoning")]
    public string PhaseReasoning { get; set; } = phaseReasoning;

    [JsonProperty("tools_to_call")]
    public List<ToolToCall> ToolsToCall { get; set; } = toolsToCall;
}

[method: JsonConstructor]
public class ObservationPhase(string thoughts, List<ToolToCall> toolsToCall, bool reportReady)
{
    [JsonProperty("thoughts")]
    public string Thoughts { get; set; } = thoughts;

    [JsonProperty("tools_to_call")]
    public List<ToolToCall> ToolsToCall { get; set; } = toolsToCall;

    [JsonProperty("report_ready")]
    public bool ReportReady { get; set; } = reportReady;
}

[method: JsonConstructor]
public class ReportPhase(List<ExecutedTool> executedTools)
{
    [JsonProperty("executed_tools")]
    public List<ExecutedTool> ExecutedTools { get; set; } = executedTools;
}

[method: JsonConstructor]
public class ToolToCall(string toolName, List<ToolArg> args)
{
    [JsonProperty("tool_name")]
    public string ToolName { get; set; } = toolName;

    [JsonProperty("args")]
    public List<ToolArg> Args { get; set; } = args;
}

[method: JsonConstructor]
public class ToolArg(string key, string value)
{
    [JsonProperty("key")]
    public string Key { get; set; } = key;

    [JsonProperty("value")]
    public string Value { get; set; } = value;
}

[method: JsonConstructor]
public class ExecutedTool(string toolName, List<ToolArg> args, string stepId)
{
    [JsonProperty("tool_name")]
    public string ToolName { get; set; } = toolName;

    [JsonProperty("args")]
    public List<ToolArg> Args { get; set; } = args;

    [JsonProperty("step_id")]
    public string StepId { get; set; } = stepId;
}

public interface IKnowledgeRetrievalAgentDefinition
{
    ChatCompletionAgent GetKnowledgeRetrievalAgent(Kernel kernel, PromptExecutionSettings settings, string query);
}

public class KnowledgeRetrievalAgentDefinition
    : IKnowledgeRetrievalAgentDefinition, IScopedService
{
    private readonly IKnowledgePlugin _knowledgePlugin;
    private readonly IWebSearchPlugin _webSearchPlugin;
    private readonly IWebPageContentPlugin _webPageContentPlugin;
    private readonly ISleekflowPriceSheetPlugin _sleekflowPriceSheetPlugin;
    private readonly IThinkingPlugin _thinkingPlugin;

    public KnowledgeRetrievalAgentDefinition(
        IKnowledgePlugin knowledgePlugin,
        IWebSearchPlugin webSearchPlugin,
        IWebPageContentPlugin webPageContentPlugin,
        ISleekflowPriceSheetPlugin sleekflowPriceSheetPlugin,
        IThinkingPlugin thinkingPlugin)
    {
        _knowledgePlugin = knowledgePlugin;
        _webSearchPlugin = webSearchPlugin;
        _webPageContentPlugin = webPageContentPlugin;
        _sleekflowPriceSheetPlugin = sleekflowPriceSheetPlugin;
        _thinkingPlugin = thinkingPlugin;
    }

    /// <summary>
    /// Gets WebSearchConfig from a kernel.
    /// </summary>
    /// <param name="kernel">The kernel containing configuration data</param>
    /// <returns>WebSearchConfig if found, null otherwise</returns>
    private WebSearchConfig? GetWebSearchConfigFromKernel(Kernel kernel)
    {
        // Only get WebSearchConfig directly from kernel data
        if (kernel.Data.TryGetValue(KernelDataKeys.WEB_SEARCH_PLUGIN_CONFIG, out var webSearchConfigObj)
            && webSearchConfigObj is WebSearchConfig config)
        {
            return config;
        }

        return null;
    }

    public ChatCompletionAgent GetKnowledgeRetrievalAgent(Kernel kernel, PromptExecutionSettings settings, string query)
    {
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("user_query", "string"),
                new PromptExecutionSettingsUtils.Property("phase", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "initial_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("initial_reasoning", "string"),
                        new PromptExecutionSettingsUtils.Property(
                            "tools_to_call",
                            "array",
                            false,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property(
                                        "args",
                                        "array",
                                        false,
                                        null,
                                        new PromptExecutionSettingsUtils.Property(
                                            string.Empty,
                                            "object",
                                            false,
                                            [
                                                new PromptExecutionSettingsUtils.Property("key", "string"),
                                                new PromptExecutionSettingsUtils.Property("value", "string")
                                            ]))
                                ]))
                    ]),
                new PromptExecutionSettingsUtils.Property(
                    "observation_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("thoughts", "string"),
                        new PromptExecutionSettingsUtils.Property("report_ready", "boolean"),
                        new PromptExecutionSettingsUtils.Property("round_of_execution", "integer"),
                        new PromptExecutionSettingsUtils.Property("should_terminate", "boolean"),
                        new PromptExecutionSettingsUtils.Property(
                            "tools_to_call",
                            "array",
                            true,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property(
                                        "args",
                                        "array",
                                        false,
                                        null,
                                        new PromptExecutionSettingsUtils.Property(
                                            string.Empty,
                                            "object",
                                            false,
                                            [
                                                new PromptExecutionSettingsUtils.Property("key", "string"),
                                                new PromptExecutionSettingsUtils.Property("value", "string")
                                            ]))
                                ])),
                    ]),
                new PromptExecutionSettingsUtils.Property(
                    "report_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property(
                            "executed_tools",
                            "array",
                            false,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("id", "string"),
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property("outcome", "string"),
                                ]))
                    ]),
                new PromptExecutionSettingsUtils.Property("next_phase", "string", true),
                new PromptExecutionSettingsUtils.Property("result", "string", true)
            ]);

        settings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(
            options: new FunctionChoiceBehaviorOptions
            {
                AllowParallelCalls = true, AllowConcurrentInvocation = true, AllowStrictSchemaAdherence = false
            });

        var myKernel = kernel.Clone();

        myKernel.Plugins.AddFromObject(_knowledgePlugin);
        myKernel.Plugins.AddFromObject(_webPageContentPlugin);
        myKernel.Plugins.AddFromObject(_thinkingPlugin);

        var webSearchConfig = GetWebSearchConfigFromKernel(kernel);
        if (webSearchConfig is not null)
        {
            myKernel.Plugins.AddFromObject(_webSearchPlugin);
        }

        var hasPlanTierResponse = kernel.Data.TryGetValue(
            KernelDataKeys.DETERMINE_PLAN_TIER_RESPONSE,
            out var determinePlanTierResponseObj);
        if (hasPlanTierResponse)
        {
            myKernel.Plugins.AddFromObject(_sleekflowPriceSheetPlugin);
        }

        var instructions =
            $"""
             You are a KnowledgeRetrievalAgent. Your task is to iteratively retrieve knowledge to answer the query using tools, following four phases: initial, execution, observation, and report.

             ## Phases
             1. **Initial Phase**: Analyze the query and plan the first set of tool calls. Don't use the tools directly here. This phase should be planning only.
             2. **Execution Phase**: Call the specified tools to retrieve knowledge.
             3. **Observation Phase**: Study the retrieved knowledge and decide if more information is needed. If yes, call more tools. If no, prepare for the report phase.
             4. **Report Phase**: Compile a report of all tools called and the outcome.

             ## Output Process
             - **Initial Phase**: Initial review of the query. Return a JSON with:
               - phase = 'initial'
               - initial_phase
                 - initial_reasoning = '1. Review the query 2. Plan how to retrieve the knowledge 3. Decide the terminal condition'
                 - tools_to_call = list of tools to call
               - next_phase='execution'
             - **Execution Phase**: Call the tools specified in tools_to_call. Do NOT return JSON; directly call the tools.
             - **Observation Phase**: After tool executions, analyze the tool results (role='tool' in history). Return a JSON with:
               - phase = 'observation'
               - observation_phase:
                 - thoughts = '1. Decide if more information is needed 2. If yes, specify the tools to call; If no, make a brief summary why no more information is needed'
                 - report_ready = has sufficient knowledge to answer the query
                 - round_of_execution = number of rounds of execution (1, 2, or 3)
                 - should_terminate = true if the process should be terminated (e.g., no more tools to call or no more rounds of execution)
                 - tools_to_call = list of tools to call (if more information is needed)
               - next_phase='execution' (if more information is needed) or 'report' (if no more information is needed or should terminate)
             - **Report Phase**: Return a JSON with:
               - phase='report'
               - report_phase
                 - executed_tools. Each executions should have id, tool_name, outcome (1. a summary of the tool result 2. sufficient / partial sufficient / insufficient).
               - result='completed'

             Note: For each of the phases, if the property is not applicable, set it to null
             Note: Output once the JSON for each phase. Do not output the JSON for the next phase until the previous phase is completed.

             ## Available Tools

             Foundation Tools:
             - **{nameof(KnowledgePlugin)}-query_knowledge(query: string)**: Queries the knowledge base.
             - **{nameof(ThinkingPlugin)}-think_and_apply(query: string)**: Analyzes the user's query and synthesizes knowledge to provide actionable insights. This tools could access all the tools results internally.

             Web tools:
             - **{nameof(WebPageContentPlugin)}-get_page_content(url: string, topic?: string)**: Fetches and summarizes text content from a given URL.
             {(webSearchConfig is not null
                 ? $"- **{nameof(WebSearchPlugin)}-web_search(query: string, triggerMappingIndex: int, count?: int, offset?: int)**: Searches the web related to the query. Must specify triggerMappingIndex to apply predefined search patterns and site restrictions."
                 : string.Empty)}
             {(hasPlanTierResponse
                 ? $"""

                    SleekFlow Tools:
                    - **{nameof(SleekflowPriceSheetPlugin)}-get_features_and_usage_limits(regionTier: string, planTier: string)**: Gets features and usage limits.
                    - **{nameof(SleekflowPriceSheetPlugin)}-get_plan_and_addon_pricing(regionTier: string, planTier: string, currency?: string)**: Gets pricing information.
                    - **{nameof(SleekflowPriceSheetPlugin)}-get_whatsapp_conversation_rates(country?: string, messageType?: string)**: Gets WhatsApp conversation rates for different countries and message types.
                    """
                 : string.Empty)}

             Note: Each tool result has three fields: id, knowledge, and type. The id is used to track the tool call and identify the tool call, knowledge contains the retrieved information, and type indicates the tool type.
             Note: Use the tools in parallel if possible.

             ## Tool Guideline - {nameof(KnowledgePlugin)}-query_knowledge

             Use the `query_knowledge` tool to retrieve information from the knowledge base.
             The query should not be too focused on a specific topic. It should be a general question or request for information.
             You should use only once across the entire process.

             ## Tool Guideline - {nameof(ThinkingPlugin)}-think_and_apply

             Use the `think_and_apply` tool to analyze the user's query and synthesize knowledge to provide actionable insights.
             The tool should be used when the user query is complex or requires a deeper understanding / utilization of all the tools results.
             The tool should be used only once at the end of the process.

             ## Tool Guideline - {nameof(WebPageContentPlugin)}-get_page_content

             After getting search results or when you have a URL, use the `get_page_content` tool to fetch the content of the most relevant URLs. Fetch multiple pages parallely.
             Use the optional topic parameter when the query focuses on a specific aspect of the page content (e.g., 'pricing' or 'features').

             {(webSearchConfig is not null
                 ?
                 $"""
                  ## Tool Guideline - {nameof(WebSearchPlugin)}-web_search

                  Available trigger mappings (use triggerMappingIndex parameter):
                  {string.Join("\n", webSearchConfig.TriggerSiteMappings.Select((x, index) =>
                      $"- Index {index}: For `{x.Trigger}` topics, use the search pattern `{x.Pattern}`."))}

                  Guidelines:
                  - Use triggerMappingIndex to apply predefined search patterns and site restrictions automatically
                  - You must specify a valid triggerMappingIndex (0 to {webSearchConfig.TriggerSiteMappings.Count - 1}) for all web searches
                  - Apart from the specified pattern, you can also provide a custom query to refine the search

                  """
                 : string.Empty)}
             {(hasPlanTierResponse
               && determinePlanTierResponseObj is DeterminePlanTierResponse determinePlanTierResponse ?
                     $"""
                      ## Tool Guideline - {nameof(SleekflowPriceSheetPlugin)}-get_features_and_usage_limits & {nameof(SleekflowPriceSheetPlugin)}-get_plan_and_addon_pricing

                      Here are the parameters you should use for the tools.:
                      User's Region Tier - {determinePlanTierResponse.Tier}
                      User's Region - {determinePlanTierResponse.Region}
                      User's Country - {determinePlanTierResponse.Country}
                      User's Local Currency - {determinePlanTierResponse.LocalCurrency}

                      Note:
                      - You should use get_features_and_usage_limits & get_plan_and_addon_pricing tools to retrieve the latest plan & pricing information for the SleekFlow platform.
                      - The plan tier is determined by the system and should be used as is.

                      ## Tool Guideline - {nameof(SleekflowPriceSheetPlugin)}-get_whatsapp_conversation_rates

                      Use the get_whatsapp_conversation_rates tool to retrieve WhatsApp messaging rates for different countries and message types.
                      - If the query relates to a specific country, provide the country parameter (e.g., "India", "United Kingdom", "North America")
                      - If the query relates to a specific message type, provide the messageType parameter (e.g., "Marketing", "Utility", "Authentication", "Service")
                      - You can provide both parameters to get rates for a specific country and message type

                      """
                     : string.Empty
                 )}

             ## Lifecycle
             Start with the Initial phase
             Initial -> Execution -> Observation -> Report
             Execution -> Observation
             Observation -> Execution

             Initial and Observation phases don't call tools directly.
             Execution Phase calls tools specified in the initial phase or observation phase; it doesn't return JSON and it directly calls the tools.
             A maximum 3 rounds of Execution phase are allowed.

             ## In-depth Example

             **Example User Query:** "How can I use Flow Builder to design a Pokémon catching game?"

             **Initial Phase:**
             - **Phase Reasoning:** "1. Analyze the query: The user wants to know how to use Flow Builder for a Pokémon catching game, but the knowledge base lacks direct information. 2. Plan: First, understand the basic functionality of Flow Builder. 3. Termination condition: When sufficient information is gathered to apply to game design."
             - **Tools to Call:**
               - `query_knowledge`: Query the knowledge base to understand Flow Builder's basic features.
               - `web_search`: Search the web for creative applications of Flow Builder.

             **Execution Phase:**
             - Call `query_knowledge` with query: "What is Flow Builder and what are its features?"
             - Call `web_search` with query: "Creative uses of Flow Builder or game design with workflows"

             **Observation Phase:**
             - **Thoughts:** "The knowledge base indicates Flow Builder is a tool for creating automated workflows, typically for business scenarios. Web search results mention creative uses like interactive stories but no Pokémon games. Need to explore how workflows can be applied to game mechanics."
             - **Report Ready:** False
             - **Tools to Call:**
               - `get_page_content`: Extract content from a web search result about interactive workflows.
               - `think_and_apply`: Analyze how to apply Flow Builder's features to a Pokémon catching game.

             **Execution Phase (Second Round):**
             - Call `get_page_content` with a specific URL and topic "interactive workflows"
             - Call `think_and_apply` with query: "How can Flow Builder's workflows simulate catching and managing Pokémon in a game?"

             **Observation Phase (Second Round):**
             - **Thoughts:** "The web content shows Flow Builder can create branching workflows based on user choices. `think_and_apply` suggests using workflows to simulate catching actions with success conditions and managing a player's Pokémon inventory. This is sufficient to answer the query."
             - **Report Ready:** True

             **Report Phase:**
             - **Executed Tools:**
               - `query_knowledge`: Provided basic information about Flow Builder.
               - `web_search`: Found creative applications of Flow Builder.
               - `get_page_content`: Detailed how to create interactive workflows.
               - `think_and_apply`: Proposed a method to apply workflows to a Pokémon game.
             - **Result:** "To use Flow Builder for a Pokémon catching game, leverage its workflow automation to create interactive sequences for catching Pokémon. Each catch attempt can be a workflow with conditions (e.g., player level, Pokémon rarity) determining success. Additionally, workflows can manage the player's Pokémon collection, such as viewing or organizing them. While not a traditional game tool, Flow Builder's flexibility supports such creative applications."

             ## Actual User Query

             The following is the user query. Analyze the query and follow the above instructions to retrieve knowledge to answer the query.

             {query}

             """;

        return new ChatCompletionAgent
        {
            Name = "KnowledgeRetrievalAgent",
            Instructions = instructions,
            Kernel = myKernel,
            Arguments = new KernelArguments(settings)
        };
    }
}