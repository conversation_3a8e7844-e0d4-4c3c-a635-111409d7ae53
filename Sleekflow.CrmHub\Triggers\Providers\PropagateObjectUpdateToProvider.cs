﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.AuditLogs;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.CrmHub.Unifies;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup(TriggerGroups.Providers)]
public class PropagateObjectUpdateToProvider : ITrigger
{
    private readonly ILogger<PropagateObjectUpdateToProvider> _logger;
    private readonly IEntityContextService _entityContextService;
    private readonly IUnifyService _unifyService;
    private readonly IProviderSelector _providerSelector;
    private readonly IEntityService _entityService;
    private readonly IProviderConfigService _providerConfigService;
    private readonly IAuditLogService _auditLogService;

    public PropagateObjectUpdateToProvider(
        ILogger<PropagateObjectUpdateToProvider> logger,
        IEntityContextService entityContextService,
        IUnifyService unifyService,
        IProviderSelector providerSelector,
        IEntityService entityService,
        IProviderConfigService providerConfigService,
        IAuditLogService auditLogService)
    {
        _logger = logger;
        _entityContextService = entityContextService;
        _unifyService = unifyService;
        _providerSelector = providerSelector;
        _entityService = entityService;
        _providerConfigService = providerConfigService;
        _auditLogService = auditLogService;
    }

    public class PropagateObjectUpdateToProviderInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonConstructor]
        public PropagateObjectUpdateToProviderInput(
            string sleekflowCompanyId,
            string providerName,
            string entityTypeName,
            string objectId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            EntityTypeName = entityTypeName;
            ObjectId = objectId;
        }
    }

    public class PropagateObjectUpdateToProviderOutput
    {
        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonConstructor]
        public PropagateObjectUpdateToProviderOutput(
            string objectId)
        {
            ObjectId = objectId;
        }
    }

    public async Task<PropagateObjectUpdateToProviderOutput> F(
        PropagateObjectUpdateToProviderInput propagateObjectUpdateToProviderInput)
    {
        var providerName = propagateObjectUpdateToProviderInput.ProviderName;
        var entityTypeName = propagateObjectUpdateToProviderInput.EntityTypeName;
        var sleekflowCompanyId = propagateObjectUpdateToProviderInput.SleekflowCompanyId;
        var objectId = propagateObjectUpdateToProviderInput.ObjectId;
        var providerConfig = await _providerConfigService.GetProviderConfigAsync(sleekflowCompanyId, providerName);

        if (providerConfig.ProviderLimit != null
            && providerConfig.ProviderLimit.IsReadOnlyIntegration)
        {
            throw new SfReadOnlyIntegrationException("The object is read only.");
        }

        var providerService = _providerSelector.GetProviderService(
            propagateObjectUpdateToProviderInput.ProviderName);
        var (providerObjectId, providerDict) = await GetProviderDictAsync(
            objectId,
            sleekflowCompanyId,
            entityTypeName,
            providerService,
            providerName);

        try
        {
            string objectOperation;

            if (providerObjectId == null)
            {
                var objectOutput = await providerService.CreateObjectAsync(
                    sleekflowCompanyId,
                    providerDict,
                    entityTypeName,
                    objectId);

                objectOperation = objectOutput.IsAsyncOperation
                    ? ObjectOperations.AsyncCreateObjectOperation
                    : ObjectOperations.CreateObjectOperation;
            }
            else
            {
                var objectOutput = await providerService.UpdateObjectAsync(
                    sleekflowCompanyId,
                    providerObjectId,
                    providerDict,
                    entityTypeName,
                    objectId);

                objectOperation = objectOutput.IsAsyncOperation
                    ? ObjectOperations.AsyncUpdateObjectOperation
                    : ObjectOperations.UpdateObjectOperation;
            }

            // Log to AuditLog
            if (objectOperation == ObjectOperations.AsyncCreateObjectOperation
                || objectOperation == ObjectOperations.AsyncUpdateObjectOperation)
            {
                await _auditLogService.CreateAuditLogAsync(
                    sleekflowCompanyId,
                    AuditLogTypes.PropagatingObjectUpdateToProvider,
                    providerDict,
                    entityTypeName,
                    objectId,
                    providerName,
                    objectOperation);
            }
            else if (objectOperation == ObjectOperations.CreateObjectOperation
                     || objectOperation == ObjectOperations.UpdateObjectOperation)
            {
                await _auditLogService.CreateAuditLogAsync(
                    sleekflowCompanyId,
                    AuditLogTypes.PropagatedObjectUpdateToProvider,
                    providerDict,
                    entityTypeName,
                    objectId,
                    providerName,
                    objectOperation);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Unable to propagate an object update to provider for dict {Dict}, entityTypeName {EntityTypeName}, providerObjectId {ProviderObjectId}",
                providerDict,
                entityTypeName,
                providerObjectId);

            await _auditLogService.CreateAuditLogAsync(
                sleekflowCompanyId,
                AuditLogTypes.PropagateObjectUpdateToProviderFailure,
                providerDict,
                entityTypeName,
                objectId,
                providerName,
                providerObjectId == null
                    ? ObjectOperations.CreateObjectOperation
                    : ObjectOperations.UpdateObjectOperation);
        }

        return new PropagateObjectUpdateToProviderOutput(
            objectId);
    }

    private async Task<(string? ProviderObjectId, Dictionary<string, object?> ProviderDict)> GetProviderDictAsync(
        string objectId,
        string sleekflowCompanyId,
        string entityTypeName,
        IProviderService providerService,
        string providerName)
    {
        var entityContext = await _entityContextService.GetEntityContextAsync(
            objectId,
            sleekflowCompanyId,
            entityTypeName);

        if (entityContext == null)
        {
            throw new SfUserFriendlyException("The object does not exist");
        }

        var completeDict = await _entityService.GetAsync(entityContext.Id, sleekflowCompanyId, entityTypeName);
        var providerObjectId = await providerService.ResolveObjectIdAsync(completeDict, entityTypeName);

        var flattenedUnifyRules = await _unifyService.GetFlattenedUnifyRules(
            sleekflowCompanyId,
            entityTypeName);
        var providerFlattenedUnifyRules = flattenedUnifyRules
            .Where(rur => rur.ProviderFieldName.StartsWith($"{providerName}:"))
            .ToList();

        var providerDict = MapCompleteDictToProviderDict(providerFlattenedUnifyRules, completeDict, providerName);

        return (providerObjectId, providerDict);
    }

    private static Dictionary<string, object?> MapCompleteDictToProviderDict(
        List<FlattenedUnifyRule> providerFlattenedUnifyRules,
        CrmHubEntity completeDict,
        string providerName)
    {
        var dict = new Dictionary<string, object?>();

        foreach (var unifyRule in providerFlattenedUnifyRules)
        {
            var unifiedFieldName = $"{UnifyService.UnifiedNamespaceName}:{unifyRule.FieldName}";
            var isMissingUnifiedField = !completeDict.ContainsKey(unifiedFieldName);
            if (isMissingUnifiedField)
            {
                continue;
            }

            var fieldValue = (JObject?) completeDict[unifiedFieldName];
            if (fieldValue == null)
            {
                dict[unifyRule.ProviderFieldName.Replace($"{providerName}:", string.Empty)] = null;
                continue;
            }

            var snapshottedValue = SnapshottedValue.FromObject(fieldValue)!;

            dict[unifyRule.ProviderFieldName.Replace($"{providerName}:", string.Empty)] = snapshottedValue.Value;
        }

        return dict;
    }
}