using System.Net;
using System.Text;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using Newtonsoft.Json;
using Polly;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.FlowHub.Workers.Triggers.Activities;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.Workers
{
    [TestFixture]
    public class GetContactsByBatchActivityTests // No direct IDisposable needed on test class itself
    {
        private Mock<ISleekflowCoreConfig> _mockCoreConfig;
        private Mock<IHttpClientFactory> _mockHttpClientFactory;
        private Mock<HttpMessageHandler> _mockHttpMessageHandler;
        private HttpClient _httpClient; // Field to hold the HttpClient instance
        private Mock<ILogger<GetContactsByBatch>> _mockLogger;
        private Mock<IAsyncPolicy<HttpResponseMessage>> _mockRetryPolicy;
        private GetContactsByBatch _activity;

        private const string CoreInternalsKey = "O19dnDlZhSZkZ5TigIXiolCTqSl461kyBCotXGOJGUMA6eiHfyxRQVwQTFN5qMr1";
        private const string Origin = "https://sleekflow-core-app-eas-dev.azurewebsites.net";
        private const string ExpectedApiPath = "/FlowHub/Internals/Functions/GetContactsByBatch";


        [SetUp]
        public void SetUp()
        {
            _mockCoreConfig = new Mock<ISleekflowCoreConfig>();
            _mockHttpClientFactory = new Mock<IHttpClientFactory>();
            _mockHttpMessageHandler = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_mockHttpMessageHandler.Object); // Assign to field
            _mockLogger = new Mock<ILogger<GetContactsByBatch>>();
            _mockRetryPolicy = new Mock<IAsyncPolicy<HttpResponseMessage>>();

            _mockHttpClientFactory.Setup(f => f.CreateClient("default-handler")).Returns(_httpClient);
            _mockCoreConfig.Setup(c => c.CoreInternalsKey).Returns(CoreInternalsKey);

            _activity = new GetContactsByBatch(
                _mockCoreConfig.Object,
                _mockHttpClientFactory.Object,
                _mockLogger.Object,
                _mockRetryPolicy.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _httpClient?.Dispose(); // Dispose the HttpClient instance after each test
        }

        // Helper to mock the Polly policy execution
        private void SetupMockPolicyExecution()
        {
            _mockRetryPolicy.Setup(
                    p => p.ExecuteAsync(
                        It.IsAny<Func<Context, Task<HttpResponseMessage>>>(),
                        It.IsAny<Context>()))
                .Returns<Func<Context, Task<HttpResponseMessage>>, Context>(
                    (action, context) => action(context));
        }


        [Test]
        public async Task RunAsync_FirstBatch_CallsApiCorrectlyAndReturnsData()
        {
            // Arrange
            var companyId = "c-first-batch";
            var batchSize = 100;
            DateTimeOffset? lastCreatedAt = null;
            string? lastId = null;

            var functionInput = new GetContactsByBatch.GetContactsByBatchFunctionInput(
                Origin,
                companyId,
                lastCreatedAt,
                lastId,
                batchSize);
            var expectedHttpInput = new GetContactsByBatchInput(companyId, lastCreatedAt, lastId, batchSize);
            var expectedJsonBody = JsonConvert.SerializeObject(expectedHttpInput);
            var expectedUri = new Uri(Origin + ExpectedApiPath);

            // Prepare mocked response data
            var contactDict = new Dictionary<string, ContactDetail>
            {
                {
                    "contact1", new ContactDetail(
                        new Dictionary<string, object?>
                        {
                            {
                                "name", "A"
                            }
                        },
                        null,
                        Array.Empty<ContactList>(),
                        null)
                }
            };
            // Create NextBatch with a specific time for comparison
            var expectedNextBatchTime = DateTimeOffset.UtcNow;
            var nextBatchInfo = new NextBatch(expectedNextBatchTime, "contact1"); // Using NextBatch constructor

            var expectedOutputData =
                new GetContactsByBatchOutput(contactDict, nextBatchInfo); // Using GetContactsByBatchOutput constructor
            var outputSuccess = true;
            var outputStatusCode = (int) HttpStatusCode.OK;
            string? outputMessage = null;
            int? outputErrorCode = null;
            Dictionary<string, object?>? outputErrorContext = null;
            string outputRequestId = Guid.NewGuid().ToString();

            var sleekflowOutput = new Output<GetContactsByBatchOutput>(
                outputSuccess,
                outputStatusCode,
                expectedOutputData,
                outputMessage,
                outputErrorCode,
                outputErrorContext,
                outputRequestId);
            var responseJson = JsonConvert.SerializeObject(sleekflowOutput);

            var httpResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
            };

            SetupMockPolicyExecution();

            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse).Verifiable();

            // Act
            var result = await _activity.RunAsync(functionInput);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Contacts.Count);
            Assert.AreEqual("A", result.Contacts["contact1"].Contact["name"]);
            Assert.IsNotNull(result.NextBatch);
            Assert.AreEqual(nextBatchInfo.LastContactId, result.NextBatch?.LastContactId);

            // --- Updated DateTimeOffset Assertion ---
            Assert.IsNotNull(
                result.NextBatch?.LastContactCreatedAt,
                "NextBatch LastContactCreatedAt should not be null");

            TimeSpan difference =
                nextBatchInfo.LastContactCreatedAt.Value -
                result.NextBatch.LastContactCreatedAt.Value; // Use .Value as it's nullable

            Assert.Less(
                difference.Duration(),
                TimeSpan.FromMilliseconds(5), // Allow a small tolerance (e.g., 5ms)
                $"NextBatch LastContactCreatedAt difference exceeded tolerance. Expected near: {nextBatchInfo.LastContactCreatedAt}, Actual: {result.NextBatch.LastContactCreatedAt}");
            // --- End Updated Assertion ---

            _mockRetryPolicy.Verify(
                p => p.ExecuteAsync(It.IsAny<Func<Context, Task<HttpResponseMessage>>>(), It.IsAny<Context>()),
                Times.Once);
            HttpRequestMessage? capturedRequest = null;

            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.Is<HttpRequestMessage>(
                    req => CheckRequestDetails(
                        req,
                        expectedUri,
                        HttpMethod.Post,
                        expectedJsonBody,
                        out capturedRequest)),
                ItExpr.IsAny<CancellationToken>());
            Assert.IsNotNull(capturedRequest, "Captured request should not be null");

            Assert.IsTrue(
                capturedRequest?.Headers.Contains("X-Sleekflow-Flow-Hub-Authorization"),
                "Auth header missing");
        }

        private bool CheckRequestDetails(
            HttpRequestMessage req,
            Uri expectedUri,
            HttpMethod expectedMethod,
            string expectedBody,
            out HttpRequestMessage? capturedRequest)
        {
            capturedRequest = req;
            var contentTask = req.Content?.ReadAsStringAsync() ?? Task.FromResult<string?>(null);
            contentTask.Wait();
            var actualBody = contentTask.Result;

            return req.Method == expectedMethod && req.RequestUri == expectedUri && actualBody == expectedBody;
        }

        [Test]
        public async Task RunAsync_SubsequentBatch_CallsApiCorrectlyAndReturnsData()
        {
            // Arrange
            var companyId = "c-next-batch";
            var batchSize = 50;
            DateTimeOffset lastCreatedAt = DateTimeOffset.UtcNow.AddMinutes(-5);
            string lastId = "last-contact-id-123";

            var functionInput = new GetContactsByBatch.GetContactsByBatchFunctionInput(
                Origin,
                companyId,
                lastCreatedAt,
                lastId,
                batchSize);
            var expectedHttpInput = new GetContactsByBatchInput(companyId, lastCreatedAt, lastId, batchSize);
            var expectedJsonBody = JsonConvert.SerializeObject(expectedHttpInput);
            var expectedUri = new Uri(Origin + ExpectedApiPath);

            // Prepare mocked response data
            var contactDict = new Dictionary<string, ContactDetail>
            {
                {
                    "contact_next", new ContactDetail(
                        new Dictionary<string, object?>
                        {
                            {
                                "name", "B"
                            }
                        },
                        null,
                        Array.Empty<ContactList>(),
                        null)
                }
            };
            NextBatch? nextBatchInfo = null; // Last batch
            var expectedOutputData = new GetContactsByBatchOutput(contactDict, nextBatchInfo);

            // Use Output<T> constructor
            var sleekflowOutput = new Output<GetContactsByBatchOutput>(
                true,
                (int) HttpStatusCode.OK,
                expectedOutputData,
                null,
                null,
                null,
                "req-id-2");
            var responseJson = JsonConvert.SerializeObject(sleekflowOutput);

            var httpResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseJson, Encoding.UTF8, "application/json")
            };

            SetupMockPolicyExecution();

            // Setup HttpMessageHandler (simpler expression)
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act
            var result = await _activity.RunAsync(functionInput);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Contacts.Count);
            Assert.AreEqual("B", result.Contacts["contact_next"].Contact["name"]);
            Assert.IsNull(result.NextBatch);

            _mockRetryPolicy.Verify(
                p => p.ExecuteAsync(It.IsAny<Func<Context, Task<HttpResponseMessage>>>(), It.IsAny<Context>()),
                Times.Once);

            // Verify SendAsync details using helper
            HttpRequestMessage? capturedRequest = null;

            _mockHttpMessageHandler.Protected().Verify(
                "SendAsync",
                Times.Once(),
                ItExpr.Is<HttpRequestMessage>(
                    req => CheckRequestDetails(
                        req,
                        expectedUri,
                        HttpMethod.Post,
                        expectedJsonBody,
                        out capturedRequest)),
                ItExpr.IsAny<CancellationToken>());
            Assert.IsNotNull(capturedRequest, "Captured request should not be null for subsequent batch");
        }

        [Test]
        public void RunAsync_ApiReturnsNonSuccess_ThrowsHttpRequestException()
        {
            // Arrange
            var companyId = "c-api-fail";

            var functionInput =
                new GetContactsByBatch.GetContactsByBatchFunctionInput(Origin, companyId, null, null, 10);
            var expectedUri = new Uri(Origin + ExpectedApiPath);

            var httpResponse = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.InternalServerError
            };

            SetupMockPolicyExecution();

            // Setup HttpMessageHandler mock
            _mockHttpMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(httpResponse);

            // Act & Assert
            Assert.ThrowsAsync<HttpRequestException>(async () => await _activity.RunAsync(functionInput));

            _mockRetryPolicy.Verify(
                p => p.ExecuteAsync(It.IsAny<Func<Context, Task<HttpResponseMessage>>>(), It.IsAny<Context>()),
                Times.Once);
        }
    }
}