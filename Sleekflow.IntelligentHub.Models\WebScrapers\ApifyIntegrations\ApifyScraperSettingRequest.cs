﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifyWebScraperSettingRequest : IWebScraperSetting
{
    [JsonProperty(PropertyName = "dynamicContentWaitSecs")]
    public int DynamicContentWaitSecs { get; set; }

    [JsonProperty(PropertyName = "maxCrawlDepth")]
    public int MaxCrawlDepth { get; set; }

    [JsonProperty(PropertyName = "maxCrawlPages")]
    public int MaxCrawlPages { get; set; }

    [JsonProperty(PropertyName = "MaxConcurrentPages")]
    public int MaxConcurrentPages { get; set; }

    [JsonProperty(PropertyName = "initialCookies")]
    public dynamic InitialCookies { get; set; }

    [JsonProperty(PropertyName = "startUrls")]
    public StartUrl[] StartUrls { get; set; }

    [JsonProperty(PropertyName = "aggressivePrune")]
    public bool AggressivePrune { get; set; }

    [JsonProperty(PropertyName = "debugMode")]
    public bool DebugMode { get; set; }

    [JsonProperty(PropertyName = "proxyConfiguration")]
    public ProxyConfiguration ProxyConfiguration { get; set; }

    [JsonProperty(PropertyName = "removeCookieWarnings")]
    public bool RemoveCookieWarnings { get; set; }

    [JsonProperty(PropertyName = "clickElementsCssSelector")]
    public string ClickElementsCssSelector { get; set; }

    [JsonProperty(PropertyName = "removeElementsCssSelector")]
    public string RemoveElementsCssSelector { get; set; }

    [JsonProperty(PropertyName = "saveFiles")]
    public bool SaveFiles { get; set; }

    [JsonProperty(PropertyName = "saveHtml")]
    public bool SaveHtml { get; set; }

    [JsonProperty(PropertyName = "saveMarkdown")]
    public bool SaveMarkdown { get; set; }

    [JsonProperty(PropertyName = "saveScreenshots")]
    public bool SaveScreenshots { get; set; }

    [JsonProperty(PropertyName = "crawlerType")]
    public string CrawlerType { get; set; }

    [JsonProperty(PropertyName = "excludeUrlGlobs")]
    public string[] ExcludeUrlGlobs { get; set; }

    [JsonProperty(PropertyName = "initialConcurrency")]
    public int InitialConcurrency { get; set; }

    [JsonProperty(PropertyName = "maxScrollHeightPixels")]
    public int MaxScrollHeightPixels { get; set; }

    [JsonProperty(PropertyName = "htmlTransformer")]
    public string HtmlTransformer { get; set; }

    [JsonProperty(PropertyName = "readableTextCharThreshold")]
    public int ReadableTextCharThreshold { get; set; }

    [JsonProperty(PropertyName = "maxResults")]
    public int MaxResults { get; set; }

    [JsonConstructor]
    public ApifyWebScraperSettingRequest(
        int dynamicContentWaitSecs,
        int maxCrawlDepth,
        int maxCrawlPages,
        int maxConcurrentPages,
        dynamic initialCookies,
        StartUrl[] startUrls,
        bool aggressivePrune,
        bool debugMode,
        ProxyConfiguration proxyConfiguration,
        bool removeCookieWarnings,
        string clickElementsCssSelector,
        string removeElementsCssSelector,
        bool saveFiles,
        bool saveHtml,
        bool saveMarkdown,
        bool saveScreenshots,
        string crawlerType,
        string[] excludeUrlGlobs,
        int initialConcurrency,
        int maxScrollHeightPixels,
        string htmlTransformer,
        int readableTextCharThreshold,
        int maxResults)
    {
        DynamicContentWaitSecs = dynamicContentWaitSecs;
        MaxCrawlDepth = maxCrawlDepth;
        MaxCrawlPages = maxCrawlPages;
        MaxConcurrentPages = maxConcurrentPages;
        InitialCookies = initialCookies;
        StartUrls = startUrls;
        AggressivePrune = aggressivePrune;
        DebugMode = debugMode;
        ProxyConfiguration = proxyConfiguration;
        RemoveCookieWarnings = removeCookieWarnings;
        ClickElementsCssSelector = clickElementsCssSelector;
        RemoveElementsCssSelector = removeElementsCssSelector;
        SaveFiles = saveFiles;
        SaveHtml = saveHtml;
        SaveMarkdown = saveMarkdown;
        SaveScreenshots = saveScreenshots;
        CrawlerType = crawlerType;
        ExcludeUrlGlobs = excludeUrlGlobs;
        InitialConcurrency = initialConcurrency;
        MaxScrollHeightPixels = maxScrollHeightPixels;
        HtmlTransformer = htmlTransformer;
        ReadableTextCharThreshold = readableTextCharThreshold;
        MaxResults = maxResults;
    }
}

public class StartUrl
{
    [JsonProperty(PropertyName = "url")]
    public string Url { get; set; }

    [JsonConstructor]
    public StartUrl(string url)
    {
        Url = url;
    }
}

public class ProxyConfiguration
{
    [JsonProperty(PropertyName = "useApifyProxy")]
    public bool UseApifyProxy { get; set; }

    [JsonConstructor]
    public ProxyConfiguration(bool useApifyProxy)
    {
        UseApifyProxy = useApifyProxy;
    }
}