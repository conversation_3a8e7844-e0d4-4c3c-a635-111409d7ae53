﻿using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Net.Http.Headers;
using Scriban;
using Scriban.Functions;
using Scriban.Runtime;
using Scriban.Syntax;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.States.Functions;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;

namespace Sleekflow.FlowHub.Controllers;

[ApiVersion("1.0")]
[Controller]
[Route("[controller]")]
public partial class PublicController : ControllerBase
{
    private const string WorkflowWebhookTriggerIdHeaderName = "X-Sleekflow-Workflow-Webhook-Trigger-Id";
    private const string WorkflowWebhookValidationTokenHeaderName = "X-Sleekflow-Workflow-Validation-Token";

    private readonly ILogger<PublicController> _logger;
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;
    private readonly IFlowHubEventHandler _flowHubEventHandler;

    [GeneratedRegex("^{{(.*)}}$")]
    private static partial Regex MyRegex();

    [GeneratedRegex("[^0-9]")]
    private static partial Regex DigitsOnlyRegex();

    public PublicController(
        ILogger<PublicController> logger,
        IWorkflowWebhookTriggerService workflowWebhookTriggerService,
        IFlowHubEventHandler flowHubEventHandler)
    {
        _logger = logger;
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
        _flowHubEventHandler = flowHubEventHandler;
    }

    [HttpPost]
    [Route("e")]
    public async Task<IActionResult> T(CancellationToken cancellationToken)
    {
        // Use ASP.NET Core's built-in request cancellation token
        // This automatically handles shutdown scenarios and client disconnections
        try
        {
            var workflowWebhookTriggerId = GetRequiredHeader(WorkflowWebhookTriggerIdHeaderName);
            var workflowValidationToken = GetRequiredHeader(WorkflowWebhookValidationTokenHeaderName);
            var contentType = GetRequiredHeader(HeaderNames.ContentType);

            var workflowWebhookTrigger = await _workflowWebhookTriggerService.GetWorkflowWebhookTriggerOrDefaultAsync(
                workflowWebhookTriggerId,
                workflowValidationToken);

            if (workflowWebhookTrigger == null)
            {
                _logger.LogError(
                    "No matching workflow webhook trigger found by combination of id {TriggerId} and validation token {ValidationToken}",
                    workflowWebhookTriggerId,
                    workflowValidationToken);

                return new NotFoundResult();
            }

            if (contentType != "application/json")
            {
                _logger.LogError(
                    "Not supported Content-Type: {ContentType}, trigger id: {TriggerId}",
                    contentType,
                    workflowWebhookTriggerId);

                return new BadRequestResult();
            }

            var reqBodyStr = await new StreamReader(Request.Body).ReadToEndAsync(cancellationToken);

            var onWebhookEventBody = new OnWebhookEventBody(
                DateTimeOffset.UtcNow,
                reqBodyStr,
                workflowWebhookTrigger.WorkflowId);

            if (await EvaluateExpressionAsync(reqBodyStr, workflowWebhookTrigger.ObjectIdExpression) is not string
                evaluatedObjectId)
            {
                _logger.LogError(
                    "Unable to evaluate object id from request body, trigger id: {TriggerId}, object id expression: {ObjectIdExpression}," +
                    " request body: {RequestBody}",
                    workflowWebhookTriggerId,
                    workflowWebhookTrigger.ObjectIdExpression,
                    reqBodyStr);

                return BadRequest();
            }

            if (workflowWebhookTrigger.ObjectType == "Contact.PhoneNumber")
            {
                evaluatedObjectId = DigitsOnlyRegex().Replace(evaluatedObjectId, string.Empty);
            }

            await _flowHubEventHandler.HandleAsync(
                onWebhookEventBody,
                evaluatedObjectId,
                workflowWebhookTrigger.ObjectType,
                workflowWebhookTrigger.SleekflowCompanyId);

            return new OkResult();
        }
        catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
        {
            _logger.LogInformation("Webhook request was cancelled (shutdown or client disconnect)");
            return StatusCode(499, "Request cancelled");
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[FlowHub workflow webhook trigger public endpoint] Error when processing webhook");

            throw;
        }
    }

    private string GetRequiredHeader(string headerName)
    {
        if (!Request.Headers.TryGetValue(headerName, out var headerValue))
        {
            throw new SfInternalErrorException($"Header {headerName} must be provided");
        }

        return headerValue.ToString();
    }

    public async Task<object?> EvaluateExpressionAsync(string reqStr, string expression)
    {
        try
        {
            var scriptObject = new ScriptObject
            {
                {
                    "reqStr", reqStr
                },
                {
                    "json", new JsonFunctions()
                },
                {
                    "array", new ExtendedArrayFunctions()
                },
            };
            scriptObject.IsReadOnly = true;

            var templateContext = new TemplateContext();
            templateContext.PushGlobal(scriptObject);
            ((DateTimeFunctions) templateContext.BuiltinObject["date"]).Format = "%FT%T%Z";

            var myRegex = MyRegex();
            var match = myRegex.Match(expression);

            if (match.Success)
            {
                expression = match.Groups[1].Value;
            }

            var evaluatedExpression = await Template.EvaluateAsync(
                expression.Trim(),
                templateContext);

            return evaluatedExpression;
        }
        catch (InvalidOperationException ex)
        {
            // TODO Log
            // throw new Exception($"Unable to evaluate expression: {expression}", ex);
            if (ex.Message.Contains("This template has errors."))
            {
                return expression;
            }

            throw;
        }
        catch (ScriptRuntimeException ex)
        {
            throw new SfScriptingException(ex);
        }
    }
}