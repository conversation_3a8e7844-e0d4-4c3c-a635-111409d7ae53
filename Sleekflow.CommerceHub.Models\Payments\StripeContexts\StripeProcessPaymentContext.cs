using Newtonsoft.Json;
using Stripe.Checkout;

namespace Sleekflow.CommerceHub.Models.Payments.StripeContexts;

public class StripeProcessPaymentContext : ProcessPaymentContext
{
    [JsonProperty("session")]
    public Session Session { get; set; }

    [JsonConstructor]
    public StripeProcessPaymentContext(
        string providerPaymentId,
        Session session)
        : base("Stripe", providerPaymentId)
    {
        Session = session;
    }
}