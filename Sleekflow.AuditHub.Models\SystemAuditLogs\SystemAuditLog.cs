﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.AuditHubDb;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs;

[Resolver(typeof(IAuditHubDbResolver))]
[DatabaseId("audithubdb")]
[ContainerId("system_audit_log")]
public class SystemAuditLog : Entity, IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sleekflow_user_profile_id")]
    public string? SleekflowUserProfileId { get; set; }

    [JsonProperty("sleekflow_staff_id")]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("data")]
    public Dictionary<string, object?>? Data { get; set; }

    [JsonProperty("created_time")]
    public DateTimeOffset CreatedTime { get; set; }

    [JsonConstructor]
    public SystemAuditLog(
        string id,
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string? sleekflowUserProfileId,
        string type,
        Dictionary<string, object?>? data,
        DateTimeOffset createdTime,
        int? ttl)
        : base(id, "SystemAuditLog", ttl)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowUserProfileId = sleekflowUserProfileId;
        Type = type;
        Data = data;
        CreatedTime = createdTime;
    }
}