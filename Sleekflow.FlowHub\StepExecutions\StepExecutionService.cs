using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.StepExecutions;

public interface IStepExecutionService
{
    Task CreateStepExecutionAsync(
        string stateId,
        StateIdentity stateIdentity,
        string stepId,
        string? stepNodeId,
        string stepExecutionStatus,
        string? workerInstanceId,
        DateTimeOffset createdAt,
        UserFriendlyError? userFriendlyError);

    Task<(List<StepExecution> StepExecutions, string? NextContinuationToken)> GetWorkflowStepExecutionsAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        string workflowId,
        string workflowVersionedId,
        string stepId);

    Task<List<StepExecution>> GetStateStepExecutionsAsync(
        string sleekflowCompanyId,
        List<string> stateIds,
        List<string> stepExecutionStatuses);

    Task<(List<StepExecution> StepExecutions, string? NextContinuationToken)> GetStateStepExecutionsAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        int limit,
        string stateId);

    Task<List<StepExecution>> UpdateStepTtlByStateIdAsync(
        string sleekflowCompanyId,
        string stateId,
        int ttl,
        int batchLimit = 60);

    Task<List<StepExecution>> GetStepExecutionsByFilterAsync(
        string sleekflowCompanyId,
        string stateId,
        string stepId,
        string stepExecutionStatus);
}

public class StepExecutionService : IStepExecutionService, IScopedService
{
    private readonly IIdService _idService;
    private readonly IStepExecutionRepository _stepExecutionRepository;
    private readonly ILogger<StepExecutionService> _logger;

    public StepExecutionService(
        IIdService idService,
        IStepExecutionRepository stepExecutionRepository,
        ILogger<StepExecutionService> logger)
    {
        _idService = idService;
        _stepExecutionRepository = stepExecutionRepository;
        _logger = logger;
    }

    public async Task CreateStepExecutionAsync(
        string stateId,
        StateIdentity stateIdentity,
        string stepId,
        string? stepNodeId,
        string stepExecutionStatus,
        string? workerInstanceId,
        DateTimeOffset createdAt,
        UserFriendlyError? userFriendlyError)
    {
        try
        {
            await _stepExecutionRepository.CreateAsync(
                new StepExecution(
                    _idService.GetId("StepExecution"),
                    null,
                    stateIdentity.SleekflowCompanyId,
                    stateId,
                    stateIdentity,
                    stepId,
                    stepNodeId,
                    stepExecutionStatus,
                    workerInstanceId,
                    createdAt,
                    userFriendlyError),
                new PartitionKeyBuilder()
                    .Add(stateIdentity.WorkflowId)
                    .Add(stepId)
                    .Add(stateId)
                    .Build());
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Fail to create step execution record for company {CompanyId} state {StateId} step {StepId} of status {StepExecutionStatus}",
                stateIdentity.SleekflowCompanyId,
                stateId,
                stepId,
                stepExecutionStatus);

            throw;
        }
    }

    public async Task<(List<StepExecution> StepExecutions, string? NextContinuationToken)>
        GetWorkflowStepExecutionsAsync(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            string workflowId,
            string workflowVersionedId,
            string stepId)
    {
        var (stepExecutions, nextContinuationToken) =
            await _stepExecutionRepository.GetContinuationTokenizedObjectsAsync(
                se =>
                    se.SleekflowCompanyId == sleekflowCompanyId
                    && se.StateIdentity.WorkflowId == workflowId
                    && se.StateIdentity.WorkflowVersionedId == workflowVersionedId
                    && se.StepId == stepId,
                continuationToken,
                limit);

        return (stepExecutions, nextContinuationToken);
    }

    public async Task<List<StepExecution>> GetStateStepExecutionsAsync(
        string sleekflowCompanyId,
        List<string> stateIds,
        List<string> stepExecutionStatuses)
    {
        var stepExecutions =
            await _stepExecutionRepository.GetObjectsAsync(
                se =>
                    se.SleekflowCompanyId == sleekflowCompanyId
                    && stateIds.Contains(se.StateId)
                    && stepExecutionStatuses.Contains(se.StepExecutionStatus));

        return stepExecutions;
    }

    public async Task<(List<StepExecution> StepExecutions, string? NextContinuationToken)>
        GetStateStepExecutionsAsync(
            string sleekflowCompanyId,
            string? continuationToken,
            int limit,
            string stateId)
    {
        var (stepExecutions, nextContinuationToken) =
            await _stepExecutionRepository.GetContinuationTokenizedObjectsAsync(
                se =>
                    se.SleekflowCompanyId == sleekflowCompanyId
                    && se.StateId == stateId,
                orderBy: se => se.CreatedAt,
                orderByAscending: true,
                continuationToken,
                limit);

        return (stepExecutions, nextContinuationToken);
    }

    public async Task<List<StepExecution>> UpdateStepTtlByStateIdAsync(
        string sleekflowCompanyId,
        string stateId,
        int ttl,
        int batchLimit = 60)
    {
        string? continuationToken = null;
        var results = new List<StepExecution>();

        while (true)
        {
            var (stepExecutions, token) = await _stepExecutionRepository.GetContinuationTokenizedObjectsAsync(
                se =>
                    se.SleekflowCompanyId == sleekflowCompanyId
                    && se.StateId == stateId,
                se => se.CreatedAt,
                true,
                continuationToken,
                batchLimit);

            await Parallel.ForEachAsync(
                stepExecutions,
                new ParallelOptions { MaxDegreeOfParallelism = 10 },
                async (stepExecution, cancellationToken) =>
                {
                    await _stepExecutionRepository.PatchAsync(
                        stepExecution.Id,
                        new PartitionKeyBuilder()
                            .Add(stepExecution.StateIdentity.WorkflowId)
                            .Add(stepExecution.StepId)
                            .Add(stateId)
                            .Build(),
                        new List<PatchOperation>()
                        {
                            PatchOperation.Set($"/{Entity.PropertyNameTtl}", ttl)
                        },
                        cancellationToken: cancellationToken);
                });

            results.AddRange(stepExecutions);

            continuationToken = token;

            if (string.IsNullOrWhiteSpace(continuationToken))
            {
                break;
            }
        }

        return results;
    }

    public async Task<List<StepExecution>> GetStepExecutionsByFilterAsync(
        string sleekflowCompanyId,
        string stateId,
        string stepId,
        string stepExecutionStatus)
    {
        var stepExecutions =
            await _stepExecutionRepository.GetObjectsAsync(
                se =>
                    se.SleekflowCompanyId == sleekflowCompanyId
                    && stateId == se.StateId
                    && stepId == se.StepId
                    && stepExecutionStatus == se.StepExecutionStatus);

        return stepExecutions;
    }
}