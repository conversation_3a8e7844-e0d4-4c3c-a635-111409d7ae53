using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.**********************.Constants;
using Sleekflow.**********************.Models.Constants;
using Sleekflow.**********************.Models.NetSuite.Integrations;
using Sleekflow.**********************.TravisBackend;

namespace Sleekflow.**********************.Triggers.NetSuite.External;

[TriggerGroup(ControllerNames.External, $"{BasePaths.NetSuite}")]
public class GetPayment
    : ITrigger<GetPayment.GetPaymentInput,
        GetPayment.GetPaymentOutput>
{
    private readonly ITravisBackendService _travisBackendService;

    public GetPayment(ITravisBackendService travisBackendService)
    {
        _travisBackendService = travisBackendService;
    }

    public class GetPaymentInput
    {
        [Required]
        [JsonProperty("bill_record_id")]
        public string BillRecordId { get; set; }

        [Required]
        [JsonProperty("customer_id")]
        public string CustomerId { get; set; }

        [JsonConstructor]
        public GetPaymentInput(
            string billRecordId,
            string customerId)
        {
            BillRecordId = billRecordId;
            CustomerId = customerId;
        }
    }

    public class GetPaymentOutput
    {
        [JsonProperty("payments", NullValueHandling = NullValueHandling.Ignore)]
        public List<GetPaymentResponse>? Payments { get; set; }

        [JsonConstructor]
        public GetPaymentOutput(
            List<GetPaymentResponse>? payments)
        {
            Payments = payments;
        }
    }

    public async Task<GetPaymentOutput> F(GetPaymentInput input)
    {
        var result = await _travisBackendService.GetPaymentRecord(input.BillRecordId, input.CustomerId);
        return new GetPaymentOutput(result);
    }
}