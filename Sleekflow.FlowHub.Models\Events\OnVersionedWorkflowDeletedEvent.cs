﻿using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Events;

public class OnVersionedWorkflowDeletedEvent : IHasSleekflowCompanyId
{
    public DateTimeOffset DeletedAt { get; } = DateTimeOffset.UtcNow;

    public string SleekflowCompanyId { get; set; }

    public string WorkflowId { get; set; }

    public string WorkflowVersionedId { get; set; }

    public AuditEntity.SleekflowStaff? DeletedBy { get; set; }

    public OnVersionedWorkflowDeletedEvent(
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        AuditEntity.SleekflowStaff? deletedBy)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        DeletedBy = deletedBy;
    }
}