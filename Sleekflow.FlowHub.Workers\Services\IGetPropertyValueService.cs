using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.Models.Crm;

namespace Sleekflow.FlowHub.Workers.Services
{
    public interface IGetPropertyValueService
    {
        Task<GetContactPropertyValueByContactIdsOutput> GetContactPropertyValuesByContactIds(
            string companyId,
            string propertyId,
            Dictionary<string, ContactDetail>? contacts,
            string origin);

        Task<GetPropertyValuesByContactIdsOutput> GetCustomObjectPropertyValueByContactsId(
            string sleekflowCompanyId,
            string schemaId,
            string schemafulObjectPropertyId,
            Dictionary<string, ContactDetail>? contacts,
            string origin);
    }
}