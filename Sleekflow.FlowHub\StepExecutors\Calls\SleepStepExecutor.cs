using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISleepStepExecutor : IStepExecutor
{
}

public class SleepStepExecutor : GeneralStepExecutor<CallStep<SleepStepArgs>>, ISleepStepExecutor, IScopedService
{
    private readonly IStepOrchestrationService _stepOrchestrationService;
    private readonly IStateEvaluator _stateEvaluator;

    public SleepStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IStepOrchestrationService stepOrchestrationService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stepOrchestrationService = stepOrchestrationService;
        _stateEvaluator = stateEvaluator;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = step as CallStep<SleepStepArgs> ?? throw new InvalidOperationException();

        var seconds =
            _stateEvaluator.IsExpression(callStep.Args.SecondsExpr)
                ? await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.SecondsExpr)
                : callStep.Args.SecondsExpr;

        var secondsDecimal = decimal.TryParse(seconds?.ToString(), out var parsedSeconds)
            ? Math.Ceiling(parsedSeconds)
            : 0;

        var timeSpan = new TimeSpan(0, 0, Convert.ToInt32(secondsDecimal));

        await _stepOrchestrationService.ExecuteSleepSleep(
            state.Id,
            step.Id,
            timeSpan,
            stackEntries);

        // The actions is Delayed Complete
    }
}