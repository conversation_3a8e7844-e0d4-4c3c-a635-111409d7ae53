config:
  auth0:client_id:
    secure: AAABALNoQ7qkJzLMZ6Ng4VFjK2pFjvurqBR6zCUEwWa1VhwTwz3vmgAmidRL5Qt6kQshZFypEegSo2zUi1WQRQ==
  auth0:client_secret:
    secure: AAABAM3C2LxKYvq2orahw2M1c0Wt1cXJNYCb1Fcm2xt6vVHwnR63PU92PC5DGzz5mT+Rmyz2vQHcUr7tQenetZ/k0OuymTY3pHZMpdjZ1kIzId45AYRwmQj85ymY0E2U
  auth0:domain: sleekflow-dev.eu.auth0.com
  core_sql_db:connection_string_eas:
    secure: AAABAPqNmA9fpc5V0005dI8xi6zt0lg9jnpV7ldof8hM3y/p36VOTuGdfW0fpj3RKuKhbAADMZAhnrMqkFR9bbkCP8t8Tz7VFLQ+YFcsFt7WtC19x1+owSCv8RLqFTCZnPASYfkpPRHZDWv1gdHoE5+n7oXueW9EUc5kl6TkWvorLsgfqCbqPHN8Fmdnv3tGCbXKv3Jj576vI4E66nT4PqsHs4Vs3y90aRPQKdahBIO8p+MchS5ocJHMYbI/g9FTPSVISvFnR9HmCzb8GqgKirOFVzZnOyEA9O4+O507ttQ7QEvQW1CikFkgl/ShsmSWYSgfUk71JprI8VOGhiSPPCuYds6wsJlbvvOmAWVwNww0GGMeHr1+IQs75euEiTSMhePkjHi2hwyi9b65s44ZxrDcbXvJ27+mq4NC4ie9ppu24Yw1q1i1NsnKu90lI0dEVpVZEUouNFGAn6yLtfQ8vPFUIoTek855fPEud11qZufa7DrEMGqADL55Il0TbnB2inaghb6brev1ElzHMc+7iUuf6EXE+xZfBf8+Qyg/KP/CXR6oXyy9TMf1F4i6eQ8zDBwKJi7IdQ==
  core_sql_db:connection_string_eus:
    secure: AAABADfaCmOPbr6z0U2bqbMroxCo/R8egYzOPkPctB4=
  core_sql_db:connection_string_seas:
    secure: AAABALRDMC6XHpqG0o774SwC9wOWmQ5neOLLsw8BBS0=
  core_sql_db:connection_string_uaen:
    secure: AAABAF8y5L5mZSKiJzzHUmPf8GiP8C0+4MOrr6W029M=
  core_sql_db:connection_string_weu:
    secure: AAABABkz3maVc4mRC7dJT+ulbEivpTzRw0ziGfkQAjM=
  azure-native:location: EastAsia
  pulumi:template: azure-csharp
  gcp:project-id: cool-phalanx-404402
  gcp:credential-json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  sleekflow:name: dev
  sleekflow:origins:
    - http://localhost:3000
    - http://localhost:5173
    - https://localhost:3000
    - https://localhost:5173
    - https://uat2.sleekflow.io
    - https://dev.sleekflow.io
    - https://dev-revamp.sleekflow.io
    - https://phrase-revamp-uat.sleekflow.io
    - https://uat.sleekflow.io
    - https://sleekflow-chat-react-app.hensontsai.com
    - https://v1-dev.sleekflow.io
  sleekflow:clients:
    - name: sleekflow-api
      allowed_logout_urls: [ ]
      allowed_origins:
        - https://localhost:5000
        - https://localhost:5001
        - https://localhost:5002
        - https://localhost:5003
        - https://localhost:7103
        - https://sleekflow-prod-api-uat-auth0.azurewebsites.net
      app_type: non_interactive
      callbacks:
        - https://localhost:5000
        - https://localhost:5001
        - https://localhost:5002
        - https://localhost:5003
        - https://localhost:7103
        - https://sleekflow-prod-api-uat-auth0.azurewebsites.net
      grant_types:
        - client_credentials
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      token_endpoint_auth_method: client_secret_post
      web_origins:
        - https://localhost:5000
        - https://localhost:5001
        - https://localhost:5002
        - https://localhost:5003
        - https://localhost:7103
        - https://sleekflow-prod-api-uat-auth0.azurewebsites.net

    - name: sleekflow-api-health-check
      allowed_logout_urls: [ ]
      allowed_origins:
        - https://localhost:5000
        - https://sleekflow-core-app-eas-dev.azurewebsites.net
        - https://sleekflow-powerflow-app-eas-dev.azurewebsites.net
      app_type: non_interactive
      callbacks:
        - https://localhost:5000
        - https://sleekflow-core-app-eas-dev.azurewebsites.net
        - https://sleekflow-powerflow-app-eas-dev.azurewebsites.net
      grant_types:
        - client_credentials
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      token_endpoint_auth_method: client_secret_post
      web_origins:
        - https://localhost:5000
        - https://sleekflow-core-app-eas-dev.azurewebsites.net
        - https://sleekflow-powerflow-app-eas-dev.azurewebsites.net

    - name: sleekflow-client-powerflow-app
      initiate_login_uri: https://powerflowdev.z7.web.core.windows.net/login
      allowed_logout_urls:
        - https://powerflow.sleekflow.io
        - https://powerflow.sleekflow.io/logout
        - https://powerflowdev.z7.web.core.windows.net
        - https://powerflowdev.z7.web.core.windows.net/logout
        - http://localhost:3000
        - http://localhost:3000/logout
        - https://localhost:3000
        - https://localhost:3000/logout
      allowed_origins:
        - http://localhost:3000
        - https://localhost:3000
        - https://powerflow.sleekflow.io
        - https://powerflowdev.z7.web.core.windows.net
      app_type: spa
      callbacks:
        - http://localhost:3000
        - http://localhost:3000/callback
        - https://localhost:3000
        - https://localhost:3000/callback
        - https://powerflow.sleekflow.io
        - https://powerflow.sleekflow.io/callback
        - https://powerflowdev.z7.web.core.windows.net
        - https://powerflowdev.z7.web.core.windows.net/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      token_endpoint_auth_method: none
      web_origins:
        - http://localhost:3000
        - https://localhost:3000
        - https://powerflow.sleekflow.io
        - https://powerflowdev.z7.web.core.windows.net

    - name: sleekflow-client-auth0-actions
      allowed_logout_urls: [ ]
      allowed_origins: [ ]
      app_type: non_interactive
      callbacks: [ ]
      grant_types:
        - client_credentials
      token_endpoint_auth_method: client_secret_post
      web_origins: [ ]

    - name: sleekflow-client-web-app
      initiate_login_uri: https://v1-dev.sleekflow.io
      allowed_logout_urls:
        - http://localhost:3000
        - http://localhost:5173
        - https://localhost:3000
        - https://localhost:5173
        - https://uat.sleekflow.io
        - https://uat2.sleekflow.io
        - https://dev.sleekflow.io
        - https://adfs.shkp.com/adfs/ls?wa=wsignoutcleanup1.0
        - https://sso-uat1.hongyip.com/auth/realms/hy-sso-uat1/protocol/openid-connect/logout
        - https://v1-dev.sleekflow.io
      allowed_origins:
        - http://localhost:3000
        - http://localhost:5173
        - https://localhost:3000
        - https://localhost:5173
        - https://uat.sleekflow.io
        - https://uat2.sleekflow.io
        - https://dev.sleekflow.io
        - https://v1-dev.sleekflow.io
      app_type: spa
      callbacks:
        - http://localhost:3000
        - http://localhost:5173
        - https://localhost:3000
        - https://localhost:5173
        - https://uat.sleekflow.io
        - https://uat2.sleekflow.io
        - http://localhost:3000/settings/opt-in
        - http://localhost:3000/settings/templates
        - https://uat.sleekflow.io/settings/templates
        - https://uat.sleekflow.io/settings/opt-in
        - https://uat2.sleekflow.io/settings/templates
        - https://uat2.sleekflow.io/settings/opt-in
        - https://dev.sleekflow.io
        - https://v1-dev.sleekflow.io
        - https://v1-dev.sleekflow.io/settings/templates
        - https://v1-dev.sleekflow.io/settings/opt-in
        - https://v1-dev.sleekflow.io/settings/inbox
        - https://v1-dev.sleekflow.io/channels
        - https://v1-dev.sleekflow.io/en-US/settings/templates
        - https://v1-dev.sleekflow.io/zh-HK/settings/templates
        - https://v1-dev.sleekflow.io/zh-CN/settings/templates
        - https://v1-dev.sleekflow.io/pt-BR/settings/templates
        - https://v1-dev.sleekflow.io/id-ID/settings/templates
        - https://v1-dev.sleekflow.io/it-IT/settings/templates
        - https://v1-dev.sleekflow.io/de-DE/settings/templates
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      web_origins:
        - http://localhost:3000
        - http://localhost:5173
        - https://localhost:3000
        - https://localhost:5173
        - https://uat.sleekflow.io
        - https://uat2.sleekflow.io
        - https://dev.sleekflow.io
        - https://v1-dev.sleekflow.io

    - name: sleekflow-client-mobile-app
      allowed_logout_urls:
        - https://sso-dev.sf.chat/logout/callback
        - https://uat2.sleekflow.io
        - https://uat.sleekflow.io
      allowed_origins: [ ]
      app_type: native
      callbacks:
        - sleekflowauth0://sleekflow-dev.eu.auth0.com/android/io.sleekflow.sleekflow/callback
        - sleekflowauth0://sso-dev.sf.chat/android/io.sleekflow.sleekflow/callback
        - io.sleekflow.sleekflow://sleekflow-dev.eu.auth0.com/ios/io.sleekflow.sleekflow/callback
        - io.sleekflow.sleekflow://sso-dev.sf.chat/ios/io.sleekflow.sleekflow/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      web_origins: [ ]
      native_client_options:
        ios:
          team_id: JNXJD4KQ2C
          app_bundle_identifier: io.sleekflow.app
        android:
          app_package_name: io.sleekflow.sleekflow
          key_hashes:
            - 4B:7A:FA:CF:5F:56:1E:88:C1:0C:01:7A:EA:5F:61:6C:31:0D:AF:18:59:50:C4:9E:FF:32:8E:31:17:0C:4E:86
            - C8:44:11:BE:74:24:10:92:66:74:36:AE:D3:83:47:FC:44:AE:C5:76:0D:5E:2E:5C:E2:4F:9E:1F:7B:EB:5C:4D

    - name: sleekflow-client-mobile-v2-app
      allowed_logout_urls:
        - https://sso-dev.sf.chat/logout/callback
        - https://uat2.sleekflow.io
        - https://uat.sleekflow.io
      allowed_origins: [ ]
      app_type: native
      callbacks:
        - sleekflowauth0://sleekflow-dev.eu.auth0.com/android/io.sleekflow.v2/callback
        - sleekflowauth0://sso-dev.sf.chat/android/io.sleekflow.v2/callback
        - io.sleekflow.v2://sleekflow-dev.eu.auth0.com/ios/io.sleekflow.v2/callback
        - io.sleekflow.v2://sso-dev.sf.chat/ios/io.sleekflow.v2/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      web_origins: [ ]
      native_client_options:
        ios:
          team_id: JNXJD4KQ2C
          app_bundle_identifier: io.sleekflow.app
        android:
          app_package_name: io.sleekflow.v2
          key_hashes:
            - 4B:7A:FA:CF:5F:56:1E:88:C1:0C:01:7A:EA:5F:61:6C:31:0D:AF:18:59:50:C4:9E:FF:32:8E:31:17:0C:4E:86
            - C8:44:11:BE:74:24:10:92:66:74:36:AE:D3:83:47:FC:44:AE:C5:76:0D:5E:2E:5C:E2:4F:9E:1F:7B:EB:5C:4D

    - name: sleekflow-client-reseller-portal-app
      initiate_login_uri: https://partner-uat.sleekflow.io
      app_type: spa
      allowed_logout_urls:
        - http://localhost:3000
        - https://localhost:3000
        - https://partner-uat.sleekflow.io
      allowed_origins:
        - http://localhost:3000
        - https://localhost:3000
        - https://partner-uat.sleekflow.io
      callbacks:
        - http://localhost:3000
        - https://localhost:3000
        - https://partner-uat.sleekflow.io
      web_origins:
        - http://localhost:3000
        - https://localhost:3000
        - https://partner-uat.sleekflow.io
      grant_types:
        - authorization_code
        - implicit
        - refresh_token

    - name: sleekflow-client-web-v2-app
      initiate_login_uri: https://dev.sleekflow.io
      allowed_logout_urls:
        - http://localhost:3000
        - https://localhost:3000
        - http://localhost:5173
        - https://localhost:5173
        - https://dev-revamp.sleekflow.io
        - https://phrase-revamp-uat.sleekflow.io
        - https://sleekflow-chat-react-app.hensontsai.com
        - https://adfs.shkp.com/adfs/ls?wa=wsignoutcleanup1.0
        - https://sso-uat1.hongyip.com/auth/realms/hy-sso-uat1/protocol/openid-connect/logout
        - https://dev.sleekflow.io
      allowed_origins:
        - http://localhost:3000
        - https://localhost:3000
        - http://localhost:5173
        - https://localhost:5173
        - https://dev-revamp.sleekflow.io
        - https://phrase-revamp-uat.sleekflow.io
        - https://sleekflow-chat-react-app.hensontsai.com
        - https://dev.sleekflow.io
      app_type: spa
      callbacks:
        - http://localhost:3000
        - https://localhost:3000
        - http://localhost:5173
        - https://localhost:5173
        - https://dev-revamp.sleekflow.io
        - https://phrase-revamp-uat.sleekflow.io
        - https://sleekflow-chat-react-app.hensontsai.com
        - https://dev.sleekflow.io
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      web_origins:
        - http://localhost:3000
        - https://localhost:3000
        - http://localhost:5173
        - https://localhost:5173
        - https://dev-revamp.sleekflow.io
        - https://phrase-revamp-uat.sleekflow.io
        - https://sleekflow-chat-react-app.hensontsai.com
        - https://dev.sleekflow.io

    - name: sleekflow-client-headless-api-portal-app
      initiate_login_uri: https://headless-api-portal-git-uat.hensontsai.com
      allowed_logout_urls:
        - http://localhost:3000
        - https://localhost:3000
        - https://headless-api-portal-git-uat.hensontsai.com
      allowed_origins:
        - http://localhost:3000
        - https://localhost:3000
        - https://headless-api-portal-git-uat.hensontsai.com
      app_type: spa
      callbacks:
        - http://localhost:3000
        - https://localhost:3000
        - https://headless-api-portal-git-uat.hensontsai.com
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      web_origins:
        - http://localhost:3000
        - https://localhost:3000
        - https://headless-api-portal-git-uat.hensontsai.com

  sleekflow:connections:
    - name: sleekflow-connection-google-oauth2
      strategy: google-oauth2
      force_replace: false
      enabled_clients:
        - sleekflow-client-web-app
        - sleekflow-client-mobile-app
        - sleekflow-client-web-v2-app
        - sleekflow-client-mobile-v2-app
      options:
        scopes: [ email ]
        client_id: 94054718414-0igmlaripp17n3asjqd628q7hlqqu1uh.apps.googleusercontent.com
        client_secret: 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

    - name: sleekflow-connection-apple
      strategy: apple
      enabled_clients:
        - sleekflow-client-web-app
        - sleekflow-client-mobile-app
        - sleekflow-client-web-v2-app
        - sleekflow-client-mobile-v2-app
      is_domain_connection: false
      force_replace: false
      options:
        kid: JGDF63GM34
        scopes:
          - name
          - email
        team_id: JNXJD4KQ2C
        client_id: io.sleekflow.app
        client_secret: |-
*****************************************************************************************************************************************************************************************************************************************************************************************************************************

  sleekflow:custom_domain:
    is_enabled: true
    domain: sso-dev.sf.chat
    type: auth0_managed_certs
  sleekflow:post_login_webhook: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/postUserLogin
  sleekflow:pre_user_registration_webhook: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/preUserRegistration
  sleekflow:post_change_password_webhook: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/postUserChangePassword
  sleekflow:requires_mfa_webhook: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/requiresMFA
  sleekflow:action_issuer: https://sso-dev.sleekflow.io/
  sleekflow:action_audience: https://api-dev.sleekflow.io/
  sleekflow:jwk_url: https://sso-dev.sf.chat/.well-known/jwks.json
  sleekflow:enable_custom_domain_in_emails: true
  sleekflow:travis_backend_base_url: https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net
  sleekflow:whatsapp_cloud_api_override_webhook: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/messaging-hub/WhatsappCloudApiWebhook
  sleekflow:auth0_client_id: A8CAuyJshC04w6FYYZZdxAtfoXSvKFnU
  sleekflow:auth0_client_secret: ****************************************************************
  sleekflow:auth0_domain: sleekflow-dev.eu.auth0.com
  sleekflow:user_email_check_secret_key: eYx1z5CTaCXkgHWObGRbLM14oo1fnVJL
