﻿using Pulumi.AzureNative.Cache.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Cache = Pulumi.AzureNative.Cache;

namespace Sleekflow.Infras.Components;

public class Redis
{
    private readonly ResourceGroup _resourceGroup;
    private readonly MyConfig _myConfig;

    public Redis(
        ResourceGroup resourceGroup,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _myConfig = myConfig;
    }

    public Cache.Redis InitRedis(string? name = null, string? locationName = null)
    {
        const string redisName = "sleekflow-redis";
        var redis = new Cache.Redis(
            name is not null ? $"{redisName}-{name}" : redisName,
            new Cache.RedisArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location =
                    locationName is not null
                        ? LocationNames.GetAzureLocation(locationName)
                        : _resourceGroup.Location,
                Sku = new Cache.Inputs.SkuArgs
                {
                    Capacity = _myConfig.Name == "production" ? 1 : 0, Family = "C", Name = "Standard",
                },
                RedisConfiguration = new RedisCommonPropertiesRedisConfigurationArgs(),
                EnableNonSslPort = false,
                RedisVersion = "6",
            });

        return redis;
    }
}