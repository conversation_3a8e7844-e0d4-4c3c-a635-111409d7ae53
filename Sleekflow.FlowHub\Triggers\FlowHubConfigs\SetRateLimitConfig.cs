using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class SetRateLimitConfig
    : ITrigger<
        SetRateLimitConfig.SetRateLimitConfigInput,
        SetRateLimitConfig.SetRateLimitConfigOutput>
{
    private readonly ICacheService _cacheService;
    private readonly ILogger<SetRateLimitConfig> _logger;

    public SetRateLimitConfig(ICacheService cacheService, ILogger<SetRateLimitConfig> logger)
    {
        _cacheService = cacheService;
        _logger = logger;
    }

    public class SetRateLimitConfigInput
    {
        [JsonProperty("key_name")]
        [Required]
        public string KeyName { get; set; }

        [JsonProperty("window_size_ms")]
        [Required]
        public int WindowSizeMs { get; set; }

        [JsonProperty("rate_limit")]
        [Required]
        public int RateLimit { get; set; }

        [JsonProperty("expiration_seconds")]
        [Required]
        public int ExpirationSeconds { get; set; }

        [JsonConstructor]
        public SetRateLimitConfigInput(string keyName, int windowSizeMs, int rateLimit, int expirationSeconds)
        {
            KeyName = keyName;
            WindowSizeMs = windowSizeMs;
            RateLimit = rateLimit;
            ExpirationSeconds = expirationSeconds;
        }
    }

    public class SetRateLimitConfigOutput
    {
        [JsonProperty("message")]
        public string Message { get; set; }

        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public SetRateLimitConfigOutput(string message, bool success)
        {
            Message = message;
            Success = success;
        }
    }

    public async Task<SetRateLimitConfigOutput> F(SetRateLimitConfigInput input)
    {
        var baseKey = input.KeyName;

        var configWindowKey = $"{baseKey}:config:window_size_ms";
        var configRateLimitKey = $"{baseKey}:config:rate_limit";
        var expiration = TimeSpan.FromSeconds(input.ExpirationSeconds);

        try
        {
            // Using UpsertAsync to set the window size
            var windowSize = await _cacheService.UpsertAsync<string>(
                configWindowKey,
                () => Task.FromResult(
                    input.WindowSizeMs.ToString()),
                expiration);

            // Using UpsertAsync to set the rate limit
            var rateLimit = await _cacheService.UpsertAsync<string>(
                configRateLimitKey,
                () => Task.FromResult(
                    input.RateLimit.ToString()),
                expiration);

            _logger.LogInformation(
                "Successfully set rate limit config for key {KeyName} (via CacheService): Window {WindowSizeMs}ms, Rate {RateLimit}, Expiration {ExpirationSeconds}s",
                input.KeyName,
                input.WindowSizeMs,
                input.RateLimit,
                input.ExpirationSeconds);
            return new SetRateLimitConfigOutput("Rate limit configuration updated successfully.", true);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "CacheService failed to set one or both rate limit config keys for base key {KeyName}",
                input.KeyName);

            return new SetRateLimitConfigOutput(
                "Failed to update rate limit configuration in Redis using CacheService. One or both operations failed.",
                false);
        }
    }
}