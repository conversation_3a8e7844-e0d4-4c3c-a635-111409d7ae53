﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ReInitConnection : ITrigger
{
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;

    public ReInitConnection(
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoConnectionService zohoConnectionService)
    {
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
    }

    public class ReInitConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonProperty("additional_details")]
        [ValidateObject]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public ReInitConnectionInput(
            string sleekflowCompanyId,
            string connectionId,
            string successUrl,
            string failureUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class ReInitConnectionOutput
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("is_re_authentication_required")]
        public bool IsReAuthenticationRequired { get; set; }

        [JsonProperty("zoho_authentication_url")]
        public string? ZohoAuthenticationUrl { get; set; }

        [JsonConstructor]
        public ReInitConnectionOutput(
            string providerName,
            bool isReAuthenticationRequired,
            string? zohoAuthenticationUrl)
        {
            ProviderName = providerName;
            IsReAuthenticationRequired = isReAuthenticationRequired;
            ZohoAuthenticationUrl = zohoAuthenticationUrl;
        }
    }

    public async Task<ReInitConnectionOutput> F(
        ReInitConnectionInput reInitConnectionInput)
    {
        var connection = await _zohoConnectionService.GetByIdAsync(
            reInitConnectionInput.ConnectionId,
            reInitConnectionInput.SleekflowCompanyId);
        if (connection.IsActive)
        {
            return new ReInitConnectionOutput(
                "zoho-integrator",
                false,
                null);
        }

        try
        {
            await _zohoAuthenticationService.ReAuthenticateAndStoreAsync(
                connection.AuthenticationId,
                connection.SleekflowCompanyId);

            await _zohoConnectionService.PatchAsync(
                connection.Id,
                connection.SleekflowCompanyId,
                connection.Name,
                true);

            return new ReInitConnectionOutput(
                "zoho-integrator",
                false,
                null);
        }
        catch (Exception)
        {
            var redirectUrl =
                await _zohoAuthenticationService.AuthenticateAsync(
                    reInitConnectionInput.SleekflowCompanyId,
                    reInitConnectionInput.SuccessUrl,
                    reInitConnectionInput.FailureUrl,
                    reInitConnectionInput.AdditionalDetails);

            return new ReInitConnectionOutput(
                "zoho-integrator",
                true,
                redirectUrl);
        }
    }
}