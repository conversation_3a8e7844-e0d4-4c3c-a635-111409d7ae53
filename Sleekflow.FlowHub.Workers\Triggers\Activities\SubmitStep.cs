using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Events.QueueNameFormatter;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.JsonConfigs;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class SubmitStep
{
    private readonly ILogger<SubmitStep> _logger;
    private readonly IServiceBusManager _serviceBusManager;

    public SubmitStep(
        ILogger<SubmitStep> logger,
        IServiceBusManager serviceBusManager)
    {
        _logger = logger;
        _serviceBusManager = serviceBusManager;
    }

    [Function("SubmitStep")]
    public async Task<SubmitStepOutput> RunAsync(
        [ActivityTrigger]
        SubmitStepInput submitStepInput)
    {
        try
        {
            // Log the attempt to publish the Service Bus message
            var eventMessage = new OnStepRequestedEvent(
                submitStepInput.StateId,
                submitStepInput.StepId,
                submitStepInput.StackEntries,
                submitStepInput.WorkerInstanceId);

            // Serialize the event (helps in logging)
            var eventJson = JsonConvert.SerializeObject(eventMessage);

            // Log before publishing message to Service Bus
            _logger.LogInformation(
                "Attempting to publish message to Service Bus queue. Event: {EventJson}, QueueName: {QueueName}",
                eventJson,
                KebabCaseFormatter.Format(nameof(OnStepRequestedEvent))); // replace with your actual queue name

            // Publish the event to the Service Bus
            await _serviceBusManager.PublishAsync(eventMessage);

            // Log success
            _logger.LogInformation(
                "Successfully published message to Service Bus queue. Event: {EventJson}, QueueName: {QueueName}",
                eventJson,
                KebabCaseFormatter.Format(nameof(OnStepRequestedEvent))); // replace with your actual queue name
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to publish message to Service Bus. Input JSON: {InputJson}, Error: {ErrorMessage}",
                JsonConvert.SerializeObject(submitStepInput, JsonConfig.DefaultJsonSerializerSettings),
                ex.Message);

            throw; // Re-throw to preserve the exception and let Azure Functions handle it
        }

        return new SubmitStepOutput();
    }
}