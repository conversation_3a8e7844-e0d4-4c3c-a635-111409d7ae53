# Lead Nurturing Agent Collaboration Architecture

## Executive Summary

This document describes a capability-based multi-agent system for lead qualification and nurturing through natural conversation. The architecture implements a modular design with specialized agent functions, structured data exchange, and sophisticated coordinating logic for dynamic agent selection.

**Core Agents:**
- **LeadClassifierAgent**: Implements a weighted scoring algorithm (Intent 30%, Buying Signals 35%, Question Depth 25%, Sentiment 5%, Customer Fit 5%) to classify leads into hot/warm/cold/existing_customer with a numeric score (0-100).
- **DecisionAgent**: Applies decision table mapping against classification rules with overrides for special conditions (human requests, demos, drop-offs) and produces deterministic routing decisions.
- **StrategyAgent**: Evaluates context to determine knowledge requirements and generates structured strategy guidance with customer journey analysis.
- **KnowledgeRetrievalAgent**: Utilizes KnowledgeRetrievalAgentGeminiChatHistoryReducer to integrate with knowledge plugin and evaluate query-knowledge alignment.
- **ReviewerAgent**: Implements differentiated review criteria based on response type, validates against placeholders, knowledge integration, and language requirements.

**Response Agents:**
- **ResponseCrafterAgent**: Utilizes ResponseCrafterAgentGeminiChatHistoryReducer to analyze knowledge sufficiency and implements a 3-tier decision logic for response generation.
- **TransitioningResponseCrafterAgent**: Specializes in crafting WhatsApp-compatible Markdown messages for handoff scenarios with context-aware formatting.
- **InformationGatheringResponseCrafterAgent**: Processes structured field validation data to generate targeted information requests with field-specific reasoning.

**Planning Agents:**
- **LeadAssignmentPlanningAgent**: Implements ActionAgentGeminiChatHistoryReducer to extract classification data and apply configurable team assignment rules with detailed reasoning.
- **DemoSchedulingPlanningAgent**: Utilizes DemoSchedulingPlanningAgentReducer for field extraction, validation and orchestrates a multi-step information gathering workflow.

**Action Agents:**
- **ConfirmationAgent**: Determines if the user has explicitly confirmed or declined a proposed action by analyzing conversation context, and provides confirmation questions when needed.
- **ActionAgent**: Implements execution of actions in two main phases:
  1. Execution Phase: Directly calls plugin tools to execute the actions (no structured output)
  2. Report Phase: Reports the outcomes with detailed results (returns structured JSON)

The system features an extensible capability registry, intelligent agent selection through a stateless coordination function with JSON parsing, comprehensive session management to prevent duplicate processing, and structured exception handling. All inter-agent communication uses strictly typed JSON data structures with dedicated model classes. The system optimizes performance by mapping different LLM models to specific agent roles based on computational requirements.

**Fully Automated Execution:** The entire lead nurturing process operates without human intervention in the decision-making loop. From initial classification through action execution, all agent transitions, decisions, reviews, and external system integrations happen programmatically. This end-to-end automation enables consistent, scalable lead nurturing while maintaining contextual awareness throughout the conversation lifecycle.

**One-Turn Processing Model:** Each customer message triggers exactly one agent group chat session that produces exactly one response, regardless of internal complexity. Within this single turn, multiple agents may be activated and multiple system actions may be performed, but the customer always receives one cohesive response. Each new customer message starts a fresh agent collaboration session.

## 1. Overview

The Lead Nurturing system is an intelligent, multi-agent collaboration designed to qualify, nurture, and manage customer leads through natural conversations. Using a capability-based architecture, the system dynamically coordinates specialized agents to handle different aspects of lead management—from classification to action execution.

Key features:
- Capability-based agent selection for improved flexibility and maintainability
- Specialized agents for lead classification, decision-making, knowledge retrieval, and response crafting
- Dynamic workflow patterns based on lead quality and conversation context
- Integration with external systems for team assignments and demo scheduling
- Session management to prevent duplicate processing of conversations
- Model-specific optimization for different agent roles
- End-to-end automation without human intervention in the process loop
- Single-turn processing where each customer message yields exactly one response

## 2. Capability-Based Collaboration Model

The system employs a capability-based architecture where agents are selected based on their capabilities rather than hardcoded references:

```mermaid
graph TD
    subgraph "Core Components"
        CF[Coordination Function]
        AR[Agent Registry]
        Agents[Specialized Agents]
        Capabilities[Capability Definitions]
    end

    CF -->|"Request capability"| AR
    AR -->|"Return agent"| CF
    AR -->|"Maps capabilities to"| Agents
    Capabilities -->|"Define"| AR
```

### 2.1 Key Components

- **Coordination Function**: Determines which capability is needed at each step of the workflow
- **Agent Registry**: Maps capabilities to agent instances, enabling dynamic agent selection
- **Capabilities**: Well-defined actions like `classify_lead`, `craft_response`, or `execute_actions`
- **Specialized Agents**: Each performs a specific task in the lead nurturing process
- **Session Manager**: Manages agent chat sessions to prevent duplicate processing

### 2.2 Capability Selection Process

```mermaid
sequenceDiagram
    participant CF as Coordination Function
    participant AR as Agent Registry
    participant Agent as Agent

    CF->>AR: Request capability (e.g., "classify_lead")
    AR->>Agent: Activate appropriate agent
    Agent->>CF: Return specialized output
    CF->>CF: Determine next capability needed
```

### 2.3 Capability to Agent Mapping

| Capability | Agent | Purpose |
|------------|-------|---------|
| `classify_lead` | LeadClassifierAgent | Categorize leads by quality |
| `decide_action` | DecisionAgent | Determine next steps based on classification |
| `define_strategy` | StrategyAgent | Develop nurturing approach for warm leads |
| `craft_response` | ResponseCrafterAgent | Generic capability for creating customer messages |
| `craft_standard_response` | ResponseCrafterAgent | Create personalized customer messages for standard interactions |
| `craft_transition_response` | TransitioningResponseCrafterAgent | Create messages for conversation transitions (handoffs) |
| `retrieve_knowledge` | KnowledgeRetrievalAgent | Retrieve relevant information |
| `assign_lead` | LeadAssignmentPlanningAgent | Handle team assignments |
| `schedule_demo` | DemoSchedulingPlanningAgent | Process demo requests |
| `review_response` | ReviewerAgent | Quality-check outgoing messages |
| `gather_info` | InformationGatheringResponseCrafterAgent | Request missing information |
| `confirm_actions` | ConfirmationAgent | Determine if the user has confirmed a proposed action |
| `execute_actions` | ActionAgent | Perform system operations |

## 3. Agent Roles and Workflows

The system orchestrates various agents in specialized workflows to handle different lead scenarios.

### 3.1 Agent Interaction Overview

```mermaid
graph TD
    Customer[Customer] <--> RC[ResponseCrafterAgent]
    Customer <--> IG[InformationGatheringResponseCrafterAgent]
    Customer <--> TR[TransitioningResponseCrafterAgent]

    subgraph "Lead Nurturing Agent Collaboration"
        CF[Coordination Function]

        subgraph "Core Agents"
            LC[LeadClassifierAgent]
            DC[DecisionAgent]
            ST[StrategyAgent]
            KR[KnowledgeRetrievalAgent]
            RV[ReviewerAgent]
        end

        subgraph "Response Agents"
            RC[ResponseCrafterAgent]
            TR[TransitioningResponseCrafterAgent]
            IG[InformationGatheringResponseCrafterAgent]
        end

        subgraph "Planning Agents"
            LAA[LeadAssignmentPlanningAgent]
            DSA[DemoSchedulingPlanningAgent]
        end

        subgraph "Action Agents"
            CA[ConfirmationAgent]
            AA[ActionAgent]
        end

        SM[SessionManager]

        CF -->|Initial| LC
        LC -->|Classification| CF
        CF -->|After Classification| DC
        DC -->|Decision| CF
        CF -->|If nurturing needed| ST
        ST -->|Strategy| CF
        CF -->|If knowledge needed| KR
        KR -->|Knowledge| CF
        CF -->|For standard responses| RC
        RC -->|Response| CF
        CF -->|If team assignment needed| LAA
        LAA -->|Team assignment decision| CF
        CF -->|If demo needed| DSA
        DSA -->|Demo scheduling decision| CF
        CF -->|If info gathering needed| IG
        IG -->|Information request| CF
        CF -->|If transitioning conversation| TR
        TR -->|Transition message| CF
        CF -->|If action confirmation needed| CA
        CA -->|Confirmation status| CF
        CF -->|If action confirmed| AA
        AA -->|Action completed| CF
        CF -->|Review message| RV
        RV -->|Approved/Rejected| CF

        SM -.->|Manages| CF
    end

    LAA -->|Team Assignment| External[External Systems]
    DSA -->|Demo Scheduling| External
    AA -->|Action Execution| External
```

### 3.2 Core Interaction Flows

The system operates through four primary interaction patterns:

#### 3.2.1 Lead Classification Flow
```mermaid
flowchart LR
    C[Customer Message] --> CL[classify_lead]
    CL --> DA[decide_action]
    DA -->|continue_nurturing| DS[define_strategy]
    DA -->|assign to team| AL[assign_lead]
    DA -->|schedule demo| SD[schedule_demo]
```

#### 3.2.2 Knowledge and Response Flow
```mermaid
flowchart LR
    DS[define_strategy] -->|needs knowledge| RK[retrieve_knowledge]
    DS -->|no knowledge needed| CSR[craft_standard_response]
    RK --> CSR[craft_standard_response]
    CSR --> RR[review_response]
    RR -->|rejected| CSR
    RR -->|approved| CM[Customer]
```

#### 3.2.3 Lead Assignment Flow
```mermaid
flowchart LR
    AL[assign_lead] --> CA[confirm_actions]
    CA -->|confirmed| EA[execute_actions]
    CA -->|not confirmed| CTR[craft_transition_response]
    EA -->|team assigned| CTR[craft_transition_response]
    CTR --> RR[review_response]
    RR -->|approved| CM[Customer]
```

#### 3.2.4 Demo Scheduling Flow
```mermaid
flowchart LR
    SD[schedule_demo] -->|missing info| GI[gather_info]
    SD -->|complete info| CA[confirm_actions]
    GI --> RR[review_response]
    RR -->|approved| CM[Customer]
    CA -->|confirmed| EA[execute_actions]
    CA -->|not confirmed| CTR[craft_transition_response]
    EA --> CTR[craft_transition_response]
    CTR --> RR
```

### 3.3 Chat Session Flow

Each agent group chat session processes exactly one customer message and produces one response:

```mermaid
sequenceDiagram
    participant Customer
    participant System as Chat System
    participant SessionManager
    participant AgentGroup as Lead Nurturing Agent Group

    Customer->>System: Message 1
    System->>SessionManager: Register new session
    SessionManager->>SessionManager: Cancel previous incomplete sessions
    SessionManager->>AgentGroup: Initialize chat session 1
    AgentGroup->>System: Generate response 1
    System->>Customer: Response 1

    Customer->>System: Message 2
    System->>SessionManager: Register new session
    SessionManager->>SessionManager: Cancel previous incomplete sessions
    SessionManager->>AgentGroup: Initialize chat session 2
    AgentGroup->>System: Generate response 2
    System->>Customer: Response 2

    Note over Customer,AgentGroup: Each message from customer starts a new agent group chat session
```

## 4. Agent Descriptions

### 4.1 Core Agents

1. **LeadClassifierAgent**
   - Classifies leads into hot, warm, cold, or existing_customer based on conversation analysis
   - Produces a lead score and detailed reasoning
   - Output format:
     ```json
     {
       "agent_name": "LeadClassifierAgent",
       "reasoning": "detailed reasoning for classification",
       "score": "85",
       "classification": "hot"
     }
     ```

2. **DecisionAgent**
   - Determines next action based on lead classification and conversation context
   - Can override classification based on specific indicators
   - Decides whether to continue nurturing or assign to a team
   - Detects demo requests and initiates demo scheduling when available
   - Output includes a "decision" field (e.g., "continue_nurturing", "assign_hot", "schedule_demo")

3. **StrategyAgent**
   - Advises on nurturing strategies for warm leads
   - Identifies needed knowledge
   - Provides communication style guidance
   - Output format:
     ```json
     {
       "agent_name": "StrategyAgent",
       "need_knowledge_reasoning": "reasoning for knowledge request",
       "need_knowledge": "specific knowledge query (or null)",
       "strategy": "detailed nurturing strategy guidance"
     }
     ```

4. **KnowledgeRetrievalAgent**
   - Retrieves relevant information from knowledge base
   - Evaluates if retrieved knowledge answers queries
   - Handles knowledge insufficiency scenarios
   - Uses a smaller model (GPT-4-1-MINI) to optimize for knowledge processing
   - Output format:
     ```json
     {
       "agent_name": "KnowledgeRetrievalAgent",
       "knowledge_overview": "summary of relevant information",
       "decision": null
     }
     ```

5. **ReviewerAgent**
   - Reviews proposed responses for quality
   - Checks for placeholder content and proper knowledge integration
   - Ensures correct language is used
   - Output includes a "review" field with "approved" or "rejected with feedback"

### 4.2 Response Generation Agents

6. **ResponseCrafterAgent**
   - Creates personalized customer responses based on strategy and knowledge
   - Handles the `craft_standard_response` capability for normal conversation
   - Ensures responses are relevant and engaging
   - Focuses on standard conversational responses during lead nurturing
   - Uses a more powerful model (FLASH-2-5) optimized for response generation
   - Output includes a "response" field with the crafted message

7. **TransitioningResponseCrafterAgent**
   - Handles the `craft_transition_response` capability
   - Crafts responses for conversation transitions and handoffs
   - Specializes in creating professional messages when a lead is being assigned to a team
   - Creates clear, informative messages about next steps after demo scheduling
   - Uses a more powerful model (FLASH-2-5) optimized for response generation
   - Output includes a "response" field with the transition message

8. **InformationGatheringResponseCrafterAgent**
   - Crafts messages specifically to gather additional customer information
   - Tracks gathered and missing information
   - Creates natural, conversational information requests
   - Uses a more powerful model (FLASH-2-5) optimized for response generation
   - Output format:
     ```json
     {
       "agent_name": "InformationGatheringResponseCrafterAgent",
       "invalid_fields": [
         {
           "field_name": "email",
           "field_reasoning": "reasoning for asking for email"
         }
       ],
       "response_reasoning": "thought process behind crafting the message",
       "response": "natural message asking for information"
     }
     ```

### 4.3 Planning Agents

9. **LeadAssignmentPlanningAgent**
   - Focuses specifically on team assignment based on lead classification
   - Determines appropriate team based on classification and context
   - Creates detailed assignment records with conversation summaries
   - Does not directly communicate with customers
   - Output format:
     ```json
     {
       "agent_name": "LeadAssignmentPlanningAgent",
       "plan_type": "assign_lead",
       "lead_assignment_action": {
         "lead_classification": "hot/warm/cold classification",
         "lead_score": "numeric score (0-100)",
         "target_team_reasoning": "reasoning for team selection",
         "target_team": "selected team name",
         "assignment_reason": "detailed explanation for assignment",
         "conversation_summary": "summary of the conversation"
       }
     }
     ```

10. **DemoSchedulingPlanningAgent**
    - Specializes in handling demo requests
    - Performs thorough analysis of entire conversation to extract field information
    - Identifies both direct mentions (e.g., "My name is John") and indirect mentions (e.g., "Please call me John")
    - Can combine partial field information (e.g., combining country code "+1" from one message with local number "555-987-6543" from another)
    - Validates each field value against its requirements
    - Provides detailed extraction reasoning and validation reasoning for each field
    - Output format:
      ```json
      {
        "agent_name": "DemoSchedulingPlanningAgent",
        "extracted_fields": [
          {
            "field_name": "first_name",
            "is_required": true,
            "extraction_reasoning": "found 'John' in message",
            "field_value": "John",
            "validation_reasoning": "valid name format",
            "is_valid_field_value": true
          }
        ],
        "demo_scheduling_action": {
          "lead_classification": "hot",
          "lead_score": "85",
          "fields": [
            {
              "field_name": "first_name",
              "field_value": "John"
            }
          ]
        },
        "plan_type": "schedule_demo" // or "modification_required" if fields are missing
      }
      ```

### 4.4 Action Agents

11. **ConfirmationAgent**
    - Determines if the user has explicitly confirmed or declined a proposed action
    - Analyzes conversation history to identify the most recent confirmation question
    - Examines user responses for explicit confirmation or declination
    - Provides suggested confirmation questions when no confirmation has been asked
    - Can distinguish between specific detail confirmation (e.g., confirming a phone number) and action confirmation
    - Output format:
      ```json
      {
        "agent_name": "ConfirmationAgent",
        "reasoning": "detailed reasoning for confirmation status",
        "confirmation_status": "confirmed" or "not confirmed",
        "confirmation_questions": [
          "suggested confirmation question 1",
          "suggested confirmation question 2"
        ]
      }
      ```
      Note: `confirmation_questions` is only included when confirmation_status is "not confirmed" and no confirmation question has been asked

12. **ActionAgent**
    - Implements a two-phase execution process:
      1. Execution Phase: Directly calls plugin tools to execute the actions (no structured output)
      2. Report Phase: Reports the outcomes with detailed results (returns structured JSON)
    - Directly uses SleekflowToolsPlugin and ChiliPiperToolsPlugin to perform operations
    - For lead assignment: calls appropriate functions to assign the lead to a team and create tracking objects
    - For demo scheduling: validates fields and schedules demos through ChiliPiperToolsPlugin
    - Provides structured feedback on actions taken and their outcomes
    - Acts as a reliable interface between decision-making agents and external systems
    - Uses a more powerful model (GPT-4-1) to ensure reliability for system operations
    - Does not directly communicate with customers
    - During Execution Phase: Directly calls tools without returning structured output
    - Output format for Report Phase:
      ```json
      {
        "agent_name": "ActionAgent",
        "executed_tools": [
          {
            "tool_name": "AssignToTeam",
            "args": [
              {"name": "target_team", "value": "sales"},
              {"name": "reason", "value": "Hot lead interested in enterprise plan"}
            ],
            "tool_result": "Assigned",
            "outcome": "Team assignment successful"
          }
        ],
        "result": "success" or "failure"
      }
      ```

## 5. Technical Implementation

### 5.1 Key Components

1. **AgentRegistry**: A central registry that maps capabilities to agent instances, enabling dynamic selection of agents based on the required capability.

2. **LeadNurturingAgentDefinitions**: Defines all agents, their instructions, and response schemas. Each agent has a dedicated method that sets up its parameters, execution settings, and detailed task instructions.

3. **LeadNurturingAgentActionsDefinitions**: Handles output from the LeadClassifierAgent, LeadAssignmentPlanningAgent, and DemoSchedulingPlanningAgent, integrating with external systems for team assignments and demo scheduling.

4. **LeadNurturingCollaborationDefinition**: Orchestrates the agent collaboration, creating and configuring the agent group, selection strategy, termination conditions, and message interception.

5. **LeadNurturingCollaborationChatCacheService**: Provides caching for knowledge and context during conversations to enable agents to access previously retrieved information.

6. **LeadNurturingAgentSessionManager**: Manages agent group chat sessions to prevent duplicate processing, tracks session status, and handles session cancellation.

### 5.2 Technical Implementation of Agent Registry

```mermaid
classDiagram
    class AgentRegistry {
        +RegisterAgent(agent, capabilities)
        +GetAgentForCapability(capability)
        +HasCapability(capability)
    }

    class AgentCapabilities {
        +ClassifyLead
        +DecideAction
        +DefineStrategy
        +CraftResponse
        +CraftStandardResponse
        +CraftTransitionResponse
        +RetrieveKnowledge
        +AssignLead
        +ScheduleDemo
        +ReviewResponse
        +GatherInfo
        +ConfirmActions
        +ExecuteActions
    }

    class AgentSessionManager {
        +RegisterAgentSessionAsync(groupChatId)
        +CancelPreviousSessionsAsync(groupChatId)
        +MarkSessionActionsPerformedAsync(groupChatId)
        +ShouldCancelSessionAsync(groupChatId)
    }

    AgentRegistry --> "1..*" Agent: registers
    Agent --> "1..*" AgentCapabilities: provides
    AgentSessionManager --> "1..*" Agent: manages

    class Agent {
        +Name
        +Execute()
    }
```

The coordination function follows a stateless, message-driven architecture to determine the next capability:

```csharp
// This coordination function manages the flow of the agent conversation using capabilities.
// Each chat session is designed to generate exactly one response to the user:
// - Regular response (via craft_response capability)
// - Information gathering request (via gather_info capability)
// - Transition message (via craft_response capability with TransitioningResponseCrafterAgent)
//
// After the review_response capability approves a response, the chat session ends.
// A new chat session begins when the customer responds.
return KernelFunctionFactory.CreateFromMethod(
    (string history) =>
    {
        var messages = JsonConvert.DeserializeObject<ChatHistoryRecord[]>(history);

        if (messages == null || messages.Length == 0)
        {
            return AgentCapabilities.ClassifyLead;
        }

        // Examine the most recent message to determine the next capability
        if (!JsonUtils.TryParseJson<Dictionary<string, object?>>(messages[0].Content, out var message))
        {
            return AgentCapabilities.ClassifyLead;
        }

        // 1. First check for classification message
        if (message!.ContainsKey("classification"))
        {
            return AgentCapabilities.DecideAction;
        }

        // 2. Check for the DecisionAgent's decision
        // ... additional logic

        // 3. Check for Planning Agents' output with plan_type
        if (message.TryGetValue("plan_type", out var actionTypeObj))
        {
            var actionType = actionTypeObj as string ?? string.Empty;

            // Check if information is sufficient for demo scheduling
            if (actionType == "modification_required")
            {
                return AgentCapabilities.GatherInfo;
            }

            // For actual actions (assign_lead, schedule_demo), route to the confirm_action capability
            if (actionType is "assign_lead" or "schedule_demo")
            {
                return AgentCapabilities.ConfirmActions;
            }

            // ... additional plan_type handling
        }

        // 4. Check for ConfirmationAgent output
        var agentName = message.TryGetValue("agent_name", out var agent)
            ? agent as string ?? string.Empty
            : string.Empty;

        if (agentName == "ConfirmationAgent")
        {
            if (message.TryGetValue("confirmation_status", out var confirmationStatusObj) &&
                confirmationStatusObj is string confirmationStatus)
            {
                if (confirmationStatus == "confirmed")
                {
                    return AgentCapabilities.ExecuteActions;
                }
                else // "not confirmed" or "cancelled"
                {
                    return AgentCapabilities.CraftTransitionResponse;
                }
            }
        }

        // ... additional message examination logic

        // Default to classify_lead if nothing else matches
        return AgentCapabilities.ClassifyLead;
    }
)
```

The coordination function examines the most recent message from any agent and uses a series of content-based rules to determine the next required capability. This enables a flexible, data-driven workflow that adapts to the conversation context.

### 5.3 Detailed Information Flow

```mermaid
sequenceDiagram
    participant Customer
    participant Coordinator as Coordination Function
    participant Registry as Agent Registry
    participant SessionManager
    participant LC as LeadClassifierAgent
    participant DC as DecisionAgent
    participant ST as StrategyAgent
    participant KR as KnowledgeRetrievalAgent
    participant LAA as LeadAssignmentPlanningAgent
    participant DSA as DemoSchedulingPlanningAgent
    participant CA as ConfirmationAgent
    participant AA as ActionAgent
    participant IG as InformationGatheringResponseCrafterAgent
    participant RC as ResponseCrafterAgent
    participant TR as TransitioningResponseCrafterAgent
    participant RV as ReviewerAgent

    Customer->>Coordinator: Initial Message
    Coordinator->>SessionManager: Register new session
    SessionManager->>SessionManager: Cancel previous incomplete sessions
    Coordinator->>Registry: Request classify_lead capability
    Registry->>LC: Activate agent
    LC->>Coordinator: Classification

    Coordinator->>Registry: Request decide_action capability
    Registry->>DC: Activate agent

    alt Continue Nurturing
        DC->>Coordinator: Decision to nurture
        Coordinator->>Registry: Request define_strategy capability
        Registry->>ST: Activate agent
        ST->>Coordinator: Request knowledge
        Coordinator->>Registry: Request retrieve_knowledge capability
        Registry->>KR: Activate agent
        KR->>Coordinator: Provide knowledge
        Coordinator->>Registry: Request craft_response capability
        Registry->>RC: Activate agent
        RC->>Coordinator: Draft response
        Coordinator->>Registry: Request review_response capability
        Registry->>RV: Activate agent
        RV->>Customer: Approved response
    else Request Demo
        DC->>Coordinator: Decision to schedule demo
        Coordinator->>Registry: Request schedule_demo capability
        Registry->>DSA: Activate agent

        alt Missing Required Fields
            DSA->>Coordinator: Modification required
            Coordinator->>Registry: Request gather_info capability
            Registry->>IG: Activate agent
            IG->>Coordinator: Draft info request
            Coordinator->>Registry: Request review_response capability
            Registry->>RV: Activate agent
            RV->>Customer: Approved info request

            Note right of Customer: New chat session starts
            Customer->>Coordinator: Provides information
            Coordinator->>SessionManager: Register new session
            SessionManager->>SessionManager: Cancel previous incomplete sessions
            Coordinator->>Registry: Request classify_lead capability
            Registry->>LC: Activate agent
            LC->>Coordinator: Classification
            Coordinator->>Registry: Request decide_action capability
            Registry->>DC: Activate agent
            DC->>Coordinator: Forward to schedule_demo
            Coordinator->>Registry: Request schedule_demo capability
            Registry->>DSA: Activate agent

            alt Still Missing Required Fields
                DSA->>Coordinator: Modification required
                Coordinator->>Registry: Request gather_info capability
                Registry->>IG: Activate agent
                IG->>Coordinator: Draft additional request
                Coordinator->>Registry: Request review_response capability
                Registry->>RV: Activate agent
                RV->>Customer: Approved additional request
            else All Required Fields Present
                DSA->>Coordinator: Schedule demo
                Coordinator->>Registry: Request confirm_actions capability
                Registry->>CA: Activate agent
                alt Confirmation Needed
                    CA->>Coordinator: Not confirmed, need confirmation
                    Coordinator->>Registry: Request craft_transition_response capability
                    Registry->>TR: Activate agent
                    TR->>Coordinator: Draft confirmation request
                    Coordinator->>Registry: Request review_response capability
                    Registry->>RV: Activate agent
                    RV->>Customer: Approved confirmation request
                    Note right of Customer: New chat session starts with customer's confirmation
                else User Already Confirmed
                    CA->>Coordinator: Confirmed
                    Coordinator->>Registry: Request execute_actions capability
                    Registry->>AA: Activate agent
                    AA->>SessionManager: Mark session actions performed
                    AA->>Coordinator: Action completed
                    Coordinator->>Registry: Request craft_transition_response capability
                    Registry->>TR: Activate agent
                    TR->>Coordinator: Draft confirmation
                    Coordinator->>Registry: Request review_response capability
                    Registry->>RV: Activate agent
                    RV->>Customer: Approved confirmation
                end
            end
        else All Required Fields Present
            DSA->>Coordinator: Schedule demo
            Coordinator->>Registry: Request confirm_actions capability
            Registry->>CA: Activate agent
            CA->>Coordinator: Confirmation status
            Coordinator->>Registry: Request execute_actions capability
            Registry->>AA: Activate agent
            AA->>SessionManager: Mark session actions performed
            AA->>Coordinator: Action completed
            Coordinator->>Registry: Request craft_transition_response capability
            Registry->>TR: Activate agent
            TR->>Coordinator: Draft confirmation
            Coordinator->>Registry: Request review_response capability
            Registry->>RV: Activate agent
            RV->>Customer: Approved confirmation
        end
    else Assign to Team
        DC->>Coordinator: Decision to assign
        Coordinator->>Registry: Request assign_lead capability
        Registry->>LAA: Activate agent
        LAA->>Coordinator: Team assignment info
        Coordinator->>Registry: Request confirm_actions capability
        Registry->>CA: Activate agent
        CA->>Coordinator: Confirmation status
        Coordinator->>Registry: Request execute_actions capability
        Registry->>AA: Activate agent
        AA->>SessionManager: Mark session actions performed
        AA->>Coordinator: Action completed
        Coordinator->>Registry: Request craft_transition_response capability
        Registry->>TR: Activate agent
        TR->>Coordinator: Draft transition message
        Coordinator->>Registry: Request review_response capability
        Registry->>RV: Activate agent
        RV->>Customer: Approved transition message
    end
```

#### 5.3.1 Fully Automated End-to-End Process

It's important to emphasize that the entire flow described above occurs programmatically without any human intervention in the loop. All decisions, actions, and transitions between agents happen automatically through the coordination function and agent registry:

1. **Automatic Decision Making**: All agent decisions (from lead classification to action execution) are made algorithmically based on conversation context and predefined rules, without requiring human review or approval.

2. **Seamless Agent Transitions**: The coordination function analyzes message content and automatically routes to the appropriate agent capability, with no manual handoff points.

3. **Automated Action Execution**: When the system decides to perform actions like team assignments or demo scheduling, these are executed programmatically by the ActionAgent invoking system operations through plugins, with no human operator required to complete these actions.

4. **Self-Reviewing Process**: The ReviewerAgent programmatically evaluates responses against quality criteria and gives approval without human oversight. No message requires human review before being sent to the customer.

5. **Autonomous Session Management**: The SessionManager automatically manages conversation state and prevents duplicate processing without requiring manual intervention.

6. **One-Turn Processing Model**: Each customer interaction is processed as a single turn, regardless of complexity:
   - One customer message initiates exactly one agent group chat session
   - Within this session, multiple agents may be activated in sequence
   - Multiple system actions may be performed (classification, knowledge retrieval, team assignment, etc.)
   - Only one response is sent back to the customer
   - The group chat session is terminated once the ReviewerAgent approves a response
   - A new customer message starts a completely new agent group chat session

This fully automated, single-turn approach enables consistent, scalable lead nurturing while maintaining contextual awareness across the entire conversation lifecycle.

### 5.4 Capability Resolution and Mapping

The system uses a sophisticated capability resolution mechanism within the `CreateSelectionStrategy` method:

```csharp
ResultParser = (result) =>
{
    try
    {
        var capabilityName = result.GetValue<string>();
        if (string.IsNullOrEmpty(capabilityName))
        {
            _logger.LogWarning("Empty capability name received from coordinating function. Using default capability.");
            return _agentRegistry.GetAgentForCapability(AgentCapabilities.ClassifyLead).Name;
        }

        // Handle generic CraftResponse capability - map to CraftStandardResponse
        if (capabilityName == AgentCapabilities.CraftResponse)
        {
            capabilityName = AgentCapabilities.CraftStandardResponse;
        }

        if (_agentRegistry.HasCapability(capabilityName))
        {
            return _agentRegistry.GetAgentForCapability(capabilityName).Name;
        }
        else
        {
            _logger.LogWarning($"Unknown capability: {capabilityName}. Using default capability.");
            return _agentRegistry.GetAgentForCapability(AgentCapabilities.ClassifyLead).Name;
        }
    }
    catch (Exception e)
    {
        _logger.LogWarning(
            e,
            "Failed to parse result from coordinating function. Defaulting to ClassifyLead capability.");

        return _agentRegistry.GetAgentForCapability(AgentCapabilities.ClassifyLead).Name;
    }
}
```

Key features of the capability resolution include:

1. **Capability Mapping**: The generic `craft_response` capability is automatically mapped to the more specific `craft_standard_response` capability.

2. **Default Fallback**: If a capability doesn't exist or an error occurs, the system defaults to the `classify_lead` capability.

3. **Registry Lookup**: The system uses the AgentRegistry to translate capability names into actual agent instances.

4. **Error Handling**: Comprehensive error handling ensures the system can recover gracefully from any issues in capability resolution.

This approach allows for backward compatibility with systems that might request the generic `craft_response` capability while maintaining the architectural benefits of specialized capabilities.

### 5.5 Message Processing and Formatting

The system includes specialized handling for responses to ensure proper formatting:

```csharp
public override async Task<string> GetFinalReplyAsync(AgentGroupChat agentGroupChat)
{
    // Extract final reply from JSON
    var chatMessages = await agentGroupChat.GetChatMessagesAsync().ToListAsync();
    var finalReplyKey = GetFinalReplyTag();

    // Find the message containing the final reply
    var finalChatMessage = chatMessages
        .FirstOrDefault(x =>
            x.Content != null
            && JsonUtils.TryParseJson<Dictionary<string, object>>(x.Content, out var json)
            && json != null
            && json.ContainsKey(finalReplyKey))
        ?.Content;

    if (finalChatMessage == null ||
        !JsonUtils.TryParseJson<Dictionary<string, object>>(finalChatMessage, out var finalJson))
    {
        return string.Empty;
    }

    var finalReplyToCustomer = finalJson?[finalReplyKey].ToString()?.Trim() ?? string.Empty;

    // Decode HTML entities
    finalReplyToCustomer = WebUtility.HtmlDecode(finalReplyToCustomer);

    // Convert HTML-style formatting tags to WhatsApp markdown format
    finalReplyToCustomer = WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(finalReplyToCustomer);

    return finalReplyToCustomer;
}
```

Key features include:
1. **HTML Entity Decoding**: Ensures emojis and special characters are properly displayed
2. **Format Conversion**: Converts HTML-style tags to WhatsApp markdown format for proper rendering
3. **Structured Response Extraction**: Extracts the final response from the structured agent output

## 6. Benefits of Capability-Based Design

The capability-based approach offers several advantages:

1. **Modularity**: Agents can be added, removed, or replaced dynamically without altering the core coordination logic.

2. **Extensibility**: New capabilities and corresponding agents can be introduced with minimal code changes.

3. **Decoupling**: Dependencies between agents are reduced, simplifying maintenance and updates.

4. **Scalability**: The structure supports multiple agents per capability, allowing for load balancing or specialized implementations.

5. **Clarity**: The system's behavior is more transparent as it's driven by a clear set of capabilities rather than hardcoded agent references.

6. **Session Management**: Prevents duplicate processing of the same conversation by multiple agent sessions.

7. **Model-Specific Optimization**: Different tasks use appropriate models, balancing performance and cost.

8. **Agent Specialization**: Dividing agents into Core, Response, Planning, and Action categories improves maintainability and clarity.

## 7. Extension and Customization

The lead nurturing system is designed to be extensible:

1. **Tools Configuration**: The `LeadNurturingTools` class allows configuring assignment rules, demo requirements, and other tool-specific settings.
   - Demo scheduling is only available when `DemoTool` is configured (non-null)
   - Team assignment rules can be customized through the `AssignmentTool`

2. **Agent Customization**: Each agent can be customized by modifying its prompt in the `LeadNurturingAgentDefinitions` class.

3. **New Actions**: New action types can be added to the `DecisionAgent` and corresponding handlers in the Planning Agents and the `LeadNurturingAgentActionsDefinitions` class.

4. **Custom Agents**: Additional specialized agents can be integrated by adding them to the agent definitions, registering them with appropriate capabilities, and updating the coordination function if needed.

5. **New Capabilities**: New capabilities can be defined in the `AgentCapabilities` class and mapped to appropriate agents.

6. **Custom Model Selection**: Different models can be selected for different agents through the `GetPromptExecutionSettings` method.
