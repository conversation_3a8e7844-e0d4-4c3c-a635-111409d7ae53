using MassTransit;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.States;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class StreamingSendMessageRequestEventConsumerDefinition : ConsumerDefinition<StreamingSendMessageRequestEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<StreamingSendMessageRequestEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class StreamingSendMessageRequestEventConsumer : IConsumer<StreamingSendMessageRequest>
{
    private readonly ICoreCommander _coreCommander;
    private readonly IStateService _stateService;

    public StreamingSendMessageRequestEventConsumer(
        ICoreCommander coreCommander,
        IStateService stateService)
    {
        _coreCommander = coreCommander;
        _stateService = stateService;
    }

    public async Task Consume(ConsumeContext<StreamingSendMessageRequest> context)
    {
        foreach (var subscription in context.Message.StreamingSendMessageSubscriptions)
        {
            var state = await _stateService.GetProxyStateAsync(subscription.ProxyStateId);

            subscription.SendMessageInput.MessageBody.TextMessage!.Text =
                context.Message.PartialRecommendedReply;

            await _coreCommander.ExecuteAsync(
                state.Origin,
                "SendMessage",
                subscription.SendMessageInput);
        }

        await context.RespondAsync(new StreamingSendMessageResponse(context.Message.CorrelationId));
    }
}