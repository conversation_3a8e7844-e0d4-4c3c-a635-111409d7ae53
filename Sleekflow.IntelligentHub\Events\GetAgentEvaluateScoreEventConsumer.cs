using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Reviewers;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.IntelligentHub.Consumers;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentEvaluateScoreEventConsumerDefinition : ConsumerDefinition<GetAgentEvaluateScoreEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentEvaluateScoreEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentEvaluateScoreEventConsumer : FlowHubAgentGenericConsumer<GetAgentEvaluateScoreEvent>, IConsumer<GetAgentEvaluateScoreEvent>
{
    private readonly IReviewerService _reviewerService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GetAgentEvaluateScoreEventConsumer(
        IBus bus,
        IReviewerService reviewerService,
        ILogger<GetAgentEvaluateScoreEventConsumer> logger,
        ICompanyAgentConfigService companyAgentConfigService,
        IIntelligentHubUsageService intelligentHubUsageService)
        : base(logger, bus)
    {
        _reviewerService = reviewerService;
        _companyAgentConfigService = companyAgentConfigService;
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    protected override async Task HandleMessageAsync(ConsumeContext<GetAgentEvaluateScoreEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var conversationContext = message.ConversationContext;
        var additionalPrompt = message.AdditionalPrompt;

        using var d1 = Serilog.Context.LogContext.PushProperty("SleekflowCompanyId", sleekflowCompanyId);
        using var d2 = Serilog.Context.LogContext.PushProperty("StateId", message.ProxyStateId);

        _logger.LogInformation(
            "Getting evaluate score for {SleekflowCompanyId} {ConversationContext}",
            sleekflowCompanyId,
            JsonConvert.SerializeObject(conversationContext, JsonConfig.DefaultLoggingJsonSerializerSettings));

        EvaluatedScore? evaluatedScore = null;
        try
        {
            evaluatedScore = await _reviewerService.GetEvaluateScoreAsync(
                conversationContext,
                additionalPrompt);

            _logger.LogInformation("Evaluated score: {EvaluatedScore}", JsonConvert.SerializeObject(evaluatedScore));

            await _intelligentHubUsageService.RecordUsageAsync(
                sleekflowCompanyId,
                PriceableFeatures.Scoring,
                null,
                new EvaluatedScoreSnapshot(
                    conversationContext,
                    evaluatedScore));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get evaluate score");
        }

        var response = new GetAgentEvaluateScoreEvent.Response(
            evaluatedScore?.Category ?? "Unknown",
            evaluatedScore?.Score ?? 0,
            evaluatedScore?.Reason ?? "Failed to evaluate");

        _logger.LogInformation(
            "Agent evaluate score published to OnAgentCompleteStepActivationEvent {Response} {ProxyStateId} {AggregateStepId} {StackEntries}",
            JsonConvert.SerializeObject(response),
            message.ProxyStateId,
            message.AggregateStepId,
            JsonConvert.SerializeObject(message.StackEntries));

        await _bus.Publish(
            new OnAgentCompleteStepActivationEvent(
                message.AggregateStepId,
                message.ProxyStateId,
                message.StackEntries,
                JsonConvert.SerializeObject(response)));
    }
}