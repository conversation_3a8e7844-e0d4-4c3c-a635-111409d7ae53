﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class CreateWebScraperTask : ITrigger<CreateWebScraperTask.CreateWebScraperTaskInput, CreateWebScraperTask.CreateWebScraperTaskOutput>
{
    private readonly IWebScraperService _webScraperService;

    public CreateWebScraperTask(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class CreateWebScraperTaskInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameDisplayName)]
        public string DisplayName { get; set; }

        [Required]
        [JsonProperty(WebScraperSetting.PropertyNameWebScraperSetting)]
        [Validations.ValidateObject]
        public WebScraperSetting WebScraperSetting { get; set; }

        [JsonConstructor]
        public CreateWebScraperTaskInput(
            string sleekflowCompanyId,
            string displayName,
            WebScraperSetting webScraperSetting)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DisplayName = displayName;
            WebScraperSetting = webScraperSetting;
        }
    }

    public class CreateWebScraperTaskOutput
    {
        [JsonProperty(WebScraperTask.PropertyNameWebScraperTask)]
        public WebScraperTask WebScraperTask { get; set; }

        [JsonConstructor]
        public CreateWebScraperTaskOutput(WebScraperTask webScraperTask)
        {
            WebScraperTask = webScraperTask;
        }
    }

    public async Task<CreateWebScraperTaskOutput> F(CreateWebScraperTaskInput createWebScraperTaskInput)
    {
        var webScraperTask = await _webScraperService.CreateWebScraperTaskAsync(
            createWebScraperTaskInput.SleekflowCompanyId,
            createWebScraperTaskInput.DisplayName,
            createWebScraperTaskInput.WebScraperSetting);

        return new CreateWebScraperTaskOutput(webScraperTask);
    }
}