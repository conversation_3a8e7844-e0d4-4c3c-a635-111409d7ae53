using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.InternalIntegrationHub.Clients;
using Sleekflow.InternalIntegrationHub.Constants.NetSuite;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.NetSuite;

public interface INetSuiteEmployeeService
{
    public Task<bool> CreateEmployeeAsync(CreateEmployeeRequest request);

    public Task<CommonListResponse> GetEmployeeAsync(bool forceRefresh = false);

    public Task<EmployeeDetailsResponse> GetEmployeeDetailsAsync(string employeeId, bool forceRefresh = false);

    public Task<string?> GetEmployeeIdByEmailAsync(string email);

    public Task<bool> UpdateEmployeeAsync(CreateEmployeeRequest request, string employeeId);

    public Task<bool> UpdateEmployeeSupervisorAsync(UpdateEmployeeRequest request, string employeeId);
}

public class NetSuiteEmployeeService : INetSuiteEmployeeService, IScopedService
{
    private readonly ILogger<NetSuiteEmployeeService> _logger;
    private readonly INetSuiteClient _client;
    private readonly ICacheService _cacheService;

    public NetSuiteEmployeeService(
        ILogger<NetSuiteEmployeeService> logger,
        INetSuiteClient client,
        ICacheService cacheService)
    {
        _logger = logger;
        _client = client;
        _cacheService = cacheService;
    }

    public async Task<CommonListResponse> GetEmployeeAsync(bool forceRefresh = false)
    {
        if (forceRefresh)
        {
            await _cacheService.RemoveCacheAsync(
                $"{nameof(NetSuiteSettingService)}:{nameof(GetEmployeeAsync)}:EmployeeList");
        }

        var employeeList = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetEmployeeAsync)}:EmployeeList",
            async () =>
            {
                var response = await _client.GetAsync<CommonListResponse>(
                    Endpoints.GetEmployeeEndpoint,
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));

        return employeeList;
    }

    public async Task<EmployeeDetailsResponse> GetEmployeeDetailsAsync(string employeeId, bool forceRefresh = false)
    {
        if (forceRefresh)
        {
            await _cacheService.RemoveCacheAsync(
                $"{nameof(NetSuiteSettingService)}:{nameof(GetEmployeeDetailsAsync)}:EmployeeDetails:{employeeId}");
        }

        var employeeDetails = await _cacheService.CacheAsync(
            $"{nameof(NetSuiteSettingService)}:{nameof(GetEmployeeDetailsAsync)}:EmployeeDetails:{employeeId}",
            async () =>
            {
                var response = await _client.GetAsync<EmployeeDetailsResponse>(
                    string.Format(Endpoints.GetEmployeeDetailsEndpoint, employeeId),
                    null);
                if (response.Data is null)
                {
                    throw new SfUserFriendlyException("Failed to connect to NetSuite");
                }

                return response.Data;
            },
            TimeSpan.FromHours(1));

        return employeeDetails;
    }

    public async Task<string?> GetEmployeeIdByEmailAsync(string email)
    {
        var employeeSearch = await _client.GetAsync<CommonSearchResponse>(
            Endpoints.CreateEmployeeEndpoint,
            new Dictionary<string, string>
            {
                {
                    "q", "email IS \"" + email + "\""
                }
            });
        if (employeeSearch.Data?.Items.Count == 0)
        {
            throw new SfUserFriendlyException("Employee not found");
        }

        return employeeSearch.Data?.Items[0].Id;
    }

    public async Task<bool> CreateEmployeeAsync(CreateEmployeeRequest request)
    {
        var response = await _client.PostAsync<object>(
            Endpoints.CreateEmployeeEndpoint,
            request);
        if (response.IsSuccessful == false)
        {
            _logger.LogError(
                "Failed to create employee to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }

    public async Task<bool> UpdateEmployeeAsync(CreateEmployeeRequest request, string employeeId)
    {
        var response = await _client.PatchAsync<object>(
            string.Format(Endpoints.UpdateEmployeeEndpoint, employeeId),
            request);
        if (response.IsSuccessful == false)
        {
            _logger.LogError(
                "Failed to update employee to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }

    public async Task<bool> UpdateEmployeeSupervisorAsync(UpdateEmployeeRequest request, string employeeId)
    {
        var response = await _client.PatchAsync<object>(
            string.Format(Endpoints.UpdateEmployeeEndpoint, employeeId),
            request);
        if (response.IsSuccessful == false)
        {
            _logger.LogError(
                "Failed to update employee supervisor to NetSuite: {Content} Request: {Request}",
                response.Content,
                request.ToString());
        }

        return response.IsSuccessful;
    }
}