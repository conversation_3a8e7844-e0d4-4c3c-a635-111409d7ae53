namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;

public interface ICompactorConfig
{
    string StorageAccountName { get; }
    string StorageAccountKey { get; }
    string EventsContainerName { get; }
    string StorageConnStr { get; }
    string ResultsContainerName { get; }
    int MaxConcurrentCompanies { get; }
    int FileBatchSize { get; }
    int RetryAttempts { get; }
    int ProcessingTimeoutMinutes { get; }
}