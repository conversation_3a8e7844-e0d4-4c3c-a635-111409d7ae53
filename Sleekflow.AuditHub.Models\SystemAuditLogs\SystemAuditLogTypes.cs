namespace Sleekflow.AuditHub.Models.SystemAuditLogs;

public static class SystemAuditLogTypes
{
    public const string
        BroadcastDeleted = "broadcast-deleted"; // BroadcastId, BroadcastName

    public const string
        FlowBuilderEnrolled = "flow-builder-enrolled";

    public const string
        FlowBuilderUnenrolled = "flow-builder-unenrolled";

    public const string
        FlowBuilderWorkflowDeleted = "flow-builder-workflow-deleted";

    public const string FlowBuilderWorkflowPatched = "flow-builder-workflow-patched";

    public const string
        MessageTemplateUnbookmarked =
            "message-template-unbookmarked"; // TemplateId, ChannelType, ChannelIdentityId, TemplateName

    public const string
        PiiMaskingConfigCreated = "pii-masking-config-created"; // PiiMaskingConfigId, DisplayName, RegexPatterns, MaskingCustomObjectSchemaIds, MaskingRoles, MaskingLocations, IsPlatformApiMasked

    public const string
        PiiMaskingConfigDeleted = "pii-masking-config-deleted"; // PiiMaskingConfigId, DisplayName

    public const string
        PiiMaskingConfigUpdated = "pii-masking-config-updated"; // PiiMaskingConfigId, DisplayName, RegexPatterns, MaskingCustomObjectSchemaIds, MaskingRoles, MaskingLocations, IsPlatformApiMasked

    public const string
        Staff2faRevoked = "staff-2fa-revoked"; // StaffId

    public const string
        StaffAddedAsCollaborator =
            "staff-added-as-collaborator"; // StaffId, ConversationId, UserProfileId, UserProfileFirstName, UserProfileLastName

    public const string
        StaffAddedToTeams = "staff-added-to-teams"; // StaffId, TeamIds, TeamNames

    public const string
        StaffAssignedToConversation = "staff-assigned-to-conversation"; // StaffId, ConversationId, UserProfileId

    public const string
        StaffDeleted = "staff-deleted"; // StaffId

    public const string
        StaffRemovedAsCollaborator =
            "staff-removed-as-collaborator"; // StaffId, ConversationId, UserProfileId, UserProfileFirstName, UserProfileLastName

    public const string
        StaffRemovedFromTeams = "staff-removed-from-teams"; // StaffId, TeamIds, TeamNames

    public const string
        StaffRoleUpdated = "staff-role-updated"; // StaffId, Role

    public const string
        StaffStatusUpdated = "staff-status-updated"; // StaffId, Status

    public const string
        StaffUnassignedFromConversation =
            "staff-unassigned-from-conversation"; // StaffId, ConversationId, UserProfileId

    public const string
        TeamDeleted = "team-deleted"; // TeamId, TeamName

    public const string
        UserProfileCreated = "user-profile-created"; // UserProfileId, FirstName, LastName, Email

    public const string
        UserProfileDeleted = "user-profile-deleted"; // UserProfileId, FirstName, LastName, Email

    public const string
        UserProfileLabelCreated = "user-profile-label-created"; // LabelName, LabelColor

    public const string
        UserProfileLabelDeleted = "user-profile-label-deleted"; // LabelName

    public const string
        UserProfileLabelUpdated = "user-profile-label-updated"; // LabelName, LabelColor

    public const string
        UserProfileListCreated = "user-profile-list-created"; // ListId, ListName

    public const string
        UserProfileListDeleted = "user-profile-list-deleted"; // ListId, ListName

    public const string
        UserProfileListExported = "user-profile-list-exported"; // ListId, ListName

    public const string
        UserProfilePropertyUpdated = "user-profile-property-updated"; // UserProfileId, PropertyName, OldValue, NewValue

    public const string
        BusinessHourConfigUpdated = "business-hour-config-updated"; // IsEnabled, WeeklyHoursJString
}