﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnClickToWhatsAppAdsMessageReceivedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;


    public OnClickToWhatsAppAdsMessageReceivedEvent(
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnClickToWhatsAppAdsMessageReceivedEventInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [Required]
        public OnClickToWhatsAppAdsMessageReceivedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnClickToWhatsAppAdsMessageReceivedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnClickToWhatsAppAdsMessageReceivedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnClickToWhatsAppAdsMessageReceivedEventOutput
    {
    }

    public async Task<OnClickToWhatsAppAdsMessageReceivedEventOutput> F(
        OnClickToWhatsAppAdsMessageReceivedEventInput onClickToWhatsAppAdsMessageReceivedEventInput)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                onClickToWhatsAppAdsMessageReceivedEventInput.EventBody,
                onClickToWhatsAppAdsMessageReceivedEventInput.ContactId,
                "Contact",
                onClickToWhatsAppAdsMessageReceivedEventInput.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onClickToWhatsAppAdsMessageReceivedEventInput.SleekflowCompanyId));

        return new OnClickToWhatsAppAdsMessageReceivedEventOutput();
    }
}