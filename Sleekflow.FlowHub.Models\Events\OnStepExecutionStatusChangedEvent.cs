﻿using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.Workers;

namespace Sleekflow.FlowHub.Models.Events;

public class OnStepExecutionStatusChangedEvent : IHighTrafficEvent
{
    public string StateId { get; set; }

    public string StepId { get; set; }

    public string StepExecutionStatus { get; set; }

    public string? WorkerInstanceId { get; set; }

    public UserFriendlyError? Error { get; set; }

    public DateTimeOffset OccurredAt { get; set; } = DateTimeOffset.UtcNow;

    public OnStepExecutionStatusChangedEvent(
        string stateId,
        string stepId,
        string stepExecutionStatus,
        string? workerInstanceId,
        UserFriendlyError? error)
    {
        StateId = stateId;
        StepId = stepId;
        StepExecutionStatus = stepExecutionStatus;
        WorkerInstanceId = workerInstanceId;
        Error = error;
    }
}