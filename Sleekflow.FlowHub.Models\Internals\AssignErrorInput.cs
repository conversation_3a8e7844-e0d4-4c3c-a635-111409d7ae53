using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workers;

namespace Sleekflow.FlowHub.Models.Internals;

public class AssignErrorInput
{
    [JsonProperty("state_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StateId { get; set; }

    [JsonProperty("try_catch_step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string TryCatchStepId { get; set; }

    [JsonProperty("stack_entries")]
    [System.ComponentModel.DataAnnotations.Required]
    [Validations.ValidateObject]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("error")]
    [System.ComponentModel.DataAnnotations.Required]
    [Validations.ValidateObject]
    public UserFriendlyError Error { get; set; }

    [JsonConstructor]
    public AssignErrorInput(
        string stateId,
        string tryCatchStepId,
        Stack<StackEntry> stackEntries,
        UserFriendlyError error)
    {
        StateId = stateId;
        TryCatchStepId = tryCatchStepId;
        StackEntries = stackEntries;
        Error = error;
    }
}