using System.Globalization;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;

namespace Sleekflow.JsonConfigs;

public class MillisecondEpochConverter : DateTimeConverterBase
{
    public override void WriteJson(
        JsonWriter writer,
        object? value,
        JsonSerializer serializer)
    {
        if (value == null)
        {
            writer.WriteRawValue(null);

            return;
        }

        var millisecond = ((DateTimeOffset) value - DateTimeOffset.UnixEpoch)
            .TotalMilliseconds;

        writer.WriteRawValue(Convert.ToInt64(millisecond).ToString(CultureInfo.InvariantCulture));
    }

    public override object? Read<PERSON>son(
        JsonReader reader,
        Type objectType,
        object? existingValue,
        JsonSerializer serializer)
    {
        return reader.Value switch
        {
            null => null,
            double d => DateTimeOffset.UnixEpoch.AddMilliseconds(d),
            long l => DateTimeOffset.UnixEpoch.AddMilliseconds(l),
            _ => null
        };
    }
}