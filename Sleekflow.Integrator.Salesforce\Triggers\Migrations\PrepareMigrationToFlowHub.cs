﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Salesforce.Triggers.Migrations;

[TriggerGroup("Migrations")]
public class PrepareMigrationToFlowHub : ITrigger
{
    private readonly ISalesforceMigrationToFlowHubService _salesforceMigrationToFlowHubService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public PrepareMigrationToFlowHub(
        ISalesforceMigrationToFlowHubService salesforceMigrationToFlowHubService,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceMigrationToFlowHubService = salesforceMigrationToFlowHubService;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class PrepareMigrationToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_types_to_migrate")]
        [Required]
        [ValidateArray]
        public List<string> EntityTypesToMigrate { get; set; }

        [JsonConstructor]
        public PrepareMigrationToFlowHubInput(
            string sleekflowCompanyId,
            List<string> entityTypesToMigrate)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypesToMigrate = entityTypesToMigrate;
        }
    }

    public class PrepareMigrationToFlowHubOutput
    {
        [JsonProperty("created_connection_id")]
        public string CreatedConnectionId { get; set; }

        [JsonConstructor]
        public PrepareMigrationToFlowHubOutput(
            string createdConnectionId)
        {
            CreatedConnectionId = createdConnectionId;
        }
    }

    public async Task<PrepareMigrationToFlowHubOutput> F(
        PrepareMigrationToFlowHubInput prepareMigrationToFlowHubInput)
    {
        await _salesforceMigrationToFlowHubService.PrepareMigrationToFlowHubAsync(
            prepareMigrationToFlowHubInput.SleekflowCompanyId,
            prepareMigrationToFlowHubInput.EntityTypesToMigrate);

        // Old Salesforce authentications use SleekflowCompanyId as Id
        var connection = await _salesforceConnectionService.GetByAuthenticationIdAsync(
            prepareMigrationToFlowHubInput.SleekflowCompanyId,
            prepareMigrationToFlowHubInput.SleekflowCompanyId);

        return new PrepareMigrationToFlowHubOutput(connection!.Id);
    }
}