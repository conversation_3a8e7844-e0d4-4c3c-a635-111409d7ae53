namespace Sleekflow.RateLimits.LuaScripts;

public class SlidingWindowNextAvailableSlotParam : ILuaScriptParam
{
    public SlidingWindowParam SlidingWindowParam { get; }

    public long EarliestDesiredPublishTimeMs { get; }

    public string UniqueMessageId { get; }

    public SlidingWindowNextAvailableSlotParam(
        SlidingWindowParam slidingWindowParam,
        DateTimeOffset earliestDesiredPublishTimeMs,
        string uniqueMessageId)
    {
        SlidingWindowParam = slidingWindowParam ??
                             throw new InvalidOperationException($"Missing required SlidingWindowParam");
        EarliestDesiredPublishTimeMs = earliestDesiredPublishTimeMs.ToUnixTimeMilliseconds();
        UniqueMessageId = uniqueMessageId;
    }
}