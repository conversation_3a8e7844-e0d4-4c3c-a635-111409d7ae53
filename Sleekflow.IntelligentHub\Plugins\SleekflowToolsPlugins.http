POST https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/flow-hub/Public/e/41YC0k1a3AXxVErV-vQKuE0VPKMaB1vzd/LHGOWD3X1IM74N3S
Content-Type: application/json

{
    "meetingIdChili": "3d66a344-e2cd-48af-92ff-006d4debaeb5",
    "meetingTitle": "<PERSON> <>  <PERSON> @ SleekFlow | Meeting",
    "meetingDescription": "If you need to reschedule please use the link below:\nhttps://sleekflow.chilipiper.com/reschedule/3d66a344-e2cd-48af-92ff-006d4debaeb5\n\nLocation not specified",
    "meetingLocation": "https://meet.google.com/nym-aaui-ixo",
    "meetingStartTime": "2025-05-27T03:30:00Z",
    "meetingEndTime": "2025-05-27T04:00:00Z",
    "primaryGuestTimeZone": null,
    "hostEmail": "<EMAIL>",
    "hostFullName": "<PERSON> SleekFlow",
    "hostIdChili": "670dd66222446e203b80a656",
    "hostCrmId": {
        "value": "371274332590"
    },
    "assigneeEmail": "<EMAIL>",
    "assigneeFullName": "Annie SleekFlow",
    "assigneeIdChili": "670dd66222446e203b80a656",
    "assigneeCrmId": {
        "value": "371274332590"
    },
    "primaryGuestEmail": "<EMAIL>",
    "primaryGuestDataFields": {
        "Company Name": "Netoryxs",
        "Email": "<EMAIL>",
        "First Name": "Melvin",
        "Last Name": "Morales",
        "Number of Employees": "20-49",
        "Phone Number": "+852 6109 6623"
    },
    "additionalGuests": null,
    "bookerEmail": null,
    "bookerName": null,
    "bookerIdChili": null,
    "bookerCrmId": null,
    "workspaceName": "Inbound Meetings",
    "workspaceIdChili": "628caa4a3a1a315a88c74ca2",
    "productFeatureType": "ConciergeRouter",
    "productFeatureName": "Inbound_Router",
    "productFeatureIdChili": "d0ef1839-e283-4955-b88d-18ebe1b9eba1",
    "distributionName": "Hong Kong and ROW - Tier 2 - 3 [Meeting]",
    "distributionIdChili": "89253f4a-5fbe-46d2-88d8-c881385dcf12",
    "meetingTypeName": "Demo Call",
    "meetingTypeIdChili": "4d76a14d-5f77-4331-af87-87d02e7f22a6",
    "conciergeRoutingId": "30036bf8-3c5c-40e6-a3aa-eba999107c45",
    "type": "Created"
}