# Lead Nurturing Plugin Architecture

## Overview

The Lead Nurturing system implements a **Key-Based Architecture** that orchestrates complex lead nurturing workflows through a manager-agent pattern. This design achieves ~80% token reduction and ~75% cost savings compared to direct multi-agent collaboration by using structured data storage and key-based references instead of passing full conversation data between agents.

## Architecture Components

### 🏗️ Foundation Infrastructure

#### BaseLeadNurturingPlugin
**File:** `BaseLeadNurturingPlugin.cs`

The foundational base class that provides:
- **Standardized Error Handling**: Consistent logging and exception management
- **Agent Duration Tracking**: Performance monitoring and telemetry
- **Data Retrieval Patterns**: Common methods for accessing stored data
- **Agent Execution Framework**: Standardized agent invocation with telemetry
- **Configuration Management**: Centralized configuration retrieval
- **Template Methods**: Reusable patterns for plugin operations

```csharp
// Example usage pattern all plugins follow
protected async Task<string> ExecutePluginOperationAsync<TResponse>(
    string sessionKey,
    string operationName,
    Func<Task<Dictionary<string, string>>> dataRetrievalFunc,
    Func<Dictionary<string, string>, Task<string>> agentExecutionFunc,
    string storageKey,
    string storageDataType,
    Func<string, TResponse?, Task>? outputInterceptorFunc = null,
    Func<TResponse?, string>? outputTransformerFunc = null)
```

#### LeadNurturingDataPane
**File:** `LeadNurturingDataPane.cs`

Redis-backed data storage system that provides:
- **Session-Isolated Storage**: Each conversation gets isolated data space
- **Structured Data Types**: Standardized data type constants
- **TTL Management**: Automatic data expiration (30 minutes)
- **Atomic Operations**: Thread-safe data operations
- **JSON Serialization**: Consistent data format handling

**Key Operations:**
```csharp
await _dataPane.StoreData(sessionKey, dataType, data);
var data = await _dataPane.GetData(sessionKey, dataType);
```

#### DataPaneKeyManager
**File:** `DataPaneKeyManager.cs`

Centralized key generation and management system:
- **Standardized Key Structure**: `{sessionKey}.{AgentName}.{PropertyType}`
- **Type-Safe Constants**: `AgentOutputKeys` prevent typos
- **Session Management**: Unique session key generation
- **Tool Integration**: Exposed as Kernel Functions for Manager Agent

**Key Structure:**
```
session_123.LeadClassifierAgent.classification
session_123.DecisionAgent.decision
session_123.StrategyAgent.need_knowledge
```

### 🤖 Individual Plugin Components

#### 1. LeadClassifierPlugin
**File:** `LeadClassifierPlugin.cs`
**Purpose:** Initial lead qualification and scoring

**Workflow:**
1. Analyzes conversation context
2. Classifies leads as: `hot`, `warm`, `cold`, or `existing_customer`
3. Provides confidence scoring
4. Stores classification results for decision making

**Output Keys:**
- `reasoning`: Classification rationale
- `score`: Confidence score (0-100)
- `classification`: Final classification category

#### 2. DecisionPlugin
**File:** `DecisionPlugin.cs`
**Purpose:** Routing decisions based on lead classification

**Decisions Supported:**
- `continue_nurturing`: Continue with response crafting
- `assign_hot/cold/human`: Route to appropriate assignment queues
- `assign_drop_off`: Handle disengaged leads
- `assign_insufficient_info`: Gather more information
- `schedule_demo`: Direct to demo scheduling workflow

**Input:** Classification results + conversation context
**Output:** Routing decision with reasoning

#### 3. StrategyPlugin
**File:** `StrategyPlugin.cs`
**Purpose:** Develops nurturing strategies for continuing conversations

**Responsibilities:**
- Analyzes conversation needs
- Determines if knowledge base lookup is required
- Provides strategic guidance for response crafting
- Considers additional instructions and configuration

**Key Decision Point:** `need_knowledge` field determines if knowledge retrieval is needed

#### 4. KeyBasedKnowledgePlugin
**File:** `KeyBasedKnowledgePlugin.cs`
**Purpose:** Retrieves relevant information from knowledge base

**Features:**
- Integrates with `IAgenticKnowledgePlugin`
- Builds contextual queries from conversation and strategy
- Returns formatted knowledge results
- Handles "no knowledge found" scenarios gracefully

#### 5. ResponseCrafterPlugin
**File:** `ResponseCrafterPlugin.cs`
**Purpose:** Creates customer-facing responses

**Capabilities:**
- Crafts natural, conversational responses
- Integrates retrieved knowledge seamlessly
- Supports multiple languages
- Handles additional instructions
- Can redirect to assignment workflow if insufficient information

**Key Decision:** Can return `assign_insufficient_info` to switch workflows

#### 6. ReviewerPlugin
**File:** `ReviewerPlugin.cs`
**Purpose:** Quality assurance for crafted responses

**Review Criteria:**
- Placeholder absence verification
- Knowledge integration quality
- Response language consistency
- Naturalness assessment
- Overall approval decision

**Workflow Control:** `approved` responses complete workflow, `rejected` responses loop back to re-crafting

#### 7. PlanningPlugin
**File:** `PlanningPlugin.cs`
**Purpose:** Plans assignments and demo scheduling

**Two Planning Types:**

**Lead Assignment Planning:**
- `assign_lead`: Execute assignment action
- `no_action`: Return to strategy phase

**Demo Scheduling Planning:**
- `modification_required`: Gather more information
- `existing_demo_scheduled`: Create transition response
- `assign_lead`: Proceed with confirmation

#### 8. ConfirmationPlugin
**File:** `ConfirmationPlugin.cs`
**Purpose:** Validates customer confirmation for planned actions

**Confirmation States:**
- `confirmed`: Proceed with action execution
- `not confirmed`: Request confirmation from customer
- `cancelled`: Create appropriate transition response

#### 9. ActionPlugin
**File:** `ActionPlugin.cs`
**Purpose:** Executes planned and confirmed actions

**Execution Results:**
- `success`: Action completed successfully
- `failure`: Action failed with error details
- Always followed by transition response crafting

#### 10. ResponseGenerationPlugin
**File:** `ResponseGenerationPlugin.cs`
**Purpose:** Generates specialized response types

**Response Types:**
- **Transition Responses**: After action execution
- **Information Gathering Responses**: When more customer data needed

**Integration:** Works with chat cache service for context management

### 🎯 Manager Agent Orchestration

#### ManagerLeadNurturingAgent
**File:** `ManagerLeadNurturingAgent.cs`
**Purpose:** Orchestrates the entire lead nurturing workflow

**Workflow Phases:**

1. **Initialize Session**
   ```
   generate_session_key → get_conversation_context_key
   ```

2. **Analyze Lead**
   ```
   get_classification_key → classify_lead → get_decision_key → make_decision
   ```

3. **Execute Based on Decision**

   **For "continue_nurturing":**
   ```
   get_strategy_key → define_strategy
   ↓ (if need_knowledge exists)
   get_knowledge_key → query_knowledge
   ↓
   get_response_key → craft_response
   ↓ (if not assign_insufficient_info)
   get_review_key → review_response
   ```

   **For assignment decisions:**
   ```
   get_planning_key → plan_lead_assignment
   ↓ (if assign_lead)
   get_confirmation_key → check_confirmation
   ↓ (if confirmed)
   get_action_key → execute_action → craft_transition_response
   ```

   **For "schedule_demo":**
   ```
   get_planning_key → plan_demo_scheduling
   ↓ (based on action_type)
   confirmation → action → transition_response
   ```

## Key Benefits

### 🚀 Performance Optimization
- **Token Reduction**: ~80% fewer tokens by using keys instead of full data
- **Cost Savings**: ~75% cost reduction through efficient data management
- **Parallel Processing**: Multiple agents can work with shared data simultaneously

### 🔒 Data Isolation
- **Session-Based**: Each conversation gets isolated data space
- **Structured Keys**: Predictable, type-safe data access
- **TTL Management**: Automatic cleanup prevents data accumulation

### 🛠️ Maintainability
- **Centralized Patterns**: Common base class reduces code duplication
- **Standardized Error Handling**: Consistent logging and exception management
- **Type Safety**: `AgentOutputKeys` constants prevent typos
- **Modular Design**: Each plugin has single responsibility

### 📊 Observability
- **Agent Duration Tracking**: Performance monitoring for each component
- **Structured Logging**: Detailed insights into workflow execution
- **Error Correlation**: Session-based error tracking

## Usage Example

```csharp
// Manager Agent orchestrates the workflow
var managerAgent = ManagerLeadNurturingAgent.Create(
    kernel, settings, logger, dataPane, keyManager,
    leadClassifierPlugin, decisionPlugin, strategyPlugin,
    knowledgePlugin, responseCrafterPlugin, responseGenerationPlugin,
    reviewerPlugin, planningPlugin, confirmationPlugin, actionPlugin);

// Workflow execution happens through structured phases
{
  "phase": "initial",
  "session_id": "group_chat_id",
  "initial_phase": {
    "workflow_plan": "classify → decide → strategy → knowledge → response → review",
    "tools_to_call": [{"tool_name": "generate_session_key", ...}]
  }
}
```

## Data Flow Architecture

```
Customer Input → ManagerAgent
                     ↓
                 Session Key Generation
                     ↓
                 Data Pane Storage
                     ↓
              Plugin Chain Execution
                     ↓
               Result Aggregation
                     ↓
              Final Response Output
```

Each plugin stores its results in the data pane using structured keys, enabling the Manager Agent to make flow control decisions based on parsed agent outputs while maintaining efficiency through the key-based architecture.