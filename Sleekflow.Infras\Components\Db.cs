﻿using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components;

public class Db
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public Db(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class DbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public DbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public DbOutput InitDb()
    {
        const string cosmosDbId = "db";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs()
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.IDbService
        var containerParams = new ContainerParam[]
        {
            new ContainerParam(
                "request",
                "request",
                new List<string>
                {
                    "/id"
                },
                (int?) 3600 * 24 * 31,
                new List<DocumentDB.Inputs.ExcludedPathArgs>()
                {
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/_etag/?"
                    },
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/output/data/*"
                    }
                },
                MaxThroughput: _myConfig.Name == "production" ? 20000 : 1000),
            new ContainerParam(
                "id_state",
                "id_state",
                new List<string>
                {
                    "/type"
                },
                (int?) -1,
                new List<DocumentDB.Inputs.ExcludedPathArgs>(),
                MaxThroughput: 1000),
            new ContainerParam(
                "webhook",
                "webhook",
                new List<string>
                {
                    "/id"
                },
                (int?) -1,
                new List<DocumentDB.Inputs.ExcludedPathArgs>()
                {
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/_etag/?"
                    },
                    new DocumentDB.Inputs.ExcludedPathArgs()
                    {
                        Path = "/request_body/*"
                    }
                },
                MaxThroughput: _myConfig.Name == "production" ? 10000 : 1000),
            new ContainerParam(
                "lock",
                "lock",
                new List<string>
                {
                    "/id"
                },
                (int?) -1,
                new List<DocumentDB.Inputs.ExcludedPathArgs>(),
                MaxThroughput: 1000)
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        if (containerIdToContainer.ContainsKey("id_state"))
        {
#pragma warning disable S1848
            new DocumentDB.SqlResourceSqlStoredProcedure(
                "sys-crossinstance-state-get-id-stored-procedure",
                new DocumentDB.SqlResourceSqlStoredProcedureArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    AccountName = _databaseAccount.Name,
                    DatabaseName = cosmosDb.Name,
                    ContainerName = containerIdToContainer["id_state"].Name,
                    Resource = new DocumentDB.Inputs.SqlStoredProcedureResourceArgs
                    {
                        Body = string.Join(
                            "\n",
                            File.ReadAllLines("./Resources/crossinstance_state/get-id-stored-procedure.js")),
                        Id = "get-id-stored-procedure",
                    },
                    StoredProcedureName = "get-id-stored-procedure",
                },
                new CustomResourceOptions
                {
                    Parent = containerIdToContainer["id_state"]
                });
#pragma warning restore S1848
        }

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new DbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}