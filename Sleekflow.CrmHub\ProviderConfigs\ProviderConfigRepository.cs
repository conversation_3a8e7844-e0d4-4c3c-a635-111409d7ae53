﻿using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.ProviderConfigs;

public interface IProviderConfigRepository : IDynamicFiltersRepository<ProviderConfig>
{
}

public class ProviderConfigRepository
    : DynamicFiltersBaseRepository<ProviderConfig>, IProviderConfigRepository, IScopedService
{
    public ProviderConfigRepository(
        ILogger<DynamicFiltersBaseRepository<ProviderConfig>> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}