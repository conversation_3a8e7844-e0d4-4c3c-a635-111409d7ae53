﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnWhatsappFlowSubmissionMessageReceivedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnWhatsappFlowSubmissionMessageReceived; }
    }

    [Required]
    [JsonProperty("message")]
    public OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage Message { get; set; }

    [JsonProperty("channel")]
    public string Channel { get; set; }

    [Required]
    [JsonProperty("channel_id")]
    public string ChannelId { get; set; }

    [Required]
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [Required]
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("message_unique_id")]
    public string MessageUniqueId { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [Required]
    [JsonProperty("is_new_contact")]
    public bool IsNewContact { get; set; }

    [JsonProperty("is_sent_from_sleekflow")]
    public bool IsSentFromSleekflow => false;

    [JsonConstructor]
    public OnWhatsappFlowSubmissionMessageReceivedEventBody(
        DateTimeOffset createdAt,
        OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage message,
        string channel,
        string channelId,
        string conversationId,
        string messageId,
        string messageUniqueId,
        string contactId,
        Dictionary<string, object?> contact,
        bool isNewContact)
        : base(createdAt)
    {
        Message = message;
        Channel = channel;
        ChannelId = channelId;
        ConversationId = conversationId;
        MessageId = messageId;
        MessageUniqueId = messageUniqueId;
        ContactId = contactId;
        Contact = contact;
        IsNewContact = isNewContact;
    }
}

public class OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage : BaseMessage
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("whatsapp_flow_id")]
    public string WhatsappFlowId { get; set; }

    [JsonProperty("whatsapp_template_id")]
    public string? WhatsappTemplateId { get; set; }

    [JsonProperty("whatsapp_flow_submission_data")]
    public Dictionary<string, object?> WhatsappFlowSubmissionData { get; set; }

    [JsonConstructor]
    public OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage(
        string id,
        string whatsappFlowId,
        string? whatsappTemplateId,
        Dictionary<string, object?> whatsappFlowSubmissionData,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        MessageBody messageBody,
        string messageType,
        string messageContent,
        string messageStatus,
        string messageDeliveryType)
        : base(
            createdAt,
            updatedAt,
            messageBody,
            messageType,
            messageContent,
            messageStatus,
            messageDeliveryType)
    {
        Id = id;
        WhatsappFlowId = whatsappFlowId;
        WhatsappTemplateId = whatsappTemplateId;
        WhatsappFlowSubmissionData = whatsappFlowSubmissionData;
    }
}