using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Subscriptions;
using Sleekflow.JsonConfigs;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitTypeSync : ITrigger
{
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceSubscriptionService _salesforceSubscriptionService;

    public InitTypeSync(
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceSubscriptionService salesforceSubscriptionService)
    {
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceSubscriptionService = salesforceSubscriptionService;
    }

    public class InitTypeSyncInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("sync_config")]
        public SyncConfig? SyncConfig { get; set; }

        [JsonConstructor]
        public InitTypeSyncInput(string sleekflowCompanyId, string entityTypeName, SyncConfig? syncConfig)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            SyncConfig = syncConfig;
        }
    }

    public class InitTypeSyncOutput
    {
    }

    public async Task<InitTypeSyncOutput> F(
        InitTypeSyncInput initTypeSyncInput)
    {
        var authentication = await _salesforceAuthenticationService.GetAsync(initTypeSyncInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        if (initTypeSyncInput.SyncConfig == null)
        {
            await _salesforceSubscriptionService.ClearAsync(
                initTypeSyncInput.EntityTypeName,
                initTypeSyncInput.SleekflowCompanyId);

            return new InitTypeSyncOutput();
        }

        var instanceUrl = authentication.InstanceUrl;
        if (instanceUrl == null)
        {
            throw new Exception(
                $"The instance_url is null. authentication {JsonConvert.SerializeObject(authentication, JsonConfig.DefaultJsonSerializerSettings)}");
        }

        // This is a special handling of the default value
        // By default, the frontend app sends 7200 to us
        if (initTypeSyncInput.SyncConfig.Interval == 7200)
        {
            await _salesforceSubscriptionService.UpsertAsync(
                initTypeSyncInput.EntityTypeName,
                initTypeSyncInput.SleekflowCompanyId,
                60 * 10);
        }
        else
        {
            await _salesforceSubscriptionService.UpsertAsync(
                initTypeSyncInput.EntityTypeName,
                initTypeSyncInput.SleekflowCompanyId,
                initTypeSyncInput.SyncConfig.Interval);
        }

        return new InitTypeSyncOutput();
    }
}