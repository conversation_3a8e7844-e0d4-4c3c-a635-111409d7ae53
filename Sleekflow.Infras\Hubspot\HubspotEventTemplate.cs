﻿using Newtonsoft.Json;

namespace Sleekflow.Infras.Hubspot;

public class HubspotEventTemplate
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("objectType")]
    public string ObjectType { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("headerTemplate")]
    public string HeaderTemplate { get; set; }

    [JsonProperty("tokens")]
    public List<HubspotEventTemplateToken> Tokens { get; set; }

    [JsonProperty("createdAt")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("updatedAt")]
    public DateTime? UpdatedAt { get; set; }

    [JsonProperty("detailTemplate")]
    public string DetailTemplate { get; set; }

    [JsonConstructor]
    public HubspotEventTemplate(
        string id,
        string objectType,
        string name,
        string headerTemplate,
        List<HubspotEventTemplateToken> tokens,
        DateTime? createdAt,
        DateTime? updatedAt,
        string detailTemplate)
    {
        Id = id;
        ObjectType = objectType;
        Name = name;
        HeaderTemplate = headerTemplate;
        Tokens = tokens;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        DetailTemplate = detailTemplate;
    }

    public HubspotEventTemplate(
        string objectType,
        string name,
        string headerTemplate,
        List<HubspotEventTemplateToken> tokens,
        string detailTemplate)
    {
        ObjectType = objectType;
        Name = name;
        HeaderTemplate = headerTemplate;
        Tokens = tokens;
        DetailTemplate = detailTemplate;
    }
}