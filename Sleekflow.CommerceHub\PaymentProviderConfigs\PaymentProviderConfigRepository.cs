using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.PaymentProviderConfigs;

public interface IPaymentProviderConfigRepository : IDynamicFiltersRepository<PaymentProviderConfig>
{
    Task<List<PaymentProviderConfig>> GetPaymentProviderConfigsAsync(
        string sleekflowCompanyId);

    Task<List<PaymentProviderConfig>> GetPaymentProviderConfigsAsync(
        string sleekflowCompanyId,
        string storeId);

    Task<PaymentProviderConfig> GetPaymentProviderConfigByStripeAccountIdAsync(
        string stripeAccountId);
}

public class PaymentProviderConfigRepository
    : DynamicFiltersBaseRepository<PaymentProviderConfig>,
        IPaymentProviderConfigRepository,
        IScopedService
{
    public PaymentProviderConfigRepository(
        ILogger<PaymentProviderConfigRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }

    public async Task<List<PaymentProviderConfig>> GetPaymentProviderConfigsAsync(string sleekflowCompanyId)
    {
        var paymentProviderConfigs = await GetObjectsAsync(
            p =>
                p.SleekflowCompanyId == sleekflowCompanyId
                && p.SysTypeName == SysTypeNames.PaymentProviderConfig);

        return paymentProviderConfigs;
    }

    public async Task<List<PaymentProviderConfig>> GetPaymentProviderConfigsAsync(
        string sleekflowCompanyId,
        string storeId)
    {
        var paymentProviderConfigs = await GetObjectsAsync(
            p =>
                p.SleekflowCompanyId == sleekflowCompanyId
                && p.StoreIds.Contains(storeId)
                && p.SysTypeName == SysTypeNames.PaymentProviderConfig);

        return paymentProviderConfigs;
    }

    public async Task<PaymentProviderConfig> GetPaymentProviderConfigByStripeAccountIdAsync(
        string stripeAccountId)
    {
        var paymentProviderConfigs = await GetObjectsAsync(
            p =>
                p.PaymentProviderExternalConfig.ProviderName == "Stripe" &&
                p.PaymentProviderExternalConfig.ProviderId == stripeAccountId &&
                p.SysTypeName == SysTypeNames.PaymentProviderConfig);

        return paymentProviderConfigs.First();
    }
}