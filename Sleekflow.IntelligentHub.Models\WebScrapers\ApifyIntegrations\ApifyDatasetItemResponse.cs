﻿using System.Net;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifyDatasetItemResponse
{
    [JsonProperty(PropertyName = "url")]
    public string Url { get; set; }

    [JsonProperty(PropertyName = "crawl")]
    public ApifyDatasetItemResponseCrawl Crawl { get; set; }

    [JsonProperty(PropertyName = "metadata")]
    public ApifyDatasetItemResponseMetadata Metadata { get; set; }

    [JsonProperty(PropertyName = "screenshotUrl")]
    public string ScreenshotUrl { get; set; }

    [JsonProperty(PropertyName = "text")]
    public string Text { get; set; }

    [JsonProperty(PropertyName = "markdown")]
    public string Markdown { get; set; }

    [JsonConstructor]
    public ApifyDatasetItemResponse(
        string url,
        ApifyDatasetItemResponseCrawl crawl,
        ApifyDatasetItemResponseMetadata metadata,
        string screenshotUrl,
        string text,
        string markdown)
    {
        Url = url;
        Crawl = crawl;
        Metadata = metadata;
        ScreenshotUrl = screenshotUrl;
        Text = text;
        Markdown = markdown;
    }
}

public class ApifyDatasetItemResponseCrawl
{
    [JsonProperty(PropertyName = "loadedUrl")]
    public string LoadedUrl { get; set; }

    [JsonProperty(PropertyName = "loadedTime")]
    public DateTime LoadedTime { get; set; }

    [JsonProperty(PropertyName = "referrerUrl")]
    public string ReferrerUrl { get; set; }

    [JsonProperty(PropertyName = "depth")]
    public int Depth { get; set; }

    [JsonProperty(PropertyName = "httpStatusCode")]
    public HttpStatusCode HttpStatusCode { get; set; }

    [JsonConstructor]
    public ApifyDatasetItemResponseCrawl(
        string loadedUrl,
        DateTime loadedTime,
        string referrerUrl,
        int depth,
        HttpStatusCode httpStatusCode)
    {
        LoadedUrl = loadedUrl;
        LoadedTime = loadedTime;
        ReferrerUrl = referrerUrl;
        Depth = depth;
        HttpStatusCode = httpStatusCode;
    }
}

public class ApifyDatasetItemResponseMetadata
{
    [JsonProperty(PropertyName = "canonicalUrl")]
    public string CanonicalUrl { get; set; }

    [JsonProperty(PropertyName = "title")]
    public string Title { get; set; }

    [JsonProperty(PropertyName = "description")]
    public string Description { get; set; }

    [JsonProperty(PropertyName = "author")]
    public string Author { get; set; }

    [JsonProperty(PropertyName = "keywords")]
    public string Keywords { get; set; }

    [JsonProperty(PropertyName = "languageCode")]
    public string LanguageCode { get; set; }

    [JsonConstructor]
    public ApifyDatasetItemResponseMetadata(
        string canonicalUrl,
        string title,
        string description,
        string author,
        string keywords,
        string languageCode)
    {
        CanonicalUrl = canonicalUrl;
        Title = title;
        Description = description;
        Author = author;
        Keywords = keywords;
        LanguageCode = languageCode;
    }
}