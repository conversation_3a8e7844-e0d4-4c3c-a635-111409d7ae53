﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Workers;

public class LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
    public string SchemaId { get; set; }

    [JsonProperty("continuation_token")]
    public string? ContinuationToken { get; set; }

    [JsonProperty("flow_hub_workflow_id")]
    [Required]
    public string FlowHubWorkflowId { get; set; }

    [JsonProperty("flow_hub_workflow_versioned_id")]
    [Required]
    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonConstructor]
    public LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput(
        string sleekflowCompanyId,
        string schemaId,
        string? continuationToken,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemaId = schemaId;
        ContinuationToken = continuationToken;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
    }
}