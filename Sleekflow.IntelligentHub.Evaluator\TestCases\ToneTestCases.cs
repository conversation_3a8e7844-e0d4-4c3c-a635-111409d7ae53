using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.Models.Constants;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class ToneTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetToneTestCases()
    {
        var config = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/HKBN/",
            SleekflowCompanyId: "b6d7e442-38ae-4b9a-b100-2951729768bc");

        var mobileSourceFilenames = new List<string>
        {
            "RS_sales briefing_N mobile_42mbps $78 $98 China data_20241204.docx.md",
            "RS_sales briefing_N mobile_APAC_20241204.docx.md",
            "RS_sales briefing_tri-area_20241204.docx.md",
            "RS_sales briefing_WW plan_20241204.docx.md"
        };

        var tones = new List<string>
        {
            TargetToneTypes.Professional, TargetToneTypes.Technical, TargetToneTypes.Casual
        };

        foreach (var tone in tones)
        {
            yield return new ChatEvalQuestion(
                config,
                $"Greetings in {tone}",
                [
                    new ChatMessageContent(AuthorRole.User, "你好。"),
                ],
                "你好，有咩可以幫到你？",
                AgentConfig: AgentConfigUtils.GetAgentConfig(tone));

            yield return new ChatEvalQuestion(
                config,
                $"客戶對5G計劃價格表示不滿並質疑服務質素 in {tone}",
                [
                    new ChatMessageContent(AuthorRole.User, "你們這個5G 20GB的計劃也太貴了吧？一個月要$168，根本就是在搶錢。"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "理解您對價格的考慮。這個$168的計劃除了20GB本地數據外，還包含每月10GB環球旅遊數據，覆蓋超過50個熱門地區，而且合約期內豁免行政費。"),
                    new ChatMessageContent(AuthorRole.User, "其他電訊商都在打折，你們卻這麼貴。而且我聽說你們網絡覆蓋很差，根本不值這個價錢！"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "感謝您的意見。我們的網絡由3HK提供支援，覆蓋穩定。另外，如果您現在登記，可以享受以下優惠：\n1. 高達3個月pandapro會籍（價值$297）\n2. KKday旅遊產品折扣優惠\n3. pandapro一年會籍75折\n要不要讓我為您詳細介紹這些優惠？"),
                    new ChatMessageContent(AuthorRole.User, "那如果我超過20GB用量會怎樣？到時候是不是還要加錢？")
                ],
                "超出20GB後，數據速度會被調整至不高於1Mbps，但不會額外收費。您也可以考慮升級到30GB計劃，每月只需多付$30，提供更大用量，讓您使用更安心。",
                SourceFilenames: mobileSourceFilenames,
                AgentConfig: AgentConfigUtils.GetAgentConfig(tone));
        }
    }
}