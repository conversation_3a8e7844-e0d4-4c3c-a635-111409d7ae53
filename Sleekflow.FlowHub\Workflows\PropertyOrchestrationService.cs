using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Workers;

namespace Sleekflow.FlowHub.Workers.Services;

public class PropertyOrchestrationService : IPropertyOrchestrationService, IScopedService
{
    private readonly IWorkerService _workerService;
    private readonly IWorkerConfig _workerConfig;
    private readonly HttpClient _httpClient;
    private readonly ILogger<PropertyOrchestrationService> _logger;

    public PropertyOrchestrationService(
        IWorkflowOrchestrationService workflowOrchestration,
        ILogger<PropertyOrchestrationService> logger,
        IWorkerService workerService,
        IWorkerConfig workerConfig,
        HttpClient httpClient,
        ILogger<PropertyOrchestrationService> logger1)
    {
        _workerService = workerService;
        _workerConfig = workerConfig;
        _httpClient = httpClient;
        _logger = logger1;
    }

    public async Task<DurablePayload> TriggerContactDateTimeWorkflowAsync<T>(
        string sleekflowCompanyId,
        string origin,
        string workflowId,
        string workflowVersionedId,
        ContactPropertyDateTimeSettings contactPropertyDateTimeSettings,
        CustomObjectDateTimeSettings customObjectDateTimeSettings,
        string workflowVersion,
        string scheduledType,
        WorkflowRecurringSettings recurringSettings)
    {
        _logger.LogInformation(
            "TriggerScheduleWorkflowAsync called with parameters: {SleekflowCompanyId}, {Origin}, {WorkflowId}, " +
            "{WorkflowVersionedId}, {ContactPropertyDateTimeSettings}, {CustomObjectDateTimeSettings}, {WorkflowVersion}",
            sleekflowCompanyId,
            origin,
            workflowId,
            workflowVersionedId,
            contactPropertyDateTimeSettings,
            customObjectDateTimeSettings,
            workflowVersion);

        var inputJsonStr = JsonConvert.SerializeObject(
            new TriggerPropertyWorkflowInput(
                sleekflowCompanyId,
                origin,
                workflowId,
                workflowVersionedId,
                contactPropertyDateTimeSettings,
                customObjectDateTimeSettings,
                workflowVersion,
                scheduledType,
                recurringSettings),
            JsonConfig.DefaultJsonSerializerSettings);

        var (_, _, output) = await _workerService.PostAsync<DurablePayload>(
            _httpClient,
            inputJsonStr,
            _workerConfig.WorkerHostname + "/api/TriggerPropertyDateTimeWorkflow");

        _logger.LogInformation(
            "TriggerScheduleWorkflowAsync {WorkflowVersionedId} response: {Output}",
            workflowVersionedId,
            JsonConvert.SerializeObject(output));

        return output.Data!;
    }
}