using MassTransit;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.StepExecutors;
using Sleekflow.FlowHub.Steps;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnStepRequestedEventConsumerDefinition
    : ConsumerDefinition<OnStepRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnStepRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 128;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

/// <summary>
/// Consumes the <see cref="OnStepRequestedEvent"/> published when a step needs to be executed.
/// Activates the step, notifies the status at various points, and handles failures.
/// </summary>
public class OnStepRequestedEventConsumer : IConsumer<OnStepRequestedEvent>, IHighTrafficConsumer<OnStepRequestedEvent>
{
    private readonly IStepExecutorActivator _stepExecutorActivator;
    private readonly IExecutorContext _executorContext;
    private readonly ILogger<OnStepRequestedEventConsumer> _logger;

    public OnStepRequestedEventConsumer(
        IStepExecutorActivator stepExecutorActivator,
        IExecutorContext executorContext,
        ILogger<OnStepRequestedEventConsumer> logger)
    {
        _stepExecutorActivator = stepExecutorActivator;
        _executorContext = executorContext;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<OnStepRequestedEvent> context)
    {
        var @event = context.Message;

        var stateId = @event.StateId;
        var stepId = @event.StepId;
        var stackEntries = @event.StackEntries;
        var workerInstanceId = @event.WorkerInstanceId;

        _executorContext.StateId = stateId;
        _executorContext.StepId = stepId;
        _executorContext.StackEntries = stackEntries;
        _executorContext.WorkerInstanceId = workerInstanceId;
        _logger.LogInformation("Received OnStepRequestedEvent message, StepId: {StepId}", @event.StepId);
        await _stepExecutorActivator.RequestStepAsync(
            stateId,
            stepId,
            stackEntries,
            workerInstanceId);
    }
}