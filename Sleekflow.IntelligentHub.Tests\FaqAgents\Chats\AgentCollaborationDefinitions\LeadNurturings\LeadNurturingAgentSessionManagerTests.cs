using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.Tests.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

[TestFixture]
public class LeadNurturingAgentSessionManagerTests
{
    private IConnectionMultiplexer _connectionMultiplexer;
    private LeadNurturingAgentSessionManager _sessionManager;
    private TestCacheConfig _cacheConfig;
    private string _testPrefix;

    [SetUp]
    public async Task Setup()
    {
        // Use a unique prefix for each test run to avoid test interference
        _testPrefix = $"TEST-LEAD-NURTURING-{Guid.NewGuid():N}";
        _cacheConfig = new TestCacheConfig(_testPrefix);
        _connectionMultiplexer = await ConnectionMultiplexer.ConnectAsync(_cacheConfig.RedisConnStr);
        _sessionManager = new LeadNurturingAgentSessionManager(
            NullLogger<LeadNurturingAgentSessionManager>.Instance,
            _connectionMultiplexer,
            _cacheConfig);
    }

    [TearDown]
    public async Task TearDown()
    {
        // Clean up Redis keys after each test
        var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
        var database = _connectionMultiplexer.GetDatabase();

        // Get and delete all keys with our test prefix
        var keys = server.Keys(pattern: $"{_testPrefix}*").ToArray();
        if (keys.Any())
        {
            await database.KeyDeleteAsync(keys);
        }

        await _connectionMultiplexer.CloseAsync();
    }

    [Test]
    public async Task RegisterAgentSession_ShouldSucceed()
    {
        // Arrange
        var groupChatId = CreateGroupChatId();

        // Act
        var result = await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Assert
        Assert.That(result, Is.True);

        // Verify the session was created in Redis
        var database = _connectionMultiplexer.GetDatabase();
        var sessionKey = $"{_cacheConfig.CachePrefix}:agent-session:{groupChatId.SessionId}";
        var sessionData = await database.StringGetAsync(sessionKey);
        Assert.That(sessionData.HasValue, Is.True);

        var sessionState = JsonConvert.DeserializeObject<AgentSessionState>(sessionData);
        Assert.That(sessionState, Is.Not.Null);
        Assert.That(sessionState.CompanyId, Is.EqualTo(groupChatId.CompanyId));
        Assert.That(sessionState.ObjectId, Is.EqualTo(groupChatId.ObjectId));
        Assert.That(sessionState.SessionId, Is.EqualTo(groupChatId.SessionId));
        Assert.That(sessionState.HasPerformedActions, Is.False);
        Assert.That(sessionState.ShouldCancel, Is.False);
    }

    [Test]
    public async Task MarkSessionActionsPerformed_ShouldUpdateSession()
    {
        // Arrange
        var groupChatId = CreateGroupChatId();
        await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Act
        await _sessionManager.MarkSessionActionsPerformedAsync(groupChatId);

        // Assert
        var database = _connectionMultiplexer.GetDatabase();
        var sessionKey = $"{_cacheConfig.CachePrefix}:agent-session:{groupChatId.SessionId}";
        var sessionData = await database.StringGetAsync(sessionKey);
        var sessionState = JsonConvert.DeserializeObject<AgentSessionState>(sessionData);

        Assert.That(sessionState, Is.Not.Null);
        Assert.That(sessionState.HasPerformedActions, Is.True);
    }

    [Test]
    public async Task ShouldCancelSession_ReturnsFalseForNewSession()
    {
        // Arrange
        var groupChatId = CreateGroupChatId();
        await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Act
        var shouldCancel = await _sessionManager.ShouldCancelSessionAsync(groupChatId);

        // Assert
        Assert.That(shouldCancel, Is.False);
    }

    [Test]
    public async Task ShouldCancelSession_ReturnsTrueForCanceledSession()
    {
        // Arrange
        var groupChatId = CreateGroupChatId();
        await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Manually set the session to be canceled
        var database = _connectionMultiplexer.GetDatabase();
        var sessionKey = $"{_cacheConfig.CachePrefix}:agent-session:{groupChatId.SessionId}";
        var sessionData = await database.StringGetAsync(sessionKey);
        var sessionState = JsonConvert.DeserializeObject<AgentSessionState>(sessionData);
        sessionState.ShouldCancel = true;
        await database.StringSetAsync(
            sessionKey,
            JsonConvert.SerializeObject(sessionState),
            expiry: TimeSpan.FromMinutes(30));

        // Act
        var shouldCancel = await _sessionManager.ShouldCancelSessionAsync(groupChatId);

        // Assert
        Assert.That(shouldCancel, Is.True);
    }

    [Test]
    public async Task CancelPreviousSessions_ShouldCancelOlderSessionsWithoutActions()
    {
        // Arrange
        var objectId = Guid.NewGuid().ToString();
        var companyId = "company123";

        // Create three sessions for the same object
        var firstGroupChatId = CreateGroupChatId(companyId, objectId, "session1");
        var secondGroupChatId = CreateGroupChatId(companyId, objectId, "session2");
        var thirdGroupChatId = CreateGroupChatId(companyId, objectId, "session3");

        await _sessionManager.RegisterAgentSessionAsync(firstGroupChatId);
        await _sessionManager.RegisterAgentSessionAsync(secondGroupChatId);

        // Mark the second session as having performed actions
        await _sessionManager.MarkSessionActionsPerformedAsync(secondGroupChatId);

        // Register the third session last
        await _sessionManager.RegisterAgentSessionAsync(thirdGroupChatId);

        // Act - Cancel previous sessions from the perspective of the third session
        await _sessionManager.CancelPreviousSessionsAsync(thirdGroupChatId);

        // Assert - First session should be canceled, second session should not be canceled
        var shouldCancelFirst = await _sessionManager.ShouldCancelSessionAsync(firstGroupChatId);
        var shouldCancelSecond = await _sessionManager.ShouldCancelSessionAsync(secondGroupChatId);
        var shouldCancelThird = await _sessionManager.ShouldCancelSessionAsync(thirdGroupChatId);

        Assert.That(shouldCancelFirst, Is.True, "First session should be canceled");
        Assert.That(shouldCancelSecond, Is.False, "Second session should not be canceled because it performed actions");
        Assert.That(
            shouldCancelThird,
            Is.False,
            "Third session should not be canceled because it's the current session");
    }

    [Test]
    public async Task RegisterAgentSession_MultipleSessions_StoresAllSessionsCorrectly()
    {
        // Arrange
        var objectId = Guid.NewGuid().ToString();
        var companyId = "company123";

        var firstGroupChatId = CreateGroupChatId(companyId, objectId, "session1");
        var secondGroupChatId = CreateGroupChatId(companyId, objectId, "session2");

        // Act
        await _sessionManager.RegisterAgentSessionAsync(firstGroupChatId);
        await _sessionManager.RegisterAgentSessionAsync(secondGroupChatId);

        // Assert
        var database = _connectionMultiplexer.GetDatabase();
        var sessionIdsKey = $"{_cacheConfig.CachePrefix}:all-agent-session-ids:{objectId}";
        var sessionIds = await database.SetMembersAsync(sessionIdsKey);

        Assert.That(sessionIds.Length, Is.EqualTo(2));
        Assert.That(sessionIds.Select(id => id.ToString()).Contains(firstGroupChatId.SessionId));
        Assert.That(sessionIds.Select(id => id.ToString()).Contains(secondGroupChatId.SessionId));
    }

    [Test]
    public async Task CancelPreviousSessions_NoSessionsToCancel_ReturnsGracefully()
    {
        // Arrange
        var groupChatId = CreateGroupChatId();
        await _sessionManager.RegisterAgentSessionAsync(groupChatId);

        // Act & Assert - Should not throw
        await _sessionManager.CancelPreviousSessionsAsync(groupChatId);
    }

    [Test]
    public async Task MarkSessionActionsPerformed_SessionDoesNotExist_HandleGracefully()
    {
        // Arrange
        var nonExistentGroupChatId = CreateGroupChatId();

        // Act & Assert - Should not throw
        await _sessionManager.MarkSessionActionsPerformedAsync(nonExistentGroupChatId);
    }

    [Test]
    public async Task ShouldCancelSession_SessionDoesNotExist_ReturnsFalse()
    {
        // Arrange
        var nonExistentGroupChatId = CreateGroupChatId();

        // Act
        var shouldCancel = await _sessionManager.ShouldCancelSessionAsync(nonExistentGroupChatId);

        // Assert
        Assert.That(shouldCancel, Is.False);
    }

    // Helper method to create a GroupChatId for testing
    private static GroupChatId CreateGroupChatId(
        string companyId = "testCompany",
        string objectId = null,
        string sessionId = null)
    {
        return new GroupChatId(
            companyId,
            objectId ?? Guid.NewGuid().ToString(),
            sessionId ?? Guid.NewGuid().ToString());
    }

    // Test implementation of ICacheConfig
    private class TestCacheConfig : ICacheConfig
    {
        private readonly string _prefix;

        public TestCacheConfig(string prefix)
        {
            _prefix = prefix;
        }

        public string CachePrefix => _prefix;

        public string RedisConnStr =>
            "sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False";
    }
}