using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Providers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.EmailHub.Repositories;

public interface IProviderConfigRepository : IRepository<ProviderConfig>
{
}

public class ProviderConfigRepository
    : BaseRepository<ProviderConfig>,
        IProviderConfigRepository,
        ISingletonService
{
    public ProviderConfigRepository(
        ILogger<ProviderConfigRepository> logger,
        IServiceProvider serviceProvider)
    : base(logger, serviceProvider)
    {
    }
}