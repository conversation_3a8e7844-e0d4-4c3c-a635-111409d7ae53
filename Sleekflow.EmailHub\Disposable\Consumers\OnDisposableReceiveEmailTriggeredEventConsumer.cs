using MassTransit;
using Sleekflow.EmailHub.Disposable.Integrator;
using Sleekflow.EmailHub.Models.Disposable.Events;
using Sleekflow.Locks;

namespace Sleekflow.EmailHub.Disposable.Consumers;

public class OnDisposableReceiveEmailTriggeredEventConsumerDefinition : ConsumerDefinition<OnDisposableReceiveEmailTriggeredEventConsumer>
{
    public const int LockDuration = 20;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnDisposableReceiveEmailTriggeredEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(LockDuration);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnDisposableReceiveEmailTriggeredEventConsumer : IConsumer<OnDisposableReceiveEmailTriggeredEvent>
{
    private readonly IDisposableIntegratorService _disposableIntegratorService;
    private readonly ILogger<OnDisposableReceiveEmailTriggeredEventConsumer> _logger;
    private readonly ILockService _lockService;

    public OnDisposableReceiveEmailTriggeredEventConsumer(
        IDisposableIntegratorService disposableIntegratorService,
        ILogger<OnDisposableReceiveEmailTriggeredEventConsumer> logger,
        ILockService lockService)
    {
        _disposableIntegratorService = disposableIntegratorService;
        _logger = logger;
        _lockService = lockService;
    }

    public async Task Consume(ConsumeContext<OnDisposableReceiveEmailTriggeredEvent> context)
    {
        var disposableEmailEvent = context.Message;
        var cancellationToken = context.CancellationToken;

        var @lock = await _lockService.LockAsync(
            new[]
            {
                disposableEmailEvent.To,
                "Disposable"
            },
            TimeSpan.FromSeconds(
                60 * OnDisposableReceiveEmailTriggeredEventConsumerDefinition.LockDuration),
            cancellationToken);

        if (@lock is null)
        {
            _logger.LogInformation(
                "[DisposableEmailEventConsumer]: Lock is not acquired: emailAddress {emailAddress} of sleekflowCompanyId {companyId}",
                disposableEmailEvent.To,
                disposableEmailEvent.SleekflowCompanyId);

            return;
        }

        try
        {
            _logger.LogInformation(
                "[DisposableEmailEventConsumer]: Starting Receive disposable provider email: emailAddress {emailAddress} of sleekflowCompanyId {companyId}",
                disposableEmailEvent.To,
                disposableEmailEvent.SleekflowCompanyId);


            await _disposableIntegratorService.ReceiveEmailAndStoreAsync(
                disposableEmailEvent.To,
                disposableEmailEvent.EmailMetadata,
                cancellationToken);

            _logger.LogInformation(
                "[DisposableEmailEventConsumer]: Receive disposable provider email successes: emailAddress {emailAddress} of sleekflowCompanyId {companyId}",
                disposableEmailEvent.To,
                disposableEmailEvent.SleekflowCompanyId);
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
        catch (Exception e)
        {
            _logger.LogInformation(
                "[DisposableEmailEventConsumer]: Receive disposable provider email fails: emailAddress {emailAddress} of sleekflowCompanyId {companyId}: Exception: {e}",
                disposableEmailEvent.To,
                disposableEmailEvent.SleekflowCompanyId,
                e);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}