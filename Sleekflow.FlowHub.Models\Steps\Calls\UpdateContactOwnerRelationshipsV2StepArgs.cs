﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactOwnerRelationshipsV2StepArgs : TypedCallStepArgs, IValidatableObject
{
    public const string CallName = "sleekflow.v2.update-contact-owner-relationships";

    [Required]
    [JsonProperty("strategy")]
    public string Strategy { get; set; }

    [JsonProperty("team_id")]
    public string? TeamId { get; set; }

    [JsonProperty("staff_id")]
    public string? StaffId { get; set; }

    [JsonProperty("staff_ids")]
    public List<string>? StaffIds { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Conversation;

    [JsonConstructor]
    public UpdateContactOwnerRelationshipsV2StepArgs(
        string strategy,
        string? teamId,
        string? staffId,
        List<string>? staffIds)
    {
        Strategy = strategy;
        TeamId = teamId;
        StaffId = staffId;
        StaffIds = staffIds;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        switch (Strategy)
        {
            case UpdateContactOwnerRelationshipsStepArgsStrategy.RoundRobbin_StaffOnly
                when StaffIds == null || StaffIds.Count == 0:
            {
                yield return new ValidationResult(
                    $"StaffIds must be provided when strategy is {Strategy}",
                    new[]
                    {
                        nameof(StaffIds)
                    });

                break;
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_Unassigned_Staff
                or UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_All_Staffs_In_Team
                when string.IsNullOrWhiteSpace(TeamId):
            {
                yield return new ValidationResult(
                    $"TeamId must be provided when strategy is {Strategy}",
                    new[]
                    {
                        nameof(TeamId)
                    });

                break;
            }

            case UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_Specific_Staffs_In_Team
                when string.IsNullOrWhiteSpace(TeamId) || StaffIds == null || StaffIds.Count == 0:
            {
                yield return new ValidationResult(
                    $"Both TeamId and StaffIds must be provided when strategy is {Strategy}",
                    new[]
                    {
                        nameof(TeamId),
                        nameof(StaffIds)
                    });

                break;
            }
        }
    }
}