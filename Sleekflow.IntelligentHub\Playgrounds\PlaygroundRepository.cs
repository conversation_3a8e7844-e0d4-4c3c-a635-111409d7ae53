﻿using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Playgrounds;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Playgrounds;

public interface IPlaygroundRepository : IRepository<Playground>
{
    Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        List<PlaygroundRecommendedReply> recommendedReplies,
        string? eTag = null);
}

public class PlaygroundRepository : BaseRepository<Playground>, IPlaygroundRepository, IScopedService
{
    public PlaygroundRepository(ILogger<PlaygroundRepository> logger, IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task PatchAsync(
        string id,
        string sleekflowCompanyId,
        List<PlaygroundRecommendedReply> recommendedReplies,
        string? eTag = null)
    {
        await PatchAsync(
            id,
            sleekflowCompanyId,
            [
                PatchOperation.Set($"/{Playground.PropertyNamePlaygroundRecommendedReplies}", recommendedReplies)
            ],
            eTag: eTag);
    }
}