using MassTransit;
using Sleekflow.CommerceHub.Models.Blobs;

namespace Sleekflow.CommerceHub.Blobs;

public class OnBlobCreatedEventConsumerDefinition : ConsumerDefinition<OnBlobCreatedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnBlobCreatedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnBlobCreatedEventConsumer : IConsumer<OnBlobCreatedEvent>
{
    private readonly IBlobService _blobService;

    public OnBlobCreatedEventConsumer(IBlobService blobService)
    {
        _blobService = blobService;
    }

    public async Task Consume(ConsumeContext<OnBlobCreatedEvent> context)
    {
        var onBlobCreatedEvent = context.Message;

        var blobType = onBlobCreatedEvent.BlobType;
        var containerName = onBlobCreatedEvent.ContainerName;
        var blobId = onBlobCreatedEvent.BlobId;

        if (await _blobService.IsBlobExistsAsync(blobId, blobType))
        {
            var blobSize = await _blobService.GetBlobSizeAsync(blobId, blobType);
            if (blobSize == 0)
            {
                await _blobService.DeleteBlobsAsync(
                    new List<Blob>
                    {
                        new (containerName, blobId, blobId)
                    },
                    blobType);
            }
        }
    }
}