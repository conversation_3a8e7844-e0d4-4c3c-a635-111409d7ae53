using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Integrator.Dynamics365.Configs;

public interface IDynamics365Config
{
    string Dynamics365ClientId { get; }

    string Dynamics365ClientSecret { get; }

    string Dynamics365OauthCallbackUrl { get; }

    string Dynamics365OauthStateEncryptionKey { get; }
}

public class Dynamics365Config : IConfig, IDynamics365Config
{
    public string Dynamics365ClientId { get; private set; }

    public string Dynamics365ClientSecret { get; private set; }

    public string Dynamics365OauthCallbackUrl { get; private set; }

    public string Dynamics365OauthStateEncryptionKey { get; private set; }

    public Dynamics365Config(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Dynamics365ClientId =
            Environment.GetEnvironmentVariable("DYNAMICS365_CLIENT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("DYNAMICS365_CLIENT_ID");
        Dynamics365ClientSecret =
            Environment.GetEnvironmentVariable("DYNAMICS365_CLIENT_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("DYNAMICS365_CLIENT_SECRET");
        Dynamics365OauthCallbackUrl =
            Environment.GetEnvironmentVariable("DYNAMICS365_OAUTH_CALLBACK_URL", target)
            ?? configuration["DYNAMICS365_OAUTH_CALLBACK_URL"]!;
        Dynamics365OauthStateEncryptionKey =
            Environment.GetEnvironmentVariable("DYNAMICS365_OAUTH_STATE_ENCRYPTION_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("DYNAMICS365_OAUTH_STATE_ENCRYPTION_KEY");
    }
}