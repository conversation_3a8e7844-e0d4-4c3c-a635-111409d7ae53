namespace Sleekflow.Exceptions;

public class SfExceedUsageLimitException : ErrorCodeException
{
    public string ResourceType { get; }

    public int CurrentUsage { get; }

    public int MaximumLimit { get; }

    public SfExceedUsageLimitException(string resourceType, int currentUsage, int maximumLimit)
        : base(
            ErrorCodeConstant.SfExceedUsageLimitException,
            $"Exceeded usage limit for {resourceType}. Current usage: {currentUsage}, Maximum limit: {maximumLimit}")
    {
        ResourceType = resourceType;
        CurrentUsage = currentUsage;
        MaximumLimit = maximumLimit;
    }
}