using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.WebCrawlingSessions;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class StartWebCrawlingSession
    : ITrigger<StartWebCrawlingSession.StartWebCrawlingSessionInput,
        StartWebCrawlingSession.StartWebCrawlingSessionOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IWebCrawlingSessionService _webCrawlingSessionService;

    public StartWebCrawlingSession(
        ISleekflowAuthorizationContext authorizationContext,
        IWebCrawlingSessionService webCrawlingSessionService)
    {
        _authorizationContext = authorizationContext;
        _webCrawlingSessionService = webCrawlingSessionService;
    }

    public class StartWebCrawlingSessionInput
    {
        [JsonProperty("url")]
        [Required]
        public string Url { get; set; }

        [JsonProperty("is_crawling_enabled")]
        [Required]
        public bool IsCrawlingEnabled { get; set; }

        [JsonConstructor]
        public StartWebCrawlingSessionInput(string url, bool isCrawlingEnabled)
        {
            Url = url;
            IsCrawlingEnabled = isCrawlingEnabled;
        }
    }

    public class StartWebCrawlingSessionOutput
    {
        [JsonProperty("web_crawling_session_id")]
        public string WebCrawlingSessionId { get; set; }

        [JsonConstructor]
        public StartWebCrawlingSessionOutput(string webCrawlingSessionId)
        {
            WebCrawlingSessionId = webCrawlingSessionId;
        }
    }

    public async Task<StartWebCrawlingSessionOutput> F(
        StartWebCrawlingSessionInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;

        var sessionId = await _webCrawlingSessionService.StartWebCrawlingSessionAsync(
            sleekflowCompanyId,
            input.Url,
            input.IsCrawlingEnabled);

        return new StartWebCrawlingSessionOutput(sessionId);
    }
}