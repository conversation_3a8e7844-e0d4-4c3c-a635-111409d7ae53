using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Payments;

public abstract class ProcessPaymentContext
{
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("provider_payment_id")]
    public string ProviderPaymentId { get; set; }

    [JsonConstructor]
    protected ProcessPaymentContext(
        string providerName,
        string providerPaymentId)
    {
        ProviderName = providerName;
        ProviderPaymentId = providerPaymentId;
    }
}