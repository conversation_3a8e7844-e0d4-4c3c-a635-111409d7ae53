﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.States;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.InflowActions.Vtex;

[TriggerGroup(ControllerNames.InflowActions)]
public class GetLoopThroughVtexOrdersProgress
    : ITrigger<
        GetLoopThroughVtexOrdersProgress.GetLoopThroughVtexOrdersProgressInput,
        GetLoopThroughVtexOrdersProgress.GetLoopThroughVtexOrdersProgressOutput>
{
    private readonly ILoopThroughObjectsService _loopThroughObjectsService;

    public GetLoopThroughVtexOrdersProgress(ILoopThroughObjectsService loopThroughObjectsService)
    {
        _loopThroughObjectsService = loopThroughObjectsService;
    }

    public class GetLoopThroughVtexOrdersProgressInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public GetLoopThroughVtexOrdersProgressInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class GetLoopThroughVtexOrdersProgressOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public GetLoopThroughVtexOrdersProgressOutput(
            long count,
            DateTime lastUpdateTime,
            string status)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            Status = status;
        }
    }

    public async Task<GetLoopThroughVtexOrdersProgressOutput?> F(
        GetLoopThroughVtexOrdersProgressInput input)
    {
        var output = await _loopThroughObjectsService.GetLoopThroughObjectsProgressAsync(
            ProviderNames.Vtex,
            input.SleekflowCompanyId,
            input.FlowHubWorkflowId,
            input.FlowHubWorkflowVersionedId);

        if (output is null)
        {
            return null;
        }

        return new GetLoopThroughVtexOrdersProgressOutput(
            output.Count,
            output.LastUpdateTime,
            output.Status);
    }
}