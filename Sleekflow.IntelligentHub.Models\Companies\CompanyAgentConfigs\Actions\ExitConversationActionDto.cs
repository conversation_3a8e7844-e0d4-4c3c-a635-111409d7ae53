using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class ExitConversationActionDto : BaseActionDto
{
    [JsonProperty("conditions")]
    public List<ExitConditionDto> Conditions { get; set; }

    [JsonConstructor]
    public ExitConversationActionDto(bool enabled, List<ExitConditionDto> conditions)
        : base(enabled)
    {
        Conditions = conditions;
    }

    public ExitConversationActionDto(ExitConversationAction action)
        : base(action)
    {
        Conditions = action.Conditions.Select(c => c.ToDto()).ToList();
    }
} 