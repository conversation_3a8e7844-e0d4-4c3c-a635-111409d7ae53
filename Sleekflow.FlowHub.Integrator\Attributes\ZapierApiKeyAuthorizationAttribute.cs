﻿using System.Net;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Sleekflow.FlowHub.Integrator.Utils;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Integrator.Attributes;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class ZapierApiKeyAuthorizationAttribute : Attribute, IAsyncAuthorizationFilter
{
    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        context.HttpContext.Request.Headers.TryGetValue("X-Sleekflow-Location", out var sleekflowLocation);

        if (!context.HttpContext.Request.Headers.TryGetValue(
                "X-Sleekflow-Zapier-Api-Key",
                out var sleekflowZapierApiKey) ||
            string.IsNullOrWhiteSpace(sleekflowZapierApiKey))
        {
            context.Result = new UnauthorizedObjectResult("Missing SleekFlow Zapier API key.");

            return;
        }

        try
        {
            var authenticateZapierApiKeyOutput =
                await TravisBackendFunctionsUtil.InvokeAsync<
                    TravisBackendFunctionsUtil.AuthenticateZapierApiKeyInput,
                    TravisBackendFunctionsUtil.AuthenticateZapierApiKeyOutput>(
                    sleekflowLocation,
                    "AuthenticateZapierApiKey",
                    new TravisBackendFunctionsUtil.AuthenticateZapierApiKeyInput(sleekflowZapierApiKey!));

            var sleekflowCompanyId = authenticateZapierApiKeyOutput.SleekflowCompanyId;
            context.HttpContext.Request.Headers[IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId] = sleekflowCompanyId;
        }
        catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.Unauthorized)
        {
            context.Result = new UnauthorizedResult();
        }
    }
}