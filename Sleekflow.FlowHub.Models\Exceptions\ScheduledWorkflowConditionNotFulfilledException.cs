﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Exceptions;

public class ScheduledWorkflowConditionNotFulfilledException : Exception
{
    [JsonProperty("state_id")]
    public string StateId { get; }

    [JsonProperty("condition_expression")]
    public string ConditionExpression { get; set; }

    [JsonConstructor]
    public ScheduledWorkflowConditionNotFulfilledException(
        string stateId,
        string conditionExpression)
        : base($"State {stateId} does not fulfill enrollment condition {conditionExpression}")
    {
        StateId = stateId;
        ConditionExpression = conditionExpression;
    }
}