﻿namespace Sleekflow.Exceptions.IntelligentHub;

public class SfKnowledgeBaseDocumentTypeNotSupportedException : ErrorCodeException
{
    public string DocumentType { get; }

    public SfKnowledgeBaseDocumentTypeNotSupportedException(string documentType)
        : base(
            ErrorCodeConstant.SfKnowledgeBaseDocumentTypeNotSupportedException,
            $"This document type is not supported. documentType=[{documentType}]",
            new Dictionary<string, object?>
            {
                {
                    "documentType", documentType
                }
            })
    {
        DocumentType = documentType;
    }
}