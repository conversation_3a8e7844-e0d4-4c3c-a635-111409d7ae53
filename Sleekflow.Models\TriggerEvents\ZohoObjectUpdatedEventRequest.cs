﻿using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class ZohoObjectUpdatedEventRequest
{
    public DateTimeOffset CreatedAt { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string ConnectionId { get; set; }

    public string ObjectId { get; set; }

    public string ObjectType { get; set; }

    public Dictionary<string, object?> ObjectDict { get; set; }

    [JsonConstructor]
    public ZohoObjectUpdatedEventRequest(
        DateTimeOffset createdAt,
        string sleekflowCompanyId,
        string connectionId,
        string objectId,
        string objectType,
        Dictionary<string, object?> objectDict)
    {
        CreatedAt = createdAt;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        ObjectId = objectId;
        ObjectType = objectType;
        ObjectDict = objectDict;
    }
}