using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooks
    : ITrigger<
        UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooks.
        UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput,
        UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooks.
        UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutput>
{
    private readonly IWhatsappCloudApiBusinessBalanceWebhookService _whatsappCloudApiBusinessBalanceWebhookService;

    public UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooks(
        IWhatsappCloudApiBusinessBalanceWebhookService whatsappCloudApiBusinessBalanceWebhookService)
    {
        _whatsappCloudApiBusinessBalanceWebhookService = whatsappCloudApiBusinessBalanceWebhookService;
    }

    public class UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_companyId")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [Validations.ValidateArray]
        [JsonProperty("facebook_business_ids")]
        public List<string> FacebookBusinessIds { get; set; }

        [Required]
        [JsonProperty("webhook_url")]
        public string WebhookUrl { get; set; }

        [JsonConstructor]
        public UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput(
            List<string> facebookBusinessIds,
            string sleekflowCompanyId,
            string webhookUrl)
        {
            FacebookBusinessIds = facebookBusinessIds;
            SleekflowCompanyId = sleekflowCompanyId;
            WebhookUrl = webhookUrl;
        }
    }

    public class UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutput
    {
    }

    public async Task<UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutput> F(
        UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput input)
    {
        await _whatsappCloudApiBusinessBalanceWebhookService.UpsertWebhooksAsync(
            input.SleekflowCompanyId,
            input.FacebookBusinessIds,
            input.WebhookUrl);

        return new UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutput();
    }
}