using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Evaluator;

public partial class ReActEvalTest
{
    private static IEnumerable<ReActEvalQuestion> GetTestCases()
    {
#pragma warning disable SA1515
        List<IEnumerable<ReActEvalQuestion>> list =
        [
            GetBasicTestCases(),
            GetSleekflowInformationGatheringTestCases(),
            GetChiliPiperWebhookTestCases(),
            GetBuyingPotentialDetectionTestCases(),
            GetAudioTranscriptionTestCases(),
        ];
#pragma warning restore SA1515

        // Use the Chili Piper test cases to validate field values
        return list.SelectMany(e => e).ToList();
    }

    private static CompanyAgentConfig GetAgentConfig(
        ToolsConfig toolsConfig,
        string instruction,
        bool isTranscriptionEnabled = false)
    {
        var agentConfig = AgentConfigUtils.GetAgentConfig();
        agentConfig.PromptInstruction = new PromptInstruction()
        {
            AdditionalInstructionCore = instruction,
        };
        agentConfig.ToolsConfig = toolsConfig;
        agentConfig.IsTranscriptionEnabled = isTranscriptionEnabled;
        return agentConfig;
    }

    private static IEnumerable<ReActEvalQuestion> GetBasicTestCases()
    {
        var toolsConfig = GetDefaultToolsConfig();

        // Test cases for information gathering and contact property updating
        yield return new ReActEvalQuestion(
            "Basic Information Gathering",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "My name is John Smith and my <NAME_EMAIL>. I'd like to learn more about your product.")
            ],
            expectExtractInformation: true,
            expectedContactProperties: new Dictionary<string, string>
            {
                {
                    "first_name", "John"
                },
                {
                    "last_name", "Smith"
                },
                {
                    "email", "<EMAIL>"
                }
            },
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                Please capture information from the conversation.

                === PROPERTIES ===
                [
                  {
                    "field": "first_name",
                    "description": "The customer's first name",
                    "required": false
                  },
                  {
                    "field": "last_name",
                    "description": "The customer's last name",
                    "required": false
                  },
                  {
                    "field": "email",
                    "description": "The customer's email address",
                    "required": false
                  },
                  {
                    "field": "company_name",
                    "description": "The customer's company name",
                    "required": false
                  },
                  {
                    "field": "phone_number",
                    "description": "The customer's phone number. It must be a phone number with country code. e.g. +852 6123-4567",
                    "required": false
                  },
                  {
                    "field": "employees_count",
                    "description": "The number of employees at the customer's company. The valid options are '1-19', '20-49', '50-499', '500+'.",
                    "required": false
                  },
                  {
                    "field": "country",
                    "description": "The customer's country",
                    "required": false
                  },
                  {
                    "field": "message",
                    "description": "Any additional message or notes from the customer",
                    "required": false
                  },
                  {
                    "field": "job_title",
                    "description": "The customer's job title",
                    "required": false
                  },
                  {
                    "field": "website",
                    "description": "The customer's company website",
                    "required": false
                  }
                ]
                === PROPERTIES ===

                Gather all the properties from the conversation and update the contact properties.

                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));

        yield return new ReActEvalQuestion(
            "Extract contact information from conversation",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'm Sarah Chen from ABC Corp. My <NAME_EMAIL> and you can reach me at +852 9123 4567.")
            ],
            expectExtractInformation: true,
            expectedContactProperties: new Dictionary<string, string>
            {
                {
                    "first_name", "Sarah"
                },
                {
                    "last_name", "Chen"
                },
                {
                    "email", "<EMAIL>"
                },
                {
                    "phone", "+852 9123 4567"
                },
                {
                    "company", "ABC Corp"
                }
            },
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                === INSTRUCTIONS ===
                Extract contact information from the conversation and update the contact properties.

                === PROPERTIES ===
                [
                  {
                    "field": "first_name",
                    "description": "The customer's first name",
                    "required": false
                  },
                  {
                    "field": "last_name",
                    "description": "The customer's last name",
                    "required": false
                  },
                  {
                    "field": "email",
                    "description": "The customer's email address",
                    "required": false
                  },
                  {
                    "field": "phone",
                    "description": "The customer's phone number",
                    "required": false
                  },
                  {
                    "field": "company",
                    "description": "The customer's company name",
                    "required": false
                  }
                ]
                === PROPERTIES ===

                Gather all the properties from the conversation and update the contact properties.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));

        yield return new ReActEvalQuestion(
            "Extract contact information with partial details",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hello, I'm David Wong. I work as a Marketing Manager at XYZ Limited.")
            ],
            expectExtractInformation: true,
            expectedContactProperties: new Dictionary<string, string>
            {
                {
                    "first_name", "David"
                },
                {
                    "last_name", "Wong"
                },
                {
                    "company", "XYZ Limited"
                },
                {
                    "job_title", "Marketing Manager"
                }
            },
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                === INSTRUCTIONS ===
                Extract contact information from the conversation and update the contact properties.

                === PROPERTIES ===
                [
                  {
                    "field": "first_name",
                    "description": "The customer's first name",
                    "required": false
                  },
                  {
                    "field": "last_name",
                    "description": "The customer's last name",
                    "required": false
                  },
                  {
                    "field": "email",
                    "description": "The customer's email address",
                    "required": false
                  },
                  {
                    "field": "company",
                    "description": "The customer's company name",
                    "required": false
                  },
                  {
                    "field": "job_title",
                    "description": "The customer's job title",
                    "required": false
                  }
                ]
                === PROPERTIES ===

                Gather all the properties from the conversation and update the contact properties.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));


        yield return new ReActEvalQuestion(
            "Extract contact information with business context",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'm Michael Lee from Tech Solutions Ltd. We're a software development company with about 75 employees, focusing on fintech solutions.")
            ],
            expectExtractInformation: true,
            expectedContactProperties: new Dictionary<string, string>
            {
                {
                    "first_name", "Michael"
                },
                {
                    "last_name", "Lee"
                },
                {
                    "company", "Tech Solutions Ltd"
                },
                {
                    "employees_count", "50-499"
                }
            },
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                === INSTRUCTIONS ===
                Extract contact information from the conversation and update the contact properties.

                === PROPERTIES ===
                [
                  {
                    "field": "first_name",
                    "description": "The customer's first name",
                    "required": false
                  },
                  {
                    "field": "last_name",
                    "description": "The customer's last name",
                    "required": false
                  },
                  {
                    "field": "email",
                    "description": "The customer's email address",
                    "required": false
                  },
                  {
                    "field": "company",
                    "description": "The customer's company name",
                    "required": false
                  },
                  {
                    "field": "employees_count",
                    "description": "The number of employees at the customer's company. The valid options are '1-19', '20-49', '50-499', '500+'.",
                    "required": false
                  },
                  {
                    "field": "industry",
                    "description": "The customer's industry",
                    "required": false
                  }
                ]
                === PROPERTIES ===

                Gather all the properties from the conversation and update the contact properties.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));

        yield return new ReActEvalQuestion(
            "Multi-message Information Gathering",
            [
                new ChatMessageContent(AuthorRole.User, "Hello, I'm interested in learning more about your services."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi there! I'd be happy to tell you more about our services. Could you share a bit about yourself and your company so I can provide more relevant information?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Sure, my name is Sarah Johnson. I work for a company called Bright Horizons Marketing."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thanks for sharing, Sarah! It's great to meet you. Could you tell me a bit more about Bright Horizons Marketing? What industry are you in, and roughly how many employees do you have?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "We're in the digital marketing industry, specializing in social media campaigns. We're a growing team of about 35 people."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's fantastic! Digital marketing is such a dynamic field. With 35 team members, you're at a great size to benefit from our solutions. Would you mind sharing your email address so I can send you some information tailored to digital marketing agencies?"),
                new ChatMessageContent(AuthorRole.User, "Yes, you can reach <NAME_EMAIL>")
            ],
            expectExtractInformation: true,
            expectedContactProperties: new Dictionary<string, string>
            {
                {
                    "first_name", "Sarah"
                },
                {
                    "last_name", "Johnson"
                },
                {
                    "email", "<EMAIL>"
                },
                {
                    "company", "Bright Horizons Marketing"
                },
                {
                    "employees_count", "20-49"
                },
                {
                    "industry", "digital marketing"
                }
            },
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                === INSTRUCTIONS ===
                Extract contact information from the conversation and update the contact properties.

                === PROPERTIES ===
                [
                  {
                    "field": "first_name",
                    "description": "The customer's first name",
                    "required": false
                  },
                  {
                    "field": "last_name",
                    "description": "The customer's last name",
                    "required": false
                  },
                  {
                    "field": "email",
                    "description": "The customer's email address",
                    "required": false
                  },
                  {
                    "field": "company",
                    "description": "The customer's company name",
                    "required": false
                  },
                  {
                    "field": "employees_count",
                    "description": "The number of employees at the customer's company. The valid options are '1-19', '20-49', '50-499', '500+'.",
                    "required": false
                  },
                  {
                    "field": "industry",
                    "description": "The customer's industry",
                    "required": false
                  }
                ]
                === PROPERTIES ===

                Gather all the properties from the conversation and update the contact properties.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));
    }

    private static IEnumerable<ReActEvalQuestion> GetSleekflowInformationGatheringTestCases()
    {
        var replyGenerationContext = new ReplyGenerationContext(
            "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
            Guid.NewGuid().ToString(),
            null,
            null,
            null);

        var agentConfig = AgentConfigUtils.GetAgentConfig();
        agentConfig.PromptInstruction = new PromptInstruction()
        {
            AdditionalInstructionCore =
                """
                === INSTRUCTIONS ===
                1. Extract contact information from the conversation (extract_fields_softly)
                2. Update the contact properties (update_contact_properties)

                === PROPERTIES ===
                [
                  {
                    "field": "business",
                    "description": "The customer's business",
                    "required": false,
                    "property_id": "403d074a-f02c-4a99-86e4-cc9551f0b1c7"
                  },
                  {
                    "field": "industry",
                    "description": "The customer's industry",
                    "required": false,
                    "property_id": "cfb6368d-8d37-4ac2-b3d1-845b3b4d8e36"
                  },
                  {
                    "field": "objectives",
                    "description": "The customer's objectives",
                    "required": false,
                    "property_id": "0d1e9518-e685-43d1-9d9d-3098e7ca73e0"
                  },
                  {
                    "field": "pain_points",
                    "description": "The customer's pain points",
                    "required": false,
                    "property_id": "7319f37c-c508-45da-8438-365084e74b2b"
                  },
                  {
                    "field": "common_messaging_channels",
                    "description": "The customer's common messaging channels",
                    "required": false,
                    "property_id": "2f62c02e-5dfd-4120-b92f-6f9a90a8da14"
                  },
                  {
                    "field": "integrations",
                    "description": "The customer's integrations",
                    "required": false,
                    "property_id": "1a7b67e4-6ca5-4110-bcea-341d47e64b1b"
                  },
                  {
                    "field": "regions",
                    "description": "The customer's regions",
                    "required": false,
                    "property_id": "aa522008-ebb9-4322-a7c2-2c55076fb1e7"
                  },
                  {
                    "field": "email",
                    "description": "The customer's email address",
                    "required": false,
                    "property_id": "357310f4-b823-4313-b4e0-d5d2e773ffd1"
                  },
                  {
                    "field": "employees_count",
                    "description": "The number of employees at the customer's company. The valid options are '1-19', '20-49', '50-499', '500+'.",
                    "required": false,
                    "property_id": "84fb8207-a857-4e1b-8350-23580a0a8229"
                  },
                ]
                === PROPERTIES ===
                """,
        };
        agentConfig.ToolsConfig = GetDefaultToolsConfig();

        yield return new ReActEvalQuestion(
            "Multi-message Information Gathering on SleekFlow",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'm interested in your customer engagement platform. My name is Sarah Johnson from Acme Corp."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hello Sarah! I'm delighted to hear you're interested in our customer engagement platform. I'd be happy to provide you with information about SleekFlow. Could you tell me a bit more about Acme Corp and what you're looking for in a customer engagement solution?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "We're a mid-sized company with about 200 employees. We're currently struggling with managing customer communications across different channels. Our team mainly uses email, WhatsApp, and Facebook Messenger."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Thank you for sharing that information, Sarah. Managing communications across multiple channels can definitely be challenging. SleekFlow specializes in unifying those channels into one platform. With 200 employees, you fall into our mid-market segment where we have great solutions. Could you share what specific pain points you're experiencing with your current communication setup?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Our main issue is that customer conversations get lost between teams, and we have no way to track the customer journey. We're also looking to integrate with our current CRM system, which is Salesforce."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "I understand those challenges completely. Fragmented conversations and lack of visibility into the customer journey can significantly impact your customer experience. SleekFlow addresses these exact issues by centralizing all communications in one place. And yes, we do offer Salesforce integration! May I ask which regions your business operates in?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "We're primarily in North America, but we're expanding to Europe next quarter. My <NAME_EMAIL> and my phone is ******-555-7890 if you want to send me more information."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That's excellent to know, Sarah. SleekFlow supports operations in both North America and Europe, so we can definitely accommodate your expansion plans. I've noted your contact details. What are your main objectives for implementing a new customer engagement platform?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Our main goal is to improve customer satisfaction and reduce response times. We also want better analytics to understand customer needs.")
            ],
            expectExtractInformation: true,
            expectedContactProperties: new Dictionary<string, string>
            {
                {
                    "403d074a-f02c-4a99-86e4-cc9551f0b1c7", "Acme Corp"
                },
                {
                    "0d1e9518-e685-43d1-9d9d-3098e7ca73e0",
                    "Improve customer satisfaction, reduce response times, better analytics to understand customer needs"
                },
                {
                    "7319f37c-c508-45da-8438-365084e74b2b",
                    "Customer conversations get lost between teams, no way to track the customer journey"
                },
                {
                    "2f62c02e-5dfd-4120-b92f-6f9a90a8da14", "Email, WhatsApp, and Facebook Messenger"
                },
                {
                    "1a7b67e4-6ca5-4110-bcea-341d47e64b1b", "Salesforce"
                },
                {
                    "aa522008-ebb9-4322-a7c2-2c55076fb1e7", "North America, Europe"
                },
                {
                    "357310f4-b823-4313-b4e0-d5d2e773ffd1", "<EMAIL>"
                },
                {
                    "84fb8207-a857-4e1b-8350-23580a0a8229", "50-499"
                },
            },
            replyGenerationContext: replyGenerationContext,
            agentConfig: agentConfig);
    }

    private static IEnumerable<ReActEvalQuestion> GetChiliPiperWebhookTestCases()
    {
        var toolsConfig = GetDefaultToolsConfig(
            new SleekflowSendMessageTool("85298696051", "whatsappcloudapi", null),
            new HubspotTool("your-hubspot-api-key"));

        var agentConfig = AgentConfigUtils.GetAgentConfig();
        agentConfig.EnricherConfigs = new List<EnricherConfig>()
        {
            new EnricherConfig
            {
                Type = "contact_properties",
                IsEnabled = true,
                Parameters = new Dictionary<string, string>()
                {
                    {
                        "PERMITTED_CONTACT_PROPERTIES_KEY", JsonConvert.SerializeObject(
                            new List<PermittedContactProperty>
                            {
                                new PermittedContactProperty("firstName", "The customer's first name"),
                                new PermittedContactProperty("lastName", "The customer's last name"),
                                new PermittedContactProperty(
                                    "AI - Touched",
                                    "Whether the contact has been touched by AI"),
                            })
                    }
                }
            }
        };
        agentConfig.PromptInstruction = new PromptInstruction()
        {
            AdditionalInstructionCore =
                """
                === INSTRUCTIONS ===
                You are a TaskExecutionAgent responsible for handling webhook events and taking appropriate actions.

                In this test, you'll receive a ChiliPiper booking webhook event in the INPUT section.

                When a booking is updated (either by the sales rep or the customer), we want to automatically
                send a confirmation message to the customer.

                When a booking is created (either by the sales rep or the customer), we want to automatically
                send a confirmation message to the customer.

                Your tasks are:
                1. Parse the ChiliPiper webhook event from the INPUT
                2. Extract relevant customer information (name, email, new and old booking details) in primaryGuestDataFields
                3. Send a personalized confirmation message to the customer using the send_message tool
                4. If the contact's field "AI - Touched" == true, update hubspot properties using the update_hubspot_properties tool
                   Here are the properties to update:
                   {
                     "latest_lead_entry_point": "Book a Demo",
                     "latest_lead_entry_point_drill_down": "AgentFlow"
                   }

                   If the contact's field "AI - Touched" != true, skip this step.
                5. Report the actions taken as structured JSON

                The confirmation message should:
                - A natural message that
                  - Thank the customer for booking a demo
                  - Meeting details
                  - Include a brief note about coming prepared with any questions they might have according to the CONVERSATION CONTEXT
                - Meeting details includes the webhook event information:
                  - Confirm the date and time of their booking
                  - Mention who will be conducting the demo
                  - Include the meeting link
                - The message should be an appropriate language (e.g. English / Traditional Chinese (Hong Kong) / Japanese / and more) according to the CONVERSATION CONTEXT

                === EXPECTED OUTPUT ===
                A JSON report of the actions taken, including the message sent.
                """,
        };
        agentConfig.ToolsConfig = toolsConfig;

        var bookingCreatedWebhook =
            """
            {
                "meetingIdChili": "3d66a344-e2cd-48af-92ff-006d4debaeb5",
                "meetingTitle": "Melvin <>  Annie @ SleekFlow | Meeting",
                "meetingDescription": "If you need to reschedule please use the link below:\nhttps://sleekflow.chilipiper.com/reschedule/3d66a344-e2cd-48af-92ff-006d4debaeb5\n\nLocation not specified",
                "meetingLocation": "https://meet.google.com/nym-aaui-ixo",
                "meetingStartTime": "2025-05-27T03:30:00Z",
                "meetingEndTime": "2025-05-27T04:00:00Z",
                "primaryGuestTimeZone": null,
                "hostEmail": "<EMAIL>",
                "hostFullName": "Annie SleekFlow",
                "hostIdChili": "670dd66222446e203b80a656",
                "hostCrmId": {
                    "value": "371274332590"
                },
                "assigneeEmail": "<EMAIL>",
                "assigneeFullName": "Annie SleekFlow",
                "assigneeIdChili": "670dd66222446e203b80a656",
                "assigneeCrmId": {
                    "value": "371274332590"
                },
                "primaryGuestEmail": "<EMAIL>",
                "primaryGuestDataFields": {
                    "Company Name": "Netoryxs",
                    "Email": "<EMAIL>",
                    "First Name": "Melvin",
                    "Last Name": "Morales",
                    "Number of Employees": "20-49",
                    "Phone Number": "+852 6109 6623"
                },
                "additionalGuests": null,
                "bookerEmail": null,
                "bookerName": null,
                "bookerIdChili": null,
                "bookerCrmId": null,
                "workspaceName": "Inbound Meetings",
                "workspaceIdChili": "628caa4a3a1a315a88c74ca2",
                "productFeatureType": "ConciergeRouter",
                "productFeatureName": "Inbound_Router",
                "productFeatureIdChili": "d0ef1839-e283-4955-b88d-18ebe1b9eba1",
                "distributionName": "Hong Kong and ROW - Tier 2 - 3 [Meeting]",
                "distributionIdChili": "89253f4a-5fbe-46d2-88d8-c881385dcf12",
                "meetingTypeName": "Demo Call",
                "meetingTypeIdChili": "4d76a14d-5f77-4331-af87-87d02e7f22a6",
                "conciergeRoutingId": "30036bf8-3c5c-40e6-a3aa-eba999107c45",
                "type": "Created"
            }
            """;

        var bookingRescheduleWebhook =
            """
            {
                "triggeredBy": "ExternalCalendar",
                "meetingIdChili": "809f02a8-edd4-44e9-87d5-54d7e7e492f7",
                "meetingTitle": "Mohamed <>  Wiam @ SleekFlow | Meeting",
                "previousMeetingTitle": "Mohamed <>  Wiam @ SleekFlow | Meeting",
                "meetingDescription": "If you need to reschedule please use the link below:\nhttps://sleekflow.chilipiper.com/reschedule/809f02a8-edd4-44e9-87d5-54d7e7e492f7\n\nLocation not specified",
                "previousMeetingDescription": "If you need to reschedule please use the link below:\nhttps://sleekflow.chilipiper.com/reschedule/809f02a8-edd4-44e9-87d5-54d7e7e492f7\n\nLocation not specified",
                "meetingLocation": "https://meet.google.com/orw-wnau-rnd",
                "previousMeetingLocation": "https://meet.google.com/orw-wnau-rnd",
                "meetingStartTime": "2025-05-27T07:15:00Z",
                "previousMeetingStartTime": "2025-05-26T08:30:00Z",
                "meetingEndTime": "2025-05-27T07:45:00Z",
                "previousMeetingEndTime": "2025-05-26T09:00:00Z",
                "primaryGuestTimeZone": null,
                "previousPrimaryGuestTimeZone": null,
                "hostEmail": "<EMAIL>",
                "previousHostEmail": "<EMAIL>",
                "hostFullName": "Wiam SleekFlow",
                "previousHostFullName": "Wiam SleekFlow",
                "hostIdChili": "66b1c6ebc50a54312c86cd49",
                "previousHostIdChili": "66b1c6ebc50a54312c86cd49",
                "hostCrmId": {
                    "value": "353094010138"
                },
                "assigneeEmail": "<EMAIL>",
                "previousAssigneeEmail": "<EMAIL>",
                "assigneeFullName": "Wiam SleekFlow",
                "previousAssigneeFullName": "Wiam SleekFlow",
                "assigneeIdChili": "66b1c6ebc50a54312c86cd49",
                "previousAssigneeIdChili": "66b1c6ebc50a54312c86cd49",
                "assigneeCrmId": {
                    "value": "353094010138"
                },
                "primaryGuestEmail": "<EMAIL>",
                "primaryGuestDataFields": {
                    "Country": "Egypt",
                    "Email": "<EMAIL>",
                    "First Name": "Mohamed",
                    "Last Name": "Hassan",
                    "Number of Employees": "1-19",
                    "Phone Number": "+852 6109 6623"
                },
                "additionalGuests": null,
                "previousAdditionalGuests": null,
                "bookerEmail": null,
                "previousBookerEmail": null,
                "bookerName": null,
                "previousBookerName": null,
                "bookerIdChili": null,
                "bookerCrmId": null,
                "previousBookerIdChili": null,
                "workspaceName": "Inbound Meetings",
                "workspaceIdChili": "628caa4a3a1a315a88c74ca2",
                "distributionName": "UAE Arabic - All Tier [Meeting]",
                "distributionIdChili": "2a746b31-8819-45d8-8b33-6a4d148aa6ee",
                "meetingTypeName": "Demo Call",
                "meetingTypeIdChili": "4d76a14d-5f77-4331-af87-87d02e7f22a6",
                "conciergeRoutingId": "********-b62a-438b-a946-c74f0e43ded7",
                "type": "Updated"
            }
            """;

        yield return new ReActEvalQuestion(
            "ChiliPiper Booking Created Webhook",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "您好，我想預約一個你們平台的示範。")
            ],
            new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                bookingCreatedWebhook),
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: true,
            agentConfig: agentConfig,
            expectHubspotUpdate: false);
        yield return new ReActEvalQuestion(
            "ChiliPiper Booking Created Webhook - Traditional Chinese",
            [
                new ChatMessageContent(AuthorRole.User, "您好，我想預約一個你們平台的示範。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我很樂意協助您預約我們平台的示範！為此，我需要您提供一些詳細資料。請問您能提供您的姓名、電子郵件地址、公司名稱、電話號碼和員工人數嗎？"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "好的，我叫 John Smith，電子郵件是 <EMAIL>，我在 Acme Corp 工作，電話號碼是 +1 555-123-4567。我們公司大約有 100 名員工。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "John，謝謝您提供資料！我已經為您預約了我們團隊的示範。您將很快收到一封確認電子郵件到 <EMAIL>，其中包含所有詳細資訊和一份日曆邀請。如果您在示範前需要重新安排時間或有任何疑問，請隨時與我們聯繫。我們期待向您展示我們的平台如何協助 Acme Corp！"),
            ],
            new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                bookingCreatedWebhook),
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: true,
            agentConfig: agentConfig,
            expectHubspotUpdate: false);
        yield return new ReActEvalQuestion(
            "ChiliPiper Booking Reschedule Webhook",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "您好，我預約左。")
            ],
            new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                bookingRescheduleWebhook),
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: true,
            agentConfig: agentConfig,
            expectHubspotUpdate: false);
        yield return new ReActEvalQuestion(
            "ChiliPiper Booking Created Webhook - Hubspot Update",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "您好，我想預約一個你們平台的示範。")
            ],
            new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                new Dictionary<string, string>
                {
                    {
                        "email", "<EMAIL>"
                    },
                    {
                        "phone", "+85261096623"
                    },
                    {
                        "AI - Touched", "true"
                    }
                },
                null,
                bookingCreatedWebhook),
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: true,
            agentConfig: agentConfig,
            expectHubspotUpdate: false);
    }

    private static IEnumerable<ReActEvalQuestion> GetBuyingPotentialDetectionTestCases()
    {
        var toolsConfig = GetDefaultToolsConfig();

        // Test cases for detecting buying potential and adding appropriate labels
        yield return new ReActEvalQuestion(
            "Enterprise Plan Interest - Large Company",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I'm the CTO at TechCorp. We have over 1000 employees and need a comprehensive customer engagement solution that can handle high volume messaging and advanced integrations."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hello! It's great to hear from you. With 1000+ employees, you'd definitely benefit from our enterprise solutions. Can you tell me more about your current messaging volume and integration requirements?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "We handle about 50,000 customer conversations monthly across WhatsApp, email, and social media. We need seamless CRM integration and advanced analytics. Budget isn't a major concern if the ROI is clear.")
            ],
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: false,
            expectHubspotUpdate: false,
            shouldAddLabels: true,
            expectedLabels: ["enterprise_potential", "high_volume_needs", "budget_flexible"],
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                You are a buying potential detection agent. Analyze conversations to identify leads with potential to purchase SleekFlow's Pro, Premium, or Enterprise plans.

                Based on conversation signals, add appropriate labels:

                ENTERPRISE POTENTIAL indicators:
                - Large company (500+ employees)
                - High messaging volume (10k+ monthly conversations)
                - Complex integration needs
                - Budget flexibility mentioned
                - Enterprise features discussed (advanced analytics, custom workflows, dedicated support)
                - Multi-team/department usage

                PREMIUM POTENTIAL indicators:
                - Medium-sized company (50-499 employees)
                - Moderate messaging volume (1k-10k monthly)
                - Multiple channel needs
                - Growth-oriented language
                - Advanced features interest (automation, integrations)

                PRO POTENTIAL indicators:
                - Small-medium business (10-99 employees)
                - Regular messaging needs (100-1k monthly)
                - Basic automation interest
                - Cost-conscious but value-focused

                LABELS TO ADD:
                - "enterprise_potential" - for enterprise plan prospects
                - "premium_potential" - for premium plan prospects
                - "pro_potential" - for pro plan prospects
                - "high_volume_needs" - for high messaging volume requirements
                - "integration_focused" - for integration requirements
                - "budget_flexible" - when budget is not a primary concern
                - "cost_conscious" - when price sensitivity is mentioned
                - "growth_focused" - for scaling/growth-oriented businesses
                - "automation_interest" - for automation feature interest

                Use the add_contact_labels function to add relevant labels based on the conversation analysis.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));

        yield return new ReActEvalQuestion(
            "Premium Plan Interest - Growing Mid-Size Company",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hello! I'm the Head of Customer Success at GrowthCo. We're a 150-person company experiencing rapid growth."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hi there! Congratulations on your growth! I'd love to help you with a customer engagement solution that can scale with your business. What challenges are you facing with customer communications?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "We're currently using basic tools but need something more robust. We handle about 3,000 customer conversations monthly across WhatsApp and email. We're looking for better automation and team collaboration features.")
            ],
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: false,
            expectHubspotUpdate: false,
            shouldAddLabels: true,
            expectedLabels: ["premium_potential", "growth_focused", "automation_interest"],
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                You are a buying potential detection agent. Analyze conversations to identify leads with potential to purchase SleekFlow's Pro, Premium, or Enterprise plans.

                Based on conversation signals, add appropriate labels:

                ENTERPRISE POTENTIAL indicators:
                - Large company (500+ employees)
                - High messaging volume (10k+ monthly conversations)
                - Complex integration needs
                - Budget flexibility mentioned
                - Enterprise features discussed (advanced analytics, custom workflows, dedicated support)
                - Multi-team/department usage

                PREMIUM POTENTIAL indicators:
                - Medium-sized company (50-499 employees)
                - Moderate messaging volume (1k-10k monthly)
                - Multiple channel needs
                - Growth-oriented language
                - Advanced features interest (automation, integrations)

                PRO POTENTIAL indicators:
                - Small-medium business (10-99 employees)
                - Regular messaging needs (100-1k monthly)
                - Basic automation interest
                - Cost-conscious but value-focused

                LABELS TO ADD:
                - "enterprise_potential" - for enterprise plan prospects
                - "premium_potential" - for premium plan prospects
                - "pro_potential" - for pro plan prospects
                - "high_volume_needs" - for high messaging volume requirements
                - "integration_focused" - for integration requirements
                - "budget_flexible" - when budget is not a primary concern
                - "cost_conscious" - when price sensitivity is mentioned
                - "growth_focused" - for scaling/growth-oriented businesses
                - "automation_interest" - for automation feature interest

                Use the add_contact_labels function to add relevant labels based on the conversation analysis.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));

        yield return new ReActEvalQuestion(
            "Pro Plan Interest - Small Business with Budget Concerns",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi, I run a small online store with about 25 employees. We're looking for a customer messaging solution but need to be mindful of costs."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Hello! I understand the importance of finding cost-effective solutions for small businesses. Can you tell me about your current customer communication volume and main pain points?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "We get about 500 customer inquiries per month, mostly through WhatsApp and email. We'd love some basic automation to help respond faster, but we can't afford enterprise pricing.")
            ],
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: false,
            expectHubspotUpdate: false,
            shouldAddLabels: true,
            expectedLabels: ["pro_potential", "cost_conscious", "automation_interest"],
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                You are a buying potential detection agent. Analyze conversations to identify leads with potential to purchase SleekFlow's Pro, Premium, or Enterprise plans.

                Based on conversation signals, add appropriate labels:

                ENTERPRISE POTENTIAL indicators:
                - Large company (500+ employees)
                - High messaging volume (10k+ monthly conversations)
                - Complex integration needs
                - Budget flexibility mentioned
                - Enterprise features discussed (advanced analytics, custom workflows, dedicated support)
                - Multi-team/department usage

                PREMIUM POTENTIAL indicators:
                - Medium-sized company (50-499 employees)
                - Moderate messaging volume (1k-10k monthly)
                - Multiple channel needs
                - Growth-oriented language
                - Advanced features interest (automation, integrations)

                PRO POTENTIAL indicators:
                - Small-medium business (10-99 employees)
                - Regular messaging needs (100-1k monthly)
                - Basic automation interest
                - Cost-conscious but value-focused

                LABELS TO ADD:
                - "enterprise_potential" - for enterprise plan prospects
                - "premium_potential" - for premium plan prospects
                - "pro_potential" - for pro plan prospects
                - "high_volume_needs" - for high messaging volume requirements
                - "integration_focused" - for integration requirements
                - "budget_flexible" - when budget is not a primary concern
                - "cost_conscious" - when price sensitivity is mentioned
                - "growth_focused" - for scaling/growth-oriented businesses
                - "automation_interest" - for automation feature interest

                Use the add_contact_labels function to add relevant labels based on the conversation analysis.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));

        yield return new ReActEvalQuestion(
            "Enterprise Plan Interest - Integration Heavy Requirements",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "We're evaluating customer engagement platforms for our fintech company. We need deep integrations with Salesforce, HubSpot, and our custom banking APIs."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "That sounds like a comprehensive integration requirement! Fintech companies often have complex needs. Can you share more about your team size and messaging volume?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "We have about 800 employees across multiple departments that would use this. We process roughly 25,000 customer interactions monthly and need advanced compliance features for financial regulations.")
            ],
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: false,
            expectHubspotUpdate: false,
            shouldAddLabels: true,
            expectedLabels: ["enterprise_potential", "integration_focused", "high_volume_needs"],
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                You are a buying potential detection agent. Analyze conversations to identify leads with potential to purchase SleekFlow's Pro, Premium, or Enterprise plans.

                Based on conversation signals, add appropriate labels:

                ENTERPRISE POTENTIAL indicators:
                - Large company (500+ employees)
                - High messaging volume (10k+ monthly conversations)
                - Complex integration needs
                - Budget flexibility mentioned
                - Enterprise features discussed (advanced analytics, custom workflows, dedicated support)
                - Multi-team/department usage

                PREMIUM POTENTIAL indicators:
                - Medium-sized company (50-499 employees)
                - Moderate messaging volume (1k-10k monthly)
                - Multiple channel needs
                - Growth-oriented language
                - Advanced features interest (automation, integrations)

                PRO POTENTIAL indicators:
                - Small-medium business (10-99 employees)
                - Regular messaging needs (100-1k monthly)
                - Basic automation interest
                - Cost-conscious but value-focused

                LABELS TO ADD:
                - "enterprise_potential" - for enterprise plan prospects
                - "premium_potential" - for premium plan prospects
                - "pro_potential" - for pro plan prospects
                - "high_volume_needs" - for high messaging volume requirements
                - "integration_focused" - for integration requirements
                - "budget_flexible" - when budget is not a primary concern
                - "cost_conscious" - when price sensitivity is mentioned
                - "growth_focused" - for scaling/growth-oriented businesses
                - "automation_interest" - for automation feature interest

                Use the add_contact_labels function to add relevant labels based on the conversation analysis.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));

        yield return new ReActEvalQuestion(
            "Mixed Signals - Startup with Growth Ambitions",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi! I'm the founder of a tech startup. We're currently just 15 people but planning to scale rapidly over the next year."),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Exciting! Startups often need solutions that can grow with them. What's your current customer communication situation and where do you see yourselves in 12 months?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "Right now we handle about 800 conversations monthly, but we're expecting to 10x that as we grow. We need something affordable now but scalable for the future. We're particularly interested in automation to help us scale efficiently.")
            ],
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: false,
            expectHubspotUpdate: false,
            shouldAddLabels: true,
            expectedLabels: ["premium_potential", "growth_focused", "automation_interest", "cost_conscious"],
            agentConfig: GetAgentConfig(
                toolsConfig,
                """
                You are a buying potential detection agent. Analyze conversations to identify leads with potential to purchase SleekFlow's Pro, Premium, or Enterprise plans.

                Based on conversation signals, add appropriate labels:

                ENTERPRISE POTENTIAL indicators:
                - Large company (500+ employees)
                - High messaging volume (10k+ monthly conversations)
                - Complex integration needs
                - Budget flexibility mentioned
                - Enterprise features discussed (advanced analytics, custom workflows, dedicated support)
                - Multi-team/department usage

                PREMIUM POTENTIAL indicators:
                - Medium-sized company (50-499 employees)
                - Moderate messaging volume (1k-10k monthly)
                - Multiple channel needs
                - Growth-oriented language
                - Advanced features interest (automation, integrations)

                PRO POTENTIAL indicators:
                - Small-medium business (10-99 employees)
                - Regular messaging needs (100-1k monthly)
                - Basic automation interest
                - Cost-conscious but value-focused

                LABELS TO ADD:
                - "enterprise_potential" - for enterprise plan prospects
                - "premium_potential" - for premium plan prospects
                - "pro_potential" - for pro plan prospects
                - "high_volume_needs" - for high messaging volume requirements
                - "integration_focused" - for integration requirements
                - "budget_flexible" - when budget is not a primary concern
                - "cost_conscious" - when price sensitivity is mentioned
                - "growth_focused" - for scaling/growth-oriented businesses
                - "automation_interest" - for automation feature interest

                Use the add_contact_labels function to add relevant labels based on the conversation analysis.
                """),
            replyGenerationContext: new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null));
    }

    private static IEnumerable<ReActEvalQuestion> GetAudioTranscriptionTestCases()
    {
        var toolsConfig = GetDefaultToolsConfig();

        // New test case for file processing with internal note creation
        yield return new ReActEvalQuestion(
            "File Processing - Audio File with Internal Note Creation",
            questionContexts: null, // Using SfChatEntriesQuestionContexts instead
            new ReplyGenerationContext(
                "5406fdfa-1e8c-4a76-91f6-89301c0d95a1",
                Guid.NewGuid().ToString(),
                null,
                null,
                null),
            GetAgentConfig(
                toolsConfig,
                """
                === INSTRUCTIONS ===
                You are a background processing agent that handles conversations with file attachments.

                When you detect files in the conversation:
                1. Check if the latest message has any files in CONVERSATION CONTEXT. There are two special patterns in the message, including `[FILE CONTENT` and `[FILE ATTACHED` patterns
                2. If any file is detected, extract transcribed content from them. If there is no transcribed content or no file is detected, do not add an internal note.
                3. Use the add_internal_note function to record the transcribed data as an internal note

                === INTERNAL NOTE PATTERN ===
                The internal note pattern is:
                `Transcribed media file ($Language): "$TranscribedContent"`

                If the transcribed content is not in English, include a translation in English after the original content. Use the following pattern: `Transcribed media file ($Language): "$TranscribedContent" (Translation: "$TranslatedContent")`

                === FILES MESSAGE PATTERN ===

                1. `FILE CONTENT` includes the metadata and the transcribed content
                2. `FILE ATTACHED` contains the metadata only

                The file content will be provided in the format:
                - [FILE CONTENT - {mimeType}]: {transcribed_content} [END FILE CONTENT]
                - Or [FILE ATTACHED: {mimeType}, Size: {size} bytes, URL: {url}]
                """,
                true),
            expectExtractInformation: false,
            expectedContactProperties: null,
            shouldSendMessage: false,
            expectHubspotUpdate: false,
            shouldAddLabels: false,
            expectedLabels: null,
            sfChatEntriesQuestionContexts:
            [
                new SfChatEntry()
                {
                    User = "Hi, I sent you an audio message about SleekFlow features", Bot = null, Files = null
                },
                new SfChatEntry()
                {
                    User = string.Empty,
                    Bot = null,
                    Files =
                    [
                        new SfChatEntryFile(
                            url:
                            "https://sleekflowmedia.blob.core.windows.net/intelligent-hub-test-case/what%20is%20sleekflow%20and%20how%20much.mp3?sp=r&st=2025-06-05T15:27:31Z&se=2099-06-05T23:27:31Z&spr=https&sv=2024-11-04&sr=b&sig=lhah7m69qVlloBVpBWHhh4TpZlxucxmGwGmRc5pnE84%3D",
                            mimeType: "audio/mpeg",
                            fileSize: 1_081_180)
                    ]
                }
            ],
            shouldAddInternalNote: true,
            expectedInternalNoteContent: "我想問你哋，你哋係咪 SleekFlow？ 同埋你哋 support 啲乜嘢 feature？ 唔可以講多少少？");
    }
}