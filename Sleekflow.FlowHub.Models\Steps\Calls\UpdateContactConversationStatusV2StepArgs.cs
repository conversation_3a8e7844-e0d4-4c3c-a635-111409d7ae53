﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactConversationStatusV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.update-contact-conversation-status";

    [Required]
    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Conversation;

    [JsonConstructor]
    public UpdateContactConversationStatusV2StepArgs(
        string status)
    {
        Status = status;
    }
}