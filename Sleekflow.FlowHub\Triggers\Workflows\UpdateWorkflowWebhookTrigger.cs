﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;
using Sleekflow.FlowHub.WorkflowWebhookTriggers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class UpdateWorkflowWebhookTrigger
    : ITrigger<
        UpdateWorkflowWebhookTrigger.UpdateWorkflowWebhookTriggerInput,
        UpdateWorkflowWebhookTrigger.UpdateWorkflowWebhookTriggerOutput>
{
    private readonly IWorkflowWebhookTriggerService _workflowWebhookTriggerService;

    public UpdateWorkflowWebhookTrigger(IWorkflowWebhookTriggerService workflowWebhookTriggerService)
    {
        _workflowWebhookTriggerService = workflowWebhookTriggerService;
    }

    public class UpdateWorkflowWebhookTriggerInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_webhook_trigger_id")]
        [Required]
        public string WorkflowWebhookTriggerId { get; set; }

        [JsonProperty(WorkflowWebhookTrigger.PropertyNameObjectIdExpression)]
        [Required]
        public string ObjectIdExpression { get; set; }

        [JsonProperty("object_type")]
        [Required]
        [AllowedStringValues(
            isIgnoreCase: false,
            "Contact",
            "Contact.Id",
            "Contact.PhoneNumber",
            "Contact.Email")]
        public string ObjectType { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateWorkflowWebhookTriggerInput(
            string sleekflowCompanyId,
            string workflowWebhookTriggerId,
            string objectIdExpression,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowWebhookTriggerId = workflowWebhookTriggerId;
            ObjectIdExpression = objectIdExpression;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateWorkflowWebhookTriggerOutput
    {
        [JsonProperty("workflow_webhook_trigger")]
        public WorkflowWebhookTrigger? WorkflowWebhookTrigger { get; set; }

        [JsonConstructor]
        public UpdateWorkflowWebhookTriggerOutput(
            WorkflowWebhookTrigger? workflowWebhookTrigger)
        {
            WorkflowWebhookTrigger = workflowWebhookTrigger;
        }
    }

    public async Task<UpdateWorkflowWebhookTriggerOutput> F(UpdateWorkflowWebhookTriggerInput input)
    {
        var updatedBy = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var workflowWebhookTrigger =
            await _workflowWebhookTriggerService.UpdateWorkflowWebhookTriggerAsync(
                input.WorkflowWebhookTriggerId,
                input.SleekflowCompanyId,
                input.ObjectIdExpression,
                input.ObjectType,
                updatedBy);

        return new UpdateWorkflowWebhookTriggerOutput(workflowWebhookTrigger);
    }
}