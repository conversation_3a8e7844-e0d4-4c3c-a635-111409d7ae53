﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Workers.Triggers.Hubspot;

public class SyncObjectsOrchestrator
{
    public class SyncObjectsOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonConstructor]
        public SyncObjectsOrchestratorCustomStatusOutput(long count, DateTime lastUpdateTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
        }
    }

    public class SyncObjectsOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public SyncObjectsOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("Hubspot_SyncObjects_Orchestrator")]
    public async Task<SyncObjectsOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var syncObjectsInput = context.GetInput<SyncObjects.SyncObjectsInput>();

        context.SetCustomStatus(new SyncObjectsOrchestratorCustomStatusOutput(0, context.CurrentUtcDateTime));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        const long limit = 100L;

        var totalCount = 0L;
        var after = (string?) null;
        while (true)
        {
            var syncObjectsBatchOutput = await context
                .CallActivityAsync<SyncObjectsBatch.SyncObjectsBatchOutput>(
                    "Hubspot_SyncObjects_Batch",
                    new SyncObjectsBatch.SyncObjectsBatchInput(
                        syncObjectsInput.SleekflowCompanyId,
                        limit,
                        after,
                        syncObjectsInput.EntityTypeName,
                        syncObjectsInput.FilterGroups,
                        syncObjectsInput.FieldFilters),
                    taskOptions);

            totalCount += syncObjectsBatchOutput.Count;
            after = syncObjectsBatchOutput.After;

            context.SetCustomStatus(
                new SyncObjectsOrchestratorCustomStatusOutput(totalCount, context.CurrentUtcDateTime));

            if (syncObjectsBatchOutput.After == null)
            {
                break;
            }

            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
                CancellationToken.None);
        }

        return new SyncObjectsOrchestratorOutput(totalCount);
    }
}