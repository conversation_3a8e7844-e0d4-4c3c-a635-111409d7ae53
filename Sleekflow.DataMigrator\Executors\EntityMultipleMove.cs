﻿using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Polly;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;
using JsonConfig = Sleekflow.JsonConfigs.JsonConfig;

namespace Sleekflow.DataMigrator.Executors;

internal class EntityMultipleMove : IExecutor
{
    private readonly CosmosClient _sourceCosmosClient;
    private readonly CosmosClient _targetCosmosClient;

    private string? _sourceDatabaseId;
    private List<string>? _sourceContainerIds;
    private string? _targetDatabaseId;
    private string? _targetContainerId;

    public EntityMultipleMove(
        DbConfig sourceDbConfig,
        DbConfig targetDbConfig)
    {
        _sourceCosmosClient = new CosmosClient(
            sourceDbConfig.Endpoint,
            sourceDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });

        _targetCosmosClient = new CosmosClient(
            targetDbConfig.Endpoint,
            targetDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Entity Multiple Move";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_sourceCosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(
                           _sourceCosmosClient,
                           selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerIds = Prompt.MultiSelect(
            "Select your database",
            containerIds);

        _sourceDatabaseId = selectedDatabaseId;
        _sourceContainerIds = selectedContainerIds.ToList();

        var (targetDatabaseId, targetContainerId) = await PromptAsync(_targetCosmosClient, "target");

        _targetDatabaseId = targetDatabaseId;
        _targetContainerId = targetContainerId;
    }

    private static async Task<(string SelectedDatabaseId, string SelectedContainerId)> PromptAsync(
        CosmosClient cosmosClient,
        string prefix)
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            $"Select your {prefix} database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerId = Prompt.Select(
            $"Select your {prefix} database",
            containerIds);

        return (selectedDatabaseId, selectedContainerId);
    }

    public async Task ExecuteAsync()
    {
        foreach (var sourceContainerId in _sourceContainerIds!)
        {
            var count = await MoveObjects(sourceContainerId);

            Console.WriteLine(
                $"Completed from {_sourceDatabaseId}.{sourceContainerId} to {_targetDatabaseId}.{_targetContainerId} for {count} objects");
        }
    }

    private async Task<int> MoveObjects(string sourceContainerId)
    {
        var retryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                21,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 4)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    // contact, company, sales_order, user, sales_order_item, opportunity, store
                    return TimeSpan.FromSeconds(0.07) * retryCount;
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    Console.WriteLine(
                        $"TooManyRequests retryCount=[{retryCount}], timeSpan=[{timeSpan}], sourceContainerId=[{sourceContainerId}], targetContainerId=[{_targetContainerId}]");

                    return Task.CompletedTask;
                });

        var sourceDatabase = _sourceCosmosClient.GetDatabase(_sourceDatabaseId);
        var sourceContainer = sourceDatabase.GetContainer(sourceContainerId);
        var targetDatabase = _targetCosmosClient.GetDatabase(_targetDatabaseId);
        var targetContainer = targetDatabase.GetContainer(_targetContainerId);
        var i = 0;

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(sourceContainer),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 160
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(
                        async () =>
                            await MigrateObject(dict, targetContainer, token, sourceContainerId));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }

    private static async Task<int> MigrateObject(
        Dictionary<string, object?> dict,
        Container targetContainer,
        CancellationToken token,
        string sourceContainerId)
    {
        dict.Remove("sys_partition_id");

        var entityTypeName = sourceContainerId switch
        {
            "activity" => "Activity",
            "company" => "Company",
            "contact" => "Contact",
            "lead" => "Lead",
            "note" => "Note",
            "opportunity" => "Opportunity",
            "user" => "User",
            "sales_order" => "SalesOrder",
            "sales_order_item" => "SalesOrderItem",
            "store" => "Store",
            "entity_event" => "EntityEvent",
            _ => throw new Exception("The type is incorrect")
        };

        dict["sys_entity_type_name"] = entityTypeName;

        await targetContainer.UpsertItemAsync(
            dict,
            new PartitionKey((string) dict["id"]!),
            new ItemRequestOptions
            {
                EnableContentResponseOnWrite = false,
            },
            token);

        return 1;
    }
}