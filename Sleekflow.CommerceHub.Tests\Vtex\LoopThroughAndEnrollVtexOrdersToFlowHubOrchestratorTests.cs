﻿using Microsoft.DurableTask;
using Moq;
using Sleekflow.CommerceHub.Models.States.StatusQueryGetOutput;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.CommerceHub.Workers.Triggers.Vtex;

namespace Sleekflow.CommerceHub.Tests.Vtex;

[TestFixture]
public class LoopThroughAndEnrollVtexOrdersToFlowHubOrchestratorTests
{
    private LoopThroughAndEnrollVtexOrdersToFlowHubOrchestrator _orchestrator;
    private Mock<TaskOrchestrationContext> _mockContext;

    [SetUp]
    public void SetUp()
    {
        _orchestrator = new LoopThroughAndEnrollVtexOrdersToFlowHubOrchestrator();

        _mockContext = new Mock<TaskOrchestrationContext>();
    }

    [Test]
    public async Task RunOrchestrator_WithMultiplePages_ShouldProcessAllPages()
    {
        // Arrange
        var input = new LoopThroughAndEnrollVtexOrdersToFlowHubInput(
            "test-company-id",
            "test-workflow-id",
            "test-workflow-versioned-id",
            "test-vtex-auth-id",
            new VtexGetOrdersSearchCondition(
                DateTimeOffset.UtcNow.AddDays(-7),
                DateTimeOffset.UtcNow,
                "order-created"));

        var currentTime = DateTimeOffset.UtcNow;

        // Mock GetInput
        _mockContext.Setup(x => x.GetInput<LoopThroughAndEnrollVtexOrdersToFlowHubInput>())
                   .Returns(input);

        // Mock CurrentUtcDateTime - 需要返回DateTime，因为LoopThroughVtexOrdersCustomStatusMetaData需要DateTime
        _mockContext.Setup(x => x.CurrentUtcDateTime)
                   .Returns(currentTime.DateTime);

        // Mock SetCustomStatus (void method)
        _mockContext.Setup(x => x.SetCustomStatus(It.IsAny<object>()));

        // Mock CreateTimer
        _mockContext.Setup(x => x.CreateTimer(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
                   .Returns(Task.CompletedTask);

        // Mock CallActivityAsync for different pages
        _mockContext.SetupSequence(x => x.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
                "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
                It.IsAny<LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput>(),
                It.IsAny<TaskOptions>()))
            .ReturnsAsync(new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(10, true, currentTime.AddMinutes(-1)))
            .ReturnsAsync(new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(5, false, currentTime.AddMinutes(-2)));

        // Act
        var result = await _orchestrator.RunOrchestrator(_mockContext.Object);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(15, result.TotalCount);

        // Verify CallActivityAsync was called twice
        _mockContext.Verify(x => x.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
            "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
            It.IsAny<LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput>(),
            It.IsAny<TaskOptions>()), Times.Exactly(2));

        // Verify SetCustomStatus was called (at least once for initialization)
        _mockContext.Verify(x => x.SetCustomStatus(It.IsAny<object>()),
            Times.AtLeastOnce);
    }

    [Test]
    public async Task RunOrchestrator_WithEmptyResult_ShouldReturnZeroCount()
    {
        // Arrange
        var input = new LoopThroughAndEnrollVtexOrdersToFlowHubInput(
            "test-company-id",
            "test-workflow-id",
            "test-workflow-versioned-id",
            "test-vtex-auth-id",
            new VtexGetOrdersSearchCondition(
                DateTimeOffset.UtcNow.AddDays(-7),
                DateTimeOffset.UtcNow,
                "order-created"));

        var currentTime = DateTimeOffset.UtcNow;

        // Mock GetInput
        _mockContext.Setup(x => x.GetInput<LoopThroughAndEnrollVtexOrdersToFlowHubInput>())
                   .Returns(input);

        // Mock CurrentUtcDateTime - 需要返回DateTime，因为LoopThroughVtexOrdersCustomStatusMetaData需要DateTime
        _mockContext.Setup(x => x.CurrentUtcDateTime)
                   .Returns(currentTime.DateTime);

        // Mock SetCustomStatus
        _mockContext.Setup(x => x.SetCustomStatus(It.IsAny<object>()));

        // Mock CallActivityAsync returning empty result
        _mockContext.Setup(x => x.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
                "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
                It.IsAny<LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput>(),
                It.IsAny<TaskOptions>()))
            .ReturnsAsync(new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(0, false, null));

        // Act
        var result = await _orchestrator.RunOrchestrator(_mockContext.Object);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(0, result.TotalCount);

        // Verify CallActivityAsync was called only once (then broke out of loop)
        _mockContext.Verify(x => x.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
            "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
            It.IsAny<LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput>(),
            It.IsAny<TaskOptions>()), Times.Once);
    }

    [Test]
    public async Task RunOrchestrator_WhenExceedsMaxPage_ShouldResetPageAndAdjustTimeWindow()
    {
        // Arrange
        var input = new LoopThroughAndEnrollVtexOrdersToFlowHubInput(
            "test-company-id",
            "test-workflow-id",
            "test-workflow-versioned-id",
            "test-vtex-auth-id",
            new VtexGetOrdersSearchCondition(
                DateTimeOffset.UtcNow.AddDays(-7),
                DateTimeOffset.UtcNow,
                "order-created"));

        var currentTime = DateTimeOffset.UtcNow;
        var earliestOrderTime = currentTime.AddHours(-1);

        // Mock GetInput
        _mockContext.Setup(x => x.GetInput<LoopThroughAndEnrollVtexOrdersToFlowHubInput>())
                   .Returns(input);

        // Mock CurrentUtcDateTime
        _mockContext.Setup(x => x.CurrentUtcDateTime)
                   .Returns(currentTime.DateTime);

        // Mock SetCustomStatus
        _mockContext.Setup(x => x.SetCustomStatus(It.IsAny<object>()));

        // Mock CreateTimer
        _mockContext.Setup(x => x.CreateTimer(It.IsAny<DateTime>(), It.IsAny<CancellationToken>()))
                   .Returns(Task.CompletedTask);

        // Mock CallActivityAsync - simulate multiple calls that would exceed MaxPage
        // First 30 calls return data with hasMore = true, 31st call returns no data
        var callCount = 0;
        _mockContext.Setup(x => x.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
                "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
                It.IsAny<LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput>(),
                It.IsAny<TaskOptions>()))
            .Returns(() =>
            {
                callCount++;
                if (callCount <= 31)
                {
                    return Task.FromResult(new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(1, true, earliestOrderTime));
                }
                else
                {
                    return Task.FromResult(new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(0, false, null));
                }
            });

        // Act
        var result = await _orchestrator.RunOrchestrator(_mockContext.Object);

        // Assert
        Assert.IsNotNull(result);
        Assert.AreEqual(31, result.TotalCount);

        // Verify CallActivityAsync was called at least 31 times
        _mockContext.Verify(x => x.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
            "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
            It.IsAny<LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput>(),
            It.IsAny<TaskOptions>()), Times.AtLeast(31));
    }

    [Test]
    public async Task RunOrchestrator_ShouldSetCorrectCustomStatus()
    {
        // Arrange
        var input = new LoopThroughAndEnrollVtexOrdersToFlowHubInput(
            "test-company-id",
            "test-workflow-id",
            "test-workflow-versioned-id",
            "test-vtex-auth-id",
            new VtexGetOrdersSearchCondition(
                DateTimeOffset.UtcNow.AddDays(-7),
                DateTimeOffset.UtcNow,
                "order-created"));

        var currentTime = DateTimeOffset.UtcNow;
        var processedTime = currentTime.AddMinutes(-5);

        _mockContext.Setup(x => x.GetInput<LoopThroughAndEnrollVtexOrdersToFlowHubInput>())
                   .Returns(input);

        _mockContext.Setup(x => x.CurrentUtcDateTime)
                   .Returns(currentTime.DateTime);

        var capturedStatuses = new List<object>();
        _mockContext.Setup(x => x.SetCustomStatus(It.IsAny<object>()))
                   .Callback<object>(status => capturedStatuses.Add(status));

        _mockContext.Setup(x => x.CallActivityAsync<LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput>(
                "LoopThroughAndEnrollVtexOrdersToFlowHub_Batch",
                It.IsAny<LoopThroughAndEnrollVtexOrdersToFlowHubBatchInput>(),
                It.IsAny<TaskOptions>()))
            .ReturnsAsync(new LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(5, false, processedTime));

        // Act
        await _orchestrator.RunOrchestrator(_mockContext.Object);

        // Assert
        Assert.IsTrue(capturedStatuses.Count >= 2, "Should have at least initial and final status");

        // Initial status should have count 0
        var initialStatus = capturedStatuses[0] as CustomStatus;
        Assert.IsNotNull(initialStatus);
        Assert.AreEqual(0, initialStatus!.Count);

        // Final status should have count 5 and processed time
        var finalStatus = capturedStatuses[capturedStatuses.Count - 1] as CustomStatus;
        Assert.IsNotNull(finalStatus);
        Assert.AreEqual(5, finalStatus!.Count);
    }
}