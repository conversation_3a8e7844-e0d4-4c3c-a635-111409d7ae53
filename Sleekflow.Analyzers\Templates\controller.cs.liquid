using Microsoft.AspNetCore.Mvc;
using Sleekflow.Mvc.Func;
{% if filters %}
using Sleekflow.Mvc.Func.Abstractions;
{% endif %}
using Sleekflow.Outputs;

namespace {{ controller_namespace_name }};

[ApiVersion("1.0")]
[ApiController]
[Route("{{ controller_route }}")]
public class {{ controller_name }}Controller : ControllerBase
{
    private readonly ILogger<{{ controller_name }}Controller> _logger;
    private readonly IFuncService _funcService;
{% for filter in filters %}
    private readonly {{ filter.fully_qualified_name }} _{{ filter.variable_name }};
{% endfor %}
{% for endpoint in endpoints %}
    {% assign trigger = endpoint.trigger %}
    private readonly {{ trigger.namespace_name }}.{{ trigger.class_name }} _{{ trigger.variable_name }};
{% endfor %}

    public {{ controller_name }}Controller(
        ILogger<{{ controller_name }}Controller> logger,
        IFuncService funcService,
{% for filter in filters %}
        {{ filter.fully_qualified_name }} {{ filter.variable_name }},
{% endfor %}
{% for endpoint in endpoints %}
        {% assign trigger = endpoint.trigger %}
        {{ trigger.namespace_name }}.{{ trigger.class_name }} {{ trigger.variable_name }}{% unless forloop.last %},{% endunless %}
{% endfor %}
    )
    {
        _logger = logger;
        _funcService = funcService;
{% for endpoint in endpoints %}
        {% assign trigger = endpoint.trigger %}
        _{{ trigger.variable_name }} = {{ trigger.variable_name }};
{% endfor %}
{% for filter in filters %}
        _{{ filter.variable_name }} = {{ filter.variable_name }};
{% endfor %}
    }

{% for endpoint in endpoints %}
    {% assign trigger = endpoint.trigger %}
    {% assign filters = endpoint.filters %}
    [Route("{{ trigger.class_name }}")]
    [HttpPost]
    [Consumes("application/json")]
    [Produces("application/json")]
    public async Task<ActionResult<Output<{{ trigger.namespace_name }}.{{ trigger.class_name }}.{{ trigger.class_name }}Output>>> {{ trigger.class_name }}(
        [FromBody] {{ trigger.namespace_name }}.{{ trigger.class_name }}.{{ trigger.class_name }}Input {{ trigger.variable_name }}Input)
    {
        return await _funcService.Run1Async(
            (HttpContext.Request, {{ trigger.variable_name }}Input),
            _{{ trigger.variable_name }}.F,
            HttpContext.Request.Path
            {% if filters.size > 0 %}
            , new List<IFuncFilter> {
              {% for filter in filters %}
              _{{filter.variable_name}}{% unless forloop.last %},{% endunless %}
              {% endfor %}
              }
            {% endif %}
            );
    }
{% endfor %}

}