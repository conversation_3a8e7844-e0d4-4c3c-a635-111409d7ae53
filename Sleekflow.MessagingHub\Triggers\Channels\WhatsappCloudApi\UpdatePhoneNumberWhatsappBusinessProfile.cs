using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Channels.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Channels)]
public class UpdatePhoneNumberWhatsappBusinessProfile
    : ITrigger<UpdatePhoneNumberWhatsappBusinessProfile.UpdatePhoneNumberWhatsappBusinessProfileInput,
        UpdatePhoneNumberWhatsappBusinessProfile.UpdatePhoneNumberWhatsappBusinessProfileOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IChannelService _channelService;

    public UpdatePhoneNumberWhatsappBusinessProfile(
        IWabaService wabaService,
        IChannelService channelService)
    {
        _wabaService = wabaService;
        _channelService = channelService;
    }

    public class UpdatePhoneNumberWhatsappBusinessProfileInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; }

        [Required]
        [JsonProperty("messaging_hub_phone_number_id")]
        public string MessagingHubPhoneNumberId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("update_phone_number_business_profile")]
        [Validations.ValidateObject]
        public UpdatePhoneNumberBusinessProfileRequest UpdatePhoneNumberBusinessProfile { get; set; }

        [JsonConstructor]
        public UpdatePhoneNumberWhatsappBusinessProfileInput(
            string sleekflowCompanyId,
            string messagingHubWabaId,
            string messagingHubPhoneNumberId,
            string sleekflowStaffId,
            UpdatePhoneNumberBusinessProfileRequest updatePhoneNumberBusinessProfile)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            MessagingHubWabaId = messagingHubWabaId;
            MessagingHubPhoneNumberId = messagingHubPhoneNumberId;
            SleekflowStaffId = sleekflowStaffId;
            UpdatePhoneNumberBusinessProfile = updatePhoneNumberBusinessProfile;
        }
    }

    public class UpdatePhoneNumberWhatsappBusinessProfileOutput
    {
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; }

        [JsonProperty("messaging_hub_phone_number_id")]
        public string MessagingHubPhoneNumberId { get; set; }

        [JsonProperty("whatsapp_business_profile")]
        public FacebookBusinessProfileResult WhatsappBusinessProfile { get; set; }

        [JsonConstructor]
        public UpdatePhoneNumberWhatsappBusinessProfileOutput(
            string messagingHubWabaId,
            string messagingHubPhoneNumberId,
            FacebookBusinessProfileResult whatsappBusinessProfile)
        {
            MessagingHubWabaId = messagingHubWabaId;
            MessagingHubPhoneNumberId = messagingHubPhoneNumberId;
            WhatsappBusinessProfile = whatsappBusinessProfile;
        }
    }

    public async Task<UpdatePhoneNumberWhatsappBusinessProfileOutput> F(UpdatePhoneNumberWhatsappBusinessProfileInput input)
    {
        var waba = await _wabaService.GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
            input.MessagingHubWabaId,
            input.SleekflowCompanyId,
            input.MessagingHubPhoneNumberId);

        var whatsappBusinessProfile = await _channelService.UpdatePhoneNumberWhatsappBusinessProfileAsync(
            waba,
            input.SleekflowCompanyId,
            input.MessagingHubPhoneNumberId,
            input.SleekflowStaffId,
            input.UpdatePhoneNumberBusinessProfile);

        return new UpdatePhoneNumberWhatsappBusinessProfileOutput(
            input.MessagingHubWabaId,
            input.MessagingHubPhoneNumberId,
            whatsappBusinessProfile);
    }
}