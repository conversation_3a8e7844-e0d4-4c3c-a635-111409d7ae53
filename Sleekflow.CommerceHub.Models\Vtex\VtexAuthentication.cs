﻿using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Vtex;

[ContainerId(ContainerNames.VtexAuthentication)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class VtexAuthentication : Entity, IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("credential")]
    public VtexCredential Credential { get; set; }

    [JsonProperty("is_deleted")]
    public bool IsDeleted { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public VtexAuthentication(
        string id,
        string sleekflowCompanyId,
        string title,
        VtexCredential credential,
        bool isDeleted,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, sysTypeName: "Authentication")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Title = title;
        Credential = credential;
        IsDeleted = isDeleted;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}