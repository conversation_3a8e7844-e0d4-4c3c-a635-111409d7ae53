using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.Outlook.Authentications;

public class OutlookAuthenticationMetadata : EmailAuthenticationMetadata
{
    [JsonProperty("nonce")]
    public string Nonce { get; set; }

    [JsonProperty("token_type")]
    public string? TokenType { get; set; }

    [JsonProperty("access_token")]
    public string? AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string? RefreshToken { get; set; }

    [JsonProperty("expires_in")]
    public long? ExpiresIn { get; set; }

    [JsonProperty("issued_at")]
    public DateTimeOffset? IssuedAt { get; set; }

    [JsonConstructor]
    public OutlookAuthenticationMetadata(
        string nonce,
        string? tokenType = null,
        string? accessToken = null,
        string? refreshToken = null,
        long? expiresIn = null,
        DateTimeOffset? issuedAt = null)
        : base(ProviderNames.Outlook, ProviderNames.Outlook)
    {
        TokenType = tokenType;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        ExpiresIn = expiresIn;
        IssuedAt = issuedAt;
        Nonce = nonce;
    }
}