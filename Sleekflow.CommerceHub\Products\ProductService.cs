using Sleekflow.CommerceHub.CustomCatalogs.Configs;
using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CommerceHub;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Queries;
using Product = Sleekflow.CommerceHub.Models.Products.Product;

namespace Sleekflow.CommerceHub.Products;

public interface IProductService
{
    Task<Product> GetProductAsync(
        string id,
        string sleekflowCompanyId,
        string storeId);

    Task<Product> CreateOrGetProductAsync(
        string sleekflowCompanyId,
        string storeId,
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        PlatformData platformData,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<Product> CreateAndGetProductAsync(
        Product product,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<Product> RecalculateProductWithVariantProductsAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<Product> PatchAndGetProductAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        bool isViewEnabled,
        Dictionary<string, object?> metadata,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task DeleteProductAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<List<Product>> GetProductsAsync(
        List<string> productIds,
        string sleekflowCompanyId,
        string storeId);

    Task<(List<Product> Products, string? NextContinuationToken)> GetProductsAsync(
        string sleekflowCompanyId,
        string storeId,
        int limit,
        List<QueryBuilder.FilterGroup> filterGroups,
        List<QueryBuilder.Sort> sorts,
        string? continuationToken);

    Task<int> GetCountProductsAsync(string sleekflowCompanyId);

    Task<(long TotalQuota, int ProductCount)>
        GetCustomCatalogProductQuotasAsync(string sleekflowCompanyId);
}

public class ProductService : IProductService, IScopedService
{
    private readonly ILogger<ProductService> _logger;
    private readonly IProductRepository _productRepository;
    private readonly IImageService _imageService;
    private readonly IProductValidator _productValidator;
    private readonly IProductVariantService _productVariantService;
    private readonly IIdService _idService;
    private readonly IDynamicFiltersRepositoryContext _dynamicFiltersRepositoryContext;
    private readonly IProductSearchService _productSearchService;
    private readonly ICustomCatalogConfigService _customCatalogConfigService;

    public ProductService(
        ILogger<ProductService> logger,
        IProductRepository productRepository,
        IImageService imageService,
        IProductValidator productValidator,
        IProductVariantService productVariantService,
        IIdService idService,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext,
        IProductSearchService productSearchService,
        ICustomCatalogConfigService customCatalogConfigService)
    {
        _logger = logger;
        _productRepository = productRepository;
        _imageService = imageService;
        _productValidator = productValidator;
        _productVariantService = productVariantService;
        _idService = idService;
        _dynamicFiltersRepositoryContext = dynamicFiltersRepositoryContext;
        _productSearchService = productSearchService;
        _customCatalogConfigService = customCatalogConfigService;
    }

    public async Task<Product> GetProductAsync(string id, string sleekflowCompanyId, string storeId)
    {
        var product = await _productRepository.GetAsync(
            id,
            sleekflowCompanyId);
        if (product is null
            || product.StoreId != storeId
            || product.SysTypeName != SysTypeNames.Product)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        return product;
    }

    public async Task<Product> CreateOrGetProductAsync(
        string sleekflowCompanyId,
        string storeId,
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        PlatformData platformData,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var limitCount = await _customCatalogConfigService.GetCustomCatalogConfigLimitAsync(
            sleekflowCompanyId,
            SysTypeNames.Product);

        var existedProductCount = await _productRepository.GetCountAsync(sleekflowCompanyId);

        if (existedProductCount + 1 > limitCount)
        {
            throw new SfExceedAvailableCountException(SysTypeNames.Product);
        }

        var products = await _productRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.StoreId == storeId
                && c.Names.Any(n => n.Value == names.First().Value)
                && c.PlatformData.Type == platformData.Type);
        if (products.Any())
        {
            return products.First();
        }

        await _productValidator.AssertValidProductPropertiesAsync(
            sleekflowCompanyId,
            storeId,
            names,
            descriptions,
            categoryIds,
            null);

        var createdProduct = await _productRepository.CreateAndGetAsync(
            new Product(
                _idService.GetId("Product"),
                sleekflowCompanyId,
                storeId,
                categoryIds,
                sku,
                url,
                names,
                descriptions,
                images,
                new List<Multilingual>(),
                new List<Description>(),
                new List<Product.ProductAttribute>(),
                new List<Price>(),
                false,
                new List<string>
                {
                    "Active"
                },
                PlatformData.CustomCatalog(),
                new Dictionary<string, object?>(),
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                createdBy: sleekflowStaff,
                updatedBy: sleekflowStaff),
            sleekflowCompanyId);

        return createdProduct;
    }

    public async Task<Product> CreateAndGetProductAsync(Product product, AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var sleekflowCompanyId = product.SleekflowCompanyId;
        await _productValidator.AssertValidProductPropertiesAsync(
            sleekflowCompanyId,
            product.StoreId,
            product.Names,
            product.Descriptions,
            product.CategoryIds);

        var limitCount = await _customCatalogConfigService.GetCustomCatalogConfigLimitAsync(
            sleekflowCompanyId,
            SysTypeNames.Product);

        var existedProductCount = await _productRepository.GetCountAsync(sleekflowCompanyId);

        if (existedProductCount + 1 > limitCount)
        {
            throw new SfExceedAvailableCountException(SysTypeNames.Product);
        }

        var createdProduct = await _productRepository.CreateAndGetAsync(
            product,
            product.SleekflowCompanyId);

        return createdProduct;
    }

    public async Task<Product> RecalculateProductWithVariantProductsAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var product = await _productRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (product is null || product.StoreId != storeId)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            sleekflowCompanyId,
            storeId,
            id);

        var productVariantNames = productVariants.SelectMany(pv => pv.Names).ToList();
        var productVariantDescriptions = productVariants.SelectMany(pv => pv.Descriptions).ToList();
        var productVariantAttributes = productVariants.SelectMany(pv => pv.Attributes)
            .GroupBy(a => a.Name)
            .Select(
                ag => new Product.ProductAttribute(
                    ag.Key,
                    ag.Select(a => new Product.ProductAttributeValue(a.Value)).ToList()))
            .ToList();
        var productVariantPrices = productVariants.SelectMany(pv => pv.Prices).ToList();

        product.ProductVariantNames = productVariantNames;
        product.ProductVariantDescriptions = productVariantDescriptions;
        product.ProductVariantAttributes = productVariantAttributes;
        product.ProductVariantPrices = productVariantPrices;
        product.UpdatedBy = sleekflowStaff;
        product.UpdatedAt = DateTimeOffset.UtcNow;

        var replacedProduct =
            await _productRepository.ReplaceAndGetAsync(product.Id, product.SleekflowCompanyId, product);

        return replacedProduct;
    }

    public async Task<Product> PatchAndGetProductAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        bool isViewEnabled,
        Dictionary<string, object?> metadata,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        await _productValidator.AssertValidProductPropertiesAsync(
            sleekflowCompanyId,
            storeId,
            names,
            descriptions,
            categoryIds,
            null);

        // TODO check sys type
        var product = await _productRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (product is null || product.StoreId != storeId)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        product.CategoryIds = categoryIds;
        product.Sku = sku;
        product.Url = url;
        product.Names = names;
        product.Descriptions = descriptions;
        product.Images = images;
        product.IsViewEnabled = isViewEnabled;
        product.Metadata = metadata;
        product.UpdatedBy = sleekflowStaff;
        product.UpdatedAt = DateTimeOffset.UtcNow;

        var replacedProduct =
            await _productRepository.ReplaceAndGetAsync(product.Id, product.SleekflowCompanyId, product);

        await _imageService.ClearUnusedImagesAsync(images, product.Images, sleekflowCompanyId, storeId);

        return replacedProduct;
    }

    public async Task DeleteProductAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var product = await GetProductAsync(id, sleekflowCompanyId, storeId);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            sleekflowCompanyId,
            storeId,
            id);
        foreach (var productVariant in productVariants)
        {
            await _productVariantService.DeleteProductVariantAsync(
                productVariant.Id,
                storeId,
                sleekflowCompanyId,
                id,
                sleekflowStaff);
        }

        var deleteAsync = await _productRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the product with Id {id} SleekflowCompanyId {sleekflowCompanyId}");
        }

        // When IsSoftDeleteEnabled,
        // Sleekflow.CommerceHub.Processors.CommerceHubDbProcessorService.ExecuteAsync can pick up the changes
        // When IsSoftDeleteEnabled is false,
        // We need to delete the indexed item manually
        if (!_dynamicFiltersRepositoryContext.IsSoftDeleteEnabled)
        {
            await _productSearchService.DeleteProductsAsync(
                new List<Product>()
                {
                    product
                });
        }
    }

    public async Task<List<Product>> GetProductsAsync(
        List<string> productIds,
        string sleekflowCompanyId,
        string storeId)
    {
        var products = await _productRepository.GetObjectsAsync(
            p =>
                productIds.Contains(p.Id)
                && p.SleekflowCompanyId == sleekflowCompanyId
                && p.StoreId == storeId
                && p.SysTypeName == SysTypeNames.Product);

        return products;
    }

    public async Task<(List<Product> Products, string? NextContinuationToken)> GetProductsAsync(
        string sleekflowCompanyId,
        string storeId,
        int limit,
        List<QueryBuilder.FilterGroup> filterGroups,
        List<QueryBuilder.Sort> sorts,
        string? continuationToken)
    {
        var fgs = new List<QueryBuilder.FilterGroup>(filterGroups)
        {
            new (
                new List<QueryBuilder.IFilter>
                {
                    new QueryBuilder.Filter(
                        IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                        "=",
                        sleekflowCompanyId),
                }),
            new (
                new List<QueryBuilder.IFilter>
                {
                    new QueryBuilder.Filter(
                        Entity.PropertyNameSysTypeName,
                        "=",
                        SysTypeNames.Product)
                }),
            new (
                new List<QueryBuilder.IFilter>
                {
                    new QueryBuilder.Filter(
                        IHasRecordStatuses.PropertyNameRecordStatuses,
                        "array_contains",
                        RecordStatuses.Active)
                }),
            new (
                new List<QueryBuilder.IFilter>
                {
                    new QueryBuilder.Filter(
                        CommonFieldNames.PropertyNameStoreId,
                        "=",
                        storeId)
                })
        };

        var queryDefinition =
            QueryBuilder.BuildQueryDef(
                new List<QueryBuilder.ISelect>(),
                fgs,
                sorts);

        _logger.LogInformation(
            "Executing queryText {QueryText}, queryParameters {QueryParameters}",
            queryDefinition.QueryText,
            queryDefinition.GetQueryParameters());

        var (records, nextContinuationToken) =
            await _productRepository.GetContinuationTokenizedObjectsAsync(
                queryDefinition,
                continuationToken,
                limit);

        return (records, nextContinuationToken);
    }

    public async Task<int> GetCountProductsAsync(string sleekflowCompanyId)
    {
        return await _productRepository.GetCountAsync(sleekflowCompanyId);
    }

    public async Task<(long TotalQuota, int ProductCount)>
        GetCustomCatalogProductQuotasAsync(string sleekflowCompanyId)
    {
        var totalQuota = await _customCatalogConfigService.GetCustomCatalogConfigLimitAsync(
            sleekflowCompanyId,
            SysTypeNames.Product);
        var existedProductCount = await _productRepository.GetCountAsync(sleekflowCompanyId);
        return (totalQuota, existedProductCount);
    }
}