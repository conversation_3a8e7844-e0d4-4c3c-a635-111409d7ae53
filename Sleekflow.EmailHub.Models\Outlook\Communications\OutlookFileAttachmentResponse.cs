using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Outlook.Communications;

public class OutlookFileAttachmentResponse
{
    [JsonProperty("@odata.context")]
    public string ODataContext { get; set; }

    [JsonProperty("value")]
    public List<FileAttachment> Value { get; set; }

    [JsonConstructor]
    public OutlookFileAttachmentResponse(string oDataContext, List<FileAttachment> value)
    {
        ODataContext = oDataContext;
        Value = value;
    }
}