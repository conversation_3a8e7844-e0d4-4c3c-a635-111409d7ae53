﻿using Pulumi.AzureNative.Portal.Inputs;

namespace Sleekflow.Infras.Components.Dashboards;

public static class ProdDashboard
{
    public static List<DashboardPartsArgs> GetDashboardPartsArgs()
    {
        return new List<DashboardPartsArgs>()
        {
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 0, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "NormalizedRUConsumption" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Normalized RU Consumption" } } } } } }, { "title", "Max Normalized RU Consumption for sleekflow90966dde" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "NormalizedRUConsumption" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Normalized RU Consumption" } } } } } }, { "title", "Max Normalized RU Consumption for sleekflow90966dde by DatabaseName where CollectionName = '<empty>'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 0, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginHealthPercentage" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Health Percentage" } } } } } }, { "title", "Avg Origin Health Percentage for sleekflow by Origin" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "Origin" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginHealthPercentage" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Health Percentage" } } } } } }, { "title", "Avg Origin Health Percentage for sleekflow by Origin" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "Origin" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 0, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginLatency" }, { "aggregationType", 3 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Latency" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "OriginLatency" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Origin Latency" } } } } } }, { "title", "Max Origin Latency and Avg Origin Latency for sleekflow" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "TotalLatency" }, { "aggregationType", 4 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Total Latency" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "TotalLatency" }, { "aggregationType", 3 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Total Latency" } } } } } }, { "title", "Avg Total Latency and Max Total Latency for sleekflow" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 0, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "RequestCount" }, { "aggregationType", 1 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Request Count" } } } } } }, { "title", "Sum Request Count for sleekflow" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cdn/profiles/sleekflow" } } }, { "name", "RequestCount" }, { "aggregationType", 1 }, { "namespace", "microsoft.cdn/profiles" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Request Count" } } } } } }, { "title", "Sum Request Count for sleekflow by Http Status" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "HttpStatus" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 31, Y = 0, ColSpan = 16, RowSpan = 8
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-production853b96c8/providers/microsoft.operationalinsights/workspaces/sleekflowa0cb997b" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "51f9c716-bfed-459e-b8f8-c952d68d6c85" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "ContainerAppConsoleLogs_CL\n| project TimeGenerated, Log_s\n| where Log_s startswith \"[GIN]\"\n| where Log_s !has \"__health\"\n| parse Log_s with \"[GIN] \" Log_s_time \" | \" Log_s_status_code \" | \" Log_s_duration \" | \" Log_s_ip_addresss \" | \" Log_s_path\n| extend Log_s_duration_time = extract(@\"([0-9]+\\.[0-9]+)\", 1, Log_s_duration, typeof(real))\n| extend Log_s_duration_unit = extract(@\"([a-z|µ]+)\", 1, Log_s_duration, typeof(string))\n| extend Log_s_duration_ms = case(\n    Log_s_duration_unit == \"ms\", Log_s_duration_time,\n    Log_s_duration_unit == \"s\", Log_s_duration_time * 1000.0,\n    Log_s_duration_unit == \"µs\", Log_s_duration_time / 1000.0,\n    -1.0)\n| summarize Count=count(), \n    Avg_Duration = round(avg(Log_s_duration_ms), 2), \n    P95_Duration = round(percentile(Log_s_duration_ms, 95), 2), \n    Max_Duration =round(max(Log_s_duration_ms), 2) \n    by bin(TimeGenerated, 1h), tostring(Log_s_path), tostring(Log_s_status_code)\n| order by TimeGenerated desc, Max_Duration desc\n\n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "FrameControlChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "value", "StackedColumn" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflowa0cb997b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "value", new Dictionary<string, object>() { { "xAxis", new Dictionary<string, object>() { { "name", "TimeGenerated" }, { "type", "datetime" } } }, { "yAxis", new [] { new Dictionary<string, object>() { { "name", "Count" }, { "type", "long" } } } }, { "splitBy", new [] { new Dictionary<string, object>() { { "name", "Log_s_path" }, { "type", "string" } } } }, { "aggregation", "Sum" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "value", new Dictionary<string, object>() { { "isEnabled", true }, { "position", "Bottom" } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "Query", "ContainerAppConsoleLogs_CL\n| project TimeGenerated, Log_s\n| where Log_s startswith \"[GIN]\"\n| where Log_s !has \"__health\"\n| parse Log_s with \"[GIN] \" Log_s_time \" | \" Log_s_status_code \" | \" Log_s_duration \" | \" Log_s_ip_addresss \" | \" Log_s_path\n| extend Log_s_duration_time = extract(@\"([0-9]+\\.[0-9]+)\", 1, Log_s_duration, typeof(real))\n| extend Log_s_duration_unit = extract(@\"([a-z|µ]+)\", 1, Log_s_duration, typeof(string))\n| extend Log_s_duration_ms = case(\n                                 Log_s_duration_unit == \"ms\",\n                                 Log_s_duration_time,\n                                 Log_s_duration_unit == \"s\",\n                                 Log_s_duration_time * 1000.0,\n                                 Log_s_duration_unit == \"µs\",\n                                 Log_s_duration_time / 1000.0,\n                                 -1.0\n                             )\n| extend Log_s_path2 = split(Log_s_path, \"?\")[0]\n| summarize\n    Count=count(), \n    Avg_Duration = round(avg(Log_s_duration_ms), 2), \n    P95_Duration = round(percentile(Log_s_duration_ms, 95), 2), \n    Max_Duration =round(max(Log_s_duration_ms), 2) \n    by bin(TimeGenerated, 1h), tostring(Log_s_path2), tostring(Log_s_status_code)\n| where Log_s_path2 !startswith \"OPTIONS\"\n| order by TimeGenerated desc, Max_Duration desc\n\n" }, { "Dimensions", new Dictionary<string, object>() { { "xAxis", new Dictionary<string, object>() { { "name", "TimeGenerated" }, { "type", "datetime" } } }, { "yAxis", new [] { new Dictionary<string, object>() { { "name", "Count" }, { "type", "long" } } } }, { "splitBy", new [] { new Dictionary<string, object>() { { "name", "Log_s_path2" }, { "type", "string" } } } }, { "aggregation", "Sum" } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 23, Y = 1, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.EventHub/namespaces/sleekflow-event-hub2556072c" } } }, { "name", "IncomingBytes" }, { "aggregationType", 1 }, { "namespace", "microsoft.eventhub/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Incoming Bytes." }, { "resourceDisplayName", "sleekflow-event-hub2556072c" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.EventHub/namespaces/sleekflow-event-hub2556072c" } } }, { "name", "OutgoingBytes" }, { "aggregationType", 1 }, { "namespace", "microsoft.eventhub/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Outgoing Bytes." }, { "resourceDisplayName", "sleekflow-event-hub2556072c" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.EventHub/namespaces/sleekflow-event-hub2556072c" } } }, { "name", "CapturedBytes" }, { "aggregationType", 1 }, { "namespace", "microsoft.eventhub/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Captured Bytes." }, { "resourceDisplayName", "sleekflow-event-hub2556072c" } } } } } }, { "title", "Throughput" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.EventHub/namespaces/sleekflow-event-hub2556072c" } } }, { "name", "IncomingBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.eventhub/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Incoming Bytes." }, { "resourceDisplayName", "sleekflow-event-hub2556072c" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.EventHub/namespaces/sleekflow-event-hub2556072c" } } }, { "name", "OutgoingBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.eventhub/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Outgoing Bytes." }, { "resourceDisplayName", "sleekflow-event-hub2556072c" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.EventHub/namespaces/sleekflow-event-hub2556072c" } } }, { "name", "ThrottledRequests" }, { "aggregationType", 3 }, { "namespace", "microsoft.eventhub/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests." }, { "resourceDisplayName", "sleekflow-event-hub2556072c" } } } } } }, { "title", "Throughput" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 5, ColSpan = 5, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cache/Redis/sleekflow-fh-redis7e7d3448" } } }, { "name", "serverLoad" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Server Load" } } } } } }, { "title", "Max Server Load for sleekflow-fh-redis7e7d3448" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "region", "eastasia" }, { "resourceType", "microsoft.cache/redis" }, { "subscription", new Dictionary<string, object>() { { "subscriptionId", "c19c9b56-93e9-4d4c-bc81-838bd3f72ad6" }, { "displayName", "Pay-As-You-Go" }, { "uniqueDisplayName", "Pay-As-You-Go" } } } } }, { "name", "serverLoad" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Server Load" }, { "resourceDisplayName", "Pay-As-You-Go" } } } } } }, { "title", "Max Server Load for Pay-As-You-Go in East Asia region  by ResourceId where ResourceId = 'sleekflow-redisa0149ad9', 'sleekflow-fh-redis7e7d3448', 'sleekflow-ueh-redis19557e0f', 'sleekflow-redis-sch22b53d9b'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "Microsoft.ResourceId" } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 5, Y = 5, ColSpan = 4, RowSpan = 6
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cache/Redis/sleekflow-fh-redis7e7d3448" } } }, { "name", "cacheLatency" }, { "aggregationType", 4 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Cache Latency Microseconds (Preview)" } } } } } }, { "title", "Avg Cache Latency Microseconds (Preview) for sleekflow-fh-redis7e7d3448" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "region", "eastasia" }, { "resourceType", "microsoft.cache/redis" }, { "subscription", new Dictionary<string, object>() { { "subscriptionId", "c19c9b56-93e9-4d4c-bc81-838bd3f72ad6" }, { "displayName", "Pay-As-You-Go" }, { "uniqueDisplayName", "Pay-As-You-Go" } } } } }, { "name", "cacheLatency" }, { "aggregationType", 4 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Cache Latency Microseconds (Preview)" }, { "resourceDisplayName", "Pay-As-You-Go" } } } } } }, { "title", "Avg Cache Latency Microseconds (Preview) for Pay-As-You-Go in East Asia region  by ResourceId where ResourceId = 'sleekflow-redisa0149ad9', 'sleekflow-fh-redis7e7d3448', 'sleekflow-ueh-redis19557e0f', 'sleekflow-redis-sch22b53d9b'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "Microsoft.ResourceId" } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 10, Y = 5, ColSpan = 7, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "NamespaceCpuUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "NamespaceMemoryUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Usage" } } } } } }, { "title", "Max CPU and Max Memory Usage for sleekflow-service-bus4aa7ef53" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "NamespaceCpuUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "NamespaceMemoryUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Usage" } } } } } }, { "title", "Max CPU and Max Memory Usage for sleekflow-service-bus4aa7ef53" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 17, Y = 5, ColSpan = 12, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "Messages" }, { "aggregationType", 2 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } } } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "CompleteMessage" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Completed Messages" }, { "resourceDisplayName", "sleekflow-service-bus4aa7ef53" } } } } } }, { "title", "Sum Completed Messages for sleekflow-service-bus4aa7ef53 by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 8, ColSpan = 5, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Cache/Redis/sleekflow-fh-redis7e7d3448" } } }, { "name", "usedmemory" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Used Memory" } } } } } }, { "title", "Max Used Memory for sleekflow-fh-redis7e7d3448" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "region", "eastasia" }, { "resourceType", "microsoft.cache/redis" }, { "subscription", new Dictionary<string, object>() { { "subscriptionId", "c19c9b56-93e9-4d4c-bc81-838bd3f72ad6" }, { "displayName", "Pay-As-You-Go" }, { "uniqueDisplayName", "Pay-As-You-Go" } } } } }, { "name", "usedmemory" }, { "aggregationType", 3 }, { "namespace", "microsoft.cache/redis" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Used Memory" }, { "resourceDisplayName", "Pay-As-You-Go" } } } } } }, { "title", "Max Used Memory for Pay-As-You-Go in East Asia region  by ResourceId where ResourceId = 'sleekflow-redisa0149ad9', 'sleekflow-fh-redis7e7d3448', 'sleekflow-ueh-redis19557e0f', 'sleekflow-redis-sch22b53d9b'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "Microsoft.ResourceId" } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 10, Y = 8, ColSpan = 7, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "Messages" }, { "aggregationType", 2 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } } } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "ThrottledRequests" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests." }, { "resourceDisplayName", "sleekflow-service-bus4aa7ef53" } } } } } }, { "title", "Sum Throttled Requests. for sleekflow-service-bus4aa7ef53 by EntityName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 17, Y = 8, ColSpan = 12, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "Messages" }, { "aggregationType", 2 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } } } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "ActiveMessages" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of active messages in a Queue/Topic." }, { "resourceDisplayName", "sleekflow-service-bus4aa7ef53" } } } } } }, { "title", "Max Count of active messages in a Queue/Topic. for sleekflow-service-bus4aa7ef53 by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 31, Y = 8, ColSpan = 16, RowSpan = 8
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-production853b96c8/providers/microsoft.operationalinsights/workspaces/sleekflowa0cb997b" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "56491918-4183-486a-9b14-45dddc913899" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "ContainerAppConsoleLogs_CL \n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "AnalyticsGrid" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflowa0cb997b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "GridColumnsWidth", new Dictionary<string, object>() { { "Log_s", "1035px" }, { "Log_s_path", "484px" }, { "Log_s_path2", "447px" } } }, { "Query", "ContainerAppConsoleLogs_CL\n| project TimeGenerated, Log_s\n| where Log_s startswith \"[GIN]\"\n| where Log_s !has \"__health\"\n| parse Log_s with \"[GIN] \" Log_s_time \" | \" Log_s_status_code \" | \" Log_s_duration \" | \" Log_s_ip_addresss \" | \" Log_s_path\n| extend Log_s_duration_time = extract(@\"([0-9]+\\.[0-9]+)\", 1, Log_s_duration, typeof(real))\n| extend Log_s_duration_unit = extract(@\"([a-z|µ]+)\", 1, Log_s_duration, typeof(string))\n| extend Log_s_duration_ms = case(\n                                 Log_s_duration_unit == \"ms\",\n                                 Log_s_duration_time,\n                                 Log_s_duration_unit == \"s\",\n                                 Log_s_duration_time * 1000.0,\n                                 Log_s_duration_unit == \"µs\",\n                                 Log_s_duration_time / 1000.0,\n                                 -1.0\n                             )\n| extend Log_s_path2 = split(Log_s_path, \"?\")[0]\n| where Log_s_path2 !contains \"user-event-hub\"\n| summarize\n    Count=count(), \n    Avg_Duration = round(avg(Log_s_duration_ms), 2), \n    P95_Duration = round(percentile(Log_s_duration_ms, 95), 2), \n    Max_Duration =round(max(Log_s_duration_ms), 2) \n    by bin(TimeGenerated, 1h), tostring(Log_s_path2), tostring(Log_s_status_code)\n| order by TimeGenerated desc, Max_Duration desc\n\n" } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 12, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow90966dde by DatabaseName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow90966dde by DatabaseName where CollectionName = '__Empty'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 12, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow90966dde by DatabaseName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "DatabaseName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow90966dde by Multiple Dimensions where CollectionName ≠ '__Empty'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", new [] { "DatabaseName", "CollectionName" } }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 12, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow-global76c9cfa6" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow-global76c9cfa6 by Multiple Dimensions" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", new [] { "DatabaseName", "CollectionName" } }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow-global76c9cfa6" } } }, { "name", "ProvisionedThroughput" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Provisioned Throughput" } } } } } }, { "title", "Max Provisioned Throughput for sleekflow-global76c9cfa6 by Multiple Dimensions" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", new [] { "DatabaseName", "CollectionName" } }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 16, Y = 12, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbookc851223d-75ec-41fe-8ea4-bc138f3c0125\",\"version\":\"MetricsItem/2.0\",\"size\":0,\"chartType\":2,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServerSideLatencyDirect\",\"aggregation\":4,\"splitBy\":\"Region\",\"columnName\":\"Direct Mode - Server Side Latency\"}],\"title\":\"Direct Connection  Mode: Server Side Latency (Avg) By Region {DatabaseText} {ContainerText} \",\"showOpenInMe\":true,\"filters\":[{\"id\":\"1\",\"key\":\"DatabaseName\",\"operator\":0,\"valueParam\":\"Database\"},{\"id\":\"2\",\"key\":\"CollectionName\",\"operator\":0,\"valueParam\":\"Container\"}],\"timeBrushParameterName\":\"TimeRange\",\"timeBrushExportOnlyWhenBrushed\":true,\"gridSettings\":{\"rowLimit\":10000}}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Latency" }, { "formattedValue", "Latency" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 20, Y = 12, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbookc851223d-75ec-41fe-8ea4-bc138f3c0125\",\"version\":\"MetricsItem/2.0\",\"size\":0,\"chartType\":2,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServerSideLatencyGateway\",\"aggregation\":4,\"splitBy\":\"Region\"}],\"title\":\"Gateway Connection Mode: Server Side Latency (Avg) By Region {DatabaseText} {ContainerText} \",\"showOpenInMe\":true,\"filters\":[{\"id\":\"1\",\"key\":\"DatabaseName\",\"operator\":0,\"valueParam\":\"Database\"},{\"id\":\"2\",\"key\":\"CollectionName\",\"operator\":0,\"valueParam\":\"Container\"}],\"timeBrushParameterName\":\"TimeRange\",\"timeBrushExportOnlyWhenBrushed\":true,\"gridSettings\":{\"rowLimit\":10000}}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Latency" }, { "formattedValue", "Latency" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 16, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "DataUsage" }, { "aggregationType", 4 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Data Usage" }, { "color", null } } } } } }, { "title", "Data & Index Usage {DatabaseText} {ContainerText} " }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", new [] { "CollectionName", "DatabaseName" } }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "IndexUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Index Usage" } } } } } }, { "title", "Data Usage" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", new [] { "CollectionName", "DatabaseName" } }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 4, Y = 16, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "DataUsage" }, { "aggregationType", 4 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Data Usage" }, { "color", null } } } } } }, { "title", "Data & Index Usage {DatabaseText} {ContainerText} " }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", new [] { "CollectionName", "DatabaseName" } }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "DataUsage" }, { "aggregationType", 3 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Data Usage" } } } } } }, { "title", "Data Usage" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", new [] { "CollectionName", "DatabaseName" } }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 8, Y = 16, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "StatusCode" }, { "operator", 0 }, { "values", new [] { "429" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "CollectionName" }, { "sort", 2 }, { "top", 10 } } }, { "metrics", new [] { new Dictionary<string, object>() { { "aggregationType", 7 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests (429s)" } } }, { "name", "TotalRequests" }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } } } } }, { "timespan", new Dictionary<string, object>() { { "grain", 1 }, { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false } } }, { "title", "Throttled Requests (429s) sleekflow90966dde" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "name", "TotalRequests" }, { "aggregationType", 7 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests (429s)" } } } } } }, { "title", "Throttled Requests (429s) sleekflow90966dde" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "StatusCode" }, { "operator", 0 }, { "values", new [] { "429" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "CollectionName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 16, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "StatusCode" }, { "operator", 0 }, { "values", new [] { "429" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "CollectionName" }, { "sort", 2 }, { "top", 10 } } }, { "metrics", new [] { new Dictionary<string, object>() { { "aggregationType", 7 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Throttled Requests (429s)" } } }, { "name", "TotalRequests" }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } } } } }, { "timespan", new Dictionary<string, object>() { { "grain", 1 }, { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false } } }, { "title", "Throttled Requests (429s) sleekflow-global76c9cfa6" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow-global76c9cfa6" } } }, { "name", "TotalRequests" }, { "aggregationType", 7 }, { "namespace", "microsoft.documentdb/databaseaccounts" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Total Requests" }, { "resourceDisplayName", "sleekflow-global76c9cfa6" } } } } } }, { "title", "Throttled Requests (429s) sleekflow-global76c9cfa6" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "StatusCode" }, { "operator", 0 }, { "values", new [] { "429" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "CollectionName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 16, Y = 16, ColSpan = 4, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "ComponentId" }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeContext" }, { "value", null }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ResourceIds" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ConfigurationId" }, { "value", "Community-Workbooks/CosmosDb/Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Type" }, { "value", "cosmosdb-insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "GalleryResourceType" }, { "value", "Microsoft.DocumentDb/databaseAccounts" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PinName" }, { "value", "Azure Cosmos DB Resource Insights" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "StepSettings" }, { "value", "{\"chartId\":\"workbookba9902dd-aa8e-4ad3-b87c-548e83e9eb4a\",\"version\":\"MetricsItem/2.0\",\"size\":0,\"chartType\":2,\"resourceType\":\"microsoft.documentdb/databaseaccounts\",\"metricScope\":0,\"resourceParameter\":\"Resources\",\"resourceIds\":[\"{Resources}\"],\"timeContextFromParameter\":\"TimeRange\",\"timeContext\":{\"durationMs\":********},\"metrics\":[{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"aggregation\":4,\"splitBy\":null},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"aggregation\":2},{\"namespace\":\"microsoft.documentdb/databaseaccounts\",\"metric\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"aggregation\":3}],\"title\":\"Service Availability (min/max/avg in %)\",\"showOpenInMe\":true,\"timeBrushParameterName\":\"TimeRange\",\"timeBrushExportOnlyWhenBrushed\":true,\"gridSettings\":{\"formatters\":[{\"columnMatch\":\"Subscription\",\"formatter\":5},{\"columnMatch\":\"Name\",\"formatter\":13,\"formatOptions\":{\"linkTarget\":\"Resource\"}},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability Timeline\",\"formatter\":5},{\"columnMatch\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"formatter\":1,\"numberFormat\":{\"unit\":1,\"options\":null}}],\"rowLimit\":10000,\"labelSettings\":[{\"columnId\":\"Subscription\",\"label\":\"Subscription/Database/Collection\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability\",\"label\":\"Service Availability (Average)\"},{\"columnId\":\"microsoft.documentdb/databaseaccounts-Requests-ServiceAvailability Timeline\",\"label\":\"Service Availability Timeline\"}]}}" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ParameterValues" }, { "value", new Dictionary<string, object>() { { "Resources", new Dictionary<string, object>() { { "type", 5 }, { "value", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Any one" }, { "displayName", "Azure Cosmos DB" }, { "specialValue", "value::1" }, { "formattedValue", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.DocumentDB/databaseAccounts/sleekflow90966dde" } } }, { "EnabledApiTypes", new Dictionary<string, object>() { { "type", 1 }, { "value", "Sql" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "Sql" }, { "displayName", "EnabledApiTypes" }, { "formattedValue", "Sql" } } }, { "ApiDatabases", new Dictionary<string, object>() { { "type", 1 }, { "value", "sqlDatabases" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "sqlDatabases" }, { "displayName", "ApiDatabases" }, { "formattedValue", "sqlDatabases" } } }, { "TimeRange", new Dictionary<string, object>() { { "type", 4 }, { "value", new Dictionary<string, object>() { { "durationMs", ******** } } }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", true }, { "paramUxId", "ext-focus-5d3e01fb-f438-49cf-b4c3-50ed26b957f6" }, { "labelValue", "Last 4 hours" }, { "displayName", "Time Range" }, { "formattedValue", "Last 4 hours" } } }, { "Database", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "APIContainerType", new Dictionary<string, object>() { { "type", 1 }, { "value", "containers" }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "containers" }, { "displayName", "APIContainerType" }, { "formattedValue", "containers" } } }, { "Container", new Dictionary<string, object>() { { "type", 2 }, { "value", null }, { "isPending", false }, { "isWaiting", false }, { "isFailed", true }, { "isGlobal", false }, { "labelValue", "<query failed>" }, { "displayName", "Container" }, { "formattedValue", "" } } }, { "selectedTab", new Dictionary<string, object>() { { "type", 1 }, { "value", "Availability" }, { "formattedValue", "Availability" } } }, { "DatabaseText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", false }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<unset>" }, { "displayName", "Database" }, { "formattedValue", "" } } }, { "ContainerText", new Dictionary<string, object>() { { "type", 1 }, { "isPending", false }, { "isWaiting", true }, { "isFailed", false }, { "isGlobal", false }, { "labelValue", "<query pending>" }, { "displayName", "ContainerText" }, { "formattedValue", "" } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Location" }, { "isOptional", true } } },
                    Type = "Extension/AppInsightsExtension/PartType/PinnedNotebookMetricsPart"
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 31, Y = 16, ColSpan = 16, RowSpan = 6
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-production853b96c8/providers/microsoft.operationalinsights/workspaces/sleekflowa0cb997b" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "56491918-4183-486a-9b14-45dddc913899" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "ContainerAppConsoleLogs_CL \n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "AnalyticsGrid" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflowa0cb997b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "GridColumnsWidth", new Dictionary<string, object>() { { "Log_s", "1035px" }, { "Log_s_path", "484px" }, { "Log_s_path2", "447px" } } }, { "Query", "ContainerAppConsoleLogs_CL\n| project TimeGenerated, Log_s\n| where Log_s startswith \"[GIN]\"\n| where Log_s !has \"__health\"\n| parse Log_s with \"[GIN] \" Log_s_time \" | \" Log_s_status_code \" | \" Log_s_duration \" | \" Log_s_ip_addresss \" | \" Log_s_path\n| extend Log_s_duration_time = extract(@\"([0-9]+\\.[0-9]+)\", 1, Log_s_duration, typeof(real))\n| extend Log_s_duration_unit = extract(@\"([a-z|µ]+)\", 1, Log_s_duration, typeof(string))\n| extend Log_s_duration_ms = case(\n                                 Log_s_duration_unit == \"ms\",\n                                 Log_s_duration_time,\n                                 Log_s_duration_unit == \"s\",\n                                 Log_s_duration_time * 1000.0,\n                                 Log_s_duration_unit == \"µs\",\n                                 Log_s_duration_time / 1000.0,\n                                 -1.0\n                             )\n| extend Log_s_path2 = split(Log_s_path, \"?\")[0]\n| where Log_s_path2 contains \"user-event-hub\"\n| summarize\n    Count=count(), \n    Avg_Duration = round(avg(Log_s_duration_ms), 2), \n    P95_Duration = round(percentile(Log_s_duration_ms, 95), 2), \n    Max_Duration =round(max(Log_s_duration_ms), 2) \n    by bin(TimeGenerated, 1h), tostring(Log_s_path2), tostring(Log_s_status_code)\n| order by TimeGenerated desc, Max_Duration desc\n\n" } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 22, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "aggregationType", 3 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" } } }, { "name", "Replicas" }, { "namespace", "microsoft.app/containerapps" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } } } } }, { "timespan", new Dictionary<string, object>() { { "grain", 1 }, { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false } } }, { "title", "Max Replica Count for sleekflow-mh-app42a8cda9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-eus" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-th-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-seas" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-th-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-uaen" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-th-app-uaen" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-weu" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-th-app-weu" } } } } } }, { "title", "Max Replica Count for sleekflow-th-app, sleekflow-th-app-eus, and 3 other resources" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 22, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max Replica Count for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-ah-app" } } } } } }, { "title", "Max Replica Count for sleekflow-apigw-app, sleekflow-wh-app, and sleekflow-ah-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 22, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app42a8cda9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-fh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-sh-app" } } } } } }, { "title", "Max Replica Count for sleekflow-fh-app and sleekflow-sh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 31, Y = 22, ColSpan = 16, RowSpan = 8
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "resourceTypeMode" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ComponentId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Scope" }, { "value", new Dictionary<string, object>() { { "resourceIds", new [] { "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourcegroups/sleekflow-resource-group-production853b96c8/providers/microsoft.operationalinsights/workspaces/sleekflowa0cb997b" } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartId" }, { "value", "56491918-4183-486a-9b14-45dddc913899" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Version" }, { "value", "2.0" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "TimeRange" }, { "value", "P1D" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DashboardId" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "DraftRequestParameters" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Query" }, { "value", "ContainerAppConsoleLogs_CL \n" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "ControlType" }, { "value", "AnalyticsGrid" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "SpecificChart" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartTitle" }, { "value", "Analytics" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "PartSubTitle" }, { "value", "sleekflowa0cb997b" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "Dimensions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "LegendOptions" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "IsQueryContainTimeRange" }, { "value", false }, { "isOptional", true } } },
                    Type = "Extension/Microsoft_OperationsManagementSuite_Workspace/PartType/LogsDashboardPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "GridColumnsWidth", new Dictionary<string, object>() { { "Log_s", "1035px" } } }, { "Query", "ContainerAppConsoleLogs_CL \n| where Log_s !has \"| 200 |\"\n| project TimeGenerated, RevisionName_s, ContainerAppName_s, ContainerName_s, Log_s, Stream_s, logtag_s, ContainerId_s, ContainerGroupName_s, ContainerGroupId_g, ContainerImage_s, EnvironmentName_s, Type\n| order by TimeGenerated desc\n" } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 19, Y = 23, ColSpan = 9, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "Messages" }, { "aggregationType", 2 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } } } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "CompleteMessage" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Completed Messages" }, { "resourceDisplayName", "sleekflow-service-bus4aa7ef53" } } } } } }, { "title", "Sum Completed Messages for sleekflow-service-bus4aa7ef53 by EntityName where EntityName = 'on-step-requested-event', 'on-trigger-event-requested-event'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 25, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-th-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-th-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-th-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 25, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-appc8257332" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-appc8257332" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-appc8257332" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-appc8257332" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-appc8257332" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-appc8257332" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-appc8257332" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-appc8257332" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-apigw-appc8257332" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-apigw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-apigw-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-apigw-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 25, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-fh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-fh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-fh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-fh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-fh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-fh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 19, Y = 27, ColSpan = 9, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } }, { "name", "Messages" }, { "aggregationType", 2 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } } } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-service-bus4aa7ef53" } } }, { "name", "ActiveMessages" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of active messages in a Queue/Topic." }, { "resourceDisplayName", "sleekflow-service-bus4aa7ef53" } } } } } }, { "title", "Max Count of active messages in a Queue/Topic. for sleekflow-service-bus4aa7ef53 by EntityName where EntityName = 'on-step-requested-event', 'on-trigger-event-requested-event'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 28, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-eus" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-th-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-eus" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-th-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-eus" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-th-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-th-app-eus" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-th-app-eus" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-th-app-eus" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 28, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 28, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appbceb558b" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-fh-worker-app1d9bf1e2" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" }, { "resourceDisplayName", "sleekflow-fh-worker-app1d9bf1e2" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-fh-worker-app1d9bf1e2" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" }, { "resourceDisplayName", "sleekflow-fh-worker-app1d9bf1e2" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-fh-worker-app1d9bf1e2" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 31, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ah-appac558a59" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ah-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 31, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-sh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 35, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app42a8cda9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-ih-app" } } } } } }, { "title", "Max Replica Count for sleekflow-commh-app, sleekflow-eh-app, and sleekflow-ih-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 35, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app42a8cda9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app and sleekflow-pagw-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 35, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app147ffd8e" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-crm-hub-app147ffd8e" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } } } }, { "title", "Max Replica Count for sleekflow-crm-hub-app147ffd8e, sleekflow-hs-in-app, and sleekflow-sf-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-d365-in-app" } } } } } }, { "title", "Max Replica Count for sleekflow-crm-hub-app, sleekflow-hs-in-app, and 2 other resources" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 35, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" } } } } } }, { "title", "Max Replica Count for sleekflow-mh-app42a8cda9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-igw-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-igw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ueh-app" } } }, { "name", "Replicas" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Replica Count" }, { "resourceDisplayName", "sleekflow-ueh-app" } } } } } }, { "title", "Max Replica Count for sleekflow-igw-app and sleekflow-ueh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 24, Y = 35, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ah-appac558a59" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sch-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sch-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sch-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sch-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sch-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sch-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sch-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sch-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-sch-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 38, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ch-app8e203121" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-commh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-commh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-commh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-commh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 38, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-mh-app42a8cda9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-mh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 38, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app147ffd8e" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-app147ffd8e" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app147ffd8e" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-app147ffd8e" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app147ffd8e" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-app147ffd8e" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app147ffd8e" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-app147ffd8e" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-crm-hub-app147ffd8e" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-crm-hub-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-crm-hub-app" } } } } } }, { "title", "Avg CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-crm-hub-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 38, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-igw-app" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" } } } } } }, { "title", "Sum Requests for sleekflow-igw-app by Status Code" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "statusCode" }, { "sort", 2 }, { "top", 10 } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-igw-app" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" } } } } } }, { "title", "Sum Requests for sleekflow-igw-app by Status Code" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "statusCode" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 41, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appbceb558b" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-commh-worker-app0afc8605" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" }, { "resourceDisplayName", "sleekflow-commh-worker-app0afc8605" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-commh-worker-app0afc8605" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" }, { "resourceDisplayName", "sleekflow-commh-worker-app0afc8605" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-commh-worker-app0afc8605" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 41, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appbceb558b" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-mh-worker-appbceb558b" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-mh-worker-appbceb558b" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 41, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb15cf5a6" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 4 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb15cf5a6" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Avg Memory working set and Sum Function Execution Units for sleekflow-ch-worker-appb15cf5a6" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb15cf5a6" } } }, { "name", "MemoryWorkingSet" }, { "aggregationType", 3 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory working set" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.Web/sites/sleekflow-ch-worker-appb15cf5a6" } } }, { "name", "FunctionExecutionUnits" }, { "aggregationType", 1 }, { "namespace", "microsoft.web/sites" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Function Execution Units" } } } } } }, { "title", "Max Memory working set and Sum Function Execution Units for sleekflow-ch-worker-appb15cf5a6" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 41, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ah-appac558a59" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ah-appac558a59" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ah-appac558a59" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-igw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-igw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-igw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-igw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-igw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-igw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-igw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-igw-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-igw-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false }, { "hideHoverCard", false }, { "hideLabelNames", true } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 44, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-eh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-eh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-eh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-eh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 44, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-mh-app42a8cda9" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-mh-app42a8cda9" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-mh-app42a8cda9" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-pagw-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-pagw-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-pagw-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 44, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-sf-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-sf-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-sf-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-sf-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 44, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ch-app8e203121" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ch-app8e203121" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ch-app8e203121" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ueh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ueh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ueh-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ueh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ueh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ueh-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ueh-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ueh-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ueh-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 47, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-wh-app9f437fc0" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-wh-app9f437fc0" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-wh-app9f437fc0" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ih-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-ih-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ih-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-ih-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-ih-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-ih-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 47, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } } } }, { "title", "Avg CPU Usage, Max CPU Usage, and 2 other metrics for sleekflow-d365-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-d365-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" } } } } } }, { "title", "Avg CPU Usage, Max CPU Usage, and 2 other metrics for sleekflow-d365-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 18, Y = 47, ColSpan = 6, RowSpan = 3
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-hs-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } } } }, { "timespan", new Dictionary<string, object>() { { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false }, { "grain", 1 } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "UsageNanoCores" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-hs-in-app" } } }, { "name", "WorkingSetBytes" }, { "aggregationType", 4 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Working Set Bytes" }, { "resourceDisplayName", "sleekflow-hs-in-app" } } } } } }, { "title", "Max CPU Usage, Avg CPU Usage, and 2 other metrics for sleekflow-hs-in-app" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 3 }, { "legendVisualization", new Dictionary<string, object>() { { "isVisible", true }, { "position", 2 }, { "hideSubtitle", false } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 2 } } }, { "y", new Dictionary<string, object>() { { "isVisible", true }, { "axisType", 1 } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 0, Y = 51, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-seas" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-seas" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-eus" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-eus" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-uaen" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-uaen" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-uaen" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-uaen" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-weu" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-weu" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-weu" } } }, { "name", "CpuPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "CPU Usage Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-weu" } } } } } }, { "title", "Max CPU Usage Percentage for OPA/OPAL" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 6, Y = 51, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-seas" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-seas" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-eus" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-eus" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-uaen" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-uaen" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-uaen" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-uaen" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-weu" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opa-app-weu" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-weu" } } }, { "name", "MemoryPercentage" }, { "aggregationType", 3 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Memory Percentage (Preview)" }, { "resourceDisplayName", "sleekflow-opal-app-weu" } } } } } }, { "title", "Max Memory Percentage for OPA/OPAL" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 12, Y = 51, ColSpan = 6, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opa-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opal-app" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-seas" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opa-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-seas" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opal-app-seas" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-eus" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opa-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-eus" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opal-app-eus" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-uaen" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opal-app-uaen" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-uaen" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opa-app-uaen" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opa-app-weu" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opa-app-weu" } } } }, new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.App/containerApps/sleekflow-opal-app-weu" } } }, { "name", "Requests" }, { "aggregationType", 1 }, { "namespace", "microsoft.app/containerapps" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Requests" }, { "resourceDisplayName", "sleekflow-opal-app-weu" } } } } } }, { "title", "Request counts for OPA/OPAL" }, { "titleKind", 2 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } } } } } } } } }
                    },
                }
            },
            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 19, Y = 23, ColSpan = 9, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "metrics", new [] { new Dictionary<string, object>() { { "aggregationType", 2 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } }, { "name", "Messages" }, { "namespace", "microsoft.servicebus/namespaces" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } } } } }, { "timespan", new Dictionary<string, object>() { { "grain", 1 }, { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-high-traffic-service-busc6687f1a" } } }, { "name", "CompleteMessage" }, { "aggregationType", 1 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Completed Messages" }, { "resourceDisplayName", "sleekflow-high-traffic-service-busc6687f1a" } } } } } }, { "title", "Sum Completed Messages for sleekflow-high-traffic-service-busc6687f1a by EntityName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            },

            new DashboardPartsArgs
            {
                Position = new DashboardPartsPositionArgs
                {
                    X = 19, Y = 27, ColSpan = 9, RowSpan = 4
                },
                Metadata = new DashboardPartMetadataArgs
                {
                    Inputs =
                    new [] { new Dictionary<string, object>() { { "name", "options" }, { "value", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "filterCollection", new Dictionary<string, object>() { { "filters", new [] { new Dictionary<string, object>() { { "key", "EntityName" }, { "operator", 1 }, { "values", new [] { "sleekflow.auditlogs/onauditlogrequestedevent", "sleekflow.crmhub.models.events/onobjectoperationevent", "sleekflow.crmhub.models.events/onobjectpersistedevent", "sleekflow.crmhub.models.events/onproviderinitializedevent", "sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent", "sleekflow.webhooks.events/onwebhookrequestedevent" } } } } } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } }, { "metrics", new [] { new Dictionary<string, object>() { { "aggregationType", 2 }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of messages in a Queue/Topic." } } }, { "name", "Messages" }, { "namespace", "microsoft.servicebus/namespaces" }, { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow05d81b8f" } } } } } }, { "timespan", new Dictionary<string, object>() { { "grain", 1 }, { "relative", new Dictionary<string, object>() { { "duration", ******** } } }, { "showUTCTime", false } } }, { "title", "Min Count of messages in a Queue/Topic. for sleekflow05d81b8f by EntityName where EntityName ≠ 'sleekflow.auditlogs/onauditlogrequestedevent', 'sleekflow.crmhub.models.events/onobjectoperationevent', 'sleekflow.crmhub.models.events/onobjectpersistedevent', 'sleekflow.crmhub.models.events/onproviderinitializedevent', 'sleekflow.crmhub.models.events/onprovidertypesyncinitializedevent', 'sleekflow.webhooks.events/onwebhookrequestedevent'" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } } } } } } } }, { "isOptional", true } }, new Dictionary<string, object>() { { "name", "sharedTimeRange" }, { "isOptional", true } } },
                    Type = "Extension/HubsExtension/PartType/MonitorChartPart",
                    Settings =
                    {
                    new Dictionary<string, object>() { { "content", new Dictionary<string, object>() { { "options", new Dictionary<string, object>() { { "chart", new Dictionary<string, object>() { { "metrics", new [] { new Dictionary<string, object>() { { "resourceMetadata", new Dictionary<string, object>() { { "id", "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/resourceGroups/sleekflow-resource-group-production853b96c8/providers/Microsoft.ServiceBus/namespaces/sleekflow-high-traffic-service-busc6687f1a" } } }, { "name", "ActiveMessages" }, { "aggregationType", 3 }, { "namespace", "microsoft.servicebus/namespaces" }, { "metricVisualization", new Dictionary<string, object>() { { "displayName", "Count of active messages in a Queue/Topic." }, { "resourceDisplayName", "sleekflow-high-traffic-service-busc6687f1a" } } } } } }, { "title", "Max Count of active messages in a Queue/Topic. for sleekflow-high-traffic-service-busc6687f1a by EntityName" }, { "titleKind", 1 }, { "visualization", new Dictionary<string, object>() { { "chartType", 2 }, { "legendVisualization", new Dictionary<string, object>() { { "hideHoverCard", false }, { "hideLabelNames", true }, { "hideSubtitle", false }, { "isVisible", true }, { "position", 2 } } }, { "axisVisualization", new Dictionary<string, object>() { { "x", new Dictionary<string, object>() { { "axisType", 2 }, { "isVisible", true } } }, { "y", new Dictionary<string, object>() { { "axisType", 1 }, { "isVisible", true } } } } }, { "disablePinning", true } } }, { "grouping", new Dictionary<string, object>() { { "dimension", "EntityName" }, { "sort", 2 }, { "top", 10 } } } } } } } } } }
                    },
                }
            }
        };
    }
}