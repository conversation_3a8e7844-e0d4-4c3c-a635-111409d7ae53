﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Mvc.Requests;

[<PERSON>solver(typeof(IDbContainerResolver))]
[DatabaseId("db")]
[ContainerId("request")]
public class Request : Entity
{
    [JsonProperty("aspnetcore_trace_id")]
    public string AspnetcoreTraceId { get; set; }

    [JsonProperty("machine_name")]
    public string MachineName { get; set; }

    [JsonProperty("start_time")]
    public DateTimeOffset StartTime { get; set; }

    [JsonProperty("path")]
    public string Path { get; set; }

    [JsonProperty("app_name")]
    public string AppName { get; }

    [JsonProperty("elapsed_milliseconds")]
    public long? ElapsedMilliseconds { get; set; }

    [JsonProperty("input")]
    public object? Input { get; set; }

    [JsonProperty("output")]
    public object? Output { get; set; }

    [JsonProperty("exception_stack_trace")]
    public string? ExceptionStackTrace { get; set; }

    [JsonProperty("success")]
    public bool? Success { get; set; }

    [JsonConstructor]
    public Request(
        string id,
        string aspnetcoreTraceId,
        string machineName,
        DateTimeOffset startTime,
        string path,
        string appName)
        : base(id, "Request")
    {
        AspnetcoreTraceId = aspnetcoreTraceId;
        MachineName = machineName;
        StartTime = startTime;
        Path = path;
        AppName = appName;
    }
}