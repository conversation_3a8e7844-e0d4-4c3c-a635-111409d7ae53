using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Dynamics365.Authentications;

namespace Sleekflow.Integrator.Dynamics365.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitProvider : ITrigger
{
    private readonly IDynamics365AuthenticationService _dynamics365AuthenticationService;

    public InitProvider(
        IDynamics365AuthenticationService dynamics365AuthenticationService)
    {
        _dynamics365AuthenticationService = dynamics365AuthenticationService;
    }

    public class InitProviderInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("return_to_url")]
        [Required]
        public string ReturnToUrl { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public InitProviderInput(
            string sleekflowCompanyId,
            string returnToUrl,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ReturnToUrl = returnToUrl;
            AdditionalDetails = additionalDetails;
        }
    }

    public class InitProviderOutput
    {
        [JsonProperty("dynamics365_authentication_url")]
        public string Dynamics365AuthenticationUrl { get; set; }

        [JsonConstructor]
        public InitProviderOutput(string dynamics365AuthenticationUrl)
        {
            Dynamics365AuthenticationUrl = dynamics365AuthenticationUrl;
        }
    }

    public async Task<InitProviderOutput> F(
        InitProviderInput initProviderInput)
    {
        var redirectUrl =
            await _dynamics365AuthenticationService.AuthenticateAsync(
                initProviderInput.SleekflowCompanyId,
                initProviderInput.ReturnToUrl,
                initProviderInput.AdditionalDetails);

        return new InitProviderOutput(redirectUrl);
    }
}