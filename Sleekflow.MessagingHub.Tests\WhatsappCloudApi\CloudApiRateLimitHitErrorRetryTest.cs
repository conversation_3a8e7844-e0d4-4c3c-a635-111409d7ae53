using System.Diagnostics;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models;
using Newtonsoft.Json;
using Polly;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi;

public class CloudApiRateLimitHitErrorRetryTest
{
    private GraphApiClientException _graphApiClientException;

    [SetUp]
    public void Setup()
    {
        var graphApiError = JsonConvert.DeserializeObject<GraphApiErrorResponse>(
            "{\"error\":{\"message\":\"(#130429) Rate limit hit\",\"type\":\"OAuthException\",\"code\":130429,\"error_data\":{\"messaging_product\":\"whatsapp\",\"details\":\"Rate limit hit\"},\"fbtrace_id\":\"A74SD2lN8xK2g-LgX3h9zya\"}}");

        if (graphApiError != null)
        {
            _graphApiClientException = new GraphApiClientException(
                graphApiError,
                HttpMethod.Post,
                "https://graph.facebook.com/v19.0/119997821092725/messages",
                400,
                "{\"to\":\"85262355859\",\"type\":\"template\",\"template\":{\"name\":\"dr_tintb1b2_2404_v1_2apr2024\",\"language\":{\"code\":\"zh_HK\",\"policy\":\"deterministic\"},\"components\":[{\"type\":\"header\",\"parameters\":[{\"type\":\"image\",\"image\":{\"link\":\"https://api.sleekflow.io/extendedmessagepayload/file/1cc3ddb0-7642-4720-9289-090503f1624c/private/image/20240329_RB_TINT2404_B2B1.jpg?hashcode=bde7f31c68772dee7eca24cba7cada1f93035c8cbcc59ff2a1ce0bbb2778e776&mode=redirect\"}}]},{\"type\":\"body\",\"parameters\":[{\"type\":\"text\",\"text\":\"2024年7月31日\"},{\"type\":\"text\",\"text\":\"DRTINT2407B1\"},{\"type\":\"text\",\"text\":\"CRMCALL\"}]}]},\"messaging_product\":\"whatsapp\"}",
                "{\"error\":{\"message\":\"(#130429) Rate limit hit\",\"type\":\"OAuthException\",\"code\":130429,\"error_data\":{\"messaging_product\":\"whatsapp\",\"details\":\"Rate limit hit\"},\"fbtrace_id\":\"A74SD2lN8xK2g-LgX3h9zya\"}}");
        }
    }

    [Test]
    public async Task PollyRetryTestFor130429ErrorCode()
    {
        var retryCount = 0;
        var watch = Stopwatch.StartNew();

        var retryPolicy = Policy.Handle<GraphApiClientException>(
                graphApiClientException =>
                    graphApiClientException.ErrorApiResponse is { Error.Code: 130429 })
            .WaitAndRetryAsync(
                3,
                _ => TimeSpan.FromSeconds(5),
                onRetry: (exception, _, _) =>
                {
                    if (exception is GraphApiClientException graphApiClientException)
                    {
                        retryCount++;
                    }
                });


        try
        {
            await retryPolicy.ExecuteAsync(() => throw _graphApiClientException);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Caught exception {ex.Message}: with retry attempt {retryCount}");
        }

        watch.Stop();

        Assert.Multiple(() =>
        {
            Assert.That(retryCount, Is.EqualTo(3));
            Assert.That(watch.ElapsedMilliseconds, Is.GreaterThanOrEqualTo(15_000));
        });
    }

    [Test]
    public async Task PollyRetryTestForOtherException()
    {
        var retryCount = 0;
        var watch = Stopwatch.StartNew();

        var retryPolicy = Policy.Handle<GraphApiClientException>(
                graphApiClientException =>
                    graphApiClientException.ErrorApiResponse is { Error.Code: 130429 })
            .WaitAndRetryAsync(
                3,
                _ => TimeSpan.FromSeconds(5),
                onRetry: (exception, _, _) =>
                {
                    if (exception is GraphApiClientException graphApiClientException)
                    {
                        retryCount++;
                    }
                });


        try
        {
            await retryPolicy.ExecuteAsync(() => throw new Exception("other exception message"));
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Caught exception {ex.Message}: with retry attempt {retryCount}");
        }

        watch.Stop();

        Assert.Multiple(() =>
        {
            Assert.That(retryCount, Is.EqualTo(0));
            Assert.That(watch.ElapsedMilliseconds, Is.LessThanOrEqualTo(1_000));
        });
    }
}