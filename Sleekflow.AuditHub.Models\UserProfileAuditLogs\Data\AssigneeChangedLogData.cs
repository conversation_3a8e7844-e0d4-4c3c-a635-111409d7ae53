using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class AssigneeChangedLogData
{
    [JsonProperty("original_assignee")]
    public AssigneeLogData? OriginalAssignee { get; set; }

    [JsonProperty("new_assignee")]
    public AssigneeLogData? NewAssignee { get; set; }

    [JsonConstructor]
    public AssigneeChangedLogData(AssigneeLogData? originalAssignee, AssigneeLogData? newAssignee)
    {
        OriginalAssignee = originalAssignee;
        NewAssignee = newAssignee;
    }
}

public class AssigneeLogData
{
    [JsonConstructor]
    public AssigneeLogData(string assigneeId, string name)
    {
        AssigneeId = assigneeId;
        Name = name;
    }

    [JsonProperty("assignee_id")]
    public string AssigneeId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }
}