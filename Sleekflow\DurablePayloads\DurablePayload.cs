﻿using Newtonsoft.Json;

namespace Sleekflow.DurablePayloads;

public class DurablePayload
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("statusQueryGetUri")]
    public string StatusQueryGetUri { get; set; }

    [JsonProperty("sendEventPostUri")]
    public string SendEventPostUri { get; set; }

    [JsonProperty("terminatePostUri")]
    public string TerminatePostUri { get; set; }

    [JsonProperty("rewindPostUri")]
    public string RewindPostUri { get; set; }

    [JsonProperty("purgeHistoryDeleteUri")]
    public string PurgeHistoryDeleteUri { get; set; }

    [JsonProperty("restartPostUri")]
    public string RestartPostUri { get; set; }

    [JsonConstructor]
    public DurablePayload(
        string id,
        string statusQueryGetUri,
        string sendEventPostUri,
        string terminatePostUri,
        string rewindPostUri,
        string purgeHistoryDeleteUri,
        string restartPostUri)
    {
        Id = id;
        StatusQueryGetUri = statusQueryGetUri;
        SendEventPostUri = sendEventPostUri;
        TerminatePostUri = terminatePostUri;
        RewindPostUri = rewindPostUri;
        PurgeHistoryDeleteUri = purgeHistoryDeleteUri;
        RestartPostUri = restartPostUri;
    }
}