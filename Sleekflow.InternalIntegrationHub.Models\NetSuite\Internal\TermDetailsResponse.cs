using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

public class TermDetailsResponse
{
    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string Id { get; set; }

    [JsonProperty("daysUntilNetDue", NullValueHandling = NullValueHandling.Ignore)]
    public long DaysUntilNetDue { get; set; }

    [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
    public string Name { get; set; }

    [JsonConstructor]
    public TermDetailsResponse(
        string id,
        long daysUntilNetDue,
        string name)
    {
        Id = id;
        DaysUntilNetDue = daysUntilNetDue;
        Name = name;
    }
}