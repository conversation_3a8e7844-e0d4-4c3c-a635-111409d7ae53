using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class ActionMessageObject : BaseMessageObject
{
    /// <summary>
    /// actual token generated by <PERSON> Backend or MessagingHub
    /// Flow Hub might not need to take care of it
    /// </summary>
    [JsonProperty("flow_token")]
    public string? FlowToken { get; set; }

    /// <summary>
    /// just applicable to WhatsApp Flows with Endpoint Type
    /// </summary>
    [JsonProperty("flow_action_data")]
    public Dictionary<string, object>? FlowActionData { get; set; }

    [JsonConstructor]
    public ActionMessageObject(string? flowToken, Dictionary<string, object>? flowActionData)
    {
        FlowToken = flowToken;
        FlowActionData = flowActionData;
    }
}