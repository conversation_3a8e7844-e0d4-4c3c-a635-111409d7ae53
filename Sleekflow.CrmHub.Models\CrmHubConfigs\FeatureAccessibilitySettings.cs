﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.CrmHubConfigs;

public class FeatureAccessibilitySettings
{
    [JsonProperty("can_access_custom_object")]
    public bool CanAccessCustomObject { get; set; }

    [JsonProperty("can_access_custom_object_flow_builder_components")]
    public bool CanAccessCustomObjectFlowBuilderComponents { get; set; }

    [JsonConstructor]
    public FeatureAccessibilitySettings(bool canAccessCustomObject, bool canAccessCustomObjectFlowBuilderComponents)
    {
        CanAccessCustomObject = canAccessCustomObject;
        CanAccessCustomObjectFlowBuilderComponents = canAccessCustomObjectFlowBuilderComponents;
    }

    public static FeatureAccessibilitySettings Default()
        => new (
            true,
            true);
}