using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Stores;

public interface IStoreRepository : IDynamicFiltersRepository<Store>
{
}

public class StoreRepository : DynamicFiltersBaseRepository<Store>, IStoreRepository, IScopedService
{
    public StoreRepository(
        ILogger<StoreRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}