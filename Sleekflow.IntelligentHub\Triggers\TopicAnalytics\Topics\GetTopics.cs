using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.TopicAnalytics;
using Sleekflow.IntelligentHub.TopicAnalytics;

namespace Sleekflow.IntelligentHub.Triggers.TopicAnalytics.Topics;

[TriggerGroup(ControllerNames.TopicAnalytics)]
public class GetTopics : ITrigger<GetTopics.GetTopicsInput, GetTopics.GetTopicsOutput>
{
    private readonly ITopicAnalyticsService _topicAnalyticsService;

    public GetTopics(ITopicAnalyticsService topicAnalyticsService)
    {
        _topicAnalyticsService = topicAnalyticsService;
    }

    public class GetTopicsInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetTopicsInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetTopicsOutput
    {
        [JsonProperty("topics")]
        public List<TopicAnalyticsTopic> Topics { get; set; }

        [JsonConstructor]
        public GetTopicsOutput(List<TopicAnalyticsTopic> topics)
        {
            Topics = topics;
        }
    }

    public async Task<GetTopicsOutput> F(GetTopicsInput input)
    {
        var result = await _topicAnalyticsService.GetTopicAnalyticsTopics(input.SleekflowCompanyId);
        return new GetTopicsOutput(result);
    }
}