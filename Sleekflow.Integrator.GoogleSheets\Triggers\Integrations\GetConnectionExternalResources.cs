﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.Services;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetConnectionExternalResources : ITrigger
{
    private readonly IGoogleSheetsObjectService _googleSheetsObjectService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;

    public GetConnectionExternalResources(
        IGoogleSheetsObjectService googleSheetsObjectService,
        IGoogleSheetsConnectionService googleSheetsConnectionService,
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService)
    {
        _googleSheetsObjectService = googleSheetsObjectService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
    }

    public class GetConnectionExternalResourcesInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetConnectionExternalResourcesInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class GetConnectionExternalResourcesOutput
    {
        [JsonProperty("external_resources")]
        public List<ExternalResource> ExternalResources { get; set; }

        [JsonConstructor]
        public GetConnectionExternalResourcesOutput(
            List<ExternalResource> externalResources)
        {
            ExternalResources = externalResources;
        }
    }

    public async Task<GetConnectionExternalResourcesOutput> F(
        GetConnectionExternalResourcesInput getConnectionExternalResourcesInput)
    {
        var connection =
            await _googleSheetsConnectionService.GetByIdAsync(
                getConnectionExternalResourcesInput.ConnectionId,
                getConnectionExternalResourcesInput.SleekflowCompanyId);

        var authentication =
            await _googleSheetsAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getConnectionExternalResourcesInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var spreadsheetInfos =
            await _googleSheetsObjectService.GetSpreadsheetInfosAsync(authentication);
        if (spreadsheetInfos is not null)
        {
            var spreadsheetResources = new List<ExternalResource>();
            foreach (var spreadsheetInfo in spreadsheetInfos)
            {
                spreadsheetResources.Add(new ExternalResource(
                    spreadsheetInfo.Id,
                    spreadsheetInfo.Name,
                    "spreadsheet",
                    spreadsheetInfo.Worksheets.Select(
                        s => new ExternalResource(
                            s.Id,
                            s.Name,
                            "worksheet",
                            null)).ToList()));
            }

            return new GetConnectionExternalResourcesOutput(spreadsheetResources);
        }

        return new GetConnectionExternalResourcesOutput(new List<ExternalResource>());
    }
}