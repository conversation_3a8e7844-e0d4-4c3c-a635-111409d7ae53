using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Carts;

public interface ICartRepository : IDynamicFiltersRepository<Cart>
{
}

public class CartRepository : DynamicFiltersBaseRepository<Cart>, ICartRepository, IScopedService
{
    public CartRepository(
        ILogger<CartRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}