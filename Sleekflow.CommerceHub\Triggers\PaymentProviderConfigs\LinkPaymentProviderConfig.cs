using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.PaymentProviderConfigs;

[TriggerGroup(ControllerNames.PaymentProviderConfigs)]
public class LinkPaymentProviderConfig
    : ITrigger<
        LinkPaymentProviderConfig.LinkPaymentProviderConfigInput,
        LinkPaymentProviderConfig.LinkPaymentProviderConfigOutput>
{
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;

    public LinkPaymentProviderConfig(
        IPaymentProviderConfigService paymentProviderConfigService)
    {
        _paymentProviderConfigService = paymentProviderConfigService;
    }

    public class LinkPaymentProviderConfigInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("payment_provider_config_id")]
        public string PaymentProviderConfigId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("currency_iso_codes")]
        public List<string> CurrencyIsoCodes { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public LinkPaymentProviderConfigInput(
            string paymentProviderConfigId,
            string storeId,
            List<string> currencyIsoCodes,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            PaymentProviderConfigId = paymentProviderConfigId;
            StoreId = storeId;
            CurrencyIsoCodes = currencyIsoCodes;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class LinkPaymentProviderConfigOutput
    {
    }

    public async Task<LinkPaymentProviderConfigOutput> F(
        LinkPaymentProviderConfigInput linkPaymentProviderConfigInput)
    {
        var paymentProviderConfig = await _paymentProviderConfigService.GetPaymentProviderConfigAsync(
            linkPaymentProviderConfigInput.PaymentProviderConfigId,
            linkPaymentProviderConfigInput.SleekflowCompanyId);

        var storeIds = paymentProviderConfig.StoreIds.Concat(
                new[]
                {
                    linkPaymentProviderConfigInput.StoreId
                })
            .Distinct()
            .ToList();
        var storeIdToCurrencyIsoCodes = new Dictionary<string, List<string>>(
            paymentProviderConfig.StoreIdToCurrencyIsoCodesDict);
        if (storeIdToCurrencyIsoCodes.ContainsKey(linkPaymentProviderConfigInput.StoreId))
        {
            storeIdToCurrencyIsoCodes[linkPaymentProviderConfigInput.StoreId]
                = storeIdToCurrencyIsoCodes[linkPaymentProviderConfigInput.StoreId]
                    .Concat(linkPaymentProviderConfigInput.CurrencyIsoCodes)
                    .Distinct()
                    .ToList();
        }
        else
        {
            storeIdToCurrencyIsoCodes[linkPaymentProviderConfigInput.StoreId]
                = linkPaymentProviderConfigInput.CurrencyIsoCodes.Distinct().ToList();
        }

        await _paymentProviderConfigService.PatchPaymentProviderConfigAsync(
            linkPaymentProviderConfigInput.PaymentProviderConfigId,
            linkPaymentProviderConfigInput.SleekflowCompanyId,
            storeIds,
            storeIdToCurrencyIsoCodes,
            paymentProviderConfig.SupportedCurrencyIsoCodes);

        return new LinkPaymentProviderConfigOutput();
    }
}