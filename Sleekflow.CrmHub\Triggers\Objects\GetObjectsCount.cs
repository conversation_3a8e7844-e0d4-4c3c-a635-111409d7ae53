﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetObjectsCount : ITrigger
{
    private readonly ILogger<GetObjectsCount> _logger;
    private readonly IEntityRepository _entityRepository;

    public GetObjectsCount(
        ILogger<GetObjectsCount> logger,
        IEntityRepository entityRepository)
    {
        _logger = logger;
        _entityRepository = entityRepository;
    }

    public class GetObjectsCountInputFilterGroup
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<EntityQueryBuilder.Filter> Filters { get; set; }

        [JsonConstructor]
        public GetObjectsCountInputFilterGroup(
            List<EntityQueryBuilder.Filter> filters)
        {
            Filters = filters;
        }
    }

    public class GetObjectsCountInput : IValidatableObject
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetObjectsCountInputFilterGroup> FilterGroups { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            var crmHubDbEntityService = validationContext.GetRequiredService<ICrmHubDbEntityService>();
            if (crmHubDbEntityService.IsNotSupportedEntityTypeName(EntityTypeName))
            {
                results.Add(
                    new ValidationResult(
                        "The EntityTypeName is not supported.",
                        new List<string>
                        {
                            nameof(EntityTypeName)
                        }));
            }

            return results;
        }

        [JsonConstructor]
        public GetObjectsCountInput(
            string sleekflowCompanyId,
            string entityTypeName,
            List<GetObjectsCountInputFilterGroup> filterGroups)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
        }
    }

    public class GetObjectsCountOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetObjectsCountOutput(
            long count)
        {
            Count = count;
        }
    }

    public async Task<GetObjectsCountOutput> F(
        GetObjectsCountInput getObjectsCountInput)
    {
        var filterGroups = getObjectsCountInput
            .FilterGroups
            .Select(fg => new EntityQueryBuilder.FilterGroup(fg.Filters.Cast<EntityQueryBuilder.IFilter>().ToList()))
            .ToList();

        var queryDefinition =
            EntityQueryBuilder.BuildQueryDef(
                new List<EntityQueryBuilder.ISelect>
                {
                    new EntityQueryBuilder.PlainSelect("COUNT(1)", "count")
                },
                getObjectsCountInput.EntityTypeName,
                filterGroups,
                new List<EntityQueryBuilder.Sort>(),
                new List<EntityQueryBuilder.GroupBy>(),
                getObjectsCountInput.SleekflowCompanyId);

        _logger.LogInformation(
            "Executing queryText {QueryText}, queryParameters {QueryParameters}",
            queryDefinition.QueryText,
            queryDefinition.GetQueryParameters());

        var rawRecords =
            await _entityRepository.GetObjectsAsync(
                queryDefinition);
        if (rawRecords.Count == 0 || rawRecords[0].ContainsKey("count") == false)
        {
            throw new SfInternalErrorException("The count response is invalid");
        }

        return new GetObjectsCountOutput((long) rawRecords[0]["count"]!);
    }
}