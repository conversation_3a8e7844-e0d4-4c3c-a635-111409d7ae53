﻿namespace Sleekflow.Exceptions.Salesforce;

public class SfSObjectOperationException : ErrorCodeException
{
    public string ResponseStr { get; }

    public string SObjectTypeName { get; }

    public HttpResponseMessage HttpResponseMessage { get; }

    public SfSObjectOperationException(
        string responseStr,
        string sObjectTypeName,
        HttpResponseMessage httpResponseMessage)
        : base(
            ErrorCodeConstant.SfSObjectOperationException,
            $"The operation request has failed. sObjectTypeName=[{sObjectTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "sObjectTypeName", sObjectTypeName
                }
            })
    {
        ResponseStr = responseStr;
        SObjectTypeName = sObjectTypeName;
        HttpResponseMessage = httpResponseMessage;
    }

    public SfSObjectOperationException(
        Exception exception,
        string responseStr,
        string sObjectTypeName,
        HttpResponseMessage httpResponseMessage)
        : base(
            ErrorCodeConstant.SfSObjectOperationException,
            $"The operation request has failed. sObjectTypeName=[{sObjectTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "sObjectTypeName", sObjectTypeName
                }
            },
            exception)
    {
        ResponseStr = responseStr;
        SObjectTypeName = sObjectTypeName;
        HttpResponseMessage = httpResponseMessage;
    }
}