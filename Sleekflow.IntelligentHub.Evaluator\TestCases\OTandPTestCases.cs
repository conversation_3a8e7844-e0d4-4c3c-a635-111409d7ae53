using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class OTandPTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetOTandPTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/OTandP/",
            SleekflowCompanyId: "5873424d-5f26-4c81-9e16-5b248f7944de");

        var bookingGuideDocuments = new List<string>
        {
            "OT&P - Booking Guide 2024 for Sleekflow.md"
        };

        var fluShotSummaryDocuments = new List<string>
        {
            "OT&P - Flu Shot Summary 2024 for Sleekflow.md"
        };

        var sleekflowKeywordsDocuments = new List<string>
        {
            "Sleekflow keywords for identification of task owner in unassigned.md"
        };

        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about the flu vaccine offered by OT&P",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can you tell me about the flu vaccines available at OT&P?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "OT&P offers several flu vaccines including Fluarix Tetra, Influvac Tetra, and Vaxigrip Tetra. These vaccines are designed to protect against two strains of influenza A (H1N1 and H3N2) and two strains of influenza B. They are suitable for individuals aged 6 months and older, including pregnant women."
                ),
                new ChatMessageContent(AuthorRole.User, "What is the cost of the flu vaccine at OT&P?"),
            ],
            "The cost for the flu vaccine at OT&P is $420 across all locations. It's advisable to book early in the season, ideally by September or October, for optimal protection.",
            SourceFilenames: fluShotSummaryDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about booking a general practice appointment",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "How do I book a general practice appointment at 5/F Century Square?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "To book a general practice appointment at 5/F Century Square, you can call 2521 3181. The clinic is open Monday to Friday from 8:00 AM to 7:00 PM, Saturday from 9:00 AM to 5:00 PM, and Sunday from 9:00 AM to 1:00 PM."),
                new ChatMessageContent(AuthorRole.User, "What are the consultation fees for new patients?"),
            ],
            "For new patients, a 30-minute appointment costs $1,260.",
            SourceFilenames: bookingGuideDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about specialist clinics at C3",
            [
                new ChatMessageContent(AuthorRole.User, "What specialist services are available at the C3 clinic?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The C3 Specialist Clinic offers services in ENT, Gastroenterology, Cardiology, Orthopaedics, Urology, General Surgery, Paediatric Surgery, Cardiothoracic Surgery, Oncology, Ophthalmology, Dermatology, and Plastic Surgery."),
                new ChatMessageContent(AuthorRole.User, "How much does an ENT consultation cost?"),
            ],
            "For ENT consultations, the cost is $1,600 for new patients and $1,800 for some specialists like Terry Hung.",
            SourceFilenames: bookingGuideDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about OBGYN services at LG Family Clinic",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "What OBGYN services are available at the LG Family Clinic?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "The LG Family Clinic offers OBGYN services with doctors like Dr. Zara Chan, Dr. Michael Chor, and Dr. Katherine Cheng."),
                new ChatMessageContent(AuthorRole.User, "How much does an OBGYN consultation cost?"),
            ],
            "An OBGYN consultation costs $1,500 for 30 minutes.",
            SourceFilenames: bookingGuideDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about booking a flu vaccine for child at Clearwater Bay clinic",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Hi OT&P, i wanna book a flu vaccine"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Certainly! We can assist you with booking a flu vaccine. Getting vaccinated reduces the risk of serious illness. You can book your appointment through this link: [https://www.otandp.com/flu-vaccine-booking?hsLang=en]. The available locations are Central General Practice, Family Clinic, Clearwater Bay, and Repulse Bay. Please let us know if you have any questions."),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I wanna make the flu vaccine reservation for my child. Is it ok?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Yes, we do offer flu vaccines for children! Our Paediatric Flu Vaccine & Check-up package is designed to reduce the risk of serious illness and complications from the flu. It's especially important now due to weakened immunity to influenza in Hong Kong. The package includes a medical history assessment, general examination, growth parameters, and a vaccine history consult. You can book the package here: [https://www.otandp.com/childrens-flu-vaccine-and-check-up-package?hsLang=en]. Do you have any other questions?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "I wanna book it in the Clearwater Bay clinic this saturday"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "Certainly! We can assist you with booking a flu vaccine for your child at our Clearwater Bay clinic this Saturday. You may book directly through this link: [https://www.otandp.com/online-appointment-gp?service_category=Paediatrics&hsLang=en]. Our Clearwater Bay clinic is open on Saturday from 9:00 AM to 12:30 PM. For any inquiries, please feel free to contact us at +852 2155 9055 or +852 2719 6366. Does that sound good?"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "想問下可唔可以喺呢度book架"),
            ],
            "當然可以！您可以通過以下連結直接預約：[https://www.otandp.com/online-appointment-gp?service_category=Paediatrics&hsLang=en]。我們的清水灣診所星期六的營業時間為上午9:00至中午12:30。如有任何疑問，請隨時致電+852` 2719 6366與我們聯繫。診所地址是香港新界清水灣道壁屋1號 Razor Hill Dairy Farm Shopping Centre 1樓。",
            SourceFilenames: fluShotSummaryDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "User inquires about booking a flu vaccine for child at Clearwater Bay clinic",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "想問下可唔可以喺呢度book Flu vaccine架"),
            ],
            "當然可以！您可以通過以下連結直接預約：[https://www.otandp.com/online-appointment-gp?service_category=Paediatrics&hsLang=en]。我們的清水灣診所星期六的營業時間為上午9:00至中午12:30。如有任何疑問，請隨時致電+852` 2719 6366與我們聯繫。診所地址是香港新界清水灣道壁屋1號 Razor Hill Dairy Farm Shopping Centre 1樓。",
            SourceFilenames: fluShotSummaryDocuments);

        // Could you help me to add 想問下可唔可以喺呢度book Flu vaccine架 test case in different language
        yield return new ChatEvalQuestion(
            testConfig,
            "User asks to book a flu vaccine (English)",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "Can I book a flu vaccine here?"),
            ],
            "Of course! You can book directly through the following link: [https://www.otandp.com/online-appointment-gp?service_category=Paediatrics&hsLang=en]. Our Clearwater Bay clinic is open on Saturdays from 9:00 AM to 12:30 PM. If you have any questions, please feel free to call us at +852 2719 6366. The clinic address is 1/F, Razor Hill Dairy Farm Shopping Centre, 1 Razor Hill Road, Clearwater Bay, New Territories, Hong Kong.",
            SourceFilenames: fluShotSummaryDocuments);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asks to book a flu vaccine (Simplified Chinese)",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "请问我可以在这里预约流感疫苗吗？"),
            ],
            "当然可以！您可以通过以下链接直接预约：[https://www.otandp.com/online-appointment-gp?service_category=Paediatrics&hsLang=en]。我们的清水湾诊所星期六的营业时间为上午9:00至中午12:30。如有任何疑问，请随时致電+852 2719 6366与我们联系。诊所地址是香港新界清水湾道壁屋1号 Razor Hill Dairy Farm Shopping Centre 1楼。",
            SourceFilenames: fluShotSummaryDocuments);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asks to book a flu vaccine (Japanese)",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "ここでインフルエンザのワクチンを予約できますか？"),
            ],
            "もちろんです！こちらのリンクから直接ご予約いただけます：[https://www.otandp.com/online-appointment-gp?service_category=Paediatrics&hsLang=en]。清水湾クリニックの土曜日の営業時間は午前9時から午後12時30分までです。ご不明な点がございましたら、+852 2719 6366までお気軽にお電話ください。クリニックの住所は香港新界清水湾道壁屋1号 Razor Hill Dairy Farm Shopping Centre 1階です。",
            SourceFilenames: fluShotSummaryDocuments);

        yield return new ChatEvalQuestion(
            testConfig,
            "User asks to book a flu vaccine (Korean)",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "여기서 독감 백신을 예약할 수 있나요?"),
            ],
            "물론입니다! 다음 링크를 통해 직접 예약하실 수 있습니다: [https://www.otandp.com/online-appointment-gp?service_category=Paediatrics&hsLang=en]. 저희 클리어워터 베이 클리닉은 토요일 오전 9시부터 오후 12시 30분까지 운영합니다. 문의사항이 있으시면 언제든지 +852 2719 6366으로 전화주십시오. 클리닉 주소는 홍콩 신계 클리어워터 베이 로드 1호 레이저 힐 데어리 팜 쇼핑센터 1층입니다.",
            SourceFilenames: fluShotSummaryDocuments);

    }
}