using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Salesforce;

[TriggerGroup(TriggerGroups.InflowActions)]
public class SearchSalesforceObject : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public SearchSalesforceObject(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class SearchSalesforceObjectInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("conditions")]
        [ValidateArray]
        [Required]
        public List<SearchObjectCondition> Conditions { get; set; }

        [JsonConstructor]
        public SearchSalesforceObjectInput(
            string sleekflowCompanyId,
            string salesforceConnectionId,
            string entityTypeName,
            List<SearchObjectCondition> conditions)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SalesforceConnectionId = salesforceConnectionId;
            EntityTypeName = entityTypeName;
            Conditions = conditions;
        }
    }

    public class SearchSalesforceObjectOutput
    {
        [JsonProperty("record")]
        public Dictionary<string, object?> Record { get; set; }

        [JsonConstructor]
        public SearchSalesforceObjectOutput(
            Dictionary<string, object?> record)
        {
            Record = record;
        }
    }

    public async Task<SearchSalesforceObjectOutput> F(
        SearchSalesforceObjectInput searchSalesforceObjectsInput)
    {
        var salesforceProviderService = _providerSelector.GetProviderService(
            "salesforce-integrator");

        var output = await salesforceProviderService.SearchObjectsAsync(
            searchSalesforceObjectsInput.SleekflowCompanyId,
            searchSalesforceObjectsInput.SalesforceConnectionId,
            searchSalesforceObjectsInput.EntityTypeName,
            searchSalesforceObjectsInput.Conditions);

        return new SearchSalesforceObjectOutput(output.Objects.Count == 0 ? new Dictionary<string, object?>() : output.Objects[0]);
    }
}