using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.States;

public class StateIdentity : IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("object_id")]
    public string ObjectId { get; set; }

    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [JsonConstructor]
    public StateIdentity(
        string objectId,
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        string objectType)
    {
        ObjectId = objectId;
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        ObjectType = objectType;
    }
}