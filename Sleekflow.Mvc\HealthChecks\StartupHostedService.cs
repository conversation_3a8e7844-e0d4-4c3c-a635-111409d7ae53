using Microsoft.Extensions.Hosting;

namespace Sleekflow.Mvc.HealthChecks;

public class StartupHostedService : IHostedService
{
    private readonly StartupHealthCheck _startupHealthCheck;

    public StartupHostedService(StartupHealthCheck startupHostedServiceHealthCheck)
    {
        _startupHealthCheck = startupHostedServiceHealthCheck;
    }

    public Task StartAsync(CancellationToken cancellationToken)
    {
        _startupHealthCheck.StartupCompleted = true;

        return Task.CompletedTask;
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }
}