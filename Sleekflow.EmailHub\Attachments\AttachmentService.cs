using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Specialized;
using MassTransit;
using MimeKit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.Exceptions;
using Sleekflow.Models.Blobs;

namespace Sleekflow.EmailHub.Attachments;

public interface IAttachmentService
{
    Task ProcessOutboundAttachment(
        EmailAttachment emailAttachment,
        BodyBuilder builder,
        CancellationToken cancellationToken = default);

    Task ProcessInboundAttachment(
        string sleekflowCompanyId,
        string emailId,
        MailboxAddress mailboxAddress,
        MimeEntity mimeEntity,
        List<EmailAttachment> attachments,
        CancellationToken cancellationToken = default);

    Task ProcessInboundAttachment(
        string sleekflowCompanyId,
        string emailId,
        MailboxAddress mailboxAddress,
        string fileName,
        byte[] attachmentBytes,
        List<EmailAttachment> attachments,
        CancellationToken cancellationToken = default);
}

public class AttachmentService : IAttachmentService, IScopedService
{
    private readonly IRequestClient<CreateBlobUploadSasUrlsRequest> _createBlobUploadSasUrlsRequestClient;
    private readonly IRequestClient<CreateBlobDownloadSasUrlsRequest> _createBlobDownloadSasUrlsRequestClient;

    public AttachmentService(
        IRequestClient<CreateBlobUploadSasUrlsRequest> createBlobUploadSasUrlsRequestClient,
        IRequestClient<CreateBlobDownloadSasUrlsRequest> createBlobDownloadSasUrlsRequestClient)
    {
        _createBlobUploadSasUrlsRequestClient = createBlobUploadSasUrlsRequestClient;
        _createBlobDownloadSasUrlsRequestClient = createBlobDownloadSasUrlsRequestClient;

    }

    public async Task<byte[]> ConvertMimeAttachmentToByte(MimeEntity attachment, CancellationToken cancellationToken = default)
    {
        if (!attachment.IsAttachment)
        {
            return Array.Empty<byte>();
        }

        using var attachmentStream = new MemoryStream();

        if (attachment is MessagePart messagePart)
        {
            await messagePart.Message.WriteToAsync(attachmentStream, cancellationToken);
        }
        else
        {
            var part = (MimePart) attachment;

            await part.Content.DecodeToAsync(attachmentStream, cancellationToken);
        }

        return attachmentStream.ToArray();
    }

    public async Task ProcessInboundAttachment(
        string sleekflowCompanyId,
        string emailId,
        MailboxAddress mailboxAddress,
        string fileName,
        byte[] attachmentBytes,
        List<EmailAttachment> attachments,
        CancellationToken cancellationToken = default)
    {
        var uri = await UploadBlobFromStreamAndReturnUriAsync(
            sleekflowCompanyId,
            attachmentBytes,
            BlobTypes.File,
            cancellationToken);

        attachments.Add(
            new EmailAttachment(sleekflowCompanyId, mailboxAddress.Address, emailId, uri.ToString(), fileName));
    }

    public async Task ProcessInboundAttachment(
        string sleekflowCompanyId,
        string emailId,
        MailboxAddress mailboxAddress,
        MimeEntity mimeEntity,
        List<EmailAttachment> attachments,
        CancellationToken cancellationToken = default)
    {
        var attachmentBytes = await ConvertMimeAttachmentToByte(mimeEntity, cancellationToken);

        var fileName = mimeEntity.ContentDisposition?.FileName ?? mimeEntity.ContentType.Name;

        await ProcessInboundAttachment(
            sleekflowCompanyId,
            emailId,
            mailboxAddress,
            fileName,
            attachmentBytes,
            attachments,
            cancellationToken);
    }

    public async Task ProcessOutboundAttachment(
        EmailAttachment emailAttachment,
        BodyBuilder builder,
        CancellationToken cancellationToken = default)
    {
        using var stream = new MemoryStream();

        await new BlobClient(new Uri(emailAttachment.FileUrlToBlob)).DownloadToAsync(
            stream,
            cancellationToken: cancellationToken);
        builder.Attachments.Add(emailAttachment.FileName, stream.ToArray());
    }

    public async Task<Uri> UploadBlobFromStreamAndReturnUriAsync(
        string sleekflowCompanyId,
        byte[] byteArr,
        string blobType,
        CancellationToken cancellationToken = default)
    {
        var uploadSas = (await _createBlobUploadSasUrlsRequestClient.GetResponse<CreateBlobUploadSasUrlsReply>(
            new CreateBlobUploadSasUrlsRequest(
                sleekflowCompanyId,
                1,
                blobType),
            cancellationToken)).Message.UploadBlobs.FirstOrDefault() ?? throw new SfInternalErrorException(
            $"Create ShareHub Blob link failed, sleekflowCompanyId: {sleekflowCompanyId}, blobType: {blobType}");

        using var stream = new MemoryStream(byteArr);

        await new BlockBlobClient(new Uri(uploadSas.Url)).UploadAsync(stream, cancellationToken: cancellationToken);

        var downloadSas = (await _createBlobDownloadSasUrlsRequestClient.GetResponse<CreateBlobDownloadSasUrlsReply>(
                              new CreateBlobDownloadSasUrlsRequest(
                                  sleekflowCompanyId,
                                  new List<string>()
                                  {
                                      uploadSas.BlobName ??
                                      throw new SfInternalErrorException(
                                          $"Get download BlobName failed, sleekflowCompanyId: {sleekflowCompanyId}, blobType: {blobType}")
                                  },
                                  BlobTypes.File,
                                  null,
                                  null),
                              cancellationToken)).Message.DownloadBlobs.FirstOrDefault() ??
                          throw new SfInternalErrorException(
                              $"Get download Blob link failed, sleekflowCompanyId: {sleekflowCompanyId}, blobType: {blobType}");


        return new Uri(downloadSas.Url);
    }
}