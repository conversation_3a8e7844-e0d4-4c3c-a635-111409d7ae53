﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IAzureCosmosVectorDBConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class AzureCosmosVectorDBConfig : IConfig, IAzureCosmosVectorDBConfig
{
    public string Endpoint { get; }

    public string Key { get; }

    public string DatabaseId { get; }

    public AzureCosmosVectorDBConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("AZURE_COSMOS_VECTOR_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("AZURE_COSMOS_VECTOR_DB_ENDPOINT");

        Key =
            Environment.GetEnvironmentVariable("AZURE_COSMOS_VECTOR_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("AZURE_COSMOS_VECTOR_DB_KEY");

        DatabaseId =
            Environment.GetEnvironmentVariable("AZURE_COSMOS_VECTOR_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("AZURE_COSMOS_VECTOR_DB_CONNECTION_STRING");
    }
}