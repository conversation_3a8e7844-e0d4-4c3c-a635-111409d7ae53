﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Schemas;

[TriggerGroup(TriggerGroups.Schemas)]
public class GetSchema : ITrigger<GetSchema.GetSchemaInput, GetSchema.GetSchemaOutput>
{
    private readonly ISchemaService _schemaService;

    public GetSchema(
        ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public class GetSchemaInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(Entity.PropertyNameId)]
        public string Id { get; set; }

        [JsonConstructor]
        public GetSchemaInput(
            string sleekflowCompanyId,
            string id)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Id = id;
        }
    }

    public class GetSchemaOutput
    {
        [JsonProperty("schema")]
        public SchemaDto Schema { get; set; }

        [JsonConstructor]
        public GetSchemaOutput(
            SchemaDto schema)
        {
            Schema = schema;
        }
    }

    public async Task<GetSchemaOutput> F(
        GetSchemaInput getSchemaInput)
    {
        var schema = await _schemaService.GetAsync(
            getSchemaInput.Id,
            getSchemaInput.SleekflowCompanyId);

        return new GetSchemaOutput(new SchemaDto(schema));
    }
}