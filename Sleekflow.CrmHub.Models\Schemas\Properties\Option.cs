﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Schemas.Properties;

public sealed class Option
{
    public const string PropertyNameId = "id";
    public const string PropertyNameValue = "value";
    public const string PropertyNameDisplayOrder = "display_order";

    [JsonProperty(PropertyNameId)]
    public string Id { get; }

    [JsonProperty(PropertyNameValue)]
    public string Value { get; set; }

    [JsonProperty(PropertyNameDisplayOrder)]
    public int DisplayOrder { get; set; }

    [JsonConstructor]
    public Option(string id, string value, int displayOrder)
    {
        Id = id;
        Value = value;
        DisplayOrder = displayOrder;
    }
}