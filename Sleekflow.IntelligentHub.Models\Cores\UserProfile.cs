using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Cores;

public class UserProfile : IHasSleekflowCompanyId
{
    [JsonProperty("Id")]
    public string Id { get; set; }

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("phone_number")]
    public string PhoneNumber { get; set; }

    [JsonConstructor]
    public UserProfile(string id, string sleekflowCompanyId, string phoneNumber)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        PhoneNumber = phoneNumber;
    }
}