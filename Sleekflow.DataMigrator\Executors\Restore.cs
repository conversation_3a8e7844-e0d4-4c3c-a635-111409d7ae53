﻿using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Polly;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

class Restore : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private List<string>? _containerIds;

    public Restore(
        DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Restore";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        _databaseId = selectedDatabaseId;
    }

    public async Task ExecuteAsync()
    {
        foreach (var file in Directory.GetFiles("."))
        {
            var fileInfo = new FileInfo(file);

            var containerId = fileInfo.Name.Replace(".json", string.Empty);
            var jsons = File.ReadLines(file);

            var count = await RestoreObjectsAsync(containerId, jsons);
            Console.WriteLine($"Completed {containerId} for {count} objects");
        }
    }

    private async Task<int> RestoreObjectsAsync(
        string containerId,
        IEnumerable<string> jsons)
    {
        var retryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                9,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 2)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    return TimeSpan.FromSeconds(1.37) * retryCount;
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    Console.WriteLine(
                        $"TooManyRequests retryCount=[{retryCount}], timeSpan=[{timeSpan}], containerId=[{containerId}]");

                    return Task.CompletedTask;
                });

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerId);

        var i = 0;

        await Parallel.ForEachAsync(
            jsons,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 40
            },
            async (json, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(
                        async () =>
                        {
                            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                                json,
                                JsonConfig.DefaultJsonSerializerSettings);
                            await container.UpsertItemAsync(dictionary, cancellationToken: token);
                        });

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }
}