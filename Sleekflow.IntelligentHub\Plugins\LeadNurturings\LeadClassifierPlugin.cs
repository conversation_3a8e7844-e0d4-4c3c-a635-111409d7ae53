using System.ComponentModel;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface ILeadClassifierPlugin
{
    [KernelFunction("classify_lead")]
    [Description(
        "Classifies leads using conversation context from data pane and stores results with structured keys for efficient data management.")]
    [return: Description("Success message confirming classification completed and stored in data pane.")]
    Task<string> ClassifyLeadWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey);
}

public class LeadClassifierPlugin : BaseLeadNurturingPlugin, ILeadClassifierPlugin
{
    public LeadClassifierPlugin(
        ILogger<LeadClassifierPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel)
    {
    }

    private async Task<string> ClassifyLeadAsync(
        Kernel kernel,
        string conversationContext)
    {
        var leadClassifierAgent = _agentDefinitions.GetLeadClassifierAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true));

        var agentThread = new ChatHistoryAgentThread();
        agentThread.ChatHistory.AddUserMessage(conversationContext);

        return await ExecuteAgentWithTelemetryAsync(leadClassifierAgent, agentThread, "LeadClassifierPlugin");
    }

    [KernelFunction("classify_lead")]
    [Description(
        "Classifies leads using conversation context from data pane and stores results with structured keys for efficient data management.")]
    [return: Description("Original agent response with lead classification details.")]
    public async Task<string> ClassifyLeadWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey)
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.LeadClassifierAgentResponse>(
            sessionKey,
            "LeadClassifier",
            dataRetrievalFunc: async () => await RetrieveMultipleDataAsync(
                (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext")
            ),
            agentExecutionFunc: async (data) => await ClassifyLeadAsync(_kernel.Clone(), data["conversationContext"]),
            storageKey: _keyManager.GetClassificationKey(sessionKey),
            storageDataType: AgentOutputKeys.LeadClassifierComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreClassificationComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task StoreClassificationComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.LeadClassifierAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "LeadClassifierAgent",
                        AgentOutputKeys.LeadClassifierReasoning),
                    AgentOutputKeys.LeadClassifierReasoning,
                    parsedResponse.Reasoning);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "LeadClassifierAgent",
                        AgentOutputKeys.LeadClassifierScore),
                    AgentOutputKeys.LeadClassifierScore,
                    parsedResponse.Score);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "LeadClassifierAgent",
                        AgentOutputKeys.LeadClassifierClassification),
                    AgentOutputKeys.LeadClassifierClassification,
                    parsedResponse.Classification);

                _logger.LogInformation(
                    "Lead classification completed for session {SessionKey}: {Classification} (Score: {Score})",
                    sessionKey,
                    parsedResponse.Classification,
                    parsedResponse.Score);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to store classification components for session {SessionKey}", sessionKey);
        }
    }
}