﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Agents.Reviewers;
using Sleekflow.IntelligentHub.Evaluator.Constants;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

namespace Sleekflow.IntelligentHub.Evaluator.ExitCondition.Methods;

public class DefaultExitConditionMethod : IExitConditionMethod<ExitConditionTestResult>
{
    private readonly IReviewerService _reviewerService;

    public string MethodName => MethodNames.ExitCondition;

    public DefaultExitConditionMethod()
    {
        using var scope = Application.Host.Services.CreateScope();
        _reviewerService = scope.ServiceProvider.GetRequiredService<IReviewerService>();
    }

    public async Task<ExitConditionTestResult> CompleteAsync(
        ChatMessageContent[] questionContexts,
        List<Models.Companies.CompanyAgentConfigs.Actions.ExitCondition> exitConditions)
    {
        var chatEntries = questionContexts.Select(ChatEntriesUtil.ToChatEntries).ToList();
        var exitConditionResult = await _reviewerService.GetExitConditionResultAsync(chatEntries, null, 100, exitConditions);
        return new ExitConditionTestResult(
            MethodName,
            exitConditionResult,
            0);
    }
}