using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.FileContentCaches;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.FileContentCaches;

public interface IFileContentCacheRepository : IRepository<FileContentCache>
{
}

public class FileContentCacheRepository : BaseRepository<FileContentCache>, IFileContentCacheRepository, IScopedService
{
    public FileContentCacheRepository(
        ILogger<FileContentCacheRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}