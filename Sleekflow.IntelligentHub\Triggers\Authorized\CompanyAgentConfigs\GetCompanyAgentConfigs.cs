using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Services;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.CompanyAgentConfigs;

[TriggerGroup(
    ControllerNames.CompanyAgentConfigs,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetCompanyAgentConfigs
    : ITrigger<GetCompanyAgentConfigs.GetCompanyAgentConfigsInput, GetCompanyAgentConfigs.GetCompanyAgentConfigsOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly IWorkflowCountService _workflowCountService;
    private readonly IIntelligentHubCharacterCountService _characterCountService;

    public GetCompanyAgentConfigs(
        ISleekflowAuthorizationContext authorizationContext,
        ICompanyAgentConfigService companyAgentConfigService,
        IWorkflowCountService workflowCountService,
        IIntelligentHubCharacterCountService characterCountService)
    {
        _authorizationContext = authorizationContext;
        _companyAgentConfigService = companyAgentConfigService;
        _workflowCountService = workflowCountService;
        _characterCountService = characterCountService;
    }

    public class GetCompanyAgentConfigsInput
    {
    }

    public class GetCompanyAgentConfigsOutput
    {
        [JsonProperty("company_agent_configs")]
        public List<CompanyAgentConfigDto> CompanyAgentConfigs { get; set; }

        [JsonProperty("character_count_dictionary")]
        public Dictionary<string, int> CharacterCountDictionary { get; set; }

        [JsonProperty("character_limit")]
        public int CharacterLimit { get; set; }

        [JsonConstructor]
        public GetCompanyAgentConfigsOutput(
            List<CompanyAgentConfigDto> companyAgentConfigs,
            Dictionary<string, int> characterCountDictionary,
            int characterLimit)
        {
            CompanyAgentConfigs = companyAgentConfigs;
            CharacterCountDictionary = characterCountDictionary;
            CharacterLimit = characterLimit;
        }
    }

    public async Task<GetCompanyAgentConfigsOutput> F(GetCompanyAgentConfigsInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;
        var configs = await _companyAgentConfigService.GetObjectsAsync(sleekflowCompanyId);

        // Get workflow counts for all agent configs
        var agentConfigIds = configs.Select(c => c.Id).ToList();
        var workflowCounts = await _workflowCountService.GetActiveWorkflowCountsByAgentConfigIdsAsync(
            sleekflowCompanyId,
            agentConfigIds);

        // Get character limit for the company
        var characterLimit =
            await _characterCountService.GetCharacterCountLimit(sleekflowCompanyId);

        // Create DTOs with workflow counts and character counts
        var configDtos = new List<CompanyAgentConfigDto>();
        var characterCountDictionary = new Dictionary<string, int>();
        foreach (var config in configs)
        {
            var dto = new CompanyAgentConfigDto(config, workflowCounts.GetValueOrDefault(config.Id, 0));
            configDtos.Add(dto);

            var characterCount = await _characterCountService.GetCharacterCountForAgent(sleekflowCompanyId, config.Id);
            characterCountDictionary[config.Id] = characterCount;
        }

        return new GetCompanyAgentConfigsOutput(configDtos, characterCountDictionary, characterLimit);
    }
}