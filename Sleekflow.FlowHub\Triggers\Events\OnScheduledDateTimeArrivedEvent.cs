using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    null,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnScheduledDateTimeArrivedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;

    public OnScheduledDateTimeArrivedEvent(
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnScheduledDateTimeArrivedEventInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [Required]
        public OnScheduledDateTimeArrivedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnScheduledDateTimeArrivedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnScheduledDateTimeArrivedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnScheduledDateTimeArrivedEventOutput
    {
    }

    public async Task<OnScheduledDateTimeArrivedEventOutput> F(
        OnScheduledDateTimeArrivedEventInput input)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                input.EventBody,
                input.ContactId,
                "Contact",
                input.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                input.SleekflowCompanyId));

        return new OnScheduledDateTimeArrivedEventOutput();
    }
}