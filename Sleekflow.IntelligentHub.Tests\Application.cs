using System.Xml;
using Alba;
using MassTransit;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.JsonConfigs;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Func.Abstractions;
using Sleekflow.Mvc.Tests;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Tests;

[SetUpFixture]
public class Application
{
    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }

    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.IntelligentHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        await SetUpHost();
    }

    private static async Task SetUpHost()
    {
        Host = await AlbaHost.For<Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(services =>
                {
                    services.AddScoped<IDynamicFiltersRepositoryContext, TestRepositoryContext>();
                    services.AddScoped<IHeadersAuthorizationFuncFilter, TestHeadersAuthorizationFuncFilter>();
                });
            },
            Array.Empty<IAlbaExtension>());

        Host.AfterEachAsync(async context =>
        {
            await BaseTestHost.InterceptAfterEachAsync(context);
        });

        InMemoryBusHost = await AlbaHost.For<Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(services =>
                {
                    services.AddScoped<IDynamicFiltersRepositoryContext, TestRepositoryContext>();
                    services.AddScoped<IHeadersAuthorizationFuncFilter, TestHeadersAuthorizationFuncFilter>();

                    services.AddMassTransitTestHarness(r =>
                    {
                        r.UsingInMemory((context, cfg) =>
                        {
                            cfg.UseNewtonsoftJsonSerializer();
                            cfg.UseNewtonsoftJsonDeserializer();
                            cfg.ConfigureNewtonsoftJsonSerializer(
                                JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                            cfg.ConfigureNewtonsoftJsonDeserializer(
                                JsonConfig.EnhanceWithDefaultJsonSerializerSettings);
                            cfg.ConfigureEndpoints(context);
                        });
                    });
                });
            },
            Array.Empty<IAlbaExtension>());

        InMemoryBusHost.AfterEachAsync(async context =>
        {
            await BaseTestHost.InterceptAfterEachAsync(context);
        });
    }

    public class TestRepositoryContext : IDynamicFiltersRepositoryContext
    {
        // The change feed processor relies on the status(active/deleted) of knowledge base entries to manage indexers,
        // if soft delete is not enable, the change feed processor cannot capture the change.
        public bool IsSoftDeleteEnabled { get; set; } = true;

        public string? SleekflowCompanyId { get; set; } = null;
    }

    public static class TestAuthorizationContext
    {
        public static string? SleekflowCompanyId { get; set; } = null;
    }

    public class TestHeadersAuthorizationFuncFilter : IHeadersAuthorizationFuncFilter
    {
        private readonly ISleekflowAuthorizationContext _sleekflowAuthorizationContext;

        public TestHeadersAuthorizationFuncFilter(
            ISleekflowAuthorizationContext sleekflowAuthorizationContext)
        {
            _sleekflowAuthorizationContext = sleekflowAuthorizationContext;
        }

        public Task FilterAsync(HttpRequest httpRequest)
        {
            _sleekflowAuthorizationContext.SleekflowCompanyId = TestAuthorizationContext.SleekflowCompanyId;

            // Do nothing
            // Replaced by the scoped TestSleekflowAuthorizationContext
            return Task.CompletedTask;
        }
    }

    public static IAlbaHost Host { get; private set; }

    public static IAlbaHost InMemoryBusHost { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        Host.Dispose();
        InMemoryBusHost.Dispose();
    }
}