﻿using Newtonsoft.Json;
using Sleekflow.DurablePayloads;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;
using Entity = Sleekflow.Persistence.Entity;

namespace Sleekflow.CrmHub.Models.Subscriptions;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("hubspot_subscription")]
public class HubspotSubscription : Entity
{
    public const string SysTypeNameValue = "HubspotSubscription";
    public const string PropertyNameLastExecutionStartTime = "last_execution_start_time";
    public const string PropertyNameLastObjectModificationTime = "last_object_modification_time";
    public const string PropertyNameDurablePayload = "durable_payload";

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("interval")]
    public int Interval { get; set; }

    [JsonProperty("connection_id")]
    public string? ConnectionId { get; set; }

    [JsonProperty("is_flows_based")]
    public bool? IsFlowsBased { get; set; }

    [JsonProperty(PropertyNameLastExecutionStartTime)]
    public DateTimeOffset LastExecutionStartTime { get; set; }

    [JsonProperty(PropertyNameLastObjectModificationTime)]
    public DateTimeOffset? LastObjectModificationTime { get; set; }

    [JsonProperty(PropertyNameDurablePayload)]
    public DurablePayload? DurablePayload { get; set; }

    [JsonConstructor]
    public HubspotSubscription(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        int interval,
        string? connectionId,
        bool? isFlowsBased,
        DateTimeOffset lastExecutionStartTime,
        DurablePayload? durablePayload,
        DateTimeOffset? lastObjectModificationTime)
        : base(id, SysTypeNameValue)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EntityTypeName = entityTypeName;
        Interval = interval;
        ConnectionId = connectionId;
        IsFlowsBased = isFlowsBased;
        LastExecutionStartTime = lastExecutionStartTime;
        DurablePayload = durablePayload;
        LastObjectModificationTime = lastObjectModificationTime;
    }
}