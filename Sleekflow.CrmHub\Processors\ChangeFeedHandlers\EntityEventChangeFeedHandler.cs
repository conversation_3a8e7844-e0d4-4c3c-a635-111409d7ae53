﻿using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.EntityEvents;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Processors.ChangeFeedHandlers.Abstractions;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Processors.ChangeFeedHandlers;

public interface IEntityEventChangeFeedHandler : IChangeFeedHandler
{
}

public class EntityEventChangeFeedHandler : IEntityEventChangeFeedHandler, ISingletonService
{
    private readonly IBus _bus;

    public EntityEventChangeFeedHandler(
        IBus bus)
    {
        _bus = bus;
    }

    public string GetContainerName()
    {
        return "entity_event";
    }

    public virtual async Task OnChangesDelegate(
        ChangeFeedProcessorContext context,
        IReadOnlyCollection<Dictionary<string, object?>> changes,
        CancellationToken cancellationToken)
    {
        var changesGroupings = changes
            .GroupBy(c => (string) c[EntityEvent.PropertyNameSysSleekflowCompanyId]!)
            .ToList();

        foreach (var grouping in changesGroupings)
        {
            var sleekflowCompanyId = grouping.Key;

            var events = grouping
                .Select(
                    dict =>
                    {
                        var onObjectPersistedEvent = new OnObjectPersistedEvent(
                            dict,
                            sleekflowCompanyId,
                            (string) dict[EntityEvent.PropertyNameEntityId]!,
                            (string) dict[EntityEvent.PropertyNameEntityTypeName]!);

                        return onObjectPersistedEvent;
                    })
                .ToList();

            await _bus.PublishBatch(
                events,
                publishContext => { publishContext.ConversationId = Guid.Parse(sleekflowCompanyId); },
                cancellationToken: cancellationToken);
        }
    }
}