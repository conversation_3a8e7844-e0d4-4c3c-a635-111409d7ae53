﻿using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;

public class CreditTransferTargetObject
{
    [JsonProperty("facebook_business_id")]
    public string? FacebookBusinessId { get; set; }

    [JsonProperty("facebook_waba_id")]
    public string? FacebookWabaId { get; set; }

    [JsonProperty("target_type")]
    public string TargetType { get; set; }

    [JsonConstructor]
    public CreditTransferTargetObject(string? facebookBusinessId, string? facebookWabaId, string targetType)
    {
        FacebookBusinessId = facebookBusinessId;
        FacebookWabaId = facebookWabaId;
        TargetType = targetType;
    }
}