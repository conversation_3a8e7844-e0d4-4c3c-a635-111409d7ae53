﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Entities;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Objects;

[TriggerGroup("Objects")]
public class GetObjects : ITrigger
{
    private readonly IEntityRepository _entityRepository;

    public GetObjects(
        IEntityRepository entityRepository)
    {
        _entityRepository = entityRepository;
    }

    public class GetObjectsInputFilterGroup
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<EntityQueryBuilder.Filter> Filters { get; set; }

        [JsonConstructor]
        public GetObjectsInputFilterGroup(
            List<EntityQueryBuilder.Filter> filters)
        {
            Filters = filters;
        }
    }

    public class GetObjectsInput : IValidatableObject
    {
        [JsonProperty("continuation_token")]
        [StringLength(16384, MinimumLength = 1)]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        [StringLength(128, MinimumLength = 1)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("entity_type_name")]
        [StringLength(128, MinimumLength = 1)]
        public string EntityTypeName { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetObjectsInputFilterGroup> FilterGroups { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("sorts")]
        public List<EntityQueryBuilder.Sort> Sorts { get; set; }

        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            var results = new List<ValidationResult>();

            var crmHubDbEntityService = validationContext.GetRequiredService<ICrmHubDbEntityService>();
            if (crmHubDbEntityService.IsNotSupportedEntityTypeName(EntityTypeName))
            {
                results.Add(
                    new ValidationResult(
                        "The EntityTypeName is not supported.",
                        new List<string>
                        {
                            nameof(EntityTypeName)
                        }));
            }

            return results;
        }

        [JsonConstructor]
        public GetObjectsInput(
            string? continuationToken,
            string sleekflowCompanyId,
            string entityTypeName,
            int limit,
            List<GetObjectsInputFilterGroup> filterGroups,
            List<EntityQueryBuilder.Sort> sorts)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            Limit = limit;
            FilterGroups = filterGroups;
            Sorts = sorts;
        }
    }

    public class GetObjectsOutput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("records")]
        public List<CrmHubEntity> Records { get; set; }

        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetObjectsOutput(
            string? continuationToken,
            List<CrmHubEntity> records,
            long count)
        {
            ContinuationToken = continuationToken;
            Records = records;
            Count = count;
        }
    }

    public async Task<GetObjectsOutput> F(
        GetObjectsInput getObjectsInput)
    {
        var filterGroups = getObjectsInput
            .FilterGroups
            .Select(fg => new EntityQueryBuilder.FilterGroup(fg.Filters.Cast<EntityQueryBuilder.IFilter>().ToList()))
            .ToList();

        var queryDefinition =
            EntityQueryBuilder.BuildQueryDef(
                new List<EntityQueryBuilder.ISelect>(),
                getObjectsInput.EntityTypeName,
                filterGroups,
                getObjectsInput.Sorts,
                new List<EntityQueryBuilder.GroupBy>(),
                getObjectsInput.SleekflowCompanyId);

        var (rawRecords, nextContinuationToken) =
            await _entityRepository.GetContinuationTokenizedObjectsAsync(
                queryDefinition,
                getObjectsInput.ContinuationToken,
                getObjectsInput.Limit);

        var records = rawRecords
            .Select(EntityService.Sanitize)
            .ToList();

        return new GetObjectsOutput(nextContinuationToken, records, records.Count);
    }
}