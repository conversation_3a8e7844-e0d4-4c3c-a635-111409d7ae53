using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.PaymentProviderConfigs;

public interface IPaymentProviderConfigService
{
    Task<PaymentProviderConfig> GetPaymentProviderConfigAsync(string id, string sleekflowCompanyId);

    Task<PaymentProviderConfig> GetPaymentProviderConfigByStripeAccountIdAsync(string stripeAccountId);

    Task<List<PaymentProviderConfig>> GetPaymentProviderConfigsAsync(
        string sleekflowCompanyId);

    Task<List<PaymentProviderConfig>> GetStorePaymentProviderConfigsAsync(
        string sleekflowCompanyId,
        string storeId);

    Task<PaymentProviderConfig> CreatePaymentProviderConfigAsync(
        PaymentProviderConfig paymentProviderConfig,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<PaymentProviderConfig> PatchPaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        List<string> storeIds,
        Dictionary<string, List<string>> storeIdToCurrencyIsoCodesDict,
        List<string> supportedCurrencyIsoCodes);

    Task<PaymentProviderConfig> PatchPaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        PaymentProviderExternalConfig paymentProviderExternalConfig);

    Task<PaymentProviderConfig> PatchPaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        string status);

    Task DeletePaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task DeletePaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId);
}

public class PaymentProviderConfigService : IPaymentProviderConfigService, IScopedService
{
    private readonly IPaymentProviderConfigRepository _paymentProviderConfigRepository;
    private readonly IPaymentProviderConfigValidator _paymentProviderConfigValidator;

    public PaymentProviderConfigService(
        IPaymentProviderConfigRepository paymentProviderConfigRepository,
        IPaymentProviderConfigValidator paymentProviderConfigValidator)
    {
        _paymentProviderConfigRepository = paymentProviderConfigRepository;
        _paymentProviderConfigValidator = paymentProviderConfigValidator;
    }

    public async Task<PaymentProviderConfig> GetPaymentProviderConfigAsync(string id, string sleekflowCompanyId)
    {
        var paymentProviderConfig =
            await _paymentProviderConfigRepository.GetAsync(
                id,
                sleekflowCompanyId);

        return paymentProviderConfig;
    }

    public async Task<PaymentProviderConfig> GetPaymentProviderConfigByStripeAccountIdAsync(string stripeAccountId)
    {
        return await _paymentProviderConfigRepository.GetPaymentProviderConfigByStripeAccountIdAsync(
            stripeAccountId);
    }

    public Task<List<PaymentProviderConfig>> GetPaymentProviderConfigsAsync(string sleekflowCompanyId)
    {
        return _paymentProviderConfigRepository.GetPaymentProviderConfigsAsync(sleekflowCompanyId);
    }

    public Task<List<PaymentProviderConfig>> GetStorePaymentProviderConfigsAsync(
        string sleekflowCompanyId,
        string storeId)
    {
        return _paymentProviderConfigRepository.GetPaymentProviderConfigsAsync(
            sleekflowCompanyId,
            storeId);
    }

    public async Task<PaymentProviderConfig> CreatePaymentProviderConfigAsync(
        PaymentProviderConfig paymentProviderConfig,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        await _paymentProviderConfigValidator.AssertValidPaymentProviderConfigAsync(
            paymentProviderConfig.SleekflowCompanyId,
            paymentProviderConfig.StoreIds,
            paymentProviderConfig.StoreIdToCurrencyIsoCodesDict,
            paymentProviderConfig.SupportedCurrencyIsoCodes);

        return await _paymentProviderConfigRepository.CreateAndGetAsync(
            paymentProviderConfig,
            paymentProviderConfig.SleekflowCompanyId);
    }

    public async Task<PaymentProviderConfig> PatchPaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        List<string> storeIds,
        Dictionary<string, List<string>> storeIdToCurrencyIsoCodesDict,
        List<string> supportedCurrencyIsoCodes)
    {
        await _paymentProviderConfigValidator.AssertValidPaymentProviderConfigAsync(
            sleekflowCompanyId,
            storeIds,
            storeIdToCurrencyIsoCodesDict,
            supportedCurrencyIsoCodes);

        var paymentProviderConfig =
            await _paymentProviderConfigRepository.GetAsync(
                id,
                sleekflowCompanyId);

        paymentProviderConfig.StoreIds = storeIds;
        paymentProviderConfig.StoreIdToCurrencyIsoCodesDict = storeIdToCurrencyIsoCodesDict;
        paymentProviderConfig.SupportedCurrencyIsoCodes = supportedCurrencyIsoCodes;

        var upsertCount = await _paymentProviderConfigRepository.UpsertAsync(
            paymentProviderConfig,
            sleekflowCompanyId);
        if (upsertCount == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the PaymentProviderConfig id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }

        return paymentProviderConfig;
    }

    public async Task<PaymentProviderConfig> PatchPaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        PaymentProviderExternalConfig paymentProviderExternalConfig)
    {
        var paymentProviderConfig =
            await _paymentProviderConfigRepository.GetAsync(
                id,
                sleekflowCompanyId);

        paymentProviderConfig.PaymentProviderExternalConfig = paymentProviderExternalConfig;

        var upsertCount = await _paymentProviderConfigRepository.UpsertAsync(
            paymentProviderConfig,
            sleekflowCompanyId);
        if (upsertCount == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the PaymentProviderConfig id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }

        return paymentProviderConfig;
    }

    public async Task<PaymentProviderConfig> PatchPaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        string status)
    {
        var paymentProviderConfig =
            await _paymentProviderConfigRepository.GetAsync(
                id,
                sleekflowCompanyId);

        paymentProviderConfig.Status = status;

        var upsertCount = await _paymentProviderConfigRepository.UpsertAsync(
            paymentProviderConfig,
            sleekflowCompanyId);
        if (upsertCount == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the PaymentProviderConfig id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }

        return paymentProviderConfig;
    }

    public async Task DeletePaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var paymentProviderConfig = await _paymentProviderConfigRepository.GetAsync(
            id,
            sleekflowCompanyId);

        var deleteAsync = await _paymentProviderConfigRepository.DeleteAsync(
            paymentProviderConfig.Id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the PaymentProviderConfig id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }

    public async Task DeletePaymentProviderConfigAsync(
        string id,
        string sleekflowCompanyId)
    {
        var paymentProviderConfig = await _paymentProviderConfigRepository.GetAsync(
            id,
            sleekflowCompanyId);

        var deleteAsync = await _paymentProviderConfigRepository.DeleteAsync(
            paymentProviderConfig.Id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the PaymentProviderConfig id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }
    }
}