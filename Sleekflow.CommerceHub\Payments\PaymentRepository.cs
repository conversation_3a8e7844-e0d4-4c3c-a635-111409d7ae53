﻿using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Payments;

public interface IPaymentRepository : IDynamicFiltersRepository<Payment>
{
}

public class PaymentRepository : DynamicFiltersBaseRepository<Payment>, IPaymentRepository, IScopedService
{
    public PaymentRepository(
        ILogger<PaymentRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}