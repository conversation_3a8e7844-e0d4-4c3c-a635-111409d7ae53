using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class StaffStatusUpdatedSystemLogData
{
    [JsonProperty("staff_id")]
    public string StaffId { get; set; }

    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonConstructor]
    public StaffStatusUpdatedSystemLogData(string staffId, string status)
    {
        StaffId = staffId;
        Status = status;
    }
}