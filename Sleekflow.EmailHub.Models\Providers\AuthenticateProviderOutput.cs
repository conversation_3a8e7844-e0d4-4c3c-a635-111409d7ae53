using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Providers;

public class AuthenticateProviderOutput
{
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("email_address")]
    public string EmailAddress { get; set; }

    [JsonProperty("context")]
    public dynamic Context { get; set; }

    [JsonConstructor]
    public AuthenticateProviderOutput(
        string providerName,
        object context,
        string emailAddress)
    {
        ProviderName = providerName;
        Context = context;
        EmailAddress = emailAddress;
    }
}