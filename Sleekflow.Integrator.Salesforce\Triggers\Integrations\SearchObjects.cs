using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SearchObjects : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public SearchObjects(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class SearchObjectsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("conditions")]
        [ValidateArray]
        [Required]
        public List<SearchObjectCondition> Conditions { get; set; }

        [JsonConstructor]
        public SearchObjectsInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<SearchObjectCondition> conditions)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            Conditions = conditions;
        }
    }

    public class SearchObjectsOutput
    {
        [JsonProperty("objects")]
        [Required]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonConstructor]
        public SearchObjectsOutput(
            List<Dictionary<string, object?>> objects)
        {
            Objects = objects;
        }
    }

    public async Task<SearchObjectsOutput> F(
        SearchObjectsInput searchObjectsInput)
    {
        var connection = await _salesforceConnectionService.GetByIdAsync(
            searchObjectsInput.ConnectionId,
            searchObjectsInput.SleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                searchObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (objects, _) = await _salesforceObjectService.SearchObjectsAsync(
            authentication,
            searchObjectsInput.EntityTypeName,
            searchObjectsInput.Conditions);

        return new SearchObjectsOutput(objects);
    }
}