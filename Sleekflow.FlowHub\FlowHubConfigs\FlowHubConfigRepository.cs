using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.FlowHubConfigs;

public interface IFlowHubConfigRepository : IRepository<FlowHubConfig>
{
}

public class FlowHubConfigRepository : BaseRepository<FlowHubConfig>, IFlowHubConfigRepository, IScopedService
{
    public FlowHubConfigRepository(
        ILogger<FlowHubConfigRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}