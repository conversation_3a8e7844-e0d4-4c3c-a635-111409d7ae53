using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Configs;
using Stripe;
using Stripe.Checkout;

namespace Sleekflow.MessagingHub.Payments;

public interface IStripeClient
{
    public Task<Session> GeneratePaymentLink(
        string? stripeCustomerId,
        string successUrl,
        string cancelUrl,
        List<SessionLineItemOptions> sessionLineItemOptions,
        Dictionary<string, string> metadata,
        bool isSetupFutureUsage = false);

    public Task<List<PaymentMethod>> FindPaymentMethodsUsedForSubscriptionsForCustomerAsync(string customerId);

    public Task<List<Source>> FindSourcesUsedForSubscriptionsForCustomerAsync(string customerId);

    public Task UpdateCustomerDefaultPaymentMethod(string customerId, string paymentMethodId);

    // Method to check if a customer has a default payment
    public Task<bool> HasDefaultPaymentMethodAsync(string customerId);

    public Task<(bool HasDefaultSource, string? SourceId)> FindCustomerDefaultSourceAsync(string customerId);
}

public class StripeClient : IStripeClient, ISingletonService
{
    private readonly ILogger<StripeClient> _logger;

    public StripeClient(ILogger<StripeClient> logger, IStripeConfig stripeConfig)
    {
        _logger = logger;
        StripeConfiguration.ApiKey = stripeConfig.StripeApiKey;
    }

    public async Task<Session> GeneratePaymentLink(
        string? stripeCustomerId,
        string successUrl,
        string cancelUrl,
        List<SessionLineItemOptions> sessionLineItemOptions,
        Dictionary<string, string> metadata,
        bool isSetupFutureUsage = false)
    {
        var sessionCreateOptions = CreateSessionCreateOptions(successUrl, cancelUrl, sessionLineItemOptions, metadata, isSetupFutureUsage);

        if (stripeCustomerId != null)
        {
            sessionCreateOptions.Customer = stripeCustomerId;
        }
        var service = new SessionService();
        return await service.CreateAsync(sessionCreateOptions);
    }

    private static SessionCreateOptions CreateSessionCreateOptions(
        string successUrl,
        string cancelUrl,
        List<SessionLineItemOptions> sessionLineItemOptions,
        Dictionary<string, string> metadata,
        bool isSetupFutureUsage)
    {
        // Need to update to <PackageReference Include="Stripe.net" Version="41.3.0" /> in order to enable this feature
        var sessionCreateOptions = new SessionCreateOptions
        {
            LineItems = sessionLineItemOptions,
            Mode = "payment",
            InvoiceCreation = new SessionInvoiceCreationOptions
            {
                Enabled = true,
                InvoiceData = new SessionInvoiceCreationInvoiceDataOptions
                {
                    Metadata = metadata
                }
            },
            PaymentIntentData = new SessionPaymentIntentDataOptions
            {
                Metadata = metadata
            },
            Metadata = metadata,
            SuccessUrl = successUrl,
            CancelUrl = cancelUrl,
            AllowPromotionCodes = true
        };

        if (isSetupFutureUsage)
        {
            sessionCreateOptions.PaymentIntentData.SetupFutureUsage = "off_session";
        }

        return sessionCreateOptions;
    }

    public async Task<List<PaymentMethod>> FindPaymentMethodsUsedForSubscriptionsForCustomerAsync(string customerId)
    {
        var subscriptionListOptions = new SubscriptionListOptions
        {
            Customer = customerId, Status = "active"
        };

        var subscriptionService = new SubscriptionService();
        StripeList<Subscription> subscriptions = await subscriptionService.ListAsync(subscriptionListOptions);

        if (subscriptions is null)
        {
            return [];
        }

        var paymentMethods = new List<PaymentMethod>();
        var paymentMethodService = new PaymentMethodService();

        foreach (var subscription in subscriptions)
        {
            if (string.IsNullOrEmpty(subscription.DefaultPaymentMethodId))
            {
                continue;
            }

            var paymentMethod = await paymentMethodService.GetAsync(subscription.DefaultPaymentMethodId);
            paymentMethods.Add(paymentMethod);
        }

        return paymentMethods;
    }

    public async Task<List<Source>> FindSourcesUsedForSubscriptionsForCustomerAsync(string customerId)
    {
        var subscriptionListOptions = new SubscriptionListOptions
        {
            Customer = customerId, Status = "active"
        };

        var subscriptionService = new SubscriptionService();
        StripeList<Subscription> subscriptions = await subscriptionService.ListAsync(subscriptionListOptions);

        if (subscriptions is null)
        {
            return [];
        }

        var sources = new List<Source>();
        var sourceService = new SourceService();

        foreach (var subscription in subscriptions)
        {
            if (string.IsNullOrEmpty(subscription.DefaultSourceId))
            {
                continue;
            }

            var source = await sourceService.GetAsync(subscription.DefaultSourceId);
            sources.Add(source);
        }

        return sources;
    }

    public async Task UpdateCustomerDefaultPaymentMethod(string customerId, string paymentMethodId)
    {
        // Attach the payment method to the customer
        var attachOptions = new PaymentMethodAttachOptions
        {
            Customer = customerId
        };

        var attachService = new PaymentMethodService();
        var attachedPaymentMethod = await attachService.AttachAsync(paymentMethodId, attachOptions);

        var updateOptions = new CustomerUpdateOptions
        {
            InvoiceSettings = new CustomerInvoiceSettingsOptions
            {
                DefaultPaymentMethod = attachedPaymentMethod.Id,
            }
        };

        var updateService = new CustomerService();
        await updateService.UpdateAsync(customerId, updateOptions);
    }

    public async Task<bool> HasDefaultPaymentMethodAsync(string customerId)
    {
        var customerService = new CustomerService();
        var customer = await customerService.GetAsync(customerId);

        return customer != null && !string.IsNullOrEmpty(customer.InvoiceSettings.DefaultPaymentMethodId);
    }

    public async Task<(bool HasDefaultSource, string? SourceId)> FindCustomerDefaultSourceAsync(string customerId)
    {
        try
        {
            var customerService = new CustomerService();
            var customer = await customerService.GetAsync(customerId);

            return (
                HasDefaultSource: customer != null && !string.IsNullOrEmpty(customer.DefaultSourceId),
                SourceId: customer?.DefaultSourceId
            );
        }
        catch (Exception)
        {
            return (false, null);
        }
    }
}