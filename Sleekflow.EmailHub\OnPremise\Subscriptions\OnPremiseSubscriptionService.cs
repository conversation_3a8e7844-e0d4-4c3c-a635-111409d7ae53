using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.OnPremise.Authentications;
using Sleekflow.EmailHub.Models.OnPremise.Events;
using Sleekflow.EmailHub.Models.OnPremise.Subscriptions;
using Sleekflow.EmailHub.Models.Providers;
using Sleekflow.EmailHub.Models.Subscriptions;
using Sleekflow.EmailHub.OnPremise.Authentications;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.OnPremise.Subscriptions;

public interface IOnPremiseSubscriptionService : IEmailSubscriptionService
{
    Task UpdateStartIndex(
        string sleekflowCompanyId,
        string emailAddress,
        int startIndex,
        CancellationToken cancellationToken = default);

    Task UpdateStartIndex(
        ProviderConfig providerConfig,
        int startIndex,
        string emailAddress,
        CancellationToken cancellationToken = default);
}

public class OnPremiseSubscriptionService : IOnPremiseSubscriptionService, IScopedService
{
    private readonly IOnPremiseAuthenticationService _onPremiseAuthenticationService;
    private readonly ILogger<OnPremiseSubscriptionService> _logger;
    private readonly IBus _bus;
    private readonly IProviderConfigService _providerConfigService;

    public OnPremiseSubscriptionService(
        IOnPremiseAuthenticationService onPremiseAuthenticationService,
        ILogger<OnPremiseSubscriptionService> logger,
        IBus bus,
        IProviderConfigService providerConfigService)
    {
        _onPremiseAuthenticationService = onPremiseAuthenticationService;
        _logger = logger;
        _bus = bus;
        _providerConfigService = providerConfigService;
    }

    public async Task<EmailSubscription> GetSubscriptionAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.OnPremise,
            cancellationToken: cancellationToken);

        if (providerConfig == null || !providerConfig.EmailSubscription.IsSubscribed)
        {
            _logger.LogError(
                "GetSubscriptionAsync fails due to not subscribed: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                emailAddress,
                sleekflowCompanyId);

            throw new SfUnauthorizedException();
        }

        return providerConfig.EmailSubscription;
    }

    public async Task SubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? subscriptionMetadata,
        CancellationToken cancellationToken = default)
    {
        _ = await _onPremiseAuthenticationService.GetAuthenticationAsync(
            sleekflowCompanyId,
            emailAddress,
            ProtocolTypes.Imap,
            cancellationToken: cancellationToken);

        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.OnPremise,
            cancellationToken: cancellationToken);

        providerConfig.EmailSubscription.IsSubscribed = true;
        providerConfig.EmailSubscription.LastSubscriptionTime = DateTimeOffset.UtcNow;
        var startIndex =
            (providerConfig.EmailSubscription.EmailSubscriptionMetadata as OnPremiseSubscriptionMetadata)
            ?.StartIndex ?? 0;
        providerConfig.EmailSubscription.EmailSubscriptionMetadata = new OnPremiseSubscriptionMetadata(startIndex);
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);

        await _bus.Publish(
            new OnOnPremiseSyncAllEmailsTriggeredEvent(
                emailAddress),
            cancellationToken);
    }

    public async Task UnsubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.OnPremise,
            cancellationToken: cancellationToken);

        var subscription = providerConfig.EmailSubscription;
        subscription.IsSubscribed = false;
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);
    }

    public Task RenewEmailSubscriptionAsync(CancellationToken cancellationToken = default)
    {
        // intentionally unimplemented
        throw new NotImplementedException();
    }

    public async Task<List<string>> FilterSubscribedCompanies(
        List<string> companyIds,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var result = new List<string>();

        foreach (var companyId in companyIds)
        {
            try
            {
                _ = await GetSubscriptionAsync(
                    companyId,
                    emailAddress,
                    cancellationToken);
                result.Add(companyId);
            }
            catch (Exception)
            {
                // ignored unsubscribed company
            }
        }

        return result;
    }

    public async Task UpdateStartIndex(
        string sleekflowCompanyId,
        string emailAddress,
        int startIndex,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = (await _providerConfigService.GetObjectsAsync(
            x =>
                    x.SleekflowCompanyId == sleekflowCompanyId &&
                    x.EmailAddress == emailAddress &&
                    x.ProviderName == ProviderNames.OnPremise,
            cancellationToken))
            .FirstOrDefault();

        if (providerConfig == null)
        {
            return;
        }

        await UpdateStartIndex(providerConfig, startIndex, emailAddress, cancellationToken);
    }

    public async Task UpdateStartIndex(
        ProviderConfig providerConfig,
        int startIndex,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var onPremiseSubscriptionMetadata = providerConfig.EmailSubscription.EmailSubscriptionMetadata as OnPremiseSubscriptionMetadata ??
                                            throw new SfInternalErrorException(
                                                $"Cannot parse onPremiseSubscriptionMetaData: emailAddress {emailAddress}");
        onPremiseSubscriptionMetadata.StartIndex = startIndex;
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);
    }
}