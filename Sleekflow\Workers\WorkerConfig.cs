﻿using Sleekflow.Exceptions;

namespace Sleekflow.Workers;

public interface IWorkerConfig
{
    string WorkerHostname { get; }

    string WorkerFunctionsKey { get; }
}

public class WorkerConfig : IWorkerConfig
{
    public string WorkerHostname { get; }

    public string WorkerFunctionsKey { get; }

    public WorkerConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        WorkerHostname =
            Environment.GetEnvironmentVariable("WORKER_HOSTNAME", target)
            ?? throw new SfMissingEnvironmentVariableException("WORKER_HOSTNAME");
        WorkerFunctionsKey =
            Environment.GetEnvironmentVariable("WORKER_FUNCTIONS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("WORKER_FUNCTIONS_KEY");
    }
}