﻿using System.Text.RegularExpressions;

namespace Sleekflow.Events.QueueNameFormatter;

public static class KebabCaseFormatter
{
    /// <summary>
    /// Converts a name (PascalCase or CamelCase) to kebab-case.
    /// For example, "OrderPlacedEvent" → "order-placed-event".
    /// </summary>
    /// <returns>Kebab Case string.</returns>
    public static string Format(string? input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            throw new ArgumentException("Input cannot be null or whitespace.", nameof(input));
        }

        return Regex.Replace(input, "(?<!^)([A-Z])", "-$1").ToLowerInvariant();
    }
}
