using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.Models.Payments.StripeContexts;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.CommerceHub.Payments;
using Sleekflow.DependencyInjection;
using Stripe;

namespace Sleekflow.CommerceHub.Stripe.Webhooks;

public interface IStripeWebhookService
{
    Task ProcessStripeConnectWebhookAsync(
        string stripeConnectWebhook,
        string stripeSignatureHeader);

    Task ProcessStripePaymentWebhookAsync(
        string stripePaymentWebhook,
        string stripeSignatureHeader);

    Task ProcessStripeRefundWebhookAsync(
        string stripeRefundWebhook,
        string stripeSignatureHeader);
}

public class StripeWebhookService : IStripeWebhookService, IScopedService
{
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;
    private readonly IOrderService _orderService;
    private readonly IPaymentService _paymentService;

    public StripeWebhookService(
        IPaymentProviderConfigService paymentProviderConfigService,
        IOrderService orderService,
        IPaymentService paymentService)
    {
        _paymentProviderConfigService = paymentProviderConfigService;
        _orderService = orderService;
        _paymentService = paymentService;
    }

    public async Task ProcessStripeConnectWebhookAsync(
        string stripeConnectWebhook,
        string stripeSignatureHeader)
    {
        var stripeEvent = await ConstructStripeConnectEvent(stripeConnectWebhook, stripeSignatureHeader);

        var account = stripeEvent.Data.Object as Account;

        if (account != null && account.Metadata["source"] == "commerce-hub")
        {
            switch (stripeEvent.Type)
            {
                case global::Stripe.Events.AccountUpdated:
                    await HandleAccountUpdatedEvent(account);
                    return;
                case global::Stripe.Events.AccountApplicationDeauthorized:
                    await HandleAccountApplicationDeauthorizedEvent(account);
                    return;
                default:
                    throw new NotImplementedException();
            }
        }
    }

    public async Task ProcessStripePaymentWebhookAsync(
        string stripePaymentWebhook,
        string stripeSignatureHeader)
    {
        var stripeEvent = await ConstructStripePaymentEvent(stripePaymentWebhook, stripeSignatureHeader);

        var paymentIntent = stripeEvent.Data.Object as PaymentIntent;

        if (paymentIntent != null && paymentIntent.Metadata["source"] == "commerce-hub")
        {
            switch (stripeEvent.Type)
            {
                case global::Stripe.Events.PaymentIntentSucceeded:
                    await HandlePaymentIntentSucceededEvent(paymentIntent);
                    return;
                case global::Stripe.Events.PaymentIntentPaymentFailed:
                    await HandlePaymentIntentPaymentFailedEvent(paymentIntent);
                    return;
                default:
                    throw new NotImplementedException();
            }
        }
    }

    public async Task ProcessStripeRefundWebhookAsync(
        string stripeRefundWebhook,
        string stripeSignatureHeader)
    {
        var stripeEvent = await ConstructStripeRefundEvent(stripeRefundWebhook, stripeSignatureHeader);

        var refund = stripeEvent.Data.Object as Refund;

        if (refund != null && refund.Metadata["source"] == "commerce-hub")
        {
            switch (stripeEvent.Type)
            {
                case global::Stripe.Events.ChargeRefundUpdated:
                    await HandleChargeRefundUpdated(refund);
                    return;
                default:
                    throw new NotImplementedException();
            }
        }
    }

    private async Task<Event> ConstructStripeConnectEvent(
        string stripeConnectWebhook,
        string stripeSignatureHeader)
    {
        var stripeConnectEvent = JsonConvert.DeserializeObject<Event>(stripeConnectWebhook)!;

        var paymentProviderConfig =
            await _paymentProviderConfigService.GetPaymentProviderConfigByStripeAccountIdAsync(
                stripeConnectEvent.Account);

        var stripePaymentProviderExternalConfig =
            (StripePaymentProviderExternalConfig) paymentProviderConfig.PaymentProviderExternalConfig;

        return ConstructStripeEvent(
            stripeConnectWebhook,
            stripeSignatureHeader,
            stripePaymentProviderExternalConfig.StripeConnectWebhookSecret);
    }

    private async Task<Event> ConstructStripePaymentEvent(
        string stripePaymentWebhook,
        string stripeSignatureHeader)
    {
        var stripePaymentEvent = JsonConvert.DeserializeObject<Event>(stripePaymentWebhook)!;

        var paymentIntentUnverified = stripePaymentEvent.Data.Object as PaymentIntent;

        var paymentProviderConfig =
            await _paymentProviderConfigService.GetPaymentProviderConfigByStripeAccountIdAsync(
                paymentIntentUnverified.OnBehalfOfId);

        var stripePaymentProviderExternalConfig =
            (StripePaymentProviderExternalConfig) paymentProviderConfig.PaymentProviderExternalConfig;

        return ConstructStripeEvent(
            stripePaymentWebhook,
            stripeSignatureHeader,
            stripePaymentProviderExternalConfig.StripePaymentWebhookSecret);
    }

    private async Task<Event> ConstructStripeRefundEvent(
        string stripeRefundWebhook,
        string stripeSignatureHeader)
    {
        var stripeRefundEvent = JsonConvert.DeserializeObject<Event>(stripeRefundWebhook)!;

        var paymentProviderConfig =
            await _paymentProviderConfigService.GetPaymentProviderConfigByStripeAccountIdAsync(
                stripeRefundEvent.Account);

        var stripePaymentProviderExternalConfig =
            (StripePaymentProviderExternalConfig) paymentProviderConfig.PaymentProviderExternalConfig;

        return ConstructStripeEvent(
            stripeRefundWebhook,
            stripeSignatureHeader,
            stripePaymentProviderExternalConfig.StripePaymentWebhookSecret);
    }

    private static Event ConstructStripeEvent(
        string stripeWebhook,
        string stripeSignatureHeader,
        string stripeWebhookSecret)
    {
        return EventUtility.ConstructEvent(
            stripeWebhook,
            stripeSignatureHeader,
            stripeWebhookSecret,
            throwOnApiVersionMismatch: false);
    }

    private async Task HandleAccountUpdatedEvent(
        Account updatedAccount)
    {
        var paymentProviderConfig =
            await _paymentProviderConfigService.GetPaymentProviderConfigByStripeAccountIdAsync(updatedAccount.Id);

        if (updatedAccount.ChargesEnabled)
        {
            if (paymentProviderConfig.Status != "Registered")
            {
                var stripePaymentProviderExternalConfig =
                    (StripePaymentProviderExternalConfig) paymentProviderConfig.PaymentProviderExternalConfig;
                stripePaymentProviderExternalConfig.StripeAccount = updatedAccount;
                stripePaymentProviderExternalConfig.IsConnectedAccount = true;
                await _paymentProviderConfigService.PatchPaymentProviderConfigAsync(
                    paymentProviderConfig.Id,
                    paymentProviderConfig.SleekflowCompanyId,
                    stripePaymentProviderExternalConfig);

                await _paymentProviderConfigService.PatchPaymentProviderConfigAsync(
                    paymentProviderConfig.Id,
                    paymentProviderConfig.SleekflowCompanyId,
                    "Registered");
            }
        }
        else
        {
            if (paymentProviderConfig.Status != "Disabled")
            {
                await _paymentProviderConfigService.PatchPaymentProviderConfigAsync(
                    paymentProviderConfig.Id,
                    paymentProviderConfig.SleekflowCompanyId,
                    "Disabled");
            }
        }
    }

    private async Task HandleAccountApplicationDeauthorizedEvent(
        Account deauthorizedAccount)
    {
        var paymentProviderConfig =
            await _paymentProviderConfigService.GetPaymentProviderConfigByStripeAccountIdAsync(deauthorizedAccount.Id);

        await _paymentProviderConfigService.DeletePaymentProviderConfigAsync(
            paymentProviderConfig.Id,
            paymentProviderConfig.SleekflowCompanyId);
    }

    private async Task HandlePaymentIntentSucceededEvent(
        PaymentIntent paymentIntent)
    {
        var orderId = paymentIntent.Metadata["orderId"];
        var sleekflowCompanyId = paymentIntent.Metadata["companyId"];

        var payment = await _paymentService.GetAsync(
            orderId,
            sleekflowCompanyId);

        payment.ProcessPaymentContext!.ProviderPaymentId = paymentIntent.Id;

        await _orderService.PatchAndGetOrderAsync(
            orderId,
            sleekflowCompanyId,
            OrderStatuses.PaymentSucceeded,
            PaymentStatuses.Paid);

        await _paymentService.PatchAndGetPaymentAsync(
            payment.Id,
            sleekflowCompanyId,
            PaymentStatuses.Paid,
            payment.ProcessPaymentContext);
    }

    private async Task HandlePaymentIntentPaymentFailedEvent(
        PaymentIntent paymentIntent)
    {
        var orderId = paymentIntent.Metadata["orderId"];
        var sleekflowCompanyId = paymentIntent.Metadata["companyId"];

        var payment = await _paymentService.GetAsync(
            orderId,
            sleekflowCompanyId);

        await _orderService.PatchAndGetOrderAsync(
            orderId,
            sleekflowCompanyId,
            OrderStatuses.PaymentFailed,
            PaymentStatuses.PaymentFailed);

        await _paymentService.PatchAndGetPaymentAsync(
            payment.Id,
            sleekflowCompanyId,
            PaymentStatuses.PaymentFailed);
    }

    private async Task HandleChargeRefundUpdated(
        Refund refund)
    {
        var orderId = refund.Metadata["orderId"];
        var sleekflowCompanyId = refund.Metadata["companyId"];

        var payment = await _paymentService.GetAsync(
            orderId,
            sleekflowCompanyId);

        var processRefundContext =
            payment.ProcessRefundContexts?.Find(c => c.ProviderRefundId == refund.Id);

        if (processRefundContext == null)
        {
            throw new Exception($"Refund with id {refund.Id} can't be processed by SleekFlow");
        }

        var stripeProcessRefundContext = (StripeProcessRefundContext) processRefundContext;

        if (stripeProcessRefundContext.Status != refund.Status)
        {
            if (refund.Status == "succeeded")
            {
                var order = await _orderService.GetAsync(orderId, sleekflowCompanyId);

                var refundedAmount = payment.ProcessRefundContexts?.Where(c => c.Status == "succeeded")
                    .Sum(c => c.Amount);
                payment.PaymentStatus = order.TotalPrice - refundedAmount > 0
                    ? PaymentStatuses.PartiallyRefunded
                    : PaymentStatuses.FullyRefunded;
            }
            else
            {
                switch (processRefundContext.Status)
                {
                    case "failed":
                        payment.PaymentStatus = PaymentStatuses.RefundFailed;
                        break;
                    case "pending":
                        payment.PaymentStatus = PaymentStatuses.RefundPending;
                        break;
                    default:
                        throw new NotImplementedException();
                }
            }

            stripeProcessRefundContext.Refund = refund;
            stripeProcessRefundContext.Status = refund.Status;

            var indexOfContextToUpdate = payment.ProcessRefundContexts.FindIndex(c => c.ProviderRefundId == refund.Id);

            payment.ProcessRefundContexts[indexOfContextToUpdate] = stripeProcessRefundContext;

            await _paymentService.PatchAndGetPaymentAsync(
                payment.Id,
                payment.SleekflowCompanyId,
                payment.PaymentStatus,
                payment.ProcessRefundContexts);
        }
    }
}