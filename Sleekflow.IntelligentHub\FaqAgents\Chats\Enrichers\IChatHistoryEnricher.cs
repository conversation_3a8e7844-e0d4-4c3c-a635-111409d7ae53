using Microsoft.SemanticKernel;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Interface for chat history enrichers that add external data to agent conversations.
/// </summary>
public interface IChatHistoryEnricher
{
    /// <summary>
    /// Enriches the chat context with external data.
    /// </summary>
    /// <param name="context">The reply generation context.</param>
    /// <param name="contactProperties">Contact properties from the chat.</param>
    /// <param name="kernel">The semantic kernel instance.</param>
    /// <param name="cancellationToken">Cancellation token.</param>
    /// <returns>Formatted string with enriched data or error message.</returns>
    Task<string> EnrichAsync(
        ReplyGenerationContext context,
        Dictionary<string, string>? contactProperties,
        Kernel kernel,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets the name of the section in the context message.
    /// </summary>
    /// <returns>Section name to be used in the context message.</returns>
    string GetContextSectionName();

    string GetContextSectionExplanation();
}