using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetCustomObjectTypes : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public GetCustomObjectTypes(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class GetCustomObjectTypesInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetCustomObjectTypesInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class GetCustomObjectTypesOutput
    {
        [JsonProperty("custom_object_types")]
        [Required]
        public List<CustomObjectType> CustomObjectTypes { get; set; }

        [JsonConstructor]
        public GetCustomObjectTypesOutput(
            List<CustomObjectType> customObjectTypes)
        {
            CustomObjectTypes = customObjectTypes;
        }
    }

    public async Task<GetCustomObjectTypesOutput> F(GetCustomObjectTypesInput getCustomObjectTypesInput)
    {
        var connection =
            await _salesforceConnectionService.GetByIdAsync(
                getCustomObjectTypesInput.ConnectionId,
                getCustomObjectTypesInput.SleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getCustomObjectTypesInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        return new GetCustomObjectTypesOutput(
            await _salesforceObjectService.GetCustomObjectTypes(authentication));
    }
}