﻿using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Orders;

public interface IOrderRepository : IDynamicFiltersRepository<Order>
{
    Task<List<Order>> GetUserOrdersAsync(string sleekflowCompanyId, string sleekflowUserProfileId);
}

public class OrderRepository : DynamicFiltersBaseRepository<Order>, IOrderRepository, IScopedService
{
    public OrderRepository(
        ILogger<OrderRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }

    public async Task<List<Order>> GetUserOrdersAsync(string sleekflowCompanyId, string sleekflowUserProfileId)
    {
        return await GetObjectsAsync(
            p =>
                p.SleekflowCompanyId == sleekflowCompanyId
                && p.SleekflowUserProfileId == sleekflowUserProfileId);
    }
}