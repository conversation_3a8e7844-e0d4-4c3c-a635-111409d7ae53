using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Stores;

public class StoreIntegrationExternalConfigInput
{
    [Required]
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [ValidateObject]
    [JsonProperty("shopify_config")]
    public ShopifyStoreIntegrationExternalConfig? ShopifyConfig { get; set; }

    [JsonConstructor]
    public StoreIntegrationExternalConfigInput(
        string providerName,
        ShopifyStoreIntegrationExternalConfig? shopifyConfig = null)
    {
        ProviderName = providerName;
        ShopifyConfig = shopifyConfig;
    }

    public StoreIntegrationExternalConfig? GetConfig()
    {
        if (ProviderName == "shopify")
        {
            return ShopifyConfig;
        }

        return null;
    }
}