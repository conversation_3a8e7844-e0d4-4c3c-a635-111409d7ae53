using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Salesforce;

[TriggerGroup(TriggerGroups.InflowActions)]
public class CreateSalesforceObject : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public CreateSalesforceObject(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class CreateSalesforceObjectInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("dict")]
        [ValidateObject]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonConstructor]
        public CreateSalesforceObjectInput(
            string sleekflowCompanyId,
            string salesforceConnectionId,
            string entityTypeName,
            Dictionary<string, object?> dict)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SalesforceConnectionId = salesforceConnectionId;
            EntityTypeName = entityTypeName;
            Dict = dict;
        }
    }

    public class CreateSalesforceObjectOutput
    {
    }

    public async Task<CreateSalesforceObjectOutput> F(
        CreateSalesforceObjectInput createSalesforceObjectInput)
    {
        var salesforceProviderService = _providerSelector.GetProviderService(
            "salesforce-integrator");

        await salesforceProviderService.CreateObjectV2Async(
            createSalesforceObjectInput.SleekflowCompanyId,
            createSalesforceObjectInput.SalesforceConnectionId,
            createSalesforceObjectInput.Dict,
            createSalesforceObjectInput.EntityTypeName);

        return new CreateSalesforceObjectOutput();
    }
}