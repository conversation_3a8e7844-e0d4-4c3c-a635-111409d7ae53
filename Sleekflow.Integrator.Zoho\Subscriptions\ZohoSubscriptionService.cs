﻿using MassTransit.Testing;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Zoho.Subscriptions;

public interface IZohoSubscriptionService
{
    Task ClearAsync(string entityTypeName, string sleekflowCompanyId);

    Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId);

    Task UpsertAsync(
        string entityTypeName,
        string sleekflowCompanyId,
        int interval,
        string connectionId,
        bool isFlowsBased);

    Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId);

    Task<List<ZohoSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName);
}

public class ZohoSubscriptionService : IZohoSubscriptionService, ISingletonService
{
    private readonly IZohoSubscriptionRepository _zohoSubscriptionRepository;
    private readonly IIdService _idService;

    public ZohoSubscriptionService(
        IZohoSubscriptionRepository zohoSubscriptionRepository,
        IIdService idService)
    {
        _zohoSubscriptionRepository = zohoSubscriptionRepository;
        _idService = idService;
    }

    public async Task ClearAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _zohoSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == ZohoSubscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _zohoSubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }

    public async Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId)
    {
        await foreach (var subscription in _zohoSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                                s.SysTypeName == ZohoSubscription.SysTypeNameValue
                                && s.ConnectionId == connectionId
                                && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _zohoSubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }

    public async Task UpsertAsync(
        string entityTypeName,
        string sleekflowCompanyId,
        int interval,
        string connectionId,
        bool isFlowsBased)
    {
        var subscription = await _zohoSubscriptionRepository.GetObjectEnumerableAsync(
                s =>
                    s.EntityTypeName == entityTypeName
                    && s.ConnectionId == connectionId
                    && s.SysTypeName == ZohoSubscription.SysTypeNameValue
                    && s.SleekflowCompanyId == sleekflowCompanyId)
            .FirstOrDefault();
        if (subscription != null)
        {
            subscription.Interval = interval;

            await _zohoSubscriptionRepository.ReplaceAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                subscription);

            return;
        }

        var createCount = await _zohoSubscriptionRepository.CreateAsync(
            new ZohoSubscription(
                _idService.GetId(ZohoSubscription.SysTypeNameValue),
                sleekflowCompanyId,
                entityTypeName,
                interval,
                DateTimeOffset.UtcNow,
                null,
                null,
                isFlowsBased,
                connectionId),
            sleekflowCompanyId);
        if (createCount == 0)
        {
            throw new SfUserFriendlyException("Unable to init the type");
        }
    }

    public async Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _zohoSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == ZohoSubscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _zohoSubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{ZohoSubscription.PropertyNameDurablePayload}",
                        new object()),
                });
        }
    }

    public async Task<List<ZohoSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName)
    {
        return await _zohoSubscriptionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId
                 && x.ConnectionId == connectionId
                 && x.EntityTypeName == entityTypeName);
    }
}