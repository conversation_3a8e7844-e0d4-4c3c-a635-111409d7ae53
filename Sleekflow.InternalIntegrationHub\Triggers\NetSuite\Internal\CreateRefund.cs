using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.NetSuite;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class CreateRefund : ITrigger<CreateRefund.CreateRefundInput, CreateRefund.CreateRefundOutput>
{
    private readonly INetSuiteCustomerService _netSuiteCustomerService;

    public CreateRefund(INetSuiteCustomerService netSuiteCustomerService)
    {
        _netSuiteCustomerService = netSuiteCustomerService;
    }

    public class CreateRefundInput
    {
        [JsonConstructor]
        public CreateRefundInput(string companyId, decimal amount, string creditMemoExternalId)
        {
            CompanyId = companyId;
            Amount = amount;
            CreditMemoExternalId = creditMemoExternalId;
        }

        [Required]
        [JsonProperty("company_id")]
        public string CompanyId { get; set; }

        [Required]
        [JsonProperty("amount")]
        [Range(0, double.MaxValue)]
        public decimal Amount { get; set; }

        [JsonProperty("credit_memo_external_id")]
        [Required]
        public string CreditMemoExternalId { get; set; }
    }

    public class CreateRefundOutput
    {
    }

    public async Task<CreateRefundOutput> F(CreateRefundInput input)
    {
        var response = await _netSuiteCustomerService.CreateRefundAsync(input);
        if (response == false)
        {
            throw new Exception("Failed to create payment");
        }

        return new CreateRefundOutput();
    }
}