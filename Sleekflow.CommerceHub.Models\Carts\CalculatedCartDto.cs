using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Discounts;
using Sleekflow.CommerceHub.Models.LineItems;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Carts;

public class CalculatedCartDto : CartDto
{
    [JsonProperty("calculated_line_items")]
    public List<CalculatedLineItem> CalculatedLineItems { get; set; }

    [JsonProperty("subtotal_price")]
    public decimal SubtotalPrice { get; set; }

    [JsonProperty("total_price")]
    public decimal TotalPrice { get; set; }

    [JsonConstructor]
    public CalculatedCartDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string storeId,
        List<CartLineItemDto> lineItems,
        Discount? cartDiscount,
        string cartStatus,
        CartExternalIntegrationInfo? cartExternalIntegrationInfo,
        List<CalculatedLineItem> calculatedLineItems,
        decimal subtotalPrice,
        decimal totalPrice)
        : base(
            id,
            sleekflowCompanyId,
            createdBy,
            updatedBy,
            createdAt,
            updatedAt,
            storeId,
            lineItems,
            cartDiscount,
            cartStatus)
    {
        CalculatedLineItems = calculatedLineItems;
        SubtotalPrice = subtotalPrice;
        TotalPrice = totalPrice;
    }

    public CalculatedCartDto(
        Cart cart,
        Dictionary<string, ProductVariant> idToProductVariantDict,
        Dictionary<string, Product> idToProductDict,
        List<CalculatedLineItem> calculatedLineItems,
        decimal subtotalPrice,
        decimal totalPrice)
        : base(cart, idToProductVariantDict, idToProductDict)
    {
        CalculatedLineItems = calculatedLineItems;
        SubtotalPrice = subtotalPrice;
        TotalPrice = totalPrice;
    }
}