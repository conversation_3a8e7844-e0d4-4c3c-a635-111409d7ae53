using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnMetaDetectedOutcomeReceivedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnMetaDetectedOutcomeReceived; }
    }

    [Required]
    [JsonProperty("detected_outcome")]
    public OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome DetectedOutcome { get; set; }

    [JsonConstructor]
    public OnMetaDetectedOutcomeReceivedEventBody(
        DateTimeOffset createdAt,
        OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome detectedOutcome)
        : base(createdAt)
    {
        DetectedOutcome = detectedOutcome;
    }
}

public class OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("event_name")]
    public string EventName { get; set; }

    [JsonProperty("timestamp")]
    public long Timestamp { get; set; }

    [JsonProperty("ctwa_clid")]
    public string CtwaClid { get; set; }

    [JsonProperty("custom_data")]
    public CustomData CustomData { get; set; }

    [JsonConstructor]
    public OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome(
        string id,
        string eventName,
        long timestamp,
        string ctwaClid,
        CustomData customData)
    {
        Id = id;
        EventName = eventName;
        Timestamp = timestamp;
        CtwaClid = ctwaClid;
        CustomData = customData;
    }
}

public class CustomData
{
    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonProperty("value")]
    public decimal Value { get; set; }

    [JsonConstructor]
    public CustomData(
        string currency,
        decimal value)
    {
        Currency = currency;
        Value = value;
    }
}