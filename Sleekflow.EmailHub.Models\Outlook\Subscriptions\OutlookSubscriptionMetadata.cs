using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Subscriptions;

namespace Sleekflow.EmailHub.Models.Outlook.Subscriptions;

public class OutlookSubscriptionMetadata : EmailSubscriptionMetadata
{
    [JsonProperty("client_state")]
    public string ClientState { get; set; }

    [JsonProperty("subscription_id")]
    public string? SubscriptionId { get; set; }

    [JsonProperty("application_id")]
    public string? ApplicationId { get; set; }

    [JsonProperty("expiration_date_time")]
    public DateTimeOffset? ExpirationDateTime { get; set; }

    [JsonProperty("delta_link")]
    public string? DeltaLink { get; set; }

    [JsonProperty("inbox_folder_id")]
    public string InboxFolderId { get; set; }

    [JsonConstructor]
    public OutlookSubscriptionMetadata(
        string clientState,
        string? subscriptionId,
        string? applicationId,
        DateTimeOffset? expirationDateTime,
        string inboxFolderId,
        string? deltaLink = null)
        : base(ProviderNames.Outlook, ProviderNames.Outlook)
    {
        ClientState = clientState;
        SubscriptionId = subscriptionId;
        ApplicationId = applicationId;
        ExpirationDateTime = expirationDateTime;
        InboxFolderId = inboxFolderId;
        DeltaLink = deltaLink;
    }
}