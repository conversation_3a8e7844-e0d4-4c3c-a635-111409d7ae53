﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateSalesforceObjectStepExecutor : IStepExecutor
{
}

public class UpdateSalesforceObjectStepExecutor
    : GeneralStepExecutor<CallStep<UpdateSalesforceObjectStepArgs>>,
        IUpdateSalesforceObjectStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IServiceBusManager _serviceBusManager;

    public UpdateSalesforceObjectStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IServiceBusManager serviceBusManager)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _serviceBusManager = serviceBusManager;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var updateSalesforceObjectInput = await GetArgs(callStep, state);

            await _serviceBusManager.PublishAsync(
                new UpdateSalesforceObjectRequest(
                    step.Id,
                    state.Id,
                    stackEntries,
                    updateSalesforceObjectInput.StateIdentity.SleekflowCompanyId,
                    updateSalesforceObjectInput.SalesforceConnectionId,
                    updateSalesforceObjectInput.EntityTypeName,
                    updateSalesforceObjectInput.IsCustomObject,
                    updateSalesforceObjectInput.ObjectId,
                    updateSalesforceObjectInput.ObjectProperties));

            // Schedule a failover event to fire after 5 minutes if no completion arrives
            await _serviceBusManager.PublishAsync(
                new OnSalesforceFailStepActivationEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    null,
                    new TimeoutException("Salesforce update object operation timed out after 5 minutes")),
                typeof(OnSalesforceFailStepActivationEvent),
                publishContext => publishContext.Delay = TimeSpan.FromMinutes(5));
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateSalesforceObjectInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("salesforce_connection_id")]
        [Required]
        public string SalesforceConnectionId { get; set; }

        [JsonProperty("object_id")]
        [Required]
        public string ObjectId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("is_custom_object")]
        [Required]
        public bool IsCustomObject { get; set; }

        [JsonProperty("object_properties")]
        [Required]
        [Validations.ValidateObject]
        public Dictionary<string, object?> ObjectProperties { get; set; }

        [JsonConstructor]
        public UpdateSalesforceObjectInput(
            string stateId,
            StateIdentity stateIdentity,
            string salesforceConnectionId,
            string objectId,
            string entityTypeName,
            bool isCustomObject,
            Dictionary<string, object?> objectProperties)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            SalesforceConnectionId = salesforceConnectionId;
            ObjectId = objectId;
            EntityTypeName = entityTypeName;
            IsCustomObject = isCustomObject;
            ObjectProperties = objectProperties;
        }
    }

    private async Task<UpdateSalesforceObjectInput> GetArgs(
        CallStep<UpdateSalesforceObjectStepArgs> callStep,
        ProxyState state)
    {
        var salesforceConnectionId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.SalesforceConnectionIdExpr)
                        ?? callStep.Args.SalesforceConnectionIdExpr);

        var objectId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ObjectIdExpr)
                      ?? callStep.Args.ObjectIdExpr);

        var objectType =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ObjectTypeExpr)
                        ?? callStep.Args.ObjectTypeExpr);

        var isCustomObjectStr =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.IsCustomObjectExpr)
                      ?? callStep.Args.IsCustomObjectExpr);

        var isCustomObject = bool.Parse(isCustomObjectStr);

        var objectProperties =
            await _stateEvaluator.EvaluateDictExpressionAsync(state, callStep.Args.ObjectPropertiesExprDict);

        return new UpdateSalesforceObjectInput(
            state.Id,
            state.Identity,
            salesforceConnectionId,
            objectId,
            objectType,
            isCustomObject,
            objectProperties);
    }
}