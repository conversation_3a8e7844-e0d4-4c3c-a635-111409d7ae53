using MassTransit;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiBusinessBalanceTransactionLogResynchronizationEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiBusinessBalanceTransactionLogResynchronizationEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiBusinessBalanceTransactionLogResynchronizationEventConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiBusinessBalanceTransactionLogResynchronizationEventConsumer
    : IConsumer<OnCloudApiBusinessBalanceTransactionLogResynchronizationEvent>
{
    private readonly IBus _bus;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<OnCloudApiBusinessBalanceTransactionLogResynchronizationEventConsumer> _logger;

    public OnCloudApiBusinessBalanceTransactionLogResynchronizationEventConsumer(
        IBus bus,
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        ILogger<OnCloudApiBusinessBalanceTransactionLogResynchronizationEventConsumer> logger)
    {
        _bus = bus;
        _logger = logger;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiBusinessBalanceTransactionLogResynchronizationEvent> context)
    {
        var onCloudApiBusinessTransactionLogRevalidateEvent = context.Message;
        var createAt = new DateTimeOffset(onCloudApiBusinessTransactionLogRevalidateEvent.CreateAt.Date);

        // Resync business transaction log event for the past 30 days
        var lastConversationUsageInsertTimestamp = createAt.AddDays(-30).ToUnixTimeSeconds();
        var latestConversationUsageInsertTimestamp = createAt.ToUnixTimeSeconds();
        foreach (var businessBalance in await _businessBalanceService.GetAllAsync())
        {
            foreach (var waba in await _wabaService.GetWabaWithFacebookBusinessIdAsync(
                         businessBalance.FacebookBusinessId))
            {
                await _bus.Publish(
                    new OnCloudApiHalfHourConversationUsageTransactionResynchronizationEvent(
                        waba.FacebookWabaId,
                        businessBalance.FacebookBusinessId,
                        lastConversationUsageInsertTimestamp,
                        latestConversationUsageInsertTimestamp));
            }
        }
    }
}