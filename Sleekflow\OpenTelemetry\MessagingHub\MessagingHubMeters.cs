using System.Diagnostics.Metrics;
using Microsoft.Extensions.Logging;
using Sleekflow.OpenTelemetry.MessagingHub.MeterNames;
using Sleekflow.OpenTelemetry.Meters;

namespace Sleekflow.OpenTelemetry.MessagingHub;

public interface IMessagingHubMeters : IBasicMeters
{
}

public static class MessagingHubWebhookMeterOptions
{
    public const string WebhookReceived = "webhook_received";
    public const string MessagesWebhookReceived = "messages_webhook_received";
    public const string MessagesWebhookDropped = "messages_webhook_dropped";
    public const string MessagesWebhookDelivered = "messages_webhook_delivered";
}

public class MessagingHubMeters : BaseMeters, IMessagingHubMeters
{
    private const string MessagingWebhookMeterName = "messaging_hub_meters.trigger_type";
    private readonly Dictionary<string, Counter<int>>? _webhookCounters;

    private readonly List<string> _webhookOptionsParams = new ()
    {
        MessagingHubWebhookMeterOptions.WebhookReceived,
        MessagingHubWebhookMeterOptions.MessagesWebhookReceived,
        MessagingHubWebhookMeterOptions.MessagesWebhookDropped,
        MessagingHubWebhookMeterOptions.MessagesWebhookDelivered,
    };

    public MessagingHubMeters(
        IMeterFactory meterFactory,
        ILogger<MessagingHubMeters> logger)
        : base(meterFactory, logger)
    {
        if (!IsMeterEnabled)
        {
            return;
        }

        _webhookCounters = _webhookOptionsParams.SelectMany(
                o => _webhookTypes.Select(
                    c => (Key: GetComposeKey(c.Key, o), Value: GetComposeKey(c.Value, o))))
            .ToDictionary(
                k => k.Key,
                k => CreateCounter<int>($"{MessagingWebhookMeterName}.{k.Value}"));
    }

    protected override Counter<int> GetCounter<T>(T name, string? option = null)
    {
        if (name is string triggerType &&
            _webhookCounters!.TryGetValue(GetComposeKey(triggerType, option), out var counter))
        {
            return counter;
        }

        throw new NotImplementedException();
    }

    private readonly Dictionary<string, string> _webhookTypes = new ()
    {
        {
            MessagingHubChannelMeterNames.WhatsappCloudApi, "whatsapp_cloud_api"
        }
    };
}