﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateSchemafulObjectV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.create-schemaful-object";
    public const string MandatoryPropertiesName = "mandatory_properties__id_expr_dict";
    public const string OptionalPropertiesName = "optional_properties__id_expr_dict";
    public const string PrimaryPropertyValueName = "primary_property_value__expr";

    [Required]
    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    [Required]
    [JsonProperty("contact_number__expr")]
    public string ContactNumberExpr { get; set; }

    [JsonProperty(PrimaryPropertyValueName)]
    public string? PrimaryPropertyValueExpr { get; set; }

    [JsonProperty(MandatoryPropertiesName)]
    public Dictionary<string, string>? MandatoryPropertiesIdExprDict { get; set; }

    [JsonProperty(OptionalPropertiesName)]
    public Dictionary<string, string?>? OptionalPropertiesIdExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.CustomObject;

    [JsonConstructor]
    public CreateSchemafulObjectV2StepArgs(
        string schemaId,
        string? primaryPropertyValueExpr,
        Dictionary<string, string>? mandatoryPropertiesIdExprDict,
        Dictionary<string, string?>? optionalPropertiesIdExprDict)
    {
        SchemaId = schemaId;
        PrimaryPropertyValueExpr = primaryPropertyValueExpr;
        MandatoryPropertiesIdExprDict = mandatoryPropertiesIdExprDict;
        OptionalPropertiesIdExprDict = optionalPropertiesIdExprDict;
    }
}