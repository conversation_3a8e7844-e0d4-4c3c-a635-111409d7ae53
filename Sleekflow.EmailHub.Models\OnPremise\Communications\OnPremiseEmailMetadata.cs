using MimeKit;
using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Communications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.OnPremise.Communications;

public class OnPremiseEmailMetadata : EmailMetadata
{
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("priority")]
    public MessagePriority Priority { get; set; }

    [JsonProperty("importance")]
    public MessageImportance Importance { get; set; }

    [JsonProperty("x-priority")]
    public XMessagePriority XMessagePriority { get; set; }

    [JsonConstructor]
    public OnPremiseEmailMetadata(
        string messageId,
        MessagePriority priority,
        MessageImportance importance,
        XMessagePriority xMessagePriority,
        DateTimeOffset sentAt)
        : base(ProviderNames.OnPremise, ProviderNames.OnPremise, sentAt)
    {
        MessageId = messageId;
        Priority = priority;
        Importance = importance;
        XMessagePriority = xMessagePriority;
    }
}