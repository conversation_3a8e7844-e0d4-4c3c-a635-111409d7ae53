using System;

namespace Sleekflow.Exceptions.TenantHub;

/// <summary>
/// Exception thrown when attempting to create a user with an ID that already exists
/// </summary>
public class SfDuplicateUserException : ErrorCodeException
{
    /// <summary>
    /// Initializes a new instance of the <see cref="SfDuplicateUserException"/> class
    /// </summary>
    /// <param name="userId">The ID of the user that already exists</param>
    public SfDuplicateUserException(string userId)
        : base(
            ErrorCodeConstant.SfDuplicateUserException,
            $"User with ID {userId} already exists")
    {
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="SfDuplicateUserException"/> class with an inner exception
    /// </summary>
    /// <param name="userId">The ID of the user that already exists</param>
    /// <param name="innerException">The inner exception that caused this exception</param>
    public SfDuplicateUserException(string userId, Exception innerException)
        : base(
            ErrorCodeConstant.SfDuplicateUserException,
            $"User with ID {userId} already exists",
            innerException)
    {
    }
}
