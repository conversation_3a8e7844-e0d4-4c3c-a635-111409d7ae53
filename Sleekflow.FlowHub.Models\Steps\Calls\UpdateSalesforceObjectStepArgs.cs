﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateSalesforceObjectStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-salesforce-object";

    [Required]
    [JsonProperty("salesforce_connection_id__expr")]
    public string SalesforceConnectionIdExpr { get; set; }

    [Required]
    [JsonProperty("object_id__expr")]
    public string ObjectIdExpr { get; set; }

    [Required]
    [JsonProperty("object_type__expr")]
    public string ObjectTypeExpr { get; set; }

    [Required]
    [JsonProperty("is_custom_object__expr")]
    public string IsCustomObjectExpr { get; set; }

    [Required]
    [JsonProperty("object_properties__expr_dict")]
    public Dictionary<string, string?> ObjectPropertiesExprDict { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.SalesforceIntegration;

    [JsonConstructor]
    public UpdateSalesforceObjectStepArgs(
        string salesforceConnectionIdExpr,
        string objectIdExpr,
        string objectTypeExpr,
        string isCustomObjectExpr,
        Dictionary<string, string?> objectPropertiesExprDict)
    {
        SalesforceConnectionIdExpr = salesforceConnectionIdExpr;
        ObjectIdExpr = objectIdExpr;
        ObjectTypeExpr = objectTypeExpr;
        IsCustomObjectExpr = isCustomObjectExpr;
        ObjectPropertiesExprDict = objectPropertiesExprDict;
    }
}