using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.BlobUploadHistory)]
public class BlobUploadHistory : Entity, IHasSleekflowCompanyId, IHasCreatedAt, IHasUpdatedAt
{
    public const string PropertyNameBlobId = "blob_id";
    public const string PropertyNameFileName = "file_name";
    public const string PropertyNameUploadedBy = "uploaded_by";
    public const string PropertyNameBlobType = "blob_type";
    public const string PropertyNameSourceType = "source_type";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameBlobId)]
    public string BlobId { get; set; }

    [JsonProperty(PropertyNameFileName)]
    public string FileName { get; set; }

    [JsonProperty(PropertyNameUploadedBy)]
    public string UploadedBy { get; set; }

    [JsonProperty(PropertyNameSourceType)]
    public string SourceType { get; set; }

    [JsonProperty(PropertyNameBlobType)]
    public string BlobType { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }


    [JsonConstructor]
    public BlobUploadHistory(
        string id,
        string sleekflowCompanyId,
        string blobId,
        string fileName,
        string uploadedBy,
        string sourceType,
        string blobType,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, SysTypeNames.BlobUploadHistory)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        BlobId = blobId;
        FileName = fileName;
        UploadedBy = uploadedBy;
        SourceType = sourceType;
        BlobType = blobType;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}