using Sleekflow.Persistence.Abstractions;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits;
using Sleekflow.MessagingHub.Models.Constants;

namespace Sleekflow.MessagingHub.Triggers.AuditLogs;

[TriggerGroup(ControllerNames.AuditLogs)]
public class CreateWabaPhoneNumberStatusChangedAuditLog(IAuditLogService auditLogService)
    : ITrigger<CreateWabaPhoneNumberStatusChangedAuditLog.CreateWabaPhoneNumberStatusChangedAuditLogInput,
        CreateWabaPhoneNumberStatusChangedAuditLog.CreateWabaPhoneNumberStatusChangedAuditLogOutput>
{
    public class CreateWabaPhoneNumberStatusChangedAuditLogInput(
        string sleekflowCompanyId,
        string companyName,
        string facebookWabaId,
        string phoneNumber,
        string status) : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; } = sleekflowCompanyId;

        [Required]
        [JsonProperty("company_name")]
        public string CompanyName { get; set; } = companyName;

        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; } = facebookWabaId;

        [Required]
        [JsonProperty("phone_number")]
        public string PhoneNumber { get; set; } = phoneNumber;

        [Required]
        [JsonProperty("status")]
        public string Status { get; set; } = status;
    }

    public class CreateWabaPhoneNumberStatusChangedAuditLogOutput(AuditLog? auditLog)
    {
        [JsonProperty("audit_log")]
        public AuditLog? AuditLog { get; set; } = auditLog;
    }

    public async Task<CreateWabaPhoneNumberStatusChangedAuditLogOutput> F(CreateWabaPhoneNumberStatusChangedAuditLogInput input)
    {
        return await auditLogService.AuditWabaPhoneNumberStatusChangedAsync(input);
    }
}