using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Categories;
using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Categories;

[TriggerGroup(ControllerNames.Categories)]
public class GetCategories : ITrigger<GetCategories.GetCategoriesInput, GetCategories.GetCategoriesOutput>
{
    private readonly ICategoryService _categoryService;

    public GetCategories(ICategoryService categoryService)
    {
        _categoryService = categoryService;
    }

    public class GetCategoriesInput
    {
        [StringLength(16384, MinimumLength = 1)]
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetCategoriesInput(
            string? continuationToken,
            string sleekflowCompanyId,
            string storeId,
            int limit)
        {
            ContinuationToken = continuationToken;
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            Limit = limit;
        }
    }

    public class GetCategoriesOutput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("categories")]
        public List<CategoryDto> Categories { get; set; }

        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonConstructor]
        public GetCategoriesOutput(
            string? continuationToken,
            List<CategoryDto> categories,
            int count)
        {
            ContinuationToken = continuationToken;
            Categories = categories;
            Count = count;
        }
    }

    public async Task<GetCategoriesOutput> F(GetCategoriesInput getCategoriesInput)
    {
        var (categories, nextContinuationToken) = await _categoryService.GetCategoriesAsync(
            getCategoriesInput.ContinuationToken,
            getCategoriesInput.SleekflowCompanyId,
            getCategoriesInput.StoreId,
            getCategoriesInput.Limit);

        return new GetCategoriesOutput(
            nextContinuationToken,
            categories.Select(c => new CategoryDto(c)).ToList(),
            categories.Count);
    }
}