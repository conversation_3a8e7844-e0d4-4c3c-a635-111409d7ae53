using System.ComponentModel.DataAnnotations;
using System.Globalization;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.TenantHub;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Workers.Services;
using Sleekflow.Models.Crm;
using Sleekflow.Validations;

using Sleekflow.Models.Crm;
namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class CheckEligibilityAndScheduleByBatch
{
    private readonly IContactBatchService _contactBatchService;
    private readonly IContactEligibilityService _contactEligibilityService;
    private readonly IContactEnrollmentService _contactEnrollmentService;
    private readonly ILogger<CheckEligibilityAndScheduleByBatch> _logger;
    private readonly IGetPropertyValueService _getPropertyValueService;

    public CheckEligibilityAndScheduleByBatch(
        IContactBatchService contactBatchService,
        IContactEligibilityService contactEligibilityService,
        IContactEnrollmentService contactEnrollmentService,
        ILogger<CheckEligibilityAndScheduleByBatch> logger,
        IGetPropertyValueService getPropertyValueService)
    {
        _contactBatchService = contactBatchService;
        _contactEligibilityService = contactEligibilityService;
        _contactEnrollmentService = contactEnrollmentService;
        _logger = logger;
        _getPropertyValueService = getPropertyValueService;
    }

    public class CheckEligibilityAndScheduleByBatchInput
        : ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchInput
    {
        [JsonConstructor]
        public CheckEligibilityAndScheduleByBatchInput(
            string origin,
            string sleekflowCompanyId,
            DateTimeOffset? lastContactCreatedAt,
            string? lastContactId,
            int? batchSize,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string condition,
            ContactPropertyDateTimeSettings contactPropertyDateTimeSettings,
            CustomObjectDateTimeSettings customObjectDateTimeSettings,
            string scheduledType,
            WorkflowRecurringSettings workflowRecurringSettings,
            DateTimeOffset currentTime
        )
            : base(
                origin,
                sleekflowCompanyId,
                lastContactCreatedAt,
                lastContactId,
                batchSize,
                workflowId,
                workflowVersionedId,
                workflowName,
                condition)
        {
            ContactPropertyDateTimeSettings = contactPropertyDateTimeSettings;
            CustomObjectDateTimeSettings = customObjectDateTimeSettings;
            ScheduledType = scheduledType;
            WorkflowRecurringSettings = workflowRecurringSettings;
            CurrentTime = currentTime;
        }

        [JsonProperty("contact_property_date_time_settings")]
        public ContactPropertyDateTimeSettings ContactPropertyDateTimeSettings { get; set; }

        [JsonProperty("custom_object_date_time_settings")]
        public CustomObjectDateTimeSettings CustomObjectDateTimeSettings { get; set; }

        [JsonProperty("scheduled_type")]
        [Required]
        public string ScheduledType { get; set; }

        [JsonProperty("workflow_recurring_settings")]
        public WorkflowRecurringSettings WorkflowRecurringSettings { get; set; }

        [JsonProperty("current_time")]
        [Required]
        public DateTimeOffset CurrentTime { get; set; }
    }

    public class CheckEligibilityAndScheduleByBatchOutput
        : ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchOutput
    {
        [JsonConstructor]
        public CheckEligibilityAndScheduleByBatchOutput(
            DateTimeOffset? nextBatchLastContactCreatedAt,
            string? nextBatchLastContactId,
            int contactsInFetchedBatch,
            int contactsEnrolled)
            : base(nextBatchLastContactCreatedAt, nextBatchLastContactId, contactsInFetchedBatch, contactsEnrolled)
        {
        }
    }

    [Function("CheckEligibilityAndScheduleByBatch")]
    public async Task<CheckEligibilityAndScheduleByBatchOutput> RunAsync(
        [ActivityTrigger]
        CheckEligibilityAndScheduleByBatchInput input)
    {
        _logger.LogInformation("Starting ProcessContactsBatchActivity, input: {Input}", JsonConvert.SerializeObject(input));

        // Step 1: Get contacts by batch
        var contactsBatchResult = await _contactBatchService.GetContactsByBatchAsync(
            input.Origin,
            input.SleekflowCompanyId,
            input.LastContactCreatedAt,
            input.LastContactId,
            input.BatchSize,
            input.WorkflowVersionedId);

        var contactsInFetchedBatch = contactsBatchResult.Contacts?.Count ?? 0;
        _logger.LogInformation("Fetched {ContactsCount} contacts for batch processing.", contactsInFetchedBatch);

        // Step 2: Get property value or custom object value for each contact by contact id
        var contactDateTimeArray = await GetPropertyValues(input, contactsBatchResult);

        // Step 3: Check time and eligibility and enroll contacts
        var contactsEnrolled = 0;

        if (contactDateTimeArray != null && contactDateTimeArray.Length > 0 && contactsBatchResult.Contacts != null &&
            contactsBatchResult.Contacts.Count > 0)
        {
            contactsEnrolled = await _contactEligibilityService.CheckTimeAndEligibilityThenEnrollAsync(
                contactDateTimeArray,
                contactsBatchResult.Contacts,
                input.WorkflowId,
                input.WorkflowVersionedId,
                input.SleekflowCompanyId,
                input.Condition,
                input.WorkflowName,
                _contactEnrollmentService,
                input.ScheduledType == WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime ? input.CustomObjectDateTimeSettings as BaseDateTimeSettings : input.ContactPropertyDateTimeSettings as BaseDateTimeSettings,
                input.WorkflowRecurringSettings,
                input.CurrentTime,
                input.Origin);
        }

        _logger.LogInformation(
            "[Property Datetime] Processed batch for Company {CompanyId}, Workflow {WorkflowVersionedId}. Fetched: {FetchedCount}, Enrolled: {EnrolledCount}. Next cursor: CreatedAt={NextCreatedAt}, Id={NextId}",
            input.SleekflowCompanyId,
            input.WorkflowVersionedId,
            contactsInFetchedBatch,
            contactsEnrolled,
            contactsBatchResult.NextBatch?.LastContactCreatedAt,
            contactsBatchResult.NextBatch?.LastContactId);

        return new CheckEligibilityAndScheduleByBatchOutput(
            contactsBatchResult.NextBatch?.LastContactCreatedAt,
            contactsBatchResult.NextBatch?.LastContactId,
            contactsInFetchedBatch,
            contactsEnrolled);
    }

    public async Task<ContactDatetime[]?> GetPropertyValues(CheckEligibilityAndScheduleByBatchInput input,
        GetContactsByBatchOutput contactsBatchResult)
    {
        ContactDatetime?[] contactDateTimeArray = null;
        if (WorkflowScheduleTypes.ContactPropertyBasedDateTime.Equals(input.ScheduledType))
        {
            _logger.LogInformation("[CheckEligibilityAndScheduleByBatch] ContactPropertyDateTimeSettings: {ContactPropertyDateTimeSettings}", input.ContactPropertyDateTimeSettings);
            // GetPropertyValues
            if (input.ContactPropertyDateTimeSettings == null || string.IsNullOrEmpty(input.ContactPropertyDateTimeSettings.ContactPropertyId))
            {
                _logger.LogInformation("ContactPropertyDateTimeSettings is invalid. ContactPropertyDateTimeSettings: {ContactPropertyDateTimeSettings}", input.ContactPropertyDateTimeSettings);
                throw new SfInternalErrorException("Invalid ContactPropertyDateTimeSettings");
            }

            GetContactPropertyValueByContactIdsOutput getContactPropertyValueByContactIdsOutput = await _getPropertyValueService.GetContactPropertyValuesByContactIds(
                input.SleekflowCompanyId,
                input.ContactPropertyDateTimeSettings.ContactPropertyId!,
                contactsBatchResult!.Contacts,
                input.Origin);

            if (getContactPropertyValueByContactIdsOutput.PropertyValues == null) {
                _logger.LogWarning("PropertyValues is null");
                return [];
            }
            contactDateTimeArray = contactsBatchResult!.Contacts.Keys
                .Select(contactId =>
                {
                    string value = getContactPropertyValueByContactIdsOutput.PropertyValues![contactId];
                    if (string.IsNullOrEmpty(value))
                    {
                        return null;
                    }

                    try
                    {
                        var parsedDateTime = DateTimeOffset.Parse(value, null, DateTimeStyles.RoundtripKind);
                        return new ContactDatetime
                        {
                            ContactId = contactId,
                            DateTimeList = new[] { parsedDateTime }
                        };
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to parse date value {Value} for Property {PropertyId} contact {ContactId}: {Error}",
                            value, input.ContactPropertyDateTimeSettings.ContactPropertyId, contactId, ex.Message);
                        return null;
                    }
                })
                .Where(result => result != null)
                .ToArray();
        }
        else if (WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime.Equals(input.ScheduledType))
        {
            _logger.LogInformation("[CheckEligibilityAndScheduleByBatch] CustomObjectDateTimeSettings: {CustomObjectDateTimeSettings}", input.CustomObjectDateTimeSettings);

            if (input.CustomObjectDateTimeSettings == null || string.IsNullOrEmpty(input.CustomObjectDateTimeSettings.SchemaId) || string.IsNullOrEmpty(input.CustomObjectDateTimeSettings.SchemafulObjectPropertyId))
            {
                _logger.LogInformation("CustomObjectDateTimeSettings is invalid. ContactPropertyDateTimeSettings: {CustomObjectDateTimeSettings}", input.CustomObjectDateTimeSettings);
                throw new SfInternalErrorException("Invalid CustomObjectDateTimeSettings");
            }

            GetPropertyValuesByContactIdsOutput propertyValuesByContactIdsOutput = await _getPropertyValueService.GetCustomObjectPropertyValueByContactsId(
                input.SleekflowCompanyId,
                input.CustomObjectDateTimeSettings.SchemaId,
                input.CustomObjectDateTimeSettings.SchemafulObjectPropertyId,
                contactsBatchResult!.Contacts,
                input.Origin);

            if (propertyValuesByContactIdsOutput.PropertyValues == null) {
                _logger.LogWarning("PropertyValues is null");
                return [];
            }
            contactDateTimeArray = contactsBatchResult!.Contacts.Keys
                .Select(contactId =>
                {
                    var values = propertyValuesByContactIdsOutput.PropertyValues![contactId];
                    var dateTimeList = values
                        .Where(v => v != null && !string.IsNullOrEmpty(v.ToString()))
                        .Select(v =>
                        {
                            try
                            {
                                return DateTimeOffset.Parse(v.ToString(), null, DateTimeStyles.RoundtripKind);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to parse date value {Value} of Custom Property {CustomObjectId} {CustomObjectPropertyId} for contact {ContactId}: {Error}",
                                    v, input.CustomObjectDateTimeSettings.SchemaId, input.CustomObjectDateTimeSettings.SchemafulObjectPropertyId, contactId, ex.Message);
                                return DateTimeOffset.MinValue;
                            }
                        })
                        .Where(dt => (dt != null && dt != DateTimeOffset.MinValue))
                        .ToArray();

                    if (!dateTimeList.Any())
                    {
                        _logger.LogInformation("No valid property values for contact {ContactId} after filtering nulls and empty strings.", contactId);
                        return null;
                    }

                    return new ContactDatetime
                    {
                        ContactId = contactId,
                        DateTimeList = dateTimeList
                    };
                })
                .Where(result => result != null)
                .ToArray();
        }
        else
        {
            throw new SfInvalidValueException("ScheduledType", input.ScheduledType);
        }

        return contactDateTimeArray;
    }

    public class ContactEligibilityCheckAndScheduleByBatchInput
        : ProcessContactEligibilityCheckByBatch.ProcessContactEligibilityCheckByBatchInput
    {
        [JsonConstructor]
        public ContactEligibilityCheckAndScheduleByBatchInput(
            string origin,
            string sleekflowCompanyId,
            DateTimeOffset? lastContactCreatedAt,
            string? lastContactId,
            int? batchSize,
            string workflowId,
            string workflowVersionedId,
            string workflowName,
            string condition,
            string propertyId)
            : base(
                origin,
                sleekflowCompanyId,
                lastContactCreatedAt,
                lastContactId,
                batchSize,
                workflowId,
                workflowVersionedId,
                workflowName,
                condition)
        {
            PropertyId = propertyId;
        }

        [Required]
        [JsonProperty("property_id")]
        public string PropertyId { get; set; }
    }
}

public class ContactDatetime
{
    public string ContactId { get; set; }
    public DateTimeOffset[] DateTimeList { get; set; }
}