﻿using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class SchemafulObjectUpdatedEventRequest
{
    public string SleekflowCompanyId { get; set; }

    public string SchemafulObjectId { get; set; }

    public string SchemaId { get; set; }

    public string PrimaryPropertyValue { get; set; }

    public string PreUpdatedSleekflowUserProfileId { get; set; }

    public string PostUpdatedSleekflowUserProfileId { get; set; }

    public Dictionary<string, object?> PreUpdatedPropertyValues { get; set; }

    public Dictionary<string, object?> PostUpdatedPropertyValues { get; set; }

    public DateTimeOffset CreatedAt { get; set; }

    public string? SleekflowStaffId { get; set; }

    public List<string>? SleekflowStaffTeamIds { get; set; }

    [JsonConstructor]
    public SchemafulObjectUpdatedEventRequest(
        string sleekflowCompanyId,
        string schemafulObjectId,
        string schemaId,
        string primaryPropertyValue,
        string preUpdatedSleekflowUserProfileId,
        string postUpdatedSleekflowUserProfileId,
        Dictionary<string, object?> preUpdatedPropertyValues,
        Dictionary<string, object?> postUpdatedPropertyValues,
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemafulObjectId = schemafulObjectId;
        SchemaId = schemaId;
        PrimaryPropertyValue = primaryPropertyValue;
        PreUpdatedSleekflowUserProfileId = preUpdatedSleekflowUserProfileId;
        PostUpdatedSleekflowUserProfileId = postUpdatedSleekflowUserProfileId;
        PreUpdatedPropertyValues = preUpdatedPropertyValues;
        PostUpdatedPropertyValues = postUpdatedPropertyValues;
        CreatedAt = createdAt;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
    }
}