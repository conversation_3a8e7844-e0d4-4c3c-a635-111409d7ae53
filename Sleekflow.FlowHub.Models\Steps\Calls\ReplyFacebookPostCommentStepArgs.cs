using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class ReplyFacebookPostCommentStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.reply-facebook-post-comment";

    [Required]
    [JsonProperty("facebook_page_id__expr")]
    public string FacebookPageIdExpr { get; set; }

    [Required]
    [JsonProperty("facebook_comment_id__expr")]
    public string FacebookCommentIdExpr { get; set; }

    [Required]
    [JsonProperty("comment_body__expr")]
    public string CommentBodyExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.FacebookIntegration;

    [JsonConstructor]
    public ReplyFacebookPostCommentStepArgs(string facebookPageIdExpr, string facebookCommentIdExpr, string commentBodyExpr)
    {
        FacebookPageIdExpr = facebookPageIdExpr;
        FacebookCommentIdExpr = facebookCommentIdExpr;
        CommentBodyExpr = commentBodyExpr;
    }
}