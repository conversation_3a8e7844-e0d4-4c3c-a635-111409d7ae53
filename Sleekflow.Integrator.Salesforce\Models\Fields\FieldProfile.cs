﻿using AutoMapper;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Integrator.Salesforce.Services.Models;

namespace Sleekflow.Integrator.Salesforce.Models.Fields;

public class FieldProfile : Profile
{
#pragma warning disable JA1002
    public FieldProfile()
#pragma warning restore JA1002
    {
        CreateMap<Field, GetTypeFieldsOutputFieldDto>()
            .ForMember(f => f.Calculated, e => e.MapFrom(p => p.Calculated))
            .ForMember(f => f.CompoundFieldName, e => e.MapFrom(p => p.CompoundFieldName))
            .ForMember(f => f.Createable, e => e.MapFrom(p => p.Createable))
            .ForMember(f => f.Custom, e => e.MapFrom(p => p.Custom))
            .ForMember(f => f.Encrypted, e => e.MapFrom(p => p.Encrypted))
            .ForMember(f => f.Label, e => e.MapFrom(p => p.Label))
            .ForMember(f => f.Length, e => e.MapFrom(p => p.Length))
            .ForMember(f => f.Name, e => e.MapFrom(p => p.Name))
            .ForMember(
                f => f.PicklistValues,
                e => e.MapFrom(
                    p => p.PicklistValues!
                        .Select(o => new GetTypeFieldsOutputPicklistValue(o.Label!, o.Value!))
                        .ToList()))
            .ForMember(f => f.SoapType, e => e.MapFrom(p => p.Type))
            .ForMember(f => f.Type, e => e.MapFrom(p => p.Type))
            .ForMember(f => f.Unique, e => e.MapFrom(p => p.Unique))
            .ForMember(f => f.Updateable, e => e.MapFrom(p => p.Updateable))
            .ForCtorParam("calculated", e => e.MapFrom(p => p.Calculated))
            .ForCtorParam("compoundFieldName", e => e.MapFrom(p => p.CompoundFieldName))
            .ForCtorParam("createable", e => e.MapFrom(p => p.Createable))
            .ForCtorParam("custom", e => e.MapFrom(p => p.Custom))
            .ForCtorParam("encrypted", e => e.MapFrom(p => p.Encrypted))
            .ForCtorParam("label", e => e.MapFrom(p => p.Label))
            .ForCtorParam("length", e => e.MapFrom(p => p.Length))
            .ForCtorParam("name", e => e.MapFrom(p => p.Name))
            .ForCtorParam(
                "picklistValues",
                e => e.MapFrom(
                    p => p.PicklistValues!
                        .Select(o => new GetTypeFieldsOutputPicklistValue(o.Label!, o.Value!))
                        .ToList()))
            .ForCtorParam("soapType", e => e.MapFrom(p => p.Type))
            .ForCtorParam("type", e => e.MapFrom(p => p.Type))
            .ForCtorParam("unique", e => e.MapFrom(p => p.Unique))
            .ForCtorParam("updateable", e => e.MapFrom(p => p.Updateable));
    }
}