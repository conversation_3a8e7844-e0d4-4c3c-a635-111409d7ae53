using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Products;

public interface IDefaultProductService
{
    Task<(Product Product, List<ProductVariant> ProductVariants)> CreateDefaultProductAsync(
        ProductInput productInput,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff,
        List<Price> prices,
        List<ProductVariant.ProductVariantAttribute> attributes);

    Task<(Product Product, List<ProductVariant> ProductVariants)> UpdateDefaultProductAsync(
        string productId,
        ProductInput productInput,
        string sleekflowCompanyId,
        string storeId,
        bool isViewEnabled,
        List<Price> prices,
        List<ProductVariant.ProductVariantAttribute> attributes,
        AuditEntity.SleekflowStaff sleekflowStaff);
}

public class DefaultProductService : IDefaultProductService, IScopedService
{
    private readonly IProductVariantService _productVariantService;
    private readonly IIdService _idService;
    private readonly IProductService _productService;
    private readonly IImageService _imageService;

    public DefaultProductService(
        IProductVariantService productVariantService,
        IIdService idService,
        IProductService productService,
        IImageService imageService)
    {
        _productVariantService = productVariantService;
        _idService = idService;
        _productService = productService;
        _imageService = imageService;
    }

    public async Task<(Product Product, List<ProductVariant> ProductVariants)> CreateDefaultProductAsync(
        ProductInput productInput,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff,
        List<Price> prices,
        List<ProductVariant.ProductVariantAttribute> attributes)
    {
        var images = await _imageService.GetImagesAsync(
            productInput.Images,
            sleekflowCompanyId,
            storeId);

        var productId = _idService.GetId(SysTypeNames.Product);

        // Create the default ProductVariant
        var productVariants = new List<ProductVariant>
        {
            Capacity = 1
        };
        var productVariant = await _productVariantService.CreateAndGetProductVariantAsync(
            new ProductVariant(
                _idService.GetId(SysTypeNames.ProductVariant),
                sleekflowCompanyId,
                storeId,
                productId,
                $"{productId}-DEFAULT",
                productInput.Url,
                prices,
                0,
                true,
                attributes,
                productInput.Names,
                productInput.Descriptions,
                new List<Image>(),
                PlatformData.CustomCatalog(),
                new List<string>
                {
                    "Active"
                },
                new Dictionary<string, object?>(),
                sleekflowStaff,
                sleekflowStaff,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
            sleekflowStaff);
        productVariants.Add(productVariant);

        // Create the Product
        var productVariantNames = productInput.Names;
        var productVariantDescriptions = productInput.Descriptions;
        var productVariantAttributes = attributes
            .GroupBy(a => a.Name)
            .Select(
                ag => new Product.ProductAttribute(
                    ag.Key,
                    ag.Select(a => new Product.ProductAttributeValue(a.Value)).ToList()))
            .ToList();
        var productVariantPrices = prices;

        var product = await _productService.CreateAndGetProductAsync(
            new Product(
                productId,
                sleekflowCompanyId,
                storeId,
                productInput.CategoryIds,
                productInput.Sku,
                productInput.Url,
                productInput.Names,
                productInput.Descriptions,
                images,
                productVariantNames,
                productVariantDescriptions,
                productVariantAttributes,
                productVariantPrices,
                false,
                new List<string>
                {
                    "Active"
                },
                PlatformData.CustomCatalog(),
                productInput.Metadata,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                createdBy: sleekflowStaff,
                updatedBy: sleekflowStaff),
            sleekflowStaff);

        return (product, productVariants);
    }

    public async Task<(Product Product, List<ProductVariant> ProductVariants)> UpdateDefaultProductAsync(
        string productId,
        ProductInput productInput,
        string sleekflowCompanyId,
        string storeId,
        bool isViewEnabled,
        List<Price> prices,
        List<ProductVariant.ProductVariantAttribute> attributes,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var images = await _imageService.GetImagesAsync(
            productInput.Images,
            sleekflowCompanyId,
            storeId);

        var product = await _productService.PatchAndGetProductAsync(
            productId,
            sleekflowCompanyId,
            storeId,
            productInput.CategoryIds,
            productInput.Sku,
            productInput.Url,
            productInput.Names,
            productInput.Descriptions,
            images,
            isViewEnabled,
            productInput.Metadata,
            sleekflowStaff);

        // Patch the default ProductVariant
        var productVariants = await _productVariantService.GetProductVariantsAsync(
            sleekflowCompanyId,
            storeId,
            productId);
        if (productVariants.All(pv => !pv.IsDefaultVariantProduct))
        {
            throw new SfUserFriendlyException(
                $"No default ProductVariant found for Product with Id {productId}");
        }

        foreach (var productVariant in productVariants.Where(productVariant => productVariant.IsDefaultVariantProduct))
        {
            await _productVariantService.PatchAndGetProductVariantAsync(
                productVariant.Id,
                sleekflowCompanyId,
                storeId,
                product.Id,
                $"{product.Id}-DEFAULT",
                productInput.Url,
                prices,
                attributes,
                productInput.Names,
                productInput.Descriptions,
                new List<Image>(),
                sleekflowStaff);
        }

        var updatedProductVariants = await _productVariantService.GetProductVariantsAsync(
            sleekflowCompanyId,
            storeId,
            productId);

        return (
            product,
            updatedProductVariants);
    }
}