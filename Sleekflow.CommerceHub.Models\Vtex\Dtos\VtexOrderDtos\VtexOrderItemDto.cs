﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

public record VtexOrderItemDto
{
    [JsonProperty("uniqueId")]
    public string UniqueId { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("productId")]
    public string ProductId { get; set; }

    [JsonProperty("ean")]
    public string Ean { get; set; }

    [JsonProperty("lockId")]
    public string LockId { get; set; }

    [JsonProperty("quantity")]
    public int Quantity { get; set; }

    [JsonProperty("seller")]
    public string Seller { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("refId")]
    public string RefId { get; set; }

    [JsonProperty("price")]
    public int Price { get; set; }

    [JsonProperty("listPrice")]
    public int ListPrice { get; set; }

    [JsonProperty("manualPrice")]
    public int? ManualPrice { get; set; }

    [JsonProperty("priceTags")]
    public List<object> PriceTags { get; set; }

    [JsonProperty("imageUrl")]
    public string ImageUrl { get; set; }

    [JsonProperty("detailUrl")]
    public string DetailUrl { get; set; }

    [JsonProperty("sellerSku")]
    public string SellerSku { get; set; }

    [JsonProperty("priceValidUntil")]
    public DateTime PriceValidUntil { get; set; }

    [JsonProperty("commission")]
    public int Commission { get; set; }

    [JsonProperty("tax")]
    public int Tax { get; set; }

    [JsonProperty("additionalInfo")]
    public VtexAdditionalInfoDto AdditionalInfo { get; set; }

    [JsonProperty("isGift")]
    public bool IsGift { get; set; }

    [JsonProperty("taxCode")]
    public string TaxCode { get; set; }

    [JsonProperty("costPrice")]
    public int CostPrice { get; set; }

    [JsonConstructor]
    public VtexOrderItemDto(
        string uniqueId,
        string id,
        string productId,
        string ean,
        string lockId,
        int quantity,
        string seller,
        string name,
        string refId,
        int price,
        int listPrice,
        int? manualPrice,
        List<object> priceTags,
        string imageUrl,
        string detailUrl,
        string sellerSku,
        DateTime priceValidUntil,
        int commission,
        int tax,
        VtexAdditionalInfoDto additionalInfo,
        bool isGift,
        string taxCode,
        int costPrice)
    {
        UniqueId = uniqueId;
        Id = id;
        ProductId = productId;
        Ean = ean;
        LockId = lockId;
        Quantity = quantity;
        Seller = seller;
        Name = name;
        RefId = refId;
        Price = price;
        ListPrice = listPrice;
        ManualPrice = manualPrice;
        PriceTags = priceTags;
        ImageUrl = imageUrl;
        DetailUrl = detailUrl;
        SellerSku = sellerSku;
        PriceValidUntil = priceValidUntil;
        Commission = commission;
        Tax = tax;
        AdditionalInfo = additionalInfo;
        IsGift = isGift;
        TaxCode = taxCode;
        CostPrice = costPrice;
    }
}

public record VtexAdditionalInfoDto
{
    [JsonProperty("brandName")]
    public string BrandName { get; set; }

    [JsonProperty("brandId")]
    public string BrandId { get; set; }

    [JsonProperty("categoriesIds")]
    public string CategoriesIds { get; set; }

    [JsonProperty("categories")]
    public List<VtexCategoryDto> Categories { get; set; }

    [JsonConstructor]
    public VtexAdditionalInfoDto(
        string brandName,
        string brandId,
        string categoriesIds,
        List<VtexCategoryDto> categories)
    {
        BrandName = brandName;
        BrandId = brandId;
        CategoriesIds = categoriesIds;
        Categories = categories;
    }
}

public record VtexCategoryDto
{
    [JsonProperty("id")]
    public int Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonConstructor]
    public VtexCategoryDto(int id, string name)
    {
        Id = id;
        Name = name;
    }
}