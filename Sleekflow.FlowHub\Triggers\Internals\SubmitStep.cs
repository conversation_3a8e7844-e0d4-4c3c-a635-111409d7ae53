using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Steps;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class SubmitStep : ITrigger
{
    private readonly IStepRequester _stepRequester;

    public SubmitStep(
        IStepRequester stepRequester)
    {
        _stepRequester = stepRequester;
    }

    public class SubmitStepInput : Sleekflow.FlowHub.Models.Internals.SubmitStepInput
    {
        [JsonConstructor]
        public SubmitStepInput(string stateId, string stepId, Stack<StackEntry> stackEntries, string executionStatus)
            : base(stateId, stepId, stackEntries, executionStatus)
        {
        }
    }

    public class SubmitStepOutput : Sleekflow.FlowHub.Models.Internals.SubmitStepOutput
    {
        [JsonConstructor]
        public SubmitStepOutput()
        {
        }
    }

    public async Task<SubmitStepOutput> F(SubmitStepInput submitStepInput)
    {
        await _stepRequester.RequestAsync(
            submitStepInput.StateId,
            submitStepInput.StepId,
            submitStepInput.StackEntries,
            submitStepInput.WorkerInstanceId);

        return new SubmitStepOutput();
    }
}