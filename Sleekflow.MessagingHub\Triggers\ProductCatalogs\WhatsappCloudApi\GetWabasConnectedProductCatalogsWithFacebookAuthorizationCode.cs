using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class GetWabasConnectedProductCatalogsWithFacebookAuthorizationCode
    : ITrigger<
        GetWabasConnectedProductCatalogsWithFacebookAuthorizationCode.GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput,
        GetWabasConnectedProductCatalogsWithFacebookAuthorizationCode.GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutput>
{
    private readonly ILogger<GetWabasConnectedProductCatalogsWithFacebookAuthorizationCode> _logger;
    private readonly IProductCatalogService _productCatalogService;

    public GetWabasConnectedProductCatalogsWithFacebookAuthorizationCode(
        ILogger<GetWabasConnectedProductCatalogsWithFacebookAuthorizationCode> logger,
        IProductCatalogService productCatalogService)
    {
        _logger = logger;
        _productCatalogService = productCatalogService;
    }

    public class GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("should_refresh")]
        public bool ShouldRefresh { get; set; }

        [JsonProperty("facebook_authorization_code")]
        public string? FacebookAuthorizationCode { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput(
            string sleekflowCompanyId,
            bool shouldRefresh,
            string? facebookAuthorizationCode,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ShouldRefresh = shouldRefresh;
            FacebookAuthorizationCode = facebookAuthorizationCode;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutput
    {
        [JsonProperty("facebook_connected_waba_product_catalog_mappings")]
        public List<FacebookConnectedWabaProductCatalogMappingDto> FacebookConnectedWabaProductCatalogMappings
        {
            get;
            set;
        }

        [JsonConstructor]
        public GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutput(
            List<FacebookConnectedWabaProductCatalogMappingDto> facebookConnectedWabaProductCatalogMappings)
        {
            FacebookConnectedWabaProductCatalogMappings = facebookConnectedWabaProductCatalogMappings;
        }
    }

    public async Task<GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutput> F(
        GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput getWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            getWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput.SleekflowStaffId,
            getWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput.SleekflowStaffTeamIds);

        var facebookAuthorizationCode = getWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput.FacebookAuthorizationCode;
        var sleekflowCompanyId = getWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput.SleekflowCompanyId;
        var shouldRefresh = getWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput.ShouldRefresh;

        _logger.LogInformation(
            "getting {SleekflowCompanyId} wabas and product catalogs information {ShouldRefresh} shouldRefresh flag by {@SleekflowStaff}",
            sleekflowCompanyId,
            shouldRefresh.ToString(),
            sleekflowStaff);

        var facebookConnectedWabaProductCatalogMappings = !string.IsNullOrEmpty(facebookAuthorizationCode)
            ? await _productCatalogService.GetWabasConnectedFacebookProductCatalogsWithFacebookAuthorizationCodeAsync(
                facebookAuthorizationCode,
                sleekflowCompanyId,
                sleekflowStaff)
            : await _productCatalogService.GetWabasConnectedFacebookProductCatalogsAsync(
                sleekflowCompanyId,
                shouldRefresh,
                sleekflowStaff);

        return new GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutput(facebookConnectedWabaProductCatalogMappings);
    }
}