﻿using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CommerceHubEventConsumers;

public class VtexOrderHookReceivedEventRequestConsumerDefinition : ConsumerDefinition<VtexOrderHookReceivedEventRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<VtexOrderHookReceivedEventRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class VtexOrderHookReceivedEventRequestConsumer : IConsumer<VtexOrderHookReceivedEventRequest>
{
    private readonly IServiceBusManager _serviceBusManager;

    public VtexOrderHookReceivedEventRequestConsumer(IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<VtexOrderHookReceivedEventRequest> context)
    {
        var vtexOrderCreatedEventRequest = context.Message;

        if (vtexOrderCreatedEventRequest.StatusCode == "order-created")
        {
            await _serviceBusManager.PublishAsync(new OnTriggerEventRequestedEvent(
                new OnVtexOrderCreatedEventBody(
                    DateTimeOffset.UtcNow,
                    vtexOrderCreatedEventRequest.VtexAuthenticationId,
                    vtexOrderCreatedEventRequest.StatusCode,
                    vtexOrderCreatedEventRequest.OrderId,
                    vtexOrderCreatedEventRequest.Order),
                vtexOrderCreatedEventRequest.OrderId,
                "Order",
                vtexOrderCreatedEventRequest.SleekflowCompanyId));
        }
        else
        {
            await _serviceBusManager.PublishAsync(new OnTriggerEventRequestedEvent(
                new OnVtexOrderStatusChangedEventBody(
                    DateTimeOffset.UtcNow,
                    vtexOrderCreatedEventRequest.VtexAuthenticationId,
                    vtexOrderCreatedEventRequest.StatusCode,
                    vtexOrderCreatedEventRequest.OrderId,
                    vtexOrderCreatedEventRequest.Order),
                vtexOrderCreatedEventRequest.OrderId,
                "Order",
                vtexOrderCreatedEventRequest.SleekflowCompanyId));
        }
    }
}