# This is a markdown for the website at https://help.sleekflow.io/en_US/general-settings/setting-up-two-factor-authentication-for-your-organization
## Setting up two-factor authentication for your organization - SleekFlow

This article guides you through setting up two-factor authentication (2FA) for your company’s SleekFlow account.

### What is 2FA?

2FA is an optional extra layer of security designed to protect your accounts from unauthorized access. When 2FA is enabled within a SleekFlow company account, an additional step is required for relevant users within that company during the login process, requiring them to provide two different forms of identification to authenticate their session.

### What types of 2FA does SleekFlow support?

SleekFlow currently supports 2FA through authenticator apps like Google Authenticator, Authy and Microsoft Authenticator.

### Manage Your Company’s 2FA Settings

**Note on access:**

2FA is only available for selected plans. You can refer to the [SleekFlow Pricing page](https://sleekflow.io/pricing) to view more details.

**Note on permissions:**

Only admin users can access and update the 2FA settings for your company’s SleekFlow account.

Access the page to manage your company’s 2FA settings by following the steps outlined below:

*   Click on the <img src="https://static.helpjuice.com/helpjuice_production/uploads/upload/image/16637/direct/*************/image.png" width="28px"> icon on the bottom left corner to access general settings
*   Within “General settings”, click “Company details” (Under “Company Settings”)

Within the “Security” tab of “Company details”, you can manage your company’s 2FA settings.

<figure class="image"><img src="https://lh7-us.googleusercontent.com/docsz/AD_4nXfG3y_fl__D0eETQwRli4BscMt0ApBvKhYCrQygGHWtwzRDgT0d_oGPBHXykAVF3mlaDQiczR-TlaetR0QsRzMsXa4pgvBz_V1P-oGsi4XBHz-fTM2mPBZsy4KGFqTLnTf331PTKNRWD4R0DLQbmEVQzrI?key=AfUaf2ON0QoSvg5YNrLuFw" width="491" height="152"></figure>

Once 2FA is enabled for certain users within your organization, they will receive an email to set up 2FA for their account. Users who have yet to set up 2FA for their account will also be prompted to do so the next time they attempt to sign in.