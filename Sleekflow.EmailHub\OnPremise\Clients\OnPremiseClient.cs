using MailKit;
using MailKit.Net.Imap;
using MailKit.Net.Smtp;
using MailKit.Security;
using MimeKit;
using Serilog;
using Sleekflow.EmailHub.Models.OnPremise.Authentications;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Hubspot;

namespace Sleekflow.EmailHub.OnPremise.Clients;

public abstract class OnPremiseClient : IDisposable
{
    public List<MimeMessage> Messages { get; protected set; }

    protected readonly FetchRequest FetchRequest;
    protected readonly string HostName;
    protected readonly string Username;
    protected readonly string Password;
    protected readonly int PortNumber;
    protected readonly SecureSocketOptions SslOptions;
    protected readonly CancellationTokenSource Cancel;
    private readonly ILogger<OnPremiseClient> _logger;

    protected string ServerType { get; private set; }

    protected readonly ImapClient? ImapClient;
    protected readonly SmtpClient? SmtpClient;

    protected OnPremiseClient(
        string serverType,
        string hostName,
        string username,
        string password,
        int portNumber,
        FetchRequest fetchRequest,
        SecureSocketOptions sslOptions)
    {
        var supportedProtocolTypes = new List<string> { ProtocolTypes.Imap, ProtocolTypes.Smtp };
        if (!supportedProtocolTypes.Contains(serverType))
        {
            throw new SfNotSupportedOperationException("OnPremiseClient");
        }

        if (serverType == ProtocolTypes.Imap)
        {
            ImapClient = new ImapClient(new ProtocolLogger(Console.OpenStandardError()));
            ImapClient.Timeout = 5000;
        }
        else if (serverType == ProtocolTypes.Smtp)
        {
            SmtpClient = new SmtpClient(new ProtocolLogger(Console.OpenStandardError()));
            SmtpClient.Timeout = 5000;
        }

        HostName = hostName;
        Username = username;
        Password = password;
        PortNumber = portNumber;
        FetchRequest = fetchRequest;
        SslOptions = sslOptions;
        ServerType = serverType;
        Cancel = new CancellationTokenSource();
        Messages = new List<MimeMessage>();
        _logger = new LoggerFactory().AddSerilog().CreateLogger<OnPremiseClient>();
    }

    protected async Task ReconnectAsync(CancellationToken cancellationToken = default)
    {
        switch (ServerType)
        {
            case ProtocolTypes.Imap:

                try
                {
                    if (!ImapClient!.IsConnected)
                    {
                        await ImapClient.ConnectAsync(
                            HostName,
                            PortNumber,
                            SslOptions,
                            Cancel.Token);
                    }

                    if (!ImapClient.IsAuthenticated)
                    {
                        await ImapClient.AuthenticateAsync(Username, Password, Cancel.Token);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        "OnPremiseClient: fails to connect or authenticate the IMAP server: {server}," +
                        " ex: {ex}", HostName, e);
                    throw new SfUnauthorizedException();
                }

                break;
            case ProtocolTypes.Smtp:

                try
                {
                    if (!SmtpClient!.IsConnected)
                    {
                        await SmtpClient.ConnectAsync(
                            HostName,
                            PortNumber,
                            SslOptions,
                            Cancel.Token);
                    }

                    if (!SmtpClient.IsAuthenticated)
                    {
                        await SmtpClient.AuthenticateAsync(Username, Password, Cancel.Token);
                    }
                }
                catch (Exception e)
                {
                    _logger.LogError(
                        "OnPremiseClient: fails to connect or authenticate the SMTP server: {server}," +
                        " ex: {ex}", HostName, e);
                    throw new SfUnauthorizedException();
                }

                break;
            default:
                throw new NotImplementedException();
        }
    }

    protected async Task<IList<IMessageSummary>> FetchMessageAndSummariesAsync(
        int startIndex,
        CancellationToken cancellationToken = default)
    {
        if (ImapClient == null)
        {
            throw new SfNotSupportedOperationException("IMAPOnPremiseClient");
        }

        await ImapClient.Inbox.OpenAsync(FolderAccess.ReadOnly, cancellationToken);

        IList<IMessageSummary> fetchedMessageSummaries;

        do
        {
            try
            {
                fetchedMessageSummaries = await ImapClient.Inbox.FetchAsync(startIndex, -1, FetchRequest, Cancel.Token);
                break;
            }
            catch (ImapProtocolException)
            {
                await ReconnectAsync(cancellationToken);
            }
            catch (IOException)
            {
                await ReconnectAsync(cancellationToken);
            }
            catch (ArgumentOutOfRangeException)
            {
                _logger.LogError("[IMAPOnPremiseClient]: nothing new to sync from the IMAP server {server}", HostName);
                return new List<IMessageSummary>();
            }
        }
        while (true);

        foreach (var messageSummary in fetchedMessageSummaries)
        {
            var message = await ImapClient.Inbox.GetMessageAsync(messageSummary.UniqueId, cancellationToken);
            Messages.Add(message);
        }

        return fetchedMessageSummaries;
    }

    protected async Task SendEmailAsync(MimeMessage email, CancellationToken cancellationToken = default)
    {
        if (SmtpClient == null)
        {
            throw new SfNotSupportedOperationException("SMTPOnPremiseClient");
        }

        await SmtpClient.SendAsync(email, cancellationToken);
    }

    public abstract Task RunAsync(CancellationToken cancellationToken = default);

    public void Exit()
    {
        Cancel.Dispose();
    }

    public void Dispose()
    {
        Cancel.Dispose();
        ImapClient?.Disconnect(true);
        ImapClient?.Dispose();
        SmtpClient?.Disconnect(true);
        SmtpClient?.Dispose();
    }
}