using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class CreateEmployeeRequest
{
    [JsonProperty(
        PropertyName = "id",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? NetSuiteInternalId { get; set; }

    [JsonProperty(
        PropertyName = "externalId",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? ExternalId { get; set; }

    [Required]
    [JsonProperty(
        PropertyName = "firstName",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? FirstName { get; set; }

    [Required]
    [JsonProperty(
        PropertyName = "lastName",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? LastName { get; set; }

    [Required]
    [JsonProperty(
        PropertyName = "email",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? Email { get; set; }

    [JsonProperty(
        PropertyName = "issalesrep",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public bool? IsSalesRep { get; set; }

    [JsonProperty(
        PropertyName = "supervisor",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public Supervisor? Supervisor { get; set; }

    [Required]
    [JsonProperty(
        PropertyName = "defaultExpenseReportCurrency",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public DefaultExpenseReportCurrency? DefaultExpenseReportCurrency { get; set; }

    [Required]
    [JsonProperty(
        PropertyName = "subsidiary",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public Subsidiary? Subsidiary { get; set; }

    [JsonConstructor]
    public CreateEmployeeRequest(
        string? externalId,
        string? firstName,
        string? lastName,
        string? email,
        DefaultExpenseReportCurrency? defaultExpenseReportCurrency,
        Subsidiary? subsidiary)
    {
        ExternalId = externalId;
        FirstName = firstName;
        LastName = lastName;
        Email = email;
        DefaultExpenseReportCurrency = defaultExpenseReportCurrency;
        Subsidiary = subsidiary;
    }

    public CreateEmployeeRequest(
        Supervisor? supervisor)
    {
        Supervisor = supervisor;
    }

    public CreateEmployeeRequest()
    {
    }
}

public class Supervisor
{
    [JsonProperty(PropertyName = "id", NullValueHandling = NullValueHandling.Include)]
    public string? Id { get; set; }

    [JsonConstructor]
    public Supervisor(string? id)
    {
        Id = id;
    }

    public Supervisor()
    {
    }
}

public class DefaultExpenseReportCurrency
{
    [JsonProperty(
        PropertyName = "id",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? Id { get; set; }

    [JsonConstructor]
    public DefaultExpenseReportCurrency(string? id)
    {
        Id = id;
    }

    public DefaultExpenseReportCurrency()
    {
    }
}

public class Subsidiary
{
    [JsonProperty(
        PropertyName = "id",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? Id { get; set; }

    [JsonProperty(
        PropertyName = "refName",
        DefaultValueHandling = DefaultValueHandling.Ignore,
        NullValueHandling = NullValueHandling.Ignore)]
    public string? RefName { get; set; }

    [JsonConstructor]
    public Subsidiary(string? id, string? refName)
    {
        Id = id;
        RefName = refName;
    }

    public Subsidiary(string? id)
    {
        Id = id;
    }

    public Subsidiary()
    {
    }
}