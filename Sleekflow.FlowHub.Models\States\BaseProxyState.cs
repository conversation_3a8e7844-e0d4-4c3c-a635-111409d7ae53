using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.States;

public class BaseProxyState
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("identity")]
    public StateIdentity Identity { get; set; }

    [JsonProperty("workflow_context")]
    public ProxyStateWorkflowContext WorkflowContext { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public BaseProxyState(
        string id,
        StateIdentity identity,
        ProxyStateWorkflowContext workflowContext,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
    {
        Id = id;
        Identity = identity;
        WorkflowContext = workflowContext;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}