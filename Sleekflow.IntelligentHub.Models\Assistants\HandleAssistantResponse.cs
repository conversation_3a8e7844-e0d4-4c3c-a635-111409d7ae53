using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Messages;

namespace Sleekflow.IntelligentHub.Models.Assistants;

public class HandleAssistantResponseInput
{
    [Required]
    [JsonProperty("message")]
    [Validations.ValidateObject]
    public MessageObject Message { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    [Validations.ValidateObject]
    public ContactObject Contact { get; set; }

    [JsonConstructor]
    public HandleAssistantResponseInput(MessageObject message, string contactId, ContactObject contact)
    {
        Message = message;
        ContactId = contactId;
        Contact = contact;
    }

    public class ContactObject
    {
        [JsonProperty("PhoneNumber")]
        public string PhoneNumber { get; set; }

        [JsonConstructor]
        public ContactObject(string phoneNumber)
        {
            PhoneNumber = phoneNumber;
        }
    }
}

public class HandleAssistantResponseOutput
{
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonConstructor]
    public HandleAssistantResponseOutput(string contactId, string message)
    {
        ContactId = contactId;
        Message = message;
    }
}