using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CaptureUserEventStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.capture-user-event";

    [Required]
    [JsonProperty("event_type__expr")]
    public string EventTypeExpr { get; set; }

    [Required]
    [JsonProperty("object_id__expr")]
    public string ObjectIdExpr { get; set; }

    [Required]
    [JsonProperty("object_type__expr")]
    public string ObjectTypeExpr { get; set; }

    [Required]
    [JsonProperty("source__expr")]
    public string SourceExpr { get; set; }

    [Required]
    [JsonProperty("campaign_id__expr")]
    public string CampaignIdExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Analytics;

    [JsonConstructor]
    public CaptureUserEventStepArgs(
        string eventTypeExpr,
        string objectIdExpr,
        string objectTypeExpr,
        string sourceExpr,
        string campaignIdExpr)
    {
        EventTypeExpr = eventTypeExpr;
        ObjectIdExpr = objectIdExpr;
        ObjectTypeExpr = objectTypeExpr;
        SourceExpr = sourceExpr;
        CampaignIdExpr = campaignIdExpr;
    }
}