﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.CrmHubConfigs;

[ContainerId(ContainerNames.CrmHubConfig)]
[DatabaseId("crmhubdb")]
[Resolver(typeof(ICrmHubDbResolver))]
public class CrmHubConfig : AuditEntity, IHasETag
{
    public const string PropertyNameUsageLimit = "usage_limit";
    public const string PropertyNameUsageLimitOffset = "usage_limit_offset";
    public const string PropertyNameFeatureAccessibilitySettings = "feature_accessibility_settings";

    [JsonProperty(PropertyNameUsageLimit)]
    public UsageLimit UsageLimit { get; set; }

    [JsonProperty(PropertyNameUsageLimitOffset)]
    public UsageLimitOffset UsageLimitOffset { get; set; }

    [JsonProperty(PropertyNameFeatureAccessibilitySettings)]
    public FeatureAccessibilitySettings FeatureAccessibilitySettings { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonConstructor]
    public CrmHubConfig(
        string id,
        UsageLimit usageLimit,
        UsageLimitOffset? usageLimitOffset,
        FeatureAccessibilitySettings? featureAccessibilitySettings,
        string? eTag,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy)
        : base(id, SysTypeNames.CrmHubConfig, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        UsageLimit = usageLimit;
        UsageLimitOffset = usageLimitOffset ?? UsageLimitOffset.Default();
        FeatureAccessibilitySettings = featureAccessibilitySettings ?? FeatureAccessibilitySettings.Default();
        ETag = eTag;
    }
}