﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence;

public interface IDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }
}

public class DbConfig : IConfig, IDbConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public DbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_DATABASE_ID");
    }
}