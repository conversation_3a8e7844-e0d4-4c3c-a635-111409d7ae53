using System.Xml;
using Alba;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Mvc.Tests;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.TenantHubDb;
using Sleekflow.TenantHub.Client;
using Sleekflow.TenantHub.ExperimentalFeatures;
using Sleekflow.TenantHub.Features;
using Sleekflow.TenantHub.Features.EnabledFeatures;
using Sleekflow.TenantHub.IpWhitelists;
using Sleekflow.TenantHub.Models.Auth0s;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.Features;
using Sleekflow.TenantHub.Models.TravisBackend;
using Sleekflow.TenantHub.Models.Users;

namespace Sleekflow.TenantHub.Tests;

[SetUpFixture]
public class Application
{
    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.TenantHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        Host = await AlbaHost.For<Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(
                    services =>
                    {
                        services.AddSingleton<ITenantHubDbConfig>(new MyTenantHubDbConfig());
                        services.AddSingleton<ITenantHubDbResolver, TenantHubDbResolver>();
                        services.AddScoped<ITravisBackendClient, MockTravisBackendClient>();
                        services.AddScoped<IFeatureService, MockFeatureService>();
                        services.AddScoped<IEnabledFeatureService, MockEnableFeatureService>();
                        services.AddScoped<IIpWhitelistRepository, IpWhitelistRepository>();
                        services.AddScoped<IIpWhitelistService, IpWhitelistService>();
                        services.AddScoped<IExperimentalFeatureRepository, ExperimentalFeatureRepository>();
                        services.AddScoped<IExperimentalFeatureService, ExperimentalFeatureService>();
                    });
            },
            Array.Empty<IAlbaExtension>());

        Host.AfterEachAsync(
            async context =>
            {
                await BaseTestHost.InterceptAfterEachAsync(context);
            });
    }

    public class MockTravisBackendClient : ITravisBackendClient
    {
        private ITravisBackendClient _travisBackendClientImplementation;

        public Task<GetUserOutput?> GetUserAsync(string sleekflowUserId)
        {
            throw new NotImplementedException();
        }

        public Task<GetUserOutput?> GetUserAsync(string sleekflowUserId, string? location)
        {
            throw new NotImplementedException();
        }

        public Task<RefreshIpWhitelistCacheOutput?> RefreshIpWhitelistCacheAsync(string sleekflowCompanyId, string? location = null)
        {
            object[] args = { true, true, null! };
            RefreshIpWhitelistCacheOutput? result =
                (RefreshIpWhitelistCacheOutput?) Activator.CreateInstance(typeof(RefreshIpWhitelistCacheOutput), args);
            return Task.FromResult(result);
        }

        public Task<CleanIpWhitelistCacheOutput?> CleanIpWhitelistCacheAsync(string sleekflowCompanyId, string? location = null)
        {
            throw new NotImplementedException();
        }

        public Task<CreateNewDbUserOutput?> CreateNewDbUserAsync(CreateNewDbUserInput auth0User, string? location = null)
        {
            throw new NotImplementedException();
        }

        public Task<UpdateDbUserOutput?> UpdateDbUserAsync(UpdateDbUserInput input, string? location = null)
        {
            throw new NotImplementedException();
        }

        public Task<IActionResult?> DeleteDbUserAsync(string sleekflowUserId, bool alsoDeleteAuth0User, string? location = null)
        {
            throw new NotImplementedException();
        }

        public Task<TResponse?> SendAsync<TResponse, TRequest>(
            TRequest input,
            string endPoint,
            string? location = null,
            Dictionary<string, string>? additionHeaders = null)
        {
            if (typeof(TResponse) == typeof(RefreshIpWhitelistCacheOutput))
            {
                object[] args = { true, true, null! };
                TResponse? result = (TResponse?) Activator.CreateInstance(typeof(TResponse?), args);

                return Task.FromResult(result);
            }

            return Task.FromResult(Activator.CreateInstance<TResponse?>());
        }

        public Task<GenerateInviteLinkOutput?> GenerateInviteLinkAsync(
            string sleekflowUserId,
            ShareableInvitationViewModel shareableInvitation,
            string? location)
        {
            throw new NotImplementedException();
        }

        public Task ResendInvitationEmailAsync(
            string sleekflowStaffUserId,
            string sleekflowTenantHubUserId,
            string sleekflowAdminUserId,
            string sleekflowCompanyId,
            string location)
        {
            throw new NotImplementedException();
        }

        public Task<List<InviteUserByEmailResponseObject>?> InviteUserByEmailAsync(
            string sleekflowUserId,
            string sleekflowCompanyId,
            List<InviteUserObject> invitedUsers,
            List<long> teamIds,
            string location,
            string tenantHubUserId)
        {
            throw new NotImplementedException();
        }

        public Task<CompleteEmailInvitationResponse?> CompleteEmailInvitationAsync(
            User user,
            string sleekflowUserId,
            string token,
            string password,
            string position,
            string timeZoneInfoId,
            string location)
        {
            throw new NotImplementedException();
        }

        public Task<CompleteLinkInvitationResponse?> CompleteLinkInvitationAsync(string shareableId, InviteUserByLinkObject userByLink, string location)
        {
            throw new NotImplementedException();
        }

        public Task<RegisterCompanyResponse?> RegisterCompanyAsync(
            string companyId,
            string userId,
            string? firstName,
            string? lastName,
            string phoneNumber,
            string industry,
            string? onlineShopSystem,
            string? companyWebsite,
            string companyName,
            string timeZoneInfoId,
            string companySize,
            string subscriptionPlanId,
            string? lmref,
            string? heardFrom,
            string? promotionCode,
            string? webClientUuid,
            string? referral,
            string companyType,
            List<string>? communicationTools,
            bool? isAgreeMarketingConsent,
            string? platformUsageIntent,
            string connectionStrategy,
            string location)
        {
            throw new NotImplementedException();
        }

        public Task<RegisterCompanyResponse?> RegisterCompanyAsync(
            string companyId,
            string userId,
            string? firstName,
            string? lastName,
            string phoneNumber,
            string industry,
            string? onlineShopSystem,
            string? companyWebsite,
            string companyName,
            string timeZoneInfoId,
            string companySize,
            string subscriptionPlanId,
            string? lmref,
            string? heardFrom,
            string? promotionCode,
            string? webClientUuid,
            string? referral,
            string companyType,
            List<string>? communicationTools,
            bool? isAgreeMarketingConsent,
            string connectionStrategy,
            string location)
        {
            throw new NotImplementedException();
        }

        public Task<IsCompanyRegisteredResponse?> IsCompanyRegisteredAsync(string userId)
        {
            throw new NotImplementedException();
        }

        public Task<TravisBackendApplicationUser?> GetApplicationUserAsync(string? sleekflowUserId = null, string? email = null, string? userName = null)
        {
            throw new NotImplementedException();
        }

        public Task<AssociateDbUserWithAuth0UserOutput?> AssociateDbUserWithAuth0UserAsync(TravisBackendApplicationUser applicationUser, global::Auth0.ManagementApi.Models.User auth0User, List<string?> roles)
        {
            throw new NotImplementedException();
        }

        public Task<IActionResult?> DeleteUserAsync(
            string sleekflowUserId,
            bool isAlsoDeleteAuth0User = false,
            string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task<ObjectResult?> OnUserChangePasswordAsync(string userId, string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task<RegisterCompanyResponse?> RegisterCompanyAsync(
            string companyId,
            string userId,
            string? firstName,
            string? lastName,
            string phoneNumber,
            string industry,
            string? onlineShopSystem,
            string? companyWebsite,
            string companyName,
            string timeZoneInfoId,
            string companySize,
            string subscriptionPlanId,
            string? lmref,
            string? heardFrom,
            string? promotionCode,
            string? webClientUuid,
            string? referral,
            string companyType,
            List<string>? communicationTools,
            bool? isAgreeMarketingConsent,
            string location)
        {
            throw new NotImplementedException();
        }

        public Task<RegisterCompanyResponse?> RegisterCompanyAsync(
            string userId,
            string? firstName,
            string? lastName,
            string phoneNumber,
            string industry,
            string onlineShopSystem,
            string companyWebsite,
            string companyName,
            string timeZoneInfoId,
            string companySize,
            string subscriptionPlanId,
            string lmref,
            string heardFrom,
            string promotionCode,
            string webClientUuid,
            string referral,
            string companyType,
            List<string> communicationTools,
            bool? isAgreeMarketingConsent,
            string location)
        {
            throw new NotImplementedException();
        }

        public Task<DeleteCompanyStaffResponse?> DeleteCompanyStaffAsync(string companyId, string staffId, string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task<GetUserStaffOutput?> GetUserStaffAsync(string sleekflowUserId)
        {
            throw new NotImplementedException();
        }

        public Task<GetCompanyOwnerOutput?> GetCompanyOwnerAsync(string companyId, string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task<UpdateStaffRoleResponse?> UpdateStaffRoleAsync(
            string staffId,
            string companyId,
            string newRole,
            string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task<IsRbacEnabledFromServerEnvironmentResponse> IsRbacEnabledFromServerEnvironmentAsync(string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task<ImportUserFromCsvResponse?> ImportUserFromCsvAsync(string sleekflowCompanyId, List<ImportUserFromCsvObject> importUsers, string location)
        {
            throw new NotImplementedException();
        }

        public Task<GetCompanyTeamIdsByNamesResponse?> GetCompanyTeamIdsByNamesAsync(string companyId, List<string> teamNames, string location)
        {
            throw new NotImplementedException();
        }

        public Task<GetCompanyUsageResponse?> GetCompanyUsageAsync(string companyId, string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task OnRbacFeatureEnabledAsync(
            string sleekflowCompanyId,
            bool isFirstTimeEnabled,
            string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task OnRbacFeatureDisabledAsync(string sleekflowCompanyId, string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task OnUserRoleAssignedAsync(
            string sleekflowCompanyId,
            string userId,
            string staffId,
            Dictionary<string, string> roles,
            DateTimeOffset assignedAt,
            string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task OnCompanyPoliciesSavedAsync(
            string sleekflowCompanyId,
            Dictionary<string, Dictionary<string, List<string>>> rolePermissions,
            DateTimeOffset savedAt,
            string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }

        public Task<GetUserLossInfoOutput?> GetUserLostInfoAsync(string userId, string email, string location)
        {
            throw new NotImplementedException();
        }

        public Task<List<InviteUserByEmailResponseObject>?> ImportUserFromCsvAsync(string sleekflowCompanyId, List<ImportUserFromCsvObject> importUsers, List<long> teamIds, string location)
        {
            throw new NotImplementedException();
        }

        public Task<UpdateStaffRoleResponse?> UpdateStaffRoleAsync(
            long staffId,
            string companyId,
            string newRole,
            string updatedBy,
            string? location = ServerLocations.EastAsia)
        {
            throw new NotImplementedException();
        }
    }

    private class MyTenantHubDbConfig : ITenantHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow-global95ca408e.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "tenanthubdb";

        public string SfLocation { get; } = "eastasia";

    }

    private IDynamicFiltersRepositoryContext GetRepositoryFiltersContext(IServiceProvider? serviceProvider = null)
    {
        return (serviceProvider ?? Application.Host.Services).GetRequiredService<IDynamicFiltersRepositoryContext>();
    }

    public class DynamicFiltersTestRepositoryContext : IDynamicFiltersRepositoryContext, IHasSleekflowCompanyId
    {
        public bool IsSoftDeleteEnabled { get; set; } = false;

        public string? SleekflowCompanyId { get; set; } = null;
    }

    public class MockFeatureService : IFeatureService
    {
        public Task<Feature> GetAsync(string id)
        {
            return Task.FromResult((Feature) null!);
        }

        public Task<List<Feature>>
            GetExperimentalFeaturesAsync(string featureType)
        {
            return Task.FromResult(new List<Feature>());
        }

        public Task<Feature?> GetObjectByNameAsync(string featureName)
        {
            throw new NotImplementedException();
        }

        public Task<List<Feature>> GetAllAsync()
        {
            return Task.FromResult(
                new List<Feature>()
                {
                    new Feature(
                        "fake-9-u",
                        "IpWhitelist",
                        "fake-9-me",
                        null,
                        true,
                        null,
                        null,
                        DateTimeOffset.Now,
                        DateTimeOffset.Now,
                        false,
                        new Dictionary<string, object>())
                });
        }

        public Task<Feature> CreateAndGetAsync(string? id, string name, string description, List<string>? categories, bool isEnabled, bool isExperimental, Dictionary<string, object> metadata)
        {
            return Task.FromResult((Feature) null!);
        }

        public Task<Feature> UpdateAndGetAsync(
            string id,
            string name,
            string description,
            List<string>? categories,
            bool isEnabled,
            bool isExperimental)
        {
            throw new NotImplementedException();
        }

        public Task<List<Feature>> GetObjectsAsync(List<string> featureNames)
        {
            return Task.FromResult((List<Feature>) null!);
        }
    }

    public class MockEnableFeatureService : IEnabledFeatureService
    {
        private IEnabledFeatureService _enabledFeatureServiceImplementation;

        public Task<EnabledFeature> EnableAndGetFeatureAsync(
            string sleekflowCompanyId,
            string featureId,
            string roleComparisonCondition = RoleComparisonConditions.EqualsExact,
            FeatureAssociation? featureAssociation = null)
        {
            return Task.FromResult((EnabledFeature) null!);
        }

        public Task<bool> IsFeatureEnabledAsync(
            string sleekflowCompanyId,
            string featureId,
            string roleComparisonCondition = RoleComparisonConditions.EqualsExact,
            FeatureAssociation? featureAssociation = null)
        {
            return Task.FromResult(true);
        }

        public Task<List<EnabledFeature>> GetAllEnabledFeaturesAsync(
            string sleekflowCompanyId,
            string roleComparisonCondition = RoleComparisonConditions.EqualsExact,
            FeatureAssociation? featureAssociation = null)
        {
            return Task.FromResult((List<EnabledFeature>) null!);
        }

        public Task<List<EnabledFeature>> GetAllEnabledFeaturesAsync(
            string sleekflowCompanyId,
            string featureId,
            string roleComparisonCondition = RoleComparisonConditions.EqualsExact,
            FeatureAssociation? featureAssociation = null)
        {
            return Task.FromResult((List<EnabledFeature>) null!);
        }

        public Task<List<EnabledFeature>> GetObjectsAsync(string sleekflowCompanyId)
        {
            return Task.FromResult((List<EnabledFeature>) null!);
        }

        public Task DisableFeatureAsync(
            string sleekflowCompanyId,
            string featureId,
            string roleComparisonCondition = RoleComparisonConditions.EqualsExact,
            FeatureAssociation? featureAssociation = null)
        {
            return Task.FromResult((List<EnabledFeature>) null!);
        }

        public Task<List<EnabledFeature>> GetEnabledFeaturesByFeatureIdAsync(string featureId, string sysTypeName = SysTypeNames.CompanyFeature)
        {
            throw new NotImplementedException();
        }

        public Task<DateTimeOffset?> GetLatestUpdateTimestampByFeatureIdAsync(string featureId, string sysTypeName = SysTypeNames.CompanyFeature)
        {
            throw new NotImplementedException();
        }
    }

    public static IAlbaHost Host { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        var requestInput = MockData.RemoveIpWhitelistsInputMock();
        var serviceScope =
            Application.Host.Services.CreateScope();
        var repositoryFiltersContext = GetRepositoryFiltersContext(serviceScope.ServiceProvider);
        var whitelistService =
            serviceScope.ServiceProvider.GetRequiredService<IIpWhitelistService>();

        repositoryFiltersContext.IsSoftDeleteEnabled = false;
        Task.Run(async () =>
            await whitelistService.RemoveWhitelist("sleekflow-my-company-id"));

        Host.Dispose();
    }

    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }
}