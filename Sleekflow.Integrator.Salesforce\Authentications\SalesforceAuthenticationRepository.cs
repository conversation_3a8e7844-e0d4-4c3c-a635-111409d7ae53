﻿using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.Authentications;

public interface ISalesforceAuthenticationRepository : IRepository<SalesforceAuthentication>
{
}

public class SalesforceAuthenticationRepository
    : BaseRepository<SalesforceAuthentication>,
        ISalesforceAuthenticationRepository,
        ISingletonService
{
    public SalesforceAuthenticationRepository(
        ILogger<BaseRepository<SalesforceAuthentication>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}