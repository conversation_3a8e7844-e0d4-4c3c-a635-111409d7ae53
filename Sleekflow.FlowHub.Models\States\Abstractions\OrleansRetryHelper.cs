namespace Sleekflow.FlowHub.Models.States.Abstractions;

/// <summary>
/// Helper class providing retry logic for Orleans grain operations during cluster instability.
/// </summary>
public static class OrleansRetryHelper
{
    private const int DefaultMaxRetries = 3;
    private static readonly TimeSpan DefaultInitialDelay = TimeSpan.FromMilliseconds(100);

    /// <summary>
    /// Executes an async operation with retry logic for Orleans grain directory issues during shutdown.
    /// </summary>
    /// <typeparam name="T">Return type of the operation.</typeparam>
    /// <param name="operation">The operation to execute.</param>
    /// <param name="maxRetries">Maximum number of retries (default: 3).</param>
    /// <param name="initialDelay">Initial delay between retries (default: 100ms).</param>
    /// <param name="operationName">Name of the operation for logging purposes.</param>
    /// <returns>The result of the operation.</returns>
    public static async Task<T> ExecuteWithRetryAsync<T>(
        Func<Task<T>> operation,
        int maxRetries = DefaultMaxRetries,
        TimeSpan? initialDelay = null,
        string operationName = "operation")
    {
        var delay = initialDelay ?? DefaultInitialDelay;
        Exception? lastException = null;

        for (var i = 0; i < maxRetries; i++)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                lastException = ex;

                // Check if this is an Orleans exception that should be retried
                if (!IsOrleansRetryableException(ex))
                {
                    // Non-Orleans exception, throw immediately
                    throw;
                }

                // If this is the last attempt, throw with context
                if (i == maxRetries - 1)
                {
                    throw new InvalidOperationException(
                        $"Orleans {operationName} failed after {maxRetries} retries due to grain directory issues. " +
                        $"This may indicate cluster instability during scale-in operations. Original error: {ex.Message}",
                        ex);
                }

                // Wait with exponential backoff - the grain directory might be updating
                var currentDelay = TimeSpan.FromMilliseconds(delay.TotalMilliseconds * Math.Pow(2, i));
                await Task.Delay(currentDelay);
            }
        }

        // This should never be reached, but compiler requires it
        throw new InvalidOperationException($"Unexpected state in retry logic for {operationName}", lastException);
    }

    /// <summary>
    /// Executes an async operation with retry logic for Orleans grain directory issues during shutdown.
    /// </summary>
    /// <param name="operation">The operation to execute.</param>
    /// <param name="maxRetries">Maximum number of retries (default: 3).</param>
    /// <param name="initialDelay">Initial delay between retries (default: 100ms).</param>
    /// <param name="operationName">Name of the operation for logging purposes.</param>
    /// <returns>Task representing the operation.</returns>
    public static async Task ExecuteWithRetryAsync(
        Func<Task> operation,
        int maxRetries = DefaultMaxRetries,
        TimeSpan? initialDelay = null,
        string operationName = "operation")
    {
        var delay = initialDelay ?? DefaultInitialDelay;

        for (var i = 0; i < maxRetries; i++)
        {
            try
            {
                await operation();
                return;
            }
            catch (Exception ex)
            {
                // Check if this is an Orleans exception that should be retried
                if (!IsOrleansRetryableException(ex))
                {
                    // Non-Orleans exception, throw immediately
                    throw;
                }

                // If this is the last attempt, throw with context
                if (i == maxRetries - 1)
                {
                    throw new InvalidOperationException(
                        $"Orleans {operationName} failed after {maxRetries} retries due to grain directory issues. " +
                        $"This may indicate cluster instability during scale-in operations. Original error: {ex.Message}",
                        ex);
                }

                // Wait with exponential backoff - the grain directory might be updating
                var currentDelay = TimeSpan.FromMilliseconds(delay.TotalMilliseconds * Math.Pow(2, i));
                await Task.Delay(currentDelay);
            }
        }
    }

    /// <summary>
    /// Determines if an Orleans exception should trigger a retry.
    /// </summary>
    /// <param name="ex">The exception that occurred.</param>
    /// <returns>True if the operation should be retried.</returns>
    private static bool IsOrleansRetryableException(Exception ex)
    {
        var message = ex.Message;
        var exceptionType = ex.GetType().Name;

        // Retry on Orleans-related exceptions that commonly occur during scale-in
        return exceptionType.Contains("Orleans") || message.Contains("Orleans");
    }
}