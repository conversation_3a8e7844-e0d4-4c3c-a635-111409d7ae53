using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.FlowHubEvents;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.RateLimits;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Events;

[TriggerGroup(
    ControllerNames.Events,
    null,
    filterNames: new[]
    {
        "Sleekflow.FlowHub.Cores.IDepthFuncFilter",
    })]
public class OnMetaDetectedOutcomeReceivedEvent : ITrigger
{
    private readonly IFlowHubEventRateLimitProducer _flowHubEventRateLimitProducer;

    public OnMetaDetectedOutcomeReceivedEvent(
        IFlowHubEventRateLimitProducer flowHubEventRateLimitProducer)
    {
        _flowHubEventRateLimitProducer = flowHubEventRateLimitProducer;
    }

    public class OnMetaDetectedOutcomeReceivedEventInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [ValidateObject]
        [JsonProperty("event_body")]
        [Required]
        public OnMetaDetectedOutcomeReceivedEventBody EventBody { get; set; }

        [JsonConstructor]
        public OnMetaDetectedOutcomeReceivedEventInput(
            string sleekflowCompanyId,
            string contactId,
            OnMetaDetectedOutcomeReceivedEventBody eventBody)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            EventBody = eventBody;
        }
    }

    public class OnMetaDetectedOutcomeReceivedEventOutput
    {
    }

    public async Task<OnMetaDetectedOutcomeReceivedEventOutput> F(
        OnMetaDetectedOutcomeReceivedEventInput onMetaDetectedOutcomeReceivedEventInput)
    {
        await _flowHubEventRateLimitProducer.PublishWithRateLimitAsync(
            new OnTriggerEventRequestedEvent(
                onMetaDetectedOutcomeReceivedEventInput.EventBody,
                onMetaDetectedOutcomeReceivedEventInput.ContactId,
                "Contact",
                onMetaDetectedOutcomeReceivedEventInput.SleekflowCompanyId),
            RateLimitCacheKeyBuilder<OnTriggerEventRequestedEvent>.BuildCacheKeyOnCompanyId(
                onMetaDetectedOutcomeReceivedEventInput.SleekflowCompanyId));

        return new OnMetaDetectedOutcomeReceivedEventOutput();
    }
}