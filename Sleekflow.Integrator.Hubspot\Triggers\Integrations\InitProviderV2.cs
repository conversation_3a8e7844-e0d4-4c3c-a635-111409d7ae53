﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class InitProviderV2 : ITrigger
{
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;

    public InitProviderV2(IHubspotAuthenticationService hubspotAuthenticationService)
    {
        _hubspotAuthenticationService = hubspotAuthenticationService;
    }

    public class InitProviderV2Input : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("success_url")]
        [Required]
        public string SuccessUrl { get; set; }

        [JsonProperty("failure_url")]
        [Required]
        public string FailureUrl { get; set; }

        [JsonConstructor]
        public InitProviderV2Input(
            string sleekflowCompanyId,
            string successUrl,
            string failureUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
        }
    }

    public class InitProviderV2Output
    {
        [JsonProperty("hubspot_authentication_url")]
        public string HubspotAuthenticationUrl { get; set; }

        [JsonConstructor]
        public InitProviderV2Output(string hubspotAuthenticationUrl)
        {
            HubspotAuthenticationUrl = hubspotAuthenticationUrl;
        }
    }

    public async Task<InitProviderV2Output> F(
        InitProviderV2Input initProviderInput)
    {
        var redirectUrl =
            await _hubspotAuthenticationService.AuthenticateV2Async(
                initProviderInput.SleekflowCompanyId,
                initProviderInput.SuccessUrl,
                initProviderInput.FailureUrl);

        return new InitProviderV2Output(redirectUrl);
    }
}