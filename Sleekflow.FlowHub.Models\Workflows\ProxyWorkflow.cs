﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Workflows;

public class ProxyWorkflow :
    BaseProxyWorkflow,
    IHasMetadata,
    IHasUpdatedAt,
    IHasCreatedAt,
    IHasSleekflowCompanyId,
    IValidatableObject,
    IHasETag
{
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("workflow_type")]
    public string WorkflowType { get; set; }

    [JsonProperty("dependency_workflow_id")]
    public string? DependencyWorkflowId { get; set; }

    [JsonProperty("workflow_group_id")]
    public string? WorkflowGroupId { get; set; }

    [JsonProperty("triggers")]
    public WorkflowTriggers Triggers { get; set; }

    [JsonProperty("workflow_enrollment_settings")]
    public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

    [JsonProperty("workflow_schedule_settings")]
    public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

    [JsonProperty("activation_status")]
    public string ActivationStatus { get; set; }

    [JsonProperty(AuditEntity.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(AuditEntity.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty("steps")]
    public List<Step> Steps { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonProperty("version")]
    public string Version { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty("manual_enrollment_source")]
    public string? ManualEnrollmentSource { get; set; }

    [JsonConstructor]
    public ProxyWorkflow(
        string id,
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        string name,
        string workflowType,
        string? workflowGroupId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings workflowEnrollmentSettings,
        WorkflowScheduleSettings workflowScheduleSettings,
        string activationStatus,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<Step> steps,
        Dictionary<string, object?> metadata,
        string version,
        string? manualEnrollmentSource = null,
        string? dependencyWorkflowId = null)
    : base(
        sleekflowCompanyId,
        workflowId,
        workflowVersionedId)
    {
        Id = id;
        Name = name;
        WorkflowType = workflowType;
        WorkflowGroupId = workflowGroupId;
        Triggers = triggers;
        WorkflowEnrollmentSettings = workflowEnrollmentSettings;
        WorkflowScheduleSettings = workflowScheduleSettings;
        ActivationStatus = activationStatus;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        Steps = steps;
        Metadata = metadata;
        Version = version;
        ManualEnrollmentSource = manualEnrollmentSource;
        DependencyWorkflowId = dependencyWorkflowId;
    }

    public ProxyWorkflow(
        Workflow workflow,
        List<Step> steps,
        Dictionary<string, object?> metadata)
    : base(workflow.SleekflowCompanyId, workflow.WorkflowId, workflow.WorkflowVersionedId)
    {
        Id = workflow.Id;
        Name = workflow.Name;
        WorkflowType = workflow.WorkflowType;
        WorkflowGroupId = workflow.WorkflowGroupId;
        Triggers = workflow.Triggers;
        WorkflowEnrollmentSettings = workflow.WorkflowEnrollmentSettings;
        WorkflowScheduleSettings = workflow.WorkflowScheduleSettings;
        ActivationStatus = workflow.ActivationStatus;
        CreatedBy = workflow.CreatedBy;
        UpdatedBy = workflow.UpdatedBy;
        CreatedAt = workflow.CreatedAt;
        UpdatedAt = workflow.UpdatedAt;
        Steps = steps is { Count: > 0 } ? steps : workflow.Steps ?? new();
        Metadata = metadata is { Count: > 0 } ? metadata : workflow.Metadata ?? new();
        Version = workflow.Version;
        ETag = workflow.ETag;
        ManualEnrollmentSource = workflow.ManualEnrollmentSource;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        List<ValidationResult> validationResults = new();

        Validator.TryValidateObject(Triggers, new ValidationContext(Triggers), validationResults, true);
        Validator.TryValidateObject(WorkflowEnrollmentSettings, new ValidationContext(WorkflowEnrollmentSettings), validationResults, true);
        Validator.TryValidateObject(WorkflowScheduleSettings, new ValidationContext(WorkflowScheduleSettings), validationResults, true);

        Steps.ForEach(s =>
            Validator.TryValidateObject(
                s,
                new ValidationContext(s),
                validationResults,
                true));

        return validationResults;
    }

    public string GetSubWorkflowType()
    {
        return IsAiWorkflow() ? "ai_workflow" : WorkflowType;
    }

    private bool IsAiWorkflow()
    {
        return Steps
            .OfType<CallStep<EnterAiAgentStepArgs>>()
            .Any(stepArgs => stepArgs.Call == EnterAiAgentStepArgs.CallName);
    }
}