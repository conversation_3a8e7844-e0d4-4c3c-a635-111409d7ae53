openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: "1.0"
servers:
  - url: https://localhost:7080
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/flow-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/flow-hub
    description: Prod Apigw
paths:
  /AiWorkflows/CreateWorkflowByAgentConfig:
    post:
      tags:
        - AiWorkflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkflowByAgentConfigInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateWorkflowByAgentConfigOutputOutput"
  /Blobs/CreateBlobDownloadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBlobDownloadSasUrlsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateBlobDownloadSasUrlsOutputOutput"
  /Blobs/CreateBlobUploadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateBlobUploadSasUrlsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateBlobUploadSasUrlsOutputOutput"
  /Events/OnClickToWhatsAppAdsMessageReceivedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnClickToWhatsAppAdsMessageReceivedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnClickToWhatsAppAdsMessageReceivedEventOutputOutput"
  /Events/OnContactConversationStatusChangedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnContactConversationStatusChangedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnContactConversationStatusChangedEventOutputOutput"
  /Events/OnContactCreatedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnContactCreatedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnContactCreatedEventOutputOutput"
  /Events/OnContactLabelRelationshipsChangedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnContactLabelRelationshipsChangedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnContactLabelRelationshipsChangedEventOutputOutput"
  /Events/OnContactListRelationshipsChangedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnContactListRelationshipsChangedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnContactListRelationshipsChangedEventOutputOutput"
  /Events/OnContactManuallyEnrolledEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnContactManuallyEnrolledEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnContactManuallyEnrolledEventOutputOutput"
  /Events/OnContactUpdatedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnContactUpdatedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnContactUpdatedEventOutputOutput"
  /Events/OnFbIgPostCommentReceivedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnFbIgPostCommentReceivedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnFbIgPostCommentReceivedEventOutputOutput"
  /Events/OnInstagramMediaCommentReceivedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnInstagramMediaCommentReceivedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnInstagramMediaCommentReceivedEventOutputOutput"
  /Events/OnMessageReceivedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnMessageReceivedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnMessageReceivedEventOutputOutput"
  /Events/OnMessageSentEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnMessageSentEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnMessageSentEventOutputOutput"
  /Events/OnMessageStatusUpdatedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnMessageStatusUpdatedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnMessageStatusUpdatedEventOutputOutput"
  /Events/OnMetaDetectedOutcomeReceivedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnMetaDetectedOutcomeReceivedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnMetaDetectedOutcomeReceivedEventOutputOutput"
  /Events/OnTicketUpdatedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnTicketUpdatedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnTicketUpdatedEventOutputOutput"
  /Events/OnWhatsappFlowSubmissionMessageReceivedEvent:
    post:
      tags:
        - Events
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/OnWhatsappFlowSubmissionMessageReceivedEventInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OnWhatsappFlowSubmissionMessageReceivedEventOutputOutput"
  /Executions/CancelWorkflowExecution:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CancelWorkflowExecutionInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CancelWorkflowExecutionOutputOutput"
  /Executions/GetStateStepExecutions:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetStateStepExecutionsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetStateStepExecutionsOutputOutput"
  /Executions/GetUniqueWorkflowExecutionCount:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetUniqueWorkflowExecutionCountInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUniqueWorkflowExecutionCountOutputOutput"
  /Executions/GetWorkflowExecution:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowExecutionInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowExecutionOutputOutput"
  /Executions/GetWorkflowExecutions:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowExecutionsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowExecutionsOutputOutput"
  /Executions/GetWorkflowExecutionsByState:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowExecutionsByStateInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowExecutionsByStateOutputOutput"
  /Executions/GetWorkflowExecutionStatistics:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowExecutionStatisticsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowExecutionStatisticsOutputOutput"
  /Executions/GetWorkflowExecutionUsages:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowExecutionUsagesInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowExecutionUsagesOutputOutput"
  /Executions/GetWorkflowStepExecutions:
    post:
      tags:
        - Executions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowStepExecutionsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowStepExecutionsOutputOutput"
  /FlowHubConfigs/EnableUsageLimitAutoScaling:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EnableUsageLimitAutoScalingInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnableUsageLimitAutoScalingOutputOutput"
  /FlowHubConfigs/EnableUsageLimitOffset:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EnableUsageLimitOffsetInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnableUsageLimitOffsetOutputOutput"
  /FlowHubConfigs/EnrollFlowHub:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EnrollFlowHubInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnrollFlowHubOutputOutput"
  /FlowHubConfigs/GetEnrolledFlowHubConfigs:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetEnrolledFlowHubConfigsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetEnrolledFlowHubConfigsOutputOutput"
  /FlowHubConfigs/GetFlowHubConfig:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetFlowHubConfigInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetFlowHubConfigOutputOutput"
  /FlowHubConfigs/SetRateLimitConfig:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SetRateLimitConfigInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SetRateLimitConfigOutputOutput"
  /FlowHubConfigs/ToggleFlowHubUsageLimit:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ToggleFlowHubUsageLimitInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ToggleFlowHubUsageLimitOutputOutput"
  /FlowHubConfigs/UnenrollFlowHub:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnenrollFlowHubInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnenrollFlowHubOutputOutput"
  /FlowHubConfigs/UpdateFlowHubConfig:
    post:
      tags:
        - FlowHubConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateFlowHubConfigInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateFlowHubConfigOutputOutput"
  /Internals/AssignError:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AssignErrorInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssignErrorOutputOutput"
  /Internals/CheckWorkflowContactEnrolmentCondition:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CheckWorkflowContactEnrolmentConditionInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CheckWorkflowContactEnrolmentConditionOutputOutput"
  /Internals/CompleteStep:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CompleteStepInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CompleteStepOutputOutput"
  /Internals/GetScheduledWorkflowEnrolmentCondition:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetScheduledWorkflowEnrolmentConditionInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetScheduledWorkflowEnrolmentConditionOutputOutput"
  /Internals/SubmitStep:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SubmitStepInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SubmitStepOutputOutput"
  /Internals/UnassignError:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UnassignErrorInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UnassignErrorOutputOutput"
  /Internals/UpdateWorkflowDurablePayload:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateWorkflowDurablePayloadInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateWorkflowDurablePayloadOutputOutput"
  /NeedConfigs/CreateActionNeeds:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateActionNeedsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateActionNeedsOutputOutput"
  /NeedConfigs/CreateActions:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateActionsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateActionsOutputOutput"
  /NeedConfigs/CreateTriggerNeeds:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTriggerNeedsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateTriggerNeedsOutputOutput"
  /NeedConfigs/CreateTriggers:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateTriggersInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateTriggersOutputOutput"
  /NeedConfigs/GetActionNeeds:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetActionNeedsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetActionNeedsOutputOutput"
  /NeedConfigs/GetActions:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetActionsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetActionsOutputOutput"
  /NeedConfigs/GetTriggerNeeds:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetTriggerNeedsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetTriggerNeedsOutputOutput"
  /NeedConfigs/GetTriggers:
    post:
      tags:
        - NeedConfigs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetTriggersInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetTriggersOutputOutput"
  /States/BulkReenrollStates:
    post:
      tags:
        - States
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BulkReenrollStatesInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/BulkReenrollStatesOutputOutput"
  /States/GetObjectStates:
    post:
      tags:
        - States
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetObjectStatesInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetObjectStatesOutputOutput"
  /States/GetStates:
    post:
      tags:
        - States
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetStatesInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetStatesOutputOutput"
  /WorkflowAgentConfigMappings/CreateWorkflowAgentConfigMappings:
    post:
      tags:
        - WorkflowAgentConfigMappings
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkflowAgentConfigMappingsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateWorkflowAgentConfigMappingsOutputOutput"
  /WorkflowAgentConfigMappings/DeleteWorkflowAgentConfigMappingsByAgentConfigId:
    post:
      tags:
        - WorkflowAgentConfigMappings
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeleteWorkflowAgentConfigMappingsByAgentConfigIdInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteWorkflowAgentConfigMappingsByAgentConfigIdOutputOutput"
  /WorkflowAgentConfigMappings/GetWorkflowsByAgentConfigId:
    post:
      tags:
        - WorkflowAgentConfigMappings
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowsByAgentConfigIdInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowsByAgentConfigIdOutputOutput"
  /WorkflowGroups/CreateWorkflowGroup:
    post:
      tags:
        - WorkflowGroups
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkflowGroupInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateWorkflowGroupOutputOutput"
  /WorkflowGroups/DeleteWorkflowGroup:
    post:
      tags:
        - WorkflowGroups
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeleteWorkflowGroupInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteWorkflowGroupOutputOutput"
  /WorkflowGroups/GetWorkflowGroups:
    post:
      tags:
        - WorkflowGroups
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowGroupsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowGroupsOutputOutput"
  /WorkflowGroups/UpdateWorkflowGroup:
    post:
      tags:
        - WorkflowGroups
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateWorkflowGroupInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateWorkflowGroupOutputOutput"
  /Workflows/AssignWorkflowToGroup:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AssignWorkflowToGroupInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AssignWorkflowToGroupOutputOutput"
  /Workflows/CountWorkflows:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CountWorkflowsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CountWorkflowsOutputOutput"
  /Workflows/CreateAiAgentWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAiAgentWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateAiAgentWorkflowOutputOutput"
  /Workflows/CreateWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateWorkflowOutputOutput"
  /Workflows/CreateWorkflowWebhookTrigger:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateWorkflowWebhookTriggerInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateWorkflowWebhookTriggerOutputOutput"
  /Workflows/DeleteVersionedWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeleteVersionedWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteVersionedWorkflowOutputOutput"
  /Workflows/DeleteWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DeleteWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DeleteWorkflowOutputOutput"
  /Workflows/DisableWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DisableWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DisableWorkflowOutputOutput"
  /Workflows/DuplicateWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/DuplicateWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/DuplicateWorkflowOutputOutput"
  /Workflows/EnableWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EnableWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnableWorkflowOutputOutput"
  /Workflows/EnableWorkflowDraft:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EnableWorkflowDraftInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EnableWorkflowDraftOutputOutput"
  /Workflows/GetActiveWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetActiveWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetActiveWorkflowOutputOutput"
  /Workflows/GetLatestWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetLatestWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetLatestWorkflowOutputOutput"
  /Workflows/GetOrCreateWorkflowWebhookTrigger:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetOrCreateWorkflowWebhookTriggerInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetOrCreateWorkflowWebhookTriggerOutputOutput"
  /Workflows/GetVersionedWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetVersionedWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetVersionedWorkflowOutputOutput"
  /Workflows/GetWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowOutputOutput"
  /Workflows/GetWorkflows:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowsOutputOutput"
  /Workflows/GetWorkflowWebhookTriggers:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/GetWorkflowWebhookTriggersInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetWorkflowWebhookTriggersOutputOutput"
  /Workflows/SaveWorkflowDraft:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SaveWorkflowDraftInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SaveWorkflowDraftOutputOutput"
  /Workflows/ScheduleDeleteWorkflows:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ScheduleDeleteWorkflowsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ScheduleDeleteWorkflowsOutputOutput"
  /Workflows/SwapWorkflows:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SwapWorkflowsInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SwapWorkflowsOutputOutput"
  /Workflows/TerminateScheduledWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TerminateScheduledWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TerminateScheduledWorkflowOutputOutput"
  /Workflows/TriggerScheduledWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TriggerScheduledWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/TriggerScheduledWorkflowOutputOutput"
  /Workflows/UpdateAiAgentWorkflowsByAiNode:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateAiAgentWorkflowsByAiNodeInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateAiAgentWorkflowsByAiNodeOutputOutput"
  /Workflows/UpdateWorkflow:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateWorkflowInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateWorkflowOutputOutput"
  /Workflows/UpdateWorkflowWebhookTrigger:
    post:
      tags:
        - Workflows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateWorkflowWebhookTriggerInput"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateWorkflowWebhookTriggerOutputOutput"
components:
  schemas:
    ActionConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        label:
          type: string
          nullable: true
        action_group:
          type: string
          nullable: true
        action_subgroup:
          type: string
          nullable: true
      additionalProperties: false
    ActionMessageObject:
      type: object
      properties:
        flow_token:
          type: string
          nullable: true
        flow_action_data:
          type: object
          additionalProperties: {}
          nullable: true
      additionalProperties: false
    ActionNeedConfig:
      type: object
      properties:
        id:
          type: string
          nullable: true
        arg_name:
          type: string
          nullable: true
        label:
          type: string
          nullable: true
        action_group:
          type: string
          nullable: true
        action_subgroup:
          type: string
          nullable: true
        input_type:
          pattern: single_select|multi_select|text|number|date|textarea|file|whatsapp_template
          type: string
          nullable: true
        input_group:
          type: string
          nullable: true
        validations:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        fields:
          type: array
          items:
            $ref: "#/components/schemas/ActionNeedConfig"
          nullable: true
        options:
          type: array
          items:
            $ref: "#/components/schemas/NeedConfigOption"
          nullable: true
        options_request_path:
          type: string
          nullable: true
        options_request_arg_names:
          type: array
          items:
            type: string
          nullable: true
        placeholder:
          type: string
          nullable: true
        helper_text:
          type: string
          nullable: true
        is_expression_supported:
          type: boolean
        conditions__expr:
          type: string
          nullable: true
      additionalProperties: false
    ActionNeedConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        arg_name:
          type: string
          nullable: true
        label:
          type: string
          nullable: true
        action_group:
          type: string
          nullable: true
        action_subgroup:
          type: string
          nullable: true
        input_type:
          pattern: single_select|multi_select|text|number|date|textarea|file|whatsapp_template
          type: string
          nullable: true
        input_group:
          type: string
          nullable: true
        validations:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        fields:
          type: array
          items:
            $ref: "#/components/schemas/ActionNeedConfig"
          nullable: true
        options:
          type: array
          items:
            $ref: "#/components/schemas/NeedConfigOption"
          nullable: true
        options_request_path:
          type: string
          nullable: true
        options_request_arg_names:
          type: array
          items:
            type: string
          nullable: true
        placeholder:
          type: string
          nullable: true
        helper_text:
          type: string
          nullable: true
        is_expression_supported:
          type: boolean
      additionalProperties: false
    AddInternalNoteToContactInput:
      required:
        - contact_id
        - content
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        content:
          minLength: 1
          type: string
      additionalProperties: false
    AssignErrorInput:
      required:
        - error
        - stack_entries
        - state_id
        - try_catch_step_id
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        try_catch_step_id:
          minLength: 1
          type: string
        stack_entries:
          type: array
          items:
            $ref: "#/components/schemas/StackEntry"
        error:
          $ref: "#/components/schemas/UserFriendlyError"
      additionalProperties: false
    AssignErrorOutput:
      type: object
      additionalProperties: false
    AssignErrorOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/AssignErrorOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AssignWorkflowToGroupInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_versioned_id
      type: object
      properties:
        workflow_versioned_id:
          minLength: 1
          type: string
        workflow_group_id:
          type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    AssignWorkflowToGroupOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    AssignWorkflowToGroupOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/AssignWorkflowToGroupOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AudioMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: "#/components/schemas/MediaMessageObjectProvider"
      additionalProperties: false
    BulkReenrollStatesInput:
      required:
        - reenrollment_period
        - sleekflow_company_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        reenrollment_period:
          minLength: 1
          type: string
      additionalProperties: false
    BulkReenrollStatesOutput:
      type: object
      additionalProperties: false
    BulkReenrollStatesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/BulkReenrollStatesOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ButtonReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    CancelWorkflowExecutionInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - state_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        state_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CancelWorkflowExecutionOutput:
      type: object
      additionalProperties: false
    CancelWorkflowExecutionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CancelWorkflowExecutionOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CapiCustomData:
      required:
        - currency
        - value
      type: object
      properties:
        currency:
          minLength: 1
          type: string
        value:
          minLength: 1
          type: string
      additionalProperties: false
    CapiData:
      required:
        - action_source
        - custom_data
        - event_name
        - event_time
        - messaging_channel
        - user_data
      type: object
      properties:
        event_name:
          minLength: 1
          type: string
        event_time:
          minLength: 1
          type: string
        action_source:
          minLength: 1
          type: string
        messaging_channel:
          minLength: 1
          type: string
        user_data:
          $ref: "#/components/schemas/CapiUserData"
        custom_data:
          $ref: "#/components/schemas/CapiCustomData"
      additionalProperties: false
    CapiUserData:
      required:
        - ctwa_clid
        - whatsapp_business_account_id
      type: object
      properties:
        whatsapp_business_account_id:
          minLength: 1
          type: string
        ctwa_clid:
          minLength: 1
          type: string
      additionalProperties: false
    CheckWorkflowContactEnrolmentConditionInput:
      required:
        - contact_detail
        - contact_id
        - sleekflow_company_id
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        contact_detail:
          $ref: "#/components/schemas/ContactDetail"
        condition:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
      additionalProperties: false
    CheckWorkflowContactEnrolmentConditionOutput:
      type: object
      properties:
        enrolment_condition_satisfied:
          type: boolean
      additionalProperties: false
    CheckWorkflowContactEnrolmentConditionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CheckWorkflowContactEnrolmentConditionOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CompleteStepInput:
      required:
        - execution_status
        - stack_entries
        - state_id
        - step_id
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        step_id:
          minLength: 1
          type: string
        stack_entries:
          type: array
          items:
            $ref: "#/components/schemas/StackEntry"
        execution_status:
          minLength: 1
          type: string
      additionalProperties: false
    CompleteStepOutput:
      type: object
      additionalProperties: false
    CompleteStepOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CompleteStepOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ContactConversation:
      required:
        - conversation_id
        - conversation_status
      type: object
      properties:
        conversation_status:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        last_message_channel:
          type: string
          nullable: true
        last_message_channel_id:
          type: string
          nullable: true
      additionalProperties: false
    ContactDetail:
      required:
        - contact
      type: object
      properties:
        contact:
          type: object
          additionalProperties:
            nullable: true
        contact_owner:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        lists:
          type: array
          items:
            $ref: "#/components/schemas/ContactList"
          nullable: true
        conversation:
          $ref: "#/components/schemas/ContactConversation"
      additionalProperties: false
    ContactLabel:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        color:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ContactList:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        added_at:
          type: string
          format: date-time
        is_imported:
          type: boolean
      additionalProperties: false
    ContactMessageObject:
      type: object
      properties:
        addresses:
          type: array
          items:
            $ref: "#/components/schemas/ContactMessageObjectAddress"
          nullable: true
        birthday:
          type: string
          nullable: true
        emails:
          type: array
          items:
            $ref: "#/components/schemas/ContactMessageObjectEmail"
          nullable: true
        name:
          $ref: "#/components/schemas/ContactMessageObjectName"
        org:
          $ref: "#/components/schemas/ContactMessageObjectOrg"
        ims:
          type: array
          items:
            $ref: "#/components/schemas/ContactMessageObjectIm"
          nullable: true
        phones:
          type: array
          items:
            $ref: "#/components/schemas/ContactMessageObjectPhone"
          nullable: true
        urls:
          type: array
          items:
            $ref: "#/components/schemas/ContactMessageObjectUrl"
          nullable: true
      additionalProperties: false
    ContactMessageObjectAddress:
      type: object
      properties:
        street:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        state:
          type: string
          nullable: true
        zip:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        country_code:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectEmail:
      type: object
      properties:
        email:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectIm:
      type: object
      properties:
        service:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectName:
      type: object
      properties:
        formatted_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        middle_name:
          type: string
          nullable: true
        suffix:
          type: string
          nullable: true
        prefix:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectOrg:
      type: object
      properties:
        company:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        department:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectPhone:
      type: object
      properties:
        phone:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        wa_id:
          type: string
          nullable: true
      additionalProperties: false
    ContactMessageObjectUrl:
      type: object
      properties:
        url:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    ConversationChannelLastMessage:
      type: object
      properties:
        conversation_id:
          type: string
          nullable: true
        message_id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_type:
          type: string
          nullable: true
        message_delivery_type:
          type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    ConversationMessageOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
        created_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    CountWorkflowsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_type:
          type: string
          nullable: true
      additionalProperties: false
    CountWorkflowsOutput:
      type: object
      properties:
        num_of_workflows:
          type: integer
          format: int32
        num_of_active_workflows:
          type: integer
          format: int32
      additionalProperties: false
    CountWorkflowsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CountWorkflowsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateActionNeedsInput:
      required:
        - needs
      type: object
      properties:
        needs:
          type: array
          items:
            $ref: "#/components/schemas/ActionNeedConfig"
        version:
          type: string
          nullable: true
      additionalProperties: false
    CreateActionNeedsOutput:
      type: object
      properties:
        needs:
          type: array
          items:
            $ref: "#/components/schemas/ActionNeedConfig"
          nullable: true
      additionalProperties: false
    CreateActionNeedsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateActionNeedsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateActionsInput:
      required:
        - actions
      type: object
      properties:
        actions:
          type: array
          items:
            $ref: "#/components/schemas/ActionConfigDto"
        version:
          type: string
          nullable: true
      additionalProperties: false
    CreateActionsOutput:
      type: object
      properties:
        actions:
          type: array
          items:
            $ref: "#/components/schemas/ActionConfigDto"
          nullable: true
      additionalProperties: false
    CreateActionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateActionsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateAiAgentWorkflowInput:
      required:
        - agents
        - ai_step_id
        - contact_property
        - dependency_workflow_id
        - labels
        - schemaful_object
        - sleekflow_company_id
        - sleekflow_staff_id
        - whatsapp_cloud_api_configs
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        whatsapp_cloud_api_configs:
          type: array
          items:
            type: object
            additionalProperties: {}
        labels:
          type: array
          items:
            type: object
            additionalProperties: {}
        schemaful_object:
          type: object
          additionalProperties: {}
        agents:
          type: array
          items:
            type: object
            additionalProperties: {}
        contact_property:
          type: object
          additionalProperties: {}
        ai_step_id:
          minLength: 1
          type: string
        dependency_workflow_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateAiAgentWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/ProxyWorkflow"
      additionalProperties: false
    CreateAiAgentWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateAiAgentWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsInput:
      required:
        - blob_names
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_names:
          type: array
          items:
            type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutput:
      type: object
      properties:
        download_blobs:
          type: array
          items:
            $ref: "#/components/schemas/PublicBlob"
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateBlobDownloadSasUrlsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsInput:
      required:
        - blob_type
        - number_of_blobs
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        number_of_blobs:
          maximum: 16
          minimum: 1
          type: integer
          format: int32
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobUploadSasUrlsOutput:
      type: object
      properties:
        upload_blobs:
          type: array
          items:
            $ref: "#/components/schemas/PublicBlob"
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateBlobUploadSasUrlsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateContactInput:
      required:
        - phone_number
        - state_id
        - state_identity
        - workflow_trigger_event_body_event_name
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        phone_number:
          minLength: 1
          type: string
        contact_properties:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        workflow_trigger_event_body_event_name:
          minLength: 1
          type: string
      additionalProperties: false
    CreateContactWithSalesforceUserMappingInput:
      required:
        - phone_number
        - salesforce_connection_id
        - salesforce_user_id_for_mapping
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        phone_number:
          minLength: 1
          type: string
        salesforce_user_id_for_mapping:
          minLength: 1
          type: string
        salesforce_connection_id:
          minLength: 1
          type: string
        contact_properties:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    CreateGoogleSheetsRowInput:
      required:
        - connection_id
        - fields_dict
        - header_row_id
        - spreadsheet_id
        - state_id
        - state_identity
        - worksheet_id
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        spreadsheet_id:
          minLength: 1
          type: string
        worksheet_id:
          minLength: 1
          type: string
        header_row_id:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateHubspotObjectInput:
      required:
        - connection_id
        - entity_type_name
        - fields_dict
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateOrUpdateContactInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        whatsapp_user_display_name:
          type: string
          nullable: true
        facebook_page_id:
          type: string
          nullable: true
        facebook_id:
          type: string
          nullable: true
        facebook_name:
          type: string
          nullable: true
        instagram_page_id:
          type: string
          nullable: true
        instagram_id:
          type: string
          nullable: true
        instagram_username:
          type: string
          nullable: true
      additionalProperties: false
    CreateSalesforceObjectInput:
      required:
        - is_custom_object
        - is_set_owner_by_user_mapping_config
        - object_properties
        - object_type
        - salesforce_connection_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        salesforce_connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        is_set_owner_by_user_mapping_config:
          type: boolean
        sleekflow_user_id_for_mapping:
          type: string
          nullable: true
        object_properties:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateSalesforceObjectV2Input:
      required:
        - connection_id
        - entity_type_name
        - fields_dict
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CreateTicketInput:
      required:
        - assignment_strategy
        - channel_identity_id
        - channel_type
        - contact_id
        - is_unassigned
        - message_origin_channel
        - priority_id
        - state_id
        - state_identity
        - title
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        message_origin_channel:
          minLength: 1
          type: string
        channel_type:
          minLength: 1
          type: string
        channel_identity_id:
          minLength: 1
          type: string
        title:
          minLength: 1
          type: string
        due_date:
          type: string
          format: date-time
          nullable: true
        type_id:
          type: string
          nullable: true
        priority_id:
          minLength: 1
          type: string
        description:
          type: string
          nullable: true
        is_unassigned:
          type: boolean
        assignment_strategy:
          minLength: 1
          type: string
        team_id:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        assignment_counter:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    CreateTicketOutput:
      required:
        - id
      type: object
      properties:
        id:
          minLength: 1
          type: string
      additionalProperties: false
    CreateTriggerNeedsInput:
      required:
        - needs
      type: object
      properties:
        needs:
          type: array
          items:
            $ref: "#/components/schemas/TriggerNeedConfig"
        version:
          type: string
          nullable: true
      additionalProperties: false
    CreateTriggerNeedsOutput:
      type: object
      properties:
        needs:
          type: array
          items:
            $ref: "#/components/schemas/TriggerNeedConfig"
          nullable: true
      additionalProperties: false
    CreateTriggerNeedsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateTriggerNeedsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateTriggersInput:
      required:
        - triggers
      type: object
      properties:
        triggers:
          type: array
          items:
            $ref: "#/components/schemas/TriggerConfig"
        version:
          type: string
          nullable: true
      additionalProperties: false
    CreateTriggersOutput:
      type: object
      properties:
        triggers:
          type: array
          items:
            $ref: "#/components/schemas/TriggerConfig"
          nullable: true
      additionalProperties: false
    CreateTriggersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateTriggersOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowAgentConfigMappingsInput:
      required:
        - agent_config_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_ids
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        agent_config_id:
          minLength: 1
          type: string
        workflow_ids:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowAgentConfigMappingsOutput:
      type: object
      properties:
        workflow_agent_config_mappings:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowAgentConfigMapping"
          nullable: true
      additionalProperties: false
    CreateWorkflowAgentConfigMappingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateWorkflowAgentConfigMappingsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowByAgentConfigInput:
      required:
        - agent_config_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        agent_config_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowByAgentConfigOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/ProxyWorkflow"
      additionalProperties: false
    CreateWorkflowByAgentConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateWorkflowByAgentConfigOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowGroupInput:
      required:
        - name
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        name:
          maxLength: 100
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowGroupOutput:
      required:
        - workflow_group
      type: object
      properties:
        workflow_group:
          $ref: "#/components/schemas/WorkflowGroupDto"
      additionalProperties: false
    CreateWorkflowGroupOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateWorkflowGroupOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowInput:
      required:
        - metadata
        - name
        - sleekflow_company_id
        - sleekflow_staff_id
        - steps
        - triggers
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        triggers:
          $ref: "#/components/schemas/WorkflowTriggers"
        workflow_enrollment_settings:
          $ref: "#/components/schemas/WorkflowEnrollmentSettings"
        workflow_schedule_settings:
          $ref: "#/components/schemas/WorkflowScheduleSettings"
        steps:
          maxItems: 1024
          minItems: 1
          type: array
          items: {}
        name:
          maxLength: 100
          minLength: 1
          type: string
        workflow_type:
          type: string
          nullable: true
        workflow_group_id:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
        version:
          pattern: "^v[1-2]$"
          type: string
          nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    CreateWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowWebhookTriggerInput:
      required:
        - object_id_expression
        - object_type
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        object_id_expression:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateWorkflowWebhookTriggerOutput:
      type: object
      properties:
        workflow_webhook_trigger:
          $ref: "#/components/schemas/WorkflowWebhookTrigger"
      additionalProperties: false
    CreateWorkflowWebhookTriggerOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/CreateWorkflowWebhookTriggerOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateZohoObjectInput:
      required:
        - connection_id
        - entity_type_name
        - fields_dict
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    CurrencyMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        amount_1000:
          type: integer
          format: int32
      additionalProperties: false
    CustomData:
      type: object
      properties:
        currency:
          type: string
          nullable: true
        value:
          type: number
          format: decimal
      additionalProperties: false
    DateTimeMessageObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
      additionalProperties: false
    DeleteVersionedWorkflowInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteVersionedWorkflowOutput:
      type: object
      additionalProperties: false
    DeleteVersionedWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/DeleteVersionedWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteWorkflowAgentConfigMappingsByAgentConfigIdInput:
      required:
        - agent_config_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        agent_config_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteWorkflowAgentConfigMappingsByAgentConfigIdOutput:
      type: object
      additionalProperties: false
    DeleteWorkflowAgentConfigMappingsByAgentConfigIdOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/DeleteWorkflowAgentConfigMappingsByAgentConfigIdOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteWorkflowGroupInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_group_id
      type: object
      properties:
        workflow_group_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteWorkflowGroupOutput:
      type: object
      additionalProperties: false
    DeleteWorkflowGroupOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/DeleteWorkflowGroupOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteWorkflowInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeleteWorkflowOutput:
      type: object
      additionalProperties: false
    DeleteWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/DeleteWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DisableWorkflowInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DisableWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    DisableWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/DisableWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DocumentMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: "#/components/schemas/MediaMessageObjectProvider"
      additionalProperties: false
    DuplicateWorkflowInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DuplicateWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    DuplicateWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/DuplicateWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DurablePayload:
      type: object
      properties:
        id:
          type: string
          nullable: true
        statusQueryGetUri:
          type: string
          nullable: true
        sendEventPostUri:
          type: string
          nullable: true
        terminatePostUri:
          type: string
          nullable: true
        rewindPostUri:
          type: string
          nullable: true
        purgeHistoryDeleteUri:
          type: string
          nullable: true
        restartPostUri:
          type: string
          nullable: true
      additionalProperties: false
    EnableUsageLimitAutoScalingInput:
      required:
        - is_enable_usage_limit_auto_scaling
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        is_enable_usage_limit_auto_scaling:
          type: boolean
      additionalProperties: false
    EnableUsageLimitAutoScalingOutput:
      type: object
      properties:
        flow_hub_config:
          $ref: "#/components/schemas/FlowHubConfig"
      additionalProperties: false
    EnableUsageLimitAutoScalingOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/EnableUsageLimitAutoScalingOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnableUsageLimitOffsetInput:
      required:
        - is_enable_usage_limit_offset
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        is_enable_usage_limit_offset:
          type: boolean
      additionalProperties: false
    EnableUsageLimitOffsetOutput:
      type: object
      properties:
        flow_hub_config:
          $ref: "#/components/schemas/FlowHubConfig"
      additionalProperties: false
    EnableUsageLimitOffsetOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/EnableUsageLimitOffsetOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnableWorkflowDraftInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    EnableWorkflowDraftOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    EnableWorkflowDraftOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/EnableWorkflowDraftOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnableWorkflowInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    EnableWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    EnableWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/EnableWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnrollFlowHubInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        origin:
          type: string
          nullable: true
      additionalProperties: false
    EnrollFlowHubOutput:
      type: object
      properties:
        flow_hub_config:
          $ref: "#/components/schemas/FlowHubConfig"
      additionalProperties: false
    EnrollFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/EnrollFlowHubOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EvaluationContext:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        origin:
          type: string
          nullable: true
      additionalProperties: false
    FacebookMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: "#/components/schemas/FacebookPageMessengerMessageObject"
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    FacebookPageMessengerAttachmentObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: "#/components/schemas/FacebookPageMessengerPayloadObject"
      additionalProperties: false
    FacebookPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: "#/components/schemas/FacebookPageMessengerAttachmentObject"
        text:
          type: string
          nullable: true
      additionalProperties: false
    FacebookPageMessengerPayloadObject:
      type: object
      properties:
        url:
          type: string
          nullable: true
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    FacebookPageMessengerSendMessageInputFromTo:
      allOf:
        - $ref: "#/components/schemas/SendMessageInputFromTo"
        - type: object
          properties:
            from_facebook_page_id:
              type: string
              nullable: true
            to_facebook_id:
              type: string
              nullable: true
            facebook_post_comment_details:
              $ref: "#/components/schemas/FacebookPostCommentDetailsObject"
          additionalProperties: false
    FacebookPostCommentDetailsObject:
      type: object
      properties:
        from_facebook_post_id:
          type: string
          nullable: true
        to_facebook_comment_id:
          type: string
          nullable: true
      additionalProperties: false
    FlowHubConfig:
      type: object
      properties:
        is_enrolled:
          type: boolean
        is_usage_limit_enabled:
          type: boolean
        is_usage_limit_offset_enabled:
          type: boolean
        is_usage_limit_auto_scaling_enabled:
          type: boolean
        usage_limit:
          $ref: "#/components/schemas/UsageLimit"
        usage_limit_offset:
          $ref: "#/components/schemas/UsageLimitOffset"
        _etag:
          type: string
          nullable: true
        origin:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    FromFacebookSender:
      type: object
      properties:
        facebook_id:
          type: string
          nullable: true
        facebook_name:
          type: string
          nullable: true
      additionalProperties: false
    FromInstagramSender:
      type: object
      properties:
        instagram_id:
          type: string
          nullable: true
        instagram_user_name:
          type: string
          nullable: true
      additionalProperties: false
    GetActionNeedsInput:
      required:
        - action_group
        - action_subgroup
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        action_group:
          minLength: 1
          type: string
        action_subgroup:
          minLength: 1
          type: string
        version:
          type: string
          nullable: true
        parameters:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    GetActionNeedsOutput:
      type: object
      properties:
        needs:
          type: array
          items:
            $ref: "#/components/schemas/ActionNeedConfigDto"
          nullable: true
      additionalProperties: false
    GetActionNeedsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetActionNeedsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetActionsInput:
      type: object
      properties:
        version:
          type: string
          nullable: true
      additionalProperties: false
    GetActionsOutput:
      type: object
      properties:
        actions:
          type: array
          items:
            $ref: "#/components/schemas/ActionConfigDto"
          nullable: true
      additionalProperties: false
    GetActionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetActionsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetActiveWorkflowInput:
      required:
        - sleekflow_company_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetActiveWorkflowOutput:
      type: object
      properties:
        active_workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    GetActiveWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetActiveWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetCompanyUsageCycleInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetCompanyUsageCycleOutput:
      type: object
      properties:
        from_date_time:
          type: string
          format: date-time
        to_date_time:
          type: string
          format: date-time
      additionalProperties: false
    GetContactConversationInput:
      required:
        - sleekflow_company_id
        - sleekflow_contact_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_contact_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactConversationOutput:
      type: object
      properties:
        conversation:
          $ref: "#/components/schemas/ContactConversation"
      additionalProperties: false
    GetContactIdByEmailInput:
      required:
        - email
        - sleekflow_company_id
        - state_id
        - workflow_id
        - workflow_name
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        workflow_name:
          minLength: 1
          type: string
        state_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactIdByPhoneNumberInput:
      required:
        - phone_number
        - sleekflow_company_id
        - state_id
        - workflow_id
        - workflow_name
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        phone_number:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        workflow_name:
          minLength: 1
          type: string
        state_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactIdOutput:
      type: object
      properties:
        contact_id:
          type: string
          nullable: true
      additionalProperties: false
    GetContactInput:
      required:
        - sleekflow_company_id
        - sleekflow_contact_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_contact_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactListsInput:
      required:
        - sleekflow_company_id
        - sleekflow_contact_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_contact_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactListsOutput:
      type: object
      properties:
        lists:
          type: array
          items:
            $ref: "#/components/schemas/ContactList"
          nullable: true
      additionalProperties: false
    GetContactOutput:
      type: object
      properties:
        contact:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    GetContactPropertiesInput:
      required:
        - contact_id
        - state_identity
      type: object
      properties:
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetContactPropertiesOutput:
      type: object
      properties:
        contact_properties:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    GetContactsByBatchInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        last_contact_created_at:
          type: string
          format: date-time
          nullable: true
        last_contact_id:
          type: string
          nullable: true
        batch_size:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    GetContactsByBatchOutput:
      required:
        - contacts
      type: object
      properties:
        contacts:
          type: object
          additionalProperties:
            $ref: "#/components/schemas/ContactDetail"
        next_batch:
          $ref: "#/components/schemas/NextBatch"
      additionalProperties: false
    GetConversationChannelLastMessageInput:
      required:
        - channel
        - channel_id
        - conversation_id
        - is_sent_from_sleekflow
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        channel:
          minLength: 1
          type: string
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        is_sent_from_sleekflow:
          type: boolean
      additionalProperties: false
    GetConversationChannelLastMessageOutput:
      type: object
      properties:
        last_message:
          $ref: "#/components/schemas/ConversationChannelLastMessage"
      additionalProperties: false
    GetConversationLastMessagesInput:
      required:
        - contact_id
        - limit
        - offset
        - state_identity
        - target_channels
      type: object
      properties:
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        target_channels:
          type: array
          items:
            type: string
        target_channel_id:
          type: string
          nullable: true
        offset:
          maximum: 999
          minimum: 0
          type: integer
          format: int32
        limit:
          maximum: 999
          minimum: 0
          type: integer
          format: int32
        retrieval_window_timestamp:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    GetConversationLastMessagesOutput:
      type: object
      properties:
        conversation_messages:
          type: array
          items:
            $ref: "#/components/schemas/ConversationMessageOutput"
          nullable: true
      additionalProperties: false
    GetEnrolledFlowHubConfigsInput:
      required:
        - limit
      type: object
      properties:
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 100
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetEnrolledFlowHubConfigsOutput:
      type: object
      properties:
        flow_hub_configs:
          type: array
          items:
            $ref: "#/components/schemas/FlowHubConfig"
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetEnrolledFlowHubConfigsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetEnrolledFlowHubConfigsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFacebookPageAccessTokenInput:
      required:
        - facebook_page_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_page_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetFacebookPageAccessTokenOutput:
      type: object
      properties:
        facebook_page_access_token:
          type: string
          nullable: true
      additionalProperties: false
    GetFlowHubConfigInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
      additionalProperties: false
    GetFlowHubConfigOutput:
      type: object
      properties:
        flow_hub_config:
          $ref: "#/components/schemas/FlowHubConfig"
      additionalProperties: false
    GetFlowHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetFlowHubConfigOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetInstagramPageAccessTokenInput:
      required:
        - instagram_page_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        instagram_page_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetInstagramPageAccessTokenOutput:
      type: object
      properties:
        instagram_page_access_token:
          type: string
          nullable: true
      additionalProperties: false
    GetIntelligentHubUsageFilterInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetIntelligentHubUsageFilterOutput:
      type: object
      properties:
        intelligent_hub_usage_filter:
          $ref: "#/components/schemas/IntelligentHubUsageFilterOutput"
      additionalProperties: false
    GetLatestWorkflowInput:
      required:
        - sleekflow_company_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetLatestWorkflowOutput:
      type: object
      properties:
        latest_workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    GetLatestWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetLatestWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectStatesInput:
      required:
        - limit
        - object_id
        - object_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          type: integer
          format: int32
      additionalProperties: false
    GetObjectStatesOutput:
      type: object
      properties:
        states:
          type: array
          items:
            $ref: "#/components/schemas/ProxyStateDto"
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectStatesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetObjectStatesOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetOrCreateContactIdByEmailInput:
      required:
        - email
        - sleekflow_company_id
        - state_id
        - workflow_id
        - workflow_name
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        workflow_name:
          minLength: 1
          type: string
        state_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetOrCreateContactIdByPhoneNumberInput:
      required:
        - phone_number
        - sleekflow_company_id
        - state_id
        - workflow_id
        - workflow_name
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        phone_number:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        workflow_name:
          minLength: 1
          type: string
        state_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetOrCreateContactIdOutput:
      type: object
      properties:
        contact_id:
          type: string
          nullable: true
      additionalProperties: false
    GetOrCreateWorkflowWebhookTriggerInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetOrCreateWorkflowWebhookTriggerOutput:
      type: object
      properties:
        webhook_trigger_id:
          type: string
          nullable: true
        validation_token:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        object_id_expression:
          type: string
          nullable: true
        webhook_url:
          type: string
          nullable: true
        sample_post_request:
          type: string
          nullable: true
      additionalProperties: false
    GetOrCreateWorkflowWebhookTriggerOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetOrCreateWorkflowWebhookTriggerOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetScheduledWorkflowEnrolmentConditionInput:
      required:
        - sleekflow_company_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetScheduledWorkflowEnrolmentConditionOutput:
      required:
        - condition
        - sleekflow_company_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        condition:
          minLength: 1
          type: string
        workflow_name:
          type: string
          nullable: true
      additionalProperties: false
    GetScheduledWorkflowEnrolmentConditionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetScheduledWorkflowEnrolmentConditionOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetStaffInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetStaffOutput:
      type: object
      properties:
        staff:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    GetStateStepExecutionsInput:
      required:
        - limit
        - sleekflow_company_id
        - state_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
        state_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetStateStepExecutionsOutput:
      type: object
      properties:
        step_executions:
          type: array
          items:
            $ref: "#/components/schemas/StepExecutionDto"
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetStateStepExecutionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetStateStepExecutionsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetStatesInput:
      required:
        - filters
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          type: integer
          format: int32
        filters:
          $ref: "#/components/schemas/GetStatesInputFilters"
      additionalProperties: false
    GetStatesInputFilters:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
        state_status:
          type: string
          nullable: true
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    GetStatesOutput:
      type: object
      properties:
        states:
          type: array
          items:
            $ref: "#/components/schemas/ProxyStateDto"
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetStatesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetStatesOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTicketExportDataInput:
      required:
        - id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        id:
          minLength: 1
          type: string
      additionalProperties: false
    GetTicketExportDataOutput:
      type: object
      properties:
        ticket:
          $ref: "#/components/schemas/GetTicketExportDataOutputTicket"
      additionalProperties: false
    GetTicketExportDataOutputChannel:
      type: object
      properties:
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
        channel_name:
          type: string
          nullable: true
      additionalProperties: false
    GetTicketExportDataOutputTicket:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_by_email:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        channel:
          $ref: "#/components/schemas/GetTicketExportDataOutputChannel"
        status_id:
          type: string
          nullable: true
        status_name:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        priority_name:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type_id:
          type: string
          nullable: true
        type_name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        first_assignee:
          type: string
          nullable: true
        first_assignee_email:
          type: string
          nullable: true
        current_assignee:
          type: string
          nullable: true
        current_assignee_email:
          type: string
          nullable: true
        contact_name:
          type: string
          nullable: true
        contact_phone_number:
          type: string
          nullable: true
        contact_email:
          type: string
          nullable: true
        first_respondent:
          type: string
          nullable: true
        first_respondent_email:
          type: string
          nullable: true
        first_response_timestamp:
          type: string
          format: date-time
          nullable: true
        resolution_agent:
          type: string
          nullable: true
        resolution_agent_email:
          type: string
          nullable: true
        resolution_timestamp:
          type: string
          format: date-time
          nullable: true
        ticket_reassigned_before_resolution:
          type: boolean
      additionalProperties: false
    GetTriggerNeedsInput:
      required:
        - trigger_id
      type: object
      properties:
        trigger_id:
          minLength: 1
          type: string
        version:
          type: string
          nullable: true
        parameters:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    GetTriggerNeedsOutput:
      type: object
      properties:
        needs:
          type: array
          items:
            $ref: "#/components/schemas/TriggerNeedConfigDto"
          nullable: true
      additionalProperties: false
    GetTriggerNeedsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetTriggerNeedsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTriggersInput:
      type: object
      properties:
        version:
          type: string
          nullable: true
      additionalProperties: false
    GetTriggersOutput:
      type: object
      properties:
        triggers:
          type: array
          items:
            $ref: "#/components/schemas/TriggerConfigDto"
          nullable: true
      additionalProperties: false
    GetTriggersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetTriggersOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUniqueWorkflowExecutionCountInput:
      required:
        - execution_from_date_time
        - execution_to_date_time
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        execution_from_date_time:
          type: string
          format: date-time
        execution_to_date_time:
          type: string
          format: date-time
        workflow_type:
          type: string
          nullable: true
      additionalProperties: false
    GetUniqueWorkflowExecutionCountOutput:
      type: object
      properties:
        unique_workflow_execution_count:
          type: integer
          format: int32
      additionalProperties: false
    GetUniqueWorkflowExecutionCountOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetUniqueWorkflowExecutionCountOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetVersionedWorkflowInput:
      required:
        - sleekflow_company_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetVersionedWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    GetVersionedWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetVersionedWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionInput:
      required:
        - sleekflow_company_id
        - state_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        state_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWorkflowExecutionOutput:
      type: object
      properties:
        workflow_execution:
          $ref: "#/components/schemas/WorkflowExecutionDto"
      additionalProperties: false
    GetWorkflowExecutionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowExecutionOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionStatisticsInput:
      required:
        - filters
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        filters:
          $ref: "#/components/schemas/GetWorkflowExecutionStatisticsInputFilters"
      additionalProperties: false
    GetWorkflowExecutionStatisticsInputFilters:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
        workflow_type:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionStatisticsOutput:
      type: object
      properties:
        num_of_started_workflows:
          type: integer
          format: int64
        num_of_complete_workflows:
          type: integer
          format: int64
        num_of_cancelled_workflows:
          type: integer
          format: int64
        num_of_blocked_workflows:
          type: integer
          format: int64
        num_of_failed_workflows:
          type: integer
          format: int64
        num_of_restricted_workflows:
          type: integer
          format: int64
      additionalProperties: false
    GetWorkflowExecutionStatisticsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowExecutionStatisticsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionUsagesInput:
      required:
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        filters:
          $ref: "#/components/schemas/WorkflowExecutionUsageFilters"
        limit:
          maximum: 15
          minimum: 1
          type: integer
          format: int32
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionUsagesOutput:
      type: object
      properties:
        workflow_execution_usages:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowExecutionUsageListDto"
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionUsagesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowExecutionUsagesOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionsByStateInput:
      required:
        - filters
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
        filters:
          $ref: "#/components/schemas/GetWorkflowExecutionsByStateInputFilters"
      additionalProperties: false
    GetWorkflowExecutionsByStateInputFilters:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
        state_status:
          type: string
          nullable: true
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionsByStateOutput:
      type: object
      properties:
        workflow_executions:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowExecutionDto"
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionsByStateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowExecutionsByStateOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionsInput:
      required:
        - filters
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
        filters:
          $ref: "#/components/schemas/GetWorkflowExecutionsInputFilters"
      additionalProperties: false
    GetWorkflowExecutionsInputFilters:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
        workflow_execution_status:
          type: string
          nullable: true
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionsOutput:
      type: object
      properties:
        workflow_executions:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowExecutionListDto"
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowExecutionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowExecutionsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowGroupsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWorkflowGroupsOutput:
      type: object
      properties:
        workflow_groups:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowGroupDto"
          nullable: true
      additionalProperties: false
    GetWorkflowGroupsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowGroupsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowInput:
      required:
        - sleekflow_company_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWorkflowOutput:
      type: object
      properties:
        active_workflow:
          $ref: "#/components/schemas/WorkflowDto"
        versioned_workflows:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowDto"
          nullable: true
      additionalProperties: false
    GetWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowStepExecutionsInput:
      required:
        - limit
        - sleekflow_company_id
        - step_id
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        step_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWorkflowStepExecutionsOutput:
      type: object
      properties:
        step_executions:
          type: array
          items:
            $ref: "#/components/schemas/StepExecutionDto"
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowStepExecutionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowStepExecutionsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowWebhookTriggersInput:
      required:
        - sleekflow_company_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWorkflowWebhookTriggersOutput:
      type: object
      properties:
        workflow_webhook_triggers:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowWebhookTrigger"
          nullable: true
      additionalProperties: false
    GetWorkflowWebhookTriggersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowWebhookTriggersOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowsByAgentConfigIdInput:
      required:
        - agent_config_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        agent_config_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetWorkflowsByAgentConfigIdOutput:
      type: object
      properties:
        workflows:
          type: array
          items:
            $ref: "#/components/schemas/ProxyWorkflow"
          nullable: true
      additionalProperties: false
    GetWorkflowsByAgentConfigIdOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowsByAgentConfigIdOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowsInput:
      required:
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 50
          minimum: 1
          type: integer
          format: int32
        search_name:
          type: string
          nullable: true
        workflow_filters:
          $ref: "#/components/schemas/WorkflowFilters"
        statistics_filters:
          $ref: "#/components/schemas/WorkflowExecutionStatisticsFilters"
      additionalProperties: false
    GetWorkflowsOutput:
      type: object
      properties:
        workflows:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowListDto"
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkflowsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/GetWorkflowsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ImageMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: "#/components/schemas/MediaMessageObjectProvider"
      additionalProperties: false
    InstagramMediaCommentDetailsObject:
      type: object
      properties:
        from_instagram_media_id:
          type: string
          nullable: true
        to_instagram_comment_id:
          type: string
          nullable: true
      additionalProperties: false
    InstagramMessengerMessageObject:
      type: object
      properties:
        message:
          $ref: "#/components/schemas/InstagramPageMessengerMessageObject"
        messaging_type:
          type: string
          nullable: true
        tag:
          type: string
          nullable: true
      additionalProperties: false
    InstagramPageMessengerAttachmentDataObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        payload:
          $ref: "#/components/schemas/InstagramPageMessengerPayloadObject"
        is_reusable:
          type: boolean
          nullable: true
      additionalProperties: false
    InstagramPageMessengerMessageObject:
      type: object
      properties:
        attachment:
          $ref: "#/components/schemas/InstagramPageMessengerAttachmentDataObject"
        text:
          type: string
          nullable: true
      additionalProperties: false
    InstagramPageMessengerPayloadObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
      additionalProperties: false
    InstagramPageMessengerSendMessageInputFromTo:
      allOf:
        - $ref: "#/components/schemas/SendMessageInputFromTo"
        - type: object
          properties:
            from_instagram_page_id:
              type: string
              nullable: true
            to_instagram_id:
              type: string
              nullable: true
            instagram_media_comment_details:
              $ref: "#/components/schemas/InstagramMediaCommentDetailsObject"
          additionalProperties: false
    IntelligentHubUsageFilterOutput:
      type: object
      properties:
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    InteractiveMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        header:
          $ref: "#/components/schemas/InteractiveMessageObjectHeader"
        body:
          $ref: "#/components/schemas/InteractiveMessageObjectBody"
        footer:
          $ref: "#/components/schemas/InteractiveMessageObjectFooter"
        action:
          $ref: "#/components/schemas/InteractiveMessageObjectAction"
      additionalProperties: false
    InteractiveMessageObjectAction:
      type: object
      properties:
        button:
          type: string
          nullable: true
        buttons:
          type: array
          items:
            $ref: "#/components/schemas/InteractiveMessageObjectActionButton"
          nullable: true
        sections:
          type: array
          items:
            $ref: "#/components/schemas/InteractiveMessageObjectActionSection"
          nullable: true
        catalog_id:
          type: string
          nullable: true
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionButton:
      type: object
      properties:
        type:
          type: string
          nullable: true
        reply:
          $ref: "#/components/schemas/InteractiveMessageObjectActionButtonReply"
      additionalProperties: false
    InteractiveMessageObjectActionButtonReply:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionSection:
      type: object
      properties:
        title:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: "#/components/schemas/InteractiveMessageObjectActionSectionProductItem"
          nullable: true
        rows:
          type: array
          items:
            $ref: "#/components/schemas/InteractiveMessageObjectActionSectionRow"
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionSectionProductItem:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectActionSectionRow:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectBody:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectFooter:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    InteractiveMessageObjectHeader:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        video:
          $ref: "#/components/schemas/VideoMessageObject"
        image:
          $ref: "#/components/schemas/ImageMessageObject"
        document:
          $ref: "#/components/schemas/DocumentMessageObject"
      additionalProperties: false
    InteractiveReplyMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        button_reply:
          $ref: "#/components/schemas/ButtonReplyMessageObject"
        list_reply:
          $ref: "#/components/schemas/ListReplyMessageObject"
        nfm_reply:
          $ref: "#/components/schemas/NfmReplyMessageObject"
      additionalProperties: false
    LightweightWorkflowDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        workflow_type:
          type: string
          nullable: true
        workflow_group_id:
          type: string
          nullable: true
        triggers:
          $ref: "#/components/schemas/WorkflowTriggers"
        workflow_enrollment_settings:
          $ref: "#/components/schemas/WorkflowEnrollmentSettings"
        workflow_schedule_settings:
          $ref: "#/components/schemas/WorkflowScheduleSettings"
        activation_status:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    LineMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    ListReplyMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    LiveChatMessageObject:
      type: object
      properties:
        message:
          type: string
          nullable: true
      additionalProperties: false
    LocationMessageObject:
      type: object
      properties:
        latitude:
          type: number
          format: double
          nullable: true
        longitude:
          type: number
          format: double
          nullable: true
        name:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
      additionalProperties: false
    MediaMessageObjectProvider:
      type: object
      properties:
        name:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        config:
          $ref: "#/components/schemas/MediaMessageObjectProviderConfig"
      additionalProperties: false
    MediaMessageObjectProviderConfig:
      type: object
      properties:
        basic:
          $ref: "#/components/schemas/MediaMessageObjectProviderConfigBasic"
        bearer:
          $ref: "#/components/schemas/MediaMessageObjectProviderConfigBearer"
      additionalProperties: false
    MediaMessageObjectProviderConfigBasic:
      type: object
      properties:
        username:
          type: string
          nullable: true
        password:
          type: string
          nullable: true
      additionalProperties: false
    MediaMessageObjectProviderConfigBearer:
      type: object
      properties:
        bearer:
          type: string
          nullable: true
      additionalProperties: false
    MessageBody:
      type: object
      properties:
        audio_message:
          $ref: "#/components/schemas/AudioMessageObject"
        contacts_message:
          type: array
          items:
            $ref: "#/components/schemas/ContactMessageObject"
          nullable: true
        currency_message:
          $ref: "#/components/schemas/CurrencyMessageObject"
        document_message:
          $ref: "#/components/schemas/DocumentMessageObject"
        image_message:
          $ref: "#/components/schemas/ImageMessageObject"
        location_message:
          $ref: "#/components/schemas/LocationMessageObject"
        reaction_message:
          $ref: "#/components/schemas/ReactionMessageObject"
        text_message:
          $ref: "#/components/schemas/TextMessageObject"
        video_message:
          $ref: "#/components/schemas/VideoMessageObject"
        date_time_message:
          $ref: "#/components/schemas/DateTimeMessageObject"
        interactive_message:
          $ref: "#/components/schemas/InteractiveMessageObject"
        template_message:
          $ref: "#/components/schemas/TemplateMessageObject"
        interactive_reply_message:
          $ref: "#/components/schemas/InteractiveReplyMessageObject"
        order_message:
          $ref: "#/components/schemas/OrderMessageObject"
        facebook_messenger_message:
          $ref: "#/components/schemas/FacebookMessengerMessageObject"
        instagram_messenger_message:
          $ref: "#/components/schemas/InstagramMessengerMessageObject"
        telegram_messenger_message:
          $ref: "#/components/schemas/TelegramMessengerMessageObject"
        wechat_messenger_message:
          $ref: "#/components/schemas/WeChatMessengerMessageObject"
        live_chat_message:
          $ref: "#/components/schemas/LiveChatMessageObject"
        viber_message:
          $ref: "#/components/schemas/ViberMessageObject"
        line_message:
          $ref: "#/components/schemas/LineMessageObject"
        sms_message:
          $ref: "#/components/schemas/SmsMessageObject"
      additionalProperties: false
    NeedConfigOption:
      type: object
      properties:
        id:
          type: string
          nullable: true
        label:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        icon:
          type: string
          nullable: true
      additionalProperties: false
    NextBatch:
      type: object
      properties:
        last_contact_created_at:
          type: string
          format: date-time
          nullable: true
        last_contact_id:
          type: string
          nullable: true
      additionalProperties: false
    NfmReplyMessageObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        response_json:
          type: string
          nullable: true
      additionalProperties: false
    OnClickToWhatsAppAdsMessageReceivedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - is_new_contact
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: "#/components/schemas/OnClickToWhatsAppAdsMessageReceivedEventBodyMessage"
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        is_new_contact:
          type: boolean
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnClickToWhatsAppAdsMessageReceivedEventBodyMessage:
      type: object
      properties:
        id:
          type: string
          nullable: true
        ctwa_clid:
          type: string
          nullable: true
        source_id:
          type: string
          nullable: true
        source_url:
          type: string
          nullable: true
        headline:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: "#/components/schemas/MessageBody"
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnClickToWhatsAppAdsMessageReceivedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnClickToWhatsAppAdsMessageReceivedEventBody"
      additionalProperties: false
    OnClickToWhatsAppAdsMessageReceivedEventOutput:
      type: object
      additionalProperties: false
    OnClickToWhatsAppAdsMessageReceivedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnClickToWhatsAppAdsMessageReceivedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnContactConversationStatusChangedEventBody:
      required:
        - contact
        - contact_id
        - conversation_id
        - created_at
        - new_status
        - original_status
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        original_status:
          minLength: 1
          type: string
        new_status:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactConversationStatusChangedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnContactConversationStatusChangedEventBody"
      additionalProperties: false
    OnContactConversationStatusChangedEventOutput:
      type: object
      additionalProperties: false
    OnContactConversationStatusChangedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnContactConversationStatusChangedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnContactCreatedEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - created_contact
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        created_contact:
          type: object
          additionalProperties:
            nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactCreatedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnContactCreatedEventBody"
      additionalProperties: false
    OnContactCreatedEventOutput:
      type: object
      additionalProperties: false
    OnContactCreatedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnContactCreatedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnContactEnrolledEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactLabelRelationshipsChangedEventBody:
      required:
        - added_to_labels
        - contact
        - contact_id
        - created_at
        - labels
        - removed_from_labels
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        added_to_labels:
          type: array
          items:
            $ref: "#/components/schemas/OnContactLabelRelationshipsChangedEventBodyContactLabel"
        removed_from_labels:
          type: array
          items:
            $ref: "#/components/schemas/OnContactLabelRelationshipsChangedEventBodyContactLabel"
        labels:
          type: array
          items:
            $ref: "#/components/schemas/OnContactLabelRelationshipsChangedEventBodyContactLabel"
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactLabelRelationshipsChangedEventBodyContactLabel:
      type: object
      properties:
        label_value:
          type: string
          nullable: true
        label_color:
          type: string
          nullable: true
        label_type:
          type: string
          nullable: true
      additionalProperties: false
    OnContactLabelRelationshipsChangedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnContactLabelRelationshipsChangedEventBody"
      additionalProperties: false
    OnContactLabelRelationshipsChangedEventOutput:
      type: object
      additionalProperties: false
    OnContactLabelRelationshipsChangedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnContactLabelRelationshipsChangedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnContactListRelationshipsChangedEventBody:
      required:
        - added_to_lists
        - contact
        - contact_id
        - created_at
        - lists
        - removed_from_lists
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        added_to_lists:
          type: array
          items:
            $ref: "#/components/schemas/OnContactListRelationshipsChangedEventBodyContactList"
        removed_from_lists:
          type: array
          items:
            $ref: "#/components/schemas/OnContactListRelationshipsChangedEventBodyContactList"
        lists:
          type: array
          items:
            $ref: "#/components/schemas/OnContactListRelationshipsChangedEventBodyContactList"
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactListRelationshipsChangedEventBodyContactList:
      type: object
      properties:
        list_id:
          type: string
          nullable: true
        list_name:
          type: string
          nullable: true
      additionalProperties: false
    OnContactListRelationshipsChangedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnContactListRelationshipsChangedEventBody"
      additionalProperties: false
    OnContactListRelationshipsChangedEventOutput:
      type: object
      additionalProperties: false
    OnContactListRelationshipsChangedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnContactListRelationshipsChangedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnContactManuallyEnrolledEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactManuallyEnrolledEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnContactManuallyEnrolledEventBody"
      additionalProperties: false
    OnContactManuallyEnrolledEventOutput:
      type: object
      additionalProperties: false
    OnContactManuallyEnrolledEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnContactManuallyEnrolledEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnContactUpdatedEventBody:
      required:
        - change_entries
        - contact
        - contact_id
        - created_at
        - post_updated_contact
        - pre_updated_contact
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        post_updated_contact:
          type: object
          additionalProperties:
            nullable: true
        pre_updated_contact:
          type: object
          additionalProperties:
            nullable: true
        change_entries:
          type: array
          items:
            $ref: "#/components/schemas/OnContactUpdatedEventBodyChangeEntry"
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnContactUpdatedEventBodyChangeEntry:
      type: object
      properties:
        property_name:
          type: string
          nullable: true
        property_id:
          type: string
          nullable: true
        from_value:
          nullable: true
        to_value:
          nullable: true
      additionalProperties: false
    OnContactUpdatedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnContactUpdatedEventBody"
      additionalProperties: false
    OnContactUpdatedEventOutput:
      type: object
      additionalProperties: false
    OnContactUpdatedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnContactUpdatedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnFbIgPostCommentReceivedEventBody:
      required:
        - channel
        - comment_id
        - created_at
        - is_new_contact
        - page_id
        - post_comment
      type: object
      properties:
        post_comment:
          $ref: "#/components/schemas/OnFbIgPostCommentReceivedEventBodyMessage"
        page_id:
          minLength: 1
          type: string
        channel:
          minLength: 1
          type: string
        comment_id:
          minLength: 1
          type: string
        is_new_contact:
          type: boolean
        conversation_id:
          type: string
          nullable: true
        contact_id:
          type: string
          nullable: true
        contact:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnFbIgPostCommentReceivedEventBodyMessage:
      type: object
      properties:
        comment_id:
          type: string
          nullable: true
        post_id:
          type: string
          nullable: true
        From:
          $ref: "#/components/schemas/FromFacebookSender"
        text:
          type: string
          nullable: true
      additionalProperties: false
    OnFbIgPostCommentReceivedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnFbIgPostCommentReceivedEventBody"
      additionalProperties: false
    OnFbIgPostCommentReceivedEventOutput:
      type: object
      additionalProperties: false
    OnFbIgPostCommentReceivedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnFbIgPostCommentReceivedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnGoogleSheetsRowCreatedEventBody:
      required:
        - connection_id
        - created_at
        - row
        - row_id
        - spreadsheet_id
        - worksheet_id
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        spreadsheet_id:
          minLength: 1
          type: string
        worksheet_id:
          minLength: 1
          type: string
        row_id:
          minLength: 1
          type: string
        row:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnGoogleSheetsRowUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - row
        - row_id
        - spreadsheet_id
        - worksheet_id
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        spreadsheet_id:
          minLength: 1
          type: string
        worksheet_id:
          minLength: 1
          type: string
        row_id:
          minLength: 1
          type: string
        row:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnHubspotObjectCreatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnHubspotObjectEnrolledEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnHubspotObjectUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnInstagramMediaCommentReceivedEventBody:
      required:
        - comment_id
        - created_at
        - instagram_media_comment
        - instagram_page_id
        - is_new_contact
      type: object
      properties:
        instagram_media_comment:
          $ref: "#/components/schemas/OnInstagramMediaCommentReceivedEventBodyMessage"
        instagram_page_id:
          minLength: 1
          type: string
        comment_id:
          minLength: 1
          type: string
        is_new_contact:
          type: boolean
        conversation_id:
          type: string
          nullable: true
        contact_id:
          type: string
          nullable: true
        contact:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnInstagramMediaCommentReceivedEventBodyMessage:
      type: object
      properties:
        comment_id:
          type: string
          nullable: true
        media_id:
          type: string
          nullable: true
        From:
          $ref: "#/components/schemas/FromInstagramSender"
        media_product_type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    OnInstagramMediaCommentReceivedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnInstagramMediaCommentReceivedEventBody"
      additionalProperties: false
    OnInstagramMediaCommentReceivedEventOutput:
      type: object
      additionalProperties: false
    OnInstagramMediaCommentReceivedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnInstagramMediaCommentReceivedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageReceivedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - is_new_contact
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: "#/components/schemas/OnMessageReceivedEventBodyMessage"
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        is_new_contact:
          type: boolean
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMessageReceivedEventBodyMessage:
      type: object
      properties:
        id:
          type: string
          nullable: true
        quoted_message:
          $ref: "#/components/schemas/QuotedMessage"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: "#/components/schemas/MessageBody"
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageReceivedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnMessageReceivedEventBody"
      additionalProperties: false
    OnMessageReceivedEventOutput:
      type: object
      additionalProperties: false
    OnMessageReceivedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnMessageReceivedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageSentEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: "#/components/schemas/OnMessageSentEventBodyMessage"
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMessageSentEventBodyMessage:
      type: object
      properties:
        quoted_message:
          $ref: "#/components/schemas/QuotedMessage"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: "#/components/schemas/MessageBody"
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageSentEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnMessageSentEventBody"
      additionalProperties: false
    OnMessageSentEventOutput:
      type: object
      additionalProperties: false
    OnMessageSentEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnMessageSentEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageStatusUpdatedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: "#/components/schemas/OnMessageStatusUpdatedEventBodyMessage"
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMessageStatusUpdatedEventBodyMessage:
      type: object
      properties:
        quoted_message:
          $ref: "#/components/schemas/QuotedMessage"
        analytic_tags:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: "#/components/schemas/MessageBody"
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnMessageStatusUpdatedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnMessageStatusUpdatedEventBody"
      additionalProperties: false
    OnMessageStatusUpdatedEventOutput:
      type: object
      additionalProperties: false
    OnMessageStatusUpdatedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnMessageStatusUpdatedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnMetaDetectedOutcomeReceivedEventBody:
      required:
        - created_at
        - detected_outcome
      type: object
      properties:
        detected_outcome:
          $ref: "#/components/schemas/OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome"
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnMetaDetectedOutcomeReceivedEventBodyDetectedOutcome:
      type: object
      properties:
        id:
          type: string
          nullable: true
        event_name:
          type: string
          nullable: true
        timestamp:
          type: integer
          format: int64
        ctwa_clid:
          type: string
          nullable: true
        custom_data:
          $ref: "#/components/schemas/CustomData"
      additionalProperties: false
    OnMetaDetectedOutcomeReceivedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnMetaDetectedOutcomeReceivedEventBody"
      additionalProperties: false
    OnMetaDetectedOutcomeReceivedEventOutput:
      type: object
      additionalProperties: false
    OnMetaDetectedOutcomeReceivedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnMetaDetectedOutcomeReceivedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnSalesforceObjectCreatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - is_custom_object
        - object_dict
        - object_type
        - salesforce_connection_id
      type: object
      properties:
        salesforce_connection_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSalesforceObjectEnrolledEventBody:
      required:
        - created_at
        - is_custom_object
        - object_dict
        - object_type
        - salesforce_connection_id
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        salesforce_connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSalesforceObjectUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - is_custom_object
        - object_dict
        - object_type
        - salesforce_connection_id
      type: object
      properties:
        salesforce_connection_id:
          minLength: 1
          type: string
        connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectCreatedEventBody:
      required:
        - contact_id
        - created_at
        - primary_property_value
        - property_values
        - schema_id
        - schemaful_object_id
        - sleekflow_staff_id
      type: object
      properties:
        schemaful_object_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        primary_property_value:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectEnrolledEventBody:
      required:
        - contact_id
        - created_at
        - primary_property_value
        - property_values
        - schema_id
        - schemaful_object_id
        - workflow_id
        - workflow_versioned_id
      type: object
      properties:
        schemaful_object_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        primary_property_value:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectUpdatedEventBody:
      required:
        - change_entries
        - contact_id
        - created_at
        - post_updated_contact_id
        - post_updated_property_values
        - pre_updated_contact_id
        - pre_updated_property_values
        - primary_property_value
        - property_values
        - schema_id
        - schemaful_object_id
        - sleekflow_staff_id
      type: object
      properties:
        schemaful_object_id:
          minLength: 1
          type: string
        schema_id:
          minLength: 1
          type: string
        primary_property_value:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        pre_updated_contact_id:
          minLength: 1
          type: string
        post_updated_contact_id:
          minLength: 1
          type: string
        property_values:
          type: object
          additionalProperties:
            nullable: true
        pre_updated_property_values:
          type: object
          additionalProperties:
            nullable: true
        post_updated_property_values:
          type: object
          additionalProperties:
            nullable: true
        change_entries:
          type: array
          items:
            $ref: "#/components/schemas/OnSchemafulObjectUpdatedEventBodyChangeEntry"
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnSchemafulObjectUpdatedEventBodyChangeEntry:
      type: object
      properties:
        property_name:
          type: string
          nullable: true
        property_id:
          type: string
          nullable: true
        from_value:
          nullable: true
        to_value:
          nullable: true
      additionalProperties: false
    OnTicketCommonEventBody:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_by_email:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        channel:
          $ref: "#/components/schemas/OnTicketCommonEventBodyTicketChannel"
        status_name:
          type: string
          nullable: true
        status_id:
          type: string
          nullable: true
        priority_name:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type_name:
          type: string
          nullable: true
        type_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        first_assignee:
          type: string
          nullable: true
        first_assignee_email:
          type: string
          nullable: true
        contact_name:
          type: string
          nullable: true
        contact_phone_number:
          type: string
          nullable: true
        contact_email:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
      additionalProperties: false
    OnTicketCommonEventBodyTicketChannel:
      type: object
      properties:
        channel_type:
          type: string
          nullable: true
        channel_identity_id:
          type: string
          nullable: true
        channel_name:
          type: string
          nullable: true
      additionalProperties: false
    OnTicketCreatedEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - ticket
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        ticket:
          $ref: "#/components/schemas/OnTicketCommonEventBody"
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnTicketUpdatedEventBody:
      required:
        - contact
        - contact_id
        - created_at
        - ticket
        - updated_properties
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        updated_properties:
          type: object
          additionalProperties:
            nullable: true
        updated_property_list:
          type: array
          items:
            type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_identity_id:
          type: string
          nullable: true
        ticket:
          $ref: "#/components/schemas/OnTicketUpdatedPropertyEventBody"
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnTicketUpdatedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnTicketUpdatedEventBody"
      additionalProperties: false
    OnTicketUpdatedEventOutput:
      type: object
      additionalProperties: false
    OnTicketUpdatedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnTicketUpdatedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnTicketUpdatedPropertyEventBody:
      type: object
      properties:
        current_assignee:
          type: string
          nullable: true
        current_assignee_email:
          type: string
          nullable: true
        first_respondent:
          type: string
          nullable: true
        first_respondent_email:
          type: string
          nullable: true
        first_response_timestamp:
          type: string
          format: date-time
          nullable: true
        resolution_agent:
          type: string
          nullable: true
        resolution_agent_email:
          type: string
          nullable: true
        resolution_timestamp:
          type: string
          format: date-time
          nullable: true
        ticket_reassigned_before_resolution:
          type: boolean
        created_at:
          type: string
          format: date-time
        created_by:
          type: string
          nullable: true
        created_by_email:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        channel:
          $ref: "#/components/schemas/OnTicketCommonEventBodyTicketChannel"
        status_name:
          type: string
          nullable: true
        status_id:
          type: string
          nullable: true
        priority_name:
          type: string
          nullable: true
        priority_id:
          type: string
          nullable: true
        due_date:
          type: string
          format: date-time
          nullable: true
        type_name:
          type: string
          nullable: true
        type_id:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        first_assignee:
          type: string
          nullable: true
        first_assignee_email:
          type: string
          nullable: true
        contact_name:
          type: string
          nullable: true
        contact_phone_number:
          type: string
          nullable: true
        contact_email:
          type: string
          nullable: true
        sleekflow_user_profile_id:
          type: string
          nullable: true
      additionalProperties: false
    OnWebhookEventBody:
      required:
        - created_at
        - request_body_str
        - workflow_id
      type: object
      properties:
        request_body_str:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnWhatsappFlowSubmissionMessageReceivedEventBody:
      required:
        - channel_id
        - contact
        - contact_id
        - conversation_id
        - created_at
        - is_new_contact
        - message
        - message_id
      type: object
      properties:
        message:
          $ref: "#/components/schemas/OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage"
        channel:
          type: string
          nullable: true
        channel_id:
          minLength: 1
          type: string
        conversation_id:
          minLength: 1
          type: string
        message_id:
          minLength: 1
          type: string
        message_unique_id:
          type: string
          nullable: true
        contact_id:
          minLength: 1
          type: string
        contact:
          type: object
          additionalProperties:
            nullable: true
        is_new_contact:
          type: boolean
        is_sent_from_sleekflow:
          type: boolean
          readOnly: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnWhatsappFlowSubmissionMessageReceivedEventBodyMessage:
      type: object
      properties:
        id:
          type: string
          nullable: true
        whatsapp_flow_id:
          type: string
          nullable: true
        whatsapp_template_id:
          type: string
          nullable: true
        whatsapp_flow_submission_data:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        message_body:
          $ref: "#/components/schemas/MessageBody"
        message_type:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
        message_status:
          type: string
          nullable: true
        message_timestamp:
          type: integer
          format: int64
          readOnly: true
        message_delivery_type:
          type: string
          nullable: true
      additionalProperties: false
    OnWhatsappFlowSubmissionMessageReceivedEventInput:
      required:
        - contact_id
        - event_body
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        contact_id:
          minLength: 1
          type: string
        event_body:
          $ref: "#/components/schemas/OnWhatsappFlowSubmissionMessageReceivedEventBody"
      additionalProperties: false
    OnWhatsappFlowSubmissionMessageReceivedEventOutput:
      type: object
      additionalProperties: false
    OnWhatsappFlowSubmissionMessageReceivedEventOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/OnWhatsappFlowSubmissionMessageReceivedEventOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OnZohoObjectCreatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnZohoObjectEnrolledEventBody:
      required:
        - created_at
        - object_dict
        - object_type
        - workflow_id
        - workflow_versioned_id
        - zoho_connection_id
      type: object
      properties:
        zoho_connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        workflow_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OnZohoObjectUpdatedEventBody:
      required:
        - connection_id
        - created_at
        - entity_type_name
        - object_dict
      type: object
      properties:
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_dict:
          type: object
          additionalProperties:
            nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    OrderMessageObject:
      type: object
      properties:
        catalog_id:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: "#/components/schemas/OrderMessageProductItem"
          nullable: true
        product_items_json:
          type: string
          nullable: true
      additionalProperties: false
    OrderMessageProductItem:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
          nullable: true
        item_price:
          type: number
          format: decimal
          nullable: true
        currency:
          type: string
          nullable: true
      additionalProperties: false
    ProxyStateDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        identity:
          $ref: "#/components/schemas/StateIdentity"
        workflow_context:
          $ref: "#/components/schemas/ProxyStateWorkflowContext"
        trigger_event_body:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        usr_var_dict:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        sys_var_dict:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        sys_company_var_dict:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    ProxyStateWorkflowContext:
      type: object
      properties:
        snapshotted_workflow:
          $ref: "#/components/schemas/ProxyWorkflow"
      additionalProperties: false
    ProxyWorkflow:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        workflow_type:
          type: string
          nullable: true
        dependency_workflow_id:
          type: string
          nullable: true
        workflow_group_id:
          type: string
          nullable: true
        triggers:
          $ref: "#/components/schemas/WorkflowTriggers"
        workflow_enrollment_settings:
          $ref: "#/components/schemas/WorkflowEnrollmentSettings"
        workflow_schedule_settings:
          $ref: "#/components/schemas/WorkflowScheduleSettings"
        activation_status:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        steps:
          type: array
          items:
            $ref: "#/components/schemas/Step"
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        version:
          type: string
          nullable: true
        _etag:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
      additionalProperties: false
    PublicBlob:
      type: object
      properties:
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    QuotedMessage:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
        message_unique_id:
          type: string
          nullable: true
        message_content:
          type: string
          nullable: true
      additionalProperties: false
    ReactionMessageObject:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
        emoji:
          type: string
          nullable: true
      additionalProperties: false
    SaveWorkflowDraftInput:
      required:
        - metadata
        - name
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        workflow_id:
          maxLength: 128
          minLength: 1
          type: string
        triggers:
          $ref: "#/components/schemas/WorkflowTriggers"
        workflow_enrollment_settings:
          $ref: "#/components/schemas/WorkflowEnrollmentSettings"
        workflow_schedule_settings:
          $ref: "#/components/schemas/WorkflowScheduleSettings"
        steps:
          type: array
          items: {}
          nullable: true
        name:
          maxLength: 100
          minLength: 1
          type: string
        workflow_group_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    SaveWorkflowDraftOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    SaveWorkflowDraftOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/SaveWorkflowDraftOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ScaleWorkflowExecutionLimitInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ScaleWorkflowExecutionLimitOutput:
      type: object
      properties:
        is_success:
          type: boolean
      additionalProperties: false
    ScheduleDeleteWorkflowsInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_ids
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_ids:
          type: array
          items:
            type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ScheduleDeleteWorkflowsOutput:
      type: object
      additionalProperties: false
    ScheduleDeleteWorkflowsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/ScheduleDeleteWorkflowsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SearchGoogleSheetsRowInput:
      required:
        - conditions
        - connection_id
        - header_row_id
        - spreadsheet_id
        - state_id
        - state_identity
        - worksheet_id
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        spreadsheet_id:
          minLength: 1
          type: string
        worksheet_id:
          minLength: 1
          type: string
        header_row_id:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: "#/components/schemas/SearchObjectCondition"
      additionalProperties: false
    SearchHubspotObjectInput:
      required:
        - conditions
        - connection_id
        - entity_type_name
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: "#/components/schemas/SearchObjectCondition"
      additionalProperties: false
    SearchObjectCondition:
      required:
        - field_name
        - operator
      type: object
      properties:
        field_name:
          minLength: 1
          type: string
        operator:
          minLength: 1
          type: string
        value:
          nullable: true
      additionalProperties: false
    SearchSalesforceObjectInput:
      required:
        - conditions
        - is_custom_object
        - object_type
        - salesforce_connection_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        salesforce_connection_id:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        conditions:
          type: array
          items:
            $ref: "#/components/schemas/SearchObjectCondition"
      additionalProperties: false
    SearchSalesforceObjectV2Input:
      required:
        - conditions
        - connection_id
        - entity_type_name
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: "#/components/schemas/SearchObjectCondition"
      additionalProperties: false
    SearchZohoObjectInput:
      required:
        - conditions
        - connection_id
        - entity_type_name
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        conditions:
          type: array
          items:
            $ref: "#/components/schemas/SearchObjectCondition"
      additionalProperties: false
    SendExecutionUsageReachedThresholdEmailInput:
      required:
        - sleekflow_company_id
        - threshold
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        threshold:
          type: number
          format: double
      additionalProperties: false
    SendExecutionUsageReachedThresholdEmailOutput:
      type: object
      additionalProperties: false
    SendMessageInput:
      required:
        - channel
        - from_to
        - message_body
        - message_type
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        channel:
          minLength: 1
          type: string
        from_to:
          oneOf:
            - $ref: "#/components/schemas/WhatsappCloudApiSendMessageInputFromTo"
            - $ref: "#/components/schemas/FacebookPageMessengerSendMessageInputFromTo"
            - $ref: "#/components/schemas/InstagramPageMessengerSendMessageInputFromTo"
        message_type:
          minLength: 1
          type: string
        message_body:
          $ref: "#/components/schemas/MessageBody"
      additionalProperties: false
    SendMessageInputFromTo:
      required:
        - from_to_type
      type: object
      properties:
        from_to_type:
          type: string
          nullable: true
      additionalProperties: false
      discriminator:
        propertyName: from_to_type
        mapping:
          WhatsappCloudApiSendMessageInputFromTo: "#/components/schemas/WhatsappCloudApiSendMessageInputFromTo"
          FacebookPageMessengerSendMessageInputFromTo: "#/components/schemas/FacebookPageMessengerSendMessageInputFromTo"
          InstagramPageMessengerSendMessageInputFromTo: "#/components/schemas/InstagramPageMessengerSendMessageInputFromTo"
    SendMessageInputFromToV2:
      required:
        - channel_identity_id
        - to_contact_id
      type: object
      properties:
        channel_identity_id:
          minLength: 1
          type: string
        to_contact_id:
          minLength: 1
          type: string
      additionalProperties: false
    SendMessageInputV2:
      required:
        - channel
        - from_to
        - message_body
        - message_type
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        channel:
          minLength: 1
          type: string
        from_to:
          $ref: "#/components/schemas/SendMessageInputFromToV2"
        message_type:
          minLength: 1
          type: string
        message_body:
          $ref: "#/components/schemas/MessageBody"
      additionalProperties: false
    SendMetaCapiEventInput:
      required:
        - data
        - partner_agent
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        data:
          type: array
          items:
            $ref: "#/components/schemas/CapiData"
        partner_agent:
          minLength: 1
          type: string
      additionalProperties: false
    SendWorkflowInfiniteLoopEmailInput:
      required:
        - created_at
        - sleekflow_company_id
        - workflow_id
        - workflow_name
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
        workflow_name:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    SendWorkflowInfiniteLoopEmailOutput:
      type: object
      additionalProperties: false
    SetRateLimitConfigInput:
      required:
        - expiration_seconds
        - key_name
        - rate_limit
        - window_size_ms
      type: object
      properties:
        key_name:
          minLength: 1
          type: string
        window_size_ms:
          type: integer
          format: int32
        rate_limit:
          type: integer
          format: int32
        expiration_seconds:
          type: integer
          format: int32
      additionalProperties: false
    SetRateLimitConfigOutput:
      type: object
      properties:
        message:
          type: string
          nullable: true
        success:
          type: boolean
      additionalProperties: false
    SetRateLimitConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/SetRateLimitConfigOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    SmsMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    StackEntry:
      type: object
      properties:
        step_id:
          type: string
          nullable: true
        worker_instance_id:
          type: string
          nullable: true
      additionalProperties: false
    StateIdentity:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
      additionalProperties: false
    StateIdentityDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        object_id:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        object_label:
          type: string
          nullable: true
        object_resolved_id:
          type: string
          nullable: true
      additionalProperties: false
    Step:
      required:
        - id
        - name
      type: object
      properties:
        id:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
        assign:
          type: object
          additionalProperties:
            type: string
          nullable: true
        next_step_id:
          type: string
          nullable: true
      additionalProperties: false
    StepExecutionDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: "#/components/schemas/StateIdentityDto"
        step_id:
          type: string
          nullable: true
        step_node_id:
          type: string
          nullable: true
        step_execution_status:
          type: string
          nullable: true
        worker_instance_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        error:
          $ref: "#/components/schemas/UserFriendlyError"
      additionalProperties: false
    SubmitStepInput:
      required:
        - stack_entries
        - state_id
        - step_id
        - worker_instance_id
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        step_id:
          minLength: 1
          type: string
        stack_entries:
          type: array
          items:
            $ref: "#/components/schemas/StackEntry"
        worker_instance_id:
          minLength: 1
          type: string
      additionalProperties: false
    SubmitStepOutput:
      type: object
      additionalProperties: false
    SubmitStepOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/SubmitStepOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SwapWorkflowsInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
        - source_workflow_versioned_id
        - target_workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        source_workflow_versioned_id:
          minLength: 1
          type: string
        target_workflow_versioned_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    SwapWorkflowsOutput:
      type: object
      properties:
        source_workflow:
          $ref: "#/components/schemas/WorkflowDto"
        target_workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    SwapWorkflowsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/SwapWorkflowsOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TelegramMessengerMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    TemplateMessageObject:
      type: object
      properties:
        template_name:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        components:
          type: array
          items:
            $ref: "#/components/schemas/TemplateMessageObjectComponent"
          nullable: true
      additionalProperties: false
    TemplateMessageObjectComponent:
      type: object
      properties:
        type:
          type: string
          nullable: true
        sub_type:
          type: string
          nullable: true
        index:
          type: integer
          format: int32
        parameters:
          type: array
          items:
            $ref: "#/components/schemas/TemplateMessageObjectComponentParameter"
          nullable: true
      additionalProperties: false
    TemplateMessageObjectComponentParameter:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        payload:
          type: string
          nullable: true
        image:
          $ref: "#/components/schemas/ImageMessageObject"
        audio:
          $ref: "#/components/schemas/AudioMessageObject"
        document:
          $ref: "#/components/schemas/DocumentMessageObject"
        video:
          $ref: "#/components/schemas/VideoMessageObject"
        location:
          $ref: "#/components/schemas/LocationMessageObject"
        currency:
          $ref: "#/components/schemas/CurrencyMessageObject"
        date_time:
          $ref: "#/components/schemas/DateTimeMessageObject"
        action:
          $ref: "#/components/schemas/ActionMessageObject"
      additionalProperties: false
    TerminateScheduledWorkflowInput:
      required:
        - sleekflow_company_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_id:
          minLength: 1
          type: string
      additionalProperties: false
    TerminateScheduledWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    TerminateScheduledWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/TerminateScheduledWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TextMessageObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    ToggleFlowHubUsageLimitInput:
      required:
        - is_usage_limit_enabled
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        is_usage_limit_enabled:
          type: boolean
      additionalProperties: false
    ToggleFlowHubUsageLimitOutput:
      type: object
      properties:
        flow_hub_config:
          $ref: "#/components/schemas/FlowHubConfig"
      additionalProperties: false
    ToggleFlowHubUsageLimitOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/ToggleFlowHubUsageLimitOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TriggerConfig:
      type: object
      properties:
        id:
          type: string
          nullable: true
        trigger_name:
          type: string
          nullable: true
        trigger_group:
          type: string
          nullable: true
        trigger_description:
          type: string
          nullable: true
        is_external_integration:
          type: boolean
        condition_arg_name:
          type: string
          nullable: true
      additionalProperties: false
    TriggerConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        trigger_name:
          type: string
          nullable: true
        trigger_group:
          type: string
          nullable: true
        trigger_description:
          type: string
          nullable: true
        is_external_integration:
          type: boolean
        condition_arg_name:
          type: string
          nullable: true
      additionalProperties: false
    TriggerNeedConfig:
      type: object
      properties:
        id:
          type: string
          nullable: true
        label:
          type: string
          nullable: true
        arg_name:
          type: string
          nullable: true
        field_path:
          type: string
          nullable: true
        trigger_id:
          type: string
          nullable: true
        input_type:
          type: string
          nullable: true
        input_group:
          type: string
          nullable: true
        validations:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        placeholder:
          type: string
          nullable: true
        helper_text:
          type: string
          nullable: true
        options:
          type: array
          items:
            $ref: "#/components/schemas/NeedConfigOption"
          nullable: true
        options_request_path:
          type: string
          nullable: true
        options_request_arg_names:
          type: array
          items:
            type: string
          nullable: true
        data_request_path:
          type: string
          nullable: true
        data_request_arg_names:
          type: array
          items:
            type: string
          nullable: true
        conditions__expr:
          type: string
          nullable: true
        disabled:
          type: boolean
          nullable: true
        is_with_copy:
          type: boolean
          nullable: true
        items:
          type: array
          items:
            $ref: "#/components/schemas/TriggerNeedConfig"
          nullable: true
        hidden:
          type: boolean
          nullable: true
        condition_ignore:
          type: boolean
          nullable: true
      additionalProperties: false
    TriggerNeedConfigDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        label:
          type: string
          nullable: true
        arg_name:
          type: string
          nullable: true
        field_path:
          type: string
          nullable: true
        trigger_id:
          type: string
          nullable: true
        input_type:
          type: string
          nullable: true
        input_group:
          type: string
          nullable: true
        validations:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        placeholder:
          type: string
          nullable: true
        helper_text:
          type: string
          nullable: true
        options:
          type: array
          items:
            $ref: "#/components/schemas/NeedConfigOption"
          nullable: true
        options_request_path:
          type: string
          nullable: true
        options_request_arg_names:
          type: array
          items:
            type: string
          nullable: true
        data_request_path:
          type: string
          nullable: true
        data_request_arg_names:
          type: array
          items:
            type: string
          nullable: true
        disabled:
          type: boolean
          nullable: true
        is_with_copy:
          type: boolean
          nullable: true
        items:
          type: array
          items:
            $ref: "#/components/schemas/TriggerNeedConfigDto"
          nullable: true
        hidden:
          type: boolean
          nullable: true
        condition_ignore:
          type: boolean
          nullable: true
      additionalProperties: false
    TriggerScheduledWorkflowInput:
      required:
        - sleekflow_company_id
        - workflow_id
      type: object
      properties:
        workflow_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    TriggerScheduledWorkflowOutput:
      required:
        - workflow
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    TriggerScheduledWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/TriggerScheduledWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UnassignErrorInput:
      required:
        - stack_entries
        - state_id
        - try_catch_step_id
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        try_catch_step_id:
          minLength: 1
          type: string
        stack_entries:
          type: array
          items:
            $ref: "#/components/schemas/StackEntry"
      additionalProperties: false
    UnassignErrorOutput:
      type: object
      additionalProperties: false
    UnassignErrorOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UnassignErrorOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UnenrollFlowHubInput:
      required:
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UnenrollFlowHubOutput:
      type: object
      properties:
        flow_hub_config:
          $ref: "#/components/schemas/FlowHubConfig"
      additionalProperties: false
    UnenrollFlowHubOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UnenrollFlowHubOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateAiAgentWorkflowsByAiNodeInput:
      required:
        - contact_property
        - labels
        - schemaful_object
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        labels:
          type: array
          items:
            type: object
            additionalProperties: {}
        schemaful_object:
          type: object
          additionalProperties: {}
        contact_property:
          type: object
          additionalProperties: {}
        workflow_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateAiAgentWorkflowsByAiNodeOutput:
      type: object
      properties:
        add_workflow_ids:
          type: array
          items:
            type: string
          nullable: true
        remove_workflow_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateAiAgentWorkflowsByAiNodeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UpdateAiAgentWorkflowsByAiNodeOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateContactCollaboratorRelationshipsInput:
      required:
        - contact_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        add_staff_ids:
          type: array
          items:
            type: string
          nullable: true
        remove_staff_ids:
          type: array
          items:
            type: string
          nullable: true
        set_staff_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateContactConversationStatusInput:
      required:
        - contact_id
        - state_id
        - state_identity
        - status
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        status:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateContactLabelRelationshipsInput:
      required:
        - contact_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        add_labels:
          type: array
          items:
            type: string
          nullable: true
        remove_labels:
          type: array
          items:
            type: string
          nullable: true
        set_labels:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateContactListRelationshipsInput:
      required:
        - contact_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        add_list_ids:
          type: array
          items:
            type: string
          nullable: true
        remove_list_ids:
          type: array
          items:
            type: string
          nullable: true
        set_list_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateContactOwnerRelationshipsInput:
      required:
        - assignment_strategy
        - contact_id
        - is_unassigned
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        is_unassigned:
          type: boolean
        assignment_strategy:
          minLength: 1
          type: string
        team_id:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        assignment_counter:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    UpdateContactPropertiesByPropertyKeyInput:
      required:
        - contact_property_key_id
        - contact_property_key_value
        - properties_dict
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_property_key_id:
          minLength: 1
          type: string
        contact_property_key_value:
          minLength: 1
          type: string
        properties_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateContactPropertiesInput:
      required:
        - contact_id
        - properties_dict
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_id:
          minLength: 1
          type: string
        properties_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateContactPropertiesWithRecordSourceInput:
      required:
        - contact_property_key_id
        - contact_property_key_value
        - properties_dict
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        contact_property_key_id:
          minLength: 1
          type: string
        contact_property_key_value:
          minLength: 1
          type: string
        properties_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateFlowHubConfigInput:
      required:
        - sleekflow_company_id
        - usage_limit
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        usage_limit:
          $ref: "#/components/schemas/UsageLimit"
        usage_limit_offset:
          $ref: "#/components/schemas/UsageLimitOffset"
        origin:
          type: string
          nullable: true
      additionalProperties: false
    UpdateFlowHubConfigOutput:
      type: object
      properties:
        flow_hub_config:
          $ref: "#/components/schemas/FlowHubConfig"
      additionalProperties: false
    UpdateFlowHubConfigOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UpdateFlowHubConfigOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateGoogleSheetsRowInput:
      required:
        - connection_id
        - fields_dict
        - header_row_id
        - row_id
        - spreadsheet_id
        - state_id
        - state_identity
        - worksheet_id
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        spreadsheet_id:
          minLength: 1
          type: string
        worksheet_id:
          minLength: 1
          type: string
        header_row_id:
          minLength: 1
          type: string
        row_id:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateHubspotObjectInput:
      required:
        - connection_id
        - entity_type_name
        - fields_dict
        - object_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateSalesforceObjectInput:
      required:
        - entity_type_name
        - is_custom_object
        - object_properties
        - salesforce_connection_id
        - salesforce_object_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        salesforce_connection_id:
          minLength: 1
          type: string
        salesforce_object_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        is_custom_object:
          type: boolean
        object_properties:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateSalesforceObjectV2Input:
      required:
        - connection_id
        - entity_type_name
        - fields_dict
        - object_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateWorkflowDurablePayloadInput:
      required:
        - durable_payload
        - sleekflow_company_id
        - workflow_versioned_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_versioned_id:
          minLength: 1
          type: string
        durable_payload:
          $ref: "#/components/schemas/DurablePayload"
      additionalProperties: false
    UpdateWorkflowDurablePayloadOutput:
      type: object
      properties:
        workflow_versioned_id:
          type: string
          nullable: true
        update_success:
          type: boolean
      additionalProperties: false
    UpdateWorkflowDurablePayloadOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UpdateWorkflowDurablePayloadOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWorkflowGroupInput:
      required:
        - name
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_group_id
      type: object
      properties:
        workflow_group_id:
          minLength: 1
          type: string
        name:
          maxLength: 100
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateWorkflowGroupOutput:
      required:
        - workflow_group
      type: object
      properties:
        workflow_group:
          $ref: "#/components/schemas/WorkflowGroupDto"
      additionalProperties: false
    UpdateWorkflowGroupOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UpdateWorkflowGroupOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWorkflowInput:
      required:
        - metadata
        - name
        - sleekflow_company_id
        - sleekflow_staff_id
        - steps
        - triggers
        - workflow_id
      type: object
      properties:
        sleekflow_company_id:
          maxLength: 128
          minLength: 1
          type: string
        workflow_id:
          maxLength: 128
          minLength: 1
          type: string
        triggers:
          $ref: "#/components/schemas/WorkflowTriggers"
        workflow_enrollment_settings:
          $ref: "#/components/schemas/WorkflowEnrollmentSettings"
        workflow_schedule_settings:
          $ref: "#/components/schemas/WorkflowScheduleSettings"
        steps:
          maxItems: 1024
          minItems: 1
          type: array
          items: {}
        name:
          maxLength: 100
          minLength: 1
          type: string
        workflow_group_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UpdateWorkflowOutput:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/WorkflowDto"
      additionalProperties: false
    UpdateWorkflowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UpdateWorkflowOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWorkflowWebhookTriggerInput:
      required:
        - object_id_expression
        - object_type
        - sleekflow_company_id
        - sleekflow_staff_id
        - workflow_webhook_trigger_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        workflow_webhook_trigger_id:
          minLength: 1
          type: string
        object_id_expression:
          minLength: 1
          type: string
        object_type:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateWorkflowWebhookTriggerOutput:
      type: object
      properties:
        workflow_webhook_trigger:
          $ref: "#/components/schemas/WorkflowWebhookTrigger"
      additionalProperties: false
    UpdateWorkflowWebhookTriggerOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: "#/components/schemas/UpdateWorkflowWebhookTriggerOutput"
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateZohoObjectInput:
      required:
        - connection_id
        - entity_type_name
        - fields_dict
        - object_id
        - state_id
        - state_identity
      type: object
      properties:
        state_id:
          minLength: 1
          type: string
        state_identity:
          $ref: "#/components/schemas/StateIdentity"
        connection_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        fields_dict:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    UsageLimit:
      type: object
      properties:
        maximum_num_of_workflows:
          type: integer
          format: int32
          nullable: true
        maximum_num_of_active_workflows:
          type: integer
          format: int32
          nullable: true
        maximum_num_of_nodes_per_workflow:
          type: integer
          format: int32
          nullable: true
        maximum_num_of_monthly_workflow_executions:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    UsageLimitOffset:
      type: object
      properties:
        maximum_num_of_active_workflows_offset:
          type: integer
          format: int32
          nullable: true
        maximum_num_of_nodes_per_workflow_offset:
          type: integer
          format: int32
          nullable: true
        maximum_num_of_monthly_workflow_executions_offset:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    UserFriendlyError:
      type: object
      properties:
        error_code:
          type: string
          nullable: true
        error_message:
          type: string
          nullable: true
      additionalProperties: false
    ViberMessageObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    VideoMessageObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: "#/components/schemas/MediaMessageObjectProvider"
      additionalProperties: false
    WeChatMessengerMessageObject:
      type: object
      properties:
        msgtype:
          type: string
          nullable: true
        text:
          $ref: "#/components/schemas/WeChatTextMessage"
      additionalProperties: false
    WeChatTextMessage:
      type: object
      properties:
        content:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiSendMessageInputFromTo:
      allOf:
        - $ref: "#/components/schemas/SendMessageInputFromTo"
        - type: object
          properties:
            from_phone_number:
              type: string
              nullable: true
            to_phone_number:
              type: string
              nullable: true
            to_contact_id:
              type: string
              nullable: true
            from_channel_id:
              type: string
              nullable: true
          additionalProperties: false
    WorkflowAgentConfigMapping:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
        agent_config_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WorkflowDto:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        workflow_type:
          type: string
          nullable: true
        workflow_group_id:
          type: string
          nullable: true
        triggers:
          $ref: "#/components/schemas/WorkflowTriggers"
        workflow_enrollment_settings:
          $ref: "#/components/schemas/WorkflowEnrollmentSettings"
        workflow_schedule_settings:
          $ref: "#/components/schemas/WorkflowScheduleSettings"
        steps:
          type: array
          items: {}
          nullable: true
        activation_status:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        version:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowEnrollmentSettings:
      type: object
      properties:
        can_enroll_only_once:
          type: boolean
        can_enroll_again_on_failure_only:
          type: boolean
        can_enroll_in_parallel:
          type: boolean
      additionalProperties: false
    WorkflowExecutionDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: "#/components/schemas/StateIdentityDto"
        workflow_execution_status:
          type: string
          nullable: true
        workflow_execution_reason_code:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        remarks:
          type: array
          items:
            $ref: "#/components/schemas/WorkflowExecutionDtoRemark"
          nullable: true
        updated_at:
          type: string
          format: date-time
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        id:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowExecutionDtoRemark:
      type: object
      properties:
        remark:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        step_id:
          type: string
          nullable: true
        step_node_id:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowExecutionListDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        state_id:
          type: string
          nullable: true
        state_identity:
          $ref: "#/components/schemas/StateIdentityDto"
        workflow_execution_status:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowExecutionStatisticsFilters:
      type: object
      properties:
        from_date_time:
          type: string
          format: date-time
          nullable: true
        to_date_time:
          type: string
          format: date-time
          nullable: true
        workflow_type:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowExecutionUsageFilters:
      type: object
      properties:
        execution_from_date_time:
          type: string
          format: date-time
          nullable: true
        execution_to_date_time:
          type: string
          format: date-time
          nullable: true
        workflow_type:
          type: string
          nullable: true
        workflow_status:
          type: string
          nullable: true
        workflow_name:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowExecutionUsageListDto:
      type: object
      properties:
        workflow:
          $ref: "#/components/schemas/LightweightWorkflowDto"
        total_execution_count:
          type: integer
          format: int64
        failed_execution_count:
          type: integer
          format: int64
        last_enrolled_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    WorkflowFilters:
      type: object
      properties:
        activation_status:
          pattern: Active|Draft
          type: string
          nullable: true
        created_by_sleekflow_staff_id:
          type: string
          nullable: true
        updated_by_sleekflow_staff_id:
          type: string
          nullable: true
        updated_from_date_time:
          type: string
          format: date-time
          nullable: true
        updated_to_date_time:
          type: string
          format: date-time
          nullable: true
        workflow_type:
          type: string
          nullable: true
        dependency_workflow_id:
          type: string
          nullable: true
        agent_config_id:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowGroupDto:
      type: object
      properties:
        name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowListDto:
      type: object
      properties:
        workflow_id:
          type: string
          nullable: true
        workflow_versioned_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        workflow_type:
          type: string
          nullable: true
        workflow_group_id:
          type: string
          nullable: true
        triggers:
          $ref: "#/components/schemas/WorkflowTriggers"
        workflow_enrollment_settings:
          $ref: "#/components/schemas/WorkflowEnrollmentSettings"
        workflow_schedule_settings:
          $ref: "#/components/schemas/WorkflowScheduleSettings"
        activation_status:
          type: string
          nullable: true
        version:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
      additionalProperties: false
    WorkflowRecurringSettings:
      required:
        - recurring_type
      type: object
      properties:
        recurring_type:
          minLength: 1
          type: string
        day:
          type: integer
          format: int32
          nullable: true
        week:
          type: integer
          format: int32
          nullable: true
        month:
          type: integer
          format: int32
          nullable: true
        year:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WorkflowScheduleSettings:
      type: object
      properties:
        durable_payload:
          $ref: "#/components/schemas/DurablePayload"
        is_new_scheduled_workflow_schema:
          type: boolean
          nullable: true
        is_old_scheduled_workflow_schema_first_recurring_completed:
          type: boolean
          nullable: true
        scheduled_at:
          type: string
          format: date-time
          nullable: true
        contact_property_id:
          type: string
          nullable: true
        schema_id:
          type: string
          nullable: true
        schemaful_object_property_id:
          type: string
          nullable: true
        schedule_type:
          type: string
          nullable: true
        recurring_settings:
          $ref: "#/components/schemas/WorkflowRecurringSettings"
      additionalProperties: false
    WorkflowTrigger:
      required:
        - condition
      type: object
      properties:
        condition:
          minLength: 1
          type: string
      additionalProperties: false
    WorkflowTriggers:
      type: object
      properties:
        conversation_status_changed:
          $ref: "#/components/schemas/WorkflowTrigger"
        contact_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        contact_label_relationships_changed:
          $ref: "#/components/schemas/WorkflowTrigger"
        contact_list_relationships_changed:
          $ref: "#/components/schemas/WorkflowTrigger"
        contact_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        message_received:
          $ref: "#/components/schemas/WorkflowTrigger"
        message_sent:
          $ref: "#/components/schemas/WorkflowTrigger"
        webhook:
          $ref: "#/components/schemas/WorkflowTrigger"
        facebook_post_comment_received:
          $ref: "#/components/schemas/WorkflowTrigger"
        instagram_media_comment_received:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_account_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_account_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_contact_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_contact_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_lead_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_lead_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_opportunity_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_opportunity_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_campaign_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_campaign_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_custom_object_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_custom_object_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        click_to_whatsapp_ads_message_received:
          $ref: "#/components/schemas/WorkflowTrigger"
        schemaful_object_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        schemaful_object_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_account_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_contact_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_lead_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_opportunity_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_campaign_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_custom_object_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        scheduled_workflow_contact_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        contact_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        contact_manually_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        schemaful_object_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        whatsapp_flow_submission_message_received:
          $ref: "#/components/schemas/WorkflowTrigger"
        message_status_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        ticket_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        google_sheets_row_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        google_sheets_row_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        hubspot_object_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        hubspot_object_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        hubspot_object_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        zoho_object_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        zoho_object_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        zoho_object_enrolled:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_object_created:
          $ref: "#/components/schemas/WorkflowTrigger"
        salesforce_object_updated:
          $ref: "#/components/schemas/WorkflowTrigger"
        meta_detected_outcome_received:
          $ref: "#/components/schemas/WorkflowTrigger"
      additionalProperties: false
    WorkflowWebhookTrigger:
      type: object
      properties:
        _etag:
          type: string
          nullable: true
        workflow_id:
          type: string
          nullable: true
        validation_token:
          type: string
          nullable: true
        object_id_expression:
          type: string
          nullable: true
        object_type:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: "#/components/schemas/SleekflowStaff"
        updated_by:
          $ref: "#/components/schemas/SleekflowStaff"
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
