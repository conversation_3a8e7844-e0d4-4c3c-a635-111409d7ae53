namespace Sleekflow.FlowHub.Utils;

public static class TimeUnitUtils
{
    public static TimeSpan? ConvertToTimeSpan(string? timeUnit, double? units)
    {
        if (timeUnit is null || units is null)
        {
            return null;
        }

        return timeUnit.ToLower() switch
        {
            "second" => TimeSpan.FromSeconds(units.Value),
            "minute" => TimeSpan.FromMinutes(units.Value),
            "hour" => TimeSpan.FromHours(units.Value),
            "day" => TimeSpan.FromDays(units.Value),
            _ => throw new InvalidOperationException("Invalid time unit")
        };
    }
}