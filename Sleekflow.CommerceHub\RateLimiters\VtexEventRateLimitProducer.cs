﻿using MassTransit.Internals;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.RateLimits;
using Sleekflow.RateLimits.LuaScripts;

namespace Sleekflow.CommerceHub.RateLimiters;

/// <summary>
/// Publish events with Rate limit.
/// <br/><br/>Strategy: Sliding window + Next available timeslot.
/// <br/><br/>Granularity : fixed to per company per event.
/// </summary>
public interface IVtexEventRateLimitProducer
{
    Task PublishWithRateLimitAsync<T>(
        T @event,
        string companyId,
        SlidingWindowParam? slidingWindowParam = null,
        CancellationToken cancellationToken = default)
        where T : class;
}

public class VtexEventRateLimitProducer : ISingletonService, IVtexEventRateLimitProducer
{
    private readonly ISlidingWindowNextAvailableSlotRateLimiter _slidingWindowNextAvailableSlotRateLimiter;
    private readonly IIdService _idService;
    private readonly IHostEnvironment _env;

    private readonly Dictionary<string, SlidingWindowParam> _slidingWindowParamsDev =
        new ()
        {
            {
                typeof(OnVtexOrderHookReceivedEvent).GetTypeName(), new SlidingWindowParam(60, 50)
            },
            {
                typeof(OnVtexOrderForFlowHubEnrollmentFetchedEvent).GetTypeName(), new SlidingWindowParam(1, 5)
            },
        };

    private readonly Dictionary<string, SlidingWindowParam> _slidingWindowParamsStag =
        new ()
        {
            {
                typeof(OnVtexOrderHookReceivedEvent).GetTypeName(), new SlidingWindowParam(60, 100)
            },
            {
                typeof(OnVtexOrderForFlowHubEnrollmentFetchedEvent).GetTypeName(), new SlidingWindowParam(1, 5)
            },
        };

    private readonly Dictionary<string, SlidingWindowParam> _slidingWindowParamsProd =
        new ()
        {
            {
                typeof(OnVtexOrderHookReceivedEvent).GetTypeName(), new SlidingWindowParam(60, 200)
            },
            {
                typeof(OnVtexOrderForFlowHubEnrollmentFetchedEvent).GetTypeName(), new SlidingWindowParam(1, 10)
            },
        };

    public VtexEventRateLimitProducer(ISlidingWindowNextAvailableSlotRateLimiter slidingWindowNextAvailableSlotRateLimiter, IIdService idService, IHostEnvironment env)
    {
        _slidingWindowNextAvailableSlotRateLimiter = slidingWindowNextAvailableSlotRateLimiter;
        _idService = idService;
        _env = env;
    }

    public async Task PublishWithRateLimitAsync<T>(
        T @event,
        string companyId,
        SlidingWindowParam? slidingWindowParam = null,
        CancellationToken cancellationToken = default) where T : class
    {
        slidingWindowParam ??= GetSlidingWindowParam<T>();

        var keyName = RateLimitCacheKeyBuilder<T>.BuildCacheKeyOnCompanyId(
            companyId);

        var uniqueMessageId = _idService.GetId(typeof(T).GetTypeName());

        await _slidingWindowNextAvailableSlotRateLimiter.PublishWithRateLimitAsync(
            @event,
            keyName,
            new SlidingWindowNextAvailableSlotParam(
                slidingWindowParam,
                DateTimeOffset.Now,
                uniqueMessageId),
            cancellationToken);
    }

    private SlidingWindowParam GetSlidingWindowParam<T>() where T : class
    {
        var typeName = typeof(T).GetTypeName();

        SlidingWindowParam? slidingWindowParam = null;

        if (_env.IsDevelopment())
        {
            _slidingWindowParamsDev.TryGetValue(typeName, out slidingWindowParam);
        }
        else if (_env.IsStaging())
        {
            _slidingWindowParamsStag.TryGetValue(typeName, out slidingWindowParam);
        }
        else
        {
            _slidingWindowParamsProd.TryGetValue(typeName, out slidingWindowParam);
        }

        if (slidingWindowParam == null)
        {
            throw new SfRateLimitConfigNotFoundException(typeName);
        }

        return slidingWindowParam;
    }
}