using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.CommerceHub.Stores.ShopifyStores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Stores.ShopifyStores;

[TriggerGroup(ControllerNames.ShopifyStores)]
public class GetShopifyStoreIntegrationExternalConfig
    : ITrigger<
        GetShopifyStoreIntegrationExternalConfig.GetShopifyStoreIntegrationExternalConfigInput,
        GetShopifyStoreIntegrationExternalConfig.GetShopifyStoreIntegrationExternalConfigOutput>
{
    private readonly IShopifyStoreService _shopifyStoreService;
    private readonly IStoreService _storeService;

    public GetShopifyStoreIntegrationExternalConfig(
        IShopifyStoreService shopifyStoreService,
        IStoreService storeService)
    {
        _shopifyStoreService = shopifyStoreService;
        _storeService = storeService;
    }

    public class GetShopifyStoreIntegrationExternalConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [JsonConstructor]
        public GetShopifyStoreIntegrationExternalConfigInput(
            string sleekflowCompanyId,
            string storeId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
        }
    }

    public class GetShopifyStoreIntegrationExternalConfigOutput
    {
        [JsonProperty("shopify_store_integration_external_config")]
        public ShopifyStoreIntegrationExternalConfig ShopifyStoreIntegrationExternalConfig { get; set; }

        [JsonConstructor]
        public GetShopifyStoreIntegrationExternalConfigOutput(
            ShopifyStoreIntegrationExternalConfig shopifyStoreIntegrationExternalConfig)
        {
            ShopifyStoreIntegrationExternalConfig = shopifyStoreIntegrationExternalConfig;
        }
    }

    public async Task<GetShopifyStoreIntegrationExternalConfigOutput> F(
        GetShopifyStoreIntegrationExternalConfigInput getShopifyStoreIntegrationExternalConfigInput)
    {
        var shopifyStore = await _storeService.GetStoreAsync(
            getShopifyStoreIntegrationExternalConfigInput.StoreId,
            getShopifyStoreIntegrationExternalConfigInput.SleekflowCompanyId);

        return new GetShopifyStoreIntegrationExternalConfigOutput(
            await _shopifyStoreService.GetShopifyStoreIntegrationExternalConfigAsync(shopifyStore));
    }
}