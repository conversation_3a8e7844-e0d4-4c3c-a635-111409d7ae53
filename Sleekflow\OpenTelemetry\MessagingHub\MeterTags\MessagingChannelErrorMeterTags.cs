using Sleekflow.OpenTelemetry.Meters.Interfaces;

namespace Sleekflow.OpenTelemetry.MessagingHub.MeterTags;

public class MessagingChannelErrorMeterTags : IMeterTags
{
    private string ErrorCode { get; }

    private string? ErrorSubCode { get; set; }

    public MessagingChannelErrorMeterTags(string errorCode, string? errorSubCode)
    {
        ErrorCode = errorCode;
        ErrorSubCode = errorSubCode;
    }

    public KeyValuePair<string, object>[] ToMeterTags()
    {
        if (ErrorSubCode != null)
        {
            return new[]
            {
                new KeyValuePair<string, object>("error_code", ErrorCode),
                new KeyValuePair<string, object>("error_sub_code", ErrorSubCode)
            };
        }

        return new[]
        {
            new KeyValuePair<string, object>("error_code", ErrorCode)
        };
    }
}