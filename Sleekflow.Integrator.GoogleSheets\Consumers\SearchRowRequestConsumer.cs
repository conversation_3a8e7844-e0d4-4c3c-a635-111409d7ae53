﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.Services;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;

namespace Sleekflow.Integrator.GoogleSheets.Consumers;

public class SearchRowsRequestConsumerDefinition : ConsumerDefinition<SearchRowRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<SearchRowRequestConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class SearchRowRequestConsumer : IConsumer<SearchGoogleSheetsRowRequest>
{
    private readonly IGoogleSheetsObjectService _googleSheetsObjectService;
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;
    private readonly IBus _bus;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly ILogger<SearchRowRequestConsumer> _logger;

    public SearchRowRequestConsumer(
        IGoogleSheetsObjectService googleSheetsObjectService,
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsConnectionService googleSheetsConnectionService,
        IBus bus,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        ILogger<SearchRowRequestConsumer> logger)
    {
        _googleSheetsObjectService = googleSheetsObjectService;
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
        _bus = bus;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _logger = logger;
    }

    public async Task Consume(ConsumeContext<SearchGoogleSheetsRowRequest> context)
    {
        var request = context.Message;

        var consumeId = Guid.NewGuid().ToString();

        try
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.GoogleSheetsSearchRowRequestReceived,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "spreadsheet_id", request.SpreadsheetId
                    },
                    {
                        "worksheet_id", request.WorksheetId
                    }
                });

            var connection = await _googleSheetsConnectionService.GetByIdAsync(
                request.ConnectionId,
                request.SleekflowCompanyId);

            var authentication =
                await _googleSheetsAuthenticationService.GetAsync(
                    connection.AuthenticationId,
                    request.SleekflowCompanyId);
            if (authentication == null)
            {
                throw new SfUnauthorizedException();
            }

            var rows = await _googleSheetsObjectService.SearchObjectsAsync(
                authentication,
                "Row",
                new List<TypedId>
                {
                    new ("Spreadsheet", request.SpreadsheetId),
                    new ("Worksheet", request.WorksheetId),
                    new ("HeaderRow", request.HeaderRowId),
                },
                request.Conditions.Select(
                    c =>
                        new Sleekflow.CrmHub.Models.InflowActions.SearchObjectCondition(
                            c.FieldName,
                            c.Operator,
                            c.Value)).ToList());

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.GoogleSheetsSearchRowRequestHandled,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    },
                    {
                        "spreadsheet_id", request.SpreadsheetId
                    },
                    {
                        "worksheet_id", request.WorksheetId
                    },
                });

            var row = rows.Count == 0 ? new Dictionary<string, object?>() : rows[0];
            await _bus.Publish(
                new OnGoogleSheetsCompleteStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    JsonConvert.SerializeObject(row)));
        }
        catch (Exception ex)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.GoogleSheetsSearchRowRequestFailed,
                new Dictionary<string, string>
                {
                    {
                        "consume_id", consumeId
                    },
                    {
                        "sleekflow_company_id", request.SleekflowCompanyId
                    },
                    {
                        "connection_id", request.ConnectionId
                    }
                });

            _logger.LogError(
                ex,
                "SearchGoogleSheetsRowRequest failed,"
                + " ConsumeId: {ConsumeId},"
                + " SleekflowCompanyId: {SleekflowCompanyId},"
                + " ConnectionId: {ConnectionId},"
                + " Conditions: {Conditions},"
                + " AggregateStepId: {AggregateStepId},"
                + " ProxyStateId: {ProxyStateId},"
                + " StackEntries: {StackEntries},",
                consumeId,
                request.SleekflowCompanyId,
                request.ConnectionId,
                JsonConvert.SerializeObject(request.Conditions),
                request.AggregateStepId,
                request.ProxyStateId,
                request.StackEntries);

            await _bus.Publish(
                new OnGoogleSheetsFailStepActivationEvent(
                    request.AggregateStepId,
                    request.ProxyStateId,
                    request.StackEntries,
                    null,
                    ex));
        }
    }
}