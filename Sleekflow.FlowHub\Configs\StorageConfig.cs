using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Configs;

public interface IStorageConfig
{
    string FileStorageConnStr { get; }
}

public class StorageConfig : IConfig, IStorageConfig
{
    public string FileStorageConnStr { get; }

    public StorageConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        FileStorageConnStr =
            Environment.GetEnvironmentVariable("FILE_STORAGE_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("FILE_STORAGE_CONN_STR");
    }
}