using Newtonsoft.Json;
using Sleekflow.Utils;

namespace Sleekflow.Exceptions.TenantHub;

public class SfInvalidValue
{
    [JsonRequired]
    public int Code { get; set; }

    [JsonRequired]
    public string Message { get; set; }

    [JsonRequired]
    public string Field { get; set; }

    [JsonRequired]
    public string Value { get; set; }

    public SfInvalidValue(int code, string message, string field, string value)
    {
        Code = code;
        Message = message;
        Field = field;
        Value = value;
    }
}

public class SfInvalidValueException : ErrorCodeException
{
    public SfInvalidValueException(string field, string value)
        : base(
            ErrorCodeConstant.SfInvalidValueException,
            $"Invalid or missing required value for field '{field}': {value}",
            new Dictionary<string, object?>
            {
                { "field", field },
                { "value", value }
            })
    {
    }
}
