using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Evaluations;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnScheduledWorkflowEnrollmentEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName => EventNames.OnScheduledWorkflowEnrolled;

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [Required]
    [JsonProperty("contact_owner")]
    public Dictionary<string, object?>? ContactOwner { get; set; }

    [Required]
    [JsonProperty("lists")]
    public ContactList[] Lists { get; set; }

    [JsonProperty("conversation")]
    public ContactConversation? Conversation { get; set; }

    [JsonConstructor]
    public OnScheduledWorkflowEnrollmentEventBody(
        DateTimeOffset createdAt,
        string contactId,
        string workflowId,
        string workflowVersionedId,
        Dictionary<string, object?> contact,
        Dictionary<string, object?>? contactOwner,
        ContactList[] lists,
        ContactConversation? conversation)
        : base(createdAt)
    {
        ContactId = contactId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        Contact = contact;
        ContactOwner = contactOwner;
        Lists = lists;
        Conversation = conversation;
    }
}