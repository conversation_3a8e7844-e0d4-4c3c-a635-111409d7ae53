using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Polly;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;
using Sleekflow.Webhooks;
using JsonConfig = Sleekflow.JsonConfigs.JsonConfig;

namespace Sleekflow.DataMigrator.Executors;

internal class PatchWebhookHubMaxRetryCount : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private string? _containerId;

    public PatchWebhookHubMaxRetryCount(DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Patch Webhook Hub MaxRetryCount";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerId = Prompt.Select(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerId = selectedContainerId;
    }

    public async Task ExecuteAsync()
    {
        if (!string.IsNullOrEmpty(_containerId))
        {
            var count = await MigrateObjectsAsync(_containerId);
            Console.WriteLine($"Completed {_containerId} for {count} objects");
        }
        else
        {
            Console.WriteLine("Unable to start PatchWebhookHubMaxRetryCount");
        }
    }

    private async Task<int> MigrateObjectsAsync(
        string containerId)
    {
        var retryPolicy = CosmosUtils.GetDefaultRetryPolicy(containerId);

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerId);
        var partitionKeyPaths =
            (await CosmosUtils.GetContainerPartitionKeyPathsAsync(_cosmosClient, _databaseId!, containerId))!;

        var i = 0;
        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(
                container,
                "SELECT * FROM root c WHERE (NOT IS_DEFINED(c.max_retry_count) or c.max_retry_count = null or c.max_retry_count = 1) AND c.event_type_name = 'OnWhatsappCloudApiMessagesRecieved'"),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 100
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(
                        async () =>
                        {
                            dict["max_retry_count"] = 5;

                            await container.ReplaceItemAsync(
                                dict,
                                dict["id"].ToString(),
                                new PartitionKey(dict["id"].ToString()),
                                new ItemRequestOptions
                                {
                                    IfMatchEtag = (string) dict["_etag"]!, EnableContentResponseOnWrite = false,
                                },
                                token);
                        });

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }


}