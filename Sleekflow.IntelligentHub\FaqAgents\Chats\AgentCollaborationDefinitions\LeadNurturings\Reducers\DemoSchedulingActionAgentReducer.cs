using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;

public class DemoSchedulingPlanningAgentReducer : ActionAgentGeminiChatHistoryReducer
{
    private readonly IInformationGatheringPlugin _informationGatheringPlugin;
    private readonly ILogger<DemoSchedulingPlanningAgentReducer> _logger;
    private readonly Kernel _kernel;

    public DemoSchedulingPlanningAgentReducer(
        ILeadNurturingAgentDefinitions leadNurturingAgentDefinitions,
        IInformationGatheringPlugin informationGatheringPlugin,
        ILogger<DemoSchedulingPlanningAgentReducer> logger,
        Kernel kernel)
        : base(leadNurturingAgentDefinitions)
    {
        _informationGatheringPlugin = informationGatheringPlugin;
        _logger = logger;
        _kernel = kernel;
    }

    public override async Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        // First, let the base class filter the messages
        var reducedChatHistory = await base.ReduceAsync(chatHistory, cancellationToken);
        if (reducedChatHistory == null)
        {
            return null;
        }

        var chatMessageContents = reducedChatHistory.ToList();

        // Find the context message that contains the conversation history
        var contextMessage = chatMessageContents.FirstOrDefault(m => m.AuthorName == "Context");
        if (contextMessage == null || string.IsNullOrEmpty(contextMessage.Content))
        {
            // If there's no context message, just return the original reduced history
            return chatMessageContents;
        }

        try
        {
            // Call the plugin to extract field information
            var extractFieldOutput = await _informationGatheringPlugin.ExtractFieldsAsync(
                _kernel,
                contextMessage.Content);

            // Add a new message with the extracted fields
            var extractedFieldsMessage = new ChatMessageContent(
                AuthorRole.Assistant,
                JsonConvert.SerializeObject(extractFieldOutput))
            {
                AuthorName = "InformationGatheringPlugin"
            };

            chatMessageContents.Add(extractedFieldsMessage);

            return chatMessageContents;
        }
        catch (Exception ex)
        {
            // Log the error and return the original reduced history
            _logger.LogError(ex, "Error extracting field information");
            return chatMessageContents;
        }
    }
}