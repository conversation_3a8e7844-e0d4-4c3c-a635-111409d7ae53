﻿namespace Sleekflow.Utils;

public static class DateTimeOffsetUtils
{
    // Rounds up the given DateTimeOffset to the nearest specified number of seconds.
    public static DateTimeOffset RoundUp(DateTimeOffset time, int roundUpToSeconds)
    {
        if (roundUpToSeconds <= 0)
        {
            throw new ArgumentOutOfRangeException(nameof(roundUpToSeconds), "roundUpToSeconds must be positive.");
        }

        var currentUnixSeconds = time.ToUnixTimeSeconds();

        var remainder = currentUnixSeconds % roundUpToSeconds;

        long secondsToAdd;

        if (remainder == 0)
        {
            // If the original 'time' had any sub-second parts (ticks), it should be rounded up to the next interval.
            var hasSubSeconds = (time.Ticks % TimeSpan.TicksPerSecond) != 0;

            secondsToAdd = hasSubSeconds ? roundUpToSeconds : 0;
        }
        else
        {
            secondsToAdd = roundUpToSeconds - remainder;
        }


        var roundedUnixSeconds = currentUnixSeconds + secondsToAdd;

        return DateTimeOffset.FromUnixTimeSeconds(roundedUnixSeconds).ToOffset(time.Offset);
    }
}