﻿using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.States.StatusQueryGetOutput;
using Sleekflow.DurablePayloads;

namespace Sleekflow.CommerceHub.Models.States.LoopThroughObjects;

public class LoopThroughObjectsProgressStateObj
{
    [JsonProperty("durable_payload")]
    public DurablePayload DurablePayload { get; set; }

    [JsonProperty("query_output")]
    public StatusQueryGetOutput<
        Input,
        CustomStatus,
        Output>? QueryOutput { get; set; }

    [JsonConstructor]
    public LoopThroughObjectsProgressStateObj(
        DurablePayload durablePayload,
        StatusQueryGetOutput<
                Input,
                CustomStatus,
                Output>?
            queryOutput)
    {
        DurablePayload = durablePayload;
        QueryOutput = queryOutput;
    }
}