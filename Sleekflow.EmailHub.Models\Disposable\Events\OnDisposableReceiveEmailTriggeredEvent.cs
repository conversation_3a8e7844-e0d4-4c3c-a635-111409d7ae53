using Sleekflow.Events;

namespace Sleekflow.EmailHub.Models.Disposable.Events;

public class OnDisposableReceiveEmailTriggeredEvent : IEvent
{
    public OnDisposableReceiveEmailTriggeredEvent(
        string sleekflowCompanyId,
        string to,
        Dictionary<string, string> emailMetadata)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EmailMetadata = emailMetadata;
        To = to;
    }

    public string SleekflowCompanyId { get; set; }

    public Dictionary<string, string> EmailMetadata { get; set; }

    public string To { get; set; }
}