﻿using Sleekflow.Events;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Events;

public class OnProviderInitializedEvent : IEvent, IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public string ProviderName { get; set; }

    public OnProviderInitializedEvent(
        string sleekflowCompanyId,
        string providerName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ProviderName = providerName;
    }
}