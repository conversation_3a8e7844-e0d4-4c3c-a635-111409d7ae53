﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderConfigs : ITrigger
{
    private readonly IProviderConfigService _providerConfigService;

    public GetProviderConfigs(
        IProviderConfigService providerConfigService)
    {
        _providerConfigService = providerConfigService;
    }

    public class SyncConfigDto
    {
        public SyncConfigDto(SyncConfig syncConfig)
        {
            FilterGroups = syncConfig.FilterGroups;
            Filters = syncConfig.FilterGroups.SelectMany(fg => fg.Filters).ToList();
            FieldFilters = syncConfig.FieldFilters;
            Interval = syncConfig.Interval;
            EntityTypeName = syncConfig.EntityTypeName;
            SyncMode = syncConfig.SyncMode;
        }

        [JsonProperty("filter_groups")]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("filters")]
        public List<SyncConfigFilter> Filters { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonProperty("interval")]
        public int Interval { get; set; }

        [JsonProperty("entity_type_name")]
        public string EntityTypeName { get; set; }

        [JsonProperty("sync_mode")]
        public string? SyncMode { get; set; }
    }

    public class ProviderConfigDto : IHasRecordStatuses
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("key")]
        public string Key { get; set; }

        [JsonProperty("entity_type_name_to_sync_config_dict")]
        public Dictionary<string, SyncConfigDto> EntityTypeNameToSyncConfigDict { get; set; }

        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("is_authenticated")]
        public bool IsAuthenticated { get; set; }

        [JsonProperty("default_region_code")]
        public string DefaultRegionCode { get; set; }

        [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
        public List<string> RecordStatuses { get; set; }

        [JsonProperty("provider_limit")]
        public ProviderLimit? ProviderLimit { get; set; }

        public ProviderConfigDto(
            ProviderConfig providerConfig)
        {
            Id = providerConfig.Id;
            SleekflowCompanyId = providerConfig.SleekflowCompanyId;
            Key = providerConfig.Key;
            EntityTypeNameToSyncConfigDict =
                providerConfig.EntityTypeNameToSyncConfigDict.ToDictionary(e => e.Key, e => new SyncConfigDto(e.Value));
            ProviderName = providerConfig.ProviderName;
            IsAuthenticated = providerConfig.IsAuthenticated;
            RecordStatuses = providerConfig.RecordStatuses;
            DefaultRegionCode = providerConfig.DefaultRegionCode;
            ProviderLimit = providerConfig.ProviderLimit;
        }
    }

    public class GetProviderConfigsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetProviderConfigsInput(
            string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetProviderConfigsOutput
    {
        [JsonProperty("provider_configs")]
        public List<ProviderConfigDto> ProviderConfigs { get; set; }

        [JsonConstructor]
        public GetProviderConfigsOutput(List<ProviderConfigDto> providerConfigs)
        {
            ProviderConfigs = providerConfigs;
        }
    }

    public async Task<GetProviderConfigsOutput> F(
        GetProviderConfigsInput getProviderConfigsInput)
    {
        var providerConfigs = await _providerConfigService.GetProviderConfigsAsync(
            getProviderConfigsInput.SleekflowCompanyId);

        return new GetProviderConfigsOutput(
            providerConfigs
                .Where(pc => pc.IsAuthenticated)
                .Select(pc => new ProviderConfigDto(pc))
                .ToList());
    }
}