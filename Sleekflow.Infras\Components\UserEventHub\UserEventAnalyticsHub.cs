using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using App = Pulumi.AzureNative.App.V20240301;
using Cache = Pulumi.AzureNative.Cache;
using ContainerRegistry = Pulumi.AzureNative.ContainerRegistry;
using Docker = Pulumi.Docker;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Storage = Pulumi.AzureNative.Storage;

namespace Sleekflow.Infras.Components.UserEventHub;

public class UserEventAnalyticsHub
{
    private readonly ContainerRegistry.Registry _registry;
    private readonly Output<string> _registryUsername;
    private readonly Output<string> _registryPassword;
    private readonly ResourceGroup _resourceGroup;
    private readonly ManagedEnvAndAppsTuple _managedEnvAndAppsTuple;
    private readonly Db.DbOutput _dbOutput;
    private readonly UserEventHubDb.UserEventHubDbOutput _userEventHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly GcpConfig _gcpConfig;

    public UserEventAnalyticsHub(
        ContainerRegistry.Registry registry,
        Output<string> registryUsername,
        Output<string> registryPassword,
        ResourceGroup resourceGroup,
        ManagedEnvAndAppsTuple managedEnvAndAppsTuple,
        Db.DbOutput dbOutput,
        UserEventHubDb.UserEventHubDbOutput userEventHubDbOutput,
        MyConfig myConfig,
        GcpConfig gcpConfig)
    {
        _registry = registry;
        _registryUsername = registryUsername;
        _registryPassword = registryPassword;
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuple = managedEnvAndAppsTuple;
        _dbOutput = dbOutput;
        _userEventHubDbOutput = userEventHubDbOutput;
        _myConfig = myConfig;
        _gcpConfig = gcpConfig;
    }

    public App.ContainerApp InitUserEventAnalyticsHub(
        Cache.Redis userEventHubRedis,
        Storage.StorageAccount uehAnalyticsStorageAccount,
        Storage.BlobContainer uehAnalyticsStorageAccountEventsContainer,
        Storage.BlobContainer uehAnalyticsStorageAccountResultsContainer)
    {
        var myImage = ImageUtils.CreateImage(
            _registry,
            _registryUsername,
            _registryPassword,
            ServiceNames.GetSleekflowPrefixedShortName(ServiceNames.UserEventAnalyticsHub),
            _myConfig.BuildTime);

        var containerApps = _managedEnvAndAppsTuple.ContainerApps;
        var managedEnvironment = _managedEnvAndAppsTuple.ManagedEnvironment;
        var logAnalyticsWorkspace = _managedEnvAndAppsTuple.LogAnalyticsWorkspace;
        var serviceBus = _managedEnvAndAppsTuple.ServiceBus;
        var eventhub = _managedEnvAndAppsTuple.EventHub;
        var massTransitBlobStorage = _managedEnvAndAppsTuple.MassTransitBlobStorage;

        var listRedisKeysOutput = Output
            .Tuple(_resourceGroup.Name, userEventHubRedis.Name, userEventHubRedis.Id)
            .Apply(
                t => Cache.ListRedisKeys.InvokeAsync(
                    new Cache.ListRedisKeysArgs
                    {
                        ResourceGroupName = t.Item1, Name = t.Item2
                    }));

        var workspaceSharedKeys = Output
            .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
            .Apply(
                items => OperationalInsights.GetSharedKeys.InvokeAsync(
                    new OperationalInsights.GetSharedKeysArgs
                    {
                        ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                    }));

        var storageAccountKeys = Storage.ListStorageAccountKeys.Invoke(
            new Storage.ListStorageAccountKeysInvokeArgs
            {
                ResourceGroupName = _resourceGroup.Name, AccountName = uehAnalyticsStorageAccount.Name
            });

        var containerAppName = _managedEnvAndAppsTuple.FormatContainerAppName(
            ServiceNames.GetShortName(ServiceNames.UserEventAnalyticsHub));

        var containerApp = new App.ContainerApp(
            containerAppName,
            new App.ContainerAppArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ManagedEnvironmentId = managedEnvironment.Id,
                ContainerAppName = containerAppName,
                Location = LocationNames.GetAzureLocation(_managedEnvAndAppsTuple.LocationName),
                Configuration = new App.Inputs.ConfigurationArgs
                {
                    Ingress = new App.Inputs.IngressArgs
                    {
                        External = false,
                        TargetPort = 80,
                        Traffic = new InputList<App.Inputs.TrafficWeightArgs>
                        {
                            new App.Inputs.TrafficWeightArgs
                            {
                                LatestRevision = true, Weight = 100
                            }
                        },
                    },
                    Registries =
                    {
                        new App.Inputs.RegistryCredentialsArgs
                        {
                            Server = _registry.LoginServer,
                            Username = _registryUsername,
                            PasswordSecretRef = "registry-password-secret",
                        }
                    },
                    Secrets =
                    {
                        new App.Inputs.SecretArgs
                        {
                            Name = "registry-password-secret", Value = _registryPassword
                        },
                        new App.Inputs.SecretArgs
                        {
                            Name = "service-bus-conn-str-secret", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr
                        },
                        new App.Inputs.SecretArgs
                        {
                            Name = "service-bus-keda-conn-str-secret",
                            Value = serviceBus.CrmHubKedaPolicyKeyPrimaryConnStr
                        },
                        new App.Inputs.SecretArgs
                        {
                            Name = "event-hub-conn-str-secret", Value = eventhub.NamespacePrimaryConnStr
                        },
                    },
                    ActiveRevisionsMode = App.ActiveRevisionsMode.Single,
                },
                Template = new App.Inputs.TemplateArgs
                {
                    TerminationGracePeriodSeconds = 5 * 60,
                    Scale = new App.Inputs.ScaleArgs
                    {
                        MinReplicas = 1,
                        MaxReplicas = 40,
                        Rules = new List<string>
                            {
                                "on-sql-job-submitted-event",
                                "on-short-sql-job-submitted-request",
                            }
                            .Select(
                                queueName => new App.Inputs.ScaleRuleArgs
                                {
                                    Name = $"azure-servicebus-{queueName}",
                                    Custom = new App.Inputs.CustomScaleRuleArgs
                                    {
                                        Type = "azure-servicebus",
                                        Metadata = new InputMap<string>
                                        {
                                            {
                                                "queueName", queueName
                                            },
                                            {
                                                "messageCount", "2"
                                            },
                                        },
                                        Auth = new InputList<App.Inputs.ScaleRuleAuthArgs>
                                        {
                                            new App.Inputs.ScaleRuleAuthArgs
                                            {
                                                TriggerParameter = "connection",
                                                SecretRef = "service-bus-keda-conn-str-secret"
                                            },
                                        }
                                    }
                                })
                            .Concat(
                                new List<App.Inputs.ScaleRuleArgs>()
                                {
                                    new App.Inputs.ScaleRuleArgs
                                    {
                                        Name = "http",
                                        Http = new App.Inputs.HttpScaleRuleArgs
                                        {
                                            Metadata = new InputMap<string>
                                            {
                                                {
                                                    "concurrentRequests", "80"
                                                }
                                            }
                                        }
                                    }
                                })
                            .ToList(),
                    },
                    Containers =
                    {
                        new App.Inputs.ContainerArgs
                        {
                            Name = "sleekflow-ueah-app",
                            Image = myImage.BaseImageName,
                            Resources = new App.Inputs.ContainerResourcesArgs
                            {
                                Cpu = 2.0, Memory = "4.0Gi"
                            },
                            Probes = new List<App.Inputs.ContainerAppProbeArgs>
                            {
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "liveness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/healthz/liveness", Port = 80, Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 8,
                                    PeriodSeconds = 2,
                                },
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "readiness",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/healthz/readiness", Port = 80, Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 8,
                                    TimeoutSeconds = 8,
                                    PeriodSeconds = 2,
                                },
                                new App.Inputs.ContainerAppProbeArgs
                                {
                                    Type = "startup",
                                    HttpGet = new App.Inputs.ContainerAppProbeHttpGetArgs
                                    {
                                        Path = "/healthz/startup", Port = 80, Scheme = App.Scheme.HTTP,
                                    },
                                    InitialDelaySeconds = 12,
                                    TimeoutSeconds = 8,
                                }
                            },
                            Env = EnvironmentVariablesUtils.GetDeduplicateEnvironmentVariables(
                                new List<App.Inputs.EnvironmentVarArgs>
                                {
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "ASPNETCORE_ENVIRONMENT", Value = "Production",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "DOTNET_RUNNING_IN_CONTAINER", Value = "true",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "ASPNETCORE_URLS", Value = "http://+:80",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                        Value = _managedEnvAndAppsTuple.InsightsComponent.ConnectionString,
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "SF_ENVIRONMENT", Value = _managedEnvAndAppsTuple.FormatSfEnvironment(),
                                    },

                                    #region UserEventHubDbConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_USER_EVENT_HUB_DB_ENDPOINT",
                                        Value = Output.Format(
                                            $"https://{_userEventHubDbOutput.AccountName}.documents.azure.com:443/"),
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_USER_EVENT_HUB_DB_KEY", Value = _userEventHubDbOutput.AccountKey,
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_USER_EVENT_HUB_DB_DATABASE_ID",
                                        Value = _userEventHubDbOutput.DatabaseId,
                                    },

                                    #endregion

                                    #region CacheConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "CACHE_PREFIX", Value = "Sleekflow.UserEventAnalyticsHub",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "REDIS_CONN_STR",
                                        Value = Output
                                            .Tuple(listRedisKeysOutput, userEventHubRedis.HostName)
                                            .Apply(
                                                t =>
                                                    $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                    },

                                    #endregion

                                    #region DbConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_ENDPOINT",
                                        Value = Output.Format(
                                            $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                    },

                                    #endregion

                                    #region CoreInternalConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "CORE_INTERNALS_ENDPOINT",
                                        Value = $"{GetCoreInternalDomain(_myConfig.Name)}/UserEventHub/Internals"
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "CORE_INTERNALS_KEY",
                                        Value = "mR5Cu2atwXj4TYWgrHh9pDNvQAyZFUnSELdf8csb3xJG7zPkKV"
                                    },

                                    #endregion

                                    #region ServiceBusConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "SERVICE_BUS_CONN_STR", SecretRef = "service-bus-conn-str-secret",
                                    },

                                    #endregion

                                    #region EventHubConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "EVENT_HUB_CONN_STR", SecretRef = "event-hub-conn-str-secret",
                                    },

                                    #endregion

                                    #region LoggerConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "LOGGER_AUTHENTICATION_ID",
                                        Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", Value = _gcpConfig.CredentialJson,
                                    },

                                    #endregion

                                    #region MassTransitStorageConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "MESSAGE_DATA_CONN_STR",
                                        Value = massTransitBlobStorage.StorageAccountConnStr
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "MESSAGE_DATA_CONTAINER_NAME",
                                        Value = massTransitBlobStorage.ContainerName
                                    },

                                    #endregion

                                    #region StorageConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "ANALYTICS_STORAGE_CONN_STR",
                                        Value = StorageUtils.GetConnectionString(
                                            _resourceGroup.Name,
                                            uehAnalyticsStorageAccount.Name)
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "ANALYTICS_STORAGE_ACCOUNT_NAME", Value = uehAnalyticsStorageAccount.Name
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "ANALYTICS_STORAGE_ACCOUNT_KEY",
                                        Value = storageAccountKeys.Apply(
                                            keys =>
                                            {
                                                var primaryStorageKey = keys.Keys[0].Value;

                                                // Build the connection string to the storage account.
                                                return primaryStorageKey;
                                            })
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "RESULTS_CONTAINER_NAME",
                                        Value = uehAnalyticsStorageAccountResultsContainer.Name
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "EVENTS_CONTAINER_NAME",
                                        Value = uehAnalyticsStorageAccountEventsContainer.Name
                                    },

                                    #endregion

                                    #region ApplicationInsightTelemetryConfig

                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "TRUE",
                                    },
                                    new App.Inputs.EnvironmentVarArgs
                                    {
                                        Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                    },

                                    #endregion
                                })
                        }
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = managedEnvironment
            });

        containerApps.Add(ServiceNames.UserEventAnalyticsHub, containerApp);

        return containerApp;
    }

    private static string GetCoreInternalDomain(string name)
    {
        return name switch
        {
            "dev" => "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            "staging" => "https://sleekflow-core-staging-dycncqcebbf4ggag.z01.azurefd.net",
            "production" => "https://sleekflow-core-production-hac3h0azhvcub0aq.z01.azurefd.net",
            _ => throw new ArgumentOutOfRangeException(nameof(name), name, null)
        };
    }
}
