﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.AuditHubDb;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs;

[Resolver(typeof(IAuditHubDbResolver))]
[DatabaseId("audithubdb")]
[ContainerId("user_profile_audit_log")]
public class UserProfileAuditLog : Entity, IHasUpdatedBy, IHasSleekflowCompanyId, IHasSleekflowUserProfileId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sleekflow_user_profile_id")]
    public string SleekflowUserProfileId { get; set; }

    [JsonProperty("sleekflow_staff_id")]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("audit_log_text")]
    public string AuditLogText { get; set; }

    [JsonProperty("data")]
    public Dictionary<string, object?>? Data { get; set; }

    [JsonProperty("created_time")]
    public DateTimeOffset CreatedTime { get; set; }

    [JsonProperty("updated_time")]
    public DateTimeOffset? UpdatedTime { get; set; }

    [JsonProperty(IHasUpdatedBy.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonConstructor]
    public UserProfileAuditLog(
        string id,
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string sleekflowUserProfileId,
        string type,
        string auditLogText,
        Dictionary<string, object?>? data,
        DateTimeOffset createdTime,
        DateTimeOffset? updatedTime,
        AuditEntity.SleekflowStaff? updatedBy,
        int? ttl)
        : base(id, "UserProfileAuditLog", ttl)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowUserProfileId = sleekflowUserProfileId;
        Type = type;
        AuditLogText = auditLogText;
        Data = data;
        CreatedTime = createdTime;
        UpdatedTime = updatedTime;
        UpdatedBy = updatedBy;
    }

    public UserProfileAuditLog(
        string id,
        string sleekflowCompanyId,
        string? sleekflowStaffId,
        string sleekflowUserProfileId,
        string type,
        string auditLogText,
        Dictionary<string, object?>? data,
        DateTimeOffset createdTime,
        int? ttl)
        : this(
            id,
            sleekflowCompanyId,
            sleekflowStaffId,
            sleekflowUserProfileId,
            type,
            auditLogText,
            data,
            createdTime,
            null,
            null,
            ttl)
    {
    }
}