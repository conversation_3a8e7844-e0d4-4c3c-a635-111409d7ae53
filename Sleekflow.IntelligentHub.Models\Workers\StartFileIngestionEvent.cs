﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Documents;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Workers;

public class StartFileIngestionEvent : IHasSleekflowCompanyId
{
    [Required]
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [Required]
    [JsonProperty("document_id")]
    public string DocumentId { get; set; }

    [Required]
    [JsonProperty("sas_download_url")]
    public string SasDownloadUrl { get; set; }

    [JsonConstructor]
    public StartFileIngestionEvent(
        string sleekflowCompanyId,
        string documentId,
        string sasDownloadUrl)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        DocumentId = documentId;
        SasDownloadUrl = sasDownloadUrl;
    }
}