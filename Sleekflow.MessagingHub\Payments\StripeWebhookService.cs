using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps.Stripe;
using Sleekflow.MessagingHub.Payments.CloudApi;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Stripe;
using Event = Stripe.Event;
using ValidationResult = System.ComponentModel.DataAnnotations.ValidationResult;

namespace Sleekflow.MessagingHub.Payments;

public interface IStripeWebhookService
{
    Task ProcessStripeWebhookAsync(
        string stripePaymentWebhook,
        string stripeSignatureHeader);
}

public class StripeWebhookService : IStripeWebhookService, IScopedService
{
    private const string EventSource = "messaging-hub";
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IBusinessBalanceAutoTopUpProfileService _businessBalanceAutoTopUpProfileService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaService _wabaService;
    private readonly IBus _bus;
    private readonly ILogger<StripeWebhookService> _logger;
    private readonly IStripeClient _stripeClient;

    public StripeWebhookService(
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IWabaService wabaService,
        ILogger<StripeWebhookService> logger,
        IBusinessBalanceService businessBalanceService,
        IBus bus,
        IBusinessBalanceAutoTopUpProfileService businessBalanceAutoTopUpProfileService,
        IStripeClient stripeClient)
    {
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _wabaService = wabaService;
        _logger = logger;
        _businessBalanceService = businessBalanceService;
        _bus = bus;
        _businessBalanceAutoTopUpProfileService = businessBalanceAutoTopUpProfileService;
        _stripeClient = stripeClient;
    }

    public async Task ProcessStripeWebhookAsync(
        string stripePaymentWebhook,
        string stripeSignatureHeader)
    {
        var stripeEvent = JsonConvert.DeserializeObject<Event>(stripePaymentWebhook)!;

        switch (stripeEvent.Type)
        {
            case Stripe.Events.CheckoutSessionCompleted:
                return;
            case Stripe.Events.InvoicePaid:
                await HandleInvoicePaidCompletedEvent(stripeEvent);
                return;
            default: throw new NotImplementedException();
        }
    }

    private async Task HandleInvoicePaidCompletedEvent(
        Event stripeEvent)
    {
        var validatedInvoice = ConstructInvoice(stripeEvent);

        if (IsInvoicePaidEventSourceNotMessagingHub(validatedInvoice))
        {
            return;
        }

        if (WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiManualTopUpEvent(validatedInvoice.Metadata))
        {
            await HandleWhatsappCloudApiTopUpEvent(validatedInvoice, stripeEvent.Type);
            return;
        }

        if (WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiAutoTopUpEvent(validatedInvoice.Metadata))
        {
            await HandleWhatsappCloudApiTopUpEvent(validatedInvoice, stripeEvent.Type);

            var facebookBusinessIdExists = validatedInvoice.Metadata.TryGetValue(
                "facebook_business_id",
                out var facebookBusinessId);

            if (facebookBusinessIdExists && facebookBusinessId is not null && validatedInvoice.CustomerId is not null)
            {
                var hasDefaultPaymentMethod =
                    await _stripeClient.HasDefaultPaymentMethodAsync(validatedInvoice.CustomerId);

                var hasPaymentMethodUsedForSubscriptions =
                    (await _stripeClient.FindPaymentMethodsUsedForSubscriptionsForCustomerAsync(
                        validatedInvoice.CustomerId)).Any();

                // This is a special handling for the edge case - client that are not using stripe for paying the subscription in SleekFlow but want to use Stripe as the auto top-up payment provider
                // Set the payment method used for paying the auto top-up invoice as the default payment method for a customer
                if (!hasPaymentMethodUsedForSubscriptions && !hasDefaultPaymentMethod)
                {
                    var paymentIntentService = new PaymentIntentService();
                    var paymentIntent = await paymentIntentService.GetAsync(validatedInvoice.PaymentIntentId);

                    await _stripeClient.UpdateCustomerDefaultPaymentMethod(
                        validatedInvoice.CustomerId,
                        paymentIntent.PaymentMethodId);
                }

                await _businessBalanceAutoTopUpProfileService.UpdateBusinessBalanceAutoTopUpProfileCustomerIdAsync(
                    facebookBusinessId,
                    validatedInvoice.CustomerId);
            }

            return;
        }

        throw new NotImplementedException();
    }

    private static bool IsInvoicePaidEventSourceMessagingHub(Invoice invoice)
    {
        if (invoice.Metadata is null)
        {
            throw new NullReferenceException("invoice metadata is null");
        }

        invoice.Metadata.TryGetValue("source", out var source);

        if (source is null)
        {
            return false;
        }

        return source == EventSource;
    }

    private static bool IsInvoicePaidEventSourceNotMessagingHub(Invoice invoice)
    {
        return !IsInvoicePaidEventSourceMessagingHub(invoice);
    }

    private async Task HandleWhatsappCloudApiTopUpEvent(Invoice invoice, string stripeEventType)
    {
        var metadata = invoice.Metadata;
        var facebookBusinessId = metadata["facebook_business_id"];

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);

        if (wabas.Count == 0)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        var businessBalance =
            await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        metadata.TryGetValue("facebook_waba_id", out var facebookWabaId);

        var invoiceSnapshot = new Dictionary<string, object?>
        {
            {
                stripeEventType, invoice
            }
        };

        var stripeTopUpCreditDetail = ConstructStripeTopUpCreditDetail(invoice, invoiceSnapshot);

        var credit = ConstructTopUpCredit(metadata);

        invoice.Metadata.TryGetValue("credited_by", out var creditedBy);
        invoice.Metadata.TryGetValue("credited_by_display_name", out var creditedByDisplayName);

        var businessBalanceTransactionLog =
            await _businessBalanceTransactionLogService
                .CreateAndGetWithBusinessWabaTopUpAsync(
                    facebookBusinessId,
                    facebookWabaId,
                    businessBalance.MarkupProfile,

                    // For PowerFlow manual top up flow Travis_backend will provide the uniqueId else
                    // The unique Id should be using WabaTransactionItem.GetUniqueId
                    invoice.Id,
                    TopUpPaymentMethods.Stripe,
                    credit,

                    // This is the staff Id from the Travis_Backend database AspNetUsers
                    creditedBy,

                    // This is the display name of the staff from the Travis_Backend database UserProfile
                    creditedByDisplayName,

                    // This provide a snapshot of the stripe checkout session
                    stripeTopUpCreditDetail,
                    invoiceSnapshot!);

        var onCloudApiBusinessBalancePendingTransactionLogCreatedEvent =
            new OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent(
                businessBalanceTransactionLog.FacebookBusinessId);
        await _bus.Publish(onCloudApiBusinessBalancePendingTransactionLogCreatedEvent);
    }

    private static Invoice ConstructInvoice(Event stripeEvent)
    {
        return stripeEvent.Data.Object as Invoice ?? throw new InvalidOperationException();
    }

    private static StripeTopUpCreditDetail ConstructStripeTopUpCreditDetail(
        Invoice invoice,
        Dictionary<string, object?> invoiceSnapshot)
    {
        return new StripeTopUpCreditDetail(null!, invoice.CustomerId, invoice.Id, invoice.Metadata, invoiceSnapshot);
    }

    private static Money ConstructTopUpCredit(IReadOnlyDictionary<string, string> metadata)
    {
        var currencyIsoCode = metadata["whatsapp_cloud_api_top_up_plan_currency"];
        var amount = decimal.Parse(metadata["whatsapp_cloud_api_top_up_plan_amount"]);
        return new Money(currencyIsoCode, amount);
    }
}