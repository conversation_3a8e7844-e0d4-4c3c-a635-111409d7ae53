using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Shopify;

public class ShopifyDraftOrderLineItemDiscount
{
    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("value")]
    public decimal Value { get; set; }

    [JsonProperty("max_amount")]
    public decimal MaxAmount { get; set; }

    [JsonConstructor]
    public ShopifyDraftOrderLineItemDiscount(
        string title,
        string type,
        decimal value,
        decimal maxAmount)
    {
        Title = title;
        Type = type;
        Value = value;
        MaxAmount = maxAmount;
    }
}

public class ShopifyDraftOrderLineItem
{
    [JsonProperty("shopify_product_variant_id")]
    public long ShopifyProductVariantId { get; set; }

    [JsonProperty("quantity")]
    public int Quantity { get; set; }

    [JsonProperty("discount_option")]
    public ShopifyDraftOrderLineItemDiscount? Discount { get; set; }

    [JsonConstructor]
    public ShopifyDraftOrderLineItem(
        long shopifyProductVariantId,
        int quantity,
        ShopifyDraftOrderLineItemDiscount? discount)
    {
        ShopifyProductVariantId = shopifyProductVariantId;
        Quantity = quantity;
        Discount = discount;
    }
}