﻿using System.Text.Json;
using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.VectorData;
using Microsoft.SemanticKernel.Connectors.AzureOpenAI;
using Microsoft.SemanticKernel.Connectors.CosmosNoSql;
using Microsoft.SemanticKernel.Embeddings;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Models.Constants;

namespace Sleekflow.IntelligentHub.Caches;

public interface IKnowledgeRetrievalCacheService
{
    Task<ReadOnlyMemory<float>> GenerateEmbeddingAsync(string value);

    Task<KnowledgeRetrievalCacheRecord?> GetCacheRecordAsync(
        ReadOnlyMemory<float> embedding,
        string sleekflowCompanyId,
        string collaborationMode = AgentCollaborationModes.Default);

    Task UpsertAsync(
        string prompt,
        string result,
        string sleekflowCompanyId,
        ReadOnlyMemory<float> embedding,
        string? id = null,
        string collaborationMode = AgentCollaborationModes.Default);
}

public class KnowledgeRetrievalCacheService : IKnowledgeRetrievalCacheService, ISingletonService
{
    private readonly Database _database;
    private readonly IIdService _idService;
    private readonly ITextEmbeddingGenerationService _textEmbeddingGenerationService;

    public KnowledgeRetrievalCacheService(
        IIdService idService,
        IAzureCosmosVectorDBConfig dbConfig,
        IAzureEmbeddingConfig embeddingConfig)
    {
        _idService = idService;

        var cosmosClient = new CosmosClient(
            dbConfig.Endpoint,
            dbConfig.Key,
            new CosmosClientOptions
            {
                // When initializing CosmosClient manually, setting this property is required
                // due to limitations in default serializer.
                UseSystemTextJsonSerializerWithOptions = new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
                },
                ConnectionMode = ConnectionMode.Direct,
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
        _database = cosmosClient.GetDatabase(dbConfig.DatabaseId);

#pragma warning disable SKEXP0010
        _textEmbeddingGenerationService = new AzureOpenAITextEmbeddingGenerationService(
            embeddingConfig.DeploymentName,
            embeddingConfig.Endpoint,
            embeddingConfig.Key);
#pragma warning restore SKEXP0010
    }

    public Task<ReadOnlyMemory<float>> GenerateEmbeddingAsync(string value)
    {
        return _textEmbeddingGenerationService.GenerateEmbeddingAsync(value);
    }

    public async Task<KnowledgeRetrievalCacheRecord?> GetCacheRecordAsync(
        ReadOnlyMemory<float> embedding,
        string sleekflowCompanyId,
        string collaborationMode = AgentCollaborationModes.Default)
    {
        var collection = GetCollection();
        var results = await collection.SearchAsync(
                embedding,
                3,
                new VectorSearchOptions<KnowledgeRetrievalCacheRecord>
                {
                    VectorProperty = x => x.Embedding,
                    Filter = e =>
                        e.SleekflowCompanyId == sleekflowCompanyId
                        && e.CollaborationMode == collaborationMode
                })
            .ToListAsync();

        // The Result are already sorted by score in descending order where the first result is the best match
        var vectorSearchResult = results.FirstOrDefault();
        if (vectorSearchResult is null || !vectorSearchResult.Score.HasValue)
        {
            return null;
        }

        var vectorScore = ScoreToSimilarity(vectorSearchResult.Score.Value);

        return vectorScore < 0.9 ? null : vectorSearchResult.Record;
    }

    public async Task UpsertAsync(
        string prompt,
        string result,
        string sleekflowCompanyId,
        ReadOnlyMemory<float> embedding,
        string? id = null,
        string collaborationMode = AgentCollaborationModes.Default)
    {
        var collection = GetCollection();
        var cacheRecord = new KnowledgeRetrievalCacheRecord
        {
            Id = id ?? _idService.GetId(SysTypeNames.KnowledgeRetrievalCache),
            SleekflowCompanyId = sleekflowCompanyId,
            Prompt = prompt,
            Result = result,
            CollaborationMode = collaborationMode,
            Embedding = embedding,
        };
        await collection.UpsertAsync(cacheRecord);
    }

    private CosmosNoSqlCollection<string, KnowledgeRetrievalCacheRecord> GetCollection()
    {
        return new CosmosNoSqlCollection<string, KnowledgeRetrievalCacheRecord>(
            _database,
            ContainerNames.KnowledgeRetrievalCache,
            options: new CosmosNoSqlCollectionOptions()
            {
                PartitionKeyPropertyName = nameof(KnowledgeRetrievalCacheRecord.SleekflowCompanyId)
            });
    }

    // Refer to https://learn.microsoft.com/en-us/azure/search/vector-search-ranking for more details
    private static double ScoreToSimilarity(double score)
    {
        var cosineDistance = (1 - score) / score;
        return -cosineDistance + 1;
    }
}