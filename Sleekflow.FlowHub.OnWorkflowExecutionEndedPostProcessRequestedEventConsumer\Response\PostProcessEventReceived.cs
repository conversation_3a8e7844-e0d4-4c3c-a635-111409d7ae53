using Newtonsoft.Json;

namespace Sleekflow.FlowHub.OnWorkflowExecutionEndedPostProcessRequestedEventConsumer.Response;

public class PostProcessEventReceived
{
    [JsonProperty("workflow_execution_end_id")]
    public string WorkflowExecutionEndId { get; set; }

    [JsonConstructor]
    public PostProcessEventReceived(string workflowExecutionEndId)
    {
        WorkflowExecutionEndId = workflowExecutionEndId;
    }
}