using MassTransit;
using Sleekflow.Exceptions;
using Sleekflow.Locks;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumer>
{
    public const int LockDuration = 5;
    public const int MaxAutoRenewDuration = 15;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumer>
            consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumer
    : IConsumer<OnCloudApiHalfHourConversationUsageTransactionResynchronizationEvent>
{
    private readonly IBus _bus;
    private readonly ILockService _lockService;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly ILogger<OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumer> _logger;

    public OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumer(
        IBus bus,
        ILockService lockService,
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        ILogger<OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumer> logger)
    {
        _bus = bus;
        _logger = logger;
        _lockService = lockService;
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public async Task Consume(
        ConsumeContext<OnCloudApiHalfHourConversationUsageTransactionResynchronizationEvent> context)
    {
        var onCloudApiRevalidateHalfHourConversationUsageTransactionEvent = context.Message;
        var cancellationToken = context.CancellationToken;

        // Resynchronization information and date ranges
        var facebookWabaId = onCloudApiRevalidateHalfHourConversationUsageTransactionEvent.FacebookWabaId;
        var facebookBusinessId = onCloudApiRevalidateHalfHourConversationUsageTransactionEvent.FacebookBusinessId;
        var lastConversationUsageInsertTimestamp =
            onCloudApiRevalidateHalfHourConversationUsageTransactionEvent.LastConversationUsageInsertTimestamp;
        var latestConversationUsageInsertTimestamp =
            onCloudApiRevalidateHalfHourConversationUsageTransactionEvent.LatestConversationUsageInsertTimestamp;

        var retryCount = context.GetRedeliveryCount();
        if (context.GetRedeliveryCount() > 10)
        {
            throw new SfInternalErrorException($"Retry count over the max limited {retryCount}");
        }

        var @lock = await _lockService.LockAsync(
            new[]
            {
                facebookWabaId,
                facebookBusinessId,
                "BusinessBalanceResynchronization"
            },
            TimeSpan.FromSeconds(
                60 *
                (OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumerDefinition.LockDuration +
                 OnCloudApiHalfHourConversationUsageTransactionResynchronizationEventConsumerDefinition
                     .MaxAutoRenewDuration)),
            cancellationToken);
        if (@lock is null)
        {
            await context.Redeliver(TimeSpan.FromSeconds(8));
            return;
        }

        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);
        if (waba is null)
        {
            _logger.LogWarning("Unable to Locate any Waba");
            await _lockService.ReleaseAsync(@lock, cancellationToken);
            return;
        }

        // Calculate The Max Conversation Count Base On The If Any Connection On Waba Phone Numbers
        var conversationHalfHourCount = waba.WabaPhoneNumbers.Any() ? 1000 : 50;
        var conversations =
            CloudApiUtils.ExtractDateTimes(
                lastConversationUsageInsertTimestamp,
                latestConversationUsageInsertTimestamp);

        // Break down the number of conversion would be done in an hour
        var conversationPartitions = conversations.Select(
                (x, i) => new
                {
                    Index = i, Value = x
                })
            .GroupBy(x => x.Index / conversationHalfHourCount)
            .Select(x => x.Select(v => v.Value).ToList())
            .ToList();

        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        var hasConversationAnalyticsRecordModified = false;
        foreach (var (
                     instanceLastConversationUsageInsertTimestamp,
                     instanceLatestConversationUsageInsertTimestamp) in conversationPartitions.First())
        {
            try
            {
                // Get the business Transaction Record in the given date range
                var businessTransactionLogs =
                    await _businessBalanceTransactionLogService.GetResynchronizationBusinessTransactionLogAsync(
                        facebookWabaId,
                        facebookBusinessId,
                        instanceLastConversationUsageInsertTimestamp,
                        instanceLatestConversationUsageInsertTimestamp);

                // Revalidate and upsert business transaction log
                var conversationAnalyticsResynchronizationStatus =
                    await _businessBalanceTransactionLogService
                        .ConversationAnalyticsResynchronizationAsync(
                            waba,
                            businessBalance.MarkupProfile,
                            businessTransactionLogs,
                            instanceLastConversationUsageInsertTimestamp,
                            instanceLatestConversationUsageInsertTimestamp);

                if (conversationAnalyticsResynchronizationStatus)
                {
                    hasConversationAnalyticsRecordModified = conversationAnalyticsResynchronizationStatus;
                }
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occur during revalidate transaction log");
            }
        }

        if (hasConversationAnalyticsRecordModified)
        {
            await _bus.Publish(
                new OnCloudApiBusinessBalanceResynchronizationEvent(facebookBusinessId),
                cancellationToken);
        }

        if (conversationPartitions.Count > 1)
        {
            await context.Redeliver(TimeSpan.FromHours(1));
        }

        _logger.LogInformation(
            "Complete half hour conversation usage resynchronization {HasConversationAnalyticsRecordModified}",
            hasConversationAnalyticsRecordModified);

        await _lockService.ReleaseAsync(@lock, cancellationToken);
    }
}