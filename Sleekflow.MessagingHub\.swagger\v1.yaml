openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7080
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/messaging-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/messaging-hub
    description: Prod Apigw
paths:
  /AuditLogs/CreateWabaPhoneNumberStatusChangedAuditLog:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWabaPhoneNumberStatusChangedAuditLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateWabaPhoneNumberStatusChangedAuditLogOutputOutput'
  /AuditLogs/GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs:
    post:
      tags:
        - AuditLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutputOutput'
  /authorized/Balances/GetConversationAnalytics:
    post:
      tags:
        - AuthorizedBalances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetConversationAnalyticsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConversationAnalyticsOutputOutput'
  /Balances/AllocateBusinessWabaLevelCredit:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AllocateBusinessWabaLevelCreditInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AllocateBusinessWabaLevelCreditOutputOutput'
  /Balances/EnqueueOnCloudApiBusinessBalanceResynchronization:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnqueueOnCloudApiBusinessBalanceResynchronizationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnqueueOnCloudApiBusinessBalanceResynchronizationOutputOutput'
  /Balances/EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooks:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutputOutput'
  /Balances/GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLink:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutputOutput'
  /Balances/GetBusinessBalanceCreditTransferTransactionLogs:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetBusinessBalanceCreditTransferTransactionLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetBusinessBalanceCreditTransferTransactionLogsOutputOutput'
  /Balances/GetWabaBalanceAutoTopUpProfile:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWabaBalanceAutoTopUpProfileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWabaBalanceAutoTopUpProfileOutputOutput'
  /Balances/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfile:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutputOutput'
  /Balances/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettings:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutputOutput'
  /Balances/GetWhatsappCloudApiBusinessBalances:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalancesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalancesOutputOutput'
  /Balances/GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoices:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutputOutput'
  /Balances/GetWhatsappCloudApiBusinessBalanceStripeTopUpPlans:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutputOutput'
  /Balances/OneTimePatchExistingWabaBalances:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OneTimePatchExistingWabaBalancesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OneTimePatchExistingWabaBalancesOutputOutput'
  /Balances/SwitchFromBusinessLevelToWabaLevelCreditManagement:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwitchFromBusinessLevelToWabaLevelCreditManagementInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwitchFromBusinessLevelToWabaLevelCreditManagementOutputOutput'
  /Balances/SwitchFromWabaLevelToBusinessLevelCreditManagement:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SwitchFromWabaLevelToBusinessLevelCreditManagementInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SwitchFromWabaLevelToBusinessLevelCreditManagementOutputOutput'
  /Balances/TopUpWhatsappCloudApiBusinessBalance:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TopUpWhatsappCloudApiBusinessBalanceInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopUpWhatsappCloudApiBusinessBalanceOutputOutput'
  /Balances/TopUpWhatsappCloudApiWabaBalance:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TopUpWhatsappCloudApiWabaBalanceInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TopUpWhatsappCloudApiWabaBalanceOutputOutput'
  /Balances/UpsertWabaBalanceAutoTopUpProfile:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertWabaBalanceAutoTopUpProfileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertWabaBalanceAutoTopUpProfileOutputOutput'
  /Balances/UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfile:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutputOutput'
  /Balances/UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooks:
    post:
      tags:
        - Balances
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutputOutput'
  /Channels/ConnectWhatsappCloudApiChannel:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectWhatsappCloudApiChannelInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectWhatsappCloudApiChannelOutputOutput'
  /Channels/DisconnectWhatsappCloudApiChannel:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisconnectWhatsappCloudApiChannelInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DisconnectWhatsappCloudApiChannelOutputOutput'
  /Channels/GetConnectedWhatsappCloudApiChannels:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetConnectedWhatsappCloudApiChannelsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectedWhatsappCloudApiChannelsOutputOutput'
  /Channels/GetPhoneNumberWhatsappBusinessProfile:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPhoneNumberWhatsappBusinessProfileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPhoneNumberWhatsappBusinessProfileOutputOutput'
  /Channels/GetWhatsappCloudApiMedia:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiMediaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiMediaOutputOutput'
  /Channels/ReconnectWhatsappCloudApiChannel:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReconnectWhatsappCloudApiChannelInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReconnectWhatsappCloudApiChannelOutputOutput'
  /Channels/RegisterWhatsAppPhoneNumber:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterWhatsAppPhoneNumberInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterWhatsAppPhoneNumberOutputOutput'
  /Channels/SendWhatsappCloudApiMessage:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendWhatsappCloudApiMessageInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendWhatsappCloudApiMessageOutputOutput'
  /Channels/UpdatePhoneNumberWhatsappBusinessProfile:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePhoneNumberWhatsappBusinessProfileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatePhoneNumberWhatsappBusinessProfileOutputOutput'
  /Channels/UpsertWhatsappCloudApiChannelWebhook:
    post:
      tags:
        - Channels
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertWhatsappCloudApiChannelWebhookInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertWhatsappCloudApiChannelWebhookOutputOutput'
  /ConversationalAutomations/GetPhoneNumberConversationalAutomation:
    post:
      tags:
        - ConversationalAutomations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPhoneNumberConversationalAutomationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPhoneNumberConversationalAutomationOutputOutput'
  /ConversationalAutomations/GetWabaPhoneNumbersConversationalAutomations:
    post:
      tags:
        - ConversationalAutomations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWabaPhoneNumbersConversationalAutomationsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWabaPhoneNumbersConversationalAutomationsOutputOutput'
  /ConversationalAutomations/UpdateConversationalAutomation:
    post:
      tags:
        - ConversationalAutomations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateConversationalAutomationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateConversationalAutomationOutputOutput'
  /Managements/DeregisterPhoneNumber:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeregisterPhoneNumberInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeregisterPhoneNumberOutputOutput'
  /Managements/DisassociateFacebookBusinessAccountFromCompany:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisassociateFacebookBusinessAccountFromCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DisassociateFacebookBusinessAccountFromCompanyOutputOutput'
  /Managements/GetAllManagementWhatsappCloudApiBusinessBalances:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiBusinessBalancesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiBusinessBalancesOutputOutput'
  /Managements/GetAllManagementWhatsappCloudApiConversationUsageAnalytics:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutputOutput'
  /Managements/GetAllManagementWhatsappCloudApiWabas:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiWabasInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiWabasOutputOutput'
  /Managements/GetAuditLogs:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAuditLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAuditLogsOutputOutput'
  /Managements/GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogs:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutputOutput'
  /Managements/GetManagementWhatsappCloudApiBusinessBalances:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetManagementWhatsappCloudApiBusinessBalancesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetManagementWhatsappCloudApiBusinessBalancesOutputOutput'
  /Managements/GetManagementWhatsappCloudApiConversationUsageAnalytic:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetManagementWhatsappCloudApiConversationUsageAnalyticInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetManagementWhatsappCloudApiConversationUsageAnalyticOutputOutput'
  /Managements/RegisterPhoneNumber:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterPhoneNumberInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterPhoneNumberOutputOutput'
  /Managements/UpdatePhoneNumberSettings:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePhoneNumberSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdatePhoneNumberSettingsOutputOutput'
  /Managements/UpdateWhatsappCloudApiBusinessBalanceMarkupProfile:
    post:
      tags:
        - Managements
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutputOutput'
  /Medias/DeleteWhatsappCloudApiMedia:
    post:
      tags:
        - Medias
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteWhatsappCloudApiMediaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteWhatsappCloudApiMediaOutputOutput'
  /Medias/GetUploadMediaLink:
    post:
      tags:
        - Medias
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUploadMediaLinkInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUploadMediaLinkOutputOutput'
  /Medias/GetWhatsappCloudApiMediaUrl:
    post:
      tags:
        - Medias
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiMediaUrlInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiMediaUrlOutputOutput'
  /Medias/UploadWhatsappCloudApiMedia:
    post:
      tags:
        - Medias
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UploadWhatsappCloudApiMediaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadWhatsappCloudApiMediaOutputOutput'
  /Medias/UploadWhatsappCloudApiMediaUsingUrl:
    post:
      tags:
        - Medias
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UploadWhatsappCloudApiMediaUsingUrlInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadWhatsappCloudApiMediaUsingUrlOutputOutput'
  /MetaConversionApis/CreateWabaDataset:
    post:
      tags:
        - MetaConversionApis
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWabaDatasetInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateWabaDatasetOutputOutput'
  /MetaConversionApiSignalEvent/SendConversionApiSignalEvent:
    post:
      tags:
        - MetaConversionApiSignalEvent
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Migrations/GetWhatsappCloudApiUserBusinesses:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessesOutputOutput'
  /Migrations/GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaId:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutputOutput'
  /Migrations/GetWhatsappCloudApiUserBusinessWabas:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessWabasInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessWabasOutputOutput'
  /Migrations/InitiateWhatsappCloudApiPhoneNumberWabaMigration:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitiateWhatsappCloudApiPhoneNumberWabaMigrationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutputOutput'
  /Migrations/MigrateWhatsappCloudApiPhoneNumberToCloudApi:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigrateWhatsappCloudApiPhoneNumberToCloudApiInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrateWhatsappCloudApiPhoneNumberToCloudApiOutputOutput'
  /Migrations/RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCode:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutputOutput'
  /Migrations/VerifyWhatsappCloudApiPhoneNumberOwnership:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyWhatsappCloudApiPhoneNumberOwnershipInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifyWhatsappCloudApiPhoneNumberOwnershipOutputOutput'
  /ProductCatalogs/ConnectWabasProductCatalogs:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectWabasProductCatalogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectWabasProductCatalogsOutputOutput'
  /ProductCatalogs/GetFacebookBusinessProductCatalogs:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFacebookBusinessProductCatalogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFacebookBusinessProductCatalogsOutputOutput'
  /ProductCatalogs/GetProductCatalogProductItem:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProductCatalogProductItemInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductCatalogProductItemOutputOutput'
  /ProductCatalogs/GetProductCatalogProductItems:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetProductCatalogProductItemsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductCatalogProductItemsOutputOutput'
  /ProductCatalogs/GetWabaProductCatalog:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWabaProductCatalogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWabaProductCatalogOutputOutput'
  /ProductCatalogs/GetWabasConnectedProductCatalogs:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWabasConnectedProductCatalogsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWabasConnectedProductCatalogsOutputOutput'
  /ProductCatalogs/GetWabasConnectedProductCatalogsWithFacebookAuthorizationCode:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutputOutput'
  /ProductCatalogs/RefreshWabaPhoneNumberProductCatalogSetting:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshWabaPhoneNumberProductCatalogSettingInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RefreshWabaPhoneNumberProductCatalogSettingOutputOutput'
  /ProductCatalogs/UpdateWabaConnectedProductCatalog:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWabaConnectedProductCatalogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateWabaConnectedProductCatalogOutputOutput'
  /ProductCatalogs/UpdateWabaPhoneNumberCatalogVisibilityWithCartButton:
    post:
      tags:
        - ProductCatalogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutputOutput'
  /Public/healthz:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /StripeWebhook/payment:
    post:
      tags:
        - StripeWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Templates/CreateWhatsappCloudApiTemplate:
    post:
      tags:
        - Templates
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateWhatsappCloudApiTemplateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateWhatsappCloudApiTemplateOutputOutput'
  /Templates/DeleteWhatsappCloudApiTemplate:
    post:
      tags:
        - Templates
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteWhatsappCloudApiTemplateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteWhatsappCloudApiTemplateOutputOutput'
  /Templates/EditWhatsappCloudApiTemplate:
    post:
      tags:
        - Templates
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EditWhatsappCloudApiTemplateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EditWhatsappCloudApiTemplateOutputOutput'
  /Templates/GetWhatsappCloudApiTemplates:
    post:
      tags:
        - Templates
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiTemplatesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiTemplatesOutputOutput'
  /Templates/UploadTemplateHeaderFile:
    post:
      tags:
        - Templates
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UploadTemplateHeaderFileInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadTemplateHeaderFileOutputOutput'
  /TransactionLogs/BulkPatchBusinessBalanceTransactionLog:
    post:
      tags:
        - TransactionLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BulkPatchBusinessBalanceTransactionLogInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BulkPatchBusinessBalanceTransactionLogOutputOutput'
  /TransactionLogs/EnqueueBusinessBalancePendingTransactionLogCreated:
    post:
      tags:
        - TransactionLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnqueueBusinessBalancePendingTransactionLogCreatedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnqueueBusinessBalancePendingTransactionLogCreatedOutputOutput'
  /TransactionLogs/EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransaction:
    post:
      tags:
        - TransactionLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutputOutput'
  /TransactionLogs/EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronization:
    post:
      tags:
        - TransactionLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutputOutput'
  /TransactionLogs/GetWhatsappCloudApiConversationUsageAnalytic:
    post:
      tags:
        - TransactionLogs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiConversationUsageAnalyticInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiConversationUsageAnalyticOutputOutput'
  /Wabas/ConnectWhatsappCloudApiWaba:
    post:
      tags:
        - Wabas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectWhatsappCloudApiWabaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectWhatsappCloudApiWabaOutputOutput'
  /Wabas/ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessToken:
    post:
      tags:
        - Wabas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutputOutput'
  /Wabas/EnqueueWabaBusinessConnected:
    post:
      tags:
        - Wabas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnqueueWabaBusinessConnectedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnqueueWabaBusinessConnectedOutputOutput'
  /Wabas/GetConnectedWhatsappCloudApiWabas:
    post:
      tags:
        - Wabas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetConnectedWhatsappCloudApiWabasInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectedWhatsappCloudApiWabasOutputOutput'
  /Wabas/GetWhatsappCloudApiWaba:
    post:
      tags:
        - Wabas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappCloudApiWabaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappCloudApiWabaOutputOutput'
  /Wabas/OnboardPartnersToMMLite:
    post:
      tags:
        - Wabas
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OnboardPartnersToMMLiteInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OnboardPartnersToMMLiteOutputOutput'
  /WhatsappCloudApiWebhook:
    get:
      tags:
        - WhatsappCloudApiWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: hub.mode
          in: query
          schema:
            type: string
        - name: hub.challenge
          in: query
          schema:
            type: string
        - name: hub.verify_token
          in: query
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
    post:
      tags:
        - WhatsappCloudApiWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /WhatsappFlows/GetWhatsappFlow:
    post:
      tags:
        - WhatsappFlows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappFlowInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappFlowOutputOutput'
  /WhatsappFlows/GetWhatsappFlowsByWabaId:
    post:
      tags:
        - WhatsappFlows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWhatsappFlowsByWabaIdInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWhatsappFlowsByWabaIdOutputOutput'
  /WhatsappFlows/UpdateWhatsappFlowMetadata:
    post:
      tags:
        - WhatsappFlows
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateWhatsappFlowMetadataInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateWhatsappFlowMetadataOutputOutput'
components:
  schemas:
    AllocateBusinessWabaLevelCreditInput:
      required:
        - credit_allocation
        - eTag
        - facebook_business_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        eTag:
          minLength: 1
          type: string
        credit_allocation:
          $ref: '#/components/schemas/CreditAllocationObject'
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    AllocateBusinessWabaLevelCreditOutput:
      type: object
      properties:
        business_balance:
          $ref: '#/components/schemas/BusinessBalanceDto'
      additionalProperties: false
    AllocateBusinessWabaLevelCreditOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AllocateBusinessWabaLevelCreditOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuditLog:
      type: object
      properties:
        auditing_partition_id:
          type: string
          nullable: true
        facebook_waba_id:
          type: string
          nullable: true
        facebook_waba_business_id:
          type: string
          nullable: true
        facebook_phone_number_id:
          type: string
          nullable: true
        facebook_message_template_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        cloud_api_audit:
          $ref: '#/components/schemas/CloudApiRequestAudit'
        waba_audit:
          $ref: '#/components/schemas/WabaAudit'
        business_balance_snapshots:
          $ref: '#/components/schemas/BusinessBalanceAudit'
        business_balance_transaction_log_snapshots:
          $ref: '#/components/schemas/BusinessBalanceTransactionLogAudit'
        cloud_api_webhook_status_update_snapshots:
          $ref: '#/components/schemas/CloudApiWebhookStatusUpdateAudit'
        auditing_operation:
          type: string
          nullable: true
        auditing_group:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        sleekflow_company_name:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    AuditLogCloudApiWebhookValueObject:
      type: object
      properties:
        messaging_product:
          type: string
          nullable: true
        metadata:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMetadataObject'
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookContactObject'
          nullable: true
        messages:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookMessageObject'
          nullable: true
        statuses:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookMessageStatus'
          nullable: true
        detected_outcomes:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookMessageDetectedOutcome'
          nullable: true
        entity_type:
          type: string
          nullable: true
        entity_id:
          type: string
          nullable: true
        alert_severity:
          type: string
          nullable: true
        alert_status:
          type: string
          nullable: true
        alert_type:
          type: string
          nullable: true
        alert_description:
          type: string
          nullable: true
        decision:
          type: string
          nullable: true
        event:
          type: string
          nullable: true
        business_verification_status:
          type: string
          nullable: true
        waba_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookWabaInfoObject'
        phone_number:
          type: string
          nullable: true
        ban_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookBanInfoObject'
        violation_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookViolationInfoObject'
        lock_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookLockInfoObject'
        restriction_info:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookWabaRestrictionFieldDataObject'
          nullable: true
        partner_client_certification_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookPartnerClientCertificationInfoObject'
        max_daily_conversation_per_phone:
          type: integer
          format: int32
          nullable: true
        max_phone_numbers_per_waba:
          type: integer
          format: int32
          nullable: true
        max_phone_numbers_per_business:
          type: integer
          format: int32
          nullable: true
        business_id:
          type: integer
          format: int32
          nullable: true
        campaign_id:
          type: string
          nullable: true
        campaign_name:
          type: string
          nullable: true
        old_status:
          type: string
          nullable: true
        new_status:
          type: string
          nullable: true
        paused_reasons:
          type: array
          items:
            type: string
          nullable: true
        complete_reason:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        flow_id:
          type: string
          nullable: true
        p90_latency:
          type: integer
          format: int32
          nullable: true
        p50_latency:
          type: integer
          format: int32
          nullable: true
        request_count:
          type: integer
          format: int32
          nullable: true
        error_rate:
          type: number
          format: double
          nullable: true
        threshold:
          type: integer
          format: int32
          nullable: true
        availability:
          type: integer
          format: int32
          nullable: true
        alert_state:
          type: string
          nullable: true
        errors:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookErrorObject'
          nullable: true
        message_template_id:
          type: string
          nullable: true
        message_template_name:
          type: string
          nullable: true
        message_template_language:
          type: string
          nullable: true
        previous_category:
          type: string
          nullable: true
        new_category:
          type: string
          nullable: true
        previous_quality_score:
          type: string
          nullable: true
        new_quality_score:
          type: string
          nullable: true
        reason:
          type: string
          nullable: true
        disable_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookDisableInfoObject'
        other_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookOtherInfoObject'
        display_phone_number:
          type: string
          nullable: true
        requested_verified_name:
          type: string
          nullable: true
        rejection_reason:
          type: string
          nullable: true
        current_limit:
          type: string
          nullable: true
        old_limit:
          type: string
          nullable: true
        requester:
          type: string
          nullable: true
      additionalProperties: false
    BulkPatchBusinessBalanceTransactionLogInput:
      required:
        - facebook_business_id
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
        default_transaction_handling_fee_rate:
          type: number
          format: decimal
          nullable: true
      additionalProperties: false
    BulkPatchBusinessBalanceTransactionLogOutput:
      type: object
      properties:
        total_count:
          type: integer
          format: int32
        un_patched_count:
          type: integer
          format: int32
        un_patched_ids:
          type: array
          items:
            type: string
          nullable: true
        patched_count:
          type: integer
          format: int32
        patched_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    BulkPatchBusinessBalanceTransactionLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/BulkPatchBusinessBalanceTransactionLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    BusinessBalance:
      type: object
      properties:
        facebook_business_id:
          type: string
          nullable: true
        credit:
          $ref: '#/components/schemas/Money'
        used:
          $ref: '#/components/schemas/Money'
        markup:
          $ref: '#/components/schemas/Money'
        transaction_handling_fee:
          $ref: '#/components/schemas/Money'
        markup_profile:
          $ref: '#/components/schemas/MarkupProfile'
        waba_balances:
          type: array
          items:
            $ref: '#/components/schemas/WabaBalance'
          nullable: true
        is_by_waba_billing_enabled:
          type: boolean
          nullable: true
        conversation_usage_insert_state:
          $ref: '#/components/schemas/ConversationUsageInsertState'
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        _etag:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    BusinessBalanceAudit:
      type: object
      properties:
        snapshot:
          $ref: '#/components/schemas/BusinessBalance'
        changes:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    BusinessBalanceAutoTopUpProfileDto:
      type: object
      properties:
        facebook_business_id:
          type: string
          nullable: true
        minimum_balance:
          $ref: '#/components/schemas/Money'
        auto_top_up_plan:
          $ref: '#/components/schemas/StripeWhatsAppCreditTopUpPlan'
        is_auto_top_up_enabled:
          type: boolean
      additionalProperties: false
    BusinessBalanceDto:
      type: object
      properties:
        facebook_business_id:
          type: string
          nullable: true
        facebook_business_name:
          type: string
          nullable: true
        facebook_business_wabas:
          type: array
          items:
            $ref: '#/components/schemas/FacebookBusinessWaba'
          nullable: true
        total_credit:
          $ref: '#/components/schemas/Money'
        all_time_usage:
          $ref: '#/components/schemas/Money'
        balance:
          $ref: '#/components/schemas/Money'
        waba_balances:
          type: array
          items:
            $ref: '#/components/schemas/WabaBalanceDto'
          nullable: true
        unallocated_credit:
          $ref: '#/components/schemas/Money'
        is_by_waba_billing_enabled:
          type: boolean
        un_calculated_credit_transfer_transaction_logs:
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferTransactionLogDto'
          nullable: true
        _etag:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    BusinessBalanceInvoice:
      type: object
      properties:
        facebook_business_id:
          type: string
          nullable: true
        facebook_business_name:
          type: string
          nullable: true
        invoice_pdf:
          type: string
        status:
          type: string
        created_at:
          type: integer
          format: int64
        pay_amount:
          $ref: '#/components/schemas/Money'
        description:
          type: string
      additionalProperties: false
    BusinessBalanceTransactionLog:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        facebook_business_id:
          type: string
          nullable: true
        unique_id:
          type: string
          nullable: true
        credit:
          $ref: '#/components/schemas/Money'
        used:
          $ref: '#/components/schemas/Money'
        markup:
          $ref: '#/components/schemas/Money'
        transaction_handling_fee:
          $ref: '#/components/schemas/Money'
        transaction_type:
          type: string
          nullable: true
        is_calculated:
          type: boolean
        calculation_status:
          type: string
          nullable: true
        waba_top_up:
          $ref: '#/components/schemas/WabaTopUpTransactionItem'
        waba_conversation_usage:
          $ref: '#/components/schemas/WabaConversationUsageTransactionItem'
        markup_profile_snapshot:
          $ref: '#/components/schemas/MarkupProfile'
        credit_transfer_from_to:
          $ref: '#/components/schemas/CreditTransferFromTo'
        resynchronize_at_histories:
          type: array
          items:
            type: string
            format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        _etag:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    BusinessBalanceTransactionLogAudit:
      type: object
      properties:
        snapshot:
          $ref: '#/components/schemas/BusinessBalanceTransactionLog'
        changes:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    BusinessBalanceTransactionLogFilter:
      required:
        - transaction_type
      type: object
      properties:
        transaction_type:
          minLength: 1
          type: string
        facebook_business_id:
          type: string
          nullable: true
        facebook_waba_id:
          type: string
          nullable: true
        top_up_payment_method:
          type: string
          nullable: true
        conversation_usage_time_range:
          $ref: '#/components/schemas/TimestampRange'
        have_top_up_amount:
          type: boolean
          nullable: true
        have_used_amount:
          type: boolean
          nullable: true
        have_markup_amount:
          type: boolean
          nullable: true
        is_calculated:
          type: boolean
          nullable: true
        created_at_range:
          $ref: '#/components/schemas/DateTimeOffsetRange'
        updated_at_range:
          $ref: '#/components/schemas/DateTimeOffsetRange'
        order_by:
          type: string
          nullable: true
        order:
          pattern: ^(ACS|DESC)$
          type: string
          nullable: true
      additionalProperties: false
    CloudApiErrorObject:
      type: object
      properties:
        code:
          type: integer
          format: int32
        title:
          type: string
          nullable: true
        details:
          type: string
          nullable: true
        href:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        error_data:
          $ref: '#/components/schemas/ErrorDataObject'
      additionalProperties: false
    CloudApiRequestAudit:
      type: object
      properties:
        parameters:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        response:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        exception:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_at:
          type: string
          format: date-time
          nullable: true
        response_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    CloudApiWebhookStatusUpdateAudit:
      type: object
      properties:
        snapshot:
          $ref: '#/components/schemas/AuditLogCloudApiWebhookValueObject'
        type:
          type: string
          nullable: true
      additionalProperties: false
    CloudApiWebhookValueObject:
      type: object
      properties:
        messaging_product:
          type: string
          nullable: true
        metadata:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMetadataObject'
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookContactObject'
          nullable: true
        messages:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookMessageObject'
          nullable: true
        statuses:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookMessageStatus'
          nullable: true
        detected_outcomes:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookMessageDetectedOutcome'
          nullable: true
        entity_type:
          type: string
          nullable: true
        entity_id:
          type: string
          nullable: true
        alert_severity:
          type: string
          nullable: true
        alert_status:
          type: string
          nullable: true
        alert_type:
          type: string
          nullable: true
        alert_description:
          type: string
          nullable: true
        decision:
          type: string
          nullable: true
        event:
          type: string
          nullable: true
        business_verification_status:
          type: string
          nullable: true
        waba_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookWabaInfoObject'
        phone_number:
          type: string
          nullable: true
        ban_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookBanInfoObject'
        violation_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookViolationInfoObject'
        lock_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookLockInfoObject'
        restriction_info:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookWabaRestrictionFieldDataObject'
          nullable: true
        partner_client_certification_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookPartnerClientCertificationInfoObject'
        max_daily_conversation_per_phone:
          type: integer
          format: int32
          nullable: true
        max_phone_numbers_per_waba:
          type: integer
          format: int32
          nullable: true
        max_phone_numbers_per_business:
          type: integer
          format: int32
          nullable: true
        business_id:
          type: integer
          format: int32
          nullable: true
        campaign_id:
          type: string
          nullable: true
        campaign_name:
          type: string
          nullable: true
        old_status:
          type: string
          nullable: true
        new_status:
          type: string
          nullable: true
        paused_reasons:
          type: array
          items:
            type: string
          nullable: true
        complete_reason:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        flow_id:
          type: string
          nullable: true
        p90_latency:
          type: integer
          format: int32
          nullable: true
        p50_latency:
          type: integer
          format: int32
          nullable: true
        request_count:
          type: integer
          format: int32
          nullable: true
        error_rate:
          type: number
          format: double
          nullable: true
        threshold:
          type: integer
          format: int32
          nullable: true
        availability:
          type: integer
          format: int32
          nullable: true
        alert_state:
          type: string
          nullable: true
        errors:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookErrorObject'
          nullable: true
        message_template_id:
          type: string
          nullable: true
        message_template_name:
          type: string
          nullable: true
        message_template_language:
          type: string
          nullable: true
        previous_category:
          type: string
          nullable: true
        new_category:
          type: string
          nullable: true
        previous_quality_score:
          type: string
          nullable: true
        new_quality_score:
          type: string
          nullable: true
        reason:
          type: string
          nullable: true
        disable_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookDisableInfoObject'
        other_info:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookOtherInfoObject'
        display_phone_number:
          type: string
          nullable: true
        requested_verified_name:
          type: string
          nullable: true
        rejection_reason:
          type: string
          nullable: true
        current_limit:
          type: string
          nullable: true
        old_limit:
          type: string
          nullable: true
        requester:
          type: string
          nullable: true
      additionalProperties: false
    Command:
      type: object
      properties:
        command_name:
          type: string
          nullable: true
        command_description:
          type: string
          nullable: true
      additionalProperties: false
    ConnectWabasProductCatalogsInput:
      required:
        - sleekflow_company_id
        - waba_id_catalog_id_dictionary
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id_catalog_id_dictionary:
          type: object
          additionalProperties:
            type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ConnectWabasProductCatalogsOutput:
      type: object
      properties:
        connected_wabas:
          type: array
          items:
            $ref: '#/components/schemas/WabaDto'
          nullable: true
      additionalProperties: false
    ConnectWabasProductCatalogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ConnectWabasProductCatalogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiChannelInput:
      required:
        - sleekflow_company_id
        - waba_id
        - waba_phone_number_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        webhook_url:
          type: string
          nullable: true
        pin:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiChannelOutput:
      type: object
      properties:
        connected_cloud_api_channel:
          $ref: '#/components/schemas/ConnectedCloudApiChannelDto'
      additionalProperties: false
    ConnectWhatsappCloudApiChannelOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ConnectWhatsappCloudApiChannelOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiWabaInput:
      required:
        - sleekflow_company_id
        - user_access_token
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        user_access_token:
          minLength: 1
          type: string
        forbidden_waba_ids:
          type: array
          items:
            type: string
          nullable: true
        webhook_url:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiWabaOutput:
      type: object
      properties:
        connected_wabas:
          type: array
          items:
            $ref: '#/components/schemas/WabaDto'
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiWabaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ConnectWhatsappCloudApiWabaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput:
      required:
        - business_integration_system_user_access_token
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        business_integration_system_user_access_token:
          minLength: 1
          type: string
        forbidden_waba_ids:
          type: array
          items:
            type: string
          nullable: true
        webhook_url:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutput:
      type: object
      properties:
        connected_wabas:
          type: array
          items:
            $ref: '#/components/schemas/WabaDto'
          nullable: true
      additionalProperties: false
    ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ConnectedCloudApiChannelDto:
      type: object
      properties:
        connected_api_channels:
          type: array
          items:
            $ref: '#/components/schemas/WabaPhoneNumberDto'
          nullable: true
      additionalProperties: false
    ConversationAnalyticsResultData:
      type: object
      properties:
        data_points:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappConversationAnalyticsResultDataPoint'
          nullable: true
      additionalProperties: false
    ConversationUsageInsertState:
      type: object
      properties:
        last_conversation_usage_insert_timestamp:
          type: integer
          format: int64
        waba_conversation_insertion_exception:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/WabaConversationInsertionException'
          nullable: true
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    ConversationalAutomation:
      type: object
      properties:
        enable_welcome_message:
          type: boolean
          nullable: true
        commands:
          type: array
          items:
            $ref: '#/components/schemas/Command'
          nullable: true
        prompts:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ConversationalAutomationListResponse:
      type: object
      properties:
        id:
          type: string
          nullable: true
        verified_name:
          type: string
          nullable: true
        display_phone_number:
          type: string
          nullable: true
        conversational_automation:
          $ref: '#/components/schemas/ConversationalAutomation'
      additionalProperties: false
    CreateWabaDatasetInput:
      required:
        - facebook_dataset_name
        - facebook_waba_id
        - sleekflow_company_id
      type: object
      properties:
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_dataset_name:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateWabaDatasetOutput:
      type: object
      properties:
        waba:
          $ref: '#/components/schemas/WabaDto'
      additionalProperties: false
    CreateWabaDatasetOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateWabaDatasetOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWabaPhoneNumberStatusChangedAuditLogInput:
      required:
        - company_name
        - facebook_waba_id
        - phone_number
        - sleekflow_company_id
        - status
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        company_name:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        phone_number:
          minLength: 1
          type: string
        status:
          minLength: 1
          type: string
      additionalProperties: false
    CreateWabaPhoneNumberStatusChangedAuditLogOutput:
      type: object
      properties:
        audit_log:
          $ref: '#/components/schemas/AuditLog'
      additionalProperties: false
    CreateWabaPhoneNumberStatusChangedAuditLogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateWabaPhoneNumberStatusChangedAuditLogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWhatsappCloudApiTemplateInput:
      required:
        - sleekflow_company_id
        - waba_id
        - whatsapp_cloud_api_create_template_object
      type: object
      properties:
        waba_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        whatsapp_cloud_api_create_template_object:
          $ref: '#/components/schemas/WhatsappCloudApiCreateTemplateObject'
      additionalProperties: false
    CreateWhatsappCloudApiTemplateOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    CreateWhatsappCloudApiTemplateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateWhatsappCloudApiTemplateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreditAllocationObject:
      type: object
      properties:
        credit_transfers:
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferFromTo'
          nullable: true
      additionalProperties: false
    CreditTransferFromTo:
      type: object
      properties:
        credit_transfer_from:
          $ref: '#/components/schemas/CreditTransferTargetObject'
        credit_transfer_to:
          $ref: '#/components/schemas/CreditTransferTargetObject'
        credit_transfer_amount:
          $ref: '#/components/schemas/Money'
        credit_transfer_type:
          type: string
          nullable: true
      additionalProperties: false
    CreditTransferTargetObject:
      type: object
      properties:
        facebook_business_id:
          type: string
          nullable: true
        facebook_waba_id:
          type: string
          nullable: true
        target_type:
          type: string
          nullable: true
      additionalProperties: false
    CreditTransferTransactionLogDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        unique_id:
          type: string
          nullable: true
        facebook_waba_id:
          type: string
          nullable: true
        facebook_business_id:
          type: string
          nullable: true
        transaction_type:
          type: string
          nullable: true
        is_calculated:
          type: boolean
        calculation_status:
          type: string
          nullable: true
        credit_transfer_from_to:
          $ref: '#/components/schemas/CreditTransferFromTo'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        _etag:
          type: string
          nullable: true
      additionalProperties: false
    CursorBasedPaginationParam:
      type: object
      properties:
        limit:
          type: integer
          format: int32
          nullable: true
        before:
          type: string
          nullable: true
        after:
          type: string
          nullable: true
      additionalProperties: false
    CursorBasedPaginationResult:
      type: object
      properties:
        cursors:
          $ref: '#/components/schemas/Cursors'
        previous:
          type: string
          nullable: true
        next:
          type: string
          nullable: true
      additionalProperties: false
    Cursors:
      type: object
      properties:
        before:
          type: string
          nullable: true
        after:
          type: string
          nullable: true
      additionalProperties: false
    DateTimeOffsetRange:
      type: object
      properties:
        start:
          type: string
          format: date-time
        end:
          type: string
          format: date-time
      additionalProperties: false
    DeleteWhatsappCloudApiMediaInput:
      required:
        - media_id
      type: object
      properties:
        media_id:
          minLength: 1
          type: string
        waba_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteWhatsappCloudApiMediaOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    DeleteWhatsappCloudApiMediaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteWhatsappCloudApiMediaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteWhatsappCloudApiTemplateInput:
      required:
        - sleekflow_company_id
        - template_name
        - waba_id
      type: object
      properties:
        waba_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        template_name:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteWhatsappCloudApiTemplateOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    DeleteWhatsappCloudApiTemplateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteWhatsappCloudApiTemplateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeregisterPhoneNumberInput:
      required:
        - messaging_hub_phone_number_id
        - messaging_hub_waba_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        messaging_hub_phone_number_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DeregisterPhoneNumberOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    DeregisterPhoneNumberOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeregisterPhoneNumberOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DisassociateFacebookBusinessAccountFromCompanyInput:
      required:
        - facebook_business_id
        - sleekflow_company_id
        - sleekflow_staff_identity_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        sleekflow_staff_identity_id:
          minLength: 1
          type: string
      additionalProperties: false
    DisassociateFacebookBusinessAccountFromCompanyOutput:
      type: object
      properties:
        business_balances:
          type: array
          items:
            $ref: '#/components/schemas/ManagementBusinessBalanceDto'
          nullable: true
      additionalProperties: false
    DisassociateFacebookBusinessAccountFromCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DisassociateFacebookBusinessAccountFromCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DisconnectWhatsappCloudApiChannelInput:
      required:
        - sleekflow_company_id
        - waba_id
        - waba_phone_number_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    DisconnectWhatsappCloudApiChannelOutput:
      type: object
      properties:
        disconnected:
          type: boolean
      additionalProperties: false
    DisconnectWhatsappCloudApiChannelOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DisconnectWhatsappCloudApiChannelOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EditWhatsappCloudApiTemplateInput:
      required:
        - sleekflow_company_id
        - template_components
        - template_id
        - waba_id
      type: object
      properties:
        waba_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
        template_id:
          minLength: 1
          type: string
        template_components:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateComponentObject'
      additionalProperties: false
    EditWhatsappCloudApiTemplateOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    EditWhatsappCloudApiTemplateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EditWhatsappCloudApiTemplateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueBusinessBalancePendingTransactionLogCreatedInput:
      required:
        - facebook_business_id
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
      additionalProperties: false
    EnqueueBusinessBalancePendingTransactionLogCreatedOutput:
      type: object
      additionalProperties: false
    EnqueueBusinessBalancePendingTransactionLogCreatedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnqueueBusinessBalancePendingTransactionLogCreatedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionInput:
      required:
        - facebook_business_id
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
      additionalProperties: false
    EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutput:
      type: object
      additionalProperties: false
    EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnqueueOnCloudApiAccumulateHalfHourConversationUsageTransactionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueOnCloudApiBusinessBalanceResynchronizationInput:
      required:
        - facebook_business_id
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
      additionalProperties: false
    EnqueueOnCloudApiBusinessBalanceResynchronizationOutput:
      type: object
      additionalProperties: false
    EnqueueOnCloudApiBusinessBalanceResynchronizationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnqueueOnCloudApiBusinessBalanceResynchronizationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationInput:
      required:
        - facebook_business_id
        - facebook_waba_id
        - last_conversation_usage_insert_timestamp
        - latest_conversation_usage_insert_timestamp
      type: object
      properties:
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        last_conversation_usage_insert_timestamp:
          type: integer
          format: int64
        latest_conversation_usage_insert_timestamp:
          type: integer
          format: int64
      additionalProperties: false
    EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutput:
      type: object
      additionalProperties: false
    EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnqueueOnCloudApiHalfHourConversationUsageTransactionResynchronizationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksInput:
      type: object
      properties:
        secret:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutput:
      type: object
      additionalProperties: false
    EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueWabaBusinessConnectedInput:
      required:
        - facebook_business_id
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
        webhook_url:
          type: string
          nullable: true
      additionalProperties: false
    EnqueueWabaBusinessConnectedOutput:
      type: object
      additionalProperties: false
    EnqueueWabaBusinessConnectedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnqueueWabaBusinessConnectedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ErrorDataObject:
      type: object
      properties:
        details:
          type: string
          nullable: true
      additionalProperties: false
    ExampleObject:
      type: object
      properties:
        header_text:
          type: array
          items:
            type: string
          nullable: true
        body_text:
          type: array
          items:
            type: array
            items:
              type: string
          nullable: true
        header_url:
          type: array
          items:
            type: string
          nullable: true
        header_handle:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    FacebookApplication:
      type: object
      properties:
        category:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
      additionalProperties: false
    FacebookBusinessIntegrationSystemUserAccessToken:
      type: object
      properties:
        encrypted_token:
          type: string
          nullable: true
        scopes:
          type: array
          items:
            type: string
          nullable: true
        granular_scopes:
          type: array
          items:
            $ref: '#/components/schemas/GranularScope'
          nullable: true
        facebook_app_id:
          type: string
          nullable: true
        facebook_application:
          type: string
          nullable: true
        facebook_business_system_user_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
      additionalProperties: false
    FacebookBusinessProfileResult:
      type: object
      properties:
        messaging_product:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        vertical:
          type: string
          nullable: true
        about:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        websites:
          type: array
          items:
            type: string
          nullable: true
        profile_picture_url:
          type: string
          nullable: true
      additionalProperties: false
    FacebookBusinessWaba:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        facebook_waba_name:
          type: string
          nullable: true
        facebook_phone_numbers:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    FacebookBusinessWabaDto:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        facebook_waba_name:
          type: string
          nullable: true
        facebook_phone_numbers:
          type: array
          items:
            type: string
          nullable: true
        facebook_waba_timezone:
          $ref: '#/components/schemas/FacebookTimezone'
      additionalProperties: false
    FacebookConnectedWabaProductCatalogMappingDto:
      type: object
      properties:
        waba:
          $ref: '#/components/schemas/WabaDto'
        waba_product_catalog:
          $ref: '#/components/schemas/WabaProductCatalogDto'
      additionalProperties: false
    FacebookProductCatalogDto:
      type: object
      properties:
        facebook_product_catalog_id:
          type: string
          nullable: true
        facebook_product_catalog_name:
          type: string
          nullable: true
        default_image_url:
          type: string
          nullable: true
        product_count:
          type: integer
          format: int32
          nullable: true
        vertical:
          type: string
          nullable: true
      additionalProperties: false
    FacebookTimezone:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        displayName:
          type: string
          nullable: true
        timezoneOffset:
          type: string
          format: date-span
      additionalProperties: false
    FlowValidationError:
      type: object
      properties:
        error:
          type: string
          nullable: true
        error_type:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
        line_start:
          type: integer
          format: int32
        line_end:
          type: integer
          format: int32
        column_start:
          type: integer
          format: int32
        column_end:
          type: integer
          format: int32
      additionalProperties: false
    FlowWebPreview:
      type: object
      properties:
        preview_url:
          type: string
          nullable: true
        expires_at:
          type: string
          nullable: true
      additionalProperties: false
    GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkInput:
      required:
        - credited_by
        - facebook_business_id
        - sleekflow_company_id
        - top_up_plan_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        top_up_plan_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        facebook_waba_id:
          type: string
          nullable: true
        stripe_customer_id:
          type: string
          nullable: true
        credited_by:
          minLength: 1
          type: string
        credited_by_display_name:
          type: string
          nullable: true
        redirect_to_url:
          type: string
          nullable: true
      additionalProperties: false
    GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput:
      type: object
      properties:
        payment_url:
          type: string
          nullable: true
      additionalProperties: false
    GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GenerateWhatsappCloudApiBusinessBalanceStripeTopUpLinkOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetAllManagementWhatsappCloudApiBusinessBalancesInput:
      type: object
      additionalProperties: false
    GetAllManagementWhatsappCloudApiBusinessBalancesOutput:
      type: object
      properties:
        business_balances:
          type: array
          items:
            $ref: '#/components/schemas/ManagementBusinessBalanceDto'
          nullable: true
      additionalProperties: false
    GetAllManagementWhatsappCloudApiBusinessBalancesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiBusinessBalancesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput:
      required:
        - end
        - granularity
        - limit
        - start
      type: object
      properties:
        start:
          type: string
          format: date-time
        end:
          type: string
          format: date-time
        granularity:
          minLength: 1
          pattern: ^(HALF_HOUR|DAILY|MONTHLY)$
          type: string
        limit:
          maximum: 10000
          minimum: 1
          type: integer
          format: int32
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput:
      type: object
      properties:
        whatsapp_cloud_api_conversation_usage_analytics:
          type: array
          items:
            $ref: '#/components/schemas/ManagementWhatsappCloudApiConversationUsageAnalytic'
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetAllManagementWhatsappCloudApiWabasInput:
      type: object
      additionalProperties: false
    GetAllManagementWhatsappCloudApiWabasOutput:
      type: object
      properties:
        wabas:
          type: array
          items:
            $ref: '#/components/schemas/ManagementWabaDto'
          nullable: true
      additionalProperties: false
    GetAllManagementWhatsappCloudApiWabasOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAllManagementWhatsappCloudApiWabasOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetAuditLogsInput:
      required:
        - auditing_operations
        - limit
      type: object
      properties:
        auditing_operations:
          type: array
          items:
            type: string
        sleekflow_company_id:
          type: string
          nullable: true
        has_cloud_api_audits_exception:
          type: boolean
          nullable: true
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    GetAuditLogsOutput:
      type: object
      properties:
        audit_logs:
          type: array
          items:
            $ref: '#/components/schemas/AuditLog'
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetAuditLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAuditLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetBlobSasUrlOutput:
      type: object
      properties:
        blobId:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        expiresOn:
          type: string
          format: date-time
      additionalProperties: false
    GetBusinessBalanceCreditTransferTransactionLogsInput:
      required:
        - facebook_business_id
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
        facebook_waba_id:
          type: string
          nullable: true
        created_at_range:
          $ref: '#/components/schemas/DateTimeOffsetRange'
        updated_at_range:
          $ref: '#/components/schemas/DateTimeOffsetRange'
        limit:
          type: integer
          format: int32
          nullable: true
        order_by:
          pattern: ^(created_at|updated_at|credit_transfer_amount)$
          type: string
          nullable: true
        order:
          pattern: ^(ACS|DESC)$
          type: string
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetBusinessBalanceCreditTransferTransactionLogsOutput:
      type: object
      properties:
        credit_transfer_transaction_logs:
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferTransactionLogDto'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
        count:
          type: integer
          format: int32
      additionalProperties: false
    GetBusinessBalanceCreditTransferTransactionLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetBusinessBalanceCreditTransferTransactionLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetBusinessDetailResponse:
      type: object
      properties:
        id:
          type: string
          nullable: true
        block_offline_analytics:
          type: boolean
        timezone_id:
          type: integer
          format: int32
        name:
          type: string
          nullable: true
        profile_picture_uri:
          type: string
          nullable: true
        vertical:
          type: string
          nullable: true
        verification_status:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        is_hidden:
          type: boolean
      additionalProperties: false
    GetConnectedWhatsappCloudApiChannelsInput:
      required:
        - should_refresh
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        should_refresh:
          type: boolean
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetConnectedWhatsappCloudApiChannelsOutput:
      type: object
      properties:
        connected_cloud_apis:
          type: array
          items:
            $ref: '#/components/schemas/ConnectedCloudApiChannelDto'
          nullable: true
      additionalProperties: false
    GetConnectedWhatsappCloudApiChannelsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetConnectedWhatsappCloudApiChannelsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetConnectedWhatsappCloudApiWabasInput:
      required:
        - should_refresh
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        should_refresh:
          type: boolean
        user_access_token:
          type: string
          nullable: true
        facebook_authorization_code:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetConnectedWhatsappCloudApiWabasOutput:
      type: object
      properties:
        connected_wabas:
          type: array
          items:
            $ref: '#/components/schemas/WabaDto'
          nullable: true
        business_integration_system_user_access_token:
          type: string
          nullable: true
      additionalProperties: false
    GetConnectedWhatsappCloudApiWabasOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetConnectedWhatsappCloudApiWabasOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetConversationAnalyticsInput:
      required:
        - end_timestamp
        - phone_number
        - start_timestamp
        - waba_id
      type: object
      properties:
        waba_id:
          minLength: 1
          type: string
        start_timestamp:
          minimum: 0
          type: integer
          format: int64
        end_timestamp:
          minimum: 0
          type: integer
          format: int64
        phone_number:
          minLength: 1
          type: string
      additionalProperties: false
    GetConversationAnalyticsOutput:
      type: object
      properties:
        conversation_analytics:
          type: array
          items:
            $ref: '#/components/schemas/ConversationAnalyticsResultData'
          nullable: true
      additionalProperties: false
    GetConversationAnalyticsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetConversationAnalyticsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetConversationalAutomationByPhoneNumberIdResponse:
      type: object
      properties:
        id:
          type: string
          nullable: true
        conversational_automation:
          $ref: '#/components/schemas/ConversationalAutomation'
      additionalProperties: false
    GetConversationalAutomationsByWabaIdResponse:
      type: object
      properties:
        paging:
          $ref: '#/components/schemas/CursorBasedPaginationResult'
        data:
          type: array
          items:
            $ref: '#/components/schemas/ConversationalAutomationListResponse'
          nullable: true
      additionalProperties: false
    GetFacebookBusinessProductCatalogsInput:
      required:
        - facebook_business_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetFacebookBusinessProductCatalogsOutput:
      type: object
      properties:
        waba_product_catalog:
          type: array
          items:
            $ref: '#/components/schemas/WabaProductCatalogDto'
          nullable: true
      additionalProperties: false
    GetFacebookBusinessProductCatalogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFacebookBusinessProductCatalogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsInput:
      required:
        - business_balance_transaction_log_filter
        - limit
      type: object
      properties:
        business_balance_transaction_log_filter:
          $ref: '#/components/schemas/BusinessBalanceTransactionLogFilter'
        limit:
          maximum: 10000
          minimum: 1
          type: integer
          format: int32
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput:
      type: object
      properties:
        business_balance_transaction_logs:
          type: array
          items:
            $ref: '#/components/schemas/BusinessBalanceTransactionLog'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
        count:
          type: integer
          format: int32
      additionalProperties: false
    GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFilteredManagementWhatsappCloudApiBusinessBalanceTransactionLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFlowResponse:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        json_version:
          type: string
          nullable: true
        data_api_version:
          type: string
          nullable: true
        endpoint_uri:
          type: string
          nullable: true
        categories:
          type: array
          items:
            type: string
          nullable: true
        validation_errors:
          type: array
          items:
            $ref: '#/components/schemas/FlowValidationError'
          nullable: true
        health_status:
          $ref: '#/components/schemas/MessagingHealthStatus'
        assets:
          $ref: '#/components/schemas/WhatsappFlowAssetData'
        updated_at:
          type: string
          nullable: true
        preview:
          $ref: '#/components/schemas/FlowWebPreview'
        application:
          $ref: '#/components/schemas/FacebookApplication'
      additionalProperties: false
    GetFlowsResponse:
      type: object
      properties:
        paging:
          $ref: '#/components/schemas/CursorBasedPaginationResult'
        data:
          type: array
          items:
            $ref: '#/components/schemas/GetFlowResponse'
          nullable: true
      additionalProperties: false
    GetManagementWhatsappCloudApiBusinessBalancesInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetManagementWhatsappCloudApiBusinessBalancesOutput:
      type: object
      properties:
        business_balances:
          type: array
          items:
            $ref: '#/components/schemas/ManagementBusinessBalanceDto'
          nullable: true
      additionalProperties: false
    GetManagementWhatsappCloudApiBusinessBalancesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetManagementWhatsappCloudApiBusinessBalancesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetManagementWhatsappCloudApiConversationUsageAnalyticInput:
      required:
        - end
        - facebook_business_id
        - granularity
        - start
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
        start:
          type: string
          format: date-time
        end:
          type: string
          format: date-time
        granularity:
          minLength: 1
          pattern: ^(HALF_HOUR|DAILY|MONTHLY)$
          type: string
      additionalProperties: false
    GetManagementWhatsappCloudApiConversationUsageAnalyticOutput:
      type: object
      properties:
        summarized_conversation_usage_analytic:
          $ref: '#/components/schemas/WhatsappCloudApiDetailedConversationUsageAnalyticDto'
        facebook_business_id:
          type: string
          nullable: true
        facebook_business_name:
          type: string
          nullable: true
        waba_conversation_usage_analytics:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWabaConversationUsageAnalytic'
          nullable: true
      additionalProperties: false
    GetManagementWhatsappCloudApiConversationUsageAnalyticOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetManagementWhatsappCloudApiConversationUsageAnalyticOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetPhoneNumberConversationalAutomationInput:
      required:
        - facebook_phone_number_id
        - facebook_waba_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_phone_number_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetPhoneNumberConversationalAutomationOutput:
      type: object
      properties:
        conversational_automation_response:
          $ref: '#/components/schemas/GetConversationalAutomationByPhoneNumberIdResponse'
      additionalProperties: false
    GetPhoneNumberConversationalAutomationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetPhoneNumberConversationalAutomationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetPhoneNumberWhatsappBusinessProfileInput:
      required:
        - messaging_hub_phone_number_id
        - messaging_hub_waba_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        messaging_hub_phone_number_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetPhoneNumberWhatsappBusinessProfileOutput:
      type: object
      properties:
        messaging_hub_waba_id:
          type: string
          nullable: true
        messaging_hub_phone_number_id:
          type: string
          nullable: true
        whatsapp_business_profile:
          $ref: '#/components/schemas/FacebookBusinessProfileResult'
      additionalProperties: false
    GetPhoneNumberWhatsappBusinessProfileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetPhoneNumberWhatsappBusinessProfileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProductCatalogProductItemInput:
      required:
        - facebook_product_catalog_id
        - facebook_retailer_id
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        facebook_product_catalog_id:
          minLength: 1
          type: string
        facebook_retailer_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProductCatalogProductItemOutput:
      type: object
      properties:
        product_item:
          $ref: '#/components/schemas/ProductItemDto'
      additionalProperties: false
    GetProductCatalogProductItemOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProductCatalogProductItemOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetProductCatalogProductItemsInput:
      required:
        - facebook_product_catalog_id
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        facebook_product_catalog_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetProductCatalogProductItemsOutput:
      type: object
      properties:
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/ProductItemDto'
          nullable: true
      additionalProperties: false
    GetProductCatalogProductItemsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetProductCatalogProductItemsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUploadMediaLinkInput:
      type: object
      additionalProperties: false
    GetUploadMediaLinkOutput:
      type: object
      properties:
        blob_sas_url:
          $ref: '#/components/schemas/GetBlobSasUrlOutput'
      additionalProperties: false
    GetUploadMediaLinkOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUploadMediaLinkOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWabaBalanceAutoTopUpProfileInput:
      required:
        - facebook_business_id
      type: object
      properties:
        facebook_business_id:
          minLength: 1
          type: string
        facebook_waba_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWabaBalanceAutoTopUpProfileOutput:
      type: object
      properties:
        business_balance_auto_top_up_profiles:
          type: array
          items:
            $ref: '#/components/schemas/WabaBalanceAutoTopUpProfile'
          nullable: true
      additionalProperties: false
    GetWabaBalanceAutoTopUpProfileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWabaBalanceAutoTopUpProfileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWabaPhoneNumbersConversationalAutomationsInput:
      required:
        - facebook_waba_id
      type: object
      properties:
        facebook_waba_id:
          minLength: 1
          type: string
        pagination_param:
          $ref: '#/components/schemas/CursorBasedPaginationParam'
      additionalProperties: false
    GetWabaPhoneNumbersConversationalAutomationsOutput:
      type: object
      properties:
        conversational_automations_response:
          $ref: '#/components/schemas/GetConversationalAutomationsByWabaIdResponse'
      additionalProperties: false
    GetWabaPhoneNumbersConversationalAutomationsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWabaPhoneNumbersConversationalAutomationsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWabaProductCatalogInput:
      required:
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWabaProductCatalogOutput:
      type: object
      properties:
        waba_product_catalog:
          $ref: '#/components/schemas/WabaProductCatalogDto'
      additionalProperties: false
    GetWabaProductCatalogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWabaProductCatalogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWabasConnectedProductCatalogsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        user_access_token:
          type: string
          nullable: true
        should_refresh:
          type: boolean
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetWabasConnectedProductCatalogsOutput:
      type: object
      properties:
        facebook_connected_waba_product_catalog_mappings:
          type: array
          items:
            $ref: '#/components/schemas/FacebookConnectedWabaProductCatalogMappingDto'
          nullable: true
      additionalProperties: false
    GetWabasConnectedProductCatalogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWabasConnectedProductCatalogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeInput:
      required:
        - should_refresh
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        should_refresh:
          type: boolean
        facebook_authorization_code:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutput:
      type: object
      properties:
        facebook_connected_waba_product_catalog_mappings:
          type: array
          items:
            $ref: '#/components/schemas/FacebookConnectedWabaProductCatalogMappingDto'
          nullable: true
      additionalProperties: false
    GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWabasConnectedProductCatalogsWithFacebookAuthorizationCodeOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappBusinessAccountSubscribedAppsResponse:
      type: object
      properties:
        subscribed_apps:
          $ref: '#/components/schemas/SubscribedAppsData'
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        account_review_status:
          type: string
          nullable: true
        on_behalf_of_business_info:
          $ref: '#/components/schemas/OnBehalfOfBusinessInfo'
        message_template_namespace:
          type: string
          nullable: true
        timezone_id:
          type: string
          nullable: true
        primary_funding_id:
          type: string
          nullable: true
        currency:
          type: string
          nullable: true
        purchase_order_number:
          type: string
          nullable: true
        marketing_messages_lite_api_status:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput:
      required:
        - facebook_business_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput:
      type: object
      properties:
        business_balance_auto_top_up_profile:
          $ref: '#/components/schemas/BusinessBalanceAutoTopUpProfileDto'
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput:
      type: object
      properties:
        minimum_balances:
          type: array
          items:
            $ref: '#/components/schemas/Money'
          nullable: true
        auto_top_up_plans:
          type: array
          items:
            $ref: '#/components/schemas/StripeWhatsAppCreditTopUpPlan'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceAutoTopUpProfileSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput:
      required:
        - limit
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        start:
          type: string
          format: date-time
          nullable: true
        end:
          type: string
          format: date-time
          nullable: true
        limit:
          maximum: 10000
          minimum: 1
          type: integer
          format: int32
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput:
      type: object
      properties:
        invoices:
          type: array
          items:
            $ref: '#/components/schemas/BusinessBalanceInvoice'
          nullable: true
        next_continuation_token:
          type: string
          nullable: true
        count:
          type: integer
          format: int32
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput:
      type: object
      properties:
        top_up_plans:
          type: array
          items:
            $ref: '#/components/schemas/StripeWhatsAppCreditTopUpPlan'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalanceStripeTopUpPlansOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalancesInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalancesOutput:
      type: object
      properties:
        business_balances:
          type: array
          items:
            $ref: '#/components/schemas/BusinessBalanceDto'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiBusinessBalancesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiBusinessBalancesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiConversationUsageAnalyticInput:
      required:
        - end
        - facebook_business_id
        - facebook_waba_id
        - granularity
        - is_detailed
        - sleekflow_company_id
        - start
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        start:
          type: string
          format: date-time
        end:
          type: string
          format: date-time
        granularity:
          minLength: 1
          pattern: ^(HALF_HOUR|DAILY|MONTHLY)$
          type: string
        is_detailed:
          type: boolean
      additionalProperties: false
    GetWhatsappCloudApiConversationUsageAnalyticOutput:
      type: object
      properties:
        conversation_usage_analytic:
          $ref: '#/components/schemas/WhatsappCloudApiDetailedConversationUsageAnalyticDto'
        facebook_business_id:
          type: string
          nullable: true
        facebook_business_name:
          type: string
          nullable: true
        facebook_business_waba:
          $ref: '#/components/schemas/FacebookBusinessWabaDto'
      additionalProperties: false
    GetWhatsappCloudApiConversationUsageAnalyticOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiConversationUsageAnalyticOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiMediaInput:
      required:
        - media_id
        - sleekflow_company_id
        - waba_phone_number_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        media_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiMediaOutput:
      type: object
      properties:
        blob_id:
          type: string
          nullable: true
        blob_url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
      additionalProperties: false
    GetWhatsappCloudApiMediaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiMediaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiMediaUrlInput:
      required:
        - media_id
      type: object
      properties:
        media_id:
          minLength: 1
          type: string
        waba_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiMediaUrlOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        mime_type:
          type: string
          nullable: true
        sha256:
          type: string
          nullable: true
        file_size:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiMediaUrlOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiMediaUrlOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiTemplatesInput:
      required:
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        waba_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiTemplatesOutput:
      type: object
      properties:
        message_templates:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplate'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiTemplatesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiTemplatesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdInput:
      required:
        - facebook_waba_id
        - sleekflow_company_id
        - user_access_token
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        user_access_token:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutput:
      type: object
      properties:
        phone_numbers:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappPhoneNumberDetail'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessPhoneNumbersByWabaIdOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessWabasInput:
      required:
        - facebook_business_id
        - sleekflow_company_id
        - user_access_token
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        user_access_token:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessWabasOutput:
      type: object
      properties:
        user_business_wabas:
          type: array
          items:
            $ref: '#/components/schemas/GetWhatsappBusinessAccountSubscribedAppsResponse'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessWabasOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessWabasOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessesInput:
      required:
        - sleekflow_company_id
        - user_access_token
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        user_access_token:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessesOutput:
      type: object
      properties:
        user_businesses:
          type: array
          items:
            $ref: '#/components/schemas/GetBusinessDetailResponse'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiUserBusinessesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiUserBusinessesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiWabaInput:
      required:
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappCloudApiWabaOutput:
      type: object
      properties:
        waba:
          $ref: '#/components/schemas/WabaDto'
      additionalProperties: false
    GetWhatsappCloudApiWabaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiWabaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput:
      required:
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        whatsapp_cloud_api_webhook_status_update_audit_logs_filters:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters'
        offset:
          type: integer
          format: int32
          nullable: true
        limit:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput:
      type: object
      properties:
        whatsapp_cloud_api_webhook_status_update_audit_logs:
          type: array
          items:
            $ref: '#/components/schemas/AuditLog'
          nullable: true
      additionalProperties: false
    GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappFlowInput:
      required:
        - flow_id
        - messaging_hub_waba_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        flow_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetWhatsappFlowOutput:
      type: object
      properties:
        flow_details:
          $ref: '#/components/schemas/GetFlowResponse'
      additionalProperties: false
    GetWhatsappFlowOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappFlowOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWhatsappFlowsByWabaIdInput:
      required:
        - messaging_hub_waba_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        pagination_Param:
          $ref: '#/components/schemas/CursorBasedPaginationParam'
      additionalProperties: false
    GetWhatsappFlowsByWabaIdOutput:
      required:
        - flows
      type: object
      properties:
        flows:
          $ref: '#/components/schemas/GetFlowsResponse'
      additionalProperties: false
    GetWhatsappFlowsByWabaIdOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWhatsappFlowsByWabaIdOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GranularScope:
      type: object
      properties:
        scope:
          type: string
          nullable: true
        target_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    InitiateWhatsappCloudApiPhoneNumberWabaMigrationInput:
      required:
        - country_code
        - facebook_waba_id
        - phone_number
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        country_code:
          minLength: 1
          type: string
        phone_number:
          minLength: 1
          type: string
      additionalProperties: false
    InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitiateWhatsappCloudApiPhoneNumberWabaMigrationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InternalTopUpCreditDetail:
      type: object
      properties:
        unique_id:
          type: string
          nullable: true
        credited_by:
          type: string
          nullable: true
        credited_by_user_name:
          type: string
          nullable: true
        credited_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    LimitedTimeOffer:
      type: object
      properties:
        text:
          type: string
          nullable: true
        has_expiration:
          type: boolean
          nullable: true
      additionalProperties: false
    ManagementBusinessBalanceDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_ids:
          type: array
          items:
            type: string
          nullable: true
        total_used:
          $ref: '#/components/schemas/Money'
        mark_up:
          $ref: '#/components/schemas/Money'
        transaction_handling_fee:
          $ref: '#/components/schemas/Money'
        waba_markup_profile:
          $ref: '#/components/schemas/MarkupProfile'
        conversation_usage_insert_state:
          $ref: '#/components/schemas/ConversationUsageInsertState'
        facebook_business_id:
          type: string
          nullable: true
        facebook_business_name:
          type: string
          nullable: true
        facebook_business_wabas:
          type: array
          items:
            $ref: '#/components/schemas/FacebookBusinessWaba'
          nullable: true
        total_credit:
          $ref: '#/components/schemas/Money'
        all_time_usage:
          $ref: '#/components/schemas/Money'
        balance:
          $ref: '#/components/schemas/Money'
        waba_balances:
          type: array
          items:
            $ref: '#/components/schemas/WabaBalanceDto'
          nullable: true
        unallocated_credit:
          $ref: '#/components/schemas/Money'
        is_by_waba_billing_enabled:
          type: boolean
        un_calculated_credit_transfer_transaction_logs:
          type: array
          items:
            $ref: '#/components/schemas/CreditTransferTransactionLogDto'
          nullable: true
        _etag:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    ManagementWabaDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        facebook_waba_id:
          type: string
          nullable: true
        sleekflow_company_ids:
          type: array
          items:
            type: string
          nullable: true
        facebook_business_id:
          type: string
          nullable: true
        facebook_waba_business_id:
          type: string
          nullable: true
        facebook_waba_business_name:
          type: string
          nullable: true
        facebook_waba_business_verification_status:
          type: string
          nullable: true
        facebook_waba_business_profile_picture_uri:
          type: string
          nullable: true
        facebook_waba_business_vertical:
          type: string
          nullable: true
        facebook_waba_name:
          type: string
          nullable: true
        facebook_waba_account_review_status:
          type: string
          nullable: true
        facebook_waba_message_template_namespace:
          type: string
          nullable: true
        waba_dto_phone_numbers:
          type: array
          items:
            $ref: '#/components/schemas/WabaPhoneNumberDto'
          nullable: true
        waba_product_catalog:
          $ref: '#/components/schemas/WabaProductCatalogDto'
        waba_dataset:
          $ref: '#/components/schemas/WabaDatasetDto'
        marketing_messages_lite_api_status:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    ManagementWhatsappCloudApiConversationUsageAnalytic:
      type: object
      properties:
        summarized_conversation_usage_analytic:
          $ref: '#/components/schemas/WhatsappCloudApiConversationUsageAnalyticDto'
        facebook_business_id:
          type: string
          nullable: true
        facebook_business_name:
          type: string
          nullable: true
        waba_conversation_usage_analytics:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWabaConversationUsageAnalytic'
          nullable: true
      additionalProperties: false
    MarkupProfile:
      type: object
      properties:
        per_paid_business_initiated_conversation_fee_markup:
          $ref: '#/components/schemas/Money'
        per_paid_user_initiated_conversation_fee_markup:
          $ref: '#/components/schemas/Money'
        business_initiated_conversation_fee_price_markup_percentage:
          type: number
          format: decimal
        user_initiated_conversation_fee_price_markup_percentage:
          type: number
          format: decimal
        transaction_handling_fee_rate:
          type: number
          format: decimal
          nullable: true
      additionalProperties: false
    MessageTemplateQualityScore:
      type: object
      properties:
        score:
          type: string
          nullable: true
        date:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    MessagingEntity:
      type: object
      properties:
        entity_type:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        can_send_message:
          type: string
          nullable: true
        errors:
          type: array
          items:
            $ref: '#/components/schemas/MessagingEntityError'
          nullable: true
        additional_info:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    MessagingEntityError:
      type: object
      properties:
        error_code:
          type: integer
          format: int32
          nullable: true
        error_description:
          type: string
          nullable: true
        possible_solution:
          type: string
          nullable: true
      additionalProperties: false
    MessagingHealthStatus:
      type: object
      properties:
        can_send_message:
          type: string
          nullable: true
        entities:
          type: array
          items:
            $ref: '#/components/schemas/MessagingEntity'
          nullable: true
      additionalProperties: false
    MigrateWhatsappCloudApiPhoneNumberToCloudApiInput:
      required:
        - facebook_phone_number_id
        - facebook_waba_id
        - pin
        - sleekflow_company_id
        - user_access_token
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_phone_number_id:
          minLength: 1
          type: string
        pin:
          minLength: 1
          type: string
        user_access_token:
          minLength: 1
          type: string
        back_up_data:
          type: string
          nullable: true
        back_up_password:
          type: string
          nullable: true
      additionalProperties: false
    MigrateWhatsappCloudApiPhoneNumberToCloudApiOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    MigrateWhatsappCloudApiPhoneNumberToCloudApiOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/MigrateWhatsappCloudApiPhoneNumberToCloudApiOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Money:
      type: object
      properties:
        currency_iso_code:
          type: string
          nullable: true
        amount:
          type: number
          format: decimal
      additionalProperties: false
    OnBehalfOfBusinessInfo:
      type: object
      properties:
        name:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    OnboardPartnersToMMLiteInput:
      required:
        - facebook_business_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        facebook_business_id:
          minLength: 1
          type: string
      additionalProperties: false
    OnboardPartnersToMMLiteOutput:
      type: object
      properties:
        wabas:
          type: array
          items:
            $ref: '#/components/schemas/WabaDto'
          nullable: true
      additionalProperties: false
    OnboardPartnersToMMLiteOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/OnboardPartnersToMMLiteOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    OneTimePatchExistingWabaBalancesInput:
      type: object
      properties:
        secret:
          type: string
          nullable: true
      additionalProperties: false
    OneTimePatchExistingWabaBalancesOutput:
      type: object
      additionalProperties: false
    OneTimePatchExistingWabaBalancesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/OneTimePatchExistingWabaBalancesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ProductItemDto:
      type: object
      properties:
        facebook_product_id:
          type: string
          nullable: true
        retailer_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        retailer_product_group_id:
          type: string
          nullable: true
        image_url:
          type: string
          nullable: true
        images:
          type: array
          items:
            type: string
          nullable: true
        additional_image_urls:
          type: array
          items:
            type: string
          nullable: true
        url:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        short_description:
          type: string
          nullable: true
        product_catalog:
          $ref: '#/components/schemas/FacebookProductCatalogDto'
        product_type:
          type: string
          nullable: true
        quantity_to_sell_on_facebook:
          type: integer
          format: int32
          nullable: true
        inventory:
          type: integer
          format: int32
          nullable: true
        review_status:
          type: string
          nullable: true
        review_rejection_reasons:
          type: array
          items:
            type: string
          nullable: true
        price:
          type: string
          nullable: true
        currency:
          type: string
          nullable: true
        availability:
          type: string
          nullable: true
        condition:
          type: string
          nullable: true
        brand:
          type: string
          nullable: true
      additionalProperties: false
    ReconnectWhatsappCloudApiChannelInput:
      required:
        - sleekflow_company_id
        - waba_id
        - waba_phone_number_id
        - webhook_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        webhook_url:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ReconnectWhatsappCloudApiChannelOutput:
      type: object
      properties:
        connected_cloud_api_channel:
          $ref: '#/components/schemas/ConnectedCloudApiChannelDto'
      additionalProperties: false
    ReconnectWhatsappCloudApiChannelOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ReconnectWhatsappCloudApiChannelOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RefreshWabaPhoneNumberProductCatalogSettingInput:
      required:
        - phone_number_id
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        phone_number_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    RefreshWabaPhoneNumberProductCatalogSettingOutput:
      type: object
      properties:
        waba:
          $ref: '#/components/schemas/WabaDto'
        waba_phone_number:
          $ref: '#/components/schemas/WabaPhoneNumberDto'
      additionalProperties: false
    RefreshWabaPhoneNumberProductCatalogSettingOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RefreshWabaPhoneNumberProductCatalogSettingOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RegisterPhoneNumberInput:
      required:
        - messaging_hub_phone_number_id
        - messaging_hub_waba_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        messaging_hub_phone_number_id:
          minLength: 1
          type: string
        Pin:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    RegisterPhoneNumberOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    RegisterPhoneNumberOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RegisterPhoneNumberOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RegisterPhoneNumberResponse:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    RegisterWhatsAppPhoneNumberInput:
      required:
        - facebook_phone_number_id
        - facebook_waba_id
        - pin
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_phone_number_id:
          minLength: 1
          type: string
        pin:
          minLength: 1
          type: string
      additionalProperties: false
    RegisterWhatsAppPhoneNumberOutput:
      type: object
      properties:
        register_phone_number_response:
          $ref: '#/components/schemas/RegisterPhoneNumberResponse'
      additionalProperties: false
    RegisterWhatsAppPhoneNumberOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RegisterWhatsAppPhoneNumberOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeInput:
      required:
        - code_method
        - facebook_phone_number_id
        - facebook_waba_id
        - language
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_phone_number_id:
          minLength: 1
          type: string
        code_method:
          minLength: 1
          type: string
        language:
          minLength: 1
          type: string
      additionalProperties: false
    RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RequestWhatsappCloudApiPhoneNumberOwnershipVerificationCodeOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SendMessageResponse:
      type: object
      properties:
        messaging_product:
          type: string
          nullable: true
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiSendMessageContactResult'
          nullable: true
        messages:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiSendMessageResult'
          nullable: true
      additionalProperties: false
    SendWhatsappCloudApiMessageInput:
      required:
        - message_object
        - sleekflow_company_id
        - waba_phone_number_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        message_object:
          $ref: '#/components/schemas/WhatsappCloudApiMessageObject'
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
        is_mm_lite:
          type: boolean
      additionalProperties: false
    SendWhatsappCloudApiMessageOutput:
      type: object
      properties:
        message_response:
          $ref: '#/components/schemas/SendMessageResponse'
      additionalProperties: false
    SendWhatsappCloudApiMessageOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SendWhatsappCloudApiMessageOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    StorageConfiguration:
      type: object
      properties:
        data_localization_region:
          type: string
          nullable: true
        retention_minutes:
          type: integer
          format: int64
          nullable: true
        status:
          type: string
          nullable: true
        data_store_config_type:
          type: string
          nullable: true
      additionalProperties: false
    StripeTopUpCreditDetail:
      type: object
      properties:
        checkout_session_id:
          type: string
          nullable: true
        customer_id:
          type: string
          nullable: true
        invoice_id:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
        snapshot:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    StripeWhatsAppCreditTopUpPlan:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        price:
          $ref: '#/components/schemas/Money'
      additionalProperties: false
    SubscribedAppsData:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappBusinessApiData'
          nullable: true
      additionalProperties: false
    SuccessGraphApiResponse:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    SupportedApp:
      type: object
      properties:
        package_name:
          type: string
          nullable: true
        signature_hash:
          type: string
          nullable: true
      additionalProperties: false
    SwitchFromBusinessLevelToWabaLevelCreditManagementInput:
      required:
        - credit_allocation
        - eTag
        - facebook_business_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        eTag:
          minLength: 1
          type: string
        credit_allocation:
          $ref: '#/components/schemas/CreditAllocationObject'
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    SwitchFromBusinessLevelToWabaLevelCreditManagementOutput:
      type: object
      properties:
        business_balance:
          $ref: '#/components/schemas/BusinessBalanceDto'
      additionalProperties: false
    SwitchFromBusinessLevelToWabaLevelCreditManagementOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SwitchFromBusinessLevelToWabaLevelCreditManagementOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SwitchFromWabaLevelToBusinessLevelCreditManagementInput:
      required:
        - eTag
        - facebook_business_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        eTag:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    SwitchFromWabaLevelToBusinessLevelCreditManagementOutput:
      type: object
      properties:
        business_balance:
          $ref: '#/components/schemas/BusinessBalanceDto'
      additionalProperties: false
    SwitchFromWabaLevelToBusinessLevelCreditManagementOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SwitchFromWabaLevelToBusinessLevelCreditManagementOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TemplateButtonObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        otp_type:
          type: string
          nullable: true
        autofill_text:
          type: string
          nullable: true
        supported_apps:
          type: array
          items:
            $ref: '#/components/schemas/SupportedApp'
          nullable: true
        example:
          type: array
          items:
            type: string
          nullable: true
        flow_id:
          type: string
          nullable: true
        flow_action:
          type: string
          nullable: true
        navigate_screen:
          type: string
          nullable: true
      additionalProperties: false
    TimestampRange:
      type: object
      properties:
        start:
          type: integer
          format: int64
        end:
          type: integer
          format: int64
      additionalProperties: false
    TopUpWhatsappCloudApiBusinessBalanceInput:
      required:
        - credit
        - credited_by
        - facebook_business_id
        - metadata
        - top_up_method
        - unique_id
      type: object
      properties:
        unique_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        top_up_method:
          minLength: 1
          type: string
        credit:
          $ref: '#/components/schemas/Money'
        credited_by:
          minLength: 1
          type: string
        credited_by_display_name:
          type: string
          nullable: true
        stripe_top_up_credit_detail:
          $ref: '#/components/schemas/StripeTopUpCreditDetail'
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    TopUpWhatsappCloudApiBusinessBalanceOutput:
      type: object
      properties:
        waba_balance_transaction_log:
          $ref: '#/components/schemas/BusinessBalanceTransactionLog'
      additionalProperties: false
    TopUpWhatsappCloudApiBusinessBalanceOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TopUpWhatsappCloudApiBusinessBalanceOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    TopUpWhatsappCloudApiWabaBalanceInput:
      required:
        - credit
        - credited_by
        - facebook_business_id
        - facebook_waba_id
        - metadata
        - top_up_method
        - unique_id
      type: object
      properties:
        unique_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_business_id:
          minLength: 1
          type: string
        top_up_method:
          minLength: 1
          type: string
        credit:
          $ref: '#/components/schemas/Money'
        credited_by:
          minLength: 1
          type: string
        credited_by_display_name:
          type: string
          nullable: true
        stripe_top_up_credit_detail:
          $ref: '#/components/schemas/StripeTopUpCreditDetail'
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    TopUpWhatsappCloudApiWabaBalanceOutput:
      type: object
      properties:
        waba_balance_transaction_log:
          $ref: '#/components/schemas/BusinessBalanceTransactionLog'
      additionalProperties: false
    TopUpWhatsappCloudApiWabaBalanceOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/TopUpWhatsappCloudApiWabaBalanceOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateConversationalAutomationInput:
      required:
        - conversation_automation
        - facebook_phone_number_id
        - facebook_waba_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        facebook_phone_number_id:
          minLength: 1
          type: string
        conversation_automation:
          $ref: '#/components/schemas/ConversationalAutomation'
      additionalProperties: false
    UpdateConversationalAutomationOutput:
      type: object
      properties:
        success_response:
          $ref: '#/components/schemas/SuccessGraphApiResponse'
      additionalProperties: false
    UpdateConversationalAutomationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateConversationalAutomationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdatePhoneNumberBusinessProfileRequest:
      type: object
      properties:
        messaging_product:
          type: string
          nullable: true
        about:
          maxLength: 139
          minLength: 1
          type: string
          nullable: true
        address:
          maxLength: 256
          type: string
          nullable: true
        description:
          maxLength: 512
          type: string
          nullable: true
        vertical:
          type: string
          nullable: true
        email:
          maxLength: 128
          pattern: '^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
          type: string
          nullable: true
        websites:
          maxItems: 2
          type: array
          items:
            type: string
          nullable: true
        profile_picture_handle:
          type: string
          nullable: true
      additionalProperties: false
    UpdatePhoneNumberSettingsInput:
      required:
        - messaging_hub_phone_number_id
        - messaging_hub_waba_id
        - sleekflow_company_id
        - storage_configuration
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        messaging_hub_phone_number_id:
          minLength: 1
          type: string
        storage_configuration:
          $ref: '#/components/schemas/StorageConfiguration'
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdatePhoneNumberSettingsOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    UpdatePhoneNumberSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdatePhoneNumberSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdatePhoneNumberWhatsappBusinessProfileInput:
      required:
        - messaging_hub_phone_number_id
        - messaging_hub_waba_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - update_phone_number_business_profile
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        messaging_hub_phone_number_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          minLength: 1
          type: string
        update_phone_number_business_profile:
          $ref: '#/components/schemas/UpdatePhoneNumberBusinessProfileRequest'
      additionalProperties: false
    UpdatePhoneNumberWhatsappBusinessProfileOutput:
      type: object
      properties:
        messaging_hub_waba_id:
          type: string
          nullable: true
        messaging_hub_phone_number_id:
          type: string
          nullable: true
        whatsapp_business_profile:
          $ref: '#/components/schemas/FacebookBusinessProfileResult'
      additionalProperties: false
    UpdatePhoneNumberWhatsappBusinessProfileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdatePhoneNumberWhatsappBusinessProfileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWabaConnectedProductCatalogInput:
      required:
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        facebook_product_catalog_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateWabaConnectedProductCatalogOutput:
      type: object
      properties:
        waba:
          $ref: '#/components/schemas/WabaDto'
      additionalProperties: false
    UpdateWabaConnectedProductCatalogOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateWabaConnectedProductCatalogOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonInput:
      required:
        - is_cart_enabled
        - is_catalog_visible
        - phone_number_id
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        phone_number_id:
          minLength: 1
          type: string
        is_catalog_visible:
          type: boolean
        is_cart_enabled:
          type: boolean
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutput:
      type: object
      properties:
        waba_phone_number:
          $ref: '#/components/schemas/WabaPhoneNumberDto'
      additionalProperties: false
    UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWhatsappCloudApiBusinessBalanceMarkupProfileInput:
      required:
        - business_balance_id
        - markup_profile
        - should_recalculate
        - sleekflow_staff_id
      type: object
      properties:
        business_balance_id:
          minLength: 1
          type: string
        markup_profile:
          $ref: '#/components/schemas/MarkupProfile'
        should_recalculate:
          type: boolean
        last_recalculate_business_balance_transaction_log:
          type: string
          format: date-time
          nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutput:
      type: object
      additionalProperties: false
    UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateWhatsappCloudApiBusinessBalanceMarkupProfileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateWhatsappFlowMetadataInput:
      required:
        - flow_id
        - flow_metadata
        - messaging_hub_waba_id
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        messaging_hub_waba_id:
          minLength: 1
          type: string
        flow_id:
          minLength: 1
          type: string
        flow_metadata:
          $ref: '#/components/schemas/WhatsappCloudApiUpdateFlowMetadataObject'
        sleekflow_staff_id:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateWhatsappFlowMetadataOutput:
      type: object
      properties:
        updated_flow_details:
          $ref: '#/components/schemas/GetFlowResponse'
      additionalProperties: false
    UpdateWhatsappFlowMetadataOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateWhatsappFlowMetadataOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UploadTemplateHeaderFileInput:
      required:
        - blob_id
        - sleekflow_company_id
        - waba_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
        blob_storage_type:
          type: string
          nullable: true
      additionalProperties: false
    UploadTemplateHeaderFileOutput:
      type: object
      properties:
        header_handle:
          type: string
          nullable: true
        file_url:
          type: string
          nullable: true
      additionalProperties: false
    UploadTemplateHeaderFileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UploadTemplateHeaderFileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UploadWhatsappCloudApiMediaInput:
      required:
        - blob_id
        - sleekflow_company_id
        - waba_id
        - waba_phone_number_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        blob_id:
          minLength: 1
          type: string
      additionalProperties: false
    UploadWhatsappCloudApiMediaOutput:
      type: object
      properties:
        media_id:
          type: string
          nullable: true
      additionalProperties: false
    UploadWhatsappCloudApiMediaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UploadWhatsappCloudApiMediaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UploadWhatsappCloudApiMediaUsingUrlInput:
      required:
        - content_type
        - file_name
        - sleekflow_company_id
        - url
        - waba_id
        - waba_phone_number_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        file_name:
          minLength: 1
          type: string
        content_type:
          minLength: 1
          type: string
        url:
          minLength: 1
          type: string
      additionalProperties: false
    UploadWhatsappCloudApiMediaUsingUrlOutput:
      type: object
      properties:
        media_id:
          type: string
          nullable: true
      additionalProperties: false
    UploadWhatsappCloudApiMediaUsingUrlOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UploadWhatsappCloudApiMediaUsingUrlOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpsertWabaBalanceAutoTopUpProfileInput:
      required:
        - credited_by
        - redirect_to_url
        - sleekflow_company_id
        - waba_balance_auto_top_up_profile
      type: object
      properties:
        waba_balance_auto_top_up_profile:
          $ref: '#/components/schemas/WabaBalanceAutoTopUpProfileDto'
        customer_id:
          type: string
          nullable: true
        credited_by:
          minLength: 1
          type: string
        credited_by_display_name:
          type: string
          nullable: true
        redirect_to_url:
          minLength: 1
          type: string
        phone_number:
          type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpsertWabaBalanceAutoTopUpProfileOutput:
      type: object
      properties:
        waba_balance_auto_top_up_profile:
          $ref: '#/components/schemas/WabaBalanceAutoTopUpProfileDto'
        payment_url:
          type: string
          nullable: true
      additionalProperties: false
    UpsertWabaBalanceAutoTopUpProfileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpsertWabaBalanceAutoTopUpProfileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput:
      required:
        - business_balance_auto_top_up_profile
        - credited_by
        - redirect_to_url
        - sleekflow_company_id
      type: object
      properties:
        business_balance_auto_top_up_profile:
          $ref: '#/components/schemas/BusinessBalanceAutoTopUpProfileDto'
        customer_id:
          type: string
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        credited_by:
          minLength: 1
          type: string
        credited_by_display_name:
          type: string
          nullable: true
        redirect_to_url:
          minLength: 1
          type: string
        phone_number:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput:
      type: object
      properties:
        business_balance_auto_top_up_profile:
          $ref: '#/components/schemas/BusinessBalanceAutoTopUpProfileDto'
        payment_url:
          type: string
          nullable: true
      additionalProperties: false
    UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksInput:
      required:
        - facebook_business_ids
        - sleekflow_companyId
        - webhook_url
      type: object
      properties:
        sleekflow_companyId:
          minLength: 1
          type: string
        facebook_business_ids:
          type: array
          items:
            type: string
        webhook_url:
          minLength: 1
          type: string
      additionalProperties: false
    UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutput:
      type: object
      additionalProperties: false
    UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpsertWhatsappCloudApiBusinessBalanceChangedEventWebhooksOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpsertWhatsappCloudApiChannelWebhookInput:
      required:
        - sleekflow_company_id
        - waba_id
        - waba_phone_number_id
        - webhook_url
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        waba_id:
          minLength: 1
          type: string
        waba_phone_number_id:
          minLength: 1
          type: string
        webhook_url:
          minLength: 1
          type: string
      additionalProperties: false
    UpsertWhatsappCloudApiChannelWebhookOutput:
      type: object
      properties:
        connected_cloud_api_channel:
          $ref: '#/components/schemas/ConnectedCloudApiChannelDto'
      additionalProperties: false
    UpsertWhatsappCloudApiChannelWebhookOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpsertWhatsappCloudApiChannelWebhookOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    VerifyWhatsappCloudApiPhoneNumberOwnershipInput:
      required:
        - code
        - facebook_phone_number_id
        - facebook_waba_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        facebook_waba_id:
          minLength: 1
          type: string
        facebook_phone_number_id:
          minLength: 1
          type: string
        code:
          minLength: 1
          type: string
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    VerifyWhatsappCloudApiPhoneNumberOwnershipOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    VerifyWhatsappCloudApiPhoneNumberOwnershipOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/VerifyWhatsappCloudApiPhoneNumberOwnershipOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Waba:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        facebook_waba_business_id:
          type: string
          nullable: true
        facebook_business_id:
          type: string
          nullable: true
        sleekflow_company_ids:
          type: array
          items:
            type: string
          nullable: true
        facebook_waba_name:
          type: string
          nullable: true
        facebook_waba_account_review_status:
          type: string
          nullable: true
        facebook_waba_business_name:
          type: string
          nullable: true
        facebook_waba_primary_funding_id:
          type: string
          nullable: true
        facebook_waba_message_template_namespace:
          type: string
          nullable: true
        facebook_waba_long_lived_access_token:
          $ref: '#/components/schemas/WabaLongLivedAccessToken'
        facebook_business_integration_system_user_access_tokens:
          type: array
          items:
            $ref: '#/components/schemas/FacebookBusinessIntegrationSystemUserAccessToken'
          nullable: true
        facebook_waba_business_detail_snapshot:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        facebook_waba_business_account_snapshot:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        waba_phone_numbers:
          uniqueItems: true
          type: array
          items:
            $ref: '#/components/schemas/WabaPhoneNumber'
          nullable: true
        messaging_function_limitation:
          type: string
          nullable: true
        waba_active_product_catalog:
          $ref: '#/components/schemas/WabaProductCatalog'
        waba_product_catalog:
          $ref: '#/components/schemas/WabaProductCatalog'
        waba_dataset:
          $ref: '#/components/schemas/WabaDataset'
        record_status:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        _etag:
          type: string
          nullable: true
        marketing_messages_lite_api_status:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WabaAudit:
      type: object
      properties:
        snapshot:
          $ref: '#/components/schemas/Waba'
        changes:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    WabaBalance:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        credit:
          $ref: '#/components/schemas/Money'
        used:
          $ref: '#/components/schemas/Money'
        markup:
          $ref: '#/components/schemas/Money'
        transaction_handling_fee:
          $ref: '#/components/schemas/Money'
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        _etag:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WabaBalanceAutoTopUpProfile:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        facebook_business_id:
          type: string
          nullable: true
        customer_id:
          type: string
          nullable: true
        minimum_balance:
          $ref: '#/components/schemas/Money'
        auto_top_up_plan:
          $ref: '#/components/schemas/StripeWhatsAppCreditTopUpPlan'
        is_auto_top_up_enabled:
          type: boolean
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        _etag:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WabaBalanceAutoTopUpProfileDto:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        facebook_business_id:
          type: string
          nullable: true
        minimum_balance:
          $ref: '#/components/schemas/Money'
        auto_top_up_plan:
          $ref: '#/components/schemas/StripeWhatsAppCreditTopUpPlan'
        is_auto_top_up_enabled:
          type: boolean
      additionalProperties: false
    WabaBalanceDto:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        credit:
          $ref: '#/components/schemas/Money'
        all_time_usage:
          $ref: '#/components/schemas/Money'
        balance:
          $ref: '#/components/schemas/Money'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    WabaConversationInsertionException:
      type: object
      properties:
        last_conversation_usage_insert_timestamp:
          type: integer
          format: int64
      additionalProperties: false
    WabaConversationUsageTransactionItem:
      type: object
      properties:
        facebook_waba_id:
          type: string
          nullable: true
        start_timestamp:
          type: integer
          format: int64
        end_timestamp:
          type: integer
          format: int64
        granularity:
          type: string
          nullable: true
        business_initiated_paid_quantity:
          type: integer
          format: int32
        business_initiated_free_tier_quantity:
          type: integer
          format: int32
        business_initiated_cost:
          type: number
          format: decimal
        user_initiated_paid_quantity:
          type: integer
          format: int32
        user_initiated_free_tier_quantity:
          type: integer
          format: int32
        user_initiated_free_entry_point_quantity:
          type: integer
          format: int32
        user_initiated_cost:
          type: number
          format: decimal
        conversation_analytics_data_points:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappConversationAnalyticsResultDataPoint'
          nullable: true
        conversation_category_quantities:
          type: object
          additionalProperties:
            type: integer
            format: int32
            nullable: true
          nullable: true
        conversation_category_costs:
          type: object
          additionalProperties:
            type: number
            format: decimal
            nullable: true
          nullable: true
      additionalProperties: false
    WabaDataset:
      type: object
      properties:
        facebook_dataset_id:
          type: string
          nullable: true
        facebook_dataset_name:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    WabaDatasetDto:
      type: object
      properties:
        facebook_dataset_id:
          type: string
          nullable: true
        facebook_dataset_name:
          type: string
          nullable: true
      additionalProperties: false
    WabaDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        facebook_waba_id:
          type: string
          nullable: true
        sleekflow_company_ids:
          type: array
          items:
            type: string
          nullable: true
        facebook_business_id:
          type: string
          nullable: true
        facebook_waba_business_id:
          type: string
          nullable: true
        facebook_waba_business_name:
          type: string
          nullable: true
        facebook_waba_business_verification_status:
          type: string
          nullable: true
        facebook_waba_business_profile_picture_uri:
          type: string
          nullable: true
        facebook_waba_business_vertical:
          type: string
          nullable: true
        facebook_waba_name:
          type: string
          nullable: true
        facebook_waba_account_review_status:
          type: string
          nullable: true
        facebook_waba_message_template_namespace:
          type: string
          nullable: true
        waba_dto_phone_numbers:
          type: array
          items:
            $ref: '#/components/schemas/WabaPhoneNumberDto'
          nullable: true
        waba_product_catalog:
          $ref: '#/components/schemas/WabaProductCatalogDto'
        waba_dataset:
          $ref: '#/components/schemas/WabaDatasetDto'
        marketing_messages_lite_api_status:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    WabaDtoPhoneNumberQualityScore:
      type: object
      properties:
        reasons:
          type: array
          items:
            type: string
          nullable: true
        score:
          type: string
          nullable: true
        date:
          type: string
          nullable: true
      additionalProperties: false
    WabaLongLivedAccessToken:
      type: object
      properties:
        encrypted_token:
          type: string
          nullable: true
        token_type:
          type: string
          nullable: true
        expiry_datetime:
          type: string
          format: date-time
          nullable: true
        is_valid:
          type: boolean
          nullable: true
      additionalProperties: false
    WabaPhoneNumber:
      type: object
      properties:
        facebook_phone_number_id:
          type: string
          nullable: true
        facebook_phone_number_detail:
          $ref: '#/components/schemas/WhatsappPhoneNumberDetail'
        sleekflow_company_id:
          type: string
          nullable: true
        webhook_url:
          type: string
          nullable: true
        record_status:
          type: string
          nullable: true
        whatsapp_commerce_setting:
          $ref: '#/components/schemas/WhatsappCommerceSetting'
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WabaPhoneNumberDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        facebook_phone_number_id:
          type: string
          nullable: true
        facebook_phone_number:
          type: string
          nullable: true
        facebook_phone_number_verified_name:
          type: string
          nullable: true
        webhook_url:
          type: string
          nullable: true
        facebook_phone_number_quality_rating:
          type: string
          nullable: true
        facebook_phone_number_name_status:
          type: string
          nullable: true
        facebook_phone_number_new_name_status:
          type: string
          nullable: true
        facebook_phone_number_account_mode:
          type: string
          nullable: true
        facebook_phone_number_code_verification_status:
          type: string
          nullable: true
        facebook_phone_number_is_pin_enabled:
          type: boolean
          nullable: true
        facebook_phone_number_quality_score:
          $ref: '#/components/schemas/WabaDtoPhoneNumberQualityScore'
        facebook_phone_number_status:
          type: string
          nullable: true
        facebook_phone_number_is_official_business_account:
          type: string
          nullable: true
        facebook_phone_number_messaging_limit_tier:
          type: string
          nullable: true
        facebook_is_catalog_visible:
          type: boolean
          nullable: true
        facebook_is_cart_enabled:
          type: boolean
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    WabaProductCatalog:
      type: object
      properties:
        facebook_product_catalog_id:
          type: string
          nullable: true
        facebook_product_catalog_name:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        default_image_url:
          type: string
          nullable: true
        product_count:
          type: integer
          format: int32
          nullable: true
        vertical:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WabaProductCatalogDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        facebook_product_catalog_id:
          type: string
          nullable: true
        facebook_product_catalog_name:
          type: string
          nullable: true
        default_image_url:
          type: string
          nullable: true
        product_count:
          type: integer
          format: int32
          nullable: true
        vertical:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
          nullable: true
        updated_at:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    WabaTopUpTransactionItem:
      type: object
      properties:
        pay_amount:
          $ref: '#/components/schemas/Money'
        payment_method:
          type: string
          nullable: true
        stripe_top_up_credit_detail:
          $ref: '#/components/schemas/StripeTopUpCreditDetail'
        internal_top_up_credit_detail:
          $ref: '#/components/schemas/InternalTopUpCreditDetail'
      additionalProperties: false
    WebhookConfiguration:
      type: object
      properties:
        whatsapp_business_account:
          type: string
          nullable: true
        application:
          type: string
          nullable: true
      additionalProperties: false
    WhatsAppCommerceSettings:
      type: object
      properties:
        id:
          type: string
          nullable: true
        is_catalog_visible:
          type: boolean
        is_cart_enabled:
          type: boolean
      additionalProperties: false
    WhatsAppPhoneNumberCommerceSettings:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/WhatsAppCommerceSettings'
          nullable: true
      additionalProperties: false
    WhatsappBusinessApiData:
      type: object
      properties:
        whatsapp_business_api_data:
          $ref: '#/components/schemas/WhatsappBusinessApiDataResult'
        override_callback_uri:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappBusinessApiDataResult:
      type: object
      properties:
        link:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappBusinessEncryptionData:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappBusinessEncryptionObject'
          nullable: true
      additionalProperties: false
    WhatsappBusinessEncryptionObject:
      type: object
      properties:
        business_public_key:
          type: string
          nullable: true
        business_public_key_signature_status:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiActionMessageParameterObject:
      type: object
      properties:
        thumbnail_product_retailer_id:
          type: string
          nullable: true
        mode:
          type: string
          nullable: true
        flow_message_version:
          type: string
          nullable: true
        flow_token:
          type: string
          nullable: true
        flow_id:
          type: string
          nullable: true
        flow_cta:
          type: string
          nullable: true
        flow_action:
          type: string
          nullable: true
        flow_action_payload:
          $ref: '#/components/schemas/WhatsappCloudApiFlowActionPayloadObject'
      additionalProperties: false
    WhatsappCloudApiActionObject:
      type: object
      properties:
        button:
          type: string
          nullable: true
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiButtonObject'
          nullable: true
        sections:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiSectionObject'
          nullable: true
        catalog_id:
          type: string
          nullable: true
        product_retailer_id:
          type: string
          nullable: true
        parameters:
          $ref: '#/components/schemas/WhatsappCloudApiActionMessageParameterObject'
        name:
          type: string
          nullable: true
        mode:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiAddressObject:
      type: object
      properties:
        street:
          type: string
          nullable: true
        city:
          type: string
          nullable: true
        state:
          type: string
          nullable: true
        zip:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        country_code:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiBasicAuth:
      type: object
      properties:
        username:
          type: string
          nullable: true
        password:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiBearerAuth:
      type: object
      properties:
        bearer:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiButtonObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        reply:
          $ref: '#/components/schemas/WhatsappCloudApiReplyObject'
      additionalProperties: false
    WhatsappCloudApiConfigObject:
      type: object
      properties:
        basic:
          $ref: '#/components/schemas/WhatsappCloudApiBasicAuth'
        bearer:
          $ref: '#/components/schemas/WhatsappCloudApiBearerAuth'
      additionalProperties: false
    WhatsappCloudApiContactObject:
      type: object
      properties:
        addresses:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiAddressObject'
          nullable: true
        birthday:
          type: string
          nullable: true
        emails:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiEmailObject'
          nullable: true
        name:
          $ref: '#/components/schemas/WhatsappCloudApiNameObject'
        org:
          $ref: '#/components/schemas/WhatsappCloudApiOrgObject'
        ims:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiImsObject'
          nullable: true
        phones:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiPhoneObject'
          nullable: true
        urls:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiUrlObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiContextObject:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiConversationObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        expiration_timestamp:
          type: integer
          format: int64
          nullable: true
        biz_opaque_callback_data:
          type: string
          nullable: true
        origin:
          $ref: '#/components/schemas/WhatsappCloudApiOriginObject'
      additionalProperties: false
    WhatsappCloudApiConversationUsageAnalyticDto:
      type: object
      properties:
        total_business_initiated_paid_quantity:
          type: integer
          format: int32
        total_business_initiated_free_tier_quantity:
          type: integer
          format: int32
        total_user_initiated_paid_quantity:
          type: integer
          format: int32
        total_user_initiated_free_tier_quantity:
          type: integer
          format: int32
        total_user_initiated_free_entry_point_quantity:
          type: integer
          format: int32
        conversation_category_quantities:
          type: object
          additionalProperties:
            type: integer
            format: int32
            nullable: true
          nullable: true
        total_used:
          $ref: '#/components/schemas/Money'
        total_markup:
          $ref: '#/components/schemas/Money'
        total_transaction_handling_fee:
          $ref: '#/components/schemas/Money'
        granularity:
          type: string
          nullable: true
        start:
          type: string
          nullable: true
        end:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiCreateTemplateObject:
      type: object
      properties:
        category:
          type: string
          nullable: true
        sub_category:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        components:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateComponentObject'
          nullable: true
        allow_category_change:
          type: boolean
          nullable: true
        message_send_ttl_seconds:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    WhatsappCloudApiCurrencyObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
        code:
          type: string
          nullable: true
        amount_1000:
          type: integer
          format: int32
      additionalProperties: false
    WhatsappCloudApiDateTimeObject:
      type: object
      properties:
        fallback_value:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiDetailedConversationUsageAnalyticDto:
      type: object
      properties:
        granular_conversation_usage_analytics:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiGranularConversationUsageAnalyticDto'
          nullable: true
        total_business_initiated_paid_quantity:
          type: integer
          format: int32
        total_business_initiated_free_tier_quantity:
          type: integer
          format: int32
        total_user_initiated_paid_quantity:
          type: integer
          format: int32
        total_user_initiated_free_tier_quantity:
          type: integer
          format: int32
        total_user_initiated_free_entry_point_quantity:
          type: integer
          format: int32
        conversation_category_quantities:
          type: object
          additionalProperties:
            type: integer
            format: int32
            nullable: true
          nullable: true
        total_used:
          $ref: '#/components/schemas/Money'
        total_markup:
          $ref: '#/components/schemas/Money'
        total_transaction_handling_fee:
          $ref: '#/components/schemas/Money'
        granularity:
          type: string
          nullable: true
        start:
          type: string
          nullable: true
        end:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiEmailObject:
      type: object
      properties:
        email:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiFlowActionPayloadObject:
      type: object
      properties:
        screen:
          type: string
          nullable: true
        data:
          nullable: true
      additionalProperties: false
    WhatsappCloudApiGranularConversationUsageAnalyticDto:
      type: object
      properties:
        business_initiated_paid_quantity:
          type: integer
          format: int32
        business_initiated_free_tier_quantity:
          type: integer
          format: int32
        user_initiated_paid_quantity:
          type: integer
          format: int32
        user_initiated_free_tier_quantity:
          type: integer
          format: int32
        user_initiated_free_entry_point_quantity:
          type: integer
          format: int32
        conversation_category_quantities:
          type: object
          additionalProperties:
            type: integer
            format: int32
            nullable: true
          nullable: true
        used:
          $ref: '#/components/schemas/Money'
        markup:
          $ref: '#/components/schemas/Money'
        transaction_handling_fee:
          $ref: '#/components/schemas/Money'
        start:
          type: string
          nullable: true
        end:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiHeaderObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        video:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        image:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        document:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
      additionalProperties: false
    WhatsappCloudApiHsmObject:
      type: object
      properties:
        namespace:
          type: string
          nullable: true
        element_name:
          type: string
          nullable: true
        language:
          $ref: '#/components/schemas/WhatsappCloudApiLanguageObject'
        localizable_params:
          $ref: '#/components/schemas/WhatsappCloudApiLocalizableParamObject'
      additionalProperties: false
    WhatsappCloudApiImsObject:
      type: object
      properties:
        service:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiInteractiveObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        header:
          $ref: '#/components/schemas/WhatsappCloudApiHeaderObject'
        body:
          $ref: '#/components/schemas/WhatsappCloudApiTextBodyObject'
        footer:
          $ref: '#/components/schemas/WhatsappCloudApiTextFooterObject'
        action:
          $ref: '#/components/schemas/WhatsappCloudApiActionObject'
      additionalProperties: false
    WhatsappCloudApiLanguageObject:
      type: object
      properties:
        code:
          type: string
          nullable: true
        policy:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiLocalizableParamObject:
      type: object
      properties:
        default:
          type: string
          nullable: true
        currency:
          $ref: '#/components/schemas/WhatsappCloudApiCurrencyObject'
        date_time:
          $ref: '#/components/schemas/WhatsappCloudApiDateTimeObject'
      additionalProperties: false
    WhatsappCloudApiLocationObject:
      type: object
      properties:
        longitude:
          type: number
          format: double
        latitude:
          type: number
          format: double
        name:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiMediaObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        link:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        provider:
          $ref: '#/components/schemas/WhatsappCloudApiProviderObject'
      additionalProperties: false
    WhatsappCloudApiMessageIdentityObject:
      type: object
      properties:
        acknowledged:
          type: string
          nullable: true
        created_timestamp:
          type: integer
          format: int64
        hash:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiMessageObject:
      type: object
      properties:
        recipient_type:
          type: string
          nullable: true
        to:
          type: string
          nullable: true
        biz_opaque_callback_data:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        text:
          $ref: '#/components/schemas/WhatsappCloudApiTextObject'
        audio:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        document:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        image:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        sticker:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        video:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiContactObject'
          nullable: true
        location:
          $ref: '#/components/schemas/WhatsappCloudApiLocationObject'
        template:
          $ref: '#/components/schemas/WhatsappCloudApiTemplateMessageObject'
        hsm:
          $ref: '#/components/schemas/WhatsappCloudApiHsmObject'
        interactive:
          $ref: '#/components/schemas/WhatsappCloudApiInteractiveObject'
        status:
          type: string
          nullable: true
        messaging_product:
          type: string
          nullable: true
        context:
          $ref: '#/components/schemas/WhatsappCloudApiContextObject'
        reaction:
          $ref: '#/components/schemas/WhatsappCloudApiReactionObject'
      additionalProperties: false
    WhatsappCloudApiMetadataObject:
      type: object
      properties:
        android-app-store-link:
          type: string
          nullable: true
        emojis:
          type: array
          items:
            type: string
          nullable: true
        ios-app-store-link:
          type: string
          nullable: true
        is-first-party-sticker:
          type: integer
          format: int32
        sticker-pack-id:
          type: string
          nullable: true
        sticker-pack-name:
          type: string
          nullable: true
        sticker-pack-publisher:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiNameObject:
      type: object
      properties:
        formatted_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        middle_name:
          type: string
          nullable: true
        suffix:
          type: string
          nullable: true
        prefix:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiOrgObject:
      type: object
      properties:
        company:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        department:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiOriginObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiParameterActionObject:
      type: object
      properties:
        thumbnail_product_retailer_id:
          type: string
          nullable: true
        sections:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiSectionObject'
          nullable: true
        flow_token:
          type: string
          nullable: true
        flow_action_data:
          nullable: true
      additionalProperties: false
    WhatsappCloudApiParameterLimitedTimeOfferObject:
      type: object
      properties:
        expiration_time_ms:
          type: integer
          format: int64
      additionalProperties: false
    WhatsappCloudApiParameterObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        payload:
          type: string
          nullable: true
        image:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        audio:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        document:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        video:
          $ref: '#/components/schemas/WhatsappCloudApiMediaObject'
        location:
          $ref: '#/components/schemas/WhatsappCloudApiLocationObject'
        date_time:
          $ref: '#/components/schemas/WhatsappCloudApiDateTimeObject'
        reference_id:
          type: string
          nullable: true
        payment_type:
          type: string
          nullable: true
        payment_configuration:
          type: string
          nullable: true
        currency:
          type: string
          nullable: true
        total_amount:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderAmountObject'
        order:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderObject'
        action:
          $ref: '#/components/schemas/WhatsappCloudApiParameterActionObject'
        coupon_code:
          type: string
          nullable: true
        limited_time_offer:
          $ref: '#/components/schemas/WhatsappCloudApiParameterLimitedTimeOfferObject'
      additionalProperties: false
    WhatsappCloudApiParameterOrderAmountObject:
      type: object
      properties:
        value:
          type: integer
          format: int32
        offset:
          type: integer
          format: int32
        description:
          type: string
          nullable: true
        discount_program_name:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiParameterOrderExpirationObject:
      type: object
      properties:
        timestamp:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiParameterOrderItemObject:
      type: object
      properties:
        retailer_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        amount:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderAmountObject'
        quantity:
          type: integer
          format: int32
        sale_amount:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderAmountObject'
      additionalProperties: false
    WhatsappCloudApiParameterOrderObject:
      type: object
      properties:
        status:
          type: string
          nullable: true
        catalog_id:
          type: string
          nullable: true
        expiration:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderExpirationObject'
        items:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiParameterOrderItemObject'
          nullable: true
        subtotal:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderAmountObject'
        shipping:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderAmountObject'
        discount:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderAmountObject'
        tax:
          $ref: '#/components/schemas/WhatsappCloudApiParameterOrderAmountObject'
      additionalProperties: false
    WhatsappCloudApiPhoneObject:
      type: object
      properties:
        phone:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        wa_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiPricingObject:
      type: object
      properties:
        pricing_model:
          type: string
          nullable: true
        billable:
          type: boolean
          nullable: true
          deprecated: true
        category:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiProductObject:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiProfileObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiProviderObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        config:
          $ref: '#/components/schemas/WhatsappCloudApiConfigObject'
      additionalProperties: false
    WhatsappCloudApiReactionObject:
      type: object
      properties:
        message_id:
          type: string
          nullable: true
        emoji:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiReplyObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiRowsObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiSectionObject:
      type: object
      properties:
        title:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiProductObject'
          nullable: true
        rows:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiRowsObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiSendMessageContactResult:
      type: object
      properties:
        input:
          type: string
          nullable: true
        wa_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiSendMessageResult:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplate:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        components:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateComponentObject'
          nullable: true
        category:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        language:
          type: string
          nullable: true
        quality_score:
          $ref: '#/components/schemas/MessageTemplateQualityScore'
        rejected_reason:
          type: string
          nullable: true
        message_send_ttl_seconds:
          type: integer
          format: int64
          nullable: true
        sub_category:
          type: string
          nullable: true
        previous_category:
          type: string
          nullable: true
        last_updated_time:
          type: string
          format: date-time
          nullable: true
        cta_url_link_tracking_opted_out:
          type: boolean
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplateCardComponentsObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        format:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        example:
          nullable: true
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/TemplateButtonObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplateCardObject:
      type: object
      properties:
        components:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateCardComponentsObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplateComponentObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        format:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
        example:
          $ref: '#/components/schemas/ExampleObject'
        buttons:
          type: array
          items:
            $ref: '#/components/schemas/TemplateButtonObject'
          nullable: true
        cards:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateCardObject'
          nullable: true
        limited_time_offer:
          $ref: '#/components/schemas/LimitedTimeOffer'
        add_security_recommendation:
          type: boolean
          nullable: true
        code_expiration_minutes:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplateMessageCardComponentObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        sub_type:
          type: string
          nullable: true
        index:
          type: integer
          format: int32
          nullable: true
        parameters:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiParameterObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplateMessageCardObject:
      type: object
      properties:
        card_index:
          type: integer
          format: int32
        components:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateMessageCardComponentObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplateMessageComponentObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        sub_type:
          type: string
          nullable: true
        index:
          type: integer
          format: int32
          nullable: true
        parameters:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiParameterObject'
          nullable: true
        add_security_recommendation:
          type: boolean
          nullable: true
        code_expiration_minutes:
          type: integer
          format: int32
          nullable: true
        cards:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateMessageCardObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTemplateMessageObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        language:
          $ref: '#/components/schemas/WhatsappCloudApiLanguageObject'
        components:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiTemplateMessageComponentObject'
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTextBodyObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTextFooterObject:
      type: object
      properties:
        text:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiTextObject:
      type: object
      properties:
        body:
          type: string
          nullable: true
        preview_url:
          type: boolean
          nullable: true
      additionalProperties: false
    WhatsappCloudApiUpdateFlowMetadataObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        categories:
          type: array
          items:
            type: string
          nullable: true
        endpoint_uri:
          type: string
          nullable: true
        application_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiUrlObject:
      type: object
      properties:
        url:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWabaConversationUsageAnalytic:
      type: object
      properties:
        conversation_usage_analytic:
          $ref: '#/components/schemas/WhatsappCloudApiConversationUsageAnalyticDto'
        facebook_business_id:
          type: string
          nullable: true
        facebook_business_name:
          type: string
          nullable: true
        facebook_business_waba:
          $ref: '#/components/schemas/FacebookBusinessWabaDto'
      additionalProperties: false
    WhatsappCloudApiWebhookBanInfoObject:
      type: object
      properties:
        waba_ban_state:
          type: string
          nullable: true
        waba_ban_date:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookButtonReplyObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookContactObject:
      type: object
      properties:
        profile:
          $ref: '#/components/schemas/WhatsappCloudApiProfileObject'
        wa_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookContextObject:
      type: object
      properties:
        from:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        referred_product:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookReferredProductObject'
        mentions:
          type: array
          items:
            type: string
          nullable: true
        forwarded:
          type: boolean
        frequently_forwarded:
          type: boolean
      additionalProperties: false
    WhatsappCloudApiWebhookCustomData:
      type: object
      properties:
        currency:
          type: string
          nullable: true
        value:
          type: integer
          format: int32
      additionalProperties: false
    WhatsappCloudApiWebhookDisableInfoObject:
      type: object
      properties:
        disable_date:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookErrorObject:
      type: object
      properties:
        error_type:
          type: string
          nullable: true
        error_rate:
          type: number
          format: double
          nullable: true
        error_count:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookInteractiveReplyObject:
      type: object
      properties:
        type:
          type: string
          nullable: true
        button_reply:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookButtonReplyObject'
        list_reply:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookListReplyObject'
        nfm_reply:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookNfmReplyObject'
      additionalProperties: false
    WhatsappCloudApiWebhookListReplyObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookLocationObject:
      type: object
      properties:
        latitude:
          type: number
          format: double
        longitude:
          type: number
          format: double
        name:
          type: string
          nullable: true
        address:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookLockInfoObject:
      type: object
      properties:
        expiration:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookMediaObject:
      type: object
      properties:
        id:
          type: string
          nullable: true
        caption:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        metadata:
          $ref: '#/components/schemas/WhatsappCloudApiMetadataObject'
        mime_type:
          type: string
          nullable: true
        sha256:
          type: string
          nullable: true
        animated:
          type: boolean
          nullable: true
        voice:
          type: boolean
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookMessageDetectedOutcome:
      type: object
      properties:
        id:
          type: string
          nullable: true
        event_name:
          type: string
          nullable: true
        timestamp:
          type: integer
          format: int64
        ctwa_clid:
          type: string
          nullable: true
        custom_data:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookCustomData'
      additionalProperties: false
    WhatsappCloudApiWebhookMessageObject:
      type: object
      properties:
        context:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookContextObject'
        from:
          type: string
          nullable: true
        id:
          type: string
          nullable: true
        identity:
          $ref: '#/components/schemas/WhatsappCloudApiMessageIdentityObject'
        timestamp:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        text:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookTextObject'
        audio:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMediaObject'
        document:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMediaObject'
        image:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMediaObject'
        video:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMediaObject'
        voice:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMediaObject'
        sticker:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookMediaObject'
        location:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookLocationObject'
        contacts:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiContactObject'
          nullable: true
        reaction:
          $ref: '#/components/schemas/WhatsappCloudApiReactionObject'
        interactive:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookInteractiveReplyObject'
        button:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookTemplateButtonReplyObject'
        system:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookSystemObject'
        order:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookOrderObject'
        errors:
          type: array
          items:
            $ref: '#/components/schemas/CloudApiErrorObject'
          nullable: true
        referral:
          $ref: '#/components/schemas/WhatsappCloudApiWebhookReferralObject'
      additionalProperties: false
    WhatsappCloudApiWebhookMessageStatus:
      type: object
      properties:
        id:
          type: string
          nullable: true
        recipient_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        timestamp:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        biz_opaque_callback_data:
          type: string
          nullable: true
        errors:
          type: array
          items:
            $ref: '#/components/schemas/CloudApiErrorObject'
          nullable: true
        conversation:
          $ref: '#/components/schemas/WhatsappCloudApiConversationObject'
        pricing:
          $ref: '#/components/schemas/WhatsappCloudApiPricingObject'
        payment:
          $ref: '#/components/schemas/WhatsappCloudApiPricingObject'
      additionalProperties: false
    WhatsappCloudApiWebhookMetadataObject:
      type: object
      properties:
        display_phone_number:
          type: string
          nullable: true
        phone_number_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookNfmReplyObject:
      type: object
      properties:
        name:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        response_json:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookOnBusinessBalanceChangedMessage:
      type: object
      properties:
        business_balance:
          $ref: '#/components/schemas/BusinessBalanceDto'
      additionalProperties: false
    WhatsappCloudApiWebhookOrderObject:
      type: object
      properties:
        catalog_id:
          type: string
          nullable: true
        product_items:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappCloudApiWebhookProductItemsObject'
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookOtherInfoObject:
      type: object
      properties:
        title:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookPartnerClientCertificationInfoObject:
      type: object
      properties:
        client_business_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        rejection_reasons:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookProductItemsObject:
      type: object
      properties:
        product_retailer_id:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
        item_price:
          type: number
          format: decimal
        currency:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookReferralObject:
      type: object
      properties:
        source_url:
          type: string
          nullable: true
        source_type:
          type: string
          nullable: true
        source_id:
          type: string
          nullable: true
        headline:
          type: string
          nullable: true
        body:
          type: string
          nullable: true
        media_type:
          type: string
          nullable: true
        image_url:
          type: string
          nullable: true
        video_url:
          type: string
          nullable: true
        thumbnail_url:
          type: string
          nullable: true
        ctwa_clid:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookReferredProductObject:
      type: object
      properties:
        catalog_id:
          type: string
          nullable: true
        product_retailer_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters:
      type: object
      properties:
        facebook_business_id:
          type: string
          nullable: true
        facebook_phone_number_id:
          type: string
          nullable: true
        facebook_message_template_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookSystemObject:
      type: object
      properties:
        body:
          type: string
          nullable: true
        new_wa_id:
          type: string
          nullable: true
        identity:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookTemplateButtonReplyObject:
      type: object
      properties:
        payload:
          type: string
          nullable: true
        text:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookTextObject:
      type: object
      properties:
        body:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookTravisMessage:
      type: object
      properties:
        waba_id:
          type: string
          nullable: true
        waba_phone_number:
          type: string
          nullable: true
        value:
          $ref: '#/components/schemas/CloudApiWebhookValueObject'
      additionalProperties: false
    WhatsappCloudApiWebhookViolationInfoObject:
      type: object
      properties:
        violation_type:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookWabaInfoObject:
      type: object
      properties:
        waba_id:
          type: string
          nullable: true
        owner_business_id:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappCloudApiWebhookWabaRestrictionFieldDataObject:
      type: object
      properties:
        restriction_type:
          type: string
          nullable: true
        expiration:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    WhatsappCommerceSetting:
      type: object
      properties:
        facebook_whatsapp_commerce_setting_id:
          type: string
          nullable: true
        is_catalog_visible:
          type: boolean
        is_cart_enabled:
          type: boolean
      additionalProperties: false
    WhatsappConversationAnalyticsResultDataPoint:
      type: object
      properties:
        start:
          type: integer
          format: int64
        end:
          type: integer
          format: int64
        start_datetime_offset:
          type: string
          format: date-time
          readOnly: true
        end_datetime_offset:
          type: string
          format: date-time
          readOnly: true
        conversation:
          type: integer
          format: int32
        phone_number:
          type: string
          nullable: true
        country:
          type: string
          nullable: true
        conversation_type:
          type: string
          nullable: true
        conversation_direction:
          type: string
          nullable: true
        conversation_category:
          type: string
          nullable: true
        cost:
          type: number
          format: decimal
      additionalProperties: false
    WhatsappFlowAsset:
      type: object
      properties:
        name:
          type: string
          nullable: true
        asset_type:
          type: string
          nullable: true
        download_url:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappFlowAssetData:
      type: object
      properties:
        paging:
          $ref: '#/components/schemas/CursorBasedPaginationResult'
        data:
          type: array
          items:
            $ref: '#/components/schemas/WhatsappFlowAsset'
          nullable: true
      additionalProperties: false
    WhatsappPhoneNumberDetail:
      type: object
      properties:
        id:
          type: string
          nullable: true
        quality_rating:
          type: string
          nullable: true
        new_certificate:
          type: string
          nullable: true
        name_status:
          type: string
          nullable: true
        new_name_status:
          type: string
          nullable: true
        account_mode:
          type: string
          nullable: true
        certificate:
          type: string
          nullable: true
        code_verification_status:
          type: string
          nullable: true
        display_phone_number:
          type: string
          nullable: true
        verified_name:
          type: string
          nullable: true
        is_pin_enabled:
          type: boolean
          nullable: true
        is_official_business_account:
          type: boolean
          nullable: true
        messaging_limit_tier:
          type: string
          nullable: true
        quality_score:
          $ref: '#/components/schemas/WhatsappPhoneNumberQualityScore'
        status:
          type: string
          nullable: true
        whatsapp_commerce_settings:
          $ref: '#/components/schemas/WhatsAppPhoneNumberCommerceSettings'
        platform_type:
          type: string
          nullable: true
        webhook_configuration:
          $ref: '#/components/schemas/WebhookConfiguration'
        whatsapp_business_encryption:
          $ref: '#/components/schemas/WhatsappBusinessEncryptionData'
        search_visibility:
          type: string
          nullable: true
        throughput:
          $ref: '#/components/schemas/WhatsappPhoneNumberThroughput'
      additionalProperties: false
    WhatsappPhoneNumberQualityScore:
      type: object
      properties:
        date:
          type: integer
          format: int32
          nullable: true
        reasons:
          type: array
          items:
            type: string
          nullable: true
        score:
          type: string
          nullable: true
      additionalProperties: false
    WhatsappPhoneNumberThroughput:
      type: object
      properties:
        level:
          type: string
          nullable: true
      additionalProperties: false