﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Schemas;

[TriggerGroup(TriggerGroups.Schemas)]
public class GetSchemas : ITrigger<GetSchemas.GetSchemasInput, GetSchemas.GetSchemasOutput>
{
    private readonly ISchemaService _schemaService;

    public GetSchemas(
        ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public sealed class GetSchemasFilterGroup
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<SchemaQueryBuilder.SchemaFilter> Filters { get; set; }

        [JsonConstructor]
        public GetSchemasFilterGroup(
            List<SchemaQueryBuilder.SchemaFilter> filters)
        {
            Filters = filters;
        }
    }

    public class GetSchemasInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [Range(1, 200)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetSchemasFilterGroup> FilterGroups { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("sorts")]
        public List<SchemaQueryBuilder.SchemaSort> Sorts { get; set; }

        [JsonConstructor]
        public GetSchemasInput(
            string sleekflowCompanyId,
            int limit,
            List<GetSchemasFilterGroup> filterGroups,
            List<SchemaQueryBuilder.SchemaSort> sorts,
            string? continuationToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FilterGroups = filterGroups;
            Sorts = sorts;
            Limit = limit;
            ContinuationToken = continuationToken;
        }
    }

    public class GetSchemasOutput
    {
        [JsonProperty("schemas")]
        public List<SchemaDto> Schemas { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonConstructor]
        public GetSchemasOutput(
            List<SchemaDto> schemas,
            string? continuationToken,
            int count)
        {
            Schemas = schemas;
            ContinuationToken = continuationToken;
            Count = count;
        }
    }

    public async Task<GetSchemasOutput> F(GetSchemasInput getSchemasInput)
    {
        var filterGroups = new List<SchemaQueryBuilder.FilterGroup>()
            .Concat(
                getSchemasInput
                    .FilterGroups
                    .Select(
                        fg => new SchemaQueryBuilder.FilterGroup(
                            fg
                                .Filters
                                .Select(f => f)
                                .Cast<SchemaQueryBuilder.ISchemaFilter>()
                                .ToList())))
            .ToList();

        var queryDefinition = SchemaQueryBuilder.BuildQueryDef(
            new List<SchemaQueryBuilder.ISelect>(),
            filterGroups,
            getSchemasInput.Sorts,
            getSchemasInput.SleekflowCompanyId);

        var (schemas, nextContinuationToken) = await _schemaService.GetContinuationTokenizedSchemasAsync(
            queryDefinition,
            getSchemasInput.ContinuationToken,
            getSchemasInput.Limit);

        var schemaDtos = schemas
            .Select(s => new SchemaDto(s))
            .ToList();

        return new GetSchemasOutput(
            schemaDtos,
            nextContinuationToken,
            schemaDtos.Count);
    }
}