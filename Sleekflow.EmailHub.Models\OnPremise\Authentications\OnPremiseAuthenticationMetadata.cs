using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;

namespace Sleekflow.EmailHub.Models.OnPremise.Authentications;

public class OnPremiseAuthenticationMetadata : EmailAuthenticationMetadata
{
    [JsonProperty("protocol_type")]
    public string ProtocolType { get; set; }

    [<PERSON><PERSON><PERSON>roperty("server_name")]
    public string ServerName { get; set; }

    [<PERSON><PERSON><PERSON>roperty("port_number")]
    public int PortNumber { get; set; }

    [<PERSON>son<PERSON>roperty("username")]
    public string Username { get; set; }

    [<PERSON>son<PERSON>roperty("password")]
    public string Password { get; set; }

    [JsonConstructor]
    public OnPremiseAuthenticationMetadata(
        string protocolType,
        string serverName,
        int portNumber,
        string username,
        string password)
        : base(ProviderNames.OnPremise, ProviderNames.OnPremise)
    {
        ProtocolType = protocolType;
        ServerName = serverName;
        PortNumber = portNumber;
        Username = username;
        Password = password;
    }
}