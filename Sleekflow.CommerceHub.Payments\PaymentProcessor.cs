using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.Payments.Stripe;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Payments;

public record PaymentProcessorOption(string LanguageIsoCode, string ReturnToUrl)
{
    public string LanguageIsoCode { get; } = LanguageIsoCode;

    public string ReturnToUrl { get; } = ReturnToUrl;
}

public interface IPaymentProcessor
{
    Task<(string PaymentLink, ProcessPaymentContext PaymentProcessorContext)> ProcessPaymentAsync(
        Order order,
        PaymentProviderConfig paymentProviderConfig,
        PaymentProcessorOption paymentProcessorOption);

    Task<(string RefundId, ProcessRefundContext ProcessRefundContext)> ProcessRefundAsync(
        Order order,
        Payment payment,
        decimal amount,
        string reason,
        PaymentProviderConfig paymentProviderConfig);
}

public class PaymentProcessor : IPaymentProcessor, ISingletonService
{
    private readonly IStripePaymentProcessor _stripePaymentProcessor;

    public PaymentProcessor(
        IStripePaymentProcessor stripePaymentProcessor)
    {
        _stripePaymentProcessor = stripePaymentProcessor;
    }

    public async Task<(string PaymentLink, ProcessPaymentContext PaymentProcessorContext)> ProcessPaymentAsync(
        Order order,
        PaymentProviderConfig paymentProviderConfig,
        PaymentProcessorOption paymentProcessorOption)
    {
        if (paymentProviderConfig.PaymentProviderExternalConfig.ProviderName == "Stripe")
        {
            return await _stripePaymentProcessor.ProcessPaymentAsync(
                order,
                paymentProviderConfig,
                paymentProcessorOption);
        }

        throw new NotImplementedException();
    }

    public async Task<(string RefundId, ProcessRefundContext ProcessRefundContext)> ProcessRefundAsync(
        Order order,
        Payment payment,
        decimal amount,
        string reason,
        PaymentProviderConfig paymentProviderConfig)
    {
        if (paymentProviderConfig.PaymentProviderExternalConfig.ProviderName == "Stripe")
        {
            return await _stripePaymentProcessor.ProcessRefundAsync(
                order,
                payment,
                amount,
                reason,
                paymentProviderConfig);
        }

        throw new NotImplementedException();
    }
}