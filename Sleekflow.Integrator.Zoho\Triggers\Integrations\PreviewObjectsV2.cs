﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class PreviewObjectsV2 : ITrigger
{
    private readonly IZohoConnectionService _zohoConnectionService;
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;

    public PreviewObjectsV2(
        IZohoConnectionService zohoConnectionService,
        IZohoObjectService zohoObjectService,
        IZohoAuthenticationService zohoAuthenticationService)
    {
        _zohoConnectionService = zohoConnectionService;
        _zohoObjectService = zohoObjectService;
        _zohoAuthenticationService = zohoAuthenticationService;
    }

    public class PreviewObjectsV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public PreviewObjectsV2Input(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class PreviewObjectsV2Output
    {
        [JsonProperty("objects")]
        [Required]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonConstructor]
        public PreviewObjectsV2Output(
            List<Dictionary<string, object?>> objects)
        {
            Objects = objects;
        }
    }

    public async Task<PreviewObjectsV2Output> F(
        PreviewObjectsV2Input previewObjectsV2Input)
    {
        var connection = await _zohoConnectionService.GetByIdAsync(
            previewObjectsV2Input.ConnectionId,
            previewObjectsV2Input.SleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                previewObjectsV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (objects, _, _) = await _zohoObjectService.GetObjectsAsync(
            authentication,
            previewObjectsV2Input.EntityTypeName,
            previewObjectsV2Input.FilterGroups,
            previewObjectsV2Input.FieldFilters);

        return new PreviewObjectsV2Output(objects);
    }
}