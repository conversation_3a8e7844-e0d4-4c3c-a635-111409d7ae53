﻿using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class GetCustomObjectSchemaRequest
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("schema_id")]
    public string SchemaId { get; set; }

    [JsonConstructor]
    public GetCustomObjectSchemaRequest(
        string sleekflowCompanyId,
        string schemaId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemaId = schemaId;
    }
}