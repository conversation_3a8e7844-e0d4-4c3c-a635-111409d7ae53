using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.TicketingHub;

public class TicketingHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;

    public TicketingHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
    }

    public class TicketingHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public TicketingHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public TicketingHubDbOutput InitTicketingHubDb()
    {
        const string cosmosDbId = "ticketinghubdb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var containerParams = new ContainerParam[]
        {
            new (
                "ticket",
                "ticket",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "ticket_external_id_sequence",
                "ticket_external_id_sequence",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "ticket_priority",
                "ticket_priority",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "ticket_status",
                "ticket_status",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "ticket_type",
                "ticket_type",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "ticket_activity",
                "ticket_activity",
                new List<string>
                {
                    "/ticket_id"
                }
            ),
            new (
                "ticket_company_config",
                "ticket_company_config",
                new List<string>
                {
                    "/sleekflow_company_id"
                }
            ),
            new (
                "ticket_comment",
                "ticket_comment",
                new List<string>
                {
                    "/ticket_id"
                }
            ),
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new TicketingHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}