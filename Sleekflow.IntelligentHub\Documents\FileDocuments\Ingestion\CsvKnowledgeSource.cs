using System.Collections.Concurrent;
using System.Text;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.Google;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface ICsvKnowledgeSource : IKnowledgeSource
{
}

public class CsvKnowledgeSource : ICsvKnowledgeSource, IScopedService
{
    private readonly ILogger<CsvKnowledgeSource> _logger;
    private readonly Kernel _kernel;

    public CsvKnowledgeSource(
        ILogger<CsvKnowledgeSource> logger,
        Kernel kernel)
    {
        _logger = logger;
        _kernel = kernel;
    }

    public async Task<IKnowledgeSource.IngestionResult> Ingest(
        Stream blobStream,
        object? fileIngestionProgress)
    {
        // Read the entire stream content
        using var reader = new StreamReader(
            blobStream,
            Encoding.UTF8,
            true,
            1024);
        var csvContent = await reader.ReadToEndAsync();

        // Split into lines using string manipulation, handling different line endings
        var lines = csvContent.Split(
            new[]
            {
                "\r\n",
                "\r",
                "\n"
            },
            StringSplitOptions.None).ToList();

        // Remove potential empty line at the end if file ends with a newline
        if (lines.Count > 0 && string.IsNullOrWhiteSpace(lines[^1]))
        {
            lines.RemoveAt(lines.Count - 1);
        }

        if (lines.Count == 0)
        {
            _logger.LogWarning("CSV stream is empty or contains no processable lines.");
            return new IKnowledgeSource.IngestionResult(
                [],
                new CsvFileIngestionProgress(0, 0)); // Return empty result if no content
        }

        var headerRow = lines[0];
        var chunkSize = CsvFileIngestionProgress.ChunkSize;
        var chunks = lines.Skip(1).Chunk(chunkSize).ToArray();
        var chunkCount = chunks.Length;

        _logger.LogInformation("Processing {LineCount} lines from CSV stream.", lines.Count);
        _logger.LogInformation(
            "Split CSV data into {ChunkCount} chunks of size {ChunkSize}.",
            chunkCount,
            chunkSize);

        // Initialize or use existing progress state
        CsvFileIngestionProgress csvFileIngestionProgress;

        switch (fileIngestionProgress)
        {
            case null:
                // Initialize a new progress object if none is provided
                csvFileIngestionProgress = new CsvFileIngestionProgress(0, chunkCount);
                break;
            case CsvFileIngestionProgress progress:
                // Direct instance of CsvFileIngestionProgress
                csvFileIngestionProgress = progress;
                break;
            default:
                try
                {
                    // Try to deserialize as JSON
                    var jsonString = JsonConvert.SerializeObject(fileIngestionProgress);
                    csvFileIngestionProgress = JsonConvert.DeserializeObject<CsvFileIngestionProgress>(jsonString)
                                               ?? throw new Exception(
                                                   "Failed to deserialize fileIngestionProgress to CsvFileIngestionProgress");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to interpret fileIngestionProgress as CsvFileIngestionProgress");
                    throw new Exception(
                        "FileIngestionProgress could not be interpreted as CsvFileIngestionProgress",
                        ex);
                }

                break;
        }

        // Process only chunks that haven't been processed yet
        var startChunkIndex = csvFileIngestionProgress.ProcessedChunks;

        // Process at most BatchSize chunks in this iteration
        var endChunkIndex = Math.Min(startChunkIndex + CsvFileIngestionProgress.BatchSize - 1, chunkCount - 1);
        var batchChunks = chunks.Skip(startChunkIndex).Take(endChunkIndex - startChunkIndex + 1).ToArray();

        _logger.LogInformation(
            "Processing chunks {StartChunk} to {EndChunk} out of {TotalChunks}",
            startChunkIndex + 1,
            endChunkIndex + 1,
            chunkCount);

        var parallelOptions = new ParallelOptions
        {
            MaxDegreeOfParallelism = CsvFileIngestionProgress.BatchSize
        };
        var chunkResults = new ConcurrentDictionary<int, string>();

        await Parallel.ForEachAsync(
            batchChunks.Select((chunk, index) => (chunk, index + startChunkIndex)),
            parallelOptions,
            async (item, cancellationToken) =>
            {
                var (csvChunkBody, index) = item;
                try
                {
                    _logger.LogInformation("Processing chunk {ChunkIndex}/{ChunkCount}.", index + 1, chunkCount);

                    var fullCsvChunk = headerRow + Environment.NewLine + string.Join(Environment.NewLine, csvChunkBody);
                    var csvMarkdown = await ConvertCsvToMarkdown(fullCsvChunk);
                    chunkResults.TryAdd(index, csvMarkdown);

                    _logger.LogInformation("Finished processing chunk {ChunkIndex}.", index + 1);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing CSV chunk {ChunkIndex}: {Message}", index + 1, ex.Message);
                    throw;
                }
            });

        // Aggregate results in order
        var markdownBuilder = new StringBuilder();
        foreach (var key in chunkResults.Keys.OrderBy(k => k))
        {
            markdownBuilder.AppendLine(chunkResults[key]);
        }

        var finalMarkdown = markdownBuilder.ToString();

        _logger.LogInformation("Finished processing batch of CSV chunks.");

        // Update progress
        csvFileIngestionProgress.ProcessedChunks = endChunkIndex + 1;

        return new IKnowledgeSource.IngestionResult([finalMarkdown], csvFileIngestionProgress);
    }

    private async Task<string> ConvertCsvToMarkdown(string csvContent)
    {
#pragma warning disable SKEXP0070
#pragma warning disable SKEXP0001
        var chatCompletionService =
            _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_FLASH);

        var chatHistory = new ChatHistory();
        chatHistory.AddSystemMessage(
            """
            You are a specialized CSV content transformer, expertly skilled at converting CSV data into meaningful markdown text.

            Each CSV document is provided as a string with comma-separated values, where each line represents a row.

            When processing CSV data, you will:

            1. Content Extraction and Organization:
            - Analyze the CSV structure to identify headers and data rows
            - Convert the tabular data into natural sentences while preserving all information and context
            - Maintain the logical flow and relationships between different data points
            - Ensure that the meaning of the entire table is preserved in the narrative form

            2. Formatting and Structure:
            - Transform complex data presentations into clear and accurate textual descriptions
            - Maintain logical progression between different parts of the data
            - Use appropriate markdown formatting to enhance readability

            3. Special Considerations:
            - Preserve exact numerical data, measurements, and specific values
            - Identify and explain relationships between columns when relevant
            - Interpret the data contextually to provide a coherent narrative

            4. Output Quality:
            - Preserve exact spelling and formatting of proper nouns, technical terms, and specialized vocabulary
            - Maintain professional language and formal tone
            - Structure information in a way that enhances readability while maintaining completeness

            5. Validation Checks:
            - Verify that the markdown contains all information from the CSV
            - Ensure no data points are omitted or misrepresented
            - Confirm that relationships between different pieces of information are maintained

            Output language:
            - The markdown should use the same language as the CSV content.

            The final output should be a complete, accurate representation of the original CSV data in markdown format that preserves the meaning and context of the tabular data.
            Answer right away, do not say things like: "Here is the converted markdown:"
            """);
        chatHistory.AddUserMessage(
        [
            new Microsoft.SemanticKernel.TextContent(csvContent)
        ]);

        var completeOutput = await chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            new GeminiPromptExecutionSettings()
            {
                Temperature = 0.1f,
            },
            _kernel);

        // _logger.LogInformation(
        //     $"CSV conversion - Input tokens: {completeChatAsync.Usage?.InputTokenCount}, Output tokens: {completeChatAsync.Usage?.OutputTokenCount}");

        var content = completeOutput.Content;
        if (content == null)
        {
            throw new Exception("CSV to markdown conversion failed.");
        }

        return content;
#pragma warning restore SKEXP0070
#pragma warning restore SKEXP0001
    }
}