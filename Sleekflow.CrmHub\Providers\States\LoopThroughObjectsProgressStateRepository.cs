﻿using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Providers.States;

public interface ILoopThroughObjectsProgressStateRepository : IRepository<LoopThroughObjectsProgressState>
{
}

public class LoopThroughObjectsProgressStateRepository
    : BaseRepository<LoopThroughObjectsProgressState>,
        ILoopThroughObjectsProgressStateRepository,
        ISingletonService
{
    public LoopThroughObjectsProgressStateRepository(
        ILogger<BaseRepository<LoopThroughObjectsProgressState>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}