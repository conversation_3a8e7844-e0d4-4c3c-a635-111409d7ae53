using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Triggers.Subscriptions;

[TriggerGroup(ControllerNames.Subscriptions)]
public class UnsubscribeProviderEmailAddress : ITrigger
{
    private readonly IEmailProviderSelector _providerSelector;

    public UnsubscribeProviderEmailAddress(IEmailProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class UnsubscribeProviderEmailAddressInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email_address")]
        [Required]
        public string EmailAddress { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public UnsubscribeProviderEmailAddressInput(string sleekflowCompanyId, string emailAddress, string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EmailAddress = emailAddress;
            ProviderName = providerName;
        }
    }

    public class UnsubscribeProviderEmailAddressOutput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("email_address")]
        [Required]
        public string EmailAddress { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public UnsubscribeProviderEmailAddressOutput(
            string sleekflowCompanyId,
            string providerName,
            string emailAddress)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EmailAddress = emailAddress;
            ProviderName = providerName;
        }
    }

    public async Task<UnsubscribeProviderEmailAddressOutput> F(
        UnsubscribeProviderEmailAddressInput unsubscribeProviderEmailAddressInput)
    {
        var emailProvider = _providerSelector.GetEmailProvider(unsubscribeProviderEmailAddressInput.ProviderName);
        var subscribeProviderOutput = await emailProvider.UnsubscribeProviderAsync(
            unsubscribeProviderEmailAddressInput.SleekflowCompanyId,
            unsubscribeProviderEmailAddressInput.EmailAddress);

        return new UnsubscribeProviderEmailAddressOutput(
            subscribeProviderOutput.SleekflowCompanyId,
            subscribeProviderOutput.ProviderName,
            subscribeProviderOutput.EmailAddress);
    }
}