using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Blobs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DurablePayloads;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.CustomCatalogs;

[ContainerId(ContainerNames.CustomCatalogFile)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class CustomCatalogFile : AuditEntity, IHasRecordStatuses
{
    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty("blob")]
    public Blob Blob { get; set; }

    [JsonProperty("file_process_status")]
    public string FileProcessStatus { get; set; }

    [JsonProperty("durable_payload")]
    public DurablePayload DurablePayload { get; set; }

    [JsonProperty("durable_runtime_status")]
    public string DurableRuntimeStatus { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonConstructor]
    public CustomCatalogFile(
        string storeId,
        Blob blob,
        string fileProcessStatus,
        DurablePayload durablePayload,
        string durableRuntimeStatus,
        List<string> recordStatuses,
        string id,
        string sleekflowCompanyId,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(
            id,
            SysTypeNames.CustomCatalogFile,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        StoreId = storeId;
        Blob = blob;
        FileProcessStatus = fileProcessStatus;
        DurablePayload = durablePayload;
        DurableRuntimeStatus = durableRuntimeStatus;
        RecordStatuses = recordStatuses;
    }
}