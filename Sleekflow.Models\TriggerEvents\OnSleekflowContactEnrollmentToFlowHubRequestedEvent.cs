using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class OnSleekflowContactEnrollmentToFlowHubRequestedEvent
{
    public DateTimeOffset CreatedAt { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string ContactId { get; set; }

    public Dictionary<string, object?> Contact { get; set; }

    public string FlowHubWorkflowId { get; set; }

    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnSleekflowContactEnrollmentToFlowHubRequestedEvent(
        DateTimeOffset createdAt,
        string sleekflowCompanyId,
        string contactId,
        Dictionary<string, object?> contact,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        CreatedAt = createdAt;
        SleekflowCompanyId = sleekflowCompanyId;
        ContactId = contactId;
        Contact = contact;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
    }
}