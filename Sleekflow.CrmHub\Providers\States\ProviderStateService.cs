﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Providers.Models;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.Providers.States;

public interface IProviderStateService
{
    Task<SyncObjectsProgressState?> GetStateAsync<T>(
        string id,
        string providerName,
        string sleekflowCompanyId);

    Task PatchSyncObjectsProgressStateAsync(
        string id,
        string sleekflowCompanyId,
        StatusQueryGetOutput<StatusQueryGetOutputInput, StatusQueryGetOutputCustomStatus, StatusQueryGetOutputOutput>
            statusQueryGetOutput);

    Task<string> CreateSyncObjectsProgressStateAsync(
        DurablePayload durablePayload,
        string providerName,
        string sleekflowCompanyId);
}

public class ProviderStateService : ISingletonService, IProviderStateService
{
    private readonly IIdService _idService;
    private readonly ISyncObjectsProgressStateRepository _syncObjectsProgressStateRepository;

    public ProviderStateService(
        IIdService idService,
        ISyncObjectsProgressStateRepository syncObjectsProgressStateRepository)
    {
        _idService = idService;
        _syncObjectsProgressStateRepository = syncObjectsProgressStateRepository;
    }

    public async Task<SyncObjectsProgressState?> GetStateAsync<T>(
        string id,
        string providerName,
        string sleekflowCompanyId)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE c.id = @id AND c.provider_name = @providerName AND c.sleekflow_company_id = @sleekflowCompanyId ")
                .WithParameter("@id", id)
                .WithParameter("@providerName", providerName)
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId);

        var objects = await _syncObjectsProgressStateRepository.GetObjectsAsync(queryDefinition);
        if (objects.Count == 0)
        {
            return default;
        }

        return objects[0];
    }

    public async Task PatchSyncObjectsProgressStateAsync(
        string id,
        string sleekflowCompanyId,
        StatusQueryGetOutput<
                StatusQueryGetOutputInput,
                StatusQueryGetOutputCustomStatus,
                StatusQueryGetOutputOutput>
            statusQueryGetOutput)
    {
        await _syncObjectsProgressStateRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace(
                    $"/{SyncObjectsProgressState.PropertyNameStateObj}/query_output",
                    statusQueryGetOutput)
            });
    }

    public async Task<string> CreateSyncObjectsProgressStateAsync(
        DurablePayload durablePayload,
        string providerName,
        string sleekflowCompanyId)
    {
        var providerState = new SyncObjectsProgressState(
            _idService.GetId("SyncObjectsProgressState"),
            stateName: "SyncObjectsProgressState",
            providerName,
            new SyncObjectsProgressState.SyncObjectsProgressStateObj(durablePayload, queryOutput: null),
            sleekflowCompanyId);

        providerState = await _syncObjectsProgressStateRepository.CreateAndGetAsync(providerState, sleekflowCompanyId);

        return providerState.Id;
    }
}