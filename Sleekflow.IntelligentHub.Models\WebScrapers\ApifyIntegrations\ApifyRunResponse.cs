﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifyRunResponse
{
    [JsonProperty(PropertyName = "data")]
    public ApifyRunResponseData Data { get; set; }

    [JsonConstructor]
    public ApifyRunResponse(ApifyRunResponseData data)
    {
        Data = data;
    }
}

public class ApifyRunResponseData
{
    [JsonProperty(PropertyName = "id")]
    public string ApifyRunId { get; set; }

    [JsonProperty(PropertyName = "actId")]
    public string ActId { get; set; }

    [JsonProperty(PropertyName = "actorTaskId")]
    public string ActorTaskId { get; set; }

    [JsonProperty(PropertyName = "startedAt")]
    public DateTime? StartedAt { get; set; }

    [JsonProperty(PropertyName = "finishedAt")]
    public DateTime? FinishedAt { get; set; }

    [JsonProperty(PropertyName = "status")]
    public string Status { get; set; }

    [JsonProperty(PropertyName = "statusMessage")]
    public string? StatusMessage { get; set; }

    [JsonProperty(PropertyName = "defaultKeyValueStoreId")]
    public string DefaultKeyValueStoreId { get; set; }

    [JsonProperty(PropertyName = "defaultDatasetId")]
    public string DefaultDatasetId { get; set; }

    [JsonProperty(PropertyName = "defaultRequestQueueId")]
    public string DefaultRequestQueueId { get; set; }

    [JsonConstructor]
    public ApifyRunResponseData(
        string apifyRunId,
        string actId,
        DateTime? startedAt,
        DateTime? finishedAt,
        string status,
        string? statusMessage,
        string defaultKeyValueStoreId,
        string defaultDatasetId,
        string defaultRequestQueueId,
        string actorTaskId)
    {
        ApifyRunId = apifyRunId;
        ActId = actId;
        StartedAt = startedAt;
        FinishedAt = finishedAt;
        Status = status;
        StatusMessage = statusMessage;
        DefaultKeyValueStoreId = defaultKeyValueStoreId;
        DefaultDatasetId = defaultDatasetId;
        DefaultRequestQueueId = defaultRequestQueueId;
        ActorTaskId = actorTaskId;
    }
}