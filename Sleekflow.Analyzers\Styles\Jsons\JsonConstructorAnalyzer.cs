using System;
using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class JsonConstructorAnalyzer : DiagnosticAnalyzer
{
    public const string MissingJsonConstructorDiagnosticId = "JA1002";
    public const string Category = "Usage";

    private static readonly LocalizableString MissingJsonConstructorTitle =
        "Constructor should have JsonConstructor attribute";

    private static readonly LocalizableString MissingJsonConstructorMessageFormat =
        "Constructor should have Json<PERSON>onstructor attribute";

    private static readonly DiagnosticDescriptor MissingJsonConstructorRule = new DiagnosticDescriptor(
        MissingJsonConstructorDiagnosticId,
        MissingJsonConstructorTitle,
        MissingJsonConstructorMessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics =>
        ImmutableArray.Create(MissingJsonConstructorRule);

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSyntaxNodeAction(AnalyzeConstructorDeclaration, SyntaxKind.ConstructorDeclaration);
    }

    private void AnalyzeConstructorDeclaration(SyntaxNodeAnalysisContext context)
    {
        var constructorDeclaration = (ConstructorDeclarationSyntax) context.Node;
        var classDeclaration = constructorDeclaration.Parent as ClassDeclarationSyntax;
        if (classDeclaration == null)
        {
            return;
        }

        var className = classDeclaration.Identifier.Text;
        var namespaceName = TypeSymbolUtils.GetNamespace(classDeclaration);

        if (className.EndsWith("Event")
            || className.EndsWith("Request")
            || className.EndsWith("Reply"))
        {
            return;
        }

        if (!className.EndsWith("Input", StringComparison.Ordinal)
            && !className.EndsWith("Output", StringComparison.Ordinal)
            && !namespaceName.Contains("Models"))
        {
            return;
        }

        // Check if there is already a constructor with JsonConstructor attribute
        var hasJsonConstructorInClass = classDeclaration.Members.OfType<ConstructorDeclarationSyntax>().Any(
            c =>
                c.AttributeLists
                    .SelectMany(list => list.Attributes)
                    .Any(a => TypeSymbolUtils.IsJsonConstructorAttribute(context, a)));
        if (hasJsonConstructorInClass)
        {
            return;
        }

        var hasJsonConstructorAttribute = constructorDeclaration.AttributeLists
            .SelectMany(list => list.Attributes)
            .Any(a => TypeSymbolUtils.IsJsonConstructorAttribute(context, a));
        if (!hasJsonConstructorAttribute)
        {
            var diagnostic = Diagnostic.Create(
                MissingJsonConstructorRule,
                constructorDeclaration.Identifier.GetLocation());
            context.ReportDiagnostic(diagnostic);
        }
    }
}