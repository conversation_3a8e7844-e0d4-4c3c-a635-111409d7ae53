using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals;

[method: JsonConstructor]
public class ScoringOutput(RawScores rawScores, QualitativeAnalysis qualitativeAnalysis)
{
    [JsonProperty("raw_scores")]
    public RawScores RawScores { get; set; } = rawScores;

    [JsonProperty("qualitative_analysis")]
    public QualitativeAnalysis QualitativeAnalysis { get; set; } = qualitativeAnalysis;
}

[method: JsonConstructor]
public class RawScores(
    EmotionalIntelligenceScores emotionalIntelligence,
    AidaFrameworkScores aidaFramework,
    PenaltyInfo penalties)
{
    [JsonProperty("emotional_intelligence")]
    public EmotionalIntelligenceScores EmotionalIntelligence { get; set; } = emotionalIntelligence;

    [JsonProperty("aida_framework")]
    public AidaFrameworkScores AidaFramework { get; set; } = aidaFramework;

    [JsonProperty("penalties")]
    public PenaltyInfo Penalties { get; set; } = penalties;
}

[method: JsonConstructor]
public class EmotionalIntelligenceScores(
    ComponentScore empathy,
    ComponentScore authenticity,
    ComponentScore trustBuilding,
    ComponentScore activeListening,
    ComponentScore engagement,
    ComponentScore personality)
{
    [JsonProperty("empathy")]
    public ComponentScore Empathy { get; set; } = empathy;

    [JsonProperty("authenticity")]
    public ComponentScore Authenticity { get; set; } = authenticity;

    [JsonProperty("trust_building")]
    public ComponentScore TrustBuilding { get; set; } = trustBuilding;

    [JsonProperty("active_listening")]
    public ComponentScore ActiveListening { get; set; } = activeListening;

    [JsonProperty("engagement")]
    public ComponentScore Engagement { get; set; } = engagement;

    [JsonProperty("personality")]
    public ComponentScore Personality { get; set; } = personality;
}

[method: JsonConstructor]
public class AidaFrameworkScores(
    ComponentScore awareness,
    ComponentScore interest,
    ComponentScore desire,
    ComponentScore action)
{
    [JsonProperty("awareness")]
    public ComponentScore Awareness { get; set; } = awareness;

    [JsonProperty("interest")]
    public ComponentScore Interest { get; set; } = interest;

    [JsonProperty("desire")]
    public ComponentScore Desire { get; set; } = desire;

    [JsonProperty("action")]
    public ComponentScore Action { get; set; } = action;
}

[method: JsonConstructor]
public class ComponentScore(int score, double weight)
{
    [JsonProperty("score")]
    public int Score { get; set; } = score;

    [JsonProperty("weight")]
    public double Weight { get; set; } = weight;
}

[method: JsonConstructor]
public class PenaltyInfo(List<string> items, double deductionPerItem)
{
    [JsonProperty("items")]
    public List<string> Items { get; set; } = items;

    [JsonProperty("deduction_per_item")]
    public double DeductionPerItem { get; set; } = deductionPerItem;
}

[method: JsonConstructor]
public class QualitativeAnalysis(
    ScenarioContext scenarioContext,
    EmotionalIntelligenceAnalysis emotionalIntelligenceAnalysis,
    ValueBuildingAnalysis valueBuildingAnalysis,
    ConsultationQuality consultationQuality,
    ResponseStrengthAnalysis responseStrengthAnalysis,
    DetailedFeedback detailedFeedback)
{
    [JsonProperty("scenario_context")]
    public ScenarioContext ScenarioContext { get; set; } = scenarioContext;

    [JsonProperty("emotional_intelligence_analysis")]
    public EmotionalIntelligenceAnalysis EmotionalIntelligenceAnalysis { get; set; } = emotionalIntelligenceAnalysis;

    [JsonProperty("value_building_analysis")]
    public ValueBuildingAnalysis ValueBuildingAnalysis { get; set; } = valueBuildingAnalysis;

    [JsonProperty("consultation_quality")]
    public ConsultationQuality ConsultationQuality { get; set; } = consultationQuality;

    [JsonProperty("response_strength_analysis")]
    public ResponseStrengthAnalysis ResponseStrengthAnalysis { get; set; } = responseStrengthAnalysis;

    [JsonProperty("detailed_feedback")]
    public DetailedFeedback DetailedFeedback { get; set; } = detailedFeedback;
}

[method: JsonConstructor]
public class ScenarioContext(string scenario, string customerConcern)
{
    [JsonProperty("scenario")]
    public string Scenario { get; set; } = scenario;

    [JsonProperty("customer_concern")]
    public string CustomerConcern { get; set; } = customerConcern;
}

[method: JsonConstructor]
public class EmotionalIntelligenceAnalysis(
    EmpathyAnalysis empathy,
    AuthenticityAnalysis authenticity,
    TrustBuildingAnalysis trustBuilding,
    ActiveListeningAnalysis activeListening,
    EngagementAnalysis engagement,
    PersonalityAnalysis personality)
{
    [JsonProperty("empathy")]
    public EmpathyAnalysis Empathy { get; set; } = empathy;

    [JsonProperty("authenticity")]
    public AuthenticityAnalysis Authenticity { get; set; } = authenticity;

    [JsonProperty("trust_building")]
    public TrustBuildingAnalysis TrustBuilding { get; set; } = trustBuilding;

    [JsonProperty("active_listening")]
    public ActiveListeningAnalysis ActiveListening { get; set; } = activeListening;

    [JsonProperty("engagement")]
    public EngagementAnalysis Engagement { get; set; } = engagement;

    [JsonProperty("personality")]
    public PersonalityAnalysis Personality { get; set; } = personality;
}

[method: JsonConstructor]
public class EmpathyAnalysis(
    List<string> observations,
    List<string> examplesFromText,
    List<string> improvementSuggestions)
{
    [JsonProperty("observations")]
    public List<string> Observations { get; set; } = observations;

    [JsonProperty("examples_from_text")]
    public List<string> ExamplesFromText { get; set; } = examplesFromText;

    [JsonProperty("improvement_suggestions")]
    public List<string> ImprovementSuggestions { get; set; } = improvementSuggestions;
}

[method: JsonConstructor]
public class AuthenticityAnalysis(
    List<string> naturalElements,
    List<string> areasOfImprovement,
    List<string> specificExamples)
{
    [JsonProperty("natural_elements")]
    public List<string> NaturalElements { get; set; } = naturalElements;

    [JsonProperty("areas_of_improvement")]
    public List<string> AreasOfImprovement { get; set; } = areasOfImprovement;

    [JsonProperty("specific_examples")]
    public List<string> SpecificExamples { get; set; } = specificExamples;
}

[method: JsonConstructor]
public class TrustBuildingAnalysis(
    List<string> trustElements,
    List<string> missedOpportunities,
    List<string> improvementAreas)
{
    [JsonProperty("trust_elements")]
    public List<string> TrustElements { get; set; } = trustElements;

    [JsonProperty("missed_opportunities")]
    public List<string> MissedOpportunities { get; set; } = missedOpportunities;

    [JsonProperty("improvement_areas")]
    public List<string> ImprovementAreas { get; set; } = improvementAreas;
}

[method: JsonConstructor]
public class ActiveListeningAnalysis(List<string> evidence, List<string> missedPoints)
{
    [JsonProperty("evidence")]
    public List<string> Evidence { get; set; } = evidence;

    [JsonProperty("missed_points")]
    public List<string> MissedPoints { get; set; } = missedPoints;
}

[method: JsonConstructor]
public class EngagementAnalysis(List<string> techniquesUsed, List<string> improvementSuggestions)
{
    [JsonProperty("techniques_used")]
    public List<string> TechniquesUsed { get; set; } = techniquesUsed;

    [JsonProperty("improvement_suggestions")]
    public List<string> ImprovementSuggestions { get; set; } = improvementSuggestions;
}

[method: JsonConstructor]
public class PersonalityAnalysis(string toneAnalysis, List<string> styleObservations)
{
    [JsonProperty("tone_analysis")]
    public string ToneAnalysis { get; set; } = toneAnalysis;

    [JsonProperty("style_observations")]
    public List<string> StyleObservations { get; set; } = styleObservations;
}

[method: JsonConstructor]
public class ValueBuildingAnalysis(
    ConcernAcknowledgment concernAcknowledgment,
    ValueTransition valueTransition,
    BenefitExplanation benefitExplanation,
    List<string> notablePhrases)
{
    [JsonProperty("concern_acknowledgment")]
    public ConcernAcknowledgment ConcernAcknowledgment { get; set; } = concernAcknowledgment;

    [JsonProperty("value_transition")]
    public ValueTransition ValueTransition { get; set; } = valueTransition;

    [JsonProperty("benefit_explanation")]
    public BenefitExplanation BenefitExplanation { get; set; } = benefitExplanation;

    [JsonProperty("notable_phrases")]
    public List<string> NotablePhrases { get; set; } = notablePhrases;
}

[method: JsonConstructor]
public class ConcernAcknowledgment(string text, string effectiveness)
{
    [JsonProperty("text")]
    public string Text { get; set; } = text;

    [JsonProperty("effectiveness")]
    public string Effectiveness { get; set; } = effectiveness;
}

[method: JsonConstructor]
public class ValueTransition(string approach, string effectiveness)
{
    [JsonProperty("approach")]
    public string Approach { get; set; } = approach;

    [JsonProperty("effectiveness")]
    public string Effectiveness { get; set; } = effectiveness;
}

[method: JsonConstructor]
public class BenefitExplanation(List<string> keyPoints, string contextRelevance)
{
    [JsonProperty("key_points")]
    public List<string> KeyPoints { get; set; } = keyPoints;

    [JsonProperty("context_relevance")]
    public string ContextRelevance { get; set; } = contextRelevance;
}

[method: JsonConstructor]
public class ConsultationQuality(
    List<string> consultativeElements,
    List<string> salesElements,
    string balanceAnalysis)
{
    [JsonProperty("consultative_elements")]
    public List<string> ConsultativeElements { get; set; } = consultativeElements;

    [JsonProperty("sales_elements")]
    public List<string> SalesElements { get; set; } = salesElements;

    [JsonProperty("balance_analysis")]
    public string BalanceAnalysis { get; set; } = balanceAnalysis;
}

[method: JsonConstructor]
public class ResponseStrengthAnalysis(
    List<string> keyStrengths,
    List<string> primaryIssues,
    List<string> improvementRecommendations)
{
    [JsonProperty("key_strengths")]
    public List<string> KeyStrengths { get; set; } = keyStrengths;

    [JsonProperty("primary_issues")]
    public List<string> PrimaryIssues { get; set; } = primaryIssues;

    [JsonProperty("improvement_recommendations")]
    public List<string> ImprovementRecommendations { get; set; } = improvementRecommendations;
}

[method: JsonConstructor]
public class DetailedFeedback(
    List<string> positiveElements,
    List<string> areasForImprovement,
    List<string> actionableSuggestions)
{
    [JsonProperty("positive_elements")]
    public List<string> PositiveElements { get; set; } = positiveElements;

    [JsonProperty("areas_for_improvement")]
    public List<string> AreasForImprovement { get; set; } = areasForImprovement;

    [JsonProperty("actionable_suggestions")]
    public List<string> ActionableSuggestions { get; set; } = actionableSuggestions;
}

public class ChatEvalScoreCalculator
{
    public class FinalScore
    {
        public double EmotionalIntelligenceScore { get; set; }

        public double AidaFrameworkScore { get; set; }

        public double BaseScore { get; set; }

        public double PenaltyDeduction { get; set; }

        public double FinalAdjustedScore { get; set; }
    }

    public FinalScore CalculateFinalScore(RawScores rawScores)
    {
        var result = new FinalScore();

        // Calculate Emotional Intelligence Score (60% weight)
        var eiScores = new Dictionary<string, double>
        {
            {
                "empathy",
                rawScores.EmotionalIntelligence.Empathy.Score * rawScores.EmotionalIntelligence.Empathy.Weight
            },
            {
                "authenticity",
                rawScores.EmotionalIntelligence.Authenticity.Score * rawScores.EmotionalIntelligence.Authenticity.Weight
            },
            {
                "trustBuilding", rawScores.EmotionalIntelligence.TrustBuilding.Score *
                                 rawScores.EmotionalIntelligence.TrustBuilding.Weight
            },
            {
                "activeListening", rawScores.EmotionalIntelligence.ActiveListening.Score *
                                   rawScores.EmotionalIntelligence.ActiveListening.Weight
            },
            {
                "engagement",
                rawScores.EmotionalIntelligence.Engagement.Score * rawScores.EmotionalIntelligence.Engagement.Weight
            },
            {
                "personality",
                rawScores.EmotionalIntelligence.Personality.Score * rawScores.EmotionalIntelligence.Personality.Weight
            }
        };

        result.EmotionalIntelligenceScore = eiScores.Values.Sum() * 0.60;

        // Calculate AIDA Framework Score (40% weight)
        var aidaScores = new Dictionary<string, double>
        {
            {
                "awareness", rawScores.AidaFramework.Awareness.Score * rawScores.AidaFramework.Awareness.Weight
            },
            {
                "interest", rawScores.AidaFramework.Interest.Score * rawScores.AidaFramework.Interest.Weight
            },
            {
                "desire", rawScores.AidaFramework.Desire.Score * rawScores.AidaFramework.Desire.Weight
            },
            {
                "action", rawScores.AidaFramework.Action.Score * rawScores.AidaFramework.Action.Weight
            }
        };

        result.AidaFrameworkScore = aidaScores.Values.Sum() * 0.40;

        // Calculate Base Score
        result.BaseScore = result.EmotionalIntelligenceScore + result.AidaFrameworkScore;

        var penaltyItems = rawScores.Penalties.Items.Where(
            i =>
                !string.Equals(
                    i,
                    "None",
                    StringComparison.InvariantCultureIgnoreCase)
                && !string.Equals(
                    i,
                    "Empty",
                    StringComparison.InvariantCultureIgnoreCase)
                && !string.IsNullOrWhiteSpace(i));

        // Calculate Penalty Deduction
        result.PenaltyDeduction = penaltyItems.Count() * rawScores.Penalties.DeductionPerItem;

        // Calculate Final Score (ensuring it stays within 1-5 range)
        result.FinalAdjustedScore = Math.Max(1, Math.Min(5, result.BaseScore - result.PenaltyDeduction));

        return result;
    }
}