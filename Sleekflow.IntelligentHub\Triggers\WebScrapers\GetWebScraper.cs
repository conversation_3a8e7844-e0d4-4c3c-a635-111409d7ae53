﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers;

[TriggerGroup(ControllerNames.WebScrapers)]
public class GetWebScraper : ITrigger<GetWebScraper.GetWebScraperInput, GetWebScraper.GetWebScraperOutput>
{
    private readonly IWebScraperService _webScraperService;

    public GetWebScraper(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class GetWebScraperInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetWebScraperInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetWebScraperOutput
    {
        [JsonProperty("web_scraper")]
        public WebScraper WebScraper { get; set; }

        [JsonConstructor]
        public GetWebScraperOutput(WebScraper webScraper)
        {
            WebScraper = webScraper;
        }
    }

    public async Task<GetWebScraperOutput> F(GetWebScraperInput getWebScraperInput)
    {
        var webScraper = await _webScraperService.GetWebScraperAsync(getWebScraperInput.SleekflowCompanyId);
        return new GetWebScraperOutput(webScraper);
    }
}