# SleekFlow Tools Plugin

## Overview

The SleekflowToolsPlugin provides a set of tools for interacting with SleekFlow's contact management and lead nurturing capabilities. It enables AI agents to interact with the SleekFlow platform by managing contact properties, labels, and creating custom objects.

## Architecture

The SleekflowToolsPlugin is implemented as a kernel function plugin within the Semantic Kernel framework. The plugins register methods as kernel functions that can be called by AI agents.

```mermaid
graph TD
    A[AI Agent] -->|Calls Functions| B[ISleekflowToolsPlugin]
    B -->|Implementation| C[SleekflowToolsPlugin]
    B -->|Mock Implementation| D[MockSleekflowToolsPlugin]
    C -->|Publishes Events| E[MassTransit IBus]
    E -->|Processes| F[FlowHub]
    F -->|Executes| G[Step Executors]

    subgraph "Plugin Methods"
        H[Lead Management]
        I[Contact Properties]
        J[Contact Labels]
    end

    C --> H
    C --> I
    C --> J

    H -->|Functions| H1[AddLeadScoreCustomObject]
    H -->|Functions| H2[AssignToTeam]
    H -->|Functions| H3[AddHandsOffCustomObject]

    I -->|Functions| I1[UpdateContactProperties]

    J -->|Functions| J1[AddContactLabels]
    J -->|Functions| J2[RemoveContactLabels]
    J -->|Functions| J3[SetContactLabels]
```

## Implementation Flow

```mermaid
sequenceDiagram
    participant Agent as AI Agent
    participant Plugin as SleekflowToolsPlugin
    participant Bus as MassTransit IBus
    participant Flow as FlowHub
    participant Executor as Step Executor

    Agent->>Plugin: Call Plugin Method

    alt Has valid state and contact ID
        Plugin->>Plugin: Create appropriate Step Args
        Plugin->>Plugin: Wrap in CallStep
        Plugin->>Bus: Publish OnExternalStepSubmittedEvent
        Bus->>Flow: Forward Event
        Flow->>Executor: Execute Step
    else Missing state or contact ID
        Plugin->>Plugin: Log simulation message
        Plugin->>Agent: Return simulation result
    end
```

## Methods

### Lead Management

#### AddLeadScoreCustomObject

Creates a new lead score custom object associated with a contact.

**Parameters:**
- `classification`: The classification of the lead type
- `reasoning`: The reasoning for the classification and lead score
- `score`: The latest lead score of the lead

**Usage:**
```csharp
await plugin.AddLeadScoreCustomObject(kernel, "High-Value", "Shows interest in enterprise plan", "85");
```

#### AssignToTeam

Assigns the lead to a specific team.

**Parameters:**
- `teamName`: The name of the team to which the lead will be assigned
- `assignReason`: The reason for triggering the assignment

**Usage:**
```csharp
await plugin.AssignToTeam(kernel, "Sales Team", "Qualified lead requesting demo");
```

#### AddHandsOffCustomObject

Creates a hands-off custom object to indicate that a lead should not be contacted.

**Parameters:**
- `assignmentReason`: The reason for triggering assignment
- `leadScore`: The latest lead score of the lead
- `shortSummary`: A comprehensive conversation summary

**Usage:**
```csharp
await plugin.AddHandsOffCustomObject(kernel, "Customer requested no contact", 65, "Customer asked to not be contacted until next quarter");
```

### Contact Property Management

#### UpdateContactProperties

Updates contact properties with specified values.

**Parameters:**
- `properties`: List of ContactProperty objects with PropertyId and PropertyValue

**Implementation Details:**
- Converts ContactProperty objects to ContactPropertyIdValuePair objects
- Uses UpdateContactPropertiesV2StepArgs internally

**Usage:**
```csharp
var properties = new List<ContactProperty>
{
    new ContactProperty { PropertyId = "email", PropertyValue = "<EMAIL>" },
    new ContactProperty { PropertyId = "company", PropertyValue = "Example Corp" }
};
await plugin.UpdateContactProperties(kernel, properties);
```

### Contact Label Management

#### AddContactLabels

Adds labels to a contact without removing existing labels.

**Parameters:**
- `labels`: List of labels to add

**Implementation Details:**
- Uses UpdateContactLabelRelationshipsV2StepArgs with null removalActionType

**Usage:**
```csharp
await plugin.AddContactLabels(kernel, new List<string> { "VIP", "Enterprise" });
```

#### RemoveContactLabels

Removes specific labels from a contact.

**Parameters:**
- `labels`: List of labels to remove

**Implementation Details:**
- Uses UpdateContactLabelRelationshipsV2StepArgs with "specific_label" removalActionType

**Usage:**
```csharp
await plugin.RemoveContactLabels(kernel, new List<string> { "Lead", "Unqualified" });
```

#### SetContactLabels

Removes all existing labels and sets the contact labels to the provided list.

**Parameters:**
- `labels`: List of labels to set (replaces all existing labels)

**Implementation Details:**
- Uses UpdateContactLabelRelationshipsV2StepArgs with "all_labels" removalActionType
- Can be used to remove all labels by passing an empty list

**Usage:**
```csharp
await plugin.SetContactLabels(kernel, new List<string> { "Customer", "Onboarding" });
```

## Mock Implementation for Testing

For testing purposes, the MockSleekflowToolsPlugin provides a test-friendly implementation that:

1. Tracks which methods were called
2. Records the parameters used in each call
3. Provides helper methods to verify calls during tests

```mermaid
graph TD
    A[Test] -->|Verifies Calls| B[MockSleekflowToolsPlugin]
    B -->|Tracks| C[SleekflowToolsCallInfo]

    subgraph "Tracking"
        D[Was Method Called?]
        E[Get Method Parameters]
        F[Clear Test State]
    end

    B --> D
    B --> E
    B --> F

    D -->|Methods| D1[WasAssignToTeamCalled]
    D -->|Methods| D2[WasLeadScoreCustomObjectCalled]
    D -->|Methods| D3[WasHandsOffCustomObjectCalled]
    D -->|Methods| D4[WasUpdateContactPropertiesCalled]
    D -->|Methods| D5[WasAddContactLabelsCalled]
    D -->|Methods| D6[WasRemoveContactLabelsCalled]
    D -->|Methods| D7[WasSetContactLabelsCalled]

    E -->|Methods| E1[GetAssignToTeamParams]
    E -->|Methods| E2[GetLeadScoreParams]
    E -->|Methods| E3[GetHandsOffParams]
    E -->|Methods| E4[GetUpdateContactPropertiesParams]
    E -->|Methods| E5[GetAddContactLabelsParams]
    E -->|Methods| E6[GetRemoveContactLabelsParams]
    E -->|Methods| E7[GetSetContactLabelsParams]

    F -->|Method| F1[ClearTestState]
```

## Integration with Semantic Kernel

The SleekflowToolsPlugin integrates with Microsoft's Semantic Kernel framework, making it easy to incorporate into AI agent-based workflows:

1. Functions are decorated with `[KernelFunction]` and `[Description]` attributes
2. Parameters are documented with `[Description]` attributes
3. Methods accept a Kernel parameter to access contextual information

## Example Usage in Agent Workflows

```mermaid
graph LR
    A[AI Agent] -->|Identifies Lead| B[Add Lead Score]
    A -->|Updates Info| C[Update Properties]
    A -->|Categorizes| D[Manage Labels]
    A -->|Assigns| E[Assign Team]
    A -->|Stops Contact| F[Hands Off]

    B --> G[Lead Scoring System]
    C --> H[Contact Database]
    D --> H
    E --> I[Team Assignment]
    F --> J[Hands Off System]
```