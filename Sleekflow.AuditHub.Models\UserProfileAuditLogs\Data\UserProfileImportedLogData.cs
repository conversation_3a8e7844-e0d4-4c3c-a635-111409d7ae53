using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileImportedLogData
{
    [JsonProperty("impacted_fields")]
    public List<ImpactedFieldLogData> ImpactedFields { get; set; }

    [JsonConstructor]
    public UserProfileImportedLogData(List<ImpactedFieldLogData> impactedFields)
    {
        ImpactedFields = impactedFields;
    }
}

public class ImpactedFieldLogData
{
    [JsonConstructor]
    public ImpactedFieldLogData(string fieldName, string value, string importAction)
    {
        FieldName = fieldName;
        Value = value;
        ImportAction = importAction;
    }

    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("value")]
    public string Value { get; set; }

    [JsonProperty("import_action")]
    public string ImportAction { get; set; }
}