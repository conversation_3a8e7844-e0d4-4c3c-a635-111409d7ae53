using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workers.Helpers;

namespace Sleekflow.FlowHub.Tests.Helpers
{
    [TestFixture]
    public class SplitContactDictionaryHelperTests
    {
        // Helper to create a simple ContactDetail instance for testing
        private ContactDetail CreateTestContactDetail(string name = "Test")
        {
            var contactDict = new Dictionary<string, object?> { { "name", name } };
            return new ContactDetail(contactDict, null, Array.Empty<ContactList>(), null);
        }

        [Test]
        public void Chunk_NullSource_ThrowsArgumentNullException()
        {
            // Arrange
            Dictionary<string, ContactDetail>? source = null;
            int numberOfChunks = 5;

            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => SplitContactDictionaryHelper.Chunk(source!, numberOfChunks));
        }

        [Test]
        public void Chunk_EmptySource_ReturnsEmptyList()
        {
            // Arrange
            var source = new Dictionary<string, ContactDetail>();
            int numberOfChunks = 5;

            // Act
            var result = SplitContactDictionaryHelper.Chunk(source, numberOfChunks);

            // Assert
            Assert.IsNotNull(result);
            Assert.IsEmpty(result);
        }

        [TestCase(0)]
        [TestCase(-1)]
        public void Chunk_InvalidNumberOfChunks_ThrowsArgumentOutOfRangeException(int numberOfChunks)
        {
            // Arrange
            var source = new Dictionary<string, ContactDetail>
            {
                { "c1", CreateTestContactDetail() }
            };

            // Act & Assert
            Assert.Throws<ArgumentOutOfRangeException>(() => SplitContactDictionaryHelper.Chunk(source, numberOfChunks));
        }

        [Test]
        public void Chunk_FewerItemsThanChunks_ReturnsOneChunkPerItem()
        {
            // Arrange
            var source = new Dictionary<string, ContactDetail>
            {
                { "c1", CreateTestContactDetail("A") },
                { "c2", CreateTestContactDetail("B") }
            };
            int numberOfChunks = 5; // More chunks than items

            // Act
            var result = SplitContactDictionaryHelper.Chunk(source, numberOfChunks);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(source.Count, result.Length); // Should get 2 chunks
            Assert.IsTrue(result.All(chunk => chunk.Count == 1)); // Each chunk has 1 item
            Assert.IsTrue(result[0].ContainsKey("c1"));
            Assert.IsTrue(result[1].ContainsKey("c2"));
        }

        [Test]
        public void Chunk_EqualItemsAndChunks_ReturnsOneChunkPerItem()
        {
            // Arrange
            var source = new Dictionary<string, ContactDetail>
            {
                { "c1", CreateTestContactDetail("A") },
                { "c2", CreateTestContactDetail("B") },
                { "c3", CreateTestContactDetail("C") }
            };
            int numberOfChunks = 3; // Equal chunks and items

            // Act
            var result = SplitContactDictionaryHelper.Chunk(source, numberOfChunks);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(numberOfChunks, result.Length);
            Assert.IsTrue(result.All(chunk => chunk.Count == 1)); // Each chunk has 1 item
            Assert.IsTrue(result[0].ContainsKey("c1"));
            Assert.IsTrue(result[1].ContainsKey("c2"));
            Assert.IsTrue(result[2].ContainsKey("c3"));
        }

        [Test]
        public void Chunk_MoreItemsThanChunks_Divisible_DistributesEvenly()
        {
            // Arrange
            var source = new Dictionary<string, ContactDetail>
            {
                { "c1", CreateTestContactDetail("A") }, { "c2", CreateTestContactDetail("B") },
                { "c3", CreateTestContactDetail("C") }, { "c4", CreateTestContactDetail("D") },
                { "c5", CreateTestContactDetail("E") }, { "c6", CreateTestContactDetail("F") }
            }; // 6 items
            int numberOfChunks = 3; // 6 is divisible by 3

            // Act
            var result = SplitContactDictionaryHelper.Chunk(source, numberOfChunks);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(numberOfChunks, result.Length); // 3 chunks
            // Each chunk should have 6 / 3 = 2 items
            Assert.IsTrue(result.All(chunk => chunk.Count == 2));

            // Verify all original keys are present across chunks
            var allKeysInChunks = result.SelectMany(chunk => chunk.Keys).ToList();
            Assert.AreEqual(source.Count, allKeysInChunks.Count);
            CollectionAssert.AreEquivalent(source.Keys, allKeysInChunks); // Order doesn't matter
        }

        [Test]
        public void Chunk_MoreItemsThanChunks_NotDivisible_DistributesRemainder()
        {
            // Arrange
            var source = new Dictionary<string, ContactDetail>
            {
                { "c1", CreateTestContactDetail("A") }, { "c2", CreateTestContactDetail("B") },
                { "c3", CreateTestContactDetail("C") }, { "c4", CreateTestContactDetail("D") },
                { "c5", CreateTestContactDetail("E") }, { "c6", CreateTestContactDetail("F") },
                { "c7", CreateTestContactDetail("G") }
            }; // 7 items
            int numberOfChunks = 3; // 7 / 3 = 2 remainder 1

            // Act
            var result = SplitContactDictionaryHelper.Chunk(source, numberOfChunks);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(numberOfChunks, result.Length); // 3 chunks

            // Check distribution: Expect sizes like 3, 2, 2
            var chunkSizes = result.Select(chunk => chunk.Count).OrderByDescending(size => size).ToList();
            Assert.AreEqual(3, chunkSizes[0]); // First chunk gets remainder
            Assert.AreEqual(2, chunkSizes[1]);
            Assert.AreEqual(2, chunkSizes[2]);
            Assert.AreEqual(source.Count, chunkSizes.Sum()); // Total count matches

            // Verify all original keys are present across chunks
            var allKeysInChunks = result.SelectMany(chunk => chunk.Keys).ToList();
            Assert.AreEqual(source.Count, allKeysInChunks.Count);
            CollectionAssert.AreEquivalent(source.Keys, allKeysInChunks); // Order doesn't matter
        }

        [Test]
        public void Chunk_LargeNumberOfItems_DistributesCorrectly()
        {
            // Arrange
            int itemCount = 100;
            var source = new Dictionary<string, ContactDetail>();
            for (int i = 0; i < itemCount; i++)
            {
                source.Add($"contact_{i}", CreateTestContactDetail($"Name_{i}"));
            }
            int numberOfChunks = 10; // 100 / 10 = 10

            // Act
            var result = SplitContactDictionaryHelper.Chunk(source, numberOfChunks);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(numberOfChunks, result.Length);
            Assert.IsTrue(result.All(chunk => chunk.Count == 10)); // Each chunk has 10 items

            // Verify total items
            Assert.AreEqual(itemCount, result.Sum(chunk => chunk.Count));

            // Verify all original keys are present
            var allKeysInChunks = result.SelectMany(chunk => chunk.Keys).ToList();
            Assert.AreEqual(itemCount, allKeysInChunks.Count);
            CollectionAssert.AreEquivalent(source.Keys, allKeysInChunks);
        }

         [Test]
        public void Chunk_OneChunkRequested_ReturnsSingleChunkWithAllItems()
        {
            // Arrange
            var source = new Dictionary<string, ContactDetail>
            {
                { "c1", CreateTestContactDetail("A") }, { "c2", CreateTestContactDetail("B") },
                { "c3", CreateTestContactDetail("C") }
            };
            int numberOfChunks = 1;

            // Act
            var result = SplitContactDictionaryHelper.Chunk(source, numberOfChunks);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Length); // Only one chunk
            Assert.AreEqual(source.Count, result[0].Count); // Contains all items
            CollectionAssert.AreEquivalent(source.Keys, result[0].Keys);
        }
    }
}