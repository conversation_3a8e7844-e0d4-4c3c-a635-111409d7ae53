﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Connections;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("zoho_connection")]
public class ZohoConnection : Entity, IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("organization_id")]
    public string OrganizationId { get; set; }

    [JsonProperty("authentication_id")]
    public string AuthenticationId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("environment")]
    public string Environment { get; set; }

    [JsonProperty("is_active")]
    public bool IsActive { get; set; }

    [JsonConstructor]
    public ZohoConnection(
        string id,
        string sleekflowCompanyId,
        string organizationId,
        string authenticationId,
        string name,
        string environment,
        bool isActive)
        : base(id, "Connection")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        OrganizationId = organizationId;
        AuthenticationId = authenticationId;
        Name = name;
        Environment = environment;
        IsActive = isActive;
    }
}