﻿using System.Net;
using Newtonsoft.Json;

namespace Sleekflow.Models.Events;

public class OnHttpRedirectReply
{
    [JsonProperty("success")]
    public bool Success { get; set; }

    [JsonProperty("http_response_message")]
    public string HttpResponseMessage { get; set; }

    [JsonProperty("http_status_code")]
    public HttpStatusCode HttpStatusCode { get; set; }

    [JsonConstructor]
    public OnHttpRedirectReply(bool success, string httpResponseMessage, HttpStatusCode httpStatusCode)
    {
        Success = success;
        HttpResponseMessage = httpResponseMessage;
        HttpStatusCode = httpStatusCode;
    }
}