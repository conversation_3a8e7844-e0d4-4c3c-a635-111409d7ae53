using System.Text;
using System.Text.RegularExpressions;
using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Events;
using Sleekflow.Models.Prompts;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.IntelligentHub.Consumers;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentRecommendedReplyEventConsumerDefinition
    : ConsumerDefinition<GetAgentRecommendedReplyEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentRecommendedReplyEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(4);
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentRecommendedReplyEventConsumer
    : FlowHubAgentGenericConsumer<GetAgentRecommendedReplyEvent>, IConsumer<GetAgentRecommendedReplyEvent>
{
    private readonly IChatService _chatService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;
    private readonly ITokenCountingService _tokenCountingService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly IIntelligentHubConfigService _intelligentHubConfigService;

    public GetAgentRecommendedReplyEventConsumer(
        IBus bus,
        IChatService chatService,
        IIntelligentHubUsageService intelligentHubUsageService,
        ILogger<GetAgentRecommendedReplyEventConsumer> logger,
        ICompanyAgentConfigService companyAgentConfigService,
        ITokenCountingService tokenCountingService,
        IIntelligentHubConfigService intelligentHubConfigService)
        : base(logger, bus)
    {
        _chatService = chatService;
        _companyAgentConfigService = companyAgentConfigService;
        _tokenCountingService = tokenCountingService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _intelligentHubConfigService = intelligentHubConfigService;
    }

    protected override async Task HandleMessageAsync(ConsumeContext<GetAgentRecommendedReplyEvent> context)
    {
        var message = context.Message;
        var sleekflowCompanyId = message.SleekflowCompanyId;
        var conversationContext = message.ConversationContext;
        var input = message.Input;

        // Initialize agent config and prompt context
        var agentConfig = message.AgentId == null
            ? null
            : await _companyAgentConfigService
                .GetOrDefaultAsync(message.AgentId, sleekflowCompanyId);

        if (agentConfig == null)
        {
            _logger.LogWarning(
                "Agent config not found for AgentId {AgentId} and SleekflowCompanyId {SleekflowCompanyId}",
                message.AgentId,
                sleekflowCompanyId);

            await _bus.Publish(
                new OnAgentCompleteStepActivationEvent(
                    message.AggregateStepId,
                    message.ProxyStateId,
                    message.StackEntries,
                    JsonConvert.SerializeObject(
                        new GetAgentRecommendedReplyEvent.Response(
                            "Unable to generate the response. No agent is provided.",
                            0))));

            return;
        }

        // Check if the usage limit is reached
        if (!message.IsCompanyHasAiPocPlan)
        {
            var intelligentHubConfig =
                await _intelligentHubConfigService.GetIntelligentHubConfigAsync(message.SleekflowCompanyId);

            var isUsageLimitExceeded = intelligentHubConfig == null ||
                                       await _intelligentHubUsageService.IsUsageLimitExceeded(
                                           message.SleekflowCompanyId,
                                           new Dictionary<string, int>
                                           {
                                               {
                                                   PriceableFeatures.AiAgentsTotalUsage, _intelligentHubUsageService
                                                       .GetFeatureTotalUsageLimit(
                                                           intelligentHubConfig,
                                                           PriceableFeatures.AiAgentsTotalUsage)
                                               }
                                           },
                                           new IntelligentHubUsageFilter(
                                               message.IntelligentHubUsageFilterFromDateTime,
                                               message.IntelligentHubUsageFilterToDateTime));

            if (isUsageLimitExceeded)
            {
                _logger.LogWarning(
                    "Cannot find IntelligentHubConfig or exceed max usage limit for AgentId {AgentId} and SleekflowCompanyId {SleekflowCompanyId}.",
                    message.AgentId,
                    sleekflowCompanyId);

                await _bus.Publish(
                    new OnAgentCompleteStepActivationEvent(
                        message.AggregateStepId,
                        message.ProxyStateId,
                        message.StackEntries,
                        JsonConvert.SerializeObject(
                            new GetAgentRecommendedReplyEvent.Response(
                                "Unable to generate the response. Cannot find IntelligentHubConfig or exceed max usage limit.",
                                -1))));

                return;
            }
        }

        using var d1 = Serilog.Context.LogContext.PushProperty("SleekflowCompanyId", sleekflowCompanyId);
        using var d2 = Serilog.Context.LogContext.PushProperty("ContactId", message.ContactId);
        using var d3 = Serilog.Context.LogContext.PushProperty("StateId", message.ProxyStateId);
        using var d4 = Serilog.Context.LogContext.PushProperty("AgentId", agentConfig.Id);
        using var d5 = Serilog.Context.LogContext.PushProperty(
            "AgentCollaborationMode",
            agentConfig.EffectiveCollaborationMode);

        _logger.LogInformation(
            "Streaming Agent Recommended Reply {SleekflowCompanyId} {ConversationContext}",
            sleekflowCompanyId,
            JsonConvert.SerializeObject(conversationContext, JsonConfig.DefaultLoggingJsonSerializerSettings));

        // Later on need to include the agent config here i.e. prompt instructions
        var (asyncEnumerable, sourcesStr, groupChatIdStr) = await _chatService.StreamAgentAnswerAsync(
            conversationContext,
            sleekflowCompanyId,
            new ReplyGenerationContext(
                sleekflowCompanyId,
                message.ContactId!,
                message.ContactProperties,
                message.ProxyStateId,
                input),
            agentConfig);

        var answerSb = new StringBuilder();
        await foreach (var partialAnswer in asyncEnumerable.WithCancellation(CancellationToken.None))
        {
            if (partialAnswer is null)
            {
                continue;
            }

            answerSb.Append(partialAnswer);
        }

        var recommendedReply = Regex.Replace(
            answerSb.ToString(),
            @"\[source\]",
            string.Empty,
            RegexOptions.IgnoreCase);

        int confidenceScoring;

        if (agentConfig.CollaborationMode == AgentCollaborationModes.ReAct

            // In Lead Nurturing mode, if the recommended reply is empty (terminated), we still want to return a default confidence scoring
            || (agentConfig.CollaborationMode == AgentCollaborationModes.LeadNurturing
                && recommendedReply == string.Empty))
        {
            confidenceScoring = 100; // Default confidence scoring for ReAct mode
        }
        else
        {
            _logger.LogInformation("Agent Recommended reply: {RecommendedReply}", recommendedReply);

            confidenceScoring = await _chatService.GetConfidenceScoring(conversationContext, recommendedReply);

            _logger.LogInformation(
                "Agent Recommended reply confidence scoring: {ConfidenceScoring}",
                confidenceScoring);
        }

        await _intelligentHubUsageService.RecordUsageAsync(
            sleekflowCompanyId,
            PriceableFeatures.AgentRecommendReply,
            null,
            new AgentRecommendReplySnapshot(
                JsonConvert.SerializeObject(
                    conversationContext,
                    JsonConfig.DefaultLoggingJsonSerializerSettings),
                sourcesStr ?? string.Empty,
                recommendedReply,
                _tokenCountingService.GetTokenCounts(groupChatIdStr),
                agentConfig.EffectiveCollaborationMode));

        var response = new GetAgentRecommendedReplyEvent.Response(recommendedReply, confidenceScoring);

        _logger.LogInformation(
            "Agent Recommended reply published to OnAgentCompleteStepActivationEvent {Response} {ProxyStateId} {AggregateStepId} {StackEntries}",
            JsonConvert.SerializeObject(response),
            message.ProxyStateId,
            message.AggregateStepId,
            JsonConvert.SerializeObject(message.StackEntries));

        await _bus.Publish(
            new OnAgentCompleteStepActivationEvent(
                message.AggregateStepId,
                message.ProxyStateId,
                message.StackEntries,
                JsonConvert.SerializeObject(response)));
    }
}