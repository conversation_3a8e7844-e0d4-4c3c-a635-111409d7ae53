﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Workers.Triggers.GoogleSheets;

public class SubscriptionsCheckOrchestrator
{
    public class SubscriptionsCheckOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTimeOffset LastUpdateTime { get; set; }

        [JsonProperty("last_object_modification_time")]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorCustomStatusOutput(
            long count,
            DateTimeOffset lastUpdateTime,
            DateTimeOffset? lastObjectModificationTime)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            LastObjectModificationTime = lastObjectModificationTime;
        }
    }

    public class SubscriptionsCheckOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("GoogleSheets_SubscriptionsCheck_Orchestrator")]
    public async Task<SubscriptionsCheckOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var startTime = context.CurrentUtcDateTime;
        var subscriptionsCheckInput = context.GetInput<SubscriptionsCheck.SubscriptionsCheckInput>();
        var googleSheetsSubscription = subscriptionsCheckInput.Subscription;

        context.SetCustomStatus(new SubscriptionsCheckOrchestratorCustomStatusOutput(0, startTime, null));

        // Configure retry policy with more attempts and longer intervals to handle Gateway Timeout (504) errors
        var taskOptions = new TaskOptions(new TaskRetryOptions(
            new RetryPolicy(
                7, // Increase max attempts to handle timeouts
                TimeSpan.FromSeconds(30), // Longer first interval
                2.0 // Same backoff coefficient
            )));

        var totalCount = 0L;

        var lastObjectModificationTime =
            googleSheetsSubscription.LastObjectModificationTime
            ?? googleSheetsSubscription.LastExecutionStartTime;
        var nextLastObjectModificationTime = lastObjectModificationTime;
        while (true)
        {
            var subscriptionsCheckBatchOutput =
                await context.CallActivityAsync<SubscriptionsCheckBatch.SubscriptionsCheckBatchOutput>(
                    "GoogleSheets_SubscriptionsCheck_Batch",
                    new SubscriptionsCheckBatch.SubscriptionsCheckBatchInput(
                        subscriptionsCheckInput.SleekflowCompanyId,
                        googleSheetsSubscription,
                        lastObjectModificationTime),
                    taskOptions);

            totalCount += subscriptionsCheckBatchOutput.Count;
            nextLastObjectModificationTime =
                subscriptionsCheckBatchOutput.NextLastObjectModificationTime > nextLastObjectModificationTime
                    ? subscriptionsCheckBatchOutput.NextLastObjectModificationTime
                    : nextLastObjectModificationTime;

            context.SetCustomStatus(
                new SubscriptionsCheckOrchestratorCustomStatusOutput(
                    totalCount,
                    context.CurrentUtcDateTime,
                    nextLastObjectModificationTime));

            if (subscriptionsCheckBatchOutput.After == null)
            {
                break;
            }

            await context.CreateTimer(
                context.CurrentUtcDateTime.Add(TimeSpan.FromSeconds(16)),
                CancellationToken.None);
        }

        await context.CallActivityAsync(
            "GoogleSheets_SubscriptionsCheck_End",
            new SubscriptionsCheckEnd.SubscriptionsCheckEndInput(
                googleSheetsSubscription,
                nextLastObjectModificationTime,
                startTime));

        return new SubscriptionsCheckOrchestratorOutput(totalCount);
    }
}