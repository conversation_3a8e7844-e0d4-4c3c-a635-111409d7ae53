using Pulumi;
using Pulumi.AzureNative.CognitiveServices;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using AppConfiguration = Pulumi.AzureNative.AppConfiguration;
using Cache = Pulumi.AzureNative.Cache;
using Insights = Pulumi.AzureNative.Insights;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.IntelligentHub;

public class IntelligentHubWorker
{
    private readonly ResourceGroup _resourceGroup;
    private readonly IntelligentHubDb.IntelligentHubDbOutput _intelligentHubDbOutput;
    private readonly AppConfiguration.ConfigurationStore _appConfig;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly Db.DbOutput _dbOutput;
    private readonly GcpConfig _gcpConfig;
    private readonly MyConfig _myConfig;
    private readonly Account _formRecognizerAccount;
    private readonly Account _textTranslationAccount;

    public IntelligentHubWorker(
        ResourceGroup resourceGroup,
        IntelligentHubDb.IntelligentHubDbOutput intelligentHubDbOutput,
        AppConfiguration.ConfigurationStore appConfig,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        Db.DbOutput dbOutput,
        GcpConfig gcpConfig,
        MyConfig myConfig,
        Account formRecognizerAccount,
        Account textTranslationAccount)
    {
        _resourceGroup = resourceGroup;
        _intelligentHubDbOutput = intelligentHubDbOutput;
        _appConfig = appConfig;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _dbOutput = dbOutput;
        _gcpConfig = gcpConfig;
        _myConfig = myConfig;
        _formRecognizerAccount = formRecognizerAccount;
        _textTranslationAccount = textTranslationAccount;
    }

    public Dictionary<string, Web.WebApp> InitWorker()
    {
        var apps = new Dictionary<string, Web.WebApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            if (managedEnvAndAppsTuple.IsExcludedFromManagedEnv(ServiceNames.IntelligentHub))
            {
                continue;
            }

            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventHub = managedEnvAndAppsTuple.EventHub;
            var redis = managedEnvAndAppsTuple.Redis;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            // Primary,Secondary,Primary Read Only,Secondary Read Only
            var primaryAppConfigReadOnlyConnStr = Output
                .Tuple(_resourceGroup.Name, _appConfig.Name)
                .Apply(
                    items => AppConfiguration.ListConfigurationStoreKeys.Invoke(
                        new AppConfiguration.ListConfigurationStoreKeysInvokeArgs
                        {
                            ResourceGroupName = items.Item1, ConfigStoreName = items.Item2
                        }))
                .Apply(o => o.Value.First(v => v.Name == "Primary Read Only").ConnectionString);

            var randomId = new Random.RandomId(
                "sleekflow-ih-worker-storage-account-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "ih-worker", "value"
                        }
                    },
                });

            var listRedisKeysOutput = Output
                .Tuple(_resourceGroup.Name, redis.Name, redis.Id)
                .Apply(
                    t => Cache.ListRedisKeys.InvokeAsync(
                        new Cache.ListRedisKeysArgs
                        {
                            ResourceGroupName = t.Item1, Name = t.Item2
                        }));

            var storageAccount = new Storage.StorageAccount(
                "sleekflow-ih-worker-storage-account",
                new Storage.StorageAccountArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new Storage.Inputs.SkuArgs
                    {
                        Name = Storage.SkuName.Standard_LRS,
                    },
                    Kind = Storage.Kind.StorageV2,
                    AccountName = randomId.Hex.Apply(h => "s" + h)
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var container = new Storage.BlobContainer(
                "sleekflow-ih-worker-zips-container",
                new Storage.BlobContainerArgs
                {
                    AccountName = storageAccount.Name,
                    PublicAccess = Storage.PublicAccess.None,
                    ResourceGroupName = _resourceGroup.Name,
                    ContainerName = "zips-container"
                },
                new CustomResourceOptions
                {
                    Parent = storageAccount
                });

            var path = "../Sleekflow.IntelligentHub.Workers/bin/Release/net8.0/publish";
            var ymdhms = new DirectoryInfo(path)
                .EnumerateFiles("*", SearchOption.AllDirectories)
                .Max(fi => fi.CreationTimeUtc)
                .ToString("yyyyMMddHHmmss");
            var blob = new Storage.Blob(
                "ih_worker_zip_" + ymdhms,
                new Storage.BlobArgs
                {
                    AccountName = storageAccount.Name,
                    ContainerName = container.Name,
                    ResourceGroupName = _resourceGroup.Name,
                    Type = Storage.BlobType.Block,
                    Source = new FileArchive(path)
                },
                new CustomResourceOptions
                {
                    Parent = container
                });
            var codeBlobUrl = StorageUtils.SignedBlobReadUrl(blob, container, storageAccount, _resourceGroup);

            var appInsights = new Insights.Component(
                "sleekflow-ih-worker-app-insight",
                new Insights.ComponentArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var appServicePlan = new Web.AppServicePlan(
                "sleekflow-ih-worker-app-service-plan",
                new Web.AppServicePlanArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Kind = string.Empty,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Tier = "Dynamic", Name = "Y1"
                    }
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var functionAppName = "sleekflow-ih-worker-app";
            var functionApp = new Web.WebApp(
                functionAppName,
                new Web.WebAppArgs
                {
                    Kind = "FunctionApp",
                    ResourceGroupName = _resourceGroup.Name,
                    ServerFarmId = appServicePlan.Id,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateNameValuePairs(
                            new[]
                            {
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_EXTENSION_VERSION", Value = "~4",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_WORKER_RUNTIME", Value = "dotnet-isolated",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_INPROC_NET8_ENABLED", Value = "1",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPINSIGHTS_INSTRUMENTATIONKEY", Value = appInsights.InstrumentationKey
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                    Value = appInsights.ConnectionString,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AzureWebJobsStorage",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "Storage",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTSHARE", Value = functionAppName + "96ed",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_RUN_FROM_PACKAGE", Value = codeBlobUrl,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APP_CONFIGURATION_CONN_STR", Value = primaryAppConfigReadOnlyConnStr,
                                },

                                #region AppConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "INTERNALS_KEY",
                                    Value =
                                        "BWijifKMPGviFQpYEkZuLjHOTpySNVawLYhiFKHEcmJyUDQPYlKuYUmJqgXVGGHP",
                                },

                                #endregion

                                #region CacheConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "CACHE_PREFIX", Value = "Sleekflow.IntelligentHub",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "REDIS_CONN_STR",
                                    Value = Output
                                        .Tuple(listRedisKeysOutput, redis.HostName)
                                        .Apply(
                                            t =>
                                                $"{t.Item2}:6380,password={t.Item1.PrimaryKey},ssl=True,abortConnect=False"),
                                },

                                #endregion

                                #region DbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                },

                                #endregion

                                #region IntelligentHubDbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_INTELLIGENT_HUB_DB_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_intelligentHubDbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_INTELLIGENT_HUB_DB_KEY", Value = _intelligentHubDbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_INTELLIGENT_HUB_DB_DATABASE_ID",
                                    Value = _intelligentHubDbOutput.DatabaseId,
                                },

                                #endregion

                                #region ServiceBusConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "SERVICE_BUS_CONN_STR", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr,
                                },

                                #endregion

                                #region EventHubConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "EVENT_HUB_CONN_STR", Value = eventHub.NamespacePrimaryConnStr,
                                },

                                #endregion

                                #region Loggings

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_AUTHENTICATION_ID",
                                    Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", Value = _gcpConfig.CredentialJson,
                                },

                                #endregion

                                #region MassTransitStorageConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONN_STR", Value = massTransitBlobStorage.StorageAccountConnStr
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONTAINER_NAME", Value = massTransitBlobStorage.ContainerName
                                },

                                #endregion


                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZUREOPENAI_ENDPOINT",
                                    Value = "https://sleekflow-ih-openai216b80d4.openai.azure.com/"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZUREOPENAI_KEY", Value = "********************************"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZUREOPENAI_EUS_ENDPOINT",
                                    Value = "https://sleekflow-openai-eus2.openai.azure.com/"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZUREOPENAI_EUS_KEY", Value = "0b0b5fec514f4a3da1a2c966874684a5"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURECOGNITIVESEARCH_ENDPOINT", Value = ""
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURECOGNITIVESEARCH_ADMIN_KEY", Value = ""
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURECOGNITIVESEARCH_ENDPOINT_2", Value = ""
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURECOGNITIVESEARCH_ADMIN_KEY_2", Value = ""
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURECOGNITIVESEARCH_ENDPOINT_3", Value = ""
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURECOGNITIVESEARCH_ADMIN_KEY_3", Value = ""
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "HEADLESS_WHATSAPP_MESSAGE_INTEGRATION_ENDPOINT",
                                    Value =
                                        "https://gw.sleekflow.io/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Messages/SendMessage"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "PROMPT_FILTER_PRINT_FUNCTIONS",
                                    Value =
                                        "EvaluateScoring,ExitCondition,CalculateLeadScore"
                                },

                                #region DbProcessorConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_CHANGE_FEED_ENV_ID", Value = "default",
                                },

                                #endregion

                                #region StorageConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "SOURCE_FILE_STORAGE_ACCOUNT_CONN_STR", Value = ""
                                },

                                #endregion

                                #region FormRecognizerConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURE_FORM_RECOGNIZER_ENDPOINT",
                                    Value =
                                        Output.Format(
                                            // this url is currently not usable  $"https://{formRecognizerAccount.Name}.cognitiveservices.azure.com/")
                                            $"https://eastasia.api.cognitive.microsoft.com/")
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURE_FORM_RECOGNIZER_KEY",
                                    Value = CognitiveServicesUtils.GetCognitiveServicesAccountKey(
                                        _resourceGroup.Name,
                                        _formRecognizerAccount.Name)
                                },

                                #endregion

                                #region TextTranslatorConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZURE_TEXT_TRANSLATOR_KEY",
                                    Value = CognitiveServicesUtils.GetCognitiveServicesAccountKey(
                                        _resourceGroup.Name,
                                        _textTranslationAccount.Name)
                                },

                                #endregion

                                #region ApplicationInsightTelemetryConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED", Value = "FALSE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED", Value = "FALSE",
                                },

                                #endregion

                                #region LightRagConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LIGHT_RAG_DOMAIN",
                                    // Value = Output
                                    //     .Tuple(
                                    //         managedEnvironment.DefaultDomain,
                                    //         containerApps[ServiceNames.IntelligentHubLightRag].Name)
                                    //     .Apply(t => $"https://{t.Item2}.{t.Item1}")
                                    Value = managedEnvAndAppsTuple
                                        .WebApps[ServiceNames.IntelligentHubLightRag].GetDomainName()
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LIGHT_RAG_SUPPORTED_COMPANIES",
                                    Value = IntelligentHub.GetLightRagSupportedCompanies(_myConfig.Name)
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LIGHT_RAG_API_KEY", Value = "cCY28CaFFBeNtJq5lSeNXErADNE7euUw"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LIGHT_RAG_WITH_NEO4J_DOMAIN",
                                    Value = _myConfig.Name == "production"
                                        ? managedEnvAndAppsTuple
                                            .WebApps[ServiceNames.IntelligentHubLightRagWithNeo4J].GetDomainName()
                                        : string.Empty
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LIGHT_RAG_WITH_NEO4J_SUPPORTED_COMPANIES",
                                    Value = IntelligentHub.GetLightRagWithNeo4JSupportedCompanies(_myConfig.Name)
                                },

                                #endregion

                                #region AZUREOPENAI_EMBEDDING_CONFIG

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZUREOPENAI_EMBEDDING_ENDPOINT",
                                    Value = "https://sleekflow-ih-openai216b80d4.openai.azure.com"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZUREOPENAI_EMBEDDING_KEY", Value = "********************************"
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AZUREOPENAI_EMBEDDING_DEPLOYMENT_NAME", Value = "embedding"
                                },

                                #endregion
                            }),
                        Use32BitWorkerProcess = true,
                        NetFrameworkVersion = "v8.0",
                    },
                },
                new CustomResourceOptions
                {
                    Parent = appServicePlan
                });

            managedEnvAndAppsTuple.WorkerApps.Add(ServiceNames.IntelligentHub, functionApp);
            apps.Add(ServiceNames.GetWorkerName(ServiceNames.IntelligentHub), functionApp);
        }

        return apps;
    }
}