using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IStorageConfig
{
    string SourceFileStorageAccountConnStr { get; }
}

public class StorageConfig : IConfig, IStorageConfig
{
    public string SourceFileStorageAccountConnStr { get; }

    public StorageConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        SourceFileStorageAccountConnStr =
            Environment.GetEnvironmentVariable("SOURCE_FILE_STORAGE_ACCOUNT_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("SOURCE_FILE_STORAGE_ACCOUNT_CONN_STR");
    }
}