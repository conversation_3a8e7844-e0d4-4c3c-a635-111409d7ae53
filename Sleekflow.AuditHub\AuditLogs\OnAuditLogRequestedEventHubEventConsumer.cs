﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.AuditHub.Models.AuditLogs;
using Sleekflow.AuditLogs;
using Sleekflow.Events.EventHub;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.AuditLogs;

public class OnAuditLogRequestedEventHubEventConsumerDefinition
    : ConsumerDefinition<OnAuditLogRequestedEventHubEventConsumer>, IRiderConsumer
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnAuditLogRequestedEventHubEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IEventHubReceiveEndpointConfigurator eventHubReceiveEndpointConfigurator)
        {
            eventHubReceiveEndpointConfigurator.ConcurrentMessageLimit = 40;
            eventHubReceiveEndpointConfigurator.PrefetchCount = 40;
            eventHubReceiveEndpointConfigurator.CheckpointInterval = TimeSpan.FromSeconds(15);
        }
    }
}

public class OnAuditLogRequestedEventHubEventConsumer : IConsumer<OnAuditLogRequestedEventHubEvent>, IRiderConsumer
{
    private readonly IAuditLogRepository _auditLogRepository;
    private readonly IIdService _idService;

    public OnAuditLogRequestedEventHubEventConsumer(
        IAuditLogRepository auditLogRepository,
        IIdService idService)
    {
        _auditLogRepository = auditLogRepository;
        _idService = idService;
    }

    public async Task Consume(ConsumeContext<OnAuditLogRequestedEventHubEvent> context)
    {
        var onAuditLogRequestedEventHubEvent = context.Message;
        var messageDataValue = await onAuditLogRequestedEventHubEvent.MessageData!.Value;
        var details = JsonConvert.DeserializeObject<Dictionary<string, object?>>(
            messageDataValue,
            new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore, MissingMemberHandling = MissingMemberHandling.Ignore,
            })!;

        var auditLog = new AuditLog(
            _idService.GetId("AuditLog"),
            onAuditLogRequestedEventHubEvent.SleekflowCompanyId,
            onAuditLogRequestedEventHubEvent.TypeName,
            onAuditLogRequestedEventHubEvent.TypeDescription,
            details,
            onAuditLogRequestedEventHubEvent.DateTime);

        await _auditLogRepository.CreateAsync(auditLog, onAuditLogRequestedEventHubEvent.SleekflowCompanyId);
    }
}