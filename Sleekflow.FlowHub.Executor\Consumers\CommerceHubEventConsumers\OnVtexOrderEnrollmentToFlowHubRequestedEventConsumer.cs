﻿using MassTransit;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Executor.Consumers.CommerceHubEventConsumers;

public class OnVtexOrderEnrollmentToFlowHubRequestedEventConsumerDefinition : ConsumerDefinition<OnVtexOrderEnrollmentToFlowHubRequestedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnVtexOrderEnrollmentToFlowHubRequestedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnVtexOrderEnrollmentToFlowHubRequestedEventConsumer : IConsumer<OnVtexOrderEnrollmentToFlowHubRequestedEvent>
{
    private readonly IServiceBusManager _serviceBusManager;

    public OnVtexOrderEnrollmentToFlowHubRequestedEventConsumer(IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public async Task Consume(ConsumeContext<OnVtexOrderEnrollmentToFlowHubRequestedEvent> context)
    {
        var @event = context.Message;

        await _serviceBusManager.PublishAsync(new OnTriggerEventRequestedEvent(
            new OnVtexOrderEnrolledEventBody(
                DateTimeOffset.UtcNow,
                @event.WorkflowId,
                @event.WorkflowVersionedId,
                @event.VtexAuthenticationId,
                @event.StatusCode,
                @event.OrderId,
                @event.Order),
            @event.OrderId,
            "Order",
            @event.SleekflowCompanyId));
    }
}