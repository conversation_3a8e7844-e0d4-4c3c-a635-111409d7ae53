using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Money = Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys.Money;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

public class BusinessBalanceAutoTopUpProfileDto
{
    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("minimum_balance")]
    public Money MinimumBalance { get; set; }

    [JsonProperty("auto_top_up_plan")]
    public StripeWhatsAppCreditTopUpPlan AutoTopUpPlan { get; set; }

    [JsonProperty("is_auto_top_up_enabled")]
    public bool IsAutoTopUpEnabled { get; set; }

    [JsonConstructor]
    public BusinessBalanceAutoTopUpProfileDto(
        string facebookBusinessId,
        Money minimumBalance,
        StripeWhatsAppCreditTopUpPlan autoTopUpPlan,
        bool isAutoTopUpEnabled)
    {
        FacebookBusinessId = facebookBusinessId;
        MinimumBalance = minimumBalance;
        AutoTopUpPlan = autoTopUpPlan;
        IsAutoTopUpEnabled = isAutoTopUpEnabled;
    }

    public BusinessBalanceAutoTopUpProfileDto(BusinessBalanceAutoTopUpProfile businessBalanceAutoTopUpProfile)
    {
        FacebookBusinessId = businessBalanceAutoTopUpProfile.FacebookBusinessId;
        MinimumBalance = businessBalanceAutoTopUpProfile.MinimumBalance;
        AutoTopUpPlan = businessBalanceAutoTopUpProfile.AutoTopUpPlan;
        IsAutoTopUpEnabled = businessBalanceAutoTopUpProfile.IsAutoTopUpEnabled;
    }
}