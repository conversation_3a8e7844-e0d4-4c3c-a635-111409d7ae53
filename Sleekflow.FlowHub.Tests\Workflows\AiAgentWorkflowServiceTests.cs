using System.Reflection;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.FlowHub.Blobs;
using Sleekflow.FlowHub.Internals.Agents;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.Tests.Workflows;

[TestFixture]
public class AiAgentWorkflowServiceTests
{
    private Mock<IWorkflowService> _mockWorkflowService;
    private Mock<IBlobService> _mockBlobService;
    private Mock<ICacheService> _mockCacheService;
    private Mock<ILogger<AiAgentWorkflowService>> _mockLogger;
    private Mock<IWorkflowStepValidator> _mockWorkflowStepValidator;
    private Mock<IIdService> _mockIdService;
    private Mock<IAgentConfigService> _mockAgentConfigService;
    private AiAgentWorkflowService _service;

    [SetUp]
    public void Setup()
    {
        _mockWorkflowService = new Mock<IWorkflowService>();
        _mockBlobService = new Mock<IBlobService>();
        _mockCacheService = new Mock<ICacheService>();
        _mockLogger = new Mock<ILogger<AiAgentWorkflowService>>();
        _mockWorkflowStepValidator = new Mock<IWorkflowStepValidator>();
        _mockIdService = new Mock<IIdService>();
        _mockAgentConfigService = new Mock<IAgentConfigService>();

        _service = new AiAgentWorkflowService(
            _mockWorkflowService.Object,
            _mockBlobService.Object,
            _mockCacheService.Object,
            _mockLogger.Object,
            _mockWorkflowStepValidator.Object,
            _mockIdService.Object,
            _mockAgentConfigService.Object
        );
    }

    #region RemoveBulkMessageStep Tests

    [Test]
    public void RemoveBulkMessageStep_WhenAggregateStepExists_ShouldRemoveStepAndUpdateReferences()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-other-call" },
                { "next_step_id", "aggregate-step-id" }
            },
            new Dictionary<string, object>
            {
                { "id", "aggregate-step-id" },
                { "call", AggregateStepArgs.CallName },
                { "next_step_id", "step-3" }
            },
            new Dictionary<string, object>
            {
                { "id", "step-3" },
                { "call", "another-call" },
                { "next_step_id", null }
            }
        };

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "RemoveBulkMessageStep", stepsJson);

        // Assert
        Assert.That(result.Count, Is.EqualTo(2));
        Assert.That(result.Any(s => s["id"].ToString() == "aggregate-step-id"), Is.False);
        Assert.That(result.First(s => s["id"].ToString() == "step-1")["next_step_id"], Is.EqualTo("step-3"));
    }

    [Test]
    public void RemoveBulkMessageStep_WhenAggregateStepExistsWithConditionBranching_ShouldUpdateSwitchCases()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "aggregate-step-id" },
                { "call", AggregateStepArgs.CallName },
                { "next_step_id", "step-3" }
            },
            new Dictionary<string, object>
            {
                { "id", "condition-step" },
                { "name", "condition-branching" },
                { "switch", new List<Dictionary<string, object>>
                    {
                        new Dictionary<string, object>
                        {
                            { "condition", "some condition" },
                            { "next_step_id", "aggregate-step-id" }
                        },
                        new Dictionary<string, object>
                        {
                            { "condition", "other condition" },
                            { "next_step_id", "other-step" }
                        }
                    }
                }
            }
        };

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "RemoveBulkMessageStep", stepsJson);

        // Assert
        var conditionStep = result.First(s => s["name"].ToString() == "condition-branching");
        var switchCases = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
            JsonConvert.SerializeObject(conditionStep["switch"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );

        Assert.That(switchCases.First(c => c["condition"].ToString() == "some condition")["next_step_id"],
            Is.EqualTo("step-3"));
    }

    [Test]
    public void RemoveBulkMessageStep_WhenAggregateStepDoesNotExist_ShouldReturnOriginalSteps()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-call" },
                { "next_step_id", "step-2" }
            },
            new Dictionary<string, object>
            {
                { "id", "step-2" },
                { "call", "another-call" },
                { "next_step_id", null }
            }
        };

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "RemoveBulkMessageStep", stepsJson);

        // Assert
        Assert.That(result.Count, Is.EqualTo(2));
        Assert.That(result, Is.EqualTo(stepsJson));
    }

    #endregion

    #region InsertAddLabelStep Tests

    [Test]
    public void InsertAddLabelStep_WhenSmartReplyStepExists_ShouldInsertAddLabelStepAfterIt()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-call" },
                { "next_step_id", "smart-reply-step" }
            },
            new Dictionary<string, object>
            {
                { "id", "smart-reply-step" },
                { "call", AgentRecommendReplyStepArgs.CallName },
                { "next_step_id", "step-3" }
            },
            new Dictionary<string, object>
            {
                { "id", "step-3" },
                { "call", "another-call" },
                { "next_step_id", null }
            }
        };

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "InsertAddLabelStep", stepsJson, false);

        // Assert
        Assert.That(result.Count, Is.EqualTo(4));

        var smartReplyStep = result.First(s => s["id"].ToString() == "smart-reply-step");
        var addLabelStep = result.Skip(2).First(); // Should be inserted after smart reply step

        Assert.That(addLabelStep["call"], Is.EqualTo(AgentAddLabelStepArgs.CallName));
        Assert.That(smartReplyStep["next_step_id"], Is.EqualTo(addLabelStep["id"]));
        Assert.That(addLabelStep["next_step_id"], Is.EqualTo("step-3"));
    }

    [Test]
    public void InsertAddLabelStep_WhenHasBulkMessageStep_ShouldSetRetrievalWindowTimestamp()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "smart-reply-step" },
                { "call", AgentRecommendReplyStepArgs.CallName },
                { "next_step_id", "step-3" }
            }
        };

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "InsertAddLabelStep", stepsJson, true);

        // Assert
        var addLabelStep = result.Last();
        var args = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(addLabelStep["args"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );

        Assert.That(args["retrieval_window_timestamp__expr"],
            Is.EqualTo("{{ '{{ (sys_var_dict[\"cadb7a99-026c-4769-9337-f44d99cb9f3b\"] | json.deserialize).completed_timestamp ?? \"\" }}' | template.eval }}"));
    }

    [Test]
    public void InsertAddLabelStep_WhenSmartReplyStepDoesNotExist_ShouldReturnOriginalSteps()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-call" },
                { "next_step_id", null }
            }
        };

        // Act & Assert - Should throw NullReferenceException when smart reply step doesn't exist
        Assert.Throws<TargetInvocationException>(() =>
        {
            InvokePrivateMethod<List<Dictionary<string, object>>>(
                _service, "InsertAddLabelStep", stepsJson, false);
        });
    }

    #endregion

    #region InsertSendGreetingMessageStep Tests

    [Test]
    public void InsertSendGreetingMessageStep_WhenGreetingMessageIsEmpty_ShouldReturnOriginalSteps()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-call" },
                { "name", "some-step" },
                { "next_step_id", null }
            }
        };

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "InsertSendGreetingMessageStep", stepsJson, "");

        // Assert
        Assert.That(result, Is.EqualTo(stepsJson));
    }

    [Test]
    public void InsertSendGreetingMessageStep_WhenGreetingMessageIsNull_ShouldReturnOriginalSteps()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-call" },
                { "name", "some-step" },
                { "next_step_id", null }
            }
        };

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "InsertSendGreetingMessageStep", stepsJson, null);

        // Assert
        Assert.That(result, Is.EqualTo(stepsJson));
    }

    [Test]
    public void InsertSendGreetingMessageStep_WhenAddLabelStepExists_ShouldInsertGreetingMessageStep()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-call" },
                { "name", "some-step" },
                { "next_step_id", "add-label-step" }
            },
            new Dictionary<string, object>
            {
                { "id", "add-label-step" },
                { "call", UpdateContactLabelRelationshipsStepArgs.CallName },
                { "name", "add_ai_session_label" },
                { "next_step_id", "step-3" }
            },
            new Dictionary<string, object>
            {
                { "id", "step-3" },
                { "call", "another-call" },
                { "name", "another-step" },
                { "next_step_id", null }
            }
        };
        var greetingMessage = "Hello! How can I help you today?";

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "InsertSendGreetingMessageStep", stepsJson, greetingMessage);

        // Assert
        Assert.That(result.Count, Is.EqualTo(4));

        var addLabelStep = result.First(s => s["id"].ToString() == "add-label-step");
        var greetingStep = result.Skip(2).First(); // Should be inserted after add label step

        Assert.That(greetingStep["call"], Is.EqualTo(SendMessageStepArgs.CallName));
        Assert.That(greetingStep["id"], Is.EqualTo("send-greeting-message-step-id"));
        Assert.That(addLabelStep["next_step_id"], Is.EqualTo("send-greeting-message-step-id"));
        Assert.That(greetingStep["next_step_id"], Is.EqualTo("step-3"));

        var args = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(greetingStep["args"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        Assert.That(args["message_body__expr"], Is.EqualTo(greetingMessage));
    }

    [Test]
    public void InsertSendGreetingMessageStep_WhenAddLabelStepDoesNotExist_ShouldReturnOriginalSteps()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", "some-call" },
                { "name", "some-step" },
                { "next_step_id", null }
            }
        };
        var greetingMessage = "Hello!";

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "InsertSendGreetingMessageStep", stepsJson, greetingMessage);

        // Assert
        Assert.That(result, Is.EqualTo(stepsJson));
    }

    #endregion

    #region UpdateMetaJson Tests

    [Test]
    public void UpdateMetaJson_WhenV6Exists_ShouldUpdateNodeIdsAndParams()
    {
        // Arrange
        var metaJson = new Dictionary<string, object>
        {
            { "v6", new Dictionary<string, object>
                {
                    { "nodes", new List<Dictionary<string, object>>
                        {
                            new Dictionary<string, object>
                            {
                                { "id", "node-1" },
                                { "data", new Dictionary<string, object>
                                    {
                                        { "formValues", new Dictionary<string, object>
                                            {
                                                { "channels", new List<object>() }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    },
                    { "edges", new List<Dictionary<string, object>>
                        {
                            new Dictionary<string, object>
                            {
                                { "id", "edge-1" },
                                { "source", "node-1" },
                                { "target", "node-2" }
                            }
                        }
                    }
                }
            }
        };

        var whatsappConfigs = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "channelIdentityId", "123456789" },
                { "name", "Test WhatsApp" },
                { "channelType", "whatsapp" }
            }
        };

        var labels = new List<Dictionary<string, object>>();
        var schemafulObject = new Dictionary<string, object>();
        var agents = new List<Dictionary<string, object>>();
        var contactProperty = new Dictionary<string, object>();
        var idMapping = new Dictionary<string, string>();

        // Act
        var result = InvokePrivateMethod<Dictionary<string, object>>(
            _service, "UpdateMetaJson", metaJson, whatsappConfigs, labels,
            schemafulObject, agents, contactProperty, idMapping);

        // Assert
        Assert.That(result.ContainsKey("v6"), Is.True);
        Assert.That(idMapping.Count, Is.GreaterThan(0)); // Should have generated new IDs
    }

    [Test]
    public void UpdateMetaJson_WhenV6DoesNotExist_ShouldReturnOriginalMeta()
    {
        // Arrange
        var metaJson = new Dictionary<string, object>
        {
            { "other", "value" }
        };

        var whatsappConfigs = new List<Dictionary<string, object>>();
        var labels = new List<Dictionary<string, object>>();
        var schemafulObject = new Dictionary<string, object>();
        var agents = new List<Dictionary<string, object>>();
        var contactProperty = new Dictionary<string, object>();
        var idMapping = new Dictionary<string, string>();

        // Act
        var result = InvokePrivateMethod<Dictionary<string, object>>(
            _service, "UpdateMetaJson", metaJson, whatsappConfigs, labels,
            schemafulObject, agents, contactProperty, idMapping);

        // Assert
        Assert.That(result, Is.EqualTo(metaJson));
    }

    [Test]
    public void UpdateMetaJson_ShouldUpdateChannelsInFormValues()
    {
        // Arrange
        var metaJson = new Dictionary<string, object>
        {
            { "v6", new Dictionary<string, object>
                {
                    { "nodes", new List<Dictionary<string, object>>
                        {
                            new Dictionary<string, object>
                            {
                                { "id", "node-1" },
                                { "data", new Dictionary<string, object>
                                    {
                                        { "formValues", new Dictionary<string, object>
                                            {
                                                { "channels", new List<object>() }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };

        var whatsappConfigs = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "channelIdentityId", "123456789" },
                { "name", "Test WhatsApp" },
                { "channelType", "whatsapp" }
            }
        };

        var labels = new List<Dictionary<string, object>>();
        var schemafulObject = new Dictionary<string, object>();
        var agents = new List<Dictionary<string, object>>();
        var contactProperty = new Dictionary<string, object>();
        var idMapping = new Dictionary<string, string>();

        // Act
        var result = InvokePrivateMethod<Dictionary<string, object>>(
            _service, "UpdateMetaJson", metaJson, whatsappConfigs, labels,
            schemafulObject, agents, contactProperty, idMapping);

        // Assert
        var v6 = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(result["v6"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        var nodes = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
            JsonConvert.SerializeObject(v6["nodes"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        var formValues = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(nodes[0]["data"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        var formValuesData = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(formValues["formValues"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        var channels = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(
            JsonConvert.SerializeObject(formValuesData["channels"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );

        Assert.That(channels.Count, Is.EqualTo(1));
        Assert.That(channels[0]["channelIdentityId"], Is.EqualTo("123456789"));
        Assert.That(channels[0]["name"], Is.EqualTo("Test WhatsApp"));
    }

    #endregion

    #region UpdateStepsJson Tests

    [Test]
    public void UpdateStepsJson_WhenSchemafulObjectStep_ShouldUpdateSchemaIdAndProperties()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", UpdateSchemafulObjectStepArgs.CallName },
                { "name", "update-schema-step" },
                { "next_step_id", null },
                { "args", new Dictionary<string, object>
                    {
                        { "schema_id__expr", "old-schema-id" },
                        { "property_values__key_expr_dict", new Dictionary<string, string>
                            {
                                { "old-prop-id", "{{ score }}" }
                            }
                        }
                    }
                }
            }
        };

        var whatsappConfigs = new List<Dictionary<string, object>>();
        var schemafulObject = new Dictionary<string, object>
        {
            { "id", "new-schema-id" },
            { "properties", new List<Dictionary<string, object>>
                {
                    new Dictionary<string, object>
                    {
                        { "id", "new-prop-id" },
                        { "unique_name", "score" }
                    }
                }
            }
        };
        var agents = new List<Dictionary<string, object>>();
        var contactProperty = new Dictionary<string, object>();
        var idMapping = new Dictionary<string, string>();

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "UpdateStepsJson", stepsJson, whatsappConfigs, schemafulObject,
            agents, contactProperty, idMapping, "ai-step", "workflow", false);

        // Assert
        var args = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(result[0]["args"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        Assert.That(args["schema_id__expr"], Is.EqualTo("{{ \"new-schema-id\" }}"));

        var propertyDict = JsonConvert.DeserializeObject<Dictionary<string, string>>(
            JsonConvert.SerializeObject(args["property_values__key_expr_dict"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        Assert.That(propertyDict.ContainsKey("new-prop-id"), Is.True);
        Assert.That(propertyDict["new-prop-id"], Is.EqualTo("{{ score }}"));
    }

    [Test]
    public void UpdateStepsJson_WhenAgentStep_ShouldUpdateAgentConfigId()
    {
        // Arrange
        var stepsJson = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "step-1" },
                { "call", AgentRecommendReplyStepArgs.CallName },
                { "name", "agent-step" },
                { "next_step_id", null },
                { "args", new Dictionary<string, object>
                    {
                        { "company_agent_config_id__expr", "old-agent-id" },
                        { "retrieval_window_timestamp__expr", "old-timestamp" }
                    }
                }
            }
        };

        var whatsappConfigs = new List<Dictionary<string, object>>();
        var schemafulObject = new Dictionary<string, object>();
        var agents = new List<Dictionary<string, object>>
        {
            new Dictionary<string, object>
            {
                { "id", "new-agent-id" }
            }
        };
        var contactProperty = new Dictionary<string, object>();
        var idMapping = new Dictionary<string, string>();

        // Act
        var result = InvokePrivateMethod<List<Dictionary<string, object>>>(
            _service, "UpdateStepsJson", stepsJson, whatsappConfigs, schemafulObject,
            agents, contactProperty, idMapping, "ai-step", "workflow", false);

        // Assert
        var args = JsonConvert.DeserializeObject<Dictionary<string, object>>(
            JsonConvert.SerializeObject(result[0]["args"], JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings
        );
        Assert.That(args["company_agent_config_id__expr"], Is.EqualTo("new-agent-id"));
        Assert.That(args["retrieval_window_timestamp__expr"], Is.EqualTo(string.Empty));
    }

    #endregion

    #region Helper Methods

    private T InvokePrivateMethod<T>(object obj, string methodName, params object[] parameters)
    {
        var method = obj.GetType().GetMethod(methodName,
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (method == null)
        {
            throw new InvalidOperationException($"Method {methodName} not found");
        }

        return (T)method.Invoke(obj, parameters);
    }

    #endregion
}