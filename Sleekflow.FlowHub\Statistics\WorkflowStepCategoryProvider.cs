﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Statistics;

public interface IWorkflowStepCategoryProvider
{
    string GetWorkflowTriggerStepCategory(WorkflowTriggers workflowTriggers);

    Dictionary<string, int> GetStepCategoryCountDict(
        List<Step> steps);

    Dictionary<string, string> GetStepIdCategoryMapping(
        List<Step> steps);
}

public sealed class WorkflowStepCategoryProvider : IWorkflowStepCategoryProvider, ISingletonService
{
    private readonly IWorkflowStepEntryProvider _workflowStepEntryProvider;

    public WorkflowStepCategoryProvider(
        IWorkflowStepEntryProvider workflowStepEntryProvider)
    {
        _workflowStepEntryProvider = workflowStepEntryProvider;
    }

    public string GetWorkflowTriggerStepCategory(WorkflowTriggers workflowTriggers)
        => workflowTriggers switch
        {
            // Conversation
            { ConversationStatusChanged: not null } => WorkflowStepCategories.Conversation,

            // Contact
            { ContactCreated: not null } => WorkflowStepCategories.Contact,
            { ContactLabelRelationshipsChanged: not null } => WorkflowStepCategories.Contact,
            { ContactListRelationshipsChanged: not null } => WorkflowStepCategories.Contact,
            { ContactUpdated: not null } => WorkflowStepCategories.Contact,
            { ContactEnrolled: not null } => WorkflowStepCategories.Contact,
            { ContactManuallyEnrolled: not null } => WorkflowStepCategories.Contact,

            // Messaging
            { MessageReceived: not null } => WorkflowStepCategories.Messaging,
            { MessageSent: not null } => WorkflowStepCategories.Messaging,
            { ClickToWhatsAppAdsMessageReceived: not null } => WorkflowStepCategories.Messaging,
            { WhatsappFlowSubmissionMessageReceived: not null } => WorkflowStepCategories.Messaging,
            { MessageStatusUpdated: not null } => WorkflowStepCategories.Messaging,

            // External integration
            { Webhook: not null } => WorkflowStepCategories.ExternalIntegration,

            // Custom object
            { SchemafulObjectCreated: not null } => WorkflowStepCategories.CustomObject,
            { SchemafulObjectUpdated: not null } => WorkflowStepCategories.CustomObject,
            { SchemafulObjectEnrolled: not null } => WorkflowStepCategories.CustomObject,

            // Salesforce integration
            { SalesforceAccountCreated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceAccountUpdated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceContactCreated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceContactUpdated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceLeadCreated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceLeadUpdated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceOpportunityCreated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceOpportunityUpdated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceCampaignCreated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceCampaignUpdated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceCustomObjectCreated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceCustomObjectUpdated: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceAccountEnrolled: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceContactEnrolled: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceLeadEnrolled: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceOpportunityEnrolled: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceCampaignEnrolled: not null } => WorkflowStepCategories.SalesforceIntegration,
            { SalesforceCustomObjectEnrolled: not null } => WorkflowStepCategories.SalesforceIntegration,

            // Facebook integration
            { FbIgPostCommentReceived: not null } => WorkflowStepCategories.FacebookIntegration,

            // Instagram integration
            { InstagramMediaCommentReceived: not null } => WorkflowStepCategories.InstagramIntegration,

            // Ticketing
            { TicketUpdated: not null } => WorkflowStepCategories.Ticketing,

            // Meta
            { MetaDetectedOutcomeReceived: not null } => WorkflowStepCategories.MetaIntegration,

            _ => string.Empty
        };

    public Dictionary<string, int> GetStepCategoryCountDict(
        List<Step> steps)
    {
        var stepEntries = _workflowStepEntryProvider.GetStepEntries(steps);
        var stepCategoryCountDict = new Dictionary<string, int>();

        foreach (var stepEntry in stepEntries)
        {
            var step = stepEntry.Step;
            var stepCategory = step.Category;

            if (string.IsNullOrWhiteSpace(stepCategory))
            {
                continue;
            }

            if (!stepCategoryCountDict.TryAdd(stepCategory, 1))
            {
                stepCategoryCountDict[stepCategory]++;
            }
        }

        return stepCategoryCountDict;
    }

    public Dictionary<string, string> GetStepIdCategoryMapping(
        List<Step> steps)
    {
        var stepEntries = _workflowStepEntryProvider.GetStepEntries(steps);
        var stepIdCategoryMapping = new Dictionary<string, string>();

        foreach (var stepEntry in stepEntries)
        {
            var stepId = stepEntry.Step.Id;
            var stepCategory = stepEntry.Step.Category;

            if (string.IsNullOrWhiteSpace(stepCategory))
            {
                continue;
            }

            stepIdCategoryMapping.Add(stepId, stepCategory);
        }

        return stepIdCategoryMapping;
    }
}