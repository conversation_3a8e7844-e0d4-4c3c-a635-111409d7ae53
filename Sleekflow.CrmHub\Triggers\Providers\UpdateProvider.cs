using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class UpdateProvider : ITrigger
{
    private readonly IProviderConfigService _providerConfigService;

    public class UpdateProviderInput
    {
        [JsonConstructor]
        public UpdateProviderInput(
            string sleekflowCompanyId,
            string providerName,
            string defaultRegionCode,
            Dictionary<string, UpdateSyncConfigDto> entityTypeNameToSyncConfigDict,
            List<string> recordStatuses,
            ProviderLimit? providerLimit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            DefaultRegionCode = defaultRegionCode;
            EntityTypeNameToSyncConfigDict = entityTypeNameToSyncConfigDict;
            RecordStatuses = recordStatuses;
            ProviderLimit = providerLimit;
        }

        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [System.ComponentModel.DataAnnotations.Required]
        public string ProviderName { get; set; }

        [JsonProperty("default_region_code")]
        [System.ComponentModel.DataAnnotations.Required]
        public string DefaultRegionCode { get; set; }

        [JsonProperty("entity_type_name_to_sync_config_dict")]
        [System.ComponentModel.DataAnnotations.Required]
        public Dictionary<string, UpdateSyncConfigDto> EntityTypeNameToSyncConfigDict { get; set; }

        [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
        [System.ComponentModel.DataAnnotations.Required]
        public List<string> RecordStatuses { get; set; }

        [JsonProperty("provider_limit")]
        public ProviderLimit? ProviderLimit { get; set; }
    }

    public class UpdateProviderOutput
    {
        [JsonConstructor]
        public UpdateProviderOutput(ProviderConfig providerConfig)
        {
            ProviderConfig = providerConfig;
        }

        [JsonProperty("provider_config")]
        public ProviderConfig ProviderConfig { get; set; }
    }

    public UpdateProvider(
        IProviderConfigService providerConfigService)
    {
        _providerConfigService = providerConfigService;
    }

    public async Task<UpdateProviderOutput> F(UpdateProviderInput updateProviderInput)
    {
        if (updateProviderInput.ProviderName == null)
        {
            throw new SfNotFoundObjectException("Provider does not exist");
        }

        if (updateProviderInput.SleekflowCompanyId == null)
        {
            throw new SfNotFoundObjectException("Provider does not exist");
        }

        var oldProviderConfig = await _providerConfigService.GetProviderConfigAsync(
            updateProviderInput.SleekflowCompanyId,
            updateProviderInput.ProviderName);

        if (oldProviderConfig == null)
        {
            throw new SfNotFoundObjectException("Provider does not exist");
        }

        if (oldProviderConfig.DefaultRegionCode != updateProviderInput.DefaultRegionCode)
        {
            await _providerConfigService.UpdateDefaultRegionCodeAsync(
                oldProviderConfig.Id,
                updateProviderInput.SleekflowCompanyId,
                updateProviderInput.DefaultRegionCode);
        }

        await _providerConfigService.UpdateOrRemoveProviderLimit(
            oldProviderConfig.Id,
            updateProviderInput.SleekflowCompanyId,
            updateProviderInput.ProviderLimit);

        // Remove old config
        foreach (var oldSyncConfig in oldProviderConfig.EntityTypeNameToSyncConfigDict)
        {
            await _providerConfigService.UpdateOrRemoveSyncConfigAsync(
                oldProviderConfig.Id,
                updateProviderInput.SleekflowCompanyId,
                oldSyncConfig.Key,
                null);
        }

        // Update with new config
        foreach (var syncConfigDtoPair in updateProviderInput.EntityTypeNameToSyncConfigDict)
        {
            var syncConfigDto = syncConfigDtoPair.Value;

            var syncConfig = new SyncConfig(
                syncConfigDto.FieldFilters,
                syncConfigDto.Interval,
                syncConfigDto.EntityTypeName,
                syncConfigDto.FilterGroups,
                syncConfigDto.SyncMode
            );

            await _providerConfigService.UpdateOrRemoveSyncConfigAsync(
                oldProviderConfig.Id,
                updateProviderInput.SleekflowCompanyId,
                syncConfigDtoPair.Key,
                syncConfig);
        }

        var providerConfig = await _providerConfigService.GetProviderConfigAsync(
            updateProviderInput.SleekflowCompanyId,
            updateProviderInput.ProviderName);

        return new UpdateProviderOutput(providerConfig);
    }
}