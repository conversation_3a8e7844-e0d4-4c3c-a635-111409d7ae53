﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.IntelligentHubConfigs;

[TriggerGroup(ControllerNames.IntelligentHubConfigs)]
public class GetFeatureUsages : ITrigger<GetFeatureUsages.GetFeatureUsagesInput, GetFeatureUsages.GetFeatureUsagesOutput>
{
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;

    public GetFeatureUsages(IIntelligentHubUsageService intelligentHubUsageService)
    {
        _intelligentHubUsageService = intelligentHubUsageService;
    }

    public class GetFeatureUsagesInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("feature_names")]
        [Validations.ValidateObject]
        public HashSet<string> FeatureNames { get; set; }

        [JsonProperty(IntelligentHubUsageFilter.ModelNameIntelligentHubUsageFilter)]
        [Validations.ValidateObject]
        public IntelligentHubUsageFilter? IntelligentHubUsageFilter { get; set; }

        [JsonConstructor]
        public GetFeatureUsagesInput(
            string sleekflowCompanyId,
            HashSet<string> featureNames,
            IntelligentHubUsageFilter? intelligentHubUsageFilter)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FeatureNames = featureNames;
            IntelligentHubUsageFilter = intelligentHubUsageFilter;
        }
    }

    public class GetFeatureUsagesOutput
    {
        [JsonProperty("feature_usages")]
        public Dictionary<string, int> FeatureUsages { get; set; }

        [JsonConstructor]
        public GetFeatureUsagesOutput(Dictionary<string, int> featureUsages)
        {
            FeatureUsages = featureUsages;
        }
    }

    public async Task<GetFeatureUsagesOutput> F(GetFeatureUsagesInput getFeatureUsagesInput)
    {
        var featureUsages = await _intelligentHubUsageService.GetFeatureUsagesAsync(
            getFeatureUsagesInput.SleekflowCompanyId,
            getFeatureUsagesInput.FeatureNames,
            getFeatureUsagesInput.IntelligentHubUsageFilter);

        return new GetFeatureUsagesOutput(featureUsages);
    }
}