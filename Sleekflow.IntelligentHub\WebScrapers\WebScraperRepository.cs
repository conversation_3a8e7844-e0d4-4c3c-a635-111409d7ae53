﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.WebScrapers;

public interface IWebScraperRepository : IRepository<WebScraper>
{
    public Task<WebScraper> GetWebScraperAsync(string sleekflowCompanyId);

    public Task<WebScraperTask> GetWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId);
}

public class WebScraperRepository
    : BaseRepository<WebScraper>,
        IWebScraperRepository,
        ISingletonService
{
    public WebScraperRepository(
        ILogger<WebScraperRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<WebScraper> GetWebScraperAsync(string sleekflowCompanyId)
    {
        var webScrapers = await GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId);

        if (!webScrapers.Any())
        {
            throw new ArgumentException("Not found valid WebScraper.", sleekflowCompanyId);
        }
        else if (webScrapers.Count > 1)
        {
            throw new SfInternalErrorException($"Duplicate WebScraper Founded! companyId: {sleekflowCompanyId}");
        }

        return webScrapers[0];
    }

    public async Task<WebScraperTask> GetWebScraperTaskAsync(string sleekflowCompanyId, string apifyTaskId)
    {
        var webScraper = await GetWebScraperAsync(sleekflowCompanyId);

        var tasks = webScraper.WebScraperTasks;

        if (tasks == null || tasks.TrueForAll(t => t.ApifyTaskId != apifyTaskId))
        {
            throw new ArgumentException("Task Not Founded!", apifyTaskId);
        }
        else if (tasks.Count(t => t.ApifyTaskId == apifyTaskId) > 1)
        {
            throw new SfInternalErrorException($"Duplicate Task Founded! sleekflowCompanyId: {sleekflowCompanyId}");
        }

        return tasks.First(t => t.ApifyTaskId == apifyTaskId);
    }
}