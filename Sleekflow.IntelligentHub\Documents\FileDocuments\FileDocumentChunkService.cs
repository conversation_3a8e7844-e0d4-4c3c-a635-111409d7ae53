using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.TextEnrichments;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments;

public interface IFileDocumentChunkService
{
    Task<FileDocumentChunk> CreateOrGetFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string blobId,
        string documentId,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata);

    Task<FileDocumentChunk> CreateFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string blobId,
        string documentId,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata);

    Task<FileDocumentChunk> CreateAndGetFileDocumentChunkAsync(
        FileDocumentChunk fileDocumentChunk);

    Task<FileDocumentChunk> GetFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string fileDocumentChunkId);

    Task<List<FileDocumentChunk>> GetFileDocumentChunksAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> fileDocumentChunkIds,
        int limit);

    Task<(List<FileDocumentChunk> FileDocumentChunks, string? NextContinuationToken)> GetFileDocumentChunksAsync(
        string sleekflowCompanyId,
        string documentId,
        string? continuationToken,
        int limit);

    Task<List<string>> GetFileDocumentChunkIdsAsync(
        string sleekflowCompanyId,
        string documentId);

    Task<FileDocumentChunk> PatchAndGetFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        EditingChunkDto editingChunk);

    Task<FileDocumentChunk> PatchAndGetFileDocumentChunkAsync(
        string fileDocumentChunkId,
        string sleekflowCompanyId,
        string documentId,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata);

    Task DeleteFileDocumentChunkAsync(string fileDocumentChunkId, string sleekflowCompanyId);

    Task DeleteFileDocumentChunkAsync(List<string> fileDocumentChunkIds, string sleekflowCompanyId);
}

public class FileDocumentChunkService : IFileDocumentChunkService, IScopedService
{
    private readonly IFileDocumentChunkRepository _fileDocumentChunkRepository;
    private readonly IIdService _idService;
    private readonly ITextTranslationService _textTranslationService;

    public FileDocumentChunkService(
        IFileDocumentChunkRepository fileDocumentChunkRepository,
        IIdService idService,
        ITextTranslationService textTranslationService)
    {
        _fileDocumentChunkRepository = fileDocumentChunkRepository;
        _idService = idService;
        _textTranslationService = textTranslationService;
    }

    public async Task<FileDocumentChunk> CreateFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string blobId,
        string documentId,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        var createdFileDocumentChunk = await _fileDocumentChunkRepository.CreateAndGetAsync(
            new FileDocumentChunk(
                _idService.GetId(SysTypeNames.FileDocumentChunk),
                sleekflowCompanyId,
                blobId,
                documentId,
                content,
                contentEn,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                categories,
                metadata),
            sleekflowCompanyId);

        return createdFileDocumentChunk;
    }

    public async Task<FileDocumentChunk> CreateOrGetFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string blobId,
        string documentId,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        var fileDocumentChunks = await _fileDocumentChunkRepository.GetObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId);
        if (fileDocumentChunks.Any())
        {
            return fileDocumentChunks.First();
        }

        var createdFileDocumentChunk = await CreateFileDocumentChunkAsync(
            sleekflowCompanyId,
            blobId,
            documentId,
            content,
            contentEn,
            categories,
            metadata);

        return createdFileDocumentChunk;
    }

    public async Task<FileDocumentChunk> CreateAndGetFileDocumentChunkAsync(FileDocumentChunk fileDocumentChunk)
    {
        var createdDocument =
            await _fileDocumentChunkRepository.CreateAndGetAsync(
                fileDocumentChunk,
                fileDocumentChunk.SleekflowCompanyId);
        return createdDocument;
    }

    public async Task<FileDocumentChunk> GetFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string fileDocumentChunkId)
    {
        var fileDocumentChunk = await _fileDocumentChunkRepository.GetAsync(fileDocumentChunkId, sleekflowCompanyId);
        if (fileDocumentChunk == null)
        {
            throw new SfNotFoundObjectException(fileDocumentChunkId, sleekflowCompanyId);
        }

        return fileDocumentChunk;
    }

    public async Task<List<FileDocumentChunk>> GetFileDocumentChunksAsync(
        string sleekflowCompanyId,
        string documentId,
        List<string> fileDocumentChunkIds,
        int limit)
    {
        return await _fileDocumentChunkRepository.GetFileDocumentChunksAsync(
            sleekflowCompanyId,
            documentId,
            fileDocumentChunkIds,
            limit);
    }

    public async Task<(List<FileDocumentChunk> FileDocumentChunks, string? NextContinuationToken)>
        GetFileDocumentChunksAsync(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit)
    {
        continuationToken = null;
        return await _fileDocumentChunkRepository.GetFileDocumentChunksAsync(
            sleekflowCompanyId,
            documentId,
            continuationToken,
            limit);
    }

    public async Task<List<string>> GetFileDocumentChunkIdsAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        return await _fileDocumentChunkRepository.GetFileDocumentChunkIdsAsync(
            sleekflowCompanyId,
            documentId);
    }

    public async Task<FileDocumentChunk> PatchAndGetFileDocumentChunkAsync(
        string fileDocumentChunkId,
        string sleekflowCompanyId,
        string documentId,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        var fileDocumentChunk = await _fileDocumentChunkRepository.GetFileDocumentChunkOrDefaultAsync(
            fileDocumentChunkId,
            sleekflowCompanyId,
            documentId);
        if (fileDocumentChunk is null)
        {
            throw new SfNotFoundObjectException(fileDocumentChunkId, sleekflowCompanyId);
        }

        var patchedFileDocumentChunk = await _fileDocumentChunkRepository.PatchAndGetFileDocumentChunkAsync(
            fileDocumentChunk,
            content,
            contentEn,
            categories,
            metadata);

        return patchedFileDocumentChunk;
    }

    public async Task<FileDocumentChunk> PatchAndGetFileDocumentChunkAsync(
        string sleekflowCompanyId,
        string documentId,
        EditingChunkDto editingChunk)
    {
        var fileDocumentChunk = await _fileDocumentChunkRepository.GetFileDocumentChunkOrDefaultAsync(
            editingChunk.ChunkId,
            sleekflowCompanyId,
            documentId);
        if (fileDocumentChunk is null)
        {
            throw new SfNotFoundObjectException(editingChunk.ChunkId, sleekflowCompanyId);
        }

        var contentEn = await _textTranslationService.TranslateByAzureTranslationServiceAsync(
            TranslationSupportedLanguages.English,
            editingChunk.Content);
        var patchedFileDocumentChunk = await _fileDocumentChunkRepository.PatchAndGetFileDocumentChunkAsync(
            fileDocumentChunk,
            editingChunk.Content,
            contentEn,
            editingChunk.Categories,
            editingChunk.Metadata);

        return patchedFileDocumentChunk;
    }

    public async Task DeleteFileDocumentChunkAsync(string fileDocumentChunkId, string sleekflowCompanyId)
    {
        await _fileDocumentChunkRepository.DeleteAsync(fileDocumentChunkId, sleekflowCompanyId);

    }

    public async Task DeleteFileDocumentChunkAsync(List<string> fileDocumentChunkIds, string sleekflowCompanyId)
    {
        await _fileDocumentChunkRepository.DeleteAsync(fileDocumentChunkIds, sleekflowCompanyId);
    }
}