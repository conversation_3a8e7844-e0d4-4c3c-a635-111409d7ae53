using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Wabas;

[TriggerGroup(ControllerNames.Wabas)]
public class ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessToken
    : ITrigger<
        ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessToken.ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput,
        ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessToken.ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessToken> _logger;

    public ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessToken(
        IWabaService wabaService,
        ILogger<ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessToken> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
    }

    public class ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("business_integration_system_user_access_token")]
        public string BusinessIntegrationSystemUserAccessToken { get; set; }

        [Validations.ValidateArray]
        [JsonProperty("forbidden_waba_ids")]
        public List<string>? ForbiddenWabaIds { get; set; }

        [JsonProperty("webhook_url")]
        public string? WebhookUrl { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput(
            string sleekflowCompanyId,
            string businessIntegrationSystemUserAccessToken,
            List<string> forbiddenWabaIds,
            string? webhookUrl,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BusinessIntegrationSystemUserAccessToken = businessIntegrationSystemUserAccessToken;
            ForbiddenWabaIds = forbiddenWabaIds;
            WebhookUrl = webhookUrl;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutput
    {
        [JsonProperty("connected_wabas")]
        public List<WabaDto> WabaDtoDtos { get; set; }

        [JsonConstructor]
        public ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutput(List<WabaDto> wabaDtos)
        {
            WabaDtoDtos = wabaDtos;
        }
    }

    public async Task<ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutput> F(
        ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenInput connectWhatsappCloudApiWabaInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            connectWhatsappCloudApiWabaInput.SleekflowStaffId,
            connectWhatsappCloudApiWabaInput.SleekflowStaffTeamIds);
        var wabas = await _wabaService.ConnectWabaWithBusinessSystemUserAccessTokenAsync(
            connectWhatsappCloudApiWabaInput.SleekflowCompanyId,
            connectWhatsappCloudApiWabaInput.BusinessIntegrationSystemUserAccessToken,
            connectWhatsappCloudApiWabaInput.ForbiddenWabaIds,
            connectWhatsappCloudApiWabaInput.WebhookUrl,
            sleekflowStaff);
        return new ConnectWhatsappCloudApiWabaWithBusinessIntegrationSystemUserAccessTokenOutput(wabas.Select(w => new WabaDto(w)).ToList());
    }
}