﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Sleekflow.CrmHub.Models.Workers;
using Sleekflow.CrmHub.Workers.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.CustomObject;

public class LoopThroughAndEnrollSchemafulObjectsToFlowHub
{
    private readonly ILogger<LoopThroughAndEnrollSchemafulObjectsToFlowHub> _logger;

    public LoopThroughAndEnrollSchemafulObjectsToFlowHub(
        ILogger<LoopThroughAndEnrollSchemafulObjectsToFlowHub> logger)
    {
        _logger = logger;
    }

    [Function("LoopThroughAndEnrollSchemafulObjectsToFlowHub")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<LoopThroughAndEnrollSchemafulObjectsToFlowHubInput, HttpManagementPayload, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<HttpManagementPayload> F(
        (LoopThroughAndEnrollSchemafulObjectsToFlowHubInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (input, logger, starter) = tuple;

        var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
            "LoopThroughAndEnrollSchemafulObjectsToFlowHub_Orchestrator",
            input);

        logger.LogInformation(
            "Started LoopThroughAndEnrollSchemafulObjectsToFlowHub with ID = [{InstanceId}]",
            instanceId);

        var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
        if (httpManagementPayload == null)
        {
            throw new Exception("Unable to get LoopThroughAndEnrollSchemafulObjectsToFlowHub_Orchestrator httpManagementPayload");
        }

        return httpManagementPayload;
    }
}