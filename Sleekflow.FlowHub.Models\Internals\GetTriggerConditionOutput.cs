using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;

public class GetTriggerConditionOutput
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("trigger_id")]
    public string TriggerId { get; set; }

    [JsonProperty("condition")]
    public string Condition { get; set; }

    [JsonProperty("workflow_name")]
    public string WorkflowName { get; set; }

    [JsonConstructor]
    public GetTriggerConditionOutput(
        string sleekflowCompanyId,
        string workflowVersionedId,
        string triggerId,
        string condition,
        string workflowName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionedId = workflowVersionedId;
        TriggerId = triggerId;
        Condition = condition;
        WorkflowName = workflowName;
    }
}