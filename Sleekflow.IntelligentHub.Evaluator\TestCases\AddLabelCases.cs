using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.AddLabel;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator;

public partial class AddLabelEvaluatorTest
{
    private static readonly List<Label> Labels = [
        new Label("1", "#Mobile", "#00FF00"),
        new Label("2", "#Healthcare", "#FFFF00"),
        new Label("3", "#Computer", "#FF0000"),
        new Label("4", "#Taxation", "#0000FF"),
        new Label("5", "#MedicalBeauty", "#FF00FF"),
    ];

    public static IEnumerable<AddLabelTestCase> GetTestCases()
    {
        return GetDefaultTestCases();
    }

    private static IEnumerable<AddLabelTestCase> GetDefaultTestCases()
    {

        yield return new AddLabelTestCase(
            "Computer and Taxation - Interest Transition (Simplified Chinese)",
            [
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "我想咨询下税务问题"),
                new ChatMessageContent(AuthorRole.Assistant, "您好，您想咨询什么税务问题"),
                new ChatMessageContent(AuthorRole.User, "算了，你们还卖电脑是么"),
                new ChatMessageContent(AuthorRole.Assistant, "是的，我们和很多电脑品牌进行合作，请问您想咨询游戏电脑还是办公电脑"),
                new ChatMessageContent(AuthorRole.User, "游戏笔记本电脑")
            ],
            Labels,
            "Customer transitions from taxation inquiry to computer product inquiry",
            new AddLabelResult("#Computer", "Customer shows interest in computer products after initial taxation inquiry")
        );

        yield return new AddLabelTestCase(
            "Medical Beauty - Price Inquiry (Simplified Chinese)",
            [
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "我想咨询下水光针"),
                new ChatMessageContent(AuthorRole.Assistant, "您好，我们这里有很多美容套餐，请问您主要想了解价格还是别的"),
                new ChatMessageContent(AuthorRole.User, "我想问下价格")
            ],
            Labels,
            "Customer inquires about medical beauty pricing",
            new AddLabelResult("#MedicalBeauty", "Customer specifically asks about pricing for medical beauty treatments")
        );

        yield return new AddLabelTestCase(
            "Enterprise Mobile Plan - Detailed Inquiry (Simplified Chinese)",
            [
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "我们公司需要升级手机计划，大约有50名员工需要新的资费套餐。"),
                new ChatMessageContent(AuthorRole.Assistant, "您好，感谢您联系我们关于企业手机计划。我们有专门针对企业客户的优惠方案，可以为您的50名员工提供灵活的数据和通话套餐。请问您对数据使用量有什么特别的要求吗？"),
                new ChatMessageContent(AuthorRole.User, "我们主要需要大量的本地数据，至少每人每月10GB，以及一些中国内地漫游数据。"),
                new ChatMessageContent(AuthorRole.Assistant, "我们的企业优惠计划正好符合您的需求。我们可以提供每人每月15GB本地数据和2GB中国内地漫游数据的方案，还附带无限本地通话。企业客户还可享受额外9折优惠。您希望安排一次详细咨询吗？"),
                new ChatMessageContent(AuthorRole.User, "听起来不错。我想了解一下具体的价格和合同期限。")
            ],
            Labels,
            "Enterprise customer provides detailed requirements for mobile plan inquiry",
            new AddLabelResult("#Mobile", "Enterprise customer provides detailed needs and asks about pricing and contracts, showing high purchase intent")
        );

        yield return new AddLabelTestCase(
            "Healthcare Service Plan - Direct Purchase Intent (Traditional Chinese)",
            [
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "我想了解一下醫療服務計劃。"),
                new ChatMessageContent(AuthorRole.Assistant, "香港寬頻市務部與Bowtie將於2024年6月27日推出全新「四合一醫療服務計劃」,免診金、免排隊、免藥費及包醫生紙,讓客戶以超值價格獲得全面的醫療保障。低至每月$99 就可享12個月計劃期內無限次普通科西醫視像會診、洗牙及全面牙科檢查、全身檢查及流感疫苗接種*,新客戶及現有客戶同樣可享計劃內高達$3,500醫療保健服務。"),
                new ChatMessageContent(AuthorRole.User, "服务时间呢"),
                new ChatMessageContent(AuthorRole.Assistant, "遙距視像會診的服務時間為週一至週五，上午9時至下午7時，和週六上午9時至下午1時，但不涵蓋公眾假期以及在黑色暴雨警告或八號或以上颱風信號生效期間。"),
                new ChatMessageContent(AuthorRole.User, "我想订购。")
            ],
            Labels,
            "Customer inquires about healthcare services and expresses purchase intent",
            new AddLabelResult("#Healthcare", "Customer clearly expresses purchase intent for healthcare services")
        );

        yield return new AddLabelTestCase(
            "Mobile Services and Home Care - Mixed Inquiry (English)",
            [
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "I would like to learn about your mobile services."),
                new ChatMessageContent(AuthorRole.Assistant, "To meet the growing demand for 'Asia-Pacific and Greater Bay Area' roaming data and 'China mobile phone numbers,' the marketing department will launch the 'N Mobile Local + Asia-Pacific Data Mobile Communication Plan with China Mobile Number' on April 24, 2024. The plan includes: Asia-Pacific data plans and Asia-Pacific data plans with a China Mainland number."),
                new ChatMessageContent(AuthorRole.User, "I heard you also have a home care plan."),
                new ChatMessageContent(AuthorRole.Assistant, "HKBN x Evercare 'Stay at Home' Home Care Plan. For as low as $399 per month, enjoy attentive home care services, making life easier for you and your family. Online booking available for a hassle-free process."),
                new ChatMessageContent(AuthorRole.User, "I would like to subscribe.")
            ],
            Labels,
            "Customer inquires about multiple services and expresses subscription intent",
            new AddLabelResult("#Healthcare", "Customer inquires about multiple services but shows final interest in healthcare/home care plan")
        );

        yield return new AddLabelTestCase(
            "Irrelevant Inquiry - Restaurant Search (English)",
            [
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "/clear"),
                new ChatMessageContent(AuthorRole.User, "I'm looking for a good Italian restaurant nearby.")
            ],
            Labels,
            "Customer asks about content unrelated to business services",
            new AddLabelResult("", "Customer inquiry is unrelated to available services, defaulting to primary service category")
        );

        yield return new AddLabelTestCase(
            "No Label Match - Weather and Stock Inquiry (English)",
            [
                new ChatMessageContent(AuthorRole.Assistant, "Hello! How can I assist you today?"),
                new ChatMessageContent(AuthorRole.User, "What's the weather like today?"),
                new ChatMessageContent(AuthorRole.Assistant, "I'm sorry, but I can only assist with our company's products and services. How can I help you with mobile plans, healthcare, computers, taxation, or medical beauty services?"),
                new ChatMessageContent(AuthorRole.User, "Can you tell me about the current stock market trends?"),
                new ChatMessageContent(AuthorRole.Assistant, "I apologize, but I don't have information about stock market trends. I'm here to help with our services."),
                new ChatMessageContent(AuthorRole.User, "What about the latest cryptocurrency prices?")
            ],
            Labels,
            "Customer asks about topics completely unrelated to any available service categories",
            new AddLabelResult("", "Customer inquiries about weather, stocks, and cryptocurrency do not match any available service labels, defaulting to primary category")
        );

        yield return new AddLabelTestCase(
            "High Interest - Clear Purchase Intent (Simplified Chinese)",
            [
                new ChatMessageContent(
                    AuthorRole.User,
                    "我想了解一下你们的手机计划，有什么优惠的套餐吗？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我们有多种手机计划，最受欢迎的是每月$78的42Mbps计划，包含10GB本地数据。"),
                new ChatMessageContent(
                    AuthorRole.User,
                    "这个计划听起来不错，我想要办理。请问需要什么手续？")
            ],
            Labels,
            "Customer evaluation based on interest level and purchase intent for label assignment",
            new AddLabelResult("#Mobile", "Customer clearly expresses purchase intent and asks about procedures, showing high interest in mobile services")
        );
    }
}