﻿using System.Reflection;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Plugins.Knowledges;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class SourceOnlyKnowledgePluginTest
{
    private MethodInfo? _knowledgeCleansingMethod;

    [SetUp]
    public void SetUp()
    {
        _knowledgeCleansingMethod = typeof(SourceOnlyKnowledgePlugin)
            .GetMethod("KnowledgeCleansing", BindingFlags.NonPublic | BindingFlags.Static);
        Assert.That(_knowledgeCleansingMethod, Is.Not.Null, "KnowledgeCleansing method should exist");
    }

    [Test]
    public void KnowledgeCleansing_WithSimpleDocumentTags_ShouldRemoveTagsAndKeepContent()
    {
        const string input = "<document id=\"1\">Simple content</document>";
        const string expected = "Simple content";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithMultipleDocumentTags_ShouldRemoveAllTagsAndKeepContent()
    {
        const string input = "<document id=\"1\">First content</document><document id=\"2\">Second content</document>";
        const string expected = "First contentSecond content";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithComplexDocumentAttributes_ShouldRemoveTagsAndKeepContent()
    {
        const string input = "<document id=\"1\" full_doc_id=\"doc-123\" created_at=\"2025-01-01\">Complex content with attributes</document>";
        const string expected = "Complex content with attributes";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithNestedContent_ShouldPreserveNestedTags()
    {
        const string input = "<document id=\"1\">Content with <span>nested tags</span> and <strong>formatting</strong></document>";
        const string expected = "Content with <span>nested tags</span> and <strong>formatting</strong>";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithMultilineContent_ShouldHandleNewlines()
    {
        const string input = @"<document id=""1"">Line 1
Line 2
Line 3</document>";
        const string expected = @"Line 1
Line 2
Line 3";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithNoDocumentTags_ShouldReturnOriginalContent()
    {
        const string input = "Content without document tags";
        const string expected = "Content without document tags";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithEmptyString_ShouldReturnEmptyString()
    {
        const string input = "";
        const string expected = "";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithEmptyDocumentTags_ShouldReturnEmpty()
    {
        const string input = "<document id=\"1\"></document>";
        const string expected = "";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithRealWorldExample_ShouldCleanProperly()
    {
        const string kgContextJson = "{\"kg_context\": \"\\n-----Entities-----\\n<document id=\\\"1\\\" full_doc_id=\\\"1\\\">&quot;WILDRIDE - BABY CARRIER DENIM&quot;,&quot;CATEGORY&quot;,&quot;A denim-style baby carrier by Wildride priced at JP¥13,335 after a discount from its original JP¥14,816, with a rating of two white circles.&quot;</document>\\n<document id=\\\"2\\\" full_doc_id=\\\"145\\\">&quot;WILDRIDE&quot;,&quot;ORGANIZATION&quot;,Wildride is a premier brand exclusively available on Jakewell&#x27;s platform</document>\\n\\n-----Relationships-----\\n<document id=\\\"1\\\" full_doc_id=\\\"2025-05-06 21:58:07\\\">&quot;WILDRIDE - BABY CARRIER DENIM&quot;,&quot;WILDRIDE&quot;,&quot;Wildride produces and sells the Baby Carrier DENIM, offering a unique denim design.&quot;,&quot;product line, baby carriers&quot;,9.0,146</document>\", \"vector_context\": \"<document id=\\\"1\\\" created_at=\\\"2025-05-06 17:12:25\\\">\\nProduct Details of Wildride - 嬰兒揹帶 MAIN</document>\"}";
        var contextData = JsonConvert.DeserializeObject<dynamic>(kgContextJson);
        var kgContext = (string) contextData.kg_context;
        var result = InvokeKnowledgeCleansing(kgContext);

        // Assert
        // Verify that document tags are removed
        Assert.That(result, Does.Not.Contain("<document"));
        Assert.That(result, Does.Not.Contain("</document>"));

        // Verify that content is preserved
        Assert.That(result, Does.Contain("WILDRIDE - BABY CARRIER DENIM"));
        Assert.That(result, Does.Contain("A denim-style baby carrier by Wildride"));
        Assert.That(result, Does.Contain("Wildride is a premier brand"));
        Assert.That(result, Does.Contain("-----Entities-----"));
        Assert.That(result, Does.Contain("-----Relationships-----"));
    }

    [Test]
    public void KnowledgeCleansing_WithVectorContextExample_ShouldCleanProperly()
    {
        const string input = "<document id=\"1\" created_at=\"2025-05-06 17:12:25\">\nProduct Details of Wildride - 嬰兒揹帶 MAIN, including details such as Suitable for children from 9 months to 4 years (maximum 20 kg)\n</document>";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Does.Not.Contain("<document"));
        Assert.That(result, Does.Not.Contain("</document>"));
        Assert.That(result, Does.Contain("Product Details of Wildride - 嬰兒揹帶 MAIN"));
        Assert.That(result, Does.Contain("Suitable for children from 9 months to 4 years"));
    }

    [Test]
    public void KnowledgeCleansing_WithSpecialCharactersInContent_ShouldPreserveSpecialChars()
    {
        const string input = "<document id=\"1\">Content with &quot;quotes&quot; and &#x27;apostrophes&#x27; and JP¥ symbols</document>";
        const string expected = "Content with &quot;quotes&quot; and &#x27;apostrophes&#x27; and JP¥ symbols";
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));
    }

    [Test]
    public void KnowledgeCleansing_WithComprehensiveWildrideContent_ShouldReturnUnchanged()
    {
        const string input = "Product Details of Wildride - 嬰兒揹帶 MAIN, including details such as Suitable for children from 9 months to 4 years (maximum 20 kg), The seat is made of 100% cotton, the black strap is made of 100% nylon, The black clip is 100% POM (Vintage rose/Dirty desert black clip is 100% PU), Gold detail logo on the strap\n\nThe Wildride baby carrier is suitable for children from 9 months to 4 years old, with a maximum weight of 20 kg. The seat of the carrier is made entirely of cotton, while the black strap is made of 100% nylon. The black clip is made of 100% POM, but for the Vintage rose and Dirty desert colors, the black clip is made of 100% PU. The carrier also features a gold detail logo on the strap.\n--New Chunk--\n沐浴陽光，還是在歷史悠久的街道上漫步，Wildride 揹帶都是我們必不可少的度假必需品，旅行必備 輕裝上陣！\n\n[立即瀏覽](https://www.jakewell.com.hk/pages/wildride)\n\nHere is a list of Wildride toddler carriers:\n\n*   **Wildride - 嬰兒揹帶 MAIN**\n    ! [Wildride - 嬰兒揹帶 MAIN](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67b58256631017000c3d3c70/900x.webp?source_format=jpg)\n    Price: JP¥11,666 (Sale), JP¥12,962 (Original)\n    Available colors include Lavender and others. For more details and options, visit [Wildride - 嬰兒揹帶 MAIN](https://www.jakewell.com.hk/zh-hant/products/wildride-toddler-carrier-main)\n\n*   **Wildride - 嬰兒揹帶 PRINT**\n    ! [Wildride - 嬰兒揹帶 PRINT](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adade883b052000d0f27ef/900x.webp?source_format=jpg)\n    Price: JP¥13,335 (Sale), JP¥14,816 (Original)\n    Available prints include Grey leopard and others. For more details and options, visit [Wildride - 嬰兒揹帶 PRINT](https://www.jakewell.com.hk/zh-hant/products/wildride-toddler-carrier-print)\n\n*   **Wildride - 嬰兒揹帶 Rib Corduroy**\n    ! [Wildride - 嬰兒揹帶 Rib Corduroy](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adae47127696000dcb6b82/900x.webp?source_format=jpg)\n    Price: JP¥20,010 (Sale), JP¥22,234 (Original)\n    Available colors include Lilac rib and others. For more details and options, visit [Wildride - 嬰兒揹帶 Rib Corduroy](https://www.jakewell.com.hk/zh-hant/products/wildride-toddler-carrier-rib-corduroy)\n\n*   **Wildride - 嬰兒揹帶 LINEN**\n    ! [Wildride - 嬰兒揹帶 LINEN](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adae882d7412000f1b9843/900x.webp?source_format=jpg)\n    Price: JP¥13,335 (Sale), JP¥14,816 (Original)\n    Available colors include Black. For more details and options, visit [Wildride - 嬰兒揹帶 LINEN](https://www.jakewell.com.hk/zh-hant/products/wildride-toddler-carrier-linen)\n\n*   **Wildride - 嬰兒揹帶 PREMIUM**\n    ! [Wildride - 嬰兒揹帶 PREMIUM](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adaed48b5f57000f458bbc/900x.webp?source_format=jpg)\n    Price: JP¥23,348 (Sale), JP¥25,942 (Original)\n    Available patterns include Checquered (黑白千鳥格紋) and others. For more details and options, visit [Wildride - 嬰兒揹帶 PREMIUM](https://www.jakewell.com.hk/zh-hant/products/wildride-toddler-carrier-premium) *   **Wildride - 嬰兒揹帶 DENIM**\n    ! [Wildride - 嬰兒揹帶 DENIM](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adaf71fe685a001172a6d3/900x.webp?source_format=jpg)\n    Price: JP¥13,335 (Sale), JP¥14,816 (Original)\n    Available in Indigo stripe【Pre Order Now! Delivery after early May】(靛藍條紋【立即預訂！5月頭後出貨】). For more details and options, visit [Wildride - 嬰兒揹帶 DENIM](https://www.jakewell.com.hk/zh-hant/products/wildride-toddler-carrier-denim)\n\n*   **Wildride - 手提收納袋**\n    ! [Wildride - 手提收納袋](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adb035d1164b000b2d7a16/900x.webp?source_format=jpg)\n    Price: JP¥7,399\n    Available patterns include Brown leo pouch (棕色豹紋) and others. For more details and options, visit [Wildride - 手提收納袋](https://www.jakewell.com.hk/zh-hant/products/wildride-pouches)\n\n## Jakewell 優質嬰兒用品店推薦\n\nJakewell is a baby product store established in 2013, committed to the belief of \"Having the best to provide you with the best.\" Our baby product brands include Stokke® from Norway,\n--New Chunk--\nWildride 嬰兒揹帶 - 常見問題: This table lists frequently asked questions about the Wildride baby carrier, including why you might need one, the differences between the various series, and whether Jakewell stores have physical Wildride products available. The questions cover topics such as reducing arm strain, material and pattern variations, and store locations.\n\n💬 **Why do I need a Wildride baby carrier?** Wildride baby carriers are designed to help parents who find their babies dislike strollers or prefer being held, which can lead to arm strain and back fatigue. The Wildride carrier, with its European patented design and manufacture, is suitable for children aged 9 months to 4 years (up to 20 kg) and helps reduce hand pressure while carrying the child. It features widened and thickened shoulder straps for extended use.\n\n**What are the differences between the various Wildride baby carrier series?** Wildride offers several series, including Denim, Main, Linen, Rib Corduroy, Print, and Premium, all of which are the same in terms of function, suitable age, weight, and usage. However, each series features different materials and patterns, allowing parents to choose a carrier that best suits their preferences and style.\n\n**Does Jakewell have physical Wildride baby carriers in stores?** Yes, Jakewell stores have physical Wildride baby carriers available for parents to experience. The four store locations are in North Point, Olympian City in West Kowloon, Monterey in Tseung Kwan O, and Wai Fang in Tai Wai. Each store is open from 11:00 to 20:00, and parents can visit to purchase Wildride baby carriers and view other types of baby products.\n--New Chunk--\nTable caption: Table of Wildride Baby Carrier - Frequently Asked Questions, including questions like why do I need to use Wildride BB carrier, what is the difference between the different series of Wildride baby carriers, and does Jakewell store have Wildride children's carrier in stock.\n\n💬 Wildride Baby Carrier is frequently asked with questions like why it is needed, what are the differences between different series, and whether Jakewell stores have it in stock. Many parents have experienced situations where babies don't want to sit in strollers or prefer to be held, but prolonged holding can strain parents' arms and backs. The Wildride baby carrier is designed to help in these situations. This European patented and manufactured carrier is suitable for children aged 9 months to 4 years (up to 20 kg), helping to reduce hand pressure while holding the child, featuring widened and thickened shoulder straps for extended use. Wildride offers various series such as Denim, Main, Linen, Rib Corduroy, Print, and Premium, all identical in function, suitable age range, weight, and usage, but each series features different materials and patterns, allowing parents to choose according to their preferences and style. Wildride baby carriers are available at Jakewell stores located in North Point, Olympian City in West Kowloon, Monterey in Tseung Kwan O, and Wai Fang in Tai Wai, with opening hours from 11:00 to 20:00, where parents can experience the convenience of Wildride and explore various types of baby products.\n--New Chunk--\n輕手部壓力，同時增進親子關係。\n\n它不僅僅是一個BB揹帶，更是你的外出好幫手，一起來體驗Wildride 為父母帶來不可思議的便利吧！\n\n### Wildride 兒童揹帶穿戴示範 👀\n\n當你初次使用Wildride 嬰兒揹帶時，可以參考以下示範影片，為你清楚示範使用步驟和小貼士，令你用得更加得心應手！\n\nThe Wildride - 嬰兒揹帶 MAIN is showcased in a demonstration video. The product is available for purchase at [https://www.jakewell.com.hk/products/wildride-toddler-carrier-main](https://www.jakewell.com.hk/products/wildride-toddler-carrier-main).\n\n[! [Wildride - 嬰兒揹帶 MAIN](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67b58256631017000c3d3c70/900x.webp?source_format=jpg)](https://www.jakewell.com.hk/products/wildride-toddler-carrier-main)\n\n[! [Wildride - 嬰兒揹帶 MAIN](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67b58261289811000e5b78c5/900x.webp?source_format=jpg)](https://www.jakewell.com.hk/products/wildride-toddler-carrier-main)\n\nThe product is priced at JP¥11,666, with a crossed-out price of JP¥12,962.\n\nThere are other options available:\n\nHere is a list of Wildride baby carriers:\n\n*   **Wildride - 嬰兒揹帶 MAIN**\n    ! [Wildride - 嬰兒揹帶 MAIN](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67b58256631017000c3d3c70/900x.webp?source_format=jpg)\n    Price: JP¥11,666 (Sale), JP¥12,962 (Original)\n    Available colors:\n\nLavender\n\n*   **Wildride - 嬰兒揹帶 PRINT**\n    ! [Wildride - 嬰兒揹帶 PRINT](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adade883b052000d0f27ef/900x.webp?source_format=jpg)\n    Price: JP¥13,335 (Sale), JP¥14,816 (Original)\n    Available prints:\n\nGrey leopard\n\n*   **Wildride - 嬰兒揹帶 PREMIUM**\n    ! [Wildride - 嬰兒揹帶 PREMIUM](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adaed48b5f57000f458bbc/900x.webp?source_format=jpg)\n    Price: JP¥23,348 (Sale), JP¥25,942 (Original)\n    Available patterns:\n\nChecquered (黑白千鳥格紋)\n\n*   **Wildride - 嬰兒揹帶 DENIM**\n    ! [Wildride - 嬰兒揹帶 DENIM](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adaf71fe685a001172a6d3/900x.webp?source_format=jpg)\n    Price: JP¥13,335 (Sale), JP¥14,816 (Original)\n    Available styles: Indigo stripe【Pre Order Now! Delivery after early May】(靛藍條紋【立即預訂！5月頭後出貨】)\n\n*   **Wildride - 嬰兒揹帶 LINEN**\n    ! [Wildride - 嬰兒揹帶 LINEN](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adae882d7412000f1b9843/900x.webp?source_format=jpg)\n    Price: JP¥13,335 (Sale), JP¥14,816 (Original)\n    Available colors:\n\nBlack (黑色)\n\n*   **Wildride - 嬰兒揹帶 Rib Corduroy**\n    ! [Wildride - 嬰兒揹帶 Rib Corduroy](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67adae47127696000dcb6b82/900x.webp?source_format=jpg)\n    Price: JP¥20,010 (Sale), JP¥22,234 (Original)\n    Available colors:\n\nLilac rib (紫色)\n\nThe following images showcase the Wildride carriers in various lifestyle settings:\n\n! [A man wearing sunglasses, a dark blue shirt, and light-colored shorts is standing on a sandy beach, holding a young child in a baby carrier. The child, dressed in a light-colored top and striped bottoms, appears to be sleeping or resting against the man's chest. Behind them, there are several black and white striped umbrellas and lounge chairs, suggesting a beach resort or club setting. The building in the background has a modern design with large windows. The sky is clear, indicating a sunny day.](https://shoplineimg.com/5db66d5f9db3cb002ca18b66/67e392f4d";
        const string expected = input; // Should remain unchanged as there are no document tags
        var result = InvokeKnowledgeCleansing(input);
        Assert.That(result, Is.EqualTo(expected));

        // Additional validation to ensure specific content is preserved
        Assert.That(result, Does.Contain("Product Details of Wildride - 嬰兒揹帶 MAIN"));
        Assert.That(result, Does.Contain("沐浴陽光，還是在歷史悠久的街道上漫步"));
        Assert.That(result, Does.Contain("Wildride 嬰兒揹帶 - 常見問題"));
        Assert.That(result, Does.Contain("JP¥11,666"));
        Assert.That(result, Does.Contain("--New Chunk--"));
        Assert.That(result, Does.Contain("💬 **Why do I need a Wildride baby carrier?**"));
    }

    private string InvokeKnowledgeCleansing(string input)
    {
        return (string) _knowledgeCleansingMethod!.Invoke(null, [input])!;
    }
}