using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.Datasets;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Managements;

public class ManagementWabaDto : WabaDto
{
    [JsonConstructor]
    public ManagementWabaDto(
        string id,
        string facebookWabaId,
        string facebookBusinessId,
        string facebookWabaBusinessId,
        string? facebookWabaBusinessName,
        string? facebookWabaBusinessVerificationStatus,
        string? facebookWabaBusinessProfilePictureUri,
        string? facebookWabaBusinessVertical,
        string? facebookWabaName,
        string? facebookWabaAccountReviewStatus,
        string? facebookWabaMessageTemplateNamespace,
        List<WabaPhoneNumberDto> wabaPhoneNumbers,
        WabaProductCatalogDto? wabaProductCatalog,
        WabaDatasetDto? wabaDatasetDto,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<string> sleekflowCompanyIds)
        : base(
            id,
            facebookWabaId,
            sleekflowCompanyIds,
            facebookBusinessId,
            facebookWabaBusinessId,
            facebookWabaBusinessName,
            facebookWabaBusinessVerificationStatus,
            facebookWabaBusinessProfilePictureUri,
            facebookWabaBusinessVertical,
            facebookWabaName,
            facebookWabaAccountReviewStatus,
            facebookWabaMessageTemplateNamespace,
            wabaPhoneNumbers,
            wabaProductCatalog,
            wabaDatasetDto,
            createdAt,
            updatedAt)
    {
    }

    public ManagementWabaDto(Waba waba)
        : base(waba)
    {
        SleekflowCompanyIds = waba.SleekflowCompanyIds;
        WabaPhoneNumbers = waba.WabaPhoneNumbers.Select(wpn => new WabaPhoneNumberDto(wpn)).ToList();
    }
}