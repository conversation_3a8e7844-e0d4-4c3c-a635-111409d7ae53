﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Integrator.Attributes;
using Sleekflow.FlowHub.Integrator.Integrators.ZapierTriggerIntegrator;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Integrator.Controllers;

public partial class PublicController
{
    private readonly IZapierTriggerOnSchemafulObjectCreatedIntegrator _zapierTriggerOnSchemafulObjectCreatedIntegrator;
    private readonly IZapierTriggerOnSchemafulObjectUpdatedIntegrator _zapierTriggerOnSchemafulObjectUpdatedIntegrator;

    public PublicController(
        IZapierTriggerOnSchemafulObjectCreatedIntegrator zapierTriggerOnSchemafulObjectCreatedIntegrator,
        IZapierTriggerOnSchemafulObjectUpdatedIntegrator zapierTriggerOnSchemafulObjectUpdatedIntegrator)
    {
        _zapierTriggerOnSchemafulObjectCreatedIntegrator = zapierTriggerOnSchemafulObjectCreatedIntegrator;
        _zapierTriggerOnSchemafulObjectUpdatedIntegrator = zapierTriggerOnSchemafulObjectUpdatedIntegrator;
    }

    public abstract class CreateZapierTriggerRequest
    {
        /// <example>
        /// subscription:42369199
        /// </example>
        /// <summary>Zap Id.</summary>
        [Required]
        [JsonProperty("zap_id")]
        public string ZapId { get; set; }

        /// <example>
        /// https://hooks.zapier.com/hooks/standard/10581699/71920490b93f4f8db073345e71ae9399/
        /// </example>
        /// <summary>Hook Url.</summary>
        [Required]
        [JsonProperty("hook_url")]
        public string HookUrl { get; set; }

        [JsonConstructor]
        protected CreateZapierTriggerRequest(string zapId, string hookUrl)
        {
            ZapId = zapId;
            HookUrl = hookUrl;
        }
    }

    public class CreateZapierTriggerResponse
    {
        [JsonProperty("subscribe_data")]
        public ZapierSubscribeData SubscribeData { get; set; }

        [JsonConstructor]
        public CreateZapierTriggerResponse(ZapierSubscribeData subscribeData)
        {
            SubscribeData = subscribeData;
        }
    }

    public class ZapierSubscribeData
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public ZapierSubscribeData(string id)
        {
            Id = id;
        }
    }

    public class CreateZapierTriggerOnSchemafulObjectCreatedRequest : CreateZapierTriggerRequest
    {
        [Required]
        [JsonProperty("schema_unique_name")]
        public string SchemaUniqueName { get; set; }

        [JsonConstructor]
        public CreateZapierTriggerOnSchemafulObjectCreatedRequest(
            string zapId,
            string hookUrl,
            string schemaUniqueName)
            : base(zapId, hookUrl)
        {
            SchemaUniqueName = schemaUniqueName;
        }
    }

    [HttpPost]
    [ZapierApiKeyAuthorization]
    [Route("Zapier/Triggers/OnSchemafulObjectCreated")]
    public async Task<ActionResult<CreateZapierTriggerResponse>> CreateZapierTriggerOnSchemafulObjectCreated(
        [FromHeader(Name = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        string sleekflowCompanyId,
        [FromHeader(Name = "X-Sleekflow-Location")]
        string? sleekflowLocation,
        [FromBody]
        CreateZapierTriggerOnSchemafulObjectCreatedRequest request)
    {
        await _zapierTriggerOnSchemafulObjectCreatedIntegrator.CreateIntegrationAsync(
            sleekflowCompanyId,
            request.ZapId,
            request.HookUrl,
            sleekflowLocation,
            request.SchemaUniqueName);

        var createZapierTriggerResponse = new CreateZapierTriggerResponse(new ZapierSubscribeData(request.ZapId));

        return Ok(createZapierTriggerResponse);
    }

    public class CreateZapierTriggerOnSchemafulObjectUpdatedRequest : CreateZapierTriggerRequest
    {
        [Required]
        [JsonProperty("schema_unique_name")]
        public string SchemaUniqueName { get; set; }

        [JsonConstructor]
        public CreateZapierTriggerOnSchemafulObjectUpdatedRequest(
            string zapId,
            string hookUrl,
            string schemaUniqueName)
            : base(zapId, hookUrl)
        {
            SchemaUniqueName = schemaUniqueName;
        }
    }

    [HttpPost]
    [ZapierApiKeyAuthorization]
    [Route("Zapier/Triggers/OnSchemafulObjectUpdated")]
    public async Task<ActionResult<CreateZapierTriggerResponse>> CreateZapierTriggerOnSchemafulObjectUpdated(
        [FromHeader(Name = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        string sleekflowCompanyId,
        [FromHeader(Name = "X-Sleekflow-Location")]
        string? sleekflowLocation,
        [FromBody]
        CreateZapierTriggerOnSchemafulObjectUpdatedRequest request)
    {
        await _zapierTriggerOnSchemafulObjectUpdatedIntegrator.CreateIntegrationAsync(
            sleekflowCompanyId,
            request.ZapId,
            request.HookUrl,
            sleekflowLocation,
            request.SchemaUniqueName);

        var createZapierTriggerResponse = new CreateZapierTriggerResponse(new ZapierSubscribeData(request.ZapId));

        return Ok(createZapierTriggerResponse);
    }

    [HttpDelete]
    [ZapierApiKeyAuthorization]
    [Route("Zapier/Triggers/OnSchemafulObjectCreated")]
    public async Task<IActionResult> DeleteZapierTriggerOnSchemafulObjectCreated(
        [FromHeader(Name = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        string sleekflowCompanyId,
        [FromQuery(Name = "zap_id")]
        string zapId)
    {
        await _zapierTriggerOnSchemafulObjectCreatedIntegrator.DeleteIntegrationAsync(sleekflowCompanyId, zapId);

        return Ok();
    }

    [HttpDelete]
    [ZapierApiKeyAuthorization]
    [Route("Zapier/Triggers/OnSchemafulObjectUpdated")]
    public async Task<IActionResult> DeleteZapierTriggerOnSchemafulObjectUpdated(
        [FromHeader(Name = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        string sleekflowCompanyId,
        [FromQuery(Name = "zap_id")]
        string zapId)
    {
        await _zapierTriggerOnSchemafulObjectUpdatedIntegrator.DeleteIntegrationAsync(sleekflowCompanyId, zapId);

        return Ok();
    }

    [HttpGet]
    [ZapierApiKeyAuthorization]
    [Route("Zapier/Triggers/OnSchemafulObjectCreated")]
    public async Task<IActionResult> GetZapierTriggerSampleDataOnSchemafulObjectCreated(
        [FromHeader(Name = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        string sleekflowCompanyId,
        [FromHeader(Name = "X-Sleekflow-Location")]
        string? sleekflowLocation,
        [FromQuery(Name = "schema_unique_name")]
        string schemaUniqueName)
    {
        var sampleData = await _zapierTriggerOnSchemafulObjectCreatedIntegrator.GetSampleAsync(
            sleekflowCompanyId,
            sleekflowLocation,
            schemaUniqueName);

        return Ok(sampleData);
    }

    [HttpGet]
    [ZapierApiKeyAuthorization]
    [Route("Zapier/Triggers/OnSchemafulObjectUpdated")]
    public async Task<IActionResult> GetZapierTriggerSampleDataOnSchemafulObjectUpdated(
        [FromHeader(Name = IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        string sleekflowCompanyId,
        [FromHeader(Name = "X-Sleekflow-Location")]
        string? sleekflowLocation,
        [FromQuery(Name = "schema_unique_name")]
        string schemaUniqueName)
    {
        var sampleData = await _zapierTriggerOnSchemafulObjectUpdatedIntegrator.GetSampleAsync(
            sleekflowCompanyId,
            sleekflowLocation,
            schemaUniqueName);

        return Ok(sampleData);
    }
}