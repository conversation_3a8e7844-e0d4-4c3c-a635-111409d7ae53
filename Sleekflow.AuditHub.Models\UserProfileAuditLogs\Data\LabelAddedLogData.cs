using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class LabelAddedLogData
{
    [JsonProperty("labels_added")]
    public List<LabelLogData> LabelsAdded { get; set; }

    [JsonConstructor]
    public LabelAddedLogData(List<LabelLogData> labelsAdded)
    {
        LabelsAdded = labelsAdded;
    }
}

public class LabelLogData
{
    [JsonConstructor]
    public LabelLogData(string label)
    {
        Label = label;
    }

    [JsonProperty("label")]
    public string Label { get; set; }
}