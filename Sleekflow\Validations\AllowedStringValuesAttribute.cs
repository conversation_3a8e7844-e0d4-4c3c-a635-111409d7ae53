﻿using System.ComponentModel.DataAnnotations;

namespace Sleekflow.Validations;

/// <summary>
/// Custom validation attribute used to ensure that input value match an entry in a predefined
/// list of allowed values, with the option to perform a case-insensitive comparison. If the
/// input value does not match any of the allowed values, a validation error is returned.
/// </summary>
public sealed class AllowedStringValuesAttribute : ValidationAttribute
{
    public IEnumerable<string> AllowedValues => _allowedValues.AsEnumerable();

    private readonly bool _isIgnoreCase;
    private readonly string[] _allowedValues;

    public AllowedStringValuesAttribute(bool isIgnoreCase = false, params string[] allowedValues)
    {
        if (allowedValues.Length == 0)
        {
            throw new ArgumentException("Allowed values must have at least one entry", nameof(allowedValues));
        }

        _isIgnoreCase = isIgnoreCase;
        _allowedValues = allowedValues;
    }

    protected override ValidationResult? IsValid(object? value, ValidationContext validationContext)
    {
        if (value is not string providedValue)
        {
            return ValidationResult.Success;
        }

        IEqualityComparer<string?> comparer = _isIgnoreCase
            ? StringComparer.OrdinalIgnoreCase
            : EqualityComparer<string?>.Default;

        var isAllowedValue = _allowedValues.Contains(providedValue, comparer);

        if (!isAllowedValue)
        {
            return new ValidationResult(
                $"The provided value '{providedValue}' is invalid",
                new[]
                {
                    validationContext.MemberName!
                });
        }

        return ValidationResult.Success;
    }
}