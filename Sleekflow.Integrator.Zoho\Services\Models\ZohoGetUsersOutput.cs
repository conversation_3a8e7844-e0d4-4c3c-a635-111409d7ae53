﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Zoho.Services.Models;

public class ZohoGetUsersOutput
{
    [JsonProperty("users")]
    public List<Dictionary<string, object?>>? Users { get; set; }

    [JsonProperty("info")]
    public PageInfo? Info { get; set; }

    public class PageInfo
    {
        [JsonProperty("count")]
        public int? Count { get; set; }

        [JsonProperty("more_records")]
        public bool? MoreRecords { get; set; }

        [JsonProperty("page")]
        public int? Page { get; set; }
    }
}