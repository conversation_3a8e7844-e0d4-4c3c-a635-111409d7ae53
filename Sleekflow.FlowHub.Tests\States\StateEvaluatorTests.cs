using System.Globalization;
using MassTransit.Serialization.JsonConverters;
using Newtonsoft.Json;
using NUnit.Framework.Constraints;
using Scriban.Runtime;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.States;
using Sleekflow.Locks;

namespace Sleekflow.FlowHub.Tests.States;

public class StateEvaluatorTests
{
#pragma warning disable CS8618
    private StateEvaluator _stateEvaluator;
    private StateAggregator _stateAggregator;
#pragma warning restore CS8618

    [SetUp]
    public void Setup()
    {
        _stateEvaluator = new StateEvaluator();
        _stateAggregator = new StateAggregator(_stateEvaluator, new InMemoryLockService());
    }

    [Test]
    public async Task EvaluateExpressionAsync_UsrVarDict()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        await usrDict.SetAsync("hello", "world");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnWebhookEventBody(DateTimeOffset.UtcNow, "{}", string.Empty),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ usr_var_dict.hello }}"),
            Is.EqualTo("world"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ usr_var_dict.world }}"),
            Is.EqualTo(null));
        Assert.ThrowsAsync<SfScriptingException>(
            async () =>
            {
                await _stateEvaluator.EvaluateExpressionAsync(state, "{{ a = 1 }}");
            });

        Assert.That(
            await _stateEvaluator.EvaluateTemplateStringExpressionAsync(state, "Hello {{ usr_var_dict.hello }}, \nit's\tflow builder {{ usr_var_dict.hello }}"),
            Is.EqualTo("Hello world, \nit's\tflow builder world"));
    }

    [Test]
    public async Task EvaluateExpressionAsync_TriggerEventBody()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactCreatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>
                {
                    {
                        "id", "123"
                    },
                    {
                        "name", "John Doe"
                    },
                    {
                        "email", "<EMAIL>"
                    },
                    {
                        "phone", "*********"
                    },
                    {
                        "address", "123 Main St"
                    },
                    {
                        "city", "New York"
                    },
                    {
                        "state", "NY"
                    },
                    {
                        "zip", "12345"
                    },
                    {
                        "country", "USA"
                    },
                    {
                        "createdAt", new DateTimeOffset(2023, 02, 01, 01, 0, 0, 123, TimeSpan.Zero).UtcDateTime
                    },
                    {
                        "updatedAt", new DateTimeOffset(2023, 02, 01, 01, 0, 0, 666, TimeSpan.Zero).UtcDateTime
                    },
                    {
                        "bool", true
                    },
                },
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.id }}"),
            Is.EqualTo("123"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.name }}"),
            Is.EqualTo("John Doe"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.email }}"),
            Is.EqualTo("<EMAIL>"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.address }}"),
            Is.EqualTo("123 Main St"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.city }}"),
            Is.EqualTo("New York"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.state }}"),
            Is.EqualTo("NY"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.zip }}"),
            Is.EqualTo("12345"));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ trigger_event_body.created_contact.bool }}"),
            Is.EqualTo(true));
        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ trigger_event_body.created_contact.country == 'USA' }}"),
            Is.EqualTo(true));

        var createdAtExpression = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            "{{ date.to_string trigger_event_body.created_contact.createdAt '%FT%T%Z' }}");
        Assert.That(
            createdAtExpression,
            Is.EqualTo("2023-02-01T01:00:00+00:00"));

        var updatedAtExpression = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            "{{ date.to_string trigger_event_body.created_contact.updatedAt '%FT%T%Z' }}");
        Assert.That(
            updatedAtExpression,
            Is.EqualTo("2023-02-01T01:00:00+00:00"));
    }

    [Test]
    public async Task EvaluateDictExpressionAsync()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        await usrDict.SetAsync("hello", "world");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnWebhookEventBody(DateTimeOffset.UtcNow, "{}", string.Empty),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var dictionary = await _stateEvaluator.EvaluateDictExpressionAsync(
            state,
            new Dictionary<string, string?>
            {
                {
                    "abc", "{{ usr_var_dict.hello }}"
                },
                {
                    "hello", "{{ usr_var_dict.hello + '123' }}"
                }
            });

        Assert.That(
            dictionary,
            new DictionaryContainsKeyValuePairConstraint("abc", "world"));
        Assert.That(
            dictionary,
            new DictionaryContainsKeyValuePairConstraint("hello", "world123"));
    }

    [Test]
    public async Task EvaluateExpressionAsync_Array()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnWebhookEventBody(DateTimeOffset.UtcNow, "{}", string.Empty),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        await _stateAggregator.AggregateStateAssignAsync(
            state,
            new Assign()
            {
                {
                    "arr1", "{{ [\"123\", \"456\"] }}"
                },
                {
                    "arr2", "{{ [\"111\", \"222\"] }}"
                }
            });

        var arr1 = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            "{{ usr_var_dict.arr1 }}");
        var concat = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            "{{ usr_var_dict.arr1 | array.concat usr_var_dict.arr2 }}");
        var dictionary = await _stateEvaluator.EvaluateDictExpressionAsync(
            state,
            new Dictionary<string, string?>
            {
                {
                    "abc", "{{ usr_var_dict.arr1 | array.concat usr_var_dict.arr2 }}"
                }
            });

        Assert.That(arr1, Is.TypeOf<object[]>());
        Assert.That(concat, Is.TypeOf<ScriptArray>());
        Assert.That(JsonConvert.SerializeObject(concat), Is.EqualTo("[\"123\",\"456\",\"111\",\"222\"]"));

        Assert.That(concat, Has.Exactly(1).Matches<string>(p => p == "123"));
        Assert.That(concat, Has.Exactly(1).Matches<string>(p => p == "456"));
        Assert.That(concat, Has.Exactly(1).Matches<string>(p => p == "111"));
        Assert.That(concat, Has.Exactly(1).Matches<string>(p => p == "222"));

        Assert.That(dictionary["abc"], Has.Exactly(1).Matches<string>(p => p == "123"));
        Assert.That(dictionary["abc"], Has.Exactly(1).Matches<string>(p => p == "456"));
        Assert.That(dictionary["abc"], Has.Exactly(1).Matches<string>(p => p == "111"));
        Assert.That(dictionary["abc"], Has.Exactly(1).Matches<string>(p => p == "222"));
    }

    [Test]
    public async Task EvaluateExpressionAsync_Array_Some()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnWebhookEventBody(DateTimeOffset.UtcNow, "{}", string.Empty),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var b = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            "{{ [1, 3, 5, 6] | array.some @(do; ret $0 % 2 == 0; end) }}");
        var dictionary = await _stateEvaluator.EvaluateDictExpressionAsync(
            state,
            new Dictionary<string, string?>
            {
                {
                    "abc", "{{ [1, 3, 5, 6] | array.some @(do; ret $0 % 2 == 0; end) }}"
                }
            });

        Assert.That(b, Is.TypeOf<bool>());
        Assert.That(b, Is.EqualTo(true));
        Assert.That(dictionary["abc"], Is.EqualTo(true));
    }

    [Test]
    public async Task EvaluateExpressionAsync_Array_Some_String()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnWebhookEventBody(DateTimeOffset.UtcNow, "{}", string.Empty),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var b = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            "{{ ['abc', 'd', 'aa', 'c'] | array.some @(do; ret string.contains $0 'a'; end) }}");
        var dictionary = await _stateEvaluator.EvaluateDictExpressionAsync(
            state,
            new Dictionary<string, string?>
            {
                {
                    "abc", "{{ ['abc', 'd', 'aa', 'c'] | array.some @(do; ret string.contains $0 'a'; end) }}"
                }
            });

        Assert.That(b, Is.TypeOf<bool>());
        Assert.That(b, Is.EqualTo(true));
        Assert.That(dictionary["abc"], Is.EqualTo(true));

        var b2 = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            "{{ ['abc', 'd', 'aa', 'c'] | array.some @(do; ret string.contains $0 'e'; end) }}");
        var dictionary2 = await _stateEvaluator.EvaluateDictExpressionAsync(
            state,
            new Dictionary<string, string?>
            {
                {
                    "abc", "{{ ['abc', 'd', 'aa', 'c'] | array.some @(do; ret string.contains $0 'e'; end) }}"
                }
            });

        Assert.That(b2, Is.TypeOf<bool>());
        Assert.That(b2, Is.EqualTo(false));
        Assert.That(dictionary2["abc"], Is.EqualTo(false));
    }

    [Test]
    public async Task EvaluateExpressionAsync_Array_Some_And_Every_Array()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>()
                {
                    {
                        "id", Guid.Empty.ToString()
                    },
                    {
                        "name", "Leo Good Good"
                    },
                    {
                        "age", 18
                    }
                },
                new Dictionary<string, object?>()
                {
                    {
                        "id", Guid.Empty.ToString()
                    },
                    {
                        "name", "Leo"
                    },
                    {
                        "age", 18
                    }
                },
                new List<OnContactUpdatedEventBodyChangeEntry>()
                {
                    new (
                        "name",
                        Guid.Empty.ToString(),
                        "Leo",
                        "Leo Good Good")
                },
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var conditions = new List<string>()
        {
            "trigger_event_body.change_entries | array.some @(do; ret $0.property_name == 'name' && string.contains $0.to_value 'Leo'; end)",
            "trigger_event_body.change_entries | array.every @(do; ret $0.property_name != 'age'; end)"
        };

        var combinedExpression = "{{ " + string.Join(" && ", conditions.Select(c => "(" + c + ")")) + " }}";

        var b = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            combinedExpression);
        var dictionary = await _stateEvaluator.EvaluateDictExpressionAsync(
            state,
            new Dictionary<string, string?>
            {
                {
                    "abc", combinedExpression
                }
            });

        Assert.That(b, Is.TypeOf<bool>());
        Assert.That(b, Is.EqualTo(true));
        Assert.That(dictionary["abc"], Is.EqualTo(true));
    }

    [Test]
    public async Task EvaluateExpressionAsync_Step_SendMessageStep()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>()
                {
                    {
                        "id", Guid.Empty.ToString()
                    },
                    {
                        "name", "Leo Good Good"
                    },
                    {
                        "age", 18
                    }
                },
                new Dictionary<string, object?>()
                {
                    {
                        "id", Guid.Empty.ToString()
                    },
                    {
                        "name", "Leo"
                    },
                    {
                        "age", 18
                    }
                },
                new List<OnContactUpdatedEventBodyChangeEntry>()
                {
                    new (
                        "name",
                        Guid.Empty.ToString(),
                        "Leo",
                        "Leo Good Good")
                },
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var expr = "{{ { 'text_message': {'text': 'Welcome!'} } }}";

        var messageBodyObj =
            await _stateEvaluator.EvaluateExpressionAsync(state, expr)
            ?? expr;
        var messageBody = JsonConvert.DeserializeObject<MessageBody>(
            JsonConvert.SerializeObject(messageBodyObj, JsonConfig.DefaultJsonSerializerSettings),
            JsonConfig.DefaultJsonSerializerSettings)!;
        var messageBodyStr =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ ({ 'text_message': {'text': 'Welcome!'} }) | json.serialize }}");

        Assert.That(messageBodyStr, Is.EqualTo("{\"text_message\":{\"text\":\"Welcome!\"}}"));
        Assert.That(messageBody, Is.Not.Null);
        Assert.That(messageBody, Is.TypeOf<MessageBody>());
        Assert.That(messageBody.TextMessage, Is.Not.Null);
        Assert.That(messageBody.TextMessage!.Text, Is.EqualTo("Welcome!"));
    }

    [Test]
    public async Task EvaluateExpressionAsync_Nested_Template()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");
        var nestedObj = new Dictionary<string, object?>
        {
            ["interactive_message"] = new Dictionary<string, object?>
            {
                ["type"] = "list",
                ["body"] = new Dictionary<string, object?>
                {
                    ["text"] = "this is list"
                },
                ["action"] = new Dictionary<string, object?>
                {
                    ["button"] = "title"
                }
            },
            ["template_message"] = null,
            ["facebook_messenger_message"] = null,
            ["instagram_messenger_message"] = null
        };

        await usrDict.SetAsync("firstName", "Neville");
        await usrDict.SetAsync("lastName", "LIN");
        await sysDict.SetAsync("companyName", "Neville's Company");
        await sysDict.SetAsync("messageType", "{{message}}");
        await usrDict.SetAsync("usrName", "fluffyBei");
        await usrDict.SetAsync("userName", "usr_var_dict.usrName");
        await usrDict.SetAsync("nestedObj", nestedObj);
        await usrDict.SetAsync("Neville", "nested_template_Neville_second_evalulation");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>(),
                new Dictionary<string, object?>(),
                new List<OnContactUpdatedEventBodyChangeEntry>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var test1 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ 'Hello {{Dear {{usr_var_dict.firstName}}}} {{usr_var_dict.lastName}}, you received a message' | template.eval | template.eval}}");

        var test2 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ 'Hello {{usr_var_dict.{{usr_var_dict.firstName}}}} {{usr_var_dict.lastName}}, you received a message' | template.eval | template.eval}}");

        var test3 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ 'Hey {{usr_var_dict.firstName}} {{usr_var_dict.lastName}}, you received a {{sys_var_dict.messageType}} from {{sys_var_dict.companyName}}' | template.eval}}");

        var test4 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ 'My name is {{usr_var_dict.firstName}}, my cat is {{{{usr_var_dict.userName}}}}' | template.eval | template.eval}}");

        var test5 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ ({ 'text_message': {'text': 'Welcome {{usr_var_dict.firstName}} {{usr_var_dict.lastName}}!'} }) | json.serialize | template.eval}}");

        var test6 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{'I am {{ usr_var_dict.firstName == Leo }} Leo' | template.eval}}");

        var test7 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{'I am {{}}Leo' | template.eval}}");

        var test8 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ {'hello': 'world', 'test': ('very handsome {{usr_var_dict.firstName}}' | template.eval)} | json.serialize}}");

        var test9 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ {'hello': 'world', 'test': ('test.{{usr_var_dict.{{usr_var_dict.firstName}}}}' | template.eval | template.eval)} | json.serialize }}");

        var test10 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                "{{ {'hello': 'world', 'test': ('very handsome {{{{usr_var_dict.firstName}}}}' | template.eval )} | json.serialize }}");

        var test11 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                """{{ '@Irish Isip Hi, {{usr_var_dict["firstName"]}} {{usr_var_dict["lastName"]}}' | template.eval }}""");

        var test12 = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            """{{ '{"message":{{ usr_var_dict.nestedObj | json.serialize }}}' | template.eval }}""");

        var test13 = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            """{{ '{{usr_var_dict.contact ?? ""}}' | template.eval}}""");

        var test14 = (AsyncTestDelegate) (() => _stateEvaluator.EvaluateExpressionAsync(
            state,
            """{{ 'Hello {{usr_var_dict.{{usr_var_dict.notExistVar}}}} {{usr_var_dict.lastName}}, you received a message' | template.eval | template.eval }}"""));

        var test15 = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            """{{ 'Hello {{usr_var_dict["{{usr_var_dict.notExistVar}}"]}} {{usr_var_dict.lastName}}, you received a message' | template.eval | template.eval }}""");

        var test16 = await _stateEvaluator.EvaluateExpressionAsync(
            state,
            """{{ '{"field": "{{ usr_var_dict.nestedObj["not_exist_field_name"] ?? "" }}"}'  | template.eval }}""");

        Assert.That(test1, Is.EqualTo("Hello Dear Neville LIN, you received a message"));
        Assert.That(test2, Is.EqualTo("Hello nested_template_Neville_second_evalulation LIN, you received a message"));
        Assert.That(test3, Is.EqualTo("Hey Neville LIN, you received a message from Neville's Company"));
        Assert.That(test4, Is.EqualTo("My name is Neville, my cat is fluffyBei"));
        Assert.That(test5, Is.EqualTo("{\"text_message\":{\"text\":\"Welcome Neville LIN!\"}}"));
        Assert.That(test6, Is.EqualTo("I am False Leo"));
        Assert.That(test7, Is.EqualTo("I am Leo"));
        Assert.That(test8, Is.EqualTo("{\"hello\":\"world\",\"test\":\"very handsome Neville\"}"));
        Assert.That(
            test9,
            Is.EqualTo("{\"hello\":\"world\",\"test\":\"test.nested_template_Neville_second_evalulation\"}"));
        Assert.That(test10, Is.EqualTo("{\"hello\":\"world\",\"test\":\"very handsome {{Neville}}\"}"));
        Assert.That(test11, Is.EqualTo("@Irish Isip Hi, Neville LIN"));
        Assert.That(
            test12,
            Is.EqualTo(
                JsonConvert.SerializeObject(
                    new
                    {
                        message = nestedObj
                    })));
        Assert.That(
            test13,
            Is.EqualTo(string.Empty));
        Assert.Multiple(
            () =>
            {
                var ex = Assert.ThrowsAsync<SfScriptingException>(test14);

                Assert.That(ex, Is.Not.Null);
                Assert.That(ex!.InnerException?.InnerException?.InnerException?.Message, Does.Contain("The dot operator is expected to be followed by a plain identifier"));
            });
        Assert.That(test15, Is.EqualTo("Hello  LIN, you received a message"));
        Assert.That(test16, Is.EqualTo("{\"field\": \"\"}"));
    }

    [Test]
    public async Task EvaluateExpressionAsync_RandomStr()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new CaseInsensitiveDictionary<object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>(),
                new Dictionary<string, object?>(),
                new List<OnContactUpdatedEventBodyChangeEntry>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var test1 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                """{{ "" + (math.random 10000000 99999999) | string.sha256 | string.truncate 8 '' }}""");

        Assert.That((string) test1!, Has.Length.EqualTo(8));
    }

    [Test]
    public async Task EvaluateExpressionAsync_DateTimeApplyTimeZone()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>(),
                new Dictionary<string, object?>(),
                new List<OnContactUpdatedEventBodyChangeEntry>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        var test1 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                """{{ date.parse "2023-09-21T09:32:34.2755608Z" | date.apply_time_zone "US Eastern Standard Time" | date.to_string_with_offset '%F %T' }}""");

        var test2 =
            await _stateEvaluator.EvaluateExpressionAsync(
                state,
                """{{ date.parse "2023-09-21T09:32:34.2755608Z" | date.apply_time_zone "China Standard Time" | date.to_string_with_offset '%v %T' }}""");
        StringAssert.AreEqualIgnoringCase("2023-09-21 05:32:34 (UTC-04:00:00)", (string) test1!);
        StringAssert.AreEqualIgnoringCase("21-SEP-2023 17:32:34 (UTC+08:00:00)", (string) test2!);
    }

    [Test]
    [TestCase("", "yyyy-MM-ddTHH:mmK", null)]
    [TestCase(null, "yyyy-MM-ddTHH:mmK", null)]
    [TestCase("2023-12-14T13:30+08:00", "yyyy-MM-ddTHH:mmK", "2023-12-14T05:30:00Z")]
    [TestCase("2023-12-14T13:30:49+08:00", "yyyy-MM-ddTHH:mm:ssK", "2023-12-14T05:30:49Z")]
    [TestCase("12-14-2023T13:30+08:00", "MM-dd-yyyyTHH:mmK", "2023-12-14T05:30:00Z")]
    public async Task EvaluateExpressionAsync_ParseDateTimeString(
        string? input,
        string? format,
        string? expected)
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>(),
                new Dictionary<string, object?>(),
                new List<OnContactUpdatedEventBodyChangeEntry>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        await state.UsrVarDict.GetInnerDictionary().SetAsync("input", input);
        await state.UsrVarDict.GetInnerDictionary().SetAsync("format", format);

        var result =
            (DateTime?) await _stateEvaluator.EvaluateExpressionAsync(
                state,
                """{{ date.parse_date_time_string usr_var_dict["input"] usr_var_dict["format"] }}""");

        if (string.IsNullOrWhiteSpace(expected))
        {
            Assert.That(result, Is.Null);
        }
        else
        {
            Assert.Multiple(
                () =>
                {
                    Assert.That(result, Is.Not.Null);
                    Assert.That(
                        DateTime.Parse(expected, DateTimeFormatInfo.InvariantInfo),
                        Is.EqualTo(result!));
                });
        }
    }

    [Test]
    [TestCase("", "yyyy-MM-dd", null)]
    [TestCase(null, "yyyy-MM-dd", null)]
    [TestCase("2023-12-14", "yyyy-MM-dd", "2023-12-14")]
    [TestCase("12-14-2023", "MM-dd-yyyy", "2023-12-14")]
    public async Task EvaluateExpressionAsync_ParseDateString(
        string? input,
        string? format,
        string? expected)
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>(),
                new Dictionary<string, object?>(),
                new List<OnContactUpdatedEventBodyChangeEntry>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        await state.UsrVarDict.GetInnerDictionary().SetAsync("input", input);
        await state.UsrVarDict.GetInnerDictionary().SetAsync("format", format);

        var result =
            (DateTime?) await _stateEvaluator.EvaluateExpressionAsync(
                state,
                """{{ date.parse_date_string usr_var_dict["input"] usr_var_dict["format"] }}""");

        if (string.IsNullOrWhiteSpace(expected))
        {
            Assert.That(result, Is.Null);
        }
        else
        {
            Assert.Multiple(
                () =>
                {
                    Assert.That(result, Is.Not.Null);
                    Assert.That(
                        DateOnly.Parse(expected, DateTimeFormatInfo.InvariantInfo, DateTimeStyles.None)
                            .ToDateTime(TimeOnly.MinValue),
                        Is.EqualTo(result!));
                });
        }
    }

    [Test]
    [TestCase("", null)]
    [TestCase(null, null)]
    [TestCase(true, null)]
    [TestCase("1702534655", "2023-12-14T06:17:35Z")]
    public async Task EvaluateExpressionAsync_ParseUnixTimestamp(
        object? input,
        string? expected)
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnContactUpdatedEventBody(
                DateTimeOffset.UtcNow,
                "staffId",
                null,
                new Dictionary<string, object?>(),
                new Dictionary<string, object?>(),
                new List<OnContactUpdatedEventBodyChangeEntry>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        await state.UsrVarDict.GetInnerDictionary().SetAsync("input", input);

        var result =
            (DateTime?) await _stateEvaluator.EvaluateExpressionAsync(
                state,
                """{{ date.parse_unix_timestamp usr_var_dict["input"] }}""");

        if (string.IsNullOrWhiteSpace(expected))
        {
            Assert.That(result, Is.Null);
        }
        else
        {
            Assert.Multiple(
                () =>
                {
                    Assert.That(result, Is.Not.Null);
                    Assert.That(
                        DateTime.Parse(expected, DateTimeFormatInfo.InvariantInfo, DateTimeStyles.AdjustToUniversal),
                        Is.EqualTo(result!));
                });
        }
    }

    [Test]
    public async Task EvaluateExpressionAsync_StreamingVar()
    {
        var usrDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("usr_dict");
        var sysDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_dict");
        var sysCompanyDict = Application.Cluster.GrainFactory.GetGrain<IDictGrain>("sys_company_dict");

        var state = new ProxyState(
            string.Empty,
            new StateIdentity(string.Empty, "my-company-id-1", string.Empty, string.Empty, "Contact"),
            new ProxyStateWorkflowContext(
                new ProxyWorkflow(
                    new Workflow(
                        "my-workflow-1",
                        "my-workflow-1-1",
                        "My Workflow 1",
                        WorkflowType.Normal,
                        workflowGroupId: null,
                        new WorkflowTriggers(
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        WorkflowEnrollmentSettings.Default(),
                        WorkflowScheduleSettings.Default(),
                        new List<Step>(),
                        WorkflowActivationStatuses.Active,
                        "my-workflow-1-1",
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        new DateTimeOffset(2023, 1, 1, 1, 1, 1, TimeSpan.Zero),
                        "my-company-id-1",
                        null,
                        null,
                        new Dictionary<string, object?>(),
                        "v1",
                        null),
                    new List<Step>(),
                    new Dictionary<string, object?>())),
            new OnWebhookEventBody(DateTimeOffset.UtcNow, "{}", string.Empty),
            "https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net",
            new AsyncDictionaryWrapper<string, object?>(usrDict),
            new AsyncDictionaryWrapper<string, object?>(sysDict),
            null,
            new AsyncDictionaryWrapper<string, object?>(sysCompanyDict),
            StateStatuses.Running,
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            false);

        Assert.That(
            await _stateEvaluator.EvaluateExpressionAsync(state, "{{ streaming_var \"b7def298-f282-4a49-b901-f767ac97416a\" }}"),
            Is.EqualTo("{{ streaming_var \"b7def298-f282-4a49-b901-f767ac97416a\" }}"));
    }
}