﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Alba" Version="8.1.1" />
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="Microsoft.Extensions.AI" Version="9.5.0" />
        <PackageReference Include="Microsoft.Extensions.AI.Evaluation" Version="9.5.0" />
        <PackageReference Include="Microsoft.Extensions.AI.Evaluation.Quality" Version="9.5.0" />
        <PackageReference Include="Microsoft.Extensions.AI.Evaluation.Reporting" Version="9.5.0" />
        <PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.5.0-preview.1.25265.7" />
        <PackageReference Include="Microsoft.ML.Tokenizers.Data.Cl100kBase" Version="1.0.1" />
        <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.13.0" />
        <PackageReference Include="Microsoft.SemanticKernel.Abstractions" Version="1.54.0" />
        <PackageReference Include="Moq" Version="4.20.72" />
        <PackageReference Include="NUnit" Version="4.3.2" />
        <PackageReference Include="NUnit.Analyzers" Version="4.6.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="NUnit3TestAdapter" Version="5.0.0" />
        <PackageReference Include="OpenAI" Version="2.2.0-beta.4" />
        <PackageReference Include="Tiktoken" Version="2.2.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.IntelligentHub\Sleekflow.IntelligentHub.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc.Tests\Sleekflow.Mvc.Tests.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Folder Include="nunit-logs\" />
    </ItemGroup>

</Project>