using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class ToggleFlowHubUsageLimit : ITrigger
{
    private readonly IFlowHubConfigService _flowHubConfigService;

    public ToggleFlowHubUsageLimit(
        IFlowHubConfigService flowHubConfigService)
    {
        _flowHubConfigService = flowHubConfigService;
    }

    public class ToggleFlowHubUsageLimitInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("is_usage_limit_enabled")]
        [Required]
        public bool IsUsageLimitEnabled { get; set; }

        [JsonConstructor]
        public ToggleFlowHubUsageLimitInput(
            string sleekflowCompanyId,
            bool isUsageLimitEnabled)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            IsUsageLimitEnabled = isUsageLimitEnabled;
        }
    }

    public class ToggleFlowHubUsageLimitOutput
    {
        [JsonProperty("flow_hub_config")]
        public FlowHubConfig FlowHubConfig { get; set; }

        [JsonConstructor]
        public ToggleFlowHubUsageLimitOutput(
            FlowHubConfig flowHubConfig)
        {
            FlowHubConfig = flowHubConfig;
        }
    }

    public async Task<ToggleFlowHubUsageLimitOutput> F(ToggleFlowHubUsageLimitInput updateFlowHubConfigInput)
    {
        var flowHubConfig = await _flowHubConfigService.ToggleFlowUsageLimitAsync(
            updateFlowHubConfigInput.SleekflowCompanyId,
            updateFlowHubConfigInput.IsUsageLimitEnabled);

        return new ToggleFlowHubUsageLimitOutput(flowHubConfig);
    }
}