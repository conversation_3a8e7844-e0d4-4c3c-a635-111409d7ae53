using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Workflows;

public class WorkflowFilters : IValidatableObject
{
    [JsonProperty("activation_status")]
    [RegularExpression($"{WorkflowActivationStatuses.Active}|{WorkflowActivationStatuses.Draft}")]
    public string? ActivationStatus { get; set; }

    [JsonProperty("created_by_sleekflow_staff_id")]
    public string? CreatedBySleekflowStaffId { get; set; }

    [JsonProperty("updated_by_sleekflow_staff_id")]
    public string? UpdatedBySleekflowStaffId { get; set; }

    [JsonProperty("updated_from_date_time")]
    public DateTimeOffset? UpdatedFromDateTime { get; set; }

    [JsonProperty("updated_to_date_time")]
    public DateTimeOffset? UpdatedToDateTime { get; set; }

    [JsonProperty("workflow_type")]
    public string? WorkflowType { get; set; }

    [JsonProperty("dependency_workflow_id")]
    public string? DependencyWorkflowId { get; set; }

    [JsonProperty("agent_config_id")]
    public string? AgentConfigId { get; set; }

    [JsonConstructor]
    public WorkflowFilters(
        string? activationStatus,
        string? createdBySleekflowStaffId,
        string? updatedBySleekflowStaffId,
        DateTimeOffset? updatedFromDateTime,
        DateTimeOffset? updatedToDateTime,
        string? workflowType,
        string? dependencyWorkflowId,
        string? agentConfigId = null)
    {
        ActivationStatus = activationStatus;
        CreatedBySleekflowStaffId = createdBySleekflowStaffId;
        UpdatedBySleekflowStaffId = updatedBySleekflowStaffId;
        UpdatedFromDateTime = updatedFromDateTime;
        UpdatedToDateTime = updatedToDateTime;
        WorkflowType = workflowType;
        DependencyWorkflowId = dependencyWorkflowId;
        AgentConfigId = agentConfigId;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        if (UpdatedFromDateTime.HasValue && !UpdatedToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both UpdatedFromDateTime and UpdatedToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(UpdatedFromDateTime)
                });
        }

        if (!UpdatedFromDateTime.HasValue && UpdatedToDateTime.HasValue)
        {
            yield return new ValidationResult(
                "Both UpdatedFromDateTime and UpdatedToDateTime must be filled or both should be left empty",
                new[]
                {
                    nameof(UpdatedToDateTime)
                });
        }

        if (UpdatedFromDateTime.HasValue && UpdatedToDateTime.HasValue)
        {
            if (UpdatedFromDateTime.Value > UpdatedToDateTime.Value)
            {
                yield return new ValidationResult(
                    "UpdatedFromDateTime can't be greater than UpdatedToDateTime",
                    new[]
                    {
                        nameof(UpdatedFromDateTime),
                        nameof(UpdatedToDateTime)
                    });
            }
        }
    }
}