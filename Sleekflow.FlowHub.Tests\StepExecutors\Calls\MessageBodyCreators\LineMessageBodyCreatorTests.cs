using NUnit.Framework;
using Sleekflow.Constants;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

namespace Sleekflow.FlowHub.Tests.StepExecutors.Calls.MessageBodyCreators;

[TestFixture]
public class LineMessageBodyCreatorTests
{
    private LineMessageBodyCreator _creator;
    private SendMessageV2StepArgs _args;

    [SetUp]
    public void Setup()
    {
        _creator = new LineMessageBodyCreator();
        _args = new SendMessageV2StepArgs(
            channelType: ChannelTypes.Line,
            channelIdentityId: "test-channel-id",
            messageExpr: "Test message",
            mediaParameters: null,
            whatsAppCloudApiMessageParameters: null,
            facebookMessageParameters: null,
            deliveryTypeExpr: null,
            staffIdExpr: null);
    }

    [Test]
    public void CanHandle_GivenLineChannel_ShouldReturnTrue()
    {
        // Act
        var result = _creator.CanHandle(ChannelTypes.Line);

        // Assert
        Assert.That(result, Is.True);
    }

    [Test]
    public void CanHandle_GivenNonLineChannel_ShouldReturnFalse()
    {
        // Act
        var result = _creator.CanHandle(ChannelTypes.Facebook);

        // Assert
        Assert.That(result, Is.False);
    }

    [Test]
    public async Task CreateMessageBodyAsync_GivenTextMessage_ShouldCreateCorrectMessageBody()
    {
        // Arrange
        const string messageText = "Hello, Line!";

        // Act
        var (body, type) = await _creator.CreateMessageBodyAndMessageTypeAsync(messageText, _args);

        // Assert
        Assert.That(body, Is.Not.Null);
        Assert.That(body.LineMessage, Is.Not.Null);
        Assert.That(body.LineMessage!.Type, Is.EqualTo("text"));
        Assert.That(body.LineMessage.Text, Is.EqualTo(messageText));
        Assert.That(type, Is.EqualTo("text"));
    }
}