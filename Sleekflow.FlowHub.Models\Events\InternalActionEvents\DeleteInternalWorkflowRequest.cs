﻿using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Events.InternalActionEvents;

public class DeleteInternalWorkflowRequest : IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public string WorkflowId { get; set; }

    public DeleteInternalWorkflowRequest(string sleekflowCompanyId, string workflowId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
    }
}