﻿using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileRecoveredLogData
{
    [JsonProperty("trigger_details")]
    public UserProfileRecoveredTriggerLogData TriggerDetails { get; set; }

    [JsonConstructor]
    public UserProfileRecoveredLogData(UserProfileRecoveredTriggerLogData triggerDetails)
    {
        TriggerDetails = triggerDetails;
    }
}

public class UserProfileRecoveredTriggerLogData
{
    [JsonProperty("trigger_source")]
    public string TriggerSource { get; set; }

    [JsonProperty("staff_id")]
    public string? StaffId { get; set; }

    [JsonProperty("staff_name")]
    public string? StaffName { get; set; }

    [JsonConstructor]
    public UserProfileRecoveredTriggerLogData(
        string triggerSource,
        string? staffId,
        string? staffName)
    {
        TriggerSource = triggerSource;
        StaffId = staffId;
        StaffName = staffName;
    }
}