using Sleekflow.CrmHub.Models.Entities;

namespace Sleekflow.CrmHub.Unifies;

public class SnapshottedValueDictWrapper
{
    private readonly Dictionary<string, SnapshottedValue?> _dict;
    private DateTimeOffset? _latestSnapshotTime;

    public SnapshottedValueDictWrapper(Dictionary<string, SnapshottedValue?> dict)
    {
        _dict = dict;
    }

    public object? this[string key]
    {
        get
        {
            var valueOrDefault = _dict.GetValueOrDefault(key);
            if (valueOrDefault == null)
            {
                return null;
            }

            if (_latestSnapshotTime == null)
            {
                _latestSnapshotTime = valueOrDefault.SnapshotTime;
            }
            else if (valueOrDefault.SnapshotTime > _latestSnapshotTime)
            {
                _latestSnapshotTime = valueOrDefault.SnapshotTime;
            }

            if (valueOrDefault.Value == null)
            {
                return null;
            }

            // https://www.newtonsoft.com/json/help/html/serializationguide.htm
            if (valueOrDefault.Value is
                string
                or int
                or float or double or decimal
                or DateTime or DateTimeOffset
                or Guid)
            {
                return valueOrDefault.Value;
            }
            else
            {
                return null;
            }
        }
        set => throw new NotImplementedException();
    }

    public DateTimeOffset? GetLatestSnapshotTime()
    {
        return _latestSnapshotTime;
    }

    public SnapshottedValueDictWrapper NewInstance()
    {
        return new SnapshottedValueDictWrapper(_dict);
    }

    public override bool Equals(object? obj)
    {
        return false;
    }

    public override string ToString()
    {
        return "d";
    }

    public override int GetHashCode()
    {
        return _dict.GetHashCode();
    }
}