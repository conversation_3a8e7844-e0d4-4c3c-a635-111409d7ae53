config:
  auth0:client_id:
    secure: AAABAMlBTHRu1CAD7F1/BKl/nvn/TMqR8d9+KZ6fLYcBjIVsDqpiEh/p36piSfRe7Ox3+GQuf0lA95k76pk/hw==
  auth0:client_secret:
    secure: AAABAJwb2tVaLAnJpnr5FCaxAFIBk+3BrgsQpDtO6cKiFj1OVfAIhN7ENdv7oJaYmcpmpv7f0y0l9+u7z7vgb2KSm960FsyfEABcw7OZV2mj8isE1sXyyPMGDjehkYV8
  auth0:domain: sleekflow-staging.eu.auth0.com
  core_sql_db:connection_string_eas:
    secure: AAABAGkXE7klBlba4/E6E6pMpRhMt+uRTQDnPkpvu8452/C1s2bYp85TFAeztKSPsECjgXxD4Ccv5szyUi3aNOTL1I14sQXWbH8CC2zwyXhTkgeAI4wgvnOeAu7PnCoMoslrL8nXVhjIrHrVOguPu2Ac0S0iEz5FlgfzGtO8IqGhom3fJaUCirfmNOBeoiLKmsmEzq60rDc+iphfw0KoDCtixhOZG4Izq7UQgnTdkx9N0evkQSfIKwY34PxZpbRJwK3ecm1cFQBGUdknG8H0dGDeXmIG0+0sFtjhVvCSfodPCXjJt3iM+TPwJGtE22/2BdxxWFtnD4OW6yCTKttC3LyEX1uj2yrk/opvWB8niu8G6RqBo6IaN76uw6bNZ/luPpn9a8q6udR/KxdQW4QrkCEpl5JTd3EY8ylJbKrrFE4LTIeA9C8hoo2vOvuAY5WzI1L2zSO/R/EO9nx4HNd4TCoy2CWLetNkGrgUjXIifkwpD92OhJ6558KTKDLIKDqpxqSLRMCcdaGu5SZLsJJBmulwkzx6aQrGCQadHY7DTIjTjLo09PwHKkBmKn+ocoT6DZj1aQXDMtwd/3SIfhXgCDpbWbOmRK5GnSg=
  core_sql_db:connection_string_eus:
    secure: AAABAL7WOj9GsO4nuzB/YDebEs9HhnjT/foFeSFjsH74oCHMYJUBCNo6ydL/RzEeAwEcAmQR1waDlNXWffjKGVAo2FNBD9aGDCEjMXmoC2H9fbVOPWsLogqCNoFNLFKICo7P1zaAdg5sEA2yUPZ7sLfn0TRG5OKlMuxbiYcGYg30Th+ORRAKprGnnm1T/z7C4AtQXpJ+7dfo7btTJSc0pUG+PnMC6orPr1naiidH1vQiEJrkyV8+u5Rh+IhOob95E19ZG5AW+weq5f+AlE1IU1xenGvn/ersXGqDIhJLIx3yvOOAaKIDMwMjIq9iLlYptnHiiyNKvmpkP6rrdP2hCHUcAae4bekLQLW/MUt3gyszEX7inGDo5gWIsLUDjuWx+f/A3BmXWGUGatWm4f9+OqMvWDLGEJAwDhTXoqk8YleDx+9KOHxV0JI1ePgEKV9emTlwH50PEZu3rgo5D2JzLpFOqoPjsaxtAbW3JC8z0lUg8sH9vQ/9X0w9aai1eRf/2STsqp5jMcbxp8bITcK6iwqgWbhorGh4p99aCK4lLIfkrNnhBdGBMAcXj57259KGg3hSzFCuB2DcDMSuA5xgLOEm/nAfh/4cZX8=
  core_sql_db:connection_string_seas:
    secure: AAABAA2ITrLnaZdb6l2/BqvaEPNYvhYUYVzmeO9i/yE=
  core_sql_db:connection_string_uaen:
    secure: AAABAD36CupZ87KxRDSGgx9LS3tPrK+r53WWOvOiQWE=
  core_sql_db:connection_string_weu:
    secure: AAABAIsU18dODRGk8+RLZ93QdVwQTe+D03hYyvTDlWQ=
  azure-native:location: EastAsia
  pulumi:template: azure-csharp
  gcp:project-id: my-staging-project-405815
  gcp:credential-json: ************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
  sleekflow:name: staging
  sleekflow:origins:
    - https://staging.sleekflow.io
    - https://localhost:3000
    - http://localhost:3000
    - https://staging-revamp.sleekflow.io
    - https://localhost:5173
    - http://localhost:5173
    - https://staging2.sleekflow.io
    - https://staging2-revamp.sleekflow.io
    - https://v1-staging.sleekflow.io
  sleekflow:clients:
    - name: sleekflow-api
      allowed_logout_urls: [ ]
      allowed_origins:
        - https://sleekflow-prod-api-staging.azurewebsites.net
        - https://localhost:5173
        - http://localhost:5173
      app_type: non_interactive
      callbacks:
        - https://sleekflow-prod-api-staging.azurewebsites.net
        - https://localhost:5173
        - http://localhost:5173
      grant_types:
        - client_credentials
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      token_endpoint_auth_method: client_secret_post
      web_origins:
        - https://sleekflow-prod-api-staging.azurewebsites.net
        - https://localhost:5173
        - http://localhost:5173

    - name: sleekflow-api-health-check
      allowed_logout_urls: [ ]
      allowed_origins:
        - https://sleekflow-core-app-eas-staging.azurewebsites.net
        - https://sleekflow-powerflow-app-eas-staging.azurewebsites.net
      app_type: non_interactive
      callbacks:
        - https://sleekflow-core-app-eas-staging.azurewebsites.net
        - https://sleekflow-powerflow-app-eas-staging.azurewebsites.net
      grant_types:
        - client_credentials
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      token_endpoint_auth_method: client_secret_post
      web_origins:
        - https://localhost:5000
        - https://sleekflow-core-app-eas-dev.azurewebsites.net

    - name: sleekflow-client-powerflow-app
      initiate_login_uri: https://powerflowdev.z7.web.core.windows.net/login
      allowed_logout_urls:
        - https://powerflow.sleekflow.io
        - https://powerflow.sleekflow.io/logout
        - https://powerflowdev.z7.web.core.windows.net
        - https://powerflowdev.z7.web.core.windows.net/logout
      allowed_origins:
        - https://powerflow.sleekflow.io
        - https://powerflowdev.z7.web.core.windows.net
      app_type: spa
      callbacks:
        - https://powerflow.sleekflow.io
        - https://powerflow.sleekflow.io/callback
        - https://powerflowdev.z7.web.core.windows.net
        - https://powerflowdev.z7.web.core.windows.net/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      token_endpoint_auth_method: none
      web_origins:
        - https://powerflow.sleekflow.io
        - https://powerflowdev.z7.web.core.windows.net

    - name: sleekflow-client-auth0-actions
      allowed_logout_urls: [ ]
      allowed_origins: [ ]
      app_type: non_interactive
      callbacks: [ ]
      grant_types:
        - client_credentials
      token_endpoint_auth_method: client_secret_post
      web_origins: [ ]

    - name: sleekflow-client-web-app
      initiate_login_uri: https://v1-staging.sleekflow.io
      allowed_logout_urls:
        - https://staging.sleekflow.io
        - https://staging2.sleekflow.io
        - https://localhost:3000
        - http://localhost:3000
        - https://adfs.shkp.com/adfs/ls?wa=wsignoutcleanup1.0
        - https://sso-uat1.hongyip.com/auth/realms/hy-sso-uat1/protocol/openid-connect/logout
        - https://v1-staging.sleekflow.io
      allowed_origins:
        - https://staging.sleekflow.io
        - https://staging2.sleekflow.io
        - https://localhost:3000
        - http://localhost:3000
        - https://v1-staging.sleekflow.io
      app_type: spa
      callbacks:
        - https://staging.sleekflow.io
        - https://staging2.sleekflow.io/
        - https://localhost:3000
        - http://localhost:3000
        - https://staging.sleekflow.io/settings/opt-in
        - https://staging.sleekflow.io/settings/templates
        - https://staging2.sleekflow.io/settings/opt-in
        - https://staging2.sleekflow.io/settings/templates
        - https://v1-staging.sleekflow.io
        - https://v1-staging.sleekflow.io/settings/opt-in
        - https://v1-staging.sleekflow.io/settings/templates
        - https://v1-staging.sleekflow.io/settings/inbox
        - https://v1-staging.sleekflow.io/channels
        - https://v1-staging.sleekflow.io/en-US/settings/templates
        - https://v1-staging.sleekflow.io/zh-HK/settings/templates
        - https://v1-staging.sleekflow.io/zh-CN/settings/templates
        - https://v1-staging.sleekflow.io/pt-BR/settings/templates
        - https://v1-staging.sleekflow.io/it-IT/settings/templates
        - https://v1-staging.sleekflow.io/id-ID/settings/templates
        - https://v1-staging.sleekflow.io/de-DE/settings/templates
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      web_origins:
        - https://localhost:3000
        - http://localhost:3000
        - https://staging.sleekflow.io
        - https://staging2.sleekflow.io
        - https://sleekflow-prod-api-staging.azurewebsites.net
        - https://v1-staging.sleekflow.io

    - name: sleekflow-client-mobile-app
      allowed_logout_urls:
        - https://sso-staging.sleekflow.io/logout/callback
      allowed_origins: [ ]
      app_type: native
      callbacks:
        - sleekflowauth0://sso-staging.sleekflow.io/android/io.sleekflow.sleekflow/callback
        - io.sleekflow.sleekflow://sso-staging.sleekflow.io/ios/io.sleekflow.sleekflow/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      web_origins: [ ]
      native_client_options:
        ios:
          team_id: JNXJD4KQ2C
          app_bundle_identifier: io.sleekflow.app
        android:
          app_package_name: io.sleekflow.sleekflow
          key_hashes:
            - 4B:7A:FA:CF:5F:56:1E:88:C1:0C:01:7A:EA:5F:61:6C:31:0D:AF:18:59:50:C4:9E:FF:32:8E:31:17:0C:4E:86
            - C8:44:11:BE:74:24:10:92:66:74:36:AE:D3:83:47:FC:44:AE:C5:76:0D:5E:2E:5C:E2:4F:9E:1F:7B:EB:5C:4D

    - name: sleekflow-client-reseller-portal-app
      initiate_login_uri: https://partner-uat.sleekflow.io
      app_type: spa
      allowed_logout_urls:
        - https://partner-uat.sleekflow.io
      allowed_origins:
        - https://partner-uat.sleekflow.io
      callbacks:
        - https://partner-uat.sleekflow.io
      web_origins:
        - https://partner-uat.sleekflow.io
      grant_types:
        - authorization_code
        - implicit
        - refresh_token

    - name: sleekflow-client-web-v2-app
      initiate_login_uri: https://staging.sleekflow.io
      allowed_logout_urls:
        - https://staging-revamp.sleekflow.io
        - https://staging2-revamp.sleekflow.io/
        - https://localhost:5173
        - http://localhost:5173
        - https://adfs.shkp.com/adfs/ls?wa=wsignoutcleanup1.0
        - https://sso-uat1.hongyip.com/auth/realms/hy-sso-uat1/protocol/openid-connect/logout
        - https://staging.sleekflow.io
      allowed_origins:
        - https://staging-revamp.sleekflow.io
        - https://staging2-revamp.sleekflow.io/
        - https://localhost:5173
        - http://localhost:5173
        - https://staging.sleekflow.io
      app_type: spa
      callbacks:
        - https://staging-revamp.sleekflow.io
        - https://staging2-revamp.sleekflow.io/
        - https://localhost:5173
        - http://localhost:5173
        - https://staging.sleekflow.io
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
      web_origins:
        - https://staging-revamp.sleekflow.io
        - https://staging2-revamp.sleekflow.io/
        - https://localhost:5173
        - http://localhost:5173
        - https://staging.sleekflow.io

    - name: sleekflow-client-mobile-v2-app
      allowed_logout_urls:
        - https://sso-staging.sleekflow.io/logout/callback
      allowed_origins: [ ]
      app_type: native
      callbacks:
        - sleekflowauth0://sso-staging.sleekflow.io/android/io.sleekflow.v2/callback
        - io.sleekflow.v2://sso-staging.sleekflow.io/ios/io.sleekflow.v2/callback
      grant_types:
        - authorization_code
        - implicit
        - refresh_token
        - password
        - http://auth0.com/oauth/grant-type/password-realm
      web_origins: [ ]
      native_client_options:
        ios:
          team_id: JNXJD4KQ2C
          app_bundle_identifier: io.sleekflow.app
        android:
          app_package_name: io.sleekflow.v2
          key_hashes:
            - 4B:7A:FA:CF:5F:56:1E:88:C1:0C:01:7A:EA:5F:61:6C:31:0D:AF:18:59:50:C4:9E:FF:32:8E:31:17:0C:4E:86
            - C8:44:11:BE:74:24:10:92:66:74:36:AE:D3:83:47:FC:44:AE:C5:76:0D:5E:2E:5C:E2:4F:9E:1F:7B:EB:5C:4D


  sleekflow:connections:
    - name: sleekflow-connection-google-oauth2
      strategy: google-oauth2
      force_replace: false
      enabled_clients:
        - sleekflow-client-web-app
        - sleekflow-client-mobile-app
        - sleekflow-client-web-v2-app
      options:
        scopes: [ email ]
        client_id: 99474478348-u0dtmseh4qoge36e661o92qovlkul6sc.apps.googleusercontent.com
        client_secret: 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

    - name: sleekflow-connection-apple
      strategy: apple
      enabled_clients:
        - sleekflow-client-web-app
        - sleekflow-client-mobile-app
        - sleekflow-client-web-v2-app
      is_domain_connection: false
      force_replace: false
      options:
        kid: JGDF63GM34
        scopes:
          - name
          - email
        team_id: JNXJD4KQ2C
        client_id: io.sleekflow.app
        client_secret: |-
*****************************************************************************************************************************************************************************************************************************************************************************************************************************

  sleekflow:custom_domain:
    is_enabled: true
    domain: sso-staging.sleekflow.io
    type: auth0_managed_certs
  sleekflow:post_login_webhook: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/postUserLogin
  sleekflow:pre_user_registration_webhook: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/preUserRegistration
  sleekflow:post_change_password_webhook: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/postUserChangePassword
  sleekflow:requires_mfa_webhook: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/tenant-hub/webhooks/auth0/requiresMFA
  sleekflow:action_issuer: https://sso-staging.sleekflow.io/
  sleekflow:action_audience: https://api-staging.sleekflow.io/
  sleekflow:jwk_url: https://sso-staging.sleekflow.io/.well-known/jwks.json
  sleekflow:enable_custom_domain_in_emails: true
  sleekflow:travis_backend_base_url: https://sleekflow-core-staging-dycncqcebbf4ggag.z01.azurefd.net
  sleekflow:whatsapp_cloud_api_override_webhook: https://sleekflow-staging-arcycrczafgrescq.z01.azurefd.net/v1/messaging-hub/WhatsappCloudApiWebhook
  sleekflow:auth0_client_id: b8xN94MKdx5rZCYg32r0SSRVJiYyNw4Z
  sleekflow:auth0_client_secret: ****************************************************************
  sleekflow:auth0_domain: sleekflow-staging.eu.auth0.com
  sleekflow:user_email_check_secret_key: eYx1z5CTaCXkgHWObGRbLM14oo1fnVJL
