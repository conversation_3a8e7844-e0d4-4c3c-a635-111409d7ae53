using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts;

[TriggerGroup(ControllerNames.Carts)]
public class UpdateCartItem
    : ITrigger<
        UpdateCartItem.UpdateCartItemInput,
        UpdateCartItem.UpdateCartItemOutput>
{
    private readonly ICartService _cartService;
    private readonly IProductVariantService _productVariantService;
    private readonly IProductService _productService;

    public UpdateCartItem(
        ICartService cartService,
        IProductVariantService productVariantService,
        IProductService productService)
    {
        _cartService = cartService;
        _productVariantService = productVariantService;
        _productService = productService;
    }

    public class UpdateCartItemInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("product_variant_id")]
        public string ProductVariantId { get; set; }

        [Required]
        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [Required]
        [JsonProperty("quantity")]
        public int Quantity { get; set; }

        [JsonConstructor]
        public UpdateCartItemInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId,
            string productVariantId,
            string productId,
            int quantity)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
            ProductVariantId = productVariantId;
            ProductId = productId;
            Quantity = quantity;
        }
    }

    public class UpdateCartItemOutput
    {
        [JsonProperty("cart")]
        public CartDto Cart { get; }

        [JsonConstructor]
        public UpdateCartItemOutput(CartDto cart)
        {
            Cart = cart;
        }
    }

    public async Task<UpdateCartItemOutput> F(UpdateCartItemInput updateCartItemInput)
    {
        var cart = await _cartService.PatchAndGetCartAsync(
            updateCartItemInput.SleekflowCompanyId,
            updateCartItemInput.StoreId,
            updateCartItemInput.SleekflowUserProfileId,
            updateCartItemInput.ProductVariantId,
            updateCartItemInput.ProductId,
            updateCartItemInput.Quantity);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            cart.LineItems.Select(x => x.ProductVariantId).ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productVariantIdToProductVariantDtoDict = productVariants
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var products = await _productService.GetProductsAsync(
            productVariants.Select(x => x.ProductId).Distinct().ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productIdToProductDict = products
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        return new UpdateCartItemOutput(
            new CartDto(
                cart,
                productVariantIdToProductVariantDtoDict,
                productIdToProductDict));
    }
}