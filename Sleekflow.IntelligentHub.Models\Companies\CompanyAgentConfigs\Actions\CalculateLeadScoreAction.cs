using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class CalculateLeadScoreAction : BaseAction
{
    [JsonProperty("criteria")]
    public List<LeadScoreCriterion> Criteria { get; set; }

    [JsonConstructor]
    public CalculateLeadScoreAction(bool enabled, List<LeadScoreCriterion> criteria)
        : base(enabled)
    {
        Criteria = criteria;
    }

    public CalculateLeadScoreAction(CalculateLeadScoreActionDto dto)
        : base(dto)
    {
        Criteria = dto.Criteria.Select(c => new LeadScoreCriterion(c)).ToList();
    }

    public override BaseActionDto ToDto()
    {
        return new CalculateLeadScoreActionDto(this);
    }
}