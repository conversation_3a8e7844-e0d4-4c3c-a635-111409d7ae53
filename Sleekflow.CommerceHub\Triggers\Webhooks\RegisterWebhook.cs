using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Webhooks;

namespace Sleekflow.CommerceHub.Triggers.Webhooks;

[TriggerGroup("Webhooks")]
public class RegisterWebhook : ITrigger
{
    private readonly IWebhookService _webhookService;
    private readonly IIdService _idService;

    public RegisterWebhook(
        IWebhookService webhookService,
        IIdService idService)
    {
        _webhookService = webhookService;
        _idService = idService;
    }

    public class RegisterWebhookInput
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("event_type_name")]
        [Required]
        public string EventTypeName { get; set; }

        [JsonProperty("url")]
        [Required]
        public string Url { get; set; }

        [JsonProperty("context")]
        [Required]
        public Dictionary<string, object?> Context { get; set; }

        [JsonConstructor]
        public RegisterWebhookInput(
            string sleekflowCompanyId,
            string entityTypeName,
            string eventTypeName,
            string url,
            Dictionary<string, object?> context)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            EntityTypeName = entityTypeName;
            EventTypeName = eventTypeName;
            Url = url;
            Context = context;
        }
    }

    public class RegisterWebhookOutput
    {
    }

    public async Task<RegisterWebhookOutput> F(
        RegisterWebhookInput registerWebhookInput)
    {
        var webhook = new Webhook(
            _idService.GetId("Webhook"),
            registerWebhookInput.SleekflowCompanyId,
            registerWebhookInput.EntityTypeName,
            registerWebhookInput.Url,
            registerWebhookInput.EventTypeName,
            registerWebhookInput.Context);
        if (registerWebhookInput.Context.Count > 0)
        {
            throw new SfInternalErrorException("The Webhook Context should be empty.");
        }

        await _webhookService.RegisterAsync(webhook);

        return new RegisterWebhookOutput();
    }
}