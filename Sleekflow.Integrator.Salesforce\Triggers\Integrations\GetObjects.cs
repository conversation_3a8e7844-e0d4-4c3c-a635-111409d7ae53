using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetObjects : ITrigger
{
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public GetObjects(
        ISalesforceObjectService salesforceObjectService,
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceObjectService = salesforceObjectService;
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class GetObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonConstructor]
        public GetObjectsInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
        }
    }

    public class GetObjectsOutput
    {
        [JsonProperty("objects")]
        [Required]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonConstructor]
        public GetObjectsOutput(
            List<Dictionary<string, object?>> objects)
        {
            Objects = objects;
        }
    }

    public async Task<GetObjectsOutput> F(
        GetObjectsInput getObjectsInput)
    {
        var connection = await _salesforceConnectionService.GetByIdAsync(
            getObjectsInput.ConnectionId,
            getObjectsInput.SleekflowCompanyId);

        var authentication =
            await _salesforceAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var (objects, _) = await _salesforceObjectService.GetObjectsAsync(
            authentication,
            getObjectsInput.EntityTypeName,
            getObjectsInput.FilterGroups,
            getObjectsInput.FieldFilters);

        return new GetObjectsOutput(objects);
    }
}