namespace Sleekflow.CommerceHub.Payments.Abstractions;

public class Address
{
    public string Line1 { get; set; }

    public string City { get; set; }

    public string PostalCode { get; set; }

    public string Country { get; set; }

    public Address(
        string line1,
        string city,
        string postalCode,
        string country)
    {
        Line1 = line1;
        City = city;
        PostalCode = postalCode;
        Country = country;
    }
}