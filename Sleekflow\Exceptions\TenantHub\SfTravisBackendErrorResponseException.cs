using Newtonsoft.Json;

namespace Sleekflow.Exceptions.TenantHub;

public class SfTravisBackendErrorResponse
{
    [JsonProperty("code")]
    public int Code { get; set; }

    [JsonRequired]
    [JsonProperty("message")]
    public string Message { get; set; }

    [JsonProperty("innerExceptionMessage")]
    public string InnerExceptionMessage { get; set; }

    public SfTravisBackendErrorResponse(string message, int code, string exceptionMessage)
    {
        Message = message;
        Code = code;
        InnerExceptionMessage = exceptionMessage;
    }
}

public class SfTravisBackendErrorResponseException : ErrorCodeException
{
    public SfTravisBackendErrorResponseException(string message)
        : base(
            ErrorCodeConstant.SfInvalidTravisBackendRequestException,
            GetResponseMessage(message),
            GetResponseContext(message))
    {
    }

    public static string GetResponseMessage(string response)
    {
        try
        {
            var res = JsonConvert.DeserializeObject<SfTravisBackendErrorResponse>(response);
            return res!.Message;
        }
        catch (Exception)
        {
            return response;
        }
    }

    public static Dictionary<string, object?> GetResponseContext(string response)
    {
        try
        {
            var res = JsonConvert.DeserializeObject<SfTravisBackendErrorResponse>(response);
            return new Dictionary<string, object?>()
            {
                { "Code", res?.Code },
                { "InnerException", res?.InnerExceptionMessage }
            };
        }
        catch (Exception)
        {
            return new Dictionary<string, object?>();
        }
    }
}