using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.Models.WorkflowSteps;
using Sleekflow.Models.WorkflowSteps.Actions;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentConfigRequestConsumerDefinition : ConsumerDefinition<GetAgentConfigRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentConfigRequestConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentConfigRequestConsumer : IConsumer<GetAgentConfigRequest>
{
    private readonly ILogger _logger;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;

    public GetAgentConfigRequestConsumer(
        ILogger<GetAgentConfigRequestConsumer> logger,
        ICompanyAgentConfigService companyAgentConfigService)
    {
        _logger = logger;
        _companyAgentConfigService = companyAgentConfigService;
    }

    public async Task Consume(ConsumeContext<GetAgentConfigRequest> context)
    {
        var message = context.Message;
        var companyAgentConfigId = message.CompanyAgentConfigId;
        var sleekflowCompanyId = message.SleekflowCompanyId;

        var config = await _companyAgentConfigService.GetOrDefaultAsync(companyAgentConfigId, sleekflowCompanyId);

        _logger.LogInformation(
            "Get Company Agent Config {Id} {SleekflowCompanyId} {Config}",
            companyAgentConfigId,
            sleekflowCompanyId,
            JsonConvert.SerializeObject(config));

        AgentActions? actions = null;
        if (config?.Actions != null)
        {
            var json = JsonConvert.SerializeObject(config.Actions);
            actions = JsonConvert.DeserializeObject<AgentActions>(json);
        }

        await context.RespondAsync(
            new GetAgentConfigResponse(
                config?.Id,
                config?.Name,
                config?.IsChatHistoryEnabledAsContext,
                config?.IsContactPropertiesEnabledAsContext,
                config?.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
                config?.ChannelType,
                config?.ChannelId,
                actions,
                config?.PromptInstruction));
    }
}