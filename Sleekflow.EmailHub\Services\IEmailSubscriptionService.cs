using Sleekflow.EmailHub.Models.Subscriptions;

namespace Sleekflow.EmailHub.Services;

public interface IEmailSubscriptionService
{
    Task<EmailSubscription> GetSubscriptionAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default);

    Task SubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? subscriptionMetadata,
        CancellationToken cancellationToken = default);

    Task UnsubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default);

    Task RenewEmailSubscriptionAsync(CancellationToken cancellationToken = default);

    Task<List<string>> FilterSubscribedCompanies(
        List<string> companyIds,
        string emailAddress,
        CancellationToken cancellationToken = default);
}