using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.Documents.WebPageDocuments;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.WebPageDocumentChunk)]
public class WebPageDocumentChunk : Chunk
{
    public const string PropertyNameDocumentId = "document_id";
    public const string PropertyNamePageUri = "page_uri";

    [JsonProperty(PropertyNameDocumentId)]
    public string DocumentId { get; set; }

    [JsonProperty(PropertyNamePageUri)]
    public string PageUri { get; set; }

    [JsonConstructor]
    public WebPageDocumentChunk(
        string id,
        string sleekflowCompanyId,
        string documentId,
        string pageUri,
        string content,
        string contentEn,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<Category> categories,
        Dictionary<string, object?> metadata)
        : base(
            id,
            SysTypeNames.WebPageDocument,
            sleekflowCompanyId,
            content,
            contentEn,
            createdAt,
            updatedAt,
            categories,
            metadata)
    {
        DocumentId = documentId;
        PageUri = pageUri;
    }
}