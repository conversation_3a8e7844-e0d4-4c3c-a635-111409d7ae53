using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class DeleteProviderConnection : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public DeleteProviderConnection(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class DeleteProviderConnectionInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_connection_id")]
        public string ProviderConnectionId { get; set; }

        [JsonConstructor]
        public DeleteProviderConnectionInput(
            string sleekflowCompanyId,
            string providerName,
            string providerConnectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderConnectionId = providerConnectionId;
        }
    }

    public class DeleteProviderConnectionOutput
    {
    }

    public async Task<DeleteProviderConnectionOutput> F(DeleteProviderConnectionInput deleteProviderConnectionInput)
    {
        var providerService = _providerSelector.GetProviderService(
            deleteProviderConnectionInput.ProviderName);

        await providerService.DeleteProviderConnectionAsync(
            deleteProviderConnectionInput.ProviderConnectionId,
            deleteProviderConnectionInput.SleekflowCompanyId);

        return new DeleteProviderConnectionOutput();
    }
}