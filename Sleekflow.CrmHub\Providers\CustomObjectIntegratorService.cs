﻿using System.Collections.Concurrent;
using System.Net;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers.Models;
using Sleekflow.CrmHub.Providers.States;
using Sleekflow.CrmHub.Workers;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions;
using Sleekflow.Models.Constants;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Providers;

public interface ICustomObjectIntegratorService
{
    Task<LoopThroughAndEnrollObjectsToFlowHubOutput>
        LoopThroughAndEnrollObjectsToFlowHubAsync(
            string sleekflowCompanyId,
            string schemaId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId);

    Task<GetLoopThroughObjectsProgressOutput?> GetLoopThroughObjectsProgressAsync(
        string sleekflowCompanyId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId);

    Task<bool> TerminateInProgressLoopThroughExecutionAsync(
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId);

    Task<List<string>> TerminateInProgressLoopThroughExecutionsAsync(
        string flowHubWorkflowId,
        string sleekflowCompanyId);
}

public class CustomObjectIntegratorService : ICustomObjectIntegratorService, IScopedService
{
    public const string ProviderName = "custom_object";

    private readonly ILoopThroughObjectsProgressStateService _loopThroughObjectsProgressStateService;
    private readonly ICrmHubWorkerService _crmHubWorkerService;
    private readonly HttpClient _httpClient;
    private readonly ILogger<CustomObjectIntegratorService> _logger;

    public CustomObjectIntegratorService(
        ILoopThroughObjectsProgressStateService loopThroughObjectsProgressStateService,
        ICrmHubWorkerService crmHubWorkerService,
        HttpClient httpClient,
        ILogger<CustomObjectIntegratorService> logger)
    {
        _loopThroughObjectsProgressStateService = loopThroughObjectsProgressStateService;
        _crmHubWorkerService = crmHubWorkerService;
        _httpClient = httpClient;
        _logger = logger;
    }

    public async Task<LoopThroughAndEnrollObjectsToFlowHubOutput>
        LoopThroughAndEnrollObjectsToFlowHubAsync(
            string sleekflowCompanyId,
            string schemaId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
    {
        var durablePayload = await _crmHubWorkerService.StartLoopThroughAndEnrollSchemafulObjectsToFlowHubAsync(
            sleekflowCompanyId,
            schemaId,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId);

        var loopThroughObjectsProgressStateId =
            await _loopThroughObjectsProgressStateService.CreateOrUpdateLoopThroughObjectsProgressStateAsync(
                durablePayload,
                ProviderName,
                flowHubWorkflowId,
                flowHubWorkflowVersionedId,
                sleekflowCompanyId);

        return new LoopThroughAndEnrollObjectsToFlowHubOutput(loopThroughObjectsProgressStateId);
    }

    public async Task<GetLoopThroughObjectsProgressOutput?> GetLoopThroughObjectsProgressAsync(
        string sleekflowCompanyId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        var state = await _loopThroughObjectsProgressStateService.GetStateAsync<GetLoopThroughObjectsProgressOutput?>(
            ProviderName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            sleekflowCompanyId);

        if (state is null)
        {
            return null;
        }

        var stateObj = state.StateObj;

        // If the Durable Completed, Return the cached
        if (stateObj.QueryOutput?.RuntimeStatus is "Completed")
        {
            return new GetLoopThroughObjectsProgressOutput(
                stateObj.QueryOutput.CustomStatus.Count,
                stateObj.QueryOutput.CustomStatus.LastUpdateTime,
                stateObj.QueryOutput.RuntimeStatus);
        }

        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri(stateObj.DurablePayload.StatusQueryGetUri),
        };
        var resMsg = await _httpClient.SendAsync(requestMessage);
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var statusQueryGetOutput = ConstructStatusQueryGetOutput(resStr);
        if (statusQueryGetOutput == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, durablePayload.StatusQueryGetUri {stateObj.DurablePayload.StatusQueryGetUri} is not working");
        }

        var isStatusUpdated = string.Equals(
                                  stateObj.QueryOutput?.RuntimeStatus,
                                  statusQueryGetOutput.RuntimeStatus,
                                  StringComparison.Ordinal)
                              == false;
        if (isStatusUpdated)
        {
            await _loopThroughObjectsProgressStateService.PatchLoopThroughObjectsProgressStateAsync(
                state.Id,
                sleekflowCompanyId,
                statusQueryGetOutput);
        }

        if (statusQueryGetOutput.RuntimeStatus == "Pending")
        {
            return new GetLoopThroughObjectsProgressOutput(
                0,
                DateTime.UtcNow,
                statusQueryGetOutput.RuntimeStatus);
        }

        return new GetLoopThroughObjectsProgressOutput(
            statusQueryGetOutput.CustomStatus.Count,
            statusQueryGetOutput.CustomStatus.LastUpdateTime,
            statusQueryGetOutput.RuntimeStatus);
    }

    public async Task<bool> TerminateInProgressLoopThroughExecutionAsync(
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId,
        string sleekflowCompanyId)
    {
        var state = await _loopThroughObjectsProgressStateService.GetStateAsync<LoopThroughObjectsProgressState?>(
            ProviderName,
            flowHubWorkflowId,
            flowHubWorkflowVersionedId,
            sleekflowCompanyId);

        var isTerminated = state is null || await TerminateInprogressLoopThroughExecutionAsync(state);

        return isTerminated;
    }

    public async Task<List<string>> TerminateInProgressLoopThroughExecutionsAsync(
        string flowHubWorkflowId,
        string sleekflowCompanyId)
    {
        var states = await _loopThroughObjectsProgressStateService.GetInProgressLoopThroughObjectsProgressStatesAsync(
            ProviderName,
            flowHubWorkflowId,
            sleekflowCompanyId);

        var terminatedStateIds = new ConcurrentBag<string>();
        await Parallel.ForEachAsync(
            states,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 3
            },
            async (state, _) =>
            {
                var isTerminated = await TerminateInprogressLoopThroughExecutionAsync(state);

                if (isTerminated)
                {
                    terminatedStateIds.Add(state.Id);
                }
            });

        if (terminatedStateIds.Count > 0)
        {
            _logger.LogInformation(
                "Terminated {Count} inprogress loop through executions: {CompanyId} {WorkflowId} - {StateIds}",
                terminatedStateIds.Count,
                sleekflowCompanyId,
                flowHubWorkflowId,
                terminatedStateIds);
        }

        return terminatedStateIds.ToList();
    }

    private async Task<bool> TerminateInprogressLoopThroughExecutionAsync(LoopThroughObjectsProgressState state)
    {
        // early return if the state is not found or the state is already terminal
        if (await IsLoopThroughObjectsExecutionFinished(state))
        {
            return true;
        }

        var res = false;
        try
        {
            await TerminateDurableFunctionAsync(state.StateObj.DurablePayload);
            res = true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(
                ex,
                "Failed to terminate durable function: {CompanyId} {WorkflowId} {WorkflowVersionedId}",
                state.SleekflowCompanyId,
                state.FlowHubWorkflowId,
                state.FlowHubWorkflowVersionedId);
        }

        return res;
    }

    private async Task<bool> IsLoopThroughObjectsExecutionFinished(LoopThroughObjectsProgressState state)
    {
        bool res;

        var cachedRuntimeStatus = state.StateObj.QueryOutput?.RuntimeStatus;
        if (_loopThroughObjectsProgressStateService.IsTerminalRuntimeStatus(cachedRuntimeStatus))
        {
            res = true;
        }
        else
        {
            var realtimeRuntimeStatus = (await GetLoopThroughObjectsProgressAsync(
                    state.SleekflowCompanyId,
                    state.FlowHubWorkflowId,
                    state.FlowHubWorkflowVersionedId))!
                .Status;

            res = _loopThroughObjectsProgressStateService.IsTerminalRuntimeStatus(realtimeRuntimeStatus);
        }

        return res;
    }

    private async Task TerminateDurableFunctionAsync(DurablePayload durablePayload)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            RequestUri = new Uri(durablePayload.TerminatePostUri),
        };
        var resMsg = await _httpClient.SendAsync(requestMessage);
        var resStr = await resMsg.Content.ReadAsStringAsync();

        if (!resMsg.IsSuccessStatusCode
            && resMsg.StatusCode != HttpStatusCode.Gone)
        {
            throw new SfInternalErrorException(
                $"Failed to terminate durable function. Status code: {resMsg.StatusCode}, Response: {resStr}");
        }
    }

    private static StatusQueryGetOutput<
        StatusQueryGetOutputInput,
        StatusQueryGetOutputCustomStatus,
        StatusQueryGetOutputOutput?>? ConstructStatusQueryGetOutput(string resStr)
    {
        var statusQueryGetOutput = resStr.ToObject<StatusQueryGetOutput<
            StatusQueryGetOutputInput,
            StatusQueryGetOutputCustomStatus,
            StatusQueryGetOutputOutput?>>();

        // if deserialization failed,
        // We check if the durable function is terminated or cancelled etc,
        // because Azure may return ["{{text}}"] as the value of [output]
        // In this scenario, we manually construct the object with output assigned to null
        if (statusQueryGetOutput is null)
        {
            var statusQueryGetOutputWithoutOutput = resStr.ToObject<StatusQueryGetOutput<
                StatusQueryGetOutputInput,
                StatusQueryGetOutputCustomStatus,
                object?>>();

            if (statusQueryGetOutputWithoutOutput is
                {
                    RuntimeStatus: AzureDurableFunctionRuntimeStatuses.Terminated
                    or AzureDurableFunctionRuntimeStatuses.Canceled
                    or AzureDurableFunctionRuntimeStatuses.Failed
                    or AzureDurableFunctionRuntimeStatuses.Suspended
                })
            {
                statusQueryGetOutput =
                    new StatusQueryGetOutput<StatusQueryGetOutputInput, StatusQueryGetOutputCustomStatus, StatusQueryGetOutputOutput?>(
                        statusQueryGetOutputWithoutOutput.Name,
                        statusQueryGetOutputWithoutOutput.InstanceId,
                        statusQueryGetOutputWithoutOutput.RuntimeStatus,
                        statusQueryGetOutputWithoutOutput.Input,
                        statusQueryGetOutputWithoutOutput.CustomStatus,
                        null, // set to null
                        statusQueryGetOutputWithoutOutput.CreatedTime,
                        statusQueryGetOutputWithoutOutput.LastUpdatedTime);
            }
        }

        return statusQueryGetOutput;
    }
}