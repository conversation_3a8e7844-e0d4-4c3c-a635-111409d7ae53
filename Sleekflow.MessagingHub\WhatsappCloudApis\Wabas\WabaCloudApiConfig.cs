using GraphApi.Client.Payloads;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

public class WabaCloudApiConfig
{
    public GetWhatsappBusinessAccountResponse WhatsappBusinessAccount { get; set; }

    public GetBusinessDetailResponse BusinessDetail { get; set; }

    public GetPhoneNumbersByWabaIdResponse PhoneNumbersByWabaId { get; set; }

    public WabaCloudApiConfig(
        GetWhatsappBusinessAccountResponse whatsappBusinessAccount,
        GetBusinessDetailResponse businessDetail,
        GetPhoneNumbersByWabaIdResponse phoneNumbersByWabaId)
    {
        WhatsappBusinessAccount = whatsappBusinessAccount;
        BusinessDetail = businessDetail;
        PhoneNumbersByWabaId = phoneNumbersByWabaId;
    }
}