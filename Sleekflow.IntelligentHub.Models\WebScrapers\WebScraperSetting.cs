﻿using System.ComponentModel;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers;

public interface IWebScraperSetting
{
    public int DynamicContentWaitSecs { get; set; }

    public int MaxCrawlDepth { get; set; }

    public int MaxCrawlPages { get; set; }

    public int MaxConcurrentPages { get; set; }

    public dynamic InitialCookies { get; set; }
}

/// <summary>
/// Scraper settings open to the customer.
/// </summary>
public class WebScraperSetting : IWebScraperSetting
{
    public const string PropertyNameWebScraperSetting = "web_scraper_setting";

    [JsonProperty(PropertyName = "dynamic_content_wait_secs")]
    [Description("The maximum time to wait for dynamic page content to load.")]
    public int DynamicContentWaitSecs { get; set; }

    [JsonProperty(PropertyName = "max_crawl_depth")]
    [Description("The maximum number of links starting from the start URL that the crawler will recursively follow.")]
    public int MaxCrawlDepth { get; set; }

    [JsonProperty(PropertyName = "max_crawl_pages")]
    [Description(
        "The maximum number pages to crawl. It includes the start URLs, pagination pages, pages with no content, etc.")]
    public int MaxCrawlPages { get; set; }

    [JsonProperty(PropertyName = "max_concurrent_pages")]
    [Description("The maximum number of web browsers or HTTP clients running in parallel.")]
    public int MaxConcurrentPages { get; set; }

    [JsonProperty(PropertyName = "start_url")]
    [Description("URL of page where the crawler will start, only one URL allowed.")]
    public string StartUrl { get; set; }

    [JsonProperty(PropertyName = "initial_cookies")]
    public dynamic InitialCookies { get; set; }

    [JsonConstructor]
    public WebScraperSetting(
        int dynamicContentWaitSecs,
        int maxCrawlDepth,
        int maxCrawlPages,
        int maxConcurrentPages,
        string startUrl,
        dynamic initialCookies)
    {
        DynamicContentWaitSecs = dynamicContentWaitSecs;
        MaxCrawlDepth = maxCrawlDepth;
        MaxCrawlPages = maxCrawlPages;
        MaxConcurrentPages = maxConcurrentPages;
        StartUrl = startUrl;
        InitialCookies = initialCookies;
    }
}