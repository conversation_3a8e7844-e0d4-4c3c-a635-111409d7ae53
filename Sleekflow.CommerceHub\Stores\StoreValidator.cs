using System.ComponentModel.DataAnnotations;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Stores;

public interface IStoreValidator
{
    void AssertValidStoreProperties(
        List<Multilingual> names,
        List<Description> descriptions,
        List<Language> languages,
        PlatformData platformData,
        string? url = null,
        object? input = null);
}

public class StoreValidator : IStoreValidator, IScopedService
{
    public void AssertValidStoreProperties(
        List<Multilingual> names,
        List<Description> descriptions,
        List<Language> languages,
        PlatformData platformData,
        string? url = null,
        object? input = null)
    {
        if (languages.Select(l => l.LanguageIsoCode).Distinct().Count() != languages.Count)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Language Iso Code must be unique for languages",
                        new[]
                        {
                            "Languages"
                        })
                });
        }

        foreach (var language in languages)
        {
            var cultureInfo = CultureUtils.GetCultureInfoByLanguageIsoCode(language.LanguageIsoCode);
            if (cultureInfo == null)
            {
                throw new SfValidationException(
                    new List<ValidationResult>
                    {
                        new (
                            "Language Iso Code must be a valid ISO 639-2 three-letter code",
                            new[]
                            {
                                "Languages"
                            })
                    });
            }
        }

        var numOfIsDefault = languages.Count(l => l.IsDefault);
        if (numOfIsDefault != 1)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Exactly one default Languages should be allowed",
                        new[]
                        {
                            "Languages"
                        })
                });
        }

        var languageIsoCodeToLanguageDict = languages.ToDictionary(l => l.LanguageIsoCode, l => l);

        if (names.Select(l => l.LanguageIsoCode).Distinct().Count() != names.Count)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Language Iso Code must be unique for names",
                        new[]
                        {
                            "Names"
                        })
                });
        }

        if (names.Any(n => !languageIsoCodeToLanguageDict.ContainsKey(n.LanguageIsoCode)))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Language Iso Code for Names must be in Languages",
                        new[]
                        {
                            "Names"
                        })
                });
        }

        // TODO Unique name for store
        // TODO At least one language

        // if (descriptions.Select(l => l.LanguageIsoCode).Distinct().Count() != descriptions.Count)
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new ("Language Iso Code must be unique for names", new[]
        //         {
        //             "Descriptions"
        //         })
        //     });
        // }
        //
        // if (descriptions.Any(n => !languageIsoCodeToLanguageDict.ContainsKey(n.LanguageIsoCode)))
        // {
        //     throw new SfValidationException(new List<ValidationResult>
        //     {
        //         new ("Language Iso Code for Descriptions must be in Languages", new[]
        //         {
        //             "Descriptions"
        //         })
        //     });
        // }
        var allowedPlatformDataTypes = new List<string>()
        {
            "CustomCatalog",
        };

        if (!allowedPlatformDataTypes.Contains(platformData.Type))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "The Platform Identity Type is invalid",
                        new[]
                        {
                            "PlatformData"
                        })
                });
        }
    }
}