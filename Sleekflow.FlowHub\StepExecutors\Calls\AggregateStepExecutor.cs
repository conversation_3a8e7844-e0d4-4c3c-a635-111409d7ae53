using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IAggregateStepExecutor : IStepExecutor
{
}

public class AggregateStepExecutor
    : GeneralStepExecutor<CallStep<AggregateStepArgs>>, IAggregateStepExecutor, IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IBus _bus;
    private readonly ILogger<AggregateStepExecutor> _logger;

    public AggregateStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IBus bus,
        ILogger<AggregateStepExecutor> logger)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _bus = bus;
        _logger = logger;
    }

    private sealed record AggregateStepInput(
        string ContactId,
        int Duration);

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);
        try
        {
            var (contactId, duration) = await GetArgs(callStep, state);

            if (duration <= 0)
            {
                _logger.LogWarning("Aggregate step was skipped as aggregate duration is {Duration} seconds.", duration);
                await onActivatedAsync(state, StepExecutionStatuses.Complete);
                return;
            }

            await _bus.Publish<OnAggregateStepEvent>(new (step.Id, state.Id, contactId, duration, stackEntries));

            // delay step complete in AggregateStateMachine
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<AggregateStepInput> GetArgs(
        CallStep<AggregateStepArgs> callStep,
        ProxyState state)
    {
        var contactId = (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                                  ?? callStep.Args.ContactIdExpr);
        var duration = int.Parse(
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.DurationExpr)
                      ?? callStep.Args.DurationExpr));

        const int maxDurationInSeconds = 30;
        if (duration > maxDurationInSeconds)
        {
            _logger.LogWarning(
                "Max duration is {MaxDuration}, but the evaluated duration is {Duration}",
                maxDurationInSeconds,
                duration);
            duration = maxDurationInSeconds;
        }

        return new AggregateStepInput(
            contactId,
            duration);
    }
}