﻿using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.WabaBalanceAutoTopUpProfiles;

public interface IWabaBalanceAutoTopUpProfileRepository : IRepository<WabaBalanceAutoTopUpProfile>
{
}

public class WabaBalanceAutoTopUpProfileRepository : BaseRepository<WabaBalanceAutoTopUpProfile>, IWabaBalanceAutoTopUpProfileRepository, ISingletonService
{
    public WabaBalanceAutoTopUpProfileRepository(
        IServiceProvider serviceProvider,
        ILogger<WabaBalanceAutoTopUpProfileRepository> logger)
        : base(logger, serviceProvider)
    {
    }
}