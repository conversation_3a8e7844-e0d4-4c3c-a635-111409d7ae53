using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Steps;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnAggregateStepAggregatedEventConsumerDefinition
    : ConsumerDefinition<OnAggregateStepAggregatedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnAggregateStepAggregatedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnAggregateStepAggregatedEventConsumer : IConsumer<OnAggregateStepAggregatedEvent>
{
    private readonly IStateService _stateService;
    private readonly IStateAggregator _stateAggregator;
    private readonly IStepExecutorActivator _stepExecutorActivator;

    public OnAggregateStepAggregatedEventConsumer(
        IStateService stateService,
        IStateAggregator stateAggregator,
        IStepExecutorActivator stepExecutorActivator)
    {
        _stateService = stateService;
        _stateAggregator = stateAggregator;
        _stepExecutorActivator = stepExecutorActivator;
    }

    private sealed class OnAggregateStepCompletedState
    {
        [JsonProperty("completed_timestamp")]
        public DateTimeOffset CompletedTimestamp { get; set; }

        [JsonConstructor]
        public OnAggregateStepCompletedState(DateTimeOffset completedTimestamp)
        {
            CompletedTimestamp = completedTimestamp;
        }
    }

    public async Task Consume(ConsumeContext<OnAggregateStepAggregatedEvent> context)
    {
        var state = await _stateService.GetProxyStateAsync(context.Message.ProxyStateId);
        await _stateAggregator.AggregateStateStepBodyAsync(
            state,
            context.Message.AggregateStepId,
            JsonConvert.SerializeObject(new OnAggregateStepCompletedState(DateTimeOffset.UtcNow)));

        await _stepExecutorActivator.CompleteStepAsync(
            context.Message.ProxyStateId,
            context.Message.AggregateStepId,
            context.Message.StackEntries,
            StepExecutionStatuses.Complete);
    }
}