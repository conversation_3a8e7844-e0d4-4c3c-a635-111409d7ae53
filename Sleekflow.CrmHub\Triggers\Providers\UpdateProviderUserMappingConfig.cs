﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class UpdateProviderUserMappingConfig : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public UpdateProviderUserMappingConfig(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class UpdateProviderUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("provider_user_mapping_config_id")]
        public string ProviderUserMappingConfigId { get; set; }

        [ValidateArray]
        [JsonProperty("user_mappings")]
        public List<UserMapping>? UserMappings { get; set; }

        [JsonConstructor]
        public UpdateProviderUserMappingConfigInput(
            string sleekflowCompanyId,
            string providerName,
            string providerUserMappingConfigId,
            List<UserMapping>? userMappings)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ProviderUserMappingConfigId = providerUserMappingConfigId;
            UserMappings = userMappings;
        }
    }

    public class UpdateProviderUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public UpdateProviderUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<UpdateProviderUserMappingConfigOutput> F(
        UpdateProviderUserMappingConfigInput updateProviderUserMappingConfigInput)
    {
        var providerService = _providerSelector.GetProviderService(
            updateProviderUserMappingConfigInput.ProviderName);

        var output = await providerService.UpdateProviderUserMappingConfigAsync(
            updateProviderUserMappingConfigInput.ProviderUserMappingConfigId,
            updateProviderUserMappingConfigInput.SleekflowCompanyId,
            updateProviderUserMappingConfigInput.UserMappings);

        return new UpdateProviderUserMappingConfigOutput(
            output.UserMappingConfig);
    }
}