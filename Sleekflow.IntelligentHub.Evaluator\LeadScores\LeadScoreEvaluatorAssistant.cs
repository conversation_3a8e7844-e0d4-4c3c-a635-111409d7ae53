﻿using Microsoft.Extensions.AI;
using Microsoft.Extensions.AI.Evaluation;
using Microsoft.Extensions.AI.Evaluation.Reporting;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.LeadScores.Methods;
using Sleekflow.IntelligentHub.Models.Reviewers;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScores;

public class LeadScoreEvaluatorAssistant
{
    private readonly ILeadScoreMethod<LeadScoreEvalOutput> _method;

    public LeadScoreEvaluatorAssistant(
        ILeadScoreMethod<LeadScoreEvalOutput> method)
    {
        _method = method;
    }

    public async Task<(EvaluatedScore? Answer, EvaluationResult EvaluationResult)> Evaluate(
        LeadScoreTestCase testCase,
        ReportingConfiguration reportingConfiguration,
        LeadScoreEvalOutput output,
        CancellationToken cancellationToken)
    {
        var evaluator = new LeadScoreEvaluator();
        var chatConfig = reportingConfiguration.ChatConfiguration;

        var result = await evaluator.EvaluateAsync(
            new ChatMessage(ChatRole.Assistant, JsonConvert.SerializeObject(output.Answer)),
            new ChatMessage(
                ChatRole.Assistant,
                JsonConvert.SerializeObject(testCase.ExpectedEvaluatedScore)),
            chatConfig,
            [new ScoreEvaluationContext(testCase.Scenario)],
            cancellationToken);

        return (output.Answer, result);
    }
}