using Newtonsoft.Json;

namespace Sleekflow.Models.Events;

public class OnRecommendedReplyStreamingEndpointStreamFinishedEvent
{
    [JsonProperty("id")]
    public Guid CorrelationId { get; set; }

    [JsonProperty("session_id")]
    public string SessionId { get; set; }

    [JsonProperty("client_request_id")]
    public string ClientRequestId { get; set; }

    [JsonConstructor]
    public OnRecommendedReplyStreamingEndpointStreamFinishedEvent(
        Guid correlationId,
        string sessionId,
        string clientRequestId)
    {
        CorrelationId = correlationId;
        SessionId = sessionId;
        ClientRequestId = clientRequestId;
    }
}