using Microsoft.AspNetCore.Mvc;

namespace Sleekflow.MessagingHub.Payments;

[ApiController]
[ApiVersion("1.0")]
[Route("[Controller]")]
public class StripeWebhookController : ControllerBase
{
    private readonly IStripeWebhookService _stripeWebhookService;
    private readonly ILogger<StripeWebhookController> _logger;

    public StripeWebhookController(IStripeWebhookService stripeWebhookService,
        ILogger<StripeWebhookController> logger)
    {
        _stripeWebhookService = stripeWebhookService;
        _logger = logger;
    }

    [HttpPost]
    [Route("payment")]
    public async Task<ActionResult> PaymentWebhook()
    {
        var stripePaymentWebhook = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();
        var stripeSignatureHeader = HttpContext.Request.Headers["Stripe-Signature"].ToString();

        _logger.LogInformation("Stripe Payment Webhook Received: {StripePaymentWebhookPayload}", stripePaymentWebhook);
        await _stripeWebhookService.ProcessStripeWebhookAsync(stripePaymentWebhook, stripeSignatureHeader);
        return Ok();
    }
}