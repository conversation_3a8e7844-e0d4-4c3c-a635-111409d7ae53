using System.Collections;

namespace Sleekflow.FlowHub.Models.States.Abstractions;

public class AsyncDictionaryWrapper<TKey, TValue> : IDictionary<TKey, TValue>
{
    private const int MaxKeyLength = 128;

    private readonly IAsyncDictionary<TKey, TValue> _innerDictionary;

#pragma warning disable JA1002
    public AsyncDictionaryWrapper(IAsyncDictionary<TKey, TValue> innerDictionary)
    {
        _innerDictionary = innerDictionary;
    }
#pragma warning restore JA1002

    public IAsyncDictionary<TKey, TValue> GetInnerDictionary()
    {
        return _innerDictionary;
    }

    public void Add(TKey key, TValue value)
    {
        if (key is string { Length: > MaxKeyLength })
        {
            throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.", nameof(key));
        }

        // Use OrleansRetryHelper for SetAsync
        OrleansRetryHelper.ExecuteWithRetryAsync(() => _innerDictionary.SetAsync(key, value), operationName: "Add")
            .GetAwaiter().GetResult();
    }

    public bool ContainsKey(TKey key)
    {
        if (key is string { Length: > MaxKeyLength })
        {
            throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.", nameof(key));
        }

        // Use OrleansRetryHelper for ContainsKeyAsync
        return OrleansRetryHelper.ExecuteWithRetryAsync(
            () => _innerDictionary.ContainsKeyAsync(key),
            operationName: "ContainsKey").GetAwaiter().GetResult();
    }

    public bool Remove(TKey key)
    {
        if (key is string { Length: > MaxKeyLength })
        {
            throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.", nameof(key));
        }

        // Use OrleansRetryHelper for RemoveAsync
        return OrleansRetryHelper
            .ExecuteWithRetryAsync(() => _innerDictionary.RemoveAsync(key), operationName: "Remove").GetAwaiter()
            .GetResult();
    }

    public bool TryGetValue(TKey key, out TValue value)
    {
        if (key is string { Length: > MaxKeyLength })
        {
            throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.", nameof(key));
        }

        try
        {
            // Use OrleansRetryHelper for GetAsync - this is the most critical operation that fails during scale-in
            value = OrleansRetryHelper
                .ExecuteWithRetryAsync(() => _innerDictionary.GetAsync(key), operationName: "TryGetValue").GetAwaiter()
                .GetResult();
            return true;
        }
        catch (Exception)
        {
            value = default!;
            return false;
        }
    }

    public TValue this[TKey key]
    {
        get
        {
            if (key is string { Length: > MaxKeyLength })
            {
                throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.", nameof(key));
            }

            // Use OrleansRetryHelper for GetAsync - this is the most critical operation
            return OrleansRetryHelper
                .ExecuteWithRetryAsync(() => _innerDictionary.GetAsync(key), operationName: "Indexer_Get").GetAwaiter()
                .GetResult();
        }

        set
        {
            if (key is string { Length: > MaxKeyLength })
            {
                throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.", nameof(key));
            }

            // Use OrleansRetryHelper for SetAsync
            OrleansRetryHelper.ExecuteWithRetryAsync(
                () => _innerDictionary.SetAsync(key, value),
                operationName: "Indexer_Set").GetAwaiter().GetResult();
        }
    }

#pragma warning disable JA1001
    public ICollection<TKey> Keys => OrleansRetryHelper
        .ExecuteWithRetryAsync(() => _innerDictionary.KeysAsync(), operationName: "Keys").GetAwaiter().GetResult();
#pragma warning restore JA1001

#pragma warning disable JA1001
    public ICollection<TValue> Values => OrleansRetryHelper
        .ExecuteWithRetryAsync(() => _innerDictionary.ValuesAsync(), operationName: "Values").GetAwaiter().GetResult();
#pragma warning restore JA1001

    public IEnumerator<KeyValuePair<TKey, TValue>> GetEnumerator()
    {
        // Use OrleansRetryHelper for ToDictionaryAsync
        return OrleansRetryHelper
            .ExecuteWithRetryAsync(() => _innerDictionary.ToDictionaryAsync(), operationName: "GetEnumerator")
            .GetAwaiter().GetResult().GetEnumerator();
    }

    IEnumerator IEnumerable.GetEnumerator()
    {
        return GetEnumerator();
    }

    public void Add(KeyValuePair<TKey, TValue> item)
    {
        if (item.Key is string { Length: > MaxKeyLength })
        {
            throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.");
        }

        // Use OrleansRetryHelper for SetAsync
        OrleansRetryHelper.ExecuteWithRetryAsync(
            () => _innerDictionary.SetAsync(item.Key, item.Value),
            operationName: "Add_KeyValuePair").GetAwaiter().GetResult();
    }

    public void Clear()
    {
        // Use OrleansRetryHelper for ClearAsync
        OrleansRetryHelper.ExecuteWithRetryAsync(() => _innerDictionary.ClearAsync(), operationName: "Clear")
            .GetAwaiter().GetResult();
    }

    public bool Contains(KeyValuePair<TKey, TValue> item)
    {
        if (item.Key is string { Length: > MaxKeyLength })
        {
            throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.");
        }

        // Use OrleansRetryHelper for ContainsValueAsync
        return OrleansRetryHelper.ExecuteWithRetryAsync(
            () => _innerDictionary.ContainsValueAsync(item.Value),
            operationName: "Contains").GetAwaiter().GetResult();
    }

    public void CopyTo(KeyValuePair<TKey, TValue>[] array, int arrayIndex)
    {
        if (array == null)
        {
            throw new ArgumentNullException(nameof(array));
        }

        if (arrayIndex < 0 || arrayIndex > array.Length)
        {
            throw new ArgumentOutOfRangeException(nameof(arrayIndex));
        }

        // Use OrleansRetryHelper for ToDictionaryAsync
        var dictionary = OrleansRetryHelper
            .ExecuteWithRetryAsync(() => _innerDictionary.ToDictionaryAsync(), operationName: "CopyTo").GetAwaiter()
            .GetResult();
        var currentIndex = arrayIndex;

        foreach (var kvp in dictionary)
        {
            if (currentIndex >= array.Length)
            {
                break;
            }

            array[currentIndex] = kvp;
            currentIndex++;
        }
    }

    public bool Remove(KeyValuePair<TKey, TValue> item)
    {
        if (item.Key is string { Length: > MaxKeyLength })
        {
            throw new ArgumentException($"Key length must not exceed {MaxKeyLength} characters.");
        }

        // Use OrleansRetryHelper for both ContainsValueAsync and RemoveAsync
        if (OrleansRetryHelper.ExecuteWithRetryAsync(
                () => _innerDictionary.ContainsValueAsync(item.Value),
                operationName: "Remove_ContainsValue").GetAwaiter().GetResult())
        {
            return OrleansRetryHelper.ExecuteWithRetryAsync(
                () => _innerDictionary.RemoveAsync(item.Key),
                operationName: "Remove_KeyValuePair").GetAwaiter().GetResult();
        }

        return false;
    }

#pragma warning disable JA1001
    public int Count => OrleansRetryHelper
        .ExecuteWithRetryAsync(() => _innerDictionary.CountAsync(), operationName: "Count").GetAwaiter().GetResult();
#pragma warning restore JA1001

#pragma warning disable JA1001
    public bool IsReadOnly => false;
#pragma warning restore JA1001
}