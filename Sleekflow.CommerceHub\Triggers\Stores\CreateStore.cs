﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Currencies;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Triggers.Stores;

[TriggerGroup(ControllerNames.Stores)]
public class CreateStore
    : ITrigger<
        CreateStore.CreateStoreInput,
        CreateStore.CreateStoreOutput>
{
    private readonly IStoreService _storeService;
    private readonly IIdService _idService;
    private readonly ICurrencyService _currencyService;

    public CreateStore(
        IStoreService storeService,
        IIdService idService,
        ICurrencyService currencyService)
    {
        _storeService = storeService;
        _idService = idService;
        _currencyService = currencyService;
    }

    public class CreateStoreInput : StoreInput, IHasSleekflowStaff, IHasMetadata
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateStoreInput(
            List<Multilingual> names,
            List<Description> descriptions,
            bool isViewEnabled,
            bool isPaymentEnabled,
            List<LanguageInputDto> languages,
            List<CurrencyInputDto> currencies,
            StoreTemplateDict templateDict,
            Dictionary<string, object?> metadata,
            StoreIntegrationExternalConfigInput? storeIntegrationExternalConfigInput,
            StoreSubscriptionStatus? subscriptionStatus,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(
                names,
                descriptions,
                isViewEnabled,
                isPaymentEnabled,
                languages,
                currencies,
                templateDict,
                metadata,
                storeIntegrationExternalConfigInput,
                subscriptionStatus)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateStoreOutput
    {
        [JsonProperty("store")]
        public StoreDto Store { get; set; }

        [JsonConstructor]
        public CreateStoreOutput(StoreDto store)
        {
            Store = store;
        }
    }

    public async Task<CreateStoreOutput> F(CreateStoreInput createStoreInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createStoreInput.SleekflowStaffId,
            createStoreInput.SleekflowStaffTeamIds);

        var languages = createStoreInput.Languages
            .Select(l => (CultureUtils.GetCultureInfoByLanguageIsoCode(l.LanguageIsoCode)!, l.IsDefault))
            .Select(t => new Language(t.Item1.Name, t.Item1.EnglishName, t.Item1.NativeName, t.IsDefault))
            .ToList();

        var currencies = _currencyService.GetCurrencies()
            .Where(
                c => createStoreInput.Currencies.Any(
                    cc => cc.CurrencyIsoCode == c.CurrencyIsoCode))
            .ToList();

        var store = await _storeService.CreateStoreAsync(
            new Store(
                _idService.GetId(SysTypeNames.Store),
                createStoreInput.SleekflowCompanyId,
                createStoreInput.Names,
                createStoreInput.Descriptions,
                null,
                PlatformData.CustomCatalog(),
                createStoreInput.IsViewEnabled,
                createStoreInput.IsPaymentEnabled,
                languages,
                currencies,
                createStoreInput.StoreIntegrationExternalConfigInput?.GetConfig(),
                createStoreInput.SubscriptionStatus,
                new List<string>
                {
                    "Active"
                },
                createStoreInput.Metadata,
                sleekflowStaff,
                sleekflowStaff,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                createStoreInput.TemplateDict),
            createStoreInput.SleekflowCompanyId);

        return new CreateStoreOutput(new StoreDto(store));
    }
}