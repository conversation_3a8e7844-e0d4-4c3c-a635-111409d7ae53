﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Workers;

public class LoopThroughAndEnrollSchemafulObjectsToFlowHubInput : IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [Json<PERSON>roperty(SchemafulObject.PropertyNameSchemaId)]
    [Required]
    public string SchemaId { get; set; }

    [JsonProperty("flow_hub_workflow_id")]
    [Required]
    public string FlowHubWorkflowId { get; set; }

    [JsonProperty("flow_hub_workflow_versioned_id")]
    [Required]
    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonConstructor]
    public LoopThroughAndEnrollSchemafulObjectsToFlowHubInput(
        string sleekflowCompanyId,
        string schemaId,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SchemaId = schemaId;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
    }
}