﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Constants;

namespace Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;

public sealed class SingleLineTextDataType : DataTypeBase
{
    [JsonProperty(IDataType.PropertyNameName)]
    public override string Name { get; set; }

    [JsonConstructor]
    public SingleLineTextDataType()
    {
        Name = SchemaPropertyDataTypes.SingleLineText;
    }
}