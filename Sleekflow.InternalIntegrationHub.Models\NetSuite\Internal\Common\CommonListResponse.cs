using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

public class CommonListResponse
{
    [JsonProperty("links", NullValueHandling = NullValueHandling.Ignore)]
    public List<Link> Links { get; set; }

    [JsonProperty("count", NullValueHandling = NullValueHandling.Ignore)]
    public int Count { get; set; }

    [JsonProperty("hasMore", NullValueHandling = NullValueHandling.Ignore)]
    public bool HasMore { get; set; }

    [JsonProperty("items", NullValueHandling = NullValueHandling.Ignore)]
    public List<Item> Items { get; set; }

    [JsonProperty("offset", NullValueHandling = NullValueHandling.Ignore)]
    public int Offset { get; set; }

    [JsonProperty("totalResults", NullValueHandling = NullValueHandling.Ignore)]
    public int TotalResults { get; set; }

    [JsonConstructor]
    public CommonListResponse(
        List<Link> links,
        int count,
        bool hasMore,
        List<Item> items,
        int offset,
        int totalResults)
    {
        this.Links = links;
        this.Count = count;
        this.HasMore = hasMore;
        this.Items = items;
        this.Offset = offset;
        this.TotalResults = totalResults;
    }
}