using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public abstract class BaseActionDto
{
    [JsonProperty("enabled")]
    public bool Enabled { get; set; }

    [JsonConstructor]
    protected BaseActionDto(bool enabled)
    {
        Enabled = enabled;
    }

    protected BaseActionDto(BaseAction action)
    {
        Enabled = action.Enabled;
    }
}