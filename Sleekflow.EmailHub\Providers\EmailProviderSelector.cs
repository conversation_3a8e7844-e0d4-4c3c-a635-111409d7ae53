using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Disposable.Integrator;
using Sleekflow.EmailHub.Gmail.Integrator;
using Sleekflow.EmailHub.OnPremise.Integrator;
using Sleekflow.EmailHub.Outlook.Integrators;

namespace Sleekflow.EmailHub.Providers;

public interface IEmailProviderSelector
{
    IEmailService GetEmailProvider(string providerName);
}

public class EmailProviderSelector : IEmailProviderSelector, IScopedService
{
    private readonly IGmailIntegratorService _gmailIntegratorService;
    private readonly IOutlookIntegratorService _outlookIntegratorService;
    private readonly IOnPremiseIntegratorService _onPremiseIntegratorService;
    private readonly IDisposableIntegratorService _disposableIntegratorService;

    public EmailProviderSelector(
        IGmailIntegratorService gmailIntegratorService,
        IOutlookIntegratorService outlookIntegratorService,
        IOnPremiseIntegratorService onPremiseIntegratorService,
        IDisposableIntegratorService disposableIntegratorService)
    {
        _gmailIntegratorService = gmailIntegratorService;
        _outlookIntegratorService = outlookIntegratorService;
        _onPremiseIntegratorService = onPremiseIntegratorService;
        _disposableIntegratorService = disposableIntegratorService;
    }

    public IEmailService GetEmailProvider(string providerName)
    {
        IEmailService emailService = providerName switch
        {
            GmailIntegratorService.EmailProviderName => _gmailIntegratorService,
            OutlookIntegratorService.EmailProviderName => _outlookIntegratorService,
            OnPremiseIntegratorService.EmailProviderName => _onPremiseIntegratorService,
            DisposableIntegratorService.EmailProviderName => _disposableIntegratorService,
            _ => throw new NotImplementedException()
        };
        return emailService;
    }
}