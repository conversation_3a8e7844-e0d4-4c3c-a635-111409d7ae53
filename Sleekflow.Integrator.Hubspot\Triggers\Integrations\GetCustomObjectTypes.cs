﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetCustomObjectTypes : ITrigger
{
    private readonly IHubspotObjectService _hubspotObjectService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotConnectionService _hubspotConnectionService;

    public GetCustomObjectTypes(
        IHubspotObjectService hubspotObjectService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotConnectionService hubspotConnectionService)
    {
        _hubspotObjectService = hubspotObjectService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotConnectionService = hubspotConnectionService;
    }

    public class GetCustomObjectTypesInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetCustomObjectTypesInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class GetCustomObjectTypesOutput
    {
        [JsonProperty("custom_object_types")]
        [Required]
        public List<CustomObjectType> CustomObjectTypes { get; set; }

        [JsonConstructor]
        public GetCustomObjectTypesOutput(
            List<CustomObjectType> customObjectTypes)
        {
            CustomObjectTypes = customObjectTypes;
        }
    }

    public async Task<GetCustomObjectTypesOutput> F(GetCustomObjectTypesInput getCustomObjectTypesInput)
    {
        var connection =
            await _hubspotConnectionService.GetByIdAsync(
                getCustomObjectTypesInput.ConnectionId,
                getCustomObjectTypesInput.SleekflowCompanyId);

        var authentication =
            await _hubspotAuthenticationService.GetAsync(
                connection.AuthenticationId,
                getCustomObjectTypesInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        return new GetCustomObjectTypesOutput(
            await _hubspotObjectService.GetCustomObjectTypesAsync(authentication));
    }
}