using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.WorkflowAgentConfigMappings;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.WorkflowAgentConfigMappings;

public interface IWorkflowAgentConfigMappingService
{
    Task<List<string>> GetWorkflowIdsByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId);

    Task<List<ProxyWorkflow>> GetWorkflowsByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId);

    Task<int> GetWorkflowCountByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId);

    Task<List<WorkflowAgentConfigMapping>> CreateWorkflowAgentConfigMappingsAsync(
        string sleekflowCompanyId,
        string agentConfigId,
        List<string> workflowIds,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task DeleteWorkflowAgentConfigMappingsAsync(
        string sleekflowCompanyId,
        string agentConfigId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<List<WorkflowAgentConfigMapping>> UpdateWorkflowAgentConfigMappingsAsync(
        string sleekflowCompanyId,
        string workflowId,
        List<string> agentConfigIds,
        AuditEntity.SleekflowStaff sleekflowStaff);

    List<string> GetAiAgentIdsFromWorkflowSteps(ProxyWorkflow workflow);
}


public class WorkflowAgentConfigMappingService : IWorkflowAgentConfigMappingService, IScopedService
{
    private readonly IWorkflowAgentConfigMappingRepository _workflowAgentConfigMappingRepository;

    private readonly IWorkflowBatchService _workflowBatchService;

    private readonly ILogger<WorkflowAgentConfigMappingService> _logger;

    public WorkflowAgentConfigMappingService(
        IWorkflowAgentConfigMappingRepository workflowAgentConfigMappingRepository,
        IWorkflowBatchService workflowBatchService,
        ILogger<WorkflowAgentConfigMappingService> logger)
    {
        _workflowAgentConfigMappingRepository = workflowAgentConfigMappingRepository;
        _workflowBatchService = workflowBatchService;
        _logger = logger;
    }

    public async Task<List<string>> GetWorkflowIdsByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId)
    {
        var workflowAgentConfigMappings = await _workflowAgentConfigMappingRepository.GetWorkflowIdsByAgentConfigIdAsync(sleekflowCompanyId, agentConfigId);

        return workflowAgentConfigMappings.Select(x => x.WorkflowId).ToList();
    }

    public async Task<List<ProxyWorkflow>> GetWorkflowsByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId)
    {
        var workflowIds = await GetWorkflowIdsByAgentConfigIdAsync(sleekflowCompanyId, agentConfigId);

        var workflows = await _workflowBatchService.GetProxyWorkflowsWithMetadataAsync(
            sleekflowCompanyId,
            workflowIds);

        return workflows;
    }

    public async Task<int> GetWorkflowCountByAgentConfigIdAsync(
        string sleekflowCompanyId,
        string agentConfigId)
    {
        var workflowIds = await GetWorkflowIdsByAgentConfigIdAsync(sleekflowCompanyId, agentConfigId);
        return workflowIds.Count;
    }

    public async Task<List<WorkflowAgentConfigMapping>> CreateWorkflowAgentConfigMappingsAsync(
        string sleekflowCompanyId,
        string agentConfigId,
        List<string> workflowIds,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var workflowAgentConfigMappings = workflowIds.Select(workflowId => new WorkflowAgentConfigMapping(
            workflowId,
            agentConfigId,
            Guid.NewGuid().ToString(),
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            sleekflowCompanyId,
            sleekflowStaff)).ToList();

        var existingMappings = await _workflowAgentConfigMappingRepository.GetWorkflowIdsByAgentConfigIdAsync(sleekflowCompanyId, agentConfigId);
        var existingWorkflowIds = existingMappings.Select(x => x.WorkflowId).ToList();

        workflowAgentConfigMappings = workflowAgentConfigMappings
            .Where(x => !existingWorkflowIds.Contains(x.WorkflowId))
            .ToList();

        var createdWorkflowAgentConfigMappings = await _workflowAgentConfigMappingRepository.BatchCreateAsync(
            workflowAgentConfigMappings,
            sleekflowCompanyId);

        return createdWorkflowAgentConfigMappings;
    }

    public async Task DeleteWorkflowAgentConfigMappingsAsync(
        string sleekflowCompanyId,
        string agentConfigId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var mappings = await _workflowAgentConfigMappingRepository.GetWorkflowIdsByAgentConfigIdAsync(sleekflowCompanyId, agentConfigId);
        var ids = mappings.Select(m => m.Id).ToList();

        _logger.LogInformation("Deleting {Count} workflow agent config mappings for agent config {AgentConfigId} by {Staff}", ids.Count, agentConfigId, sleekflowStaff);

        if (ids.Count > 0)
        {
            await _workflowAgentConfigMappingRepository.DeleteAsync(ids, sleekflowCompanyId);
        }
    }

    public async Task<List<WorkflowAgentConfigMapping>> UpdateWorkflowAgentConfigMappingsAsync(
        string sleekflowCompanyId,
        string workflowId,
        List<string> agentConfigIds,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        _logger.LogInformation("Updating workflow agent config mappings for workflow {WorkflowId} with {Count} agent configs by {Staff}",
            workflowId, agentConfigIds.Count, sleekflowStaff);

        await _workflowAgentConfigMappingRepository.BatchDeleteByWorkflowIdAsync(sleekflowCompanyId, workflowId);

        if (!agentConfigIds.Any())
        {
            return new List<WorkflowAgentConfigMapping>();
        }

        var newMappings = agentConfigIds.Select(agentConfigId => new WorkflowAgentConfigMapping(
            workflowId,
            agentConfigId,
            Guid.NewGuid().ToString(),
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            sleekflowCompanyId,
            sleekflowStaff)).ToList();

        var createdMappings = await _workflowAgentConfigMappingRepository.BatchCreateAsync(
            newMappings,
            sleekflowCompanyId);

        _logger.LogInformation("Successfully updated workflow agent config mappings for workflow {WorkflowId}. Created {Count} new mappings",
            workflowId, createdMappings.Count);

        return createdMappings;
    }

    public List<string> GetAiAgentIdsFromWorkflowSteps(ProxyWorkflow workflow)
    {
        var aiAgentIds = new List<string>();

        var workflowAiAgentIds = ExtractAiAgentIdsFromSteps(workflow.Steps);
        aiAgentIds.AddRange(workflowAiAgentIds);

        return aiAgentIds.Distinct().ToList();
    }

    private List<string> ExtractAiAgentIdsFromSteps(List<Step> steps)
    {
        var aiAgentIds = new List<string>();

        foreach (var step in steps)
        {
            if (step is CallStep<EnterAiAgentStepArgs> callStep &&
                callStep.Call == EnterAiAgentStepArgs.CallName)
            {
                aiAgentIds.Add(callStep.Args.AiAgentId);
            }
        }

        return aiAgentIds;
    }
}
