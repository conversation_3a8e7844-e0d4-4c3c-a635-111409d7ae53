openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7088
    description: Local
paths:
  /Integrations/CreateObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateObjectOutputOutput'
  /Integrations/DeactivateTypeSync:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeactivateTypeSyncInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeactivateTypeSyncOutputOutput'
  /Integrations/DeleteObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteObjectOutputOutput'
  /Integrations/GetObjectDirectRefUrl:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectDirectRefUrlInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectDirectRefUrlOutputOutput'
  /Integrations/GetObjectsCount:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetObjectsCountInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetObjectsCountOutputOutput'
  /Integrations/GetSupportedTypes:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetSupportedTypesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetSupportedTypesOutputOutput'
  /Integrations/GetTypeFields:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetTypeFieldsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTypeFieldsOutputOutput'
  /Integrations/InitProvider:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitProviderOutputOutput'
  /Integrations/InitTypeSync:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InitTypeSyncInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InitTypeSyncOutputOutput'
  /Integrations/PreviewObjects:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreviewObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreviewObjectsOutputOutput'
  /Integrations/ReAuthenticate:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReAuthenticateInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReAuthenticateOutputOutput'
  /Integrations/ResolveObjectId:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResolveObjectIdInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResolveObjectIdOutputOutput'
  /Integrations/SyncObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncObjectOutputOutput'
  /Integrations/SyncObjects:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncObjectsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncObjectsOutputOutput'
  /Integrations/UpdateObject:
    post:
      tags:
        - Integrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateObjectInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateObjectOutputOutput'
  /Internals/SubscriptionsCheckBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscriptionsCheckBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionsCheckBatchOutputOutput'
  /Internals/SyncObjectsBatch:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncObjectsBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncObjectsBatchOutputOutput'
  /Public/healthz:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Public/AuthenticateCallback:
    get:
      tags:
        - Public
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: code
          in: query
          schema:
            type: string
        - name: state
          in: query
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
components:
  schemas:
    CreateObjectInput:
      required:
        - dict
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    CreateObjectOutput:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    CreateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeactivateTypeSyncInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    DeactivateTypeSyncOutput:
      type: object
      additionalProperties: false
    DeactivateTypeSyncOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeactivateTypeSyncOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteObjectInput:
      required:
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteObjectOutput:
      type: object
      additionalProperties: false
    DeleteObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DurablePayload:
      type: object
      properties:
        id:
          type: string
          nullable: true
        statusQueryGetUri:
          type: string
          nullable: true
        sendEventPostUri:
          type: string
          nullable: true
        terminatePostUri:
          type: string
          nullable: true
        rewindPostUri:
          type: string
          nullable: true
        purgeHistoryDeleteUri:
          type: string
          nullable: true
        restartPostUri:
          type: string
          nullable: true
      additionalProperties: false
    Dynamics365Subscription:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        entity_type_name:
          type: string
          nullable: true
        interval:
          type: integer
          format: int32
        last_execution_start_time:
          type: string
          format: date-time
        last_object_modification_time:
          type: string
          format: date-time
          nullable: true
        durable_payload:
          $ref: '#/components/schemas/DurablePayload'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    GetObjectDirectRefUrlInput:
      required:
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetObjectDirectRefUrlOutput:
      required:
        - object_direct_ref_url
      type: object
      properties:
        object_direct_ref_url:
          minLength: 1
          type: string
      additionalProperties: false
    GetObjectDirectRefUrlOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectDirectRefUrlOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetObjectsCountInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    GetObjectsCountOutput:
      required:
        - count
      type: object
      properties:
        count:
          type: integer
          format: int64
      additionalProperties: false
    GetObjectsCountOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetObjectsCountOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetSupportedTypesInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetSupportedTypesOutput:
      type: object
      properties:
        supported_types:
          type: array
          items:
            $ref: '#/components/schemas/SupportedEntityType'
          nullable: true
      additionalProperties: false
    GetSupportedTypesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetSupportedTypesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    GetTypeFieldsOutput:
      type: object
      properties:
        updatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        creatable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
        viewable_fields:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputFieldDto'
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputFieldDto:
      type: object
      properties:
        calculated:
          type: boolean
        compound_field_name:
          type: string
          nullable: true
        createable:
          type: boolean
        custom:
          type: boolean
        encrypted:
          type: boolean
        label:
          type: string
          nullable: true
        length:
          type: integer
          format: int32
        name:
          type: string
          nullable: true
        picklist_values:
          type: array
          items:
            $ref: '#/components/schemas/GetTypeFieldsOutputPicklistValue'
          nullable: true
        soap_type:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        unique:
          type: boolean
        updateable:
          type: boolean
        mandatory:
          type: boolean
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetTypeFieldsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetTypeFieldsOutputPicklistValue:
      type: object
      properties:
        label:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderInput:
      required:
        - return_to_url
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        return_to_url:
          minLength: 1
          type: string
        additional_details:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    InitProviderOutput:
      type: object
      properties:
        dynamics365_authentication_url:
          type: string
          nullable: true
      additionalProperties: false
    InitProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    InitTypeSyncInput:
      required:
        - entity_type_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        sync_config:
          $ref: '#/components/schemas/SyncConfig'
      additionalProperties: false
    InitTypeSyncOutput:
      type: object
      additionalProperties: false
    InitTypeSyncOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/InitTypeSyncOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreviewObjectsInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    PreviewObjectsOutput:
      required:
        - objects
      type: object
      properties:
        objects:
          type: array
          items:
            type: object
            additionalProperties: { }
      additionalProperties: false
    PreviewObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreviewObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ReAuthenticateInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ReAuthenticateOutput:
      type: object
      additionalProperties: false
    ReAuthenticateOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ReAuthenticateOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ResolveObjectIdInput:
      required:
        - dict
        - entity_type_name
      type: object
      properties:
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    ResolveObjectIdOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    ResolveObjectIdOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ResolveObjectIdOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchInput:
      required:
        - entity_type_name
        - filter_groups
        - last_object_modification_time
        - sleekflow_company_id
        - subscription
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        subscription:
          $ref: '#/components/schemas/Dynamics365Subscription'
        entity_type_name:
          minLength: 1
          type: string
        last_object_modification_time:
          type: string
          format: date-time
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_last_object_modification_time:
          type: string
          format: date-time
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SubscriptionsCheckBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SubscriptionsCheckBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SupportedEntityType:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfig:
      required:
        - interval
      type: object
      properties:
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
          nullable: true
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
          writeOnly: true
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        interval:
          maximum: 86400
          minimum: 3600
          type: integer
          format: int32
        entity_type_name:
          type: string
          nullable: true
        sync_mode:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFieldFilter:
      type: object
      properties:
        name:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilter:
      type: object
      properties:
        field_name:
          type: string
          nullable: true
        value:
          type: string
          nullable: true
        operator:
          type: string
          nullable: true
      additionalProperties: false
    SyncConfigFilterGroup:
      type: object
      properties:
        filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilter'
          nullable: true
      additionalProperties: false
    SyncObjectInput:
      required:
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    SyncObjectOutput:
      type: object
      additionalProperties: false
    SyncObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsBatchInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsBatchOutput:
      type: object
      properties:
        count:
          type: integer
          format: int64
        next_records_url:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncObjectsBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsInput:
      required:
        - entity_type_name
        - filter_groups
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        entity_type_name:
          minLength: 1
          type: string
        filter_groups:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFilterGroup'
        field_filters:
          type: array
          items:
            $ref: '#/components/schemas/SyncConfigFieldFilter'
          nullable: true
      additionalProperties: false
    SyncObjectsOutput:
      type: object
      properties:
        id:
          type: string
          nullable: true
        statusQueryGetUri:
          type: string
          nullable: true
        sendEventPostUri:
          type: string
          nullable: true
        terminatePostUri:
          type: string
          nullable: true
        rewindPostUri:
          type: string
          nullable: true
        purgeHistoryDeleteUri:
          type: string
          nullable: true
        restartPostUri:
          type: string
          nullable: true
      additionalProperties: false
    SyncObjectsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncObjectsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateObjectInput:
      required:
        - dict
        - entity_type_name
        - object_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        object_id:
          minLength: 1
          type: string
        dict:
          type: object
          additionalProperties:
            nullable: true
        entity_type_name:
          minLength: 1
          type: string
      additionalProperties: false
    UpdateObjectOutput:
      type: object
      properties:
        is_async_operation:
          type: boolean
      additionalProperties: false
    UpdateObjectOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateObjectOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false