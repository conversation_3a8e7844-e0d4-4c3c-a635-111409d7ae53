using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Models.Events;

namespace Sleekflow.IntelligentHub.Consumers
{
    public abstract class FlowHubAgentGenericConsumer<TMessage>(ILogger logger, IBus bus)
        where TMessage : AgentEventBase
    {
        protected readonly ILogger _logger = logger;
        protected readonly IBus _bus = bus;

        public async Task Consume(ConsumeContext<TMessage> context)
        {
            var message = context.Message;
            try
            {
                _logger.LogInformation(
                    "Consuming message {MessageType}: {MessageId} for AggregateStepId {AggregateStepId}",
                    typeof(TMessage).Name,
                    context.MessageId,
                    message.AggregateStepId);

                await HandleMessageAsync(context);

                _logger.LogInformation(
                    "Successfully consumed message {MessageType}: {MessageId} for AggregateStepId {AggregateStepId}",
                    typeof(TMessage).Name,
                    context.MessageId,
                    message.AggregateStepId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Error consuming message {MessageType}: {MessageId} for AggregateStepId {AggregateStepId}. Message: {MessageContent}",
                    typeof(TMessage).Name,
                    context.MessageId,
                    message.AggregateStepId,
                    JsonConvert.SerializeObject(message));

                var errorResponse = new
                {
                    Error = ex.Message
                };

                await _bus.Publish(
                    new OnAgentCompleteStepActivationEvent(
                        message.AggregateStepId,
                        message.ProxyStateId,
                        message.StackEntries,
                        JsonConvert.SerializeObject(errorResponse),
                        "Failed"));
            }
        }

        protected abstract Task HandleMessageAsync(ConsumeContext<TMessage> context);
    }
}