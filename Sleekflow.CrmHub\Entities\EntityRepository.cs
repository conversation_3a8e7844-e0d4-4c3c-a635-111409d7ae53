﻿using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Entities;

public interface IEntityRepository : IRepository<CrmHubEntity>
{
}

public class EntityRepository : BaseRepository<CrmHubEntity>, IEntityRepository, ISingletonService
{
    public EntityRepository(
        ILogger<BaseRepository<CrmHubEntity>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}