using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IHeadlessWhatsappMessageIntegrationConfig
{
    string Endpoint { get; }
}

public class HeadlessWhatsappMessageIntegrationConfig : IConfig, IHeadlessWhatsappMessageIntegrationConfig
{
    /// <summary>
    /// Whatsapp messaging endpoint.
    /// </summary>
    public string Endpoint { get; }

    public HeadlessWhatsappMessageIntegrationConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("HEADLESS_WHATSAPP_MESSAGE_INTEGRATION_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("HEADLESS_WHATSAPP_MESSAGE_INTEGRATION_ENDPOINT");
    }
}