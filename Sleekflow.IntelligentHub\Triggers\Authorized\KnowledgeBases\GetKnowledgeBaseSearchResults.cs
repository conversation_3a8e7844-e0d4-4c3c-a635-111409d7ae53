using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.KnowledgeBases;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Models.Chats;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class GetKnowledgeBaseSearchResults
    : ITrigger<GetKnowledgeBaseSearchResults.GetKnowledgeBaseSearchResultsInput,
        GetKnowledgeBaseSearchResults.GetKnowledgeBaseSearchResultsOutput>
{
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IKnowledgeBaseService _knowledgeBaseService;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public GetKnowledgeBaseSearchResults(
        IKbDocumentService kbDocumentService,
        IKnowledgeBaseService knowledgeBaseService,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _kbDocumentService = kbDocumentService;
        _knowledgeBaseService = knowledgeBaseService;
        _authorizationContext = authorizationContext;
    }

    public class GetKnowledgeBaseSearchResultsInput
    {
        [Required]
        [JsonProperty("search_text")]
        public string SearchText { get; set; }

        [Validations.ValidateArray]
        [JsonProperty("sf_chat_entries")]
        public List<SfChatEntry>? SfChatEntries { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseSearchResultsInput(string searchText, List<SfChatEntry>? sfChatEntries)
        {
            SearchText = searchText;
            SfChatEntries = sfChatEntries;
        }
    }

    public class GetKnowledgeBaseSearchResultsOutput
    {
        [JsonProperty("search_results")]
        public List<SearchResult> SearchResults { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseSearchResultsOutput(List<SearchResult> searchResults)
        {
            SearchResults = searchResults;
        }

        public GetKnowledgeBaseSearchResultsOutput(List<(string Sourcepage, string Content, string ContentEn)> results)
        {
            SearchResults = results.Select(r => new SearchResult(r.Sourcepage, r.Content)).ToList();
        }

        public class SearchResult
        {
            [JsonProperty("sourcepage")]
            public string Sourcepage { get; set; }

            [JsonProperty("content")]
            public string Content { get; set; }

            [JsonConstructor]
            public SearchResult(string sourcepage, string content)
            {
                Sourcepage = sourcepage;
                Content = content;
            }
        }
    }

    public async Task<GetKnowledgeBaseSearchResultsOutput> F(GetKnowledgeBaseSearchResultsInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;
        var searchResults = new List<(string Sourcepage, string Content, string ContentEn)>();

        var fileDocument = await _kbDocumentService.GetKbDocumentsByCompanyAsync(sleekflowCompanyId);

        if (fileDocument.Count <= 0)
        {
            return new GetKnowledgeBaseSearchResultsOutput(searchResults);
        }

        searchResults = await _knowledgeBaseService.GetSearchResultsAsync(
            sleekflowCompanyId,
            input.SearchText,
            input.SfChatEntries);
        return new GetKnowledgeBaseSearchResultsOutput(searchResults);
    }
}