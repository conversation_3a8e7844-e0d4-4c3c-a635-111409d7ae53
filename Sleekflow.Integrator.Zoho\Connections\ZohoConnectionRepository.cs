using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.Connections;

public interface IZohoConnectionRepository : IRepository<ZohoConnection>
{
}

public class ZohoConnectionRepository
    : BaseRepository<ZohoConnection>,
        IZohoConnectionRepository,
        ISingletonService
{
    public ZohoConnectionRepository(
        ILogger<BaseRepository<ZohoConnection>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}