﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnVtexOrderEnrolledEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnVtexOrderEnrolled; }
    }

    [Required]
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [Required]
    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("vtex_authentication_id")]
    public string VtexAuthenticationId { get; set; }

    [JsonProperty("status_code")]
    public string StatusCode { get; set; }

    [JsonProperty("order_id")]
    public string OrderId { get; set; }

    [JsonProperty("order")]
    public VtexOrderOverview Order { get; set; }

    public OnVtexOrderEnrolledEventBody(
        DateTimeOffset createdAt, // create time for the event, not the Order
        string workflowId,
        string workflowVersionedId,
        string vtexAuthenticationId,
        string statusCode,
        string orderId,
        VtexOrderOverview order)
        : base(createdAt)
    {
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        VtexAuthenticationId = vtexAuthenticationId;
        StatusCode = statusCode;
        OrderId = orderId;
        Order = order;
    }
}