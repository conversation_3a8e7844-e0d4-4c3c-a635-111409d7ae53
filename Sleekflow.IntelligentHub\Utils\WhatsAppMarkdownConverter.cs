using System.Text.RegularExpressions;
using Newtonsoft.Json.Linq;

namespace Sleekflow.IntelligentHub.Utils;

/// <summary>
/// Utility class to convert HTML-style formatting tags to WhatsApp markdown format.
/// </summary>
public static class WhatsAppMarkdownConverter
{
    private static readonly Regex BoldRegex = new (
        @"<b>(.*?)</b>",
        RegexOptions.Compiled | RegexOptions.Singleline);

    private static readonly Regex ItalicRegex = new (
        @"<i>(.*?)</i>",
        RegexOptions.Compiled | RegexOptions.Singleline);

    private static readonly Regex StrikethroughRegex = new (
        @"<s>(.*?)</s>",
        RegexOptions.Compiled | RegexOptions.Singleline);

    private static readonly Regex QuoteRegex = new (
        @"<q>(.*?)</q>",
        RegexOptions.Compiled | RegexOptions.Singleline);

    private static readonly Regex HyperlinkRegex = new (
        @"<l>(.*?)</l>",
        RegexOptions.Compiled | RegexOptions.Singleline);

    /// <summary>
    /// Converts HTML-style formatting tags to WhatsApp markdown format.
    /// Supported conversions:
    /// - <b>text</b> → *text* (with space before if not at beginning of line and space after if needed)
    /// - <i>text</i> → _text_ (with space before if not at beginning of line and space after if needed)
    /// - <s>text</s> → ~text~ (with space before if not at beginning of line and space after if needed)
    /// - <q>text</q> → \n> text\n (always on a new line and ends with a new line).
    /// </summary>
    /// <param name="input">The input text with HTML-style formatting tags.</param>
    /// <returns>The converted text with WhatsApp markdown formatting.</returns>
    public static string ConvertToWhatsAppMarkdown(string input)
    {
        if (string.IsNullOrEmpty(input))
        {
            return input;
        }

        // Convert bold tags with proper spacing
        var result = BoldRegex.Replace(input, match =>
        {
            var content = match.Groups[1].Value;
            var prefix = match.Index > 0 && !char.IsWhiteSpace(input[match.Index - 1]) ? " " : "";
            var suffix = match.Index + match.Length < input.Length && !char.IsWhiteSpace(input[match.Index + match.Length]) ? " " : "";
            return $"{prefix}*{content}*{suffix}";
        });

        // Convert italic tags with proper spacing
        result = ItalicRegex.Replace(result, match =>
        {
            var content = match.Groups[1].Value;
            var prefix = match.Index > 0 && !char.IsWhiteSpace(result[match.Index - 1]) ? " " : "";
            var suffix = match.Index + match.Length < result.Length && !char.IsWhiteSpace(result[match.Index + match.Length]) ? " " : "";
            return $"{prefix}_{content}_{suffix}";
        });

        // Convert strikethrough tags with proper spacing
        result = StrikethroughRegex.Replace(result, match =>
        {
            var content = match.Groups[1].Value;
            var prefix = match.Index > 0 && !char.IsWhiteSpace(result[match.Index - 1]) ? " " : "";
            var suffix = match.Index + match.Length < result.Length && !char.IsWhiteSpace(result[match.Index + match.Length]) ? " " : "";
            return $"{prefix}~{content}~{suffix}";
        });

        // Convert quote tags - WhatsApp quotes must start and end with a new line
        result = QuoteRegex.Replace(
            result,
            match =>
            {
                var quoteText = match.Groups[1].Value;

                // Replace newlines with spaces within the quote content
                quoteText = quoteText.Replace('\n', ' ');

                // Ensure quote starts on a new line
                var prefix = match.Index > 0 && result[match.Index - 1] != '\n' ? "\n" : "";

                // Ensure quote ends with a new line
                var suffix = match.Index + match.Length < result.Length && result[match.Index + match.Length] != '\n' ? "\n" : "";

                return $"{prefix}> {quoteText}{suffix}";
            });

        // Convert hyperlink tags - Wrap with [hyperlink] and add space after the bracket
        result = HyperlinkRegex.Replace(result, match =>
        {
            var linkText = match.Groups[1].Value;
            return $"[{linkText}] ";
        });

        return result;
    }

    /// <summary>
    /// Applies WhatsApp markdown conversion to the "response" field in a JObject.
    /// </summary>
    /// <param name="responseObject">The JObject containing a response field.</param>
    /// <returns>The same JObject with the response field converted to WhatsApp markdown.</returns>
    public static JObject ApplyWhatsAppMarkdownToResponse(this JObject responseObject)
    {
        if (responseObject == null)
        {
            return null;
        }

        if (responseObject.TryGetValue("response", out var responseToken) && responseToken.Type == JTokenType.String)
        {
            string originalResponse = responseToken.Value<string>();
            string convertedResponse = ConvertToWhatsAppMarkdown(originalResponse);
            responseObject["response"] = convertedResponse;
        }

        return responseObject;
    }
}