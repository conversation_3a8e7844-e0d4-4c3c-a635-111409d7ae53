﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Exceptions;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class UpdateStaffManualAddedLog : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public UpdateStaffManualAddedLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class UpdateStaffManualAddedLogInput : IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("audit_log_text")]
        public string? AuditLogText { get; set; }

        [JsonProperty("data")]
        [Validations.ValidateObject]
        public Dictionary<string, object?>? Data { get; set; }

        [JsonConstructor]
        public UpdateStaffManualAddedLogInput(
            string id,
            string sleekflowUserProfileId,
            string auditLogText,
            Dictionary<string, object?>? data)
        {
            Id = id;
            SleekflowUserProfileId = sleekflowUserProfileId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class UpdateStaffManualAddedLogOutput
    {
        [JsonProperty("user_profile_audit_log")]
        public UserProfileAuditLog UserProfileAuditLog { get; set; }

        [JsonConstructor]
        public UpdateStaffManualAddedLogOutput(UserProfileAuditLog userProfileAuditLog)
        {
            UserProfileAuditLog = userProfileAuditLog;
        }
    }

    public async Task<UpdateStaffManualAddedLogOutput> F(UpdateStaffManualAddedLogInput updateStaffManualAddedLogInput)
    {
        await ValidateUserProfileAuditLogType(
            updateStaffManualAddedLogInput.Id,
            updateStaffManualAddedLogInput.SleekflowUserProfileId);

        var distributedInvocationContext = _distributedInvocationContextService.GetContext();

        var updatedBy = string.IsNullOrWhiteSpace(distributedInvocationContext?.SleekflowStaffId)
            ? null
            : new AuditEntity.SleekflowStaff(
                distributedInvocationContext.SleekflowStaffId,
                distributedInvocationContext.SleekflowStaffTeamIds);

        var userProfileAuditLog = await _userProfileAuditLogService.UpdateUserProfileAuditLogAsync(
            updateStaffManualAddedLogInput.Id,
            updateStaffManualAddedLogInput.SleekflowUserProfileId,
            updateStaffManualAddedLogInput.AuditLogText,
            updateStaffManualAddedLogInput.Data,
            updatedBy);

        return new UpdateStaffManualAddedLogOutput(userProfileAuditLog);
    }

    private async Task ValidateUserProfileAuditLogType(string id, string sleekflowUserProfileId)
    {
        var userProfileAuditLog =
            await _userProfileAuditLogService.GetUserProfileAuditLogAsync(id, sleekflowUserProfileId);

        if (userProfileAuditLog.Type != UserProfileAuditLogTypes.ManualLog)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new ($"Only {UserProfileAuditLogTypes.ManualLog} log is allow to be updated.")
                });
        }
    }
}