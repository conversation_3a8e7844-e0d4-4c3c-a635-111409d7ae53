﻿using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads.WhatsappFlows;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.WhatsappFlows;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.WhatsappFlows;

[TriggerGroup(ControllerNames.WhatsappFlows)]
public class GetWhatsappFlow(IWabaService wabaService, IWhatsappFlowService whatsappFlowService)
    : ITrigger<GetWhatsappFlow.GetWhatsappFlowInput, GetWhatsappFlow.GetWhatsappFlowOutput>
{
    [method: JsonConstructor]
    public class GetWhatsappFlowInput(
        string sleekflowCompanyId,
        string messagingHubWabaId,
        string flowId) : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; } = sleekflowCompanyId;

        [Required]
        [JsonProperty("messaging_hub_waba_id")]
        public string MessagingHubWabaId { get; set; } = messagingHubWabaId;

        [Required]
        [JsonProperty("flow_id")]
        public string FlowId { get; set; } = flowId;
    }

    [method: JsonConstructor]
    public class GetWhatsappFlowOutput(GetFlowResponse flowDetails)
    {
        [JsonProperty("flow_details")]
        public GetFlowResponse FlowDetails { get; set; } = flowDetails;
    }

    public async Task<GetWhatsappFlowOutput> F(GetWhatsappFlowInput input)
    {
        var waba = await wabaService.GetWabaOrDefaultAsync(input.MessagingHubWabaId, input.SleekflowCompanyId);

        if (waba == null)
        {
            throw new SfNotFoundObjectException(input.MessagingHubWabaId);
        }

        return new GetWhatsappFlowOutput(await whatsappFlowService.GetFlowAsync(waba.FacebookWabaId, input.FlowId));
    }
}