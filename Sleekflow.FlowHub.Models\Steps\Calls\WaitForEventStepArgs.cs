using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class WaitForEventStepArgs : TypedCallStepArgs
{
    public const string CallName = "sys.wait-for-event";

    [Required]
    [JsonProperty("event_name")]
    public string EventName { get; set; }

    [JsonProperty("condition__expr")]
    public string? ConditionExpr { get; set; }

    [JsonProperty("timeout_seconds__expr")]
    public string? TimeoutSecondsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Messaging;

    [JsonConstructor]
    public WaitForEventStepArgs(
        string eventName,
        string? conditionExpr,
        string? timeoutSecondsExpr)
    {
        EventName = eventName;
        ConditionExpr = conditionExpr;
        TimeoutSecondsExpr = timeoutSecondsExpr;
    }
}