using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;

public interface ICompanyAgentConfigService
{
    Task<CompanyAgentConfig> CreateAndGetAsync(
        string name,
        string type,
        string sleekflowCompanyId,
        bool isChatHistoryEnabledAsContext,
        bool isContactPropertiesEnabledAsContext,
        int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? collaborationMode,
        LeadNurturingTools? leadNurturingTools,
        ToolsConfig? toolsConfig,
        List<EnricherConfig>? enricherConfigs,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? description = null,
        CompanyAgentConfigActions? actions = null,
        PromptInstruction? promptInstruction = null);

    Task<List<CompanyAgentConfig>> GetObjectsAsync(string sleekflowCompanyId);

    Task<CompanyAgentConfig?> GetOrDefaultAsync(string id, string sleekflowCompanyId);

    Task DeleteAsync(string id, string sleekflowCompanyId, string eTag);

    Task<CompanyAgentConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string eTag,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? name = null,
        PromptInstruction? promptInstruction = null,
        bool? isChatHistoryEnabledAsContext = null,
        bool? isContactPropertiesEnabledAsContext = null,
        int? numberOfPreviousMessagesInChatHistoryAvailableAsContext = null,
        string? channelType = null,
        string? channelId = null,
        string? description = null,
        string? collaborationMode = null,
        string? type = null,
        LeadNurturingTools? leadNurturingTools = null,
        ToolsConfig? toolsConfig = null,
        List<EnricherConfig>? enricherConfigs = null,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        CompanyAgentConfigActions? actions = null);
}

public class CompanyAgentConfigService : ICompanyAgentConfigService, IScopedService
{
    private readonly ILogger _logger;
    private readonly IIdService _idService;
    private readonly ICompanyAgentConfigRepository _companyAgentConfigRepository;

    public CompanyAgentConfigService(
        IIdService idService,
        ILogger<CompanyAgentConfigService> logger,
        ICompanyAgentConfigRepository companyAgentConfigRepository)
    {
        _logger = logger;
        _idService = idService;
        _companyAgentConfigRepository = companyAgentConfigRepository;
    }

    public async Task<CompanyAgentConfig> CreateAndGetAsync(
        string name,
        string type,
        string sleekflowCompanyId,
        bool isChatHistoryEnabledAsContext,
        bool isContactPropertiesEnabledAsContext,
        int numberOfPreviousMessagesInChatHistoryAvailableAsContext,
        string? channelType,
        string? channelId,
        string? collaborationMode,
        LeadNurturingTools? leadNurturingTools,
        ToolsConfig? toolsConfig,
        List<EnricherConfig>? enricherConfigs,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? description = null,
        CompanyAgentConfigActions? actions = null,
        PromptInstruction? promptInstruction = null)
    {
        var createdTime = DateTimeOffset.UtcNow;
        var staff = new AuditEntity.SleekflowStaff(
            sleekflowStaffId,
            sleekflowTeamIds);

        var config = new CompanyAgentConfig(
            _idService.GetId(SysTypeNames.CompanyAgentConfig),
            name,
            sleekflowCompanyId,
            isChatHistoryEnabledAsContext,
            isContactPropertiesEnabledAsContext,
            numberOfPreviousMessagesInChatHistoryAvailableAsContext,
            channelType,
            channelId,
            description,
            promptInstruction,
            type,
            collaborationMode ?? AgentCollaborationModes.Default,
            leadNurturingTools,
            createdTime,
            createdTime,
            staff,
            staff,
            eTag: null,
            enricherConfigs: enricherConfigs,
            knowledgeRetrievalConfig: knowledgeRetrievalConfig,
            toolsConfig: toolsConfig,
            actions: actions);

        _logger.LogInformation("Creating companyAgentConfig with id {Config}", JsonConvert.SerializeObject(config));

        return await _companyAgentConfigRepository.CreateAndGetAsync(config, sleekflowCompanyId);
    }

    public async Task<List<CompanyAgentConfig>> GetObjectsAsync(string sleekflowCompanyId)
    {
        return await _companyAgentConfigRepository.GetObjectsAsync(c => c.SleekflowCompanyId == sleekflowCompanyId);
    }

    public async Task<CompanyAgentConfig?> GetOrDefaultAsync(string id, string sleekflowCompanyId)
    {
        return await _companyAgentConfigRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
    }

    public async Task DeleteAsync(string id, string sleekflowCompanyId, string eTag)
    {
        await _companyAgentConfigRepository.DeleteAsync(id, sleekflowCompanyId, eTag);
    }

    public async Task<CompanyAgentConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        string eTag,
        string sleekflowStaffId,
        List<string>? sleekflowTeamIds,
        string? name = null,
        PromptInstruction? promptInstruction = null,
        bool? isChatHistoryEnabledAsContext = null,
        bool? isContactPropertiesEnabledAsContext = null,
        int? numberOfPreviousMessagesInChatHistoryAvailableAsContext = null,
        string? channelType = null,
        string? channelId = null,
        string? description = null,
        string? collaborationMode = null,
        string? type = null,
        LeadNurturingTools? leadNurturingTools = null,
        ToolsConfig? toolsConfig = null,
        List<EnricherConfig>? enricherConfigs = null,
        KnowledgeRetrievalConfig? knowledgeRetrievalConfig = null,
        CompanyAgentConfigActions? actions = null)
    {
        _logger.LogInformation(
            "Patching companyAgentConfig with id {Id} {CompanyId} {SleekflowStaffId} {SleekflowTeamIds} {IsChatHistoryEnabledAsContext} {IsContactPropertiesEnabledAsContext} {NumberOfPreviousMessagesInChatHistoryAvailableAsContext} {CollaborationMode} {Type} {Actions}",
            id,
            sleekflowCompanyId,
            sleekflowStaffId,
            JsonConvert.SerializeObject(sleekflowTeamIds),
            isChatHistoryEnabledAsContext,
            isContactPropertiesEnabledAsContext,
            numberOfPreviousMessagesInChatHistoryAvailableAsContext,
            collaborationMode,
            type,
            JsonConvert.SerializeObject(actions));

        // Retrieve the existing configuration
        var existingConfig = await _companyAgentConfigRepository.GetAsync(id, sleekflowCompanyId);

        // Verify ETag for optimistic concurrency control
        if (existingConfig.ETag != eTag)
        {
            throw new InvalidOperationException("ETag mismatch. The object has been modified by another process.");
        }

        // Update the properties that are provided (non-null values)
        var updatedStaff = new AuditEntity.SleekflowStaff(sleekflowStaffId, sleekflowTeamIds);
        var updatedTime = DateTimeOffset.UtcNow;

        var updatedConfig = new CompanyAgentConfig(
            existingConfig.Id,
            name ?? existingConfig.Name,
            existingConfig.SleekflowCompanyId,
            isChatHistoryEnabledAsContext ?? existingConfig.IsChatHistoryEnabledAsContext,
            isContactPropertiesEnabledAsContext ?? existingConfig.IsContactPropertiesEnabledAsContext,
            numberOfPreviousMessagesInChatHistoryAvailableAsContext ??
            existingConfig.NumberOfPreviousMessagesInChatHistoryAvailableAsContext,
            channelType ?? existingConfig.ChannelType,
            channelId ?? existingConfig.ChannelId,
            description ?? existingConfig.Description,
            promptInstruction ?? existingConfig.PromptInstruction,
            type ?? existingConfig.Type,
            collaborationMode ?? existingConfig.CollaborationMode,
            leadNurturingTools ?? existingConfig.LeadNurturingTools,
            existingConfig.CreatedAt,
            updatedTime,
            existingConfig.CreatedBy,
            updatedStaff,
            eTag: eTag,
            enricherConfigs: enricherConfigs ?? existingConfig.EnricherConfigs,
            knowledgeRetrievalConfig: knowledgeRetrievalConfig ?? existingConfig.KnowledgeRetrievalConfig,
            toolsConfig: toolsConfig ?? existingConfig.ToolsConfig,
            actions: actions ?? existingConfig.Actions);

        // Upsert the updated configuration
        return await _companyAgentConfigRepository.UpsertAndGetAsync(updatedConfig, sleekflowCompanyId);
    }
}