﻿using System.Globalization;
using Microsoft.Azure.Cosmos;
using Sleekflow.Constants;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Utils;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.ProviderConfigs;

public interface IProviderConfigService
{
    Task<ProviderConfig> GetProviderConfigAsync(string sleekflowCompanyId, string providerName);

    Task<ProviderConfig?> GetProviderConfigOrDefaultAsync(string sleekflowCompanyId, string providerName);

    Task<ProviderConfig> InitProviderConfigAsync(
        string sleekflowCompanyId,
        string providerName,
        string defaultRegionCode,
        Dictionary<string, object?>? additionalDetails,
        ProviderLimit? providerLimit);

    Task UpdateOrRemoveSyncConfigAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        SyncConfig? syncConfig);

    Task UpdateIsAuthenticatedAsync(
        string id,
        string sleekflowCompanyId,
        bool isAuthenticated);

    Task<List<ProviderConfig>> GetProviderConfigsAsync(string sleekflowCompanyId);

    Task<int> DeleteProviderConfigAsync(string id, string sleekflowCompanyId);

    Task UpdateOrRemoveProviderLimit(string id, string sleekflowCompanyId, ProviderLimit? providerLimit);

    Task UpdateProviderNameAsync(string id, string sleekflowCompanyId, string providerName);

    Task UpdateDefaultRegionCodeAsync(string id, string sleekflowCompanyId, string defaultRegionCode);
}

public class ProviderConfigService : IScopedService, IProviderConfigService
{
    private readonly IIdService _idService;
    private readonly IProviderConfigRepository _providerConfigRepository;

    public ProviderConfigService(
        IIdService idService,
        IProviderConfigRepository providerConfigRepository)
    {
        _idService = idService;
        _providerConfigRepository = providerConfigRepository;
    }

    public async Task<ProviderConfig> GetProviderConfigAsync(string sleekflowCompanyId, string providerName)
    {
        var providerConfigs = await _providerConfigRepository.GetObjectsAsync(
            pc =>
                pc.SleekflowCompanyId == sleekflowCompanyId
                && pc.ProviderName == providerName);
        if (providerConfigs.Count == 0)
        {
            throw new SfNotInitializedException(providerName);
        }

        return providerConfigs[0];
    }

    public async Task<ProviderConfig?> GetProviderConfigOrDefaultAsync(string sleekflowCompanyId, string providerName)
    {
        var providerConfigs = await _providerConfigRepository.GetObjectsAsync(
            pc =>
                pc.SleekflowCompanyId == sleekflowCompanyId
                && pc.ProviderName == providerName);
        if (providerConfigs.Count == 0)
        {
            return null;
        }

        return providerConfigs[0];
    }

    public async Task<ProviderConfig> InitProviderConfigAsync(
        string sleekflowCompanyId,
        string providerName,
        string defaultRegionCode,
        Dictionary<string, object?>? additionalDetails,
        ProviderLimit? providerLimit)
    {
        var providerConfig = await GetProviderConfigOrDefaultAsync(sleekflowCompanyId, providerName);
        if (providerConfig != null)
        {
            return providerConfig;
        }

        providerConfig = providerLimit == null
            ? new ProviderConfig(
                _idService.GetId("ProviderConfig"),
                sleekflowCompanyId,
                RandomStringUtils.Gen(20),
                new Dictionary<string, SyncConfig>(),
                providerName,
                false,
                defaultRegionCode,
                new List<string>
                {
                    RecordStatuses.Active
                },
                new (
                    false,
                    new Dictionary<string, int>
                    {
                        {
                            "Contact", 1000000
                        }
                    }
                ))
            : new ProviderConfig(
                _idService.GetId("ProviderConfig"),
                sleekflowCompanyId,
                RandomStringUtils.Gen(20),
                new Dictionary<string, SyncConfig>(),
                providerName,
                false,
                defaultRegionCode,
                new List<string>
                {
                    RecordStatuses.Active
                },
                providerLimit);

        if (defaultRegionCode != "ZZ")
        {
            var regionInfo = new RegionInfo(defaultRegionCode);
            if (regionInfo.TwoLetterISORegionName != defaultRegionCode)
            {
                throw new SfUserFriendlyException(
                    "The region code is incorrect. Please provide an two-letter ISO region code.");
            }
        }

        var upsertCount = await _providerConfigRepository.UpsertAsync(
            providerConfig,
            providerConfig.SleekflowCompanyId);
        if (upsertCount == 0)
        {
            throw new SfUserFriendlyException("Unable to create the provider config");
        }

        return providerConfig;
    }

    public async Task UpdateOrRemoveSyncConfigAsync(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        SyncConfig? syncConfig)
    {
        if (syncConfig == null)
        {
            await _providerConfigRepository.PatchAsync(
                id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Remove("/entity_type_name_to_sync_config_dict/" + entityTypeName)
                });
        }
        else
        {
            await _providerConfigRepository.PatchAsync(
                id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/entity_type_name_to_sync_config_dict/" + entityTypeName, syncConfig)
                });
        }
    }

    public async Task UpdateOrRemoveProviderLimit(string id, string sleekflowCompanyId, ProviderLimit? providerLimit)
    {
        if (providerLimit == null)
        {
            await _providerConfigRepository.PatchAsync(
                id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Remove("/provider_limit")
                });
        }
        else
        {
            await _providerConfigRepository.PatchAsync(
                id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set("/provider_limit", providerLimit)
                });
        }
    }

    public async Task UpdateIsAuthenticatedAsync(string id, string sleekflowCompanyId, bool isAuthenticated)
    {
        await _providerConfigRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/is_authenticated", isAuthenticated)
            });
    }

    public async Task UpdateProviderNameAsync(string id, string sleekflowCompanyId, string providerName)
    {
        await _providerConfigRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/provider_name", providerName)
            });
    }

    public async Task UpdateDefaultRegionCodeAsync(string id, string sleekflowCompanyId, string defaultRegionCode)
    {
        await _providerConfigRepository.PatchAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/default_region_code", defaultRegionCode)
            });
    }

    public async Task<List<ProviderConfig>> GetProviderConfigsAsync(string sleekflowCompanyId)
    {
        var providerConfigs = await _providerConfigRepository.GetObjectsAsync(
            pc => pc.SleekflowCompanyId == sleekflowCompanyId);

        return providerConfigs;
    }

    public async Task<int> DeleteProviderConfigAsync(string id, string sleekflowCompanyId)
    {
        // Soft delete
        var count = await _providerConfigRepository.DeleteAsync(id, sleekflowCompanyId);
        return count;
    }
}