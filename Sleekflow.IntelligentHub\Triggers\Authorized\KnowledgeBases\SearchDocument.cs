using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class SearchDocument
    : ITrigger<
        SearchDocument.SearchDocumentInput,
        SearchDocument.SearchDocumentOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IKbDocumentService _kbDocumentService;

    public SearchDocument(
        ISleekflowAuthorizationContext authorizationContext,
        IKbDocumentService kbDocumentService)
    {
        _authorizationContext = authorizationContext;
        _kbDocumentService = kbDocumentService;
    }

    public class SearchDocumentInput
    {
        [Required]
        [JsonProperty("search_input")]
        public string SearchInput { get; set; }

        [JsonConstructor]
        public SearchDocumentInput(string searchInput)
        {
            SearchInput = searchInput;
        }
    }

    public class SearchDocumentOutput
    {
        [JsonProperty("kb_documents")]
        public List<KbDocument> KbDocuments { get; set; }

        [JsonConstructor]
        public SearchDocumentOutput(List<KbDocument> kbDocuments)
        {
            KbDocuments = kbDocuments;
        }
    }

    public async Task<SearchDocumentOutput> F(
        SearchDocumentInput searchDocumentInput)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;

        var kbDocuments = await _kbDocumentService.SearchDocumentAsync(
            sleekflowCompanyId,
            searchDocumentInput.SearchInput);

        return new SearchDocumentOutput(kbDocuments.ToList());
    }
}