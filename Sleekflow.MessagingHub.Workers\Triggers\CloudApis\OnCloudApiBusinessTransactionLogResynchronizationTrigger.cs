using MassTransit;
using Microsoft.Azure.Functions.Worker;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Workers.Triggers.CloudApis;

public class OnCloudApiBusinessTransactionLogResynchronizationTrigger
{
    private readonly IBus _bus;

    public OnCloudApiBusinessTransactionLogResynchronizationTrigger(IBus bus)
    {
        _bus = bus;
    }

    /// <summary>
    /// Scheduling an Weekly recurring job for OnCloudApiBusinessTransactionLogResynchronizationTrigger.
    /// </summary>
    /// <param name="timerInfo">Schedule Expression on "0 0 21 * * *" -> 4.00 AM UTC.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    [Function("OnCloudApiBusinessTransactionLogResynchronizationTrigger")]
    public Task RunAsync(
        [TimerTrigger("0 0 20 * * *")]
        TimerInfo timerInfo)
    {
        return _bus.Publish(
            new OnCloudApiBusinessBalanceTransactionLogResynchronizationEvent(DateTimeOffset.UtcNow));
    }
}