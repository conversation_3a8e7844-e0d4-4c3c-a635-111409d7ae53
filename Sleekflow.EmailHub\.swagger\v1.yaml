openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7099
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/email-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/email-hub
    description: Prod Apigw
paths:
  /Blobs/CreateBlobDownloadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBlobDownloadSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateBlobDownloadSasUrlsOutputOutput'
  /Blobs/CreateBlobUploadSasUrls:
    post:
      tags:
        - Blobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateBlobUploadSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateBlobUploadSasUrlsOutputOutput'
  /DisposableWebhook/NotifyOnDisposableReceive:
    post:
      tags:
        - DisposableWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Emails/AuthenticateProvider:
    post:
      tags:
        - Emails
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticateProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticateProviderOutputOutput'
  /Emails/DeleteEmail:
    post:
      tags:
        - Emails
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteEmailInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteEmailOutputOutput'
  /Emails/SendEmail:
    post:
      tags:
        - Emails
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendEmailInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendEmailOutputOutput'
  /GmailAuthentication/GmailAuthCallback:
    get:
      tags:
        - GmailAuthentication
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /GmailWebhook/NotifyOnGmailReceive:
    post:
      tags:
        - GmailWebhook
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json-patch+json:
            schema:
              $ref: '#/components/schemas/NotifyOnGmailReceiveInput'
          application/json:
            schema:
              $ref: '#/components/schemas/NotifyOnGmailReceiveInput'
          text/json:
            schema:
              $ref: '#/components/schemas/NotifyOnGmailReceiveInput'
          application/*+json:
            schema:
              $ref: '#/components/schemas/NotifyOnGmailReceiveInput'
      responses:
        '200':
          description: OK
  /OutlookAuthentication/OutlookAuthCallback:
    get:
      tags:
        - OutlookAuthentication
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /OutlookSubscription/OutlookSubscriptionCallBack:
    post:
      tags:
        - OutlookSubscription
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      responses:
        '200':
          description: OK
  /Providers/GetConnectedProviders:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetConnectedProvidersInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetConnectedProvidersOutputOutput'
  /Providers/PeriodicSyncOnPremiseProvider:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PeriodicSyncOnPremiseProviderInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PeriodicSyncOnPremiseProviderOutputOutput'
  /Providers/SyncProviderEmail:
    post:
      tags:
        - Providers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncProviderEmailInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncProviderEmailOutputOutput'
  /Subscriptions/RenewProviderEmailSubscription:
    post:
      tags:
        - Subscriptions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RenewProviderEmailSubscriptionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RenewProviderEmailSubscriptionOutputOutput'
  /Subscriptions/SubscribeProviderEmailAddress:
    post:
      tags:
        - Subscriptions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubscribeProviderEmailAddressInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscribeProviderEmailAddressOutputOutput'
  /Subscriptions/UnsubscribeProviderEmailAddress:
    post:
      tags:
        - Subscriptions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UnsubscribeProviderEmailAddressInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnsubscribeProviderEmailAddressOutputOutput'
components:
  schemas:
    AuthenticateProviderInput:
      required:
        - email_address
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email_address:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        extended_auth_metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    AuthenticateProviderOutput:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
        email_address:
          type: string
          nullable: true
        context:
          nullable: true
      additionalProperties: false
    AuthenticateProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthenticateProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CompanyProviderWithEmails:
      type: object
      properties:
        provider_name:
          type: string
          nullable: true
        email_address:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsInput:
      required:
        - blob_names
        - blob_type
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        blob_names:
          type: array
          items:
            type: string
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutput:
      type: object
      properties:
        download_blobs:
          type: array
          items:
            $ref: '#/components/schemas/PublicBlob'
          nullable: true
      additionalProperties: false
    CreateBlobDownloadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateBlobDownloadSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsInput:
      required:
        - blob_type
        - number_of_blobs
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        number_of_blobs:
          maximum: 16
          minimum: 1
          type: integer
          format: int32
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    CreateBlobUploadSasUrlsOutput:
      type: object
      properties:
        upload_blobs:
          type: array
          items:
            $ref: '#/components/schemas/PublicBlob'
          nullable: true
      additionalProperties: false
    CreateBlobUploadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateBlobUploadSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DeleteEmailInput:
      required:
        - email_id
      type: object
      properties:
        email_id:
          minLength: 1
          type: string
      additionalProperties: false
    DeleteEmailOutput:
      type: object
      additionalProperties: false
    DeleteEmailOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DeleteEmailOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EmailAttachment:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        email_address:
          type: string
          nullable: true
        email_id:
          type: string
          nullable: true
        file_url_to_blob:
          type: string
          nullable: true
        file_name:
          type: string
          nullable: true
      additionalProperties: false
    EmailContact:
      type: object
      properties:
        email_address:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
      additionalProperties: false
    GetConnectedProvidersInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetConnectedProvidersOutput:
      type: object
      properties:
        company_provider_with_email_list:
          type: array
          items:
            $ref: '#/components/schemas/CompanyProviderWithEmails'
          nullable: true
      additionalProperties: false
    GetConnectedProvidersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetConnectedProvidersOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    NotifyOnGmailReceiveInput:
      required:
        - message
        - subscription
      type: object
      properties:
        message:
          $ref: '#/components/schemas/NotifyOnGmailReceiveMessage'
        subscription:
          minLength: 1
          type: string
      additionalProperties: false
    NotifyOnGmailReceiveMessage:
      type: object
      properties:
        data:
          type: string
          nullable: true
        messageId:
          type: string
          nullable: true
        message_id:
          type: string
          nullable: true
          readOnly: true
        publishTime:
          type: string
          nullable: true
        publish_time:
          type: string
          nullable: true
          readOnly: true
      additionalProperties: false
    PeriodicSyncOnPremiseProviderInput:
      type: object
      additionalProperties: false
    PeriodicSyncOnPremiseProviderOutput:
      type: object
      additionalProperties: false
    PeriodicSyncOnPremiseProviderOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PeriodicSyncOnPremiseProviderOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PublicBlob:
      type: object
      properties:
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    RenewProviderEmailSubscriptionInput:
      required:
        - provider_name
      type: object
      properties:
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    RenewProviderEmailSubscriptionOutput:
      type: object
      additionalProperties: false
    RenewProviderEmailSubscriptionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RenewProviderEmailSubscriptionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SendEmailInput:
      required:
        - bcc
        - cc
        - email_attachments
        - provider_name
        - reply_to
        - sender
        - sleekflow_company_id
        - subject
        - to
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        sender:
          $ref: '#/components/schemas/EmailContact'
        email_metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
        to:
          type: array
          items:
            $ref: '#/components/schemas/EmailContact'
        cc:
          type: array
          items:
            $ref: '#/components/schemas/EmailContact'
        bcc:
          type: array
          items:
            $ref: '#/components/schemas/EmailContact'
        subject:
          minLength: 1
          type: string
        reply_to:
          type: array
          items:
            $ref: '#/components/schemas/EmailContact'
        html_body:
          type: string
          nullable: true
        text_body:
          type: string
          nullable: true
        email_attachments:
          type: array
          items:
            $ref: '#/components/schemas/EmailAttachment'
      additionalProperties: false
    SendEmailOutput:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        provider_name:
          type: string
          nullable: true
        sender:
          $ref: '#/components/schemas/EmailContact'
      additionalProperties: false
    SendEmailOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SendEmailOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SubscribeProviderEmailAddressInput:
      required:
        - email_address
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email_address:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
        subscription_metadata:
          type: object
          additionalProperties:
            type: string
          nullable: true
      additionalProperties: false
    SubscribeProviderEmailAddressOutput:
      required:
        - email_address
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email_address:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    SubscribeProviderEmailAddressOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SubscribeProviderEmailAddressOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SyncProviderEmailInput:
      required:
        - email_address
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email_address:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    SyncProviderEmailOutput:
      type: object
      additionalProperties: false
    SyncProviderEmailOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncProviderEmailOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UnsubscribeProviderEmailAddressInput:
      required:
        - email_address
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email_address:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    UnsubscribeProviderEmailAddressOutput:
      required:
        - email_address
        - provider_name
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        email_address:
          minLength: 1
          type: string
        provider_name:
          minLength: 1
          type: string
      additionalProperties: false
    UnsubscribeProviderEmailAddressOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UnsubscribeProviderEmailAddressOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false