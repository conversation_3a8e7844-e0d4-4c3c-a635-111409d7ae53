using MassTransit;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;
using Sleekflow.MessagingHub.Webhooks;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Events;

public class OnWabaResyncEventConsumerDefinition
    : ConsumerDefinition<OnWabaResyncEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnWabaResyncEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnWabaResyncEventConsumer : IConsumer<OnWabaResyncEvent>
{
    private readonly ILogger<OnWabaResyncEventConsumer> _logger;
    private readonly IWabaService _wabaService;
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;

    public OnWabaResyncEventConsumer(
        ILogger<OnWabaResyncEventConsumer> logger,
        IWabaService wabaService,
        IMessagingHubWebhookService messagingHubWebhookService)
    {
        _logger = logger;
        _wabaService = wabaService;
        _messagingHubWebhookService = messagingHubWebhookService;
    }

    public async Task Consume(ConsumeContext<OnWabaResyncEvent> context)
    {
        var facebookWabaId = context.Message.FacebookWabaId;
        _logger.LogInformation("Starting resynchronization for waba {Waba}", facebookWabaId);

        try
        {
            var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);

            await _wabaService.GetAndReconstructWabaAsync(waba, null, true, null);
            _logger.LogInformation("Successfully resynchronized waba {Waba}", facebookWabaId);

            await Task.WhenAll(waba.WabaPhoneNumbers.Select(wabaPhoneNumber => _messagingHubWebhookService.SendWebhooksAsync(
                wabaPhoneNumber.SleekflowCompanyId,
                WebhookEntityTypeNames.WabaPhoneNumber,
                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                new WhatsappCloudApiWebhookTravisMessage(
                    waba.Id,
                    wabaPhoneNumber.Id,
                    new GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject
                    {
                        Event = WhatsappCloudApiWebhookEventTypeNames.AdAccountLinked,
                    }),
                wabaPhoneNumber.Id)));
            _logger.LogInformation("Sent AdAccountLinked webhooks for waba {Waba} after resynchronization", facebookWabaId);
        }
        catch (Exception exception)
        {
            _logger.LogError(
                exception,
                "Exception occurred during scheduled Waba resynchronization for {Waba}. Event: {EventData}, Exception: {ExceptionDetails}",
                facebookWabaId,
                JsonConvert.SerializeObject(context.Message),
                JsonConvert.SerializeObject(exception));
        }
    }
} 