﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.IntelligentHub;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters.Abstractions;
using Sleekflow.IntelligentHub.Models.Constants;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Splitters;

public interface IDocumentSplitterFactory
{
    IDocumentSplitter CreateDocumentSplitter(string documentType);
}

public class DocumentSplitterFactory : IScopedService, IDocumentSplitterFactory
{
    public IDocumentSplitter CreateDocumentSplitter(string documentType)
    {
        switch (documentType.ToLower())
        {
            case DocumentTypes.Pdf:
                return new PdfDocumentSplitter();
            case DocumentTypes.Text:
                return new TxtDocumentSplitter();
            case DocumentTypes.Csv:
                return new CsvDocumentSplitter();
            case DocumentTypes.MicrosoftExcel:
                return new ExcelDocumentSplitter();
            default:
                throw new SfKnowledgeBaseDocumentTypeNotSupportedException(documentType.ToLower());
        }
    }
}