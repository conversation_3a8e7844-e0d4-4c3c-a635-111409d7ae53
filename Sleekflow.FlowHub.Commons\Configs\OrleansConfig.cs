﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Commons.Configs;

public interface IOrleansConfig
{
    string OrleansStorageConnStr { get; }
}

public class OrleansConfig : IConfig, IOrleansConfig
{
    public string OrleansStorageConnStr { get; private set; }

    public OrleansConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        OrleansStorageConnStr =
            Environment.GetEnvironmentVariable("ORLEANS_STORAGE_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("ORLEANS_STORAGE_CONN_STR");
    }
}