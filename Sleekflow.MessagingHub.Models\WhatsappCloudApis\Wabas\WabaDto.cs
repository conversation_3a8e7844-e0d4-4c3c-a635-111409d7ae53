using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.Datasets;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;

public class WabaDto : IHasCreatedAt, IHasUpdatedAt
{
    private const string VerificationStatus = "verification_status";

    private const string ProfilePictureUri = "profile_picture_uri";

    private const string Vertical = "vertical";

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty("sleekflow_company_ids", NullValueHandling = NullValueHandling.Ignore)]
    public List<string>? SleekflowCompanyIds { get; set; }

    [JsonProperty("facebook_business_id")]
    public string? FacebookBusinessId { get; set; }

    [JsonProperty("facebook_waba_business_id")]
    public string FacebookWabaBusinessId { get; set; }

    [JsonProperty("facebook_waba_business_name")]
    public string? FacebookWabaBusinessName { get; set; }

    [JsonProperty("facebook_waba_business_verification_status")]
    public string? FacebookWabaBusinessVerificationStatus { get; set; }

    [JsonProperty("facebook_waba_business_profile_picture_uri")]
    public string? FacebookWabaBusinessProfilePictureUri { get; set; }

    [JsonProperty("facebook_waba_business_vertical")]
    public string? FacebookWabaBusinessVertical { get; set; }

    [JsonProperty("facebook_waba_name")]
    public string? FacebookWabaName { get; set; }

    [JsonProperty("facebook_waba_account_review_status")]
    public string? FacebookWabaAccountReviewStatus { get; set; }

    [JsonProperty("facebook_waba_message_template_namespace")]
    public string? FacebookWabaMessageTemplateNamespace { get; set; }

    [JsonProperty("waba_dto_phone_numbers")]
    public List<WabaPhoneNumberDto> WabaPhoneNumbers { get; set; }

    [JsonProperty("waba_product_catalog")]
    public WabaProductCatalogDto? WabaProductCatalog { get; set; }

    [JsonProperty("waba_dataset")]
    public WabaDatasetDto? WabaDataset { get; set; }

    [JsonProperty("marketing_messages_lite_api_status")]
    public string? MarketingMessagesLiteApiStatus { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public WabaDto(
        string id,
        string facebookWabaId,
        List<string> sleekflowCompanyIds,
        string? facebookBusinessId,
        string facebookWabaBusinessId,
        string? facebookWabaBusinessName,
        string? facebookWabaBusinessVerificationStatus,
        string? facebookWabaBusinessProfilePictureUri,
        string? facebookWabaBusinessVertical,
        string? facebookWabaName,
        string? facebookWabaAccountReviewStatus,
        string? facebookWabaMessageTemplateNamespace,
        List<WabaPhoneNumberDto> wabaPhoneNumbers,
        WabaProductCatalogDto? wabaProductCatalog,
        WabaDatasetDto? wabaDataset,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string? marketingMessagesLiteApiStatus = null)
    {
        Id = id;
        FacebookWabaId = facebookWabaId;
        SleekflowCompanyIds = sleekflowCompanyIds;
        FacebookBusinessId = facebookBusinessId;
        FacebookWabaBusinessId = facebookWabaBusinessId;
        FacebookWabaBusinessName = facebookWabaBusinessName;
        FacebookWabaBusinessVerificationStatus = facebookWabaBusinessVerificationStatus;
        FacebookWabaBusinessProfilePictureUri = facebookWabaBusinessProfilePictureUri;
        FacebookWabaBusinessVertical = facebookWabaBusinessVertical;
        FacebookWabaName = facebookWabaName;
        FacebookWabaAccountReviewStatus = facebookWabaAccountReviewStatus;
        FacebookWabaMessageTemplateNamespace = facebookWabaMessageTemplateNamespace;
        WabaPhoneNumbers = wabaPhoneNumbers;
        WabaProductCatalog = wabaProductCatalog;
        WabaDataset = wabaDataset;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        MarketingMessagesLiteApiStatus = marketingMessagesLiteApiStatus;
    }

    public WabaDto(Waba waba)
        : this(
            waba.Id,
            waba.FacebookWabaId,
            null,
            waba.FacebookBusinessId,
            waba.FacebookWabaBusinessId,
            waba.FacebookWabaBusinessName,
            waba.FacebookWabaBusinessDetailSnapshot?.GetValueOrDefault(VerificationStatus)?.ToString(),
            waba.FacebookWabaBusinessDetailSnapshot?.GetValueOrDefault(ProfilePictureUri)?.ToString(),
            waba.FacebookWabaBusinessDetailSnapshot?.GetValueOrDefault(Vertical)?.ToString(),
            waba.FacebookWabaName,
            waba.FacebookWabaAccountReviewStatus,
            waba.FacebookWabaMessageTemplateNamespace,
            waba.WabaPhoneNumbers
                .Where(wpn => wpn.RecordStatus == WabaPhoneNumberStatuses.Active)
                .Select(wpn => new WabaPhoneNumberDto(wpn)).ToList(),
            waba.WabaProductCatalog != null? new WabaProductCatalogDto(waba.WabaProductCatalog) : null,
            waba.WabaDataset != null? new WabaDatasetDto(waba.WabaDataset) : null,
            waba.CreatedAt,
            waba.UpdatedAt,
            waba.MarketingMessagesLiteApiStatus)
    {
    }
}