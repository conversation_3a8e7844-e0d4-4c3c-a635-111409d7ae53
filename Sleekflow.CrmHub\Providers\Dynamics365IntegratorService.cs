using Sleekflow.CrmHub.Configs;
using Sleekflow.CrmHub.Models.ObjectIdResolvers;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers.States;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Providers;

public class Dynamics365IntegratorService : GenericProviderService
{
    public const string ProviderName = "d365";

    private readonly IDynamics365ObjectIdResolver _dynamics365ObjectIdResolver;

    public Dynamics365IntegratorService(
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        IProviderStateService providerStateService,
        ILoopThroughObjectsProgressStateService loopThroughObjectsProgressStateService,
        IDynamics365ObjectIdResolver dynamics365ObjectIdResolver,
        ICustomSyncConfigService customSyncConfigService,
        ILogger<Dynamics365IntegratorService> logger)
        : base(
            providerStateService,
            loopThroughObjectsProgressStateService,
            httpClientFactory.CreateClient("default-handler"),
            customSyncConfigService,
            ProviderName,
            appConfig.Dynamics365IntegratorEndpoint,
            logger)
    {
        _dynamics365ObjectIdResolver = dynamics365ObjectIdResolver;
    }

    public override Task<string?> ResolveObjectIdAsync(Dictionary<string, object?> dict, string entityTypeName)
    {
        try
        {
            return Task.FromResult((string?) _dynamics365ObjectIdResolver.ResolveObjectId(dict, entityTypeName));
        }
        catch (SfIdUnresolvableException)
        {
            return Task.FromResult((string?) null);
        }
    }
}