using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.WorkflowAgentConfigMappings;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowAgentConfigMappings;

[TriggerGroup(ControllerNames.WorkflowAgentConfigMappings)]
public class GetWorkflowsByAgentConfigId : ITrigger
{
    private readonly IWorkflowAgentConfigMappingService _workflowAgentConfigMappingService;

    public GetWorkflowsByAgentConfigId(
        IWorkflowAgentConfigMappingService workflowAgentConfigMappingService)
    {
        _workflowAgentConfigMappingService = workflowAgentConfigMappingService;
    }

    public class GetWorkflowsByAgentConfigIdInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

    }

    public class GetWorkflowsByAgentConfigIdOutput
    {
        [JsonProperty("workflows")]
        public List<ProxyWorkflow> Workflows { get; set; }

        [JsonConstructor]
        public GetWorkflowsByAgentConfigIdOutput(
            List<ProxyWorkflow> workflows)
        {
            Workflows = workflows;
        }
    }

    public async Task<GetWorkflowsByAgentConfigIdOutput> F(GetWorkflowsByAgentConfigIdInput getWorkflowsByAgentConfigIdInput)
    {
        var workflows = await _workflowAgentConfigMappingService.GetWorkflowsByAgentConfigIdAsync(
            getWorkflowsByAgentConfigIdInput.SleekflowCompanyId,
            getWorkflowsByAgentConfigIdInput.AgentConfigId);

        return new GetWorkflowsByAgentConfigIdOutput(workflows);
    }
}