using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.TicketingHubDb;

public interface ITicketingHubDbResolver : IContainerResolver
{
}

public class TicketingHubDbResolver : ITicketingHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public TicketingHubDbResolver(ITicketingHubDbConfig ticketingHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            ticketingHubDbConfig.Endpoint,
            ticketingHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}