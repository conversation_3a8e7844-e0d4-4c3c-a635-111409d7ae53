﻿using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Workers.Configs;

public interface ISleekflowCoreConfig
{
    string CoreInternalsKey { get; }
}

public class SleekflowCoreConfig : ISleekflowCoreConfig
{
    public string CoreInternalsKey { get; }

    public SleekflowCoreConfig()
    {
        CoreInternalsKey = Environment.GetEnvironmentVariable("CORE_INTERNALS_KEY", EnvironmentVariableTarget.Process)
                       ?? throw new SfMissingEnvironmentVariableException("CORE_INTERNALS_KEY");
    }
}