﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperRuns;

[TriggerGroup(ControllerNames.WebScraperRuns)]
public class StartOneTimeRun : ITrigger<StartOneTimeRun.StartOneTimeRunInput, StartOneTimeRun.StartOneTimeRunOutput>
{
    private readonly IWebScraperService _webScraperService;

    public StartOneTimeRun(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class StartOneTimeRunInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperRun.PropertyNameOneTimeRunName)]
        public string OneTimeRunName { get; set; }

        [Required]
        [JsonProperty(WebScraperSetting.PropertyNameWebScraperSetting)]
        public WebScraperSetting WebScraperSetting { get; set; }

        [JsonConstructor]
        public StartOneTimeRunInput(
            string sleekflowCompanyId,
            WebScraperSetting webScraperSetting,
            string oneTimeRunName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WebScraperSetting = webScraperSetting;
            OneTimeRunName = oneTimeRunName;
        }
    }

    public class StartOneTimeRunOutput
    {
        [JsonProperty(WebScraperRun.PropertyNameWebScraperRun)]
        public WebScraperRun WebScraperRun { get; set; }

        [JsonConstructor]
        public StartOneTimeRunOutput(WebScraperRun webScraperRun)
        {
            WebScraperRun = webScraperRun;
        }
    }

    public async Task<StartOneTimeRunOutput> F(StartOneTimeRunInput startOneTimeRunInput)
    {
        var run = await _webScraperService.StartOneTimeRunAsync(
            startOneTimeRunInput.SleekflowCompanyId,
            startOneTimeRunInput.OneTimeRunName,
            startOneTimeRunInput.WebScraperSetting);
        return new StartOneTimeRunOutput(run);
    }
}