﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Schemas;

[TriggerGroup(TriggerGroups.Schemas)]
public class CreateSchema : ITrigger<CreateSchema.CreateSchemaInput, CreateSchema.CreateSchemaOutput>
{
    private readonly ISchemaService _schemaService;

    public CreateSchema(
        ISchemaService schemaService)
    {
        _schemaService = schemaService;
    }

    public class CreateSchemaInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(Schema.PropertyNameDisplayName)]
        public string DisplayName { get; set; }

        [Required]
        [JsonProperty(IProperty.PropertyNameUniqueName)]
        public string UniqueName { get; set; }

        [Required]
        [JsonProperty(Schema.PropertyNameRelationshipType)]
        public string RelationshipType { get; set; }

        [Required]
        [JsonProperty("property_inputs")]
        [Validations.ValidateArray]
        public List<PropertyInput> PropertyInputs { get; set; }

        [Required]
        [JsonProperty("primary_property_input")]
        [Validations.ValidateObject]
        public PrimaryPropertyInput PrimaryPropertyInput { get; set; }

        [Required]
        [JsonProperty(Schema.PropertyNameSchemaAccessibilitySettings)]
        public SchemaAccessibilitySettings? SchemaAccessibilitySettings { get; set; }

        [JsonConstructor]
        public CreateSchemaInput(
            string sleekflowCompanyId,
            string displayName,
            string uniqueName,
            string relationshipType,
            List<PropertyInput> propertyInputs,
            PrimaryPropertyInput primaryPropertyInput,
            SchemaAccessibilitySettings? schemaAccessibilitySettings)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DisplayName = displayName;
            UniqueName = uniqueName;
            RelationshipType = relationshipType;
            PropertyInputs = propertyInputs;
            PrimaryPropertyInput = primaryPropertyInput;
            SchemaAccessibilitySettings = schemaAccessibilitySettings;
        }
    }

    public class CreateSchemaOutput
    {
        [JsonProperty("schema")]
        public SchemaDto Schema { get; set; }

        [JsonConstructor]
        public CreateSchemaOutput(
            SchemaDto schema)
        {
            Schema = schema;
        }
    }

    public async Task<CreateSchemaOutput> F(CreateSchemaInput createSchemaInput)
    {
        var schema = await _schemaService.CreateAndGetAsync(
            createSchemaInput.DisplayName,
            createSchemaInput.UniqueName,
            createSchemaInput.SleekflowCompanyId,
            createSchemaInput.RelationshipType,
            createSchemaInput.PropertyInputs,
            createSchemaInput.PrimaryPropertyInput,
            createSchemaInput.SchemaAccessibilitySettings ?? SchemaAccessibilitySettings.Default());

        return new CreateSchemaOutput(new SchemaDto(schema));
    }
}