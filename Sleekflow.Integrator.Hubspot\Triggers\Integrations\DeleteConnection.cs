﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.Subscriptions;
using Sleekflow.Integrator.Hubspot.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeleteConnection : ITrigger
{
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;
    private readonly IHubspotSubscriptionService _hubspotSubscriptionService;
    private readonly IHubspotUserMappingConfigService _hubspotUserMappingConfigService;

    public DeleteConnection(
        IHubspotConnectionService hubspotConnectionService,
        IHubspotAuthenticationService hubspotAuthenticationService,
        IHubspotSubscriptionService hubspotSubscriptionService,
        IHubspotUserMappingConfigService hubspotUserMappingConfigService)
    {
        _hubspotConnectionService = hubspotConnectionService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
        _hubspotSubscriptionService = hubspotSubscriptionService;
        _hubspotUserMappingConfigService = hubspotUserMappingConfigService;
    }

    public class DeleteConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public DeleteConnectionInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class DeleteConnectionOutput
    {
    }

    public async Task<DeleteConnectionOutput> F(DeleteConnectionInput deleteConnectionInput)
    {
        var connection = await _hubspotConnectionService.GetByIdAsync(
            deleteConnectionInput.ConnectionId,
            deleteConnectionInput.SleekflowCompanyId);

        if (connection is null)
        {
            throw new SfNotFoundObjectException(
                deleteConnectionInput.ConnectionId,
                deleteConnectionInput.SleekflowCompanyId);
        }

        await _hubspotSubscriptionService.ClearByConnectionIdAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        await _hubspotUserMappingConfigService.ClearAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        await _hubspotAuthenticationService.DeleteAsync(
            connection.AuthenticationId);

        await _hubspotConnectionService.DeleteAsync(
            connection.Id,
            connection.SleekflowCompanyId);

        return new DeleteConnectionOutput();
    }
}