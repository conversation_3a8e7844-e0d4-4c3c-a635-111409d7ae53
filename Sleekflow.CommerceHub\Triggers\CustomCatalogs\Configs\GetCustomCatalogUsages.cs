using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs.Config;

[TriggerGroup(ControllerNames.CustomCatalogs)]
public class GetCustomCatalogUsages
    : ITrigger<
        GetCustomCatalogUsages.GetCustomCatalogUsagesInput,
        GetCustomCatalogUsages.GetCustomCatalogUsagesOutput>
{
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;

    public GetCustomCatalogUsages(IProductService productService, IProductVariantService productVariantService)
    {
        _productService = productService;
        _productVariantService = productVariantService;
    }

    public class GetCustomCatalogUsagesInput
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetCustomCatalogUsagesInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetCustomCatalogUsagesOutput
    {
        [JsonProperty("product_count")]
        public int ProductCount { get; set; }

        [JsonProperty("product_variant_count")]
        public int ProductVariantCount { get; set; }

        [JsonConstructor]
        public GetCustomCatalogUsagesOutput(int productCount, int productVariantCount)
        {
            ProductCount = productCount;
            ProductVariantCount = productVariantCount;
        }
    }

    public async Task<GetCustomCatalogUsagesOutput> F(GetCustomCatalogUsagesInput getCustomCatalogUsagesInput)
    {
        var sleekflowCompanyId = getCustomCatalogUsagesInput.SleekflowCompanyId;
        var countProducts = await _productService.GetCountProductsAsync(sleekflowCompanyId);
        var countProductVariants = await _productVariantService.GetCountProductVariantsAsync(sleekflowCompanyId);
        return new GetCustomCatalogUsagesOutput(countProducts, countProductVariants);
    }
}