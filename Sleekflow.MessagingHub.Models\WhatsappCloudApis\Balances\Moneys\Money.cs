using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;

public sealed class Money : IEquatable<Money>
{
    public const string PropertyNameCurrencyIsoCode = "currency_iso_code";

    public const string PropertyNameAmount = "amount";

    [JsonProperty(PropertyNameCurrencyIsoCode)]
    public string CurrencyIsoCode { get; set; }

    [JsonProperty(PropertyNameAmount)]
    public decimal Amount { get; set; }

    [JsonConstructor]
    public Money(string currencyIsoCode, decimal amount)
    {
        CurrencyIsoCode = currencyIsoCode;
        Amount = amount;
    }

    public override string ToString()
    {
        return $"{Amount} {CurrencyIsoCode}";
    }

    public bool Equals(Money? other)
    {
        if (ReferenceEquals(null, other))
        {
            return false;
        }

        if (ReferenceEquals(this, other))
        {
            return true;
        }

        return CurrencyIsoCode == other.CurrencyIsoCode && Amount == other.Amount;
    }

    public override bool Equals(object? obj)
    {
        if (ReferenceEquals(null, obj))
        {
            return false;
        }

        if (ReferenceEquals(this, obj))
        {
            return true;
        }

        return obj.GetType() == GetType() && Equals((Money) obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(CurrencyIsoCode, Amount);
    }
}