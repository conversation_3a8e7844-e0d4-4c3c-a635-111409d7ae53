using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class GetFacebookBusinessProductCatalogs : ITrigger<
    GetFacebookBusinessProductCatalogs.GetFacebookBusinessProductCatalogsInput,
    GetFacebookBusinessProductCatalogs.GetFacebookBusinessProductCatalogsOutput>
{
    private readonly IProductCatalogService _productCatalogService;
    private readonly ILogger<GetFacebookBusinessProductCatalogs> _logger;

    public GetFacebookBusinessProductCatalogs(IProductCatalogService productCatalogService, ILogger<GetFacebookBusinessProductCatalogs> logger)
    {
        _productCatalogService = productCatalogService;
        _logger = logger;
    }

    public class GetFacebookBusinessProductCatalogsInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("facebook_business_id")]
        public string FacebookBusinessId { get; set; }

        [JsonConstructor]
        public GetFacebookBusinessProductCatalogsInput(
            string sleekflowCompanyId,
            string facebookBusinessId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FacebookBusinessId = facebookBusinessId;
        }
    }

    public class GetFacebookBusinessProductCatalogsOutput
    {
        [JsonProperty("waba_product_catalog")]
        public List<WabaProductCatalogDto> WabaProductCatalogs { get; set; }

        [JsonConstructor]
        public GetFacebookBusinessProductCatalogsOutput(List<WabaProductCatalogDto> wabaProductCatalogs)
        {
            WabaProductCatalogs = wabaProductCatalogs;
        }
    }

    public async Task<GetFacebookBusinessProductCatalogsOutput> F(
        GetFacebookBusinessProductCatalogsInput getFacebookBusinessProductCatalogsInput)
    {
        _logger.LogInformation("getting facebook business id {FacebookBusinessId} owned product catalogs", getFacebookBusinessProductCatalogsInput.FacebookBusinessId);

        return new GetFacebookBusinessProductCatalogsOutput(
            await _productCatalogService.GetFacebookBusinessProductCatalogsAsync(
                getFacebookBusinessProductCatalogsInput.SleekflowCompanyId,
                getFacebookBusinessProductCatalogsInput.FacebookBusinessId));
    }
}