﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Dynamics365.Objects;

public class Dynamics365GetObjectsODataCountOutput
{
    [JsonConstructor]
    public Dynamics365GetObjectsODataCountOutput(string odataContext, long odataCount)
    {
        OdataContext = odataContext;
        OdataCount = odataCount;
    }

    [JsonProperty("@odata.context")]
    public string OdataContext { get; set; }

    [JsonProperty("@odata.count")]
    public long OdataCount { get; set; }
}