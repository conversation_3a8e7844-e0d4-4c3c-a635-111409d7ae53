using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public abstract class BaseAction
{
    [JsonProperty("enabled")]
    public bool Enabled { get; set; }

    [JsonConstructor]
    protected BaseAction(bool enabled)
    {
        Enabled = enabled;
    }

    protected BaseAction(BaseActionDto dto)
    {
        Enabled = dto.Enabled;
    }

    public abstract BaseActionDto ToDto();
}