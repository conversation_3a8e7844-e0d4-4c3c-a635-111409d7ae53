﻿using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Polly;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

internal class Delete : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private List<string>? _containerIds;

    public Delete(
        DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
            });
    }

    public string GetDisplayName()
    {
        return "Delete";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerIds = Prompt.MultiSelect(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerIds = selectedContainerIds.ToList();
    }

    public async Task ExecuteAsync()
    {
        foreach (var containerId in _containerIds!)
        {
            var count = await DeleteObjectsAsync(containerId);
            Console.WriteLine($"Completed {containerId} for {count} objects");
        }
    }

    private async Task<int> DeleteObjectsAsync(
        string containerId)
    {
        var retryPolicy = CosmosUtils.GetDefaultRetryPolicy(containerId);

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerId);
        var partitionKeyPaths =
            (await CosmosUtils.GetContainerPartitionKeyPathsAsync(_cosmosClient, _databaseId!, containerId))!;

        var i = 0;
        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(container),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 500
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(
                        async () => await DeleteObject(dict, container, partitionKeyPaths, token));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }

    private static async Task<int> DeleteObject(
        Dictionary<string, object?> dict,
        Container container,
        IReadOnlyList<string> partitionKeyPaths,
        CancellationToken token)
    {
        var partitionKeyBuilder = new PartitionKeyBuilder();
        foreach (var partitionKeyPath in partitionKeyPaths!)
        {
            var keyParts = partitionKeyPath.TrimStart('/').Split('/');
            var currentValue = CosmosUtils.GetValueFromDictionary(dict, keyParts, 0);
            partitionKeyBuilder.Add(currentValue);
        }

        await container.DeleteItemAsync<Dictionary<string, object?>>(
            (string) dict["id"]!,
            partitionKeyBuilder.Build(),
            cancellationToken: token);

        return 1;
    }
}