using MassTransit;
using Microsoft.AspNetCore.Mvc;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Disposable.Events;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Disposable.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class DisposableWebhookController : ControllerBase
{
    private readonly IEmailProviderSelector _providerSelector;
    private readonly IBus _bus;
    private readonly ILogger<DisposableWebhookController> _logger;
    private readonly IProviderConfigService _providerConfigService;

    public DisposableWebhookController(
        IBus bus,
        ILogger<DisposableWebhookController> logger,
        IEmailProviderSelector providerSelector,
        IProviderConfigService providerConfigService)
    {
        _bus = bus;
        _logger = logger;
        _providerSelector = providerSelector;
        _providerConfigService = providerConfigService;
    }

    [Route("NotifyOnDisposableReceive")]
    [HttpPost]
    public async Task<IActionResult> NotifyOnDisposableReceive()
    {
        var toAddressString = Request.Form["to"].ToString();
        var emailMessage = Request.Form["email"].ToString();

        var emailMetadata = new Dictionary<string, string>
        {
            {
                "email", emailMessage
            }
        };

        var sleekflowCompanyId = (await _providerConfigService.GetCompanyIdsByEmailAddressAsync(toAddressString))[0];

        _logger.LogInformation(
            "Receive disposable notification on mailbox : emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            toAddressString,
            sleekflowCompanyId);

        await _bus.Publish(
            new OnDisposableReceiveEmailTriggeredEvent(
                sleekflowCompanyId,
                toAddressString,
                emailMetadata));

        return Ok();
    }
}