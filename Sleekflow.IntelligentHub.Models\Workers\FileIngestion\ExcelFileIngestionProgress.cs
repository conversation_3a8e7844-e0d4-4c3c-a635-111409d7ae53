using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

public class ExcelFileIngestionProgress : IFileIngestionProgress
{
    [JsonProperty("processed_worksheets")]
    public int ProcessedWorksheets { get; set; }

    [JsonProperty("total_worksheets")]
    public int TotalWorksheets { get; set; }

    [JsonConstructor]
    public ExcelFileIngestionProgress(int processedWorksheets, int totalWorksheets)
    {
        ProcessedWorksheets = processedWorksheets;
        TotalWorksheets = totalWorksheets;
    }

    public bool IsCompleted()
    {
        return ProcessedWorksheets >= TotalWorksheets;
    }

    public double GetProgressPercentage()
    {
        return TotalWorksheets == 0 ? 100.0 : (ProcessedWorksheets * 100.0 / TotalWorksheets);
    }
}