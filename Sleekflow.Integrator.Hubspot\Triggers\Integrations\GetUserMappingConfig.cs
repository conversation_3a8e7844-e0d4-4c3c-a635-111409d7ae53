﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Hubspot.Authentications;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Integrator.Hubspot.UserMappingConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetUserMappingConfig : ITrigger
{
    private readonly IHubspotUserMappingConfigService _hubspotUserMappingConfigService;
    private readonly IHubspotConnectionService _hubspotConnectionService;
    private readonly IHubspotAuthenticationService _hubspotAuthenticationService;

    public GetUserMappingConfig(
        IHubspotUserMappingConfigService hubspotUserMappingConfigService,
        IHubspotConnectionService hubspotConnectionService,
        IHubspotAuthenticationService hubspotAuthenticationService)
    {
        _hubspotUserMappingConfigService = hubspotUserMappingConfigService;
        _hubspotConnectionService = hubspotConnectionService;
        _hubspotAuthenticationService = hubspotAuthenticationService;
    }

    public class GetUserMappingConfigInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonConstructor]
        public GetUserMappingConfigInput(
            string sleekflowCompanyId,
            string connectionId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
        }
    }

    public class GetUserMappingConfigOutput
    {
        [JsonProperty("user_mapping_config")]
        public ProviderUserMappingConfigDto UserMappingConfig { get; set; }

        [JsonConstructor]
        public GetUserMappingConfigOutput(
            ProviderUserMappingConfigDto userMappingConfig)
        {
            UserMappingConfig = userMappingConfig;
        }
    }

    public async Task<GetUserMappingConfigOutput> F(
        GetUserMappingConfigInput getUserMappingConfigInput)
    {
        var sleekflowCompanyId = getUserMappingConfigInput.SleekflowCompanyId;
        var connectionId = getUserMappingConfigInput.ConnectionId;

        var connection =
            await _hubspotConnectionService.GetByIdAsync(
                connectionId,
                sleekflowCompanyId);

        var authentication =
            await _hubspotAuthenticationService.GetAsync(
                connection.AuthenticationId,
                sleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var userMappingConfig =
            await _hubspotUserMappingConfigService.GetAsync(
                sleekflowCompanyId,
                connectionId);

        return new GetUserMappingConfigOutput(
            new ProviderUserMappingConfigDto(
                userMappingConfig.Id,
                userMappingConfig.SleekflowCompanyId,
                userMappingConfig.ConnectionId,
                userMappingConfig.UserMappings));
    }
}