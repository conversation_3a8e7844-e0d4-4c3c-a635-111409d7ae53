﻿using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileSoftDeletedLogs
    : ITrigger<
        CreateUserProfileSoftDeletedLogs.CreateUserProfileSoftDeletedLogsInput,
        CreateUserProfileSoftDeletedLogs.CreateUserProfileSoftDeletedLogsOutput>
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;

    public CreateUserProfileSoftDeletedLogs(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
    }

    public class CreateUserProfileSoftDeletedLogsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_ids")]
        public List<string> SleekflowUserProfileIds { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        public UserProfileSoftDeletedLogData Data { get; set; }

        [JsonConstructor]
        public CreateUserProfileSoftDeletedLogsInput(
            string sleekflowCompanyId,
            List<string> sleekflowUserProfileIds,
            string? sleekflowStaffId,
            string auditLogText)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileIds = sleekflowUserProfileIds;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
        }
    }

    public class CreateUserProfileSoftDeletedLogsOutput
    {
        [JsonProperty("ids")]
        public List<string> Ids { get; set; }

        [JsonConstructor]
        public CreateUserProfileSoftDeletedLogsOutput(List<string> ids)
        {
            Ids = ids;
        }
    }

    public async Task<CreateUserProfileSoftDeletedLogsOutput> F(
        CreateUserProfileSoftDeletedLogsInput createUserProfileSoftDeletedLogsInput)
    {
        const int maxDegreeOfParallelism = 3;

        var sleekflowCompanyId = createUserProfileSoftDeletedLogsInput.SleekflowCompanyId;
        var sleekflowStaffId = createUserProfileSoftDeletedLogsInput.SleekflowStaffId;
        var auditLogText = createUserProfileSoftDeletedLogsInput.AuditLogText;
        var dataStr = JsonConvert.SerializeObject(createUserProfileSoftDeletedLogsInput.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var auditLogIds = new ConcurrentBag<string>();

        await Parallel.ForEachAsync(
            createUserProfileSoftDeletedLogsInput.SleekflowUserProfileIds,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = maxDegreeOfParallelism
            },
            async (sleekflowUserProfileId, _) =>
            {
                var id = _idService.GetId("UserProfileAuditLog");
                await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
                    new UserProfileAuditLog(
                        id,
                        sleekflowCompanyId,
                        sleekflowStaffId,
                        sleekflowUserProfileId,
                        UserProfileAuditLogTypes.UserProfileSoftDeleted,
                        auditLogText,
                        data,
                        DateTimeOffset.UtcNow,
                        null));

                auditLogIds.Add(id);
            });

        return new CreateUserProfileSoftDeletedLogsOutput(auditLogIds.ToList());
    }
}