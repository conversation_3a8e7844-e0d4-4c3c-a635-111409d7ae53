using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.ProductVariants;

[TriggerGroup(ControllerNames.ProductVariants)]
public class GetProductVariants
    : ITrigger<
        GetProductVariants.GetProductVariantsInput,
        GetProductVariants.GetProductVariantsOutput>
{
    private readonly IProductVariantService _productVariantService;

    public GetProductVariants(
        IProductVariantService productVariantService)
    {
        _productVariantService = productVariantService;
    }

    public class GetProductVariantsInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("product_id")]
        public string ProductId { get; set; }

        [JsonConstructor]
        public GetProductVariantsInput(
            string sleekflowCompanyId,
            string storeId,
            string productId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductId = productId;
        }
    }

    public class GetProductVariantsOutput
    {
        [JsonProperty("product_variants")]
        public List<ProductVariantDto> ProductVariants { get; set; }

        [JsonConstructor]
        public GetProductVariantsOutput(
            List<ProductVariant> productVariants)
        {
            ProductVariants = productVariants.Select(r => new ProductVariantDto(r)).ToList();
        }
    }

    public async Task<GetProductVariantsOutput> F(GetProductVariantsInput getProductVariantsInput)
    {
        var productVariants = await _productVariantService.GetProductVariantsAsync(
            getProductVariantsInput.SleekflowCompanyId,
            getProductVariantsInput.StoreId,
            getProductVariantsInput.ProductId);

        return new GetProductVariantsOutput(productVariants);
    }
}