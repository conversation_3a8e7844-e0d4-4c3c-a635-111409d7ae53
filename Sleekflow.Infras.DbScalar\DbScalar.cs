using System.Net.Http.Headers;
using System.Text;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Sleekflow.Infras.DbScalar;

public static class DbScalar
{
    public static async Task<string?> GetAccessTokenAsync(
        HttpClient client,
        string tenantId,
        string clientId,
        string clientSecret)
    {
        var requestMessage = new HttpRequestMessage
        {
            Method = HttpMethod.Get, RequestUri = new Uri($"https://login.microsoftonline.com/{tenantId}/oauth2/token")
        };
        var requestBody = new FormUrlEncodedContent(
            new List<KeyValuePair<string, string>>
            {
                new KeyValuePair<string, string>("grant_type", "client_credentials"),
                new KeyValuePair<string, string>("client_id", clientId),
                new KeyValuePair<string, string>("client_secret", clientSecret),
                new KeyValuePair<string, string>("resource", "https://management.azure.com")
            });
        requestMessage.Content = requestBody;
        var httpResponseMessage = await client.SendAsync(requestMessage);
        if (!httpResponseMessage.IsSuccessStatusCode)
        {
            return null;
        }

        var str = await httpResponseMessage.Content.ReadAsStringAsync();
        var strObj = JObject.Parse(str);
        var token = strObj["access_token"]?.ToString();
        return token;
    }

    // https://learn.microsoft.com/en-us/rest/api/sql/2022-05-01-preview/databases/update?tabs=HTTP
    public static async Task ScaleDbAsync(
        HttpClient httpClient,
        string? accessToken,
        string subscriptionId,
        string resourceGroupName,
        string serverName,
        string databaseName,
        int skuCapacity,
        int highAvailabilityReplicaCount,
        ILogger log)
    {
        var body = new DbUpdateRequest
        {
            Sku = new Sku
            {
                Name = "HS_PRMS", Tier = "Hyperscale", Family = "PRMS", Capacity = skuCapacity
            },
            Properties = new Properties
            {
                HighAvailabilityReplicaCount = highAvailabilityReplicaCount,
                ReadScale = highAvailabilityReplicaCount > 0 ? "Enabled" : "Disabled"
            }
        };
        var url =
            $"https://management.azure.com/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.Sql/servers/{serverName}/databases/{databaseName}?api-version=2021-02-01-preview";
        httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", accessToken);

        var stringContent = new StringContent(JsonConvert.SerializeObject(body), Encoding.UTF8, "application/json");
        var responseMessage = await httpClient.PatchAsync(url, stringContent);
        var content = await responseMessage.Content.ReadAsStringAsync();
        var code = responseMessage.StatusCode.ToString();

        log.LogInformation(
            $"ScaleDbAsync at {DateTime.Now}, response status code : {code}, responseContent : {content}");
    }

    public class DbUpdateRequest
    {
        [JsonProperty("sku")]
        public Sku? Sku { get; set; }

        [JsonProperty("properties")]
        public Properties? Properties { get; set; }
    }

    public class Sku
    {
        [JsonProperty("name")]
        public string? Name { get; set; }

        [JsonProperty("tier")]
        public string? Tier { get; set; }

        [JsonProperty("family")]
        public string? Family { get; set; }

        [JsonProperty("capacity")]
        public int Capacity { get; set; }
    }

    // {
    //     "type": "Microsoft.Sql/servers/databases",
    //     "apiVersion": "2023-05-01-preview",
    //     "name": "[concat(parameters('servers_sleekflow_core_sql_server_eas_productiond2a4d949_name'), '/sleekflow-crm-prod')]",
    //     "location": "eastasia",
    //     "sku": {
    //         "name": "HS_PRMS",
    //         "tier": "Hyperscale",
    //         "family": "PRMS",
    //         "capacity": 80
    //     },
    //     "kind": "v12.0,user,vcore,hyperscale",
    //     "properties": {
    //         "collation": "SQL_Latin1_General_CP1_CI_AS",
    //         "maxSizeBytes": -1,
    //         "catalogCollation": "SQL_Latin1_General_CP1_CI_AS",
    //         "zoneRedundant": false,
    //         "licenseType": "LicenseIncluded",
    //         "readScale": "Enabled",
    //         "highAvailabilityReplicaCount": 1,
    //         "requestedBackupStorageRedundancy": "Geo",
    //         "maintenanceConfigurationId": "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/providers/Microsoft.Maintenance/publicMaintenanceConfigurations/SQL_Default",
    //         "isLedgerOn": false,
    //         "availabilityZone": "NoPreference"
    //     }
    // },
    // {
    //     "type": "Microsoft.Sql/servers/databases",
    //     "apiVersion": "2023-05-01-preview",
    //     "name": "[concat(parameters('servers_sleekflow_core_sql_server_eas_productiond2a4d949_name'), '/sleekflow-crm-prod')]",
    //     "location": "eastasia",
    //     "sku": {
    //         "name": "HS_PRMS",
    //         "tier": "Hyperscale",
    //         "family": "PRMS",
    //         "capacity": 80
    //     },
    //     "kind": "v12.0,user,vcore,hyperscale",
    //     "properties": {
    //         "collation": "SQL_Latin1_General_CP1_CI_AS",
    //         "maxSizeBytes": -1,
    //         "catalogCollation": "SQL_Latin1_General_CP1_CI_AS",
    //         "zoneRedundant": false,
    //         "licenseType": "LicenseIncluded",
    //         "readScale": "Disabled",
    //         "highAvailabilityReplicaCount": 0,
    //         "requestedBackupStorageRedundancy": "Geo",
    //         "maintenanceConfigurationId": "/subscriptions/c19c9b56-93e9-4d4c-bc81-838bd3f72ad6/providers/Microsoft.Maintenance/publicMaintenanceConfigurations/SQL_Default",
    //         "isLedgerOn": false,
    //         "availabilityZone": "NoPreference"
    //     }
    // },

    public class Properties
    {
        [JsonProperty("highAvailabilityReplicaCount")]
        public int HighAvailabilityReplicaCount { get; set; }

        [JsonProperty("readScale")]
        public string? ReadScale { get; set; }
    }
}