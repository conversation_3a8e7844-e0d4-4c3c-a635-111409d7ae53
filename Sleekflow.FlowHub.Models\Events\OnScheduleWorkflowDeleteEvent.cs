﻿using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Events;

public class OnScheduleWorkflowDeleteEvent : IHasSleekflowCompanyId
{
    public DateTimeOffset DeletedAt { get; } = DateTimeOffset.UtcNow;

    public string SleekflowCompanyId { get; set; }

    public string WorkflowId { get; set; }

    public AuditEntity.SleekflowStaff SleekflowStaff { get; set; }

    public OnScheduleWorkflowDeleteEvent(
        string sleekflowCompanyId,
        string workflowId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        SleekflowStaff = sleekflowStaff;
    }
}