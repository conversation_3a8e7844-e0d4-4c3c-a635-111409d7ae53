﻿namespace Sleekflow.Auth0.BulkImporter.Services;

public static class EmailUtils
{
    public static bool IsValidEmail(string email)
    {
        try
        {
            var mailAddress = new System.Net.Mail.MailAddress(email);
            return mailAddress.Address == email;
        }
        catch
        {
            return false;
        }
    }

    public static string CreateTmpEmail(
        string prefix = "tmp.",
        string customDomain = "id.sleekflow.io",
        int length = 12)
    {
        return $"{prefix}{GenerateRandomString(length, false)}@{customDomain}";
    }

    internal static string GenerateRandomString(int length = 10, bool includeSymbols = true)
    {
        var rnd = new Random(Guid.NewGuid().GetHashCode());
        var chars = includeSymbols
            ? "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_"
            : "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

        return new string(
            Enumerable.Repeat(chars, length)
                .Select(s => s[rnd.Next(s.Length)]).ToArray());
    }
}