using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class CreatePostWorkflowPublishedEnrollmentConfigs : ITrigger<
    CreatePostWorkflowPublishedEnrollmentConfigs.CreatePostWorkflowPublishedEnrollmentConfigsInput,
    CreatePostWorkflowPublishedEnrollmentConfigs.CreatePostWorkflowPublishedEnrollmentConfigsOutput>
{
    private readonly INeedConfigService _needConfigService;

    public CreatePostWorkflowPublishedEnrollmentConfigs(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class CreatePostWorkflowPublishedEnrollmentConfigsInput
    {
        [Required]
        [JsonProperty("configs")]
        public List<PostWorkflowPublishedEnrollmentConfig> Configs { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonConstructor]
        public CreatePostWorkflowPublishedEnrollmentConfigsInput(
            List<PostWorkflowPublishedEnrollmentConfig> configs,
            string? version)
        {
            Configs = configs;
            Version = version;
        }
    }

    public class CreatePostWorkflowPublishedEnrollmentConfigsOutput
    {
        [JsonProperty("configs")]
        public List<PostWorkflowPublishedEnrollmentConfig> Configs { get; set; }

        [JsonConstructor]
        public CreatePostWorkflowPublishedEnrollmentConfigsOutput(List<PostWorkflowPublishedEnrollmentConfig> configs)
        {
            Configs = configs;
        }
    }

    public async Task<CreatePostWorkflowPublishedEnrollmentConfigsOutput> F(CreatePostWorkflowPublishedEnrollmentConfigsInput input)
    {
        return new CreatePostWorkflowPublishedEnrollmentConfigsOutput(
            await _needConfigService.CreatePostWorkflowPublishedEnrollmentConfigsAsync(
                input.Version,
                input.Configs));
    }
}