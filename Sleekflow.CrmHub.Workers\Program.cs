using Microsoft.DurableTask.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sleekflow;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.CrmHub.Workers.ProviderConfigs;
using Sleekflow.CrmHub.Workers.Subscriptions;
using Sleekflow.Mvc;
using Sleekflow.Persistence.Abstractions;

const string name = "SleekflowCrmHubWorker";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildDbServices(services);
        CrmHubModules.BuildCrmHubDbServices(services);
        services.AddDurableTaskClient(
            builder =>
            {
            });

        services.AddSingleton<IAppConfig, AppConfig>();
        services.AddScoped<IProviderConfigRepository, ProviderConfigRepository>();
        services.AddScoped<IProviderConfigService, ProviderConfigService>();
        services.AddScoped<IDynamicFiltersRepositoryContext, DynamicFiltersRepositoryContext>();
        services.AddScoped<ICustomSyncConfigService, CustomSyncConfigService>();
        services.AddSingleton<IHubspotSubscriptionRepository, HubspotSubscriptionRepository>();
        services.AddSingleton<ISalesforceSubscriptionRepository, SalesforceSubscriptionRepository>();
        services.AddSingleton<IDynamics365SubscriptionRepository, Dynamics365SubscriptionRepository>();
        services.AddSingleton<IGoogleSheetsSubscriptionRepository, GoogleSheetsSubscriptionRepository>();
        services.AddSingleton<IZohoSubscriptionRepository, ZohoSubscriptionRepository>();
        services.AddHttpContextAccessor();
    });

hostBuilder.Build().Run();