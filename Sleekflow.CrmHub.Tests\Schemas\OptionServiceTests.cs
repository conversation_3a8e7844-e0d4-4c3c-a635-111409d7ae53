﻿using Microsoft.Extensions.Logging.Abstractions;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Schemas.Utils;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.CrmHub.Tests.Schemas;

public class OptionServiceTests
{
    private List<Option> MockOriginalOptions { get; set; } = null!;

    private IOptionService _optionService = null!;

    [SetUp]
    public void SetUp()
    {
        var idService = GetIdService();
        _optionService = new OptionService(idService);

        MockOriginalOptions = new List<Option>
        {
            new Option("origin_0", "0", 0),
            new Option("origin_1", "1", 1)
        };
    }

    [Test]
    public void SanitizeAndSortOptions_WithEmptyInput_ShouldReturnCorrectValue()
    {
        List<Option>? receivedOptions_null = null;
        List<Option>? originalOptions_null = null;
        var receivedOptions_empty = new List<Option>();
        var originalOptions_empty = new List<Option>();

        var res = _optionService.SanitizeAndSortOptions(receivedOptions_null, originalOptions_null);

        Assert.That(res.SanitizedOptions, Is.Null);
        Assert.That(res.DeletedOptionIds, Is.Empty);

        var res_2 = _optionService.SanitizeAndSortOptions(receivedOptions_empty, originalOptions_empty);

        Assert.That(res_2.SanitizedOptions, Is.Null);
        Assert.That(res_2.DeletedOptionIds, Is.Empty);

        var res_3 = _optionService.SanitizeAndSortOptions(receivedOptions_null, originalOptions_empty);
        Assert.That(res_3.SanitizedOptions, Is.Null);
        Assert.That(res_3.DeletedOptionIds, Is.Empty);
    }

    [Test]
    public void SanitizeAndSortOptions_WithEmptyReceivedOptions_ShouldReturnCorrectValue()
    {
        List<Option>? receivedOptions_null = null;
        var receivedOptions_empty = new List<Option>();

        var res = _optionService.SanitizeAndSortOptions(receivedOptions_null, MockOriginalOptions);

        Assert.That(res.SanitizedOptions, Is.Null);
        Assert.That(res.DeletedOptionIds.Count, Is.EqualTo(2));

        var res_2 = _optionService.SanitizeAndSortOptions(receivedOptions_empty, MockOriginalOptions);

        Assert.That(res_2.SanitizedOptions, Is.Null);
        Assert.That(res_2.DeletedOptionIds.Count, Is.EqualTo(2));
    }

    [Test]
    public void SanitizeAndSortOptions_WithEmptyOriginalOptions_ShouldReturnCorrectValue()
    {
        List<Option>? originalOptions_null = null;

        var receivedOptions = new List<Option>
        {
            new Option(string.Empty, "0", 0),
            new Option(string.Empty, "1", 3),
            new Option(string.Empty, "2", 10),
        };

        var res = _optionService.SanitizeAndSortOptions(receivedOptions, originalOptions_null);

        Assert.That(res.SanitizedOptions, Is.Not.Null);
        Assert.That(res.DeletedOptionIds, Is.Empty);

        Assert.That(string.IsNullOrWhiteSpace(res.SanitizedOptions![2].Id), Is.False);
        Assert.That(res.SanitizedOptions![2].Value, Is.EqualTo("2"));
        Assert.That(res.SanitizedOptions![2].DisplayOrder, Is.EqualTo(2));
    }

    [Test]
    public void SanitizeAndSortOptions_WithOptionChanges_ShouldReturnCorrectValue()
    {
        /*
         * - "origin_0": update value to "000", reorder to the last one
         * - "origin_1": delete
         * - add 1 new option
         */
        var receivedOptions = new List<Option>
        {
            new Option("origin_0", "000", 22),
            new Option(string.Empty, "new", 0),
        };

        var res = _optionService.SanitizeAndSortOptions(receivedOptions, MockOriginalOptions);

        Assert.That(res.SanitizedOptions, Is.Not.Null);
        Assert.That(res.DeletedOptionIds[0], Is.EqualTo("origin_1"));

        var newOption = res.SanitizedOptions![0];
        var updatedOption = res.SanitizedOptions[1];

        Assert.That(string.IsNullOrWhiteSpace(newOption.Id), Is.False);
        Assert.That(newOption.Value, Is.EqualTo("new"));

        Assert.That(updatedOption.Id, Is.EqualTo("origin_0"));
        Assert.That(updatedOption.Value, Is.EqualTo("000"));
        Assert.That(updatedOption.DisplayOrder, Is.EqualTo(1));
    }

    private IdService GetIdService()
    {
        var dbContainerResolver = new DbContainerResolver(new MyCrmHubDbConfig());

        var idService = new IdService(
            NullLogger<IdService>.Instance,
            dbContainerResolver);

        return idService;
    }

    private class MyCrmHubDbConfig : IDbConfig
    {
        public string Endpoint => "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key => "****************************************************************************************";

        public string DatabaseId => "crmhubdb";
    }
}