﻿using System.Net;
using <PERSON>;

namespace Sleekflow.FlowHub.Integrator.Https;

public static class HttpPolicies
{
    public static IAsyncPolicy<HttpResponseMessage> HttpTransientErrorRetryPolicy
        => Policy<HttpResponseMessage>
            .Handle<HttpRequestException>(
                ex =>
                    ex.StatusCode is > HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
            .OrResult(
                res =>
                    res.StatusCode is > HttpStatusCode.InternalServerError or HttpStatusCode.RequestTimeout)
            .WaitAndRetryAsync(
                retryCount: 3,
                sleepDurationProvider: currentRetryCount => TimeSpan.FromSeconds(5 * currentRetryCount));
}