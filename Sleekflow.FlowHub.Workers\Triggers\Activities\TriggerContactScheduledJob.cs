using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;

namespace Sleekflow.FlowHub.Workers.Triggers.Activities;

public class TriggerContactScheduledJob
{
    private readonly ILogger<TriggerContactScheduledJob> _logger;

    public TriggerContactScheduledJob(ILogger<TriggerContactScheduledJob> logger)
    {
        _logger = logger;
    }

    [Function("TriggerContactScheduledJob")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient client)
    {
        // return await Func.Run2Async<ContactEligibilityService.ContactScheduledJobInput, HttpManagementPayload, DurableTaskClient>(
        //     req,
        //     _logger,
        //     client,
        //     F);
        throw new NotImplementedException();
    }
    //
    // private async Task<HttpManagementPayload> F(
    //     (ContactEligibilityService.ContactScheduledJobInput Input, ILogger Logger, DurableTaskClient Starter) context)
    // {
    //     var (input, logger, starter) = context;
    //     _logger.LogInformation(
    //         "Be in to schedule a job for contact: {ContactDetail}. ContactId: {ContactId}, WorkflowVersion: {WorkflowVersionId}",
    //         input.ContactDetail,
    //         input.ContactId,
    //         input.WorkflowVersionedId);
    //
    //     var orchestratorName = "ContactSelfScheduledJob";
    //
    //     // start scheduled job
    //     var instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
    //         orchestratorName,
    //         input,
    //         );
    //     _logger.LogInformation(
    //         "Started {OrchestratorName} with InstanceId: {InstanceId}",
    //         orchestratorName,
    //         instanceId);
    //
    //     var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
    //
    //     if (httpManagementPayload == null)
    //     {
    //         _logger.LogError("failed to create http management payload");
    //         throw new Exception("Unable to get " + orchestratorName + " httpManagementPayload");
    //     }
    //
    //     return httpManagementPayload;
    // }
}