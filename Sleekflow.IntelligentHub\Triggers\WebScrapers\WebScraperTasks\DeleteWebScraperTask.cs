﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperTasks;

[TriggerGroup(ControllerNames.WebScraperTasks)]
public class DeleteWebScraperTask : ITrigger<DeleteWebScraperTask.DeleteWebScraperTaskInput, DeleteWebScraperTask.DeleteWebScraperTaskOutput>
{
    private readonly IWebScraperService _webScraperService;

    public DeleteWebScraperTask(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class DeleteWebScraperTaskInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperTask.PropertyNameApifyTaskId)]
        public string ApifyTaskId { get; set; }

        [JsonConstructor]
        public DeleteWebScraperTaskInput(string sleekflowCompanyId, string apifyTaskId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyTaskId = apifyTaskId;
        }
    }

    public class DeleteWebScraperTaskOutput
    {
    }

    public async Task<DeleteWebScraperTaskOutput> F(DeleteWebScraperTaskInput deleteWebScraperTaskInput)
    {
        await _webScraperService.DeleteWebScraperTaskAsync(
            deleteWebScraperTaskInput.SleekflowCompanyId,
            deleteWebScraperTaskInput.ApifyTaskId);

        return new DeleteWebScraperTaskOutput();
    }
}