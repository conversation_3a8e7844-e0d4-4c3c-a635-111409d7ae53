using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors;

public interface ILogStepExecutor : IStepExecutor
{
}

public class LogStepExecutor : GeneralStepExecutor<LogStep>, ILogStepExecutor, IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly IWorkflowLogger _workflowLogger;

    public LogStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IWorkflowLogger workflowLogger)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _workflowLogger = workflowLogger;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var logStep = (LogStep) step;

        try
        {
            var logLevel =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(state, logStep.LogLevel)
                          ?? logStep.LogLevel);
            var logMessage =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(state, logStep.LogMessage)
                          ?? logStep.LogMessage);
            await _workflowLogger.LogAsync(workflow, state, logLevel, logMessage);

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }
}