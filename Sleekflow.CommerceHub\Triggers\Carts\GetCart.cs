using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts;

[TriggerGroup(ControllerNames.Carts)]
public class GetCart
    : ITrigger<
        GetCart.GetCartInput,
        GetCart.GetCartOutput>
{
    private readonly ICartService _cartService;
    private readonly IProductVariantService _productVariantService;
    private readonly IProductService _productService;

    public GetCart(
        ICartService cartService,
        IProductVariantService productVariantService,
        IProductService productService)
    {
        _cartService = cartService;
        _productVariantService = productVariantService;
        _productService = productService;
    }

    public class GetCartInput : IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [JsonConstructor]
        public GetCartInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
        }
    }

    public class GetCartOutput
    {
        [JsonProperty("cart")]
        public CartDto Cart { get; set; }

        [JsonConstructor]
        public GetCartOutput(CartDto cart)
        {
            Cart = cart;
        }
    }

    public async Task<GetCartOutput> F(GetCartInput getCartInput)
    {
        var cart = await _cartService.GetOrCreateStoreUserCartAsync(
            getCartInput.SleekflowCompanyId,
            getCartInput.StoreId,
            getCartInput.SleekflowUserProfileId);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            cart.LineItems.Select(x => x.ProductVariantId).ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productVariantIdToProductVariantDtoDict = productVariants
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var products = await _productService.GetProductsAsync(
            productVariants.Select(x => x.ProductId).Distinct().ToList(),
            cart.SleekflowCompanyId,
            cart.StoreId);
        var productIdToProductDict = products
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        return new GetCartOutput(
            new CartDto(
                cart,
                productVariantIdToProductVariantDtoDict,
                productIdToProductDict));
    }
}