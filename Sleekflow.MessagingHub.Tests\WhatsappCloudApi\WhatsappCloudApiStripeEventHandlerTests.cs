using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Payments.CloudApi;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi;

[TestFixture]
public class WhatsappCloudApiStripeEventHandlerTests
{
    [Test]
    public void IsWhatsappCloudApiManualTopUpEvent_ReturnsFalse_WhenTypeIsAutoTopUp()
    {
        // Arrange
        var metadata = new Dictionary<string, string>
        {
            {
                "type", TopUpTypes.WhatsappCloudApiAutoTopUp
            }
        };

        // Act
        var result = WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiManualTopUpEvent(metadata);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void IsWhatsappCloudApiManualTopUpEvent_ReturnsTrue_WhenTypeIsManualTopUp()
    {
        // Arrange
        var metadata = new Dictionary<string, string>
        {
            {
                "type", TopUpTypes.WhatsappCloudApiManualTopUp
            }
        };

        // Act
        var result = WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiManualTopUpEvent(metadata);

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public void IsWhatsappCloudApiAutoTopUpEvent_ReturnsTrue_WhenTypeIsAutoTopUp()
    {
        // Arrange
        var metadata = new Dictionary<string, string>
        {
            {
                "type", TopUpTypes.WhatsappCloudApiAutoTopUp
            }
        };

        // Act
        var result = WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiAutoTopUpEvent(metadata);

        // Assert
        Assert.IsTrue(result);
    }

    [Test]
    public void IsWhatsappCloudApiAutoTopUpEvent_ReturnsFalse_WhenTypeIsManualTopUp()
    {
        // Arrange
        var metadata = new Dictionary<string, string>
        {
            {
                "type", TopUpTypes.WhatsappCloudApiManualTopUp
            }
        };

        // Act
        var result = WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiAutoTopUpEvent(metadata);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void IsWhatsappCloudApiAutoTopUpEvent_ReturnsFalse_WhenThereIsNoType()
    {
        // Arrange
        var metadata = new Dictionary<string, string>();

        // Act
        var result = WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiAutoTopUpEvent(metadata);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void IsWhatsappCloudApiManualTopUpEvent_ReturnsFalse_WhenThereIsNoType()
    {
        // Arrange
        var metadata = new Dictionary<string, string>();

        // Act
        var result = WhatsappCloudApiStripeEventHandler.IsWhatsappCloudApiAutoTopUpEvent(metadata);

        // Assert
        Assert.IsFalse(result);
    }
}