namespace Sleekflow.CommerceHub.Payments.Abstractions;

public class LineItem
{
    public string Currency { get; set; }

    public decimal Amount { get; set; }

    public string Description { get; set; }

    public long Quantity { get; set; }

    public string Name { get; set; }

    public LineItem(
        string currency,
        decimal amount,
        string description,
        long quantity,
        string name)
    {
        Currency = currency;
        Amount = amount;
        Description = description;
        Quantity = quantity;
        Name = name;
    }
}