# Sleekflow.FlowHub

Sleekflow.FlowHub is an asynchronous workflow engine that allows customers to define their business flows and logic. It provides a flexible framework for creating, managing, and executing workflows with customizable steps and triggers.

## Overview

FlowHub serves as the core workflow orchestration system for the Sleekflow platform. Its primary responsibilities include:

- Defining and managing workflow definitions
- Processing workflow triggers from various sources
- Executing workflow steps in a controlled, asynchronous manner
- Communicating with other Sleekflow services
- Managing workflow state and execution context

## Architecture

### Core Components

1. **Workflows**: Define the structure and behavior of business processes
   - Workflow definitions contain metadata, steps, and trigger configurations
   - Support for versioning, draft states, and activation management
   - Workflows can be grouped and organized by type
   - Implemented in: `Workflow`, `WorkflowService`, `WorkflowDto`, `WorkflowMetadataService`

2. **Triggers**: Entry points for workflow execution
   - API-based triggers
   - Webhook triggers (`WorkflowWebhookTrigger`, `WorkflowWebhookTriggerService`)
   - Scheduled triggers (`WorkflowScheduleSettings`)
   - Event-based triggers
   - Implementation in: `CreateWorkflow`, `GetOrCreateWorkflowWebhookTrigger`, etc.

3. **Steps**: Individual actions within a workflow
   - Built-in step types for common operations (HTTP calls, logging, flow control)
   - Custom step types for specialized business logic
   - Support for parallel execution, conditional branching, and error handling
   - Step classes: `Step` (base), `CallStep<TArgs>`, `HttpGetStepArgs`, etc.

4. **State Management**: Tracking and persistence of workflow execution state
   - Each workflow execution has its own state context
   - States can be running, scheduled, completed, failed, canceled, abandoned, or blocked
   - Implemented in: `ProxyState`, `StateService`, `IStateEvaluator`, `IStateAggregator`

5. **Service Bus Integration**: Asynchronous communication between components
   - Event publishing and consumption
   - Message-based coordination
   - Implemented via: `IServiceBusManager`, `IBus`, `OnStepRequestedEvent`, etc.

### Execution Flow

```mermaid
flowchart LR
    TriggerEvent[Trigger Event] --> WorkflowState[Workflow State]
    WorkflowState --> StepExecution[Step Execution]
    StepExecution --> ExternalService[External Service]
    ExternalService --> StepComplete[Step Complete]
    StepComplete --> NextStep[Next Step]
    NextStep -.-> StepExecution
```

1. Workflow execution begins when a trigger is activated
2. The system creates a workflow state and initiates execution
3. Steps are executed sequentially based on workflow definition
4. Steps can interact with external services or perform internal operations
5. Upon step completion, the system moves to the next step or completes the workflow
6. Workflows can include branching logic, error handling, and parallel execution paths

## Key Services

### WorkflowService

The `WorkflowService` is responsible for:
- Creating and managing workflow definitions
- Storing and retrieving workflow metadata
- Handling workflow versioning and activation status
- Key methods: `CreateWorkflowAsync`, `EnableWorkflowAsync`, `UpdateWorkflowAsync`, `GetWorkflowAsync`

### WorkflowExecutionService

The `WorkflowExecutionService` orchestrates workflow execution:
- Initiating workflow execution
- Managing workflow state transitions
- Handling workflow completion, failure, and cancellation
- Communicates with `WorkflowRuntimeService` to process workflows
- Key methods: `ExecuteWorkflowAsync`, `ProcessWorkflowStepAsync`

### WorkflowRuntimeService

The `WorkflowRuntimeService` manages the runtime aspects of workflow execution:
- Starting and completing workflows
- Processing workflow state transitions
- Managing workflow execution lifecycle events
- Key methods: `ExecuteWorkflowAsync`, `CompleteWorkflowAsync`, `FailWorkflowAsync`, `CancelWorkflowAsync`

### StepExecutors

Step executors are specialized components that implement the execution logic for different step types:
- `HttpGetStepExecutor`: Makes HTTP GET requests to external services
- `LogStepExecutor`: Logs information during workflow execution
- `SubFlowStepExecutor`: Executes a sub-workflow within the main workflow
- `SwitchStepExecutor`: Implements conditional branching logic
- `ParallelStepExecutor`: Executes multiple steps in parallel
- All executors inherit from `GeneralStepExecutor<T>` and implement `IStepExecutor`

## Step Lifecycle

1. **Step Request**: The system requests a step to be executed
   - `StepRequester.RequestAsync` publishes `OnStepRequestedEvent`

2. **Step Activation**: The appropriate executor is activated for the step
   - `StepExecutorMatcher` identifies the correct executor
   - `StepExecutorActivator` activates the executor instance

3. **Step Execution**: The executor performs the step's logic
   - Concrete step executor's `OnStepActivateAsync` method runs
   - For HTTP steps, `HttpGetStepExecutor` makes the HTTP request

4. **Step Completion**: The executor signals completion and provides updated state
   - Executor calls the provided `onActivatedAsync` callback with updated state
   - Status set to `StepExecutionStatuses.Complete`

5. **Next Step Determination**: The system determines the next step to execute
   - `WorkflowStepLocator.GetNextStep` identifies the next step
   - `StepOrchestrationService` manages the progression

### Step Execution Sequence

```mermaid
sequenceDiagram
    participant Client
    participant Controller as PublicController
    participant TriggerService as WorkflowWebhookTriggerService
    participant EventHandler as FlowHubEventHandler
    participant ExecutionService as WorkflowExecutionService
    participant RuntimeService as WorkflowRuntimeService
    participant Matcher as StepExecutorMatcher
    participant Activator as StepExecutorActivator
    participant Executor as StepExecutor

    Client->>Controller: HTTP Request
    Controller->>TriggerService: Validate Webhook
    TriggerService->>Controller: Return Trigger Info
    Controller->>EventHandler: Handle Event
    EventHandler->>ExecutionService: Execute Workflow
    ExecutionService->>RuntimeService: Initialize Workflow
    RuntimeService->>ExecutionService: Publish StepRequested
    ExecutionService->>Matcher: Find Executor
    Matcher->>Activator: Activate Executor
    Activator->>Executor: OnStepActivateAsync
    Executor->>Executor: Perform Step Logic
    Executor->>RuntimeService: onActivatedAsync Callback
    RuntimeService->>ExecutionService: Determine Next Step
    alt Has Next Step
        ExecutionService->>ExecutionService: Request Next Step
    else No Next Step
        RuntimeService->>RuntimeService: CompleteWorkflow
    end
```

### Communication with External Services

For HTTP-based steps:

1. `HttpGetStepExecutor` receives the step activation
2. `IStateEvaluator` processes any expressions in the step arguments
3. `HttpClient` makes the actual HTTP request
4. Response is processed and added to the workflow state
5. `IStateAggregator` updates the workflow state with the response data

### Class Relationships

```mermaid
flowchart TD
    WorkflowService[WorkflowService] <--> WorkflowExecution[WorkflowExecutionService]
    WorkflowExecution <--> StepOrchestration[StepOrchestrationService]

    WorkflowService <--> WorkflowRepository[WorkflowRepository]
    WorkflowExecution <--> RuntimeService[WorkflowRuntimeService]
    StepOrchestration <--> ExecutorActivator[StepExecutorActivator]

    RuntimeService <--> StateService[StateService]
    ExecutorActivator <--> ExecutorMatcher[StepExecutorMatcher]

    ExecutorMatcher <--> StepExecutors[IStepExecutor Implementations]
```

## Integration with Other Services

FlowHub integrates with various other Sleekflow services:
- IntelligentHub: For AI-powered workflow steps and agent interactions
- Other backend services: For accessing business data and functionality
- External services: Through HTTP and other integration methods

Communication between services is primarily event-based, using the service bus infrastructure to ensure reliable message delivery and processing. For example:

1. FlowHub `AgentRecommendReplyStepExecutor` publishes a `GetAgentRecommendedReplyEvent`
2. IntelligentHub consumes this event via `GetAgentRecommendedReplyEventConsumer`
3. After processing, IntelligentHub publishes an `OnAgentCompleteStepActivationEvent`
4. FlowHub consumes this event to continue workflow execution

## Getting Started

For developers looking to get started with FlowHub development, including:
- Creating custom step executors
- Understanding step implementation details
- Registering new step types

Please refer to the [Getting Started Guide](./GETTING_STARTED.md).

For detailed information about working with expressions and state evaluation:
- [State Evaluation Guide](./STATE_EVALUATION.md) - Learn how to use the state evaluation system to create dynamic workflows

## Examples

See [IntelligentHub README](../Sleekflow.IntelligentHub/FaqAgents/Chats/README.md) for an example of how steps interact between FlowHub and IntelligentHub in an end-to-end communication flow.