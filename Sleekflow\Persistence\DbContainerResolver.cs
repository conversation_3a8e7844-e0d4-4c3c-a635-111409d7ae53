﻿using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence;

public interface IDbContainerResolver : IContainerResolver
{
}

public class DbContainerResolver : IDbContainerResolver
{
    private readonly CosmosClient _cosmosClient;

    public DbContainerResolver(IDbConfig dbConfig)
    {
        _cosmosClient = new CosmosClient(
            dbConfig.Endpoint,
            dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Gateway,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                GatewayModeMaxConnectionLimit = 50,
                RequestTimeout = TimeSpan.FromSeconds(60),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);

        return database.GetContainer(containerId);
    }
}