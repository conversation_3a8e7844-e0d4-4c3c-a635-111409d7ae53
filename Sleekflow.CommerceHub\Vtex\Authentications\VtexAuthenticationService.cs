﻿using System.ComponentModel.DataAnnotations;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.CommerceHub.Vtex.Helpers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CommerceHub;
using Sleekflow.Ids;

namespace Sleekflow.CommerceHub.Vtex.Authentications;

public interface IVtexAuthenticationService
{
    Task<VtexAuthentication?> GetAsync(
        string id,
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default);

    Task<List<VtexAuthentication>> GetVtexAuthenticationsAsync(
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default);

    Task<VtexAuthentication> AuthenticateAsync(
        string sleekflowCompanyId,
        string title,
        VtexCredential credential,
        CancellationToken cancellationToken = default);

    Task<VtexAuthentication> UpdateCredentialAndReauthenticateAsync(
        string id,
        string sleekflowCompanyId,
        VtexCredential credential,
        CancellationToken cancellationToken = default);

    Task<VtexAuthentication> UpdateTitleAsync(
        string id,
        string sleekflowCompanyId,
        string title,
        CancellationToken cancellationToken = default);

    Task DeleteAsync(
        string id,
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default);
}

public class VtexAuthenticationService : IVtexAuthenticationService, IScopedService
{
    private readonly IVtexAuthenticationRepository _vtexAuthenticationRepository;
    private readonly IIdService _idService;
    private readonly IVtexOrderHookRegister _vtexOrderHookRegister;
    private readonly ILogger<VtexAuthenticationService> _logger;

    public VtexAuthenticationService(
        IVtexAuthenticationRepository vtexAuthenticationRepository,
        IIdService idService,
        IVtexOrderHookRegister vtexOrderHookRegister,
        ILogger<VtexAuthenticationService> logger)
    {
        _vtexAuthenticationRepository = vtexAuthenticationRepository;
        _idService = idService;
        _vtexOrderHookRegister = vtexOrderHookRegister;
        _logger = logger;
    }

    public async Task<VtexAuthentication?> GetAsync(string id, string sleekflowCompanyId, CancellationToken cancellationToken = default)
    {
        var vtexAuthentication =
            await _vtexAuthenticationRepository.GetOrDefaultAsync(id, sleekflowCompanyId, cancellationToken);

        return vtexAuthentication is { IsDeleted: true }
            ? null
            : vtexAuthentication;
    }

    public async Task<List<VtexAuthentication>> GetVtexAuthenticationsAsync(string sleekflowCompanyId, CancellationToken cancellationToken = default)
    {
        return await _vtexAuthenticationRepository.GetObjectsAsync(
            x =>
                x.SleekflowCompanyId == sleekflowCompanyId &&
                x.IsDeleted == false,
            cancellationToken: cancellationToken);
    }

    public async Task<VtexAuthentication> AuthenticateAsync(
        string sleekflowCompanyId,
        string title,
        VtexCredential credential,
        CancellationToken cancellationToken = default)
    {
        var id = _idService.GetId(SysTypeNames.VtexAuthentication);

        // one domain could only have one connection
        var authenticationExisted = await IsAuthenticationExistedAsync(sleekflowCompanyId, credential.Domain, cancellationToken);
        if (authenticationExisted)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ("Authentication already existed.", new[] { "Domain" })
                });
        }

        // subscribe order hook
        try
        {
            await _vtexOrderHookRegister.RegisterAsync(
                credential,
                id,
                sleekflowCompanyId,
                cancellationToken);
        }
        catch (SfVtexInvalidCredentialException)
        {
            throw new SfValidationException(
            [
                new ValidationResult(
                    $"Invalid credential pairs.",
                    new[]
                    {
                        "Domain",
                        "AppKey",
                        "AppToken"
                    })
            ]);
        }

        // persistence
        var vtexAuthentication = new VtexAuthentication(
            id,
            sleekflowCompanyId,
            title,
            credential,
            false,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        return await _vtexAuthenticationRepository.CreateAndGetAsync(
            vtexAuthentication,
            sleekflowCompanyId,
            cancellationToken);
    }

    public async Task<VtexAuthentication> UpdateCredentialAndReauthenticateAsync(
        string id,
        string sleekflowCompanyId,
        VtexCredential newCredential,
        CancellationToken cancellationToken = default)
    {
        var vtexAuthentication = await GetAsync(id, sleekflowCompanyId, cancellationToken);
        if (vtexAuthentication == null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        var authenticationExisted = await IsAuthenticationExistedAsync(sleekflowCompanyId, newCredential.Domain, cancellationToken);
        if (authenticationExisted)
        {
            throw new SfValidationException(
                new List<ValidationResult>()
                {
                    new ("Authentication already existed.", new[] { "Domain" })
                });
        }

        // unsubscribe the old hook
        // normally the old credential is invalid at the time, thus ignore the exception
        try
        {
            await _vtexOrderHookRegister.RemoveRegistrationAsync(vtexAuthentication.Credential, cancellationToken);
        }
        catch
        {
            _logger.LogWarning(
                "Failed to remove old VTEX order hook registration. {SleekflowCompanyId} {VtexAuthenticationId} {VtexCredential}",
                sleekflowCompanyId,
                id,
                vtexAuthentication.Credential);
        }

        // subscribe order hook with new credential
        await _vtexOrderHookRegister.RegisterAsync(
            newCredential,
            id,
            sleekflowCompanyId,
            cancellationToken);

        // persistence
        vtexAuthentication.Credential = newCredential;
        vtexAuthentication.UpdatedAt = DateTimeOffset.UtcNow;

        return await _vtexAuthenticationRepository.UpsertAndGetAsync(
            vtexAuthentication,
            sleekflowCompanyId,
            cancellationToken: cancellationToken);
    }

    public async Task<VtexAuthentication> UpdateTitleAsync(
        string id,
        string sleekflowCompanyId,
        string title,
        CancellationToken cancellationToken = default)
    {
        var vtexAuthentication = await GetAsync(id, sleekflowCompanyId, cancellationToken);
        if (vtexAuthentication == null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        vtexAuthentication.Title = title;
        vtexAuthentication.UpdatedAt = DateTimeOffset.UtcNow;

        return await _vtexAuthenticationRepository.UpsertAndGetAsync(
            vtexAuthentication,
            sleekflowCompanyId,
            cancellationToken: cancellationToken);
    }

    public async Task DeleteAsync(string id, string sleekflowCompanyId, CancellationToken cancellationToken = default)
    {
        var vtexAuthentication = await GetAsync(id, sleekflowCompanyId, cancellationToken);
        if (vtexAuthentication == null)
        {
            return;
        }

        // unsubscribe order hook
        try
        {
            await _vtexOrderHookRegister.RemoveRegistrationAsync(
                vtexAuthentication.Credential,
                cancellationToken);
        }
        catch (SfVtexInvalidCredentialException)
        {
            // this could be caused by user deprecated their appToken in VTEX, thus the hook is already not existed
            // ignore
        }

        // set deleted
        vtexAuthentication.IsDeleted = true;
        await _vtexAuthenticationRepository.UpsertAsync(
            vtexAuthentication,
            sleekflowCompanyId,
            cancellationToken: cancellationToken);
    }

    private async Task<bool> IsAuthenticationExistedAsync(
        string sleekflowCompanyId,
        string domain,
        CancellationToken cancellationToken = default)
    {
        var vtexAuthentications =
            await _vtexAuthenticationRepository.GetObjectsAsync(
                x =>
                    x.SleekflowCompanyId == sleekflowCompanyId &&
                    x.IsDeleted == false &&
                    x.Credential.Domain == domain,
                cancellationToken: cancellationToken);

        return vtexAuthentications.Count > 0;
    }
}