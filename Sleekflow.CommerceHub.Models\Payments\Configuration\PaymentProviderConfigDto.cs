using Newtonsoft.Json;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Models.Payments.Configuration;

public class PaymentProviderConfigDto : AuditEntityDto
{
    [JsonProperty("status")]
    public string Status { get; set; }

    [JsonProperty("store_ids")]
    public List<string> StoreIds { get; set; }

    [JsonProperty("store_id_to_currency_iso_codes_dict")]
    public Dictionary<string, List<string>> StoreIdToCurrencyIsoCodesDict { get; set; }

    [JsonProperty("supported_currency_iso_codes")]
    public List<string> SupportedCurrencyIsoCodes { get; set; }

    [JsonProperty("payment_provider_external_config")]
    public PaymentProviderExternalConfigDto PaymentProviderExternalConfig { get; set; }

    [JsonConstructor]
    public PaymentProviderConfigDto(
        string id,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string status,
        List<string> storeIds,
        Dictionary<string, List<string>> storeIdToCurrencyIsoCodesDict,
        List<string> supportedCurrencyIsoCodes,
        PaymentProviderExternalConfigDto paymentProviderExternalConfig)
        : base(
            id,
            sleekflowCompanyId,
            createdBy,
            updatedBy,
            createdAt,
            updatedAt)
    {
        Status = status;
        StoreIds = storeIds;
        StoreIdToCurrencyIsoCodesDict = storeIdToCurrencyIsoCodesDict;
        SupportedCurrencyIsoCodes = supportedCurrencyIsoCodes;
        PaymentProviderExternalConfig = paymentProviderExternalConfig;
    }

    public PaymentProviderConfigDto(PaymentProviderConfig paymentProviderConfig)
        : this(
            paymentProviderConfig.Id,
            paymentProviderConfig.SleekflowCompanyId,
            paymentProviderConfig.CreatedBy,
            paymentProviderConfig.UpdatedBy,
            paymentProviderConfig.CreatedAt,
            paymentProviderConfig.UpdatedAt,
            paymentProviderConfig.Status,
            paymentProviderConfig.StoreIds,
            paymentProviderConfig.StoreIdToCurrencyIsoCodesDict,
            paymentProviderConfig.SupportedCurrencyIsoCodes,
            new PaymentProviderExternalConfigDto(paymentProviderConfig.PaymentProviderExternalConfig))
    {
    }
}