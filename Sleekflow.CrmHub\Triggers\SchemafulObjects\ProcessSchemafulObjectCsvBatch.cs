﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.Models.SchemafulObjects.Readers;
using Sleekflow.CrmHub.SchemafulObjects.FileProcessors;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class ProcessSchemafulObjectCsvBatch
    : ITrigger<ProcessSchemafulObjectCsvBatch.ProcessSchemafulObjectCsvBatchInput,
        ProcessSchemafulObjectCsvBatch.ProcessSchemafulObjectCsvBatchOutput>
{
    private readonly ISchemafulObjectFileService _schemafulObjectFileService;

    public ProcessSchemafulObjectCsvBatch(ISchemafulObjectFileService schemafulObjectFileService)
    {
        _schemafulObjectFileService = schemafulObjectFileService;
    }

    public class ProcessSchemafulObjectCsvBatchInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("blob_sas_uri")]
        public string BlobSasUri { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("batch_size")]
        public int BatchSize { get; set; }

        [Validations.ValidateObject]
        [JsonProperty("my_schemaful_object_csv_reader_state")]
        public MySchemafulObjectCsvReaderState? MySchemafulObjectCsvReaderState { get; set; }

        [JsonConstructor]
        public ProcessSchemafulObjectCsvBatchInput(
            string schemaId,
            string sleekflowCompanyId,
            string blobSasUri,
            int batchSize,
            MySchemafulObjectCsvReaderState? mySchemafulObjectCsvReaderState)
        {
            SchemaId = schemaId;
            SleekflowCompanyId = sleekflowCompanyId;
            BlobSasUri = blobSasUri;
            BatchSize = batchSize;
            MySchemafulObjectCsvReaderState = mySchemafulObjectCsvReaderState;
        }
    }

    public class ProcessSchemafulObjectCsvBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("my_custom_catalog_csv_reader_state")]
        public MySchemafulObjectCsvReaderState? MySchemafulObjectCsvReaderState { get; }

        [JsonConstructor]
        public ProcessSchemafulObjectCsvBatchOutput(
            long count,
            MySchemafulObjectCsvReaderState? mySchemafulObjectCsvReaderState)
        {
            Count = count;
            MySchemafulObjectCsvReaderState = mySchemafulObjectCsvReaderState;
        }
    }

    public async Task<ProcessSchemafulObjectCsvBatchOutput> F(
        ProcessSchemafulObjectCsvBatchInput processSchemafulObjectCsvBatchInput)
    {
        var (count, mySchemafulObjectCsvReaderState) = await _schemafulObjectFileService.ProcessSchemafulObjectCsvBatchAsync(
            processSchemafulObjectCsvBatchInput.SchemaId,
            processSchemafulObjectCsvBatchInput.SleekflowCompanyId,
            processSchemafulObjectCsvBatchInput.BlobSasUri,
            processSchemafulObjectCsvBatchInput.BatchSize,
            processSchemafulObjectCsvBatchInput.MySchemafulObjectCsvReaderState);

        return new ProcessSchemafulObjectCsvBatchOutput(count, mySchemafulObjectCsvReaderState);
    }
}