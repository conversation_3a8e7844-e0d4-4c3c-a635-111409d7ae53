using System.Text;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Sleekflow.IntelligentHub.Documents.Statistics;
using Sleekflow.IntelligentHub.Documents.Statistics.Abstractions;
using Sleekflow.IntelligentHub.Models.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;

public class ExcelStatisticsCalculator : IDocumentStatisticsCalculator
{
    public const int RowsPerPage = 50;

    private readonly IDocumentCounterService _documentCounterService;

    public ExcelStatisticsCalculator(IDocumentCounterService documentCounterService)
    {
        _documentCounterService = documentCounterService;
    }

    public DocumentStatistics CalculateDocumentStatistics(Stream stream)
    {
        using var spreadsheetDocument = SpreadsheetDocument.Open(stream, false);
        var workbookPart = spreadsheetDocument.WorkbookPart;
        var isExcelDocumentEmpty = workbookPart == null ||
                                   workbookPart.WorksheetParts.Count() == 0;
        if (isExcelDocumentEmpty)
        {
            return new DocumentStatistics(0, 0, 0, 0, 0);
        }

        var totalTokenCount = 0;
        var totalWordCount = 0;
        var totalCharacters = 0;
        var totalPages = 0;
        foreach (var worksheetPart in workbookPart.WorksheetParts)
        {
            var sheetStatistics = ProcessWorksheet(worksheetPart, workbookPart);
            totalTokenCount += sheetStatistics.Tokens;
            totalWordCount += sheetStatistics.Words;
            totalCharacters += sheetStatistics.Characters;
            totalPages += sheetStatistics.Pages;
        }

        return new DocumentStatistics(
            totalTokenCount,
            totalWordCount,
            totalCharacters,
            totalPages,
            (int) stream.Length);
    }

    private sealed record ExcelSheetStatistics
    {
        public int Tokens;
        public int Words;
        public int Characters;
        public int Pages;

        public ExcelSheetStatistics(int tokens, int words, int characters, int pages)
        {
            Tokens = tokens;
            Words = words;
            Characters = characters;
            Pages = pages;
        }
    };

    private ExcelSheetStatistics ProcessWorksheet(WorksheetPart worksheetPart, WorkbookPart workbookPart)
    {
        var sheetData = worksheetPart.Worksheet.Elements<SheetData>().First();

        var workSheetStatistics = new ExcelSheetStatistics(0, 0, 0, 0);

        // To prevent a massive Excel worksheet from using up a lot of memory, we chunk them into 100 rows at a time
        foreach (var chunk in sheetData.Elements<Row>().Chunk(100))
        {
            var csvStringBuilder = new StringBuilder();
            foreach (var row in chunk)
            {
                var cellValues = row.Elements<Cell>().Select(c => GetCellValue(c, workbookPart)).ToArray();
                csvStringBuilder.Append(string.Join(",", cellValues) + '\n');
            }

            var fullCsvChunk = csvStringBuilder.ToString();
            var totalTokensInChunk = _documentCounterService.CountTokens(fullCsvChunk);
            var totalWordsInChunk = _documentCounterService.CountWords(fullCsvChunk);
            var totalCharactersInChunk = _documentCounterService.CountCharacters(fullCsvChunk);
            workSheetStatistics.Tokens += totalTokensInChunk;
            workSheetStatistics.Words += totalWordsInChunk;
            workSheetStatistics.Characters += totalCharactersInChunk;
        }

        var numOfRows = sheetData.Elements<Row>().Count();
        workSheetStatistics.Pages = (numOfRows / RowsPerPage) + (numOfRows % RowsPerPage == 0 ? 0 : 1);

        return workSheetStatistics;
    }

    public static string GetCellValue(Cell cell, WorkbookPart workbookPart)
    {
        // If 2 cells contain the same data, Excel may store the data elsewhere and only store a reference in the cells
        // to save space. Hence, we need to fetch the shared string if needed.
        var stringTablePart = workbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();
        var value = cell.InnerText;

        if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString && stringTablePart != null)
        {
            // shared string
            return stringTablePart.SharedStringTable.ElementAt(int.Parse(value)).InnerText;
        }

        return value;
    }
}