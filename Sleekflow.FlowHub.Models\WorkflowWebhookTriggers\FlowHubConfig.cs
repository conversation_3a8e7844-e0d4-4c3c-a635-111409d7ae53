using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.WorkflowWebhookTriggers;

[ContainerId(ContainerNames.WorkflowWebhookTrigger)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class WorkflowWebhookTrigger : AuditEntity, IHasETag, IHasSleekflowCompanyId
{
    public const string PropertyNameWorkflowId = "workflow_id";
    public const string PropertyNameValidationToken = "validation_token";
    public const string PropertyNameObjectIdExpression = "object_id_expression";

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty(PropertyNameWorkflowId)]
    public string WorkflowId { get; set; }

    [JsonProperty(PropertyNameValidationToken)]
    public string ValidationToken { get; set; }

    [JsonProperty(PropertyNameObjectIdExpression)]
    public string ObjectIdExpression { get; set; }

    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [JsonConstructor]
    public WorkflowWebhookTrigger(
        string? eTag,
        string workflowId,
        string validationToken,
        string objectIdExpression,
        string id,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string sleekflowCompanyId,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy,
        string objectType)
        : base(id, SysTypeNames.WorkflowWebhookTrigger, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        ETag = eTag;
        WorkflowId = workflowId;
        ValidationToken = validationToken;
        ObjectIdExpression = objectIdExpression;
        ObjectType = objectType;
    }
}