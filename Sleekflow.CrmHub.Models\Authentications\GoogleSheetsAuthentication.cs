﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Authentications;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("google_sheets_authentication")]
public class GoogleSheetsAuthentication : Entity
{
    [JsonConstructor]
    public GoogleSheetsAuthentication(
        string id,
        string sleekflowCompanyId,
        string accessToken,
        string refreshToken,
        int expiresIn,
        string tokenType,
        string scope,
        DateTimeOffset issuedAt,
        dynamic rawRes,
        dynamic? refreshRes)
        : base(id, "Authentication")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        ExpiresIn = expiresIn;
        TokenType = tokenType;
        Scope = scope;
        IssuedAt = issuedAt;
        RawRes = rawRes;
        RefreshRes = refreshRes;
    }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonProperty("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("scope")]
    public string Scope { get; set; }

    [JsonProperty("issued_at")]
    public DateTimeOffset IssuedAt { get; set; }

    [JsonProperty("raw_res")]
    public dynamic RawRes { get; set; }

    [JsonProperty("refresh_res")]
    public dynamic? RefreshRes { get; set; }
}