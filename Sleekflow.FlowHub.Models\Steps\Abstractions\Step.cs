using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Steps.Abstractions;

[JsonConverter(typeof(StepConverter))]
public abstract class Step : IHasNextStepId
{
    [Required]
    [JsonProperty("id")]
    public string Id { get; set; }

    [Required]
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("assign")]
    public Assign? Assign { get; set; }

    [JsonProperty("next_step_id")]
    public string? NextStepId { get; set; }

    [JsonIgnore]
    [JsonProperty("category")]
    public abstract string Category { get; }

    [JsonConstructor]
    protected Step(string id, string name, Assign? assign, string? nextStepId)
    {
        Id = id;
        Name = name;
        Assign = assign;
        NextStepId = nextStepId;
    }
}

public interface IHasNextStepId
{
    public string? NextStepId { get; set; }
}