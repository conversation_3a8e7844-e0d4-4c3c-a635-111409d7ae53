using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Steps;

public interface IStepRequester
{
    Task RequestAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId);
}

public class StepRequester
    : IStepRequester, IScopedService
{
    private readonly IServiceBusManager _serviceBusManager;

    public StepRequester(
        IServiceBusManager serviceBusManager)
    {
        _serviceBusManager = serviceBusManager;
    }

    public virtual async Task RequestAsync(
        string stateId,
        string stepId,
        Stack<StackEntry> stackEntries,
        string? workerInstanceId)
    {
        await _serviceBusManager.PublishAsync<OnStepRequestedEvent>(
            new OnStepRequestedEvent(
                stateId,
                stepId,
                stackEntries,
                workerInstanceId));
    }
}