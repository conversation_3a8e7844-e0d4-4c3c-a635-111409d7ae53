﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Caches;

public interface ICacheConfig
{
    string CachePrefix { get; }

    string RedisConnStr { get; }
}

public class CacheConfig : IConfig, ICacheConfig
{
    public string CachePrefix { get; private set; }

    public string RedisConnStr { get; private set; }

    public CacheConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        CachePrefix =
            Environment.GetEnvironmentVariable("CACHE_PREFIX", target)
            ?? throw new SfMissingEnvironmentVariableException("CACHE_PREFIX");
        RedisConnStr =
            Environment.GetEnvironmentVariable("REDIS_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("REDIS_CONN_STR");
    }
}