using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.StepExecutors.Calls;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnAgentAddLabelCompleteEventConsumerDefinition
    : ConsumerDefinition<OnAgentAddLabelCompleteEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnAgentAddLabelCompleteEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 128;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnAgentAddLabelCompleteEventConsumer(
    ICoreCommander coreCommander,
    IServiceBusManager serviceBusManager,
    IStateService stateService,
    IStepExecutionService stepExecutionService,
    ILogger<OnAgentAddLabelCompleteEventConsumer> logger)
    : IConsumer<OnAgentAddLabelCompleteEvent>, IScopedService
{
    public async Task Consume(ConsumeContext<OnAgentAddLabelCompleteEvent> context)
    {
        var message = context.Message;
        var aggregateContext = JsonConvert.DeserializeObject<Dictionary<string, object>>(message.AggregateStateContext);
        try
        {
            var state = await stateService.GetProxyStateAsync(message.ProxyStateId);

            await coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactLabelRelationships",
                new UpdateContactLabelRelationshipsStepExecutor.UpdateContactLabelRelationshipsInput(
                    message.ProxyStateId,
                    state.Identity,
                    state.Identity.ObjectId,
                    [$"{aggregateContext["hashtag"]}"],
                    null,
                    null));

            logger.LogInformation("Agent Labels added for StateId: {StateId}", message.ProxyStateId);

            await serviceBusManager.PublishAsync(
                new OnAgentCompleteStepActivationEvent(
                    message.AggregateStepId,
                    message.ProxyStateId,
                    message.StackEntries,
                   message.AggregateStateContext));

            logger.LogInformation("OnAgentCompleteStepActivationEvent published for StepId: {StepId}, StateId: {StateId}", message.AggregateStepId, message.ProxyStateId);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing OnAgentAddLabelComplete for StepId: {StepId}, StateId: {StateId}. This will likely be retried or moved to an error queue.", message.AggregateStepId, message.ProxyStateId);
        }
    }
}