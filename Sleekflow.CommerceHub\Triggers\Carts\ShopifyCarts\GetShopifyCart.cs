using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts.ShopifyCarts;

[TriggerGroup(ControllerNames.ShopifyCarts)]
public class GetShopifyCart
    : ITrigger<
        GetShopifyCart.GetShopifyCartInput,
        GetShopifyCart.GetShopifyCartOutput>
{
    private readonly IShopifyCartService _shopifyCartService;
    private readonly IProductService _productService;
    private readonly IProductVariantService _productVariantService;

    public GetShopifyCart(
        IShopifyCartService shopifyCartService,
        IProductService productService,
        IProductVariantService productVariantService)
    {
        _shopifyCartService = shopifyCartService;
        _productService = productService;
        _productVariantService = productVariantService;
    }

    public class GetShopifyCartInput : IHasSleekflowCompanyId, IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty("store_id")]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty("cart_status")]
        public string CartStatus { get; set; }

        [Required]
        [JsonProperty("shopify_cart_token")]
        public string ShopifyCartToken { get; set; }

        [JsonConstructor]
        public GetShopifyCartInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string storeId,
            string cartStatus,
            string shopifyCartToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            StoreId = storeId;
            CartStatus = cartStatus;
            ShopifyCartToken = shopifyCartToken;
        }
    }

    public class GetShopifyCartOutput
    {
        [JsonProperty("shopify_cart")]
        public ShopifyCartDto? ShopifyCart { get; set; }

        [JsonConstructor]
        public GetShopifyCartOutput(
            ShopifyCartDto? shopifyCart)
        {
            ShopifyCart = shopifyCart;
        }
    }

    public async Task<GetShopifyCartOutput> F(GetShopifyCartInput getShopifyCartInput)
    {
        var shopifyCart = await _shopifyCartService.GetShopifyCartAsync(
            getShopifyCartInput.SleekflowCompanyId,
            getShopifyCartInput.SleekflowUserProfileId,
            getShopifyCartInput.StoreId,
            getShopifyCartInput.CartStatus,
            getShopifyCartInput.ShopifyCartToken);

        if (shopifyCart is null)
        {
            return new GetShopifyCartOutput(null);
        }

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            shopifyCart.LineItems.Select(x => x.ProductVariantId).ToList(),
            shopifyCart.SleekflowCompanyId,
            shopifyCart.StoreId);
        var productVariantIdToProductVariantDtoDict = productVariants
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        var products = await _productService.GetProductsAsync(
            productVariants.Select(x => x.ProductId).Distinct().ToList(),
            shopifyCart.SleekflowCompanyId,
            shopifyCart.StoreId);
        var productIdToProductDict = products
            .GroupBy(pv => pv.Id)
            .ToDictionary(pv => pv.Key, pv => pv.First());

        return new GetShopifyCartOutput(
            new ShopifyCartDto(
                shopifyCart,
                productVariantIdToProductVariantDtoDict,
                productIdToProductDict));
    }
}