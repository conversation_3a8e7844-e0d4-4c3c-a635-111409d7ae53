using Newtonsoft.Json.Linq;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Steps.Common;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Tests.TestClients;
using Sleekflow.FlowHub.Triggers.WorkflowAgentConfigMappings;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class WorkflowsCoreIntegrationTests
{
    [Test]
    public async Task DisableWorkflowTest()
    {
        var mockCompanyId = nameof(DisableWorkflowTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DisableWorkflow
        var disableWorkflowOutputOutput = await WorkflowTestClient.DisableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(disableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(disableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            disableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Draft));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task CreateWorkflowWebhookTriggerTest()
    {
        var mockCompanyId = nameof(CreateWorkflowWebhookTriggerTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflowWebhookTrigger
        var createWorkflowWebhookTriggerOutputOutput =
            await WorkflowWebhookTriggerTestClient.CreateWorkflowWebhookTriggerAsync(
                mockCompanyId,
                createWorkflowOutputOutput.Data.Workflow.WorkflowId,
                "{{ }}",
                "Contact",
                mockStaffId);

        Assert.That(createWorkflowWebhookTriggerOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowWebhookTriggerOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetOrCreateWorkflowWebhookTriggerTest()
    {
        var mockCompanyId = nameof(GetOrCreateWorkflowWebhookTriggerTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetOrCreateWorkflowWebhookTriggerTest
        var createWorkflowWebhookTriggerOutputOutput =
            await WorkflowWebhookTriggerTestClient.GetOrCreateWorkflowWebhookTriggerAsync(
                mockCompanyId,
                createWorkflowOutputOutput.Data.Workflow.WorkflowId,
                mockStaffId);

        Assert.That(createWorkflowWebhookTriggerOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowWebhookTriggerOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetOrCreateWorkflowWebhookTriggerTest
        var getWorkflowWebhookTriggerOutputOutput =
            await WorkflowWebhookTriggerTestClient.GetOrCreateWorkflowWebhookTriggerAsync(
                mockCompanyId,
                createWorkflowOutputOutput.Data.Workflow.WorkflowId,
                mockStaffId);

        Assert.That(getWorkflowWebhookTriggerOutputOutput, Is.Not.Null);
        Assert.That(getWorkflowWebhookTriggerOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            getWorkflowWebhookTriggerOutputOutput.Data.ValidationToken,
            Is.EqualTo(createWorkflowWebhookTriggerOutputOutput.Data.ValidationToken));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task DuplicateWorkflowTest()
    {
        var mockCompanyId = nameof(DuplicateWorkflowTest) + Random.Shared.NextInt64(0, 1000000);
        var mockStaffId = "mock-staff-id";

        // /FlowHubConfigs/EnrollFlowHub
        var enrollFlowHubOutput = await FlowHubConfigTestClient.EnrollAsync(
            mockCompanyId,
            mockStaffId);

        Assert.That(enrollFlowHubOutput, Is.Not.Null);
        Assert.That(enrollFlowHubOutput!.HttpStatusCode, Is.EqualTo(200));

        // FlowHubConfigs/UpdateFlowHubConfig
        var updateFlowHubConfigOutput1 = await FlowHubConfigTestClient.UpdateFlowHubConfigAsync(
            mockCompanyId,
            mockStaffId,
            usageLimit: new UsageLimit(
                2,
                2,
                200,
                10000));

        Assert.That(updateFlowHubConfigOutput1, Is.Not.Null);
        Assert.That(updateFlowHubConfigOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateFlowHubConfigOutput1!.Data.FlowHubConfig.UsageLimit, Is.Not.Null);
        Assert.That(
            updateFlowHubConfigOutput1!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfWorkflows,
            Is.EqualTo(2));
        Assert.That(
            updateFlowHubConfigOutput1!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfActiveWorkflows,
            Is.EqualTo(2));
        Assert.That(
            updateFlowHubConfigOutput1!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfNodesPerWorkflow,
            Is.EqualTo(200));
        Assert.That(
            updateFlowHubConfigOutput1!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfMonthlyWorkflowExecutions,
            Is.EqualTo(10000));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DuplicateWorkflow
        var duplicateWorkflowOutputOutput1 = await WorkflowTestClient.DuplicateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(duplicateWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(duplicateWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DuplicateWorkflow
        var duplicateWorkflowOutputOutput2 = await WorkflowTestClient.DuplicateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(duplicateWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(500));
        Assert.That(duplicateWorkflowOutputOutput2!.ErrorCode, Is.EqualTo(9009));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutputDuplicate = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            duplicateWorkflowOutputOutput1.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutputDuplicate, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutputDuplicate!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task EnableWorkflowTest()
    {
        var mockCompanyId = nameof(EnableWorkflowTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task DeleteWorkflowTest()
    {
        var mockCompanyId = nameof(DeleteWorkflowTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task DeleteWorkflowsTest()
    {
        var mockCompanyId = nameof(DeleteWorkflowsTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput1 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput2 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 2",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput3 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 3",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput3, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput3!.HttpStatusCode, Is.EqualTo(200));

        var createdWorkflow1 = createWorkflowOutputOutput1.Data.Workflow;
        var createdWorkflow2 = createWorkflowOutputOutput2.Data.Workflow;
        var createdWorkflow3 = createWorkflowOutputOutput3.Data.Workflow;

        // Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createdWorkflow1.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/ScheduleDeleteWorkflows
        var scheduleDeleteWorkflowsOutputOutput = await WorkflowTestClient.ScheduleDeleteWorkflowsAsync(
            mockCompanyId,
            new List<string>()
            {
                createdWorkflow2.WorkflowId, createdWorkflow3.WorkflowId
            },
            mockStaffId);

        Assert.That(scheduleDeleteWorkflowsOutputOutput, Is.Not.Null);
        Assert.That(scheduleDeleteWorkflowsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowTest()
    {
        var mockCompanyId = nameof(GetWorkflowTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/UpdateWorkflow
        var updateWorkflowOutputOutput = await WorkflowTestClient.UpdateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            createWorkflowOutputOutput.Data.Workflow.Name,
            mockStaffId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowGroupId,
            createWorkflowOutputOutput.Data.Workflow.Triggers,
            createWorkflowOutputOutput.Data.Workflow.WorkflowEnrollmentSettings,
            createWorkflowOutputOutput.Data.Workflow.WorkflowScheduleSettings,
            new List<Step>()
            {
                new SimpleStep("step-1", "Step 1", new Assign(), null)
            });

        Assert.That(updateWorkflowOutputOutput, Is.Not.Null);
        Assert.That(updateWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            updateWorkflowOutputOutput.Data.Workflow.Steps[0].GetValue("id")!.Value<string>(),
            Is.EqualTo("step-1"));

        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // /Workflows/GetWorkflow
        var getWorkflowOutputOutput = await WorkflowTestClient.GetWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId);

        Assert.That(getWorkflowOutputOutput, Is.Not.Null);
        Assert.That(getWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowOutputOutput!.Data.ActiveWorkflow, Is.Not.Null);
        Assert.That(
            getWorkflowOutputOutput!.Data.ActiveWorkflow!.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));
        Assert.That(
            getWorkflowOutputOutput!.Data.VersionedWorkflows.Count,
            Is.EqualTo(2));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowsTest()
    {
        var mockCompanyId = nameof(GetWorkflowsTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput1 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput2 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 2",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetWorkflows
        var getWorkflowsOutputOutput2 = await WorkflowTestClient.GetWorkflowsAsync(mockCompanyId);

        Assert.That(getWorkflowsOutputOutput2, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput2!.Data.Workflows.Count, Is.GreaterThanOrEqualTo(2));

        // /Workflows/GetWorkflows
        var getWorkflowsOutputOutput3 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowExecutionStatisticsFilters: new WorkflowExecutionStatisticsFilters(
                DateTimeOffset.UtcNow.AddDays(-20),
                DateTimeOffset.UtcNow,
                null));

        Assert.That(getWorkflowsOutputOutput3, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput3!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput3!.Data.Workflows.Count, Is.GreaterThanOrEqualTo(2));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput1 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput2 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput2.Data.Workflow.WorkflowId,
            mockStaffId,
            null);

        Assert.That(deleteWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetLatestWorkflowTest()
    {
        var mockCompanyId = nameof(GetLatestWorkflowTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Get latest workflow",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetLatestWorkflow
        var getLatestWorkflowOutputOutput1 = await WorkflowTestClient.GetLatestWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId);

        Assert.That(getLatestWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(getLatestWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/UpdateWorkflow
        var updateWorkflowOutputOutput = await WorkflowTestClient.UpdateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            "Get latest updated workflow",
            mockStaffId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowGroupId,
            createWorkflowOutputOutput.Data.Workflow.Triggers,
            createWorkflowOutputOutput.Data.Workflow.WorkflowEnrollmentSettings,
            createWorkflowOutputOutput.Data.Workflow.WorkflowScheduleSettings,
            new List<Step>()
            {
                new SimpleStep("step-1", "Step 1", new Assign(), null)
            });

        Assert.That(updateWorkflowOutputOutput, Is.Not.Null);
        Assert.That(updateWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetLatestWorkflow
        var getLatestWorkflowOutputOutput2 = await WorkflowTestClient.GetLatestWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId);

        Assert.That(getLatestWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(getLatestWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            getLatestWorkflowOutputOutput2.Data.LatestWorkflow.WorkflowId,
            Is.EqualTo(
                getLatestWorkflowOutputOutput1.Data.LatestWorkflow.WorkflowId));
        Assert.That(
            getLatestWorkflowOutputOutput2.Data.LatestWorkflow.CreatedAt,
            Is.GreaterThan(getLatestWorkflowOutputOutput1.Data.LatestWorkflow.CreatedAt));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetActiveWorkflowTest()
    {
        var mockCompanyId = nameof(GetActiveWorkflowTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Get active workflow",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetActiveWorkflow
        var getActiveWorkflowOutputOutput1 = await WorkflowTestClient.GetActiveWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId);

        Assert.That(getActiveWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(getActiveWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getActiveWorkflowOutputOutput1!.Data.ActiveWorkflow, Is.Null);

        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetActiveWorkflow
        var getActiveWorkflowOutputOutput2 = await WorkflowTestClient.GetActiveWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId);

        Assert.That(getActiveWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(getActiveWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getActiveWorkflowOutputOutput2!.Data.ActiveWorkflow, Is.Not.Null);

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowsTest_GivenSearchKeyword()
    {
        var mockCompanyId = $"{nameof(GetWorkflowsTest)}-{Guid.NewGuid()}";
        var mockStaffId = $"mock-{Guid.NewGuid()}";
        var originalKeyword = "search";
        var updatedKeyword = "devs";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput1 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Search workflow",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput2 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My workflow 2",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/GetWorkflows
        var getWorkflowsOutputOutput1 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            searchName: originalKeyword,
            workflowFilters: new WorkflowFilters(
                null,
                mockStaffId,
                null,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput1, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput1!.Data.Workflows.Count, Is.EqualTo(1));

        // /Workflows/UpdateWorkflow
        var updateWorkflowOutputOutput = await WorkflowTestClient.UpdateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowId,
            "DEVS-3703",
            mockStaffId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowGroupId,
            createWorkflowOutputOutput1.Data.Workflow.Triggers,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowEnrollmentSettings,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowScheduleSettings,
            new List<Step>()
            {
                new LogStep("log", "Log", null, null, "Hello", "Information")
            });

        Assert.That(updateWorkflowOutputOutput, Is.Not.Null);
        Assert.That(updateWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // GetWorkflows with old search keyword
        getWorkflowsOutputOutput1 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            searchName: originalKeyword,
            workflowFilters: new WorkflowFilters(
                null,
                mockStaffId,
                null,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput1, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput1!.Data.Workflows.Count, Is.EqualTo(0));

        // GetWorkflows with new search keyword
        getWorkflowsOutputOutput1 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            searchName: updatedKeyword,
            workflowFilters: new WorkflowFilters(
                null,
                mockStaffId,
                null,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput1, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput1!.Data.Workflows.Count, Is.EqualTo(1));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput1 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput2 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput2.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowsTest_GivenActivationStatusFilter()
    {
        var mockCompanyId = $"{nameof(GetWorkflowsTest_GivenActivationStatusFilter)}-{Guid.NewGuid()}";
        var mockStaffId = $"mock-{Guid.NewGuid()}";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput1 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Filter workflow",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput2 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Filter Workflow 2",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // GetWorkflows with activation status filter - Active
        var getWorkflowsOutputOutput1 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                WorkflowActivationStatuses.Active,
                mockStaffId,
                null,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput1, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput1!.Data.Workflows.Count, Is.EqualTo(1));

        foreach (var workflow in getWorkflowsOutputOutput1.Data.Workflows)
        {
            Assert.That(
                workflow.ActivationStatus,
                Is.EqualTo(WorkflowActivationStatuses.Active));
        }

        // GetWorkflows with activation status filter - Draft
        var getWorkflowsOutputOutput2 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                WorkflowActivationStatuses.Draft,
                mockStaffId,
                null,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput2, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput2!.Data.Workflows.Count, Is.EqualTo(1));

        foreach (var workflow in getWorkflowsOutputOutput2.Data.Workflows)
        {
            Assert.That(
                workflow.ActivationStatus,
                Is.EqualTo(WorkflowActivationStatuses.Draft));
        }

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput1 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput2 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput2.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowsTest_GivenCreatedByStaffIdFilter()
    {
        var mockCompanyId = $"{nameof(GetWorkflowsTest_GivenCreatedByStaffIdFilter)}-{Guid.NewGuid()}";

        // /Workflows/CreateWorkflow
        var mockStaffId1 = $"mock-{Guid.NewGuid()}";

        var createWorkflowOutputOutput1 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Filter workflow",
            mockStaffId1);

        Assert.That(createWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var mockStaffId2 = $"mock-{Guid.NewGuid()}";

        var createWorkflowOutputOutput2 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Filter Workflow 2",
            mockStaffId2);

        Assert.That(createWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // GetWorkflows with filter - CreatedBy Staff Id
        var getWorkflowsOutputOutput1 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                null,
                mockStaffId1,
                null,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput1, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput1!.Data.Workflows.Count, Is.EqualTo(1));

        foreach (var workflow in getWorkflowsOutputOutput1.Data.Workflows)
        {
            Assert.That(
                workflow.CreatedBy?.SleekflowStaffId,
                Is.EqualTo(mockStaffId1));
        }

        // GetWorkflows with filter - CreatedBy Staff Id
        var getWorkflowsOutputOutput2 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                null,
                "fakeId",
                null,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput2, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput2.Data.Workflows.Count, Is.EqualTo(0));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput1 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowId,
            mockStaffId1);

        Assert.That(deleteWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput2 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput2.Data.Workflow.WorkflowId,
            mockStaffId2);

        Assert.That(deleteWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
    }


    [Test]
    public async Task GetWorkflowsTest_GivenUpdatedByStaffIdFilter()
    {
        var mockCompanyId = $"{nameof(GetWorkflowsTest_GivenUpdatedByStaffIdFilter)}-{Guid.NewGuid()}";

        // /Workflows/CreateWorkflow
        var mockStaffId1 = $"mock-{Guid.NewGuid()}";

        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Filter workflow",
            mockStaffId1);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/UpdateWorkflow
        var mockStaffId2 = $"mock-{Guid.NewGuid()}";

        var updateWorkflowOutputOutput2 = await WorkflowTestClient.UpdateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            createWorkflowOutputOutput.Data.Workflow.Name,
            mockStaffId2,
            createWorkflowOutputOutput.Data.Workflow.WorkflowGroupId,
            createWorkflowOutputOutput.Data.Workflow.Triggers,
            createWorkflowOutputOutput.Data.Workflow.WorkflowEnrollmentSettings,
            createWorkflowOutputOutput.Data.Workflow.WorkflowScheduleSettings,
            new List<Step>()
            {
                new LogStep("step-1", "Log", null, null, "Hello", "Information")
            });

        Assert.That(updateWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(updateWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // GetWorkflows with filter - UpdatedBy Staff Id
        var getWorkflowsOutputOutput = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                null,
                null,
                mockStaffId2,
                null,
                null,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput!.Data.Workflows.Count, Is.EqualTo(1));

        foreach (var workflow in getWorkflowsOutputOutput.Data.Workflows)
        {
            Assert.That(
                workflow.UpdatedBy?.SleekflowStaffId,
                Is.EqualTo(mockStaffId2));
        }

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId1);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowsTest_GivenUpdatedAtDateTimeRange()
    {
        var mockCompanyId = $"{nameof(GetWorkflowsTest_GivenUpdatedAtDateTimeRange)}-{Guid.NewGuid()}";

        // /Workflows/CreateWorkflow
        var mockStaffId1 = $"mock-{Guid.NewGuid()}";

        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Filter workflow",
            mockStaffId1);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // GetWorkflows with filter - UpdatedAt DateTime Range
        var updatedFromDateTime = createWorkflowOutputOutput.Data.Workflow.CreatedAt.AddHours(-1);
        var updatedToDateTime = createWorkflowOutputOutput.Data.Workflow.CreatedAt.AddMinutes(10);

        var getWorkflowsOutputOutput = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                null,
                mockStaffId1,
                null,
                updatedFromDateTime,
                updatedToDateTime,
                null,
                null));

        Assert.That(getWorkflowsOutputOutput, Is.Not.Null);
        Assert.That(getWorkflowsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutputOutput!.Data.Workflows.Count, Is.EqualTo(1));

        foreach (var workflow in getWorkflowsOutputOutput.Data.Workflows)
        {
            Assert.That(
                workflow.UpdatedAt,
                Is.GreaterThan(updatedFromDateTime));
            Assert.That(
                workflow.UpdatedAt,
                Is.LessThan(updatedToDateTime));
        }

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput1 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId1);

        Assert.That(deleteWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
    }


    [Test]
    public async Task SwapWorkflowsTest()
    {
        var mockCompanyId = nameof(SwapWorkflowsTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/UpdateWorkflow
        var updateWorkflowOutputOutput = await WorkflowTestClient.UpdateWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            createWorkflowOutputOutput.Data.Workflow.Name,
            mockStaffId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowGroupId,
            createWorkflowOutputOutput.Data.Workflow.Triggers,
            createWorkflowOutputOutput.Data.Workflow.WorkflowEnrollmentSettings,
            createWorkflowOutputOutput.Data.Workflow.WorkflowScheduleSettings,
            new List<Step>()
            {
                new SimpleStep("step-1", "Step 1", new Assign(), null)
            });

        Assert.That(updateWorkflowOutputOutput, Is.Not.Null);
        Assert.That(updateWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // /Workflows/SwapWorkflows
        var swapWorkflowsOutputOutput = await WorkflowTestClient.SwapWorkflowsAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            updateWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(swapWorkflowsOutputOutput, Is.Not.Null);
        Assert.That(swapWorkflowsOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            swapWorkflowsOutputOutput!.Data.SourceWorkflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Draft));
        Assert.That(
            swapWorkflowsOutputOutput!.Data.TargetWorkflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task CheatsheetTest()
    {
        var mockContactId = "contactId";
        var mockCompanyId = nameof(CheatsheetTest);
        var mockStaffId = "mock-staff-id";

        var webhookUrl = "https://webhook.site/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12";

        // /Events/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Workflow 1",
            mockStaffId,
            workflowTriggers: new WorkflowTriggers(
                null,
                new WorkflowTrigger("{{ event_body.created_contact.name == 'Leo' }}"),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            steps: new List<Step>
            {
                new CallStep<HttpGetStepArgs>(
                    "step1",
                    "step 1",
                    null,
                    null,
                    "http.get",
                    new HttpGetStepArgs(
                        webhookUrl,
                        new Dictionary<string, string?>
                        {
                            {
                                "Step", "step1"
                            }
                        })),
                new SubFlowStep(
                    "step2",
                    "step 2",
                    null,
                    null,
                    new List<Step>
                    {
                        new CallStep<HttpGetStepArgs>(
                            "step2.1",
                            "step 2.1",
                            null,
                            null,
                            "http.get",
                            new HttpGetStepArgs(
                                webhookUrl,
                                new Dictionary<string, string?>
                                {
                                    {
                                        "Step", "step2.1"
                                    }
                                })),
                        new CallStep<SleepStepArgs>(
                            "step2.2",
                            "step 2.2",
                            null,
                            null,
                            "sys.sleep",
                            new SleepStepArgs("5")),
                        new CallStep<HttpGetStepArgs>(
                            "step2.3",
                            "step 2.3",
                            null,
                            null,
                            "http.get",
                            new HttpGetStepArgs(
                                webhookUrl,
                                new Dictionary<string, string?>
                                {
                                    {
                                        "Step", "step2.3"
                                    }
                                })),
                    }),
                new TryCatchStep(
                    "step3",
                    "step 3",
                    null,
                    null,
                    new TryCatchStepTry(
                        new CallStep<HttpGetStepArgs>(
                            "step3.1",
                            "step 3.1",
                            null,
                            null,
                            "http.get",
                            new HttpGetStepArgs(
                                webhookUrl,
                                new Dictionary<string, string?>
                                {
                                    {
                                        "Step", "step3.1"
                                    }
                                }))),
                    new TryCatchStepCatch(
                        "e",
                        new CallStep<HttpGetStepArgs>(
                            "step3.2",
                            "step 3.2",
                            null,
                            null,
                            "http.get",
                            new HttpGetStepArgs(
                                webhookUrl,
                                new Dictionary<string, string?>
                                {
                                    {
                                        "Step", "step3.2"
                                    }
                                })))),
                new SwitchStep(
                    "step4",
                    "step 4",
                    null,
                    null,
                    new List<SwitchStepCase>
                    {
                        new SwitchStepCase(
                            "step4.1 id",
                            "step4.1 name",
                            "false",
                            new List<ConditionCriterion>(),
                            "step4.1"),
                        new SwitchStepCase(
                            "step4.2 id",
                            "step4.2 name",
                            "true",
                            new List<ConditionCriterion>(),
                            "step4.2"),
                    }),
                new CallStep<HttpGetStepArgs>(
                    "step4.1",
                    "step 4.1",
                    null,
                    null,
                    "http.get",
                    new HttpGetStepArgs(
                        webhookUrl,
                        new Dictionary<string, string?>
                        {
                            {
                                "Step", "step4.1"
                            }
                        })),
                new CallStep<HttpGetStepArgs>(
                    "step4.2",
                    "step 4.2",
                    null,
                    null,
                    "http.get",
                    new HttpGetStepArgs(
                        webhookUrl,
                        new Dictionary<string, string?>
                        {
                            {
                                "Step", "step4.2"
                            }
                        })),
                new TryCatchStep(
                    "step5",
                    "step 5",
                    null,
                    null,
                    new TryCatchStepTry(
                        new ThrowStep(
                            "step5.1",
                            "step 5.1",
                            null,
                            null,
                            50,
                            "Unable to process")),
                    new TryCatchStepCatch(
                        "e",
                        new CallStep<HttpGetStepArgs>(
                            "step5.2",
                            "step 5.2",
                            null,
                            null,
                            "http.get",
                            new HttpGetStepArgs(
                                webhookUrl,
                                new Dictionary<string, string?>
                                {
                                    {
                                        "Step", "step5.2"
                                    }
                                })))),
                new ParallelStep(
                    "step6",
                    "step 6",
                    null,
                    null,
                    new List<ParallelStepBranch>
                    {
                        new ParallelStepBranch(
                            new CallStep<HttpGetStepArgs>(
                                "step6.1",
                                "step 6.1",
                                new Assign
                                {
                                    {
                                        "concurrency_test", "english"
                                    },
                                },
                                null,
                                "http.get",
                                new HttpGetStepArgs(
                                    webhookUrl,
                                    new Dictionary<string, string?>
                                    {
                                        {
                                            "Step", "step6.1"
                                        }
                                    }))),
                        new ParallelStepBranch(
                            new SubFlowStep(
                                "step6.2",
                                "Step 6.2",
                                null,
                                null,
                                new List<Step>
                                {
                                    new CallStep<HttpGetStepArgs>(
                                        "step6.2.1",
                                        "step 6.2.1",
                                        new Assign
                                        {
                                            {
                                                "concurrency_test", "chinese"
                                            },
                                        },
                                        null,
                                        "http.get",
                                        new HttpGetStepArgs(
                                            webhookUrl,
                                            new Dictionary<string, string?>
                                            {
                                                {
                                                    "Step", "step6.2.1"
                                                }
                                            })),
                                    new CallStep<HttpGetStepArgs>(
                                        "step6.2.2",
                                        "step 6.2.2",
                                        null,
                                        null,
                                        "http.get",
                                        new HttpGetStepArgs(
                                            webhookUrl,
                                            new Dictionary<string, string?>
                                            {
                                                {
                                                    "Step", "step6.2.2"
                                                }
                                            })),
                                    new CallStep<HttpGetStepArgs>(
                                        "step6.2.3",
                                        "step 6.2.3",
                                        null,
                                        null,
                                        "http.get",
                                        new HttpGetStepArgs(
                                            webhookUrl,
                                            new Dictionary<string, string?>
                                            {
                                                {
                                                    "Step", "step6.2.3"
                                                }
                                            })),
                                })),
                        new ParallelStepBranch(
                            new CallStep<HttpGetStepArgs>(
                                "step6.3",
                                "step 6.3",
                                new Assign
                                {
                                    {
                                        "concurrency_test", "japanese"
                                    },
                                },
                                null,
                                "http.get",
                                new HttpGetStepArgs(
                                    webhookUrl,
                                    new Dictionary<string, string?>
                                    {
                                        {
                                            "Step", "step6.3"
                                        }
                                    }))),
                        new ParallelStepBranch(
                            new CallStep<HttpGetStepArgs>(
                                "step6.4",
                                "step 6.4",
                                new Assign
                                {
                                    {
                                        "concurrency_test", "korean"
                                    },
                                },
                                null,
                                "http.get",
                                new HttpGetStepArgs(
                                    webhookUrl,
                                    new Dictionary<string, string?>
                                    {
                                        {
                                            "Step", "step6.4"
                                        }
                                    }))),
                    }),
                new CallStep<WaitForEventStepArgs>(
                    "step7",
                    "step 7",
                    null,
                    null,
                    "sys.wait-for-event",
                    new WaitForEventStepArgs(
                        "OnContactCreated",
                        "{{ event_body.created_contact.name == 'Leo' }}",
                        "360")),
                new CallStep<HttpGetStepArgs>(
                    "step8",
                    "step 8",
                    null,
                    null,
                    "http.get",
                    new HttpGetStepArgs(
                        webhookUrl,
                        new Dictionary<string, string?>
                        {
                            {
                                "Step", "step8"
                            },
                            {
                                "Leo", "{{ trigger_event_body.created_contact.name + \" Choi\" }}"
                            }
                        })),
                new LogStep(
                    "step9",
                    "step 9",
                    null,
                    null,
                    "{{ trigger_event_body.created_contact.name + \" Choi\" }}",
                    "Information"),
                new CallStep<HttpGetStepArgs>(
                    "step10",
                    "Step 10",
                    new Assign
                    {
                        {
                            "Currencies", "{{ (sys_var_dict.step10 | json.deserialize) }}"
                        },
                        {
                            "Usd", "{{ (sys_var_dict.step10 | json.deserialize)[\"USD\"] }}"
                        },
                        {
                            "Symbol", "{{ (sys_var_dict.step10 | json.deserialize)[\"USD\"][\"symbol\"] }}"
                        }
                    },
                    null,
                    "http.get",
                    new HttpGetStepArgs(
                        "https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json",
                        new Dictionary<string, string?>())),
                new LogStep(
                    "step11",
                    "step 11",
                    null,
                    null,
                    "{{ usr_var_dict.Symbol }}",
                    "Information"),
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task CountWorkflowsTest()
    {
        var mockCompanyId = $"{nameof(CountWorkflowsTest)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput1 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Count Workflow 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));


        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput2 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Count Workflow 2",
            mockStaffId,
            WorkflowType.InternalForZapierIntegration);

        Assert.That(createWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // //Workflows/CountWorkflows
        var countWorkflowsOutputOutput1 = await WorkflowTestClient.CountWorkflowsAsync(
            mockCompanyId,
            workflowType: null);

        Assert.That(countWorkflowsOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(countWorkflowsOutputOutput1, Is.Not.Null);
        Assert.That(countWorkflowsOutputOutput1.Data.NumOfWorkflows, Is.EqualTo(2));
        Assert.That(countWorkflowsOutputOutput1.Data.NumOfActiveWorkflows, Is.EqualTo(1));

        // //Workflows/CountWorkflows
        var countWorkflowsOutputOutput2 = await WorkflowTestClient.CountWorkflowsAsync(
            mockCompanyId,
            WorkflowType.Normal);

        Assert.That(countWorkflowsOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(countWorkflowsOutputOutput2, Is.Not.Null);
        Assert.That(countWorkflowsOutputOutput2.Data.NumOfWorkflows, Is.EqualTo(1));
        Assert.That(countWorkflowsOutputOutput2.Data.NumOfActiveWorkflows, Is.EqualTo(1));

        // //Workflows/CountWorkflows
        var countWorkflowsOutputOutput3 = await WorkflowTestClient.CountWorkflowsAsync(
            mockCompanyId,
            WorkflowType.InternalForZapierIntegration);

        Assert.That(countWorkflowsOutputOutput3!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(countWorkflowsOutputOutput3, Is.Not.Null);
        Assert.That(countWorkflowsOutputOutput3.Data.NumOfWorkflows, Is.EqualTo(1));
        Assert.That(countWorkflowsOutputOutput3.Data.NumOfActiveWorkflows, Is.EqualTo(0));

        // /Workflows/ScheduleDeleteWorkflows
        var scheduleDeleteWorkflowsOutputOutput = await WorkflowTestClient.ScheduleDeleteWorkflowsAsync(
            mockCompanyId,
            new List<string>()
            {
                createWorkflowOutputOutput1.Data.Workflow.WorkflowId
            },
            mockStaffId);

        Assert.That(scheduleDeleteWorkflowsOutputOutput, Is.Not.Null);
        Assert.That(scheduleDeleteWorkflowsOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput2.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // Wait for ScheduleDeleteWorkflows
        await Task.Delay(TimeSpan.FromSeconds(5));

        // /Workflows/CountWorkflows
        var countWorkflowsOutputOutput4 = await WorkflowTestClient.CountWorkflowsAsync(
            mockCompanyId,
            workflowType: null);

        Assert.That(countWorkflowsOutputOutput4!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(countWorkflowsOutputOutput4, Is.Not.Null);
        Assert.That(countWorkflowsOutputOutput4.Data.NumOfWorkflows, Is.EqualTo(0));
        Assert.That(countWorkflowsOutputOutput4.Data.NumOfActiveWorkflows, Is.EqualTo(0));
    }

    [Test]
    public async Task CreateWorkflowWithUsageLimitTest()
    {
        var mockCompanyId = $"{nameof(CreateWorkflowWithUsageLimitTest)}-{Guid.NewGuid()}";
        var mockStaffId = "mock-staff-id";

        // /FlowHubConfigs/EnrollFlowHub
        var enrollFlowHubOutput = await FlowHubConfigTestClient.EnrollAsync(
            mockCompanyId,
            mockStaffId);

        Assert.That(enrollFlowHubOutput, Is.Not.Null);
        Assert.That(enrollFlowHubOutput!.HttpStatusCode, Is.EqualTo(200));

        // /FlowHubConfigs/UpdateFlowHub
        var updateFlowHubConfigOutput = await FlowHubConfigTestClient.UpdateFlowHubConfigAsync(
            mockCompanyId,
            mockStaffId,
            usageLimit: new UsageLimit(
                maximumNumOfWorkflows: 2,
                maximumNumOfActiveWorkflows: 1,
                maximumNumOfNodesPerWorkflow: 100,
                maximumNumOfMonthlyWorkflowExecutions: 10000));

        Assert.That(updateFlowHubConfigOutput, Is.Not.Null);
        Assert.That(updateFlowHubConfigOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(updateFlowHubConfigOutput.Data.FlowHubConfig.UsageLimit, Is.Not.Null);
        Assert.That(
            updateFlowHubConfigOutput!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfWorkflows,
            Is.EqualTo(2));
        Assert.That(
            updateFlowHubConfigOutput!.Data.FlowHubConfig.UsageLimit!.MaximumNumOfActiveWorkflows,
            Is.EqualTo(1));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput1 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "CreateWorkflowWithUsageLimit 1",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput2 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "CreateWorkflowWithUsageLimit 2",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput3 = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "CreateWorkflowWithUsageLimit 3",
            mockStaffId);

        Assert.That(createWorkflowOutputOutput3, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput3!.ErrorCode, Is.EqualTo(9009));

        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput1 = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput1!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));


        // /Workflows/EnableWorkflow
        var enableWorkflowOutputOutput2 = await WorkflowTestClient.EnableWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput2.Data.Workflow.WorkflowVersionedId,
            mockStaffId);

        Assert.That(enableWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput2!.ErrorCode, Is.EqualTo(9009));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput1 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput1.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput1, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput1!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput2 = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput2.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput2, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput2!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetWorkflowsAfterDeleteWorkflowsTest()
    {
        // Arrange
        var companyId = $"mock-company-{Guid.NewGuid()}";
        var staffId = $"mock-staff-{Guid.NewGuid()}";

        HashSet<WorkflowDto> createdWorkflows = new ();

        for (var i = 0; i < 3; i++)
        {
            var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
                companyId,
                $"{nameof(GetWorkflowsAfterDeleteWorkflowsTest)}-{i}",
                staffId);

            createdWorkflows.Add(createWorkflowOutputOutput!.Data.Workflow);
        }

        var workflowCountOutputBeforeDeletion = await WorkflowTestClient.CountWorkflowsAsync(
            companyId,
            WorkflowType.Normal);

        var deletedWorkflows = createdWorkflows
            .Take(1)
            .ToHashSet();

        createdWorkflows = createdWorkflows
            .Except(deletedWorkflows)
            .ToHashSet();

        for (var i = 0; i < deletedWorkflows.Count; ++i)
        {
            await WorkflowTestClient.DeleteWorkflowAsync(
                companyId,
                deletedWorkflows.ElementAt(i).WorkflowId,
                staffId);
        }

        var workflowCountOutputAfterDeletion = await WorkflowTestClient.CountWorkflowsAsync(
            companyId,
            WorkflowType.Normal);

        // Act
        var getWorkflowsOutput = await WorkflowTestClient.GetWorkflowsAsync(
            companyId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(getWorkflowsOutput, Is.Not.Null);
            Assert.That(getWorkflowsOutput!.HttpStatusCode, Is.EqualTo(200));
            Assert.That(getWorkflowsOutput.Data.Workflows, Has.Count.EqualTo(2));
            Assert.That(
                getWorkflowsOutput.Data.Workflows
                    .Select(x => x.WorkflowVersionedId)
                    .ToList(),
                Is.EquivalentTo(
                    createdWorkflows
                        .Select(x => x.WorkflowVersionedId)
                        .ToList()));

            Assert.That(workflowCountOutputBeforeDeletion!.Data.NumOfWorkflows, Is.EqualTo(3));
            Assert.That(workflowCountOutputBeforeDeletion.Data.NumOfActiveWorkflows, Is.EqualTo(0));
            Assert.That(workflowCountOutputAfterDeletion!.Data.NumOfWorkflows, Is.EqualTo(2));
            Assert.That(workflowCountOutputAfterDeletion.Data.NumOfActiveWorkflows, Is.EqualTo(0));
        });
    }

    [Test]
    public async Task GetVersionedWorkflowAfterDeleteWorkflowsTest()
    {
        // Arrange
        var companyId = $"mock-company-{Guid.NewGuid()}";
        var staffId = $"mock-staff-{Guid.NewGuid()}";

        var createWorkflowOutput = await WorkflowTestClient.CreateWorkflowAsync(
            companyId,
            $"{nameof(GetVersionedWorkflowAfterDeleteWorkflowsTest)}",
            staffId);

        await WorkflowTestClient.DeleteWorkflowAsync(
            companyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId,
            staffId);

        // Act
        var getVersionedWorkflowOutput = await WorkflowTestClient.GetVersionedWorkflowAsync(
            companyId,
            createWorkflowOutput.Data.Workflow.WorkflowVersionedId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(getVersionedWorkflowOutput, Is.Not.Null);
            Assert.That(getVersionedWorkflowOutput!.HttpStatusCode, Is.EqualTo(200));
            Assert.That(getVersionedWorkflowOutput.Data.Workflow, Is.Not.Null);
            Assert.That(
                getVersionedWorkflowOutput.Data.Workflow!.ActivationStatus,
                Is.EqualTo(WorkflowActivationStatuses.Deleted));
        });
    }

    [Test]
    public async Task EnableDeletedWorkflowTest()
    {
        // Arrange
        var companyId = $"mock-company-{Guid.NewGuid()}";
        var staffId = $"mock-staff-{Guid.NewGuid()}";

        var createWorkflowOutput = await WorkflowTestClient.CreateWorkflowAsync(
            companyId,
            "Workflow",
            staffId);

        await WorkflowTestClient.DeleteWorkflowAsync(
            companyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId,
            staffId);

        // Act
        var enableWorkflowOutput = await WorkflowTestClient.EnableWorkflowAsync(
            companyId,
            createWorkflowOutput.Data.Workflow.WorkflowVersionedId,
            staffId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(enableWorkflowOutput!.HttpStatusCode, Is.EqualTo(500));
            Assert.That(enableWorkflowOutput!.ErrorCode, Is.EqualTo(ErrorCodeConstant.SfWorkflowDeletedException));
        });
    }

    [Test]
    public async Task DisableDeletedWorkflowTest()
    {
        // Arrange
        var companyId = $"mock-company-{Guid.NewGuid()}";
        var staffId = $"mock-staff-{Guid.NewGuid()}";

        var createWorkflowOutput = await WorkflowTestClient.CreateWorkflowAsync(
            companyId,
            "Workflow",
            staffId);

        await WorkflowTestClient.DeleteWorkflowAsync(
            companyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId,
            staffId);

        // Act
        var enableWorkflowOutput = await WorkflowTestClient.DisableWorkflowAsync(
            companyId,
            createWorkflowOutput.Data.Workflow.WorkflowVersionedId,
            staffId);

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(enableWorkflowOutput!.HttpStatusCode, Is.EqualTo(500));
            Assert.That(enableWorkflowOutput!.ErrorCode, Is.EqualTo(ErrorCodeConstant.SfWorkflowDeletedException));
        });
    }

    [Test]
    public async Task UpdateToInvalidWorkflowAndEnableIt()
    {
        // Arrange
        var companyId = $"mock-company-{Guid.NewGuid()}";
        var staffId = $"mock-staff-{Guid.NewGuid()}";

        var createWorkflowOutput = await WorkflowTestClient.CreateWorkflowAsync(
            companyId,
            "Workflow",
            staffId);

        // Act - 1 - Update to invalid workflow
        var updateWorkflowOutput = await WorkflowTestClient.SaveWorkflowDraftAsync(
            companyId,
            createWorkflowOutput!.Data.Workflow.WorkflowId,
            createWorkflowOutput.Data.Workflow.Name,
            staffId,
            null,
            createWorkflowOutput.Data.Workflow.Triggers,
            createWorkflowOutput.Data.Workflow.WorkflowEnrollmentSettings,
            createWorkflowOutput.Data.Workflow.WorkflowScheduleSettings,
            [
                new CallStep<SendMessageStepArgs>(
                    "send-message-step-id",
                    "send-message",
                    null,
                    null,
                    SendMessageStepArgs.CallName,
                    new SendMessageStepArgs(
                        "channelId",
                        new FromTo(
                            "fromPhoneNumber",
                            null,
                            "toContactId",
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        "messageType",
                        string.Empty,
                        deliveryTypeExpr: null,
                        staffIdExpr: null))
            ]);

        // Act 1 - Assert
        Assert.Multiple(() =>
        {
            Assert.That(updateWorkflowOutput, Is.Not.Null);
            Assert.That(updateWorkflowOutput!.HttpStatusCode, Is.EqualTo(200));
            Assert.That(updateWorkflowOutput.Success, Is.True);
            Assert.That(updateWorkflowOutput.Data, Is.Not.Null);
            Assert.That(
                updateWorkflowOutput.Data.Workflow.ActivationStatus,
                Is.EqualTo(WorkflowActivationStatuses.Draft));
        });

        // Act 2 - Enable the workflow
        var enableWorkflowDraftOutput = await WorkflowTestClient.EnableWorkflowDraftAsync<object?>(
            companyId,
            updateWorkflowOutput.Data.Workflow.WorkflowVersionedId,
            staffId);

        // Act 2 - Assert
        Assert.Multiple(() =>
        {
            Assert.That(enableWorkflowDraftOutput, Is.Not.Null);
            Assert.That(enableWorkflowDraftOutput!.HttpStatusCode, Is.EqualTo(400));
            Assert.That(enableWorkflowDraftOutput.Success, Is.False);
            Assert.That(enableWorkflowDraftOutput.Data, Is.Not.Null);
            Assert.That(enableWorkflowDraftOutput.Data, Is.TypeOf<JArray>());
            Assert.That(enableWorkflowDraftOutput.Data, Has.Count.EqualTo(1));
        });

        // Cleanup
        var deleteWorkflowOutputOutputDuplicate = await WorkflowTestClient.DeleteWorkflowAsync(
            companyId,
            createWorkflowOutput.Data.Workflow.WorkflowId,
            staffId);
    }

    [Test]
    public async Task GetWorkflows_GivenAgentConfigId()
    {
        var mockCompanyId = nameof(GetWorkflows_GivenAgentConfigId) + Random.Shared.NextInt64(0, 1000000);
        var mockStaffId = "mock-staff-id";
        var agentConfigId1 = $"agent-config-id-1-{Guid.NewGuid()}";
        var agentConfigId2 = $"agent-config-id-2-{Guid.NewGuid()}";

        // Create Workflow 1
        var createWorkflow1Output = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Workflow 1 for AgentConfig Test",
            mockStaffId);
        Assert.That(createWorkflow1Output, Is.Not.Null);
        Assert.That(createWorkflow1Output!.HttpStatusCode, Is.EqualTo(200));
        var workflow1Id = createWorkflow1Output.Data.Workflow.WorkflowId;

        // Create Workflow 2
        var createWorkflow2Output = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "Workflow 2 for AgentConfig Test",
            mockStaffId);
        Assert.That(createWorkflow2Output, Is.Not.Null);
        Assert.That(createWorkflow2Output!.HttpStatusCode, Is.EqualTo(200));
        var workflow2Id = createWorkflow2Output.Data.Workflow.WorkflowId;

        // Map Workflow 1 to AgentConfigId1
        var createMappingInput = new CreateWorkflowAgentConfigMappings.CreateWorkflowAgentConfigMappingsInput
        {
            SleekflowCompanyId = mockCompanyId,
            AgentConfigId = agentConfigId1,
            WorkflowIds = new List<string>
            {
                workflow1Id
            },
            SleekflowStaffId = mockStaffId
        };
        var createMappingScenarioResult = await Application.Host.Scenario(s =>
        {
            s.WithRequestHeader("X-Sleekflow-Record", "true");
            s.Post.Json(createMappingInput).ToUrl("/WorkflowAgentConfigMappings/CreateWorkflowAgentConfigMappings");
        });
        Assert.That(createMappingScenarioResult.Context.Response.StatusCode, Is.EqualTo(200));

        // Get workflows with AgentConfigId1
        var getWorkflowsOutput1 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                activationStatus: null,
                createdBySleekflowStaffId: null,
                updatedBySleekflowStaffId: null,
                updatedFromDateTime: null,
                updatedToDateTime: null,
                workflowType: null,
                dependencyWorkflowId: null,
                agentConfigId: agentConfigId1));

        Assert.That(getWorkflowsOutput1, Is.Not.Null);
        Assert.That(getWorkflowsOutput1!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutput1.Data.Workflows.Count, Is.EqualTo(1));
        Assert.That(getWorkflowsOutput1.Data.Workflows[0].WorkflowId, Is.EqualTo(workflow1Id));

        // Get workflows with AgentConfigId2 (should be empty)
        var getWorkflowsOutput2 = await WorkflowTestClient.GetWorkflowsAsync(
            mockCompanyId,
            workflowFilters: new WorkflowFilters(
                activationStatus: null,
                createdBySleekflowStaffId: null,
                updatedBySleekflowStaffId: null,
                updatedFromDateTime: null,
                updatedToDateTime: null,
                workflowType: null,
                dependencyWorkflowId: null,
                agentConfigId: agentConfigId2));

        Assert.That(getWorkflowsOutput2, Is.Not.Null);
        Assert.That(getWorkflowsOutput2!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getWorkflowsOutput2.Data.Workflows.Count, Is.EqualTo(0));

        // Cleanup: Delete mappings
        var deleteMappingInput =
            new DeleteWorkflowAgentConfigMappingsByAgentConfigId.DeleteWorkflowAgentConfigMappingsByAgentConfigIdInput
            {
                SleekflowCompanyId = mockCompanyId, AgentConfigId = agentConfigId1, SleekflowStaffId = mockStaffId
            };
        var deleteMappingScenarioResult = await Application.Host.Scenario(s =>
        {
            s.WithRequestHeader("X-Sleekflow-Record", "true");
            s.Post.Json(deleteMappingInput).ToUrl(
                "/WorkflowAgentConfigMappings/DeleteWorkflowAgentConfigMappingsByAgentConfigId");
        });
        Assert.That(deleteMappingScenarioResult.Context.Response.StatusCode, Is.EqualTo(200));

        // Cleanup: Delete workflows
        await WorkflowTestClient.DeleteWorkflowAsync(mockCompanyId, workflow1Id, mockStaffId);
        await WorkflowTestClient.DeleteWorkflowAsync(mockCompanyId, workflow2Id, mockStaffId);
    }
}