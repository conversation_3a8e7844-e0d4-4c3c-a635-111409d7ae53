﻿using Microsoft.Extensions.Logging;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Workers.Subscriptions;

public interface IGoogleSheetsSubscriptionRepository : IRepository<GoogleSheetsSubscription>
{
}

public class GoogleSheetsSubscriptionRepository
    : BaseRepository<GoogleSheetsSubscription>,
        IGoogleSheetsSubscriptionRepository
{
    public GoogleSheetsSubscriptionRepository(
        ILogger<BaseRepository<GoogleSheetsSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}