using MimeKit;
using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.EmailHubDb;

namespace Sleekflow.EmailHub.Models.Communications;

[Resolver(typeof(IEmailHubDbResolver))]
[DatabaseId("emailhubdb")]
[ContainerId("email")]
public class Email : Entity, IHasSleekflowCompanyId
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("sender")]
    public EmailContact Sender { get; set; }

    [JsonProperty("to")]
    public List<EmailContact> To { get; set; }

    [JsonProperty("from")]
    public List<EmailContact> From { get; set; }

    [JsonProperty("cc")]
    public List<EmailContact> Cc { get; set; }

    [JsonProperty("bcc")]
    public List<EmailContact> Bcc { get; set; }

    [JsonProperty("subject")]
    public string Subject { get; set; }

    [JsonProperty("reply_to")]
    public List<EmailContact> ReplyTo { get; set; }

    [JsonProperty("html_body")]
    public string? HtmlBody { get; set; }

    [JsonProperty("text_body")]
    public string? TextBody { get; set; }

    [JsonProperty("email_attachments")]
    public List<EmailAttachment> EmailAttachments { get; set; }

    [JsonProperty("email_metadata", TypeNameHandling = TypeNameHandling.Objects)]
    public EmailMetadata EmailMetadata { get; set; }

    [JsonProperty("is_from_sleekflow")]
    public bool IsFromSleekflow { get; set; }

    [JsonConstructor]
    public Email(
        string id,
        string sleekflowCompanyId,
        EmailContact sender,
        List<EmailContact> from,
        List<EmailContact> to,
        List<EmailContact> cc,
        List<EmailContact> bcc,
        List<EmailContact> replyTo,
        string subject,
        string? htmlBody,
        string? textBody,
        bool isFromSleekflow,
        List<EmailAttachment> emailAttachments,
        EmailMetadata emailMetadata)
        : base(id, "Email")
    {
        Bcc = bcc;
        Cc = cc;
        EmailAttachments = emailAttachments;
        EmailMetadata = emailMetadata;
        From = from;
        IsFromSleekflow = isFromSleekflow;
        HtmlBody = htmlBody;
        ReplyTo = replyTo;
        Sender = sender;
        SleekflowCompanyId = sleekflowCompanyId;
        Subject = subject;
        TextBody = textBody;
        To = to;
    }
}