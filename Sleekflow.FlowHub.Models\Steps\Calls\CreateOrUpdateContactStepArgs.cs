using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateOrUpdateContactStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-or-update-contact";

    [JsonProperty("contact_id__expr")]
    public string? ContactIdExpr { get; set; }

    [JsonProperty("phone_number__expr")]
    public string? PhoneNumberExpr { get; set; }

    [JsonProperty("whatsapp_user_display_name__expr")]
    public string? WhatsappUserDisplayNameExpr { get; set; }

    [JsonProperty("facebook_page_id__expr")]
    public string? FacebookPageId { get; set; }

    [JsonProperty("facebook_id__expr")]
    public string? FacebookIdExpr { get; set; }

    [JsonProperty("facebook_name__expr")]
    public string? FacebookNameExpr { get; set; }

    [JsonProperty("instagram_page_id__expr")]
    public string? InstagramPageId { get; set; }

    [JsonProperty("instagram_id__expr")]
    public string? InstagramIdExpr { get; set; }

    [JsonProperty("instagram_username__expr")]
    public string? InstagramUsernameExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public CreateOrUpdateContactStepArgs(
        string? contactIdExpr,
        string? phoneNumberExpr,
        string? whatsappUserDisplayNameExpr,
        string? facebookPageId,
        string? facebookIdExpr,
        string? facebookNameExpr,
        string? instagramPageId,
        string? instagramIdExpr,
        string? instagramUsernameExpr)
    {
        ContactIdExpr = contactIdExpr;
        PhoneNumberExpr = phoneNumberExpr;
        WhatsappUserDisplayNameExpr = whatsappUserDisplayNameExpr;
        FacebookPageId = facebookPageId;
        FacebookIdExpr = facebookIdExpr;
        FacebookNameExpr = facebookNameExpr;
        InstagramPageId = instagramPageId;
        InstagramIdExpr = instagramIdExpr;
        InstagramUsernameExpr = instagramUsernameExpr;
    }
}