using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ICreateOrUpdateContactStepExecutor : IStepExecutor
{
}

public class CreateOrUpdateContactStepExecutor
    : GeneralStepExecutor<CallStep<CreateOrUpdateContactStepArgs>>,
        ICreateOrUpdateContactStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public CreateOrUpdateContactStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "CreateOrUpdateContact",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class CreateOrUpdateContactInput
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        public string? ContactId { get; set; }

        [JsonProperty("phone_number")]
        public string? PhoneNumber { get; set; }

        [JsonProperty("whatsapp_user_display_name")]
        public string? WhatsappUserDisplayName { get; set; }

        [JsonProperty("facebook_page_id")]
        public string? FacebookPageId { get; set; }

        [JsonProperty("facebook_id")]
        public string? FacebookId { get; set; }

        [JsonProperty("facebook_name")]
        public string? FacebookName { get; set; }

        [JsonProperty("instagram_page_id")]
        public string? InstagramPageId { get; set; }

        [JsonProperty("instagram_id")]
        public string? InstagramId { get; set; }

        [JsonProperty("instagram_username")]
        public string? InstagramUsername { get; set; }

        [JsonConstructor]
        public CreateOrUpdateContactInput(
            string sleekflowCompanyId,
            string? contactId,
            string? phoneNumber,
            string? whatsappUserDisplayName,
            string? facebookPageId,
            string? facebookId,
            string? facebookName,
            string? instagramPageId,
            string? instagramId,
            string? instagramUsername)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            PhoneNumber = phoneNumber;
            WhatsappUserDisplayName = whatsappUserDisplayName;
            FacebookPageId = facebookPageId;
            FacebookId = facebookId;
            FacebookName = facebookName;
            InstagramPageId = instagramPageId;
            InstagramId = instagramId;
            InstagramUsername = instagramUsername;
        }
    }

    private async Task<CreateOrUpdateContactInput> GetArgs(
        CallStep<CreateOrUpdateContactStepArgs> callStep,
        ProxyState state)
    {
        var contactId =
            callStep.Args.ContactIdExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                            ?? callStep.Args.ContactIdExpr)
                : null;

        var phoneNumber =
            callStep.Args.PhoneNumberExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.PhoneNumberExpr)
                            ?? callStep.Args.PhoneNumberExpr)
                : null;

        var whatsappUserDisplayName = callStep.Args.WhatsappUserDisplayNameExpr != null
            ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.WhatsappUserDisplayNameExpr)
                        ?? callStep.Args.WhatsappUserDisplayNameExpr)
            : null;

        var facebookPageId =
            callStep.Args.FacebookPageId != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.FacebookPageId)
                            ?? callStep.Args.FacebookPageId)
                : null;

        var facebookId =
            callStep.Args.FacebookIdExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.FacebookIdExpr)
                            ?? callStep.Args.FacebookIdExpr)
                : null;

        var facebookName =
            callStep.Args.FacebookNameExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.FacebookNameExpr)
                            ?? callStep.Args.FacebookNameExpr)
                : null;

        var instagramPageId =
            callStep.Args.InstagramPageId != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.InstagramPageId)
                            ?? callStep.Args.InstagramPageId)
                : null;

        var instagramId =
            callStep.Args.InstagramIdExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.InstagramIdExpr)
                            ?? callStep.Args.InstagramIdExpr)
                : null;

        var instagramUsername =
            callStep.Args.InstagramUsernameExpr != null
                ? (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.InstagramUsernameExpr)
                            ?? callStep.Args.InstagramUsernameExpr)
                : null;

        return new CreateOrUpdateContactInput(
            state.Identity.SleekflowCompanyId,
            contactId,
            phoneNumber,
            whatsappUserDisplayName,
            facebookPageId,
            facebookId,
            facebookName,
            instagramPageId,
            instagramId,
            instagramUsername);
    }
}