using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderTypeFieldsV2 : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderTypeFieldsV2(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderTypeFieldsV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_connection_id")]
        [Required]
        public string ProviderConnectionId { get; set; }

        [Json<PERSON>roperty("typed_ids")]
        [ValidateArray]
        public List<TypedId>? TypedIds { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public GetProviderTypeFieldsV2Input(
            string sleekflowCompanyId,
            string providerConnectionId,
            List<TypedId>? typedIds,
            string entityTypeName,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderConnectionId = providerConnectionId;
            TypedIds = typedIds;
            EntityTypeName = entityTypeName;
            ProviderName = providerName;
        }
    }

    public class GetProviderTypeFieldsV2Output
    {
        [JsonProperty("updatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> UpdatableFields { get; set; }

        [JsonProperty("creatable_fields")]
        public List<GetTypeFieldsOutputFieldDto> CreatableFields { get; set; }

        [JsonProperty("viewable_fields")]
        public List<GetTypeFieldsOutputFieldDto> ViewableFields { get; set; }

        [JsonConstructor]
        public GetProviderTypeFieldsV2Output(
            List<GetTypeFieldsOutputFieldDto> updatableFields,
            List<GetTypeFieldsOutputFieldDto> creatableFields,
            List<GetTypeFieldsOutputFieldDto> viewableFields)
        {
            UpdatableFields = updatableFields;
            CreatableFields = creatableFields;
            ViewableFields = viewableFields;
        }
    }

    public async Task<GetProviderTypeFieldsV2Output> F(
        GetProviderTypeFieldsV2Input getProviderTypeFieldsV2Input)
    {
        var providerService = _providerSelector.GetProviderService(getProviderTypeFieldsV2Input.ProviderName);

        var getTypeFieldsOutput = await providerService.GetTypeFieldsV2Async(
            getProviderTypeFieldsV2Input.SleekflowCompanyId,
            getProviderTypeFieldsV2Input.ProviderConnectionId,
            getProviderTypeFieldsV2Input.TypedIds,
            getProviderTypeFieldsV2Input.EntityTypeName);
        if (getTypeFieldsOutput == null)
        {
            throw new SfUserFriendlyException("The integrator does not respond fields");
        }

        return new GetProviderTypeFieldsV2Output(
            getTypeFieldsOutput.UpdatableFields,
            getTypeFieldsOutput.CreatableFields,
            getTypeFieldsOutput.ViewableFields);
    }
}