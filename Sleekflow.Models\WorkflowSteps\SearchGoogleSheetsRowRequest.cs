﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.WorkflowSteps;

public class SearchGoogleSheetsRowRequest
{
    [JsonProperty("aggregate_step_id")]
    [Required]
    public string AggregateStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    [Required]
    public string ProxyStateId { get; set; }

    [JsonProperty("stack_entries")]
    [Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("connection_id")]
    [Required]
    public string ConnectionId { get; set; }

    [JsonProperty("spreadsheet_id")]
    [Required]
    public string SpreadsheetId { get; set; }

    [JsonProperty("worksheet_id")]
    [Required]
    public string WorksheetId { get; set; }

    [JsonProperty("header_row_id")]
    [Required]
    public string HeaderRowId { get; set; }

    [JsonProperty("conditions")]
    [Required]
    public List<SearchObjectCondition> Conditions { get; set; }

    [JsonConstructor]
    public SearchGoogleSheetsRowRequest(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string connectionId,
        string spreadsheetId,
        string worksheetId,
        string headerRowId,
        List<SearchObjectCondition> conditions)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        SleekflowCompanyId = sleekflowCompanyId;
        ConnectionId = connectionId;
        SpreadsheetId = spreadsheetId;
        WorksheetId = worksheetId;
        HeaderRowId = headerRowId;
        Conditions = conditions;
    }
}