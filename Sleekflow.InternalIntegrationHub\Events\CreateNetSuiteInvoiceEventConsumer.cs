using MassTransit;
using Sleekflow.InternalIntegrationHub.NetSuite;
using Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;
using Sleekflow.Models.NetSuite;

namespace Sleekflow.InternalIntegrationHub.Events;

public class CreateNetSuiteInvoiceEventConsumerDefinition : ConsumerDefinition<CreateNetSuiteInvoiceEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<CreateNetSuiteInvoiceEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class CreateNetSuiteInvoiceEventConsumer
    : IConsumer<CreateNetSuiteInvoiceEvent>
{
    private readonly INetSuiteInvoiceService _netSuiteInvoiceService;

    public CreateNetSuiteInvoiceEventConsumer(
        INetSuiteInvoiceService netSuiteInvoiceService)
    {
        _netSuiteInvoiceService = netSuiteInvoiceService;
    }

    public async Task Consume(ConsumeContext<CreateNetSuiteInvoiceEvent> context)
    {
        var req = context.Message;
        var createInvoiceInput = new CreateInvoice.CreateInvoiceInput(
            req.ExternalId,
            req.SleekflowCompanyId,
            req.SubscriptionFee,
            req.SubscriptionDescription,
            req.OneTimeSetupFee,
            req.OneTimeSetupDescription,
            req.WhatsappCreditAmount,
            req.WhatsappCreditDescription,
            req.SubscriptionStartDate,
            req.SubscriptionEndDate,
            req.PaymentTerm,
            req.Currency,
            req.PaidAmount,
            req.CreatedDate);
        await _netSuiteInvoiceService.CreateInvoiceAsync(createInvoiceInput);
    }
}