﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.KnowledgeBases;

[TriggerGroup(ControllerNames.KnowledgeBases)]
public class GetKnowledgeBaseEntry
    : ITrigger<
        GetKnowledgeBaseEntry.GetKnowledgeBaseEntryInput,
        GetKnowledgeBaseEntry.GetKnowledgeBaseEntryOutput>
{
    private readonly IKnowledgeBaseEntryService _knowledgeBaseEntryService;

    public GetKnowledgeBaseEntry(IKnowledgeBaseEntryService knowledgeBaseEntryService)
    {
        _knowledgeBaseEntryService = knowledgeBaseEntryService;
    }

    public class GetKnowledgeBaseEntryInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("knowledge_base_entry_id")]
        public string KnowledgeBaseEntryId { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseEntryInput(string sleekflowCompanyId, string knowledgeBaseEntryId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            KnowledgeBaseEntryId = knowledgeBaseEntryId;
        }
    }

    public class GetKnowledgeBaseEntryOutput
    {
        [JsonProperty("knowledge_base_entry")]
        public KnowledgeBaseEntry KnowledgeBaseEntry { get; set; }

        [JsonConstructor]
        public GetKnowledgeBaseEntryOutput(KnowledgeBaseEntry knowledgeBaseEntry)
        {
            KnowledgeBaseEntry = knowledgeBaseEntry;
        }
    }

    public async Task<GetKnowledgeBaseEntryOutput> F(GetKnowledgeBaseEntryInput getKnowledgeBaseEntryInput)
    {
        var entry = await _knowledgeBaseEntryService.GetKnowledgeBaseEntryAsync(
            getKnowledgeBaseEntryInput.SleekflowCompanyId,
            getKnowledgeBaseEntryInput.KnowledgeBaseEntryId);

        return new GetKnowledgeBaseEntryOutput(entry);
    }
}