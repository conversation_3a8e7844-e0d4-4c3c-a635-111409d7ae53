using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Constants;
using Sleekflow.InternalIntegrationHub.Models.Constants;
using Sleekflow.InternalIntegrationHub.OmniHr;

namespace Sleekflow.InternalIntegrationHub.Triggers.NetSuite.Internal;

[TriggerGroup(ControllerNames.Internal, $"{BasePaths.NetSuite}")]
public class GenerateImportEmployeeCsv
    : ITrigger<GenerateImportEmployeeCsv.GenerateImportEmployeeCsvInput,
        GenerateImportEmployeeCsv.GenerateImportEmployeeCsvOutput>
{
    private readonly IOmniHrIntegrationService _omniHrIntegrationService;

    public GenerateImportEmployeeCsv(
        IOmniHrIntegrationService omniHrIntegrationService)
    {
        _omniHrIntegrationService = omniHrIntegrationService;
    }

    public class GenerateImportEmployeeCsvInput
    {
    }

    [method: JsonConstructor]
    public class GenerateImportEmployeeCsvOutput(string csvContent, string fileName, int totalRows)
    {
        [JsonProperty("csv_content")]
        public string CsvContent { get; set; } = csvContent;

        [JsonProperty("file_name")]
        public string FileName { get; set; } = fileName;

        [JsonProperty("generated_at")]
        public DateTime GeneratedAt { get; set; } = DateTime.Now;

        [JsonProperty("total_rows")]
        public int TotalRows { get; set; } = totalRows;
    }

    public async Task<GenerateImportEmployeeCsvOutput> F(GenerateImportEmployeeCsvInput input)
    {
        var (csv, totalRows) = await _omniHrIntegrationService.GenerateImportEmployeeCsvAsync();

        var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
        var fileName = $"staff_export_{timestamp}.csv";

        return new GenerateImportEmployeeCsvOutput(
            csv.ToString(),
            fileName,
            totalRows);
    }
}