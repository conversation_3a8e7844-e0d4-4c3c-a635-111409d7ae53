using System.ClientModel;
using Azure.AI.OpenAI;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.AI.Evaluation;
using Microsoft.Extensions.AI.Evaluation.Reporting;
using Microsoft.Extensions.AI.Evaluation.Reporting.Storage;
using Microsoft.ML.Tokenizers;
using Serilog.Context;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals.Methods;
using Sleekflow.IntelligentHub.Evaluator.Evaluators;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.Models.Prompts;
using static Sleekflow.IntelligentHub.Models.Constants.AgentCollaborationModes;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals;

public class ChatEvalFixture
{
    private const string StorageRootPath = "./";

    private readonly List<IMethod<ChatEvalConfig, ChatEvalOutput>> _methods =
    [
        // new NoRAGMethod(),
        // new SmartReplyMethod(),
        // new LightRagMethod(Long),
        new LightRagMethod(Short),
        new LightRagMethod(LeadNurturing),
        // new LightRagMethod(ManagerLeadNurturing)
    ];

    private readonly ReportingConfiguration _reportingConfiguration = GetReportingConfiguration();

    private static ReportingConfiguration GetReportingConfiguration()
    {
        var config = new AzureOpenAIConfig();

        var endpoint = new Uri(config.Endpoint);
        var oaioptions = new AzureOpenAIClientOptions();
        var azureClient = new AzureOpenAIClient(endpoint, new ApiKeyCredential(config.Key), oaioptions);

        // Setup the chat client that is used to perform the evaluations
        var chatClient = azureClient.GetChatClient("turbo").AsIChatClient();

        var chatConfig = new ChatConfiguration(chatClient);

        // The DiskBasedReportingConfiguration caches LLM responses to reduce costs and
        // increase test run performance.
        return DiskBasedReportingConfiguration.Create(
            storageRootPath: StorageRootPath,
            chatConfiguration: chatConfig,
            evaluators:
            [
                new ChatEvalAnswerScoringEvaluator(),
                new RagOutputScoringEvaluator(),
            ],
            executionName: "default");
    }

    public async Task<ChatEvalResult[]> EvaluateAsync(
        ChatEvalQuestion chatEvalQuestion,
        Guid testId,
        CancellationToken cancellationToken)
    {
        // generate the AI response
        var outputs = await Task.WhenAll(
            _methods.Select(async method =>
            {
                using var property = LogContext.PushProperty("TestId", testId + "_" + method.MethodName);

                var stopwatch = System.Diagnostics.Stopwatch.StartNew();

                ChatEvalOutput output;

                // Set the CurrentTestId for this async context
                ChatEvalTest.CurrentTestId.Value = testId.ToString();
                try
                {
                    var replyGenerationContext = chatEvalQuestion.ReplyGenerationContext ?? new ReplyGenerationContext(
                        chatEvalQuestion.ChatEvalConfig.SleekflowCompanyId,
                        testId + "_" + method.MethodName,
                        null,
                        null,
                        null);

                    // Remove testId from this call signature
                    output = await method.CompleteAsync(
                        chatEvalQuestion.ChatEvalConfig,
                        chatEvalQuestion.QuestionContexts,
                        chatEvalQuestion.SourceFilenames,
                        replyGenerationContext,
                        chatEvalQuestion.AgentConfig ?? AgentConfigUtils.GetAgentConfig(),
                        chatEvalQuestion.SfChatEntriesQuestionContexts);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                    output = new ChatEvalOutput("Error", "Error");
                }
                finally
                {
                    // Clear the CurrentTestId after the operation
                    ChatEvalTest.CurrentTestId.Value = null;
                }

                stopwatch.Stop();

                return (method, output, stopwatch.ElapsedMilliseconds);
            }));

        var chatEvalResults = await Task.WhenAll(
            outputs.Select(async (methodOutput) =>
            {
                var (method, output, elapsedMilliseconds) = methodOutput;

                var (answer, evaluationResult) = await new ChatEvalAssistant(method).Evaluate(
                    chatEvalQuestion,
                    _reportingConfiguration,
                    output.Answer,
                    output.Source,
                    cancellationToken);

                return new ChatEvalResult(
                    method.MethodName,
                    answer,
                    evaluationResult,
                    elapsedMilliseconds);
            }));

        return chatEvalResults.ToArray();
    }
}