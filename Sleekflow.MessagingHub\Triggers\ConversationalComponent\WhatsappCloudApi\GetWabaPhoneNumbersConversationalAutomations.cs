using System.ComponentModel.DataAnnotations;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Payloads.Whatsapp.ConversationalComponent;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.ConversationalComponents;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.ConversationalComponent.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ConversationalAutomations)]
public class GetWabaPhoneNumbersConversationalAutomations
    : ITrigger<
        GetWabaPhoneNumbersConversationalAutomations.GetWabaPhoneNumbersConversationalAutomationsInput,
        GetWabaPhoneNumbersConversationalAutomations.GetWabaPhoneNumbersConversationalAutomationsOutput>
{
    private readonly IWabaService _wabaService;
    private readonly IConversationalAutomationService _conversationalAutomationService;
    private readonly ILogger<GetWabaPhoneNumbersConversationalAutomations> _logger;

    public GetWabaPhoneNumbersConversationalAutomations(
        IWabaService wabaService,
        IConversationalAutomationService conversationalAutomationService,
        ILogger<GetWabaPhoneNumbersConversationalAutomations> logger)
    {
        _wabaService = wabaService;
        _conversationalAutomationService = conversationalAutomationService;
        _logger = logger;
    }

    public class GetWabaPhoneNumbersConversationalAutomationsInput
    {
        [Required]
        [JsonProperty("facebook_waba_id")]
        public string FacebookWabaId { get; set; }

        [Validations.ValidateObject]
        [JsonProperty("pagination_param")]
        public CursorBasedPaginationParam? PaginationParam { get; set; }

        [JsonConstructor]
        public GetWabaPhoneNumbersConversationalAutomationsInput(
            string facebookWabaId,
            CursorBasedPaginationParam? paginationParam = null)
        {
            FacebookWabaId = facebookWabaId;
            PaginationParam = paginationParam;
        }
    }

    public class GetWabaPhoneNumbersConversationalAutomationsOutput
    {
        [JsonProperty("conversational_automations_response")]
        public GetConversationalAutomationsByWabaIdResponse ConversationalAutomationsResponse { get; set; }

        [JsonConstructor]
        public GetWabaPhoneNumbersConversationalAutomationsOutput(
            GetConversationalAutomationsByWabaIdResponse conversationalAutomationsResponse)
        {
            ConversationalAutomationsResponse = conversationalAutomationsResponse;
        }
    }

    public async Task<GetWabaPhoneNumbersConversationalAutomationsOutput> F(
        GetWabaPhoneNumbersConversationalAutomationsInput input)
    {
        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(
            input.FacebookWabaId);

        if (!CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        return new GetWabaPhoneNumbersConversationalAutomationsOutput(
            await _conversationalAutomationService.GetConversationalAutomationsAsync(
                waba,
                input.PaginationParam));
    }
}