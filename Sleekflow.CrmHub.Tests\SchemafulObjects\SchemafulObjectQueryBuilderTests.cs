﻿using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Tests.SchemafulObjects;

public class SchemafulObjectQueryBuilderTests
{
    [SetUp]
    public void Setup()
    {
        // Method intentionally left empty.
    }

    [Test]
    public void QueryTest()
    {
        var mockPropertyId1 = "mock_property_id_1";
        var mockPropertyId2 = "mock_property_id_2";

        var mockPropertyValue1 = 100;
        var mockPropertyValue2 = "option_id_1";
        var mockPropertyValue3 = "option_id_2";

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                            "=",
                            "mock_userprofileid",
                            false)
                    }),
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId1,
                            "=",
                            mockPropertyValue1,
                            true)
                    }),
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "arrayContains",
                            mockPropertyValue2,
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "arrayContains",
                            mockPropertyValue3,
                            true),
                    }),
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>
            {
                new SchemafulObjectQueryBuilder.SchemafulObjectSort(IHasCreatedBy.PropertyNameCreatedBy, "desc", false)
            },
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            "mock_company_id",
            "mock_schema_id",
            true);

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT *\n" + "FROM schema_mock_schema_id m\n" +
                "WHERE (m[\"sleekflow_company_id\"] = @param_sleekflow_company_id_m_0) " +
                "AND (m[\"schema_id\"] = @param_schema_id_m_1) AND (m[\"sleekflow_user_profile_id\"] = @param_sleekflow_user_profile_id_m_2) " +
                "AND (m[\"indexed_property_values\"][\"mock_property_id_1\"] = @param_mock_property_id_1_m_3) " +
                "AND (ARRAY_CONTAINS(m[\"indexed_property_values\"][\"mock_property_id_2\"], @param_mock_property_id_2_m_4, false) OR ARRAY_CONTAINS(m[\"indexed_property_values\"][\"mock_property_id_2\"], @param_mock_property_id_2_m_5, false))\n" +
                "ORDER BY m[\"created_by\"] desc"));

        Assert.That(queryDefinition.GetQueryParameters()[0].Name, Is.EqualTo("@param_sleekflow_company_id_m_0"));
        Assert.That(queryDefinition.GetQueryParameters()[0].Value, Is.EqualTo("mock_company_id"));

        Assert.That(queryDefinition.GetQueryParameters()[1].Name, Is.EqualTo("@param_schema_id_m_1"));
        Assert.That(queryDefinition.GetQueryParameters()[1].Value, Is.EqualTo("mock_schema_id"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@param_sleekflow_user_profile_id_m_2"));
        Assert.That(queryDefinition.GetQueryParameters()[2].Value, Is.EqualTo("mock_userprofileid"));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@param_mock_property_id_1_m_3"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo(mockPropertyValue1));

        Assert.That(queryDefinition.GetQueryParameters()[4].Name, Is.EqualTo("@param_mock_property_id_2_m_4"));
        Assert.That(queryDefinition.GetQueryParameters()[4].Value, Is.EqualTo(mockPropertyValue2));

        Assert.That(queryDefinition.GetQueryParameters()[5].Name, Is.EqualTo("@param_mock_property_id_2_m_5"));
        Assert.That(queryDefinition.GetQueryParameters()[5].Value, Is.EqualTo(mockPropertyValue3));
    }

    [Test]
    public void CountGroupByTest()
    {
        var mockPropertyId1 = "mock_property_id_1";
        var mockPropertyId2 = "mock_property_id_2";

        var mockPropertyValue1 = 100;
        var mockPropertyValue2 = "option_id_1";
        var mockPropertyValue3 = "option_id_2";

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>
            {
                new SchemafulObjectQueryBuilder.PlainSelect("COUNT(1)", "count")
            },
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                            "=",
                            "mock_userprofileid",
                            false)
                    }),
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId1,
                            "=",
                            mockPropertyValue1,
                            true)
                    }),
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "arrayContains",
                            mockPropertyValue2,
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "arrayContains",
                            mockPropertyValue3,
                            true),
                    }),
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>
            {
                new SchemafulObjectQueryBuilder.GroupBy(IHasCreatedAt.PropertyNameCreatedAt, false)
            },
            "mock_company_id",
            "mock_schema_id",
            true);

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT COUNT(1) count, m[\"created_at\"] as \"created_at\"\n" +
                "FROM schema_mock_schema_id m\n" +
                "WHERE (m[\"sleekflow_company_id\"] = @param_sleekflow_company_id_m_0) AND (m[\"schema_id\"] = @param_schema_id_m_1) AND (m[\"sleekflow_user_profile_id\"] = @param_sleekflow_user_profile_id_m_2) AND (m[\"indexed_property_values\"][\"mock_property_id_1\"] = @param_mock_property_id_1_m_3) AND (ARRAY_CONTAINS(m[\"indexed_property_values\"][\"mock_property_id_2\"], @param_mock_property_id_2_m_4, false) OR ARRAY_CONTAINS(m[\"indexed_property_values\"][\"mock_property_id_2\"], @param_mock_property_id_2_m_5, false))\n" +
                "GROUP BY m[\"created_at\"]"));

        Assert.That(queryDefinition.GetQueryParameters()[0].Name, Is.EqualTo("@param_sleekflow_company_id_m_0"));
        Assert.That(queryDefinition.GetQueryParameters()[0].Value, Is.EqualTo("mock_company_id"));

        Assert.That(queryDefinition.GetQueryParameters()[1].Name, Is.EqualTo("@param_schema_id_m_1"));
        Assert.That(queryDefinition.GetQueryParameters()[1].Value, Is.EqualTo("mock_schema_id"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@param_sleekflow_user_profile_id_m_2"));
        Assert.That(queryDefinition.GetQueryParameters()[2].Value, Is.EqualTo("mock_userprofileid"));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@param_mock_property_id_1_m_3"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo(mockPropertyValue1));

        Assert.That(queryDefinition.GetQueryParameters()[4].Name, Is.EqualTo("@param_mock_property_id_2_m_4"));
        Assert.That(queryDefinition.GetQueryParameters()[4].Value, Is.EqualTo(mockPropertyValue2));

        Assert.That(queryDefinition.GetQueryParameters()[5].Name, Is.EqualTo("@param_mock_property_id_2_m_5"));
        Assert.That(queryDefinition.GetQueryParameters()[5].Value, Is.EqualTo(mockPropertyValue3));
    }

    [Test]
    public void NotOperatorTest()
    {
        var mockPropertyId1 = "mock_property_id_1";
        var mockPropertyId2 = "mock_property_id_2";

        var mockPropertyValue1 = 100;
        var mockPropertyValue2 = "option_id_1";
        var mockPropertyValue3 = "option_id_2";

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                            "notIn",
                            new List<string>
                            {
                                "mock_userprofileid1",
                                "mock_userprofileid2"
                            },
                            false)
                    }),
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId1,
                            "notContains",
                            mockPropertyValue1,
                            true)
                    }),
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "notArrayContains",
                            mockPropertyValue2,
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "notArrayContains",
                            mockPropertyValue3,
                            true),
                    }),
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            "mock_company_id",
            "mock_schema_id",
            true);

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT *\n" + "FROM schema_mock_schema_id m\n" +
                "WHERE (m[\"sleekflow_company_id\"] = @param_sleekflow_company_id_m_0) " +
                "AND (m[\"schema_id\"] = @param_schema_id_m_1) AND (NOT ARRAY_CONTAINS(@param_sleekflow_user_profile_id_m_2, m[\"sleekflow_user_profile_id\"], false)) " +
                "AND (NOT CONTAINS(m[\"indexed_property_values\"][\"mock_property_id_1\"], @param_mock_property_id_1_m_3, true)) " +
                "AND (NOT ARRAY_CONTAINS(m[\"indexed_property_values\"][\"mock_property_id_2\"], @param_mock_property_id_2_m_4, false) OR NOT ARRAY_CONTAINS(m[\"indexed_property_values\"][\"mock_property_id_2\"], @param_mock_property_id_2_m_5, false))"));
    }

    [Test]
    public void CreatedByAndUpdatedByTest()
    {
        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            "created_by",
                            "in",
                            new List<string>
                            {
                                "11",
                                "22"
                            },
                            false)
                    }),
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            "updated_by",
                            "!=",
                            "33",
                            false)
                    }),
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            "mock_company_id",
            "mock_schema_id",
            true);

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT *\n" + "FROM schema_mock_schema_id m\n" +
                "WHERE (m[\"sleekflow_company_id\"] = @param_sleekflow_company_id_m_0) AND (m[\"schema_id\"] = @param_schema_id_m_1) " +
                "AND (ARRAY_CONTAINS(@param_created_by_m_2, m[\"created_by\"][\"sleekflow_staff_id\"], false)) " +
                "AND (m[\"updated_by\"][\"sleekflow_staff_id\"] != @param_updated_by_m_3)"));
    }

    [Test]
    public void JoinTest_WithTwoJoinClauses_ShouldReturnCorrectValue()
    {
        var mockPropertyId1 = "mock_property_id_1";
        var mockPropertyId2 = "mock_property_id_2";

        var mockPropertyValue1 = 100;
        var mockPropertyValue2 = "option_id_1";
        var mockPropertyValue3 = "option_id_2";

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            "mock_company_id",
            "mock_schema_id",
            true,
            new List<SchemafulObjectQueryBuilder.ArrayExist>
            {
                new SchemafulObjectQueryBuilder.ArrayExist(
                    "mock_inner_property_id",
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId1,
                            "=",
                            mockPropertyValue1,
                            true)
                    }),
                new SchemafulObjectQueryBuilder.ArrayExist(
                    "mock_inner_property_id",
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "arrayContains",
                            mockPropertyValue2,
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId2,
                            "in",
                            new List<string>
                            {
                                mockPropertyValue2,
                                mockPropertyValue3
                            },
                            true)
                    })
            });

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT *\n" + "FROM schema_mock_schema_id m\n" +
                "WHERE (m[\"sleekflow_company_id\"] = @param_sleekflow_company_id_m_0) " +
                "AND (m[\"schema_id\"] = @param_schema_id_m_1)\n " +
                "AND EXISTS ( SELECT 1 FROM e0 IN m[\"indexed_property_values\"][\"mock_inner_property_id\"] WHERE (e0[\"property_values\"][\"mock_property_id_1\"] = @param_mock_property_id_1_e0_0)) " +
                "AND EXISTS ( SELECT 1 FROM e1 IN m[\"indexed_property_values\"][\"mock_inner_property_id\"] WHERE (ARRAY_CONTAINS(e1[\"property_values\"][\"mock_property_id_2\"], @param_mock_property_id_2_e1_0, false) OR ARRAY_CONTAINS(@param_mock_property_id_2_e1_1, e1[\"property_values\"][\"mock_property_id_2\"], false)))"));

        Assert.That(queryDefinition.GetQueryParameters()[0].Name, Is.EqualTo("@param_sleekflow_company_id_m_0"));
        Assert.That(queryDefinition.GetQueryParameters()[0].Value, Is.EqualTo("mock_company_id"));

        Assert.That(queryDefinition.GetQueryParameters()[1].Name, Is.EqualTo("@param_schema_id_m_1"));
        Assert.That(queryDefinition.GetQueryParameters()[1].Value, Is.EqualTo("mock_schema_id"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@param_mock_property_id_1_e0_0"));
        Assert.That(queryDefinition.GetQueryParameters()[2].Value, Is.EqualTo(mockPropertyValue1));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@param_mock_property_id_2_e1_0"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo(mockPropertyValue2));

        Assert.That(queryDefinition.GetQueryParameters()[4].Name, Is.EqualTo("@param_mock_property_id_2_e1_1"));
        Assert.That(queryDefinition.GetQueryParameters()[4].Value, Is.EqualTo(new List<string>
        {
            mockPropertyValue2,
            mockPropertyValue3
        }));
    }

    [Test]
    public void JoinTest_WithNormalFilters_ShouldReturnCorrectValue()
    {
        var mockPropertyId1 = "mock_property_id_1";

        var mockPropertyValue1 = 100;

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            new List<SchemafulObjectQueryBuilder.FilterGroup>
            {
                new SchemafulObjectQueryBuilder.FilterGroup(
                    new List<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                            "notIn",
                            new List<string>
                            {
                                "mock_userprofileid1",
                                "mock_userprofileid2"
                            },
                            false)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            "mock_company_id",
            "mock_schema_id",
            true,
            new List<SchemafulObjectQueryBuilder.ArrayExist>
            {
                new SchemafulObjectQueryBuilder.ArrayExist(
                    "mock_inner_property_id",
                    true,
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            mockPropertyId1,
                            "=",
                            mockPropertyValue1,
                            true)
                    })
            });

        Assert.That(
            queryDefinition.QueryText,
            Is.EqualTo(
                "SELECT *\n" + "FROM schema_mock_schema_id m\n" +
                "WHERE (m[\"sleekflow_company_id\"] = @param_sleekflow_company_id_m_0) " +
                "AND (m[\"schema_id\"] = @param_schema_id_m_1) AND (NOT ARRAY_CONTAINS(@param_sleekflow_user_profile_id_m_2, m[\"sleekflow_user_profile_id\"], false))\n " +
                "AND EXISTS ( SELECT 1 FROM e0 IN m[\"indexed_property_values\"][\"mock_inner_property_id\"] WHERE (e0[\"property_values\"][\"mock_property_id_1\"] = @param_mock_property_id_1_e0_0))"));

        Assert.That(queryDefinition.GetQueryParameters()[2].Name, Is.EqualTo("@param_sleekflow_user_profile_id_m_2"));
        Assert.That(
            queryDefinition.GetQueryParameters()[2].Value,
            Is.EqualTo(
                new List<string>
                {
                    "mock_userprofileid1", "mock_userprofileid2"
                }));

        Assert.That(queryDefinition.GetQueryParameters()[3].Name, Is.EqualTo("@param_mock_property_id_1_e0_0"));
        Assert.That(queryDefinition.GetQueryParameters()[3].Value, Is.EqualTo(mockPropertyValue1));
    }
}