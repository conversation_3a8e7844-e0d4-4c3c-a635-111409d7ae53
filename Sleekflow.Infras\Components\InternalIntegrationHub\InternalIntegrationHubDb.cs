using System.Collections.Immutable;
using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Components.Utils;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.InternalIntegrationHub;

public class InternalIntegrationHubDb
{
    private readonly ResourceGroup _resourceGroup;
    private readonly DocumentDB.DatabaseAccount _databaseAccount;
    private readonly MyConfig _myConfig;

    public InternalIntegrationHubDb(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        MyConfig myConfig)
    {
        _resourceGroup = resourceGroup;
        _databaseAccount = databaseAccount;
        _myConfig = myConfig;
    }

    public class InternalIntegrationHubDbOutput
    {
        public Output<string> AccountName { get; }

        public Output<string> AccountKey { get; }

        public string DatabaseId { get; }

        public InternalIntegrationHubDbOutput(
            Output<string> accountName,
            Output<string> accountKey,
            string databaseId)
        {
            AccountName = accountName;
            AccountKey = accountKey;
            DatabaseId = databaseId;
        }
    }

    public InternalIntegrationHubDbOutput InitInternalIntegrationHubDb()
    {
        const string cosmosDbId = "internalIntegrationHubDb";
        var cosmosDb = new DocumentDB.SqlResourceSqlDatabase(
            cosmosDbId,
            new DocumentDB.SqlResourceSqlDatabaseArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                AccountName = _databaseAccount.Name,
                Resource = new DocumentDB.Inputs.SqlDatabaseResourceArgs
                {
                    Id = cosmosDbId,
                },
                Options = new DocumentDB.Inputs.CreateUpdateOptionsArgs
                {
                    AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                    {
                        MaxThroughput = _myConfig.Name == "production" ? 20000 : 1000
                    }
                }
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        // Sleekflow.Cosmos.AuditHubDb.IAuditHubDbService
        var containerParams = new ContainerParam[]
        {
        };

        var containerIdToContainer = ContainerUtils.CreateSqlResourceSqlContainers(
            _resourceGroup,
            _databaseAccount,
            cosmosDb,
            cosmosDbId,
            containerParams);

        var cosmosDbAccountKeys = DocumentDB.ListDatabaseAccountKeys.Invoke(
            new DocumentDB.ListDatabaseAccountKeysInvokeArgs
            {
                AccountName = _databaseAccount.Name, ResourceGroupName = _resourceGroup.Name
            });
        var cosmosDbAccountName = _databaseAccount.Name;
        var cosmosDbAccountKey = cosmosDbAccountKeys.Apply(accountKeys => accountKeys.PrimaryMasterKey);

        return new InternalIntegrationHubDbOutput(
            cosmosDbAccountName,
            cosmosDbAccountKey,
            cosmosDbId);
    }
}