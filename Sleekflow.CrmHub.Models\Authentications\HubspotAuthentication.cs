﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Authentications;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("hubspot_authentication")]
public class HubspotAuthentication : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("token_type")]
    public string TokenType { get; set; }

    [JsonProperty("access_token")]
    public string AccessToken { get; set; }

    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; }

    [JsonProperty("expires_in")]
    public long ExpiresIn { get; set; }

    [JsonProperty("issued_at")]
    public DateTimeOffset IssuedAt { get; set; }

    [JsonProperty("raw_res")]
    public Dictionary<string, object?> RawRes { get; set; }

    [JsonProperty("refresh_res")]
    public Dictionary<string, object?>? RefreshRes { get; set; }

    [JsonProperty("account_information")]
    public AccountInformation? AccountInformation { get; set; }

    [JsonConstructor]
    public HubspotAuthentication(
        string id,
        string sleekflowCompanyId,
        string tokenType,
        string accessToken,
        string refreshToken,
        long expiresIn,
        DateTimeOffset issuedAt,
        Dictionary<string, object?> rawRes,
        AccountInformation? accountInformation)
        : base(id, "Authentication")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        TokenType = tokenType;
        AccessToken = accessToken;
        RefreshToken = refreshToken;
        ExpiresIn = expiresIn;
        IssuedAt = issuedAt;
        RawRes = rawRes;
        AccountInformation = accountInformation;
    }
}

public class AccountInformation
{
    [JsonProperty("portalId")]
    public int PortalId { get; set; }

    [JsonProperty("timeZone")]
    public string TimeZone { get; set; }

    [JsonProperty("companyCurrency")]
    public string CompanyCurrency { get; set; }

    [JsonProperty("additionalCurrencies")]
    public List<string> AdditionalCurrencies { get; set; }

    [JsonProperty("utcOffset")]
    public string UtcOffset { get; set; }

    [JsonProperty("utcOffsetMilliseconds")]
    public int UtcOffsetMilliseconds { get; set; }

    [JsonProperty("uiDomain")]
    public string UiDomain { get; set; }

    [JsonProperty("dataHostingLocation")]
    public string DataHostingLocation { get; set; }

    [JsonConstructor]
    public AccountInformation(
        int portalId,
        string timeZone,
        string companyCurrency,
        List<string> additionalCurrencies,
        string utcOffset,
        int utcOffsetMilliseconds,
        string uiDomain,
        string dataHostingLocation)
    {
        PortalId = portalId;
        TimeZone = timeZone;
        CompanyCurrency = companyCurrency;
        AdditionalCurrencies = additionalCurrencies;
        UtcOffset = utcOffset;
        UtcOffsetMilliseconds = utcOffsetMilliseconds;
        UiDomain = uiDomain;
        DataHostingLocation = dataHostingLocation;
    }
}