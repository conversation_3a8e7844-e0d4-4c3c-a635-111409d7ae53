using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Internals;

public class UnassignErrorInput
{
    [JsonProperty("state_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StateId { get; set; }

    [JsonProperty("try_catch_step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string TryCatchStepId { get; set; }

    [JsonProperty("stack_entries")]
    [System.ComponentModel.DataAnnotations.Required]
    [Validations.ValidateObject]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public UnassignErrorInput(
        string stateId,
        string tryCatchStepId,
        Stack<StackEntry> stackEntries)
    {
        StateId = stateId;
        TryCatchStepId = tryCatchStepId;
        StackEntries = stackEntries;
    }
}