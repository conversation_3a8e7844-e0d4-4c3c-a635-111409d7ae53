﻿using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.ApexTriggerAuthentications;

public interface IApexTriggerAuthenticationRepository : IRepository<SalesforceApexTriggerAuthentication>
{
}

public class ApexTriggerAuthenticationRepository
    : BaseRepository<SalesforceApexTriggerAuthentication>,
        IApexTriggerAuthenticationRepository,
        ISingletonService
{
    public ApexTriggerAuthenticationRepository(
        ILogger<BaseRepository<SalesforceApexTriggerAuthentication>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}