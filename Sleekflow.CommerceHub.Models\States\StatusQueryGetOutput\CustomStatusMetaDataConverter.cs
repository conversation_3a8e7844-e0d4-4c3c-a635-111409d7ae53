﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Newtonsoft.Json.Serialization;

namespace Sleekflow.CommerceHub.Models.States.StatusQueryGetOutput;

public class CustomStatusMetaDataConcreteClassResolver : DefaultContractResolver
{
    protected override JsonConverter? ResolveContractConverter(Type objectType)
    {
        if (typeof(ICustomStatusMetaData).IsAssignableFrom(objectType)
            && !objectType.IsInterface)
        {
            return null;
        }

        return base.ResolveContractConverter(objectType);
    }
}

public class CustomStatusMetaDataConverter : JsonConverter
{
    private static readonly JsonSerializerSettings ConcreteClassConversion =
        new JsonSerializerSettings()
        {
            ContractResolver = new CustomStatusMetaDataConcreteClassResolver()
        };

#pragma warning disable JA1001
    public override bool CanWrite => false;
#pragma warning restore JA1001

    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        throw new NotImplementedException();
    }

    public override object ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        var jo = JObject.Load(reader);
        var name = jo.GetValue("name")!.Value<string>()!;

        switch (name)
        {
            case "LoopThroughVtexOrdersCustomStatusMetaData":
                return JsonConvert.DeserializeObject<LoopThroughVtexOrdersCustomStatusMetaData>(
                    jo.ToString(),
                    ConcreteClassConversion)!;
            default:
                throw new NotImplementedException();
        }
    }

    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(ICustomStatusMetaData);
    }
}