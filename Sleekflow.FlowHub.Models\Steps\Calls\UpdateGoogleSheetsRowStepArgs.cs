﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateGoogleSheetsRowStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-google-sheets-row";

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("spreadsheet_id")]
    public string SpreadsheetId { get; set; }

    [Required]
    [JsonProperty("worksheet_id")]
    public string WorksheetId { get; set; }

    [Required]
    [JsonProperty("header_row_id")]
    public string HeaderRowId { get; set; }

    [Required]
    [JsonProperty("row_source")]
    public string RowSource { get; set; }

    [JsonProperty("row_id")]
    public string? RowId { get; set; }

    [JsonProperty("row_id__expr")]
    public string? RowIdExpr { get; set; }

    [JsonProperty("fields__id_expr_set")]
    public HashSet<RowFieldIdValuePair> FieldsIdExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.GoogleSheetsIntegration;

    [JsonConstructor]
    public UpdateGoogleSheetsRowStepArgs(
        string connectionId,
        string spreadsheetId,
        string worksheetId,
        string headerRowId,
        string rowSource,
        string? rowId,
        string? rowIdExpr,
        HashSet<RowFieldIdValuePair> fieldsIdExprSet)
    {
        ConnectionId = connectionId;
        SpreadsheetId = spreadsheetId;
        WorksheetId = worksheetId;
        HeaderRowId = headerRowId;
        RowSource = rowSource;
        RowId = rowId;
        RowIdExpr = rowIdExpr;
        FieldsIdExprSet = fieldsIdExprSet;
    }
}