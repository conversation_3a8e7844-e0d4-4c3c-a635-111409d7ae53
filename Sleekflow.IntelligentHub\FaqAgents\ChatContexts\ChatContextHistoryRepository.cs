using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.FaqAgents.ChatContexts;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.FaqAgents.ChatContexts;

public interface IChatContextHistoryRepository : IRepository<ChatContextHistory>
{
}

public class ChatContextHistoryRepository
    : BaseRepository<ChatContextHistory>, IChatContextHistoryRepository, IScopedService
{
    public ChatContextHistoryRepository(
        ILogger<ChatContextHistoryRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}