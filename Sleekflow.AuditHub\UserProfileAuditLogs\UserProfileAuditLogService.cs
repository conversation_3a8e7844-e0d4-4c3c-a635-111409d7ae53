using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.AuditHub.UserProfileAuditLogs;

public interface IUserProfileAuditLogService
{
    Task<UserProfileAuditLog> GetUserProfileAuditLogAsync(string id, string userProfileId);

    Task<List<UserProfileAuditLog>> GetUserProfileAuditLogsAsync(
        string sleekflowCompanyId,
        string userProfileId,
        int limit,
        int offset);

    Task<(string? NextContinuationToken, List<UserProfileAuditLog> UserProfileAuditLogs)> GetUserProfileAuditLogsAsync(
        string sleekflowCompanyId,
        string userProfileId,
        string? continuationToken,
        UserProfileAuditLogsFilters filters,
        int limit);

    Task CreateUserProfileAuditLogAsync(
        UserProfileAuditLog userProfileAuditLog,
        CancellationToken cancellationToken = default);

    Task<UserProfileAuditLog> UpdateUserProfileAuditLogAsync(
        string id,
        string userProfileId,
        string? auditLogText,
        Dictionary<string, object?>? data,
        AuditEntity.SleekflowStaff? updatedBy,
        CancellationToken cancellationToken = default);

    Task<int> DeleteUserProfileAuditLogsAsync(string id, string userProfileId);
}

public class UserProfileAuditLogService : IUserProfileAuditLogService, ISingletonService
{
    private readonly ILogger<UserProfileAuditLogService> _logger;
    private readonly IUserProfileAuditLogRepository _userProfileAuditLogRepository;

    public UserProfileAuditLogService(
        ILogger<UserProfileAuditLogService> logger,
        IUserProfileAuditLogRepository userProfileAuditLogRepository)
    {
        _logger = logger;
        _userProfileAuditLogRepository = userProfileAuditLogRepository;
    }

    public async Task<UserProfileAuditLog> GetUserProfileAuditLogAsync(string id, string userProfileId)
    {
        return await _userProfileAuditLogRepository.GetAsync(id, userProfileId);
    }

    public async Task<List<UserProfileAuditLog>> GetUserProfileAuditLogsAsync(
        string sleekflowCompanyId,
        string userProfileId,
        int limit,
        int offset)
    {
        var userProfileAuditLogs = await _userProfileAuditLogRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId AND c.sleekflow_user_profile_id = @sleekflowUserProfileId " +
                    "ORDER BY c.sleekflow_user_profile_id ASC, c.sleekflow_company_id ASC, c.created_time DESC " +
                    "OFFSET @offset LIMIT @limit ")
                .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                .WithParameter("@sleekflowUserProfileId", userProfileId)
                .WithParameter("@offset", offset)
                .WithParameter("@limit", limit),
            limit);

        return userProfileAuditLogs;
    }

    public async Task<(string? NextContinuationToken, List<UserProfileAuditLog> UserProfileAuditLogs)>
        GetUserProfileAuditLogsAsync(
            string sleekflowCompanyId,
            string userProfileId,
            string? continuationToken,
            UserProfileAuditLogsFilters filters,
            int limit)
    {
        var (userProfileAuditLogs, nextContinuationToken) =
            await _userProfileAuditLogRepository.GetContinuationTokenizedObjectsAsync(
                new QueryDefinition(
                        $"""
                         SELECT *
                         FROM c
                         WHERE
                            c.sleekflow_company_id = @sleekflowCompanyId
                            AND c.sleekflow_user_profile_id = @sleekflowUserProfileId
                            {(filters.Types != null ? "AND ARRAY_CONTAINS(@types, c.type) " : string.Empty)}
                            {(filters.HasSleekflowStaffId == true ? "AND c.sleekflow_staff_id != null " : string.Empty)}
                            {(filters.HasSleekflowStaffId == false ? "AND c.sleekflow_staff_id = null " : string.Empty)}
                         ORDER BY c.sleekflow_user_profile_id ASC, c.sleekflow_company_id ASC, c.created_time DESC
                         """)
                    .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                    .WithParameter("@sleekflowUserProfileId", userProfileId)
                    .WithParameter("@types", filters.Types)
                    .WithParameter("@sleekflowStaffId", filters.HasSleekflowStaffId),
                continuationToken,
                limit);

        return (nextContinuationToken, userProfileAuditLogs);
    }

    public async Task CreateUserProfileAuditLogAsync(
        UserProfileAuditLog userProfileAuditLog,
        CancellationToken cancellationToken = default)
    {
        var insertCount = await _userProfileAuditLogRepository.CreateAsync(
            userProfileAuditLog,
            userProfileAuditLog.SleekflowUserProfileId,
            cancellationToken: cancellationToken);
        if (insertCount == 0)
        {
            _logger.LogError(
                "Unable to create the log. {UserProfileAuditLog}",
                JsonConvert.SerializeObject(userProfileAuditLog, JsonConfig.DefaultLoggingJsonSerializerSettings));

            throw new SfUserFriendlyException("Unable to create the log.");
        }
    }

    public async Task<UserProfileAuditLog> UpdateUserProfileAuditLogAsync(
        string id,
        string userProfileId,
        string? auditLogText,
        Dictionary<string, object?>? data,
        AuditEntity.SleekflowStaff? updatedBy,
        CancellationToken cancellationToken = default)
    {
        var patchOperations = new List<PatchOperation>();

        if (!string.IsNullOrEmpty(auditLogText))
        {
            patchOperations.Add(Set("audit_log_text", auditLogText));
        }

        if (data != null)
        {
            patchOperations.Add(Set("data", data));
        }

        if (!patchOperations.Any())
        {
            return await GetUserProfileAuditLogAsync(id, userProfileId);
        }

        patchOperations.AddRange(new[]
        {
            Set("updated_time", DateTimeOffset.UtcNow),
            Set("updated_by", updatedBy),
        });

        return await _userProfileAuditLogRepository.PatchAndGetAsync(
            id,
            userProfileId,
            patchOperations,
            cancellationToken: cancellationToken);
    }

    public async Task<int> DeleteUserProfileAuditLogsAsync(string id, string userProfileId)
    {
        return await _userProfileAuditLogRepository.DeleteAsync(id, userProfileId);
    }
}