using System.ClientModel.Primitives;
using System.Text;
using System.Text.RegularExpressions;

namespace Sleekflow.IntelligentHub.Kernels;

/// <summary>
/// A custom pipeline transport that overrides outgoing requests to replace the "max_tokens" property
/// and to modify the API version query parameter in the request URI.
/// </summary>
public class AzureOpenAICustomHttpClientPipelineTransport : HttpClientPipelineTransport
{
    private readonly string? _overrideApiVersion;

    public AzureOpenAICustomHttpClientPipelineTransport(string? overrideApiVersion = null)
        : base()
    {
        _overrideApiVersion = overrideApiVersion;
    }

    public AzureOpenAICustomHttpClientPipelineTransport(HttpClient client, string? overrideApiVersion = null)
        : base(client)
    {
        _overrideApiVersion = overrideApiVersion;
    }

    protected override void OnSendingRequest(PipelineMessage message, HttpRequestMessage httpRequest)
    {
        // Call the base implementation (if any).
        base.OnSendingRequest(message, httpRequest);

        // 1. Modify the request content if it meets our conditions.
        if (httpRequest.Content != null && httpRequest.RequestUri != null)
        {
            // Since this method is synchronous, we synchronously wait on the async ReadAsStringAsync.
            var requestBody = httpRequest.Content.ReadAsStringAsync().GetAwaiter().GetResult();

            // Check if the request body contains the target model and "max_tokens" property.
            if ((httpRequest.RequestUri.AbsoluteUri.Contains(SemanticKernelExtensions.S_O3_MINI)
                 || httpRequest.RequestUri.AbsoluteUri.Contains(SemanticKernelExtensions.S_O4_MINI))
                && requestBody.Contains("\"max_tokens\":"))
            {
                // Replace "max_tokens" with "max_completion_tokens"
                requestBody = requestBody.Replace("\"max_tokens\":", "\"max_completion_tokens\":");

                // Update the request content.
                httpRequest.Content = new StringContent(requestBody, Encoding.UTF8, "application/json");
            }
        }

        // 2. Override the API version in the request URI if required.
        if (_overrideApiVersion != null && httpRequest.RequestUri != null)
        {
            var requestUriStr = httpRequest.RequestUri.ToString();

            // Look for a version string at the end of the URL (e.g. "2023-01-15" or "2023-01-15-preview").
            var currentVersion = Regex.Match(requestUriStr, @"\d{4}-\d{2}-\d{2}(-preview)?$").Value;

            if (!string.IsNullOrEmpty(currentVersion))
            {
                // Replace the current version with the override.
                requestUriStr = requestUriStr.Replace(currentVersion, _overrideApiVersion);
            }
            else
            {
                // If no version is found, append the API version as a query parameter.
                var separator = requestUriStr.Contains("?") ? "&" : "?";
                requestUriStr = $"{requestUriStr}{separator}api-version={_overrideApiVersion}";
            }

            httpRequest.RequestUri = new Uri(requestUriStr);
        }
    }
}