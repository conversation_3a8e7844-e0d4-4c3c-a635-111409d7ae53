# ReAct Evaluation Framework

This document provides an overview of the ReAct Evaluation Framework used for testing and evaluating ReAct agent performance in Sleekflow.IntelligentHub.

## Overview

The ReAct (Reasoning and Acting) Evaluation Framework is designed to assess how effectively AI agents can reason through complex scenarios and take appropriate actions based on user inputs. The current implementation focuses on testing the agent's ability to extract information from conversations and update contact properties accordingly.

## Components

### `ReActEvalTest`

The main test class that orchestrates the evaluation process. Key features:

- Uses NUnit's `TestCaseSource` to run tests in parallel
- Tracks test execution with AsyncLocal context
- Calculates and reports performance metrics
- Verifies tool calls match expected behavior

### `ReActEvalFixture`

The test fixture responsible for:

- Executing individual test cases
- Calculating scores based on expected vs. actual tool calls
- Tracking performance metrics (elapsed time, success rate)
- Awarding bonuses for accurate field extraction and time efficiency

### `MockInformationGatheringPlugin`

A mock implementation of the information gathering plugin that:

- Simulates extraction of fields from conversation text
- Records extraction calls and extracted fields for verification
- Provides utilities to check field extraction performance
- Calculates time efficiency bonuses

### `MockSleekflowToolsPlugin`

A mock implementation of the Sleekflow tools plugin that:

- Simulates contact property updating
- Records tool calls and parameters for verification
- Provides utilities to check if specific tools were called
- Stores test context by test ID to support parallel execution

### Test Cases

Test cases are defined in `ReActEvalTestCases.cs` and focus on information gathering scenarios:

1. **Basic Information Gathering**: Simple conversations with clear contact information
2. **Complex Information Gathering**: Multi-field extraction with various contact details
3. **Partial Information Gathering**: Conversations with limited explicit information
4. **No Clear Information**: Conversations without any extractable contact data

## How Scoring Works

Each test case receives a score (0-10) based on:

- Whether fields were correctly extracted from conversations (+1)
- Whether contact properties were correctly updated (+1)
- Accuracy of extracted fields vs. expected fields (up to +3)
- Processing time efficiency (up to +1)

Scores are normalized to a 0-10 scale for consistent reporting.

## Running Tests

Tests are executed via NUnit and can be run in parallel. Each test:

1. Generates a unique test ID
2. Sets up the context with that ID
3. Executes the ReAct agent with the test case
4. Verifies information extraction and property updating
5. Cleans up test state

## Adding New Test Cases

To add new test cases:

1. Add to the `GetInformationGatheringTestCases()` method in `ReActEvalTestCases.cs`
2. Define test cases with:
   - Scenario description
   - User instruction (conversation text)
   - Expected fields to extract
   - Expected contact properties to update

Example:

```csharp
yield return new ReActTestCase(
    "Basic Information Gathering",
    "My name is John Smith and my <NAME_EMAIL>. I'd like to learn more about your product.",
    expectExtractInformation: true,
    expectedContactProperties: new Dictionary<string, string>
    {
        { "first_name", "John" },
        { "last_name", "Smith" },
        { "email", "<EMAIL>" }
    },
    toolsConfig: toolsConfig);
```

## Output and Reporting

The framework generates:

- Console output with test results
- CSV report with detailed metrics
- Summary statistics across all tests

## Information Gathering Workflow

The ReAct agent follows this process:

1. Analyzes the conversation text for contact information
2. Uses `InformationGatheringPlugin` to extract and validate fields
3. Uses `SleekflowToolsPlugin` to update contact properties with the extracted information
4. Provides an appropriate response to the user

## Extending the Framework

The framework can be extended by:

1. Enhancing the field extraction capabilities in `MockInformationGatheringPlugin`
2. Adding new test scenarios in `ReActEvalTestCases.cs`
3. Implementing more sophisticated property validation in `AssertToolCalls()`
4. Fine-tuning the scoring algorithm in `ReActEvalFixture.CalculateScore()`