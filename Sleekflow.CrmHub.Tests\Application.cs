﻿using System.Xml;
using Alba;
using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Mvc.Tests;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Tests;

[SetUpFixture]
public class Application
{
    private static DirectoryInfo? TryGetSolutionDirectoryInfo()
    {
        var directory = new DirectoryInfo(Directory.GetCurrentDirectory());
        while (directory != null && !directory.GetFiles("*.sln").Any())
        {
            directory = directory.Parent;
        }

        return directory;
    }

    [OneTimeSetUp]
    public async Task Init()
    {
        var doc = new XmlDocument();
        doc.Load(Path.Combine(TryGetSolutionDirectoryInfo()!.FullName, ".run/Sleekflow.CrmHub.run.xml"));

        foreach (XmlNode node in doc.SelectNodes("/component/configuration/envs/env")!)
        {
            Environment.SetEnvironmentVariable(
                node.Attributes!["name"]!.Value,
                node.Attributes["value"]!.Value);
        }

        await SetUpHost();
    }

    private static async Task SetUpHost()
    {
        Host = await AlbaHost.For<Program>(
            webHostBuilder =>
            {
                webHostBuilder.ConfigureServices(
                    services =>
                    {
                        services.AddScoped<IDynamicFiltersRepositoryContext, TestRepositoryContext>();
                    });
            },
            Array.Empty<IAlbaExtension>());

        Host.AfterEachAsync(
            async context =>
            {
                await BaseTestHost.InterceptAfterEachAsync(context);
            });
    }

    public class TestRepositoryContext : IDynamicFiltersRepositoryContext
    {
        public bool IsSoftDeleteEnabled { get; set; } = false;

        public string? SleekflowCompanyId { get; set; } = null;
    }

    public static IAlbaHost Host { get; private set; }

    // Make sure that NUnit will shut down the AlbaHost when
    // all the projects are finished
    [OneTimeTearDown]
    public void Teardown()
    {
        Host.Dispose();
    }
}