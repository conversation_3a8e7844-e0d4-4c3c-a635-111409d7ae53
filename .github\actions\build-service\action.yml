name: 'Build Service'
description: 'Builds a service Docker image or retags an existing one'

inputs:
  service_name:
    description: 'Name of the service to build'
    required: true
  branch_name:
    description: 'The current branch name'
    required: true
  build_time:
    description: 'Build timestamp'
    required: true
  image_prefix:
    description: 'The image prefix'
    required: true
  registry:
    description: 'The registry URL'
    required: true
  github_sha:
    description: 'The GitHub SHA'
    required: true
  common_image:
    description: 'The common image to use as base'
    required: true

runs:
  using: 'composite'
  steps:
    - name: Check for changes
      id: changes
      uses: ./.github/actions/check-service-changes
      with:
        service_name: ${{ inputs.service_name }}

    - name: Set build info
      id: build
      shell: bash
      run: |
        # Get service info from mappings file
        service_info=$(jq -r --arg svc "${{ inputs.service_name }}" '.[$svc]' .github/workflows/service-mappings.json)
        path=$(echo "$service_info" | jq -r '.path')
        image=$(echo "$service_info" | jq -r '.image')
        context=$(echo "$service_info" | jq -r '.context // "."')

        # Extract build args and format them for docker build
        build_args=$(echo "$service_info" | jq -r '.build_args // {} | to_entries | map("\(.key)=\(.value)") | join("\n")')

        echo "path=./$path/" >> $GITHUB_OUTPUT
        echo "dockerfile=./$path/Dockerfile" >> $GITHUB_OUTPUT
        echo "image_name=-$image" >> $GITHUB_OUTPUT
        echo "context=$context" >> $GITHUB_OUTPUT
        echo "build_args<<EOF" >> $GITHUB_OUTPUT
        echo "$build_args" >> $GITHUB_OUTPUT
        echo "EOF" >> $GITHUB_OUTPUT

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ inputs.registry }}
        username: ${{ github.actor }}
        password: ${{ github.token }}

    - name: Process ${{ inputs.service_name }}
      id: process
      uses: ./.github/actions/check-build-needed
      with:
        common_changed: ${{ steps.changes.outputs.common_changed }}
        service_changed: ${{ steps.changes.outputs.service_changed }}
        branch_name: ${{ inputs.branch_name }}
        image_name: sleekflow${{ steps.build.outputs.image_name }}
        image_prefix: ${{ inputs.image_prefix }}
        registry: ${{ inputs.registry }}

    # Build new image if needed
    - name: Build ${{ inputs.service_name }}
      if: steps.process.outputs.action == 'build'
      uses: docker/build-push-action@v5
      with:
        context: ${{ steps.build.outputs.context }}
        file: ${{ steps.build.outputs.dockerfile }}
        tags: |
          ${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ inputs.branch_name }}
          ${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ inputs.build_time }}
          ${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ inputs.github_sha }}
        cache-from: |
          type=registry,ref=${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:buildcache-${{ inputs.branch_name }}
          type=registry,ref=${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow-common:buildcache-${{ inputs.branch_name }}
        cache-to: type=registry,ref=${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:buildcache-${{ inputs.branch_name }},mode=max
        push: true
        build-args: |
          COMMON_IMAGE=${{ inputs.common_image }}
          ${{ steps.build.outputs.build_args }}

    # Create new tags without pulling the image
    - name: Create new tags
      if: steps.process.outputs.action == 'retag'
      shell: bash
      run: |
        echo "::group::Debug - Create new tags"
        echo "Creating new tags using buildx imagetools"

        # Create build time tag
        docker buildx imagetools create \
          ${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ inputs.branch_name }} \
          --tag ${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ inputs.build_time }}

        # Create SHA tag
        docker buildx imagetools create \
          ${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ inputs.branch_name }} \
          --tag ${{ inputs.registry }}/${{ inputs.image_prefix }}/sleekflow${{ steps.build.outputs.image_name }}:${{ inputs.github_sha }}
        echo "::endgroup::" 