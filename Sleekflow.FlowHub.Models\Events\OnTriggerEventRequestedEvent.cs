using Sleekflow.Persistence.Abstractions;
using Newtonsoft.Json;
using Sleekflow.Events;
using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

namespace Sleekflow.FlowHub.Models.Events;

public class OnTriggerEventRequestedEvent : IEvent, IHighTrafficEvent, IHasSleekflowCompanyId
{
    [JsonProperty("event_body", TypeNameHandling = TypeNameHandling.Objects)]
    public EventBody EventBody { get; set; }

    [JsonProperty("object_id")]
    public string ObjectId { get; set; }

    [JsonProperty("object_type")]
    public string ObjectType { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    public OnTriggerEventRequestedEvent(
        EventBody eventBody,
        string objectId,
        string objectType,
        string sleekflowCompanyId)
    {
        EventBody = eventBody;
        ObjectId = objectId;
        ObjectType = objectType;
        SleekflowCompanyId = sleekflowCompanyId;
    }
}