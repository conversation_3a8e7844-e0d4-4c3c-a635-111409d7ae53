using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactListRelationshipsStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.update-contact-list-relationships";

    [Required]
    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty("add_list_ids__expr")]
    public string? AddListIdsExpr { get; set; }

    [JsonProperty("remove_list_ids__expr")]
    public string? RemoveListIdsExpr { get; set; }

    [JsonProperty("set_list_ids__expr")]
    public string? SetListIdsExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactListRelationshipsStepArgs(
        string contactIdExpr,
        string? addListIdsExpr,
        string? removeListIdsExpr,
        string? setListIdsExpr)
    {
        ContactIdExpr = contactIdExpr;
        AddListIdsExpr = addListIdsExpr;
        RemoveListIdsExpr = removeListIdsExpr;
        SetListIdsExpr = setListIdsExpr;
    }
}