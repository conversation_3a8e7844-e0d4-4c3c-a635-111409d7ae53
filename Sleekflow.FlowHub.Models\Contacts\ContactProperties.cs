using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Contacts;

public class ContactProperties
{
    [SwaggerInclude]
    public class GetContactPropertiesInput
    {
        [Required]
        [Validations.ValidateObject]
        [JsonProperty("state_identity")]
        public StateIdentity StateIdentity { get; set; }

        [Required]
        [JsonProperty("contact_id")]
        public string ContactId { get; set; }

        [JsonConstructor]
        public GetContactPropertiesInput(StateIdentity stateIdentity, string contactId)
        {
            StateIdentity = stateIdentity;
            ContactId = contactId;
        }
    }

    [SwaggerInclude]
    public class GetContactPropertiesOutput
    {
        [JsonProperty("contact_properties")]
        public Dictionary<string, string>? ContactProperties { get; set; }

        [JsonConstructor]
        public GetContactPropertiesOutput(Dictionary<string, string>? contactProperties)
        {
            ContactProperties = contactProperties;
        }
    }
}