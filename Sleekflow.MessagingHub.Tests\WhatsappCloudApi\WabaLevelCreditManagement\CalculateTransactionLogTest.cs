﻿using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi.WabaLevelCreditManagement;

public class CalculateTransactionLogTest
{
    [Test]
    public void TestCreditTransferTransactionLogForWabaLevelEnabledBusinessBalance_1()
    {
        // business balance total credit = 100
        // no credit has been used up
        // 2 waba balances, both originally are 0 credit with no credit used up
        // now transfer 10 credit from business to the second waba balance
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 0
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 0
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 0
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;
        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  {
                                                      "facebook_waba_id": "166367663226916",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "166367663226916/1959854171056250/CreditTransferFromBusinessToWaba/10_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_type": "CREDIT_TRANSFER",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "credit_transfer_from_to": {
                                                          "credit_transfer_from": {
                                                              "facebook_business_id": "1959854171056250",
                                                              "facebook_waba_id": null,
                                                              "target_type" : "facebook_business"
                                                          },
                                                          "credit_transfer_to": {
                                                              "facebook_business_id": null,
                                                              "facebook_waba_id": "166367663226916",
                                                              "target_type" : "facebook_waba"
                                                          },
                                                          "credit_transfer_amount": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 10
                                                          },
                                                          "credit_transfer_type": "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA"
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:08:10.518Z",
                                                      "updated_at": "2024-06-14T08:08:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  }
                                                  """;

        var creditTransferTransactionLog =
            JsonConvert.DeserializeObject<BusinessBalanceTransactionLog>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLog, Is.Not.Null);

        businessBalance.CalculateFromCreditTransferTransactionLogs(
            new List<BusinessBalanceTransactionLog>()
            {
                creditTransferTransactionLog
            });

        var wabaBalance = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance, Is.Not.Null);

                Assert.Multiple(
                    () =>
                    {
                        Assert.That(wabaBalance.Credit.Amount, Is.EqualTo(10));
                        Assert.That(businessBalance.Credit.Amount, Is.EqualTo(100));
                        Assert.That(businessBalance.Balance.Amount, Is.EqualTo(100));
                        Assert.That(businessBalance.UnallocatedCredit, Is.Not.Null);
                        Assert.That(businessBalance.UnallocatedCredit?.Amount, Is.EqualTo(90));

                        Assert.That(creditTransferTransactionLog.IsCalculated, Is.True);
                    });
            });
    }

    [Test]
    public void TestCreditTransferTransactionLogForWabaLevelEnabledBusinessBalance_2()
    {
        // business balance total credit = 100
        // no credit has been used up
        // 2 waba balances, one originally is 0 credit with no credit used up and the other originally is 40 credit with no credit used up
        // now transfer 10 credit from business to the second waba balance and 20 credit from the first waba balance to business
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 0
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 0
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 0
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 40
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;
        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  [{
                                                      "facebook_waba_id": "166367663226916",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "166367663226916/1959854171056250/CreditTransferFromBusinessToWaba/10_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_type": "CREDIT_TRANSFER",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "credit_transfer_from_to": {
                                                          "credit_transfer_from": {
                                                              "facebook_business_id": "1959854171056250",
                                                              "facebook_waba_id": null,
                                                              "target_type" : "facebook_business"
                                                          },
                                                          "credit_transfer_to": {
                                                              "facebook_business_id": null,
                                                              "facebook_waba_id": "166367663226916",
                                                              "target_type" : "facebook_waba"
                                                          },
                                                          "credit_transfer_amount": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 10
                                                          },
                                                          "credit_transfer_type": "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA"
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:08:10.518Z",
                                                      "updated_at": "2024-06-14T08:08:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  },
                                                  {
                                                      "facebook_waba_id": "169233172937026",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "169233172937026/1959854171056250/CreditTransferFromWabaToBusiness/20_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_type": "CREDIT_TRANSFER",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "credit_transfer_from_to": {
                                                          "credit_transfer_from": {
                                                              "facebook_business_id": null,
                                                              "facebook_waba_id": "169233172937026",
                                                              "target_type" : "facebook_waba"
                                                          },
                                                          "credit_transfer_to": {
                                                              "facebook_business_id": "1959854171056250",
                                                              "facebook_waba_id": null,
                                                              "target_type" : "facebook_business"
                                                          },
                                                          "credit_transfer_amount": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 20
                                                          },
                                                          "credit_transfer_type": "NORMAL_TRANSFER_FROM_WABA_TO_BUSINESS"
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:09:10.518Z",
                                                      "updated_at": "2024-06-14T08:09:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  }]
                                                  """;

        var creditTransferTransactionLogs =
            JsonConvert.DeserializeObject<List<BusinessBalanceTransactionLog>>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLogs, Is.Not.Null);
        Assert.That(creditTransferTransactionLogs, Has.Count.EqualTo(2));

        businessBalance.CalculateFromCreditTransferTransactionLogs(creditTransferTransactionLogs);

        var wabaBalance1 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance1, Is.Not.Null);

        var wabaBalance2 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "169233172937026");
        Assert.That(wabaBalance2, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance1.Credit.Amount, Is.EqualTo(10));
                Assert.That(wabaBalance2.Credit.Amount, Is.EqualTo(20));
                Assert.That(businessBalance.Credit.Amount, Is.EqualTo(100));
                Assert.That(businessBalance.Balance.Amount, Is.EqualTo(100));
                Assert.That(businessBalance.UnallocatedCredit, Is.Not.Null);
                Assert.That(businessBalance.UnallocatedCredit?.Amount, Is.EqualTo(70));

                Assert.That(
                    creditTransferTransactionLogs,
                    Is.All.Matches<BusinessBalanceTransactionLog>(
                        log => log.IsCalculated));
            });
    }

    [Test]
    public void TestCreditTransferTransactionLogForWabaLevelEnabledBusinessBalance_3()
    {
        // business balance total credit = 100
        // 20 credit has been used up
        // 2 waba balances, one originally is 0 credit with no credit used up and the other originally is 40 credit with no credit used up
        // now transfer 10 credit from business to the second waba balance and 20 credit from the first waba balance to business
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 18
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 1
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 1
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 40
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;

        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  [{
                                                      "facebook_waba_id": "166367663226916",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "166367663226916/1959854171056250/CreditTransferFromBusinessToWaba/10_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_type": "CREDIT_TRANSFER",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "credit_transfer_from_to": {
                                                          "credit_transfer_from": {
                                                              "facebook_business_id": "1959854171056250",
                                                              "facebook_waba_id": null,
                                                              "target_type" : "facebook_business"
                                                          },
                                                          "credit_transfer_to": {
                                                              "facebook_business_id": null,
                                                              "facebook_waba_id": "166367663226916",
                                                              "target_type" : "facebook_waba"
                                                          },
                                                          "credit_transfer_amount": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 10
                                                          },
                                                          "credit_transfer_type": "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA"
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:08:10.518Z",
                                                      "updated_at": "2024-06-14T08:08:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  },
                                                  {
                                                      "facebook_waba_id": "169233172937026",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "169233172937026/1959854171056250/CreditTransferFromWabaToBusiness/20_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_type": "CREDIT_TRANSFER",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "credit_transfer_from_to": {
                                                          "credit_transfer_from": {
                                                              "facebook_business_id": null,
                                                              "facebook_waba_id": "169233172937026",
                                                              "target_type" : "facebook_waba"
                                                          },
                                                          "credit_transfer_to": {
                                                              "facebook_business_id": "1959854171056250",
                                                              "facebook_waba_id": null,
                                                              "target_type" : "facebook_business"
                                                          },
                                                          "credit_transfer_amount": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 20
                                                          },
                                                          "credit_transfer_type": "NORMAL_TRANSFER_FROM_WABA_TO_BUSINESS"
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:09:10.518Z",
                                                      "updated_at": "2024-06-14T08:09:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  }]
                                                  """;

        var creditTransferTransactionLogs =
            JsonConvert.DeserializeObject<List<BusinessBalanceTransactionLog>>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLogs, Is.Not.Null);
        Assert.That(creditTransferTransactionLogs, Has.Count.EqualTo(2));

        businessBalance.CalculateFromCreditTransferTransactionLogs(creditTransferTransactionLogs);

        var wabaBalance1 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance1, Is.Not.Null);

        var wabaBalance2 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "169233172937026");
        Assert.That(wabaBalance2, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance1.Credit.Amount, Is.EqualTo(10));
                Assert.That(wabaBalance2.Credit.Amount, Is.EqualTo(20));
                Assert.That(businessBalance.Credit.Amount, Is.EqualTo(100));
                Assert.That(businessBalance.Balance.Amount, Is.EqualTo(80));
                Assert.That(businessBalance.UnallocatedCredit, Is.Not.Null);
                Assert.That(businessBalance.UnallocatedCredit?.Amount, Is.EqualTo(50));

                Assert.That(
                    creditTransferTransactionLogs,
                    Is.All.Matches<BusinessBalanceTransactionLog>(
                        log => log.IsCalculated));
            });
    }

    [Test]
    public void TestCreditTransferTransactionLogWithSomeCreditsUsedUpAlready()
    {
        // business balance total credit = 100
        // 40 credit has been used up
        // 2 waba balances, one originally is 40 credit with no credit used up and the other one originally is 0 credit with no credit used up and the other
        // now transfer 20 credit from the second waba balance to business and 10 credit from business to the first waba balance
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 30
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 8
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 2
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 40
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 18
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 1
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 1
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": ["active"],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;

        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  [
                                                  {
                                                      "facebook_waba_id": "169233172937026",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "169233172937026/1959854171056250/CreditTransferFromWabaToBusiness/20_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_type": "CREDIT_TRANSFER",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "credit_transfer_from_to": {
                                                          "credit_transfer_from": {
                                                              "facebook_business_id": null,
                                                              "facebook_waba_id": "169233172937026",
                                                              "target_type" : "facebook_waba"
                                                          },
                                                          "credit_transfer_to": {
                                                              "facebook_business_id": "1959854171056250",
                                                              "facebook_waba_id": null,
                                                              "target_type" : "facebook_business"
                                                          },
                                                          "credit_transfer_amount": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 20
                                                          },
                                                          "credit_transfer_type": "NORMAL_TRANSFER_FROM_WABA_TO_BUSINESS"
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:09:10.518Z",
                                                      "updated_at": "2024-06-14T08:09:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  },
                                                  {
                                                      "facebook_waba_id": "166367663226916",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "166367663226916/1959854171056250/CreditTransferFromBusinessToWaba/10_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0
                                                      },
                                                      "transaction_type": "CREDIT_TRANSFER",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "credit_transfer_from_to": {
                                                          "credit_transfer_from": {
                                                              "facebook_business_id": "1959854171056250",
                                                              "facebook_waba_id": null,
                                                              "target_type" : "facebook_business"
                                                          },
                                                          "credit_transfer_to": {
                                                              "facebook_business_id": null,
                                                              "facebook_waba_id": "166367663226916",
                                                              "target_type" : "facebook_waba"
                                                          },
                                                          "credit_transfer_amount": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 10
                                                          },
                                                          "credit_transfer_type": "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA"
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:08:10.518Z",
                                                      "updated_at": "2024-06-14T08:08:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  }]
                                                  """;

        var creditTransferTransactionLogs =
            JsonConvert.DeserializeObject<List<BusinessBalanceTransactionLog>>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLogs, Is.Not.Null);
        Assert.That(creditTransferTransactionLogs, Has.Count.EqualTo(2));

        businessBalance.CalculateFromCreditTransferTransactionLogs(creditTransferTransactionLogs);

        var wabaBalance1 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "169233172937026");
        Assert.That(wabaBalance1, Is.Not.Null);

        var wabaBalance2 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance2, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance1.Credit.Amount, Is.EqualTo(20));
                Assert.That(wabaBalance1.Balance.Amount, Is.EqualTo(0));
                Assert.That(wabaBalance2.Credit.Amount, Is.EqualTo(10));
                Assert.That(wabaBalance2.Balance.Amount, Is.EqualTo(10));
                Assert.That(businessBalance.Credit.Amount, Is.EqualTo(100));
                Assert.That(businessBalance.Balance.Amount, Is.EqualTo(60));
                Assert.That(businessBalance.UnallocatedCredit, Is.Not.Null);
                Assert.That(businessBalance.UnallocatedCredit?.Amount, Is.EqualTo(50));

                Assert.That(
                    creditTransferTransactionLogs,
                    Is.All.Matches<BusinessBalanceTransactionLog>(
                        log => log.IsCalculated));
            });
    }

    [Test]
    public void TestCreditTransferTransactionLogWithWabaAllCreditsUsedUpAlready()
    {
        // business balance total credit = 100
        // 40 credits has been used up from business level
        // all 30 credits has been used up from waba id 169233172937026 level
        // 2 waba balances, one originally is 30 credit with all credits used up and the other one originally is 0 credit with no credit used up
        // now transfer 20 credit from waba id 169233172937026 balance to business and 10 credit from business to the waba id 166367663226916 balance
        // Expected: business balance 60 with 50 unallocated credit, waba id 169233172937026 0 balance, waba id 166367663226916 10 balance with 10 credit
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 30
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 5
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 5
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 30
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 20
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;

        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  [
                                                      {
                                                          "facebook_waba_id": "169233172937026",
                                                          "facebook_business_id": "1959854171056250",
                                                          "unique_id": "169233172937026/1959854171056250/CreditTransferFromWabaToBusiness/20_USD",
                                                          "credit": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "used": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_handling_fee": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_type": "CREDIT_TRANSFER",
                                                          "is_calculated": false,
                                                          "waba_top_up": null,
                                                          "waba_conversation_usage": null,
                                                          "markup_profile_snapshot": {
                                                              "per_paid_business_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "per_paid_user_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "transaction_handling_fee_rate": 0.03
                                                          },
                                                          "credit_transfer_from_to": {
                                                              "credit_transfer_from": {
                                                                  "facebook_business_id": null,
                                                                  "facebook_waba_id": "169233172937026",
                                                                  "target_type": "facebook_waba"
                                                              },
                                                              "credit_transfer_to": {
                                                                  "facebook_business_id": "1959854171056250",
                                                                  "facebook_waba_id": null,
                                                                  "target_type": "facebook_business"
                                                              },
                                                              "credit_transfer_amount": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 20
                                                              },
                                                              "credit_transfer_type": "NORMAL_TRANSFER_FROM_WABA_TO_BUSINESS"
                                                          },
                                                          "resynchronize_at_histories": null,
                                                          "created_at": "2024-06-14T08:09:10.518Z",
                                                          "updated_at": "2024-06-14T08:09:10.518Z",
                                                          "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                          "id": "G0cqWLNQMV3QW2",
                                                          "sys_type_name": "BusinessBalanceTransactionLog",
                                                          "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                          "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                          "_attachments": "attachments/",
                                                          "_ts": 1702971407
                                                      },
                                                      {
                                                          "facebook_waba_id": "166367663226916",
                                                          "facebook_business_id": "1959854171056250",
                                                          "unique_id": "166367663226916/1959854171056250/CreditTransferFromBusinessToWaba/10_USD",
                                                          "credit": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "used": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_handling_fee": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_type": "CREDIT_TRANSFER",
                                                          "is_calculated": false,
                                                          "waba_top_up": null,
                                                          "waba_conversation_usage": null,
                                                          "markup_profile_snapshot": {
                                                              "per_paid_business_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "per_paid_user_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "transaction_handling_fee_rate": 0.03
                                                          },
                                                          "credit_transfer_from_to": {
                                                              "credit_transfer_from": {
                                                                  "facebook_business_id": "1959854171056250",
                                                                  "facebook_waba_id": null,
                                                                  "target_type": "facebook_business"
                                                              },
                                                              "credit_transfer_to": {
                                                                  "facebook_business_id": null,
                                                                  "facebook_waba_id": "166367663226916",
                                                                  "target_type": "facebook_waba"
                                                              },
                                                              "credit_transfer_amount": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 10
                                                              },
                                                              "credit_transfer_type": "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA"
                                                          },
                                                          "resynchronize_at_histories": null,
                                                          "created_at": "2024-06-14T08:08:10.518Z",
                                                          "updated_at": "2024-06-14T08:08:10.518Z",
                                                          "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                          "id": "G0cqWLNQMV3QW2",
                                                          "sys_type_name": "BusinessBalanceTransactionLog",
                                                          "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                          "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                          "_attachments": "attachments/",
                                                          "_ts": 1702971407
                                                      }
                                                  ]
                                                  """;

        var creditTransferTransactionLogs =
                    JsonConvert.DeserializeObject<List<BusinessBalanceTransactionLog>>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLogs, Is.Not.Null);
        Assert.That(creditTransferTransactionLogs, Has.Count.EqualTo(2));

        businessBalance.CalculateFromCreditTransferTransactionLogs(creditTransferTransactionLogs);

        var wabaBalance1 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "169233172937026");
        Assert.That(wabaBalance1, Is.Not.Null);

        var wabaBalance2 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance2, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance1.Credit.Amount, Is.EqualTo(30));
                Assert.That(wabaBalance1.Balance.Amount, Is.EqualTo(0));
                Assert.That(wabaBalance2.Credit.Amount, Is.EqualTo(10));
                Assert.That(wabaBalance2.Balance.Amount, Is.EqualTo(10));
                Assert.That(businessBalance.Credit.Amount, Is.EqualTo(100));
                Assert.That(businessBalance.Balance.Amount, Is.EqualTo(60));
                Assert.That(businessBalance.UnallocatedCredit, Is.Not.Null);
                Assert.That(businessBalance.UnallocatedCredit?.Amount, Is.EqualTo(50));

                Assert.That(creditTransferTransactionLogs[0].IsCalculated, Is.False);
                Assert.That(creditTransferTransactionLogs[1].IsCalculated, Is.True);
            });
    }

    [Test]
    public void TestCreditTransferTransactionLogWithBusinessAllCreditsUsedUpAndAllocatedAlready()
    {
        // business balance total credit = 100
        // 70 credits has been used up from business level
        // all 30 credits has been used up from waba id 169233172937026 level
        // 2 waba balances, one originally is 30 credit with all credits used up and the other one originally is 20 credit with no credit used up
        // now transfer 50 credit from business to waba id 169233172937026 balance and 10 credit from waba id 166367663226916 to business
        // Expected: business balance 30 with 20 unallocated credit, waba id 169233172937026 balance 0, waba id 166367663226916 balance 10
        // credit transfer 50 credit from business to waba id 169233172937026 is skipped for insufficient unallocated credits
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 40
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 25
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 5
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 30
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 20
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 20
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;

        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  [
                                                      {
                                                          "facebook_waba_id": "169233172937026",
                                                          "facebook_business_id": "1959854171056250",
                                                          "unique_id": "169233172937026/1959854171056250/CreditTransferFromWabaToBusiness/20_USD",
                                                          "credit": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "used": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_handling_fee": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_type": "CREDIT_TRANSFER",
                                                          "is_calculated": false,
                                                          "waba_top_up": null,
                                                          "waba_conversation_usage": null,
                                                          "markup_profile_snapshot": {
                                                              "per_paid_business_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "per_paid_user_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "transaction_handling_fee_rate": 0.03
                                                          },
                                                          "credit_transfer_from_to": {
                                                              "credit_transfer_from": {
                                                                  "facebook_business_id": "1959854171056250",
                                                                  "facebook_waba_id": null,
                                                                  "target_type": "facebook_business"
                                                              },
                                                              "credit_transfer_to": {
                                                                  "facebook_business_id": null,
                                                                  "facebook_waba_id": "169233172937026",
                                                                  "target_type": "facebook_waba"
                                                              },
                                                              "credit_transfer_amount": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 50
                                                              },
                                                              "credit_transfer_type": "NORMAL_TRANSFER_FROM_BUSINESS_TO_WABA"
                                                          },
                                                          "resynchronize_at_histories": null,
                                                          "created_at": "2024-06-14T08:09:10.518Z",
                                                          "updated_at": "2024-06-14T08:09:10.518Z",
                                                          "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                          "id": "G0cqWLNQMV3QW2",
                                                          "sys_type_name": "BusinessBalanceTransactionLog",
                                                          "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                          "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                          "_attachments": "attachments/",
                                                          "_ts": 1702971407
                                                      },
                                                      {
                                                          "facebook_waba_id": "166367663226916",
                                                          "facebook_business_id": "1959854171056250",
                                                          "unique_id": "166367663226916/1959854171056250/CreditTransferFromBusinessToWaba/10_USD",
                                                          "credit": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "used": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_handling_fee": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "transaction_type": "CREDIT_TRANSFER",
                                                          "is_calculated": false,
                                                          "waba_top_up": null,
                                                          "waba_conversation_usage": null,
                                                          "markup_profile_snapshot": {
                                                              "per_paid_business_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "per_paid_user_initiated_conversation_fee_markup": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 0
                                                              },
                                                              "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                              "transaction_handling_fee_rate": 0.03
                                                          },
                                                          "credit_transfer_from_to": {
                                                              "credit_transfer_from": {
                                                                  "facebook_business_id": null,
                                                                  "facebook_waba_id": "166367663226916",
                                                                  "target_type": "facebook_business"
                                                              },
                                                              "credit_transfer_to": {
                                                                  "facebook_business_id": "1959854171056250",
                                                                  "facebook_waba_id": null,
                                                                  "target_type": "facebook_waba"
                                                              },
                                                              "credit_transfer_amount": {
                                                                  "currency_iso_code": "USD",
                                                                  "amount": 10
                                                              },
                                                              "credit_transfer_type": "NORMAL_TRANSFER_FROM_WABA_TO_BUSINESS"
                                                          },
                                                          "resynchronize_at_histories": null,
                                                          "created_at": "2024-06-14T08:08:10.518Z",
                                                          "updated_at": "2024-06-14T08:08:10.518Z",
                                                          "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                          "id": "G0cqWLNQMV3QW2",
                                                          "sys_type_name": "BusinessBalanceTransactionLog",
                                                          "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                          "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                          "_attachments": "attachments/",
                                                          "_ts": 1702971407
                                                      }
                                                  ]
                                                  """;

        var creditTransferTransactionLogs =
            JsonConvert.DeserializeObject<List<BusinessBalanceTransactionLog>>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLogs, Is.Not.Null);
        Assert.That(creditTransferTransactionLogs, Has.Count.EqualTo(2));

        businessBalance.CalculateFromCreditTransferTransactionLogs(creditTransferTransactionLogs);

        var wabaBalance1 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "169233172937026");
        Assert.That(wabaBalance1, Is.Not.Null);

        var wabaBalance2 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance2, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance1.Credit.Amount, Is.EqualTo(30));
                Assert.That(wabaBalance1.Balance.Amount, Is.EqualTo(0));
                Assert.That(wabaBalance2.Credit.Amount, Is.EqualTo(10));
                Assert.That(wabaBalance2.Balance.Amount, Is.EqualTo(10));
                Assert.That(businessBalance.Credit.Amount, Is.EqualTo(100));
                Assert.That(businessBalance.Balance.Amount, Is.EqualTo(30));
                Assert.That(businessBalance.UnallocatedCredit, Is.Not.Null);
                Assert.That(businessBalance.UnallocatedCredit?.Amount, Is.EqualTo(20));

                Assert.That(creditTransferTransactionLogs[0].IsCalculated, Is.False);
                Assert.That(creditTransferTransactionLogs[1].IsCalculated, Is.True);
            });
    }

    [Test]
    public void TestTransactionLogApplyToBusinessAndWabaBalanceWithWabaId()
    {
        // Ensure both Business and specific waba balance will update the fields Credit, Use, Markup, Transaction Handling Fee and Balance
        // When waba id provided in transaction log.
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 40
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 25
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 5
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 30
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 20
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 20
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;

        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  {
                                                      "facebook_waba_id": "169233172937026",
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "169233172937026/1959854171056250/CreditTransferFromWabaToBusiness/20_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 10
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 5
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0.3
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0.5
                                                      },
                                                      "transaction_type": "TOP_UP",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:09:10.518Z",
                                                      "updated_at": "2024-06-14T08:09:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  }
                                                  """;

        var creditTransferTransactionLog =
            JsonConvert.DeserializeObject<BusinessBalanceTransactionLog>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLog, Is.Not.Null);

        businessBalance.CalculateTransactionLog(creditTransferTransactionLog);

        var wabaBalance1 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "169233172937026");
        Assert.That(wabaBalance1, Is.Not.Null);

        var wabaBalance2 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance2, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance1.Credit.Amount, Is.EqualTo(40));
                Assert.That(wabaBalance1.Balance.Amount, Is.EqualTo(4.2));
                Assert.That(wabaBalance1.Used.Amount, Is.EqualTo(25));
                Assert.That(wabaBalance1.Markup.Amount, Is.EqualTo(5.3));
                Assert.That(wabaBalance1.TransactionHandlingFee?.Amount, Is.Not.Null.And.EqualTo(5.5));

                Assert.That(wabaBalance2.Credit.Amount, Is.EqualTo(20));
                Assert.That(wabaBalance2.Balance.Amount, Is.EqualTo(20));

                Assert.That(businessBalance.Credit.Amount, Is.EqualTo(110));
                Assert.That(businessBalance.Balance.Amount, Is.EqualTo(34.2));
                Assert.That(businessBalance.Used.Amount, Is.EqualTo(45));
                Assert.That(businessBalance.Markup.Amount, Is.EqualTo(25.3));
                Assert.That(businessBalance.TransactionHandlingFee?.Amount, Is.Not.Null.And.EqualTo(5.5));
            });
    }

    [Test]
    public void TestTransactionLogApplyToBusinessOnlyWithOutWabaId()
    {
        // Ensure Business will update the fields Credit, Use, Markup, Transaction Handling Fee and Balance
        var businessBalanceString = """
                                    {
                                        "facebook_business_id": "1959854171056250",
                                        "credit": {
                                            "currency_iso_code": "USD",
                                            "amount": 100
                                        },
                                        "used": {
                                            "currency_iso_code": "USD",
                                            "amount": 40
                                        },
                                        "markup": {
                                            "currency_iso_code": "USD",
                                            "amount": 25
                                        },
                                        "transaction_handling_fee": {
                                            "currency_iso_code": "USD",
                                            "amount": 5
                                        },
                                        "markup_profile": {
                                            "per_paid_business_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "per_paid_user_initiated_conversation_fee_markup": {
                                                "currency_iso_code": "USD",
                                                "amount": 0
                                            },
                                            "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                            "transaction_handling_fee_rate": 0.03
                                        },
                                        "waba_balances": [
                                            {
                                                "id": "1",
                                                "facebook_waba_id": "169233172937026",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 30
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 20
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 5
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0001\"",
                                                "sys_type_name": "WabaBalance"
                                            },
                                            {
                                                "id": "2",
                                                "facebook_waba_id": "166367663226916",
                                                "credit": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 20
                                                },
                                                "used": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "markup": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "transaction_handling_fee": {
                                                    "currency_iso_code": "USD",
                                                    "amount": 0
                                                },
                                                "record_statuses": [
                                                    "active"
                                                ],
                                                "created_at": "2024-06-14T08:07:10.518Z",
                                                "updated_at": "2024-06-14T08:07:10.518Z",
                                                "_etag": "\"0000041f-0000-1900-0000-666a9a2c0002\"",
                                                "sys_type_name": "WabaBalance"
                                            }
                                        ],
                                        "is_by_waba_billing_enabled": true,
                                        "conversation_usage_insert_state": {
                                            "last_conversation_usage_insert_timestamp": 1718262000,
                                            "waba_conversation_insertion_exception": {},
                                            "updated_at": "2024-06-13T07:05:15.824Z"
                                        },
                                        "record_statuses": [],
                                        "created_at": "2023-11-24T08:07:10.518Z",
                                        "updated_at": "2024-06-13T07:05:16.084Z",
                                        "_etag": "\"0000041f-0000-1900-0000-666a9a2c0000\"",
                                        "id": "v0ieGePyAel2AG",
                                        "sys_type_name": "BusinessBalance",
                                        "_rid": "pl8lANSPd4q5BAAAAAAAAA==",
                                        "_self": "dbs/pl8lAA==/colls/pl8lANSPd4o=/docs/pl8lANSPd4q5BAAAAAAAAA==/",
                                        "_attachments": "attachments/",
                                        "_ts": 1718262316
                                    }
                                    """;

        var businessBalance = JsonConvert.DeserializeObject<BusinessBalance>(businessBalanceString);
        Assert.That(businessBalance, Is.Not.Null);

        var businessBalanceTransactionLogString = """
                                                  {
                                                      "facebook_business_id": "1959854171056250",
                                                      "unique_id": "169233172937026/1959854171056250/CreditTransferFromWabaToBusiness/20_USD",
                                                      "credit": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 10
                                                      },
                                                      "used": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 5
                                                      },
                                                      "markup": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0.3
                                                      },
                                                      "transaction_handling_fee": {
                                                          "currency_iso_code": "USD",
                                                          "amount": 0.5
                                                      },
                                                      "transaction_type": "TOP_UP",
                                                      "is_calculated": false,
                                                      "waba_top_up": null,
                                                      "waba_conversation_usage": null,
                                                      "markup_profile_snapshot": {
                                                          "per_paid_business_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "per_paid_user_initiated_conversation_fee_markup": {
                                                              "currency_iso_code": "USD",
                                                              "amount": 0
                                                          },
                                                          "business_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "user_initiated_conversation_fee_price_markup_percentage": 0.12,
                                                          "transaction_handling_fee_rate": 0.03
                                                      },
                                                      "resynchronize_at_histories": null,
                                                      "created_at": "2024-06-14T08:09:10.518Z",
                                                      "updated_at": "2024-06-14T08:09:10.518Z",
                                                      "_etag": "\"0b00d1ab-0000-1900-0000-6581480f0000\"",
                                                      "id": "G0cqWLNQMV3QW2",
                                                      "sys_type_name": "BusinessBalanceTransactionLog",
                                                      "_rid": "pl8lAPLGgnlTrhEAAAAAAA==",
                                                      "_self": "dbs/pl8lAA==/colls/pl8lAPLGgnk=/docs/pl8lAPLGgnlTrhEAAAAAAA==/",
                                                      "_attachments": "attachments/",
                                                      "_ts": 1702971407
                                                  }
                                                  """;

        var creditTransferTransactionLog =
            JsonConvert.DeserializeObject<BusinessBalanceTransactionLog>(businessBalanceTransactionLogString);
        Assert.That(creditTransferTransactionLog, Is.Not.Null);

        businessBalance.CalculateTransactionLog(creditTransferTransactionLog);

        var wabaBalance1 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "169233172937026");
        Assert.That(wabaBalance1, Is.Not.Null);

        var wabaBalance2 = businessBalance.WabaBalances?.Find(waba => waba.FacebookWabaId == "166367663226916");
        Assert.That(wabaBalance2, Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(wabaBalance1.Credit.Amount, Is.EqualTo(30));
                Assert.That(wabaBalance1.Balance.Amount, Is.EqualTo(0));
                Assert.That(wabaBalance1.Used.Amount, Is.EqualTo(20));
                Assert.That(wabaBalance1.Markup.Amount, Is.EqualTo(5));
                Assert.That(wabaBalance1.TransactionHandlingFee?.Amount, Is.Not.Null.And.EqualTo(5));

                Assert.That(wabaBalance2.Credit.Amount, Is.EqualTo(20));
                Assert.That(wabaBalance2.Balance.Amount, Is.EqualTo(20));

                Assert.That(businessBalance.Credit.Amount, Is.EqualTo(110));
                Assert.That(businessBalance.Balance.Amount, Is.EqualTo(34.2));
                Assert.That(businessBalance.Used.Amount, Is.EqualTo(45));
                Assert.That(businessBalance.Markup.Amount, Is.EqualTo(25.3));
                Assert.That(businessBalance.TransactionHandlingFee?.Amount, Is.Not.Null.And.EqualTo(5.5));
            });
    }
}