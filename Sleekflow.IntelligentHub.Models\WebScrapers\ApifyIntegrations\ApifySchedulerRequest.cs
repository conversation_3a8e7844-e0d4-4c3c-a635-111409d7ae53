﻿using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;

public class ApifySchedulerRequest
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("isEnabled")]
    public bool IsEnabled { get; set; }

    [JsonProperty("isExclusive")]
    public bool IsExclusive { get; set; }

    [JsonProperty("cronExpression")]
    public string CronExpression { get; set; }

    [JsonProperty("timezone")]
    public string Timezone { get; set; }

    [JsonProperty("actions")]
    public List<ApifySchedulerRequestAction> Actions { get; set; }

    [JsonConstructor]
    public ApifySchedulerRequest(
        string name,
        string title,
        bool isEnabled,
        bool isExclusive,
        string cronExpression,
        string timezone,
        List<ApifySchedulerRequestAction> actions)
    {
        Name = name;
        Title = title;
        IsEnabled = isEnabled;
        IsExclusive = isExclusive;
        CronExpression = cronExpression;
        Timezone = timezone;
        Actions = actions;
    }
}

public sealed class ApifySchedulerRequestAction
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("actorTaskId")]
    public string ActorTaskId { get; set; }

    [JsonConstructor]
    public ApifySchedulerRequestAction(string type, string actorTaskId)
    {
        Type = type;
        ActorTaskId = actorTaskId;
    }
}