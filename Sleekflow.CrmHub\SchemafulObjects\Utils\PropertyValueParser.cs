﻿using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.SchemafulObjects.Dtos;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;

namespace Sleekflow.CrmHub.SchemafulObjects.Utils;

/// <summary>
/// Parse string input to the correct data type.
/// </summary>
public interface IPropertyValueParser
{
    Dictionary<string, object?> Parse(List<Property> properties, Dictionary<string, object?> values);

    public object? Parse(Property property, object? value);
}

public class PropertyValueParser : IPropertyValueParser, IScopedService
{
    private readonly IIdService _idService;

    public PropertyValueParser(IIdService idService)
    {
        _idService = idService;
    }

    public Dictionary<string, object?> Parse(List<Property> properties, Dictionary<string, object?> values)
    {
        var parsedValues = new Dictionary<string, object?>();
        foreach (var value in values)
        {
            try
            {
                parsedValues.Add(
                    value.Key,
                    Parse(properties.First(p => p.Id == value.Key), value.Value));
            }
            catch (Exception)
            {
                parsedValues.Add(value.Key, value.Value);
            }
        }

        return parsedValues;
    }

    public object? Parse(Property property, object? value)
    {
        if (value == null || IsEmptyString(value))
        {
            return null;
        }

        if (property.DataType.Name == SchemaPropertyDataTypes.MultipleChoice)
        {
            return ParseMultipleChoiceValue(property, value);
        }

        if (property.DataType.Name == SchemaPropertyDataTypes.ArrayObject)
        {
            return ParseArrayObjectValue(property, value);
        }

        if (value is not string stringValue)
        {
            return value;
        }

        switch (property.DataType.Name)
        {
            case SchemaPropertyDataTypes.Numeric:
                if (long.TryParse(stringValue, out var longValue))
                {
                    return longValue;
                }

                break;
            case SchemaPropertyDataTypes.Decimal:
                if (decimal.TryParse(stringValue, out var decimalValue))
                {
                    return decimalValue;
                }

                break;
            case SchemaPropertyDataTypes.SingleChoice:
                // if value is not Option.Id, try match with Option.Value
                if (!property.Options!.Exists(o => o.Id == stringValue))
                {
                    return property.Options.Find(o => o.Value == stringValue)!.Id;
                }

                break;
            case SchemaPropertyDataTypes.Boolean:
                if (bool.TryParse(stringValue, out var boolValue))
                {
                    return boolValue;
                }

                break;

            case SchemaPropertyDataTypes.Date:
            case SchemaPropertyDataTypes.DateTime:
                if (DateTimeOffset.TryParse(stringValue, out var dateTimeOffsetValue))
                {
                    return dateTimeOffsetValue;
                }

                break;
        }

        return value;
    }

    /// <summary>
    /// Detect if the input value is list of [Option.Value], if so, parse to list of [Option.Id].
    /// </summary>
    /// <param name="property">Property.</param>
    /// <param name="value">Property Value.</param>
    private static object? ParseMultipleChoiceValue(Property property, object? value)
    {
        if (!TryParseToJArray(value, out var arr))
        {
            return value;
        }

        List<string> receivedValues = arr
            .Children<JValue>()
            .Select(opt => opt.Value<string>())
            .ToList()!;

        // if input already is option id, just return
        var optionIds = property.Options!.Select(o => o.Id).ToList();
        if (optionIds.Count == optionIds.Union(receivedValues).ToList().Count)
        {
            return arr;
        }

        var parsedValues = new List<string>();
        receivedValues.ForEach(r => parsedValues.Add(property.Options!.Find(o => o.Value == r)!.Id));

        return JArray.FromObject(parsedValues);
    }

    private static bool TryParseToJArray(object? value, out JArray jArray)
    {
        var res = false;
        try
        {
            if (value is JArray arr)
            {
                jArray = arr;
            }
            else
            {
                jArray = JArray.Parse((string) value!);
            }

            res = true;
        }
        catch
        {
            jArray = new JArray();
        }

        return res;
    }

    /// <summary>
    /// Detect if the input value is list of [InnerSchemafulObjectInputDto], if so,
    /// - parse the inner PropertyValues
    /// - assign correct Id, CreatedAt, UpdatedAt.
    /// </summary>
    /// <param name="property">Property.</param>
    /// <param name="value">Property Value.</param>
    /// <returns>JArray of [InnerSchemafulObjectDto].</returns>
    private object? ParseArrayObjectValue(Property property, object? value)
    {
        if (!TryParseToJArray(value, out var arr))
        {
            return value;
        }

        var settings = new JsonSerializerSettings
        {
            DateTimeZoneHandling = DateTimeZoneHandling.Utc
        };

        var innerSchema = property.DataType.GetInnerSchema();
        var innerSchemafulObjectInputDtos = JsonConvert.DeserializeObject<List<InnerSchemafulObjectInputDto>>(
            arr.ToString(),
            settings)!;

        var innerSchemafulObjectDtos = new List<InnerSchemafulObjectDto>();

        foreach (var innerSchemafulObjectInputDto in innerSchemafulObjectInputDtos)
        {
            var propertyValues = Parse(innerSchema.Properties, innerSchemafulObjectInputDto.PropertyValues);

            var id = string.IsNullOrEmpty(innerSchemafulObjectInputDto.Id)
                ? _idService.GetId(SysTypeNames.InnerSchemafulObject)
                : innerSchemafulObjectInputDto.Id;

            var createdAt = innerSchemafulObjectInputDto.CreatedAt ?? DateTimeOffset.UtcNow;

            innerSchemafulObjectDtos.Add(new InnerSchemafulObjectDto(
                id,
                propertyValues,
                createdAt));
        }

        return JArray.FromObject(innerSchemafulObjectDtos, JsonSerializer.Create(settings));
    }

    private static bool IsEmptyString(object? value)
    {
        return value is string s && string.IsNullOrWhiteSpace(s);
    }
}