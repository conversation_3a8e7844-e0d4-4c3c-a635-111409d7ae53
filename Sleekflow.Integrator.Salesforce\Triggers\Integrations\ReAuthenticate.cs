using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Salesforce.Authentications;

namespace Sleekflow.Integrator.Salesforce.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class ReAuthenticate : ITrigger
{
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;

    public ReAuthenticate(ISalesforceAuthenticationService salesforceAuthenticationService)
    {
        _salesforceAuthenticationService = salesforceAuthenticationService;
    }

    public class ReAuthenticateInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public ReAuthenticateInput(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class ReAuthenticateOutput
    {
    }

    public async Task<ReAuthenticateOutput> F(
        ReAuthenticateInput reAuthenticateInput)
    {
        await _salesforceAuthenticationService.ReAuthenticateAndStoreAsync(
            reAuthenticateInput.SleekflowCompanyId);

        return new ReAuthenticateOutput();
    }
}