﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.WebPageDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.WebPageDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class GetWebPageDocumentInformation
    : ITrigger<GetWebPageDocumentInformation.GetWebPageDocumentInformationInput,
        GetWebPageDocumentInformation.GetWebPageDocumentInformationOutput>
{
    private readonly IWebPageDocumentService _webPageDocumentService;

    public GetWebPageDocumentInformation(IWebPageDocumentService webPageDocumentService)
    {
        _webPageDocumentService = webPageDocumentService;
    }

    public class GetWebPageDocumentInformationInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(FileDocument.PropertyNameBlobId)]
        public string BlobId { get; set; }

        [JsonConstructor]
        public GetWebPageDocumentInformationInput(string sleekflowCompanyId, string blobId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            BlobId = blobId;
        }
    }

    public class GetWebPageDocumentInformationOutput
    {
        [JsonProperty("document")]
        public KbDocument Document { get; set; }

        [JsonConstructor]
        public GetWebPageDocumentInformationOutput(KbDocument document)
        {
            Document = document;
        }
    }

    public async Task<GetWebPageDocumentInformationOutput> F(
        GetWebPageDocumentInformationInput getWebPageDocumentInformationInput)
    {
        var webPageDocument = await _webPageDocumentService.GetDocumentByBlobIdAsync(
            getWebPageDocumentInformationInput.SleekflowCompanyId,
            getWebPageDocumentInformationInput.BlobId);
        return new GetWebPageDocumentInformationOutput(webPageDocument);
    }
}