using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Tokens;

public class ChatEntriesPrompt : Prompt
{
    private readonly List<SfChatEntry> _contextChatEntries;
    private readonly SfChatEntry _currentChatEntry;

    public ChatEntriesPrompt(
        List<SfChatEntry> contextChatEntries,
        SfChatEntry currentChatEntry)
    {
        _contextChatEntries = contextChatEntries;
        _currentChatEntry = currentChatEntry;

        if (_contextChatEntries.Exists(ce => ce.Sys != null)
            || _currentChatEntry.Sys != null)
        {
            throw new NotImplementedException();
        }
    }

    public override
        (List<SfChatEntry> ChatEntries, int ConsumedNumOfTokens)
        GetChatEntries(
            int maxNumOfTokens,
            ITokenService tokenService)
    {
        var remainingNumOfTokens = maxNumOfTokens - GetMinimumNumOfTokens(tokenService);

        var chatEntries = new LinkedList<SfChatEntry>();

        // 1. _contextChatEntries
        foreach (var chatEntry in Enumerable.Reverse(_contextChatEntries))
        {
            var chatEntryNumOfTokens =
                (chatEntry.Bot == null ? 0 : tokenService.Count(chatEntry.Bot))
                + (chatEntry.User == null ? 0 : tokenService.Count(chatEntry.User));

            if (chatEntryNumOfTokens > remainingNumOfTokens)
            {
                break;
            }

            chatEntries.AddFirst(chatEntry);
            remainingNumOfTokens -= chatEntryNumOfTokens;
        }

        // 2. _currentChatEntry
        chatEntries.AddLast(_currentChatEntry);

        return (chatEntries.ToList(), maxNumOfTokens - remainingNumOfTokens);
    }

    public override int GetMinimumNumOfTokens(ITokenService tokenService)
    {
        return
            (_currentChatEntry.Bot == null ? 0 : tokenService.Count(_currentChatEntry.Bot))
            + (_currentChatEntry.User == null ? 0 : tokenService.Count(_currentChatEntry.User));
    }
}