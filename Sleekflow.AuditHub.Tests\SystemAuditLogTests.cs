using Sleekflow.AuditHub.Models.SystemAuditLogs;
using Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;
using Sleekflow.AuditHub.Triggers.SystemAuditLogs;
using Sleekflow.DistributedInvocations;
using Sleekflow.Outputs;

namespace Sleekflow.AuditHub.Tests;

public class SystemAuditLogTests
{
    // [Test]
    // public async Task Test_GetSystemAuditLogs_Filters()
    // {
    //     var mockCompanyId = "mockCompanyId";
    //     var mockUserProfileId = "a5005219-8f9b-4362-bd45-b80007ea67a9";
    //
    //     // /AuditLogs/GetSystemAuditLogs
    //     var getSystemAuditLogsInput1 =
    //         new GetSystemAuditLogs.GetSystemAuditLogsInput(
    //             mockCompanyId,
    //             null,
    //             new GetSystemAuditLogs.GetSystemAuditLogsInputFilters(
    //                 new List<string>()
    //                 {
    //                     SystemAuditLogTypes.StaffAddedToTeams
    //                 },
    //                 null,
    //                 null,
    //                 null,
    //                 null),
    //             10);
    //
    //     var getSystemAuditLogsScenarioResult1 = await Application.Host.Scenario(
    //         _ =>
    //         {
    //             _.WithRequestHeader("X-Sleekflow-Record", "true");
    //             _.Post.Json(getSystemAuditLogsInput1).ToUrl("/SystemAuditLogs/GetSystemAuditLogs");
    //         });
    //     var getSystemAuditLogsOutputOutput1 =
    //         await getSystemAuditLogsScenarioResult1.ReadAsJsonAsync<
    //             Output<GetSystemAuditLogs.GetSystemAuditLogsOutput>>();
    //     var getSystemAuditLogsOutput1 = getSystemAuditLogsOutputOutput1!.Data;
    //
    //     Assert.That(getSystemAuditLogsOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
    //     Assert.That(getSystemAuditLogsOutput1.SystemAuditLogs.Count, Is.GreaterThan(0));
    // }

    [Test]
    public async Task Test_CreateSystemAuditLog_StaffAddedToTeams()
    {
        // /AuditLogs/CreateSystemAuditLog
        var createSystemAuditLogInput1 =
            new CreateSystemAuditLog.CreateSystemAuditLogInput(
                null,
                SystemAuditLogTypes.StaffAddedToTeams,
                new StaffAddedToTeamsSystemLogData(
                    "MockStaffId",
                    new List<string>()
                    {
                        "Team1", "Team2"
                    },
                    new List<string>()
                    {
                        "Team 1", "Team 2"
                    }));

        var createSystemAuditLogScenarioResult1 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.WithRequestHeader(
                    IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
                    "eyJzbGVla2Zsb3dfY29tcGFueV9pZCI6ImI2ZDdlNDQyLTM4YWUtNGI5YS1iMTAwLTI5NTE3Mjk3NjhiYyIsInNsZWVrZmxvd19zdGFmZl9pZCI6IjEiLCJzbGVla2Zsb3dfc3RhZmZfdGVhbV9pZHMiOlsiMSJdfQ==");
                _.Post.Json(createSystemAuditLogInput1).ToUrl("/SystemAuditLogs/CreateSystemAuditLog");
            });
        var createSystemAuditLogOutputOutput1 =
            await createSystemAuditLogScenarioResult1.ReadAsJsonAsync<
                Output<CreateSystemAuditLog.CreateSystemAuditLogOutput>>();
        var createSystemAuditLogOutput1 = createSystemAuditLogOutputOutput1!.Data;

        Assert.That(createSystemAuditLogOutputOutput1!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task Test_CreateSystemAuditLog_StaffAddedToTeams_Invalid()
    {
        // /AuditLogs/CreateSystemAuditLog
        var createSystemAuditLogInput1 =
            new CreateSystemAuditLog.CreateSystemAuditLogInput(
                null,
                SystemAuditLogTypes.StaffAddedToTeams,
                new UserProfileListCreatedSystemLogData(
                    "123",
                    "Lorem Ipsum"));

        var createSystemAuditLogScenarioResult1 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.WithRequestHeader(
                    IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
                    "eyJzbGVla2Zsb3dfY29tcGFueV9pZCI6ImI2ZDdlNDQyLTM4YWUtNGI5YS1iMTAwLTI5NTE3Mjk3NjhiYyIsInNsZWVrZmxvd19zdGFmZl9pZCI6IjEiLCJzbGVla2Zsb3dfc3RhZmZfdGVhbV9pZHMiOlsiMSJdfQ==");
                _.Post.Json(createSystemAuditLogInput1).ToUrl("/SystemAuditLogs/CreateSystemAuditLog");
            });
        var createSystemAuditLogOutputOutput1 =
            await createSystemAuditLogScenarioResult1.ReadAsJsonAsync<
                Output<object>>();
        var createSystemAuditLogOutput1 = createSystemAuditLogOutputOutput1!.Data;

        Assert.That(createSystemAuditLogOutputOutput1!.HttpStatusCode, Is.EqualTo(400));
    }
}