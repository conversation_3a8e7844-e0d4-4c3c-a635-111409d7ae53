﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.States.StatusQueryGetOutput;

[JsonConverter(typeof(CustomStatusMetaDataConverter))]
public interface ICustomStatusMetaData
{
    [JsonProperty("name")]
    string Name { get; }
}

public sealed class LoopThroughVtexOrdersCustomStatusMetaData : ICustomStatusMetaData
{
    [JsonProperty("name")]
    public string Name => "LoopThroughVtexOrdersCustomStatusMetaData";

    [JsonProperty("processed_earliest_order_created_at")]
    public DateTimeOffset? ProcessedEarliestOrderCreatedAt { get; set; }

    [JsonConstructor]
    public LoopThroughVtexOrdersCustomStatusMetaData(
        DateTimeOffset? processedEarliestOrderCreatedAt)
    {
        ProcessedEarliestOrderCreatedAt = processedEarliestOrderCreatedAt;
    }
}