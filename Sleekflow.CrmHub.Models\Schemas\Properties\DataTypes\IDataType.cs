﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;

[JsonConverter(typeof(DataTypeConverter))]
public interface IDataType
{
    public const string PropertyNameName = "name";
    public const string PropertyNameInnerSchema = "inner_schema";

    [JsonProperty(PropertyNameName)]
    public string Name { get; set; }

    [JsonProperty(PropertyNameInnerSchema)]
    public InnerSchema? InnerSchema { get; set; }

    public bool HasInnerSchema();

    public InnerSchema GetInnerSchema();
}