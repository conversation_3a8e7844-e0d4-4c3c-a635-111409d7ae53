using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class UserProfileListCreatedSystemLogData
{
    [Required]
    [JsonProperty("list_id")]
    public string ListId { get; set; }

    [Required]
    [JsonProperty("list_name")]
    public string ListName { get; set; }

    [JsonConstructor]
    public UserProfileListCreatedSystemLogData(string listId, string listName)
    {
        ListId = listId;
        ListName = listName;
    }
}