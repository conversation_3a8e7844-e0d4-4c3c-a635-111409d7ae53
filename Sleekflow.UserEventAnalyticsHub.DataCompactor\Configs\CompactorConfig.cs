using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.UserEventAnalyticsHub.DataCompactor.Configs;

public class CompactorConfig : IConfig, ICompactorConfig
{
    public string StorageAccountName { get; }
    public string StorageAccountKey { get; }
    public string EventsContainerName { get; }
    public string StorageConnStr { get; }
    public string ResultsContainerName { get; }
    public int MaxConcurrentCompanies { get; }
    public int FileBatchSize { get; }
    public int RetryAttempts { get; }
    public int ProcessingTimeoutMinutes { get; }

    public CompactorConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        StorageAccountName = Environment.GetEnvironmentVariable("ANALYTICS_STORAGE_ACCOUNT_NAME", target) ??
                             throw new SfMissingEnvironmentVariableException("ANALYTICS_STORAGE_ACCOUNT_NAME");

        StorageAccountKey = Environment.GetEnvironmentVariable("ANALYTICS_STORAGE_ACCOUNT_KEY", target) ??
                            throw new SfMissingEnvironmentVariableException("ANALYTICS_STORAGE_ACCOUNT_KEY");

        EventsContainerName = Environment.GetEnvironmentVariable("EVENTS_CONTAINER_NAME", target) ??
                              throw new SfMissingEnvironmentVariableException("EVENTS_CONTAINER_NAME");

        StorageConnStr = Environment.GetEnvironmentVariable("ANALYTICS_STORAGE_CONN_STR", target) ??
                         throw new SfMissingEnvironmentVariableException("ANALYTICS_STORAGE_CONN_STR");

        ResultsContainerName = Environment.GetEnvironmentVariable("RESULTS_CONTAINER_NAME", target) ??
                               throw new SfMissingEnvironmentVariableException("RESULTS_CONTAINER_NAME");

        MaxConcurrentCompanies = int.TryParse(Environment.GetEnvironmentVariable("COMPACTOR_MAX_CONCURRENT_COMPANIES", target), out var maxConcurrent)
            ? maxConcurrent : 3;

        FileBatchSize = int.TryParse(Environment.GetEnvironmentVariable("COMPACTOR_FILE_BATCH_SIZE", target), out var batchSize)
            ? batchSize : 100;

        RetryAttempts = int.TryParse(Environment.GetEnvironmentVariable("COMPACTOR_RETRY_ATTEMPTS", target), out var retries)
            ? retries : 3;

        ProcessingTimeoutMinutes = int.TryParse(Environment.GetEnvironmentVariable("COMPACTOR_PROCESSING_TIMEOUT_MINUTES", target), out var timeout)
            ? timeout : 30;
    }
}