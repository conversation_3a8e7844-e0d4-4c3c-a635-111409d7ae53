using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

public class JpgFileIngestionProgress : IFileIngestionProgress
{
    [JsonProperty("is_ingestion_completed")]
    public bool IsIngestionCompleted { get; set; }

    [JsonConstructor]
    public JpgFileIngestionProgress(bool isIngestionCompleted)
    {
        IsIngestionCompleted = isIngestionCompleted;
    }

    public bool IsCompleted()
    {
        return IsIngestionCompleted;
    }

    public double GetProgressPercentage()
    {
        return IsIngestionCompleted ? 100.0 : 0.0;
    }
}