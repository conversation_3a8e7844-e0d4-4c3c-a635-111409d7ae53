using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.FaqAgents.ChatContexts;

namespace Sleekflow.IntelligentHub.FaqAgents.ChatContexts;

public interface IChatContextService
{
    Task<string> GetContextIdAsync(string sleekflowCompanyId, string phoneNumber, bool isReset = false);
}

public class ChatContextService : IChatContextService, IScopedService
{
    private readonly IChatContextRepository _chatContextRepository;

    public ChatContextService(IChatContextRepository chatContextRepository)
    {
        _chatContextRepository = chatContextRepository;
    }

    public async Task<string> GetContextIdAsync(string sleekflowCompanyId, string phoneNumber, bool isReset = false)
    {
        var currentTime = DateTimeOffset.UtcNow;
        var interval = TimeSpan.Zero;

        var checkpoint = await _chatContextRepository.GetOrDefaultAsync(phoneNumber, sleekflowCompanyId);

        if (checkpoint is not null)
        {
            interval = currentTime - checkpoint.ContextStartTime;
        }

        if (isReset || checkpoint is null || checkpoint.ContextId is null || checkpoint.HistoryCount >= 20 ||
            interval.TotalHours > 2)
        {
            checkpoint = new ChatContext(
                phoneNumber,
                sleekflowCompanyId,
                Guid.NewGuid().ToString(),
                currentTime,
                0);
        }

        if (!isReset)
        {
            checkpoint.HistoryCount++;
        }

        await _chatContextRepository.UpsertAsync(checkpoint, sleekflowCompanyId);

        return checkpoint.ContextId;
    }
}