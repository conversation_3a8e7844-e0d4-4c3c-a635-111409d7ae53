﻿using Sleekflow.CrmHub.Blobs;
using Sleekflow.CrmHub.Models.SchemafulObjects.Readers;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;
using Sleekflow.Persistence;

namespace Sleekflow.CrmHub.SchemafulObjects.FileProcessors;

public interface ISchemafulObjectFileService
{
    Task<string> GetSchemafulObjectCsvTemplateAsync(string schemaId, string sleekflowCompanyId);

    Task<(int Count, MySchemafulObjectCsvReaderState? MySchemafulObjectCsvReaderState)>
        ProcessSchemafulObjectCsvBatchAsync(
            string schemaId,
            string sleekflowCompanyId,
            string blobSasUri,
            int batchSize,
            MySchemafulObjectCsvReaderState? mySchemafulObjectCsvReaderState);
}

public class SchemafulObjectFileService : ISchemafulObjectFileService, IScopedService
{
    private const string AuditSource = "import";

    private readonly ILogger<MySchemafulObjectCsvReader> _myCustomCatalogCsvReaderLogger;
    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;
    private readonly IAzureBlobClientFactory _azureBlobClientFactory;

    public SchemafulObjectFileService(
        ILogger<MySchemafulObjectCsvReader> myCustomCatalogCsvReaderLogger,
        ISchemaService schemaService,
        ISchemafulObjectService schemafulObjectService,
        IAzureBlobClientFactory azureBlobClientFactory)
    {
        _myCustomCatalogCsvReaderLogger = myCustomCatalogCsvReaderLogger;
        _schemaService = schemaService;
        _schemafulObjectService = schemafulObjectService;
        _azureBlobClientFactory = azureBlobClientFactory;
    }

    public async Task<string> GetSchemafulObjectCsvTemplateAsync(string schemaId, string sleekflowCompanyId)
    {
        var schema = await _schemaService.GetAsync(schemaId, sleekflowCompanyId);

        return MySchemafulObjectCsvReader.GetCsvTemplate(schema.Properties);
    }

    public async Task<(int Count, MySchemafulObjectCsvReaderState? MySchemafulObjectCsvReaderState)> ProcessSchemafulObjectCsvBatchAsync(
        string schemaId,
        string sleekflowCompanyId,
        string blobSasUri,
        int batchSize,
        MySchemafulObjectCsvReaderState? mySchemafulObjectCsvReaderState)
    {
        var schema = await _schemaService.GetAsync(schemaId, sleekflowCompanyId);
        var blobClient = _azureBlobClientFactory.GetBlobClient(blobSasUri);

        var mySchemafulObjectCsvReader = new MySchemafulObjectCsvReader(
            blobClient,
            mySchemafulObjectCsvReaderState,
            _myCustomCatalogCsvReaderLogger,
            schema);

        await mySchemafulObjectCsvReader.GetHeadersAsync();
        var myReaderEntries = await mySchemafulObjectCsvReader.ReadLinesAsync(batchSize);

        var tasks = myReaderEntries
            .Select(
                myReaderEntry =>
                    ProcessMyReaderEntryAsync(
                        schema,
                        myReaderEntry))
            .ToList();
        await Task.WhenAll(tasks);

        return (myReaderEntries.Count, mySchemafulObjectCsvReader.GetState());
    }

    #region private methods

    private async Task ProcessMyReaderEntryAsync(Schema schema, MyReaderEntry myReaderEntry)
    {
        var myReaderSchemafulObjectDto = myReaderEntry.SchemafulObject;

        try
        {
            if (myReaderSchemafulObjectDto == null)
            {
                throw new SfUserFriendlyException(myReaderEntry.ErrorMessage ?? "Unknown Error");
            }

            await CreateOrUpdateSchemafulObjectAsync(
                schema,
                schema.SleekflowCompanyId,
                myReaderSchemafulObjectDto);
        }
        catch (Exception e)
        {
            _myCustomCatalogCsvReaderLogger.LogInformation(
                e,
                "{FuncName} Errors occured in row {Row}, Message {Message}",
                nameof(ProcessSchemafulObjectCsvBatchAsync),
                myReaderEntry.Row,
                e.Message);
        }
    }

    private async Task CreateOrUpdateSchemafulObjectAsync(
        Schema schema,
        string sleekflowCompanyId,
        MyReaderSchemafulObjectDto myReaderSchemafulObjectDto)
    {
        try
        {
            await _schemafulObjectService.CreateAndGetSchemafulObjectAsync(
                schema,
                sleekflowCompanyId,
                myReaderSchemafulObjectDto.PrimaryPropertyValue,
                myReaderSchemafulObjectDto.PropertyValues,
                myReaderSchemafulObjectDto.SleekflowUserProfileId,
                AuditSource,
                new AuditEntity.SleekflowStaff(string.Empty, null));
        }
        catch (SfCustomObjectPrimaryPropertyValueOccupiedException)
        {
            // if schemaful object exists, update instead
            var existedSchemafulObject = await _schemafulObjectService.GetSchemafulObjectByPrimaryPropertyValueAsync(
                schema.Id,
                sleekflowCompanyId,
                myReaderSchemafulObjectDto.PrimaryPropertyValue);

            await _schemafulObjectService.PatchAndGetSchemafulObjectAsync(
                existedSchemafulObject.Id,
                schema,
                sleekflowCompanyId,
                myReaderSchemafulObjectDto.PropertyValues,
                myReaderSchemafulObjectDto.SleekflowUserProfileId,
                AuditSource,
                new AuditEntity.SleekflowStaff(string.Empty, null));
        }
    }

    #endregion
}