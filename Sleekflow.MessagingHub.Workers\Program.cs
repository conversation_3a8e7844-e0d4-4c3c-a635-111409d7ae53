using Microsoft.DurableTask.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sleekflow;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Workers.SentMessageBatches;
using Sleekflow.Mvc;

const string name = "SleekflowMessagingHubWorker";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildDbServices(services);
        Modules.BuildServiceBusServices(services);
        MessagingHubModules.BuildMessagingHubDbServices(services);
        services.AddDurableTaskClient(
            builder =>
            {
            });

        services.AddSingleton<IIdService, IdService>();
        services.AddSingleton<ISentMessageBatchRepository, SentMessageBatchRepository>();
        services.AddHttpContextAccessor();
    });

hostBuilder.Build().Run();