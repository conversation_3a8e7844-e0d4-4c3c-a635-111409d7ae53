﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;

namespace Sleekflow.CrmHub.Models.Unifies;

[Resolver(typeof(ICrmHubDbResolver))]
[DatabaseId("crmhubdb")]
[ContainerId("sys_unify")]
public class Unify : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("unify_rules")]
    public List<UnifyRule> UnifyRules { get; set; }

    [JsonProperty("flattened_unify_rules")]
    public List<FlattenedUnifyRule> FlattenedUnifyRules { get; set; }

    [JsonConstructor]
    public Unify(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        List<UnifyRule> unifyRules,
        List<FlattenedUnifyRule> flattenedUnifyRules)
        : base(id, "Unify")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        EntityTypeName = entityTypeName;
        UnifyRules = unifyRules;
        FlattenedUnifyRules = flattenedUnifyRules;
    }
}