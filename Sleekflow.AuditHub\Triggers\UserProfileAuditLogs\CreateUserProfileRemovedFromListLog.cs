﻿using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileRemovedFromListLog : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateUserProfileRemovedFromListLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateUserProfileRemovedFromListLogInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [Validations.ValidateArray]
        [JsonProperty("sleekflow_user_profile_ids")]
        public List<string> SleekflowUserProfileIds { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public UserProfileRemovedFromListLogData Data { get; set; }

        [JsonConstructor]
        public CreateUserProfileRemovedFromListLogInput(
            string? sleekflowCompanyId,
            List<string> sleekflowUserProfileIds,
            string? sleekflowStaffId,
            string auditLogText,
            UserProfileRemovedFromListLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileIds = sleekflowUserProfileIds;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class CreateUserProfileRemovedFromListLogOutput
    {
        [JsonProperty("ids")]
        public List<string> Ids { get; set; }

        [JsonConstructor]
        public CreateUserProfileRemovedFromListLogOutput(List<string> ids)
        {
            Ids = ids;
        }
    }

    public async Task<CreateUserProfileRemovedFromListLogOutput> F(
        CreateUserProfileRemovedFromListLogInput createUserProfileRemovedFromListLogInput)
    {
        var dataStr = JsonConvert.SerializeObject(createUserProfileRemovedFromListLogInput.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var ids = new ConcurrentBag<string>();
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await Parallel.ForEachAsync(
            createUserProfileRemovedFromListLogInput.SleekflowUserProfileIds,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (userProfileId, cancellationToken) =>
            {
                var id = _idService.GetId("UserProfileAuditLog");
                await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
                    new UserProfileAuditLog(
                        id,
                        (distributedInvocationContext?.SleekflowCompanyId
                         ?? createUserProfileRemovedFromListLogInput.SleekflowCompanyId)
                        ?? throw new InvalidOperationException(),
                        distributedInvocationContext?.SleekflowStaffId
                        ?? createUserProfileRemovedFromListLogInput.SleekflowStaffId,
                        userProfileId,
                        UserProfileAuditLogTypes.UserProfileRemovedFromList,
                        createUserProfileRemovedFromListLogInput.AuditLogText,
                        data,
                        DateTimeOffset.UtcNow,
                        null),
                    cancellationToken);
                ids.Add(id);
            });

        return new CreateUserProfileRemovedFromListLogOutput(ids.ToList());
    }
}