﻿using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CommerceHub.Workers.Configs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Workers.Triggers;

public class PatchCustomCatalogFileProcessStatus : ITrigger
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public PatchCustomCatalogFileProcessStatus(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public class PatchCustomCatalogFileProcessStatusInput
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string Id { get; set; }

        [JsonProperty("file_process_status")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FileProcessStatus { get; set; }

        [JsonConstructor]
        public PatchCustomCatalogFileProcessStatusInput(
            string sleekflowCompanyId,
            string id,
            string fileProcessStatus)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Id = id;
            FileProcessStatus = fileProcessStatus;
        }
    }

    public class PatchCustomCatalogFileProcessStatusOutput
    {
    }

    [Function("PatchCustomCatalogFileProcessStatus")]
    public async Task<PatchCustomCatalogFileProcessStatusOutput> Batch(
        [ActivityTrigger]
        PatchCustomCatalogFileProcessStatusInput patchCustomCatalogFileProcessStatusInput)
    {
        var inputJsonStr =
            JsonConvert.SerializeObject(
                patchCustomCatalogFileProcessStatusInput,
                JsonConfig.DefaultJsonSerializerSettings);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.CommerceHubInternalsEndpoint + "/PatchCustomCatalogFileProcessStatus"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<PatchCustomCatalogFileProcessStatusOutput>()!;
    }
}