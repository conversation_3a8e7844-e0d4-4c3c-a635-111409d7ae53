using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.Events;

public class OnZohoCompleteStepActivationEvent
{
    public string AggregateStepId { get; set; }

    public string ProxyStateId { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public string? AggregateStateContext { get; set; }

    public OnZohoCompleteStepActivationEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string? aggregateStateContext)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        AggregateStateContext = aggregateStateContext;
    }
} 