using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class DeleteProducts
    : ITrigger<
        DeleteProducts.DeleteProductsInput,
        DeleteProducts.DeleteProductsOutput>
{
    private readonly IProductService _productService;

    public DeleteProducts(
        IProductService productService)
    {
        _productService = productService;
    }

    public class DeleteProductsInput : IHasSleekflowStaff
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [MaxLength(50)]
        [JsonProperty("product_ids")]
        public List<string> ProductIds { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public DeleteProductsInput(
            string sleekflowCompanyId,
            string storeId,
            List<string> productIds,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            ProductIds = productIds;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class DeleteProductsOutput
    {
    }

    public async Task<DeleteProductsOutput> F(DeleteProductsInput deleteProductsInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            deleteProductsInput.SleekflowStaffId,
            deleteProductsInput.SleekflowStaffTeamIds);

        foreach (var productId in deleteProductsInput.ProductIds)
        {
            await _productService.DeleteProductAsync(
                productId,
                deleteProductsInput.SleekflowCompanyId,
                deleteProductsInput.StoreId,
                sleekflowStaff);
        }

        return new DeleteProductsOutput();
    }
}