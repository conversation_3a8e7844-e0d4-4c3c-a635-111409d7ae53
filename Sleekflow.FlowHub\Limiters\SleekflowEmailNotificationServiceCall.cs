using System.ComponentModel.DataAnnotations;
using System.Text;
using Newtonsoft.Json;
using Serilog;
using Sleekflow.Attributes;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Utils;
using Sleekflow.Mvc.Https;
using Sleekflow.Persistence.Abstractions;
using ILogger = Serilog.ILogger;

namespace Sleekflow.FlowHub.Limiters;

public class SleekflowEmailNotificationServiceCall
{
    private static readonly ILogger Logger = Log.Logger.ForContext<SleekflowEmailNotificationServiceCall>();
    private static readonly IAppConfig AppConfig = new AppConfig();

    private static readonly HttpClient HttpClient = new HttpClient(
        new PrivateNetworkHttpClientHandler
        {
            AllowAutoRedirect = false,
        });

    [SwaggerInclude]
    public class SendWorkflowInfiniteLoopEmailInput : IHasSleekflowCompanyId, IHasCreatedAt
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_id")]
        [Required]
        public string WorkflowId { get; set; }

        [JsonProperty("workflow_name")]
        [Required]
        public string WorkflowName { get; set; }

        [JsonProperty("created_at")]
        [Required]
        public DateTimeOffset CreatedAt { get; set; }


        [JsonConstructor]
        public SendWorkflowInfiniteLoopEmailInput(
            string sleekflowCompanyId,
            string workflowId,
            string workflowName,
            DateTimeOffset createdAt)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowId = workflowId;
            WorkflowName = workflowName;
            CreatedAt = createdAt;
        }
    }

    [SwaggerInclude]
    public class SendWorkflowInfiniteLoopEmailOutput
    {
        [JsonConstructor]
        public SendWorkflowInfiniteLoopEmailOutput()
        {
        }
    }

    public static async Task SendWorkflowInfiniteLoopEmailAsync(
        string sleekflowCompanyId,
        string workflowId,
        string workflowName,
        string? origin,
        DateTimeOffset createdAt)
    {
        await InvokeAsync<SendWorkflowInfiniteLoopEmailInput, SendWorkflowInfiniteLoopEmailOutput>(
            origin,
            "SendWorkflowInfiniteLoopEmail",
            new SendWorkflowInfiniteLoopEmailInput(sleekflowCompanyId, workflowId, workflowName, createdAt));
    }

    #region SendExecutionUsageReachedThresholdEmail

    [SwaggerInclude]
    public class SendExecutionUsageReachedThresholdEmailInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("threshold")]
        [Required]
        public double Threshold { get; set; }

        [JsonConstructor]
        public SendExecutionUsageReachedThresholdEmailInput(string sleekflowCompanyId, double threshold)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Threshold = threshold;
        }
    }

    [SwaggerInclude]
    public class SendExecutionUsageReachedThresholdEmailOutput
    {
        [JsonConstructor]
        public SendExecutionUsageReachedThresholdEmailOutput()
        {
        }
    }

    public static async Task SendExecutionUsageReachedThresholdEmailAsync(
        string? origin,
        string companyId,
        double usagePercentageThreshold)
    {
        var input = new SendExecutionUsageReachedThresholdEmailInput(companyId, usagePercentageThreshold);

        await InvokeAsync<SendExecutionUsageReachedThresholdEmailInput, SendExecutionUsageReachedThresholdEmailOutput>(
            origin,
            "SendExecutionUsageReachedThresholdEmail",
            input);
    }

    #endregion

    private static async Task<TOutput> InvokeAsync<TInput, TOutput>(
        string? origin,
        string methodName,
        TInput input)
    {
        var baseUrl = string.IsNullOrWhiteSpace(origin)
            ? AppConfig.CoreInternalsEndpoint
            : $"{origin}/FlowHub/Internals";

        var url = baseUrl + "/ServicesCall/EmailNotificationService/" + methodName;
        var headers = new Dictionary<string, string?>
        {
            {
                "X-Sleekflow-Flow-Hub-Authorization", InternalsTokenUtils.CreateJwt(AppConfig.CoreInternalsKey)
            },
        };

        var reqMsg = new HttpRequestMessage(
            HttpMethod.Post,
            url);
        foreach (var (key, value) in headers)
        {
            reqMsg.Headers.Add(key, value ?? string.Empty);
        }

        var argsJson = JsonConvert.SerializeObject(
            input,
            JsonConfig.DefaultJsonSerializerSettings);

        reqMsg.Content = new StringContent(
            argsJson,
            Encoding.UTF8,
            "application/json");

        Logger.Information(
            "SleekflowEmailNotificationServiceCall: {MethodName} {ArgsJson}",
            methodName,
            argsJson);

        var resMsg = await HttpClient.SendAsync(reqMsg);

        Logger.Information(
            "SleekflowEmailNotificationServiceCall: {MethodName} {ArgsJson} {StatusCode}",
            methodName,
            argsJson,
            resMsg.StatusCode);

        resMsg.EnsureSuccessStatusCode();

        var responseStr = await resMsg.Content.ReadAsStringAsync();

        Logger.Information(
            "SleekflowEmailNotificationServiceCall: {MethodName} {ArgsJson} {ResponseStr}",
            methodName,
            argsJson,
            responseStr);

        return JsonConvert.DeserializeObject<TOutput>(responseStr, JsonConfig.DefaultJsonSerializerSettings)!;
    }
}