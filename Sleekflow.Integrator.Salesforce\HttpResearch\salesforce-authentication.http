﻿#
# https://developer.salesforce.com/docs/atlas.en-us.api_rest.meta/api_rest/intro_oauth_and_connected_apps.htm
# https://help.salesforce.com/s/articleView?id=sf.remoteaccess_oauth_tokens_scopes.htm&type=5
#

### 1. Request an Authorization Code
POST https://login.salesforce.com/services/oauth2/authorize?response_type=code&client_id=3MVG9wt4IL4O5wvJVHcTe2hOnRGRb51O49poWGUQodkxM2vKip5gpWgjay2CBcYWk04wiZIJI8ifQuX80dNUp&redirect_uri=https://webhook.site/99c22ca4-025b-4645-8f79-477efcf49cd8&state=12312312312312&scope=openid+full+refresh_token HTTP/1.1

### 2. User Authenticates and Authorizes Access
### 3. Salesforce Grants Authorization Code

### 4. Request an Access Token
POST https://login.salesforce.com/services/oauth2/token HTTP/1.1
Content-type: application/x-www-form-urlencoded

grant_type=authorization_code
&code=aPrx_yTkbIprMMjNp0N8jLv_4eDjkx5orWBobdGkoPSjcoL.KZoEON5fDr_jm40IN5_T.nbpww==
&client_id=3MVG9wt4IL4O5wvJVHcTe2hOnRGRb51O49poWGUQodkxM2vKip5gpWgjay2CBcYWk04wiZIJI8ifQuX80dNUp
&client_secret=****************************************************************
&redirect_uri=https://webhook.site/99c22ca4-025b-4645-8f79-477efcf49cd8

### Salesforce Grants an Access Token
# {
#   "access_token": "00D5i000001vMQV!ARQAQOhm7awsgIekw6hagKgJvTn9R6.3iR0_FremXPZ85dGdJIVBq_snKs0MDCzcZX.62Mi3Qo7hTDzr1F2emCBzmgzuBkNl",
#   "refresh_token": "***************************************************************************************",
#   "signature": "OsYagxZTzTGpH6s7dZgh2SsI5b0GbMPgiBTa21rEzzA=",
#   "scope": "refresh_token openid full",
#   "id_token": "*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
#   "instance_url": "https://sleekflow-dev-ed.my.salesforce.com",
#   "id": "https://login.salesforce.com/id/00D5i000001vMQVEA2/0055i000001eMWiAAM",
#   "token_type": "Bearer",
#   "issued_at": "1651826709544"
# }

# If you use refresh tokens, your code should first try the regular API call, and if you get a 4xx result, try using the refresh token to get a new session token, and if that fails, then you've been kicked out, and the user needs to re-authenticate to continue. If you don't use refresh tokens, you can skip the middle step, obviously

### Get an Access Token with the Refresh Token
POST https://login.salesforce.com/services/oauth2/token HTTP/1.1
Content-type: application/x-www-form-urlencoded

grant_type=refresh_token
&client_id=3MVG9wt4IL4O5wvJVHcTe2hOnRGRb51O49poWGUQodkxM2vKip5gpWgjay2CBcYWk04wiZIJI8ifQuX80dNUp
&client_secret=****************************************************************
&refresh_token=***************************************************************************************

### Userinfo

GET https://login.salesforce.com/services/oauth2/userinfo HTTP/1.1
Authorization: Bearer {{access_token}}
