﻿using Sleekflow.CommerceHub.Models.Vtex;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Vtex.Authentications;

public interface IVtexAuthenticationRepository : IRepository<VtexAuthentication>
{
}

public class VtexAuthenticationRepository
    : BaseRepository<VtexAuthentication>, IVtexAuthenticationRepository, IScopedService
{
    public VtexAuthenticationRepository(
        ILogger<VtexAuthenticationRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}