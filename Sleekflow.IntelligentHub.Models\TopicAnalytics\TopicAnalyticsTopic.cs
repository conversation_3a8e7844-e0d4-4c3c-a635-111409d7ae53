using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;

namespace Sleekflow.IntelligentHub.Models.TopicAnalytics;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.TopicAnalyticsTopic)]
public class TopicAnalyticsTopic : AuditEntity, IHasRecordStatuses, IHasMetadata
{
    public static string EntityType => "TopicAnalyticsTopic";

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("terms")]
    public List<TopicAnalyticsTerm> Terms { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public TopicAnalyticsTopic(
        string id,
        string sleekflowCompanyId,
        string name,
        List<TopicAnalyticsTerm> terms,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        List<string> recordStatuses,
        Dictionary<string, object?> metadata,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(id, SysTypeNames.TopicAnalyticsTopic, createdAt, updatedAt, sleekflowCompanyId, createdBy, updatedBy)
    {
        Name = name;
        Terms = terms;
        Metadata = metadata;
        RecordStatuses = recordStatuses;
    }
}