﻿using Sleekflow.CrmHub.Configs;
using Sleekflow.CrmHub.Models.ObjectIdResolvers;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers.States;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Providers;

public class SalesforceIntegratorService : GenericProviderService
{
    public const string ProviderName = "salesforce-integrator";

    private readonly ISalesforceObjectIdResolver _salesforceObjectIdResolver;

    public SalesforceIntegratorService(
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        IProviderStateService providerStateService,
        ILoopThroughObjectsProgressStateService loopThroughObjectsProgressStateService,
        ISalesforceObjectIdResolver salesforceObjectIdResolver,
        ICustomSyncConfigService customSyncConfigService,
        ILogger<SalesforceIntegratorService> logger)
        : base(
            providerStateService,
            loopThroughObjectsProgressStateService,
            httpClientFactory.CreateClient("default-handler"),
            customSyncConfigService,
            ProviderName,
            appConfig.SalesforceIntegratorEndpoint,
            logger)
    {
        _salesforceObjectIdResolver = salesforceObjectIdResolver;
    }

    public override Task<string?> ResolveObjectIdAsync(Dictionary<string, object?> dict, string entityTypeName)
    {
        try
        {
            return Task.FromResult((string?) _salesforceObjectIdResolver.ResolveObjectId(dict));
        }
        catch (SfIdUnresolvableException)
        {
            return Task.FromResult((string?) null);
        }
    }
}