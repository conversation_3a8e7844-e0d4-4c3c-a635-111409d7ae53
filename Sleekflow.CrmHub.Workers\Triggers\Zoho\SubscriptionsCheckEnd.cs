﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.Zoho;

public class SubscriptionsCheckEnd
{
    private readonly IZohoSubscriptionRepository _zohoSubscriptionRepository;

    public SubscriptionsCheckEnd(
        IZohoSubscriptionRepository zohoSubscriptionRepository)
    {
        _zohoSubscriptionRepository = zohoSubscriptionRepository;
    }

    public class SubscriptionsCheckEndInput
    {
        [JsonProperty("subscription")]
        [Required]
        public ZohoSubscription Subscription { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset? LastObjectModificationTime { get; set; }

        [JsonProperty("last_execution_start_time")]
        [Required]
        public DateTimeOffset LastExecutionStartTime { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckEndInput(
            ZohoSubscription subscription,
            DateTimeOffset? lastObjectModificationTime,
            DateTimeOffset lastExecutionStartTime)
        {
            Subscription = subscription;
            LastObjectModificationTime = lastObjectModificationTime;
            LastExecutionStartTime = lastExecutionStartTime;
        }
    }

    [Function("Zoho_SubscriptionsCheck_End")]
    public async Task End(
        [ActivityTrigger]
        SubscriptionsCheckEndInput subscriptionsCheckEndInput)
    {
        var patchOperations = new List<PatchOperation>
        {
            PatchOperation.Replace(
                $"/{ZohoSubscription.PropertyNameDurablePayload}",
                (HttpManagementPayload?) null),
            PatchOperation.Replace(
                $"/{ZohoSubscription.PropertyNameLastExecutionStartTime}",
                subscriptionsCheckEndInput.LastExecutionStartTime),
        };
        if (subscriptionsCheckEndInput.LastObjectModificationTime != null)
        {
            patchOperations.Add(
                PatchOperation.Replace(
                    $"/{ZohoSubscription.PropertyNameLastObjectModificationTime}",
                    subscriptionsCheckEndInput.LastObjectModificationTime));
        }

        await _zohoSubscriptionRepository.PatchAsync(
            subscriptionsCheckEndInput.Subscription.Id,
            subscriptionsCheckEndInput.Subscription.SleekflowCompanyId,
            patchOperations);
    }
}