using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Utils;
using Sleekflow.JsonConfigs;

namespace Sleekflow.CrmHub.Tests;

public class JTokenUtilsTests
{
    [SetUp]
    public void Setup()
    {
        // Method intentionally left empty.
    }

    [Test]
    public void Test1()
    {
        var deserializeObject1 = JsonConvert.DeserializeObject(
            "{\"date\":\"2022-09-08T06:05:39.123Z\"}",
            JsonConfig.DefaultJsonSerializerSettings) as JObject;
        var deserializeObject2 = JsonConvert.DeserializeObject(
            "{\"date\":\"2022-09-08T06:05:39.124Z\"}",
            JsonConfig.DefaultJsonSerializerSettings) as JObject;

        Assert.That(deserializeObject1!.GetValue("date"), Is.Not.Null);
        Assert.That(deserializeObject2!.GetValue("date"), Is.Not.Null);

        var deepEquals =
            JTokenUtils.DeepEquals(deserializeObject1.GetValue("date"), deserializeObject2.GetValue("date"));

        Assert.That(deepEquals, Is.EqualTo(true));
    }
}