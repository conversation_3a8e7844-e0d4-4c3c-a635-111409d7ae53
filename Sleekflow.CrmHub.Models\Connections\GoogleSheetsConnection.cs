﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Connections;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("google_sheets_connection")]
public class GoogleSheetsConnection : Entity, IHasSleekflowCompanyId
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("authentication_id")]
    public string AuthenticationId { get; set; }

    [JsonProperty("organization_id")]
    public string OrganizationId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("is_active")]
    public bool IsActive { get; set; }

    [JsonConstructor]
    public GoogleSheetsConnection(
        string id,
        string sleekflowCompanyId,
        string authenticationId,
        string organizationId,
        string name,
        bool isActive)
        : base(id, "Connection")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        AuthenticationId = authenticationId;
        OrganizationId = organizationId;
        Name = name;
        IsActive = isActive;
    }
}