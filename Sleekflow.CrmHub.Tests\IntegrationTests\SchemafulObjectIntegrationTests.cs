﻿using Microsoft.Azure.Cosmos;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.CrmHub.Sequences;
using Sleekflow.CrmHub.Triggers.CrmHubConfigs;
using Sleekflow.CrmHub.Triggers.SchemafulObjects;
using Sleekflow.CrmHub.Triggers.Schemas;
using Sleekflow.Exceptions;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubDb;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Tests.IntegrationTests;

public class SchemafulObjectIntegrationTests
{
    private static readonly string MockCompanyId = $"aaaabbbb-cccc-dddd-eeee-{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString()[..12]}";
    private const string CreateAuditSource = "integration_test_creation";
    private const string UpdateAuditSource = "integration_test_update";

    private SchemaDto MockSchema;
    private CrmHubConfig MockCrmHubConfig;

    private string MockSingleLineTextPropertyId;
    private string MockNumericPropertyId;
    private string MockDecimalPropertyId;
    private string MockSingleChoicePropertyId;
    private string MockMultipleChoicePropertyId;
    private string MockBooleanPropertyId;
    private string MockDatePropertyId;
    private string MockDateTimePropertyId;

    private List<string> MockSingleChoiceOptionIds;
    private List<string> MockMultipleChoiceOptionIds;

    [SetUp]
    public async Task TestSetUp()
    {
        MockCrmHubConfig = await CreateCrmHubConfig();
        MockSchema = await CreateSchema();

        MockSingleLineTextPropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test SingleLineText").Id;
        MockNumericPropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test Numeric" ).Id;
        MockDecimalPropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test Decimal" ).Id;
        MockSingleChoicePropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test SingleChoice" ).Id;
        MockMultipleChoicePropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test MultipleChoice" ).Id;
        MockBooleanPropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test Boolean" ).Id;
        MockDatePropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test Date" ).Id;
        MockDateTimePropertyId = MockSchema.Properties.First(p => p.DisplayName == "Test DateTime" ).Id;

        MockSingleChoiceOptionIds = MockSchema.Properties.First(p => p.DisplayName == "Test SingleChoice")
            .Options!.Select(o => o.Id).ToList();
        MockMultipleChoiceOptionIds = MockSchema.Properties.First(p => p.DisplayName == "Test MultipleChoice")
            .Options!.Select(o => o.Id).ToList();
    }

    [TearDown]
    public async Task TearDown()
    {
        var schemaRepository = GetSchemaRepository();

        var schemas = await schemaRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId")
                .WithParameter("@sleekflowCompanyId", MockCompanyId));

        foreach (var schema in schemas)
        {
            await schemaRepository.DeleteAsync(
                schema.Id,
                MockCompanyId);
        }

        var schemafulObjectRepository = GetSchemafulObjectRepository();

        var schemafulObjects = await schemafulObjectRepository.GetObjectsAsync(
            new QueryDefinition(
                    "SELECT * " +
                    "FROM %%CONTAINER_NAME%% c " +
                    "WHERE c.sleekflow_company_id = @sleekflowCompanyId")
                .WithParameter("@sleekflowCompanyId", MockCompanyId));

        foreach (var schemafulObject in schemafulObjects)
        {
            await schemafulObjectRepository.DeleteAsync(
                schemafulObject.Id,
                new PartitionKeyBuilder()
                    .Add(MockCompanyId)
                    .Add(MockSchema.Id)
                    .Add(schemafulObject.Id)
                    .Build());
        }

        var sequenceRepository = GetSequenceRepository();
        await sequenceRepository.DeleteAsync(MockSchema.Id, MockSchema.Id);

        var crmHubConfigRepository = GetCrmHubConfigRepository();
        await crmHubConfigRepository.DeleteAsync(MockCrmHubConfig.Id, MockCompanyId);
    }

    [Test]
    public async Task SchemafulObjectLifeCycleTest()
    {
        // /SchemafulObjects/CreateSchemafulObject
        var createSchemafulObjectInput = new CreateSchemafulObject.CreateSchemafulObjectInput(
            MockSchema.Id,
            MockCompanyId,
            string.Empty,
            new Dictionary<string, object?>
            {
                {
                    MockSingleLineTextPropertyId, "meaningless.."
                },
                {
                    MockNumericPropertyId, 1234
                },
                {
                    MockDecimalPropertyId, 12.34
                },
                {
                    MockSingleChoicePropertyId, MockSingleChoiceOptionIds[0]
                },
                {
                    MockMultipleChoicePropertyId, new List<string>
                    {
                        MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[1]
                    }
                },
                {
                    MockBooleanPropertyId, true
                },
                {
                    MockDatePropertyId, DateTimeOffset.UtcNow
                },
                {
                    MockDateTimePropertyId, DateTimeOffset.UtcNow
                }
            },
            "mock_user_profile_id",
            "123",
            null,
            CreateAuditSource);

        var createSchemafulObjectScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemafulObjectInput).ToUrl("/SchemafulObjects/CreateSchemafulObject");
            });

        var createSchemaOutput =
            await createSchemafulObjectScenarioResult.ReadAsJsonAsync<
                Output<CreateSchemafulObject.CreateSchemafulObjectOutput>>();

        Assert.That(createSchemaOutput, Is.Not.Null);
        Assert.That(createSchemaOutput!.HttpStatusCode, Is.EqualTo(200));

        // /SchemafulObjects/GetSchemafulObject
        var getSchemafulObject = new GetSchemafulObject.GetSchemafulObjectInput(
            MockCompanyId,
            MockSchema.Id,
            createSchemaOutput.Data.SchemafulObject.Id);

        var getSchemafulObjectResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObject).ToUrl("/SchemafulObjects/GetSchemafulObject");
            });

        var getSchemafulObjectOutput =
            await getSchemafulObjectResult.ReadAsJsonAsync<
                Output<GetSchemafulObject.GetSchemafulObjectOutput>>();

        Assert.That(getSchemafulObjectOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectOutput!.HttpStatusCode, Is.EqualTo(200));

        var schemafulObject = getSchemafulObjectOutput.Data.SchemafulObject;
        Assert.That(schemafulObject.PrimaryPropertyValue, Is.EqualTo("LOL0000001"));
        Assert.That(schemafulObject.PropertyValues.Count, Is.EqualTo(8));
        Assert.That(schemafulObject.PropertyValues[MockSingleLineTextPropertyId], Is.EqualTo("meaningless.."));
        Assert.That(schemafulObject.PropertyValues[MockNumericPropertyId], Is.EqualTo(1234));
        Assert.That(schemafulObject.PropertyValues[MockDecimalPropertyId], Is.EqualTo(12.34));
        Assert.That(
            schemafulObject.PropertyValues[MockSingleChoicePropertyId],
            Is.EqualTo(MockSingleChoiceOptionIds[0]));
        Assert.That(
            schemafulObject.PropertyValues[MockMultipleChoicePropertyId] is JArray jArray &&
            jArray.ToObject<List<string>>()!.TrueForAll(
                v => new List<string>
                {
                    MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[1]
                }.Contains(v)),
            Is.True);
        Assert.That(schemafulObject.PropertyValues[MockBooleanPropertyId], Is.EqualTo(true));
        Assert.That(schemafulObject.CreatedVia, Is.EqualTo(CreateAuditSource));

        // /SchemafulObjects/UpdateSchemafulObject
        var updatedPropertyValues = schemafulObject.PropertyValues;
        var updatedTime = DateTimeOffset.UtcNow;
        updatedPropertyValues[MockSingleLineTextPropertyId] = "updated...";
        updatedPropertyValues[MockDecimalPropertyId] = null;
        updatedPropertyValues[MockBooleanPropertyId] = false;
        updatedPropertyValues[MockDatePropertyId] = updatedTime;

        var updateSchemafulObjectInput = new UpdateSchemafulObject.UpdateSchemafulObjectInput(
            schemafulObject.Id,
            MockSchema.Id,
            MockCompanyId,
            updatedPropertyValues,
            "mock_another_user_profile_id",
            "456",
            null,
            UpdateAuditSource);

        var updateSchemafulObjectResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(updateSchemafulObjectInput).ToUrl("/SchemafulObjects/UpdateSchemafulObject");
            });

        var updateSchemafulObjectOutput =
            await updateSchemafulObjectResult.ReadAsJsonAsync<
                Output<UpdateSchemafulObject.UpdateSchemafulObjectOutput>>();

        Assert.That(updateSchemafulObjectOutput, Is.Not.Null);
        Assert.That(updateSchemafulObjectOutput!.HttpStatusCode, Is.EqualTo(200));

        schemafulObject = updateSchemafulObjectOutput.Data.SchemafulObject;
        Assert.That(schemafulObject.SleekflowUserProfileId, Is.EqualTo("mock_another_user_profile_id"));
        Assert.That(schemafulObject.UpdatedBy!.SleekflowStaffId, Is.EqualTo("456"));
        Assert.That(schemafulObject.PropertyValues[MockSingleLineTextPropertyId], Is.EqualTo("updated..."));
        Assert.That(schemafulObject.PropertyValues[MockDecimalPropertyId], Is.Null);
        Assert.That(schemafulObject.PropertyValues[MockBooleanPropertyId], Is.EqualTo(false));
        Assert.That(
            schemafulObject.PropertyValues[MockDatePropertyId].ToDateTimeOffset()!.Value.ToString("u"),
            Is.EqualTo(updatedTime.ToString("u")));
        Assert.That(schemafulObject.CreatedVia, Is.EqualTo(CreateAuditSource));
        Assert.That(schemafulObject.UpdatedVia, Is.EqualTo(UpdateAuditSource));

        // /SchemafulObjects/DeleteSchemafulObjects
        var deleteSchemafulObjectsInput = new DeleteSchemafulObjects.DeleteSchemafulObjectsInput(
            new List<string>
            {
                schemafulObject.Id
            },
            MockSchema.Id,
            MockCompanyId);

        var deleteSchemafulObjectsResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteSchemafulObjectsInput).ToUrl("/SchemafulObjects/DeleteSchemafulObjects");
            });

        var deleteSchemafulObjectsOutput =
            await deleteSchemafulObjectsResult.ReadAsJsonAsync<
                Output<DeleteSchemafulObjects.DeleteSchemafulObjectsOutput>>();

        Assert.That(deleteSchemafulObjectsOutput, Is.Not.Null);
        Assert.That(deleteSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(deleteSchemafulObjectsOutput.Data.DeletedCount, Is.EqualTo(1));
    }

    [Test]
    public async Task SchemafulObjectUsageLimitTests()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Tests require Task.Delay() several minutes. Skipped in Github Action.");
        }

        // scenario:
        // UsageLimit.CustomObjectMaximumSchemafulObjectNumPerCompany = 11
        var createSchemafulObjectInput = new CreateSchemafulObject.CreateSchemafulObjectInput(
            MockSchema.Id,
            MockCompanyId,
            string.Empty,
            new Dictionary<string, object?>
            {
                {
                    MockSingleLineTextPropertyId, "meaningless.."
                },
                {
                    MockNumericPropertyId, 1234
                },
                {
                    MockDecimalPropertyId, 12.34
                },
                {
                    MockSingleChoicePropertyId, MockSingleChoiceOptionIds[0]
                },
                {
                    MockMultipleChoicePropertyId, new List<string>
                    {
                        MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[1]
                    }
                },
                {
                    MockBooleanPropertyId, true
                },
                {
                    MockDatePropertyId, DateTimeOffset.UtcNow
                },
                {
                    MockDateTimePropertyId, DateTimeOffset.UtcNow
                }
            },
            "mock_user_profile_id",
            "123",
            null,
            CreateAuditSource);

        // create 10 record
        await CreateSchemafulObjects();

        // create 11th record
        string schemafulObjectId;
        var createSchemafulObjectResult11 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemafulObjectInput).ToUrl("/SchemafulObjects/CreateSchemafulObject");
            });

        var createSchemafulObjectOutput11 = await createSchemafulObjectResult11.ReadAsJsonAsync<
            Output<CreateSchemafulObject.CreateSchemafulObjectOutput>>();

        Assert.That(createSchemafulObjectOutput11, Is.Not.Null);
        Assert.That(createSchemafulObjectOutput11!.HttpStatusCode, Is.EqualTo(200));
        schemafulObjectId = createSchemafulObjectOutput11.Data.SchemafulObject.Id;

        // await cache expired
        await Task.Delay(TimeSpan.FromMinutes(2));

        // create 12th record
        createSchemafulObjectInput.SleekflowUserProfileId = "mock_user_profile_id_12th";
        var createSchemafulObjectResult12 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemafulObjectInput).ToUrl("/SchemafulObjects/CreateSchemafulObject");
            });

        var createSchemafulObjectOutput12 = await createSchemafulObjectResult12.ReadAsJsonAsync<
            Output<CreateSchemafulObject.CreateSchemafulObjectOutput>>();

        Assert.That(createSchemafulObjectOutput12, Is.Not.Null);
        Assert.That(createSchemafulObjectOutput12!.HttpStatusCode, Is.EqualTo(500));
        Assert.That(createSchemafulObjectOutput12.ErrorCode, Is.EqualTo(ErrorCodeConstant.SfCrmHubExceedUsageException));

        // delete 1 record
        await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(
                        new DeleteSchemafulObjects.DeleteSchemafulObjectsInput(
                            new List<string>
                            {
                                schemafulObjectId
                            },
                            MockSchema.Id,
                            MockCompanyId))
                    .ToUrl("/SchemafulObjects/DeleteSchemafulObjects");
            });

        // await cache expired
        await Task.Delay(TimeSpan.FromMinutes(2));

        // create again
        var createSchemafulObjectResult11_2 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemafulObjectInput).ToUrl("/SchemafulObjects/CreateSchemafulObject");
            });

        var createSchemafulObjectOutput11_2 = await createSchemafulObjectResult11_2.ReadAsJsonAsync<
            Output<CreateSchemafulObject.CreateSchemafulObjectOutput>>();

        Assert.That(createSchemafulObjectOutput11_2, Is.Not.Null);
        Assert.That(createSchemafulObjectOutput11_2!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task SchemafulObjectFilterTests()
    {
        await CreateSchemafulObjects();

        // continuationToken test
        var getSchemafulObjects = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            5,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>
            {
                new SchemafulObjectQueryBuilder.SchemafulObjectSort(
                    MockNumericPropertyId,
                    "asc",
                    true)
            },
            null);

        var getSchemafulObjectsResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjects).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsOutput =
            await getSchemafulObjectsResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsOutput.Data.SchemafulObjects.Count, Is.EqualTo(5));
        Assert.That(
            getSchemafulObjectsOutput.Data.SchemafulObjects[0].SleekflowUserProfileId,
            Is.EqualTo("mock_user_profile_id_0"));

        var primaryPropertyValues = getSchemafulObjectsOutput.Data.SchemafulObjects.Select(so => so.PrimaryPropertyValue).ToList();

        var getSchemafulObjectsWithContinuationToken = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            getSchemafulObjectsOutput.Data.ContinuationToken,
            5,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>(),
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>
            {
                new SchemafulObjectQueryBuilder.SchemafulObjectSort(
                    MockNumericPropertyId,
                    "asc",
                    true)
            },
            null);

        var getSchemafulObjectsWithContinuationTokenResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsWithContinuationToken).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsWithContinuationTokenOutput =
            await getSchemafulObjectsWithContinuationTokenResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsWithContinuationTokenOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsWithContinuationTokenOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsWithContinuationTokenOutput.Data.SchemafulObjects.Count, Is.EqualTo(5));
        Assert.That(
            getSchemafulObjectsWithContinuationTokenOutput.Data.SchemafulObjects[0].SleekflowUserProfileId,
            Is.EqualTo("mock_user_profile_id_5"));

        primaryPropertyValues.AddRange(
            getSchemafulObjectsWithContinuationTokenOutput.Data.SchemafulObjects.Select(so => so.PrimaryPropertyValue).ToList());
        primaryPropertyValues.Sort();
        Assert.That(
            primaryPropertyValues,
            Is.EquivalentTo(Enumerable.Range(1, 10)
                    .Select(i => "LOL" + i.ToString().PadLeft(7, '0'))
                    .ToList()));

        // operator: >=
        // decimal
        var getSchemafulObjectsEqualGreaterThanDecimal = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDecimalPropertyId,
                            ">=",
                            2.2,
                            true)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            null);

        var getSchemafulObjectsEqualGreaterThanDecimalResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsEqualGreaterThanDecimal).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsEqualGreaterThanDecimalOutput =
            await getSchemafulObjectsEqualGreaterThanDecimalResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsEqualGreaterThanDecimalOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsEqualGreaterThanDecimalOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsEqualGreaterThanDecimalOutput.Data.SchemafulObjects.Count, Is.EqualTo(8));

        var countSchemafulObjects = new CountSchemafulObjects.CountSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            true,
            new List<CountSchemafulObjects.CountSchemafulObjectsFilterGroup>
            {
                new CountSchemafulObjects.CountSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDecimalPropertyId,
                            ">=",
                            2.2,
                            true)
                    }),
            },
            null);

        var countSchemafulObjectsResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(countSchemafulObjects).ToUrl("/SchemafulObjects/CountSchemafulObjects");
            });

        var countSchemafulObjectsOutput =
            await countSchemafulObjectsResult.ReadAsJsonAsync<
                Output<CountSchemafulObjects.CountSchemafulObjectsOutput>>();

        Assert.That(countSchemafulObjectsOutput, Is.Not.Null);
        Assert.That(countSchemafulObjectsOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(countSchemafulObjectsOutput.Data.Count, Is.EqualTo(8));

        // operator: <=
        // date time
        var getSchemafulObjectsLessEqualThanDateTime = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDateTimePropertyId,
                            "<=",
                            new DateTimeOffset(2023, 5, 31, 0, 0, 0, TimeSpan.Zero),
                            true)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            null);

        var getSchemafulObjectsLessEqualThanDateTimeResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsLessEqualThanDateTime).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsLessEqualThanDateTimeOutput =
            await getSchemafulObjectsLessEqualThanDateTimeResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsLessEqualThanDateTimeOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsLessEqualThanDateTimeOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsLessEqualThanDateTimeOutput.Data.SchemafulObjects.Count, Is.EqualTo(5));

        // operator: value is null
        var getSchemafulObjectsEqualToNull = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockBooleanPropertyId,
                            "=",
                            null,
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockBooleanPropertyId,
                            "=",
                            string.Empty,
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockBooleanPropertyId,
                            "isDefined",
                            false,
                            true)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            null);

        var getSchemafulObjectsEqualToNullResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsEqualToNull).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsEqualToNullOutput =
            await getSchemafulObjectsEqualToNullResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsEqualToNullOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsEqualToNullOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsEqualToNullOutput.Data.SchemafulObjects.Count, Is.EqualTo(5));
        Assert.That(
            getSchemafulObjectsEqualToNullOutput.Data.SchemafulObjects
                .Exists(so => !so.PropertyValues.ContainsKey(MockBooleanPropertyId) ||
                              so.PropertyValues[MockBooleanPropertyId] is null),
            Is.True);

        // operator: contains
        var getSchemafulObjectsContains = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockSingleLineTextPropertyId,
                            "contains",
                            "is 8",
                            true),
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockSingleLineTextPropertyId,
                            "contains",
                            "is 9",
                            true)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            null);

        var getSchemafulObjectsContainsResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsContains).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsContainsOutput =
            await getSchemafulObjectsContainsResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsContainsOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsContainsOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsContainsOutput.Data.SchemafulObjects.Count, Is.EqualTo(2));

        // operator: arrayContains
        var getSchemafulObjectsArrayContains = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockMultipleChoicePropertyId,
                            "arrayContains",
                            MockMultipleChoiceOptionIds[1],
                            true)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            null);

        var getSchemafulObjectsArrayContainsResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsArrayContains).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsArrayContainsOutput =
            await getSchemafulObjectsArrayContainsResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsArrayContainsOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsArrayContainsOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsArrayContainsOutput.Data.SchemafulObjects.Count, Is.EqualTo(3));

        // operater: in
        // /SchemafulObjects/GetSchemafulObjects
        var getSchemafulObjectsIn = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            5,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId,
                            "in",
                            new List<string>
                            {
                                "mock_user_profile_id_0",
                                "mock_user_profile_id_1",
                                "mock_user_profile_id_2"
                            },
                            false)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>
            {
                new SchemafulObjectQueryBuilder.SchemafulObjectSort(
                    MockNumericPropertyId,
                    "asc",
                    true)
            },
            null);

        var getSchemafulObjectsInResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsIn).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsInOutput =
            await getSchemafulObjectsInResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsInOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsInOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsInOutput.Data.SchemafulObjects.Count, Is.EqualTo(3));
        Assert.That(
            getSchemafulObjectsInOutput.Data.SchemafulObjects.Exists(
                so => so.SleekflowUserProfileId == "mock_user_profile_id_0"),
            Is.True);
        Assert.That(
            getSchemafulObjectsInOutput.Data.SchemafulObjects.Exists(
                so => so.SleekflowUserProfileId == "mock_user_profile_id_3"),
            Is.False);

        // operator: startsWith
        // Date time
        var getSchemafulObjectsStartsWithDateTime = new GetSchemafulObjects.GetSchemafulObjectsInput(
            MockCompanyId,
            MockSchema.Id,
            null,
            10,
            true,
            new List<GetSchemafulObjects.GetSchemafulObjectsFilterGroup>
            {
                new GetSchemafulObjects.GetSchemafulObjectsFilterGroup(
                    new List<SchemafulObjectQueryBuilder.SchemafulObjectFilter>
                    {
                        new SchemafulObjectQueryBuilder.SchemafulObjectFilter(
                            MockDateTimePropertyId,
                            "startsWith",
                            "2023-03",
                            true)
                    })
            },
            new List<SchemafulObjectQueryBuilder.SchemafulObjectSort>(),
            null);

        var getSchemafulObjectsStartsWithDateTimeResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getSchemafulObjectsStartsWithDateTime).ToUrl("/SchemafulObjects/GetSchemafulObjects");
            });

        var getSchemafulObjectsStartsWithDateTimeOutput =
            await getSchemafulObjectsStartsWithDateTimeResult.ReadAsJsonAsync<
                Output<GetSchemafulObjects.GetSchemafulObjectsOutput>>();

        Assert.That(getSchemafulObjectsStartsWithDateTimeOutput, Is.Not.Null);
        Assert.That(getSchemafulObjectsStartsWithDateTimeOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(getSchemafulObjectsStartsWithDateTimeOutput.Data.SchemafulObjects.Count, Is.EqualTo(1));
    }

    private async Task CreateSchemafulObjects()
    {
        for (int i = 0; i < 10; i++)
        {
            // /SchemafulObjects/CreateSchemafulObject
            var createSchemafulObjectInput = new CreateSchemafulObject.CreateSchemafulObjectInput(
                MockSchema.Id,
                MockCompanyId,
                string.Empty,
                new Dictionary<string, object?>
                {
                    {
                        MockSingleLineTextPropertyId, $"This is {i}"
                    },
                    {
                        MockNumericPropertyId, i
                    },
                    {
                        MockDecimalPropertyId, i * 1.1
                    },
                    {
                        MockSingleChoicePropertyId, MockSingleChoiceOptionIds[0]
                    },
                    {
                        MockMultipleChoicePropertyId,
                        new List<string>
                        {
                            MockMultipleChoiceOptionIds[0], MockMultipleChoiceOptionIds[i < 3 ? 1 : 2]
                        }
                    },
                    {
                        MockBooleanPropertyId, i < 3 ? true : i < 5 ? false : null
                    },
                    {
                        MockDatePropertyId, new DateTimeOffset(2023, i + 1, i + 1, 16, 0, 0, TimeSpan.Zero)
                    },
                    {
                        MockDateTimePropertyId, new DateTimeOffset(2023, i + 1, i + 1, i + 1, i, i, TimeSpan.Zero)
                    }
                },
                $"mock_user_profile_id_{i}",
                "123",
                null,
                CreateAuditSource);

            if (i > 8)
            {
                // For 'not required' properties, their value could be null, or just not ignored in the input.
                createSchemafulObjectInput.PropertyValues.Remove(MockBooleanPropertyId);
            }

            await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createSchemafulObjectInput).ToUrl("/SchemafulObjects/CreateSchemafulObject");
                });
        }
    }

    private async Task<SchemaDto> CreateSchema()
    {
        var mockSleekflowStaff = new AuditEntity.SleekflowStaff("mocked-staff-id", null);

        var mockPropertyInputs = new List<PropertyInput>
        {
            new PropertyInput(
                "Test SingleLineText",
                "test_single_line_text",
                new SingleLineTextDataType(),
                false,
                true,
                true,
                true,
                true,
                1,
                mockSleekflowStaff,
                null),
            new PropertyInput(
                "Test Numeric",
                "test_numeric",
                new NumericDataType(),
                false,
                true,
                true,
                true,
                true,
                2,
                mockSleekflowStaff,
                null),
            new PropertyInput(
                "Test Decimal",
                "test_decimal",
                new DecimalDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                3,
                mockSleekflowStaff,
                null),
            new PropertyInput(
                "Test SingleChoice",
                "test_single_choice",
                new SingleChoiceDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                4,
                null,
                new List<Option>
                {
                    new Option(string.Empty, "Male", 0),
                    new Option(string.Empty, "Female", 1)
                }),
            new PropertyInput(
                "Test MultipleChoice",
                "test_multiple_choice",
                new MultipleChoiceDataType(),
                false,
                true,
                false,
                false,
                true,
                5,
                null,
                new List<Option>
                {
                    new Option(string.Empty, "aaa", 0),
                    new Option(string.Empty, "bbb", 1),
                    new Option(string.Empty, "ccc", 2)
                }),
            new PropertyInput(
                "Test Boolean",
                "test_boolean",
                new BooleanDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                6,
                mockSleekflowStaff,
                null),
            new PropertyInput(
                "Test Date",
                "test_date",
                new DateDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                7,
                mockSleekflowStaff,
                null),
            new PropertyInput(
                "Test DateTime",
                "test_date_time",
                new DateTimeDataType(),
                false,
                false, // set to not required
                true,
                true,
                true,
                8,
                mockSleekflowStaff,
                null),
        };

        var mockPrimaryPropertyInput = new PrimaryPropertyInput(
            "Primary Property",
            "primary_property",
            new SingleLineTextDataType(),
            true,
            true,
            true,
            new PrimaryPropertyConfig(true, new Sequential("LOL", 10)),
            null);

        var schemaAccessibilitySettings = new SchemaAccessibilitySettings("custom");

        // /Schemas/CreateSchema
        var createSchemaInput = new CreateSchema.CreateSchemaInput(
            MockCompanyId,
            $"Test Schema {DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            $"test_schema_{DateTimeOffset.UtcNow.ToUnixTimeMilliseconds()}",
            "one-to-one",
            mockPropertyInputs,
            mockPrimaryPropertyInput,
            schemaAccessibilitySettings);

        var createSchemaScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createSchemaInput).ToUrl("/Schemas/CreateSchema");
            });

        var createSchemaOutput =
            await createSchemaScenarioResult.ReadAsJsonAsync<
                Output<CreateSchema.CreateSchemaOutput>>();

        return createSchemaOutput!.Data.Schema;
    }

    private async Task<CrmHubConfig> CreateCrmHubConfig()
    {
        var initializeCrmHubConfigInput = new InitializeCrmHubConfig.InitializeCrmHubConfigInput(
            MockCompanyId,
            new UsageLimit(100, 100, 100, 11, 10),
            null,
            "3880",
            null);

        var initializeCrmHubConfigScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(initializeCrmHubConfigInput).ToUrl("/CrmHubConfigs/InitializeCrmHubConfig");
            });

        var initializeCrmHubConfigOutput =
            await initializeCrmHubConfigScenarioResult.ReadAsJsonAsync<
                Output<InitializeCrmHubConfig.InitializeCrmHubConfigOutput>>();

        return initializeCrmHubConfigOutput!.Data.CrmHubConfig;
    }

    private SchemaRepository GetSchemaRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        return new SchemaRepository(
            NullLogger<SchemaRepository>.Instance,
            serviceProvider);
    }

    private SchemafulObjectRepository GetSchemafulObjectRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        return new SchemafulObjectRepository(
            NullLogger<SchemafulObjectRepository>.Instance,
            serviceProvider);
    }

    private SequenceRepository GetSequenceRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        return new SequenceRepository(
            NullLogger<SequenceRepository>.Instance,
            serviceProvider);
    }

    private CrmHubConfigRepository GetCrmHubConfigRepository()
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton<IPersistenceRetryPolicyService>(
            new PersistenceRetryPolicyService(NullLogger<PersistenceRetryPolicyService>.Instance));
        serviceCollection.AddSingleton<ICrmHubDbResolver>(
            new CrmHubDbResolver(new MyCrmHubDbConfig()));
        var serviceProvider = serviceCollection.BuildServiceProvider();

        var crmHubConfigRepository = new CrmHubConfigRepository(
            NullLogger<CrmHubConfigRepository>.Instance,
            serviceProvider);

        return crmHubConfigRepository;
    }

    private class MyCrmHubDbConfig : ICrmHubDbConfig
    {
        public string Endpoint { get; }
            = "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key { get; } =
            "****************************************************************************************";

        public string DatabaseId { get; } =
            "crmhubdb";
    }
}