using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;

public class ManagementWhatsappCloudApiConversationUsageAnalytic
{
    [JsonProperty("summarized_conversation_usage_analytic")]
    public WhatsappCloudApiConversationUsageAnalyticDto SummarizedConversationUsageAnalytic { get; set; }

    [JsonProperty("facebook_business_id")]
    public string FacebookBusinessId { get; set; }

    [JsonProperty("facebook_business_name")]
    public string? FacebookBusinessName { get; set; }

    [JsonProperty("waba_conversation_usage_analytics")]
    public List<WhatsappCloudApiWabaConversationUsageAnalytic> WabaConversationUsageAnalytics { get; set; }

    [JsonConstructor]
    public ManagementWhatsappCloudApiConversationUsageAnalytic(
        WhatsappCloudApiConversationUsageAnalyticDto summarizedConversationUsageAnalytic,
        string facebookBusinessId,
        string? facebookBusinessName,
        List<WhatsappCloudApiWabaConversationUsageAnalytic> wabaConversationUsageAnalytics)
    {
        SummarizedConversationUsageAnalytic = summarizedConversationUsageAnalytic;
        FacebookBusinessId = facebookBusinessId;
        FacebookBusinessName = facebookBusinessName;
        WabaConversationUsageAnalytics = wabaConversationUsageAnalytics;
    }
}