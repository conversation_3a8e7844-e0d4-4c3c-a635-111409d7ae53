﻿using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Processors.ChangeFeedHandlers.Abstractions;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Processors.ChangeFeedHandlers;

public interface IEntityChangeFeedHandler : IChangeFeedHandler
{
}

public class EntityChangeFeedHandler : IEntityChangeFeedHandler, ISingletonService
{
    private readonly IBus _bus;

    public EntityChangeFeedHandler(IBus bus)
    {
        _bus = bus;
    }

    public string GetContainerName()
    {
        return "entity";
    }

    public virtual async Task OnChangesDelegate(
        ChangeFeedProcessorContext context,
        IReadOnlyCollection<Dictionary<string, object?>> changes,
        CancellationToken cancellationToken)
    {
        var changesGroupings = changes
            .GroupBy(c => (string) c[CrmHubEntityContext.PropertyNameSysSleekflowCompanyId]!)
            .ToList();

        foreach (var grouping in changesGroupings)
        {
            var sleekflowCompanyId = grouping.Key;

            var events = grouping
                .Select(
                    dict =>
                    {
                        var onObjectPersistedEvent = new OnObjectPersistedEvent(
                            dict,
                            sleekflowCompanyId,
                            (string) dict[CrmHubEntityContext.PropertyNameId]!,
                            (string) dict[CrmHubEntityContext.PropertyNameSysEntityTypeName]!);

                        return onObjectPersistedEvent;
                    })
                .ToList();

            await _bus.PublishBatch(
                events,
                publishContext => { publishContext.ConversationId = Guid.Parse(sleekflowCompanyId); },
                cancellationToken: cancellationToken);
        }
    }
}