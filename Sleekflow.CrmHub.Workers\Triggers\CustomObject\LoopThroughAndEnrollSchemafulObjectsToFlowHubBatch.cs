﻿using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Workers;
using Sleekflow.CrmHub.Workers.Configs;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CrmHub.Workers.Triggers.CustomObject;

public class LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    [Function("LoopThroughAndEnrollSchemafulObjectsToFlowHub_Batch")]
    public async Task<LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput> Batch(
        [ActivityTrigger]
        LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput loopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput)
    {
        var inputJsonStr = JsonConvert.SerializeObject(loopThroughAndEnrollSchemafulObjectsToFlowHubBatchInput, JsonConfig.DefaultJsonSerializerSettings);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.CrmHubInternalsEndpoint + "/LoopThroughAndEnrollSchemafulObjectsToFlowHubBatch"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput>()!;
    }
}