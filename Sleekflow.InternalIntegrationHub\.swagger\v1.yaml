openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7074
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/internal-integration-hub/
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/internal-integration-hub/
    description: Prod Apigw
paths:
  /NetSuite/External/CreatePayment:
    post:
      tags:
        - NetSuiteExternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentOutputOutput'
  /NetSuite/External/GetPayment:
    post:
      tags:
        - NetSuiteExternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPaymentInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetPaymentOutputOutput'
  /NetSuite/Internal/CreateCreditMemo:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCreditMemoInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCreditMemoOutputOutput'
  /NetSuite/Internal/CreateCustomer:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateCustomerOutputOutput'
  /NetSuite/Internal/CreateEmployeeManually:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateEmployeeManuallyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateEmployeeManuallyOutputOutput'
  /NetSuite/Internal/CreateInvoice:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInvoiceInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateInvoiceOutputOutput'
  /NetSuite/Internal/CreateInvoiceBatch:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateInvoiceBatchInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateInvoiceBatchOutputOutput'
  /NetSuite/Internal/CreatePaymentFromStripe:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePaymentFromStripeInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreatePaymentFromStripeOutputOutput'
  /NetSuite/Internal/CreateRefund:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRefundInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateRefundOutputOutput'
  /NetSuite/Internal/GenerateImportEmployeeCsv:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GenerateImportEmployeeCsvInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenerateImportEmployeeCsvOutputOutput'
  /NetSuite/Internal/SyncInvoiceByCompanyIds:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncInvoiceByCompanyIdsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncInvoiceByCompanyIdsOutputOutput'
  /NetSuite/Internal/UpdateCustomer:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCustomerInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateCustomerOutputOutput'
  /NetSuite/Internal/UpdateEmployeeManually:
    post:
      tags:
        - NetSuiteInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmployeeManuallyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpdateEmployeeManuallyOutputOutput'
  /OmniHr/Internal/SyncEmployees:
    post:
      tags:
        - OmniHrInternal
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SyncEmployeesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncEmployeesOutputOutput'
components:
  schemas:
    BillItem:
      required:
        - amount
        - bill_record_id
      type: object
      properties:
        bill_record_id:
          minLength: 1
          type: string
        amount:
          minimum: 0
          type: number
          format: decimal
      additionalProperties: false
    CreateCreditMemoInput:
      required:
        - amount
        - company_id
        - invoice_external_id
        - item_name
      type: object
      properties:
        amount:
          minimum: 0
          type: number
          format: decimal
        company_id:
          minLength: 1
          type: string
        invoice_external_id:
          minLength: 1
          type: string
        item_name:
          minLength: 1
          type: string
      additionalProperties: false
    CreateCreditMemoOutput:
      type: object
      properties:
        external_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateCreditMemoOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateCreditMemoOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateCustomerInput:
      required:
        - company_country
        - company_id
        - company_name
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        company_name:
          minLength: 1
          type: string
        company_country:
          minLength: 1
          type: string
        company_owner_email:
          type: string
          nullable: true
        company_owner_phone:
          type: string
          nullable: true
        sales_rep_email:
          type: string
          nullable: true
      additionalProperties: false
    CreateCustomerOutput:
      type: object
      additionalProperties: false
    CreateCustomerOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateCustomerOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateEmployeeManuallyInput:
      required:
        - employees
      type: object
      properties:
        employees:
          type: array
          items:
            $ref: '#/components/schemas/CreateEmployeeRequest'
      additionalProperties: false
    CreateEmployeeManuallyOutput:
      type: object
      properties:
        success_count:
          type: integer
          format: int32
        failed_employee_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CreateEmployeeManuallyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateEmployeeManuallyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateEmployeeRequest:
      required:
        - defaultExpenseReportCurrency
        - email
        - firstName
        - lastName
        - subsidiary
      type: object
      properties:
        id:
          type: string
          nullable: true
        externalId:
          type: string
          nullable: true
        firstName:
          minLength: 1
          type: string
        lastName:
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
        issalesrep:
          type: boolean
          nullable: true
        supervisor:
          $ref: '#/components/schemas/Supervisor'
        defaultExpenseReportCurrency:
          $ref: '#/components/schemas/DefaultExpenseReportCurrency'
        subsidiary:
          $ref: '#/components/schemas/Subsidiary'
      additionalProperties: false
    CreateInvoiceBatchInput:
      required:
        - company_id
        - is_enable_check_bill_record
        - is_enable_check_whatsapp_credit
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        is_enable_check_bill_record:
          type: boolean
        is_enable_check_whatsapp_credit:
          type: boolean
      additionalProperties: false
    CreateInvoiceBatchOutput:
      type: object
      properties:
        invoice_count:
          type: integer
          format: int32
      additionalProperties: false
    CreateInvoiceBatchOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateInvoiceBatchOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateInvoiceInput:
      required:
        - company_id
        - currency
        - one_time_setup_fee
        - subscription_fee
        - whatsapp_credit_amount
      type: object
      properties:
        external_id:
          type: string
          nullable: true
        company_id:
          minLength: 1
          type: string
        subscription_fee:
          type: number
          format: decimal
        subscription_description:
          type: string
          nullable: true
        one_time_setup_fee:
          type: number
          format: decimal
        one_time_setup_description:
          type: string
          nullable: true
        whatsapp_credit_amount:
          type: number
          format: decimal
        whatsapp_credit_description:
          type: string
          nullable: true
        subscription_start_date:
          type: string
          format: date-time
          nullable: true
        subscription_end_date:
          type: string
          format: date-time
          nullable: true
        payment_term:
          type: integer
          format: int32
          nullable: true
        currency:
          minLength: 1
          type: string
        paid_amount:
          type: number
          format: double
          nullable: true
        created_date:
          type: string
          format: date-time
          nullable: true
      additionalProperties: false
    CreateInvoiceOutput:
      type: object
      properties:
        external_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateInvoiceOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateInvoiceOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreatePaymentFromStripeInput:
      required:
        - company_id
        - currency
        - external_id
        - items
      type: object
      properties:
        external_id:
          minLength: 1
          type: string
        items:
          type: array
          items:
            $ref: '#/components/schemas/BillItem'
        currency:
          minLength: 1
          type: string
        company_id:
          minLength: 1
          type: string
      additionalProperties: false
    CreatePaymentFromStripeOutput:
      type: object
      additionalProperties: false
    CreatePaymentFromStripeOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreatePaymentFromStripeOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreatePaymentInput:
      required:
        - payments
      type: object
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/CreatePaymentRequest'
      additionalProperties: false
    CreatePaymentOutput:
      type: object
      additionalProperties: false
    CreatePaymentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreatePaymentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreatePaymentRequest:
      required:
        - bill_record_id
        - currency
        - customer_id
        - invoice_id
        - one_time_setup_fee
        - payment_date
        - payment_terms
        - sales_rep_id
        - subscription_fee
        - whatsapp_credit_amount
      type: object
      properties:
        customer_id:
          minLength: 1
          type: string
        bill_record_id:
          minLength: 1
          type: string
        subscription_fee:
          type: number
          format: decimal
        one_time_setup_fee:
          type: number
          format: decimal
        whatsapp_credit_amount:
          type: number
          format: decimal
        currency:
          minLength: 1
          type: string
        payment_date:
          type: string
          format: date-time
        invoice_id:
          minLength: 1
          type: string
        payment_terms:
          minLength: 1
          type: string
        sales_rep_id:
          minLength: 1
          type: string
      additionalProperties: false
    CreateRefundInput:
      required:
        - amount
        - company_id
        - credit_memo_external_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        amount:
          minimum: 0
          type: number
          format: decimal
        credit_memo_external_id:
          minLength: 1
          type: string
      additionalProperties: false
    CreateRefundOutput:
      type: object
      additionalProperties: false
    CreateRefundOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateRefundOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DefaultExpenseReportCurrency:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    GenerateImportEmployeeCsvInput:
      type: object
      additionalProperties: false
    GenerateImportEmployeeCsvOutput:
      type: object
      properties:
        csv_content:
          type: string
          nullable: true
        file_name:
          type: string
          nullable: true
        generated_at:
          type: string
          format: date-time
        total_rows:
          type: integer
          format: int32
      additionalProperties: false
    GenerateImportEmployeeCsvOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GenerateImportEmployeeCsvOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetPaymentInput:
      required:
        - bill_record_id
        - customer_id
      type: object
      properties:
        bill_record_id:
          minLength: 1
          type: string
        customer_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetPaymentOutput:
      type: object
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/GetPaymentResponse'
          nullable: true
      additionalProperties: false
    GetPaymentOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetPaymentOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetPaymentResponse:
      required:
        - bill_record_id
        - currency
        - customer_id
        - invoice_id
        - one_time_setup_fee
        - payment_date
        - payment_terms
        - subscription_fee
        - whatsapp_credit_amount
      type: object
      properties:
        customer_id:
          minLength: 1
          type: string
        bill_record_id:
          minLength: 1
          type: string
        subscription_fee:
          type: number
          format: decimal
        one_time_setup_fee:
          type: number
          format: decimal
        whatsapp_credit_amount:
          type: number
          format: decimal
        currency:
          minLength: 1
          type: string
        payment_date:
          type: string
          format: date-time
        invoice_id:
          minLength: 1
          type: string
        payment_terms:
          minLength: 1
          type: string
      additionalProperties: false
    Subsidiary:
      type: object
      properties:
        id:
          type: string
          nullable: true
        refName:
          type: string
          nullable: true
      additionalProperties: false
    Supervisor:
      type: object
      properties:
        id:
          type: string
          nullable: true
      additionalProperties: false
    SyncEmployeesInput:
      type: object
      additionalProperties: false
    SyncEmployeesOutput:
      type: object
      additionalProperties: false
    SyncEmployeesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncEmployeesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    SyncInvoiceByCompanyIdsInput:
      required:
        - check_bill_record
        - check_whatsapp_credit
        - company_ids
      type: object
      properties:
        company_ids:
          type: array
          items:
            type: string
        check_bill_record:
          type: boolean
        check_whatsapp_credit:
          type: boolean
      additionalProperties: false
    SyncInvoiceByCompanyIdsOutput:
      type: object
      properties:
        is_success:
          type: boolean
      additionalProperties: false
    SyncInvoiceByCompanyIdsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/SyncInvoiceByCompanyIdsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateCustomerInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        company_name:
          type: string
          nullable: true
        company_country:
          type: string
          nullable: true
        company_owner_email:
          type: string
          nullable: true
        company_owner_phone:
          type: string
          nullable: true
        sales_rep_email:
          type: string
          nullable: true
      additionalProperties: false
    UpdateCustomerOutput:
      type: object
      additionalProperties: false
    UpdateCustomerOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateCustomerOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateEmployeeManuallyInput:
      type: object
      properties:
        update_supervisors:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/UpdateEmployeeRequest'
          nullable: true
        update_employee_info:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/CreateEmployeeRequest'
          nullable: true
      additionalProperties: false
    UpdateEmployeeManuallyOutput:
      type: object
      properties:
        updated_supervisors_count:
          type: integer
          format: int32
        updated_employee_info_count:
          type: integer
          format: int32
        failed_employee_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    UpdateEmployeeManuallyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/UpdateEmployeeManuallyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    UpdateEmployeeRequest:
      type: object
      properties:
        supervisor:
          $ref: '#/components/schemas/Supervisor'
      additionalProperties: false