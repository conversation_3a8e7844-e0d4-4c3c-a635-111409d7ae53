using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.FlowHubConfigs;

[TriggerGroup(ControllerNames.FlowHubConfigs)]
public class UnenrollFlowHub : ITrigger
{
    private readonly IFlowHubConfigService _flowHubConfigService;

    public UnenrollFlowHub(
        IFlowHubConfigService flowHubConfigService)
    {
        _flowHubConfigService = flowHubConfigService;
    }

    public class UnenrollFlowHubInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UnenrollFlowHubInput(
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UnenrollFlowHubOutput
    {
        [JsonProperty("flow_hub_config")]
        public FlowHubConfig FlowHubConfig { get; set; }

        [JsonConstructor]
        public UnenrollFlowHubOutput(
            FlowHubConfig flowHubConfig)
        {
            FlowHubConfig = flowHubConfig;
        }
    }

    public async Task<UnenrollFlowHubOutput> F(UnenrollFlowHubInput unenrollFlowHubInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            unenrollFlowHubInput.SleekflowStaffId,
            unenrollFlowHubInput.SleekflowStaffTeamIds);

        var flowHubConfig = await _flowHubConfigService.UnenrollFlowHubAsync(
            unenrollFlowHubInput.SleekflowCompanyId,
            sleekflowStaff);

        return new UnenrollFlowHubOutput(flowHubConfig);
    }
}