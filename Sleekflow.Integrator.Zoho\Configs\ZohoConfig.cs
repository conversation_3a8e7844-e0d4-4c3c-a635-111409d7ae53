using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Integrator.Zoho.Configs;

public interface IZohoConfig
{
    string ZohoClientId { get; }

    string ZohoClientSecret { get; }

    string ZohoOauthCallbackUrl { get; }

    string ZohoOauthStateEncryptionKey { get; }

    string ZohoAccountsDomain { get; }
}

public class ZohoConfig : IConfig, IZohoConfig
{
    public string ZohoClientId { get; private set; }

    public string ZohoClientSecret { get; private set; }

    public string ZohoOauthCallbackUrl { get; private set; }

    public string ZohoOauthStateEncryptionKey { get; private set; }

    public string ZohoAccountsDomain { get; private set; }

    public ZohoConfig(IConfiguration configuration)
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ZohoClientId =
            Environment.GetEnvironmentVariable("ZOHO_CLIENT_ID", target)
            ?? throw new SfMissingEnvironmentVariableException("ZOHO_CLIENT_ID");

        ZohoClientSecret =
            Environment.GetEnvironmentVariable("ZOHO_CLIENT_SECRET", target)
            ?? throw new SfMissingEnvironmentVariableException("ZOHO_CLIENT_SECRET");

        ZohoOauthCallbackUrl =
            Environment.GetEnvironmentVariable("ZOHO_OAUTH_CALLBACK_URL", target)
            ?? configuration["ZOHO_OAUTH_CALLBACK_URL"]!;

        ZohoOauthStateEncryptionKey =
            Environment.GetEnvironmentVariable("ZOHO_OAUTH_STATE_ENCRYPTION_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("ZOHO_OAUTH_STATE_ENCRYPTION_KEY");

        ZohoAccountsDomain =
            Environment.GetEnvironmentVariable("ZOHO_ACCOUNTS_DOMAIN", target)
            ?? configuration["ZOHO_ACCOUNTS_DOMAIN"]!;
    }
}