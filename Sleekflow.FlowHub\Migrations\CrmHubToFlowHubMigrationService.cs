﻿using System.Text;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;
using Sleekflow.Models.CrmHubToFlowHubMigrations;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Migrations;

public interface ICrmHubToFlowHubMigrationService
{
    Task CreateWorkflowsForCrmHubIntegration(
        string sleekflowCompanyId,
        Dictionary<string, MigrateCrmHubSyncConfigDto> crmHubEntityTypeNameToSyncConfigDict,
        Dictionary<string, List<SleekflowFieldToCrmProviderFieldMapping>> crmHubEntityTypeNameToFieldMappingsDict,
        string crmHubProviderName,
        string crmHubProviderConnectionId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);
}

public class CrmHubToFlowHubMigrationService : IScopedService, ICrmHubToFlowHubMigrationService
{
    private const string CrmHubSalesforceProviderName = "salesforce-integrator";

    private const string SalesforceContactObjectType = "Contact";

    private const string SalesforceStringFieldType = "string";

    private const string SalesforceDateFieldType = "date";

    private const string SalesforceDateTimeFieldType = "datetime";

    private const string SalesforceStringEqualsOperator = "=";

    private const string SalesforceDateAfterOperator = ">";

    private const string SalesforceContactCreatedWorkflowTriggerName = "SalesforceContactCreated";

    private const string SalesforceContactUpdatedWorkflowTriggerName = "SalesforceContactUpdated";

    private const string SleekflowContactCreatedWorkflowTriggerName = "ContactCreated";

    private const string SleekflowContactUpdatedWorkflowTriggerName = "ContactUpdated";

    private const string SetupContactAndConversationStepId = "setup-contact-and-conversation";

    private const string SetupContactAndConversationStepName = "setup";

    private const string SetupContactOwnerStepId = "setup-contact-owner";

    private const string SetupContactOwnerStepName = "setup-contact-owner";

    private const string EndStepName = "end";

    private const string CreateSleekflowContactStepCall = "sleekflow.v1.create-contact";

    private const string CreateSalesforceObjectStepCall = "sleekflow.v1.create-salesforce-object";

    private const string Action1StepName = "Action 1";

    private const string EmptyWorkflowTriggerCondition = "{{ (true) }}";

    private readonly IIdService _idService;
    private readonly IWorkflowService _workflowService;
    private readonly IFlowHubConfigService _flowHubConfigService;

    public CrmHubToFlowHubMigrationService(
        IIdService idService,
        IWorkflowService workflowService,
        IFlowHubConfigService flowHubConfigService)
    {
        _idService = idService;
        _workflowService = workflowService;
        _flowHubConfigService = flowHubConfigService;
    }

    public async Task CreateWorkflowsForCrmHubIntegration(
        string sleekflowCompanyId,
        Dictionary<string, MigrateCrmHubSyncConfigDto> crmHubEntityTypeNameToSyncConfigDict,
        Dictionary<string, List<SleekflowFieldToCrmProviderFieldMapping>> crmHubEntityTypeNameToFieldMappingsDict,
        string crmHubProviderName,
        string crmHubProviderConnectionId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        foreach (var (crmHubEntityTypeName, syncConfig) in crmHubEntityTypeNameToSyncConfigDict)
        {
            if (syncConfig.SyncMode is "from-provider" or "two-way-sync")
            {
                var workflowTriggerNameToTriggerDict = GetSyncFromCrmHubProviderWorkflowTriggerNameToTriggerDict(
                    crmHubEntityTypeName,
                    syncConfig,
                    crmHubProviderName,
                    crmHubProviderConnectionId);

                foreach (var (workflowTriggerName, workflowTrigger) in workflowTriggerNameToTriggerDict)
                {
                    var workflowTriggers = GetWorkflowTriggersFromWorkflowTrigger(workflowTriggerName, workflowTrigger);

                    var fieldMappings =
                        crmHubEntityTypeNameToFieldMappingsDict[crmHubEntityTypeName];

                    List<Step> internalSteps;

                    if (workflowTriggerName.Contains("Created"))
                    {
                        internalSteps = GetCrmProviderObjectCreatedWorkflowInternalSteps(
                            crmHubEntityTypeName,
                            fieldMappings);
                    }
                    else
                    {
                        // To be implemented: Object updated workflow, awaiting node design update
                        internalSteps = new List<Step>();
                    }

                    await CreateWorkflowAsync(
                        sleekflowCompanyId,
                        workflowTriggerName,
                        workflowTriggers,
                        internalSteps,
                        sleekflowStaffId,
                        sleekflowStaffTeamIds);
                }

                if (syncConfig.SyncMode == "two-way-sync" && crmHubEntityTypeName == "Contact")
                {
                    workflowTriggerNameToTriggerDict =
                        GetSyncToCrmHubProviderWorkflowTriggerNameToTriggerDict();

                    foreach (var (workflowTriggerName, workflowTrigger) in workflowTriggerNameToTriggerDict)
                    {
                        var workflowTriggers = GetWorkflowTriggersFromWorkflowTrigger(workflowTriggerName, workflowTrigger);

                        var fieldMappings =
                            crmHubEntityTypeNameToFieldMappingsDict[crmHubEntityTypeName];

                        List<Step> internalSteps;

                        if (workflowTriggerName.Contains("Created"))
                        {
                            // CrmHub logic: Sleekflow contact created => create Salesforce Lead
                            internalSteps = GetSleekflowContactCreatedWorkflowInternalSteps(
                                crmHubProviderName,
                                crmHubProviderConnectionId,
                                "Lead",
                                fieldMappings);
                        }
                        else
                        {
                            // To be implemented: Object updated workflow, awaiting node design update
                            internalSteps = new List<Step>();
                        }

                        await CreateWorkflowAsync(
                            sleekflowCompanyId,
                            workflowTriggerName,
                            workflowTriggers,
                            internalSteps,
                            sleekflowStaffId,
                            sleekflowStaffTeamIds);
                    }
                }
            }
            else
            {
                var workflowTriggerNameToTriggerDict =
                    GetSyncToCrmHubProviderWorkflowTriggerNameToTriggerDict();

                foreach (var (workflowTriggerName, workflowTrigger) in workflowTriggerNameToTriggerDict)
                {
                    var workflowTriggers = GetWorkflowTriggersFromWorkflowTrigger(workflowTriggerName, workflowTrigger);

                    var fieldMappings =
                        crmHubEntityTypeNameToFieldMappingsDict[crmHubEntityTypeName];

                    List<Step> internalSteps;

                    if (workflowTriggerName.Contains("Created"))
                    {
                        // CrmHub logic: Sleekflow contact created => create Salesforce Lead
                        internalSteps = GetSleekflowContactCreatedWorkflowInternalSteps(
                            crmHubProviderName,
                            crmHubProviderConnectionId,
                            "Lead",
                            fieldMappings);
                    }
                    else
                    {
                        // To be implemented: Object updated workflow, awaiting node design update
                        internalSteps = new List<Step>();
                    }

                    await CreateWorkflowAsync(
                        sleekflowCompanyId,
                        workflowTriggerName,
                        workflowTriggers,
                        internalSteps,
                        sleekflowStaffId,
                        sleekflowStaffTeamIds);
                }
            }
        }
    }

    private static Dictionary<string, WorkflowTrigger> GetSyncFromCrmHubProviderWorkflowTriggerNameToTriggerDict(
        string entityTypeName,
        MigrateCrmHubSyncConfigDto syncConfig,
        string crmHubProviderName,
        string crmHubProviderConnectionId)
    {
        if (crmHubProviderName == CrmHubSalesforceProviderName)
        {
            return GetSyncFromSalesforceProviderWorkflowTriggerNameToTriggerDict(
                entityTypeName,
                syncConfig,
                crmHubProviderConnectionId);
        }

        throw new NotImplementedException();
    }

    private static Dictionary<string, WorkflowTrigger> GetSyncFromSalesforceProviderWorkflowTriggerNameToTriggerDict(
        string entityTypeName,
        MigrateCrmHubSyncConfigDto syncConfig,
        string connectionId)
    {
        var salesforceObjectTriggerFieldsCondition = GetSalesforceObjectTriggerFieldsCondition(
            entityTypeName,
            syncConfig);

        return new Dictionary<string, WorkflowTrigger>
        {
            {
                GetSalesforceObjectCreatedTriggerName(entityTypeName), new WorkflowTrigger(
                    GetSalesforceObjectTriggerCondition(
                        connectionId,
                        entityTypeName,
                        EventNames.OnSalesforceObjectCreated,
                        salesforceObjectTriggerFieldsCondition))
            }
        };
    }

    private static string GetSalesforceObjectCreatedTriggerName(string entityTypeName)
    {
        return entityTypeName switch
        {
            SalesforceContactObjectType => SalesforceContactCreatedWorkflowTriggerName,
            _ => throw new NotImplementedException()
        };
    }

    private static string GetSalesforceObjectTriggerFieldsCondition(
        string entityTypeName,
        MigrateCrmHubSyncConfigDto syncConfig)
    {
        var conditionBuilder = new StringBuilder("(");

        var isFirstFilter = true;
        foreach (var filterGroup in syncConfig.FilterGroups)
        {
            var groupConditionBuilder = new StringBuilder();

            foreach (var filter in filterGroup.Filters)
            {
                var fieldName = filter.FieldName;
                var providerFieldType = filter.ProviderFieldType;
                var value = filter.Value;
                var @operator = filter.Operator;

                var filterCondition = GenerateSalesforceObjectFilterCondition(
                    fieldName,
                    providerFieldType,
                    value,
                    entityTypeName,
                    @operator,
                    isFirstFilter);

                groupConditionBuilder.Append($"({filterCondition}) && ");
            }

            var groupCondition = groupConditionBuilder.ToString().TrimEnd(' ', '&');

            conditionBuilder.Append($"{groupCondition} && ");

            if (isFirstFilter)
            {
                isFirstFilter = false;
            }
        }

        return conditionBuilder.ToString().TrimEnd(' ', '&');
    }

    private static string GenerateSalesforceObjectFilterCondition(
        string fieldName,
        string providerFieldType,
        string value,
        string objectType,
        string @operator,
        bool isFirstFilter)
    {
        var conditionOperator = GetSalesforceObjectConditionOperator(providerFieldType, @operator);

        if (providerFieldType == SalesforceStringFieldType)
        {
            value = value.Replace("'", "\"");

            return isFirstFilter
                ? $"(event_body.object_type == \"{objectType}\")) " +
                  $"&& ((event_body.object_dict[\"{fieldName}\"] {conditionOperator} {value}))"
                : $"((event_body.object_type == \"{objectType}\")) " +
                  $"&& ((event_body.object_dict[\"{fieldName}\"] {conditionOperator} {value}))";
        }

        if (providerFieldType is SalesforceDateFieldType or SalesforceDateTimeFieldType)
        {
            value = $"(date.parse '{value}')";

            return $"(event_body.object_type == \"{objectType}\")) " +
                   $"&& ((date.parse event_body.object_dict[\"{fieldName}\"]) {conditionOperator} {value})";
        }

        throw new NotImplementedException();
    }

    private static string GetSalesforceObjectConditionOperator(
        string providerFieldType,
        string @operator)
    {
        if (@operator == SalesforceStringEqualsOperator
            && providerFieldType == SalesforceStringFieldType)
        {
            return "==";
        }

        if (@operator == SalesforceDateAfterOperator
            && providerFieldType is SalesforceDateFieldType or SalesforceDateTimeFieldType)
        {
            return ">";
        }

        throw new NotImplementedException();
    }

    private static string GetSalesforceObjectTriggerCondition(
        string connectionId,
        string objectType,
        string eventName,
        string? fieldsCondition = null)
    {
        var template =
            $"{{{{ (event_body.salesforce_connection_id == \"{connectionId}\") " +
            $"&& (event_body.object_type == \"{objectType}\") " +
            "&& (event_body.is_custom_object == false) " +
            $"&& ((event_body.event_name == \"{eventName}\")) ";

        if (!string.IsNullOrEmpty(fieldsCondition))
        {
            template += $"&& ({fieldsCondition})";
        }

        template += " }}";

        return template;
    }

    private static Dictionary<string, WorkflowTrigger> GetSyncToCrmHubProviderWorkflowTriggerNameToTriggerDict()
    {
        return new Dictionary<string, WorkflowTrigger>
        {
            // no contact filter option for sleekflow-salesforce sync, therefore no condition
            {
                SleekflowContactCreatedWorkflowTriggerName,
                new WorkflowTrigger(EmptyWorkflowTriggerCondition)
            }
        };
    }

    private static List<Step> GetSleekflowContactCreatedWorkflowInternalSteps(
        string crmHubProviderName,
        string crmHubProviderConnectionId,
        string entityTypeName,
        List<SleekflowFieldToCrmProviderFieldMapping> fieldMappings)
    {
        if (crmHubProviderName == CrmHubSalesforceProviderName)
        {
            var defaultSetupSteps = GetDefaultWorkflowSetupSteps();

            var defaultEndStep = GetDefaultWorkflowEndStep();

            var createSalesforceObjectStep = GetCreateSalesforceObjectStep(
                crmHubProviderConnectionId,
                entityTypeName,
                defaultEndStep.Id,
                fieldMappings);

            return defaultSetupSteps.Append(createSalesforceObjectStep).Append(defaultEndStep).ToList();
        }

        throw new NotImplementedException();
    }

    private static Step GetCreateSalesforceObjectStep(
        string connectionId,
        string entityTypeName,
        string nextStepId,
        List<SleekflowFieldToCrmProviderFieldMapping> fieldMappings)
    {
        return new JObject(
                new JProperty("id", GetNonPredefinedStepId()),
                new JProperty("name", Action1StepName),
                new JProperty("next_step_id", nextStepId),
                new JProperty("call", CreateSalesforceObjectStepCall),
                new JProperty("args", GetCreateSalesforceObjectStepArgs(
                    connectionId,
                    entityTypeName,
                    fieldMappings)))
            .ToObject<Step>()!;
    }

    private static JObject GetCreateSalesforceObjectStepArgs(
        string connectionId,
        string entityTypeName,
        List<SleekflowFieldToCrmProviderFieldMapping> fieldMappings)
    {
        var objectPropertiesExprDict = new JObject();
        foreach (var mapping in fieldMappings)
        {
            objectPropertiesExprDict[mapping.CrmProviderField] =
                $"{{ '{{ usr_var_dict.contact[\"{mapping.SleekflowField}\"] ?? \"\" }}' | template.eval }}";
        }

        return new JObject(
            new JProperty("salesforce_connection_id__expr", $"{{ \"{connectionId}\" }}"),
            new JProperty("is_custom_object__expr", "{{ \"false\" }}"),
            new JProperty("object_type__expr", $"{{ \"{entityTypeName}\" }}"),
            new JProperty("object_properties__expr_dict", objectPropertiesExprDict));
    }

    private static List<Step> GetCrmProviderObjectCreatedWorkflowInternalSteps(
        string crmHubEntityTypeName,
        List<SleekflowFieldToCrmProviderFieldMapping> fieldMappings)
    {
        if (crmHubEntityTypeName is "Contact")
        {
            return GetCrmProviderContactCreatedWorkflowInternalSteps(
                fieldMappings);
        }

        throw new NotImplementedException();
    }

    private static List<Step> GetCrmProviderContactCreatedWorkflowInternalSteps(
        List<SleekflowFieldToCrmProviderFieldMapping> fieldMappings)
    {
        var defaultSetupSteps = GetDefaultWorkflowSetupSteps();

        var defaultEndStep = GetDefaultWorkflowEndStep();

        var createSleekflowContactStep = GetCreateSleekflowContactStep(
            defaultEndStep.Id,
            fieldMappings);

        return defaultSetupSteps.Append(createSleekflowContactStep).Append(defaultEndStep).ToList();
    }

    private static List<Step> GetDefaultWorkflowSetupSteps()
    {
        var defaultSetupContactAndConversationStep = new JObject(
            new JProperty("id", SetupContactAndConversationStepId),
            new JProperty("name", SetupContactAndConversationStepName),
            new JProperty(
                "assign",
                new JObject(
                    new JProperty("contact", "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact) }}"),
                    new JProperty("lists", "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_lists) }}"),
                    new JProperty("conversation", "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_conversation) }}"))))
            .ToObject<Step>()!;

        var defaultSetupContactOwnerStep = new JObject(
            new JProperty("id", SetupContactOwnerStepId),
            new JProperty("name", SetupContactOwnerStepName),
            new JProperty(
                "assign",
                new JObject(
                    new JProperty(
                        "contactOwner",
                        "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"))))
            .ToObject<Step>()!;

        return new List<Step>
        {
            defaultSetupContactAndConversationStep, defaultSetupContactOwnerStep
        };
    }

    private static Step GetDefaultWorkflowEndStep()
    {
        return new JObject(
                new JProperty("id", GetNonPredefinedStepId()),
                new JProperty("name", EndStepName))
            .ToObject<Step>()!;
    }

    private static Step GetCreateSleekflowContactStep(
        string nextStepId,
        List<SleekflowFieldToCrmProviderFieldMapping> fieldMappings)
    {
        return new JObject(
                new JProperty("id", GetNonPredefinedStepId()),
                new JProperty("name", Action1StepName),
                new JProperty("next_step_id", nextStepId),
                new JProperty("call", CreateSleekflowContactStepCall),
                new JProperty("args", GetCreateSleekflowContactStepArgs(fieldMappings)))
            .ToObject<Step>()!;
    }

    private static JObject GetCreateSleekflowContactStepArgs(List<SleekflowFieldToCrmProviderFieldMapping> fieldMappings)
    {
        var contactPropertiesExprDict = new JObject();
        foreach (var mapping in fieldMappings)
        {
            contactPropertiesExprDict[mapping.SleekflowField] =
                $"{{ '{{ trigger_event_body.object_dict[\"{mapping.CrmProviderField}\"] }}' | template.eval }}";
        }

        return new JObject(
            new JProperty("contact_phone_number__expr", $"{{ '{{ trigger_event_body.object_dict[\"Phone\"] }}' | template.eval }}"),
            new JProperty("contact_properties__expr_dict", contactPropertiesExprDict));
    }

    private static string GetNonPredefinedStepId()
    {
        return Guid.NewGuid().ToString();
    }

    private static WorkflowTriggers GetWorkflowTriggersFromWorkflowTrigger(
        string workflowTriggerName,
        WorkflowTrigger workflowTrigger)
    {
        WorkflowTrigger? salesforceContactCreated = null;
        WorkflowTrigger? salesforceContactUpdated = null;
        WorkflowTrigger? contactCreated = null;
        WorkflowTrigger? contactUpdated = null;

        switch (workflowTriggerName)
        {
            case SalesforceContactCreatedWorkflowTriggerName:
                salesforceContactCreated = workflowTrigger;
                break;
            case SalesforceContactUpdatedWorkflowTriggerName:
                salesforceContactUpdated = workflowTrigger;
                break;
            case SleekflowContactCreatedWorkflowTriggerName:
                contactCreated = workflowTrigger;
                break;
            case SleekflowContactUpdatedWorkflowTriggerName:
                contactUpdated = workflowTrigger;
                break;
        }

        return new WorkflowTriggers(
            null,
            contactCreated,
            null,
            null,
            contactUpdated,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            salesforceContactUpdated,
            salesforceContactCreated,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);
    }

    private async Task CreateWorkflowAsync(
        string sleekflowCompanyId,
        string name,
        WorkflowTriggers triggers,
        List<Step> internalSteps,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var workflowId = _idService.GetId("Workflow");
        var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflowId);

        var numOfWorkflows = await _workflowService.CountWorkflowsAsync(
            sleekflowCompanyId);
        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(sleekflowCompanyId);
        var usageLimit = flowHubConfig.UsageLimit;

        if (usageLimit is not null)
        {
            var numOfMaximumWorkflows = usageLimit.MaximumNumOfWorkflows;

            if (numOfWorkflows >= numOfMaximumWorkflows)
            {
                throw new SfFlowHubExceedUsageException(UsageLimitFieldNames.PropertyNameMaximumNumOfWorkflows);
            }
        }

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            sleekflowStaffId,
            sleekflowStaffTeamIds);

        await _workflowService.CreateWorkflowAsync(
            new Workflow(
                workflowId,
                workflowVersionedId,
                name,
                WorkflowType.Normal,
                null,
                triggers,
                null,
                null,
                internalSteps,
                WorkflowActivationStatuses.Active,
                workflowVersionedId,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                sleekflowCompanyId,
                sleekflowStaff,
                null,
                new Dictionary<string, object?>(),
                "v1",
                null),
            sleekflowCompanyId);
    }
}