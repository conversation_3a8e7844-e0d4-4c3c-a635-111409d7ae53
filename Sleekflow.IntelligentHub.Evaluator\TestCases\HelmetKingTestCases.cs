using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class HelmetKingTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetHelmetKingTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/HelmetKing/",
            SleekflowCompanyId: "aecdf113-2b29-4ee1-90d2-ff061b6f073d");

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢產品價格同送貨安排",
            [
                new ChatMessageContent(AuthorRole.User, "我想知WIZ膝頭滑塊 #ISLE OF MAN 賣幾錢一對？係咪包郵？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "WIZ KNEE SLIDERS #ISLE OF MAN 嘅價格係HKD448.00一對。而家訂單滿HKS$500就可以享香港同澳門免基本運費，不過優惠商品同會員優惠就唔適用啦。"),
                new ChatMessageContent(AuthorRole.User, "如果今日落單幾時送到？"),
            ],
            "現貨商品一般會喺1-3個工作天內安排出貨。預購商品嘅送貨時間就要睇返產品描述嘅說明。",
            [
                "https%3a%2f%2fwww.helmetking.com%2fen%2fproducts%2fwizkneeslidersisleofman%3fColour%3dWHITE%252FBLACK-0.md"
            ]);
        yield return new ChatEvalQuestion(
            testConfig,
            "海外客戶查詢關稅責任",
            [
                new ChatMessageContent(AuthorRole.User, "如果我喺海外訂購，使唔使俾關稅？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "海外客戶需要自行承擔關稅同進口稅。我哋會協助處理清關手續，但相關風險同費用都要由客戶負責。建議落單前先了解當地嘅進口稅率。"),
                new ChatMessageContent(AuthorRole.User, "如果海關要檢查會點處理？"),
            ],
            "政府部門有權隨時開箱檢查貨件，我哋唔會為檢查造成嘅延誤負責。如果海關需要額外文件證明，客戶需要及時自費提供。",
            [
                "https%3a%2f%2fwww.helmetking.com%2fen%2fproducts%2fwizkneeslidersisleofman%3fColour%3dWHITE%252FBLACK-0.md"
            ]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶了解退換貨安排",
            [
                new ChatMessageContent(AuthorRole.User, "如果收到貨唔鍾意可唔可以退錢？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "除咗特別提到嘅更換保證條件，所有售出商品都係不可退貨嘅。特殊情況下退款會以積分形式返還，金額以實際支付為準。"),
                new ChatMessageContent(AuthorRole.User, "如果之後減價可唔可以退差價？"),
            ],
            "商品價格會有波動，最終售價以落單時為準，之後嘅價格調整或優惠都唔可以退返差價。",
            [
                "https%3a%2f%2fwww.helmetking.com%2fen%2fproducts%2fwizkneeslidersisleofman%3fColour%3dWHITE%252FBLACK-0.md"
            ]);
        yield return new ChatEvalQuestion(
            testConfig,
            "學習摩托車駕照的流程",
            [
                new ChatMessageContent(AuthorRole.User, "我想考摩托車駕照，請問流程是怎樣的？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "考摩托車駕照的流程包括參加強制性考試和路考。強制性考試主要是評估你的基本操作能力，而路考則是考驗你在實際道路上的駕駛技巧。"),
                new ChatMessageContent(AuthorRole.User, "那麼強制性考試和路考有什麼不同？"),
            ],
            "強制性考試和路考是摩托車駕照考試的兩個主要部分。強制性考試關注基本操作技巧，如停車、轉彎等，而路考則評估你在真實道路環境下的駕駛能力和安全意識。",
            ["https%3a%2f%2fwww.helmetking.com%2fen%2fblogs%2fbeginner-lesson-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "選擇摩托車頭盔",
            [
                new ChatMessageContent(AuthorRole.User, "我是摩托車新手，請問該如何選擇頭盔？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "選擇頭盔時，應該考慮安全性、舒適度和風阻等因素。初學者可以選擇入門級的頭盔，之後再根據需求升級。"),
                new ChatMessageContent(AuthorRole.User, "那麼哪些品牌比較好？"),
            ],
            "選擇頭盔時，應該優先考慮安全性和舒適度。初學者可以選擇入門級的頭盔，品牌選擇上可以考慮Helmet King等知名品牌。",
            ["https%3a%2f%2fwww.helmetking.com%2fen%2fblogs%2fbeginner-lesson-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "摩托車駕駛安全裝備",
            [
                new ChatMessageContent(AuthorRole.User, "學習摩托車駕駛需要什麼安全裝備？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "學習摩托車駕駛時，應該配備頭盔、手套、防護服等安全裝備，以減少意外受傷的風險。"),
                new ChatMessageContent(AuthorRole.User, "這些裝備真的有用嗎？"),
            ],
            "正確的安全裝備能夠有效減少摩托車駕駛中的受傷風險。頭盔、手套和防護服等都是必不可少的保護裝備，能夠在意外發生時提供重要的保護。",
            ["https%3a%2f%2fwww.helmetking.com%2fen%2fblogs%2fbeginner-lesson-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對綿羊車的操作簡單性表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "請問綿羊車的操作有幾咁簡單？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "綿羊車的操作相當簡單，因為它使用自動波，唔需要控制離合器同波段，減少咗死火嘅問題。只需控制油門同剎車掣，就可以平穩地駕駛。"),
                new ChatMessageContent(AuthorRole.User, "那如果我係棍波車嘅經驗，會唔會難以適應？"),
            ],
            "就算你有棍波車嘅經驗，綿羊車都係好易上手，因為操作簡單，唔需要太多時間適應。",
            [
                "https%3a%2f%2f26king.hk%2feng%2f%e3%80%90%e6%96%b0%e6%89%8b%e8%b2%b7%e8%bb%8a%e3%80%91%e4%bd%a0%e5%8f%af%e8%83%bd%e5%be%9e%e6%9c%aa%e8%80%83%e6%85%ae%e7%9a%84%e8%bb%8a%e5%9e%8b-%e5%a5%bd%e8%99%95%e5%a4%9a%e5%a4%9a%2f-0.md"
            ]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對綿羊車的儲物空間表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "請問綿羊車的儲物空間有幾大？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "綿羊車的儲物空間相當龐大，通常有大型置物空間，可以放頭盔、車冚、手套等物品。有些車款甚至有平地台，可以擺放外賣袋等。"),
                new ChatMessageContent(AuthorRole.User, "那如果我係買餸或買外賣，綿羊車會唔會方便？"),
            ],
            "綿羊車的儲物空間非常適合買餸或買外賣，因為有足夠嘅空間放置相關物品，非常方便。",
            [
                "https%3a%2f%2f26king.hk%2feng%2f%e3%80%90%e6%96%b0%e6%89%8b%e8%b2%b7%e8%bb%8a%e3%80%91%e4%bd%a0%e5%8f%af%e8%83%bd%e5%be%9e%e6%9c%aa%e8%80%83%e6%85%ae%e7%9a%84%e8%bb%8a%e5%9e%8b-%e5%a5%bd%e8%99%95%e5%a4%9a%e5%a4%9a%2f-0.md"
            ]);

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對26CHECK服務的價格和優惠表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "請問26CHECK電單車檢查服務的價格是多少？有什麼優惠嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "26CHECK的價格取決於檢查項目和維修需求。您可以享受10%的折扣，如果您在檢查後選擇我們的維修服務。另外，提前預約還可以享受HKD 100的折扣。"),
                new ChatMessageContent(AuthorRole.User, "那如果我是回頭客呢？有額外的優惠嗎？"),
            ],
            "是的，回頭客在提前預約時可以享受最高HKD 200的折扣。請聯繫我們的專業團隊預約服務。電話：6385 8830。",
            ["https%3a%2f%2f26king.hk%2feng%2f26check%2f-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對26CHECK服務的檢查項目和維修時間表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "26CHECK檢查服務包括哪些項目？維修時間大約需要多久？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "26CHECK檢查共有55個項目，涵蓋引擎、煞車、電池等。維修時間通常需要1到2天，視具體維修項目而定。標準檢查時間約為4到5小時。"),
                new ChatMessageContent(AuthorRole.User, "如果我需要更換零件，會影響檢查時間嗎？"),
            ],
            "是的，如果需要更換零件，可能需要額外的時間。檢查後我們會提供詳細的維修建議和時間估算。",
            ["https%3a%2f%2f26king.hk%2feng%2f26check%2f-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對26CHECK服務的售後支持表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "使用26CHECK服務後，如果出現問題，怎麼辦？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "在維修後的3個月或1500公里內，如果出現問題，我們會提供全面維護支持。您可以聯繫我們進行協助。"),
                new ChatMessageContent(AuthorRole.User, "那如果我需要拖車服務呢？"),
            ],
            "我們提供拖車至26KING的服務。如果您的電單車故障或發生意外，您可以聯繫我們進行一站式報價、維修和檢查。",
            ["https%3a%2f%2f26king.hk%2feng%2f26check%2f-0.md"]);
    }
}