using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnContactListRelationshipsChangedEventBody : EventBody, IHasSleekflowStaff
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnContactListRelationshipsChanged; }
    }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [Required]
    [JsonProperty("added_to_lists")]
    public List<OnContactListRelationshipsChangedEventBodyContactList> AddedToLists { get; set; }

    [Required]
    [JsonProperty("removed_from_lists")]
    public List<OnContactListRelationshipsChangedEventBodyContactList> RemovedFromLists { get; set; }

    [Required]
    [JsonProperty("lists")]
    public List<OnContactListRelationshipsChangedEventBodyContactList> Lists { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [JsonProperty("sleekflow_staff_identity_id")]
    public string? SleekflowStaffIdentityId { get; set; }

    [JsonConstructor]
    public OnContactListRelationshipsChangedEventBody(
        DateTimeOffset createdAt,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        List<OnContactListRelationshipsChangedEventBodyContactList> addedToLists,
        List<OnContactListRelationshipsChangedEventBodyContactList> removedFromLists,
        List<OnContactListRelationshipsChangedEventBodyContactList> lists,
        string contactId,
        Dictionary<string, object?> contact,
        string? sleekflowStaffIdentityId)
        : base(createdAt)
    {
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        AddedToLists = addedToLists;
        RemovedFromLists = removedFromLists;
        Lists = lists;
        ContactId = contactId;
        Contact = contact;
        SleekflowStaffIdentityId = sleekflowStaffIdentityId;
    }
}

public class OnContactListRelationshipsChangedEventBodyContactList
{
    [JsonProperty("list_id")]
    public string ListId { get; set; }

    [JsonProperty("list_name")]
    public string ListName { get; set; }

    [JsonConstructor]
    public OnContactListRelationshipsChangedEventBodyContactList(string listId, string listName)
    {
        ListId = listId;
        ListName = listName;
    }
}