using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class CreateWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IIdService _idService;
    private readonly IWorkflowStepValidator _workflowStepValidator;

    public CreateWorkflow(
        IWorkflowService workflowService,
        IFlowHubConfigService flowConfigService,
        IIdService idService,
        IWorkflowStepValidator workflowStepValidator)
    {
        _workflowService = workflowService;
        _flowHubConfigService = flowConfigService;
        _idService = idService;
        _workflowStepValidator = workflowStepValidator;
    }

    public class CreateWorkflowInput : IHasSleekflowStaff, IHasSleekflowCompanyId, IHasMetadata
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("triggers")]
        public WorkflowTriggers Triggers { get; set; }

        [ValidateObject]
        [JsonProperty("workflow_enrollment_settings")]
        public WorkflowEnrollmentSettings? WorkflowEnrollmentSettings { get; set; }

        [ValidateObject]
        [JsonProperty("workflow_schedule_settings")]
        public WorkflowScheduleSettings? WorkflowScheduleSettings { get; set; }

        [Required]
        [MaxLength(1024)]
        [MinLength(1)]
        [JsonProperty("steps")]
        public List<JObject> Steps { get; set; }

#pragma warning disable JA1001
#pragma warning disable S2365
        [JsonIgnore]
        [ValidateArray]
        [Required]
        public List<Step> InternalSteps
        {
            get
            {
                return Steps
                    .Select(step => step.ToObject<Step>()!)
                    .ToList();
            }
        }
#pragma warning restore JA1001
#pragma warning restore S2365

        [Required]
        [MaxLength(100)]
        [MinLength(1)]
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("workflow_type")]
        public string? WorkflowType { get; set; }

        [JsonProperty("workflow_group_id")]
        public string? WorkflowGroupId { get; set; }

        [Required]
        [JsonProperty("metadata")]
        [ValidateObject]
        public Dictionary<string, object?> Metadata { get; set; }

        [JsonProperty("version")]
        [RegularExpression(@"^v[1-2]$")]
        public string? Version { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty("manual_enrollment_source")]
        public string? ManualEnrollmentSource { get; set; }

        [JsonConstructor]
        public CreateWorkflowInput(
            string sleekflowCompanyId,
            WorkflowTriggers triggers,
            WorkflowEnrollmentSettings? workflowEnrollmentSettings,
            WorkflowScheduleSettings? workflowScheduleSettings,
            List<JObject> steps,
            string name,
            string? workflowType,
            string? workflowGroupId,
            Dictionary<string, object?> metadata,
            string? version,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string? manualEnrollmentSource)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Triggers = triggers;
            WorkflowEnrollmentSettings = workflowEnrollmentSettings;
            WorkflowScheduleSettings = workflowScheduleSettings;
            Steps = steps;
            Name = name;
            WorkflowType = workflowType;
            WorkflowGroupId = workflowGroupId;
            Metadata = metadata;
            Version = version;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            ManualEnrollmentSource = manualEnrollmentSource;
        }
    }

    public class CreateWorkflowOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public CreateWorkflowOutput(
            WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<CreateWorkflowOutput> F(CreateWorkflowInput createWorkflowInput)
    {
        await _workflowStepValidator.AssertAllStepsAreValidAsync(
            createWorkflowInput.InternalSteps,
            createWorkflowInput.WorkflowScheduleSettings?.ScheduleType,
            createWorkflowInput.WorkflowScheduleSettings?.IsNewScheduledWorkflowSchema);

        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            createWorkflowInput.SleekflowStaffId,
            createWorkflowInput.SleekflowStaffTeamIds);

        var workflowId = _idService.GetId("Workflow");
        var workflowVersionedId = _idService.GetId("WorkflowVersioned", workflowId);

        if (createWorkflowInput.WorkflowType == WorkflowType.Normal)
        {
            var numOfWorkflows = await _workflowService.CountWorkflowsAsync(
                createWorkflowInput.SleekflowCompanyId,
                workflowType: WorkflowType.Normal);
            var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(createWorkflowInput.SleekflowCompanyId);
            var usageLimit = flowHubConfig.UsageLimit;

            if (usageLimit is not null)
            {
                var numOfMaximumWorkflows = usageLimit.MaximumNumOfWorkflows;

                if (numOfWorkflows >= numOfMaximumWorkflows)
                {
                    throw new SfFlowHubExceedUsageException(UsageLimitFieldNames.PropertyNameMaximumNumOfWorkflows);
                }
            }
        }

        var workflow = await _workflowService.CreateWorkflowAsync(
            new Workflow(
                workflowId,
                workflowVersionedId,
                createWorkflowInput.Name,
                createWorkflowInput.WorkflowType,
                createWorkflowInput.WorkflowGroupId,
                createWorkflowInput.Triggers,
                createWorkflowInput.WorkflowEnrollmentSettings,
                createWorkflowInput.WorkflowScheduleSettings,
                createWorkflowInput.InternalSteps,
                WorkflowActivationStatuses.Draft,
                workflowVersionedId,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow,
                createWorkflowInput.SleekflowCompanyId,
                sleekflowStaff,
                null,
                createWorkflowInput.Metadata,
                createWorkflowInput.Version,
                manualEnrollmentSource: createWorkflowInput.ManualEnrollmentSource),
            createWorkflowInput.SleekflowCompanyId);

        return new CreateWorkflowOutput(new WorkflowDto(workflow));
    }
}