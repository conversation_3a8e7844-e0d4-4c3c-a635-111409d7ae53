using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface IMp4KnowledgeSource : IKnowledgeSource
{
}

public class Mp4KnowledgeSource : IMp4KnowledgeSource, IScopedService
{
    private readonly ILogger<Mp4KnowledgeSource> _logger;
    private readonly Kernel _kernel;

    public Mp4KnowledgeSource(
        ILogger<Mp4KnowledgeSource> logger,
        Kernel kernel)
    {
        _logger = logger;
        _kernel = kernel;
    }

    public async Task<IKnowledgeSource.IngestionResult> Ingest(
        Stream blobStream,
        object? fileIngestionProgress)
    {
        throw new NotImplementedException();
    }
}