using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Carts.ShopifyCarts;

public class ShopifyCartStatistics
{
    [JsonProperty("count")]
    public long Count { get; set; }

    [JsonProperty("total_price")]
    public decimal TotalPrice { get; set; }

    [JsonConstructor]
    public ShopifyCartStatistics(
        long count,
        decimal totalPrice)
    {
        Count = count;
        TotalPrice = totalPrice;
    }
}