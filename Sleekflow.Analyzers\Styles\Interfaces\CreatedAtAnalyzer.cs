using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class CreatedAtAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1003";
    public const string Category = "Design";

    private static readonly LocalizableString Title = "Class should implement IHasCreatedAt";

    private static readonly LocalizableString MessageFormat =
        "Class '{0}' has a '{1}' property but does not implement the corresponding interface";

    private static readonly LocalizableString Description =
        "If a class has a CreatedAt property, it should implement the IHasCreatedAt interface.";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSymbolAction(AnalyzeSymbol, SymbolKind.NamedType);
    }

    private static void AnalyzeSymbol(SymbolAnalysisContext context)
    {
        var namedTypeSymbol = (INamedTypeSymbol) context.Symbol;

        // Check if the class has a CreatedAt property
        var createdAtProperty = namedTypeSymbol.GetMembers()
            .FirstOrDefault(m => m.Kind == SymbolKind.Property && m.Name == "CreatedAt");
        if (createdAtProperty == null)
        {
            return;
        }

        // Check if the class already implements IHasCreatedAt
        var iHasCreatedAtInterface = namedTypeSymbol.Interfaces.FirstOrDefault(
            i => i.ToDisplayString() == "Sleekflow.Persistence.Abstractions.IHasCreatedAt");
        if (iHasCreatedAtInterface != null)
        {
            return;
        }

        // Create a diagnostic and report it
        var createdAtDiagnostic = Diagnostic.Create(
            Rule,
            namedTypeSymbol.Locations[0],
            namedTypeSymbol.Name,
            "CreatedAt");
        context.ReportDiagnostic(createdAtDiagnostic);
    }
}