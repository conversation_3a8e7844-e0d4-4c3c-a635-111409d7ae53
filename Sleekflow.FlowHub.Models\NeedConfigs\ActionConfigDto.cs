﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.NeedConfigs;

public class ActionConfigDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("action_group")]
    public string ActionGroup { get; set; }

    [JsonProperty("action_subgroup")]
    public string ActionSubgroup { get; set; }

    [JsonConstructor]
    public ActionConfigDto(
        string id,
        string label,
        string actionGroup,
        string actionSubgroup)
    {
        Id = id;
        Label = label;
        ActionGroup = actionGroup;
        ActionSubgroup = actionSubgroup;
    }

    public ActionConfigDto(ActionConfig actionConfig)
    {
        Id = actionConfig.Id;
        Label = actionConfig.Label;
        ActionGroup = actionConfig.ActionGroup;
        ActionSubgroup = actionConfig.ActionSubgroup;
    }
}