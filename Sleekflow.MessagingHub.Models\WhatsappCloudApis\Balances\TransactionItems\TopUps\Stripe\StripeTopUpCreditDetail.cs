using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps.Stripe;

public class StripeTopUpCreditDetail
{
    // use it as the unique id of the Credit
    [JsonProperty("checkout_session_id")]
    public string? CheckoutSessionId { get; set; }

    [JsonProperty("customer_id")]
    public string CustomerId { get; set; }

    [JsonProperty("invoice_id")]
    public string InvoiceId { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, string> Metadata { get; set; }

    [JsonProperty("snapshot")]
    public Dictionary<string, object?> SnapShot { get; set; }

    [JsonConstructor]
    public StripeTopUpCreditDetail(
        string? checkoutSessionId,
        string customerId,
        string invoiceId,
        Dictionary<string, string> metadata,
        Dictionary<string, object?> snapShot)
    {
        Metadata = metadata;
        SnapShot = snapShot;
        InvoiceId = invoiceId;
        CheckoutSessionId = checkoutSessionId;
        CustomerId = customerId;
    }
}