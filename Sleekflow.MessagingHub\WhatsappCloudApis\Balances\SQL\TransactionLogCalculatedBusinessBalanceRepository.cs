using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Balances.SQL;

public interface ITransactionLogCalculatedBusinessBalanceRepository
    : IRepository<TransactionLogCalculatedBusinessBalance>
{
    Task<TransactionLogCalculatedBusinessBalance?>
        AccumulateBusinessBalanceTransactionLogAsync(string facebookBusinessId);

    Task<TransactionLogCalculatedBusinessBalance?> AccumulateBusinessBalanceTransactionLogByFacebookWabaIdAsync(
        string facebookBusinessId,
        string facebookWabaId);
}

public class TransactionLogCalculatedBusinessBalanceRepository
    : BaseRepository<TransactionLogCalculatedBusinessBalance>,
        ITransactionLogCalculatedBusinessBalanceRepository,
        ISingletonService
{
    public TransactionLogCalculatedBusinessBalanceRepository(
        ILogger<TransactionLogCalculatedBusinessBalanceRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<TransactionLogCalculatedBusinessBalance?> AccumulateBusinessBalanceTransactionLogAsync(
        string facebookBusinessId)
    {
        const string facebookBusinessIdPrefix = "@FacebookBusinessId";
        var query = new QueryDefinition(
                "SELECT " +
                "Count(c) as total, " +
                "SUM(c.credit.amount) as credit, " +
                "SUM(c.used.amount) as used, " +
                "SUM(c.markup.amount) as markup, " +
                "SUM(c.transaction_handling_fee.amount) as transaction_handling_fee, " +
                "SUM(c.waba_conversation_usage.business_initiated_cost) as business_initiated_cost, " +
                "SUM(c.waba_conversation_usage.user_initiated_cost) as user_initiated_cost, " +
                "SUM(c.waba_conversation_usage.business_initiated_paid_quantity) as business_initiated_paid_quantity, " +
                "SUM(c.waba_conversation_usage.business_initiated_free_tier_quantity) as business_initiated_free_tier_quantity, " +
                "SUM(c.waba_conversation_usage.user_initiated_paid_quantity) as user_initiated_paid_quantity, " +
                "SUM(c.waba_conversation_usage.user_initiated_free_tier_quantity) as user_initiated_free_tier_quantity, " +
                "SUM(c.waba_conversation_usage.user_initiated_free_entry_point_quantity) as user_initiated_free_entry_point_quantity " +
                $"FROM c where c.facebook_business_id = {facebookBusinessIdPrefix}")
            .WithParameter(facebookBusinessIdPrefix, facebookBusinessId);
        return (await GetObjectsAsync(query)).FirstOrDefault();
    }

    public async Task<TransactionLogCalculatedBusinessBalance?>
        AccumulateBusinessBalanceTransactionLogByFacebookWabaIdAsync(string facebookBusinessId, string facebookWabaId)
    {
        const string facebookBusinessIdPrefix = "@FacebookBusinessId";
        const string facebookWabaIdPrefix = "@facebookWabaId";
        var query = new QueryDefinition(
                "SELECT " +
                "Count(c) as total, " +
                "SUM(c.credit.amount) as credit, " +
                "SUM(c.used.amount) as used, " +
                "SUM(c.markup.amount) as markup, " +
                "SUM(c.transaction_handling_fee.amount) as transaction_handling_fee, " +
                "SUM(c.waba_conversation_usage.business_initiated_cost) as business_initiated_cost, " +
                "SUM(c.waba_conversation_usage.user_initiated_cost) as user_initiated_cost, " +
                "SUM(c.waba_conversation_usage.business_initiated_paid_quantity) as business_initiated_paid_quantity, " +
                "SUM(c.waba_conversation_usage.business_initiated_free_tier_quantity) as business_initiated_free_tier_quantity, " +
                "SUM(c.waba_conversation_usage.user_initiated_paid_quantity) as user_initiated_paid_quantity, " +
                "SUM(c.waba_conversation_usage.user_initiated_free_tier_quantity) as user_initiated_free_tier_quantity, " +
                "SUM(c.waba_conversation_usage.user_initiated_free_entry_point_quantity) as user_initiated_free_entry_point_quantity " +
                $"FROM c where c.facebook_business_id = {facebookBusinessIdPrefix} and c.facebook_waba_id = {facebookWabaIdPrefix}")
            .WithParameter(facebookBusinessIdPrefix, facebookBusinessId)
            .WithParameter(facebookWabaIdPrefix, facebookWabaId);
        return (await GetObjectsAsync(query)).FirstOrDefault();
    }
}