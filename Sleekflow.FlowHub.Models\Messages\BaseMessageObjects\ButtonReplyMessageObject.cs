using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

public class ButtonReplyMessageObject : BaseMessageObject
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonConstructor]
    public ButtonReplyMessageObject(string id, string title)
    {
        Id = id;
        Title = title;
    }
}