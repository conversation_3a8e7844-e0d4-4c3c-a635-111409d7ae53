using System;
using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class JsonPropertyAnalyzer : DiagnosticAnalyzer
{
    public const string MissingJsonPropertyDiagnosticId = "JA1001";
    public const string Category = "Usage";

    private static readonly LocalizableString MissingJsonPropertyTitle =
        "Property should have JsonProperty attribute";

    private static readonly LocalizableString MissingJsonPropertyMessageFormat =
        "Property '{0}' should have JsonProperty attribute";

    private static readonly DiagnosticDescriptor MissingJsonPropertyRule = new DiagnosticDescriptor(
        MissingJsonPropertyDiagnosticId,
        MissingJsonPropertyTitle,
        MissingJsonPropertyMessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics =>
        ImmutableArray.Create(MissingJsonPropertyRule);

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSyntaxNodeAction(AnalyzePropertyDeclaration, SyntaxKind.PropertyDeclaration);
    }

    private void AnalyzePropertyDeclaration(SyntaxNodeAnalysisContext context)
    {
        var propertyDeclaration = (PropertyDeclarationSyntax) context.Node;
        var classDeclaration = propertyDeclaration.Parent as ClassDeclarationSyntax;
        if (classDeclaration == null)
        {
            return;
        }

        var className = classDeclaration.Identifier.Text;
        var namespaceName = GetNamespace(classDeclaration);

        if (className.EndsWith("Event")
            || className.EndsWith("Request")
            || className.EndsWith("Reply"))
        {
            return;
        }

        if (!className.EndsWith("Input", StringComparison.Ordinal)
            && !className.EndsWith("Output", StringComparison.Ordinal)
            && !namespaceName.Contains("Models"))
        {
            return;
        }

        var hasJsonPropertyAttribute = propertyDeclaration.AttributeLists
            .SelectMany(list => list.Attributes)
            .Any(a => TypeSymbolUtils.IsJsonPropertyAttribute(context, a));
        if (!hasJsonPropertyAttribute)
        {
            var diagnostic = Diagnostic.Create(
                MissingJsonPropertyRule,
                propertyDeclaration.Identifier.GetLocation(),
                propertyDeclaration.Identifier.Text);
            context.ReportDiagnostic(diagnostic);
        }
    }

    private static string GetNamespace(SyntaxNode syntaxNode)
    {
        return string.Join(
            ".",
            syntaxNode
                .Ancestors()
                .OfType<BaseNamespaceDeclarationSyntax>()
                .Reverse()
                .Select(_ => _.Name));
    }
}