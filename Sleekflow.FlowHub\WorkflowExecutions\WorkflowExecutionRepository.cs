﻿using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowExecutions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.WorkflowExecutions;

public interface IWorkflowExecutionRepository : IRepository<WorkflowExecution>
{
    Task<int> GetUniqueWorkflowExecutionCountAsync(
        string sleekflowCompanyId,
        string? workflowType,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime);
}

public class WorkflowExecutionRepository
    : BaseRepository<WorkflowExecution>, IWorkflowExecutionRepository, IScopedService
{
    public WorkflowExecutionRepository(
        ILogger<WorkflowExecutionRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<int> GetUniqueWorkflowExecutionCountAsync(
        string sleekflowCompanyId,
        string? workflowType,
        DateTimeOffset fromDateTime,
        DateTimeOffset toDateTime)
    {
        var container = GetContainer();
        var queryDefinition = new QueryDefinition(
                $"""
                 SELECT VALUE COUNT(1)
                 FROM (SELECT DISTINCT c.state_identity.workflow_id
                 FROM c
                 WHERE c.sleekflow_company_id = @sleekflow_company_id
                 AND c.created_at >= @from
                 AND c.created_at <= @to
                 {(string.IsNullOrWhiteSpace(workflowType)
                     ? string.Empty
                     : (workflowType == WorkflowType.Normal ? "AND (c.workflow_type = @workflow_type OR IS_NULL(c.workflow_type))" : "AND c.workflow_type = @workflow_type"))})
                 """)
            .WithParameter("@sleekflow_company_id", sleekflowCompanyId)
            .WithParameter("@from", fromDateTime)
            .WithParameter("@to", toDateTime)
            .WithParameter("@workflow_type", workflowType);

        var qd = queryDefinition.GetQueryParameters()
            .Aggregate(
                new QueryDefinition(queryDefinition.QueryText.Replace("%%CONTAINER_NAME%%", container.Id)),
                (current, queryParameter) => current.WithParameter(queryParameter.Name, queryParameter.Value));

        using var itemQueryIterator = container.GetItemQueryIterator<int?>(qd);
        var queryResult = await itemQueryIterator.ReadNextAsync();

        return queryResult.FirstOrDefault() ?? 0;
    }
}