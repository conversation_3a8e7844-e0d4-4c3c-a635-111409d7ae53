using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Tools;

public class RealTimeAssistantTool
{
    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("schema")]
    public object Schema { get; set; }

    [JsonProperty("function")]
    public Func<(AdditionalFields AdditionalFields, JObject JObject), Task<Result>> Function { get; set; }

    [JsonConstructor]
    public RealTimeAssistantTool(
        string name,
        object schema,
        Func<(AdditionalFields AdditionalFields, JObject JObject), Task<Result>> function)
    {
        Name = name;
        Schema = schema;
        Function = function;
    }

    public class Result
    {
        [JsonProperty("text")]
        public object Text { get; set; }

        [JsonProperty("instructions")]
        public string Instructions { get; set; }

        [JsonConstructor]
        public Result(object text, string instructions)
        {
            Text = text;
            Instructions = instructions;
        }
    }

    public class AdditionalFields : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public AdditionalFields(string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }
}