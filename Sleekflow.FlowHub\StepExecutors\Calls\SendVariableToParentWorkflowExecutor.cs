using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Utils;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface ISendVariableToParentWorkflowExecutor : IStepExecutor;

public class SendVariableToParentWorkflowExecutor(
    IWorkflowStepLocator workflowStepLocator,
    IWorkflowRuntimeService workflowRuntimeService,
    IServiceProvider serviceProvider,
    IStateService stateService,
    // IStateAggregator stateAggregator, // TODO: Remove or uncomment and use
    IServiceBusManager serviceBusManager,
    ILogger<SendVariableToParentWorkflowExecutor> logger)
    : GeneralStepExecutor<CallStep<SendVariableToParentWorkflowStepArgs>>(
            workflowStepLocator,
            workflowRuntimeService,
            serviceProvider),
        ISendVariableToParentWorkflowExecutor,
        IScopedService
{
    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        try
        {
            var runningStates = await stateService.GetRunningStatesAsync(
                state.Identity.ObjectId,
                state.Identity.ObjectType,
                state.Identity.SleekflowCompanyId,
                state.TriggerEventBody);

            var (parentState, targetStepInParent) = stateService
                .GetParentState(runningStates, state.WorkflowContext.SnapshottedWorkflow.WorkflowId);

            // hard coded for s56
            var fieldsToInclude = new HashSet<string> { "category", "score", "confidence_score", "exit_condition" };

            var sysVarDict = await state.SysVarDict.GetInnerDictionary().ToDictionaryAsync();
            var mergedVariablesForParent = DictionaryTransformUtils.MergeAndFilterValues(
                sysVarDict.Values,
                fieldsToInclude);

            await serviceBusManager
                .PublishAsync(new OnAgentCompleteStepActivationEvent(
                    targetStepInParent.Id,
                    parentState.Id,
                    stackEntries,
                    JsonConvert.SerializeObject(mergedVariablesForParent)));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception ex)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                ex);
        }
    }
}