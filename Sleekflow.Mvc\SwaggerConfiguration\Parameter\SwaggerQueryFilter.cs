#if SWAGGERGEN
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Sleekflow.Mvc.SwaggerConfiguration.Parameter;

public class SwaggerQueryFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        var attributes = context
            .MethodInfo
            .DeclaringType?
            .GetCustomAttributes(true)
            .Union(context.MethodInfo.GetCustomAttributes(true))
            .OfType<SwaggerQueryAttribute>();

        if (attributes == null) return;

        foreach (var attribute in attributes)
        {
            foreach (var name in attribute.Names)
            {
                operation.Parameters.Add(new OpenApiParameter
                {
                    Name = name,
                    Required = attribute.Required,
                    In = ParameterLocation.Query,
                    Schema = new OpenApiSchema
                    {
                        Type = "string"
                    }
                });
            }
        }
    }
}
#endif