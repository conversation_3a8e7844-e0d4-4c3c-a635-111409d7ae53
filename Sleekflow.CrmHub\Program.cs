using System.Reflection;
using MassTransit;
using Serilog;
using Sleekflow;
using Sleekflow.Configs;
using Sleekflow.CrmHub.Processors;
using Sleekflow.CrmHub.Processors.ChangeFeedHandlers.Abstractions;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Events.EventHub;
using Sleekflow.Events.ServiceBus;
using Sleekflow.Mvc;
using Sleekflow.Mvc.HealthChecks;
using Sleekflow.Persistence.CrmHubDb;

#if SWAGGERGEN
using Microsoft.AspNetCore.Mvc.ApiExplorer;
using Microsoft.OpenApi.Models;
using Moq;
#endif

static void BuildCrmHubDbProcessorServices(IServiceCollection b, Type rootType)
{
#if SWAGGERGEN
#else
    b.AddSingleton<ICrmHubDbProcessorConfig, CrmHubDbProcessorConfig>();
    b.AddHostedService<CrmHubDbProcessorService>(
        sp =>
        {
            var eventHandlers = new List<IChangeFeedHandler>();
            foreach (var t in rootType.Assembly
                         .GetTypes()
                         .Where(
                             type =>
                                 type.IsAssignableTo(typeof(ISingletonService))
                                 && type != typeof(ISingletonService)
                                 && type.GetInterfaces().Any(i => i == typeof(IChangeFeedHandler))))
            {
                var @interface = t.GetInterfaces().FirstOrDefault(i => i.Name == "I" + t.Name);
                if (@interface == null)
                {
                    continue;
                }

                eventHandlers.Add((IChangeFeedHandler) sp.GetRequiredService(@interface));
            }

            return new CrmHubDbProcessorService(
                sp.GetRequiredService<ILogger<CrmHubDbProcessorService>>(),
                sp.GetRequiredService<ICrmHubDbResolver>(),
                sp.GetRequiredService<ICrmHubDbProcessorConfig>(),
                eventHandlers.ToList());
        });
#endif
}

const string appName = "SleekflowCrmHub";

MvcModules.BuildLogger(appName);

try
{
    Log.Information("Starting web host");

    var builder = WebApplication.CreateBuilder(args);
    builder.Host.UseSerilog();
    builder.Services.AddHttpContextAccessor();

    MvcModules.BuildHealthCheck(builder.Services);
    MvcModules.BuildTelemetryServices(builder.Services, builder.Environment, appName);
#if SWAGGERGEN
    MvcModules.BuildApiBehaviors(builder, list =>
    {

        list.AddRange(new List<OpenApiServer>()
        {
            new OpenApiServer()
            {
                Description = "Local",
                Url = $"https://localhost:7070",
            },
            new OpenApiServer()
            {
                Description = "Dev Apigw",
                Url = $"https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/crm-hub",
            },
            new OpenApiServer()
            {
                Description = "Prod Apigw",
                Url = $"https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/crm-hub",
            }
        });
    }, Assembly.Load("Sleekflow.CrmHub"), Assembly.Load("Sleekflow.CrmHub.Models"));
#else
    MvcModules.BuildApiBehaviors(builder);
#endif
    Modules.BuildHttpClients(builder.Services);
    Modules.BuildConfigs(builder.Services);
    Modules.BuildServices(builder.Services, Assembly.Load("Sleekflow.CrmHub.Models"));
    Modules.BuildTriggers(builder.Services);
    Modules.BuildServiceBusServices(
        builder.Services,
        null,
        null,
        Assembly.Load("Sleekflow.CrmHub.Models"));
    Modules.BuildCacheServices(builder.Services);
    Modules.BuildDbServices(builder.Services);
    Modules.BuildWorkerServices(builder.Services);
    MvcModules.BuildFuncServices(builder.Services, appName);
    CrmHubModules.BuildCrmHubDbServices(builder.Services);
    BuildCrmHubDbProcessorServices(builder.Services, typeof(Program));

    builder.Services.AddSingleton<HubspotIntegratorService>();
    builder.Services.AddSingleton<SalesforceIntegratorService>();
    builder.Services.AddSingleton<Dynamics365IntegratorService>();
    builder.Services.AddSingleton<GoogleSheetsIntegratorService>();
    builder.Services.AddSingleton<ZohoIntegratorService>();

    var app = builder.Build();

    // app.UseHttpsRedirection();
    app.UseAuthorization();
    app.MapControllers();
    HealthCheckMapping.MapHealthChecks(app);

#if SWAGGERGEN
    app.UseSwagger();
    app.UseSwaggerUI(
        options =>
        {
            var provider = app.Services.GetRequiredService<IApiVersionDescriptionProvider>();

            foreach (var description in provider.ApiVersionDescriptions)
            {
                options.SwaggerEndpoint(
                    $"/swagger/{description.GroupName}/swagger.json",
                    description.GroupName.ToUpperInvariant());
            }
        });
#endif

    ThreadPool.SetMinThreads(128, 128);
    ThreadPool.SetMaxThreads(512, 512);

    app.Run();

    return 0;
}
catch (Exception ex)
{
    Log.Fatal(ex, "Host terminated unexpectedly");
    return 1;
}
finally
{
    Log.CloseAndFlush();
}