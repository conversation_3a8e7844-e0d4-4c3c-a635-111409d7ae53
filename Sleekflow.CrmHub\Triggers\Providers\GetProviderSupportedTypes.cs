﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class GetProviderSupportedTypes : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetProviderSupportedTypes(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetProviderSupportedTypesInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public GetProviderSupportedTypesInput(string sleekflowCompanyId, string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
        }
    }

    public class GetProviderSupportedTypesOutput
    {
        [JsonProperty("supported_entity_types")]
        public List<GetSupportedTypesOutputSupportedType> SupportedEntityTypes { get; set; }

        [JsonConstructor]
        public GetProviderSupportedTypesOutput(List<GetSupportedTypesOutputSupportedType> supportedEntityTypes)
        {
            SupportedEntityTypes = supportedEntityTypes;
        }
    }

    public async Task<GetProviderSupportedTypesOutput> F(
        GetProviderSupportedTypesInput getProviderSupportedTypesInput)
    {
        var providerService = _providerSelector.GetProviderService(getProviderSupportedTypesInput.ProviderName);

        var supportedTypes = await providerService.GetSupportedTypesAsync(
            getProviderSupportedTypesInput.SleekflowCompanyId);

        return new GetProviderSupportedTypesOutput(supportedTypes.SupportedTypes);
    }
}