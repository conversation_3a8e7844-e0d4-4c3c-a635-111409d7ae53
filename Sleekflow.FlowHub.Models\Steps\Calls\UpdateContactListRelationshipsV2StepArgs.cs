﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactListRelationshipsV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.update-contact-list-relationships";

    [JsonProperty("list_ids")]
    public List<string>? ListIds { get; set; }

    [JsonProperty("removal_action_type")]
    public string? RemovalActionType { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Contact;

    [JsonConstructor]
    public UpdateContactListRelationshipsV2StepArgs(
        List<string>? listIds,
        string? removalActionType)
    {
        ListIds = listIds;
        RemovalActionType = removalActionType;
    }
}