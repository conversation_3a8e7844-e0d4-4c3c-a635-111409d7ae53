using MassTransit;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.Steps;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnExternalStepSubmittedEventConsumerDefinition
    : ConsumerDefinition<OnExternalStepSubmittedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnExternalStepSubmittedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 48;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 0;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(2);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnExternalStepSubmittedEventConsumer
    : IConsumer<OnExternalStepSubmittedEvent>
{
    private readonly IStepExecutorMatcher _stepExecutorMatcher;
    private readonly IStateService _stateService;
    private readonly IStepExecutionService _stepExecutionService;

    public OnExternalStepSubmittedEventConsumer(
        IStepExecutorMatcher stepExecutorMatcher,
        IStateService stateService,
        IStepExecutionService stepExecutionService)
    {
        _stepExecutorMatcher = stepExecutorMatcher;
        _stateService = stateService;
        _stepExecutionService = stepExecutionService;
    }

    public async Task Consume(ConsumeContext<OnExternalStepSubmittedEvent> context)
    {
        var @event = context.Message;

        var state = await _stateService.GetProxyStateAsync(@event.StateId);
        var step = @event.InternalStep;
        var externalStepId = $"external-step-{step.Id}";

        await _stepExecutionService.CreateStepExecutionAsync(
            state.Id,
            state.Identity,
            externalStepId,
            null,
            StepExecutionStatuses.Started,
            null,
            DateTimeOffset.UtcNow,
            null);

        await _stepExecutorMatcher.MatchExecutor(step).OnStepActivateAsync(
            state.WorkflowContext.SnapshottedWorkflow,
            state,
            step,
            new Stack<StackEntry>(),
            async (s, activationStatus) =>
            {
                await _stepExecutionService.CreateStepExecutionAsync(
                    s.Id,
                    s.Identity,
                    externalStepId,
                    null,
                    activationStatus,
                    null,
                    DateTimeOffset.UtcNow,
                    null);
            });
    }
}