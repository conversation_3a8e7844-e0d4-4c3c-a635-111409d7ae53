using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Microsoft.SemanticKernel.Connectors.AzureOpenAI;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator.Plugins;

public class DiagnosticsSummaryPlugin
{
    private readonly Kernel _kernel;
    private readonly IChatCompletionService _chatCompletionService;

    public DiagnosticsSummaryPlugin()
    {
        using var scope = Application.Host.Services.CreateScope();
        _kernel = scope.ServiceProvider.GetRequiredService<Kernel>();
        _chatCompletionService = _kernel.GetRequiredService<IChatCompletionService>(SemanticKernelExtensions.S_GPT_4_1_MINI);
    }

    public class DiagnosticsSummary
    {
        [JsonProperty("area_of_improvement")]
        public string AreaOfImprovement;

        [JsonProperty("occurences")]
        public int Occurences;

        [JsonConstructor]
        public DiagnosticsSummary(string areaOfImprovement, int occurences)
        {
            AreaOfImprovement = areaOfImprovement;
            Occurences = occurences;
        }
    }

    public async Task<DiagnosticsSummary[]> GetDiagnosticsSummary(ChatEvalResult[] evalResults)
    {
        var chatHistory = new ChatHistory
        {
            new ChatMessageContent(
                AuthorRole.System,
                """
                You are given a list of improvements that can be made. Aggregate similar improvements together and count their number of occurrences.

                Instructions:
                The input is a list of improvements separated by newlines.
                The output should be in descending order based on the occurence count.
                The output must be in English.
                The total number of occurences must be equal to the number of input lines.

                Required Output Format:
                [
                    {
                        "area_of_improvement": "string",
                        "occurences": "int"
                    }
                ]
                """),
            new ChatMessageContent(
                AuthorRole.User,
                """
                Could include more personal engagement techniques.
                """),
            new ChatMessageContent(
                AuthorRole.Assistant,
                """
                [
                    {
                        "area_of_improvement": "Improve personal engagement and build stronger connections with the customer",
                        "occurences": 1
                    },
                ]
                """),
        };

        var areasForImprovementList = evalResults.Select(
            evalResult => string.Join(
                " ",
                evalResult.AnswerScoringDiagnostic.QualitativeAnalysis.DetailedFeedback.AreasForImprovement.ToArray()));
        var areasOfImprovementText = string.Join("\n", areasForImprovementList);
        chatHistory.Add(
            new ChatMessageContent(
                AuthorRole.User,
                areasOfImprovementText));

        var executionSettings = new AzureOpenAIPromptExecutionSettings
        {
            FunctionChoiceBehavior = FunctionChoiceBehavior.Auto()
        };

        var messageContent = await _chatCompletionService.GetChatMessageContentAsync(
            chatHistory,
            executionSettings: executionSettings,
            kernel: _kernel);
        if (messageContent.Content == null)
        {
            throw new Exception("Failed to generate answer.");
        }

        var diagnosticsSummaries =
            JsonConvert.DeserializeObject<DiagnosticsSummary[]>(messageContent.Content)?.Take(5).ToArray()
            ?? throw new Exception("Failed to deserialize result.");
        return diagnosticsSummaries;
    }
}