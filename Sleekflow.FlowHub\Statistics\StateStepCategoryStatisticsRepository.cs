﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Statistics;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Statistics;

public interface IStateStepCategoryStatisticsRepository : IRepository<StateStepCategoryStatistics>
{
}

public sealed class StateStepCategoryStatisticsRepository
    : BaseRepository<StateStepCategoryStatistics>,
      IStateStepCategoryStatisticsRepository,
      IScopedService
{
    public StateStepCategoryStatisticsRepository(
        ILogger<StateStepCategoryStatisticsRepository> logger,
        IServiceProvider serviceProvider)
        : base(
            logger,
            serviceProvider)
    {
    }
}