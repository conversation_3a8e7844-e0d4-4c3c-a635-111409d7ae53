﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.WebScrapers;
using Sleekflow.IntelligentHub.WebScrapers;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.WebScrapers.WebScraperRuns;

[TriggerGroup(ControllerNames.WebScraperRuns)]
public class GetWebPageContent : ITrigger<GetWebPageContent.GetWebPageContentInput, GetWebPageContent.GetWebPageContentOutput>
{
    private readonly IWebScraperService _webScraperService;

    public GetWebPageContent(
        IWebScraperService webScraperService)
    {
        _webScraperService = webScraperService;
    }

    public class GetWebPageContentInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(WebScraperRun.PropertyNameApifyRunId)]
        public string ApifyRunId { get; set; }

        [Required]
        [JsonProperty(WebPage.PropertyNameWebPageId)]
        public string WebPageId { get; set; }

        [JsonConstructor]
        public GetWebPageContentInput(string sleekflowCompanyId, string apifyRunId, string webPageId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ApifyRunId = apifyRunId;
            WebPageId = webPageId;
        }
    }

    public class GetWebPageContentOutput
    {
        [JsonProperty("web_page_content")]
        public string Content { get; set; }

        [JsonConstructor]
        public GetWebPageContentOutput(string content)
        {
            Content = content;
        }
    }

    public async Task<GetWebPageContentOutput> F(GetWebPageContentInput getWebPageContentInput)
    {
        var content = await _webScraperService.GetWebPageContentAsync(
            getWebPageContentInput.SleekflowCompanyId,
            getWebPageContentInput.ApifyRunId,
            getWebPageContentInput.WebPageId);
        return new GetWebPageContentOutput(content);
    }
}