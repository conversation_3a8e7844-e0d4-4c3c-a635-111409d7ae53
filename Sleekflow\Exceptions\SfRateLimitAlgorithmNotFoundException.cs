﻿namespace Sleekflow.Exceptions;

public class SfRateLimitAlgorithmNotFoundException : ErrorCodeException
{
    public string AlgorithmName { get; }

    public SfRateLimitAlgorithmNotFoundException(string algorithmName)
        : base(
            ErrorCodeConstant.SfRateLimitAlgorithmNotFoundException,
            $"Rate limit algorithm '{algorithmName}' not found.")
    {
        AlgorithmName = algorithmName;
    }
}