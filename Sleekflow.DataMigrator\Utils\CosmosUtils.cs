﻿using System.Net;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json.Linq;
using Polly;
using Polly.Retry;

namespace Sleekflow.DataMigrator.Utils;

public static class CosmosUtils
{
    public static async IAsyncEnumerable<DatabaseProperties> GetDatabasesAsync(
        CosmosClient cosmosClient)
    {
        var itemQueryIterator = cosmosClient.GetDatabaseQueryIterator<DatabaseProperties>();
        while (itemQueryIterator.HasMoreResults)
        {
            var response = await itemQueryIterator.ReadNextAsync();

            foreach (var databaseProperties in response)
            {
                yield return databaseProperties;
            }
        }
    }

    public static async IAsyncEnumerable<ContainerProperties> GetContainersAsync(
        CosmosClient cosmosClient,
        string databaseId)
    {
        var itemQueryIterator = cosmosClient.GetDatabase(databaseId).GetContainerQueryIterator<ContainerProperties>();
        while (itemQueryIterator.HasMoreResults)
        {
            var response = await itemQueryIterator.ReadNextAsync();

            foreach (var containerProperties in response)
            {
                yield return containerProperties;
            }
        }
    }

    public static async Task<IReadOnlyList<string>?> GetContainerPartitionKeyPathsAsync(
        CosmosClient cosmosClient,
        string databaseId,
        string containerId)
    {
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(cosmosClient, databaseId))
        {
            if (containerId == containerProperties.Id)
            {
                return containerProperties.PartitionKeyPaths;
            }
        }

        return null;
    }

    public static async IAsyncEnumerable<Dictionary<string, object?>> GetObjectsAsync(
        Container container,
        string sqlQuery = "SELECT * FROM root c")
    {
        var itemQueryIterator = container.GetItemQueryIterator<Dictionary<string, object?>>(
            sqlQuery,
            requestOptions: new QueryRequestOptions
            {
                MaxConcurrency = 10, MaxItemCount = 100, MaxBufferedItemCount = 1000,
            });
        while (itemQueryIterator.HasMoreResults)
        {
            FeedResponse<Dictionary<string, object?>> response;
            while (true)
            {
                try
                {
                    response = await itemQueryIterator.ReadNextAsync();

                    break;
                }
                catch (CosmosException e)
                {
                    if (e.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(1));
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            foreach (var entity in response)
            {
                yield return entity;
            }
        }
    }

    public static async IAsyncEnumerable<Dictionary<string, object?>> GetObjectsAsync(
        Container container,
        QueryDefinition queryDefinition)
    {
        var itemQueryIterator = container.GetItemQueryIterator<Dictionary<string, object?>>(
            queryDefinition,
            requestOptions: new QueryRequestOptions
            {
                MaxConcurrency = 10, MaxItemCount = 100, MaxBufferedItemCount = 1000,
            });

        while (itemQueryIterator.HasMoreResults)
        {
            FeedResponse<Dictionary<string, object?>> response;
            while (true)
            {
                try
                {
                    response = await itemQueryIterator.ReadNextAsync();

                    break;
                }
                catch (CosmosException e)
                {
                    if (e.StatusCode == HttpStatusCode.TooManyRequests)
                    {
                        await Task.Delay(TimeSpan.FromSeconds(1));
                    }
                    else
                    {
                        throw;
                    }
                }
            }

            foreach (var entity in response)
            {
                yield return entity;
            }
        }
    }

    public static AsyncRetryPolicy GetDefaultRetryPolicy(string containerId)
    {
        var retryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                20,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 2)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    return TimeSpan.FromSeconds(2.37) * retryCount * new Random().Next(1, 3);
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    Console.WriteLine(
                        $"TooManyRequests retryCount=[{retryCount}], timeSpan=[{timeSpan}], containerName=[{containerId}]");

                    return Task.CompletedTask;
                });

        return retryPolicy;
    }

    public static string? GetValueFromDictionary(Dictionary<string, object?> dictionary, string[] keyParts, int index)
    {
        if (index >= keyParts.Length)
        {
            return null;
        }

        if (!dictionary.TryGetValue(keyParts[index], out var value) || value == null)
        {
            return null;
        }

        if (value is JObject jObject)
        {
            value = jObject.ToObject<Dictionary<string, object>>();
        }

        if (index == keyParts.Length - 1)
        {
            return value as string;
        }

        return value is Dictionary<string, object> subDictionary
            ? GetValueFromDictionary(subDictionary, keyParts, index + 1)
            : null;
    }

    public static async Task<bool> ContainerExistsAsync(Database database, string containerName)
    {
        try
        {
            var container = database.GetContainer(containerName);
            await container.ReadContainerAsync();
            return true;
        }
        catch (CosmosException ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
        {
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex.Message);
            return false;
        }
    }
}