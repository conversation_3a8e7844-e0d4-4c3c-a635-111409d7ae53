﻿<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Sleekflow.EmailHub" type="DotNetProject" factoryName=".NET Project">
    <option name="EXE_PATH" value="$PROJECT_DIR$/Sleekflow.EmailHub/bin/Debug/net8.0/Sleekflow.EmailHub.dll" />
    <option name="PROGRAM_PARAMETERS" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/Sleekflow.EmailHub" />
    <option name="PASS_PARENT_ENVS" value="1" />
    <envs>
      <env name="ASPNETCORE_ENVIRONMENT" value="Development" />
      <env name="ASPNETCORE_URLS" value="https://localhost:7099;http://localhost:7100" />
      <env name="CACHE_PREFIX" value="Sleekflow.EmailHub" />
      <env name="COSMOS_CHANGE_FEED_ENV_ID" value="default" />
      <env name="COSMOS_DATABASE_ID" value="db" />
      <env name="COSMOS_EMAIL_HUB_DB_DATABASE_ID" value="emailhubdb" />
      <env name="COSMOS_EMAIL_HUB_DB_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_EMAIL_HUB_DB_KEY" value="****************************************************************************************" />
      <env name="COSMOS_ENDPOINT" value="https://sleekflow2bd1537b.documents.azure.com:443/" />
      <env name="COSMOS_KEY" value="****************************************************************************************" />
      <env name="DISPOSABLE_PASSWORD" value="*********************************************************************" />
      <env name="DISPOSABLE_PORT" value="25" />
      <env name="DISPOSABLE_SERVER_NAME" value="smtp.sendgrid.net" />
      <env name="DISPOSABLE_USERNAME" value="apikey" />
      <env name="GMAIL_CLIENT_ID" value="369323807749-pc8q82kjfma91jgf9n4nl1rvbmmb44ef.apps.googleusercontent.com" />
      <env name="GMAIL_CLIENT_SECRET" value="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" />
      <env name="GMAIL_PROJECT_ID" value="emailgetstarted" />
      <env name="GMAIL_REDIRECT_URI" value="https://localhost:7099/GmailAuthentication/GmailAuthCallback" />
      <env name="GMAIL_SUBSCRIPTION_ID" value="MyPushSub" />
      <env name="GMAIL_TOPIC_ID" value="MyTopic" />
      <env name="LOGGER_AUTHENTICATION_ID" value="PxOFmtDmfRvHYCoGsuItWHqipxqn72YE0WxgLy7msPitr3TMgvFFtX1RY7yvnP6Mu+lx0HUGy+Z5Un4oshm9Lw==" />
      <env name="LOGGER_IS_LOG_ANALYTICS_ENABLED" value="FALSE" />
      <env name="LOGGER_WORKSPACE_ID" value="f0ea3579-8e0a-483f-81bb-62617cdd75a6" />
      <env name="LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED" value="FALSE" />
      <env name="LOGGER_GOOGLE_CLOUD_PROJECT_ID" value="cool-phalanx-404402" />
      <env name="LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON" value="************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" />
      <env name="MESSAGE_DATA_CONN_STR" value="DefaultEndpointsProtocol=https;AccountName=lxg8d38o3e;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net" />
      <env name="MESSAGE_DATA_CONTAINER_NAME" value="message-data" />
      <env name="OUTLOOK_CLIENT_ID" value="********-5449-4aad-9618-8db8100f821c" />
      <env name="OUTLOOK_CLIENT_SECRET" value="****************************************" />
      <env name="OUTLOOK_NOTIFICATION_URL" value="https://14de-42-200-140-118.ngrok-free.app/OutlookSubscription/OutlookSubscriptionCallBack" />
      <env name="OUTLOOK_REDIRECT_URI" value="https://14de-42-200-140-118.ngrok-free.app/OutlookAuthentication/OutlookAuthCallback" />
      <env name="OUTLOOK_TENANT" value="d66fa1cc-347d-42e9-9444-19c5fd0bbcce" />
      <env name="REDIS_CONN_STR" value="sleekflow-redis739dbd6c.redis.cache.windows.net:6380,password=LSpaOPbm5b308TOUYaMDQwfDVUQZV7OODAzCaBAySj0=,ssl=True,abortConnect=False" />
      <env name="SERVICE_BUS_CONN_STR" value="Endpoint=sb://sleekflow-local.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=ZKDnptWyDBxBPASM36H7o+NrnyDqtK7L3+ASbPJHFzw=" />
      <env name="EVENT_HUB_CONN_STR" value="Endpoint=sb://sleekflowlocal.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=jhlCshBxrz+WK7I90i8w1GKoVTOmSaGBO+AEhDcsaYU=" />
      <env name="WORKER_FUNCTIONS_KEY" value="PLACEHOLDER" />
      <env name="APPLICATIONINSIGHTS_IS_TELEMETRY_TRACER_ENABLED" value="FALSE" />
      <env name="APPLICATIONINSIGHTS_IS_SAMPLING_DISABLED" value="FALSE" />
    </envs>
    <option name="USE_EXTERNAL_CONSOLE" value="0" />
    <option name="USE_MONO" value="0" />
    <option name="RUNTIME_ARGUMENTS" value="" />
    <option name="PROJECT_PATH" value="$PROJECT_DIR$/Sleekflow.EmailHub/Sleekflow.EmailHub.csproj" />
    <option name="PROJECT_EXE_PATH_TRACKING" value="1" />
    <option name="PROJECT_ARGUMENTS_TRACKING" value="1" />
    <option name="PROJECT_WORKING_DIRECTORY_TRACKING" value="1" />
    <option name="PROJECT_KIND" value="DotNetCore" />
    <option name="PROJECT_TFM" value="net8.0" />
    <method v="2">
      <option name="Build" />
    </method>
  </configuration>
</component>