using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Disposable.Authentications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Disposable.Subscriptions;
using Sleekflow.EmailHub.Models.Subscriptions;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;

namespace Sleekflow.EmailHub.Disposable.Subscriptions;

public interface IDisposableSubscriptionService : IEmailSubscriptionService
{
}

public class DisposableSubscriptionService
    : IScopedService, IDisposableSubscriptionService
{
    private readonly ILogger<DisposableSubscriptionService> _logger;
    private readonly IProviderConfigService _providerConfigService;
    private readonly IDisposableAuthenticationService _disposableAuthenticationService;

    public DisposableSubscriptionService(
        ILogger<DisposableSubscriptionService> logger,
        IProviderConfigService providerConfigService,
        IDisposableAuthenticationService disposableAuthenticationService)
    {
        _logger = logger;
        _providerConfigService = providerConfigService;
        _disposableAuthenticationService = disposableAuthenticationService;
    }

    public async Task<EmailSubscription> GetSubscriptionAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Disposable,
            cancellationToken: cancellationToken);

        if (providerConfig == null || !providerConfig.EmailSubscription.IsSubscribed)
        {
            _logger.LogError(
                "GetSubscriptionAsync fails due to not subscribed: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                emailAddress,
                sleekflowCompanyId);

            throw new SfUnauthorizedException();
        }

        return providerConfig.EmailSubscription;
    }

    public async Task SubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? subscriptionMetadata,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Started SubscribeToAnEmailAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        var authentication = await _disposableAuthenticationService.GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken);

        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Disposable,
            cancellationToken: cancellationToken);

        providerConfig.EmailSubscription.IsSubscribed = true;
        providerConfig.EmailSubscription.LastSubscriptionTime = DateTimeOffset.UtcNow;
        providerConfig.EmailSubscription.EmailSubscriptionMetadata =
            new DisposableSubscriptionMetadata(ProviderNames.Disposable, ProviderNames.Disposable);

        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);

        _logger.LogInformation(
            "SubscribeToAnEmail successes: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);
    }

    public async Task UnsubscribeAtEmailAddressAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        _ = await _disposableAuthenticationService.GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken);

        var providerConfig = await _providerConfigService.GetOrCreateProviderConfigAsync(
            sleekflowCompanyId,
            emailAddress,
            ProviderNames.Disposable,
            cancellationToken: cancellationToken);

        providerConfig.EmailSubscription.IsSubscribed = false;
        await _providerConfigService.UpsertAsync(providerConfig, cancellationToken);

        _logger.LogInformation(
            "UnSubscribeToAnEmail successes: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);
    }

    public Task RenewEmailSubscriptionAsync(CancellationToken cancellationToken)
    {
        // intentionally unimplemented
        throw new NotImplementedException();
    }

    public async Task<List<string>> FilterSubscribedCompanies(
        List<string> companyIds,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var result = new List<string>();

        foreach (var companyId in companyIds)
        {
            try
            {
                _ = await GetSubscriptionAsync(
                    companyId,
                    emailAddress,
                    cancellationToken);
                result.Add(companyId);
            }
            catch (Exception)
            {
                // ignored unsubscribed company
            }
        }

        return result;
    }
}