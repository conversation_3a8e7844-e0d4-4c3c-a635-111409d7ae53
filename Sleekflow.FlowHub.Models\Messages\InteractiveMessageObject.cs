using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

namespace Sleekflow.FlowHub.Models.Messages;

public class InteractiveMessageObject : BaseMessageObject
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("header")]
    public InteractiveMessageObjectHeader? Header { get; set; }

    [JsonProperty("body")]
    public InteractiveMessageObjectBody? Body { get; set; }

    [JsonProperty("footer")]
    public InteractiveMessageObjectFooter? Footer { get; set; }

    [JsonProperty("action")]
    public InteractiveMessageObjectAction? Action { get; set; }

    [JsonConstructor]
    public InteractiveMessageObject(
        string type,
        InteractiveMessageObjectHeader? header,
        InteractiveMessageObjectBody? body,
        InteractiveMessageObjectFooter? footer,
        InteractiveMessageObjectAction? action)
    {
        Type = type;
        Header = header;
        Body = body;
        Footer = footer;
        Action = action;
    }
}

public class InteractiveMessageObjectHeader
{
    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonProperty("video")]
    public VideoMessageObject Video { get; set; }

    [JsonProperty("image")]
    public ImageMessageObject Image { get; set; }

    [JsonProperty("document")]
    public DocumentMessageObject Document { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectHeader(
        string type,
        string text,
        VideoMessageObject video,
        ImageMessageObject image,
        DocumentMessageObject document)
    {
        Type = type;
        Text = text;
        Video = video;
        Image = image;
        Document = document;
    }
}

public class InteractiveMessageObjectBody
{
    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectBody(string text)
    {
        Text = text;
    }
}

public class InteractiveMessageObjectFooter
{
    [JsonProperty("text")]
    public string Text { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectFooter(string text)
    {
        Text = text;
    }
}

public class InteractiveMessageObjectAction
{
    [JsonProperty("button")]
    public string? Button { get; set; }

    [JsonProperty("buttons")]
    public List<InteractiveMessageObjectActionButton>? Buttons { get; set; }

    [JsonProperty("sections")]
    public List<InteractiveMessageObjectActionSection>? Sections { get; set; }

    [JsonProperty("catalog_id")]
    public string? CatalogId { get; set; }

    [JsonProperty("product_retailer_id")]
    public string? ProductRetailerId { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectAction(
        string? button,
        List<InteractiveMessageObjectActionButton>? buttons,
        List<InteractiveMessageObjectActionSection>? sections,
        string? catalogId,
        string? productRetailerId)
    {
        Button = button;
        Buttons = buttons;
        Sections = sections;
        CatalogId = catalogId;
        ProductRetailerId = productRetailerId;
    }
}

public class InteractiveMessageObjectActionButton
{
    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("reply")]
    public InteractiveMessageObjectActionButtonReply? Reply { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectActionButton(string? type, InteractiveMessageObjectActionButtonReply? reply)
    {
        Type = type;
        Reply = reply;
    }
}

public class InteractiveMessageObjectActionButtonReply
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("title")]
    public string? Title { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectActionButtonReply(string? id, string? title)
    {
        Id = id;
        Title = title;
    }
}

public class InteractiveMessageObjectActionSection
{
    [JsonProperty("title")]
    public string? Title { get; set; }

    [JsonProperty("product_items")]
    public List<InteractiveMessageObjectActionSectionProductItem>? ProductItems { get; set; }

    [JsonProperty("rows")]
    public List<InteractiveMessageObjectActionSectionRow>? Rows { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectActionSection(
        string? title,
        List<InteractiveMessageObjectActionSectionProductItem>? productItems,
        List<InteractiveMessageObjectActionSectionRow>? rows)
    {
        Title = title;
        ProductItems = productItems;
        Rows = rows;
    }
}

public class InteractiveMessageObjectActionSectionProductItem
{
    [JsonProperty("product_retailer_id")]
    public string? ProductRetailerId { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectActionSectionProductItem(string? productRetailerId)
    {
        ProductRetailerId = productRetailerId;
    }
}

public class InteractiveMessageObjectActionSectionRow
{
    [JsonProperty("id")]
    public string? Id { get; set; }

    [JsonProperty("title")]
    public string? Title { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonConstructor]
    public InteractiveMessageObjectActionSectionRow(string? id, string? title, string? description)
    {
        Id = id;
        Title = title;
        Description = description;
    }
}