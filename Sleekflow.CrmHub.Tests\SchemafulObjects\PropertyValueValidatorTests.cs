﻿using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.SchemafulObjects.Dtos;
using Sleekflow.CrmHub.SchemafulObjects.Utils;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Tests.SchemafulObjects;

public class PropertyValueValidatorTests
{
    private const string SingleLineTextPropertyId = "single_line_text";
    private const string NumericPropertyId = "numeric";
    private const string DecimalPropertyId = "decimal";
    private const string SingleChoicePropertyId = "single_choice";
    private const string MultipleChoicePropertyId = "multiple_choice";
    private const string BooleanPropertyId = "boolean";
    private const string DatePropertyId = "date";
    private const string DateTimePropertyId = "datetime";
    private const string ArrayObjectPropertyId = "array_object";
    private const string ImagePropertyId = "image";

    private const string OptionId1 = "option_id_1";
    private const string OptionId2 = "option_id_2";
    private const string OptionId3 = "option_id_3";

    private readonly List<Property> _properties;
    private readonly List<Property> _nullableProperties = new ();

    private CrmHubConfig MockCrmHubConfig { get; set; }

    private PropertyValueValidator PropertyValueValidator { get; set; }

    private record MockPropertyValue
    {
        [JsonProperty("property_values")]
        public Dictionary<string, object?> PropertyValues { get; set; }

        [JsonConstructor]
        public MockPropertyValue(Dictionary<string, object?> propertyValues)
        {
            PropertyValues = propertyValues;
        }
    }

    public PropertyValueValidatorTests()
    {
        PropertyValueValidator = new PropertyValueValidator(NullLogger<PropertyValueValidator>.Instance);

        MockCrmHubConfig = new CrmHubConfig(
            "id",
            new UsageLimit(10, 10, 10, 10, 10),
            UsageLimitOffset.Default(),
            FeatureAccessibilitySettings.Default(),
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            "id",
            null,
            null);

        var options = new List<Option>
        {
            new Option(OptionId1, "first", 0),
            new Option(OptionId2, "second", 1),
            new Option(OptionId3, "third", 2),
        };

        _properties = new List<Property>
        {
            new Property(
                SingleLineTextPropertyId,
                SingleLineTextPropertyId,
                SingleLineTextPropertyId,
                new SingleLineTextDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                NumericPropertyId,
                NumericPropertyId,
                NumericPropertyId,
                new NumericDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DecimalPropertyId,
                DecimalPropertyId,
                DecimalPropertyId,
                new DecimalDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                SingleChoicePropertyId,
                SingleChoicePropertyId,
                SingleChoicePropertyId,
                new SingleChoiceDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                options),
            new Property(
                MultipleChoicePropertyId,
                MultipleChoicePropertyId,
                MultipleChoicePropertyId,
                new MultipleChoiceDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                options),
            new Property(
                BooleanPropertyId,
                BooleanPropertyId,
                BooleanPropertyId,
                new BooleanDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DatePropertyId,
                DatePropertyId,
                DatePropertyId,
                new DateDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DateTimePropertyId,
                DateTimePropertyId,
                DateTimePropertyId,
                new DateTimeDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                ImagePropertyId,
                ImagePropertyId,
                ImagePropertyId,
                new ImageDataType(),
                false,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null)
        };

        _properties.ForEach(
            p =>
            {
                _nullableProperties.Add(
                    new Property(
                        p.Id,
                        p.DisplayName,
                        p.UniqueName,
                        p.DataType,
                        false,
                        true,
                        true,
                        true,
                        0,
                        null,
                        DateTimeOffset.UtcNow,
                        p.Options));
            });
    }

    [Test]
    public void BasicDataTypeTests()
    {
        var validInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, "test single line..."
            },
            {
                NumericPropertyId, 12345
            },
            {
                DecimalPropertyId, 123.45
            },
            {
                BooleanPropertyId, true
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(validInputValues));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var nullInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, null
            },
            {
                NumericPropertyId, null
            },
            {
                DecimalPropertyId, null
            },
            {
                BooleanPropertyId, null
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(nullInputValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });
    }

    [Test]
    public void BasicDataTypeInvalidValueTests()
    {
        var nullInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, null
            },
            {
                NumericPropertyId, null
            },
            {
                DecimalPropertyId, null
            },
            {
                BooleanPropertyId, null
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(nullInputValues));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var emptyInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, string.Empty
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(emptyInputValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var invalidSingleLineTextValue = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, false
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(invalidSingleLineTextValue));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var invalidNumericValue = new Dictionary<string, object?>
        {
            {
                NumericPropertyId, 123.45
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(invalidNumericValue));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var invalidDecimalValue = new Dictionary<string, object?>
        {
            {
                DecimalPropertyId, "aaaaaa"
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(invalidDecimalValue));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var invalidBooleanValue = new Dictionary<string, object?>
        {
            {
                BooleanPropertyId, "false"
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(invalidBooleanValue));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });
    }

    [Test]
    public void DropdownDataTypeTests()
    {
        var validInputValues = new Dictionary<string, object?>
        {
            {
                SingleChoicePropertyId, OptionId1
            },
            {
                MultipleChoicePropertyId, new List<string>
                {
                    OptionId1, OptionId2
                }
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(validInputValues));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var nullInputValues = new Dictionary<string, object?>
        {
            {
                SingleChoicePropertyId, null
            },
            {
                MultipleChoicePropertyId, null
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(nullInputValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });
    }

    [Test]
    public void DropdownDataTypeInvalidValueTests()
    {
        var nullInputValues = new Dictionary<string, object?>
        {
            {
                SingleChoicePropertyId, string.Empty
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(nullInputValues));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        nullInputValues = new Dictionary<string, object?>
        {
            {
                MultipleChoicePropertyId, new List<string>()
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(nullInputValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var unexistedOptionValues = new Dictionary<string, object?>
        {
            {
                SingleChoicePropertyId, "abc"
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(unexistedOptionValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        unexistedOptionValues = new Dictionary<string, object?>
        {
            {
                MultipleChoicePropertyId, new List<string>
                {
                    OptionId1,
                    "abc"
                }
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(unexistedOptionValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var duplicatedOptionValues = new Dictionary<string, object?>
        {
            {
                MultipleChoicePropertyId, new List<string>
                {
                    OptionId1,
                    OptionId1,
                    OptionId2
                }
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(duplicatedOptionValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var illegalDataTypeValues = new Dictionary<string, object?>
        {
            {
                MultipleChoicePropertyId, new List<object>
                {
                    12345,
                    OptionId1,
                    OptionId2
                }
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(illegalDataTypeValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        illegalDataTypeValues = new Dictionary<string, object?>
        {
            {
                SingleChoicePropertyId, 12345
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(illegalDataTypeValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });
    }

    [Test]
    public void DateTimeDataTypeTests()
    {
        var validInputValues = new Dictionary<string, object?>
        {
            {
                DatePropertyId, DateTimeOffset.UtcNow
            },
            {
                DateTimePropertyId, DateTimeOffset.UtcNow
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(validInputValues));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var nullInputValues = new Dictionary<string, object?>
        {
            {
                DatePropertyId, null
            },
            {
                DateTimePropertyId, null
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(nullInputValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        var invalidValues = new Dictionary<string, object?>
        {
            {
                DatePropertyId, string.Empty
            }
        };

        jsonString = JsonConvert.SerializeObject(new MockPropertyValue(invalidValues));

        mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });
    }

    [Test]
    public void MissingRequiredPropertyTests()
    {
        var validInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, "test single line..."
            },
            {
                NumericPropertyId, 12345
            },
            {
                DecimalPropertyId, 123.45
            },
            {
                BooleanPropertyId, true
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(validInputValues));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(_properties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });
    }

    [Test]
    public void NonExistedPropertyTests()
    {
        var mockNonExistedPropertyId = "non_existed_property_id";

        var validInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, "test single line..."
            },
            {
                NumericPropertyId, 12345
            },
            {
                DecimalPropertyId, 123.45
            },
            {
                BooleanPropertyId, true
            },
            {
                mockNonExistedPropertyId, 1
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(validInputValues));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(_nullableProperties, mockInput!.PropertyValues, string.Empty, MockCrmHubConfig);
            });

        Assert.That(mockInput!.PropertyValues.ContainsKey(mockNonExistedPropertyId), Is.False);
    }

    [Test]
    public void ValidateArrayPropertyValue_WithCorrectInput_ShouldNotThrowError()
    {
        const string mockInnerSchemafulObjectId1 = "mockInnerSchemafulObjectId1";
        const string mockInnerSchemafulObjectId2 = "mockInnerSchemafulObjectId2";

        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValuesForInnerSchemafulObject = new Dictionary<string, object?>
        {
            {
                NumericPropertyId, 12345
            },
            {
                DatePropertyId, DateTimeOffset.UtcNow
            },
            {
                MultipleChoicePropertyId, new List<object>
                {
                    OptionId1, OptionId2
                }
            }
        };

        var propertyValueForArrayObject = new List<InnerSchemafulObjectDto>
        {
            new InnerSchemafulObjectDto(
                mockInnerSchemafulObjectId1,
                propertyValuesForInnerSchemafulObject,
                DateTimeOffset.Now),
            new InnerSchemafulObjectDto(
                mockInnerSchemafulObjectId2,
                propertyValuesForInnerSchemafulObject,
                DateTimeOffset.Now)
        };

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], MockCrmHubConfig);
            });
    }

    [Test]
    public void ValidateArrayPropertyValue_WithIncorrectInput_ShouldThrowError()
    {
        const string mockInnerSchemafulObjectId1 = "mockInnerSchemafulObjectId1";

        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValuesForInnerSchemafulObject = new Dictionary<string, object?>
        {
            {
                NumericPropertyId, 12345
            },
            {
                DatePropertyId, DateTimeOffset.UtcNow
            },
            {
                MultipleChoicePropertyId, new List<object>
                {
                    OptionId1, OptionId2
                }
            }
        };

        // single object instead of an array/list
        var propertyValueForArrayObject = new InnerSchemafulObjectDto(
            mockInnerSchemafulObjectId1,
            propertyValuesForInnerSchemafulObject,
            DateTimeOffset.Now);

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], MockCrmHubConfig);
            });
    }

    [Test]
    public void ValidateArrayPropertyValue_WithIncorrectInnerInput_ShouldThrowError()
    {
        const string mockInnerSchemafulObjectId1 = "mockInnerSchemafulObjectId1";
        const string mockInnerSchemafulObjectId2 = "mockInnerSchemafulObjectId2";

        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValuesForInnerSchemafulObject = new Dictionary<string, object?>
        {
            {
                NumericPropertyId, "12345"
            }
        };

        var propertyValueForArrayObject = new List<InnerSchemafulObjectDto>
        {
            new InnerSchemafulObjectDto(
                mockInnerSchemafulObjectId1,
                propertyValuesForInnerSchemafulObject,
                DateTimeOffset.Now),
            new InnerSchemafulObjectDto(
                mockInnerSchemafulObjectId2,
                propertyValuesForInnerSchemafulObject,
                DateTimeOffset.Now)
        };

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], MockCrmHubConfig);
            });
    }

    [Test]
    public void ValidateArrayPropertyValue_WithIncorrectInnerInput2_ShouldThrowError()
    {
        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValueForArrayObject = new List<InnerSchemafulObjectInputDto>
        {
            new InnerSchemafulObjectInputDto(
                string.Empty, // Id set to empty
                new Dictionary<string, object?>
                {
                    {
                        NumericPropertyId, 12345
                    }
                },
                DateTimeOffset.UtcNow)
        };

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], MockCrmHubConfig);
            });
    }

    [Test]
    public void ValidateArrayPropertyValue_WithIncorrectInnerInput3_ShouldThrowError()
    {
        const string mockInnerSchemafulObjectId1 = "mockInnerSchemafulObjectId1";

        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValueForArrayObject = new List<InnerSchemafulObjectInputDto>
        {
            new InnerSchemafulObjectInputDto(
                mockInnerSchemafulObjectId1,
                new Dictionary<string, object?>
                {
                    {
                        NumericPropertyId, 12345
                    }
                },
                null) // createdAt set to null
        };

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));
        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], MockCrmHubConfig);
            });
    }

    [Test]
    public void ValidateArrayPropertyValue_MissingRequiredInnerProperty_ShouldThrowError()
    {
        const string mockInnerSchemafulObjectId1 = "mockInnerSchemafulObjectId1";

        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValueForArrayObject = new List<InnerSchemafulObjectInputDto>
        {
            new InnerSchemafulObjectInputDto(
                mockInnerSchemafulObjectId1,
                new Dictionary<string, object?>
                {
                    {
                        DatePropertyId, DateTimeOffset.UtcNow
                    }
                },
                DateTimeOffset.UtcNow) // createdAt set to null
        };

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));
        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfValidationException>(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], MockCrmHubConfig);
            });
    }

    [Test]
    public void ValidateArrayPropertyValue_ExceedArraySize_ShouldThrowError()
    {
        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValuesForInnerSchemafulObject = new Dictionary<string, object?>
        {
            {
                NumericPropertyId, 12345
            }
        };

        var propertyValueForArrayObject = new List<InnerSchemafulObjectDto>();
        for (int i = 0; i < 11; i++)
        {
            propertyValueForArrayObject.Add(
                new InnerSchemafulObjectDto(
                    $"id{i}",
                    propertyValuesForInnerSchemafulObject,
                    DateTimeOffset.Now));
        }

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.Throws<SfCrmHubExceedUsageException>(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], MockCrmHubConfig);
            });
    }

    [Test]
    public void ValidateArrayPropertyValue_WithUsageLimitOffset_ShouldNotThrow()
    {
        var crmHubConfig = new CrmHubConfig(
            "id",
            new UsageLimit(10, 10, 10, 10, 10),
            new UsageLimitOffset(0, 0, 0,0 , 5),
            FeatureAccessibilitySettings.Default(),
            null,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            "id",
            null,
            null);
        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValuesForInnerSchemafulObject = new Dictionary<string, object?>
        {
            {
                NumericPropertyId, 12345
            }
        };

        var propertyValueForArrayObject = new List<InnerSchemafulObjectDto>();
        for (int i = 0; i < 13; i++)
        {
            propertyValueForArrayObject.Add(
                new InnerSchemafulObjectDto(
                    $"id{i}",
                    propertyValuesForInnerSchemafulObject,
                    DateTimeOffset.Now));
        }

        var propertyValuesInput = new Dictionary<string, object?>
        {
            {
                ArrayObjectPropertyId, propertyValueForArrayObject
            }
        };

        var jsonString = JsonConvert.SerializeObject(new MockPropertyValue(propertyValuesInput));

        var mockInput = JsonConvert.DeserializeObject<MockPropertyValue>(jsonString);

        Assert.DoesNotThrow(
            () =>
            {
                PropertyValueValidator.Validate(arrayObjectProperty, mockInput!.PropertyValues[ArrayObjectPropertyId], crmHubConfig);
            });
    }

    [Test]
    public void ValidateImagePropertyValue_WithCorrectInput_ShouldNotThrowError()
    {
        var imageProperty = _properties.First(p => p.DataType is ImageDataType);

        // property value - with url only
        {
            var propertyValue = new { download_url = "https://www.example.com/image.jpg" };
            var jsonString = JsonConvert.SerializeObject(propertyValue);

            Assert.DoesNotThrow(
                () =>
                {
                    PropertyValueValidator.Validate(imageProperty, jsonString, MockCrmHubConfig);
                });
        }
        {
            var propertyValue = new { download_url = "https://s0f5f5fa7.blob.core.windows.net/image-container/39ee5f12-7997-45fa-a960-e4feecba425c/kXSpxE6dX1Y5LDD?sv=2024-08-04&se=9999-12-31T23%3A59%3A59Z&sr=b&sp=r&sig=cjguGLMGNSUET6Pb2hHPCceHYawxsku4HhbR7ZdYkXc%3D" };
            var jsonString = JsonConvert.SerializeObject(propertyValue);

            Assert.DoesNotThrow(
                () =>
                {
                    PropertyValueValidator.Validate(imageProperty, jsonString, MockCrmHubConfig);
                });
        }

        // property value - with ImageDto
        {
            var propertyValue = new ImageDto(
                "https://www.example.com/image.jpg",
                    new PublicBlobDto(
                        "Image",
                        "sample_blob_name",
                        null,
                        null,
                        null));
            var jsonString = JsonConvert.SerializeObject(propertyValue);

            Assert.DoesNotThrow(
                () =>
                {
                    PropertyValueValidator.Validate(imageProperty, jsonString, MockCrmHubConfig);
                });
        }

        // property value - with null value
        {
            Assert.DoesNotThrow(
                () =>
                {
                    PropertyValueValidator.Validate(imageProperty, null, MockCrmHubConfig);
                });
        }
    }

    [Test]
    public void ValidateImagePropertyValue_WithInCorrectInput_ShouldThrowError()
    {
        var imageProperty = _properties.First(p => p.DataType is ImageDataType);

        // property value - with invalid url
        {
            var propertyValue = new { download_url = "https://example.com/invalid space" };
            var jsonString = JsonConvert.SerializeObject(propertyValue);

            Assert.Throws<SfValidationException>(
                () =>
                {
                    PropertyValueValidator.Validate(imageProperty, jsonString, MockCrmHubConfig);
                });
        }

        // property value - without url
        {
            var propertyValue = new ImageDto(
                string.Empty,
                new PublicBlobDto(
                    "Image",
                    "sample_blob_name",
                    null,
                    null,
                    null));
            var jsonString = JsonConvert.SerializeObject(propertyValue);

            Assert.Throws<SfValidationException>(
                () =>
                {
                    PropertyValueValidator.Validate(imageProperty, jsonString, MockCrmHubConfig);
                });
        }

        // property value - with empty string input
        {
            Assert.Throws<SfValidationException>(
                () =>
                {
                    PropertyValueValidator.Validate(imageProperty, string.Empty, MockCrmHubConfig);
                });
        }
    }

    private Property GenerateArrayObjectProperty()
    {
        var properties = new List<Property>
        {
            new Property(
                NumericPropertyId,
                "Test Numeric",
                "test_numeric",
                new NumericDataType(),
                true,
                false,
                true,
                true,
                2,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DatePropertyId,
                "Test Date",
                "test_numeric",
                new DateDataType(),
                false,
                true,
                true,
                true,
                5,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                MultipleChoicePropertyId,
                "Test MultipleChoice",
                MultipleChoicePropertyId,
                new MultipleChoiceDataType(),
                false,
                true,
                true,
                true,
                7,
                null,
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(OptionId1, "option 1", 0),
                    new Option(OptionId2, "option 2", 1),
                    new Option(OptionId3, "option 3", 2)
                })
        };

        var innerSchema = new InnerSchema(properties);

        return new Property(
            ArrayObjectPropertyId,
            "Test ArrayObject",
            "test_arrayobject",
            new ArrayObjectDataType(innerSchema),
            false,
            false,
            true,
            true,
            1,
            null,
            DateTimeOffset.UtcNow,
            null);
    }
}