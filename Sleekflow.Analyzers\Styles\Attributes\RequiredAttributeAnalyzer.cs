using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.CSharp;
using Microsoft.CodeAnalysis.CSharp.Syntax;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class RequiredAttributeAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1102";
    public const string Category = "Design";

    private static readonly LocalizableString Title =
        "Non-nullable properties in Input classes should have [Required] attribute";

    private static readonly LocalizableString MessageFormat = "The property '{0}' should have the [Required] attribute";

    private static readonly LocalizableString Description =
        "Non-nullable properties in Input classes should have [Required] attribute.";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSyntaxNodeAction(AnalyzeNode, SyntaxKind.PropertyDeclaration);
    }

    private static void AnalyzeNode(SyntaxNodeAnalysisContext context)
    {
        var propertyDeclaration = (PropertyDeclarationSyntax) context.Node;
        var propertySymbol = context.SemanticModel.GetDeclaredSymbol(propertyDeclaration);

        if (propertySymbol == null
            || propertySymbol.ContainingType == null)
        {
            return;
        }

        if (!propertySymbol.ContainingType.Name.EndsWith("Input"))
        {
            return;
        }

        var nullableType = context.SemanticModel.Compilation.GetTypeByMetadataName("System.Nullable`1");
        if (propertySymbol.Type is INamedTypeSymbol namedType
            && SymbolEqualityComparer.Default.Equals(namedType.ConstructedFrom, nullableType))
        {
            return;
        }

        if (propertySymbol.Type.IsReferenceType
            && propertySymbol.Type.NullableAnnotation == NullableAnnotation.Annotated)
        {
            return;
        }

        var requiredAttribute = propertySymbol
            .GetAttributes()
            .FirstOrDefault(a => a.AttributeClass?.Name == "RequiredAttribute");

        if (requiredAttribute != null)
        {
            return;
        }

        var diagnostic = Diagnostic.Create(Rule, propertyDeclaration.GetLocation(), propertySymbol.Name);
        context.ReportDiagnostic(diagnostic);
    }
}