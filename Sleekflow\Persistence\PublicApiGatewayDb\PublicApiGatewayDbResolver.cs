using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.PublicApiGatewayDb;

public interface IPublicApiGatewayDbResolver : IContainerResolver
{
}

public class PublicApiGatewayDbResolver : IPublicApiGatewayDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public PublicApiGatewayDbResolver(IPublicApiGatewayDbConfig publicApiGatewayDbConfig)
    {
        _cosmosClient = new CosmosClient(
            publicApiGatewayDbConfig.Endpoint,
            publicApiGatewayDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);
        return database.GetContainer(containerId);
    }
}