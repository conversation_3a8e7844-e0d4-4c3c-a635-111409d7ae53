using MassTransit.Testing;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Models.Chats;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class AgentIntegrationTests
{
    private ITestHarness _harness;

    [SetUp]
    public void Setup()
    {
        var provider = Application.InMemoryBusHost.Server.Services;
        _harness = provider.GetRequiredService<ITestHarness>();
    }

    private const string SleekflowCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";

    private const string LeadQualification =
        "- Evaluate the conversation using Rating (High: indicates strong intent to purchase, Medium: indicates moderate interest, Low: indicates low interest), Score (0–100, where 100 is high interest, 0-10 this would be intercated that the user would never instreast), and Reason (a brief explanation of the user's intent and interest level based on chat history, within 50 words).";

    private const string CategoryMatch =
        "- Evaluate the conversation using Rating (High: continue, Medium: moderate interest but continue, Low: prefer human or end), Score (0–100, where 100 is high interest), and Reason (a brief explanation of the user's intent and interest level based on chat history, within 50 words).";

    [Test]
    [TestCase("[]")]
    [TestCase(
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"Hi, I'm interested in purchasing the SleekFlow Plan.\"},{\"bot\":\"Great choice! The SleekFlow Plan is designed to enhance your workflow with advanced features. Would you like to hear more about what's included?\"},{\"user\":\"Yes, please. What features are included in the SleekFlow Plan?\"},{\"bot\":\"The SleekFlow Plan includes unlimited project boards, advanced analytics, priority customer support, and integration with various third-party apps. It’s priced at $99 per month. Would you like to proceed with the purchase or need more information?\"},{\"user\":\"That sounds good. Can I get a discount if I subscribe for a year instead of monthly?\"}]")]
    [TestCase(
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"/clear\"},{\"user\":\"I'm looking for a good Italian restaurant nearby.\"}]")]
    public async Task AgentRecommendedReplyTest(string chatEntries)
    {
        await _harness.Bus.Publish(
            new GetAgentRecommendedReplyEvent(
                string.Empty,
                string.Empty,
                new Stack<StackEntry>(),
                SleekflowCompanyId,
                string.Empty,
                JsonConvert.DeserializeObject<List<SfChatEntry>>(chatEntries)!));

        Assert.That(await _harness.Consumed.Any<GetAgentRecommendedReplyEvent>());
    }

    [Test]
    [TestCase("[]")]
    [TestCase(
        "[{\"bot\":\"Hi! How can I help you today?\"},{\"user\":\"I'm looking for a good Italian restaurant nearby.\"},{\"bot\":\"Sure! There's 'Luigi's Italian Bistro' just 2 miles away with excellent reviews. Would you like the address and contact information?\"},{\"user\":\"Yes, please.\"},{\"bot\":\"Address: 123 Main Street, Cityville. Phone: (*************. Would you like to make a reservation?\"},{\"user\":\"Yes, a table for two at 7 PM tonight.\"},{\"bot\":\"Reservation confirmed for two at Luigi's Italian Bistro on [Date] at 7 PM. Enjoy your meal!\"},{\"user\":\"Thank you!\"},{\"bot\":\"You're welcome! Let me know if you need anything else.\"}]")]
    [TestCase(
        "[{\"bot\":\"Hello! What can I do for you today?\"},{\"user\":\"Can you tell me the weather forecast for tomorrow in New York?\"},{\"bot\":\"Certainly! Tomorrow in New York, expect partly cloudy skies with a high of 75°F and a low of 60°F. Would you like more details?\"},{\"user\":\"Yes, will it rain at all?\"},{\"bot\":\"No rain is expected tomorrow. It's a great day to be outdoors!\"},{\"user\":\"Great, thanks!\"},{\"bot\":\"Happy to help! Let me know if you need anything else.\"}]")]
    [TestCase(
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"/clear\"},{\"user\":\"I'm looking for a good Italian restaurant nearby.\"}]")]
    public async Task AgentSummarizeTest(string chatEntries)
    {
        await _harness.Bus.Publish(
            new GetAgentSummarizeEvent(
                string.Empty,
                string.Empty,
                new Stack<StackEntry>(),
                SleekflowCompanyId,
                string.Empty,
                JsonConvert.DeserializeObject<List<SfChatEntry>>(chatEntries)!,
                "Handover from AI to human agent was triggered due to user request for specialized assistance"));

        Assert.That(await _harness.Consumed.Any<GetAgentSummarizeEvent>());
    }

    [Test]
    [TestCase(LeadQualification, "[]")]
    [TestCase(
        LeadQualification,
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"Hi, I'm interested in purchasing your premium membership.\"},{\"bot\":\"Great choice! Our premium membership is $49.99 per month. Would you like to proceed?\"},{\"user\":\"Yes, please. What are the payment options?\"},{\"bot\":\"We accept credit cards, PayPal, and bank transfers. How would you like to pay?\"},{\"user\":\"I'd like to use my credit card.\"},{\"bot\":\"Perfect! Please provide your credit card details to complete the purchase.\"},{\"user\":\"Here are my details: [securely provided].\"},{\"bot\":\"Thank you! Your premium membership has been activated. Enjoy the benefits!\"}]")]
    [TestCase(
        LeadQualification,
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"Hi, I'm interested in buying the latest smartphone.\"},{\"bot\":\"Great choice! The latest smartphone is priced at $799. Would you like to add it to your cart?\"},{\"user\":\"Actually, I've found a better deal elsewhere. I won't be purchasing it now.\"},{\"bot\":\"I'm sorry to hear that. If you have any other questions or need assistance, feel free to ask!\"}]")]
    [TestCase(
        LeadQualification,
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"/clear\"},{\"user\":\"I'm looking for a good Italian restaurant nearby.\"}]")]
    [TestCase(CategoryMatch, "[]")]
    [TestCase(
        CategoryMatch,
        "[{\"bot\":\"Hello! How can I help you today?\"},{\"user\":\"I'm looking for some information about your product.\"},{\"bot\":\"Sure, what would you like to know about our product?\"},{\"user\":\"Actually, never mind. I don't want to continue this conversation anymore.\"}]")]
    [TestCase(
        CategoryMatch,
        "[{\"bot\":\"Hello! How can I help you today?\"},{\"user\":\"Hi, I'm having trouble understanding how to set up your software.\"},{\"bot\":\"I’m sorry you’re having trouble. Can you tell me which part you’re stuck on?\"},{\"user\":\"I've followed the instructions, but it keeps giving me an error message. I need to speak to a human agent, please.\"}]")]
    [TestCase(
        CategoryMatch,
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"Hi there! I'm curious about your services. Can you tell me more?\"},{\"bot\":\"Certainly! We offer a range of solutions including marketing automation, customer analytics, and CRM integration. Would you like details on any particular area?\"},{\"user\":\"Yes, I'd like to learn more about the customer analytics features. They sound intriguing.\"},{\"bot\":\"Our customer analytics feature helps you track user behavior, engagement, and lifetime value across multiple channels. Are you interested in how to implement this, or would you like to know about pricing options?\"},{\"user\":\"Both, actually. I'd love to know how to implement it and also see if it fits my budget.\"}]")]
    [TestCase(
        CategoryMatch,
        "[{\"bot\":\"Hello! How can I assist you today?\"},{\"user\":\"/clear\"},{\"user\":\"I'm looking for a good Italian restaurant nearby.\"}]")]
    public async Task AgentEvaluateScoreTest(string additionalPrompt, string chatEntries)
    {
        await _harness.Bus.Publish(
            new GetAgentEvaluateScoreEvent(
                string.Empty,
                string.Empty,
                new Stack<StackEntry>(),
                SleekflowCompanyId,
                string.Empty,
                JsonConvert.DeserializeObject<List<SfChatEntry>>(chatEntries)!,
                additionalPrompt));

        Assert.That(await _harness.Consumed.Any<GetAgentEvaluateScoreEvent>());
    }
}