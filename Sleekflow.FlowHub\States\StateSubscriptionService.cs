using MassTransit;
using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Events;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.States;

public interface IStateSubscriptionService
{
    Task<List<StateSubscription>> GetStateSubscriptionsAsync(string stateId);

    Task<StateSubscription> GetStateSubscriptionAsync(string stateSubscriptionId, string stateId);

    Task<StateSubscription> CreateStateSubscriptionAsync(
        StateSubscription stateSubscription);

    Task<StateSubscription> UpdateStateSubscriptionIsExecutedAsync(
        StateSubscription stateSubscription,
        bool isExecuted);

    Task<StateSubscription> UpdateStateSubscriptionIsExpiredAsync(
        StateSubscription stateSubscription,
        bool isTimeout);

    Task DeleteStateSubscriptionAsync(string stateSubscriptionId, string stateId);
}

public class StateSubscriptionService : IStateSubscriptionService, IScopedService
{
    private readonly IStateSubscriptionRepository _stateSubscriptionRepository;
    private readonly IBus _bus;

    public StateSubscriptionService(
        IStateSubscriptionRepository stateSubscriptionRepository,
        IBus bus)
    {
        _stateSubscriptionRepository = stateSubscriptionRepository;
        _bus = bus;
    }

    public async Task<List<StateSubscription>> GetStateSubscriptionsAsync(string stateId)
    {
        return await _stateSubscriptionRepository.GetObjectsAsync(
            ss =>
                ss.StateId == stateId
                && ss.IsExecuted == false
                && ss.IsTimeout == false);
    }

    public async Task<StateSubscription> GetStateSubscriptionAsync(string stateSubscriptionId, string stateId)
    {
        return await _stateSubscriptionRepository.GetAsync(
            stateSubscriptionId,
            stateId);
    }

    public async Task<StateSubscription> CreateStateSubscriptionAsync(StateSubscription stateSubscription)
    {
        var createdStateSubscription = await _stateSubscriptionRepository.CreateAndGetAsync(
            stateSubscription,
            stateSubscription.StateId);

        await _bus.Publish(
            new OnStateSubscriptionCreatedEvent(
                createdStateSubscription));

        return createdStateSubscription;
    }

    public async Task<StateSubscription> UpdateStateSubscriptionIsExecutedAsync(
        StateSubscription stateSubscription,
        bool isExecuted)
    {
        return await _stateSubscriptionRepository.PatchAndGetAsync(
            stateSubscription.Id,
            stateSubscription.StateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/is_executed", isExecuted)
            },
            stateSubscription.ETag);
    }

    public async Task<StateSubscription> UpdateStateSubscriptionIsExpiredAsync(
        StateSubscription stateSubscription,
        bool isTimeout)
    {
        return await _stateSubscriptionRepository.PatchAndGetAsync(
            stateSubscription.Id,
            stateSubscription.StateId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/is_timeout", isTimeout)
            },
            stateSubscription.ETag);
    }

    public async Task DeleteStateSubscriptionAsync(string stateSubscriptionId, string stateId)
    {
        await _stateSubscriptionRepository.DeleteAsync(stateSubscriptionId, stateId);
    }
}