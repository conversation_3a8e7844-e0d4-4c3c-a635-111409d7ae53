using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.AuditLogs.WhatsappCloudApiWebhookStatusUpdateAuditLogs;

[TriggerGroup(ControllerNames.AuditLogs)]
public class GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs
    : ITrigger<
        GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs.GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput,
        GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs.GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput>
{
    private readonly IAuditLogService _auditLogService;
    private readonly IWabaService _wabaService;

    public GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs(
        IAuditLogService auditLogService,
        IWabaService wabaService)
    {
        _auditLogService = auditLogService;
        _wabaService = wabaService;
    }

    public class GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Validations.ValidateObject]
        [JsonProperty("whatsapp_cloud_api_webhook_status_update_audit_logs_filters")]
        public WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters? WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters { get; set; }

        [JsonProperty("offset")]
        [Validations.ValidateObject]
        public int? Offset { get; set; }

        [JsonProperty("limit")]
        [Validations.ValidateObject]
        public int? Limit { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput(
            string sleekflowCompanyId,
            string wabaId,
            WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters? whatsappCloudApiWebhookStatusUpdateAuditLogsFilters,
            int? offset,
            int? limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters = whatsappCloudApiWebhookStatusUpdateAuditLogsFilters;
            Offset = offset ?? 0;
            Limit = limit ?? 10;
        }
    }

    public class GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput
    {
        [JsonProperty("whatsapp_cloud_api_webhook_status_update_audit_logs")]
        public List<AuditLog> WhatsappCloudApiWabaWebhookStatusUpdateAuditLogs { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput(
            List<AuditLog> whatsappCloudApiWabaWebhookStatusUpdateAuditLogs)
        {
            WhatsappCloudApiWabaWebhookStatusUpdateAuditLogs = whatsappCloudApiWabaWebhookStatusUpdateAuditLogs;
        }
    }

    public async Task<GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput> F(
        GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput input)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(input.WabaId, input.SleekflowCompanyId);

        if (waba == null)
        {
            throw new SfNotFoundObjectException(input.WabaId);
        }

        return new GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput(
            await _auditLogService.GetCloudApiWebhookStatusUpdateAuditLogsWithFiltersAsync(
                waba.FacebookWabaId,
                input.WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters,
                input.Offset.GetValueOrDefault(0),
                input.Limit.GetValueOrDefault(10)));
    }
}