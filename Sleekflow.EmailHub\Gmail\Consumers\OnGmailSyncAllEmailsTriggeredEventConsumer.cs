using MassTransit;
using Sleekflow.EmailHub.Gmail.Communications;
using Sleekflow.EmailHub.Models.Gmail.Events;
using Sleekflow.Locks;

namespace Sleekflow.EmailHub.Gmail.Consumers;

public class OnGmailSyncAllEmailsTriggeredEventConsumerDefinition : ConsumerDefinition<OnGmailSyncAllEmailsTriggeredEventConsumer>
{
    public const int LockDuration = 30;

    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnGmailSyncAllEmailsTriggeredEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
            serviceBusReceiveEndpointConfiguration.MaxAutoRenewDuration = TimeSpan.FromMinutes(30);
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromMinutes(5);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnGmailSyncAllEmailsTriggeredEventConsumer : IConsumer<OnGmailSyncAllEmailsTriggeredEvent>
{
    private readonly IGmailCommunicationService _gmailCommunicationService;
    private readonly ILogger<OnGmailSyncAllEmailsTriggeredEventConsumer> _logger;
    private readonly ILockService _lockService;

    public OnGmailSyncAllEmailsTriggeredEventConsumer(
        IGmailCommunicationService gmailCommunicationService,
        ILogger<OnGmailSyncAllEmailsTriggeredEventConsumer> logger,
        ILockService lockService)
    {
        _gmailCommunicationService = gmailCommunicationService;
        _logger = logger;
        _lockService = lockService;
    }

    public async Task Consume(ConsumeContext<OnGmailSyncAllEmailsTriggeredEvent> context)
    {
        var gmailFullSyncEvent = context.Message;
        var cancellationToken = context.CancellationToken;
        var @lock = await _lockService.LockAsync(
            new[]
            {
                gmailFullSyncEvent.EmailAddress,
                "GmailFullSync"
            },
            TimeSpan.FromSeconds(
                60 * OnGmailSyncAllEmailsTriggeredEventConsumerDefinition.LockDuration),
            cancellationToken);
        if (@lock is null)
        {
            _logger.LogInformation(
                "[OnGmailSyncAllEmailsTriggeredEventConsumer]: Lock is not acquired: emailAddress {emailAddress}",
                gmailFullSyncEvent.EmailAddress);
            return;
        }

        try
        {
            _logger.LogInformation(
                "[OnGmailSyncAllEmailsTriggeredEventConsumer]: Starting Gmail Full Sync: emailAddress {emailAddress}",
                gmailFullSyncEvent.EmailAddress);

            await _gmailCommunicationService.SyncAllEmailsAsync(
                gmailFullSyncEvent.EmailAddress,
                cancellationToken);

            _logger.LogInformation(
                "[OnGmailSyncAllEmailsTriggeredEventConsumer]: Gmail Full Sync successes: emailAddress {emailAddress}",
                gmailFullSyncEvent.EmailAddress);
        }
        catch (Exception e)
        {
            _logger.LogInformation(
                "[OnGmailSyncAllEmailsTriggeredEventConsumer]: Gmail Full Sync fails due to {e}: emailAddress {emailAddress}",
                e,
                gmailFullSyncEvent.EmailAddress);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock, cancellationToken);
        }
    }
}