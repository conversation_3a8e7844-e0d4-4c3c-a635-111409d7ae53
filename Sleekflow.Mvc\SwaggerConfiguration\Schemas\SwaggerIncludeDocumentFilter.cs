#if SWAGGERGEN
using System.Reflection;
using Microsoft.OpenApi.Models;
using Sleekflow.Attributes;
using Swashbuckle.AspNetCore.SwaggerGen;

namespace Sleekflow.Mvc.SwaggerConfiguration.Schemas;

public class SwaggerIncludeDocumentFilter : IDocumentFilter
{
    private readonly string _joinedAssemblyNames;

    public SwaggerIncludeDocumentFilter(string joinedAssemblyNames)
    {
        _joinedAssemblyNames = joinedAssemblyNames;
    }

    public void Apply(OpenApiDocument swaggerDoc, DocumentFilterContext context)
    {
        if (_joinedAssemblyNames == "")
        {
            return;
        }

        var assemblies = _joinedAssemblyNames.Split(";").Select(Assembly.Load);

        foreach (var assembly in assemblies)
        {
            var types =
                assembly.GetTypes().Where(x => x.GetCustomAttribute(typeof(SwaggerIncludeAttribute), false) != null);
            foreach (var type in types)
            {
                context.SchemaGenerator.GenerateSchema(type, context.SchemaRepository);
            }
        }
    }
}
#endif