using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Agents.Actions;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class EvaluateExitConditionsStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.evaluate-exit-conditions-by-agent";

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty(IHasCompanyAgentConfigIdExpr.PropertyNameCompanyAgentConfigIdExpr)]
    public string CompanyAgentConfigIdExpr { get; set; }

    [JsonProperty("confidence_score__expr")]
    public string ConfidenceScoreExpr { get; set; }

    [JsonProperty("retrieval_window_timestamp__expr")]
    public string? RetrievalWindowTimestampExpr { get; set; }

    [JsonProperty("score__expr")]
    public string? ScoreExpr { get; set; }



    [JsonConstructor]
    public EvaluateExitConditionsStepArgs(
        string contactIdExpr,
        string companyAgentConfigIdExpr,
        string confidenceScoreExpr,
        string? retrievalWindowTimestampExpr = null,
        string? scoreExpr = null
    )
    {
        ContactIdExpr = contactIdExpr;
        CompanyAgentConfigIdExpr = companyAgentConfigIdExpr;
        RetrievalWindowTimestampExpr = retrievalWindowTimestampExpr;
        ConfidenceScoreExpr = confidenceScoreExpr;
        ScoreExpr = scoreExpr;
    }
}
