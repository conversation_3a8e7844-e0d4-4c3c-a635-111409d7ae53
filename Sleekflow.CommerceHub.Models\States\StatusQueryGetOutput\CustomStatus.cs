﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.States.StatusQueryGetOutput;

public class CustomStatus
{
    [JsonProperty("count")]
    public long Count { get; set; }

    [JsonProperty("last_update_time")]
    public DateTime LastUpdateTime { get; set; }

    [JsonProperty("meta_data")]
    public ICustomStatusMetaData? MetaData { get; set; }

    [JsonConstructor]
    public CustomStatus(long count, DateTime lastUpdateTime, ICustomStatusMetaData? metaData)
    {
        Count = count;
        LastUpdateTime = lastUpdateTime;
        MetaData = metaData;
    }
}