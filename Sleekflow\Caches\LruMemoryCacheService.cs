using Microsoft.Extensions.Caching.Memory;

namespace Sleekflow.Caches;

public interface ISpecializedMemoryCacheService : IDisposable
{
    string CacheName { get; }

    bool Exists(string key);

    Task<T> GetOrCreateAsync<T>(
        string key,
        Func<Task<T>> valueFactoryFunc,
        Action<ICacheEntry>? cacheEntryAction = null);

    void Remove(string key);
}

public sealed class LruMemoryCacheService : ISpecializedMemoryCacheService
{
    private readonly int _allowedSize;
    private readonly MemoryCache _memoryCache;

    public LruMemoryCacheService(
        string cacheName,
        int allowedSize)
    {
        CacheName = cacheName;

        _allowedSize = allowedSize;
        _memoryCache = new (
            new MemoryCacheOptions()
            {
                SizeLimit = allowedSize * 2
            });
    }

    public string CacheName { get; }

    public bool Exists(string key)
    {
        return _memoryCache.TryGetValue(key, out _);
    }

    public async Task<T> GetOrCreateAsync<T>(
        string key,
        Func<Task<T>> valueFactoryFunc,
        Action<ICacheEntry>? cacheEntryAction = null)
    {
        var item = await _memoryCache.GetOrCreateAsync(
            key,
            entry =>
            {
                entry.Size = 1;

                cacheEntryAction?.Invoke(entry);

                return valueFactoryFunc();
            });

        if (_memoryCache.Count >= _allowedSize)
        {
            var compactPercentage = (_memoryCache.Count - _allowedSize) / (double) _memoryCache.Count;
            _memoryCache.Compact(compactPercentage);
        }

        return item!;
    }

    public void Remove(string key)
    {
        _memoryCache.Remove(key);
    }

    public void Dispose()
    {
        _memoryCache.Dispose();
    }
}