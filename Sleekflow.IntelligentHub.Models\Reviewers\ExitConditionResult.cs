using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Reviewers;

public class ExitConditionResult
{
    [JsonProperty("is_match_exit_condition")]
    public bool IsMatchExitCondition { get; set; }

    [JsonProperty("exit_condition_title")]
    public string ExitConditionTitle { get; set; }

    [JsonProperty("reason")]
    public string Reason { get; set; }

    [JsonConstructor]
    public ExitConditionResult(bool isMatchExitCondition, string exitConditionTitle, string reason)
    {
        IsMatchExitCondition = isMatchExitCondition;
        ExitConditionTitle = exitConditionTitle;
        Reason = reason;
    }
}
