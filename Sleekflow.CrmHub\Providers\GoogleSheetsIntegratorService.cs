﻿using Sleekflow.CrmHub.Configs;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers.States;

namespace Sleekflow.CrmHub.Providers;

public class GoogleSheetsIntegratorService : GenericProviderService
{
    public const string ProviderName = "google-sheets-integrator";

    public GoogleSheetsIntegratorService(
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        IProviderStateService providerStateService,
        ILoopThroughObjectsProgressStateService loopThroughObjectsProgressStateService,
        ICustomSyncConfigService customSyncConfigService,
        ILogger<GoogleSheetsIntegratorService> logger)
        : base(
            providerStateService,
            loopThroughObjectsProgressStateService,
            httpClientFactory.CreateClient("default-handler"),
            customSyncConfigService,
            ProviderName,
            appConfig.GoogleSheetsIntegratorEndpoint,
            logger)
    {
    }
}