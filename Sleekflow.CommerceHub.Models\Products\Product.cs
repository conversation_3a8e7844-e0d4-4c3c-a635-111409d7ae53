﻿using Azure.Search.Documents.Indexes;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Products;

[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.Product)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class Product : AuditEntity, IHasRecordStatuses, IHasMetadata
{
    public const string PropertyNameCategoryIds = "category_ids";
    public const string PropertyNameSku = "sku";
    public const string PropertyNameUrl = "url";
    public const string PropertyNameNames = "names";
    public const string PropertyNameDescriptions = "descriptions";
    public const string PropertyNameImages = "images";
    public const string PropertyNameProductVariantNames = "product_variant_names";
    public const string PropertyNameProductVariantDescriptions = "product_variant_descriptions";
    public const string PropertyNameProductVariantAttributes = "product_variant_attributes";
    public const string PropertyNameProductVariantPrices = "product_variant_prices";
    public const string PropertyNameIsViewEnabled = "is_view_enabled";

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(PropertyNameCategoryIds)]
    public List<string> CategoryIds { get; set; }

    [JsonProperty(PropertyNameSku)]
    public string? Sku { get; set; }

    [JsonProperty(PropertyNameUrl)]
    public string? Url { get; set; }

    [JsonProperty(PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(PropertyNameImages)]
    public List<Image> Images { get; set; }

    [JsonProperty(PropertyNameProductVariantNames)]
    public List<Multilingual> ProductVariantNames { get; set; }

    [JsonProperty(PropertyNameProductVariantDescriptions)]
    public List<Description> ProductVariantDescriptions { get; set; }

    [JsonProperty(PropertyNameProductVariantAttributes)]
    public List<ProductAttribute> ProductVariantAttributes { get; set; }

    [JsonProperty(PropertyNameProductVariantPrices)]
    public List<Price> ProductVariantPrices { get; set; }

    [JsonProperty(PropertyNameIsViewEnabled)]
    public bool IsViewEnabled { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(CommonFieldNames.PropertyNamePlatformData)]
    public PlatformData PlatformData { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    public class ProductAttribute
    {
        [SearchableField(IsFilterable = true, IsFacetable = true)]
        [JsonProperty("name")]
        public string Name { get; set; }

        [SimpleField]
        [JsonProperty("values")]
        public List<ProductAttributeValue> Values { get; set; }

        [JsonConstructor]
        public ProductAttribute(
            string name,
            List<ProductAttributeValue> values)
        {
            Name = name;
            Values = values;
        }
    }

    public class ProductAttributeValue
    {
        [SearchableField(IsFilterable = true, IsFacetable = true)]
        [JsonProperty("value")]
        public string? Value { get; set; }

        [JsonConstructor]
        public ProductAttributeValue(string? value)
        {
            Value = value;
        }
    }

    [JsonConstructor]
    public Product(
        string id,
        string sleekflowCompanyId,
        string storeId,
        List<string> categoryIds,
        string? sku,
        string? url,
        List<Multilingual> names,
        List<Description> descriptions,
        List<Image> images,
        List<Multilingual> productVariantNames,
        List<Description> productVariantDescriptions,
        List<ProductAttribute> productVariantAttributes,
        List<Price> productVariantPrices,
        bool isViewEnabled,
        List<string> recordStatuses,
        PlatformData platformData,
        Dictionary<string, object?> metadata,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        SleekflowStaff? createdBy = null,
        SleekflowStaff? updatedBy = null)
        : base(
            id,
            SysTypeNames.Product,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        StoreId = storeId;
        CategoryIds = categoryIds;
        Sku = sku;
        Url = url;
        Names = names;
        Descriptions = descriptions;
        Images = images;
        ProductVariantNames = productVariantNames;
        ProductVariantDescriptions = productVariantDescriptions;
        ProductVariantAttributes = productVariantAttributes;
        ProductVariantPrices = productVariantPrices;
        IsViewEnabled = isViewEnabled;
        RecordStatuses = recordStatuses;
        PlatformData = platformData;
        Metadata = metadata;
    }
}