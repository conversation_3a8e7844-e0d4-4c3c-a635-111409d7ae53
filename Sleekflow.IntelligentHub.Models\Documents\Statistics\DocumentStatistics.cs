using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Documents.Statistics;

public class DocumentStatistics
{
    [JsonProperty(PropertyName = "total_token_counts")]
    public int TotalTokenCounts { get; }

    [JsonProperty(PropertyName = "total_word_counts")]
    public int TotalWordCounts { get; }

    [JsonProperty(PropertyName = "total_characters")]
    public int TotalCharacters { get; }

    [JsonProperty(PropertyName = "total_pages")]
    public int TotalPages { get; }

    [JsonProperty(PropertyName = "file_size_bytes")]
    public int? FileSizeBytes { get; }

    [JsonConstructor]
    public DocumentStatistics(int totalTokenCounts, int totalWordCounts, int totalCharacters, int totalPages, int? fileSizeBytes)
    {
        TotalTokenCounts = totalTokenCounts;
        TotalWordCounts = totalWordCounts;
        TotalCharacters = totalCharacters;
        TotalPages = totalPages;
        FileSizeBytes = fileSizeBytes;
    }
}