using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Stripe;

namespace Sleekflow.CommerceHub.Payments.Stripe;

public interface IStripeClients
{
    StripeClient GetStripeClient(string platformCountry);

    StripeClient GetCustomStripeClient(string apiKey);
}

public class StripeClients : IStripeClients, ISingletonService
{
    private readonly IStripePaymentConfig _stripePaymentConfig;

    public StripeClients(
        IStripePaymentConfig stripePaymentConfig)
    {
        _stripePaymentConfig = stripePaymentConfig;
    }

    public StripeClient GetStripeClient(string platformCountry)
    {
        var apiKey = platformCountry.ToLower() switch
        {
            "hk" => _stripePaymentConfig.ApiKeyHk,
            "sg" => _stripePaymentConfig.ApiKeySg,
            "my" => _stripePaymentConfig.ApiKeyMy,
            "gb" => _stripePaymentConfig.ApiKeyGb,
            _ => throw new SfInternalErrorException("The platformCountry is not supported.")
        };

        return new StripeClient(apiKey);
    }

    public StripeClient GetCustomStripeClient(string apiKey)
    {
        return new StripeClient(apiKey);
    }
}