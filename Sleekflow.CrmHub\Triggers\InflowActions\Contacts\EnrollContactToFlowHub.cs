using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.TriggerEvents;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Contacts;

[TriggerGroup(TriggerGroups.InflowActions)]
public class EnrollContactToFlowHub : ITrigger
{
    private readonly IBus _bus;

    public EnrollContactToFlowHub(
        IBus bus)
    {
        _bus = bus;
    }

    public class EnrollContactToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("contact")]
        public Dictionary<string, object?> Contact { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public EnrollContactToFlowHubInput(
            string sleekflowCompanyId,
            string contactId,
            Dictionary<string, object?> contact,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ContactId = contactId;
            Contact = contact;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class EnrollContactToFlowHubOutput
    {
    }

    public async Task<EnrollContactToFlowHubOutput> F(
        EnrollContactToFlowHubInput enrollContactToFlowHubInput)
    {
        await _bus.Publish(
            new OnSleekflowContactEnrollmentToFlowHubRequestedEvent(
                DateTimeOffset.UtcNow,
                enrollContactToFlowHubInput.SleekflowCompanyId,
                enrollContactToFlowHubInput.ContactId,
                enrollContactToFlowHubInput.Contact,
                enrollContactToFlowHubInput.FlowHubWorkflowId,
                enrollContactToFlowHubInput.FlowHubWorkflowVersionedId),
            context => { context.ConversationId = Guid.Parse(enrollContactToFlowHubInput.SleekflowCompanyId); });

        return new EnrollContactToFlowHubOutput();
    }
}