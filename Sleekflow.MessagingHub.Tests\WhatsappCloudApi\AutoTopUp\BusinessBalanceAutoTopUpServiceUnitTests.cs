using MassTransit;
using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Payments;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUp;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;

namespace Sleekflow.MessagingHub.Tests.WhatsappCloudApi.AutoTopUp;

[TestFixture]
public class BusinessBalanceAutoTopUpServiceUnitTests
{
    private BusinessBalanceAutoTopUpService _service;
    private Mock<ILogger<BusinessBalanceAutoTopUpService>> _loggerMock;
    private Mock<IBus> _busMock;
    private Mock<IBusinessBalanceAutoTopUpProfileService> _profileServiceMock;
    private Mock<IStripeClient> _stripeClientMock;
    private BusinessBalance _mockBusinessBalance;
    private BusinessBalanceAutoTopUpProfile _mockBusinessBalanceAutoTopUpProfile;
    private string _mockFacebookBusinessId;

    [SetUp]
    public void BusinessBalanceAutoTopUpServiceUnitTestsSetUp()
    {
        _loggerMock = new Mock<ILogger<BusinessBalanceAutoTopUpService>>();
        _busMock = new Mock<IBus>();
        _profileServiceMock = new Mock<IBusinessBalanceAutoTopUpProfileService>();
        _service = new BusinessBalanceAutoTopUpService(_busMock.Object, _loggerMock.Object);

        _mockFacebookBusinessId = "123";
        _mockBusinessBalance = new BusinessBalance(
            "test_id",
            _mockFacebookBusinessId,
            null!,
            null!,
            null!,
            null!,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow);

        _mockBusinessBalanceAutoTopUpProfile = new BusinessBalanceAutoTopUpProfile(
            "test_id",
            _mockFacebookBusinessId,
            "test_customer_id",
            null,
            null,
            true,
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            "test_companyId",
            null,
            null,
            new List<string>
            {
                RecordStatuses.Active
            });
    }

    [Test]
    public async Task ShouldPerformAutoTopUp_ReturnsFalse_WhenAutoTopUpIsDisabled()
    {
        // Arrange
        var profile = _mockBusinessBalanceAutoTopUpProfile;
        profile.IsAutoTopUpEnabled = false;

        _profileServiceMock.Setup(x => x.GetWithFacebookBusinessIdAsync(_mockFacebookBusinessId)).ReturnsAsync(profile);

        // Act
        var result = _service.ShouldPerformAutoTopUp(_mockFacebookBusinessId, _mockBusinessBalance, profile);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void ShouldPerformAutoTopUp_ReturnsFalse_WhenMinimumBalanceCurrencyDoesNotMatchBusinessBalance()
    {
        // Arrange
        var profile = _mockBusinessBalanceAutoTopUpProfile;
        profile.MinimumBalance = new Money("GBP", 11m);
        var businessBalance = _mockBusinessBalance;
        businessBalance.Credit = new Money("USD", 11m);

        // Act
        var result = _service.ShouldPerformAutoTopUp(_mockFacebookBusinessId, _mockBusinessBalance, profile);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void ShouldPerformAutoTopUp_ReturnsFalse_WhenBalanceIsGreaterThanMinimumBalance()
    {
        // Arrange
        var businessBalance = _mockBusinessBalance;
        businessBalance.Credit = new Money("USD", 11m);
        businessBalance.Used = new Money("USD", 0m);
        var profile = _mockBusinessBalanceAutoTopUpProfile;
        profile.MinimumBalance = new Money("USD", 10m);

        _profileServiceMock.Setup(x => x.GetWithFacebookBusinessIdAsync(_mockFacebookBusinessId)).ReturnsAsync(profile);

        // Act
        var result = _service.ShouldPerformAutoTopUp(_mockFacebookBusinessId, businessBalance, profile);

        // Assert
        Assert.IsFalse(result);
    }

    [Test]
    public void ShouldPerformAutoTopUp_ReturnsFalse_WhenBalanceIsLessThanMinimumBalance()
    {
        // Arrange
        var businessBalance = _mockBusinessBalance;
        businessBalance.Credit = new Money("USD", 9m);
        businessBalance.Used = new Money("USD", 0m);
        var profile = _mockBusinessBalanceAutoTopUpProfile;
        profile.MinimumBalance = new Money("USD", 10m);

        _profileServiceMock.Setup(x => x.GetWithFacebookBusinessIdAsync(_mockFacebookBusinessId)).ReturnsAsync(profile);

        // Act
        var result = _service.ShouldPerformAutoTopUp(_mockFacebookBusinessId, businessBalance, profile);

        // Assert
        Assert.IsTrue(result);
    }
}