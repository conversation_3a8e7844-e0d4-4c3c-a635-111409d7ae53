﻿using MassTransit;
using Sleekflow.Events;

namespace Sleekflow.AuditLogs;

public class OnAuditLogRequestedEvent : IEvent
{
    public string SleekflowCompanyId { get; set; }

    public string TypeName { get; set; }

    public string TypeDescription { get; set; }

    public MessageData<string> MessageData { get; set; }

    public DateTimeOffset DateTime { get; set; }

    public OnAuditLogRequestedEvent(
        string sleekflowCompanyId,
        string typeName,
        string typeDescription,
        MessageData<string> messageData,
        DateTimeOffset dateTime)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        TypeName = typeName;
        TypeDescription = typeDescription;
        MessageData = messageData;
        DateTime = dateTime;
    }
}