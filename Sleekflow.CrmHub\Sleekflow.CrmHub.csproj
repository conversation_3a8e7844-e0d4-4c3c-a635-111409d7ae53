<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CsvHelper" Version="33.0.1" />
        <PackageReference Include="DynamicExpresso.Core" Version="2.17.2" />
        <PackageReference Include="libphonenumber-csharp" Version="8.13.43" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.13" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Sleekflow.CrmHub.Models\Sleekflow.CrmHub.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Models\Sleekflow.Models.csproj" />
        <ProjectReference Include="..\Sleekflow.Mvc\Sleekflow.Mvc.csproj" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="Sleekflow.CrmHub.Tests" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Triggers\InflowActions\Salesforce" />
    </ItemGroup>

    <Target Name="SwaggerGen" AfterTargets="Build" Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <MakeDir Directories=".swagger" />
        <Exec Command="dotnet tool restore" />
        <Exec Command="dotnet swagger tofile --output ./.swagger/v1.yaml --yaml $(OutputPath)$(AssemblyName).dll v1" WorkingDirectory="$(ProjectDir)" />
    </Target>

</Project>