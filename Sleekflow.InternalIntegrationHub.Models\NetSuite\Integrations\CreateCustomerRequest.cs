using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class CreateCustomerRequest
{
    [JsonProperty("externalId", NullValueHandling = NullValueHandling.Ignore)]
    public string? ExternalId { get; set; }

    [JsonProperty("companyName", NullValueHandling = NullValueHandling.Ignore)]
    public string? CompanyName { get; set; }

    [JsonProperty("subsidiary", NullValueHandling = NullValueHandling.Ignore)]
    public CreateCustomerRequestCommonId? Subsidiary { get; set; }

    [JsonProperty("email", NullValueHandling = NullValueHandling.Ignore)]
    public string? Email { get; set; }

    [JsonProperty("phone", NullValueHandling = NullValueHandling.Ignore)]
    public string? Phone { get; set; }

    [JsonProperty("terms", NullValueHandling = NullValueHandling.Ignore)]
    public CreateCustomerRequestCommonId? Terms { get; set; }

    [JsonProperty("currency", NullValueHandling = NullValueHandling.Ignore)]
    public CreateCustomerRequestCommonId? Currency { get; set; }

    [JsonProperty("currencyList", NullValueHandling = NullValueHandling.Ignore)]
    public CurrencyList? CurrencyList { get; set; }

    [JsonProperty("salesRep", NullValueHandling = NullValueHandling.Ignore)]
    public CreateCustomerRequestCommonId? SalesRep { get; set; }

    [JsonConstructor]
    public CreateCustomerRequest(
        string? externalId,
        string? companyName,
        CreateCustomerRequestCommonId? subsidiary,
        string? email,
        string? phone,
        CreateCustomerRequestCommonId? terms,
        CreateCustomerRequestCommonId? currency,
        CurrencyList? currencyList,
        CreateCustomerRequestCommonId? salesRep)
    {
        ExternalId = externalId;
        CompanyName = companyName;
        Subsidiary = subsidiary;
        Email = email;
        Phone = phone;
        Terms = terms;
        Currency = currency;
        CurrencyList = currencyList;
        SalesRep = salesRep;
    }
}

public class CreateCustomerRequestCommonId
{
    [JsonProperty("id")]
    public string InternalId { get; set; }

    [JsonConstructor]
    public CreateCustomerRequestCommonId(string internalId)
    {
        InternalId = internalId;
    }
}

public class CurrencyList
{
    [JsonProperty("items")]
    public List<CurrencyListElement> Items { get; set; }

    [JsonConstructor]
    public CurrencyList(List<CurrencyListElement> items)
    {
        Items = items;
    }
}

public class CurrencyListElement
{
    [JsonProperty("currency")]
    public string Currency { get; set; }

    [JsonConstructor]
    public CurrencyListElement(string currency)
    {
        Currency = currency;
    }
}