using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CommerceHubDb;

namespace Sleekflow.CommerceHub.Models.Categories;

[ContainerId(ContainerNames.Category)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(ICommerceHubDbResolver))]
public class Category : AuditEntity, IHasRecordStatuses, IHasMetadata
{
    public const string PropertyNameNames = "names";
    public const string PropertyNameDescriptions = "descriptions";
    public const string PropertyNamePlatformData = "platform_data";

    [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
    public string StoreId { get; set; }

    [JsonProperty(PropertyNameNames)]
    public List<Multilingual> Names { get; set; }

    [JsonProperty(PropertyNameDescriptions)]
    public List<Description> Descriptions { get; set; }

    [JsonProperty(PropertyNamePlatformData)]
    public PlatformData PlatformData { get; set; }

    [JsonProperty(IHasRecordStatuses.PropertyNameRecordStatuses)]
    public List<string> RecordStatuses { get; set; }

    [JsonProperty(IHasMetadata.PropertyNameMetadata)]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public Category(
        string id,
        string sleekflowCompanyId,
        List<Multilingual> names,
        List<Description> descriptions,
        string storeId,
        PlatformData platformData,
        List<string> recordStatuses,
        Dictionary<string, object?> metadata,
        SleekflowStaff? createdBy,
        SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(
            id,
            SysTypeNames.Category,
            createdAt,
            updatedAt,
            sleekflowCompanyId,
            createdBy,
            updatedBy)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Names = names;
        Descriptions = descriptions;
        StoreId = storeId;
        PlatformData = platformData;
        RecordStatuses = recordStatuses;
        Metadata = metadata;
    }
}