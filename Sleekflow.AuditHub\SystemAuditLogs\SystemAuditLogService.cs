using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.AuditHub.Models.SystemAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.JsonConfigs;

namespace Sleekflow.AuditHub.SystemAuditLogs;

public interface ISystemAuditLogService
{
    Task<(string? NextContinuationToken, List<SystemAuditLog> SystemAuditLogs)> GetSystemAuditLogsAsync(
        string sleekflowCompanyId,
        string? continuationToken,
        SystemAuditLogsFilters filters,
        int limit);

    Task CreateSystemAuditLogAsync(
        SystemAuditLog systemAuditLog,
        CancellationToken cancellationToken = default);
}

public class SystemAuditLogService : ISystemAuditLogService, ISingletonService
{
    private readonly ILogger<SystemAuditLogService> _logger;
    private readonly ISystemAuditLogRepository _systemAuditLogRepository;
    private readonly IIdService _idService;

    public SystemAuditLogService(
        ILogger<SystemAuditLogService> logger,
        ISystemAuditLogRepository systemAuditLogRepository,
        IIdService idService)
    {
        _logger = logger;
        _systemAuditLogRepository = systemAuditLogRepository;
        _idService = idService;
    }

    public async Task<(string? NextContinuationToken, List<SystemAuditLog> SystemAuditLogs)>
        GetSystemAuditLogsAsync(
            string sleekflowCompanyId,
            string? continuationToken,
            SystemAuditLogsFilters filters,
            int limit)
    {
        var (systemAuditLogs, nextContinuationToken) =
            await _systemAuditLogRepository.GetContinuationTokenizedObjectsAsync(
                new QueryDefinition(
                        $"""
                         SELECT *
                         FROM c
                         WHERE
                            c.sleekflow_company_id = @sleekflowCompanyId
                            {(filters.Types != null ? "AND ARRAY_CONTAINS(@types, c.type) " : string.Empty)}
                            {(filters.SleekflowUserProfileId != null ? "AND c.sleekflow_user_profile_id = @sleekflowUserProfile_id " : string.Empty)}
                            {(filters.SleekflowStaffId != null ? "AND c.sleekflow_staff_id = @sleekflowStaffId " : string.Empty)}
                            {(filters.FromCreatedTime != null ? "AND c.created_time >= @fromCreatedTime " : string.Empty)}
                            {(filters.ToCreatedTime != null ? "AND c.created_time <= @toCreatedTime " : string.Empty)}
                         ORDER BY c.sleekflow_company_id ASC, c.created_time DESC
                         """)
                    .WithParameter("@sleekflowCompanyId", sleekflowCompanyId)
                    .WithParameter("@types", filters.Types)
                    .WithParameter("@sleekflowUserProfileId", filters.SleekflowUserProfileId)
                    .WithParameter("@sleekflowStaffId", filters.SleekflowStaffId)
                    .WithParameter("@fromCreatedTime", filters.FromCreatedTime)
                    .WithParameter("@toCreatedTime", filters.ToCreatedTime),
                continuationToken,
                limit);

        return (nextContinuationToken, systemAuditLogs);
    }

    public async Task CreateSystemAuditLogAsync(
        SystemAuditLog systemAuditLog,
        CancellationToken cancellationToken = default)
    {
        var insertCount = await _systemAuditLogRepository.CreateAsync(
            systemAuditLog,
            systemAuditLog.Id,
            cancellationToken: cancellationToken);
        if (insertCount == 0)
        {
            _logger.LogError(
                "Unable to create the log. {SystemAuditLog}",
                JsonConvert.SerializeObject(systemAuditLog, JsonConfig.DefaultLoggingJsonSerializerSettings));

            throw new SfUserFriendlyException("Unable to create the log.");
        }
    }
}