﻿namespace Sleekflow.MessagingHub.Webhooks.Constants;

public static class WhatsappCloudApiWebhookEventTypeNames
{
    public const string BusinessVerificationStatusUpdate = "BUSINESS_VERIFICATION_STATUS_UPDATE";
    public const string PartnerAdded = "PARTNER_ADDED";
    public const string AccountDeleted = "ACCOUNT_DELETED";
    public const string PhoneNumberAdded = "PHONE_NUMBER_ADDED";
    public const string PhoneNumberRemoved = "PHONE_NUMBER_REMOVED";
    public const string CompromisedNotification = "COMPROMISED_NOTIFICATION";
    public const string FlowStatusChange = "FLOW_STATUS_CHANGE";

    // Template, WABA Display name updates
    public const string Approved = "APPROVED";
    public const string Rejected = "REJECTED";
    public const string Deferred = "DEFERRED";

    // Template
    public const string Flagged = "FLAGGED"; // phone number as well
    public const string Paused = "PAUSED";
    public const string PendingDeletion = "PENDING_DELETION";
    public const string InAppeal = "IN_APPEAL";
    public const string Reinstated = "REINSTATED";

    // waba update
    public const string VerifiedAccount = "VERIFIED_ACCOUNT";
    public const string DisabledUpdate = "DISABLED_UPDATE";
    public const string AccountViolation = "ACCOUNT_VIOLATION";
    public const string AccountRestriction = "ACCOUNT_RESTRICTION";
    public const string AdAccountLinked = "AD_ACCOUNT_LINKED";

    // Phone Number update
    public const string Downgraded = "DOWNGRADED";
    public const string Onboarding = "ONBOARDING";
    public const string Upgraded = "UPGRADED";
    public const string Unflagged = "UNFLAGGED";

    // 2FA update
    public const string PinChanged = "PIN_CHANGED";
    public const string PinResetRequest = "PIN_RESET_REQUEST";
    public const string PinResetSuccess = "PIN_RESET_SUCCESS";
}