using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.Configs;

public interface IGoogleCustomSearchConfig
{
    public string ApiKey { get; }

    public string SearchEngineId { get; }
}

public class GoogleCustomSearchConfig
    : IGoogleCustomSearchConfig, IConfig
{
    public string ApiKey { get; }

    public string SearchEngineId { get; }

    public GoogleCustomSearchConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ApiKey = Environment.GetEnvironmentVariable("GOOGLE_CUSTOM_SEARCH_ENGINE_API_KEY", target)
                 ?? "AIzaSyAiI2iyXGJQGaYopwy6UM4crbGrc6Qjdo0";

        SearchEngineId = Environment.GetEnvironmentVariable("GOOGLE_CUSTOM_SEARCH_ENGINE_ID", target)
                         ?? "90b4ba8a6b3314b9f";
    }
}