using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Tests.Workflows;

[TestFixture]
public class WorkflowEnrolmentConditionEvaluatorTests
{
    private Mock<IStateEvaluator> _mockStateEvaluator = null!;
    private Mock<ILogger<WorkflowEnrolmentConditionEvaluator>> _mockLogger = null!;
    private WorkflowEnrolmentConditionEvaluator _evaluator = null!;

    [SetUp]
    public void SetUp()
    {
        _mockStateEvaluator = new Mock<IStateEvaluator>();
        _mockLogger = new Mock<ILogger<WorkflowEnrolmentConditionEvaluator>>();
        _evaluator = new WorkflowEnrolmentConditionEvaluator(_mockStateEvaluator.Object, _mockLogger.Object);
    }

    private ProxyGrainWorkflow CreateProxyGrainWorkflow(
        Dictionary<string, object?>? contactsDict = null,
        bool isNewSchema = false,
        string? enrollmentExpr = null)
    {
        var scheduleSettings = new WorkflowScheduleSettings(
            durablePayload: null,
            isNewScheduledWorkflowSchema: isNewSchema,
            isOldScheduledWorkflowSchemaFirstRecurringCompleted: null,
            scheduledAt: null,
            contactPropertyId: null,
            schemaId: null,
            schemafulObjectPropertyId: null,
            scheduleType: WorkflowScheduleTypes.None,
            recurringSettings: null
        );

        var backingDict = contactsDict ?? new Dictionary<string, object?>();

        var mockAsyncDict = new Mock<IAsyncDictionary<string, object?>>();

        mockAsyncDict.Setup(d => d.GetAsync(It.IsAny<string>()))
                     .Returns<string>(key => Task.FromResult(backingDict[key]));

        mockAsyncDict.Setup(d => d.ContainsKeyAsync(It.IsAny<string>()))
                     .Returns<string>(key => Task.FromResult(backingDict.ContainsKey(key)));

        mockAsyncDict.Setup(d => d.ToDictionaryAsync())
                     .Returns(Task.FromResult<Dictionary<string, object?>>(backingDict));

        mockAsyncDict.Setup(d => d.CountAsync()).Returns(Task.FromResult(backingDict.Count));

        var wrappedContactsDict = new AsyncDictionaryWrapper<string, object?>(mockAsyncDict.Object);

        var triggers = new WorkflowTriggers(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);
        var enrollmentSettings = WorkflowEnrollmentSettings.Default();

        return new ProxyGrainWorkflow(
            sleekflowCompanyId: "test-company-id",
            workflowId: "test-workflow-id",
            workflowVersionedId: "test-versioned-id",
            workflowContactsDict: wrappedContactsDict,
            name: "TestGrainWorkflow",
            workflowType: "TestGrainType",
            workflowGroupId: null,
            triggers: triggers,
            workflowEnrollmentSettings: enrollmentSettings,
            workflowScheduleSettings: scheduleSettings,
            activationStatus: "Active",
            steps: new List<Step>(),
            version: "v1",
            createdBy: null,
            updatedBy: null,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow
        );
    }

    private ProxyWorkflow CreateProxyWorkflow(List<Step>? steps = null, string name = "TestWorkflow")
    {
        var enrollmentSettings = WorkflowEnrollmentSettings.Default();
        var scheduleSettings = WorkflowScheduleSettings.Default();

        var triggers = new WorkflowTriggers(
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);

        return new ProxyWorkflow(
             id: "proxy-workflow-id",
             sleekflowCompanyId: "test-company-id",
             workflowId: "test-workflow-id",
             workflowVersionedId: "test-versioned-id",
             name: name,
             workflowType: "TestType",
             workflowGroupId: null,
             triggers: triggers,
             workflowEnrollmentSettings: enrollmentSettings,
             workflowScheduleSettings: scheduleSettings,
             activationStatus: "Active",
             createdBy: null,
             updatedBy: null,
             createdAt: DateTimeOffset.UtcNow,
             updatedAt: DateTimeOffset.UtcNow,
             steps: steps ?? new List<Step>(),
             metadata: new Dictionary<string, object?>(),
             version: "v1"
         );
    }

    // Example Expression provided by user
    private const string SampleExpression =
        "{{ ((usr_var_dict.contact.labels | array.some @(do; ret $0.LabelValue == \"w\"; end))) && (usr_var_dict.conversation.conversation_status == \"open\") && ((usr_var_dict.lists | array.some @(do; ret $0.id == \"139745\"; end))) && (((usr_var_dict.contact[\"first_name\"] | string.downcase) | string.contains \"johnson\")) }}";

    // Helper to create sample contact data structure
    private object CreateSampleContactData(
        string firstName = "test",
        string conversationStatus = "closed",
        List<object>? labels = null,
        List<object>? lists = null)
    {
        return new
        {
            contact = new
            {
                id = "e78abb91-2670-4419-988d-72c74279cbe0",
                company_id = "b6d7e442-38ae-4b9a-b100-2951729768bc",
                first_name = firstName,
                last_name = (string?) null,
                picture_url = (string?) null,
                created_at = "2025-01-27T05:51:55.6562508Z",
                updated_at = "2025-04-15T03:46:21.6511494Z",
                last_contact = "2025-03-20T07:10:59.9040539Z",
                last_contact_from_customers = "2022-01-10T08:32:41.734Z",
                is_sandbox = false,
                is_shopify_profile = false,
                description = (string?) null,
                status = (string?) null,
                labels = labels ?? new List<object>(),
                country = "Hong Kong SAR",
                PhoneNumber = "85294351539",
                Subscriber = "true",
                LastContactFromCustomers = "2022-01-10T08:32:41.7340000Z",
                LastChannel = "whatsappcloudapi",
                food = "test loop through with cluster 85294351539",
                LastContact = "2025-03-20T07:10:59.9040539Z",
                DB_Custom_Field1 = "test",
                DB_Custom_Field2 = "custom field 2",
                conversation_id = "d4581b05-00e0-4ce4-8694-c07c719c396e"
            },
            contact_owner = new { },
            lists = lists ?? new List<object> { new { id = "139745", name = "Nick 3", added_at = "2025-04-09T03:49:33.0912606+08:00", is_imported = false } },
            conversation = new
            {
                conversation_status = conversationStatus,
                conversation_id = "d4581b05-00e0-4ce4-8694-c07c719c396e",
                last_message_channel = "whatsappcloudapi",
                last_message_channel_id = "18454069890"
            }
        };
    }

    [Test]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_NewSchema_NullOrEmptyExpr_ReturnsFalse()
    {
        var contactId = "contact1";
        var contactData = new { Name = "Test" };
        var contactsDict = new Dictionary<string, object?> { { contactId, contactData } };
        var proxyGrainWorkflow = CreateProxyGrainWorkflow(contactsDict: contactsDict, isNewSchema: true, enrollmentExpr: string.Empty);
        var proxyWorkflow = CreateProxyWorkflow();

        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow, new ContactDetail(
                new Dictionary<string, object?>()
                {
                    { contactId, contactData }
                },
                null,
                null,
                null));

        Assert.That(result, Is.False);
    }

    [TestCase(true)]
    [TestCase("true")]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_NewSchema_ConditionFulfilled_ReturnsTrue(object evalResult)
    {
        var contactId = "e78abb91-2670-4419-988d-72c74279cbe0";
        // Create data that fulfills the sample expression
        var contactData = CreateSampleContactData(
            firstName: "johnson",
            conversationStatus: "open",
            labels: new List<object> { new { LabelValue = "w", LabelColor = "Blue", LabelType = "Normal" } },
            lists: new List<object> { new { id = "139745", name = "Nick 3" } } // Ensure list 139745 exists
        );

        var contactDetail = JsonConvert.DeserializeObject<ContactDetail>(JsonConvert.SerializeObject(contactData));
        var expression = SampleExpression; // Use the sample expression
        var proxyWorkflow = CreateProxyWorkflow(
            steps:
            [
                new CallStep<ScheduledTriggerConditionsCheckStepArgs>(
                "id",
                "name",
                null,
                null,
                null,
                new ScheduledTriggerConditionsCheckStepArgs(SampleExpression))
            ],
            name: "NewSchemaWF");

        _mockStateEvaluator.Setup(s => s.IsExpression(expression)).Returns(true);
        // Use It.IsAny<object>() for matching complex anonymous type
        _mockStateEvaluator.Setup(s => s.EvaluateExpressionAsync("NewSchemaWF", It.IsAny<object>(), expression))
                           .ReturnsAsync(evalResult);

        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow,
            contactDetail);

        Assert.That(result, Is.True);
        _mockStateEvaluator.Verify(s => s.IsExpression(expression), Times.Once);
        // Verify with It.IsAny<object>()
        _mockStateEvaluator.Verify(s => s.EvaluateExpressionAsync("NewSchemaWF", It.IsAny<object>(), expression), Times.Once);
    }

    [TestCase(false)]
    [TestCase("false")]
    [TestCase("other")]
    [TestCase(null)]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_NewSchema_ConditionNotFulfilled_ReturnsFalse(object? evalResult)
    {
        var contactId = "e78abb91-2670-4419-988d-72c74279cbe0";
        // Use sample data structure - this specific data does NOT fulfill the expression
        var contactData = CreateSampleContactData(
             firstName: "85294351539", // Does not contain johnson
             conversationStatus: "closed", // Not open
             labels: new List<object>(), // No label 'w'
             lists: new List<object> { new { id = "139745", name = "Nick 3" } } // List 139745 exists
        );
        var contactDetail = JsonConvert.DeserializeObject<ContactDetail>(JsonConvert.SerializeObject(contactData));

        var expression = SampleExpression; // Use the sample expression
        var proxyWorkflow = CreateProxyWorkflow(name: "NewSchemaWF_Fail");

        _mockStateEvaluator.Setup(s => s.IsExpression(expression)).Returns(true);
        // Use It.IsAny<object>() for matching complex anonymous type
        _mockStateEvaluator.Setup(s => s.EvaluateExpressionAsync("NewSchemaWF_Fail", It.IsAny<object>(), expression))
                           .ReturnsAsync(evalResult);

        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow,
            new ContactDetail(
                new Dictionary<string, object?>()
                {
                    {
                        contactId, contactData
                    }
                },
                null,
                [new ContactList("139745", "Nick 3", default, false)],
                new ContactConversation( "closed", "id", null, null)));

        Assert.That(result, Is.False);
    }

    [Test]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_NewSchema_NotAnExpression_ReturnsFalse()
    {
        var contactId = "contact1";
        var contactData = new Dictionary<string, object> { { "Status", "Active" } };
        var contactsDict = new Dictionary<string, object?> { { contactId, contactData } };
        var expression = "Just a string";
        var proxyGrainWorkflow = CreateProxyGrainWorkflow(contactsDict: contactsDict, isNewSchema: true, enrollmentExpr: expression);
        var proxyWorkflow = CreateProxyWorkflow();

        _mockStateEvaluator.Setup(s => s.IsExpression(expression)).Returns(false);

        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow,
            new ContactDetail(
                new Dictionary<string, object?>()
                {
                    {
                        contactId, contactData
                    }
                },
                null,
                null,
                null));

        Assert.That(result, Is.False);
    }

    [Test]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_OldSchema_NoCallStep_ReturnsFalse()
    {
        var contactId = "contact1";
        var contactData = new { Name = "Test" };
        var contactsDict = new Dictionary<string, object?> { { contactId, contactData } };
        var proxyGrainWorkflow = CreateProxyGrainWorkflow(contactsDict: contactsDict, isNewSchema: false);
        var steps = new List<Step>();
        var proxyWorkflow = CreateProxyWorkflow(steps: steps);

        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow,
            new ContactDetail(
                new Dictionary<string, object?>()
                {
                    {
                        contactId, contactData
                    }
                },
                null,
                null,
                null));

        Assert.That(result, Is.False);
    }

    [TestCase(true)]
    [TestCase("true")]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_OldSchema_ConditionFulfilled_ReturnsTrue(object evalResult)
    {
        var contactId = "e78abb91-2670-4419-988d-72c74279cbe0";
        // Create data that fulfills the sample expression
        var contactData = CreateSampleContactData(
            firstName: "johnson",
            conversationStatus: "open",
            labels: new List<object> { new { LabelValue = "w", LabelColor = "Blue", LabelType = "Normal" } },
            lists: new List<object> { new { id = "139745", name = "Nick 3" } }
        );
        var contactsDict = new Dictionary<string, object?> { { contactId, contactData } };
        var proxyGrainWorkflow = CreateProxyGrainWorkflow(contactsDict: contactsDict, isNewSchema: false);
        var expression = SampleExpression;
        var callStepArgs = new ScheduledTriggerConditionsCheckStepArgs(expression);
        var callStep = new CallStep<ScheduledTriggerConditionsCheckStepArgs>(
            id: "step-id", name: "call-step", assign: null, nextStepId: null,
            call: ScheduledTriggerConditionsCheckStepArgs.CallName, args: callStepArgs);
        var steps = new List<Step> { callStep };
        var proxyWorkflow = CreateProxyWorkflow(steps: steps, name: "OldSchemaWF");

        _mockStateEvaluator.Setup(s => s.IsExpression(expression)).Returns(true);
        // Use It.IsAny<object>() for matching complex anonymous type
        _mockStateEvaluator.Setup(s => s.EvaluateExpressionAsync("OldSchemaWF", It.IsAny<object>(), expression))
                           .ReturnsAsync(evalResult);

        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow,
            new ContactDetail(
                new Dictionary<string, object?>()
                {
                    {
                        contactId, contactData
                    }
                },
                null,
                null,
                null));

        Assert.That(result, Is.True);
        _mockStateEvaluator.Verify(s => s.IsExpression(expression), Times.Once);
        // Verify with It.IsAny<object>()
        _mockStateEvaluator.Verify(s => s.EvaluateExpressionAsync("OldSchemaWF", It.IsAny<object>(), expression), Times.Once);
    }

    [TestCase(false)]
    [TestCase("false")]
    [TestCase("maybe")]
    [TestCase(null)]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_OldSchema_ConditionNotFulfilled_ReturnsFalse(object? evalResult)
    {
        var contactId = "e78abb91-2670-4419-988d-72c74279cbe0";
        // Use sample data structure - this specific data does NOT fulfill the expression
        var contactData = CreateSampleContactData(
             firstName: "85294351539",
             conversationStatus: "closed",
             labels: new List<object>()
        // List 139745 exists by default in helper
        );
        var contactsDict = new Dictionary<string, object?> { { contactId, contactData } };
        var proxyGrainWorkflow = CreateProxyGrainWorkflow(contactsDict: contactsDict, isNewSchema: false);
        var expression = SampleExpression;
        var callStepArgs = new ScheduledTriggerConditionsCheckStepArgs(expression);
        var callStep = new CallStep<ScheduledTriggerConditionsCheckStepArgs>(
            id: "step-id", name: "call-step", assign: null, nextStepId: null,
            call: ScheduledTriggerConditionsCheckStepArgs.CallName, args: callStepArgs);
        var steps = new List<Step> { callStep };
        var proxyWorkflow = CreateProxyWorkflow(steps: steps, name: "OldSchemaWF_Fail");

        _mockStateEvaluator.Setup(s => s.IsExpression(expression)).Returns(true);
        // Use It.IsAny<object>() for matching complex anonymous type
        _mockStateEvaluator.Setup(s => s.EvaluateExpressionAsync("OldSchemaWF_Fail", It.IsAny<object>(), expression))
                           .ReturnsAsync(evalResult);

        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow,
            new ContactDetail(
                new Dictionary<string, object?>()
                {
                    {
                        contactId, contactData
                    }
                },
                null,
                null,
                null));

        Assert.That(result, Is.False);
        _mockStateEvaluator.Verify(s => s.IsExpression(expression), Times.Once);
        // Verify with It.IsAny<object>()
        _mockStateEvaluator.Verify(s => s.EvaluateExpressionAsync("OldSchemaWF_Fail", It.IsAny<object>(), expression), Times.Once);
    }

    [Test]
    public async Task EvaluateScheduledWorkflowEnrolmentConditionAsync_OldSchema_NotAnExpression_ReturnsFalse()
    {
        var contactId = "contact1";
        // Create dummy contact data (content doesn't matter for this test)
        var contactData = CreateSampleContactData();
        var contactsDict = new Dictionary<string, object?> { { contactId, contactData } };
        // * Fix: Added missing proxyGrainWorkflow creation *
        var proxyGrainWorkflow = CreateProxyGrainWorkflow(contactsDict: contactsDict, isNewSchema: false);
        var expression = "Not an expression";
        var callStepArgs = new ScheduledTriggerConditionsCheckStepArgs(expression);
        var callStep = new CallStep<ScheduledTriggerConditionsCheckStepArgs>(
            id: "step-id", name: "call-step", assign: null, nextStepId: null,
            call: ScheduledTriggerConditionsCheckStepArgs.CallName, args: callStepArgs);
        var steps = new List<Step> { callStep };
        var proxyWorkflow = CreateProxyWorkflow(steps: steps);

        _mockStateEvaluator.Setup(s => s.IsExpression(expression)).Returns(false);

        // Now proxyGrainWorkflow should exist
        var result = await _evaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
            proxyWorkflow,
            new ContactDetail(
                new Dictionary<string, object?>()
                {
                    {
                        contactId, contactData
                    }
                },
                null,
                null,
                null));

        Assert.That(result, Is.False);
        _mockStateEvaluator.Verify(s => s.IsExpression(expression), Times.Once);
        _mockStateEvaluator.Verify(s => s.EvaluateExpressionAsync(It.IsAny<string>(), It.IsAny<object>(), It.IsAny<string>()), Times.Never);
    }
}