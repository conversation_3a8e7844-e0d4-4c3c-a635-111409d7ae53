using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class CustomObjectType
{
    [JsonProperty("api_name")]
    public string ApiName { get; set; }

    [JsonProperty("display_name")]
    public string DisplayName { get; set; }

    [JsonConstructor]
    public CustomObjectType(
        string apiName,
        string displayName)
    {
        ApiName = apiName;
        DisplayName = displayName;
    }
}