<?xml version="1.0"?>
<doc>
    <assembly>
        <name>GraphApi.Client</name>
    </assembly>
    <members>
        <member name="P:GraphApi.Client.ApiClients.Models.GraphApiResponseBase.HttpPayload">
            <summary>
            Http Request Response Body, for Debug Purpose
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetUserBusinesses(System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Threading.CancellationToken)">
            <summary>
            use user access token to get an user's businesses information
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetUserBusinessWabas(System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Threading.CancellationToken)">
            <summary>
            use user access token to get list of wabas with subscribed apps data from a business id 
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetWhatsappBusinessAccount(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns the account information of a WhatsApp Business Account
            https://developers.facebook.com/docs/graph-api/reference/whats-app-business-account/
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetPhoneNumberDetail(System.String,System.Threading.CancellationToken)">
            <summary>
            Get phone number from waba id with field query and filtering
            https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers
            filtering example: filtering=[{ "field":"account_mode", "operator":"EQUAL", "value":"SANDBOX"}]
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetPhoneNumbersByWabaId(System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Collections.Generic.List{GraphApi.Client.ApiClients.Models.Common.GraphApiFilterParam},System.Threading.CancellationToken)">
            <summary>
            Get phone number from waba id with field query and filtering
            https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers
            filtering example: filtering=[{ "field":"account_mode", "operator":"EQUAL", "value":"SANDBOX"}]
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetPhoneNumberSettings(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the settings for a phone number, including storage configuration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.UpdatePhoneNumberSettings(System.String,GraphApi.Client.Payloads.StorageConfiguration,System.Threading.CancellationToken)">
            <summary>
            Updates the settings for a phone number, including storage configuration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.BackupOnPremisesPhoneNumberResponse(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            on premises api - pending to edit and improve
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.InitiatePhoneNumberMigrationAsync(GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigratePhoneNumberObject,System.String,System.Threading.CancellationToken)">
            <summary>
            Initiate migrate phone number to another waba id
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.VerifyPhoneOwnershipRequestAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            request a registration code for phone number migration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.VerifyPhoneOwnershipCodeAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            To verify the code requested from the method VerifyPhoneOwnershipRequestAsync call
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.SetupTwoStepVerificationAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Set up two-step verification for your phone number, as this provides an extra layer of security to the business accounts.
            There is no endpoint to disable two-step verification in cloud api.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.RegisterPhoneNumberAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            You need to register your phone number in the following scenarios:
                Account Creation: When you implement this API, you need to register the phone number you want to use to send messages. We enforce setting two-step verification during account creation to add an extra layer of security to your accounts.
                Name Change: In this case, your phone is already registered and you want to change your display name. To do that, you must first file for a name change on WhatsApp Manager. Once the name is approved, you need to register your phone again under the new name.
                WABA Migration
            Before registering your phone, you need to verify that you own that phone number with a SMS or voice code.
            https://developers.facebook.com/docs/whatsapp/cloud-api/reference/registration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.DeregisterPhoneNumberAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            You can always re-register your phone, if needed.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetWhatsappPhoneNumberProfileAsync(System.String,System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            To complete the following API calls, you need to get a business profile ID. To do that, make a GET call to the /{{Phone-Number-ID}} endpoint and add whatsapp_business_profile as a URL field. Within the whatsapp_business_profile request, you can specify what you want to know from your business.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.ResumableUploadCreateSessionAsync(System.Int64,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Use Resumable Upload Api to upload picture to get file handle for profile picture update
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.UpdateWhatsappPhoneNumberProfileAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.String,System.Threading.CancellationToken)">
            <summary>`
            Update the business profile information such as the business description, email or address. To update your profile, make a POST call to /{{Phone-Number-ID}}/whatsapp_business_profile. In your request, you can include the parameters listed below.
            It is recommended that you use Resumable Upload - Create an Upload Session to obtain an upload ID. Then use this upload ID in a call to Resumable Upload - Upload File Data to obtain the picture handle. This handle can be used for the profile_picture_handle.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetWabaSubscriptionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Get a list of apps subscribed to Webhooks for a WABA
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.CreateWabaSubscriptionsAsync(System.String,GraphApi.Client.Payloads.WabaSubscriptionOverrideCallbackUrl,System.Threading.CancellationToken)">
            <summary>
            Subscribe an app to a WhatsApp Business Account.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.DeleteWabaSubscriptionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            To unsubscribe your app from webhooks for a WhatsApp Business Account
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetConversationAnalyticsAsync(System.String,System.Int64,System.Int64,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Enables one to retrieve the conversation based pricing analytics data for this WhatsApp Business Account
            </summary>
            <param name="whatsappBusinessAccountId">Required. Waba Id</param>
            <param name="startUnixTimestamp">Required. The start date for the date range you are retrieving analytics for.</param>
            <param name="endUnixTimestamp">Required. The end date for the date range you are retrieving analytics for.</param>
            <param name="granularity">Required. The granularity by which you would like to retrieve the analytics. Supported Options:HALF_HOUR,DAILY,MONTHLY</param>
            <param name="phoneNumbers">Optional. An array of phone numbers for which you would like to retrieve analytics. If not provided, all phone numbers added to your WABA are included.</param>
            <param name="countryCodes">Optional. The countries for which you would like to retrieve analytics. Provide an array with 2 letter country codes for the countries you would like to include. If not provided, analytics will be returned for all countries you have communicated with.</param>
            <param name="metricTypes">Optional. List of metrics you would like to receive. If you send an empty list, we return results for all metric types. Supported Options:COST,CONVERSATION</param>
            <param name="conversationTypes">Optional. List of conversation types. If you send an empty list, we return results for all conversation types. Supported Options:UNKNOWN,REGULAR,FREE_ENTRY_POINT,FREE_TIER</param>
            <param name="conversationDirections">Optional. List of conversation directions. If you send an empty list, we return results for all conversation directions. Supported Options: business_initiated,user_initiated</param>
            <param name="conversationCategories">Optional. List of conversation Categories. If you send an empty list, we return results for all conversation categories. Supported Options: authentication,marketing,service,utility</param>
            <param name="dimensions">Optional. List of breakdowns you would like to apply to your metrics. If you send an empty list, we return results without any breakdowns. Supported Options: phone,country,conversation_type,conversation_direction</param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetMessageAnalyticsAsync(System.String,System.DateTime,System.DateTime,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Provides the number and type of messages sent and delivered by the phone numbers associated with a specific WABA
            </summary>
            <param name="whatsappBusinessAccountId">Required. Waba Id</param>
            <param name="start">Required. The start date for the date range you are retrieving analytics for.</param>
            <param name="end">Required. The end date for the date range you are retrieving analytics for.</param>
            <param name="granularity">Required. The granularity by which you would like to retrieve the analytics. Supported Options:HALF_HOUR,DAILY,MONTHLY</param>
            <param name="phoneNumbers">Optional.An array of phone numbers for which you would like to retrieve analytics. If not provided, all phone numbers added to your WABA are included.</param>
            <param name="productTypes">Optional. The types of messages (notification messages and/or customer support messages) for which you want to retrieve notifications. Provide an array and include 0 for notification messages, and 2 for customer support messages. If not provided, analytics will be returned for all messages together.</param>
            <param name="countryCodes">Optional. The countries for which you would like to retrieve analytics. Provide an array with 2 letter country codes for the countries you would like to include. If not provided, analytics will be returned for all countries you have communicated with.</param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetMessageAnalyticsAsync(System.String,System.Int64,System.Int64,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Provides the number and type of messages sent and delivered by the phone numbers associated with a specific WABA
            </summary>
            <param name="whatsappBusinessAccountId">Required. Waba Id</param>
            <param name="startUnixTimestamp">Required. The start date for the date range you are retrieving analytics for.</param>
            <param name="endUnixTimestamp">Required. The end date for the date range you are retrieving analytics for.</param>
            <param name="granularity">Required. The granularity by which you would like to retrieve the analytics. Supported Options:HALF_HOUR,DAILY,MONTHLY</param>
            <param name="phoneNumbers">Optional.An array of phone numbers for which you would like to retrieve analytics. If not provided, all phone numbers added to your WABA are included.</param>
            <param name="productTypes">Optional. The types of messages (notification messages and/or customer support messages) for which you want to retrieve notifications. Provide an array and include 0 for notification messages, and 2 for customer support messages. If not provided, analytics will be returned for all messages together.</param>
            <param name="countryCodes">Optional. The countries for which you would like to retrieve analytics. Provide an array with 2 letter country codes for the countries you would like to include. If not provided, analytics will be returned for all countries you have communicated with.</param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetSystemUsers(System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Collections.Generic.List{GraphApi.Client.ApiClients.Models.Common.GraphApiFilterParam},System.Threading.CancellationToken)">
            <summary>
            Retrieve System User IDs, this is mainly use by BSP
            https://developers.facebook.com/docs/whatsapp/embedded-signup/manage-system-users#retrieve-system-user-ids
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.GetWabaAssignedUsers(System.String,System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Collections.Generic.List{GraphApi.Client.ApiClients.Models.Common.GraphApiFilterParam},System.Threading.CancellationToken)">
            <summary>
            Retrieve assigned users
            You can fetch the assigned users of the WhatsApp Business Account to verify that the user was added. This is not a required step but helps with validation.
            https://developers.facebook.com/docs/whatsapp/embedded-signup/manage-system-users/?translation#retrieve-assigned-users
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.IWhatsappCloudApiBspClient.AddWabaAssignedUser(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Add System Users to a WhatsApp Business Account
            For this API call, you need to use the access token of a System User with admin permissions.
            https://developers.facebook.com/docs/whatsapp/embedded-signup/manage-system-users/?translation#add-system-users-to-a-whatsapp-business-account
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetUserBusinesses(System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Threading.CancellationToken)">
            <summary>
            use user access token to get an user's businesses information
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetUserBusinessWabas(System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Threading.CancellationToken)">
            <summary>
            use user access token to get list of wabas with subscribed apps data from a business id 
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetWhatsappBusinessAccount(System.String,System.Threading.CancellationToken)">
            <summary>
            Returns the account information of a WhatsApp Business Account
            https://developers.facebook.com/docs/graph-api/reference/whats-app-business-account/
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetPhoneNumberDetail(System.String,System.Threading.CancellationToken)">
            <summary>
            Get phone number from waba id with field query and filtering
            https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers
            filtering example: filtering=[{ "field":"account_mode", "operator":"EQUAL", "value":"SANDBOX"}]
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetPhoneNumbersByWabaId(System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Collections.Generic.List{GraphApi.Client.ApiClients.Models.Common.GraphApiFilterParam},System.Threading.CancellationToken)">
            <summary>
            Get phone number from waba id with field query and filtering
            https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers
            filtering example: filtering=[{ "field":"account_mode", "operator":"EQUAL", "value":"SANDBOX"}]
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetPhoneNumberSettings(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets the settings for a phone number, including storage configuration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.UpdatePhoneNumberSettings(System.String,GraphApi.Client.Payloads.StorageConfiguration,System.Threading.CancellationToken)">
            <summary>
            Updates the settings for a phone number, including storage configuration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.BackupOnPremisesPhoneNumberResponse(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            on premises api - pending to edit and improve
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.InitiatePhoneNumberMigrationAsync(GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigratePhoneNumberObject,System.String,System.Threading.CancellationToken)">
            <summary>
            Initiate migrate phone number to another waba id
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.VerifyPhoneOwnershipRequestAsync(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            request a registration code for phone number migration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.VerifyPhoneOwnershipCodeAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            To verify the code requested from the method VerifyPhoneOwnershipRequestAsync call
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.SetupTwoStepVerificationAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Set up two-step verification for your phone number, as this provides an extra layer of security to the business accounts.
            There is no endpoint to disable two-step verification in cloud api.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.RegisterPhoneNumberAsync(System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            You need to register your phone number in the following scenarios:
                Account Creation: When you implement this API, you need to register the phone number you want to use to send messages. We enforce setting two-step verification during account creation to add an extra layer of security to your accounts.
                Name Change: In this case, your phone is already registered and you want to change your display name. To do that, you must first file for a name change on WhatsApp Manager. Once the name is approved, you need to register your phone again under the new name.
                WABA Migration
            Before registering your phone, you need to verify that you own that phone number with a SMS or voice code.
            https://developers.facebook.com/docs/whatsapp/cloud-api/reference/registration
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.DeregisterPhoneNumberAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            You can always re-register your phone, if needed.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetWhatsappPhoneNumberProfileAsync(System.String,System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            To complete the following API calls, you need to get a business profile ID. To do that, make a GET call to the /{{Phone-Number-ID}} endpoint and add whatsapp_business_profile as a URL field. Within the whatsapp_business_profile request, you can specify what you want to know from your business.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.ResumableUploadCreateSessionAsync(System.Int64,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Use Resumable Upload Api to upload picture to get file handle for profile picture update
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.UpdateWhatsappPhoneNumberProfileAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.String,System.Threading.CancellationToken)">
            <summary>`
            Update the business profile information such as the business description, email or address. To update your profile, make a POST call to /{{Phone-Number-ID}}/whatsapp_business_profile. In your request, you can include the parameters listed below.
            It is recommended that you use Resumable Upload - Create an Upload Session to obtain an upload ID. Then use this upload ID in a call to Resumable Upload - Upload File Data to obtain the picture handle. This handle can be used for the profile_picture_handle.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetWabaSubscriptionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Get a list of apps subscribed to Webhooks for a WABA
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.CreateWabaSubscriptionsAsync(System.String,GraphApi.Client.Payloads.WabaSubscriptionOverrideCallbackUrl,System.Threading.CancellationToken)">
            <summary>
            Subscribe an app to a WhatsApp Business Account.
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.DeleteWabaSubscriptionsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            To unsubscribe your app from webhooks for a WhatsApp Business Account
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetMessageAnalyticsAsync(System.String,System.DateTime,System.DateTime,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Provides the number and type of messages sent and delivered by the phone numbers associated with a specific WABA
            </summary>
            <param name="whatsappBusinessAccountId">Required. Waba Id</param>
            <param name="start">Required. The start date for the date range you are retrieving analytics for.</param>
            <param name="end">Required. The end date for the date range you are retrieving analytics for.</param>
            <param name="granularity">Required. The granularity by which you would like to retrieve the analytics. Supported Options:HALF_HOUR,DAILY,MONTHLY</param>
            <param name="phoneNumbers">Optional.An array of phone numbers for which you would like to retrieve analytics. If not provided, all phone numbers added to your WABA are included.</param>
            <param name="productTypes">Optional. The types of messages (notification messages and/or customer support messages) for which you want to retrieve notifications. Provide an array and include 0 for notification messages, and 2 for customer support messages. If not provided, analytics will be returned for all messages together.</param>
            <param name="countryCodes">Optional. The countries for which you would like to retrieve analytics. Provide an array with 2 letter country codes for the countries you would like to include. If not provided, analytics will be returned for all countries you have communicated with.</param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetMessageAnalyticsAsync(System.String,System.Int64,System.Int64,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Provides the number and type of messages sent and delivered by the phone numbers associated with a specific WABA
            </summary>
            <param name="whatsappBusinessAccountId">Required. Waba Id</param>
            <param name="startUnixTimestamp">Required. The start date for the date range you are retrieving analytics for.</param>
            <param name="endUnixTimestamp">Required. The end date for the date range you are retrieving analytics for.</param>
            <param name="granularity">Required. The granularity by which you would like to retrieve the analytics. Supported Options:HALF_HOUR,DAILY,MONTHLY</param>
            <param name="phoneNumbers">Optional.An array of phone numbers for which you would like to retrieve analytics. If not provided, all phone numbers added to your WABA are included.</param>
            <param name="productTypes">Optional. The types of messages (notification messages and/or customer support messages) for which you want to retrieve notifications. Provide an array and include 0 for notification messages, and 2 for customer support messages. If not provided, analytics will be returned for all messages together.</param>
            <param name="countryCodes">Optional. The countries for which you would like to retrieve analytics. Provide an array with 2 letter country codes for the countries you would like to include. If not provided, analytics will be returned for all countries you have communicated with.</param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetConversationAnalyticsAsync(System.String,System.Int64,System.Int64,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Collections.Generic.List{System.String},System.Threading.CancellationToken)">
            <summary>
            Enables one to retrieve the conversation based pricing analytics data for this WhatsApp Business Account
            </summary>
            <param name="whatsappBusinessAccountId">Required. Waba Id</param>
            <param name="startUnixTimestamp">Required. The start date for the date range you are retrieving analytics for.</param>
            <param name="endUnixTimestamp">Required. The end date for the date range you are retrieving analytics for.</param>
            <param name="granularity">Required. The granularity by which you would like to retrieve the analytics. Supported Options:HALF_HOUR,DAILY,MONTHLY</param>
            <param name="phoneNumbers">Optional. An array of phone numbers for which you would like to retrieve analytics. If not provided, all phone numbers added to your WABA are included.</param>
            <param name="countryCodes">Optional. The countries for which you would like to retrieve analytics. Provide an array with 2 letter country codes for the countries you would like to include. If not provided, analytics will be returned for all countries you have communicated with.</param>
            <param name="metricTypes">Optional. List of metrics you would like to receive. If you send an empty list, we return results for all metric types. Supported Options:COST,CONVERSATION</param>
            <param name="conversationTypes">Optional. List of conversation types. If you send an empty list, we return results for all conversation types. Supported Options:UNKNOWN,REGULAR,FREE_ENTRY_POINT,FREE_TIER</param>
            <param name="conversationDirections">Optional. List of conversation directions. If you send an empty list, we return results for all conversation directions. Supported Options: business_initiated,user_initiated</param>
            <param name="conversationCategories">Optional. List of conversation Categories. If you send an empty list, we return results for all conversation categories. Supported Options: authentication,marketing,service,utility</param>
            <param name="dimensions">Optional. List of breakdowns you would like to apply to your metrics. If you send an empty list, we return results without any breakdowns. Supported Options: phone,country,conversation_type,conversation_direction</param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetSystemUsers(System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Collections.Generic.List{GraphApi.Client.ApiClients.Models.Common.GraphApiFilterParam},System.Threading.CancellationToken)">
            <summary>
            Retrieve System User IDs, this is mainly use by BSP
            https://developers.facebook.com/docs/whatsapp/embedded-signup/manage-system-users#retrieve-system-user-ids
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.GetWabaAssignedUsers(System.String,System.String,System.Collections.Generic.List{System.String},GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Collections.Generic.List{GraphApi.Client.ApiClients.Models.Common.GraphApiFilterParam},System.Threading.CancellationToken)">
            <summary>
            Retrieve assigned users
            You can fetch the assigned users of the WhatsApp Business Account to verify that the user was added. This is not a required step but helps with validation.
            https://developers.facebook.com/docs/whatsapp/embedded-signup/manage-system-users/?translation#retrieve-assigned-users
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiBspClient.AddWabaAssignedUser(System.String,System.String,System.String,System.Threading.CancellationToken)">
            <summary>
            Add System Users to a WhatsApp Business Account
            For this API call, you need to use the access token of a System User with admin permissions.
            https://developers.facebook.com/docs/whatsapp/embedded-signup/manage-system-users/?translation#add-system-users-to-a-whatsapp-business-account
            </summary>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiConversationalAutomationClient.GetConversationalAutomationsAsync(System.String,GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Threading.CancellationToken)">
            <summary>
            <b>View The Current Configurations Via The API</b>
            <br />
            You can view a list of configuration of Conversational Components on a given waba by calling the GET endpoint.
            </summary>
            <param name="wabaId"></param>
            <param name="paginationParam"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiConversationalAutomationClient.GetConversationalAutomationAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            <b>View The Current Configuration Via The API</b>
            <br />
            You can view a configuration of Conversational Components on a given phone number id by calling the GET endpoint.
            </summary>
            <param name="phoneNumberId"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiConversationalAutomationClient.UpdateConversationalAutomationAsync(System.String,GraphApi.Client.Payloads.Models.ConversationalComponent.ConversationalAutomation,System.Threading.CancellationToken)">
            <summary>
            <b>Configure Conversational Components Via The API</b>
            <br />
            You can configure Conversational Components on a given phone number by calling the POST endpoint.
            </summary>
            <param name="phoneNumberId">Required</param>
            <param name="updateObject">Required</param>
            <param name="cancellationToken">Required</param>
            <returns>success</returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.GetFlowsAsync(System.String,GraphApi.Client.ApiClients.Models.Common.CursorBasedPaginationParam,System.Threading.CancellationToken)">
            <summary>
            Get list of flows<br/>
            To retrieve a list of Flows under a WhatsApp Business Account (WABA)
            </summary>
            <param name="whatsappBusinessAccountId">WABA Id</param>
            <param name="paginationParam"></param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.GetFlowAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Get Flow's details by id <br/>
            This request will return a single Flow's details.
            By default it will return the fields id,name, status, categories, validation_errors. You can request other fields by using the fields param in the request. The request example below includes all possible fields.
            </summary>
            <param name="flowId">Id of Flow</param>
            <param name="optionalFields">Optional field for query params</param>
            <param name="cancellationToken"></param>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.GetFlowAssetsAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Retrieving a Flow's List of Assets
            <br />
            Returns all assets attached to a specified Flow.
            </summary>
            <param name="flowId"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.CreateFlowAsync(System.String,GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiCreateFlowObject,System.Threading.CancellationToken)">
            <summary>
            Create new flows in DRAFT status.<br/>
            Then make changes to the Flow by uploading an updated JSON file
            via FlowClient.UpdateFlowJsonAsync()
            </summary>
            <param name="whatsappBusinessAccountId"></param>
            <param name="flow"></param>
            <param name="cancellationToken"></param>
            <returns>(string) id</returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.UpdateFlowMetadataAsync(System.String,GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiUpdateFlowMetadataObject,System.Threading.CancellationToken)">
            <summary>
            Update the name or categories of a flow.
            </summary>
            <param name="flowId">Id of Flow</param>
            <param name="metadata">Name and Categories(optional)</param>
            <param name="cancellationToken"></param>
            <returns>success(bool)</returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.UpdateFlowJsonAsync(System.String,System.Byte[],System.Threading.CancellationToken)">
            <summary>
            To update Flow JSON for a specified Flow
            </summary>
            <param name="flowId">Id of Flow</param>
            <param name="fileBytes">
                File with the JSON content.
                The size is limited to 10 MB
            </param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.DeleteFlowAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            While a Flow is in DRAFT status, it can be deleted.
            </summary>
            <param name="flowId"></param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.GetFlowWebPreviewUrlAsync(System.String,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Preview the Flow via web url<br/>
            </summary>
            <param name="flowId">Id of Flow</param>
            <param name="isInvalidate">Is invalidate Url needed</param>
            <param name="cancellationToken"></param>
            <returns></returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.PublishFlowAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Publishing a Flow<br />
            This request updates the status of the Flow to <b>PUBLISHED</b>.<br />
            <b>This action is not reversible.</b><br />
            The Flow and its assets become immutable once published.<br />
            To update the Flow after that, you must create a new Flow.<br />
            You specify the existing Flow ID as the clone_flow_id parameter while creating to copy the existing flow.
            </summary>
            <param name="flowId"></param>
            <param name="cancellationToken"></param>
            <returns>Success(bool)</returns>
        </member>
        <member name="M:GraphApi.Client.ApiClients.WhatsappCloudApiFlowClient.DeprecateFlowAsync(System.String,System.Threading.CancellationToken)">
            <summary>
            Deprecating a Flow<br />
            Once a Flow is published, it cannot be modified or deleted, but can be marked as <b>deprecated</b>.
            </summary>
            <param name="flowId"></param>
            <param name="cancellationToken"></param>
            <returns>Success(bool)</returns>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerMessageObject.QuickReplies">
            <summary>
            A maximum of 13 quick replies are supported and each quick reply allows up to 20 characters before being truncated. Quick replies only support plain text.
            https://developers.facebook.com/docs/messenger-platform/instagram/features/quick-replies
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerAttachmentDataObject.Type">
            <summary>
            file or template or MEDIA_SHARE or sticker,i.e. like_heart
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.Id">
            <summary>
            POST-ID for MEDIA_SHARE, ATTACHMENT-ID for MEDIA_SHARE
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.NotificationMessagesCtaText">
            <summary>
            enum { ALLOW, GET, GET_UPDATES, OPT_IN, SIGN_UP }
            ALLOW – set optin message button text to “Allow messages”
            GET – set optin message button text to “Get messages”
            GET_UPDATES – set optin message button text to “Get updates”, this is also default if notification_messages_cta_text is not set
            OPT_IN – set optin message button text to “Opt in to messages”
            SIGN_UP – set optin message button text to “Sign up for messages”
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.TemplateType">
            <summary>
            notification_messages or product
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.NotificationMessagesTimezone">
            <summary>
            Timezone for the person receiving the message, i.e. UTC
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.Title">
            <summary>
            The title to display in the template, can not exceed 65 characters. If no value is assigned, the value defaults to "Updates and promotions"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.ImageUrl">
            <summary>
            The URL for the image to display in the template
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.Payload">
            <summary>
            Required for the type of marketing message, such as promotional messaging or product release messaging, for this marketing message opt in request
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerPayloadObject.ImageAspectRation">
            <summary>
            enum { HORIZONTAL, SQUARE }
            Aspect ratio for the image
            SQUARE – render square image (1:1). Image will be cropped if needed
            HORIZONTAL – render horizontal image (1.91:1). Image will be cropped if needed
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerElementObject.Title">
            <summary>
            Notification Messages Elements: The title to display in the template, can not exceed 65 characters. If no value is assigned, the value defaults to "Updates and promotions" 
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerElementObject.ImageUrl">
            <summary>
            The URL for the image to display in the template
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerElementObject.NotificationMessagesCtaText">
            <summary>
            enum { ALLOW, FREQUENCY, GET, GET_UPDATES, OPT_IN, SIGN_UP }
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerElementObject.NotificationMessageFrequency">
            <summary>
            enum { DAILY, WEEKLY, MONTHLY }	
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerElementObject.Payload">
            <summary>
            INFORMATION-ABOUT-THIS-MESSAGE
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerElementObject.Id">
            <summary>
            PRODUCT-ID, https://developers.facebook.com/docs/messenger-platform/instagram/features/product-template#send-a-product-message
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerElementObject.NotificationMessagesReOptin">
            <summary>
            ENUM  {ENABLED, DISABLED}
            </summary>
        </member>
        <member name="T:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerQuickReplyObject">
            <summary>
            https://developers.facebook.com/docs/messenger-platform/instagram/features/quick-replies
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerQuickReplyObject.ContentType">
            <summary>
            Quick replies only support plain text.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerQuickReplyObject.Title">
            <summary>
            allows up to 20 characters before being truncated.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerQuickReplyObject.Payload">
            <summary>
            POSTBACK PAYLOAD, will be returned from webhook, when the users clicks it
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerRecipientObject.NotificationMessagesToken">
            <summary>
            For Opt In Followup Messages
            https://developers.facebook.com/docs/messenger-platform/marketing-messages#notification-message-tokens
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerSendMessageObject.MessagingType">
            <summary>
            By default, MESSAGE_TAG
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.InstagramPageObjects.InstagramPageMessengerSendMessageObject.Tag">
            <summary>
            By default, HUMAN_AGENT
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiAddressObject.Street">
            <summary>
            Optional.
            Street number and name
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiAddressObject.City">
            <summary>
            Optional.
            City name.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiAddressObject.State">
            <summary>
            Optional.
            State abbreviation.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiAddressObject.Zip">
            <summary>
            Optional.
            ZIP code.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiAddressObject.Country">
            <summary>
            Optional.
            Full country name.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiAddressObject.CountryCode">
            <summary>
            Optional.
            Two-letter country abbreviation.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiAddressObject.Type">
            <summary>
            Optional.
            Standard Values: HOME, WORK
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Addresses">
            <summary>
            Optional.
            Full contact address(es) —see AddressObject.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Birthday">
            <summary>
            Optional.
            YYYY-MM-DD formatted string.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Emails">
            <summary>
            Optional.
            Contact email address(es) —see EmailObject.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Name">
            <summary>
            Required.
            Full contact name — see NameObject.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Org">
            <summary>
            Optional.
            Contact organization information —see OrgObject.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Ims">
            <summary>
            Not Support Cloud Api, but only On Premises Api
            Messaging contact information
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Phones">
            <summary>
            Optional.
            Contact phone number(s) —see PhoneObject.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiContactObject.Urls">
            <summary>
            Optional.
            Contact URL(s) —see UrlObject.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiEmailObject.Email">
            <summary>
            Optional.
            Email address
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiEmailObject.Type">
            <summary>
            Optional.
            Standard Values: HOME, WORK
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiNameObject.FormattedName">
            <summary>
            Required.
            Full name, as it normally appears.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiNameObject.FirstName">
            <summary>
            Optional*.
            First name
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiNameObject.LastName">
            <summary>
            Optional*.
            Last name.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiNameObject.MiddleName">
            <summary>
            Optional*.
            Middle name.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiNameObject.Suffix">
            <summary>
            Optional*.
            Name suffix.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiNameObject.Prefix">
            <summary>
            Optional*.
            Name prefix.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiOrgObject.Company">
            <summary>
            Optional.
            Name of the contact's company.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiOrgObject.Title">
            <summary>
            Optional.
            Contact's business title.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiOrgObject.Department">
            <summary>
            Optional.
            Name of the contact's department.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiPhoneObject.Phone">
            <summary>
            Optional.
            Automatically populated with the wa_id value as a formatted phone number.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiPhoneObject.Type">
            <summary>
            Optional.
            Standard Values: CELL, MAIN, IPHONE, HOME, WORK
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiPhoneObject.WaId">
            <summary>
            Optional.
            WhatsApp ID.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiUrlObject.Url">
            <summary>
            Optional.
            URL.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ContactObjects.WhatsappCloudApiUrlObject.Type">
            <summary>
            Optional.
            Standard Values: HOME, WORK
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiCreateFlowObject.CloneFlowId">
            <summary>
            Optional.
            ID of source Flow to clone.
            You must have permission to access the specified Flow.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiUpdateFlowMetadataObject.Name">
            <summary>
            Flow name
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiUpdateFlowMetadataObject.Categories">
            <summary>
            Optional
            <br />
            A list of Flow categories.
            Missing value will keep existing categories.
            If provided, at least one values is required.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiUpdateFlowMetadataObject.EndpointUri">
            <summary>
            Optional. (Currently Not Supported)
            <br />
            The URL of the WA Flow Endpoint.
            <br />
            Starting from Flow JSON version 3.0 this property should be specified only via API.
            <br />
            Do not provide this field if you are cloning a Flow with Flow JSON version below 3.0.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.FlowObjects.WhatsappCloudApiUpdateFlowMetadataObject.ApplicationId">
            <summary>
            Optional. (Currently Not Supported)
            <br />
            The ID of the Meta application which will be connected to the Flow. 
            <br />
            Starting from Flow JSON version 3.0 this property should be specified only via API.
            <br />
            Do not provide this field if you are cloning a Flow with Flow JSON version below 3.0.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiCurrencyObject.FallbackValue">
            <summary>
            Required.
            Default text if localization fails.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiCurrencyObject.Code">
            <summary>
            Required.
            Currency code as defined in ISO 4217.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiCurrencyObject.Amount1000">
            <summary>
            Required.
            Amount multiplied by 1000.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiDateTimeObject.FallbackValue">
            <summary>
            Required.
            Default text. For Cloud API, we always use the fallback value, and we do not attempt to localize using other optional fields.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeComponentObject.DayOfWeek">
            <summary>
            Optional.
            If different from the value derived from the date (if specified), use the derived value. Both strings and numbers are accepted.
            Options: "MONDAY", 1, "TUESDAY", 2, "WEDNESDAY", 3, "THURSDAY", 4, "FRIDAY", 5, "SATURDAY", 6, "SUNDAY", 7
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeComponentObject.Year">
            <summary>
            Optional.
            The year
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeComponentObject.Month">
            <summary>
            Optional.
            The month.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeComponentObject.DayOfMonth">
            <summary>
            Optional.
            The day of month.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeComponentObject.Hour">
            <summary>
            Optional.
            The hour.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeComponentObject.Minute">
            <summary>
            Optional.
            The minute.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeComponentObject.Calendar">
            <summary>
            Optional.
            Type of calendar.
            Options: GREGORIAN, SOLAR_HIJRI
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmDateTimeUnixEpochObject.Timestamp">
            <summary>
            Required.
            Epoch timestamp in seconds. This field is planned to be deprecated.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmObject.Namespace">
            <summary>
            Required.
            The namespace to be used. Beginning with v2.2.7, if the namespace does not match up to the element_name, the message fails to send.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmObject.ElementName">
            <summary>
            Required.
            The element name that indicates which template to use within the namespace. Beginning with v2.2.7, if the element_name does not match up to the namespace, the message fails to send.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmObject.Language">
            <summary>
            Required.
            Allows for the specification of a deterministic language. See the Language section for more information.
            This field used to allow for a fallback option, but this has been deprecated with v2.27.8.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiHsmObject.LocalizableParams">
            <summary>
            Required.
            This field is an array of values to apply to variables in the template. See the Localizable Parameters section for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiLocalizableParamObject.Default">
            <summary>
            Required.
            The code of the language or locale to use. Accepts both language and language_locale formats (e.g., en and en_US).
            See Supported Languages for all codes.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiLocalizableParamObject.Currency">
            <summary>
            Optional.
            If the currency object is used, it contains required parameters currency_code and amount_1000.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.HsmObjects.WhatsappCloudApiLocalizableParamObject.DateTime">
            <summary>
            Optional.
            If the date_time object is used, further definition of the date and time is required. See the example below for two of the options.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.Button">
            <summary>
            Required for List Messages.
            Button content. It cannot be an empty string and must be unique within the message Does not allow emojis or markdown.
            Maximum length: 20 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.Buttons">
            <summary>
            Required for Reply Button Messages.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.Sections">
            <summary>
            Required for List Messages and Multi-Product Messages.
            Array of SectionObject. There is a minimum of 1 and maximum of 10. See section object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.CatalogId">
            <summary>
            Required for Single Product Messages and Multi-Product Messages.
            Unique identifier of the Facebook catalog linked to your WhatsApp Business Account. This ID can be retrieved via Commerce Manager.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.ProductRetailerId">
            <summary>
            Required for Single Product Messages and Multi-Product Messages.
            Unique identifier of the product in a catalog.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.Parameters">
            <summary>
            Required for Catalog Message and Flow Message.
            Item SKU number. Labeled as Content ID in the Commerce Manager.
            The thumbnail of this item will be used as the message's header image.
            If the parameters object is omitted, the product image of the first item in your catalog will be used.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.Name">
            <summary>
            Required for Single Product Messages, Multi-Product Messages and Flow Message.
            For Flow Message, Value must be "flow".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionObject.Mode">
             <summary>
            Optional for Flows Messages.
             The current mode of the Flow, either draft or published.
             Default: published
             </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiFlowActionPayloadObject.Screen">
            <summary>
            Required. The id of the first screen of the Flow.
            It needs to be an entry screen*. https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson#routing-rules
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiFlowActionPayloadObject.Data">
            <summary>
            Optional. The input data for the first screen of the Flow. Must be a non-empty object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.ThumbnailProductRetailerId">
            <summary>
            Required for catalog message.
            Item SKU number. Labeled as Content ID in the Commerce Manager.
            The thumbnail of this item will be used as the message's header image.
            If the parameters object is omitted, the product image of the first item in your catalog will be used.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.Mode">
            <summary>
            The Flow can be in either draft or published mode.
            (Default value: published)
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.FlowMessageVersion">
            <summary>
            Value must be "3".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.FlowToken">
            <summary>
            Flow token that is generated by the business to serve as an identifier.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.FlowId">
            <summary>
            Unique ID of the Flow provided by WhatsApp.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.FlowCta">
            <summary>
            Text on the CTA button. For example: "Signup" Character limit - 20 characters (no emoji).
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.FlowAction">
            <summary>
            navigate or data_exchange.
            (Default value: navigate)
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiActionMessageParameterObject.FlowActionPayload">
            <summary>
            Required if flow_action is navigate. Should be omitted otherwise.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiButtonObject.Type">
            <summary>
            Required.
            only supported type is reply (for Reply Button Messages)
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiHeaderObject.Type">
            <summary>
            Required.
            The header type you would like to use. Supported values are:
            text: Used for List Messages, Reply Buttons, and Multi-Product Messages.
            video: Used for Reply Buttons.
            image: Used for Reply Buttons.
            document: Used for Reply Buttons.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiHeaderObject.Text">
            <summary>
            Required if type is set to text.
            Text for the header. Formatting allows emojis, but not markdown.
            Maximum length: 60 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiHeaderObject.Video">
            <summary>
            Required if type is set to video.
            Contains the media object for this video.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiHeaderObject.Image">
            <summary>
            Required if type is set to image.
            Contains the media object for this image.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiHeaderObject.Document">
            <summary>
            Required if type is set to document.
            Contains the media object for this document.
            </summary>
        </member>
        <member name="T:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiInteractiveObject">
            <summary>
            Sending Interactive Messages
            https://developers.facebook.com/docs/whatsapp/guides/interactive-messages/
            https://developers.facebook.com/docs/whatsapp/api/messages#body-object
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiInteractiveObject.Type">
            <summary>
            Required.
            The type of interactive message you want to send. Supported values:
            - list: Use it for List Messages.
            - button: Use it for Reply Buttons.
            - product: Use it for Single Product Messages.
            - product_list: Use it for Multi-Product Messages.
            - catalog_message: Free-form messages containing a button that, when tapped, displays your product catalog within WhatsApp.
            - flow: Use it for Flow Messages.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiInteractiveObject.Header">
            <summary>
            Required for type product_list. Optional for other types.
            Header content displayed on top of a message. See header object for more information.
            You cannot set a header if your interactive object is of product type.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiInteractiveObject.Body">
            <summary>
            Optional for type product. Required for other message types.
            The body of the message. Emojis and markdown are supported.
            Maximum length: 1024 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiInteractiveObject.Footer">
            <summary>
            Optional.
            The footer of the message. Emojis and markdown are supported.
            Maximum length: 60 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiInteractiveObject.Action">
            <summary>
            Required.
            Action you want the user to perform after reading the message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiProductObject.ProductRetailerId">
            <summary>
            Required for Multi-Product Messages.
            Unique identifier of the product in a catalog.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiReplyObject.Id">
            <summary>
            Required.
            Unique identifier for your button. This ID is returned in the webhook when the button is clicked by the user.
            Maximum length: 256 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiReplyObject.Title">
            <summary>
            Required.
            Button title. It cannot be an empty string and must be unique within the message. Does not allow emojis or markdown.
            Maximum length: 20 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiSectionObject.Title">
            <summary>
            Required if the message has more than one section.
            Title of the section.
            Maximum length: 24 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiSectionObject.ProductItems">
            <summary>
            Required for List Messages.
            Contains a list of rows.
            Additional item objects (up to 30)
            Each row must have a title (Maximum length: 24 characters) and an ID (Maximum length: 200 characters). You can add a description (Maximum length: 72 characters), but it is optional.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiSectionObject.Rows">
            <summary>
            Required for Multi-Product Messages.
            Array of product objects. There is a minimum of 1 product per section. There is a maximum of 30 products across all sections.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageComponentObject.Type">
            <summary>
            Required.
            Describes the component type.
            Values: header, body, or button, carousel. For text-based templates, we only support the type=body.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageComponentObject.SubType">
            <summary>
            Optional. Used when type is set to button.
            Supported values are:
            quick_reply: Refers to a previously created quick reply button that allows for the customer to return a predefined message. See Callback from a Quick Reply Button Click for an example of a response from a quick reply button.
            url: Refers to a previously created button that allows the customer to visit the URL generated by appending the text parameter to the predefined prefix URL in the template.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageComponentObject.Index">
            <summary>
            Optional. Used when type is set to button.
            Position index of the button. You can have up to 3 buttons using index values of 0-2.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageComponentObject.Parameters">
            <summary>
            Optional.
            Array of parameter objects with the content of the message.
            Required when type=button.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageComponentObject.AddSecurityRecommendation">
            <summary>
            Optional.
            Set to true if you want the template to include the string, For your security, do not share this code. Set to false to exclude the string.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageComponentObject.CodeExpirationMinutes">
            <summary>
            Optional.
            Indicates number of minutes the password or code is valid.
            If omitted, the code expiration warning will not be displayed in the delivered message.
            Minimum 1, maximum 90.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageCardObject.CardIndex">
            <summary>
            Zero-indexed order in which card appears within the card carousel. 0 indicates first card, 1 indicates second card, etc.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageCardComponentObject.Type">
            <summary>
            Required.
            Describes the component type.
            Values: header, body, or button, carousel. For text-based templates, we only support the type=body.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageCardComponentObject.SubType">
            <summary>
            Optional. Used when type is set to button.
            Supported values are:
            quick_reply: Refers to a previously created quick reply button that allows for the customer to return a predefined message. See Callback from a Quick Reply Button Click for an example of a response from a quick reply button.
            url: Refers to a previously created button that allows the customer to visit the URL generated by appending the text parameter to the predefined prefix URL in the template.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageCardComponentObject.Index">
            <summary>
            Optional. Used when type is set to button.
            Position index of the button. You can have up to 3 buttons using index values of 0-2.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTemplateMessageCardComponentObject.Parameters">
            <summary>
            Optional.
            Array of parameter objects with the content of the message.
            Required when type=button.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTextBodyObject.Text">
            <summary>
            Required.
            The body content of the message. Emojis and markdown are supported. Links are supported.
            Maximum length: 1024 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.InteractiveObjects.WhatsappCloudApiTextFooterObject.Text">
            <summary>
            Required if the footer object is present.
            The footer content. Emojis and markdown are supported. Links are supported.
            Maximum length: 60 characters
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.LocationObjects.WhatsappCloudApiLocationObject.Longitude">
            <summary>
            Required.
            Longitude of the location.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.LocationObjects.WhatsappCloudApiLocationObject.Latitude">
            <summary>
            Required.
            Latitude of the location.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.LocationObjects.WhatsappCloudApiLocationObject.Name">
            <summary>
            Optional.
            Name of the location.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.LocationObjects.WhatsappCloudApiLocationObject.Address">
            <summary>
            Optional.
            Address of the location. Only displayed if name is present.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiMediaObject.Id">
            <summary>
            Required when type is audio, document, image, sticker, or video and you are not using a link.
            The media object ID. Do not use this field when message type is set to text.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiMediaObject.Link">
            <summary>
            Required when type is audio, document, image, sticker, or video and you are not using an uploaded media ID.
            The protocol and URL of the media to be sent. Use only with HTTP/HTTPS URLs.
            Do not use this field when message type is set to text.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiMediaObject.Caption">
            <summary>
            Optional.
            Describes the specified document, image, or video media.
            Do not use with audio or sticker media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiMediaObject.Filename">
            <summary>
            Optional.
            Describes the filename for the specific document. Use only with document media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiMediaObject.Provider">
            <summary>
            Optional.
            This path is optionally used with a link when the HTTP/HTTPS link is not directly accessible and requires additional configurations like a bearer token.
            </summary>
        </member>
        <member name="T:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiProviderObject">
            <summary>
            Media Providers
            https://developers.facebook.com/docs/whatsapp/api/settings/media-providers
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiProviderObject.Name">
            <summary>
            Required.
            The name for the provider
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiProviderObject.Type">
            <summary>
            Required.
            The type of provider
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.MediaObjects.WhatsappCloudApiProviderObject.Config">
            <summary>
            Required.
            The ConfigObject
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ReactionObjects.WhatsappCloudApiReactionObject.MessageId">
            The WhatsApp Message ID (wamid) of the message on which the reaction should appear. The reaction will not be sent if:
                The message is older than 30 days
                The message is a reaction message
                The message has been deleted
            If the ID is of a message that has been deleted, the message will not be delivered.
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.ReactionObjects.WhatsappCloudApiReactionObject.Emoji">
            <summary>
            Emoji to appear on the message.
                All emojis supported by Android and iOS devices are supported.
                Rendered-emojis are supported.
                If using emoji unicode values, values must be Java- or JavaScript-escape encoded.
                Only one emoji can be sent in a reaction message
                Use an empty string to remove a previously sent emoji.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiCreateTemplateObject.Category">
            <summary>
            Required.
            The type of message template.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiCreateTemplateObject.SubCategory">
            <summary>
            Sub category of the template
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiCreateTemplateObject.Name">
            <summary>
            Required.
            The name of the message template.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiCreateTemplateObject.Language">
            <summary>
            Required.
            The language of the message template
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiCreateTemplateObject.Components">
            <summary>
            Optional.
            Array of components objects containing the parameters of the message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiCreateTemplateObject.AllowCategoryChange">
            <summary>
            Set to true to allow us to assign a category based on our template guidelines and the template's contents.
            This can prevent your template from being rejected for miscategorization. See Template Categories.
            If omitted, template will not be auto-assigned a category and may get rejected if determined to be miscategorized.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiCreateTemplateObject.MessageSendTtlSeconds">
            <summary>
            Time to live for message template sent. If users are offline for more than TTL duration after message template is sent, message will be dropped from message queue and will not be delivered.
            Only allowed for authentication message templates.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.TemplateButtonObject.Type">
            <summary>
            FLOW
            QUICK_REPLY
            MPM for multi-product message (MPM) templates
            CATALOG for Catalog Templates
            OTP for Authentication Templates
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.TemplateButtonObject.Text">
            <summary>
            Button label text. 25 characters maximum.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.TemplateButtonObject.FlowId">
            <summary>
            Unique identifier of the Flow provided by WhatsApp. The Flow must be published.
            </summary>
            <example>123456789012345</example>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.TemplateButtonObject.FlowAction">
            <summary>
            Use 'navigate' to predefine the first screen as part of the template message.
            Use 'data_exchange' for advanced use-cases where the first screen is provided by your endpoint.
            </summary>
            <remarks>Default: navigate</remarks>
            <example>data_exchange</example>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.TemplateButtonObject.NavigateScreen">
            <summary>
            Required only if flow_action is navigate. The id of the first screen of the Flow.
            </summary>
            <example>flow_json_first_screen</example>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiTemplateMessageObject.Name">
            <summary>
            Required.
            Name of the template. The message template name can only have lowercase letters and underscores.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiTemplateMessageObject.Language">
            <summary>
            Required.
            Contains a language object. Specifies the language the template may be rendered in.
            Only the deterministic language policy works with media template messages.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TemplateObjects.WhatsappCloudApiTemplateMessageObject.Components">
            <summary>
            Optional.
            Array of components objects containing the parameters of the message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TextObjects.WhatsappCloudApiTextObject.Body">
            <summary>
            Required.
            Contains the text of the message, which can contain URLs and formatting.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.TextObjects.WhatsappCloudApiTextObject.PreviewUrl">
             <summary>
             Optional.
             By default, WhatsApp recognizes URLs and makes them clickable, but you can also include a preview box with more information about the link. Set this field to true if you want to include a URL preview box.
            
             The majority of the time, the receiver will see a URL they can click on when you send an URL, set preview_url to true, and provide a body object with a http or https link.
            
             URL previews are only rendered after one of the following has happened:
                 The business has sent a message template to the user.
                 The user initiates a conversation with a "click to chat" link.
                 The user adds the business phone number to their address book and initiates a conversation.
            
             Default: false.
            
             If you have used the On-Premises API, you have seen this field being used inside the message object. Please use preview_url inside the text object for Cloud API calls.
             </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiLanguageObject.Code">
            <summary>
            Required.
            The code of the language or locale to use. Accepts both language and language_locale formats (e.g., en and en_US).
            See WhatsAppLanguage for all codes.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiLanguageObject.Policy">
            <summary>
            Required.
            Default (and only supported option): deterministic
            The language policy the message should follow. See Language Policy Options for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.RecipientType">
            <summary>
            Optional.
            The type of recipient the message is being sent to.
            Supported value: individual
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.To">
            <summary>
            Required.
            The WhatsApp ID for the recipient of your message. Use the ID returned from the contacts endpoint.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.BizOpaqueCallbackData">
            <summary>
            Optional.
            An arbitrary string, useful for tracking.
            For example, you could pass the message template ID in this field to track your customer's journey starting from the first message you send.
            You could then track the ROI of different message template types to determine the most effective one.
            Any app subscribed to the messages webhook field on the WhatsApp Business Account can get this string, as it is included in statuses object within webhook payloads.
            Cloud API does not process this field, it just returns it as part of sent/delivered/read message webhooks.
            Maximum 512 characters.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Type">
            <summary>
            Optional for text messages. Required for all other message types.
            The type of message you would like to send.
            Supported values:
            audio - Used in media messages.
            contact - Used in contact messages.
            document - Used in media messages.
            image - Used in media messages.
            location - Used in location messages.
            sticker - Used in media messages.
            template - Used to create message templates.
            text - Default. Used on text messages.
            video - Used in media messages.
            interactive - Used in interactive messages.
            hsm - This option will be deprecated when we launch the WhatsApp Business API v2.39. Use template instead.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Text">
            <summary>
            Required for messages of text type.
            Contains a text object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Audio">
            <summary>
            Required when type is set to audio.
            The media object containing audio.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Document">
            <summary>
            Required when type is set to document.
            The media object containing a document.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Image">
            <summary>
            Required when type is set to image.
            The media object containing an image.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Sticker">
            <summary>
            Required when type is set to sticker.
            The media object containing a sticker.
            only webp file accepted and dimension should be 512 * 512
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Video">
            <summary>
            Required when type is set to video.
            The media object containing a video.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Contacts">
            <summary>
            Required when type is set to contacts.
            Contains a contacts object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Location">
            <summary>
            Required when type is set to location.
            Contains a location object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Template">
            <summary>
            Only used with message templates.
            Contains a template object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Hsm">
            <summary>
            Only used with message templates.
            Contains an hsm object. This option will be deprecated when we launch the WhatsApp Business API v2.39. Use the template object instead.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Interactive">
            <summary>
            Required for interactive messages.
            This object contains information about the message you would like to send.
            The components of each interactive object generally follow a consistent pattern: header, body, footer, and action. See the interactive object below for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Status">
            <summary>
            A message's status. You can use this field to mark a message as read.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.MessagingProduct">
            <summary>
            Required. Only used for Cloud API.
            Messaging service used for the request. Use "whatsapp".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Context">
            <summary>
            Required if replying to any message in the conversation. Only used for Cloud API.
            An object containing the ID of a previous message you are replying to. For example: {"message_id":"MESSAGE_ID"}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiMessageObject.Reaction">
            <summary>
            Required if reacting to any message with emoji in the conversation.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Type">
            <summary>
            Required.
            Describes the parameter type.
            Values: text, currency, date_time, image, document, video, location, payload, action and limited_time_offer
            The media types (image, document and video) follow the same format as those used in standard media messages, see the Media documentation for more information.
            Only PDF documents are currently supported for media message templates.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Text">
            <summary>
            Required when type is set to text.
            The text message parameter.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Payload">
            <summary>
            Required when type is set to payload for quick_reply buttons.
            Developer-defined payload that will be returned when the button is clicked in addition to the display text on the button.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Image">
            <summary>
            Required when type is set to image.
            The media object containing image.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Audio">
            <summary>
            Required when type is set to audio.
            The media object containing audio.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Document">
            <summary>
            Required when type is set to document.
            The media object containing a document.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Video">
            <summary>
            Required when type is set to video.
            The media object containing a video.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Location">
            <summary>
            Required when type is set to location.
            Contains a location object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.DateTime">
            <summary>
            Required when type is set to date_time.
            The date time parameter.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.ReferenceId">
            <summary>
            Unique identifier for the order or invoice provided by the business. This cannot be an empty string and can only contain English letters, numbers, underscores, dashes, or dots, and should not exceed 35 characters.
            The reference_id must be unique for each order_details message for a given business. If there is a need to send multiple order_details messages for the same order, it is recommended to include a sequence number in the reference_id (for example, "BM345A-12") to ensure reference_id uniqueness.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.PaymentType">
            <summary>
            Required for order_details message. Must be "p2m-lite:stripe".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.PaymentConfiguration">
            <summary>
            Required for order_details message. The name of the pre-configured payment configuration to use for this order and must not exceed 60 characters. This value must match with a payment configuration set up on the WhatsApp Business Manager as shown here.
            When payment_configuration is invalid, the customer will be unable to pay for their order. We strongly advise businesses to conduct extensive testing of this setup during the integration phase.    
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Currency">
            <summary>
            Required for order_details message. The currency for this order. Must be "SGD".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.TotalAmount">
            <summary>
            Required for order_details message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Order">
            <summary>
            Required for order_details message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.Action">
            <summary>
            Required for multi product message template.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.CouponCode">
            <summary>
            Required for coupon template. The coupon code to be copied when the customer taps the button.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterObject.LimitedTimeOffer">
            <summary>
            Required for limited time offer template. The limited time offer object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.Status">
            <summary>
            Required. Only supported value in order_details message is 'pending'.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.CatalogId">
            <summary>
            Optional. Unique identifier of the Facebook catalog being used by the business.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.Expiration">
            <summary>
            Optional. Expiration for the order. Business must define the following fields inside this object:
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.Items">
            <summary>
            Required. An object with the list of items for this order, containing the following fields:
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.Subtotal">
            <summary>
            Required. The value must be equal to sum of item.amount.value * item.amount.quantity for all items in the order.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.Shipping">
            <summary>
            Optional. The shipping cost of the order. The object contains the following fields:
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.Discount">
            <summary>
             Optional. The discount for the order. The object contains the following fields:
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderObject.Tax">
            <summary>
            Required. The tax information for this order which contains the following fields:
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderAmountObject.Value">
            <summary>
            Required. Positive integer representing the amount value multiplied by offset. For example, S$12.34 has value 1234
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderAmountObject.Offset">
            <summary>
            Required. Must be 100 for SGD
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderAmountObject.Description">
            <summary>
            Optional. Max character limit is 60 characters
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderAmountObject.DiscountProgramName">
            <summary>
            Optional. Text used for defining incentivised orders. If order is incentivised, the merchant needs to define this information. Max character limit is 60 characters
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderExpirationObject.Timestamp">
            <summary>
            UTC timestamp in seconds of time when order should expire. Minimum threshold is 300 seconds.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderExpirationObject.Description">
            <summary>
            Text explanation for expiration. Max character limit is 120 characters
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderItemObject.RetailerId">
            <summary>
            Required. Unique identifier of the Facebook catalog being used by the business
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderItemObject.Name">
            <summary>
            Required. The item’s name to be displayed to the user. Cannot exceed 60 characters
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderItemObject.Amount">
            <summary>
            Required. The price per item
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderItemObject.Quantity">
            <summary>
            Required. The number of items in this order
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterOrderItemObject.SaleAmount">
            <summary>
            Optional. The discounted price per item. This should be less than the original amount. If included, this field is used to calculate the subtotal amount
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterActionObject.Sections">
            <summary>
            Array of SectionObject. There is a minimum of 1 and maximum of 10. See section object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MessageObjects.WhatsappCloudApiParameterActionObject.FlowToken">
            <summary>
            Flow token that is generated by the business to serve as an identifier.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappBackupPhoneNumberObject.Data">
            <summary>
            Required.
            The data value you get when you backup your on-premise deployment. This contains the account registration info and application settings.
            For more information, see Backup and Restore to backup your on-premise implementation. https://developers.facebook.com/docs/whatsapp/on-premises/reference/settings/backup-and-restore
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappBackupPhoneNumberObject.Password">
            <summary>
            Required.
            The password you used in the backup API of your On-Premises deployment.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigrateAccountObject.MessagingProduct">
            <summary>
            Required.
            Messaging service used for the request. For account migration, always use "whatsapp".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigrateAccountObject.Pin">
            <summary>
            Required.
            A 6-digit pin you have previously set up — See Set Two-Step Verification If you use the wrong pin, your on-premise deployment will be down and will be disconnected from the WhatsApp servers.
            If you haven't set up or you have disabled two-step verification, provide a random 6-digit pin in the request, and this will be used to enable two-step verification in the WhatsApp Business Cloud-Based API.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigrateAccountObject.Backup">
            <summary>
            Required
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigratePhoneNumberObject.CountryCode">
            <summary>
            Required.
            Numerical country code for the phone number being registered.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigratePhoneNumberObject.PhoneNumber">
            <summary>
            Required.
            Phone number being registered, without the country code or plus symbol (+).
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberMigratePhoneNumberObject.MigratePhoneNumber">
            <summary>
            Required.
            To migrate a phone number, set this to true.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberRequestCodeObject.CodeMethod">
            <summary>
            Required. Method of receiving the registration code. Supported values: SMS and VOICE.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberRequestCodeObject.Language">
            <summary>
            Required. Language in which you want to receive the registration code.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberTwoStepVerificationObject.Pin">
            <summary>
            Required. A 6-digit pin you wish to use for two-step authentication.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.MigrationObjects.WhatsappPhoneNumberVerifyCodeObject.Code">
            <summary>
            Required.
            6-digit registration code received after making the method VerifyPhoneOwnershipRequestAsync call
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.RegistrationObjects.WhatsappCloudApiRegisterPhoneNumberObject.MessagingProduct">
            <summary>
            Required. Messaging service used. In this case, use "whatsapp".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.RegistrationObjects.WhatsappCloudApiRegisterPhoneNumberObject.Pin">
            <summary>
            Required. A 6-digit pin you have previously set up
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiErrorObject.Code">
            <summary>
            Error code.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiErrorObject.Title">
            <summary>
            Error title.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiErrorObject.Details">
            <summary>
            Optional. Error details provided, if available/applicable.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiErrorObject.Href">
            <summary>
            Optional. Location for error detail.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiConversationObject.Id">
            <summary>
            Represents the ID of the conversation the given status notification belongs to.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiConversationObject.ExpirationTimestamp">
            <summary>
            Date when the conversation expires
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiConversationObject.BizOpaqueCallbackData">
            <summary>
            Arbitrary string included in sent message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiConversationObject.Origin">
            <summary>
            Describes where the conversation originated from. See origin object for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiMessageIdentityObject.Acknowledged">
            <summary>
            State of acknowledgment for latest user_identity_changed system notification.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiMessageIdentityObject.CreatedTimestamp">
            <summary>
            Timestamp of when the WhatsApp Business API client detected the user potentially changed.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiMessageIdentityObject.Hash">
            <summary>
            Identifier for the latest user_identity_changed system notification.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiOriginObject.Type">
            <summary>
            Indicates where a conversation has started. This can also be referred to as a conversation entry point. Currently, the available options are:
            business_initiated: indicates that the conversation started by a business sending the first message to a user. This applies any time it has been more than 24 hours since the last user message.
            user_initiated: indicates that the conversation started by a business replying to a user message. This applies only when the business reply is within 24 hours of the last user message.
            referral_conversion: indicates that the conversation originated from a free entry point. These conversations are always user-initiated.
            We may add more granularity on entry point point type in the origin object in future.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiPaymentObject.ReferenceId">
            <summary>
            Unique reference ID for the order sent in order_details message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiPricingObject.PricingModel">
            <summary>
            Type of pricing model being used. Current supported values are:
            "CBP" (conversation-based pricing): See Conversation-Based Pricing for rates based on recipient country.
            "NBP" (notification-based pricing): Notifications are also known as Template Messages (more details on pricing here). This pricing model will be deprecated starting February 1st, 2022.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiPricingObject.Billable">
            <summary>
            Indicates if the given message or conversation is billable. Value varies according to pricing_model.
            - This flag is set to false if the conversation was initiated from free entry points. Conversations initiated from free entry points are not billable.
            - For all other conversations, it’s set to true.
            - This is also set to true for conversations inside your free tier limit. You will not be charged for these conversations, but they are considered billable and will be reflected on your invoice.
            If you are using NBP (notification-based pricing):
            - This flag is false for user-initiated conversations.
            - This flag is set to true for notification messages (template messages)
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiPricingObject.Category">
            <summary>
            Indicates the conversation pricing category. Currently, available options are:
            - business_initiated: indicates that the conversation started by a business sending the first message to a user. This applies any time it has been more than 24 hours since the last user message.
            - user_initiated: indicates that the conversation started by a business replying to a user message. This applies only when the business reply is within 24 hours of the last user message.
            - referral_conversion: indicates that the conversation originated from a free entry point. These conversations are always user-initiated.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookContactObject.Profile">
            <summary>
            This object contains the sender's profile information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookContactObject.WaId">
            <summary>
            The WhatsApp ID of the contact.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookInteractiveReplyObject.Type">
            <summary>
            button_reply, list_reply, npm_reply
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookInteractiveReplyObject.ButtonReply">
            <summary>
            Used to notify you when customers are replying to Quick Reply Messages.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookInteractiveReplyObject.ListReply">
            <summary>
            Used to notify you when customers are replying to List Messages.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookInteractiveReplyObject.NfmReply">
            <summary>
            Used to notify you when customers are replying to Flow Messages.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookLocationObject.Latitude">
            <summary>
            Latitude of location being sent.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookLocationObject.Longitude">
            <summary>
            Longitude of location being sent.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookLocationObject.Name">
            <summary>
            Address of the location.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookLocationObject.Address">
            <summary>
            Address of the location.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookLocationObject.Url">
            <summary>
            URL for the website where the user downloaded the location information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.Id">
            <summary>
            Required when type is audio, document, image, sticker, or video and you are not using a link.
            The media object ID. Do not use this field when message type is set to text.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.Caption">
            <summary>
            Optional.
            Describes the specified document, image, or video media.
            Do not use with audio or sticker media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.Filename">
            <summary>
            Optional.
            Describes the filename for the specific document. Use only with document media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.Metadata">
            <summary>
            Metadata pertaining to sticker media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.MimeType">
            <summary>
            Mime type of the media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.Sha256">
            <summary>
            Checksum.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.Animated">
            <summary>
            for sticker
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMediaObject.Voice">
            <summary>
            for audio
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageDetectedOutcome.Id">
            <summary>
            Message ID
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageDetectedOutcome.EventName">
            <summary>
            Event name
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageDetectedOutcome.Timestamp">
            <summary>
            Timestamp of the event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageDetectedOutcome.CtwaClid">
            <summary>
            Click ID for Click to WhatsApp Ads
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageDetectedOutcome.CustomData">
            <summary>
            Custom data for the event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookCustomData.Currency">
            <summary>
            Currency code
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookCustomData.Value">
            <summary>
            Value amount
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Context">
            <summary>
            Optional.
            This object will only be included when someone replies to one of your messages.
            Contains information about the content of the original message, such as the ID of the sender and the ID of the message.
            See context object for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.From">
            <summary>
            WhatsApp ID of the sender.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Id">
            <summary>
            Message ID.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Identity">
            <summary>
            Optional.
            Contains information about the user identity used to decrypt the incoming message. See identity object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Timestamp">
            <summary>
            Message received timestamp.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Type">
            <summary>
            Message type.
            Supported values are: unknown, text, audio, video, contacts, document, image, location, sticker, voice, interactive, button, order, system, ephemeral
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Text">
            <summary>
            When the notification describes a text message, the text object provides the body of the text message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Audio">
            <summary>
            When a message with audio is received, the WhatsApp Business API client will download the media. Once the media is downloaded, a notification is sent to your Webhook. This message contains information that identifies the media object and enables you to find and download the object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Document">
            <summary>
            When a message with document is received, the WhatsApp Business API client will download the media. Once the media is downloaded, a notification is sent to your Webhook. This message contains information that identifies the media object and enables you to find and download the object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Image">
            <summary>
            When a message with image is received, the WhatsApp Business API client will download the media. Once the media is downloaded, a notification is sent to your Webhook. This message contains information that identifies the media object and enables you to find and download the object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Video">
            <summary>
            When a message with media video is received, the WhatsApp Business API client will download the media. Once the media is downloaded, a notification is sent to your Webhook. This message contains information that identifies the media object and enables you to find and download the object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Voice">
            <summary>
            When a message with media voice is received, the WhatsApp Business API client will download the media. Once the media is downloaded, a notification is sent to your Webhook. This message contains information that identifies the media object and enables you to find and download the object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Sticker">
            <summary>
            When a message with media sticker is received, the WhatsApp Business API client will download the media. Once the media is downloaded, a notification is sent to your Webhook. This message contains information that identifies the media object and enables you to find and download the object.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Location">
            <summary>
            When you receive a notification of a user's static location, the location object provides the details of the location.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Contacts">
            <summary>
            When you receive a notification of a user's contact information, the contacts object provides the contact information details.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Reaction">
            <summary>
            When you receive a notification of a user's static reaction, the reaction object provides the details of the location.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.InteractiveReply">
            <summary>
            An interactive object is found in webhooks related to interactive messages, including List Messages and Reply Button.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.System">
            <summary>
            Notifications of changes to the system.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Order">
            <summary>
            An order object is found in webhooks related to Multi and Single Product Messages.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageObject.Referral">
            <summary>
            This object is included in notifications when a user clicks on an ad that clicks to WhatsApp and sends a message to the business. 
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Id">
            <summary>
            Message ID
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.RecipientId">
            <summary>
            WhatsApp ID of recipient
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Status">
            <summary>
            Status of a message.
            Values: read, delivered, sent, failed, deleted, warming
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Timestamp">
            <summary>
            Timestamp of the status message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Type">
            <summary>
            The type of entity this status object is about. Currently, the only available option is "message" and "payment".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.BizOpaqueCallbackData">
            <summary>
            Arbitrary string included in sent message.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Errors">
            <summary>
            When there are any out-of-band errors that occur in the normal operation of the application, the errors array provides a description of the error.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Conversation">
            <summary>
            Object containing conversation attributes, including id. See conversation object for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Pricing">
            <summary>
            Object containing billing attributes, including pricing_model, billable flag, and category. See pricing object for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookMessageStatus.Payment">
            <summary>
            Object containing billing attributes, including pricing_model, billable flag, and category. See pricing object for more information.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookNfmReplyObject.Name">
            <summary>
            Value is always "flow"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookNfmReplyObject.Body">
            <summary>
            Value is always "Sent"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookNfmReplyObject.ResponseJson">
            <summary>
            Flow-specific data.
            The structure is either defined in flow JSON (see Complete action, https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson#complete-action)
            or if flow is using an endpoint, controlled by endpoint (see Final Response Payload in Data Exchange Request, https://developers.facebook.com/docs/whatsapp/flows/guides/implementingyourflowendpoint#data_exchange_request)
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.EntityType">
            <summary>
            account_alerts event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.Decision">
            <summary>
            account_review_update event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.Event">
            <summary>
            account_update event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.MaxDailyConversationPerPhone">
            <summary>
            business_capability_update event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.BusinessId">
            <summary>
            business_status_update event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.CampaignId">
            <summary>
            campaign_status_update event
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.Message">
            <summary>
            Flows Event https://developers.facebook.com/docs/whatsapp/flows/reference/qualmgmtwebhook#value-object
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.MessageTemplateId">
            <summary>
            template_category_update event https://developers.facebook.com/docs/whatsapp/business-management-api/webhooks/components/#template-category-update
            message_template_quality_update event https://developers.facebook.com/docs/whatsapp/business-management-api/webhooks/components/#message-template-quality-update
            message_template_status_update https://developers.facebook.com/docs/whatsapp/business-management-api/webhooks/components/#message-template-updates
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.DisplayPhoneNumber">
            <summary>
            phone_number_name_update, phone_number_quality_update event https://developers.facebook.com/docs/whatsapp/business-management-api/webhooks/components/#phone-number-updates
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.CloudApiWebhookValueObject.Requester">
            <summary>
            security event https://developers.facebook.com/docs/graph-api/webhooks/reference/whatsapp_business_account/#security
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookOrderObject.CatalogId">
            <summary>
            ID of the catalog that contains the products listed under product_items sections.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookOrderObject.ProductItems">
            <summary>
            Array of product items.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookOrderObject.Text">
            <summary>
            Text message sent along with the order.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookProductItemsObject.ProductRetailerId">
            <summary>
            Unique identifier (in the catalog) of the product.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookProductItemsObject.Quantity">
            <summary>
            Number of items purchased.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookProductItemsObject.ItemPrice">
            <summary>
            Unitary price of the items.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookProductItemsObject.Currency">
            <summary>
            Currency of the price.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.SourceUrl">
            <summary>
            The Meta URL that leads to the ad or post clicked by the customer. Opening this url takes you to the ad viewed by your customer.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.SourceType">
            <summary>
            The type of the ad’s source; ad or post.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.SourceId">
            <summary>
            Meta ID for an ad or a post.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.Headline">
            <summary>
            Headline used in the ad or post.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.Body">
            <summary>
            Body for the ad or post.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.MediaType">
            <summary>
            Media present in the ad or post; image or video.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.ImageUrl">
            <summary>
            URL of the image, when media_type is an image.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.VideoUrl">
            <summary>
            URL of the video, when media_type is a video.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.ThumbnailUrl">
            <summary>
            URL for the thumbnail, when media_type is a video.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookReferralObject.CtwaClid">
            <summary>
            Click ID generated by Meta for ads that click to WhatsApp.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WebhookObjects.WhatsappCloudApiWebhookTextObject.Body">
            <summary>
            Message text
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.WhatsappFlowDataExchangeRequestObject.Action">
            <summary>
            Defines the type of the request. For data exchange request there are multiple choices depending on when the trigger
            INIT if the request is triggered when opening the Flow
            BACK if the request is triggered when pressing "back"
            data_exchange if the request is triggered when submitting the screen
            WhatsappFlowDataExchangeRequestActionConst class contains the possible values
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.WhatsappFlowDataExchangeRequestObject.Screen">
            <summary>
            If action is set to INIT or BACK, this field may not be populated.
            (Note: "SUCCESS" is a reserved name and cannot be used by any screens.)
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.WhatsappFlowDataExchangeRequestObject.Version">
            <summary>
            It must be 3.0 now
            https://developers.facebook.com/docs/whatsapp/flows/guides/implementingyourflowendpoint#data_exchange_request
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.WhatsappFlowDataExchangeRequestObject.Data">
            <summary>
            Data associated with the screen. This allow to display the example of the data of the input
            </summary>    
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.WhatsappFlowDataExchangeRequestObject.FlowToken">
            <summary>
            A Flow token generated and sent by you as part of the Flow message.
            The flow token is similar to a session identifier commonly used in web applications.
            It should be generated using established best practices (e.g. it should not be predictable) to ensure the security of data exchanges with an endpoint.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.WhatsappFlowDataExchangeBaseResponseObject.Screen">
            <summary>
            The screen to be rendered once the data exchange is complete
            </summary>
            <returns></returns>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.WhatsappFlowDataExchangeBaseResponseObject.Data">
            <summary>
            A JSON of properties and its values to render the screen after data exchange is complete.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.NextScreenData.ErrorMessage">
            <summary>
            If a bad request was sent from the WhatsApp client to you, the error message will be defined here.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.Params.FlowToken">
            <summary>
            Cannot be null for triggering flow completion.
            Generated by a business signifying a session or a user flow, when sending the flow message to the user.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Models.WhatsappFlowObjects.WhatsappFlowDataExchangeObjects.ErrorNotificationData.Acknowledged">
            <summary>
             Default true to indicate that error notification was acknowledged
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetMediaUrlResponse.Url">
            <summary>
            The URL is only valid for 5 minutes.
            </summary>
        </member>
        <member name="T:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse">
            <summary>
            WhatsApp Business Account To Number Current Status
            https://developers.facebook.com/docs/graph-api/reference/whats-app-business-account-to-number-current-status/
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.NewCertificate">
            <summary>
            Certificate of the new name that was requested
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.NameStatus">
            <summary>
            The status of the name review
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.NewNameStatus">
            <summary>
            The status of the review of the new name requested
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.AccountMode">
            <summary>
            The account mode of the phone number
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.Certificate">
            <summary>
            Certificate of the phone number
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.CodeVerificationStatus">
            <summary>
            Indicates the phone number's one-time password (OTP) verification status. Values can be NOT_VERIFIED or VERIFIED. Only phone numbers with a VERIFIED status can be registered.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.DisplayPhoneNumber">
            <summary>
            International format representation of the phone number
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.VerifiedName">
            <summary>
            The name registered in this phone's business profile that will be displayed to customers.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.IsPinEnabled">
            <summary>
            Returns True if a pin for two-step verification is enabled.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.IsOfficialBusinessAccount">
            <summary>
            Determines if the Business Profile connected to this Number is an Official Business Account.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.MessagingLimitTier">
            <summary>
            Current messaging limit tier enum{TIER_50, TIER_250, TIER_1K, TIER_10K, TIER_100K, TIER_UNLIMITED}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.QualityScore">
            <summary>
            Quality score of the phone
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.Status">
            <summary>
            The operating status of the phone number (eg. connected, rate limited, warned)
            enum {PENDING, DELETED, MIGRATED, BANNED, RESTRICTED, RATE_LIMITED, FLAGGED, CONNECTED, DISCONNECTED, UNKNOWN, UNVERIFIED}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.WhatsappCommerceSettings">
            <summary>
            whatsapp_business_management permission required
            System User Access Token is okay to retrieve
            Get a business phone number's WhatsApp Commerce Settings. Returns empty if commerce settings have not been set.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.SearchVisibility">
            <summary>
            The availability of the phone_number in the WhatsApp Business search.
            enum {NON_VISIBLE, PENDING_VISIBLE, VISIBLE}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetPhoneNumberDetailResponse.Throughput">
            <summary>
            The business phone number's Cloud API throughput level. Values can be STANDARD, HIGH, or NOT_APPLICABLE.
            If NOT_APPLICABLE, the number is not registered with Cloud API. 
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.WhatsappBusinessEncryptionObject.BusinessPublicKey">
            <summary>
            Stored 2048-bit RSA business public key.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.WhatsappBusinessEncryptionObject.BusinessPublicKeySignatureStatus">
            <summary>
            Status of stored 2048-bit RSA business public key.
            VALID | MISMATCH
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InitiatePhoneNumberMigrationResponse.Id">
            <summary>
            PHONE_NUMBER_ID
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaCommentDetailsResponse.ParentId">
            <summary>
            ID of the parent IG Comment if this comment was created on another IG Comment (i.e. a reply to another comment.)
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaCommentDetailsResponse.Timestamp">
            <summary>
            ISO 8601 formatted timestamp indicating when IG Comment was created. Example: 2017-05-19T23:27:28+0000.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaCommentDetailsResponse.User">
            <summary>
            ID of IG User who created the IG Comment. Only returned if the app user created the IG Comment, otherwise username will be returned instead.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaCommentDetailsResponse.Username">
            <summary>
            Username of Instagram user who created the IG Comment.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.InstagramPageMediaCommentMedia.MediaProductType">
            <summary>
            Surface where the media is published. Can be AD, FEED, STORY or REELS.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.Id">
            <summary>
            media id
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.IgId">
            <summary>
            legacy media id
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.IsSharedToFeed">
            <summary>
            For Reels only.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.LikeCount">
            <summary>
            v10.0 and older calls: The value is 0 if the media owner has hidden like counts. v11.0+ calls: The like_count field is omitted if the media owner has hidden like counts
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.MediaProductType">
            <summary>
            AD, FEED, STORY or REELS.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.MediaType">
            <summary>
            CAROUSEL_ALBUM, IMAGE, or VIDEO.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.MediaUrl">
            <summary>
            omitted from responses if the media contains copyrighted material or has been flagged for a copyright violation. 
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.InstagramPage.GetInstagramPageMediaDetailsResponse.ThumbnailUrl">
            <summary>
            Only available on VIDEO media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.ConversationalComponent.ConversationalAutomation.EnableWelcomeMessage">
            <summary>
            A boolean for enabling or disabling a welcome message on the phone number.
            <c>Boolean (Optional)</c>
            </summary>
            <example><c>true</c></example>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.ConversationalComponent.ConversationalAutomation.Commands">
            <summary>
            A list of commands to be configured.
            <c>JSON (Optional)</c>
            </summary>
            <example></example>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.ConversationalComponent.ConversationalAutomation.Prompts">
            <summary>
            The prompt(s) to be configured.
            <c>List of String (Optional)</c>
            </summary>
            <example><c>["Book a flight","plan a vacation"]</c></example>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.ConversationalComponent.Command.CommandName">
            <summary>
            Name of the command.
            <c>string (Required)</c>
            </summary>
            <example>generate</example>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.ConversationalComponent.Command.CommandDescription">
            <summary>
            Description of the command.
            <c>string (Required)</c>
            </summary>
            <example>Create a new image</example>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.FacebookPagePost.Message">
            <summary>
            // The message written in the post
            </summary>
        </member>
        <member name="T:GraphApi.Client.Payloads.Models.FacebookPagePostPrivacyObject">
            <summary>
            https://developers.facebook.com/docs/graph-api/reference/privacy/
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.FacebookPagePostPropertyObject.Name">
            <summary>
            length
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.FacebookPagePostPropertyObject.Text">
            <summary>
            00:11
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.FacebookProfilePictureObject.Type">
            <summary>
            small, normal, album, large, square
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponent.Version">
            <summary>
            Version of the Flow
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponent.DataApiVersion">
            <summary>
            Represents the version to use during communication with the WhatsApp Flows Data Endpoint. Currently, it is 3.0.
            If flow uses the data-channel capability, the validation system will ask to provide this property.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponent.Screens">
            <summary>
            List of screens within the JSON payload.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponent.RoutingModel">
            <summary>
            Routing the screen navigation
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentScreen.Id">
            <summary>
            Unique identifier of the screen.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentScreen.Title">
            <summary>
            Title of the screen.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentScreen.Data">
            <summary>
            Data associated with the screen. This allow to display the example of the data of the input
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentScreen.Layout">
            <summary>
            Layout configuration of the screen.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentScreen.Terminal">
            <summary>
            Indicate of this layout is end of the screen
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentScreen.Success">
            <summary>
            
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayout.Type">
            <summary>
            Type of layout for the screen. Refer to FlowComponentLayoutType constant
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayout.Children">
            <summary>
            List of child elements within the layout.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Type">
            <summary>
            Type of UI component for the layout element
            Example: Refer to FlowComponentLayoutChildrenType constant
            Required: Yes
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Text">
            <summary>
            Text content associated with the layout element.
            Required: Yes if Type: "TextHeading", "TextSubheading", "TextBody", "TextCaption"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.FontWeight">
            <summary>
            Font weight of the text
            Example: "bold", "italic", "bold_italic", "normal"
            Value: FirionComponentFontWeight const
            Required: No
            Only for Type: "TextBody", "TextCaption", If include it in other type will cause error
            </summary>~
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Strikethrough">
            <summary>
            Text content associated with the layout element.
            Required: No
            Only for Type: "TextBody", "TextCaption", If include it in other type will cause error
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Visible">
            <summary>
            Set the child element visibility
            Required: No
            Default: True
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Label">
            <summary>
            Label text for input elements.
            Required: Yes, for Type: "RadioButtonsGroup" -> Before 4.0: optional, after 4.0: required
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Name">
            <summary>
            Name or identifier for the layout element.
            Required: Yes, except Type: "Footer", "Dropdown","EmbeddedLink", "Image", "If", "Switch"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.InputType">
            <summary>
            Input type
            Example: Refer to FlowComponentLayoutChildrenInputType constant
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Required">
            <summary>
            Boolean indicating if the element is required to be filled.
            Required: No
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MinCharacters">
            <summary>
            Minimum characters of the input
            Required: No
            For Type: "TextInput"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MaxCharacters">
            <summary>
            Maximum characters of the input
            Required: No
            Default: 80
            For Type: "TextInput"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Enabled">
            <summary>
            Boolean indicating if the element is required to be filled.
            Required: Yes, if Type: "TextArea", "CheckboxGroup", "RadioButtonsGroup", "Dropdown", "DatePicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.HelperText">
            <summary>
            Helper text of the input
            Required: No
            For Type: "TextInput"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MaxLength">
            <summary>
            Max length of the text area
            Required: No
            For Type: "TextArea"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.DataSource">
            <summary>
            List of options for input
            Required: Yes if Type: "CheckboxGroup", "RadioButtonsGroup", "Dropdown"
            Min: 1
            Max: 20
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.OnClickAction">
            <summary>
            Action that is executed on clicking "Read more".
            Example: Name = "navigate", "complete", "data_exchange"
            Required: Yes if Type: "Footer", "OptIn", "EmbeddedLink"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MinSelectedItems">
            <summary>
            Minimum selected items of the elements
            Required: No
            For Type: "TextArea"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MaxSelectedItems">
            <summary>
            Maximum selected items of the elements
            Required: No
            For Type: "TextArea"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.OnSelectAction">
            <summary>
            Only `data_exchange` is supported.
            Required: No
            For Type: "TextArea"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Description">
            <summary>
            Supported starting with Flow JSON version 4.0
            Required: No
            For Type: "CheckboxGroup", "RadioButtonsGroup"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.LeftCaption">
            <summary>
            Can set left-caption and right-caption or only center-caption, but not all 3 at once
            Required: No
            For Type: "Footer"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.CenterCaption">
            <summary>
            Can set left-caption and right-caption or only center-caption, but not all 3 at once
            Required: No
            For Type: "Footer"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.RightCaption">
            <summary>
            Can set left-caption and right-caption or only center-caption, but not all 3 at once
            Required: No
            For Type: "Footer"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MinDate">
            <summary>
            Minimum date. Refer to https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson/components#datepicker-guidelines 
            Required: No
            For Type: "DatePicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MaxDate">
            <summary>
            Maximum date. Refer to https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson/components#datepicker-guidelines 
            Required: No
            For Type: "DatePicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.UnavailableDates">
            <summary>
            Unavailable date for selection
            Required: No
            For Type: "DatePicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Width">
            <summary>
            Width of image
            Required: No
            For Type: "Image"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Height">
            <summary>
            Height of image
            Required: No
            For Type: "Image"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.ScaleType">
            <summary>
            Width of image. Refer to FlowComponentImageScaleType constant
            Required: No
            Default value: `contain`
            For Type: "Image"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.AspectRatio">
            <summary>
            Aspect ratio of image
            Required: No
            Default value: 1
            For Type: "Image"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.AltText">
            <summary>
            Alternative Text is for the accessibility feature, eg. Talkback and Voice over
            Required: No
            For Type: "Image"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Condition">
            <summary>
            Boolean expression, it allows both dynamic and static data. Check session below for more info.
            Refer to https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson/components#supported-operators for using the condition.
            Required: Yes if Type: "If"
            For Type: "If"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Then">
            <summary>
            The components that will be rendered when `condition` is `true`. Allowed components: "TextHeading",
            "TextSubheading", "TextBody", "TextCaption", "CheckboxGroup", "DatePicker", "Dropdown", "EmbeddedLink", "Footer",
            "Image", "OptIn", "RadioButtonsGroup", "Switch", "TextArea", "TextInput" and "If"*.
            It is allowed to nest up to 3 "If" components.
            Required: Yes if Type: "If"
            For Type: "If"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Else">
            <summary>
            The components that will be rendered when `condition` is `false`. Allowed components: "TextHeading",
            "TextSubheading", "TextBody", "TextCaption", "CheckboxGroup", "DatePicker", "Dropdown",
            "EmbeddedLink", "Footer", "Image", "OptIn", "RadioButtonsGroup", "Switch",
            "TextArea", "TextInput" and "If"*. It is allowed to nest up to 3 "If" components.
            Required: No
            For Type: "If"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Value">
            <summary>
            A variable that will have its value evaluated during runtime. Example - `${data.animal}`
            Required: No
            For Type: "If"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Cases">
            <summary>
            Each property is a key (string) that maps to an Array of Components. When the `value` matches the key,
            it renders its array of components. Allowed components: "TextHeading", "TextSubheading", "TextBody",
            "TextCaption", "CheckboxGroup", "DatePicker", "Dropdown", "EmbeddedLink", "Footer", "Image", "OptIn",
            "RadioButtonsGroup", "Switch", "TextArea", "TextInput".
            Required: No
            For Type: "If"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.PhotoSource">
            <summary>
            Specifies the source where the image can be selected from. Refer to FlowComponentPhotoSource constant
            Required: No
            Default: 'camera_gallery'
            For Type: "PhotoPicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MaxFileSizeKb">
            <summary>
            Specifies the maximum file size (in kibibytes) that can be uploaded.
            Required: No
            For Type: "PhotoPicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MinUploadedPhotos">
            <summary>
            Specifies the minimum number of photos that are required.
            This property determines whether the component is optional (set to 0) or required (set above 0).
            Note: Above limits apply if media files are sent to the endpoint via "data_exchange" action.
            Only 1 image can be sent as part of the response message at the moment.
            Default: 0, Allowed range: [0, 30]
            Required: No
            For Type: "PhotoPicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.MaxUploadedPhotos">
            <summary>
            Specifies the maximum number of photos that can be uploaded.
            Note: Above limits apply if media files are sent to the endpoint via "data_exchange" action.
            Only 1 image can be sent as part of the response message at the moment.
            Default: 0, Allowed range: [1, 30]
            Required: No
            For Type: "PhotoPicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.ErrorMessage">
            <summary>
            Specifies errors when processing the documents.
            Default: 0, Allowed range: [1, 30]
            Required: No
            For Type: "PhotoPicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.AllowedMimeTypes">
            <summary>
            Specifies which document mime types can be selected.
            If it contains “image/jpeg”, picking photos from the gallery will be available as well.
            Refer to https://developers.facebook.com/docs/whatsapp/flows/reference/flowjson/components/media_upload#limitations-and-restrictions
            Default: 0, Allowed range: [1, 30]
            Required: No
            For Type: "DocumentPicker"
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.InitValue">
            <summary>
            Only available when component is outside Form component
            Optional Form: Supported starting with Flow JSON version 4.0
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildren.Children">
            <summary>
            possibly nested self children
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentOnAction.Name">
            <summary>
            Name of the action to perform on click.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentOnAction.Next">
            <summary>
            Next screen to navigate to after the click action.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentOnAction.Payload">
            <summary>
            Payload data to be sent with the click action.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentNextScreen.Type">
            <summary>
            Type of the next screen (e.g., "screen").
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentNextScreen.Name">
            <summary>
            Unique identifier or name of the next screen.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildrenDataSource.Id">
            <summary>
            Unique identifier of the radio button option.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowComponentLayoutChildrenDataSource.Title">
            <summary>
            Display title of the radio button option.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowWebPreview.PreviewUrl">
            <summary>
            Link for the preview page.
            This link does not require login and can be shared with stakeholders or embedded in an iframe,
            but the link will expire in 30 days, or if you call the API with invalidate=true which will generate a new link.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.Flows.FlowWebPreview.ExpiresAt">
            <summary>
            Time when the link will expire and the developer needs to call the API again to get a new link (30 days from link creation)
            </summary>
        </member>
        <member name="T:GraphApi.Client.Payloads.Models.InstagramPageMedia">
            <summary>
            https://developers.facebook.com/docs/instagram-api/reference/ig-media/#fields
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.Id">
            <summary>
            media id
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.IgId">
            <summary>
            legacy media id
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.IsSharedToFeed">
            <summary>
            For Reels only.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.LikeCount">
            <summary>
            v10.0 and older calls: The value is 0 if the media owner has hidden like counts. v11.0+ calls: The like_count field is omitted if the media owner has hidden like counts
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.MediaProductType">
            <summary>
            AD, FEED, STORY or REELS.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.MediaType">
            <summary>
            CAROUSEL_ALBUM, IMAGE, or VIDEO.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.MediaUrl">
            <summary>
            omitted from responses if the media contains copyrighted material or has been flagged for a copyright violation. 
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.InstagramPageMedia.ThumbnailUrl">
            <summary>
            Only available on VIDEO media.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.Id">
            <summary>
            ID
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.Name">
            <summary>
            The message template name
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.Components">
            <summary>
            An array of JSON objects describing the message template components.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.Category">
            <summary>
            The category type of the message template
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.Status">
            <summary>
            The status of the message template
            enum {APPROVED, IN_APPEAL, PENDING, REJECTED, PENDING_DELETION, DELETED, DISABLED, PAUSED, LIMIT_EXCEEDED}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.Language">
            <summary>
            The language (and locale) of the element translation
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.QualityScore">
            <summary>
            Quality score of the HSM
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.RejectedReason">
            <summary>
            The reason the message template was rejected
            enum {ABUSIVE_CONTENT, INVALID_FORMAT, NONE, PROMOTIONAL, TAG_CONTENT_MISMATCH, SCAM}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.MessageSendTtlSeconds">
            <summary>
            Time to live for message template sent. If users are offline for more than TTL duration after message template is sent, message will be dropped from message queue and will not be delivered.
            Only allowed for authentication message templates.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.SubCategory">
            <summary>
            Sub category of the template
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.PreviousCategory">
            <summary>
            Previous category of the template
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappCloudApiTemplate.CtaUrlLinkTrackingOptedOut">
            <summary>
            Optional boolean field for opting out/in of link tracking at template level
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.MessageTemplateQualityScore.Score">
            <summary>
            array GREEN, YELLOW, RED, UNKNOWN}>
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.MessageTemplateQualityScore.Date">
            <summary>
            Timestamp of when the score was calculated
            </summary>
        </member>
        <member name="T:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail">
            <summary>
            WhatsApp Business Account To Number Current Status
            https://developers.facebook.com/docs/graph-api/reference/whats-app-business-account-to-number-current-status/
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.NewCertificate">
            <summary>
            Certificate of the new name that was requested
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.NameStatus">
            <summary>
            The status of the name review
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.NewNameStatus">
            <summary>
            The status of the review of the new name requested
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.AccountMode">
            <summary>
            The account mode of the phone number
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.Certificate">
            <summary>
            Certificate of the phone number
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.CodeVerificationStatus">
            <summary>
            Indicates the phone number's one-time password (OTP) verification status. Values can be NOT_VERIFIED or VERIFIED. Only phone numbers with a VERIFIED status can be registered.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.DisplayPhoneNumber">
            <summary>
            International format representation of the phone number
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.VerifiedName">
            <summary>
            The name registered in this phone's business profile that will be displayed to customers.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.IsPinEnabled">
            <summary>
            Returns True if a pin for two-step verification is enabled.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.IsOfficialBusinessAccount">
            <summary>
            Determines if the Business Profile connected to this Number is an Official Business Account.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.MessagingLimitTier">
            <summary>
            Current messaging limit tier enum{TIER_50, TIER_250, TIER_1K, TIER_10K, TIER_100K, TIER_UNLIMITED}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.QualityScore">
            <summary>
            Quality score of the phone
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.Status">
            <summary>
            The operating status of the phone number (eg. connected, rate limited, warned)
            enum {PENDING, DELETED, MIGRATED, BANNED, RESTRICTED, RATE_LIMITED, FLAGGED, CONNECTED, DISCONNECTED, UNKNOWN, UNVERIFIED}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.WhatsappCommerceSettings">
            <summary>
            whatsapp_business_management permission required
            System User Access Token is okay to retrieve
            Get a business phone number's WhatsApp Commerce Settings. Returns empty if commerce settings have not been set.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.SearchVisibility">
            <summary>
            The availability of the phone_number in the WhatsApp Business search.
            enum {NON_VISIBLE, PENDING_VISIBLE, VISIBLE}
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberDetail.Throughput">
            <summary>
            The business phone number's Cloud API throughput level. Values can be STANDARD, HIGH, or NOT_APPLICABLE.
            If NOT_APPLICABLE, the number is not registered with Cloud API. 
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberQualityScore.Date">
            <summary>
            Timestamp of when the score was calculated
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberQualityScore.Reasons">
            <summary>
            Reasons for the quality score
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Models.WhatsappPhoneNumberQualityScore.Score">
            <summary>
            Quality score fo the phone
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.RegisterPhoneNumberRequest.MessagingProduct">
            <summary>
            Required. Messaging service used. In this case, use "whatsapp".
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.RegisterPhoneNumberRequest.Pin">
            <summary>
            Required. A 6-digit pin you have previously set up
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.SetPhoneNumberBusinessPublicKeyRequest.BusinessPublicKey">
            <summary>
            2048-bit RSA business public key generated.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.SetupTwoStepVerificationRequest.Pin">
            <summary>
            Required. A 6-digit pin you wish to use for two-step authentication.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.UploadMediaRequest.Type">
             <summary>
             Required. Type of media file being uploaded.
             Supported options for images are:
                 image/jpeg
                 image/png
             
             Supported options for documents are:
                 text/plain
                 application/pdf
                 application/vnd.ms-powerpoint
                 application/msword
                 application/vnd.ms-excel
                 application/vnd.openxmlformats-officedocument.wordprocessingml.document
                 application/vnd.openxmlformats-officedocument.presentationml.presentation
                 application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
            
             Supported options for audio are:
                 audio/aac
                 audio/mp4
                 audio/mpeg
                 audio/amr
                 audio/ogg
                 audio/opus
            
             Supported options for video are:
                 video/mp4
                 video/3gp
             
             Supported options for stickers are:
                 image/webp
             </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.VerifyPhoneOwnershipCodeRequest.Code">
            <summary>
            Required.
            6-digit registration code received after making the method VerifyPhoneOwnershipRequestAsync call
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.VerifyPhoneOwnershipCodeResponse.Success">
            <summary>
            If your API call returns success: true, the ownership of your phone number is verified.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.VerifyPhoneOwnershipRequestCodeRequest.CodeMethod">
            <summary>
            Required. Method of receiving the registration code. Supported values: SMS and VOICE.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.VerifyPhoneOwnershipRequestCodeRequest.Language">
            <summary>
            Required. Language in which you want to receive the registration code.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.VerifyPhoneOwnershipRequestCodeResponse.Success">
            <summary>
            If your API call returns success: true, you should get your code via the selected code_method to the phone number being migrated.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Whatsapp.ConversationalComponent.GetConversationalAutomationByPhoneNumberIdResponse.Id">
            <summary>
            The phone number id of the config
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Whatsapp.ConversationalComponent.GetConversationalAutomationByPhoneNumberIdResponse.ConversationalAutomation">
            <summary>
            The object
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Whatsapp.ConversationalComponent.ConversationalAutomationListResponse.Id">
            <summary>
            The phone number id of the config
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Whatsapp.ConversationalComponent.ConversationalAutomationListResponse.VerifiedName">
            <summary>
            The name registered in this phone's business profile that will be displayed to customers.
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Whatsapp.ConversationalComponent.ConversationalAutomationListResponse.DisplayPhoneNumber">
            <summary>
            International format representation of the phone number
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.Whatsapp.ConversationalComponent.ConversationalAutomationListResponse.ConversationalAutomation">
            <summary>
            The object
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.GetWhatsappBusinessAccountResponse.MarketingMessagesLiteApiStatus">
            <summary>
            The status of marketing messages lite API for this WhatsApp Business Account.
            Possible values:
            - UNKNOWN: Status cannot be determined
            - INELIGIBLE: Account is not eligible for marketing messages lite API
            - ELIGIBLE: Account is eligible but not yet onboarded
            - ONBOARDED: Account has successfully onboarded to marketing messages lite API
            - PENDING: Account status is pending determination
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.WhatsappFlows.CreateFlowResponse.FlowId">
            <summary>
            Flow Id
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.WhatsappFlows.GetFlowResponse.HealthStatus">
            <summary>
            Each of these nodes can have one of the following health statuses assigned to the can_send_message property:
            AVAILABLE: Indicates that the node meets all requirements.
            LIMITED: Indicates that the node meets requirements, but has some limitations. If a given node has this value, additional info will be included.
            BLOCKED: Indicates that the node does not meet one or more messaging requirements. If a given node has this value, the errors property will be included which describes the error and a possible solution.
            The Flow node will have the can_send_message property set to:
            LIMITED: If published Flow is in THROTTLED state.
            BLOCKED: If unpublished Flow has one of the the publishing checks failing. If published Flow is in BLOCKED or DEPRECATED state.
            </summary>
            <returns></returns>
        </member>
        <member name="P:GraphApi.Client.Payloads.WhatsappFlows.MessagingHealthStatus.CanSendMessage">
            <summary>
            Messaging Health Status
            The overall health status property (health_status.can_send_message) will be set as follows:
            If one or more nodes is blocked, it will be set to BLOCKED.
            If no nodes are blocked, but one or more nodes is limited, it will be set to LIMITED.
            If all nodes are available, it will be set to AVAILABLE.
            </summary>
        </member>
        <member name="T:GraphApi.Client.Payloads.WhatsappFlows.MessagingEntity">
            <summary>
            https://developers.facebook.com/docs/whatsapp/cloud-api/health-status/#messaging-health-status
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.WhatsappFlows.MessagingEntity.Errors">
            <summary>
            If a given node's can_send_message property is set to BLOCKED, the errors property will be included, which describes the reason for the status and a possible solution.
            https://developers.facebook.com/docs/whatsapp/cloud-api/health-status/#errors-property
            </summary>
        </member>
        <member name="P:GraphApi.Client.Payloads.WhatsappFlows.MessagingEntity.AdditionalInfo">
            <summary>
            If a given node's can_send_message property is set to LIMITED, the additional_info property will be included, which provides additional context for the limitation.
            https://developers.facebook.com/docs/whatsapp/cloud-api/health-status/#additional-info-property
            </summary>
        </member>
    </members>
</doc>
