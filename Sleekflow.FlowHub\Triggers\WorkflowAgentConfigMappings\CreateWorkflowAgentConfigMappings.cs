using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowAgentConfigMappings;
using Sleekflow.FlowHub.WorkflowAgentConfigMappings;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowAgentConfigMappings;

[TriggerGroup(ControllerNames.WorkflowAgentConfigMappings)]
public class CreateWorkflowAgentConfigMappings : ITrigger
{
    private readonly IWorkflowAgentConfigMappingService _workflowAgentConfigMappingService;

    public CreateWorkflowAgentConfigMappings(
        IWorkflowAgentConfigMappingService workflowAgentConfigMappingService)
    {
        _workflowAgentConfigMappingService = workflowAgentConfigMappingService;
    }

    public class CreateWorkflowAgentConfigMappingsInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }

        [Required]
        [JsonProperty("workflow_ids")]
        public List<string> WorkflowIds { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

    }

    public class CreateWorkflowAgentConfigMappingsOutput
    {
        [JsonProperty("workflow_agent_config_mappings")]
        public List<WorkflowAgentConfigMapping> WorkflowAgentConfigMappings { get; set; }

        [JsonConstructor]
        public CreateWorkflowAgentConfigMappingsOutput(
            List<WorkflowAgentConfigMapping> workflowAgentConfigMappings)
        {
            WorkflowAgentConfigMappings = workflowAgentConfigMappings;
        }
    }

    public async Task<CreateWorkflowAgentConfigMappingsOutput> F(CreateWorkflowAgentConfigMappingsInput createWorkflowAgentConfigMappingsInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
          createWorkflowAgentConfigMappingsInput.SleekflowStaffId,
          createWorkflowAgentConfigMappingsInput.SleekflowStaffTeamIds);

        var workflowAgentConfigMappings = await _workflowAgentConfigMappingService.CreateWorkflowAgentConfigMappingsAsync(
            createWorkflowAgentConfigMappingsInput.SleekflowCompanyId,
            createWorkflowAgentConfigMappingsInput.AgentConfigId,
            createWorkflowAgentConfigMappingsInput.WorkflowIds,
            sleekflowStaff);

        return new CreateWorkflowAgentConfigMappingsOutput(workflowAgentConfigMappings);
    }
}