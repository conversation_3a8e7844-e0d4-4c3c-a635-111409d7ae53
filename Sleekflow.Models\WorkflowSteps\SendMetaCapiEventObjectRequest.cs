using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class SendMetaCapiEventObjectRequest
{
    [JsonProperty("partner_agent")]
    public string PartnerAgent { get; set; } = "sleekflow";

    [JsonProperty("data")]
    public SendMetaCapiEventData[] Data { get; set; } = Array.Empty<SendMetaCapiEventData>();

    [JsonConstructor]
    public SendMetaCapiEventObjectRequest(SendMetaCapiEventData[] data)
    {
        Data = data ?? Array.Empty<SendMetaCapiEventData>();
    }
}

public class SendMetaCapiEventData
{
    [JsonProperty("event_name")]
    [Required]
    public string EventName { get; set; }

    [JsonProperty("event_time")]
    [Required]
    public string EventTime { get; set; }

    [JsonProperty("action_source")]
    [Required]
    public string ActionSource { get; set; }

    [JsonProperty("messaging_channel")]
    [Required]
    public string MessagingChannel { get; set; }

    [JsonProperty("user_data")]
    [Required]
    public SendMetaCapiEventUserData UserData { get; set; }

    [JsonProperty("custom_data")]
    [Required]
    public SendMetaCapiEventCustomData CustomData { get; set; }

    [JsonConstructor]
    public SendMetaCapiEventData(
        string eventName,
        string eventTime,
        string actionSource,
        string messagingChannel,
        SendMetaCapiEventUserData userData,
        SendMetaCapiEventCustomData customData)
    {
        EventName = eventName;
        EventTime = eventTime;
        ActionSource = actionSource;
        MessagingChannel = messagingChannel;
        UserData = userData ?? throw new ArgumentNullException(nameof(userData));
        CustomData = customData ?? throw new ArgumentNullException(nameof(customData));
    }
}

public class SendMetaCapiEventUserData
{
    [JsonProperty("whatsapp_business_account_id")]
    [Required]
    public string WhatsappBusinessAccountId { get; set; }

    [JsonProperty("ctwa_clid")]
    [Required]
    public string CtwaClid { get; set; }

    [JsonConstructor]
    public SendMetaCapiEventUserData(string whatsappBusinessAccountId, string ctwaClid)
    {
        WhatsappBusinessAccountId = whatsappBusinessAccountId ?? throw new ArgumentNullException(nameof(whatsappBusinessAccountId));
        CtwaClid = ctwaClid ?? throw new ArgumentNullException(nameof(ctwaClid));
    }
}

public class SendMetaCapiEventCustomData
{
    [JsonProperty("currency")]
    [Required]
    public string Currency { get; set; }

    [JsonProperty("value")]
    [Required]
    public string Value { get; set; }

    [JsonConstructor]
    public SendMetaCapiEventCustomData(string currency, string value)
    {
        Currency = currency ?? throw new ArgumentNullException(nameof(currency));
        Value = value ?? throw new ArgumentNullException(nameof(value));
    }
}