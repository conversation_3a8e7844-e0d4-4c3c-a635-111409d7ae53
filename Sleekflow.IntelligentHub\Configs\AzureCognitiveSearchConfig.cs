using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IAzureCognitiveSearchConfig
{
    string GetEndpoint(string suffix);

    string GetKey(string suffix);
}

public class AzureCognitiveSearchConfig : IConfig, IAzureCognitiveSearchConfig
{
    /// <summary>
    /// Azure Cognitive Search endpoint.
    /// </summary>
    private string Endpoint { get; }

    /// <summary>
    /// Key to access the Azure Cognitive Search.
    /// </summary>
    private string Key { get; }

    /// <summary>
    /// Azure Cognitive Search endpoint.
    /// </summary>
    private string Endpoint2 { get; }

    /// <summary>
    /// Key to access the Azure Cognitive Search.
    /// </summary>
    private string Key2 { get; }

    /// <summary>
    /// Azure Cognitive Search endpoint.
    /// </summary>
    private string Endpoint3 { get; }

    /// <summary>
    /// Key to access the Azure Cognitive Search.
    /// </summary>
    private string Key3 { get; }

    public string GetEndpoint(string suffix)
    {
        if (suffix == string.Empty)
        {
            return Endpoint;
        }

        if (suffix == "_2")
        {
            return Endpoint2;
        }

        if (suffix == "_3")
        {
            return Endpoint3;
        }

        throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ENDPOINT");
    }

    public string GetKey(string suffix)
    {
        if (suffix == string.Empty)
        {
            return Key;
        }

        if (suffix == "_2")
        {
            return Key2;
        }

        if (suffix == "_3")
        {
            return Key3;
        }

        throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ADMIN_KEY");
    }

    public AzureCognitiveSearchConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("AZURECOGNITIVESEARCH_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ENDPOINT");

        Endpoint2 =
            Environment.GetEnvironmentVariable("AZURECOGNITIVESEARCH_ENDPOINT_2", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ENDPOINT_2");

        Endpoint3 =
            Environment.GetEnvironmentVariable("AZURECOGNITIVESEARCH_ENDPOINT_3", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ENDPOINT_3");

        Key =
            Environment.GetEnvironmentVariable("AZURECOGNITIVESEARCH_ADMIN_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ADMIN_KEY");

        Key2 =
            Environment.GetEnvironmentVariable("AZURECOGNITIVESEARCH_ADMIN_KEY_2", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ADMIN_KEY_2");

        Key3 =
            Environment.GetEnvironmentVariable("AZURECOGNITIVESEARCH_ADMIN_KEY_3", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURECOGNITIVESEARCH_ADMIN_KEY_3");
    }
}