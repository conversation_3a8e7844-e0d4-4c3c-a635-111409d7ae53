﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;

namespace Sleekflow.Mvc.Validators;

public class NullObjectModelValidator : IObjectModelValidator
{
    public void Validate(
        ActionContext actionContext,
        ValidationStateDictionary? validationState,
        string prefix,
        object? model)
    {
        // https://github.com/dotnet/aspnetcore/issues/30938
        foreach (var entry in actionContext.ModelState.Values)
        {
            // or ModelValidationState.Valid
            entry.ValidationState = ModelValidationState.Skipped;
        }
    }
}