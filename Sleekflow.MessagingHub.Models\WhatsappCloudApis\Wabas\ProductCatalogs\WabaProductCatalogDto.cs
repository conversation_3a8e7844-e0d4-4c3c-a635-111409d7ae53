using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;

public class WabaProductCatalogDto
{
    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string? Id { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string? SleekflowCompanyId { get; set; }

    [JsonProperty("facebook_product_catalog_id")]
    public string FacebookProductCatalogId { get; set; }

    [JsonProperty("facebook_product_catalog_name")]
    public string FacebookProductCatalogName { get; set; }

    [JsonProperty("default_image_url")]
    public string? DefaultImageUrl { get; set; }

    [JsonProperty("product_count")]
    public int? ProductCount { get; set; }

    [JsonProperty("vertical")] // the type of catalog (for example: hotels, commerce, etc)
    public string? Vertical { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; } // active || inactive

    [JsonProperty("created_at")]
    public DateTimeOffset? CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTimeOffset? UpdatedAt { get; set; }

    [JsonConstructor]
    public WabaProductCatalogDto(string? id, string? sleekflowCompanyId, string facebookProductCatalogId, string facebookProductCatalogName, string? defaultImageUrl, int? productCount, string? vertical, string? status, DateTimeOffset? createdAt, DateTimeOffset? updatedAt)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        FacebookProductCatalogId = facebookProductCatalogId;
        FacebookProductCatalogName = facebookProductCatalogName;
        DefaultImageUrl = defaultImageUrl;
        ProductCount = productCount;
        Vertical = vertical;
        Status = status;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public WabaProductCatalogDto(WabaProductCatalog wabaProductCatalog)
        : this(
            wabaProductCatalog.Id,
            wabaProductCatalog.SleekflowCompanyId,
            wabaProductCatalog.FacebookProductCatalogId,
            wabaProductCatalog.FacebookProductCatalogName,
            wabaProductCatalog.DefaultImageUrl,
            wabaProductCatalog.ProductCount,
            wabaProductCatalog.Vertical,
            wabaProductCatalog.Status,
            wabaProductCatalog.CreatedAt,
            wabaProductCatalog.UpdatedAt
        )
    {
    }

    public WabaProductCatalogDto(FacebookProductCatalog facebookProductCatalog)
        : this (
        null,
        null,
        facebookProductCatalog.Id,
        facebookProductCatalog.Name,
        facebookProductCatalog.DefaultImageUrl,
        facebookProductCatalog.ProductCount,
        facebookProductCatalog.Vertical,
        null,
        null,
        null)
    {
    }
}