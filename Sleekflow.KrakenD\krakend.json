{
  "endpoints": [
    {
      "endpoint": "/v1/healthz",
      "backend": [
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "SALESFORCE_INTEGRATOR_HOST" }}"
          ],
          "encoding": "string",
          "group": "salesforce-integrator",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "HUBSPOT_INTEGRATOR_HOST" }}"
          ],
          "encoding": "string",
          "group": "hubspot-integrator",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "DYNAMICS365_INTEGRATOR_HOST" }}"
          ],
          "encoding": "string",
          "group": "dynamics365-integrator",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "GOOGLE_SHEETS_INTEGRATOR_HOST" }}"
          ],
          "encoding": "string",
          "group": "google-sheets-integrator",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "ZOHO_INTEGRATOR_HOST" }}"
          ],
          "encoding": "string",
          "group": "zoho-integrator",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "crm-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "EMAIL_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "email-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "messaging-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "commerce-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "WEBHOOK_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "webhook-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "SHARE_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "share-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "string",
          "group": "public-api-gateway",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "flow-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "FLOW_HUB_EXECUTOR_HOST" }}"
          ],
          "encoding": "string",
          "group": "flow-hub-executor",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "FLOW_HUB_INTEGRATOR_HOST" }}"
          ],
          "encoding": "string",
          "group": "flow-hub-integrator",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "tenant-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "intelligent-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "user-event-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "SUPPORT_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "support-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "ticketing-hub",
          "method": "GET"
        },
        {
          "url_pattern": "/healthz/liveness",
          "host": [
            "{{ env "INTERNAL_INTEGRATION_HUB_HOST" }}"
          ],
          "encoding": "string",
          "group": "internal-integration-hub",
          "method": "GET"
        }
      ],
      "extra_config": {
        "qos/ratelimit/router": {
          "client_max_rate": 5.0,
          "key": "X-Azure-ClientIP",
          "max_rate": 100.0,
          "strategy": "ip"
        }
      },
      "method": "GET"
    },
    {
      "endpoint": "/v1/commerce-hub/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub-executor/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "FLOW_HUB_EXECUTOR_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_EXECUTOR_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/webhook-hub/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "WEBHOOK_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "WEBHOOK_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/Public/e/{webhookTriggerId}/{validationToken}",
      "backend": [
        {
          "url_pattern": "/Public/e",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "pre": "local r = request.load(); r:headers('X-Sleekflow-Workflow-Webhook-Trigger-Id', r:params('WebhookTriggerId')); r:headers('X-Sleekflow-Workflow-Validation-Token', r:params('ValidationToken')); r:headers('Content-Type', 'application/json')"
        }
      },
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/flow-hub/Public/e",
      "backend": [
        {
          "url_pattern": "/Public/e",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "Content-Type",
        "X-Sleekflow-Workflow-Webhook-Trigger-Id",
        "X-Sleekflow-Workflow-Validation-Token"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/hubspot-integrator/HubspotCallback",
      "backend": [
        {
          "url_pattern": "/Public/HubspotCallback",
          "host": [
            " {{ env "HUBSPOT_INTEGRATOR_HOST" }} "
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "input_headers": [
        "X-HubSpot-Signature"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/salesforce-integrator/ApexCallback",
      "backend": [
        {
          "url_pattern": "/Public/ApexCallback",
          "host": [
            "{{ env "SALESFORCE_INTEGRATOR_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/WhatsappCloudApiWebhook",
      "backend": [
        {
          "url_pattern": "/WhatsappCloudApiWebhook",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/messaging-hub/WhatsappCloudApiWebhook",
      "backend": [
        {
          "url_pattern": "/WhatsappCloudApiWebhook",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_query_strings": [
        "hub.mode",
        "hub.challenge",
        "hub.verify_token"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/messaging-hub/MetaConversionApiSignalEvent/SendConversionApiSignalEvent",
      "backend": [
        {
          "url_pattern": "/MetaConversionApiSignalEvent/SendConversionApiSignalEvent",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "Content-Type",
        "X-Sleekflow-Facebook-Waba-Id"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/messaging-hub/StripeWebhook/{type}",
      "backend": [
        {
          "url_pattern": "/StripeWebhook/{type}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "Stripe-Signature"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/l/{str}",
      "backend": [
        {
          "url_pattern": "/Public/l/{str}",
          "host": [
            "{{ env "SHARE_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_headers": [
        "*"
      ],
      "input_query_strings": [
        "*"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/q/{str}",
      "backend": [
        {
          "url_pattern": "/Public/q/{str}",
          "host": [
            "{{ env "SHARE_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_headers": [
        "*"
      ],
      "input_query_strings": [
        "*"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/commerce-hub/StripeWebhook/{type}",
      "backend": [
        {
          "url_pattern": "/StripeWebhook/{type}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "Stripe-Signature"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/commerce-hub/StripeConnectOnboardingLinkRefresh",
      "backend": [
        {
          "url_pattern": "/StripeConnectOnboardingLinkRefresh",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/commerce-hub/CustomCatalogs/GetCsvTemplate",
      "backend": [
        {
          "url_pattern": "/CustomCatalogs/GetCsvTemplate",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/commerce-hub/CustomCatalogs/GetCsvTemplateSample",
      "backend": [
        {
          "url_pattern": "/CustomCatalogs/GetCsvTemplateSample",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/commerce-hub/VtexWebhook/order",
      "backend": [
        {
          "url_pattern": "/VtexWebhook/order",
          "host": [
            " {{ env "COMMERCE_HUB_HOST" }} "
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleeklow-VtexAuthenticationId",
        "X-Sleeklow-CompanyId",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/email-hub/gmail/auth/callback",
      "backend": [
        {
          "url_pattern": "/GmailAuthentication/GmailAuthCallback",
          "host": [
            " {{ env "EMAIL_HUB_HOST" }} "
          ],
          "encoding": "json",
          "method": "GET"
        }
      ],
      "method": "GET"
    },
    {
      "endpoint": "/v1/email-hub/outlook/auth/callback",
      "backend": [
        {
          "url_pattern": "/OutlookAuthentication/OutlookAuthCallback",
          "host": [
            " {{ env "EMAIL_HUB_HOST" }} "
          ],
          "encoding": "json",
          "method": "GET"
        }
      ],
      "method": "GET"
    },
    {
      "endpoint": "/v1/email-hub/gmail/email/receive",
      "backend": [
        {
          "url_pattern": "/GmailWebhook/NotifyOnGmailReceive",
          "host": [
            " {{ env "EMAIL_HUB_HOST" }} "
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/email-hub/disposable/email/receive",
      "backend": [
        {
          "url_pattern": "/DisposableWebhook/NotifyOnDisposableReceive",
          "host": [
            " {{ env "EMAIL_HUB_HOST" }} "
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/email-hub/outlook/subscription/callback",
      "backend": [
        {
          "url_pattern": "/OutlookSubscription/OutlookSubscriptionCallBack",
          "host": [
            " {{ env "EMAIL_HUB_HOST" }} "
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/ReliableMessage/negotiate",
      "backend": [
        {
          "url_pattern": "/ReliableMessage/negotiate",
          "host": [
            " {{ env "USER_EVENT_HUB_HOST" }} "
          ],
          "encoding": "no-op",
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        },
        "qos/ratelimit/router": {
          "capacity": 1000,
          "client_capacity": 2,
          "client_max_rate": 1.0,
          "every": "10s",
          "key": "Authorization",
          "max_rate": 0.0,
          "strategy": "header"
        }
      },
      "input_headers": [
        "Content-Type",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy"
      ],
      "input_query_strings": [
        "data"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/user-event-hub/SignalRWebhook",
      "backend": [
        {
          "url_pattern": "/SignalRWebhook",
          "host": [
            " {{ env "USER_EVENT_HUB_HOST" }} "
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "*"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/tenant-hub/Geolocations/{method}",
      "backend": [
        {
          "url_pattern": "Geolocations/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Webhooks/Auth0/{method}",
      "backend": [
        {
          "url_pattern": "Webhooks/Auth0/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/invite/{method}",
      "backend": [
        {
          "url_pattern": "invite/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Register/Companies/{method}",
      "backend": [
        {
          "url_pattern": "Register/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-Email",
        "X-Sleekflow-Connection-Strategy"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/webhook-hub/FacebookWebhook/Webhooks",
      "backend": [
        {
          "url_pattern": "/FacebookWebhook/Webhooks",
          "host": [
            "{{ env "WEBHOOK_HUB_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub-integrator/Zapier/Triggers/{triggerName}",
      "backend": [
        {
          "url_pattern": "/Public/Zapier/Triggers/{triggerName}",
          "host": [
            " {{ env "FLOW_HUB_INTEGRATOR_HOST" }} "
          ],
          "encoding": "no-op",
          "method": "POST"
        }
      ],
      "input_headers": [
        "Content-Type",
        "X-Sleekflow-Zapier-Api-Key",
        "X-Sleekflow-Location"
      ],
      "method": "POST",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/flow-hub-integrator/Zapier/Triggers/{triggerName}",
      "backend": [
        {
          "url_pattern": "/Public/Zapier/Triggers/{triggerName}",
          "host": [
            " {{ env "FLOW_HUB_INTEGRATOR_HOST" }} "
          ],
          "encoding": "no-op",
          "method": "DELETE"
        }
      ],
      "input_headers": [
        "X-Sleekflow-Zapier-Api-Key",
        "X-Sleekflow-Location"
      ],
      "input_query_strings": [
        "zap_id"
      ],
      "method": "DELETE",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/flow-hub-integrator/Zapier/Triggers/{triggerName}",
      "backend": [
        {
          "url_pattern": "/Public/Zapier/Triggers/{triggerName}",
          "host": [
            " {{ env "FLOW_HUB_INTEGRATOR_HOST" }} "
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_headers": [
        "X-Sleekflow-Zapier-Api-Key",
        "X-Sleekflow-Location"
      ],
      "input_query_strings": [
        "schema_unique_name"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/internal-integration-hub/NetSuite/External/{triggerName}",
      "backend": [
        {
          "url_pattern": "/NetSuite/External/{triggerName}",
          "host": [
            "{{ env "INTERNAL_INTEGRATION_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTERNAL_INTEGRATION_HUB_NETSUITE_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/salesforce-integrator/AuthenticateCallback",
      "backend": [
        {
          "url_pattern": "/Public/AuthenticateCallback",
          "host": [
            "{{ env "SALESFORCE_INTEGRATOR_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_query_strings": [
        "code",
        "state"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/salesforce-integrator/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "SALESFORCE_INTEGRATOR_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "SALESFORCE_INTEGRATOR_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/hubspot-integrator/AuthenticateCallback",
      "backend": [
        {
          "url_pattern": "/Public/AuthenticateCallback",
          "host": [
            "{{ env "HUBSPOT_INTEGRATOR_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_query_strings": [
        "code",
        "state"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/hubspot-integrator/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "HUBSPOT_INTEGRATOR_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "HUBSPOT_INTEGRATOR_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/dynamics365-integrator/AuthenticateCallback",
      "backend": [
        {
          "url_pattern": "/Public/AuthenticateCallback",
          "host": [
            "{{ env "DYNAMICS365_INTEGRATOR_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_query_strings": [
        "code",
        "state"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/dynamics365-integrator/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "DYNAMICS365_INTEGRATOR_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "DYNAMICS365_INTEGRATOR_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/google-sheets-integrator/AuthenticateCallback",
      "backend": [
        {
          "url_pattern": "/Public/AuthenticateCallback",
          "host": [
            "{{ env "GOOGLE_SHEETS_INTEGRATOR_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_query_strings": [
        "code",
        "state"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/google-sheets-integrator/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "GOOGLE_SHEETS_INTEGRATOR_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "GOOGLE_SHEETS_INTEGRATOR_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/zoho-integrator/AuthenticateCallback",
      "backend": [
        {
          "url_pattern": "/Public/AuthenticateCallback",
          "host": [
            "{{ env "ZOHO_INTEGRATOR_HOST" }}"
          ],
          "encoding": "no-op",
          "method": "GET"
        }
      ],
      "input_query_strings": [
        "code",
        "state"
      ],
      "method": "GET",
      "output_encoding": "no-op"
    },
    {
      "endpoint": "/v1/zoho-integrator/internals/{method}",
      "backend": [
        {
          "url_pattern": "/Internals/{method}",
          "host": [
            "{{ env "ZOHO_INTEGRATOR_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "ZOHO_INTEGRATOR_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/Objects/{method}",
      "backend": [
        {
          "url_pattern": "/Objects/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/Providers/{method}",
      "backend": [
        {
          "url_pattern": "/Providers/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/UnifyRules/{method}",
      "backend": [
        {
          "url_pattern": "/UnifyRules/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/Webhooks/{method}",
      "backend": [
        {
          "url_pattern": "/Webhooks/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/Schemas/{method}",
      "backend": [
        {
          "url_pattern": "/Schemas/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/SchemafulObjects/{method}",
      "backend": [
        {
          "url_pattern": "/SchemafulObjects/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/CrmHubConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/CrmHubConfigs/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/InflowActions/{method}",
      "backend": [
        {
          "url_pattern": "/InflowActions/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/crm-hub/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/Blobs/{method}",
          "host": [
            "{{ env "CRM_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "CRM_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/email-hub/Emails/{method}",
      "backend": [
        {
          "url_pattern": "/Emails/{method}",
          "host": [
            "{{ env "EMAIL_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "EMAIL_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/email-hub/Subscriptions/{method}",
      "backend": [
        {
          "url_pattern": "/Subscriptions/{method}",
          "host": [
            "{{ env "EMAIL_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "EMAIL_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/email-hub/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/Blobs/{method}",
          "host": [
            "{{ env "EMAIL_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "EMAIL_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/email-hub/Providers/{method}",
      "backend": [
        {
          "url_pattern": "/Providers/{method}",
          "host": [
            "{{ env "EMAIL_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "EMAIL_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Messages/{method}",
      "backend": [
        {
          "url_pattern": "/Messages/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Wabas/{method}",
      "backend": [
        {
          "url_pattern": "/Wabas/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Channels/{method}",
      "backend": [
        {
          "url_pattern": "/Channels/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Templates/{method}",
      "backend": [
        {
          "url_pattern": "/Templates/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Balances/{method}",
      "backend": [
        {
          "url_pattern": "/Balances/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/TransactionLogs/{method}",
      "backend": [
        {
          "url_pattern": "/TransactionLogs/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Migrations/{method}",
      "backend": [
        {
          "url_pattern": "/Migrations/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Managements/{method}",
      "backend": [
        {
          "url_pattern": "/Managements/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/Medias/{method}",
      "backend": [
        {
          "url_pattern": "/Medias/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/ProductCatalogs/{method}",
      "backend": [
        {
          "url_pattern": "/ProductCatalogs/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/AuditLogs/{method}",
      "backend": [
        {
          "url_pattern": "/AuditLogs/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/ConversationalAutomations/{method}",
      "backend": [
        {
          "url_pattern": "/ConversationalAutomations/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/WhatsappFlows/{method}",
      "backend": [
        {
          "url_pattern": "/WhatsappFlows/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/MetaConversionApis/{method}",
      "backend": [
        {
          "url_pattern": "/MetaConversionApis/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "MESSAGING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/Blobs/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Carts/{method}",
      "backend": [
        {
          "url_pattern": "/Carts/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Categories/{method}",
      "backend": [
        {
          "url_pattern": "/Categories/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Currencies/{method}",
      "backend": [
        {
          "url_pattern": "/Currencies/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/CustomCatalogs/{method}",
      "backend": [
        {
          "url_pattern": "/CustomCatalogs/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Languages/{method}",
      "backend": [
        {
          "url_pattern": "/Languages/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Orders/{method}",
      "backend": [
        {
          "url_pattern": "/Orders/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/PaymentProviderConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/PaymentProviderConfigs/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Payments/{method}",
      "backend": [
        {
          "url_pattern": "/Payments/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Products/{method}",
      "backend": [
        {
          "url_pattern": "/Products/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/ProductVariants/{method}",
      "backend": [
        {
          "url_pattern": "/ProductVariants/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Stores/{method}",
      "backend": [
        {
          "url_pattern": "/Stores/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Webhooks/{method}",
      "backend": [
        {
          "url_pattern": "/Webhooks/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/CustomCatalogConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/CustomCatalogConfigs/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/Vtex/{method}",
      "backend": [
        {
          "url_pattern": "/Vtex/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/commerce-hub/InflowActions/{method}",
      "backend": [
        {
          "url_pattern": "/InflowActions/{method}",
          "host": [
            "{{ env "COMMERCE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "COMMERCE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/share-hub/Analytics/{method}",
      "backend": [
        {
          "url_pattern": "/Analytics/{method}",
          "host": [
            "{{ env "SHARE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "SHARE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/share-hub/CustomDomains/{method}",
      "backend": [
        {
          "url_pattern": "/CustomDomains/{method}",
          "host": [
            "{{ env "SHARE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "SHARE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/share-hub/Links/{method}",
      "backend": [
        {
          "url_pattern": "/Links/{method}",
          "host": [
            "{{ env "SHARE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "SHARE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/share-hub/QrCodes/{method}",
      "backend": [
        {
          "url_pattern": "/QrCodes/{method}",
          "host": [
            "{{ env "SHARE_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "SHARE_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/audit-hub/AuditLogs/{method}",
      "backend": [
        {
          "url_pattern": "/AuditLogs/{method}",
          "host": [
            "{{ env "AUDIT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "AUDIT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/audit-hub/SystemAuditLogs/{method}",
      "backend": [
        {
          "url_pattern": "/SystemAuditLogs/{method}",
          "host": [
            "{{ env "AUDIT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "AUDIT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/Blobs/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/Events/{method}",
      "backend": [
        {
          "url_pattern": "/Events/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/Executions/{method}",
      "backend": [
        {
          "url_pattern": "/Executions/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/FlowHubConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/FlowHubConfigs/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/States/{method}",
      "backend": [
        {
          "url_pattern": "/States/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/Workflows/{method}",
      "backend": [
        {
          "url_pattern": "/Workflows/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/NeedConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/NeedConfigs/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/AiWorkflows/{method}",
      "backend": [
        {
          "url_pattern": "/AiWorkflows/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "FLOW_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/TextEnrichments/{method}",
      "backend": [
        {
          "url_pattern": "/TextEnrichments/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/IntelligentHubConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/IntelligentHubConfigs/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/Blobs/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/Documents/{method}",
      "backend": [
        {
          "url_pattern": "/Documents/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/KnowledgeBases/{method}",
      "backend": [
        {
          "url_pattern": "/KnowledgeBases/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/RecommendedReplies/{method}",
      "backend": [
        {
          "url_pattern": "/RecommendedReplies/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/TopicAnalytics/{method}",
      "backend": [
        {
          "url_pattern": "/TopicAnalytics/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTELLIGENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/webhook-hub/FacebookWebhooks/{method}",
      "backend": [
        {
          "url_pattern": "/FacebookWebhooks/{method}",
          "host": [
            "{{ env "WEBHOOK_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "WEBHOOK_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Companies/{method}",
      "backend": [
        {
          "url_pattern": "/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Users/<USER>",
      "backend": [
        {
          "url_pattern": "/Users/<USER>",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/EnabledFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/EnabledFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Roles/{method}",
      "backend": [
        {
          "url_pattern": "/Roles/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Migrations/{method}",
      "backend": [
        {
          "url_pattern": "/Migrations/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Features/{method}",
      "backend": [
        {
          "url_pattern": "/Features/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/IpWhitelists/{method}",
      "backend": [
        {
          "url_pattern": "/IpWhitelists/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Companies/{method}",
      "backend": [
        {
          "url_pattern": "/management/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Users/<USER>",
      "backend": [
        {
          "url_pattern": "/management/Users/<USER>",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/EnabledFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/management/EnabledFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Roles/{method}",
      "backend": [
        {
          "url_pattern": "/management/Roles/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Features/{method}",
      "backend": [
        {
          "url_pattern": "/management/Features/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/IpWhitelists/{method}",
      "backend": [
        {
          "url_pattern": "/management/IpWhitelists/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/Rbac/{method}",
      "backend": [
        {
          "url_pattern": "/management/Rbac/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/management/ImportUser/{method}",
      "backend": [
        {
          "url_pattern": "/management/ImportUser/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TENANT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/Messages/{method}",
      "backend": [
        {
          "url_pattern": "/Messages/{method}",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "USER_EVENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/Sessions/{method}",
      "backend": [
        {
          "url_pattern": "/Sessions/{method}",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "USER_EVENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/Blobs/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/TicketCompanyConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/TicketCompanyConfigs/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/TicketPriorities/{method}",
      "backend": [
        {
          "url_pattern": "/TicketPriorities/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/Tickets/{method}",
      "backend": [
        {
          "url_pattern": "/Tickets/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/TicketTypes/{method}",
      "backend": [
        {
          "url_pattern": "/TicketTypes/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/TicketActivities/{method}",
      "backend": [
        {
          "url_pattern": "/TicketActivities/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/TicketStatuses/{method}",
      "backend": [
        {
          "url_pattern": "/TicketStatuses/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/TicketComments/{method}",
      "backend": [
        {
          "url_pattern": "/TicketComments/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/ticketing-hub/Managements/{method}",
      "backend": [
        {
          "url_pattern": "/Managements/{method}",
          "host": [
            "{{ env "TICKETING_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "TICKETING_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/Notifications/{method}",
      "backend": [
        {
          "url_pattern": "/Notifications/{method}",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "USER_EVENT_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/internal-integration-hub/NetSuite/Internal/{method}",
      "backend": [
        {
          "url_pattern": "/NetSuite/Internal/{method}",
          "host": [
            "{{ env "INTERNAL_INTEGRATION_HUB_HOST" }}"
          ],
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "INTERNAL_INTEGRATION_HUB_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/messaging-hub/authorized/Balances/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Balances/{method}",
          "host": [
            "{{ env "MESSAGING_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/UserWorkspaces/{method}",
      "backend": [
        {
          "url_pattern": "/UserWorkspaces/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/Workspaces/{method}",
      "backend": [
        {
          "url_pattern": "/Workspaces/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Companies/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Companies/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Users/<USER>",
      "backend": [
        {
          "url_pattern": "/authorized/Users/<USER>",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Plans/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Plans/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Roles/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Roles/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Features/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Features/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/PlanDefinitions/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/PlanDefinitions/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Subscriptions/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Subscriptions/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/UserWorkspaces/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/UserWorkspaces/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/IpWhitelists/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/IpWhitelists/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/ExperimentalFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/ExperimentalFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Rbac/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Rbac/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/Blobs/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Blobs/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/tenant-hub/authorized/EnabledFeatures/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/EnabledFeatures/{method}",
          "host": [
            "{{ env "TENANT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/authorized/Notifications/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Notifications/{method}",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/audit-hub/authorized/SystemAuditLogs/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/SystemAuditLogs/{method}",
          "host": [
            "{{ env "AUDIT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/authorized/CompanyConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/CompanyConfigs/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/authorized/CompanyAgentConfigs/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/CompanyAgentConfigs/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/authorized/KnowledgeBases/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/KnowledgeBases/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/authorized/SqlJobs/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/SqlJobs/{method}",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/authorized/UserEvents/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/UserEvents/{method}",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/user-event-hub/authorized/UserEventTypes/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/UserEventTypes/{method}",
          "host": [
            "{{ env "USER_EVENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/intelligent-hub/authorized/Playgrounds/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/Playgrounds/{method}",
          "host": [
            "{{ env "INTELLIGENT_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/flow-hub/authorized/AiWorkflows/{method}",
      "backend": [
        {
          "url_pattern": "/authorized/AiWorkflows/{method}",
          "host": [
            "{{ env "FLOW_HUB_HOST" }}"
          ],
          "extra_config": {
            "plugin/http-client": {
              "name": "krakend-authentication-plugin",
              "krakend-authentication-plugin": {
                "get_user_auth_details_url": "{{ env "TENANT_HUB_HOST" }}{{ env "TENANT_HUB_GET_USER_AUTHENTICATION_DETAILS" }}"
              }
            }
          },
          "method": "POST"
        }
      ],
      "extra_config": {
        "auth/validator": {
          "alg": "RS256",
          "audience": [
            "{{ env "AUTH_0_AUDIENCE" }}"
          ],
          "cache": true,
          "jwk_url": "{{ env "AUTH_0_JWK_URL" }}",
          "propagate_claims": [
            [
              "https://app.sleekflow.io/email",
              "X-Sleekflow-Email"
            ],
            [
              "https://app.sleekflow.io/tenanthub_user_id",
              "X-Sleekflow-TenantHub-User-Id"
            ],
            [
              "https://app.sleekflow.io/user_id",
              "X-Sleekflow-User-Id"
            ],
            [
              "https://app.sleekflow.io/roles",
              "X-Sleekflow-Roles"
            ],
            [
              "https://app.sleekflow.io/connection_strategy",
              "X-Sleekflow-Connection-Strategy"
            ],
            [
              "https://app.sleekflow.io/login_as_user",
              "X-Sleekflow-Login-As-User"
            ]
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent",
        "X-Sleekflow-Roles",
        "X-Sleekflow-Email",
        "X-Sleekflow-TenantHub-User-Id",
        "X-Sleekflow-User-Id",
        "X-Sleekflow-Login-As-User",
        "X-Sleekflow-Connection-Strategy",
        "X-Sleekflow-Distributed-Invocation-Context"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/management/SleekflowCompanies/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/management/SleekflowCompanies/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "PUBLIC_API_GATEWAY_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/management/SleekflowCompanyUsers/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/management/SleekflowCompanyUsers/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "PUBLIC_API_GATEWAY_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/management/Subscriptions/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/management/Subscriptions/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint('{{ env "PUBLIC_API_GATEWAY_KEY" }}')",
          "sources": [
            "lua/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/portal/Authentications/{method}",
      "backend": [
        {
          "url_pattern": "/portal/Authentications/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/portal/Wabas/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/portal/Wabas/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/JwtToken/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Authorization",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/portal/Accounts/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/portal/Accounts/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/JwtToken/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Authorization",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/portal/Channels/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/portal/Channels/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/JwtToken/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "Authorization",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Medias/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/whatsapp/cloudapi/Medias/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "X-Sleekflow-Api-Key",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Messages/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/whatsapp/cloudapi/Messages/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "X-Sleekflow-Api-Key",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Templates/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/whatsapp/cloudapi/Templates/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "X-Sleekflow-Api-Key",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Webhooks/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/whatsapp/cloudapi/Webhooks/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "X-Sleekflow-Api-Key",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Channels/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/whatsapp/cloudapi/Channels/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "X-Sleekflow-Api-Key",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    },
    {
      "endpoint": "/v1/public-api-gateway/messaging-hub/whatsapp/cloudapi/Transactions/{method}",
      "backend": [
        {
          "url_pattern": "/messaging-hub/whatsapp/cloudapi/Transactions/{method}",
          "host": [
            "{{ env "PUBLIC_API_GATEWAY_HOST" }}"
          ],
          "encoding": "json",
          "method": "POST"
        }
      ],
      "extra_config": {
        "modifier/lua-endpoint": {
          "allow_open_libs": false,
          "live": false,
          "pre": "pre_endpoint()",
          "sources": [
            "lua/PublicApiGateway/ApiKey/pre_endpoint.lua"
          ]
        },
        "modifier/lua-proxy": {
          "allow_open_libs": false,
          "live": false,
          "post": "post_proxy()",
          "sources": [
            "lua/post_proxy.lua"
          ]
        }
      },
      "input_headers": [
        "X-Sleekflow-Api-Key",
        "Content-Type",
        "Traceparent"
      ],
      "method": "POST"
    }
  ],
  "extra_config": {
    "router": {
      "auto_options": true,
      "hide_version_header": true,
      "logger_skip_paths": [
        "/__health",
        "/v1/tenant-hub/management/Rbac/IsRbacEnabled",
        "/v1/audit-hub/SystemAuditLogs/CreateSystemAuditLog",
        "/v1/user-event-hub/SignalRWebhook",
        "/v1/user-event-hub/Messages/SendMessageToUsers",
        "/v1/user-event-hub/Notifications/SendPushNotification",
        "/v1/user-event-hub/ReliableMessage/negotiate"
      ]
    },
    "telemetry/logging": {
      "level": "INFO",
      "prefix": "[KRAKEND]",
      "stdout": true,
      "syslog": false
    }
  },
  "plugin": {
    "folder": "./plugins/",
    "pattern": ".so"
  },
  "timeout": "60000ms",
  "version": 3
}