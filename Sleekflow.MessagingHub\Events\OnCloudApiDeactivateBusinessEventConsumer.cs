using MassTransit;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiDeactivateBusinessEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiDeactivateBusinessEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiDeactivateBusinessEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiDeactivateBusinessEventConsumer : IConsumer<OnCloudApiDeactivateBusinessEvent>
{
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;

    public OnCloudApiDeactivateBusinessEventConsumer(
        IWabaService wabaService,
        IBusinessBalanceService businessBalanceService)
    {
        _wabaService = wabaService;
        _businessBalanceService = businessBalanceService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiDeactivateBusinessEvent> context)
    {
        // var facebookBusinessId = context.Message.FacebookBusinessId;
        // var businessBalances = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);
        //
        // if (businessBalances is null)
        // {
        //     throw new SfNotFoundObjectException(facebookBusinessId);
        // }
        //
        // var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(businessBalances.FacebookBusinessId);
        //
        // if (wabas is null)
        // {
        //     throw new SfInternalErrorException(
        //         $"Unable to locate wabas with facebook business id {businessBalances.FacebookBusinessId}");
        // }
        //
        // foreach (var waba in wabas)
        // {
        //     foreach (var wabaPhoneNumber in waba.WabaPhoneNumbers)
        //     {
        //         var deactivateStatus = await _wabaService.DeactivateWabaAsync(wabaPhoneNumber.FacebookPhoneNumberId);
        //         _logger.LogInformation(
        //             "Deactivate {Waba} / {Status}",
        //             JsonConvert.SerializeObject(waba), deactivateStatus);
        //     }
        // }
    }
}