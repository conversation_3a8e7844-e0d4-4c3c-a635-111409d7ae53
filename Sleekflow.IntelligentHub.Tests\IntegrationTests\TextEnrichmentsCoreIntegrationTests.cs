using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Triggers.TextEnrichments;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;
using Sleekflow.Persistence;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class TextEnrichmentsCoreIntegrationTests
{
    private const string MockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
    private readonly IntelligentHubUsageFilter _intelligentHubUsageFilter;
    private readonly AuditEntity.SleekflowStaff _sleekflowStaff;

    public TextEnrichmentsCoreIntegrationTests()
    {
        var utcNow = DateTimeOffset.UtcNow;
        _intelligentHubUsageFilter = new IntelligentHubUsageFilter(
            new DateTimeOffset(utcNow.Year, utcNow.Month, 1, 0, 0, 0, TimeSpan.Zero),
            new DateTimeOffset(utcNow.Year, utcNow.Month, DateTime.DaysInMonth(utcNow.Year, utcNow.Month), 23, 59, 59, TimeSpan.Zero));
        _sleekflowStaff = new AuditEntity.SleekflowStaff(
            "3880",
            new List<string>
            {
                "233", "282"
            });
    }

    [SetUp]
    public void TestSetUp()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test requires ShareHub to run in the background");
        }
    }

    [Test]
    public async Task ChangeToneTest()
    {
        // /TextEnrichments/ChangeTone
        var changeToneInput =
            new ChangeTone.ChangeToneInput(
                "你好嗎？",
                TargetToneTypes.Authoritative,
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var changeToneScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(changeToneInput).ToUrl("/TextEnrichments/ChangeTone");
            });
        var changeToneOutput =
            await changeToneScenarioResult.ReadAsJsonAsync<
                Output<ChangeTone.ChangeToneOutput>>();

        Assert.That(changeToneOutput, Is.Not.Null);
        Assert.That(changeToneOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task RephraseTest()
    {
        // /TextEnrichments/Rephrase
        var rephraseInput =
            new Rephrase.RephraseInput(
                "祝你心想事成，萬事如意。",
                TargetRephraseTypes.Bulletize,
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var rephraseScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(rephraseInput).ToUrl("/TextEnrichments/Rephrase");
            });
        var rephraseOutput =
            await rephraseScenarioResult.ReadAsJsonAsync<
                Output<Rephrase.RephraseOutput>>();

        Assert.That(rephraseOutput, Is.Not.Null);
        Assert.That(rephraseOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task TranslateTest()
    {
        // /TextEnrichments/Translate
        var translateInput =
            new Translate.TranslateInput(
                "你好嗎？",
                "en",
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var translateScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(translateInput).ToUrl("/TextEnrichments/Translate");
            });
        var translateOutput =
            await translateScenarioResult.ReadAsJsonAsync<
                Output<Translate.TranslateOutput>>();

        Assert.That(translateOutput, Is.Not.Null);
        Assert.That(translateOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task CustomPromptRewriteTest()
    {
        // /TextEnrichments/CustomPromptRewrite
        var customPromptRewriteInput =
            new CustomPromptRewrite.CustomPromptRewriteInput(
                "你必须给我马上离开这里！！！",
                "Please translate the text to English and make sure the tone is polite",
                MockCompanyId,
                _intelligentHubUsageFilter,
                _sleekflowStaff);
        var customPromptRewriteScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(customPromptRewriteInput).ToUrl("/TextEnrichments/CustomPromptRewrite");
            });
        var customPromptRewriteOutput =
            await customPromptRewriteScenarioResult.ReadAsJsonAsync<
                Output<CustomPromptRewrite.CustomPromptRewriteOutput>>();

        Assert.That(customPromptRewriteOutput, Is.Not.Null);
        Assert.That(customPromptRewriteOutput!.HttpStatusCode, Is.EqualTo(200));
    }
}