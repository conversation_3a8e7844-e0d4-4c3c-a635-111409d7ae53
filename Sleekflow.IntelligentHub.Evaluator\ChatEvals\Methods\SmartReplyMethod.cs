using System.Text;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Evaluator.Utils;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;
using static Sleekflow.IntelligentHub.Evaluator.Constants.MethodNames;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals.Methods;

public class SmartReplyMethod : IMethod<ChatEvalConfig, ChatEvalOutput>
{
    private readonly IChatService _chatService;

    public SmartReplyMethod()
    {
        using var scope = Application.Host.Services.CreateScope();
        _chatService = scope.ServiceProvider.GetRequiredService<IChatService>();
    }

    public string MethodName => SmartReply;

    public async Task<ChatEvalOutput> CompleteAsync(
        ChatEvalConfig chatEvalConfig,
        ChatMessageContent[]? questionContexts,
        List<string>? sourceFilenames,
        ReplyGenerationContext replyGenerationContext,
        CompanyAgentConfig agentConfig,
        SfChatEntry[]? sfChatEntriesQuestionContexts)
    {
        var sfChatEntries = questionContexts.Select(ChatEntriesUtil.ToChatEntries);

        var (asyncEnumerable, _, sourcesStr) = await _chatService.StreamAnswerAsync(
            [..sfChatEntries],
            chatEvalConfig.SleekflowCompanyId);

        var answerBuilder = new StringBuilder();
        await foreach (var item in asyncEnumerable)
        {
            answerBuilder.Append(item);
        }

        var finalAnswer = answerBuilder.ToString();
        if (string.IsNullOrEmpty(finalAnswer))
        {
            throw new Exception("Failed to generate answer.");
        }

        return new ChatEvalOutput(sourcesStr!, finalAnswer);
    }
}