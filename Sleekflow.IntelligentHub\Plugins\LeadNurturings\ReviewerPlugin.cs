using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IReviewerPlugin
{
    [KernelFunction("review_response")]
    [Description(
        "Reviews proposed responses using data from data pane and stores review results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with review details and approval status.")]
    Task<string> ReviewResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where proposed response is stored in the data pane.")]
        string proposedResponseKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where knowledge information is stored in the data pane (optional).")]
        string knowledgeKey = "");
}

public class ReviewerPlugin : BaseLeadNurturingPlugin, IReviewerPlugin
{
    public ReviewerPlugin(
        ILogger<ReviewerPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingCollaborationChatCacheService chatCacheService,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel,
            chatCacheService)
    {
    }

    private async Task<string> ReviewResponseAsync(
        Kernel kernel,
        string proposedResponse,
        string conversationContext,
        string knowledgeInfo = "",
        string responseLanguage = "English")
    {
        var reviewerAgent = _agentDefinitions.GetReviewerAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true),
            responseLanguage,
            _chatCacheService!);

        var agentThread = CreateAgentThread(conversationContext);

        if (!string.IsNullOrEmpty(knowledgeInfo))
        {
            AddContextToThread(agentThread, knowledgeInfo);
        }

        // Add the proposed response to review
        AddContextToThread(agentThread, $"{{ \"response\": \"{proposedResponse}\" }}");

        return await ExecuteAgentWithTelemetryAsync(reviewerAgent, agentThread, "ReviewerPlugin");
    }

    [KernelFunction("review_response")]
    [Description(
        "Reviews proposed responses using data from data pane and stores review results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with review details and approval status.")]
    public async Task<string> ReviewResponseWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where proposed response is stored in the data pane.")]
        string proposedResponseKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where knowledge information is stored in the data pane (optional).")]
        string knowledgeKey = "")
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.ReviewerAgentResponse>(
            sessionKey,
            "Reviewer",
            dataRetrievalFunc: async () => await RetrieveDataWithConfiguration(
                sessionKey,
                proposedResponseKey,
                conversationContextKey,
                knowledgeKey),
            agentExecutionFunc: async (data) => await ReviewResponseAsync(
                _kernel.Clone(),
                data["proposedResponse"],
                data["conversationContext"],
                data["knowledgeInfo"],
                data["responseLanguage"]),
            storageKey: _keyManager.GetReviewKey(sessionKey),
            storageDataType: AgentOutputKeys.ReviewComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreReviewComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task<Dictionary<string, string>> RetrieveDataWithConfiguration(
        string sessionKey,
        string proposedResponseKey,
        string conversationContextKey,
        string knowledgeKey)
    {
        var results = await RetrieveMultipleDataAsync(
            (proposedResponseKey, AgentOutputKeys.ResponseComplete, "proposedResponse"),
            (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext")
        );

        // Add optional knowledge data
        if (!string.IsNullOrEmpty(knowledgeKey))
        {
            var knowledgeInfo = await _dataPane.GetData(knowledgeKey, AgentOutputKeys.KnowledgeComplete) ?? "";
            results["knowledgeInfo"] = knowledgeInfo;
        }
        else
        {
            results["knowledgeInfo"] = "";
        }

        // Retrieve configuration
        results["responseLanguage"] = await GetConfigurationAsync(
            sessionKey,
            AgentOutputKeys.ResponseLanguage,
            "English");

        return results;
    }

    private async Task StoreReviewComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.ReviewerAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "ReviewerAgent", AgentOutputKeys.ReviewDecision),
                    AgentOutputKeys.ReviewDecision,
                    parsedResponse.Review);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "ReviewerAgent",
                        AgentOutputKeys.ReviewPlaceholderAbsenceReasoning),
                    AgentOutputKeys.ReviewPlaceholderAbsenceReasoning,
                    parsedResponse.PlaceholderAbsenceReasoning);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "ReviewerAgent",
                        AgentOutputKeys.ReviewKnowledgeIntegrationReasoning),
                    AgentOutputKeys.ReviewKnowledgeIntegrationReasoning,
                    parsedResponse.KnowledgeIntegrationReasoning);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "ReviewerAgent",
                        AgentOutputKeys.ReviewResponseLanguageReasoning),
                    AgentOutputKeys.ReviewResponseLanguageReasoning,
                    parsedResponse.ResponseLanguageReasoning);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "ReviewerAgent",
                        AgentOutputKeys.ReviewNaturalnessReasoning),
                    AgentOutputKeys.ReviewNaturalnessReasoning,
                    parsedResponse.NaturalnessReasoning);

                _logger.LogInformation(
                    "Review completed for session {SessionKey}: {Review}",
                    sessionKey,
                    parsedResponse.Review);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to store review components for session {SessionKey}", sessionKey);
        }
    }
}