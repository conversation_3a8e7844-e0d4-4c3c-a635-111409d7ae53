﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.WebPageDocuments;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.Documents.WebPageDocuments;

[TriggerGroup(ControllerNames.Documents)]
public class GetWebPageDocumentChunks
    : ITrigger<
        GetWebPageDocumentChunks.GetWebPageDocumentChunksInput,
        GetWebPageDocumentChunks.GetWebPageDocumentChunksOutput>
{
    private readonly IWebPageDocumentChunkService _webPageDocumentChunkService;

    public GetWebPageDocumentChunks(
        IWebPageDocumentChunkService webPageDocumentChunkService)
    {
        _webPageDocumentChunkService = webPageDocumentChunkService;
    }

    public class GetWebPageDocumentChunksInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("document_id")]
        public string DocumentId { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetWebPageDocumentChunksInput(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
            ContinuationToken = continuationToken;
            Limit = limit;
        }
    }

    public class GetWebPageDocumentChunksOutput
    {
        [JsonProperty("document_chunks")]
        public List<Chunk> DocumentChunks { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetWebPageDocumentChunksOutput(List<Chunk> documentChunks, string? nextContinuationToken)
        {
            DocumentChunks = documentChunks;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetWebPageDocumentChunksOutput> F(GetWebPageDocumentChunksInput getWebPageDocumentChunksInput)
    {
        var (webPageDocumentChunks, nextContinuationToken) =
            await _webPageDocumentChunkService.GetWebPageDocumentChunksAsync(
                getWebPageDocumentChunksInput.SleekflowCompanyId,
                getWebPageDocumentChunksInput.DocumentId,
                getWebPageDocumentChunksInput.ContinuationToken,
                getWebPageDocumentChunksInput.Limit);

        return new GetWebPageDocumentChunksOutput(
            webPageDocumentChunks.Cast<Chunk>().ToList(),
            nextContinuationToken);
    }
}