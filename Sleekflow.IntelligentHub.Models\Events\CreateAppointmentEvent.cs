using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Models.Events;

public class CreateAppointmentEvent : IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public string Caller { get; set; }

    public string CallerName { get; set; }

    public string Receiver { get; set; }

    public string EventName { get; set; }

    public string BranchName { get; set; }

    public DateTimeOffset ScheduledTime { get; set; }

    public CreateAppointmentEvent(
        string sleekflowCompanyId,
        string caller,
        string callerName,
        string receiver,
        string eventName,
        string branchName,
        DateTimeOffset scheduledTime)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Caller = caller;
        CallerName = callerName;
        Receiver = receiver;
        EventName = eventName;
        BranchName = branchName;
        ScheduledTime = scheduledTime;
    }
}