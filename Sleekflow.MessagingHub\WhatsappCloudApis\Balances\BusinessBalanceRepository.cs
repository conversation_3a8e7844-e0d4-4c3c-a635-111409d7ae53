using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Balances;

public interface IBusinessBalanceRepository : IRepository<BusinessBalance>
{
}

public class BusinessBalanceRepository
    : BaseRepository<BusinessBalance>, IBusinessBalanceRepository, ISingletonService
{
    public BusinessBalanceRepository(
        ILogger<BusinessBalanceRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}