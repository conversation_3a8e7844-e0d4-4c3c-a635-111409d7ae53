using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.IntelligentHubDb;
using Entity = Sleekflow.Persistence.Entity;

namespace Sleekflow.IntelligentHub.Models.FaqAgents.ChatContexts;

[Resolver(typeof(IIntelligentHubDbResolver))]
[DatabaseId("intelligenthubdb")]
[ContainerId("message_processing_history")]
public class ChatContextHistory : Entity
{
    public const string SysTypeNameValue = "MessageProcessingHistory";

    [JsonProperty(PropertyName = "sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyName = "context_id")]
    public string ContextId { get; set; }

    [JsonProperty(PropertyName = "phone_number")]
    public string PhoneNumber { get; set; }

    [JsonProperty(PropertyName = "user")]
    public string User { get; set; }

    [JsonProperty(PropertyName = "bot")]
    public string Bot { get; set; }

    [JsonProperty(PropertyName = "sent_time")]
    public DateTimeOffset SentTime { get; set; }

    [JsonProperty("prompt_num_of_tokens")]
    public int PromptNumOfTokens { get; }

    [JsonProperty("answer_num_of_tokens")]
    public int AnswerNumOfTokens { get; }

    [JsonConstructor]
    public ChatContextHistory(
        string id,
        string sleekflowCompanyId,
        string contextId,
        string phoneNumber,
        string user,
        string bot,
        DateTimeOffset sentTime,
        int promptNumOfTokens,
        int answerNumOfTokens)
        : base(id, SysTypeNameValue)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ContextId = contextId;
        PhoneNumber = phoneNumber;
        User = user;
        Bot = bot;
        SentTime = sentTime;
        PromptNumOfTokens = promptNumOfTokens;
        AnswerNumOfTokens = answerNumOfTokens;
    }
}