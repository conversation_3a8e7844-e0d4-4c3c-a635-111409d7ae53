﻿using System.Net;
using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Polly;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;
using JsonConfig = Sleekflow.JsonConfigs.JsonConfig;

namespace Sleekflow.DataMigrator.Executors;

internal class EntityMove : IExecutor
{
    private readonly CosmosClient _sourceCosmosClient;
    private readonly CosmosClient _targetCosmosClient;

    private string? _sourceDatabaseId;
    private string? _sourceContainerId;
    private string? _targetDatabaseId;
    private string? _targetContainerId;

    public EntityMove(
        DbConfig sourceDbConfig,
        DbConfig targetDbConfig)
    {
        _sourceCosmosClient = new CosmosClient(
            sourceDbConfig.Endpoint,
            sourceDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });

        _targetCosmosClient = new CosmosClient(
            targetDbConfig.Endpoint,
            targetDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Entity Move";
    }

    public async Task PrepareAsync()
    {
        var (sourceDatabaseId, sourceContainerId) = await PromptAsync(_sourceCosmosClient, "source");
        var (targetDatabaseId, targetContainerId) = await PromptAsync(_targetCosmosClient, "target");

        _sourceDatabaseId = sourceDatabaseId;
        _sourceContainerId = sourceContainerId;
        _targetDatabaseId = targetDatabaseId;
        _targetContainerId = targetContainerId;
    }

    private static async Task<(string, string)> PromptAsync(
        CosmosClient cosmosClient,
        string prefix)
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            $"Select your {prefix} database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerId = Prompt.Select(
            $"Select your {prefix} database",
            containerIds);

        return (selectedDatabaseId, selectedContainerId);
    }

    public async Task ExecuteAsync()
    {
        var count = await MoveObjects();
        Console.WriteLine(
            $"Completed from {_sourceDatabaseId}.{_sourceContainerId} to {_targetDatabaseId}.{_targetContainerId} for {count} objects");
    }

    private async Task<int> MoveObjects()
    {
        var retryPolicy = Policy
            .Handle<CosmosException>(exception => exception.StatusCode == HttpStatusCode.TooManyRequests)
            .WaitAndRetryAsync(
                9,
                sleepDurationProvider: (retryCount, exception, _) =>
                {
                    if (exception is CosmosException { RetryAfter: { } } cosmosException
                        && retryCount <= 2)
                    {
                        return cosmosException.RetryAfter.Value;
                    }

                    return TimeSpan.FromSeconds(1.37) * retryCount;
                },
                onRetryAsync: (e, timeSpan, retryCount, context) =>
                {
                    if (retryCount < 6)
                    {
                        return Task.CompletedTask;
                    }

                    Console.WriteLine(
                        $"TooManyRequests retryCount=[{retryCount}], timeSpan=[{timeSpan}], sourceContainerId=[{_sourceContainerId}], targetContainerId=[{_targetContainerId}]");

                    return Task.CompletedTask;
                });

        var sourceDatabase = _sourceCosmosClient.GetDatabase(_sourceDatabaseId);
        var sourceContainer = sourceDatabase.GetContainer(_sourceContainerId);
        var targetDatabase = _targetCosmosClient.GetDatabase(_targetDatabaseId);
        var targetContainer = targetDatabase.GetContainer(_targetContainerId);
        var i = 0;

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(sourceContainer),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(
                        async () =>
                            await MigrateObject(dict, targetContainer, token));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }

    private static async Task<int> MigrateObject(
        Dictionary<string, object?> dict,
        Container targetContainer,
        CancellationToken token)
    {
        var itemResponse = await targetContainer.UpsertItemAsync(
            dict,
            new PartitionKey((string) dict["id"]!),
            new ItemRequestOptions()
            {
                IfMatchEtag = (string) dict["_etag"]!, EnableContentResponseOnWrite = false,
            },
            token);

        return 1;
    }
}