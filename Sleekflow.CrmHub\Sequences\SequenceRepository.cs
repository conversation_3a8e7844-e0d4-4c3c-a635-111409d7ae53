﻿using System.Net;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Sequences;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Sequences;

public interface ISequenceRepository : IRepository<Sequence>
{
    Task<long> GetNextValueAsync(
        string id,
        string partitionKey,
        CancellationToken cancellationToken = default);

    public Task InitializeSequenceSafeAsync(
        Sequence sequence,
        CancellationToken cancellationToken = default);
}

public class SequenceRepository : BaseRepository<Sequence>, ISequenceRepository, IScopedService
{
    public SequenceRepository(
        ILogger<BaseRepository<Sequence>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }

    public async Task<long> GetNextValueAsync(string id, string partitionKey, CancellationToken cancellationToken = default)
    {
        var sequence = await PatchAndGetAsync(
            id,
            partitionKey,
            new List<PatchOperation>
            {
                PatchOperation.Increment($"/{Sequence.PropertyNameValue}", 1)
            },
            cancellationToken: cancellationToken);

        return sequence.Value;
    }

    public async Task InitializeSequenceSafeAsync(
        Sequence sequence,
        CancellationToken cancellationToken = default)
    {
        var container = GetContainer();
        var retryPolicy = GetRetryPolicy();

        var policyResult = await retryPolicy.ExecuteAndCaptureAsync(
            async _ =>
            {
                try
                {
                    var patchItemRequestOptions = new PatchItemRequestOptions
                    {
                        EnableContentResponseOnWrite = true,
                    };

                    var itemResponse = await container.CreateItemAsync(
                        sequence,
                        new PartitionKey(sequence.Id),
                        cancellationToken: cancellationToken,
                        requestOptions: patchItemRequestOptions);

                    return itemResponse.Resource;
                }
                catch (CosmosException ex) when (ex.StatusCode == HttpStatusCode.Conflict)
                {
                    return GetAsync(sequence.Id, sequence.Id, cancellationToken).Result;
                }
            },
            new Dictionary<string, object>
            {
                {
                    "containerName", container.Id
                }
            });

        if (policyResult.FinalException != null)
        {
            throw new SfQueryException(policyResult.FinalException, nameof(CreateAndGetAsync));
        }
    }
}