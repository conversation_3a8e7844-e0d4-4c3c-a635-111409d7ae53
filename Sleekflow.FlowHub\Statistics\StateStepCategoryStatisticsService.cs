﻿using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Statistics;
using Sleekflow.Locks;

namespace Sleekflow.FlowHub.Statistics;

public interface IStateStepCategoryStatisticsService
{
    Task RecordStateStepCategoriesAsync(
        StateStepCategoryStatistics stateStepCategoryStatistics,
        string sleekflowCompanyId);
}

public sealed class StateStepCategoryStatisticsService
    : IStateStepCategoryStatisticsService,
      IScopedService
{
    private readonly ILogger<StateStepCategoryStatisticsService> _logger;
    private readonly IStateStepCategoryStatisticsRepository _stateStepCategoryStatisticsRepository;
    private readonly ILockService _lockService;

    public StateStepCategoryStatisticsService(
        ILogger<StateStepCategoryStatisticsService> logger,
        IStateStepCategoryStatisticsRepository stateStepCategoryStatisticsRepository,
        ILockService lockService)
    {
        _logger = logger;
        _stateStepCategoryStatisticsRepository = stateStepCategoryStatisticsRepository;
        _lockService = lockService;
    }

    public async Task RecordStateStepCategoriesAsync(
        StateStepCategoryStatistics stateStepCategoryStatistics,
        string sleekflowCompanyId)
    {
        Lock? @lock = null;

        try
        {
            @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    "StateStepCategoryStatistics",
                    sleekflowCompanyId,
                    stateStepCategoryStatistics.StateId
                },
                TimeSpan.FromSeconds(15),
                TimeSpan.FromSeconds(30));

            var existingRecord =
                (await _stateStepCategoryStatisticsRepository
                    .GetObjectsAsync(
                        x =>
                            x.SleekflowCompanyId == sleekflowCompanyId
                            && x.StateId == stateStepCategoryStatistics.StateId))
                .SingleOrDefault();

            if (existingRecord is not null)
            {
                return;
            }

            await _stateStepCategoryStatisticsRepository.CreateAsync(
                stateStepCategoryStatistics,
                new PartitionKeyBuilder()
                    .Add(sleekflowCompanyId)
                    .Add(stateStepCategoryStatistics.StateId)
                    .Build());
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to record state step categories for state {StateId} in company {SleekflowCompanyId}, step category count: {StepCategoryCountDict}",
                stateStepCategoryStatistics.StateId,
                sleekflowCompanyId,
                JsonConvert.SerializeObject(stateStepCategoryStatistics.StepCategoryCountDict));
        }
        finally
        {
            if (@lock is not null)
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }
    }
}