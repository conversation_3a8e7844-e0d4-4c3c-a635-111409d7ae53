using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Templates;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Template.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Templates)]
public class GetWhatsappCloudApiTemplates
    : ITrigger<
        GetWhatsappCloudApiTemplates.GetWhatsappCloudApiTemplatesInput,
        GetWhatsappCloudApiTemplates.GetWhatsappCloudApiTemplatesOutput>
{
    private readonly IWabaService _wabaService;
    private readonly ITemplateService _templateService;
    private readonly ILogger<GetWhatsappCloudApiTemplates> _logger;

    public GetWhatsappCloudApiTemplates(
        IWabaService wabaService,
        ITemplateService templateService,
        ILogger<GetWhatsappCloudApiTemplates> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
        _templateService = templateService;
    }

    public class GetWhatsappCloudApiTemplatesInput
    {
        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiTemplatesInput(
            string wabaId,
            string sleekflowCompanyId)
        {
            WabaId = wabaId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class GetWhatsappCloudApiTemplatesOutput
    {
        [JsonProperty("message_templates")]
        public List<WhatsappCloudApiTemplate> MessageTemplates { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiTemplatesOutput(List<WhatsappCloudApiTemplate> messageTemplates)
        {
            MessageTemplates = messageTemplates;
        }
    }

    public async Task<GetWhatsappCloudApiTemplatesOutput> F(
        GetWhatsappCloudApiTemplatesInput getWhatsappCloudApiTemplatesInput)
    {
        var waba = await _wabaService.GetWabaOrDefaultAsync(
            getWhatsappCloudApiTemplatesInput.WabaId,
            getWhatsappCloudApiTemplatesInput.SleekflowCompanyId);
        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var cloudApiWhatsappTemplates = await _templateService.GetCloudApiWhatsappTemplateAsync(
            waba.FacebookWabaId,
            hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null);
        return new GetWhatsappCloudApiTemplatesOutput(cloudApiWhatsappTemplates);
    }
}