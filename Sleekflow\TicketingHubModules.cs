using Microsoft.Extensions.DependencyInjection;
using Sleekflow.Persistence.TicketingHubDb;

#if SWAGGERGEN
using Moq;
#endif

namespace Sleekflow;

public static class TicketingHubModules
{
    public static void BuildTicketingHubDbServices(IServiceCollection b)
    {
#if SWAGGERGEN
        b.<PERSON><PERSON><PERSON><ITicketingHubDbConfig>(new Mock<ITicketingHubDbConfig>().Object);
        b.<PERSON><PERSON><ITicketingHubDbResolver>(new Mock<ITicketingHubDbResolver>().Object);

#else
        var ticketingHubDbConfig = new TicketingHubDbConfig();

        b.<PERSON><PERSON><PERSON><PERSON><ITicketingHubDbConfig>(ticketingHubDbConfig);
        b.<PERSON>d<PERSON><PERSON><PERSON><ITicketingHubDbResolver, TicketingHubDbResolver>();
#endif
    }
}