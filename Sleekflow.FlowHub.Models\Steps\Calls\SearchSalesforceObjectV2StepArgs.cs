﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class SearchSalesforceObjectV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.search-salesforce-object";

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [Required]
    [JsonProperty("conditions")]
    public List<SearchSalesforceObjectV2Condition> Conditions { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.SalesforceIntegration;

    [JsonConstructor]
    public SearchSalesforceObjectV2StepArgs(
        string connectionId,
        string entityTypeName,
        List<SearchSalesforceObjectV2Condition> conditions)
    {
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        Conditions = conditions;
    }
}

public class SearchSalesforceObjectV2Condition
{
    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("search_operator")]
    public string SearchOperator { get; set; }

    [JsonProperty("value__expr")]
    public string? ValueExpr { get; set; }

    [JsonConstructor]
    public SearchSalesforceObjectV2Condition(
        string fieldName,
        string searchOperator,
        string? valueExpr)
    {
        FieldName = fieldName;
        SearchOperator = searchOperator;
        ValueExpr = valueExpr;
    }
}