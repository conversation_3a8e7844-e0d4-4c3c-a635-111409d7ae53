using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Orders;

public abstract class OrderExternalIntegrationInfo
{
    [JsonProperty("provider_name")]
    public string ProviderName { get; set; }

    [JsonProperty("provider_order_id")]
    public string? ProviderOrderId { get; set; }

    [JsonConstructor]
    protected OrderExternalIntegrationInfo(
        string providerName,
        string? providerOrderId)
    {
        ProviderName = providerName;
        ProviderOrderId = providerOrderId;
    }
}