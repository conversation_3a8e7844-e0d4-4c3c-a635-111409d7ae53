using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Tests.TestClients;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class WorkflowsStepsIntegrationTests
{
    [Test]
    public async Task AddInternalNoteToContactStepTest()
    {
        var mockCompanyId = nameof(AddInternalNoteToContactStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - AddInternalNoteToContactStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        },
                        {
                            "content", "You have a new internal note."
                        }
                    },
                    null),
                new CallStep<AddInternalNoteToContactStepArgs>(
                    "step-1",
                    "AddInternalNoteToContactStep",
                    null,
                    null,
                    AddInternalNoteToContactStepArgs.CallName,
                    new AddInternalNoteToContactStepArgs(
                        "{{ usr_var_dict.contact_id }}",
                        "{{ usr_var_dict.content }}"))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactCollaboratorRelationshipsStepTest()
    {
        var mockCompanyId = nameof(UpdateContactCollaboratorRelationshipsStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactCollaboratorRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        },
                        {
                            "staff_id", "{{ '4b8cf8d0-fc79-41b7-be76-ea553acbab22' }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactCollaboratorRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactCollaboratorRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactCollaboratorRelationshipsStepArgs.CallName,
                    new UpdateContactCollaboratorRelationshipsStepArgs(
                        "{{ usr_var_dict.contact_id }}",
                        "{{ [usr_var_dict.staff_id] }}",
                        null,
                        null)),
                new CallStep<UpdateContactCollaboratorRelationshipsStepArgs>(
                    "step-2",
                    "UpdateContactCollaboratorRelationshipsStep 2",
                    null,
                    null,
                    UpdateContactCollaboratorRelationshipsStepArgs.CallName,
                    new UpdateContactCollaboratorRelationshipsStepArgs(
                        "{{ usr_var_dict.contact_id }}",
                        null,
                        "{{ [usr_var_dict.staff_id] }}",
                        null))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactConversationStatusStepTest()
    {
        var mockCompanyId = nameof(UpdateContactConversationStatusStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactConversationStatusStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        },
                        {
                            "status", "{{ 'Closed' }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactConversationStatusStepArgs>(
                    "step-1",
                    "UpdateContactConversationStatusStep 1",
                    null,
                    null,
                    UpdateContactConversationStatusStepArgs.CallName,
                    new UpdateContactConversationStatusStepArgs(
                        "{{ usr_var_dict.contact_id }}",
                        "{{ usr_var_dict.status }}"))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactLabelRelationshipsStepTest()
    {
        var mockCompanyId = nameof(UpdateContactLabelRelationshipsStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactLabelRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactLabelRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactLabelRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactLabelRelationshipsStepArgs.CallName,
                    new UpdateContactLabelRelationshipsStepArgs(
                        "{{ usr_var_dict.contact_id }}",
                        "{{ [\"Hello\", \"World\"] }}",
                        null,
                        null))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactListRelationshipsStepTest()
    {
        var mockCompanyId = nameof(UpdateContactListRelationshipsStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactListRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        },
                        {
                            "list_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactListRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactListRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactListRelationshipsStepArgs.CallName,
                    new UpdateContactListRelationshipsStepArgs(
                        "{{ usr_var_dict.contact_id }}",
                        "{{ [] }}",
                        null,
                        null))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactOwnerRelationshipsStepTest_CompanyRoundRobbinAllStaffs()
    {
        var mockCompanyId = nameof(UpdateContactOwnerRelationshipsStepTest_CompanyRoundRobbinAllStaffs);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactOwnerRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "setup-contact-and-conversation",
                    new Assign()
                    {
                        {
                            "contact", "{{ trigger_event_body.contact_id | sleekflow.get_contact }}"
                        },
                        {
                            "conversation", "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        {
                            "lists", "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactOwnerRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactOwnerRelationshipsStepArgs.CallName,
                    new UpdateContactOwnerRelationshipsStepArgs(
                        "{{ trigger_event_body.contact_id }}",
                        null,
                        null,
                        UpdateContactOwnerRelationshipsStepArgsStrategy.Company_RoundRobbin_All_Staffs))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactOwnerRelationshipsStepTest_RoundRobbinStaffOnly()
    {
        var mockCompanyId = nameof(UpdateContactOwnerRelationshipsStepTest_RoundRobbinStaffOnly);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactOwnerRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "setup-contact-and-conversation",
                    new Assign()
                    {
                        {
                            "contact", "{{ trigger_event_body.contact_id | sleekflow.get_contact }}"
                        },
                        {
                            "conversation", "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        {
                            "lists", "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactOwnerRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactOwnerRelationshipsStepArgs.CallName,
                    new UpdateContactOwnerRelationshipsStepArgs(
                        "{{ trigger_event_body.contact_id }}",
                        "{{ [ \"720c1be2-a694-4012-89d8-37d53d0d1023\" ] }}",
                        null,
                        UpdateContactOwnerRelationshipsStepArgsStrategy.RoundRobbin_StaffOnly))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactOwnerRelationshipsStepTest_RoundRobbinTeamOnly()
    {
        var mockCompanyId = nameof(UpdateContactOwnerRelationshipsStepTest_RoundRobbinTeamOnly);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactOwnerRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "setup-contact-and-conversation",
                    new Assign()
                    {
                        {
                            "contact", "{{ trigger_event_body.contact_id | sleekflow.get_contact }}"
                        },
                        {
                            "conversation", "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        {
                            "lists", "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactOwnerRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactOwnerRelationshipsStepArgs.CallName,
                    new UpdateContactOwnerRelationshipsStepArgs(
                        "{{ trigger_event_body.contact_id }}",
                        null,
                        "{{ [149] }}",
                        UpdateContactOwnerRelationshipsStepArgsStrategy.RoundRobbin_TeamOnly))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactOwnerRelationshipsStepTest_TeamAndRoundRobbinAllStaffsInTeam()
    {
        var mockCompanyId = nameof(UpdateContactOwnerRelationshipsStepTest_TeamAndRoundRobbinAllStaffsInTeam);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactOwnerRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "setup-contact-and-conversation",
                    new Assign()
                    {
                        {
                            "contact", "{{ trigger_event_body.contact_id | sleekflow.get_contact }}"
                        },
                        {
                            "conversation", "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        {
                            "lists", "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactOwnerRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactOwnerRelationshipsStepArgs.CallName,
                    new UpdateContactOwnerRelationshipsStepArgs(
                        "{{ trigger_event_body.contact_id }}",
                        null,
                        "{{ [149] }}",
                        UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_All_Staffs_In_Team))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactOwnerRelationshipsStepTest_TeamAndRoundRobbinSpecificStaffsInTeam()
    {
        var mockCompanyId = nameof(UpdateContactOwnerRelationshipsStepTest_TeamAndRoundRobbinSpecificStaffsInTeam);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactOwnerRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "setup-contact-and-conversation",
                    new Assign()
                    {
                        {
                            "contact", "{{ trigger_event_body.contact_id | sleekflow.get_contact }}"
                        },
                        {
                            "conversation", "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        {
                            "lists", "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactOwnerRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactOwnerRelationshipsStepArgs.CallName,
                    new UpdateContactOwnerRelationshipsStepArgs(
                        "{{ trigger_event_body.contact_id }}",
                        "{{ [\"70be7cd2-5b3d-4aad-8270-04e9fb553252\", \"7ec5163f3-6843-4d6b-8664-b6ad4985b4b1\"] }}",
                        "{{ [149] }}",
                        UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_RoundRobbin_Specific_Staffs_In_Team))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactOwnerRelationshipsStepTest_TeamAndUnassignedStaff()
    {
        var mockCompanyId = nameof(UpdateContactOwnerRelationshipsStepTest_TeamAndUnassignedStaff);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactOwnerRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "setup-contact-and-conversation",
                    new Assign()
                    {
                        {
                            "contact", "{{ trigger_event_body.contact_id | sleekflow.get_contact }}"
                        },
                        {
                            "conversation", "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        {
                            "lists", "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactOwnerRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactOwnerRelationshipsStepArgs.CallName,
                    new UpdateContactOwnerRelationshipsStepArgs(
                        "{{ trigger_event_body.contact_id }}",
                        null,
                        "{{ [149] }}",
                        UpdateContactOwnerRelationshipsStepArgsStrategy.Team_And_Unassigned_Staff))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactOwnerRelationshipsStepTest_Unassigned()
    {
        var mockCompanyId = nameof(UpdateContactOwnerRelationshipsStepTest_Unassigned);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactOwnerRelationshipsStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "setup-contact-and-conversation",
                    new Assign()
                    {
                        {
                            "contact", "{{ trigger_event_body.contact_id | sleekflow.get_contact }}"
                        },
                        {
                            "conversation", "{{ trigger_event_body.contact_id | sleekflow.get_contact_conversation }}"
                        },
                        {
                            "lists", "{{ trigger_event_body.contact_id | sleekflow.get_contact_lists }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactOwnerRelationshipsStepArgs>(
                    "step-1",
                    "UpdateContactOwnerRelationshipsStep 1",
                    null,
                    null,
                    UpdateContactOwnerRelationshipsStepArgs.CallName,
                    new UpdateContactOwnerRelationshipsStepArgs(
                        "{{ trigger_event_body.contact_id }}",
                        null,
                        null,
                        UpdateContactOwnerRelationshipsStepArgsStrategy.Unassigned))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateContactPropertiesStepTest()
    {
        var mockCompanyId = nameof(UpdateContactPropertiesStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateContactPropertiesStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        }
                    },
                    null),
                new CallStep<UpdateContactPropertiesStepArgs>(
                    "step-1",
                    "UpdateContactPropertiesStep 1",
                    null,
                    null,
                    UpdateContactPropertiesStepArgs.CallName,
                    new UpdateContactPropertiesStepArgs(
                        "{{ usr_var_dict.contact_id }}",
                        new Dictionary<string, string?>()
                        {
                            {
                                "first_name", "{{ 'test' }}"
                            },
                            {
                                "LastName", "'test'"
                            }
                        }))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task SendMessageStepTest()
    {
        var mockCompanyId = nameof(SendMessageStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - SendMessageStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        },
                        {
                            "content", "Test Message"
                        }
                    },
                    null),
                new CallStep<SendMessageStepArgs>(
                    "step-1",
                    "SendMessageStep",
                    null,
                    null,
                    SendMessageStepArgs.CallName,
                    new SendMessageStepArgs(
                        "whatsappcloudapi",
                        new FromTo(
                            "{{ usr_var_dict.contact_id }}",
                            null,
                            "{{ usr_var_dict.contact_id }}",
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null,
                            null),
                        "text",
                        "{{ { 'text_message': {'text': 'Welcome!'} } }}",
                        deliveryTypeExpr: null,
                        staffIdExpr: null))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task SendMessageStepTest_Validations()
    {
        var mockCompanyId = nameof(SendMessageStepTest_Validations);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync<Output<object>>(
            mockCompanyId,
            "My Testing Workflow - SendMessageStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new SimpleStep(
                    "step-0",
                    "Prepare",
                    new Assign()
                    {
                        {
                            "contact_id", "{{ '720c1be2-a694-4012-89d8-37d53d0d1023' }}"
                        },
                        {
                            "content", "Test Message"
                        }
                    },
                    null),
                new CallStep<SendMessageStepArgs>(
                    "step-1",
                    "SendMessageStep",
                    null,
                    null,
                    SendMessageStepArgs.CallName,
                    new SendMessageStepArgs(
                        null!,
                        null!,
                        null!,
                        null!,
                        deliveryTypeExpr: null,
                        staffIdExpr: null))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(400));
    }

    [Test]
    public async Task CreateSalesforceObjectStepTest()
    {
        var mockCompanyId = nameof(CreateSalesforceObjectStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - CreateSalesforceObjectStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new CallStep<CreateSalesforceObjectStepArgs>(
                    "step-0",
                    "CreateSalesforceObjectStep",
                    null,
                    null,
                    CreateSalesforceObjectStepArgs.CallName,
                    new CreateSalesforceObjectStepArgs(
                        "xMRfL4Y7xzN1Z4y4",
                        "Account",
                        "false",
                        "false",
                        null,
                        new Dictionary<string, string?>()
                        {
                            {
                                "Name", "Test Account"
                            }
                        }))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task SearchSalesforceObjectStepTest()
    {
        var mockCompanyId = nameof(SearchSalesforceObjectStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - SearchSalesforceObjectStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new CallStep<SearchSalesforceObjectStepArgs>(
                    "step-0",
                    "SearchSalesforceObjectStep",
                    null,
                    null,
                    SearchSalesforceObjectStepArgs.CallName,
                    new SearchSalesforceObjectStepArgs(
                        "xMRfL4Y7xzN1Z4y4",
                        "Account",
                        "false",
                        @"
                        [
                            {
                                ""field_name"": ""Name"",
                                ""operator"": ""DoesNotContain"",
                                ""value"": ""Gibberish""
                            }
                        ]"))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task UpdateSalesforceObjectStepTest()
    {
        var mockCompanyId = nameof(UpdateSalesforceObjectStepTest);
        var mockStaffId = "mock-staff-id";

        // /Workflows/CreateWorkflow
        var createWorkflowOutputOutput = await WorkflowTestClient.CreateWorkflowAsync(
            mockCompanyId,
            "My Testing Workflow - UpdateSalesforceObjectStep",
            mockStaffId,
            steps: new List<Step>()
            {
                new CallStep<UpdateSalesforceObjectStepArgs>(
                    "step-0",
                    "UpdateSalesforceObjectStep",
                    null,
                    null,
                    UpdateSalesforceObjectStepArgs.CallName,
                    new UpdateSalesforceObjectStepArgs(
                        "xMRfL4Y7xzN1Z4y4",
                        "0015j00001c5NWjAAM",
                        "Account",
                        "false",
                        new Dictionary<string, string?>()
                        {
                            {
                                "Name", "Test Account updated"
                            }
                        }))
            });

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowOutputOutput = await WorkflowTestClient.DeleteWorkflowAsync(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId);

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }
}