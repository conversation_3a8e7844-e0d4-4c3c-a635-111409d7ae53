using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.Events;

public class OnSalesforceCompleteStepActivationEvent
{
    public string AggregateStepId { get; set; }

    public string ProxyStateId { get; set; }

    public Stack<StackEntry> StackEntries { get; set; }

    public string? AggregateStateContext { get; set; }

    public OnSalesforceCompleteStepActivationEvent(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string? aggregateStateContext)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        AggregateStateContext = aggregateStateContext;
    }
} 