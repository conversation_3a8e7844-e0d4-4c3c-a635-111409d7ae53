using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Workers;

public class ExecuteTryCatchStepInput
{
    [JsonProperty("state_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StateId { get; set; }

    [JsonProperty("step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StepId { get; set; }

    [JsonProperty("try_step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string TryStepId { get; set; }

    [JsonProperty("catch_step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string CatchStepId { get; set; }

    [JsonProperty("stack_entries")]
    [System.ComponentModel.DataAnnotations.Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public ExecuteTryCatchStepInput(
        string stateId,
        string stepId,
        string tryStepId,
        string catchStepId,
        Stack<StackEntry> stackEntries)
    {
        StateId = stateId;
        StepId = stepId;
        TryStepId = tryStepId;
        CatchStepId = catchStepId;
        StackEntries = stackEntries;
    }
}