using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.TopicAnalytics;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.TopicAnalytics;

public interface ITopicAnalyticsTopicRepository : IRepository<TopicAnalyticsTopic>
{
}

public class TopicAnalyticsTopicRepository
    : BaseRepository<TopicAnalyticsTopic>,
        ITopicAnalyticsTopicRepository,
        IScopedService
{
    private readonly ILogger<TopicAnalyticsTopicRepository> _logger;

    public TopicAnalyticsTopicRepository(
        ILogger<TopicAnalyticsTopicRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
        _logger = logger;
    }




}