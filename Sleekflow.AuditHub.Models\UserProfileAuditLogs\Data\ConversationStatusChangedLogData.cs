using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class ConversationStatusChangedLogData
{
    [JsonProperty("original_status")]
    public string OriginalStatus { get; set; }

    [JsonProperty("new_status")]
    public string NewStatus { get; set; }

    [JsonProperty("scheduled_time")]
    public DateTimeOffset? ScheduledTime { get; set; }

    [JsonConstructor]
    public ConversationStatusChangedLogData(string originalStatus, string newStatus, DateTimeOffset? scheduledTime)
    {
        OriginalStatus = originalStatus;
        NewStatus = newStatus;
        ScheduledTime = scheduledTime;
    }
}