using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Languages;

public class Currency
{
    public const string PropertyNameCurrencyIsoCode = "currency_iso_code";
    public const string PropertyNameCurrencyName = "currency_name";
    public const string PropertyNameCurrencySymbol = "currency_symbol";

    [JsonProperty(PropertyNameCurrencyIsoCode)]
    public string CurrencyIsoCode { get; set; }

    [JsonProperty(PropertyNameCurrencyName)]
    public string CurrencyName { get; set; }

    [JsonProperty(PropertyNameCurrencySymbol)]
    public string CurrencySymbol { get; set; }

    [JsonConstructor]
    public Currency(
        string currencyIsoCode,
        string currencyName,
        string currencySymbol)
    {
        CurrencyIsoCode = currencyIsoCode;
        CurrencyName = currencyName;
        CurrencySymbol = currencySymbol;
    }
}