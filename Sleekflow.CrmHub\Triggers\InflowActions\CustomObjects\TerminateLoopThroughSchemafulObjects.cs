﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.CustomObjects;

[TriggerGroup(TriggerGroups.InflowActions)]
public class TerminateLoopThroughSchemafulObjects
    : ITrigger<
        TerminateLoopThroughSchemafulObjects.TerminateLoopThroughSchemafulObjectsInput,
        TerminateLoopThroughSchemafulObjects.TerminateLoopThroughSchemafulObjectsOutput>
{
    private readonly ICustomObjectIntegratorService _customObjectIntegratorService;

    public TerminateLoopThroughSchemafulObjects(
        ICustomObjectIntegratorService customObjectIntegratorService)
    {
        _customObjectIntegratorService = customObjectIntegratorService;
    }

    public class TerminateLoopThroughSchemafulObjectsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughSchemafulObjectsInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class TerminateLoopThroughSchemafulObjectsOutput
    {
        [JsonProperty("is_terminated")]
        public bool IsTerminated { get; set; }

        [JsonConstructor]
        public TerminateLoopThroughSchemafulObjectsOutput(bool isTerminated)
        {
            IsTerminated = isTerminated;
        }
    }

    public async Task<TerminateLoopThroughSchemafulObjectsOutput> F(
        TerminateLoopThroughSchemafulObjectsInput input)
    {
        var isTerminated = await _customObjectIntegratorService
            .TerminateInProgressLoopThroughExecutionAsync(
                input.FlowHubWorkflowId,
                input.FlowHubWorkflowVersionedId,
                input.SleekflowCompanyId);

        return new TerminateLoopThroughSchemafulObjectsOutput(isTerminated);
    }
}