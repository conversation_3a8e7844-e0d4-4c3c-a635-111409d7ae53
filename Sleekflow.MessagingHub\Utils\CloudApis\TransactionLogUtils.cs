using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;

namespace Sleekflow.MessagingHub.Utils.CloudApis;

public static class TransactionLogUtils
{
    public static Money GetCalculatedMarkup(
        MarkupProfile markupProfile,
        decimal userInitiatedCost,
        decimal businessInitiatedCost,
        int userInitiatedPaidQuantity,
        int businessInitiatedPaidQuantity)
    {
        var userInitiatedMarkup = MoneyExtensions.MultiplyByAmount(
            markupProfile.PerPaidUserInitiatedConversationFeeMarkup,
            userInitiatedPaidQuantity);

        var businessInitiatedMarkup = MoneyExtensions.MultiplyByAmount(
            markupProfile.PerPaidBusinessInitiatedConversationFeeMarkup,
            businessInitiatedPaidQuantity);

        var calculatedUserInitiatedConversationFeePriceMarkup = MoneyExtensions.Add(
            userInitiatedMarkup,
            MoneyExtensions.MultiplyByPercentage(
                new Money(Currencies.Usd, userInitiatedCost),
                markupProfile.UserInitiatedConversationFeePriceMarkupPercentage));

        var calculatedBusinessInitiatedConversationFeePriceMarkup = MoneyExtensions.Add(
            businessInitiatedMarkup,
            MoneyExtensions.MultiplyByPercentage(
                new Money(Currencies.Usd, businessInitiatedCost),
                markupProfile.BusinessInitiatedConversationFeePriceMarkupPercentage));

        return MoneyExtensions.Add(
            calculatedBusinessInitiatedConversationFeePriceMarkup,
            calculatedUserInitiatedConversationFeePriceMarkup);
    }

    public static Money GetCalculatedTransactionHandlingFee(decimal totalCost, MarkupProfile markupProfile)
    {
        return MoneyExtensions.MultiplyByPercentage(
            new Money(Currencies.Usd, totalCost),
            markupProfile.TransactionHandlingFeeRate ?? MarkupProfile.DefaultTransactionHandlingFeeRate);
    }
}