using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.Models.FacebookPageObjects;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IReplyFacebookPostCommentStepExecutor : IStepExecutor
{
}

public class ReplyFacebookPostCommentStepExecutor
    : GeneralStepExecutor<CallStep<ReplyFacebookPostCommentStepArgs>>,
        IReplyFacebookPostCommentStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly HttpClient _httpClient;
    private readonly IStateAggregator _stateAggregator;
    private readonly ICoreCommander _coreCommander;

    public ReplyFacebookPostCommentStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        IHttpClientFactory httpClientFactory,
        IStateAggregator stateAggregator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
        _stateAggregator = stateAggregator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            var facebookPageId =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.FacebookPageIdExpr)
                          ?? callStep.Args.FacebookPageIdExpr);

            var replyToFacebookCommentId =
                (string) (await _stateEvaluator.EvaluateExpressionAsync(
                              state,
                              callStep.Args.FacebookCommentIdExpr)
                          ?? callStep.Args.FacebookCommentIdExpr);

            var commentBodyObj =
                await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.CommentBodyExpr)
                ?? callStep.Args.CommentBodyExpr;

            var getFacebookPageAccessToken = JsonConvert.DeserializeObject<GetFacebookPageAccessTokenOutput>(
                await _coreCommander.ExecuteAsync(
                    state.Origin,
                    "GetFacebookPageAccessToken",
                    await GetArgs(callStep, state)));

            if (getFacebookPageAccessToken == null || string.IsNullOrEmpty(getFacebookPageAccessToken.FacebookPageAccessToken))
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.InternalError,
                    $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id} because of unable to get page {facebookPageId} access token");
            }

            var facebookPageCommentClient = new FacebookPageCommentClient(getFacebookPageAccessToken.FacebookPageAccessToken, _httpClient);

            var replyToCommentResponse = await facebookPageCommentClient.PublishFacebookCommentAsync(
                replyToFacebookCommentId,
                JsonConvert.DeserializeObject<FacebookPagePublishCommentObject>(
                    JsonConvert.SerializeObject(
                        commentBodyObj,
                        JsonConfig.DefaultJsonSerializerSettings),
                    JsonConfig.DefaultJsonSerializerSettings)!);

            var updatedState = await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                step.Id,
                JsonConvert.SerializeObject(replyToCommentResponse));

            await onActivatedAsync(updatedState, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException g)
            {
                throw new SfFlowHubUserFriendlyException(
                    UserFriendlyErrorCodes.MetaGraphApiClientError,
                    $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                    g);
            }

            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    private async Task<GetFacebookPageAccessTokenInput> GetArgs(
        CallStep<ReplyFacebookPostCommentStepArgs> callStep,
        ProxyState state)
    {
        var facebookPageId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.FacebookPageIdExpr)
                      ?? callStep.Args.FacebookPageIdExpr);

        return new GetFacebookPageAccessTokenInput(state.Identity.SleekflowCompanyId, facebookPageId);
    }
}