using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Dynamics365.Subscriptions;

public interface IDynamics365SubscriptionRepository : IRepository<Dynamics365Subscription>
{
}

public class Dynamics365SubscriptionRepository
    : BaseRepository<Dynamics365Subscription>,
        IDynamics365SubscriptionRepository,
        ISingletonService
{
    public Dynamics365SubscriptionRepository(
        ILogger<BaseRepository<Dynamics365Subscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}