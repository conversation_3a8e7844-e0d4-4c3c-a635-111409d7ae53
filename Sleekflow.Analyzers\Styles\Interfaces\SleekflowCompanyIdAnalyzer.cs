using System.Collections.Immutable;
using System.Linq;
using Microsoft.CodeAnalysis;
using Microsoft.CodeAnalysis.Diagnostics;

namespace Sleekflow.Analyzers.Styles;

[DiagnosticAnalyzer(LanguageNames.CSharp)]
public class SleekflowCompanyIdAnalyzer : DiagnosticAnalyzer
{
    public const string DiagnosticId = "SF1004";
    public const string Category = "Design";

    private static readonly LocalizableString Title = "Class should implement IHasSleekflowCompanyId";

    private static readonly LocalizableString MessageFormat =
        "Class '{0}' has a '{1}' property but does not implement the corresponding interface";

    private static readonly LocalizableString Description =
        "If a class has a SleekflowCompanyId property, it should implement the IHasSleekflowCompanyId interface.";

    private static readonly DiagnosticDescriptor Rule = new DiagnosticDescriptor(
        DiagnosticId,
        Title,
        MessageFormat,
        Category,
        DiagnosticSeverity.Warning,
        isEnabledByDefault: true,
        description: Description);

    public override ImmutableArray<DiagnosticDescriptor> SupportedDiagnostics
    {
        get { return ImmutableArray.Create(Rule); }
    }

    public override void Initialize(AnalysisContext context)
    {
        context.ConfigureGeneratedCodeAnalysis(GeneratedCodeAnalysisFlags.None);
        context.EnableConcurrentExecution();
        context.RegisterSymbolAction(AnalyzeSymbol, SymbolKind.NamedType);
    }

    private static void AnalyzeSymbol(SymbolAnalysisContext context)
    {
        var namedTypeSymbol = (INamedTypeSymbol) context.Symbol;

        // Check if the class has a SleekflowCompanyId property
        var sleekflowCompanyIdProperty = namedTypeSymbol.GetMembers()
            .FirstOrDefault(m => m.Kind == SymbolKind.Property && m.Name == "SleekflowCompanyId");
        if (sleekflowCompanyIdProperty == null)
        {
            return;
        }

        // Check if the class already implements IHasSleekflowCompanyId
        var iHasSleekflowCompanyIdInterface = namedTypeSymbol.Interfaces.FirstOrDefault(
            i => i.ToDisplayString() == "Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId");
        if (iHasSleekflowCompanyIdInterface != null)
        {
            return;
        }

        // Create a diagnostic and report it
        var sleekflowCompanyIdDiagnostic = Diagnostic.Create(
            Rule,
            namedTypeSymbol.Locations[0],
            namedTypeSymbol.Name,
            "SleekflowCompanyId");
        context.ReportDiagnostic(sleekflowCompanyIdDiagnostic);
    }
}