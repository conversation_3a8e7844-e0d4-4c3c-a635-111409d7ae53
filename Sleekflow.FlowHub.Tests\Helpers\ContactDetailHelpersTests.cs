﻿using Sleekflow.FlowHub.Commons.Helpers;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.Internals;

namespace Sleekflow.FlowHub.Tests.Helpers;

[TestFixture]
public class ContactDetailHelpersTests
{
    [Test]
    public void GetPropertiesAsDictionary_WithAllPropertiesSet_ReturnsDictionaryWithCorrectObjects()
    {
        // Arrange
        var contactData = new Dictionary<string, object?> { { "id", "contact1" }, { "email", "<EMAIL>" } };
        var ownerData = new Dictionary<string, object?> { { "id", "owner1" }, { "name", "Admin User" } };
        var listArray = new ContactList[]
        {
            new ContactList("list1", "Leads", DateTimeOffset.UtcNow.AddDays(-1), false),
            new ContactList("list2", "Customers", DateTimeOffset.UtcNow, true)
        };
        var conversationData = new ContactConversation(
            conversationStatus: "open",
            conversationId: "conv123",
            lastMessageChannel: "whatsapp",
            lastMessageChannelId: "channel_xyz");

        var contactDetail = new ContactDetail(
            contact: contactData,
            contactOwner: ownerData,
            listArray,
            conversationData);

        // Expected dictionary maps camelCase property names to the *actual objects*
        var expected = new Dictionary<string, object>
        {
            { "contact", contactData },
            { "contactOwner", ownerData },
            { "lists", listArray },
            { "conversation", conversationData }
        };

        // Act
        var result = ContactDetailHelpers.GetPropertiesAsDictionary(contactDetail);

        // Assert
        Assert.That(result, Is.EquivalentTo(expected), "Dictionary content should match expected");
        Assert.That(result["contact"], Is.SameAs(contactData), "contact object instance should be the same");
        Assert.That(result["contactOwner"], Is.SameAs(ownerData), "contactOwner object instance should be the same");
        Assert.That(result["lists"], Is.SameAs(listArray), "lists array instance should be the same");
        Assert.That(result["conversation"], Is.SameAs(conversationData), "conversation object instance should be the same");
        Assert.That(result.Count, Is.EqualTo(4), "Should contain all 4 properties");
    }


    [Test]
    public void GetPropertiesAsDictionary_WithNullOptionalProperty_ExcludesNullProperty()
    {
        // Arrange
        var contactData = new Dictionary<string, object?> { { "id", "contact2" } };
        // ContactOwner is nullable in ContactDetail definition, pass null to constructor
        Dictionary<string, object?>? ownerData = null;
        var listArray = new ContactList[] { }; // Empty array, but not null
        var conversationData = new ContactConversation("closed", "conv456", null, null); // Minimal conversation

        var contactDetail = new ContactDetail(
            contact: contactData,
            contactOwner: ownerData, // Passing null here
            lists: listArray,
            conversation: conversationData);

        // Expected dictionary should only contain the non-null properties
        var expected = new Dictionary<string, object>
        {
            { "contact", contactData },
            // contactOwner should be absent
            { "lists", listArray },
            { "conversation", conversationData }
        };

        // Act
        var result = ContactDetailHelpers.GetPropertiesAsDictionary(contactDetail);

        // Assert
        Assert.That(result, Is.EquivalentTo(expected), "Dictionary content should match expected");
        Assert.That(result.ContainsKey("contactOwner"), Is.False, "contactOwner should be excluded when null");
        Assert.That(result.Count, Is.EqualTo(3), "Should contain only the 3 non-null properties");
        Assert.That(result["contact"], Is.SameAs(contactData));
        Assert.That(result["lists"], Is.SameAs(listArray));
        Assert.That(result["conversation"], Is.SameAs(conversationData));
    }

    [Test]
    public void GetPropertiesAsDictionary_WithNullRequiredProperty_ExcludesNullProperty()
    {
        // Arrange
        // Test the helper's behavior if it encounters a ContactDetail where a required
        // property's value *is* null (e.g., Contact, Conversation).
        Dictionary<string, object?>? contactData = null; // Null value for the 'Contact' property
        var ownerData = new Dictionary<string, object?> { { "id", "owner3" } };
        var listArray = new ContactList[]
        {
             new ContactList("list3", "Archive", DateTimeOffset.UtcNow, false)
        };

        // Simulate Conversation property being null *after* object creation
        ContactConversation? conversationData = null;

        // Create a valid object first, then set properties to null if constructor requires non-null
        var contactDetail = new ContactDetail(
            new Dictionary<string, object?>
            {
                {
                    "id", "temp"
                }
            }, // Temporary valid dictionary for Contact constructor arg
            ownerData,
            listArray,
            new ContactConversation("temp_status", "temp_id", null, null)); // Temporary valid object for Conversation constructor arg

        // Now set the actual properties to null *after* construction
        contactDetail.Contact = contactData!;
        contactDetail.Conversation = conversationData!;

        var expected = new Dictionary<string, object>
        {
             // contact should be absent
            { "contactOwner", ownerData },
            { "lists", listArray }

             // conversation should be absent
        };

        // Act
        var result = ContactDetailHelpers.GetPropertiesAsDictionary(contactDetail);

        // Assert
        Assert.That(result, Is.EquivalentTo(expected), "Dictionary content should match expected");
        Assert.That(result.ContainsKey("contact"), Is.False, "contact should be excluded when null");
        Assert.That(result.ContainsKey("conversation"), Is.False, "conversation should be excluded when null");
        Assert.That(result.Count, Is.EqualTo(2), "Should contain only the 2 non-null properties");
        Assert.That(result["contactOwner"], Is.SameAs(ownerData));
        Assert.That(result["lists"], Is.SameAs(listArray));
    }

    [Test]
    public void GetPropertiesAsDictionary_WithEmptyNonNullProperties_IncludesProperties()
    {
        // Arrange
        // Test case where properties are initialized but logically "empty" (e.g., empty dict/array)
        var contactData = new Dictionary<string, object?>(); // Empty dictionary, but not null
        var ownerData = new Dictionary<string, object?>(); // Empty dictionary, but not null
        var listArray = new ContactList[] { }; // Empty array, but not null
        var conversationData = new ContactConversation("new", "conv789", null, null); // Valid "empty" conversation

        var contactDetail = new ContactDetail(
            contact: contactData,
            contactOwner: ownerData,
            lists: listArray,
            conversation: conversationData);

        // Expected dictionary should include all properties because their values are not null
        var expected = new Dictionary<string, object>
        {
            { "contact", contactData },
            { "contactOwner", ownerData },
            { "lists", listArray },
            { "conversation", conversationData }
        };

        // Act
        var result = ContactDetailHelpers.GetPropertiesAsDictionary(contactDetail);

        // Assert
        Assert.That(result, Is.EquivalentTo(expected), "Dictionary content should match expected");
        Assert.That(result.Count, Is.EqualTo(4), "Should contain all 4 properties even if 'empty'");
        Assert.That(result["contact"], Is.SameAs(contactData));
        Assert.That(result["contactOwner"], Is.SameAs(ownerData));
        Assert.That(result["lists"], Is.SameAs(listArray));
        Assert.That(result["conversation"], Is.SameAs(conversationData));
    }
}