﻿using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileAddedToListLog : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateUserProfileAddedToListLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateUserProfileAddedToListLogInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [Validations.ValidateArray]
        [JsonProperty("sleekflow_user_profile_ids")]
        public List<string> SleekflowUserProfileIds { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public UserProfileAddedToListLogData Data { get; set; }

        [JsonConstructor]
        public CreateUserProfileAddedToListLogInput(
            string? sleekflowCompanyId,
            List<string> sleekflowUserProfileIds,
            string? sleekflowStaffId,
            string auditLogText,
            UserProfileAddedToListLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileIds = sleekflowUserProfileIds;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class CreateUserProfileAddedToListLogOutput
    {
        [JsonProperty("ids")]
        public List<string> Ids { get; set; }

        [JsonConstructor]
        public CreateUserProfileAddedToListLogOutput(List<string> ids)
        {
            Ids = ids;
        }
    }

    public async Task<CreateUserProfileAddedToListLogOutput> F(
        CreateUserProfileAddedToListLogInput createUserProfileAddedToListLogInput)
    {
        var dataStr = JsonConvert.SerializeObject(createUserProfileAddedToListLogInput.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var ids = new ConcurrentBag<string>();
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await Parallel.ForEachAsync(
            createUserProfileAddedToListLogInput.SleekflowUserProfileIds,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 20
            },
            async (userProfileId, cancellationToken) =>
            {
                var id = _idService.GetId("UserProfileAuditLog");
                await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
                    new UserProfileAuditLog(
                        id,
                        (distributedInvocationContext?.SleekflowCompanyId
                         ?? createUserProfileAddedToListLogInput.SleekflowCompanyId)
                        ?? throw new InvalidOperationException(),
                        distributedInvocationContext?.SleekflowStaffId
                        ?? createUserProfileAddedToListLogInput.SleekflowStaffId,
                        userProfileId,
                        UserProfileAuditLogTypes.UserProfileAddedToList,
                        createUserProfileAddedToListLogInput.AuditLogText,
                        data,
                        DateTimeOffset.UtcNow,
                        null),
                    cancellationToken);
                ids.Add(id);
            });

        return new CreateUserProfileAddedToListLogOutput(ids.ToList());
    }
}