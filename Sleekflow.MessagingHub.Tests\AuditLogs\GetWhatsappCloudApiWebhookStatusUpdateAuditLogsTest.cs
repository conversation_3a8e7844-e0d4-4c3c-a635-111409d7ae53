using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Audits;
using Sleekflow.MessagingHub.Triggers.AuditLogs.WhatsappCloudApiWebhookStatusUpdateAuditLogs;
using Sleekflow.Outputs;

namespace Sleekflow.MessagingHub.Tests.AuditLogs;

public class GetWhatsappCloudApiWebhookStatusUpdateAuditLogsTest
{
    [TestCase("n2UmK4v4bk8pde", "166367663226916", "b6d7e442-38ae-4b9a-b100-2951729768bc", null, 0, 10)]
    [TestCase(
        "n2UmK4v4bk8pde",
        "166367663226916",
        "b6d7e442-38ae-4b9a-b100-2951729768bc",
        @"{""facebook_phone_number_id"": ""148887004982567"", ""facebook_message_template_id"": ""779636234103463""}",
        0,
        10)]
    public async Task Test_GetWhatsappCloudApiWebhookStatusUpdateAuditLogsFromWabaId(
        string wabaId,
        string facebookWabaId,
        string sleekflowCompanyId,
        string? filters,
        int offset,
        int limit)
    {
        // /AuditLogs/GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs
        var getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput =
            new GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs.
                GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput(
                    sleekflowCompanyId,
                    wabaId,
                    !string.IsNullOrEmpty(filters)
                        ? JsonConvert.DeserializeObject<WhatsappCloudApiWebhookStatusUpdateAuditLogsFilters>(
                            filters)
                        : null,
                    offset,
                    limit);

        var getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsResults = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Key", "a12da7c775d00cada5b1ee611d3f6dca");

                _.Post.Json(getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsInput)
                    .ToUrl("/AuditLogs/GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs");
            });

        var getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput =
            await getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsResults
                .ReadAsJsonAsync<Output<GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogs.
                    GetWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput>>();

        Assert.That(getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput, Is.Not.Null);

        Assert.That(
            getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput.Data
                .WhatsappCloudApiWabaWebhookStatusUpdateAuditLogs,
            Is.Not.Null);

        Assert.Multiple(
            () =>
            {
                Assert.That(
                    getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput.Data
                        .WhatsappCloudApiWabaWebhookStatusUpdateAuditLogs,
                    Is.Not.Empty);

                Assert.That(
                    getWhatsappCloudApiWabaWebhookStatusUpdateAuditLogsOutput.Data
                        .WhatsappCloudApiWabaWebhookStatusUpdateAuditLogs
                        .TrueForAll(x => x.FacebookWabaId == facebookWabaId));
            });
    }
}