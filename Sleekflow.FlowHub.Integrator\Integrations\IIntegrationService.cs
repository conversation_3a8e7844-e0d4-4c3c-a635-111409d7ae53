﻿using System.Linq.Expressions;
using Sleekflow.FlowHub.Models.Integrations;

namespace Sleekflow.FlowHub.Integrator.Integrations;

public interface IIntegrationService<T>
where T : IIntegration
{
    Task<T> GetIntegrationAsync(string sleekflowCompanyId, string id);

    Task<List<T>> GetIntegrationsAsync(Expression<Func<T, bool>> predicate, int? limit = 1, CancellationToken cancellationToken = default);

    Task<T> CreateIntegrationAsync(T integration, string sleekflowCompanyId);

    Task DeleteIntegrationAsync(string sleekflowCompanyId, string id);

    IIntegrationRepository<T> GetRepository();
}

public abstract class IntegrationServiceBase<T> : IIntegrationService<T>
where T : IIntegration
{
    private readonly IIntegrationRepository<T> _zapierTriggerIntegrationRepository;

    protected IntegrationServiceBase(IIntegrationRepository<T> zapierTriggerIntegrationRepository)
    {
        _zapierTriggerIntegrationRepository = zapierTriggerIntegrationRepository;
    }

    public virtual async Task<T> GetIntegrationAsync(string sleekflowCompanyId, string id)
    {
        return await _zapierTriggerIntegrationRepository.GetAsync(id, sleekflowCompanyId);
    }

    public virtual async Task<List<T>> GetIntegrationsAsync(Expression<Func<T, bool>> predicate, int? limit = 1, CancellationToken cancellationToken = default)
    {
        return await _zapierTriggerIntegrationRepository.GetObjectsAsync(predicate, limit, cancellationToken);
    }

    public virtual async Task<T> CreateIntegrationAsync(T integration, string sleekflowCompanyId)
    {
        return await _zapierTriggerIntegrationRepository.CreateAndGetAsync(integration, sleekflowCompanyId);
    }

    public virtual async Task DeleteIntegrationAsync(string sleekflowCompanyId, string id)
    {
        await _zapierTriggerIntegrationRepository.DeleteAsync(id, sleekflowCompanyId);
    }

    public IIntegrationRepository<T> GetRepository()
    {
        return _zapierTriggerIntegrationRepository;
    }
}