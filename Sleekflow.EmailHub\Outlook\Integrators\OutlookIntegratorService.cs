using MassTransit;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Outlook.Authentications;
using Sleekflow.EmailHub.Outlook.Communications;
using Sleekflow.EmailHub.Outlook.Subscriptions;
using Sleekflow.EmailHub.Providers;
using Sleekflow.EmailHub.Repositories;

namespace Sleekflow.EmailHub.Outlook.Integrators;

public interface IOutlookIntegratorService : IEmailService
{
}

public class OutlookIntegratorService : EmailService, IOutlookIntegratorService, IScopedService
{
    public const string EmailProviderName = ProviderNames.Outlook;

    public OutlookIntegratorService(
        IOutlookAuthenticationService outlookAuthenticationService,
        IOutlookSubscriptionService outlookSubscriptionService,
        IOutlookCommunicationService outlookCommunicationService,
        IEmailRepository emailRepository,
        IBus bus,
        IProviderConfigService providerConfigService)
        : base(
        EmailProviderName,
        outlookAuthenticationService,
        outlookSubscriptionService,
        outlookCommunicationService,
        emailRepository,
        bus,
        providerConfigService)
    {
    }
}