using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Persistence.TenantHubDb;

public interface ITenantHubDbConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DatabaseId { get; }

    string SfLocation { get; }
}

public class TenantHubDbConfig : IConfig, ITenantHubDbConfig
{
    public string Endpoint { get; private set; }

    public string Key { get; private set; }

    public string DatabaseId { get; private set; }

    public string SfLocation { get; private set; }

    public TenantHubDbConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("COSMOS_TENANT_HUB_DB_ENDPOINT", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_TENANT_HUB_DB_ENDPOINT");
        Key =
            Environment.GetEnvironmentVariable("COSMOS_TENANT_HUB_DB_KEY", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_TENANT_HUB_DB_KEY");
        DatabaseId =
            Environment.GetEnvironmentVariable("COSMOS_TENANT_HUB_DB_DATABASE_ID", target) ??
            throw new SfMissingEnvironmentVariableException("COSMOS_TENANT_HUB_DB_DATABASE_ID");
        SfLocation = Environment.GetEnvironmentVariable("SF_LOCATION", target) ??
                     throw new SfMissingEnvironmentVariableException("SF_LOCATION");
    }
}