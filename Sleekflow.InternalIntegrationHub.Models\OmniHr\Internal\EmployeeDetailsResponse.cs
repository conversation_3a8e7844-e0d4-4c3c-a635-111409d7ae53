using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;

public class EmployeeDetailsResponse
{
    [JsonConstructor]
    public EmployeeDetailsResponse(
        int id,
        int systemId,
        string firstName,
        string lastName,
        PrimaryEmail primaryEmail,
        string locationName,
        string position,
        string department,
        string country,
        int? currentManagerHistorical)
    {
        Id = id;
        SystemId = systemId;
        FirstName = firstName;
        LastName = lastName;
        PrimaryEmail = primaryEmail;
        LocationName = locationName;
        Position = position;
        Department = department;
        Country = country;
        CurrentManagerHistorical = currentManagerHistorical;
    }

    [JsonProperty(PropertyName = "id", NullValueHandling = NullValueHandling.Ignore)]
    public int Id { get; set; }

    [JsonProperty(PropertyName = "system_id", NullValueHandling = NullValueHandling.Ignore)]
    public int SystemId { get; set; }

    [JsonProperty(PropertyName = "first_name", NullValueHandling = NullValueHandling.Ignore)]
    public string FirstName { get; set; }

    [JsonProperty(PropertyName = "last_name", NullValueHandling = NullValueHandling.Ignore)]
    public string LastName { get; set; }

    [JsonProperty(PropertyName = "primary_email", NullValueHandling = NullValueHandling.Ignore)]
    public PrimaryEmail PrimaryEmail { get; set; }

    [JsonProperty(PropertyName = "location_name", NullValueHandling = NullValueHandling.Ignore)]
    public string LocationName { get; set; }

    [JsonProperty(PropertyName = "position", NullValueHandling = NullValueHandling.Ignore)]
    public string Position { get; set; }

    [JsonProperty(PropertyName = "department", NullValueHandling = NullValueHandling.Ignore)]
    public string Department { get; set; }

    [JsonProperty(PropertyName = "country", NullValueHandling = NullValueHandling.Ignore)]
    public string Country { get; set; }

    [JsonProperty(PropertyName = "current_manager_historical", NullValueHandling = NullValueHandling.Ignore)]
    public int? CurrentManagerHistorical { get; set; }
}

public class PrimaryEmail
{
    [JsonProperty(PropertyName = "id", NullValueHandling = NullValueHandling.Ignore)]
    public int? Id { get; set; }

    [JsonProperty(PropertyName = "value", NullValueHandling = NullValueHandling.Ignore)]
    public string? Value { get; set; }

    [JsonProperty(PropertyName = "is_primary", NullValueHandling = NullValueHandling.Ignore)]
    public bool? IsPrimary { get; set; }

    [JsonConstructor]
    public PrimaryEmail(int? id, string? value, bool? isPrimary)
    {
        Id = id;
        Value = value;
        IsPrimary = isPrimary;
    }

    public PrimaryEmail(string value)
    {
        Value = value;
    }
}