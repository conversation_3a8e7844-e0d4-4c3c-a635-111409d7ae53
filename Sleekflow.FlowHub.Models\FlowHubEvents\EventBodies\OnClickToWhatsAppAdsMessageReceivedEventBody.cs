﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class OnClickToWhatsAppAdsMessageReceivedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnClickToWhatsAppAdsMessageReceived; }
    }

    [Required]
    [JsonProperty("message")]
    public OnClickToWhatsAppAdsMessageReceivedEventBodyMessage Message { get; set; }

    [JsonProperty("channel")]
    public string Channel { get; set; }

    [Required]
    [JsonProperty("channel_id")]
    public string ChannelId { get; set; }

    [Required]
    [JsonProperty("conversation_id")]
    public string ConversationId { get; set; }

    [Required]
    [JsonProperty("message_id")]
    public string MessageId { get; set; }

    [JsonProperty("message_unique_id")]
    public string MessageUniqueId { get; set; }

    [Required]
    [JsonProperty("contact_id")]
    public string ContactId { get; set; }

    [Required]
    [JsonProperty("contact")]
    public Dictionary<string, object?> Contact { get; set; }

    [Required]
    [JsonProperty("is_new_contact")]
    public bool IsNewContact { get; set; }

    [JsonProperty("is_sent_from_sleekflow")]
    public bool IsSentFromSleekflow => false;

    [JsonConstructor]
    public OnClickToWhatsAppAdsMessageReceivedEventBody(
        DateTimeOffset createdAt,
        OnClickToWhatsAppAdsMessageReceivedEventBodyMessage message,
        string channel,
        string channelId,
        string conversationId,
        string messageId,
        string messageUniqueId,
        string contactId,
        Dictionary<string, object?> contact,
        bool isNewContact)
        : base(createdAt)
    {
        Message = message;
        Channel = channel;
        ChannelId = channelId;
        ConversationId = conversationId;
        MessageId = messageId;
        MessageUniqueId = messageUniqueId;
        ContactId = contactId;
        Contact = contact;
        IsNewContact = isNewContact;
    }
}

public class OnClickToWhatsAppAdsMessageReceivedEventBodyMessage : BaseMessage
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("ctwa_clid")]
    public string CtwaClid { get; set; }

    [JsonProperty("source_id")]
    public string SourceId { get; set; }

    [JsonProperty("source_url")]
    public string SourceUrl { get; set; }

    [JsonProperty("headline")]
    public string Headline { get; set; }

    [JsonProperty("body")]
    public string Body { get; set; }

    [JsonConstructor]
    public OnClickToWhatsAppAdsMessageReceivedEventBodyMessage(
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        MessageBody messageBody,
        string messageType,
        string id,
        string messageContent,
        string messageStatus,
        string messageDeliveryType,
        string ctwaClid,
        string sourceId,
        string sourceUrl,
        string headline,
        string body)
        : base(
            createdAt,
            updatedAt,
            messageBody,
            messageType,
            messageContent,
            messageStatus,
            messageDeliveryType)
    {
        CtwaClid = ctwaClid;
        SourceId = sourceId;
        SourceUrl = sourceUrl;
        Id = id;
        MessageContent = messageContent;
        Headline = headline;
        Body = body;
    }
}