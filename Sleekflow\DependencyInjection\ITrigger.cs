﻿namespace Sleekflow.DependencyInjection;

/// <summary>
/// Base marker interface for API triggers in the Sleekflow architecture.
/// </summary>
/// <remarks>
/// <para>
/// Triggers are classes that expose API endpoints or handle webhook invocations.
/// Classes implementing this interface are automatically registered in the dependency injection
/// container and can be discovered for API route registration.
/// </para>
/// <para>
/// This base interface should be implemented by non-generic triggers or
/// used as a constraint for more specific trigger interfaces.
/// </para>
/// </remarks>
public interface ITrigger
{
}

/// <summary>
/// Generic interface for API triggers that handle input of type <typeparamref name="TInput"/>
/// and produce output of type <typeparamref name="TOutput"/>.
/// </summary>
/// <typeparam name="TInput">The type of the input data the trigger accepts.</typeparam>
/// <typeparam name="TOutput">The type of the output data the trigger produces.</typeparam>
/// <remarks>
/// <para>
/// This interface extends the base <see cref="ITrigger"/> interface with a typed operation method.
/// It is designed for creating triggers that handle specific input/output type pairs.
/// </para>
/// <para>
/// Implementers should provide the processing logic in the <see cref="F"/> method,
/// which typically represents the main operation the trigger performs.
/// </para>
/// </remarks>
public interface ITrigger<in TInput, TOutput> : ITrigger
{
    /// <summary>
    /// Processes the input data and produces an output result.
    /// </summary>
    /// <param name="input">The input data to process.</param>
    /// <returns>The processing result of type <typeparamref name="TOutput"/>.</returns>
    Task<TOutput> F(TInput input);
}