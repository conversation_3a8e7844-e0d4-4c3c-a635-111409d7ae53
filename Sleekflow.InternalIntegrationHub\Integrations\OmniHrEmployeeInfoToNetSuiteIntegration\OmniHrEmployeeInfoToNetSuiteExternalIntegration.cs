using AutoMapper;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Integrations;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Internal;
using Sleekflow.InternalIntegrationHub.NetSuite;
using Sleekflow.InternalIntegrationHub.OmniHr;
using Sleekflow.InternalIntegrationHub.Utils;
using EmployeeDetailsResponse = Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.EmployeeDetailsResponse;
using Supervisor = Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations.Supervisor;

namespace Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration;

public interface IOmniHrEmployeeInfoToNetSuiteExternalIntegration : IExternalIntegration
{
}

public class OmniHrEmployeeInfoToNetSuiteExternalIntegration
    : IOmniHrEmployeeInfoToNetSuiteExternalIntegration, IScopedService
{
    private readonly IOmniHrIntegrationService _omniHrIntegrationService;
    private readonly INetSuiteSettingService _netSuiteSettingService;
    private readonly INetSuiteEmployeeService _netSuiteEmployeeService;
    private readonly IMapper _mapper;
    private readonly ILogger<OmniHrEmployeeInfoToNetSuiteExternalIntegration> _logger;

    public OmniHrEmployeeInfoToNetSuiteExternalIntegration(
        IOmniHrIntegrationService omniHrIntegrationService,
        INetSuiteSettingService netSuiteSettingService,
        INetSuiteEmployeeService netSuiteEmployeeService,
        IMapper mapper,
        ILogger<OmniHrEmployeeInfoToNetSuiteExternalIntegration> logger)
    {
        _omniHrIntegrationService = omniHrIntegrationService;
        _netSuiteSettingService = netSuiteSettingService;
        _netSuiteEmployeeService = netSuiteEmployeeService;
        _mapper = mapper;
        _logger = logger;
    }

    // Required info
    // - First name
    // - Last name
    // - Compensation currency
    // - Location
    // - Manager
    //
    // Step:
    // 1. Get subsidiary list from NetSuite (done)
    // 2. Get currency list from NetSuite (done)
    // 3. Get employees from OmniHr (done)
    // 4. Get employees from NetSuite (done)
    // 5. Diff the employees from OmniHr and NetSuite
    // 6. If there is a new employee in OmniHr, sync to NetSuite
    // 7. If there is change in employee info in OmniHr, sync to NetSuite
    public async Task Run(CancellationToken cancellationToken)
    {
        var (subsidiaryDetails, currencyDetails, localNetSuiteEmployeeList, termDetails) =
            await _netSuiteSettingService.NetSuitePreparation(
                cancellationToken,
                showTerm: false,
                forceRefreshEmployee: true);

        var employees = await OmniHrPreparation();

        if (employees == null)
        {
            _logger.LogError("Failed to get employees from OmniHr");
            return;
        }

        await SyncEmployeeToNetSuite(
            employees,
            subsidiaryDetails,
            currencyDetails,
            localNetSuiteEmployeeList,
            cancellationToken);

        await SyncManagersToNetSuite(employees, cancellationToken);
    }

    private async Task<List<OmniHrEmployee>?> OmniHrPreparation()
    {
        // Get employees from OmniHr
        return await _omniHrIntegrationService.GetEmployeesAsync();
    }

    private async Task SyncEmployeeToNetSuite(
        List<OmniHrEmployee> employees,
        List<SubsidiaryDetailsResponse> subsidiaryDetails,
        List<CurrencyDetailsResponse> currencyDetails,
        List<EmployeeDetailsResponse> localNetSuiteEmployeeList,
        CancellationToken cancellationToken)
    {
        // Sync employee info to NetSuite without manager info
        await Parallel.ForEachAsync(
            employees,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 3, CancellationToken = cancellationToken,
            },
            async (employee, _) =>
            {
                _logger.LogInformation(
                    "Syncing employee info to NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}",
                    employee.Id,
                    employee.FirstName,
                    employee.LastName);
                var shouldRunPatch =
                    localNetSuiteEmployeeList.FirstOrDefault(
                        e => string.Equals(e?.ExternalId, employee.SystemId.ToString()),
                        null);
                if (shouldRunPatch != null)
                {
                    var netSuiteEmployee = shouldRunPatch;
                    var localNetSuiteEmployee =
                        _mapper.Map<EmployeeDetailsResponse, FullEmployeeInfo>(netSuiteEmployee!);
                    localNetSuiteEmployee.LocationName =
                        subsidiaryDetails.FirstOrDefault(
                            e => string.Equals(e?.Name, localNetSuiteEmployee.LocationName),
                            null)?.Id;

                    if (localNetSuiteEmployee.LocationName == null)
                    {
                        _logger.LogError(
                            "Failed to sync employee info to NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}, Reason: Location not found",
                            employee.Id,
                            employee.FirstName,
                            employee.LastName);
                        return;
                    }

                    localNetSuiteEmployee.Currency =
                        currencyDetails.FirstOrDefault(e => string.Equals(e?.Id, localNetSuiteEmployee.Currency), null)
                            ?.Id;

                    if (localNetSuiteEmployee.Currency == null)
                    {
                        _logger.LogError(
                            "Failed to sync employee info to NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}, Reason: Currency not found",
                            employee.Id,
                            employee.FirstName,
                            employee.LastName);
                        return;
                    }

                    localNetSuiteEmployee.PrimaryEmail = netSuiteEmployee.Email == null
                        ? null
                        : new OmniHrEmail(netSuiteEmployee.Email);

                    var id = employee.Id;
                    var fullEmployeeInfo = new FullEmployeeInfo
                    {
                        ExternalId = employee.SystemId.ToString(),
                        FirstName = employee.FirstName,
                        LastName = employee.LastName,
                        PrimaryEmail = employee.PrimaryEmail,
                        LocationName = OmniHrUtil.LocationConverter(employee.LocationName, subsidiaryDetails),
                    };
                    var compensationCurrency = await _omniHrIntegrationService.GetCompensationCurrencyAsync(id);
                    if (compensationCurrency == null)
                    {
                        _logger.LogError(
                            "Failed to sync employee info to NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}, Reason: Compensation currency not found",
                            employee.Id,
                            employee.FirstName,
                            employee.LastName);
                        return;
                    }

                    fullEmployeeInfo.Currency = OmniHrUtil.CurrencyConverter(compensationCurrency, currencyDetails);

                    if (!fullEmployeeInfo.Equals(localNetSuiteEmployee))
                    {
                        var updateRequest = _mapper.Map<FullEmployeeInfo, CreateEmployeeRequest>(fullEmployeeInfo);
                        var isSyncToNetSuiteSuccessfully =
                            await _netSuiteEmployeeService.UpdateEmployeeAsync(updateRequest, netSuiteEmployee!.Id!);
                        _logger.LogDebug(
                            "Update NetSuite Employee: {UpdateRequest}",
                            JsonConvert.SerializeObject(updateRequest));
                        if (isSyncToNetSuiteSuccessfully)
                        {
                            _logger.LogInformation(
                                "Employee info updated in NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}",
                                employee.Id,
                                employee.FirstName,
                                employee.LastName);
                        }
                        else
                        {
                            _logger.LogError(
                                "Failed to update employee info in NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}",
                                employee.Id,
                                employee.FirstName,
                                employee.LastName);
                        }
                    }
                }
                else
                {
                    var id = employee.Id;
                    var fullEmployeeInfo = new FullEmployeeInfo
                    {
                        ExternalId = employee.SystemId.ToString(),
                        FirstName = employee.FirstName,
                        LastName = employee.LastName,
                        PrimaryEmail = employee.PrimaryEmail,
                        LocationName = OmniHrUtil.LocationConverter(employee.LocationName, subsidiaryDetails),
                    };

                    var compensationCurrency = await _omniHrIntegrationService.GetCompensationCurrencyAsync(id);
                    if (compensationCurrency == null)
                    {
                        // skip employee without compensation currency
                        _logger.LogError(
                            "Failed to sync employee info to NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}, Reason: Compensation currency not found",
                            employee.Id,
                            employee.FirstName,
                            employee.LastName);
                        return;
                    }

                    fullEmployeeInfo.Currency = OmniHrUtil.CurrencyConverter(compensationCurrency, currencyDetails);

                    var netSuiteEmployee = _mapper.Map<FullEmployeeInfo, CreateEmployeeRequest>(fullEmployeeInfo);
                    netSuiteEmployee.IsSalesRep = employee.Department == "Sales";
                    var isSyncToNetSuiteSuccessfully =
                        await _netSuiteEmployeeService.CreateEmployeeAsync(netSuiteEmployee);
                    _logger.LogDebug(
                        "Create NetSuite Employee: {NetSuiteEmployee}",
                        JsonConvert.SerializeObject(netSuiteEmployee));

                    if (isSyncToNetSuiteSuccessfully)
                    {
                        _logger.LogInformation(
                            "Employee info created in NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}",
                            employee.Id,
                            employee.FirstName,
                            employee.LastName);
                    }
                    else
                    {
                        _logger.LogError(
                            "Failed to create employee info in NetSuite: {EmployeeId} {EmployeeFirstName} {EmployeeLastName}",
                            employee.Id,
                            employee.FirstName,
                            employee.LastName);
                    }
                }
            });
    }


    private async Task SyncManagersToNetSuite(
        List<OmniHrEmployee> employees,
        CancellationToken cancellationToken)
    {
        List<EmployeeDetailsResponse> localNetSuiteEmployeeList = [];
        var netSuiteEmployeeList = await _netSuiteEmployeeService.GetEmployeeAsync(true);

        await Parallel.ForEachAsync(
            netSuiteEmployeeList.Items.Select(e => e.Id),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 3, CancellationToken = cancellationToken,
            },
            async (id, _) =>
            {
                _logger.LogInformation("NetSuite Employee ID: {Id}", id);
                var netSuiteEmployee =
                    await _netSuiteEmployeeService.GetEmployeeDetailsAsync(id, true);
                localNetSuiteEmployeeList.Add(netSuiteEmployee);
            });

        // Sync manager info to employees
        await Parallel.ForEachAsync(
            localNetSuiteEmployeeList,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 3
            },
            async (netSuiteEmployee, _) =>
            {
                if (netSuiteEmployee.Supervisor == null)
                {
                    // Try to create employee manager info in NetSuite
                    var omniEmployee = employees.FirstOrDefault(
                        e => string.Equals(e?.SystemId.ToString(), netSuiteEmployee.ExternalId),
                        null);
                    if (omniEmployee != null)
                    {
                        // get manager info
                        var userDataInfo =
                            await _omniHrIntegrationService.GetEmployeeDetailsAsync(omniEmployee.SystemId);
                        if (userDataInfo.CurrentManagerHistorical == null)
                        {
                            return;
                        }

                        _logger.LogInformation("Start update manager info for employee: {EmployeeId}", omniEmployee.Id);
                        var omniHrManager = await _omniHrIntegrationService.GetManagerHistoryAsync(
                            omniEmployee.Id,
                            userDataInfo.CurrentManagerHistorical.Value);
                        if (omniHrManager.ManagerData != null)
                        {
                            var managerId =
                                localNetSuiteEmployeeList.Find(
                                    e => e.Email == omniHrManager.ManagerData?.PrimaryEmail.Value);
                            var supervisor = new Supervisor(managerId?.Id);
                            var updateEmployee = new CreateEmployeeRequest(supervisor: supervisor);
                            var isUpdateSuccessfully =
                                await _netSuiteEmployeeService.UpdateEmployeeAsync(
                                    updateEmployee,
                                    netSuiteEmployee.Id!);
                            if (isUpdateSuccessfully)
                            {
                                _logger.LogInformation(
                                    "Manager info updated for employee: {EmployeeId}, manager: {ManagerId}",
                                    omniEmployee.Id,
                                    managerId?.Id);
                            }
                            else
                            {
                                _logger.LogError(
                                    "Failed to update manager info for employee: {EmployeeId}, manager: {ManagerId}",
                                    omniEmployee.Id,
                                    managerId?.Id);
                            }
                        }
                    }
                }
                else
                {
                    // Try update employee manager info in NetSuite
                    var mangerInfo = localNetSuiteEmployeeList.FirstOrDefault(
                        e => string.Equals(e?.Id, netSuiteEmployee.Supervisor.Id),
                        null);
                    var omniEmployee = employees.FirstOrDefault(
                        e => string.Equals(e?.SystemId.ToString(), netSuiteEmployee.ExternalId),
                        null);
                    if (omniEmployee != null)
                    {
                        _logger.LogInformation("Start update manager info for employee: {EmployeeId}", omniEmployee.Id);
                        var userDataInfo =
                            await _omniHrIntegrationService.GetEmployeeDetailsAsync(omniEmployee.SystemId);
                        if (userDataInfo.CurrentManagerHistorical == null)
                        {
                            // remove manager info in NetSuite
                            var supervisor = new Supervisor(null);
                            var updateEmployee = new CreateEmployeeRequest(supervisor: supervisor);
                            var isUpdateSuccessfully =
                                await _netSuiteEmployeeService.UpdateEmployeeAsync(
                                    updateEmployee,
                                    netSuiteEmployee.Id!);
                            if (isUpdateSuccessfully)
                            {
                                _logger.LogInformation(
                                    "Manager info removed for employee: {EmployeeId}",
                                    omniEmployee.Id);
                            }
                            else
                            {
                                _logger.LogError(
                                    "Failed to remove manager info for employee: {EmployeeId}",
                                    omniEmployee.Id);
                            }

                            return;
                        }

                        var omniHrManager = await _omniHrIntegrationService.GetManagerHistoryAsync(
                            omniEmployee.Id,
                            userDataInfo.CurrentManagerHistorical.Value);

                        var netSuiteEmployeeManagerFromOmniHr = localNetSuiteEmployeeList.Find(
                            e => e.ExternalId == omniHrManager.ManagerData?.SystemId.ToString());

                        if (netSuiteEmployeeManagerFromOmniHr?.ExternalId != mangerInfo?.ExternalId)
                        {
                            // update manager info in NetSuite
                            var managerId =
                                localNetSuiteEmployeeList.FirstOrDefault(
                                    e => string.Equals(e?.Email, omniHrManager.ManagerData?.PrimaryEmail.Value),
                                    null);
                            var supervisor = new Supervisor(managerId?.Id);
                            var updateEmployee = new CreateEmployeeRequest(supervisor: supervisor);
                            var isUpdateSuccessfully =
                                await _netSuiteEmployeeService.UpdateEmployeeAsync(
                                    updateEmployee,
                                    netSuiteEmployee.Id!);
                            if (isUpdateSuccessfully)
                            {
                                _logger.LogInformation(
                                    "Manager info updated for employee: {EmployeeId}",
                                    omniEmployee.Id);
                            }
                            else
                            {
                                _logger.LogError(
                                    "Failed to update manager info for employee: {EmployeeId}",
                                    omniEmployee.Id);
                            }
                        }
                    }
                }
            });
    }
}