using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.SystemAuditLogs;
using Sleekflow.AuditHub.SystemAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.AuditHub.Triggers.SystemAuditLogs.Authorized;

[TriggerGroup(
    "SystemAuditLogs",
    $"authorized",
    new[]
    {
        AuthorizationFilterNames.HeadersAuthorizationFuncFilter,
    })]
public class GetSystemAuditLogs : ITrigger
{
    private readonly ISystemAuditLogService _systemAuditLogService;
    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public GetSystemAuditLogs(
        ISystemAuditLogService systemAuditLogService,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _systemAuditLogService = systemAuditLogService;
        _authorizationContext = authorizationContext;
    }

    public class GetSystemAuditLogsInput
    {
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [ValidateObject]
        [JsonProperty("filters")]
        public GetSystemAuditLogsInputFilters Filters { get; set; }

        [Required]
        [Range(1, 100)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetSystemAuditLogsInput(
            string? continuationToken,
            GetSystemAuditLogsInputFilters filters,
            int limit)
        {
            ContinuationToken = continuationToken;
            Filters = filters;
            Limit = limit;
        }
    }

    public class GetSystemAuditLogsInputFilters : SystemAuditLogsFilters
    {
        [JsonConstructor]
        public GetSystemAuditLogsInputFilters(
            List<string>? types,
            string? sleekflowUserProfileId,
            string? sleekflowStaffId,
            DateTimeOffset? fromCreatedTime,
            DateTimeOffset? toCreatedTime)
            : base(
                types,
                sleekflowUserProfileId,
                sleekflowStaffId,
                fromCreatedTime,
                toCreatedTime)
        {
        }
    }

    public class GetSystemAuditLogsOutput
    {
        [JsonProperty("user_profile_audit_logs")]
        public List<SystemAuditLog> SystemAuditLogs { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonConstructor]
        public GetSystemAuditLogsOutput(
            List<SystemAuditLog> systemAuditLogs,
            string? nextContinuationToken)
        {
            SystemAuditLogs = systemAuditLogs;
            NextContinuationToken = nextContinuationToken;
        }
    }

    public async Task<GetSystemAuditLogsOutput> F(
        GetSystemAuditLogsInput getSystemAuditLogsInput)
    {
        if (_authorizationContext.SleekflowCompanyId == null)
        {
            throw new SfUnauthorizedException();
        }

        var (nextContinuationToken, systemAuditLogs) =
            await _systemAuditLogService.GetSystemAuditLogsAsync(
                _authorizationContext.SleekflowCompanyId,
                getSystemAuditLogsInput.ContinuationToken,
                getSystemAuditLogsInput.Filters,
                getSystemAuditLogsInput.Limit);

        return new GetSystemAuditLogsOutput(
            systemAuditLogs,
            nextContinuationToken);
    }
}