using Microsoft.DurableTask.Client;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Sleekflow;
using Sleekflow.EmailHub.Workers.Configs;
using Sleekflow.Mvc;
using Sleekflow.Persistence.Abstractions;

const string name = "SleekflowEmailHubWorker";

var hostBuilder = new HostBuilder();

MvcModules.BuildIsolatedAzureFunction(
    name,
    hostBuilder,
    services =>
    {
        Modules.BuildDbServices(services);
        services.AddDurableTaskClient(
            builder =>
            {
            });

        services.AddSingleton<IAppConfig, AppConfig>();
        services.AddScoped<IDynamicFiltersRepositoryContext, DynamicFiltersRepositoryContext>();
        services.AddHttpContextAccessor();
    });

hostBuilder.Build().Run();