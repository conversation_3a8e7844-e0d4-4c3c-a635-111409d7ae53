﻿using Pulumi;
using Pulumi.AzureNative.Portal;
using Pulumi.AzureNative.Portal.Inputs;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;

namespace Sleekflow.Infras.Components.Dashboards;

public class AzurePortalDashboard
{
    private readonly MyConfig _myConfig;
    private readonly ResourceGroup _resourceGroup;

    public AzurePortalDashboard(MyConfig myConfig, ResourceGroup resourceGroup)
    {
        _myConfig = myConfig;
        _resourceGroup = resourceGroup;
    }

    public void InitPortalDashboard()
    {
        var dashboardName = $"sleekflow-v2-dashboard-{_myConfig.Name}";

        var dashboardMetadata = new Dictionary<string, object>
        {
            {
                "model", new Dictionary<string, object>
                {
                    {
                        "timeRange", new Dictionary<string, object>
                        {
                            {
                                "value", new Dictionary<string, object>
                                {
                                    {
                                        "relative", new Dictionary<string, object>
                                        {
                                            {
                                                "duration", 24
                                            },
                                            {
                                                "timeUnit", 1
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "type", "MsPortalFx.Composition.Configuration.ValueTypes.TimeRange"
                            }
                        }
                    },
                    {
                        "filterLocale", new Dictionary<string, object>
                        {
                            {
                                "value", "en-us"
                            }
                        }
                    },
                    {
                        "filters", new Dictionary<string, object>
                        {
                            {
                                "value", new Dictionary<string, object>
                                {
                                    {
                                        "MsPortalFx_TimeRange", new Dictionary<string, object>
                                        {
                                            {
                                                "model", new Dictionary<string, object>
                                                {
                                                    {
                                                        "format", "local"
                                                    },
                                                    {
                                                        "granularity", "1m"
                                                    },
                                                    {
                                                        "relative", "12h"
                                                    }
                                                }
                                            },
                                            {
                                                "displayCache", new Dictionary<string, object>
                                                {
                                                    {
                                                        "name", "Local Time"
                                                    },
                                                    {
                                                        "value", "Past 12 hours"
                                                    }
                                                }
                                            },
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        };

        switch (_myConfig.Name.ToLower())
        {
            case "dev":
            {
                var dashboard = new Dashboard(
                    dashboardName,
                    new DashboardArgs
                    {
                        DashboardName = dashboardName,
                        Location = "global",
                        Properties = new DashboardPropertiesWithProvisioningStateArgs
                        {
                            Lenses = new[]
                            {
                                new DashboardLensArgs
                                {
                                    Order = 0, Parts = DevDashboard.GetDashboardPartsArgs()
                                }
                            },
                            Metadata = dashboardMetadata
                        },
                        ResourceGroupName = _resourceGroup.Name
                    });
                break;
            }

            case "production":
            {
                var dashboard = new Dashboard(
                    dashboardName,
                    new DashboardArgs
                    {
                        DashboardName = dashboardName,
                        Location = "global",
                        Properties = new DashboardPropertiesWithProvisioningStateArgs
                        {
                            Lenses = new[]
                            {
                                new DashboardLensArgs
                                {
                                    Order = 0, Parts = ProdDashboard.GetDashboardPartsArgs()
                                }
                            },
                            Metadata = dashboardMetadata
                        },
                        ResourceGroupName = _resourceGroup.Name
                    });
                break;
            }
        }
    }
}