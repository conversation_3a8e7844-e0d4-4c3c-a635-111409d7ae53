using Sleekflow.DependencyInjection;
using Stripe;

namespace Sleekflow.CommerceHub.Payments.Stripe;

public interface IStripeCustomerRepository
{
    Task<Customer?> GetCustomerOrDefaultAsync(
        string customerId,
        string apiKey);

    Task<Customer> GetCustomerAsync(
        string customerId,
        string apiKey);

    Task<Customer> GetOrCreateCustomerAsync(
        string name,
        string email,
        string? phone,
        StripeClient stripeClient);

    Task<Customer> UpdateCustomerAsync(
        string customerId,
        string name,
        string email,
        string phone,
        string apiKey);
}

public class StripeCustomerRepository : IStripeCustomerRepository, ISingletonService
{
    private readonly IStripeClients _stripeClients;

    public StripeCustomerRepository(
        IStripeClients stripeClients)
    {
        _stripeClients = stripeClients;
    }

    public async Task<Customer?> GetCustomerOrDefaultAsync(
        string customerId,
        string apiKey)
    {
        try
        {
            return await GetCustomerAsync(
                customerId,
                apiKey);
        }
        catch (StripeException)
        {
            return null;
        }
    }

    public Task<Customer> GetCustomerAsync(
        string customerId,
        string apiKey)
    {
        var stripeClient = _stripeClients.GetCustomStripeClient(apiKey);

        var customerService = new CustomerService(stripeClient);

        return customerService.GetAsync(customerId);
    }

    public async Task<Customer> GetOrCreateCustomerAsync(
        string name,
        string email,
        string? phone,
        StripeClient stripeClient)
    {
        if (email == null && phone == null)
        {
            throw new ArgumentException("Either email or phone must be provided.");
        }

        var customerService = new CustomerService(stripeClient);

        var customers = await customerService.ListAsync(
            new CustomerListOptions()
            {
                Email = email,
            });
        if (customers.Any())
        {
            return customers.First();
        }

        var customerCreateOptions = new CustomerCreateOptions()
        {
            Name = name, Email = email, Phone = phone
        };
        var customer = await customerService.CreateAsync(customerCreateOptions);

        return customer;
    }

    public Task<Customer> UpdateCustomerAsync(
        string customerId,
        string name,
        string email,
        string phone,
        string apiKey)
    {
        var stripeClient = _stripeClients.GetCustomStripeClient(apiKey);

        var customerService = new CustomerService(stripeClient);
        var customerOptions = new CustomerUpdateOptions
        {
            Email = email, Name = name, Phone = phone,
        };
        var stripeCustomer = customerService.UpdateAsync(
            customerId,
            customerOptions);

        return stripeCustomer;
    }
}