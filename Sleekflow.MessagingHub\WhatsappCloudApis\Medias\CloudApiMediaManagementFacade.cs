using GraphApi.Client.ApiClients;
using Sleekflow.DependencyInjection;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Medias;

public interface ICloudApiMediaManagementFacade
{
    Task<(
        string Id,
        string Url, string MineType, string Sha256, string FileSize)> GetCloudApiMediaUrlAsync(
        string mediaId,
        string? businessIntegrationSystemUserAccessToken);

    Task<string> UploadCloudApiMediaAsync(
        string fromPhoneNumberId,
        byte[] fileBytes,
        string type,
        string fileName,
        string? businessIntegrationSystemUserAccessToken);

    Task<bool> DeleteCloudApiMediaAsync(
        string mediaId,
        string? businessIntegrationSystemUserAccessToken);
}

public class CloudApiMediaManagementFacade : ICloudApiMediaManagementFacade, ISingletonService
{
    private readonly ILogger<CloudApiMediaManagementFacade> _logger;
    private readonly IWhatsappCloudApiMessagingClient _whatsappCloudApiMessagingClient;
    private readonly HttpClient _httpClient;

    public CloudApiMediaManagementFacade(
        ICloudApiClients cloudApiClients,
        ILogger<CloudApiMediaManagementFacade> logger,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _whatsappCloudApiMessagingClient = cloudApiClients.WhatsappCloudApiMessagingClient;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<(
        string Id,
        string Url, string MineType, string Sha256, string FileSize)> GetCloudApiMediaUrlAsync(
        string mediaId,
        string? businessIntegrationSystemUserAccessToken)
    {
        if (string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            var getMediaUrlResponse = await _whatsappCloudApiMessagingClient.GetMediaUrlAsync(mediaId);

            return (getMediaUrlResponse.Id, getMediaUrlResponse.Url,
                getMediaUrlResponse.MimeType, getMediaUrlResponse.Sha256, getMediaUrlResponse.FileSize);
        }
        else
        {
            var whatsappCloudApiMessagingClient = new WhatsappCloudApiMessagingClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient);
            var getMediaUrlResponse = await whatsappCloudApiMessagingClient.GetMediaUrlAsync(mediaId);

            return (getMediaUrlResponse.Id, getMediaUrlResponse.Url,
                getMediaUrlResponse.MimeType, getMediaUrlResponse.Sha256, getMediaUrlResponse.FileSize);
        }
    }

    public async Task<string> UploadCloudApiMediaAsync(
        string fromPhoneNumberId,
        byte[] fileBytes,
        string type,
        string fileName,
        string? businessIntegrationSystemUserAccessToken)
    {
        if (string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            var whatsappCloudApiUploadMediaResponse =
                await _whatsappCloudApiMessagingClient.UploadMediaAsync(fromPhoneNumberId, fileBytes, type, fileName);
            return whatsappCloudApiUploadMediaResponse.Id;
        }
        else
        {
            var whatsappCloudApiMessagingClient = new WhatsappCloudApiMessagingClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient);

            var whatsappCloudApiUploadMediaResponse =
                await whatsappCloudApiMessagingClient.UploadMediaAsync(fromPhoneNumberId, fileBytes, type, fileName);
            return whatsappCloudApiUploadMediaResponse.Id;
        }
    }

    public async Task<bool> DeleteCloudApiMediaAsync(string mediaId, string? businessIntegrationSystemUserAccessToken)
    {
        if (string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            var deleteMediaResponse = await _whatsappCloudApiMessagingClient.DeleteMediaAsync(mediaId);
            return deleteMediaResponse.Success;
        }
        else
        {
            var whatsappCloudApiMessagingClient = new WhatsappCloudApiMessagingClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient);

            var deleteMediaResponse = await whatsappCloudApiMessagingClient.DeleteMediaAsync(mediaId);
            return deleteMediaResponse.Success;
        }
    }
}