using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class SendMetaCapiEventRequest
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; }

    [JsonProperty("whatsapp_business_account_id")]
    [Required]
    public string WhatsappBusinessAccountId { get; }

    [JsonProperty("conversion_api_event")]
    [Required]
    public SendMetaCapiEventObjectRequest ConversionApiEvent { get; }

    [JsonConstructor]
    public SendMetaCapiEventRequest(
        string sleekflowCompanyId,
        string whatsappBusinessAccountId,
        SendMetaCapiEventObjectRequest conversionApiEvent)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WhatsappBusinessAccountId = whatsappBusinessAccountId;
        ConversionApiEvent = conversionApiEvent;
    }
}