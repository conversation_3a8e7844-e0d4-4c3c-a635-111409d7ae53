using Sleekflow.Persistence.Abstractions;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Medias;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Medias.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Medias)]
public class DeleteWhatsappCloudApiMedia
    : ITrigger<
        DeleteWhatsappCloudApiMedia.DeleteWhatsappCloudApiMediaInput,
        DeleteWhatsappCloudApiMedia.DeleteWhatsappCloudApiMediaOutput>
{
    private readonly ICloudApiMediaManagementFacade _cloudApiMediaManagementFacade;
    private readonly IWabaService _wabaService;
    private readonly ILogger<DeleteWhatsappCloudApiMedia> _logger;

    public DeleteWhatsappCloudApiMedia(ICloudApiMediaManagementFacade cloudApiMediaManagementFacade, IWabaService wabaService, ILogger<DeleteWhatsappCloudApiMedia> logger)
    {
        _cloudApiMediaManagementFacade = cloudApiMediaManagementFacade;
        _wabaService = wabaService;
        _logger = logger;
    }

    public class DeleteWhatsappCloudApiMediaInput
    {
        [JsonProperty("media_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string MediaId { get; set; }

        [JsonProperty("waba_id")]
        public string? WabaId { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public DeleteWhatsappCloudApiMediaInput(string mediaId, string? wabaId, string? sleekflowCompanyId)
        {
            MediaId = mediaId;
            WabaId = wabaId;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class DeleteWhatsappCloudApiMediaOutput
    {
        [JsonProperty("success")]
        public bool Success { get; set; }

        [JsonConstructor]
        public DeleteWhatsappCloudApiMediaOutput(bool success)
        {
            Success = success;
        }
    }

    public async Task<DeleteWhatsappCloudApiMediaOutput> F(
        DeleteWhatsappCloudApiMediaInput deleteWhatsappCloudApiMediaInput)
    {
        if (string.IsNullOrEmpty(deleteWhatsappCloudApiMediaInput.WabaId) || string.IsNullOrEmpty(deleteWhatsappCloudApiMediaInput.SleekflowCompanyId))
        {
            return new DeleteWhatsappCloudApiMediaOutput(
                await _cloudApiMediaManagementFacade.DeleteCloudApiMediaAsync(
                    deleteWhatsappCloudApiMediaInput.MediaId,
                    null));
        }

        var waba = await _wabaService.GetWabaOrDefaultAsync(deleteWhatsappCloudApiMediaInput.WabaId, deleteWhatsappCloudApiMediaInput.SleekflowCompanyId);

        if (waba == null || !CloudApiUtils.IsWabaMessagingFunctionAvailable(_logger, waba))
        {
            throw new SfNotSupportedOperationException("Unable to locate any valid waba");
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        if (hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null)
        {
            return new DeleteWhatsappCloudApiMediaOutput(
                await _cloudApiMediaManagementFacade.DeleteCloudApiMediaAsync(
                    deleteWhatsappCloudApiMediaInput.MediaId,
                    decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken));
        }

        return new DeleteWhatsappCloudApiMediaOutput(
            await _cloudApiMediaManagementFacade.DeleteCloudApiMediaAsync(
                deleteWhatsappCloudApiMediaInput.MediaId,
                null));
    }
}