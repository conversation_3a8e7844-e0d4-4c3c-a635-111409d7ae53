using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Carts;
using Sleekflow.CommerceHub.Models.Carts;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Carts;

[TriggerGroup(ControllerNames.Carts)]
public class GetUserCarts
    : ITrigger<
        GetUserCarts.GetUserCartsInput,
        GetUserCarts.GetUserCartsOutput>
{
    private readonly ICartService _cartService;
    private readonly IProductVariantService _productVariantService;
    private readonly IProductService _productService;

    public GetUserCarts(
        ICartService cartService,
        IProductVariantService productVariantService,
        IProductService productService)
    {
        _cartService = cartService;
        _productVariantService = productVariantService;
        _productService = productService;
    }

    public class GetUserCartsInput : IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [JsonProperty(Cart.PropertyNameCartStatus)]
        public string CartStatus { get; set; }

        [JsonConstructor]
        public GetUserCartsInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            string cartStatus)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            CartStatus = cartStatus;
        }
    }

    public class GetUserCartsOutput
    {
        [JsonProperty("carts")]
        public List<CartDto> Carts { get; set; }

        [JsonConstructor]
        public GetUserCartsOutput(List<CartDto> carts)
        {
            Carts = carts;
        }
    }

    public async Task<GetUserCartsOutput> F(GetUserCartsInput getUserCartsInput)
    {
        var carts = await _cartService.GetUserCartsAsync(
            getUserCartsInput.SleekflowCompanyId,
            getUserCartsInput.SleekflowUserProfileId,
            getUserCartsInput.CartStatus);

        var productVariantIds = carts
            .SelectMany(c => c.LineItems.Select(li => li.ProductVariantId))
            .Distinct()
            .ToList();

        var cartDtos = new List<CartDto>();
        foreach (var cart in carts)
        {
            var productVariants = await _productVariantService.GetProductVariantsAsync(
                cart.LineItems.Select(x => x.ProductVariantId).ToList(),
                cart.SleekflowCompanyId,
                cart.StoreId);
            var productVariantIdToProductVariantDtoDict = productVariants
                .GroupBy(pv => pv.Id)
                .ToDictionary(pv => pv.Key, pv => pv.First());

            var products = await _productService.GetProductsAsync(
                productVariants.Select(x => x.ProductId).Distinct().ToList(),
                cart.SleekflowCompanyId,
                cart.StoreId);
            var productIdToProductDict = products
                .GroupBy(pv => pv.Id)
                .ToDictionary(pv => pv.Key, pv => pv.First());

            cartDtos.Add(
                new CartDto(
                    cart,
                    productVariantIdToProductVariantDtoDict,
                    productIdToProductDict));
        }

        return new GetUserCartsOutput(cartDtos);
    }
}