using Sleekflow.CommerceHub.Models.CustomCatalogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.CustomCatalogs;

public interface ICustomCatalogFileRepository : IDynamicFiltersRepository<CustomCatalogFile>
{
}

public class CustomCatalogFileRepository
    : DynamicFiltersBaseRepository<CustomCatalogFile>, ICustomCatalogFileRepository, IScopedService
{
    public CustomCatalogFileRepository(
        ILogger<DynamicFiltersBaseRepository<CustomCatalogFile>> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }
}