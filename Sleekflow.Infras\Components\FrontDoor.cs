using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Auth0s;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Cdn = Pulumi.AzureNative.Cdn;
using Random = Pulumi.Random;
using Resource = Pulumi.Resource;

namespace Sleekflow.Infras.Components;

public class FrontDoor
{
    private readonly ResourceGroup _resourceGroup;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly MyConfig _myConfig;
    private readonly Auth0Config _auth0Config;

    public FrontDoor(
        ResourceGroup resourceGroup,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        MyConfig myConfig,
        Auth0Config auth0Config)
    {
        _resourceGroup = resourceGroup;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _myConfig = myConfig;
        _auth0Config = auth0Config;
    }

    public Cdn.AFDEndpoint InitFrontDoor()
    {
        var profile = new Cdn.Profile(
            "sleekflow-front-door-profile",
            new Cdn.ProfileArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = "sleekflow",
                Location = "Global",
                Sku = new Cdn.Inputs.SkuArgs
                {
                    Name = Cdn.SkuName.Standard_AzureFrontDoor,
                },
                OriginResponseTimeoutSeconds = 60,
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup
            });

        var afdEndpoint = new Cdn.AFDEndpoint(
            "sleekflow-front-door-endpoint",
            new Cdn.AFDEndpointArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                EndpointName = _myConfig.Name == "production" ? "sleekflow" : "sleekflow-" + _myConfig.Name,
                EnabledState = Cdn.EnabledState.Enabled,
                Location = "Global",
            },
            new CustomResourceOptions
            {
                Parent = profile
            });

        // Excluding all the global managed envs from the default origin group
        var (afdOriginGroup, afdOrigins) = InitAfdOriginGroup(
            profile,
            _managedEnvAndAppsTuples.Where(m => m is not FilteredManagedEnvAndAppsTuple).ToList());

        // Init the global managed envs for global origin group
        var (globalAfdOriginGroup, globalAfdOrigins) = InitAfdOriginGroup(
            profile,
            _managedEnvAndAppsTuples,
            "global");

        var ruleSet = InitRuleSet(
            profile,
            out var sfdcRule,
            out var corsRule,
            out var publicApiGatewayRule,
            out var signalrRule,
            out var corsDomainRules);

        var _ = new Cdn.Route(
            "sleekflow-front-door-default-route",
            new Cdn.RouteArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                EndpointName = afdEndpoint.Name,
                RouteName = "default-route",
                CustomDomains = new InputList<Cdn.Inputs.ActivatedResourceReferenceArgs>(),
                OriginGroup = new Cdn.Inputs.ResourceReferenceArgs
                {
                    Id = Output.Tuple(afdOriginGroup.Id, afdOrigins.First().ProvisioningState)
                        .Apply(t => t.Item1),
                },
                RuleSets = new List<Cdn.Inputs.ResourceReferenceArgs>
                {
                    new Cdn.Inputs.ResourceReferenceArgs
                    {
                        Id = ruleSet.Id
                    }
                },
                SupportedProtocols =
                {
                    "Https",
                },
                PatternsToMatch =
                {
                    "/*",
                },
                ForwardingProtocol = Cdn.ForwardingProtocol.MatchRequest,
                LinkToDefaultDomain = Cdn.LinkToDefaultDomain.Enabled,
                HttpsRedirect = Cdn.HttpsRedirect.Disabled,
                EnabledState = Cdn.EnabledState.Enabled,
            },
            new CustomResourceOptions
            {
                DependsOn =
                    afdOrigins
                        .Concat(
                            new List<Resource>
                            {
                                ruleSet,
                                sfdcRule,
                                corsRule,
                                publicApiGatewayRule,
                                signalrRule,
                            })
                        .Concat(
                            corsDomainRules)
                        .ToList(),
                Parent = profile
            });

        var __ = new Cdn.Route(
            "sleekflow-front-door-tenant-hub-route",
            new Cdn.RouteArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                EndpointName = afdEndpoint.Name,
                RouteName = "tenant-hub-route",
                CustomDomains = new InputList<Cdn.Inputs.ActivatedResourceReferenceArgs>(),
                OriginGroup = new Cdn.Inputs.ResourceReferenceArgs
                {
                    Id = Output.Tuple(globalAfdOriginGroup.Id, globalAfdOrigins.First().ProvisioningState)
                        .Apply(t => t.Item1),
                },
                RuleSets = new List<Cdn.Inputs.ResourceReferenceArgs>
                {
                    new Cdn.Inputs.ResourceReferenceArgs
                    {
                        Id = ruleSet.Id
                    }
                },
                SupportedProtocols =
                {
                    "Https",
                },
                PatternsToMatch =
                {
                    "/v1/tenant-hub/*",
                },
                ForwardingProtocol = Cdn.ForwardingProtocol.MatchRequest,
                LinkToDefaultDomain = Cdn.LinkToDefaultDomain.Enabled,
                HttpsRedirect = Cdn.HttpsRedirect.Disabled,
                EnabledState = Cdn.EnabledState.Enabled,
            },
            new CustomResourceOptions
            {
                DependsOn =
                    globalAfdOrigins
                        .Concat(
                            new List<Resource>
                            {
                                ruleSet,
                                sfdcRule,
                                corsRule,
                                publicApiGatewayRule,
                                signalrRule
                            })
                        .Concat(corsDomainRules)
                        .ToList(),
                Parent = profile
            });

        return afdEndpoint;
    }

    private Cdn.RuleSet InitRuleSet(
        Cdn.Profile profile,
        out Cdn.Rule sfdcRule,
        out Cdn.Rule corsRule,
        out Cdn.Rule publicApiGatewayRule,
        out Cdn.Rule signalrRule,
        out List<Cdn.Rule> corsDomainRules)
    {
        var ruleSet = new Cdn.RuleSet(
            "sleekflow-front-door-default-ruleset",
            new Cdn.RuleSetArgs
            {
                ResourceGroupName = _resourceGroup.Name, ProfileName = profile.Name, RuleSetName = "default"
            },
            new CustomResourceOptions
            {
                Parent = profile
            });

        sfdcRule = new Cdn.Rule(
            "sleekflow-front-door-default-ruleset-default-rule",
            new Cdn.RuleArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                RuleSetName = ruleSet.Name,
                RuleName = "sfdc",
                Conditions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleRequestHeaderConditionArgs
                    {
                        Name = "RequestHeader",
                        Parameters = new Cdn.Inputs.RequestHeaderMatchConditionParametersArgs
                        {
                            TypeName = "DeliveryRuleRequestHeaderConditionParameters",
                            Operator = "Any",
                            Selector = "Sfdc_stack_depth",
                            NegateCondition = false,
                            MatchValues = new InputList<string>(),
                            Transforms = new InputList<Union<string, Cdn.Transform>>()
                        },
                    },
                },
                Actions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleRequestHeaderActionArgs
                    {
                        Name = "ModifyRequestHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Delete",
                            HeaderName = "Sfdc_stack_depth",
                        },
                    },
                },
                Order = 200,
                MatchProcessingBehavior = Cdn.MatchProcessingBehavior.Continue
            },
            new CustomResourceOptions
            {
                Parent = ruleSet
            });

        corsRule = new Cdn.Rule(
            "sleekflow-front-door-default-ruleset-default-cors-rule",
            new Cdn.RuleArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                RuleSetName = ruleSet.Name,
                RuleName = "CORS",
                Actions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Overwrite",
                            HeaderName = "access-control-allow-credentials",
                            Value = "true"
                        },
                    },
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Overwrite",
                            HeaderName = "access-control-allow-headers",
                            Value = "authorization,cache-control,content-type,x-sleekflow-location,x-sleekflow-distributed-invocation-context,x-sleekflow-rbac-key"
                        },
                    },
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Overwrite",
                            HeaderName = "access-control-allow-origin",
                            Value = "*"
                        },
                    },
                },
                Order = 210,
                MatchProcessingBehavior = Cdn.MatchProcessingBehavior.Continue
            },
            new CustomResourceOptions
            {
                Parent = ruleSet
            });

        publicApiGatewayRule = new Cdn.Rule(
            "sleekflow-front-door-default-ruleset-default-public-api-gateway-rule",
            new Cdn.RuleArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                RuleSetName = ruleSet.Name,
                RuleName = "PublicApiGateway",
                Conditions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleRequestHeaderConditionArgs
                    {
                        Name = "RequestHeader",
                        Parameters = new Cdn.Inputs.RequestHeaderMatchConditionParametersArgs
                        {
                            TypeName = "DeliveryRuleRequestHeaderConditionParameters",
                            Operator = "Contains",
                            Selector = "Access-Control-Request-Headers",
                            NegateCondition = false,
                            MatchValues = new InputList<string>
                            {
                                "x-sleekflow-api-key",
                            },
                            Transforms = new InputList<Union<string, Cdn.Transform>>(),
                        },
                    },
                },
                Actions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Append",
                            HeaderName = "access-control-allow-headers",
                            Value = ",x-sleekflow-api-key"
                        },
                    },
                },
                Order = 220,
                MatchProcessingBehavior = Cdn.MatchProcessingBehavior.Continue
            },
            new CustomResourceOptions
            {
                Parent = ruleSet
            });

        signalrRule = new Cdn.Rule(
            "sleekflow-front-door-default-ruleset-default-signalr-rule",
            new Cdn.RuleArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                RuleSetName = ruleSet.Name,
                RuleName = "SignalR",
                Conditions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleRequestHeaderConditionArgs
                    {
                        Name = "RequestHeader",
                        Parameters = new Cdn.Inputs.RequestHeaderMatchConditionParametersArgs
                        {
                            TypeName = "DeliveryRuleRequestHeaderConditionParameters",
                            Operator = "Contains",
                            Selector = "Access-Control-Request-Headers",
                            NegateCondition = false,
                            MatchValues = new InputList<string>
                            {
                                "x-signalr-user-agent", "x-requested-with",
                            },
                            Transforms = new InputList<Union<string, Cdn.Transform>>(),
                        },
                    },
                },
                Actions = new List<object>
                {
                    new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                    {
                        Name = "ModifyResponseHeader",
                        Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                        {
                            TypeName = "DeliveryRuleHeaderActionParameters",
                            HeaderAction = "Append",
                            HeaderName = "access-control-allow-headers",
                            Value = ",x-signalr-user-agent,x-requested-with"
                        },
                    },
                },
                Order = 230,
                MatchProcessingBehavior = Cdn.MatchProcessingBehavior.Continue
            },
            new CustomResourceOptions
            {
                Parent = ruleSet
            });

        var corsDomainRulesCounter = 500;

        var domains = _myConfig.Origins;

        corsDomainRules = new List<Cdn.Rule>();
        foreach (var domain in domains)
        {
            var randomId = new Random.RandomId(
                $"sleekflow-front-door-default-ruleset-{domain}-cors-domain-rule-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 10,
                    Keepers =
                    {
                        {
                            "Domain", domain
                        }
                    },
                },
                new CustomResourceOptions
                {
                    Parent = ruleSet
                });
            var domainRandomId = randomId.Hex.Apply(h => "s" + h);

            corsDomainRules.Add(
                new Cdn.Rule(
                    $"sleekflow-front-door-default-ruleset-{domain}-cors-domain-rule",
                    new Cdn.RuleArgs
                    {
                        ResourceGroupName = _resourceGroup.Name,
                        ProfileName = profile.Name,
                        RuleSetName = ruleSet.Name,
                        RuleName = domainRandomId.Apply(d => $"CorsDomainRule{d}"),
                        Conditions = new List<object>
                        {
                            new Cdn.Inputs.DeliveryRuleRequestHeaderConditionArgs
                            {
                                Name = "RequestHeader",
                                Parameters = new Cdn.Inputs.RequestHeaderMatchConditionParametersArgs
                                {
                                    TypeName = "DeliveryRuleRequestHeaderConditionParameters",
                                    Operator = "Contains",
                                    Selector = "Origin",
                                    NegateCondition = false,
                                    MatchValues = new InputList<string>
                                    {
                                        domain
                                    },
                                    Transforms = new InputList<Union<string, Cdn.Transform>>()
                                },
                            },
                        },
                        Actions = new List<object>
                        {
                            new Cdn.Inputs.DeliveryRuleResponseHeaderActionArgs
                            {
                                Name = "ModifyResponseHeader",
                                Parameters = new Cdn.Inputs.HeaderActionParametersArgs
                                {
                                    TypeName = "DeliveryRuleHeaderActionParameters",
                                    HeaderAction = "Overwrite",
                                    HeaderName = "access-control-allow-origin",
                                    Value = domain
                                },
                            },
                        },
                        Order = corsDomainRulesCounter
                    },
                    new CustomResourceOptions
                    {
                        Parent = ruleSet,
                    }));
            corsDomainRulesCounter++;
        }

        return ruleSet;
    }

    private (Cdn.AFDOriginGroup AfdOriginGroup, List<Cdn.AFDOrigin> AfdOrigins) InitAfdOriginGroup(
        Cdn.Profile profile,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        string originGroupIdentity = "default")
    {
        var afdOriginGroup = new Cdn.AFDOriginGroup(
            $"sleekflow-front-door-{originGroupIdentity}-origin-group",
            new Cdn.AFDOriginGroupArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                ProfileName = profile.Name,
                OriginGroupName = $"sleekflow-{originGroupIdentity}-origin-group",
                SessionAffinityState = Cdn.EnabledState.Disabled,

                // As the front door is a global service,
                // the health probes are also from global instances.
                // This would causes serious traffics globally.
                // So this is disabled currently.
                HealthProbeSettings = new Cdn.Inputs.HealthProbeParametersArgs
                {
                    ProbePath = "/__health",
                    ProbeProtocol = Cdn.ProbeProtocol.Https,
                    ProbeRequestType = Cdn.HealthProbeRequestType.GET,
                    ProbeIntervalInSeconds = 120,
                },
                LoadBalancingSettings = new Cdn.Inputs.LoadBalancingSettingsParametersArgs
                {
                    SampleSize = 4, SuccessfulSamplesRequired = 2, AdditionalLatencyInMilliseconds = 50,
                },
            },
            new CustomResourceOptions
            {
                Parent = profile
            });

        var afdOrigins = new List<Cdn.AFDOrigin>();
        foreach (var managedEnvAndAppsTuple in managedEnvAndAppsTuples)
        {
            var name = managedEnvAndAppsTuple.Name;
            var containerApps = managedEnvAndAppsTuple.ContainerApps;

            var afdOrigin = new Cdn.AFDOrigin(
                $"sleekflow-front-door-{originGroupIdentity}-origin-" + name,
                new Cdn.AFDOriginArgs
                {
                    ProfileName = profile.Name,
                    ResourceGroupName = _resourceGroup.Name,
                    OriginGroupName = afdOriginGroup.Name,
                    OriginName = $"sleekflow-{originGroupIdentity}-origin-" + name,
                    HostName = containerApps[ServiceNames.ApiGateway]
                        .Configuration
                        .Apply(c => c!.Ingress!.Fqdn),
                    HttpPort = 80,
                    HttpsPort = 443,
                    OriginHostHeader = containerApps[ServiceNames.ApiGateway]
                        .Configuration
                        .Apply(c => c!.Ingress!.Fqdn),
                    Priority = 1,
                    Weight = 1000,
                    EnabledState = Cdn.EnabledState.Enabled,
                },
                new CustomResourceOptions
                {
                    Parent = afdOriginGroup, Version = string.Empty
                });

            afdOrigins.Add(afdOrigin);
        }

        return (afdOriginGroup, afdOrigins);
    }
}