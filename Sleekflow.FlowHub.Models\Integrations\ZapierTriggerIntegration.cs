﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubIntegrationDb;

namespace Sleekflow.FlowHub.Models.Integrations;

[ContainerId(ContainerNames.ZapierTriggerIntegration)]
[DatabaseId(ContainerNames.FlowHubIntegrationDatabaseId)]
[Resolver(typeof(IFlowHubIntegrationDbResolver))]
public class ZapierTriggerIntegration : Entity, IIntegration
{
    public const string PropertyNameZapId = "zap_id";
    public const string PropertyNameHookUrl = "hook_url";
    public const string PropertyNameTriggerName = "trigger_name";
    public const string PropertyNameSleekflowLocation = "sleekflow_location";

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(PropertyNameZapId)]
    public string ZapId { get; set; }

    [JsonProperty(PropertyNameHookUrl)]
    public string HookUrl { get; set; }

    [JsonProperty(PropertyNameTriggerName)]
    public string TriggerName { get; set; }

    [JsonProperty(PropertyNameSleekflowLocation)]
    public string? SleekflowLocation { get; set; }

    [JsonProperty(IIntegration.PropertyNameWorkflowId)]
    public string WorkflowId { get; set; }

    [JsonProperty(IIntegration.PropertyNameWorkflowVersionedId)]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public ZapierTriggerIntegration(
        string id,
        string sleekflowCompanyId,
        string zapId,
        string hookUrl,
        string triggerName,
        string? sleekflowLocation,
        string workflowId,
        string workflowVersionedId,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
        : base(id, SysTypeNames.ZapierTriggerIntegration)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        ZapId = zapId;
        HookUrl = hookUrl;
        TriggerName = triggerName;
        SleekflowLocation = sleekflowLocation;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }
}