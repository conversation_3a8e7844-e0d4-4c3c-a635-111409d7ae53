using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

namespace Sleekflow.FlowHub.Models.Messages;

public class OrderMessageObject : BaseMessageObject
{
    [JsonProperty("catalog_id")]
    public string CatalogId { get; set; }

    [JsonProperty("text")]
    public string? Text { get; set; }

    [JsonProperty("product_items")]
    public List<OrderMessageProductItem>? ProductItems { get; set; }

    [JsonProperty("product_items_json")]
    public string? ProductItemsJson { get; set; }

    [JsonConstructor]
    public OrderMessageObject(
        string catalogId,
        string text,
        List<OrderMessageProductItem>? productItems,
        string productItemsJson)
    {
        CatalogId = catalogId;
        Text = text;
        ProductItems = productItems;
        ProductItemsJson = productItemsJson;
    }
}

public class OrderMessageProductItem
{
    [JsonProperty("product_retailer_id")]
    public string? ProductRetailerId { get; set; }

    [JsonProperty("quantity")]
    public int? Quantity { get; set; }

    [JsonProperty("item_price")]
    public decimal? ItemPrice { get; set; }

    [JsonProperty("currency")]
    public string? Currency { get; set; }

    [JsonConstructor]
    public OrderMessageProductItem(
        string? productRetailerId,
        int? quantity,
        decimal? itemPrice,
        string? currency)
    {
        ProductRetailerId = productRetailerId;
        Quantity = quantity;
        ItemPrice = itemPrice;
        Currency = currency;
    }
}