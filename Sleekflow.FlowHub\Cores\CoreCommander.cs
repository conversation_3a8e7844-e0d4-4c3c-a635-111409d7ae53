using System.Text;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.FlowHub.Configs;
using Sleekflow.FlowHub.Https;
using Sleekflow.FlowHub.JsonConfigs;
using Sleekflow.FlowHub.Utils;

namespace Sleekflow.FlowHub.Cores;

public interface ICoreCommander
{
    Task<T?> ExecuteAsync<T>(string? origin, string commandName, object args);

    Task<string> ExecuteAsync(string? origin, string commandName, object args);
}

public class CoreCommander
    : ICoreCommander, IScopedService
{
    private readonly ILogger<CoreCommander> _logger;
    private readonly HttpClient _httpClient;
    private readonly IAppConfig _appConfig;
    private readonly IDepthContext _depthContext;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CoreCommander(
        ILogger<CoreCommander> logger,
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig,
        IDepthContext depthContext,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient("default-flow-hub-handler");
        _appConfig = appConfig;
        _depthContext = depthContext;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public async Task<T?> ExecuteAsync<T>(string? origin, string commandName, object args)
    {
        var responseStr = await ExecuteAsync(origin, commandName, args);
        return JsonConvert.DeserializeObject<T>(
            responseStr,
            JsonConfig.DefaultJsonSerializerSettings);
    }

    public async Task<string> ExecuteAsync(string? origin, string commandName, object args)
    {
        var baseUrl = string.IsNullOrWhiteSpace(origin)
            ? _appConfig.CoreInternalsEndpoint
            : $"{origin}/FlowHub/Internals";
        var url = baseUrl + "/Commands/" + commandName;
        var headers = new Dictionary<string, string?>
        {
            {
                "X-Sleekflow-Flow-Hub-Authorization", InternalsTokenUtils.CreateJwt(_appConfig.CoreInternalsKey)
            },
            {
                "X-Sleekflow-Flow-Hub-Depth", $"{_depthContext.Depth + 1}"
            },
            {
                IDistributedInvocationContextService.XSleekflowDistributedInvocationContext,
                _distributedInvocationContextService.GetSerializedContextHeader()
            }
        };

        var argsJson = JsonConvert.SerializeObject(
            args,
            JsonConfig.DefaultJsonSerializerSettings);

        var resMsg = await HttpPolicies.HttpTransientErrorRetryPolicy
            .ExecuteAsync(
                async () =>
                {
                    var reqMsg = new HttpRequestMessage(
                        HttpMethod.Post,
                        url);

                    foreach (var (key, value) in headers)
                    {
                        reqMsg.Headers.Add(key, value ?? string.Empty);
                    }

                    reqMsg.Content = new StringContent(
                        argsJson,
                        Encoding.UTF8,
                        "application/json");

                    _logger.LogInformation(
                        "CoreCommander: {CommandName} {ArgsJson}",
                        commandName,
                        argsJson);

                    var response = await _httpClient.SendAsync(reqMsg);

                    _logger.LogInformation(
                        "CoreCommander: {CommandName} {ArgsJson} {StatusCode}",
                        commandName,
                        argsJson,
                        response.StatusCode);

                    response.EnsureSuccessStatusCode();

                    return response;
                });

        var responseStr = await resMsg.Content.ReadAsStringAsync();

        _logger.LogInformation(
            "CoreCommander: {CommandName} {ArgsJson} {ResponseStr}",
            commandName,
            argsJson,
            responseStr);

        return responseStr;
    }
}