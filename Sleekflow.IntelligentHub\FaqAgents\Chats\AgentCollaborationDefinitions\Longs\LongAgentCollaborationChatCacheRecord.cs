using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;

public class LongAgentCollaborationChatCacheRecord
{
    [JsonProperty("group_chat_id")]
    public string GroupChatId { get; set; }

    [JsonProperty("customer_inquiry_record")]
    public string CustomerInquiryRecord { get; set; }

    [JsonProperty("strategy_record")]
    public string StrategyRecord { get; set; }

    [JsonProperty("all_proposed_reply_to_customer_records")]
    public string[] AllProposedReplyToCustomerRecords { get; set; }

    [JsonProperty("all_confirmed_knowledge_records")]
    public string[] AllConfirmedKnowledgeRecords { get; set; }

    [JsonConstructor]
    public LongAgentCollaborationChatCacheRecord(
        string groupChatId,
        string customerInquiryRecord,
        string strategyRecord,
        string[] allProposedReplyToCustomerRecords,
        string[] allConfirmedKnowledgeRecords)
    {
        GroupChatId = groupChatId;
        CustomerInquiryRecord = customerInquiryRecord;
        StrategyRecord = strategyRecord;
        AllProposedReplyToCustomerRecords = allProposedReplyToCustomerRecords;
        AllConfirmedKnowledgeRecords = allConfirmedKnowledgeRecords;
    }
}