using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Agents.Summaries;
using Sleekflow.IntelligentHub.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Consumers;
using Sleekflow.IntelligentHub.IntelligentHubConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Snapshots;
using Sleekflow.Models.Events;
using Sleekflow.Models.WorkflowSteps;

namespace Sleekflow.IntelligentHub.Events;

public class GetAgentSummarizeEventConsumerDefinition : ConsumerDefinition<GetAgentSummarizeEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetAgentSummarizeEventConsumer> consumerConfigurator,
        IRegistrationContext context)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration inMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetAgentSummarizeEventConsumer : FlowHubAgentGenericConsumer<GetAgentSummarizeEvent>, IConsumer<GetAgentSummarizeEvent>
{
    private readonly ISummaryService _summaryService;
    private readonly IIntelligentHubUsageService _intelligentHubUsageService;
    private readonly ICompanyAgentConfigService _companyAgentConfigService;

    public GetAgentSummarizeEventConsumer(
        IBus bus,
        ISummaryService summaryService,
        ILogger<GetAgentSummarizeEventConsumer> logger,
        IIntelligentHubUsageService intelligentHubUsageService,
        ICompanyAgentConfigService companyAgentConfigService)
        : base(logger, bus)
    {
        _summaryService = summaryService;
        _intelligentHubUsageService = intelligentHubUsageService;
        _companyAgentConfigService = companyAgentConfigService;
    }

    protected override async Task HandleMessageAsync(ConsumeContext<GetAgentSummarizeEvent> context)
    {
        var message = context.Message;

        _logger.LogInformation("Get Conversation Summary {Message}", JsonConvert.SerializeObject(message));

        var (tone, responseLevel, languageIsoCode, contactProperties) = await GetPromptParameters(
            message.SleekflowCompanyId,
            message.AgentId,
            message.LanguageIsoCode,
            message.ContactProperties);

        var sfChatEntries = message.ConversationContext;

        var conversationSummary = await _summaryService.GetConversationSummaryAsync(
            sfChatEntries,
            message.HandoverReason,
            tone,
            responseLevel,
            languageIsoCode,
            contactProperties);

        var conversationSummaryStr = JsonConvert.SerializeObject(conversationSummary);

        await _intelligentHubUsageService.RecordUsageAsync(
            message.SleekflowCompanyId,
            PriceableFeatures.Summary,
            null,
            new SummarizedSnapshot(sfChatEntries, conversationSummaryStr));

        _logger.LogInformation("Generated Conversation Summary {ConversationSummary}", conversationSummaryStr);
        var response = new GetAgentSummarizeEvent.Response(
            conversationSummary!.Context,
            conversationSummary.HandoverReason);

        await _bus.Publish(
            new OnAgentCompleteStepActivationEvent(
                message.AggregateStepId,
                message.ProxyStateId,
                message.StackEntries,
                JsonConvert.SerializeObject(response)));
    }

    private async
        Task<(string Tone, string ResponseLevel, string LanguageIsoCode, Dictionary<string, string>? ContactProperties)>
        GetPromptParameters(
            string sleekflowCompanyId,
            string? agentId,
            string? languageIsoCode,
            Dictionary<string, string>? contactProperties)
    {
        if (string.IsNullOrEmpty(agentId))
        {
            return ("Professional", string.Empty, languageIsoCode ?? "en", contactProperties);
        }

        var config = await _companyAgentConfigService.GetOrDefaultAsync(agentId, sleekflowCompanyId);
        var instruction = config?.PromptInstruction;

        return (
            instruction?.Tone ?? "Professional",
            instruction?.ResponseLevel ?? string.Empty,
            languageIsoCode ?? "en",
            contactProperties
        );
    }
}