﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.CrmHubIntegrationDb;

namespace Sleekflow.CrmHub.Models.Authentications;

[Resolver(typeof(ICrmHubIntegrationDbResolver))]
[DatabaseId("crmhubintegrationdb")]
[ContainerId("salesforce_apex_trigger_authentication")]
public class SalesforceApexTriggerAuthentication : Entity
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("key")]
    public string Key { get; set; }

    [JsonProperty("s_object_type_name")]
    public string SObjectTypeName { get; set; }

    [JsonConstructor]
    public SalesforceApexTriggerAuthentication(string id, string sleekflowCompanyId, string key, string sObjectTypeName)
        : base(id, "ApexTriggerAuthentication")
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        Key = key;
        SObjectTypeName = sObjectTypeName;
    }
}