﻿using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;
using Sleekflow.Utils;
using StackExchange.Redis;

namespace Sleekflow.Locks;

public partial interface ILockService
{
    Task<(bool IsLocked, TimeSpan? ExpireIn)> IsLockedAsync(string[] strings, CancellationToken cancellationToken = default);

    Task<Lock?> LockAsync(string[] strings, TimeSpan minimumDuration, CancellationToken cancellationToken = default);

    Task<Lock> WaitUnitLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default);

    Task<int> ReleaseAsync(Lock @lock, CancellationToken cancellationToken = default);
}

public partial class LockService : ILockService, ISingletonService
{
    private readonly ILogger<LockService> _logger;
    private readonly IConnectionMultiplexer _connectionMultiplexer;

    public LockService(
        ILogger<LockService> logger,
        IConnectionMultiplexer connectionMultiplexer)
    {
        _logger = logger;
        _connectionMultiplexer = connectionMultiplexer;
    }

    public async Task<(bool IsLocked, TimeSpan? ExpireIn)> IsLockedAsync(
        string[] strings,
        CancellationToken cancellationToken = default)
    {
        var id = GetLockId(strings);

        var keyTtl = await _connectionMultiplexer
            .GetDatabase()
            .KeyTimeToLiveAsync(id);

        return (keyTtl.HasValue, keyTtl);
    }

    public async Task<Lock?> LockAsync(
        string[] strings,
        TimeSpan minimumDuration,
        CancellationToken cancellationToken = default)
    {
        var id = GetLockId(strings);
        var totalSeconds = Math.Max(1, (int) Math.Ceiling(minimumDuration.TotalSeconds));

        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug(
                "Acquiring a lock for strings {Strings}.",
                JsonConvert.SerializeObject(strings));
        }

        Lock? @lock = null;
        try
        {
            var eTag = RandomStringUtils.Gen(16);
            var isLocked = await _connectionMultiplexer.GetDatabase().StringSetAsync(
                id,
                eTag,
                minimumDuration,
                When.NotExists);
            if (isLocked)
            {
                @lock = new Lock(id, totalSeconds)
                {
                    ETag = eTag
                };
            }
        }
        catch
        {
            // ignored
        }

        if (@lock == null)
        {
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "Unable to acquire a lock for strings {Strings}.",
                    JsonConvert.SerializeObject(strings, JsonConfig.DefaultJsonSerializerSettings));
            }
        }
        else
        {
            if (_logger.IsEnabled(LogLevel.Debug))
            {
                _logger.LogDebug(
                    "Acquired a lock {Lock} for strings {Strings}.",
                    JsonConvert.SerializeObject(@lock, JsonConfig.DefaultJsonSerializerSettings),
                    JsonConvert.SerializeObject(strings, JsonConfig.DefaultJsonSerializerSettings));
            }
        }

        return @lock;
    }

    public async Task<Lock> WaitUnitLockAsync(
        string[] strings,
        TimeSpan minimumLockDuration,
        TimeSpan maximumWaitDuration,
        CancellationToken cancellationToken = default)
    {
        var retryDelayDuration = TimeSpan.FromMilliseconds(100);
        var maxRetryCount = maximumWaitDuration / retryDelayDuration;

        var i = 0;
        while (true)
        {
            var @lock = await LockAsync(strings, minimumLockDuration, cancellationToken);
            if (@lock != null)
            {
                return @lock;
            }

            if (i++ > maxRetryCount)
            {
                throw new TimeoutException("Unable to acquire a lock.");
            }

            await Task.Delay(retryDelayDuration, cancellationToken);
        }
    }

    public async Task<int> ReleaseAsync(Lock @lock, CancellationToken cancellationToken = default)
    {
        if (_logger.IsEnabled(LogLevel.Debug))
        {
            _logger.LogDebug("Released the lock {Lock}.", @lock);
        }

        var database = _connectionMultiplexer.GetDatabase();

        var tran = database.CreateTransaction();
        tran.AddCondition(Condition.StringEqual(@lock.Id, @lock.ETag));
#pragma warning disable CS4014
        tran.KeyDeleteAsync(@lock.Id);
#pragma warning restore CS4014
        var committed = await tran.ExecuteAsync();

        return committed ? 1 : 0;
    }

    private static string GetLockId(string[] strings)
        => "lock-" + string.Join("-", strings);
}