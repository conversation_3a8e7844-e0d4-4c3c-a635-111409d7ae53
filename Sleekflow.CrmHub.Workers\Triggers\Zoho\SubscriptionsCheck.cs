﻿using System.ComponentModel.DataAnnotations;
using Microsoft.Azure.Cosmos;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.CrmHub.Workers.Subscriptions;

namespace Sleekflow.CrmHub.Workers.Triggers.Zoho;

public class SubscriptionsCheck
{
    private readonly ILogger<SubscriptionsCheck> _logger;
    private readonly IZohoSubscriptionRepository _zohoSubscriptionRepository;

    public SubscriptionsCheck(
        ILogger<SubscriptionsCheck> logger,
        IZohoSubscriptionRepository zohoSubscriptionRepository)
    {
        _logger = logger;
        _zohoSubscriptionRepository = zohoSubscriptionRepository;

    }

    public class SubscriptionsCheckInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public ZohoSubscription Subscription { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckInput(
            string sleekflowCompanyId,
            ZohoSubscription subscription)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
        }
    }

    [Function("Zoho_SubscriptionsCheck")]
    public async Task RunAsync(
        [TimerTrigger("0 */1 * * * *")]
        TimerInfo timerInfo,
        [DurableClient]
        DurableTaskClient starter)
    {
        var queryDefinition =
            new QueryDefinition(
                    "SELECT * "
                    + "FROM %%CONTAINER_NAME%% c "
                    + "WHERE DateTimeDiff('second', c.last_execution_start_time, @now) > c.interval AND c.durable_payload = null")
                .WithParameter("@now", DateTimeOffset.UtcNow);
        var objects =
            await _zohoSubscriptionRepository.GetObjectsAsync(queryDefinition);

        foreach (var subscription in objects)
        {
            string instanceId;

            instanceId = await starter.ScheduleNewOrchestrationInstanceAsync(
                "Zoho_SubscriptionsCheck_Orchestrator",
                input: new SubscriptionsCheckInput(
                    subscription.SleekflowCompanyId,
                    subscription));

            var httpManagementPayload = starter.CreateHttpManagementPayload(instanceId);
            if (httpManagementPayload == null)
            {
                _logger.LogInformation(
                    "Unable to get Zoho_SubscriptionsCheck_Orchestrator httpManagementPayload");
            }

            await _zohoSubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{ZohoSubscription.PropertyNameDurablePayload}",
                        httpManagementPayload)
                });
        }
    }
}