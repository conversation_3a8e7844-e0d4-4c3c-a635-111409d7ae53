﻿using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.CustomCatalogs;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;
using Sleekflow.CommerceHub.Models.Workers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Workers.Triggers;

public class ProcessCustomCatalogCsvOrchestrator : ITrigger
{
    public class ProcessCustomCatalogCsvOrchestratorCustomStatusOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("my_custom_catalog_csv_reader_state")]
        public MyCustomCatalogCsvReaderState? MyCustomCatalogCsvReaderState { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvOrchestratorCustomStatusOutput(
            long count,
            MyCustomCatalogCsvReaderState? myCustomCatalogCsvReaderState,
            DateTime lastUpdateTime)
        {
            Count = count;
            MyCustomCatalogCsvReaderState = myCustomCatalogCsvReaderState;
            LastUpdateTime = lastUpdateTime;
        }
    }

    public class ProcessCustomCatalogCsvOrchestratorOutput
    {
        [JsonProperty("total_count")]
        public long TotalCount { get; set; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvOrchestratorOutput(long totalCount)
        {
            TotalCount = totalCount;
        }
    }

    [Function("ProcessCustomCatalogCsv_Orchestrator")]
    public async Task<ProcessCustomCatalogCsvOrchestratorOutput> RunOrchestrator(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var processCustomCatalogCsvInput = context.GetInput<ProcessCustomCatalogCsvInput>();

        context.SetCustomStatus(
            new ProcessCustomCatalogCsvOrchestratorCustomStatusOutput(
                0,
                null,
                context.CurrentUtcDateTime));

        var taskOptions = new TaskOptions(new TaskRetryOptions(new RetryPolicy(5, TimeSpan.FromSeconds(16), 2)));

        await context
            .CallActivityAsync<PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusOutput>(
                "PatchCustomCatalogFileProcessStatus",
                new PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusInput(
                    processCustomCatalogCsvInput.SleekflowCompanyId,
                    processCustomCatalogCsvInput.CustomCatalogFileId,
                    CustomCatalogFileProcessStatuses.Processing),
                taskOptions);

        var totalCount = 0L;
        var myCustomCatalogCsvReaderState = null as MyCustomCatalogCsvReaderState;

        try
        {
            while (true)
            {
                var processCustomCatalogCsvBatchOutput = await context
                    .CallActivityAsync<ProcessCustomCatalogCsvBatch.ProcessCustomCatalogCsvBatchOutput>(
                        "ProcessCustomCatalogCsv_Batch",
                        new ProcessCustomCatalogCsvBatch.ProcessCustomCatalogCsvBatchInput(
                            processCustomCatalogCsvInput.SleekflowCompanyId,
                            processCustomCatalogCsvInput.CustomCatalogFileId,
                            myCustomCatalogCsvReaderState),
                        taskOptions);

                totalCount += processCustomCatalogCsvBatchOutput.Count;
                myCustomCatalogCsvReaderState = processCustomCatalogCsvBatchOutput.MyCustomCatalogCsvReaderState;

                context.SetCustomStatus(
                    new ProcessCustomCatalogCsvOrchestratorCustomStatusOutput(
                        totalCount,
                        myCustomCatalogCsvReaderState,
                        context.CurrentUtcDateTime));

                if (myCustomCatalogCsvReaderState == null || myCustomCatalogCsvReaderState.IsCompleted)
                {
                    break;
                }
            }

            await context
                .CallActivityAsync<
                    PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusOutput>(
                    "PatchCustomCatalogFileProcessStatus",
                    new PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusInput(
                        processCustomCatalogCsvInput.SleekflowCompanyId,
                        processCustomCatalogCsvInput.CustomCatalogFileId,
                        CustomCatalogFileProcessStatuses.Completed),
                    taskOptions);
        }
        catch (Exception)
        {
            await context
                .CallActivityAsync<
                    PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusOutput>(
                    "PatchCustomCatalogFileProcessStatus",
                    new PatchCustomCatalogFileProcessStatus.PatchCustomCatalogFileProcessStatusInput(
                        processCustomCatalogCsvInput.SleekflowCompanyId,
                        processCustomCatalogCsvInput.CustomCatalogFileId,
                        CustomCatalogFileProcessStatuses.Failed),
                    taskOptions);
        }

        return new ProcessCustomCatalogCsvOrchestratorOutput(totalCount);
    }
}