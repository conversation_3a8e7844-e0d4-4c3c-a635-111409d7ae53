using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Providers;

namespace Sleekflow.EmailHub.Triggers.Emails;

[TriggerGroup(ControllerNames.Emails)]
public class DeleteEmail : ITrigger
{
    private readonly IEmailProviderSelector _providerSelector;

    public DeleteEmail(IEmailProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class DeleteEmailInput
    {
        [JsonConstructor]
        public DeleteEmailInput(string emailId)
        {
            EmailId = emailId;
        }

        [JsonProperty("email_id")]
        [Required]
        public string EmailId { get; set; }
    }

    public class DeleteEmailOutput
    {
    }

    public async Task<DeleteEmailOutput> F(DeleteEmailInput deleteEmailInput)
    {
        var provider = _providerSelector.GetEmailProvider(ProviderNames.Gmail);
        await provider.DeleteEmailAsync(deleteEmailInput.EmailId);
        return new DeleteEmailOutput();
    }
}