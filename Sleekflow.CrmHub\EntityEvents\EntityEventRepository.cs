﻿using Sleekflow.CrmHub.Models.EntityEvents;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.EntityEvents;

public interface IEntityEventRepository : IRepository<EntityEvent>
{
}

public class EntityEventRepository : BaseRepository<EntityEvent>, IEntityEventRepository, ISingletonService
{
    public EntityEventRepository(
        ILogger<BaseRepository<EntityEvent>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}