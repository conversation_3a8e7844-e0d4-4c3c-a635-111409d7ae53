using System.Globalization;
using Newtonsoft.Json.Linq;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Triggers.Events;
using Sleekflow.FlowHub.Triggers.Workflows;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.Workflows;

public class WorkflowTestRunTests
{
    [Test]
    public async Task PostAndPutAndPatchAndGetAndDeleteTest()
    {
        var mockContactId = "contactId";
        var mockCompanyId = nameof(PostAndPutAndPatchAndGetAndDeleteTest);
        var mockStaffId = "mock-staff-id";

        // /Events/CreateWorkflow
        var createWorkflowInput = new CreateWorkflow.CreateWorkflowInput(
            mockCompanyId,
            new WorkflowTriggers(
                null,
                null,
                new WorkflowTrigger("{{ true }}"),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            new List<Step>
            {
                new CallStep<HttpGetStepArgs>(
                    "initial-step",
                    "Initial Step",
                    null,
                    null,
                    "http.get",
                    new HttpGetStepArgs(
                        "https://webhook.site/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12",
                        new Dictionary<string, string?>
                        {
                            {
                                "X-Key", "'ABC'"
                            }
                        })),
                new CallStep<HttpPostStepArgs>(
                    "post-step",
                    "Post Step",
                    null,
                    null,
                    "http.post",
                    new HttpPostStepArgs(
                        "https://webhook.site/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12",
                        new Dictionary<string, string?>
                        {
                            {
                                "X-Key", "'ABC'"
                            }
                        },
                        null,
                        "body_str")),
                new CallStep<HttpPutStepArgs>(
                    "put-step",
                    "Put Step",
                    null,
                    null,
                    "http.put",
                    new HttpPutStepArgs(
                        "https://webhook.site/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12",
                        new Dictionary<string, string?>
                        {
                            {
                                "X-Key", "'ABC'"
                            }
                        },
                        null,
                        "body_str")),
                new CallStep<HttpPatchStepArgs>(
                    "patch-step",
                    "Patch Step",
                    null,
                    null,
                    "http.patch",
                    new HttpPatchStepArgs(
                        "https://webhook.site/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12",
                        new Dictionary<string, string?>
                        {
                            {
                                "X-Key", "'ABC'"
                            }
                        },
                        null,
                        "body_str")),
                new CallStep<HttpDeleteStepArgs>(
                    "delete-step",
                    "Delete Step",
                    null,
                    null,
                    "http.delete",
                    new HttpDeleteStepArgs(
                        "https://webhook.site/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12",
                        new Dictionary<string, string?>
                        {
                            {
                                "X-Key", "'ABC'"
                            }
                        })),
            }.Select(s => JObject.FromObject(s)).ToList(),
            "My Workflow 1",
            WorkflowType.Normal,
            workflowGroupId: null,
            new Dictionary<string, object?>(),
            "v1",
            mockStaffId,
            null,
            null);
        var createWorkflowScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createWorkflowInput).ToUrl("/Workflows/CreateWorkflow");
            });
        var createWorkflowOutputOutput = await createWorkflowScenarioResult
            .ReadAsJsonAsync<Output<CreateWorkflow.CreateWorkflowOutput>>();

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/EnableWorkflow
        var enableWorkflowInput = new EnableWorkflow.EnableWorkflowInput(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId,
            null);
        var enableWorkflowScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(enableWorkflowInput).ToUrl("/Workflows/EnableWorkflow");
            });
        var enableWorkflowOutputOutput = await enableWorkflowScenarioResult
            .ReadAsJsonAsync<Output<EnableWorkflow.EnableWorkflowOutput>>();

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // /Events/OnContactCreatedEvent
        var onContactCreatedEventInput = new OnContactCreatedEvent.OnContactCreatedEventInput(
            mockCompanyId,
            mockContactId,
            new OnContactCreatedEventBody(
                DateTimeOffset.UtcNow,
                mockStaffId,
                new List<string>(),
                new Dictionary<string, object?>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"));
        var onContactCreatedEventScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(onContactCreatedEventInput).ToUrl("/Events/OnContactCreatedEvent");
            });
        var onContactCreatedEventOutputOutput = await onContactCreatedEventScenarioResult
            .ReadAsJsonAsync<Output<OnContactCreatedEvent.OnContactCreatedEventOutput>>();

        Assert.That(onContactCreatedEventOutputOutput, Is.Not.Null);
        Assert.That(onContactCreatedEventOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowInput = new DeleteWorkflow.DeleteWorkflowInput(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId,
            null);
        var deleteWorkflowScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteWorkflowInput).ToUrl("/Workflows/DeleteWorkflow");
            });
        var deleteWorkflowOutputOutput = await deleteWorkflowScenarioResult
            .ReadAsJsonAsync<Output<DeleteWorkflow.DeleteWorkflowOutput>>();

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task TryCatchTest()
    {
        var mockContactId = "contactId";
        var mockCompanyId = nameof(TryCatchTest);
        var mockStaffId = "mock-staff-id";

        // /Events/CreateWorkflow
        var createWorkflowInput = new CreateWorkflow.CreateWorkflowInput(
            mockCompanyId,
            new WorkflowTriggers(
                null,
                null,
                new WorkflowTrigger("{{ true }}"),
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null),
            WorkflowEnrollmentSettings.Default(),
            WorkflowScheduleSettings.Default(),
            new List<Step>
            {
                new TryCatchStep(
                    "initial-step",
                    "Initial Step",
                    null,
                    null,
                    new TryCatchStepTry(
                        new CallStep<HttpGetStepArgs>(
                            "try-step",
                            "Try Step",
                            null,
                            null,
                            "http.get",
                            new HttpGetStepArgs(
                                "https://localhost/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12",
                                new Dictionary<string, string?>
                                {
                                    {
                                        "X-Key", "'ABC'"
                                    }
                                }))),
                    new TryCatchStepCatch(
                        "e",
                        new CallStep<HttpPostStepArgs>(
                            "post-step",
                            "Post Step",
                            null,
                            null,
                            "http.post",
                            new HttpPostStepArgs(
                                "https://webhook.site/f00ac6b6-4ccc-4c94-afd1-22edaaa58f12",
                                new Dictionary<string, string?>
                                {
                                    {
                                        "X-Key", "{{ sys_var_dict.e.error_message }}"
                                    }
                                },
                                null,
                                "body_str"))))
            }.Select(s => JObject.FromObject(s)).ToList(),
            "My Workflow 1",
            WorkflowType.Normal,
            workflowGroupId: null,
            new Dictionary<string, object?>(),
            "v1",
            mockStaffId,
            null,
            null);
        var createWorkflowScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(createWorkflowInput).ToUrl("/Workflows/CreateWorkflow");
            });
        var createWorkflowOutputOutput = await createWorkflowScenarioResult
            .ReadAsJsonAsync<Output<CreateWorkflow.CreateWorkflowOutput>>();

        Assert.That(createWorkflowOutputOutput, Is.Not.Null);
        Assert.That(createWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/EnableWorkflow
        var enableWorkflowInput = new EnableWorkflow.EnableWorkflowInput(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowVersionedId,
            mockStaffId,
            null);
        var enableWorkflowScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(enableWorkflowInput).ToUrl("/Workflows/EnableWorkflow");
            });
        var enableWorkflowOutputOutput = await enableWorkflowScenarioResult
            .ReadAsJsonAsync<Output<EnableWorkflow.EnableWorkflowOutput>>();

        Assert.That(enableWorkflowOutputOutput, Is.Not.Null);
        Assert.That(enableWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        Assert.That(
            enableWorkflowOutputOutput!.Data.Workflow.ActivationStatus,
            Is.EqualTo(WorkflowActivationStatuses.Active));

        // /Events/OnContactCreatedEvent
        var onContactCreatedEventInput = new OnContactCreatedEvent.OnContactCreatedEventInput(
            mockCompanyId,
            mockContactId,
            new OnContactCreatedEventBody(
                DateTimeOffset.UtcNow,
                mockStaffId,
                new List<string>(),
                new Dictionary<string, object?>(),
                "contactId",
                new Dictionary<string, object?>(),
                "staffIdentityId"));
        var onContactCreatedEventScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(onContactCreatedEventInput).ToUrl("/Events/OnContactCreatedEvent");
            });
        var onContactCreatedEventOutputOutput = await onContactCreatedEventScenarioResult
            .ReadAsJsonAsync<Output<OnContactCreatedEvent.OnContactCreatedEventOutput>>();

        Assert.That(onContactCreatedEventOutputOutput, Is.Not.Null);
        Assert.That(onContactCreatedEventOutputOutput!.HttpStatusCode, Is.EqualTo(200));

        // /Workflows/DeleteWorkflow
        var deleteWorkflowInput = new DeleteWorkflow.DeleteWorkflowInput(
            mockCompanyId,
            createWorkflowOutputOutput.Data.Workflow.WorkflowId,
            mockStaffId,
            null);
        var deleteWorkflowScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(deleteWorkflowInput).ToUrl("/Workflows/DeleteWorkflow");
            });
        var deleteWorkflowOutputOutput = await deleteWorkflowScenarioResult
            .ReadAsJsonAsync<Output<DeleteWorkflow.DeleteWorkflowOutput>>();

        Assert.That(deleteWorkflowOutputOutput, Is.Not.Null);
        Assert.That(deleteWorkflowOutputOutput!.HttpStatusCode, Is.EqualTo(200));
    }
}