﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.Hubspot;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.Ids;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Commons;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.BusinessWabaCreditTransfer.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Filters;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Validators;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.Webhooks.WhatsappCloudApis.BusinessBalance;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Balances;

public interface IWabaLevelCreditManagementService
{
    Task<BusinessBalance> InitWabaBalancesInBusinessBalanceAsync(
        HashSet<string> facebookWabaIds,
        BusinessBalance businessBalance);

    Task<BusinessBalance> SwitchFromBusinessLevelToWabaLevelCreditManagementAsync(
        string facebookBusinessId,
        string eTag,
        CreditAllocationObject creditAllocation,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    Task<BusinessBalance> SwitchFromWabaLevelToBusinessLevelCreditManagementAsync(
        string facebookBusinessId,
        string eTag,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    Task<BusinessBalance> CreditTransferFromCreditAllocationAsync(
        string facebookBusinessId,
        string eTag,
        CreditAllocationObject creditAllocation,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds);

    Task<BusinessBalance> CreditTransferFromWabaDeletionWebhookAsync(
        string facebookWabaId,
        string facebookBusinessId,
        string businessBalanceETag);

    Task CalculateCreditTransferTransactionLogsAsync(
        BusinessBalance businessBalance,
        List<BusinessBalanceTransactionLog> creditTransferTransactionLogs,
        List<CreditTransferFromTo> creditTransfers,
        string? sleekflowCompanyId,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        CancellationToken cancellationToken = default);

    Task<List<CreditTransferTransactionLogDto>> GetUnCalculatedCreditTransferTransactionLogsAsync(
        BusinessBalance businessBalance);

    Task<(List<CreditTransferTransactionLogDto> CreditTransferTransactionLogDtos, string? NextContinuationToken)>
        GetCreditTransferTransactionLogsAsync(
            string facebookBusinessId,
            string? facebookWabaId,
            DateTimeOffsetRange? createdAtRange,
            DateTimeOffsetRange? updatedAtRange,
            int? limit,
            string? orderBy,
            string? order,
            string? continuationToken);
}

public class WabaLevelCreditManagementService : IWabaLevelCreditManagementService, ISingletonService
{
    private readonly IIdService _idService;
    private readonly IAuditLogService _auditLogService;
    private readonly ILogger<WabaLevelCreditManagementService> _logger;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceTransactionLogRepository _businessBalanceTransactionLogRepository;
    private readonly IBus _bus;
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWhatsappCloudApiBusinessBalanceWebhookService _whatsappCloudApiBusinessBalanceWebhookService;

    public WabaLevelCreditManagementService(
        IIdService idService,
        IAuditLogService auditLogService,
        ILogger<WabaLevelCreditManagementService> logger,
        IBusinessBalanceService businessBalanceService,
        IWabaService wabaService,
        IBusinessBalanceTransactionLogRepository businessBalanceTransactionLogRepository,
        IBus bus,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IWhatsappCloudApiBusinessBalanceWebhookService whatsappCloudApiBusinessBalanceWebhookService)
    {
        _idService = idService;
        _auditLogService = auditLogService;
        _logger = logger;
        _businessBalanceService = businessBalanceService;
        _wabaService = wabaService;
        _businessBalanceTransactionLogRepository = businessBalanceTransactionLogRepository;
        _bus = bus;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _whatsappCloudApiBusinessBalanceWebhookService = whatsappCloudApiBusinessBalanceWebhookService;
    }

    public async Task<BusinessBalance> SwitchFromBusinessLevelToWabaLevelCreditManagementAsync(
        string facebookBusinessId,
        string eTag,
        CreditAllocationObject creditAllocation,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        if (eTag != businessBalance.ETag)
        {
            throw new SfConcurrentETagException(
                "BusinessBalance",
                facebookBusinessId,
                eTag,
                businessBalance.ETag!);
        }

        if (businessBalance.IsByWabaBillingEnabled == true)
        {
            throw new SfNotSupportedOperationException(
                $"Business balance {businessBalance.Id} already enabled WABA level credit management");
        }

        var unCalculatedCreditTransferTransactionLogs =
            await _businessBalanceTransactionLogService.GetUnCalculatedLogsWithFacebookBusinessIdAsync(
                facebookBusinessId,
                new List<string>()
                {
                    TransactionTypes.CreditTransfer
                });

        // Validate the current incoming credit allocation with un-calculated credit transfer transaction logs
        //  Construct the most updated business balance with un-calculated credit transfer transaction logs
        //  Check the validity of the credit allocation, compared with constructed business balance
        businessBalance.ValidateCreditAllocation(creditAllocation, unCalculatedCreditTransferTransactionLogs);

        var beforeSwitchToWabaLevelBusinessBalanceSnapshot =
            JsonConvert.DeserializeObject<BusinessBalance>(JsonConvert.SerializeObject(businessBalance));

        businessBalance.IsByWabaBillingEnabled = true;
        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);
        var facebookWabaIds = wabas.Select(x => x.FacebookWabaId).ToHashSet();

        businessBalance = await InitWabaBalancesInBusinessBalanceAsync(facebookWabaIds, businessBalance);

        var businessBalanceUpsertState = await _businessBalanceService.UpsertBusinessBalanceAsync(businessBalance);

        if (businessBalanceUpsertState == 0)
        {
            throw new SfInternalErrorException(
                "Unable to upsert business balance from switching to WABA level credit management");
        }

        var creditTransfers = creditAllocation.CreditTransfers;

        if (creditTransfers == null || !creditTransfers.Any())
        {
            throw new SfInternalErrorException(
                $"Credit transfers are required for credit allocation in Facebook Business Id {facebookBusinessId} Business Balance");
        }

        businessBalance = await _businessBalanceService.GetAsync(businessBalance.Id, facebookBusinessId);

        var creditTransferTransactionLogs =
            await CreditTransferFromBusinessToWabasAndGetTransactionLogsAsync(
                facebookBusinessId,
                businessBalance,
                creditTransfers);

        if (!creditTransferTransactionLogs.Any())
        {
            _logger.LogError(
                "no credit transfer transaction logs created for credit allocation {CreditAllocation} in Facebook Business Id {FacebookBusinessId} Business Balance",
                JsonConvert.SerializeObject(creditAllocation),
                facebookBusinessId);

            throw new SfInternalErrorException(
                $"no credit transfer transaction logs created for credit allocation in Facebook Business Id {facebookBusinessId} Business Balance");
        }

        await _bus.Publish(
            new OnCloudApiCalculateCreditTransferTransactionLogsEvent(
                businessBalance,
                creditTransferTransactionLogs,
                creditTransfers,
                sleekflowCompanyId,
                sleekflowStaffId,
                sleekflowStaffTeamIds));

        await _auditLogService.AuditBusinessWabaLevelCreditManagementSwitchByUserAsync(
            beforeSwitchToWabaLevelBusinessBalanceSnapshot!,
            facebookBusinessId,
            facebookBusinessId,
            AuditingOperation.SwitchWabaLevelCreditManagement,
            new Dictionary<string, object?>()
            {
                {
                    "change", businessBalance
                },
                {
                    "sleekflow_company_id", sleekflowCompanyId
                },
                {
                    "sleekflow_staff_id", sleekflowStaffId
                },
                {
                    "sleekflow_staff_team_ids", sleekflowStaffTeamIds
                }
            });

        businessBalance = await _businessBalanceService.GetAsync(businessBalance.Id, facebookBusinessId);

        return businessBalance;
    }

    public async Task<BusinessBalance> SwitchFromWabaLevelToBusinessLevelCreditManagementAsync(
        string facebookBusinessId,
        string eTag,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        if (eTag != businessBalance.ETag)
        {
            throw new SfConcurrentETagException(
                "BusinessBalance",
                facebookBusinessId,
                eTag,
                businessBalance.ETag!);
        }

        if (businessBalance.IsByWabaBillingEnabled is null or false)
        {
            throw new SfNotSupportedOperationException(
                $"Business balance {businessBalance.Id} already enabled Business level credit management");
        }

        var beforeSwitchToBusinessLevelBusinessBalanceSnapshot =
            JsonConvert.DeserializeObject<BusinessBalance>(JsonConvert.SerializeObject(businessBalance));

        var wabaBalances =
            JsonConvert.DeserializeObject<List<WabaBalance>>(JsonConvert.SerializeObject(businessBalance.WabaBalances));

        if (wabaBalances != null)
        {
            var creditTransfers = wabaBalances.Select(
                    wabaBalance => new CreditTransferFromTo(
                        new CreditTransferTargetObject(
                            null,
                            wabaBalance.FacebookWabaId,
                            FacebookTargetTypes.FacebookWaba),
                        new CreditTransferTargetObject(facebookBusinessId, null, FacebookTargetTypes.FacebookBusiness),
                        new Money(CurrencyIsoCodes.USD, wabaBalance.Balance.Amount),
                        CreditTransferTypes.NormalTransferFromWabaToBusiness))
                .ToList();

            var creditTransferTransactionLogs =
                await CreditTransferFromWabaToBusinessAndGetTransactionLogsAsync(
                    facebookBusinessId,
                    businessBalance,
                    creditTransfers);

            if (!creditTransferTransactionLogs.Any())
            {
                _logger.LogError(
                    "no credit transfer transaction logs created for credit transfers {CreditTransfers} in Facebook Business Id {FacebookBusinessId} Business Balance",
                    JsonConvert.SerializeObject(creditTransfers),
                    facebookBusinessId);

                throw new SfInternalErrorException(
                    $"no credit transfer transaction logs created for credit allocation in Facebook Business Id {facebookBusinessId} Business Balance");
            }

            businessBalance.IsByWabaBillingEnabled = false;
            var businessBalanceUpsertState = await _businessBalanceService.UpsertBusinessBalanceAsync(businessBalance);

            if (businessBalanceUpsertState == 0)
            {
                throw new SfInternalErrorException(
                    "Unable to upsert business balance from switching to business level credit management");
            }

            businessBalance = await _businessBalanceService.GetAsync(businessBalance.Id, businessBalance.FacebookBusinessId);

            await _bus.Publish(
                new OnCloudApiCalculateCreditTransferTransactionLogsEvent(
                    businessBalance,
                    creditTransferTransactionLogs,
                    creditTransfers,
                    sleekflowCompanyId,
                    sleekflowStaffId,
                    sleekflowStaffTeamIds));

            await _auditLogService.AuditBusinessWabaLevelCreditManagementSwitchByUserAsync(
                beforeSwitchToBusinessLevelBusinessBalanceSnapshot!,
                facebookBusinessId,
                facebookBusinessId,
                AuditingOperation.SwitchBusinessLevelCreditManagement,
                new Dictionary<string, object?>()
                {
                    {
                        "change", businessBalance
                    },
                    {
                        "sleekflow_company_id", sleekflowCompanyId
                    },
                    {
                        "sleekflow_staff_id", sleekflowStaffId
                    },
                    {
                        "sleekflow_staff_team_ids", sleekflowStaffTeamIds
                    }
                });
        }

        businessBalance = await _businessBalanceService.GetAsync(businessBalance.Id, facebookBusinessId);

        return businessBalance;
    }

    public async Task<BusinessBalance> CreditTransferFromCreditAllocationAsync(
        string facebookBusinessId,
        string eTag,
        CreditAllocationObject creditAllocation,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        // decide this credit allocation is for From-Business-To-Waba OR From-Waba-To-Business
        var isBusinessToWaba = creditAllocation.CreditTransfers.TrueForAll(
            x => x.CreditTransferType == CreditTransferTypes.NormalTransferFromBusinessToWaba);

        var isWabaToBusiness = creditAllocation.CreditTransfers.TrueForAll(
            x => x.CreditTransferType == CreditTransferTypes.NormalTransferFromWabaToBusiness);

        if (!isBusinessToWaba && !isWabaToBusiness)
        {
            throw new SfNotSupportedOperationException(
                $"Credit allocation is not all from business to waba or all from waba to business");
        }

        BusinessBalance businessBalance;

        if (isBusinessToWaba)
        {
            businessBalance = await CreditTransferFromBusinessToWabaAsync(
                facebookBusinessId,
                eTag,
                creditAllocation.CreditTransfers,
                sleekflowCompanyId,
                sleekflowStaffId,
                sleekflowStaffTeamIds);
        }
        else
        {
            businessBalance = await CreditTransferFromWabaToBusinessAsync(
                facebookBusinessId,
                eTag,
                creditAllocation.CreditTransfers,
                sleekflowCompanyId,
                sleekflowStaffId,
                sleekflowStaffTeamIds);
        }

        return businessBalance;
    }

    public async Task<BusinessBalance> CreditTransferFromWabaDeletionWebhookAsync(
        string facebookWabaId,
        string facebookBusinessId,
        string businessBalanceETag)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        if (businessBalanceETag != businessBalance.ETag)
        {
            throw new SfConcurrentETagException(
                "BusinessBalance",
                facebookBusinessId,
                businessBalanceETag,
                businessBalance.ETag!);
        }

        var wabaBalance = businessBalance.WabaBalances?.Find(wb => wb.FacebookWabaId == facebookWabaId);

        if (wabaBalance is { Balance.Amount: > 0 })
        {
            var creditTransfers = new List<CreditTransferFromTo>()
            {
                new (
                    new CreditTransferTargetObject(
                        null,
                        facebookWabaId,
                        FacebookTargetTypes.FacebookWaba),
                    new CreditTransferTargetObject(facebookBusinessId, null, FacebookTargetTypes.FacebookBusiness),
                    new Money(CurrencyIsoCodes.USD, wabaBalance.Balance.Amount),
                    CreditTransferTypes.SystemTransferFromWabaToBusiness)
            };

            var creditTransferTransactionLogs =
                await CreditTransferFromWabaToBusinessAndGetTransactionLogsAsync(
                    facebookBusinessId,
                    businessBalance,
                    creditTransfers);

            if (!creditTransferTransactionLogs.Any())
            {
                _logger.LogError(
                    "no credit transfer transaction logs created for credit transfer {CreditTransfer} in Facebook Business Id {FacebookBusinessId} Business Balance from waba deletion webhook received",
                    JsonConvert.SerializeObject(creditTransfers),
                    facebookBusinessId);

                throw new SfInternalErrorException(
                    $"no credit transfer transaction logs created for credit allocation in Facebook Business Id {facebookBusinessId} Business Balance from waba deletion webhook received");
            }

            await _bus.Publish(
                new OnCloudApiCalculateCreditTransferTransactionLogsEvent(
                    businessBalance,
                    creditTransferTransactionLogs,
                    creditTransfers,
                    null,
                    null,
                    null));
        }

        businessBalance = await _businessBalanceService.GetAsync(businessBalance.Id, facebookBusinessId);

        return businessBalance;
    }

    private async Task<BusinessBalance> CreditTransferFromBusinessToWabaAsync(
        string facebookBusinessId,
        string eTag,
        List<CreditTransferFromTo> creditTransfers,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        if (eTag != businessBalance.ETag)
        {
            throw new SfConcurrentETagException(
                "BusinessBalance",
                facebookBusinessId,
                eTag,
                businessBalance.ETag!);
        }

        var unCalculatedCreditTransferTransactionLogs =
            await _businessBalanceTransactionLogService.GetUnCalculatedLogsWithFacebookBusinessIdAsync(
                facebookBusinessId,
                new List<string>()
                {
                    TransactionTypes.CreditTransfer
                });

        // Validate the current incoming credit allocation with un-calculated credit transfer transaction logs
        //  Construct the most updated business balance with un-calculated credit transfer transaction logs
        //  Check the validity of the credit allocation, compared with constructed business balance
        businessBalance.ValidateCreditAllocation(
            new CreditAllocationObject(creditTransfers),
            unCalculatedCreditTransferTransactionLogs);

        var creditTransferTransactionLogs =
            await CreditTransferFromBusinessToWabasAndGetTransactionLogsAsync(
                facebookBusinessId,
                businessBalance,
                creditTransfers);

        if (!creditTransferTransactionLogs.Any())
        {
            _logger.LogError(
                "no credit transfer transaction logs created for credit transfer {CreditTransfer} in Facebook Business Id {FacebookBusinessId} Business Balance",
                JsonConvert.SerializeObject(creditTransfers),
                facebookBusinessId);

            throw new SfInternalErrorException(
                $"no credit transfer transaction logs created for credit allocation in Facebook Business Id {facebookBusinessId} Business Balance");
        }

        await _bus.Publish(
            new OnCloudApiCalculateCreditTransferTransactionLogsEvent(
                businessBalance,
                creditTransferTransactionLogs,
                creditTransfers,
                sleekflowCompanyId,
                sleekflowStaffId,
                sleekflowStaffTeamIds));

        businessBalance = await _businessBalanceService.GetAsync(businessBalance.Id, facebookBusinessId);

        return businessBalance;
    }

    private async Task<BusinessBalance> CreditTransferFromWabaToBusinessAsync(
        string facebookBusinessId,
        string eTag,
        List<CreditTransferFromTo> creditTransfers,
        string sleekflowCompanyId,
        string sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds)
    {
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(facebookBusinessId);
        }

        if (eTag != businessBalance.ETag)
        {
            throw new SfConcurrentETagException(
                "BusinessBalance",
                facebookBusinessId,
                eTag,
                businessBalance.ETag!);
        }

        var unCalculatedCreditTransferTransactionLogs =
            await _businessBalanceTransactionLogService.GetUnCalculatedLogsWithFacebookBusinessIdAsync(
                facebookBusinessId,
                new List<string>()
                {
                    TransactionTypes.CreditTransfer
                });

        // Validate the current incoming credit allocation with un-calculated credit transfer transaction logs
        //  Construct the most updated business balance with un-calculated credit transfer transaction logs
        //  Check the validity of the credit allocation, compared with constructed business balance
        businessBalance.ValidateCreditAllocation(
            new CreditAllocationObject(creditTransfers),
            unCalculatedCreditTransferTransactionLogs);

        var creditTransferTransactionLogs =
            await CreditTransferFromWabaToBusinessAndGetTransactionLogsAsync(
                facebookBusinessId,
                businessBalance,
                creditTransfers);

        if (!creditTransferTransactionLogs.Any())
        {
            _logger.LogError(
                "no credit transfer transaction logs created for credit transfer {CreditTransfer} in Facebook Business Id {FacebookBusinessId} Business Balance",
                JsonConvert.SerializeObject(creditTransfers),
                facebookBusinessId);

            throw new SfInternalErrorException(
                $"no credit transfer transaction logs created for credit allocation in Facebook Business Id {facebookBusinessId} Business Balance");
        }

        await _bus.Publish(
            new OnCloudApiCalculateCreditTransferTransactionLogsEvent(
                businessBalance,
                creditTransferTransactionLogs,
                creditTransfers,
                sleekflowCompanyId,
                sleekflowStaffId,
                sleekflowStaffTeamIds));

        businessBalance = await _businessBalanceService.GetAsync(businessBalance.Id, facebookBusinessId);

        return businessBalance;
    }

    public async Task<BusinessBalance> InitWabaBalancesInBusinessBalanceAsync(
        HashSet<string> facebookWabaIds,
        BusinessBalance businessBalance)
    {
        businessBalance.WabaBalances ??=[];

        foreach (var facebookWabaId in facebookWabaIds)
        {
            if (businessBalance.WabaBalances.Exists(wb => wb.FacebookWabaId == facebookWabaId))
            {
                continue;
            }

            var historyTransactionLogs =
                await _businessBalanceTransactionLogService.GetAllCalculatedTransactionLogAsync(
                    businessBalance.FacebookBusinessId,
                    facebookWabaId,
                    businessBalance.CreatedAt.ToUnixTimeSeconds(),
                    DateTimeOffset.UtcNow.ToUnixTimeSeconds());

            businessBalance.WabaBalances.Add(
                new WabaBalance(
                    _idService.GetId(SysTypeNames.WabaBalance),
                    facebookWabaId,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow,
                    historyTransactionLogs));
        }

        businessBalance.UpdatedAt = DateTimeOffset.UtcNow;

        return businessBalance;
    }

    public async Task<List<CreditTransferTransactionLogDto>>
        GetUnCalculatedCreditTransferTransactionLogsAsync(
            BusinessBalance businessBalance)
    {
        var unCalculatedCreditTransferTransactionLogDtos =
            new List<CreditTransferTransactionLogDto>();

        if (businessBalance.IsByWabaBillingEnabled is null or false)
        {
            return unCalculatedCreditTransferTransactionLogDtos;
        }

        var facebookBusinessId = businessBalance.FacebookBusinessId;

        var unCalculatedCreditTransferTransactionLogs =
            await _businessBalanceTransactionLogService.GetUnCalculatedLogsWithFacebookBusinessIdAsync(
                facebookBusinessId,
                new List<string>()
                {
                    TransactionTypes.CreditTransfer
                });

        if (unCalculatedCreditTransferTransactionLogs.Any())
        {
            unCalculatedCreditTransferTransactionLogDtos.AddRange(
                unCalculatedCreditTransferTransactionLogs.Select(
                    log => new CreditTransferTransactionLogDto(log)));
        }

        return unCalculatedCreditTransferTransactionLogDtos;
    }

    public async
        Task<(List<CreditTransferTransactionLogDto> CreditTransferTransactionLogDtos, string? NextContinuationToken)>
        GetCreditTransferTransactionLogsAsync(
            string facebookBusinessId,
            string? facebookWabaId,
            DateTimeOffsetRange? createdAtRange,
            DateTimeOffsetRange? updatedAtRange,
            int? limit,
            string? orderBy,
            string? order,
            string? continuationToken)
    {
        var businessBalanceTransactionLogFilter = new BusinessBalanceTransactionLogFilter(
            TransactionTypes.CreditTransfer,
            facebookBusinessId,
            facebookWabaId,
            null,
            null,
            null,
            null,
            null,
            null,
            createdAtRange,
            updatedAtRange,
            orderBy,
            order);

        var (filteredTransactionLogs, nextContinuationToken) =
            await _businessBalanceTransactionLogRepository.FilterCreditTransferTransactionLogs(
                businessBalanceTransactionLogFilter,
                continuationToken,
                limit ?? 10000);

        return (filteredTransactionLogs.Select(x => new CreditTransferTransactionLogDto(x)).ToList(),
            nextContinuationToken);
    }

    private async Task<List<BusinessBalanceTransactionLog>> CreditTransferFromBusinessToWabasAndGetTransactionLogsAsync(
        string facebookBusinessId,
        BusinessBalance businessBalance,
        List<CreditTransferFromTo> creditTransfers)
    {
        var creditTransferTransactionLogs = new List<BusinessBalanceTransactionLog>();

        foreach (var creditTransfer in creditTransfers)
        {
            var facebookWabaId = creditTransfer.CreditTransferTo.FacebookWabaId;

            if (string.IsNullOrEmpty(facebookWabaId))
            {
                throw new SfInternalErrorException(
                    $"Facebook WABA ID is required for credit transfer in Facebook Business Id {facebookBusinessId} Business Balance");
            }

            if (creditTransfer.CreditTransferType != CreditTransferTypes.NormalTransferFromBusinessToWaba)
            {
                _logger.LogError(
                    "not transfer from business {FacebookBusinessId} to waba {FacebookWabaId} type allocation",
                    facebookBusinessId,
                    facebookWabaId);

                continue;
            }

            var utcNow = DateTimeOffset.UtcNow;

            var uniqueId =
                $"{facebookWabaId}/{utcNow.ToUnixTimeSeconds().ToString()}/CreditTransferFromBusinessToWaba/{creditTransfer.CreditTransferAmount.Amount}_{creditTransfer.CreditTransferAmount.CurrencyIsoCode}";

            var isDuplicateUniqueId =
                await _businessBalanceTransactionLogRepository.IsDuplicateUniqueId(uniqueId, facebookBusinessId);

            if (isDuplicateUniqueId)
            {
                var creditTransferBusinessBalanceTransactionLogs =
                    await _businessBalanceTransactionLogRepository.GetObjectsAsync(
                        x => x.FacebookBusinessId == facebookBusinessId && x.UniqueId == uniqueId);

                _logger.LogError(
                    "A duplicate unique id is found:{UniqueId}, credit transfer from {FacebookBusinessId} business to waba {FacebookWabaId}: {CreditTransferBusinessBalanceTransactionLogs}",
                    uniqueId,
                    facebookBusinessId,
                    facebookWabaId,
                    JsonConvert.SerializeObject(creditTransferBusinessBalanceTransactionLogs));

                continue;
            }

            var businessBalanceTransactionLog = await _businessBalanceTransactionLogRepository.CreateAndGetAsync(
                new BusinessBalanceTransactionLog(
                    _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
                    facebookWabaId,
                    facebookBusinessId,
                    uniqueId,
                    new Money(CurrencyIsoCodes.USD, 0),
                    new Money(CurrencyIsoCodes.USD, 0),
                    new Money(CurrencyIsoCodes.USD, 0),
                    new Money(CurrencyIsoCodes.USD, 0),
                    TransactionTypes.CreditTransfer,
                    false,
                    null,
                    null,
                    businessBalance.MarkupProfile,
                    creditTransfer,
                    null,
                    utcNow,
                    utcNow),
                facebookBusinessId);

            creditTransferTransactionLogs.Add(businessBalanceTransactionLog);
        }

        return creditTransferTransactionLogs;
    }

    private async Task<List<BusinessBalanceTransactionLog>> CreditTransferFromWabaToBusinessAndGetTransactionLogsAsync(
        string facebookBusinessId,
        BusinessBalance businessBalance,
        List<CreditTransferFromTo> creditTransfers)
    {
        var creditTransferTransactionLogs = new List<BusinessBalanceTransactionLog>();

        foreach (var creditTransfer in creditTransfers)
        {
            var facebookWabaId = creditTransfer.CreditTransferFrom.FacebookWabaId;

            if (string.IsNullOrEmpty(facebookWabaId))
            {
                throw new SfInternalErrorException(
                    $"Facebook WABA ID is required for credit transfer in Facebook Business Id {facebookBusinessId} Business Balance");
            }

            if (creditTransfer.CreditTransferType != CreditTransferTypes.NormalTransferFromWabaToBusiness &&
                creditTransfer.CreditTransferType != CreditTransferTypes.SystemTransferFromWabaToBusiness)
            {
                _logger.LogError(
                    "not transfer from business {FacebookBusinessId} to waba {FacebookWabaId} type allocation",
                    facebookBusinessId,
                    facebookWabaId);

                continue;
            }

            var utcNow = DateTimeOffset.UtcNow;

            var uniqueId =
                $"{facebookWabaId}/{utcNow.ToUnixTimeSeconds().ToString()}/CreditTransferFromWabaToBusiness/{creditTransfer.CreditTransferAmount.Amount}_{creditTransfer.CreditTransferAmount.CurrencyIsoCode}";

            var isDuplicateUniqueId =
                await _businessBalanceTransactionLogRepository.IsDuplicateUniqueId(uniqueId, facebookBusinessId);

            if (isDuplicateUniqueId)
            {
                var creditTransferBusinessBalanceTransactionLogs =
                    await _businessBalanceTransactionLogRepository.GetObjectsAsync(
                        x => x.FacebookBusinessId == facebookBusinessId && x.UniqueId == uniqueId);

                _logger.LogError(
                    "A duplicate unique id is found:{UniqueId}, credit transfer from {FacebookBusinessId} business to waba {FacebookWabaId}: {CreditTransferBusinessBalanceTransactionLogs}",
                    uniqueId,
                    facebookBusinessId,
                    facebookWabaId,
                    JsonConvert.SerializeObject(creditTransferBusinessBalanceTransactionLogs));

                continue;
            }

            creditTransferTransactionLogs.Add(
                await _businessBalanceTransactionLogRepository.CreateAndGetAsync(
                    new BusinessBalanceTransactionLog(
                        _idService.GetId(SysTypeNames.BusinessBalanceTransactionLog),
                        facebookWabaId,
                        facebookBusinessId,
                        uniqueId,
                        new Money(CurrencyIsoCodes.USD, 0),
                        new Money(CurrencyIsoCodes.USD, 0),
                        new Money(CurrencyIsoCodes.USD, 0),
                        new Money(CurrencyIsoCodes.USD, 0),
                        TransactionTypes.CreditTransfer,
                        false,
                        null,
                        null,
                        businessBalance.MarkupProfile,
                        creditTransfer,
                        null,
                        utcNow,
                        utcNow),
                    facebookBusinessId));
        }

        return creditTransferTransactionLogs;
    }

    public async Task CalculateCreditTransferTransactionLogsAsync(
        BusinessBalance businessBalance,
        List<BusinessBalanceTransactionLog> creditTransferTransactionLogs,
        List<CreditTransferFromTo> creditTransfers,
        string? sleekflowCompanyId,
        string? sleekflowStaffId,
        List<string>? sleekflowStaffTeamIds,
        CancellationToken cancellationToken = default)
    {
        // Snapshot before calculate business balance
        var beforeCalculateBusinessBalanceSnapshot =
            JsonConvert.DeserializeObject<BusinessBalance>(
                JsonConvert.SerializeObject(businessBalance));
        var facebookBusinessId = businessBalance.FacebookBusinessId;

        if (!creditTransferTransactionLogs.Any())
        {
            return;
        }

        businessBalance.CalculateFromCreditTransferTransactionLogs(creditTransferTransactionLogs);

        var result = await _businessBalanceService.UpsertBusinessBalanceAsync(businessBalance);

        if (result == 0)
        {
            foreach (var creditTransferTransactionLog in creditTransferTransactionLogs)
            {
                creditTransferTransactionLog.SetCalculationStatusToFailed();
                await _businessBalanceTransactionLogService
                    .UpsertBusinessBalanceTransactionLogAsync(creditTransferTransactionLog);
            }

            _logger.LogError(
                "Unable to calculate credit transfer transaction logs {CreditTransferTransactionLogs} for {CreditTransfers} in Facebook Business {FacebookBusinessId} business balance",
                JsonConvert.SerializeObject(creditTransferTransactionLogs),
                JsonConvert.SerializeObject(creditTransfers),
                facebookBusinessId);

            throw new SfInternalErrorException(
                $"Unable to calculate credit transfer transaction logs in Facebook Business {facebookBusinessId} business balance");
        }

        foreach (var creditTransferTransactionLog in creditTransferTransactionLogs)
        {
            await _businessBalanceTransactionLogRepository.UpsertAsync(
                creditTransferTransactionLog,
                creditTransferTransactionLog.FacebookBusinessId,
                eTag: creditTransferTransactionLog.ETag,
                cancellationToken: cancellationToken);
        }

        var isCreditTransferBySystem = creditTransfers.Exists(
            x => x.CreditTransferType == CreditTransferTypes.SystemTransferFromWabaToBusiness);

        if (isCreditTransferBySystem)
        {
            await _auditLogService.AuditBusinessBalanceAsync(
                beforeCalculateBusinessBalanceSnapshot,
                businessBalance.FacebookBusinessId,
                AuditingOperation.CreditTransferFromWabaToBusiness,
                new Dictionary<string, object?>
                {
                    {
                        "changes", businessBalance
                    }
                });
        }
        else
        {
            await _auditLogService.AuditBusinessBalanceAsync(
                beforeCalculateBusinessBalanceSnapshot,
                businessBalance.FacebookBusinessId,
                creditTransfers.Exists(
                    x => x.CreditTransferType == CreditTransferTypes.NormalTransferFromBusinessToWaba)
                    ? AuditingOperation.CreditTransferFromBusinessToWaba
                    : AuditingOperation.CreditTransferFromWabaToBusiness,
                new Dictionary<string, object?>
                {
                    {
                        "changes", businessBalance
                    },
                    {
                        "sleekflow_company_id", sleekflowCompanyId
                    },
                    {
                        "sleekflow_staff_id", sleekflowStaffId
                    },
                    {
                        "sleekflow_staff_team_ids", sleekflowStaffTeamIds
                    }
                });
        }

        var facebookWabaIds = creditTransfers
            .Select(x =>
                x.CreditTransferTo.FacebookWabaId
                ?? x.CreditTransferFrom.FacebookWabaId)
            .ToList();

        if (!facebookWabaIds.Any() || facebookWabaIds.Exists(string.IsNullOrEmpty))
        {
            throw new SfInternalErrorException(
                $"Facebook WABA IDs are required for credit transfer in Facebook Business Id {facebookBusinessId} Business Balance");
        }

        foreach (var creditTransfer in creditTransfers)
        {
            var facebookWabaId = creditTransfer.CreditTransferTo.FacebookWabaId
                                 ?? creditTransfer.CreditTransferFrom.FacebookWabaId;

            await _auditLogService.AuditBusinessWabaLevelCreditTransferAsync(
                facebookBusinessId,
                facebookWabaId!,
                facebookBusinessId,
                AuditingOperation.CreditTransferFromBusinessToWaba,
                new Dictionary<string, object?>()
                {
                    {
                        "credit_transfer", creditTransfer
                    },
                    {
                        "sleekflow_company_id", sleekflowCompanyId
                    },
                    {
                        "sleekflow_staff_id", sleekflowStaffId
                    },
                    {
                        "sleekflow_staff_team_ids", sleekflowStaffTeamIds
                    }
                });
        }

        await CheckBalanceToUnblockOrBlockWabasAsync(businessBalance);

        // send webhook to tell travis backend to inform frontend business balance has been updated
        var onWhatsappCloudApiBusinessBalanceChangedWebhooks = await _whatsappCloudApiBusinessBalanceWebhookService
            .GetOnWhatsappCloudApiBusinessBalanceChangedWebhooksAsync(
                WebhookEntityTypeNames.BusinessBalance,
                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiBusinessBalanceChanged,
                facebookBusinessId,
                cancellationToken);

        var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId);

        var unCalculatedCreditTransferTransactionLogs =
            await GetUnCalculatedCreditTransferTransactionLogsAsync(businessBalance);

        var businessBalanceDto = new BusinessBalanceDto(
            businessBalance,
            wabas,
            unCalculatedCreditTransferTransactionLogs);

        var payload = new WhatsappCloudApiWebhookOnBusinessBalanceChangedMessage(
            businessBalanceDto);

        await _whatsappCloudApiBusinessBalanceWebhookService.SendWebhooksAsync(
            onWhatsappCloudApiBusinessBalanceChangedWebhooks,
            payload,
            cancellationToken);
    }

    private async Task CheckBalanceToUnblockOrBlockWabasAsync(BusinessBalance businessBalance)
    {
        if (businessBalance.Balance.Amount < 0)
        {
            await _bus.Publish(
                new OnCloudApiBusinessOutOfBalanceEvent(
                    businessBalance.FacebookBusinessId));
        }
        else if (businessBalance.WabaBalances != null)
        {
            foreach (var wabaBalance in businessBalance.WabaBalances)
            {
                var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(wabaBalance.FacebookWabaId);

                switch (waba.MessagingFunctionLimitation)
                {
                    case MessagingFunctionLimitationType.BlockAll when wabaBalance.Balance.Amount >= 0:
                        await _wabaService.UnblockOrBlockWabaAsync(waba.Id, waba.FacebookWabaId, isUnblock: true);

                        break;
                    case null when wabaBalance.Balance.Amount < 0:
                        await _bus.Publish(
                            new OnCloudApiWabaOutOfBalanceEvent(businessBalance.FacebookBusinessId, waba.FacebookWabaId));
                        break;
                }
            }
        }
    }
}