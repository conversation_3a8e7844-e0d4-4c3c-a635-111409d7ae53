using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Scriban.Runtime;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Commons.Helpers;
using Sleekflow.FlowHub.Jsons;
using Sleekflow.FlowHub.Models.Evaluations;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.States.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.Locks;
using JsonConfig = Sleekflow.FlowHub.JsonConfigs.JsonConfig;

namespace Sleekflow.FlowHub.States;

public interface IStateAggregator
{
    Task<ProxyState> AggregateStateAssignAsync(
        ProxyState proxyState,
        Assign assign,
        EventBody? eventBody = null);

    Task<ProxyState> AggregateStateStepBodyAsync(
        ProxyState proxyState,
        string stepId,
        string bodyStr);

    Task<ProxyState> SetSysVarStringAsync(
        ProxyState proxyState,
        string key,
        string value);

    Task<long> IncrementSysCompanyVarAsync(
        ProxyState proxyState,
        string varName,
        long increment = 1);

    Task<ProxyState> AggregateStateError(
        ProxyState proxyState,
        string catchAs,
        UserFriendlyError? error);

    Task<ProxyState> AggregateStateContactDetailAsync(
        ProxyState proxyState,
        ContactDetail contactDetail);
}

public class StateAggregator : IStateAggregator, ISingletonService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ILockService _lockService;

    public StateAggregator(
        IStateEvaluator stateEvaluator,
        ILockService lockService)
    {
        _stateEvaluator = stateEvaluator;
        _lockService = lockService;
    }

    public async Task<ProxyState> AggregateStateAssignAsync(
        ProxyState proxyState,
        Assign assign,
        EventBody? eventBody = null)
    {
        var innerDictionary = proxyState.UsrVarDict.GetInnerDictionary();

        foreach (var keyValuePair in assign)
        {
            var @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    nameof(StateAggregator),
                    proxyState.Id,
                    "UsrVarDict",
                    keyValuePair.Key
                },
                TimeSpan.FromSeconds(10),
                TimeSpan.FromSeconds(30));

            try
            {
                var o = await _stateEvaluator.EvaluateExpressionAsync(
                    proxyState,
                    keyValuePair.Value,
                    eventBody);

                var valueToSet = o switch
                {
                    Dictionary<string, object?> dictionary =>
                        JsonConvert.DeserializeObject<JObject>(
                                JsonConvert.SerializeObject(dictionary))!
                            .ToNativeDictionary(),
                    JValue jValue => jValue.Value,
                    JObject jObject => jObject.ToNativeDictionary(),
                    ScriptObject scriptObject =>
                        JsonConvert.DeserializeObject<JObject>(
                                JsonConvert.SerializeObject(
                                    new Dictionary<string, object?>(scriptObject),
                                    JsonConfig.DefaultJsonSerializerSettings),
                                JsonConfig.DefaultJsonSerializerSettings)!
                            .ToNativeDictionary(),
                    ScriptArray scriptArray =>
                        JsonConvert.DeserializeObject<JArray>(
                                JsonConvert.SerializeObject(
                                    new List<object?>(scriptArray),
                                    JsonConfig.DefaultJsonSerializerSettings),
                                JsonConfig.DefaultJsonSerializerSettings)!
                            .ToNativeArray(),
                    ScriptRange scriptRange =>
                        JsonConvert.DeserializeObject<JArray>(
                                JsonConvert.SerializeObject(
                                    new List<object?>(scriptRange),
                                    JsonConfig.DefaultJsonSerializerSettings),
                                JsonConfig.DefaultJsonSerializerSettings)!
                            .ToNativeArray(),
                    _ => o
                };

                await OrleansRetryHelper.ExecuteWithRetryAsync(
                    () => innerDictionary.SetAsync(keyValuePair.Key, valueToSet),
                    operationName: "AggregateStateAssign");
            }
            finally
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }

        return proxyState;
    }

    public async Task<ProxyState> AggregateStateStepBodyAsync(
        ProxyState proxyState,
        string stepId,
        string bodyStr)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(StateAggregator),
                proxyState.Id,
                "SysVarDict",
                stepId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            await OrleansRetryHelper.ExecuteWithRetryAsync(
                () => proxyState.SysVarDict.GetInnerDictionary().SetAsync(stepId, bodyStr),
                operationName: "AggregateStateStepBody");
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }

        return proxyState;
    }

    public async Task<ProxyState> SetSysVarStringAsync(ProxyState proxyState, string key, string value)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(StateAggregator),
                proxyState.Id,
                "SysVarDict",
                key
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            await OrleansRetryHelper.ExecuteWithRetryAsync(
                () => proxyState.SysVarDict.GetInnerDictionary().SetAsync(key, value),
                operationName: "SetSysVarString");
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }

        return proxyState;
    }

    public Task<long> IncrementSysCompanyVarAsync(ProxyState proxyState, string varName, long increment = 1)
    {
        return OrleansRetryHelper.ExecuteWithRetryAsync(
            () => proxyState.SysCompanyVarDict.GetInnerDictionary().IncrementAsync(varName, increment),
            operationName: "IncrementSysCompanyVar");
    }

    public async Task<ProxyState> AggregateStateError(ProxyState proxyState, string catchAs, UserFriendlyError? error)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(StateAggregator),
                proxyState.Id,
                "SysVarDict",
                catchAs
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            if (error != null)
            {
                await OrleansRetryHelper.ExecuteWithRetryAsync(
                    () => proxyState.SysVarDict.GetInnerDictionary().SetAsync(catchAs, error),
                    operationName: "AggregateStateError_Set");
            }
            else
            {
                await OrleansRetryHelper.ExecuteWithRetryAsync(
                    () => proxyState.SysVarDict.GetInnerDictionary().RemoveAsync(catchAs),
                    operationName: "AggregateStateError_Remove");
            }
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }

        return proxyState;
    }

    public async Task<ProxyState> AggregateStateContactDetailAsync(
        ProxyState proxyState,
        ContactDetail contactDetail)
    {
        var innerDictionary = proxyState.UsrVarDict.GetInnerDictionary();
        var contactProperties = ContactDetailHelpers.GetPropertiesAsDictionary(contactDetail);

        foreach (var contactProperty in contactProperties)
        {
            var @lock = await _lockService.WaitUnitLockAsync(
                new[]
                {
                    nameof(StateAggregator),
                    proxyState.Id,
                    "UsrVarDict",
                    contactProperty.Key
                },
                TimeSpan.FromSeconds(10),
                TimeSpan.FromSeconds(30));

            try
            {
                var valueToSet = contactProperty.Value switch
                {
                    Dictionary<string, object?> dictionary =>
                        JsonConvert.DeserializeObject<JObject>(
                                JsonConvert.SerializeObject(dictionary))!
                            .ToNativeDictionary(),
                    ContactList[] contactLists => JArray.Parse(
                            JsonConvert.SerializeObject(contactLists, JsonConfig.DefaultJsonSerializerSettings))
                        .ToNativeArray(),
                    ContactConversation contactConversation => JObject.Parse(
                            JsonConvert.SerializeObject(
                                contactConversation,
                                JsonConfig.DefaultJsonSerializerSettings))
                        .ToNativeDictionary(),
                    _ => contactProperty.Value
                };

                await OrleansRetryHelper.ExecuteWithRetryAsync(
                    () => innerDictionary.SetAsync(contactProperty.Key, valueToSet),
                    operationName: "AggregateStateContactDetail");
            }
            finally
            {
                await _lockService.ReleaseAsync(@lock);
            }
        }

        return proxyState;
    }
}