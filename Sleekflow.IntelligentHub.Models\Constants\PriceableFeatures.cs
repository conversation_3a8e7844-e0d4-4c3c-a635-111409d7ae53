﻿namespace Sleekflow.IntelligentHub.Models.Constants;

public static class PriceableFeatures
{
    public const string AiFeaturesTotalUsage = "ai_features_total_usage";
    public const string AiAgentsTotalUsage = "ai_agents_total_usage";
    public const string AiAgentsKnowledgeBaseTotalCharacterCount = "ai_agents_knowledge_base_total_character_count";
    public const string Translate = "translate";
    public const string Rephrase = "rephrase";
    public const string ChangeTone = "change_tone";
    public const string RecommendReply = "recommend_reply";
    public const string CustomPromptRewrite = "custom_prompt_rewrite";
    public const string Summary = "summary";
    public const string Scoring = "scoring";
    public const string HandoffTeam = "handoff_team";
    public const string AgentRecommendReply = "agent_recommend_reply";
}