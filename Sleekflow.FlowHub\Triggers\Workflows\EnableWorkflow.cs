using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Triggers.Workflows;

[TriggerGroup(ControllerNames.Workflows)]
public class EnableWorkflow : ITrigger
{
    private readonly IWorkflowService _workflowService;
    private readonly IFlowHubConfigService _flowHubConfigService;

    public EnableWorkflow(
        IWorkflowService workflowService,
        IFlowHubConfigService flowHubConfigService)
    {
        _workflowService = workflowService;
        _flowHubConfigService = flowHubConfigService;
    }

    public class EnableWorkflowInput : IHasSleekflowStaff, Sleekflow.Persistence.Abstractions.IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("workflow_versioned_id")]
        [Required]
        public string WorkflowVersionedId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public EnableWorkflowInput(
            string sleekflowCompanyId,
            string workflowVersionedId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WorkflowVersionedId = workflowVersionedId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class EnableWorkflowOutput
    {
        [JsonProperty("workflow")]
        public WorkflowDto Workflow { get; set; }

        [JsonConstructor]
        public EnableWorkflowOutput(WorkflowDto workflow)
        {
            Workflow = workflow;
        }
    }

    public async Task<EnableWorkflowOutput> F(EnableWorkflowInput enableWorkflowInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            enableWorkflowInput.SleekflowStaffId,
            enableWorkflowInput.SleekflowStaffTeamIds);

        var numOfActiveWorkflows = await _workflowService.CountWorkflowsAsync(
            enableWorkflowInput.SleekflowCompanyId,
            activationStatus: WorkflowActivationStatuses.Active,
            workflowType: WorkflowType.Normal);

        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(enableWorkflowInput.SleekflowCompanyId);

        var usageLimit = flowHubConfig.UsageLimit;
        var usageLimitOffset = flowHubConfig.UsageLimitOffset;

        var versonedWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            enableWorkflowInput.SleekflowCompanyId,
            enableWorkflowInput.WorkflowVersionedId);

        var workflowType = versonedWorkflow?.WorkflowType;

        if ((string.IsNullOrEmpty(workflowType) || workflowType == WorkflowType.Normal) && usageLimit != null)
        {
            var maximumNumOfActiveWorkflows = usageLimit.MaximumNumOfActiveWorkflows ?? 0;
            var maximumNumOfActiveWorkflowsOffset = usageLimitOffset?.MaximumNumOfActiveWorkflowsOffset ?? 0;

            if (numOfActiveWorkflows >= maximumNumOfActiveWorkflows + maximumNumOfActiveWorkflowsOffset)
            {
                throw new SfFlowHubExceedUsageException(UsageLimitFieldNames.PropertyNameMaximumNumOfActiveWorkflows);
            }
        }



        var workflow = await _workflowService.EnableWorkflowAsync(
            enableWorkflowInput.WorkflowVersionedId,
            enableWorkflowInput.SleekflowCompanyId,
            sleekflowStaff);

        return new EnableWorkflowOutput(new WorkflowDto(workflow));
    }
}