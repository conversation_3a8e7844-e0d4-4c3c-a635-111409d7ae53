﻿using Sleekflow.AuditHub.Models.SystemAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.SystemAuditLogs;

public interface ISystemAuditLogRepository : IRepository<SystemAuditLog>
{
}

public class SystemAuditLogRepository
    : BaseRepository<SystemAuditLog>,
        ISystemAuditLogRepository,
        ISingletonService
{
    public SystemAuditLogRepository(
        ILogger<SystemAuditLogRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}