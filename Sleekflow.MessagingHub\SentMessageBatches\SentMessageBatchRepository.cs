﻿using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.SentMessageBatches;

public interface ISentMessageBatchRepository : IRepository<SentMessageBatch>
{
}

public class SentMessageBatchRepository
    : BaseRepository<SentMessageBatch>,
        ISentMessageBatchRepository,
        ISingletonService
{
    public SentMessageBatchRepository(
        ILogger<SentMessageBatchRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}