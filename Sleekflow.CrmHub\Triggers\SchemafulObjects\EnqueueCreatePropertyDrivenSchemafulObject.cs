using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.ActionEvents.CrmHub;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class EnqueueCreatePropertyDrivenSchemafulObject
    : ITrigger<EnqueueCreatePropertyDrivenSchemafulObject.EnqueueCreatePropertyDrivenSchemafulObjectInput,
        EnqueueCreatePropertyDrivenSchemafulObject.EnqueueCreatePropertyDrivenSchemafulObjectOutput>
{
    private readonly IBus _bus;

    public EnqueueCreatePropertyDrivenSchemafulObject(IBus bus)
    {
        _bus = bus;
    }

    public class EnqueueCreatePropertyDrivenSchemafulObjectInput : CreatePropertyDrivenSchemafulObjectEvent
    {
        [JsonConstructor]
        public EnqueueCreatePropertyDrivenSchemafulObjectInput(
            string schemaUniqueName,
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            Dictionary<string, object?> propertyValues,
            string createdVia)
            : base(schemaUniqueName, sleekflowCompanyId, sleekflowUserProfileId, propertyValues, createdVia)
        {
        }
    }

    public class EnqueueCreatePropertyDrivenSchemafulObjectOutput
    {
    }

    public async Task<EnqueueCreatePropertyDrivenSchemafulObjectOutput> F(
        EnqueueCreatePropertyDrivenSchemafulObjectInput input)
    {
        await _bus.Publish(
            new CreatePropertyDrivenSchemafulObjectEvent(
                input.SchemaUniqueName,
                input.SleekflowCompanyId,
                input.SleekflowUserProfileId,
                input.PropertyValues,
                input.CreatedVia));

        return new EnqueueCreatePropertyDrivenSchemafulObjectOutput();
    }
}