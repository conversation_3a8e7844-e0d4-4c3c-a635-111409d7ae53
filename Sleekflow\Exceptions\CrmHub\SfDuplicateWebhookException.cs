﻿namespace Sleekflow.Exceptions.CrmHub;

public class SfDuplicateWebhookException : ErrorCodeException
{
    public string EntityTypeName { get; }

    public string EventTypeName { get; }

    public SfDuplicateWebhookException(string entityTypeName, string eventTypeName)
        : base(
            ErrorCodeConstant.SfDuplicateWebhookException,
            $"The webhook is duplicate. entityTypeName=[{entityTypeName}], eventTypeName=[{eventTypeName}]",
            new Dictionary<string, object?>
            {
                {
                    "entityTypeName", entityTypeName
                },
                {
                    "eventTypeName", eventTypeName
                }
            })
    {
        EntityTypeName = entityTypeName;
        EventTypeName = eventTypeName;
    }
}