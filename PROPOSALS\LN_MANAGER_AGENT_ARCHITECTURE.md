# Lead Nurturing Manager Agent Architecture

## 1. Overview

This proposal outlines a transformative architectural change for the Lead Nurturing system, moving from the current multi-agent collaboration model to a single Manager Agent orchestrating specialized agent tools through a shared Data Pane. This change addresses architectural complexity while maintaining the sophisticated workflow logic and functionality of the existing system.

**Current Architecture Challenge**: The existing Lead Nurturing system uses a capability-based multi-agent collaboration with a coordination function that routes between 14 specialized agents. While highly effective, this architecture creates complexity in message passing, workflow management, and debugging.

**Proposed Solution**: Transform to a Manager-Agent pattern where:
- **Single Manager Agent**: Orchestrates the entire workflow using sophisticated reasoning
- **Data Pane Plugin**: Shared storage for large data objects (knowledge, strategies, conversation state)
- **Agent Tools**: Each current agent becomes a specialized tool with defined inputs/outputs
- **Simplified Data Flow**: Clear separation between control flow (manager) and data flow (data pane)

This architecture maintains all existing capabilities while providing clearer separation of concerns, easier debugging, and more predictable data flows.

## 2. Goals

- **Simplify Architecture**: Replace complex multi-agent coordination with single manager orchestration
- **Maintain Functionality**: Preserve all existing lead nurturing capabilities and workflow logic
- **Improve Debugging**: Provide clearer visibility into workflow decisions and data flows
- **Reduce Message Overhead**: Move large data through data pane instead of agent messages
- **Enable Better Error Handling**: Centralized error management in the manager agent
- **Prepare for Extensions**: Make it easier to add new capabilities as tools

## 3. Key Considerations

### 3.1 Architectural Complexity

- **Manager Agent Sophistication**: The manager must understand the entire workflow and make complex routing decisions
- **Tool Interface Design**: Each agent-tool needs carefully designed inputs and outputs
- **Data Pane Lifecycle**: Proper management of shared data lifecycle and cleanup
- **State Management**: Maintaining conversation state across tool invocations

### 3.2 Performance

- **Tool Invocation Overhead**: Multiple tool calls vs. direct agent communication
- **Data Pane Access**: Redis operations for data storage/retrieval
- **Workflow Efficiency**: Manager decision-making speed vs. coordination function

### 3.3 Maintainability

- **Workflow Centralization**: All logic concentrated in manager vs. distributed in coordination function
- **Tool Independence**: Each tool must be self-contained and testable
- **Data Schema Evolution**: Managing data pane schema changes over time

### 3.4 Compatibility

- **Existing Integrations**: Maintain compatibility with external systems (ChiliPiper, Sleekflow Tools)
- **Model Selection**: Preserve optimized model assignments for different functions
- **Configuration Support**: Support existing LeadNurturingTools configuration

## 4. High-Level Architecture

### 4.1 Core Components

1. **ManagerLeadNurturingAgent**: Single orchestrating agent that manages the entire workflow
2. **LeadNurturingDataPane**: Plugin providing shared data storage and retrieval
3. **Agent Tools**: Specialized tools derived from current agents (StrategyTool, KnowledgeRetrievalTool, etc.)
4. **Workflow Engine**: Embedded logic in manager for routing and decision-making

### 4.2 Component Diagram

```mermaid
graph TD
    Customer[Customer] <--> Manager[ManagerLeadNurturingAgent]

    Manager --> DataPane[LeadNurturingDataPane Plugin]

    Manager --> StrategyTool[StrategyTool]
    Manager --> KnowledgeTool[KnowledgeRetrievalTool]
    Manager --> ResponseTool[ResponseCrafterTool]
    Manager --> TransitionTool[TransitioningResponseCrafterTool]
    Manager --> InfoGatheringTool[InformationGatheringTool]
    Manager --> ReviewTool[ReviewerTool]
    Manager --> ClassifierTool[LeadClassifierTool]
    Manager --> DecisionTool[DecisionTool]
    Manager --> PlanningTool[PlanningTool]
    Manager --> ConfirmationTool[ConfirmationTool]
    Manager --> ActionTool[ActionTool]

    StrategyTool -.-> DataPane
    KnowledgeTool -.-> DataPane
    ResponseTool -.-> DataPane
    TransitionTool -.-> DataPane
    InfoGatheringTool -.-> DataPane
    ReviewTool -.-> DataPane

    DataPane --> Redis[(Redis Cache)]

    ActionTool --> SleekflowPlugin[SleekflowToolsPlugin]
    ActionTool --> ChiliPiperPlugin[ChiliPiperToolsPlugin]
```

### 4.3 Data Flow Architecture

```mermaid
sequenceDiagram
    participant Customer
    participant Manager as ManagerLeadNurturingAgent
    participant DataPane as LeadNurturingDataPane
    participant Tools as Agent Tools

    Customer->>Manager: Message
    Manager->>Tools: ClassifyLead(conversation)
    Tools->>Manager: Classification Result
    Manager->>DataPane: StoreClassification(result)
    Manager->>Tools: MakeDecision(classification)
    Tools->>Manager: Decision
    Manager->>DataPane: StoreDecision(decision)

    alt Continue Nurturing
        Manager->>Tools: DefineStrategy(context)
        Tools->>DataPane: StoreStrategy(strategy)
        Tools->>Manager: Strategy Summary
        Manager->>Tools: RetrieveKnowledge(query)
        Tools->>DataPane: StoreKnowledge(knowledge)
        Tools->>Manager: Knowledge Summary
        Manager->>Tools: CraftResponse(context)
        Tools->>DataPane: GetStrategy()
        Tools->>DataPane: GetKnowledge()
        Tools->>Manager: Draft Response
        Manager->>Tools: ReviewResponse(draft)
        Tools->>Manager: Approved Response
        Manager->>Customer: Final Response
    else Team Assignment
        Manager->>Tools: PlanAssignment(classification)
        Tools->>Manager: Assignment Plan
        Manager->>Tools: ConfirmAction(plan)
        Tools->>Manager: Confirmation Status
        Manager->>Tools: ExecuteAction(plan)
        Tools->>Manager: Execution Result
        Manager->>Tools: CraftTransition(result)
        Tools->>Manager: Transition Message
        Manager->>Customer: Final Response
    end
```

## 5. Detailed Design

### 5.1 ManagerLeadNurturingAgent (`Sleekflow.IntelligentHub/FaqAgents/Chats/ManagerAgents/ManagerLeadNurturingAgent.cs`)

The manager agent contains the core workflow logic and orchestrates all other tools:

```csharp
using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Utils;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.ManagerAgents;

public class ManagerLeadNurturingAgent : ChatCompletionAgent
{
    private readonly ILogger<ManagerLeadNurturingAgent> _logger;
    private readonly LeadNurturingDataPane _dataPane;

    public ManagerLeadNurturingAgent(
        Kernel kernel,
        PromptExecutionSettings settings,
        ILogger<ManagerLeadNurturingAgent> logger,
        LeadNurturingDataPane dataPane)
    {
        _logger = logger;
        _dataPane = dataPane;

        Name = "ManagerLeadNurturingAgent";
        Kernel = kernel;
        Arguments = new KernelArguments(settings);

        // Register all tools
        Kernel.Plugins.AddFromObject(_dataPane);
        Kernel.Plugins.AddFromObject(new LeadClassifierTool());
        Kernel.Plugins.AddFromObject(new DecisionTool());
        Kernel.Plugins.AddFromObject(new StrategyTool());
        Kernel.Plugins.AddFromObject(new KnowledgeRetrievalTool());
        Kernel.Plugins.AddFromObject(new ResponseCrafterTool());
        Kernel.Plugins.AddFromObject(new TransitioningResponseCrafterTool());
        Kernel.Plugins.AddFromObject(new InformationGatheringTool());
        Kernel.Plugins.AddFromObject(new ReviewerTool());
        Kernel.Plugins.AddFromObject(new PlanningTool());
        Kernel.Plugins.AddFromObject(new ConfirmationTool());
        Kernel.Plugins.AddFromObject(new ActionTool());

        Instructions = GetManagerInstructions();
    }

    private string GetManagerInstructions()
    {
        return """
            You are the ManagerLeadNurturingAgent, responsible for orchestrating the complete lead nurturing workflow.
            Your role is to analyze customer conversations and guide them through a structured process that may result in:
            - Continued nurturing with personalized responses
            - Team assignment for hot/cold leads
            - Demo scheduling when requested
            - Information gathering when needed

            WORKFLOW PROCESS:
            Follow this exact sequence for each customer message:

            1. CLASSIFY LEAD: Use ClassifyLead tool to analyze conversation and determine lead quality
            2. MAKE DECISION: Use MakeDecision tool based on classification to determine next action
            3. EXECUTE BASED ON DECISION:

               If continue_nurturing:
               - Use DefineStrategy to create nurturing approach
               - Use RetrieveKnowledge if strategy indicates knowledge needed
               - Use CraftResponse to create personalized response
               - Use ReviewResponse to ensure quality

               If assign_lead:
               - Use PlanAssignment to prepare team assignment
               - Use ConfirmAction to verify user consent
               - Use ExecuteAction to perform assignment
               - Use CraftTransition to create handoff message
               - Use ReviewResponse for final check

               If schedule_demo:
               - Use PlanDemo to analyze requirements
               - If missing info: Use GatherInformation and ReviewResponse
               - If complete: Use ConfirmAction, ExecuteAction, CraftTransition, ReviewResponse

            DATA MANAGEMENT:
            - Store large data (strategies, knowledge, classifications) in data pane using StoreData tool
            - Retrieve data using GetData tool when needed by other tools
            - Keep only summaries and decisions in your working memory

            TOOL USAGE RULES:
            - Always provide complete context to tools
            - Tools may automatically access data pane for their operations
            - Review all responses before sending to customer
            - End workflow only after ReviewResponse approves the final message

            CONVERSATION CONTINUITY:
            - Each customer message starts a fresh workflow
            - Use data pane to maintain context across tool calls
            - Ensure responses feel natural and personalized
            - Avoid repetitive patterns or corporate language

            Your goal is to provide exactly one high-quality response per customer message through systematic tool orchestration.
            """;
    }
}
```

### 5.2 LeadNurturingDataPane Plugin (`Sleekflow.IntelligentHub/Plugins/LeadNurturingDataPane.cs`)

The data pane manages shared data storage for all tools:

```csharp
using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.JsonConfigs;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.Plugins;

public class LeadNurturingDataPane
{
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ICacheConfig _cacheConfig;
    private readonly ILogger<LeadNurturingDataPane> _logger;
    private static readonly TimeSpan DataExpiration = TimeSpan.FromMinutes(30);

    public LeadNurturingDataPane(
        IConnectionMultiplexer connectionMultiplexer,
        ICacheConfig cacheConfig,
        ILogger<LeadNurturingDataPane> logger)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _cacheConfig = cacheConfig;
        _logger = logger;
    }

    [KernelFunction, Description("Store data in the data pane for access by other tools")]
    public async Task<string> StoreData(
        [Description("Unique identifier for the conversation session")] string sessionId,
        [Description("Type of data being stored (classification, decision, strategy, knowledge, etc.)")] string dataType,
        [Description("The data to store as JSON string")] string data,
        [Description("Optional metadata about the data")] string? metadata = null)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var key = GetDataKey(sessionId, dataType);

            var dataRecord = new DataPaneRecord
            {
                SessionId = sessionId,
                DataType = dataType,
                Data = data,
                Metadata = metadata,
                Timestamp = DateTimeOffset.UtcNow
            };

            await database.StringSetAsync(
                key,
                JsonConvert.SerializeObject(dataRecord, JsonConfig.DefaultJsonSerializerSettings),
                DataExpiration);

            _logger.LogInformation("Stored {DataType} for session {SessionId}", dataType, sessionId);
            return "Success: Data stored successfully";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to store {DataType} for session {SessionId}", dataType, sessionId);
            return $"Error: Failed to store data - {ex.Message}";
        }
    }

    [KernelFunction, Description("Retrieve data from the data pane")]
    public async Task<string> GetData(
        [Description("Unique identifier for the conversation session")] string sessionId,
        [Description("Type of data to retrieve (classification, decision, strategy, knowledge, etc.)")] string dataType)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var key = GetDataKey(sessionId, dataType);

            var result = await database.StringGetAsync(key);
            if (!result.HasValue)
            {
                return $"No {dataType} data found for session {sessionId}";
            }

            var dataRecord = JsonConvert.DeserializeObject<DataPaneRecord>(
                result!, JsonConfig.DefaultJsonSerializerSettings);

            return dataRecord?.Data ?? $"Empty {dataType} data for session {sessionId}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve {DataType} for session {SessionId}", dataType, sessionId);
            return $"Error: Failed to retrieve data - {ex.Message}";
        }
    }

    [KernelFunction, Description("List all data types available for a session")]
    public async Task<string> ListSessionData(
        [Description("Unique identifier for the conversation session")] string sessionId)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var pattern = GetDataKeyPattern(sessionId);

            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern).ToList();

            var dataTypes = keys.Select(key =>
                key.ToString().Split(':').Last()).ToList();

            return JsonConvert.SerializeObject(dataTypes, JsonConfig.DefaultJsonSerializerSettings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to list session data for {SessionId}", sessionId);
            return $"Error: Failed to list session data - {ex.Message}";
        }
    }

    [KernelFunction, Description("Clear all data for a session")]
    public async Task<string> ClearSessionData(
        [Description("Unique identifier for the conversation session")] string sessionId)
    {
        try
        {
            var database = _connectionMultiplexer.GetDatabase();
            var pattern = GetDataKeyPattern(sessionId);

            var server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());
            var keys = server.Keys(pattern: pattern).ToArray();

            if (keys.Length > 0)
            {
                await database.KeyDeleteAsync(keys);
            }

            _logger.LogInformation("Cleared {Count} data entries for session {SessionId}", keys.Length, sessionId);
            return $"Success: Cleared {keys.Length} data entries";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to clear session data for {SessionId}", sessionId);
            return $"Error: Failed to clear session data - {ex.Message}";
        }
    }

    private string GetDataKey(string sessionId, string dataType)
    {
        return $"{_cacheConfig.CachePrefix}:lead-nurturing-data:{sessionId}:{dataType}";
    }

    private string GetDataKeyPattern(string sessionId)
    {
        return $"{_cacheConfig.CachePrefix}:lead-nurturing-data:{sessionId}:*";
    }

    public class DataPaneRecord
    {
        public string SessionId { get; set; } = string.Empty;
        public string DataType { get; set; } = string.Empty;
        public string Data { get; set; } = string.Empty;
        public string? Metadata { get; set; }
        public DateTimeOffset Timestamp { get; set; }
    }
}
```

### 5.3 Agent Tool Examples

#### 5.3.1 LeadClassifierTool (`Sleekflow.IntelligentHub/Tools/LeadNurturing/LeadClassifierTool.cs`)

```csharp
using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Tools.LeadNurturing;

public class LeadClassifierTool
{
    [KernelFunction, Description("Classify a lead based on conversation context")]
    public async Task<string> ClassifyLead(
        [Description("The complete conversation context between customer and company")] string conversationContext,
        [Description("Session ID for data storage")] string sessionId,
        KernelFunction? storeDataFunction = null)
    {
        // This would contain the same logic as the current LeadClassifierAgent
        // but as a tool function instead of an agent

        var classification = await PerformLeadClassification(conversationContext);

        // Store in data pane if function available
        if (storeDataFunction != null)
        {
            await storeDataFunction.InvokeAsync(
                new KernelArguments
                {
                    ["sessionId"] = sessionId,
                    ["dataType"] = "classification",
                    ["data"] = JsonConvert.SerializeObject(classification)
                });
        }

        // Return summary for manager
        return $"Lead classified as {classification.Classification} with score {classification.Score}. " +
               $"Reasoning: {classification.Reasoning}";
    }

    private async Task<LeadClassificationResult> PerformLeadClassification(string conversationContext)
    {
        // Implementation would use the same logic as current LeadClassifierAgent
        // but return structured data instead of agent message

        return new LeadClassificationResult
        {
            Classification = "warm", // Actual classification logic here
            Score = "75",
            Reasoning = "Customer shows interest but needs more information"
        };
    }

    public class LeadClassificationResult
    {
        public string Classification { get; set; } = string.Empty;
        public string Score { get; set; } = string.Empty;
        public string Reasoning { get; set; } = string.Empty;
    }
}
```

#### 5.3.2 StrategyTool (`Sleekflow.IntelligentHub/Tools/LeadNurturing/StrategyTool.cs`)

```csharp
using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Tools.LeadNurturing;

public class StrategyTool
{
    [KernelFunction, Description("Define nurturing strategy based on lead classification and context")]
    public async Task<string> DefineStrategy(
        [Description("The conversation context")] string conversationContext,
        [Description("Lead classification and decision information")] string classificationInfo,
        [Description("Session ID for data access")] string sessionId,
        KernelFunction? getDataFunction = null,
        KernelFunction? storeDataFunction = null)
    {
        // Get classification data from data pane
        string? classificationData = null;
        if (getDataFunction != null)
        {
            var result = await getDataFunction.InvokeAsync(new KernelArguments
            {
                ["sessionId"] = sessionId,
                ["dataType"] = "classification"
            });
            classificationData = result.GetValue<string>();
        }

        var strategy = await GenerateStrategy(conversationContext, classificationData ?? classificationInfo);

        // Store strategy in data pane
        if (storeDataFunction != null)
        {
            await storeDataFunction.InvokeAsync(new KernelArguments
            {
                ["sessionId"] = sessionId,
                ["dataType"] = "strategy",
                ["data"] = JsonConvert.SerializeObject(strategy)
            });
        }

        // Return summary for manager
        return $"Strategy defined. Knowledge needed: {(strategy.NeedKnowledge != null ? "Yes" : "No")}. " +
               $"Strategy summary: {strategy.StrategySummary}";
    }

    private async Task<StrategyResult> GenerateStrategy(string conversationContext, string classificationInfo)
    {
        // Implementation would use the same logic as current StrategyAgent
        return new StrategyResult
        {
            NeedKnowledge = "Product features and pricing information",
            StrategySummary = "Focus on product benefits and address pricing concerns",
            DetailedStrategy = "Detailed strategy guidance here..."
        };
    }

    public class StrategyResult
    {
        public string? NeedKnowledge { get; set; }
        public string StrategySummary { get; set; } = string.Empty;
        public string DetailedStrategy { get; set; } = string.Empty;
    }
}
```

### 5.4 Integration with Current Architecture

#### 5.4.1 ManagerLeadNurturingCollaborationDefinition (`Sleekflow.IntelligentHub/FaqAgents/Chats/AgentCollaborationDefinitions/ManagerLeadNurturings/ManagerLeadNurturingCollaborationDefinition.cs`)

```csharp
using Microsoft.SemanticKernel.Agents;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Base;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ManagerLeadNurturings;

public class ManagerLeadNurturingCollaborationDefinition : BaseAgentCollaborationDefinition
{
    private readonly ILogger<ManagerLeadNurturingCollaborationDefinition> _logger;
    private readonly LeadNurturingDataPane _dataPane;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;

    public ManagerLeadNurturingCollaborationDefinition(
        ILogger<ManagerLeadNurturingCollaborationDefinition> logger,
        Kernel kernel,
        LeadNurturingDataPane dataPane,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        // ... other dependencies
        ) : base(logger, kernel, /* other base dependencies */)
    {
        _logger = logger;
        _dataPane = dataPane;
        _promptExecutionSettingsService = promptExecutionSettingsService;
    }

    public override Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig)
    {
        var settings = _promptExecutionSettingsService.GetPromptExecutionSettings(
            SemanticKernelExtensions.GPT_4_1, true);

        var managerAgent = new ManagerLeadNurturingAgent(
            kernel,
            settings,
            _logger.CreateLogger<ManagerLeadNurturingAgent>(),
            _dataPane);

        return Task.FromResult(new List<Agent> { managerAgent });
    }

    public override SelectionStrategy CreateSelectionStrategy(Kernel kernel)
    {
        // Single agent - no selection needed
        return new SequentialSelectionStrategy();
    }

    public override RegexTerminationStrategy CreateTerminationStrategy(List<Agent> agents)
    {
        // Terminate when manager provides final response
        return new RegexTerminationStrategy("FINAL_RESPONSE:")
        {
            MaximumIterations = 1,
            AutomaticReset = true,
            Agents = agents
        };
    }

    public override string GetFinalReplyTag() => "FINAL_RESPONSE";

    public override async Task<string> GetFinalReplyAsync(AgentGroupChat agentGroupChat)
    {
        var chatMessages = await agentGroupChat.GetChatMessagesAsync().ToListAsync();
        var finalMessage = chatMessages.LastOrDefault(m =>
            m.Content?.Contains("FINAL_RESPONSE:") == true);

        if (finalMessage?.Content != null)
        {
            var response = finalMessage.Content.Split("FINAL_RESPONSE:")[1].Trim();
            return WhatsAppMarkdownConverter.ConvertToWhatsAppMarkdown(response);
        }

        return string.Empty;
    }
}
```

## 6. Tool Specifications

### 6.1 Complete Tool Mapping

| Current Agent | Tool Name | Input Data | Output Data | Data Pane Usage |
|---------------|-----------|------------|-------------|-----------------|
| LeadClassifierAgent | LeadClassifierTool | Conversation context | Classification summary | Stores: classification, score, reasoning |
| DecisionAgent | DecisionTool | Classification data | Decision summary | Stores: decision, decision reasoning |
| StrategyAgent | StrategyTool | Context + classification | Strategy summary | Stores: strategy, knowledge needs |
| KnowledgeRetrievalAgent | KnowledgeRetrievalTool | Knowledge query | Knowledge summary | Stores: retrieved knowledge, evaluation |
| ResponseCrafterAgent | ResponseCrafterTool | Context + strategy + knowledge | Draft response | Accesses: strategy, knowledge |
| TransitioningResponseCrafterAgent | TransitioningResponseCrafterTool | Transition context | Transition message | Accesses: action results |
| InformationGatheringResponseCrafterAgent | InformationGatheringTool | Missing field info | Info request | Stores: missing fields |
| ReviewerAgent | ReviewerTool | Response to review | Review result | Accesses: response history |
| LeadAssignmentPlanningAgent | PlanningTool (assign mode) | Classification + context | Assignment plan | Stores: assignment plan |
| DemoSchedulingPlanningAgent | PlanningTool (demo mode) | Demo request + fields | Demo plan | Stores: demo plan, field status |
| ConfirmationAgent | ConfirmationTool | Action plan | Confirmation status | Accesses: pending actions |
| ActionAgent | ActionTool | Confirmed plan | Execution result | Stores: execution results |

### 6.2 Data Pane Schema

```csharp
// Data types stored in the data pane
public static class DataPaneTypes
{
    public const string Classification = "classification";
    public const string Decision = "decision";
    public const string Strategy = "strategy";
    public const string Knowledge = "knowledge";
    public const string AssignmentPlan = "assignment_plan";
    public const string Demoplan = "demo_plan";
    public const string ExecutionResult = "execution_result";
    public const string ResponseHistory = "response_history";
    public const string FieldStatus = "field_status";
}

// Example data structures
public class ClassificationData
{
    public string Classification { get; set; } = string.Empty;
    public string Score { get; set; } = string.Empty;
    public string Reasoning { get; set; } = string.Empty;
    public DateTimeOffset Timestamp { get; set; }
}

public class StrategyData
{
    public string? NeedKnowledge { get; set; }
    public string NeedKnowledgeReasoning { get; set; } = string.Empty;
    public string Strategy { get; set; } = string.Empty;
    public string StrategyReasoning { get; set; } = string.Empty;
    public DateTimeOffset Timestamp { get; set; }
}

public class KnowledgeData
{
    public string Query { get; set; } = string.Empty;
    public string RetrievedKnowledge { get; set; } = string.Empty;
    public string KnowledgeOverview { get; set; } = string.Empty;
    public string KnowledgeOverviewReasoning { get; set; } = string.Empty;
    public string? Decision { get; set; }
    public DateTimeOffset Timestamp { get; set; }
}
```

## 7. Implementation Plan

### 7.1 Phase 1: Core Infrastructure (2 weeks)

**Tasks:**
- [ ] Create LeadNurturingDataPane plugin with Redis backend
- [ ] Implement DataPaneRecord model and core data operations
- [ ] Create base ManagerLeadNurturingAgent class structure
- [ ] Implement tool interface contracts and base classes
- [ ] Create ManagerLeadNurturingCollaborationDefinition
- [ ] Set up dependency injection for new components

**Dependencies:**
- Access to Redis configuration and existing cache infrastructure
- Understanding of current agent workflow logic

**Expected Outcome:**
Basic infrastructure with data pane operations working and manager agent skeleton that can be extended with tool implementations. Unit tests passing for core data operations.

**Time Estimate:** 2 weeks

### 7.2 Phase 2: Core Tool Implementation (3 weeks)

**Tasks:**
- [ ] Implement LeadClassifierTool with existing logic
- [ ] Implement DecisionTool with decision table logic
- [ ] Implement StrategyTool with nurturing guidance
- [ ] Implement KnowledgeRetrievalTool with existing knowledge integration
- [ ] Create comprehensive tool unit tests
- [ ] Implement manager workflow orchestration logic
- [ ] Add error handling and logging throughout

**Dependencies:**
- Phase 1 completion
- Analysis of existing agent prompts and logic
- Knowledge plugin integration

**Expected Outcome:**
Core workflow tools implemented and tested. Manager can successfully orchestrate lead classification, decision making, strategy definition, and knowledge retrieval. Integration tests passing for basic nurturing flow.

**Time Estimate:** 3 weeks

### 7.3 Phase 3: Response and Action Tools (2 weeks)

**Tasks:**
- [ ] Implement ResponseCrafterTool with data pane integration
- [ ] Implement TransitioningResponseCrafterTool for handoff scenarios
- [ ] Implement InformationGatheringTool for missing data collection
- [ ] Implement ReviewerTool with quality checks
- [ ] Implement PlanningTool (both assignment and demo modes)
- [ ] Implement ConfirmationTool and ActionTool
- [ ] Add workflow termination and response extraction logic

**Dependencies:**
- Phase 2 completion
- Integration with SleekflowToolsPlugin and ChiliPiperToolsPlugin
- Understanding of current action execution flows

**Expected Outcome:**
Complete tool suite implemented. Manager can handle all workflow scenarios including team assignments, demo scheduling, and response generation. End-to-end workflow tests passing.

**Time Estimate:** 2 weeks

### 7.4 Phase 4: Integration and Migration (2 weeks)

**Tasks:**
- [ ] Integrate with existing AgentCollaborationMode enum
- [ ] Create configuration migration utilities
- [ ] Implement comprehensive logging and telemetry
- [ ] Performance testing and optimization
- [ ] Create migration documentation and guides
- [ ] Set up feature flag for gradual rollout
- [ ] Implement fallback mechanisms to current architecture

**Dependencies:**
- Phase 3 completion
- Coordination with operations team for deployment
- Performance benchmarking against current system

**Expected Outcome:**
Production-ready implementation with migration path from existing architecture. Performance metrics show comparable or improved response times. Documentation and deployment procedures complete.

**Time Estimate:** 2 weeks

### 7.5 Implementation Checklist Summary

**Phase 1:**
- [ ] Data pane infrastructure complete
- [ ] Manager agent framework functional
- [ ] Core tests passing
- [ ] Redis integration working

**Phase 2:**
- [ ] Core workflow tools implemented
- [ ] Manager orchestration logic complete
- [ ] Integration tests passing
- [ ] Error handling comprehensive

**Phase 3:**
- [ ] All tool types implemented
- [ ] End-to-end workflows functional
- [ ] Response generation working
- [ ] Action execution integrated

**Phase 4:**
- [ ] Production deployment ready
- [ ] Migration tools complete
- [ ] Performance validated
- [ ] Documentation complete

## 8. Testing Strategy

### 8.1 Unit Tests

**Tool Testing:**
- Each tool tested independently with mock data pane
- Verify tool input/output contracts
- Test error handling and edge cases
- Mock external dependencies (knowledge plugins, external APIs)

**Data Pane Testing:**
- Redis operations (store, retrieve, list, clear)
- Data serialization/deserialization
- Cache expiration and cleanup
- Concurrent access scenarios

### 8.2 Integration Tests

**Manager Workflow Testing:**
- Complete workflow scenarios (nurturing, assignment, demo scheduling)
- Tool orchestration and data flow validation
- Error recovery and fallback mechanisms
- Performance benchmarking against current architecture

**External Integration Testing:**
- SleekflowToolsPlugin and ChiliPiperToolsPlugin integration
- Knowledge plugin compatibility
- Existing configuration support

### 8.3 Migration Testing

**Compatibility Testing:**
- Existing LeadNurturingTools configuration support
- Response format compatibility
- Feature parity validation against current system

**Performance Testing:**
- Response time comparison
- Memory usage analysis
- Concurrent session handling
- Redis load impact assessment

## 9. Benefits and Trade-offs

### 9.1 Benefits

**Architectural Simplicity:**
- Single point of control for workflow logic
- Clearer separation between orchestration and execution
- Easier debugging with centralized decision-making
- Reduced complexity in agent coordination

**Performance Improvements:**
- Reduced message passing overhead between agents
- Optimized data storage for large objects
- Better caching of intermediate results
- Potential for parallel tool execution

**Maintainability:**
- Tools are independent and easily testable
- Workflow logic concentrated in one place
- Easier to add new capabilities as tools
- Clear data contracts between components

### 9.2 Trade-offs

**Manager Complexity:**
- Single agent must understand entire workflow
- More sophisticated prompt engineering required
- Higher cognitive load for manager agent
- Potential single point of failure

**Tool Overhead:**
- Additional function call overhead vs. direct communication
- Redis operations for data pane access
- More complex tool input/output handling
- Need for careful tool interface design

**Migration Risk:**
- Significant architectural change
- Need to preserve all existing functionality
- Potential performance impacts during transition
- Training required for team on new architecture

## 10. Alternatives Considered

### 10.1 Hybrid Architecture

**Description:** Keep critical agents (LeadClassifier, DecisionAgent) as agents while converting others to tools.

**Pros:** Gradual migration path, reduced risk
**Cons:** Still maintains architectural complexity, unclear separation of concerns

**Decision:** Rejected in favor of complete transformation for maximum architectural benefits

### 10.2 Event-Driven Architecture

**Description:** Replace coordination function with event-driven workflows using message queues.

**Pros:** Better scalability, clearer event flow
**Cons:** Added infrastructure complexity, overkill for single-turn processing

**Decision:** Rejected as too complex for current requirements

### 10.3 Pipeline Architecture

**Description:** Create a linear pipeline of tool executions with fixed ordering.

**Pros:** Very simple to understand and debug
**Cons:** Less flexibility for complex workflows, harder to handle branching logic

**Decision:** Rejected due to loss of dynamic workflow capabilities

## 11. Future Enhancements

### 11.1 Parallel Tool Execution

**Description:** Execute independent tools in parallel to improve performance.

**Implementation:** Manager identifies tools that can run concurrently and executes them simultaneously.

**Benefits:** Reduced total response time, better resource utilization

### 11.2 Tool Versioning and A/B Testing

**Description:** Support multiple versions of tools for gradual rollouts and experimentation.

**Implementation:** Tool registry with version management and routing logic.

**Benefits:** Safer deployments, ability to test improvements incrementally

### 11.3 Advanced Workflow Patterns

**Description:** Support for more complex workflows like loops, conditional branching, and sub-workflows.

**Implementation:** Enhanced manager logic with workflow state management.

**Benefits:** Handle more sophisticated nurturing scenarios, better customization

### 11.4 Tool Performance Analytics

**Description:** Detailed monitoring and analytics for tool performance and effectiveness.

**Implementation:** Telemetry integration with tool execution metrics.

**Benefits:** Data-driven optimization, better understanding of workflow efficiency