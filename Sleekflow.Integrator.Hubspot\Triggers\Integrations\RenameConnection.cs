﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class RenameConnection : ITrigger
{
    private readonly IHubspotConnectionService _hubspotConnectionService;

    public RenameConnection(
        IHubspotConnectionService hubspotConnectionService)
    {
        _hubspotConnectionService = hubspotConnectionService;
    }

    public class RenameConnectionInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("name")]
        [Required]
        public string Name { get; set; }

        [JsonConstructor]
        public RenameConnectionInput(
            string sleekflowCompanyId,
            string connectionId,
            string name)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            Name = name;
        }
    }

    public class RenameConnectionOutput
    {
        [JsonProperty("connection")]
        public ProviderConnectionDto Connection { get; set; }

        [JsonConstructor]
        public RenameConnectionOutput(ProviderConnectionDto connection)
        {
            Connection = connection;
        }
    }

    public async Task<RenameConnectionOutput> F(
        RenameConnectionInput renameConnectionInput)
    {
        var connection = await _hubspotConnectionService.PatchAndGetAsync(
            renameConnectionInput.ConnectionId,
            renameConnectionInput.SleekflowCompanyId,
            renameConnectionInput.Name);

        return new RenameConnectionOutput(new ProviderConnectionDto(
            connection.Id,
            connection.SleekflowCompanyId,
            connection.OrganizationId,
            connection.Name,
            connection.Environment,
            connection.IsActive,
            null));
    }
}