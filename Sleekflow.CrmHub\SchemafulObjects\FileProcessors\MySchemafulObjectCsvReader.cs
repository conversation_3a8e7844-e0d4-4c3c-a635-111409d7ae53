﻿using System.Globalization;
using System.Text;
using Azure.Storage.Blobs;
using CsvHelper;
using CsvHelper.Configuration;
using Sleekflow.CrmHub.Models.SchemafulObjects.Readers;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.SchemafulObjects.Utils;
using Sleekflow.Exceptions;

namespace Sleekflow.CrmHub.SchemafulObjects.FileProcessors;

public class MySchemafulObjectCsvReader
{
    private const string HeaderNamePrimaryPropertyValue = "primary_property_value";
    private const string HeaderNameReferencedPhoneNumber = "referenced_phone_number";
    private const string HeaderNameReferencedUserProfileId = "referenced_user_profile_id";

    private readonly BlobClient _blobClient;
    private readonly ILogger<MySchemafulObjectCsvReader> _logger;
    private readonly Schema _schema;

    private bool _canRead = true;

    private long _lastByteCount;
    private long _lastBytePosition;
    private long _numOfRecords;
    private string[]? _headers;

    public MySchemafulObjectCsvReader(
        BlobClient blobClient,
        MySchemafulObjectCsvReaderState? mySchemafulObjectCsvReaderState,
        ILogger<MySchemafulObjectCsvReader> logger,
        Schema schema)
    {
        _blobClient = blobClient;
        _logger = logger;
        _schema = schema;

        if (mySchemafulObjectCsvReaderState == null)
        {
            _lastByteCount = 0;
            _lastBytePosition = 0;
            _numOfRecords = 0;
            _headers = null;
        }
        else
        {
            _lastByteCount = 0;
            _lastBytePosition = mySchemafulObjectCsvReaderState.LastBytePosition;
            _numOfRecords = mySchemafulObjectCsvReaderState.NumOfRecords;
            _headers = mySchemafulObjectCsvReaderState.Headers;
        }
    }

    public static string GetCsvTemplate(List<Property> properties)
    {
        return string.Empty
               + HeaderNamePrimaryPropertyValue + ","
               + HeaderNameReferencedPhoneNumber + ","
               + HeaderNameReferencedUserProfileId + ","
               + string.Join(",", properties.Select(p => p.UniqueName))
               + "\n\"ABC00001\",\"85290000000\",\"74ca584a-bc23-4dc3-8365-30bec23fcf66\","
               + string.Join(",", properties.Select(GetPropertyValueTemplate));
    }

    public MySchemafulObjectCsvReaderState GetState()
    {
        var isCompleted = !_canRead;

        return new MySchemafulObjectCsvReaderState(
            _lastBytePosition,
            _numOfRecords,
            _headers,
            isCompleted);
    }

    public async Task<string[]> GetHeadersAsync()
    {
        if (_headers != null)
        {
            return _headers;
        }

        if (_lastBytePosition != 0)
        {
            throw new Exception("The headers have already been read.");
        }

        await using var stream = await _blobClient.OpenReadAsync(false, 0L, 1024);
        using var streamReader = new StreamReader(stream, Encoding.UTF8);
        using var csv = new CsvReader(
            streamReader,
            new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                CountBytes = true
            });

        _canRead = await csv.ReadAsync();
        _canRead = csv.ReadHeader();

        _lastBytePosition = csv.Parser.ByteCount;
        _headers = csv.HeaderRecord!;

        var duplicatedHeaders = _headers
            .GroupBy(h => h)
            .Where(g => g.Count() > 1)
            .ToList();
        if (duplicatedHeaders.Any())
        {
            throw new SfUserFriendlyException(
                $"There are some duplicated headers {string.Join(", ", duplicatedHeaders.Select(g => g.Key))}");
        }

        var nonExistedProperties = _headers
            .Skip(3) // skip the first 3 system headers
            .Where(h => _schema.Properties.TrueForAll(p => p.UniqueName != h))
            .ToList();
        if (nonExistedProperties.Any())
        {
            throw new SfUserFriendlyException(
                $"There are some non-existed properties in headers {string.Join(", ", nonExistedProperties)}");
        }

        return _headers;
    }

    public async Task<List<MyReaderEntry>> ReadLinesAsync(int limit = 1)
    {
        if (_headers == null)
        {
            throw new Exception("Please get headers first");
        }

        if (!_canRead)
        {
            throw new Exception("No more records to read");
        }

        var entries = new List<MyReaderEntry>();

        await using var stream = await _blobClient.OpenReadAsync(false, _lastBytePosition, 1024);
        using var streamReader = new StreamReader(stream, Encoding.UTF8);
        using var csv = new CsvReader(
            streamReader,
            new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                CountBytes = true
            });

        var globalLimit = _numOfRecords + limit;
        while (true)
        {
            _canRead = await csv.ReadAsync();
            if (!_canRead)
            {
                break;
            }

            try
            {
                string primaryPropertyValue = string.Empty;
                string sleekflowUserProfileId = string.Empty;
                Dictionary<string, object?> propertyValues = _schema.Properties.ToDictionary(p => p.Id, _ => (object?)null);

                for (var headerIndex = 0; headerIndex < _headers.Length; headerIndex++)
                {
                    var header = _headers[headerIndex];
                    var value = csv.GetField(headerIndex);

                    switch (header)
                    {
                        case HeaderNamePrimaryPropertyValue:
                            primaryPropertyValue = value;

                            break;
                        case HeaderNameReferencedPhoneNumber:
                            break;
                        case HeaderNameReferencedUserProfileId:
                            sleekflowUserProfileId = value;

                            break;
                        default:
                            var property = _schema.Properties.Find(p => p.UniqueName == header)!;
                            propertyValues[property.Id] = value;

                            break;
                    }
                }

                entries.Add(
                    new MyReaderEntry(
                        null,
                        new MyReaderSchemafulObjectDto(
                            primaryPropertyValue,
                            sleekflowUserProfileId,
                            propertyValues),
                        csv.Parser.Row));
            }
            catch (Exception e)
            {
                var message = $"{nameof(MySchemafulObjectCsvReader)} Unable to read the csv at the row=[{csv.Parser.Row}], message=[{e.Message}]";

                _logger.LogError(
                    e,
                    message);

                entries.Add(
                    new MyReaderEntry(
                        message,
                        null,
                        csv.Parser.Row));
            }

            _lastBytePosition += csv.Parser.ByteCount - _lastByteCount;
            _lastByteCount = csv.Parser.ByteCount;
            _numOfRecords++;

            if (!_canRead || _numOfRecords >= globalLimit)
            {
                break;
            }
        }

        return entries;
    }

    private static object GetPropertyValueTemplate(Property property)
    {
        return
            "\""
            + SampleDataUtils.GetPropertyDefaultValueForCsv(property)
            + "\"";
    }
}