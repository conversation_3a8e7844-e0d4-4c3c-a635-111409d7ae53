using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.VNet;
using Sleekflow.Infras.Constants;
using App = Pulumi.AzureNative.App.V20230401Preview;
using ManagedEnvironmentPeerAuthenticationArgs =
    Pulumi.AzureNative.App.V20230401Preview.Inputs.ManagedEnvironmentPeerAuthenticationArgs;
using MtlsArgs = Pulumi.AzureNative.App.V20230401Preview.Inputs.MtlsArgs;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using WorkloadProfileArgs = Pulumi.AzureNative.App.V20230401Preview.Inputs.WorkloadProfileArgs;

namespace Sleekflow.Infras.Components;

public class VNetManagedEnv
{
    private readonly ResourceGroup _resourceGroup;
    private readonly OperationalInsights.Workspace _logAnalyticsWorkspace;
    private readonly WebhookBridgeVNetOutput _vnetOutput;

    public VNetManagedEnv(
        ResourceGroup resourceGroup,
        OperationalInsights.Workspace logAnalyticsWorkspace,
        WebhookBridgeVNetOutput vnetOutput)
    {
        _resourceGroup = resourceGroup;
        _logAnalyticsWorkspace = logAnalyticsWorkspace;
        _vnetOutput = vnetOutput;
    }

    public App.ManagedEnvironment InitVNetManagedEnv(string? name = null, string? locationName = null)
    {
        var workspaceSharedKeys = Output
            .Tuple(_resourceGroup.Name, _logAnalyticsWorkspace.Name)
            .Apply(items => OperationalInsights.GetSharedKeys.InvokeAsync(
                new OperationalInsights.GetSharedKeysArgs
                {
                    ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                }));

        var managedEnvName = name == null
            ? "sleekflow-container-apps-env-vnet"
            : $"sleekflow-container-apps-env-vnet-{name}";

        var managedEnv = new App.ManagedEnvironment(
            managedEnvName,
            new App.ManagedEnvironmentArgs
            {
                ResourceGroupName = _resourceGroup.Name,
                Location = locationName is not null
                    ? LocationNames.GetAzureLocation(locationName)
                    : _resourceGroup.Location,
                EnvironmentName = managedEnvName,
                VnetConfiguration = new App.Inputs.VnetConfigurationArgs
                {
                    InfrastructureSubnetId = _vnetOutput.Subnet.Id, Internal = false
                },
                WorkloadProfiles = new InputList<WorkloadProfileArgs>()
                {
                    new WorkloadProfileArgs
                    {
                        WorkloadProfileType = "Consumption", Name = "Consumption",
                    }
                },
                PeerAuthentication = new ManagedEnvironmentPeerAuthenticationArgs()
                {
                    Mtls = new MtlsArgs()
                    {
                        Enabled = false
                    }
                },
            },
            new CustomResourceOptions
            {
                Parent = _resourceGroup,
                DependsOn = new InputList<Pulumi.Resource>
                {
                    _vnetOutput.Subnet, _vnetOutput.NatGateway
                }
            });

        return managedEnv;
    }
}