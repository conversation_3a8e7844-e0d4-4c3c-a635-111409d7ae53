﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Triggers.Balances;

[TriggerGroup(ControllerNames.Balances)]
public class EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooks
    : ITrigger<
        EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooks.
        EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksInput,
        EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooks.
        EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutput>
{
    private readonly IBus _bus;

    public EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooks(IBus bus)
    {
        _bus = bus;
    }

    public class EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksInput(string secret)
    {
        [JsonProperty("secret")]
        public string? Secret { get; set; } = secret;
    }

    public class EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutput
    {
    }

    public async Task<EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutput> F(
        EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksInput
            enqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksInput)
    {
        if (enqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksInput.Secret is not "only-for-sleekflow")
        {
            return new EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutput();
        }

        await _bus.Publish(
            new OneTimeCompaniesBusinessBalanceChangedEventWebhooksBulkUpsertEvent());

        return new EnqueueOneTimeBulkUpsertCompaniesBusinessBalanceChangedEventWebhooksOutput();
    }
}