name: 'Check Build Needed'
description: 'Determines if a service needs to be rebuilt or can be retagged'

inputs:
  common_changed:
    description: 'True if common files have changed'
    required: true
  service_changed:
    description: 'True if service-specific files have changed'
    required: true
  branch_name:
    description: 'The current branch name'
    required: true
  image_name:
    description: 'The image name to check'
    required: true
  image_prefix:
    description: 'The image prefix'
    required: true
  registry:
    description: 'The registry URL'
    required: true

outputs:
  action:
    description: 'Whether to build or retag'
    value: ${{ steps.decide.outputs.action }}

runs:
  using: 'composite'
  steps:
    - id: decide
      shell: bash
      run: |
        echo "::group::Debug - Process Service"
        need_build=false

        # Check if changes require a build
        if [[ "${{ inputs.common_changed }}" == "true" || "${{ inputs.service_changed }}" == "true" ]]; then
          echo "Changes detected, need to build"
          need_build=true
        else
          echo "No changes detected, checking if image exists"
          # Check if image exists in registry without pulling
          if ! docker buildx imagetools inspect ${{ inputs.registry }}/${{ inputs.image_prefix }}/${{ inputs.image_name }}:${{ inputs.branch_name }} >/dev/null 2>&1; then
            echo "Branch-specific image not found, need to build"
            need_build=true
          else
            echo "Branch-specific image exists in registry, will re-tag"
          fi
        fi

        if [[ "$need_build" == "true" ]]; then
          echo "action=build" >> $GITHUB_OUTPUT
        else
          echo "action=retag" >> $GITHUB_OUTPUT
        fi
        echo "::endgroup::" 