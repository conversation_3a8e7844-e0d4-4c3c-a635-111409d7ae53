﻿using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.CrmHub.Unifies;
using Sleekflow.DependencyInjection;
using Sleekflow.Models.CrmHubToFlowHubMigrations;

namespace Sleekflow.CrmHub.Migrations;

public interface ICrmHubToFlowHubMigrationService
{
    Task<List<SleekflowFieldToCrmProviderFieldMapping>> GetSleekflowFieldToProviderFieldMappings(
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName);
}

public class CrmHubToFlowHubMigrationService : IScopedService, ICrmHubToFlowHubMigrationService
{
    private readonly IUnifyService _unifyService;

    public CrmHubToFlowHubMigrationService(
        IUnifyService unifyService)
    {
        _unifyService = unifyService;
    }

    public async Task<List<SleekflowFieldToCrmProviderFieldMapping>> GetSleekflowFieldToProviderFieldMappings(
        string sleekflowCompanyId,
        string entityTypeName,
        string providerName)
    {
        var flattenedUnifyRules = await _unifyService.GetFlattenedUnifyRules(
            sleekflowCompanyId,
            entityTypeName);

        return ExtractFieldMappingsFromFlattenedUnifyRules(flattenedUnifyRules, providerName);
    }

    private static List<SleekflowFieldToCrmProviderFieldMapping> ExtractFieldMappingsFromFlattenedUnifyRules(
        List<FlattenedUnifyRule> flattenedUnifyRules,
        string providerName)
    {
        var sleekflowMappings = flattenedUnifyRules
            .Where(rule => rule.ProviderFieldName.StartsWith("sleekflow:"))
            .GroupBy(rule => rule.FieldName)
            .ToDictionary(
                group => group.Key,
                group => group.First().ProviderFieldName.Replace(
                    "sleekflow:",
                    string.Empty));

        var providerMappings = flattenedUnifyRules
            .Where(rule => rule.ProviderFieldName.StartsWith(providerName + ":"))
            .GroupBy(rule => rule.FieldName)
            .ToDictionary(
                group => group.Key,
                group => group.First().ProviderFieldName.Replace(
                    providerName + ":",
                    string.Empty));

        return sleekflowMappings.Keys
            .Intersect(providerMappings.Keys)
            .Select(fieldName =>
                new SleekflowFieldToCrmProviderFieldMapping(
                    sleekflowMappings[fieldName],
                    providerMappings[fieldName]))
            .ToList();
    }
}