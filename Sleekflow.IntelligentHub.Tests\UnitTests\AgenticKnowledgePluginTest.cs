using System.Reflection;
using Sleekflow.IntelligentHub.Plugins.Knowledges;

namespace Sleekflow.IntelligentHub.Tests.UnitTests;

[TestFixture]
public class AgenticKnowledgePluginTest
{
    private MethodInfo? _separateMultipleJsonObjectsMethod;

    [SetUp]
    public void SetUp()
    {
        _separateMultipleJsonObjectsMethod = typeof(AgenticKnowledgePlugin)
            .GetMethod("SeparateMultipleJsonObjects", BindingFlags.NonPublic | BindingFlags.Static);
        Assert.That(_separateMultipleJsonObjectsMethod, Is.Not.Null, "SeparateMultipleJsonObjects method should exist");
    }

    public class JsonSeparationTestCase
    {
        public string TestName { get; set; } = string.Empty;

        public string Input { get; set; } = string.Empty;

        public string[] ExpectedOutputs { get; set; } = [];

        public string Description { get; set; } = string.Empty;
    }

    private static IEnumerable<JsonSeparationTestCase> JsonSeparationTestCases
    {
        get
        {
            // Test Case 1: Single JSON object
            yield return new JsonSeparationTestCase
            {
                TestName = "Single JSON Object",
                Input = """{"phase": "search", "query": "test"}""",
                ExpectedOutputs = ["""{"phase": "search", "query": "test"}"""],
                Description = "Should correctly identify and return a single JSON object"
            };

            // Test Case 2: Two simple JSON objects concatenated
            yield return new JsonSeparationTestCase
            {
                TestName = "Two Simple JSON Objects",
                Input = """{"phase": "search"}{"phase": "report"}""",
                ExpectedOutputs = ["""{"phase": "search"}""", """{"phase": "report"}"""],
                Description = "Should separate two simple JSON objects"
            };

            // Test Case 3: Multiple JSON objects with complex content
            yield return new JsonSeparationTestCase
            {
                TestName = "Multiple Complex JSON Objects",
                Input =
                    """{"phase": "search", "query": "test", "results": [1,2,3]}{"phase": "analyze", "data": {"count": 5, "status": "ok"}}{"phase": "report", "final": true}""",
                ExpectedOutputs =
                [
                    """{"phase": "search", "query": "test", "results": [1,2,3]}""",
                    """{"phase": "analyze", "data": {"count": 5, "status": "ok"}}""",
                    """{"phase": "report", "final": true}"""
                ],
                Description = "Should separate multiple complex JSON objects with nested structures"
            };

            // Test Case 4: JSON objects with strings containing braces
            yield return new JsonSeparationTestCase
            {
                TestName = "JSON Objects With Braces In Strings",
                Input =
                    """{"message": "Use {braces} in strings"}{"warning": "Don't confuse {this} with actual JSON"}""",
                ExpectedOutputs =
                [
                    """{"message": "Use {braces} in strings"}""",
                    """{"warning": "Don't confuse {this} with actual JSON"}"""
                ],
                Description = "Should correctly handle braces that appear within string literals"
            };

            // Test Case 5: JSON objects with escaped quotes
            yield return new JsonSeparationTestCase
            {
                TestName = "JSON Objects With Escaped Quotes",
                Input = """{"quote": "He said \"Hello\""}{"response": "She replied \"Hi there\""}""",
                ExpectedOutputs =
                [
                    """{"quote": "He said \"Hello\""}""",
                    """{"response": "She replied \"Hi there\""}"""
                ],
                Description = "Should properly handle escaped quotes within JSON strings"
            };

            // Test Case 6: JSON objects with nested objects and arrays
            yield return new JsonSeparationTestCase
            {
                TestName = "Deeply Nested JSON Objects",
                Input = """{"outer": {"inner": {"deep": {"value": "test"}}}}{"array": [{"item": 1}, {"item": 2}]}""",
                ExpectedOutputs =
                [
                    """{"outer": {"inner": {"deep": {"value": "test"}}}}""",
                    """{"array": [{"item": 1}, {"item": 2}]}"""
                ],
                Description = "Should handle deeply nested JSON structures"
            };

            // Test Case 7: JSON objects with whitespace
            yield return new JsonSeparationTestCase
            {
                TestName = "JSON Objects With Whitespace",
                Input = """  {"phase": "search"}  {"phase": "report"}  """,
                ExpectedOutputs =
                [
                    """{"phase": "search"}""",
                    """{"phase": "report"}"""
                ],
                Description = "Should trim whitespace and extract clean JSON objects"
            };

            // Test Case 8: Empty string
            yield return new JsonSeparationTestCase
            {
                TestName = "Empty String",
                Input = "",
                ExpectedOutputs = [],
                Description = "Should return empty list for empty input"
            };

            // Test Case 9: Whitespace only
            yield return new JsonSeparationTestCase
            {
                TestName = "Whitespace Only",
                Input = "   \n\t   ",
                ExpectedOutputs = [],
                Description = "Should return empty list for whitespace-only input"
            };

            // Test Case 10: Non-JSON content (fallback behavior)
            yield return new JsonSeparationTestCase
            {
                TestName = "Non-JSON Content",
                Input = "This is just plain text without JSON",
                ExpectedOutputs = ["This is just plain text without JSON"],
                Description = "Should return original content as single item when no JSON structure is detected"
            };

            // Test Case 11: Malformed JSON (fallback behavior)
            yield return new JsonSeparationTestCase
            {
                TestName = "Malformed JSON",
                Input = """{"incomplete": "json" """,
                ExpectedOutputs = ["""{"incomplete": "json" """.Trim()],
                Description = "Should return original content for malformed JSON"
            };

            // Test Case 12: Mixed JSON and non-JSON content
            yield return new JsonSeparationTestCase
            {
                TestName = "Mixed JSON and Non-JSON",
                Input = """Some text {"valid": "json"} more text {"another": "json"} final text""",
                ExpectedOutputs =
                [
                    """{"valid": "json"}""",
                    """{"another": "json"}"""
                ],
                Description = "Should extract valid JSON objects from mixed content"
            };

            // Test Case 13: JSON with special characters and Unicode
            yield return new JsonSeparationTestCase
            {
                TestName = "JSON With Special Characters",
                Input =
                    """{"chinese": "你好世界", "emoji": "😀🎉", "symbols": "!@#$%^&*()"}{"japanese": "こんにちは", "korean": "안녕하세요"}""",
                ExpectedOutputs =
                [
                    """{"chinese": "你好世界", "emoji": "😀🎉", "symbols": "!@#$%^&*()"}""",
                    """{"japanese": "こんにちは", "korean": "안녕하세요"}"""
                ],
                Description = "Should handle Unicode and special characters correctly"
            };

            // Test Case 14: JSON with multiline strings
            yield return new JsonSeparationTestCase
            {
                TestName = "JSON With Multiline Content",
                Input = """{"multiline": "Line 1\nLine 2\nLine 3"}{"another": "Single line"}""",
                ExpectedOutputs =
                [
                    """{"multiline": "Line 1\nLine 2\nLine 3"}""",
                    """{"another": "Single line"}"""
                ],
                Description = "Should handle JSON objects containing multiline strings"
            };

            // Test Case 15: Single character and edge cases
            yield return new JsonSeparationTestCase
            {
                TestName = "Edge Cases",
                Input = """{}{"empty": ""}""",
                ExpectedOutputs = ["""{}""", """{"empty": ""}"""],
                Description = "Should handle empty JSON objects and empty string values"
            };

            // Test Case 16: Real-world KnowledgeRetrievalAgentResponse example
            yield return new JsonSeparationTestCase
            {
                TestName = "Real-world Agent Response",
                Input =
                    """{"phase": "search", "query": "SleekFlow pricing", "reasoning": "Searching for pricing information"}{"phase": "analyze", "data": {"found": true, "relevance": 0.95}}{"phase": "report", "result": "SleekFlow offers three pricing tiers: Basic, Standard, and Premium"}""",
                ExpectedOutputs =
                [
                    """{"phase": "search", "query": "SleekFlow pricing", "reasoning": "Searching for pricing information"}""",
                    """{"phase": "analyze", "data": {"found": true, "relevance": 0.95}}""",
                    """{"phase": "report", "result": "SleekFlow offers three pricing tiers: Basic, Standard, and Premium"}"""
                ],
                Description = "Should handle realistic agent response JSON sequences"
            };
        }
    }

    [TestCaseSource(nameof(JsonSeparationTestCases))]
    [Parallelizable(ParallelScope.Children)]
    public void SeparateMultipleJsonObjects_VariousInputs_Test(JsonSeparationTestCase testCase)
    {
        // Act
        var result = InvokeSeparateMultipleJsonObjects(testCase.Input);

        // Assert
        TestContext.WriteLine($"Test: {testCase.TestName}");
        TestContext.WriteLine($"Description: {testCase.Description}");
        TestContext.WriteLine($"Input: {testCase.Input}");
        TestContext.WriteLine(
            $"Expected Outputs: [{string.Join(", ", testCase.ExpectedOutputs.Select(o => $"\"{o}\""))}]");
        TestContext.WriteLine($"Actual Outputs: [{string.Join(", ", result.Select(o => $"\"{o}\""))}]");
        TestContext.WriteLine();

        Assert.That(result, Is.Not.Null, "Result should not be null");
        Assert.That(
            result.Count,
            Is.EqualTo(testCase.ExpectedOutputs.Length),
            $"Expected {testCase.ExpectedOutputs.Length} outputs but got {result.Count}");

        for (int i = 0; i < testCase.ExpectedOutputs.Length; i++)
        {
            Assert.That(
                result[i],
                Is.EqualTo(testCase.ExpectedOutputs[i]),
                $"Output at index {i} does not match expected value");
        }
    }

    [Test]
    public void SeparateMultipleJsonObjects_WithNestedBracesInStrings_ShouldNotConfuseParser()
    {
        // Arrange
        var input = """{"config": "{\"nested\": \"value\"}", "status": "ok"}{"data": "Contains } and { characters"}""";

        // Act
        var result = InvokeSeparateMultipleJsonObjects(input);

        // Assert
        TestContext.WriteLine($"Input: {input}");
        TestContext.WriteLine($"Results: [{string.Join(", ", result.Select(r => $"\"{r}\""))}]");

        Assert.That(result, Has.Count.EqualTo(2));
        Assert.That(result[0], Is.EqualTo("""{"config": "{\"nested\": \"value\"}", "status": "ok"}"""));
        Assert.That(result[1], Is.EqualTo("""{"data": "Contains } and { characters"}"""));
    }

    [Test]
    public void SeparateMultipleJsonObjects_WithEscapedQuotes_ShouldHandleCorrectly()
    {
        // Arrange
        var input =
            """
            {"message": "He said \"Hello\""}{"response": "She replied \"Hi there\""
            """;

        // Act
        var result = InvokeSeparateMultipleJsonObjects(input);

        // Assert
        TestContext.WriteLine($"Input: {input}");
        TestContext.WriteLine($"Results: [{string.Join(", ", result.Select(r => $"\"{r}\""))}]");

        Assert.That(result, Has.Count.EqualTo(1));
        Assert.That(result[0], Does.Contain("message"));
        Assert.That(result[1], Does.Contain("response"));
    }

    [Test]
    public void SeparateMultipleJsonObjects_WithLargeInput_ShouldPerformReasonably()
    {
        // Arrange - Create a large input with many JSON objects
        var jsonObjects = new List<string>();
        for (int i = 0; i < 100; i++)
        {
            jsonObjects.Add(
                $$"""{"id": {{i}}, "data": "This is test data for object number {{i}}", "timestamp": "2025-01-01T00:00:00Z"}""");
        }

        var input = string.Join("", jsonObjects);

        // Act
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var result = InvokeSeparateMultipleJsonObjects(input);
        stopwatch.Stop();

        // Assert
        TestContext.WriteLine($"Processed {jsonObjects.Count} JSON objects in {stopwatch.ElapsedMilliseconds}ms");
        TestContext.WriteLine($"Input length: {input.Length} characters");
        TestContext.WriteLine($"Output count: {result.Count}");

        Assert.That(result, Has.Count.EqualTo(100));
        Assert.That(stopwatch.ElapsedMilliseconds, Is.LessThan(1000), "Should complete within reasonable time");

        // Verify first and last objects are correctly parsed
        Assert.That(result[0], Does.Contain("\"id\": 0"));
        Assert.That(result[99], Does.Contain("\"id\": 99"));
    }

    private List<string> InvokeSeparateMultipleJsonObjects(string input)
    {
        return (List<string>) _separateMultipleJsonObjectsMethod!.Invoke(null, [input])!;
    }
}