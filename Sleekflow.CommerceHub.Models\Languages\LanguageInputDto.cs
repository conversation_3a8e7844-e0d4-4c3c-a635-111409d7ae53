using Newtonsoft.Json;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Models.Languages;

public class LanguageInputDto
{
    [ValidateIsoLanguageCode]
    [JsonProperty(Language.PropertyNameLanguageIsoCode)]
    public string LanguageIsoCode { get; set; }

    [JsonProperty(Language.PropertyNameIsDefault)]
    public bool IsDefault { get; set; }

    [JsonConstructor]
    public LanguageInputDto(
        string languageIsoCode,
        bool isDefault)
    {
        LanguageIsoCode = languageIsoCode;
        IsDefault = isDefault;
    }
}