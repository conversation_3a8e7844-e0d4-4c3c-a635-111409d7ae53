﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class GetActionNeeds : ITrigger<
    GetActionNeeds.GetActionNeedsInput,
    GetActionNeeds.GetActionNeedsOutput>
{
    private readonly INeedConfigService _needConfigService;

    public GetActionNeeds(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class GetActionNeedsInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("action_group")]
        public string ActionGroup { get; set; }

        [Required]
        [JsonProperty("action_subgroup")]
        public string ActionSubgroup { get; set; }

        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonProperty("parameters")]
        public Dictionary<string, object?>? Parameters { get; set; }

        [JsonConstructor]
        public GetActionNeedsInput(
            string sleekflowCompanyId,
            string actionGroup,
            string actionSubgroup,
            string? version,
            Dictionary<string, object?>? parameters)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ActionGroup = actionGroup;
            ActionSubgroup = actionSubgroup;
            Version = version;
            Parameters = parameters;
        }
    }

    public class GetActionNeedsOutput
    {
        [JsonProperty("needs")]
        public List<ActionNeedConfigDto> Needs { get; set; }

        [JsonConstructor]
        public GetActionNeedsOutput(List<ActionNeedConfigDto> needs)
        {
            Needs = needs;
        }
    }

    public async Task<GetActionNeedsOutput> F(GetActionNeedsInput input)
    {
        var needConfigs = await _needConfigService.GetActionNeedsAsync(
            input.SleekflowCompanyId,
            input.ActionGroup,
            input.ActionSubgroup,
            input.Version,
            input.Parameters);

        return new GetActionNeedsOutput(
            needConfigs
                .Select(x => new ActionNeedConfigDto(x))
                .ToList());
    }
}