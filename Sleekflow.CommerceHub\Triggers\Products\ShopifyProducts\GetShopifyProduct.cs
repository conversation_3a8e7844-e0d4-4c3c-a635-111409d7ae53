using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.CommerceHub.Products.ShopifyProducts;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products.ShopifyProducts;

[TriggerGroup(ControllerNames.ShopifyProducts)]
public class GetShopifyProduct
    : ITrigger<
        GetShopifyProduct.GetShopifyProductInput,
        GetShopifyProduct.GetShopifyProductOutput>
{
    private readonly IShopifyProductService _shopifyProductService;
    private readonly IProductVariantService _productVariantService;

    public GetShopifyProduct(
        IShopifyProductService shopifyProductService,
        IProductVariantService productVariantService)
    {
        _shopifyProductService = shopifyProductService;
        _productVariantService = productVariantService;
    }

    public class GetShopifyProductInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [StringLength(128, MinimumLength = 1)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty("platform_data_id")]
        public string PlatformDataId { get; set; }

        [JsonConstructor]
        public GetShopifyProductInput(
            string sleekflowCompanyId,
            string storeId,
            string platformDataId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            PlatformDataId = platformDataId;
        }
    }

    public class GetShopifyProductOutput
    {
        [JsonProperty("shopify_product")]
        public ProductDto? ShopifyProduct { get; set; }

        [JsonConstructor]
        public GetShopifyProductOutput(ProductDto? shopifyProduct)
        {
            ShopifyProduct = shopifyProduct;
        }
    }

    public async Task<GetShopifyProductOutput> F(
        GetShopifyProductInput getShopifyProductInput)
    {
        var shopifyProduct = await _shopifyProductService.GetShopifyProductAsync(
            getShopifyProductInput.SleekflowCompanyId,
            getShopifyProductInput.StoreId,
            getShopifyProductInput.PlatformDataId);

        if (shopifyProduct is null)
        {
            return new GetShopifyProductOutput(null);
        }

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            getShopifyProductInput.SleekflowCompanyId,
            getShopifyProductInput.StoreId,
            shopifyProduct.Id);

        var productIdToProductVariantsDict = productVariants
            .GroupBy(pv => pv.ProductId)
            .ToDictionary(e => e.Key, e => e.ToList());

        return new GetShopifyProductOutput(
            new ProductDto(
                shopifyProduct,
                productIdToProductVariantsDict.GetValueOrDefault(shopifyProduct.Id, new List<ProductVariant>())));
    }
}