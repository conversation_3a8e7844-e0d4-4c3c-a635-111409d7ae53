using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;

public class CheckWorkflowContactEnrolmentConditionOutput
{
    [JsonProperty("enrolment_condition_satisfied")]
    public bool EnrolmentConditionSatisfied { get; set; }

    [JsonConstructor]
    public CheckWorkflowContactEnrolmentConditionOutput(bool enrolmentConditionSatisfied)
    {
        EnrolmentConditionSatisfied = enrolmentConditionSatisfied;
    }
}