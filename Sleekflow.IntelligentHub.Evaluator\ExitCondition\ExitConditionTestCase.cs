﻿using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Evaluator.ExitCondition;

public record ExitConditionTestCase(
    string <PERSON><PERSON>rio,
    ChatMessageContent[] ChatMessageContents,
    List<Models.Companies.CompanyAgentConfigs.Actions.ExitCondition> ExitConditions,
    ExitConditionResult ExpectedExitConditionResult);