using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Currencies;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Currencies;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Currencies;

[TriggerGroup(ControllerNames.Currencies)]
public class GetSupportedCurrencies
    : ITrigger<
        GetSupportedCurrencies.GetSupportedCurrenciesInput,
        GetSupportedCurrencies.GetSupportedCurrenciesOutput>
{
    private readonly ICurrencyService _currencyService;
    private readonly IStoreService _storeService;

    public GetSupportedCurrencies(
        ICurrencyService currencyService,
        IStoreService storeService)
    {
        _currencyService = currencyService;
        _storeService = storeService;
    }

    public class GetSupportedCurrenciesInput
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [JsonConstructor]
        public GetSupportedCurrenciesInput(string sleekflowCompanyId, string storeId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
        }
    }

    public class GetSupportedCurrenciesOutput
    {
        [JsonProperty("currencies")]
        public List<CurrencyDto> Currencies { get; set; }

        [JsonConstructor]
        public GetSupportedCurrenciesOutput(
            List<CurrencyDto> currencies)
        {
            Currencies = currencies;
        }
    }

    public async Task<GetSupportedCurrenciesOutput> F(GetSupportedCurrenciesInput getSupportedCurrenciesInput)
    {
        var currencies = _currencyService.GetCurrencies();

        var store = await _storeService.GetStoreAsync(
            getSupportedCurrenciesInput.StoreId,
            getSupportedCurrenciesInput.SleekflowCompanyId);
        var storeCurrencyIsoCodes = store.Currencies.Select(c => c.CurrencyIsoCode).ToHashSet();

        return new GetSupportedCurrenciesOutput(
            currencies
                .Select(c => new CurrencyDto(c))
                .Where(c => storeCurrencyIsoCodes.Contains(c.CurrencyIsoCode))
                .ToList());
    }
}