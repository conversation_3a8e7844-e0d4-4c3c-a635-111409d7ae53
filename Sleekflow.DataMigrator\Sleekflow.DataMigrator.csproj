<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\Sleekflow.CrmHub.Models\Sleekflow.CrmHub.Models.csproj" />
      <ProjectReference Include="..\Sleekflow.TenantHub.Models\Sleekflow.TenantHub.Models.csproj" />
      <ProjectReference Include="..\Sleekflow\Sleekflow.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="ConsoleAppFramework" Version="5.4.1">
        <PrivateAssets>all</PrivateAssets>
        <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      </PackageReference>
      <PackageReference Include="Sharprompt" Version="3.0.0" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Working" />
    </ItemGroup>

</Project>
