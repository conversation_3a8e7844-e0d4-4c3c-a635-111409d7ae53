using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Events;

public class StartStreamingSendMessageEvent
{
    [JsonProperty("recommended_reply_step_id")]
    public string RecommendReplyStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    public string ProxyStateId { get; set; }

    [JsonProperty("send_message_input")]
    public SendMessageInput SendMessageInput { get; set; }

    [JsonProperty("send_message_step_id")]
    public string SendMessageStepId { get; set; }

    [JsonProperty("step_entries")]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public StartStreamingSendMessageEvent(
        string recommendReplyStepId,
        string proxyStateId,
        SendMessageInput sendMessageInput,
        string sendMessageStepId,
        Stack<StackEntry> stackEntries)
    {
        RecommendReplyStepId = recommendReplyStepId;
        ProxyStateId = proxyStateId;
        SendMessageInput = sendMessageInput;
        SendMessageStepId = sendMessageStepId;
        StackEntries = stackEntries;
    }
}