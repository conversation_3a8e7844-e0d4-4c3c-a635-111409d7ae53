using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;

namespace Sleekflow.FlowHub.Models.Messages;

public class MessageBody
{
    #region Standard

    [JsonProperty("audio_message")]
    public AudioMessageObject? AudioMessage { get; set; }

    [JsonProperty("contacts_message")]
    public List<ContactMessageObject>? ContactsMessage { get; set; }

    [JsonProperty("currency_message")]
    public CurrencyMessageObject? CurrencyMessage { get; set; }

    [JsonProperty("document_message")]
    public DocumentMessageObject? DocumentMessage { get; set; }

    [JsonProperty("image_message")]
    public ImageMessageObject? ImageMessage { get; set; }

    [JsonProperty("location_message")]
    public LocationMessageObject? LocationMessage { get; set; }

    [JsonProperty("reaction_message")]
    public ReactionMessageObject? ReactionMessage { get; set; }

    [JsonProperty("text_message")]
    public TextMessageObject? TextMessage { get; set; }

    [JsonProperty("video_message")]
    public VideoMessageObject? VideoMessage { get; set; }

    [Json<PERSON>roperty("date_time_message")]
    public DateTimeMessageObject? DateTimeMessage { get; set; }

    #endregion

    #region WhatsApp Specific

    [JsonProperty("interactive_message")]
    public InteractiveMessageObject? InteractiveMessage { get; set; }

    [JsonProperty("template_message")]
    public TemplateMessageObject? TemplateMessage { get; set; }

    [JsonProperty("interactive_reply_message")]
    public InteractiveReplyMessageObject? InteractiveReplyMessage { get; set; }

    [JsonProperty("order_message")]
    public OrderMessageObject? OrderMessage { get; set; }

    #endregion

    #region Platform Specific

    [JsonProperty("facebook_messenger_message")]
    public FacebookMessengerMessageObject? FacebookMessengerMessage { get; set; }

    [JsonProperty("instagram_messenger_message")]
    public InstagramMessengerMessageObject? InstagramMessengerMessage { get; set; }

    [JsonProperty("telegram_messenger_message")]
    public TelegramMessengerMessageObject? TelegramMessengerMessage { get; set; }

    [JsonProperty("wechat_messenger_message")]
    public WeChatMessengerMessageObject? WeChatMessengerMessage { get; set; }

    [JsonProperty("live_chat_message")]
    public LiveChatMessageObject? LiveChatMessage { get; set; }

    [JsonProperty("viber_message")]
    public ViberMessageObject? ViberMessage { get; set; }

    [JsonProperty("line_message")]
    public LineMessageObject? LineMessage { get; set; }

    [JsonProperty("sms_message")]
    public SmsMessageObject? SmsMessage { get; set; }

    #endregion

    [JsonConstructor]
    public MessageBody(
        AudioMessageObject? audioMessage,
        List<ContactMessageObject>? contactsMessage,
        CurrencyMessageObject? currencyMessage,
        DocumentMessageObject? documentMessage,
        ImageMessageObject? imageMessage,
        LocationMessageObject? locationMessage,
        ReactionMessageObject? reactionMessage,
        TextMessageObject? textMessage,
        VideoMessageObject? videoMessage,
        InteractiveMessageObject? interactiveMessage,
        TemplateMessageObject? templateMessage,
        InteractiveReplyMessageObject? interactiveReplyMessage,
        DateTimeMessageObject? dateTimeMessage,
        FacebookMessengerMessageObject? facebookMessengerMessage,
        InstagramMessengerMessageObject? instagramMessengerMessage,
        OrderMessageObject? orderMessage,
        TelegramMessengerMessageObject? telegramMessengerMessage,
        WeChatMessengerMessageObject? weChatMessengerMessage,
        LiveChatMessageObject? liveChatMessage,
        ViberMessageObject? viberMessage,
        LineMessageObject? lineMessage,
        SmsMessageObject? smsMessage)
    {
        AudioMessage = audioMessage;
        ContactsMessage = contactsMessage;
        CurrencyMessage = currencyMessage;
        DocumentMessage = documentMessage;
        ImageMessage = imageMessage;
        LocationMessage = locationMessage;
        ReactionMessage = reactionMessage;
        TextMessage = textMessage;
        VideoMessage = videoMessage;
        InteractiveMessage = interactiveMessage;
        TemplateMessage = templateMessage;
        InteractiveReplyMessage = interactiveReplyMessage;
        DateTimeMessage = dateTimeMessage;
        FacebookMessengerMessage = facebookMessengerMessage;
        InstagramMessengerMessage = instagramMessengerMessage;
        OrderMessage = orderMessage;
        TelegramMessengerMessage = telegramMessengerMessage;
        WeChatMessengerMessage = weChatMessengerMessage;
        LiveChatMessage = liveChatMessage;
        ViberMessage = viberMessage;
        LineMessage = lineMessage;
        SmsMessage = smsMessage;
    }
}