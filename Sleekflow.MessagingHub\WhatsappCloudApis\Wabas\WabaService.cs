using System.Collections.Concurrent;
using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.ApiClients.Models.Common;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Payloads;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.Ids;
using Sleekflow.JsonConfigs;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

public interface IWabaService
{
    Task<List<Waba>> ConnectWabaWithBusinessSystemUserAccessTokenAsync(
        string sleekflowCompanyId,
        string businessIntegrationSystemUserAccessToken,
        List<string>? forbiddenWabaIds,
        string? webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<List<Waba>> ConnectWabaAsync(
        string sleekflowCompanyId,
        string userAccessToken,
        List<string>? forbiddenWabaIds,
        string? webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<Waba> GetAndRefreshWabaAsync(
        string wabaId,
        string sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? businessIntegrationSystemUserAccessToken = null,
        string? userAccessToken = null);

    Task<Waba?> GetWabaOrDefaultAsync(string wabaId, string sleekflowCompanyId);

    Task<Waba> GetAndReconstructWabaAsync(
        Waba waba,
        string? sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? businessIntegrationSystemUserAccessToken = null,
        string? userAccessToken = null);

    Task<Waba> GetWabaWithFacebookWabaIdAsync(string facebookWabaId);

    Task<Waba> GetWabaWithWabaPhoneNumberIdAsync(string sleekflowCompanyId, string wabaPhoneNumberId);

    Task<Waba> GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
        string wabaId,
        string sleekflowCompanyId,
        string wabaPhoneNumberId);

    Task<Waba?> GetActiveWabaWithIdAndWabaPhoneNumberIdAsync(
        string wabaId,
        string wabaPhoneNumberId,
        string sleekflowCompanyId);

    Task<Waba?> GetWabaWithFacebookWabaIdAndFacebookPhoneNumberIdAsync(
        string facebookWabaId,
        string facebookPhoneNumberId);

    Task<List<Waba>> GetAllAsync();

    Task<(List<Waba> Wabas, string? NextContinuationToken)> GetWabaAsync(
        string? continuationToken,
        int limit);

    Task<List<Waba>> GetWabasAsync(string sleekflowCompanyId);

    Task<List<Waba>> GetConnectedWabaAsync(
        string sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<List<Waba>> GetAndRefreshWabasAsync(
        string sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? businessIntegrationSystemUserAccessToken,
        string? userAccessToken = null);

    Task<List<Waba>> GetWabaWithFacebookBusinessIdAsync(string facebookBusinessId);

    Task<List<Waba>?> GetConnectedWabaWithUserAccessTokenAsync(string userAccessToken);

    Task<(List<Waba>? Wabas, string BusinessIntegrationSystemUserAccessToken)>
        GetConnectedWabaWithBISUTokenFromFacebookAuthorizationCodeAsync(
            string facebookAuthorizationCode);

    Task<int> UnblockOrBlockWabaAsync(string id, string facebookWabaId, bool isUnblock);

    Task<bool> DeactivateWabaAsync(string facebookPhoneNumberId, string? businessIntegrationSystemUserAccessToken);

    Task<DebugTokenResponse> GetUserAccessTokenGranularScopes(string userAccessToken);

    (bool HasEnabledFLFB, DecryptedBusinessIntegrationSystemUserAccessTokenDto? DecryptedBusinessIntegrationSystemUserAccessTokenDto)
    GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(Waba waba);

    Task<string> ExchangeAccessTokenFromFacebookAuthorizationCodeAsync(string code);

    FacebookBusinessIntegrationSystemUserAccessToken
        GetEncryptedFacebookBusinessIntegrationSystemUserAccessToken(
            string businessIntegrationSystemUserAccessToken,
            DebugTokenResponse debugTokenResponse);

    Task<bool> DisassociateFacebookBusinessAccountFromCompanyAsync(string sleekflowCompanyId, string facebookBusinessId);

    Task<List<Waba>> OnboardPartnersToMMLiteAsync(
        string sleekflowCompanyId,
        string facebookBusinessId,
        AuditEntity.SleekflowStaff? sleekflowStaff);
}

public class WabaService : IWabaService, ISingletonService
{
    private readonly IBus _bus;
    private readonly IIdService _idService;
    private readonly ISecretConfig _secretConfig;
    private readonly ILogger<WabaService> _logger;
    private readonly IWabaRepository _wabaRepository;
    private readonly IAuditLogService _auditLogService;
    private readonly ICommonRetryPolicyService _commonRetryPolicyService;
    private readonly IWhatsappCloudApiBspClient _whatsappCloudApiBspClient;
    private readonly IWhatsappCloudApiWebhookConfig _whatsappCloudApiWebhookConfig;
    private readonly IWhatsappCloudApiAuthenticationClient _whatsappCloudApiAuthenticationClient;
    private readonly IWhatsappMMLiteApiClient _whatsappMMLiteApiClient;
    private readonly IAppConfig _appConfig;
    private readonly IFacebookCommerceClient _facebookCommerceClient;
    private readonly HttpClient _httpClient;
    private readonly IWabaAssetsManager _wabaAssetsManager;

    public WabaService(
        IBus bus,
        IIdService idService,
        ISecretConfig secretConfig,
        ILogger<WabaService> logger,
        IWabaRepository wabaRepository,
        IAuditLogService auditLogService,
        ICloudApiClients cloudApiClients,
        ICommonRetryPolicyService commonRetryPolicyService,
        IWhatsappCloudApiWebhookConfig whatsappCloudApiWebhookConfig,
        IAppConfig appConfig,
        IHttpClientFactory httpClientFactory,
        IWabaAssetsManager wabaAssetsManager)
    {
        _bus = bus;
        _logger = logger;
        _idService = idService;
        _secretConfig = secretConfig;
        _wabaRepository = wabaRepository;
        _auditLogService = auditLogService;
        _commonRetryPolicyService = commonRetryPolicyService;
        _whatsappCloudApiWebhookConfig = whatsappCloudApiWebhookConfig;
        _appConfig = appConfig;
        _whatsappCloudApiBspClient = cloudApiClients.WhatsappCloudApiBspClient;
        _whatsappCloudApiAuthenticationClient = cloudApiClients.WhatsappCloudApiAuthenticationClient;
        _whatsappMMLiteApiClient = cloudApiClients.WhatsappMMLiteApiClient;
        _facebookCommerceClient = cloudApiClients.FacebookCommerceClient;
        _httpClient = httpClientFactory.CreateClient("default-handler");
        _wabaAssetsManager = wabaAssetsManager;
    }

    public async Task<List<Waba>> ConnectWabaWithBusinessSystemUserAccessTokenAsync(
        string sleekflowCompanyId,
        string businessIntegrationSystemUserAccessToken,
        List<string>? forbiddenWabaIds,
        string? webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var debugTokenResponse =
            await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(businessIntegrationSystemUserAccessToken);

        var scopes = new WabaDebugTokenGranularScopes(
            _logger,
            debugTokenResponse);

        var wabaIds = forbiddenWabaIds == null
            ? scopes.WabaIds
            : scopes.WabaIds.Where(s => !forbiddenWabaIds.Contains(s)).ToList();

        var wabaConcurrentBag = new ConcurrentBag<Waba>();

        await Parallel.ForEachAsync(
            wabaIds,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            },
            async (wabaId, cancellationToken) =>
            {
                try
                {
                    // Assign System user if not Assigned
                    if (!await WabaAssignedUsers(wabaId, sleekflowCompanyId))
                    {
                        _logger.LogWarning("Unable to assign user with {WabaId}", wabaId);

                        return;
                    }

                    // Subscribe Webhook if not Subscribed
                    if (!await WabaSubscriptionsAsync(
                            wabaId,
                            businessIntegrationSystemUserAccessToken,
                            sleekflowCompanyId))
                    {
                        _logger.LogWarning(
                            "Unable to subscript waba {WabaId} with {BISUToken}",
                            wabaId,
                            businessIntegrationSystemUserAccessToken);

                        return;
                    }

                    var config = await WabaCloudApiConfig(wabaId, businessIntegrationSystemUserAccessToken);
                    if (!await WabaCreditLineAllocation(config.BusinessDetail.Id, wabaId, sleekflowCompanyId, CurrencyIsoCodes.USD))
                    {
                        _logger.LogWarning("Unable to credit line allocation {WabaId}", wabaId);

                        return;
                    }

                    var encryptedFacebookBusinessIntegrationSystemUserAccessToken =
                        GetEncryptedFacebookBusinessIntegrationSystemUserAccessToken(
                            businessIntegrationSystemUserAccessToken,
                            debugTokenResponse);

                    var createOrPatchWabaResult = await _commonRetryPolicyService.GetAsyncRetryPolicy()
                        .ExecuteAndCaptureAsync(
                            () => CreateOrPatchWabaAsync(
                                wabaId,
                                sleekflowCompanyId,
                                config,
                                encryptedFacebookBusinessIntegrationSystemUserAccessToken,
                                sleekflowStaff));

                    if (createOrPatchWabaResult.FinalException is not null || createOrPatchWabaResult.Result is null)
                    {
                        _logger.LogWarning(
                            "Unable to create or patch waba {WabaId}/{SleekflowCompanyId}/{BusinessIntegrationSystemUserAccessToken}/{SleekflowStaff}",
                            wabaId,
                            sleekflowCompanyId,
                            businessIntegrationSystemUserAccessToken,
                            JsonConvert.SerializeObject(sleekflowStaff));

                        return;
                    }

                    var waba = createOrPatchWabaResult.Result;

                    waba.WabaPhoneNumbers = waba.WabaPhoneNumbers
                        .Where(
                            p =>
                                (p.SleekflowCompanyId == null ||
                                 p.SleekflowCompanyId == sleekflowCompanyId) &&
                                p.RecordStatus == WabaPhoneNumberStatuses.Active)
                        .ToHashSet();

                    wabaConcurrentBag.Add(waba);
                }
                catch (Exception exception)
                {
                    _logger.LogError(
                        exception,
                        "Error occur during connect waba {WabaId}: {Exception}",
                        wabaId,
                        exception.Message);
                }
            });

        var wabas = wabaConcurrentBag.ToList();

        if (wabas.Count == 0)
        {
            throw new SfInternalErrorException("Unexpect exception occur during connect waba ");
        }

        var facebookBusinessIdToWabasDict =
            wabas
                .Where(w => w.FacebookBusinessId != null)
                .GroupBy(w => w.FacebookBusinessId!)
                .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var facebookBusinessIdToWabas in facebookBusinessIdToWabasDict)
        {
            var (facebookBusinessId, _) = facebookBusinessIdToWabas;
            await _bus.Publish(new OnCloudApiWabaBusinessConnectedEvent(facebookBusinessId, webhookUrl));
        }

        return wabas;
    }

    public async Task<List<Waba>> ConnectWabaAsync(
        string sleekflowCompanyId,
        string userAccessToken,
        List<string>? forbiddenWabaIds,
        string? webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {

        var scopes = new WabaDebugTokenGranularScopes(
            _logger,
            await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(userAccessToken));

        var longLivedAccessTokenResponse = await _whatsappCloudApiAuthenticationClient.GetLongLivedAccessTokenAsync(
            _secretConfig.FacebookAppId,
            _secretConfig.FacebookAppSecret,
            userAccessToken);

        _logger.LogInformation(
            "Got longLivedAccessTokenResponse {LongLivedAccessTokenResponse}",
            JsonConvert.SerializeObject(longLivedAccessTokenResponse, JsonConfig.DefaultJsonSerializerSettings));

        var wabaIds = forbiddenWabaIds == null
            ? scopes.WabaIds
            : scopes.WabaIds.Where(s => !forbiddenWabaIds.Contains(s)).ToList();

        var wabaConcurrentBag = new ConcurrentBag<Waba>();
        await Parallel.ForEachAsync(
            wabaIds,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            },
            async (wabaId, cancellationToken) =>
            {
                try
                {
                    // Assign System user if not Assigned
                    if (!await WabaAssignedUsers(wabaId, sleekflowCompanyId))
                    {
                        _logger.LogWarning("Unable to assign user with {WabaId}", wabaId);

                        return;
                    }

                    // Subscribe Webhook if not Subscribed
                    if (!await WabaSubscriptions(wabaId, sleekflowCompanyId))
                    {
                        _logger.LogWarning("Unable to subscript waba {WabaId}", wabaId);

                        return;
                    }

                    var config = await WabaCloudApiConfig(wabaId, businessIntegrationSystemUserAccessToken: null);
                    if (!await WabaCreditLineAllocation(config.BusinessDetail.Id, wabaId, sleekflowCompanyId, CurrencyIsoCodes.USD))
                    {
                        _logger.LogWarning("Unable to credit line allocation {WabaId}", wabaId);

                        return;
                    }

                    var createOrPatchWabaResult = await _commonRetryPolicyService.GetAsyncRetryPolicy()
                        .ExecuteAndCaptureAsync(
                            () => CreateOrPatchWabaAsync(
                                wabaId,
                                sleekflowCompanyId,
                                config,
                                longLivedAccessTokenResponse,
                                sleekflowStaff));

                    if (createOrPatchWabaResult.FinalException is not null || createOrPatchWabaResult.Result is null)
                    {
                        _logger.LogWarning(
                            "Unable to create or patch waba {WabaId}/{SleekflowCompanyId}/{UserAccessToken}/{SleekflowStaff}",
                            wabaId,
                            sleekflowCompanyId,
                            userAccessToken,
                            JsonConvert.SerializeObject(sleekflowStaff));

                        return;
                    }

                    var waba = createOrPatchWabaResult.Result;

                    waba.WabaPhoneNumbers = waba.WabaPhoneNumbers
                        .Where(
                            p =>
                                (p.SleekflowCompanyId == null ||
                                 p.SleekflowCompanyId == sleekflowCompanyId) &&
                                p.RecordStatus == WabaPhoneNumberStatuses.Active)
                        .ToHashSet();

                    wabaConcurrentBag.Add(waba);
                }
                catch (Exception exception)
                {
                    _logger.LogWarning(
                        exception,
                        "Error occur during connect waba {WabaId} / {Exception}",
                        wabaId,
                        exception.Message);
                }
            });

        var wabas = wabaConcurrentBag.ToList();

        if (wabas.Count == 0)
        {
            throw new SfInternalErrorException("Unexpect exception occur during connect waba ");
        }

        var facebookBusinessIdToWabasDict =
            wabas
                .Where(w => w.FacebookBusinessId != null)
                .GroupBy(w => w.FacebookBusinessId!)
                .ToDictionary(g => g.Key, g => g.ToList());

        foreach (var facebookBusinessIdToWabas in facebookBusinessIdToWabasDict)
        {
            var (facebookBusinessId, _) = facebookBusinessIdToWabas;
            await _bus.Publish(new OnCloudApiWabaBusinessConnectedEvent(facebookBusinessId, webhookUrl));
        }

        return wabas;
    }

    public async Task<Waba> GetAndRefreshWabaAsync(
        string wabaId,
        string sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? businessIntegrationSystemUserAccessToken = null,
        string? userAccessToken = null)
    {
        var refreshedWaba = await _commonRetryPolicyService.GetAsyncRetryPolicy().ExecuteAsync(
            async () =>
            {
                var waba = await GetWabaOrDefaultAsync(wabaId, sleekflowCompanyId);

                if (waba is null)
                {
                    throw new SfNotFoundObjectException($"Unable to locate waba information {wabaId}");
                }

                return await GetAndReconstructWabaAsync(
                    waba,
                    sleekflowCompanyId,
                    shouldRefresh,
                    sleekflowStaff,
                    businessIntegrationSystemUserAccessToken,
                    userAccessToken);
            });

        if (refreshedWaba is null)
        {
            throw new SfInternalErrorException($"Unable to get and refresh waba");
        }

        return refreshedWaba;
    }

    public async Task<Waba?> GetWabaOrDefaultAsync(string wabaId, string sleekflowCompanyId)
    {
        return (await _wabaRepository.GetObjectsAsync(
            w =>
                w.Id == wabaId &&
                w.SleekflowCompanyIds.Contains(sleekflowCompanyId))).FirstOrDefault();
    }

    public async Task<Waba> GetAndReconstructWabaAsync(
        Waba waba,
        string? sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? businessIntegrationSystemUserAccessToken = null,
        string? userAccessToken = null)
    {
        if (string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken))
        {
            var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
                GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

            if (hasEnabledFLFB && decryptedBusinessIntegrationSystemUserAccessTokenDto != null)
            {
                businessIntegrationSystemUserAccessToken =
                    decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken;
            }
        }

        var refreshedWaba = shouldRefresh
            ? await RefreshAndGetWaba(
                waba,
                sleekflowCompanyId,
                await WabaCloudApiConfig(waba.FacebookWabaId, businessIntegrationSystemUserAccessToken),
                sleekflowStaff,
                businessIntegrationSystemUserAccessToken,
                userAccessToken)
            : waba;

        if (refreshedWaba is null)
        {
            throw new SfInternalErrorException($"Unable to get or patch waba");
        }

        return refreshedWaba;
    }

    public async Task<Waba> GetWabaWithFacebookWabaIdAsync(string facebookWabaId)
    {
        return GetWabaOrThrow(await _wabaRepository.GetObjectsAsync(w => w.FacebookWabaId == facebookWabaId));
    }

    public async Task<Waba> GetWabaWithWabaPhoneNumberIdAsync(string sleekflowCompanyId, string wabaPhoneNumberId)
    {
        return GetWabaOrThrow(
            await _wabaRepository.GetObjectsAsync(
                w =>
                    w.WabaPhoneNumbers.Any(
                        p => p.SleekflowCompanyId == sleekflowCompanyId && p.Id == wabaPhoneNumberId)));
    }

    public async Task<Waba> GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(
        string wabaId,
        string sleekflowCompanyId,
        string wabaPhoneNumberId)
    {
        return GetWabaOrThrow(
            await _wabaRepository.GetObjectsAsync(
                w =>
                    w.Id == wabaId &&
                    w.WabaPhoneNumbers.Any(
                        p => p.SleekflowCompanyId == sleekflowCompanyId && p.Id == wabaPhoneNumberId)));
    }

    public async Task<Waba?> GetActiveWabaWithIdAndWabaPhoneNumberIdAsync(
        string wabaId,
        string wabaPhoneNumberId,
        string sleekflowCompanyId)
    {
        return GetWabaOrThrow(
            await _wabaRepository.GetObjectsAsync(
                w =>
                    w.Id == wabaId &&
                    w.SleekflowCompanyIds.Contains(sleekflowCompanyId) &&
                    w.WabaPhoneNumbers.Any(
                        p =>
                            p.Id == wabaPhoneNumberId && p.RecordStatus == WabaPhoneNumberStatuses.Active)));
    }

    public async Task<Waba?> GetWabaWithFacebookWabaIdAndFacebookPhoneNumberIdAsync(
        string facebookWabaId,
        string facebookPhoneNumberId)
    {
        return (await _wabaRepository.GetObjectsAsync(
                w =>
                    w.FacebookWabaId == facebookWabaId &&
                    w.WabaPhoneNumbers.Any(
                        p =>
                            p.FacebookPhoneNumberId == facebookPhoneNumberId &&
                            p.RecordStatus == WabaPhoneNumberStatuses.Active)))
            .FirstOrDefault();
    }

    public async Task<List<Waba>> GetAllAsync()
    {
        return await _wabaRepository.GetObjectsAsync(w => true);
    }

    public async Task<(List<Waba> Wabas, string? NextContinuationToken)> GetWabaAsync(
        string? continuationToken,
        int limit)
    {
        var (wabas, nextContinuationToken) =
            await _wabaRepository.GetContinuationTokenizedObjectsAsync(w => true, continuationToken, limit);

        return (wabas, nextContinuationToken);
    }

    public async Task<List<Waba>> GetWabasAsync(string sleekflowCompanyId)
    {
        return await _wabaRepository.GetObjectsAsync(w => w.SleekflowCompanyIds.Contains(sleekflowCompanyId));
    }

    public async Task<List<Waba>> GetConnectedWabaAsync(
        string sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        return await GetAndRefreshWabasAsync(sleekflowCompanyId, shouldRefresh, sleekflowStaff, null);
    }

    public async Task<List<Waba>> GetAndRefreshWabasAsync(
        string sleekflowCompanyId,
        bool shouldRefresh,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? businessIntegrationSystemUserAccessToken,
        string? userAccessToken = null)
    {
        var wabas = await GetWabasAsync(sleekflowCompanyId);

        var refreshedWabas = new List<Waba>();

        await Parallel.ForEachAsync(
            wabas,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 10
            },
            async (waba, cancellationToken) =>
            {
                var currentWaba = waba;

                var refreshedWaba = await _commonRetryPolicyService.GetAsyncRetryPolicy().ExecuteAsync(async () =>
                {
                    try
                    {
                        return await GetAndReconstructWabaAsync(
                            currentWaba,
                            sleekflowCompanyId,
                            shouldRefresh,
                            sleekflowStaff,
                            businessIntegrationSystemUserAccessToken,
                            userAccessToken);
                    }
                    catch (Exception exception)
                    {
                        _logger.LogError(exception, "GetAndRefreshWabasAsync {Exception}", exception.Message);
                        currentWaba = await GetWabaOrDefaultAsync(currentWaba.Id, sleekflowCompanyId);

                        if (currentWaba is null)
                        {
                            throw new SfNotFoundObjectException($"Unable to locate waba information {currentWaba}");
                        }

                        if (exception is GraphApiClientException)
                        {
                            return currentWaba;
                        }

                        throw;
                    }
                });

                if (refreshedWaba is null)
                {
                    throw new SfInternalErrorException($"Unable to patch or get wabas");
                }

                refreshedWabas.Add(refreshedWaba);
            });

        return refreshedWabas;
    }

    public async Task<List<Waba>> GetWabaWithFacebookBusinessIdAsync(string facebookBusinessId)
    {
        return await _wabaRepository.GetObjectsAsync(w => w.FacebookBusinessId == facebookBusinessId);
    }

    public async Task<List<Waba>?> GetConnectedWabaWithUserAccessTokenAsync(string userAccessToken)
    {
        var wabas = new List<Waba>();

        var scopes = new WabaDebugTokenGranularScopes(
            _logger,
            await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(userAccessToken));

        foreach (var wabaId in scopes.WabaIds)
        {
            var obtainedWabas = await _wabaRepository.GetObjectsAsync(w => w.FacebookWabaId == wabaId);
            wabas = wabas.Concat(obtainedWabas).ToList();
        }

        return wabas;
    }

    public async Task<(List<Waba>? Wabas, string BusinessIntegrationSystemUserAccessToken)> GetConnectedWabaWithBISUTokenFromFacebookAuthorizationCodeAsync(string facebookAuthorizationCode)
    {
        var businessIntegrationSystemUserAccessToken =
            await ExchangeAccessTokenFromFacebookAuthorizationCodeAsync(facebookAuthorizationCode);

        var debugTokenResponse =
            await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(businessIntegrationSystemUserAccessToken);

        var scopes = new WabaDebugTokenGranularScopes(
            _logger,
            debugTokenResponse);

        var wabas = new List<Waba>();

        foreach (var facebookWabaId in scopes.WabaIds.ToList())
        {
            var obtainedWabas = await _wabaRepository.GetObjectsAsync(w => w.FacebookWabaId == facebookWabaId);

            wabas = wabas.Concat(obtainedWabas).ToList();
        }

        return (wabas, businessIntegrationSystemUserAccessToken);
    }

    public async Task<int> UnblockOrBlockWabaAsync(string id, string facebookWabaId, bool isUnblock)
    {
        var waba = await _wabaRepository.GetOrDefaultAsync(id, facebookWabaId);

        if (waba is null)
        {
            throw new SfNotFoundObjectException("Unable to locate waba ");
        }

        var wabaSnapshot = JsonConvert.DeserializeObject<Waba>(JsonConvert.SerializeObject(waba));

        waba.MessagingFunctionLimitation = isUnblock ? null : MessagingFunctionLimitationType.BlockAll;
        waba.UpdatedAt = DateTimeOffset.UtcNow;

        await _auditLogService.AuditWabaAsync(
            wabaSnapshot,
            waba.FacebookWabaId,
            null,
            AuditingOperation.UnblockOrBlockWabaSnapshot,
            JsonConvertExtensions.ToDictionary(waba));

        return await _wabaRepository.UpsertAsync(waba, waba.FacebookWabaId, eTag: waba.ETag);
    }

    public async Task<bool> DeactivateWabaAsync(string facebookPhoneNumberId, string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken) ? new WhatsappCloudApiBspClient(
            businessIntegrationSystemUserAccessToken,
            _httpClient) : _whatsappCloudApiBspClient;

        return (await whatsappCloudApiBspClient.DeregisterPhoneNumberAsync(facebookPhoneNumberId)).Success;
    }

    public async Task<DebugTokenResponse> GetUserAccessTokenGranularScopes(string userAccessToken)
    {
        return await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(userAccessToken);
    }

    public (bool HasEnabledFLFB, DecryptedBusinessIntegrationSystemUserAccessTokenDto? DecryptedBusinessIntegrationSystemUserAccessTokenDto)
    GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(Waba waba)
    {
        if (waba.FacebookBusinessIntegrationSystemUserAccessTokens == null || !waba.FacebookBusinessIntegrationSystemUserAccessTokens.Any())
        {
            return (false, null);
        }

        var encryptedBusinessIntegrationSystemUserAccessToken = waba.FacebookBusinessIntegrationSystemUserAccessTokens
            .OrderByDescending(x => x.CreatedAt).ToList().Find(
                x => x.GranularScopes.Any(
                    y => y is { TargetIds: not null, Scope: "whatsapp_business_messaging" } &&
                         y.TargetIds.Contains(waba.FacebookWabaId) &&
                         x.GranularScopes.Any(
                             y => y is
                             {
                                 TargetIds: not null, Scope: "whatsapp_business_management"
                             }

                                  &&
                                  y.TargetIds.Contains(waba.FacebookWabaId))));

        var isWabaEnabledFLFB = encryptedBusinessIntegrationSystemUserAccessToken != null;
        DecryptedBusinessIntegrationSystemUserAccessTokenDto? decryptedBusinessIntegrationSystemUserAccessTokenDto = null;

        if (encryptedBusinessIntegrationSystemUserAccessToken != null)
        {
            decryptedBusinessIntegrationSystemUserAccessTokenDto =
                new DecryptedBusinessIntegrationSystemUserAccessTokenDto(
                    encryptedBusinessIntegrationSystemUserAccessToken,
                    _secretConfig.FacebookBusinessIntegrationSystemUserAccessTokenSecret);
        }

        return (isWabaEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto);
    }

    public async Task<string> ExchangeAccessTokenFromFacebookAuthorizationCodeAsync(string facebookAuthorizationCode)
    {
        var jsonSerializerSettings = JsonConfig.DefaultJsonSerializerSettings;

        try
        {
            var exchangeAccessTokenFromCodeResponse = await _whatsappCloudApiAuthenticationClient.ExchangeAccessTokenFromCodeAsync(
                _secretConfig.FacebookAppId,
                _secretConfig.FacebookAppSecret,
                facebookAuthorizationCode);

            _logger.LogInformation(
                "Exchange {Code} for {ExchangeAccessTokenFromCodeResponse}",
                facebookAuthorizationCode,
                JsonConvert.SerializeObject(exchangeAccessTokenFromCodeResponse, jsonSerializerSettings));

            return exchangeAccessTokenFromCodeResponse.AccessToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Caught error when exchanging facebook authorization code for business integration system user access token");

            if (ex is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(
                    gx.ErrorApiResponse?.Error?.Message ?? "unable to exchange facebook authorization code",
                    JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }

            throw new SfInternalErrorException(
                ex,
                "Caught error when exchanging for business integration system user access token");
        }
    }

    public FacebookBusinessIntegrationSystemUserAccessToken
        GetEncryptedFacebookBusinessIntegrationSystemUserAccessToken(
            string businessIntegrationSystemUserAccessToken,
            DebugTokenResponse debugTokenResponse)
    {
        var granularScopes = new List<GranularScope>();

        foreach (var granularScope in debugTokenResponse.Data.GranularScopes)
        {
            granularScopes.Add(new GranularScope(granularScope));
        }

        var facebookBusinessIntegrationSystemUserAccessToken =
            new FacebookBusinessIntegrationSystemUserAccessToken(
                AesUtils.AesEncryptBase64(
                    businessIntegrationSystemUserAccessToken,
                    _secretConfig.FacebookBusinessIntegrationSystemUserAccessTokenSecret),
                new List<string>(debugTokenResponse.Data.Scopes),
                granularScopes,
                debugTokenResponse.Data.AppId,
                debugTokenResponse.Data.Application,
                debugTokenResponse.Data.UserId,
                DateTimeOffset.UtcNow);

        return facebookBusinessIntegrationSystemUserAccessToken;
    }

    public async Task<bool> DisassociateFacebookBusinessAccountFromCompanyAsync(
        string sleekflowCompanyId,
        string facebookBusinessId)
    {
        // Checking phone numbers for knowing if the channel is in use or not

        var wabas = await _wabaRepository.GetObjectsAsync(
            w =>
                w.FacebookBusinessId == facebookBusinessId && w.SleekflowCompanyIds.Contains(sleekflowCompanyId));

        if (wabas.Count == 0)
        {
            return true;
        }

        foreach (var waba in wabas)
        {
            waba.SleekflowCompanyIds.Remove(sleekflowCompanyId);
            await _wabaRepository.UpsertAsync(waba, waba.FacebookWabaId, eTag: waba.ETag);
        }

        return true;
    }

    public async Task<List<Waba>> OnboardPartnersToMMLiteAsync(
        string sleekflowCompanyId,
        string facebookBusinessId,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        try
        {
            await _auditLogService.GetCloudApiAuditedResponse(
                () => _whatsappMMLiteApiClient.OnboardPartnersToMMLiteAsync(facebookBusinessId),
                new BaseAuditingObject(facebookBusinessId)
                {
                    SleekflowCompanyId = sleekflowCompanyId,
                    Parameters = new Dictionary<string, object?>
                    {
                        { "facebook_business_id", facebookBusinessId }
                    },
                    Operation = AuditingOperation.OnboardPartnersToMMLite
                });

            var wabas = await UpdateWabaMarketingMessagesLiteApiStatusAsync(facebookBusinessId, "PENDING");

            _logger.LogInformation(
                "Successfully initiated MM Lite onboarding for Facebook Business ID {FacebookBusinessId}, triggered by Company {SleekflowCompanyId}, Staff {SleekflowStaff}",
                facebookBusinessId,
                sleekflowCompanyId,
                JsonConvert.SerializeObject(sleekflowStaff));

            return wabas;
        }
        catch (GraphApiClientException gx)
        {
            _logger.LogError(gx, "Graph API error during MM Lite onboarding for Facebook Business ID {FacebookBusinessId}", facebookBusinessId);
            throw new SfGraphApiErrorException(
                gx.ErrorApiResponse?.Error?.ErrorUserMsg ?? gx.ErrorApiResponse?.Error?.Message ?? "Error occurred during MM Lite onboarding",
                JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
        }
    }

    private Waba AddFacebookBusinessIntegrationSystemUserAccessTokenToWaba(
        Waba waba,
        FacebookBusinessIntegrationSystemUserAccessToken encryptedFacebookBusinessIntegrationSystemUserAccessToken)
    {
        // limit encrypted business integration system user access token list to at most latest 5 records
        if (waba.FacebookBusinessIntegrationSystemUserAccessTokens != null &&
            waba.FacebookBusinessIntegrationSystemUserAccessTokens.Count >= 5)
        {
            var latestBusinessIntegrationSystemUserAccessTokens = waba
                .FacebookBusinessIntegrationSystemUserAccessTokens.OrderByDescending(x => x.CreatedAt).Take(4);

            waba.FacebookBusinessIntegrationSystemUserAccessTokens = latestBusinessIntegrationSystemUserAccessTokens.Prepend(encryptedFacebookBusinessIntegrationSystemUserAccessToken).ToList();
        }
        else
        {
            waba.FacebookBusinessIntegrationSystemUserAccessTokens =
                waba.FacebookBusinessIntegrationSystemUserAccessTokens != null
                    ? waba.FacebookBusinessIntegrationSystemUserAccessTokens
                        .Prepend(encryptedFacebookBusinessIntegrationSystemUserAccessToken).ToList()
                    : new List<FacebookBusinessIntegrationSystemUserAccessToken>()
                    {
                        encryptedFacebookBusinessIntegrationSystemUserAccessToken
                    };
        }

        return waba;
    }

    private async Task<bool> WabaAssignedUsers(string facebookWabaId, string sleekflowCompanyId)
    {
        return (await _auditLogService.GetCloudApiAuditedResponse(
            () => _whatsappCloudApiBspClient.AddWabaAssignedUser(
                facebookWabaId,
                _secretConfig.FacebookSystemUserId,
                WabaAssignedUserPermissionConst.MANAGE),
            new BaseAuditingObject(facebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "waba_id", facebookWabaId
                    },
                    {
                        "facebook_system_user_id", _secretConfig.FacebookSystemUserId
                    },
                    {
                        "waba_assigned_user_permission", WabaAssignedUserPermissionConst.MANAGE
                    }
                },
                Operation = AuditingOperation.ConnectWabaAddWabaAssignedUser
            })).Success;
    }

    private async Task<bool> WabaSubscriptions(string facebookWabaId, string sleekflowCompanyId)
    {
        var wabaSubscriptionOverrideCallbackUrl = !_whatsappCloudApiWebhookConfig.IsProduction
            ? new WabaSubscriptionOverrideCallbackUrl(
                _whatsappCloudApiWebhookConfig.WhatsappCloudApiOverrideWebhookUrl ??
                throw new SfInternalErrorException($"Unable to get WhatsappCloudApiOverrideWebhookUrl"),
                _secretConfig.FacebookWebhookVerificationToken)
            : null;
        return (await _auditLogService.GetCloudApiAuditedResponse(
            () => _whatsappCloudApiBspClient.CreateWabaSubscriptionsAsync(
                facebookWabaId,
                wabaSubscriptionOverrideCallbackUrl),
            new BaseAuditingObject(facebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "waba_id", facebookWabaId
                    }
                },
                Operation = AuditingOperation.ConnectWabaCreateWabaSubscription
            })).Success;
    }

    private async Task<bool> WabaSubscriptionsAsync(
        string facebookWabaId,
        string businessIntegrationSystemUserAccessToken,
        string sleekflowCompanyId)
    {
        var wabaSubscriptionOverrideCallbackUrl = !_whatsappCloudApiWebhookConfig.IsProduction
            ? new WabaSubscriptionOverrideCallbackUrl(
                _whatsappCloudApiWebhookConfig.WhatsappCloudApiOverrideWebhookUrl ??
                throw new SfInternalErrorException($"Unable to get WhatsappCloudApiOverrideWebhookUrl"),
                _secretConfig.FacebookWebhookVerificationToken)
            : null;

        return (await _auditLogService.GetCloudApiAuditedResponse(
            () => new WhatsappCloudApiBspClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient).CreateWabaSubscriptionsAsync(
                facebookWabaId,
                wabaSubscriptionOverrideCallbackUrl),
            new BaseAuditingObject(facebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "waba_id", facebookWabaId
                    }
                },
                Operation = AuditingOperation.ConnectWabaCreateWabaSubscription
            })).Success;
    }

    private async ValueTask<bool> WabaCreditLineAllocation(
        string facebookBusinessId,
        string facebookWabaId,
        string sleekflowCompanyId,
        string currency)
    {
        // Skip credit line allocation if facebook business id is our BSP business
        if (facebookBusinessId == _secretConfig.FacebookBusinessId)
        {
            return true;
        }

        return (await _auditLogService.GetCloudApiAuditedResponse(
            () => _whatsappCloudApiBspClient.AttachWabaCreditLine(
                _secretConfig.FacebookBusinessCreditLineId,
                facebookWabaId,
                currency),
            new BaseAuditingObject(facebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "facebook_business_credit_line_id", _secretConfig.FacebookBusinessCreditLineId
                    },
                    {
                        "facebook_waba_id", facebookWabaId
                    },
                    {
                        "currency", currency
                    }
                },
                Operation = AuditingOperation.AttachWabaCreditLine
            })).WabaId == facebookWabaId;
    }

    private async Task<WabaCloudApiConfig> WabaCloudApiConfig(string facebookWabaId, string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken) ? new WhatsappCloudApiBspClient(
            businessIntegrationSystemUserAccessToken,
            _httpClient) : _whatsappCloudApiBspClient;

        var whatsappBusinessAccount = await whatsappCloudApiBspClient.GetWhatsappBusinessAccount(facebookWabaId);

        var businessDetail =
            await whatsappCloudApiBspClient.GetBusinessDetail(whatsappBusinessAccount.OnBehalfOfBusinessInfo.Id);

        var phoneNumbersByWabaId = await whatsappCloudApiBspClient.GetPhoneNumbersByWabaId(
            facebookWabaId,
            paginationParam: new CursorBasedPaginationParam()
            {
                Limit = 500
            });

        return new WabaCloudApiConfig(whatsappBusinessAccount, businessDetail, phoneNumbersByWabaId);
    }

    private async Task<Waba> CreateOrPatchWabaAsync(
        string facebookWabaId,
        string sleekflowCompanyId,
        WabaCloudApiConfig config,
        FacebookBusinessIntegrationSystemUserAccessToken encryptedFacebookBusinessIntegrationSystemUserAccessToken,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var facebookBusinessId = config.WhatsappBusinessAccount.OnBehalfOfBusinessInfo.Id;

        var existingWaba = (await _wabaRepository.GetObjectsAsync(
            w =>
                w.FacebookWabaId == facebookWabaId &&
                w.FacebookBusinessId == facebookBusinessId)).FirstOrDefault();

        var waba = existingWaba ?? new Waba(
            _idService.GetId(SysTypeNames.Waba),
            facebookWabaId,
            facebookBusinessId,
            facebookBusinessId,
            new List<string>(),
            facebookWabaName: null,
            facebookWabaAccountReviewStatus: null,
            facebookWabaBusinessName: null,
            facebookWabaPrimaryFundingId: null,
            facebookWabaMessageTemplateNamespace: null,
            facebookWabaUserLongLivedAccessToken: null,
            facebookBusinessIntegrationSystemUserAccessTokens: null,
            facebookWabaBusinessDetailSnapshot: null,
            facebookWabaSnapshot: null,
            wabaPhoneNumbers: new HashSet<WabaPhoneNumber>(),
            recordStatus: WabaStatuses.Active,
            sleekflowStaff,
            sleekflowStaff,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow);

        waba = ComposeWabaWithLatestResponses(
            waba,
            config.WhatsappBusinessAccount,
            config.BusinessDetail,
            config.PhoneNumbersByWabaId,
            sleekflowStaff);

        waba = AddFacebookBusinessIntegrationSystemUserAccessTokenToWaba(
            waba,
            encryptedFacebookBusinessIntegrationSystemUserAccessToken);

        waba.SleekflowCompanyIds.Add(sleekflowCompanyId);
        waba.SleekflowCompanyIds = waba.SleekflowCompanyIds.Distinct().ToList();

        var audit = await _auditLogService.AuditWabaAsync(
            existingWaba,
            facebookWabaId,
            sleekflowCompanyId,
            AuditingOperation.WabaUpsertSnapshots,
            JsonConvertExtensions.ToDictionary(waba));

        // If waba object not already existed
        if (existingWaba == null)
        {
            return await _wabaRepository.CreateAndGetAsync(waba, facebookWabaId);
        }

        if (!await _wabaRepository.ReplaceWabaAsync(waba, existingWaba))
        {
            throw new SfInternalErrorException("Error occur during patch waba");
        }

        return await _wabaRepository.GetAsync(waba.Id, facebookWabaId);
    }

    private async Task<Waba> CreateOrPatchWabaAsync(
        string facebookWabaId,
        string sleekflowCompanyId,
        WabaCloudApiConfig config,
        LongLivedAccessTokenResponse liveAccessToken,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var facebookBusinessId = config.WhatsappBusinessAccount.OnBehalfOfBusinessInfo.Id;

        var existingWaba = (await _wabaRepository.GetObjectsAsync(
            w =>
                w.FacebookWabaId == facebookWabaId &&
                w.FacebookBusinessId == facebookBusinessId)).FirstOrDefault();

        var waba = existingWaba ?? new Waba(
            _idService.GetId(SysTypeNames.Waba),
            facebookWabaId,
            facebookBusinessId,
            facebookBusinessId,
            new List<string>(),
            facebookWabaName: null,
            facebookWabaAccountReviewStatus: null,
            facebookWabaBusinessName: null,
            facebookWabaPrimaryFundingId: null,
            facebookWabaMessageTemplateNamespace: null,
            facebookWabaUserLongLivedAccessToken: null,
            facebookBusinessIntegrationSystemUserAccessTokens: null,
            facebookWabaBusinessDetailSnapshot: null,
            facebookWabaSnapshot: null,
            wabaPhoneNumbers: new HashSet<WabaPhoneNumber>(),
            WabaStatuses.Active,
            sleekflowStaff,
            sleekflowStaff,
            createdAt: DateTimeOffset.UtcNow,
            updatedAt: DateTimeOffset.UtcNow);

        waba = ComposeWabaWithLatestResponses(
            waba,
            config.WhatsappBusinessAccount,
            config.BusinessDetail,
            config.PhoneNumbersByWabaId,
            sleekflowStaff);

        var encryption = AesUtils.AesEncryptBase64(
            liveAccessToken.AccessToken,
            _secretConfig.FacebookWabaUserLongAccessTokenSecret);

        waba.FacebookWabaUserLongLivedAccessToken =
            new WabaLongLivedAccessToken(
                encryption,
                liveAccessToken.TokenType,
                liveAccessToken.ExpiresIn == 0 ? null : DateTime.UtcNow.AddSeconds(liveAccessToken.ExpiresIn));
        waba.SleekflowCompanyIds.Add(sleekflowCompanyId);
        waba.SleekflowCompanyIds = waba.SleekflowCompanyIds.Distinct().ToList();

        var audit = await _auditLogService.AuditWabaAsync(
            existingWaba,
            facebookWabaId,
            sleekflowCompanyId,
            AuditingOperation.WabaUpsertSnapshots,
            JsonConvertExtensions.ToDictionary(waba));

        // If waba object not already existed
        if (existingWaba == null)
        {
            return await _wabaRepository.CreateAndGetAsync(waba, facebookWabaId);
        }

        if (!await _wabaRepository.ReplaceWabaAsync(waba, existingWaba))
        {
            throw new SfInternalErrorException("Error occur during patch waba");
        }

        return await _wabaRepository.GetAsync(waba.Id, facebookWabaId);
    }

    private async Task<(Waba Waba, DecryptedBusinessIntegrationSystemUserAccessTokenDto? DecryptedBusinessIntegrationSystemUserAccessTokenDto)>
        HandleWabaTokensAsync(
            Waba waba,
            string? businessIntegrationSystemUserAccessToken,
            string? userAccessToken)
    {
        var (hasEnabledFLFB, decrpytedBISUTokenDto) =
            GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        if (!hasEnabledFLFB && !string.IsNullOrEmpty(userAccessToken))
        {
            waba.FacebookWabaUserLongLivedAccessToken =
                await RefreshFacebookWabaUserLongLivedAccessTokenAsync(waba, userAccessToken);
        }
        else if (hasEnabledFLFB && !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken) && decrpytedBISUTokenDto != null && decrpytedBISUTokenDto.DecryptedToken != businessIntegrationSystemUserAccessToken)
        {
            var debugTokenResponse =
                await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(businessIntegrationSystemUserAccessToken);

            var encryptedFacebookBusinessIntegrationSystemUserAccessToken =
                GetEncryptedFacebookBusinessIntegrationSystemUserAccessToken(
                    businessIntegrationSystemUserAccessToken,
                    debugTokenResponse);
            waba = AddFacebookBusinessIntegrationSystemUserAccessTokenToWaba(
                waba,
                encryptedFacebookBusinessIntegrationSystemUserAccessToken);

            decrpytedBISUTokenDto =
                new DecryptedBusinessIntegrationSystemUserAccessTokenDto(
                    encryptedFacebookBusinessIntegrationSystemUserAccessToken,
                    _secretConfig.FacebookBusinessIntegrationSystemUserAccessTokenSecret);
        }

        return (waba, decrpytedBISUTokenDto);
    }

    private async Task<Waba> RefreshAndGetWaba(
        Waba waba,
        string? sleekflowCompanyId,
        WabaCloudApiConfig config,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? businessIntegrationSystemUserAccessToken,
        string? userAccessToken = null)
    {
        (waba, var decrpytedBISUTokenDto) = await HandleWabaTokensAsync(
            waba,
            businessIntegrationSystemUserAccessToken,
            userAccessToken);

        waba.WabaProductCatalog = await RefreshWabaProductCatalogAsync(waba, sleekflowStaff, sleekflowCompanyId, decrpytedBISUTokenDto);

        waba.WabaDataset =
            await _wabaAssetsManager.RefreshWabaDatasetAsync(waba, decrpytedBISUTokenDto?.DecryptedToken);

        var newWaba = ComposeWabaWithLatestResponses(
            waba,
            config.WhatsappBusinessAccount,
            config.BusinessDetail,
            config.PhoneNumbersByWabaId,
            sleekflowStaff);

        await _auditLogService.AuditWabaAsync(
            waba,
            waba.FacebookWabaId,
            sleekflowCompanyId,
            AuditingOperation.WabaRefreshSnapshots,
            JsonConvertExtensions.ToDictionary(newWaba));

        if (!await _wabaRepository.ReplaceWabaAsync(waba, newWaba))
        {
            throw new SfInternalErrorException($"Unable to refresh the Waba {waba.Id}");
        }

        return await _wabaRepository.GetAsync(waba.Id, waba.FacebookWabaId);
    }

    private async Task<WabaLongLivedAccessToken?> RefreshFacebookWabaUserLongLivedAccessTokenAsync(
        Waba waba,
        string userAccessToken)
    {
        try
        {
            // User manually refresh
            if (!string.IsNullOrEmpty(userAccessToken))
            {
                var refreshedUserAccessToken = await RefreshUserAccessTokenAsync(userAccessToken);

                var scopes = new WabaDebugTokenGranularScopes(
                    _logger,
                    await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(refreshedUserAccessToken.AccessToken));

                if (scopes.WabaIds.Contains(waba.FacebookWabaId))
                {
                    if (scopes.IsValid is true)
                    {
                        if (scopes.ExpiresAt is 0)
                        {
                            waba.FacebookWabaUserLongLivedAccessToken = new WabaLongLivedAccessToken(
                                AesUtils.AesEncryptBase64(
                                    refreshedUserAccessToken.AccessToken,
                                    _secretConfig.FacebookWabaUserLongAccessTokenSecret),
                                refreshedUserAccessToken.TokenType,
                                null);
                        }
                        else if (scopes.ExpiresAt > 0)
                        {
                            waba.FacebookWabaUserLongLivedAccessToken = new WabaLongLivedAccessToken(
                                AesUtils.AesEncryptBase64(
                                    refreshedUserAccessToken.AccessToken,
                                    _secretConfig.FacebookWabaUserLongAccessTokenSecret),
                                refreshedUserAccessToken.TokenType,
                                DateTime.UtcNow.AddSeconds(refreshedUserAccessToken.ExpiresIn));
                        }
                    }
                    else
                    {
                        waba.FacebookWabaUserLongLivedAccessToken = new WabaLongLivedAccessToken(
                            AesUtils.AesEncryptBase64(
                                refreshedUserAccessToken.AccessToken,
                                _secretConfig.FacebookWabaUserLongAccessTokenSecret),
                            refreshedUserAccessToken.TokenType,
                            null,
                            false);
                    }
                }
            }

            // System refresh without new user access token
            else
            {
                if (waba.FacebookWabaUserLongLivedAccessToken == null ||
                    waba.FacebookWabaUserLongLivedAccessToken.IsValid is false)
                {
                    return waba.FacebookWabaUserLongLivedAccessToken;
                }

                var scopes = new WabaDebugTokenGranularScopes(
                    _logger,
                    await _whatsappCloudApiAuthenticationClient.DebugTokenAsync(
                        AesUtils.AesEncryptBase64(
                            waba.FacebookWabaUserLongLivedAccessToken.EncryptedToken,
                            _secretConfig.FacebookWabaUserLongAccessTokenSecret)));

                if (scopes.IsValid is true)
                {
                    if (scopes.ExpiresAt is 0)
                    {
                        waba.FacebookWabaUserLongLivedAccessToken.ExpiryDateTime = null;
                        waba.FacebookWabaUserLongLivedAccessToken.IsValid = true;
                    }
                    else if (scopes.ExpiresAt > 0)
                    {
                        waba.FacebookWabaUserLongLivedAccessToken.ExpiryDateTime =
                            DateTimeOffset.UtcNow.AddSeconds(scopes.ExpiresAt.Value);
                        waba.FacebookWabaUserLongLivedAccessToken.IsValid = true;
                    }
                }
                else
                {
                    waba.FacebookWabaUserLongLivedAccessToken.IsValid = false;
                    waba.FacebookWabaUserLongLivedAccessToken.ExpiryDateTime = null;
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(
                "Unable to refresh waba {Waba} user access token with error {Exception}",
                JsonConvert.SerializeObject(waba),
                JsonConvert.SerializeObject(exception));

            if (waba.FacebookWabaUserLongLivedAccessToken != null)
            {
                waba.FacebookWabaUserLongLivedAccessToken.ExpiryDateTime = null;
                waba.FacebookWabaUserLongLivedAccessToken.IsValid = false;
            }
        }

        return waba.FacebookWabaUserLongLivedAccessToken;
    }

    private async Task<WabaProductCatalog?> RefreshWabaProductCatalogAsync(
        Waba waba,
        AuditEntity.SleekflowStaff? sleekflowStaff,
        string? sleekflowCompanyId,
        DecryptedBusinessIntegrationSystemUserAccessTokenDto? decryptedBusinessIntegrationSystemUserAccessTokenDto)
    {
        try
        {
            if (waba.WabaProductCatalog != null)
            {
                var facebookCommerceClient =
                    decryptedBusinessIntegrationSystemUserAccessTokenDto != null &&
                    decryptedBusinessIntegrationSystemUserAccessTokenDto.Scopes.Contains("catalog_management")
                        ? new FacebookCommerceClient(
                            decryptedBusinessIntegrationSystemUserAccessTokenDto.DecryptedToken,
                            _httpClient)
                        : _facebookCommerceClient;

                var getWabaProductCatalogsResponse =
                    await facebookCommerceClient.GetWabaProductCatalogs(waba.FacebookWabaId);

                if (!getWabaProductCatalogsResponse.Data.Any())
                {
                    if (waba.WabaProductCatalog != null)
                    {
                        waba.WabaProductCatalog.Status = "inactive";
                    }

                    return waba.WabaProductCatalog;
                }

                var wabaProductCatalog = getWabaProductCatalogsResponse.Data.Select(
                    x =>
                    {
                        if (waba.WabaProductCatalog.FacebookProductCatalogId != x.Id)
                        {
                            return new WabaProductCatalog(
                                _idService.GetId(SysTypeNames.WabaPhoneNumber),
                                x.Id,
                                x.Name,
                                DateTimeOffset.UtcNow,
                                DateTimeOffset.UtcNow,
                                sleekflowCompanyId,
                                x.DefaultImageUrl,
                                x.ProductCount,
                                x.Vertical,
                                "active",
                                sleekflowStaff,
                                sleekflowStaff);
                        }

                        var entity = waba.WabaProductCatalog;

                        // Upsert the latest information
                        entity.FacebookProductCatalogName = x.Name;
                        entity.DefaultImageUrl = x.DefaultImageUrl;
                        entity.ProductCount = x.ProductCount;
                        entity.Vertical = x.Vertical;
                        entity.Status = "active";
                        entity.SleekflowCompanyId = sleekflowCompanyId;
                        entity.UpdatedAt = DateTimeOffset.UtcNow;

                        if (sleekflowStaff is not null)
                        {
                            entity.UpdatedBy = sleekflowStaff;
                        }

                        return entity;
                    }).FirstOrDefault();

                waba.WabaProductCatalog = wabaProductCatalog;
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(
                "Unable to refresh waba {Waba} active product catalog with error {Exception}",
                JsonConvert.SerializeObject(waba),
                JsonConvert.SerializeObject(exception));
        }

        return waba.WabaProductCatalog;
    }

    private Waba ComposeWabaWithLatestResponses(
        Waba waba,
        GetWhatsappBusinessAccountResponse getWhatsappBusinessAccountResponse,
        GetBusinessDetailResponse getBusinessDetailResponse,
        GetPhoneNumbersByWabaIdResponse getPhoneNumbersByWabaIdResponse,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        // Update waba base information
        waba.FacebookWabaName = getWhatsappBusinessAccountResponse.Name;
        waba.FacebookWabaAccountReviewStatus = getWhatsappBusinessAccountResponse.AccountReviewStatus;
        waba.FacebookWabaBusinessName = getWhatsappBusinessAccountResponse.OnBehalfOfBusinessInfo.Name;
        waba.FacebookWabaPrimaryFundingId = getWhatsappBusinessAccountResponse.PrimaryFundingId;
        waba.FacebookWabaMessageTemplateNamespace = getWhatsappBusinessAccountResponse.MessageTemplateNamespace;

        waba.FacebookWabaSnapshot =
            JsonConvertExtensions.ToDictionary(getWhatsappBusinessAccountResponse);
        waba.FacebookWabaBusinessDetailSnapshot =
            JsonConvertExtensions.ToDictionary(getBusinessDetailResponse);

        waba.RecordStatus = WabaStatuses.Active;
        waba.UpdatedAt = DateTimeOffset.UtcNow;

        if (waba.MarketingMessagesLiteApiStatus != "PENDING" || getWhatsappBusinessAccountResponse.MarketingMessagesLiteApiStatus == "ONBOARDED")
        {
            // Pending status is a Status introduced by our system to indicate that the account is waiting for mm lite api approval
            // If the account is still in pending status, only update the status if the new status is onboarded
            _logger.LogInformation("MM Lite API status updated from {OldStatus} to {NewStatus}", waba.MarketingMessagesLiteApiStatus, getWhatsappBusinessAccountResponse.MarketingMessagesLiteApiStatus);
            waba.MarketingMessagesLiteApiStatus = getWhatsappBusinessAccountResponse.MarketingMessagesLiteApiStatus;
        }

        // Update phone number details
        var patchedPhoneNumbers = getPhoneNumbersByWabaIdResponse.Data
            .Select(
                p =>
                {
                    if (waba.WabaPhoneNumbers.All(w => w.FacebookPhoneNumberId != p.Id))
                    {
                        WhatsappCommerceSetting? whatsappCommerceSetting = null;
                        if (p.WhatsappCommerceSettings?.Data?.FirstOrDefault() != null)
                        {
                            var setting = p.WhatsappCommerceSettings.Data.FirstOrDefault()!;

                            whatsappCommerceSetting = new(
                                setting.Id,
                                setting.IsCatalogVisible,
                                setting.IsCartEnabled);
                        }

                        return new WabaPhoneNumber(
                            _idService.GetId(SysTypeNames.WabaPhoneNumber),
                            null,
                            p.Id,
                            WabaPhoneNumberStatuses.Active,
                            p,
                            whatsappCommerceSetting,
                            sleekflowStaff,
                            sleekflowStaff,
                            DateTimeOffset.UtcNow,
                            DateTimeOffset.UtcNow);
                    }

                    var entity = waba.WabaPhoneNumbers.First(n => n.FacebookPhoneNumberId == p.Id);

                    // Upsert the latest information
                    entity.FacebookPhoneNumberDetail = p;
                    entity.RecordStatus = WabaPhoneNumberStatuses.Active;
                    entity.UpdatedAt = DateTimeOffset.UtcNow;
                    entity.UpdatedBy = sleekflowStaff;

                    return entity;
                })
            .ToList();

        // In active any record is not existed in facebook phone number detail response
        var deactivatedWabaPhoneNumbers = new List<WabaPhoneNumber>();
        foreach (var wabaPhoneNumber in waba.WabaPhoneNumbers.Where(
                     phoneNumber =>
                         getPhoneNumbersByWabaIdResponse.Data.All(n => n.Id != phoneNumber.FacebookPhoneNumberId)))
        {
            wabaPhoneNumber.RecordStatus = WabaPhoneNumberStatuses.InActive;
            wabaPhoneNumber.UpdatedAt = DateTimeOffset.UtcNow;
            deactivatedWabaPhoneNumbers.Add(wabaPhoneNumber);
        }

        waba.WabaPhoneNumbers = patchedPhoneNumbers.Concat(deactivatedWabaPhoneNumbers).ToHashSet();

        return waba;
    }

    private Waba GetWabaOrThrow(List<Waba> wabas)
    {
        _logger.LogDebug("Extract first waba or throw {Wabas}", JsonConvert.SerializeObject(wabas));
        if (wabas.Count == 0)
        {
            throw new SfNotFoundObjectException("Unable to locate any waba");
        }

        return wabas.First();
    }

    private async Task<LongLivedAccessTokenResponse> RefreshUserAccessTokenAsync(string userAccessToken)
    {
        var longLivedAccessTokenResponse = await _whatsappCloudApiAuthenticationClient.GetLongLivedAccessTokenAsync(
            _secretConfig.FacebookAppId,
            _secretConfig.FacebookAppSecret,
            userAccessToken);

        return longLivedAccessTokenResponse;
    }

    private async Task<List<Waba>> UpdateWabaMarketingMessagesLiteApiStatusAsync(
        string facebookBusinessId,
        string marketingMessagesLiteApiStatus)
    {
        var wabas = await _wabaRepository.GetObjectsAsync(w => w.FacebookBusinessId == facebookBusinessId);

        if (wabas.Count == 0)
        {
            return new List<Waba>();
        }

        _logger.LogInformation("Updating MM Lite API status for Wabas {Wabas}", JsonConvert.SerializeObject(wabas.Select(w => w.FacebookWabaId).ToList()));

        await Task.WhenAll(wabas.Select(waba =>
            _wabaRepository.PatchWabaMarketingMessagesLiteApiStatusAsync(waba, marketingMessagesLiteApiStatus)));

        _logger.LogInformation("Updated MM Lite API status for wabas");

        return await _wabaRepository.GetObjectsAsync(w => w.FacebookBusinessId == facebookBusinessId);
    }
}