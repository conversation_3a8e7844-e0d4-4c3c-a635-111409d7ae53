﻿using Sleekflow.CrmHub.Models.UserMappingConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Salesforce.UserMappingConfigs;

public interface ISalesforceUserMappingConfigRepository : IRepository<SalesforceUserMappingConfig>
{
}

public class SalesforceUserMappingConfigRepository
    : BaseRepository<SalesforceUserMappingConfig>,
        ISalesforceUserMappingConfigRepository,
        ISingletonService
{
    public SalesforceUserMappingConfigRepository(
        ILogger<BaseRepository<SalesforceUserMappingConfig>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}