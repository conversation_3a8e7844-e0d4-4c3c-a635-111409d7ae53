using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas.ProductCatalogs;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

public interface IWabaRepository : IRepository<Waba>
{
    Task<bool> ReplaceWabaAsync(Waba waba, Waba newWaba);

    Task<bool> PatchWabaAsync(Waba waba, HashSet<WabaPhoneNumber> wabaPhoneNumbers);

    Task<bool> PatchWabaAsync(Waba waba, WabaProductCatalog wabaProductCatalog);

    Task<bool> PatchWabaAsync(Waba waba, HashSet<WabaPhoneNumber> wabaPhoneNumbers, WabaProductCatalog wabaProductCatalog);

    Task<bool> PatchWabaMarketingMessagesLiteApiStatusAsync(Waba waba, string marketingMessagesLiteApiStatus);
}

public class WabaRepository : BaseRepository<Waba>, IWabaRepository, ISingletonService
{
    private readonly ILogger<WabaRepository> _logger;

    public WabaRepository(
        ILogger<WabaRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
        _logger = logger;
    }

    public async Task<bool> ReplaceWabaAsync(Waba waba, Waba newWaba)
    {
        return await ReplaceAsync(waba.Id, waba.FacebookWabaId, newWaba, eTag: waba.ETag) == 1;
    }

    public async Task<bool> PatchWabaAsync(Waba waba, HashSet<WabaPhoneNumber> wabaPhoneNumbers)
    {
        return await PatchAsync(
            waba.Id,
            waba.FacebookWabaId,
            new List<PatchOperation>
            {
                Replace(Waba.PropertyNameWabaPhoneNumbers, wabaPhoneNumbers),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow),
            },
            eTag: waba.ETag) == 1;
    }

    public async Task<bool> PatchWabaAsync(Waba waba, WabaProductCatalog wabaProductCatalog)
    {
        return await PatchAsync(
            waba.Id,
            waba.FacebookWabaId,
            new List<PatchOperation>()
            {
                Replace(Waba.PropertyNameWabaProductCatalog, wabaProductCatalog),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
            },
            eTag: waba.ETag) == 1;
    }

    public async Task<bool> PatchWabaMarketingMessagesLiteApiStatusAsync(Waba waba, string marketingMessagesLiteApiStatus)
    {
        return await PatchAsync(
            waba.Id,
            waba.FacebookWabaId,
            new List<PatchOperation>()
            {
                Set(Waba.PropertyNameMarketingMessagesLiteApiStatus, marketingMessagesLiteApiStatus),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
            },
            eTag: waba.ETag) == 1;
    }

    public async Task<bool> PatchWabaAsync(
        Waba waba,
        HashSet<WabaPhoneNumber> wabaPhoneNumbers,
        WabaProductCatalog wabaProductCatalog)
    {
        return await PatchAsync(
            waba.Id,
            waba.FacebookWabaId,
            new List<PatchOperation>
            {
                Replace(Waba.PropertyNameWabaPhoneNumbers, wabaPhoneNumbers),
                Replace(Waba.PropertyNameWabaProductCatalog, wabaProductCatalog),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow),
            },
            eTag: waba.ETag) == 1;
    }
}