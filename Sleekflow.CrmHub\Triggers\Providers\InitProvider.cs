﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class InitProvider : ITrigger
{
    private readonly IProviderConfigService _providerConfigService;
    private readonly IProviderSelector _providerSelector;

    public InitProvider(
        IProviderConfigService providerConfigService,
        IProviderSelector providerSelector)
    {
        _providerConfigService = providerConfigService;
        _providerSelector = providerSelector;
    }

    public class InitProviderInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonProperty("return_to_url")]
        [Required]
        public string ReturnToUrl { get; set; }

        [JsonProperty("default_region_code")]
        [Required]
        public string DefaultRegionCode { get; set; }

        [JsonProperty("additional_details")]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public InitProviderInput(
            string sleekflowCompanyId,
            string providerName,
            string returnToUrl,
            string defaultRegionCode,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            ReturnToUrl = returnToUrl;
            DefaultRegionCode = defaultRegionCode;
            AdditionalDetails = additionalDetails;
        }
    }

    public class InitProviderOutput
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("context")]
        public dynamic Context { get; set; }

        [JsonConstructor]
        public InitProviderOutput(string providerName, object context)
        {
            ProviderName = providerName;
            Context = context;
        }
    }

    public async Task<InitProviderOutput> F(
        InitProviderInput initProviderInput)
    {
        var providerService = _providerSelector.GetProviderService(initProviderInput.ProviderName);

        await _providerConfigService.InitProviderConfigAsync(
            initProviderInput.SleekflowCompanyId,
            initProviderInput.ProviderName,
            initProviderInput.DefaultRegionCode,
            initProviderInput.AdditionalDetails,
            null);

        var initProviderOutput = await providerService.InitProviderAsync(
            initProviderInput.SleekflowCompanyId,
            initProviderInput.ReturnToUrl,
            initProviderInput.AdditionalDetails);

        return new InitProviderOutput(initProviderOutput.ProviderName, initProviderOutput.Context);
    }
}