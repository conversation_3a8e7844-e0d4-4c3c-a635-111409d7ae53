﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.Migrations;

[TriggerGroup(TriggerGroups.Migrations)]
public class PrepareMigrationToFlowHub : ITrigger
{
    private static readonly List<string> EntityTypesToMigrate = new()
    {
        "Contact",
        "Lead",
        "Opportunity",
        "Campaign"
    };

    private readonly IProviderSelector _providerSelector;

    public PrepareMigrationToFlowHub(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class PrepareMigrationToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("provider_name")]
        [Required]
        public string ProviderName { get; set; }

        [JsonConstructor]
        public PrepareMigrationToFlowHubInput(
            string sleekflowCompanyId,
            string providerName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
        }
    }

    public class PrepareMigrationToFlowHubOutput
    {
    }

    public async Task<PrepareMigrationToFlowHubOutput> F(
        PrepareMigrationToFlowHubInput prepareMigrationToFlowHubInput)
    {
        var sleekflowCompanyId = prepareMigrationToFlowHubInput.SleekflowCompanyId;
        var providerName = prepareMigrationToFlowHubInput.ProviderName;

        var providerService = _providerSelector.GetProviderService(providerName);

        await providerService.PrepareMigrationToFlowHubAsync(
            sleekflowCompanyId,
            EntityTypesToMigrate);

        return new PrepareMigrationToFlowHubOutput();
    }
}