﻿using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowExecutionEndedV2Event : IHighTrafficEvent
{
    public string SleekflowCompanyId { get; set; }

    public string StateId { get; set; }

    public string WorkflowExecutionStatus { get; set; }

    public string? WorkflowExecutionReasonCode { get; set; }

    public StateIdentity StateIdentity { get; set; }

    public string? WorkflowType { get; set; }

    public AuditEntity.SleekflowStaff? EndedBy { get; set; }

    public DateTimeOffset EndedAt { get; set; } = DateTimeOffset.UtcNow;

    public string SubWorkflowType { get; set; }

    public OnWorkflowExecutionEndedV2Event(
        string sleekflowCompanyId,
        string stateId,
        string workflowExecutionStatus,
        string? workflowExecutionReasonCode,
        StateIdentity stateIdentity,
        string? workflowType,
        AuditEntity.SleekflowStaff? endedBy,
        string subWorkflowType)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        WorkflowExecutionStatus = workflowExecutionStatus;
        WorkflowExecutionReasonCode = workflowExecutionReasonCode;
        StateIdentity = stateIdentity;
        WorkflowType = workflowType;
        EndedBy = endedBy;
        SubWorkflowType = subWorkflowType;
    }
}