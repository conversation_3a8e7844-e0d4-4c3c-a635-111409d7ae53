using MailKit;
using MailKit.Security;
using MimeKit;
using Serilog;

namespace Sleekflow.EmailHub.OnPremise.Clients;

public class OnPremiseSendEmailClient : OnPremiseClient
{
    private readonly ILogger<OnPremiseSendEmailClient> _logger;
    private readonly MimeMessage _email;

    public OnPremiseSendEmailClient(
        MimeMessage email,
        string serverType,
        string hostName,
        string username,
        string password,
        int portNumber,
        SecureSocketOptions sslOptions,
        FetchRequest? fetchRequest = null)
        : base(serverType, hostName, username, password, portNumber, fetchRequest!, sslOptions)
    {
        _email = email;
        _logger = new LoggerFactory().AddSerilog().CreateLogger<OnPremiseSendEmailClient>();
    }

    public override async Task RunAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            await ReconnectAsync();
            await SendEmailAsync(_email);
        }
        catch (OperationCanceledException)
        {
            // ignored
        }

        _logger.LogInformation(
            "[OnPremiseSendEmailClient]: Finish RunAsync," +
            "serverName: {hostName}",
            HostName);
    }
}