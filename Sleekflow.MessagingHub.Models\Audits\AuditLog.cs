using GraphApi.Client.ApiClients.Models;
using GraphApi.Client.Models.WebhookObjects;
using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.MessagingHubDb;
using Sleekflow.Utils;

namespace Sleekflow.MessagingHub.Models.Audits;

[DatabaseId(ContainerNames.DatabaseId)]
[ContainerId(ContainerNames.AuditLog)]
[Resolver(typeof(IMessagingHubDbResolver))]
public class AuditLog : Entity, IHasCreatedAt
{
    [JsonProperty("auditing_partition_id")]
    public string AuditingPartitionId { get; set; }

    [JsonProperty("facebook_waba_id")]
    public string? FacebookWabaId { get; set; }

    // TODO Need to update the property name to facebook business id when doing the data migration
    [JsonProperty("facebook_waba_business_id")]
    public string? FacebookBusinessId { get; set; }

    [JsonProperty("facebook_phone_number_id")]
    public string? FacebookPhoneNumberId { get; set; }

    [JsonProperty("facebook_message_template_id")]
    public string? FacebookMessageTemplateId { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string? SleekflowCompanyId { get; set; }

    // TODO Rename
    [JsonProperty("cloud_api_audit")]
    public CloudApiRequestAudit? CloudApiAudits { get; set; }

    [JsonProperty("waba_audit")]
    public WabaAudit? WabaSnapshots { get; set; }

    [JsonProperty("business_balance_snapshots")]
    public BusinessBalanceAudit? BusinessBalanceSnapshots { get; set; }

    [JsonProperty("business_balance_transaction_log_snapshots")]
    public BusinessBalanceTransactionLogAudit? BusinessBalanceTransactionLogSnapshots { get; set; }

    [JsonProperty("cloud_api_webhook_status_update_snapshots")]
    public CloudApiWebhookStatusUpdateAudit? CloudApiWebhookStatusUpdateSnapshots { get; set; }

    [JsonProperty("auditing_operation")]
    public string AuditingOperation { get; set; }

    [JsonProperty("auditing_group")]
    public string AuditingGroup { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty("sleekflow_company_name")]
    public string? SleekflowCompanyName { get; set; }

    [JsonConstructor]
    public AuditLog(
        string id,
        string sysTypeName,
        int? ttl,
        string auditingPartitionId,
        string? facebookWabaId,
        string? facebookBusinessId,
        string? facebookPhoneNumberId,
        string? facebookMessageTemplateId,
        string? sleekflowCompanyId,
        CloudApiRequestAudit? cloudApiAudits,
        WabaAudit? wabaSnapshots,
        BusinessBalanceAudit? businessBalanceSnapshots,
        BusinessBalanceTransactionLogAudit? businessBalanceTransactionLogSnapshots,
        CloudApiWebhookStatusUpdateAudit? cloudApiWebhookStatusUpdateSnapshots,
        string auditingOperation,
        string auditingGroup,
        DateTimeOffset createdAt,
        string? sleekflowCompanyName = null)
        : base(id, sysTypeName, ttl)
    {
        AuditingPartitionId = auditingPartitionId;
        FacebookWabaId = facebookWabaId;
        FacebookBusinessId = facebookBusinessId;
        FacebookPhoneNumberId = facebookPhoneNumberId;
        FacebookMessageTemplateId = facebookMessageTemplateId;
        SleekflowCompanyId = sleekflowCompanyId;
        CloudApiAudits = cloudApiAudits;
        WabaSnapshots = wabaSnapshots;
        BusinessBalanceSnapshots = businessBalanceSnapshots;
        BusinessBalanceTransactionLogSnapshots = businessBalanceTransactionLogSnapshots;
        CloudApiWebhookStatusUpdateSnapshots = cloudApiWebhookStatusUpdateSnapshots;
        AuditingOperation = auditingOperation;
        AuditingGroup = auditingGroup;
        CreatedAt = createdAt;
        SleekflowCompanyName = sleekflowCompanyName;
    }

    public class CloudApiRequestAudit
    {
        public const string RequestPath = "cloud_api_audit/request";

        public const string ResponsePath = "cloud_api_audit/response";

        public const string ExceptionPath = "cloud_api_audit/exception";

        public const string ResponseAtPath = "cloud_api_audit/response_at";

        [JsonProperty("parameters")]
        public Dictionary<string, object?>? Parameters { get; set; }

        [JsonProperty("request")]
        public Dictionary<string, object?>? Request { get; set; }

        [JsonProperty("response")]
        public Dictionary<string, object?>? Response { get; set; }

        [JsonProperty("exception")]
        public Dictionary<string, object?>? Exception { get; set; }

        [JsonProperty("request_at")]
        public DateTimeOffset? RequestAt { get; set; }

        [JsonProperty("response_at")]
        public DateTimeOffset? ResponseAt { get; set; }

        [JsonConstructor]
        public CloudApiRequestAudit(
            Dictionary<string, object?>? parameters,
            Dictionary<string, object?>? request,
            Dictionary<string, object?>? response,
            Dictionary<string, object?>? exception,
            DateTimeOffset? requestAt,
            DateTimeOffset? responseAt)
        {
            Parameters = parameters;
            Request = request;
            Response = response;
            Exception = exception;
            RequestAt = requestAt;
            ResponseAt = responseAt;
        }

        public CloudApiRequestAudit(
            Dictionary<string, object?>? parameters,
            DateTimeOffset? requestAt)
        {
            Parameters = parameters;
            RequestAt = requestAt;
        }
    }

    public class WabaAudit
    {
        [JsonProperty("snapshot")]
        public Waba? Snapshot { get; set; }

        [JsonProperty("changes")]
        public Dictionary<string, object?>? Changes { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonConstructor]
        public WabaAudit(
            Waba? snapshot,
            Dictionary<string, object?>? changes,
            string type)
        {
            Snapshot = snapshot;
            Changes = changes;
            Type = type;
        }
    }

    public class BusinessBalanceAudit
    {
        [JsonProperty("snapshot")]
        public BusinessBalance? Snapshot { get; set; }

        [JsonProperty("changes")]
        public Dictionary<string, object?>? Changes { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonConstructor]
        public BusinessBalanceAudit(
            BusinessBalance? snapshot,
            Dictionary<string, object?>? changes,
            string type)
        {
            Snapshot = snapshot;
            Changes = changes;
            Type = type;
        }
    }

    public class BusinessBalanceTransactionLogAudit
    {
        [JsonProperty("snapshot")]
        public BusinessBalanceTransactionLog? Snapshot { get; set; }

        [JsonProperty("changes")]
        public Dictionary<string, object?>? Changes { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonConstructor]
        public BusinessBalanceTransactionLogAudit(
            BusinessBalanceTransactionLog? snapshot,
            Dictionary<string, object?>? changes,
            string type)
        {
            Snapshot = snapshot;
            Changes = changes;
            Type = type;
        }
    }

    public class CloudApiWebhookStatusUpdateAudit
    {
        [JsonProperty("snapshot")]
        public AuditLogCloudApiWebhookValueObject? Snapshot { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonConstructor]
        public CloudApiWebhookStatusUpdateAudit(
            AuditLogCloudApiWebhookValueObject? snapshot,
            string type)
        {
            Snapshot = snapshot;
            Type = type;
        }
    }

    public class AuditingRequest : JsonDictionaryDeserialize
    {
        [JsonProperty("http_method")]
        public string? HttpMethod { get; set; }

        [JsonProperty("request_path")]
        public string? RequestPath { get; set; }

        [JsonProperty("request_body")]
        public string? RequestBody { get; set; }

        [JsonProperty("http_status_code")]
        public int? HttpStatusCode { get; set; }

        [JsonConstructor]
        public AuditingRequest(
            HttpPayload? payload)
        {
            HttpMethod = payload?.HttpMethod;
            RequestPath = payload?.RequestPath;
            RequestBody = payload?.RequestBody;
            HttpStatusCode = payload?.HttpStatusCode;
        }
    }

    public class AuditingResponse : JsonDictionaryDeserialize
    {
        [JsonProperty("response_body")]
        public string? ResponseBody { get; set; }

        [JsonConstructor]
        public AuditingResponse(
            HttpPayload? payload)
        {
            ResponseBody = payload?.ResponseBody;
        }
    }

    public class AuditingError : JsonDictionaryDeserialize
    {
        [JsonProperty("exception")]
        public Exception? Exception { get; set; }

        [JsonConstructor]
        public AuditingError(
            Exception exception)
        {
            Exception = exception;
        }
    }
}