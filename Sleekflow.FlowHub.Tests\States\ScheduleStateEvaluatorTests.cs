using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.States;

namespace Sleekflow.FlowHub.Tests.States;

public class ScheduleStateEvaluatorTests
{
    private StateEvaluator _stateEvaluator;
    private const string WorkflowName = "workflow_name";

    [SetUp]
    public void Setup()
    {
        _stateEvaluator = new StateEvaluator();
    }

    [Test]
    public async Task EvaluateExpressionAsync_ReturnFalse_V1()
    {
        const string jsonString = """
                                   {
                                       "contact": {
                                           "id": "39eb2241-cef2-4892-bd1d-30ce3adb631e",
                                           "company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                           "first_name": "<PERSON><PERSON><PERSON>",
                                           "last_name": "<PERSON><PERSON>",
                                           "picture_url": null,
                                           "created_at": "2025-01-03T10:43:06.99Z",
                                           "updated_at": "2025-04-15T03:50:02.5620872Z",
                                           "last_contact": "2025-03-20T07:18:23.5349707Z",
                                           "last_contact_from_customers": null,
                                           "is_sandbox": false,
                                           "is_shopify_profile": false,
                                           "description": null,
                                           "status": null,
                                           "labels": null,
                                           "PhoneNumber": "85269369558",
                                           "Subscriber": "true",
                                           "Title (e.g. Mr / Mrs)": "Animi suscipit quidem ut suscipit optio et libero provident ducimus provident.",
                                           "food": "test loop through with cluster Winnifred",
                                           "LastContact": "2025-03-20T07:18:23.5349707Z",
                                           "LastChannel": "whatsappcloudapi",
                                           "DB_Custom_Field1": "field 1",
                                           "DB_Custom_Field2": "custom field 2",
                                           "conversation_id": "bcb35e05-e5f0-4e78-a45a-8442b0f3bd76"
                                       },
                                       "contact_owner": {},
                                       "lists": [
                                           {
                                               "id": "139745",
                                               "name": "Nick 3",
                                               "added_at": "2025-04-09T03:49:33.0912606+08:00",
                                               "is_imported": false
                                           }
                                       ],
                                       "conversation": {
                                           "conversation_status": "closed",
                                           "conversation_id": "bcb35e05-e5f0-4e78-a45a-8442b0f3bd76",
                                           "last_message_channel": "whatsappcloudapi",
                                           "last_message_channel_id": "18454069890"
                                       }
                                   }
                                   """;
        var contactDetail = JsonConvert.DeserializeObject<ContactDetail>(jsonString);

        Assert.That(contactDetail, Is.Not.Null);
        var result = await _stateEvaluator.EvaluateExpressionAsync(
            WorkflowName,
            contactDetail,
            "{{ ((usr_var_dict.contact.labels | array.some @(do; ret $0.LabelValue == \"w\"; end))) && (usr_var_dict.conversation.conversation_status == \"open\") && ((usr_var_dict.lists | array.some @(do; ret $0.id == \"6505\"; end))) && (((usr_var_dict.contact[\"first_name\"] | string.downcase) | string.contains \"johnson\")) }}");

        Assert.That(result, Is.EqualTo(false));
    }

    [Test]
    public async Task EvaluateExpressionAsync_ReturnTrue_V1()
    {
        const string jsonString = """
                                   {
                                       "contact": {
                                           "id": "39eb2241-cef2-4892-bd1d-30ce3adb631e",
                                           "company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                                           "first_name": "johnson",
                                           "last_name": "Hartmann",
                                           "picture_url": null,
                                           "created_at": "2025-01-03T10:43:06.99Z",
                                           "updated_at": "2025-04-15T03:50:02.5620872Z",
                                           "last_contact": "2025-03-20T07:18:23.5349707Z",
                                           "last_contact_from_customers": null,
                                           "is_sandbox": false,
                                           "is_shopify_profile": false,
                                           "description": null,
                                           "status": null,
                                           "labels": [
                                               {
                                                   "LabelValue": "w",
                                                   "LabelColor": "Blue",
                                                   "LabelType": "Normal"
                                               }
                                           ],
                                           "PhoneNumber": "85269369558",
                                           "Subscriber": "true",
                                           "Title (e.g. Mr / Mrs)": "Animi suscipit quidem ut suscipit optio et libero provident ducimus provident.",
                                           "food": "test loop through with cluster Winnifred",
                                           "LastContact": "2025-03-20T07:18:23.5349707Z",
                                           "LastChannel": "whatsappcloudapi",
                                           "DB_Custom_Field1": "field 1",
                                           "DB_Custom_Field2": "custom field 2",
                                           "conversation_id": "bcb35e05-e5f0-4e78-a45a-8442b0f3bd76"
                                       },
                                       "contact_owner": {},
                                       "lists": [
                                           {
                                               "id": "139745",
                                               "name": "Nick 3",
                                               "added_at": "2025-04-09T03:49:33.0912606+08:00",
                                               "is_imported": false
                                           }
                                       ],
                                       "conversation": {
                                           "conversation_status": "open",
                                           "conversation_id": "bcb35e05-e5f0-4e78-a45a-8442b0f3bd76",
                                           "last_message_channel": "whatsappcloudapi",
                                           "last_message_channel_id": "18454069890"
                                       }
                                   }
                                   """;
        var contactDetail = JsonConvert.DeserializeObject<ContactDetail>(jsonString);

        Assert.That(contactDetail, Is.Not.Null);
        var result = await _stateEvaluator.EvaluateExpressionAsync(
            WorkflowName,
            contactDetail,
            "{{ ((usr_var_dict.contact.labels | array.some @(do; ret $0.LabelValue == \"w\"; end))) && (usr_var_dict.conversation.conversation_status == \"open\") && ((usr_var_dict.lists | array.some @(do; ret $0.id == \"139745\"; end))) && (((usr_var_dict.contact[\"first_name\"] | string.downcase) | string.contains \"johnson\")) }}");

        Assert.That(result, Is.EqualTo(true));
    }
}