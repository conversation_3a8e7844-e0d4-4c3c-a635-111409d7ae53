using Newtonsoft.Json;

namespace Sleekflow.Models.WorkflowSteps;

public class GetAgentConfigRequest
{
    [JsonProperty("company_agent_config_id")]
    public string CompanyAgentConfigId { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonConstructor]
    public GetAgentConfigRequest(string companyAgentConfigId, string sleekflowCompanyId)
    {
        CompanyAgentConfigId = companyAgentConfigId;
        SleekflowCompanyId = sleekflowCompanyId;
    }
}