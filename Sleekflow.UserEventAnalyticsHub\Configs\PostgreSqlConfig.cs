using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.UserEventAnalyticsHub.Configs;

public class PostgreSqlConfig : IConfig, IPostgreSqlConfig
{
    public string ConnectionString { get; }
    public string ServerName { get; }
    public string DatabaseName { get; }

    public PostgreSqlConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        ConnectionString = Environment.GetEnvironmentVariable("POSTGRES_CONNECTION_STRING", target) ??
                          throw new SfMissingEnvironmentVariableException("POSTGRES_CONNECTION_STRING");

        ServerName = Environment.GetEnvironmentVariable("POSTGRES_SERVER_NAME", target) ??
                    throw new SfMissingEnvironmentVariableException("POSTGRES_SERVER_NAME");

        DatabaseName = Environment.GetEnvironmentVariable("POSTGRES_DATABASE_NAME", target) ??
                      throw new SfMissingEnvironmentVariableException("POSTGRES_DATABASE_NAME");
    }
}