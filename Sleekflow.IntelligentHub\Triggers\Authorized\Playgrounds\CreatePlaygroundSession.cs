﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.IntelligentHub.Playgrounds;
using Sleekflow.Models.Prompts;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.Playgrounds;

[TriggerGroup(
    ControllerNames.Playgrounds,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class CreatePlaygroundSession
    : ITrigger<CreatePlaygroundSession.CreatePlaygroundSessionInput,
        CreatePlaygroundSession.CreatePlaygroundSessionOutput>
{
    private readonly IPlaygroundService _playgroundService;
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IIdService _idService;

    public CreatePlaygroundSession(
        IPlaygroundService playgroundService,
        ISleekflowAuthorizationContext authorizationContext,
        IIdService idService)
    {
        _playgroundService = playgroundService;
        _authorizationContext = authorizationContext;
        _idService = idService;
    }

    public class CreatePlaygroundSessionInput
    {
        [Required]
        [JsonProperty("bot_name")]
        public string BotName { get; set; }

        [Required]
        [JsonProperty("prompt_instruction")]
        [Validations.ValidateObject]
        public PromptInstructionDto PromptInstruction { get; set; }

        [JsonProperty("collaboration_mode")]
        public string? CollaborationMode { get; set; }

        [JsonProperty("agent_config")]
        [Validations.ValidateObject]
        public CompanyAgentConfigDto? AgentConfig { get; set; }

        [JsonConstructor]
        public CreatePlaygroundSessionInput(
            string botName,
            PromptInstructionDto promptInstruction,
            string? collaborationMode = null,
            CompanyAgentConfigDto? agentConfig = null)
        {
            BotName = botName;
            PromptInstruction = promptInstruction;
            CollaborationMode = collaborationMode;
            AgentConfig = agentConfig;
        }
    }

    public class CreatePlaygroundSessionOutput
    {
        [JsonProperty("session_id")]
        public string SessionId { get; set; }

        [JsonConstructor]
        public CreatePlaygroundSessionOutput(string sessionId)
        {
            SessionId = sessionId;
        }
    }

    public async Task<CreatePlaygroundSessionOutput> F(CreatePlaygroundSessionInput input)
    {
        var playgroundId = _idService.GetId(SysTypeNames.Playground);

        var agentConfigDto = input.AgentConfig ?? new CompanyAgentConfigDto(
            "Playground",
            "Playground AI",
            _authorizationContext.SleekflowCompanyId!,
            true,
            true,
            4096,
            null,
            null,
            null,
            new PromptInstructionDto(new PromptInstruction()),
            input.CollaborationMode ?? AgentCollaborationModes.Default,
            null,
            CompanyAgentTypes.Sales,
            DateTimeOffset.Now,
            null,
            DateTimeOffset.UtcNow,
            null,
            eTag: null);

        var playground = await _playgroundService.CreateAsync(
            playgroundId,
            _authorizationContext.SleekflowCompanyId!,
            _authorizationContext.SleekflowUserId!,
            agentConfigDto);

        return new CreatePlaygroundSessionOutput(playground.Id);
    }
}