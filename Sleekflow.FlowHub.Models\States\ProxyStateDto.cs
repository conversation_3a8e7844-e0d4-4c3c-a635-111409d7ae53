using Newtonsoft.Json;
using Sleekflow.JsonConfigs;

namespace Sleekflow.FlowHub.Models.States;

public class ProxyStateDto
{
    #region State

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("identity")]
    public StateIdentity Identity { get; set; }

    [JsonProperty("workflow_context")]
    public ProxyStateWorkflowContext WorkflowContext { get; set; }

    [JsonProperty("trigger_event_body")]
    public Dictionary<string, object?> TriggerEventBody { get; set; }

    #endregion

    [JsonProperty("usr_var_dict")]
    public Dictionary<string, object?> UsrVarDict { get; set; }

    [JsonProperty("sys_var_dict")]
    public Dictionary<string, object?> SysVarDict { get; set; }

    [JsonProperty("sys_company_var_dict")]
    public Dictionary<string, object?> SysCompanyVarDict { get; set; }

    [JsonConstructor]
    public ProxyStateDto(
        string id,
        StateIdentity identity,
        ProxyStateWorkflowContext workflowContext,
        Dictionary<string, object?> triggerEventBody,
        Dictionary<string, object?> usrVarDict,
        Dictionary<string, object?> sysVarDict,
        Dictionary<string, object?> sysCompanyVarDict)
    {
        Id = id;
        Identity = identity;
        WorkflowContext = workflowContext;
        TriggerEventBody = triggerEventBody;
        UsrVarDict = usrVarDict;
        SysVarDict = sysVarDict;
        SysCompanyVarDict = sysCompanyVarDict;
    }

    public ProxyStateDto(
        ProxyState proxyState)
        : this(
            proxyState.Id,
            proxyState.Identity,
            proxyState.WorkflowContext,
            JsonConvert.DeserializeObject<Dictionary<string, object?>>(
                JsonConvert.SerializeObject(
                    proxyState.TriggerEventBody,
                    JsonConfig.DefaultJsonSerializerSettings))!,
            proxyState.UsrVarDict.ToDictionary(d => d.Key, d => d.Value),
            proxyState.SysVarDict.ToDictionary(d => d.Key, d => d.Value),
            proxyState.SysCompanyVarDict.ToDictionary(d => d.Key, d => d.Value))
    {
    }
}