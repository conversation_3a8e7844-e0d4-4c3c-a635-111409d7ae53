﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnHubspotObjectUpdatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnHubspotObjectUpdated; }
    }

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [Required]
    [JsonProperty("object_dict")]
    public Dictionary<string, object?> ObjectDict { get; set; }

    [JsonConstructor]
    public OnHubspotObjectUpdatedEventBody(
        DateTimeOffset createdAt,
        string connectionId,
        string entityTypeName,
        Dictionary<string, object?> objectDict)
        : base(createdAt)
    {
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        ObjectDict = objectDict;
    }
}