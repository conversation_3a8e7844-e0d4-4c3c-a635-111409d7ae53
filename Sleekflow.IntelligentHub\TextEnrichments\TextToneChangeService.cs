using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.TextEnrichments;

public interface ITextToneChangeService
{
    Task<string> ChangeToneAsync(string text, string targetToneType);
}

public class TextToneChangeService : ITextToneChangeService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly ILogger<TextToneChangeService> _logger;
    private readonly ITextPlugin _textPlugin;

    public TextToneChangeService(
        Kernel kernel,
        ILogger<TextToneChangeService> logger,
        ITextPlugin textPlugin)
    {
        _kernel = kernel;
        _logger = logger;
        _textPlugin = textPlugin;
    }

    public async Task<string> ChangeToneAsync(string text, string targetToneType)
    {
        switch (targetToneType)
        {
            case TargetToneTypes.Authoritative:
                return await _textPlugin.RephraseIntoAuthoritativeText(_kernel, text);
            case TargetToneTypes.Empathetic:
                return await _textPlugin.RephraseIntoEmpatheticText(_kernel, text);
            case TargetToneTypes.Friendly:
                return await _textPlugin.RephraseIntoFriendlyText(_kernel, text);
            case TargetToneTypes.Instructional:
                return await _textPlugin.RephraseIntoInstructionalText(_kernel, text);
            case TargetToneTypes.Professional:
                return await _textPlugin.RephraseIntoProfessionalText(_kernel, text);
            case TargetToneTypes.Urgent:
                return await _textPlugin.RephraseIntoUrgentText(_kernel, text);
        }

        _logger.LogError("Failed to find the corresponding type {TargetType} in TextToneChangeService", targetToneType);
        throw new Exception($"Failed to find the corresponding type {targetToneType} in TextToneChangeService");
    }
}