using Sleekflow.Configs;
using Sleekflow.Exceptions;

namespace Sleekflow.CommerceHub.Configs;

public interface IProductSearchConfig : IBaseCognitiveSearchConfig
{
}

public class ProductSearchConfig : BaseCognitiveSearchConfig, IProductSearchConfig
{
    public ProductSearchConfig()
        : base(EnvironmentVariableTarget.Process)
    {
    }

    public override string GetIndexName()
    {
        return Environment.GetEnvironmentVariable("COGNITIVE_SEARCH_COMMERCE_HUB_PRODUCT_INDEX") ??
               throw new SfMissingEnvironmentVariableException("COGNITIVE_SEARCH_COMMERCE_HUB_PRODUCT_INDEX");
    }
}