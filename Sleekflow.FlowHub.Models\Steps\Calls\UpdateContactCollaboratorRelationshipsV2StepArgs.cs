﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class UpdateContactCollaboratorRelationshipsV2StepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v2.update-contact-collaborator-relationships";

    [JsonProperty("staff_ids")]
    public List<string> StaffIds { get; set; }

    [JsonProperty("removal_action_type")]
    public string? RemovalActionType { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Conversation;

    [JsonConstructor]
    public UpdateContactCollaboratorRelationshipsV2StepArgs(
        List<string> staffIds,
        string? removalActionType)
    {
        StaffIds = staffIds;
        RemovalActionType = removalActionType;
    }
}