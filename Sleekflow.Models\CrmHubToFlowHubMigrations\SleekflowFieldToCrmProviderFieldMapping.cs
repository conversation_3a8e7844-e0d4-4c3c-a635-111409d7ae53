﻿using Newtonsoft.Json;

namespace Sleekflow.Models.CrmHubToFlowHubMigrations;

public class SleekflowFieldToCrmProviderFieldMapping
{
    [JsonProperty("sleekflow_field")]
    public string SleekflowField { get; set; }

    [JsonProperty("crm_provider_field")]
    public string CrmProviderField { get; set; }

    [JsonConstructor]
    public SleekflowFieldToCrmProviderFieldMapping(
        string sleekflowField,
        string crmProviderField)
    {
        SleekflowField = sleekflowField;
        CrmProviderField = crmProviderField;
    }
}