using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;
using Sleekflow.IntelligentHub.Models.Reviewers;
using ChatMessageContent = Microsoft.SemanticKernel.ChatMessageContent;

namespace Sleekflow.IntelligentHub.Evaluator.AddLabel;

public record AddLabelTestCase(
    string Scenario,
    ChatMessageContent[] ChatMessageContents,
    List<Models.Companies.CompanyAgentConfigs.Actions.Label> Labels,
    string Instructions,
    AddLabelResult ExpectedAddLabelResult);