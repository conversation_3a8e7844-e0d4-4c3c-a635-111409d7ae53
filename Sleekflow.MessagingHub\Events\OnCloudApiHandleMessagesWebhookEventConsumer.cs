using GraphApi.Client.Models.WebhookObjects;
using MassTransit;
using Newtonsoft.Json;
using Slack.Webhooks;
using Sleekflow.JsonConfigs;
using Sleekflow.MessagingHub.Configs;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;
using Sleekflow.MessagingHub.Webhooks;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Telemetries.Constants;
using Sleekflow.OpenTelemetry.MessagingHub;
using Sleekflow.OpenTelemetry.MessagingHub.MeterNames;
using Sleekflow.OpenTelemetry.MessagingHub.MeterTags;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiHandleWebhookMessagesEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiHandleMessagesWebhookEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiHandleMessagesWebhookEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 80;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 40;
            serviceBusReceiveEndpointConfiguration.LockDuration = TimeSpan.FromSeconds(30);
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiHandleMessagesWebhookEventConsumer : IConsumer<OnCloudApiHandleMessagesWebhookEvent>
{
    private const int DefaultMaxRetryCount = 5;
    private readonly ILogger<OnCloudApiHandleMessagesWebhookEventConsumer> _logger;
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;
    private readonly IWabaService _wabaService;
    private readonly ISlackIncomingWebhooksConfig _slackIncomingWebhooksConfig;
    private readonly IApplicationInsightsTelemetryTracer _applicationInsightsTelemetryTracer;
    private readonly IMessagingHubMeters _messagingHubMeters;

    public OnCloudApiHandleMessagesWebhookEventConsumer(
        ILogger<OnCloudApiHandleMessagesWebhookEventConsumer> logger,
        IMessagingHubWebhookService messagingHubWebhookService,
        IWabaService wabaService,
        ISlackIncomingWebhooksConfig slackIncomingWebhooksConfig,
        IApplicationInsightsTelemetryTracer applicationInsightsTelemetryTracer,
        IMessagingHubMeters messagingHubMeters)
    {
        _logger = logger;
        _messagingHubWebhookService = messagingHubWebhookService;
        _wabaService = wabaService;
        _slackIncomingWebhooksConfig = slackIncomingWebhooksConfig;
        _applicationInsightsTelemetryTracer = applicationInsightsTelemetryTracer;
        _messagingHubMeters = messagingHubMeters;
    }

    public async Task Consume(ConsumeContext<OnCloudApiHandleMessagesWebhookEvent> context)
    {
        var onCloudApiHandleWebhookMessagesEvent = context.Message;
        var retryCount = context.GetRedeliveryCount();
        var maxRetryCount = DefaultMaxRetryCount;

        try
        {
            var status = await HandleCloudApiMessagesWebhookEventAsync(onCloudApiHandleWebhookMessagesEvent);

            _logger.LogInformation(
                "Handled the messages webhook from Cloud Api {Change}/{Status}. Entry:{Entry}, WebhookEvent{WebhookEvent}.",
                JsonConvert.SerializeObject(
                    onCloudApiHandleWebhookMessagesEvent.Change,
                    JsonConfig.DefaultJsonSerializerSettings),
                status,
                JsonConvert.SerializeObject(
                    onCloudApiHandleWebhookMessagesEvent.Entry,
                    JsonConfig.DefaultJsonSerializerSettings),
                JsonConvert.SerializeObject(
                    onCloudApiHandleWebhookMessagesEvent.WebhookMessages,
                    JsonConfig.DefaultJsonSerializerSettings));
        }
        catch (Exception e)
        {
            await HandleCloudApiMessagesWebhookEventFailureAsync(
                context,
                onCloudApiHandleWebhookMessagesEvent,
                e,
                retryCount,
                maxRetryCount);
        }
    }

    private async ValueTask<bool> HandleCloudApiMessagesWebhookEventAsync(
        OnCloudApiHandleMessagesWebhookEvent onCloudApiHandleWebhookMessagesEvent)
    {
        var wabaId = onCloudApiHandleWebhookMessagesEvent.WabaId;
        var whatsappCloudApiChange = onCloudApiHandleWebhookMessagesEvent.Change;

        var value =
            JsonConvert.DeserializeObject<CloudApiWebhookValueObject>(
                JsonConvert.SerializeObject(whatsappCloudApiChange.Value));

        if (value == null)
        {
            return false;
        }

        // Get Waba configs
        var waba =
            await _wabaService.GetWabaWithFacebookWabaIdAndFacebookPhoneNumberIdAsync(
                wabaId,
                value.Metadata.PhoneNumberId);

        if (waba == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(TraceEventNames.WhatsappCloudApiWebhookDropped);
            return false;
        }

        var wabaPhoneNumberConfig =
            waba.WabaPhoneNumbers.FirstOrDefault(
                c =>
                    c.FacebookPhoneNumberId == value.Metadata.PhoneNumberId);

        if (wabaPhoneNumberConfig?.SleekflowCompanyId == null || wabaPhoneNumberConfig.WebhookUrl == null)
        {
            _applicationInsightsTelemetryTracer.TraceEvent(TraceEventNames.WhatsappCloudApiWebhookDropped);
            _messagingHubMeters.IncrementCounter(MessagingHubChannelMeterNames.WhatsappCloudApi, MessagingHubWebhookMeterOptions.MessagesWebhookDropped);

            return false;
        }

        if (_messagingHubWebhookService.IsSendCloudApiWebhookAsExpressWebhooks(
                wabaPhoneNumberConfig.SleekflowCompanyId,
                value))
        {
            // Enqueue Express Webhook for incoming messages
            await _messagingHubWebhookService.SendExpressWebhooksAsync(
                wabaPhoneNumberConfig.SleekflowCompanyId,
                WebhookEntityTypeNames.WabaPhoneNumber,
                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                new WhatsappCloudApiWebhookTravisMessage(
                    waba.Id,
                    wabaPhoneNumberConfig.Id,
                    value),
                wabaPhoneNumberConfig.Id);
        }
        else
        {
            await _messagingHubWebhookService.SendWebhooksAsync(
                wabaPhoneNumberConfig.SleekflowCompanyId,
                WebhookEntityTypeNames.WabaPhoneNumber,
                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                new WhatsappCloudApiWebhookTravisMessage(
                    waba.Id,
                    wabaPhoneNumberConfig.Id,
                    value),
                wabaPhoneNumberConfig.Id);
        }

        TraceMessageWebhookDeliveredEvent(value);
        IncrementMessageWebhookDeliveredCounter(wabaPhoneNumberConfig.WebhookUrl, value);

        return true;
    }

    private async Task HandleCloudApiMessagesWebhookEventFailureAsync(
        ConsumeContext<OnCloudApiHandleMessagesWebhookEvent> context,
        OnCloudApiHandleMessagesWebhookEvent onCloudApiHandleMessagesWebhookEvent,
        Exception e,
        int retryCount,
        int maxRetryCount)
    {
        if (retryCount < maxRetryCount)
        {
            _logger.LogError(
                e,
                "Unable to handle the messages webhook from Cloud Api, scheduling retry. WabaId: {WabaId}, Change: {Change}, Webhook Event: {WebhookEvent}, Retry Attempts: {RetryCount}",
                onCloudApiHandleMessagesWebhookEvent.WabaId,
                JsonConvert.SerializeObject(
                    onCloudApiHandleMessagesWebhookEvent.Change,
                    JsonConfig.DefaultJsonSerializerSettings),
                JsonConvert.SerializeObject(
                    onCloudApiHandleMessagesWebhookEvent,
                    JsonConfig.DefaultJsonSerializerSettings),
                retryCount);

            await context.Redeliver(delay: GetRetryTimeSpan(retryCount));

            return;
        }

        _logger.LogError(
            e,
            "Max retry limit reached. Failed to process messages webhook from Cloud Api. WabaId: {WabaId}, Change: {Change}, Webhook Event: {WebhookEvent}, Retry Attempts: {RetryCount}",
            onCloudApiHandleMessagesWebhookEvent.WabaId,
            JsonConvert.SerializeObject(
                onCloudApiHandleMessagesWebhookEvent.Change,
                JsonConfig.DefaultJsonSerializerSettings),
            JsonConvert.SerializeObject(onCloudApiHandleMessagesWebhookEvent, JsonConfig.DefaultJsonSerializerSettings),
            retryCount);

        await SendMaxRetriesReachedAlertAsync(onCloudApiHandleMessagesWebhookEvent, e);
    }

    private static TimeSpan GetRetryTimeSpan(int retryCount)
    {
        // Max 1800 second (30 minutes)
        // This event should be retry immediately after the first failure
        const int maxRetrySeconds = 60 * 30;

        var calculatedRetrySeconds = Math.Pow(1.25, retryCount) * retryCount * 30;

        var finalRetrySeconds = calculatedRetrySeconds > maxRetrySeconds ? maxRetrySeconds : calculatedRetrySeconds;

        return TimeSpan.FromSeconds(finalRetrySeconds);
    }

    private async Task SendMaxRetriesReachedAlertAsync(
        OnCloudApiHandleMessagesWebhookEvent onCloudApiHandleMessagesWebhookEvent,
        Exception exception)
    {
        var slackClient = new SlackClient(_slackIncomingWebhooksConfig.WhatsappCloudApiErrorAlertsWebhookUrl);

        // Send a message to the webhook.
        var message = new SlackMessage
        {
            Text = "*WhatsApp Cloud API Error Occured*",
            Attachments = new List<SlackAttachment>
            {
                new ()
                {
                    Color = "#FF0000",
                    Fields = new List<SlackField>
                    {
                        new ()
                        {
                            Title = "Application", Value = "MessagingHub", Short = true
                        },
                        new ()
                        {
                            Title = "Environment", Value = _slackIncomingWebhooksConfig.Env.ToUpper(), Short = true
                        },
                        new ()
                        {
                            Title = "Facebook Waba ID",
                            Value = onCloudApiHandleMessagesWebhookEvent.WabaId,
                            Short = true
                        },
                        new ()
                        {
                            Title = "DateTime", Value = DateTimeOffset.Now.ToString("u"), Short = true
                        },
                        new ()
                        {
                            Title = "Error Message",
                            Value = "Max retries reached for processing messages webhook",
                            Short = false
                        },
                        new ()
                        {
                            Title = "Error Context", Value = "```" + exception + "```", Short = false
                        },
                        new ()
                        {
                            Title = "Webhook Payload",
                            Value = "```" + JsonConvert.SerializeObject(
                                onCloudApiHandleMessagesWebhookEvent.WebhookMessages,
                                JsonConfig.DefaultJsonSerializerSettings) + "```",
                            Short = false
                        }
                    }
                }
            }
        };

        await slackClient.PostAsync(message);
    }

    private void TraceMessageWebhookDeliveredEvent(CloudApiWebhookValueObject value)
    {
        try
        {
            var customProperties = value switch
            {
                { Messages: { } messages } when messages.Count != 0 => new Dictionary<string, string>()
                {
                    {
                        "type", "message"
                    },
                    {
                        "message_type", messages.FirstOrDefault()?.Type ?? "unknown"
                    }
                },
                { Statuses: { } statuses } when statuses.Count != 0 => new Dictionary<string, string>()
                {
                    {
                        "type", "status"
                    },
                    {
                        "message_status", statuses.FirstOrDefault()?.Status ?? "unknown"
                    }
                },
                _ => null
            };

            _applicationInsightsTelemetryTracer.TraceEvent(
                TraceEventNames.WhatsappCloudApiWebhookHandled,
                customProperties);
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to trace messages webhook delivered event");
        }
    }

    private void IncrementMessageWebhookDeliveredCounter(string webhookUrl, CloudApiWebhookValueObject value)
    {
        // Track messages webhook delivered metrics for non private cloud deployments
        try
        {
            if (webhookUrl.StartsWith("https://api.sleekflow.io") ||
                webhookUrl.StartsWith("https://sleekflow-core-app-"))
            {
                _messagingHubMeters.IncrementCounter(
                    MessagingHubChannelMeterNames.WhatsappCloudApi,
                    MessagingHubWebhookMeterOptions.MessagesWebhookDelivered,
                    tags: value switch
                    {
                        { Messages: { } messages } when messages.Count != 0 => new WebhookTypeMeterTags(
                            "message",
                            messageType: messages.FirstOrDefault()?.Type),
                        { Statuses: { } statuses } when statuses.Count != 0 => new WebhookTypeMeterTags(
                            "status",
                            messageStatus: statuses.FirstOrDefault()?.Status),
                        _ => new WebhookTypeMeterTags("unknown")
                    });
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Failed to increment messages webhook delivered counter");
        }
    }
}