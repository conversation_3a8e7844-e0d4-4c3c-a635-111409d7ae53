﻿using System.Collections.Immutable;
using Pulumi;
using Pulumi.AzureNative.Resources;
using DocumentDB = Pulumi.AzureNative.DocumentDB;

namespace Sleekflow.Infras.Components.Utils;

public static class ContainerUtils
{
    public static Dictionary<string, DocumentDB.SqlResourceSqlContainer> CreateSqlResourceSqlContainers(
        ResourceGroup resourceGroup,
        DocumentDB.DatabaseAccount databaseAccount,
        DocumentDB.SqlResourceSqlDatabase cosmosDb,
        string cosmosDbId,
        ContainerParam[] containerParams)
    {
        var containerIdToContainer = containerParams
            .ToDictionary(
                c => c.Id,
                c => new DocumentDB.SqlResourceSqlContainer(
                    cosmosDbId + "-" + c.Name,
                    new DocumentDB.SqlResourceSqlContainerArgs
                    {
                        ResourceGroupName = resourceGroup.Name,
                        AccountName = databaseAccount.Name,
                        DatabaseName = cosmosDb.Name,
                        ContainerName = c.Name,
                        Resource = new DocumentDB.Inputs.SqlContainerResourceArgs
                        {
                            Id = c.Id,
                            PartitionKey = new DocumentDB.Inputs.ContainerPartitionKeyArgs
                            {
                                Paths = c.PartitionKeyPaths,
                                Kind = c.PartitionKeyPaths.Count > 1 ? "MultiHash" : "Hash",
                                Version = c.PartitionKeyPaths.Count > 1 ? 2 : null,
                            },
                            DefaultTtl = c.Ttl,
                            IndexingPolicy = new DocumentDB.Inputs.IndexingPolicyArgs()
                            {
                                IndexingMode = DocumentDB.IndexingMode.Consistent,
                                Automatic = true,
                                IncludedPaths = c.IncludedPathArgsList
                                                ?? new List<DocumentDB.Inputs.IncludedPathArgs>()
                                                {
                                                    new DocumentDB.Inputs.IncludedPathArgs()
                                                    {
                                                        Path = "/*"
                                                    }
                                                },
                                ExcludedPaths = c.ExcludedIndexingPathsList
                                                ?? new List<DocumentDB.Inputs.ExcludedPathArgs>()
                                                {
                                                    new DocumentDB.Inputs.ExcludedPathArgs()
                                                    {
                                                        Path = "/_etag/?"
                                                    }
                                                },
                                CompositeIndexes = c.CompositeIndexingPathArgsList
                                                   ?? new List<ImmutableArray<DocumentDB.Inputs.CompositePathArgs>>()
                            },
                            UniqueKeyPolicy = new DocumentDB.Inputs.UniqueKeyPolicyArgs
                            {
                                UniqueKeys = c.UniqueKeyArgsList ?? new List<DocumentDB.Inputs.UniqueKeyArgs>()
                            },
                            AnalyticalStorageTtl = c.IsAnalyticalStorageEnabled ? -1 : null,
                        },
                        Options = c.MaxThroughput != null
                            ? new DocumentDB.Inputs.CreateUpdateOptionsArgs
                            {
                                AutoscaleSettings = new DocumentDB.Inputs.AutoscaleSettingsArgs
                                {
                                    MaxThroughput = c.MaxThroughput
                                },
                            }
                            : new DocumentDB.Inputs.CreateUpdateOptionsArgs()
                    },
                    new CustomResourceOptions
                    {
                        Parent = cosmosDb,
                    }));
        return containerIdToContainer;
    }
}