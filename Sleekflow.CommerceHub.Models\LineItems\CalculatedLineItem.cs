using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Renderings;
using Discount = Sleekflow.CommerceHub.Models.Discounts.Discount;

namespace Sleekflow.CommerceHub.Models.LineItems;

public class CalculatedLineItem : LineItem
{
    [JsonProperty("applied_discounts")]
    public List<CalculatedLineItemAppliedDiscount> AppliedDiscounts { get; set; }

    [JsonProperty("line_item_pre_calculated_amount")]
    public decimal LineItemPreCalculatedAmount { get; }

    [JsonProperty("line_item_calculated_amount")]
    public decimal LineItemCalculatedAmount { get; set; }

    [JsonProperty("message_preview")]
    public RenderedTemplate MessagePreview { get; set; }

    [JsonConstructor]
    public CalculatedLineItem(
        string productVariantId,
        string productId,
        string? description,
        int quantity,
        Dictionary<string, object?> metadata,
        List<CalculatedLineItemAppliedDiscount> appliedDiscounts,
        decimal lineItemPreCalculatedAmount,
        decimal lineItemCalculatedAmount,
        RenderedTemplate messagePreview,
        Discount? lineItemDiscount)
        : base(
            productVariantId,
            productId,
            description,
            quantity,
            lineItemDiscount,
            metadata)
    {
        AppliedDiscounts = appliedDiscounts;
        LineItemPreCalculatedAmount = lineItemPreCalculatedAmount;
        LineItemCalculatedAmount = lineItemCalculatedAmount;
        MessagePreview = messagePreview;
    }
}