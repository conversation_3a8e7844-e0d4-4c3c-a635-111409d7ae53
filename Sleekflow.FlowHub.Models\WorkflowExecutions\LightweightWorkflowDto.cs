﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class LightweightWorkflowDto :
    IHasSleekflowCompanyId,
    IHasCreatedAt,
    IHasUpdatedAt
{
    [Json<PERSON>roperty(Entity.PropertyNameId)]
    public string Id { get; set; }

    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("workflow_type")]
    public string WorkflowType { get; set; }

    [JsonProperty("workflow_group_id")]
    public string? WorkflowGroupId { get; set; }

    [JsonProperty("triggers")]
    public WorkflowTriggers Triggers { get; set; }

    [JsonProperty("workflow_enrollment_settings")]
    public WorkflowEnrollmentSettings WorkflowEnrollmentSettings { get; set; }

    [JsonProperty("workflow_schedule_settings")]
    public WorkflowScheduleSettings WorkflowScheduleSettings { get; set; }

    [JsonProperty("activation_status")]
    public string ActivationStatus { get; set; }

    [JsonProperty(AuditEntity.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(AuditEntity.PropertyNameUpdatedBy)]
    public AuditEntity.SleekflowStaff? UpdatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty("manual_enrollment_source")]
    public string? ManualEnrollmentSource { get; set; }

    [JsonConstructor]
    public LightweightWorkflowDto(
        string id,
        string sleekflowCompanyId,
        string workflowId,
        string workflowVersionedId,
        string name,
        string workflowType,
        string? workflowGroupId,
        WorkflowTriggers triggers,
        WorkflowEnrollmentSettings workflowEnrollmentSettings,
        WorkflowScheduleSettings workflowScheduleSettings,
        string activationStatus,
        AuditEntity.SleekflowStaff? createdBy,
        AuditEntity.SleekflowStaff? updatedBy,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt,
        string? manualEnrollmentSource = null)
    {
        Id = id;
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        Name = name;
        WorkflowType = workflowType;
        WorkflowGroupId = workflowGroupId;
        Triggers = triggers;
        WorkflowEnrollmentSettings = workflowEnrollmentSettings;
        WorkflowScheduleSettings = workflowScheduleSettings;
        ActivationStatus = activationStatus;
        CreatedBy = createdBy;
        UpdatedBy = updatedBy;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
        ManualEnrollmentSource = manualEnrollmentSource;
    }

    public LightweightWorkflowDto(LightWeightProxyWorkflow workflow)
    {
        Id = workflow.Id;
        SleekflowCompanyId = workflow.SleekflowCompanyId;
        WorkflowId = workflow.WorkflowId;
        WorkflowVersionedId = workflow.WorkflowVersionedId;
        Name = workflow.Name;
        WorkflowType = workflow.WorkflowType;
        WorkflowGroupId = workflow.WorkflowGroupId;
        Triggers = workflow.Triggers;
        WorkflowEnrollmentSettings = workflow.WorkflowEnrollmentSettings;
        WorkflowScheduleSettings = workflow.WorkflowScheduleSettings;
        ActivationStatus = workflow.ActivationStatus;
        CreatedBy = workflow.CreatedBy;
        UpdatedBy = workflow.UpdatedBy;
        CreatedAt = workflow.CreatedAt;
        UpdatedAt = workflow.UpdatedAt;
        ManualEnrollmentSource = workflow.ManualEnrollmentSource;
    }
}