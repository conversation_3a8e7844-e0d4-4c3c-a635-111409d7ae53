using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IEnterAiAgentStepExecutor : IStepExecutor;

public class EnterAiAgentStepExecutor(
    IWorkflowStepLocator workflowStepLocator,
    IWorkflowRuntimeService workflowRuntimeService,
    IServiceProvider serviceProvider,
    ICoreCommander coreCommander,
    ILogger<EnterAiAgentStepExecutor> logger,
    IBus bus)
    : GeneralStepExecutor<CallStep<EnterAiAgentStepArgs>>(
            workflowStepLocator,
            workflowRuntimeService,
            serviceProvider),
        IEnterAiAgentStepExecutor,
        IScopedService
{
    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        // label set up here
        try
        {
            await coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactLabelRelationships",
                new UpdateContactLabelRelationshipsStepExecutor.UpdateContactLabelRelationshipsInput(
                state.Id,
                state.Identity,
                state.Identity.ObjectId,
                [$"AI_POC_{workflow.WorkflowId}_{step.Id}"],
                null,
                null));

            // complete step after 24 hrs timeout
            await bus.Publish(
                new OnAiAgentStepExitEvent(
                    step.Id,
                    state.Id,
                    stackEntries,
                    workflow.WorkflowId,
                    AgentStepExitConditions.Timeout),
                context =>
                {
                    // 24 hr for production
                    context.SetScheduledEnqueueTime(DateTime.UtcNow.AddHours(24));
                });

            // intended to not complete step here
        }
        catch (Exception ex)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                ex);
        }
    }
}