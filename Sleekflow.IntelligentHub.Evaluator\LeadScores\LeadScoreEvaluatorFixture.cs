﻿using System.ClientModel;
using System.Diagnostics;
using Azure.AI.OpenAI;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.AI.Evaluation;
using Microsoft.Extensions.AI.Evaluation.Reporting;
using Microsoft.Extensions.AI.Evaluation.Reporting.Storage;
using Microsoft.ML.Tokenizers;
using Sleekflow.IntelligentHub.Configs;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;
using Sleekflow.IntelligentHub.Evaluator.LeadScores.Methods;

namespace Sleekflow.IntelligentHub.Evaluator.LeadScores;

public class LeadScoreEvaluatorFixture
{
    private const string StorageRootPath = "./";
    private readonly DefaultLeadScoringMethod _method = new ();
    private readonly ReportingConfiguration _reportingConfiguration = GetReportingConfiguration();

    private static ReportingConfiguration GetReportingConfiguration()
    {
        var config = new AzureOpenAIConfig();

        var endpoint = new Uri(config.Endpoint);
        var oaioptions = new AzureOpenAIClientOptions();
        var azureClient = new AzureOpenAIClient(endpoint, new ApiKeyCredential(config.Key), oaioptions);

        // Setup the chat client that is used to perform the evaluations
        var chatClient = azureClient.GetChatClient("turbo").AsIChatClient();

        var chatConfig = new ChatConfiguration(chatClient);

        // The DiskBasedReportingConfiguration caches LLM responses to reduce costs and
        // increase test run performance.
        return DiskBasedReportingConfiguration.Create(
            storageRootPath: StorageRootPath,
            chatConfiguration: chatConfig,
            evaluators:
            [
                // Measures the extent to which the model's retrieved documents are pertinent and directly related to the given queries.
                new ChatEvalAnswerScoringEvaluator()
            ],
            executionName: "default");
    }

    public async Task<LeadScoreEvaluatorResult[]> EvaluateAsync(
        LeadScoreTestCase testCase,
        CancellationToken cancellationToken)
    {
        if (testCase.LeadScoreConfigs == null || testCase.LeadScoreConfigs.Count == 0)
        {
            throw new Exception("LeadScoreConfigs list cannot be null or empty");
        }

        var outputs = await Task.WhenAll(
            testCase.LeadScoreConfigs.Select(
                async leadScoreConfig =>
                {
                    var stopwatch = Stopwatch.StartNew();

                    LeadScoreEvalOutput output;

                    try
                    {
                        output = await _method.CompleteAsync(
                            leadScoreConfig.AdditionalPrompt,
                            testCase.ChatMessageContents);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex);
                        throw;
                    }

                    stopwatch.Stop();

                    return (leadScoreConfig, output, stopwatch.ElapsedMilliseconds);
                }));

        return await Task.WhenAll(
            outputs.Select(
                async output =>
                {
                    var evaluationResult = await new LeadScoreEvaluatorAssistant(_method).Evaluate(
                        testCase,
                        _reportingConfiguration,
                        output.output,
                        cancellationToken);

                    return new LeadScoreEvaluatorResult(
                        output.leadScoreConfig.Name,
                        output.output.Answer,
                        evaluationResult.Item2,
                        output.ElapsedMilliseconds);
                }));
    }
}