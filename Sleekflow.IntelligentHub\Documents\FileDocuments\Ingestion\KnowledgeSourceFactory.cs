using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;

namespace Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;

public interface IKnowledgeSourceFactory
{
    IKnowledgeSource GetKnowledgeSource(string blobType);

    IKnowledgeSource GetKnowledgeSourceByMimeType(string mimeType);
}

public class KnowledgeSourceFactory : IKnowledgeSourceFactory, IScopedService
{
    private readonly IPdfKnowledgeSource _pdfKnowledgeSource;
    private readonly ICsvKnowledgeSource _csvKnowledgeSource;
    private readonly IExcelKnowledgeSource _excelKnowledgeSource;
    private readonly IWordKnowledgeSource _wordKnowledgeSource;
    private readonly IJpgKnowledgeSource _jpgKnowledgeSource;
    private readonly IMp4KnowledgeSource _mp4KnowledgeSource;

    public KnowledgeSourceFactory(
        IPdfKnowledgeSource pdfKnowledgeSource,
        ICsvKnowledgeSource csvKnowledgeSource,
        IExcelKnowledgeSource excelKnowledgeSource,
        IWordKnowledgeSource wordKnowledgeSource,
        IJpgKnowledgeSource jpgKnowledgeSource,
        IMp4KnowledgeSource mp4KnowledgeSource)
    {
        _pdfKnowledgeSource = pdfKnowledgeSource;
        _csvKnowledgeSource = csvKnowledgeSource;
        _excelKnowledgeSource = excelKnowledgeSource;
        _wordKnowledgeSource = wordKnowledgeSource;
        _jpgKnowledgeSource = jpgKnowledgeSource;
        _mp4KnowledgeSource = mp4KnowledgeSource;
    }

    public IKnowledgeSource GetKnowledgeSource(string blobType)
    {
        return blobType switch
        {
            DocumentTypes.Pdf => _pdfKnowledgeSource,
            DocumentTypes.Csv => _csvKnowledgeSource,
            DocumentTypes.MicrosoftExcel => _excelKnowledgeSource,
            DocumentTypes.MicrosoftWord => _wordKnowledgeSource,
            DocumentTypes.Jpg or DocumentTypes.Jpeg => _jpgKnowledgeSource,
            DocumentTypes.Mp4 => _mp4KnowledgeSource,
            _ => throw new Exception($"No implementation for blob type {blobType}")
        };

        // Add a default return or handle other cases appropriately
    }

    public IKnowledgeSource GetKnowledgeSourceByMimeType(string mimeType)
    {
        return mimeType.ToLowerInvariant() switch
        {
            "application/pdf" => _pdfKnowledgeSource,
            "text/csv" => _csvKnowledgeSource,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" => _excelKnowledgeSource,
            "application/vnd.ms-excel" => _excelKnowledgeSource,
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document" => _wordKnowledgeSource,
            "application/msword" => _wordKnowledgeSource,
            "image/jpeg" => _jpgKnowledgeSource,
            "image/jpg" => _jpgKnowledgeSource,
            "video/mp4" => _mp4KnowledgeSource,
            _ => throw new NotSupportedException($"MIME type '{mimeType}' is not supported for file ingestion")
        };
    }
}