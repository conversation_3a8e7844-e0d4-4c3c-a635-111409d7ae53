function post_proxy()
    local r = response.load();



    local responseData = r:data()

    -- Check if the request is allowed by RBAC
    local rbac_not_allowed = responseData:get('unauthorized_access')
    if (rbac_not_allowed ~= nil) then
        custom_error("Unauthorized access", 403)
    end

    local success = responseData:get('success')
    local http_status_code = responseData:get('http_status_code')

    local error_code = responseData:get('error_code')
    local request_id = responseData:get('request_id')

    if (success ~= nil) then
        r:headers('X-SLEEKFLOW-SUCCESS', tostring(success))
    end
    if (http_status_code ~= nil) then
        r:headers('X-SLEEKFLOW-HTTP-STATUS-CODE', tostring(http_status_code))
    end
    if (error_code ~= nil) then
        r:headers('X-SLEEKFLOW-ERROR-CODE', tostring(error_code))
    end
    if (request_id ~= nil) then
        r:headers('X-SLEEKFLOW-REQUEST-ID', tostring(request_id))
    end
end


function custom_error(message, status_code)
    response.clear()
    response.status(status_code)
    response.set_header("Content-Type", "application/json")
    response.body(string.format('{"error": "%s"}', message))
end