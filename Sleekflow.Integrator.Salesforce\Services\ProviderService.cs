﻿using Newtonsoft.Json;
using Sleekflow.DependencyInjection;

namespace Sleekflow.Integrator.Salesforce.Services;

public interface IProviderService
{
    List<ProviderService.SupportedEntityType> GetSupportedEntityTypes();

    bool IsSupportedEntityType(string entityTypeName);
}

public class ProviderService : ISingletonService, IProviderService
{
    public class SupportedEntityType
    {
        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("is_auto_sync_supported")]
        public bool IsAutoSyncSupported { get; set; }

        public SupportedEntityType(
            string name,
            bool isAutoSyncSupported)
        {
            Name = name;
            IsAutoSyncSupported = isAutoSyncSupported;
        }
    }

    private readonly List<SupportedEntityType> _entityTypeNames = new List<SupportedEntityType>
    {
        new SupportedEntityType("Activity", true), // Event
        new SupportedEntityType("Company", true), // Account
        new SupportedEntityType("Contact", true), // Contact
        new SupportedEntityType("Lead", true), // Lead
        new SupportedEntityType("Note", false), // ContentNote
        new SupportedEntityType("Opportunity", true), // Opportunity
        new SupportedEntityType("User", true), // User
        new SupportedEntityType("Campaign", true), // Campaign
        new SupportedEntityType("CampaignMember", true), // CampaignMember
    };

    public List<SupportedEntityType> GetSupportedEntityTypes()
    {
        return _entityTypeNames.ToList();
    }

    public bool IsSupportedEntityType(string entityTypeName)
    {
        return _entityTypeNames.Select(etn => etn.Name).Contains(entityTypeName);
    }
}