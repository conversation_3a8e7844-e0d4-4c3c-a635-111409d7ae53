﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.FlowHub.Configs;

public interface IWorkflowRateLimitConfig
{
    int EnrollmentRateLimitWindowSeconds { get; }

    int MaxEnrollmentAllowedWithinWindow { get; }
}

public class WorkflowRateLimitConfig : IWorkflowRateLimitConfig, IConfig
{
    public int EnrollmentRateLimitWindowSeconds { get; }

    public int MaxEnrollmentAllowedWithinWindow { get; }

    public WorkflowRateLimitConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        EnrollmentRateLimitWindowSeconds =
            int.TryParse(Environment.GetEnvironmentVariable("WORKFLOW_ENROLLMENT_RATE_LIMIT_WINDOW_SECONDS", target), out var rateLimitWindowSeconds)
                ? rateLimitWindowSeconds
                : throw new SfMissingEnvironmentVariableException("WORKFLOW_ENROLLMENT_RATE_LIMIT_WINDOW_SECONDS");

        MaxEnrollmentAllowedWithinWindow =
            int.TryParse(Environment.GetEnvironmentVariable("WORKFLOW_ENROLLMENT_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW", target), out var maxAllowedWithinWindow)
                ? maxAllowedWithinWindow
                : throw new SfMissingEnvironmentVariableException("WORKFLOW_ENROLLMENT_RATE_LIMIT_MAX_ALLOWED_WITHIN_WINDOW");
    }
}