﻿using Sleekflow.CrmHub.Models.UserMappingConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Zoho.UserMappingConfigs;

public interface IZohoUserMappingConfigRepository : IRepository<ZohoUserMappingConfig>
{
}

public class ZohoUserMappingConfigRepository
    : BaseRepository<ZohoUserMappingConfig>,
        IZohoUserMappingConfigRepository,
        ISingletonService
{
    public ZohoUserMappingConfigRepository(
        ILogger<BaseRepository<ZohoUserMappingConfig>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}