using Microsoft.Extensions.Diagnostics.HealthChecks;

namespace Sleekflow.Mvc.HealthChecks;

public class StartupHealthCheck : IHealthCheck
{
    private volatile bool _hasStarted;

    public bool StartupCompleted
    {
        get => _hasStarted;
        set => _hasStarted = value;
    }

    public Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        return Task.FromResult(
            _hasStarted
                ? HealthCheckResult.Healthy("The application startup task has completed.")
                : HealthCheckResult.Unhealthy("That application startup task is still running."));
    }
}