using MassTransit;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiConversationAnalyticsTriggerEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiConversationAnalyticsTriggerEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiConversationAnalyticsTriggerEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class
    OnCloudApiConversationAnalyticsTriggerEventConsumer : IConsumer<OnCloudApiConversationAnalyticsTriggerEvent>
{
    private readonly IBus _bus;
    private readonly IBusinessBalanceService _businessBalance;
    private readonly ILogger<OnCloudApiConversationAnalyticsTriggerEventConsumer> _logger;

    public OnCloudApiConversationAnalyticsTriggerEventConsumer(
        IBus bus,
        IBusinessBalanceService businessBalance,
        ILogger<OnCloudApiConversationAnalyticsTriggerEventConsumer> logger)
    {
        _bus = bus;
        _logger = logger;
        _businessBalance = businessBalance;
    }

    public async Task Consume(ConsumeContext<OnCloudApiConversationAnalyticsTriggerEvent> context)
    {
        var onCloudApiConversationAnalyticsTriggerEvent = context.Message;
        var cancellationToken = context.CancellationToken;

        var businessBalances = await _businessBalance.GetAllAsync();

        foreach (var facebookBusinessId in businessBalances.Select(businessBalance => businessBalance.FacebookBusinessId))
        {
            await _bus.Publish(
                new OnCloudApiAccumulateHalfHourConversationUsageTransactionEvent(facebookBusinessId),
                publishContext => { publishContext.Delay = TimeSpan.FromMinutes(5); },
                cancellationToken);
        }
    }
}