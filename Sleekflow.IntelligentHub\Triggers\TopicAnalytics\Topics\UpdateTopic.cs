using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.TopicAnalytics;
using Sleekflow.IntelligentHub.TopicAnalytics;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Triggers.TopicAnalytics.Topics;

[TriggerGroup(ControllerNames.TopicAnalytics)]
public class UpdateTopic : ITrigger<UpdateTopic.UpdateTopicInput, UpdateTopic.UpdateTopicOutput>
{
    private readonly ITopicAnalyticsService _topicAnalyticsService;

    public UpdateTopic(ITopicAnalyticsService topicAnalyticsService)
    {
        _topicAnalyticsService = topicAnalyticsService;
    }

    public class UpdateTopicInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }


        [JsonProperty("updated_properties")]
        public Dictionary<string, object?> UpdatedProperties { get; set; }

        [JsonConstructor]
        public UpdateTopicInput(
            string sleekflowCompanyId,
            string id,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            Dictionary<string, object?> updatedProperties)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            UpdatedProperties = updatedProperties;
        }
    }

    public class UpdateTopicOutput
    {
        [JsonProperty("topic")]
        public TopicAnalyticsTopic Topic { get; set; }

        [JsonConstructor]
        public UpdateTopicOutput(TopicAnalyticsTopic topic)
        {
            Topic = topic;
        }
    }

    public async Task<UpdateTopicOutput> F(UpdateTopicInput input)
    {
        var topic = await _topicAnalyticsService.UpdateTopicAnalyticsTopic(
            input.SleekflowCompanyId,
            input.Id,
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds,
            input.UpdatedProperties);

        return new UpdateTopicOutput(topic);
    }
}