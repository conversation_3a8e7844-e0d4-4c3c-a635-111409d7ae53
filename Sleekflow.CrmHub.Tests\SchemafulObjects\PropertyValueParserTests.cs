﻿using Microsoft.Extensions.Logging.Abstractions;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.CrmHub.SchemafulObjects.Dtos;
using Sleekflow.CrmHub.SchemafulObjects.Utils;
using Sleekflow.Ids;
using Sleekflow.Persistence;

namespace Sleekflow.CrmHub.Tests.SchemafulObjects;

public class PropertyValueParserTests
{
    private const string SingleLineTextPropertyId = "single_line_text";
    private const string NumericPropertyId = "numeric";
    private const string DecimalPropertyId = "decimal";
    private const string SingleChoicePropertyId = "single_choice";
    private const string MultipleChoicePropertyId = "multiple_choice";
    private const string BooleanPropertyId = "boolean";
    private const string DatePropertyId = "date";
    private const string DateTimePropertyId = "datetime";
    private const string ArrayObjectPropertyId = "array_object";

    private const string OptionId1 = "option_id_1";
    private const string OptionId2 = "option_id_2";
    private const string OptionId3 = "option_id_3";

    private readonly List<Property> _properties;
    private readonly List<Property> _nullableProperties = new ();

    private PropertyValueParser PropertyValueParser { get; set; }

    private record MockPropertyValue
    {
        [JsonProperty("property_values")]
        public Dictionary<string, object?> PropertyValues { get; set; }

        [JsonConstructor]
        public MockPropertyValue(Dictionary<string, object?> propertyValues)
        {
            PropertyValues = propertyValues;
        }
    }

    public PropertyValueParserTests()
    {
        PropertyValueParser = new PropertyValueParser(GetIdService());

        var options = new List<Option>
        {
            new Option(OptionId1, "first", 0),
            new Option(OptionId2, "second", 1),
            new Option(OptionId3, "third", 2),
        };

        _properties = new List<Property>
        {
            new Property(
                SingleLineTextPropertyId,
                SingleLineTextPropertyId,
                SingleLineTextPropertyId,
                new SingleLineTextDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                NumericPropertyId,
                NumericPropertyId,
                NumericPropertyId,
                new NumericDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DecimalPropertyId,
                DecimalPropertyId,
                DecimalPropertyId,
                new DecimalDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                SingleChoicePropertyId,
                SingleChoicePropertyId,
                SingleChoicePropertyId,
                new SingleChoiceDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                options),
            new Property(
                MultipleChoicePropertyId,
                MultipleChoicePropertyId,
                MultipleChoicePropertyId,
                new MultipleChoiceDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                options),
            new Property(
                BooleanPropertyId,
                BooleanPropertyId,
                BooleanPropertyId,
                new BooleanDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DatePropertyId,
                DatePropertyId,
                DatePropertyId,
                new DateDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DateTimePropertyId,
                DateTimePropertyId,
                DateTimePropertyId,
                new DateTimeDataType(),
                true,
                true,
                true,
                true,
                0,
                null,
                DateTimeOffset.UtcNow,
                null),
        };

        _properties.ForEach(
            p =>
            {
                _nullableProperties.Add(
                    new Property(
                        p.Id,
                        p.DisplayName,
                        p.UniqueName,
                        p.DataType,
                        false,
                        true,
                        true,
                        true,
                        0,
                        null,
                        DateTimeOffset.UtcNow,
                        p.Options));
            });
    }

    [Test]
    public void ParsePropertyValue_WithStringInputs_ShouldReturnCorrectValue()
    {
        var stringInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, "string"
            },
            {
                NumericPropertyId, "12345"
            },
            {
                DecimalPropertyId, "123.45"
            },
            {
                BooleanPropertyId, "true"
            },
            {
                DateTimePropertyId, "2023-01-01T00:00:00Z"
            },
            {
                DatePropertyId, "2023-01-01T00:00:00Z"
            }
        };

        var parsedValues = PropertyValueParser.Parse(_properties, stringInputValues);

        Assert.That(parsedValues[SingleLineTextPropertyId], Is.EqualTo("string"));
        Assert.That(parsedValues[NumericPropertyId], Is.EqualTo(12345));
        Assert.That(parsedValues[DecimalPropertyId], Is.EqualTo(123.45));
        Assert.That(parsedValues[BooleanPropertyId], Is.EqualTo(true));
        Assert.That(parsedValues[DateTimePropertyId], Is.EqualTo(DateTimeOffset.Parse("2023-01-01T00:00:00Z")));
        Assert.That(parsedValues[DatePropertyId], Is.EqualTo(DateTimeOffset.Parse("2023-01-01T00:00:00Z")));
    }

    [Test]
    public void ParsePropertyValue_WithOptionValueInputs_ShouldReturnCorrectValue()
    {
        var validInputValues = new Dictionary<string, object?>
        {
            {
                SingleChoicePropertyId, "first"
            },
            {
                MultipleChoicePropertyId, JArray.FromObject(
                    new List<string>
                    {
                        "first", "second"
                    })
            }
        };

        var parsedValues = PropertyValueParser.Parse(_properties, validInputValues);

        Assert.That(parsedValues[SingleChoicePropertyId], Is.EqualTo(OptionId1));
        Assert.That(
            parsedValues[MultipleChoicePropertyId],
            Is.EqualTo(
                JArray.FromObject(
                    new List<string>
                    {
                        OptionId1, OptionId2
                    })));

        var validInputValues2 = new Dictionary<string, object?>
        {
            {
                MultipleChoicePropertyId, "[\"first\", \"second\"]"
            }
        };

        var parsedValues2 = PropertyValueParser.Parse(_properties, validInputValues2);

        Assert.That(
            parsedValues2[MultipleChoicePropertyId],
            Is.EqualTo(
                JArray.FromObject(
                    new List<string>
                    {
                        OptionId1, OptionId2
                    })));
    }

    [Test]
    public void ParsePropertyValue_WithInvalidInputs_ShouldReturnRawValue()
    {
        var stringInputValues = new Dictionary<string, object?>
        {
            {
                SingleLineTextPropertyId, true
            },
            {
                NumericPropertyId, "hi"
            },
            {
                DecimalPropertyId, "hi"
            },
            {
                BooleanPropertyId, 123
            },
            {
                DateTimePropertyId, "2023-0123T"
            },
            {
                DatePropertyId, "2023-0123T"
            },
            {
                SingleChoicePropertyId, "non exist value"
            },
            {
                MultipleChoicePropertyId, JArray.FromObject(
                    new List<string>
                    {
                        "non exist value", "second"
                    })
            }
        };

        var parsedValues = PropertyValueParser.Parse(_properties, stringInputValues);

        Assert.That(parsedValues[SingleLineTextPropertyId], Is.EqualTo(true));
        Assert.That(parsedValues[NumericPropertyId], Is.EqualTo("hi"));
        Assert.That(parsedValues[DecimalPropertyId], Is.EqualTo("hi"));
        Assert.That(parsedValues[BooleanPropertyId], Is.EqualTo(123));
        Assert.That(parsedValues[DateTimePropertyId], Is.EqualTo("2023-0123T"));
        Assert.That(parsedValues[DatePropertyId], Is.EqualTo("2023-0123T"));
        Assert.That(parsedValues[SingleChoicePropertyId], Is.EqualTo("non exist value"));
        Assert.That(
            parsedValues[MultipleChoicePropertyId],
            Is.EqualTo(
                JArray.FromObject(
                    new List<string>
                    {
                        "non exist value", "second"
                    })));
    }

    [Test]
    public void ParseArrayObjectValue_WithValidInnerSchemafulObjectInputs_ShouldReturnCorrectValue()
    {
        const string existedInnerSchemafulObjectId = "existedInnerSchemafulObjectId";

        var arrayObjectProperty = GenerateArrayObjectProperty();

        var propertyValuesForInnerSchemafulObject = new Dictionary<string, object?>
        {
            {
                NumericPropertyId, "12345"
            },
            {
                DatePropertyId, "2023-01-01T00:00:00Z"
            },
            {
                MultipleChoicePropertyId, JArray.FromObject(new List<object>
                {
                    OptionId1, OptionId2
                })
            }
        };

        var inputValue = JArray.FromObject(
            new List<InnerSchemafulObjectInputDto>
            {
                new InnerSchemafulObjectInputDto(
                    string.Empty,
                    propertyValuesForInnerSchemafulObject,
                    null),
                new InnerSchemafulObjectInputDto(
                    existedInnerSchemafulObjectId,
                    propertyValuesForInnerSchemafulObject,
                    DateTimeOffset.Parse("2023-01-01T00:00:00Z"))
            });

        var parsedValue = PropertyValueParser.Parse(arrayObjectProperty, inputValue);

        var innerSchemafulObjects = new List<InnerSchemafulObjectDto>();

        var convertable =
            parsedValue is JArray arr &&
            TryConvertToInnerSchemafulObject(arr, out innerSchemafulObjects);

        Assert.Multiple(() =>
        {
            Assert.That(convertable, Is.True);
            Assert.That(innerSchemafulObjects.Count, Is.EqualTo(2));

            Assert.That(string.IsNullOrWhiteSpace(innerSchemafulObjects[0].Id), Is.False);
            Assert.That(string.IsNullOrWhiteSpace(innerSchemafulObjects[1].Id), Is.False);
        });

        // new created item
        var propertyValues1 = innerSchemafulObjects[0].PropertyValues;
        Assert.Multiple(() =>
        {
            Assert.That(propertyValues1.Count, Is.EqualTo(3));
            Assert.That(propertyValues1[NumericPropertyId], Is.EqualTo(12345));
            Assert.That(propertyValues1[DatePropertyId], Is.EqualTo(DateTimeOffset.Parse("2023-01-01T00:00:00Z")));
            Assert.That(
                propertyValues1[MultipleChoicePropertyId],
                Is.EqualTo(
                    JArray.FromObject(
                        new List<string>
                        {
                        OptionId1, OptionId2
                        })));
        });

        // updated item
        Assert.Multiple(() =>
        {
            Assert.That( innerSchemafulObjects[1].Id, Is.EqualTo(existedInnerSchemafulObjectId));
            Assert.That( innerSchemafulObjects[1].CreatedAt, Is.EqualTo(DateTimeOffset.Parse("2023-01-01T00:00:00Z")));
        });
    }

    private Property GenerateArrayObjectProperty()
    {
        var properties = new List<Property>
        {
            new Property(
                NumericPropertyId,
                "Test Numeric",
                "test_numeric",
                new NumericDataType(),
                false,
                false,
                true,
                true,
                2,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                DatePropertyId,
                "Test Date",
                "test_numeric",
                new DateDataType(),
                false,
                true,
                true,
                true,
                5,
                null,
                DateTimeOffset.UtcNow,
                null),
            new Property(
                MultipleChoicePropertyId,
                "Test MultipleChoice",
                MultipleChoicePropertyId,
                new MultipleChoiceDataType(),
                false,
                true,
                true,
                true,
                7,
                null,
                DateTimeOffset.UtcNow,
                new List<Option>
                {
                    new Option(OptionId1, "option 1", 0),
                    new Option(OptionId2, "option 2", 1),
                    new Option(OptionId3, "option 3", 2)
                })
        };

        var innerSchema = new InnerSchema(properties);

        return new Property(
            ArrayObjectPropertyId,
            "Test ArrayObject",
            "test_arrayobject",
            new ArrayObjectDataType(innerSchema),
            false,
            false,
            true,
            true,
            1,
            null,
            DateTimeOffset.UtcNow,
            null);
    }

    private static bool TryConvertToInnerSchemafulObject(JArray jArray, out List<InnerSchemafulObjectDto> innerSchemafulObjects)
    {
        try
        {
            innerSchemafulObjects = jArray.ToObject<List<InnerSchemafulObjectDto>>()!;
            return true;
        }
        catch
        {
            innerSchemafulObjects = new List<InnerSchemafulObjectDto>();
            return false;
        }
    }

    private IdService GetIdService()
    {
        var dbContainerResolver = new DbContainerResolver(new MyCrmHubDbConfig());

        var idService = new IdService(
            NullLogger<IdService>.Instance,
            dbContainerResolver);

        return idService;
    }

    private class MyCrmHubDbConfig : IDbConfig
    {
        public string Endpoint => "https://sleekflow2bd1537b.documents.azure.com:443/";

        public string Key => "****************************************************************************************";

        public string DatabaseId => "crmhubdb";
    }
}