using MassTransit;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Models.WhatsappCloudApi;

namespace Sleekflow.MessagingHub.Events;

public class GetWabasEventConsumerDefinition
    : ConsumerDefinition<GetWabasEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<GetWabasEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class GetWabasEventConsumer
    : IConsumer<GetWabasRequest>
{
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWabaService _wabaService;

    public GetWabasEventConsumer(
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IWabaService wabaService)
    {
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _wabaService = wabaService;
    }

    public async Task Consume(ConsumeContext<GetWabasRequest> context)
    {
        var request = context.Message;
        var wabas = await _wabaService.GetWabasAsync(request.SleekflowCompanyId);
        var formattedWabas = wabas.Where(x => x.FacebookBusinessId != null)
            .Select(x => new Waba(x.FacebookBusinessId!, x.SleekflowCompanyIds)).ToList();

        await context.RespondAsync(new GetWabasReply(formattedWabas));
    }
}