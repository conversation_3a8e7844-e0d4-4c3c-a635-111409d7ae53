using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.UserEventHub.Constants;
using Sleekflow.UserEventHub.Models.Constants;
using Sleekflow.UserEventHub.Models.Notifications;
using Sleekflow.UserEventHub.Notifications;

namespace Sleekflow.UserEventHub.Triggers.Authorized.Notifications;

[TriggerGroup(
    ControllerNames.Notifications,
    $"{BasePaths.Authorized}",
    new[]
    {
        AuthorizationFilterNames.HeadersAuthorizationFuncFilter
    })]
public class RegisterAndEnableMobilePushNotification
    : ITrigger<RegisterAndEnableMobilePushNotification.RegisterAndEnableMobilePushNotificationInput,
        RegisterAndEnableMobilePushNotification.RegisterAndEnableMobilePushNotificationOutput>
{
    private readonly INotificationService _notificationService;

    private readonly ISleekflowAuthorizationContext _authorizationContext;

    public RegisterAndEnableMobilePushNotification(
        INotificationService notificationService,
        ISleekflowAuthorizationContext authorizationContext)
    {
        _notificationService = notificationService;
        _authorizationContext = authorizationContext;
    }

    public class RegisterAndEnableMobilePushNotificationInput
    {
        [Required]
        [JsonProperty(DeviceRegistration.PropertyNameHandle)]
        public string Handle { get; set; }

        [Required]
        [JsonProperty(DeviceRegistration.PropertyNameTags)]
        [Validations.ValidateObject]
        public string[] Tags { get; set; }

        [JsonProperty(DeviceRegistration.PropertyNameDeviceId)]
        public string? DeviceId { get; set; }

        [JsonProperty(DeviceRegistration.PropertyNamePlatform)]
        public string? Platform { get; set; }

        [JsonProperty(DeviceRegistration.PropertyNameHubName)]
        public string? HubName { get; set; }

        [JsonConstructor]
        public RegisterAndEnableMobilePushNotificationInput(string handle, string[] tags, string? deviceId, string? platform, string? hubName)
        {
            Handle = handle;
            Tags = tags;
            DeviceId = deviceId;
            Platform = platform;
            HubName = hubName;
        }
    }

    public class RegisterAndEnableMobilePushNotificationOutput
    {
        [JsonProperty("notification_settings")]
        public NotificationSettingsDto NotificationSettings { get; set; }

        [JsonConstructor]
        public RegisterAndEnableMobilePushNotificationOutput(NotificationSettingsDto notificationSettings)
        {
            NotificationSettings = notificationSettings;
        }
    }

    public async Task<RegisterAndEnableMobilePushNotificationOutput> F(
        RegisterAndEnableMobilePushNotificationInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId;
        var userId = _authorizationContext.SleekflowUserId;

        if (string.IsNullOrEmpty(sleekflowCompanyId) || string.IsNullOrEmpty(userId))
        {
            throw new SfUserFriendlyException(
                "SleekflowCompanyId or UserId are required to get notification settings.");
        }

        var updatedNotificationSettings = await _notificationService.RegisterAndEnableMobilePushNotification(
            input.Handle,
            input.Tags,
            userId,
            sleekflowCompanyId,
            input.Platform,
            input.DeviceId,
            input.HubName);

        return new RegisterAndEnableMobilePushNotificationOutput(
            new NotificationSettingsDto(updatedNotificationSettings));
    }
}