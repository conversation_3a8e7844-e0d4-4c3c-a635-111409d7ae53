using System.Text;
using Newtonsoft.Json.Linq;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Searches;

public interface ISearchFilterBuilder
{
    string BuildFilterStr(
        IList<SearchFilterGroup> searchFilterGroups,
        IReadOnlyDictionary<string, SearchFieldDefinition> searchFieldNameToDefinitionDict,
        string sleekflowCompanyId,
        string storeId);
}

public class SearchFilterBuilder : ISearchFilterBuilder, ISingletonService
{
    public string BuildFilterStr(
        IList<SearchFilterGroup> searchFilterGroups,
        IReadOnlyDictionary<string, SearchFieldDefinition> searchFieldNameToDefinitionDict,
        string sleekflowCompanyId,
        string storeId)
    {
        var defaultFilterGroups = new List<SearchFilterGroup>
        {
            Capacity = 16
        };
        defaultFilterGroups.Add(
            new SearchFilterGroup(
                new List<SearchFilter>
                {
                    new (IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId, "=", sleekflowCompanyId)
                }));
        defaultFilterGroups.Add(
            new SearchFilterGroup(
                new List<SearchFilter>
                {
                    new (CommonFieldNames.PropertyNameStoreId, "=", storeId)
                }));

        var myFilterGroups = searchFilterGroups
            .Concat(defaultFilterGroups)
            .ToList();

        var stringBuilder = new StringBuilder();

        stringBuilder.AppendJoin(
            " and ",
            myFilterGroups
                .Where(fg => fg.Filters.Any())
                .Select(
                    fg =>
                    {
                        var filterClauseStrs = fg.Filters.Select(
                            f =>
                            {
                                var searchFieldDefinition = searchFieldNameToDefinitionDict[f.FieldName];

                                var searchPath = searchFieldDefinition.SearchPath;

                                var isPrices = searchFieldDefinition.Type == SearchFieldTypes.CollectionPrice;
                                var isCollection = searchFieldDefinition.Type.StartsWith("Collection");

                                if (isPrices)
                                {
                                    if (f.Value is not JObject jObject
                                        || !jObject.ContainsKey("currency_iso_code")
                                        || !jObject.ContainsKey("amount"))
                                    {
                                        throw new NotImplementedException();
                                    }

                                    var price = jObject.ToObject<Price>()!;

                                    return f.Operator switch
                                    {
                                        "=" =>
                                            $"{searchPath}/any(t: t/currency_iso_code eq '{price.CurrencyIsoCode}' and t/amount eq {price.Amount})",
                                        ">" =>
                                            $"{searchPath}/any(t: t/currency_iso_code eq '{price.CurrencyIsoCode}' and t/amount gt {price.Amount})",
                                        "<" =>
                                            $"{searchPath}/any(t: t/currency_iso_code eq '{price.CurrencyIsoCode}' and t/amount lt {price.Amount})",
                                        ">=" =>
                                            $"{searchPath}/any(t: t/currency_iso_code eq '{price.CurrencyIsoCode}' and t/amount ge {price.Amount})",
                                        "<=" =>
                                            $"{searchPath}/any(t: t/currency_iso_code eq '{price.CurrencyIsoCode}' and t/amount le {price.Amount})",
                                        "!=" =>
                                            $"{searchPath}/any(t: t/currency_iso_code eq '{price.CurrencyIsoCode}' and t/amount ne {price.Amount})",
                                        _ => throw new NotImplementedException()
                                    };

                                    // TODO Better to throw a specific exception here
                                }

                                if (f.Value is bool boolValue)
                                {
                                    return f.Operator switch
                                    {
                                        "=" => $"{searchPath} eq {(boolValue ? "true" : "false")}",
                                        "!=" => $"{searchPath} ne {(boolValue ? "false" : "true")}",

                                        // TODO Better to throw a specific exception here
                                        _ => throw new NotImplementedException()
                                    };
                                }

                                return f.Operator switch
                                {
                                    "contains" => isCollection
                                        ? $"{searchPath}/any(t: t eq '{f.Value}')"
                                        : $"search.ismatchscoring('{f.Value}', '{searchPath}')",
                                    "startsWith" => $"search.ismatchscoring('{f.Value}*', '{searchPath}')",
                                    "=" => $"{searchPath} eq '{f.Value}'",
                                    ">" => $"{searchPath} gt '{f.Value}'",
                                    "<" => $"{searchPath} lt '{f.Value}'",
                                    ">=" => $"{searchPath} ge '{f.Value}'",
                                    "<=" => $"{searchPath} le '{f.Value}'",
                                    "!=" => $"{searchPath} ne '{f.Value}'",

                                    // TODO Better to throw a specific exception here
                                    _ => throw new NotImplementedException()
                                };
                            });

                        var sb = new StringBuilder();
                        sb.Append('(');
                        sb.AppendJoin(" or ", filterClauseStrs);
                        sb.Append(')');

                        return sb.ToString();
                    }));

        return stringBuilder.ToString();
    }
}