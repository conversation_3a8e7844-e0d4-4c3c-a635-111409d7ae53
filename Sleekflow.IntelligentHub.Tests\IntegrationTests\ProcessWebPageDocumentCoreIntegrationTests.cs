﻿using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;
using Sleekflow.IntelligentHub.Triggers.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Triggers.Documents.WebPageDocuments;
using Sleekflow.IntelligentHub.Triggers.KnowledgeBases;
using Sleekflow.Mvc.Tests;
using Sleekflow.Outputs;

namespace Sleekflow.IntelligentHub.Tests.IntegrationTests;

public class ProcessWebPageDocumentCoreIntegrationTests
{
    private const string SleekflowCompanyId = "sample-company-id";

    private readonly List<(string, string)> _fileNames = new ()
    {
        ("sampleTxt.txt", "txt")
    };

    private readonly List<string> _webPageUris = new ()
    {
        "https://docs.sleekflow.io/using-the-platform/commerce/whatsapp-catalog/whatsapp-catalog-settings-mobile"
    };

    private readonly List<string> _loadChunkIds = new ();

    [SetUp]
    public void TestSetUp()
    {
        if (BaseTestHost.IsGithubAction)
        {
            Assert.Ignore("Test requires ShareHub to run in the background");
        }
    }

    [Test, Order(1)]
    public async Task ProcessWebPageDocumentsTest()
    {
        foreach (var (fileName, _) in _fileNames)
        {
            var createPopulateWebPageDocumentFromBlobInput =
                new PopulateWebPageDocumentFromBlob.PopulateWebPageDocumentFromBlobInput(
                    SleekflowCompanyId,
                    SleekflowCompanyId + "/" + fileName,
                    "txt",
                    "https://docs.sleekflow.io/using-the-platform/commerce/whatsapp-catalog/whatsapp-catalog-settings-mobile",
                    "en",
                    new List<string>()
                    {
                        "SplitIntoPages", "SplitIntoChunks", "TransformTableToText", "Translate"
                    });

            var createPopulateWebPageDocumentFromBlobScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createPopulateWebPageDocumentFromBlobInput)
                        .ToUrl("/Documents/PopulateWebPageDocumentFromBlob");
                });

            var createPopulateWebPageDocumentFromBlobOutputOutput =
                await createPopulateWebPageDocumentFromBlobScenarioResult
                    .ReadAsJsonAsync<Output<PopulateWebPageDocumentFromBlob.PopulateWebPageDocumentFromBlobOutput>>();

            var createPopulateWebPageDocumentFromBlobOutput = createPopulateWebPageDocumentFromBlobOutputOutput?.Data;

            Assert.That(createPopulateWebPageDocumentFromBlobOutputOutput, Is.Not.Null);
            Assert.That(createPopulateWebPageDocumentFromBlobOutput, Is.Not.Null);
            Assert.That(createPopulateWebPageDocumentFromBlobOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        }
    }

    [Test, Order(2)]
    public async Task GetAndEditWebPageDocumentTest()
    {
        foreach (var (fileName, _) in _fileNames)
        {
            // GetWebPageDocumentInformation
            var createGetWebPageDocumentInformationInput =
                new GetWebPageDocumentInformation.GetWebPageDocumentInformationInput(
                    SleekflowCompanyId,
                    SleekflowCompanyId + "/" + fileName);

            var createGetWebPageDocumentInformationInputScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createGetWebPageDocumentInformationInput)
                        .ToUrl("/Documents/GetWebPageDocumentInformation");
                });

            var createGetWebPageDocumentInformationOutputOutput =
                await createGetWebPageDocumentInformationInputScenarioResult
                    .ReadAsJsonAsync<Output<GetWebPageDocumentInformation.GetWebPageDocumentInformationOutput>>();

            var createGetWebPageDocumentInformationOutput = createGetWebPageDocumentInformationOutputOutput?.Data;

            Assert.That(createGetWebPageDocumentInformationOutputOutput, Is.Not.Null);
            Assert.That(createGetWebPageDocumentInformationOutput?.Document, Is.Not.Null);
            Assert.That(createGetWebPageDocumentInformationOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            // GetWebDocumentChunks
            var documentId = createGetWebPageDocumentInformationOutput!.Document.Id;
            var createGetWebPageDocumentChunksInput =
                new GetWebPageDocumentChunks.GetWebPageDocumentChunksInput(
                    SleekflowCompanyId,
                    documentId,
                    null,
                    1000);

            var createGetWebPageDocumentChunksInputScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createGetWebPageDocumentChunksInput)
                        .ToUrl("/Documents/GetWebPageDocumentChunks");
                });

            var createGetWebPageDocumentChunksOutputOutput =
                await createGetWebPageDocumentChunksInputScenarioResult
                    .ReadAsJsonAsync<Output<GetWebPageDocumentChunks.GetWebPageDocumentChunksOutput>>();

            var createGetWebPageDocumentChunksOutput = createGetWebPageDocumentChunksOutputOutput?.Data;
            Assert.That(createGetWebPageDocumentChunksOutputOutput, Is.Not.Null);
            Assert.That(createGetWebPageDocumentChunksOutput?.DocumentChunks, Is.Not.Null);
            Assert.That(createGetWebPageDocumentChunksOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            // EditWebDocumentChunks
            var chunkIds = createGetWebPageDocumentChunksOutput!.DocumentChunks.Select(e => e.Id).ToList();

            var editingChunkDtos = new List<EditingChunkDto>();
            for (var i = 0; i < chunkIds.Count; i++)
            {
                if (i % 2 == 0)
                {
                    var editingChunkDto = new EditingChunkDto(
                        chunkIds[i],
                        "modified content2",
                        new List<Category>(),
                        new Dictionary<string, object?>());
                    editingChunkDtos.Add(editingChunkDto);
                }

                _loadChunkIds.Add(chunkIds[i]);
            }

            var createEditWebPageDocumentChunksInput = new EditWebPageDocumentChunks.EditWebPageDocumentChunksInput(
                SleekflowCompanyId,
                documentId,
                editingChunkDtos);

            var createEditWebPageDocumentChunksInputScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createEditWebPageDocumentChunksInput)
                        .ToUrl("/Documents/EditWebPageDocumentChunks");
                });

            var createEditWebPageDocumentChunksOutputOutput =
                await createEditWebPageDocumentChunksInputScenarioResult
                    .ReadAsJsonAsync<Output<EditFileDocumentChunks.EditFileDocumentChunksOutput>>();

            var createEditWebPageDocumentChunksOutput = createEditWebPageDocumentChunksOutputOutput?.Data;
            Assert.That(createEditWebPageDocumentChunksOutputOutput, Is.Not.Null);
            Assert.That(createEditWebPageDocumentChunksOutput?.EditedChunks, Is.Not.Null);
            Assert.That(createEditWebPageDocumentChunksOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            // // load file doc to kb entry
            // var createLoadWebPageDocumentChunksToKnowledgeBaseInput =
            //     new LoadWebPageDocumentChunksToKnowledgeBase.LoadWebPageDocumentChunksToKnowledgeBaseInput(
            //         SleekflowCompanyId,
            //         documentId,
            //         _loadChunkIds);
            //
            // var createLoadWebPageDocumentChunksToKnowledgeBaseScenarioResult = await Application.Host.Scenario(
            //     _ =>
            //     {
            //         _.WithRequestHeader("X-Sleekflow-Record", "true");
            //         _.Post.Json(createLoadWebPageDocumentChunksToKnowledgeBaseInput)
            //             .ToUrl("/KnowledgeBases/LoadWebPageDocumentChunksToKnowledgeBase");
            //     });
            //
            // var createLoadWebPageDocumentChunksToKnowledgeBaseOutputOutput =
            //     await createLoadWebPageDocumentChunksToKnowledgeBaseScenarioResult
            //         .ReadAsJsonAsync<
            //             Output<LoadWebPageDocumentChunksToKnowledgeBase.
            //                 LoadWebPageDocumentChunksToKnowledgeBaseOutput>>();
            //
            // var createLoadWebPageDocumentChunksToKnowledgeBaseOutput =
            //     createLoadWebPageDocumentChunksToKnowledgeBaseOutputOutput?.Data;
            // Assert.That(createLoadWebPageDocumentChunksToKnowledgeBaseOutputOutput, Is.Not.Null);
            // Assert.That(createLoadWebPageDocumentChunksToKnowledgeBaseOutput, Is.Not.Null);
            // Assert.That(createLoadWebPageDocumentChunksToKnowledgeBaseOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        }
    }

    [Test, Order(3)]
    public async Task KnowledgeBaseEntriesTest()
    {
        foreach (var uri in _webPageUris)
        {
            var createGetKnowledgeBaseEntriesInput = new GetKnowledgeBaseEntries.GetKnowledgeBaseEntriesInput(
                SleekflowCompanyId,
                new GetKnowledgeBaseEntriesFilters(
                    uri,
                    KnowledgeBaseSourceTypes.WebPageDocument),
                1000,
                null);

            var createGetKnowledgeBaseEntriesScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createGetKnowledgeBaseEntriesInput)
                        .ToUrl("/KnowledgeBases/GetKnowledgeBaseEntries");
                });

            var createGetKnowledgeBaseEntriesOutputOutput =
                await createGetKnowledgeBaseEntriesScenarioResult
                    .ReadAsJsonAsync<Output<GetKnowledgeBaseEntries.GetKnowledgeBaseEntriesOutput>>();

            var createGetKnowledgeBaseEntriesOutput = createGetKnowledgeBaseEntriesOutputOutput?.Data;

            Assert.That(createGetKnowledgeBaseEntriesOutputOutput, Is.Not.Null);
            Assert.That(createGetKnowledgeBaseEntriesOutput?.KnowledgeBaseEntries, Is.Not.Null);
            Assert.That(createGetKnowledgeBaseEntriesOutputOutput!.HttpStatusCode, Is.EqualTo(200));

            var entryIds = createGetKnowledgeBaseEntriesOutput!.KnowledgeBaseEntries.Select(e => e.Id).ToList();

            foreach (var entryId in entryIds)
            {
                // Get entry by Id
                var createGetKnowledgeBaseEntryInput = new GetKnowledgeBaseEntry.GetKnowledgeBaseEntryInput(
                    SleekflowCompanyId,
                    entryId);

                var createGetKnowledgeBaseEntryScenarioResult = await Application.Host.Scenario(
                    _ =>
                    {
                        _.WithRequestHeader("X-Sleekflow-Record", "true");
                        _.Post.Json(createGetKnowledgeBaseEntryInput)
                            .ToUrl("/KnowledgeBases/GetKnowledgeBaseEntry");
                    });

                var createGetKnowledgeBaseEntryOutputOutput =
                    await createGetKnowledgeBaseEntryScenarioResult
                        .ReadAsJsonAsync<Output<GetKnowledgeBaseEntry.GetKnowledgeBaseEntryOutput>>();

                var createGetKnowledgeBaseEntryOutput = createGetKnowledgeBaseEntryOutputOutput?.Data;

                Assert.That(createGetKnowledgeBaseEntryOutputOutput, Is.Not.Null);
                Assert.That(createGetKnowledgeBaseEntryOutput?.KnowledgeBaseEntry, Is.Not.Null);
                Assert.That(createGetKnowledgeBaseEntryOutputOutput!.HttpStatusCode, Is.EqualTo(200));
            }

            // delete entries
            var createRemoveKnowledgeBaseEntriesInput = new RemoveKnowledgeBaseEntries.RemoveKnowledgeBaseEntriesInput(
                SleekflowCompanyId,
                entryIds);

            var createRemoveKnowledgeBaseEntriesScenarioResult = await Application.Host.Scenario(
                _ =>
                {
                    _.WithRequestHeader("X-Sleekflow-Record", "true");
                    _.Post.Json(createRemoveKnowledgeBaseEntriesInput)
                        .ToUrl("/KnowledgeBases/RemoveKnowledgeBaseEntries");
                });

            var createRemoveKnowledgeBaseEntriesOutputOutput =
                await createRemoveKnowledgeBaseEntriesScenarioResult
                    .ReadAsJsonAsync<Output<RemoveKnowledgeBaseEntries.RemoveKnowledgeBaseEntriesOutput>>();

            var createRemoveKnowledgeBaseEntriesOutput = createRemoveKnowledgeBaseEntriesOutputOutput?.Data;

            Assert.That(createRemoveKnowledgeBaseEntriesOutputOutput, Is.Not.Null);
            Assert.That(createRemoveKnowledgeBaseEntriesOutput?.DeletedEntryIds, Is.Not.Null);
            Assert.That(createRemoveKnowledgeBaseEntriesOutputOutput!.HttpStatusCode, Is.EqualTo(200));
        }
    }
}