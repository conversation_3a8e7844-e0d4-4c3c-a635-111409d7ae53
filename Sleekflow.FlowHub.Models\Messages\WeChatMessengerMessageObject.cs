using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Messages;

/// <summary>
/// Please refer to https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Service_Center_messages.html here.
/// </summary>
public class WeChatMessengerMessageObject
{
    /// <summary>
    /// Message type: text, image, voice, video, music, news, mpnews, msgmenu, miniprogram_page, etc.
    /// </summary>
    [JsonProperty("msgtype")]
    public string MsgType { get; set; }

    /// <summary>
    /// Message content.
    /// </summary>
    [JsonProperty("text")]
    public WeChatTextMessage? Text { get; set; }

    [JsonConstructor]
    public WeChatMessengerMessageObject(string msgType, WeChatTextMessage? text)
    {
        MsgType = msgType;
        Text = text;
    }
}

/// <summary>
/// Text message content for WeChat.
/// </summary>
public class WeChatTextMessage
{
    /// <summary>
    /// Message content.
    /// </summary>
    [JsonProperty("content")]
    public string Content { get; set; }

    [JsonConstructor]
    public WeChatTextMessage(string content)
    {
        Content = content;
    }
}