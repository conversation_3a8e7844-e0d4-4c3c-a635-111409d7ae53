﻿using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Sleekflow.FlowHub.Models.JsonConfigs;

namespace Sleekflow.FlowHub.JsonConfigs;

public static class JsonConfig
{
    public static readonly JsonSerializerSettings DefaultJsonSerializerSettings = new JsonSerializerSettings()
    {
        Converters = new List<JsonConverter>()
        {
            new IsoDateTimeConverter
            {
                DateTimeFormat = "yyyy'-'MM'-'dd'T'HH':'mm':'ss.fff'Z'"
            },
            new ScribanScriptObjectConverter(),
            new SendMessageInputFromToConverter()
        },
        ReferenceLoopHandling = ReferenceLoopHandling.Error,
        DateParseHandling = DateParseHandling.DateTimeOffset,
        DateTimeZoneHandling = DateTimeZoneHandling.Utc,
        MaxDepth = 32,
    };
}