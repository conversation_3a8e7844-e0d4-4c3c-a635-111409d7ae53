using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BusinessBalanceAutoTopUpProfiles;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfile
    : ITrigger<
        UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfile.
        UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput,
        UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfile.
        UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput>
{
    private readonly IBusinessBalanceAutoTopUpProfileService _businessBalanceAutoTopUpProfileService;

    public class UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput : IHasSleekflowCompanyId
    {
        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("business_balance_auto_top_up_profile")]
        public BusinessBalanceAutoTopUpProfileDto BusinessBalanceAutoTopUpProfile { get; set; }

        [JsonProperty("customer_id")]
        public string? CustomerId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("credited_by")]
        public string CreditedBy { get; set; }

        [JsonProperty("credited_by_display_name")]
        public string? CreditedByDisplayName { get; set; }

        [System.ComponentModel.DataAnnotations.Required]
        [JsonProperty("redirect_to_url")]
        public string RedirectToUrl { get; set; }

        [JsonProperty("phone_number")]
        public string? PhoneNumber { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput(
            BusinessBalanceAutoTopUpProfileDto businessBalanceAutoTopUpProfile,
            string sleekflowCompanyId,
            string? sleekflowStaffId,
            List<string> sleekflowStaffTeamIds,
            string? customerId,
            string creditedBy,
            string? creditedByDisplayName,
            string redirectToUrl,
            string? phoneNumber)
        {
            BusinessBalanceAutoTopUpProfile = businessBalanceAutoTopUpProfile;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            CustomerId = customerId;
            CreditedBy = creditedBy;
            CreditedByDisplayName = creditedByDisplayName;
            RedirectToUrl = redirectToUrl;
            PhoneNumber = phoneNumber;
        }
    }

    public class UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput
    {
        [JsonProperty("business_balance_auto_top_up_profile")]
        public BusinessBalanceAutoTopUpProfileDto BusinessBalanceAutoTopUpProfile { get; set; }

        [JsonProperty("payment_url", NullValueHandling = NullValueHandling.Ignore)]
        public string? PaymentUrl { get; set; }

        [JsonConstructor]
        public UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput(
            BusinessBalanceAutoTopUpProfile businessBalanceAutoTopUpProfile,
            string? paymentUrl)
        {
            BusinessBalanceAutoTopUpProfile = new BusinessBalanceAutoTopUpProfileDto(businessBalanceAutoTopUpProfile);
            PaymentUrl = paymentUrl;
        }
    }

    public UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfile(IBusinessBalanceAutoTopUpProfileService businessBalanceAutoTopUpProfileService)
    {
        _businessBalanceAutoTopUpProfileService = businessBalanceAutoTopUpProfileService;
    }

    public async Task<UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput> F(
        UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileInput input)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var result = await _businessBalanceAutoTopUpProfileService.UpsertBusinessBalanceAutoTopUpProfileAsync(
            input.BusinessBalanceAutoTopUpProfile.FacebookBusinessId,
            input.CustomerId,
            input.BusinessBalanceAutoTopUpProfile.MinimumBalance,
            input.BusinessBalanceAutoTopUpProfile.AutoTopUpPlan,
            input.BusinessBalanceAutoTopUpProfile.IsAutoTopUpEnabled,
            input.SleekflowCompanyId,
            sleekflowStaff!);

        string? paymentUrl = null;

        if (result.CustomerId is null)
        {
            // Used for update the payment method for auto top up
            paymentUrl = await _businessBalanceAutoTopUpProfileService
                .GenerateBusinessBalanceAutoTopUpProfilePaymentLinkAsync(
                    input.SleekflowCompanyId,
                    input.BusinessBalanceAutoTopUpProfile.FacebookBusinessId,
                    input.CreditedBy,
                    input.CreditedByDisplayName,
                    input.RedirectToUrl,
                    input.PhoneNumber,
                    input.BusinessBalanceAutoTopUpProfile.AutoTopUpPlan);
        }

        return new UpsertWhatsappCloudApiBusinessBalanceAutoTopUpProfileOutput(result, paymentUrl);
    }
}