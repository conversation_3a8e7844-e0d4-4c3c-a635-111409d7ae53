﻿using System.ComponentModel.DataAnnotations;
using System.Text;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.Exceptions;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Schemas.Utils;

public class SchemaQueryBuilder
{
    public class FilterGroup
    {
        [Required]
        [JsonProperty("filters")]
        public List<ISchemaFilter> Filters { get; set; }

        [JsonConstructor]
        public FilterGroup(
            List<ISchemaFilter> filters)
        {
            Filters = filters;
        }
    }

    public interface ISchemaFilter
    {
        public string FieldName { get; set; }

        public string Operator { get; set; }

        public object? Value { get; set; }
    }

    public class SchemaFilter : ISchemaFilter
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^(=|>|<|>=|<=|!=|contains|arrayContains|in|startsWith)$")]
        [JsonProperty("operator")]
        public string Operator { get; set; }

        [JsonProperty("value")]
        public object? Value { get; set; }

        [JsonConstructor]
        public SchemaFilter(string fieldName, string @operator, object? value)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
        }
    }

    public class PlainSchemaFilter : ISchemaFilter
    {
        public string FieldName { get; set; }

        public string Operator { get; set; }

        public object? Value { get; set; }

        public PlainSchemaFilter(string fieldName, string @operator, object? value)
        {
            FieldName = fieldName;
            Operator = @operator;
            Value = value;
        }
    }

    public class SchemaSort
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^(asc|desc|ASC|DESC)$")]
        [JsonProperty("direction")]
        public string Direction { get; set; }

        [JsonConstructor]
        public SchemaSort(string fieldName, string direction)
        {
            FieldName = fieldName;
            Direction = direction;
        }
    }

    private sealed class Param
    {
        public string Name { get; set; }

        public object? Value { get; set; }

        public Param(string name, object? value)
        {
            Name = name;
            Value = value;
        }
    }

    public interface ISelect
    {
        public string FieldName { get; set; }

        public string? As { get; set; }
    }

    public class Select : ISelect
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [RegularExpression("^[a-zA-Z0-9_\\[\\]\\.:]+$")]
        [ValidateNotEqualToAnyString(
            new[]
            {
                IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
            })]
        [JsonProperty("field_name")]
        public string FieldName { get; set; }

        [JsonProperty("as")]
        public string? As { get; set; }

        [JsonConstructor]
        public Select(string fieldName, string? @as)
        {
            FieldName = fieldName;
            As = @as;
        }
    }

    public class PlainSelect : ISelect
    {
        public string FieldName { get; set; }

        public string? As { get; set; }

        public PlainSelect(string fieldName, string? @as)
        {
            FieldName = fieldName;
            As = @as;
        }
    }

    public static QueryDefinition BuildQueryDef(
        List<ISelect> selects,
        List<FilterGroup> filterGroups,
        List<SchemaSort> sorts,
        string sleekflowCompanyId)
    {
        var selectExpressions = new List<string>()
            {
                Capacity = selects.Count
            }
            .Concat(
                selects.Select(
                    f =>
                    {
                        if (f is PlainSelect)
                        {
                            return $"{f.FieldName} {f.As}";
                        }

                        return $"m[\"{f.FieldName}\"] {f.As}";
                    }))
            .ToList();

        var selectClause = selectExpressions.Any()
            ? "SELECT "
              + string.Join(", ", selectExpressions)
            : "SELECT *";

        var defaultFilterGroups = new List<FilterGroup>
        {
            new FilterGroup(
                new List<ISchemaFilter>
                {
                    new PlainSchemaFilter(
                        IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId,
                        "=",
                        sleekflowCompanyId)
                })
        };

        var (@params, whereClause) = GetWhereClause(
            defaultFilterGroups
                .Concat(filterGroups)
                .ToList());

        var sortClause = sorts.Count == 0
            ? string.Empty
            : "ORDER BY "
              + string.Join(
                  ", ",
                  sorts.Select(
                      f => $"{GetHierarchicalFieldName(f.FieldName)} {f.Direction}"));

        var clauses = new List<string>
            {
                selectClause,
                $"FROM schemas m",
                whereClause,
                sortClause,
            }
            .Where(l => !string.IsNullOrWhiteSpace(l))
            .ToList();

        var queryDefinition = @params
            .Aggregate(
                new QueryDefinition(string.Join("\n", clauses)),
                (qd, param) =>
                {
                    if (param.Value is JArray jArray)
                    {
                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<string>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<long>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        try
                        {
                            return qd.WithParameter(param.Name, jArray.Values<double>().ToList());
                        }
                        catch (Exception)
                        {
                            // skip
                        }

                        throw new SfValidationException(
                            new List<ValidationResult>()
                            {
                                new ValidationResult(
                                    "Invalid parameter value",
                                    new[]
                                    {
                                        param.Name
                                    })
                            });
                    }

                    return qd.WithParameter(param.Name, param.Value);
                });

        return queryDefinition;
    }

    private static (List<Param> Params, string Where) GetWhereClause(
        IReadOnlyCollection<FilterGroup> filterGroups)
    {
        var @params = new List<Param>();

        if (filterGroups.Count <= 0)
        {
            return (@params, string.Empty);
        }

        var stringBuilder = new StringBuilder();
        var i = 0;

        stringBuilder.Append("WHERE ");
        stringBuilder.AppendJoin(
            " AND ",
            filterGroups
                .Where(fg => fg.Filters.Any())
                .Select(
                    fg =>
                    {
                        var filterClauseStrs = fg.Filters.Select(
                            f =>
                            {
                                var fieldName = GetHierarchicalFieldName(f.FieldName);

                                return f.Operator switch
                                {
                                    "contains" =>
                                        $"CONTAINS({fieldName}, {GetSanitizedParamName(f.FieldName, i++)}, true)",
                                    "arrayContains" =>
                                        $"ARRAY_CONTAINS({fieldName}, {GetSanitizedParamName(f.FieldName, i++)}, false)",
                                    "startsWith" =>
                                        $"STARTSWITH({fieldName}, {GetSanitizedParamName(f.FieldName, i++)}, false)",
                                    "in" =>
                                        $"ARRAY_CONTAINS({GetSanitizedParamName(f.FieldName, i++)}, {fieldName}, false)",
                                    _ =>
                                        $"{fieldName} {f.Operator} {GetSanitizedParamName(f.FieldName, i++)}"
                                };
                            });

                        var sb = new StringBuilder();
                        sb.Append('(');
                        sb.AppendJoin(" OR ", filterClauseStrs);
                        sb.Append(')');

                        return sb.ToString();
                    }));

        i = 0;

        @params.AddRange(
            filterGroups
                .SelectMany(fg => fg.Filters)
                .Select(f => new Param(GetSanitizedParamName(f.FieldName, i++), f.Value)));

        return (@params, stringBuilder.ToString());
    }

    private static string GetSanitizedParamName(string name, int index)
    {
        return "@param_"
               + name
                   .Replace("[", "_")
                   .Replace("]", "_")
                   .Replace(".", "_")
                   .Replace(":", "_")
               + "_" + index;
    }

    private static string GetHierarchicalFieldName(string fieldNameExpression)
    {
        var fieldNames = fieldNameExpression.Split('.');

        return 'm' + string.Concat(fieldNames.Select(f => $"[\"{f}\"]"));
    }
}