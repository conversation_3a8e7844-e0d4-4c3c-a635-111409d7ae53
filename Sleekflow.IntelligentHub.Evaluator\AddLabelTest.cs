using System.Globalization;
using System.Reflection;
using System.Text.RegularExpressions;
using CsvHelper;
using CsvHelper.Configuration.Attributes;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Evaluator.AddLabel;

namespace Sleekflow.IntelligentHub.Evaluator;

[TestFixture]
[Parallelizable(ParallelScope.Children)]
public partial class AddLabelTest
{
    private static readonly List<(string TestId, string Scenario, AddLabelTestCase TestCase, AddLabelTestResult[] Results)> AllTestResults = [];

    [TestCaseSource(nameof(GetTestCases))]
    [Parallelizable(ParallelScope.All)]
    public async Task EvaluateAddLabel(AddLabelTestCase testCase)
    {
        var testId = Guid.NewGuid();
        var fixture = new AddLabelFixture();
        var results = await fixture.EvaluateAsync(testCase, CancellationToken.None);

        var resultsWithScores = results.Select(r => new
        {
            Result = r,
        }).ToArray();

        AllTestResults.Add((testId.ToString(), testCase.Scenario, testCase, resultsWithScores.Select(r =>
            new AddLabelTestResult(
                r.Result.MethodName,
                r.Result.Answer,
                r.Result.ElapsedMilliseconds)).ToArray()));

        Console.WriteLine("Chat messages scenarios:");
        foreach (var chatMessageContent in testCase.ChatMessageContents)
        {
            Console.WriteLine($"{chatMessageContent.Role}: {chatMessageContent.Content}");
        }

        Console.WriteLine("----------------------- Best Agent ---------------------");

        var bestOutput = resultsWithScores.OrderByDescending(r => r.Result.Answer?.Hashtag != null).First();
        Console.WriteLine($"Best Scoring Agent: {bestOutput.Result.MethodName}");

        Console.WriteLine("----------------------- Scores ------------------------");

        foreach (var result in resultsWithScores.OrderByDescending(r => r.Result.Answer?.Hashtag != null))
        {
            Console.WriteLine($"Agent Name: {result.Result.MethodName}");
            Console.WriteLine($"Elapsed Time: {result.Result.ElapsedMilliseconds} ms");

            Console.WriteLine("-------------------------------------------------------");

            Console.WriteLine(
                $"Expected Answer: {JsonConvert.SerializeObject(testCase.ExpectedAddLabelResult, Formatting.Indented)}");
            Console.WriteLine(
                $"Generated Answer: {JsonConvert.SerializeObject(result.Result.Answer, Formatting.Indented)}");

            Console.WriteLine("-------------------------------------------------------");
        }
    }

    private string GetRootPath()
    {
        var exePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
        var appPathMatcher = new Regex(@"(?<!file)[A-Za-z]:\\+[\S\s]*?(?=\\+bin)");
        return appPathMatcher.Match(exePath).Value;
    }

    public static IEnumerable<AddLabelTestCase> GetTestCases()
    {
        return AddLabelEvaluatorTest.GetTestCases();
    }
}
