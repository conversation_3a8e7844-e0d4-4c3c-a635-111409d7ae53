using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class RefreshWabaPhoneNumberProductCatalogSetting
    : ITrigger<
        RefreshWabaPhoneNumberProductCatalogSetting.RefreshWabaPhoneNumberProductCatalogSettingInput,
        RefreshWabaPhoneNumberProductCatalogSetting.RefreshWabaPhoneNumberProductCatalogSettingOutput>
{
    private readonly ILogger<RefreshWabaPhoneNumberProductCatalogSetting> _logger;
    private readonly IProductCatalogService _productCatalogService;

    public RefreshWabaPhoneNumberProductCatalogSetting(
        ILogger<RefreshWabaPhoneNumberProductCatalogSetting> logger,
        IProductCatalogService productCatalogService)
    {
        _logger = logger;
        _productCatalogService = productCatalogService;
    }

    public class RefreshWabaPhoneNumberProductCatalogSettingInput : IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("phone_number_id")]
        public string PhoneNumberId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public RefreshWabaPhoneNumberProductCatalogSettingInput(
            string sleekflowCompanyId,
            string wabaId,
            string phoneNumberId,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            PhoneNumberId = phoneNumberId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class RefreshWabaPhoneNumberProductCatalogSettingOutput
    {
        [JsonProperty("waba")]
        public WabaDto Waba { get; set; }

        [JsonProperty("waba_phone_number")]
        public WabaPhoneNumberDto WabaPhoneNumber { get; set; }

        [JsonConstructor]
        public RefreshWabaPhoneNumberProductCatalogSettingOutput(WabaDto waba, WabaPhoneNumberDto wabaPhoneNumber)
        {
            Waba = waba;
            WabaPhoneNumber = wabaPhoneNumber;
        }
    }

    public async Task<RefreshWabaPhoneNumberProductCatalogSettingOutput> F(
        RefreshWabaPhoneNumberProductCatalogSettingInput connectWabasProductCatalogsInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            connectWabasProductCatalogsInput.SleekflowStaffId,
            connectWabasProductCatalogsInput.SleekflowStaffTeamIds);

        var waba = await _productCatalogService.RefreshWabaProductCatalogAsync(
            connectWabasProductCatalogsInput.SleekflowCompanyId,
            connectWabasProductCatalogsInput.WabaId,
            sleekflowStaff);

        var wabaDto = new WabaDto(waba);

        var wabaPhoneNumber = await _productCatalogService.RetrieveWabaPhoneNumberWhatsappCommerceSettingAsync(
            connectWabasProductCatalogsInput.SleekflowCompanyId,
            connectWabasProductCatalogsInput.WabaId,
            connectWabasProductCatalogsInput.PhoneNumberId,
            sleekflowStaff,
            true);

        var wabaPhoneNumberDto = new WabaPhoneNumberDto(wabaPhoneNumber);

        return new RefreshWabaPhoneNumberProductCatalogSettingOutput(wabaDto, wabaPhoneNumberDto);
    }
}