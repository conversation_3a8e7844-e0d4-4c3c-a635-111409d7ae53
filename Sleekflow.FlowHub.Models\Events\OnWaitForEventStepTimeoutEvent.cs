using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWaitForEventStepTimeoutEvent
{
    public string StateSubscriptionId { get; set; }

    public string StateId { get; set; }

    [JsonConstructor]
    public OnWaitForEventStepTimeoutEvent(
        string stateSubscriptionId,
        string stateId)
    {
        StateSubscriptionId = stateSubscriptionId;
        StateId = stateId;
    }
}