using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs.Actions;

public class LeadScoreCriterionDto
{
    [JsonProperty("weight")]
    public int Weight { get; set; }

    [JsonProperty("description")]
    public string Description { get; set; }

    [JsonConstructor]
    public LeadScoreCriterionDto(int weight, string description)
    {
        Weight = weight;
        Description = description;
    }

    public LeadScoreCriterionDto(LeadScoreCriterion criterion)
    {
        Weight = criterion.Weight;
        Description = criterion.Description;
    }
} 