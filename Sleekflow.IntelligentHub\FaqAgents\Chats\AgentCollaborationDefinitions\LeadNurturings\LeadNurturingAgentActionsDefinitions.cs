using MassTransit;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Events; // Assuming the new event is here
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Services;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public interface ILeadNurturingAgentActionsDefinitions
{
    Task HandleLeadClassifierAction(string content, Kernel kernel);

    Task HandleFollowUpAction(string content, Kernel kernel, string groupChatIdStr);
}

public class LeadNurturingAgentActionsDefinitions : ILeadNurturingAgentActionsDefinitions, IScopedService
{
    private readonly ILogger<LeadNurturingAgentActionsDefinitions> _logger;
    private readonly ISleekflowToolsPlugin _sleekflowToolsPlugin;
    private readonly IBus _bus; // Changed from IPublishEndpoint to IBus
    private readonly IUserInteractionTrackingService _userInteractionTracker;

    public LeadNurturingAgentActionsDefinitions(
        ILogger<LeadNurturingAgentActionsDefinitions> logger,
        ISleekflowToolsPlugin sleekflowToolsPlugin,
        IBus bus, // Changed from IPublishEndpoint to IBus
        IUserInteractionTrackingService userInteractionTracker)
    {
        _logger = logger;
        _sleekflowToolsPlugin = sleekflowToolsPlugin;
        _bus = bus; // Changed from IPublishEndpoint to IBus
        _userInteractionTracker = userInteractionTracker;
    }

    public async Task HandleLeadClassifierAction(string content, Kernel kernel)
    {
        try
        {
            if (!string.IsNullOrEmpty(content))
            {
                var leadClassifierAgentResponse =
                    JsonConvert.DeserializeObject<LeadNurturingAgentDefinitions.LeadClassifierAgentResponse>(content)!;

                Console.WriteLine(
                    $"LeadScore: {leadClassifierAgentResponse.Score} classified as [{leadClassifierAgentResponse.Classification}] with reasoning {leadClassifierAgentResponse.Reasoning}");

                await _sleekflowToolsPlugin.AddLeadScoreCustomObject(
                    kernel,
                    leadClassifierAgentResponse.Classification,
                    leadClassifierAgentResponse.Reasoning,
                    leadClassifierAgentResponse.Score);
            }
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            _logger.LogError(e, "Error processing LeadClassifierAgent response");
        }
    }

    public async Task HandleFollowUpAction(string content, Kernel kernel, string groupChatIdStr)
    {
        try
        {
            if (string.IsNullOrEmpty(content))
            {
                _logger.LogWarning("FollowUpAgent response content is empty.");
                return;
            }

            var followUpAgentResponse =
                JsonConvert.DeserializeObject<FollowUpAgentResponse>(content);

            if (followUpAgentResponse == null)
            {
                _logger.LogWarning($"Failed to deserialize FollowUpAgent response: {content}");
                return;
            }

            _logger.LogInformation($"FollowUpAgent output: {content}");

            if (followUpAgentResponse.ShouldFollowup && !string.IsNullOrEmpty(followUpAgentResponse.FollowupMessage))
            {
                var delayMinutes = followUpAgentResponse.FollowupDelayMinutes ?? 0;
                if (delayMinutes < 0 || delayMinutes > 60)
                {
                    _logger.LogWarning($"Invalid FollowUpDelayMinutes: {delayMinutes}. Defaulting to 0.");
                    delayMinutes = 0;
                }

                var scheduledTimeUtc = DateTime.UtcNow.AddMinutes(delayMinutes);
                var groupChatIdParsed = GroupChatId.Parse(groupChatIdStr);

                // Extract context data from kernel for proper follow-up configuration
                var stateId = kernel.Data.TryGetValue(KernelDataKeys.STATE_ID, out var stateIdObj) ? stateIdObj as string : null;
                var contactId = kernel.Data.TryGetValue(KernelDataKeys.CONTACT_ID, out var contactIdObj) ? contactIdObj as string : null;
                var agentId = kernel.Data.TryGetValue(KernelDataKeys.AGENT_ID, out var agentIdObj) ? agentIdObj as string : null;

                // Serialize tools configuration for later use
                string? toolsConfigJson = null;
                if (kernel.Data.TryGetValue(KernelDataKeys.TOOLS_CONFIG, out var toolsConfigObj) && toolsConfigObj != null)
                {
                    try
                    {
                        toolsConfigJson = JsonConvert.SerializeObject(toolsConfigObj);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to serialize ToolsConfig for follow-up");
                    }
                }

                string? leadNurturingToolsConfigJson = null;
                if (kernel.Data.TryGetValue(KernelDataKeys.LEAD_NURTURING_TOOLS_CONFIG, out var leadNurturingToolsObj) && leadNurturingToolsObj != null)
                {
                    try
                    {
                        leadNurturingToolsConfigJson = JsonConvert.SerializeObject(leadNurturingToolsObj);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to serialize LeadNurturingTools for follow-up");
                    }
                }

                // Get the current user message to include for comparison later
                string? originalUserMessage = null;
                try
                {
                    originalUserMessage = await _userInteractionTracker.GetLastUserMessageAsync(
                        groupChatIdParsed.CompanyId,
                        groupChatIdParsed.ObjectId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to get last user message for follow-up comparison");
                }

                var scheduleFollowUpEvent = new ScheduleFollowUpMessage(
                    groupChatId: groupChatIdStr,
                    followUpMessage: followUpAgentResponse.FollowupMessage,
                    companyId: groupChatIdParsed.CompanyId,
                    objectId: groupChatIdParsed.ObjectId,
                    scheduledTimeUtc: scheduledTimeUtc,
                    stateId: stateId,
                    contactId: contactId,
                    agentId: agentId,
                    toolsConfigJson: toolsConfigJson,
                    leadNurturingToolsConfigJson: leadNurturingToolsConfigJson,
                    originalUserMessage: originalUserMessage
                );

                // Using MassTransit to schedule the message via IBus.CreateMessageScheduler()
                var messageScheduler = _bus.CreateMessageScheduler();
                await messageScheduler.SchedulePublish(
                    scheduledTimeUtc,
                    scheduleFollowUpEvent);

                _logger.LogInformation(
                    $"Scheduled follow-up message for GroupChatId: {groupChatIdStr} at {scheduledTimeUtc}. Message: {followUpAgentResponse.FollowupMessage}");
            }
        }
        catch (JsonException jsonEx)
        {
            _logger.LogError(jsonEx, $"Error deserializing FollowUpAgent response: {content}");
        }
        catch (Exception e)
        {
            _logger.LogError(e, $"Error processing FollowUpAgent response: {content}");
        }
    }

    // Define a local record for deserialization if not already defined elsewhere
    // This should ideally be in a shared Models location if used by other agents/services
    private record FollowUpAgentResponse(
        [property: JsonProperty("agent_name")]
        string AgentName,
        [property: JsonProperty("reasoning")]
        string Reasoning,
        [property: JsonProperty("should_followup")]
        bool ShouldFollowup,
        [property: JsonProperty("followup_delay_minutes")]
        int? FollowupDelayMinutes,
        [property: JsonProperty("followup_type")]
        string? FollowupType,
        [property: JsonProperty("followup_message")]
        string? FollowupMessage
    );
}