using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.Providers;

[TriggerGroup("Providers")]
public class InitProviderV2 : ITrigger
{
    private readonly IProviderSelector _providerSelector;
    private readonly IProviderConfigService _providerConfigService;

    public InitProviderV2(
        IProviderSelector providerSelector,
        IProviderConfigService providerConfigService)
    {
        _providerSelector = providerSelector;
        _providerConfigService = providerConfigService;
    }

    public class InitProviderV2Input : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [Required]
        [JsonProperty("success_url")]
        public string SuccessUrl { get; set; }

        [Required]
        [JsonProperty("failure_url")]
        public string FailureUrl { get; set; }

        [Required]
        [JsonProperty("default_region_code")]
        public string DefaultRegionCode { get; set; }

        [ValidateObject]
        [JsonProperty("additional_details")]
        public Dictionary<string, object?>? AdditionalDetails { get; set; }

        [JsonConstructor]
        public InitProviderV2Input(
            string sleekflowCompanyId,
            string providerName,
            string successUrl,
            string failureUrl,
            string defaultRegionCode,
            Dictionary<string, object?>? additionalDetails)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ProviderName = providerName;
            SuccessUrl = successUrl;
            FailureUrl = failureUrl;
            DefaultRegionCode = defaultRegionCode;
            AdditionalDetails = additionalDetails;
        }
    }

    public class InitProviderV2Output
    {
        [JsonProperty("provider_name")]
        public string ProviderName { get; set; }

        [JsonProperty("context")]
        public dynamic Context { get; set; }

        [JsonConstructor]
        public InitProviderV2Output(string providerName, object context)
        {
            ProviderName = providerName;
            Context = context;
        }
    }

    public async Task<InitProviderV2Output> F(
        InitProviderV2Input initProviderV2Input)
    {
        var providerService = _providerSelector.GetProviderService(initProviderV2Input.ProviderName);

        await _providerConfigService.InitProviderConfigAsync(
            initProviderV2Input.SleekflowCompanyId,
            initProviderV2Input.ProviderName,
            initProviderV2Input.DefaultRegionCode,
            initProviderV2Input.AdditionalDetails,
            null);

        var initProviderOutput = await providerService.InitProviderAsync(
            initProviderV2Input.SleekflowCompanyId,
            initProviderV2Input.SuccessUrl,
            initProviderV2Input.FailureUrl,
            initProviderV2Input.AdditionalDetails);

        return new InitProviderV2Output(initProviderOutput.ProviderName, initProviderOutput.Context);
    }
}