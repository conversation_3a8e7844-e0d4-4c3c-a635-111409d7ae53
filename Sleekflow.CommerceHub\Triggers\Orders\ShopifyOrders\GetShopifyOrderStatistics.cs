using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders.ShopifyOrders;
using Sleekflow.CommerceHub.Orders.ShopifyOrders;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Orders.ShopifyOrders;

[TriggerGroup(ControllerNames.ShopifyOrders)]
public class GetShopifyOrderStatistics
    : ITrigger<
        GetShopifyOrderStatistics.GetShopifyOrderStatisticsInput,
        GetShopifyOrderStatistics.GetShopifyOrderStatisticsOutput>
{
    private readonly IShopifyOrderService _shopifyOrderService;

    public GetShopifyOrderStatistics(
        IShopifyOrderService shopifyOrderService)
    {
        _shopifyOrderService = shopifyOrderService;
    }

    public class GetShopifyOrderStatisticsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty("sleekflow_staff_team_id")]
        public string? SleekflowStaffTeamId { get; set; }

        [JsonProperty("conversion_status")]
        public string? ConversionStatus { get; set; }

        [Required]
        [JsonProperty("from_date_time")]
        public DateTimeOffset FromDateTime { get; set; }

        [Required]
        [JsonProperty("to_date_time")]
        public DateTimeOffset ToDateTime { get; set; }

        [JsonConstructor]
        public GetShopifyOrderStatisticsInput(
            string sleekflowCompanyId,
            string? sleekflowStaffId,
            string? sleekflowStaffTeamId,
            string? conversionStatus,
            DateTimeOffset fromDateTime,
            DateTimeOffset toDateTime)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamId = sleekflowStaffTeamId;
            ConversionStatus = conversionStatus;
            FromDateTime = fromDateTime;
            ToDateTime = toDateTime;
        }
    }

    public class GetShopifyOrderStatisticsOutput : ShopifyOrderStatistics
    {
        [JsonConstructor]
        public GetShopifyOrderStatisticsOutput(
            long count,
            decimal totalPrice)
            : base(
                count,
                totalPrice)
        {
        }
    }

    public async Task<GetShopifyOrderStatisticsOutput> F(
        GetShopifyOrderStatisticsInput getShopifyOrderStatisticsInput)
    {
        var shopifyOrderStatistics = await _shopifyOrderService.GetShopifyOrderStatisticsAsync(
            getShopifyOrderStatisticsInput.SleekflowCompanyId,
            getShopifyOrderStatisticsInput.SleekflowStaffId,
            getShopifyOrderStatisticsInput.SleekflowStaffTeamId,
            getShopifyOrderStatisticsInput.ConversionStatus,
            getShopifyOrderStatisticsInput.FromDateTime,
            getShopifyOrderStatisticsInput.ToDateTime);

        return new GetShopifyOrderStatisticsOutput(
            shopifyOrderStatistics.Count,
            shopifyOrderStatistics.TotalPrice);
    }
}