using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateTicketStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-ticket";

    [Required]
    [JsonProperty("message_origin_channel")]
    public string MessageOriginChannel { get; set; }

    [JsonProperty("channel_type")]
    public string ChannelType { get; set; }

    [JsonProperty("channel_identity_id")]
    public string ChannelIdentityId { get; set; }

    [Required]
    [JsonProperty("title__expr")]
    public string TitleExpr { get; set; }

    [Required]
    [JsonProperty("due_date_setting")]
    public string DueDateSetting { get; set; }

    [JsonProperty("due_date_value")]
    public int DueDateValue { get; set; }

    [JsonProperty("due_date_value_unit")]
    public string DueDateValueUnit { get; set; }

    [JsonProperty("type_id")]
    public string? TypeId { get; set; }

    [Required]
    [JsonProperty("priority_id")]
    public string PriorityId { get; set; }

    [JsonProperty("strategy")]
    public string Strategy { get; set; }

    [JsonProperty("team_id")]
    public string? TeamId { get; set; }

    [JsonProperty("staff_id")]
    public string? StaffId { get; set; }

    [JsonProperty("description__expr")]
    public string DescriptionExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ticketing;

    [JsonConstructor]
    public CreateTicketStepArgs(
        string massageOriginChannel,
        string channelType,
        string channelIdentityId,
        string titleExpr,
        string dueDateSetting,
        int dueDateValue,
        string dueDateValueUnit,
        string priorityId,
        string strategy,
        string? teamId,
        string? staffId,
        string descriptionExpr)
    {
        MessageOriginChannel = massageOriginChannel;
        ChannelType = channelType;
        ChannelIdentityId = channelIdentityId;
        TitleExpr = titleExpr;
        DueDateSetting = dueDateSetting;
        DueDateValue = dueDateValue;
        DueDateValueUnit = dueDateValueUnit;
        PriorityId = priorityId;
        Strategy = strategy;
        TeamId = teamId;
        StaffId = staffId;
        DescriptionExpr = descriptionExpr;
    }

    public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
    {
        switch (Strategy)
        {
            case CreateTicketV2StepArgsStrategy.Specific_Staff
                when string.IsNullOrEmpty(StaffId):
            {
                yield return new ValidationResult(
                    $"StaffId must be provided when strategy is {Strategy}",
                    new[]
                    {
                        nameof(StaffId)
                    });

                break;
            }

            case CreateTicketV2StepArgsStrategy.Team_And_RoundRobbin_All_Staffs_In_Team
                when string.IsNullOrWhiteSpace(TeamId):
            {
                yield return new ValidationResult(
                    $"TeamId must be provided when strategy is {Strategy}",
                    new[]
                    {
                        nameof(TeamId)
                    });

                break;
            }
        }
    }

    public static class CreateTicketV2StepArgsStrategy
    {
#pragma warning disable SA1310`
        public const string Specific_Staff = "Specific_Staff";
        public const string Company_RoundRobbin_All_Staffs = "Company_RoundRobbin_All_Staffs";
        public const string Team_And_RoundRobbin_All_Staffs_In_Team = "Team_And_RoundRobbin_All_Staffs_In_Team";
#pragma warning restore SA1310
    }
}