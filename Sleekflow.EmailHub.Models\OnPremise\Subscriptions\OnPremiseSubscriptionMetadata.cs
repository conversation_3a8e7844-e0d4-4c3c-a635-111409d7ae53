using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Subscriptions;

namespace Sleekflow.EmailHub.Models.OnPremise.Subscriptions;

public class OnPremiseSubscriptionMetadata : EmailSubscriptionMetadata
{
    [JsonProperty("start_index")]
    public int StartIndex { get; set; }

    [JsonConstructor]
    public OnPremiseSubscriptionMetadata(int startIndex)
        : base(ProviderNames.OnPremise, ProviderNames.OnPremise)
    {
        StartIndex = startIndex;
    }
}