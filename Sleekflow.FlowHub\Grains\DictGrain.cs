namespace Sleekflow.FlowHub.Grains;

public interface IDictGrain : IPersistentDictionaryGrain
{
}

public class DictGrain : PersistentDictionaryGrainBase, IDictGrain
{
    public DictGrain(
        [PersistentState("dict", "dictStore")] // Keep the original name for consistency and backward compatibility
        IPersistentState<Dictionary<string, object?>> state)
        : base(state)
    {
    }

    // Implement or override methods specific to IDictGrain here if needed
}