﻿using Newtonsoft.Json;

namespace Sleekflow.Models.CrmHubToFlowHubMigrations;

public class MigrateCrmHubSyncConfigDto
{
    [JsonProperty("filter_groups")]
    public List<MigrateCrmHubSyncConfigFilterGroupDto> FilterGroups { get; set; }

    [JsonProperty("interval")]
    public int Interval { get; set; }

    [JsonProperty("sync_mode")]
    public string? SyncMode { get; set; }

    [JsonConstructor]
    public MigrateCrmHubSyncConfigDto(
        List<MigrateCrmHubSyncConfigFilterGroupDto> filterGroups,
        int interval,
        string? syncMode)
    {
        FilterGroups = filterGroups;
        Interval = interval;
        SyncMode = syncMode;
    }
}