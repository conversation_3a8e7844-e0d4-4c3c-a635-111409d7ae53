﻿using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Models.UserMappingConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Hubspot.UserMappingConfigs;

public interface IHubspotUserMappingConfigService
{
    Task<HubspotUserMappingConfig> GetAsync(
        string sleekflowCompanyId,
        string connectionId);

    Task<HubspotUserMappingConfig> GetByIdAsync(
        string id,
        string sleekflowCompanyId);

    Task<HubspotUserMappingConfig> CreateAndGetAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings);

    Task<HubspotUserMappingConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings);

    Task ClearAsync(string connectionId, string sleekflowCompanyId);
}

public class HubspotUserMappingConfigService : ISingletonService, IHubspotUserMappingConfigService
{
    private readonly IHubspotUserMappingConfigRepository _hubspotUserMappingConfigRepository;
    private readonly IIdService _idService;

    public HubspotUserMappingConfigService(
        IHubspotUserMappingConfigRepository hubspotUserMappingConfigRepository,
        IIdService idService)
    {
        _hubspotUserMappingConfigRepository = hubspotUserMappingConfigRepository;
        _idService = idService;
    }

    public async Task<HubspotUserMappingConfig> GetAsync(
        string sleekflowCompanyId,
        string connectionId)
    {
        var userMappingConfig = (await _hubspotUserMappingConfigRepository.GetObjectsAsync(
            c => c.SleekflowCompanyId == sleekflowCompanyId
                 && c.ConnectionId == connectionId)).FirstOrDefault();

        if (userMappingConfig is null)
        {
            throw new SfNotFoundObjectException(connectionId, sleekflowCompanyId);
        }

        return userMappingConfig;
    }

    public async Task<HubspotUserMappingConfig> GetByIdAsync(
        string id,
        string sleekflowCompanyId)
    {
        return await _hubspotUserMappingConfigRepository.GetAsync(id, sleekflowCompanyId);
    }

    public async Task<HubspotUserMappingConfig> CreateAndGetAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<UserMapping>? userMappings)
    {
        var userMappingConfig = new HubspotUserMappingConfig(
            _idService.GetId("HubspotUserMappingConfig"),
            sleekflowCompanyId,
            connectionId,
            userMappings);

        return await _hubspotUserMappingConfigRepository.CreateAndGetAsync(
            userMappingConfig,
            sleekflowCompanyId);
    }

    public async Task<HubspotUserMappingConfig> PatchAndGetAsync(
        string id,
        string sleekflowCompanyId,
        List<UserMapping>? userMappings)
    {
        return await _hubspotUserMappingConfigRepository.PatchAndGetAsync(
            id,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/user_mappings", userMappings)
            });
    }

    public async Task ClearAsync(string connectionId, string sleekflowCompanyId)
    {
        await foreach (var config in _hubspotUserMappingConfigRepository.GetObjectEnumerableAsync(
                           c =>
                               c.SysTypeName == "UserMappingConfig"
                               && c.ConnectionId == connectionId
                               && c.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _hubspotUserMappingConfigRepository.DeleteAsync(
                config.Id,
                config.SleekflowCompanyId);
        }
    }
}