using Google.Type;
using Newtonsoft.Json;
using DateTime = System.DateTime;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs;

public class SystemAuditLogsFilters
{
    [JsonProperty("types")]
    public List<string>? Types { get; set; }

    [JsonProperty("sleekflow_user_profile_id")]
    public string? SleekflowUserProfileId { get; set; }

    [JsonProperty("sleekflow_staff_id")]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty("from_created_time")]
    public DateTimeOffset? FromCreatedTime { get; set; }

    [JsonProperty("to_created_time")]
    public DateTimeOffset? ToCreatedTime { get; set; }

    [JsonConstructor]
    public SystemAuditLogsFilters(
        List<string>? types,
        string? sleekflowUserProfileId,
        string? sleekflowStaffId,
        DateTimeOffset? fromCreatedTime,
        DateTimeOffset? toCreatedTime)
    {
        Types = types;
        SleekflowUserProfileId = sleekflowUserProfileId;
        SleekflowStaffId = sleekflowStaffId;
        FromCreatedTime = fromCreatedTime;
        ToCreatedTime = toCreatedTime;
    }
}