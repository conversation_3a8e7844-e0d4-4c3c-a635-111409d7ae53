using Microsoft.Extensions.DependencyInjection;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments.Ingestion;

namespace Sleekflow.IntelligentHub.Tests.Documents.WebsiteDocuments.Ingestion;

[TestFixture]
[TestOf(typeof(WebsiteKnowledgeSource))]
public class WebsiteKnowledgeSourceTest
{
    private static readonly string[] TestUrls =
    [
        "https://example.com/",
        "https://sleekflow.io/",
        "https://help.sleekflow.io/en_US/whatsapp/pricing",
        "https://tanstack.com/",
        "https://www.egltours.com/website/home",
        "https://mybeame.com/beame-student-discount",
        "https://www.otandp.com/body-check/"
    ];

    [TestCaseSource(nameof(TestUrls))]
    [Parallelizable(ParallelScope.Children)]
    public async Task WebsiteKnowledgeSourceIngestTest(string url)
    {
        using var scope = Application.Host.Services.CreateScope();
        var websiteKnowledgeSource = scope.ServiceProvider.GetRequiredService<IWebsiteKnowledgeSource>();

        var markdown = await websiteKnowledgeSource.Ingest(url);

        Assert.That(markdown, Is.Not.Empty);
    }
}