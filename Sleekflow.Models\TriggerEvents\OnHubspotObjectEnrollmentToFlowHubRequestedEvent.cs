﻿using Newtonsoft.Json;

namespace Sleekflow.Models.TriggerEvents;

public class OnHubspotObjectEnrollmentToFlowHubRequestedEvent
{
    public DateTimeOffset CreatedAt { get; set; }

    public string SleekflowCompanyId { get; set; }

    public string HubspotConnectionId { get; set; }

    public string ObjectId { get; set; }

    public string ObjectType { get; set; }

    public bool IsCustomObject { get; set; }

    public Dictionary<string, object?> ObjectDict { get; set; }

    public string FlowHubWorkflowId { get; set; }

    public string FlowHubWorkflowVersionedId { get; set; }

    [JsonConstructor]
    public OnHubspotObjectEnrollmentToFlowHubRequestedEvent(
        DateTimeOffset createdAt,
        string sleekflowCompanyId,
        string hubspotConnectionId,
        string objectId,
        string objectType,
        bool isCustomObject,
        Dictionary<string, object?> objectDict,
        string flowHubWorkflowId,
        string flowHubWorkflowVersionedId)
    {
        CreatedAt = createdAt;
        SleekflowCompanyId = sleekflowCompanyId;
        HubspotConnectionId = hubspotConnectionId;
        ObjectId = objectId;
        ObjectType = objectType;
        IsCustomObject = isCustomObject;
        ObjectDict = objectDict;
        FlowHubWorkflowId = flowHubWorkflowId;
        FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
    }
}