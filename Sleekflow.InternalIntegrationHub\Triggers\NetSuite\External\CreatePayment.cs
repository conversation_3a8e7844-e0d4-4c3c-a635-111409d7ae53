using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.**********************.Constants;
using Sleekflow.**********************.Models.Constants;
using Sleekflow.**********************.Models.NetSuite.Integrations;
using Sleekflow.**********************.TravisBackend;

namespace Sleekflow.**********************.Triggers.NetSuite.External;

[TriggerGroup(ControllerNames.External, $"{BasePaths.NetSuite}")]
public class CreatePayment
    : ITrigger<CreatePayment.CreatePaymentInput,
        CreatePayment.CreatePaymentOutput>
{
    private readonly ITravisBackendService _travisBackendService;

    public CreatePayment(ITravisBackendService travisBackendService)
    {
        _travisBackendService = travisBackendService;
    }

    public class CreatePaymentInput
    {
        [Required]
        [Validations.ValidateArray]
        [JsonProperty("payments")]
        public List<CreatePaymentRequest> CreatePaymentRequests { get; set; }


        [JsonConstructor]
        public CreatePaymentInput(
            List<CreatePaymentRequest> createPaymentRequests)
        {
            CreatePaymentRequests = createPaymentRequests;
        }
    }

    public class CreatePaymentOutput
    {
    }

    public async Task<CreatePaymentOutput> F(CreatePaymentInput input)
    {
        var requests = input.CreatePaymentRequests;
        var result = await _travisBackendService.CreatePaymentRecordAsync(requests);
        return new CreatePaymentOutput();
    }
}