using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;
using Sleekflow.CommerceHub.Vtex.Helpers;

namespace Sleekflow.CommerceHub.Tests.Vtex
{
    [TestFixture]
    public class VtexOrderDtoExtensionTests
    {
        [Test]
        public void ToVtexOrderOverview_ShouldConvertFromJson_Successfully()
        {
            // Arrange
            var jsonInput = """
{
    "orderId": "1530090500021-01",
    "origin": "Marketplace",
    "status": "ready-for-handling",
    "workflowIsInError": false,
    "value": 10600,
    "creationDate": "2025-05-06T09:02:47.9348585+00:00",
    "lastChange": "2025-05-06T09:32:58.2052451+00:00",
    "orderGroup": "1530090500021",
    "hostname": "sandboxsleekflo16",
    "isCompleted": true,
    "orderFormId": "509be71dfec94531a017113c5aa6496c",
    "allowCancellation": true,
    "isCheckedIn": false,
    "authorizedDate": "2025-05-06T09:02:51.5682132+00:00",
    "invoicedDate": null,
    "cancelReason": null,
    "totals": [
        {
            "id": "Items",
            "name": "Items Total",
            "value": 10100
        },
        {
            "id": "Discounts",
            "name": "Discounts Total",
            "value": 0
        },
        {
            "id": "Shipping",
            "name": "Shipping Total",
            "value": 500
        },
        {
            "id": "Tax",
            "name": "Tax Total",
            "value": 0
        }
    ],
    "sellers": [
        {
            "id": "1",
            "name": "Sleekflow DEV",
            "logo": "",
            "fulfillmentEndpoint": "http://fulfillment.vtexcommerce.com.br/api/fulfillment?an=sandboxsleekflo16"
        }
    ],
    "clientPreferencesData": {
        "locale": "en-US",
        "optinNewsLetter": false
    },
    "itemMetadata": {
        "Items": [
            {
                "Id": "2",
                "Seller": "1",
                "Name": "The Big Ben - SKU",
                "SkuName": "The Big Ben - SKU",
                "ProductId": "2",
                "RefId": "big-bben",
                "Ean": "UPC-big-bben",
                "ImageUrl": "https://sandboxsleekflo16.vteximg.com.br/arquivos/ids/155398-55-55/bigben.jpg.jpg?v=638815089546670000",
                "DetailUrl": "/the-big-ben/p"
            }
        ]
    },
    "storePreferencesData": {
        "countryCode": null,
        "currencyCode": "USD",
        "currencyFormatInfo": {
            "CurrencyDecimalDigits": 2,
            "CurrencyDecimalSeparator": ".",
            "CurrencyGroupSeparator": ",",
            "CurrencyGroupSize": 3,
            "StartsWithCurrencySymbol": true
        },
        "currencyLocale": 1033,
        "currencySymbol": "$",
        "timeZone": "Eastern Standard Time"
    },
    "shippingData": {
        "id": "shippingData",
        "address": {
            "addressType": "residential",
            "receiverName": "Phill",
            "addressId": "8732108276370",
            "postalCode": null,
            "city": "IRVINGTON",
            "state": "NJ",
            "country": "USA",
            "street": "My address 111"
        }
    },
    "clientProfileData": {
        "id": "clientProfileData",
        "email": "<EMAIL>",
        "firstName": "Phil",
        "lastName": "VTEX Customer",
        "documentType": null,
        "phone": "+85294977061",
        "corporateName": null,
        "tradeName": null,
        "corporatePhone": null,
        "isCorporate": false,
        "userProfileId": "11ba30dc-fa06-4acf-b6f6-0c7879dfa065",
        "customerCode": null
    },
    "items": [
        {
            "uniqueId": "662A40C6D5C34DBEB462AB9050288D47",
            "id": "2",
            "productId": "2",
            "ean": "UPC-big-bben",
            "lockId": "00-1530090500021-01",
            "itemAttachment": {
                "content": {},
                "name": null
            },
            "quantity": 1,
            "seller": "1",
            "name": "The Big Ben - SKU",
            "refId": "big-bben",
            "price": 10100,
            "listPrice": 10100,
            "manualPrice": null,
            "priceTags": [],
            "imageUrl": "https://sandboxsleekflo16.vteximg.com.br/arquivos/ids/155398-55-55/bigben.jpg.jpg?v=638815089546670000",
            "detailUrl": "/the-big-ben/p",
            "sellerSku": "2",
            "priceValidUntil": "2026-05-06T09:01:07.0000000+00:00",
            "commission": 0,
            "tax": 0,
            "additionalInfo": {
                "brandName": "Test Brand name",
                "brandId": "2000000",
                "categoriesIds": "/1/",
                "categories": [
                    {
                        "id": 1,
                        "name": "My Category"
                    }
                ]
            },
            "isGift": false,
            "taxCode": "",
            "costPrice": 100
        }
    ]
}
""";

            // Deserialize JSON into VtexOrderDto
            var vtexOrderDto = JsonConvert.DeserializeObject<VtexOrderDto>(jsonInput);
            Assert.That(vtexOrderDto, Is.Not.Null, "VtexOrderDto should be successfully deserialized from JSON");

            // Act
            var result = vtexOrderDto!.ToVtexOrderOverview();

            // Assert
            Assert.That(result, Is.Not.Null, "Result should not be null");

            // Basic order details
            Assert.That(result.OrderId, Is.EqualTo("1530090500021-01"));
            Assert.That(result.StatusCode, Is.EqualTo("ready-for-handling"));
            Assert.That(result.IsCompleted, Is.EqualTo(true));
            Assert.That(result.IsCheckedIn, Is.EqualTo(false));
            Assert.That(result.CreatedAt, Is.EqualTo(new DateTime(2025, 5, 6, 9, 2, 47, 934, DateTimeKind.Utc).AddTicks(8585)));
            Assert.That(result.UpdatedAt, Is.EqualTo(new DateTime(2025, 5, 6, 9, 32, 58, 205, DateTimeKind.Utc).AddTicks(2451)));
            Assert.That(result.TotalValue, Is.EqualTo(106.0));
            Assert.That(result.TotalItems, Is.EqualTo(101.0));
            Assert.That(result.TotalTax, Is.EqualTo(0.0));
            Assert.That(result.TotalDiscount, Is.EqualTo(0.0));
            Assert.That(result.TotalShipping, Is.EqualTo(5.0));

            // Shipping data
            Assert.NotNull(result.ShippingData);
            Assert.That(result.ShippingData.ReceiverName, Is.EqualTo("Phill"));
            Assert.That(result.ShippingData.PostalCode, Is.Null);
            Assert.That(result.ShippingData.City, Is.EqualTo("IRVINGTON"));
            Assert.That(result.ShippingData.State, Is.EqualTo("NJ"));
            Assert.That(result.ShippingData.Country, Is.EqualTo("USA"));
            Assert.That(result.ShippingData.Street, Is.EqualTo("My address 111"));

            // Client profile data
            Assert.NotNull(result.ClientProfileData);
            Assert.That(result.ClientProfileData.Email, Is.EqualTo("<EMAIL>"));
            Assert.That(result.ClientProfileData.FirstName, Is.EqualTo("Phil"));
            Assert.That(result.ClientProfileData.LastName, Is.EqualTo("VTEX Customer"));
            Assert.That(result.ClientProfileData.Phone, Is.EqualTo("+85294977061"));

            // Store preferences data
            Assert.NotNull(result.StorePreferencesData);
            Assert.That(result.StorePreferencesData.CountryCode, Is.Null);
            Assert.That(result.StorePreferencesData.CurrencyCode, Is.EqualTo("USD"));
            Assert.That(result.StorePreferencesData.CurrencySymbol, Is.EqualTo("$"));
            Assert.That(result.StorePreferencesData.TimeZone, Is.EqualTo("Eastern Standard Time"));

            // Order items
            Assert.NotNull(result.OrderItems);
            Assert.That(result.OrderItems.Count, Is.EqualTo(1));
            var item = result.OrderItems[0];
            Assert.That(item.Id, Is.EqualTo("2"));
            Assert.That(item.ProductId, Is.EqualTo("2"));
            Assert.That(item.RefId, Is.EqualTo("big-bben"));
            Assert.That(item.SkuName, Is.EqualTo("The Big Ben - SKU"));
            Assert.That(item.ImageUrl, Is.EqualTo("https://sandboxsleekflo16.vteximg.com.br/arquivos/ids/155398-55-55/bigben.jpg.jpg?v=638815089546670000"));
            Assert.That(item.Quantity, Is.EqualTo(1));
            Assert.That(item.Price, Is.EqualTo(101.00));

            // Ensure we have a non-empty normalized items string
            Assert.NotNull(result.NormalizedOrderItems);
            Assert.IsTrue(result.NormalizedOrderItems.Contains("The Big Ben - SKU"));
        }
    }
}