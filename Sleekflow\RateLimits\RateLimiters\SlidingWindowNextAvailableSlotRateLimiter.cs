using Microsoft.Extensions.Logging;
using Sleekflow.Caches;
using Sleekflow.Constants;
using Sleekflow.RateLimits.LuaScripts;
using StackExchange.Redis;

namespace Sleekflow.RateLimits.RateLimiters;

public class SlidingWindowNextAvailableSlotRateLimiter : IRateLimiter
{
    private const string RateLimitAlgorithm = RateLimitAlgorithms.SlidingWindowNextAvailableSlot;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILuaScriptRepositoryService _luaScriptRepositoryService;
    private readonly ILogger<SlidingWindowNextAvailableSlotRateLimiter> _logger;
    private readonly ICacheConfig _cacheConfig;

    public SlidingWindowNextAvailableSlotRateLimiter(
        IConnectionMultiplexer connectionMultiplexer,
        ILuaScriptRepositoryService luaScriptRepositoryService,
        ILogger<SlidingWindowNextAvailableSlotRateLimiter> logger,
        ICacheConfig cacheConfig)
    {
        _connectionMultiplexer = connectionMultiplexer;
        _luaScriptRepositoryService = luaScriptRepositoryService;
        _logger = logger;
        _cacheConfig = cacheConfig;
    }

    public async Task<(DateTimeOffset ScheduledTime, int RemainCount)> GetNextAvailableSlotAsync(
        string keyName,
        SlidingWindowNextAvailableSlotParam slidingWindowNextAvailableSlotParam)
    {
        var db = _connectionMultiplexer.GetDatabase();

        var luaScript = _luaScriptRepositoryService.GetScript(RateLimitAlgorithm);

        var currentTimeMs = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

        var earliestDesiredPublishTimeMs = slidingWindowNextAvailableSlotParam.EarliestDesiredPublishTimeMs;
        if (earliestDesiredPublishTimeMs < currentTimeMs)
        {
            earliestDesiredPublishTimeMs = currentTimeMs;
        }

        try
        {
            var scriptResult = await db.ScriptEvaluateAsync(
                luaScript,
                new
                {
                    key_name = new RedisKey(keyName),
                    current_time = currentTimeMs.ToString(),
                    window_size_ms =
                        (slidingWindowNextAvailableSlotParam.SlidingWindowParam.WindowSeconds * 1000).ToString(),
                    rate_limit =
                        slidingWindowNextAvailableSlotParam.SlidingWindowParam.MaxRequestsAllowedWithinWindow
                            .ToString(),
                    earliest_desired_publish_time_ms = earliestDesiredPublishTimeMs.ToString(),
                    unique_message_id = slidingWindowNextAvailableSlotParam.UniqueMessageId,
                    cache_config_prefix = _cacheConfig.CachePrefix,
                });

            if (scriptResult.IsNull)
            {
                throw new RedisException(
                    "Unable to calculate the next available timeslot");
            }

            var finalScheduledTimeMs = (long) scriptResult[0];
            var remainingCount = (int) scriptResult[1];
            return (DateTimeOffset.FromUnixTimeMilliseconds(finalScheduledTimeMs).ToLocalTime(), remainingCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "[Evaluate Lua Script] error occured when evaluating rate limit Lua Script: {Message}",
                ex.ToString());

            // If timeout for evaluating Lua script, we will schedule the desired time
            return (DateTimeOffset.FromUnixTimeMilliseconds(
                slidingWindowNextAvailableSlotParam.EarliestDesiredPublishTimeMs), 1);
        }
    }
}