﻿using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.KnowledgeBaseEntries;

namespace Sleekflow.IntelligentHub.KnowledgeBaseEntries;

public interface IKnowledgeBaseEntryLoadingService
{
    Task LoadChunksToKnowledgeBaseAsync(
        string sleekflowCompanyId,
        string documentId);
}

public class KnowledgeBaseEntryLoadingService : IKnowledgeBaseEntryLoadingService, IScopedService
{
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;
    private readonly IWebsiteDocumentChunkService _websiteDocumentChunkService;
    private readonly IKnowledgeBaseEntryService _knowledgeBaseEntryService;

    public KnowledgeBaseEntryLoadingService(
        IKbDocumentService kbDocumentService,
        IFileDocumentChunkService fileDocumentChunkService,
        IWebsiteDocumentChunkService websiteDocumentChunkService,
        IKnowledgeBaseEntryService knowledgeBaseEntryService)
    {
        _kbDocumentService = kbDocumentService;
        _fileDocumentChunkService = fileDocumentChunkService;
        _websiteDocumentChunkService = websiteDocumentChunkService;
        _knowledgeBaseEntryService = knowledgeBaseEntryService;
    }

    public async Task LoadChunksToKnowledgeBaseAsync(
        string sleekflowCompanyId,
        string documentId)
    {
        var kbDocument = await _kbDocumentService.GetDocumentAsync(sleekflowCompanyId, documentId);

        if (kbDocument is FileDocument fileDocument)
        {
            var chunkIds = await _fileDocumentChunkService.GetFileDocumentChunkIdsAsync(sleekflowCompanyId, documentId);
            await LoadFileChunksToKnowledgeBaseAsync(chunkIds, sleekflowCompanyId);
        }
        else if (kbDocument is WebsiteDocument websiteDocument)
        {
            var chunkIds =
                await _websiteDocumentChunkService.GetWebsiteDocumentChunkIdsAsync(sleekflowCompanyId, documentId);
            await LoadWebsiteChunksToKnowledgeBaseAsync(chunkIds, sleekflowCompanyId);
        }
        else
        {
            throw new Exception("Unknown KbDocument type");
        }
    }

    private async Task LoadFileChunksToKnowledgeBaseAsync(
        List<string> chunkIds,
        string sleekflowCompanyId)
    {
        await Parallel.ForEachAsync(
            chunkIds,
            async (chunkId, _) =>
            {
                var chunk = await _fileDocumentChunkService.GetFileDocumentChunkAsync(sleekflowCompanyId, chunkId);
                await _knowledgeBaseEntryService.CreateKnowledgeBaseEntryAsync(
                    sleekflowCompanyId,
                    new KnowledgeBaseEntrySource(chunk.DocumentId, KnowledgeBaseSourceTypes.FileDocument),
                    chunk.Id,
                    chunk.Content,
                    chunk.ContentEn,
                    new List<Category>());
            });
    }

    private async Task LoadWebsiteChunksToKnowledgeBaseAsync(
        List<string> chunkIds,
        string sleekflowCompanyId)
    {
        await Parallel.ForEachAsync(
            chunkIds,
            async (chunkId, _) =>
            {
                var chunk = await _websiteDocumentChunkService.GetWebsiteDocumentChunkAsync(
                    sleekflowCompanyId,
                    chunkId);
                await _knowledgeBaseEntryService.CreateKnowledgeBaseEntryAsync(
                    sleekflowCompanyId,
                    new KnowledgeBaseEntrySource(chunk.DocumentId, KnowledgeBaseSourceTypes.WebPageDocument),
                    chunk.Id,
                    chunk.Content,
                    chunk.ContentEn,
                    new List<Category>());
            });
    }
}