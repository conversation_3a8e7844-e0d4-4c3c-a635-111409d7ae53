using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetContactPropertyValueByContactIdsOutput
{
  [JsonProperty("property_values")]
  [Required]
  public Dictionary<string, string> PropertyValues { get; set; }

  [JsonConstructor]
  public GetContactPropertyValueByContactIdsOutput(
      Dictionary<string, string> propertyValues)
  {
    PropertyValues = propertyValues;
  }
}