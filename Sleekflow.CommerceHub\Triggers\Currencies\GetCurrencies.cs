using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Currencies;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Currencies;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CommerceHub.Triggers.Currencies;

[TriggerGroup(ControllerNames.Currencies)]
public class GetCurrencies
    : ITrigger<
        GetCurrencies.GetCurrenciesInput,
        GetCurrencies.GetCurrenciesOutput>
{
    private readonly ICurrencyService _currencyService;

    public GetCurrencies(
        ICurrencyService currencyService)
    {
        _currencyService = currencyService;
    }

    public class GetCurrenciesInput
    {
    }

    public class GetCurrenciesOutput
    {
        [JsonProperty("currencies")]
        public List<CurrencyDto> Currencies { get; set; }

        [JsonConstructor]
        public GetCurrenciesOutput(
            List<CurrencyDto> currencies)
        {
            Currencies = currencies;
        }
    }

    public Task<GetCurrenciesOutput> F(GetCurrenciesInput getCurrenciesInput)
    {
        var currencies = _currencyService.GetCurrencies();

        return Task.FromResult(
            new GetCurrenciesOutput(
                currencies
                    .Select(c => new CurrencyDto(c))
                    .ToList()));
    }
}