using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Prompts;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Evaluator;

public class EGLTourTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetEGLTourTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/EGLTour/",
            SleekflowCompanyId: "18e2da38-52a7-4729-8102-6f5673fd3952");

        yield return new ChatEvalQuestion(
            testConfig,
            "查詢杜拜及土耳其航空公司行李限制",
            [
                new ChatMessageContent(AuthorRole.User, "我想問下搭阿聯酋同土耳其航空，寄艙同手提行李有咩重量同尺寸限制？")
            ],
            "阿聯酋航空(EK)同土耳其航空(TK)嘅寄艙行李規定係：寄艙行李數量無限制，但總重量唔可以超過30公斤（66磅），每件行李嘅長、闊、高總和唔可以超過158厘米（62吋）。手提行李方面，阿聯酋航空每人可攜帶一件，重量唔超過7公斤（15磅），尺寸唔超過55×38×20厘米；土耳其航空每人可攜帶一件，重量唔超過8公斤（17磅），尺寸唔超過55×40×23厘米。以上尺寸已包括手提行李嘅滑輪同手柄。",
            SourceFilenames: ["0ba81f0c-9aa8-e29d-ba73-4ebd668b511f.md"]);

        yield return new ChatEvalQuestion(
            testConfig,
            "查詢香港民航處對外置充電器嘅新規定",
            [
                new ChatMessageContent(AuthorRole.User, "聽講2025年4月7號開始，香港民航處對外置充電器有新規定，想知可唔可以帶超過20000mAh嘅充電器上機？")
            ],
            "根據香港民航處2025年4月7號開始嘅新規定，任何外置充電器容量低於100Wh（約20000mAh以下，因為計算公式係mAh×電壓3.7V÷1000=Wh）嘅，可以隨身攜帶上機，無需申報。容量介乎100Wh至160Wh之間嘅，需航空公司批准，每人最多可攜帶2個。容量高於160Wh嘅充電器係禁止攜帶上機，包括寄艙同手提行李。若充電器冇標明Wh或者mAh同電壓，建議唔好帶上機。",
            SourceFilenames: ["0ba81f0c-9aa8-e29d-ba73-4ebd668b511f.md"]);

        yield return new ChatEvalQuestion(
            testConfig,
            "查詢杜拜入境攜帶免稅香煙及烈酒限額",
            [
                new ChatMessageContent(AuthorRole.User, "去杜拜旅遊，成年人可以帶幾多免稅香煙同烈酒入境？")
            ],
            "杜拜海關規定，年滿18歲以上嘅旅客可以免稅攜帶200支香煙，香水化妝品適量，以及2公升烈酒（但只限非回教徒人士）。超過呢個數量可能會被徵稅或沒收。",
            SourceFilenames: ["0ba81f0c-9aa8-e29d-ba73-4ebd668b511f.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶想了解EGL至尊全球旅遊保嘅保障範圍同特色",
            [
                new ChatMessageContent(AuthorRole.User, "請問EGL至尊全球旅遊保有啲咩保障？有冇年齡限制同地域限制？"),
            ],
            "EGL至尊全球旅遊保係一個全球性保障計劃，冇地域限制同年齡上限，所有保障均免除自負金額（即無墊底費）。佢提供特快賠償服務，即使喺海外都可以辦理。仲有24小時國際支援熱線，支援電話醫療諮詢、醫生及醫院轉介、入院保證金、律師轉介、翻譯服務、行李追蹤同旅遊資訊等，保障彈性高，適合應付突發狀況。",
            SourceFilenames: ["d5a840d6-9c2d-0e42-39aa-b3c8ce0ace74-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶查詢忠意保險集團嘅規模同歷史",
            [
                new ChatMessageContent(AuthorRole.User, "我想知忠意保險係咪大公司？佢哋有幾多年歷史？員工同客戶數目大概係幾多？"),
            ],
            "忠意保險集團係全球最大保險集團之一，擁有超過180年歷史。2015年集團總保費收入超過740億歐羅，並成功並列《財富》世界50強。集團現有超過7萬6千名員工，遍佈全球超過60個國家，為7千2百萬客戶提供專業服務，喺西歐市場佔有領先地位，業務亦擴展至中東歐及亞洲地區。",
            SourceFilenames: ["d5a840d6-9c2d-0e42-39aa-b3c8ce0ace74-0.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶想知道忠誠澳門保險股份有限公司嘅背景及資本",
            [
                new ChatMessageContent(AuthorRole.User, "請問忠誠澳門保險係邊間公司承保？佢哋嘅註冊資本係幾多？主要股東係邊個？"),
            ],
            "忠誠澳門保險股份有限公司負責承保EGL至尊全球/郵輪旅遊保(澳門)計劃。佢嘅母公司係葡萄牙最大保險公司忠誠保險，主要股東係香港上市公司復星國際有限公司（港交所編號656）。忠誠澳門保險股份有限公司嘅註冊股本為澳門幣1億6千萬（160,000,000 MOP）。",
            SourceFilenames: ["d5a840d6-9c2d-0e42-39aa-b3c8ce0ace74-0.md"]);
    }

    public static IEnumerable<ChatEvalQuestion> GetEGLTourPlaceholderTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/EGLTour/",
            SleekflowCompanyId: "18e2da38-52a7-4729-8102-6f5673fd3952");

        // run 10 times to verify placeholder behaviour (it doesn't happen every time)
        for (var i = 0; i < 10; ++i)
        {
            yield return new ChatEvalQuestion(
                testConfig,
                "Test placeholder",
                [
                    new ChatMessageContent(AuthorRole.User, "Hi"),
                    new ChatMessageContent(
                        AuthorRole.Assistant,
                        "Hi there! 👋 So glad you reached out. What can I help you with today? 😊"),
                    new ChatMessageContent(AuthorRole.User, "有冇九州嘅旅行？Please give me link to itinerary"),
                ],
                "哈囉！👋 梗係有九州嘅旅行團啦！🤩 我哋有直飛熊本、鹿兒島、宮崎、別府同福岡嘅團㗎！✈️ 你可以去熊本熊主題公園影相，浸下溫泉酒店，或者去埋世界遺產嘅景點參觀！😎 想睇多啲行程細節？即刻撳呢度睇下啦！👉 [link to itinerary] 有冇其他嘢想知呀？😊",
                SourceFilenames: []);
        }
    }

    public static IEnumerable<ChatEvalQuestion> GetEGLTourTourIdTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/EGLTour/",
            SleekflowCompanyId: "18e2da38-52a7-4729-8102-6f5673fd3952");

        yield return new ChatEvalQuestion(
            testConfig,
            "Directly ask about tour JTET06",
            [
                new ChatMessageContent(AuthorRole.User, "Tell me about tour JTET06"),
            ],
            "您好！很高興為您介紹JTET06東京、箱根、鎌倉、橫濱溫泉酒店六天團。這個行程包含了入住2023年全新開幕的LA VISTA橫須賀露台溫泉酒店海景露台房，並暢遊橫濱紅磚倉庫、箱根纜車、大涌谷地獄溫泉、小田原城等精彩景點。不過，此團的最後出發日期為5月31日，目前已暫停接受報名。如果您對日本其他精彩行程感興趣，或是想了解更多溫泉酒店體驗，歡迎告訴我您的偏好，我樂意為您推薦最適合的選擇！",
            SourceFilenames: []);

        yield return new ChatEvalQuestion(
            testConfig,
            "Directly ask about tour JEEK07",
            [
                new ChatMessageContent(AuthorRole.User, "Tell me about tour JEEK07"),
            ],
            "您好！很高興您對JEEK07東北日本七天團感興趣！\n\n這個行程將帶您深入探索日本東北的迷人風光與獨特體驗，包括：\n\n* *「日本九寨溝」五色沼* ：感受大自然的神秘色彩。\n\n* *「時光隧道」大內宿* ：體驗古老日式風情。\n\n* *夢幻銀山溫泉* ：享受溫泉鄉的浪漫氛圍，是絕佳的打卡點！\n\n* *Hello Kitty御守白山神社* ：可愛又獨特的文化體驗。\n\n* *新潟最高建築物Toki展望台* ：俯瞰壯麗景色。\n\n這趟旅程結合了自然美景、文化探索與美食體驗，讓您留下難忘的回憶。想了解更多行程細節或出發日期嗎？\ud83d\ude0a",
            SourceFilenames: []);

        yield return new ChatEvalQuestion(
            testConfig,
            "ask about tour JTET06 in long conversation",
            [
                new ChatMessageContent(AuthorRole.User, "hihi 我想嚟緊同老人家去旅行，你有咩推介"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您推薦適合與長者同行的旅行團！\ud83d\udc75\u2708\ufe0f\\n\\n為長者安排旅程，最重要是行程輕鬆、舒適，又能體驗當地文化。我們特別為家庭旅客設計了多個「純玩」行程，非常適合一家大小，包括長者一同出遊！\\n\\n例如，我們的北京純玩團就非常受歡迎：\\n*   *北京5天純玩團 (CPFA05)* ：行程涵蓋故宮、長城夜遊、世界花園等，讓您和家人輕鬆感受古都魅力與自然美景。\\n*   *北京6天親子純玩團 (CPFF06)* ：除了故宮、長城，更可暢玩環球影城及入住Club Med地中海俱樂部，享受一站式度假體驗，非常舒適寫意！\\n\\n這兩個團都主打「無指定購物點」，讓您和長者可以更自在地享受旅程，無需趕行程。\u2728\\n\\n請問您和家人預計什麼時候出發呢？對哪類型的景點比較感興趣？例如是文化古蹟、自然風光還是休閒度假呢？期待您的回覆，讓我為您提供更精準的建議！"),
                new ChatMessageContent(AuthorRole.User, "how about Japan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "日本當然也是非常棒的選擇！\ud83c\uddef\ud83c\uddf5 我們有多個專為家庭設計的日本團，行程舒適又豐富，非常適合與長者一同出遊。\\n\\n例如，我們的 *東京、箱根、山梨、富士山牧場5天團 (JTJK05)* ，能讓您和長者輕鬆欣賞富士山美景與箱根的藝術氣息。或是 *東京、埼玉、群馬、輕井澤7天團 (JTJB07)* ，帶您體驗日本的自然風光、溫泉文化與時尚購物，行程更為悠閒。\\n\\n這些團都特別注重行程的舒適度與多樣性，確保長者能輕鬆享受旅程。\\n\\n請問您對日本哪個地區比較感興趣呢？希望旅程是偏向自然風光、文化體驗還是休閒購物？讓我為您提供更精準的建議！"),
                new ChatMessageContent(AuthorRole.User, "how about JTET06"),
            ],
            "您好！很高興為您介紹JTET06東京、箱根、鎌倉、橫濱溫泉酒店六天團。這個行程包含了入住2023年全新開幕的LA VISTA橫須賀露台溫泉酒店海景露台房，並暢遊橫濱紅磚倉庫、箱根纜車、大涌谷地獄溫泉、小田原城等精彩景點。不過，此團的最後出發日期為5月31日，目前已暫停接受報名。如果您對日本其他精彩行程感興趣，或是想了解更多溫泉酒店體驗，歡迎告訴我您的偏好，我樂意為您推薦最適合的選擇！",
            SourceFilenames: []);

        yield return new ChatEvalQuestion(
            testConfig,
            "ask about tour JEEK07 in long conversation",
            [
                new ChatMessageContent(AuthorRole.User, "hihi 我想嚟緊同老人家去旅行，你有咩推介"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！很高興為您推薦適合與長者同行的旅行團！\ud83d\udc75\u2708\ufe0f\\n\\n為長者安排旅程，最重要是行程輕鬆、舒適，又能體驗當地文化。我們特別為家庭旅客設計了多個「純玩」行程，非常適合一家大小，包括長者一同出遊！\\n\\n例如，我們的北京純玩團就非常受歡迎：\\n*   *北京5天純玩團 (CPFA05)* ：行程涵蓋故宮、長城夜遊、世界花園等，讓您和家人輕鬆感受古都魅力與自然美景。\\n*   *北京6天親子純玩團 (CPFF06)* ：除了故宮、長城，更可暢玩環球影城及入住Club Med地中海俱樂部，享受一站式度假體驗，非常舒適寫意！\\n\\n這兩個團都主打「無指定購物點」，讓您和長者可以更自在地享受旅程，無需趕行程。\u2728\\n\\n請問您和家人預計什麼時候出發呢？對哪類型的景點比較感興趣？例如是文化古蹟、自然風光還是休閒度假呢？期待您的回覆，讓我為您提供更精準的建議！"),
                new ChatMessageContent(AuthorRole.User, "how about Japan?"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "日本當然也是非常棒的選擇！\ud83c\uddef\ud83c\uddf5 我們有多個專為家庭設計的日本團，行程舒適又豐富，非常適合與長者一同出遊。\\n\\n例如，我們的 *東京、箱根、山梨、富士山牧場5天團 (JTJK05)* ，能讓您和長者輕鬆欣賞富士山美景與箱根的藝術氣息。或是 *東京、埼玉、群馬、輕井澤7天團 (JTJB07)* ，帶您體驗日本的自然風光、溫泉文化與時尚購物，行程更為悠閒。\\n\\n這些團都特別注重行程的舒適度與多樣性，確保長者能輕鬆享受旅程。\\n\\n請問您對日本哪個地區比較感興趣呢？希望旅程是偏向自然風光、文化體驗還是休閒購物？讓我為您提供更精準的建議！"),
                new ChatMessageContent(AuthorRole.User, "how about JEEK07"),
            ],
            "您好！很高興您對JEEK07東北日本七天團感興趣！\n\n這個行程將帶您深入探索日本東北的迷人風光與獨特體驗，包括：\n\n* *「日本九寨溝」五色沼* ：感受大自然的神秘色彩。\n\n* *「時光隧道」大內宿* ：體驗古老日式風情。\n\n* *夢幻銀山溫泉* ：享受溫泉鄉的浪漫氛圍，是絕佳的打卡點！\n\n* *Hello Kitty御守白山神社* ：可愛又獨特的文化體驗。\n\n* *新潟最高建築物Toki展望台* ：俯瞰壯麗景色。\n\n這趟旅程結合了自然美景、文化探索與美食體驗，讓您留下難忘的回憶。想了解更多行程細節或出發日期嗎？\ud83d\ude0a",
            SourceFilenames: []);
    }
}