﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Workers;

public class LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput
{
    [JsonProperty("count")]
    public int Count { get; set; }

    [JsonProperty("has_next_page")]
    public bool HasNextPage { get; set; }

    [JsonProperty("processed_earliest_order_created_at")]
    public DateTimeOffset? ProcessedEarliestOrderCreatedAt { get; set; }

    [JsonConstructor]
    public LoopThroughAndEnrollVtexOrdersToFlowHubBatchOutput(
        int count,
        bool hasNextPage,
        DateTimeOffset? processedEarliestOrderCreatedAt)
    {
        Count = count;
        HasNextPage = hasNextPage;
        ProcessedEarliestOrderCreatedAt = processedEarliestOrderCreatedAt;
    }
}