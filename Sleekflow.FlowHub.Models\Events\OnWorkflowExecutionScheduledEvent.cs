﻿using Sleekflow.Events.ServiceBus.HighTrafficServiceBus;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowExecutionScheduledEvent : IHighTrafficEvent
{
    public string SleekflowCompanyId { get; set; }

    public string StateId { get; set; }

    public string? WorkflowExecutionReasonCode { get; set; }

    public StateIdentity StateIdentity { get; set; }

    public string? WorkflowType { get; set; }

    public AuditEntity.SleekflowStaff? ScheduledBy { get; set; }

    public DateTimeOffset ScheduledAt { get; set; } = DateTimeOffset.UtcNow;

    public OnWorkflowExecutionScheduledEvent(
        string sleekflowCompanyId,
        string stateId,
        string? workflowExecutionReasonCode,
        StateIdentity stateIdentity,
        string? workflowType,
        AuditEntity.SleekflowStaff? scheduledBy)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        StateId = stateId;
        WorkflowExecutionReasonCode = workflowExecutionReasonCode;
        StateIdentity = stateIdentity;
        WorkflowType = workflowType;
        ScheduledBy = scheduledBy;
    }
}