﻿using System.Reflection;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;
using Sleekflow.FlowHub.Statistics;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Tests.Statistics;

public class WorkflowStepCategoryProviderTests
{
    private readonly IWorkflowStepCategoryProvider _workflowStepCategoryProvider = new WorkflowStepCategoryProvider(
        new WorkflowStepEntryProvider());

    [Test]
    public void GetStepCategoryCountDict_GivenValidWorkflowWithSteps_ShouldReturnStepCategoryCountDict()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "oG4U5vX7MV3nGma2",
                "workflow_versioned_id": "oG4U5vX7MV3nGma2-rNLcbqLGr7pNkoda",
                "name": "Untitled",
                "workflow_type": "normal",
                "triggers": {
                    "message_received": {
                        "condition": "{{ ([\"***********\"] | array.contains event_body.channel_id) && ((((event_body.contact[\"1824e844-348d-48f9-9746-4945dc380fff\"] | string.downcase) | string.contains \"189376147\"))) }}"
                    }
                },
                "workflow_enrollment_settings": {
                    "can_enroll_only_once": false,
                    "can_enroll_again_on_failure_only": false,
                    "can_enroll_in_parallel": false
                },
                "workflow_schedule_settings": {
                    "schedule_type": "none"
                },
                "steps": [
                    {
                        "assign": {
                            "contact": "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact) }}",
                            "conversation": "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_conversation) }}",
                            "lists": "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_lists) }}"
                        },
                        "id": "setup-contact-and-conversation",
                        "name": "setup",
                        "next_step_id": null
                    },
                    {
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "id": "setup-contact-owner",
                        "name": "setup-contact-owner",
                        "next_step_id": null
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Hello\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "a0068961-e3ed-4f4a-86bd-16ed11fec163",
                        "name": "Action 1",
                        "next_step_id": "3929124a-1534-45ae-b417-008b4ef6f86b"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"image_message\":{\"link\":\"https://sfd6c2495.blob.core.windows.net/image-container/b6d7e442-38ae-4b9a-b100-2951729768bc/JQSP0ykdmrp1XkZ?sv=2024-08-04&se=9999-12-31T23%3A59%3A59Z&sr=b&sp=r&sig=d3Udp27gTEioh%2Fuc55KjNOa0O7hNJ%2B2VeiVL%2BU6cy1g%3D\",\"filename\":\"13b.png\"}} }}",
                            "message_type__expr": "image"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "3929124a-1534-45ae-b417-008b4ef6f86b",
                        "name": "Action 2",
                        "next_step_id": "85e9198e-1fd5-43a7-abc4-9ef79f140e72"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "content__expr": "{{ \"this is a note\" }}"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.add-internal-note-to-contact",
                        "id": "85e9198e-1fd5-43a7-abc4-9ef79f140e72",
                        "name": "Action 3",
                        "next_step_id": "6308320d-bb46-4aa2-b8a8-520f66ab15d0"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "properties__key_expr_dict": {
                                "LastName": "{{ \"abc\" }}"
                            }
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-properties",
                        "id": "6308320d-bb46-4aa2-b8a8-520f66ab15d0",
                        "name": "Action 4",
                        "next_step_id": "2162c2d6-fab9-46ac-8741-ad3e9575a36b"
                    },
                    {
                        "args": {
                            "add_list_ids__expr": "{{ [\"6157\"] }}",
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_list_ids__expr": null,
                            "set_list_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-list-relationships",
                        "id": "2162c2d6-fab9-46ac-8741-ad3e9575a36b",
                        "name": "Action 13",
                        "next_step_id": "69b94a53-7a53-4135-b153-71fb8b512081"
                    },
                    {
                        "args": {
                            "add_list_ids__expr": null,
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_list_ids__expr": "{{ [\"6157\"] }}",
                            "set_list_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-list-relationships",
                        "id": "69b94a53-7a53-4135-b153-71fb8b512081",
                        "name": "Action 14",
                        "next_step_id": "5b7b0a55-2d8a-4040-b7ab-43a65d103307"
                    },
                    {
                        "args": {
                            "add_labels__expr": "{{ [\"f\"] }}",
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_labels__expr": null,
                            "set_labels__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-label-relationships",
                        "id": "5b7b0a55-2d8a-4040-b7ab-43a65d103307",
                        "name": "Action 16",
                        "next_step_id": "911b0e7a-75bf-49f2-bfb1-2e23731eed16"
                    },
                    {
                        "args": {
                            "add_labels__expr": null,
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_labels__expr": "{{ [\"f\"] }}",
                            "set_labels__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-label-relationships",
                        "id": "911b0e7a-75bf-49f2-bfb1-2e23731eed16",
                        "name": "Action 15",
                        "next_step_id": "cd971280-ae71-463f-b1bc-776284915f5b"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ ('{{ usr_var_dict.contact[\"PhoneNumber\"] ?? \"\" }}' | template.eval | sleekflow.get_or_create_contact_id_by_phone_number) }}",
                            "primary_property_value__expr": "{{ \"123\" }}",
                            "property_values__key_expr_dict": {
                                "3Zi772x2N7ay53e": "{{ \"3131\" }}",
                                "Wpi114a4X1031qM": "{{ 123 }}",
                                "Wpi114a4X1kNb2l": "{{ \"131414\" }}",
                                "XAiRR8B8GRL2l0Q": "{{ \"2025-08-02\" }}",
                                "XAiRR8B8GRQMR1m": "{{ 131 }}",
                                "Z1iLLklbpqZxz0Y": "{{ \"a\" }}",
                                "qkiAA5k58AeXxdq": "{{ \"true\" }}",
                                "z8iMMAdAbML4yP1": "{{ \"3131\" }}"
                            },
                            "schema_id__expr": "{{ \"aoidnp68Kz8JLMY\" }}"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.create-schemaful-object",
                        "id": "cd971280-ae71-463f-b1bc-776284915f5b",
                        "name": "Action 5",
                        "next_step_id": "8dfdb6a8-673b-4f23-a013-b6b0bb78012d"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "status__expr": "{{ \"open\" }}"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-conversation-status",
                        "id": "8dfdb6a8-673b-4f23-a013-b6b0bb78012d",
                        "name": "Action 6",
                        "next_step_id": "86112502-d098-48f8-8677-c0c228ed4eeb"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "staff_ids__expr": "{{ [\"70be7cd2-5b3d-4aad-8270-04e9fb553252\"] }}",
                            "strategy": "RoundRobbin_StaffOnly",
                            "team_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-owner-relationships",
                        "id": "86112502-d098-48f8-8677-c0c228ed4eeb",
                        "name": "Action 7",
                        "next_step_id": "b353d4d4-0efd-49f9-970b-7e63935926bb"
                    },
                    {
                        "args": {
                            "add_staff_ids__expr": "{{ [\"8f60e313-3eb1-4816-a434-f61848e4c05a\"] }}",
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_staff_ids__expr": null,
                            "set_staff_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-collaborator-relationships",
                        "id": "b353d4d4-0efd-49f9-970b-7e63935926bb",
                        "name": "Action 8",
                        "next_step_id": "0448b77c-0ed9-4a4d-8db1-47b307cfadbf"
                    },
                    {
                        "args": {
                            "add_staff_ids__expr": null,
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_staff_ids__expr": "{{ [\"8f60e313-3eb1-4816-a434-f61848e4c05a\"] }}",
                            "set_staff_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-collaborator-relationships",
                        "id": "0448b77c-0ed9-4a4d-8db1-47b307cfadbf",
                        "name": "Action 9",
                        "next_step_id": "ea9c4deb-da84-4248-a866-f9dbbe6e01aa"
                    },
                    {
                        "args": {
                            "body__expr": "{{ trigger_event_body | json.serialize }}",
                            "body__key_expr_dict": null,
                            "headers__key_expr_dict": {
                                "Content-Type": "application/json",
                                "X-Method": "{{ \"message_webhook\" }}"
                            },
                            "url__expr": "https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7"
                        },
                        "assign": null,
                        "call": "http.post",
                        "id": "ea9c4deb-da84-4248-a866-f9dbbe6e01aa",
                        "name": "Action 10",
                        "next_step_id": "7e150988-36fc-42a7-8aff-31c079d82076"
                    },
                    {
                        "args": {
                            "body__expr": "{{ \"hello from POST request\" }}",
                            "body__key_expr_dict": null,
                            "headers__key_expr_dict": {
                                "Content-Type": "text/plain"
                            },
                            "url__expr": "{{ \"https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7\" }}"
                        },
                        "assign": null,
                        "call": "http.post",
                        "id": "7e150988-36fc-42a7-8aff-31c079d82076",
                        "name": "Action 11",
                        "next_step_id": "1b25cad1-7f12-4491-82f7-420bd1b0188f"
                    },
                    {
                        "args": {
                            "conditions__expr": "{{ [{\"field_name\":\"Id\",\"operator\":\"isEqualTo\",\"value\":\"{{ \\\"123\\\" }}\"}] }}",
                            "is_custom_object__expr": "{{ \"false\" }}",
                            "object_type__expr": "{{ \"Account\" }}",
                            "salesforce_connection_id__expr": "{{ \"xMRfL4Y7xzN1Z4y4\" }}"
                        },
                        "assign": {
                            "312312": "{{ (sys_var_dict[\"1b25cad1-7f12-4491-82f7-420bd1b0188f\"] | json.deserialize).Name }}"
                        },
                        "call": "sleekflow.v1.search-salesforce-object",
                        "id": "1b25cad1-7f12-4491-82f7-420bd1b0188f",
                        "name": "Action 12",
                        "next_step_id": "ede0112c-5796-4b94-a069-bf2cc9121f52"
                    },
                    {
                        "assign": null,
                        "id": "ede0112c-5796-4b94-a069-bf2cc9121f52",
                        "name": "end",
                        "next_step_id": null
                    }
                ],
                "activation_status": "Draft",
                "metadata": {
                    "v4": "{\"nodes\":[{\"id\":\"a6b4ecc9-c83d-4478-8d63-6093ef05f86f\",\"position\":{\"x\":0,\"y\":0},\"data\":{\"category\":\"start\",\"formValues\":{\"isFilterByCondition\":true,\"conditions\":[{\"conditionType\":\"contactProperty\",\"contactProperty\":{\"id\":\"1824e844-348d-48f9-9746-4945dc380fff\",\"label\":\"Phone Number\",\"type\":\"PhoneNumber\"},\"operator\":\"contains\",\"value\":\"189376147\"}],\"triggerType\":\"incoming-messages\",\"channels\":[{\"id\":\"***********\",\"channelIdentityId\":\"***********\",\"subId\":206,\"name\":\"Flow Builder Test UAT\",\"channel\":\"whatsappcloudapi\",\"messagingHubWabaId\":\"amUx8pKxvn051X\",\"facebookWabaBusinessId\":\"3305169899744267\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"incoming-messages\",\"positionAbsolute\":{\"x\":0,\"y\":0},\"width\":300,\"height\":216,\"selected\":false},{\"id\":\"a0068961-e3ed-4f4a-86bd-16ed11fec163\",\"type\":\"sendMessage\",\"position\":{\"x\":0,\"y\":336},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 1\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"Hello\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":336},\"width\":300,\"height\":202},{\"id\":\"3929124a-1534-45ae-b417-008b4ef6f86b\",\"type\":\"sendMedia\",\"position\":{\"x\":0,\"y\":658},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 2\",\"actionType\":\"sendMedia\",\"channelLogic\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"media\":[{\"name\":\"13b.png\",\"preview\":\"https://sfd6c2495.blob.core.windows.net/image-container/b6d7e442-38ae-4b9a-b100-2951729768bc/JQSP0ykdmrp1XkZ?sv=2024-08-04&se=9999-12-31T23%3A59%3A59Z&sr=b&sp=r&sig=d3Udp27gTEioh%2Fuc55KjNOa0O7hNJ%2B2VeiVL%2BU6cy1g%3D\",\"type\":\"image\",\"size\":1436767}],\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":658},\"width\":300,\"height\":364},{\"id\":\"85e9198e-1fd5-43a7-abc4-9ef79f140e72\",\"type\":\"internalNote\",\"position\":{\"x\":0,\"y\":1142},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 3\",\"defaultTitle\":\"\",\"actionType\":\"internalNote\",\"messageContent\":\"this is a note\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":1142},\"width\":300,\"height\":182},{\"id\":\"6308320d-bb46-4aa2-b8a8-520f66ab15d0\",\"type\":\"updateContactProperties\",\"position\":{\"x\":0,\"y\":1444},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 4\",\"actionType\":\"updateContactProperties\",\"properties\":[{\"property\":{\"id\":\"LastName\",\"label\":\"Last name\",\"type\":\"SingleLineText\",\"options\":[]},\"value\":\"abc\",\"valueLabel\":\"\"}]},\"formState\":{\"errors\":{\"properties\":[{\"value\":{\"message\":\"This is required\",\"type\":\"required\",\"ref\":{}}}]},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":1444},\"width\":300,\"height\":148},{\"id\":\"2162c2d6-fab9-46ac-8741-ad3e9575a36b\",\"type\":\"addToList\",\"position\":{\"x\":0,\"y\":1712},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 13\",\"actionType\":\"addToList\",\"lists\":[{\"id\":\"6157\",\"listName\":\"Hong List\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":1712},\"width\":300,\"height\":188},{\"id\":\"69b94a53-7a53-4135-b153-71fb8b512081\",\"type\":\"removeFromList\",\"position\":{\"x\":0,\"y\":2020},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 14\",\"actionType\":\"removeFromList\",\"removeLogic\":\"specific\",\"lists\":[{\"id\":\"6157\",\"listName\":\"Hong List\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2020},\"width\":300,\"height\":188},{\"id\":\"5b7b0a55-2d8a-4040-b7ab-43a65d103307\",\"type\":\"addLabel\",\"position\":{\"x\":0,\"y\":2328},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 16\",\"actionType\":\"addLabel\",\"labels\":[{\"id\":\"a27ffb3b-1d8c-44cb-93d1-574cad1d02d9\",\"hashtag\":\"f\",\"hashTagColor\":\"Blue\",\"count\":7,\"hashTagType\":\"Normal\",\"label\":\"f\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2328},\"width\":300,\"height\":184},{\"id\":\"911b0e7a-75bf-49f2-bfb1-2e23731eed16\",\"type\":\"removeLabel\",\"position\":{\"x\":0,\"y\":2632},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 15\",\"actionType\":\"removeLabel\",\"removeLogic\":\"specific\",\"labels\":[{\"id\":\"a27ffb3b-1d8c-44cb-93d1-574cad1d02d9\",\"hashtag\":\"f\",\"hashTagColor\":\"Blue\",\"count\":7,\"hashTagType\":\"Normal\",\"label\":\"f\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2632},\"width\":300,\"height\":184},{\"id\":\"cd971280-ae71-463f-b1bc-776284915f5b\",\"type\":\"createCustomObjectRecord\",\"position\":{\"x\":0,\"y\":2936},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 5\",\"actionType\":\"createCustomObjectRecord\",\"customObjectId\":\"aoidnp68Kz8JLMY\",\"contactNumber\":\"{{contact.PhoneNumber}}\",\"properties\":[{\"type\":\"number\",\"value\":\"123\",\"property\":{\"id\":\"Wpi114a4X1031qM\",\"name\":\"number\",\"type\":\"number\"}},{\"type\":\"decimalNumber\",\"value\":\"131\",\"property\":{\"id\":\"XAiRR8B8GRQMR1m\",\"name\":\"decimal number\",\"type\":\"decimalNumber\"}},{\"type\":\"variable\",\"value\":\"3131\",\"property\":{\"id\":\"z8iMMAdAbML4yP1\",\"name\":\"multi select\",\"type\":\"multiSelection\",\"options\":[{\"id\":\"4Li00dpdY074qN3\",\"value\":\"a\"},{\"id\":\"briooVXVeo5bmlo\",\"value\":\"b\"},{\"id\":\"Ezi112726143ZlK\",\"value\":\"c\"},{\"id\":\"oEi554n4k5Wx7Pp\",\"value\":\"d\"}]}},{\"type\":\"variable\",\"value\":\"3131\",\"property\":{\"id\":\"3Zi772x2N7ay53e\",\"name\":\"single select\",\"type\":\"singleSelection\",\"options\":[{\"id\":\"kKippVPV7pBRXGG\",\"value\":\"a\"},{\"id\":\"A7iGG0e0RGrn1b4\",\"value\":\"b\"}]}},{\"type\":\"variable\",\"value\":\"131414\",\"property\":{\"id\":\"Wpi114a4X1kNb2l\",\"name\":\"date\",\"type\":\"date\"}},{\"type\":\"variable\",\"value\":\"2025-08-02\",\"property\":{\"id\":\"XAiRR8B8GRL2l0Q\",\"name\":\"date time\",\"type\":\"dateTime\"}},{\"type\":\"variable\",\"value\":\"true\",\"property\":{\"id\":\"qkiAA5k58AeXxdq\",\"name\":\"boolean\",\"type\":\"boolean\"}},{\"type\":\"text\",\"value\":\"a\",\"property\":{\"id\":\"Z1iLLklbpqZxz0Y\",\"name\":\"read variable\",\"type\":\"text\"}}],\"optionalProperties\":[],\"primaryProperty\":{\"value\":\"123\",\"isRequired\":true}},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2936},\"width\":300,\"height\":128},{\"id\":\"8dfdb6a8-673b-4f23-a013-b6b0bb78012d\",\"type\":\"updateConversationStatus\",\"position\":{\"x\":0,\"y\":3184},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 6\",\"actionType\":\"updateConversationStatus\",\"updatedStatus\":\"open\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":3184},\"width\":300,\"height\":96},{\"id\":\"86112502-d098-48f8-8677-c0c228ed4eeb\",\"type\":\"assignTo\",\"position\":{\"x\":0,\"y\":3400},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 7\",\"actionType\":\"assignTo\",\"assignTo\":\"specificUser\",\"assignees\":[{\"id\":\"70be7cd2-5b3d-4aad-8270-04e9fb553252\",\"name\":\"Yi Zhi Hong\",\"avatar\":\"https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/comapny/profilepicture/96091217-9589-457b-83e4-60041921385b\"}],\"assignLogic\":\"\",\"team\":{\"id\":\"\",\"teamName\":\"\"}},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":3400},\"width\":300,\"height\":180},{\"id\":\"b353d4d4-0efd-49f9-970b-7e63935926bb\",\"type\":\"addCollaborator\",\"position\":{\"x\":0,\"y\":3700},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 8\",\"actionType\":\"addCollaborator\",\"collaborators\":[{\"id\":\"8f60e313-3eb1-4816-a434-f61848e4c05a\",\"name\":\"Allison K\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":3700},\"width\":300,\"height\":196},{\"id\":\"0448b77c-0ed9-4a4d-8db1-47b307cfadbf\",\"type\":\"removeCollaborator\",\"position\":{\"x\":0,\"y\":4016},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 9\",\"actionType\":\"removeCollaborator\",\"removeLogic\":\"specific\",\"collaborators\":[{\"id\":\"8f60e313-3eb1-4816-a434-f61848e4c05a\",\"name\":\"Allison K\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4016},\"width\":300,\"height\":196},{\"id\":\"ea9c4deb-da84-4248-a866-f9dbbe6e01aa\",\"type\":\"messageWebhook\",\"position\":{\"x\":0,\"y\":4332},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 10\",\"webhookURL\":\"https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7\",\"actionType\":\"messageWebhook\",\"isEnableHeaders\":true,\"headers\":[{\"key\":\"X-Method\",\"value\":\"message_webhook\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4332},\"width\":300,\"height\":192},{\"id\":\"7e150988-36fc-42a7-8aff-31c079d82076\",\"type\":\"httpRequest\",\"position\":{\"x\":0,\"y\":4644},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 11\",\"actionType\":\"httpRequest\",\"handleFailure\":false,\"httpMethod\":\"POST\",\"httpRequestURL\":\"https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7\",\"httpRequestHeaders\":[{\"key\":\"\",\"value\":\"\"}],\"httpRequestQueryString\":[{\"key\":\"\",\"value\":\"\"}],\"isHttpBodyOn\":true,\"httpRequestBody\":{\"bodyType\":\"raw\",\"contentType\":\"text/plain\",\"bodyValue\":\"hello from POST request\"},\"saveHttpResponse\":[{\"variable\":\"\",\"expression\":\"\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4644},\"width\":300,\"height\":172},{\"id\":\"1b25cad1-7f12-4491-82f7-420bd1b0188f\",\"type\":\"mapSalesforceAccount\",\"position\":{\"x\":0,\"y\":4936},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 12\",\"actionType\":\"mapSalesforceAccount\",\"organization\":{\"id\":\"xMRfL4Y7xzN1Z4y4\",\"name\":\"SleekFlow Technologies Inc.\",\"environment\":\"production\",\"organizationId\":\"sleekflowtechnologiesinc.my.salesforce.com\",\"status\":\"connected\"},\"entityType\":\"Account\",\"searchQueries\":[{\"property\":{\"name\":\"Id\",\"propertyType\":\"Account\",\"type\":\"id\",\"options\":[],\"label\":\"Account ID\"},\"operator\":\"isEqualTo\",\"value\":\"123\"}],\"saveAsFlowVariables\":[{\"property\":{\"name\":\"Name\",\"propertyType\":\"Account\",\"type\":\"string\",\"options\":[],\"mandatory\":true,\"label\":\"Account Name\"},\"expression\":\"312312\"}]},\"formState\":{\"errors\":{},\"isValid\":false,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4936},\"width\":300,\"height\":148},{\"id\":\"b578ece4-e24e-43f0-a14f-633bb46cd4f2\",\"position\":{\"x\":0,\"y\":5204},\"data\":{\"category\":\"end\",\"formValues\":{\"endType\":\"here\",\"targetNode\":null,\"title\":\" \"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"end\",\"positionAbsolute\":{\"x\":0,\"y\":5204},\"width\":300,\"height\":116,\"selected\":false}],\"edges\":[{\"type\":\"buttonEdge\",\"id\":\"87ac3846-98d9-490d-9707-60ab39b00dbc\",\"source\":\"a6b4ecc9-c83d-4478-8d63-6093ef05f86f\",\"target\":\"a0068961-e3ed-4f4a-86bd-16ed11fec163\"},{\"type\":\"buttonEdge\",\"id\":\"5f429054-5155-4a04-8591-a9c28577f0d8\",\"source\":\"a0068961-e3ed-4f4a-86bd-16ed11fec163\",\"target\":\"3929124a-1534-45ae-b417-008b4ef6f86b\"},{\"type\":\"buttonEdge\",\"id\":\"030016e0-3e83-497a-b336-d06e49b8b584\",\"source\":\"3929124a-1534-45ae-b417-008b4ef6f86b\",\"target\":\"85e9198e-1fd5-43a7-abc4-9ef79f140e72\"},{\"type\":\"buttonEdge\",\"id\":\"07e8bb47-e9e8-4091-99e1-f0a21e4e1b5d\",\"source\":\"85e9198e-1fd5-43a7-abc4-9ef79f140e72\",\"target\":\"6308320d-bb46-4aa2-b8a8-520f66ab15d0\"},{\"type\":\"buttonEdge\",\"id\":\"92f4a4e3-4e59-4b44-aa22-6460fc4b6be2\",\"source\":\"cd971280-ae71-463f-b1bc-776284915f5b\",\"target\":\"8dfdb6a8-673b-4f23-a013-b6b0bb78012d\"},{\"type\":\"buttonEdge\",\"id\":\"187fa276-dda1-49f7-a087-fafb043c0a86\",\"source\":\"8dfdb6a8-673b-4f23-a013-b6b0bb78012d\",\"target\":\"86112502-d098-48f8-8677-c0c228ed4eeb\"},{\"type\":\"buttonEdge\",\"id\":\"5805d97f-a788-4682-8d2b-b0edaeddcfb0\",\"source\":\"86112502-d098-48f8-8677-c0c228ed4eeb\",\"target\":\"b353d4d4-0efd-49f9-970b-7e63935926bb\"},{\"type\":\"buttonEdge\",\"id\":\"16d0f195-1f47-4621-877f-5f6ed4edd15b\",\"source\":\"b353d4d4-0efd-49f9-970b-7e63935926bb\",\"target\":\"0448b77c-0ed9-4a4d-8db1-47b307cfadbf\"},{\"type\":\"buttonEdge\",\"id\":\"5da12d12-e15b-43a7-a71b-3058f52164b1\",\"source\":\"0448b77c-0ed9-4a4d-8db1-47b307cfadbf\",\"target\":\"ea9c4deb-da84-4248-a866-f9dbbe6e01aa\"},{\"type\":\"buttonEdge\",\"id\":\"50fa5b0d-2508-45d5-ba64-53d98927bc6d\",\"source\":\"ea9c4deb-da84-4248-a866-f9dbbe6e01aa\",\"target\":\"7e150988-36fc-42a7-8aff-31c079d82076\"},{\"type\":\"buttonEdge\",\"id\":\"96f242e2-675e-4ab9-b752-7e94de895ea1\",\"source\":\"7e150988-36fc-42a7-8aff-31c079d82076\",\"target\":\"1b25cad1-7f12-4491-82f7-420bd1b0188f\"},{\"type\":\"buttonEdge\",\"id\":\"48f07f5d-25de-4fd3-a81e-06f21f7055d1\",\"source\":\"1b25cad1-7f12-4491-82f7-420bd1b0188f\",\"target\":\"b578ece4-e24e-43f0-a14f-633bb46cd4f2\"},{\"type\":\"buttonEdge\",\"id\":\"d0a03a02-b060-43db-b265-aa41745cdc05\",\"source\":\"6308320d-bb46-4aa2-b8a8-520f66ab15d0\",\"target\":\"2162c2d6-fab9-46ac-8741-ad3e9575a36b\"},{\"type\":\"buttonEdge\",\"id\":\"4c6173e6-f7e0-47fc-992b-0c2625ac7c05\",\"source\":\"2162c2d6-fab9-46ac-8741-ad3e9575a36b\",\"target\":\"69b94a53-7a53-4135-b153-71fb8b512081\"},{\"type\":\"buttonEdge\",\"id\":\"b22ee45f-f2e0-4edb-bb03-8a61b407c82b\",\"source\":\"911b0e7a-75bf-49f2-bfb1-2e23731eed16\",\"target\":\"cd971280-ae71-463f-b1bc-776284915f5b\"},{\"type\":\"buttonEdge\",\"id\":\"e1ade524-97a0-44b9-85de-87956ff6f173\",\"source\":\"69b94a53-7a53-4135-b153-71fb8b512081\",\"target\":\"5b7b0a55-2d8a-4040-b7ab-43a65d103307\"},{\"type\":\"buttonEdge\",\"id\":\"790dd1c1-860c-41c0-b07a-cdf14e1d6457\",\"source\":\"5b7b0a55-2d8a-4040-b7ab-43a65d103307\",\"target\":\"911b0e7a-75bf-49f2-bfb1-2e23731eed16\"}]}"
                },
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-08-28T06:12:09.9+00:00",
                "updated_at": "2024-08-28T06:12:09.9+00:00",
                "id": "oG4U5vX7MV3nGma2-rNLcbqLGr7pNkoda"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData)!;
        var expected = new Dictionary<string, int>()
        {
            [WorkflowStepCategories.Messaging] = 3,
            [WorkflowStepCategories.Contact] = 5,
            [WorkflowStepCategories.CustomObject] = 1,
            [WorkflowStepCategories.Conversation] = 4,
            [WorkflowStepCategories.ExternalIntegration] = 2,
            [WorkflowStepCategories.SalesforceIntegration] = 1,
        };

        // Act
        var result = _workflowStepCategoryProvider.GetStepCategoryCountDict(workflow.Steps);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result, Is.EquivalentTo(expected));
            });
    }

    [Test]
    public void GetStepIdCategoryMapping_GivenValidWorkflowWithSteps_ShouldReturnStepIdCategoryDict()
    {
        // Arrange
        const string testData =
            """
            {
                "workflow_id": "oG4U5vX7MV3nGma2",
                "workflow_versioned_id": "oG4U5vX7MV3nGma2-rNLcbqLGr7pNkoda",
                "name": "Untitled",
                "workflow_type": "normal",
                "triggers": {
                    "message_received": {
                        "condition": "{{ ([\"***********\"] | array.contains event_body.channel_id) && ((((event_body.contact[\"1824e844-348d-48f9-9746-4945dc380fff\"] | string.downcase) | string.contains \"189376147\"))) }}"
                    }
                },
                "workflow_enrollment_settings": {
                    "can_enroll_only_once": false,
                    "can_enroll_again_on_failure_only": false,
                    "can_enroll_in_parallel": false
                },
                "workflow_schedule_settings": {
                    "schedule_type": "none"
                },
                "steps": [
                    {
                        "assign": {
                            "contact": "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact) }}",
                            "conversation": "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_conversation) }}",
                            "lists": "{{ (trigger_event_body.contact_id | string.empty) ? {} : (trigger_event_body.contact_id | sleekflow.get_contact_lists) }}"
                        },
                        "id": "setup-contact-and-conversation",
                        "name": "setup",
                        "next_step_id": null
                    },
                    {
                        "assign": {
                            "contactOwner": "{{ (usr_var_dict.contact[\"ContactOwner\"] | string.empty) ? \"\" : (usr_var_dict.contact[\"ContactOwner\"] | sleekflow.get_staff) }}"
                        },
                        "id": "setup-contact-owner",
                        "name": "setup-contact-owner",
                        "next_step_id": null
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"text_message\":{\"text\":\"Hello\"}} }}",
                            "message_type__expr": "text"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "a0068961-e3ed-4f4a-86bd-16ed11fec163",
                        "name": "Action 1",
                        "next_step_id": "3929124a-1534-45ae-b417-008b4ef6f86b"
                    },
                    {
                        "args": {
                            "channel__expr": "whatsappcloudapi",
                            "from_to": {
                                "from_channel_id__expr": null,
                                "from_facebook_page_id__expr": null,
                                "from_facebook_post_id__expr": null,
                                "from_instagram_media_id__expr": null,
                                "from_instagram_page_id__expr": null,
                                "from_phone_number__expr": "{{ trigger_event_body.channel_id }}",
                                "to_contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                                "to_facebook_comment_id__expr": null,
                                "to_facebook_id__expr": null,
                                "to_instagram_comment_id__expr": null,
                                "to_instagram_id__expr": null,
                                "to_phone_number__expr": null
                            },
                            "message_body__expr": "{{ {\"image_message\":{\"link\":\"https://sfd6c2495.blob.core.windows.net/image-container/b6d7e442-38ae-4b9a-b100-2951729768bc/JQSP0ykdmrp1XkZ?sv=2024-08-04&se=9999-12-31T23%3A59%3A59Z&sr=b&sp=r&sig=d3Udp27gTEioh%2Fuc55KjNOa0O7hNJ%2B2VeiVL%2BU6cy1g%3D\",\"filename\":\"13b.png\"}} }}",
                            "message_type__expr": "image"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.send-message",
                        "id": "3929124a-1534-45ae-b417-008b4ef6f86b",
                        "name": "Action 2",
                        "next_step_id": "85e9198e-1fd5-43a7-abc4-9ef79f140e72"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "content__expr": "{{ \"this is a note\" }}"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.add-internal-note-to-contact",
                        "id": "85e9198e-1fd5-43a7-abc4-9ef79f140e72",
                        "name": "Action 3",
                        "next_step_id": "6308320d-bb46-4aa2-b8a8-520f66ab15d0"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "properties__key_expr_dict": {
                                "LastName": "{{ \"abc\" }}"
                            }
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-properties",
                        "id": "6308320d-bb46-4aa2-b8a8-520f66ab15d0",
                        "name": "Action 4",
                        "next_step_id": "2162c2d6-fab9-46ac-8741-ad3e9575a36b"
                    },
                    {
                        "args": {
                            "add_list_ids__expr": "{{ [\"6157\"] }}",
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_list_ids__expr": null,
                            "set_list_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-list-relationships",
                        "id": "2162c2d6-fab9-46ac-8741-ad3e9575a36b",
                        "name": "Action 13",
                        "next_step_id": "69b94a53-7a53-4135-b153-71fb8b512081"
                    },
                    {
                        "args": {
                            "add_list_ids__expr": null,
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_list_ids__expr": "{{ [\"6157\"] }}",
                            "set_list_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-list-relationships",
                        "id": "69b94a53-7a53-4135-b153-71fb8b512081",
                        "name": "Action 14",
                        "next_step_id": "5b7b0a55-2d8a-4040-b7ab-43a65d103307"
                    },
                    {
                        "args": {
                            "add_labels__expr": "{{ [\"f\"] }}",
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_labels__expr": null,
                            "set_labels__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-label-relationships",
                        "id": "5b7b0a55-2d8a-4040-b7ab-43a65d103307",
                        "name": "Action 16",
                        "next_step_id": "911b0e7a-75bf-49f2-bfb1-2e23731eed16"
                    },
                    {
                        "args": {
                            "add_labels__expr": null,
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_labels__expr": "{{ [\"f\"] }}",
                            "set_labels__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-label-relationships",
                        "id": "911b0e7a-75bf-49f2-bfb1-2e23731eed16",
                        "name": "Action 15",
                        "next_step_id": "cd971280-ae71-463f-b1bc-776284915f5b"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ ('{{ usr_var_dict.contact[\"PhoneNumber\"] ?? \"\" }}' | template.eval | sleekflow.get_or_create_contact_id_by_phone_number) }}",
                            "primary_property_value__expr": "{{ \"123\" }}",
                            "property_values__key_expr_dict": {
                                "3Zi772x2N7ay53e": "{{ \"3131\" }}",
                                "Wpi114a4X1031qM": "{{ 123 }}",
                                "Wpi114a4X1kNb2l": "{{ \"131414\" }}",
                                "XAiRR8B8GRL2l0Q": "{{ \"2025-08-02\" }}",
                                "XAiRR8B8GRQMR1m": "{{ 131 }}",
                                "Z1iLLklbpqZxz0Y": "{{ \"a\" }}",
                                "qkiAA5k58AeXxdq": "{{ \"true\" }}",
                                "z8iMMAdAbML4yP1": "{{ \"3131\" }}"
                            },
                            "schema_id__expr": "{{ \"aoidnp68Kz8JLMY\" }}"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.create-schemaful-object",
                        "id": "cd971280-ae71-463f-b1bc-776284915f5b",
                        "name": "Action 5",
                        "next_step_id": "8dfdb6a8-673b-4f23-a013-b6b0bb78012d"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "status__expr": "{{ \"open\" }}"
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-conversation-status",
                        "id": "8dfdb6a8-673b-4f23-a013-b6b0bb78012d",
                        "name": "Action 6",
                        "next_step_id": "86112502-d098-48f8-8677-c0c228ed4eeb"
                    },
                    {
                        "args": {
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "staff_ids__expr": "{{ [\"70be7cd2-5b3d-4aad-8270-04e9fb553252\"] }}",
                            "strategy": "RoundRobbin_StaffOnly",
                            "team_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-owner-relationships",
                        "id": "86112502-d098-48f8-8677-c0c228ed4eeb",
                        "name": "Action 7",
                        "next_step_id": "b353d4d4-0efd-49f9-970b-7e63935926bb"
                    },
                    {
                        "args": {
                            "add_staff_ids__expr": "{{ [\"8f60e313-3eb1-4816-a434-f61848e4c05a\"] }}",
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_staff_ids__expr": null,
                            "set_staff_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-collaborator-relationships",
                        "id": "b353d4d4-0efd-49f9-970b-7e63935926bb",
                        "name": "Action 8",
                        "next_step_id": "0448b77c-0ed9-4a4d-8db1-47b307cfadbf"
                    },
                    {
                        "args": {
                            "add_staff_ids__expr": null,
                            "contact_id__expr": "{{ trigger_event_body.contact_id ?? usr_var_dict.contact.id }}",
                            "remove_staff_ids__expr": "{{ [\"8f60e313-3eb1-4816-a434-f61848e4c05a\"] }}",
                            "set_staff_ids__expr": null
                        },
                        "assign": null,
                        "call": "sleekflow.v1.update-contact-collaborator-relationships",
                        "id": "0448b77c-0ed9-4a4d-8db1-47b307cfadbf",
                        "name": "Action 9",
                        "next_step_id": "ea9c4deb-da84-4248-a866-f9dbbe6e01aa"
                    },
                    {
                        "args": {
                            "body__expr": "{{ trigger_event_body | json.serialize }}",
                            "body__key_expr_dict": null,
                            "headers__key_expr_dict": {
                                "Content-Type": "application/json",
                                "X-Method": "{{ \"message_webhook\" }}"
                            },
                            "url__expr": "https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7"
                        },
                        "assign": null,
                        "call": "http.post",
                        "id": "ea9c4deb-da84-4248-a866-f9dbbe6e01aa",
                        "name": "Action 10",
                        "next_step_id": "7e150988-36fc-42a7-8aff-31c079d82076"
                    },
                    {
                        "args": {
                            "body__expr": "{{ \"hello from POST request\" }}",
                            "body__key_expr_dict": null,
                            "headers__key_expr_dict": {
                                "Content-Type": "text/plain"
                            },
                            "url__expr": "{{ \"https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7\" }}"
                        },
                        "assign": null,
                        "call": "http.post",
                        "id": "7e150988-36fc-42a7-8aff-31c079d82076",
                        "name": "Action 11",
                        "next_step_id": "1b25cad1-7f12-4491-82f7-420bd1b0188f"
                    },
                    {
                        "args": {
                            "conditions__expr": "{{ [{\"field_name\":\"Id\",\"operator\":\"isEqualTo\",\"value\":\"{{ \\\"123\\\" }}\"}] }}",
                            "is_custom_object__expr": "{{ \"false\" }}",
                            "object_type__expr": "{{ \"Account\" }}",
                            "salesforce_connection_id__expr": "{{ \"xMRfL4Y7xzN1Z4y4\" }}"
                        },
                        "assign": {
                            "312312": "{{ (sys_var_dict[\"1b25cad1-7f12-4491-82f7-420bd1b0188f\"] | json.deserialize).Name }}"
                        },
                        "call": "sleekflow.v1.search-salesforce-object",
                        "id": "1b25cad1-7f12-4491-82f7-420bd1b0188f",
                        "name": "Action 12",
                        "next_step_id": "ede0112c-5796-4b94-a069-bf2cc9121f52"
                    },
                    {
                        "assign": null,
                        "id": "ede0112c-5796-4b94-a069-bf2cc9121f52",
                        "name": "end",
                        "next_step_id": null
                    }
                ],
                "activation_status": "Draft",
                "metadata": {
                    "v4": "{\"nodes\":[{\"id\":\"a6b4ecc9-c83d-4478-8d63-6093ef05f86f\",\"position\":{\"x\":0,\"y\":0},\"data\":{\"category\":\"start\",\"formValues\":{\"isFilterByCondition\":true,\"conditions\":[{\"conditionType\":\"contactProperty\",\"contactProperty\":{\"id\":\"1824e844-348d-48f9-9746-4945dc380fff\",\"label\":\"Phone Number\",\"type\":\"PhoneNumber\"},\"operator\":\"contains\",\"value\":\"189376147\"}],\"triggerType\":\"incoming-messages\",\"channels\":[{\"id\":\"***********\",\"channelIdentityId\":\"***********\",\"subId\":206,\"name\":\"Flow Builder Test UAT\",\"channel\":\"whatsappcloudapi\",\"messagingHubWabaId\":\"amUx8pKxvn051X\",\"facebookWabaBusinessId\":\"3305169899744267\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"incoming-messages\",\"positionAbsolute\":{\"x\":0,\"y\":0},\"width\":300,\"height\":216,\"selected\":false},{\"id\":\"a0068961-e3ed-4f4a-86bd-16ed11fec163\",\"type\":\"sendMessage\",\"position\":{\"x\":0,\"y\":336},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 1\",\"channelType\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"actionType\":\"sendMessage\",\"messageType\":\"text\",\"message\":\"Hello\",\"messageParams\":[],\"templateTitle\":\"\",\"headerText\":\"\",\"media\":[],\"footerText\":\"\",\"buttonType\":\"NONE\",\"dynamicUrlSuffix\":\"\",\"buttonList\":[],\"listTitle\":\"\",\"sectionList\":[],\"isButtonAsBranch\":false,\"isSaveReply\":false,\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\",\"saveResponseAsField\":{\"id\":\"\",\"value\":\"\",\"displayName\":\"\"},\"saveResponseAsVariable\":\"\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":336},\"width\":300,\"height\":202},{\"id\":\"3929124a-1534-45ae-b417-008b4ef6f86b\",\"type\":\"sendMedia\",\"position\":{\"x\":0,\"y\":658},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 2\",\"actionType\":\"sendMedia\",\"channelLogic\":\"INCOMING_CHANNEL\",\"channel\":\"\",\"media\":[{\"name\":\"13b.png\",\"preview\":\"https://sfd6c2495.blob.core.windows.net/image-container/b6d7e442-38ae-4b9a-b100-2951729768bc/JQSP0ykdmrp1XkZ?sv=2024-08-04&se=9999-12-31T23%3A59%3A59Z&sr=b&sp=r&sig=d3Udp27gTEioh%2Fuc55KjNOa0O7hNJ%2B2VeiVL%2BU6cy1g%3D\",\"type\":\"image\",\"size\":1436767}],\"isMessageTimeout\":false,\"timeoutDuration\":5,\"timeoutUnit\":\"minute\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":658},\"width\":300,\"height\":364},{\"id\":\"85e9198e-1fd5-43a7-abc4-9ef79f140e72\",\"type\":\"internalNote\",\"position\":{\"x\":0,\"y\":1142},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 3\",\"defaultTitle\":\"\",\"actionType\":\"internalNote\",\"messageContent\":\"this is a note\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":1142},\"width\":300,\"height\":182},{\"id\":\"6308320d-bb46-4aa2-b8a8-520f66ab15d0\",\"type\":\"updateContactProperties\",\"position\":{\"x\":0,\"y\":1444},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 4\",\"actionType\":\"updateContactProperties\",\"properties\":[{\"property\":{\"id\":\"LastName\",\"label\":\"Last name\",\"type\":\"SingleLineText\",\"options\":[]},\"value\":\"abc\",\"valueLabel\":\"\"}]},\"formState\":{\"errors\":{\"properties\":[{\"value\":{\"message\":\"This is required\",\"type\":\"required\",\"ref\":{}}}]},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":1444},\"width\":300,\"height\":148},{\"id\":\"2162c2d6-fab9-46ac-8741-ad3e9575a36b\",\"type\":\"addToList\",\"position\":{\"x\":0,\"y\":1712},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 13\",\"actionType\":\"addToList\",\"lists\":[{\"id\":\"6157\",\"listName\":\"Hong List\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":1712},\"width\":300,\"height\":188},{\"id\":\"69b94a53-7a53-4135-b153-71fb8b512081\",\"type\":\"removeFromList\",\"position\":{\"x\":0,\"y\":2020},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 14\",\"actionType\":\"removeFromList\",\"removeLogic\":\"specific\",\"lists\":[{\"id\":\"6157\",\"listName\":\"Hong List\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2020},\"width\":300,\"height\":188},{\"id\":\"5b7b0a55-2d8a-4040-b7ab-43a65d103307\",\"type\":\"addLabel\",\"position\":{\"x\":0,\"y\":2328},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 16\",\"actionType\":\"addLabel\",\"labels\":[{\"id\":\"a27ffb3b-1d8c-44cb-93d1-574cad1d02d9\",\"hashtag\":\"f\",\"hashTagColor\":\"Blue\",\"count\":7,\"hashTagType\":\"Normal\",\"label\":\"f\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2328},\"width\":300,\"height\":184},{\"id\":\"911b0e7a-75bf-49f2-bfb1-2e23731eed16\",\"type\":\"removeLabel\",\"position\":{\"x\":0,\"y\":2632},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 15\",\"actionType\":\"removeLabel\",\"removeLogic\":\"specific\",\"labels\":[{\"id\":\"a27ffb3b-1d8c-44cb-93d1-574cad1d02d9\",\"hashtag\":\"f\",\"hashTagColor\":\"Blue\",\"count\":7,\"hashTagType\":\"Normal\",\"label\":\"f\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2632},\"width\":300,\"height\":184},{\"id\":\"cd971280-ae71-463f-b1bc-776284915f5b\",\"type\":\"createCustomObjectRecord\",\"position\":{\"x\":0,\"y\":2936},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 5\",\"actionType\":\"createCustomObjectRecord\",\"customObjectId\":\"aoidnp68Kz8JLMY\",\"contactNumber\":\"{{contact.PhoneNumber}}\",\"properties\":[{\"type\":\"number\",\"value\":\"123\",\"property\":{\"id\":\"Wpi114a4X1031qM\",\"name\":\"number\",\"type\":\"number\"}},{\"type\":\"decimalNumber\",\"value\":\"131\",\"property\":{\"id\":\"XAiRR8B8GRQMR1m\",\"name\":\"decimal number\",\"type\":\"decimalNumber\"}},{\"type\":\"variable\",\"value\":\"3131\",\"property\":{\"id\":\"z8iMMAdAbML4yP1\",\"name\":\"multi select\",\"type\":\"multiSelection\",\"options\":[{\"id\":\"4Li00dpdY074qN3\",\"value\":\"a\"},{\"id\":\"briooVXVeo5bmlo\",\"value\":\"b\"},{\"id\":\"Ezi112726143ZlK\",\"value\":\"c\"},{\"id\":\"oEi554n4k5Wx7Pp\",\"value\":\"d\"}]}},{\"type\":\"variable\",\"value\":\"3131\",\"property\":{\"id\":\"3Zi772x2N7ay53e\",\"name\":\"single select\",\"type\":\"singleSelection\",\"options\":[{\"id\":\"kKippVPV7pBRXGG\",\"value\":\"a\"},{\"id\":\"A7iGG0e0RGrn1b4\",\"value\":\"b\"}]}},{\"type\":\"variable\",\"value\":\"131414\",\"property\":{\"id\":\"Wpi114a4X1kNb2l\",\"name\":\"date\",\"type\":\"date\"}},{\"type\":\"variable\",\"value\":\"2025-08-02\",\"property\":{\"id\":\"XAiRR8B8GRL2l0Q\",\"name\":\"date time\",\"type\":\"dateTime\"}},{\"type\":\"variable\",\"value\":\"true\",\"property\":{\"id\":\"qkiAA5k58AeXxdq\",\"name\":\"boolean\",\"type\":\"boolean\"}},{\"type\":\"text\",\"value\":\"a\",\"property\":{\"id\":\"Z1iLLklbpqZxz0Y\",\"name\":\"read variable\",\"type\":\"text\"}}],\"optionalProperties\":[],\"primaryProperty\":{\"value\":\"123\",\"isRequired\":true}},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":2936},\"width\":300,\"height\":128},{\"id\":\"8dfdb6a8-673b-4f23-a013-b6b0bb78012d\",\"type\":\"updateConversationStatus\",\"position\":{\"x\":0,\"y\":3184},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 6\",\"actionType\":\"updateConversationStatus\",\"updatedStatus\":\"open\"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":3184},\"width\":300,\"height\":96},{\"id\":\"86112502-d098-48f8-8677-c0c228ed4eeb\",\"type\":\"assignTo\",\"position\":{\"x\":0,\"y\":3400},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 7\",\"actionType\":\"assignTo\",\"assignTo\":\"specificUser\",\"assignees\":[{\"id\":\"70be7cd2-5b3d-4aad-8270-04e9fb553252\",\"name\":\"Yi Zhi Hong\",\"avatar\":\"https://sleekflow-core-dev-e6d7dyf5drg4eag5.z01.azurefd.net/comapny/profilepicture/96091217-9589-457b-83e4-60041921385b\"}],\"assignLogic\":\"\",\"team\":{\"id\":\"\",\"teamName\":\"\"}},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":3400},\"width\":300,\"height\":180},{\"id\":\"b353d4d4-0efd-49f9-970b-7e63935926bb\",\"type\":\"addCollaborator\",\"position\":{\"x\":0,\"y\":3700},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 8\",\"actionType\":\"addCollaborator\",\"collaborators\":[{\"id\":\"8f60e313-3eb1-4816-a434-f61848e4c05a\",\"name\":\"Allison K\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":3700},\"width\":300,\"height\":196},{\"id\":\"0448b77c-0ed9-4a4d-8db1-47b307cfadbf\",\"type\":\"removeCollaborator\",\"position\":{\"x\":0,\"y\":4016},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 9\",\"actionType\":\"removeCollaborator\",\"removeLogic\":\"specific\",\"collaborators\":[{\"id\":\"8f60e313-3eb1-4816-a434-f61848e4c05a\",\"name\":\"Allison K\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4016},\"width\":300,\"height\":196},{\"id\":\"ea9c4deb-da84-4248-a866-f9dbbe6e01aa\",\"type\":\"messageWebhook\",\"position\":{\"x\":0,\"y\":4332},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 10\",\"webhookURL\":\"https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7\",\"actionType\":\"messageWebhook\",\"isEnableHeaders\":true,\"headers\":[{\"key\":\"X-Method\",\"value\":\"message_webhook\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4332},\"width\":300,\"height\":192},{\"id\":\"7e150988-36fc-42a7-8aff-31c079d82076\",\"type\":\"httpRequest\",\"position\":{\"x\":0,\"y\":4644},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 11\",\"actionType\":\"httpRequest\",\"handleFailure\":false,\"httpMethod\":\"POST\",\"httpRequestURL\":\"https://webhook.site/7d7e4bb9-b2f2-4071-82ef-a5e18daae0a7\",\"httpRequestHeaders\":[{\"key\":\"\",\"value\":\"\"}],\"httpRequestQueryString\":[{\"key\":\"\",\"value\":\"\"}],\"isHttpBodyOn\":true,\"httpRequestBody\":{\"bodyType\":\"raw\",\"contentType\":\"text/plain\",\"bodyValue\":\"hello from POST request\"},\"saveHttpResponse\":[{\"variable\":\"\",\"expression\":\"\"}]},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4644},\"width\":300,\"height\":172},{\"id\":\"1b25cad1-7f12-4491-82f7-420bd1b0188f\",\"type\":\"mapSalesforceAccount\",\"position\":{\"x\":0,\"y\":4936},\"selected\":false,\"data\":{\"category\":\"action\",\"formValues\":{\"title\":\"Action 12\",\"actionType\":\"mapSalesforceAccount\",\"organization\":{\"id\":\"xMRfL4Y7xzN1Z4y4\",\"name\":\"SleekFlow Technologies Inc.\",\"environment\":\"production\",\"organizationId\":\"sleekflowtechnologiesinc.my.salesforce.com\",\"status\":\"connected\"},\"entityType\":\"Account\",\"searchQueries\":[{\"property\":{\"name\":\"Id\",\"propertyType\":\"Account\",\"type\":\"id\",\"options\":[],\"label\":\"Account ID\"},\"operator\":\"isEqualTo\",\"value\":\"123\"}],\"saveAsFlowVariables\":[{\"property\":{\"name\":\"Name\",\"propertyType\":\"Account\",\"type\":\"string\",\"options\":[],\"mandatory\":true,\"label\":\"Account Name\"},\"expression\":\"312312\"}]},\"formState\":{\"errors\":{},\"isValid\":false,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"positionAbsolute\":{\"x\":0,\"y\":4936},\"width\":300,\"height\":148},{\"id\":\"b578ece4-e24e-43f0-a14f-633bb46cd4f2\",\"position\":{\"x\":0,\"y\":5204},\"data\":{\"category\":\"end\",\"formValues\":{\"endType\":\"here\",\"targetNode\":null,\"title\":\" \"},\"formState\":{\"errors\":{},\"isValid\":true,\"isDirty\":false,\"isValidating\":false},\"validateOnMount\":false},\"type\":\"end\",\"positionAbsolute\":{\"x\":0,\"y\":5204},\"width\":300,\"height\":116,\"selected\":false}],\"edges\":[{\"type\":\"buttonEdge\",\"id\":\"87ac3846-98d9-490d-9707-60ab39b00dbc\",\"source\":\"a6b4ecc9-c83d-4478-8d63-6093ef05f86f\",\"target\":\"a0068961-e3ed-4f4a-86bd-16ed11fec163\"},{\"type\":\"buttonEdge\",\"id\":\"5f429054-5155-4a04-8591-a9c28577f0d8\",\"source\":\"a0068961-e3ed-4f4a-86bd-16ed11fec163\",\"target\":\"3929124a-1534-45ae-b417-008b4ef6f86b\"},{\"type\":\"buttonEdge\",\"id\":\"030016e0-3e83-497a-b336-d06e49b8b584\",\"source\":\"3929124a-1534-45ae-b417-008b4ef6f86b\",\"target\":\"85e9198e-1fd5-43a7-abc4-9ef79f140e72\"},{\"type\":\"buttonEdge\",\"id\":\"07e8bb47-e9e8-4091-99e1-f0a21e4e1b5d\",\"source\":\"85e9198e-1fd5-43a7-abc4-9ef79f140e72\",\"target\":\"6308320d-bb46-4aa2-b8a8-520f66ab15d0\"},{\"type\":\"buttonEdge\",\"id\":\"92f4a4e3-4e59-4b44-aa22-6460fc4b6be2\",\"source\":\"cd971280-ae71-463f-b1bc-776284915f5b\",\"target\":\"8dfdb6a8-673b-4f23-a013-b6b0bb78012d\"},{\"type\":\"buttonEdge\",\"id\":\"187fa276-dda1-49f7-a087-fafb043c0a86\",\"source\":\"8dfdb6a8-673b-4f23-a013-b6b0bb78012d\",\"target\":\"86112502-d098-48f8-8677-c0c228ed4eeb\"},{\"type\":\"buttonEdge\",\"id\":\"5805d97f-a788-4682-8d2b-b0edaeddcfb0\",\"source\":\"86112502-d098-48f8-8677-c0c228ed4eeb\",\"target\":\"b353d4d4-0efd-49f9-970b-7e63935926bb\"},{\"type\":\"buttonEdge\",\"id\":\"16d0f195-1f47-4621-877f-5f6ed4edd15b\",\"source\":\"b353d4d4-0efd-49f9-970b-7e63935926bb\",\"target\":\"0448b77c-0ed9-4a4d-8db1-47b307cfadbf\"},{\"type\":\"buttonEdge\",\"id\":\"5da12d12-e15b-43a7-a71b-3058f52164b1\",\"source\":\"0448b77c-0ed9-4a4d-8db1-47b307cfadbf\",\"target\":\"ea9c4deb-da84-4248-a866-f9dbbe6e01aa\"},{\"type\":\"buttonEdge\",\"id\":\"50fa5b0d-2508-45d5-ba64-53d98927bc6d\",\"source\":\"ea9c4deb-da84-4248-a866-f9dbbe6e01aa\",\"target\":\"7e150988-36fc-42a7-8aff-31c079d82076\"},{\"type\":\"buttonEdge\",\"id\":\"96f242e2-675e-4ab9-b752-7e94de895ea1\",\"source\":\"7e150988-36fc-42a7-8aff-31c079d82076\",\"target\":\"1b25cad1-7f12-4491-82f7-420bd1b0188f\"},{\"type\":\"buttonEdge\",\"id\":\"48f07f5d-25de-4fd3-a81e-06f21f7055d1\",\"source\":\"1b25cad1-7f12-4491-82f7-420bd1b0188f\",\"target\":\"b578ece4-e24e-43f0-a14f-633bb46cd4f2\"},{\"type\":\"buttonEdge\",\"id\":\"d0a03a02-b060-43db-b265-aa41745cdc05\",\"source\":\"6308320d-bb46-4aa2-b8a8-520f66ab15d0\",\"target\":\"2162c2d6-fab9-46ac-8741-ad3e9575a36b\"},{\"type\":\"buttonEdge\",\"id\":\"4c6173e6-f7e0-47fc-992b-0c2625ac7c05\",\"source\":\"2162c2d6-fab9-46ac-8741-ad3e9575a36b\",\"target\":\"69b94a53-7a53-4135-b153-71fb8b512081\"},{\"type\":\"buttonEdge\",\"id\":\"b22ee45f-f2e0-4edb-bb03-8a61b407c82b\",\"source\":\"911b0e7a-75bf-49f2-bfb1-2e23731eed16\",\"target\":\"cd971280-ae71-463f-b1bc-776284915f5b\"},{\"type\":\"buttonEdge\",\"id\":\"e1ade524-97a0-44b9-85de-87956ff6f173\",\"source\":\"69b94a53-7a53-4135-b153-71fb8b512081\",\"target\":\"5b7b0a55-2d8a-4040-b7ab-43a65d103307\"},{\"type\":\"buttonEdge\",\"id\":\"790dd1c1-860c-41c0-b07a-cdf14e1d6457\",\"source\":\"5b7b0a55-2d8a-4040-b7ab-43a65d103307\",\"target\":\"911b0e7a-75bf-49f2-bfb1-2e23731eed16\"}]}"
                },
                "sleekflow_company_id": "b6d7e442-38ae-4b9a-b100-2951729768bc",
                "created_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "updated_by": {
                    "sleekflow_staff_id": "4320",
                    "sleekflow_staff_team_ids": []
                },
                "created_at": "2024-08-28T06:12:09.9+00:00",
                "updated_at": "2024-08-28T06:12:09.9+00:00",
                "id": "oG4U5vX7MV3nGma2-rNLcbqLGr7pNkoda"
            }
            """;

        var workflow = JsonConvert.DeserializeObject<ProxyWorkflow>(testData)!;
        var expected = new Dictionary<string, string>()
        {
            ["a0068961-e3ed-4f4a-86bd-16ed11fec163"] = WorkflowStepCategories.Messaging, // Send message
            ["3929124a-1534-45ae-b417-008b4ef6f86b"] = WorkflowStepCategories.Messaging, // Send media
            ["85e9198e-1fd5-43a7-abc4-9ef79f140e72"] = WorkflowStepCategories.Messaging, // Send internal note
            ["6308320d-bb46-4aa2-b8a8-520f66ab15d0"] = WorkflowStepCategories.Contact, // Update contact properties
            ["2162c2d6-fab9-46ac-8741-ad3e9575a36b"] = WorkflowStepCategories.Contact, // Add to list
            ["69b94a53-7a53-4135-b153-71fb8b512081"] = WorkflowStepCategories.Contact, // Remove from list
            ["5b7b0a55-2d8a-4040-b7ab-43a65d103307"] = WorkflowStepCategories.Contact, // Add label
            ["911b0e7a-75bf-49f2-bfb1-2e23731eed16"] = WorkflowStepCategories.Contact, // Remove label
            ["cd971280-ae71-463f-b1bc-776284915f5b"] = WorkflowStepCategories.CustomObject, // Create schemaful custom object
            ["8dfdb6a8-673b-4f23-a013-b6b0bb78012d"] = WorkflowStepCategories.Conversation, // Update conversation status
            ["86112502-d098-48f8-8677-c0c228ed4eeb"] = WorkflowStepCategories.Conversation, // Update contact owner
            ["b353d4d4-0efd-49f9-970b-7e63935926bb"] = WorkflowStepCategories.Conversation, // Add collaborator
            ["0448b77c-0ed9-4a4d-8db1-47b307cfadbf"] = WorkflowStepCategories.Conversation, // Remove collaborator
            ["ea9c4deb-da84-4248-a866-f9dbbe6e01aa"] = WorkflowStepCategories.ExternalIntegration, // Post message webhook
            ["7e150988-36fc-42a7-8aff-31c079d82076"] = WorkflowStepCategories.ExternalIntegration, // HTTP POST webhook
            ["1b25cad1-7f12-4491-82f7-420bd1b0188f"] = WorkflowStepCategories.SalesforceIntegration, // Search salesforce object
        };

        // Act
        var result = _workflowStepCategoryProvider.GetStepIdCategoryMapping(workflow.Steps);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(result, Is.Not.Null);
                Assert.That(result, Is.EquivalentTo(expected));
            });
    }

    [Test]
    [TestCase("conversation_status_changed", WorkflowStepCategories.Conversation)]
    [TestCase("contact_label_relationships_changed", WorkflowStepCategories.Contact)]
    [TestCase("contact_list_relationships_changed", WorkflowStepCategories.Contact)]
    [TestCase("contact_updated", WorkflowStepCategories.Contact)]
    [TestCase("message_received", WorkflowStepCategories.Messaging)]
    [TestCase("message_sent", WorkflowStepCategories.Messaging)]
    [TestCase("webhook", WorkflowStepCategories.ExternalIntegration)]
    [TestCase("facebook_post_comment_received", WorkflowStepCategories.FacebookIntegration)]
    [TestCase("instagram_media_comment_received", WorkflowStepCategories.InstagramIntegration)]
    [TestCase("salesforce_account_updated", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_account_created", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_contact_updated", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_contact_created", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_lead_updated", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_lead_created", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_opportunity_updated", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_opportunity_created", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_campaign_updated", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_campaign_created", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_custom_object_created", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_custom_object_updated", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("click_to_whatsapp_ads_message_received", WorkflowStepCategories.Messaging)]
    [TestCase("schemaful_object_created", WorkflowStepCategories.CustomObject)]
    [TestCase("schemaful_object_updated", WorkflowStepCategories.CustomObject)]
    [TestCase("salesforce_account_enrolled", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_contact_enrolled", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_lead_enrolled", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_opportunity_enrolled", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("salesforce_campaign_enrolled", WorkflowStepCategories.SalesforceIntegration)]
    [TestCase("contact_enrolled", WorkflowStepCategories.Contact)]
    [TestCase("contact_manually_enrolled", WorkflowStepCategories.Contact)]
    public void GetWorkflowTriggerStepCategory_GivenValidWorkflowTrigger_ShouldReturnCorrectTriggerStepCategory(
        string triggerType,
        string expectedCategory)
    {
        // Arrange
        var testData =
            $$$"""
            {
                "{{{ triggerType }}}": {
                    "condition": "{{ true }}"
                }
            }
            """;

        var trigger = JsonConvert.DeserializeObject<WorkflowTriggers>(testData)!;

        // Act
        var triggerStepCategory = _workflowStepCategoryProvider.GetWorkflowTriggerStepCategory(trigger);

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(triggerStepCategory, Is.EqualTo(expectedCategory));
            });
    }

    [Test]
    public void GetWorkflowTriggerStepCategory_EnsureAllWorkflowTriggersHaveCategory()
    {
        // Arrange
        var triggerTypes = typeof(WorkflowTriggers)
            .GetProperties(BindingFlags.Instance | BindingFlags.Public | BindingFlags.GetProperty)
            .Select(p => p.GetCustomAttribute<JsonPropertyAttribute>())
            .Select(a => a?.PropertyName)
            .ToList();

        foreach (var triggerType in triggerTypes)
        {
            var testData =
                $$$"""
                   {
                       "{{{ triggerType }}}": {
                           "condition": "{{ true }}"
                       }
                   }
                   """;

            var trigger = JsonConvert.DeserializeObject<WorkflowTriggers>(testData)!;

            // Act
            Assert.Multiple(
                () =>
                {
                    Assert.That(triggerType, Is.Not.Null);
                    Assert.That(triggerType, Is.Not.Empty);
                    Assert.That(_workflowStepCategoryProvider.GetWorkflowTriggerStepCategory(trigger), Is.Not.Empty);
                });
        }
    }
}