using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiRechargeBalanceEventConsumerDefinition : ConsumerDefinition<OnCloudApiRechargeBalanceEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiRechargeBalanceEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiRechargeBalanceEventConsumer : IConsumer<OnCloudApiRechargeBalanceEvent>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<OnCloudApiRechargeBalanceEventConsumer> _logger;

    public OnCloudApiRechargeBalanceEventConsumer(
        ILogger<OnCloudApiRechargeBalanceEventConsumer> logger,
        IWabaService wabaService)
    {
        _logger = logger;
        _wabaService = wabaService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiRechargeBalanceEvent> context)
    {
        var onCloudApiRechargeBalanceEvent = context.Message;
        var facebookBusinessId = onCloudApiRechargeBalanceEvent.FacebookBusinessId;
        var facebookWabaId = onCloudApiRechargeBalanceEvent.FacebookWabaId;
        var wabas = new List<Waba>();

        if (!string.IsNullOrEmpty(facebookWabaId))
        {
            wabas.Add(await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId));
        }
        else
        {
            wabas.AddRange(await _wabaService.GetWabaWithFacebookBusinessIdAsync(facebookBusinessId));
        }

        var blockedWabas = wabas
            .Where(w => w.MessagingFunctionLimitation == MessagingFunctionLimitationType.BlockAll).ToList();

        foreach (var waba in blockedWabas)
        {
            try
            {
                if (await _wabaService.UnblockOrBlockWabaAsync(waba.Id, waba.FacebookWabaId, true) == 0)
                {
                    throw new SfInternalErrorException("Unable to UnblockOrBlockWabaAsync ");
                }
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    exception,
                    "Exception occur during OnCloudApiUnBlockWabaEvent {Waba}/{Exception},",
                    JsonConvert.SerializeObject(waba),
                    JsonConvert.SerializeObject(exception));
            }
        }
    }
}