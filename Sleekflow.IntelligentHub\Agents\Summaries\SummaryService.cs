using Microsoft.SemanticKernel;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Chats;
using Sleekflow.IntelligentHub.Models.Summary;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.Models.Chats;

namespace Sleekflow.IntelligentHub.Agents.Summaries;

public interface ISummaryService
{
    Task<ConversationSummary?> GetConversationSummaryAsync(
        List<SfChatEntry> sfChatEntries,
        string handoverReason,
        string tone = "Professional",
        string responseLevel = "",
        string languageIsoCode = "en",
        Dictionary<string, string>? contactProperties = null);
}

public class SummaryService : ISummaryService, IScopedService
{
    private readonly Kernel _kernel;
    private readonly ILogger _logger;
    private readonly ISummaryPlugin _summaryPlugin;

    public SummaryService(
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILogger<SummaryService> logger)
    {
        _kernel = kernel;
        _logger = logger;
        _summaryPlugin = summaryPlugin;
    }

    public Task<ConversationSummary?> GetConversationSummaryAsync(
        List<SfChatEntry> sfChatEntries,
        string handoverReason,
        string tone = "Professional",
        string responseLevel = "",
        string languageIsoCode = "en",
        Dictionary<string, string>? contactProperties = null)
    {
        // Extract chat history string from the chat entries
        var chatHistoryStr = SfChatEntryUtils.ToChatHistoryStr(sfChatEntries, ["/clear"]);
        _logger.LogInformation("Formatted chat history string: {ChatHistoryStr}", chatHistoryStr);

        // Summarize the chat history string
        return _summaryPlugin.SummarizeConversationForHandoverAsync(
            _kernel,
            chatHistoryStr,
            handoverReason,
            tone,
            responseLevel,
            languageIsoCode,
            contactProperties ?? new Dictionary<string, string>());
    }
}