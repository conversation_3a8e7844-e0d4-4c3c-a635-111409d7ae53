﻿using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.StepExecutors;

public interface IExecutorContext
{
    string? StateId { get; set; }

    string? StepId { get; set; }

    Stack<StackEntry>? StackEntries { get; set; }

    string? WorkerInstanceId { get; set; }
}

public class ExecutorContext
    : IExecutorContext, IScopedService
{
    public string? StateId { get; set; }

    public string? StepId { get; set; }

    public Stack<StackEntry>? StackEntries { get; set; }

    public string? WorkerInstanceId { get; set; }
}