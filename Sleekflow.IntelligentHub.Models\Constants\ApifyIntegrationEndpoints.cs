﻿namespace Sleekflow.IntelligentHub.Models.Constants;

public static class ApifyIntegrationEndpoints
{
    // Actor Endpoints
    public const string ActorRunStartEndpoint = "https://api.apify.com/v2/acts/apify~website-content-crawler/runs";

    // Run Endpoints
    public const string RunDetailGetEndpoint = "https://api.apify.com/v2/actor-runs/apifyRunId";
    public const string DatasetItemsGetEndpoint = "https://api.apify.com/v2/datasets/apifyDatasetId/items";
    public const string DatasetDetailGetEndpoint = "https://api.apify.com/v2/datasets/apifyDatasetId";
    public const string RunAbortEndpoint = "https://api.apify.com/v2/actor-runs/apifyRunId/abort";
    public const string RunResurrectEndpoint = "https://api.apify.com/v2/actor-runs/apifyRunId/resurrect";

    // Task Endpoints
    public const string TaskWebhookCreateEndpoint = "https://api.apify.com/v2/webhooks";
    public const string TaskManageEndpoint = "https://api.apify.com/v2/actor-tasks/apifyTaskId";
    public const string TaskCreateEndpoint = "https://api.apify.com/v2/actor-tasks";
    public const string TaskStartEndpoint = "https://api.apify.com/v2/actor-tasks/apifyTaskId/runs";

    // Scheduler Endpoints
    public const string SchedulerManageEndpoint = "https://api.apify.com/v2/schedules/apifySchedulerId";
    public const string SchedulerCreateEndpoint = "https://api.apify.com/v2/schedules";
}