﻿using MassTransit;
using MassTransit.InMemoryTransport.Configuration;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutions;
using Sleekflow.FlowHub.Steps;
using Sleekflow.Models.Events;

namespace Sleekflow.FlowHub.Executor.Consumers;

public class OnHubspotFailStepActivationEventConsumerDefinition
    : ConsumerDefinition<OnHubspotFailStepActivationEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnHubspotFailStepActivationEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else if (endpointConfigurator is InMemoryReceiveEndpointConfiguration)
        {
            // do nothing
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnHubspotFailStepActivationEventConsumer : IConsumer<OnHubspotFailStepActivationEvent>
{
    private readonly ILogger _logger;
    private readonly IStateService _stateService;
    private readonly IStateAggregator _stateAggregator;
    private readonly IStepExecutorActivator _stepExecutorActivator;
    private readonly IStepExecutionService _stepExecutionService;

    public OnHubspotFailStepActivationEventConsumer(
        ILogger<OnHubspotFailStepActivationEventConsumer> logger,
        IStateService stateService,
        IStateAggregator stateAggregator,
        IStepExecutorActivator stepExecutorActivator,
        IStepExecutionService stepExecutionService)
    {
        _logger = logger;
        _stateService = stateService;
        _stateAggregator = stateAggregator;
        _stepExecutorActivator = stepExecutorActivator;
        _stepExecutionService = stepExecutionService;
    }

    public async Task Consume(ConsumeContext<OnHubspotFailStepActivationEvent> context)
    {
        var message = context.Message;
        _logger.LogInformation(
            "OnHubspotFailStepActivationEventConsumer: Consume: ProxyStateId: {ProxyStateId}, AggregateStepId: {AggregateStepId}",
            message.ProxyStateId,
            message.AggregateStepId);
        var state = await _stateService.GetProxyStateAsync(context.Message.ProxyStateId);

        // Check if the step is already in a terminal state (Failed, Complete, Timeout)
        var stepExecutions = await _stepExecutionService.GetStateStepExecutionsAsync(
            state.Identity.SleekflowCompanyId,
            new List<string> { message.ProxyStateId },
            new List<string> { StepExecutionStatuses.Failed, StepExecutionStatuses.Complete, StepExecutionStatuses.Timeout });

        var existingTerminalExecution = stepExecutions.Find(se => se.StepId == message.AggregateStepId);
        if (existingTerminalExecution != null)
        {
            _logger.LogInformation(
                "Step already in terminal state {Status}. Skipping failure. ProxyStateId: {ProxyStateId}, AggregateStepId: {AggregateStepId}",
                existingTerminalExecution.StepExecutionStatus, message.ProxyStateId, message.AggregateStepId);
            return;
        }

        if (!string.IsNullOrEmpty(message.AggregateStateContext))
        {
            await _stateAggregator.AggregateStateStepBodyAsync(
                state,
                message.AggregateStepId,
                message.AggregateStateContext);
        }

        await _stepExecutorActivator.FailStepAsync(
            message.ProxyStateId,
            message.AggregateStepId,
            message.StackEntries,
            null,
            message.Exception);
    }
}