using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;

/// <summary>
/// A chat history reducer that filters chat messages based on author names.
/// </summary>
/// <param name="authorNames">A list of author names to include in the reduced chat history.</param>
public class AuthorNamesChatHistoryReducer(List<string> authorNames) : IChatHistoryReducer
{
    /// <summary>
    /// Reduces the chat history to include only messages from specified authors or system messages.
    /// </summary>
    /// <param name="chatHistory">The original chat history.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the reduced chat history.</returns>
    public Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        return Task.FromResult<IEnumerable<ChatMessageContent>?>(
            chatHistory
                .Where(h =>
                    (h.AuthorName != null
                     && authorNames.Contains(h.AuthorName))

                    // System messages should not be filtered out
                    || h.Role == AuthorRole.System)
                .ToList());
    }
}