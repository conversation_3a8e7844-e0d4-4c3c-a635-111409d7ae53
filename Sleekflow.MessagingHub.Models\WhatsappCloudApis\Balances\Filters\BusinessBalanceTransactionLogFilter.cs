using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.Commons;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Filters;

public class BusinessBalanceTransactionLogFilter
{
    [Required]
    [JsonProperty("transaction_type")]
    public string TransactionType { get; set; }

    [JsonProperty("facebook_business_id")]
    public string? FacebookBusinessId { get; set; }

    [JsonProperty("facebook_waba_id")]
    public string? FacebookWabaId { get; set; }

    [JsonProperty("top_up_payment_method")]

    public string? TopUpPaymentMethod { get; set; }

    [JsonProperty("conversation_usage_time_range")]
    public TimestampRange? ConversationUsageTimeRange { get; set; }

    [JsonProperty("have_top_up_amount")]
    public bool? HaveCreditAmount { get; set; }

    [JsonProperty("have_used_amount")]
    public bool? HaveUsedAmount { get; set; }

    [JsonProperty("have_markup_amount")]
    public bool? HaveMarkupAmount { get; set; }

    [JsonProperty("is_calculated")]
    public bool? IsCalculated { get; set; }

    [JsonProperty("created_at_range")]
    public DateTimeOffsetRange? CreatedAtRange { get; set; }

    [JsonProperty("updated_at_range")]
    public DateTimeOffsetRange? UpdatedAtRange { get; set; }

    [JsonProperty("order_by")]
    public string? OrderBy { get; set; }

    [JsonProperty("order")]
    [RegularExpression("^(ACS|DESC)$")]
    public string? Order { get; set; }

    [JsonConstructor]
    public BusinessBalanceTransactionLogFilter(
        string transactionType,
        string? facebookBusinessId,
        string? facebookWabaId,
        string? topUpPaymentMethod,
        TimestampRange? conversationUsageTimeRange,
        bool? haveCreditAmount,
        bool? haveUsedAmount,
        bool? haveMarkupAmount,
        bool? isCalculated,
        DateTimeOffsetRange? createdAtRange,
        DateTimeOffsetRange? updatedAtRange,
        string? orderBy,
        string? order)
    {
        TransactionType = transactionType;
        FacebookBusinessId = facebookBusinessId;
        FacebookWabaId = facebookWabaId;
        TopUpPaymentMethod = topUpPaymentMethod;
        ConversationUsageTimeRange = conversationUsageTimeRange;
        HaveCreditAmount = haveCreditAmount;
        HaveUsedAmount = haveUsedAmount;
        HaveMarkupAmount = haveMarkupAmount;
        IsCalculated = isCalculated;
        CreatedAtRange = createdAtRange;
        UpdatedAtRange = updatedAtRange;
        OrderBy = orderBy;
        Order = order;
    }

    public BusinessBalanceTransactionLogFilter(
        string transactionType,
        string? facebookBusinessId,
        string? facebookWabaId,
        TimestampRange? conversationUsageTimeRange,
        string? orderBy,
        string? order)
        : this(
            transactionType,
            facebookBusinessId,
            facebookWabaId,
            null,
            conversationUsageTimeRange,
            null,
            null,
            null,
            null,
            null,
            null,
            orderBy,
            order)
    {
    }
}