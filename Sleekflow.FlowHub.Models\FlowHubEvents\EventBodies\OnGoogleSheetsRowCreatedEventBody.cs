﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

[SwaggerInclude]
public class OnGoogleSheetsRowCreatedEventBody : EventBody
{
    [Required]
    [JsonProperty("event_name")]
    public override string EventName
    {
        get { return EventNames.OnGoogleSheetsRowCreated; }
    }

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("spreadsheet_id")]
    public string SpreadsheetId { get; set; }

    [Required]
    [JsonProperty("worksheet_id")]
    public string WorksheetId { get; set; }

    [Required]
    [JsonProperty("row_id")]
    public string RowId { get; set; }

    [Required]
    [JsonProperty("row")]
    public Dictionary<string, object?> Row { get; set; }

    [JsonConstructor]
    public OnGoogleSheetsRowCreatedEventBody(
        DateTimeOffset createdAt,
        string connectionId,
        string spreadsheetId,
        string worksheetId,
        string rowId,
        Dictionary<string, object?> row)
        : base(createdAt)
    {
        ConnectionId = connectionId;
        SpreadsheetId = spreadsheetId;
        WorksheetId = worksheetId;
        RowId = rowId;
        Row = row;
    }
}