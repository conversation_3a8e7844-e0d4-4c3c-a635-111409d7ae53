using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;
using Sleekflow.Validations;

namespace Sleekflow.IntelligentHub.Triggers.Authorized.KnowledgeBases;

[TriggerGroup(
    ControllerNames.KnowledgeBases,
    $"{BasePath.Authorized}",
    [AuthorizationFilterNames.HeadersAuthorizationFuncFilter])]
public class UpdateDocumentAgentAssignmentsForDocument
    : ITrigger<UpdateDocumentAgentAssignmentsForDocument.UpdateDocumentAgentAssignmentsForDocumentInput,
        UpdateDocumentAgentAssignmentsForDocument.UpdateDocumentAgentAssignmentsForDocumentOutput>
{
    private readonly ISleekflowAuthorizationContext _authorizationContext;
    private readonly IKbDocumentService _kbDocumentService;

    public UpdateDocumentAgentAssignmentsForDocument(
        ISleekflowAuthorizationContext authorizationContext,
        IKbDocumentService kbDocumentService)
    {
        _authorizationContext = authorizationContext;
        _kbDocumentService = kbDocumentService;
    }

    public class UpdateDocumentAgentAssignmentsForDocumentInput
    {
        [JsonProperty("document_id")]
        [Required]
        public string DocumentId { get; set; }

        [JsonProperty("agent_ids")]
        [Required]
        [ValidateArray]
        public List<string> AgentIds { get; set; }

        [JsonConstructor]
        public UpdateDocumentAgentAssignmentsForDocumentInput(string documentId, List<string> agentIds)
        {
            DocumentId = documentId;
            AgentIds = agentIds;
        }
    }

    // Empty output class as per specification {}
    public class UpdateDocumentAgentAssignmentsForDocumentOutput
    {
        [JsonConstructor]
        public UpdateDocumentAgentAssignmentsForDocumentOutput()
        {
        }
    }

    public async Task<UpdateDocumentAgentAssignmentsForDocumentOutput> F(
        UpdateDocumentAgentAssignmentsForDocumentInput input)
    {
        var sleekflowCompanyId = _authorizationContext.SleekflowCompanyId!;

        await _kbDocumentService.PatchAgentAssignmentsForDocumentAsync(
            sleekflowCompanyId,
            input.DocumentId,
            input.AgentIds);

        return new UpdateDocumentAgentAssignmentsForDocumentOutput();
    }
}