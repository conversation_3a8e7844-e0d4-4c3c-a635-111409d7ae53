using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.Steps;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors;

public interface ITryCatchStepExecutor : IStepExecutor
{
}

public class TryCatchStepExecutor : GeneralStepExecutor<TryCatchStep>, ITryCatchStepExecutor, IScopedService
{
    private readonly IStepOrchestrationService _stepOrchestrationService;
    private readonly IExecutorContext _executorContext;

    public TryCatchStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IStepOrchestrationService stepOrchestrationService,
        IServiceProvider serviceProvider,
        IExecutorContext executorContext)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stepOrchestrationService = stepOrchestrationService;
        _executorContext = executorContext;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var tryCatchStep = (TryCatchStep) step;

        var newStackEntries = new Stack<StackEntry>(stackEntries);
        newStackEntries.Push(new StackEntry(step.Id, _executorContext.WorkerInstanceId));

        await _stepOrchestrationService.ExecuteTryCatchStepAsync(
            state.Id,
            step.Id,
            tryCatchStep.Try.Step.Id,
            tryCatchStep.Catch.Step.Id,
            newStackEntries);

        // The actions is Delayed Complete
    }
}