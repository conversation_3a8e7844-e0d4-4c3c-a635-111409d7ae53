﻿using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.Events.EventHub;

public interface IEventHubProcessorConfig
{
    string EventHubStorageConnStr { get; }

    string EventHubConsumerGroup { get; }
}

public class EventHubProcessorConfig : IConfig, IEventHubProcessorConfig
{
    public string EventHubStorageConnStr { get; private set; }

    public string EventHubConsumerGroup { get; private set; }

    public EventHubProcessorConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        EventHubStorageConnStr =
            Environment.GetEnvironmentVariable("EVENT_HUB_STORAGE_CONN_STR", target)
            ?? throw new SfMissingEnvironmentVariableException("EVENT_HUB_STORAGE_CONN_STR");
        EventHubConsumerGroup =
            Environment.GetEnvironmentVariable("EVENT_HUB_CONSUMER_GROUP", target)
            ?? throw new SfMissingEnvironmentVariableException("EVENT_HUB_CONSUMER_GROUP");
    }
}