using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Payloads.Models;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Constants;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi.Authorized;

[TriggerGroup(
    ControllerNames.Balances,
    $"{BasePaths.Authorized}",
    [
        AuthorizationFilterNames.HeadersAuthorizationFuncFilter
    ])
]

public class GetConversationAnalytics(
    ISleekflowAuthorizationContext authorizationContext,
    IWabaService wabaService,
    IBusinessBalanceTransactionLogService businessBalanceTransactionLogService)
    : ITrigger<GetConversationAnalytics.GetConversationAnalyticsInput, GetConversationAnalytics.GetConversationAnalyticsOutput>
{
    public class GetConversationAnalyticsInput(
        string wabaId,
        long startTimestamp,
        long endTimestamp,
        string phoneNumber)
    {
        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; } = wabaId;

        [Required]
        [JsonProperty("start_timestamp")]
        [Range(0, long.MaxValue)]
        public long StartTimestamp { get; set; } = startTimestamp;

        [Required]
        [JsonProperty("end_timestamp")]
        [Range(0, long.MaxValue)]
        public long EndTimestamp { get; set; } = endTimestamp;

        [Required]
        [JsonProperty("phone_number")]
        public string PhoneNumber { get; set; } = phoneNumber;
    }

    public class GetConversationAnalyticsOutput(List<ConversationAnalyticsResultData>? conversationAnalytics)
    {
        [JsonProperty("conversation_analytics")]
        public List<ConversationAnalyticsResultData>? ConversationAnalytics = conversationAnalytics;
    }

    public async Task<GetConversationAnalyticsOutput> F(GetConversationAnalyticsInput input)
    {
        var companyId = authorizationContext.SleekflowCompanyId;
        if (companyId == null)
        {
            throw new SfUnauthorizedException();
        }

        var waba = await wabaService.GetWabaOrDefaultAsync(input.WabaId, companyId);
        if (waba is null)
        {
            throw new SfNotFoundObjectException(input.WabaId);
        }

        var result = await businessBalanceTransactionLogService
            .GetConversationAnalyticsAsync(
                waba,
                input.StartTimestamp,
                input.EndTimestamp,
                WhatsappConversationAnalyticGranularityConst.DAILY,
                input.PhoneNumber);

        return new GetConversationAnalyticsOutput(result);
    }
}