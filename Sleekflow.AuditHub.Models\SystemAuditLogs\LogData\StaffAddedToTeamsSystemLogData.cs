using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class StaffAddedToTeamsSystemLogData
{
    [Required]
    [JsonProperty("staff_id")]
    public string StaffId { get; set; }

    [Required]
    [JsonProperty("team_ids")]
    public List<string> TeamIds { get; set; }

    [Required]
    [JsonProperty("team_names")]
    public List<string> TeamNames { get; set; }

    [JsonConstructor]
    public StaffAddedToTeamsSystemLogData(string staffId, List<string> teamIds, List<string> teamNames)
    {
        StaffId = staffId;
        TeamIds = teamIds;
        TeamNames = teamNames;
    }
}