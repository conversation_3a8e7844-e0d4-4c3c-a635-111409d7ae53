using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IDbProcessorConfig
{
    string CosmosChangeFeedEnvId { get; }
}

public class DbProcessorConfig : IConfig, IDbProcessorConfig
{
    public string CosmosChangeFeedEnvId { get; private set; }

    public DbProcessorConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        CosmosChangeFeedEnvId = Environment.GetEnvironmentVariable("COSMOS_CHANGE_FEED_ENV_ID", target) ??
                                throw new SfMissingEnvironmentVariableException("COSMOS_CHANGE_FEED_ENV_ID");
    }
}