﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.Salesforce;

[TriggerGroup(TriggerGroups.InflowActions)]
public class GetLoopThroughSalesforceObjectsProgress : ITrigger
{
    private readonly IProviderSelector _providerSelector;

    public GetLoopThroughSalesforceObjectsProgress(
        IProviderSelector providerSelector)
    {
        _providerSelector = providerSelector;
    }

    public class GetLoopThroughSalesforceObjectsProgressInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public GetLoopThroughSalesforceObjectsProgressInput(
            string sleekflowCompanyId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class GetLoopThroughSalesforceObjectsProgressOutput
    {
        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonProperty("last_update_time")]
        public DateTime LastUpdateTime { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }

        [JsonConstructor]
        public GetLoopThroughSalesforceObjectsProgressOutput(
            int count,
            DateTime lastUpdateTime,
            string status)
        {
            Count = count;
            LastUpdateTime = lastUpdateTime;
            Status = status;
        }
    }

    public async Task<GetLoopThroughSalesforceObjectsProgressOutput?> F(
        GetLoopThroughSalesforceObjectsProgressInput getLoopThroughSalesforceObjectsProgressInput)
    {
        var providerService = _providerSelector.GetProviderService("salesforce-integrator");

        var output = await providerService.GetLoopThroughObjectsProgressAsync(
            getLoopThroughSalesforceObjectsProgressInput.SleekflowCompanyId,
            "salesforce-integrator",
            getLoopThroughSalesforceObjectsProgressInput.FlowHubWorkflowId,
            getLoopThroughSalesforceObjectsProgressInput.FlowHubWorkflowVersionedId);

        if (output is null)
        {
            return null;
        }

        return new GetLoopThroughSalesforceObjectsProgressOutput(
            output.Count,
            output.LastUpdateTime,
            output.Status);
    }
}