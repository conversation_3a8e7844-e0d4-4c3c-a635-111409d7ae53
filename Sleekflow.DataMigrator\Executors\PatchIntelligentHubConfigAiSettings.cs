using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;

namespace Sleekflow.DataMigrator.Executors;

internal class PatchIntelligentHubConfigAiSettings : IExecutor
{
    private readonly DbConfig _dbConfig;
    private readonly CosmosClient _cosmosClient;

    private string? _databaseId;
    private string? _containerId;

    public PatchIntelligentHubConfigAiSettings(DbConfig dbConfig)
    {
        _dbConfig = dbConfig;
        _cosmosClient = new CosmosClient(
            _dbConfig.Endpoint,
            _dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
    }

    public string GetDisplayName()
    {
        return "Patch IntelligentHub Config AiSettings";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        var selectedDatabaseId = Prompt.Select(
            "Select your database",
            databaseIds);

        var containerIds = new List<string>();
        await foreach (var containerProperties in CosmosUtils.GetContainersAsync(_cosmosClient, selectedDatabaseId))
        {
            containerIds.Add(containerProperties.Id);
        }

        var selectedContainerId = Prompt.Select(
            "Select your database",
            containerIds);

        _databaseId = selectedDatabaseId;
        _containerId = selectedContainerId;
    }

    public async Task ExecuteAsync()
    {
        if (!string.IsNullOrEmpty(_containerId))
        {
            var count = await MigrateObjectsAsync(_containerId);
            Console.WriteLine($"Completed {_containerId} for {count} objects");
        }
        else
        {
            Console.WriteLine("Unable to start PatchIntelligentHubConfigAiSettings");
        }
    }

    private async Task<int> MigrateObjectsAsync(
        string containerId)
    {
        var retryPolicy = CosmosUtils.GetDefaultRetryPolicy(containerId);

        var database = _cosmosClient.GetDatabase(_databaseId);
        var container = database.GetContainer(containerId);
        var partitionKeyPaths =
            (await CosmosUtils.GetContainerPartitionKeyPathsAsync(_cosmosClient, _databaseId!, containerId))!;

        var i = 0;
        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(container, "SELECT * FROM root c WHERE c.created_at = c.updated_at AND (IS_DEFINED(c.enable_writing_assistant) = false OR IS_DEFINED(c.enable_smart_reply) = false OR c.enable_smart_reply = false OR c.enable_writing_assistant = false)"),
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 2000
            },
            async (dict, token) =>
            {
                var policyResult =
                    await retryPolicy.ExecuteAndCaptureAsync(async () => await MigrateObjectAsync(dict, container, partitionKeyPaths, token));

                if (policyResult.FinalException != null)
                {
                    throw new Exception("Failed.", policyResult.FinalException);
                }

                Interlocked.Increment(ref i);

                if (i % 1000 == 0)
                {
                    Console.WriteLine("In Progress " + i);
                }
            });

        return i;
    }

    private static async Task<int> MigrateObjectAsync(
        Dictionary<string, object?> dict,
        Container container,
        IReadOnlyList<string> partitionKeyPaths,
        CancellationToken token)
    {
        var partitionKeyBuilder = new PartitionKeyBuilder();
        foreach (var partitionKeyPath in partitionKeyPaths!)
        {
            var keyParts = partitionKeyPath.TrimStart('/').Split('/');
            var currentValue = CosmosUtils.GetValueFromDictionary(dict, keyParts, 0);
            partitionKeyBuilder.Add(currentValue);
        }

        dict["enable_writing_assistant"] = true;
        dict["enable_smart_reply"] = true;

        await container.UpsertItemAsync(
            dict,
            partitionKeyBuilder.Build(),
            new ItemRequestOptions
            {
                EnableContentResponseOnWrite = false,
            },
            token);

        return 1;
    }
}