using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;

public class CreateCreditMemoRequest
{
    public CreateCreditMemoRequest(Entity entity, string externalId, Collection<Element<Item>> creditMemoItem)
    {
        Entity = entity;
        ExternalId = externalId;
        CreditMemoItem = creditMemoItem;
    }

    [JsonProperty("entity")]
    public Entity Entity { get; set; }

    [JsonProperty("externalId")]
    public string ExternalId { get; set; }

    [JsonProperty("item")]
    public Collection<Element<Item>> CreditMemoItem { get; set; }
}