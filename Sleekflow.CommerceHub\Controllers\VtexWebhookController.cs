﻿using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Models.Vtex.Dtos;
using Sleekflow.CommerceHub.RateLimiters;

namespace Sleekflow.CommerceHub.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class VtexWebhookController : ControllerBase
{
    private readonly ILogger<VtexWebhookController> _logger;
    private readonly IVtexEventRateLimitProducer _vtexEventRateLimitProducer;

    public VtexWebhookController(
        ILogger<VtexWebhookController> logger,
        IVtexEventRateLimitProducer vtexEventRateLimitProducer)
    {
        _logger = logger;
        _vtexEventRateLimitProducer = vtexEventRateLimitProducer;
    }

    public record VtexHookAcknowledgementRequest
    {
        [JsonProperty("hookConfig")]
        public string? HookConfig { get; set; }
    }

    /// <summary>
    /// VTEX utilize one endpoint for 2 types of operations:
    /// <br/>1. order hook registration
    /// <br/>2. actual order hooks (with order data overview)
    /// <br/><br/>See https://developers.vtex.com/docs/guides/orders-feed#hook-configuration
    /// </summary>
    [HttpPost]
    [Route("order")]
    public async Task<IActionResult> OrderHook(
        [Required]
        [FromHeader(Name = VtexHttpHeaderNames.SfVtexAuthenticationId)]
        string vtexAuthenticationId,
        [Required]
        [FromHeader(Name = VtexHttpHeaderNames.SfCompanyId)]
        string sleekflowCompanyId,
        CancellationToken cancellationToken = default)
    {
        // process request
        using var reader = new StreamReader(Request.Body);
        var requestBody = await reader.ReadToEndAsync(cancellationToken);

        // first, verify if it is a hook registration request
        var hookRequest = JsonConvert.DeserializeObject<VtexHookAcknowledgementRequest>(requestBody);
        if (hookRequest != null && !string.IsNullOrWhiteSpace(hookRequest.HookConfig))
        {
            _logger.LogInformation(
                "Received VTEX hook registration request. {VtexAuthenticationId} {CompanyId}",
                vtexAuthenticationId,
                sleekflowCompanyId);

            return Ok();
        }

        // if not, then assume it is an order hook
        var orderHook = JsonConvert.DeserializeObject<VtexOrderHookDto>(requestBody);
        if (orderHook == null)
        {
            _logger.LogWarning(
                "Unable to deserialize Order Hook. {VtexAuthenticationId} {CompanyId} {RequestBody}",
                vtexAuthenticationId,
                sleekflowCompanyId,
                requestBody);

            return Ok();
        }

        _logger.LogInformation(
            "Received VTEX order hook for {VtexAuthenticationId} {CompanyId}: {OrderHook}",
            vtexAuthenticationId,
            sleekflowCompanyId,
            orderHook);

        var onVtexOrderHookReceivedEvent = new OnVtexOrderHookReceivedEvent(
            sleekflowCompanyId,
            vtexAuthenticationId,
            orderHook);

        await _vtexEventRateLimitProducer.PublishWithRateLimitAsync(
            onVtexOrderHookReceivedEvent,
            sleekflowCompanyId,
            cancellationToken:cancellationToken);

        return Ok();
    }
}