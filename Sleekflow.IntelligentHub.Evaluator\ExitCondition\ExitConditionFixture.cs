using System.Diagnostics;
using Sleekflow.IntelligentHub.Evaluator.ExitCondition.Methods;

namespace Sleekflow.IntelligentHub.Evaluator.ExitCondition;

public class ExitConditionFixture
{
    private readonly DefaultExitConditionMethod _method = new();

    public async Task<ExitConditionTestResult[]> EvaluateAsync(
        ExitConditionTestCase testCase,
        CancellationToken cancellationToken)
    {
        var stopwatch = Stopwatch.StartNew();

        ExitConditionTestResult output;

        try
        {
            output = await _method.CompleteAsync(
                testCase.ChatMessageContents,
                testCase.ExitConditions);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            throw;
        }

        stopwatch.Stop();

        var result = new ExitConditionTestResult(
            _method.MethodName,
            output.Answer,
            stopwatch.ElapsedMilliseconds);

        return new[] { result };
    }
}