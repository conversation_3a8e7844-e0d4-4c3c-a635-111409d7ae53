﻿using Newtonsoft.Json;

namespace Sleekflow.Integrator.Hubspot.Services.Models;

public class HubspotObjectsSearchInput
{
    public class Filter
    {
        [JsonProperty("propertyName")]
        public string PropertyName { get; set; }

        [JsonProperty("operator")]
        public string Operator { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonConstructor]
        public Filter(string propertyName, string @operator, string value)
        {
            PropertyName = propertyName;
            Operator = @operator;
            Value = value;
        }
    }

    public class FilterGroup
    {
        [JsonProperty("filters")]
        public List<Filter> Filters { get; set; }

        [JsonConstructor]
        public FilterGroup(List<Filter> filters)
        {
            Filters = filters;
        }
    }

    public class Sort
    {
        [JsonProperty("propertyName")]
        public string PropertyName { get; set; }

        [JsonProperty("direction")]
        public string Direction { get; set; }

        [JsonConstructor]
        public Sort(string propertyName, string direction)
        {
            PropertyName = propertyName;
            Direction = direction;
        }
    }

    [JsonProperty("filterGroups")]
    [System.ComponentModel.DataAnnotations.Required]
    public List<FilterGroup> FilterGroups { get; set; }

    [JsonProperty("sorts")]
    [System.ComponentModel.DataAnnotations.Required]
    public List<Sort> Sorts { get; set; }

    [JsonProperty("limit")]
    [System.ComponentModel.DataAnnotations.Required]
    public int Limit { get; set; }

    [JsonProperty("after")]
    public string? After { get; set; }

    [JsonProperty("properties")]
    [System.ComponentModel.DataAnnotations.Required]
    public List<string> Properties { get; set; }

    [JsonConstructor]
    public HubspotObjectsSearchInput(
        List<FilterGroup> filterGroups,
        List<Sort> sorts,
        int limit,
        string? after,
        List<string> properties)
    {
        FilterGroups = filterGroups;
        Sorts = sorts;
        Limit = limit;
        After = after;
        Properties = properties;
    }
}