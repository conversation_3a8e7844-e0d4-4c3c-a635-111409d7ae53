﻿using Newtonsoft.Json;

namespace Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;

public class UserProfileEnrolledIntoFlowHubWorkflowLogData
{
    [JsonProperty("workflow_id")]
    public string WorkflowId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("workflow_name")]
    public string WorkflowName { get; set; }

    [JsonProperty("state_id")]
    public string StateId { get; set; }

    [JsonConstructor]
    public UserProfileEnrolledIntoFlowHubWorkflowLogData(
        string workflowId,
        string workflowVersionedId,
        string workflowName,
        string stateId)
    {
        WorkflowId = workflowId;
        WorkflowVersionedId = workflowVersionedId;
        WorkflowName = workflowName;
        StateId = stateId;
    }
}