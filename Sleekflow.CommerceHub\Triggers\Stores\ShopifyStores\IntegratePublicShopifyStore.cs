using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Stores;
using Sleekflow.CommerceHub.Models.Stores.ShopifyStores;
using Sleekflow.CommerceHub.Stores.ShopifyStores;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CommerceHub.Triggers.Stores.ShopifyStores;

[TriggerGroup(ControllerNames.ShopifyStores)]
public class IntegratePublicShopifyStore
    : ITrigger<
        IntegratePublicShopifyStore.IntegratePublicShopifyStoreInput,
        IntegratePublicShopifyStore.IntegratePublicShopifyStoreOutput>
{
    private readonly IShopifyStoreService _shopifyStoreService;

    public IntegratePublicShopifyStore(
        IShopifyStoreService shopifyStoreService)
    {
        _shopifyStoreService = shopifyStoreService;
    }

    public class IntegratePublicShopifyStoreInput :
        ShopifyStoreInput, IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("authorization_code")]
        public string AuthorizationCode { get; set; }

        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public IntegratePublicShopifyStoreInput(
            string sleekflowCompanyId,
            string shopifyUrl,
            string authorizationCode,
            bool isViewEnabled,
            bool isPaymentEnabled,
            StoreSubscriptionStatus? subscriptionStatus,
            Dictionary<string, object?> metadata,
            StoreTemplateDict templateDict,
            ShopifySyncConfig shopifySyncConfig,
            ShopifyPaymentConfig shopifyPaymentConfig,
            ShopifySyncStatus? shopifySyncStatus,
            List<ShopifyMessageTemplate>? shopifyMessageTemplates,
            bool? isShopifyBillingOwner,
            DateTimeOffset? chargeUpdatedAt,
            string? chargeId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(
                shopifyUrl,
                isViewEnabled,
                isPaymentEnabled,
                subscriptionStatus,
                metadata,
                templateDict,
                shopifySyncConfig,
                shopifyPaymentConfig,
                shopifySyncStatus,
                shopifyMessageTemplates,
                isShopifyBillingOwner,
                chargeUpdatedAt,
                chargeId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            AuthorizationCode = authorizationCode;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class IntegratePublicShopifyStoreOutput
    {
        [JsonProperty("store")]
        public StoreDto Store { get; set; }

        [JsonConstructor]
        public IntegratePublicShopifyStoreOutput(StoreDto store)
        {
            Store = store;
        }
    }

    public async Task<IntegratePublicShopifyStoreOutput> F(
        IntegratePublicShopifyStoreInput integratePublicShopifyStoreInput)
    {
        var shopifyStore = await _shopifyStoreService.CreateAndGetPublicShopifyStoreAsync(
            integratePublicShopifyStoreInput.SleekflowCompanyId,
            integratePublicShopifyStoreInput.ShopifyUrl,
            integratePublicShopifyStoreInput.AuthorizationCode,
            integratePublicShopifyStoreInput.IsViewEnabled,
            integratePublicShopifyStoreInput.IsPaymentEnabled,
            integratePublicShopifyStoreInput.SubscriptionStatus,
            integratePublicShopifyStoreInput.Metadata,
            integratePublicShopifyStoreInput.TemplateDict,
            integratePublicShopifyStoreInput.ShopifySyncConfig,
            integratePublicShopifyStoreInput.ShopifyPaymentConfig,
            integratePublicShopifyStoreInput.ShopifySyncStatus,
            integratePublicShopifyStoreInput.ShopifyMessageTemplates,
            integratePublicShopifyStoreInput.IsShopifyBillingOwner,
            integratePublicShopifyStoreInput.ChargeUpdatedAt,
            integratePublicShopifyStoreInput.ChargeId,
            integratePublicShopifyStoreInput.SleekflowStaffId,
            integratePublicShopifyStoreInput.SleekflowStaffTeamIds);

        return new IntegratePublicShopifyStoreOutput(new StoreDto(shopifyStore));
    }
}