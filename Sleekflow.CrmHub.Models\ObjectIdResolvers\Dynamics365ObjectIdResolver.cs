using Newtonsoft.Json.Linq;
using Sleekflow.CrmHub.Models.Entities;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Models.ObjectIdResolvers;

public interface IDynamics365ObjectIdResolver
{
    string ResolveObjectId(Dictionary<string, object?> dict, string entityTypeNameOrCodeName);
}

public class Dynamics365ObjectIdResolver : IDynamics365ObjectIdResolver, ISingletonService
{
    /// <summary>
    ///
    /// </summary>
    /// <param name="dict"></param>
    /// <param name="entityTypeNameOrCodeName">contacts; salesorders; salesorderdetails.</param>
    /// <returns></returns>
    /// <exception cref="SfIdUnresolvableException"></exception>
    public string ResolveObjectId(Dictionary<string, object?> dict, string entityTypeNameOrCodeName)
    {
        var tn = entityTypeNameOrCodeName switch
        {
            // EntityTypeName
            "Contact" => "contact",
            "User" => "systemuser",
            "SalesOrder" => "salesorder",
            "SalesOrderItem" => "salesorderdetail",
            "Store" => "thk_store",
            "Language" => "new_apmlang",
            "Product" => "product",
            "ProductVariant" => "thk_productvariant",
            "Currency" => "transactioncurrency",
            "Salesperson" => "thk_salesperson",
            "AwarenessSource" => "thk_howdoyoukonwus",

            // CodeName
            "systemusers" => "systemuser",
            "contacts" => "contact",
            "salesorders" => "salesorder",
            "salesorderdetails" => "salesorderdetail",
            "thk_stores" => "thk_store",
            "new_apmlangs" => "new_apmlang",
            "products" => "product",
            "thk_productvariants" => "thk_productvariant",
            "transactioncurrencies" => "transactioncurrency",
            "thk_salespersons" => "thk_salesperson",
            "thk_howdoyoukonwuses" => "thk_howdoyoukonwus",
            _ => throw new SfIdUnresolvableException(dict)
        };

        if (dict.ContainsKey($"d365:Id"))
        {
            var entry = dict["d365:Id"]!;
            if (entry is JObject jObject)
            {
                if (jObject.ContainsKey(SnapshottedValue.PropertyNameValue))
                {
                    return jObject.Value<string>(SnapshottedValue.PropertyNameValue)!;
                }
                else if (jObject.ContainsKey("value"))
                {
                    return jObject.Value<string>("value")!;
                }
            }
            else if (entry is string s)
            {
                return s;
            }
        }
        else if (dict.ContainsKey("Id"))
        {
            return (string) dict["Id"]!;
        }
        else if (dict.ContainsKey($"d365:{tn}id"))
        {
            var entry = dict[$"d365:{tn}id"]!;
            if (entry is JObject jObject)
            {
                if (jObject.ContainsKey(SnapshottedValue.PropertyNameValue))
                {
                    return jObject.Value<string>(SnapshottedValue.PropertyNameValue)!;
                }
                else if (jObject.ContainsKey("value"))
                {
                    return jObject.Value<string>("value")!;
                }
            }
            else if (entry is string s)
            {
                return s;
            }
        }
        else if (dict.ContainsKey($"{tn}id"))
        {
            return (string) dict[$"{tn}id"]!;
        }

        throw new SfIdUnresolvableException(dict);
    }
}