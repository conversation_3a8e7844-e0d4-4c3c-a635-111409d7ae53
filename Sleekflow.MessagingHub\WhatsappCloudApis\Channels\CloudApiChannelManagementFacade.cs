using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using GraphApi.Client.ApiClients;
using GraphApi.Client.ApiClients.Exceptions;
using GraphApi.Client.Const.WhatsappCloudApi;
using GraphApi.Client.Payloads;
using GraphApi.Client.Payloads.Models;
using MassTransit.Initializers;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Exceptions.MessagingHub;
using Sleekflow.Ids;
using Sleekflow.JsonConfigs;
using Sleekflow.MessagingHub.Audits;
using Sleekflow.MessagingHub.Models.Audits.Constants;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Exceptions;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Channels;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.Webhooks;
using Sleekflow.MessagingHub.Webhooks.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence;
using Sleekflow.Utils;
using Sleekflow.Webhooks;

namespace Sleekflow.MessagingHub.WhatsappCloudApis.Channels;

public interface IChannelService
{
    Task<Waba?> ConnectCloudApiChannelAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string? pin,
        string? webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<bool> DisconnectCloudApiChannelAsync(
        Waba waba,
        string wabaPhoneNumberId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<Waba?> ReconnectCloudApiChannel(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<RegisterPhoneNumberResponse> RegisterPhoneNumber(
        string facebookWabaId,
        string sleekflowCompanyId,
        string facebookPhoneNumberId,
        string pin,
        string? businessIntegrationSystemUserAccessToken);

    Task<GetPhoneNumberSettingsResponse> GetPhoneNumberSettings(
        string facebookWabaId,
        string sleekflowCompanyId,
        string facebookPhoneNumberId,
        string? businessIntegrationSystemUserAccessToken);

    Task<UpdatePhoneNumberSettingsResponse> UpdatePhoneNumberSettingsAsync(
        string facebookWabaId,
        string sleekflowCompanyId,
        string facebookPhoneNumberId,
        StorageConfiguration storageConfiguration,
        string? businessIntegrationSystemUserAccessToken);

    Task<Waba?> UpsertWhatsappCloudApiChannelWebhook(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string webhookUrl);

    Task<FacebookBusinessProfileResult> GetPhoneNumberWhatsappBusinessProfileAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId);

    Task<FacebookBusinessProfileResult> UpdatePhoneNumberWhatsappBusinessProfileAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string sleekflowStaffId,
        UpdatePhoneNumberBusinessProfileRequest updatePhoneNumberBusinessProfile);
}

public class ChannelService : IChannelService, ISingletonService
{
    private readonly IIdService _idService;
    private readonly IWabaService _wabaService;
    private readonly IWabaRepository _wabaRepository;
    private readonly ILogger<ChannelService> _logger;
    private readonly IAuditLogService _auditLogService;
    private readonly ICommonRetryPolicyService _commonRetryPolicyService;
    private readonly IWhatsappCloudApiBspClient _whatsappCloudApiBspClient;
    private readonly IMessagingHubWebhookService _messagingHubWebhookService;
    private readonly HttpClient _httpClient;

    public ChannelService(
        IIdService idService,
        IWabaService wabaService,
        ILogger<ChannelService> logger,
        IWabaRepository wabaRepository,
        IAuditLogService auditLogService,
        ICloudApiClients cloudApiClients,
        ICommonRetryPolicyService commonRetryPolicyService,
        IMessagingHubWebhookService messagingHubWebhookService,
        IHttpClientFactory _httpClientFactory)
    {
        _logger = logger;
        _idService = idService;
        _wabaService = wabaService;
        _wabaRepository = wabaRepository;
        _auditLogService = auditLogService;
        _commonRetryPolicyService = commonRetryPolicyService;
        _messagingHubWebhookService = messagingHubWebhookService;
        _whatsappCloudApiBspClient = cloudApiClients.WhatsappCloudApiBspClient;
        _httpClient = _httpClientFactory.CreateClient("default-handler");
    }

    public async Task<Waba?> ConnectCloudApiChannelAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string? pin,
        string? webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        try
        {
            var targetWabaPhoneNumber = GetTargetWabaPhoneNumber(waba, wabaPhoneNumberId);

            if (targetWabaPhoneNumber.FacebookPhoneNumberDetail.Status
                is WhatsappCloudApiPhoneNumberStatusConst.PENDING
                or WhatsappCloudApiPhoneNumberStatusConst.DISCONNECTED)
            {
                var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
                    _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

                var registerResult = await RegisterFacebookPhoneNumber(
                    waba.FacebookWabaId,
                    sleekflowCompanyId,
                    targetWabaPhoneNumber,
                    pin,
                    hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null);
                if (!registerResult)
                {
                    throw new SfInternalErrorException("Unable to register phone number");
                }

                waba = await _wabaService.GetAndRefreshWabaAsync(waba.Id, sleekflowCompanyId, true, sleekflowStaff);

                targetWabaPhoneNumber = GetTargetWabaPhoneNumber(waba, wabaPhoneNumberId);
            }

            return await ConnectWabaChannel(
                waba,
                targetWabaPhoneNumber,
                sleekflowCompanyId,
                wabaPhoneNumberId,
                webhookUrl,
                sleekflowStaff);
        }
        catch (Exception e)
        {
            if (e is GraphApiClientException gx)
            {
                throw new SfGraphApiErrorException(gx.ErrorApiResponse?.Error?.Message ?? "Unable to register waba", JsonConvertExtensions.ToDictionary(gx.ErrorApiResponse));
            }

            throw;
        }
    }

    public async Task<bool> DisconnectCloudApiChannelAsync(
        Waba waba,
        string wabaPhoneNumberId,
        string sleekflowCompanyId,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        return await _commonRetryPolicyService.GetAsyncRetryPolicy()
            .ExecuteAsync(
                async () =>
                {
                    try
                    {
                        var targetWabaPhoneNumber = GetTargetWabaPhoneNumber(
                            waba,
                            wabaPhoneNumberId,
                            sleekflowCompanyId,
                            false);

                        targetWabaPhoneNumber.SleekflowCompanyId = null;
                        targetWabaPhoneNumber.WebhookUrl = null;
                        targetWabaPhoneNumber.UpdatedAt = DateTimeOffset.UtcNow;

                        var updatedWabaPhoneNumbers =
                            ReplaceWabaPhoneNumberById(waba.WabaPhoneNumbers, targetWabaPhoneNumber, sleekflowStaff);

                        await _messagingHubWebhookService.RemoveWebhooksAsync(
                            sleekflowCompanyId,
                            WebhookEntityTypeNames.WabaPhoneNumber,
                            WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                            targetWabaPhoneNumber.Id);

                        return await PatchWabaAsync(
                            sleekflowCompanyId,
                            waba,
                            updatedWabaPhoneNumbers,
                            targetWabaPhoneNumber,
                            true);
                    }
                    catch (Exception)
                    {
                        var refreshWabaEntity = await _wabaService.GetWabaOrDefaultAsync(waba.Id, sleekflowCompanyId);
                        waba = refreshWabaEntity ??
                               throw new SfNotFoundObjectException($"Unable to locate waba information {waba}");

                        throw;
                    }
                });
    }

    public async Task<Waba?> ReconnectCloudApiChannel(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var patchWabaAndCreateWebhookResult = await _commonRetryPolicyService.GetAsyncRetryPolicy().ExecuteAsync(
            async () =>
            {
                try
                {
                    var targetWabaPhoneNumber = GetTargetWabaPhoneNumber(waba, wabaPhoneNumberId, sleekflowCompanyId);
                    targetWabaPhoneNumber.WebhookUrl = webhookUrl;

                    var updatedWabaPhoneNumbers =
                        ReplaceWabaPhoneNumberById(waba.WabaPhoneNumbers, targetWabaPhoneNumber, sleekflowStaff);

                    var webhooks = (await _messagingHubWebhookService.GetWebhooksWithWabaPhoneNumberIdAsync(
                        sleekflowCompanyId,
                        WebhookEntityTypeNames.WabaPhoneNumber,
                        WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                        targetWabaPhoneNumber.Id)).FirstOrDefault();

                    if (webhooks is null && targetWabaPhoneNumber.SleekflowCompanyId != null)
                    {
                        await _messagingHubWebhookService.RegisterAsync(
                            new Webhook(
                                _idService.GetId(SysTypeNames.Webhook),
                                targetWabaPhoneNumber.SleekflowCompanyId,
                                WebhookEntityTypeNames.WabaPhoneNumber,
                                targetWabaPhoneNumber.WebhookUrl,
                                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                                new Dictionary<string, object?>()),
                            targetWabaPhoneNumber.Id);
                    }
                    else
                    {
                        await _messagingHubWebhookService.PatchWebhookAsync(webhooks.Id, webhookUrl);
                    }

                    return await PatchWabaAsync(
                        sleekflowCompanyId,
                        waba,
                        updatedWabaPhoneNumbers,
                        targetWabaPhoneNumber);
                }
                catch (Exception)
                {
                    var refreshWabaEntity = await _wabaService.GetWabaOrDefaultAsync(waba.Id, sleekflowCompanyId);
                    waba = refreshWabaEntity ??
                           throw new SfNotFoundObjectException($"Unable to locate waba information {waba}");

                    throw;
                }
            });

        if (!patchWabaAndCreateWebhookResult)
        {
            throw new SfInternalErrorException("unable to reconnect webhook or waba");
        }

        return await GetAndFilterWaba(sleekflowCompanyId, waba.Id, wabaPhoneNumberId);
    }

    public async Task<RegisterPhoneNumberResponse> RegisterPhoneNumber(
        string facebookWabaId,
        string sleekflowCompanyId,
        string facebookPhoneNumberId,
        string pin,
        string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new WhatsappCloudApiBspClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient)
            : _whatsappCloudApiBspClient;

        return await _commonRetryPolicyService.GetAsyncRetryPolicy(
            exceptionPredicate: exception => exception is DataLocalizationRequiredException,
            totalRetryCount: 1).ExecuteAsync(async () =>
        {
            try
            {
                var response = await _auditLogService.GetCloudApiAuditedResponse(
                    () => whatsappCloudApiBspClient.RegisterPhoneNumberAsync(facebookPhoneNumberId, pin),
                    new BaseAuditingObject(facebookWabaId)
                    {
                        SleekflowCompanyId = sleekflowCompanyId,
                        Parameters = new Dictionary<string, object?>
                        {
                            {
                                "facebook_waba_id", facebookWabaId
                            },
                            {
                                "facebook_waba_phone_number_id", facebookPhoneNumberId
                            },
                            {
                                "pin", pin
                            }
                        },
                        Operation = AuditingOperation.RegisterWabaPhoneNumber
                    });
                return response;
            }
            catch (GraphApiClientException graphApiClientException)
            {
                var (isUpdateDataLocalizationRequired, dataLocalizationRegion) =
                    IsUpdateDataLocalizationRequiredForRegisterPhoneNumber(graphApiClientException);

                if (isUpdateDataLocalizationRequired)
                {
                    await UpdatePhoneNumberSettingsAsync(
                        facebookWabaId,
                        sleekflowCompanyId,
                        facebookPhoneNumberId,
                        new StorageConfiguration()
                        {
                            Status = StorageConfigurationStatus.IN_COUNTRY_STORAGE_ENABLED,
                            DataLocalizationRegion = dataLocalizationRegion
                        },
                        businessIntegrationSystemUserAccessToken);

                    throw new DataLocalizationRequiredException(
                        "Data localization region is required during register phone number");
                }

                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Register phone number error {MessagingHubPhoneNumberId} with {Exception}",
                    facebookPhoneNumberId,
                    JsonConvert.SerializeObject(ex));

                throw;
            }
        });
    }

    private (bool IsUpdateDataLocalizationRequired, string? DataLocalizationRegion)
        IsUpdateDataLocalizationRequiredForRegisterPhoneNumber(GraphApiClientException graphApiClientException)
    {
        try
        {
            if (graphApiClientException.ErrorApiResponse is { Error: { Code: 100, ErrorData: not null } })
            {
                var errorData = JObject.FromObject(graphApiClientException.ErrorApiResponse.Error.ErrorData);
                var errorDetails = errorData["details"]?.ToString();
                string pattern = @"Data localization region: '(\w+)'";
                if (errorDetails != null && errorDetails.StartsWith(
                        "Migrated phone number should be first registered with the same data localization region used on the source WABA."))
                {
                    var match = Regex.Match(errorDetails, pattern);
                    if (match.Success)
                    {
                        var dataLocalizationRegion = match.Groups[1].Value;
                        return (true, dataLocalizationRegion);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, $"An error occured while parsing graph API error: {exception.Message}");
        }

        return (false, null);
    }

    public async Task<GetPhoneNumberSettingsResponse> GetPhoneNumberSettings(
        string facebookWabaId,
        string sleekflowCompanyId,
        string facebookPhoneNumberId,
        string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new WhatsappCloudApiBspClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient)
            : _whatsappCloudApiBspClient;

        return await whatsappCloudApiBspClient.GetPhoneNumberSettings(facebookPhoneNumberId);
    }

    public async Task<UpdatePhoneNumberSettingsResponse> UpdatePhoneNumberSettingsAsync(
        string facebookWabaId,
        string sleekflowCompanyId,
        string facebookPhoneNumberId,
        StorageConfiguration storageConfiguration,
        string? businessIntegrationSystemUserAccessToken)
    {
        var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken)
            ? new WhatsappCloudApiBspClient(
                businessIntegrationSystemUserAccessToken,
                _httpClient)
            : _whatsappCloudApiBspClient;

        return await _auditLogService.GetCloudApiAuditedResponse(
            () => whatsappCloudApiBspClient.UpdatePhoneNumberSettings(facebookPhoneNumberId, storageConfiguration),
            new BaseAuditingObject(facebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "facebook_waba_id", facebookWabaId
                    },
                    {
                        "facebook_phone_number_id", facebookPhoneNumberId
                    },
                    {
                        "storage_configuration", storageConfiguration
                    }
                },
                Operation = AuditingOperation.UpdatePhoneNumberSettings
            });
    }

    public async Task<Waba?> UpsertWhatsappCloudApiChannelWebhook(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string webhookUrl)
    {
        var targetWabaPhoneNumber = GetTargetWabaPhoneNumber(waba, wabaPhoneNumberId, sleekflowCompanyId);
        if (!targetWabaPhoneNumber.SleekflowCompanyId!.Equals(sleekflowCompanyId))
        {
            throw new SfUnauthorizedException();
        }

        if (targetWabaPhoneNumber.WebhookUrl != null)
        {
            await _messagingHubWebhookService.RemoveWebhooksAsync(
                sleekflowCompanyId,
                WebhookEntityTypeNames.WabaPhoneNumber,
                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                targetWabaPhoneNumber.Id);
        }

        return await ConnectWabaChannel(
            waba,
            targetWabaPhoneNumber,
            sleekflowCompanyId,
            wabaPhoneNumberId,
            webhookUrl,
            null);
    }

    public async Task<FacebookBusinessProfileResult> GetPhoneNumberWhatsappBusinessProfileAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId)
    {
        var targetWabaPhoneNumber = GetTargetWabaPhoneNumber(waba, wabaPhoneNumberId, sleekflowCompanyId);
        if (!targetWabaPhoneNumber.SleekflowCompanyId!.Equals(sleekflowCompanyId))
        {
            throw new SfUnauthorizedException();
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var businessIntegrationSystemUserAccessToken = hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null;

        var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken) ? new WhatsappCloudApiBspClient(
            businessIntegrationSystemUserAccessToken,
            _httpClient) : _whatsappCloudApiBspClient;

        var businessProfile = await whatsappCloudApiBspClient.GetWhatsappPhoneNumberProfileAsync(targetWabaPhoneNumber.FacebookPhoneNumberId);

        return businessProfile.Data.FirstOrDefault() ?? throw new SfNotFoundObjectException("whatsapp business profile not found from meta api");
    }

    public async Task<FacebookBusinessProfileResult> UpdatePhoneNumberWhatsappBusinessProfileAsync(
        Waba waba,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string sleekflowStaffId,
        UpdatePhoneNumberBusinessProfileRequest updatePhoneNumberBusinessProfile)
    {
        var targetWabaPhoneNumber = GetTargetWabaPhoneNumber(waba, wabaPhoneNumberId, sleekflowCompanyId);
        if (!targetWabaPhoneNumber.SleekflowCompanyId!.Equals(sleekflowCompanyId))
        {
            throw new SfUnauthorizedException();
        }

        var (hasEnabledFLFB, decryptedBusinessIntegrationSystemUserAccessTokenDto) =
            _wabaService.GetWabaFLFBOrNotAndDecryptedBusinessIntegrationSystemUserAccessToken(waba);

        var businessIntegrationSystemUserAccessToken = hasEnabledFLFB ? decryptedBusinessIntegrationSystemUserAccessTokenDto!.DecryptedToken : null;

        var whatsappCloudApiBspClient = !string.IsNullOrEmpty(businessIntegrationSystemUserAccessToken) ? new WhatsappCloudApiBspClient(
            businessIntegrationSystemUserAccessToken,
            _httpClient) : _whatsappCloudApiBspClient;

        updatePhoneNumberBusinessProfile.MessagingProduct = "whatsapp";

        await _auditLogService.GetCloudApiAuditedResponse(
            () => whatsappCloudApiBspClient.UpdateWhatsappPhoneNumberProfileAsync(
                targetWabaPhoneNumber.FacebookPhoneNumberId,
                updatePhoneNumberBusinessProfile.MessagingProduct,
                updatePhoneNumberBusinessProfile.Address,
                updatePhoneNumberBusinessProfile.Description,
                updatePhoneNumberBusinessProfile.Vertical,
                updatePhoneNumberBusinessProfile.About,
                updatePhoneNumberBusinessProfile.Email,
                updatePhoneNumberBusinessProfile.Websites,
                updatePhoneNumberBusinessProfile.ProfilePictureHandle),
            new BaseAuditingObject(waba.FacebookWabaId)
            {
                SleekflowCompanyId = sleekflowCompanyId,
                Parameters = new Dictionary<string, object?>
                {
                    {
                        "facebook_waba_id", waba.FacebookWabaId
                    },
                    {
                        "facebook_phone_number_id", targetWabaPhoneNumber.FacebookPhoneNumberId
                    },
                    {
                        "sleekflow_staff_id", sleekflowStaffId
                    },
                    {
                        "update_phone_number_business_profile_request", updatePhoneNumberBusinessProfile
                    }
                },
                Operation = AuditingOperation.UpdateWabaPhoneNumberWhatsappBusinessProfile
            });

        return await GetPhoneNumberWhatsappBusinessProfileAsync(waba, sleekflowCompanyId, wabaPhoneNumberId);
    }

    private async Task<Waba?> ConnectWabaChannel(
        Waba waba,
        WabaPhoneNumber targetWabaPhoneNumber,
        string sleekflowCompanyId,
        string wabaPhoneNumberId,
        string? webhookUrl,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var registerAndPatchWaba = await _commonRetryPolicyService.GetAsyncRetryPolicy().ExecuteAsync(
            async () =>
            {
                try
                {
                    targetWabaPhoneNumber.SleekflowCompanyId = sleekflowCompanyId;
                    targetWabaPhoneNumber.WebhookUrl = webhookUrl;
                    targetWabaPhoneNumber.UpdatedAt = DateTimeOffset.UtcNow;

                    var updatedWabaPhoneNumbers =
                        ReplaceWabaPhoneNumberById(waba.WabaPhoneNumbers, targetWabaPhoneNumber, sleekflowStaff);
                    if (webhookUrl is not null)
                    {
                        await _messagingHubWebhookService.RegisterAsync(
                            new Webhook(
                                _idService.GetId(SysTypeNames.Webhook),
                                targetWabaPhoneNumber.SleekflowCompanyId,
                                WebhookEntityTypeNames.WabaPhoneNumber,
                                webhookUrl,
                                WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                                new Dictionary<string, object?>()),
                            targetWabaPhoneNumber.Id);
                    }

                    return await PatchWabaAsync(
                        sleekflowCompanyId,
                        waba,
                        updatedWabaPhoneNumbers,
                        targetWabaPhoneNumber);
                }
                catch (Exception)
                {
                    var refreshWabaEntity = await _wabaService.GetWabaOrDefaultAsync(waba.Id, sleekflowCompanyId);
                    waba = refreshWabaEntity ??
                           throw new SfNotFoundObjectException($"Unable to locate waba information {waba}");
                    targetWabaPhoneNumber = GetTargetWabaPhoneNumber(waba, wabaPhoneNumberId);

                    throw;
                }
            });

        if (!registerAndPatchWaba)
        {
            throw new SfInternalErrorException("Unable to register and patch waba");
        }

        return await GetAndFilterWaba(sleekflowCompanyId, waba.Id, wabaPhoneNumberId);
    }

    private WabaPhoneNumber GetTargetWabaPhoneNumber(
        Waba waba,
        string wabaPhoneNumberId,
        string? sleekflowCompanyId = null,
        bool shouldValidateRecordStatus = true)
    {
        var targetWabaPhoneNumber = waba.WabaPhoneNumbers.SingleOrDefault(
            w =>
            {
                var conditions =
                    w.Id == wabaPhoneNumberId
                    && (sleekflowCompanyId is null
                        ? string.IsNullOrEmpty(w.SleekflowCompanyId)
                        : w.SleekflowCompanyId == sleekflowCompanyId);
                return shouldValidateRecordStatus
                    ? conditions && w.RecordStatus == WabaPhoneNumberStatuses.Active
                    : conditions;
            });
        if (targetWabaPhoneNumber == null)
        {
            throw new SfNotFoundObjectException(
                $"Unable to get WabaPhoneNumber with connectCloudApiChannelInput.WabaId {waba.Id} and connectCloudApiChannelInput.WabaPhoneNumberId {wabaPhoneNumberId}");
        }

        return targetWabaPhoneNumber;
    }

    private async Task<bool> RegisterFacebookPhoneNumber(
        string facebookWabaId,
        string sleekflowCompanyId,
        WabaPhoneNumber wabaPhoneNumber,
        string? pin,
        string? businessIntegrationSystemUserAccessToken)
    {
        if (wabaPhoneNumber.FacebookPhoneNumberDetail.IsPinEnabled != null &&
            !wabaPhoneNumber.FacebookPhoneNumberDetail.IsPinEnabled.Value)
        {
            pin ??= "000000";
            var registerPhoneNumber = await RegisterPhoneNumber(
                facebookWabaId,
                sleekflowCompanyId,
                wabaPhoneNumber.FacebookPhoneNumberId,
                pin,
                businessIntegrationSystemUserAccessToken);

            if (!registerPhoneNumber.Success)
            {
                throw new SfInternalErrorException($"Unable to register waba");
            }

            return registerPhoneNumber.Success;
        }

        throw new SfValidationException(
            new List<ValidationResult>
            {
                new ($"unable to connect waba due to pin is enabled")
            });
    }

    private HashSet<WabaPhoneNumber> ReplaceWabaPhoneNumberById(
        IEnumerable<WabaPhoneNumber> wabaPhoneNumbers,
        WabaPhoneNumber targetWpn,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        targetWpn.UpdatedBy = sleekflowStaff;

        return wabaPhoneNumbers
            .Select(wpn => wpn.Id == targetWpn.Id ? targetWpn : wpn)
            .ToHashSet();
    }

    private async Task<bool> PatchWabaAsync(
        string sleekflowCompanyId,
        Waba waba,
        HashSet<WabaPhoneNumber> updatedWabaPhoneNumbers,
        WabaPhoneNumber targetWabaPhoneNumber,
        bool isDisconnect = false)
    {
        var isPatched = await _wabaRepository.PatchWabaAsync(waba, updatedWabaPhoneNumbers);
        if (isPatched)
        {
            await _auditLogService.AuditWabaAsync(
                waba,
                waba.FacebookWabaId,
                sleekflowCompanyId,
                AuditingOperation.WabaUpdate,
                JsonConvertExtensions.ToDictionary(
                    new Dictionary<string, object>
                    {
                        {
                            "changes", updatedWabaPhoneNumbers
                        }
                    }));
        }
        else
        {
            if (!isDisconnect)
            {
                await _messagingHubWebhookService.RemoveWebhooksAsync(
                    sleekflowCompanyId,
                    WebhookEntityTypeNames.WabaPhoneNumber,
                    WebhookEventTypeNames.EventTypeNameOnWhatsappCloudApiMessageReceived,
                    targetWabaPhoneNumber.Id);
            }

            throw new SfInternalErrorException(
                $"Unable to patch Waba {JsonConvert.SerializeObject(waba, JsonConfig.DefaultJsonSerializerSettings)} with updatedWabaPhoneNumbers {JsonConvert.SerializeObject(updatedWabaPhoneNumbers, JsonConfig.DefaultJsonSerializerSettings)} for targetWabaPhoneNumber {JsonConvert.SerializeObject(targetWabaPhoneNumber, JsonConfig.DefaultJsonSerializerSettings)}");
        }

        return isPatched;
    }

    private async Task<Waba?> GetAndFilterWaba(string sleekflowCompanyId, string wabaId, string wabaPhoneNumberId)
    {
        return await _wabaService
            .GetWabaWithWabaIdAndWabaPhoneNumberIdAsync(wabaId, sleekflowCompanyId, wabaPhoneNumberId)
            .Select(
                w =>
                {
                    if (w == null)
                    {
                        return null;
                    }

                    w.WabaPhoneNumbers = w.WabaPhoneNumbers
                        .Where(
                            p =>
                                p.Id == wabaPhoneNumberId &&
                                (p.SleekflowCompanyId == null || p.SleekflowCompanyId == sleekflowCompanyId))
                        .ToHashSet();
                    return w;
                });
    }
}