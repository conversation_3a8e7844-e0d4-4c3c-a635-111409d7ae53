using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.CustomCatalogs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.CustomCatalogs;

[TriggerGroup(ControllerNames.CustomCatalogs)]
public class ProcessCustomCatalogCsv
    : ITrigger<
        ProcessCustomCatalogCsv.ProcessCustomCatalogCsvInput,
        ProcessCustomCatalogCsv.ProcessCustomCatalogCsvOutput>
{
    private readonly ICustomCatalogFileService _customCatalogFileService;

    public ProcessCustomCatalogCsv(
        ICustomCatalogFileService customCatalogFileService)
    {
        _customCatalogFileService = customCatalogFileService;
    }

    public class ProcessCustomCatalogCsvInput : IHasSleekflowStaff
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        [Required]
        public string StoreId { get; set; }

        [JsonProperty("blob_name")]
        [Required]
        public string BlobName { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvInput(
            string sleekflowCompanyId,
            string storeId,
            string blobName,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            BlobName = blobName;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class ProcessCustomCatalogCsvOutput
    {
        [JsonProperty("custom_catalog_file_id")]
        public string CustomCatalogFileId { get; set; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvOutput(string customCatalogFileId)
        {
            CustomCatalogFileId = customCatalogFileId;
        }
    }

    public async Task<ProcessCustomCatalogCsvOutput> F(
        ProcessCustomCatalogCsvInput processCustomCatalogCsvInput)
    {
        var customCatalogFileId = await _customCatalogFileService.ProcessCustomCatalogCsvAsync(
            processCustomCatalogCsvInput.SleekflowCompanyId,
            processCustomCatalogCsvInput.StoreId,
            processCustomCatalogCsvInput.BlobName,
            new AuditEntity.SleekflowStaff(
                processCustomCatalogCsvInput.SleekflowStaffId,
                processCustomCatalogCsvInput.SleekflowStaffTeamIds));

        try
        {
            await _customCatalogFileService.ReadCustomCatalogCsvHeadersAsync(
                customCatalogFileId,
                processCustomCatalogCsvInput.SleekflowCompanyId);
        }
        catch (Exception)
        {
            await _customCatalogFileService.PatchCustomCatalogFileFileProcessStatusAsync(
                customCatalogFileId,
                processCustomCatalogCsvInput.SleekflowCompanyId,
                CustomCatalogFileProcessStatuses.Failed);

            throw;
        }

        return new ProcessCustomCatalogCsvOutput(
            customCatalogFileId);
    }
}