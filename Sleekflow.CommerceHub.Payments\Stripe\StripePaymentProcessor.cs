using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.CommerceHub.Models.Payments.Configuration;
using Sleekflow.CommerceHub.Models.Payments.StripeContexts;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Stripe;
using Stripe.Checkout;

namespace Sleekflow.CommerceHub.Payments.Stripe;

public interface IStripePaymentProcessor : IPaymentProcessor
{
}

public class StripePaymentProcessor : IStripePaymentProcessor, ISingletonService
{
    private readonly IStripeClients _stripeClients;
    private readonly ICurrencyConverter _currencyConverter;
    private readonly IApplicationFeeConverter _applicationFeeConverter;
    private readonly IStripeCustomerRepository _stripeCustomerRepository;

    public StripePaymentProcessor(
        IStripeClients stripeClients,
        ICurrencyConverter currencyConverter,
        IApplicationFeeConverter applicationFeeConverter,
        IStripeCustomerRepository stripeCustomerRepository)
    {
        _stripeClients = stripeClients;
        _currencyConverter = currencyConverter;
        _applicationFeeConverter = applicationFeeConverter;
        _stripeCustomerRepository = stripeCustomerRepository;
    }

    public async Task<(string PaymentLink, ProcessPaymentContext PaymentProcessorContext)> ProcessPaymentAsync(
        Order order,
        PaymentProviderConfig paymentProviderConfig,
        PaymentProcessorOption paymentProcessorOption)
    {
        var stripePaymentProviderExternalConfig = GetStripePaymentProviderExternalConfig(paymentProviderConfig);
        if (!stripePaymentProviderExternalConfig.IsConnectedAccount)
        {
            throw new Exception("Stripe Connect account has not completed onboarding yet");
        }

        var stripeClient = GetStripeClient(paymentProviderConfig);

        var (languageIsoCode, returnToUrl) = paymentProcessorOption;

        var lineItems = order.LineItems;
        var sleekflowCompanyId = order.SleekflowCompanyId;
        var currencyIsoCode = "USD";

        var sessionLineItems = new List<SessionLineItemOptions>();
        var totalAmount = 0m;

        foreach (var li in lineItems)
        {
            var productVariantSnapshot = li.ProductVariantSnapshot;

            // TODO Check this logic
            var price = productVariantSnapshot.Prices.Find(p => p.CurrencyIsoCode == currencyIsoCode);
            if (price == null)
            {
                throw new SfUserFriendlyException("The ProductVariant doesn't have a valid price.");
            }

            var usdAmount = await _currencyConverter.ConvertToUsdAsync(
                price.Amount,
                price.CurrencyIsoCode);

            totalAmount += usdAmount * 100;

            sessionLineItems.Add(
                new SessionLineItemOptions
                {
                    PriceData = new SessionLineItemPriceDataOptions
                    {
                        // Multiply the amount by 100 before converting it to a long? value.
                        UnitAmount = (long) (usdAmount * 100),
                        Currency = currencyIsoCode,
                        ProductData = new SessionLineItemPriceDataProductDataOptions
                        {
                            Name = productVariantSnapshot.Names
                                .Where(n => n.LanguageIsoCode == languageIsoCode)
                                .Select(n => n.Value)
                                .First(),
                            Description = li.Description,
                            Images = productVariantSnapshot.Images.Any()
                                ? productVariantSnapshot.Images
                                    .Select(i => i.ImageUrl)
                                    .Take(8)
                                    .ToList()
                                : null
                        },
                    },
                    Quantity = li.Quantity,
                });
        }

        // Create an object of type ChargeShippingOptions with the values from the ShippingOptions object.
        var shipping = order.Shipping;
        var addressOptions = shipping is { Address: { } }
            ? new AddressOptions
            {
                Line1 = shipping.Address.Line1,
                Line2 = shipping.Address.Line2,
                City = shipping.Address.City,
                PostalCode = shipping.Address.PostalCode,
                Country = shipping.Address.Province
            }
            : null;
        var chargeShippingOptions = shipping != null
            ? new ChargeShippingOptions
            {
                Name = shipping.FirstName + " " + shipping.LastName, Phone = shipping.Phone, Address = addressOptions
            }
            : null;

        // Create a UserProfile object with the values from the UserProfile object.
        var orderUserProfile = order.UserProfile;
        var customer = await _stripeCustomerRepository.GetOrCreateCustomerAsync(
            orderUserProfile.FirstName + " " + orderUserProfile.LastName,
            orderUserProfile.Email,
            orderUserProfile.Phone,
            stripeClient);

        // Next, we need to create a session object with the customer, shipping options, and line items.
        var sessionCreateOptions = new SessionCreateOptions
        {
            Customer = customer.Id,
            PaymentMethodTypes = new List<string>
            {
                "card"
            },
            LineItems = sessionLineItems,
            PaymentIntentData = new SessionPaymentIntentDataOptions
            {
                Shipping = chargeShippingOptions,
                ApplicationFeeAmount = stripePaymentProviderExternalConfig.IsConnectedAccount
                    ? _applicationFeeConverter.GetApplicationFee(
                        sleekflowCompanyId,
                        currencyIsoCode,
                        totalAmount)
                    : null,
                OnBehalfOf = stripePaymentProviderExternalConfig.IsConnectedAccount
                    ? stripePaymentProviderExternalConfig.StripeAccount.Id
                    : null,
                TransferData = stripePaymentProviderExternalConfig.IsConnectedAccount
                    ? new SessionPaymentIntentDataTransferDataOptions
                    {
                        Destination = stripePaymentProviderExternalConfig.StripeAccount.Id,
                    }
                    : null,
                Metadata = new Dictionary<string, string>
                {
                    {
                        "source", "commerce-hub"
                    },
                    {
                        "orderId", order.Id
                    },
                    {
                        "companyId", sleekflowCompanyId
                    }
                }
            },
            Mode = "payment",
            SuccessUrl = "https://example.com/success?return_to=" + returnToUrl,
            CancelUrl = "https://example.com/cancel?return_to=" + returnToUrl,
            ExpiresAt = order.PaymentLinkExpiredAt.HasValue
                ? order.PaymentLinkExpiredAt.Value.UtcDateTime
                : null,
        };

        var sessionService = new SessionService(stripeClient);
        var session = await sessionService.CreateAsync(sessionCreateOptions);

        return (
            session.Url,
            new StripeProcessPaymentContext(session.Id, session));
    }

    private StripePaymentProviderExternalConfig GetStripePaymentProviderExternalConfig(
        PaymentProviderConfig paymentProviderConfig)
    {
        if (paymentProviderConfig.PaymentProviderExternalConfig is
            StripePaymentProviderExternalConfig stripePaymentProviderExternalConfig)
        {
            return stripePaymentProviderExternalConfig;
        }

        throw new NotImplementedException();
    }

    private StripeClient GetStripeClient(PaymentProviderConfig paymentProviderConfig)
    {
        if (paymentProviderConfig.PaymentProviderExternalConfig is
            StripePaymentProviderExternalConfig stripePaymentProviderExternalConfig)
        {
            return _stripeClients.GetCustomStripeClient(stripePaymentProviderExternalConfig.StripeApiKey);
        }

        throw new NotImplementedException();
    }

    public async Task<(string RefundId, ProcessRefundContext ProcessRefundContext)> ProcessRefundAsync(
        Order order,
        Payment payment,
        decimal amount,
        string reason,
        PaymentProviderConfig paymentProviderConfig)
    {
        var stripeClient = GetStripeClient(paymentProviderConfig);

        var paymentIntentService = new PaymentIntentService(stripeClient);
        var paymentIntent = await paymentIntentService.GetAsync(
            payment.ProcessPaymentContext!.ProviderPaymentId);

        var refundService = new RefundService(stripeClient);
        var refunds = await refundService.ListAsync(
            new RefundListOptions
            {
                PaymentIntent = paymentIntent.Id
            });

        var refundsSum = refunds.Where(r => r.Status == "succeeded" || r.Status == "pending").Sum(r => r.Amount);
        if (refundsSum + amount > paymentIntent.AmountReceived)
        {
            throw new SfUserFriendlyException("The refund amount exceeds the amount paid.");
        }

        var refundCreateOptions = new RefundCreateOptions
        {
            PaymentIntent = paymentIntent.Id,
            Amount = Convert.ToInt64(amount * 100),
            Reason = reason,
            ReverseTransfer = true,
            RefundApplicationFee = false,
            Metadata = new Dictionary<string, string>
            {
                {
                    "source", "commerce-hub"
                },
                {
                    "orderId", order.Id
                },
                {
                    "companyId", order.SleekflowCompanyId
                }
            },
        };

        var refund = await refundService.CreateAsync(refundCreateOptions);

        return (refund.Id,
            new StripeProcessRefundContext(
                refund.Id,
                refund.Status,
                refund.Status == "succeeded" ? refund.Amount : null,
                refund));
    }
}