using Azure.Storage.Blobs;
using MassTransit;
using MassTransit.AzureStorage.MessageData;
using Newtonsoft.Json;
using Sleekflow.Configs;

namespace Sleekflow.Events;

public interface IMessageDataService
{
    Task<MessageData<string>> PutString(string val);

    Task<MessageData<string>> PutString(Dictionary<string, object?> val);

    Task<MessageData<byte[]>> PutBytes(byte[] val);

    Task<MessageData<Stream>> PutStream(Stream val);
}

public class MessageDataService : IMessageDataService
{
    private readonly AzureStorageMessageDataRepository _azureStorageMessageDataRepository;

    public MessageDataService(IMassTransitStorageConfig massTransitStorageConfig)
    {
        var client = new BlobServiceClient(massTransitStorageConfig.MessageDataConnStr);

        _azureStorageMessageDataRepository =
            client.CreateMessageDataRepository(massTransitStorageConfig.MessageDataContainerName);
    }

    public AzureStorageMessageDataRepository GetAzureStorageMessageDataRepository()
    {
        return _azureStorageMessageDataRepository;
    }

    public async Task<MessageData<string>> PutString(string val)
    {
        return await _azureStorageMessageDataRepository.PutString(val);
    }

    public async Task<MessageData<string>> PutString(Dictionary<string, object?> val)
    {
        return await _azureStorageMessageDataRepository.PutString(JsonConvert.SerializeObject(val));
    }

    public async Task<MessageData<byte[]>> PutBytes(byte[] val)
    {
        return await _azureStorageMessageDataRepository.PutBytes(val);
    }

    public async Task<MessageData<Stream>> PutStream(Stream val)
    {
        return await _azureStorageMessageDataRepository.PutStream(val);
    }
}