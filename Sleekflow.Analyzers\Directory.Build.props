<Project>
    <ItemGroup>
        <PackageReference
            Include="StyleCop.Analyzers"
            Version="1.2.0-beta.435"
            PrivateAssets="all"
            IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive"
            Condition="$(MSBuildProjectExtension) == '.csproj'"
        />
        <PackageReference
            Include="SonarAnalyzer.CSharp"
            Version="9.31.0.96804"
            PrivateAssets="all"
            IncludeAssets="runtime; build; native; contentfiles; analyzers; buildtransitive"
            Condition="$(MSBuildProjectExtension) == '.csproj'"
        />
    </ItemGroup>
    <!--
    <Target Name="PostClean" AfterTargets="Clean">
        <RemoveDir Directories="$(BaseIntermediateOutputPath)" />
        <RemoveDir Directories="$(BaseOutputPath)" />
    </Target>
    -->
</Project>