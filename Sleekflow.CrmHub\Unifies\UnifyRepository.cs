﻿using Sleekflow.CrmHub.Models.Unifies;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Unifies;

public interface IUnifyRepository : IRepository<Unify>
{
}

public class UnifyRepository : BaseRepository<Unify>, IUnifyRepository, ISingletonService
{
    public UnifyRepository(
        ILogger<BaseRepository<Unify>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}