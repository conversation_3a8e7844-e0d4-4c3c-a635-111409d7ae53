﻿using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Documents.Chunks;
using Sleekflow.IntelligentHub.Models.Documents.WebPageDocuments;
using Sleekflow.Persistence.Abstractions;
using static Sleekflow.Persistence.PatchVariable;

namespace Sleekflow.IntelligentHub.Documents.WebPageDocuments;

public interface IWebPageDocumentChunkRepository : IDynamicFiltersRepository<WebPageDocumentChunk>
{
    Task<WebPageDocumentChunk?> GetWebPageDocumentChunkOrDefaultAsync(
        string webPageDocumentChunkId,
        string sleekflowCompanyId,
        string documentId);

    Task<WebPageDocumentChunk> PatchAndGetWebPageDocumentChunkAsync(
        WebPageDocumentChunk webPageDocumentChunk,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata);

    Task<(List<WebPageDocumentChunk> WebPageDocumentChunks, string? NextContinuationToken)> GetWebPageDocumentChunksAsync(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit);
}

public class WebPageDocumentChunkRepository
    : DynamicFiltersBaseRepository<WebPageDocumentChunk>, IScopedService, IWebPageDocumentChunkRepository
{
    public WebPageDocumentChunkRepository(
        ILogger<WebPageDocumentChunkRepository> logger,
        IServiceProvider serviceProvider,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext)
        : base(logger, serviceProvider, dynamicFiltersRepositoryContext)
    {
    }

    public async Task<WebPageDocumentChunk?> GetWebPageDocumentChunkOrDefaultAsync(
        string webPageDocumentChunkId,
        string sleekflowCompanyId,
        string documentId)
    {
        return (await GetObjectsAsync(
                e =>
                    e.Id == webPageDocumentChunkId
                    && e.SleekflowCompanyId == sleekflowCompanyId
                    && e.DocumentId == documentId))
            .FirstOrDefault();
    }

    public async Task<WebPageDocumentChunk> PatchAndGetWebPageDocumentChunkAsync(
        WebPageDocumentChunk webPageDocumentChunk,
        string content,
        string contentEn,
        List<Category> categories,
        Dictionary<string, object?> metadata)
    {
        return await PatchAndGetAsync(
            webPageDocumentChunk.Id,
            webPageDocumentChunk.SleekflowCompanyId,
            new List<PatchOperation>
            {
                Replace(Chunk.PropertyNameContent, content),
                Replace(Chunk.PropertyNameContentEn, contentEn),
                Replace(Chunk.PropertyNameCategories, categories),
                Replace(IHasMetadata.PropertyNameMetadata, metadata),
                Replace(IHasUpdatedAt.PropertyNameUpdatedAt, DateTimeOffset.UtcNow)
            });
    }

    public async Task<(List<WebPageDocumentChunk> WebPageDocumentChunks, string? NextContinuationToken)> GetWebPageDocumentChunksAsync(
            string sleekflowCompanyId,
            string documentId,
            string? continuationToken,
            int limit)
    {
        return await GetContinuationTokenizedObjectsAsync(
            e =>
                e.SleekflowCompanyId == sleekflowCompanyId
                && e.DocumentId == documentId,
            continuationToken,
            limit);
    }
}