﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.WebScrapers.ApifyIntegrations;
using Sleekflow.IntelligentHub.WebScrapers;

namespace Sleekflow.IntelligentHub.Controllers;

[ApiController]
[ApiVersion("1.0")]
[Route("[Controller]")]
public class WebhookController : ControllerBase
{
    private readonly IWebScraperWebhookService _webScraperWebhookService;
    private readonly ILogger<WebhookController> _logger;

    public WebhookController(
        IWebScraperWebhookService webScraperWebhookService,
        ILogger<WebhookController> logger)
    {
        _webScraperWebhookService = webScraperWebhookService;
        _logger = logger;
    }

    [HttpPost]
    [Route("web-scraper")]
    public async Task<ActionResult> HandleWebScraperWebhook()
    {
        var requestBody = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync();

        try
        {
            var apifyWebhook = JsonConvert.DeserializeObject<ApifyWebhook>(requestBody);
            if (apifyWebhook == null)
            {
                throw new ArgumentException("Cannot deserialize request body");
            }

            await _webScraperWebhookService.HandleApifyWebhookAsync(apifyWebhook);
        }
        catch (Exception e)
        {
            _logger.LogError("[HandleWebScraperWebhook] failed. {RequestBody} {ErrorMessage}", requestBody, e.Message);
            return BadRequest();
        }

        return Ok();
    }
}