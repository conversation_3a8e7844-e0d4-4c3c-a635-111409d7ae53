﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;

public record VtexShippingDataDto
{
    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("address")]
    public VtexAddressDto Address { get; set; }

    [JsonConstructor]
    public VtexShippingDataDto(string id, VtexAddressDto address)
    {
        Id = id;
        Address = address;
    }
}

public record VtexAddressDto
{
    [JsonProperty("addressType")]
    public string AddressType { get; set; }

    [JsonProperty("receiverName")]
    public string ReceiverName { get; set; }

    [JsonProperty("addressId")]
    public string AddressId { get; set; }

    [JsonProperty("postalCode")]
    public string PostalCode { get; set; }

    [JsonProperty("city")]
    public string City { get; set; }

    [JsonProperty("state")]
    public string State { get; set; }

    [JsonProperty("country")]
    public string Country { get; set; }

    [JsonProperty("street")]
    public string Street { get; set; }

    [JsonConstructor]
    public VtexAddressDto(
        string addressType,
        string receiverName,
        string addressId,
        string postalCode,
        string city,
        string state,
        string country,
        string street)
    {
        AddressType = addressType;
        ReceiverName = receiverName;
        AddressId = addressId;
        PostalCode = postalCode;
        City = city;
        State = state;
        Country = country;
        Street = street;
    }
}