openapi: 3.0.1
info:
  title: Sleekflow 1.0
  version: '1.0'
servers:
  - url: https://localhost:7074
    description: Local
  - url: https://sleekflow-dev-gug7frbbe9grfvhh.z01.azurefd.net/v1/tenant-hub
    description: Dev Apigw
  - url: https://sleekflow-a6aeg3bth3f5fneq.z01.azurefd.net/v1/tenant-hub
    description: Prod Apigw
paths:
  /authorized/Blobs/CreateBlobUploadSasUrls:
    post:
      tags:
        - AuthorizedBlobs
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateBlobUploadSasUrlsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateBlobUploadSasUrlsOutputOutput'
  /authorized/Companies/CreateCompany:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateCompanyOutputOutput'
  /authorized/Companies/DeleteCompanyStaff:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedDeleteCompanyStaffInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedDeleteCompanyStaffOutputOutput'
  /authorized/Companies/DeleteCompanyStaffCompletely:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedDeleteCompanyStaffCompletelyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedDeleteCompanyStaffCompletelyOutputOutput'
  /authorized/Companies/GenerateInviteLink:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGenerateInviteLinkInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGenerateInviteLinkOutputOutput'
  /authorized/Companies/GetAllCompanies:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetAllCompaniesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetAllCompaniesOutputOutput'
  /authorized/Companies/GetCompany:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetCompanyOutputOutput'
  /authorized/Companies/InviteUserByEmail:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedInviteUserByEmailInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedInviteUserByEmailOutputOutput'
  /authorized/Companies/ResendInvitationEmail:
    post:
      tags:
        - AuthorizedCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedResendInvitationEmailInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedResendInvitationEmailOutputOutput'
  /authorized/EnabledFeatures/GetEnabledFeaturesForCompany:
    post:
      tags:
        - AuthorizedEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetEnabledFeaturesForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetEnabledFeaturesForCompanyOutputOutput'
  /authorized/ExperimentalFeatures/GetEnabledExperimentalFeatures:
    post:
      tags:
        - AuthorizedExperimentalFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetEnabledExperimentalFeaturesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetEnabledExperimentalFeaturesOutputOutput'
  /authorized/ExperimentalFeatures/GetExperimentalFeatures:
    post:
      tags:
        - AuthorizedExperimentalFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetExperimentalFeaturesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetExperimentalFeaturesOutputOutput'
  /authorized/ExperimentalFeatures/UpsertCompanyExperimentalFeatures:
    post:
      tags:
        - AuthorizedExperimentalFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedUpsertCompanyExperimentalFeaturesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedUpsertCompanyExperimentalFeaturesOutputOutput'
  /authorized/ExperimentalFeatures/UpsertStaffExperimentalFeatures:
    post:
      tags:
        - AuthorizedExperimentalFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedUpsertStaffExperimentalFeaturesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedUpsertStaffExperimentalFeaturesOutputOutput'
  /authorized/Features/CreateFeature:
    post:
      tags:
        - AuthorizedFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateFeatureInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateFeatureOutputOutput'
  /authorized/Features/GetAllFeatures:
    post:
      tags:
        - AuthorizedFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetAllFeaturesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetAllFeaturesOutputOutput'
  /authorized/Features/GetFeature:
    post:
      tags:
        - AuthorizedFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetFeatureInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetFeatureOutputOutput'
  /authorized/Features/UpdateFeature:
    post:
      tags:
        - AuthorizedFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedUpdateFeatureInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedUpdateFeatureOutputOutput'
  /authorized/IpWhitelists/DeleteIpWhitelistSettings:
    post:
      tags:
        - AuthorizedIpWhitelists
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedDeleteIpWhitelistSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedDeleteIpWhitelistSettingsOutputOutput'
  /authorized/IpWhitelists/GetIpWhitelistSettings:
    post:
      tags:
        - AuthorizedIpWhitelists
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetIpWhitelistSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetIpWhitelistSettingsOutputOutput'
  /authorized/IpWhitelists/UpdateIpWhitelistSettings:
    post:
      tags:
        - AuthorizedIpWhitelists
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedUpdateIpWhitelistSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedUpdateIpWhitelistSettingsOutputOutput'
  /authorized/IpWhitelists/ValidateIpWhitelistSettings:
    post:
      tags:
        - AuthorizedIpWhitelists
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedValidateIpWhitelistSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedValidateIpWhitelistSettingsOutputOutput'
  /authorized/PlanDefinitions/CreatePlanDefinition:
    post:
      tags:
        - AuthorizedPlanDefinitions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreatePlanDefinitionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreatePlanDefinitionOutputOutput'
  /authorized/PlanDefinitions/GetAllPlanDefinitions:
    post:
      tags:
        - AuthorizedPlanDefinitions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetAllPlanDefinitionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetAllPlanDefinitionsOutputOutput'
  /authorized/PlanDefinitions/GetPlanDefinition:
    post:
      tags:
        - AuthorizedPlanDefinitions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetPlanDefinitionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetPlanDefinitionOutputOutput'
  /authorized/Plans/CreatePlan:
    post:
      tags:
        - AuthorizedPlans
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreatePlanInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreatePlanOutputOutput'
  /authorized/Plans/ExtendPlanDuration:
    post:
      tags:
        - AuthorizedPlans
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedExtendPlanDurationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedExtendPlanDurationOutputOutput'
  /authorized/Plans/GetAllPlans:
    post:
      tags:
        - AuthorizedPlans
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetAllPlansInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetAllPlansOutputOutput'
  /authorized/Plans/GetPlans:
    post:
      tags:
        - AuthorizedPlans
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetPlansInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetPlansOutputOutput'
  /authorized/Rbac/AssignUserToMultipleRoles:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedAssignUserToMultipleRolesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedAssignUserToMultipleRolesOutputOutput'
  /authorized/Rbac/AssignUserToRole:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedAssignUserToRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedAssignUserToRoleOutputOutput'
  /authorized/Rbac/BulkUpdatePermission:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedBulkUpdatePermissionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedBulkUpdatePermissionOutputOutput'
  /authorized/Rbac/CreateCustomRole:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateCustomRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateCustomRoleOutputOutput'
  /authorized/Rbac/DuplicateRole:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedDuplicateRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedDuplicateRoleOutputOutput'
  /authorized/Rbac/GetAllRolesInCompany:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetAllRolesInCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetAllRolesInCompanyOutputOutput'
  /authorized/Rbac/GetCompanyPolicies:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetCompanyPoliciesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetCompanyPoliciesOutputOutput'
  /authorized/Rbac/GetCompanyRolesWithPolicies:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetCompanyRolesWithPoliciesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetCompanyRolesWithPoliciesOutputOutput'
  /authorized/Rbac/GetRolesDetail:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetRolesDetailInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetRolesDetailOutputOutput'
  /authorized/Rbac/GetRolesWithPermissionsByStaffIds:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetRolesWithPermissionsByStaffIdsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetRolesWithPermissionsByStaffIdsOutputOutput'
  /authorized/Rbac/GetUsersByRole:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetUsersByRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetUsersByRoleOutputOutput'
  /authorized/Rbac/IsRbacEnabled:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedIsRbacEnabledInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedIsRbacEnabledOutputOutput'
  /authorized/Rbac/IsTheRoleNameExisted:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedIsTheRoleNameExistedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedIsTheRoleNameExistedOutputOutput'
  /authorized/Rbac/RemoveRole:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedRemoveRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedRemoveRoleOutputOutput'
  /authorized/Rbac/RemoveUserFromRole:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedRemoveUserFromRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedRemoveUserFromRoleOutputOutput'
  /authorized/Rbac/SaveCompanyPolicies:
    post:
      tags:
        - AuthorizedRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedSaveCompanyPoliciesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedSaveCompanyPoliciesOutputOutput'
  /authorized/Roles/CreateRole:
    post:
      tags:
        - AuthorizedRoles
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateRoleOutputOutput'
  /authorized/Roles/GetAllRoles:
    post:
      tags:
        - AuthorizedRoles
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetAllRolesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetAllRolesOutputOutput'
  /authorized/Roles/GetRole:
    post:
      tags:
        - AuthorizedRoles
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetRoleOutputOutput'
  /authorized/Subscriptions/CreateSubscription:
    post:
      tags:
        - AuthorizedSubscriptions
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateSubscriptionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateSubscriptionOutputOutput'
  /authorized/Users/<USER>
    post:
      tags:
        - AuthorizedUsers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedCreateUserInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedCreateUserOutputOutput'
  /authorized/Users/<USER>
    post:
      tags:
        - AuthorizedUsers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedDeleteUserWithAuth0Input'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedDeleteUserWithAuth0OutputOutput'
  /authorized/Users/<USER>
    post:
      tags:
        - AuthorizedUsers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetUsersInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetUsersOutputOutput'
  /authorized/UserWorkspaces/GetUserWorkspaces:
    post:
      tags:
        - AuthorizedUserWorkspaces
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthorizedGetUserWorkspacesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthorizedGetUserWorkspacesOutputOutput'
  /Companies/IsCompanyCreated:
    post:
      tags:
        - Companies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsCompanyCreatedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsCompanyCreatedOutputOutput'
  /EnabledFeatures/DisableFeatureForCompany:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisableFeatureForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DisableFeatureForCompanyOutputOutput'
  /EnabledFeatures/DisableFeatureForRole:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DisableFeatureForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DisableFeatureForRoleOutputOutput'
  /EnabledFeatures/EnableFeatureForCompany:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnableFeatureForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnableFeatureForCompanyOutputOutput'
  /EnabledFeatures/EnableFeatureForRole:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EnableFeatureForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/EnableFeatureForRoleOutputOutput'
  /EnabledFeatures/GetEnabledFeaturesForCompany:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetEnabledFeaturesForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEnabledFeaturesForCompanyOutputOutput'
  /EnabledFeatures/GetEnabledFeaturesForRole:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetEnabledFeaturesForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetEnabledFeaturesForRoleOutputOutput'
  /EnabledFeatures/GetFeatureEnablements:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFeatureEnablementsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFeatureEnablementsOutputOutput'
  /EnabledFeatures/IsFeatureEnabled:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsFeatureEnabledInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsFeatureEnabledOutputOutput'
  /EnabledFeatures/IsFeatureEnabledForCompany:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsFeatureEnabledForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsFeatureEnabledForCompanyOutputOutput'
  /EnabledFeatures/IsFeatureEnabledForRole:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsFeatureEnabledForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsFeatureEnabledForRoleOutputOutput'
  /EnabledFeatures/IsFeatureEnabledForUser:
    post:
      tags:
        - EnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsFeatureEnabledForUserInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsFeatureEnabledForUserOutputOutput'
  /Features/CreateFeature:
    post:
      tags:
        - Features
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFeatureInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateFeatureOutputOutput'
  /Features/GetAllFeatures:
    post:
      tags:
        - Features
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAllFeaturesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllFeaturesOutputOutput'
  /Features/GetFeature:
    post:
      tags:
        - Features
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetFeatureInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetFeatureOutputOutput'
  /Geolocations/GetClosestDataCenter:
    post:
      tags:
        - Geolocations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetClosestDataCenterInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetClosestDataCenterOutputOutput'
  /Internals/GetUserAuthenticationDetails:
    post:
      tags:
        - Internals
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserAuthenticationDetailsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserAuthenticationDetailsOutputOutput'
  /Invite/CompleteEmailInvitation:
    post:
      tags:
        - Invite
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompleteEmailInvitationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompleteEmailInvitationOutputOutput'
  /Invite/CompleteLinkInvitation:
    post:
      tags:
        - Invite
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompleteLinkInvitationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CompleteLinkInvitationOutputOutput'
  /IpWhitelists/IsAllowedIp:
    post:
      tags:
        - IpWhitelists
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsAllowedIpInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsAllowedIpOutputOutput'
  /management/Companies/GetAllCompanies:
    post:
      tags:
        - ManagementCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetAllCompaniesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetAllCompaniesOutputOutput'
  /management/Companies/IsCompanyCreated:
    post:
      tags:
        - ManagementCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsCompanyCreatedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsCompanyCreatedOutputOutput'
  /management/Companies/OnUpdateStaffInformation:
    post:
      tags:
        - ManagementCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementOnUpdateStaffInformationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementOnUpdateStaffInformationOutputOutput'
  /management/EnabledFeatures/DisableFeatureForCompany:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementDisableFeatureForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementDisableFeatureForCompanyOutputOutput'
  /management/EnabledFeatures/DisableFeatureForRole:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementDisableFeatureForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementDisableFeatureForRoleOutputOutput'
  /management/EnabledFeatures/EnableFeatureForCompany:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementEnableFeatureForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementEnableFeatureForCompanyOutputOutput'
  /management/EnabledFeatures/EnableFeatureForRole:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementEnableFeatureForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementEnableFeatureForRoleOutputOutput'
  /management/EnabledFeatures/GetEnabledFeaturesForCompany:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetEnabledFeaturesForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetEnabledFeaturesForCompanyOutputOutput'
  /management/EnabledFeatures/GetEnabledFeaturesForRole:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetEnabledFeaturesForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetEnabledFeaturesForRoleOutputOutput'
  /management/EnabledFeatures/GetFeatureEnablements:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetFeatureEnablementsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetFeatureEnablementsOutputOutput'
  /management/EnabledFeatures/IsFeatureEnabled:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsFeatureEnabledInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsFeatureEnabledOutputOutput'
  /management/EnabledFeatures/IsFeatureEnabledForCompany:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsFeatureEnabledForCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsFeatureEnabledForCompanyOutputOutput'
  /management/EnabledFeatures/IsFeatureEnabledForRole:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsFeatureEnabledForRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsFeatureEnabledForRoleOutputOutput'
  /management/EnabledFeatures/IsFeatureEnabledForUser:
    post:
      tags:
        - ManagementEnabledFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsFeatureEnabledForUserInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsFeatureEnabledForUserOutputOutput'
  /management/Features/CreateFeature:
    post:
      tags:
        - ManagementFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementCreateFeatureInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementCreateFeatureOutputOutput'
  /management/Features/GetAllFeatures:
    post:
      tags:
        - ManagementFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetAllFeaturesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetAllFeaturesOutputOutput'
  /management/Features/GetFeature:
    post:
      tags:
        - ManagementFeatures
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetFeatureInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetFeatureOutputOutput'
  /management/ImportUser/GetImportUserProgress:
    post:
      tags:
        - ManagementImportUser
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetImportUserProgressInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetImportUserProgressOutputOutput'
  /management/ImportUser/ImportUserFromCsv:
    post:
      tags:
        - ManagementImportUser
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementImportUserFromCsvInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementImportUserFromCsvOutputOutput'
  /management/IpWhitelists/GetIpWhitelistSettings:
    post:
      tags:
        - ManagementIpWhitelists
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetIpWhitelistSettingsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetIpWhitelistSettingsOutputOutput'
  /management/IpWhitelists/IsAllowedIp:
    post:
      tags:
        - ManagementIpWhitelists
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsAllowedIpInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsAllowedIpOutputOutput'
  /management/Rbac/AddRbacEnabledCompany:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementAddRbacEnabledCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementAddRbacEnabledCompanyOutputOutput'
  /management/Rbac/AssignUserToMultipleRoles:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementAssignUserToMultipleRolesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementAssignUserToMultipleRolesOutputOutput'
  /management/Rbac/CleanRbacCache:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementCleanRbacCacheInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementCleanRbacCacheOutputOutput'
  /management/Rbac/GetAllRolesInCompanyWithPermissions:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetAllRolesInCompanyWithPermissionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetAllRolesInCompanyWithPermissionsOutputOutput'
  /management/Rbac/GetAllStaffRolesWithPermissions:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetAllStaffRolesWithPermissionsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetAllStaffRolesWithPermissionsOutputOutput'
  /management/Rbac/GetRolesByStaffId:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetRolesByStaffIdInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetRolesByStaffIdOutputOutput'
  /management/Rbac/GetRolesByStaffIds:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetRolesByStaffIdsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetRolesByStaffIdsOutputOutput'
  /management/Rbac/GetRolesWithPermissionsByStaffIds:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetRolesWithPermissionsByStaffIdsInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetRolesWithPermissionsByStaffIdsOutputOutput'
  /management/Rbac/IsRbacEnabled:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsRbacEnabledInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsRbacEnabledOutputOutput'
  /management/Rbac/ListRbacEnabledCompanies:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementListRbacEnabledCompaniesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementListRbacEnabledCompaniesOutputOutput'
  /management/Rbac/ManagementBulkUpdatePermission:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementManagementBulkUpdatePermissionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementManagementBulkUpdatePermissionOutputOutput'
  /management/Rbac/RemoveRbacEnabledCompany:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementRemoveRbacEnabledCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementRemoveRbacEnabledCompanyOutputOutput'
  /management/Rbac/VerifyPermission:
    post:
      tags:
        - ManagementRbac
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementVerifyPermissionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementVerifyPermissionOutputOutput'
  /management/Roles/GetAllRoles:
    post:
      tags:
        - ManagementRoles
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementGetAllRolesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementGetAllRolesOutputOutput'
  /management/Users/<USER>
    post:
      tags:
        - ManagementUsers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsUserCreatedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsUserCreatedOutputOutput'
  /management/Users/<USER>
    post:
      tags:
        - ManagementUsers
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementIsUserLostCheckInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementIsUserLostCheckOutputOutput'
  /Migrations/MigrateCompany:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigrateCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrateCompanyOutputOutput'
  /Migrations/MigratePlan:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigratePlanInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigratePlanOutputOutput'
  /Migrations/MigrateSubscription:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigrateSubscriptionInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrateSubscriptionOutputOutput'
  /Migrations/MigrateUser:
    post:
      tags:
        - Migrations
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MigrateUserInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MigrateUserOutputOutput'
  /register/Companies/IsCompanyRegistered:
    post:
      tags:
        - RegisterCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsCompanyRegisteredInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsCompanyRegisteredOutputOutput'
  /register/Companies/RegisterCompany:
    post:
      tags:
        - RegisterCompanies
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterCompanyInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterCompanyOutputOutput'
  /Roles/CreateRole:
    post:
      tags:
        - Roles
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateRoleOutputOutput'
  /Roles/GetAllRoles:
    post:
      tags:
        - Roles
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetAllRolesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAllRolesOutputOutput'
  /Roles/GetRole:
    post:
      tags:
        - Roles
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetRoleInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRoleOutputOutput'
  /Users/<USER>
    post:
      tags:
        - Users
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IsUserCreatedInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IsUserCreatedOutputOutput'
  /UserWorkspaces/GetUserWorkspaces:
    post:
      tags:
        - UserWorkspaces
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetUserWorkspacesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetUserWorkspacesOutputOutput'
  /webhooks/Auth0/PostUserChangePassword:
    post:
      tags:
        - WebhooksAuth
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostUserChangePasswordInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostUserChangePasswordOutputOutput'
  /webhooks/Auth0/PostUserLogin:
    post:
      tags:
        - WebhooksAuth
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PostUserLoginInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PostUserLoginOutputOutput'
  /webhooks/Auth0/PreUserRegistration:
    post:
      tags:
        - WebhooksAuth
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PreUserRegistrationInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PreUserRegistrationOutputOutput'
  /webhooks/Auth0/RequiresMfa:
    post:
      tags:
        - WebhooksAuth
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequiresMfaInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequiresMfaOutputOutput'
  /Workspaces/GetWorkspaces:
    post:
      tags:
        - Workspaces
      parameters:
        - name: X-API-VERSION
          in: header
          schema:
            type: string
        - name: X-Sleekflow-Distributed-Invocation-Context
          in: header
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetWorkspacesInput'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetWorkspacesOutputOutput'
components:
  schemas:
    AnalyticsMetadata:
      type: object
      properties:
        hubspot_id:
          type: string
          nullable: true
        lead_source:
          type: string
          nullable: true
        analytics:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    Auth0AppMetadata:
      type: object
      properties:
        roles:
          type: array
          items:
            type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        sleekflow_id:
          type: string
          nullable: true
        tenanthub_user_id:
          type: string
          nullable: true
        login_requires_email_verification:
          type: boolean
          nullable: true
        login_as_user:
          $ref: '#/components/schemas/LoginAsUser'
        is_rbac_enabled:
          type: boolean
          nullable: true
      additionalProperties: false
    AuthorizedAssignUserToMultipleRolesInput:
      required:
        - role_ids
        - user_id
      type: object
      properties:
        user_id:
          minLength: 1
          type: string
        role_ids:
          type: array
          items:
            type: string
        company_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedAssignUserToMultipleRolesOutput:
      type: object
      properties:
        assignment_successful:
          type: boolean
        message:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedAssignUserToMultipleRolesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedAssignUserToMultipleRolesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedAssignUserToRoleInput:
      required:
        - role_id
        - user_id
      type: object
      properties:
        user_id:
          minLength: 1
          type: string
        role_id:
          minLength: 1
          type: string
        company_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedAssignUserToRoleOutput:
      type: object
      properties:
        assignment_successful:
          type: boolean
        message:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedAssignUserToRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedAssignUserToRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedBulkUpdatePermissionInput:
      required:
        - modifications
      type: object
      properties:
        company_id:
          type: string
          nullable: true
        modifications:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizedPermissionModification'
      additionalProperties: false
    AuthorizedBulkUpdatePermissionOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    AuthorizedBulkUpdatePermissionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedBulkUpdatePermissionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCompanyExperimentalFeatureDto:
      required:
        - feature_id
      type: object
      properties:
        feature_id:
          minLength: 1
          type: string
        is_enabled:
          type: boolean
      additionalProperties: false
    AuthorizedCreateBlobUploadSasUrlsInput:
      required:
        - blob_type
        - number_of_blobs
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        number_of_blobs:
          maximum: 16
          minimum: 1
          type: integer
          format: int32
        blob_type:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedCreateBlobUploadSasUrlsOutput:
      type: object
      properties:
        upload_blobs:
          type: array
          items:
            $ref: '#/components/schemas/PublicBlob'
          nullable: true
      additionalProperties: false
    AuthorizedCreateBlobUploadSasUrlsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateBlobUploadSasUrlsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateCompanyInput:
      required:
        - company_location
        - metadata
        - name
        - platforms
        - server_location
      type: object
      properties:
        analytics_metadata:
          $ref: '#/components/schemas/AnalyticsMetadata'
        server_location:
          minLength: 1
          type: string
        company_location:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
        platforms:
          type: array
          items:
            type: string
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    AuthorizedCreateCompanyOutput:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/CompanyDto'
      additionalProperties: false
    AuthorizedCreateCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateCustomRoleInput:
      required:
        - role_name
      type: object
      properties:
        role_name:
          minLength: 1
          type: string
        description:
          type: string
          nullable: true
        company_id:
          type: string
          nullable: true
        default_role_id:
          type: string
          nullable: true
        is_enabled:
          type: boolean
          nullable: true
      additionalProperties: false
    AuthorizedCreateCustomRoleOutput:
      type: object
      properties:
        created_rbac_role:
          $ref: '#/components/schemas/RbacRole'
      additionalProperties: false
    AuthorizedCreateCustomRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateCustomRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateFeatureInput:
      required:
        - description
        - is_enabled
        - is_experimental
        - name
      type: object
      properties:
        name:
          minLength: 1
          type: string
        description:
          minLength: 1
          type: string
        categories:
          type: array
          items:
            type: string
          nullable: true
        is_enabled:
          type: boolean
        is_experimental:
          type: boolean
      additionalProperties: false
    AuthorizedCreateFeatureOutput:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/Feature'
      additionalProperties: false
    AuthorizedCreateFeatureOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateFeatureOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreatePlanDefinitionInput:
      required:
        - descriptions
        - duration
        - feature_quantities
        - metadata
        - names
        - plan_amounts
        - plan_type
        - subscription_type
      type: object
      properties:
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
        feature_quantities:
          type: array
          items:
            $ref: '#/components/schemas/FeatureQuantity'
        plan_type:
          minLength: 1
          type: string
        subscription_type:
          minLength: 1
          type: string
        plan_amounts:
          type: array
          items:
            $ref: '#/components/schemas/Price'
        version:
          type: string
          nullable: true
        duration:
          minLength: 1
          type: string
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    AuthorizedCreatePlanDefinitionOutput:
      type: object
      properties:
        plan_definition:
          $ref: '#/components/schemas/PlanDefinitionDto'
      additionalProperties: false
    AuthorizedCreatePlanDefinitionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreatePlanDefinitionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreatePlanInput:
      required:
        - duration
        - metadata
        - plan_definition_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        plan_definition_id:
          minLength: 1
          type: string
        duration:
          minLength: 1
          type: string
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    AuthorizedCreatePlanOutput:
      type: object
      properties:
        plan:
          $ref: '#/components/schemas/PlanDto'
      additionalProperties: false
    AuthorizedCreatePlanOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreatePlanOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateRoleInput:
      required:
        - description
        - is_enabled
        - name
      type: object
      properties:
        name:
          minLength: 1
          type: string
        description:
          minLength: 1
          type: string
        is_enabled:
          type: boolean
      additionalProperties: false
    AuthorizedCreateRoleOutput:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/Role'
      additionalProperties: false
    AuthorizedCreateRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateSubscriptionInput:
      required:
        - currency
        - is_free_trial
        - metadata
        - sleekflow_company_id
        - sleekflow_staff_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        plan_definition_id:
          type: string
          nullable: true
        feature_id:
          type: string
          nullable: true
        is_free_trial:
          type: boolean
        currency:
          $ref: '#/components/schemas/Currency'
        payment_context:
          $ref: '#/components/schemas/BasePaymentContext'
        metadata:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateSubscriptionOutput:
      type: object
      properties:
        subscription:
          $ref: '#/components/schemas/SubscriptionDto'
      additionalProperties: false
    AuthorizedCreateSubscriptionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateSubscriptionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedCreateUserInput:
      required:
        - display_name
        - email
        - first_name
        - last_name
        - metadata
        - phone_number
        - user_workspaces
        - username
      type: object
      properties:
        username:
          minLength: 1
          type: string
        first_name:
          minLength: 1
          type: string
        last_name:
          minLength: 1
          type: string
        display_name:
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
        phone_number:
          minLength: 1
          type: string
        user_workspaces:
          minItems: 1
          type: array
          items:
            $ref: '#/components/schemas/UserWorkspace'
        profile_picture_url:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
      additionalProperties: false
    AuthorizedCreateUserOutput:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/UserDto'
      additionalProperties: false
    AuthorizedCreateUserOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedCreateUserOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedDeleteCompanyStaffCompletelyInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        staff_id:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
        staffs:
          maxItems: 15
          type: array
          items:
            $ref: '#/components/schemas/AuthorizedSelectedStaffForDelete'
          nullable: true
      additionalProperties: false
    AuthorizedDeleteCompanyStaffCompletelyOutput:
      type: object
      additionalProperties: false
    AuthorizedDeleteCompanyStaffCompletelyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedDeleteCompanyStaffCompletelyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedDeleteCompanyStaffInput:
      required:
        - company_id
        - staff_id
        - user_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        staff_id:
          minLength: 1
          type: string
        user_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedDeleteCompanyStaffOutput:
      type: object
      additionalProperties: false
    AuthorizedDeleteCompanyStaffOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedDeleteCompanyStaffOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedDeleteIpWhitelistSettingsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedDeleteIpWhitelistSettingsOutput:
      type: object
      additionalProperties: false
    AuthorizedDeleteIpWhitelistSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedDeleteIpWhitelistSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedDeleteUserWithAuth0Input:
      required:
        - user_email
      type: object
      properties:
        user_email:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedDeleteUserWithAuth0Output:
      type: object
      additionalProperties: false
    AuthorizedDeleteUserWithAuth0OutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedDeleteUserWithAuth0Output'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedDuplicateRoleInput:
      required:
        - role_id
      type: object
      properties:
        role_id:
          minLength: 1
          type: string
        company_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedDuplicateRoleOutput:
      type: object
      properties:
        duplicated_rbac_role:
          $ref: '#/components/schemas/RbacRole'
      additionalProperties: false
    AuthorizedDuplicateRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedDuplicateRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedExtendPlanDurationInput:
      required:
        - metadata
        - plan_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        plan_id:
          minLength: 1
          type: string
        duration:
          type: string
          nullable: true
        custom_extensions:
          type: string
          format: date-time
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    AuthorizedExtendPlanDurationOutput:
      type: object
      additionalProperties: false
    AuthorizedExtendPlanDurationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedExtendPlanDurationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGenerateInviteLinkInput:
      required:
        - data
      type: object
      properties:
        data:
          $ref: '#/components/schemas/ShareableInvitationViewModel'
        location:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGenerateInviteLinkOutput:
      type: object
      properties:
        company_name:
          type: string
          nullable: true
        invitation_id:
          type: string
          nullable: true
        role:
          type: string
          nullable: true
        team_ids:
          type: array
          items:
            type: integer
            format: int64
          nullable: true
        team_names:
          type: array
          items:
            type: string
          nullable: true
        quota:
          type: integer
          format: int32
        redeemed:
          type: integer
          format: int32
        expiration_date:
          type: string
          format: date-time
        status:
          type: string
          nullable: true
        generated_by:
          $ref: '#/components/schemas/StaffWithoutCompanyResponse'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        shareable_invitation_records:
          type: array
          items:
            $ref: '#/components/schemas/ShareableInvitationRecordResponse'
          nullable: true
        location:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGenerateInviteLinkOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGenerateInviteLinkOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAllCompaniesInput:
      required:
        - platform
      type: object
      properties:
        platform:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetAllCompaniesOutput:
      type: object
      properties:
        companies:
          type: array
          items:
            $ref: '#/components/schemas/CompanyDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetAllCompaniesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetAllCompaniesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAllFeaturesInput:
      type: object
      additionalProperties: false
    AuthorizedGetAllFeaturesOutput:
      type: object
      properties:
        features:
          type: array
          items:
            $ref: '#/components/schemas/Feature'
          nullable: true
      additionalProperties: false
    AuthorizedGetAllFeaturesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetAllFeaturesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAllPlanDefinitionsInput:
      type: object
      additionalProperties: false
    AuthorizedGetAllPlanDefinitionsOutput:
      type: object
      properties:
        subscriptions:
          type: array
          items:
            $ref: '#/components/schemas/PlanDefinitionDto'
          nullable: true
        add_ons:
          type: array
          items:
            $ref: '#/components/schemas/PlanDefinitionDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetAllPlanDefinitionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetAllPlanDefinitionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAllPlansInput:
      type: object
      additionalProperties: false
    AuthorizedGetAllPlansOutput:
      type: object
      properties:
        plans:
          type: array
          items:
            $ref: '#/components/schemas/PlanDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetAllPlansOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetAllPlansOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAllRolesInCompanyInput:
      type: object
      properties:
        company_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAllRolesInCompanyOutput:
      type: object
      properties:
        roles:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizedRoleDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetAllRolesInCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetAllRolesInCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetAllRolesInput:
      type: object
      additionalProperties: false
    AuthorizedGetAllRolesOutput:
      type: object
      properties:
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
          nullable: true
      additionalProperties: false
    AuthorizedGetAllRolesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetAllRolesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyInput:
      required:
        - id
      type: object
      properties:
        id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetCompanyOutput:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/CompanyDto'
      additionalProperties: false
    AuthorizedGetCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyPoliciesInput:
      required:
        - role
      type: object
      properties:
        company_id:
          type: string
          nullable: true
        role:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetCompanyPoliciesOutput:
      type: object
      properties:
        permissions:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyPoliciesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetCompanyPoliciesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyRolesWithPoliciesInput:
      type: object
      additionalProperties: false
    AuthorizedGetCompanyRolesWithPoliciesOutput:
      type: object
      properties:
        roles_with_policies:
          type: array
          items:
            $ref: '#/components/schemas/RbacRolesWithPolicies'
          nullable: true
      additionalProperties: false
    AuthorizedGetCompanyRolesWithPoliciesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetCompanyRolesWithPoliciesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetEnabledExperimentalFeaturesInput:
      type: object
      additionalProperties: false
    AuthorizedGetEnabledExperimentalFeaturesOutput:
      type: object
      properties:
        enabled_company_features:
          type: array
          items:
            $ref: '#/components/schemas/ExperimentalFeatureDto'
          nullable: true
        enabled_staff_features:
          type: array
          items:
            $ref: '#/components/schemas/ExperimentalFeatureDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetEnabledExperimentalFeaturesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetEnabledExperimentalFeaturesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetEnabledFeaturesForCompanyInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetEnabledFeaturesForCompanyOutput:
      type: object
      properties:
        enabled_features:
          type: array
          items:
            $ref: '#/components/schemas/Feature'
          nullable: true
      additionalProperties: false
    AuthorizedGetEnabledFeaturesForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetEnabledFeaturesForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetExperimentalFeaturesInput:
      type: object
      additionalProperties: false
    AuthorizedGetExperimentalFeaturesOutput:
      type: object
      properties:
        company_features:
          type: array
          items:
            $ref: '#/components/schemas/FeatureDto'
          nullable: true
        staff_features:
          type: array
          items:
            $ref: '#/components/schemas/FeatureDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetExperimentalFeaturesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetExperimentalFeaturesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetFeatureInput:
      required:
        - feature_id
      type: object
      properties:
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetFeatureOutput:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/Feature'
      additionalProperties: false
    AuthorizedGetFeatureOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetFeatureOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetIpWhitelistSettingsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetIpWhitelistSettingsOutput:
      type: object
      properties:
        ip_whitelist_settings:
          $ref: '#/components/schemas/IpWhitelistSettings'
      additionalProperties: false
    AuthorizedGetIpWhitelistSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetIpWhitelistSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetPlanDefinitionInput:
      required:
        - id
      type: object
      properties:
        id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetPlanDefinitionOutput:
      type: object
      properties:
        plan_definition:
          $ref: '#/components/schemas/PlanDefinitionDto'
      additionalProperties: false
    AuthorizedGetPlanDefinitionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetPlanDefinitionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetPlansInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetPlansOutput:
      type: object
      properties:
        plans:
          type: array
          items:
            $ref: '#/components/schemas/PlanDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetPlansOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetPlansOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetRoleInput:
      required:
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_role_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetRoleOutput:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/Role'
      additionalProperties: false
    AuthorizedGetRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetRolesDetailInput:
      required:
        - role_id
      type: object
      properties:
        role_id:
          minLength: 1
          type: string
        company_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetRolesDetailOutput:
      type: object
      properties:
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        permissions:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetRolesDetailOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetRolesDetailOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetRolesWithPermissionsByStaffIdsInput:
      required:
        - staff_ids
      type: object
      properties:
        company_id:
          type: string
          nullable: true
        staff_ids:
          type: array
          items:
            type: string
        permission_filter:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetRolesWithPermissionsByStaffIdsOutput:
      type: object
      properties:
        staff_roles:
          type: array
          items:
            $ref: '#/components/schemas/RbacUserRolePermissions'
          nullable: true
      additionalProperties: false
    AuthorizedGetRolesWithPermissionsByStaffIdsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetRolesWithPermissionsByStaffIdsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetUserWorkspacesInput:
      type: object
      additionalProperties: false
    AuthorizedGetUserWorkspacesOutput:
      type: object
      properties:
        user_workspaces:
          type: array
          items:
            $ref: '#/components/schemas/UserWorkspaceDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetUserWorkspacesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetUserWorkspacesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetUsersByRoleInput:
      required:
        - role_id
      type: object
      properties:
        company_id:
          type: string
          nullable: true
        role_id:
          minLength: 1
          type: string
        page_number:
          maximum: 2147483647
          minimum: 1
          type: integer
          format: int32
        page_size:
          maximum: 2147483647
          minimum: 1
          type: integer
          format: int32
        sort_by:
          type: string
          nullable: true
        sort_order:
          type: string
          nullable: true
        search_query:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetUsersByRoleOutput:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserDto'
          nullable: true
        total_count:
          type: integer
          format: int32
        page_number:
          type: integer
          format: int32
        page_size:
          type: integer
          format: int32
      additionalProperties: false
    AuthorizedGetUsersByRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetUsersByRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedGetUsersInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedGetUsersOutput:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserDto'
          nullable: true
      additionalProperties: false
    AuthorizedGetUsersOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedGetUsersOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedInviteUserByEmailInput:
      required:
        - invite_users
        - location
        - team_ids
      type: object
      properties:
        tenanthub_user_id:
          type: string
          nullable: true
        invite_users:
          type: array
          items:
            $ref: '#/components/schemas/InviteUserObject'
        team_ids:
          type: array
          items:
            type: integer
            format: int64
        location:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedInviteUserByEmailOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          type: array
          items:
            $ref: '#/components/schemas/InviteUserByEmailResponseObject'
          nullable: true
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedIsRbacEnabledInput:
      type: object
      additionalProperties: false
    AuthorizedIsRbacEnabledOutput:
      type: object
      properties:
        is_enabled:
          type: boolean
      additionalProperties: false
    AuthorizedIsRbacEnabledOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedIsRbacEnabledOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedIsTheRoleNameExistedInput:
      required:
        - role_name
      type: object
      properties:
        company_id:
          type: string
          nullable: true
        role_name:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedIsTheRoleNameExistedOutput:
      type: object
      properties:
        exists:
          type: boolean
      additionalProperties: false
    AuthorizedIsTheRoleNameExistedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedIsTheRoleNameExistedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedPermissionModification:
      required:
        - permission
        - role_ids_to_add
        - role_ids_to_remove
      type: object
      properties:
        permission:
          minLength: 1
          type: string
        role_ids_to_add:
          type: array
          items:
            type: string
        role_ids_to_remove:
          type: array
          items:
            type: string
      additionalProperties: false
    AuthorizedRemoveRoleInput:
      required:
        - role_id
      type: object
      properties:
        role_id:
          minLength: 1
          type: string
        company_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedRemoveRoleOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    AuthorizedRemoveRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedRemoveRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedRemoveUserFromRoleInput:
      required:
        - role_id
        - user_id
      type: object
      properties:
        user_id:
          minLength: 1
          type: string
        role_id:
          minLength: 1
          type: string
        company_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedRemoveUserFromRoleOutput:
      type: object
      properties:
        removal_successful:
          type: boolean
        message:
          type: string
          nullable: true
        error_key:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedRemoveUserFromRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedRemoveUserFromRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedResendInvitationEmailInput:
      required:
        - sleekflow_user_id
      type: object
      properties:
        sleekflow_user_id:
          minLength: 1
          type: string
        location:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedResendInvitationEmailOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    AuthorizedResendInvitationEmailOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedResendInvitationEmailOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedRoleDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        user_count:
          type: integer
          format: int32
        is_default:
          type: boolean
      additionalProperties: false
    AuthorizedSaveCompanyPoliciesInput:
      required:
        - name
        - permissions
      type: object
      properties:
        company_id:
          type: string
          nullable: true
        role_id:
          type: string
          nullable: true
        name:
          minLength: 1
          type: string
        description:
          type: string
          nullable: true
        permissions:
          type: array
          items:
            type: string
      additionalProperties: false
    AuthorizedSaveCompanyPoliciesOutput:
      type: object
      properties:
        permissions:
          type: array
          items:
            type: string
          nullable: true
        is_success:
          type: boolean
      additionalProperties: false
    AuthorizedSaveCompanyPoliciesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedSaveCompanyPoliciesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedSelectedStaffForDelete:
      required:
        - staff_id
        - user_id
      type: object
      properties:
        staff_id:
          minLength: 1
          type: string
        user_id:
          minLength: 1
          type: string
      additionalProperties: false
    AuthorizedStaffExperimentalFeatureDto:
      required:
        - feature_id
      type: object
      properties:
        feature_id:
          minLength: 1
          type: string
        is_enabled:
          type: boolean
      additionalProperties: false
    AuthorizedUpdateFeatureInput:
      required:
        - description
        - id
        - is_enabled
        - is_experimental
        - name
      type: object
      properties:
        id:
          minLength: 1
          type: string
        name:
          minLength: 1
          type: string
        description:
          minLength: 1
          type: string
        categories:
          type: array
          items:
            type: string
          nullable: true
        is_enabled:
          type: boolean
        is_experimental:
          type: boolean
      additionalProperties: false
    AuthorizedUpdateFeatureOutput:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/Feature'
      additionalProperties: false
    AuthorizedUpdateFeatureOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedUpdateFeatureOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedUpdateIpWhitelistSettingsInput:
      required:
        - ip_range_details
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        ip_range_details:
          type: array
          items:
            $ref: '#/components/schemas/IpRangeDetails'
      additionalProperties: false
    AuthorizedUpdateIpWhitelistSettingsOutput:
      type: object
      properties:
        ip_whitelist_settings:
          $ref: '#/components/schemas/IpWhitelistSettings'
      additionalProperties: false
    AuthorizedUpdateIpWhitelistSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedUpdateIpWhitelistSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedUpsertCompanyExperimentalFeaturesInput:
      required:
        - company_experimental_features
      type: object
      properties:
        company_experimental_features:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizedCompanyExperimentalFeatureDto'
      additionalProperties: false
    AuthorizedUpsertCompanyExperimentalFeaturesOutput:
      type: object
      properties:
        company_features:
          type: array
          items:
            $ref: '#/components/schemas/ExperimentalFeatureDto'
          nullable: true
      additionalProperties: false
    AuthorizedUpsertCompanyExperimentalFeaturesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedUpsertCompanyExperimentalFeaturesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedUpsertStaffExperimentalFeaturesInput:
      required:
        - staff_experimental_features
      type: object
      properties:
        staff_experimental_features:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizedStaffExperimentalFeatureDto'
      additionalProperties: false
    AuthorizedUpsertStaffExperimentalFeaturesOutput:
      type: object
      properties:
        staff_experimental_features:
          type: array
          items:
            $ref: '#/components/schemas/ExperimentalFeatureDto'
          nullable: true
      additionalProperties: false
    AuthorizedUpsertStaffExperimentalFeaturesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedUpsertStaffExperimentalFeaturesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    AuthorizedValidateIpWhitelistSettingsInput:
      required:
        - ip_range_details
      type: object
      properties:
        ip_range_details:
          type: array
          items:
            $ref: '#/components/schemas/IpRangeDetails'
      additionalProperties: false
    AuthorizedValidateIpWhitelistSettingsOutput:
      type: object
      properties:
        has_error:
          type: boolean
        errors:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    AuthorizedValidateIpWhitelistSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/AuthorizedValidateIpWhitelistSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    BasePaymentContext:
      type: object
      properties:
        source:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        snapshotted_currency_rate:
          $ref: '#/components/schemas/CurrencyRate'
        created_at:
          type: string
          format: date-time
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    CleanIpWhitelistCacheOutput:
      type: object
      properties:
        success:
          type: boolean
        is_enabled:
          type: boolean
        data:
          $ref: '#/components/schemas/IpWhitelistSettings'
      additionalProperties: false
    Company:
      type: object
      properties:
        analytics_metadata:
          $ref: '#/components/schemas/AnalyticsMetadata'
        server_location:
          type: string
          nullable: true
        company_location:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        owner_id:
          type: string
          nullable: true
        platforms:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    CompanyDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        server_location:
          type: string
          nullable: true
        company_location:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        owner_id:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_at:
          type: string
          format: date-time
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    CompleteEmailInvitationInput:
      required:
        - tenanthub_user_id
      type: object
      properties:
        sleekflow_user_id:
          type: string
          nullable: true
        tenanthub_user_id:
          minLength: 1
          type: string
        user_name:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        password:
          type: string
          nullable: true
        token:
          type: string
          nullable: true
        position:
          type: string
          nullable: true
        time_zone_info_id:
          type: string
          nullable: true
        location:
          type: string
          nullable: true
      additionalProperties: false
    CompleteEmailInvitationOutput:
      type: object
      properties:
        message:
          type: string
          nullable: true
      additionalProperties: false
    CompleteEmailInvitationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CompleteEmailInvitationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CompleteLinkInvitationInput:
      required:
        - invite_shared_user_object
        - shareable_id
      type: object
      properties:
        shareable_id:
          minLength: 1
          type: string
        invite_shared_user_object:
          $ref: '#/components/schemas/InviteUserByLinkObject'
        location:
          type: string
          nullable: true
      additionalProperties: false
    CompleteLinkInvitationOutput:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        response:
          $ref: '#/components/schemas/CompleteLinkInvitationResponse'
      additionalProperties: false
    CompleteLinkInvitationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CompleteLinkInvitationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CompleteLinkInvitationResponse:
      type: object
      properties:
        user_id:
          type: string
          nullable: true
        company_id:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        role_type:
          type: string
          nullable: true
        team_ids:
          type: array
          items:
            type: string
          nullable: true
        auth0_user:
          $ref: '#/components/schemas/ManagementUser'
      additionalProperties: false
    CreateFeatureInput:
      required:
        - description
        - is_enabled
        - name
      type: object
      properties:
        name:
          minLength: 1
          type: string
        description:
          minLength: 1
          type: string
        categories:
          type: array
          items:
            type: string
          nullable: true
        is_enabled:
          type: boolean
        is_experimental:
          type: boolean
      additionalProperties: false
    CreateFeatureOutput:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/Feature'
      additionalProperties: false
    CreateFeatureOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateFeatureOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CreateRoleInput:
      required:
        - description
        - is_enabled
        - name
      type: object
      properties:
        name:
          minLength: 1
          type: string
        description:
          minLength: 1
          type: string
        is_enabled:
          type: boolean
      additionalProperties: false
    CreateRoleOutput:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/Role'
      additionalProperties: false
    CreateRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/CreateRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    CsvRecord:
      type: object
      properties:
        firstName:
          type: string
          nullable: true
        lastName:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        password:
          type: string
          nullable: true
        username:
          type: string
          nullable: true
        role:
          type: string
          nullable: true
        team:
          type: string
          nullable: true
        position:
          type: string
          nullable: true
      additionalProperties: false
    Currency:
      type: object
      properties:
        currency_iso_code:
          type: string
          nullable: true
        currency_name:
          type: string
          nullable: true
        currency_symbol:
          type: string
          nullable: true
      additionalProperties: false
    CurrencyRate:
      type: object
      properties:
        source_currency:
          $ref: '#/components/schemas/Currency'
        target_currency:
          $ref: '#/components/schemas/Currency'
        rate:
          type: number
          format: decimal
        markup_rate:
          type: number
          format: decimal
        is_approved:
          type: boolean
        approved_by:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    Details:
      type: object
      properties:
        csv_record:
          $ref: '#/components/schemas/CsvRecord'
        status:
          type: string
          nullable: true
        message:
          type: string
          nullable: true
      additionalProperties: false
    DisableFeatureForCompanyInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    DisableFeatureForCompanyOutput:
      type: object
      additionalProperties: false
    DisableFeatureForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DisableFeatureForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    DisableFeatureForRoleInput:
      required:
        - feature_id
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    DisableFeatureForRoleOutput:
      type: object
      additionalProperties: false
    DisableFeatureForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/DisableFeatureForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnableFeatureForCompanyInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    EnableFeatureForCompanyOutput:
      type: object
      properties:
        enabled_feature:
          $ref: '#/components/schemas/EnabledFeature'
      additionalProperties: false
    EnableFeatureForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnableFeatureForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnableFeatureForRoleInput:
      required:
        - feature_id
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    EnableFeatureForRoleOutput:
      type: object
      properties:
        enabled_feature:
          $ref: '#/components/schemas/EnabledFeature'
      additionalProperties: false
    EnableFeatureForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/EnableFeatureForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    EnabledFeature:
      type: object
      properties:
        feature_association:
          $ref: '#/components/schemas/FeatureAssociation'
        feature_id:
          type: string
          nullable: true
        enabled_by:
          type: string
          nullable: true
        is_negative:
          type: boolean
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    ExperimentalFeatureDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        feature_id:
          type: string
          nullable: true
        is_enabled:
          type: boolean
      additionalProperties: false
    Feature:
      type: object
      properties:
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        categories:
          type: array
          items:
            type: string
          nullable: true
        is_enabled:
          type: boolean
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        is_experimental:
          type: boolean
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    FeatureAssociation:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_role_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    FeatureDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        categories:
          type: array
          items:
            type: string
          nullable: true
        is_enabled:
          type: boolean
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    FeatureQuantity:
      type: object
      properties:
        feature_id:
          type: string
          nullable: true
        quantity:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    FeatureQuantityDto:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/FeatureDto'
        quantity:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    GetAllFeaturesInput:
      type: object
      additionalProperties: false
    GetAllFeaturesOutput:
      type: object
      properties:
        features:
          type: array
          items:
            $ref: '#/components/schemas/Feature'
          nullable: true
      additionalProperties: false
    GetAllFeaturesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAllFeaturesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetAllRolesInput:
      type: object
      additionalProperties: false
    GetAllRolesOutput:
      type: object
      properties:
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
          nullable: true
      additionalProperties: false
    GetAllRolesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetAllRolesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetClosestDataCenterInput:
      type: object
      additionalProperties: false
    GetClosestDataCenterOutput:
      type: object
      properties:
        data_center_location:
          type: string
          nullable: true
      additionalProperties: false
    GetClosestDataCenterOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetClosestDataCenterOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetEnabledFeaturesForCompanyInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetEnabledFeaturesForCompanyOutput:
      type: object
      properties:
        enabled_features:
          type: array
          items:
            $ref: '#/components/schemas/EnabledFeature'
          nullable: true
      additionalProperties: false
    GetEnabledFeaturesForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetEnabledFeaturesForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetEnabledFeaturesForRoleInput:
      required:
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetEnabledFeaturesForRoleOutput:
      type: object
      properties:
        enabled_features:
          type: array
          items:
            $ref: '#/components/schemas/EnabledFeature'
          nullable: true
      additionalProperties: false
    GetEnabledFeaturesForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetEnabledFeaturesForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFeatureEnablementsInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        feature_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetFeatureEnablementsOutput:
      type: object
      properties:
        is_feature_enabled_for_company:
          type: boolean
        is_feature_enabled_for_roles_dict:
          type: object
          additionalProperties:
            type: boolean
          nullable: true
      additionalProperties: false
    GetFeatureEnablementsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFeatureEnablementsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetFeatureInput:
      required:
        - feature_id
      type: object
      properties:
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetFeatureOutput:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/Feature'
      additionalProperties: false
    GetFeatureOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetFeatureOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetRoleInput:
      required:
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_role_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetRoleOutput:
      type: object
      properties:
        role:
          $ref: '#/components/schemas/Role'
      additionalProperties: false
    GetRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserAuthenticationDetailsInput:
      required:
        - sleekflow_user_id
      type: object
      properties:
        sleekflow_tenanthub_user_id:
          type: string
          nullable: true
        sleekflow_user_id:
          minLength: 1
          type: string
        sleekflow_email:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        targeted_company_id:
          type: string
          nullable: true
        login_as_user:
          $ref: '#/components/schemas/LoginAsUser'
      additionalProperties: false
    GetUserAuthenticationDetailsOutput:
      type: object
      properties:
        sleekflow_tenanthub_user_id:
          type: string
          nullable: true
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_roles:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_role_ids:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_team_ids:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_impersonator:
          $ref: '#/components/schemas/Impersonator'
        is_rbac_enabled:
          type: boolean
        rbac_role:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetUserAuthenticationDetailsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserAuthenticationDetailsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetUserInput:
      required:
        - sleekflow_user_id
      type: object
      properties:
        sleekflow_user_id:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserLossInfoInput:
      required:
        - email
        - userId
      type: object
      properties:
        userId:
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
      additionalProperties: false
    GetUserLossInfoOutput:
      type: object
      properties:
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        role_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetUserOutput:
      type: object
      properties:
        staff_id:
          type: integer
          format: int64
        company_id:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        display_id:
          type: string
          nullable: true
        user_name:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        position:
          type: string
          nullable: true
        created_at:
          type: string
          format: date-time
        last_login_at:
          type: string
          format: date-time
        email_confirmed:
          type: boolean
        status:
          type: string
          nullable: true
        role:
          type: string
          nullable: true
        role_type:
          type: string
          nullable: true
        roles:
          type: array
          items:
            type: string
          nullable: true
        role_ids:
          type: array
          items:
            type: string
          nullable: true
        time_zone_info_id:
          type: string
          nullable: true
        team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    GetUserWorkspacesInput:
      type: object
      additionalProperties: false
    GetUserWorkspacesOutput:
      type: object
      properties:
        user_workspaces:
          type: array
          items:
            $ref: '#/components/schemas/UserWorkspaceDto'
          nullable: true
      additionalProperties: false
    GetUserWorkspacesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetUserWorkspacesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    GetWorkspacesInput:
      type: object
      additionalProperties: false
    GetWorkspacesOutput:
      type: object
      properties:
        workspaces:
          type: array
          items:
            $ref: '#/components/schemas/UserWorkspaceDto'
          nullable: true
      additionalProperties: false
    GetWorkspacesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/GetWorkspacesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Impersonator:
      type: object
      properties:
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_tenanthub_user_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    ImportUserProgress:
      type: object
      properties:
        task_id:
          type: string
          nullable: true
        status:
          type: string
          nullable: true
        details:
          type: array
          items:
            $ref: '#/components/schemas/Details'
          nullable: true
      additionalProperties: false
    InviteUserByEmailResponseObject:
      type: object
      properties:
        email:
          type: string
          nullable: true
        username:
          type: string
          nullable: true
        firstname:
          type: string
          nullable: true
        lastname:
          type: string
          nullable: true
        user_role:
          type: string
          nullable: true
        position:
          type: string
          nullable: true
        time_zone_info_id:
          type: string
          nullable: true
        staff_id:
          type: integer
          format: int64
        sleekflow_user_id:
          type: string
          nullable: true
        tenant_hub_user_id:
          type: string
          nullable: true
        auth0_user:
          $ref: '#/components/schemas/ManagementUser'
        user_rbac_roles:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    InviteUserByLinkObject:
      required:
        - email
        - password
      type: object
      properties:
        tenanthub_user_id:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
        email:
          minLength: 1
          type: string
        user_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        position:
          type: string
          nullable: true
        time_zone_info_id:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        password:
          minLength: 1
          type: string
        confirm_password:
          type: string
          format: password
          nullable: true
      additionalProperties: false
    InviteUserObject:
      required:
        - email
        - user_role
      type: object
      properties:
        tenanthub_user_id:
          type: string
          nullable: true
        sleekflow_user_id:
          type: string
          nullable: true
        email:
          minLength: 1
          type: string
        user_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        user_role:
          minLength: 1
          type: string
        user_rbac_roles:
          type: array
          items:
            type: string
          nullable: true
        position:
          type: string
          nullable: true
        time_zone_info_id:
          type: string
          nullable: true
      additionalProperties: false
    IpRangeDetails:
      type: object
      properties:
        ip_range:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
      additionalProperties: false
    IpWhitelistSettings:
      type: object
      properties:
        ip_range_details:
          type: array
          items:
            $ref: '#/components/schemas/IpRangeDetails'
          nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    IsAllowedIpInput:
      required:
        - ip_address
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        ip_address:
          minLength: 1
          type: string
      additionalProperties: false
    IsAllowedIpOutput:
      type: object
      properties:
        is_allowed_ip:
          type: boolean
      additionalProperties: false
    IsAllowedIpOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsAllowedIpOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsCompanyCreatedInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    IsCompanyCreatedOutput:
      type: object
      properties:
        is_company_created:
          type: boolean
      additionalProperties: false
    IsCompanyCreatedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsCompanyCreatedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsCompanyRegisteredInput:
      type: object
      additionalProperties: false
    IsCompanyRegisteredOutput:
      type: object
      properties:
        is_company_registered:
          type: boolean
      additionalProperties: false
    IsCompanyRegisteredOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsCompanyRegisteredOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsFeatureEnabledForCompanyInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    IsFeatureEnabledForCompanyOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    IsFeatureEnabledForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsFeatureEnabledForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsFeatureEnabledForRoleInput:
      required:
        - feature_id
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    IsFeatureEnabledForRoleOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    IsFeatureEnabledForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsFeatureEnabledForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsFeatureEnabledForUserInput:
      required:
        - feature_id
        - sleekflow_user_id
      type: object
      properties:
        sleekflow_user_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    IsFeatureEnabledForUserOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    IsFeatureEnabledForUserOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsFeatureEnabledForUserOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsFeatureEnabledInput:
      required:
        - feature_id
        - role_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
        role_id:
          minLength: 1
          type: string
      additionalProperties: false
    IsFeatureEnabledOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    IsFeatureEnabledOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsFeatureEnabledOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    IsUserCreatedInput:
      required:
        - sleekflow_user_id
      type: object
      properties:
        sleekflow_user_id:
          minLength: 1
          type: string
      additionalProperties: false
    IsUserCreatedOutput:
      type: object
      properties:
        is_user_created:
          type: boolean
      additionalProperties: false
    IsUserCreatedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/IsUserCreatedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    LoginAsUser:
      type: object
      properties:
        user_Id:
          type: string
          nullable: true
        tenanthub_user_id:
          type: string
          nullable: true
        company_id:
          type: string
          nullable: true
        staff_id:
          type: integer
          format: int64
          nullable: true
        expire_at:
          type: string
          format: date-time
      additionalProperties: false
    ManagementAddRbacEnabledCompanyInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementAddRbacEnabledCompanyOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    ManagementAddRbacEnabledCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementAddRbacEnabledCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementAssignUserToMultipleRolesInput:
      required:
        - role_ids
        - sleekflow_company_id
        - user_id
      type: object
      properties:
        user_id:
          minLength: 1
          type: string
        role_ids:
          type: array
          items:
            type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementAssignUserToMultipleRolesOutput:
      type: object
      properties:
        assignment_successful:
          type: boolean
        message:
          type: string
          nullable: true
      additionalProperties: false
    ManagementAssignUserToMultipleRolesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementAssignUserToMultipleRolesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementCacheType:
      enum:
        - 0
        - 1
        - 2
      type: integer
      format: int32
    ManagementCleanRbacCacheInput:
      type: object
      properties:
        cache_type:
          $ref: '#/components/schemas/ManagementCacheType'
        show_response_time:
          type: boolean
          nullable: true
      additionalProperties: false
    ManagementCleanRbacCacheOutput:
      type: object
      properties:
        success:
          type: boolean
        message:
          type: string
          nullable: true
        response_time_ms:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    ManagementCleanRbacCacheOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementCleanRbacCacheOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementCreateFeatureInput:
      required:
        - description
        - is_enabled
        - is_experimental
        - name
      type: object
      properties:
        name:
          minLength: 1
          type: string
        description:
          minLength: 1
          type: string
        categories:
          type: array
          items:
            type: string
          nullable: true
        is_enabled:
          type: boolean
        is_experimental:
          type: boolean
      additionalProperties: false
    ManagementCreateFeatureOutput:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/Feature'
      additionalProperties: false
    ManagementCreateFeatureOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementCreateFeatureOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementDisableFeatureForCompanyInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementDisableFeatureForCompanyOutput:
      type: object
      additionalProperties: false
    ManagementDisableFeatureForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementDisableFeatureForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementDisableFeatureForRoleInput:
      required:
        - feature_id
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementDisableFeatureForRoleOutput:
      type: object
      additionalProperties: false
    ManagementDisableFeatureForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementDisableFeatureForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementEnableFeatureForCompanyInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementEnableFeatureForCompanyOutput:
      type: object
      properties:
        enabled_feature:
          $ref: '#/components/schemas/EnabledFeature'
      additionalProperties: false
    ManagementEnableFeatureForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementEnableFeatureForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementEnableFeatureForRoleInput:
      required:
        - feature_id
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementEnableFeatureForRoleOutput:
      type: object
      properties:
        enabled_feature:
          $ref: '#/components/schemas/EnabledFeature'
      additionalProperties: false
    ManagementEnableFeatureForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementEnableFeatureForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetAllCompaniesInput:
      required:
        - limit
      type: object
      properties:
        server_location:
          type: string
          nullable: true
        continuation_token:
          type: string
          nullable: true
        limit:
          maximum: 1000
          minimum: 1
          type: integer
          format: int32
      additionalProperties: false
    ManagementGetAllCompaniesOutput:
      type: object
      properties:
        companies:
          type: array
          items:
            $ref: '#/components/schemas/CompanyDto'
          nullable: true
        continuation_token:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetAllCompaniesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetAllCompaniesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetAllFeaturesInput:
      type: object
      additionalProperties: false
    ManagementGetAllFeaturesOutput:
      type: object
      properties:
        features:
          type: array
          items:
            $ref: '#/components/schemas/Feature'
          nullable: true
      additionalProperties: false
    ManagementGetAllFeaturesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetAllFeaturesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetAllRolesInCompanyWithPermissionsInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetAllRolesInCompanyWithPermissionsOutput:
      type: object
      properties:
        roles:
          type: array
          items:
            $ref: '#/components/schemas/RbacRolePermissionsItem'
          nullable: true
      additionalProperties: false
    ManagementGetAllRolesInCompanyWithPermissionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetAllRolesInCompanyWithPermissionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetAllRolesInput:
      type: object
      additionalProperties: false
    ManagementGetAllRolesOutput:
      type: object
      properties:
        roles:
          type: array
          items:
            $ref: '#/components/schemas/Role'
          nullable: true
      additionalProperties: false
    ManagementGetAllRolesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetAllRolesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetAllStaffRolesWithPermissionsInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        permission_filter:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ManagementGetAllStaffRolesWithPermissionsOutput:
      type: object
      properties:
        staff_roles:
          type: array
          items:
            $ref: '#/components/schemas/RbacUserRolePermissions'
          nullable: true
      additionalProperties: false
    ManagementGetAllStaffRolesWithPermissionsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetAllStaffRolesWithPermissionsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetEnabledFeaturesForCompanyInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetEnabledFeaturesForCompanyOutput:
      type: object
      properties:
        enabled_features:
          type: array
          items:
            $ref: '#/components/schemas/EnabledFeature'
          nullable: true
      additionalProperties: false
    ManagementGetEnabledFeaturesForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetEnabledFeaturesForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetEnabledFeaturesForRoleInput:
      required:
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetEnabledFeaturesForRoleOutput:
      type: object
      properties:
        enabled_features:
          type: array
          items:
            $ref: '#/components/schemas/EnabledFeature'
          nullable: true
      additionalProperties: false
    ManagementGetEnabledFeaturesForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetEnabledFeaturesForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetFeatureEnablementsInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        feature_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetFeatureEnablementsOutput:
      type: object
      properties:
        is_feature_enabled_for_company:
          type: boolean
        is_feature_enabled_for_roles_dict:
          type: object
          additionalProperties:
            type: boolean
          nullable: true
      additionalProperties: false
    ManagementGetFeatureEnablementsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetFeatureEnablementsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetFeatureInput:
      required:
        - feature_id
      type: object
      properties:
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetFeatureOutput:
      type: object
      properties:
        feature:
          $ref: '#/components/schemas/Feature'
      additionalProperties: false
    ManagementGetFeatureOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetFeatureOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetImportUserProgressInput:
      required:
        - task_id
      type: object
      properties:
        task_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetImportUserProgressOutput:
      type: object
      properties:
        progress:
          $ref: '#/components/schemas/ImportUserProgress'
      additionalProperties: false
    ManagementGetImportUserProgressOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetImportUserProgressOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetIpWhitelistSettingsInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetIpWhitelistSettingsOutput:
      type: object
      properties:
        ip_whitelist_settings:
          $ref: '#/components/schemas/IpWhitelistSettings'
      additionalProperties: false
    ManagementGetIpWhitelistSettingsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetIpWhitelistSettingsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetRolesByStaffIdInput:
      required:
        - company_id
        - staff_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        staff_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementGetRolesByStaffIdOutput:
      type: object
      properties:
        user_roles:
          $ref: '#/components/schemas/RbacUserRoles'
      additionalProperties: false
    ManagementGetRolesByStaffIdOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetRolesByStaffIdOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetRolesByStaffIdsInput:
      required:
        - company_id
        - staff_ids
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        staff_ids:
          type: array
          items:
            type: string
      additionalProperties: false
    ManagementGetRolesByStaffIdsOutput:
      type: object
      properties:
        user_roles:
          type: array
          items:
            $ref: '#/components/schemas/RbacUserRoles'
          nullable: true
      additionalProperties: false
    ManagementGetRolesByStaffIdsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetRolesByStaffIdsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementGetRolesWithPermissionsByStaffIdsInput:
      required:
        - company_id
        - staff_ids
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        staff_ids:
          type: array
          items:
            type: string
        permission_filter:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ManagementGetRolesWithPermissionsByStaffIdsOutput:
      type: object
      properties:
        staff_roles:
          type: array
          items:
            $ref: '#/components/schemas/RbacUserRolePermissions'
          nullable: true
      additionalProperties: false
    ManagementGetRolesWithPermissionsByStaffIdsOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementGetRolesWithPermissionsByStaffIdsOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIdentity:
      type: object
      properties:
        access_token:
          type: string
          nullable: true
        access_token_secret:
          type: string
          nullable: true
        connection:
          type: string
          nullable: true
        expires_in:
          type: integer
          format: int32
        isSocial:
          type: boolean
          nullable: true
        profileData:
          type: object
          additionalProperties: { }
          nullable: true
        provider:
          type: string
          nullable: true
        refresh_token:
          type: string
          nullable: true
        user_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementImportUserFromCsvInput:
      required:
        - blob_names
        - location
        - sleekflow_company_id
      type: object
      properties:
        blob_names:
          type: array
          items:
            type: string
        location:
          minLength: 1
          type: string
        is_enterprise_users:
          type: boolean
          nullable: true
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementImportUserFromCsvOutput:
      type: object
      properties:
        task_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementImportUserFromCsvOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementImportUserFromCsvOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsAllowedIpInput:
      required:
        - ip_address
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        ip_address:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsAllowedIpOutput:
      type: object
      properties:
        is_allowed_ip:
          type: boolean
      additionalProperties: false
    ManagementIsAllowedIpOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsAllowedIpOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsCompanyCreatedInput:
      required:
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsCompanyCreatedOutput:
      type: object
      properties:
        is_company_created:
          type: boolean
      additionalProperties: false
    ManagementIsCompanyCreatedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsCompanyCreatedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsFeatureEnabledForCompanyInput:
      required:
        - feature_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsFeatureEnabledForCompanyOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    ManagementIsFeatureEnabledForCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsFeatureEnabledForCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsFeatureEnabledForRoleInput:
      required:
        - feature_id
        - sleekflow_company_id
        - sleekflow_role_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        sleekflow_role_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsFeatureEnabledForRoleOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    ManagementIsFeatureEnabledForRoleOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsFeatureEnabledForRoleOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsFeatureEnabledForUserInput:
      required:
        - feature_id
        - sleekflow_user_id
      type: object
      properties:
        sleekflow_user_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
        sleekflow_company_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsFeatureEnabledForUserOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    ManagementIsFeatureEnabledForUserOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsFeatureEnabledForUserOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsFeatureEnabledInput:
      required:
        - feature_id
        - role_id
        - sleekflow_company_id
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        feature_id:
          minLength: 1
          type: string
        role_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsFeatureEnabledOutput:
      type: object
      properties:
        is_feature_enabled:
          type: boolean
      additionalProperties: false
    ManagementIsFeatureEnabledOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsFeatureEnabledOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsRbacEnabledInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsRbacEnabledOutput:
      type: object
      properties:
        is_enabled:
          type: boolean
      additionalProperties: false
    ManagementIsRbacEnabledOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsRbacEnabledOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsUserCreatedInput:
      required:
        - sleekflow_user_id
      type: object
      properties:
        sleekflow_user_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsUserCreatedOutput:
      type: object
      properties:
        is_user_created:
          type: boolean
      additionalProperties: false
    ManagementIsUserCreatedOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsUserCreatedOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementIsUserLostCheckInput:
      required:
        - email
      type: object
      properties:
        email:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementIsUserLostCheckOutput:
      type: object
      properties:
        users:
          type: array
          items:
            $ref: '#/components/schemas/User'
          nullable: true
      additionalProperties: false
    ManagementIsUserLostCheckOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementIsUserLostCheckOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementListRbacEnabledCompaniesInput:
      type: object
      properties:
        exclude_company_names:
          type: boolean
          nullable: true
        show_response_time:
          type: boolean
          nullable: true
      additionalProperties: false
    ManagementListRbacEnabledCompaniesOutput:
      type: object
      properties:
        companies:
          type: array
          items:
            $ref: '#/components/schemas/ManagementRbacEnabledCompanyInfo'
          nullable: true
        ids:
          type: array
          items:
            type: string
          nullable: true
        response_time_ms:
          type: integer
          format: int64
          nullable: true
      additionalProperties: false
    ManagementListRbacEnabledCompaniesOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementListRbacEnabledCompaniesOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementManagementBulkUpdatePermissionInput:
      required:
        - company_id
        - modifications
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        modifications:
          type: array
          items:
            $ref: '#/components/schemas/ManagementPermissionModification'
      additionalProperties: false
    ManagementManagementBulkUpdatePermissionOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    ManagementManagementBulkUpdatePermissionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementManagementBulkUpdatePermissionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementOnUpdateStaffInformationInput:
      required:
        - company_id
        - staff_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
        staff_id:
          minLength: 1
          type: string
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        profile_picture_url:
          type: string
          nullable: true
        role_ids:
          type: array
          items:
            type: string
          nullable: true
        team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    ManagementOnUpdateStaffInformationOutput:
      type: object
      properties:
        success:
          type: boolean
        updated_user:
          $ref: '#/components/schemas/User'
      additionalProperties: false
    ManagementOnUpdateStaffInformationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementOnUpdateStaffInformationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementPermissionModification:
      required:
        - permission
        - role_ids_to_add
        - role_ids_to_remove
      type: object
      properties:
        permission:
          minLength: 1
          type: string
        role_ids_to_add:
          type: array
          items:
            type: string
        role_ids_to_remove:
          type: array
          items:
            type: string
      additionalProperties: false
    ManagementRbacEnabledCompanyInfo:
      type: object
      properties:
        id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
      additionalProperties: false
    ManagementRemoveRbacEnabledCompanyInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
      additionalProperties: false
    ManagementRemoveRbacEnabledCompanyOutput:
      type: object
      properties:
        success:
          type: boolean
      additionalProperties: false
    ManagementRemoveRbacEnabledCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementRemoveRbacEnabledCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    ManagementUser:
      type: object
      properties:
        created_at:
          type: string
          format: date-time
          nullable: true
        identities:
          type: array
          items:
            $ref: '#/components/schemas/ManagementIdentity'
          nullable: true
        last_ip:
          type: string
          nullable: true
        last_login:
          type: string
          format: date-time
          nullable: true
        last_password_reset:
          type: string
          format: date-time
          nullable: true
        locale:
          type: string
          nullable: true
        logins_count:
          type: string
          nullable: true
        updated_at:
          type: string
          format: date-time
          nullable: true
        user_id:
          type: string
          nullable: true
        multifactor:
          type: array
          items:
            type: string
          nullable: true
        app_metadata:
          nullable: true
        email:
          type: string
          nullable: true
        email_verified:
          type: boolean
          nullable: true
        phone_number:
          type: string
          nullable: true
        phone_verified:
          type: boolean
          nullable: true
        user_metadata:
          nullable: true
        username:
          type: string
          nullable: true
        nickname:
          type: string
          nullable: true
        given_name:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        family_name:
          type: string
          nullable: true
        picture:
          type: string
          nullable: true
        blocked:
          type: boolean
          nullable: true
      additionalProperties: { }
    ManagementVerifyPermissionInput:
      required:
        - company_id
        - method
        - path
        - role
      type: object
      properties:
        method:
          minLength: 1
          type: string
        path:
          minLength: 1
          type: string
        company_id:
          minLength: 1
          type: string
        role: { }
      additionalProperties: false
    ManagementVerifyPermissionOutput:
      type: object
      properties:
        allowed:
          type: boolean
        permission:
          type: string
          nullable: true
      additionalProperties: false
    ManagementVerifyPermissionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/ManagementVerifyPermissionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    MigrateCompanyInput:
      required:
        - analytics_metadata
        - created_at
        - created_by
        - metadata
        - name
        - owner_id
        - platforms
        - sleekflow_company_id
        - updated_at
        - updated_by
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        analytics_metadata:
          $ref: '#/components/schemas/AnalyticsMetadata'
        server_location:
          type: string
          nullable: true
        company_location:
          type: string
          nullable: true
        name:
          minLength: 1
          type: string
        platforms:
          type: array
          items:
            type: string
        owner_id:
          minLength: 1
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        metadata:
          type: object
          additionalProperties:
            nullable: true
      additionalProperties: false
    MigrateCompanyOutput:
      type: object
      properties:
        company:
          $ref: '#/components/schemas/Company'
      additionalProperties: false
    MigrateCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/MigrateCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    MigratePlanInput:
      required:
        - created_at
        - duration
        - metadata
        - plan_definition_id
        - sleekflow_company_id
        - sleekflow_staff_id
        - sleekflow_team_ids
        - updated_at
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        plan_definition_id:
          minLength: 1
          type: string
        duration:
          minLength: 1
          type: string
        metadata:
          type: object
          additionalProperties:
            nullable: true
        sleekflow_staff_id:
          minLength: 1
          type: string
        sleekflow_team_ids:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    MigratePlanOutput:
      type: object
      properties:
        plan:
          $ref: '#/components/schemas/PlanDto'
      additionalProperties: false
    MigratePlanOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/MigratePlanOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    MigrateSubscriptionInput:
      required:
        - created_at
        - currency
        - metadata
        - plan_definition_id
        - sleekflow_company_id
        - updated_at
        - updated_by
      type: object
      properties:
        sleekflow_company_id:
          minLength: 1
          type: string
        plan_definition_id:
          minLength: 1
          type: string
        currency:
          $ref: '#/components/schemas/Currency'
        payment_context:
          $ref: '#/components/schemas/BasePaymentContext'
        metadata:
          type: object
          additionalProperties:
            nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    MigrateSubscriptionOutput:
      type: object
      properties:
        subscription:
          $ref: '#/components/schemas/SubscriptionDto'
      additionalProperties: false
    MigrateSubscriptionOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/MigrateSubscriptionOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    MigrateUserInput:
      required:
        - created_at
        - display_name
        - email
        - first_name
        - last_name
        - metadata
        - sleekflow_user_id
        - updated_at
        - updated_by
        - user_workspaces
        - username
      type: object
      properties:
        sleekflow_user_id:
          minLength: 1
          type: string
        username:
          minLength: 1
          type: string
        first_name:
          minLength: 1
          type: string
        last_name:
          minLength: 1
          type: string
        display_name:
          minLength: 1
          type: string
        email:
          minLength: 1
          type: string
        phone_number:
          type: string
          nullable: true
        user_workspaces:
          type: array
          items:
            $ref: '#/components/schemas/UserWorkspace'
        profile_picture_url:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    MigrateUserOutput:
      type: object
      properties:
        user:
          $ref: '#/components/schemas/User'
        is_migrated:
          type: boolean
        is_duplicated:
          type: boolean
      additionalProperties: false
    MigrateUserOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/MigrateUserOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Multilingual:
      required:
        - language_iso_code
        - value
      type: object
      properties:
        language_iso_code:
          maxLength: 128
          minLength: 1
          type: string
        value:
          maxLength: 4096
          minLength: 1
          type: string
        is_default:
          type: boolean
      additionalProperties: false
    MyTimeZoneInfo:
      type: object
      properties:
        id:
          type: string
          nullable: true
        displayName:
          type: string
          nullable: true
        standardName:
          type: string
          nullable: true
        baseUtcOffset:
          type: string
          format: date-span
        baseUtcOffsetInHour:
          type: number
          format: double
          readOnly: true
      additionalProperties: false
    Period:
      type: object
      properties:
        start:
          type: string
          format: date-time
        end:
          type: string
          format: date-time
      additionalProperties: false
    PlanDefinitionDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        names:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
          nullable: true
        descriptions:
          type: array
          items:
            $ref: '#/components/schemas/Multilingual'
          nullable: true
        feature_quantities:
          type: array
          items:
            $ref: '#/components/schemas/FeatureQuantityDto'
          nullable: true
        plan_amounts:
          type: array
          items:
            $ref: '#/components/schemas/Price'
          nullable: true
        plan_type:
          type: string
          nullable: true
        subscription_type:
          type: string
          nullable: true
        version:
          type: string
          nullable: true
        duration:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    PlanDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        sleekflow_company_id:
          type: string
          nullable: true
        plan_definition_id:
          type: string
          nullable: true
        salesperson_ids:
          type: array
          items:
            type: string
          nullable: true
        period:
          $ref: '#/components/schemas/Period'
        historical_periods:
          type: array
          items:
            $ref: '#/components/schemas/Period'
          nullable: true
        is_free_trial:
          type: boolean
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    PostUserChangePasswordInput:
      required:
        - token
        - user
      type: object
      properties:
        token:
          minLength: 1
          type: string
        user:
          $ref: '#/components/schemas/ManagementUser'
      additionalProperties: false
    PostUserChangePasswordOutput:
      type: object
      properties:
        message:
          type: string
          nullable: true
      additionalProperties: false
    PostUserChangePasswordOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PostUserChangePasswordOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PostUserLoginInput:
      required:
        - token
        - user
      type: object
      properties:
        token:
          minLength: 1
          type: string
        user:
          $ref: '#/components/schemas/ManagementUser'
        connection_strategy:
          type: string
          nullable: true
        connection_name:
          type: string
          nullable: true
      additionalProperties: false
    PostUserLoginOutput:
      type: object
      properties:
        auth0_user_id:
          type: string
          nullable: true
        app_metadata:
          $ref: '#/components/schemas/Auth0AppMetadata'
        status:
          type: string
          nullable: true
      additionalProperties: false
    PostUserLoginOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PostUserLoginOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    PreUserRegistrationInput:
      required:
        - auth0_client_id
        - auth0_event_user
        - token
      type: object
      properties:
        token:
          minLength: 1
          type: string
        auth0_event_user:
          $ref: '#/components/schemas/ManagementUser'
        auth0_client_id:
          minLength: 1
          type: string
      additionalProperties: false
    PreUserRegistrationOutput:
      type: object
      properties:
        app_metadata:
          $ref: '#/components/schemas/Auth0AppMetadata'
        status:
          type: string
          nullable: true
      additionalProperties: false
    PreUserRegistrationOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/PreUserRegistrationOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Price:
      required:
        - amount
        - currency_iso_code
      type: object
      properties:
        currency_iso_code:
          maxLength: 3
          minLength: 3
          type: string
        amount:
          type: number
          format: decimal
      additionalProperties: false
    ProfilePictureFile:
      type: object
      properties:
        id:
          type: integer
          format: int64
        profilePictureId:
          type: string
          nullable: true
        companyId:
          type: string
          nullable: true
        blobContainer:
          type: string
          nullable: true
        filename:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        mimeType:
          type: string
          nullable: true
      additionalProperties: false
    PublicBlob:
      type: object
      properties:
        container_name:
          type: string
          nullable: true
        blob_name:
          type: string
          nullable: true
        blob_id:
          type: string
          nullable: true
        url:
          type: string
          nullable: true
        expires_on:
          type: string
          format: date-time
        content_type:
          type: string
          nullable: true
      additionalProperties: false
    RbacRole:
      type: object
      properties:
        default_role_id:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
        description:
          type: string
          nullable: true
        is_enabled:
          type: boolean
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        sleekflow_company_id:
          type: string
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    RbacRoleItem:
      type: object
      properties:
        role_id:
          type: string
          nullable: true
        role_name:
          type: string
          nullable: true
        is_default:
          type: boolean
      additionalProperties: false
    RbacRolePermissionsItem:
      type: object
      properties:
        role_id:
          type: string
          nullable: true
        role_name:
          type: string
          nullable: true
        is_default:
          type: boolean
        permissions:
          type: array
          items:
            type: string
      additionalProperties: false
    RbacRolesWithPolicies:
      type: object
      properties:
        role_id:
          type: string
          nullable: true
        role_name:
          type: string
          nullable: true
        permissions:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    RbacUserRolePermissions:
      type: object
      properties:
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        roles:
          type: array
          items:
            $ref: '#/components/schemas/RbacRolePermissionsItem'
          nullable: true
      additionalProperties: false
    RbacUserRoles:
      type: object
      properties:
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        roles:
          type: array
          items:
            $ref: '#/components/schemas/RbacRoleItem'
          nullable: true
      additionalProperties: false
    RefreshIpWhitelistCacheInput:
      required:
        - company_id
      type: object
      properties:
        company_id:
          minLength: 1
          type: string
      additionalProperties: false
    RefreshIpWhitelistCacheOutput:
      type: object
      properties:
        success:
          type: boolean
        is_enabled:
          type: boolean
        data:
          $ref: '#/components/schemas/IpWhitelistSettings'
      additionalProperties: false
    RegisterCompanyInput:
      required:
        - company_name
        - company_size
        - connection_strategy
        - industry
        - location
        - phone_number
        - subscription_plan_id
        - time_zone_info_id
      type: object
      properties:
        connection_strategy:
          minLength: 1
          type: string
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone_number:
          minLength: 1
          type: string
        industry:
          minLength: 1
          type: string
        online_shop_system:
          type: string
          nullable: true
        company_website:
          type: string
          nullable: true
        company_name:
          minLength: 1
          type: string
        time_zone_info_id:
          minLength: 1
          type: string
        company_size:
          minLength: 1
          type: string
        subscription_plan_id:
          minLength: 1
          type: string
        lmref:
          type: string
          nullable: true
        heard_from:
          type: string
          nullable: true
        promotion_code:
          type: string
          nullable: true
        web_client_uuid:
          type: string
          nullable: true
        referral:
          type: string
          nullable: true
        communication_tools:
          type: array
          items:
            type: string
          nullable: true
        is_agree_marketing_consent:
          type: boolean
          nullable: true
        platform_usage_intent:
          type: array
          items:
            type: string
          nullable: true
        location:
          minLength: 1
          type: string
        partnerstack_key:
          type: string
          nullable: true
      additionalProperties: false
    RegisterCompanyOutput:
      type: object
      properties:
        user_id:
          type: string
          nullable: true
        staff_id:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        user_name:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        signalr_group_name:
          type: string
          nullable: true
        is_shopify_account:
          type: boolean
        associated_company_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    RegisterCompanyOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RegisterCompanyOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    RequiresMfaInput:
      required:
        - auth0_event_user
        - connection_name
        - connection_strategy
        - token
      type: object
      properties:
        token:
          minLength: 1
          type: string
        auth0_event_user:
          $ref: '#/components/schemas/ManagementUser'
        auth0_client_name:
          type: string
          nullable: true
        connection_strategy:
          minLength: 1
          type: string
        connection_name:
          minLength: 1
          type: string
      additionalProperties: false
    RequiresMfaOutput:
      type: object
      properties:
        is_mfa_required:
          type: boolean
        allow_remember_browser:
          type: boolean
      additionalProperties: false
    RequiresMfaOutputOutput:
      type: object
      properties:
        success:
          type: boolean
        data:
          $ref: '#/components/schemas/RequiresMfaOutput'
        message:
          type: string
          nullable: true
        date_time:
          type: string
          format: date-time
        http_status_code:
          type: integer
          format: int32
        error_code:
          type: integer
          format: int32
          nullable: true
        error_context:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        request_id:
          type: string
          nullable: true
      additionalProperties: false
    Role:
      type: object
      properties:
        name:
          type: string
          nullable: true
        Description:
          type: string
          nullable: true
        is_enabled:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    ShareableInvitationRecordResponse:
      type: object
      properties:
        invitedStaff:
          $ref: '#/components/schemas/StaffWithoutCompanyResponse'
        invitedAt:
          type: string
          format: date-time
      additionalProperties: false
    ShareableInvitationViewModel:
      type: object
      properties:
        role:
          type: string
          nullable: true
        rbac_role:
          type: string
          nullable: true
        team_ids:
          type: array
          items:
            type: integer
            format: int64
          nullable: true
        quota:
          type: integer
          format: int32
          nullable: true
        expiration_date:
          type: string
          format: date-time
      additionalProperties: false
    SleekflowStaff:
      type: object
      properties:
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_staff_team_ids:
          type: array
          items:
            type: string
          nullable: true
      additionalProperties: false
    StaffWithoutCompanyResponse:
      type: object
      properties:
        userInfo:
          $ref: '#/components/schemas/UserInfoResponse'
        staffId:
          type: integer
          format: int64
        role:
          type: string
          nullable: true
        roleType:
          type: string
          nullable: true
        name:
          type: string
          nullable: true
          readOnly: true
        locale:
          type: string
          nullable: true
        timeZoneInfoId:
          type: string
          nullable: true
        timeZoneInfo:
          $ref: '#/components/schemas/MyTimeZoneInfo'
        position:
          type: string
          nullable: true
        profilePictureURL:
          type: string
          nullable: true
          readOnly: true
        profilePicture:
          $ref: '#/components/schemas/ProfilePictureFile'
        status:
          type: string
          nullable: true
        isAcceptedInvitation:
          type: boolean
          nullable: true
          readOnly: true
      additionalProperties: false
    SubscriptionDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        plan_id:
          type: string
          nullable: true
        feature_id:
          type: string
          nullable: true
        subscription_type:
          type: string
          nullable: true
        currency:
          $ref: '#/components/schemas/Currency'
        billing_amount:
          $ref: '#/components/schemas/Price'
        captured_amount:
          $ref: '#/components/schemas/Price'
        payment_contexts:
          type: array
          items:
            $ref: '#/components/schemas/BasePaymentContext'
          nullable: true
        created_by:
          $ref: '#/components/schemas/SleekflowStaff'
        updated_by:
          $ref: '#/components/schemas/SleekflowStaff'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    User:
      type: object
      properties:
        username:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        user_workspaces:
          type: array
          items:
            $ref: '#/components/schemas/UserWorkspace'
          nullable: true
        profile_picture_url:
          type: string
          nullable: true
        is_agree_marketing_consent:
          type: boolean
          nullable: true
        record_statuses:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        id:
          type: string
          nullable: true
        sys_type_name:
          type: string
          nullable: true
        ttl:
          type: integer
          format: int32
          nullable: true
      additionalProperties: false
    UserDto:
      type: object
      properties:
        id:
          type: string
          nullable: true
        username:
          type: string
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        display_name:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        phone_number:
          type: string
          nullable: true
        user_workspaces:
          type: array
          items:
            $ref: '#/components/schemas/UserWorkspace'
          nullable: true
        profile_picture_url:
          type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      additionalProperties: false
    UserInfoResponse:
      type: object
      properties:
        id:
          type: string
          nullable: true
        firstName:
          type: string
          nullable: true
        lastName:
          type: string
          nullable: true
        displayName:
          type: string
          nullable: true
        userName:
          type: string
          nullable: true
        email:
          type: string
          nullable: true
        userRole:
          type: string
          nullable: true
        phoneNumber:
          type: string
          nullable: true
        emailConfirmed:
          type: boolean
        createdAt:
          type: string
          format: date-time
      additionalProperties: false
    UserWorkspace:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        sleekflow_user_id:
          type: string
          nullable: true
        sleekflow_staff_id:
          type: string
          nullable: true
        sleekflow_role_ids:
          type: array
          items:
            type: string
          nullable: true
        sleekflow_team_ids:
          type: array
          items:
            type: string
          nullable: true
        is_default:
          type: boolean
        additional_permissions:
          type: array
          items:
            type: string
          nullable: true
        metadata:
          type: object
          additionalProperties:
            nullable: true
          nullable: true
      additionalProperties: false
    UserWorkspaceDto:
      type: object
      properties:
        sleekflow_company_id:
          type: string
          nullable: true
        server_location:
          type: string
          nullable: true
        is_default:
          type: boolean
      additionalProperties: false