using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Scriban.Runtime;
using Sleekflow.Attributes;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Cores;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.States;
using Sleekflow.FlowHub.StepExecutors.Abstractions;
using Sleekflow.FlowHub.WorkflowExecutions;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.StepExecutors.Calls;

public interface IUpdateContactListRelationshipsStepExecutor : IStepExecutor
{
}

public class UpdateContactListRelationshipsStepExecutor
    : GeneralStepExecutor<CallStep<UpdateContactListRelationshipsStepArgs>>,
        IUpdateContactListRelationshipsStepExecutor,
        IScopedService
{
    private readonly IStateEvaluator _stateEvaluator;
    private readonly ICoreCommander _coreCommander;

    public UpdateContactListRelationshipsStepExecutor(
        IWorkflowStepLocator workflowStepLocator,
        IWorkflowRuntimeService workflowRuntimeService,
        IServiceProvider serviceProvider,
        IStateEvaluator stateEvaluator,
        ICoreCommander coreCommander)
        : base(workflowStepLocator, workflowRuntimeService, serviceProvider)
    {
        _stateEvaluator = stateEvaluator;
        _coreCommander = coreCommander;
    }

    public override async Task OnStepActivateAsync(
        ProxyWorkflow workflow,
        ProxyState state,
        Step step,
        Stack<StackEntry> stackEntries,
        Func<ProxyState, string, Task> onActivatedAsync)
    {
        var callStep = ToConcreteStep(step);

        try
        {
            await _coreCommander.ExecuteAsync(
                state.Origin,
                "UpdateContactListRelationships",
                await GetArgs(callStep, state));

            await onActivatedAsync(state, StepExecutionStatuses.Complete);
        }
        catch (Exception e)
        {
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                $"Failed to execute step {step.Id} of workflow {workflow.Id} in state {state.Id}",
                e);
        }
    }

    [SwaggerInclude]
    public class UpdateContactListRelationshipsInput
    {
        [JsonProperty("state_id")]
        [Required]
        public string StateId { get; set; }

        [JsonProperty("state_identity")]
        [Required]
        [Validations.ValidateObject]
        public StateIdentity StateIdentity { get; set; }

        [JsonProperty("contact_id")]
        [Required]
        public string ContactId { get; set; }

        [JsonProperty("add_list_ids")]
        [Validations.ValidateArray]
        public List<string>? AddListIds { get; set; }

        [JsonProperty("remove_list_ids")]
        [Validations.ValidateArray]
        public List<string>? RemoveListIds { get; set; }

        [JsonProperty("set_list_ids")]
        [Validations.ValidateArray]
        public List<string>? SetListIds { get; set; }

        [JsonConstructor]
        public UpdateContactListRelationshipsInput(
            string stateId,
            StateIdentity stateIdentity,
            string contactId,
            List<string>? addListIds,
            List<string>? removeListIds,
            List<string>? setListIds)
        {
            StateId = stateId;
            StateIdentity = stateIdentity;
            ContactId = contactId;
            AddListIds = addListIds;
            RemoveListIds = removeListIds;
            SetListIds = setListIds;
        }
    }

    private async Task<UpdateContactListRelationshipsInput> GetArgs(
        CallStep<UpdateContactListRelationshipsStepArgs> callStep,
        ProxyState state)
    {
        var contactId =
            (string) (await _stateEvaluator.EvaluateExpressionAsync(state, callStep.Args.ContactIdExpr)
                      ?? callStep.Args.ContactIdExpr);
        var addListIds =
            callStep.Args.AddListIdsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.AddListIdsExpr))!).Cast<string>().ToList();
        var removeListIds =
            callStep.Args.RemoveListIdsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.RemoveListIdsExpr))!).Cast<string>().ToList();
        var setListIds =
            callStep.Args.SetListIdsExpr == null
                ? null
                : ((ScriptArray) (await _stateEvaluator.EvaluateExpressionAsync(
                    state,
                    callStep.Args.SetListIdsExpr))!).Cast<string>().ToList();

        return new UpdateContactListRelationshipsInput(
            state.Id,
            state.Identity,
            contactId,
            addListIds,
            removeListIds,
            setListIds);
    }
}