﻿using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.FlowHub.Models.Internals;

[SwaggerInclude]
public class GetFacebookPageAccessTokenInput
{
    [JsonProperty("sleekflow_company_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("facebook_page_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string FacebookPageId { get; set; }

    [JsonConstructor]
    public GetFacebookPageAccessTokenInput(string sleekflowCompanyId, string facebookPageId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        FacebookPageId = facebookPageId;
    }
}