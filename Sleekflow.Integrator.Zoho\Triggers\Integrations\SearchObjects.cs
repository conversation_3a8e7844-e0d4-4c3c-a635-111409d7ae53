﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SearchObjects : ITrigger
{
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;

    public SearchObjects(
        IZohoObjectService zohoObjectService,
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoConnectionService zohoConnectionService)
    {
        _zohoObjectService = zohoObjectService;
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
    }

    public class SearchObjectsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("conditions")]
        [ValidateArray]
        [Required]
        public List<SearchObjectCondition> Conditions { get; set; }

        [JsonConstructor]
        public SearchObjectsInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<SearchObjectCondition> conditions)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            EntityTypeName = entityTypeName;
            Conditions = conditions;
        }
    }

    public class SearchObjectsOutput
    {
        [JsonProperty("objects")]
        [Required]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonConstructor]
        public SearchObjectsOutput(
            List<Dictionary<string, object?>> objects)
        {
            Objects = objects;
        }
    }

    public async Task<SearchObjectsOutput> F(
        SearchObjectsInput searchObjectsInput)
    {
        var connection = await _zohoConnectionService.GetByIdAsync(
            searchObjectsInput.ConnectionId,
            searchObjectsInput.SleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                searchObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var objects = await _zohoObjectService.SearchObjectsAsync(
            authentication,
            searchObjectsInput.EntityTypeName,
            searchObjectsInput.Conditions);

        return new SearchObjectsOutput(objects);
    }
}