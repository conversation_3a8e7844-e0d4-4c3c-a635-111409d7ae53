using Sleekflow.CommerceHub.Models.Categories;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Stores;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Categories;

public interface ICategoryService
{
    Task<Category> CreateOrGetCategoryAsync(
        string sleekflowCompanyId,
        string storeId,
        Multilingual name,
        PlatformData platformData,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<Category> CreateAndGetCategoryAsync(
        Category category,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<(List<Category> Categories, string? NextContinuationToken)> GetCategoriesAsync(
        string? continuationToken,
        string sleekflowCompanyId,
        string storeId,
        int limit);

    Task<List<Category>> GetCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        int limit,
        List<string> categoryIds);

    Task<Category> PatchAndGetCategoryAsync(
        string id,
        string sleekflowCompanyId,
        List<Multilingual> names,
        List<Description> descriptions,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task DeleteCategoryAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff);
}

public class CategoryService : ICategoryService, IScopedService
{
    private readonly IStoreService _storeService;
    private readonly ICategoryRepository _categoryRepository;
    private readonly ICategoryValidator _categoryValidator;
    private readonly IIdService _idService;
    private readonly IDynamicFiltersRepositoryContext _dynamicFiltersRepositoryContext;
    private readonly ICategorySearchService _categorySearchService;

    public CategoryService(
        IStoreService storeService,
        ICategoryRepository categoryRepository,
        ICategoryValidator categoryValidator,
        IIdService idService,
        IDynamicFiltersRepositoryContext dynamicFiltersRepositoryContext,
        ICategorySearchService categorySearchService)
    {
        _storeService = storeService;
        _categoryRepository = categoryRepository;
        _categoryValidator = categoryValidator;
        _idService = idService;
        _dynamicFiltersRepositoryContext = dynamicFiltersRepositoryContext;
        _categorySearchService = categorySearchService;
    }

    public async Task<Category> CreateOrGetCategoryAsync(
        string sleekflowCompanyId,
        string storeId,
        Multilingual name,
        PlatformData platformData,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var categories = await _categoryRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId
                && c.StoreId == storeId
                && c.Names.Any(n => n.Value == name.Value && n.LanguageIsoCode == name.LanguageIsoCode)
                && c.PlatformData.Type == platformData.Type);
        if (categories.Any())
        {
            return categories.First();
        }

        await _categoryValidator.AssertValidCategoryPropertiesAsync(
            sleekflowCompanyId,
            storeId,
            new List<Multilingual>
            {
                name
            },
            new List<Description>());

        var createdCategory = await _categoryRepository.CreateAndGetAsync(
            new Category(
                _idService.GetId("Category"),
                sleekflowCompanyId,
                new List<Multilingual>
                {
                    name
                },
                new List<Description>(),
                storeId,
                platformData,
                new List<string>
                {
                    "Active"
                },
                new Dictionary<string, object?>(),
                sleekflowStaff,
                sleekflowStaff,
                DateTimeOffset.UtcNow,
                DateTimeOffset.UtcNow),
            sleekflowCompanyId);

        return createdCategory;
    }

    public async Task<Category> CreateAndGetCategoryAsync(
        Category category,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        await _categoryValidator.AssertValidCategoryPropertiesAsync(
            category.SleekflowCompanyId,
            category.StoreId,
            category.Names,
            category.Descriptions);

        var createdCategory = await _categoryRepository.CreateAndGetAsync(category, category.SleekflowCompanyId);

        return createdCategory;
    }

    public async Task<(
        List<Category> Categories,
        string? NextContinuationToken)> GetCategoriesAsync(
        string? continuationToken,
        string sleekflowCompanyId,
        string storeId,
        int limit)
    {
        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);

        return await _categoryRepository.GetCategoriesAsync(
            sleekflowCompanyId,
            store.Id,
            continuationToken,
            limit);
    }

    public async Task<List<Category>> GetCategoriesAsync(
        string sleekflowCompanyId,
        string storeId,
        int limit,
        List<string> categoryIds)
    {
        var store = await _storeService.GetStoreAsync(storeId, sleekflowCompanyId);

        return await _categoryRepository.GetCategoriesAsync(
            sleekflowCompanyId,
            store.Id,
            categoryIds,
            limit);
    }

    public async Task<Category> PatchAndGetCategoryAsync(
        string id,
        string sleekflowCompanyId,
        List<Multilingual> names,
        List<Description> descriptions,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var category = await _categoryRepository.GetCategoryOrDefaultAsync(
            id,
            sleekflowCompanyId,
            storeId);
        if (category is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        await _categoryValidator.AssertValidCategoryPropertiesAsync(
            sleekflowCompanyId,
            storeId,
            names,
            descriptions);

        var patchedCategory = await _categoryRepository.PatchAndGetCategoryAsync(
            category,
            names,
            descriptions,
            sleekflowStaff);

        return patchedCategory;
    }

    public async Task DeleteCategoryAsync(
        string id,
        string sleekflowCompanyId,
        string storeId,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var category = await _categoryRepository.GetAsync(
            id,
            sleekflowCompanyId);
        if (category is null || category.StoreId != storeId)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        // TODO Make sure empty category
        // TODO UpdatedBy

        var deleteAsync = await _categoryRepository.DeleteAsync(
            id,
            sleekflowCompanyId);
        if (deleteAsync == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to delete the category with id {id}, sleekflowCompanyId {sleekflowCompanyId}");
        }

        // When IsSoftDeleteEnabled,
        // Sleekflow.CommerceHub.Processors.CommerceHubDbProcessorService.ExecuteAsync can pick up the changes
        // When IsSoftDeleteEnabled is false,
        // We need to delete the indexed item manually
        if (!_dynamicFiltersRepositoryContext.IsSoftDeleteEnabled)
        {
            await _categorySearchService.DeleteCategoriesAsync(
                new List<Category>
                {
                    category
                });
        }
    }
}