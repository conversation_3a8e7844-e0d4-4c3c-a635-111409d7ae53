using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Triggers.TransactionLogs;

[TriggerGroup(ControllerNames.TransactionLogs)]
public class EnqueueBusinessBalancePendingTransactionLogCreated
    : ITrigger<
        EnqueueBusinessBalancePendingTransactionLogCreated.EnqueueBusinessBalancePendingTransactionLogCreatedInput,
        EnqueueBusinessBalancePendingTransactionLogCreated.EnqueueBusinessBalancePendingTransactionLogCreatedOutput>
{
    private readonly IBus _bus;

    public EnqueueBusinessBalancePendingTransactionLogCreated(IBus bus)
    {
        _bus = bus;
    }

    public class EnqueueBusinessBalancePendingTransactionLogCreatedInput
    {
        [JsonProperty("facebook_business_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string FacebookBusinessId { get; set; }

        [JsonConstructor]
        public EnqueueBusinessBalancePendingTransactionLogCreatedInput(string facebookBusinessId)
        {
            FacebookBusinessId = facebookBusinessId;
        }
    }

    public class EnqueueBusinessBalancePendingTransactionLogCreatedOutput
    {
    }

    public async Task<EnqueueBusinessBalancePendingTransactionLogCreatedOutput> F(
        EnqueueBusinessBalancePendingTransactionLogCreatedInput enqueueBusinessBalancePendingTransactionLogCreatedInput)
    {
        var onCloudApiBusinessBalancePendingTransactionLogCreatedEvent =
            new OnCloudApiBusinessBalancePendingTransactionLogCreatedEvent(
                enqueueBusinessBalancePendingTransactionLogCreatedInput.FacebookBusinessId);
        await _bus.Publish(onCloudApiBusinessBalancePendingTransactionLogCreatedEvent);

        return new EnqueueBusinessBalancePendingTransactionLogCreatedOutput();
    }
}