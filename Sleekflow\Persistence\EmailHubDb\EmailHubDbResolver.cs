using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Sleekflow.JsonConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Persistence.EmailHubDb;

public interface IEmailHubDbResolver : IContainerResolver
{
}

public class EmailHubDbResolver : IEmailHubDbResolver
{
    private readonly CosmosClient _cosmosClient;

    public EmailHubDbResolver(IEmailHubDbConfig emailHubDbConfig)
    {
        _cosmosClient = new CosmosClient(
            emailHubDbConfig.Endpoint,
            emailHubDbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 0,
                RequestTimeout = TimeSpan.FromSeconds(30),
                AllowBulkExecution = false,
            });
    }

    public Container Resolve(string databaseId, string containerId)
    {
        var database = _cosmosClient.GetDatabase(databaseId);
        return database.GetContainer(containerId);
    }
}