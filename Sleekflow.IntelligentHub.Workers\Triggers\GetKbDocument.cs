using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Documents.WebsiteDocuments;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.Workers.Triggers;

public class GetKbDocument : ITrigger
{
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;
    private readonly IWebsiteDocumentChunkService _websiteDocumentChunkService;

    public GetKbDocument(
        IKbDocumentService kbDocumentService,
        IFileDocumentChunkService fileDocumentChunkService,
        IWebsiteDocumentChunkService websiteDocumentChunkService)
    {
        _kbDocumentService = kbDocumentService;
        _fileDocumentChunkService = fileDocumentChunkService;
        _websiteDocumentChunkService = websiteDocumentChunkService;
    }

    public class GetKbDocumentInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("document_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string DocumentId { get; set; }

        [JsonConstructor]
        public GetKbDocumentInput(
            string sleekflowCompanyId,
            string documentId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            DocumentId = documentId;
        }
    }

    public class GetKbDocumentOutput
    {
        [JsonProperty("kb_document")]
        public object KbDocument { get; set; }

        [JsonProperty("chunk_ids")]
        public List<string> ChunkIds { get; set; }

        public GetKbDocumentOutput(object kbDocument, List<string> chunkIds)
        {
            KbDocument = kbDocument;
            ChunkIds = chunkIds;
        }
    }

    [Function(nameof(GetKbDocument))]
    public async Task<GetKbDocumentOutput> Run(
        [ActivityTrigger]
        GetKbDocumentInput input)
    {
        var kbDocument = await _kbDocumentService.GetDocumentAsync(
            input.SleekflowCompanyId,
            input.DocumentId);

        List<string> chunkIds;
        if (kbDocument is FileDocument fileDocument)
        {
            chunkIds = await _fileDocumentChunkService.GetFileDocumentChunkIdsAsync(
                input.SleekflowCompanyId,
                input.DocumentId);
        }
        else if (kbDocument is WebsiteDocument websiteDocument)
        {
            chunkIds = await _websiteDocumentChunkService.GetWebsiteDocumentChunkIdsAsync(
                input.SleekflowCompanyId,
                input.DocumentId);
        }
        else
        {
            chunkIds = new List<string>();
        }

        return new GetKbDocumentOutput(kbDocument, chunkIds);
    }
}