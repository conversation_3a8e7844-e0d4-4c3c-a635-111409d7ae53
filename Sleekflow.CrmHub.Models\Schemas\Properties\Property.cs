﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Schemas.Properties;

public sealed class Property : IProperty, IHasCreatedAt
{
    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; }

    [JsonProperty(IProperty.PropertyNameDisplayName)]
    public string DisplayName { get; set; }

    [JsonProperty(IProperty.PropertyNameUniqueName)]
    public string UniqueName { get; set; }

    [JsonProperty(IProperty.PropertyNameDataType)]
    public IDataType DataType { get; }

    [JsonProperty(IProperty.PropertyNameIsRequired)]
    public bool IsRequired { get; set; }

    [JsonProperty(IProperty.PropertyNameIsVisible)]
    public bool IsVisible { get; set; }

    [JsonProperty(IProperty.PropertyNameIsPinned)]
    public bool IsPinned { get; set; }

    [JsonProperty(IProperty.PropertyNameIsSearchable)]
    public bool IsSearchable { get; set; }

    [JsonProperty(IProperty.PropertyNameDisplayOrder)]
    public int DisplayOrder { get; set; }

    [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IProperty.PropertyNameOptions)]
    public List<Option>? Options { get; set; }

    [JsonConstructor]
    public Property(
        string id,
        string displayName,
        string uniqueName,
        IDataType dataType,
        bool isRequired,
        bool isVisible,
        bool isPinned,
        bool isSearchable,
        int displayOrder,
        AuditEntity.SleekflowStaff? createdBy,
        DateTimeOffset createdAt,
        List<Option>? options)
    {
        Id = id;
        DisplayName = displayName;
        UniqueName = uniqueName;
        DataType = dataType;
        IsRequired = isRequired;
        IsVisible = isVisible;
        IsPinned = isPinned;
        IsSearchable = isSearchable;
        DisplayOrder = displayOrder;
        CreatedBy = createdBy;
        CreatedAt = createdAt;
        Options = options;
    }
}