﻿using Newtonsoft.Json;

namespace Sleekflow.DistributedInvocations;

public class FlowBuilderWorkflowContext
{
    [JsonProperty("workflow_versioned_id")]
    public string? WorkflowVersionedId { get; set; }

    [JsonProperty("step_id")]
    public string? StepId { get; set; }

    [JsonConstructor]
    public FlowBuilderWorkflowContext(
        string? workflowVersionedId,
        string? stepId)
    {
        WorkflowVersionedId = workflowVersionedId;
        StepId = stepId;
    }
}