﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Zoho.Authentications;
using Sleekflow.Integrator.Zoho.Connections;
using Sleekflow.Integrator.Zoho.Services;

namespace Sleekflow.Integrator.Zoho.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class CreateObjectV2 : ITrigger
{
    private readonly IZohoObjectService _zohoObjectService;
    private readonly IZohoAuthenticationService _zohoAuthenticationService;
    private readonly IZohoConnectionService _zohoConnectionService;

    public CreateObjectV2(
        IZohoObjectService zohoObjectService,
        IZohoAuthenticationService zohoAuthenticationService,
        IZohoConnectionService zohoConnectionService)
    {
        _zohoObjectService = zohoObjectService;
        _zohoAuthenticationService = zohoAuthenticationService;
        _zohoConnectionService = zohoConnectionService;
    }

    public class CreateObjectV2Input
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("dict")]
        [Required]
        public Dictionary<string, object?> Dict { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public CreateObjectV2Input(
            string sleekflowCompanyId,
            string connectionId,
            Dictionary<string, object?> dict,
            string entityTypeName)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            Dict = dict;
            EntityTypeName = entityTypeName;
        }
    }

    public class CreateObjectV2Output
    {
        [JsonProperty("is_async_operation")]
        public bool IsAsyncOperation { get; set; }

        [JsonConstructor]
        public CreateObjectV2Output(bool isAsyncOperation)
        {
            IsAsyncOperation = isAsyncOperation;
        }
    }

    public async Task<CreateObjectV2Output> F(CreateObjectV2Input createObjectV2Input)
    {
        var connection = await _zohoConnectionService.GetByIdAsync(
            createObjectV2Input.ConnectionId,
            createObjectV2Input.SleekflowCompanyId);

        var authentication =
            await _zohoAuthenticationService.GetAsync(
                connection.AuthenticationId,
                createObjectV2Input.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var getFieldsOutput =
            await _zohoObjectService.GetFieldsAsync(authentication, createObjectV2Input.EntityTypeName);
        var updatableFieldNames = getFieldsOutput.UpdatableFields.Select(f => f.Name).ToList();
        var updatableBooleanFieldNames = getFieldsOutput.UpdatableFields
            .Where(f => f.Type == "boolean")
            .Select(f => f.Name)
            .ToList();
        var updatableDateFieldNames = getFieldsOutput.UpdatableFields
            .Where(f => f.Type == "date")
            .Select(f => f.Name)
            .ToList();
        var updatableDateTimeFieldNames = getFieldsOutput.UpdatableFields
            .Where(f => f.Type == "datetime")
            .Select(f => f.Name)
            .ToList();

        var dict = createObjectV2Input.Dict
            .Where(e => updatableFieldNames.Contains(e.Key))
            .ToDictionary(e => e.Key, e =>
            {
                if (updatableBooleanFieldNames.Contains(e.Key) && e.Value is string value)
                {
                    return value.ToLower();
                }

                if (updatableDateFieldNames.Contains(e.Key))
                {
                    if (e.Value is DateTimeOffset dateValue)
                    {
                        return dateValue.ToString("yyyy-MM-dd");
                    }

                    if (e.Value is string dateString && DateTimeOffset.TryParse(dateString, out var parsedDateValue))
                    {
                        return parsedDateValue.ToString("yyyy-MM-dd");
                    }
                }

                if (updatableDateTimeFieldNames.Contains(e.Key))
                {
                    if (e.Value is DateTimeOffset dateTimeValue)
                    {
                        return dateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                    }

                    if (e.Value is string dateTimeString && DateTimeOffset.TryParse(dateTimeString, out var parsedDateTimeValue))
                    {
                        return parsedDateTimeValue.ToString("yyyy-MM-ddTHH:mm:ss.fffK");
                    }
                }

                return e.Value;
            });

        await _zohoObjectService.CreateAsync(
            authentication,
            dict,
            createObjectV2Input.EntityTypeName);

        return new CreateObjectV2Output(false);
    }
}