using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

public class CreateInvoiceOrRefundInput
{
    [JsonConstructor]
    public CreateInvoiceOrRefundInput(
        string feeType,
        decimal amount,
        string? invoiceExternalId,
        string companyId,
        string? subscriptionStartDate,
        string? subscriptionEndDate,
        string? subscriptionDescription)
    {
        FeeType = feeType;
        Amount = amount;
        ExternalId = invoiceExternalId;
        CompanyId = companyId;
        SubscriptionStartDate = subscriptionStartDate;
        SubscriptionEndDate = subscriptionEndDate;
        SubscriptionDescription = subscriptionDescription;
    }

    [Required]
    [JsonProperty("fee_type")]
    public string FeeType { get; set; }

    [Required]
    [JsonProperty("amount")]
    [Range(0, double.MaxValue)]
    public decimal Amount { get; set; }

    [JsonProperty("external_id")]
    public string? ExternalId { get; set; }

    [Required]
    [JsonProperty("company_id")]
    public string CompanyId { get; set; }

    [JsonProperty("subscription_start_date")]
    public string? SubscriptionStartDate { get; set; }

    [JsonProperty("subscription_end_date")]
    public string? SubscriptionEndDate { get; set; }

    [JsonProperty("subscription_description")]
    public string? SubscriptionDescription { get; set; }
}