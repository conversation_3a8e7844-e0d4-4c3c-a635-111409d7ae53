﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.NeedConfigs;
using Sleekflow.FlowHub.NeedConfigs;

namespace Sleekflow.FlowHub.Triggers.NeedConfigs;

[TriggerGroup(ControllerNames.NeedConfigs)]
public class GetActions : ITrigger<
    GetActions.GetActionsInput,
    GetActions.GetActionsOutput>
{
    private readonly INeedConfigService _needConfigService;

    public GetActions(
        INeedConfigService needConfigService)
    {
        _needConfigService = needConfigService;
    }

    public class GetActionsInput
    {
        [JsonProperty("version")]
        public string? Version { get; set; }

        [JsonConstructor]
        public GetActionsInput(
            string? version)
        {
            Version = version;
        }
    }

    public class GetActionsOutput
    {
        [JsonProperty("actions")]
        public List<ActionConfigDto> Actions { get; set; }

        [JsonConstructor]
        public GetActionsOutput(List<ActionConfigDto> actions)
        {
            Actions = actions;
        }
    }

    public async Task<GetActionsOutput> F(GetActionsInput input)
    {
        var needConfigs = await _needConfigService.GetActionsAsync(
            input.Version);

        return new GetActionsOutput(
            needConfigs
                .Select(x => new ActionConfigDto(x))
                .ToList());
    }
}