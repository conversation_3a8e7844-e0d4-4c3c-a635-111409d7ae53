﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Events.InternalActionEvents;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Triggers;

namespace Sleekflow.FlowHub.Integrator.FlowHubs;

public interface IInternalWorkflowService
{
    Task<WorkflowDto> CreateAndEnableInternalWorkflowAsync(
        string sleekflowCompanyId,
        string workflowType,
        string workflowTriggers,
        string workflowSteps,
        string? workflowMetadata,
        string name);

    Task<WorkflowDto> CreateAndEnableInternalWorkflowAsync(
        string sleekflowCompanyId,
        string workflowType,
        WorkflowTriggers workflowTriggers,
        List<Step> steps,
        Dictionary<string, object?> metadata,
        string name);

    Task DeleteInternalWorkflowAsync(string sleekflowCompanyId, string workflowId);
}

public class InternalWorkflowService : IInternalWorkflowService, IScopedService
{
    private readonly IRequestClient<CreateInternalWorkflowRequest> _createInternalWorkflowRequestClient;
    private readonly IRequestClient<EnableInternalWorkflowRequest> _enableInternalWorkflowRequestClient;
    private readonly IRequestClient<DeleteInternalWorkflowRequest> _deleteInternalWorkflowRequestClient;

    public InternalWorkflowService(
        IRequestClient<CreateInternalWorkflowRequest> createInternalWorkflowRequestClient,
        IRequestClient<EnableInternalWorkflowRequest> enableInternalWorkflowRequestClient,
        IRequestClient<DeleteInternalWorkflowRequest> deleteInternalWorkflowRequestClient)
    {
        _createInternalWorkflowRequestClient = createInternalWorkflowRequestClient;
        _enableInternalWorkflowRequestClient = enableInternalWorkflowRequestClient;
        _deleteInternalWorkflowRequestClient = deleteInternalWorkflowRequestClient;
    }

    public async Task<WorkflowDto> CreateAndEnableInternalWorkflowAsync(
        string sleekflowCompanyId,
        string workflowType,
        string workflowTriggers,
        string workflowSteps,
        string? workflowMetadata,
        string name)
    {
        var triggers = JsonConvert.DeserializeObject<WorkflowTriggers>(workflowTriggers)!;

        var stepJsonSettings = new JsonSerializerSettings
        {
            Converters = { new StepConverter() }
        };
        var steps = JsonConvert.DeserializeObject<List<Step>>(workflowSteps, stepJsonSettings)!;

        var metadata = string.IsNullOrWhiteSpace(workflowMetadata)
            ? new Dictionary<string, object?>()
            : JsonConvert.DeserializeObject<Dictionary<string, object?>>(workflowMetadata)!;

        return await CreateAndEnableInternalWorkflowAsync(
            sleekflowCompanyId,
            workflowType,
            triggers,
            steps,
            metadata,
            name);
    }

    public async Task<WorkflowDto> CreateAndEnableInternalWorkflowAsync(
        string sleekflowCompanyId,
        string workflowType,
        WorkflowTriggers workflowTriggers,
        List<Step> steps,
        Dictionary<string, object?> metadata,
        string name)
    {
        var createInterWorkflowResponse =
            await _createInternalWorkflowRequestClient.GetResponse<CreateInternalWorkflowReply>(
                new CreateInternalWorkflowRequest(
                    sleekflowCompanyId,
                    workflowType,
                    workflowTriggers,
                    steps,
                    name,
                    metadata));

        var workflowVersionedId = createInterWorkflowResponse.Message.Workflow.WorkflowVersionedId;

        var enableInternalWorkflowResponse =
            await _enableInternalWorkflowRequestClient.GetResponse<EnableInternalWorkflowReply>(
                new EnableInternalWorkflowRequest(
                    sleekflowCompanyId,
                    workflowVersionedId));

        return enableInternalWorkflowResponse.Message.Workflow;
    }

    public async Task DeleteInternalWorkflowAsync(string sleekflowCompanyId, string workflowId)
    {
        await _deleteInternalWorkflowRequestClient.GetResponse<DeleteInternalWorkflowReply>(
            new DeleteInternalWorkflowRequest(
                sleekflowCompanyId,
                workflowId));
    }
}