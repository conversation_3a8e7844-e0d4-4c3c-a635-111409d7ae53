﻿using Azure.Core.Serialization;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Mvc.Versioning;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Serilog;
using Serilog.Enrichers.Span;
using Serilog.Events;
using Serilog.Sinks.GoogleCloudLogging;
using Sleekflow.Configs;
using Sleekflow.DistributedInvocations;
using Sleekflow.JsonConfigs;
using Sleekflow.Loggings;
using Sleekflow.Mvc.Authorizations;
using Sleekflow.Mvc.Func;
using Sleekflow.Mvc.Func.Abstractions;
using Sleekflow.Mvc.HealthChecks;
using Sleekflow.Mvc.Https.Builders;
using Sleekflow.Mvc.Requests;
using Sleekflow.Mvc.Telemetries;
using Sleekflow.Mvc.Validators;

#if SWAGGERGEN
using Moq;
using Microsoft.Extensions.Options;
using Microsoft.OpenApi.Models;
using Sleekflow.Mvc.Configs;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;
using Sleekflow.Mvc.SwaggerConfiguration.Parameter;
using Sleekflow.Mvc.SwaggerConfiguration.Schemas;
using Sleekflow.Mvc.SwaggerConfiguration.OperationFilters;
#endif

namespace Sleekflow.Mvc;

public static class MvcModules
{
    public static void BuildTelemetryServices(
        IServiceCollection b,
        IWebHostEnvironment environment,
        string appName)
    {
#if SWAGGERGEN
        b.AddSingleton<ITelemetryInitializer>(new Mock<ITelemetryInitializer>().Object);
        b.AddSingleton<IApplicationInsightsTelemetryTracer>(new Mock<IApplicationInsightsTelemetryTracer>().Object);
#else
        var myTelemetryInitializer = new MyTelemetryInitializer(appName);

        b.AddSingleton<ITelemetryInitializer>(myTelemetryInitializer);

        if (environment.IsProduction())
        {
            var applicationInsightTelemetryConfig = new ApplicationInsightsTelemetryConfig();
            b.AddApplicationInsightsTelemetry(
                options =>
                {
                    if (applicationInsightTelemetryConfig.IsSamplingDisabled == "TRUE")
                    {
                        options.EnableAdaptiveSampling = false;
                    }
                });
        }

        b.AddSingleton<IApplicationInsightsTelemetryTracer, ApplicationInsightsTelemetryTracer>();
#endif
    }

    public static void BuildFuncServices(IServiceCollection b, string appName)
    {
        BuildHttpRequestBuilder(b);
#if SWAGGERGEN
        b.AddSingleton<IRequestRepository>(new Mock<IRequestRepository>().Object);
        b.AddScoped<IFuncService>(_ => new Mock<IFuncService>().Object);
        b.AddScoped<IHeadersAuthorizationFuncFilter>(_ => new Mock<IHeadersAuthorizationFuncFilter>().Object);
        b.AddScoped<ISleekflowAuthorizationContext>(_ => new Mock<SleekflowAuthorizationContext>().Object);
        b.AddSingleton<IRequestConfig>(new Mock<IRequestConfig>().Object);
        b.AddSingleton<IRequestService>(new Mock<IRequestService>().Object);
        b.AddScoped<IDistributedInvocationContextService>(_ => new Mock<DistributedInvocationContextService>().Object);
#else
        b.AddSingleton<IRequestRepository, RequestRepository>();
        b.AddScoped<IFuncService, FuncService>();

        // Dependency injection for the authorization filter to FuncService.
        b.AddScoped<IHeadersAuthorizationFuncFilter, HeadersAuthorizationFuncFilter>();
        b.AddScoped<ISleekflowAuthorizationContext, SleekflowAuthorizationContext>();
        b.AddSingleton<IRequestConfig>(new RequestConfig(appName));
        b.AddSingleton<IRequestService, RequestService>();
        b.AddScoped<IDistributedInvocationContextService, DistributedInvocationContextService>();
#endif
    }

    public static void BuildApiBehaviors(WebApplicationBuilder b)
    {
        // FuncService is the replacement to validate the inputs
        b.Services.AddSingleton<IObjectModelValidator, NullObjectModelValidator>();

        // https://github.com/advisories/GHSA-5crp-9r3c-p9vr
        b.Services
            .AddControllers()
            .AddNewtonsoftJson(
                options =>
                {
                    JsonConfig.EnhanceWithDefaultJsonSerializerSettings(options.SerializerSettings);
                });
        JsonConvert.DefaultSettings = () => JsonConfig.DefaultJsonSerializerSettings;

#if DEBUG
        b.Services.AddCors(
            options => options.AddPolicy(
                "DEBUG",
                policyBuilder =>
                {
                    policyBuilder
                        .SetIsOriginAllowed(_ => true)
                        .AllowCredentials()
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                }));
#endif

        b.Services.AddApiVersioning(
            o =>
            {
                o.AssumeDefaultVersionWhenUnspecified = true;
                o.DefaultApiVersion = new ApiVersion(1, 0);
                o.ReportApiVersions = false;
                o.ApiVersionReader = new HeaderApiVersionReader("X-API-VERSION");
            });
        b.Services.AddVersionedApiExplorer(options => options.GroupNameFormat = "'v'VVV");
    }

#if SWAGGERGEN
    public static void BuildApiBehaviors(
        WebApplicationBuilder b,
        Action<List<OpenApiServer>> action,
        params Assembly[]? additionalAssemblies)
    {
        BuildApiBehaviors(b);

        b.Services.AddTransient<IConfigureOptions<SwaggerGenOptions>, ConfigureSwaggerOptions>();
        b.Services.AddSwaggerGen(
            options =>
            {
                action.Invoke(options.SwaggerGeneratorOptions.Servers);

                options.EnableAnnotations(enableAnnotationsForInheritance: true, enableAnnotationsForPolymorphism: true);

                options.OperationFilter<SwaggerQueryFilter>();
                options.MapType<decimal>(
                    () => new OpenApiSchema
                    {
                        Type = "number", Format = "decimal"
                    });

                if (additionalAssemblies != null)
                {
                    options.DocumentFilter<SwaggerIncludeDocumentFilter>(
                        string.Join(";", additionalAssemblies.Select(a => a.FullName)));
                }
                options.OperationFilter<DistributedInvocationContextHeaderOperationFilter>();
            });
        b.Services.AddSwaggerGenNewtonsoftSupport();
    }

#endif

    public static void BuildLogger(string name, LogEventLevel? defaultLogEventLevel = null)
    {
        var loggerConfig = new LoggerConfig();
        var loggerConfiguration = new LoggerConfiguration()
            .MinimumLevel.Is(defaultLogEventLevel ?? LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft", defaultLogEventLevel ?? LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.AspNetCore", defaultLogEventLevel ?? LogEventLevel.Warning)
            .MinimumLevel.Override("Azure.Messaging.ServiceBus", defaultLogEventLevel ?? LogEventLevel.Warning)
            .MinimumLevel.Override("System.Net.Http.HttpClient", defaultLogEventLevel ?? LogEventLevel.Warning)
#if DEBUG
            .MinimumLevel.Override("Sleekflow.Persistence", defaultLogEventLevel ?? LogEventLevel.Debug)
            .MinimumLevel.Override("Sleekflow.CrmHub.Persistence", defaultLogEventLevel ?? LogEventLevel.Debug)
            .MinimumLevel.Override(
                "Sleekflow.IntelligentHub.Plugins.KnowledgePlugin",
                defaultLogEventLevel ?? LogEventLevel.Debug)
#endif
            .Enrich.WithSpan()
            .Enrich.FromLogContext()
            .Enrich.WithMachineName();

        // Check if running in NUnit test context
        var isRunningInNUnit = AppDomain.CurrentDomain.GetAssemblies()
            .Any(
                a => a.FullName is not null && a.FullName.StartsWith(
                    "nunit.framework",
                    StringComparison.OrdinalIgnoreCase));

        if (isRunningInNUnit)
        {
            // Create logs directory if it doesn't exist
            var logsDirectory = Path.Combine(
                AppDomain.CurrentDomain.BaseDirectory.Contains("bin")
                    ? AppDomain.CurrentDomain.BaseDirectory.Substring(
                        0,
                        AppDomain.CurrentDomain.BaseDirectory.IndexOf("bin", StringComparison.Ordinal))
                    : AppDomain.CurrentDomain.BaseDirectory,
                "nunit-logs");
            Directory.CreateDirectory(logsDirectory);

            // Generate unique log file name based on timestamp and test name
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");

            loggerConfiguration = loggerConfiguration
                .WriteTo.Map(
                    keyPropertyName: "TestId",
                    defaultKey: "default",
                    configure: (testIdValue, wt) => wt.File(
                        path: Path.Combine(logsDirectory, $"{name}_{timestamp}_{testIdValue}.log"),
                        outputTemplate:
                        "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3} {MachineName}][{TestId}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}",
                        fileSizeLimitBytes: 10 * 1024 * 1024))
                .WriteTo.Async(
                    a => a.Console(
                        outputTemplate:
                        "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
        }

        if (loggerConfig.IsLogAnalyticsEnabled == "TRUE" || loggerConfig.IsGoogleCloudLoggingEnabled == "TRUE")
        {
            if (loggerConfig.IsLogAnalyticsEnabled == "TRUE")
            {
                loggerConfiguration = loggerConfiguration
                    .WriteTo.AzureAnalytics(
                        loggerConfig.WorkspaceId,
                        loggerConfig.AuthenticationId,
                        name,
                        flattenObject: false);
            }

            if (loggerConfig.IsGoogleCloudLoggingEnabled == "TRUE")
            {
                loggerConfiguration = loggerConfiguration
                    .WriteTo.GoogleCloudLogging(
                        new GoogleCloudLoggingSinkOptions(
                            projectId: loggerConfig.GoogleCloudProjectId,
                            googleCredentialJson: loggerConfig.GoogleCloudCredentialJson,
                            serviceName: name,
                            logName: name,
                            useSourceContextAsLogName: false,
                            labels: new Dictionary<string, string>()
                            {
                                {
                                    "PROJECT", "SLEEKFLOW"
                                }
                            }));
            }
        }
        else
        {
            loggerConfiguration = loggerConfiguration
                .WriteTo.Async(
                    wt => wt.Console(
                        outputTemplate:
                        "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
        }

        Log.Logger = loggerConfiguration.CreateLogger();
    }

    public class CustomTelemetryProcessor(ITelemetryProcessor next) : ITelemetryProcessor
    {
        private ITelemetryProcessor Next { get; set; } = next;

        public void Process(ITelemetry item)
        {
            if (item is TraceTelemetry trace)
            {
                // Filter out function execution traces
                if (trace.Message.Contains("Executing 'Functions.") ||
                    trace.Message.Contains("Executed 'Functions."))
                {
                    return;
                }
            }
            else if (item is RequestTelemetry request)
            {
                if (request.Name == "Hubspot_PropagateBatchToProvider")
                {
                    return; // Drop this telemetry
                }
            }
            else if (item is DependencyTelemetry dependency)
            {
                if (dependency.Context.Operation.Name == "Hubspot_PropagateBatchToProvider")
                {
                    return; // Drop this telemetry
                }
            }

            Next.Process(item);
        }
    }

    public static void BuildIsolatedAzureFunction(
        string name,
        HostBuilder hostBuilder,
        Action<IServiceCollection> additionalServiceConfig)
    {
        var loggerConfig = new LoggerConfig();
        var loggerConfiguration = new LoggerConfiguration()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .MinimumLevel.Override("Microsoft.AspNetCore", LogEventLevel.Warning)
            .MinimumLevel.Override("Azure.Messaging.ServiceBus", LogEventLevel.Warning)
            .MinimumLevel.Override("Azure.Core", LogEventLevel.Warning)
            .MinimumLevel.Override("DurableTask.AzureStorage", LogEventLevel.Warning)
            .MinimumLevel.Override("Host.Aggregator", LogEventLevel.Warning)
            .MinimumLevel.Override("Host.Results", LogEventLevel.Warning)
            .Enrich.WithSpan()
            .Enrich.FromLogContext()
            .Enrich.WithMachineName();
        if (loggerConfig.IsLogAnalyticsEnabled == "TRUE" || loggerConfig.IsGoogleCloudLoggingEnabled == "TRUE")
        {
            if (loggerConfig.IsLogAnalyticsEnabled == "TRUE")
            {
                loggerConfiguration = loggerConfiguration
                    .WriteTo.AzureAnalytics(
                        loggerConfig.WorkspaceId,
                        loggerConfig.AuthenticationId,
                        name,
                        flattenObject: false);
            }

            if (loggerConfig.IsGoogleCloudLoggingEnabled == "TRUE")
            {
                loggerConfiguration = loggerConfiguration
                    .WriteTo.GoogleCloudLogging(
                        new GoogleCloudLoggingSinkOptions(
                            projectId: loggerConfig.GoogleCloudProjectId,
                            googleCredentialJson: loggerConfig.GoogleCloudCredentialJson,
                            serviceName: name,
                            logName: name,
                            useSourceContextAsLogName: false,
                            labels: new Dictionary<string, string>()
                            {
                                {
                                    "PROJECT", "SLEEKFLOW"
                                }
                            }));
            }
        }
        else
        {
            loggerConfiguration = loggerConfiguration
                .WriteTo.Async(
                    wt => wt.Console(
                        outputTemplate:
                        "[{Timestamp:HH:mm:ss} {Level:u3} {MachineName}][{SfRequestId}][{SourceContext}] {Message:lj}{NewLine}{Exception}"));
        }

        var logger = loggerConfiguration.CreateLogger();

        hostBuilder
            .ConfigureFunctionsWebApplication(
                builder =>
                {
                    builder.Services.Configure<WorkerOptions>(
                        workerOptions =>
                        {
                            workerOptions.Serializer =
                                new NewtonsoftJsonObjectSerializer(JsonConfig.DefaultJsonSerializerSettings);
                        });
                })
            .ConfigureAppConfiguration(
                builder =>
                {
                    builder.AddAzureAppConfiguration(Environment.GetEnvironmentVariable("APP_CONFIGURATION_CONN_STR"));
                })
            .ConfigureServices(
                services =>
                {
                    services.Configure<TelemetryConfiguration>(
                        telemetryConfiguration =>
                        {
                            var telemetryProcessorChainBuilder = telemetryConfiguration
                                .DefaultTelemetrySink
                                .TelemetryProcessorChainBuilder;

                            telemetryProcessorChainBuilder.Use(x => new CustomTelemetryProcessor(x));

                            // Using adaptive sampling
                            telemetryProcessorChainBuilder.UseAdaptiveSampling(maxTelemetryItemsPerSecond: 1);

                            telemetryProcessorChainBuilder.Build();
                        });

                    services.AddApplicationInsightsTelemetryWorkerService();
                    services.ConfigureFunctionsApplicationInsights();
                    services
                        .AddHttpClient("default-handler")
                        .ConfigurePrimaryHttpMessageHandler(
                            () => new HttpClientHandler
                            {
                                AllowAutoRedirect = false,
                            });
                    services.AddMvc().AddNewtonsoftJson();

                    additionalServiceConfig.Invoke(services);
                })
            .ConfigureLogging(
                cl =>
                {
                    cl.ClearProviders();
                    cl.AddSerilog(logger);
                });

        Log.Logger = logger;
        JsonConvert.DefaultSettings = () => JsonConfig.DefaultJsonSerializerSettings;
    }

    public static void BuildHealthCheck(IServiceCollection b)
    {
        b.AddHostedService<StartupHostedService>();
        b.AddSingleton<StartupHealthCheck>();
        b.AddHealthChecks().AddCheck<StartupHealthCheck>(
            "Startup",
            tags: new[]
            {
                "ready"
            });
    }

    private static void BuildHttpRequestBuilder(IServiceCollection b)
    {
        b.AddScoped<IHttpRequestBuilder, HttpRequestBuilder>();
    }
}