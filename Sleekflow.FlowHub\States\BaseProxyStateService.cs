using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.States;

public interface IBaseProxyStateService<T>
    where T : BaseProxyState
{
    Task<T> GetProxyStateAsync(string stateId);
}

public class BaseProxyStateService : IBaseProxyStateService<BaseProxyState>, IScopedService
{
    private readonly IStateRepository _stateRepository;
    private readonly IWorkflowService _workflowService;

    public BaseProxyStateService(IStateRepository stateRepository, IWorkflowService workflowService)
    {
        _stateRepository = stateRepository;
        _workflowService = workflowService;
    }

    protected async Task<ProxyWorkflow> GetProxyWorkflowAsync(State state)
    {
        var proxyWorkflow = await _workflowService.GetVersionedWorkflowOrDefaultAsync(
            state.Identity.SleekflowCompanyId,
            state.Identity.WorkflowVersionedId);

        proxyWorkflow ??= new ProxyWorkflow(
            state.WorkflowContext.SnapshottedWorkflow,
            state.WorkflowContext.SnapshottedWorkflow.Steps ?? new (),
            state.WorkflowContext.SnapshottedWorkflow.Metadata ?? new ());

        return proxyWorkflow;
    }

    public async Task<BaseProxyState> GetProxyStateAsync(string stateId)
    {
        var state = await _stateRepository.GetAsync(stateId, stateId);

        var proxyWorkflow = await GetProxyWorkflowAsync(state);

        return new BaseProxyState(
            state.Id,
            state.Identity,
            new ProxyStateWorkflowContext(proxyWorkflow),
            state.CreatedAt,
            state.UpdatedAt);
    }
}