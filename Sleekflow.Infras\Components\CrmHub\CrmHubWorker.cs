using Pulumi;
using Pulumi.AzureNative.Resources;
using Sleekflow.Infras.Components.Configs;
using Sleekflow.Infras.Constants;
using Sleekflow.Infras.Utils;
using AppConfiguration = Pulumi.AzureNative.AppConfiguration;
using Insights = Pulumi.AzureNative.Insights;
using OperationalInsights = Pulumi.AzureNative.OperationalInsights;
using Random = Pulumi.Random;
using Storage = Pulumi.AzureNative.Storage;
using Web = Pulumi.AzureNative.Web;

namespace Sleekflow.Infras.Components.CrmHub;

public class CrmHubWorker
{
    private readonly ResourceGroup _resourceGroup;
    private readonly CrmHubDb.CrmHubDbOutput _crmHubDbOutput;
    private readonly MyConfig _myConfig;
    private readonly AppConfiguration.ConfigurationStore _appConfig;
    private readonly Db.DbOutput _dbOutput;
    private readonly CrmHubIntegrationDb.CrmHubIntegrationDbOutput _crmHubIntegrationDbOutput;
    private readonly List<ManagedEnvAndAppsTuple> _managedEnvAndAppsTuples;
    private readonly GcpConfig _gcpConfig;

    public CrmHubWorker(
        ResourceGroup resourceGroup,
        CrmHubDb.CrmHubDbOutput crmHubDbOutput,
        MyConfig myConfig,
        AppConfiguration.ConfigurationStore appConfig,
        Db.DbOutput dbOutput,
        CrmHubIntegrationDb.CrmHubIntegrationDbOutput crmHubIntegrationDbOutput,
        List<ManagedEnvAndAppsTuple> managedEnvAndAppsTuples,
        GcpConfig gcpConfig)
    {
        _resourceGroup = resourceGroup;
        _crmHubDbOutput = crmHubDbOutput;
        _myConfig = myConfig;
        _appConfig = appConfig;
        _dbOutput = dbOutput;
        _crmHubIntegrationDbOutput = crmHubIntegrationDbOutput;
        _managedEnvAndAppsTuples = managedEnvAndAppsTuples;
        _gcpConfig = gcpConfig;
    }

    public Dictionary<string, Web.WebApp> InitWorker()
    {
        var apps = new Dictionary<string, Web.WebApp>();
        foreach (var managedEnvAndAppsTuple in _managedEnvAndAppsTuples)
        {
            // If any service is included in the managed env, deploy the worker.
            var services = new string[]
            {
                ServiceNames.SalesforceIntegrator,
                ServiceNames.HubspotIntegrator,
                ServiceNames.Dynamics365Integrator,
                ServiceNames.GoogleSheetsIntegrator,
                ServiceNames.ZohoIntegrator,
                ServiceNames.CrmHub,
            };
            if (managedEnvAndAppsTuple.AreAllExcludedFromManagedEnv(services))
            {
                continue;
            }

            var serviceBus = managedEnvAndAppsTuple.ServiceBus;
            var eventHub = managedEnvAndAppsTuple.EventHub;
            var logAnalyticsWorkspace = managedEnvAndAppsTuple.LogAnalyticsWorkspace;
            var massTransitBlobStorage = managedEnvAndAppsTuple.MassTransitBlobStorage;

            var workspaceSharedKeys = Output
                .Tuple(_resourceGroup.Name, logAnalyticsWorkspace.Name)
                .Apply(
                    items => OperationalInsights.GetSharedKeys.InvokeAsync(
                        new OperationalInsights.GetSharedKeysArgs
                        {
                            ResourceGroupName = items.Item1, WorkspaceName = items.Item2,
                        }));

            // Primary,Secondary,Primary Read Only,Secondary Read Only
            var primaryAppConfigReadOnlyConnStr = Output
                .Tuple(_resourceGroup.Name, _appConfig.Name)
                .Apply(
                    items => AppConfiguration.ListConfigurationStoreKeys.Invoke(
                        new AppConfiguration.ListConfigurationStoreKeysInvokeArgs
                        {
                            ResourceGroupName = items.Item1, ConfigStoreName = items.Item2
                        }))
                .Apply(o => o.Value.First(v => v.Name == "Primary Read Only").ConnectionString);

            var randomId = new Random.RandomId(
                "sleekflow-ch-worker-storage-account-random-id",
                new Random.RandomIdArgs
                {
                    ByteLength = 4,
                    Keepers =
                    {
                        {
                            "hello", "world"
                        }
                    },
                });

            var storageAccount = new Storage.StorageAccount(
                "sleekflow-ch-worker-storage-account",
                new Storage.StorageAccountArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Sku = new Storage.Inputs.SkuArgs
                    {
                        Name = Storage.SkuName.Standard_LRS,
                    },
                    Tags = new InputMap<string>
                    {
                        {
                            "Environment", _myConfig.Name
                        },
                        {
                            "StorageAccountName", $"sleekflow-Crm-Worker-storage-{_myConfig.Name}"
                        }
                    },
                    Kind = Storage.Kind.StorageV2,
                    AccountName = randomId.Hex.Apply(h => "s" + h)
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var container = new Storage.BlobContainer(
                "sleekflow-ch-worker-zips-container",
                new Storage.BlobContainerArgs
                {
                    AccountName = storageAccount.Name,
                    PublicAccess = Storage.PublicAccess.None,
                    ResourceGroupName = _resourceGroup.Name,
                    ContainerName = "zips-container"
                },
                new CustomResourceOptions
                {
                    Parent = storageAccount
                });

            var path = "../Sleekflow.CrmHub.Workers/bin/Release/net8.0/publish";
            var ymdhms = new DirectoryInfo(path)
                .EnumerateFiles("*", SearchOption.AllDirectories)
                .Max(fi => fi.CreationTimeUtc)
                .ToString("yyyyMMddHHmmss");

            var blob = new Storage.Blob(
                "ch_worker_zip_" + ymdhms,
                new Storage.BlobArgs
                {
                    AccountName = storageAccount.Name,
                    ContainerName = container.Name,
                    ResourceGroupName = _resourceGroup.Name,
                    Type = Storage.BlobType.Block,
                    Source = new FileArchive(path)
                },
                new CustomResourceOptions
                {
                    Parent = container
                });

            var codeBlobUrl = StorageUtils.SignedBlobReadUrl(blob, container, storageAccount, _resourceGroup);

            var appInsights = new Insights.Component(
                "sleekflow-ch-worker-app-insight",
                new Insights.ComponentArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    ApplicationType = Insights.ApplicationType.Web,
                    FlowType = "Redfield",
                    RequestSource = "IbizaWebAppExtensionCreate",
                    Kind = "Web",
                    WorkspaceResourceId = logAnalyticsWorkspace.Id
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            var appServicePlan = new Web.AppServicePlan(
                "sleekflow-ch-worker-app-service-plan",
                new Web.AppServicePlanArgs
                {
                    ResourceGroupName = _resourceGroup.Name,
                    Kind = string.Empty,
                    Sku = new Web.Inputs.SkuDescriptionArgs
                    {
                        Tier = "Dynamic", Name = "Y1"
                    }
                },
                new CustomResourceOptions
                {
                    Parent = _resourceGroup
                });

            const string functionAppName = "sleekflow-ch-worker-app";
            var functionApp = new Web.WebApp(
                functionAppName,
                new Web.WebAppArgs
                {
                    Kind = "FunctionApp",
                    ResourceGroupName = _resourceGroup.Name,
                    ServerFarmId = appServicePlan.Id,
                    SiteConfig = new Web.Inputs.SiteConfigArgs
                    {
                        AppSettings = EnvironmentVariablesUtils.GetDeduplicateNameValuePairs(
                            new[]
                            {
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_EXTENSION_VERSION", Value = "~4",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_WORKER_RUNTIME", Value = "dotnet-isolated",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "FUNCTIONS_INPROC_NET8_ENABLED", Value = "1",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPINSIGHTS_INSTRUMENTATIONKEY", Value = appInsights.InstrumentationKey
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APPLICATIONINSIGHTS_CONNECTION_STRING",
                                    Value = appInsights.ConnectionString,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "AzureWebJobsStorage",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "Storage",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING",
                                    Value = StorageUtils.GetConnectionString(_resourceGroup.Name, storageAccount.Name),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_CONTENTSHARE", Value = functionAppName + "96ed",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "WEBSITE_RUN_FROM_PACKAGE", Value = codeBlobUrl,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "APP_CONFIGURATION_CONN_STR", Value = primaryAppConfigReadOnlyConnStr,
                                },

                                #region AppConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "INTERNALS_KEY", Value = "a12da7c775d00cada5b1ee611d3f6dca",
                                },

                                #endregion

                                #region DbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_dbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_KEY", Value = _dbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_DATABASE_ID", Value = _dbOutput.DatabaseId,
                                },

                                #endregion

                                #region CrmHubDbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_CRM_HUB_DB_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_crmHubDbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_CRM_HUB_DB_KEY", Value = _crmHubDbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_CRM_HUB_DB_DATABASE_ID", Value = _crmHubDbOutput.DatabaseId,
                                },

                                #endregion

                                #region CrmHubIntegrationDbConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_CRM_HUB_INTEGRATION_DB_ENDPOINT",
                                    Value = Output.Format(
                                        $"https://{_crmHubIntegrationDbOutput.AccountName}.documents.azure.com:443/"),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_CRM_HUB_INTEGRATION_DB_KEY",
                                    Value = _crmHubIntegrationDbOutput.AccountKey,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "COSMOS_CRM_HUB_INTEGRATION_DB_DATABASE_ID",
                                    Value = _crmHubIntegrationDbOutput.DatabaseId,
                                },

                                #endregion

                                #region ServiceBusConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "SERVICE_BUS_CONN_STR", Value = serviceBus.CrmHubPolicyKeyPrimaryConnStr,
                                },

                                #endregion

                                #region EventHubConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "EVENT_HUB_CONN_STR", Value = eventHub.NamespacePrimaryConnStr,
                                },

                                #endregion

                                #region Loggings

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_LOG_ANALYTICS_ENABLED", Value = "FALSE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_WORKSPACE_ID", Value = logAnalyticsWorkspace.CustomerId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_AUTHENTICATION_ID",
                                    Value = workspaceSharedKeys.Apply(r => r.PrimarySharedKey!),
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", Value = "TRUE",
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_PROJECT_ID", Value = _gcpConfig.ProjectId,
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", Value = _gcpConfig.CredentialJson,
                                },

                                #endregion

                                #region MassTransitStorageConfig

                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONN_STR", Value = massTransitBlobStorage.StorageAccountConnStr
                                },
                                new Web.Inputs.NameValuePairArgs
                                {
                                    Name = "MESSAGE_DATA_CONTAINER_NAME", Value = massTransitBlobStorage.ContainerName
                                },

                                #endregion
                            }),
                        Use32BitWorkerProcess = true,
                        NetFrameworkVersion = "v8.0",
                    },
                },
                new CustomResourceOptions
                {
                    Parent = appServicePlan
                });

            managedEnvAndAppsTuple.WorkerApps.Add(ServiceNames.CrmHub, functionApp);
            apps.Add(ServiceNames.GetWorkerName(ServiceNames.CrmHub), functionApp);
        }

        return apps;
    }
}