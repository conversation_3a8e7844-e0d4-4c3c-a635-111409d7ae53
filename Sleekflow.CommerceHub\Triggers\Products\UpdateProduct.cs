﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Images;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.CommerceHub.Products;
using Sleekflow.CommerceHub.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Products;

[TriggerGroup(ControllerNames.Products)]
public class UpdateProduct
    : ITrigger<
        UpdateProduct.UpdateProductInput,
        UpdateProduct.UpdateProductOutput>
{
    private readonly IProductService _productService;
    private readonly IImageService _imageService;
    private readonly IProductVariantService _productVariantService;

    public UpdateProduct(
        IProductService productService,
        IImageService imageService,
        IProductVariantService productVariantService)
    {
        _productService = productService;
        _imageService = imageService;
        _productVariantService = productVariantService;
    }

    public class UpdateProductInput : ProductInput, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CommonFieldNames.PropertyNameStoreId)]
        public string StoreId { get; set; }

        [Required]
        [JsonProperty(Product.PropertyNameIsViewEnabled)]
        public bool IsViewEnabled { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateProductInput(
            List<string> categoryIds,
            string? sku,
            string? url,
            List<Multilingual> names,
            List<Description> descriptions,
            List<ImageDto> images,
            Dictionary<string, object?> metadata,
            string id,
            string sleekflowCompanyId,
            string storeId,
            bool isViewEnabled,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
            : base(categoryIds, sku, url, names, descriptions, images, metadata)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            StoreId = storeId;
            IsViewEnabled = isViewEnabled;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateProductOutput
    {
        [JsonProperty("product")]
        public ProductDto Product { get; set; }

        [JsonConstructor]
        public UpdateProductOutput(ProductDto product)
        {
            Product = product;
        }
    }

    public async Task<UpdateProductOutput> F(UpdateProductInput updateProductInput)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateProductInput.SleekflowStaffId,
            updateProductInput.SleekflowStaffTeamIds);

        var images = await _imageService.GetImagesAsync(
            updateProductInput.Images,
            updateProductInput.SleekflowCompanyId,
            updateProductInput.StoreId);

        var product = await _productService.PatchAndGetProductAsync(
            updateProductInput.Id,
            updateProductInput.SleekflowCompanyId,
            updateProductInput.StoreId,
            updateProductInput.CategoryIds,
            updateProductInput.Sku,
            updateProductInput.Url,
            updateProductInput.Names,
            updateProductInput.Descriptions,
            images,
            updateProductInput.IsViewEnabled,
            updateProductInput.Metadata,
            sleekflowStaff);

        var productVariants = await _productVariantService.GetProductVariantsAsync(
            updateProductInput.SleekflowCompanyId,
            updateProductInput.StoreId,
            updateProductInput.Id);

        return new UpdateProductOutput(new ProductDto(product, productVariants));
    }
}