using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.DependencyInjection;
using Sleekflow.EmailHub.Configs;
using Sleekflow.EmailHub.Models.Authentications;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Outlook.Authentications;
using Sleekflow.EmailHub.Repositories;
using Sleekflow.EmailHub.Services;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using IHttpClientFactory = System.Net.Http.IHttpClientFactory;

namespace Sleekflow.EmailHub.Outlook.Authentications;

public interface IOutlookAuthenticationService : IEmailAuthenticationService, IEmailOAuthService
{
}

public class OutlookAuthenticationService : IScopedService, IOutlookAuthenticationService
{
    private readonly IOutlookConfig _outlookConfig;
    private readonly IIdService _idService;
    private readonly ILogger<OutlookAuthenticationService> _logger;
    private readonly HttpClient _httpClient;
    private readonly IEmailAuthenticationRepository _emailAuthenticationRepository;

    public OutlookAuthenticationService(
        IOutlookConfig outlookConfig,
        IIdService idService,
        ILogger<OutlookAuthenticationService> logger,
        IHttpClientFactory httpClientFactory,
        IEmailAuthenticationRepository emailAuthenticationRepository)
    {
        _outlookConfig = outlookConfig;
        _idService = idService;
        _logger = logger;
        _emailAuthenticationRepository = emailAuthenticationRepository;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public async Task<EmailAuthentication> GetAuthenticationAsync(
        string sleekflowCompanyId,
        string emailAddress,
        string? serverType = null,
        CancellationToken cancellationToken = default)
    {
        var authentication = (await _emailAuthenticationRepository.GetObjectsAsync(
                x=>
                x.SleekflowCompanyId == sleekflowCompanyId &&
                x.EmailAddress == emailAddress &&
                x.ProviderName == ProviderNames.Outlook,
                cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        var outlookAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new NullReferenceException($"Cannot parse outlookAuthenticationMetadata: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}");

        if (outlookAuthenticationMetadata.ExpiresIn != null &&
            DateTimeOffset.UtcNow - TimeSpan.FromSeconds(30) - outlookAuthenticationMetadata.IssuedAt <
            TimeSpan.FromSeconds((long) outlookAuthenticationMetadata.ExpiresIn))
        {
            return authentication;
        }

        return await ReAuthenticateAndStoreAsync(sleekflowCompanyId, emailAddress, cancellationToken);
    }

    public async Task<EmailAuthentication> ReAuthenticateAndStoreAsync(
        string sleekflowCompanyId,
        string emailAddress,
        CancellationToken cancellationToken = default)
    {
        var authentication = (await _emailAuthenticationRepository.GetObjectsAsync(
            x=>
                x.SleekflowCompanyId == sleekflowCompanyId &&
                x.EmailAddress == emailAddress &&
                x.ProviderName == ProviderNames.Outlook,
            cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        if (authentication.IsAuthenticated == false)

        {
            return authentication;
        }

        _logger.LogInformation(
            "Started Outlook ReAuthenticateAndStoreAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        var outlookAuthenticationMetadata =
            authentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new NullReferenceException("Cannot parse outlookAuthenticationMetaData");
        var tenant = _outlookConfig.Tenant;
        var clientId = _outlookConfig.ClientId;
        var clientSecret = _outlookConfig.ClientSecret;
        var refreshToken = outlookAuthenticationMetadata.RefreshToken ?? throw new SfUnauthorizedException();
        const string scope = "openid offline_access https://graph.microsoft.com/.default";

        var getTokenRequestBody = new FormUrlEncodedContent(
            new Dictionary<string, string>
            {
                ["client_id"] = clientId,
                ["scope"] = scope,
                ["refresh_token"] = refreshToken,
                ["grant_type"] = "refresh_token",
                ["client_secret"] = clientSecret
            });
        getTokenRequestBody.Headers.Clear();
        getTokenRequestBody.Headers.Add("Content-Type", "application/x-www-form-urlencoded");

        var getTokenResponse = await _httpClient.PostAsync(
            $"https://login.microsoftonline.com/common/oauth2/v2.0/token",
            getTokenRequestBody,
            cancellationToken);
        var getTokenResponseStr = await getTokenResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!getTokenResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(getTokenResponseStr);
            var errorMsg = errorObject["error"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} Outlook ReAuthenticateAndStoreAsync fails: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
                getTokenResponse.StatusCode,
                errorMsg,
                emailAddress,
                sleekflowCompanyId);

            throw new SfInternalErrorException($"HTTP[{getTokenResponse.StatusCode}] {errorMsg}");
        }

        var token = JsonConvert.DeserializeObject<OutlookTokenResponse>(getTokenResponseStr);

        _logger.LogInformation(
            "Completed Outlook ReAuthenticateAndStoreAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);

        outlookAuthenticationMetadata.AccessToken = token!.AccessToken;
        outlookAuthenticationMetadata.RefreshToken = token.RefreshToken;
        outlookAuthenticationMetadata.IssuedAt = DateTimeOffset.UtcNow - TimeSpan.FromSeconds(30);
        outlookAuthenticationMetadata.ExpiresIn = token.ExpiresIn;
        outlookAuthenticationMetadata.TokenType = token.TokenType;
        await _emailAuthenticationRepository.UpsertAsync(authentication, authentication.Id, cancellationToken: cancellationToken);

        return authentication;
    }

    public async Task<string> AuthenticateAsync(
        string sleekflowCompanyId,
        string emailAddress,
        Dictionary<string, string>? extendedAuthMetadata,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation(
            "Started AuthenticationAsync: emailAddress {emailAddress} of sleekflowCompanyId {sleekflowCompanyId}",
            emailAddress,
            sleekflowCompanyId);
        var nonce = Guid.NewGuid().ToString();
        var tenant = _outlookConfig.Tenant;
        var clientId = _outlookConfig.ClientId;
        var redirectUri = _outlookConfig.RedirectUri;
        const string scope = "openid offline_access https://graph.microsoft.com/.default";
        const string responseType = "code";
        const string accessType = "offline";
        const string responseMode = "query";

        var queryParams = new Dictionary<string, string>
        {
            {
                "client_id", clientId
            },
            {
                "response_type", responseType
            },
            {
                "redirect_uri", redirectUri
            },
            {
                "response_mode", responseMode
            },
            {
                "scope", scope
            },
            {
                "access_type", accessType
            },
            {
                "state", nonce
            }
        };

        var outlookRedirectToOutlookAuthServerUrl = $"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?" +
                                                    $"{string.Join(
                                                        "&",
                                                        queryParams
                                                            .Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}"))}";

        string authenticationId;

        try
        {
            authenticationId = (await GetAuthenticationAsync(sleekflowCompanyId, emailAddress, cancellationToken: cancellationToken)).Id;
        }
        catch (SfUnauthorizedException)
        {
            authenticationId = _idService.GetId("EmailAuthentication");
        }

        await _emailAuthenticationRepository.UpsertAsync(
            new EmailAuthentication(
                id: authenticationId,
                sleekflowCompanyId: sleekflowCompanyId,
                emailAddress: emailAddress,
                isAuthenticated: false,
                new OutlookAuthenticationMetadata(
                    nonce: nonce),
                ProviderNames.Outlook),
            authenticationId,
            cancellationToken: cancellationToken);

        return outlookRedirectToOutlookAuthServerUrl;
    }

    public async Task HandleAuthCallbackAndStoreAsync(
        string code,
        string nonce,
        CancellationToken cancellationToken = default)
    {
        var pendingAuthentication = (await _emailAuthenticationRepository.GetObjectsAsync(
                x =>
                    x.ProviderName == ProviderNames.Outlook &&
                    ((OutlookAuthenticationMetadata) x.EmailAuthenticationMetadata).Nonce == nonce,
                cancellationToken: cancellationToken))
            .FirstOrDefault();

        if (pendingAuthentication == null)
        {
            return;
        }

        var tenant = _outlookConfig.Tenant;
        var clientId = _outlookConfig.ClientId;
        const string scope = "openid offline_access https://graph.microsoft.com/.default";
        var redirectUri = _outlookConfig.RedirectUri;
        const string grantType = "authorization_code";
        var clientSecret = _outlookConfig.ClientSecret;

        var getTokenRequestBody = new FormUrlEncodedContent(
            new Dictionary<string, string>
            {
                ["client_id"] = clientId,
                ["scope"] = scope,
                ["code"] = code,
                ["redirect_uri"] = redirectUri,
                ["grant_type"] = grantType,
                ["client_secret"] = clientSecret,
            });
        getTokenRequestBody.Headers.Clear();
        getTokenRequestBody.Headers.Add("Content-Type", "application/x-www-form-urlencoded");

        var getTokenResponse = await _httpClient.PostAsync(
            $"https://login.microsoftonline.com/common/oauth2/v2.0/token",
            getTokenRequestBody,
            cancellationToken);
        var getTokenResponseStr = await getTokenResponse.Content.ReadAsStringAsync(cancellationToken);

        if (!getTokenResponse.IsSuccessStatusCode)
        {
            var errorObject = JObject.Parse(getTokenResponseStr);
            var errorMsg = errorObject["error"]?["message"]?.ToString();

            _logger.LogError(
                "HTTP[{statusCode}] {errorMsg} HandleAuthCallbackAndStoreAsync fails: cannot exchange for token outlook",
                getTokenResponse.StatusCode,
                errorMsg);

            throw new SfInternalErrorException($"HTTP[{getTokenResponse.StatusCode}] {errorMsg}");
        }

        var token = JsonConvert.DeserializeObject<OutlookTokenResponse>(getTokenResponseStr);

        var outlookAuthenticationMetadata =
            pendingAuthentication.EmailAuthenticationMetadata as OutlookAuthenticationMetadata ??
            throw new SfInternalErrorException("Cannot parse outlookAuthenticationMetadata");
        outlookAuthenticationMetadata.AccessToken = token!.AccessToken;
        outlookAuthenticationMetadata.TokenType = token.TokenType;
        outlookAuthenticationMetadata.RefreshToken = token.RefreshToken;
        outlookAuthenticationMetadata.ExpiresIn = token.ExpiresIn;
        outlookAuthenticationMetadata.IssuedAt = DateTimeOffset.UtcNow - TimeSpan.FromSeconds(30);
        pendingAuthentication.IsAuthenticated = true;
        await _emailAuthenticationRepository.UpsertAsync(pendingAuthentication, pendingAuthentication.Id, cancellationToken: cancellationToken);

        _logger.LogInformation(
            "HandleAuthCallbackAndStoreAsync successes for nonce: {nonce}",
            nonce);
    }
}