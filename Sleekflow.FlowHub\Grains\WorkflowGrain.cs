﻿namespace Sleekflow.FlowHub.Grains;

public interface IWorkflowGrain : IPersistentDictionaryGrain
{
}

public class WorkflowGrain : PersistentDictionaryGrainBase, IWorkflowGrain
{
    // Constructor injects the specific state and passes it to the base
    public WorkflowGrain(
        [PersistentState("workflow", "workflowStore")] // State name and storage provider name
        IPersistentState<Dictionary<string, object?>> state)
        : base(state) // Pass state to the base class constructor
    {
    }

    // Implement or override methods specific to IWorkflowGrain here if needed
}