using MassTransit.AzureCosmos.Saga;
using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sharprompt;
using Sleekflow.DataMigrator.Configs;
using Sleekflow.DataMigrator.Constants;
using Sleekflow.DataMigrator.Executors.Abstractions;
using Sleekflow.DataMigrator.Utils;
using Sleekflow.TenantHub.Models.Constants;
using Sleekflow.TenantHub.Models.Roles;
using ContainerNames = Sleekflow.DataMigrator.Constants.ContainerNames;
using TenantHubUser = Sleekflow.TenantHub.Models.Users.User;

namespace Sleekflow.DataMigrator.Executors;

internal class PatchMissingRoleIds : IExecutor
{
    private readonly HttpClient _httpClient;
    private readonly CosmosClient _cosmosClient;
    private readonly List<TravisBackendClientConfig?> _travisBackendClientConfigs;

    private bool _isPreviewData;
    private TravisBackendHttpClient? _travisBackendClient;
    private Database _database;

    public PatchMissingRoleIds(
        DbConfig dbConfig,
        List<TravisBackendClientConfig?> travisBackendClientConfigs,
        HttpClient httpClient)
    {
        _cosmosClient = new CosmosClient(
            dbConfig.Endpoint,
            dbConfig.Key,
            new CosmosClientOptions
            {
                ConnectionMode = ConnectionMode.Direct,
                Serializer = new NewtonsoftJsonCosmosSerializer(JsonConfig.DefaultJsonSerializerSettings),
                MaxRetryAttemptsOnRateLimitedRequests = 9
            });
        _httpClient = httpClient;
        _travisBackendClientConfigs = travisBackendClientConfigs;
    }

    public string GetDisplayName()
    {
        return "Patch Missing Role Ids";
    }

    public async Task PrepareAsync()
    {
        var databaseIds = new List<string>();
        await foreach (var databaseProperties in CosmosUtils.GetDatabasesAsync(_cosmosClient))
        {
            databaseIds.Add(databaseProperties.Id);
        }

        if (!databaseIds.Exists(d => DatabaseIds.TenantHub.Equals(d)))
        {
            throw new Exception("TenantHub database not found");
        }

        if (_travisBackendClientConfigs.Count == 0)
        {
            throw new Exception("No Travis Backend Server Configurations found");
        }

        var selectedLocation =
            Prompt.Select(
                "Select the Travis Backend Server",
                _travisBackendClientConfigs.Select(s => s?.Location)) ??
            throw new Exception("No Travis Backend Server Configurations found");

        _travisBackendClient = new TravisBackendHttpClient(
            _travisBackendClientConfigs.Find(c => c?.Location == selectedLocation),
            _httpClient,
            HubNames.TenantHub);

        var isPreviewData = Prompt.Confirm("Preview the update data ?");

        _isPreviewData = isPreviewData;

        _database = _cosmosClient.GetDatabase(DatabaseIds.TenantHub);
    }

    public async Task ExecuteAsync()
    {
        // Check the container id is existed
        var containerExists =
            await CosmosUtils.ContainerExistsAsync(_database, ContainerNames.User)
            && await CosmosUtils.ContainerExistsAsync(_database, ContainerNames.Role);

        if (!containerExists)
        {
            throw new Exception("Container not found");
        }

        var count = await MigrateObjectsAsync(ContainerNames.User, ContainerNames.Role);
        Console.WriteLine($"Completed {ContainerNames.User} for {count} objects");
    }

    private async Task<int> MigrateObjectsAsync(string userContainerId, string roleContainerId)
    {
        var retryPolicy = CosmosUtils.GetDefaultRetryPolicy(userContainerId);

        var userContainer = _database.GetContainer(userContainerId);
        var roleContainer = _database.GetContainer(roleContainerId);

        const string userSqlQuery =
            "SELECT VALUE c FROM c JOIN uw IN c.user_workspaces where ARRAY_LENGTH(uw.sleekflow_role_ids) = 0"; // + " ORDER BY c.id OFFSET 0 LIMIT 25";

        var updated = 0;
        var totalFoundUsers = 0;

        // Fetch all roles
        var roleQueryIterator =
            roleContainer.GetItemQueryIterator<dynamic>(new QueryDefinition("SELECT * FROM c"));
        var roles = new List<Role>();

        while (roleQueryIterator.HasMoreResults)
        {
            var currentResultSet = await roleQueryIterator.ReadNextAsync();
            roles.AddRange(
                currentResultSet
                    .Select(item => JsonConvert.DeserializeObject<Role>(item.ToString()))
                    .Select(row => (Role) row));
        }

        await Parallel.ForEachAsync(
            CosmosUtils.GetObjectsAsync(
                userContainer,
                userSqlQuery),
            new ParallelOptions()
            {
                MaxDegreeOfParallelism = 10
            },
            async (dict, token) =>
            {
                await retryPolicy.ExecuteAndCaptureAsync(
                    async () =>
                    {
                        totalFoundUsers++;

                        var raw = JsonConvert.SerializeObject(dict);
                        var tenantHubUser = JsonConvert.DeserializeObject<TenantHubUser>(raw);
                        var workspaces = tenantHubUser?.UserWorkspaces;

                        try
                        {
                            if (workspaces != null && workspaces.Any())
                            {
                                foreach (var workspace in workspaces)
                                {
                                    // Create a snapshot of the data before updating
                                    var now = DateTime.UtcNow;
                                    var snapshotKey = $"snapshot_DEVS-8081_{now.Year}_{now.Month}_{now.Day}";

                                    var clonedSnapShotData = dict;
                                    clonedSnapShotData.Remove("metadata");

                                    workspace.Metadata ??= new Dictionary<string, object?>();
                                    workspace.Metadata.Add(snapshotKey, dict);

                                    var sleekflowUserId = workspace.SleekflowUserId;
                                    var res = await _travisBackendClient.SendRequestAsync(
                                        TravisBackendPaths.GetUser,
                                        new
                                        {
                                            sleekflow_user_id = $"{sleekflowUserId}"
                                        });

                                    if (res is null)
                                    {
                                        // Skip the update if the user is not found
                                        throw new Exception(
                                            $"User {sleekflowUserId} not found in Sleekflow Core, skipping update");
                                    }

                                    dynamic resObj = JsonConvert.DeserializeObject(res) ?? string.Empty;
                                    var userRoleType = (string) resObj.role_type!;
                                    var roleId = roles.Where(r => r.Name == userRoleType).Select(r => r.Id)
                                        .FirstOrDefault();

                                    workspace.SleekflowRoleIds = new List<string>
                                    {
                                        roleId!
                                    };
                                }
                            }

                            switch (_isPreviewData)
                            {
                                case false:
                                    await userContainer.ReplaceItemAsync(
                                        tenantHubUser,
                                        tenantHubUser!.Id,
                                        new PartitionKey(tenantHubUser.Id),
                                        new ItemRequestOptions()
                                        {
                                            IfMatchEtag = (string) dict["_etag"]!
                                        },
                                        token);
                                    Console.WriteLine($"User {dict["id"]!} Updated");
                                    break;
                                case true:
                                    Console.Write(
                                        $"{JsonConvert.SerializeObject(tenantHubUser, Formatting.Indented)}\n\n");
                                    break;
                            }

                            updated++;
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine($"\n{e.Message}");
                        }
                    });
            });

        Console.WriteLine($"\nTotal found users: {totalFoundUsers}");
        return updated;
    }
}