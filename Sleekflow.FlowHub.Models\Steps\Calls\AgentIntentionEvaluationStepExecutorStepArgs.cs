﻿using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class AgentEvaluateIntentionStepExecutorStepArgs : TypedCallStepArgs, IHasCompanyAgentConfigIdExpr
{
    public const string CallName = "sleekflow.v1.agent-evaluate-intention";

    [JsonProperty("contact_id__expr")]
    public string ContactIdExpr { get; set; }

    [JsonProperty(IHasCompanyAgentConfigIdExpr.PropertyNameCompanyAgentConfigIdExpr)]
    public string? CompanyAgentConfigIdExpr { get; set; }

    [JsonProperty("team_categories__expr")]
    public string TeamCategories { get; set; }

    [JsonProperty("retrieval_window_timestamp__expr")]
    public string? RetrievalWindowTimestampExpr { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.Ai;

    [JsonConstructor]
    public AgentEvaluateIntentionStepExecutorStepArgs(
        string contactIdExpr,
        string? companyAgentConfigIdExpr,
        string teamCategories,
        string? retrievalWindowTimestampExpr = null)
    {
        ContactIdExpr = contactIdExpr;
        CompanyAgentConfigIdExpr = companyAgentConfigIdExpr;
        TeamCategories = teamCategories;
        RetrievalWindowTimestampExpr = retrievalWindowTimestampExpr;
    }
}