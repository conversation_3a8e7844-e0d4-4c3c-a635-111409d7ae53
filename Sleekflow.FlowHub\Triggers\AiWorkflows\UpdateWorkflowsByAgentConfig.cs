using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowAgentConfigMappings;

[TriggerGroup(ControllerNames.AiWorkflows)]
public class UpdateWorkflowsByAgentConfig : ITrigger
{
    private readonly IAiWorkflowService _aiWorkflowService;

    public UpdateWorkflowsByAgentConfig(IAiWorkflowService aiWorkflowService)
    {
        _aiWorkflowService = aiWorkflowService;
    }

    public class UpdateWorkflowsByAgentConfigInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [StringLength(128, MinimumLength = 1)]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("agent_config_id")]
        public string AgentConfigId { get; set; }


        [Required]
        [JsonProperty("publish_type")]
        //keep-original-status or mark-as-draft
        public string PublishType { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }
    }

    public class UpdateWorkflowsByAgentConfigOutput
    {
        [JsonProperty("workflow_ids")]
        public List<string> WorkflowIds { get; set; }

        [JsonProperty("need_enable_workflow_ids")]
        public List<string> NeedEnableWorkflowIds { get; set; }

        [JsonConstructor]
        public UpdateWorkflowsByAgentConfigOutput(List<string> workflowIds, List<string> needEnableWorkflowIds)
        {
            WorkflowIds = workflowIds;
            NeedEnableWorkflowIds = needEnableWorkflowIds;
        }
    }

    public async Task<UpdateWorkflowsByAgentConfigOutput> F(
        UpdateWorkflowsByAgentConfigInput updateWorkflowsByAgentConfigInput
    )
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            updateWorkflowsByAgentConfigInput.SleekflowStaffId,
            updateWorkflowsByAgentConfigInput.SleekflowStaffTeamIds
        );

        var (updatedWorkflowIds, needEnableWorkflowIds) = await _aiWorkflowService.UpdateAiWorkflows(
            updateWorkflowsByAgentConfigInput.SleekflowCompanyId,
            updateWorkflowsByAgentConfigInput.AgentConfigId,
            updateWorkflowsByAgentConfigInput.PublishType,
            sleekflowStaff
        );

        return new UpdateWorkflowsByAgentConfigOutput(updatedWorkflowIds, needEnableWorkflowIds);
    }
}
