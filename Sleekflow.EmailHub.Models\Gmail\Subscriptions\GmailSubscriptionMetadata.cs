using Newtonsoft.Json;
using Sleekflow.EmailHub.Models.Constants;
using Sleekflow.EmailHub.Models.Subscriptions;

namespace Sleekflow.EmailHub.Models.Gmail.Subscriptions;

public class GmailSubscriptionMetadata : EmailSubscriptionMetadata
{
    [JsonProperty("history_id")]
    public ulong? HistoryId { get; set; }

    [JsonProperty("expire_in")]
    public DateTimeOffset ExpireIn { get; set; }

    [JsonConstructor]
    public GmailSubscriptionMetadata(
        ulong? historyId,
        DateTimeOffset expireIn)
        : base(ProviderNames.Gmail, ProviderNames.Gmail)
    {
        HistoryId = historyId;
        ExpireIn = expireIn;
    }
}