﻿using Microsoft.Azure.Cosmos;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Locks;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.IntelligentHubConfigs;

public interface IIntelligentHubConfigService
{
    public Task<IntelligentHubConfig?> GetIntelligentHubConfigAsync(string sleekflowCompanyId);

    public Task<(List<IntelligentHubConfig> IntelligentHubConfigs, string? ContinuationToken)>
        GetIntelligentHubConfigsAsync(
            string? continuationToken,
            int limit);

    public Task<IntelligentHubConfig> InitializeIntelligentHubConfigAsync(
        string sleekflowCompanyId,
        Dictionary<string, int> usageLimits);

    public Task<IntelligentHubConfig> UpdateIntelligentHubConfigAsync(
        string id,
        string sleekflowCompanyId,
        Dictionary<string, int> usageLimits,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    Task<IntelligentHubConfig> UpdateIntelligentHubConfigUsageLimitOffsetsAsync(
        string id,
        string sleekflowCompanyId,
        Dictionary<string, int> usageLimitOffsets,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    public Task<(bool EnableWritingAssistant, bool EnableSmartReply)> UpdateAiFeatureSettingAsync(
        string sleekflowCompanyId,
        bool enableWritingAssistant,
        bool enableSmartReply,
        AuditEntity.SleekflowStaff? sleekflowStaff);

    public Task<(bool EnableWritingAssistant, bool EnableSmartReply)> GetAiFeatureSettingAsync(
        string sleekflowCompanyId);
}

public class IntelligentHubConfigService : IIntelligentHubConfigService, IScopedService
{
    private readonly IIdService _idService;
    private readonly ILockService _lockService;
    private readonly IIntelligentHubConfigRepository _intelligentHubConfigRepository;

    public IntelligentHubConfigService(
        IIdService idService,
        ILockService lockService,
        IIntelligentHubConfigRepository intelligentHubConfigRepository)
    {
        _idService = idService;
        _lockService = lockService;
        _intelligentHubConfigRepository = intelligentHubConfigRepository;
    }

    public async Task<IntelligentHubConfig?> GetIntelligentHubConfigAsync(string sleekflowCompanyId)
    {
        return (await _intelligentHubConfigRepository.GetObjectsAsync(
                c =>
                    c.SleekflowCompanyId == sleekflowCompanyId))
            .FirstOrDefault();
    }

    public async Task<(List<IntelligentHubConfig> IntelligentHubConfigs, string? ContinuationToken)>
        GetIntelligentHubConfigsAsync(
            string? continuationToken,
            int limit)
    {
        return await _intelligentHubConfigRepository.GetContinuationTokenizedObjectsAsync(
            c => true,
            continuationToken,
            limit);
    }

    public async Task<IntelligentHubConfig> InitializeIntelligentHubConfigAsync(
        string sleekflowCompanyId,
        Dictionary<string, int> usageLimits)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(IntelligentHubConfigService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            var existedConfig = await GetIntelligentHubConfigAsync(sleekflowCompanyId);
            if (existedConfig is not null)
            {
                throw new SfUserFriendlyException("Intelligent hub config already initialized.");
            }

            return await _intelligentHubConfigRepository.UpsertAndGetAsync(
                new IntelligentHubConfig(
                    _idService.GetId(SysTypeNames.IntelligentHubConfig),
                    sleekflowCompanyId,
                    usageLimits,
                    null,
                    null,
                    DateTimeOffset.UtcNow,
                    DateTimeOffset.UtcNow),
                sleekflowCompanyId);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<IntelligentHubConfig> UpdateIntelligentHubConfigAsync(
        string id,
        string sleekflowCompanyId,
        Dictionary<string, int> usageLimits,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(IntelligentHubConfigService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            return await _intelligentHubConfigRepository.PatchAndGetAsync(
                id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set($"/{IntelligentHubConfig.PropertyNameUsageLimits}", usageLimits),
                    PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                    PatchOperation.Set($"/{IHasUpdatedBy.PropertyNameUpdatedBy}", sleekflowStaff)
                });
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<IntelligentHubConfig> UpdateIntelligentHubConfigUsageLimitOffsetsAsync(
        string id,
        string sleekflowCompanyId,
        Dictionary<string, int> usageLimitOffsets,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var @lock = await _lockService.WaitUnitLockAsync(
            new[]
            {
                nameof(IntelligentHubConfigService),
                sleekflowCompanyId
            },
            TimeSpan.FromSeconds(10),
            TimeSpan.FromSeconds(30));

        try
        {
            return await _intelligentHubConfigRepository.PatchAndGetAsync(
                id,
                sleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Set($"/{IntelligentHubConfig.PropertyNameUsageLimitOffsets}", usageLimitOffsets),
                    PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow),
                    PatchOperation.Set($"/{IHasUpdatedBy.PropertyNameUpdatedBy}", sleekflowStaff)
                });
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<(bool EnableWritingAssistant, bool EnableSmartReply)> UpdateAiFeatureSettingAsync(
        string sleekflowCompanyId,
        bool enableWritingAssistant,
        bool enableSmartReply,
        AuditEntity.SleekflowStaff? sleekflowStaff)
    {
        var intelligentHubConfig = (await _intelligentHubConfigRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId)).FirstOrDefault();

        if (intelligentHubConfig is null)
        {
            throw new SfUserFriendlyException("IntelligentHub not enrolled yet.");
        }

        intelligentHubConfig.EnableWritingAssistant = enableWritingAssistant;
        intelligentHubConfig.EnableSmartReply = enableSmartReply;
        intelligentHubConfig.UpdatedBy = sleekflowStaff;

        await _intelligentHubConfigRepository.ReplaceAsync(
            intelligentHubConfig.Id,
            sleekflowCompanyId,
            intelligentHubConfig);

        return (enableWritingAssistant, enableSmartReply);
    }

    public async Task<(bool EnableWritingAssistant, bool EnableSmartReply)> GetAiFeatureSettingAsync(
        string sleekflowCompanyId)
    {
        var intelligentHubConfig = (await _intelligentHubConfigRepository.GetObjectsAsync(
            c =>
                c.SleekflowCompanyId == sleekflowCompanyId)).FirstOrDefault();

        if (intelligentHubConfig is null)
        {
            throw new SfUserFriendlyException("IntelligentHub not enrolled yet.");
        }

        return (intelligentHubConfig.EnableWritingAssistant, intelligentHubConfig.EnableSmartReply);
    }
}