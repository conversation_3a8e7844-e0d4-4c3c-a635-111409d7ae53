using Sleekflow.CommerceHub.Models.Languages;
using Sleekflow.DependencyInjection;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Currencies;

public interface ICurrencyService
{
    List<Currency> GetCurrencies();
}

public class CurrencyService : ICurrencyService, ISingletonService
{
    public List<Currency> GetCurrencies()
    {
        var currencies = CultureUtils.GetThreeLetterIsoCurrencySymbolToRegionInfo()
            .Select(
                kvp => new Currency(
                    kvp.Key,
                    kvp.Value.CurrencyEnglishName,
                    kvp.Value.CurrencySymbol))
            .Where(
                c =>
                    c.CurrencyIsoCode == "HKD"
                    || c.CurrencyIsoCode == "GBP"
                    || c.CurrencyIsoCode == "MYR"
                    || c.CurrencyIsoCode == "SGD"
                    || c.CurrencyIsoCode == "IDR"
                    || c.CurrencyIsoCode == "BRL"
                    || c.CurrencyIsoCode == "AED"
                    || c.CurrencyIsoCode == "USD")
            .ToList();

        return currencies;
    }
}