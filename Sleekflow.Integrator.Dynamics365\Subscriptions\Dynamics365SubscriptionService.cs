﻿using MassTransit.Testing;
using Microsoft.Azure.Cosmos;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.Dynamics365.Subscriptions;

public interface IDynamics365SubscriptionService
{
    Task ClearAsync(string entityTypeName, string sleekflowCompanyId);

    Task UpsertAsync(string entityTypeName, string sleekflowCompanyId, int interval);

    Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId);
}

public class Dynamics365SubscriptionService : IDynamics365SubscriptionService, ISingletonService
{
    private readonly IDynamics365SubscriptionRepository _dynamics365SubscriptionRepository;
    private readonly IIdService _idService;

    public Dynamics365SubscriptionService(
        IDynamics365SubscriptionRepository dynamics365SubscriptionRepository,
        IIdService idService)
    {
        _dynamics365SubscriptionRepository = dynamics365SubscriptionRepository;
        _idService = idService;
    }

    public async Task ClearAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _dynamics365SubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == Dynamics365Subscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _dynamics365SubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }

    public async Task UpsertAsync(string entityTypeName, string sleekflowCompanyId, int interval)
    {
        var subscription = await _dynamics365SubscriptionRepository.GetObjectEnumerableAsync(
                s =>
                    s.EntityTypeName == entityTypeName
                    && s.SysTypeName == Dynamics365Subscription.SysTypeNameValue
                    && s.SleekflowCompanyId == sleekflowCompanyId)
            .FirstOrDefault();
        if (subscription != null)
        {
            subscription.Interval = interval;

            await _dynamics365SubscriptionRepository.ReplaceAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                subscription);

            return;
        }

        var createCount = await _dynamics365SubscriptionRepository.CreateAsync(
            new Dynamics365Subscription(
                _idService.GetId(Dynamics365Subscription.SysTypeNameValue),
                sleekflowCompanyId,
                entityTypeName,
                interval,
                DateTimeOffset.UtcNow,
                null,
                null),
            sleekflowCompanyId);
        if (createCount == 0)
        {
            throw new SfUserFriendlyException("Unable to init the type");
        }
    }

    public async Task UpdateWithEmptyDurablePayloadAsync(string entityTypeName, string sleekflowCompanyId)
    {
        await foreach (var subscription in _dynamics365SubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.EntityTypeName == entityTypeName
                               && s.SysTypeName == Dynamics365Subscription.SysTypeNameValue
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _dynamics365SubscriptionRepository.PatchAsync(
                subscription.Id,
                subscription.SleekflowCompanyId,
                new List<PatchOperation>
                {
                    PatchOperation.Replace(
                        $"/{Dynamics365Subscription.PropertyNameDurablePayload}",
                        new object()),
                });
        }
    }
}