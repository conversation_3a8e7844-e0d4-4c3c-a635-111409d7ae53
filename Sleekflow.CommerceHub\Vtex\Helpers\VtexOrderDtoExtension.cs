﻿using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Vtex.Dtos.VtexOrderDtos;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.CommerceHub.Vtex.Helpers;

public static class VtexOrderDtoExtension
{
    public static VtexOrderOverview ToVtexOrderOverview(this VtexOrderDto vtexOrderDto)
    {
        var vtexStorePreferencesOverview = new VtexStorePreferencesOverview(
            vtexOrderDto.StorePreferencesData?.CountryCode,
            vtexOrderDto.StorePreferencesData?.CurrencyCode,
            vtexOrderDto.StorePreferencesData?.CurrencySymbol,
            vtexOrderDto.StorePreferencesData?.TimeZone);

        var vtexShippingOverview = new VtexShippingOverview(
            vtexOrderDto.ShippingData?.Address?.ReceiverName,
            vtexOrderDto.ShippingData?.Address?.PostalCode,
            vtexOrderDto.ShippingData?.Address?.City,
            vtexOrderDto.ShippingData?.Address?.State,
            vtexOrderDto.ShippingData?.Address?.Country,
            vtexOrderDto.ShippingData?.Address?.Street);

        var vtexClientProfileOverview = new VtexClientProfileOverview(
            vtexOrderDto.ClientProfileData?.Email,
            vtexOrderDto.ClientProfileData?.FirstName,
            vtexOrderDto.ClientProfileData?.LastName,
            vtexOrderDto.ClientProfileData?.Phone);

        var statusDisplayName = VtexOrderStatusCodes.ToStatusDisplayName(vtexOrderDto.Status);

        // prices
        var currencyDecimalDigits = vtexOrderDto.StorePreferencesData?.CurrencyFormatInfo?.CurrencyDecimalDigits ?? 2;
        var divider = Math.Pow(10, currencyDecimalDigits);

        // Calculate total value, discount, and tax
        var totalValue = (vtexOrderDto.Value ?? 0.0) / divider;
        var totalItems = 0.0;
        var totalDiscount = 0.0;
        var totalTax = 0.0;
        var totalShipping = 0.0;

        var itemsTotal = vtexOrderDto.Totals?.FirstOrDefault(t => t.Id == "Items");
        if (itemsTotal != null)
        {
            totalItems = itemsTotal.Value / divider;
        }

        var discountTotal = vtexOrderDto.Totals?.FirstOrDefault(t => t.Id == "Discounts");
        if (discountTotal != null)
        {
            totalDiscount = discountTotal.Value / divider;
        }

        var taxTotal = vtexOrderDto.Totals?.FirstOrDefault(t => t.Id == "Tax");
        if (taxTotal != null)
        {
            totalTax = taxTotal.Value / divider;
        }

        var shippingTotal = vtexOrderDto.Totals?.FirstOrDefault(t => t.Id == "Shipping");
        if (shippingTotal != null)
        {
            totalShipping += shippingTotal.Value / divider;
        }

        // Convert order items
        var orderItems = vtexOrderDto.Items?.Select(item => new VtexOrderItemOverview(
            item.Id,
            item.ProductId,
            item.RefId,
            item.Name,
            item.ImageUrl,
            item.Quantity,
            item.Price / divider
        )).ToList() ?? new List<VtexOrderItemOverview>();

        // Create normalized order items string
        var currencySymbol = vtexOrderDto.StorePreferencesData?.CurrencySymbol?? "$";
        var normalizedOrderItems = string.Join("; ", orderItems.Select(item =>
            $"{item.SkuName} {currencySymbol}{item.Price} ×{item.Quantity}"));

        return new VtexOrderOverview(
            vtexOrderDto.OrderId,
            statusDisplayName,
            vtexOrderDto.Status,
            vtexOrderDto.CreationDate,
            vtexOrderDto.LastChange,
            vtexOrderDto.IsCompleted,
            vtexOrderDto.IsCheckedIn,
            vtexOrderDto.AuthorizedDate,
            vtexOrderDto.InvoicedDate,
            vtexOrderDto.CancelReason,
            totalValue,
            totalItems,
            totalDiscount,
            totalTax,
            totalShipping,
            orderItems,
            normalizedOrderItems,
            vtexStorePreferencesOverview,
            vtexShippingOverview,
            vtexClientProfileOverview);
    }
}