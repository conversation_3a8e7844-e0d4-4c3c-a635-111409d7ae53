﻿using MassTransit;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Events.InternalActionEvents;
using Sleekflow.FlowHub.Workflows;
using Sleekflow.Persistence;

namespace Sleekflow.FlowHub.Executor.Consumers.InternalActionEventConsumers;

public class DeleteInternalWorkflowRequestConsumerDefinition
    : ConsumerDefinition<DeleteInternalWorkflowRequestConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<DeleteInternalWorkflowRequestConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 16;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class DeleteInternalWorkflowRequestConsumer : IConsumer<DeleteInternalWorkflowRequest>
{
    private readonly IWorkflowService _workflowService;

    public DeleteInternalWorkflowRequestConsumer(IWorkflowService workflowService)
    {
        _workflowService = workflowService;
    }

    public async Task Consume(ConsumeContext<DeleteInternalWorkflowRequest> context)
    {
        var deleteInternalWorkflowRequest = context.Message;

        await ValidateWorkflowType(deleteInternalWorkflowRequest.WorkflowId, deleteInternalWorkflowRequest.SleekflowCompanyId);

        await _workflowService.DeleteWorkflowAsync(
            deleteInternalWorkflowRequest.WorkflowId,
            deleteInternalWorkflowRequest.SleekflowCompanyId,
            new AuditEntity.SleekflowStaff(string.Empty, null));

        await context.RespondAsync(new DeleteInternalWorkflowReply());
    }

    private async Task ValidateWorkflowType(string workflowId, string sleekflowCompanyId)
    {
        var workflow = await _workflowService.GetLatestWorkflowAsync(workflowId, sleekflowCompanyId);

        if (workflow.WorkflowType == WorkflowType.Normal)
        {
            throw new SfWorkflowTypeException("Normal workflow cannot be deleted here.");
        }
    }
}