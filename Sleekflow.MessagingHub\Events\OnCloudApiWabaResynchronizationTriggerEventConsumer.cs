using MassTransit;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class
    OnCloudApiWabaResynchronizationTriggerEventConsumerDefinition
    : ConsumerDefinition<
        OnCloudApiWabaResynchronizationTriggerEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiWabaResynchronizationTriggerEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class
    OnCloudApiWabaResynchronizationTriggerEventConsumer : IConsumer<OnCloudApiWabaResynchronizationTriggerEvent>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<OnCloudApiWabaResynchronizationTriggerEventConsumer> _logger;

    public OnCloudApiWabaResynchronizationTriggerEventConsumer(
        IWabaService wabaService,
        ILogger<OnCloudApiWabaResynchronizationTriggerEventConsumer> logger)
    {
        _logger = logger;
        _wabaService = wabaService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiWabaResynchronizationTriggerEvent> context)
    {
        var onCloudApiWabaResynchronizationTriggerEvent = context.Message;
        var wabas = await _wabaService.GetAllAsync();
        foreach (var waba in wabas)
        {
            await _wabaService.GetAndReconstructWabaAsync(waba, null, true, null);
        }
    }
}