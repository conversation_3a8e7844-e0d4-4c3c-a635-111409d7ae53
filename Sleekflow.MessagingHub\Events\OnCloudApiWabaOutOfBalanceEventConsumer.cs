using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Events;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiWabaOutOfBalanceEventConsumerDefinition : ConsumerDefinition<OnCloudApiWabaOutOfBalanceEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiWabaOutOfBalanceEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32 * 10;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiWabaOutOfBalanceEventConsumer : IConsumer<OnCloudApiWabaOutOfBalanceEvent>
{
    private readonly IWabaService _wabaService;
    private readonly ILogger<OnCloudApiWabaOutOfBalanceEventConsumer> _logger;
    private readonly IBusinessBalanceService _businessBalanceService;

    public OnCloudApiWabaOutOfBalanceEventConsumer(
        IWabaService wabaService,
        ILogger<OnCloudApiWabaOutOfBalanceEventConsumer> logger,
        IBusinessBalanceService businessBalanceService)
    {
        _wabaService = wabaService;
        _logger = logger;
        _businessBalanceService = businessBalanceService;
    }

    public async Task Consume(ConsumeContext<OnCloudApiWabaOutOfBalanceEvent> context)
    {
        var onCloudApiWabaOutOfBalanceEvent = context.Message;
        var facebookWabaId = onCloudApiWabaOutOfBalanceEvent.FacebookWabaId;
        var facebookBusinessId = onCloudApiWabaOutOfBalanceEvent.FacebookBusinessId;
        var businessBalance = await _businessBalanceService.GetWithFacebookBusinessIdAsync(facebookBusinessId);

        if (businessBalance is null)
        {
            throw new SfNotFoundObjectException(
                $"Unable to locate Business balance {JsonConvert.SerializeObject(businessBalance)}");
        }

        var wabaBalance = businessBalance.WabaBalances.FirstOrDefault(x => x.FacebookWabaId == facebookWabaId);

        if (wabaBalance is null)
        {
            throw new SfNotFoundObjectException(
                $"Unable to locate Waba {facebookWabaId} balance {JsonConvert.SerializeObject(businessBalance)}");
        }

        if (wabaBalance.Balance.Amount >= 0)
        {
            throw new SfInternalErrorException(
                $"Not supported operation OnCloudApiWabaOutOfBalanceEventConsumer while balance is greater then zero {JsonConvert.SerializeObject(wabaBalance)}");
        }

        var waba = await _wabaService.GetWabaWithFacebookWabaIdAsync(facebookWabaId);

        _logger.LogInformation("Obtained require to be blocked waba {Waba}", JsonConvert.SerializeObject(waba));

        if (waba.MessagingFunctionLimitation != MessagingFunctionLimitationType.BlockAll)
        {
            try
            {
                if (await _wabaService.UnblockOrBlockWabaAsync(waba.Id, waba.FacebookWabaId, false) == 0)
                {
                    throw new SfInternalErrorException("Unable to BlockAllActionAndUpsertWaba ");
                }
            }
            catch (Exception exception)
            {
                _logger.LogError(
                    exception,
                    "Exception occur during OnCloudApiWabaOutOfBalanceEventConsumer {Waba}/{Exception},",
                    JsonConvert.SerializeObject(waba), JsonConvert.SerializeObject(exception));
            }
        }
        else
        {
            _logger.LogInformation("Waba is already blocked {Waba}", JsonConvert.SerializeObject(waba));
        }
    }
}