using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Reducers;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings.Reducers;

public class ActionAgentGeminiChatHistoryReducer(
    ILeadNurturingAgentDefinitions leadNurturingAgentDefinitions)
    : GeminiChatHistoryReducer
{
    public override async Task<IEnumerable<ChatMessageContent>?> ReduceAsync(
        IReadOnlyList<ChatMessageContent> chatHistory,
        CancellationToken cancellationToken = default)
    {
        var chatMessageContents =
            chatHistory.ToList();

        var filteredMessageContents =
            chatMessageContents
                .Where(c =>
                    c.AuthorName == "Context"
                    || c.AuthorName == leadNurturingAgentDefinitions.LeadClassifierAgentName
                    || c.AuthorName == leadNurturingAgentDefinitions.LeadAssignmentPlanningAgentName
                    || c.AuthorName == leadNurturingAgentDefinitions.DemoSchedulingPlanningAgentName
                    || (
                        JsonUtils.TryParseJson<Dictionary<string, object>>(c.Content ?? "{}", out var json)
                        && json!.ContainsKey("decision")))
                .ToList();

        return await base.ReduceAsync(filteredMessageContents, cancellationToken);
    }
}