using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.DistributedInvocations;

public class DistributedInvocationContext : IHasSleekflowCompanyId, IHasSleekflowStaff
{
    [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId, NullValueHandling = NullValueHandling.Ignore)]
    public string? SleekflowStaffId { get; set; }

    [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds, NullValueHandling = NullValueHandling.Ignore)]
    public List<string>? SleekflowStaffTeamIds { get; set; }

    [JsonProperty("flow_builder_workflow_context", NullValueHandling = NullValueHandling.Ignore)]
    public FlowBuilderWorkflowContext? FlowBuilderWorkflowContext { get; set; }

    [JsonConstructor]
    public DistributedInvocationContext(
        string sleekflowCompanyId,
        string? sleekflowStaffId = null,
        List<string>? sleekflowStaffTeamIds = null,
        FlowBuilderWorkflowContext? flowBuilderWorkflowContext = null)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        SleekflowStaffId = sleekflowStaffId;
        SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        FlowBuilderWorkflowContext = flowBuilderWorkflowContext;
    }
}