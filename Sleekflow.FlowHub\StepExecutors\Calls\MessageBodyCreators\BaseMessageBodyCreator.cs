using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Messages;
using Sleekflow.FlowHub.Models.Messages.BaseMessageObjects;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.StepExecutors.Abstractions;

namespace Sleekflow.FlowHub.StepExecutors.Calls.MessageBodyCreators;

public abstract class BaseMessageBodyCreator : IMessageBodyCreator, IScopedService
{
    protected readonly string ChannelType;

    protected BaseMessageBodyCreator(string channelType)
    {
        ChannelType = channelType;
    }

    public bool CanHandle(string channelType) => ChannelType == channelType;

    public abstract Task<(MessageBody Body, string MessageType)> CreateMessageBodyAndMessageTypeAsync(string messageStr, SendMessageV2StepArgs args);

    public virtual Task<MessageBody> CreateMediaMessageBodyAsync(string mediaUrl, string mediaType, string? mediaName, SendMessageV2StepArgs args)
    {
        return Task.FromResult(new MessageBody(
            audioMessage: mediaType is "audio"
                ? new AudioMessageObject(
                    null,
                    mediaUrl,
                    null,
                    mediaName,
                    null)
                : null,
            contactsMessage: null,
            currencyMessage: null,
            documentMessage: mediaType is "document"
                ? new DocumentMessageObject(
                    null,
                    mediaUrl,
                    null,
                    mediaName,
                    null)
                : null,
            imageMessage: mediaType is "image"
                ? new ImageMessageObject(
                    null,
                    mediaUrl,
                    null,
                    mediaName,
                    null)
                : null,
            locationMessage: null,
            reactionMessage: null,
            textMessage: null,
            videoMessage: mediaType is "video"
                ? new VideoMessageObject(
                    null,
                    mediaUrl,
                    null,
                    mediaName,
                    null)
                : null,
            interactiveMessage: null,
            templateMessage: null,
            interactiveReplyMessage: null,
            dateTimeMessage: null,
            facebookMessengerMessage: null,
            instagramMessengerMessage: null,
            orderMessage: null,
            telegramMessengerMessage: null,
            weChatMessengerMessage: null,
            liveChatMessage: null,
            viberMessage: null,
            lineMessage: null,
            smsMessage: null));
    }

    protected MessageBody CreateBaseMessageBody(
        AudioMessageObject? audioMessage = null,
        List<ContactMessageObject>? contactsMessage = null,
        CurrencyMessageObject? currencyMessage = null,
        DocumentMessageObject? documentMessage = null,
        ImageMessageObject? imageMessage = null,
        LocationMessageObject? locationMessage = null,
        ReactionMessageObject? reactionMessage = null,
        TextMessageObject? textMessage = null,
        VideoMessageObject? videoMessage = null,
        InteractiveMessageObject? interactiveMessage = null,
        TemplateMessageObject? templateMessage = null,
        InteractiveReplyMessageObject? interactiveReplyMessage = null,
        DateTimeMessageObject? dateTimeMessage = null,
        FacebookMessengerMessageObject? facebookMessengerMessage = null,
        InstagramMessengerMessageObject? instagramMessengerMessage = null,
        OrderMessageObject? orderMessage = null,
        TelegramMessengerMessageObject? telegramMessengerMessage = null,
        WeChatMessengerMessageObject? weChatMessengerMessage = null,
        LiveChatMessageObject? liveChatMessage = null,
        ViberMessageObject? viberMessage = null,
        LineMessageObject? lineMessage = null,
        SmsMessageObject? smsMessage = null)
    {
        return new MessageBody(
            audioMessage,
            contactsMessage,
            currencyMessage,
            documentMessage,
            imageMessage,
            locationMessage,
            reactionMessage,
            textMessage,
            videoMessage,
            interactiveMessage,
            templateMessage,
            interactiveReplyMessage,
            dateTimeMessage,
            facebookMessengerMessage,
            instagramMessengerMessage,
            orderMessage,
            telegramMessengerMessage,
            weChatMessengerMessage,
            liveChatMessage,
            viberMessage,
            lineMessage,
            smsMessage);
    }
}