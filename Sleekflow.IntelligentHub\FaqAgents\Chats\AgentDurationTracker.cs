using System.Diagnostics;
using System.Text;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

public class AgentResponseMetrics
{
    public int InputTokenCount { get; set; }

    public int OutputTokenCount { get; set; }

    public string AuthorName { get; set; } = string.Empty;

    public string Content { get; set; } = string.Empty;

    public TimeSpan TimeSinceLastResponse { get; set; }

    public TimeSpan TotalElapsedTime { get; set; }

    public int CachedOutputTokenCount { get; set; }
}

public interface IAgentDurationTracker
{
    void StartTracking();

    void TrackResponse(
        int inputTokenCount,
        int outputTokenCount,
        string authorName,
        string content,
        int? cachedOutputTokenCount = 0);

    string GetMetricReport(string collaborationMode);
}

public class AgentDurationTracker
    : IAgentDurationTracker, IScopedService
{
    private readonly Stopwatch _stopwatch = new Stopwatch();
    private readonly List<AgentResponseMetrics> _responseMetrics = new ();
    private DateTime _lastResponseTime = DateTime.UtcNow;

    public void StartTracking()
    {
        _stopwatch.Start();
        _lastResponseTime = DateTime.UtcNow;
    }

    public void TrackResponse(
        int inputTokenCount,
        int outputTokenCount,
        string authorName,
        string content,
        int? cachedOutputTokenCount = 0)
    {
        var now = DateTime.UtcNow;
        var timeSinceLastResponse = now - _lastResponseTime;

        _responseMetrics.Add(
            new AgentResponseMetrics
            {
                InputTokenCount = inputTokenCount,
                OutputTokenCount = outputTokenCount,
                CachedOutputTokenCount = cachedOutputTokenCount ?? 0,
                AuthorName = authorName,
                Content = content,
                TimeSinceLastResponse = timeSinceLastResponse,
                TotalElapsedTime = _stopwatch.Elapsed
            });

        _lastResponseTime = now;
    }

    private TimeSpan GetTotalElapsedTime()
    {
        return _stopwatch.Elapsed;
    }

    private IReadOnlyList<AgentResponseMetrics> GetResponseMetrics()
    {
        return _responseMetrics.AsReadOnly();
    }

    public string GetMetricReport(string collaborationMode)
    {
        var metrics = GetResponseMetrics();
        var totalTime = GetTotalElapsedTime();

        var sb = new StringBuilder();

        sb.AppendLine();
        sb.AppendLine("====================== Agent Duration Metrics ======================");
        sb.AppendLine($"Mode: {collaborationMode}");
        sb.AppendLine($"Total Time: {totalTime}");
        sb.AppendLine($"Response Count: {metrics.Count}");
        sb.AppendLine();
        sb.AppendLine("Detailed Metrics:");

        for (int i = 0; i < metrics.Count; i++)
        {
            var m = metrics[i];


#if DEBUG
            sb.AppendLine(
                $"""
                 Response #{i + 1}:
                   Author: {m.AuthorName}
                   Input Tokens: {m.InputTokenCount} ({m.CachedOutputTokenCount})
                   Output Tokens: {m.OutputTokenCount}
                   Time Since Last Response: {m.TimeSinceLastResponse}
                   Total Elapsed Time: {m.TotalElapsedTime}
                   Content: {m.Content}
                 """);
#else
            var minimizedJson = GetMinimizedJson(m.Content);

            sb.AppendLine(
                $"""
                 Response #{i + 1}:
                   Author: {m.AuthorName}
                   Input Tokens: {m.InputTokenCount} ({m.CachedOutputTokenCount})
                   Output Tokens: {m.OutputTokenCount}
                   Time Since Last Response: {m.TimeSinceLastResponse}
                   Total Elapsed Time: {m.TotalElapsedTime}
                   Content: {(minimizedJson.Length > 4000 ? minimizedJson.Substring(0, 4000) + "..." : minimizedJson)}
                 """);
#endif
        }

        sb.AppendLine("=====================================================================");

        return sb.ToString();
    }

    private string GetMinimizedJson(string json)
    {
        try
        {
            return JsonConvert.SerializeObject(JsonConvert.DeserializeObject(json), Formatting.None);
        }
        catch (Exception)
        {
            return json;
        }
    }
}