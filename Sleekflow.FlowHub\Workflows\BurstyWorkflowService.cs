using Microsoft.Azure.Cosmos;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.DurablePayloads;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Exceptions;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.Workflows;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Workers.Services;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Workflows;

public interface IBurstyWorkflowService
{
    Task<ProxyWorkflow> TriggerScheduledWorkflowAsync(string sleekflowCompanyId, string workflowId);

    // only for new scheduled workflow logic
    Task<ProxyWorkflow> TerminateScheduledWorkflowAsync(string sleekflowCompanyId, string workflowId);
}

public class BurstyWorkflowService : IBurstyWorkflowService, IScopedService
{
    private readonly IWorkflowOrchestrationService _workflowOrchestrationService;
    private readonly IWorkflowService _workflowService;
    private readonly IFlowHubConfigService _flowHubConfigService;
    private readonly IWorkflowRecurringSettingsParser _workflowRecurringSettingsParser;
    private readonly ILogger<BurstyWorkflowService> _logger;
    private readonly IPropertyOrchestrationService _propertyOrchestrationService;

    public BurstyWorkflowService(
        IWorkflowOrchestrationService workflowOrchestrationService,
        IWorkflowService workflowService,
        IFlowHubConfigService flowHubConfigService,
        IWorkflowRecurringSettingsParser workflowRecurringSettingsParser,
        ILogger<BurstyWorkflowService> logger,
        IPropertyOrchestrationService propertyOrchestrationService)
    {
        _workflowOrchestrationService = workflowOrchestrationService;
        _workflowService = workflowService;
        _flowHubConfigService = flowHubConfigService;
        _workflowRecurringSettingsParser = workflowRecurringSettingsParser;
        _logger = logger;
        _propertyOrchestrationService = propertyOrchestrationService;
    }

    public async Task<ProxyWorkflow> TriggerScheduledWorkflowAsync(string sleekflowCompanyId, string workflowId)
    {
        _logger.LogInformation(
            "begin to TriggerScheduledWorkflowAsync: sleekflowCompanyId: {SleekflowCompanyId}, workflowId: {WorkflowId}",
            sleekflowCompanyId,
            workflowId);
        var proxyWorkflow = await _workflowService.GetLatestWorkflowAsync(workflowId, sleekflowCompanyId);
        _logger.LogInformation("proxyWorkflow: {ProxyWorkflow}", JsonConvert.SerializeObject(proxyWorkflow));

        if (proxyWorkflow is null || proxyWorkflow.ActivationStatus != WorkflowActivationStatuses.Active)
        {
            _logger.LogError("No active workflow found.");
            throw new SfWorkflowNotActiveException();
        }

        if (proxyWorkflow.WorkflowScheduleSettings is null)
        {
            _logger.LogError("workflow_schedule_settings not set.");
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.InternalError,
                "workflow_schedule_settings not set.");
        }

        if (proxyWorkflow.WorkflowScheduleSettings.DurablePayload is not null)
        {
            _logger.LogWarning(
                "[{Method}] Scheduled Workflow {WorkflowId} is already scheduled",
                nameof(TriggerScheduledWorkflowAsync),
                proxyWorkflow.WorkflowId);
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.UnmatchedCondition,
                $"Workflow {proxyWorkflow.WorkflowId} is already scheduled");
        }

        var flowHubConfig = await _flowHubConfigService.GetFlowHubConfigAsync(sleekflowCompanyId);

        if (flowHubConfig?.Origin is null)
        {
            _logger.LogWarning(
                "[{Method}] FlowHubConfig Origin not found for company {CompanyId}",
                nameof(TriggerScheduledWorkflowAsync),
                sleekflowCompanyId);
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.UnmatchedCondition,
                $"FlowHubConfig Origin not found for company {sleekflowCompanyId}");
        }

        DurablePayload? durablePayload = null;
        if (WorkflowScheduleTypes.ContactPropertyBasedDateTime.Equals(
                proxyWorkflow.WorkflowScheduleSettings.ScheduleType))
        {
            durablePayload = await ProcessContactPropertyDateTime(sleekflowCompanyId, workflowId, flowHubConfig, proxyWorkflow);
        }
        else if (WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime.Equals(
                     proxyWorkflow.WorkflowScheduleSettings.ScheduleType))
        {
            durablePayload = await ProcessCustomObjectPropertyDateTime(
                sleekflowCompanyId,
                workflowId,
                flowHubConfig,
                proxyWorkflow);
        }
        else
        {
            durablePayload = await ProcessScheduledDateTime(
                sleekflowCompanyId,
                workflowId,
                proxyWorkflow,
                flowHubConfig);
        }

        if (durablePayload is null)
        {
            _logger.LogError("durablePayload is null");
            throw new SfFlowHubUserFriendlyException(UserFriendlyErrorCodes.InternalError, "durablePayload is null");
        }

        // save the durablePayload to the database, so that we can terminate it later
        await _workflowService.PatchWorkflowAsync(
            proxyWorkflow.WorkflowVersionedId,
            proxyWorkflow.SleekflowCompanyId,
            new List<PatchOperation>()
            {
                PatchOperation.Set(
                    "/workflow_schedule_settings/durable_payload",
                    durablePayload),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            },
            proxyWorkflow.ETag!);

        proxyWorkflow = await _workflowService.GetLatestWorkflowAsync(workflowId, sleekflowCompanyId);

        return proxyWorkflow;
    }

    private async Task<DurablePayload> ProcessContactPropertyDateTime(string sleekflowCompanyId, string workflowId,
        FlowHubConfig flowHubConfig, ProxyWorkflow proxyWorkflow)
    {
        DurablePayload durablePayload;
        _logger.LogInformation("begin to trigger contact property workflow");
        durablePayload = await _propertyOrchestrationService
            .TriggerContactDateTimeWorkflowAsync<ContactPropertyDateTimeSettings>(
                sleekflowCompanyId,
                flowHubConfig.Origin,
                workflowId,
                proxyWorkflow.WorkflowVersionedId,
                proxyWorkflow.WorkflowScheduleSettings!.ContactPropertyDateTimeSettings,
                null,
                proxyWorkflow.Version,
                proxyWorkflow.WorkflowScheduleSettings.ScheduleType,
                proxyWorkflow.WorkflowScheduleSettings.RecurringSettings);
        return durablePayload;
    }

    private async Task<DurablePayload> ProcessScheduledDateTime(
        string sleekflowCompanyId,
        string workflowId,
        ProxyWorkflow? proxyWorkflow,
        FlowHubConfig? flowHubConfig)
    {
        if (proxyWorkflow.WorkflowScheduleSettings.ScheduledAt is null)
        {
            _logger.LogWarning(
                "[{Method}] Scheduled Workflow {WorkflowId} is missing ScheduledAt",
                nameof(TriggerScheduledWorkflowAsync),
                proxyWorkflow.WorkflowId);
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.UnmatchedCondition,
                $"Scheduled Workflow {proxyWorkflow.WorkflowId} is missing ScheduledAt");
        }

        var scheduledAt = proxyWorkflow.WorkflowScheduleSettings.ScheduledAt.Value;

        if (proxyWorkflow.WorkflowScheduleSettings is
            {
                IsNewScheduledWorkflowSchema: not true,
                IsOldScheduledWorkflowSchemaFirstRecurringCompleted: true,
                RecurringSettings: not null
            })
        {
            // we need to adjust the scheduled time to the next recurring time, for old scheduled workflow schema
            scheduledAt = _workflowRecurringSettingsParser.GetNextOccurrenceAfter(
                scheduledAt,
                proxyWorkflow.WorkflowScheduleSettings.RecurringSettings,
                DateTimeOffset.UtcNow);
            _logger.LogInformation(
                "`scheduledAt` is change to {ScheduledAt} due to old scheduled workflow schema.",
                scheduledAt);
        }

        _logger.LogInformation("begin to trigger scheduled workflow");
        var durablePayload = await _workflowOrchestrationService.TriggerScheduleWorkflowAsync(
            sleekflowCompanyId,
            flowHubConfig.Origin,
            workflowId,
            proxyWorkflow.WorkflowVersionedId,
            scheduledAt,
            proxyWorkflow.WorkflowScheduleSettings.RecurringSettings,
            proxyWorkflow.Version);
        return durablePayload;
    }

    private async Task<DurablePayload> ProcessCustomObjectPropertyDateTime(
        string sleekflowCompanyId,
        string workflowId,
        FlowHubConfig flowHubConfig,
        ProxyWorkflow proxyWorkflow)
    {
        _logger.LogInformation("begin to trigger custom object workflow");
        return await _propertyOrchestrationService.TriggerContactDateTimeWorkflowAsync<ContactPropertyDateTimeSettings>(
            sleekflowCompanyId,
            flowHubConfig.Origin,
            workflowId,
            proxyWorkflow.WorkflowVersionedId,
            null,
            proxyWorkflow.WorkflowScheduleSettings.CustomObjectDateTimeSettings,
            proxyWorkflow.Version,
            proxyWorkflow.WorkflowScheduleSettings.ScheduleType,
            proxyWorkflow.WorkflowScheduleSettings.RecurringSettings);
    }

    public async Task<ProxyWorkflow> TerminateScheduledWorkflowAsync(string sleekflowCompanyId, string workflowId)
    {
        var proxyWorkflow = await _workflowService.GetLatestWorkflowAsync(workflowId, sleekflowCompanyId);

        if (proxyWorkflow.WorkflowScheduleSettings is null)
        {
            _logger.LogWarning(
                "[{Method}] Scheduled Workflow {WorkflowId} is missing WorkflowScheduleSettings",
                nameof(TerminateScheduledWorkflowAsync),
                proxyWorkflow.WorkflowId);
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.UnmatchedCondition,
                $"Scheduled Workflow {proxyWorkflow.WorkflowId} is missing WorkflowScheduleSettings");
        }

        if (proxyWorkflow.WorkflowScheduleSettings.DurablePayload is null)
        {
            _logger.LogWarning(
                "[{Method}] Scheduled Workflow {WorkflowId} is missing DurablePayload",
                nameof(TerminateScheduledWorkflowAsync),
                proxyWorkflow.WorkflowId);
            throw new SfFlowHubUserFriendlyException(
                UserFriendlyErrorCodes.UnmatchedCondition,
                $"Scheduled Workflow {proxyWorkflow.WorkflowId} is missing DurablePayload");
        }

        await _workflowOrchestrationService.TerminateWorkflowDurableFunctionAsync(
            sleekflowCompanyId,
            workflowId,
            proxyWorkflow.WorkflowVersionedId,
            proxyWorkflow.WorkflowScheduleSettings.DurablePayload);

        // Remove the durablePayload or change status from the database
        await _workflowService.PatchWorkflowAsync(
            proxyWorkflow.WorkflowVersionedId,
            proxyWorkflow.SleekflowCompanyId,
            new List<PatchOperation>()
            {
                PatchOperation.Remove(
                    "/workflow_schedule_settings/durable_payload"),
                PatchOperation.Set($"/{IHasUpdatedAt.PropertyNameUpdatedAt}", DateTimeOffset.UtcNow)
            },
            proxyWorkflow.ETag!);

        proxyWorkflow = await _workflowService.GetLatestWorkflowAsync(workflowId, sleekflowCompanyId);

        return proxyWorkflow;
    }
}