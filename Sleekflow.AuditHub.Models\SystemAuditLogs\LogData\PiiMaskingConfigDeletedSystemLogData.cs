using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class PiiMaskingConfigDeletedSystemLogData
{
    [JsonProperty("pii_masking_config_id")]
    public long PiiMaskingConfigId { get; set; }

    [JsonProperty("display_name")]
    public string DisplayName { get; set; }

    [JsonConstructor]
    public PiiMaskingConfigDeletedSystemLogData(
        long piiMaskingConfigId,
        string displayName)
    {
        PiiMaskingConfigId = piiMaskingConfigId;
        DisplayName = displayName;
    }
}