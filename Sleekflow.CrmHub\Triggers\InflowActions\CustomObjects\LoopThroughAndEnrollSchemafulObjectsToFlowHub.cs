﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.InflowActions.CustomObjects;

[TriggerGroup(TriggerGroups.InflowActions)]
public class LoopThroughAndEnrollSchemafulObjectsToFlowHub
    : ITrigger<
        LoopThroughAndEnrollSchemafulObjectsToFlowHub.LoopThroughAndEnrollSchemafulObjectsToFlowHubInput,
        LoopThroughAndEnrollSchemafulObjectsToFlowHub.LoopThroughAndEnrollSchemafulObjectsToFlowHubOutput>
{
    private readonly ICustomObjectIntegratorService _customObjectIntegratorService;

    public LoopThroughAndEnrollSchemafulObjectsToFlowHub(
        ICustomObjectIntegratorService customObjectIntegratorService)
    {
        _customObjectIntegratorService = customObjectIntegratorService;
    }

    public class LoopThroughAndEnrollSchemafulObjectsToFlowHubInput : IHasSleekflowCompanyId
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
        [Required]
        public string SchemaId { get; set; }

        [JsonProperty("flow_hub_workflow_id")]
        [Required]
        public string FlowHubWorkflowId { get; set; }

        [JsonProperty("flow_hub_workflow_versioned_id")]
        [Required]
        public string FlowHubWorkflowVersionedId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSchemafulObjectsToFlowHubInput(
            string sleekflowCompanyId,
            string schemaId,
            string flowHubWorkflowId,
            string flowHubWorkflowVersionedId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SchemaId = schemaId;
            FlowHubWorkflowId = flowHubWorkflowId;
            FlowHubWorkflowVersionedId = flowHubWorkflowVersionedId;
        }
    }

    public class LoopThroughAndEnrollSchemafulObjectsToFlowHubOutput
    {
        [JsonProperty("loop_through_objects_progress_state_id")]
        public string LoopThroughObjectsProgressStateId { get; set; }

        [JsonConstructor]
        public LoopThroughAndEnrollSchemafulObjectsToFlowHubOutput(string loopThroughObjectsProgressStateId)
        {
            LoopThroughObjectsProgressStateId = loopThroughObjectsProgressStateId;
        }
    }

    public async Task<LoopThroughAndEnrollSchemafulObjectsToFlowHubOutput> F(
        LoopThroughAndEnrollSchemafulObjectsToFlowHubInput loopThroughAndEnrollSchemafulObjectsToFlowHubInput)
    {
        await _customObjectIntegratorService.TerminateInProgressLoopThroughExecutionsAsync(
            loopThroughAndEnrollSchemafulObjectsToFlowHubInput.FlowHubWorkflowId,
            loopThroughAndEnrollSchemafulObjectsToFlowHubInput.SleekflowCompanyId);

        var output = await _customObjectIntegratorService.LoopThroughAndEnrollObjectsToFlowHubAsync(
            loopThroughAndEnrollSchemafulObjectsToFlowHubInput.SleekflowCompanyId,
            loopThroughAndEnrollSchemafulObjectsToFlowHubInput.SchemaId,
            loopThroughAndEnrollSchemafulObjectsToFlowHubInput.FlowHubWorkflowId,
            loopThroughAndEnrollSchemafulObjectsToFlowHubInput.FlowHubWorkflowVersionedId);

        return new LoopThroughAndEnrollSchemafulObjectsToFlowHubOutput(output.LoopThroughObjectsProgressStateId);
    }
}