using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Invoices;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.Balances.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Balances)]
public class GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoices
    : ITrigger<
        GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoices.
        GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput,
        GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoices.
        GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput>
{
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWabaService _wabaService;

    public GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoices(
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IWabaService wabaService)
    {
        _wabaService = wabaService;
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
    }

    public class GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("start", NullValueHandling = NullValueHandling.Include)]
        public DateTimeOffset? Start { get; set; }

        [JsonProperty("end", NullValueHandling = NullValueHandling.Include)]
        public DateTimeOffset? End { get; set; }

        [JsonProperty("limit")]
        [Range(1, 10000)]
        [Required]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput(
            string sleekflowCompanyId,
            DateTimeOffset? start,
            DateTimeOffset? end,
            int limit,
            string? continuationToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Start = start;
            End = end;
            Limit = limit;
            ContinuationToken = continuationToken;
        }
    }

    public class GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput
    {
        [JsonProperty("invoices")]
        public List<BusinessBalanceInvoice> BusinessBalanceInvoices { get; set; }

        [JsonProperty("next_continuation_token")]
        public string? NextContinuationToken { get; set; }

        [JsonProperty("count")]
        public int Count { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput(
            List<BusinessBalanceInvoice> businessBalanceInvoices,
            string? nextContinuationToken,
            int count)
        {
            BusinessBalanceInvoices = businessBalanceInvoices;
            NextContinuationToken = nextContinuationToken;
            Count = count;
        }
    }

    public async Task<GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput> F(
        GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesInput input)
    {
        var sleekflowCompanyId = input.SleekflowCompanyId;

        var wabas = await _wabaService.GetWabasAsync(sleekflowCompanyId);

        if (wabas.Count == 0)
        {
            return new GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput(
                new List<BusinessBalanceInvoice>(),
                null,
                0);
        }

        var facebookBusinessIdToWabasDict =
            wabas
                .Where(w=>w.FacebookBusinessId!= null)
                .GroupBy(w => w.FacebookBusinessId!)
                .ToDictionary(g => g.Key, g => g.ToList());

        if (facebookBusinessIdToWabasDict.Count == 0)
        {
            return new GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput(
                new List<BusinessBalanceInvoice>(),
                null,
                0);
        }

        var businessBalanceInvoicesTuple =
            await _businessBalanceTransactionLogService
                .GetBusinessBalanceStripeTopUpInvoicesByFacebookBusinessIdAsync(
                    facebookBusinessIdToWabasDict,
                    input.Start,
                    input.End,
                    input.ContinuationToken,
                    input.Limit);

        return new GetWhatsappCloudApiBusinessBalanceStripeTopUpInvoicesOutput(
            businessBalanceInvoicesTuple.Invoices,
            businessBalanceInvoicesTuple.NextContinuationToken,
            businessBalanceInvoicesTuple.TotalNumberOfBusinessBalanceInvoices);
    }
}