using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.AuditHub.Models.SystemAuditLogs.LogData;

[SwaggerInclude]
public class PiiMaskingConfigCreatedSystemLogData
{
    [JsonProperty("pii_masking_config_id")]
    public long PiiMaskingConfigId { get; set; }

    [JsonProperty("display_name")]
    public string DisplayName { get; set; }

    [JsonProperty("regex_patterns")]
    public List<string> RegexPatterns { get; set; }

    [Json<PERSON>roperty("masking_custom_object_schema_ids")]
    public List<string> MaskingCustomObjectSchemaIds { get; set; }

    [JsonProperty("masking_roles")]
    public int MaskingRoles { get; set; }

    [JsonProperty("masking_locations")]
    public int MaskingLocations { get; set; }

    [JsonProperty("is_platform_api_masked")]
    public bool IsPlatformApiMasked { get; set; }

    [JsonConstructor]
    public PiiMaskingConfigCreatedSystemLogData(
        long piiMaskingConfigId,
        string displayName,
        List<string> regexPatterns,
        List<string> maskingCustomObjectSchemaIds,
        int maskingRoles,
        int maskingLocations,
        bool isPlatformApiMasked)
    {
        PiiMaskingConfigId = piiMaskingConfigId;
        DisplayName = displayName;
        RegexPatterns = regexPatterns;
        MaskingCustomObjectSchemaIds = maskingCustomObjectSchemaIds;
        MaskingRoles = maskingRoles;
        MaskingLocations = maskingLocations;
        IsPlatformApiMasked = isPlatformApiMasked;
    }
}