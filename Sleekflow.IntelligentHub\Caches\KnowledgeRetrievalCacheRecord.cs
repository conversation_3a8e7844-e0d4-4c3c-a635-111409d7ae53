﻿using Microsoft.Extensions.VectorData;
using Newtonsoft.Json;

namespace Sleekflow.IntelligentHub.Caches;

public class KnowledgeRetrievalCacheRecord
{
    [JsonProperty("Id")]
    [VectorStoreKey]
    public string Id { get; set; }

    [VectorStoreData]
    [Json<PERSON>roperty("SleekflowCompanyId")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("Prompt")]
    [VectorStoreData]
    public string Prompt { get; set; }

    [VectorStoreData]
    [JsonProperty("Result")]
    public string Result { get; set; }

    [JsonProperty("Embedding")]
    [VectorStoreVector(Dimensions: 3072)]
    public ReadOnlyMemory<float> Embedding { get; set; }

    [VectorStoreData]
    [JsonProperty("CollaborationMode")]
    public string CollaborationMode { get; set; }
}