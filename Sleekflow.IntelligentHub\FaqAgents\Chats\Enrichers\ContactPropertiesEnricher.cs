using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;

/// <summary>
/// Represents a contact property that an agent is permitted to access.
/// </summary>
public class PermittedContactProperty
{
    /// <summary>
    /// The name of the contact property.
    /// </summary>
    [JsonProperty("name")]
    public string Name { get; set; }

    /// <summary>
    /// A description of the contact property.
    /// </summary>
    [JsonProperty("description")]
    public string Description { get; set; }

    /// <summary>
    /// Initializes a new instance of the <see cref="PermittedContactProperty"/> class.
    /// </summary>
    /// <param name="name">The name of the contact property.</param>
    /// <param name="description">A description of the contact property.</param>
    [JsonConstructor]
    public PermittedContactProperty(string name, string description)
    {
        Name = name;
        Description = description;
    }
}

/// <summary>
/// Enriches chat context with contact properties.
/// </summary>
public class ContactPropertiesEnricher : IChatHistoryEnricher
{
    private readonly ILogger<ContactPropertiesEnricher> _logger;
    private readonly List<PermittedContactProperty> _permittedContactProperties;

    /// <summary>
    /// Initializes a new instance of the <see cref="ContactPropertiesEnricher"/> class.
    /// </summary>
    /// <param name="logger">Logger for the enricher.</param>
    /// <param name="parameters">Configuration parameters from EnricherConfig.</param>
    public ContactPropertiesEnricher(
        ILogger<ContactPropertiesEnricher> logger,
        Dictionary<string, string> parameters)
    {
        _logger = logger;

        if (parameters.TryGetValue(
                ContactPropertiesEnricherConstants.PERMITTED_CONTACT_PROPERTIES_KEY,
                out var value))
        {
            try
            {
                _permittedContactProperties =
                    JsonConvert.DeserializeObject<List<PermittedContactProperty>>(value)
                    ?? [];
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to deserialize permitted contact properties from parameters");

                _permittedContactProperties = new List<PermittedContactProperty>();
            }
        }
        else
        {
            _permittedContactProperties = new List<PermittedContactProperty>();
        }
    }

    /// <summary>
    /// Gets the name of the section in the context message.
    /// </summary>
    public string GetContextSectionName() => EnricherSectionKeys.CONTACT_PROPERTIES_SECTION;

    public string GetContextSectionExplanation()
    {
        return
            $"{GetContextSectionName()} is a section that contains information about the contact properties of the customer stored in our system.";
    }

    /// <summary>
    /// Enriches chat context with contact properties.
    /// </summary>
    public Task<string> EnrichAsync(
        ReplyGenerationContext context,
        Dictionary<string, string>? contactProperties,
        Kernel kernel,
        CancellationToken cancellationToken = default)
    {
        if (contactProperties == null || !contactProperties.Any())
        {
            _logger.LogInformation("No contact properties available");
            return Task.FromResult("No contact properties available");
        }

        try
        {
            // Get permitted contact properties based on lead nurturing tools configuration
            var permittedContactProperties =
                GetPermittedContactProperties(contactProperties, _permittedContactProperties);

            if (permittedContactProperties.Count == 0)
            {
                _logger.LogInformation("No permitted contact properties found");
                return Task.FromResult("No permitted contact properties available");
            }

            var result = JsonConvert.SerializeObject(permittedContactProperties);
            _logger.LogInformation(
                "Successfully processed {Count} contact properties",
                permittedContactProperties.Count);

            return Task.FromResult(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enriching chat history with contact properties");
            return Task.FromResult("Failed to process contact properties");
        }
    }

    /// <summary>
    /// Filters contact properties based on permitted properties in lead nurturing tools.
    /// </summary>
    private Dictionary<string, string> GetPermittedContactProperties(
        Dictionary<string, string> contactProperties,
        List<PermittedContactProperty> permittedContactProperties)
    {
        _logger.LogInformation(
            "Filtering contact properties. Total: {Total}, Permitted: {PermittedCount}",
            contactProperties.Count,
            permittedContactProperties.Count);

        var filteredProperties = contactProperties
            .Where(cp => permittedContactProperties.Exists(p => p.Name == cp.Key))
            .ToDictionary(cp => cp.Key, cp => cp.Value);

        return filteredProperties;
    }
}