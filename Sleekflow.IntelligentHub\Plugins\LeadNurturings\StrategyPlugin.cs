using System.ComponentModel;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.FaqAgents.Chats;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;
using Sleekflow.IntelligentHub.Kernels;

namespace Sleekflow.IntelligentHub.Plugins.LeadNurturings;

public interface IStrategyPlugin
{
    [KernelFunction("define_strategy")]
    [Description(
        "Develops strategy using data from data pane and stores strategy results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with strategy details and knowledge requirements.")]
    Task<string> DefineStrategyWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where decision results are stored in the data pane.")]
        string decisionKey);
}

public class StrategyPlugin : BaseLeadNurturingPlugin, IStrategyPlugin
{
    public StrategyPlugin(
        ILogger<StrategyPlugin> logger,
        ILeadNurturingAgentDefinitions agentDefinitions,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        IAgentDurationTracker agentDurationTracker,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        Kernel kernel)
        : base(
            logger,
            agentDefinitions,
            promptExecutionSettingsService,
            agentDurationTracker,
            dataPane,
            keyManager,
            kernel)
    {
    }

    private async Task<string> DefineStrategyAsync(
        Kernel kernel,
        string conversationContext,
        string decisionInfo,
        string additionalInstructionStrategy = "")
    {
        var strategyAgent = _agentDefinitions.GetStrategyAgent(
            kernel,
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true),
            additionalInstructionStrategy);

        var agentThread = CreateAgentThread(conversationContext);
        AddContextToThread(agentThread, decisionInfo);

        return await ExecuteAgentWithTelemetryAsync(strategyAgent, agentThread, "StrategyPlugin");
    }

    [KernelFunction("define_strategy")]
    [Description(
        "Develops strategy using data from data pane and stores strategy results with structured keys for efficient workflow management.")]
    [return:
        Description("Original agent response with strategy details and knowledge requirements.")]
    public async Task<string> DefineStrategyWithKeyAsync(
        [Description("Session key for data isolation and management.")]
        string sessionKey,
        [Description("Data key where conversation context is stored in the data pane.")]
        string conversationContextKey,
        [Description("Data key where decision results are stored in the data pane.")]
        string decisionKey)
    {
        return await ExecutePluginOperationAsync<LeadNurturingAgentDefinitions.StrategyAgentResponse>(
            sessionKey,
            "Strategy",
            dataRetrievalFunc: async () =>
                await RetrieveDataWithConfiguration(sessionKey, conversationContextKey, decisionKey),
            agentExecutionFunc: async (data) => await DefineStrategyAsync(
                _kernel.Clone(),
                data["conversationContext"],
                data["decisionInfo"],
                data["additionalInstructionStrategy"]),
            storageKey: _keyManager.GetStrategyKey(sessionKey),
            storageDataType: AgentOutputKeys.StrategyComplete,
            outputInterceptorFunc: async (rawResult, parsedResponse) =>
                await StoreStrategyComponents(sessionKey, rawResult, parsedResponse)
        );
    }

    private async Task<Dictionary<string, string>> RetrieveDataWithConfiguration(
        string sessionKey,
        string conversationContextKey,
        string decisionKey)
    {
        var results = await RetrieveMultipleDataAsync(
            (conversationContextKey, AgentOutputKeys.Conversation, "conversationContext"),
            (decisionKey, AgentOutputKeys.DecisionComplete, "decisionInfo")
        );

        // Retrieve configuration
        results["additionalInstructionStrategy"] = await GetConfigurationAsync(
            sessionKey,
            AgentOutputKeys.AdditionalInstructionStrategy,
            "");

        return results;
    }

    private async Task StoreStrategyComponents(
        string sessionKey,
        string rawResult,
        LeadNurturingAgentDefinitions.StrategyAgentResponse? parsedResponse)
    {
        try
        {
            if (parsedResponse != null)
            {
                // Store individual components for easy access
                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "StrategyAgent", AgentOutputKeys.StrategyNeedKnowledge),
                    AgentOutputKeys.StrategyNeedKnowledge,
                    parsedResponse.NeedKnowledge ?? "");

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(
                        sessionKey,
                        "StrategyAgent",
                        AgentOutputKeys.StrategyNeedKnowledgeReasoning),
                    AgentOutputKeys.StrategyNeedKnowledgeReasoning,
                    parsedResponse.NeedKnowledgeReasoning);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "StrategyAgent", AgentOutputKeys.StrategyGuidance),
                    AgentOutputKeys.StrategyGuidance,
                    parsedResponse.Strategy);

                await _dataPane.StoreData(
                    _keyManager.GetAgentOutputKey(sessionKey, "StrategyAgent", AgentOutputKeys.StrategyReasoning),
                    AgentOutputKeys.StrategyReasoning,
                    parsedResponse.StrategyReasoning);

                _logger.LogInformation(
                    "Strategy development completed for session {SessionKey}. Need knowledge: {NeedKnowledge}",
                    sessionKey,
                    !string.IsNullOrEmpty(parsedResponse.NeedKnowledge));
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to store strategy components for session {SessionKey}", sessionKey);
        }
    }
}