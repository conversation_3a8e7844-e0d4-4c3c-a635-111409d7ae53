﻿using Newtonsoft.Json;
using Sleekflow.CrmHub.Models.Schemas.Properties.DataTypes;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Models.Schemas.Properties;

public sealed class PrimaryProperty : IProperty, IHasCreatedAt
{
    public const string PropertyNamePrimaryPropertyConfig = "primary_property_config";

    [JsonProperty(Entity.PropertyNameId)]
    public string Id { get; }

    [JsonProperty(IProperty.PropertyNameDisplayName)]
    public string DisplayName { get; set; }

    [JsonProperty(IProperty.PropertyNameUniqueName)]
    public string UniqueName { get; set; }

    [JsonProperty(IProperty.PropertyNameDataType)]
    public IDataType DataType { get; }

    [JsonProperty(IProperty.PropertyNameIsVisible)]
    public bool IsVisible { get; set; }

    [JsonProperty(IProperty.PropertyNameIsPinned)]
    public bool IsPinned { get; set; }

    [JsonProperty(IProperty.PropertyNameIsSearchable)]
    public bool IsSearchable { get; set; }

    [JsonProperty(PropertyNamePrimaryPropertyConfig)]
    public PrimaryPropertyConfig PrimaryPropertyConfig { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasCreatedBy.PropertyNameCreatedBy)]
    public AuditEntity.SleekflowStaff? CreatedBy { get; set; }

    [JsonConstructor]
    public PrimaryProperty(
        string id,
        string displayName,
        string uniqueName,
        IDataType dataType,
        bool isVisible,
        bool isPinned,
        bool isSearchable,
        PrimaryPropertyConfig primaryPropertyConfig,
        DateTimeOffset createdAt,
        AuditEntity.SleekflowStaff? createdBy)
    {
        Id = id;
        DisplayName = displayName;
        UniqueName = uniqueName;
        DataType = dataType;
        IsVisible = isVisible;
        IsPinned = isPinned;
        IsSearchable = isSearchable;
        PrimaryPropertyConfig = primaryPropertyConfig;
        CreatedAt = createdAt;
        CreatedBy = createdBy;
    }
}