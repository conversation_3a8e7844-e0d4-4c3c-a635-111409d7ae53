using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.EmailSentRecords;

namespace Sleekflow.FlowHub.EmailSentRecords;

public interface IEmailSentRecordService
{
    Task<bool> IsEmailSentAsync(string id);

    Task<EmailSentRecord> CreateRecordAsync(string id, object? data = null, TimeSpan? expiration = null);
}

public class EmailSentRecordService : IEmailSentRecordService, IScopedService
{
    private readonly IEmailSentRecordRepository _emailSentRecordRepository;

    public EmailSentRecordService(IEmailSentRecordRepository emailSentRecordRepository)
    {
        _emailSentRecordRepository = emailSentRecordRepository;
    }

    public async Task<bool> IsEmailSentAsync(string id)
    {
        var entity = await _emailSentRecordRepository.GetObjectsAsync(x => x.Id == id);
        return entity.Count > 0;
    }

    public async Task<EmailSentRecord> CreateRecordAsync(string id, object? data = null, TimeSpan? expiration = null)
    {
        var now = DateTimeOffset.UtcNow;
        expiration ??= now.AddMonths(1) - now;
        var ttl = (int) Math.Ceiling(expiration.Value.TotalSeconds);

        var entity = new EmailSentRecord(id, data, ttl);

        return await _emailSentRecordRepository.CreateAndGetAsync(entity, id);
    }
}