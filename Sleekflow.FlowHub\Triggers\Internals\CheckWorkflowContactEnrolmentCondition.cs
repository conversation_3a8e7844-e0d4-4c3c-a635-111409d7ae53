﻿using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Grains;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Workflows;

namespace Sleekflow.FlowHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class CheckWorkflowContactEnrolmentCondition : ITrigger<
    CheckWorkflowContactEnrolmentCondition.CheckWorkflowContactEnrolmentConditionInput,
    CheckWorkflowContactEnrolmentCondition.CheckWorkflowContactEnrolmentConditionOutput>
{
    private readonly IWorkflowService _workflowService;
    private readonly IWorkflowEnrolmentConditionEvaluator _workflowEnrolmentConditionEvaluator;

    public CheckWorkflowContactEnrolmentCondition(
        IWorkflowService workflowService,
        IWorkflowEnrolmentConditionEvaluator workflowEnrolmentConditionEvaluator)
    {
        _workflowService = workflowService;
        _workflowEnrolmentConditionEvaluator = workflowEnrolmentConditionEvaluator;
    }

    public class CheckWorkflowContactEnrolmentConditionInput : Sleekflow.FlowHub.Models.Internals.CheckWorkflowContactEnrolmentConditionInput
    {
        [JsonConstructor]
        public CheckWorkflowContactEnrolmentConditionInput(
            string workflowId,
            string workflowVersionedId,
            string sleekflowCompanyId,
            string contactId,
            ContactDetail contactDetail,
            string? condition,
            string? workflowName)
        : base(
            workflowId,
            workflowVersionedId,
            sleekflowCompanyId,
            contactId,
            contactDetail,
            condition,
            workflowName)
        {
        }
    }

    public class CheckWorkflowContactEnrolmentConditionOutput : Sleekflow.FlowHub.Models.Internals.CheckWorkflowContactEnrolmentConditionOutput
    {
        [JsonConstructor]
        public CheckWorkflowContactEnrolmentConditionOutput(bool enrolmentConditionSatisfied)
            : base(enrolmentConditionSatisfied)
        {
        }
    }

    public async Task<CheckWorkflowContactEnrolmentConditionOutput> F(CheckWorkflowContactEnrolmentConditionInput input)
    {
        bool enrolmentConditionSatisfied;
        if (input is { Condition: not null, WorkflowName: not null })
        {
            enrolmentConditionSatisfied =
                await _workflowEnrolmentConditionEvaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
                    input.Condition,
                    input.WorkflowName,
                    input.ContactDetail);
        }
        else
        {
            var proxyWorkflow = await _workflowService.GetLatestWorkflowAsync(
                input.WorkflowId,
                input.SleekflowCompanyId);

            enrolmentConditionSatisfied =
                await _workflowEnrolmentConditionEvaluator.EvaluateScheduledWorkflowEnrolmentConditionAsync(
                    proxyWorkflow,
                    input.ContactDetail);
        }

        /* not to use this for storing contact data into workflow level grains due to table storage data size limit & dynamic contact update use case in the future
        if (enrolmentConditionSatisfied)
        {
            var workflowContactGrain = _grainFactory.GetGrain<IWorkflowGrain>(
                input.WorkflowVersionedId + "_contacts");

            await workflowContactGrain.SetAsync(
                "sleekflow_company_id",
                input.SleekflowCompanyId);

            await workflowContactGrain.SetAsync(input.ContactId, input.ContactDetail);
        }
        */

        return new CheckWorkflowContactEnrolmentConditionOutput(
            enrolmentConditionSatisfied);
    }
}