using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Commons.Workflows;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Workflows.Settings;

namespace Sleekflow.FlowHub.Tests.Workflows;

public class WorkflowRecurringSettingsV2ParserTest
{
    private WorkflowRecurringSettingsV2Parser _parser;

    [SetUp]
    public void Setup()
    {
        _parser = new WorkflowRecurringSettingsV2Parser(new Logger<WorkflowRecurringSettingsV2Parser>(new LoggerFactory()));
    }


    [Test]
    public void GetNextOccurrenceAfter_Daily_ShouldReturnNextDay()
    {
        var firstOccurrence = new DateTimeOffset(2023, 1, 1, 7, 0, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2023, 1, 2, 10, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Daily, Day = 3
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2023, 1, 4, 7, 0, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Daily_ShouldReturnNextDay_When_Today_Eclipse()
    {
        var firstOccurrence = new DateTimeOffset(2023, 1, 1, 7, 0, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2023, 1, 2, 10, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Daily, Day = 1
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2023, 1, 3, 7, 0, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Weekly_ShouldReturnNextWeek()
    {
        var firstOccurrence = new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero); // Sunday
        var currentTime = new DateTimeOffset(2023, 1, 2, 0, 0, 0, TimeSpan.Zero); // Monday
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Weekly, Week = 1, RepeatOnWeekly = "default"
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2023, 1, 8, 0, 0, 0, TimeSpan.Zero), result); // Next Sunday
    }

    [Test]
    public void GetNextOccurrenceAfter_Long_Period_ShouldReturnNextLongPeriod()
    {
        var firstOccurrence = new DateTimeOffset(1990, 1, 31, 12, 21, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2025, 5, 29, 10, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Weekly, Week = 1, RepeatOnWeekly = "specific", RepeatOnWeeklySpecificDay = ["monday", "wednesday"]
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2025, 6, 2, 12, 21, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_This_Peroid_Eclapsed()
    {
        var firstOccurrence = new DateTimeOffset(2025, 5, 5, 12, 21, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2025, 5, 22, 23, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Weekly, Week = 2, RepeatOnWeekly = "specific", RepeatOnWeeklySpecificDay = ["tuesday", "wednesday"]
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2025, 6, 3, 12, 21, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Monthly_ShouldReturnNextMonth()
    {
        var firstOccurrence = new DateTimeOffset(2023, 1, 13, 0, 0, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2023, 2, 1, 0, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Monthly, Month = 2, RepeatOnMonthly = 15
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2023, 3, 15, 0, 0, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Yearly_ShouldReturnNextYear()
    {
        var firstOccurrence = new DateTimeOffset(2023, 1, 1, 0, 0, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2024, 6, 1, 0, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Yearly, Year = 1
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2025, 1, 1, 0, 0, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Long_Period_Month_ShouldReturnNextLongPeriod()
    {
        var firstOccurrence = new DateTimeOffset(1992, 2, 29, 12, 21, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2025, 5, 29, 10, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Monthly, Month = 2, RepeatOnMonthly = 10
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2025, 6, 10, 12, 21, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Edge_Case_Invalid_ShouldReturnNull_2_29()
    {
        var firstOccurrence = new DateTimeOffset(1992, 2, 29, 8, 21, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2025, 6, 29, 10, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Monthly, Month = 2, RepeatOnMonthly = 29
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2025, 8, 29, 8, 21, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Gap_Year()
    {
        var firstOccurrence = new DateTimeOffset(1992, 2, 29, 12, 21, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2025, 5, 29, 10, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Yearly, Year = 1
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(new DateTimeOffset(2028, 2, 29, 12, 21, 0, TimeSpan.Zero), result);
    }

    [Test]
    public void GetNextOccurrenceAfter_Should_Return_Null_When_A_UnExisted_Date()
    {
        var firstOccurrence = new DateTimeOffset(1992, 2, 29, 12, 21, 0, TimeSpan.Zero);
        var currentTime = new DateTimeOffset(2025, 5, 29, 10, 0, 0, TimeSpan.Zero);
        var settings = new WorkflowRecurringSettings
        {
            RecurringType = WorkflowRecurringTypes.Monthly, RepeatOnMonthly = 31, Month = 12
        };

        var result = _parser.GetNextOccurrenceAfter(firstOccurrence, settings, currentTime);
        Assert.AreEqual(DateTimeOffset.MaxValue, result);
    }
}