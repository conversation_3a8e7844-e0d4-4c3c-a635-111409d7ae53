using GraphApi.Client.Models.WebhookObjects;
using Newtonsoft.Json;
using Sleekflow.Attributes;

namespace Sleekflow.MessagingHub.Models.Webhooks.WhatsappCloudApis;

public class WhatsappCloudApiWebhookTravisMessage
{
    [JsonProperty("waba_id")]
    public string WabaId { get; set; }

    [JsonProperty("waba_phone_number")]
    public string WabaPhoneNumberId { get; set; }

    [JsonProperty("value")]
    public CloudApiWebhookValueObject Value { get; set; }

    public WhatsappCloudApiWebhookTravisMessage(
        string wabaId,
        string wabaPhoneNumberId,
        CloudApiWebhookValueObject value)
    {
        WabaId = wabaId;
        WabaPhoneNumberId = wabaPhoneNumberId;
        Value = value;
    }
}