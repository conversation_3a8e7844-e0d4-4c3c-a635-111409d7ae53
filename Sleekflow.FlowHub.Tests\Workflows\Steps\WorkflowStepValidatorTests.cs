﻿using Newtonsoft.Json;
using Sleekflow.Exceptions;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps;
using Sleekflow.FlowHub.Models.Steps.Abstractions;
using Sleekflow.FlowHub.Models.Steps.Calls;
using Sleekflow.FlowHub.Models.Steps.Common;
using Sleekflow.FlowHub.Workflows;

#pragma warning disable CS8604 // Possible null reference argument.

namespace Sleekflow.FlowHub.Tests.Workflows.Steps;

public class WorkflowStepValidatorTests
{
    private readonly IWorkflowStepValidator _workflowStepValidator =
        new WorkflowStepValidator(new WorkflowStepEntryProvider());

    #region Assert all steps are valid

    [Test]
    public void AssertAllStepsAreValidAsync_GivenNullStepList_ShouldPassValidation()
    {
        // Arrange
        List<Step>? steps = null;

        // Act & Assert
        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertAllStepsAreValidAsync(steps, WorkflowScheduleTypes.None, null);
            });
    }

    [Test]
    public void AssertAllStepsAreValidAsync_GivenEmptyStepList_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ();

        // Act & Assert
        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertAllStepsAreValidAsync(steps, WorkflowScheduleTypes.None, null);
            });
    }

    [Test]
    public void AssertAllStepsAreValidAsync_GivenValidStepList_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new SwitchStep(
                "step-2",
                "condition-branching",
                null,
                null,
                new ()
                {
                    new (
                        "step-2 condition 1 id",
                        "step-2 condition 1 name",
                        "step-2 condition 1",
                        new List<ConditionCriterion>(),
                        "step-3"),
                    new (
                        "step-2 condition 2 id",
                        "step-2 condition 2 name",
                        "step-2 condition 2",
                        new List<ConditionCriterion>(),
                        "end")
                }),
            new TryCatchStep(
                "step-3",
                "http-try-catch",
                null,
                "end",
                new TryCatchStepTry(
                    new CallStep<HttpGetStepArgs>(
                        "step-3-try",
                        "wait-for-get-request",
                        new Assign
                        {
                            {
                                "Symbol",
                                "{{ (sys_var_dict[\\\"a3df0430-23bc-4ba5-a7ab-a66d93063731\\\"] | json.deserialize)[\\\"USD\\\"][\\\"Symbol\\\"] }}"
                            }
                        },
                        null,
                        "http.get",
                        new (
                            "{{ https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json }}",
                            null))),
                new TryCatchStepCatch(
                    "get-request-error",
                    new SimpleStep(
                        "step-3-catch",
                        "failed-get-request",
                        new Assign
                        {
                            {
                                "error_step-3-catch", "some error"
                            }
                        },
                        null))),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act & Assert
        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertAllStepsAreValidAsync(steps, WorkflowScheduleTypes.None, null);
            });
    }

    [Test]
    public void
        AssertAllStepsAreValidAsync_Given_WorkflowScheduleTypeIsContactPropertyBasedDateTime_And_HasRequiredSleepStep_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new CallStep<ScheduledTriggerConditionsCheckStepArgs>(
                "enrollment-conditions-check",
                "enrollment-conditions-check",
                null,
                null,
                ScheduledTriggerConditionsCheckStepArgs.CallName,
                new ScheduledTriggerConditionsCheckStepArgs("{{ true }}")),
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new SwitchStep(
                "step-2",
                "condition-branching",
                null,
                null,
                new ()
                {
                    new (
                        "step-2 condition 1 id",
                        "step-2 condition 1 name",
                        "step-2 condition 1",
                        new List<ConditionCriterion>(),
                        "step-3"),
                    new (
                        "step-2 condition 2 id",
                        "step-2 condition 2 name",
                        "step-2 condition 2",
                        new List<ConditionCriterion>(),
                        "end")
                }),
            new TryCatchStep(
                "step-3",
                "http-try-catch",
                null,
                "end",
                new TryCatchStepTry(
                    new CallStep<HttpGetStepArgs>(
                        "step-3-try",
                        "wait-for-get-request",
                        new Assign
                        {
                            {
                                "Symbol",
                                "{{ (sys_var_dict[\\\"a3df0430-23bc-4ba5-a7ab-a66d93063731\\\"] | json.deserialize)[\\\"USD\\\"][\\\"Symbol\\\"] }}"
                            }
                        },
                        null,
                        "http.get",
                        new (
                            "{{ https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json }}",
                            null))),
                new TryCatchStepCatch(
                    "get-request-error",
                    new SimpleStep(
                        "step-3-catch",
                        "failed-get-request",
                        new Assign
                        {
                            {
                                "error_step-3-catch", "some error"
                            }
                        },
                        null))),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act & Assert
        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertAllStepsAreValidAsync(steps, WorkflowScheduleTypes.None, null);
            });
    }

    [Test]
    [TestCase((string) WorkflowScheduleTypes.PredefinedDateTime)]
    [TestCase((string) WorkflowScheduleTypes.ContactPropertyBasedDateTime)]
    [TestCase((string) WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime)]
    public void
        AssertAllStepsAreValidAsync_Given_WorkflowIsOfScheduleType_And_MissingRequiredSleepStep_ShouldFailValidation(
            string workflowScheduleType)
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new SwitchStep(
                "step-2",
                "condition-branching",
                null,
                null,
                new ()
                {
                    new (
                        "step-2 condition 1 id",
                        "step-2 condition 1 name",
                        "step-2 condition 1",
                        new List<ConditionCriterion>(),
                        "step-3"),
                    new (
                        "step-2 condition 2 id",
                        "step-2 condition 2 name",
                        "step-2 condition 2",
                        new List<ConditionCriterion>(),
                        "end")
                }),
            new TryCatchStep(
                "step-3",
                "http-try-catch",
                null,
                "end",
                new TryCatchStepTry(
                    new CallStep<HttpGetStepArgs>(
                        "step-3-try",
                        "wait-for-get-request",
                        new Assign
                        {
                            {
                                "Symbol",
                                "{{ (sys_var_dict[\\\"a3df0430-23bc-4ba5-a7ab-a66d93063731\\\"] | json.deserialize)[\\\"USD\\\"][\\\"Symbol\\\"] }}"
                            }
                        },
                        null,
                        "http.get",
                        new (
                            "{{ https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json }}",
                            null))),
                new TryCatchStepCatch(
                    "get-request-error",
                    new SimpleStep(
                        "step-3-catch",
                        "failed-get-request",
                        new Assign
                        {
                            {
                                "error_step-3-catch", "some error"
                            }
                        },
                        null))),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertAllStepsAreValidAsync(steps, workflowScheduleType, null);
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo(
                        $"Schedule based workflow must have exactly one call step that invokes '{ScheduledTriggerConditionsCheckStepArgs.CallName}'"));
            });
    }

    #endregion Assert all steps are valid

    #region Step ids uniqueness

    [Test]
    public void AssertStepIdsUniquenessAsync_GivenNullStepList_ShouldPassValidation()
    {
        List<Step>? steps = null;

        EnsureUniqueIdsValidationPassed(steps);
    }

    [Test]
    public void AssertStepIdsUniquenessAsync_GivenEmptyStepList_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ();

        // Act & Assert
        EnsureUniqueIdsValidationPassed(steps);
    }

    [Test]
    public void AssertStepIdsUniquenessAsync_GivenStepList_HasNoNestedSteps_And_HasNoDuplicateIds_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SimpleStep(
                "step-2",
                "step 2",
                null,
                null)
        };

        // Act & Assert
        EnsureUniqueIdsValidationPassed(steps);
    }

    [Test]
    public void AssertStepIdsUniquenessAsync_GivenStepList_HasNoNestedSteps_And_HasDuplicateIds_ShouldFailValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SimpleStep(
                "step-2",
                "step 2",
                null,
                "step-3"),
            new SimpleStep(
                "step-2", // duplicate
                "step 3",
                null,
                null)
        };

        // Act & Assert
        EnsureDuplicateIdsValidationFailed(
            steps,
            new ()
            {
                "step-2"
            });
    }

    [Test]
    public void AssertStepIdsUniquenessAsync_GivenStepList_HasNoNestedSteps_And_HasEmptyId_ShouldFailValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SimpleStep(
                "step-2",
                "step 2",
                null,
                "step-3"),
            new SimpleStep(
                string.Empty,
                "step 3",
                null,
                null)
        };

        // Act & Assert
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertStepIdsUniquenessAsync(steps);
            });

        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo($"One or more steps have empty '{nameof(Step.Id)}' field"));
            });
    }

    [Test]
    public void
        AssertStepIdsUniquenessAsync_GivenStepList_HasNestedSteps_And_HasMultipleDuplicateIds_ShouldFailValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SubFlowStep(
                "step-2",
                "step 2",
                null,
                null,
                new ()
                {
                    new SimpleStep(
                        "step-2", // Duplicate
                        "step 2.1",
                        null,
                        null),
                    new SubFlowStep(
                        "step-2.2.1",
                        "step 2.2.1",
                        null,
                        null,
                        new ()
                        {
                            new SimpleStep(
                                "step-2.2.1", // Duplicate
                                "step *******",
                                null,
                                null)
                        })
                })
        };

        // Act & Assert
        EnsureDuplicateIdsValidationFailed(
            steps,
            new ()
            {
                "step-2", "step-2.2.1"
            });
    }

    [Test]
    public void
        AssertStepIdsUniquenessAsync_GivenStepList_HasNestedParallelSteps_And_HasNoDuplicateIds_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new ParallelStep(
                "step-2",
                "step 2",
                null,
                null,
                new ()
                {
                    new ParallelStepBranch(
                        new SimpleStep(
                            "step-2.1",
                            "step 2.1",
                            null,
                            null)),
                    new ParallelStepBranch(
                        new SimpleStep(
                            "step-2.2",
                            "step 2.2",
                            null,
                            null)),
                    new ParallelStepBranch(
                        new SubFlowStep(
                            "step-2.3",
                            "step 3.3",
                            null,
                            null,
                            new ()
                            {
                                new SimpleStep(
                                    "step-2.3.1",
                                    "step 2.3.1",
                                    null,
                                    null),
                                new SimpleStep(
                                    "step-2.3.2",
                                    "step 2.3.2",
                                    null,
                                    null)
                            }))
                })
        };

        // Act & Assert
        EnsureUniqueIdsValidationPassed(steps);
    }

    [Test]
    public void
        AssertStepIdsUniquenessAsync_GivenStepList_HasNestedParallelSteps_And_HasDuplicateIds_ShouldFailValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new ParallelStep(
                "step-2",
                "step 2",
                null,
                null,
                new ()
                {
                    new ParallelStepBranch(
                        new SimpleStep(
                            "step-2.1",
                            "step 2.1",
                            null,
                            null)),
                    new ParallelStepBranch(
                        new SubFlowStep(
                            "step-2.2",
                            "step 2.2",
                            null,
                            null,
                            new ()
                            {
                                new SimpleStep(
                                    "step-2.2.1",
                                    "step 2.2.1",
                                    null,
                                    null),
                                new SimpleStep(
                                    "step-2.2.1", // Duplicate
                                    "step 2.2.2",
                                    null,
                                    null)
                            }))
                })
        };

        // Act & Assert
        EnsureDuplicateIdsValidationFailed(
            steps,
            new ()
            {
                "step-2.2.1"
            });
    }

    [Test]
    public void
        AssertStepIdsUniquenessAsync_GivenStepList_HasNestedSubFlowSteps_And_HasNoDuplicateIds_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SubFlowStep(
                "step-2",
                "step 2",
                null,
                null,
                new ()
                {
                    new SimpleStep(
                        "step-2.1",
                        "step 2.1",
                        null,
                        null),
                    new SubFlowStep(
                        "step-2.2.1",
                        "step 2.2.1",
                        null,
                        null,
                        new ()
                        {
                            new SimpleStep(
                                "step-*******",
                                "step *******",
                                null,
                                null)
                        })
                })
        };

        // Act & Assert
        EnsureUniqueIdsValidationPassed(steps);
    }

    [Test]
    public void
        AssertStepIdsUniquenessAsync_GivenStepList_HasNestedSubFlowSteps_And_HasDuplicateIds_ShouldFailValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SubFlowStep(
                "step-2",
                "step 2",
                null,
                null,
                new ()
                {
                    new SimpleStep(
                        "step-2.1",
                        "step 2.1",
                        null,
                        null),
                    new SubFlowStep(
                        "step-2.2.1",
                        "step 2.2.1",
                        null,
                        null,
                        new ()
                        {
                            new SimpleStep(
                                "step-2.2.1", // Duplicate
                                "step *******",
                                null,
                                null)
                        })
                })
        };

        // Act & Assert
        EnsureDuplicateIdsValidationFailed(
            steps,
            new ()
            {
                "step-2.2.1"
            });
    }

    [Test]
    public void
        AssertStepIdsUniquenessAsync_GivenStepList_HasNestedTryCatchStep_And_HasNoDuplicateIds_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SubFlowStep(
                "step-2",
                "step 2",
                null,
                null,
                new ()
                {
                    new SimpleStep(
                        "step-2.1",
                        "step 2.1",
                        null,
                        null),
                    new TryCatchStep(
                        "step-2.2.1",
                        "step 2.2.1",
                        null,
                        null,
                        new TryCatchStepTry(
                            new LogStep(
                                "step-2.2.1-log",
                                "step 2.2.1 log",
                                null,
                                null,
                                "log message",
                                "Information")),
                        new TryCatchStepCatch(
                            "catch",
                            new LogStep(
                                "step-2.2.1-catch",
                                "step 2.2.1 catch",
                                null,
                                null,
                                "log error message",
                                "Error")))
                })
        };

        // Act & Assert
        EnsureUniqueIdsValidationPassed(steps);
    }

    [Test]
    public void
        AssertStepIdsUniquenessAsync_GivenStepList_HasNestedTryCatchStep_And_HasDuplicateIds_ShouldFailValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                "step-2"),
            new SubFlowStep(
                "step-2",
                "step 2",
                null,
                null,
                new ()
                {
                    new SimpleStep(
                        "step-2.1",
                        "step 2.1",
                        null,
                        null),
                    new TryCatchStep(
                        "step-2.2.1",
                        "step 2.2.1",
                        null,
                        null,
                        new TryCatchStepTry(
                            new LogStep(
                                "step-2.2.1-log",
                                "step 2.2.1 log",
                                null,
                                null,
                                "log message",
                                "Information")),
                        new TryCatchStepCatch(
                            "catch",
                            new LogStep(
                                "step-2.2.1", // duplicate
                                "step 2.2.1 catch",
                                null,
                                null,
                                "log error message",
                                "Error")))
                })
        };

        // Act & Assert
        EnsureDuplicateIdsValidationFailed(
            steps,
            new ()
            {
                "step-2.2.1"
            });
    }

    private void EnsureUniqueIdsValidationPassed(List<Step> steps)
    {
        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertStepIdsUniquenessAsync(steps);
            });
    }

    private void EnsureDuplicateIdsValidationFailed(List<Step> steps, List<string> duplicateIds)
    {
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertStepIdsUniquenessAsync(steps);
            });

        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo(
                        $"Two or more steps have duplicate values for '{nameof(Step.Id)}'. Duplicate step ids: {JsonConvert.SerializeObject(duplicateIds)}"));
            });
    }

    #endregion Step ids uniqueness

    #region Next step id depth and existence

    [Test]
    public void AssertValidNextStepAsync_GivenNullStepList_ShouldPassValidation()
    {
        List<Step>? steps = null;

        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });
    }

    [Test]
    public void AssertValidNextStepAsync_GivenEmptyStepList_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ();

        // Act & Assert
        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });
    }

    [Test]
    public void AssertValidNextStepAsync_GivenAllStepsAreValid_ShouldPassValidation()
    {
        // Arrange
        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new SwitchStep(
                "step-2",
                "condition-branching",
                null,
                null,
                new ()
                {
                    new (
                        "step-2 condition 1 id",
                        "step-2 condition 1 name",
                        "step-2 condition 1",
                        new List<ConditionCriterion>(),
                        "step-3"),
                    new (
                        "step-2 condition 2 id",
                        "step-2 condition 2 name",
                        "step-2 condition 2",
                        new List<ConditionCriterion>(),
                        "end")
                }),
            new TryCatchStep(
                "step-3",
                "http-try-catch",
                null,
                "end",
                new TryCatchStepTry(
                    new CallStep<HttpGetStepArgs>(
                        "step-3-try",
                        "wait-for-get-request",
                        new Assign
                        {
                            {
                                "Symbol",
                                "{{ (sys_var_dict[\\\"a3df0430-23bc-4ba5-a7ab-a66d93063731\\\"] | json.deserialize)[\\\"USD\\\"][\\\"Symbol\\\"] }}"
                            }
                        },
                        null,
                        "http.get",
                        new (
                            "{{ https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json }}",
                            null))),
                new TryCatchStepCatch(
                    "get-request-error",
                    new SimpleStep(
                        "step-3-catch",
                        "failed-get-request",
                        new Assign
                        {
                            {
                                "error_step-3-catch", "some error"
                            }
                        },
                        null))),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act & Assert
        Assert.DoesNotThrowAsync(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });
    }

    [Test]
    public void AssertValidNextStepAsync_GivenOneStepHasNonExistentNextStep_ShouldFailValidation()
    {
        // Arrange
        var invalidStepIds = new List<string>()
        {
            "step-3",
        };

        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new SwitchStep(
                "step-2",
                "condition-branching",
                null,
                null,
                new ()
                {
                    new (
                        "step-2 condition 1 id",
                        "step-2 condition 1 name",
                        "step-2 condition 1",
                        new List<ConditionCriterion>(),
                        "step-3"),
                    new (
                        "step-2 condition 2 id",
                        "step-2 condition 2 name",
                        "step-2 condition 2",
                        new List<ConditionCriterion>(),
                        "end")
                }),
            new TryCatchStep(
                "step-3",
                "http-try-catch",
                null,
                "step-4", // Non-existent step
                new TryCatchStepTry(
                    new CallStep<HttpGetStepArgs>(
                        "step-3-try",
                        "wait-for-get-request",
                        new Assign
                        {
                            {
                                "Symbol",
                                "{{ (sys_var_dict[\\\"a3df0430-23bc-4ba5-a7ab-a66d93063731\\\"] | json.deserialize)[\\\"USD\\\"][\\\"Symbol\\\"] }}"
                            }
                        },
                        null,
                        "http.get",
                        new (
                            "{{ https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json }}",
                            null))),
                new TryCatchStepCatch(
                    "get-request-error",
                    new SimpleStep(
                        "step-3-catch",
                        "failed-get-request",
                        new Assign
                        {
                            {
                                "error_step-3-catch", "some error"
                            }
                        },
                        null))),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo(
                        $"One or more steps have invalid next step that is either non-existent or not in the same depth. Problematic step ids: {JsonConvert.SerializeObject(invalidStepIds)}"));
            });
    }

    [Test]
    public void AssertValidNextStepAsync_GivenMultipleStepsHaveNonExistentNextStep_ShouldFailValidation()
    {
        // Arrange
        var invalidStepIds = new List<string>()
        {
            "step-2", "step-3"
        };

        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new SwitchStep(
                "step-2",
                "condition-branching",
                null,
                "step-4", // Non-existent step
                new ()
                {
                    new (
                        "step-2 condition 1 id",
                        "step-2 condition 1 name",
                        "step-2 condition 1",
                        new List<ConditionCriterion>(),
                        "step-3"),
                    new (
                        "step-2 condition 2 id",
                        "step-2 condition 2 name",
                        "step-2 condition 2",
                        new List<ConditionCriterion>(),
                        "step-5") // Non-existent step
                }),
            new TryCatchStep(
                "step-3",
                "http-try-catch",
                null,
                "step-4", // Non-existent step
                new TryCatchStepTry(
                    new CallStep<HttpGetStepArgs>(
                        "step-3-try",
                        "wait-for-get-request",
                        new Assign
                        {
                            {
                                "Symbol",
                                "{{ (sys_var_dict[\\\"a3df0430-23bc-4ba5-a7ab-a66d93063731\\\"] | json.deserialize)[\\\"USD\\\"][\\\"Symbol\\\"] }}"
                            }
                        },
                        null,
                        "http.get",
                        new (
                            "{{ https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json }}",
                            null))),
                new TryCatchStepCatch(
                    "get-request-error",
                    new SimpleStep(
                        "step-3-catch",
                        "failed-get-request",
                        new Assign
                        {
                            {
                                "error_step-3-catch", "some error"
                            }
                        },
                        null))),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo(
                        $"One or more steps have invalid next step that is either non-existent or not in the same depth. Problematic step ids: {JsonConvert.SerializeObject(invalidStepIds)}"));
            });
    }

    [Test]
    public void AssertValidNextStepAsync_GivenSwitchCaseStepHasNextStepInDifferentDepth_ShouldFailValidation()
    {
        // Arrange
        var invalidStepIds = new List<string>()
        {
            "step-2",
        };

        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new SwitchStep(
                "step-2",
                "condition-branching",
                null,
                null,
                new ()
                {
                    new (
                        "step-2 condition 1 id",
                        "step-2 condition 1 name",
                        "step-2 condition 1",
                        new List<ConditionCriterion>(),
                        "step-3-try"), // Next step invalid depth
                    new (
                        "step-2 condition 2 id",
                        "step-2 condition 2 name",
                        "step-2 condition 2",
                        new List<ConditionCriterion>(),
                        "end")
                }),
            new TryCatchStep(
                "step-3",
                "http-try-catch",
                null,
                "end",
                new TryCatchStepTry(
                    new CallStep<HttpGetStepArgs>(
                        "step-3-try",
                        "wait-for-get-request",
                        new Assign
                        {
                            {
                                "Symbol",
                                "{{ (sys_var_dict[\\\"a3df0430-23bc-4ba5-a7ab-a66d93063731\\\"] | json.deserialize)[\\\"USD\\\"][\\\"Symbol\\\"] }}"
                            }
                        },
                        null,
                        "http.get",
                        new (
                            "{{ https://gist.githubusercontent.com/ksafranski/2973986/raw/5fda5e87189b066e11c1bf80bbfbecb556cf2cc1/Common-Currency.json }}",
                            null))),
                new TryCatchStepCatch(
                    "get-request-error",
                    new SimpleStep(
                        "step-3-catch",
                        "failed-get-request",
                        new Assign
                        {
                            {
                                "error_step-3-catch", "some error"
                            }
                        },
                        null))),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo(
                        $"One or more steps have invalid next step that is either non-existent or not in the same depth. Problematic step ids: {JsonConvert.SerializeObject(invalidStepIds)}"));
            });
    }

    [Test]
    public void AssertValidNextStepAsync_GivenParallelStepHasNextStepInDifferentDepth_ShouldFailValidation()
    {
        // Arrange
        var invalidStepIds = new List<string>()
        {
            "step-2.3",
        };

        List<Step> steps = new ()
        {
            new SimpleStep(
                "step-1",
                "step 1",
                null,
                null),
            new ParallelStep(
                "step-2",
                "step 2",
                null,
                "step-2-end",
                new ()
                {
                    new ParallelStepBranch(
                        new SimpleStep(
                            "step-2.1",
                            "step 2.1",
                            null,
                            null)),
                    new ParallelStepBranch(
                        new SwitchStep(
                            "step-2.2",
                            "step 2.2",
                            null,
                            null,
                            new ()
                            {
                                new SwitchStepCase(
                                    "step-2.3 id",
                                    "step-2.3 name",
                                    "{{ (math.random 1 11) > 5 }}",
                                    new List<ConditionCriterion>(),
                                    "step-2.3"),
                                new SwitchStepCase(
                                    "step-2.4 id",
                                    "step-2.4 name",
                                    "{{ true }}",
                                    new List<ConditionCriterion>(),
                                    "step-2.4")
                            })),
                    new ParallelStepBranch(
                        new SimpleStep(
                            "step-2.3",
                            "step 2.3",
                            null,
                            "step-2-end")), // Next step invalid depth
                    new ParallelStepBranch(
                        new SimpleStep(
                            "step-2.4",
                            "step 2.4",
                            null,
                            null))
                }),
            new SimpleStep(
                "step-2-end",
                "redirect-to-end",
                null,
                "end"),
            new SimpleStep(
                "end",
                "end",
                null,
                null)
        };

        // Act
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo(
                        $"One or more steps have invalid next step that is either non-existent or not in the same depth. Problematic step ids: {JsonConvert.SerializeObject(invalidStepIds)}"));
            });
    }

    [Test]
    public void AssertValidNextStepAsync_GivenSubFlowStepHasNextStepInDifferentDepth_ShouldFailValidation()
    {
        // Arrange
        var invalidStepIds = new List<string>()
        {
            "step-3.2",
        };

        List<Step> steps = new List<Step>
        {
            new SimpleStep(
                "step-1",
                "Step 1",
                null,
                null),
            new SimpleStep(
                "step-2",
                "Step 2",
                null,
                null),
            new SubFlowStep(
                "step-3",
                "Step 3",
                null,
                "step-4",
                new List<Step>
                {
                    new SimpleStep(
                        "step-3.1",
                        "Step 3.1",
                        null,
                        null),
                    new SimpleStep(
                        "step-3.2",
                        "Step 3.2",
                        null,
                        "step-4") // Next step invalid depth
                }),
            new SimpleStep("step-4", "Step 4", null, null)
        };

        // Act
        var exception = Assert.ThrowsAsync<SfValidationException>(
            async () =>
            {
                await _workflowStepValidator.AssertValidNextStepAsync(steps);
            });

        // Assert
        Assert.Multiple(
            () =>
            {
                Assert.That(exception, Is.Not.Null);
                Assert.That(exception!.ValidationResults, Has.Count.EqualTo(1));
                Assert.That(
                    exception.ValidationResults[0].ErrorMessage,
                    Is.EqualTo(
                        $"One or more steps have invalid next step that is either non-existent or not in the same depth. Problematic step ids: {JsonConvert.SerializeObject(invalidStepIds)}"));
            });
    }

    #endregion
}