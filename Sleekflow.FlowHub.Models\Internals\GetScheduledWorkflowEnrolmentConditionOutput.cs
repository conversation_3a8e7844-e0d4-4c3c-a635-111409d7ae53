﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Internals;

public class GetScheduledWorkflowEnrolmentConditionOutput
{
    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("workflow_versioned_id")]
    [Required]
    public string WorkflowVersionedId { get; set; }

    [JsonProperty("condition")]
    [Required]
    public string Condition { get; set; }

    [JsonProperty("workflow_name")]
    public string WorkflowName { get; set; }

    [JsonConstructor]
    public GetScheduledWorkflowEnrolmentConditionOutput(
        string sleekflowCompanyId,
        string workflowVersionedId,
        string condition,
        string workflowName)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowVersionedId = workflowVersionedId;
        Condition = condition;
        WorkflowName = workflowName;
    }
}