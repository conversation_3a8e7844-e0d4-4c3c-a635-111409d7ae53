using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class GetUserProfileAuditLogs : ITrigger
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;

    public GetUserProfileAuditLogs(IUserProfileAuditLogService userProfileAuditLogService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
    }

    public class GetUserProfileAuditLogsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [Required]
        [Range(0, 4000)]
        [JsonProperty("offset")]
        public int Offset { get; set; }

        [Required]
        [Range(0, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [JsonConstructor]
        public GetUserProfileAuditLogsInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId,
            int offset,
            int limit)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            Offset = offset;
            Limit = limit;
        }
    }

    public class GetUserProfileAuditLogsOutput
    {
        [JsonProperty]
        public List<UserProfileAuditLog> UserProfileAuditLogs { get; set; }

        [JsonConstructor]
        public GetUserProfileAuditLogsOutput(List<UserProfileAuditLog> userProfileAuditLogs)
        {
            UserProfileAuditLogs = userProfileAuditLogs;
        }
    }

    public async Task<GetUserProfileAuditLogsOutput> F(
        GetUserProfileAuditLogsInput getUserProfileAuditLogsInput)
    {
        var userProfileAuditLogs = await _userProfileAuditLogService.GetUserProfileAuditLogsAsync(
            getUserProfileAuditLogsInput.SleekflowCompanyId,
            getUserProfileAuditLogsInput.SleekflowUserProfileId,
            getUserProfileAuditLogsInput.Limit,
            getUserProfileAuditLogsInput.Offset);

        return new GetUserProfileAuditLogsOutput(userProfileAuditLogs);
    }
}