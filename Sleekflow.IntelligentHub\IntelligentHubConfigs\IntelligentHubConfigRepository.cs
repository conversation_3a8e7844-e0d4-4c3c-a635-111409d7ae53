﻿using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Models.IntelligentHubConfigs;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.IntelligentHub.IntelligentHubConfigs;

public interface IIntelligentHubConfigRepository : IRepository<IntelligentHubConfig>
{
}

public class IntelligentHubConfigRepository : BaseRepository<IntelligentHubConfig>, IIntelligentHubConfigRepository, IScopedService
{
    public IntelligentHubConfigRepository(
        ILogger<IntelligentHubConfigRepository> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}