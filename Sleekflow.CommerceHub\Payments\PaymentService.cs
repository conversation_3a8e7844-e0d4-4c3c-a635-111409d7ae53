﻿using MassTransit;
using Newtonsoft.Json;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Models.Payments;
using Sleekflow.CommerceHub.PaymentProviderConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;
using Sleekflow.JsonConfigs;
using Sleekflow.Models.Links;
using Sleekflow.Persistence;

namespace Sleekflow.CommerceHub.Payments;

public interface IPaymentService
{
    Task<(string PaymentLink, Payment Payment)> CreateOrderPaymentAsync(
        Order order,
        string paymentProviderName,
        string languageIsoCode,
        AuditEntity.SleekflowStaff sleekflowStaff,
        string returnToUrl);

    Task<(string RefundId, Payment Payment)> RefundOrderPaymentAsync(
        Order order,
        string paymentProviderName,
        decimal amount,
        string reason,
        AuditEntity.SleekflowStaff sleekflowStaff);

    Task<Payment> GetAsync(
        string orderId,
        string sleekflowCompanyId);

    Task<Payment> PatchAndGetPaymentAsync(
        string id,
        string sleekflowCompanyId,
        string paymentStatus);

    Task<Payment> PatchAndGetPaymentAsync(
        string id,
        string sleekflowCompanyId,
        string paymentStatus,
        ProcessPaymentContext processPaymentContext);

    Task<Payment> PatchAndGetPaymentAsync(
        string id,
        string sleekflowCompanyId,
        string paymentStatus,
        List<ProcessRefundContext> processRefundContexts);
}

public class PaymentService : IPaymentService, IScopedService
{
    private readonly IPaymentRepository _paymentRepository;
    private readonly IPaymentProcessor _paymentProcessor;
    private readonly IPaymentProviderConfigService _paymentProviderConfigService;
    private readonly IIdService _idService;
    private readonly IRequestClient<ShortenLinkRequest> _shortenLinkRequestClient;

    public PaymentService(
        IPaymentRepository paymentRepository,
        IPaymentProcessor paymentProcessor,
        IPaymentProviderConfigService paymentProviderConfigService,
        IIdService idService,
        IRequestClient<ShortenLinkRequest> shortenLinkRequestClient)
    {
        _paymentRepository = paymentRepository;
        _paymentProcessor = paymentProcessor;
        _paymentProviderConfigService = paymentProviderConfigService;
        _idService = idService;
        _shortenLinkRequestClient = shortenLinkRequestClient;
    }

    public async Task<(string PaymentLink, Payment Payment)> CreateOrderPaymentAsync(
        Order order,
        string paymentProviderName,
        string languageIsoCode,
        AuditEntity.SleekflowStaff sleekflowStaff,
        string returnToUrl)
    {
        var paymentProviderConfigs = await _paymentProviderConfigService.GetPaymentProviderConfigsAsync(
            order.SleekflowCompanyId);
        var paymentProviderConfig = paymentProviderConfigs.First(
            ppc =>
                ppc.PaymentProviderExternalConfig.ProviderName == paymentProviderName);

        var (paymentLink, processPaymentContext) = await _paymentProcessor.ProcessPaymentAsync(
            order,
            paymentProviderConfig,
            new PaymentProcessorOption(languageIsoCode, returnToUrl));

        var payment = new Payment(
            _idService.GetId(SysTypeNames.Payment),
            order.SleekflowCompanyId,
            order.SleekflowUserProfileId,
            order.Id,
            PaymentStatuses.GeneratedPaymentLink,
            processPaymentContext,
            null,
            new Dictionary<string, object?>(),
            new List<string>
            {
                "Active"
            },
            DateTimeOffset.UtcNow,
            DateTimeOffset.UtcNow,
            sleekflowStaff,
            sleekflowStaff);

        return (
            await GetShortenedUrl(paymentLink, order.SleekflowCompanyId, "Payment Link"),
            await _paymentRepository.CreateAndGetAsync(payment, payment.SleekflowCompanyId));
    }

    private async Task<string> GetShortenedUrl(
        string url,
        string sleekflowCompanyId,
        string title)
    {
        var shortenLinkReplyResponse = await _shortenLinkRequestClient.GetResponse<ShortenLinkReply>(
            new ShortenLinkRequest(
                url,
                sleekflowCompanyId,
                title,
                null));
        var shortenLinkReply = shortenLinkReplyResponse.Message;

        return shortenLinkReply.ShortUrl;
    }

    public async Task<Payment> GetAsync(
        string orderId,
        string sleekflowCompanyId)
    {
        var payments = await _paymentRepository.GetObjectsAsync(
            e =>
                e.OrderId == orderId &&
                e.SleekflowCompanyId == sleekflowCompanyId);

        if (payments.Count != 1)
        {
            throw new SfInternalErrorException("The object does not exist");
        }

        return payments[0];
    }

    public async Task<Payment> PatchAndGetPaymentAsync(
        string id,
        string sleekflowCompanyId,
        string paymentStatus)
    {
        var payment = await _paymentRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (payment is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        payment.PaymentStatus = paymentStatus;

        var isPatched = await _paymentRepository.UpsertAsync(
            payment,
            payment.SleekflowCompanyId);
        if (isPatched == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the payment {JsonConvert.SerializeObject(payment, JsonConfig.DefaultLoggingJsonSerializerSettings)}");
        }

        return payment;
    }

    public async Task<(string RefundId, Payment Payment)> RefundOrderPaymentAsync(
        Order order,
        string paymentProviderName,
        decimal amount,
        string reason,
        AuditEntity.SleekflowStaff sleekflowStaff)
    {
        var payment = await GetAsync(order.Id, order.SleekflowCompanyId);

        var paymentProviderConfigs = await _paymentProviderConfigService.GetPaymentProviderConfigsAsync(
            order.SleekflowCompanyId);
        var paymentProviderConfig = paymentProviderConfigs.First(
            ppc =>
                ppc.PaymentProviderExternalConfig.ProviderName == paymentProviderName);

        var (refundId, processRefundContext) = await _paymentProcessor.ProcessRefundAsync(
            order,
            payment,
            amount,
            reason,
            paymentProviderConfig);

        payment.ProcessRefundContexts = payment.ProcessRefundContexts == null
            ? new List<ProcessRefundContext>
            {
                processRefundContext
            }
            : payment.ProcessRefundContexts.Append(processRefundContext).ToList();

        if (processRefundContext.Status == "succeeded")
        {
            var refundedAmount = payment.ProcessRefundContexts.Where(c => c.Status == "succeeded").Sum(c => c.Amount);
            payment.PaymentStatus = order.TotalPrice - refundedAmount > 0
                ? PaymentStatuses.PartiallyRefunded
                : PaymentStatuses.FullyRefunded;
        }
        else
        {
            switch (processRefundContext.Status)
            {
                case "failed":
                    payment.PaymentStatus = PaymentStatuses.RefundFailed;
                    break;
                case "pending":
                    payment.PaymentStatus = PaymentStatuses.RefundPending;
                    break;
                default:
                    throw new NotImplementedException();
            }
        }

        payment.UpdatedBy = sleekflowStaff;
        payment.UpdatedAt = DateTimeOffset.Now;

        await _paymentRepository.UpsertAsync(
            payment,
            payment.SleekflowCompanyId);

        return (refundId, payment);
    }

    public async Task<Payment> PatchAndGetPaymentAsync(
        string id,
        string sleekflowCompanyId,
        string paymentStatus,
        List<ProcessRefundContext> processRefundContexts)
    {
        var payment = await _paymentRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (payment is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        payment.PaymentStatus = paymentStatus;
        payment.ProcessRefundContexts = processRefundContexts;

        var isPatched = await _paymentRepository.UpsertAsync(
            payment,
            payment.SleekflowCompanyId);
        if (isPatched == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the payment {JsonConvert.SerializeObject(payment, JsonConfig.DefaultLoggingJsonSerializerSettings)}");
        }

        return payment;
    }

    public async Task<Payment> PatchAndGetPaymentAsync(
        string id,
        string sleekflowCompanyId,
        string paymentStatus,
        ProcessPaymentContext processPaymentContext)
    {
        var payment = await _paymentRepository.GetOrDefaultAsync(id, sleekflowCompanyId);
        if (payment is null)
        {
            throw new SfNotFoundObjectException(id, sleekflowCompanyId);
        }

        payment.PaymentStatus = paymentStatus;
        payment.ProcessPaymentContext = processPaymentContext;

        var isPatched = await _paymentRepository.UpsertAsync(
            payment,
            payment.SleekflowCompanyId);
        if (isPatched == 0)
        {
            throw new SfInternalErrorException(
                $"Unable to patch the payment {JsonConvert.SerializeObject(payment, JsonConfig.DefaultLoggingJsonSerializerSettings)}");
        }

        return payment;
    }
}