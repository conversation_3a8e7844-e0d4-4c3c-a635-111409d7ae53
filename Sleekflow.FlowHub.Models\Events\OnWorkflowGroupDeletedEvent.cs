﻿using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.FlowHub.Models.Events;

public class OnWorkflowGroupDeletedEvent : IHasSleekflowCompanyId
{
    public string SleekflowCompanyId { get; set; }

    public string WorkflowGroupId { get; set; }

    public OnWorkflowGroupDeletedEvent(
        string sleekflowCompanyId,
        string workflowGroupId)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        WorkflowGroupId = workflowGroupId;
    }
}