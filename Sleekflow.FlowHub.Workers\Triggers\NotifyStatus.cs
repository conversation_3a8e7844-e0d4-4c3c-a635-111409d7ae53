using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask.Client;
using Microsoft.Extensions.Logging;
using Sleekflow.FlowHub.Models.Workers;
using Sleekflow.FlowHub.Workers.Utils;

namespace Sleekflow.FlowHub.Workers.Triggers;

public class NotifyStatus
{
    private readonly ILogger<NotifyStatus> _logger;

    public NotifyStatus(
        ILogger<NotifyStatus> logger)
    {
        _logger = logger;
    }

    public class NotifyStatusOutput
    {
    }

    [Function("NotifyStatus")]
    public async Task<IActionResult> RunAsync(
        [HttpTrigger(AuthorizationLevel.Function, "post")]
        HttpRequest req,
        [DurableClient]
        DurableTaskClient starter)
    {
        return await Func.Run2Async<NotifyStatusInput, NotifyStatusOutput, DurableTaskClient>(
            req,
            _logger,
            starter,
            F);
    }

    private async Task<NotifyStatusOutput> F(
        (NotifyStatusInput Input, ILogger Logger, DurableTaskClient Starter) tuple)
    {
        var (notifyStatusInput, logger, starter) = tuple;

        await starter.RaiseEventAsync(notifyStatusInput.WorkerInstanceId, notifyStatusInput.Status, notifyStatusInput);

        return new NotifyStatusOutput();
    }
}