using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.States;

[ContainerId(ContainerNames.State)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class State : Entity, IHasETag, IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty("identity")]
    public StateIdentity Identity { get; set; }

    [JsonProperty("workflow_context")]
    public StateWorkflowContext WorkflowContext { get; set; }

    [JsonProperty("trigger_event_body", TypeNameHandling = TypeNameHandling.Objects)]
    public EventBody TriggerEventBody { get; set; }

    [JsonProperty("state_status")]
    public string StateStatus { get; set; }

    [JsonProperty(StateFieldNames.StateReasonCode)]
    public string? StateReasonCode { get; set; }

    [JsonProperty(IHasETag.PropertyNameETag)]
    public string? ETag { get; set; }

    [JsonProperty("origin")]
    public string? Origin { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonProperty("is_reenrollment")]
    public bool IsReenrolllment { get; set; }

    [JsonProperty("parent_state_id")]
    public string? ParentStateId { get; set; }

    [JsonConstructor]
    public State(
        string id,
        StateIdentity identity,
        StateWorkflowContext workflowContext,
        EventBody triggerEventBody,
        string stateStatus,
        string? stateReasonCode,
        string? eTag,
        string? origin,
        DateTimeOffset createdAt,
        DateTimeOffset? updatedAt,
        bool? isReenrollment,
        int? ttl,
        string? parentStateId = null)
        : base(id, SysTypeNames.State)
    {
        Id = id;
        Identity = identity;
        WorkflowContext = workflowContext;
        TriggerEventBody = triggerEventBody;
        StateStatus = stateStatus;
        StateReasonCode = stateReasonCode;
        ETag = eTag;
        Origin = origin;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt.GetValueOrDefault(createdAt);
        IsReenrolllment = isReenrollment.GetValueOrDefault(false);
        Ttl = ttl;
        ParentStateId = parentStateId;
    }
}