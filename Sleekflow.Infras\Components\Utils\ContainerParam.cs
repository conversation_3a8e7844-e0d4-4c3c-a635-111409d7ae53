using System.Collections.Immutable;

namespace Sleekflow.Infras.Components.Utils;

public record ContainerParam(
    string Name,
    string Id,
    List<string> PartitionKeyPaths,
    int? Ttl = null,
    List<Pulumi.AzureNative.DocumentDB.Inputs.ExcludedPathArgs>? ExcludedIndexingPathsList = null,
    List<Pulumi.AzureNative.DocumentDB.Inputs.IncludedPathArgs>? IncludedPathArgsList = null,
    List<ImmutableArray<Pulumi.AzureNative.DocumentDB.Inputs.CompositePathArgs>>?
        CompositeIndexingPathArgsList = null,
    int? MaxThroughput = null,
    List<Pulumi.AzureNative.DocumentDB.Inputs.UniqueKeyArgs>? UniqueKeyArgsList = null,
    bool IsAnalyticalStorageEnabled = false);