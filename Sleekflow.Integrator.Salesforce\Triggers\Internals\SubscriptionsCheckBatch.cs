﻿using System.ComponentModel.DataAnnotations;
using MassTransit;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Authentications;
using Sleekflow.CrmHub.Models.Events;
using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.Salesforce.Authentications;
using Sleekflow.Integrator.Salesforce.Configs;
using Sleekflow.Integrator.Salesforce.Connections;
using Sleekflow.Integrator.Salesforce.Services;
using Sleekflow.Models.TriggerEvents;

namespace Sleekflow.Integrator.Salesforce.Triggers.Internals;

[TriggerGroup("Internals")]
public class SubscriptionsCheckBatch : ITrigger
{
    private readonly ISalesforceAuthenticationService _salesforceAuthenticationService;
    private readonly ISalesforceObjectService _salesforceObjectService;
    private readonly IBus _bus;
    private readonly ILogger<SubscriptionsCheckBatch> _logger;
    private readonly ISalesforceConnectionService _salesforceConnectionService;

    public SubscriptionsCheckBatch(
        ISalesforceAuthenticationService salesforceAuthenticationService,
        ISalesforceObjectService salesforceObjectService,
        IBus bus,
        ILogger<SubscriptionsCheckBatch> logger,
        ISalesforceConnectionService salesforceConnectionService)
    {
        _salesforceAuthenticationService = salesforceAuthenticationService;
        _salesforceObjectService = salesforceObjectService;
        _bus = bus;
        _logger = logger;
        _salesforceConnectionService = salesforceConnectionService;
    }

    public class SubscriptionsCheckBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("subscription")]
        [Required]
        public SalesforceSubscription Subscription { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("last_object_modification_time")]
        [Required]
        public DateTimeOffset LastObjectModificationTime { get; }

        [JsonProperty("filter_groups")]
        [Required]
        public List<SyncConfigFilterGroup> FilterGroups { get; set; }

        [JsonProperty("field_filters")]
        public List<SyncConfigFieldFilter>? FieldFilters { get; set; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; set; }

        [JsonConstructor]
        public SubscriptionsCheckBatchInput(
            string sleekflowCompanyId,
            SalesforceSubscription subscription,
            string entityTypeName,
            List<SyncConfigFilterGroup> filterGroups,
            List<SyncConfigFieldFilter>? fieldFilters,
            DateTimeOffset lastObjectModificationTime,
            string? nextRecordsUrl)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            Subscription = subscription;
            EntityTypeName = entityTypeName;
            FilterGroups = filterGroups;
            FieldFilters = fieldFilters;
            LastObjectModificationTime = lastObjectModificationTime;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public class SubscriptionsCheckBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("next_last_object_modification_time")]
        public DateTimeOffset NextLastObjectModificationTime { get; }

        [JsonProperty("next_records_url")]
        public string? NextRecordsUrl { get; }

        [JsonConstructor]
        public SubscriptionsCheckBatchOutput(
            long count,
            DateTimeOffset nextLastObjectModificationTime,
            string? nextRecordsUrl)
        {
            Count = count;
            NextLastObjectModificationTime = nextLastObjectModificationTime;
            NextRecordsUrl = nextRecordsUrl;
        }
    }

    public async Task<SubscriptionsCheckBatchOutput> F(
        SubscriptionsCheckBatchInput subscriptionsCheckBatchInput)
    {
        var subscription = subscriptionsCheckBatchInput.Subscription;
        var entityTypeName = subscriptionsCheckBatchInput.EntityTypeName;
        var lastObjectModificationTime = subscriptionsCheckBatchInput.LastObjectModificationTime;
        var after = subscriptionsCheckBatchInput.NextRecordsUrl;

        SalesforceAuthentication? authentication;

        if (subscription.ConnectionId is null)
        {
            authentication =
                await _salesforceAuthenticationService.GetAsync(subscriptionsCheckBatchInput.SleekflowCompanyId);
        }
        else
        {
            var connection = await _salesforceConnectionService.GetByIdAsync(
                subscription.ConnectionId,
                subscription.SleekflowCompanyId);

            authentication =
                await _salesforceAuthenticationService.GetAsync(connection.AuthenticationId, subscriptionsCheckBatchInput.SleekflowCompanyId);
        }

        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        _logger.LogInformation(
            "Started sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime);

        var (objects, nextRecordsUrl) = await _salesforceObjectService.GetRecentlyUpdatedObjectsAsync(
            authentication,
            entityTypeName,
            lastObjectModificationTime,
            subscriptionsCheckBatchInput.FilterGroups,
            subscriptionsCheckBatchInput.FieldFilters,
            after);

        _logger.LogInformation(
            "Ended sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}, count {Count}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime,
            objects.Count);

        var nextLastObjectModificationTime =
            subscription.LastObjectModificationTime ?? subscription.LastExecutionStartTime;

        if (subscription.IsFlowsBased is true)
        {
            var customObjectApiNames =
                (await _salesforceObjectService.GetCustomObjectTypes(authentication)).Select(t => t.ApiName).ToList();
            var isFromCustomObjectSubscription = customObjectApiNames.Contains(entityTypeName);

            var isFromSupportedFlowsBasedSubscription = isFromCustomObjectSubscription ||
                                                        entityTypeName is "Account" ||
                                                        entityTypeName is "Contact" ||
                                                        entityTypeName is "Lead" ||
                                                        entityTypeName is "Opportunity" ||
                                                        entityTypeName is "Campaign";

            if (isFromSupportedFlowsBasedSubscription)
            {
                var salesforceObjectCreatedEventRequests = new List<SalesforceObjectCreatedEventRequest>();
                var salesforceObjectUpdatedEventRequests = new List<SalesforceObjectUpdatedEventRequest>();

                foreach (var dict in objects)
                {
                    var systemModstamp = dict["SystemModstamp"] as DateTimeOffset?;
                    if (systemModstamp == null)
                    {
                        _logger.LogError(
                            "The systemModstamp is invalid. systemModstamp {SystemModstamp}",
                            systemModstamp);

                        continue;
                    }

                    nextLastObjectModificationTime = (DateTimeOffset) systemModstamp > nextLastObjectModificationTime
                        ? (DateTimeOffset) systemModstamp
                        : nextLastObjectModificationTime;

                    var createdDate = dict["CreatedDate"] as DateTimeOffset?;
                    if (createdDate == null)
                    {
                        _logger.LogError(
                            "The createdDate is invalid. createdDate {CreatedDate}",
                            createdDate);

                        continue;
                    }

                    if ((systemModstamp.Value - createdDate.Value).Duration().TotalSeconds <= 1)
                    {
                        salesforceObjectCreatedEventRequests.Add(
                            new SalesforceObjectCreatedEventRequest(
                                DateTimeOffset.UtcNow,
                                subscriptionsCheckBatchInput.SleekflowCompanyId,
                                subscriptionsCheckBatchInput.Subscription.ConnectionId!,
                                (string) dict["Id"]!,
                                entityTypeName,
                                isFromCustomObjectSubscription,
                                dict));
                    }
                    else
                    {
                        salesforceObjectUpdatedEventRequests.Add(
                            new SalesforceObjectUpdatedEventRequest(
                                DateTimeOffset.UtcNow,
                                subscriptionsCheckBatchInput.SleekflowCompanyId,
                                subscriptionsCheckBatchInput.Subscription.ConnectionId!,
                                (string) dict["Id"]!,
                                entityTypeName,
                                isFromCustomObjectSubscription,
                                dict));
                    }
                }

                if (salesforceObjectCreatedEventRequests.Count > 0)
                {
                    await _bus.PublishBatch(
                        salesforceObjectCreatedEventRequests,
                        context =>
                        {
                            context.ConversationId = Guid.Parse(subscriptionsCheckBatchInput.SleekflowCompanyId);
                        });
                }

                if (salesforceObjectUpdatedEventRequests.Count > 0)
                {
                    await _bus.PublishBatch(
                        salesforceObjectUpdatedEventRequests,
                        context =>
                        {
                            context.ConversationId = Guid.Parse(subscriptionsCheckBatchInput.SleekflowCompanyId);
                        });
                }
            }
        }
        else
        {
            var events = new List<OnObjectOperationEvent>();
            foreach (var dict in objects)
            {
                var systemModstamp = dict["SystemModstamp"] as DateTimeOffset?;
                if (systemModstamp == null)
                {
                    _logger.LogError(
                        "The systemModstamp is invalid. systemModstamp {SystemModstamp}",
                        systemModstamp);

                    continue;
                }

                nextLastObjectModificationTime = (DateTimeOffset) systemModstamp > nextLastObjectModificationTime
                    ? (DateTimeOffset) systemModstamp
                    : nextLastObjectModificationTime;

                var onObjectOperationEvent = new OnObjectOperationEvent(
                    dict,
                    OnObjectOperationEvent.OperationCreateOrUpdateObject,
                    "salesforce-integrator",
                    subscriptionsCheckBatchInput.SleekflowCompanyId,
                    _salesforceObjectService.ResolveObjectId(dict),
                    subscriptionsCheckBatchInput.EntityTypeName,
                    null);

                events.Add(onObjectOperationEvent);
            }

            foreach (var onObjectOperationEvents in events.Chunk(30))
            {
                await _bus.PublishBatch(
                    onObjectOperationEvents,
                    context => { context.ConversationId = Guid.Parse(subscriptionsCheckBatchInput.SleekflowCompanyId); });
            }
        }

        _logger.LogInformation(
            "Flushed sleekflowCompanyId {SleekflowCompanyId}, subscription.Id {SubscriptionId}, entityTypeName {EntityTypeName}, lastObjectModificationTime {LastObjectModificationTime}, count {Count}, nextLastObjectModificationTime {NextLastObjectModificationTime}",
            subscription.SleekflowCompanyId,
            subscription.Id,
            entityTypeName,
            lastObjectModificationTime,
            objects.Count,
            nextLastObjectModificationTime);

        return new SubscriptionsCheckBatchOutput(objects.Count, nextLastObjectModificationTime, nextRecordsUrl);
    }
}