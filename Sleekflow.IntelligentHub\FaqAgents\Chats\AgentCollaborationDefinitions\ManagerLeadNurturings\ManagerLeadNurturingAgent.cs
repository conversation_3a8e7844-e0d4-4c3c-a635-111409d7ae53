using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Plugins.LeadNurturings;
using Sleekflow.IntelligentHub.Utils;
using IReviewerPlugin = Sleekflow.IntelligentHub.Plugins.LeadNurturings.IReviewerPlugin;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.ManagerLeadNurturings;

public class ManagerLeadNurturingAgent
{
    private readonly ILogger<ManagerLeadNurturingAgent> _logger;
    private readonly ILeadNurturingDataPane _dataPane;

    public static ChatCompletionAgent Create(
        Kernel kernel,
        PromptExecutionSettings settings,
        ILogger<ManagerLeadNurturingAgent> logger,
        ILeadNurturingDataPane dataPane,
        IDataPaneKeyManager keyManager,
        ILeadClassifierPlugin leadClassifierPlugin,
        IDecisionPlugin decisionPlugin,
        IStrategyPlugin strategyPlugin,
        IKeyBasedKnowledgePlugin knowledgePlugin,
        IResponseCrafterPlugin responseCrafterPlugin,
        IResponseGenerationPlugin responseGenerationPlugin,
        IReviewerPlugin reviewerPlugin,
        IPlanningPlugin planningPlugin,
        IConfirmationPlugin confirmationPlugin,
        IActionPlugin actionPlugin)
    {
        // Configure structured output for lead nurturing workflow phases
        PromptExecutionSettingsUtils.EnrichPromptExecutionSettingsWithStructuredOutput(
            settings,
            [
                new PromptExecutionSettingsUtils.Property("agent_name", "string"),
                new PromptExecutionSettingsUtils.Property("session_id", "string"),
                new PromptExecutionSettingsUtils.Property("phase", "string"),
                new PromptExecutionSettingsUtils.Property(
                    "initial_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("initial_reasoning", "string"),
                        new PromptExecutionSettingsUtils.Property("workflow_plan", "string"),
                        new PromptExecutionSettingsUtils.Property(
                            "tools_to_call",
                            "array",
                            false,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                                    new PromptExecutionSettingsUtils.Property(
                                        "args",
                                        "array",
                                        false,
                                        null,
                                        new PromptExecutionSettingsUtils.Property(
                                            string.Empty,
                                            "object",
                                            false,
                                            [
                                                new PromptExecutionSettingsUtils.Property("key", "string"),
                                                new PromptExecutionSettingsUtils.Property("value", "string")
                                            ]))
                                ]))
                    ]),
                new PromptExecutionSettingsUtils.Property(
                    "observation_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("thoughts", "string"),
                        new PromptExecutionSettingsUtils.Property("tool_results_analysis", "string"),
                        new PromptExecutionSettingsUtils.Property("workflow_ready", "boolean"),
                        new PromptExecutionSettingsUtils.Property("round_of_execution", "integer"),
                        new PromptExecutionSettingsUtils.Property("should_terminate", "boolean"),
                        new PromptExecutionSettingsUtils.Property("next_workflow_step", "string", true),
                        new PromptExecutionSettingsUtils.Property(
                            "tools_to_call",
                            "array",
                            true,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property("reasoning", "string"),
                                    new PromptExecutionSettingsUtils.Property(
                                        "args",
                                        "array",
                                        false,
                                        null,
                                        new PromptExecutionSettingsUtils.Property(
                                            string.Empty,
                                            "object",
                                            false,
                                            [
                                                new PromptExecutionSettingsUtils.Property("key", "string"),
                                                new PromptExecutionSettingsUtils.Property("value", "string")
                                            ]))
                                ])),
                    ]),
                new PromptExecutionSettingsUtils.Property(
                    "report_phase",
                    "object",
                    true,
                    [
                        new PromptExecutionSettingsUtils.Property("workflow_summary", "string"),
                        new PromptExecutionSettingsUtils.Property("lead_classification", "string"),
                        new PromptExecutionSettingsUtils.Property("decision_made", "string"),
                        new PromptExecutionSettingsUtils.Property("actions_taken", "string"),
                        new PromptExecutionSettingsUtils.Property(
                            "executed_tools",
                            "array",
                            false,
                            null,
                            new PromptExecutionSettingsUtils.Property(
                                string.Empty,
                                "object",
                                false,
                                [
                                    new PromptExecutionSettingsUtils.Property("id", "string"),
                                    new PromptExecutionSettingsUtils.Property("tool_name", "string"),
                                    new PromptExecutionSettingsUtils.Property("outcome", "string"),
                                ])),
                    ]),
                new PromptExecutionSettingsUtils.Property("next_phase", "string", true),
                new PromptExecutionSettingsUtils.Property("result", "string", true)
            ]);

        settings.FunctionChoiceBehavior = FunctionChoiceBehavior.Auto(
            options: new FunctionChoiceBehaviorOptions
            {
                AllowParallelCalls = true, // Lead nurturing is sequential
                AllowConcurrentInvocation = true,
                AllowStrictSchemaAdherence = false
            });

        // Register the data pane for shared storage
        kernel.Plugins.AddFromObject(dataPane);

        // Register the key manager for structured key generation
        kernel.Plugins.AddFromObject(keyManager);

        // Register all Lead Nurturing tools
        kernel.Plugins.AddFromObject(leadClassifierPlugin);
        kernel.Plugins.AddFromObject(decisionPlugin);
        kernel.Plugins.AddFromObject(strategyPlugin);
        kernel.Plugins.AddFromObject(knowledgePlugin);
        kernel.Plugins.AddFromObject(responseCrafterPlugin);
        kernel.Plugins.AddFromObject(responseGenerationPlugin);
        kernel.Plugins.AddFromObject(reviewerPlugin);
        kernel.Plugins.AddFromObject(planningPlugin);
        kernel.Plugins.AddFromObject(confirmationPlugin);
        kernel.Plugins.AddFromObject(actionPlugin);

        // Dynamically generate available tools description
        var availableTools = GetAvailableToolsDescription(kernel);

        return new ChatCompletionAgent
        {
            Name = GetAgentName(),
            Description =
                "Manager Lead Nurturing Agent - orchestrates lead nurturing workflows using Key-Based Architecture.",
            Kernel = kernel,
            Arguments = new KernelArguments(settings),
            Instructions = GetManagerInstructions(availableTools)
        };
    }

    public static string GetAgentName()
    {
        return "ManagerLeadNurturingAgent";
    }

    private static string GetAvailableToolsDescription(Kernel kernel)
    {
        return string.Join(
            "\n",
            kernel.Plugins.SelectMany(p => p.AsAIFunctions())
                .Select(f => $"- {f.Name}: {f.Description ?? "No description available"}"));
    }

    private static string GetManagerInstructions(string availableTools)
    {
        return $$"""
                 You are the ManagerLeadNurturingAgent, responsible for lead nurturing through a key-based workflow system.

                 ## Core Workflow

                 Execute these steps in order:

                 **1. Initialize Session**
                 - Call `generate_session_key` with the GROUP_CHAT_ID
                 - Call `get_conversation_context_key` to get the conversation key (data is pre-stored)

                 **2. Analyze Lead**
                 - Call `get_classification_key` for classification storage
                 - Call `classify_lead` using session and conversation context keys
                 - Parse the classification result to understand lead quality
                 - Call `get_decision_key` for decision storage
                 - Call `make_decision` using stored classification and context
                 - Parse the decision result to determine routing

                 **3. Execute Based on Decision Analysis**

                 **For "continue_nurturing" decision:**
                 - Call `get_strategy_key` → `define_strategy`
                 - Parse strategy response for "need_knowledge" field:
                   - If need_knowledge is present: Call `get_knowledge_key` → `query_knowledge`
                   - If need_knowledge is null/empty: Skip knowledge step
                 - Call `get_response_key` → `craft_response`
                 - Parse response crafter output:
                   - If decision is "assign_insufficient_info": Switch to assignment workflow
                   - Otherwise: Call `get_review_key` → `review_response`
                     - Parse review result for approval status
                     - If approved: End workflow
                     - If rejected: Re-craft response

                 **For assignment decisions (assign_hot, assign_cold, assign_human, assign_drop_off, assign_insufficient_info):**
                 - Call `get_planning_key` → `plan_lead_assignment`
                 - Parse planning response for "action_type":
                   - If "assign_lead": Call `get_confirmation_key` → `check_confirmation`
                     - Parse confirmation status:
                       - If "confirmed": Call `get_action_key` → `execute_action` → `craft_transition_response`
                       - If "not confirmed": Call `craft_transition_response` with confirmation questions
                       - If "cancelled": Call `craft_transition_response`
                   - If "no_action": Go back to strategy step
                 - Call `review_response`

                 **For "schedule_demo" decision:**
                 - Call `get_planning_key` → `plan_demo_scheduling`
                 - Parse planning response for "action_type":
                   - If "modification_required": Call `craft_information_gathering_response`
                   - If "existing_demo_scheduled": Call `craft_transition_response`
                   - Otherwise: Call `get_confirmation_key` → `check_confirmation`
                     - Parse confirmation and execute action if confirmed
                   - If "no_action": Go back to strategy step
                 - Call `review_response`

                 ## Decision Interpretation

                 **Tool Response Format:**
                 Tools now return their original agent outputs. You must analyze these outputs to determine the next workflow step.

                 **Agent Output Analysis:**

                 **Lead Classification Analysis:**
                 - Parse "classification" field (hot/warm/cold/existing_customer)
                 - Always proceed to make_decision after classification

                 **Decision Analysis:**
                 - Parse "decision" field from DecisionAgentResponse
                 - Route based on decision value:
                   - "continue_nurturing" → Define strategy for nurturing
                   - "assign_hot", "assign_cold", "assign_human", "assign_drop_off", "assign_insufficient_info" → Plan lead assignment
                   - "schedule_demo" → Plan demo scheduling

                 **Strategy Analysis:**
                 - Parse "need_knowledge" field from StrategyAgentResponse
                 - If need_knowledge is not null/empty → Query knowledge base
                 - If need_knowledge is null/empty → Skip to craft_response

                 **Response Crafter Analysis:**
                 - Parse "decision" field from transformed response
                 - Look for "assign_insufficient_info" → Switch to assignment workflow
                 - Otherwise → Proceed to review_response

                 **Review Analysis:**
                 - Parse "review" field from ReviewerAgentResponse
                 - If review contains "approved" → End workflow (workflow_complete)
                 - If review contains "rejected" or "not approved" → Re-craft response

                 **Planning Analysis:**
                 - Parse "action_type" field from planning responses
                 - For assignment planning: "assign_lead" → Check confirmation, "no_action" → Return to strategy
                 - For demo planning: "modification_required" → Gather information, "existing_demo_scheduled" → Craft transition

                 **Confirmation Analysis:**
                 - Parse "confirmation_status" field
                 - "confirmed" → Execute action
                 - "not confirmed" → Craft confirmation request
                 - "cancelled" → Craft transition response

                 **Action Analysis:**
                 - Parse "result" field from ActionAgentResponse
                 - "success" or "failure" → Always craft transition response

                 ## Output Format

                 Use structured JSON output with phases:

                 **Initial Phase:**
                 ```json
                 {
                   "phase": "initial",
                   "session_id": "group_chat_id",
                   "initial_phase": {
                     "initial_reasoning": "Brief analysis and plan",
                     "workflow_plan": "Step-by-step plan",
                     "tools_to_call": [{"tool_name": "generate_session_key", "reasoning": "reason", "args": [{"key": "groupChatId", "value": "group_chat_id"}]}]
                   },
                   "next_phase": "execution"
                 }
                 ```

                 **Observation Phase:**
                 ```json
                 {
                   "phase": "observation",
                   "session_id": "session_key",
                   "observation_phase": {
                     "thoughts": "Analysis of tool results",
                     "tool_results_analysis": "What tools returned and flow decisions",
                     "workflow_ready": true/false,
                     "round_of_execution": 1,
                     "should_terminate": true/false,
                     "next_workflow_step": "Next step based on flow control decision",
                     "tools_to_call": [...]
                   },
                   "next_phase": "execution/report"
                 }
                 ```

                 **Report Phase:**
                 ```json
                 {
                   "phase": "report",
                   "session_id": "session_key",
                   "report_phase": {
                     "workflow_summary": "Summary",
                     "lead_classification": "hot/warm/cold",
                     "decision_made": "decision",
                     "actions_taken": "actions",
                     "executed_tools": [...]
                   },
                   "result": "completed"
                 }
                 ```

                 ## Key Rules

                 - **Parse tool responses** - Extract "decision" field to determine next step
                 - **Handle insufficient knowledge** - Switch to assignment workflow when knowledge is inadequate
                 - **Manage review cycles** - Re-craft responses when rejected by reviewer
                 - **All functions use keys only** - never pass raw conversation text
                 - **Configuration is auto-retrieved** - don't pass responseLanguage, additionalInstructions as parameters
                 - **Use same session key throughout** - for data isolation
                 - **Maximum 5 execution rounds** - then terminate
                 - **Start with generate_session_key** - always first step

                 ## Available Tools
                 {{availableTools}}

                 **Important:** The key-based architecture with flow control decisions enables the complex agent interactions from the original multi-agent system while maintaining the efficiency of the manager-orchestrated approach.
                 """;
    }
}