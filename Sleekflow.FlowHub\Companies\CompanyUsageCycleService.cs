using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.FlowHubConfigs;
using Sleekflow.FlowHub.Models.TravisBackend;
using Sleekflow.FlowHub.TravisBackend;

namespace Sleekflow.FlowHub.Companies;

public interface ICompanyUsageCycleService
{
    Task<(DateTimeOffset FromDateTime, DateTimeOffset ToDateTime)> GetUsageCycleAsync(FlowHubConfig flowHubConfig, bool refreshCache = false);
}

public class CompanyUsageCycleService : ICompanyUsageCycleService, IScopedService
{
    private readonly ITravisBackendHttpClient _travisBackendHttpClient;

    private readonly ICacheService _cacheService;

    private readonly ILogger<CompanyUsageCycleService> _logger;

    public CompanyUsageCycleService(
        ITravisBackendHttpClient travisBackendHttpClient,
        ICacheService cacheService,
        ILogger<CompanyUsageCycleService> logger)
    {
        _travisBackendHttpClient = travisBackendHttpClient;
        _cacheService = cacheService;
        _logger = logger;
    }

    public async Task<(DateTimeOffset FromDateTime, DateTimeOffset ToDateTime)> GetUsageCycleAsync(FlowHubConfig flowHubConfig, bool refreshCache = false)
    {
        try
        {
            var cacheKey = $"CompanyUsageCycle:{flowHubConfig.SleekflowCompanyId}";

            if (refreshCache)
            {
                await _cacheService.RemoveCacheAsync(cacheKey);
            }

            return await _cacheService.CacheAsync(
                cacheKey,
                async () =>
                {
                    var response = await _travisBackendHttpClient.GetCompanyUsageCycleAsync(
                        flowHubConfig?.Origin,
                        new GetCompanyUsageCycleInput(flowHubConfig!.SleekflowCompanyId));

                    return (response.FromDateTime, response.ToDateTime);
                },
                TimeSpan.FromHours(1));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to Retrieve UsageCycle for Company. CompanyId {CompanyId}, Message: {Message}", flowHubConfig.SleekflowCompanyId, ex.Message);
            throw;
        }
    }
}