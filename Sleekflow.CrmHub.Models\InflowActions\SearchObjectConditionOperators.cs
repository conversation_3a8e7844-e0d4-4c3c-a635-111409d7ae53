﻿namespace Sleekflow.CrmHub.Models.InflowActions;

public static class SearchObjectConditionOperators
{
    public const string IsToday = "isToday";
    public const string IsBetween = "isBetween";
    public const string IsNotBetween = "isNotBetween";
    public const string IsWithin = "isWithin";
    public const string IsNotWithin = "isNotWithin";
    public const string IsEqualTo = "isEqualTo";
    public const string IsNotEqualTo = "isNotEqualTo";
    public const string IsAfter = "isAfter";
    public const string IsBefore = "isBefore";
    public const string IsOn = "isOn";
    public const string Exists = "exists";
    public const string DoesNotExist = "doesNotExist";
    public const string Contains = "contains";
    public const string DoesNotContain = "doesNotContain";
    public const string IsGreaterThan = "isGreaterThan";
    public const string IsLessThan = "isLessThan";
    public const string IsGreaterThanOrEqualTo = "isGreaterThanOrEqualTo";
    public const string IsLessThanOrEqualTo = "isLessThanOrEqualTo";
    public const string StartsWith = "startsWith";
    public const string In = "in";
}