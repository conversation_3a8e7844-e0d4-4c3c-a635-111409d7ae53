using Sleekflow.FlowHub.Models.States.Abstractions;

namespace Sleekflow.FlowHub.Grains;

public interface IPersistentDictionaryGrain : IGrainWithStringKey, IAsyncDictionary<string, object?>
{
}

public abstract class PersistentDictionaryGrainBase : Grain, IPersistentDictionaryGrain
{
    protected readonly IPersistentState<Dictionary<string, object?>> StateStorage;

    // Constructor accepts the state from derived classes
    protected PersistentDictionaryGrainBase(IPersistentState<Dictionary<string, object?>> state)
    {
        StateStorage = state;
        StateStorage.State ??= new Dictionary<string, object?>();
    }

    // --- Implement ALL methods from IAsyncDictionary<string, object?> here ---
    // --- using StateStorage.State and await StateStorage.WriteStateAsync() ---
    public virtual async Task SetAsync(string key, object? value)
    {
        StateStorage.State[key] = value;
        await StateStorage.WriteStateAsync();
    }

    public virtual Task<object?> GetAsync(string key)
    {
        return StateStorage.State.TryGetValue(key, out var value)
            ? Task.FromResult(value)
            : Task.FromResult<object?>(null);
    }

    public virtual async Task ClearAsync()
    {
        StateStorage.State.Clear();
        await StateStorage.ClearStateAsync();
    }

    public virtual async Task<long> IncrementAsync(string key, long increment = 1)
    {
        if (StateStorage.State.TryGetValue(key, out var value) )
        {
            if (value is long longValue)
            {
                try
                {
                    var newValue = longValue + increment;
                    StateStorage.State[key] = newValue;
                    await StateStorage.WriteStateAsync();

                    return newValue;
                }
                catch (OverflowException)
                {
                    StateStorage.State[key] = 0;

                    await StateStorage.WriteStateAsync();

                    return 0;
                }
            }
            else
            {
                throw new InvalidCastException($"Cannot increment value of type {value?.GetType().Name}.");
            }
        }
        else
        {
            StateStorage.State[key] = increment;

            await StateStorage.WriteStateAsync();

            return increment;
        }
    }

    public virtual Task<bool> ContainsKeyAsync(string key)
    {
        return Task.FromResult(StateStorage.State.ContainsKey(key));
    }

    public virtual Task<bool> ContainsValueAsync(object? value)
    {
        return Task.FromResult(StateStorage.State.ContainsValue(value));
    }

    public virtual Task<int> CountAsync()
    {
        return Task.FromResult(StateStorage.State.Count);
    }

    public virtual async Task<bool> RemoveAsync(string key)
    {
        var removed = StateStorage.State.Remove(key);

        if (removed)
        {
            await StateStorage.WriteStateAsync();
        }

        return removed;
    }

    public virtual Task<ICollection<string>> KeysAsync()
    {
        return Task.FromResult<ICollection<string>>(StateStorage.State.Keys);
    }

    public virtual Task<ICollection<object?>> ValuesAsync()
    {
        return Task.FromResult<ICollection<object?>>(StateStorage.State.Values);
    }

    public virtual Task<Dictionary<string, object?>> ToDictionaryAsync()
    {
        return Task.FromResult(new Dictionary<string, object?>(StateStorage.State));
    }
}