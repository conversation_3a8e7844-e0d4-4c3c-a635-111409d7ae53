# This is a markdown for the website at https://help.sleekflow.io/en_US/flow-triggers/configuring-integration-trigger-nodes
# Configuring "Integration" trigger nodes

All trigger nodes require setting up in a node form, where you can further specify the parameters for a flow to be triggered. In this article, we will guide you on how to set up every “Integration” trigger node so you can initiate your flows based on expected circumstances. 

## Webhook received

⚠️ The "Webhook received" trigger will only be available for **Premium** and **Enterprise** plan users

The "Webhook received" trigger allows you to enroll contacts into a flow when SleekFlow receives a webhook matching the configurations set within the trigger’s node form.

Set up the "Webhook received" trigger node form by following the steps outlined below:

1.  Activate webhook received trigger node
2.  Set up your webhook
3.  Specify contact identifier
4.  Configure response persistence

### Activate “Webhook received” trigger node

To activate the "Webhook received" trigger node:

1.  Click on the node to open up its form, which will appear on the right side of the screen
2.  Click the "Generate" button within the form to activate the node, generating the necessary sections that enable you to configure the trigger

    <img src="https://lh7-rt.googleusercontent.com/docsz/AD_4nXeCWK8cGtXWG8MWjS9d7V0bygTP_dHthpA41rwi9ErVmDjT4BJuw85-JD_oluJ9nmpec3ftoT90iFW88yRv-Llv6Me1KY1g0f6aLg38FcY-OXGppYBFTsDduf3rnicCFCS1nF5Sh3WnF8bH5Wiw-eaaVMsg?key=ZuFncuUEx5YQsgFawHQwyQ" width="167" height="247" alt="">
3.  Once you click "Generate", the following new sections will appear within the form:
    *   Setup
    *   Identify contacts
    *   Save response

The following segments of this article outline the purpose of each section and how to configure them. 

### Set up your webhook

After the "Webhook received" trigger has been activated, the first section displayed is  "Setup". This section provides the information required for you to set up the necessary connectivity between your external system and SleekFlow, so your external system can send a valid webhook to trigger a flow enrollment.

You’ll need to use the following 3 items that are are provided within the "Setup" section to set up a proper webhook connection from your external system to SleekFlow:

1.  **Webhook URL -** This is the URL your external system needs to send the webhook to in order for it to be received by SleekFlow
2.  **Webhook trigger ID -** This ID allows the SleekFlow system to map the webhook to the flow, ensuring contacts are properly enrolled. Please provide this in your webhook headers.
3.  **Validation token -** This allows the SleekFlow system to verify the webhook is coming from you. Please provide this in your webhook headers.

<img src="https://lh7-rt.googleusercontent.com/docsz/AD_4nXf8vqmD7quIFTfQuVRWkD7KWkIM9wcSN3eroDaORgeiyALeZ6ibQ4uGdRjB1BjlYco-KblRcI_h1kwR92-Gbseo84JiyE238eM6eXJGk179PTgCqVXdjECVJRKnGJOTj3h5y8z6YTnuYSeHxYe0UcZv6Brh?key=ZuFncuUEx5YQsgFawHQwyQ" width="227" height="217" alt="">

You can either set up the webhook connection by including the above parameters in your HTTP request header, or you can embed the necessary parameters in a single URL and copy the URL to best suit your integration setup needs. 

<img src="https://lh7-rt.googleusercontent.com/docsz/AD_4nXfHdk5gttiAUD5erHFSOcUSI_mittXmBQcNCM0fFy49_ACzSEg3ab_wUQW7Inkkm8ucbjkgsaxOfxIBikxc0Ac_hKuWBKO7-V9MMJpf8Pd0GAp4o9jm5ZeweUBhYmxOs6ZCLM8DKROcV-GSWDLquRvO5G3N?key=ZuFncuUEx5YQsgFawHQwyQ" width="316" height="85" alt="">

Within the "Setup" section there is also a "Post request" code sample to help you configure your HTTP request to properly send the webhook to SleekFlow.

<img src="https://lh7-rt.googleusercontent.com/docsz/AD_4nXdYlacjgkXwSY8KhTz5P3a42uLJZq75MOAXXYl32g_UTkl6m97QSdbOig7uIt7rDyl2WLE6sPpdcdsOeqjETr-tLWnvdQGSmL2U4L9q9l4y7x9BJ6HyzcllfSJcXgp8g9eUhtgzczle9YRo_pMw3BySyA9G?key=ZuFncuUEx5YQsgFawHQwyQ" width="198" height="181" alt="">

The information provided in the "Setup" section will enable you to configure a valid webhook to be sent from your external system to SleekFlow, triggering your desired flow enrollments. 

### Specify contact identifier

The "Identify contacts" section allows you to configure how the system will map the webhooks it receives to corresponding contacts that are enrolled into the flow. 

<img src="https://lh7-rt.googleusercontent.com/docsz/AD_4nXc1H_s7_Ld3GN0WiEABJMG4r173qNScE5CJrDdrHVh9_SYOKSCg5xmnBjDLaN-YMsOaRJ1ZhePmV9ZzXsLopb76eLT59YYYCOys-uSNgkNAfjm4EDmWPIvHSr3j72gRVYejIjLpu6MpmOawbTDCtElG60xV?key=ZuFncuUEx5YQsgFawHQwyQ" width="180" height="244" alt="">

This section has 2 fields you must fill in:

1.  **Contact payload key -** This field allows you to specify which variable within the JSON webhook payload received by the system should be used to cross-check against the contact records in SleekFlow. We recommend using a variable that is a unique identifier for a contact, e.g., contact ID, phone number, or email. Please use the JSON dot notation here to specify which field from the webhook payload the system should extract and reference.

    💡 **JSON dot notation explainer**

    The JSON dot notation is a way to specify a particular field or value within a nested data structure like JSON. It allows you to navigate through the structure by indicating the path to the desired field using dot-separated keys.

    Format:
    The JSON dot notation begins with a $., followed by all the field names listed in sequential order to navigate through the nested data structure to get to your target field. Each field name specified in the notation is separated by a dot (e.g. $.key1.key2.key3)

    Example:
    For the payload specified below, the phone number field would be represented using the JSON dot notation like so:  $.data.profile.phone
    ```json
    {
        "data": {
            "id": "12345",
            "name": "Bob Chan",
            "profile": {
                "email": "<EMAIL>",
                "phone": "***********"
            }
        }
    }
    ```
2.  **Identifier type -** This drop-down menu allows you to select which contact property the system will cross-check your specified contact identifier against within your SleekFlow account’s contact records. Select between 3 options:
    *   Contact ID (I.e. the unique UUID of each contact provided by SleekFlow)
    *   Phone number
    *   Email

        💡 **Note on non-existent contacts**

        There may be instances where the contact identifier provided in the JSON webhook payload does not correspond to an existing contact within your SleekFlow account.

        When "Phone number" or "Email" is selected as your "Identifier type", an additional toggle - "New customer enrollment" - is provided to handle such cases.

        <img src="https://lh7-rt.googleusercontent.com/docsz/AD_4nXdwC7D5Lc7XiTcV1qgCE1Y1ZRHoC_wALyOO0r-sl-jESQtvHa7qNV5JW_nndqJdJarJgLPzGAMVCS77w7HoL6CnPk-h7vHaF9Ro_xw_sAyTT__QOsZ49E-ph-zFsWxaf-A9yCLI08fhCZ8Dm9DynZCRk7Po?key=ZuFncuUEx5YQsgFawHQwyQ" width="201" height="105" alt="">

        If "New customer enrollment" is toggled on, a phone number or email address provided in the webhook payload that cannot be matched with an existing contact on SleekFlow will automatically have a new contact record created with the provided contact identifier. The newly created contact will then be enrolled into the flow.

### Configure response persistence

The "Save response" section allows you to specify data points within the received JSON webhook payload to save and use later as a variable in the flow. This will provide you with more flexibility in creating tailored interactions with your contacts through dynamic data.

<img src="https://lh7-rt.googleusercontent.com/docsz/AD_4nXfQ3GhGLA6c2Avgwvif_ai1npmwEMnTL4qqVpd4-fiJhxuonjBCOiAqmwdv6pwtWVjrKdp0kDZY4mlQBFPamC2o71bq8dXVTqpLAcE3e71zpMdi1nQdP0Lq67XowQTlCmDzzdty5TlHvxzv58JE9Q_mUgky?key=ZuFncuUEx5YQsgFawHQwyQ" width="163" height="215" alt="">

This section allows you to add key-value pairs for values within the webhook you want to  save as a variable, as well as provide variable names to save them under. Each key-value pair consists of the following 2 fields:

*   **{{variable}} -** Specify the variable name that the system will use to store the data extracted from the webhook payload under. You can use this variable in subsequent condition and action nodes within the flow when you want to reference the values extracted from the webhook payload. Please specify your desired variable name in this field using double curly braces (i.e. **{{variable\_name}}**)
*   **$.response\_variable -** This field allows you to specify the variable to extract values from within the JSON webhook payload received by the system. Please use the JSON dot notation here to specify which field from the webhook payload the system should extract (refer to [JSON dot notation explainer](https://help.sleekflow.io/en_US/flow-triggers/configuring-integration-trigger-nodes#json-dot-notation-explainer-4)).
