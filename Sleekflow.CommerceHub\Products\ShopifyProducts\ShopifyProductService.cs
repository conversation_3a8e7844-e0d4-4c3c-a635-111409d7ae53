using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;

namespace Sleekflow.CommerceHub.Products.ShopifyProducts;

public interface IShopifyProductService
{
    Task<Product?> GetShopifyProductAsync(
        string sleekflowCompanyId,
        string storeId,
        string platformDataId);

    Task<Product> CreateAndGetShopifyProductAsync(
        string sleekflowCompanyId,
        string storeId,
        string currencyIsoCode,
        List<Image> images,
        bool isViewEnabled,
        Dictionary<string, object?> providerProduct,
        List<Dictionary<string, object?>> providerProductVariants,
        Dictionary<string, object?> metadata);

    Task<Product> PatchAndGetShopifyProductAsync(
        string productId,
        string sleekflowCompanyId,
        string currencyIsoCode,
        List<Image> images,
        bool isViewEnabled,
        Dictionary<string, object?> providerProduct,
        List<Dictionary<string, object?>> providerProductVariants,
        Dictionary<string, object?> metadata);
}

public class ShopifyProductService : IShopifyProductService, IScopedService
{
    private readonly IProductRepository _productRepository;
    private readonly IIdService _idService;

    public ShopifyProductService(
        IProductRepository productRepository,
        IIdService idService)
    {
        _productRepository = productRepository;
        _idService = idService;
    }

    public async Task<Product?> GetShopifyProductAsync(
        string sleekflowCompanyId,
        string storeId,
        string platformDataId)
    {
        var products = await _productRepository.GetObjectsAsync(
            p => p.SleekflowCompanyId == sleekflowCompanyId
            && p.StoreId == storeId
            && p.PlatformData.Type == PlatformDataTypes.Shopify
            && p.PlatformData.Id == platformDataId);

        return products.FirstOrDefault();
    }

    public async Task<Product> CreateAndGetShopifyProductAsync(
        string sleekflowCompanyId,
        string storeId,
        string currencyIsoCode,
        List<Image> images,
        bool isViewEnabled,
        Dictionary<string, object?> providerProduct,
        List<Dictionary<string, object?>> providerProductVariants,
        Dictionary<string, object?> metadata)
    {
        var categoryIds = new List<string>();
        var productVariantAttributes = new List<Product.ProductAttribute>();
        var createdAt = ((DateTimeOffset) providerProduct["CreatedAt"]!).UtcDateTime;
        string? url = null;

        var productProperties = GetCreatableAndUpdatableProductProperties(
            currencyIsoCode,
            providerProduct,
            providerProductVariants);

        return await _productRepository.UpsertAndGetAsync(
            new Product(
                _idService.GetId(SysTypeNames.Product),
                sleekflowCompanyId,
                storeId,
                categoryIds,
                productProperties.Sku,
                url,
                productProperties.Names,
                productProperties.Descriptions,
                images,
                productProperties.ProductVariantNames,
                productProperties.ProductVariantDescriptions,
                productVariantAttributes,
                productProperties.ProductVariantPrices,
                isViewEnabled,
                new List<string>
                {
                    "Active"
                },
                productProperties.PlatformData,
                metadata,
                createdAt,
                productProperties.UpdatedAt),
            sleekflowCompanyId);
    }

    public async Task<Product> PatchAndGetShopifyProductAsync(
        string productId,
        string sleekflowCompanyId,
        string currencyIsoCode,
        List<Image> images,
        bool isViewEnabled,
        Dictionary<string, object?> providerProduct,
        List<Dictionary<string, object?>> providerProductVariants,
        Dictionary<string, object?> metadata)
    {
        var productProperties = GetCreatableAndUpdatableProductProperties(
            currencyIsoCode,
            providerProduct,
            providerProductVariants);

        return await _productRepository.PatchAndGetAsync(
            productId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Replace("/images", images),
                PatchOperation.Replace("/is_view_enabled", isViewEnabled),
                PatchOperation.Replace("/names", productProperties.Names),
                PatchOperation.Replace("/descriptions", productProperties.Descriptions),
                PatchOperation.Replace("/product_variant_names", productProperties.ProductVariantNames),
                PatchOperation.Replace("/product_variant_descriptions", productProperties.ProductVariantDescriptions),
                PatchOperation.Replace("/product_variant_prices", productProperties.ProductVariantPrices),
                PatchOperation.Set("/sku", productProperties.Sku),
                PatchOperation.Replace("/updated_at", productProperties.UpdatedAt),
                PatchOperation.Replace("/platform_data", productProperties.PlatformData),
                PatchOperation.Replace("/metadata", metadata)
            });
    }

    private static (
        List<Multilingual> Names,
        List<Description> Descriptions,
        List<Multilingual> ProductVariantNames,
        List<Description> ProductVariantDescriptions,
        List<Price> ProductVariantPrices,
        string Sku,
        DateTimeOffset UpdatedAt,
        PlatformData PlatformData)
        GetCreatableAndUpdatableProductProperties(
            string currencyIsoCode,
            Dictionary<string, object?> providerProduct,
            List<Dictionary<string, object?>> providerProductVariants)
    {
        var names = new List<Multilingual>
        {
            new(languageIsoCode: "en", value: (string)providerProduct["Title"]!)
        };
        var descriptions = new List<Description>
        {
            new (
                type: "text",
                text: new Multilingual(languageIsoCode: "en", value: (string)providerProduct["Title"]!),
                image: null,
                youtube: null)
        };
        var productVariantNames = providerProductVariants
            .Select(p => new Multilingual("en", (string)p["Title"]!))
            .ToList();
        var productVariantDescriptions = providerProductVariants
            .Select(p => new Description(
                "text",
                new Multilingual("en", (string)p["Title"]!),
                null,
                null))
            .ToList();
        var productVariantPrices = providerProductVariants
            .Select(p => new Price(currencyIsoCode, (decimal)p["Price"]!))
            .ToList();
        var sku = string.Join(",", providerProductVariants.Select(p => (string)p["SKU"]!));
        var updatedAt = ((DateTimeOffset) providerProduct["UpdatedAt"]!).UtcDateTime;

        var providerProductId = (string)providerProduct["Id"]!;
        var platformData = new PlatformData(providerProductId, PlatformDataTypes.Shopify, providerProduct);

        return (
            names,
            descriptions,
            productVariantNames,
            productVariantDescriptions,
            productVariantPrices,
            sku,
            updatedAt,
            platformData);
    }
}