# [Proposal Title]

## 1. Overview

[Provide a concise summary of the proposal in 2-3 paragraphs. Explain what problem this solves and why it's important. This section should answer what, why, and how at a high level.]

## 2. Goals

- [Primary goal - the most important outcome]
- [Secondary goal]
- [Tertiary goal]
- [Additional goals as needed]

## 3. Key Considerations

[Identify important constraints, requirements, and considerations that will influence the design. This section should come early to ensure key factors are addressed before detailed design.]

### 3.1 Security

[Outline security considerations and how they'll be addressed]

### 3.2 Performance

[Discuss performance requirements and constraints]

### 3.3 Scalability

[Address scalability requirements and constraints]

### 3.4 Compatibility

[Discuss compatibility with existing systems and any backward compatibility requirements]

## 4. High-Level Architecture

[Provide a high-level description of the proposed architecture before diving into details]

### 4.1 Core Components

1. **[Component Name]**: [Brief description of purpose and responsibility]
2. **[Component Name]**: [Brief description of purpose and responsibility]
3. **[Component Name]**: [Brief description of purpose and responsibility]
4. [Add more components as needed]

### 4.2 Component Diagram

```mermaid
graph TD
    A[Component A] --> B[Component B]
    A --> C[Component C]
    B --> D[Component D]
    C --> D
```

[Explain the diagram and key relationships]

## 5. Detailed Design

### 5.1 [Component/Class/Interface Name] (`path/to/Component.cs`)

[Describe the component in detail, including its responsibilities, interactions, and design decisions]

```csharp
// Provide sample code to illustrate the implementation
public interface IExampleInterface
{
    Task<Result> DoSomethingAsync(InputType input, CancellationToken cancellationToken = default);
}
```

[Explain key aspects of this implementation, design choices, and rationale.]

### 5.2 [Component/Class/Interface Name] (`path/to/Implementation.cs`)

[Describe the component in detail]

```csharp
// Provide more sample code
public class ExampleImplementation : IExampleInterface
{
    // Implementation details
}
```

[Continue with additional components as needed]

## 6. Integration and Configuration

[Explain how the new feature/system integrates with existing code. Include:]

### 6.1 Changes to Existing Components

[List specific changes needed to existing components, with file paths]

### 6.2 Configuration Options

[Detail configuration options and how they affect behavior]

### 6.3 Dependency Registration

[Explain how to register the new components in the DI container]

### 6.4 Database Changes

[Describe any changes to database schema or data]

## 7. Usage Examples

### 7.1 [Example Use Case/Scenario]

```csharp
// Example usage code
var service = new ExampleService();
await service.DoSomethingAsync(input);
```

[Explain what this example demonstrates]

### 7.2 [Another Example]

[Include additional examples with realistic data]

## 8. Implementation Plan

[Break down the implementation into manageable phases]

### 8.1 Phase 1: [Phase Name]

**Tasks:**
- [ ] Task 1: [Brief description]
- [ ] Task 2: [Brief description]
- [ ] Task 3: [Brief description]

**Dependencies:**
- [List any dependencies or prerequisites]

**Expected Outcome:**
[Describe what will be completed at the end of this phase, including any deliverables, functionality, or milestone achievements]

**Time Estimate:** [X days/weeks]

### 8.2 Phase 2: [Phase Name]

**Tasks:**
- [ ] Task 1: [Brief description]
- [ ] Task 2: [Brief description]
- [ ] Task 3: [Brief description]

**Dependencies:**
- [List any dependencies or prerequisites, including completion of previous phases if relevant]

**Expected Outcome:**
[Describe what will be completed at the end of this phase, including any deliverables, functionality, or milestone achievements]

**Time Estimate:** [X days/weeks]

### 8.3 Implementation Checklist Summary

**Phase 1:**
- [ ] All Phase 1 tasks completed
- [ ] Unit tests passing
- [ ] Code reviewed

**Phase 2:**
- [ ] All Phase 2 tasks completed
- [ ] Integration tests passing
- [ ] Documentation updated

[Add additional phases as needed]

## 9. Testing Strategy

[Describe how the implementation will be tested]

### 9.1 Unit Tests

[Describe approach to unit testing, including key test cases]

### 9.2 Integration Tests

[Describe approach to integration testing]

### 9.3 Performance Tests

[Describe any performance or load testing planned]

## 10. Alternatives Considered

[Discuss alternative approaches that were considered and why they were rejected]

## 11. Future Enhancements

[Outline potential future improvements that are out of scope for the initial implementation]