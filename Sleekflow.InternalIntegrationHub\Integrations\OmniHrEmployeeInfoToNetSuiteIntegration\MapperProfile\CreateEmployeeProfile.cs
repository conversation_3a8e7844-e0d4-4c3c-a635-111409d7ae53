using AutoMapper;
using Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile.TypeConverter;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Integrations;
using Sleekflow.InternalIntegrationHub.Models.OmniHr.Integrations;

namespace Sleekflow.InternalIntegrationHub.Integrations.OmniHrEmployeeInfoToNetSuiteIntegration.MapperProfile;

public class CreateEmployeeProfile : Profile
{
#pragma warning disable JA1002
    public CreateEmployeeProfile()
#pragma warning restore JA1002
    {
        CreateMap<string, Subsidiary>().ConvertUsing(new SubsidiaryTypeConverter());
        CreateMap<string, DefaultExpenseReportCurrency>().ConvertUsing(new ReportCurrencyTypeConverter());
        CreateMap<FullEmployeeInfo, CreateEmployeeRequest>()
            .ForMember(f => f.ExternalId, e => e.MapFrom(p => p.ExternalId))
            .ForMember(f => f.FirstName, e => e.MapFrom(p => p.FirstName))
            .ForMember(f => f.LastName, e => e.MapFrom(p => p.LastName))
            .ForMember(f => f.Email, e => e.MapFrom(p => p.PrimaryEmail!.Value))
            .ForMember(f => f.Subsidiary, e => e.MapFrom(p => p.LocationName))
            .ForMember(f => f.DefaultExpenseReportCurrency, e => e.MapFrom(p => p.Currency));
    }
}

