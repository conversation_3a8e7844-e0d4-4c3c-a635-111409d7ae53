using Newtonsoft.Json;

namespace Sleekflow.Auth0.BulkImporter.Models;

public class Auth0JobResponse
{
    [JsonProperty("type")]
    public string? Type { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("connection_id")]
    public string? ConnectionId { get; set; }

    [JsonProperty("connection")]
    public string? Connection { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset? CreatedAt { get; set; }

    [JsonProperty("id")]
    public string? Id { get; set; }
}