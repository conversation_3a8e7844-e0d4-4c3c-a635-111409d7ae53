﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.0</TargetFramework>
        <Nullable>enable</Nullable>
        <LangVersion>10</LangVersion>
        <EnforceExtendedAnalyzerRules>true</EnforceExtendedAnalyzerRules>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.CodeAnalysis.CSharp.Workspaces" Version="4.5.0" PrivateAssets="all" />
        <PackageReference Include="System.Collections.Immutable" Version="8.0.0" />
    </ItemGroup>

    <ItemGroup>
        <Content Remove="Templates\controller.cs.liquid" />
    </ItemGroup>
    <ItemGroup>
        <EmbeddedResource Include="Templates\controller.cs.liquid" Type="Non-Resx" WithCulture="false" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Scriban" Version="5.12.1" GeneratePathProperty="true" PrivateAssets="all" />
    </ItemGroup>

    <PropertyGroup>
        <GetTargetPathDependsOn>$(GetTargetPathDependsOn);GetDependencyTargetPaths</GetTargetPathDependsOn>
    </PropertyGroup>

    <Target Name="GetDependencyTargetPaths">
        <ItemGroup>
            <TargetPathWithTargetPlatformMoniker Include="$(PKGScriban)\lib\netstandard2.0\Scriban.dll" IncludeRuntimeDependency="false" />
        </ItemGroup>
    </Target>

</Project>
