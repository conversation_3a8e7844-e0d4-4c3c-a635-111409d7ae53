﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Orders;
using Sleekflow.CommerceHub.Orders;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Orders;

[TriggerGroup(ControllerNames.Orders)]
public class GetUserOrders
    : ITrigger<
        GetUserOrders.GetUserOrdersInput,
        GetUserOrders.GetUserOrdersOutput>
{
    private readonly IOrderService _orderService;

    public GetUserOrders(
        IOrderService orderService)
    {
        _orderService = orderService;
    }

    public class GetUserOrdersInput : IHasSleekflowUserProfileId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [JsonConstructor]
        public GetUserOrdersInput(
            string sleekflowCompanyId,
            string sleekflowUserProfileId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
        }
    }

    public class GetUserOrdersOutput
    {
        [JsonProperty("orders")]
        public List<OrderDto> Orders { get; set; }

        [JsonConstructor]
        public GetUserOrdersOutput(List<Order> orders)
        {
            Orders = orders.Select(o => new OrderDto(o)).ToList();
        }
    }

    public async Task<GetUserOrdersOutput> F(GetUserOrdersInput createProductsInput)
    {
        return new GetUserOrdersOutput(
            await _orderService.GetUserOrdersAsync(
                createProductsInput.SleekflowCompanyId,
                createProductsInput.SleekflowUserProfileId));
    }
}