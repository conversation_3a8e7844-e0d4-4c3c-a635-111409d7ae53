using Newtonsoft.Json;

namespace Sleekflow.Integrator.Hubspot.Services.Models;

public class HubspotOwnerListOutput
{
    [JsonProperty("results")]
    public List<Dictionary<string, object?>> Results { get; set; }

    [JsonProperty("paging")]
    public Paging? Paging { get; set; }

    [JsonConstructor]
    public HubspotOwnerListOutput(List<Dictionary<string, object?>> results, Paging? paging)
    {
        Results = results;
        Paging = paging;
    }
}