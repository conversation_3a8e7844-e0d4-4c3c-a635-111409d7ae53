﻿using System.Text;
using Sleekflow.DependencyInjection;

namespace Sleekflow.Loggings;

public interface ILoggerConfig
{
    string IsLogAnalyticsEnabled { get; }

    string WorkspaceId { get; }

    string AuthenticationId { get; }

    string IsGoogleCloudLoggingEnabled { get; }

    string GoogleCloudProjectId { get; }

    string GoogleCloudCredentialJson { get; }
}

public class LoggerConfig : IConfig, ILoggerConfig
{
    public string IsLogAnalyticsEnabled { get; }

    public string WorkspaceId { get; }

    public string AuthenticationId { get; }

    public string IsGoogleCloudLoggingEnabled { get; }

    public string GoogleCloudProjectId { get; }

    public string GoogleCloudCredentialJson { get; }

    public LoggerConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        IsLogAnalyticsEnabled =
            Environment.GetEnvironmentVariable("LOGGER_IS_LOG_ANALYTICS_ENABLED", target)
            ?? "FALSE";
        WorkspaceId =
            Environment.GetEnvironmentVariable("LOGGER_WORKSPACE_ID", target)
            ?? string.Empty;
        AuthenticationId =
            Environment.GetEnvironmentVariable("LOGGER_AUTHENTICATION_ID", target)
            ?? string.Empty;

        IsGoogleCloudLoggingEnabled =
            Environment.GetEnvironmentVariable("LOGGER_IS_GOOGLE_CLOUD_LOGGING_ENABLED", target)
            ?? "FALSE";
        GoogleCloudProjectId =
            Environment.GetEnvironmentVariable("LOGGER_GOOGLE_CLOUD_PROJECT_ID", target)
            ?? string.Empty;
        try
        {
            var base64EncodedGoogleCloudCredentialJson =
                Environment.GetEnvironmentVariable("LOGGER_GOOGLE_CLOUD_CREDENTIAL_JSON", target)
                ?? string.Empty;

            var bytes = Convert.FromBase64String(base64EncodedGoogleCloudCredentialJson);
            var googleCloudCredentialJson = Encoding.UTF8.GetString(bytes);

            GoogleCloudCredentialJson = googleCloudCredentialJson;
        }
        catch
        {
            GoogleCloudCredentialJson = string.Empty;
        }

        GoogleCloudProjectId =
            Environment.GetEnvironmentVariable("LOGGER_GOOGLE_CLOUD_PROJECT_ID", target)
            ?? string.Empty;
    }
}