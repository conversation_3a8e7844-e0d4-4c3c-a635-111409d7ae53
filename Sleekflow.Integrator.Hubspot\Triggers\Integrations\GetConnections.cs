﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Connections;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Connections;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class GetConnections : ITrigger
{
    private readonly IHubspotConnectionService _hubspotConnectionService;

    public GetConnections(
        IHubspotConnectionService hubspotConnectionService)
    {
        _hubspotConnectionService = hubspotConnectionService;
    }

    public class GetConnectionsInput : IHasSleekflowCompanyId
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonConstructor]
        public GetConnectionsInput(
            string sleekflowCompanyId)
        {
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class HubspotConnectionDto : IHasSleekflowCompanyId
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("organization_id")]
        public string? OrganizationId { get; set; }

        [JsonProperty("name")]
        public string? Name { get; set; }

        [JsonProperty("environment")]
        public string Environment { get; set; }

        [JsonProperty("is_active")]
        public bool IsActive { get; set; }

        [JsonConstructor]
        public HubspotConnectionDto(
            HubspotConnection connection)
        {
            Id = connection.Id;
            SleekflowCompanyId = connection.SleekflowCompanyId;
            OrganizationId = connection.OrganizationId;
            Name = connection.Name;
            Environment = connection.Environment;
            IsActive = connection.IsActive;
        }
    }

    public class GetConnectionsOutput
    {
        [JsonProperty("connections")]
        [Required]
        public List<HubspotConnectionDto> Connections { get; set; }

        [JsonConstructor]
        public GetConnectionsOutput(
            List<HubspotConnectionDto> connections)
        {
            Connections = connections;
        }
    }

    public async Task<GetConnectionsOutput> F(
        GetConnectionsInput getConnectionsInput)
    {
        var connections =
            await _hubspotConnectionService.GetConnectionsAsync(getConnectionsInput.SleekflowCompanyId);

        return new GetConnectionsOutput(connections.Select(c => new HubspotConnectionDto(c)).ToList());
    }
}