using Microsoft.OpenApi.Readers;
using Sleekflow.KrakenD.Generator.InternalObjects;

namespace Sleekflow.KrakenD.Generator;

internal class KrakendValidator
{
    private const string SleekflowPath = "../";
    private const string PublicApiGateway = "PublicApiGateway";

    private readonly List<Healthz> _healthz;
    private readonly List<Endpoint> _endpoints;
    private readonly List<string> _managedEndpoints;

    public KrakendValidator(List<Healthz> healthz, List<Endpoint> endpoints)
        : this(healthz, endpoints, new List<string>())
    {
    }

    public KrakendValidator(List<Healthz> healthz, List<Endpoint> endpoints, List<string> managedEndpoints)
    {
        _healthz = healthz;
        _endpoints = endpoints;
        _managedEndpoints = managedEndpoints;
    }

    public void Validate()
    {
        var directory = new DirectoryInfo(SleekflowPath).GetFiles("v1.yaml", SearchOption.AllDirectories);
        var openApiStringReader = new OpenApiStringReader(new OpenApiReaderSettings());

        var apps = new List<App>();
        foreach (var fileInfo in directory)
        {
            var path = fileInfo.FullName.Replace("\\", "/");

            var namespaceName = path
                .Substring(path.LastIndexOf("Sleekflow/", StringComparison.InvariantCulture) + "Sleekflow/".Length)
                .Replace("/.swagger/v1.yaml", string.Empty);

            var appName = namespaceName.Replace("Sleekflow.", string.Empty);
            if (appName.StartsWith("Integrator."))
            {
                appName = appName.Remove(0, "Integrator.".Length) + ".Integrator";
            }
            else if (appName.EndsWith(".Integrator"))
            {
                // ignore
            }
            else if (appName.EndsWith(".VoicePoc"))
            {
                // ignore
            }
            else if (appName.EndsWith("FlowHub.Executor"))
            {
                // ignore
            }
            else if (appName.Contains('.'))
            {
                throw new Exception($"Please handle this appName. {appName}");
            }

            if (_managedEndpoints.Any() && !_managedEndpoints.Contains(appName))
            {
                continue;
            }

            apps.Add(new App(namespaceName, appName, path));
        }

        // Healthz Definitions Validator
        foreach (var (_, appName, _) in apps)
        {
            var kebabCaseAppName = appName.ToKebabCase();

            if (_healthz.All(h => h.Group != kebabCaseAppName))
            {
                Console.WriteLine(
                    $"::warning title=HealthzValidator::Healthz Missing {kebabCaseAppName} in krakend.json. Please consider adding it back.");
            }
        }

        // ManagedEndpoint Definitions Validator
        foreach (var (_, appName, swaggerDocFullPath) in apps)
        {
            if (appName.Contains("integrator", StringComparison.InvariantCultureIgnoreCase))
            {
                continue;
            }

            var openApiDocument =
                openApiStringReader.Read(File.ReadAllText(swaggerDocFullPath), out _);

            var kebabCaseAppName = appName.ToKebabCase();
            var paths = openApiDocument.Paths.Select(p => p.Key).ToList();

            // Special case validator for PublicApiGateway
            if (appName.Contains(PublicApiGateway, StringComparison.InvariantCulture))
            {
                var appPublicApiGatewayEndpoints = _endpoints
                    .Where(e => e is PublicApiGatewayEndpoint)
                    .Cast<PublicApiGatewayEndpoint>()
                    .Where(me => me.Group == kebabCaseAppName).ToList();

                foreach (var path in paths.Where(
                             path =>
                                 Filters.IsValidatingPath(path)
                                 &&
                                 appPublicApiGatewayEndpoints.All(
                                     ame => !path.StartsWith($"/{ame.ServiceType}/{ame.Subgroup}/"))))
                {
                    Console.WriteLine(
                        $"::warning title=ManagedEndpointValidator::ManagedEndpoint Missing {kebabCaseAppName} {path} in krakend.json. Please consider adding it back.");
                }

                continue;
            }

            var managedEndpoints = _endpoints.Where(e => e is ManagedEndpoint).Cast<ManagedEndpoint>().ToList();
            var managedEndpointSubgroups = managedEndpoints
                .Where(me => me.Group == kebabCaseAppName)
                .Select(me => $"/{me.Subgroup}/")
                .ToList();

            foreach (var path in paths.Where(
                         path =>
                             Filters.IsValidatingPath(path)
                             && managedEndpointSubgroups.All(mes => !path.StartsWith(mes))))
            {
                Console.WriteLine(
                    $"::warning title=ManagedEndpointValidator::ManagedEndpoint Missing {kebabCaseAppName} {path} in krakend.json. Please consider adding it back.");
            }
        }
    }
}