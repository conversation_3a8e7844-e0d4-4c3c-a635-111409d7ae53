﻿namespace Sleekflow.RateLimits.ThrottledConsumers;

public class LimitMonitor
{
    private readonly int _remainLimit;
    private readonly int _allowedLimit;
    private readonly List<int> _monitorLimits;

    public LimitMonitor(int remainLimit, int allowedLimit, List<int> monitorLimits)
    {
        _remainLimit = remainLimit;
        _allowedLimit = allowedLimit;
        _monitorLimits = monitorLimits;
    }


    public int ReachLimit()
    {
        var consumed = _allowedLimit - _remainLimit;
        var matchedLimit = _monitorLimits.Where(l => l <= consumed).OrderByDescending(l => l).FirstOrDefault();

        return matchedLimit;
    }
}