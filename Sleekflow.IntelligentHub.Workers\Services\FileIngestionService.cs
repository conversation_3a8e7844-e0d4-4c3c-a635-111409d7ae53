using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Blobs;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.Documents.FileDocuments;
using Sleekflow.IntelligentHub.Documents.FileDocuments.Ingestion;
using Sleekflow.IntelligentHub.Models.Categories;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers.FileIngestion;
using Sleekflow.IntelligentHub.TextEnrichments;

namespace Sleekflow.IntelligentHub.Workers.Services;

public interface IFileIngestionService
{
    Task<IFileIngestionProgress> ProcessFileDocument(
        string sleekflowCompanyId,
        string documentId,
        string blobUrl,
        object? fileIngestionProgress);
}

public class FileIngestionService : IScopedService, IFileIngestionService
{
    private readonly ILogger<FileIngestionService> _logger;
    private readonly IKnowledgeSourceFactory _knowledgeSourceFactory;
    private readonly ITextTranslationService _textTranslationService;
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IFileDocumentChunkService _fileDocumentChunkService;
    private readonly IAzureBlobClientFactory _azureBlobClientFactory;

    public FileIngestionService(
        ILogger<FileIngestionService> logger,
        IKnowledgeSourceFactory knowledgeSourceFactory,
        ITextTranslationService textTranslationService,
        IKbDocumentService kbDocumentService,
        IFileDocumentChunkService fileDocumentChunkService,
        IAzureBlobClientFactory azureBlobClientFactory)
    {
        _logger = logger;
        _knowledgeSourceFactory = knowledgeSourceFactory;
        _textTranslationService = textTranslationService;
        _kbDocumentService = kbDocumentService;
        _fileDocumentChunkService = fileDocumentChunkService;
        _azureBlobClientFactory = azureBlobClientFactory;
    }

    public async Task<IFileIngestionProgress>
        ProcessFileDocument(
            string sleekflowCompanyId,
            string documentId,
            string blobUrl,
            object? fileIngestionProgress)
    {
        _logger.LogInformation(
            "ProcessFileDocument converting {DocumentId} {IFileIngestionProgress}",
            documentId,
            JsonConvert.SerializeObject(fileIngestionProgress));

        var fileDocument = (FileDocument) await _kbDocumentService.GetDocumentAsync(sleekflowCompanyId, documentId);

        _logger.LogInformation(
            "Loading blob for document: {DocumentId}, blobId: {BlobId}, using provided URL",
            documentId,
            fileDocument.BlobId);

        var blobStream = await LoadBlobAsStreamAsync(blobUrl);
        var knowledgeSource = _knowledgeSourceFactory.GetKnowledgeSource(fileDocument.BlobType);
        var (markdowns, updatedFileIngestionProgress) =
            await knowledgeSource.Ingest(blobStream, fileIngestionProgress);

        _logger.LogInformation(
            "ProcessFileDocument conversion finished for {DocumentId} {IFileIngestionProgress}",
            documentId,
            JsonConvert.SerializeObject(updatedFileIngestionProgress));
        var chunkIds = await StoreMarkdownsAsFileDocumentChunks(
            sleekflowCompanyId,
            fileDocument.BlobId,
            documentId,
            markdowns);

        return updatedFileIngestionProgress;
    }

    private async Task<List<string>> StoreMarkdownsAsFileDocumentChunks(
        string sleekflowCompanyId,
        string blobId,
        string documentId,
        string[] markdowns)
    {
        var chunkIds = new ConcurrentBag<string>();

        await Parallel.ForEachAsync(
            markdowns,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 8
            },
            async (markdown, ct) =>
            {
                // translate
                var contentEn = await _textTranslationService.TranslateByAzureTranslationServiceAsync(
                    TranslationSupportedLanguages.English,
                    markdown);

                var documentChunk = await _fileDocumentChunkService.CreateFileDocumentChunkAsync(
                    sleekflowCompanyId,
                    blobId,
                    documentId,
                    markdown,
                    contentEn,
                    new List<Category>(),
                    new Dictionary<string, object?>());

                chunkIds.Add(documentChunk.Id);
            });

        return chunkIds.ToList();
    }

    private async Task<Stream> LoadBlobAsStreamAsync(string blobUrl)
    {
        _logger.LogInformation("LoadBlobAsStreamAsync {BlobUrl}", blobUrl);

        var blobClient = _azureBlobClientFactory.GetBlobClient(blobUrl);
        var blobStream = new MemoryStream();
        await blobClient.DownloadToAsync(blobStream);
        blobStream.Position = 0;

        return blobStream;
    }
}