using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.FlowHub;
using Sleekflow.Mvc.Func.Abstractions;

namespace Sleekflow.FlowHub.Cores;

public interface IDepthFuncFilter : IFuncFilter
{
    public const string XSleekflowFlowHubDepth = "X-Sleekflow-Flow-Hub-Depth";
}

public class DepthFuncFilter : IDepthFuncFilter, IScopedService
{
    private readonly IDepthContext _depthContext;

    public DepthFuncFilter(
        IDepthContext depthContext)
    {
        _depthContext = depthContext;
    }

    public Task FilterAsync(HttpRequest httpRequest)
    {
        var headers = httpRequest.Headers;

        _depthContext.Depth = headers.TryGetValue(IDepthFuncFilter.XSleekflowFlowHubDepth, out var stringValues)
            ? int.Parse(stringValues.ToString())
            : 1;

        if (_depthContext.Depth > 8)
        {
            throw new SfInfiniteDetectionException();
        }

        return Task.CompletedTask;
    }
}