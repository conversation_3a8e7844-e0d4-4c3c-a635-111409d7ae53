using System.Text;
using System.Web;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.InternalIntegrationHub.Https;
using Sleekflow.JsonConfigs;

namespace Sleekflow.InternalIntegrationHub.Clients;

public interface IExternalClient
{
    Task<T?> GetAsync<T>(
        string baseUrl,
        string path,
        Dictionary<string, object>? queryParams,
        Dictionary<string, string?> headers);

    Task<T?> PostAsync<T>(
        string baseUrl,
        string path,
        object args,
        Dictionary<string, string?> headers);
}

public class ExternalClient
    : IExternalClient, IScopedService
{
    private readonly ILogger<ExternalClient> _logger;
    private readonly HttpClient _httpClient;

    public ExternalClient(
        ILogger<ExternalClient> logger,
        IHttpClientFactory httpClientFactory)
    {
        _logger = logger;
        _httpClient = httpClientFactory.CreateClient("default-internal-integration-hub-handler");
    }

    public async Task<T?> GetAsync<T>(
        string baseUrl,
        string path,
        Dictionary<string, object>? queryParams = null,
        Dictionary<string, string?> headers = null)
    {
        var url = baseUrl + path;
        var urlBuilder = new UriBuilder(url);
        var query = HttpUtility.ParseQueryString(urlBuilder.Query);
        if (queryParams != null)
        {
            foreach (var keyValuePair in queryParams)
            {
                query[keyValuePair.Key] = keyValuePair.Value.ToString();
            }
        }

        urlBuilder.Query = query.ToString();
        var resMsg = await HttpPolicies.HttpTransientErrorRetryPolicy.ExecuteAsync(
            async () =>
            {
                var reqMsg = new HttpRequestMessage(
                    HttpMethod.Get,
                    urlBuilder.ToString());

                _logger.LogInformation(
                    "IIHClient: {CommandName} {ArgsJson}",
                    urlBuilder.Path,
                    urlBuilder.Query);

                var response = await _httpClient.SendAsync(reqMsg);

                _logger.LogInformation(
                    "IIHClient: {CommandName} {ArgsJson} {StatusCode}",
                    urlBuilder.Path,
                    urlBuilder.Query,
                    response.StatusCode);

                response.EnsureSuccessStatusCode();

                return response;
            });

        var responseStr = await resMsg.Content.ReadAsStringAsync();
        var responseObj = JsonConvert.DeserializeObject<T>(responseStr);

        _logger.LogInformation(
            "IIHClient: {CommandName} {ArgsJson} {ResponseStr}",
            urlBuilder.Path,
            urlBuilder.Query,
            responseStr);

        return responseObj;
    }


    public async Task<T?> PostAsync<T>(
        string baseUrl,
        string path,
        object args,
        Dictionary<string, string?> headers)
    {
        var url = baseUrl + path;
        var urlBuilder = new UriBuilder(url);
        var argsJson = JsonConvert.SerializeObject(
            args,
            JsonConfig.DefaultJsonSerializerSettings);

        var resMsg = await HttpPolicies.HttpTransientErrorRetryPolicy
            .ExecuteAsync(
                async () =>
                {
                    var reqMsg = new HttpRequestMessage(
                        HttpMethod.Post,
                        urlBuilder.ToString());

                    foreach (var (key, value) in headers)
                    {
                        reqMsg.Headers.Add(key, value ?? string.Empty);
                    }

                    reqMsg.Content = new StringContent(
                        argsJson,
                        Encoding.UTF8,
                        "application/json");

                    _logger.LogInformation(
                        "IIHClient: {CommandName} {ArgsJson}",
                        urlBuilder.Path,
                        argsJson);

                    var response = await _httpClient.SendAsync(reqMsg);

                    _logger.LogInformation(
                        "IIHClient: {CommandName} {ArgsJson} {StatusCode}",
                        urlBuilder.Path,
                        argsJson,
                        response.StatusCode);

                    response.EnsureSuccessStatusCode();

                    return response;
                });

        var responseStr = await resMsg.Content.ReadAsStringAsync();
        var responseObj = await resMsg.Content.ReadFromJsonAsync<T>();

        _logger.LogInformation(
            "IIHClient: {CommandName} {ArgsJson} {ResponseStr}",
            urlBuilder.Path,
            argsJson,
            responseStr);

        return responseObj;
    }
}