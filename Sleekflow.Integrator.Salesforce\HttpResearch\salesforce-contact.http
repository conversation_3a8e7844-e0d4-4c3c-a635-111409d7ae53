### Get Basic Object Information

GET {{instance_url}}/services/data/v54.0/sobjects/Contact/ HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Get a List of Fields - Contact

GET {{instance_url}}/services/data/v54.0/sobjects/Contact/describe HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Execute a SOQL Query for counting Contacts

GET {{instance_url}}/services/data/v54.0/query?q=SELECT+COUNT(Id)+from+Contact HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Execute a SOQL Query for multiple Contacts

GET {{instance_url}}/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+from+Contact+LIMIT+10 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Execute a SOQL Query for one Contact

GET {{instance_url}}/services/data/v54.0/query?q=SELECT+FIELDS(ALL)+from+Contact+WHERE+Id+=+'0035i000005PauxAAC'+LIMIT+10 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1

### Update a Field on a Record

PATCH {{instance_url}}/services/data/v54.0/sobjects/Contact/0035i000005PauxAAC HTTP/1.1
Authorization: Bearer {{access_token}}
Content-Type: application/json

{
  "Phone": "+852 61096623"
}

### Get an Object

GET {{instance_url}}/services/data/v54.0/sobjects/Contact/0035i000005PauxAAC HTTP/1.1
Authorization: Bearer {{access_token}}
Content-Type: application/json

### Delete an Object

DELETE {{instance_url}}/services/data/v54.0/sobjects/Contact/0035i0000050fNtAAI HTTP/1.1
Authorization: Bearer {{access_token}}
Content-Type: application/json

### Get Updated Objects

GET {{instance_url}}/services/data/v54.0/sobjects/Contact/updated/?start=2022-05-05T00%3A00%3A00%2B00%3A00&end=2022-05-06T00%3A00%3A00%2B00%3A00 HTTP/1.1
Authorization: Bearer {{access_token}}
X-PrettyPrint: 1