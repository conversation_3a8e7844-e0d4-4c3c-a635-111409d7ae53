using Microsoft.AspNetCore.Mvc;
using Sleekflow.EmailHub.Outlook.Authentications;

namespace Sleekflow.EmailHub.Outlook.Controllers;

[ApiVersion("1.0")]
[ApiController]
[Route("[controller]")]
public class OutlookAuthenticationController : ControllerBase
{
    private readonly IOutlookAuthenticationService _outlookAuthenticationService;
    private readonly ILogger<OutlookAuthenticationController> _logger;

    public OutlookAuthenticationController(
        IOutlookAuthenticationService outlookAuthenticationService,
        ILogger<OutlookAuthenticationController> logger)
    {
        _outlookAuthenticationService = outlookAuthenticationService;
        _logger = logger;
    }

    [HttpGet]
    [Route("OutlookAuthCallback")]
    public async Task<IActionResult> OutlookAuthCallback()
    {
        var code = HttpContext.Request.Query["code"].ToString();
        var state = HttpContext.Request.Query["state"].ToString();
        var sessionState = HttpContext.Request.Query["session_state"].ToString();
        _logger.LogInformation(
            "received outlook OutlookAuthCallback, code:{code}, state: {state}",
            code,
            state);
        if (string.IsNullOrEmpty(code))
        {
            return BadRequest("Authorization code not found");
        }

        await _outlookAuthenticationService.HandleAuthCallbackAndStoreAsync(code, state);

        return Ok();
    }
}