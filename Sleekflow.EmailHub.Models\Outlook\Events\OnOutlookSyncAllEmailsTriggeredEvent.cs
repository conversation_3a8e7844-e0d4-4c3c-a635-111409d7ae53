using Sleekflow.Events;

namespace Sleekflow.EmailHub.Models.Outlook.Events;

public class OnOutlookSyncAllTriggeredEvent : IEvent
{
    public string SleekflowCompanyId { get; set; }

    public string EmailAddress { get; set; }

    public OnOutlookSyncAllTriggeredEvent(
        string emailAddress,
        string sleekflowCompanyId)
    {
        EmailAddress = emailAddress;
        SleekflowCompanyId = sleekflowCompanyId;
    }
}