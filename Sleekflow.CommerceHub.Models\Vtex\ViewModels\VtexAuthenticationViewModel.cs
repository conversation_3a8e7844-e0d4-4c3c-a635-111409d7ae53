﻿using Newtonsoft.Json;

namespace Sleekflow.CommerceHub.Models.Vtex.ViewModels;

public class VtexAuthenticationViewModel
{
    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("id")]
    public string Id { get; set; }

    [JsonProperty("title")]
    public string Title { get; set; }

    [JsonProperty("credential")]
    public VtexCredential Credential { get; set; }

    [JsonProperty("is_active")]
    public bool IsActive { get; set; }

    [JsonProperty("created_at")]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonConstructor]
    public VtexAuthenticationViewModel(
        string sleekflowCompanyId,
        string id,
        string title,
        VtexCredential credential,
        bool isActive,
        DateTimeOffset createdAt)
    {
        SleekflowCompanyId = sleekflowCompanyId;
        Id = id;
        Title = title;
        Credential = credential;
        IsActive = isActive;
        CreatedAt = createdAt;
    }

    public VtexAuthenticationViewModel(VtexAuthentication authentication, bool isActive)
    {
        SleekflowCompanyId = authentication.SleekflowCompanyId;
        Id = authentication.Id;
        Title = authentication.Title;
        Credential = authentication.Credential;
        IsActive = isActive;
        CreatedAt = authentication.CreatedAt;
    }
}