﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.CrmHubConfigs;

public class UsageLimit
{
    public const string PropertyNameCustomObjectMaximumSchemaNum = "custom_object_maximum_schema_num";
    public const string PropertyNameCustomObjectMaximumPropertyNumPerSchema = "custom_object_maximum_property_num_per_schema";
    public const string PropertyNameCustomObjectMaximumSchemafulObjectNumPerSchema = "custom_object_maximum_schemaful_object_num_per_schema";
    public const string PropertyNameCustomObjectMaximumSchemafulObjectNumPerCompany = "custom_object_maximum_schemaful_object_num_per_company";
    public const string PropertyNameCustomObjectMaximumArrayObjectArraySize = "custom_object_maximum_array_object_array_size";

    [JsonProperty(PropertyNameCustomObjectMaximumSchemaNum)]
    public int? CustomObjectMaximumSchemaNum { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumPropertyNumPerSchema)]
    public int? CustomObjectMaximumPropertyNumPerSchema { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumSchemafulObjectNumPerSchema)]
    public int? CustomObjectMaximumSchemafulObjectNumPerSchema { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumSchemafulObjectNumPerCompany)]
    public int? CustomObjectMaximumSchemafulObjectNumPerCompany { get; set; }

    [JsonProperty(PropertyNameCustomObjectMaximumArrayObjectArraySize)]
    public int? CustomObjectMaximumArrayObjectArraySize { get; set; }

    [JsonConstructor]
    public UsageLimit(
        int customObjectMaximumSchemaNum,
        int customObjectMaximumPropertyNumPerSchema,
        int customObjectMaximumSchemafulObjectNumPerSchema,
        int customObjectMaximumSchemafulObjectNumPerCompany,
        int customObjectMaximumArrayObjectArraySize)
    {
        CustomObjectMaximumSchemaNum = customObjectMaximumSchemaNum;
        CustomObjectMaximumPropertyNumPerSchema = customObjectMaximumPropertyNumPerSchema;
        CustomObjectMaximumSchemafulObjectNumPerSchema = customObjectMaximumSchemafulObjectNumPerSchema;
        CustomObjectMaximumSchemafulObjectNumPerCompany = customObjectMaximumSchemafulObjectNumPerCompany;
        CustomObjectMaximumArrayObjectArraySize = customObjectMaximumArrayObjectArraySize;
    }
}