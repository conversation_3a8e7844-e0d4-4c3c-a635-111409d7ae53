using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Agents;
using Microsoft.SemanticKernel.Agents.Chat;
using Microsoft.SemanticKernel.ChatCompletion;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Companies.CompanyConfigs;
using Sleekflow.IntelligentHub.FaqAgents.Chats.Enrichers;
using Sleekflow.IntelligentHub.Kernels;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Plugins;
using Sleekflow.IntelligentHub.Plugins.Knowledges;
using Sleekflow.IntelligentHub.Utils;
using Sleekflow.Models.Chats;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;

public interface ILongAgentCollaborationDefinition : IAgentCollaborationDefinition
{
}

public class LongAgentCollaborationDefinition
    : BaseAgentCollaborationDefinition, ILongAgentCollaborationDefinition, IScopedService
{
    private const string HistoryVariable = "history";

    private readonly IAgentReviewerToolsPlugin _agentReviewerToolsPlugin;
    private readonly IPromptExecutionSettingsService _promptExecutionSettingsService;
    private readonly ILongAgentCollaborationChatCacheService _longAgentCollaborationChatCacheService;
    private readonly ILongAgentKnowledgePlugin _longAgentKnowledgePlugin;

    public LongAgentCollaborationDefinition(
        ILogger<LongAgentCollaborationDefinition> logger,
        Kernel kernel,
        ISummaryPlugin summaryPlugin,
        ILanguagePlugin languagePlugin,
        IAgentDurationTracker agentDurationTracker,
        ICompanyConfigService companyConfigService,
        IAgentReviewerToolsPlugin agentReviewerToolsPlugin,
        IPromptExecutionSettingsService promptExecutionSettingsService,
        ILongAgentCollaborationChatCacheService longAgentCollaborationChatCacheService,
        IChatHistoryEnricherFactory enricherFactory,
        IFileContentExtractionPlugin fileContentExtractionPlugin,
        ILongAgentKnowledgePlugin longAgentKnowledgePlugin)
        : base(
            logger,
            kernel,
            summaryPlugin,
            languagePlugin,
            agentDurationTracker,
            companyConfigService,
            enricherFactory,
            fileContentExtractionPlugin)
    {
        _agentReviewerToolsPlugin = agentReviewerToolsPlugin;
        _promptExecutionSettingsService = promptExecutionSettingsService;
        _longAgentCollaborationChatCacheService = longAgentCollaborationChatCacheService;
        _longAgentKnowledgePlugin = longAgentKnowledgePlugin;
    }

    public override Task<List<Agent>> CreateAgents(
        Kernel kernel,
        List<SfChatEntry> chatEntries,
        string sleekflowCompanyId,
        AgentCollaborationConfig agentCollaborationConfig)
    {
        var salesStrategyAgent = LongAgentDefinitions.GetSalesStrategyAgent(
            kernel,
            "SalesStrategyAgent",
            GetPromptExecutionSettings("SalesStrategyAgent"),
            agentCollaborationConfig.Tone);
        var salesAgent = LongAgentDefinitions.GetSalesAgent(
            kernel,
            "SalesAgent",
            agentCollaborationConfig.DetectedResponseLanguage,
            GetPromptExecutionSettings("SalesAgent"));
        var knowledgeRetrievalAgent = LongAgentDefinitions.GetKnowledgeRetrievalAgent(
            kernel,
            _longAgentKnowledgePlugin,
            "KnowledgeRetrievalAgent",
            sleekflowCompanyId,
            GetPromptExecutionSettings("KnowledgeRetrievalAgent"),
            agentCollaborationConfig.RestrictivenessLevel);
        var reviewerAgent = LongAgentDefinitions.GetReviewerAgent(
            kernel,
            "ReviewerAgent",
            GetPromptExecutionSettings("ReviewerAgent"),
            _agentReviewerToolsPlugin);
        return Task.FromResult(
            new List<Agent>
            {
                salesStrategyAgent, salesAgent, knowledgeRetrievalAgent, reviewerAgent
            });
    }

    public override SelectionStrategy? CreateSelectionStrategy(Kernel kernel)
    {
        var promptExecutionSettings =
            _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_FLASH, true);

        return new KernelFunctionSelectionStrategy(CreateCoordinatingFunction(), kernel)
        {
            HistoryReducer = new ChatHistoryTruncationReducer(1),
            HistoryVariableName = "history",
            ResultParser = (result) =>
            {
                var jsonStr = result.GetValue<string>();
                var jsonOutput = JsonConvert.DeserializeObject<Dictionary<string, object>>(
                    jsonStr?.Replace("```json", string.Empty).Replace("```", string.Empty) ?? "{}");
                return (string?) jsonOutput?["nextParticipant"] ?? LongAgentDefinitions.SalesAgentName;
            },
            Arguments = new KernelArguments(promptExecutionSettings),
            UseInitialAgentAsFallback = true,
        };
    }

    public override RegexTerminationStrategy? CreateTerminationStrategy(List<Agent> agents)
    {
        return new RegexTerminationStrategy("CUSTOMER_FACING_REPLY_APPROVED")
        {
            MaximumIterations = 15,
            AutomaticReset = true,
            Agents = agents.Where(a => a.Name == LongAgentDefinitions.ReviewerAgentName).ToList()
        };
    }

    public override async Task<(string ChatHistoryStr, string Context)> InitializeChatHistoryAsync(
        AgentGroupChat? agentGroupChat,
        string groupChatIdStr,
        List<SfChatEntry> chatEntries,
        ReplyGenerationContext replyGenerationContext,
        AgentCollaborationConfig agentCollaborationConfig,
        CompanyAgentConfig? agentConfig)
    {
        var (chatHistoryStr, context) = await base.InitializeChatHistoryAsync(
            agentGroupChat,
            groupChatIdStr,
            chatEntries,
            replyGenerationContext,
            agentCollaborationConfig,
            agentConfig);

        await _longAgentCollaborationChatCacheService.SetCustomerInquiryAsync(groupChatIdStr, chatHistoryStr);

        return (chatHistoryStr, context);
    }

    public override async Task<bool> InterceptAgentReplyAsync(
        ChatMessageContent response,
        string groupChatIdStr)
    {
        await base.InterceptAgentReplyAsync(response, groupChatIdStr);

        // Cache STRATEGY for AgentReviewerToolsPlugin
        if (response.Content?.Contains("<STRATEGY>", StringComparison.OrdinalIgnoreCase) == true)
        {
            await _longAgentCollaborationChatCacheService.SetStrategyAsync(
                groupChatIdStr,
                TagUtils.ExtractContentBetweenTags(
                    response.Content,
                    "STRATEGY"));
        }

        // Cache PROPOSED_REPLY_TO_CUSTOMER for AgentReviewerToolsPlugin
        if (response.Content?.Contains("<PROPOSED_REPLY_TO_CUSTOMER>", StringComparison.OrdinalIgnoreCase) == true)
        {
            await _longAgentCollaborationChatCacheService.AppendProposedReplyToCustomerAsync(
                groupChatIdStr,
                TagUtils.ExtractContentBetweenTags(
                    response.Content,
                    "PROPOSED_REPLY_TO_CUSTOMER"));
        }

        /* <CONFIRMED_KNOWLEDGE> is cached in KnowledgePlugin */

        return true;
    }

    public override string GetFinalReplyTag() => "PROPOSED_REPLY_TO_CUSTOMER";

    public override string GetSourceTag() => "CONFIRMED_KNOWLEDGE";

    private PromptExecutionSettings GetPromptExecutionSettings(string name)
    {
        // SalesAgent needs to use a more advanced model because it has a more complex role
        if (name is LongAgentDefinitions.SalesAgentName)
        {
            return _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1);
        }

        return _promptExecutionSettingsService.GetPromptExecutionSettings(SemanticKernelExtensions.S_GPT_4_1_MINI);
    }

    private static KernelFunction CreateCoordinatingFunction()
    {
        return AgentGroupChatUtils.CreatePromptFunctionForStrategy(
            name: "LongCoordinatingFunction",
            template:
            $$$"""
               You are to determine the next participant in a conversation by following these exact reasoning steps:

               STEP 1: Identify all tags in the latest message
               - List every tag found between < >
               - If multiple tags found -> Continue to STEP 2
               - If no tags -> Go to STEP 3

               STEP 2: Tag Priority Processing
               Process tags in this EXACT order - stop at first match:
               1. PROPOSED_REPLY_TO_CUSTOMER -> {{{LongAgentDefinitions.ReviewerAgentName}}}
               2. CUSTOMER_FACING_REPLY_APPROVED -> complete
               3. CUSTOMER_FACING_REPLY_REJECTED -> {{{LongAgentDefinitions.SalesAgentName}}}
               4. SUGGEST_NEED_KNOWLEDGE -> {{{LongAgentDefinitions.SalesAgentName}}}
               5. NEED_KNOWLEDGE -> {{{LongAgentDefinitions.KnowledgeRetrievalAgentName}}}
               6. CONFIRMED_KNOWLEDGE -> {{{LongAgentDefinitions.SalesAgentName}}}
               7. ADDITIONAL_INSIGHTS -> {{{LongAgentDefinitions.SalesAgentName}}}
               8. STRATEGY -> {{{LongAgentDefinitions.SalesAgentName}}}

               STEP 3: Default Route
               If no other conditions match -> {{{LongAgentDefinitions.SalesStrategyAgentName}}}

               Output your reasoning as a JSON object with this exact structure:
               {
                   "reasoning": {
                       "tagsFound": [
                           {"tag": "e.g. STRATEGY", "corresponding_index": 8},
                           {"tag": "e.g. NEED_KNOWLEDGE", "corresponding_index": 5},
                       ],
                       "highestPriorityTag": "e.g. NEED_KNOWLEDGE",
                       "routingReason": "brief explanation of final decision e.g. lowest corresponding_index: 5"
                   },
                   "nextParticipant": "participantName"
               }

               Valid participants:
               - {{{LongAgentDefinitions.SalesStrategyAgentName}}}
               - {{{LongAgentDefinitions.SalesAgentName}}}
               - {{{LongAgentDefinitions.KnowledgeRetrievalAgentName}}}
               - {{{LongAgentDefinitions.ReviewerAgentName}}}

               HISTORY:
               ```json
               {{${{{HistoryVariable}}}}}
               ```
               """,
            safeParameterNames:
            HistoryVariable);
    }
}