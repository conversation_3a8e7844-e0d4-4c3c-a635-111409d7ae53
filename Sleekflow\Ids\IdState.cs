﻿using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Ids;

[Resolver(typeof(IDbContainerResolver))]
[DatabaseId("db")]
[ContainerId("webhook")]
public class IdState : Entity
{
    public IdState(string id, string sysTypeName)
        : base(id, sysTypeName)
    {
    }

    public IdState(string id, string sysTypeName, int? ttl)
        : base(id, sysTypeName, ttl)
    {
    }
}