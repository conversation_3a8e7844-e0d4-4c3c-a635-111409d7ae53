﻿using Newtonsoft.Json;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances;

public class WabaBalanceDto : IHasCreatedAt, IHasUpdatedAt
{
    [JsonProperty("facebook_waba_id")]
    public string FacebookWabaId { get; set; }

    [JsonProperty("credit")]
    public Money Credit { get; set; }

    [JsonProperty("all_time_usage")]
    public Money AllTimeUsage { get; set; }

    [JsonProperty("balance")]
    public Money Balance { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset CreatedAt { get; set; }

    [JsonProperty(IHasUpdatedAt.PropertyNameUpdatedAt)]
    public DateTimeOffset UpdatedAt { get; set; }

    [JsonConstructor]
    public WabaBalanceDto(
        string facebookWabaId,
        Money credit,
        Money allTimeUsage,
        Money balance,
        DateTimeOffset createdAt,
        DateTimeOffset updatedAt)
    {
        FacebookWabaId = facebookWabaId;
        Credit = credit;
        AllTimeUsage = allTimeUsage;
        Balance = balance;
        CreatedAt = createdAt;
        UpdatedAt = updatedAt;
    }

    public WabaBalanceDto(WabaBalance wabaBalance)
        : this(wabaBalance.FacebookWabaId, wabaBalance.Credit, wabaBalance.AllTimeUsage, wabaBalance.Balance, wabaBalance.CreatedAt, wabaBalance.UpdatedAt)
    {
    }
}