using MassTransit;
using Sleekflow.CommerceHub.Models.Events;
using Sleekflow.CommerceHub.Products;

namespace Sleekflow.CommerceHub.Events;

public class OnProductVariantChangedEventConsumerDefinition : ConsumerDefinition<OnProductVariantChangedEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnProductVariantChangedEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = false;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnProductVariantChangedEventConsumer : IConsumer<OnProductVariantChangedEvent>
{
    private readonly IProductService _productService;

    public OnProductVariantChangedEventConsumer(IProductService productService)
    {
        _productService = productService;
    }

    public async Task Consume(ConsumeContext<OnProductVariantChangedEvent> context)
    {
        var @event = context.Message;

        // TODO Explore group by product id and then update the product
        await _productService.RecalculateProductWithVariantProductsAsync(
            @event.ProductId,
            @event.SleekflowCompanyId,
            @event.StoreId,
            @event.SleekflowStaff);
    }
}