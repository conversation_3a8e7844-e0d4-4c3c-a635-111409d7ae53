﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Auth0.ManagementApi.Models;
using Microsoft.Data.SqlClient;
using Newtonsoft.Json;
using NUnit.Framework;
using Sleekflow.Auth0.BulkImporter.Models;
using Sleekflow.Auth0.BulkImporter.Services;
using Sleekflow.Mvc.Tests;
using User = Auth0.ManagementApi.Models.User;

namespace Sleekflow.Auth0.BulkImporter.Tests
{
    public class ImporterTest
    {
        #region Sleekflow DEV

        private const string ClientId = "KkA7yjPAuBFWl50cE1drTdyf7HiusQMg";
        private const string ClientSecret = "lDDbBMcEzWfOFFQjeMLDT--eVBaWDMoELdHbyWyTb2uP5_cAKgSj7XHHkN4bZ8fe";
        private const string Audience = "https://sleekflow-dev.eu.auth0.com/api/v2/";

        #endregion

        #region Sleekflow TEST

        // private const string ClientId = "";
        // private const string ClientSecret = "";
        // private const string Audience = "https://.jp.auth0.com/api/v2/";

        #endregion

        private const string GrantType = "client_credentials";
        private const string CachePath = "_cache/_test";
        private const string PwdUsersDir = CachePath + "/userPwd";

        // UAT
        private const string ConnString =
            "Server=tcp:sleekflow-core-sql-server-eas-dev928ea268.database.windows.net,1433;Initial Catalog=travis-crm-prod-db;Persist Security Info=False;User ID=s81604a6f;Password=*********************************************************************;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;Pooling=true;Max Pool Size=500;Min Pool Size=100;";

        private async Task InitFolders()
        {
            Console.Write("Cleaning up cache folder .. ");

            var exists = Directory.Exists(PwdUsersDir);
            if (!exists)
            {
                Directory.CreateDirectory(PwdUsersDir);
            }

            var dirInfo = new DirectoryInfo(PwdUsersDir);
            if (exists)
            {
                foreach (var dir in dirInfo.GetFiles())
                {
                    await Task.Run(() => dir.Delete());
                }

                foreach (var dir in dirInfo.GetDirectories())
                {
                    dir.Delete(true);
                }
            }
        }

        [Test]
        public async Task ConnectSql()
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            var sqlConn = new SqlConnection(ConnString);
            string sql = "select top 10 * from AspNetUsers";

            await sqlConn.OpenAsync();
            var cmd = new SqlCommand(sql, sqlConn);
            DataTable dt = new DataTable();
            await using var reader = await cmd.ExecuteReaderAsync();
            if (reader.HasRows)
            {
                dt.Load(reader);
            }

            foreach (DataRow row in dt.Rows)
            {
                TestContext.Write(row["Email"] + ", ");
                TestContext.Write(row["PhoneNumber"] + ", ");
                TestContext.Write("\n");
            }

            Assert.IsFalse(dt.HasErrors, "Database connection is not valid.");
        }

        [Test]
        public async Task ConvertUsersDataTest()
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            var batchSize = 100;

            var sleekflowUserService = new SleekflowUserService(ConnString);
            var users = await sleekflowUserService.FetchAndConvertUsers(batchSize);

            var resultString = JsonConvert.SerializeObject(users);
            var size = Encoding.Unicode.GetByteCount(resultString);

            TestContext.WriteLine($"Converted json size in batch size {batchSize}: {size / 1024} kbs");
            TestContext.WriteLine(JsonConvert.SerializeObject(users.Take(5), Formatting.Indented));

            Assert.That(users.Count, Is.EqualTo(batchSize));
            Assert.That(users, Is.AssignableFrom(typeof(List<ImportUser>)));
        }

        [Test]
        [TestCase("con_K0uHCFuKNuiBpLqV", 5)] // Sleekflow-dev.
        public async Task NullUserEmailFunctionTest(string connectionId, int testCount)
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            await this.InitFolders();

            var sleekflowUserService = new SleekflowUserService(ConnString);
            var userList = await sleekflowUserService.FetchAndConvertUsers();

            var fileBatchSize = 50;
            var userImporter =
                await Auth0UserImporter.InitAsync(ClientId, ClientSecret, Audience, GrantType);

            // var emptyEmailUsers = MockData.GetFakeUsers(count: testCount, haveEmail: false);
            // var emptyEmailUsers = userList.FindAll(u => u.Email == string.Empty).ToList();
            var emptyEmailUsers = userList.FindAll(u => u.Email.Contains("@id.sleekflow.io")).ToList();
            var emptyUserList = userList.FindAll(u => u.Email == string.Empty).ToList();
            var clearList = emptyEmailUsers.Select(u => u.UserId).ToList();

            Assert.That(emptyUserList, Is.Empty, "Result has empty email value ! test case failed.");

            try
            {
                foreach (var emailCheck in emptyEmailUsers)
                {
                    Assert.That(EmailUtils.IsValidEmail(emailCheck?.Email), Is.True);
                    await TestContext.Progress.WriteLineAsync(
                        $"Import user: {emailCheck?.Email}, {emailCheck.UserName}");
                }

                await TestContext.Progress.WriteLineAsync($"Creating users file..");
                await Auth0UserImporter.WriteUsersToFiles(emptyEmailUsers, PwdUsersDir, fileBatchSize);

                var dirInfo = new DirectoryInfo(PwdUsersDir);
                foreach (var file in dirInfo.GetFiles())
                {
                    await TestContext.Progress.WriteLineAsync($"\nSending {file.Name}..");

                    Stream stream = new FileStream(file.FullName, FileMode.Open);
                    if (userImporter.ManagementClient != null)
                    {
                        var importResponse =
                            await userImporter.ManagementClient.Jobs.ImportUsersAsync(connectionId, file.Name, stream);

                        await TestContext.Progress.WriteLineAsync($"Returned Job id: {importResponse.Id}");

                        Assert.That(importResponse, Is.AssignableFrom(typeof(Job)));
                        Assert.That(importResponse.Type, Is.EqualTo("users_import"));
                        Assert.That(importResponse.Status, Is.EqualTo("pending"));

                        await TestContext.Progress.WriteLineAsync(
                            $"Import users thread returned job id: {importResponse.Id}");

                        while (importResponse.Status != "completed")
                        {
                            importResponse = await userImporter.GetJobStatus(importResponse.Id);
                            await TestContext.Progress.WriteLineAsync(
                                $"\rWaiting job {importResponse.Id}.. {importResponse.Status}");
                            await Task.Delay(500);
                        }
                    }
                }

                foreach (var checkUser in emptyEmailUsers)
                {
                    await TestContext.Progress.WriteLineAsync($"Finding {checkUser.UserName}, {checkUser.Email}..");
                    var testUser =
                        await userImporter.ManagementClient.Users.GetAsync($"auth0|{checkUser.UserId}");

                    await TestContext.Progress.WriteLineAsync(
                        $"Found user in auth0 {testUser.Email} ({testUser.UserName})");

                    Assert.That(testUser, Is.AssignableFrom<User>());
                }
            }
            catch (Exception err)
            {
                await TestContext.Progress.WriteLineAsync($"Error !! \n{err.Message}\n{err.InnerException}");
            }

            await TestContext.Progress.WriteLineAsync($"Cleaning data now..");
            await userImporter.CleanData(clearList);
        }

        [Test]
        [TestCase("con_K0uHCFuKNuiBpLqV", 3, 50, 10)] // Sleekflow-dev.
        // [TestCase("con_00KTsHJgZwkHqzyi", 3, 50, 10)] // Sleekflow auth0 UAT.
        // [TestCase("con_l8nbZRyIu1TW05bM", 3, 50, 10)]        // powerflow-test.jp.auth0.com
        public async Task ImportFakeUserTest(
            string connectionId,
            int pickUserCount,
            int fileBatchSize = 10,
            int genCount = 3)
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            #region Auth0 Client

            try
            {
                await this.InitFolders();

                var userImporter =
                    await Auth0UserImporter.InitAsync(ClientId, ClientSecret, Audience, GrantType);

                var userList = MockData.GetFakeUsers(3);
                var clearList = userList.Select(u => u.UserId).ToList();

                await TestContext.Progress.WriteLineAsync($"Creating users file..");
                await Auth0UserImporter.WriteUsersToFiles(userList, PwdUsersDir, fileBatchSize);

                var dirInfo = new DirectoryInfo(PwdUsersDir);
                foreach (var file in dirInfo.GetFiles())
                {
                    await TestContext.Progress.WriteLineAsync($"\nSending {file.Name}..");

                    Stream stream = new FileStream(file.FullName, FileMode.Open);
                    if (userImporter.ManagementClient != null)
                    {
                        var importResponse =
                            await userImporter.ManagementClient.Jobs.ImportUsersAsync(connectionId, file.Name, stream);

                        await TestContext.Progress.WriteLineAsync($"Returned Job id: {importResponse.Id}");

                        Assert.That(importResponse, Is.AssignableFrom<Job>());
                        Assert.That(importResponse.Type, Is.EqualTo("users_import"));
                        Assert.That(importResponse.Status, Is.EqualTo("pending"));

                        await TestContext.Progress.WriteLineAsync(
                            $"Import users thread returned job id: {importResponse.Id}");

                        while (importResponse.Status != "completed")
                        {
                            importResponse = await userImporter.GetJobStatus(importResponse.Id);
                            await TestContext.Progress.WriteAsync(
                                $"\rWaiting job {importResponse.Id}.. {importResponse.Status}");
                            await Task.Delay(500);
                        }
                    }
                }

                try
                {
                    await TestContext.Progress.WriteLineAsync($"\nAll files are sent. Test getting imported users.");
                    for (int i = 0; i < pickUserCount; i++)
                    {
                        var rnd = new Random();
                        var rndItem = rnd.Next(clearList.Count);
                        if (userImporter.ManagementClient != null)
                        {
                            var testUser =
                                await userImporter.ManagementClient.Users.GetAsync($"auth0|{clearList[rndItem]}");
                            var metadata =
                                (MetaData) JsonConvert.DeserializeObject<MetaData>(testUser.AppMetadata.ToString());

                            await TestContext.Progress.WriteLineAsync($"Found user {testUser.Email}");
                            Assert.That(
                                clearList[rndItem],
                                Is.EqualTo(
                                    ((MetaData) JsonConvert.DeserializeObject<MetaData>(
                                        testUser.AppMetadata.ToString()))
                                    ?.SleekflowId));
                            Assert.That($"auth0|{clearList[rndItem]}", Is.EqualTo(testUser.UserId));
                            Assert.IsAssignableFrom<User>(testUser); // Auth0.ManagementApi.Model.User
                        }
                    }
                }
                catch (Exception err)
                {
                    await TestContext.Progress.WriteLineAsync($"Error !! \n{err.Message}\n{err.InnerException}");
                }

                await TestContext.Progress.WriteLineAsync($"Cleaning data now..");
                await userImporter.CleanData(clearList);

                await TestContext.Progress.WriteLineAsync($"Clean completed.");
            }
            catch (Exception err)
            {
                throw new Exception(err.Message, err.InnerException);
            }

            #endregion
        }

        [Test]
        // [TestCase("con_l8nbZRyIu1TW05bM", 3, 10, 30)]        // powerflow-test.jp.auth0.com
        // [TestCase("con_00KTsHJgZwkHqzyi", 3, 10, 30)] // Sleekflow auth0 UAT.
        [TestCase("con_K0uHCFuKNuiBpLqV", 3, 10, 30)] // Sleekflow auth0 dev.
        public async Task ImportUserFromDbTest(
            string connectionId,
            int pickCount,
            int batchSize = 10,
            int importCount = 0)
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            #region Auth0Client

            try
            {
                await this.InitFolders();

                var sleekflowUserService = new SleekflowUserService(ConnString);
                var userList = await sleekflowUserService.FetchAndConvertUsers(batchSize);

                var userImporter =
                    await Auth0UserImporter.InitAsync(ClientId, ClientSecret, Audience, GrantType);

                Dictionary<string, Job> jobsList = new Dictionary<string, Job>();

                var clearList = userList.Select(u => u.UserId).ToList();

                await TestContext.Progress.WriteLineAsync($"Creating users file..");
                await Auth0UserImporter.WriteUsersToFiles(userList, PwdUsersDir, batchSize);

                var dirInfo = new DirectoryInfo(PwdUsersDir);
                foreach (var file in dirInfo.GetFiles())
                {
                    await TestContext.Progress.WriteLineAsync($"Sending {file.Name}..");

                    Stream stream = new FileStream(file.FullName, FileMode.Open);
                    if (userImporter.ManagementClient != null)
                    {
                        var importResponse =
                            await userImporter.ManagementClient.Jobs.ImportUsersAsync(
                                connectionId,
                                file.Name,
                                stream,
                                true,
                                null,
                                true);

                        jobsList.Add(importResponse.Id, importResponse);

                        Assert.IsAssignableFrom<Job>(importResponse);
                        Assert.That(importResponse.Type, Is.EqualTo("users_import"));
                        Assert.That(importResponse.Status, Is.EqualTo("pending"));

                        await TestContext.Progress.WriteLineAsync(
                            $"Import users thread returned job id: {importResponse.Id}");

                        while (importResponse.Status != "completed")
                        {
                            importResponse = await userImporter.GetJobStatus(importResponse.Id);
                            await TestContext.Progress.WriteAsync(
                                $"\rWaiting job {importResponse.Id}.. {importResponse.Status}");

                            jobsList[importResponse.Id].PercentageDone = importResponse.PercentageDone;
                            jobsList[importResponse.Id].Status = importResponse.Status;
                            jobsList[importResponse.Id].Summary = importResponse.Summary;
                            await Task.Delay(1800);
                        }
                    }

                    await TestContext.Progress.WriteAsync("\n");
                }

                try
                {
                    await TestContext.Progress.WriteLineAsync($"\nAll files are sent. Test getting imported users.");
                    for (int i = 0; i < pickCount; i++)
                    {
                        var rnd = new Random();
                        var rndItem = rnd.Next(clearList.Count);
                        if (userImporter.ManagementClient != null)
                        {
                            var testUser =
                                await userImporter.ManagementClient.Users.GetAsync($"auth0|{clearList[rndItem]}");

                            await TestContext.Progress.WriteLineAsync($"Found user {testUser.Email}");
                            Assert.That(
                                clearList[rndItem],
                                Is.EqualTo(
                                    ((MetaData) JsonConvert.DeserializeObject<MetaData>(
                                        testUser.AppMetadata.ToString()))
                                    ?.SleekflowId));
                            Assert.That($"auth0|{clearList[rndItem]}", Is.EqualTo(testUser.UserId));
                            Assert.That(userList.Select(u => u.Email).ToList(), Does.Contain(testUser.Email));
                        }
                        else
                        {
                            Assert.Fail("ManagementClient cannot be initialize");
                        }

                        await Task.Delay(300);
                    }
                }
                catch (Exception err)
                {
                    await TestContext.Progress.WriteLineAsync($"{err.Message}\n{err.InnerException}");
                }

                var failsCount = jobsList.Sum(o => o.Value.Summary.Failed);
                var failJobs = jobsList
                    .Where(o => o.Value.Summary.Failed > 0)
                    .Select(
                        o => new
                        {
                            o.Value.Id, o.Value.Summary
                        })
                    .ToList();
                var failsSummary = new List<Auth0JobErrorDetailsResponse>();
                if (failJobs.Count > 0)
                {
                    foreach (var info in failJobs)
                    {
                        var (obj, message) = await userImporter.GetFailDetails(info.Id);
                        failsSummary = failsSummary.Concat(obj).ToList();
                    }
                }

                await TestContext.Progress.WriteLineAsync($"Test importer return {failsCount} fail jobs.");
                await TestContext.Progress.WriteLineAsync(
                    $"Summary:\n{JsonConvert.SerializeObject(failsSummary, Formatting.Indented)}");
                await TestContext.Progress.WriteLineAsync($"Cleaning data now..");
                await userImporter.CleanData(clearList);

                await TestContext.Progress.WriteLineAsync($"Clean completed.");
            }
            catch (Exception err)
            {
                throw new Exception(err.Message, err.InnerException);
            }

            #endregion
        }

        [Test]
        [TestCase("job_HDQBJnSPdMxdHs0E")]
        public async Task GetImportStatus(string jobId)
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            try
            {
                var userImporter =
                    await Auth0UserImporter.InitAsync(
                        ClientId,
                        ClientSecret,
                        Audience,
                        GrantType);

                var jobStatus = await userImporter.GetJobStatus(jobId);
                TestContext.WriteLine($"Job Type: {jobStatus.Type}");
                TestContext.WriteLine(
                    $"Job Summary: {JsonConvert.SerializeObject(jobStatus.Summary, Formatting.Indented)}");
                TestContext.WriteLine($"Job Status: {jobStatus.Status}");
                TestContext.WriteLine($"Percentage done: {jobStatus.PercentageDone}");
            }
            catch (Exception err)
            {
                TestContext.WriteLine($"Get Job status failed.\nMessage: {err.Message}");
                Assert.Fail();
            }
        }

        [Test]
        [TestCase("job_anxrOtn8wD9301GW")]
        public async Task GetErrorLogsTest(string jobId)
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            try
            {
                var userImporter =
                    await Auth0UserImporter.InitAsync(
                        ClientId,
                        ClientSecret,
                        Audience,
                        GrantType);

                var jobStatus = await userImporter.GetFailDetails(jobId);

                await TestContext.Progress.WriteLineAsync(
                    $"Job {jobId} return: \n" +
                    JsonConvert.SerializeObject(jobStatus.ErrorResponse, Formatting.Indented));
            }
            catch (Exception err)
            {
                TestContext.WriteLine($"Get Job status failed.\nMessage: {err.Message}");
                Assert.Fail();
            }
        }

        [Test]

        // First project test
        [TestCase(
            "yFj14qg1KIWR2wcXP1QEDmujfp0BPuRA",
            "HhBjSy2cvT95Zl3juMAGJAvzFizlcR8ze-xCU92Cy99GL5_skkwYahdUVXSeaO53",
            "https://dev--billy-sleekflow.jp.auth0.com/api/v2/")]

        // UAT Powerflow
        [TestCase(
            "Dvk40c04o0OMyxh8eyFdo9kPNRpXACjY",
            "hAUrBqQmc7T_MiHmik-lAo2kr4gBJCm1kgyhlCuvIzYw_9pbi4KWxUSVTos8LA1P",
            "https://powerflow-test.jp.auth0.com/api/v2/")]
        public void InitTest(string clientId, string clientSecret, string audience)
        {
            if (BaseTestHost.IsGithubAction)
            {
                Assert.Ignore("Skip this test on Github action.");
            }

            Assert.DoesNotThrowAsync(
                async () =>
                {
                    var userImporter =
                        await Auth0UserImporter.InitAsync(
                            ClientId,
                            ClientSecret,
                            Audience,
                            GrantType);

                    Assert.IsNotEmpty(userImporter.AccessToken);
                });
        }

        [Test]
        public void FirstTest()
        {
            Assert.That(
                PasswordUtils.ToAuth0PasswordHash(
                    "AQAAAAEAACcQAAAAEI6MpYT60NMJ+WdtDpLgAochUfGTGyZUjBEkmtmKo4yBHmz27Y0M2BBZwJ0cd848YA=="),
                Is.Not.Null);
            Assert.That(
                PasswordUtils.ToAuth0PasswordHash(
                    "AQAAAAEAACcQAAAAEI6MpYT60NMJ+WdtDpLgAochUfGTGyZUjBEkmtmKo4yBHmz27Y0M2BBZwJ0cd848YA=="),
                Is.EqualTo(
                    "$pbkdf2-sha256$i=10000,l=32$joylhPrQ0wn5Z20OkuAChw$IVHxkxsmVIwRJJrZiqOMgR5s9u2NDNgQWcCdHHfOPGA"));
        }
    }
}