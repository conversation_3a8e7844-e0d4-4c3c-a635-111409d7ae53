﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class ObjectFieldNameValuePair
{
    [JsonProperty("field_name")]
    public string FieldName { get; set; }

    [JsonProperty("field_value__expr")]
    public string? FieldValueExpr { get; set; }

    [JsonConstructor]
    public ObjectFieldNameValuePair(
        string fieldName,
        string? fieldValueExpr)
    {
        FieldName = fieldName;
        FieldValueExpr = fieldValueExpr;
    }
}