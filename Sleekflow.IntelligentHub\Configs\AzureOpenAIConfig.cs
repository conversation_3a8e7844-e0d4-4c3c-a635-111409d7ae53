using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Connectors.OpenAI;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IAzureOpenAIConfig
{
    string Endpoint { get; }

    string Key { get; }

    string DeploymentName { get; }

    string EusEndpoint { get; }

    string EusKey { get; }

    OpenAIPromptExecutionSettings DefaultPromptExecutionSettings { get; }

    OpenAIPromptExecutionSettings DefaultReasoningPromptExecutionSettings { get; }
}

public class AzureOpenAIConfig : IConfig, IAzureOpenAIConfig
{
    /// <summary>
    /// Azure OpenAI endpoint.
    /// </summary>
    public string Endpoint { get; }

    /// <summary>
    /// Key to access the AI service.
    /// </summary>
    public string Key { get; }

    /// <summary>
    /// Azure OpenAI deployment name to use for completions.
    /// </summary>
    public string DeploymentName { get; }

    public string EusEndpoint { get; }

    public string EusKey { get; }

    public OpenAIPromptExecutionSettings DefaultPromptExecutionSettings
    {
        get
        {
            var promptExecutionSettings = new OpenAIPromptExecutionSettings
            {
                Temperature = 0,
                MaxTokens = 2048,
                ServiceId = "default",
                FunctionChoiceBehavior = FunctionChoiceBehavior.None()
            };

            return promptExecutionSettings;
        }
    }

    public OpenAIPromptExecutionSettings DefaultReasoningPromptExecutionSettings
    {
        get
        {
            // This model does not support Temperature, TopP, PresencePenalty, FrequencyPenalty
            var promptExecutionSettings = new OpenAIPromptExecutionSettings
            {
                MaxTokens = 4096,
                ServiceId = "o4-mini",
            };

            return promptExecutionSettings;
        }
    }

    public AzureOpenAIConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        Endpoint =
            Environment.GetEnvironmentVariable("AZUREOPENAI_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("AZUREOPENAI_ENDPOINT");

        Key =
            Environment.GetEnvironmentVariable("AZUREOPENAI_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("AZUREOPENAI_KEY");

        EusEndpoint =
            Environment.GetEnvironmentVariable("AZUREOPENAI_EUS_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("AZUREOPENAI_ENDPOINT");

        EusKey =
            Environment.GetEnvironmentVariable("AZUREOPENAI_EUS_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("AZUREOPENAI_KEY");
    }
}