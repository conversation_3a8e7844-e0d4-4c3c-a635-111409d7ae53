using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Persistence.FlowHubDb;

namespace Sleekflow.FlowHub.Models.FlowHubEvents;

[ContainerId(ContainerNames.FlowHubEvent)]
[DatabaseId(ContainerNames.DatabaseId)]
[Resolver(typeof(IFlowHubDbResolver))]
public class FlowHubEvent : Entity
{
    [JsonProperty("event_body", TypeNameHandling = TypeNameHandling.Objects)]
    public EventBody EventBody { get; set; }

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("object_id")]
    public string ObjectId { get; set; }

    public FlowHubEvent(
        string id,
        int? ttl,
        EventBody eventBody,
        string sleekflowCompanyId,
        string objectId)
        : base(id, SysTypeNames.FlowHubEvent, ttl)
    {
        EventBody = eventBody;
        SleekflowCompanyId = sleekflowCompanyId;
        ObjectId = objectId;
    }
}