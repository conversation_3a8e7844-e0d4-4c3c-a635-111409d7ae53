using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;
using Microsoft.SemanticKernel;
using Sleekflow.IntelligentHub.Plugins;

namespace Sleekflow.IntelligentHub.Evaluator;

public class MockHubspotPlugin : IHubspotPlugin
{
    public const string PhoneNumber = "+852 6109 6623";

    private readonly ILogger<MockHubspotPlugin> _logger;

    // Store calls by test ID
    private static readonly ConcurrentDictionary<string, HubspotCallInfo> CallInfo = new ();

    public MockHubspotPlugin(ILogger<MockHubspotPlugin> logger)
    {
        _logger = logger;
    }

    [KernelFunction("get_contact_properties")]
    [System.ComponentModel.Description(
        "Gets all available Hubspot contact properties for a contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        System.ComponentModel.Description(
            "A JSON string containing all the contact's properties, or null if the contact is not found or an error occurs.")]
    public Task<string?> GetContactPropertiesAsync(
        Kernel kernel,
        [System.ComponentModel.Description("The email address of the contact")]
        string? email = null,
        [System.ComponentModel.Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult<string?>("{}");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new HubspotCallInfo());
        callInfo.GetContactPropertiesCalled = true;
        callInfo.GetContactPropertiesParams = new GetContactPropertiesParams
        {
            Email = email, Phone = phone
        };

        _logger.LogInformation(
            "Mock GetContactProperties call for test {TestId}: Email: {Email}, Phone: {Phone}",
            testId,
            email,
            phone);

        if (phone == MockHubspotPlugin.PhoneNumber)
        {
            return Task.FromResult<string?>(
                """
                {
                  "id": "116264368735",
                  "properties": {
                    "company": "SLEEKFLOW ENGINEERING Association",
                    "createdate": "2025-04-23T02:18:00.200Z",
                    "email": "<EMAIL>",
                    "firstname": "Leo",
                    "hs_object_id": "116264368735",
                    "hubspot_owner_id": "*********",
                    "lastmodifieddate": "2025-04-24T07:26:01.215Z",
                    "lastname": "Choi",
                    "lifecyclestage": "lead",
                    "phone": "******-456-7890",
                    "website": null
                  },
                  "createdAt": "2025-04-23T02:18:00.200Z",
                  "updatedAt": "2025-04-24T07:26:01.215Z",
                  "archived": false
                }
                """);
        }

        return Task.FromResult<string?>(null);
    }

    [KernelFunction("get_contact_meetings")]
    [System.ComponentModel.Description(
        "Gets details of all meetings associated with a Hubspot contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        System.ComponentModel.Description(
            "A JSON string representing a list of meeting objects. Returns an empty list '[]' if the contact is not found, has no meetings, or an error occurs.")]
    public Task<string?> GetContactMeetingsAsync(
        Kernel kernel,
        [System.ComponentModel.Description("The email address of the contact")]
        string? email = null,
        [System.ComponentModel.Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult<string?>("[]");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new HubspotCallInfo());
        callInfo.GetContactMeetingsCalled = true;
        callInfo.GetContactMeetingsParams = new GetContactMeetingsParams
        {
            Email = email, Phone = phone
        };

        _logger.LogInformation(
            "Mock GetContactMeetings call for test {TestId}: Email: {Email}, Phone: {Phone}",
            testId,
            email,
            phone);
        if (phone == MockHubspotPlugin.PhoneNumber)
        {
            return Task.FromResult<string?>(
                """
                [
                  {
                    "id": "77651448504",
                    "properties": {
                      "hs_createdate": "2025-04-23T02:19:45Z",
                      "hs_internal_meeting_notes": null,
                      "hs_lastmodifieddate": "2025-04-24T08:01:37.509Z",
                      "hs_meeting_body": "If you need to reschedule please use the link below:\nhttps://sleekflow.chilipiper.com/reschedule/1a806e99-a83c-4881-b07c-eae805914538\n\nLocation not specified",
                      "hs_meeting_end_time": "2025-04-24T07:00:00Z",
                      "hs_meeting_start_time": "2025-04-24T06:30:00Z",
                      "hs_meeting_title": "Leo <> Josephine @ SleekFlow | Meeting",
                      "hs_object_id": "77651448504",
                      "hubspot_owner_id": "*********"
                    },
                    "createdAt": "2025-04-23T02:19:45Z",
                    "updatedAt": "2025-04-24T08:01:37.509Z",
                    "archived": false
                  }
                ]
                """);
        }

        return Task.FromResult<string?>("[]");
    }

    [KernelFunction("update_contact_properties")]
    [System.ComponentModel.Description(
        "Updates properties of a Hubspot contact matching the given email or phone number, using API key from Kernel context.")]
    [return:
        System.ComponentModel.Description(
            "A JSON string containing the updated contact's properties, or null if the contact is not found or an error occurs.")]
    public Task<string?> UpdateContactPropertiesAsync(
        Kernel kernel,
        [System.ComponentModel.Description("A list of HubspotProperty objects containing the properties to update")]
        List<HubspotProperty> properties,
        [System.ComponentModel.Description("The email address of the contact")]
        string? email = null,
        [System.ComponentModel.Description("The phone number of the contact")]
        string? phone = null,
        CancellationToken cancellationToken = default)
    {
        var testId = ChatEvalTest.CurrentTestId.Value;
        if (testId == null)
        {
            _logger.LogWarning("No test ID found in context - skipping call tracking");
            return Task.FromResult<string?>("{}");
        }

        var callInfo = CallInfo.GetOrAdd(testId, _ => new HubspotCallInfo());
        callInfo.UpdateContactPropertiesCalled = true;
        callInfo.UpdateContactPropertiesParams = new HubspotUpdateContactPropertiesParams
        {
            Email = email,
            Phone = phone,
            Properties = properties
        };

        _logger.LogInformation(
            "Mock UpdateContactProperties call for test {TestId}: Email: {Email}, Phone: {Phone}, Properties: {Properties}",
            testId,
            email,
            phone,
            string.Join(", ", properties?.Select(p => $"{p.PropertyName}={p.PropertyValue}") ?? new string[0]));

        if (phone == MockHubspotPlugin.PhoneNumber)
        {
            // Return mock updated contact data
            return Task.FromResult<string?>(
                """
                {
                  "id": "116264368735",
                  "properties": {
                    "company": "SLEEKFLOW ENGINEERING Association",
                    "createdate": "2025-04-23T02:18:00.200Z",
                    "email": "<EMAIL>",
                    "firstname": "Leo",
                    "hs_object_id": "116264368735",
                    "hubspot_owner_id": "*********",
                    "lastmodifieddate": "2025-04-24T07:26:01.215Z",
                    "lastname": "Choi",
                    "lifecyclestage": "lead",
                    "phone": "******-456-7890",
                    "website": null
                  },
                  "createdAt": "2025-04-23T02:18:00.200Z",
                  "updatedAt": "2025-04-24T07:26:01.215Z",
                  "archived": false
                }
                """);
        }

        return Task.FromResult<string?>(null);
    }

    /// <summary>
    /// Checks if GetContactProperties was called for a specific test.
    /// </summary>
    public static bool WasGetContactPropertiesCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.GetContactPropertiesCalled;
    }

    /// <summary>
    /// Checks if GetContactMeetings was called for a specific test.
    /// </summary>
    public static bool WasGetContactMeetingsCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.GetContactMeetingsCalled;
    }

    /// <summary>
    /// Get the parameters used in GetContactProperties for a specific test.
    /// </summary>
    public static GetContactPropertiesParams? GetContactPropertiesParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.GetContactPropertiesParams : null;
    }

    /// <summary>
    /// Get the parameters used in GetContactMeetings for a specific test.
    /// </summary>
    public static GetContactMeetingsParams? GetContactMeetingsParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.GetContactMeetingsParams : null;
    }

    /// <summary>
    /// Checks if UpdateContactProperties was called for a specific test.
    /// </summary>
    public static bool WasUpdateContactPropertiesCalled(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) && info.UpdateContactPropertiesCalled;
    }

    /// <summary>
    /// Get the parameters used in UpdateContactProperties for a specific test.
    /// </summary>
    public static HubspotUpdateContactPropertiesParams? GetUpdateContactPropertiesParams(string testId)
    {
        return CallInfo.TryGetValue(testId, out var info) ? info.UpdateContactPropertiesParams : null;
    }

    /// <summary>
    /// Clears the stored call info for a specific test.
    /// </summary>
    public static void ClearTestState(string testId)
    {
        CallInfo.TryRemove(testId, out _);
    }
}

public class HubspotCallInfo
{
    public bool GetContactPropertiesCalled { get; set; }

    public bool GetContactMeetingsCalled { get; set; }

    public bool UpdateContactPropertiesCalled { get; set; }

    public GetContactPropertiesParams? GetContactPropertiesParams { get; set; }

    public GetContactMeetingsParams? GetContactMeetingsParams { get; set; }

    public HubspotUpdateContactPropertiesParams? UpdateContactPropertiesParams { get; set; }
}

public class GetContactPropertiesParams
{
    public string? Email { get; set; }

    public string? Phone { get; set; }
}

public class GetContactMeetingsParams
{
    public string? Email { get; set; }

    public string? Phone { get; set; }
}

public class HubspotUpdateContactPropertiesParams
{
    public string? Email { get; set; }

    public string? Phone { get; set; }

    public List<HubspotProperty>? Properties { get; set; }
}

