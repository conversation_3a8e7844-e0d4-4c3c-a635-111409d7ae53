using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.WorkflowExecutions;

public class WorkflowStatistics
{
    [JsonProperty("num_of_started_workflows")]
    public long NumOfStartedWorkflows { get; set; }

    [JsonProperty("num_of_complete_workflows")]
    public long NumOfCompleteWorkflows { get; set; }

    [JsonProperty("num_of_cancelled_workflows")]
    public long NumOfCancelledWorkflows { get; set; }

    [JsonProperty("num_of_blocked_workflows")]
    public long NumOfBlockedWorkflows { get; set; }

    [JsonProperty("num_of_failed_workflows")]
    public long NumOfFailedWorkflows { get; set; }

    [JsonProperty("num_of_restricted_workflows")]
    public long NumOfRestrictedWorkflows { get; set; }

    [JsonConstructor]
    public WorkflowStatistics(
        long numOfStartedWorkflows,
        long numOfCompleteWorkflows,
        long numOfCancelledWorkflows,
        long numOfBlockedWorkflows,
        long numOfFailedWorkflows,
        long numOfRestrictedWorkflows)
    {
        NumOfStartedWorkflows = numOfStartedWorkflows;
        NumOfCompleteWorkflows = numOfCompleteWorkflows;
        NumOfCancelledWorkflows = numOfCancelledWorkflows;
        NumOfBlockedWorkflows = numOfBlockedWorkflows;
        NumOfFailedWorkflows = numOfFailedWorkflows;
        NumOfRestrictedWorkflows = numOfRestrictedWorkflows;
    }

    public static WorkflowStatistics Default()
        => new WorkflowStatistics(
            0,
            0,
            0,
            0,
            0,
            0);
}