using Newtonsoft.Json;

namespace Sleekflow.EmailHub.Models.Webhooks;

public class NotifyOnGmailReceiveData
{
    [JsonProperty("emailAddress")]
    public string EmailAddress { get; }

    [JsonProperty("historyId")]
    public string HistoryId { get; }

    [JsonConstructor]
    public NotifyOnGmailReceiveData(
        string emailAddress,
        string historyId)
    {
        EmailAddress = emailAddress;
        HistoryId = historyId;
    }
}

public class NotifyOnGmailReceiveMessage
{
    [JsonProperty("data")]
    public string Data { get; }

    [JsonProperty("messageId")]
    public string MessageId { get; private set; }

    [JsonProperty("message_id")]
    public string LegacyMessageId => MessageId;

    [JsonProperty("publishTime")]
    public string PublishTime { get; private set; }

    [JsonProperty("publish_time")]
    public string LegacyPublishTime => PublishTime;

    [JsonConstructor]
    public NotifyOnGmailReceiveMessage(
        string data,
        string messageId,
        string publishTime)
    {
        Data = data;
        MessageId = messageId;
        PublishTime = publishTime;
    }
}