﻿namespace Sleekflow.Exceptions.MessagingHub;

public class SfStillInProgressException : ErrorCodeException
{
    public string StateId { get; }

    public string RuntimeStatus { get; }

    public SfStillInProgressException(string stateId, string runtimeStatus)
        : base(
            ErrorCodeConstant.SfStillInProgressException,
            $"It is still in progress. stateId=[{stateId}]",
            new Dictionary<string, object?>
            {
                {
                    "stateId", stateId
                },
                {
                    "runtimeStatus", runtimeStatus
                },
            })
    {
        StateId = stateId;
        RuntimeStatus = runtimeStatus;
    }
}