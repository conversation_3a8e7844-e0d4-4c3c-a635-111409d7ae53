using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.FlowHub.Models.Workers;

public class ExecuteParallelStepInput
{
    [JsonProperty("state_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StateId { get; set; }

    [JsonProperty("step_id")]
    [System.ComponentModel.DataAnnotations.Required]
    public string StepId { get; set; }

    [JsonProperty("parallel_step_ids")]
    [System.ComponentModel.DataAnnotations.Required]
    public List<string> ParallelStepIds { get; set; }

    [JsonProperty("stack_entries")]
    [System.ComponentModel.DataAnnotations.Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonConstructor]
    public ExecuteParallelStepInput(
        string stateId,
        string stepId,
        List<string> parallelStepIds,
        Stack<StackEntry> stackEntries)
    {
        StateId = stateId;
        StepId = stepId;
        ParallelStepIds = parallelStepIds;
        StackEntries = stackEntries;
    }
}