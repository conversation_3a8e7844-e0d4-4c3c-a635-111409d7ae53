using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Tools;
using Sleekflow.Models.Prompts;

namespace Sleekflow.IntelligentHub.Tests.Utils;

public static class AgentConfigUtils
{
    public static CompanyAgentConfig GetAgentConfig(
        string? tone = null,
        LeadNurturingTools? leadNurturingTools = null,
        string? type = null)
    {
        var now = DateTimeOffset.Now;

        return new CompanyAgentConfig(
            Guid.NewGuid().ToString(),
            "Testing Agent",
            string.Empty,
            true,
            true,
            50,
            null,
            null,
            null,
            new PromptInstruction
            {
                Tone = tone ?? TargetToneTypes.Professional,
            },
            type ?? CompanyAgentTypes.Sales,
            AgentCollaborationModes.Default,
            leadNurturingTools,
            now,
            now);
    }
}