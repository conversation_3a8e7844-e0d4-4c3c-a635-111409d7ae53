﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs;
using Sleekflow.AuditHub.Models.UserProfileAuditLogs.Data;
using Sleekflow.AuditHub.UserProfileAuditLogs;
using Sleekflow.DependencyInjection;
using Sleekflow.DistributedInvocations;
using Sleekflow.Ids;

namespace Sleekflow.AuditHub.Triggers.UserProfileAuditLogs;

[TriggerGroup("AuditLogs")]
public class CreateUserProfileDeletedLog
    : ITrigger<
        CreateUserProfileDeletedLog.CreateUserProfileDeletedLogInput,
        CreateUserProfileDeletedLog.CreateUserProfileDeletedLogOutput>
{
    private readonly IUserProfileAuditLogService _userProfileAuditLogService;
    private readonly IIdService _idService;
    private readonly IDistributedInvocationContextService _distributedInvocationContextService;

    public CreateUserProfileDeletedLog(
        IUserProfileAuditLogService userProfileAuditLogService,
        IIdService idService,
        IDistributedInvocationContextService distributedInvocationContextService)
    {
        _userProfileAuditLogService = userProfileAuditLogService;
        _idService = idService;
        _distributedInvocationContextService = distributedInvocationContextService;
    }

    public class CreateUserProfileDeletedLogInput
    {
        [JsonProperty("sleekflow_company_id")]
        public string? SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("sleekflow_user_profile_id")]
        public string SleekflowUserProfileId { get; set; }

        [JsonProperty("sleekflow_staff_id")]
        public string? SleekflowStaffId { get; set; }

        [Required]
        [JsonProperty("audit_log_text")]
        public string AuditLogText { get; set; }

        [Required]
        [JsonProperty("data")]
        [Validations.ValidateObject]
        public UserProfileDeletedLogData Data { get; set; }

        [JsonConstructor]
        public CreateUserProfileDeletedLogInput(
            string? sleekflowCompanyId,
            string sleekflowUserProfileId,
            string? sleekflowStaffId,
            string auditLogText,
            UserProfileDeletedLogData data)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            AuditLogText = auditLogText;
            Data = data;
        }
    }

    public class CreateUserProfileDeletedLogOutput
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonConstructor]
        public CreateUserProfileDeletedLogOutput(string id)
        {
            Id = id;
        }
    }

    public async Task<CreateUserProfileDeletedLogOutput> F(CreateUserProfileDeletedLogInput input)
    {
        var dataStr = JsonConvert.SerializeObject(input.Data);
        var data = JsonConvert.DeserializeObject<Dictionary<string, object?>>(dataStr);

        var id = _idService.GetId("UserProfileAuditLog");
        var distributedInvocationContext = _distributedInvocationContextService.GetContext();
        await _userProfileAuditLogService.CreateUserProfileAuditLogAsync(
            new UserProfileAuditLog(
                id,
                (distributedInvocationContext?.SleekflowCompanyId
                 ?? input.SleekflowCompanyId)
                ?? throw new InvalidOperationException(),
                distributedInvocationContext?.SleekflowStaffId
                ?? input.SleekflowStaffId,
                input.SleekflowUserProfileId,
                UserProfileAuditLogTypes.UserProfileDeleted,
                input.AuditLogText,
                data,
                DateTimeOffset.UtcNow,
                null));

        return new CreateUserProfileDeletedLogOutput(id);
    }
}