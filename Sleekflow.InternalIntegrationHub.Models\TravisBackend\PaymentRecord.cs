using Newtonsoft.Json;

namespace Sleekflow.InternalIntegrationHub.Models.TravisBackend;

public class PaymentRecord
{
    [JsonProperty("company_id")]
    public string? CompanyId { get; set; }

    [JsonProperty("bill_record_id")]
    public long? BillRecordId { get; set; }

    [JsonProperty("subscription_fee")]
    public decimal? SubscriptionFee { get; set; }

    [JsonProperty("one_time_setup_fee")]
    public decimal? OneTimeSetupFee { get; set; }

    [JsonProperty("whatsapp_credit_amount")]
    public decimal? WhatsappCreditAmount { get; set; }

    [JsonProperty("currency")]
    public string? Currency { get; set; }

    [JsonProperty("payment_method")]
    public int? PaymentMethod { get; set; }

    [JsonProperty("paid_at")]
    public DateTime? PaidAt { get; set; }

    [JsonProperty("invoice_id")]
    public string? InvoiceId { get; set; }

    [JsonProperty("last_modified_date")]
    public DateTime? LastModifiedDate { get; set; }

    [JsonProperty("created_at")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("payment_term_int")]
    public int? PaymentTermInt { get; set; }

    [JsonConstructor]
    public PaymentRecord(
        string? companyId,
        long? billRecordId,
        decimal? subscriptionFee,
        decimal? oneTimeSetupFee,
        decimal? whatsappCreditAmount,
        string? currency,
        int? paymentMethod,
        DateTime? paidAt,
        string? invoiceId,
        DateTime? lastModifiedDate,
        DateTime? createdAt,
        int? paymentTermInt)
    {
        CompanyId = companyId;
        BillRecordId = billRecordId;
        SubscriptionFee = subscriptionFee;
        OneTimeSetupFee = oneTimeSetupFee;
        WhatsappCreditAmount = whatsappCreditAmount;
        Currency = currency;
        PaymentMethod = paymentMethod;
        PaidAt = paidAt;
        InvoiceId = invoiceId;
        LastModifiedDate = lastModifiedDate;
        CreatedAt = createdAt;
        PaymentTermInt = paymentTermInt;
    }
}