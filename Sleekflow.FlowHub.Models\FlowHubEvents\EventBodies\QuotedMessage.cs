﻿using Newtonsoft.Json;

namespace Sleekflow.FlowHub.Models.FlowHubEvents.EventBodies;

public class QuotedMessage
{
    [JsonProperty("message_id")]
    public string? MessageId { get; set; }

    [JsonProperty("message_unique_id")]
    public string MessageUniqueId { get; set; }

    [JsonProperty("message_content")]
    public string? MessageContent { get; set; }

    [JsonConstructor]
    public QuotedMessage(
        string? messageId,
        string messageUniqueId,
        string? messageContent)
    {
        MessageId = messageId;
        MessageUniqueId = messageUniqueId;
        MessageContent = messageContent;
    }
}