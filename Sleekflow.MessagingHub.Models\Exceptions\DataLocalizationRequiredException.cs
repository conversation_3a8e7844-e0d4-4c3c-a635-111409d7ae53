namespace Sleekflow.MessagingHub.Models.Exceptions;

/// <summary>
/// An exception that is thrown when data localization is required when Register Cloud API Phone Number.
/// </summary>
public class DataLocalizationRequiredException : Exception
{
    public DataLocalizationRequiredException(string message)
        : base(message)
    {
    }

    public DataLocalizationRequiredException(string message, Exception innerException)
        : base(message, innerException)
    {
    }
}