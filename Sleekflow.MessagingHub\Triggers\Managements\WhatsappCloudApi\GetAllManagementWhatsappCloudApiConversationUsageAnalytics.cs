using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.Moneys;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Balances.TransactionItems.TopUps;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.Utils.CloudApis;
using Sleekflow.MessagingHub.WhatsappCloudApis.Balances;
using Sleekflow.MessagingHub.WhatsappCloudApis.BalanceTransactionLogs;
using Sleekflow.MessagingHub.WhatsappCloudApis.Wabas;

namespace Sleekflow.MessagingHub.Triggers.Managements.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Managements)]
public class GetAllManagementWhatsappCloudApiConversationUsageAnalytics
    : ITrigger<
        GetAllManagementWhatsappCloudApiConversationUsageAnalytics.
        GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput,
        GetAllManagementWhatsappCloudApiConversationUsageAnalytics.
        GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput>
{
    private readonly IBusinessBalanceTransactionLogService _businessBalanceTransactionLogService;
    private readonly IWabaService _wabaService;
    private readonly IBusinessBalanceService _businessBalanceService;
    private readonly ILogger<GetAllManagementWhatsappCloudApiConversationUsageAnalytics> _logger;

    public GetAllManagementWhatsappCloudApiConversationUsageAnalytics(
        ILogger<GetAllManagementWhatsappCloudApiConversationUsageAnalytics> logger,
        IWabaService wabaService,
        IBusinessBalanceTransactionLogService businessBalanceTransactionLogService,
        IBusinessBalanceService businessBalanceService)
    {
        _businessBalanceTransactionLogService = businessBalanceTransactionLogService;
        _businessBalanceService = businessBalanceService;
        _wabaService = wabaService;
        _logger = logger;
    }

    public class GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput
    {
        [JsonConstructor]
        public GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput(
            DateTimeOffset start,
            DateTimeOffset end,
            string granularity,
            int limit,
            string? continuationToken)
        {
            Start = start;
            End = end;
            Granularity = granularity;
            Limit = limit;
            ContinuationToken = continuationToken;
        }

        [Required]
        [JsonProperty("start")]
        public DateTimeOffset Start { get; set; }

        [Required]
        [JsonProperty("end")]
        public DateTimeOffset End { get; set; }

        [Required]
        [RegularExpression("^(HALF_HOUR|DAILY|MONTHLY)$")]
        [JsonProperty("granularity")]
        public string Granularity { get; set; }

        [JsonProperty("limit")]
        [Range(1, 10000)]
        [Required]
        public int Limit { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    public class GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput
    {
        [JsonConstructor]
        public GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput(
            List<ManagementWhatsappCloudApiConversationUsageAnalytic> whatsappCloudApiConversationUsageAnalytics,
            string? continuationToken)
        {
            WhatsappCloudApiConversationUsageAnalytics = whatsappCloudApiConversationUsageAnalytics;
            ContinuationToken = continuationToken;
        }

        [JsonProperty("whatsapp_cloud_api_conversation_usage_analytics")]
        public List<ManagementWhatsappCloudApiConversationUsageAnalytic>
            WhatsappCloudApiConversationUsageAnalytics { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }
    }

    public async Task<GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput> F(
        GetAllManagementWhatsappCloudApiConversationUsageAnalyticsInput input)
    {
        // Adjust End date for including the specified date
        switch (input.Granularity)
        {
            case "HALF_HOUR":
                input.End = input.End.AddMinutes(30);
                break;
            case "DAILY":
            case "MONTHLY":
                input.End = input.End.AddDays(1);
                break;
        }

        if (input.Start.Date > DateTimeOffset.UtcNow.Date.AddHours(12))
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Start date cannot later then today",
                        new[]
                        {
                            nameof(input.Start)
                        })
                });
        }

        if (input.Start > input.End)
        {
            throw new SfValidationException(
                new List<ValidationResult>
                {
                    new (
                        "Start cannot later then End",
                        new[]
                        {
                            nameof(input.End),
                            nameof(input.Start)
                        })
                });
        }

        var (businessBalances, nextContinuationToken) = await _businessBalanceService.GetBusinessBalanceAsync(input.ContinuationToken, input.Limit);

        if (businessBalances.Count == 0)
        {
            throw new SfUserFriendlyException("No business found");
        }

        var result = new ConcurrentBag<ManagementWhatsappCloudApiConversationUsageAnalytic>();

        await Parallel.ForEachAsync(
            businessBalances,
            new ParallelOptions
            {
                MaxDegreeOfParallelism = 5
            },
            async (businessBalance, cancellationToken) =>
            {
                var allWabaConversationUsageAnalytics =
                    new List<(WhatsappCloudApiDetailedConversationUsageAnalyticDto ConversationUsageAnalyic,
                        Models.WhatsappCloudApis.Wabas.Waba Waba)>();

                var wabas = await _wabaService.GetWabaWithFacebookBusinessIdAsync(businessBalance.FacebookBusinessId);

                foreach (var waba in wabas)
                {
                    allWabaConversationUsageAnalytics.Add(
                        await _businessBalanceTransactionLogService
                            .GetWhatsappCloudApiConversationUsageAnalyticByWaba(
                                businessBalance.FacebookBusinessId,
                                waba,
                                input.Start,
                                input.End,
                                input.Granularity,
                                true));
                }

                if (allWabaConversationUsageAnalytics.Count == 0)
                {
                    _logger.LogWarning("No Waba found in the business {FacebookBusinessId}", businessBalance.FacebookBusinessId);
                    return;
                }

                var conversationAnalytics =
                    allWabaConversationUsageAnalytics.Select(x => x.ConversationUsageAnalyic).ToArray();

                var summarizedConversationUsageAnalytic = new WhatsappCloudApiConversationUsageAnalyticDto(
                    conversationAnalytics.Sum(c => c.TotalBusinessInitiatedPaidQuantity),
                    conversationAnalytics.Sum(c => c.TotalBusinessInitiatedFreeTierQuantity),
                    conversationAnalytics.Sum(c => c.TotalUserInitiatedPaidQuantity),
                    conversationAnalytics.Sum(c => c.TotalUserInitiatedFreeTierQuantity),
                    conversationAnalytics.Sum(c => c.TotalUserInitiatedFreeEntryPointQuantity),
                    CloudApiUtils.SumConversationCategoryQuantities(
                        conversationAnalytics.Where(x => x.ConversationCategoryQuantities != null)
                            .Select(c => c.ConversationCategoryQuantities!)
                            .ToList()),
                    conversationAnalytics
                        .Select(y => y.TotalUsed)
                        .Aggregate(
                            new Money(CurrencyIsoCodes.USD, 0),
                            MoneyExtensions.Add),
                    conversationAnalytics
                        .Where(x => x.TotalMarkup != null)
                        .Select(y => y.TotalMarkup!)
                        .Aggregate(
                            new Money(CurrencyIsoCodes.USD, 0),
                            MoneyExtensions.Add),
                    conversationAnalytics
                        .Where(x => x.TotalTransactionHandlingFee != null)
                        .Select(y => y.TotalTransactionHandlingFee!)
                        .Aggregate(
                            new Money(CurrencyIsoCodes.USD, 0),
                            MoneyExtensions.Add),
                    conversationAnalytics.First().Granularity,
                    conversationAnalytics.First().Start,
                    conversationAnalytics.First().End);

                var whatsappCloudApiConversationUsageAnalytic = new ManagementWhatsappCloudApiConversationUsageAnalytic(
                    summarizedConversationUsageAnalytic,
                    allWabaConversationUsageAnalytics.First().Waba.FacebookBusinessId,
                    allWabaConversationUsageAnalytics.First().Waba.FacebookWabaBusinessName,
                    allWabaConversationUsageAnalytics.Select(
                        x =>
                            new WhatsappCloudApiWabaConversationUsageAnalytic(
                                x.ConversationUsageAnalyic,
                                x.Waba.FacebookBusinessId,
                                x.Waba.FacebookWabaBusinessName,
                                new FacebookBusinessWabaDto(
                                    x.Waba,
                                    CloudApiUtils.GetFacebookTimezone(
                                        x.Waba.FacebookWabaSnapshot["timezone_id"].ToString())))).ToList());
                result.Add(whatsappCloudApiConversationUsageAnalytic);
            });

        return new GetAllManagementWhatsappCloudApiConversationUsageAnalyticsOutput(result.ToList(), nextContinuationToken);
    }
}