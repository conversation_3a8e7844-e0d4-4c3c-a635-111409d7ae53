using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.Integrator.Hubspot.Subscriptions;

namespace Sleekflow.Integrator.Hubspot.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class DeactivateTypeSync : ITrigger
{
    private readonly IHubspotSubscriptionService _hubspotSubscriptionService;

    public DeactivateTypeSync(IHubspotSubscriptionService hubspotSubscriptionService)
    {
        _hubspotSubscriptionService = hubspotSubscriptionService;
    }

    public class DeactivateTypeSyncInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonConstructor]
        public DeactivateTypeSyncInput(
            string entityTypeName,
            string sleekflowCompanyId)
        {
            EntityTypeName = entityTypeName;
            SleekflowCompanyId = sleekflowCompanyId;
        }
    }

    public class DeactivateTypeSyncOutput
    {
        [JsonConstructor]
        public DeactivateTypeSyncOutput()
        {
        }
    }

    public async Task<DeactivateTypeSyncOutput> F(
        DeactivateTypeSyncInput deactivateTypeSyncInput)
    {
        await _hubspotSubscriptionService.UpdateWithEmptyDurablePayloadAsync(
            deactivateTypeSyncInput.EntityTypeName,
            deactivateTypeSyncInput.SleekflowCompanyId);

        return new DeactivateTypeSyncOutput();
    }
}