﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Steps.Abstractions;

namespace Sleekflow.FlowHub.Models.Steps.Calls;

public class CreateGoogleSheetsRowStepArgs : TypedCallStepArgs
{
    public const string CallName = "sleekflow.v1.create-google-sheets-row";

    [Required]
    [JsonProperty("connection_id")]
    public string ConnectionId { get; set; }

    [Required]
    [JsonProperty("spreadsheet_id")]
    public string SpreadsheetId { get; set; }

    [Required]
    [JsonProperty("worksheet_id")]
    public string WorksheetId { get; set; }

    [Required]
    [JsonProperty("header_row_id")]
    public string HeaderRowId { get; set; }

    [JsonProperty("fields__id_expr_set")]
    public HashSet<RowFieldIdValuePair> FieldsIdExprSet { get; set; }

    [JsonIgnore]
    [JsonProperty("step_category")]
    public override string StepCategory => WorkflowStepCategories.GoogleSheetsIntegration;

    [JsonConstructor]
    public CreateGoogleSheetsRowStepArgs(
        string connectionId,
        string spreadsheetId,
        string worksheetId,
        string headerRowId,
        HashSet<RowFieldIdValuePair> fieldsIdExprSet)
    {
        ConnectionId = connectionId;
        SpreadsheetId = spreadsheetId;
        WorksheetId = worksheetId;
        HeaderRowId = headerRowId;
        FieldsIdExprSet = fieldsIdExprSet;
    }
}

public class RowFieldIdValuePair
{
    [JsonProperty("field_id")]
    public string FieldId { get; set; }

    [JsonProperty("field_value__expr")]
    public string? FieldValueExpr { get; set; }

    [JsonConstructor]
    public RowFieldIdValuePair(
        string fieldId,
        string? fieldValueExpr)
    {
        FieldId = fieldId;
        FieldValueExpr = fieldValueExpr;
    }
}