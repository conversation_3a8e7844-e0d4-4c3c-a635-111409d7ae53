﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Providers;

public class GetTypeFieldsOutputFieldDto
{
    [JsonProperty("calculated")]
    public bool Calculated { get; set; }

    [JsonProperty("compound_field_name")]
    public string CompoundFieldName { get; set; }

    [JsonProperty("createable")]
    public bool Createable { get; set; }

    [JsonProperty("custom")]
    public bool Custom { get; set; }

    [JsonProperty("encrypted")]
    public bool Encrypted { get; set; }

    [JsonProperty("label")]
    public string Label { get; set; }

    [JsonProperty("length")]
    public int Length { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("picklist_values")]
    public List<GetTypeFieldsOutputPicklistValue> PicklistValues { get; set; }

    [JsonProperty("soap_type")]
    public string SoapType { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("unique")]
    public bool Unique { get; set; }

    [Json<PERSON>roperty("updateable")]
    public bool Updateable { get; set; }

    [JsonProperty("mandatory")]
    public bool? Mandatory { get; set; }

    [JsonConstructor]
    public GetTypeFieldsOutputFieldDto(
        bool calculated,
        string compoundFieldName,
        bool createable,
        bool custom,
        bool encrypted,
        string label,
        int length,
        string name,
        List<GetTypeFieldsOutputPicklistValue> picklistValues,
        string soapType,
        string type,
        bool unique,
        bool updateable,
        bool? mandatory = null)
    {
        Calculated = calculated;
        CompoundFieldName = compoundFieldName;
        Createable = createable;
        Custom = custom;
        Encrypted = encrypted;
        Label = label;
        Length = length;
        Name = name;
        PicklistValues = picklistValues;
        SoapType = soapType;
        Type = type;
        Unique = unique;
        Updateable = updateable;
        Mandatory = mandatory;
    }
}