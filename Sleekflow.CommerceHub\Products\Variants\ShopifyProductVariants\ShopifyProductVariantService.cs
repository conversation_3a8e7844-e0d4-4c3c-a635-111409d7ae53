using Microsoft.Azure.Cosmos;
using Sleekflow.CommerceHub.Models.Common;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.Images;
using Sleekflow.CommerceHub.Models.Products.Variants;
using Sleekflow.DependencyInjection;
using Sleekflow.Ids;

namespace Sleekflow.CommerceHub.Products.Variants.ShopifyProductVariants;

public interface IShopifyProductVariantService
{
    Task<ProductVariant?> GetShopifyProductVariantAsync(
        string sleekflowCompanyId,
        string productId,
        string storeId,
        string platformDataId);

    Task<ProductVariant> CreateAndGetShopifyProductVariantAsync(
        string sleekflowCompanyId,
        string productId,
        string storeId,
        string currencyIsoCode,
        List<Image> images,
        Dictionary<string, object?> providerProductVariant,
        Dictionary<string, object?> metadata);

    Task<ProductVariant> PatchAndGetShopifyProductVariantAsync(
        string sleekflowCompanyId,
        string productVariantId,
        string currencyIsoCode,
        List<Image> images,
        Dictionary<string, object?> providerProductVariant,
        Dictionary<string, object?> metadata);
}

public class ShopifyProductVariantService : IShopifyProductVariantService, IScopedService
{
    private readonly IProductVariantRepository _productVariantRepository;
    private readonly IIdService _idService;

    public ShopifyProductVariantService(
        IProductVariantRepository productVariantRepository,
        IIdService idService)
    {
        _productVariantRepository = productVariantRepository;
        _idService = idService;
    }

    public async Task<ProductVariant?> GetShopifyProductVariantAsync(
        string sleekflowCompanyId,
        string productId,
        string storeId,
        string platformDataId)
    {
        var products = await _productVariantRepository.GetObjectsAsync(
            p => p.SleekflowCompanyId == sleekflowCompanyId
                 && p.StoreId == storeId
                 && p.ProductId == productId
                 && p.PlatformData.Type == PlatformDataTypes.Shopify
                 && p.PlatformData.Id == platformDataId);

        return products.FirstOrDefault();
    }

    public async Task<ProductVariant> CreateAndGetShopifyProductVariantAsync(
        string sleekflowCompanyId,
        string productId,
        string storeId,
        string currencyIsoCode,
        List<Image> images,
        Dictionary<string, object?> providerProductVariant,
        Dictionary<string, object?> metadata)
    {
        string? url = null;
        var isDefaultProductVariant = false;
        var productVariantAttributes = new List<ProductVariant.ProductVariantAttribute>();
        var createdAt = ((DateTimeOffset) providerProductVariant["CreatedAt"]!).UtcDateTime;

        var productVariantProperties = GetCreatableAndUpdatableProductVariantProperties(
            currencyIsoCode,
            providerProductVariant);

        return await _productVariantRepository.CreateAndGetAsync(
            new ProductVariant(
            _idService.GetId(SysTypeNames.ProductVariant),
            sleekflowCompanyId,
            storeId,
            productId,
            productVariantProperties.Sku,
            url,
            productVariantProperties.Prices,
            productVariantProperties.Position,
            isDefaultProductVariant,
            productVariantAttributes,
            productVariantProperties.Names,
            productVariantProperties.Descriptions,
            images,
            productVariantProperties.PlatformData,
            new List<string>
            {
                "Active"
            },
            metadata,
            null,
            null,
            createdAt,
            productVariantProperties.UpdatedAt),
            sleekflowCompanyId);
    }

    public async Task<ProductVariant> PatchAndGetShopifyProductVariantAsync(
        string sleekflowCompanyId,
        string productVariantId,
        string currencyIsoCode,
        List<Image> images,
        Dictionary<string, object?> providerProductVariant,
        Dictionary<string, object?> metadata)
    {
        var productVariantProperties = GetCreatableAndUpdatableProductVariantProperties(
            currencyIsoCode,
            providerProductVariant);

        return await _productVariantRepository.PatchAndGetAsync(
            productVariantId,
            sleekflowCompanyId,
            new List<PatchOperation>
            {
                PatchOperation.Set("/sku", productVariantProperties.Sku),
                PatchOperation.Replace("/position", productVariantProperties.Position),
                PatchOperation.Replace("/prices", productVariantProperties.Prices),
                PatchOperation.Replace("/names", productVariantProperties.Names),
                PatchOperation.Replace("/descriptions", productVariantProperties.Descriptions),
                PatchOperation.Replace("/updated_at", productVariantProperties.UpdatedAt),
                PatchOperation.Replace("/platform_data", productVariantProperties.PlatformData),
                PatchOperation.Replace("/metadata", metadata)
            });
    }

    private static (
        string Sku,
        int Position,
        List<Price> Prices,
        List<Multilingual> Names,
        List<Description> Descriptions,
        DateTimeOffset UpdatedAt,
        PlatformData PlatformData)
        GetCreatableAndUpdatableProductVariantProperties(
            string currencyIsoCode,
            Dictionary<string, object?> providerProductVariant)
    {
        var sku = (string) providerProductVariant["SKU"]!;
        var position = (int) providerProductVariant["Position"]!;
        var prices = new List<Price>
        {
            new (currencyIsoCode, (decimal) providerProductVariant["Price"]!)
        };
        var names = new List<Multilingual>
        {
            new(languageIsoCode: "en", value: (string)providerProductVariant["Title"]!)
        };
        var descriptions = new List<Description>
        {
            new (
                type: "text",
                text: new Multilingual(languageIsoCode: "en", value: (string)providerProductVariant["Title"]!),
                image: null,
                youtube: null)
        };
        var updatedAt = ((DateTimeOffset) providerProductVariant["UpdatedAt"]!).UtcDateTime;

        var providerProductVariantId = (string) providerProductVariant["Id"]!;
        var platformData = new PlatformData(
            providerProductVariantId,
            PlatformDataTypes.Shopify,
            providerProductVariant);

        return (
            sku,
            position,
            prices,
            names,
            descriptions,
            updatedAt,
            platformData);
    }
}