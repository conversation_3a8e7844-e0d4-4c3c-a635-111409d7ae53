using System.ComponentModel;
using Microsoft.SemanticKernel;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.IntelligentHub.Documents;
using Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.Longs;
using Sleekflow.IntelligentHub.Models.Companies.CompanyAgentConfigs;
using Sleekflow.IntelligentHub.Models.Constants;
using Sleekflow.IntelligentHub.Models.Documents.FilesDocuments;
using Sleekflow.IntelligentHub.Models.Workers;
using Sleekflow.IntelligentHub.Plugins.Models;

namespace Sleekflow.IntelligentHub.Plugins.Knowledges;

[method: JsonConstructor]
public class LongAgentKnowledgePluginResponse : QueryKnowledgeResponse
{
    public LongAgentKnowledgePluginResponse(string knowledge, string id)
        : base(knowledge, id, "LongAgentKnowledgeBase")
    {
    }
}

public interface ILongAgentKnowledgePlugin : IKnowledgePlugin
{
}

/// <summary>
/// Long Agent Knowledge Plugin Wrapper
///
/// This plugin is specifically designed for long agent groups and intelligently decides between
/// using regular KnowledgePlugin or AgenticKnowledgePlugin with web search capabilities based
/// on the presence of in-progress website documents.
///
/// Primary Purpose:
/// Enable real-time web crawling for selected upload sites and combine results with Knowledge Base
/// content to answer user questions in Long Agent Mode, using the specialized ILongAgentCollaborationChatCacheService
/// to cache knowledge and avoid redundant LLM processing for tool call results.
///
/// How it works:
/// 1. Checks for in-progress website documents for the current agent
/// 2. If in-progress websites exist:
///    - Uses AgenticKnowledgePlugin with web search capabilities
///    - Configures WebSearchPlugin to search the in-progress sites
///    - Provides real-time information from websites being uploaded
/// 3. If no in-progress websites:
///    - Uses regular KnowledgePlugin with existing knowledge base
/// 4. Uses ILongAgentCollaborationChatCacheService to cache all confirmed knowledge
///    to enable efficient knowledge reuse without LLM processing
///
/// Benefits:
/// - Seamless user experience: No waiting for uploads to complete
/// - Real-time information: Access to latest content from websites being processed
/// - Intelligent fallback: Uses regular knowledge base when no uploads in progress
/// - Automatic configuration: No manual setup required for web search
/// - Efficient caching: Knowledge is cached for reuse within long agent collaborations
/// - Optimized performance: Avoids redundant LLM calls for previously retrieved knowledge
/// </summary>
public class LongAgentKnowledgePlugin : ILongAgentKnowledgePlugin, IScopedService
{
    private readonly ILogger<LongAgentKnowledgePlugin> _logger;
    private readonly IKbDocumentService _kbDocumentService;
    private readonly IKnowledgePlugin _knowledgePlugin;
    private readonly IAgenticKnowledgePlugin _agenticKnowledgePlugin;
    private readonly ILongAgentCollaborationChatCacheService _longAgentCollaborationChatCacheService;

    public LongAgentKnowledgePlugin(
        ILogger<LongAgentKnowledgePlugin> logger,
        IKbDocumentService kbDocumentService,
        IKnowledgePlugin knowledgePlugin,
        IAgenticKnowledgePlugin agenticKnowledgePlugin,
        ILongAgentCollaborationChatCacheService longAgentCollaborationChatCacheService)
    {
        _logger = logger;
        _kbDocumentService = kbDocumentService;
        _knowledgePlugin = knowledgePlugin;
        _agenticKnowledgePlugin = agenticKnowledgePlugin;
        _longAgentCollaborationChatCacheService = longAgentCollaborationChatCacheService;
    }

    [KernelFunction("query_knowledge")]
    [Description(
        "Intelligently retrieves information from the knowledge base for long agent collaborations. " +
        "Automatically enables real-time web crawling for selected upload sites and combines results " +
        "with Knowledge Base content when there are in-progress website documents. Uses specialized " +
        "caching to avoid redundant LLM processing for previously retrieved knowledge.")]
    [return: Description(
        "Tailored information from the knowledge base, potentially enhanced with real-time web search results, " +
        "optimized for long agent collaborations.")]
    public async Task<QueryKnowledgeResponse> QueryKnowledgeAsync(
        Kernel kernel,
        [Description(
            "Express your information needs naturally, whether as direct questions, scenario descriptions, or specific information requests. Provide clear context, specific requirements, and any relevant background details for the most accurate and useful results.")]
        string query)
    {
        var sleekflowCompanyId = (string) kernel.Data[KernelDataKeys.SLEEKFLOW_COMPANY_ID]!;
        var agentId = (string) kernel.Data[KernelDataKeys.AGENT_ID]!;
        var groupChatIdStr = kernel.Data.TryGetValue(KernelDataKeys.GROUP_CHAT_ID, out var value)
            ? (string) value!
            : Guid.NewGuid().ToString();

        _logger.LogInformation(
            "LongAgentKnowledgePlugin: Processing query for company {SleekflowCompanyId}, agent {AgentId}, groupChat {GroupChatId}, query: {Query}",
            sleekflowCompanyId,
            agentId,
            groupChatIdStr,
            query);

        // Check for in-progress website documents for this agent
        var inProgressWebsiteDocuments = await GetInProgressWebsiteDocumentsAsync(sleekflowCompanyId, agentId);

        QueryKnowledgeResponse response;

        if (inProgressWebsiteDocuments.Any())
        {
            _logger.LogInformation(
                "Found {Count} in-progress website documents. Using AgenticKnowledgePlugin with web search capabilities.",
                inProgressWebsiteDocuments.Count);

            // Configure web search for the in-progress websites
            ConfigureWebSearchForInProgressSites(kernel, inProgressWebsiteDocuments);

            // Use the agentic knowledge plugin with web search capabilities
            var agenticResponse = await _agenticKnowledgePlugin.QueryKnowledgeAsync(kernel, query);

            response = new LongAgentKnowledgePluginResponse(
                agenticResponse.Knowledge,
                Guid.NewGuid().ToString());
        }
        else
        {
            _logger.LogInformation(
                "No in-progress website documents found. Using regular KnowledgePlugin.");

            // Use the regular knowledge plugin
            var regularResponse = await _knowledgePlugin.QueryKnowledgeAsync(kernel, query);

            response = new LongAgentKnowledgePluginResponse(
                regularResponse.Knowledge,
                Guid.NewGuid().ToString());
        }

        // Cache the confirmed knowledge for long agent collaboration efficiency
        // This prevents redundant LLM processing for previously retrieved knowledge
        await CacheConfirmedKnowledgeAsync(groupChatIdStr, response.Knowledge);

        return response;
    }

    /// <summary>
    /// Retrieves website documents that are currently being processed for the given agent.
    /// </summary>
    private async Task<List<WebsiteDocument>> GetInProgressWebsiteDocumentsAsync(
        string sleekflowCompanyId,
        string agentId)
    {
        try
        {
            // Get all documents for this company and agent
            var allDocuments = await _kbDocumentService.GetKbDocumentsByCompanyAsync(
                sleekflowCompanyId,
                filterAgent: agentId);

            // Filter to only website documents that are in progress
            var inProgressWebsiteDocuments = allDocuments
                .OfType<WebsiteDocument>()
                .Where(IsWebsiteDocumentInProgress)
                .ToList();

            _logger.LogInformation(
                "Found {TotalDocuments} total documents, {WebsiteDocuments} website documents, {InProgressWebsiteDocuments} in-progress website documents",
                allDocuments.Count,
                allDocuments.OfType<WebsiteDocument>().Count(),
                inProgressWebsiteDocuments.Count);

            return inProgressWebsiteDocuments;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving website documents for company {SleekflowCompanyId}, agent {AgentId}",
                sleekflowCompanyId, agentId);
            return new List<WebsiteDocument>();
        }
    }

    /// <summary>
    /// Determines if a website document is currently being processed.
    /// Checks document processing status, agent assignment status, and individual URL statuses.
    /// </summary>
    private static bool IsWebsiteDocumentInProgress(WebsiteDocument websiteDoc)
    {
        // Check if the document's processing status indicates it's still being processed
        var isDocumentInProgress = websiteDoc.FileDocumentProcessStatus is
            ProcessFileDocumentStatuses.NotConverted or
            ProcessFileDocumentStatuses.Converting;

        // Check if any agent assignment is still in progress
        var hasInProgressAgentAssignment = websiteDoc.AgentAssignments?.Any(assignment =>
            assignment.RagStatus is RagStatus.Pending or RagStatus.InProgress) ?? false;

        // Check if any selected URLs are still being processed
        var hasInProgressUrls = websiteDoc.SelectedUrls?.Any(url =>
            url.Status is SelectedUrlStatuses.Pending or SelectedUrlStatuses.Converting) ?? false;

        return isDocumentInProgress || hasInProgressAgentAssignment || hasInProgressUrls;
    }

    /// <summary>
    /// Configures web search to target the domains of in-progress website documents.
    /// Creates trigger site mappings that restrict search to specific domains being uploaded.
    /// </summary>
    private void ConfigureWebSearchForInProgressSites(Kernel kernel, List<WebsiteDocument> inProgressWebsiteDocuments)
    {
        try
        {
            // Extract unique base URLs from in-progress website documents
            var baseUrls = inProgressWebsiteDocuments
                .Select(doc => doc.BaseUrl)
                .Distinct()
                .ToList();

            _logger.LogInformation(
                "Configuring web search for {Count} in-progress sites: {Sites}",
                baseUrls.Count,
                string.Join(", ", baseUrls));

            // Create trigger site mappings for each base URL
            var triggerSiteMappings = baseUrls.Select((baseUrl, index) =>
            {
                // Extract domain from URL for site search
                var uri = new Uri(baseUrl);
                var domain = uri.Host;

                return new TriggerSiteMapping(
                    trigger: $"site_{index}",
                    site: domain,
                    pattern: "{query}");
            }).ToList();

            // Create web search config
            var webSearchConfig = new WebSearchConfig(triggerSiteMappings);

            // Add the web search config to kernel data
            kernel.Data[KernelDataKeys.WEB_SEARCH_PLUGIN_CONFIG] = webSearchConfig;

            _logger.LogInformation(
                "Successfully configured web search with {Count} trigger mappings",
                triggerSiteMappings.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error configuring web search for in-progress sites");
        }
    }

    /// <summary>
    /// Caches the confirmed knowledge using the long agent collaboration chat cache service.
    /// This enables efficient knowledge reuse within long agent collaborations without
    /// requiring additional LLM processing for previously retrieved knowledge.
    /// </summary>
    private async Task CacheConfirmedKnowledgeAsync(string groupChatIdStr, string knowledge)
    {
        try
        {
            if (!string.IsNullOrWhiteSpace(knowledge))
            {
                await _longAgentCollaborationChatCacheService.AppendConfirmedKnowledgeAsync(
                    groupChatIdStr,
                    knowledge);

                _logger.LogInformation(
                    "Successfully cached confirmed knowledge for group chat {GroupChatId}",
                    groupChatIdStr);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error caching confirmed knowledge for group chat {GroupChatId}",
                groupChatIdStr);
        }
    }
}