﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.CrmHubConfigs;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.CrmHubConfigs;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.CrmHubConfigs;

[TriggerGroup(TriggerGroups.CrmHubConfigs)]
public class UpdateCrmHubConfig : ITrigger<UpdateCrmHubConfig.UpdateCrmHubConfigInput, UpdateCrmHubConfig.UpdateCrmHubConfigOutput>
{
    private readonly ICrmHubConfigService _crmHubConfigService;

    public UpdateCrmHubConfig(ICrmHubConfigService crmHubConfigService)
    {
        _crmHubConfigService = crmHubConfigService;
    }

    public class UpdateCrmHubConfigInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("id")]
        public string Id { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(CrmHubConfig.PropertyNameUsageLimit)]
        [Validations.ValidateObject]
        public UsageLimit UsageLimit { get; set; }

        [JsonProperty(CrmHubConfig.PropertyNameFeatureAccessibilitySettings)]
        [Validations.ValidateObject]
        public FeatureAccessibilitySettings? FeatureAccessibilitySettings { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateCrmHubConfigInput(
            string id,
            string sleekflowCompanyId,
            UsageLimit usageLimit,
            FeatureAccessibilitySettings? featureAccessibilitySettings,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            Id = id;
            SleekflowCompanyId = sleekflowCompanyId;
            UsageLimit = usageLimit;
            FeatureAccessibilitySettings = featureAccessibilitySettings;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateCrmHubConfigOutput
    {
        [JsonProperty("crm_hub_config")]
        public CrmHubConfig CrmHubConfig { get; set; }

        [JsonConstructor]
        public UpdateCrmHubConfigOutput(CrmHubConfig crmHubConfig)
        {
            CrmHubConfig = crmHubConfig;
        }
    }

    public async Task<UpdateCrmHubConfigOutput> F(UpdateCrmHubConfigInput updateCrmHubConfigInput)
    {
        var updatedBy = string.IsNullOrWhiteSpace(updateCrmHubConfigInput.SleekflowStaffId)
            ? null
            : new AuditEntity.SleekflowStaff(
                updateCrmHubConfigInput.SleekflowStaffId,
                updateCrmHubConfigInput.SleekflowStaffTeamIds);

        var crmHubConfig = await _crmHubConfigService.UpdateCrmHubConfigAsync(
            updateCrmHubConfigInput.Id,
            updateCrmHubConfigInput.SleekflowCompanyId,
            updateCrmHubConfigInput.UsageLimit,
            updateCrmHubConfigInput.FeatureAccessibilitySettings,
            updatedBy);

        return new UpdateCrmHubConfigOutput(crmHubConfig);
    }
}