﻿using Microsoft.Extensions.Logging;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Workers.Subscriptions;

public interface ISalesforceSubscriptionRepository : IRepository<SalesforceSubscription>
{
}

public class SalesforceSubscriptionRepository
    : BaseRepository<SalesforceSubscription>,
        ISalesforceSubscriptionRepository
{
    public SalesforceSubscriptionRepository(
        ILogger<BaseRepository<SalesforceSubscription>> logger,
        IServiceProvider serviceProvider)
        : base(logger, serviceProvider)
    {
    }
}