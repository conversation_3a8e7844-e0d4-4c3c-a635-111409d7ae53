﻿using Newtonsoft.Json;

namespace Sleekflow.CrmHub.Models.Workers;

public class LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput
{
    [JsonProperty("count")]
    public long Count { get; set; }

    [JsonProperty("next_continuation_token")]
    public string? NextContinuationToken { get; set; }

    [JsonConstructor]
    public LoopThroughAndEnrollSchemafulObjectsToFlowHubBatchOutput(long count, string? nextContinuationToken)
    {
        Count = count;
        NextContinuationToken = nextContinuationToken;
    }
}