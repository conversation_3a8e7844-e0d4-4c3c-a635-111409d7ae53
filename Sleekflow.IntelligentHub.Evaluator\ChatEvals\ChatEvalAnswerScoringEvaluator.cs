using System.Text.Json;
using Microsoft.Extensions.AI;
using Microsoft.Extensions.AI.Evaluation;
using Microsoft.Extensions.AI.Evaluation.Quality;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Sleekflow.IntelligentHub.Evaluator.ChatEvals;

public class ChatEvalAnswerScoringEvaluator : IEvaluator
{
    public const string AnswerScoringMetricName = "AnswerScoring";

    public const int FullScore = 5;

    public IReadOnlyCollection<string> EvaluationMetricNames { get; } =
    [
        AnswerScoringMetricName
    ];

    private const string JsonSchemaInput =
        """
        {
          "$schema": "http://json-schema.org/draft-07/schema#",
          "strict": true,
          "type": "object",
          "required": ["qualitative_analysis", "raw_scores"],
          "additionalProperties": false,
          "properties": {
          "qualitative_analysis": {
              "type": "object",
              "required": [
                "scenario_context",
                "emotional_intelligence_analysis",
                "value_building_analysis",
                "consultation_quality",
                "response_strength_analysis",
                "detailed_feedback"
              ],
              "additionalProperties": false,
              "properties": {
                "scenario_context": {
                  "type": "object",
                  "required": ["scenario", "customer_concern", "response_text", "model_answer"],
                  "additionalProperties": false,
                  "properties": {
                    "scenario": {
                      "type": "string"
                    },
                    "customer_concern": {
                      "type": "string"
                    },
                    "response_text": {
                      "type": "string"
                    },
                    "model_answer": {
                      "type": "string"
                    }
                  }
                },
                "emotional_intelligence_analysis": {
                  "type": "object",
                  "required": ["empathy", "authenticity", "trust_building", "active_listening", "engagement", "personality"],
                  "additionalProperties": false,
                  "properties": {
                    "empathy": {
                      "$ref": "#/definitions/analysisComponent"
                    },
                    "authenticity": {
                      "type": "object",
                      "required": ["natural_elements", "areas_of_improvement", "specific_examples"],
                      "additionalProperties": false,
                      "properties": {
                        "natural_elements": {
                          "type": "array",
                          "items": {"type": "string"}
                        },
                        "areas_of_improvement": {
                          "type": "array",
                          "items": {"type": "string"}
                        },
                        "specific_examples": {
                          "type": "array",
                          "items": {"type": "string"}
                        }
                      }
                    },
                    "trust_building": {
                      "type": "object",
                      "required": ["trust_elements", "missed_opportunities", "improvement_areas"],
                      "additionalProperties": false,
                      "properties": {
                        "trust_elements": {
                          "type": "array",
                          "items": {"type": "string"}
                        },
                        "missed_opportunities": {
                          "type": "array",
                          "items": {"type": "string"}
                        },
                        "improvement_areas": {
                          "type": "array",
                          "items": {"type": "string"}
                        }
                      }
                    },
                    "active_listening": {
                      "type": "object",
                      "required": ["evidence", "missed_points"],
                      "additionalProperties": false,
                      "properties": {
                        "evidence": {
                          "type": "array",
                          "items": {"type": "string"}
                        },
                        "missed_points": {
                          "type": "array",
                          "items": {"type": "string"}
                        }
                      }
                    },
                    "engagement": {
                      "type": "object",
                      "required": ["techniques_used", "improvement_suggestions"],
                      "additionalProperties": false,
                      "properties": {
                        "techniques_used": {
                          "type": "array",
                          "items": {"type": "string"}
                        },
                        "improvement_suggestions": {
                          "type": "array",
                          "items": {"type": "string"}
                        }
                      }
                    },
                    "personality": {
                      "type": "object",
                      "required": ["tone_analysis", "style_observations"],
                      "additionalProperties": false,
                      "properties": {
                        "tone_analysis": {
                          "type": "string"
                        },
                        "style_observations": {
                          "type": "array",
                          "items": {"type": "string"}
                        }
                      }
                    }
                  }
                },
                "value_building_analysis": {
                  "type": "object",
                  "required": ["concern_acknowledgment", "value_transition", "benefit_explanation", "notable_phrases"],
                  "additionalProperties": false,
                  "properties": {
                    "concern_acknowledgment": {
                      "type": "object",
                      "required": ["text", "effectiveness"],
                      "additionalProperties": false,
                      "properties": {
                        "text": {"type": "string"},
                        "effectiveness": {"type": "string"}
                      }
                    },
                    "value_transition": {
                      "type": "object",
                      "required": ["approach", "effectiveness"],
                      "additionalProperties": false,
                      "properties": {
                        "approach": {"type": "string"},
                        "effectiveness": {"type": "string"}
                      }
                    },
                    "benefit_explanation": {
                      "type": "object",
                      "required": ["key_points", "context_relevance"],
                      "additionalProperties": false,
                      "properties": {
                        "key_points": {
                          "type": "array",
                          "items": {"type": "string"}
                        },
                        "context_relevance": {"type": "string"}
                      }
                    },
                    "notable_phrases": {
                      "type": "array",
                      "items": {"type": "string"}
                    }
                  }
                },
                "consultation_quality": {
                  "type": "object",
                  "required": ["consultative_elements", "sales_elements", "balance_analysis"],
                  "additionalProperties": false,
                  "properties": {
                    "consultative_elements": {
                      "type": "array",
                      "items": {"type": "string"}
                    },
                    "sales_elements": {
                      "type": "array",
                      "items": {"type": "string"}
                    },
                    "balance_analysis": {"type": "string"}
                  }
                },
                "response_strength_analysis": {
                  "type": "object",
                  "required": ["key_strengths", "primary_issues", "improvement_recommendations"],
                  "additionalProperties": false,
                  "properties": {
                    "key_strengths": {
                      "type": "array",
                      "items": {"type": "string"}
                    },
                    "primary_issues": {
                      "type": "array",
                      "items": {"type": "string"}
                    },
                    "improvement_recommendations": {
                      "type": "array",
                      "items": {"type": "string"}
                    }
                  }
                },
                "detailed_feedback": {
                  "type": "object",
                  "required": ["positive_elements", "areas_for_improvement", "actionable_suggestions"],
                  "additionalProperties": false,
                  "properties": {
                    "positive_elements": {
                      "type": "array",
                      "items": {"type": "string"}
                    },
                    "areas_for_improvement": {
                      "type": "array",
                      "items": {"type": "string"}
                    },
                    "actionable_suggestions": {
                      "type": "array",
                      "items": {"type": "string"}
                    }
                  }
                }
              }
            },
            "raw_scores": {
              "type": "object",
              "required": ["emotional_intelligence", "aida_framework", "penalties"],
              "additionalProperties": false,
              "properties": {
                "emotional_intelligence": {
                  "type": "object",
                  "required": ["empathy", "authenticity", "trust_building", "active_listening", "engagement", "personality"],
                  "additionalProperties": false,
                  "properties": {
                    "empathy": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "authenticity": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "trust_building": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "active_listening": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "engagement": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "personality": {
                      "$ref": "#/definitions/componentScore"
                    }
                  }
                },
                "aida_framework": {
                  "type": "object",
                  "required": ["awareness", "interest", "desire", "action"],
                  "additionalProperties": false,
                  "properties": {
                    "awareness": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "interest": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "desire": {
                      "$ref": "#/definitions/componentScore"
                    },
                    "action": {
                      "$ref": "#/definitions/componentScore"
                    }
                  }
                },
                "penalties": {
                  "type": "object",
                  "required": ["items", "deduction_per_item"],
                  "additionalProperties": false,
                  "properties": {
                    "items": {
                      "type": "array",
                      "items": {
                        "type": "string"
                      }
                    },
                    "deduction_per_item": {
                      "type": "number"
                    }
                  }
                }
              }
            }
          },
          "definitions": {
            "componentScore": {
              "type": "object",
              "required": ["score", "weight"],
              "additionalProperties": false,
              "properties": {
                "score": {
                  "type": "integer"
                },
                "weight": {
                  "type": "number"
                }
              }
            },
            "analysisComponent": {
              "type": "object",
              "required": ["observations", "examples_from_text", "improvement_suggestions"],
              "additionalProperties": false,
              "properties": {
                "observations": {
                  "type": "array",
                  "items": {"type": "string"}
                },
                "examples_from_text": {
                  "type": "array",
                  "items": {"type": "string"}
                },
                "improvement_suggestions": {
                  "type": "array",
                  "items": {"type": "string"}
                }
              }
            }
          }
        }
        """;

    private ChatOptions? ChatOptions { get; } = new ()
    {
        MaxOutputTokens = 8192,
        Temperature = 0.0f,
        TopP = 1f,
        PresencePenalty = 0.0f,
        FrequencyPenalty = 0.0f,
        ResponseFormat = ChatResponseFormat.ForJsonSchema(
            JsonSerializer.SerializeToElement(
                JsonDocument.Parse(JsonSchemaInput))),
    };


    public async ValueTask<EvaluationResult> EvaluateAsync(
        IEnumerable<ChatMessage> messages,
        ChatResponse modelResponse,
        ChatConfiguration? chatConfiguration = null,
        IEnumerable<EvaluationContext>? additionalContext = null,
        CancellationToken cancellationToken = new CancellationToken())
    {
        var context = additionalContext?.OfType<AnswerScoringEvaluationContext>().FirstOrDefault() ??
                      throw new Exception("Expect to find context in additionalContext.");

        var prompt =
            $$"""
              You are an expert scoring system evaluating AI responses in customer service and sales scenarios, with special focus on authenticity, consultative approach, and value building. Your goal is to assess how effectively responses build genuine connections while addressing customer concerns.

              Scoring Scale:
              1 = Robotic or sales-heavy response
              2 = Basic response with limited personal connection
              3 = Good balance but room for improvement
              4 = Strong consultative approach
              5 = Exceptional customer-centric response with natural value building

              Special Scenarios:
              - As AI Agent couldn't handle all scenarios, when the response is about to hand off to a human agent or another agent, we should give a score of 3 as the base score. And evaluate the response based on the handoff context and the transition to the next agent.

              Core Evaluation Dimensions:

              1. Emotional Intelligence & Engagement (60%):
              - Empathy (25%): Understanding and acknowledging customer concerns
              - Authenticity (20%): Natural, consultative conversation style
              - Trust Building (20%): Creating credibility and rapport
              - Active Listening (15%): Proper response to specific points
              - Engagement (10%): Encouraging meaningful dialogue
              - Personality (10%): Appropriate warmth and character

              2. AIDA Framework Implementation (40%):
              - Awareness (30%): Clear understanding of customer situation
              - Interest (25%): Natural engagement and value building
              - Desire (25%): Contextual benefit explanation
              - Action (20%): Appropriate next steps without pushing

              Detailed Scoring Criteria:

              Empathy (1-5):
              5: Deep understanding with contextual acknowledgment
              - Acknowledges specific concerns
              - Shows understanding of underlying needs
              - Validates customer feelings
              - Provides relevant context

              4: Clear acknowledgment with some context
              3: Basic acknowledgment without context
              2: Generic response
              1: No empathy shown

              Authenticity (1-5):
              5: Natural consultative approach
              - Conversational tone
              - No marketing jargon
              - Genuine interest in customer needs
              - Natural flow from concern to solution

              4: Good balance of professional and personal
              3: Professional but somewhat formal
              2: Overly formal or scripted
              1: Robotic or completely impersonal

              Trust Building (1-5):
              5: Strong credibility establishment
              - Shares relevant experience
              - Transparent about limitations
              - Balanced perspective
              - Customer-centric explanations

              4: Good trust elements with some room for improvement
              3: Basic trust elements present
              2: Minimal trust building
              1: No trust building elements

              Value Building Assessment:
              - Natural transition from acknowledgment to value
              - Customer-centric benefit explanation
              - Contextual feature presentation
              - Consultative tone maintenance

              Negative Scoring Factors (-0.25 point each):
              - Immediate pivot to sales
              - Feature listing without context
              - Pushing alternatives too early
              - Marketing-heavy language
              - Ignoring customer concerns
              - Robotic or templated responses

              Example Calibration:

              5-Score Response:
              "我理解這個價格確實需要認真考慮。讓我先了解一下您平時主要使用手機的情況，這樣我才能更好地說明這個計劃是否真的適合您。很多客戶最初也有類似的考慮，但他們發現..."
              Why: Shows deep understanding, consultative approach, natural flow

              3-Score Response:
              "這個計劃提供很多功能，包括20GB數據，全球漫遊等。價格方面我們也有其他選擇..."
              Why: Basic information, lacks personal touch, more feature-focused

              1-Score Response:
              "我們現在有特別優惠，要不要考慮轉到這個更便宜的計劃？"
              Why: Purely sales-focused, ignores concerns, no value building

              Scoring Process:
              1. Analyze initial response to customer concern
              2. Evaluate natural flow and consultation quality
              3. Assess value building approach
              4. Check for penalties
              5. Calculate weighted scores
              6. Apply adjustments
              7. Provide detailed rationale

              Required Output Format:
              {
                "qualitative_analysis": {
                  "scenario_context": {
                    "scenario": "string",
                    "customer_concern": "string",
                    "response_text": "string",
                    "model_answer": "string"
                  },
                  "emotional_intelligence_analysis": {
                    "empathy": {
                      "observations": ["string"],
                      "examples_from_text": ["string"],
                      "improvement_suggestions": ["string"]
                    },
                    "authenticity": {
                      "natural_elements": ["string"],
                      "areas_of_improvement": ["string"],
                      "specific_examples": ["string"]
                    },
                    "trust_building": {
                      "trust_elements": ["string"],
                      "missed_opportunities": ["string"],
                      "improvement_areas": ["string"]
                    },
                    "active_listening": {
                      "evidence": ["string"],
                      "missed_points": ["string"]
                    },
                    "engagement": {
                      "techniques_used": ["string"],
                      "improvement_suggestions": ["string"]
                    },
                    "personality": {
                      "tone_analysis": "string",
                      "style_observations": ["string"]
                    }
                  },
                  "value_building_analysis": {
                    "concern_acknowledgment": {
                      "text": "string",
                      "effectiveness": "string"
                    },
                    "value_transition": {
                      "approach": "string",
                      "effectiveness": "string"
                    },
                    "benefit_explanation": {
                      "key_points": ["string"],
                      "context_relevance": "string"
                    },
                    "notable_phrases": ["string"]
                  },
                  "consultation_quality": {
                    "consultative_elements": ["string"],
                    "sales_elements": ["string"],
                    "balance_analysis": "string"
                  },
                  "response_strength_analysis": {
                    "key_strengths": ["string"],
                    "primary_issues": ["string"],
                    "improvement_recommendations": ["string"]
                  },
                  "detailed_feedback": {
                    "positive_elements": ["string"],
                    "areas_for_improvement": ["string"],
                    "actionable_suggestions": ["string"]
                  }
                },
                "raw_scores": {
                  "emotional_intelligence": {
                    "empathy": {
                      "score": 1-5,
                      "weight": 0.25
                    },
                    "authenticity": {
                      "score": 1-5,
                      "weight": 0.20
                    },
                    "trust_building": {
                      "score": 1-5,
                      "weight": 0.20
                    },
                    "active_listening": {
                      "score": 1-5,
                      "weight": 0.15
                    },
                    "engagement": {
                      "score": 1-5,
                      "weight": 0.10
                    },
                    "personality": {
                      "score": 1-5,
                      "weight": 0.10
                    }
                  },
                  "aida_framework": {
                    "awareness": {
                      "score": 1-5,
                      "weight": 0.30
                    },
                    "interest": {
                      "score": 1-5,
                      "weight": 0.25
                    },
                    "desire": {
                      "score": 1-5,
                      "weight": 0.25
                    },
                    "action": {
                      "score": 1-5,
                      "weight": 0.20
                    }
                  },
                  "penalties": {
                    "items": [
                      "string (description of each penalty applied)"
                    ],
                    "deduction_per_item": 0.25
                  }
                }
              }

              Here is the full context to be scored:

              ======Scenario======
              {{context.Scenario}}
              ======Scenario======

              ======Customer Concern=======
              {{messages.RenderText()}}
              ======Customer Concern=======

              ======AI Response=======
              {{modelResponse}}
              ======AI Response=======

              ======Reference Model Answer=======
              {{context.ModelAnswer}}
              ======Reference Model Answer=======

              Please analyze the context and output a single valid JSON object.
              """;

        var evaluationResponse =
            await chatConfiguration.ChatClient.GetResponseAsync(
                new List<ChatMessage>
                {
                    new ChatMessage(ChatRole.System, prompt),
                },
                ChatOptions,
                cancellationToken: cancellationToken).ConfigureAwait(false);

        var modelResponseForEvaluationPrompt = evaluationResponse.Text.Trim();

        var result = new EvaluationResult(new NumericMetric(AnswerScoringMetricName));
        var metric = result.Get<NumericMetric>(AnswerScoringMetricName);

        var scoringOutput = JsonConvert.DeserializeObject<ScoringOutput>(modelResponseForEvaluationPrompt)!;

        // Calculate final scores
        var calculator = new ChatEvalScoreCalculator();
        var finalScore = calculator.CalculateFinalScore(scoringOutput.RawScores);

        var finalScoring = finalScore.FinalAdjustedScore;
        metric.Value = finalScoring;
        metric.Interpretation = Convert.ToInt32(Math.Round(finalScoring, 0)) switch
        {
            1 => new EvaluationMetricInterpretation(EvaluationRating.Unacceptable),
            2 => new EvaluationMetricInterpretation(EvaluationRating.Poor),
            3 => new EvaluationMetricInterpretation(EvaluationRating.Average),
            4 => new EvaluationMetricInterpretation(EvaluationRating.Good),
            5 => new EvaluationMetricInterpretation(EvaluationRating.Exceptional),
            _ => new EvaluationMetricInterpretation(EvaluationRating.Inconclusive)
        };
        metric.Diagnostics = new List<EvaluationDiagnostic>
        {
            new (
                EvaluationDiagnosticSeverity.Informational,
                JsonConvert.SerializeObject(scoringOutput))
        };

        return result;
    }
}

public class AnswerScoringEvaluationContext(string scenario, string modelAnswer)
    : EvaluationContext("AnswerScoringEvaluationContext")
{
    public readonly string Scenario = scenario;
    public readonly string ModelAnswer = modelAnswer;
}