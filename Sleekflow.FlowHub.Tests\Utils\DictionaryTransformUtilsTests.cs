using System.Globalization; // Added for CultureInfo
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.FlowHub.Utils;
using static NUnit.Framework.Assert;
using Contains = NUnit.Framework.Contains;

namespace Sleekflow.FlowHub.Tests.Utils
{
    [TestFixture]
    public class DictionaryTransformUtilsTests
    {
        [Test]
        public void MergeAndFilterValues_NullSource_ReturnsEmptyObjectDictionary()
        {
            // Arrange
            IEnumerable<object?>? sourceValues = null;
            var fieldsToInclude = new HashSet<string> { "key1" };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues!, fieldsToInclude);

            // Assert
            That(result, Is.Not.Null);
            That(result, Is.InstanceOf<IDictionary<string, object>>());
            That(result, Is.Empty);
        }

        [Test]
        public void MergeAndFilterValues_EmptySource_ReturnsEmptyObjectDictionary()
        {
            // Arrange
            var sourceValues = Enumerable.Empty<object?>();
            var fieldsToInclude = new HashSet<string> { "key1" };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Is.Not.Null);
            That(result, Is.InstanceOf<IDictionary<string, object>>());
            That(result, Is.Empty);
        }

        [Test]
        public void MergeAndFilterValues_NullFieldsToInclude_ReturnsEmptyObjectDictionary()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                new Dictionary<string, object> { { "key1", "value1" } }
            };
            ICollection<string>? fieldsToInclude = null;

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Is.Not.Null);
            That(result, Is.InstanceOf<IDictionary<string, object>>());
            That(result, Is.Empty);
        }

        [Test]
        public void MergeAndFilterValues_EmptyFieldsToInclude_ReturnsEmptyObjectDictionary()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                new Dictionary<string, object> { { "key1", "value1" } }
            };
            var fieldsToInclude = new HashSet<string>();

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Is.Not.Null);
            That(result, Is.InstanceOf<IDictionary<string, object>>());
            That(result, Is.Empty);
        }

        [Test]
        public void MergeAndFilterValues_IDictionarySource_MergesAndFiltersCorrectly()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                new Dictionary<string, object> { { "id", 1 }, { "name", "Alice" }, { "city", "Wonderland" } },
                new Dictionary<string, object> { { "id", 2 }, { "occupation", "Explorer" }, { "name", "Bob" } }
            };
            var fieldsToInclude = new HashSet<string> { "name", "occupation" };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Has.Count.EqualTo(2));
            That(result["name"], Is.EqualTo("Bob")); // Last one wins for duplicate keys
            That(result["occupation"], Is.EqualTo("Explorer"));
        }

        [Test]
        public void MergeAndFilterValues_JObjectSource_MergesAndFiltersCorrectly()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                JObject.FromObject(new { id = 1, name = "Alice", city = "Wonderland" }),
                JObject.FromObject(new { id = 2, occupation = "Explorer", name = "Bob" })
            };
            var fieldsToInclude = new HashSet<string> { "name", "occupation" };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Has.Count.EqualTo(2));
            That(result["name"], Is.EqualTo("Bob"));
            That(result["occupation"], Is.EqualTo("Explorer"));
        }

        [Test]
        public void MergeAndFilterValues_MixedSource_MergesAndFiltersCorrectly()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                new Dictionary<string, object> { { "id", 1 }, { "name", "Alice" }, { "city", "Wonderland" } },
                JObject.FromObject(new { id = 2, occupation = "Explorer", country = "Oz" }),
                new Dictionary<string, object> { { "name", "Charlie" }, { "occupation", "Dreamer" } }
            };
            var fieldsToInclude = new HashSet<string> { "name", "occupation", "country" };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Has.Count.EqualTo(3));
            That(result["name"], Is.EqualTo("Charlie"));
            That(result["occupation"], Is.EqualTo("Dreamer"));
            That(result["country"], Is.EqualTo("Oz"));
        }

        [Test]
        public void MergeAndFilterValues_SkipsNullValuesInSourceProperties()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                new Dictionary<string, object?>
                    { { "key1", "value1" }, { "key2", null } },
                JObject.FromObject(new { key3 = "value3", key4 = (string?)null })
            };
            var fieldsToInclude = new HashSet<string> { "key1", "key2", "key3", "key4" };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Has.Count.EqualTo(2));
            That(result, Contains.Key("key1"));
            That(result["key1"], Is.EqualTo("value1"));
            That(result, Does.Not.ContainKey("key2")); // key2's value was null
            That(result, Contains.Key("key3"));
            That(result["key3"], Is.EqualTo("value3"));
            That(result, Does.Not.ContainKey("key4")); // key4's value was null
        }

        [Test]
        public void MergeAndFilterValues_SkipsNullItemsInSourceValuesList()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                new Dictionary<string, object> { { "key1", "value1" } },
                null, // Null item in the list
                JObject.FromObject(new { key2 = "value2" })
            };
            var fieldsToInclude = new HashSet<string> { "key1", "key2" };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Has.Count.EqualTo(2));
            That(result["key1"], Is.EqualTo("value1"));
            That(result["key2"], Is.EqualTo("value2"));
        }

        [Test]
        public void MergeAndFilterValues_OnlyIncludesSpecifiedFields()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                new Dictionary<string, object> { { "include_me", "yes" }, { "exclude_me", "no" } },
                JObject.FromObject(new { also_include = "indeed", also_exclude = "nope" })
            };
            var fieldsToInclude = new HashSet<string> { "include_me", "also_include" };

            // Act
            var resultObject = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(resultObject, Is.Not.Null);
            That(resultObject, Is.InstanceOf<IDictionary<string, object>>());
            That(resultObject, Has.Count.EqualTo(2));
            That(resultObject, Contains.Key("include_me"));
            That(resultObject, Contains.Key("also_include"));
            That(resultObject, Does.Not.ContainKey("exclude_me"));
            That(resultObject, Does.Not.ContainKey("also_exclude"));
        }

        [Test]
        public void MergeAndFilterValues_UserProvidedExample_MergesAndFiltersCorrectly()
        {
            // Arrange
            var sourceValues = new List<object?>
            {
                JObject.FromObject(new
                {
                    confidence_score = 80,
                    recommended_reply = "hello, welcome!"
                }),
                JObject.FromObject(new
                {
                    category = "HIGH_CONFIDENCE",
                    score = 95,
                    reason = "warm welcome to user"
                })
            };

            var fieldsToInclude = new HashSet<string>
            {
                "category", "score", "reason", "confidence_score"
            };

            // Act
            var resultObject = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);
            Console.WriteLine(JsonConvert.SerializeObject(sourceValues));
            Console.WriteLine(JsonConvert.SerializeObject(resultObject));

            // Assert
            That(resultObject, Is.Not.Null);
            That(resultObject, Is.InstanceOf<IDictionary<string, object>>());
            That(resultObject, Has.Count.EqualTo(4), "Dictionary should contain 4 items.");

            That(resultObject, Contains.Key("confidence_score"));
            That(resultObject["confidence_score"], Is.EqualTo(80L), "Checking confidence_score. Note: JObject parsing might result in long for numbers.");

            That(resultObject, Contains.Key("category"));
            That(resultObject["category"], Is.EqualTo("HIGH_CONFIDENCE"), "Checking category.");

            That(resultObject, Contains.Key("score"));
            That(resultObject["score"], Is.EqualTo(95L), "Checking score.");

            That(resultObject, Contains.Key("reason"));
            That(resultObject["reason"], Is.EqualTo("warm welcome to user"), "Checking reason.");

            That(resultObject, Does.Not.ContainKey("recommended_reply"), "'recommended_reply' should be excluded.");
        }

        [Test]
        public void MergeAndFilterValues_ListOfJsonStrings_MergesAndFiltersCorrectly()
        {
            // Arrange
            var jsonInput = """
                            [
                                "{\"completed_timestamp\":\"2025-05-09T06:04:13.572Z\"}",
                                "{\"category\":\"Low\",\"score\":10,\"reason\":\"The conversation lacks clarity and intent, with random and incoherent messages from the user.\"}"
                            ]
                            """;
            // The method expects IEnumerable<object?>, so we deserialize to List<object?>
            // where each object is a string that needs to be parsed as JSON by the method.
            var sourceValues = JsonConvert.DeserializeObject<List<object?>>(jsonInput);

            var fieldsToInclude = new HashSet<string>
            {
                "completed_timestamp", "category", "score", "reason", "does_not_exist"
            };

            // Act
            var result = DictionaryTransformUtils.MergeAndFilterValues(sourceValues, fieldsToInclude);

            // Assert
            That(result, Is.Not.Null);
            That(result, Is.InstanceOf<IDictionary<string, object>>());
            That(result, Has.Count.EqualTo(4));

            That(result, Contains.Key("completed_timestamp"));
            That(result["completed_timestamp"], Is.EqualTo(DateTimeOffset.Parse("2025-05-09T06:04:13.572Z", CultureInfo.InvariantCulture).UtcDateTime));

            That(result, Contains.Key("category"));
            That(result["category"], Is.EqualTo("Low"));

            That(result, Contains.Key("score"));
            That(result["score"], Is.EqualTo(10L)); // JObject parsing results in long for numbers

            That(result, Contains.Key("reason"));
            That(result["reason"], Is.EqualTo("The conversation lacks clarity and intent, with random and incoherent messages from the user."));
        }
    }
}