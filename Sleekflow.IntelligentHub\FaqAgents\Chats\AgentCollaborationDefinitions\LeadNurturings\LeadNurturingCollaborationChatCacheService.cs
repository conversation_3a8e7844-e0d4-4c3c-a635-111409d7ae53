using Newtonsoft.Json;
using Sleekflow.Caches;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;
using StackExchange.Redis;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats.AgentCollaborationDefinitions.LeadNurturings;

public interface ILeadNurturingCollaborationChatCacheService
{
    public Task<Dictionary<string, string>> GetAllConfirmedKnowledgesAsync(string groupChatIdStr);

    public Task AppendConfirmedKnowledgeAsync(string groupChatIdStr, string needKnowledge, string confirmedKnowledge);

    public Task<string?> GetLastResponseAgentReplyAsync(string groupChatIdStr);

    public Task SetLastResponseAgentReplyAsync(string groupChatIdStr, string reply);
}

public class LeadNurturingCollaborationChatCacheService : ILeadNurturingCollaborationChatCacheService, IScopedService
{
    private readonly ILogger<LeadNurturingCollaborationChatCacheService> _logger;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ICacheConfig _cacheConfig;

    private static readonly TimeSpan CacheExpiration = TimeSpan.FromMinutes(30);

    public LeadNurturingCollaborationChatCacheService(
        ILogger<LeadNurturingCollaborationChatCacheService> logger,
        IConnectionMultiplexer connectionMultiplexer,
        ICacheConfig cacheConfig)
    {
        _logger = logger;
        _connectionMultiplexer = connectionMultiplexer;
        _cacheConfig = cacheConfig;
    }

    public Task<Dictionary<string, string>> GetAllConfirmedKnowledgesAsync(string groupChatIdStr)
    {
        var database = _connectionMultiplexer.GetDatabase();

        return database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord =
                        JsonConvert.DeserializeObject<LeadNurturingCollaborationChatCacheRecord>(
                            cachedValue!,
                            JsonConfig.DefaultJsonSerializerSettings);

                    return groupChatCacheRecord!.AllConfirmedKnowledgeRecords;
                }

                return new Dictionary<string, string>();
            });
    }

    public async Task AppendConfirmedKnowledgeAsync(
        string groupChatIdStr,
        string needKnowledge,
        string confirmedKnowledge)
    {
        if (string.IsNullOrWhiteSpace(confirmedKnowledge))
        {
            // Some cases the confirmed knowledge is empty, we don't need to cache it
            return;
        }

        var database = _connectionMultiplexer.GetDatabase();

        await database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord =
                        JsonConvert.DeserializeObject<LeadNurturingCollaborationChatCacheRecord>(
                            cachedValue!,
                            JsonConfig.DefaultJsonSerializerSettings)!;

                    groupChatCacheRecord.AllConfirmedKnowledgeRecords[needKnowledge] = confirmedKnowledge;

                    database.StringSetAsync(
                        GetCacheKey(groupChatIdStr),
                        JsonConvert.SerializeObject(groupChatCacheRecord, JsonConfig.DefaultJsonSerializerSettings),
                        CacheExpiration);

                    return Task.CompletedTask;
                }

                return database.StringSetAsync(
                    GetCacheKey(groupChatIdStr),
                    JsonConvert.SerializeObject(
                        new LeadNurturingCollaborationChatCacheRecord(
                            groupChatIdStr,
                            new Dictionary<string, string>()
                            {
                                {
                                    needKnowledge, confirmedKnowledge
                                }
                            }),
                        JsonConfig.DefaultJsonSerializerSettings),
                    CacheExpiration);
            });
    }

    public Task<string?> GetLastResponseAgentReplyAsync(string groupChatIdStr)
    {
        var database = _connectionMultiplexer.GetDatabase();

        return database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord =
                        JsonConvert.DeserializeObject<LeadNurturingCollaborationChatCacheRecord>(
                            cachedValue!,
                            JsonConfig.DefaultJsonSerializerSettings);

                    return groupChatCacheRecord!.LastResponseAgentReply;
                }

                return null;
            });
    }

    public async Task SetLastResponseAgentReplyAsync(string groupChatIdStr, string reply)
    {
        var database = _connectionMultiplexer.GetDatabase();

        await database
            .StringGetAsync(GetCacheKey(groupChatIdStr))
            .ContinueWith(task =>
            {
                var cachedValue = task.Result;
                if (cachedValue.HasValue)
                {
                    var groupChatCacheRecord =
                        JsonConvert.DeserializeObject<LeadNurturingCollaborationChatCacheRecord>(
                            cachedValue!,
                            JsonConfig.DefaultJsonSerializerSettings)!;

                    groupChatCacheRecord.LastResponseAgentReply = reply;

                    database.StringSetAsync(
                        GetCacheKey(groupChatIdStr),
                        JsonConvert.SerializeObject(groupChatCacheRecord, JsonConfig.DefaultJsonSerializerSettings),
                        CacheExpiration);

                    return Task.CompletedTask;
                }

                return database.StringSetAsync(
                    GetCacheKey(groupChatIdStr),
                    JsonConvert.SerializeObject(
                        new LeadNurturingCollaborationChatCacheRecord(
                            groupChatIdStr,
                            null,
                            reply),
                        JsonConfig.DefaultJsonSerializerSettings),
                    CacheExpiration);
            });
    }

    private string GetCacheKey(string groupChatIdStr)
    {
        return $"{_cacheConfig.CachePrefix}:group-chat:{groupChatIdStr}";
    }
}