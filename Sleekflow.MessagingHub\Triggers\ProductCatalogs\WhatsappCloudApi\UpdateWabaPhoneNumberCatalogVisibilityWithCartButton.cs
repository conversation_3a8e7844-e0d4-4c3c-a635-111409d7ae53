using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.Models.WhatsappCloudApis.Wabas;
using Sleekflow.MessagingHub.WhatsappCloudApis.ProductCatalogs;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.MessagingHub.Triggers.ProductCatalogs.WhatsappCloudApi;

[TriggerGroup(ControllerNames.ProductCatalogs)]
public class UpdateWabaPhoneNumberCatalogVisibilityWithCartButton
    : ITrigger<
        UpdateWabaPhoneNumberCatalogVisibilityWithCartButton.UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonInput,
        UpdateWabaPhoneNumberCatalogVisibilityWithCartButton.UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutput>
{
    private readonly ILogger<UpdateWabaPhoneNumberCatalogVisibilityWithCartButton> _logger;
    private readonly IProductCatalogService _productCatalogService;

    public UpdateWabaPhoneNumberCatalogVisibilityWithCartButton(
        ILogger<UpdateWabaPhoneNumberCatalogVisibilityWithCartButton> logger,
        IProductCatalogService productCatalogService)
    {
        _logger = logger;
        _productCatalogService = productCatalogService;
    }

    public class UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonInput : IHasSleekflowStaff, IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("waba_id")]
        public string WabaId { get; set; }

        [Required]
        [JsonProperty("phone_number_id")]
        public string PhoneNumberId { get; set; }

        [Required]
        [JsonProperty("is_catalog_visible")]
        public bool IsCatalogVisible { get; set; }

        [Required]
        [JsonProperty("is_cart_enabled")]
        public bool IsCartEnabled { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string? SleekflowStaffId { get; set; }

        [Validations.ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonInput(
            string sleekflowCompanyId,
            string wabaId,
            string phoneNumberId,
            bool isCatalogVisible,
            bool isCartEnabled,
            string? sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            WabaId = wabaId;
            PhoneNumberId = phoneNumberId;
            IsCatalogVisible = isCatalogVisible;
            IsCartEnabled = isCartEnabled;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutput
    {
        [JsonProperty("waba_phone_number")]
        public WabaPhoneNumberDto? WabaPhoneNumber { get; set; }

        [JsonConstructor]
        public UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutput(WabaPhoneNumberDto? wabaPhoneNumber)
        {
            WabaPhoneNumber = wabaPhoneNumber;
        }
    }

    public async Task<UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutput> F(
        UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonInput
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput)
    {
        var sleekflowStaff = AuditEntity.ConstructSleekflowStaff(
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput.SleekflowStaffId,
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput.SleekflowStaffTeamIds);

        var wabaPhoneNumber = await _productCatalogService.SetWabaPhoneNumberProductCatalogVisibleEnableCartAsync(
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput.SleekflowCompanyId,
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput.WabaId,
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput.PhoneNumberId,
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput.IsCatalogVisible,
            updateWabaPhoneNumberCatalogVisibilityWithCartButtonInput.IsCartEnabled,
            sleekflowStaff);

        return new UpdateWabaPhoneNumberCatalogVisibilityWithCartButtonOutput(new WabaPhoneNumberDto(wabaPhoneNumber));
    }
}