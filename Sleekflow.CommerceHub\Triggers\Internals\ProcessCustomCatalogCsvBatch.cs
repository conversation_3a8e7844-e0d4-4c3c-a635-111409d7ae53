using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CommerceHub.CustomCatalogs;
using Sleekflow.CommerceHub.Models.Constants;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Triggers.Internals;

[TriggerGroup(ControllerNames.Internals)]
public class ProcessCustomCatalogCsvBatch
    : ITrigger<
        ProcessCustomCatalogCsvBatch.ProcessCustomCatalogCsvBatchInput,
        ProcessCustomCatalogCsvBatch.ProcessCustomCatalogCsvBatchOutput>
{
    private readonly ICustomCatalogFileService _customCatalogFileService;

    public ProcessCustomCatalogCsvBatch(ICustomCatalogFileService customCatalogFileService)
    {
        _customCatalogFileService = customCatalogFileService;
    }

    public class ProcessCustomCatalogCsvBatchInput
    {
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("custom_catalog_file_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string CustomCatalogFileId { get; set; }

        [JsonProperty("my_custom_catalog_csv_reader_state")]
        public MyCustomCatalogCsvReaderState? MyCustomCatalogCsvReaderState { get; set; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvBatchInput(
            string sleekflowCompanyId,
            string customCatalogFileId,
            MyCustomCatalogCsvReaderState? myCustomCatalogCsvReaderState)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            CustomCatalogFileId = customCatalogFileId;
            MyCustomCatalogCsvReaderState = myCustomCatalogCsvReaderState;
        }
    }

    public class ProcessCustomCatalogCsvBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("my_custom_catalog_csv_reader_state")]
        public MyCustomCatalogCsvReaderState? MyCustomCatalogCsvReaderState { get; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvBatchOutput(
            long count,
            MyCustomCatalogCsvReaderState? myCustomCatalogCsvReaderState)
        {
            Count = count;
            MyCustomCatalogCsvReaderState = myCustomCatalogCsvReaderState;
        }
    }

    public async Task<ProcessCustomCatalogCsvBatchOutput> F(
        ProcessCustomCatalogCsvBatchInput processCustomCatalogCsvBatchInput)
    {
        var (count, myCsvReaderState) = await _customCatalogFileService.ProcessCustomCatalogCsvBatchAsync(
            processCustomCatalogCsvBatchInput.CustomCatalogFileId,
            processCustomCatalogCsvBatchInput.SleekflowCompanyId,
            processCustomCatalogCsvBatchInput.MyCustomCatalogCsvReaderState);

        return new ProcessCustomCatalogCsvBatchOutput(count, myCsvReaderState);
    }
}