﻿using Newtonsoft.Json;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.Webhooks;

[<PERSON><PERSON><PERSON>(typeof(IDbContainerResolver))]
[DatabaseId("db")]
[ContainerId("webhook")]
public class Webhook : Entity
{
    public const string PropertyNameUrl = "url";

    public const string PropertyNameSecondaryUrl = "secondary_url";

    [JsonProperty("sleekflow_company_id")]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("entity_type_name")]
    public string EntityTypeName { get; set; }

    [JsonProperty("event_type_name")]
    public string EventTypeName { get; set; }

    [JsonProperty("max_retry_count")]
    public int? MaxRetryCount { get; set; }

    [JsonProperty(PropertyNameUrl)]
    public string Url { get; set; }

    [JsonProperty(PropertyNameSecondaryUrl)]
    public string? SecondaryUrl { get; set; }

    [JsonProperty("context")]
    public Dictionary<string, object?> Context { get; set; }

    [JsonConstructor]
    public Webhook(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        int? maxRetryCount,
        string url,
        string? secondaryUrl,
        string eventTypeName,
        Dictionary<string, object?> context)
        : base(id, "Webhook")
    {
        SleekflowCompanyId = sleekflowCompanyId;
        EntityTypeName = entityTypeName;
        MaxRetryCount = maxRetryCount;
        Url = url;
        SecondaryUrl = secondaryUrl;
        EventTypeName = eventTypeName;
        Context = context;
    }

    public Webhook(
        string id,
        string sleekflowCompanyId,
        string entityTypeName,
        string url,
        string eventTypeName,
        Dictionary<string, object?> context)
        : this(
            id,
            sleekflowCompanyId,
            entityTypeName,
            null,
            url,
            null,
            eventTypeName,
            context)
    {
    }
}