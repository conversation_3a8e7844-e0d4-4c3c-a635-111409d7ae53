using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.ChatCompletion;
using Sleekflow.IntelligentHub.Evaluator.ChatEvals;

namespace Sleekflow.IntelligentHub.Evaluator;

public static class BeameTestCases
{
    public static IEnumerable<ChatEvalQuestion> GetBeameTestCases()
    {
        var testConfig = new ChatEvalConfig(
            SourceDir: "../../../KnowledgeSources/Beame/",
            SleekflowCompanyId: "cddf6a22-0751-47ff-94c8-4d78c17b37b1");

        var implantDocuments = new List<string>
        {
            "https%3a%2f%2fmybeame.com%2faboutimplant.md", "https%3a%2f%2fmybeame.com%2fimplant.md"
        };

        var wisdomToothDocuments = new List<string>
        {
            "https%3a%2f%2fmybeame.com%2fwisdomtooth.md"
        };

        var veneerDocuments = new List<string>
        {
            "https%3a%2f%2fmybeame.com%2faboutveneer.md", "https%3a%2f%2fmybeame.com%2fveneer.md"
        };

        var childrenDentistryDocuments = new List<string>
        {
            "https%3a%2f%2fmybeame.com%2fchildren.md", "https%3a%2f%2fmybeame.com%2fchildren-dentistry.md"
        };

        var aligenerDocuments =
            new List<string>
            {
                "https%3a%2f%2fmybeame.com%2f.md",
                "https%3a%2f%2fmybeame.com%2f2nd-aligner.md",
                "https%3a%2f%2fmybeame.com%2fabout.md",
                "https%3a%2f%2fmybeame.com%2fabout-beame.md",
                "https%3a%2f%2fmybeame.com%2faboutdental.md",
                "https%3a%2f%2fmybeame.com%2faligner.md",
                "https%3a%2f%2fmybeame.com%2faligner-flow.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2f2nd-braces-101.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2finvisible-braces-price.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2fsz-double-11-discount.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2fthe-best-dating-app-pro-pic.md",
                "https%3a%2f%2fmybeame.com%2finvisible.md",
            };

        var retainerDocuments =
            new List<string>
            {
                "https%3a%2f%2fmybeame.com%2fretainer.md",
            };

        var studentDiscountDocuments =
            new List<string>
            {
                "https%3a%2f%2fmybeame.com%2fbeame-student-discount.md",
            };

        var customerSupportDocuments =
            new List<string>
            {
                "https%3a%2f%2fmybeame.com%2ffeedback.md",
            };

        var discountDocuments =
            new List<string>
            {
                "https%3a%2f%2fmybeame.com%2fpromotion.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2fthe-best-dating-app-pro-pic.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2fsz-double-11-discount.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2finvisible-braces-price.md",
                "https%3a%2f%2fmybeame.com%2fblogdetail%2f2nd-braces-101.md",
            };

        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對箍牙療程的價格和優惠表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "請問隱形牙套矯正的價錢大概是多少？想先了解一個大概的範圍。"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "隱形牙套矯正的價格範圍較廣，會因應牙齒的複雜程度和所需的牙套數量而有所不同。一般來說，價格可能從幾萬到十幾萬不等。最準確的估價還是需要由牙醫檢查後才能確定。"),
                new ChatMessageContent(AuthorRole.User, "那如果我對價錢不滿意，可以隨時取消嗎？"),
            ],
            "在您接受療程前，我們會詳細說明所有費用，確保您清楚了解。如果您在初步評估後決定不進行療程，可以取消，無需支付任何費用。",
            SourceFilenames: aligenerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "學生想了解透明牙箍的學生折扣優惠",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我是一名學生，想了解一下你們的透明牙箍是否有學生折扣？"),
            ],
            "您好！是的，Be@me 為學生提供特別的透明牙箍折扣。學生可以節省高達 $3,820，並且還可享受免費的超聲波洗牙服務。這些優惠適用於所有年齡段的學生，只需出示有效的學生證或手冊即可享受此優惠。",
            SourceFilenames: studentDiscountDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對Beame隱形牙箍的推薦計劃感興趣",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說Beame有一個推薦朋友的計劃，可以詳細介紹一下嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，我們目前有「隱形牙箍推薦計劃」。只要您推薦朋友使用Beame隱形牙箍，您和您的朋友都可以獲得價值$300的Apple禮品卡，總共是$600的獎勵。這個活動從2024年9月9日開始。"),
                new ChatMessageContent(AuthorRole.User, "那麼，我需要怎麼做才能參與這個推薦計劃？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "首先，您需要讓您的朋友在網上預約Beame門市的箍牙諮詢，預約連結是[立即預約 be@me 箍牙](https://mybeame.com/aligner)。在預約時，請提醒他們選擇「親友介紹」服務類別，並在備註欄填寫您的姓名。然後，他們需要按照預約時間到門市完成3D口腔掃描和諮詢。最重要的是，在諮詢時提供您的姓名和電話，並說明使用推薦計劃，這樣就可以鎖定Apple禮品卡了。"),
                new ChatMessageContent(AuthorRole.User, "如果我的朋友最終沒有選擇Beame的箍牙方案，我們還能獲得禮品卡嗎？")
            ],
            "只有在您和您的朋友都確認參與Beame箍牙方案後，雙方才能獲得$300的Apple禮品卡。禮品卡將在您們領取牙套時一同發放。您也可以預約到指定門市領取禮品卡。",
            SourceFilenames: aligenerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶擔心美容療程時間超出預期，並詢問是否需要支付額外維護費用",
            [
                new ChatMessageContent(AuthorRole.User, "我聽說有時候美容療程會比預期的時間長，如果這樣，我需要支付額外的費用嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "療程時間的確可能因個人情況而異。例如，如果在清潔過程中發現牙周問題，可能需要額外的時間。為了確保服務質量，我們的諮詢都是一對一進行的，如果您有很多問題或遲到，可能會導致延誤[。"),
                new ChatMessageContent(AuthorRole.User, "如果療程後效果不滿意，我可以獲得免費的維護嗎？"),
            ],
            "在大多數情況下，牙醫或助理會在服務完成後與您一起檢查結果。如果出現問題，例如填充物在手術後不久脫落，您可以聯繫我們的客戶服務部門安排複診進行修正。您可以使用售後WhatsApp鏈接聯繫客戶服務部門。",
            SourceFilenames: customerSupportDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問Be@me在哪些方面比隱*美更具優勢",
            [
                new ChatMessageContent(AuthorRole.User, "你提到Be@me在技術和服務上比隱*美有優勢，可以更具體地說明嗎？隱*美到底欠缺了什麼？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "當然可以！隱*美作為隱形牙套的領導品牌，在市場上擁有較高的知名度。但Be@me在幾個關鍵方面更勝一籌。首先，價格方面，Be@me更具競爭力。"),
                new ChatMessageContent(AuthorRole.User, "除了價格，還有其他方面的優勢嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的。Be@me提供全天候客戶服務和矯正進度追蹤，這在隱*美那裡是沒有的。我們的矯正時間通常也更短，一般為6-12個月，而隱*美可能需要2年。此外，Be@me採用高科技智能3D取模和AI模擬矯正成效，能夠為您定制更精準高效的矯齒方案。"),
                new ChatMessageContent(AuthorRole.User, "隱*美在材料和技術上不是也有國際認證嗎？Be@me的特別之處在哪裡？")
            ],
            "雖然隱*美也使用國際認證材料，但Be@me採用的是通過TUV nord 南德認證的德國進口創新安全材質。更重要的是，Be@me的牙醫會為您設計專屬牙箍，並根據您的個人需求提供靈活的牙醫面見選擇。此外，Be@me在療程中採用微創技術，減少對口腔的影響，提供10年保養。總體來說，Be@me在價格、服務、技術和材料上都力求做到更好，為您提供更優質的矯正體驗。",
            SourceFilenames: ["https%3a%2f%2fmybeame.com%2f.md"]);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對固定器和牙套的區別以及價格表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "請問固定器和牙套有什麼區別？我剛做完牙齒矯正，需要戴哪一種？"),
            ],
            "您好！牙套（隱形牙套）主要用於矯正牙齒，而固定器則是在牙齒矯正後使用，用來維持牙齒在新的位置，防止牙齒回彈，確保矯正效果的穩定性。恭喜您完成了牙齒矯正！現在建議您佩戴固定器以保持牙齒的整齊哦。",
            SourceFilenames: aligenerDocuments.Concat(retainerDocuments).ToList());
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問牙齒走位後是否有保障服務",
            [
                new ChatMessageContent(AuthorRole.User, "如果我戴了固定器，但牙齒還是走位了，有什麼保障嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "別擔心，我們提供be@me Care+服務，如果您的牙齒在使用固定器後仍然出現走位情況，我們會提供免費重箍服務，確保您的牙齒始終保持在最佳狀態，可以說是終身笑容保養哦！"
                ),
                new ChatMessageContent(AuthorRole.User, "聽起來不錯！我怎樣預約固定器諮詢呢？"),
            ],
            "您可以通過以下步驟預約免費固定器諮詢：\n1. 訪問我們的網站，預約免費固定器諮詢及體驗。\n2. 選擇合適的諮詢時間及日期。\n3. 填寫您的個人資料（姓名、性別、服務類別、電話號碼、電郵等）。\n我們在香港有多家分店，您可以選擇離您最近的一家進行諮詢。",
            SourceFilenames: retainerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對隱形牙箍的流程和價格表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "你們的隱形牙箍療程是怎樣的？每一步驟的費用是多少？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！我們的隱形牙箍服務包含幾個主要步驟：首先是免費的 be@me 諮詢，接著是 3D 牙齒掃描。在您決定開始後，需要支付訂金或全額費用以鎖定價格。我們的牙科團隊會根據掃描數據設計專屬方案供您審閱。然後您就可以在門市領取牙套，我們會專人教授使用方法。最後就是佩戴牙套，我們會持續跟進您的矯正進度。"),
                new ChatMessageContent(AuthorRole.User, "3D 牙齒掃描需要額外收費嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "3D 牙齒掃描是免費的。您可以通過這個掃描了解口腔實時狀況，並模擬矯正效果。"),
                new ChatMessageContent(AuthorRole.User, "如果我需要額外約見牙醫，費用是怎樣計算的？")
            ],
            "如果您需要額外約見香港合作註冊牙醫或自有光門診牙醫，這部分的費用會根據具體情況而定，需要額外付費。建議您在預約時與我們的團隊確認具體情況。",
            SourceFilenames: aligenerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對隱形牙套的療程流程表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "我想了解一下隱形牙套的療程流程是怎樣的？需要多少時間？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "隱形牙套的療程通常包括五個步驟：首先，您可以通過電話或電腦輕鬆預約免費的線上諮詢。接下來，您需要到店進行免費諮詢和3D掃描，以獲得個人牙齒模型。然後，牙醫會根據您的牙齒狀況設計治療計劃。接著，您可以在選定的商店領取牙套，並獲得佩戴和護理的指導。最後，您可以在線上追蹤進度，並獲得持續的支持。"),
                new ChatMessageContent(AuthorRole.User, "這整個過程需要多長時間呢？"),
            ],
            "整個隱形牙套的療程時間因人而異，但通常治療期間為6至12個月，具體取決於您的牙齒狀況和所選擇的方案。",
            SourceFilenames: aligenerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶想知道Beame有冇顎骨矯正手術",
            [
                new ChatMessageContent(AuthorRole.User, "唔該，想問下你哋有冇顎骨矯正手術啊？"),
            ],
            "我哋主要提供隱形牙套矯正服務，針對牙齒唔齊、有牙縫等等問題。如果你想改善顎骨嘅問題，可能要諮詢其他專業嘅正顎手術服務。",
            SourceFilenames: aligenerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對諮詢多位牙醫以獲得第二意見的問題表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "我可以諮詢多位牙醫嚟攞第二意見嗎？咁樣會唔會額外收費？"),
            ],
            "當然可以！你可以隨時去諮詢多位牙醫，攞到唔同嘅意見。至於費用方面，Beame提供嘅初步諮詢係免費嘅，所以你唔需要擔心額外收費。",
            SourceFilenames: aligenerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "想問下香港同深圳嘅 Beame 牙科服務有冇唔同？",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想問下你哋香港同深圳嘅 Beame 服務有冇唔同㗎？"),
            ],
            "香港嘅 Beame 門市主要提供箍牙諮詢同體驗，唔提供醫療服務。深圳嘅 Beame 診所就有齊全面嘅牙科服務，例如牙貼面、激光美白、植牙同洗牙等等。兩邊嘅門市都可以預約免費嘅箍牙諮詢同體驗。",
            SourceFilenames: aligenerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問新客戶是否有優惠或折扣",
            [
                new ChatMessageContent(AuthorRole.User, "請問新客戶有冇咩優惠或者折扣？"),
            ],
            "新客戶可以享受自有光提供的迎新優惠，透過 WhatsApp 聯絡客服，發送指定訊息，即可獲得總值 $300 的牙科醫療消費券！只需在預約服務時出示這個消費券，就可以享受優惠。",
            SourceFilenames: discountDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問是否可以獲得箍牙優惠",
            [
                new ChatMessageContent(AuthorRole.User, "我同女朋友想一齊箍牙，請問有冇優惠？"),
            ],
            "如果你同你女朋友一齊買透明牙箍，網上預約即減高達$3820。仲有，如果你哋選藍光美白，二人同行每人只需付¥880，總共可以慳到¥1240！",
            SourceFilenames: discountDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對箍牙療程的價格和優惠表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "我同女朋友想一齊箍牙，請問有冇優惠？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "如果你同你女朋友一齊買透明牙箍，網上預約即減高達$3820。仲有，如果你哋選藍光美白，二人同行每人只需付¥880，總共可以慳到¥1240！"
                ),
                new ChatMessageContent(AuthorRole.User, "點解你提到藍光美白？我其實係想箍牙。"),
            ],
            "抱歉，我誤會咗你嘅意思。對於箍牙，透明牙箍有優惠，網上預約可以減高達$3820，而如果你有其他問題或者需要了解更多優惠，都可以隨時問我！",
            SourceFilenames: discountDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶對長者優惠表示疑問",
            [
                new ChatMessageContent(AuthorRole.User, "請問有冇長者優惠？"),
            ],
            "自有光為長者提供精選4大優惠，應付常見的口腔需求，如牙周治療和植牙。你可以參考這個連結了解更多詳情：[https://mybeame.com/blogdetail/sz-elderly-diseases](https://mybeame.com/blogdetail/sz-elderly-diseases)。",
            SourceFilenames: discountDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問植牙服務和費用",
            [
                new ChatMessageContent(AuthorRole.User, "你好，我想了解一下植牙的服務，大概需要多少費用？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "您好！植牙費用會根據您的具體情況而定。我們會先進行口腔檢查和3D掃描，評估您的牙齦和骨質狀況。一般來說，單顆牙齒的植牙費用在¥15,000-20,000之間。您想預約免費諮詢嗎？"),
                new ChatMessageContent(AuthorRole.User, "植牙的過程會很痛嗎？需要多久才能恢復？"),
            ],
            "植牙手術會使用局部麻醉，所以過程中不會感到疼痛。手術後可能會有些許不適，但通常3-5天就能恢復正常生活。整個植牙過程包括植入、骨整合和安裝牙冠，一般需要3-6個月完成。我們的醫生會根據您的情況制定詳細的治療計劃，並全程提供專業指導。",
            SourceFilenames: implantDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問智齒問題",
            [
                new ChatMessageContent(AuthorRole.User, "我的智齒一直很痛，想問下拔智齒的流程是怎樣的？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "我理解您的困擾。拔智齒前，我們會先進行X光檢查，評估智齒的位置和生長情況。根據智齒的複雜程度，可能需要手術或一般拔牙。您最近是否有發炎或腫痛的情況？"),
                new ChatMessageContent(AuthorRole.User, "有點腫，而且吃東西時會痛。拔完智齒後要休息多久？"),
            ],
            "拔智齒後建議休息2-3天，避免劇烈運動和吸煙。術後24小時內要用冰敷減少腫脹，不要漱口或吐口水。3-4天後可以恢復正常飲食，但要注意保持口腔清潔。如果您想了解更多細節或預約諮詢，我很樂意為您安排。",
            SourceFilenames: wisdomToothDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "客戶詢問牙貼面服務",
            [
                new ChatMessageContent(AuthorRole.User, "我對牙齒的顏色和形狀不太滿意，聽說牙貼面可以改善，是真的嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，牙貼面是一種很好的美學修復方案，可以改善牙齒的顏色、形狀和排列。我們使用高品質的瓷貼面，能夠提供自然美觀的效果。您具體想改善哪些方面呢？"),
                new ChatMessageContent(AuthorRole.User, "主要是想讓牙齒更白更整齊。做牙貼面會不會傷害原本的牙齒？"),
            ],
            "牙貼面的製作需要磨除極少量的牙齒表面（約0.3-0.5毫米），對牙齒的傷害非常小。我們採用數字化設計，確保最小程度的牙齒預備。瓷貼面不僅美觀，還具有很好的強度和耐用性，可以維持10-15年。如果您感興趣，可以預約免費諮詢，我們的醫生會為您進行專業評估。",
            SourceFilenames: veneerDocuments);
        yield return new ChatEvalQuestion(
            testConfig,
            "家長詢問兒童牙科服務",
            [
                new ChatMessageContent(AuthorRole.User, "我的小孩今年5歲，想帶他做口腔檢查，你們有兒童牙科服務嗎？"),
                new ChatMessageContent(
                    AuthorRole.Assistant,
                    "是的，我們有專門的兒童牙科服務。我們的醫生經驗豐富，會用溫和的方式為小朋友進行檢查和治療。首次檢查會包括口腔健康評估、蛀牙檢查和口腔衛生指導。您的小朋友之前有看過牙醫嗎？"),
                new ChatMessageContent(AuthorRole.User, "沒有，這是第一次。他有點怕看牙醫，你們有什麼方法可以讓他不那麼害怕嗎？"),
            ],
            "請放心，我們的兒童牙科診療室特別設計得溫馨有趣，醫生會用遊戲的方式讓小朋友放鬆。我們也會教導小朋友正確的刷牙方法，並提供兒童專用的口腔護理產品。建議從小培養定期檢查的習慣，預防勝於治療。您想預約一個適合的時間嗎？",
            SourceFilenames: childrenDentistryDocuments);
    }
}