using Microsoft.Azure.Functions.Worker;
using Microsoft.DurableTask;
using Microsoft.Extensions.Logging;
using Sleekflow.Extensions;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.StepExecutions;
using Sleekflow.FlowHub.Models.Workers;

namespace Sleekflow.FlowHub.Workers.Triggers.Orchestrators;

public class ExecuteParallelStepOrchestrator
{
    private readonly ILogger<ExecuteParallelStepOrchestrator> _logger;

    public ExecuteParallelStepOrchestrator(
        ILogger<ExecuteParallelStepOrchestrator> logger)
    {
        _logger = logger;
    }

    [Function("ExecuteParallelStep_Orchestrator")]
    public async Task RunAsync(
        [OrchestrationTrigger]
        TaskOrchestrationContext context)
    {
        var executeParallelStepInput = context.GetInput<ExecuteParallelStepInput>();

        try
        {
            await ExecuteParallelStepAsync(context, executeParallelStepInput);
        }
        catch (Exception e)
        {
            _logger.LogError(
                e,
                "Error executing ParallelSteps {ParallelSteps}",
                executeParallelStepInput.StepId);

            // Fails during ParallelSteps
            await context.CallActivityAsync(
                "CompleteStep",
                new CompleteStepInput(
                    executeParallelStepInput.StateId,
                    executeParallelStepInput.StepId,
                    executeParallelStepInput.StackEntries,
                    StepExecutionStatuses.Failed));
        }
    }

    private async Task ExecuteParallelStepAsync(
        TaskOrchestrationContext context,
        ExecuteParallelStepInput executeParallelStepInput)
    {
        var parallelTasks = new List<Task>();

        foreach (var stepId in executeParallelStepInput.ParallelStepIds)
        {
            parallelTasks.Add(
                context.CallSubOrchestratorAsync(
                    "SubmitStep_Orchestrator",
                    new SubmitStepOrchestrator.SubmitStepOrchestratorInput(
                        executeParallelStepInput.StateId,
                        stepId,
                        executeParallelStepInput.StackEntries)));
        }

        await parallelTasks.WhenAllFailFast();

        _logger.LogInformation(
            "All parallel tasks completed for state {StateId} step {StepId}, stack entries: {@StackEntries}",
            executeParallelStepInput.StateId,
            executeParallelStepInput.StepId,
            executeParallelStepInput.StackEntries);

        await context.CallActivityAsync(
            "CompleteStep",
            new CompleteStepInput(
                executeParallelStepInput.StateId,
                executeParallelStepInput.StepId,
                executeParallelStepInput.StackEntries,
                StepExecutionStatuses.Complete));
    }
}