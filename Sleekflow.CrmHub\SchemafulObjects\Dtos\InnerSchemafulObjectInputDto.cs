﻿using Newtonsoft.Json;
using Sleekflow.Attributes;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.SchemafulObjects.Dtos;

[SwaggerInclude]
public class InnerSchemafulObjectInputDto
{
    [JsonProperty(Entity.PropertyNameId)]
    public string? Id { get; set; }

    [JsonProperty(SchemafulObject.PropertyNamePropertyValues)]
    public Dictionary<string, object?> PropertyValues { get; set; }

    [JsonProperty(IHasCreatedAt.PropertyNameCreatedAt)]
    public DateTimeOffset? CreatedAt { get; set; }

    [JsonConstructor]
    public InnerSchemafulObjectInputDto(
        string? id,
        Dictionary<string, object?> propertyValues,
        DateTimeOffset? createdAt)
    {
        Id = id;
        PropertyValues = propertyValues;
        CreatedAt = createdAt;
    }
}