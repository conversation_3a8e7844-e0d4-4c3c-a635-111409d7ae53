﻿using Sleekflow.CrmHub.Models.Schemas;
using Sleekflow.CrmHub.Models.Schemas.Properties;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions.CrmHub;

namespace Sleekflow.CrmHub.Schemas.Utils;

public interface IDataTypeInnerSchemaService
{
    InnerSchema Create(List<Property> properties);

    void Update(InnerSchema originalInnerSchema, InnerSchema receivedInnerSchema);
}

public class DataTypeInnerSchemaService : IDataTypeInnerSchemaService, ISingletonService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ISchemaValidator _schemaValidator;

    public DataTypeInnerSchemaService(IServiceProvider serviceProvider, ISchemaValidator schemaValidator)
    {
        _serviceProvider = serviceProvider;
        _schemaValidator = schemaValidator;
    }

    public InnerSchema Create(List<Property> properties)
    {
        AssertInnerSchemaPropertiesDepth(properties);

        var constructedProperties = properties
            .Select(
                p => GetPropertyConstructor().Construct(p))
            .ToList();

        _schemaValidator.ValidateAndSortProperties(constructedProperties);

        return new InnerSchema(constructedProperties);
    }

    public void Update(InnerSchema originalInnerSchema, InnerSchema receivedInnerSchema)
    {
        AssertInnerSchemaPropertiesDepth(receivedInnerSchema.Properties);

        var toBeDeletedPropertyIds = originalInnerSchema
            .Properties
            .Where(p => !receivedInnerSchema.Properties.Exists(rp => rp.Id == p.Id))
            .Select(p => p.Id)
            .ToList();

        foreach (var receivedProperty in receivedInnerSchema.Properties)
        {
            var index = originalInnerSchema.Properties.FindIndex(p => p.Id == receivedProperty.Id);
            if (index == -1)
            {
                var newProperty = GetPropertyConstructor().Construct(receivedProperty);

                originalInnerSchema.Properties.Add(newProperty);
            }
            else
            {
                GetPropertyConstructor().Update(originalInnerSchema.Properties[index], receivedProperty);
            }
        }

        originalInnerSchema.Properties.RemoveAll(p => toBeDeletedPropertyIds.Contains(p.Id));

        _schemaValidator.ValidateAndSortProperties(originalInnerSchema.Properties);
    }

    private static void AssertInnerSchemaPropertiesDepth(List<Property> properties)
    {
        // validate if have nested inner schema
        if (properties.Exists(x => x.DataType.HasInnerSchema()))
        {
            throw new SfCustomObjectNestedInnerSchemaException();
        }
    }

    private IPropertyConstructor GetPropertyConstructor()
    {
        return _serviceProvider.GetRequiredService<IPropertyConstructor>();
    }
}