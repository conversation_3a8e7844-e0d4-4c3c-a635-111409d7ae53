using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;

namespace Sleekflow.IntelligentHub.Configs;

public interface IAzureFormRecognizerConfig
{
    string AzureFormRecognizerEndpoint { get; }

    string AzureFormRecognizerKey { get; }
}

public class AzureFormRecognizerConfig : IConfig, IAzureFormRecognizerConfig
{
    public string AzureFormRecognizerEndpoint { get; }

    public string AzureFormRecognizerKey { get; }

    public AzureFormRecognizerConfig()
    {
        const EnvironmentVariableTarget target = EnvironmentVariableTarget.Process;

        AzureFormRecognizerEndpoint =
            Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_ENDPOINT", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURE_FORM_RECOGNIZER_ENDPOINT");

        AzureFormRecognizerKey =
            Environment.GetEnvironmentVariable("AZURE_FORM_RECOGNIZER_KEY", target)
            ?? throw new SfMissingEnvironmentVariableException("AZURE_FORM_RECOGNIZER_KEY");
    }
}