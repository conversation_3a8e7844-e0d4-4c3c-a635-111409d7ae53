using Sleekflow.FlowHub.Models.States;
using Sleekflow.FlowHub.Triggers.States;
using Sleekflow.Outputs;

namespace Sleekflow.FlowHub.Tests.IntegrationTests;

public class StatesCoreIntegrationTests
{
    [Test]
    public async Task GetObjectStatesTest()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";

        // /States/GetObjectStates
        var getObjectStatesInput =
            new GetObjectStates.GetObjectStatesInput(
                mockCompanyId,
                "a5005219-8f9b-4362-bd45-b80007ea67a9",
                "Contact",
                null,
                50);
        var getObjectStatesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getObjectStatesInput).ToUrl("/States/GetObjectStates");
            });
        var getObjectStatesOutput =
            await getObjectStatesScenarioResult.ReadAsJsonAsync<
                Output<GetObjectStates.GetObjectStatesOutput>>();

        Assert.That(getObjectStatesOutput, Is.Not.Null);
        Assert.That(getObjectStatesOutput!.HttpStatusCode, Is.EqualTo(200));
    }

    [Test]
    public async Task GetStatesTest()
    {
        var mockCompanyId = "b6d7e442-38ae-4b9a-b100-2951729768bc";
        var mockWorkflowId = "aQPUxexxV2GYPVz";

        // /States/GetStates
        var getStatesInput =
            new GetStates.GetStatesInput(
                mockCompanyId,
                null,
                50,
                new GetStates.GetStatesInputFilters(
                    mockWorkflowId,
                    null,
                    null,
                    null));
        var getStatesScenarioResult = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStatesInput).ToUrl("/States/GetStates");
            });
        var getStatesOutput =
            await getStatesScenarioResult.ReadAsJsonAsync<
                Output<GetStates.GetStatesOutput>>();

        Assert.That(getStatesOutput, Is.Not.Null);
        Assert.That(getStatesOutput!.HttpStatusCode, Is.EqualTo(200));

        // /States/GetStates
        var getStatesInput2 =
            new GetStates.GetStatesInput(
                mockCompanyId,
                null,
                50,
                new GetStates.GetStatesInputFilters(
                    mockWorkflowId,
                    StateStatuses.Complete,
                    null,
                    null));
        var getStatesScenarioResult2 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStatesInput2).ToUrl("/States/GetStates");
            });
        var getStatesOutput2 =
            await getStatesScenarioResult2.ReadAsJsonAsync<
                Output<GetStates.GetStatesOutput>>();

        Assert.That(getStatesOutput2, Is.Not.Null);
        Assert.That(getStatesOutput2!.HttpStatusCode, Is.EqualTo(200));

        // /States/GetStates
        var getStatesInput3 =
            new GetStates.GetStatesInput(
                mockCompanyId,
                null,
                50,
                new GetStates.GetStatesInputFilters(
                    mockWorkflowId,
                    null,
                    DateTimeOffset.UtcNow.AddDays(-10),
                    DateTimeOffset.UtcNow));
        var getStatesScenarioResult3 = await Application.Host.Scenario(
            _ =>
            {
                _.WithRequestHeader("X-Sleekflow-Record", "true");
                _.Post.Json(getStatesInput3).ToUrl("/States/GetStates");
            });
        var getStatesOutput3 =
            await getStatesScenarioResult3.ReadAsJsonAsync<
                Output<GetStates.GetStatesOutput>>();

        Assert.That(getStatesOutput3, Is.Not.Null);
        Assert.That(getStatesOutput3!.HttpStatusCode, Is.EqualTo(200));
    }
}