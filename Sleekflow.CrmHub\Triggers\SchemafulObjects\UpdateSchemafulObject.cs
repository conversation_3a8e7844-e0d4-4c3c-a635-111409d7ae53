﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.CrmHub.Schemas;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class UpdateSchemafulObject
    : ITrigger<UpdateSchemafulObject.UpdateSchemafulObjectInput, UpdateSchemafulObject.UpdateSchemafulObjectOutput>
{
    private readonly ISchemaService _schemaService;
    private readonly ISchemafulObjectService _schemafulObjectService;

    public UpdateSchemafulObject(
        ISchemafulObjectService schemafulObjectService,
        ISchemaService schemaService)
    {
        _schemafulObjectService = schemafulObjectService;
        _schemaService = schemaService;
    }

    public class UpdateSchemafulObjectInput : IHasSleekflowCompanyId, IHasSleekflowUserProfileId, IHasSleekflowStaff
    {
        [Required]
        [JsonProperty(Entity.PropertyNameId)]
        public string Id { get; set; }

        [Required]
        [JsonProperty("schema_id")]
        public string SchemaId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(SchemafulObject.PropertyNamePropertyValues)]
        [Validations.ValidateObject]
        public Dictionary<string, object?> PropertyValues { get; set; }

        [Required(AllowEmptyStrings = true)]
        [JsonProperty(IHasSleekflowUserProfileId.PropertyNameSleekflowUserProfileId)]
        public string SleekflowUserProfileId { get; set; }

        [Required(AllowEmptyStrings = true)]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        [Validations.ValidateArray]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonProperty(SchemafulObject.PropertyNameUpdatedVia)]
        public string UpdatedVia { get; set; }

        [JsonConstructor]
        public UpdateSchemafulObjectInput(
            string id,
            string schemaId,
            string sleekflowCompanyId,
            Dictionary<string, object?> propertyValues,
            string sleekflowUserProfileId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds,
            string updatedVia)
        {
            Id = id;
            SchemaId = schemaId;
            SleekflowCompanyId = sleekflowCompanyId;
            PropertyValues = propertyValues;
            SleekflowUserProfileId = sleekflowUserProfileId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
            UpdatedVia = updatedVia;
        }
    }

    public class UpdateSchemafulObjectOutput
    {
        [JsonProperty("schemaful_object")]
        public SchemafulObjectDto SchemafulObject { get; set; }

        [JsonConstructor]
        public UpdateSchemafulObjectOutput(SchemafulObjectDto schemafulObject)
        {
            SchemafulObject = schemafulObject;
        }
    }

    public async Task<UpdateSchemafulObjectOutput> F(UpdateSchemafulObjectInput updateSchemafulObjectInput)
    {
        var schema = await _schemaService.GetAsync(
            updateSchemafulObjectInput.SchemaId,
            updateSchemafulObjectInput.SleekflowCompanyId);

        var schemafulObject = await _schemafulObjectService.PatchAndGetSchemafulObjectAsync(
            updateSchemafulObjectInput.Id,
            schema,
            updateSchemafulObjectInput.SleekflowCompanyId,
            updateSchemafulObjectInput.PropertyValues,
            updateSchemafulObjectInput.SleekflowUserProfileId,
            updateSchemafulObjectInput.UpdatedVia,
            new AuditEntity.SleekflowStaff(
                updateSchemafulObjectInput.SleekflowStaffId,
                updateSchemafulObjectInput.SleekflowStaffTeamIds));

        return new UpdateSchemafulObjectOutput(new SchemafulObjectDto(schemafulObject));
    }
}