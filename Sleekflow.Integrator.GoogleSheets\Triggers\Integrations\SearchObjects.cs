﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.InflowActions;
using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Integrator.GoogleSheets.Authentications;
using Sleekflow.Integrator.GoogleSheets.Connections;
using Sleekflow.Integrator.GoogleSheets.Services;
using Sleekflow.Validations;

namespace Sleekflow.Integrator.GoogleSheets.Triggers.Integrations;

[TriggerGroup("Integrations")]
public class SearchObjects : ITrigger
{
    private readonly IGoogleSheetsObjectService _googleSheetsObjectService;
    private readonly IGoogleSheetsAuthenticationService _googleSheetsAuthenticationService;
    private readonly IGoogleSheetsConnectionService _googleSheetsConnectionService;

    public SearchObjects(
        IGoogleSheetsObjectService googleSheetsObjectService,
        IGoogleSheetsAuthenticationService googleSheetsAuthenticationService,
        IGoogleSheetsConnectionService googleSheetsConnectionService)
    {
        _googleSheetsObjectService = googleSheetsObjectService;
        _googleSheetsAuthenticationService = googleSheetsAuthenticationService;
        _googleSheetsConnectionService = googleSheetsConnectionService;
    }

    public class SearchObjectsInput
    {
        [JsonProperty("sleekflow_company_id")]
        [Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("connection_id")]
        [Required]
        public string ConnectionId { get; set; }

        [JsonProperty("entity_type_name")]
        [Required]
        public string EntityTypeName { get; set; }

        [JsonProperty("typed_ids")]
        [ValidateArray]
        public List<TypedId> TypedIds { get; set; }

        [JsonProperty("conditions")]
        [ValidateArray]
        public List<SearchObjectCondition>? Conditions { get; set; }

        [JsonConstructor]
        public SearchObjectsInput(
            string sleekflowCompanyId,
            string connectionId,
            string entityTypeName,
            List<TypedId> typedIds,
            List<SearchObjectCondition>? conditions)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            ConnectionId = connectionId;
            TypedIds = typedIds;
            EntityTypeName = entityTypeName;
            Conditions = conditions;
        }
    }

    public class SearchObjectsOutput
    {
        [JsonProperty("objects")]
        [Required]
        public List<Dictionary<string, object?>> Objects { get; set; }

        [JsonConstructor]
        public SearchObjectsOutput(
            List<Dictionary<string, object?>> objects)
        {
            Objects = objects;
        }
    }

    public async Task<SearchObjectsOutput> F(SearchObjectsInput searchObjectsInput)
    {
        var connection = await _googleSheetsConnectionService.GetByIdAsync(
            searchObjectsInput.ConnectionId,
            searchObjectsInput.SleekflowCompanyId);

        var authentication =
            await _googleSheetsAuthenticationService.GetAsync(
                connection.AuthenticationId,
                searchObjectsInput.SleekflowCompanyId);
        if (authentication == null)
        {
            throw new SfUnauthorizedException();
        }

        return new SearchObjectsOutput(await _googleSheetsObjectService.SearchObjectsAsync(
            authentication,
            searchObjectsInput.EntityTypeName,
            searchObjectsInput.TypedIds,
            searchObjectsInput.Conditions));
    }
}