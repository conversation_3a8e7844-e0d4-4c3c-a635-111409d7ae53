using System.ComponentModel.DataAnnotations;
using GraphApi.Client.Payloads;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.MessagingHub.Models.Constants;
using Sleekflow.MessagingHub.WhatsappCloudApis.Migrations;

namespace Sleekflow.MessagingHub.Triggers.Migrations.WhatsappCloudApi;

[TriggerGroup(ControllerNames.Migrations)]
public class GetWhatsappCloudApiUserBusinesses
    : ITrigger<
        GetWhatsappCloudApiUserBusinesses.GetWhatsappCloudApiUserBusinessesInput,
        GetWhatsappCloudApiUserBusinesses.GetWhatsappCloudApiUserBusinessesOutput>
{
    private readonly IMigrationService _migrationService;

    public GetWhatsappCloudApiUserBusinesses(IMigrationService migrationService)
    {
        _migrationService = migrationService;
    }

    public class GetWhatsappCloudApiUserBusinessesInput
    {
        [Required]
        [JsonProperty("sleekflow_company_id")]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty("user_access_token")]
        public string UserAccessToken { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiUserBusinessesInput(
            string sleekflowCompanyId,
            string userAccessToken)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            UserAccessToken = userAccessToken;
        }
    }

    public class GetWhatsappCloudApiUserBusinessesOutput
    {
        [JsonProperty("user_businesses")]
        public List<GetBusinessDetailResponse> UserBusinesses { get; set; }

        [JsonConstructor]
        public GetWhatsappCloudApiUserBusinessesOutput(List<GetBusinessDetailResponse> userBusinesses)
        {
            UserBusinesses = userBusinesses;
        }
    }

    public async Task<GetWhatsappCloudApiUserBusinessesOutput> F(
        GetWhatsappCloudApiUserBusinessesInput getUserBusinessesInput)
    {
        return new GetWhatsappCloudApiUserBusinessesOutput(
            await _migrationService.GetWhatsappCloudApiUserBusinessesAsync(
                getUserBusinessesInput.SleekflowCompanyId,
                getUserBusinessesInput.UserAccessToken));
    }
}