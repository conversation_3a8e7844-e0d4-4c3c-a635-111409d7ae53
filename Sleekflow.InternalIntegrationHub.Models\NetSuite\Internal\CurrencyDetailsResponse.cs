using Newtonsoft.Json;
using Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal.Common;

namespace Sleekflow.InternalIntegrationHub.Models.NetSuite.Internal;

public class CurrencyDetailsResponse
{
    [JsonProperty("links", NullValueHandling = NullValueHandling.Ignore)]
    public List<Link> Links { get; set; }

    [JsonProperty("currencyPrecision", NullValueHandling = NullValueHandling.Ignore)]
    public int CurrencyPrecision { get; set; }

    [JsonProperty("displaySymbol", NullValueHandling = NullValueHandling.Ignore)]
    public string DisplaySymbol { get; set; }

    [JsonProperty("exchangeRate", NullValueHandling = NullValueHandling.Ignore)]
    public double ExchangeRate { get; set; }

    [JsonProperty("formatSample", NullValueHandling = NullValueHandling.Ignore)]
    public string FormatSample { get; set; }

    [JsonProperty("id", NullValueHandling = NullValueHandling.Ignore)]
    public string Id { get; set; }

    [JsonProperty("isBaseCurrency", NullValueHandling = NullValueHandling.Ignore)]
    public bool IsBaseCurrency { get; set; }

    [JsonProperty("isInactive", NullValueHandling = NullValueHandling.Ignore)]
    public bool IsInactive { get; set; }

    [JsonProperty("lastModifiedDate", NullValueHandling = NullValueHandling.Ignore)]
    public DateTime LastModifiedDate { get; set; }

    [JsonProperty("locale", NullValueHandling = NullValueHandling.Ignore)]
    public Locale Locale { get; set; }

    [JsonProperty("name", NullValueHandling = NullValueHandling.Ignore)]
    public string Name { get; set; }

    [JsonProperty("overrideCurrencyFormat", NullValueHandling = NullValueHandling.Ignore)]
    public bool OverrideCurrencyFormat { get; set; }

    [JsonProperty("refName", NullValueHandling = NullValueHandling.Ignore)]
    public string? RefName { get; set; }

    [JsonProperty("symbol", NullValueHandling = NullValueHandling.Ignore)]
    public string Symbol { get; set; }

    [JsonProperty("symbolPlacement", NullValueHandling = NullValueHandling.Ignore)]
    public SymbolPlacement SymbolPlacement { get; set; }

    [JsonConstructor]
    public CurrencyDetailsResponse(
        List<Link> links,
        int currencyPrecision,
        string displaySymbol,
        double exchangeRate,
        string formatSample,
        string id,
        bool isBaseCurrency,
        bool isInactive,
        DateTime lastModifiedDate,
        Locale locale,
        string name,
        bool overrideCurrencyFormat,
        string refName,
        string symbol,
        SymbolPlacement symbolPlacement)
    {
        Links = links;
        CurrencyPrecision = currencyPrecision;
        DisplaySymbol = displaySymbol;
        ExchangeRate = exchangeRate;
        FormatSample = formatSample;
        Id = id;
        IsBaseCurrency = isBaseCurrency;
        IsInactive = isInactive;
        LastModifiedDate = lastModifiedDate;
        Locale = locale;
        Name = name;
        OverrideCurrencyFormat = overrideCurrencyFormat;
        RefName = refName;
        Symbol = symbol;
        SymbolPlacement = symbolPlacement;
    }
}