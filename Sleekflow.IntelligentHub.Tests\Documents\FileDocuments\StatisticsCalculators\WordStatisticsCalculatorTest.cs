using Moq;
using Sleekflow.IntelligentHub.Documents.FileDocuments.StatisticsCalculators;
using Sleekflow.IntelligentHub.Documents.Statistics;

namespace Sleekflow.IntelligentHub.Tests.Documents.FileDocuments.StatisticsCalculators;

[TestFixture]
[TestOf(typeof(WordStatisticsCalculator))]
public class WordStatisticsCalculatorTest
{
    // Relative path from the test execution directory (usually bin/Debug/netX.X)
    // Adjust if your build output path is different
    const string WordFilePath = "../../../Binaries/OT&P - Flu Shot Summary 2024 for Sleekflow.docx";

    [Test]
    public void CalculateDocumentStatisticsTest()
    {
        var statisticsCalculator = new WordStatisticsCalculator(new Mock<IDocumentCounterService>().Object);

        using var fileStream = new FileStream(
            WordFilePath,
            FileMode.Open,
            FileAccess.Read,
            FileShare.Read); // Use FileShare.Read
        var documentStatistics = statisticsCalculator.CalculateDocumentStatistics(fileStream);

        Assert.That(documentStatistics.TotalPages, Is.EqualTo(1));
    }
}