using Microsoft.SemanticKernel;
using Serilog;
using ILogger = Serilog.ILogger;

namespace Sleekflow.IntelligentHub.Kernels;

public class SafePromptFilter : IPromptRenderFilter
{
    private readonly ILogger _logger = Log.Logger.ForContext<SafePromptFilter>();

    private readonly string[] _needPrintFunctions = Environment.GetEnvironmentVariable("PROMPT_FILTER_PRINT_FUNCTIONS")?.Split(',', StringSplitOptions.RemoveEmptyEntries) ??[];

    public async Task OnPromptRenderAsync(PromptRenderContext context, Func<PromptRenderContext, Task> next)
    {
        await next(context);

        // Record the final rendered prompt
        try
        {
            if (context.RenderedPrompt != null && _needPrintFunctions.Contains(context.Function?.Name ?? "Unknown function"))
            {
                _logger.Information(
                    "Final rendered prompt (function: {FunctionName}): {RenderedPrompt}",
                    context.Function?.Name ?? "Unknown function",
                    context.RenderedPrompt);
            }
        }
        catch (Exception ex)
        {
            _logger.Warning(ex, "Error recording the final rendered prompt");
        }
    }
}