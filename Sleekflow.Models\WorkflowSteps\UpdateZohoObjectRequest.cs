﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.FlowHub.Models.States;

namespace Sleekflow.Models.WorkflowSteps;

public class UpdateZohoObjectRequest
{
    [JsonProperty("aggregate_step_id")]
    [Required]
    public string AggregateStepId { get; set; }

    [JsonProperty("proxy_state_id")]
    [Required]
    public string ProxyStateId { get; set; }

    [JsonProperty("stack_entries")]
    [Required]
    public Stack<StackEntry> StackEntries { get; set; }

    [JsonProperty("sleekflow_company_id")]
    [Required]
    public string SleekflowCompanyId { get; set; }

    [JsonProperty("object_id")]
    [Required]
    public string ObjectId { get; set; }

    [JsonProperty("connection_id")]
    [Required]
    public string ConnectionId { get; set; }

    [JsonProperty("entity_type_name")]
    [Required]
    public string EntityTypeName { get; set; }

    [JsonProperty("object_properties")]
    [Required]
    public Dictionary<string, object?> ObjectProperties { get; set; }

    [JsonConstructor]
    public UpdateZohoObjectRequest(
        string aggregateStepId,
        string proxyStateId,
        Stack<StackEntry> stackEntries,
        string sleekflowCompanyId,
        string objectId,
        string connectionId,
        string entityTypeName,
        Dictionary<string, object?> objectProperties)
    {
        AggregateStepId = aggregateStepId;
        ProxyStateId = proxyStateId;
        StackEntries = stackEntries;
        SleekflowCompanyId = sleekflowCompanyId;
        ObjectId = objectId;
        ConnectionId = connectionId;
        EntityTypeName = entityTypeName;
        ObjectProperties = objectProperties;
    }
}