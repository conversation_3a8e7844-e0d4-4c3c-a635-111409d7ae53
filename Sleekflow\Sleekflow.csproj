<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>netstandard2.1</TargetFramework>
        <LangVersion>11</LangVersion>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Azure.Core" Version="1.45.0" />
        <PackageReference Include="Azure.Identity" Version="1.13.2" />
        <PackageReference Include="Azure.Messaging.EventHubs" Version="5.11.6" />
        <PackageReference Include="Azure.Messaging.EventHubs.Processor" Version="5.11.6" />
        <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.4" />
        <PackageReference Include="Azure.Monitor.OpenTelemetry.Exporter" Version="1.3.0" />
        <PackageReference Include="Azure.Search.Documents" Version="11.6.0" />
        <PackageReference Include="Azure.Storage.Blobs" Version="12.23.0" />
        <PackageReference Include="Hashids.net" Version="1.7.0" />
        <PackageReference Include="IdGen" Version="3.0.7" />
        <PackageReference Include="MassTransit.Analyzers" Version="8.2.4">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="MassTransit.Azure.Cosmos" Version="8.2.4" />
        <PackageReference Include="MassTransit.Azure.ServiceBus.Core" Version="8.2.4" />
        <PackageReference Include="MassTransit.Azure.Storage" Version="8.2.4" />
        <PackageReference Include="MassTransit.EventHub" Version="8.2.5" />
        <PackageReference Include="Microsoft.AspNetCore.Http" Version="2.2.2" />
        <PackageReference Include="Microsoft.Azure.Core.NewtonsoftJson" Version="2.0.0" />
        <PackageReference Include="MassTransit.Newtonsoft" Version="8.2.4" />
        <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.48.0" />
        <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
        <PackageReference Include="Microsoft.Extensions.Azure" Version="1.10.0" />
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Http" Version="8.0.1" />
        <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.1" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.9.0" />
        <PackageReference Include="Polly" Version="8.5.2" />
        <PackageReference Include="Scrutor" Version="4.2.2" />
        <PackageReference Include="Serilog" Version="4.0.1" />
        <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
        <PackageReference Include="Serilog.Sinks.GoogleCloudLogging" Version="5.0.0" />
        <PackageReference Include="StackExchange.Redis" Version="2.8.24" />
        <PackageReference Include="System.Collections.Immutable" Version="8.0.0" />
        <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
        <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
    </ItemGroup>

    <PropertyGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <DefineConstants>SWAGGERGEN</DefineConstants>
    </PropertyGroup>

    <ItemGroup Condition="'$(SWAGGERGEN)' == 'TRUE'">
        <PackageReference Include="Moq" Version="4.18.1" />
    </ItemGroup>

</Project>
