using System.Text.RegularExpressions;

namespace Sleekflow.IntelligentHub.FaqAgents.Chats;

public static class TagUtils
{
    public static string RemoveBetweenTags(string input, params string[] tagNames)
    {
        return tagNames
            .Select(tagName => $@"<{tagName}>[\s\S]*?</{tagName}>")
            .Aggregate(
                input,
                (current, pattern) => Regex.Replace(current, pattern, string.Empty));
    }

    public static string ExtractContentBetweenTags(string input, string tagName)
    {
        var pattern = $@"<{tagName}>(.*?)</{tagName}>";
        var match = Regex.Match(input, pattern, RegexOptions.Singleline);

        return match.Success ? match.Groups[1].Value : string.Empty;
    }
}