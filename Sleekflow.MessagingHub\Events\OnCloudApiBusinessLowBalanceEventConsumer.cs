using MassTransit;
using Sleekflow.MessagingHub.Models.Events;

namespace Sleekflow.MessagingHub.Events;

public class OnCloudApiBusinessLowBalanceEventConsumerDefinition
    : ConsumerDefinition<OnCloudApiBusinessLowBalanceEventConsumer>
{
    protected override void ConfigureConsumer(
        IReceiveEndpointConfigurator endpointConfigurator,
        IConsumerConfigurator<OnCloudApiBusinessLowBalanceEventConsumer> consumerConfigurator)
    {
        if (endpointConfigurator is IServiceBusReceiveEndpointConfigurator serviceBusReceiveEndpointConfiguration)
        {
            serviceBusReceiveEndpointConfiguration.EnablePartitioning = true;
            serviceBusReceiveEndpointConfiguration.MaxSizeInMegabytes = 2048;
            serviceBusReceiveEndpointConfiguration.ConcurrentMessageLimit = 32;
            serviceBusReceiveEndpointConfiguration.PrefetchCount = 32;
        }
        else
        {
            throw new Exception("Unable to handle the endpointConfigurator");
        }
    }
}

public class OnCloudApiBusinessLowBalanceEventConsumer : IConsumer<OnCloudApiBusinessLowBalanceEvent>
{
    private readonly ILogger<OnCloudApiBusinessLowBalanceEventConsumer> _logger;

    public OnCloudApiBusinessLowBalanceEventConsumer(
        ILogger<OnCloudApiBusinessLowBalanceEventConsumer> logger)
    {
        _logger = logger;
    }

    public Task Consume(ConsumeContext<OnCloudApiBusinessLowBalanceEvent> context)
    {
        var onCloudApiBusinessLowBalanceEvent = context.Message;
        var facebookBusinessId = onCloudApiBusinessLowBalanceEvent.FacebookBusinessId;

        _logger.LogWarning("Business low balance {FacebookBusinessId}", facebookBusinessId);

        return Task.CompletedTask;
    }
}