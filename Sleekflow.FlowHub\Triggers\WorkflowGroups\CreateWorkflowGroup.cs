﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.DependencyInjection;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.WorkflowGroups;
using Sleekflow.FlowHub.WorkflowGroups;
using Sleekflow.Ids;
using Sleekflow.Persistence;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.FlowHub.Triggers.WorkflowGroups;

[TriggerGroup(ControllerNames.WorkflowGroups)]
public class CreateWorkflowGroup
    : ITrigger<
        CreateWorkflowGroup.CreateWorkflowGroupInput,
        CreateWorkflowGroup.CreateWorkflowGroupOutput>
{
    private readonly IIdService _idService;
    private readonly IWorkflowGroupService _workflowGroupService;

    public CreateWorkflowGroup(
        IIdService idService,
        IWorkflowGroupService workflowGroupService)
    {
        _idService = idService;
        _workflowGroupService = workflowGroupService;
    }

    public class CreateWorkflowGroupInput : IHasSleekflowCompanyId, IHasSleekflowStaff
    {
        [Required]
        [MinLength(1)]
        [MaxLength(100)]
        [JsonProperty("name")]
        public string Name { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffId)]
        public string SleekflowStaffId { get; set; }

        [ValidateArray]
        [JsonProperty(IHasSleekflowStaff.PropertyNameSleekflowStaffTeamIds)]
        public List<string>? SleekflowStaffTeamIds { get; set; }

        [JsonConstructor]
        public CreateWorkflowGroupInput(
            string name,
            string sleekflowCompanyId,
            string sleekflowStaffId,
            List<string>? sleekflowStaffTeamIds)
        {
            Name = name;
            SleekflowCompanyId = sleekflowCompanyId;
            SleekflowStaffId = sleekflowStaffId;
            SleekflowStaffTeamIds = sleekflowStaffTeamIds;
        }
    }

    public class CreateWorkflowGroupOutput
    {
        [Required]
        [JsonProperty("workflow_group")]
        public WorkflowGroupDto WorkflowGroup { get; set; }

        [JsonConstructor]
        public CreateWorkflowGroupOutput(
            WorkflowGroupDto workflowGroup)
        {
            WorkflowGroup = workflowGroup;
        }
    }

    public async Task<CreateWorkflowGroupOutput> F(CreateWorkflowGroupInput input)
    {
        var sleekflowStaff = new AuditEntity.SleekflowStaff(
            input.SleekflowStaffId,
            input.SleekflowStaffTeamIds);

        var workflowGroupId = _idService.GetId("WorkflowGroup");

        var workflowGroup = await _workflowGroupService.CreateWorkflowGroupAsync(
            new WorkflowGroup(
                input.Name,
                workflowGroupId,
                createdAt: DateTimeOffset.UtcNow,
                updatedAt: DateTimeOffset.UtcNow,
                input.SleekflowCompanyId,
                createdBy: sleekflowStaff,
                updatedBy: null));

        return new CreateWorkflowGroupOutput(new WorkflowGroupDto(workflowGroup));
    }
}