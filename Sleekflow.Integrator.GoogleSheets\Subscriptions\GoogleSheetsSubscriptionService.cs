﻿using Sleekflow.CrmHub.Models.Providers;
using Sleekflow.CrmHub.Models.Subscriptions;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.Ids;

namespace Sleekflow.Integrator.GoogleSheets.Subscriptions;

public interface IGoogleSheetsSubscriptionService
{
    Task UpsertAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<TypedId> typedIds,
        string entityTypeName,
        int interval,
        bool isFlowsBased,
        DateTimeOffset? lastObjectModificationTime);

    Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId);

    Task<GoogleSheetsSubscription?> GetAsync(
        string connectionId,
        string sleekflowCompanyId,
        List<TypedId> typedIds,
        string entityTypeName,
        bool isFlowsBased);

    Task<List<GoogleSheetsSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName,
        List<TypedId>? typedIds);
}

public class GoogleSheetsSubscriptionService : IGoogleSheetsSubscriptionService, ISingletonService
{
    private readonly IGoogleSheetsSubscriptionRepository _googleSheetsSubscriptionRepository;
    private readonly IIdService _idService;

    public GoogleSheetsSubscriptionService(
        IGoogleSheetsSubscriptionRepository googleSheetsSubscriptionRepository,
        IIdService idService)
    {
        _googleSheetsSubscriptionRepository = googleSheetsSubscriptionRepository;
        _idService = idService;
    }

    public async Task UpsertAsync(
        string sleekflowCompanyId,
        string connectionId,
        List<TypedId> typedIds,
        string entityTypeName,
        int interval,
        bool isFlowsBased,
        DateTimeOffset? lastObjectModificationTime)
    {
        var createCount = await _googleSheetsSubscriptionRepository.CreateAsync(
            new GoogleSheetsSubscription(
                _idService.GetId(GoogleSheetsSubscription.SysTypeNameValue),
                sleekflowCompanyId,
                connectionId,
                typedIds,
                entityTypeName,
                interval,
                isFlowsBased,
                DateTimeOffset.UtcNow,
                lastObjectModificationTime,
                null),
            sleekflowCompanyId);
        if (createCount == 0)
        {
            throw new SfUserFriendlyException("Unable to init the type");
        }
    }

    public async Task ClearByConnectionIdAsync(string connectionId, string sleekflowCompanyId)
    {
        await foreach (var subscription in _googleSheetsSubscriptionRepository.GetObjectEnumerableAsync(
                           s =>
                               s.SysTypeName == GoogleSheetsSubscription.SysTypeNameValue
                               && s.ConnectionId == connectionId
                               && s.SleekflowCompanyId == sleekflowCompanyId))
        {
            await _googleSheetsSubscriptionRepository.DeleteAsync(
                subscription.Id,
                subscription.SleekflowCompanyId);
        }
    }

    public async Task<GoogleSheetsSubscription?> GetAsync(
        string connectionId,
        string sleekflowCompanyId,
        List<TypedId> typedIds,
        string entityTypeName,
        bool isFlowsBased)
    {
        return (await _googleSheetsSubscriptionRepository.GetObjectsAsync(
            s =>
                s.SysTypeName == GoogleSheetsSubscription.SysTypeNameValue
                && s.ConnectionId == connectionId
                && s.SleekflowCompanyId == sleekflowCompanyId
                && s.EntityTypeName == entityTypeName
                && s.IsFlowsBased == isFlowsBased))
            .Find(s => s.TypedIds.SequenceEqual(typedIds));
    }

    public async Task<List<GoogleSheetsSubscription>> GetSubscriptionsAsync(
        string sleekflowCompanyId,
        string connectionId,
        string entityTypeName,
        List<TypedId>? typedIds)
    {
        var subscriptions = await _googleSheetsSubscriptionRepository.GetObjectsAsync(
            x => x.SleekflowCompanyId == sleekflowCompanyId
                 && x.ConnectionId == connectionId
                 && x.EntityTypeName == entityTypeName);
        if (typedIds != null)
        {
            subscriptions = subscriptions.Where(s => s.TypedIds.SequenceEqual(typedIds)).ToList();
        }

        return subscriptions;
    }
}