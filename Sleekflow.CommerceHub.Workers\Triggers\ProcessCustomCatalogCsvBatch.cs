﻿using System.Text;
using Microsoft.Azure.Functions.Worker;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Sleekflow.CommerceHub.Models.CustomCatalogs.Readers;
using Sleekflow.CommerceHub.Workers.Configs;
using Sleekflow.DependencyInjection;
using Sleekflow.Exceptions;
using Sleekflow.JsonConfigs;
using Sleekflow.Outputs;
using Sleekflow.Utils;

namespace Sleekflow.CommerceHub.Workers.Triggers;

public class ProcessCustomCatalogCsvBatch : ITrigger
{
    private readonly IAppConfig _appConfig;
    private readonly HttpClient _httpClient;

    public ProcessCustomCatalogCsvBatch(
        IHttpClientFactory httpClientFactory,
        IAppConfig appConfig)
    {
        _appConfig = appConfig;
        _httpClient = httpClientFactory.CreateClient("default-handler");
    }

    public class ProcessCustomCatalogCsvBatchInput
    {
        [JsonProperty("sleekflow_company_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string SleekflowCompanyId { get; set; }

        [JsonProperty("custom_catalog_file_id")]
        [System.ComponentModel.DataAnnotations.Required]
        public string CustomCatalogFileId { get; set; }

        [JsonProperty("my_custom_catalog_csv_reader_state")]
        public MyCustomCatalogCsvReaderState? MyCustomCatalogCsvReaderState { get; set; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvBatchInput(
            string sleekflowCompanyId,
            string customCatalogFileId,
            MyCustomCatalogCsvReaderState? myCustomCatalogCsvReaderState)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            CustomCatalogFileId = customCatalogFileId;
            MyCustomCatalogCsvReaderState = myCustomCatalogCsvReaderState;
        }
    }

    public class ProcessCustomCatalogCsvBatchOutput
    {
        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonProperty("my_custom_catalog_csv_reader_state")]
        public MyCustomCatalogCsvReaderState? MyCustomCatalogCsvReaderState { get; }

        [JsonConstructor]
        public ProcessCustomCatalogCsvBatchOutput(
            long count,
            MyCustomCatalogCsvReaderState? myCustomCatalogCsvReaderState)
        {
            Count = count;
            MyCustomCatalogCsvReaderState = myCustomCatalogCsvReaderState;
        }
    }

    [Function("ProcessCustomCatalogCsv_Batch")]
    public async Task<ProcessCustomCatalogCsvBatchOutput> Batch(
        [ActivityTrigger]
        ProcessCustomCatalogCsvBatchInput processCustomCatalogCsvBatchInput)
    {
        var inputJsonStr =
            JsonConvert.SerializeObject(processCustomCatalogCsvBatchInput, JsonConfig.DefaultJsonSerializerSettings);

        var reqMsg = new HttpRequestMessage
        {
            Method = HttpMethod.Post,
            Content = new StringContent(inputJsonStr, Encoding.UTF8, "application/json"),
            RequestUri = new Uri(_appConfig.CommerceHubInternalsEndpoint + "/ProcessCustomCatalogCsvBatch"),
            Headers =
            {
                {
                    "X-Sleekflow-Key", _appConfig.InternalsKey
                }
            },
        };
        var resMsg = (await _httpClient.SendAsync(reqMsg)).EnsureSuccessStatusCode();
        var resStr = await resMsg.Content.ReadAsStringAsync();

        var output = resStr.ToObject<Output<dynamic>>();

        if (output == null)
        {
            throw new SfInternalErrorException(
                $"The resMsg {resMsg}, resStr {resStr}, inputJsonStr {inputJsonStr} is not working");
        }

        if (output.Success == false)
        {
            throw new ErrorCodeException(output);
        }

        return ((JObject) output.Data).ToObject<ProcessCustomCatalogCsvBatchOutput>()!;
    }
}