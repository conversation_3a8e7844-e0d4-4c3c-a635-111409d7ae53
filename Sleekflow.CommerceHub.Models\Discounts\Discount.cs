using Newtonsoft.Json;
using Sleekflow.Persistence.Abstractions;

namespace Sleekflow.CommerceHub.Models.Discounts;

public class Discount : IHasMetadata
{
    [JsonProperty("title")]
    public string? Title { get; set; }

    [JsonProperty("description")]
    public string? Description { get; set; }

    [JsonProperty("value")]
    public decimal Value { get; set; }

    [JsonProperty("type")]
    public string Type { get; set; }

    [JsonProperty("metadata")]
    public Dictionary<string, object?> Metadata { get; set; }

    [JsonConstructor]
    public Discount(
        string? title,
        string? description,
        decimal value,
        string type,
        Dictionary<string, object?> metadata)
    {
        Title = title;
        Description = description;
        Value = value;
        Type = type;
        Metadata = metadata;
    }
}