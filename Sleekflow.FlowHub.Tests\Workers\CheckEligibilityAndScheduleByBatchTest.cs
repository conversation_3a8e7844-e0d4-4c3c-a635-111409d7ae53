using Microsoft.Extensions.Logging;
using Moq;
using Sleekflow.FlowHub.Models.Constants;
using Sleekflow.FlowHub.Models.Internals;
using Sleekflow.FlowHub.Models.Workflows.Settings;
using Sleekflow.FlowHub.Workers.Configs;
using Sleekflow.FlowHub.Workers.Services;
using Sleekflow.FlowHub.Workers.Triggers.Activities;
using Sleekflow.Models.Crm;

namespace Sleekflow.FlowHub.Tests.Workers
{
    [TestFixture]
    public class CheckEligibilityAndScheduleByBatchTest
    {
        private CheckEligibilityAndScheduleByBatch _activity;
        private Mock<IContactBatchService> _mockContactBatchService;
        private Mock<IContactEligibilityService> _mockContactEligibilityService;
        private Mock<IContactEnrollmentService> _mockContactEnrollmentService;
        private Mock<ILogger<CheckEligibilityAndScheduleByBatch>> _mockLogger;
        private Mock<IHttpClientFactory> _mockHttpClientFactory;
        private Mock<ISleekflowCoreConfig> _mockCoreConfig;
        private Mock<IGetPropertyValueService> _mockContactPropertyValueService;

        [SetUp]
        public void Setup()
        {
            _mockContactBatchService = new Mock<IContactBatchService>();
            _mockContactEligibilityService = new Mock<IContactEligibilityService>();
            _mockContactEnrollmentService = new Mock<IContactEnrollmentService>();
            _mockLogger = new Mock<ILogger<CheckEligibilityAndScheduleByBatch>>();
            _mockHttpClientFactory = new Mock<IHttpClientFactory>();
            _mockCoreConfig = new Mock<ISleekflowCoreConfig>();
            _mockContactPropertyValueService = new Mock<IGetPropertyValueService>();

            // 初始化被测类
            _activity = new CheckEligibilityAndScheduleByBatch(
                _mockContactBatchService.Object,
                _mockContactEligibilityService.Object,
                _mockContactEnrollmentService.Object,
                _mockLogger.Object,
                _mockContactPropertyValueService.Object
            );
        }

        [Test]
        public async Task GetPropertyValues_ContactPropertyBasedDateTime_ReturnsValidDates()
        {
            var contactPropertyDateTimeSettings =
                new ContactPropertyDateTimeSettings("property-1", null, null, null, null);
            // Arrange
            var input = new CheckEligibilityAndScheduleByBatch.CheckEligibilityAndScheduleByBatchInput(
                origin: "https://test.com",
                sleekflowCompanyId: "company-1",
                lastContactCreatedAt: null,
                lastContactId: null,
                batchSize: 10,
                workflowId: "workflow-1",
                workflowVersionedId: "workflow-1-v1",
                workflowName: "Test Workflow",
                condition: "true",
                contactPropertyDateTimeSettings: contactPropertyDateTimeSettings,
                customObjectDateTimeSettings: null,
                scheduledType: WorkflowScheduleTypes.ContactPropertyBasedDateTime,
                workflowRecurringSettings: new WorkflowRecurringSettings(),
                currentTime: DateTimeOffset.UtcNow
            );
            var contactDetails = new Dictionary<string, ContactDetail>
            {
                {
                    "contact-1", new ContactDetail(null, null, null, null)
                },
                {
                    "contact-2", new ContactDetail(null, null, null, null)
                }
            };
            var contactsBatchResult = new GetContactsByBatchOutput(contactDetails, null);

            Dictionary<string, string> output = new Dictionary<string, string>
            {
                { "contact-1", "2023-10-01T00:00:00Z" },
                { "contact-2", "2023-10-02T00:00:00Z" }
            };

            // Setup mock
            _mockContactPropertyValueService
                .Setup(x => x.GetContactPropertyValuesByContactIds(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, ContactDetail>>(),
                    It.IsAny<string>()))
                .ReturnsAsync(new GetContactPropertyValueByContactIdsOutput(output));

            // Act
            var result = await _activity.GetPropertyValues(input, contactsBatchResult);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Length);
            Assert.AreEqual("contact-1", result[0].ContactId);
            Assert.AreEqual("2023-10-01T00:00:00Z", result[0].DateTimeList[0].ToString("yyyy-MM-ddTHH:mm:ssZ"));
            Assert.AreEqual("contact-2", result[1].ContactId);
            Assert.AreEqual("2023-10-02T00:00:00Z", result[1].DateTimeList[0].ToString("yyyy-MM-ddTHH:mm:ssZ"));
        }

        [Test]
        public async Task GetPropertyValues_SchemafulObjectPropertyBasedDateTime_ReturnsValidDates()
        {
            // Arrange
            var input = new CheckEligibilityAndScheduleByBatch.CheckEligibilityAndScheduleByBatchInput(
                origin: "https://test.com",
                sleekflowCompanyId: "company-1",
                lastContactCreatedAt: null,
                lastContactId: null,
                batchSize: 10,
                workflowId: "workflow-1",
                workflowVersionedId: "workflow-1-v1",
                workflowName: "Test Workflow",
                condition: "true",
                contactPropertyDateTimeSettings: null,
                customObjectDateTimeSettings: new CustomObjectDateTimeSettings(
                    schemaId: "schema-1",
                    schemafulObjectPropertyId: "property-1",
                    triggerDateType: "exactly",
                    triggerDateDuration: new[] { "0", "day" },
                    triggerTimeType: "exactly",
                    triggerCustomTime: null),
                scheduledType: WorkflowScheduleTypes.SchemafulObjectPropertyBasedDateTime,
                workflowRecurringSettings: new WorkflowRecurringSettings(),
                currentTime: DateTimeOffset.UtcNow
            );

            var contactDetails = new Dictionary<string, ContactDetail>
            {
                { "contact-1", new ContactDetail(null, null, null, null) },
                { "contact-2", new ContactDetail(null, null, null, null) }
            };
            var contactsBatchResult = new GetContactsByBatchOutput(contactDetails, null);

            // Setup mock for custom object property values
            var propertyValues = new Dictionary<string, List<object?>>
            {
                { "contact-1", new List<object?> {
                    "2023-10-01T00:00:00Z",
                    "2023-10-01T10:30:00Z",
                    "2023-10-02T15:45:00Z",
                    "2023-10-03T08:20:00Z"
                } },
                { "contact-2", new List<object?> {
                    "2023-10-02T00:00:00Z",
                    "2023-10-02T14:20:00Z",
                    "2023-10-03T09:15:00Z",
                    "2023-10-04T16:30:00Z"
                } }
            };

            _mockContactPropertyValueService
                .Setup(x => x.GetCustomObjectPropertyValueByContactsId(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, ContactDetail>>(),
                    It.IsAny<string>()))
                .ReturnsAsync(new GetPropertyValuesByContactIdsOutput(propertyValues));

            // Act
            var result = await _activity.GetPropertyValues(input, contactsBatchResult);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Length);

            // Verify contact-1
            Assert.AreEqual(2, result.Length);
            Assert.AreEqual("contact-1", result[0].ContactId);
            Assert.AreEqual(4, result[0].DateTimeList.Length);
            Assert.AreEqual("2023-10-01T00:00:00Z", result[0].DateTimeList[0].ToString("yyyy-MM-ddTHH:mm:ssZ"));
            Assert.AreEqual("2023-10-01T10:30:00Z", result[0].DateTimeList[1].ToString("yyyy-MM-ddTHH:mm:ssZ"));
            Assert.AreEqual("2023-10-02T15:45:00Z", result[0].DateTimeList[2].ToString("yyyy-MM-ddTHH:mm:ssZ"));
            Assert.AreEqual("2023-10-03T08:20:00Z", result[0].DateTimeList[3].ToString("yyyy-MM-ddTHH:mm:ssZ"));

            // Verify contact-2
            Assert.AreEqual("contact-2", result[1].ContactId);
            Assert.AreEqual(4, result[1].DateTimeList.Length);
            Assert.AreEqual("2023-10-02T00:00:00Z", result[1].DateTimeList[0].ToString("yyyy-MM-ddTHH:mm:ssZ"));
            Assert.AreEqual("2023-10-02T14:20:00Z", result[1].DateTimeList[1].ToString("yyyy-MM-ddTHH:mm:ssZ"));
            Assert.AreEqual("2023-10-03T09:15:00Z", result[1].DateTimeList[2].ToString("yyyy-MM-ddTHH:mm:ssZ"));
            Assert.AreEqual("2023-10-04T16:30:00Z", result[1].DateTimeList[3].ToString("yyyy-MM-ddTHH:mm:ssZ"));

            // Verify service was called with correct parameters
            _mockContactPropertyValueService.Verify(
                x => x.GetCustomObjectPropertyValueByContactsId(
                    "company-1",
                    "schema-1",
                    "property-1",
                    contactDetails,
                    "https://test.com"),
                Times.Once);
        }
    }
}