﻿using Sleekflow.CrmHub.Models.ProviderConfigs;
using Sleekflow.CrmHub.Providers;
using Sleekflow.DependencyInjection;

namespace Sleekflow.CrmHub.ProviderConfigs;

/// <summary>
/// Primarily used to override sync config for companies when sync conditions are unsupported in the UI.
/// </summary>
public interface ICustomSyncConfigService
{
    void OverrideSyncConfig(SyncConfig syncConfig, string sleekflowCompanyId, string providerName, string entityTypeName);

    void OverrideSyncConfigFilterGroups(
        List<SyncConfigFilterGroup> syncConfigFilterGroups,
        string sleekflowCompanyId,
        string providerName,
        string entityTypeName);
}

/// <summary>
/// Primarily used to override sync config for companies when sync conditions are unsupported in the UI.
/// </summary>
public class CustomSyncConfigService : ICustomSyncConfigService, ISingletonService
{
    public void OverrideSyncConfig(SyncConfig syncConfig, string sleekflowCompanyId, string providerName, string entityTypeName)
    {
        OverrideSyncConfigFilterGroups(syncConfig.FilterGroups, sleekflowCompanyId, providerName, entityTypeName);
    }

    public void OverrideSyncConfigFilterGroups(
        List<SyncConfigFilterGroup> syncConfigFilterGroups,
        string sleekflowCompanyId,
        string providerName,
        string entityTypeName)
    {
        // DEVS-3340 - Elite UAE
        if (sleekflowCompanyId == "4f182034-b214-49ce-8f74-e6ca3708cf96")
        {
            if (providerName == SalesforceIntegratorService.ProviderName && entityTypeName == "Contact")
            {
                var fieldName = "Relationship_Manager__c";
                var fieldValue = "null";
                var fieldOperator = "!=";

                var hasFilter = syncConfigFilterGroups
                    .SelectMany(fg => fg.Filters)
                    .Any(f => f.FieldName == fieldName && f.Value == fieldValue && f.Operator == fieldOperator);

                if (!hasFilter)
                {
                    syncConfigFilterGroups.Add(new SyncConfigFilterGroup(
                        new List<SyncConfigFilter>
                        {
                            new(fieldName: fieldName, value: fieldValue, @operator: fieldOperator),
                        }));
                }
            }
        }
    }
}