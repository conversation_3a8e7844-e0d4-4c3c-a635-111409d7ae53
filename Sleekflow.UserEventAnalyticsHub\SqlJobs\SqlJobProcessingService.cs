using System.Data.Common;
using Dapper;
using DuckDB.NET.Data;
using Newtonsoft.Json;
using Sleekflow.DependencyInjection;
using Sleekflow.JsonConfigs;
using Sleekflow.Locks;
using Sleekflow.Models.Constants;
using Sleekflow.UserEventAnalyticsHub.Configs;
using Sleekflow.UserEventAnalyticsHub.Cores;
using Sleekflow.UserEventAnalyticsHub.Extensions;
using Sleekflow.UserEventHub.Models.Cores;
using Sleekflow.UserEventHub.Models.SqlJobs;
using Sleekflow.UserEventHub.Utils;

namespace Sleekflow.UserEventAnalyticsHub.SqlJobs;

public interface ISqlJobProcessingService
{
    Task ProcessSqlJobAsync(string jobId, string sleekflowCompanyId);

    Task<List<Dictionary<string, object>>> ProcessShortSqlJobAsync(
        string jobId,
        string sleekflowCompanyId,
        CancellationToken cancellationToken);
}

public class SqlJobProcessingService : ISqlJobProcessingService, IScopedService
{
    private readonly ILogger<SqlJobProcessingService> _logger;
    private readonly IPostgreSqlConfig _postgresConfig;
    private readonly ISqlJobService _sqlJobService;
    private readonly ILockService _lockService;
    private readonly ICoreCommander _coreCommander;

    private const int BufferSize = 1024 * 1024; // 1MB buffer
    private const int MaxSizeInBytes = 20 * 1024 * 1024; // 20MB limit

    public SqlJobProcessingService(
        ILogger<SqlJobProcessingService> logger,
        IPostgreSqlConfig postgresConfig,
        ISqlJobService sqlJobService,
        ILockService lockService,
        ICoreCommander coreCommander)
    {
        _logger = logger;
        _postgresConfig = postgresConfig;
        _sqlJobService = sqlJobService;
        _lockService = lockService;
        _coreCommander = coreCommander;
    }

    public async Task ProcessSqlJobAsync(string jobId, string sleekflowCompanyId)
    {
        var job = await _sqlJobService.GetOrDefaultAsync(jobId, sleekflowCompanyId);
        if (job == null)
        {
            _logger.LogInformation("Job {JobId} in Company {SleekflowCompanyId} not found", jobId, sleekflowCompanyId);

            return;
        }

        var @lock = await _lockService.LockAsync(
            [
                nameof(SqlJobProcessingService),
                nameof(ProcessSqlJobAsync),
                jobId
            ],
            TimeSpan.FromMinutes(15));
        if (@lock is null)
        {
            return;
        }

        await _sqlJobService.SetRunningAsync(job);

        try
        {
            // Initialize DuckDB with PostgreSQL connection
            await using var duckDbConnection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
            await duckDbConnection.OpenAsync();

            var userProfileIds = job.Conditions.Count == 0 ? null : await GetUserProfileIdsAsync(job);

            await SetUpEventsDbAsync(
                sleekflowCompanyId,
                userProfileIds,
                duckDbConnection);

            await using var dbDataReader = await duckDbConnection.ExecuteReaderAsync(
                new CommandDefinition(
                    job.SqlQuery,
                    commandTimeout: 300));

            var results = await WriteToListAsync(dbDataReader);

            await _sqlJobService.CompleteJobAsync(
                job,
                $"Results processed successfully: {results.Count} records");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing job {JobId}", job.Id);

            await _sqlJobService.FailJobAsync(
                job,
                ex.Message);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }
    }

    public async Task<List<Dictionary<string, object>>> ProcessShortSqlJobAsync(
        string jobId,
        string sleekflowCompanyId,
        CancellationToken cancellationToken)
    {
        var job = await _sqlJobService.GetOrDefaultAsync(jobId, sleekflowCompanyId);
        if (job == null)
        {
            _logger.LogInformation("Job {JobId} in Company {SleekflowCompanyId} not found", jobId, sleekflowCompanyId);

            return new List<Dictionary<string, object>>();
        }

        var @lock = await _lockService.LockAsync(
            [
                nameof(SqlJobProcessingService),
                nameof(ProcessSqlJobAsync),
                jobId
            ],
            TimeSpan.FromMinutes(15));
        if (@lock is null)
        {
            return new List<Dictionary<string, object>>();
        }

        await _sqlJobService.SetRunningAsync(job);

        try
        {
            // Initialize DuckDB with PostgreSQL connection
            await using var duckDbConnection = new DuckDBConnection("DataSource=:memory:;memory_limit=1536MB");
            await duckDbConnection.OpenAsync(cancellationToken);

            var userProfileIds = job.Conditions.Count == 0 ? null : await GetUserProfileIdsAsync(job);

            await SetUpEventsDbAsync(
                sleekflowCompanyId,
                userProfileIds,
                duckDbConnection);

            await using var dbDataReader = await duckDbConnection.ExecuteReaderAsync(
                new CommandDefinition(
                    job.SqlQuery,
                    commandTimeout: 300));

            var records = await WriteToListAsync(dbDataReader);

            await _sqlJobService.CompleteJobAsync(
                job,
                null);

            return records;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing job {JobId}", job.Id);

            await _sqlJobService.FailJobAsync(
                job,
                ex.Message);
        }
        finally
        {
            await _lockService.ReleaseAsync(@lock);
        }

        return new List<Dictionary<string, object>>();
    }

    private async Task<List<string>> GetUserProfileIdsAsync(SqlJob job)
    {
        // TODO Server Location

        var getUserProfileIdsOutputJson = await _coreCommander.ExecuteAsync(
            "GetUserProfileIds",
            ServerLocations.EastAsia,
            new GetUserProfileIdsInput(
                job.SleekflowCompanyId,
                job.Conditions));
        var getUserProfileIdsOutput = JsonConvert.DeserializeObject<GetUserProfileIdsOutput>(
            getUserProfileIdsOutputJson,
            JsonConfig.DefaultJsonSerializerSettings);

        return getUserProfileIdsOutput?.UserProfileIds ?? new List<string>();
    }

    private async Task SetUpEventsDbAsync(
        string sleekflowCompanyId,
        List<string>? userProfileIds,
        DuckDBConnection duckDbConnection)
    {
        _logger.LogInformation("Setting up DuckDB with PostgreSQL backend for company: {CompanyId}", sleekflowCompanyId);

        // Configure DuckDB with PostgreSQL connection
        await DuckDbExtensions.ConfigureDuckDbAsync(duckDbConnection, _postgresConfig, _logger);

        // Get the proper table name using the helper
        var eventsTableName = PostgreSqlTableNameHelper.GetEventsTableName(sleekflowCompanyId);

        // Create efficient user_profile_ids table with proper indexing if needed
        if (userProfileIds is not null && userProfileIds.Count > 0)
        {
            await using var userProfileTableCommand = duckDbConnection.CreateCommand();
            userProfileTableCommand.CommandText =
                """
                CREATE TABLE user_profile_ids (
                    user_profile_id VARCHAR PRIMARY KEY
                );
                """;
            await userProfileTableCommand.ExecuteNonQueryAsync();

            // Insert user_profile_ids efficiently
            using (var appender = duckDbConnection.CreateAppender("user_profile_ids"))
            {
                foreach (var id in userProfileIds)
                {
                    var row = appender.CreateRow();
                    row.AppendValue(id).EndRow();
                }
            }

            _logger.LogInformation("Created user_profile_ids table with {Count} entries", userProfileIds.Count);
        }

        // Create optimized view with PostgreSQL backend
        await using var viewCommand = duckDbConnection.CreateCommand();

        var userProfileJoin = userProfileIds is null || userProfileIds.Count == 0
            ? string.Empty
            : "JOIN user_profile_ids u ON u.user_profile_id = e.objectId";

        var sql = $"""
            CREATE VIEW events AS
            SELECT
                e.id,
                e.eventType,
                e.metadata,
                e.objectId,
                e.objectType,
                e.properties,
                e.sleekflowCompanyId,
                e.source,
                to_timestamp(e.timestamp / 1000.0) AS timestamp,
                e.year,
                e.month,
                e.day,
                e.hour
            FROM postgres_db.{eventsTableName} e
            {userProfileJoin};
            """;

        viewCommand.CommandText = sql;
        await viewCommand.ExecuteNonQueryAsync();

        _logger.LogInformation("Created events view for PostgreSQL table: {TableName}", eventsTableName);
    }



    private async Task<List<Dictionary<string, object>>> WriteToListAsync(DbDataReader dbDataReader)
    {
        var results = new List<Dictionary<string, object>>();
        var columnNames = new List<string>();
        var recordCount = 0;

        // Get column names
        for (int i = 0; i < dbDataReader.FieldCount; i++)
        {
            columnNames.Add(dbDataReader.GetName(i));
        }

        // Stream records directly from database
        while (await dbDataReader.ReadAsync())
        {
            var record = new Dictionary<string, object>();

            // Write record
            for (int i = 0; i < dbDataReader.FieldCount; i++)
            {
                record[columnNames[i]] = dbDataReader.GetValue(i);
            }

            results.Add(record);
            recordCount++;

            // Check size limit (optional)
            const int maxRecords = 1000;
            if (recordCount >= maxRecords) // You can define MaxRecords as needed
            {
                _logger.LogInformation("Record count exceeds maximum limit of {MaxRecords} records", maxRecords);
                break;
            }
        }

        return results;
    }


}