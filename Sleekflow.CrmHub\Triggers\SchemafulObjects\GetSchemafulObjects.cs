﻿using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json;
using Sleekflow.Analyzers;
using Sleekflow.CrmHub.Models.Constants;
using Sleekflow.CrmHub.Models.SchemafulObjects;
using Sleekflow.CrmHub.SchemafulObjects;
using Sleekflow.DependencyInjection;
using Sleekflow.Persistence.Abstractions;
using Sleekflow.Validations;

namespace Sleekflow.CrmHub.Triggers.SchemafulObjects;

[TriggerGroup(TriggerGroups.SchemafulObjects)]
public class GetSchemafulObjects : ITrigger<GetSchemafulObjects.GetSchemafulObjectsInput, GetSchemafulObjects.GetSchemafulObjectsOutput>
{
    private readonly ISchemafulObjectService _schemafulObjectService;

    public GetSchemafulObjects(ISchemafulObjectService schemafulObjectService)
    {
        _schemafulObjectService = schemafulObjectService;
    }

    public sealed class GetSchemafulObjectsFilterGroup
    {
        [Required]
        [ValidateArray]
        [JsonProperty("filters")]
        public List<SchemafulObjectQueryBuilder.SchemafulObjectFilter> Filters { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectsFilterGroup(
            List<SchemafulObjectQueryBuilder.SchemafulObjectFilter> filters)
        {
            Filters = filters;
        }
    }

    public class GetSchemafulObjectsInput : IHasSleekflowCompanyId
    {
        [Required]
        [JsonProperty(IHasSleekflowCompanyId.PropertyNameSleekflowCompanyId)]
        [StringLength(1024)]
        public string SleekflowCompanyId { get; set; }

        [Required]
        [JsonProperty(SchemafulObject.PropertyNameSchemaId)]
        public string SchemaId { get; set; }

        [StringLength(16384, MinimumLength = 1)]
        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [Required]
        [Range(1, 1000)]
        [JsonProperty("limit")]
        public int Limit { get; set; }

        [Required]
        [JsonProperty("is_search_indexed_property_values")]
        public bool IsSearchIndexedPropertyValues { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("filter_groups")]
        public List<GetSchemafulObjectsFilterGroup> FilterGroups { get; set; }

        [Required]
        [ValidateArray]
        [JsonProperty("sorts")]
        public List<SchemafulObjectQueryBuilder.SchemafulObjectSort> Sorts { get; set; }

        [ValidateArray]
        [JsonProperty("array_exists")]
        public List<SchemafulObjectQueryBuilder.ArrayExist>? ArrayExists { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectsInput(
            string sleekflowCompanyId,
            string schemaId,
            string? continuationToken,
            int limit,
            bool isSearchIndexedPropertyValues,
            List<GetSchemafulObjectsFilterGroup> filterGroups,
            List<SchemafulObjectQueryBuilder.SchemafulObjectSort> sorts,
            List<SchemafulObjectQueryBuilder.ArrayExist>? arrayExists)
        {
            SleekflowCompanyId = sleekflowCompanyId;
            SchemaId = schemaId;
            ContinuationToken = continuationToken;
            Limit = limit;
            IsSearchIndexedPropertyValues = isSearchIndexedPropertyValues;
            FilterGroups = filterGroups;
            Sorts = sorts;
            ArrayExists = arrayExists;
        }
    }

    public class GetSchemafulObjectsOutput
    {
        [JsonProperty("schemaful_objects")]
        public List<SchemafulObjectDto> SchemafulObjects { get; set; }

        [JsonProperty("continuation_token")]
        public string? ContinuationToken { get; set; }

        [JsonProperty("count")]
        public long Count { get; set; }

        [JsonConstructor]
        public GetSchemafulObjectsOutput(
            List<SchemafulObjectDto> schemafulObjects,
            string? continuationToken,
            long count)
        {
            SchemafulObjects = schemafulObjects;
            ContinuationToken = continuationToken;
            Count = count;
        }
    }

    public async Task<GetSchemafulObjectsOutput> F(GetSchemafulObjectsInput getSchemafulObjectsInput)
    {
        var filterGroups = new List<SchemafulObjectQueryBuilder.FilterGroup>()
            .Concat(
                getSchemafulObjectsInput
                    .FilterGroups
                    .Select(
                        fg => new SchemafulObjectQueryBuilder.FilterGroup(
                            fg
                                .Filters
                                .Select(f => f)
                                .Cast<SchemafulObjectQueryBuilder.ISchemafulObjectFilter>()
                                .ToList())))
            .ToList();

        var queryDefinition = SchemafulObjectQueryBuilder.BuildQueryDef(
            new List<SchemafulObjectQueryBuilder.ISelect>(),
            filterGroups,
            getSchemafulObjectsInput.Sorts,
            new List<SchemafulObjectQueryBuilder.GroupBy>(),
            getSchemafulObjectsInput.SleekflowCompanyId,
            getSchemafulObjectsInput.SchemaId,
            getSchemafulObjectsInput.IsSearchIndexedPropertyValues,
            getSchemafulObjectsInput.ArrayExists);

        var (rawRecords, nextContinuationToken) =
            await _schemafulObjectService.GetContinuationTokenizedSchemafulObjectsAsync(
                queryDefinition,
                getSchemafulObjectsInput.ContinuationToken,
                getSchemafulObjectsInput.Limit);

        var schemafulObjects = rawRecords
            .Select(so => new SchemafulObjectDto(so))
            .ToList();

        return new GetSchemafulObjectsOutput(schemafulObjects, nextContinuationToken, schemafulObjects.Count);
    }
}